# Code Duplication Analysis - Session Components

## Overview
This report identifies duplicated code and functions within the session components directory (`src/components/sessions/`), specifically between `ClaudeCodeSession.tsx` and its backup file `ClaudeCodeSession.tsx.backup`.

## 1. Duplicated Utility Functions

### detectQuestion Function
**Location:** Both files contain identical implementation
- `ClaudeCodeSession.tsx:167-180`
- `ClaudeCodeSession.tsx.backup:151-164`

```typescript
const detectQuestion = (text: string): boolean => {
  // Common question patterns
  const questionPatterns = [
    /\?$/m,  // Ends with question mark
    /\?\s*$/m,  // Ends with question mark and whitespace
    /^(Would you|Do you|Can you|Should I|May I|Could you|Will you|Is it|Are you|Have you|What|When|Where|How|Why|Which)/mi,
    /(would you like|do you want|should I|can I|may I|shall I|need me to|want me to)/i,
    /(please (confirm|specify|provide|clarify|choose|select|let me know|tell me))/i,
    /(yes or no|y\/n|\(y\/n\))/i,
    /(proceed\?|continue\?|go ahead\?)/i
  ];
  
  return questionPatterns.some(pattern => pattern.test(text));
};
```

## 2. Duplicated Event Listener Patterns

### Session-Specific Event Listeners
Both files set up identical event listeners in multiple locations:

**First Set (Reconnection):**
- `ClaudeCodeSession.tsx:426-458`
- `ClaudeCodeSession.tsx.backup:428-460`

```typescript
const outputUnlisten = await listen<string>(`claude-output:${sessionId}`, async (event) => {
  // ... handling logic
});

const errorUnlisten = await listen<string>(`claude-error:${sessionId}`, (event) => {
  // ... handling logic
});

const completeUnlisten = await listen<boolean>(`claude-complete:${sessionId}`, async (event) => {
  // ... handling logic
});

unlistenRefs.current = [outputUnlisten, errorUnlisten, completeUnlisten];
```

**Second Set (Session-Specific):**
- `ClaudeCodeSession.tsx:553-569`
- `ClaudeCodeSession.tsx.backup:552-568`

```typescript
const specificOutputUnlisten = await listen<string>(`claude-output:${sid}`, (evt) => {
  handleStreamMessage(evt.payload);
});

const specificErrorUnlisten = await listen<string>(`claude-error:${sid}`, (evt) => {
  console.error('Claude error (scoped):', evt.payload);
  setError(evt.payload);
});

const specificCompleteUnlisten = await listen<boolean>(`claude-complete:${sid}`, (evt) => {
  console.log('[ClaudeCodeSession] Received claude-complete (scoped):', evt.payload);
  processComplete(evt.payload);
});
```

## 3. Duplicated State Management

### Common State Variables
Both files declare identical state variables:
- `const [projectPath, setProjectPath] = useState(...)`
- `const [messages, setMessages] = useState<ClaudeStreamMessage[]>([])`
- `const [isLoading, setIsLoading] = useState(false)`
- `const [error, setError] = useState<string | null>(null)`
- `const [rawJsonlOutput, setRawJsonlOutput] = useState<string[]>([])`
- `const [copyPopoverOpen, setCopyPopoverOpen] = useState(false)`
- `const [isFirstPrompt, setIsFirstPrompt] = useState(!session)`
- `const [resumeFlag, setResumeFlag] = useState(false)`
- `const [totalTokens, setTotalTokens] = useState(0)`
- `const [extractedSessionInfo, setExtractedSessionInfo] = useState(...)`
- `const [claudeSessionId, setClaudeSessionId] = useState<string | null>(null)`
- `const [showTimeline, setShowTimeline] = useState(false)`
- `const [showSettings, setShowSettings] = useState(false)`
- `const [showForkDialog, setShowForkDialog] = useState(false)`
- `const [showSlashCommandsSettings, setShowSlashCommandsSettings] = useState(false)`
- `const [showSuperClaude, setShowSuperClaude] = useState(false)`
- `const [forkCheckpointId, setForkCheckpointId] = useState<string | null>(null)`
- `const [forkSessionName, setForkSessionName] = useState("")`
- `const [queuedPrompts, setQueuedPrompts] = useState<Array<...>>([])`

### Common Refs
Both files use identical refs:
- `const unlistenRefs = useRef<UnlistenFn[]>([])`
- `const hasActiveSessionRef = useRef(false)`
- `const floatingPromptRef = useRef<FloatingPromptInputRef>(null)`
- `const queuedPromptsRef = useRef<Array<...>>([])`
- `const isMountedRef = useRef(true)`
- `const isListeningRef = useRef(false)`
- `const messagesRef = useRef<ClaudeStreamMessage[]>([])`
- `const sessionStartTime = useRef<number>(Date.now())`

## 4. Duplicated API Call Patterns

### SuperClaude API Calls
Multiple components make identical API calls:

**In SessionList.tsx and SuperClaudePanel.tsx:**
```typescript
api.getSessionPersonas(sessionId, projectId).catch(() => [])
api.getCommandHistory(sessionId, projectId, limit).catch(() => [])
api.getSuperClaudeStats(projectId).catch(() => null)
```

**In SuperClaudeHistory.tsx:**
```typescript
api.getCommandHistory(sessionId, projectId, maxItems)
api.getProjectCommandHistory(projectId, maxItems)
api.getSuperClaudeStats(projectId)
```

## 5. Duplicated Message Handling

### handleStreamMessage Function
The backup file has inline `handleStreamMessage` implementation while the main file has it as a separate function, but the core logic is duplicated:

**ClaudeCodeSession.tsx:599-648**
```typescript
function handleStreamMessage(payload: string) {
  try {
    if (!isMountedRef.current) return;
    setRawJsonlOutput((prev) => [...prev, payload]);
    const message = JSON.parse(payload) as ClaudeStreamMessage;
    // ... processing logic
    setMessages((prev) => [...prev, message]);
  } catch (err) {
    // error handling
  }
}
```

**ClaudeCodeSession.tsx.backup:598-788** contains similar logic with additional session metrics tracking.

## 6. Duplicated Cleanup Patterns

### Unlisten Reference Cleanup
Both files use identical cleanup patterns in multiple locations:
```typescript
unlistenRefs.current.forEach(unlisten => unlisten());
unlistenRefs.current = [];
```

This pattern appears at:
- Component unmount
- Before setting up new listeners
- Session termination
- Error recovery

## 7. Duplicated Session Metrics

### Session Metrics Structure
**ClaudeCodeSession.tsx.backup:168-183** has inline metrics:
```typescript
const sessionMetrics = useRef({
  firstMessageTime: null as number | null,
  promptsSent: 0,
  toolsExecuted: 0,
  toolsFailed: 0,
  filesCreated: 0,
  filesModified: 0,
  filesDeleted: 0,
  codeBlocksGenerated: 0,
  errorsEncountered: 0,
  lastActivityTime: Date.now(),
  toolExecutionTimes: [] as number[],
  checkpointCount: 0,
  wasResumed: !!session,
  modelChanges: [] as Array<{ from: string; to: string; timestamp: number }>,
});
```

**ClaudeCodeSession.tsx** uses the extracted `useSessionMetrics` hook with similar structure.

## 8. Duplicated Component Structure

Both files share identical:
- Import statements (with minor variations)
- Component props interface
- Component documentation
- Overall component structure and flow
- Error boundary usage
- Analytics tracking setup

## 9. Duplicated Effect Hooks

### Ref Synchronization
Both files have identical effects:
```typescript
useEffect(() => {
  queuedPromptsRef.current = queuedPrompts;
}, [queuedPrompts]);

useEffect(() => {
  messagesRef.current = messages;
}, [messages]);
```

## 10. Component State Duplication Across Files

### SessionOutputViewer.tsx
Also contains duplicated state and patterns:
- Similar message state: `useState<ClaudeStreamMessage[]>([])`
- Similar event listener setup for agent events
- Similar cleanup patterns with `unlistenRefs`

## Summary

The main areas of duplication are:

1. **Complete file duplication**: `ClaudeCodeSession.tsx` and `ClaudeCodeSession.tsx.backup` are nearly identical with minor refactoring differences
2. **Event listener patterns**: Identical listener setup/cleanup across multiple components
3. **State management**: Same state variables and refs across components
4. **API call patterns**: Repeated API calls with identical error handling
5. **Utility functions**: `detectQuestion` function duplicated exactly
6. **Message handling**: Similar message processing logic with minor variations
7. **Cleanup patterns**: Identical unlisten reference cleanup throughout

The backup file appears to be a previous version before some refactoring was done to extract hooks like `useSessionMetrics`, `usePlanMode`, `useExtendedThinking`, and `usePreview`, but the core logic remains duplicated.