{"name": "sanity", "private": true, "version": "0.1.0", "license": "AGPL-3.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri", "check": "tsc --noEmit && cd src-tauri && cargo check", "test": "vitest", "test:unit": "vitest run src/services/__tests__/", "test:integration": "vitest run src/test/integration/", "test:e2e": "vitest run src/test/e2e/", "test:performance": "vitest run src/test/performance/", "test:accessibility": "vitest run src/test/accessibility/", "test:cross-platform": "vitest run src/test/cross-platform/", "test:ci": "vitest run src/test/ci/", "test:all": "npm run test:unit && npm run test:integration && npm run test:e2e && npm run test:performance && npm run test:accessibility && npm run test:cross-platform", "test:coverage": "vitest run --coverage", "test:watch": "vitest", "test:ui": "vitest --ui"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@fullcalendar/core": "^6.1.11", "@fullcalendar/daygrid": "^6.1.11", "@fullcalendar/interaction": "^6.1.11", "@fullcalendar/list": "^6.1.11", "@fullcalendar/react": "^6.1.11", "@fullcalendar/timegrid": "^6.1.11", "@hookform/resolvers": "^3.9.1", "@mui/icons-material": "^5.15.14", "@mui/lab": "^5.0.0-alpha.170", "@mui/material": "^5.15.14", "@mui/x-charts": "^7.0.0", "@mui/x-data-grid": "^7.0.0", "@mui/x-date-pickers": "^7.0.0", "@tanstack/react-query": "^5.85.5", "@tanstack/react-table": "^8.15.3", "@tanstack/react-virtual": "^3.13.10", "@tauri-apps/api": "^2.8.0", "@tauri-apps/plugin-dialog": "^2.3.2", "@tauri-apps/plugin-fs": "^2.4.2", "@tauri-apps/plugin-global-shortcut": "^2.3.0", "@tauri-apps/plugin-opener": "^2", "@tauri-apps/plugin-shell": "^2.3.0", "@types/canvas-confetti": "^1.9.0", "@types/diff": "^8.0.0", "@types/dompurify": "^3.0.5", "@types/html2canvas": "^0.5.35", "@types/jspdf": "^1.3.3", "@types/react-syntax-highlighter": "^15.5.13", "@uiw/react-md-editor": "^4.0.7", "ansi-to-html": "^0.7.2", "canvas-confetti": "^1.9.3", "chart.js": "^4.4.2", "chartjs-adapter-date-fns": "^3.0.0", "chartjs-plugin-annotation": "^3.0.1", "chartjs-plugin-datalabels": "^2.2.0", "chartjs-plugin-zoom": "^2.0.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "diff": "^8.0.2", "dompurify": "^3.2.6", "flatpickr": "^4.6.13", "formik": "^2.4.5", "framer-motion": "^12.0.0-alpha.1", "html2canvas": "^1.4.1", "idb": "^8.0.0", "immer": "^10.1.1", "jspdf": "^3.0.1", "lucide-react": "^0.541.0", "mdast-util-to-string": "^4.0.0", "mermaid": "^11.10.0", "notistack": "^3.0.1", "papaparse": "^5.5.3", "posthog-js": "^1.258.3", "react": "^18.3.1", "react-chartjs-2": "^5.2.0", "react-countup": "^6.5.3", "react-day-picker": "^9.8.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-flatpickr": "^3.10.13", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.5.2", "react-markdown": "^9.0.3", "react-resizable-panels": "^3.0.4", "react-router-dom": "^7.8.1", "react-select": "^5.8.0", "react-syntax-highlighter": "^15.6.1", "react-table": "^7.8.0", "recharts": "^2.15.4", "remark-gfm": "^4.0.0", "remark-parse": "^11.0.0", "sweetalert2": "^11.10.7", "sweetalert2-react-content": "^5.0.7", "unified": "^11.0.5", "unist-util-visit": "^5.0.0", "xlsx": "^0.18.5", "yup": "^1.4.0", "zod": "^3.24.1", "zustand": "^5.0.6"}, "devDependencies": {"@tauri-apps/cli": "^2.8.1", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/mdast": "^4.0.4", "@types/node": "^22.17.2", "@types/papaparse": "^5.3.16", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@types/react-flatpickr": "^3.8.11", "@types/react-select": "^5.0.1", "@types/react-table": "^7.7.19", "@types/sharp": "^0.32.0", "@types/unist": "^3.0.3", "@types/xlsx": "^0.0.36", "@vitejs/plugin-react": "^4.3.4", "@vitest/ui": "^3.2.4", "jsdom": "^26.1.0", "sharp": "^0.34.2", "typescript": "~5.9.2", "vite": "^6.0.3", "vitest": "^3.2.4"}, "trustedDependencies": ["@parcel/watcher"]}