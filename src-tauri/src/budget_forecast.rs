use serde::{Deserialize, Serialize};
use anyhow::Result;
use rusqlite::{params, Connection};
use chrono::{Utc, Datelike, NaiveDate};
use std::collections::HashMap;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ForecastData {
    pub month: i32,
    pub predicted_spend: f64,
    pub actual_spend: Option<f64>,
    pub confidence_interval: (f64, f64),
    pub trend: String, // "increasing", "decreasing", "stable"
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Anomaly {
    pub date: String,
    pub amount: f64,
    pub expected_amount: f64,
    pub deviation_percentage: f64,
    pub severity: String, // "low", "medium", "high"
    pub description: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ExpenseRow {
    pub id: String,
    pub amount: f64,
    pub category: String,
    pub date: String,
    pub department_id: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct SeasonalPattern {
    pub month: i32,
    pub seasonal_factor: f64,
    pub historical_average: f64,
}

pub fn predict_monthly_spend(
    conn: &Connection, 
    historical_data: &[ExpenseRow], 
    year: i64
) -> Result<Vec<ForecastData>> {
    let mut forecasts = Vec::new();
    
    // Group historical data by month
    let mut monthly_totals: HashMap<i32, Vec<f64>> = HashMap::new();
    
    for expense in historical_data {
        if let Ok(date) = NaiveDate::parse_from_str(&expense.date, "%Y-%m-%d") {
            let month = date.month() as i32;
            monthly_totals.entry(month).or_insert_with(Vec::new).push(expense.amount);
        }
    }
    
    // Calculate seasonal patterns
    let seasonal_patterns = calculate_seasonal_patterns(&monthly_totals);
    
    // Generate forecasts for each month
    for month in 1..=12 {
        let historical_amounts = monthly_totals.get(&month).cloned().unwrap_or_default();
        
        // Calculate base prediction using moving average
        let base_prediction = if !historical_amounts.is_empty() {
            let avg: f64 = historical_amounts.iter().sum::<f64>() / historical_amounts.len() as f64;
            avg
        } else {
            // Use overall average if no historical data for this month
            let total_avg: f64 = monthly_totals.values()
                .flat_map(|v| v.iter())
                .sum::<f64>() / monthly_totals.values()
                .map(|v| v.len())
                .sum::<usize>().max(1) as f64;
            total_avg
        };
        
        // Apply seasonal adjustment
        let seasonal_factor = seasonal_patterns.get(&month)
            .map(|p| p.seasonal_factor)
            .unwrap_or(1.0);
        let predicted_spend = base_prediction * seasonal_factor;
        
        // Calculate confidence interval
        let std_dev = calculate_std_dev(&historical_amounts);
        let confidence_level = 1.96; // 95% confidence interval
        let margin = std_dev * confidence_level;
        let confidence_interval = (
            (predicted_spend - margin).max(0.0),
            predicted_spend + margin
        );
        
        // Determine trend
        let trend = determine_trend(&historical_amounts);
        
        // Get actual spend if available (for past months in current year)
        let actual_spend = get_actual_spend(conn, year, month)?;
        
        forecasts.push(ForecastData {
            month,
            predicted_spend,
            actual_spend,
            confidence_interval,
            trend,
        });
    }
    
    Ok(forecasts)
}

pub fn calculate_burn_rate(
    current_spend: f64, 
    days_elapsed: i32, 
    total_days: i32, 
    total_budget: f64
) -> f64 {
    if days_elapsed == 0 {
        return 0.0;
    }
    
    // Daily burn rate
    let daily_burn = current_spend / days_elapsed as f64;
    
    // Project total spend at current rate
    let projected_total = daily_burn * total_days as f64;
    
    // Calculate burn rate as percentage of budget
    (projected_total / total_budget) * 100.0
}

pub fn identify_spending_anomalies(
    conn: &Connection,
    expenses: &[ExpenseRow]
) -> Result<Vec<Anomaly>> {
    let mut anomalies = Vec::new();
    
    // Calculate statistics for anomaly detection
    let amounts: Vec<f64> = expenses.iter().map(|e| e.amount).collect();
    let mean = calculate_mean(&amounts);
    let std_dev = calculate_std_dev(&amounts);
    
    // Z-score threshold for anomaly detection
    let z_threshold = 2.5;
    
    for expense in expenses {
        let z_score = if std_dev > 0.0 {
            (expense.amount - mean).abs() / std_dev
        } else {
            0.0
        };
        
        if z_score > z_threshold {
            let deviation_percentage = ((expense.amount - mean) / mean) * 100.0;
            
            let severity = if z_score > 4.0 {
                "high"
            } else if z_score > 3.0 {
                "medium"
            } else {
                "low"
            };
            
            anomalies.push(Anomaly {
                date: expense.date.clone(),
                amount: expense.amount,
                expected_amount: mean,
                deviation_percentage,
                severity: severity.to_string(),
                description: format!(
                    "Expense of ${:.2} deviates {:.1}% from expected ${:.2}",
                    expense.amount, deviation_percentage, mean
                ),
            });
        }
    }
    
    // Check for pattern-based anomalies
    let pattern_anomalies = detect_pattern_anomalies(expenses);
    anomalies.extend(pattern_anomalies);
    
    Ok(anomalies)
}

fn calculate_seasonal_patterns(monthly_totals: &HashMap<i32, Vec<f64>>) -> HashMap<i32, SeasonalPattern> {
    let mut patterns = HashMap::new();
    
    // Calculate overall average
    let overall_avg: f64 = monthly_totals.values()
        .flat_map(|v| v.iter())
        .sum::<f64>() / monthly_totals.values()
        .map(|v| v.len())
        .sum::<usize>().max(1) as f64;
    
    for month in 1..=12 {
        let month_amounts = monthly_totals.get(&month).cloned().unwrap_or_default();
        let month_avg = if !month_amounts.is_empty() {
            month_amounts.iter().sum::<f64>() / month_amounts.len() as f64
        } else {
            overall_avg
        };
        
        let seasonal_factor = if overall_avg > 0.0 {
            month_avg / overall_avg
        } else {
            1.0
        };
        
        patterns.insert(month, SeasonalPattern {
            month,
            seasonal_factor,
            historical_average: month_avg,
        });
    }
    
    patterns
}

fn calculate_mean(values: &[f64]) -> f64 {
    if values.is_empty() {
        return 0.0;
    }
    values.iter().sum::<f64>() / values.len() as f64
}

fn calculate_std_dev(values: &[f64]) -> f64 {
    if values.len() < 2 {
        return 0.0;
    }
    
    let mean = calculate_mean(values);
    let variance = values.iter()
        .map(|v| (v - mean).powi(2))
        .sum::<f64>() / (values.len() - 1) as f64;
    
    variance.sqrt()
}

fn determine_trend(values: &[f64]) -> String {
    if values.len() < 3 {
        return "stable".to_string();
    }
    
    // Simple linear regression for trend
    let n = values.len() as f64;
    let x_mean = (n - 1.0) / 2.0;
    let y_mean = calculate_mean(values);
    
    let mut numerator = 0.0;
    let mut denominator = 0.0;
    
    for (i, y) in values.iter().enumerate() {
        let x = i as f64;
        numerator += (x - x_mean) * (y - y_mean);
        denominator += (x - x_mean).powi(2);
    }
    
    if denominator == 0.0 {
        return "stable".to_string();
    }
    
    let slope = numerator / denominator;
    let slope_percentage = (slope / y_mean) * 100.0;
    
    if slope_percentage > 5.0 {
        "increasing".to_string()
    } else if slope_percentage < -5.0 {
        "decreasing".to_string()
    } else {
        "stable".to_string()
    }
}

fn get_actual_spend(conn: &Connection, year: i64, month: i32) -> Result<Option<f64>> {
    let current_date = Utc::now().naive_utc().date();
    let current_year = current_date.year() as i64;
    let current_month = current_date.month() as i32;
    
    // Only return actual spend for past months
    if year > current_year || (year == current_year && month > current_month) {
        return Ok(None);
    }
    
    let result: Option<f64> = conn.query_row(
        "SELECT COALESCE(SUM(amount), 0) FROM expenses 
         WHERE strftime('%Y', date) = ?1 AND strftime('%m', date) = ?2",
        params![year.to_string(), format!("{:02}", month)],
        |row| row.get(0),
    ).ok();
    
    Ok(result)
}

fn detect_pattern_anomalies(expenses: &[ExpenseRow]) -> Vec<Anomaly> {
    let mut anomalies = Vec::new();
    
    // Group by date to detect unusual daily spending
    let mut daily_totals: HashMap<String, f64> = HashMap::new();
    for expense in expenses {
        *daily_totals.entry(expense.date.clone()).or_insert(0.0) += expense.amount;
    }
    
    let daily_amounts: Vec<f64> = daily_totals.values().cloned().collect();
    let daily_mean = calculate_mean(&daily_amounts);
    let daily_std = calculate_std_dev(&daily_amounts);
    
    // Detect days with unusual total spending
    for (date, total) in daily_totals {
        if daily_std > 0.0 {
            let z_score = (total - daily_mean).abs() / daily_std;
            if z_score > 3.0 {
                anomalies.push(Anomaly {
                    date: date.clone(),
                    amount: total,
                    expected_amount: daily_mean,
                    deviation_percentage: ((total - daily_mean) / daily_mean) * 100.0,
                    severity: if z_score > 4.0 { "high" } else { "medium" }.to_string(),
                    description: format!("Unusual daily total spending on {}", date),
                });
            }
        }
    }
    
    // Detect sudden changes in spending patterns
    let mut sorted_expenses = expenses.to_vec();
    sorted_expenses.sort_by(|a, b| a.date.cmp(&b.date));
    
    for window in sorted_expenses.windows(5) {
        let first_half_avg = (window[0].amount + window[1].amount) / 2.0;
        let second_half_avg = (window[3].amount + window[4].amount) / 2.0;
        
        let change_rate = if first_half_avg > 0.0 {
            ((second_half_avg - first_half_avg) / first_half_avg).abs()
        } else {
            0.0
        };
        
        if change_rate > 2.0 { // 200% change
            anomalies.push(Anomaly {
                date: window[2].date.clone(),
                amount: window[2].amount,
                expected_amount: first_half_avg,
                deviation_percentage: change_rate * 100.0,
                severity: "medium".to_string(),
                description: "Sudden change in spending pattern detected".to_string(),
            });
        }
    }
    
    anomalies
}

pub fn project_budget_overrun(
    conn: &Connection,
    current_spend: f64,
    days_elapsed: i32,
    total_days: i32,
    total_budget: f64
) -> Result<Option<NaiveDate>> {
    if days_elapsed == 0 || current_spend == 0.0 {
        return Ok(None);
    }
    
    let daily_burn = current_spend / days_elapsed as f64;
    let remaining_budget = total_budget - current_spend;
    
    if remaining_budget <= 0.0 {
        // Already overrun
        return Ok(Some(Utc::now().naive_utc().date()));
    }
    
    if daily_burn <= 0.0 {
        return Ok(None);
    }
    
    let days_until_overrun = (remaining_budget / daily_burn) as i64;
    let overrun_date = Utc::now().naive_utc().date() + chrono::Duration::days(days_until_overrun);
    
    Ok(Some(overrun_date))
}