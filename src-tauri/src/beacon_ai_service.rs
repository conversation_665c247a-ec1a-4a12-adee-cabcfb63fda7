use anyhow::{anyhow, Result};
use reqwest::Client;
use serde::{Deserialize, Serialize};
use serde_json::json;
use std::env;
use std::time::Duration;
use tokio::time::sleep;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct AIConfig {
    pub provider: AIProvider,
    pub api_key: String,
    pub model: String,
    pub max_tokens: Option<u32>,
    pub temperature: f32,
    pub api_base_url: Option<String>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum AIProvider {
    OpenAI,
    Claude,
    Local,
    Mock,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AIRequest {
    pub prompt: String,
    pub context: Option<Vec<String>>,
    pub max_tokens: Option<u32>,
    pub temperature: Option<f32>,
    pub system_prompt: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AIResponse {
    pub content: String,
    pub tokens_used: Option<u32>,
    pub model: String,
    pub provider: String,
}

pub struct LightbulbAIService {
    config: Option<AIConfig>,
    client: Client,
}

impl LightbulbAIService {
    pub fn new() -> Self {
        let client = Client::builder()
            .timeout(Duration::from_secs(60))
            .build()
            .unwrap_or_else(|_| Client::new());
        
        // Try to load config from environment
        let config = Self::load_config_from_env();
        
        Self { config, client }
    }
    
    fn load_config_from_env() -> Option<AIConfig> {
        // Check for OpenAI configuration
        if let Ok(api_key) = env::var("OPENAI_API_KEY") {
            return Some(AIConfig {
                provider: AIProvider::OpenAI,
                api_key,
                model: env::var("OPENAI_MODEL").unwrap_or_else(|_| "gpt-4-turbo-preview".to_string()),
                max_tokens: env::var("OPENAI_MAX_TOKENS")
                    .ok()
                    .and_then(|s| s.parse().ok()),
                temperature: env::var("OPENAI_TEMPERATURE")
                    .ok()
                    .and_then(|s| s.parse().ok())
                    .unwrap_or(0.7),
                api_base_url: env::var("OPENAI_API_BASE").ok(),
            });
        }
        
        // Check for Claude configuration
        if let Ok(api_key) = env::var("CLAUDE_API_KEY") {
            return Some(AIConfig {
                provider: AIProvider::Claude,
                api_key,
                model: env::var("CLAUDE_MODEL").unwrap_or_else(|_| "claude-3-opus-20240229".to_string()),
                max_tokens: env::var("CLAUDE_MAX_TOKENS")
                    .ok()
                    .and_then(|s| s.parse().ok()),
                temperature: env::var("CLAUDE_TEMPERATURE")
                    .ok()
                    .and_then(|s| s.parse().ok())
                    .unwrap_or(0.7),
                api_base_url: env::var("CLAUDE_API_BASE").ok(),
            });
        }
        
        None
    }
    
    pub fn update_config(&mut self, config: AIConfig) {
        self.config = Some(config);
    }
    
    pub fn get_config(&self) -> Option<&AIConfig> {
        self.config.as_ref()
    }
    
    pub async fn generate(&self, request: AIRequest) -> Result<AIResponse> {
        let config = self.config.as_ref()
            .ok_or_else(|| anyhow!("AI service not configured"))?;
        
        match config.provider {
            AIProvider::OpenAI => self.generate_openai(request, config).await,
            AIProvider::Claude => self.generate_claude(request, config).await,
            AIProvider::Local => self.generate_local(request).await,
            AIProvider::Mock => self.generate_mock(request).await,
        }
    }
    
    async fn generate_openai(&self, request: AIRequest, config: &AIConfig) -> Result<AIResponse> {
        let api_url = config.api_base_url.as_ref()
            .map(|s| s.to_string())
            .unwrap_or_else(|| "https://api.openai.com/v1".to_string());
        
        let mut messages = vec![];
        
        if let Some(system) = request.system_prompt {
            messages.push(json!({
                "role": "system",
                "content": system
            }));
        }
        
        if let Some(context) = request.context {
            for ctx in context {
                messages.push(json!({
                    "role": "system",
                    "content": format!("Context: {}", ctx)
                }));
            }
        }
        
        messages.push(json!({
            "role": "user",
            "content": request.prompt
        }));
        
        let body = json!({
            "model": config.model,
            "messages": messages,
            "temperature": request.temperature.unwrap_or(config.temperature),
            "max_tokens": request.max_tokens.or(config.max_tokens),
        });
        
        let response = self.client
            .post(format!("{}/chat/completions", api_url))
            .header("Authorization", format!("Bearer {}", config.api_key))
            .header("Content-Type", "application/json")
            .json(&body)
            .send()
            .await?;
        
        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow!("OpenAI API error: {}", error_text));
        }
        
        let data: serde_json::Value = response.json().await?;
        
        let content = data["choices"][0]["message"]["content"]
            .as_str()
            .ok_or_else(|| anyhow!("Invalid response from OpenAI"))?
            .to_string();
        
        let tokens_used = data["usage"]["total_tokens"].as_u64().map(|t| t as u32);
        
        Ok(AIResponse {
            content,
            tokens_used,
            model: config.model.clone(),
            provider: "openai".to_string(),
        })
    }
    
    async fn generate_claude(&self, request: AIRequest, config: &AIConfig) -> Result<AIResponse> {
        let api_url = config.api_base_url.as_ref()
            .map(|s| s.to_string())
            .unwrap_or_else(|| "https://api.anthropic.com/v1".to_string());
        
        let mut prompt = String::new();
        
        if let Some(context) = request.context {
            for ctx in context {
                prompt.push_str(&format!("Context: {}\n\n", ctx));
            }
        }
        
        prompt.push_str(&request.prompt);
        
        let body = json!({
            "model": config.model,
            "messages": [{
                "role": "user",
                "content": prompt
            }],
            "system": request.system_prompt,
            "max_tokens": request.max_tokens.or(config.max_tokens).unwrap_or(1024),
            "temperature": request.temperature.unwrap_or(config.temperature),
        });
        
        let response = self.client
            .post(format!("{}/messages", api_url))
            .header("x-api-key", &config.api_key)
            .header("anthropic-version", "2023-06-01")
            .header("Content-Type", "application/json")
            .json(&body)
            .send()
            .await?;
        
        if !response.status().is_success() {
            let error_text = response.text().await?;
            return Err(anyhow!("Claude API error: {}", error_text));
        }
        
        let data: serde_json::Value = response.json().await?;
        
        let content = data["content"][0]["text"]
            .as_str()
            .ok_or_else(|| anyhow!("Invalid response from Claude"))?
            .to_string();
        
        let tokens_used = data["usage"]["total_tokens"].as_u64().map(|t| t as u32);
        
        Ok(AIResponse {
            content,
            tokens_used,
            model: config.model.clone(),
            provider: "claude".to_string(),
        })
    }
    
    async fn generate_local(&self, request: AIRequest) -> Result<AIResponse> {
        // Placeholder for local model integration (e.g., llama.cpp, GGML)
        Err(anyhow!("Local AI models not yet implemented. Please use OpenAI or Claude."))
    }
    
    async fn generate_mock(&self, request: AIRequest) -> Result<AIResponse> {
        // Mock response for testing
        let response = format!(
            "This is a mock AI response to your prompt: '{}'. \
            In a real implementation, this would be generated by an AI model.",
            request.prompt
        );
        
        Ok(AIResponse {
            content: response,
            tokens_used: Some(100),
            model: "mock-model".to_string(),
            provider: "mock".to_string(),
        })
    }
    
    // Specialized functions for Lightbulb features
    
    // Enhanced methods with retry logic
    pub async fn generate_with_retry(&self, request: AIRequest, max_retries: u32) -> Result<AIResponse> {
        let mut retries = 0;
        let mut last_error = None;
        
        while retries < max_retries {
            match self.generate(request.clone()).await {
                Ok(response) => return Ok(response),
                Err(e) => {
                    last_error = Some(e);
                    retries += 1;
                    if retries < max_retries {
                        sleep(Duration::from_secs(2u64.pow(retries))).await;
                    }
                }
            }
        }
        
        Err(last_error.unwrap_or_else(|| anyhow!("Failed after {} retries", max_retries)))
    }
    
    pub async fn summarize_content(&self, content: &str) -> Result<String> {
        let request = AIRequest {
            prompt: format!("Please provide a concise summary of the following content:\n\n{}", content),
            context: None,
            max_tokens: Some(200),
            temperature: Some(0.5),
            system_prompt: Some("You are a helpful assistant that creates clear, concise summaries.".to_string()),
        };
        
        let response = self.generate_with_retry(request, 3).await?;
        Ok(response.content)
    }
    
    pub async fn extract_key_points(&self, content: &str) -> Result<Vec<String>> {
        let request = AIRequest {
            prompt: format!("Extract the key points from the following content as a bullet list:\n\n{}", content),
            context: None,
            max_tokens: Some(500),
            temperature: Some(0.7),
            system_prompt: Some("You are a helpful assistant that extracts key information.".to_string()),
        };
        
        let response = self.generate_with_retry(request, 3).await?;
        
        // Parse bullet points from response
        let points: Vec<String> = response.content
            .lines()
            .filter(|line| line.trim().starts_with('-') || line.trim().starts_with('•') || line.trim().starts_with('*'))
            .map(|line| line.trim_start_matches(&['-', '•', '*'][..]).trim().to_string())
            .collect();
        
        Ok(points)
    }
    
    pub async fn generate_tags(&self, content: &str) -> Result<Vec<String>> {
        let request = AIRequest {
            prompt: format!(
                "Analyze the following content and suggest 3-5 relevant tags. \
                Return only the tags as a comma-separated list:\n\n{}",
                content
            ),
            context: None,
            max_tokens: Some(50),
            temperature: Some(0.3),
            system_prompt: Some("You are a helpful assistant that generates relevant tags for content organization.".to_string()),
        };
        
        let response = self.generate_with_retry(request, 3).await?;
        let tags: Vec<String> = response.content
            .split(',')
            .map(|s| s.trim().to_lowercase())
            .filter(|s| !s.is_empty())
            .collect();
        
        Ok(tags)
    }
    
    pub async fn answer_question(&self, question: &str, context: Vec<String>) -> Result<String> {
        let request = AIRequest {
            prompt: question.to_string(),
            context: Some(context),
            max_tokens: Some(500),
            temperature: Some(0.7),
            system_prompt: Some(
                "You are a knowledgeable assistant helping users with their questions. \
                Use the provided context to give accurate, helpful answers."
                .to_string()
            ),
        };
        
        let response = self.generate_with_retry(request, 3).await?;
        Ok(response.content)
    }
    
    pub async fn expand_notes(&self, notes: &str) -> Result<String> {
        let request = AIRequest {
            prompt: format!(
                "Please expand these notes into a well-structured document with proper sections and details:\n\n{}",
                notes
            ),
            context: None,
            max_tokens: Some(1000),
            temperature: Some(0.7),
            system_prompt: Some(
                "You are a helpful assistant that expands brief notes into comprehensive, well-organized documents."
                .to_string()
            ),
        };
        
        let response = self.generate_with_retry(request, 3).await?;
        Ok(response.content)
    }
}

// Singleton instance
lazy_static::lazy_static! {
    pub static ref AI_SERVICE: std::sync::Mutex<LightbulbAIService> = 
        std::sync::Mutex::new(LightbulbAIService::new());
}

// Tauri commands for AI configuration
#[tauri::command]
pub async fn configure_ai_service(config: AIConfig) -> Result<String, String> {
    let mut service = AI_SERVICE.lock()
        .map_err(|e| format!("Failed to access AI service: {}", e))?;
    
    service.update_config(config);
    Ok("AI service configured successfully".to_string())
}

#[tauri::command]
pub async fn get_ai_config() -> Result<Option<AIConfig>, String> {
    let service = AI_SERVICE.lock()
        .map_err(|e| format!("Failed to access AI service: {}", e))?;
    
    Ok(service.get_config().cloned())
}

#[tauri::command]
pub async fn test_ai_connection() -> Result<AIResponse, String> {
    let service = AI_SERVICE.lock()
        .map_err(|e| format!("Failed to access AI service: {}", e))?;
    
    let request = AIRequest {
        prompt: "Hello! Please respond with a brief greeting to confirm the connection is working.".to_string(),
        context: None,
        max_tokens: Some(50),
        temperature: Some(0.5),
        system_prompt: None,
    };
    
    service.generate(request).await
        .map_err(|e| format!("AI connection test failed: {}", e))
}

#[tauri::command]
pub async fn ai_summarize(content: String) -> Result<String, String> {
    let service = AI_SERVICE.lock()
        .map_err(|e| format!("Failed to access AI service: {}", e))?;
    
    service.summarize_content(&content).await
        .map_err(|e| format!("Failed to summarize content: {}", e))
}

#[tauri::command]
pub async fn ai_generate_tags(content: String) -> Result<Vec<String>, String> {
    let service = AI_SERVICE.lock()
        .map_err(|e| format!("Failed to access AI service: {}", e))?;
    
    service.generate_tags(&content).await
        .map_err(|e| format!("Failed to generate tags: {}", e))
}

#[tauri::command]
pub async fn ai_answer_question(question: String, context: Vec<String>) -> Result<String, String> {
    let service = AI_SERVICE.lock()
        .map_err(|e| format!("Failed to access AI service: {}", e))?;
    
    service.answer_question(&question, context).await
        .map_err(|e| format!("Failed to answer question: {}", e))
}

#[tauri::command]
pub async fn ai_expand_notes(notes: String) -> Result<String, String> {
    let service = AI_SERVICE.lock()
        .map_err(|e| format!("Failed to access AI service: {}", e))?;
    
    service.expand_notes(&notes).await
        .map_err(|e| format!("Failed to expand notes: {}", e))
}

#[tauri::command]
pub async fn ai_extract_key_points(content: String) -> Result<Vec<String>, String> {
    let service = AI_SERVICE.lock()
        .map_err(|e| format!("Failed to access AI service: {}", e))?;
    
    service.extract_key_points(&content).await
        .map_err(|e| format!("Failed to extract key points: {}", e))
}