use anyhow::Result;
use chrono::{DateTime, Utc};
use rusqlite::{params, Connection, Row};
use serde::{Deserialize, Serialize};
use serde_json;
use uuid::Uuid;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Page {
    pub id: String,
    pub user_id: String,
    pub title: String,
    pub content: Option<String>,
    #[serde(rename = "type")]
    pub page_type: String,
    pub status: String,
    pub tags: Option<Vec<String>>,
    pub metadata: Option<serde_json::Value>,
    pub is_pinned: bool,
    pub is_template: bool,
    pub parent_id: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

impl Page {
    fn from_row(row: &Row) -> rusqlite::Result<Self> {
        let tags_json: Option<String> = row.get(6)?;
        let metadata_json: Option<String> = row.get(7)?;
        
        let tags = tags_json
            .and_then(|s| serde_json::from_str(&s).ok());
        let metadata = metadata_json
            .and_then(|s| serde_json::from_str(&s).ok());
        
        Ok(Page {
            id: row.get(0)?,
            user_id: row.get(1)?,
            title: row.get(2)?,
            content: row.get(3)?,
            page_type: row.get(4)?,
            status: row.get(5)?,
            tags,
            metadata,
            is_pinned: row.get(8)?,
            is_template: row.get(9)?,
            parent_id: row.get(10)?,
            created_at: row.get(11)?,
            updated_at: row.get(12)?,
        })
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreatePageRequest {
    pub title: String,
    pub content: Option<String>,
    pub page_type: Option<String>,
    pub tags: Option<Vec<String>>,
    pub metadata: Option<serde_json::Value>,
    pub parent_id: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdatePageRequest {
    pub title: Option<String>,
    pub content: Option<String>,
    pub status: Option<String>,
    pub tags: Option<Vec<String>>,
    pub metadata: Option<serde_json::Value>,
    pub is_pinned: Option<bool>,
}

pub struct PageRepository<'a> {
    conn: &'a Connection,
}

impl<'a> PageRepository<'a> {
    pub fn new(conn: &'a Connection) -> Self {
        Self { conn }
    }
    
    pub fn create(&self, user_id: &str, request: CreatePageRequest) -> Result<Page> {
        let id = format!("page_{}", Uuid::new_v4());
        let tags_json = request.tags.as_ref().map(|t| serde_json::to_string(t).unwrap_or_default());
        let metadata_json = request.metadata.as_ref().map(|m| serde_json::to_string(m).unwrap_or_default());
        let now = Utc::now();
        
        self.conn.execute(
            "INSERT INTO pages (id, user_id, title, content, type, status, tags, metadata, parent_id, created_at, updated_at)
             VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10, ?11)",
            params![
                &id,
                user_id,
                &request.title,
                &request.content,
                request.page_type.as_deref().unwrap_or("page"),
                "draft",
                &tags_json,
                &metadata_json,
                &request.parent_id,
                &now,
                &now
            ],
        )?;
        
        self.get_by_id(&id)?.ok_or_else(|| anyhow::anyhow!("Failed to retrieve created page"))
    }
    
    pub fn get_by_id(&self, id: &str) -> Result<Option<Page>> {
        let mut stmt = self.conn.prepare(
            "SELECT id, user_id, title, content, type, status, tags, metadata, 
                    is_pinned, is_template, parent_id, created_at, updated_at
             FROM pages WHERE id = ?1"
        )?;
        
        let page = stmt
            .query_row(params![id], Page::from_row)
            .ok();
        
        Ok(page)
    }
    
    pub fn get_user_pages(&self, user_id: &str, limit: i64, offset: i64) -> Result<Vec<Page>> {
        let mut stmt = self.conn.prepare(
            "SELECT id, user_id, title, content, type, status, tags, metadata,
                    is_pinned, is_template, parent_id, created_at, updated_at
             FROM pages 
             WHERE user_id = ?1 
             ORDER BY created_at DESC 
             LIMIT ?2 OFFSET ?3"
        )?;
        
        let pages = stmt
            .query_map(params![user_id, limit, offset], Page::from_row)?
            .collect::<Result<Vec<_>, _>>()?;
        
        Ok(pages)
    }
    
    pub fn update(&self, id: &str, user_id: &str, request: UpdatePageRequest) -> Result<Page> {
        // Start transaction
        let tx = self.conn.unchecked_transaction()?;
        
        // Build dynamic update query
        let mut update_strings = vec![];
        let mut updates = vec!["updated_at = ?1"];
        let mut params: Vec<Box<dyn rusqlite::ToSql>> = vec![Box::new(Utc::now())];
        let mut param_idx = 2;
        
        if let Some(title) = &request.title {
            update_strings.push(format!("title = ?{}", param_idx));
            params.push(Box::new(title.clone()));
            param_idx += 1;
        }
        
        if let Some(content) = &request.content {
            update_strings.push(format!("content = ?{}", param_idx));
            params.push(Box::new(content.clone()));
            param_idx += 1;
        }
        
        if let Some(status) = &request.status {
            update_strings.push(format!("status = ?{}", param_idx));
            params.push(Box::new(status.clone()));
            param_idx += 1;
        }
        
        if let Some(tags) = &request.tags {
            update_strings.push(format!("tags = ?{}", param_idx));
            params.push(Box::new(serde_json::to_string(tags)?));
            param_idx += 1;
        }
        
        if let Some(metadata) = &request.metadata {
            update_strings.push(format!("metadata = ?{}", param_idx));
            params.push(Box::new(serde_json::to_string(metadata)?));
            param_idx += 1;
        }
        
        if let Some(is_pinned) = request.is_pinned {
            update_strings.push(format!("is_pinned = ?{}", param_idx));
            params.push(Box::new(is_pinned));
            param_idx += 1;
        }
        
        // Combine all updates
        for update in &update_strings {
            updates.push(update.as_str());
        }
        
        // Add id and user_id to params
        params.push(Box::new(id.to_string()));
        params.push(Box::new(user_id.to_string()));
        
        let query = format!(
            "UPDATE pages SET {} WHERE id = ?{} AND user_id = ?{}",
            updates.join(", "),
            param_idx,
            param_idx + 1
        );
        
        let rows_affected = tx.execute(&query, rusqlite::params_from_iter(params.iter()))?;
        
        if rows_affected == 0 {
            return Err(anyhow::anyhow!("Page not found or unauthorized"));
        }
        
        tx.commit()?;
        
        self.get_by_id(id)?.ok_or_else(|| anyhow::anyhow!("Failed to retrieve updated page"))
    }
    
    pub fn delete(&self, id: &str, user_id: &str) -> Result<bool> {
        let rows_affected = self.conn.execute(
            "DELETE FROM pages WHERE id = ?1 AND user_id = ?2",
            params![id, user_id],
        )?;
        
        Ok(rows_affected > 0)
    }
    
    pub fn search(&self, user_id: &str, query: &str) -> Result<Vec<Page>> {
        let search_pattern = format!("%{}%", query);
        
        let mut stmt = self.conn.prepare(
            "SELECT id, user_id, title, content, type, status, tags, metadata,
                    is_pinned, is_template, parent_id, created_at, updated_at
             FROM pages 
             WHERE user_id = ?1 
             AND (title LIKE ?2 OR content LIKE ?2 OR tags LIKE ?2)
             ORDER BY 
                CASE 
                    WHEN title LIKE ?2 THEN 1
                    WHEN tags LIKE ?2 THEN 2
                    ELSE 3
                END,
                created_at DESC
             LIMIT 50"
        )?;
        
        let pages = stmt
            .query_map(params![user_id, &search_pattern], Page::from_row)?
            .collect::<Result<Vec<_>, _>>()?;
        
        Ok(pages)
    }
    
    pub fn get_templates(&self, is_public: bool) -> Result<Vec<Page>> {
        if !is_public {
            return Ok(vec![]);
        }
        
        let mut stmt = self.conn.prepare(
            "SELECT id, user_id, title, content, type, status, tags, metadata,
                    is_pinned, is_template, parent_id, created_at, updated_at
             FROM pages 
             WHERE is_template = 1
             ORDER BY created_at DESC"
        )?;
        
        let pages = stmt
            .query_map([], Page::from_row)?
            .collect::<Result<Vec<_>, _>>()?;
        
        Ok(pages)
    }
}