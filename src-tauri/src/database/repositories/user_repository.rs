use anyhow::Result;
use chrono::{DateTime, Utc};
use rusqlite::{params, Connection, Row};
use serde::{Deserialize, Serialize};
use serde_json;
use uuid::Uuid;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct User {
    pub id: String,
    pub email: String,
    pub username: String,
    pub full_name: Option<String>,
    pub role: String,
    pub metadata: Option<serde_json::Value>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

impl User {
    fn from_row(row: &Row) -> rusqlite::Result<Self> {
        let metadata_json: Option<String> = row.get(5)?;
        let metadata = metadata_json
            .and_then(|s| serde_json::from_str(&s).ok());
        
        Ok(User {
            id: row.get(0)?,
            email: row.get(1)?,
            username: row.get(2)?,
            full_name: row.get(3)?,
            role: row.get(4)?,
            metadata,
            created_at: row.get(6)?,
            updated_at: row.get(7)?,
        })
    }
}

pub struct UserRepository;

impl UserRepository {
    pub fn create_table(conn: &Connection) -> Result<()> {
        conn.execute(
            "CREATE TABLE IF NOT EXISTS users (
                id TEXT PRIMARY KEY,
                email TEXT NOT NULL UNIQUE,
                username TEXT NOT NULL UNIQUE,
                full_name TEXT,
                role TEXT NOT NULL DEFAULT 'user',
                metadata TEXT,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL
            )",
            [],
        )?;
        Ok(())
    }

    pub fn create(conn: &Connection, user: &User) -> Result<()> {
        let metadata_json = user.metadata.as_ref()
            .map(|m| serde_json::to_string(m))
            .transpose()?;

        conn.execute(
            "INSERT INTO users (id, email, username, full_name, role, metadata, created_at, updated_at)
             VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8)",
            params![
                user.id,
                user.email,
                user.username,
                user.full_name,
                user.role,
                metadata_json,
                user.created_at.to_rfc3339(),
                user.updated_at.to_rfc3339(),
            ],
        )?;
        Ok(())
    }

    pub fn find_by_id(conn: &Connection, id: &str) -> Result<Option<User>> {
        let mut stmt = conn.prepare(
            "SELECT id, email, username, full_name, role, metadata, created_at, updated_at
             FROM users WHERE id = ?1"
        )?;
        
        let user = stmt.query_row(params![id], User::from_row).ok();
        Ok(user)
    }

    pub fn find_by_email(conn: &Connection, email: &str) -> Result<Option<User>> {
        let mut stmt = conn.prepare(
            "SELECT id, email, username, full_name, role, metadata, created_at, updated_at
             FROM users WHERE email = ?1"
        )?;
        
        let user = stmt.query_row(params![email], User::from_row).ok();
        Ok(user)
    }

    pub fn find_by_username(conn: &Connection, username: &str) -> Result<Option<User>> {
        let mut stmt = conn.prepare(
            "SELECT id, email, username, full_name, role, metadata, created_at, updated_at
             FROM users WHERE username = ?1"
        )?;
        
        let user = stmt.query_row(params![username], User::from_row).ok();
        Ok(user)
    }

    pub fn update(conn: &Connection, user: &User) -> Result<()> {
        let metadata_json = user.metadata.as_ref()
            .map(|m| serde_json::to_string(m))
            .transpose()?;

        conn.execute(
            "UPDATE users SET email = ?1, username = ?2, full_name = ?3, role = ?4, metadata = ?5, updated_at = ?6
             WHERE id = ?7",
            params![
                user.email,
                user.username,
                user.full_name,
                user.role,
                metadata_json,
                user.updated_at.to_rfc3339(),
                user.id,
            ],
        )?;
        Ok(())
    }

    pub fn delete(conn: &Connection, id: &str) -> Result<()> {
        conn.execute("DELETE FROM users WHERE id = ?1", params![id])?;
        Ok(())
    }

    pub fn list_all(conn: &Connection) -> Result<Vec<User>> {
        let mut stmt = conn.prepare(
            "SELECT id, email, username, full_name, role, metadata, created_at, updated_at
             FROM users ORDER BY created_at DESC"
        )?;
        
        let users = stmt.query_map([], User::from_row)?
            .collect::<rusqlite::Result<Vec<_>>>()?;
        Ok(users)
    }
}