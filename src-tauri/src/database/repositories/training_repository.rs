//! Training Repository
//! Handles CRUD operations for training-related data

use anyhow::{anyhow, Result};
use rusqlite::{params, Connection, Row};
use serde::{Deserialize, Serialize};
use crate::database::Database;
use crate::training::models::*;

#[derive(Clone)]
pub struct TrainingRepository {
    db: Database,
}

impl TrainingRepository {
    pub fn new(db: Database) -> Self {
        Self { db }
    }

    // Training Needs CRUD Operations
    pub fn create_training_need(&self, training_need: &TrainingNeed) -> Result<i64> {
        self.db.with_connection(|conn| {
            let mut stmt = conn.prepare(
                "INSERT INTO training_needs (
                    employee_id, employee_name, department, position, manager, hire_date, location,
                    current_skills, required_skills, skill_gaps, priority, training_type,
                    recommended_training, estimated_cost, currency, expected_roi, completion_timeline,
                    status, completion_percentage, assessment_score, certificate_number, expiry_date, notes
                ) VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10, ?11, ?12, ?13, ?14, ?15, ?16, ?17, ?18, ?19, ?20, ?21, ?22, ?23)"
            )?;

            stmt.execute(params![
                training_need.employee_id,
                training_need.employee_name,
                training_need.department,
                training_need.position,
                training_need.manager,
                training_need.hire_date,
                training_need.location,
                serde_json::to_string(&training_need.current_skills)?,
                serde_json::to_string(&training_need.required_skills)?,
                serde_json::to_string(&training_need.skill_gaps)?,
                training_need.priority.to_string(),
                training_need.training_type.to_string(),
                serde_json::to_string(&training_need.recommended_training)?,
                training_need.estimated_cost,
                training_need.currency,
                training_need.expected_roi,
                training_need.completion_timeline,
                training_need.status.to_string(),
                training_need.completion_percentage,
                training_need.assessment_score,
                training_need.certificate_number,
                training_need.expiry_date,
                training_need.notes
            ])?;

            Ok(conn.last_insert_rowid())
        })
    }

    pub fn get_training_need(&self, id: i64) -> Result<Option<TrainingNeed>> {
        self.db.with_connection(|conn| {
            let mut stmt = conn.prepare(
                "SELECT * FROM training_needs WHERE id = ?1"
            )?;

            let mut rows = stmt.query_map([id], |row| {
                self.row_to_training_need(row)
            })?;

            match rows.next() {
                Some(row) => Ok(Some(row?)),
                None => Ok(None),
            }
        })
    }

    pub fn get_training_needs_by_employee(&self, employee_id: &str) -> Result<Vec<TrainingNeed>> {
        self.db.with_connection(|conn| {
            let mut stmt = conn.prepare(
                "SELECT * FROM training_needs WHERE employee_id = ?1 ORDER BY created_at DESC"
            )?;

            let rows = stmt.query_map([employee_id], |row| {
                self.row_to_training_need(row)
            })?;

            let mut training_needs = Vec::new();
            for row in rows {
                training_needs.push(row?);
            }

            Ok(training_needs)
        })
    }

    pub fn get_training_needs_by_department(&self, department: &str) -> Result<Vec<TrainingNeed>> {
        self.db.with_connection(|conn| {
            let mut stmt = conn.prepare(
                "SELECT * FROM training_needs WHERE department = ?1 ORDER BY priority DESC, created_at DESC"
            )?;

            let rows = stmt.query_map([department], |row| {
                self.row_to_training_need(row)
            })?;

            let mut training_needs = Vec::new();
            for row in rows {
                training_needs.push(row?);
            }

            Ok(training_needs)
        })
    }

    pub fn update_training_need(&self, id: i64, training_need: &TrainingNeed) -> Result<()> {
        self.db.with_connection(|conn| {
            let mut stmt = conn.prepare(
                "UPDATE training_needs SET 
                    employee_name = ?1, department = ?2, position = ?3, manager = ?4,
                    current_skills = ?5, required_skills = ?6, skill_gaps = ?7, priority = ?8,
                    training_type = ?9, recommended_training = ?10, estimated_cost = ?11,
                    expected_roi = ?12, completion_timeline = ?13, status = ?14,
                    completion_percentage = ?15, assessment_score = ?16, certificate_number = ?17,
                    expiry_date = ?18, notes = ?19
                WHERE id = ?20"
            )?;

            stmt.execute(params![
                training_need.employee_name,
                training_need.department,
                training_need.position,
                training_need.manager,
                serde_json::to_string(&training_need.current_skills)?,
                serde_json::to_string(&training_need.required_skills)?,
                serde_json::to_string(&training_need.skill_gaps)?,
                training_need.priority.to_string(),
                training_need.training_type.to_string(),
                serde_json::to_string(&training_need.recommended_training)?,
                training_need.estimated_cost,
                training_need.expected_roi,
                training_need.completion_timeline,
                training_need.status.to_string(),
                training_need.completion_percentage,
                training_need.assessment_score,
                training_need.certificate_number,
                training_need.expiry_date,
                training_need.notes,
                id
            ])?;

            Ok(())
        })
    }

    pub fn delete_training_need(&self, id: i64) -> Result<()> {
        self.db.with_connection(|conn| {
            conn.execute("DELETE FROM training_needs WHERE id = ?1", [id])?;
            Ok(())
        })
    }

    // Training Programs CRUD Operations
    pub fn create_training_program(&self, program: &TrainingProgram) -> Result<i64> {
        self.db.with_connection(|conn| {
            let mut stmt = conn.prepare(
                "INSERT INTO training_programs (
                    name, description, category, provider, delivery_mode, duration_hours,
                    duration_days, cost_per_person, currency, min_participants, max_participants,
                    prerequisites, learning_objectives, target_audience, certification_offered,
                    certification_validity_months, effectiveness_score, completion_rate,
                    satisfaction_score, start_date, end_date, enrollment_deadline, location,
                    online_platform, materials_included
                ) VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10, ?11, ?12, ?13, ?14, ?15, ?16, ?17, ?18, ?19, ?20, ?21, ?22, ?23, ?24, ?25)"
            )?;

            stmt.execute(params![
                program.name,
                program.description,
                program.category.to_string(),
                program.provider,
                program.delivery_mode.to_string(),
                program.duration_hours,
                program.duration_days,
                program.cost_per_person,
                program.currency,
                program.min_participants,
                program.max_participants,
                serde_json::to_string(&program.prerequisites)?,
                serde_json::to_string(&program.learning_objectives)?,
                serde_json::to_string(&program.target_audience)?,
                program.certification_offered,
                program.certification_validity_months,
                program.effectiveness_score,
                program.completion_rate,
                program.satisfaction_score,
                program.start_date,
                program.end_date,
                program.enrollment_deadline,
                program.location,
                program.online_platform,
                serde_json::to_string(&program.materials_included)?
            ])?;

            Ok(conn.last_insert_rowid())
        })
    }

    pub fn get_training_programs(&self) -> Result<Vec<TrainingProgram>> {
        self.db.with_connection(|conn| {
            let mut stmt = conn.prepare(
                "SELECT * FROM training_programs ORDER BY name"
            )?;

            let rows = stmt.query_map([], |row| {
                self.row_to_training_program(row)
            })?;

            let mut programs = Vec::new();
            for row in rows {
                programs.push(row?);
            }

            Ok(programs)
        })
    }

    pub fn get_training_programs_by_category(&self, category: &TrainingCategory) -> Result<Vec<TrainingProgram>> {
        self.db.with_connection(|conn| {
            let mut stmt = conn.prepare(
                "SELECT * FROM training_programs WHERE category = ?1 ORDER BY name"
            )?;

            let rows = stmt.query_map([category.to_string()], |row| {
                self.row_to_training_program(row)
            })?;

            let mut programs = Vec::new();
            for row in rows {
                programs.push(row?);
            }

            Ok(programs)
        })
    }

    // Graduate Trainee Programs CRUD Operations
    pub fn create_graduate_trainee_program(&self, program: &GraduateTraineeProgram) -> Result<i64> {
        self.db.with_connection(|conn| {
            let mut stmt = conn.prepare(
                "INSERT INTO graduate_trainee_programs (
                    employee_id, employee_name, department, position, manager, mentor,
                    start_date, expected_end_date, actual_end_date, program_duration_months,
                    status, overall_progress, quarterly_reviews, skills_development,
                    certifications, training_hours_completed, training_hours_required,
                    budget_allocated, budget_spent, notes
                ) VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10, ?11, ?12, ?13, ?14, ?15, ?16, ?17, ?18, ?19, ?20)"
            )?;

            stmt.execute(params![
                program.employee_id,
                program.employee_name,
                program.department,
                program.position,
                program.manager,
                program.mentor,
                program.start_date,
                program.expected_end_date,
                program.actual_end_date,
                program.program_duration_months,
                program.status.to_string(),
                program.overall_progress,
                serde_json::to_string(&program.quarterly_reviews)?,
                serde_json::to_string(&program.skills_development)?,
                serde_json::to_string(&program.certifications)?,
                program.training_hours_completed,
                program.training_hours_required,
                program.budget_allocated,
                program.budget_spent,
                program.notes
            ])?;

            Ok(conn.last_insert_rowid())
        })
    }

    pub fn get_graduate_trainee_programs(&self) -> Result<Vec<GraduateTraineeProgram>> {
        self.db.with_connection(|conn| {
            let mut stmt = conn.prepare(
                "SELECT * FROM graduate_trainee_programs ORDER BY start_date DESC"
            )?;

            let rows = stmt.query_map([], |row| {
                self.row_to_graduate_trainee_program(row)
            })?;

            let mut programs = Vec::new();
            for row in rows {
                programs.push(row?);
            }

            Ok(programs)
        })
    }

    pub fn get_graduate_trainee_program_by_employee(&self, employee_id: &str) -> Result<Option<GraduateTraineeProgram>> {
        self.db.with_connection(|conn| {
            let mut stmt = conn.prepare(
                "SELECT * FROM graduate_trainee_programs WHERE employee_id = ?1"
            )?;

            let mut rows = stmt.query_map([employee_id], |row| {
                self.row_to_graduate_trainee_program(row)
            })?;

            match rows.next() {
                Some(row) => Ok(Some(row?)),
                None => Ok(None),
            }
        })
    }

    // Helper methods to convert database rows to structs
    fn row_to_training_need(&self, row: &Row) -> rusqlite::Result<TrainingNeed> {
        let current_skills_json: String = row.get("current_skills")?;
        let required_skills_json: String = row.get("required_skills")?;
        let skill_gaps_json: String = row.get("skill_gaps")?;
        let recommended_training_json: String = row.get("recommended_training")?;
        let priority_str: String = row.get("priority")?;
        let training_type_str: String = row.get("training_type")?;
        let status_str: String = row.get("status")?;

        Ok(TrainingNeed {
            id: Some(row.get("id")?),
            employee_id: row.get("employee_id")?,
            employee_name: row.get("employee_name")?,
            department: row.get("department")?,
            position: row.get("position")?,
            manager: row.get("manager")?,
            hire_date: row.get("hire_date")?,
            location: row.get("location")?,
            current_skills: serde_json::from_str(&current_skills_json).unwrap_or_default(),
            required_skills: serde_json::from_str(&required_skills_json).unwrap_or_default(),
            skill_gaps: serde_json::from_str(&skill_gaps_json).unwrap_or_default(),
            priority: priority_str.parse().unwrap_or(Priority::Medium),
            training_type: training_type_str.parse().unwrap_or(TrainingCategory::Technical),
            recommended_training: serde_json::from_str(&recommended_training_json).unwrap_or_default(),
            estimated_cost: row.get("estimated_cost")?,
            currency: row.get("currency")?,
            expected_roi: row.get("expected_roi")?,
            completion_timeline: row.get("completion_timeline")?,
            status: status_str.parse().unwrap_or(TrainingStatus::NotStarted),
            completion_percentage: row.get("completion_percentage")?,
            assessment_score: row.get("assessment_score")?,
            certificate_number: row.get("certificate_number")?,
            expiry_date: row.get("expiry_date")?,
            notes: row.get("notes")?,
        })
    }

    fn row_to_training_program(&self, row: &Row) -> rusqlite::Result<TrainingProgram> {
        let category_str: String = row.get("category")?;
        let delivery_mode_str: String = row.get("delivery_mode")?;
        let prerequisites_json: String = row.get("prerequisites")?;
        let learning_objectives_json: String = row.get("learning_objectives")?;
        let target_audience_json: String = row.get("target_audience")?;
        let materials_included_json: String = row.get("materials_included")?;

        Ok(TrainingProgram {
            id: Some(row.get("id")?),
            name: row.get("name")?,
            description: row.get("description")?,
            category: category_str.parse().unwrap_or(TrainingCategory::Technical),
            provider: row.get("provider")?,
            delivery_mode: delivery_mode_str.parse().unwrap_or(DeliveryMode::Online),
            duration_hours: row.get("duration_hours")?,
            duration_days: row.get("duration_days")?,
            cost_per_person: row.get("cost_per_person")?,
            currency: row.get("currency")?,
            min_participants: row.get("min_participants")?,
            max_participants: row.get("max_participants")?,
            prerequisites: serde_json::from_str(&prerequisites_json).unwrap_or_default(),
            learning_objectives: serde_json::from_str(&learning_objectives_json).unwrap_or_default(),
            target_audience: serde_json::from_str(&target_audience_json).unwrap_or_default(),
            certification_offered: row.get("certification_offered")?,
            certification_validity_months: row.get("certification_validity_months")?,
            effectiveness_score: row.get("effectiveness_score")?,
            completion_rate: row.get("completion_rate")?,
            satisfaction_score: row.get("satisfaction_score")?,
            start_date: row.get("start_date")?,
            end_date: row.get("end_date")?,
            enrollment_deadline: row.get("enrollment_deadline")?,
            location: row.get("location")?,
            online_platform: row.get("online_platform")?,
            materials_included: serde_json::from_str(&materials_included_json).unwrap_or_default(),
        })
    }

    fn row_to_graduate_trainee_program(&self, row: &Row) -> rusqlite::Result<GraduateTraineeProgram> {
        let status_str: String = row.get("status")?;
        let quarterly_reviews_json: String = row.get("quarterly_reviews")?;
        let skills_development_json: String = row.get("skills_development")?;
        let certifications_json: String = row.get("certifications")?;

        Ok(GraduateTraineeProgram {
            id: Some(row.get("id")?),
            employee_id: row.get("employee_id")?,
            employee_name: row.get("employee_name")?,
            department: row.get("department")?,
            position: row.get("position")?,
            manager: row.get("manager")?,
            mentor: row.get("mentor")?,
            start_date: row.get("start_date")?,
            expected_end_date: row.get("expected_end_date")?,
            actual_end_date: row.get("actual_end_date")?,
            program_duration_months: row.get("program_duration_months")?,
            status: status_str.parse().unwrap_or(TraineeStatus::NotStarted),
            overall_progress: row.get("overall_progress")?,
            quarterly_reviews: serde_json::from_str(&quarterly_reviews_json).unwrap_or_default(),
            skills_development: serde_json::from_str(&skills_development_json).unwrap_or_default(),
            certifications: serde_json::from_str(&certifications_json).unwrap_or_default(),
            training_hours_completed: row.get("training_hours_completed")?,
            training_hours_required: row.get("training_hours_required")?,
            budget_allocated: row.get("budget_allocated")?,
            budget_spent: row.get("budget_spent")?,
            notes: row.get("notes")?,
        })
    }

    // Analytics and Reporting Methods
    pub fn get_training_metrics(&self) -> Result<TrainingMetrics> {
        self.db.with_connection(|conn| {
            // Get total employees count (this would need to be adjusted based on your employee table)
            let total_employees: i32 = conn.query_row(
                "SELECT COUNT(DISTINCT employee_id) FROM training_needs",
                [],
                |row| row.get(0)
            ).unwrap_or(0);

            let employees_in_training: i32 = conn.query_row(
                "SELECT COUNT(DISTINCT employee_id) FROM training_needs WHERE status IN ('scheduled', 'in_progress')",
                [],
                |row| row.get(0)
            ).unwrap_or(0);

            let completed_this_month: i32 = conn.query_row(
                "SELECT COUNT(*) FROM training_needs WHERE status = 'completed' AND date(created_at) >= date('now', 'start of month')",
                [],
                |row| row.get(0)
            ).unwrap_or(0);

            let completed_this_quarter: i32 = conn.query_row(
                "SELECT COUNT(*) FROM training_needs WHERE status = 'completed' AND date(created_at) >= date('now', '-3 months')",
                [],
                |row| row.get(0)
            ).unwrap_or(0);

            let upcoming_sessions: i32 = conn.query_row(
                "SELECT COUNT(*) FROM training_needs WHERE status = 'scheduled'",
                [],
                |row| row.get(0)
            ).unwrap_or(0);

            let total_budget_allocated: f64 = conn.query_row(
                "SELECT COALESCE(SUM(estimated_cost), 0.0) FROM training_needs",
                [],
                |row| row.get(0)
            ).unwrap_or(0.0);

            let budget_utilized: f64 = conn.query_row(
                "SELECT COALESCE(SUM(estimated_cost), 0.0) FROM training_needs WHERE status IN ('completed', 'in_progress')",
                [],
                |row| row.get(0)
            ).unwrap_or(0.0);

            let average_completion_rate: f64 = conn.query_row(
                "SELECT COALESCE(AVG(completion_percentage), 0.0) FROM training_needs WHERE status != 'not_started'",
                [],
                |row| row.get(0)
            ).unwrap_or(0.0);

            let average_roi: f64 = conn.query_row(
                "SELECT COALESCE(AVG(expected_roi), 0.0) FROM training_needs",
                [],
                |row| row.get(0)
            ).unwrap_or(0.0);

            Ok(TrainingMetrics {
                total_employees,
                employees_in_training,
                completed_this_month,
                completed_this_quarter,
                upcoming_sessions,
                total_budget_allocated,
                budget_utilized,
                budget_remaining: total_budget_allocated - budget_utilized,
                average_completion_rate,
                average_satisfaction_score: 4.2, // This would need actual satisfaction data
                average_roi,
                skills_gap_closure_rate: 75.0, // This would need actual calculation
                certification_achievement_rate: 85.0, // This would need actual calculation
                training_hours_delivered: 0.0, // This would need actual calculation
                cost_per_employee: if total_employees > 0 { budget_utilized / total_employees as f64 } else { 0.0 },
                department_metrics: Vec::new(), // This would need department-specific calculations
                training_type_metrics: Vec::new(), // This would need type-specific calculations
            })
        })
    }
}