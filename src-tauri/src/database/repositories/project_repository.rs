use anyhow::Result;
use chrono::{DateTime, Utc};
use rusqlite::{params, Connection, Row};
use serde::{Deserialize, Serialize};
use serde_json;
use uuid::Uuid;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Project {
    pub id: String,
    pub user_id: String,
    pub name: String,
    pub description: Option<String>,
    pub status: String,
    pub metadata: Option<serde_json::Value>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

impl Project {
    fn from_row(row: &Row) -> rusqlite::Result<Self> {
        let metadata_json: Option<String> = row.get(5)?;
        let metadata = metadata_json
            .and_then(|s| serde_json::from_str(&s).ok());
        
        Ok(Project {
            id: row.get(0)?,
            user_id: row.get(1)?,
            name: row.get(2)?,
            description: row.get(3)?,
            status: row.get(4)?,
            metadata,
            created_at: row.get(6)?,
            updated_at: row.get(7)?,
        })
    }
}

pub struct ProjectRepository;

impl ProjectRepository {
    pub fn create_table(conn: &Connection) -> Result<()> {
        conn.execute(
            "CREATE TABLE IF NOT EXISTS projects (
                id TEXT PRIMARY KEY,
                user_id TEXT NOT NULL,
                name TEXT NOT NULL,
                description TEXT,
                status TEXT NOT NULL DEFAULT 'active',
                metadata TEXT,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL
            )",
            [],
        )?;
        Ok(())
    }

    pub fn create(conn: &Connection, project: &Project) -> Result<()> {
        let metadata_json = project.metadata.as_ref()
            .map(|m| serde_json::to_string(m))
            .transpose()?;

        conn.execute(
            "INSERT INTO projects (id, user_id, name, description, status, metadata, created_at, updated_at)
             VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8)",
            params![
                project.id,
                project.user_id,
                project.name,
                project.description,
                project.status,
                metadata_json,
                project.created_at.to_rfc3339(),
                project.updated_at.to_rfc3339(),
            ],
        )?;
        Ok(())
    }

    pub fn find_by_id(conn: &Connection, id: &str) -> Result<Option<Project>> {
        let mut stmt = conn.prepare(
            "SELECT id, user_id, name, description, status, metadata, created_at, updated_at
             FROM projects WHERE id = ?1"
        )?;
        
        let project = stmt.query_row(params![id], Project::from_row).ok();
        Ok(project)
    }

    pub fn find_by_user(conn: &Connection, user_id: &str) -> Result<Vec<Project>> {
        let mut stmt = conn.prepare(
            "SELECT id, user_id, name, description, status, metadata, created_at, updated_at
             FROM projects WHERE user_id = ?1 ORDER BY updated_at DESC"
        )?;
        
        let projects = stmt.query_map(params![user_id], Project::from_row)?
            .collect::<rusqlite::Result<Vec<_>>>()?;
        Ok(projects)
    }

    pub fn update(conn: &Connection, project: &Project) -> Result<()> {
        let metadata_json = project.metadata.as_ref()
            .map(|m| serde_json::to_string(m))
            .transpose()?;

        conn.execute(
            "UPDATE projects SET name = ?1, description = ?2, status = ?3, metadata = ?4, updated_at = ?5
             WHERE id = ?6",
            params![
                project.name,
                project.description,
                project.status,
                metadata_json,
                project.updated_at.to_rfc3339(),
                project.id,
            ],
        )?;
        Ok(())
    }

    pub fn delete(conn: &Connection, id: &str) -> Result<()> {
        conn.execute("DELETE FROM projects WHERE id = ?1", params![id])?;
        Ok(())
    }
}