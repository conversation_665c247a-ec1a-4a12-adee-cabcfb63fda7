use anyhow::Result;
use chrono::{DateTime, Utc};
use rusqlite::{params, Connection, Row};
use serde::{Deserialize, Serialize};
use serde_json;
use uuid::Uuid;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Source {
    pub id: String,
    pub project_id: String,
    pub name: String,
    pub source_type: String,
    pub url: Option<String>,
    pub content: Option<String>,
    pub metadata: Option<serde_json::Value>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

impl Source {
    fn from_row(row: &Row) -> rusqlite::Result<Self> {
        let metadata_json: Option<String> = row.get(6)?;
        let metadata = metadata_json
            .and_then(|s| serde_json::from_str(&s).ok());
        
        Ok(Source {
            id: row.get(0)?,
            project_id: row.get(1)?,
            name: row.get(2)?,
            source_type: row.get(3)?,
            url: row.get(4)?,
            content: row.get(5)?,
            metadata,
            created_at: row.get(7)?,
            updated_at: row.get(8)?,
        })
    }
}

pub struct SourceRepository;

impl SourceRepository {
    pub fn create_table(conn: &Connection) -> Result<()> {
        conn.execute(
            "CREATE TABLE IF NOT EXISTS sources (
                id TEXT PRIMARY KEY,
                project_id TEXT NOT NULL,
                name TEXT NOT NULL,
                source_type TEXT NOT NULL,
                url TEXT,
                content TEXT,
                metadata TEXT,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL,
                FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE
            )",
            [],
        )?;
        Ok(())
    }

    pub fn create(conn: &Connection, source: &Source) -> Result<()> {
        let metadata_json = source.metadata.as_ref()
            .map(|m| serde_json::to_string(m))
            .transpose()?;

        conn.execute(
            "INSERT INTO sources (id, project_id, name, source_type, url, content, metadata, created_at, updated_at)
             VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9)",
            params![
                source.id,
                source.project_id,
                source.name,
                source.source_type,
                source.url,
                source.content,
                metadata_json,
                source.created_at.to_rfc3339(),
                source.updated_at.to_rfc3339(),
            ],
        )?;
        Ok(())
    }

    pub fn find_by_id(conn: &Connection, id: &str) -> Result<Option<Source>> {
        let mut stmt = conn.prepare(
            "SELECT id, project_id, name, source_type, url, content, metadata, created_at, updated_at
             FROM sources WHERE id = ?1"
        )?;
        
        let source = stmt.query_row(params![id], Source::from_row).ok();
        Ok(source)
    }

    pub fn find_by_project(conn: &Connection, project_id: &str) -> Result<Vec<Source>> {
        let mut stmt = conn.prepare(
            "SELECT id, project_id, name, source_type, url, content, metadata, created_at, updated_at
             FROM sources WHERE project_id = ?1 ORDER BY created_at DESC"
        )?;
        
        let sources = stmt.query_map(params![project_id], Source::from_row)?
            .collect::<rusqlite::Result<Vec<_>>>()?;
        Ok(sources)
    }

    pub fn update(conn: &Connection, source: &Source) -> Result<()> {
        let metadata_json = source.metadata.as_ref()
            .map(|m| serde_json::to_string(m))
            .transpose()?;

        conn.execute(
            "UPDATE sources SET name = ?1, source_type = ?2, url = ?3, content = ?4, metadata = ?5, updated_at = ?6
             WHERE id = ?7",
            params![
                source.name,
                source.source_type,
                source.url,
                source.content,
                metadata_json,
                source.updated_at.to_rfc3339(),
                source.id,
            ],
        )?;
        Ok(())
    }

    pub fn delete(conn: &Connection, id: &str) -> Result<()> {
        conn.execute("DELETE FROM sources WHERE id = ?1", params![id])?;
        Ok(())
    }
}