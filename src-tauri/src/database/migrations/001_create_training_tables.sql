-- Training Database Schema Migration
-- Creates tables for training needs analysis, competency models, skill matrices, and analytics

-- Training Needs Table
CREATE TABLE IF NOT EXISTS training_needs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    employee_id TEXT NOT NULL,
    employee_name TEXT NOT NULL,
    department TEXT NOT NULL,
    position TEXT NOT NULL,
    manager TEXT,
    hire_date TEXT,
    location TEXT,
    current_skills TEXT, -- JSON array
    required_skills TEXT, -- JSON array
    skill_gaps TEXT, -- JSON array of SkillGap objects
    priority TEXT NOT NULL CHECK (priority IN ('critical', 'high', 'medium', 'low')),
    training_type TEXT NOT NULL CHECK (training_type IN ('technical', 'soft_skills', 'leadership', 'compliance', 'safety', 'professional')),
    recommended_training TEXT NOT NULL, -- JSON array
    estimated_cost REAL NOT NULL DEFAULT 0.0,
    currency TEXT NOT NULL DEFAULT 'USD',
    expected_roi REAL NOT NULL DEFAULT 0.0,
    completion_timeline TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'not_started' CHECK (status IN ('not_started', 'scheduled', 'in_progress', 'completed', 'cancelled', 'on_hold')),
    completion_percentage REAL NOT NULL DEFAULT 0.0,
    assessment_score REAL,
    certificate_number TEXT,
    expiry_date TEXT,
    notes TEXT,
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Training Programs Table
CREATE TABLE IF NOT EXISTS training_programs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    description TEXT NOT NULL,
    category TEXT NOT NULL CHECK (category IN ('technical', 'soft_skills', 'leadership', 'compliance', 'safety', 'professional')),
    provider TEXT NOT NULL,
    delivery_mode TEXT NOT NULL CHECK (delivery_mode IN ('online', 'in_person', 'hybrid', 'self_paced', 'virtual')),
    duration_hours REAL NOT NULL,
    duration_days INTEGER,
    cost_per_person REAL NOT NULL,
    currency TEXT NOT NULL DEFAULT 'USD',
    min_participants INTEGER,
    max_participants INTEGER,
    prerequisites TEXT, -- JSON array
    learning_objectives TEXT NOT NULL, -- JSON array
    target_audience TEXT, -- JSON array
    certification_offered BOOLEAN NOT NULL DEFAULT 0,
    certification_validity_months INTEGER,
    effectiveness_score REAL NOT NULL DEFAULT 0.0,
    completion_rate REAL NOT NULL DEFAULT 0.0,
    satisfaction_score REAL,
    start_date TEXT,
    end_date TEXT,
    enrollment_deadline TEXT,
    location TEXT,
    online_platform TEXT,
    materials_included TEXT, -- JSON array
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Competency Models Table
CREATE TABLE IF NOT EXISTS competency_models (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    description TEXT,
    category TEXT NOT NULL,
    department TEXT,
    role_level TEXT,
    competencies TEXT NOT NULL, -- JSON array of competency objects
    business_criticality TEXT CHECK (business_criticality IN ('critical', 'important', 'beneficial')),
    industry_standard TEXT,
    version TEXT NOT NULL DEFAULT '1.0',
    is_active BOOLEAN NOT NULL DEFAULT 1,
    created_by TEXT,
    approved_by TEXT,
    approval_date TEXT,
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Skill Matrices Table
CREATE TABLE IF NOT EXISTS skill_matrices (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    description TEXT,
    department TEXT,
    role TEXT,
    skills TEXT NOT NULL, -- JSON array of skill objects with levels
    proficiency_levels TEXT NOT NULL, -- JSON array of level definitions
    assessment_criteria TEXT, -- JSON object
    created_by TEXT,
    is_template BOOLEAN NOT NULL DEFAULT 0,
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Employee Assessments Table
CREATE TABLE IF NOT EXISTS employee_assessments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    employee_id TEXT NOT NULL,
    competency_id INTEGER,
    skill_id TEXT NOT NULL,
    score REAL NOT NULL,
    max_score REAL NOT NULL,
    assessment_date TEXT NOT NULL,
    assessor_id TEXT NOT NULL,
    assessment_type TEXT NOT NULL CHECK (assessment_type IN ('self', 'manager', '360', 'observation', 'test')),
    notes TEXT,
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (competency_id) REFERENCES competency_models(id) ON DELETE SET NULL
);

-- Graduate Trainee Programs Table
CREATE TABLE IF NOT EXISTS graduate_trainee_programs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    employee_id TEXT NOT NULL UNIQUE,
    employee_name TEXT NOT NULL,
    department TEXT NOT NULL,
    position TEXT NOT NULL,
    manager TEXT NOT NULL,
    mentor TEXT,
    start_date TEXT NOT NULL,
    expected_end_date TEXT NOT NULL,
    actual_end_date TEXT,
    program_duration_months INTEGER NOT NULL DEFAULT 18,
    status TEXT NOT NULL DEFAULT 'not_started' CHECK (status IN ('not_started', 'in_progress', 'on_hold', 'completed', 'terminated')),
    overall_progress REAL NOT NULL DEFAULT 0.0,
    quarterly_reviews TEXT, -- JSON array of QuarterlyReview objects
    skills_development TEXT, -- JSON array of SkillDevelopment objects
    certifications TEXT, -- JSON array of Certification objects
    training_hours_completed REAL NOT NULL DEFAULT 0.0,
    training_hours_required REAL NOT NULL DEFAULT 1000.0,
    budget_allocated REAL NOT NULL DEFAULT 0.0,
    budget_spent REAL NOT NULL DEFAULT 0.0,
    notes TEXT,
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Learning Paths Table
CREATE TABLE IF NOT EXISTS learning_paths (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    employee_id TEXT NOT NULL,
    employee_name TEXT NOT NULL,
    path_name TEXT NOT NULL,
    description TEXT,
    target_role TEXT,
    modules TEXT NOT NULL, -- JSON array of LearningModule objects
    total_duration_hours REAL NOT NULL DEFAULT 0.0,
    total_cost REAL NOT NULL DEFAULT 0.0,
    currency TEXT NOT NULL DEFAULT 'USD',
    progress_percentage REAL NOT NULL DEFAULT 0.0,
    status TEXT NOT NULL DEFAULT 'not_started' CHECK (status IN ('not_started', 'in_progress', 'completed', 'paused', 'abandoned')),
    start_date TEXT NOT NULL,
    expected_completion TEXT NOT NULL,
    actual_completion TEXT,
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Approval Workflows Table
CREATE TABLE IF NOT EXISTS approval_workflows (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    request_id TEXT NOT NULL UNIQUE,
    request_type TEXT NOT NULL,
    training_need_id INTEGER,
    total_budget REAL NOT NULL,
    currency TEXT NOT NULL DEFAULT 'USD',
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'approved', 'rejected', 'revision', 'cancelled')),
    current_stage INTEGER NOT NULL DEFAULT 1,
    stages TEXT NOT NULL, -- JSON array of ApprovalStage objects
    submitted_by TEXT NOT NULL,
    submitted_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    completed_at TEXT,
    notes TEXT,
    FOREIGN KEY (training_need_id) REFERENCES training_needs(id) ON DELETE CASCADE
);

-- Historical Analytics Data Table
CREATE TABLE IF NOT EXISTS historical_analytics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    period TEXT NOT NULL, -- e.g., '2025-01', '2025-Q1'
    period_type TEXT NOT NULL CHECK (period_type IN ('monthly', 'quarterly', 'yearly')),
    department TEXT,
    metrics TEXT NOT NULL, -- JSON object with all metrics
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Training Metrics Snapshots Table
CREATE TABLE IF NOT EXISTS training_metrics_snapshots (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    snapshot_date TEXT NOT NULL,
    total_employees INTEGER NOT NULL DEFAULT 0,
    employees_in_training INTEGER NOT NULL DEFAULT 0,
    completed_this_month INTEGER NOT NULL DEFAULT 0,
    completed_this_quarter INTEGER NOT NULL DEFAULT 0,
    upcoming_sessions INTEGER NOT NULL DEFAULT 0,
    total_budget_allocated REAL NOT NULL DEFAULT 0.0,
    budget_utilized REAL NOT NULL DEFAULT 0.0,
    budget_remaining REAL NOT NULL DEFAULT 0.0,
    average_completion_rate REAL NOT NULL DEFAULT 0.0,
    average_satisfaction_score REAL NOT NULL DEFAULT 0.0,
    average_roi REAL NOT NULL DEFAULT 0.0,
    skills_gap_closure_rate REAL NOT NULL DEFAULT 0.0,
    certification_achievement_rate REAL NOT NULL DEFAULT 0.0,
    training_hours_delivered REAL NOT NULL DEFAULT 0.0,
    cost_per_employee REAL NOT NULL DEFAULT 0.0,
    department_metrics TEXT, -- JSON array of DepartmentMetrics
    training_type_metrics TEXT, -- JSON array of TrainingTypeMetrics
    period_start TEXT NOT NULL,
    period_end TEXT NOT NULL,
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_training_needs_employee_id ON training_needs(employee_id);
CREATE INDEX IF NOT EXISTS idx_training_needs_department ON training_needs(department);
CREATE INDEX IF NOT EXISTS idx_training_needs_status ON training_needs(status);
CREATE INDEX IF NOT EXISTS idx_training_needs_priority ON training_needs(priority);

CREATE INDEX IF NOT EXISTS idx_training_programs_category ON training_programs(category);
CREATE INDEX IF NOT EXISTS idx_training_programs_provider ON training_programs(provider);
CREATE INDEX IF NOT EXISTS idx_training_programs_delivery_mode ON training_programs(delivery_mode);

CREATE INDEX IF NOT EXISTS idx_competency_models_category ON competency_models(category);
CREATE INDEX IF NOT EXISTS idx_competency_models_department ON competency_models(department);
CREATE INDEX IF NOT EXISTS idx_competency_models_active ON competency_models(is_active);

CREATE INDEX IF NOT EXISTS idx_skill_matrices_department ON skill_matrices(department);
CREATE INDEX IF NOT EXISTS idx_skill_matrices_role ON skill_matrices(role);
CREATE INDEX IF NOT EXISTS idx_skill_matrices_template ON skill_matrices(is_template);

CREATE INDEX IF NOT EXISTS idx_employee_assessments_employee_id ON employee_assessments(employee_id);
CREATE INDEX IF NOT EXISTS idx_employee_assessments_skill_id ON employee_assessments(skill_id);
CREATE INDEX IF NOT EXISTS idx_employee_assessments_date ON employee_assessments(assessment_date);

CREATE INDEX IF NOT EXISTS idx_graduate_trainee_programs_employee_id ON graduate_trainee_programs(employee_id);
CREATE INDEX IF NOT EXISTS idx_graduate_trainee_programs_department ON graduate_trainee_programs(department);
CREATE INDEX IF NOT EXISTS idx_graduate_trainee_programs_status ON graduate_trainee_programs(status);
CREATE INDEX IF NOT EXISTS idx_graduate_trainee_programs_manager ON graduate_trainee_programs(manager);

CREATE INDEX IF NOT EXISTS idx_learning_paths_employee_id ON learning_paths(employee_id);
CREATE INDEX IF NOT EXISTS idx_learning_paths_status ON learning_paths(status);

CREATE INDEX IF NOT EXISTS idx_approval_workflows_request_id ON approval_workflows(request_id);
CREATE INDEX IF NOT EXISTS idx_approval_workflows_status ON approval_workflows(status);
CREATE INDEX IF NOT EXISTS idx_approval_workflows_submitted_by ON approval_workflows(submitted_by);

CREATE INDEX IF NOT EXISTS idx_historical_analytics_period ON historical_analytics(period);
CREATE INDEX IF NOT EXISTS idx_historical_analytics_department ON historical_analytics(department);

CREATE INDEX IF NOT EXISTS idx_training_metrics_snapshots_date ON training_metrics_snapshots(snapshot_date);

-- Create triggers for updating timestamps
CREATE TRIGGER IF NOT EXISTS update_training_needs_timestamp 
AFTER UPDATE ON training_needs 
FOR EACH ROW
BEGIN
    UPDATE training_needs SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS update_training_programs_timestamp 
AFTER UPDATE ON training_programs 
FOR EACH ROW
BEGIN
    UPDATE training_programs SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS update_competency_models_timestamp 
AFTER UPDATE ON competency_models 
FOR EACH ROW
BEGIN
    UPDATE competency_models SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS update_skill_matrices_timestamp 
AFTER UPDATE ON skill_matrices 
FOR EACH ROW
BEGIN
    UPDATE skill_matrices SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS update_graduate_trainee_programs_timestamp 
AFTER UPDATE ON graduate_trainee_programs 
FOR EACH ROW
BEGIN
    UPDATE graduate_trainee_programs SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS update_learning_paths_timestamp 
AFTER UPDATE ON learning_paths 
FOR EACH ROW
BEGIN
    UPDATE learning_paths SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;