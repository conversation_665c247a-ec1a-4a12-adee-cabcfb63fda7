use anyhow::{anyhow, Result};
use rusqlite::{Connection, OpenFlags};
use std::env;
use std::path::PathBuf;
use std::sync::{Arc, Mutex};
use serde::{Deserialize, Serialize};

pub type DbConnection = Arc<Mutex<Connection>>;

#[derive(Clone)]
pub struct Database {
    pub conn: DbConnection,
    pub database_path: PathBuf,
}

impl Database {
    pub fn new() -> Result<Self> {
        let database_path = Self::get_database_path()?;
        Self::with_path(database_path)
    }
    
    pub fn with_path(database_path: PathBuf) -> Result<Self> {
        // Create parent directory if it doesn't exist
        if let Some(parent) = database_path.parent() {
            std::fs::create_dir_all(parent)?;
        }
        
        // Open connection with appropriate flags
        let conn = Connection::open_with_flags(
            &database_path,
            OpenFlags::SQLITE_OPEN_READ_WRITE 
                | OpenFlags::SQLITE_OPEN_CREATE 
                | OpenFlags::SQLITE_OPEN_URI
                | OpenFlags::SQLITE_OPEN_NO_MUTEX,
        )?;
        
        // Set pragmas for better performance
        conn.execute_batch(
            "
            PRAGMA journal_mode = WAL;
            PRAGMA synchronous = NORMAL;
            PRAGMA cache_size = 10000;
            PRAGMA temp_store = MEMORY;
            PRAGMA foreign_keys = ON;
            PRAGMA busy_timeout = 30000;
            ",
        )?;
        
        let db = Self {
            conn: Arc::new(Mutex::new(conn)),
            database_path: database_path.clone(),
        };
        
        // Run migrations
        db.run_migrations()?;
        
        Ok(db)
    }
    
    fn get_database_path() -> Result<PathBuf> {
        // Check for environment variable first
        if let Ok(url) = env::var("DATABASE_URL") {
            // Remove sqlite: prefix if present
            let path = url.strip_prefix("sqlite:").unwrap_or(&url);
            return Ok(PathBuf::from(path));
        }
        
        // Otherwise use app data directory
        let data_dir = dirs::data_dir()
            .ok_or_else(|| anyhow!("Could not determine data directory"))?;
        
        let app_dir = data_dir.join("beacon");
        std::fs::create_dir_all(&app_dir)?;
        
        Ok(app_dir.join("beacon.db"))
    }
    
    fn run_migrations(&self) -> Result<()> {
        let conn = self.conn.lock().map_err(|_| anyhow!("Failed to lock database connection"))?;
        
        // Check if migrations table exists
        let mut stmt = conn.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='migrations'")?;
        let table_exists = stmt.exists([])?;
        
        if !table_exists {
            // Create migrations table
            conn.execute(
                "CREATE TABLE migrations (
                    id INTEGER PRIMARY KEY,
                    version TEXT NOT NULL UNIQUE,
                    applied_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )",
                [],
            )?;
        }
        
        // Check current version
        let current_version = self.get_current_version(&conn)?;
        
        // Apply migrations based on version
        if current_version < 1 {
            self.run_migration_v1(&conn)?;
        }
        
        // Apply new schema if needed
        if current_version < 2 {
            self.run_migration_v2(&conn)?;
        }
        
        Ok(())
    }
    
    fn get_current_version(&self, conn: &Connection) -> Result<i32> {
        let version: Result<String, _> = conn.query_row(
            "SELECT version FROM migrations ORDER BY id DESC LIMIT 1",
            [],
            |row| row.get(0),
        );
        
        match version {
            Ok(v) => Ok(v.parse().unwrap_or(0)),
            Err(_) => Ok(0),
        }
    }
    
    fn run_migration_v1(&self, conn: &Connection) -> Result<()> {
        // Create initial tables (legacy schema)
        conn.execute_batch(
            "
            CREATE TABLE IF NOT EXISTS projects (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                description TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            );
            
            CREATE TABLE IF NOT EXISTS pages (
                id TEXT PRIMARY KEY,
                project_id TEXT NOT NULL,
                title TEXT NOT NULL,
                content TEXT NOT NULL DEFAULT '',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE
            );
            
            CREATE TABLE IF NOT EXISTS sources (
                id TEXT PRIMARY KEY,
                project_id TEXT,
                title TEXT NOT NULL,
                url TEXT,
                content TEXT,
                source_type TEXT NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE SET NULL
            );
            
            CREATE INDEX IF NOT EXISTS idx_pages_project_id ON pages(project_id);
            CREATE INDEX IF NOT EXISTS idx_sources_project_id ON sources(project_id);
            CREATE INDEX IF NOT EXISTS idx_sources_type ON sources(source_type);
            ",
        )?;
        
        // Mark migration as applied
        conn.execute(
            "INSERT OR IGNORE INTO migrations (version) VALUES (?1)",
            ["1"],
        )?;
        
        Ok(())
    }
    
    fn run_migration_v2(&self, conn: &Connection) -> Result<()> {
        // Apply the new comprehensive schema
        let schema_sql = include_str!("schema.sql");
        
        // Split the schema into individual statements and execute them
        let statements: Vec<&str> = schema_sql
            .split(';')
            .map(|s| s.trim())
            .filter(|s| !s.is_empty() && !s.starts_with("--"))
            .collect();
        
        for statement in statements {
            if !statement.trim().is_empty() {
                conn.execute(statement, [])?;
            }
        }
        
        // Mark migration as applied
        conn.execute(
            "INSERT OR IGNORE INTO migrations (version) VALUES (?1)",
            ["2"],
        )?;
        
        Ok(())
    }
    
    pub fn health_check(&self) -> Result<DatabaseHealth> {
        let conn = self.conn.lock().map_err(|_| anyhow!("Failed to lock database connection"))?;
        
        let start_time = std::time::Instant::now();
        
        // Simple query to check if database is responsive
        let result: i32 = conn.query_row("SELECT 1", [], |row| row.get(0))?;
        let response_time = start_time.elapsed();
        
        // Check database integrity
        let integrity_check: String = conn.query_row("PRAGMA integrity_check", [], |row| row.get(0))?;
        let is_healthy = result == 1 && integrity_check == "ok";
        
        // Get database size
        let page_count: i64 = conn.query_row("PRAGMA page_count", [], |row| row.get(0))?;
        let page_size: i64 = conn.query_row("PRAGMA page_size", [], |row| row.get(0))?;
        let database_size = page_count * page_size;
        
        Ok(DatabaseHealth {
            is_healthy,
            response_time_ms: response_time.as_millis() as u64,
            database_size_bytes: database_size as u64,
            integrity_check: integrity_check == "ok",
            connection_count: 1, // Single connection for rusqlite
        })
    }
    
    pub fn get_statistics(&self) -> Result<DatabaseStatistics> {
        let conn = self.conn.lock().map_err(|_| anyhow!("Failed to lock database connection"))?;
        
        // Get table counts
        let projects_count: i64 = conn.query_row(
            "SELECT COUNT(*) FROM projects", [], |row| row.get(0)
        ).unwrap_or(0);
        
        let pages_count: i64 = conn.query_row(
            "SELECT COUNT(*) FROM pages", [], |row| row.get(0)
        ).unwrap_or(0);
        
        let sources_count: i64 = conn.query_row(
            "SELECT COUNT(*) FROM sources", [], |row| row.get(0)
        ).unwrap_or(0);
        
        // Get database info
        let page_count: i64 = conn.query_row("PRAGMA page_count", [], |row| row.get(0))?;
        let page_size: i64 = conn.query_row("PRAGMA page_size", [], |row| row.get(0))?;
        let freelist_count: i64 = conn.query_row("PRAGMA freelist_count", [], |row| row.get(0))?;
        
        Ok(DatabaseStatistics {
            total_tables: 3, // projects, pages, sources (basic count)
            total_records: projects_count + pages_count + sources_count,
            database_size_bytes: (page_count * page_size) as u64,
            free_space_bytes: (freelist_count * page_size) as u64,
            connection_pool_size: 1,
            active_connections: 1,
        })
    }
    
    pub fn begin_transaction(&self) -> Result<()> {
        let conn = self.conn.lock().map_err(|_| anyhow!("Failed to lock database connection"))?;
        conn.execute("BEGIN TRANSACTION", [])?;
        Ok(())
    }
    
    pub fn commit_transaction(&self) -> Result<()> {
        let conn = self.conn.lock().map_err(|_| anyhow!("Failed to lock database connection"))?;
        conn.execute("COMMIT", [])?;
        Ok(())
    }
    
    pub fn rollback_transaction(&self) -> Result<()> {
        let conn = self.conn.lock().map_err(|_| anyhow!("Failed to lock database connection"))?;
        conn.execute("ROLLBACK", [])?;
        Ok(())
    }
    
    pub fn vacuum(&self) -> Result<()> {
        let conn = self.conn.lock().map_err(|_| anyhow!("Failed to lock database connection"))?;
        conn.execute("VACUUM", [])?;
        Ok(())
    }
    
    pub fn analyze(&self) -> Result<()> {
        let conn = self.conn.lock().map_err(|_| anyhow!("Failed to lock database connection"))?;
        conn.execute("ANALYZE", [])?;
        Ok(())
    }
    
    pub fn backup_to_path(&self, backup_path: &PathBuf) -> Result<()> {
        let conn = self.conn.lock().map_err(|_| anyhow!("Failed to lock database connection"))?;
        
        // Create backup directory if it doesn't exist
        if let Some(parent) = backup_path.parent() {
            std::fs::create_dir_all(parent)?;
        }
        
        // Simple file copy for SQLite backup
        std::fs::copy(&self.database_path, backup_path)?;
        Ok(())
    }
    
    // Helper method to execute queries with the connection
    pub fn with_connection<F, R>(&self, f: F) -> Result<R>
    where
        F: FnOnce(&Connection) -> Result<R>,
    {
        let conn = self.conn.lock().map_err(|_| anyhow!("Failed to lock database connection"))?;
        f(&*conn)
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DatabaseHealth {
    pub is_healthy: bool,
    pub response_time_ms: u64,
    pub database_size_bytes: u64,
    pub integrity_check: bool,
    pub connection_count: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DatabaseStatistics {
    pub total_tables: u64,
    pub total_records: i64,
    pub database_size_bytes: u64,
    pub free_space_bytes: u64,
    pub connection_pool_size: u32,
    pub active_connections: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DatabaseConfig {
    pub database_path: PathBuf,
    pub enable_wal: bool,
    pub cache_size: i32,
    pub busy_timeout_ms: u32,
    pub enable_foreign_keys: bool,
}

// Repository modules
pub mod repositories;

// Re-export commonly used types
pub use repositories::*;