-- Beacon Backend Database Schema
-- This file contains all table definitions for the Beacon application

-- Enable foreign key constraints
PRAGMA foreign_keys = ON;

-- ============================================================================
-- CORE TABLES
-- ============================================================================

-- Projects table - top-level organization unit
CREATE TABLE IF NOT EXISTS projects (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    settings TEXT NOT NULL DEFAULT '{}', -- JSON serialized ProjectSettings
    status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'archived', 'deleted')),
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    metadata TEXT DEFAULT '{}' -- JSON serialized Metadata
);

-- Pages table - individual pages within projects
CREATE TABLE IF NOT EXISTS pages (
    id TEXT PRIMARY KEY,
    project_id TEXT NOT NULL,
    title TEXT NOT NULL,
    content TEXT NOT NULL DEFAULT '',
    settings TEXT NOT NULL DEFAULT '{}', -- JSON serialized PageSettings
    layout TEXT NOT NULL DEFAULT 'default', -- JSON serialized PageLayout
    status TEXT NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived', 'deleted')),
    priority TEXT NOT NULL DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    metadata TEXT DEFAULT '{}', -- JSON serialized Metadata
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE
);

-- Page templates for reusable page structures
CREATE TABLE IF NOT EXISTS page_templates (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    template_content TEXT NOT NULL,
    settings TEXT NOT NULL DEFAULT '{}', -- JSON serialized PageSettings
    layout TEXT NOT NULL DEFAULT 'default', -- JSON serialized PageLayout
    category TEXT DEFAULT 'general',
    is_system BOOLEAN NOT NULL DEFAULT FALSE,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    metadata TEXT DEFAULT '{}'
);

-- Project templates for reusable project structures
CREATE TABLE IF NOT EXISTS project_templates (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    settings TEXT NOT NULL DEFAULT '{}', -- JSON serialized ProjectSettings
    category TEXT DEFAULT 'general',
    is_system BOOLEAN NOT NULL DEFAULT FALSE,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    metadata TEXT DEFAULT '{}'
);

-- ============================================================================
-- SOURCE AND CONTENT TABLES
-- ============================================================================

-- Sources table - external content sources
CREATE TABLE IF NOT EXISTS sources (
    id TEXT PRIMARY KEY,
    project_id TEXT,
    title TEXT NOT NULL,
    url TEXT,
    content TEXT,
    source_type TEXT NOT NULL CHECK (source_type IN ('url', 'file', 'text', 'api', 'database')),
    format TEXT, -- file format or content type
    size_bytes INTEGER DEFAULT 0,
    source_metadata TEXT DEFAULT '{}', -- JSON serialized SourceMetadata
    embedding_status TEXT NOT NULL DEFAULT 'pending' CHECK (embedding_status IN ('pending', 'processing', 'completed', 'failed')),
    embedding_vector TEXT, -- JSON serialized vector
    status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'archived', 'deleted')),
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    metadata TEXT DEFAULT '{}',
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE SET NULL
);

-- Supported formats configuration
CREATE TABLE IF NOT EXISTS supported_formats (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    extension TEXT NOT NULL,
    mime_type TEXT,
    category TEXT NOT NULL CHECK (category IN ('document', 'image', 'audio', 'video', 'archive', 'code', 'data')),
    max_size_mb INTEGER DEFAULT 100,
    is_enabled BOOLEAN NOT NULL DEFAULT TRUE,
    processing_options TEXT DEFAULT '{}', -- JSON serialized options
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- ============================================================================
-- AI AND CHAT TABLES
-- ============================================================================

-- Notebooks for organizing AI conversations
CREATE TABLE IF NOT EXISTS notebooks (
    id TEXT PRIMARY KEY,
    project_id TEXT,
    title TEXT NOT NULL,
    description TEXT,
    settings TEXT NOT NULL DEFAULT '{}', -- JSON serialized NotebookSettings
    status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'archived', 'deleted')),
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    metadata TEXT DEFAULT '{}',
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE SET NULL
);

-- Chat messages within notebooks
CREATE TABLE IF NOT EXISTS chat_messages (
    id TEXT PRIMARY KEY,
    notebook_id TEXT NOT NULL,
    role TEXT NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
    content TEXT NOT NULL,
    message_metadata TEXT DEFAULT '{}', -- JSON serialized MessageMetadata
    attachments TEXT DEFAULT '[]', -- JSON serialized array of Attachment
    citations TEXT DEFAULT '[]', -- JSON serialized array of Citation
    reactions TEXT DEFAULT '[]', -- JSON serialized array of Reaction
    status TEXT NOT NULL DEFAULT 'sent' CHECK (status IN ('draft', 'sent', 'delivered', 'failed', 'deleted')),
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    metadata TEXT DEFAULT '{}',
    FOREIGN KEY (notebook_id) REFERENCES notebooks(id) ON DELETE CASCADE
);

-- AI context for maintaining conversation state
CREATE TABLE IF NOT EXISTS ai_contexts (
    id TEXT PRIMARY KEY,
    notebook_id TEXT NOT NULL,
    context_data TEXT NOT NULL, -- JSON serialized context
    preferences TEXT DEFAULT '{}', -- JSON serialized AIPreferences
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (notebook_id) REFERENCES notebooks(id) ON DELETE CASCADE
);

-- Studio documents for AI-generated content
CREATE TABLE IF NOT EXISTS studio_documents (
    id TEXT PRIMARY KEY,
    project_id TEXT,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    document_type TEXT NOT NULL CHECK (document_type IN ('article', 'summary', 'analysis', 'report', 'note')),
    source_ids TEXT DEFAULT '[]', -- JSON array of source IDs
    status TEXT NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived', 'deleted')),
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    metadata TEXT DEFAULT '{}',
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE SET NULL
);

-- ============================================================================
-- SEARCH AND DISCOVERY TABLES
-- ============================================================================

-- Saved searches for quick access to common queries
CREATE TABLE IF NOT EXISTS saved_searches (
    id TEXT PRIMARY KEY,
    project_id TEXT,
    name TEXT NOT NULL,
    query TEXT NOT NULL,
    filters TEXT DEFAULT '{}', -- JSON serialized SearchFilters
    is_favorite BOOLEAN NOT NULL DEFAULT FALSE,
    usage_count INTEGER NOT NULL DEFAULT 0,
    last_used_at DATETIME,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    metadata TEXT DEFAULT '{}',
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE SET NULL
);

-- User patterns for personalization
CREATE TABLE IF NOT EXISTS user_patterns (
    id TEXT PRIMARY KEY,
    pattern_type TEXT NOT NULL CHECK (pattern_type IN ('search', 'content', 'workflow', 'preference')),
    pattern_data TEXT NOT NULL, -- JSON serialized pattern data
    confidence_score REAL NOT NULL DEFAULT 0.0,
    usage_count INTEGER NOT NULL DEFAULT 0,
    last_updated DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    metadata TEXT DEFAULT '{}'
);

-- ============================================================================
-- SYSTEM TABLES
-- ============================================================================

-- System configuration and settings
CREATE TABLE IF NOT EXISTS system_config (
    key TEXT PRIMARY KEY,
    value TEXT NOT NULL,
    description TEXT,
    is_encrypted BOOLEAN NOT NULL DEFAULT FALSE,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Audit log for tracking changes
CREATE TABLE IF NOT EXISTS audit_log (
    id TEXT PRIMARY KEY,
    table_name TEXT NOT NULL,
    record_id TEXT NOT NULL,
    action TEXT NOT NULL CHECK (action IN ('create', 'update', 'delete')),
    old_values TEXT, -- JSON serialized old values
    new_values TEXT, -- JSON serialized new values
    user_id TEXT,
    timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    metadata TEXT DEFAULT '{}'
);

-- ============================================================================
-- INDEXES FOR PERFORMANCE
-- ============================================================================

-- Project indexes
CREATE INDEX IF NOT EXISTS idx_projects_status ON projects(status);
CREATE INDEX IF NOT EXISTS idx_projects_created_at ON projects(created_at);
CREATE INDEX IF NOT EXISTS idx_projects_updated_at ON projects(updated_at);

-- Page indexes
CREATE INDEX IF NOT EXISTS idx_pages_project_id ON pages(project_id);
CREATE INDEX IF NOT EXISTS idx_pages_status ON pages(status);
CREATE INDEX IF NOT EXISTS idx_pages_priority ON pages(priority);
CREATE INDEX IF NOT EXISTS idx_pages_created_at ON pages(created_at);
CREATE INDEX IF NOT EXISTS idx_pages_updated_at ON pages(updated_at);
CREATE INDEX IF NOT EXISTS idx_pages_title ON pages(title);

-- Source indexes
CREATE INDEX IF NOT EXISTS idx_sources_project_id ON sources(project_id);
CREATE INDEX IF NOT EXISTS idx_sources_type ON sources(source_type);
CREATE INDEX IF NOT EXISTS idx_sources_status ON sources(status);
CREATE INDEX IF NOT EXISTS idx_sources_embedding_status ON sources(embedding_status);
CREATE INDEX IF NOT EXISTS idx_sources_created_at ON sources(created_at);
CREATE INDEX IF NOT EXISTS idx_sources_url ON sources(url);

-- Chat message indexes
CREATE INDEX IF NOT EXISTS idx_chat_messages_notebook_id ON chat_messages(notebook_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_role ON chat_messages(role);
CREATE INDEX IF NOT EXISTS idx_chat_messages_status ON chat_messages(status);
CREATE INDEX IF NOT EXISTS idx_chat_messages_created_at ON chat_messages(created_at);

-- Notebook indexes
CREATE INDEX IF NOT EXISTS idx_notebooks_project_id ON notebooks(project_id);
CREATE INDEX IF NOT EXISTS idx_notebooks_status ON notebooks(status);
CREATE INDEX IF NOT EXISTS idx_notebooks_created_at ON notebooks(created_at);

-- Search indexes
CREATE INDEX IF NOT EXISTS idx_saved_searches_project_id ON saved_searches(project_id);
CREATE INDEX IF NOT EXISTS idx_saved_searches_favorite ON saved_searches(is_favorite);
CREATE INDEX IF NOT EXISTS idx_saved_searches_usage ON saved_searches(usage_count);
CREATE INDEX IF NOT EXISTS idx_saved_searches_last_used ON saved_searches(last_used_at);

-- User pattern indexes
CREATE INDEX IF NOT EXISTS idx_user_patterns_type ON user_patterns(pattern_type);
CREATE INDEX IF NOT EXISTS idx_user_patterns_confidence ON user_patterns(confidence_score);
CREATE INDEX IF NOT EXISTS idx_user_patterns_usage ON user_patterns(usage_count);

-- Audit log indexes
CREATE INDEX IF NOT EXISTS idx_audit_log_table_record ON audit_log(table_name, record_id);
CREATE INDEX IF NOT EXISTS idx_audit_log_action ON audit_log(action);
CREATE INDEX IF NOT EXISTS idx_audit_log_timestamp ON audit_log(timestamp);
CREATE INDEX IF NOT EXISTS idx_audit_log_user_id ON audit_log(user_id);

-- ============================================================================
-- TRIGGERS FOR AUTOMATIC UPDATES
-- ============================================================================

-- Update timestamps automatically
CREATE TRIGGER IF NOT EXISTS update_projects_timestamp 
    AFTER UPDATE ON projects
    BEGIN
        UPDATE projects SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

CREATE TRIGGER IF NOT EXISTS update_pages_timestamp 
    AFTER UPDATE ON pages
    BEGIN
        UPDATE pages SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

CREATE TRIGGER IF NOT EXISTS update_sources_timestamp 
    AFTER UPDATE ON sources
    BEGIN
        UPDATE sources SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

CREATE TRIGGER IF NOT EXISTS update_notebooks_timestamp 
    AFTER UPDATE ON notebooks
    BEGIN
        UPDATE notebooks SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

CREATE TRIGGER IF NOT EXISTS update_chat_messages_timestamp 
    AFTER UPDATE ON chat_messages
    BEGIN
        UPDATE chat_messages SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

CREATE TRIGGER IF NOT EXISTS update_saved_searches_timestamp 
    AFTER UPDATE ON saved_searches
    BEGIN
        UPDATE saved_searches SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

-- Audit log triggers
CREATE TRIGGER IF NOT EXISTS audit_projects_insert 
    AFTER INSERT ON projects
    BEGIN
        INSERT INTO audit_log (id, table_name, record_id, action, new_values, timestamp)
        VALUES (hex(randomblob(16)), 'projects', NEW.id, 'create', 
                json_object('name', NEW.name, 'description', NEW.description, 'status', NEW.status), 
                CURRENT_TIMESTAMP);
    END;

CREATE TRIGGER IF NOT EXISTS audit_projects_update 
    AFTER UPDATE ON projects
    BEGIN
        INSERT INTO audit_log (id, table_name, record_id, action, old_values, new_values, timestamp)
        VALUES (hex(randomblob(16)), 'projects', NEW.id, 'update',
                json_object('name', OLD.name, 'description', OLD.description, 'status', OLD.status),
                json_object('name', NEW.name, 'description', NEW.description, 'status', NEW.status),
                CURRENT_TIMESTAMP);
    END;

CREATE TRIGGER IF NOT EXISTS audit_projects_delete 
    AFTER DELETE ON projects
    BEGIN
        INSERT INTO audit_log (id, table_name, record_id, action, old_values, timestamp)
        VALUES (hex(randomblob(16)), 'projects', OLD.id, 'delete',
                json_object('name', OLD.name, 'description', OLD.description, 'status', OLD.status),
                CURRENT_TIMESTAMP);
    END;

-- ============================================================================
-- INITIAL DATA
-- ============================================================================

-- Insert default supported formats
INSERT OR IGNORE INTO supported_formats (id, name, extension, mime_type, category, max_size_mb) VALUES
('txt', 'Plain Text', '.txt', 'text/plain', 'document', 10),
('md', 'Markdown', '.md', 'text/markdown', 'document', 10),
('pdf', 'PDF Document', '.pdf', 'application/pdf', 'document', 50),
('docx', 'Word Document', '.docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'document', 50),
('html', 'HTML Document', '.html', 'text/html', 'document', 10),
('json', 'JSON Data', '.json', 'application/json', 'data', 10),
('csv', 'CSV Data', '.csv', 'text/csv', 'data', 25),
('jpg', 'JPEG Image', '.jpg', 'image/jpeg', 'image', 25),
('png', 'PNG Image', '.png', 'image/png', 'image', 25),
('mp3', 'MP3 Audio', '.mp3', 'audio/mpeg', 'audio', 100),
('mp4', 'MP4 Video', '.mp4', 'video/mp4', 'video', 500);

-- Insert default system configuration
INSERT OR IGNORE INTO system_config (key, value, description) VALUES
('app_version', '1.0.0', 'Current application version'),
('db_version', '1.0.0', 'Current database schema version'),
('max_file_size_mb', '100', 'Maximum file size for uploads in MB'),
('embedding_model', 'text-embedding-ada-002', 'Default embedding model'),
('ai_model', 'gpt-4', 'Default AI model for chat'),
('cache_ttl_seconds', '3600', 'Default cache TTL in seconds'),
('search_results_limit', '50', 'Default limit for search results');

-- Insert default page templates
INSERT OR IGNORE INTO page_templates (id, name, description, template_content, is_system) VALUES
('blank', 'Blank Page', 'A completely blank page', '', TRUE),
('note', 'Simple Note', 'A basic note template', '# Note Title\n\n## Content\n\nWrite your note here...', TRUE),
('meeting', 'Meeting Notes', 'Template for meeting notes', '# Meeting Notes\n\n**Date:** \n**Attendees:** \n\n## Agenda\n\n## Discussion\n\n## Action Items\n\n## Next Steps\n', TRUE),
('project', 'Project Overview', 'Template for project documentation', '# Project Name\n\n## Overview\n\n## Goals\n\n## Timeline\n\n## Resources\n\n## Status\n', TRUE);

-- Insert default project templates
INSERT OR IGNORE INTO project_templates (id, name, description, is_system) VALUES
('personal', 'Personal Project', 'Template for personal projects', TRUE),
('work', 'Work Project', 'Template for work-related projects', TRUE),
('research', 'Research Project', 'Template for research and study projects', TRUE),
('creative', 'Creative Project', 'Template for creative and artistic projects', TRUE);