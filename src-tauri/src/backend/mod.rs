// Main Backend Module - Rust with Python Integration
use actix_web::{web, App, HttpServer, HttpResponse, Result, middleware};
use serde::{Deserialize, Serialize};
use tokio::sync::RwLock;
use std::sync::Arc;
use std::collections::HashMap;
use uuid::Uuid;
use chrono::{DateTime, Utc};
use rusqlite::{Connection, params};
use std::sync::Mutex;
use pyo3::prelude::*;
use pyo3::types::PyDict;

pub mod chat;
pub mod content;
pub mod data_processing;
pub mod file_manager;
pub mod knowledge_base;
pub mod python_bridge;

use chat::ChatService;
use content::ContentService;
use data_processing::DataProcessor;
use file_manager::FileManager;
use knowledge_base::KnowledgeBase;
use python_bridge::PythonBridge;

#[derive(Clone)]
pub struct AppState {
    pub db: Arc<Mutex<Connection>>,
    pub cache: Arc<RwLock<HashMap<String, Vec<u8>>>>, // In-memory cache instead of Redis
    pub chat_service: Arc<ChatService>,
    pub content_service: Arc<ContentService>,
    pub data_processor: Arc<DataProcessor>,
    pub file_manager: Arc<FileManager>,
    pub knowledge_base: Arc<RwLock<KnowledgeBase>>,
    pub python_bridge: Arc<PythonBridge>,
}

impl AppState {
    pub async fn new() -> Result<Self, Box<dyn std::error::Error>> {
        // SQLite database connection (embedded, no server needed)
        let app_dir = dirs::data_dir()
            .ok_or("Could not find data directory")?
            .join("sanity");
        std::fs::create_dir_all(&app_dir)?;
        
        let db_path = app_dir.join("data_center.db");
        let db = Connection::open(db_path)?;
        
        // Initialize tables
        db.execute_batch(include_str!("../database/schema.sql"))?;
        
        // In-memory cache instead of Redis
        let cache = Arc::new(RwLock::new(HashMap::new()));

        // Initialize services (using Arc<Mutex<Connection>> for db)
        let db = Arc::new(Mutex::new(db));
        let chat_service = Arc::new(ChatService::new());
        let content_service = Arc::new(ContentService::new());
        let data_processor = Arc::new(DataProcessor::new());
        let file_manager = Arc::new(FileManager::new("./uploads".to_string()));
        let knowledge_base = Arc::new(RwLock::new(KnowledgeBase::new()));
        let python_bridge = Arc::new(PythonBridge::new()?);

        Ok(Self {
            db,
            cache,
            chat_service,
            content_service,
            data_processor,
            file_manager,
            knowledge_base,
            python_bridge,
        })
    }
}

// Tauri Commands - Direct IPC without HTTP
#[tauri::command]
pub async fn process_chat_message(
    message: String,
    context: Option<Vec<String>>,
    state: tauri::State<'_, Arc<AppState>>,
) -> Result<ChatResponse, String> {
    state.chat_service
        .process_message(message, context)
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn save_content(
    content: ContentInput,
    state: tauri::State<'_, Arc<AppState>>,
) -> Result<Content, String> {
    state.content_service
        .save_content(content)
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn search_content(
    query: String,
    filters: Option<SearchFilters>,
    state: tauri::State<'_, Arc<AppState>>,
) -> Result<Vec<Content>, String> {
    state.content_service
        .search(query, filters)
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn upload_file(
    file_path: String,
    file_type: String,
    state: tauri::State<'_, Arc<AppState>>,
) -> Result<FileMetadata, String> {
    state.file_manager
        .upload_file(file_path, file_type)
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn process_data(
    data: serde_json::Value,
    operation: String,
    state: tauri::State<'_, Arc<AppState>>,
) -> Result<serde_json::Value, String> {
    state.data_processor
        .process(data, operation)
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn execute_python(
    code: String,
    context: HashMap<String, serde_json::Value>,
    state: tauri::State<'_, Arc<AppState>>,
) -> Result<PythonResult, String> {
    state.python_bridge
        .execute(code, context)
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn train_model(
    dataset: String,
    model_type: String,
    parameters: HashMap<String, serde_json::Value>,
    state: tauri::State<'_, Arc<AppState>>,
) -> Result<ModelInfo, String> {
    state.python_bridge
        .train_model(dataset, model_type, parameters)
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn run_inference(
    model_id: String,
    input_data: serde_json::Value,
    state: tauri::State<'_, Arc<AppState>>,
) -> Result<InferenceResult, String> {
    state.python_bridge
        .run_inference(model_id, input_data)
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn analyze_data(
    data: serde_json::Value,
    analysis_type: String,
    state: tauri::State<'_, Arc<AppState>>,
) -> Result<AnalysisResult, String> {
    match analysis_type.as_str() {
        "statistical" => {
            state.data_processor
                .statistical_analysis(data)
                .await
                .map_err(|e| e.to_string())
        },
        "quality" => {
            state.data_processor
                .quality_check(data)
                .await
                .map_err(|e| e.to_string())
        },
        "profiling" => {
            state.python_bridge
                .profile_data(data)
                .await
                .map_err(|e| e.to_string())
        },
        _ => Err("Unknown analysis type".to_string())
    }
}

#[tauri::command]
pub async fn get_system_metrics(
    state: tauri::State<'_, Arc<AppState>>,
) -> Result<SystemMetrics, String> {
    use sysinfo::System;
    
    let mut sys = System::new_all();
    sys.refresh_all();
    
    // Get CPU usage (average of all CPUs)
    let cpu_usage = sys.cpus().iter()
        .map(|cpu| cpu.cpu_usage())
        .sum::<f32>() / sys.cpus().len() as f32;
    
    Ok(SystemMetrics {
        cpu_usage,
        memory_used: sys.used_memory(),
        memory_total: sys.total_memory(),
        disk_usage: Vec::new(), // Simplified for now
        network: NetworkInfo {
            bytes_sent: 0,
            bytes_received: 0,
        },
    })
}

// Data Types
#[derive(Debug, Serialize, Deserialize)]
pub struct ChatResponse {
    pub message: String,
    pub context_used: Vec<String>,
    pub timestamp: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ContentInput {
    pub title: String,
    pub content: String,
    pub content_type: String,
    pub tags: Vec<String>,
    pub metadata: Option<serde_json::Value>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Content {
    pub id: String,
    pub title: String,
    pub content: String,
    pub content_type: String,
    pub tags: Vec<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub metadata: Option<serde_json::Value>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SearchFilters {
    pub content_type: Option<String>,
    pub tags: Option<Vec<String>>,
    pub date_from: Option<DateTime<Utc>>,
    pub date_to: Option<DateTime<Utc>>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct FileMetadata {
    pub id: String,
    pub filename: String,
    pub file_type: String,
    pub size: u64,
    pub path: String,
    pub uploaded_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PythonResult {
    pub output: serde_json::Value,
    pub logs: Vec<String>,
    pub execution_time: f64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ModelInfo {
    pub id: String,
    pub model_type: String,
    pub accuracy: f64,
    pub parameters: HashMap<String, serde_json::Value>,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct InferenceResult {
    pub predictions: serde_json::Value,
    pub confidence: Option<f64>,
    pub execution_time: f64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AnalysisResult {
    pub analysis_type: String,
    pub results: serde_json::Value,
    pub visualizations: Option<Vec<String>>,
    pub summary: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SystemMetrics {
    pub cpu_usage: f32,
    pub memory_used: u64,
    pub memory_total: u64,
    pub disk_usage: Vec<DiskInfo>,
    pub network: NetworkInfo,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct DiskInfo {
    pub name: String,
    pub available: u64,
    pub total: u64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct NetworkInfo {
    pub bytes_sent: u64,
    pub bytes_received: u64,
}

// Optional: HTTP server for external integrations
pub async fn run_http_server(state: Arc<AppState>) -> std::io::Result<()> {
    HttpServer::new(move || {
        App::new()
            .app_data(web::Data::new(state.clone()))
            .wrap(middleware::Logger::default())
            .wrap(middleware::Compress::default())
            .service(
                web::scope("/api")
                    .route("/health", web::get().to(health_check))
                    .route("/data/process", web::post().to(process_data_http))
                    .route("/python/execute", web::post().to(execute_python_http))
            )
    })
    .bind("127.0.0.1:8080")?
    .run()
    .await
}

async fn health_check() -> Result<HttpResponse> {
    Ok(HttpResponse::Ok().json(serde_json::json!({
        "status": "healthy",
        "timestamp": Utc::now()
    })))
}

async fn process_data_http(
    data: web::Json<serde_json::Value>,
    state: web::Data<Arc<AppState>>,
) -> Result<HttpResponse> {
    let result = state.data_processor
        .process(data.into_inner(), "default".to_string())
        .await
        .map_err(|e| actix_web::error::ErrorInternalServerError(e))?;
    
    Ok(HttpResponse::Ok().json(result))
}

async fn execute_python_http(
    req: web::Json<PythonRequest>,
    state: web::Data<Arc<AppState>>,
) -> Result<HttpResponse> {
    let result = state.python_bridge
        .execute(req.code.clone(), req.context.clone())
        .await
        .map_err(|e| actix_web::error::ErrorInternalServerError(e))?;
    
    Ok(HttpResponse::Ok().json(result))
}

#[derive(Debug, Deserialize)]
struct PythonRequest {
    code: String,
    context: HashMap<String, serde_json::Value>,
}