use aes_gcm::{
    aead::{<PERSON><PERSON>, <PERSON><PERSON>ni<PERSON>, OsRng},
    Aes256Gcm, Key, Nonce
};
use argon2::{
    password_hash::{PasswordHasher, SaltString},
    Argon2
};
use base64::{Engine as _, engine::general_purpose};
use rand::RngCore;
use sha2::{Sha256, Digest};
use anyhow::{Result, anyhow};

pub struct BudgetEncryption {
    cipher: Aes256Gcm,
}

impl BudgetEncryption {
    /// Create a new encryption instance with a derived key
    pub fn new(master_password: &str) -> Result<Self> {
        let key = Self::derive_key(master_password)?;
        let cipher = Aes256Gcm::new(Key::<Aes256Gcm>::from_slice(&key));
        
        Ok(Self { cipher })
    }

    /// Create with a specific key (for testing or key rotation)
    pub fn with_key(key: &[u8; 32]) -> Self {
        let cipher = Aes256Gcm::new(Key::<Aes256Gcm>::from_slice(key));
        Self { cipher }
    }

    /// Encrypt sensitive data using AES-256-GCM
    pub fn encrypt_sensitive_data(&self, data: &str) -> Result<String> {
        // Generate a random nonce
        let mut nonce_bytes = [0u8; 12];
        OsRng.fill_bytes(&mut nonce_bytes);
        let nonce = Nonce::from_slice(&nonce_bytes);

        // Encrypt the data
        let ciphertext = self.cipher
            .encrypt(nonce, data.as_bytes())
            .map_err(|e| anyhow!("Encryption failed: {}", e))?;

        // Combine nonce and ciphertext
        let mut combined = Vec::with_capacity(nonce_bytes.len() + ciphertext.len());
        combined.extend_from_slice(&nonce_bytes);
        combined.extend_from_slice(&ciphertext);

        // Encode as base64 for storage
        Ok(general_purpose::STANDARD.encode(combined))
    }

    /// Decrypt sensitive data
    pub fn decrypt_sensitive_data(&self, encrypted_data: &str) -> Result<String> {
        // Decode from base64
        let combined = general_purpose::STANDARD
            .decode(encrypted_data)
            .map_err(|e| anyhow!("Base64 decode failed: {}", e))?;

        // Split nonce and ciphertext
        if combined.len() < 12 {
            return Err(anyhow!("Invalid encrypted data: too short"));
        }

        let (nonce_bytes, ciphertext) = combined.split_at(12);
        let nonce = Nonce::from_slice(nonce_bytes);

        // Decrypt the data
        let plaintext = self.cipher
            .decrypt(nonce, ciphertext)
            .map_err(|e| anyhow!("Decryption failed: {}", e))?;

        String::from_utf8(plaintext)
            .map_err(|e| anyhow!("UTF-8 decode failed: {}", e))
    }

    /// Derive encryption key from password using Argon2
    fn derive_key(password: &str) -> Result<[u8; 32]> {
        let salt = SaltString::generate(&mut OsRng);
        let argon2 = Argon2::default();
        
        let mut key = [0u8; 32];
        argon2
            .hash_password_into(password.as_bytes(), salt.as_str().as_bytes(), &mut key)
            .map_err(|e| anyhow!("Key derivation failed: {}", e))?;
        
        Ok(key)
    }
}

/// Hash audit data for integrity verification using SHA-256
pub fn hash_audit_data(data: &str) -> String {
    let mut hasher = Sha256::new();
    hasher.update(data.as_bytes());
    let result = hasher.finalize();
    
    // Convert to hex string
    format!("{:x}", result)
}

/// Verify audit data integrity
pub fn verify_audit_hash(data: &str, expected_hash: &str) -> bool {
    let computed_hash = hash_audit_data(data);
    computed_hash == expected_hash
}

/// Encrypt field-level data (for specific sensitive fields)
pub fn encrypt_field(field_value: &str, field_name: &str, key: &[u8; 32]) -> Result<String> {
    // Add field name as additional authenticated data
    let mut nonce_bytes = [0u8; 12];
    OsRng.fill_bytes(&mut nonce_bytes);
    let nonce = Nonce::from_slice(&nonce_bytes);

    let cipher = Aes256Gcm::new(Key::<Aes256Gcm>::from_slice(key));
    
    // Create payload with field identifier
    let payload = format!("{}:{}", field_name, field_value);
    
    let ciphertext = cipher
        .encrypt(nonce, payload.as_bytes())
        .map_err(|e| anyhow!("Field encryption failed: {}", e))?;

    // Combine nonce and ciphertext
    let mut combined = Vec::with_capacity(nonce_bytes.len() + ciphertext.len());
    combined.extend_from_slice(&nonce_bytes);
    combined.extend_from_slice(&ciphertext);

    Ok(general_purpose::STANDARD.encode(combined))
}

/// Decrypt field-level data
pub fn decrypt_field(encrypted_value: &str, field_name: &str, key: &[u8; 32]) -> Result<String> {
    let combined = general_purpose::STANDARD
        .decode(encrypted_value)
        .map_err(|e| anyhow!("Base64 decode failed: {}", e))?;

    if combined.len() < 12 {
        return Err(anyhow!("Invalid encrypted field: too short"));
    }

    let (nonce_bytes, ciphertext) = combined.split_at(12);
    let nonce = Nonce::from_slice(nonce_bytes);

    let cipher = Aes256Gcm::new(Key::<Aes256Gcm>::from_slice(key));
    
    let plaintext = cipher
        .decrypt(nonce, ciphertext)
        .map_err(|e| anyhow!("Field decryption failed: {}", e))?;

    let payload = String::from_utf8(plaintext)
        .map_err(|e| anyhow!("UTF-8 decode failed: {}", e))?;

    // Verify field name matches
    if let Some((name, value)) = payload.split_once(':') {
        if name == field_name {
            Ok(value.to_string())
        } else {
            Err(anyhow!("Field name mismatch in decrypted data"))
        }
    } else {
        Err(anyhow!("Invalid decrypted field format"))
    }
}

/// Generate a secure random key
pub fn generate_key() -> [u8; 32] {
    let mut key = [0u8; 32];
    OsRng.fill_bytes(&mut key);
    key
}

/// Securely wipe sensitive data from memory
pub fn secure_wipe(data: &mut [u8]) {
    for byte in data.iter_mut() {
        *byte = 0;
    }
    // Force compiler not to optimize this away
    std::sync::atomic::compiler_fence(std::sync::atomic::Ordering::SeqCst);
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_encryption_decryption() {
        let encryption = BudgetEncryption::new("test_password").unwrap();
        let original = "sensitive budget data";
        
        let encrypted = encryption.encrypt_sensitive_data(original).unwrap();
        assert_ne!(encrypted, original);
        
        let decrypted = encryption.decrypt_sensitive_data(&encrypted).unwrap();
        assert_eq!(decrypted, original);
    }

    #[test]
    fn test_audit_hash() {
        let data = "audit log entry";
        let hash1 = hash_audit_data(data);
        let hash2 = hash_audit_data(data);
        
        assert_eq!(hash1, hash2);
        assert!(verify_audit_hash(data, &hash1));
        assert!(!verify_audit_hash("different data", &hash1));
    }

    #[test]
    fn test_field_encryption() {
        let key = generate_key();
        let field_name = "credit_card";
        let field_value = "4111-1111-1111-1111";
        
        let encrypted = encrypt_field(field_value, field_name, &key).unwrap();
        let decrypted = decrypt_field(&encrypted, field_name, &key).unwrap();
        
        assert_eq!(decrypted, field_value);
        
        // Test with wrong field name
        let result = decrypt_field(&encrypted, "wrong_field", &key);
        assert!(result.is_err());
    }
}