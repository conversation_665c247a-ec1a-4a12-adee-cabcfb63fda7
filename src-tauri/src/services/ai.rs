//! AI service with caching and improved error handling

use std::sync::Arc;
use std::collections::HashMap;
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

use crate::models::{
    common::{BeaconResult, BeaconError},
    lighthouse::{ChatMessage, AIContext, AIPreferences, MessageRole, EmbeddingVector, SearchResult},
};
use super::{DatabaseService, CacheService};

/// AI service configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AIServiceConfig {
    pub default_model: String,
    pub embedding_model: String,
    pub max_context_length: u32,
    pub default_temperature: f32,
    pub cache_ttl_seconds: u64,
    pub retry_attempts: u32,
    pub timeout_seconds: u64,
}

impl Default for AIServiceConfig {
    fn default() -> Self {
        Self {
            default_model: "claude-3-sonnet".to_string(),
            embedding_model: "text-embedding-ada-002".to_string(),
            max_context_length: 100000,
            default_temperature: 0.7,
            cache_ttl_seconds: 3600, // 1 hour
            retry_attempts: 3,
            timeout_seconds: 30,
        }
    }
}

/// AI service with caching and error recovery
pub struct AIService {
    config: AIServiceConfig,
    database: Arc<DatabaseService>,
    cache: Arc<CacheService>,
    active_contexts: Arc<RwLock<HashMap<String, AIContext>>>,
    embedding_cache: Arc<RwLock<HashMap<String, Vec<f32>>>>,
}

impl AIService {
    /// Create new AI service
    pub async fn new(
        database: Arc<DatabaseService>,
        cache: Arc<CacheService>,
    ) -> BeaconResult<Self> {
        let config = AIServiceConfig::default();
        Self::with_config(config, database, cache).await
    }

    /// Create AI service with custom configuration
    pub async fn with_config(
        config: AIServiceConfig,
        database: Arc<DatabaseService>,
        cache: Arc<CacheService>,
    ) -> BeaconResult<Self> {
        Ok(Self {
            config,
            database,
            cache,
            active_contexts: Arc::new(RwLock::new(HashMap::new())),
            embedding_cache: Arc::new(RwLock::new(HashMap::new())),
        })
    }

    /// Health check for AI service
    pub async fn health_check(&self) -> BeaconResult<()> {
        // Test basic AI functionality
        let test_message = "Hello, this is a health check.";
        let _response = self.generate_response_internal(test_message, &AIPreferences::default()).await?;
        Ok(())
    }

    /// Generate AI response with caching
    pub async fn generate_response(
        &self,
        context_id: &str,
        message: &str,
        preferences: Option<AIPreferences>,
    ) -> BeaconResult<AIResponse> {
        let prefs = preferences.unwrap_or_default();
        
        // Check cache first
        let cache_key = format!("ai_response:{}:{}", context_id, self.hash_message(message));
        if let Some(cached_response) = self.cache.get::<AIResponse>(&cache_key).await? {
            return Ok(cached_response);
        }

        // Get or create context
        let context = self.get_or_create_context(context_id).await?;
        
        // Generate response with retry logic
        let response = self.generate_with_retry(message, &prefs, &context).await?;
        
        // Cache the response
        self.cache.set(&cache_key, &response, Some(self.config.cache_ttl_seconds)).await?;
        
        // Update context
        self.update_context(context_id, message, &response.content).await?;
        
        Ok(response)
    }

    /// Generate embeddings with caching
    pub async fn generate_embeddings(&self, text: &str) -> BeaconResult<Vec<f32>> {
        let cache_key = format!("embedding:{}", self.hash_message(text));
        
        // Check cache first
        {
            let cache = self.embedding_cache.read().await;
            if let Some(cached_embedding) = cache.get(&cache_key) {
                return Ok(cached_embedding.clone());
            }
        }

        // Generate embedding
        let embedding = self.generate_embedding_internal(text).await?;
        
        // Cache the embedding
        {
            let mut cache = self.embedding_cache.write().await;
            cache.insert(cache_key, embedding.clone());
        }
        
        Ok(embedding)
    }

    /// Batch generate embeddings
    pub async fn batch_generate_embeddings(&self, texts: Vec<String>) -> BeaconResult<Vec<EmbeddingVector>> {
        let mut results = Vec::new();
        
        for (index, text) in texts.iter().enumerate() {
            let embedding = self.generate_embeddings(text).await?;
            
            results.push(EmbeddingVector {
                id: uuid::Uuid::new_v4().to_string(),
                source_id: format!("batch_{}", index),
                chunk_index: index as u32,
                content: text.clone(),
                vector: embedding,
                model: self.config.embedding_model.clone(),
                dimensions: 1536, // Default for OpenAI embeddings
                timestamps: crate::models::common::Timestamps::new(),
            });
        }
        
        Ok(results)
    }

    /// Semantic search using embeddings
    pub async fn semantic_search(
        &self,
        query: &str,
        limit: Option<u32>,
        threshold: Option<f32>,
    ) -> BeaconResult<Vec<SearchResult>> {
        let query_embedding = self.generate_embeddings(query).await?;
        let limit = limit.unwrap_or(10);
        let threshold = threshold.unwrap_or(0.7);
        
        // This would typically query a vector database
        // For now, we'll return a placeholder implementation
        let results = self.search_similar_embeddings(&query_embedding, limit, threshold).await?;
        
        Ok(results)
    }

    /// Process and chunk large documents
    pub async fn process_document(
        &self,
        content: &str,
        chunk_size: Option<usize>,
        overlap: Option<usize>,
    ) -> BeaconResult<Vec<DocumentChunk>> {
        let chunk_size = chunk_size.unwrap_or(1000);
        let overlap = overlap.unwrap_or(100);
        
        let chunks = self.chunk_text(content, chunk_size, overlap);
        let mut processed_chunks = Vec::new();
        
        for (index, chunk) in chunks.iter().enumerate() {
            let embedding = self.generate_embeddings(chunk).await?;
            
            processed_chunks.push(DocumentChunk {
                id: uuid::Uuid::new_v4().to_string(),
                index: index as u32,
                content: chunk.clone(),
                embedding,
                word_count: chunk.split_whitespace().count() as u32,
                character_count: chunk.len() as u32,
            });
        }
        
        Ok(processed_chunks)
    }

    /// Get AI service statistics
    pub async fn get_statistics(&self) -> AIServiceStats {
        let contexts_count = self.active_contexts.read().await.len();
        let embeddings_cached = self.embedding_cache.read().await.len();
        
        AIServiceStats {
            active_contexts: contexts_count as u32,
            cached_embeddings: embeddings_cached as u32,
            cache_hit_rate: self.cache.get_hit_rate().await.unwrap_or(0.0),
            total_requests: 0, // Would be tracked in a real implementation
            average_response_time_ms: 0, // Would be tracked in a real implementation
        }
    }

    // Private helper methods
    async fn get_or_create_context(&self, context_id: &str) -> BeaconResult<AIContext> {
        let mut contexts = self.active_contexts.write().await;
        
        if let Some(context) = contexts.get(context_id) {
            Ok(context.clone())
        } else {
            let new_context = AIContext::new(
                context_id.to_string(),
                "default_user".to_string(), // Would come from authentication
            );
            contexts.insert(context_id.to_string(), new_context.clone());
            Ok(new_context)
        }
    }

    async fn update_context(
        &self,
        context_id: &str,
        user_message: &str,
        ai_response: &str,
    ) -> BeaconResult<()> {
        let mut contexts = self.active_contexts.write().await;
        
        if let Some(context) = contexts.get_mut(context_id) {
            // Add messages to conversation history
            let user_msg_id = uuid::Uuid::new_v4().to_string();
            let ai_msg_id = uuid::Uuid::new_v4().to_string();
            
            context.add_message(user_msg_id);
            context.add_message(ai_msg_id);
            
            // Trim context if it gets too long
            if context.conversation_history.len() > self.config.max_context_length as usize {
                let trim_count = context.conversation_history.len() - self.config.max_context_length as usize;
                context.conversation_history.drain(0..trim_count);
            }
        }
        
        Ok(())
    }

    async fn generate_with_retry(
        &self,
        message: &str,
        preferences: &AIPreferences,
        _context: &AIContext,
    ) -> BeaconResult<AIResponse> {
        let mut last_error = None;
        
        for attempt in 0..self.config.retry_attempts {
            match self.generate_response_internal(message, preferences).await {
                Ok(response) => return Ok(response),
                Err(e) => {
                    last_error = Some(e);
                    if attempt < self.config.retry_attempts - 1 {
                        // Exponential backoff
                        let delay = std::time::Duration::from_millis(100 * (2_u64.pow(attempt)));
                        tokio::time::sleep(delay).await;
                    }
                }
            }
        }
        
        Err(last_error.unwrap_or_else(|| {
            BeaconError::AIServiceError("All retry attempts failed".to_string())
        }))
    }

    async fn generate_response_internal(
        &self,
        message: &str,
        preferences: &AIPreferences,
    ) -> BeaconResult<AIResponse> {
        // This would integrate with actual AI service (Claude, OpenAI, etc.)
        // For now, return a mock response
        Ok(AIResponse {
            content: format!("AI response to: {}", message),
            model: preferences.model.clone(),
            tokens_used: message.len() as u32,
            processing_time_ms: 500,
            confidence_score: 0.95,
            citations: Vec::new(),
            metadata: HashMap::new(),
        })
    }

    async fn generate_embedding_internal(&self, text: &str) -> BeaconResult<Vec<f32>> {
        // This would integrate with actual embedding service
        // For now, return a mock embedding
        let mut embedding = vec![0.0; 1536];
        for (i, byte) in text.bytes().enumerate() {
            if i < 1536 {
                embedding[i] = (byte as f32) / 255.0;
            }
        }
        Ok(embedding)
    }

    async fn search_similar_embeddings(
        &self,
        _query_embedding: &[f32],
        limit: u32,
        _threshold: f32,
    ) -> BeaconResult<Vec<SearchResult>> {
        // This would query a vector database
        // For now, return mock results
        let mut results = Vec::new();
        for i in 0..limit {
            results.push(SearchResult {
                source_id: format!("source_{}", i),
                title: format!("Result {}", i),
                excerpt: format!("This is excerpt {}", i),
                relevance_score: 0.9 - (i as f32 * 0.1),
                source_type: "document".to_string(),
                metadata: HashMap::new(),
                highlights: vec![format!("highlight {}", i)],
            });
        }
        Ok(results)
    }

    fn chunk_text(&self, text: &str, chunk_size: usize, overlap: usize) -> Vec<String> {
        let mut chunks = Vec::new();
        let words: Vec<&str> = text.split_whitespace().collect();
        
        let mut start = 0;
        while start < words.len() {
            let end = std::cmp::min(start + chunk_size, words.len());
            let chunk = words[start..end].join(" ");
            chunks.push(chunk);
            
            if end >= words.len() {
                break;
            }
            
            start = end - overlap;
        }
        
        chunks
    }

    fn hash_message(&self, message: &str) -> String {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};
        
        let mut hasher = DefaultHasher::new();
        message.hash(&mut hasher);
        format!("{:x}", hasher.finish())
    }
}

/// AI response structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AIResponse {
    pub content: String,
    pub model: String,
    pub tokens_used: u32,
    pub processing_time_ms: u64,
    pub confidence_score: f32,
    pub citations: Vec<String>,
    pub metadata: HashMap<String, serde_json::Value>,
}

/// Document chunk for processing
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DocumentChunk {
    pub id: String,
    pub index: u32,
    pub content: String,
    pub embedding: Vec<f32>,
    pub word_count: u32,
    pub character_count: u32,
}

/// AI service statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AIServiceStats {
    pub active_contexts: u32,
    pub cached_embeddings: u32,
    pub cache_hit_rate: f32,
    pub total_requests: u64,
    pub average_response_time_ms: u64,
}