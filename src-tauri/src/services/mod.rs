//! Service layer for the Beacon backend
//! 
//! This module provides a centralized service layer that manages all backend services
//! including database operations, AI services, search functionality, caching, and validation.

use std::sync::Arc;
use serde::{Deserialize, Serialize};
use tokio::sync::RwLock;

use crate::models::common::{BeaconResult, BeaconError};

pub mod database;
pub mod ai;
pub mod search;
pub mod cache;
pub mod validation;

use database::{DatabaseService, DatabaseServiceConfig};
use ai::{AIService, AIServiceConfig};
use search::{SearchService, SearchServiceConfig};
use cache::{CacheService, CacheServiceConfig};
use validation::{ValidationService, ValidationServiceConfig};

/// Service container configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceContainerConfig {
    pub database: DatabaseServiceConfig,
    pub ai: AIServiceConfig,
    pub search: SearchServiceConfig,
    pub cache: CacheServiceConfig,
    pub validation: ValidationServiceConfig,
}

impl Default for ServiceContainerConfig {
    fn default() -> Self {
        Self {
            database: DatabaseServiceConfig::default(),
            ai: AIServiceConfig::default(),
            search: SearchServiceConfig::default(),
            cache: CacheServiceConfig::default(),
            validation: ValidationServiceConfig::default(),
        }
    }
}

/// Service container that manages all backend services
pub struct ServiceContainer {
    pub database: Arc<DatabaseService>,
    pub ai: Arc<AIService>,
    pub search: Arc<SearchService>,
    pub cache: Arc<CacheService>,
    pub validation: Arc<ValidationService>,
    config: ServiceContainerConfig,
    initialized: Arc<RwLock<bool>>,
}

impl ServiceContainer {
    /// Initialize all services with default configuration
    pub async fn new() -> BeaconResult<Self> {
        let config = ServiceContainerConfig::default();
        Self::with_config(config).await
    }
    
    /// Initialize services with custom configuration
    pub async fn with_config(config: ServiceContainerConfig) -> BeaconResult<Self> {
        // Initialize cache service first (other services depend on it)
        let cache = Arc::new(CacheService::with_config(config.cache.clone()).await?);
        
        // Initialize validation service (no async dependencies)
        let validation = Arc::new(ValidationService::with_config(config.validation.clone())?);
        
        // Initialize database service
        let database = Arc::new(DatabaseService::with_config(config.database.clone()).await?);
        
        // Initialize AI service
        let ai = Arc::new(AIService::with_config(
            config.ai.clone(),
            database.clone(),
            cache.clone(),
        ).await?);
        
        // Initialize search service
        let search = Arc::new(SearchService::with_config(
            config.search.clone(),
            database.clone(),
            ai.clone(),
            cache.clone(),
        ).await?);
        
        let container = Self {
            database,
            ai,
            search,
            cache,
            validation,
            config,
            initialized: Arc::new(RwLock::new(true)),
        };
        
        // Perform initial health check
        let health_status = container.health_check().await?;
        if !health_status.overall_healthy {
            return Err(BeaconError::ServiceError(
                "One or more services failed health check during initialization".to_string()
            ));
        }
        
        Ok(container)
    }
    
    /// Initialize services with custom database path
    pub async fn with_database_path(db_path: &str) -> BeaconResult<Self> {
        let mut config = ServiceContainerConfig::default();
        config.database.database_url = format!("sqlite:{}", db_path);
        Self::with_config(config).await
    }
    
    /// Check if services are initialized
    pub async fn is_initialized(&self) -> bool {
        *self.initialized.read().await
    }
    
    /// Reinitialize services (useful for configuration changes)
    pub async fn reinitialize(&self) -> BeaconResult<()> {
        let mut initialized = self.initialized.write().await;
        *initialized = false;
        
        // Perform reinitialization logic here if needed
        // For now, we'll just mark as initialized again
        
        *initialized = true;
        Ok(())
    }
    
    /// Perform health checks on all services
    pub async fn health_check(&self) -> BeaconResult<ServiceHealthStatus> {
        let mut status = ServiceHealthStatus::new();
        
        // Check database service
        match self.database.health_check().await {
            Ok(_) => status.database = ServiceStatus::Healthy,
            Err(e) => {
                status.database = ServiceStatus::Unhealthy(e.to_string());
                status.overall_healthy = false;
            }
        }
        
        // Check AI service
        match self.ai.health_check().await {
            Ok(_) => status.ai = ServiceStatus::Healthy,
            Err(e) => {
                status.ai = ServiceStatus::Unhealthy(e.to_string());
                status.overall_healthy = false;
            }
        }
        
        // Check search service
        match self.search.health_check().await {
            Ok(_) => status.search = ServiceStatus::Healthy,
            Err(e) => {
                status.search = ServiceStatus::Unhealthy(e.to_string());
                status.overall_healthy = false;
            }
        }
        
        // Check cache service
        match self.cache.health_check().await {
            Ok(_) => status.cache = ServiceStatus::Healthy,
            Err(e) => {
                status.cache = ServiceStatus::Unhealthy(e.to_string());
                status.overall_healthy = false;
            }
        }
        
        // Check validation service
        match self.validation.health_check() {
            Ok(_) => status.validation = ServiceStatus::Healthy,
            Err(e) => {
                status.validation = ServiceStatus::Unhealthy(e.to_string());
                status.overall_healthy = false;
            }
        }
        
        Ok(status)
    }
    
    /// Get service statistics
    pub async fn get_statistics(&self) -> ServiceStatistics {
        ServiceStatistics {
            database: self.database.get_statistics().await,
            ai: self.ai.get_statistics().await,
            search: self.search.get_statistics().await,
            cache: self.cache.get_statistics().await,
            validation: self.validation.get_validation_stats(),
        }
    }
    
    /// Shutdown all services gracefully
    pub async fn shutdown(&self) -> BeaconResult<()> {
        let mut initialized = self.initialized.write().await;
        *initialized = false;
        
        // Services will be dropped automatically when the Arc references are dropped
        // Any cleanup logic would go here
        Ok(())
    }
}

/// Service health status
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceHealthStatus {
    pub overall_healthy: bool,
    pub database: ServiceStatus,
    pub ai: ServiceStatus,
    pub search: ServiceStatus,
    pub cache: ServiceStatus,
    pub validation: ServiceStatus,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

impl ServiceHealthStatus {
    fn new() -> Self {
        Self {
            overall_healthy: true,
            database: ServiceStatus::Unknown,
            ai: ServiceStatus::Unknown,
            search: ServiceStatus::Unknown,
            cache: ServiceStatus::Unknown,
            validation: ServiceStatus::Unknown,
            timestamp: chrono::Utc::now(),
        }
    }
}

/// Individual service status
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ServiceStatus {
    Healthy,
    Unhealthy(String),
    Unknown,
}

/// Combined service statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceStatistics {
    pub database: database::DatabaseStats,
    pub ai: ai::AIServiceStats,
    pub search: search::SearchServiceStats,
    pub cache: cache::CacheStats,
    pub validation: validation::ValidationStats,
}