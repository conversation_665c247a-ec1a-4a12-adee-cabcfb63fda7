//! Cache service with TTL support and statistics

use std::sync::Arc;
use std::collections::HashMap;
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize, de::DeserializeOwned};
use chrono::{DateTime, Utc, Duration};

use crate::models::common::{BeaconResult, BeaconError};

/// Cache service configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheServiceConfig {
    pub max_entries: usize,
    pub default_ttl_seconds: u64,
    pub cleanup_interval_seconds: u64,
    pub enable_statistics: bool,
}

impl Default for CacheServiceConfig {
    fn default() -> Self {
        Self {
            max_entries: 10000,
            default_ttl_seconds: 3600, // 1 hour
            cleanup_interval_seconds: 300, // 5 minutes
            enable_statistics: true,
        }
    }
}

/// Cache entry with TTL
#[derive(Debug, <PERSON>lone)]
struct CacheEntry {
    data: Vec<u8>, // Serialized data
    expires_at: DateTime<Utc>,
    created_at: DateTime<Utc>,
    access_count: u64,
    last_accessed: DateTime<Utc>,
}

impl CacheEntry {
    fn new(data: Vec<u8>, ttl_seconds: Option<u64>, default_ttl: u64) -> Self {
        let now = Utc::now();
        let ttl = ttl_seconds.unwrap_or(default_ttl);
        
        Self {
            data,
            expires_at: now + Duration::seconds(ttl as i64),
            created_at: now,
            access_count: 0,
            last_accessed: now,
        }
    }
    
    fn is_expired(&self) -> bool {
        Utc::now() > self.expires_at
    }
    
    fn access(&mut self) {
        self.access_count += 1;
        self.last_accessed = Utc::now();
    }
}

/// Cache statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheStats {
    pub total_entries: usize,
    pub total_hits: u64,
    pub total_misses: u64,
    pub hit_rate: f32,
    pub memory_usage_bytes: usize,
    pub expired_entries: usize,
    pub average_access_count: f32,
}

/// Cache service with TTL and LRU eviction
pub struct CacheService {
    config: CacheServiceConfig,
    cache: Arc<RwLock<HashMap<String, CacheEntry>>>,
    stats: Arc<RwLock<CacheServiceStats>>,
    cleanup_handle: Option<tokio::task::JoinHandle<()>>,
}

#[derive(Debug, Default)]
struct CacheServiceStats {
    hits: u64,
    misses: u64,
    evictions: u64,
    cleanups: u64,
}

impl CacheService {
    /// Create new cache service
    pub async fn new() -> BeaconResult<Self> {
        let config = CacheServiceConfig::default();
        Self::with_config(config).await
    }

    /// Create cache service with custom configuration
    pub async fn with_config(config: CacheServiceConfig) -> BeaconResult<Self> {
        let cache = Arc::new(RwLock::new(HashMap::new()));
        let stats = Arc::new(RwLock::new(CacheServiceStats::default()));
        
        let mut service = Self {
            config: config.clone(),
            cache: cache.clone(),
            stats: stats.clone(),
            cleanup_handle: None,
        };
        
        // Start cleanup task
        if config.cleanup_interval_seconds > 0 {
            let cleanup_cache = cache.clone();
            let cleanup_stats = stats.clone();
            let cleanup_interval = config.cleanup_interval_seconds;
            
            let handle = tokio::spawn(async move {
                let mut interval = tokio::time::interval(
                    std::time::Duration::from_secs(cleanup_interval)
                );
                
                loop {
                    interval.tick().await;
                    Self::cleanup_expired_entries(cleanup_cache.clone(), cleanup_stats.clone()).await;
                }
            });
            
            service.cleanup_handle = Some(handle);
        }
        
        Ok(service)
    }

    /// Health check for cache service
    pub async fn health_check(&self) -> BeaconResult<()> {
        let cache_size = self.cache.read().await.len();
        if cache_size > self.config.max_entries {
            return Err(BeaconError::CacheError(
                format!("Cache size {} exceeds maximum {}", cache_size, self.config.max_entries)
            ));
        }
        Ok(())
    }

    /// Get value from cache
    pub async fn get<T>(&self, key: &str) -> BeaconResult<Option<T>>
    where
        T: DeserializeOwned,
    {
        let mut cache = self.cache.write().await;
        
        if let Some(entry) = cache.get_mut(key) {
            if entry.is_expired() {
                cache.remove(key);
                self.increment_misses().await;
                return Ok(None);
            }
            
            entry.access();
            self.increment_hits().await;
            
            let deserialized: T = serde_json::from_slice(&entry.data)
                .map_err(|e| BeaconError::CacheError(format!("Deserialization error: {}", e)))?;
            
            Ok(Some(deserialized))
        } else {
            self.increment_misses().await;
            Ok(None)
        }
    }

    /// Set value in cache
    pub async fn set<T>(&self, key: &str, value: &T, ttl_seconds: Option<u64>) -> BeaconResult<()>
    where
        T: Serialize,
    {
        let serialized = serde_json::to_vec(value)
            .map_err(|e| BeaconError::CacheError(format!("Serialization error: {}", e)))?;
        
        let entry = CacheEntry::new(serialized, ttl_seconds, self.config.default_ttl_seconds);
        
        let mut cache = self.cache.write().await;
        
        // Check if we need to evict entries
        if cache.len() >= self.config.max_entries {
            self.evict_lru_entries(&mut cache, 1).await;
        }
        
        cache.insert(key.to_string(), entry);
        Ok(())
    }

    /// Remove value from cache
    pub async fn remove(&self, key: &str) -> BeaconResult<bool> {
        let mut cache = self.cache.write().await;
        Ok(cache.remove(key).is_some())
    }

    /// Check if key exists in cache
    pub async fn exists(&self, key: &str) -> BeaconResult<bool> {
        let cache = self.cache.read().await;
        
        if let Some(entry) = cache.get(key) {
            Ok(!entry.is_expired())
        } else {
            Ok(false)
        }
    }

    /// Clear all cache entries
    pub async fn clear(&self) -> BeaconResult<()> {
        let mut cache = self.cache.write().await;
        cache.clear();
        
        // Reset stats
        let mut stats = self.stats.write().await;
        *stats = CacheServiceStats::default();
        
        Ok(())
    }

    /// Get cache statistics
    pub async fn get_statistics(&self) -> CacheStats {
        let cache = self.cache.read().await;
        let stats = self.stats.read().await;
        
        let total_requests = stats.hits + stats.misses;
        let hit_rate = if total_requests > 0 {
            stats.hits as f32 / total_requests as f32
        } else {
            0.0
        };
        
        let memory_usage = cache.values()
            .map(|entry| entry.data.len())
            .sum::<usize>();
        
        let expired_count = cache.values()
            .filter(|entry| entry.is_expired())
            .count();
        
        let total_access_count: u64 = cache.values()
            .map(|entry| entry.access_count)
            .sum();
        
        let average_access_count = if !cache.is_empty() {
            total_access_count as f32 / cache.len() as f32
        } else {
            0.0
        };
        
        CacheStats {
            total_entries: cache.len(),
            total_hits: stats.hits,
            total_misses: stats.misses,
            hit_rate,
            memory_usage_bytes: memory_usage,
            expired_entries: expired_count,
            average_access_count,
        }
    }

    /// Get hit rate
    pub async fn get_hit_rate(&self) -> BeaconResult<f32> {
        let stats = self.stats.read().await;
        let total_requests = stats.hits + stats.misses;
        
        if total_requests > 0 {
            Ok(stats.hits as f32 / total_requests as f32)
        } else {
            Ok(0.0)
        }
    }

    /// Get keys matching a pattern
    pub async fn get_keys(&self, pattern: Option<&str>) -> BeaconResult<Vec<String>> {
        let cache = self.cache.read().await;
        
        let keys: Vec<String> = if let Some(pattern) = pattern {
            cache.keys()
                .filter(|key| key.contains(pattern))
                .cloned()
                .collect()
        } else {
            cache.keys().cloned().collect()
        };
        
        Ok(keys)
    }

    /// Set multiple values at once
    pub async fn set_many<T>(&self, entries: HashMap<String, T>, ttl_seconds: Option<u64>) -> BeaconResult<()>
    where
        T: Serialize,
    {
        for (key, value) in entries {
            self.set(&key, &value, ttl_seconds).await?;
        }
        Ok(())
    }

    /// Get multiple values at once
    pub async fn get_many<T>(&self, keys: &[String]) -> BeaconResult<HashMap<String, T>>
    where
        T: DeserializeOwned,
    {
        let mut results = HashMap::new();
        
        for key in keys {
            if let Some(value) = self.get::<T>(key).await? {
                results.insert(key.clone(), value);
            }
        }
        
        Ok(results)
    }

    /// Extend TTL for a key
    pub async fn extend_ttl(&self, key: &str, additional_seconds: u64) -> BeaconResult<bool> {
        let mut cache = self.cache.write().await;
        
        if let Some(entry) = cache.get_mut(key) {
            if !entry.is_expired() {
                entry.expires_at = entry.expires_at + Duration::seconds(additional_seconds as i64);
                return Ok(true);
            }
        }
        
        Ok(false)
    }

    /// Get TTL for a key
    pub async fn get_ttl(&self, key: &str) -> BeaconResult<Option<i64>> {
        let cache = self.cache.read().await;
        
        if let Some(entry) = cache.get(key) {
            if !entry.is_expired() {
                let remaining = entry.expires_at - Utc::now();
                return Ok(Some(remaining.num_seconds()));
            }
        }
        
        Ok(None)
    }

    // Private helper methods
    async fn increment_hits(&self) {
        if self.config.enable_statistics {
            let mut stats = self.stats.write().await;
            stats.hits += 1;
        }
    }

    async fn increment_misses(&self) {
        if self.config.enable_statistics {
            let mut stats = self.stats.write().await;
            stats.misses += 1;
        }
    }

    async fn evict_lru_entries(&self, cache: &mut HashMap<String, CacheEntry>, count: usize) {
        if cache.is_empty() {
            return;
        }
        
        // Find LRU entries
        let mut entries: Vec<(String, DateTime<Utc>)> = cache.iter()
            .map(|(key, entry)| (key.clone(), entry.last_accessed))
            .collect();
        
        entries.sort_by(|a, b| a.1.cmp(&b.1));
        
        // Remove the oldest entries
        for (key, _) in entries.into_iter().take(count) {
            cache.remove(&key);
        }
        
        // Update eviction stats
        if self.config.enable_statistics {
            let mut stats = self.stats.write().await;
            stats.evictions += count as u64;
        }
    }

    async fn cleanup_expired_entries(
        cache: Arc<RwLock<HashMap<String, CacheEntry>>>,
        stats: Arc<RwLock<CacheServiceStats>>,
    ) {
        let mut cache = cache.write().await;
        let expired_keys: Vec<String> = cache.iter()
            .filter(|(_, entry)| entry.is_expired())
            .map(|(key, _)| key.clone())
            .collect();
        
        for key in expired_keys {
            cache.remove(&key);
        }
        
        // Update cleanup stats
        let mut stats = stats.write().await;
        stats.cleanups += 1;
    }
}

impl Drop for CacheService {
    fn drop(&mut self) {
        if let Some(handle) = self.cleanup_handle.take() {
            handle.abort();
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio::time::{sleep, Duration};

    #[tokio::test]
    async fn test_cache_basic_operations() {
        let cache = CacheService::new().await.unwrap();
        
        // Test set and get
        cache.set("key1", &"value1", None).await.unwrap();
        let result: Option<String> = cache.get("key1").await.unwrap();
        assert_eq!(result, Some("value1".to_string()));
        
        // Test non-existent key
        let result: Option<String> = cache.get("nonexistent").await.unwrap();
        assert_eq!(result, None);
        
        // Test remove
        let removed = cache.remove("key1").await.unwrap();
        assert!(removed);
        
        let result: Option<String> = cache.get("key1").await.unwrap();
        assert_eq!(result, None);
    }

    #[tokio::test]
    async fn test_cache_ttl() {
        let cache = CacheService::new().await.unwrap();
        
        // Set with short TTL
        cache.set("key1", &"value1", Some(1)).await.unwrap();
        
        // Should exist immediately
        let exists = cache.exists("key1").await.unwrap();
        assert!(exists);
        
        // Wait for expiration
        sleep(Duration::from_secs(2)).await;
        
        // Should be expired
        let exists = cache.exists("key1").await.unwrap();
        assert!(!exists);
    }

    #[tokio::test]
    async fn test_cache_statistics() {
        let cache = CacheService::new().await.unwrap();
        
        // Generate some cache activity
        cache.set("key1", &"value1", None).await.unwrap();
        let _: Option<String> = cache.get("key1").await.unwrap(); // Hit
        let _: Option<String> = cache.get("nonexistent").await.unwrap(); // Miss
        
        let stats = cache.get_statistics().await;
        assert_eq!(stats.total_hits, 1);
        assert_eq!(stats.total_misses, 1);
        assert_eq!(stats.hit_rate, 0.5);
        assert_eq!(stats.total_entries, 1);
    }
}