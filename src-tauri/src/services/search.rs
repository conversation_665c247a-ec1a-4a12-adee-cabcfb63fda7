//! Search service with semantic search and indexing capabilities

use std::sync::Arc;
use std::collections::HashMap;
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

use crate::models::{
    common::{BeaconResult, BeaconError, PaginationParams, PaginatedResponse, SortOrder},
    beacon::{Source, SavedSearch, SearchFilters, DateRange},
    lighthouse::{SearchResult, EmbeddingVector},
};
use super::{DatabaseService, AIService, CacheService};

/// Search service configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchServiceConfig {
    pub index_batch_size: u32,
    pub max_results: u32,
    pub default_similarity_threshold: f32,
    pub cache_ttl_seconds: u64,
    pub enable_fuzzy_search: bool,
    pub enable_semantic_search: bool,
}

impl Default for SearchServiceConfig {
    fn default() -> Self {
        Self {
            index_batch_size: 100,
            max_results: 50,
            default_similarity_threshold: 0.7,
            cache_ttl_seconds: 1800, // 30 minutes
            enable_fuzzy_search: true,
            enable_semantic_search: true,
        }
    }
}

/// Search index entry
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchIndexEntry {
    pub id: String,
    pub source_id: String,
    pub title: String,
    pub content: String,
    pub keywords: Vec<String>,
    pub embedding: Option<Vec<f32>>,
    pub indexed_at: DateTime<Utc>,
    pub metadata: HashMap<String, serde_json::Value>,
}

/// Search service with full-text and semantic search
pub struct SearchService {
    config: SearchServiceConfig,
    database: Arc<DatabaseService>,
    ai_service: Arc<AIService>,
    cache: Arc<CacheService>,
    search_index: Arc<RwLock<HashMap<String, SearchIndexEntry>>>,
    keyword_index: Arc<RwLock<HashMap<String, Vec<String>>>>, // keyword -> document IDs
}

impl SearchService {
    /// Create new search service
    pub async fn new(
        database: Arc<DatabaseService>,
        ai_service: Arc<AIService>,
        cache: Arc<CacheService>,
    ) -> BeaconResult<Self> {
        let config = SearchServiceConfig::default();
        Self::with_config(config, database, ai_service, cache).await
    }

    /// Create search service with custom configuration
    pub async fn with_config(
        config: SearchServiceConfig,
        database: Arc<DatabaseService>,
        ai_service: Arc<AIService>,
        cache: Arc<CacheService>,
    ) -> BeaconResult<Self> {
        let service = Self {
            config,
            database,
            ai_service,
            cache,
            search_index: Arc::new(RwLock::new(HashMap::new())),
            keyword_index: Arc::new(RwLock::new(HashMap::new())),
        };

        // Initialize search index
        service.rebuild_index().await?;

        Ok(service)
    }

    /// Health check for search service
    pub async fn health_check(&self) -> BeaconResult<()> {
        let index_size = self.search_index.read().await.len();
        if index_size == 0 {
            return Err(BeaconError::SearchError("Search index is empty".to_string()));
        }
        Ok(())
    }

    /// Perform comprehensive search
    pub async fn search(
        &self,
        query: &str,
        filters: Option<SearchFilters>,
        pagination: Option<PaginationParams>,
    ) -> BeaconResult<PaginatedResponse<SearchResult>> {
        let cache_key = format!("search:{}:{:?}:{:?}", query, filters, pagination);
        
        // Check cache first
        if let Some(cached_results) = self.cache.get::<PaginatedResponse<SearchResult>>(&cache_key).await? {
            return Ok(cached_results);
        }

        let mut all_results = Vec::new();

        // Perform different types of search based on configuration
        if self.config.enable_semantic_search {
            let semantic_results = self.semantic_search(query, &filters).await?;
            all_results.extend(semantic_results);
        }

        if self.config.enable_fuzzy_search {
            let fuzzy_results = self.fuzzy_search(query, &filters).await?;
            all_results.extend(fuzzy_results);
        }

        let keyword_results = self.keyword_search(query, &filters).await?;
        all_results.extend(keyword_results);

        // Remove duplicates and sort by relevance
        all_results = self.deduplicate_and_sort(all_results);

        // Apply filters
        if let Some(ref filters) = filters {
            all_results = self.apply_filters(all_results, filters).await?;
        }

        // Apply pagination
        let pagination = pagination.unwrap_or_default();
        let total_count = all_results.len() as u32;
        let start_index = (pagination.page.saturating_sub(1)) * pagination.limit;
        let end_index = std::cmp::min(start_index + pagination.limit, total_count);
        
        let results = if start_index < total_count {
            all_results[start_index as usize..end_index as usize].to_vec()
        } else {
            Vec::new()
        };

        let paginated_response = PaginatedResponse {
            items: results,
            total: total_count,
            page: pagination.page,
            limit: pagination.limit,
            has_next: end_index < total_count,
            has_prev: pagination.page > 1,
        };

        // Cache the results
        self.cache.set(&cache_key, &paginated_response, Some(self.config.cache_ttl_seconds)).await?;

        Ok(paginated_response)
    }

    /// Semantic search using embeddings
    pub async fn semantic_search(
        &self,
        query: &str,
        _filters: &Option<SearchFilters>,
    ) -> BeaconResult<Vec<SearchResult>> {
        let query_embedding = self.ai_service.generate_embeddings(query).await?;
        let mut results = Vec::new();

        let index = self.search_index.read().await;
        for entry in index.values() {
            if let Some(ref embedding) = entry.embedding {
                let similarity = self.cosine_similarity(&query_embedding, embedding);
                
                if similarity >= self.config.default_similarity_threshold {
                    let search_result = SearchResult {
                        source_id: entry.source_id.clone(),
                        title: entry.title.clone(),
                        excerpt: self.generate_excerpt(&entry.content, query),
                        relevance_score: similarity,
                        source_type: "semantic".to_string(),
                        metadata: entry.metadata.clone(),
                        highlights: self.generate_highlights(&entry.content, query),
                    };
                    results.push(search_result);
                }
            }
        }

        // Sort by similarity score
        results.sort_by(|a, b| b.relevance_score.partial_cmp(&a.relevance_score).unwrap_or(std::cmp::Ordering::Equal));
        
        // Limit results
        results.truncate(self.config.max_results as usize);
        
        Ok(results)
    }

    /// Fuzzy search for typo tolerance
    pub async fn fuzzy_search(
        &self,
        query: &str,
        _filters: &Option<SearchFilters>,
    ) -> BeaconResult<Vec<SearchResult>> {
        let mut results = Vec::new();
        let query_lower = query.to_lowercase();
        
        let index = self.search_index.read().await;
        for entry in index.values() {
            let title_distance = self.levenshtein_distance(&query_lower, &entry.title.to_lowercase());
            let content_words: Vec<&str> = entry.content.split_whitespace().collect();
            
            let mut min_distance = usize::MAX;
            for word in content_words {
                let distance = self.levenshtein_distance(&query_lower, &word.to_lowercase());
                min_distance = std::cmp::min(min_distance, distance);
            }
            
            // Consider it a match if edit distance is small relative to query length
            let max_distance = std::cmp::max(1, query.len() / 3);
            if title_distance <= max_distance || min_distance <= max_distance {
                let relevance = 1.0 - (std::cmp::min(title_distance, min_distance) as f32 / query.len() as f32);
                
                let search_result = SearchResult {
                    source_id: entry.source_id.clone(),
                    title: entry.title.clone(),
                    excerpt: self.generate_excerpt(&entry.content, query),
                    relevance_score: relevance,
                    source_type: "fuzzy".to_string(),
                    metadata: entry.metadata.clone(),
                    highlights: self.generate_highlights(&entry.content, query),
                };
                results.push(search_result);
            }
        }
        
        results.sort_by(|a, b| b.relevance_score.partial_cmp(&a.relevance_score).unwrap_or(std::cmp::Ordering::Equal));
        results.truncate(self.config.max_results as usize);
        
        Ok(results)
    }

    /// Keyword-based search
    pub async fn keyword_search(
        &self,
        query: &str,
        _filters: &Option<SearchFilters>,
    ) -> BeaconResult<Vec<SearchResult>> {
        let mut results = Vec::new();
        let query_keywords: Vec<&str> = query.split_whitespace().collect();
        
        let keyword_index = self.keyword_index.read().await;
        let search_index = self.search_index.read().await;
        
        let mut document_scores: HashMap<String, f32> = HashMap::new();
        
        for keyword in &query_keywords {
            let keyword_lower = keyword.to_lowercase();
            if let Some(doc_ids) = keyword_index.get(&keyword_lower) {
                for doc_id in doc_ids {
                    *document_scores.entry(doc_id.clone()).or_insert(0.0) += 1.0;
                }
            }
        }
        
        for (doc_id, score) in document_scores {
            if let Some(entry) = search_index.get(&doc_id) {
                let normalized_score = score / query_keywords.len() as f32;
                
                let search_result = SearchResult {
                    source_id: entry.source_id.clone(),
                    title: entry.title.clone(),
                    excerpt: self.generate_excerpt(&entry.content, query),
                    relevance_score: normalized_score,
                    source_type: "keyword".to_string(),
                    metadata: entry.metadata.clone(),
                    highlights: self.generate_highlights(&entry.content, query),
                };
                results.push(search_result);
            }
        }
        
        results.sort_by(|a, b| b.relevance_score.partial_cmp(&a.relevance_score).unwrap_or(std::cmp::Ordering::Equal));
        results.truncate(self.config.max_results as usize);
        
        Ok(results)
    }

    /// Add document to search index
    pub async fn index_document(
        &self,
        source_id: &str,
        title: &str,
        content: &str,
        metadata: Option<HashMap<String, serde_json::Value>>,
    ) -> BeaconResult<()> {
        let embedding = if self.config.enable_semantic_search {
            Some(self.ai_service.generate_embeddings(content).await?)
        } else {
            None
        };
        
        let keywords = self.extract_keywords(content);
        let entry_id = uuid::Uuid::new_v4().to_string();
        
        let entry = SearchIndexEntry {
            id: entry_id.clone(),
            source_id: source_id.to_string(),
            title: title.to_string(),
            content: content.to_string(),
            keywords: keywords.clone(),
            embedding,
            indexed_at: Utc::now(),
            metadata: metadata.unwrap_or_default(),
        };
        
        // Add to search index
        {
            let mut index = self.search_index.write().await;
            index.insert(entry_id.clone(), entry);
        }
        
        // Update keyword index
        {
            let mut keyword_index = self.keyword_index.write().await;
            for keyword in keywords {
                keyword_index.entry(keyword.to_lowercase())
                    .or_insert_with(Vec::new)
                    .push(entry_id.clone());
            }
        }
        
        Ok(())
    }

    /// Remove document from search index
    pub async fn remove_document(&self, source_id: &str) -> BeaconResult<()> {
        let mut entries_to_remove = Vec::new();
        
        // Find entries to remove
        {
            let index = self.search_index.read().await;
            for (entry_id, entry) in index.iter() {
                if entry.source_id == source_id {
                    entries_to_remove.push((entry_id.clone(), entry.keywords.clone()));
                }
            }
        }
        
        // Remove from search index
        {
            let mut index = self.search_index.write().await;
            for (entry_id, _) in &entries_to_remove {
                index.remove(entry_id);
            }
        }
        
        // Remove from keyword index
        {
            let mut keyword_index = self.keyword_index.write().await;
            for (entry_id, keywords) in entries_to_remove {
                for keyword in keywords {
                    if let Some(doc_ids) = keyword_index.get_mut(&keyword.to_lowercase()) {
                        doc_ids.retain(|id| id != &entry_id);
                        if doc_ids.is_empty() {
                            keyword_index.remove(&keyword.to_lowercase());
                        }
                    }
                }
            }
        }
        
        Ok(())
    }

    /// Rebuild entire search index
    pub async fn rebuild_index(&self) -> BeaconResult<()> {
        // Clear existing indices
        {
            let mut search_index = self.search_index.write().await;
            let mut keyword_index = self.keyword_index.write().await;
            search_index.clear();
            keyword_index.clear();
        }
        
        // Get all sources from database
        let sources = self.database.list_sources("", PaginationParams::default()).await?;
        
        // Index all sources
        for source in sources.items {
            // Convert SourceMetadata to HashMap
            let mut metadata_map = HashMap::new();
            metadata_map.insert("source_type".to_string(), serde_json::json!(source.source_type));
            if let Some(url) = &source.url {
                metadata_map.insert("url".to_string(), serde_json::json!(url));
            }
            if let Some(file_path) = &source.file_path {
                metadata_map.insert("file_path".to_string(), serde_json::json!(file_path));
            }
            // Add other metadata fields as needed
            
            self.index_document(
                &source.id,
                &source.title,
                &source.content,
                Some(metadata_map),
            ).await?;
        }
        
        Ok(())
    }

    /// Get search statistics
    pub async fn get_statistics(&self) -> SearchServiceStats {
        let search_index_size = self.search_index.read().await.len();
        let keyword_index_size = self.keyword_index.read().await.len();
        
        SearchServiceStats {
            indexed_documents: search_index_size as u32,
            unique_keywords: keyword_index_size as u32,
            cache_hit_rate: self.cache.get_hit_rate().await.unwrap_or(0.0),
            average_search_time_ms: 0, // Would be tracked in a real implementation
        }
    }

    // Private helper methods
    fn cosine_similarity(&self, a: &[f32], b: &[f32]) -> f32 {
        if a.len() != b.len() {
            return 0.0;
        }
        
        let dot_product: f32 = a.iter().zip(b.iter()).map(|(x, y)| x * y).sum();
        let norm_a: f32 = a.iter().map(|x| x * x).sum::<f32>().sqrt();
        let norm_b: f32 = b.iter().map(|x| x * x).sum::<f32>().sqrt();
        
        if norm_a == 0.0 || norm_b == 0.0 {
            0.0
        } else {
            dot_product / (norm_a * norm_b)
        }
    }

    fn levenshtein_distance(&self, s1: &str, s2: &str) -> usize {
        let len1 = s1.len();
        let len2 = s2.len();
        let mut matrix = vec![vec![0; len2 + 1]; len1 + 1];
        
        for i in 0..=len1 {
            matrix[i][0] = i;
        }
        for j in 0..=len2 {
            matrix[0][j] = j;
        }
        
        let s1_chars: Vec<char> = s1.chars().collect();
        let s2_chars: Vec<char> = s2.chars().collect();
        
        for i in 1..=len1 {
            for j in 1..=len2 {
                let cost = if s1_chars[i - 1] == s2_chars[j - 1] { 0 } else { 1 };
                matrix[i][j] = std::cmp::min(
                    std::cmp::min(matrix[i - 1][j] + 1, matrix[i][j - 1] + 1),
                    matrix[i - 1][j - 1] + cost,
                );
            }
        }
        
        matrix[len1][len2]
    }

    fn extract_keywords(&self, text: &str) -> Vec<String> {
        // Simple keyword extraction - in a real implementation, this would be more sophisticated
        text.split_whitespace()
            .filter(|word| word.len() > 2) // Filter out very short words
            .map(|word| word.to_lowercase().trim_matches(|c: char| !c.is_alphanumeric()).to_string())
            .filter(|word| !word.is_empty())
            .collect()
    }

    fn generate_excerpt(&self, content: &str, query: &str) -> String {
        let query_lower = query.to_lowercase();
        let words: Vec<&str> = content.split_whitespace().collect();
        
        // Find the first occurrence of any query word
        for (i, word) in words.iter().enumerate() {
            if word.to_lowercase().contains(&query_lower) {
                let start = i.saturating_sub(10);
                let end = std::cmp::min(i + 10, words.len());
                let excerpt = words[start..end].join(" ");
                return if start > 0 { format!("...{}", excerpt) } else { excerpt };
            }
        }
        
        // If no match found, return first 100 characters
        if content.len() > 100 {
            format!("{}...", &content[..100])
        } else {
            content.to_string()
        }
    }

    fn generate_highlights(&self, content: &str, query: &str) -> Vec<String> {
        let query_words: Vec<&str> = query.split_whitespace().collect();
        let mut highlights = Vec::new();
        
        for word in query_words {
            if content.to_lowercase().contains(&word.to_lowercase()) {
                highlights.push(word.to_string());
            }
        }
        
        highlights
    }

    fn deduplicate_and_sort(&self, mut results: Vec<SearchResult>) -> Vec<SearchResult> {
        // Remove duplicates based on source_id
        let mut seen = std::collections::HashSet::new();
        results.retain(|result| seen.insert(result.source_id.clone()));
        
        // Sort by relevance score (descending)
        results.sort_by(|a, b| b.relevance_score.partial_cmp(&a.relevance_score).unwrap_or(std::cmp::Ordering::Equal));
        
        results
    }

    async fn apply_filters(
        &self,
        results: Vec<SearchResult>,
        filters: &SearchFilters,
    ) -> BeaconResult<Vec<SearchResult>> {
        let mut filtered_results = results;
        
        // Apply source type filter
        if !filters.source_types.is_empty() {
            filtered_results.retain(|result| {
                // Parse string to SourceType for comparison
                filters.source_types.iter().any(|st| {
                    format!("{:?}", st).to_lowercase() == result.source_type.to_lowercase()
                })
            });
        }
        
        // Apply date range filter (would need to be implemented based on metadata)
        if let Some(ref _date_range) = filters.date_range {
            // Implementation would depend on how dates are stored in metadata
        }
        
        // Note: min_relevance_score is not a field in SearchFilters
        // If needed, it should be added to the SearchFilters struct
        
        Ok(filtered_results)
    }
}

/// Search service statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchServiceStats {
    pub indexed_documents: u32,
    pub unique_keywords: u32,
    pub cache_hit_rate: f32,
    pub average_search_time_ms: u64,
}