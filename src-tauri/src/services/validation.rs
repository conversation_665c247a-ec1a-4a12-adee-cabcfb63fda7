//! Validation service for input validation and data sanitization

use std::collections::HashMap;
use serde::{Deserialize, Serialize};
use regex::Regex;
use chrono::{DateTime, Utc};

use crate::models::{
    common::{BeaconResult, BeaconError},
    beacon::{Page, Project, Source, SavedSearch},
    lighthouse::{ChatMessage, Notebook, StudioDocument},
};

/// Validation rule types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ValidationRule {
    Required,
    MinLength(usize),
    MaxLength(usize),
    Pattern(String),
    Email,
    Url,
    Range(f64, f64),
    Custom(String), // Custom validation function name
}

/// Validation error details
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationError {
    pub field: String,
    pub rule: ValidationRule,
    pub message: String,
    pub value: Option<String>,
}

/// Validation result
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ValidationResult {
    pub is_valid: bool,
    pub errors: Vec<ValidationError>,
    pub warnings: Vec<String>,
}

impl ValidationResult {
    pub fn new() -> Self {
        Self {
            is_valid: true,
            errors: Vec::new(),
            warnings: Vec::new(),
        }
    }
    
    pub fn add_error(&mut self, error: ValidationError) {
        self.is_valid = false;
        self.errors.push(error);
    }
    
    pub fn add_warning(&mut self, warning: String) {
        self.warnings.push(warning);
    }
    
    pub fn merge(&mut self, other: ValidationResult) {
        if !other.is_valid {
            self.is_valid = false;
        }
        self.errors.extend(other.errors);
        self.warnings.extend(other.warnings);
    }
}

/// Validation service configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationServiceConfig {
    pub enable_sanitization: bool,
    pub max_string_length: usize,
    pub allowed_html_tags: Vec<String>,
    pub enable_profanity_filter: bool,
    pub custom_patterns: HashMap<String, String>,
}

impl Default for ValidationServiceConfig {
    fn default() -> Self {
        Self {
            enable_sanitization: true,
            max_string_length: 10000,
            allowed_html_tags: vec![
                "p".to_string(), "br".to_string(), "strong".to_string(), 
                "em".to_string(), "ul".to_string(), "ol".to_string(), 
                "li".to_string(), "a".to_string(), "code".to_string(),
                "pre".to_string(), "blockquote".to_string(),
            ],
            enable_profanity_filter: false,
            custom_patterns: HashMap::new(),
        }
    }
}

/// Validation service for input validation and sanitization
pub struct ValidationService {
    config: ValidationServiceConfig,
    email_regex: Regex,
    url_regex: Regex,
    html_regex: Regex,
    script_regex: Regex,
}

impl ValidationService {
    /// Create new validation service
    pub fn new() -> BeaconResult<Self> {
        let config = ValidationServiceConfig::default();
        Self::with_config(config)
    }

    /// Create validation service with custom configuration
    pub fn with_config(config: ValidationServiceConfig) -> BeaconResult<Self> {
        let email_regex = Regex::new(r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$")
            .map_err(|e| BeaconError::ValidationError(format!("Email regex error: {}", e)))?;
        
        let url_regex = Regex::new(r"^https?://[^\s/$.?#].[^\s]*$")
            .map_err(|e| BeaconError::ValidationError(format!("URL regex error: {}", e)))?;
        
        let html_regex = Regex::new(r"<[^>]*>")
            .map_err(|e| BeaconError::ValidationError(format!("HTML regex error: {}", e)))?;
        
        let script_regex = Regex::new(r"(?i)<script[^>]*>.*?</script>")
            .map_err(|e| BeaconError::ValidationError(format!("Script regex error: {}", e)))?;
        
        Ok(Self {
            config,
            email_regex,
            url_regex,
            html_regex,
            script_regex,
        })
    }

    /// Health check for validation service
    pub fn health_check(&self) -> BeaconResult<()> {
        // Test regex patterns
        let test_email = "<EMAIL>";
        if !self.email_regex.is_match(test_email) {
            return Err(BeaconError::ValidationError("Email regex not working".to_string()));
        }
        Ok(())
    }

    /// Validate a Page object
    pub fn validate_page(&self, page: &Page) -> ValidationResult {
        let mut result = ValidationResult::new();
        
        // Validate title
        self.validate_string_field(&page.title, "title", &[
            ValidationRule::Required,
            ValidationRule::MinLength(1),
            ValidationRule::MaxLength(200),
        ], &mut result);
        
        // Validate content
        self.validate_string_field(&page.content, "content", &[
            ValidationRule::MaxLength(self.config.max_string_length),
        ], &mut result);
        
        // Validate tags
        for (i, tag) in page.tags.iter().enumerate() {
            self.validate_string_field(tag, &format!("tags[{}]", i), &[
                ValidationRule::MinLength(1),
                ValidationRule::MaxLength(50),
                ValidationRule::Pattern(r"^[a-zA-Z0-9_-]+$".to_string()),
            ], &mut result);
        }
        
        // Validate project_id if present
        if let Some(ref project_id) = page.project_id {
            self.validate_uuid(project_id, "project_id", &mut result);
        }
        
        result
    }

    /// Validate a Project object
    pub fn validate_project(&self, project: &Project) -> ValidationResult {
        let mut result = ValidationResult::new();
        
        // Validate name
        self.validate_string_field(&project.name, "name", &[
            ValidationRule::Required,
            ValidationRule::MinLength(1),
            ValidationRule::MaxLength(100),
        ], &mut result);
        
        // Validate description
        if let Some(ref description) = project.description {
            self.validate_string_field(description, "description", &[
                ValidationRule::MaxLength(1000),
            ], &mut result);
        }
        
        result
    }

    /// Validate a Source object
    pub fn validate_source(&self, source: &Source) -> ValidationResult {
        let mut result = ValidationResult::new();
        
        // Validate title
        self.validate_string_field(&source.title, "title", &[
            ValidationRule::Required,
            ValidationRule::MinLength(1),
            ValidationRule::MaxLength(200),
        ], &mut result);
        
        // Validate content
        self.validate_string_field(&source.content, "content", &[
            ValidationRule::Required,
            ValidationRule::MinLength(1),
        ], &mut result);
        
        // Validate URL if present
        if let Some(ref url) = source.url {
            self.validate_string_field(url, "url", &[
                ValidationRule::Url,
            ], &mut result);
        }
        
        result
    }

    /// Validate a ChatMessage object
    pub fn validate_chat_message(&self, message: &ChatMessage) -> ValidationResult {
        let mut result = ValidationResult::new();
        
        // Validate content
        self.validate_string_field(&message.content, "content", &[
            ValidationRule::Required,
            ValidationRule::MinLength(1),
            ValidationRule::MaxLength(self.config.max_string_length),
        ], &mut result);
        
        // Validate conversation_id
        self.validate_string_field(&message.conversation_id, "conversation_id", &[
            ValidationRule::Required,
            ValidationRule::MinLength(1),
        ], &mut result);
        
        result
    }

    /// Validate a SavedSearch object
    pub fn validate_saved_search(&self, search: &SavedSearch) -> ValidationResult {
        let mut result = ValidationResult::new();
        
        // Validate name
        self.validate_string_field(&search.name, "name", &[
            ValidationRule::Required,
            ValidationRule::MinLength(1),
            ValidationRule::MaxLength(100),
        ], &mut result);
        
        // Validate query
        self.validate_string_field(&search.query, "query", &[
            ValidationRule::Required,
            ValidationRule::MinLength(1),
            ValidationRule::MaxLength(500),
        ], &mut result);
        
        result
    }

    /// Sanitize HTML content
    pub fn sanitize_html(&self, content: &str) -> String {
        if !self.config.enable_sanitization {
            return content.to_string();
        }
        
        let mut sanitized = content.to_string();
        
        // Remove script tags
        sanitized = self.script_regex.replace_all(&sanitized, "").to_string();
        
        // Remove dangerous attributes
        sanitized = self.remove_dangerous_attributes(&sanitized);
        
        // Filter allowed HTML tags
        sanitized = self.filter_html_tags(&sanitized);
        
        sanitized
    }

    /// Sanitize string input
    pub fn sanitize_string(&self, input: &str) -> String {
        if !self.config.enable_sanitization {
            return input.to_string();
        }
        
        let mut sanitized = input.to_string();
        
        // Remove null bytes
        sanitized = sanitized.replace('\0', "");
        
        // Trim whitespace
        sanitized = sanitized.trim().to_string();
        
        // Limit length
        if sanitized.len() > self.config.max_string_length {
            sanitized.truncate(self.config.max_string_length);
        }
        
        sanitized
    }

    /// Validate email format
    pub fn validate_email(&self, email: &str) -> bool {
        self.email_regex.is_match(email)
    }

    /// Validate URL format
    pub fn validate_url(&self, url: &str) -> bool {
        self.url_regex.is_match(url)
    }

    /// Validate UUID format
    pub fn validate_uuid_format(&self, uuid: &str) -> bool {
        uuid::Uuid::parse_str(uuid).is_ok()
    }

    /// Validate JSON structure
    pub fn validate_json(&self, json_str: &str) -> BeaconResult<serde_json::Value> {
        serde_json::from_str(json_str)
            .map_err(|e| BeaconError::ValidationError(format!("Invalid JSON: {}", e)))
    }

    /// Validate date range
    pub fn validate_date_range(&self, start: &DateTime<Utc>, end: &DateTime<Utc>) -> bool {
        start <= end
    }

    /// Batch validate multiple objects
    pub fn batch_validate<T, F>(&self, items: &[T], validator: F) -> Vec<ValidationResult>
    where
        F: Fn(&T) -> ValidationResult,
    {
        items.iter().map(validator).collect()
    }

    /// Get validation statistics
    pub fn get_validation_stats(&self) -> ValidationStats {
        ValidationStats {
            sanitization_enabled: self.config.enable_sanitization,
            max_string_length: self.config.max_string_length,
            allowed_html_tags_count: self.config.allowed_html_tags.len(),
            custom_patterns_count: self.config.custom_patterns.len(),
        }
    }

    // Private helper methods
    fn validate_string_field(
        &self,
        value: &str,
        field_name: &str,
        rules: &[ValidationRule],
        result: &mut ValidationResult,
    ) {
        for rule in rules {
            match rule {
                ValidationRule::Required => {
                    if value.trim().is_empty() {
                        result.add_error(ValidationError {
                            field: field_name.to_string(),
                            rule: rule.clone(),
                            message: format!("{} is required", field_name),
                            value: Some(value.to_string()),
                        });
                    }
                }
                ValidationRule::MinLength(min_len) => {
                    if value.len() < *min_len {
                        result.add_error(ValidationError {
                            field: field_name.to_string(),
                            rule: rule.clone(),
                            message: format!("{} must be at least {} characters long", field_name, min_len),
                            value: Some(value.to_string()),
                        });
                    }
                }
                ValidationRule::MaxLength(max_len) => {
                    if value.len() > *max_len {
                        result.add_error(ValidationError {
                            field: field_name.to_string(),
                            rule: rule.clone(),
                            message: format!("{} must be at most {} characters long", field_name, max_len),
                            value: Some(value.to_string()),
                        });
                    }
                }
                ValidationRule::Pattern(pattern) => {
                    if let Ok(regex) = Regex::new(pattern) {
                        if !regex.is_match(value) {
                            result.add_error(ValidationError {
                                field: field_name.to_string(),
                                rule: rule.clone(),
                                message: format!("{} does not match required pattern", field_name),
                                value: Some(value.to_string()),
                            });
                        }
                    }
                }
                ValidationRule::Email => {
                    if !self.validate_email(value) {
                        result.add_error(ValidationError {
                            field: field_name.to_string(),
                            rule: rule.clone(),
                            message: format!("{} must be a valid email address", field_name),
                            value: Some(value.to_string()),
                        });
                    }
                }
                ValidationRule::Url => {
                    if !self.validate_url(value) {
                        result.add_error(ValidationError {
                            field: field_name.to_string(),
                            rule: rule.clone(),
                            message: format!("{} must be a valid URL", field_name),
                            value: Some(value.to_string()),
                        });
                    }
                }
                _ => {} // Other rules don't apply to strings
            }
        }
    }

    fn validate_uuid(&self, uuid: &str, field_name: &str, result: &mut ValidationResult) {
        if !self.validate_uuid_format(uuid) {
            result.add_error(ValidationError {
                field: field_name.to_string(),
                rule: ValidationRule::Custom("uuid".to_string()),
                message: format!("{} must be a valid UUID", field_name),
                value: Some(uuid.to_string()),
            });
        }
    }

    fn remove_dangerous_attributes(&self, content: &str) -> String {
        let dangerous_attrs = [
            "onload", "onclick", "onmouseover", "onerror", "onsubmit",
            "onchange", "onkeydown", "onkeyup", "onfocus", "onblur",
            "javascript:", "vbscript:", "data:",
        ];
        
        let mut result = content.to_string();
        for attr in &dangerous_attrs {
            let pattern = format!(r"(?i){}[^\s>]*", regex::escape(attr));
            if let Ok(regex) = Regex::new(&pattern) {
                result = regex.replace_all(&result, "").to_string();
            }
        }
        
        result
    }

    fn filter_html_tags(&self, content: &str) -> String {
        if self.config.allowed_html_tags.is_empty() {
            // Remove all HTML tags
            return self.html_regex.replace_all(content, "").to_string();
        }
        
        // This is a simplified implementation
        // In a real application, you'd want to use a proper HTML parser
        let mut result = content.to_string();
        
        // Find all HTML tags
        let tag_regex = Regex::new(r"<(/?)([a-zA-Z][a-zA-Z0-9]*)[^>]*>").unwrap();
        
        result = tag_regex.replace_all(&result, |caps: &regex::Captures| {
            let tag_name = caps.get(2).unwrap().as_str().to_lowercase();
            if self.config.allowed_html_tags.contains(&tag_name) {
                caps.get(0).unwrap().as_str().to_string()
            } else {
                String::new()
            }
        }).to_string();
        
        result
    }
}

/// Validation service statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationStats {
    pub sanitization_enabled: bool,
    pub max_string_length: usize,
    pub allowed_html_tags_count: usize,
    pub custom_patterns_count: usize,
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::models::common::Timestamps;
    use crate::models::beacon::{PageLayout, PageSettings};

    #[test]
    fn test_email_validation() {
        let validator = ValidationService::new().unwrap();
        
        assert!(validator.validate_email("<EMAIL>"));
        assert!(validator.validate_email("<EMAIL>"));
        assert!(!validator.validate_email("invalid-email"));
        assert!(!validator.validate_email("@domain.com"));
        assert!(!validator.validate_email("user@"));
    }

    #[test]
    fn test_url_validation() {
        let validator = ValidationService::new().unwrap();
        
        assert!(validator.validate_url("https://example.com"));
        assert!(validator.validate_url("http://subdomain.example.com/path"));
        assert!(!validator.validate_url("not-a-url"));
        assert!(!validator.validate_url("ftp://example.com"));
    }

    #[test]
    fn test_html_sanitization() {
        let validator = ValidationService::new().unwrap();
        
        let malicious_html = r#"<script>alert('xss')</script><p>Safe content</p><div onclick="alert('click')">Click me</div>"#;
        let sanitized = validator.sanitize_html(malicious_html);
        
        assert!(!sanitized.contains("<script>"));
        assert!(!sanitized.contains("onclick"));
        assert!(sanitized.contains("<p>Safe content</p>"));
    }

    #[test]
    fn test_page_validation() {
        let validator = ValidationService::new().unwrap();
        
        let valid_page = Page {
            id: "test-id".to_string(),
            title: "Valid Title".to_string(),
            content: "Valid content".to_string(),
            layout: PageLayout::Document,
            tags: vec!["tag1".to_string(), "tag2".to_string()],
            project_id: None,
            settings: PageSettings::default(),
            metadata: std::collections::HashMap::new(),
            timestamps: Timestamps::new(),
        };
        
        let result = validator.validate_page(&valid_page);
        assert!(result.is_valid);
        
        let invalid_page = Page {
            id: "test-id".to_string(),
            title: "".to_string(), // Empty title should fail
            content: "Valid content".to_string(),
            layout: PageLayout::Document,
            tags: vec!["invalid tag with spaces".to_string()], // Invalid tag format
            project_id: None,
            settings: PageSettings::default(),
            metadata: std::collections::HashMap::new(),
            timestamps: Timestamps::new(),
        };
        
        let result = validator.validate_page(&invalid_page);
        assert!(!result.is_valid);
        assert!(!result.errors.is_empty());
    }
}