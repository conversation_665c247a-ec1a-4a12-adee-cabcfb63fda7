use anyhow::{anyhow, Result};
use serde::{Deserialize, Serialize};
use std::path::{Path, PathBuf};
use std::fs;
use uuid::Uuid;
use chrono::Utc;
use rusqlite::Connection;

// Re-use types from document_processing module if available
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DocumentMetadata {
    pub title: String,
    pub author: Option<String>,
    pub created_date: Option<String>,
    pub modified_date: Option<String>,
    pub page_count: Option<usize>,
    pub word_count: Option<usize>,
    pub language: Option<String>,
    pub format: String,
    pub size_bytes: usize,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProcessedDocument {
    pub id: String,
    pub name: String,
    pub source_type: String,
    pub content: String,
    pub metadata: DocumentMetadata,
    pub extracted_text: String,
    pub summary: Option<String>,
    pub key_points: Vec<String>,
    pub tags: Vec<String>,
    pub processed_at: i64,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ProcessingProgress {
    pub document_id: String,
    pub status: ProcessingStatus,
    pub progress: f32,
    pub message: String,
    pub error: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum ProcessingStatus {
    Pending,
    Processing,
    Completed,
    Failed,
}

pub struct LightbulbDocumentProcessor {
    upload_dir: PathBuf,
    ai_service: Option<crate::beacon_ai_service::LightbulbAIService>,
}

impl LightbulbDocumentProcessor {
    pub fn new(upload_dir: PathBuf) -> Result<Self> {
        // Create upload directory if it doesn't exist
        fs::create_dir_all(&upload_dir)?;
        
        Ok(Self {
            upload_dir,
            ai_service: None,
        })
    }
    
    pub fn with_ai_service(mut self, ai_service: crate::beacon_ai_service::LightbulbAIService) -> Self {
        self.ai_service = Some(ai_service);
        self
    }
    
    pub async fn process_document(&self, file_path: &Path) -> Result<ProcessedDocument> {
        // Get file metadata
        let metadata = self.extract_metadata(file_path)?;
        
        // Extract text based on file type
        let extracted_text = self.extract_text(file_path).await?;
        
        // Generate AI-powered enhancements if AI service is available
        let (summary, key_points, tags) = if let Some(ai) = &self.ai_service {
            let summary = ai.summarize_content(&extracted_text).await.ok();
            let key_points = ai.extract_key_points(&extracted_text).await.unwrap_or_default();
            let tags = ai.generate_tags(&extracted_text).await.unwrap_or_default();
            (summary, key_points, tags)
        } else {
            (None, vec![], vec![])
        };
        
        let document = ProcessedDocument {
            id: format!("doc_{}", Uuid::new_v4()),
            name: file_path.file_name()
                .and_then(|n| n.to_str())
                .unwrap_or("unknown")
                .to_string(),
            source_type: metadata.format.clone(),
            content: extracted_text.clone(),
            metadata,
            extracted_text,
            summary,
            key_points,
            tags,
            processed_at: Utc::now().timestamp(),
        };
        
        Ok(document)
    }
    
    async fn extract_text(&self, file_path: &Path) -> Result<String> {
        let extension = file_path.extension()
            .and_then(|e| e.to_str())
            .unwrap_or("")
            .to_lowercase();
        
        match extension.as_str() {
            "txt" | "md" => self.extract_text_file(file_path),
            "pdf" => self.extract_pdf_text(file_path).await,
            "docx" | "doc" => self.extract_docx_text(file_path).await,
            "jpg" | "jpeg" | "png" | "gif" | "webp" => self.extract_image_text(file_path).await,
            "mp3" | "wav" | "m4a" | "ogg" => self.extract_audio_text(file_path).await,
            "mp4" | "webm" | "avi" | "mov" => self.extract_video_text(file_path).await,
            _ => Err(anyhow!("Unsupported file type: {}", extension)),
        }
    }
    
    fn extract_text_file(&self, file_path: &Path) -> Result<String> {
        fs::read_to_string(file_path)
            .map_err(|e| anyhow!("Failed to read text file: {}", e))
    }
    
    async fn extract_pdf_text(&self, file_path: &Path) -> Result<String> {
        // Try using pdf-extract crate first (simple text extraction)
        match pdf_extract::extract_text(file_path) {
            Ok(text) if !text.trim().is_empty() => Ok(text),
            _ => {
                // If pdf-extract fails or returns empty, try Python-based extraction
                self.extract_with_python(file_path, "pdf").await
            }
        }
    }
    
    async fn extract_docx_text(&self, file_path: &Path) -> Result<String> {
        // Use Python bridge for DOCX extraction
        self.extract_with_python(file_path, "docx").await
    }
    
    async fn extract_image_text(&self, file_path: &Path) -> Result<String> {
        // Use Python bridge for OCR
        self.extract_with_python(file_path, "image").await
    }
    
    async fn extract_audio_text(&self, file_path: &Path) -> Result<String> {
        // Use Python bridge for audio transcription
        self.extract_with_python(file_path, "audio").await
    }
    
    async fn extract_video_text(&self, file_path: &Path) -> Result<String> {
        // Use Python bridge for video transcription
        self.extract_with_python(file_path, "video").await
    }
    
    async fn extract_with_python(&self, file_path: &Path, doc_type: &str) -> Result<String> {
        // This would integrate with the Python bridge
        // For now, return a placeholder
        Ok(format!(
            "[Document processing for {} files requires Python bridge to be enabled. \
            File: {}]",
            doc_type,
            file_path.display()
        ))
    }
    
    fn extract_metadata(&self, file_path: &Path) -> Result<DocumentMetadata> {
        let file_meta = fs::metadata(file_path)?;
        let extension = file_path.extension()
            .and_then(|e| e.to_str())
            .unwrap_or("")
            .to_lowercase();
        
        Ok(DocumentMetadata {
            title: file_path.file_stem()
                .and_then(|s| s.to_str())
                .unwrap_or("Untitled")
                .to_string(),
            author: None,
            created_date: None,
            modified_date: Some(
                chrono::DateTime::<chrono::Utc>::from(file_meta.modified()?)
                    .to_rfc3339()
            ),
            page_count: None,
            word_count: None,
            language: None,
            format: extension,
            size_bytes: file_meta.len() as usize,
        })
    }
    
    pub async fn process_url(&self, url: &str) -> Result<ProcessedDocument> {
        // Fetch content from URL
        let client = reqwest::Client::new();
        let response = client.get(url).send().await?;
        let content = response.text().await?;
        
        // Extract meaningful text from HTML if needed
        let extracted_text = self.extract_text_from_html(&content)?;
        
        // Generate AI-powered enhancements if available
        let (summary, key_points, tags) = if let Some(ai) = &self.ai_service {
            let summary = ai.summarize_content(&extracted_text).await.ok();
            let key_points = ai.extract_key_points(&extracted_text).await.unwrap_or_default();
            let tags = ai.generate_tags(&extracted_text).await.unwrap_or_default();
            (summary, key_points, tags)
        } else {
            (None, vec![], vec![])
        };
        
        let document = ProcessedDocument {
            id: format!("url_{}", Uuid::new_v4()),
            name: url.to_string(),
            source_type: "url".to_string(),
            content: content.clone(),
            metadata: DocumentMetadata {
                title: self.extract_title_from_html(&content).unwrap_or_else(|| url.to_string()),
                author: None,
                created_date: None,
                modified_date: None,
                page_count: None,
                word_count: Some(extracted_text.split_whitespace().count()),
                language: None,
                format: "html".to_string(),
                size_bytes: content.len(),
            },
            extracted_text,
            summary,
            key_points,
            tags,
            processed_at: Utc::now().timestamp(),
        };
        
        Ok(document)
    }
    
    fn extract_text_from_html(&self, html: &str) -> Result<String> {
        // Simple HTML text extraction (removes tags)
        // In production, use a proper HTML parser like scraper
        let text = html
            .split('<')
            .flat_map(|s| s.split('>').skip(1))
            .collect::<Vec<_>>()
            .join(" ");
        
        Ok(text.trim().to_string())
    }
    
    fn extract_title_from_html(&self, html: &str) -> Option<String> {
        // Extract title from HTML
        if let Some(start) = html.find("<title>") {
            if let Some(end) = html[start..].find("</title>") {
                let title = &html[start + 7..start + end];
                return Some(title.trim().to_string());
            }
        }
        None
    }
    
    pub async fn save_to_database(&self, document: &ProcessedDocument, conn: &Connection) -> Result<()> {
        let metadata_json = serde_json::to_string(&document.metadata)?;
        let tags_json = serde_json::to_string(&document.tags)?;
        let key_points_json = serde_json::to_string(&document.key_points)?;
        
        // Save to beacon_sources table
        conn.execute(
            "INSERT INTO beacon_sources 
             (id, name, type, content, extracted_text, metadata, uploaded_at, processed_at, status) 
             VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9)",
            rusqlite::params![
                &document.id,
                &document.name,
                &document.source_type,
                &document.content,
                &document.extracted_text,
                &metadata_json,
                Utc::now().timestamp(),
                document.processed_at,
                "completed"
            ],
        )?;
        
        // Save to beacon_knowledge_items table
        if !document.extracted_text.is_empty() {
            let knowledge_id = format!("knowledge_{}", Uuid::new_v4());
            conn.execute(
                "INSERT INTO beacon_knowledge_items 
                 (id, title, content, summary, tags, source_type, source_id, created_at, updated_at) 
                 VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9)",
                rusqlite::params![
                    &knowledge_id,
                    &document.metadata.title,
                    &document.extracted_text,
                    &document.summary,
                    &tags_json,
                    "document",
                    &document.id,
                    document.processed_at,
                    document.processed_at
                ],
            )?;
        }
        
        Ok(())
    }
}

// Tauri commands for document processing
#[tauri::command]
pub async fn process_uploaded_document(file_path: String) -> Result<ProcessedDocument, String> {
    let path = Path::new(&file_path);
    
    let processor = LightbulbDocumentProcessor::new(
        dirs::document_dir().unwrap_or_else(|| PathBuf::from("./uploads"))
    ).map_err(|e| format!("Failed to initialize processor: {}", e))?;
    
    processor.process_document(path).await
        .map_err(|e| format!("Failed to process document: {}", e))
}

#[tauri::command]
pub async fn process_url_content(url: String) -> Result<ProcessedDocument, String> {
    let processor = LightbulbDocumentProcessor::new(
        dirs::document_dir().unwrap_or_else(|| PathBuf::from("./uploads"))
    ).map_err(|e| format!("Failed to initialize processor: {}", e))?;
    
    processor.process_url(&url).await
        .map_err(|e| format!("Failed to process URL: {}", e))
}

#[tauri::command]
pub async fn get_processing_progress(document_id: String) -> Result<ProcessingProgress, String> {
    // This would check actual processing status from a queue or database
    Ok(ProcessingProgress {
        document_id,
        status: ProcessingStatus::Completed,
        progress: 1.0,
        message: "Processing complete".to_string(),
        error: None,
    })
}

#[tauri::command]
pub async fn batch_process_documents(file_paths: Vec<String>) -> Result<Vec<ProcessedDocument>, String> {
    let processor = LightbulbDocumentProcessor::new(
        dirs::document_dir().unwrap_or_else(|| PathBuf::from("./uploads"))
    ).map_err(|e| format!("Failed to initialize processor: {}", e))?;
    
    let mut results = Vec::new();
    for file_path in file_paths {
        match processor.process_document(Path::new(&file_path)).await {
            Ok(doc) => results.push(doc),
            Err(e) => {
                // Log error but continue processing other documents
                eprintln!("Failed to process {}: {}", file_path, e);
            }
        }
    }
    
    Ok(results)
}