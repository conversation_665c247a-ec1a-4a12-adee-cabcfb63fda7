use serde::{Deserialize, Serialize};
use anyhow::Result;
use rusqlite::{params, Connection};
use chrono::Utc;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ComplianceRule {
    pub id: String,
    pub name: String,
    pub rule_type: String, // "spending_limit", "approval_required", "category_restriction"
    pub conditions: String, // JSON conditions
    pub actions: String, // JSON actions to take
    pub severity: String, // "warning", "error", "block"
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ComplianceResult {
    pub is_compliant: bool,
    pub violations: Vec<Violation>,
    pub warnings: Vec<Warning>,
    pub required_approvals: Vec<Approver>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Violation {
    pub rule_id: String,
    pub rule_name: String,
    pub severity: String,
    pub message: String,
    pub details: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Warning {
    pub rule_id: String,
    pub rule_name: String,
    pub message: String,
    pub threshold_percentage: f64,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Approver {
    pub id: String,
    pub name: String,
    pub role: String,
    pub level: i32,
    pub required_for_amount: f64,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct NewExpense {
    pub amount: f64,
    pub category: String,
    pub department: String,
    pub description: String,
    pub vendor: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ExpenseRow {
    pub id: String,
    pub amount: f64,
    pub category: String,
    pub department: String,
    pub description: String,
    pub vendor: Option<String>,
    pub status: String,
    pub created_at: String,
    pub updated_at: String,
}

pub fn check_compliance(conn: &Connection, expense: &NewExpense) -> Result<ComplianceResult> {
    let mut violations = Vec::new();
    let mut warnings = Vec::new();
    let mut required_approvals = Vec::new();
    
    // Check spending limits
    if let Err(violation) = check_spending_limits(conn, &expense.department, &expense.category, expense.amount) {
        violations.push(violation);
    }
    
    // Check approval requirements
    let approvers = get_required_approvers(conn, expense.amount, &expense.category)?;
    required_approvals.extend(approvers);
    
    // Check category restrictions
    if let Err(violation) = check_category_restrictions(conn, &expense.category, &expense.department) {
        violations.push(violation);
    }
    
    // Check for warnings (e.g., approaching limits)
    if let Some(warning) = check_threshold_warnings(conn, &expense.department, &expense.category, expense.amount)? {
        warnings.push(warning);
    }
    
    Ok(ComplianceResult {
        is_compliant: violations.is_empty(),
        violations,
        warnings,
        required_approvals,
    })
}

pub fn enforce_spending_limits(
    conn: &Connection, 
    department: &str, 
    category: &str, 
    amount: f64, 
    year: i64
) -> Result<bool> {
    // Check department spending limits
    let dept_limit: Option<f64> = conn.query_row(
        "SELECT allocated_amount FROM department_allocations 
         WHERE name = ?1 AND year = ?2",
        params![department, year],
        |row| row.get(0),
    ).ok();
    
    if let Some(limit) = dept_limit {
        let current_spend: f64 = conn.query_row(
            "SELECT COALESCE(SUM(amount), 0) FROM expenses 
             WHERE department_id = ?1 AND strftime('%Y', date) = ?2",
            params![department, year.to_string()],
            |row| row.get(0),
        )?;
        
        if current_spend + amount > limit {
            return Ok(false);
        }
    }
    
    // Check category limits
    let cat_limit: Option<f64> = conn.query_row(
        "SELECT limit_amount FROM category_limits 
         WHERE category = ?1 AND year = ?2",
        params![category, year],
        |row| row.get(0),
    ).ok();
    
    if let Some(limit) = cat_limit {
        let current_spend: f64 = conn.query_row(
            "SELECT COALESCE(SUM(amount), 0) FROM expenses 
             WHERE category = ?1 AND strftime('%Y', date) = ?2",
            params![category, year.to_string()],
            |row| row.get(0),
        )?;
        
        if current_spend + amount > limit {
            return Ok(false);
        }
    }
    
    // Check overall budget constraints
    let total_budget: Option<f64> = conn.query_row(
        "SELECT total_amount FROM budgets WHERE year = ?1",
        params![year],
        |row| row.get(0),
    ).ok();
    
    if let Some(budget) = total_budget {
        let total_spend: f64 = conn.query_row(
            "SELECT COALESCE(SUM(amount), 0) FROM expenses 
             WHERE strftime('%Y', date) = ?1",
            params![year.to_string()],
            |row| row.get(0),
        )?;
        
        if total_spend + amount > budget {
            return Ok(false);
        }
    }
    
    Ok(true)
}

pub fn validate_approval_chain(conn: &Connection, expense: &ExpenseRow) -> Result<Vec<Approver>> {
    let mut approvers = Vec::new();
    
    // Define approval thresholds
    let thresholds = vec![
        (1000.0, "Manager", 1),
        (5000.0, "Director", 2),
        (10000.0, "VP", 3),
        (50000.0, "CFO", 4),
    ];
    
    for (threshold, role, level) in thresholds {
        if expense.amount >= threshold {
            approvers.push(Approver {
                id: format!("approver_{}", level),
                name: format!("{} Approval", role),
                role: role.to_string(),
                level,
                required_for_amount: threshold,
            });
        }
    }
    
    // Check department-specific approval rules
    let dept_rules: Vec<(String, f64)> = conn.prepare(
        "SELECT rule_type, value FROM allocation_rules 
         WHERE target = 'department' AND target_id = ?1 AND enabled = 1"
    )?.query_map(params![&expense.department], |row| {
        Ok((row.get(0)?, row.get(1)?))
    })?.collect::<Result<Vec<_>, _>>()?;
    
    for (rule_type, threshold) in dept_rules {
        if rule_type == "approval_threshold" && expense.amount >= threshold {
            approvers.push(Approver {
                id: format!("dept_approver_{}", expense.department),
                name: format!("{} Department Head", expense.department),
                role: "Department Head".to_string(),
                level: 2,
                required_for_amount: threshold,
            });
        }
    }
    
    Ok(approvers)
}

fn check_spending_limits(
    conn: &Connection, 
    department: &str, 
    category: &str, 
    amount: f64
) -> Result<(), Violation> {
    // Check if amount exceeds immediate spending limits
    let year = Utc::now().format("%Y").to_string().parse::<i64>().unwrap();
    
    // Check category limit
    if let Ok(limit) = conn.query_row::<f64, _, _>(
        "SELECT limit_amount FROM category_limits 
         WHERE category = ?1 AND year = ?2",
        params![category, year],
        |row| row.get(0),
    ) {
        if let Ok(current) = conn.query_row::<f64, _, _>(
            "SELECT COALESCE(SUM(amount), 0) FROM expenses 
             WHERE category = ?1 AND strftime('%Y', date) = ?2",
            params![category, year.to_string()],
            |row| row.get(0),
        ) {
            if current + amount > limit {
                return Err(Violation {
                    rule_id: "cat_limit".to_string(),
                    rule_name: "Category Spending Limit".to_string(),
                    severity: "error".to_string(),
                    message: format!("Expense exceeds category limit for {}", category),
                    details: format!("Limit: ${:.2}, Current: ${:.2}, Requested: ${:.2}", 
                                   limit, current, amount),
                });
            }
        }
    }
    
    Ok(())
}

fn check_category_restrictions(
    conn: &Connection, 
    category: &str, 
    department: &str
) -> Result<(), Violation> {
    // Check if category is restricted for department
    let restricted: bool = conn.query_row(
        "SELECT COUNT(*) > 0 FROM budget_policies 
         WHERE active = 1 AND rules LIKE ?1",
        params![format!("%restrict_category:{}:department:{}%", category, department)],
        |row| row.get(0),
    ).unwrap_or(false);
    
    if restricted {
        return Err(Violation {
            rule_id: "cat_restrict".to_string(),
            rule_name: "Category Restriction".to_string(),
            severity: "block".to_string(),
            message: format!("Category {} is restricted for department {}", category, department),
            details: "This combination is not allowed by policy".to_string(),
        });
    }
    
    Ok(())
}

fn check_threshold_warnings(
    conn: &Connection, 
    department: &str, 
    category: &str, 
    amount: f64
) -> Result<Option<Warning>> {
    let year = Utc::now().format("%Y").to_string().parse::<i64>().unwrap();
    
    // Check if approaching category limit (80% threshold)
    if let Ok(limit) = conn.query_row::<f64, _, _>(
        "SELECT limit_amount FROM category_limits 
         WHERE category = ?1 AND year = ?2",
        params![category, year],
        |row| row.get(0),
    ) {
        if let Ok(current) = conn.query_row::<f64, _, _>(
            "SELECT COALESCE(SUM(amount), 0) FROM expenses 
             WHERE category = ?1 AND strftime('%Y', date) = ?2",
            params![category, year.to_string()],
            |row| row.get(0),
        ) {
            let percentage = ((current + amount) / limit) * 100.0;
            if percentage >= 80.0 && percentage < 100.0 {
                return Ok(Some(Warning {
                    rule_id: "threshold_warn".to_string(),
                    rule_name: "Spending Threshold Warning".to_string(),
                    message: format!("Approaching {} spending limit", category),
                    threshold_percentage: percentage,
                }));
            }
        }
    }
    
    Ok(None)
}

fn get_required_approvers(
    conn: &Connection, 
    amount: f64, 
    category: &str
) -> Result<Vec<Approver>> {
    let mut approvers = Vec::new();
    
    // Basic approval levels based on amount
    if amount >= 1000.0 {
        approvers.push(Approver {
            id: "mgr_001".to_string(),
            name: "Manager Approval".to_string(),
            role: "Manager".to_string(),
            level: 1,
            required_for_amount: 1000.0,
        });
    }
    
    if amount >= 5000.0 {
        approvers.push(Approver {
            id: "dir_001".to_string(),
            name: "Director Approval".to_string(),
            role: "Director".to_string(),
            level: 2,
            required_for_amount: 5000.0,
        });
    }
    
    if amount >= 10000.0 {
        approvers.push(Approver {
            id: "vp_001".to_string(),
            name: "VP Approval".to_string(),
            role: "VP".to_string(),
            level: 3,
            required_for_amount: 10000.0,
        });
    }
    
    // Category-specific approvals
    let category_rules: Vec<String> = conn.prepare(
        "SELECT rules FROM budget_policies 
         WHERE active = 1 AND rules LIKE ?1"
    )?.query_map(
        params![format!("%category:{}%approval%", category)],
        |row| row.get(0)
    )?.collect::<Result<Vec<_>, _>>()?;
    
    // Parse and add category-specific approvers
    for _rule in category_rules {
        // In a real implementation, parse JSON rules and add approvers
    }
    
    Ok(approvers)
}