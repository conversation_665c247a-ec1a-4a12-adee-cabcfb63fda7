-- Lightbulb Database Schema
-- This file contains the complete database schema for the Lightbulb knowledge management system

-- Pages table for storing knowledge pages
CREATE TABLE IF NOT EXISTS lightbulb_pages (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    tags TEXT NOT NULL, -- JSON array
    parent_id TEXT,
    template_id TEXT,
    metadata TEXT NOT NULL, -- JSON object
    settings TEXT NOT NULL, -- JSON object
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL,
    FOREIGN KEY (parent_id) REFERENCES lightbulb_pages(id) ON DELETE SET NULL,
    FOREIGN KEY (template_id) REFERENCES lightbulb_page_templates(id) ON DELETE SET NULL
);

-- Page templates table
CREATE TABLE IF NOT EXISTS lightbulb_page_templates (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT NOT NULL,
    content TEXT NOT NULL,
    category TEXT NOT NULL,
    tags TEXT NOT NULL, -- <PERSON><PERSON><PERSON> array
    icon TEXT,
    preview_image TEXT,
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL
);

-- Projects table
CREATE TABLE IF NOT EXISTS lightbulb_projects (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT NOT NULL,
    tags TEXT NOT NULL, -- JSON array
    color TEXT NOT NULL DEFAULT '#3B82F6',
    icon TEXT NOT NULL DEFAULT 'folder',
    status TEXT NOT NULL DEFAULT 'active',
    priority TEXT NOT NULL DEFAULT 'medium',
    metadata TEXT NOT NULL, -- JSON object
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL
);

-- Project templates table
CREATE TABLE IF NOT EXISTS lightbulb_project_templates (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT NOT NULL,
    default_structure TEXT NOT NULL, -- JSON array
    default_tags TEXT NOT NULL, -- JSON array
    icon TEXT NOT NULL,
    created_at INTEGER NOT NULL
);

-- Project-Page relationship table
CREATE TABLE IF NOT EXISTS lightbulb_project_pages (
    project_id TEXT NOT NULL,
    page_id TEXT NOT NULL,
    position INTEGER DEFAULT 0,
    created_at INTEGER NOT NULL,
    PRIMARY KEY (project_id, page_id),
    FOREIGN KEY (project_id) REFERENCES lightbulb_projects(id) ON DELETE CASCADE,
    FOREIGN KEY (page_id) REFERENCES lightbulb_pages(id) ON DELETE CASCADE
);

-- Sources table for document management
CREATE TABLE IF NOT EXISTS lightbulb_sources (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    type TEXT NOT NULL, -- pdf, txt, docx, url, note, image, video, audio
    content TEXT,
    url TEXT,
    file_path TEXT,
    metadata TEXT, -- JSON object
    uploaded_at INTEGER NOT NULL,
    processed_at INTEGER,
    status TEXT NOT NULL DEFAULT 'pending', -- pending, processing, completed, failed
    error_message TEXT,
    extracted_text TEXT,
    embeddings BLOB -- For vector search
);

-- Knowledge items table (processed content)
CREATE TABLE IF NOT EXISTS lightbulb_knowledge_items (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    summary TEXT,
    tags TEXT NOT NULL, -- JSON array
    source_type TEXT, -- manual, document, url, note
    source_id TEXT,
    embeddings BLOB, -- For vector search
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL,
    FOREIGN KEY (source_id) REFERENCES lightbulb_sources(id) ON DELETE SET NULL
);

-- Saved searches table
CREATE TABLE IF NOT EXISTS lightbulb_saved_searches (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    filters TEXT NOT NULL, -- JSON object
    created_at INTEGER NOT NULL
);

-- User patterns table for learning preferences
CREATE TABLE IF NOT EXISTS lightbulb_user_patterns (
    id TEXT PRIMARY KEY,
    pattern_type TEXT NOT NULL,
    pattern_value REAL NOT NULL,
    metadata TEXT, -- JSON object
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL
);

-- Chat messages table for AI interactions
CREATE TABLE IF NOT EXISTS lightbulb_chat_messages (
    id TEXT PRIMARY KEY,
    conversation_id TEXT NOT NULL,
    content TEXT NOT NULL,
    is_user INTEGER NOT NULL,
    context_ids TEXT, -- JSON array of knowledge item IDs
    metadata TEXT, -- JSON object
    created_at INTEGER NOT NULL
);

-- Conversations table
CREATE TABLE IF NOT EXISTS lightbulb_conversations (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    summary TEXT,
    tags TEXT, -- JSON array
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL
);

-- Tags table for centralized tag management
CREATE TABLE IF NOT EXISTS lightbulb_tags (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    color TEXT,
    icon TEXT,
    usage_count INTEGER DEFAULT 0,
    created_at INTEGER NOT NULL
);

-- Categories table
CREATE TABLE IF NOT EXISTS lightbulb_categories (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    color TEXT,
    icon TEXT,
    parent_id TEXT,
    created_at INTEGER NOT NULL,
    FOREIGN KEY (parent_id) REFERENCES lightbulb_categories(id) ON DELETE SET NULL
);

-- Export history table
CREATE TABLE IF NOT EXISTS lightbulb_export_history (
    id TEXT PRIMARY KEY,
    export_type TEXT NOT NULL, -- pdf, markdown, html, docx
    content_type TEXT NOT NULL, -- page, project, search_results
    content_ids TEXT NOT NULL, -- JSON array
    file_path TEXT,
    metadata TEXT, -- JSON object
    created_at INTEGER NOT NULL
);

-- Collaboration sessions table
CREATE TABLE IF NOT EXISTS lightbulb_collaboration_sessions (
    id TEXT PRIMARY KEY,
    page_id TEXT NOT NULL,
    participants TEXT NOT NULL, -- JSON array
    status TEXT NOT NULL DEFAULT 'active', -- active, paused, ended
    created_at INTEGER NOT NULL,
    ended_at INTEGER,
    FOREIGN KEY (page_id) REFERENCES lightbulb_pages(id) ON DELETE CASCADE
);

-- Activity log table
CREATE TABLE IF NOT EXISTS lightbulb_activity_log (
    id TEXT PRIMARY KEY,
    user_id TEXT,
    action TEXT NOT NULL,
    entity_type TEXT NOT NULL,
    entity_id TEXT,
    metadata TEXT, -- JSON object
    created_at INTEGER NOT NULL
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_pages_updated_at ON lightbulb_pages(updated_at DESC);
CREATE INDEX IF NOT EXISTS idx_pages_parent_id ON lightbulb_pages(parent_id);
CREATE INDEX IF NOT EXISTS idx_pages_template_id ON lightbulb_pages(template_id);

CREATE INDEX IF NOT EXISTS idx_projects_updated_at ON lightbulb_projects(updated_at DESC);
CREATE INDEX IF NOT EXISTS idx_projects_status ON lightbulb_projects(status);

CREATE INDEX IF NOT EXISTS idx_sources_status ON lightbulb_sources(status);
CREATE INDEX IF NOT EXISTS idx_sources_type ON lightbulb_sources(type);
CREATE INDEX IF NOT EXISTS idx_sources_uploaded_at ON lightbulb_sources(uploaded_at DESC);

CREATE INDEX IF NOT EXISTS idx_knowledge_items_source_id ON lightbulb_knowledge_items(source_id);
CREATE INDEX IF NOT EXISTS idx_knowledge_items_created_at ON lightbulb_knowledge_items(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_chat_messages_conversation_id ON lightbulb_chat_messages(conversation_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_created_at ON lightbulb_chat_messages(created_at);

CREATE INDEX IF NOT EXISTS idx_tags_name ON lightbulb_tags(name);
CREATE INDEX IF NOT EXISTS idx_tags_usage_count ON lightbulb_tags(usage_count DESC);

CREATE INDEX IF NOT EXISTS idx_activity_log_created_at ON lightbulb_activity_log(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_activity_log_entity ON lightbulb_activity_log(entity_type, entity_id);

-- Full-text search virtual tables
CREATE VIRTUAL TABLE IF NOT EXISTS lightbulb_pages_fts USING fts5(
    title, 
    content, 
    tags,
    content=lightbulb_pages,
    content_rowid=rowid
);

CREATE VIRTUAL TABLE IF NOT EXISTS lightbulb_knowledge_items_fts USING fts5(
    title,
    content,
    summary,
    tags,
    content=lightbulb_knowledge_items,
    content_rowid=rowid
);

-- Triggers to keep FTS tables in sync
CREATE TRIGGER IF NOT EXISTS lightbulb_pages_fts_insert 
AFTER INSERT ON lightbulb_pages BEGIN
    INSERT INTO lightbulb_pages_fts(title, content, tags) 
    VALUES (new.title, new.content, new.tags);
END;

CREATE TRIGGER IF NOT EXISTS lightbulb_pages_fts_update 
AFTER UPDATE ON lightbulb_pages BEGIN
    UPDATE lightbulb_pages_fts 
    SET title = new.title, content = new.content, tags = new.tags
    WHERE rowid = new.rowid;
END;

CREATE TRIGGER IF NOT EXISTS lightbulb_pages_fts_delete 
AFTER DELETE ON lightbulb_pages BEGIN
    DELETE FROM lightbulb_pages_fts WHERE rowid = old.rowid;
END;

CREATE TRIGGER IF NOT EXISTS lightbulb_knowledge_items_fts_insert 
AFTER INSERT ON lightbulb_knowledge_items BEGIN
    INSERT INTO lightbulb_knowledge_items_fts(title, content, summary, tags) 
    VALUES (new.title, new.content, new.summary, new.tags);
END;

CREATE TRIGGER IF NOT EXISTS lightbulb_knowledge_items_fts_update 
AFTER UPDATE ON lightbulb_knowledge_items BEGIN
    UPDATE lightbulb_knowledge_items_fts 
    SET title = new.title, content = new.content, summary = new.summary, tags = new.tags
    WHERE rowid = new.rowid;
END;

CREATE TRIGGER IF NOT EXISTS lightbulb_knowledge_items_fts_delete 
AFTER DELETE ON lightbulb_knowledge_items BEGIN
    DELETE FROM lightbulb_knowledge_items_fts WHERE rowid = old.rowid;
END;