use anyhow::Result;
use rusqlite::Connection;

/// Initialize Beacon database tables and schema
pub fn init_beacon_tables(conn: &Connection) -> Result<()> {
    // Read the schema file
    let schema = include_str!("./db_beacon_schema.sql");
    
    // Execute the schema
    conn.execute_batch(schema)?;
    
    // Insert default templates if they don't exist
    insert_default_templates(conn)?;
    
    Ok(())
}

fn insert_default_templates(conn: &Connection) -> Result<()> {
    let now = chrono::Utc::now().timestamp();
    
    // Check if templates already exist
    let count: i64 = conn.query_row(
        "SELECT COUNT(*) FROM beacon_page_templates",
        [],
        |row| row.get(0),
    )?;
    
    if count > 0 {
        return Ok(()); // Templates already exist
    }
    
    // Insert default page templates
    let page_templates = vec![
        (
            "tpl_meeting_notes",
            "Meeting Notes",
            "Template for capturing meeting discussions and action items",
            "# Meeting Notes\n\n**Date:** {{date}}\n**Attendees:** \n\n## Agenda\n- \n\n## Discussion Points\n\n## Action Items\n- [ ] \n\n## Next Steps\n\n## Notes",
            "business",
            r#"["meetings", "notes", "business"]"#,
            "meeting"
        ),
        (
            "tpl_project_brief",
            "Project Brief",
            "Template for project planning and documentation",
            "# Project Brief: {{project_name}}\n\n## Overview\n\n## Objectives\n1. \n\n## Scope\n### In Scope\n- \n\n### Out of Scope\n- \n\n## Timeline\n- Start Date: \n- End Date: \n\n## Resources\n\n## Success Criteria\n\n## Risks & Mitigation",
            "business",
            r#"["project", "planning", "brief"]"#,
            "briefcase"
        ),
        (
            "tpl_daily_journal",
            "Daily Journal",
            "Template for daily reflections and thoughts",
            "# Daily Journal - {{date}}\n\n## Today's Highlights\n- \n\n## Gratitude\nI'm grateful for:\n1. \n\n## Learnings\n\n## Tomorrow's Priorities\n1. \n\n## Reflections",
            "personal",
            r#"["journal", "daily", "personal"]"#,
            "book"
        ),
        (
            "tpl_research_notes",
            "Research Notes",
            "Template for organizing research findings",
            "# Research: {{topic}}\n\n## Research Question\n\n## Key Sources\n1. \n\n## Findings\n\n### Main Points\n- \n\n### Supporting Evidence\n- \n\n## Analysis\n\n## Conclusions\n\n## Further Research Needed\n- ",
            "academic",
            r#"["research", "academic", "study"]"#,
            "microscope"
        ),
        (
            "tpl_book_notes",
            "Book Notes",
            "Template for book summaries and insights",
            "# Book Notes: {{title}}\n\n**Author:** {{author}}\n**Date Read:** {{date}}\n**Rating:** ⭐⭐⭐⭐⭐\n\n## Summary\n\n## Key Takeaways\n1. \n\n## Favorite Quotes\n> \n\n## Personal Reflections\n\n## Action Items\n- [ ] ",
            "learning",
            r#"["books", "reading", "learning"]"#,
            "book-open"
        ),
        (
            "tpl_code_snippet",
            "Code Documentation",
            "Template for documenting code snippets and solutions",
            "# Code: {{title}}\n\n## Problem Statement\n\n## Solution\n\n```{{language}}\n// Your code here\n```\n\n## Explanation\n\n## Time Complexity\nO()\n\n## Space Complexity\nO()\n\n## Test Cases\n```\nInput: \nOutput: \n```\n\n## Notes",
            "technical",
            r#"["code", "programming", "technical"]"#,
            "code"
        )
    ];
    
    for template in page_templates {
        conn.execute(
            "INSERT INTO beacon_page_templates 
             (id, name, description, content, category, tags, icon, created_at, updated_at) 
             VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9)",
            rusqlite::params![
                template.0, template.1, template.2, template.3,
                template.4, template.5, template.6, now, now
            ],
        )?;
    }
    
    // Insert default project templates
    let project_templates = vec![
        (
            "tpl_proj_software",
            "Software Development",
            "Template for software development projects",
            r#"["requirements", "design", "development", "testing", "deployment"]"#,
            r#"["software", "development", "agile"]"#,
            "code-branch"
        ),
        (
            "tpl_proj_research",
            "Research Project",
            "Template for research and analysis projects",
            r#"["hypothesis", "literature_review", "methodology", "data_collection", "analysis", "conclusions"]"#,
            r#"["research", "analysis", "academic"]"#,
            "chart-bar"
        ),
        (
            "tpl_proj_content",
            "Content Creation",
            "Template for content creation projects",
            r#"["ideation", "outline", "draft", "review", "publish"]"#,
            r#"["content", "writing", "creative"]"#,
            "edit"
        ),
        (
            "tpl_proj_learning",
            "Learning Path",
            "Template for structured learning projects",
            r#"["overview", "resources", "notes", "exercises", "projects", "review"]"#,
            r#"["learning", "education", "course"]"#,
            "graduation-cap"
        )
    ];
    
    for template in project_templates {
        conn.execute(
            "INSERT INTO beacon_project_templates 
             (id, name, description, default_structure, default_tags, icon, created_at) 
             VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7)",
            rusqlite::params![
                template.0, template.1, template.2, template.3,
                template.4, template.5, now
            ],
        )?;
    }
    
    // Insert default categories
    let categories = vec![
        ("cat_business", "Business", "Work and business related content", "#3B82F6", "briefcase"),
        ("cat_personal", "Personal", "Personal notes and journals", "#10B981", "user"),
        ("cat_learning", "Learning", "Educational content and courses", "#F59E0B", "book-open"),
        ("cat_technical", "Technical", "Technical documentation and code", "#8B5CF6", "code"),
        ("cat_creative", "Creative", "Creative projects and ideas", "#EC4899", "palette"),
        ("cat_research", "Research", "Research and analysis", "#06B6D4", "microscope"),
    ];
    
    for category in categories {
        conn.execute(
            "INSERT OR IGNORE INTO beacon_categories 
             (id, name, description, color, icon, created_at) 
             VALUES (?1, ?2, ?3, ?4, ?5, ?6)",
            rusqlite::params![
                category.0, category.1, category.2, category.3, category.4, now
            ],
        )?;
    }
    
    // Insert common tags
    let tags = vec![
        ("Important", "#EF4444", "star"),
        ("Todo", "#F59E0B", "check-circle"),
        ("In Progress", "#3B82F6", "clock"),
        ("Completed", "#10B981", "check"),
        ("Archive", "#6B7280", "archive"),
        ("Idea", "#A855F7", "lightbulb"),
        ("Reference", "#06B6D4", "bookmark"),
    ];
    
    for tag in tags {
        conn.execute(
            "INSERT OR IGNORE INTO beacon_tags 
             (id, name, color, icon, created_at) 
             VALUES (?1, ?2, ?3, ?4, ?5)",
            rusqlite::params![
                format!("tag_{}", tag.0.to_lowercase().replace(" ", "_")),
                tag.0, tag.1, tag.2, now
            ],
        )?;
    }
    
    Ok(())
}