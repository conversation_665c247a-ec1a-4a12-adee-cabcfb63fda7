use anyhow::{anyhow, Result};
use chrono::{Utc, Datelike};
use rusqlite::{params, Connection, OptionalExtension};
use serde::{Deserialize, Serialize};
use std::path::PathBuf;
use std::sync::Mutex;

// Re-export budget extensions
pub use crate::db_budget_extensions::*;
// Re-export content extensions
pub use crate::db_content_extensions::*;
// Import Lightbulb initialization
mod db_lightbulb_init;

lazy_static::lazy_static! {
  static ref CONN: Mutex<Option<Connection>> = Mutex::new(None);
  static ref DB_PATH: Mutex<Option<PathBuf>> = Mutex::new(None);
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Budget { pub id: i64, pub year: i64, pub total_amount: f64, pub spent_amount: f64, pub created_at: String, pub updated_at: String }

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct RequestRow { pub id: i64, pub title: String, pub description: Option<String>, pub category: String, pub amount: f64, pub status: String, pub year: i64, pub comment: Option<String>, pub created_at: String, pub updated_at: String }

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct CategoryTotal { pub category: String, pub total: f64 }

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Stats { pub year: i64, pub total: f64, pub spent: f64, pub remaining: f64, pub count_pending: i64, pub count_approved: i64, pub count_rejected: i64, pub by_category: Vec<CategoryTotal> }

#[derive(Debug, Serialize, Deserialize, Clone, Default)]
pub struct RequestFilter { pub status: Option<String>, pub category: Option<String>, pub year: Option<i64> }

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct NewRequest { pub title: String, pub description: Option<String>, pub category: String, pub amount: f64, pub year: i64 }

#[derive(Debug, Serialize, Deserialize, Clone, Default)]
pub struct UpdateRequest { pub title: Option<String>, pub description: Option<String>, pub category: Option<String>, pub amount: Option<f64>, pub year: Option<i64>, pub status: Option<String>, pub comment: Option<String> }

// Expense Models
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Expense {
    pub id: String,
    pub title: String,
    pub description: String,
    pub amount: f64,
    pub category: String,
    pub department_id: Option<String>,
    pub status: String,
    pub date: String,
    pub receipt_url: Option<String>,
    pub recurrence: String,
    pub recurrence_end_date: Option<String>,
    pub created_at: String,
    pub updated_at: String,
}

// Training Need Models
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct TrainingNeedRow { 
    pub id: i64, 
    pub employee_id: String, 
    pub employee_name: String, 
    pub department: String, 
    pub current_position: String, 
    pub target_position: String, 
    pub skill_gap: String, 
    pub priority: String, 
    pub training_type: String, 
    pub recommended_training: String, 
    pub estimated_cost: f64, 
    pub expected_roi: f64, 
    pub completion_timeline: String, 
    pub business_impact: i32, 
    pub created_at: String, 
    pub updated_at: String 
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct NewTrainingNeed { 
    pub employee_id: String, 
    pub employee_name: String, 
    pub department: String, 
    pub current_position: String, 
    pub target_position: String, 
    pub skill_gap: String, 
    pub priority: String, 
    pub training_type: String, 
    pub recommended_training: String, 
    pub estimated_cost: f64, 
    pub expected_roi: f64, 
    pub completion_timeline: String, 
    pub business_impact: i32 
}

#[derive(Debug, Serialize, Deserialize, Clone, Default)]
pub struct UpdateTrainingNeed { 
    pub employee_id: Option<String>, 
    pub employee_name: Option<String>, 
    pub department: Option<String>, 
    pub current_position: Option<String>, 
    pub target_position: Option<String>, 
    pub skill_gap: Option<String>, 
    pub priority: Option<String>, 
    pub training_type: Option<String>, 
    pub recommended_training: Option<String>, 
    pub estimated_cost: Option<f64>, 
    pub expected_roi: Option<f64>, 
    pub completion_timeline: Option<String>, 
    pub business_impact: Option<i32> 
}

// Training Program Models
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct TrainingProgramRow { 
    pub id: i64, 
    pub name: String, 
    pub description: String, 
    pub training_type: String, 
    pub duration: String, 
    pub cost_per_person: f64, 
    pub provider: String, 
    pub completion_rate: f64, 
    pub effectiveness_score: f64, 
    pub created_at: String, 
    pub updated_at: String 
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct NewTrainingProgram { 
    pub name: String, 
    pub description: String, 
    pub training_type: String, 
    pub duration: String, 
    pub cost_per_person: f64, 
    pub provider: String, 
    pub completion_rate: f64, 
    pub effectiveness_score: f64 
}

#[derive(Debug, Serialize, Deserialize, Clone, Default)]
pub struct UpdateTrainingProgram { 
    pub name: Option<String>, 
    pub description: Option<String>, 
    pub training_type: Option<String>, 
    pub duration: Option<String>, 
    pub cost_per_person: Option<f64>, 
    pub provider: Option<String>, 
    pub completion_rate: Option<f64>, 
    pub effectiveness_score: Option<f64> 
}

// Training Analysis Models
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct TrainingAnalysisRow { 
    pub id: i64, 
    pub name: String, 
    pub description: String, 
    pub total_budget: f64, 
    pub allocated_amount: f64, 
    pub remaining_budget: f64, 
    pub expected_roi: f64, 
    pub completion_rate: f64, 
    pub feasibility: String, 
    pub created_at: String, 
    pub updated_at: String 
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct NewTrainingAnalysis { 
    pub name: String, 
    pub description: String, 
    pub total_budget: f64, 
    pub allocated_amount: f64, 
    pub remaining_budget: f64, 
    pub expected_roi: f64, 
    pub completion_rate: f64, 
    pub feasibility: String 
}

#[derive(Debug, Serialize, Deserialize, Clone, Default)]
pub struct UpdateTrainingAnalysis { 
    pub name: Option<String>, 
    pub description: Option<String>, 
    pub total_budget: Option<f64>, 
    pub allocated_amount: Option<f64>, 
    pub remaining_budget: Option<f64>, 
    pub expected_roi: Option<f64>, 
    pub completion_rate: Option<f64>, 
    pub feasibility: Option<String> 
}

pub fn init_db(app_dir: PathBuf) -> Result<()> {
  let mut db_path = app_dir;
  db_path.push("training_budget.db");
  
  // Store the database path for later use
  *DB_PATH.lock().unwrap() = Some(db_path.clone());
  
  let conn = Connection::open(&db_path)?;
  conn.execute_batch(
    "PRAGMA journal_mode=WAL;
     CREATE TABLE IF NOT EXISTS budgets (
       id INTEGER PRIMARY KEY AUTOINCREMENT,
       year INTEGER NOT NULL UNIQUE,
       total_amount REAL NOT NULL DEFAULT 0,
       spent_amount REAL NOT NULL DEFAULT 0,
       created_at TEXT NOT NULL,
       updated_at TEXT NOT NULL
     );
     CREATE TABLE IF NOT EXISTS requests (
       id INTEGER PRIMARY KEY AUTOINCREMENT,
       title TEXT NOT NULL,
       description TEXT,
       category TEXT NOT NULL,
       amount REAL NOT NULL,
       status TEXT NOT NULL DEFAULT 'pending',
       year INTEGER NOT NULL,
       comment TEXT,
       created_at TEXT NOT NULL,
       updated_at TEXT NOT NULL
     );
     CREATE INDEX IF NOT EXISTS idx_requests_year ON requests(year);
     CREATE INDEX IF NOT EXISTS idx_requests_status ON requests(status);
     CREATE INDEX IF NOT EXISTS idx_requests_category ON requests(category);
     
     -- Expenses Table
     CREATE TABLE IF NOT EXISTS expenses (
       id TEXT PRIMARY KEY,
       title TEXT NOT NULL,
       description TEXT NOT NULL,
       amount REAL NOT NULL,
       category TEXT NOT NULL,
       department_id TEXT,
       status TEXT NOT NULL,
       date TEXT NOT NULL,
       receipt_url TEXT,
       recurrence TEXT NOT NULL,
       recurrence_end_date TEXT,
       created_at TEXT NOT NULL,
       updated_at TEXT NOT NULL,
       year INTEGER NOT NULL
     );
     CREATE INDEX IF NOT EXISTS idx_expenses_year ON expenses(year);
     CREATE INDEX IF NOT EXISTS idx_expenses_status ON expenses(status);
     CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses(category);
     CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(date);
     
     -- Training Needs Tables
     CREATE TABLE IF NOT EXISTS training_needs (
       id INTEGER PRIMARY KEY AUTOINCREMENT,
       employee_id TEXT NOT NULL,
       employee_name TEXT NOT NULL,
       department TEXT NOT NULL,
       current_position TEXT NOT NULL,
       target_position TEXT NOT NULL,
       skill_gap TEXT NOT NULL,
       priority TEXT NOT NULL,
       training_type TEXT NOT NULL,
       recommended_training TEXT NOT NULL,
       estimated_cost REAL NOT NULL,
       expected_roi REAL NOT NULL,
       completion_timeline TEXT NOT NULL,
       business_impact INTEGER NOT NULL,
       created_at TEXT NOT NULL,
       updated_at TEXT NOT NULL
     );
     
     CREATE TABLE IF NOT EXISTS training_programs (
       id INTEGER PRIMARY KEY AUTOINCREMENT,
       name TEXT NOT NULL,
       description TEXT NOT NULL,
       training_type TEXT NOT NULL,
       duration TEXT NOT NULL,
       cost_per_person REAL NOT NULL,
       provider TEXT NOT NULL,
       completion_rate REAL NOT NULL,
       effectiveness_score REAL NOT NULL,
       created_at TEXT NOT NULL,
       updated_at TEXT NOT NULL
     );
     
     CREATE TABLE IF NOT EXISTS training_analyses (
       id INTEGER PRIMARY KEY AUTOINCREMENT,
       name TEXT NOT NULL,
       description TEXT NOT NULL,
       total_budget REAL NOT NULL,
       allocated_amount REAL NOT NULL,
       remaining_budget REAL NOT NULL,
       expected_roi REAL NOT NULL,
       completion_rate REAL NOT NULL,
       feasibility TEXT NOT NULL,
       created_at TEXT NOT NULL,
       updated_at TEXT NOT NULL
     );
     
     -- Enhanced Slash Commands Tables
     CREATE TABLE IF NOT EXISTS enhanced_commands (
       id INTEGER PRIMARY KEY AUTOINCREMENT,
       name TEXT NOT NULL UNIQUE,
       category TEXT NOT NULL,
       description TEXT NOT NULL,
       template TEXT NOT NULL,
       parameters TEXT, -- JSON array of parameter definitions
       tags TEXT, -- JSON array of tags
       scope TEXT NOT NULL DEFAULT 'user', -- user, project, global
       version INTEGER NOT NULL DEFAULT 1,
       is_active BOOLEAN NOT NULL DEFAULT 1,
       created_at TEXT NOT NULL,
       updated_at TEXT NOT NULL,
       usage_count INTEGER DEFAULT 0,
       last_used_at TEXT
     );
     
     CREATE TABLE IF NOT EXISTS command_categories (
       id INTEGER PRIMARY KEY AUTOINCREMENT,
       name TEXT NOT NULL UNIQUE,
       description TEXT,
       icon TEXT,
       color TEXT,
       sort_order INTEGER DEFAULT 0
     );
     
     CREATE TABLE IF NOT EXISTS command_usage_analytics (
       id INTEGER PRIMARY KEY AUTOINCREMENT,
       command_id INTEGER NOT NULL,
       execution_time REAL NOT NULL,
       success BOOLEAN NOT NULL,
       error_message TEXT,
       context_data TEXT, -- JSON
       executed_at TEXT NOT NULL,
       FOREIGN KEY (command_id) REFERENCES enhanced_commands(id)
     );
     
     CREATE TABLE IF NOT EXISTS command_dependencies (
       id INTEGER PRIMARY KEY AUTOINCREMENT,
       command_id INTEGER NOT NULL,
       dependency_command_id INTEGER NOT NULL,
       dependency_type TEXT NOT NULL DEFAULT 'required', -- required, optional, suggested
       created_at TEXT NOT NULL,
       FOREIGN KEY (command_id) REFERENCES enhanced_commands(id),
       FOREIGN KEY (dependency_command_id) REFERENCES enhanced_commands(id)
     );
     
     CREATE TABLE IF NOT EXISTS command_shortcuts (
       id INTEGER PRIMARY KEY AUTOINCREMENT,
       command_id INTEGER NOT NULL,
       shortcut_key TEXT NOT NULL,
       platform TEXT, -- windows, macos, linux, all
       created_at TEXT NOT NULL,
       FOREIGN KEY (command_id) REFERENCES enhanced_commands(id)
     );
     
     -- CLAUDE.md Context System Tables
     CREATE TABLE IF NOT EXISTS claude_contexts (
       id INTEGER PRIMARY KEY AUTOINCREMENT,
       file_path TEXT NOT NULL UNIQUE,
       project_root TEXT NOT NULL,
       relative_path TEXT NOT NULL,
       content_hash TEXT NOT NULL,
       parsed_content TEXT, -- JSON structure of parsed context
       template_type TEXT,
       parent_context_id INTEGER, -- For inheritance
       is_active BOOLEAN NOT NULL DEFAULT 1,
       created_at TEXT NOT NULL,
       updated_at TEXT NOT NULL,
       last_accessed_at TEXT,
       access_count INTEGER DEFAULT 0,
       FOREIGN KEY (parent_context_id) REFERENCES claude_contexts(id)
     );
     
     CREATE TABLE IF NOT EXISTS context_templates (
       id INTEGER PRIMARY KEY AUTOINCREMENT,
       name TEXT NOT NULL UNIQUE,
       description TEXT,
       category TEXT NOT NULL, -- react, rust, python, nodejs, fullstack, generic
       template_content TEXT NOT NULL,
       schema_definition TEXT, -- JSON schema for validation
       variables TEXT, -- JSON array of template variables
       is_default BOOLEAN DEFAULT 0,
       sort_order INTEGER DEFAULT 0,
       created_at TEXT NOT NULL,
       updated_at TEXT NOT NULL
     );
     
     CREATE TABLE IF NOT EXISTS context_history (
       id INTEGER PRIMARY KEY AUTOINCREMENT,
       context_id INTEGER NOT NULL,
       version INTEGER NOT NULL,
       content_hash TEXT NOT NULL,
       content TEXT NOT NULL,
       change_description TEXT,
       changed_by TEXT, -- session_id or user identifier
       created_at TEXT NOT NULL,
       FOREIGN KEY (context_id) REFERENCES claude_contexts(id)
     );
     
     CREATE TABLE IF NOT EXISTS context_inheritance (
       id INTEGER PRIMARY KEY AUTOINCREMENT,
       child_context_id INTEGER NOT NULL,
       parent_context_id INTEGER NOT NULL,
       inheritance_type TEXT NOT NULL DEFAULT 'extends', -- extends, includes, references
       priority INTEGER DEFAULT 0, -- For multiple inheritance ordering
       created_at TEXT NOT NULL,
       FOREIGN KEY (child_context_id) REFERENCES claude_contexts(id),
       FOREIGN KEY (parent_context_id) REFERENCES claude_contexts(id)
     );
     
     CREATE TABLE IF NOT EXISTS context_usage_analytics (
       id INTEGER PRIMARY KEY AUTOINCREMENT,
       context_id INTEGER NOT NULL,
       usage_type TEXT NOT NULL, -- read, write, reference, merge
       session_id TEXT,
       command_id INTEGER, -- If used with a command
       timestamp TEXT NOT NULL,
       metadata TEXT, -- JSON for additional context
       FOREIGN KEY (context_id) REFERENCES claude_contexts(id),
       FOREIGN KEY (command_id) REFERENCES enhanced_commands(id)
     );
     
     -- Indexes for better performance
     CREATE INDEX IF NOT EXISTS idx_enhanced_commands_category ON enhanced_commands(category);
     CREATE INDEX IF NOT EXISTS idx_enhanced_commands_scope ON enhanced_commands(scope);
     CREATE INDEX IF NOT EXISTS idx_enhanced_commands_active ON enhanced_commands(is_active);
     CREATE INDEX IF NOT EXISTS idx_command_usage_analytics_command_id ON command_usage_analytics(command_id);
     CREATE INDEX IF NOT EXISTS idx_command_usage_analytics_executed_at ON command_usage_analytics(executed_at);
     
     -- Context System Indexes
     CREATE INDEX IF NOT EXISTS idx_claude_contexts_project_root ON claude_contexts(project_root);
     CREATE INDEX IF NOT EXISTS idx_claude_contexts_template_type ON claude_contexts(template_type);
     CREATE INDEX IF NOT EXISTS idx_claude_contexts_parent ON claude_contexts(parent_context_id);
     CREATE INDEX IF NOT EXISTS idx_claude_contexts_active ON claude_contexts(is_active);
     CREATE INDEX IF NOT EXISTS idx_context_templates_category ON context_templates(category);
     CREATE INDEX IF NOT EXISTS idx_context_history_context_id ON context_history(context_id);
     CREATE INDEX IF NOT EXISTS idx_context_inheritance_child ON context_inheritance(child_context_id);
     CREATE INDEX IF NOT EXISTS idx_context_inheritance_parent ON context_inheritance(parent_context_id);
     CREATE INDEX IF NOT EXISTS idx_context_usage_analytics_context_id ON context_usage_analytics(context_id);
     CREATE INDEX IF NOT EXISTS idx_context_usage_analytics_timestamp ON context_usage_analytics(timestamp);
     
     -- App Settings Table
     CREATE TABLE IF NOT EXISTS app_settings (
       key TEXT PRIMARY KEY,
       value TEXT NOT NULL,
       created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
     );
     
     -- Enhanced MCP Server Tables (Phase 3)
     CREATE TABLE IF NOT EXISTS enhanced_mcp_servers (
       id INTEGER PRIMARY KEY AUTOINCREMENT,
       name TEXT NOT NULL UNIQUE,
       server_type TEXT NOT NULL, -- 'stdio', 'sse'
       command TEXT,
       args TEXT, -- JSON array
       env TEXT, -- JSON object
       url TEXT, -- for SSE servers
       status TEXT NOT NULL DEFAULT 'inactive',
       health_status TEXT DEFAULT 'unknown',
       last_health_check TEXT,
       memory_usage INTEGER DEFAULT 0,
       cpu_usage REAL DEFAULT 0.0,
       created_at TEXT NOT NULL,
       updated_at TEXT NOT NULL
     );
     
     -- MCP Analytics Table
     CREATE TABLE IF NOT EXISTS mcp_analytics (
       id INTEGER PRIMARY KEY AUTOINCREMENT,
       server_id INTEGER NOT NULL,
       metric_type TEXT NOT NULL, -- 'tool_usage', 'resource_access', 'performance'
       metric_value REAL NOT NULL,
       metadata TEXT, -- JSON
       timestamp TEXT NOT NULL,
       FOREIGN KEY (server_id) REFERENCES enhanced_mcp_servers(id)
     );
     
     -- MCP Memory Management Table
     CREATE TABLE IF NOT EXISTS mcp_memory_entries (
       id INTEGER PRIMARY KEY AUTOINCREMENT,
       server_id INTEGER NOT NULL,
       key TEXT NOT NULL,
       value TEXT NOT NULL,
       expires_at TEXT,
       created_at TEXT NOT NULL,
       accessed_at TEXT NOT NULL,
       access_count INTEGER DEFAULT 0,
       FOREIGN KEY (server_id) REFERENCES enhanced_mcp_servers(id)
     );
     
     -- MCP Knowledge Graph Tables
     CREATE TABLE IF NOT EXISTS mcp_knowledge_nodes (
       id INTEGER PRIMARY KEY AUTOINCREMENT,
       server_id INTEGER NOT NULL,
       node_id TEXT NOT NULL UNIQUE,
       node_type TEXT NOT NULL,
       properties TEXT NOT NULL, -- JSON
       created_at TEXT NOT NULL,
       updated_at TEXT NOT NULL,
       FOREIGN KEY (server_id) REFERENCES enhanced_mcp_servers(id)
     );
     
     CREATE TABLE IF NOT EXISTS mcp_knowledge_relationships (
       id INTEGER PRIMARY KEY AUTOINCREMENT,
       server_id INTEGER NOT NULL,
       from_node_id TEXT NOT NULL,
       to_node_id TEXT NOT NULL,
       relationship_type TEXT NOT NULL,
       properties TEXT, -- JSON
       created_at TEXT NOT NULL,
       FOREIGN KEY (server_id) REFERENCES enhanced_mcp_servers(id)
     );
     
     -- MCP Test Results Table
     CREATE TABLE IF NOT EXISTS mcp_test_results (
       id INTEGER PRIMARY KEY AUTOINCREMENT,
       server_id INTEGER NOT NULL,
       test_type TEXT NOT NULL, -- 'unit', 'integration', 'performance'
       test_name TEXT NOT NULL,
       status TEXT NOT NULL, -- 'passed', 'failed', 'skipped'
       execution_time REAL,
       error_message TEXT,
       coverage_percentage REAL,
       created_at TEXT NOT NULL,
       FOREIGN KEY (server_id) REFERENCES enhanced_mcp_servers(id)
     );
     
     -- MCP Performance Snapshots Table
     CREATE TABLE IF NOT EXISTS mcp_performance_snapshots (
       id INTEGER PRIMARY KEY AUTOINCREMENT,
       server_id INTEGER NOT NULL,
       cpu_usage REAL NOT NULL,
       memory_usage INTEGER NOT NULL,
       response_time_avg REAL,
       success_rate REAL,
       active_connections INTEGER,
       snapshot_time TEXT NOT NULL,
       FOREIGN KEY (server_id) REFERENCES enhanced_mcp_servers(id)
     );
     
     -- Enhanced MCP Indexes
     CREATE INDEX IF NOT EXISTS idx_enhanced_mcp_servers_status ON enhanced_mcp_servers(status);
     CREATE INDEX IF NOT EXISTS idx_enhanced_mcp_servers_type ON enhanced_mcp_servers(server_type);
     CREATE INDEX IF NOT EXISTS idx_mcp_analytics_server_metric ON mcp_analytics(server_id, metric_type);
     CREATE INDEX IF NOT EXISTS idx_mcp_analytics_timestamp ON mcp_analytics(timestamp);
     CREATE INDEX IF NOT EXISTS idx_mcp_memory_server_key ON mcp_memory_entries(server_id, key);
     CREATE INDEX IF NOT EXISTS idx_mcp_memory_expires ON mcp_memory_entries(expires_at);
     CREATE INDEX IF NOT EXISTS idx_mcp_knowledge_nodes_server ON mcp_knowledge_nodes(server_id);
     CREATE INDEX IF NOT EXISTS idx_mcp_knowledge_relationships_server ON mcp_knowledge_relationships(server_id);
     CREATE INDEX IF NOT EXISTS idx_mcp_test_results_server ON mcp_test_results(server_id);
     CREATE INDEX IF NOT EXISTS idx_mcp_performance_snapshots_server ON mcp_performance_snapshots(server_id, snapshot_time);
    "
  )?;
  
  // Phase 5: Git Hooks Tables
  conn.execute(
    "CREATE TABLE IF NOT EXISTS git_hooks (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      repository_path TEXT NOT NULL,
      hook_type TEXT NOT NULL,
      script_content TEXT NOT NULL,
      is_active BOOLEAN NOT NULL DEFAULT 1,
      configuration TEXT NOT NULL, -- JSON configuration
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      UNIQUE(repository_path, hook_type)
    )",
    [],
  )?;

  conn.execute(
    "CREATE TABLE IF NOT EXISTS git_hook_templates (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      description TEXT,
      hook_type TEXT NOT NULL,
      category TEXT NOT NULL,
      tags TEXT, -- JSON array
      script_template TEXT NOT NULL,
      default_config TEXT NOT NULL, -- JSON configuration
      variables TEXT, -- JSON object
      examples TEXT, -- JSON array
      is_builtin BOOLEAN NOT NULL DEFAULT 0,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )",
    [],
  )?;

  conn.execute(
    "CREATE TABLE IF NOT EXISTS git_hook_executions (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      hook_id INTEGER NOT NULL,
      repository_path TEXT NOT NULL,
      hook_type TEXT NOT NULL,
      git_context TEXT NOT NULL, -- JSON context
      status TEXT NOT NULL,
      output TEXT,
      error_message TEXT,
      duration_ms INTEGER,
      started_at TIMESTAMP NOT NULL,
      completed_at TIMESTAMP,
      FOREIGN KEY (hook_id) REFERENCES git_hooks(id) ON DELETE CASCADE
    )",
    [],
  )?;

  // Phase 4: Workflow Automation Tables
  conn.execute(
    "CREATE TABLE IF NOT EXISTS workflows (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      description TEXT,
      category TEXT NOT NULL,
      trigger_config TEXT, -- JSON configuration for triggers
      steps TEXT NOT NULL, -- JSON array of workflow steps
      variables TEXT, -- JSON object of workflow variables
      is_active BOOLEAN DEFAULT 1,
      is_template BOOLEAN DEFAULT 0,
      created_by TEXT,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )",
    [],
  )?;
  
  conn.execute(
    "CREATE TABLE IF NOT EXISTS workflow_executions (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      workflow_id INTEGER NOT NULL,
      trigger_data TEXT, -- JSON data that triggered the workflow
      status TEXT NOT NULL, -- 'running', 'completed', 'failed', 'cancelled'
      current_step INTEGER DEFAULT 0,
      started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      completed_at TIMESTAMP,
      error_message TEXT,
      execution_log TEXT, -- JSON array of execution steps
      FOREIGN KEY (workflow_id) REFERENCES workflows(id)
    )",
    [],
  )?;
  
  conn.execute(
    "CREATE TABLE IF NOT EXISTS workflow_templates (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL UNIQUE,
      description TEXT,
      category TEXT NOT NULL,
      icon TEXT,
      template_data TEXT NOT NULL, -- JSON workflow definition
      variables_schema TEXT, -- JSON schema for template variables
      usage_count INTEGER DEFAULT 0,
      author TEXT,
      version TEXT,
      tags TEXT, -- Comma-separated tags
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )",
    [],
  )?;
  
  conn.execute(
    "CREATE TABLE IF NOT EXISTS workflow_triggers (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      workflow_id INTEGER NOT NULL,
      trigger_type TEXT NOT NULL, -- 'schedule', 'event', 'webhook', 'manual'
      trigger_config TEXT NOT NULL, -- JSON configuration
      is_active BOOLEAN DEFAULT 1,
      last_triggered TIMESTAMP,
      next_trigger TIMESTAMP,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (workflow_id) REFERENCES workflows(id)
    )",
    [],
  )?;
  
  conn.execute(
    "CREATE TABLE IF NOT EXISTS workflow_step_logs (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      execution_id INTEGER NOT NULL,
      step_index INTEGER NOT NULL,
      step_name TEXT NOT NULL,
      status TEXT NOT NULL, -- 'started', 'completed', 'failed', 'skipped'
      input_data TEXT, -- JSON input
      output_data TEXT, -- JSON output
      error_message TEXT,
      started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      completed_at TIMESTAMP,
      duration_ms INTEGER,
      FOREIGN KEY (execution_id) REFERENCES workflow_executions(id)
    )",
    [],
  )?;
  
  // Create indexes for workflow tables
  conn.execute_batch(
    "CREATE INDEX IF NOT EXISTS idx_workflows_active ON workflows(is_active);
     CREATE INDEX IF NOT EXISTS idx_workflows_category ON workflows(category);
     CREATE INDEX IF NOT EXISTS idx_workflow_executions_workflow ON workflow_executions(workflow_id, status);
     CREATE INDEX IF NOT EXISTS idx_workflow_triggers_workflow ON workflow_triggers(workflow_id, is_active);
     CREATE INDEX IF NOT EXISTS idx_workflow_step_logs_execution ON workflow_step_logs(execution_id, step_index);"
  )?;
  
  // Phase 6: Agent Marketplace Tables
  conn.execute(
    "CREATE TABLE IF NOT EXISTS agent_marketplace (
       id INTEGER PRIMARY KEY AUTOINCREMENT,
       name TEXT NOT NULL,
       description TEXT,
       author TEXT NOT NULL,
       version TEXT NOT NULL,
       category TEXT NOT NULL,
       tags TEXT, -- JSON array
       config_schema TEXT, -- JSON schema
       agent_data TEXT NOT NULL, -- JSON agent configuration
       download_count INTEGER DEFAULT 0,
       rating REAL DEFAULT 0.0,
       rating_count INTEGER DEFAULT 0,
       is_verified BOOLEAN DEFAULT 0,
       created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
     )",
    [],
  )?;
  
  conn.execute(
    "CREATE TABLE IF NOT EXISTS agent_installations (
       id INTEGER PRIMARY KEY AUTOINCREMENT,
       agent_id INTEGER NOT NULL,
       installed_version TEXT NOT NULL,
       config_overrides TEXT, -- JSON
       is_active BOOLEAN DEFAULT 1,
       installed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       FOREIGN KEY (agent_id) REFERENCES agent_marketplace(id)
     )",
    [],
  )?;
  
  conn.execute(
    "CREATE TABLE IF NOT EXISTS agent_reviews (
       id INTEGER PRIMARY KEY AUTOINCREMENT,
       agent_id INTEGER NOT NULL,
       user_id TEXT NOT NULL,
       rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
       review_text TEXT,
       created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       FOREIGN KEY (agent_id) REFERENCES agent_marketplace(id)
     )",
    [],
  )?;
  
  // Agent Collaboration Tables
  conn.execute(
    "CREATE TABLE IF NOT EXISTS agent_collaborations (
       id INTEGER PRIMARY KEY AUTOINCREMENT,
       name TEXT NOT NULL,
       description TEXT,
       coordinator_agent_id INTEGER NOT NULL,
       participant_agent_ids TEXT NOT NULL, -- JSON array
       collaboration_type TEXT NOT NULL, -- sequential, parallel, hierarchical
       shared_context TEXT, -- JSON shared data
       status TEXT NOT NULL DEFAULT 'inactive',
       created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       FOREIGN KEY (coordinator_agent_id) REFERENCES agents(id)
     )",
    [],
  )?;
  
  conn.execute(
    "CREATE TABLE IF NOT EXISTS agent_messages (
       id INTEGER PRIMARY KEY AUTOINCREMENT,
       collaboration_id INTEGER NOT NULL,
       from_agent_id INTEGER NOT NULL,
       to_agent_id INTEGER,
       message_type TEXT NOT NULL, -- task_request, task_result, resource_request, status_update
       message_content TEXT NOT NULL, -- JSON
       timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       FOREIGN KEY (collaboration_id) REFERENCES agent_collaborations(id),
       FOREIGN KEY (from_agent_id) REFERENCES agents(id),
       FOREIGN KEY (to_agent_id) REFERENCES agents(id)
     )",
    [],
  )?;
  
  conn.execute(
    "CREATE TABLE IF NOT EXISTS agent_task_queue (
       id INTEGER PRIMARY KEY AUTOINCREMENT,
       collaboration_id INTEGER NOT NULL,
       agent_id INTEGER NOT NULL,
       task_type TEXT NOT NULL,
       task_data TEXT NOT NULL, -- JSON
       priority INTEGER DEFAULT 0,
       status TEXT NOT NULL DEFAULT 'pending',
       assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       started_at TIMESTAMP,
       completed_at TIMESTAMP,
       result_data TEXT, -- JSON
       FOREIGN KEY (collaboration_id) REFERENCES agent_collaborations(id),
       FOREIGN KEY (agent_id) REFERENCES agents(id)
     )",
    [],
  )?;
  
  // Create unified workflows table
  conn.execute(
    "CREATE TABLE IF NOT EXISTS unified_workflows (
      workflow_id TEXT PRIMARY KEY,
      data TEXT NOT NULL,
      created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
      updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
    )",
    [],
  )?;
  
  // Create indexes for agent marketplace
  conn.execute_batch(
    "CREATE INDEX IF NOT EXISTS idx_agent_marketplace_category ON agent_marketplace(category);
     CREATE INDEX IF NOT EXISTS idx_agent_marketplace_author ON agent_marketplace(author);
     CREATE INDEX IF NOT EXISTS idx_agent_marketplace_verified ON agent_marketplace(is_verified);
     CREATE INDEX IF NOT EXISTS idx_agent_installations_agent ON agent_installations(agent_id);
     CREATE INDEX IF NOT EXISTS idx_agent_reviews_agent ON agent_reviews(agent_id);
     CREATE INDEX IF NOT EXISTS idx_agent_collaborations_status ON agent_collaborations(status);
     CREATE INDEX IF NOT EXISTS idx_agent_messages_collaboration ON agent_messages(collaboration_id);
     CREATE INDEX IF NOT EXISTS idx_agent_task_queue_collaboration ON agent_task_queue(collaboration_id, status);
     CREATE INDEX IF NOT EXISTS idx_unified_workflows_updated ON unified_workflows(updated_at);"
  )?;
  
  // Initialize budget extension tables
  crate::db_budget_extensions::init_budget_extensions(&conn)?;
  
  // Initialize content processing and vector storage extensions
  crate::db_content_extensions::init_content_extensions(&conn)?;
  
  // Run content database migrations
  crate::db_content_extensions::run_content_migrations(&conn)?;
  
  // Initialize Lightbulb tables
  db_lightbulb_init::init_lightbulb_tables(&conn)?;
  
  *CONN.lock().unwrap() = Some(conn);
  Ok(())
}

pub fn get_connection() -> Result<Connection> {
  let db_path = match DB_PATH.lock() {
    Ok(guard) => guard,
    Err(_) => return Err(anyhow!("Failed to lock database path"))
  };
  
  match db_path.as_ref() {
    Some(path) => {
      match Connection::open(path) {
        Ok(conn) => {
          // Set WAL mode for better concurrency
          match conn.execute_batch("PRAGMA journal_mode=WAL;") {
            Ok(_) => Ok(conn),
            Err(e) => Err(anyhow!("Failed to set WAL mode: {}", e))
          }
        }
        Err(e) => Err(anyhow!("Failed to open database: {}", e))
      }
    }
    None => Err(anyhow!("Database not initialized"))
  }
}

pub fn conn() -> Result<Connection> {
  // Always use the stored path to open a new connection
  let db_path_guard = DB_PATH.lock().unwrap();
  if let Some(db_path) = db_path_guard.as_ref() {
    return Ok(Connection::open(db_path)?);
  }
  Err(anyhow!("DB not initialized"))
}



#[allow(dead_code)]
pub fn get_budget(year: i64) -> Result<Budget> {
  let c = conn()?;
  let now = Utc::now().to_rfc3339();
  c.execute("INSERT OR IGNORE INTO budgets(year,total_amount,spent_amount,created_at,updated_at) VALUES(?,0,0,?,?)", params![year, now, now])?;
  Ok(c.query_row(
    "SELECT id, year, total_amount, spent_amount, created_at, updated_at FROM budgets WHERE year=?",
    params![year],
    |row| Ok(Budget{ id: row.get(0)?, year: row.get(1)?, total_amount: row.get(2)?, spent_amount: row.get(3)?, created_at: row.get(4)?, updated_at: row.get(5)? })
  )?)
}

#[allow(dead_code)]
pub fn set_budget(year: i64, total: f64) -> Result<Budget> {
  let c = conn()?;
  let now = Utc::now().to_rfc3339();
  c.execute("INSERT INTO budgets(year,total_amount,spent_amount,created_at,updated_at) VALUES(?,?,?,?,?) ON CONFLICT(year) DO UPDATE SET total_amount=excluded.total_amount, updated_at=excluded.updated_at",
    params![year, total, 0f64, now.clone(), now.clone()])?;
  
  // Recalculate spent amount from existing expenses
  recalculate_spent(year)?;
  
  get_budget(year)
}

#[allow(dead_code)]
pub fn list_requests(filter: RequestFilter) -> Result<Vec<RequestRow>> {
  let c = conn()?;
  let mut sql = String::from("SELECT id,title,description,category,amount,status,year,comment,created_at,updated_at FROM requests WHERE 1=1");
  let mut args: Vec<rusqlite::types::Value> = Vec::new();
  if let Some(s) = filter.status { sql.push_str(" AND status = ?"); args.push(s.into()); }
  if let Some(cat) = filter.category { sql.push_str(" AND category = ?"); args.push(cat.into()); }
  if let Some(y) = filter.year { sql.push_str(" AND year = ?"); args.push(y.into()); }
  sql.push_str(" ORDER BY created_at DESC");
  let mut stmt = c.prepare(&sql)?;
  let rows = stmt.query_map(rusqlite::params_from_iter(args.iter()), |row| Ok(RequestRow{ id: row.get(0)?, title: row.get(1)?, description: row.get(2)?, category: row.get(3)?, amount: row.get(4)?, status: row.get(5)?, year: row.get(6)?, comment: row.get(7)?, created_at: row.get(8)?, updated_at: row.get(9)? }))?;
  Ok(rows.filter_map(Result::ok).collect())
}

#[allow(dead_code)]
pub fn create_request(n: NewRequest) -> Result<RequestRow> {
  let c = conn()?;
  let now = Utc::now().to_rfc3339();
  c.execute("INSERT INTO requests(title,description,category,amount,status,year,comment,created_at,updated_at) VALUES(?,?,?,?, 'pending', ?, NULL, ?, ?)", params![n.title, n.description, n.category, n.amount, n.year, now, now])?;
  let id = c.last_insert_rowid();
  load_request(id)
}

#[allow(dead_code)]
fn load_request(id: i64) -> Result<RequestRow> {
  let c = conn()?;
  Ok(c.query_row("SELECT id,title,description,category,amount,status,year,comment,created_at,updated_at FROM requests WHERE id=?", params![id], |row| Ok(RequestRow{ id: row.get(0)?, title: row.get(1)?, description: row.get(2)?, category: row.get(3)?, amount: row.get(4)?, status: row.get(5)?, year: row.get(6)?, comment: row.get(7)?, created_at: row.get(8)?, updated_at: row.get(9)? }))?)
}

fn adjust_spent(year: i64, delta: f64) -> Result<()> {
  let c = conn()?;
  let now = Utc::now().to_rfc3339();
  c.execute("INSERT OR IGNORE INTO budgets(year,total_amount,spent_amount,created_at,updated_at) VALUES(?,0,0,?,?)", params![year, now, now])?;
  c.execute("UPDATE budgets SET spent_amount = MAX(0, spent_amount + ?), updated_at=? WHERE year=?", params![delta, now, year])?;
  Ok(())
}

// Recalculate the total spent amount from all paid/reimbursed expenses
fn recalculate_spent(year: i64) -> Result<()> {
  let c = conn()?;
  let now = Utc::now().to_rfc3339();
  
  // Calculate total from paid and reimbursed expenses
  let total_spent: f64 = c.query_row(
    "SELECT COALESCE(SUM(amount), 0) FROM expenses WHERE year=? AND (status='paid' OR status='reimbursed')",
    params![year],
    |r| r.get(0)
  )?;
  
  // Update the budget's spent_amount
  c.execute(
    "UPDATE budgets SET spent_amount = ?, updated_at = ? WHERE year = ?",
    params![total_spent, now, year]
  )?;
  
  Ok(())
}

#[allow(dead_code)]
pub fn update_request(id: i64, u: UpdateRequest) -> Result<RequestRow> {
  let c = conn()?;
  let prev = load_request(id)?;
  let mut new_status = u.status.clone().unwrap_or(prev.status.clone());
  let mut new_year = u.year.unwrap_or(prev.year);
  let mut new_amount = u.amount.unwrap_or(prev.amount);
  let now = Utc::now().to_rfc3339();
  let mut sql = String::from("UPDATE requests SET updated_at=?");
  let mut args: Vec<rusqlite::types::Value> = vec![now.clone().into()];
  if let Some(v) = u.title { sql.push_str(", title=?"); args.push(v.into()); }
  if let Some(v) = u.description { sql.push_str(", description=?"); args.push(v.into()); }
  if let Some(v) = u.category { sql.push_str(", category=?"); args.push(v.into()); }
  if let Some(v) = u.amount { sql.push_str(", amount=?"); args.push(v.into()); new_amount = v; }
  if let Some(v) = u.year { sql.push_str(", year=?"); args.push(v.into()); new_year = v; }
  if let Some(v) = u.status { sql.push_str(", status=?"); args.push(v.clone().into()); new_status = v; }
  if let Some(v) = u.comment { sql.push_str(", comment=?"); args.push(v.into()); }
  sql.push_str(" WHERE id=?");
  args.push(id.into());
  c.execute(&sql, rusqlite::params_from_iter(args.iter()))?;
  if prev.status == "approved" {
    if new_status != "approved" { adjust_spent(prev.year, -prev.amount)?; }
    else if new_status == "approved" && (new_year != prev.year || (new_amount - prev.amount).abs() > f64::EPSILON) {
      if new_year != prev.year { adjust_spent(prev.year, -prev.amount)?; adjust_spent(new_year, new_amount)?; }
      else { adjust_spent(new_year, new_amount - prev.amount)?; }
    }
  } else if new_status == "approved" {
    adjust_spent(new_year, new_amount)?;
  }
  load_request(id)
}

#[allow(dead_code)]
pub fn delete_request(id: i64) -> Result<bool> {
  let c = conn()?;
  // Use query_row with optional() extension
  let prev: Option<RequestRow> = c.query_row(
    "SELECT id,title,description,category,amount,status,year,comment,created_at,updated_at FROM requests WHERE id=?", 
    params![id], 
    |row| Ok(RequestRow{ id: row.get(0)?, title: row.get(1)?, description: row.get(2)?, category: row.get(3)?, amount: row.get(4)?, status: row.get(5)?, year: row.get(6)?, comment: row.get(7)?, created_at: row.get(8)?, updated_at: row.get(9)? })
  ).optional()?;
  if let Some(r) = prev { if r.status == "approved" { adjust_spent(r.year, -r.amount)?; } }
  c.execute("DELETE FROM requests WHERE id=?", params![id])?;
  Ok(true)
}

#[allow(dead_code)]
pub fn approve_request(id: i64, comment: Option<String>) -> Result<RequestRow> {
  let req = load_request(id)?;
  if req.status != "approved" { adjust_spent(req.year, req.amount)?; }
  let c = conn()?;
  let now = Utc::now().to_rfc3339();
  c.execute("UPDATE requests SET status='approved', comment=?, updated_at=? WHERE id=?", params![comment, now, id])?;
  load_request(id)
}

#[allow(dead_code)]
pub fn reject_request(id: i64, comment: Option<String>) -> Result<RequestRow> {
  let req = load_request(id)?;
  if req.status == "approved" { adjust_spent(req.year, -req.amount)?; }
  let c = conn()?;
  let now = Utc::now().to_rfc3339();
  c.execute("UPDATE requests SET status='rejected', comment=?, updated_at=? WHERE id=?", params![comment, now, id])?;
  load_request(id)
}

#[allow(dead_code)]
pub fn get_stats(year: i64) -> Result<Stats> {
  // First, recalculate the spent amount to ensure it's accurate
  recalculate_spent(year)?;
  
  let b = get_budget(year)?;
  let c = conn()?;
  
  // Count expenses by status
  let count_pending: i64 = c.query_row("SELECT COUNT(*) FROM expenses WHERE year=? AND (status='draft' OR status='submitted')", params![year], |r| r.get(0)).unwrap_or(0);
  let count_approved: i64 = c.query_row("SELECT COUNT(*) FROM expenses WHERE year=? AND (status='paid' OR status='reimbursed')", params![year], |r| r.get(0)).unwrap_or(0);
  let count_rejected: i64 = c.query_row("SELECT COUNT(*) FROM expenses WHERE year=? AND status='rejected'", params![year], |r| r.get(0)).unwrap_or(0);
  
  // Get category totals from paid/reimbursed expenses
  let mut stmt = c.prepare("SELECT category, SUM(amount) as total FROM expenses WHERE year=? AND (status='paid' OR status='reimbursed') GROUP BY category")?;
  let rows = stmt.query_map(params![year], |row| Ok(CategoryTotal{ category: row.get(0)?, total: row.get(1)? }))?;
  let by_category: Vec<CategoryTotal> = rows.filter_map(Result::ok).collect();
  
  Ok(Stats{ year, total: b.total_amount, spent: b.spent_amount, remaining: (b.total_amount - b.spent_amount).max(0.0), count_pending, count_approved, count_rejected, by_category })
}
// Training Needs
#[allow(dead_code)]
pub fn list_training_needs() -> Result<Vec<TrainingNeedRow>> {
    let c = conn()?;
    let mut stmt = c.prepare("SELECT id, employee_id, employee_name, department, current_position, target_position, skill_gap, priority, training_type, recommended_training, estimated_cost, expected_roi, completion_timeline, business_impact, created_at, updated_at FROM training_needs ORDER BY created_at DESC")?;
    let rows = stmt.query_map([], |row| Ok(TrainingNeedRow {
        id: row.get(0)?,
        employee_id: row.get(1)?,
        employee_name: row.get(2)?,
        department: row.get(3)?,
        current_position: row.get(4)?,
        target_position: row.get(5)?,
        skill_gap: row.get(6)?,
        priority: row.get(7)?,
        training_type: row.get(8)?,
        recommended_training: row.get(9)?,
        estimated_cost: row.get(10)?,
        expected_roi: row.get(11)?,
        completion_timeline: row.get(12)?,
        business_impact: row.get(13)?,
        created_at: row.get(14)?,
        updated_at: row.get(15)?,
    }))?;
    Ok(rows.filter_map(Result::ok).collect())
}

#[allow(dead_code)]
pub fn create_training_need(n: NewTrainingNeed) -> Result<TrainingNeedRow> {
    let c = conn()?;
    let now = Utc::now().to_rfc3339();
    c.execute(
        "INSERT INTO training_needs (employee_id, employee_name, department, current_position, target_position, skill_gap, priority, training_type, recommended_training, estimated_cost, expected_roi, completion_timeline, business_impact, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
        params![
            n.employee_id,
            n.employee_name,
            n.department,
            n.current_position,
            n.target_position,
            n.skill_gap,
            n.priority,
            n.training_type,
            n.recommended_training,
            n.estimated_cost,
            n.expected_roi,
            n.completion_timeline,
            n.business_impact,
            now,
            now,
        ],
    )?;
    let id = c.last_insert_rowid();
    load_training_need(id)
}

#[allow(dead_code)]
pub fn update_training_need(id: i64, u: UpdateTrainingNeed) -> Result<TrainingNeedRow> {
    let c = conn()?;
    let now = Utc::now().to_rfc3339();
    let mut sql = String::from("UPDATE training_needs SET updated_at=?");
    let mut args: Vec<rusqlite::types::Value> = vec![now.clone().into()];
    if let Some(v) = u.employee_id { sql.push_str(", employee_id=?"); args.push(v.into()); }
    if let Some(v) = u.employee_name { sql.push_str(", employee_name=?"); args.push(v.into()); }
    if let Some(v) = u.department { sql.push_str(", department=?"); args.push(v.into()); }
    if let Some(v) = u.current_position { sql.push_str(", current_position=?"); args.push(v.into()); }
    if let Some(v) = u.target_position { sql.push_str(", target_position=?"); args.push(v.into()); }
    if let Some(v) = u.skill_gap { sql.push_str(", skill_gap=?"); args.push(v.into()); }
    if let Some(v) = u.priority { sql.push_str(", priority=?"); args.push(v.into()); }
    if let Some(v) = u.training_type { sql.push_str(", training_type=?"); args.push(v.into()); }
    if let Some(v) = u.recommended_training { sql.push_str(", recommended_training=?"); args.push(v.into()); }
    if let Some(v) = u.estimated_cost { sql.push_str(", estimated_cost=?"); args.push(v.into()); }
    if let Some(v) = u.expected_roi { sql.push_str(", expected_roi=?"); args.push(v.into()); }
    if let Some(v) = u.completion_timeline { sql.push_str(", completion_timeline=?"); args.push(v.into()); }
    if let Some(v) = u.business_impact { sql.push_str(", business_impact=?"); args.push(v.into()); }
    sql.push_str(" WHERE id=?");
    args.push(id.into());
    c.execute(&sql, rusqlite::params_from_iter(args.iter()))?;
    load_training_need(id)
}

#[allow(dead_code)]
pub fn delete_training_need(id: i64) -> Result<bool> {
    let c = conn()?;
    c.execute("DELETE FROM training_needs WHERE id=?", params![id])?;
    Ok(true)
}

#[allow(dead_code)]
fn load_training_need(id: i64) -> Result<TrainingNeedRow> {
    let c = conn()?;
    Ok(c.query_row(
        "SELECT id, employee_id, employee_name, department, current_position, target_position, skill_gap, priority, training_type, recommended_training, estimated_cost, expected_roi, completion_timeline, business_impact, created_at, updated_at FROM training_needs WHERE id=?",
        params![id],
        |row| Ok(TrainingNeedRow {
            id: row.get(0)?,
            employee_id: row.get(1)?,
            employee_name: row.get(2)?,
            department: row.get(3)?,
            current_position: row.get(4)?,
            target_position: row.get(5)?,
            skill_gap: row.get(6)?,
            priority: row.get(7)?,
            training_type: row.get(8)?,
            recommended_training: row.get(9)?,
            estimated_cost: row.get(10)?,
            expected_roi: row.get(11)?,
            completion_timeline: row.get(12)?,
            business_impact: row.get(13)?,
            created_at: row.get(14)?,
            updated_at: row.get(15)?,
        })
    )?)
}

// Training Programs
#[allow(dead_code)]
pub fn list_training_programs() -> Result<Vec<TrainingProgramRow>> {
    let c = conn()?;
    let mut stmt = c.prepare("SELECT id, name, description, training_type, duration, cost_per_person, provider, completion_rate, effectiveness_score, created_at, updated_at FROM training_programs ORDER BY created_at DESC")?;
    let rows = stmt.query_map([], |row| Ok(TrainingProgramRow {
        id: row.get(0)?,
        name: row.get(1)?,
        description: row.get(2)?,
        training_type: row.get(3)?,
        duration: row.get(4)?,
        cost_per_person: row.get(5)?,
        provider: row.get(6)?,
        completion_rate: row.get(7)?,
        effectiveness_score: row.get(8)?,
        created_at: row.get(9)?,
        updated_at: row.get(10)?,
    }))?;
    Ok(rows.filter_map(Result::ok).collect())
}

#[allow(dead_code)]
pub fn create_training_program(n: NewTrainingProgram) -> Result<TrainingProgramRow> {
    let c = conn()?;
    let now = Utc::now().to_rfc3339();
    c.execute(
        "INSERT INTO training_programs (name, description, training_type, duration, cost_per_person, provider, completion_rate, effectiveness_score, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
        params![
            n.name,
            n.description,
            n.training_type,
            n.duration,
            n.cost_per_person,
            n.provider,
            n.completion_rate,
            n.effectiveness_score,
            now,
            now,
        ],
    )?;
    let id = c.last_insert_rowid();
    load_training_program(id)
}

#[allow(dead_code)]
pub fn update_training_program(id: i64, u: UpdateTrainingProgram) -> Result<TrainingProgramRow> {
    let c = conn()?;
    let now = Utc::now().to_rfc3339();
    let mut sql = String::from("UPDATE training_programs SET updated_at=?");
    let mut args: Vec<rusqlite::types::Value> = vec![now.clone().into()];
    if let Some(v) = u.name { sql.push_str(", name=?"); args.push(v.into()); }
    if let Some(v) = u.description { sql.push_str(", description=?"); args.push(v.into()); }
    if let Some(v) = u.training_type { sql.push_str(", training_type=?"); args.push(v.into()); }
    if let Some(v) = u.duration { sql.push_str(", duration=?"); args.push(v.into()); }
    if let Some(v) = u.cost_per_person { sql.push_str(", cost_per_person=?"); args.push(v.into()); }
    if let Some(v) = u.provider { sql.push_str(", provider=?"); args.push(v.into()); }
    if let Some(v) = u.completion_rate { sql.push_str(", completion_rate=?"); args.push(v.into()); }
    if let Some(v) = u.effectiveness_score { sql.push_str(", effectiveness_score=?"); args.push(v.into()); }
    sql.push_str(" WHERE id=?");
    args.push(id.into());
    c.execute(&sql, rusqlite::params_from_iter(args.iter()))?;
    load_training_program(id)
}

#[allow(dead_code)]
pub fn delete_training_program(id: i64) -> Result<bool> {
    let c = conn()?;
    c.execute("DELETE FROM training_programs WHERE id=?", params![id])?;
    Ok(true)
}

#[allow(dead_code)]
fn load_training_program(id: i64) -> Result<TrainingProgramRow> {
    let c = conn()?;
    Ok(c.query_row(
        "SELECT id, name, description, training_type, duration, cost_per_person, provider, completion_rate, effectiveness_score, created_at, updated_at FROM training_programs WHERE id=?",
        params![id],
        |row| Ok(TrainingProgramRow {
            id: row.get(0)?,
            name: row.get(1)?,
            description: row.get(2)?,
            training_type: row.get(3)?,
            duration: row.get(4)?,
            cost_per_person: row.get(5)?,
            provider: row.get(6)?,
            completion_rate: row.get(7)?,
            effectiveness_score: row.get(8)?,
            created_at: row.get(9)?,
            updated_at: row.get(10)?,
        })
    )?)
}

// Training Analyses
#[allow(dead_code)]
pub fn list_training_analyses() -> Result<Vec<TrainingAnalysisRow>> {
    let c = conn()?;
    let mut stmt = c.prepare("SELECT id, name, description, total_budget, allocated_amount, remaining_budget, expected_roi, completion_rate, feasibility, created_at, updated_at FROM training_analyses ORDER BY created_at DESC")?;
    let rows = stmt.query_map([], |row| Ok(TrainingAnalysisRow {
        id: row.get(0)?,
        name: row.get(1)?,
        description: row.get(2)?,
        total_budget: row.get(3)?,
        allocated_amount: row.get(4)?,
        remaining_budget: row.get(5)?,
        expected_roi: row.get(6)?,
        completion_rate: row.get(7)?,
        feasibility: row.get(8)?,
        created_at: row.get(9)?,
        updated_at: row.get(10)?,
    }))?;
    Ok(rows.filter_map(Result::ok).collect())
}

#[allow(dead_code)]
pub fn create_training_analysis(n: NewTrainingAnalysis) -> Result<TrainingAnalysisRow> {
    let c = conn()?;
    let now = Utc::now().to_rfc3339();
    c.execute(
        "INSERT INTO training_analyses (name, description, total_budget, allocated_amount, remaining_budget, expected_roi, completion_rate, feasibility, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
        params![
            n.name,
            n.description,
            n.total_budget,
            n.allocated_amount,
            n.remaining_budget,
            n.expected_roi,
            n.completion_rate,
            n.feasibility,
            now,
            now,
        ],
    )?;
    let id = c.last_insert_rowid();
    load_training_analysis(id)
}

#[allow(dead_code)]
pub fn update_training_analysis(id: i64, u: UpdateTrainingAnalysis) -> Result<TrainingAnalysisRow> {
    let c = conn()?;
    let now = Utc::now().to_rfc3339();
    let mut sql = String::from("UPDATE training_analyses SET updated_at=?");
    let mut args: Vec<rusqlite::types::Value> = vec![now.clone().into()];
    if let Some(v) = u.name { sql.push_str(", name=?"); args.push(v.into()); }
    if let Some(v) = u.description { sql.push_str(", description=?"); args.push(v.into()); }
    if let Some(v) = u.total_budget { sql.push_str(", total_budget=?"); args.push(v.into()); }
    if let Some(v) = u.allocated_amount { sql.push_str(", allocated_amount=?"); args.push(v.into()); }
    if let Some(v) = u.remaining_budget { sql.push_str(", remaining_budget=?"); args.push(v.into()); }
    if let Some(v) = u.expected_roi { sql.push_str(", expected_roi=?"); args.push(v.into()); }
    if let Some(v) = u.completion_rate { sql.push_str(", completion_rate=?"); args.push(v.into()); }
    if let Some(v) = u.feasibility { sql.push_str(", feasibility=?"); args.push(v.into()); }
    sql.push_str(" WHERE id=?");
    args.push(id.into());
    c.execute(&sql, rusqlite::params_from_iter(args.iter()))?;
    load_training_analysis(id)
}

#[allow(dead_code)]
pub fn delete_training_analysis(id: i64) -> Result<bool> {
    let c = conn()?;
    c.execute("DELETE FROM training_analyses WHERE id=?", params![id])?;
    Ok(true)
}

#[allow(dead_code)]
fn load_training_analysis(id: i64) -> Result<TrainingAnalysisRow> {
    let c = conn()?;
    Ok(c.query_row(
        "SELECT id, name, description, total_budget, allocated_amount, remaining_budget, expected_roi, completion_rate, feasibility, created_at, updated_at FROM training_analyses WHERE id=?",
        params![id],
        |row| Ok(TrainingAnalysisRow {
            id: row.get(0)?,
            name: row.get(1)?,
            description: row.get(2)?,
            total_budget: row.get(3)?,
            allocated_amount: row.get(4)?,
            remaining_budget: row.get(5)?,
            expected_roi: row.get(6)?,
            completion_rate: row.get(7)?,
            feasibility: row.get(8)?,
            created_at: row.get(9)?,
            updated_at: row.get(10)?,
        })
    )?)
}

// ============= Expense Functions =============

#[allow(dead_code)]
pub fn get_expenses(year: i64) -> Result<Vec<Expense>> {
    let conn = CONN.lock().unwrap();
    let conn = conn.as_ref().ok_or_else(|| anyhow!("Database not initialized"))?;
    
    let mut stmt = conn.prepare(
        "SELECT id, title, description, amount, category, department_id, status, date, 
         receipt_url, recurrence, recurrence_end_date, created_at, updated_at 
         FROM expenses WHERE year = ?1 ORDER BY date DESC"
    )?;
    
    let expenses = stmt.query_map(params![year], |row| {
        Ok(Expense {
            id: row.get(0)?,
            title: row.get(1)?,
            description: row.get(2)?,
            amount: row.get(3)?,
            category: row.get(4)?,
            department_id: row.get(5)?,
            status: row.get(6)?,
            date: row.get(7)?,
            receipt_url: row.get(8)?,
            recurrence: row.get(9)?,
            recurrence_end_date: row.get(10)?,
            created_at: row.get(11)?,
            updated_at: row.get(12)?,
        })
    })?
    .collect::<Result<Vec<_>, _>>()?;
    
    Ok(expenses)
}

#[allow(dead_code)]
pub fn add_expense(expense: Expense) -> Result<Expense> {
    let conn = CONN.lock().unwrap();
    let conn = conn.as_ref().ok_or_else(|| anyhow!("Database not initialized"))?;
    
    let now = Utc::now().to_rfc3339();
    let id = format!("exp-{}", Utc::now().timestamp_millis());
    let year = expense.date[0..4].parse::<i64>().unwrap_or(Utc::now().year() as i64);
    
    conn.execute(
        "INSERT INTO expenses (id, title, description, amount, category, department_id, 
         status, date, receipt_url, recurrence, recurrence_end_date, created_at, updated_at, year)
         VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10, ?11, ?12, ?13, ?14)",
        params![
            id,
            expense.title,
            expense.description,
            expense.amount,
            expense.category,
            expense.department_id,
            expense.status,
            expense.date,
            expense.receipt_url,
            expense.recurrence,
            expense.recurrence_end_date,
            now,
            now,
            year
        ],
    )?;
    
    // Update the budget's spent_amount for paid or reimbursed expenses
    if expense.status == "paid" || expense.status == "reimbursed" {
        // Ensure budget exists for this year
        conn.execute(
            "INSERT OR IGNORE INTO budgets(year,total_amount,spent_amount,created_at,updated_at) VALUES(?,0,0,?,?)", 
            params![year, &now, &now]
        )?;
        
        conn.execute(
            "UPDATE budgets SET spent_amount = spent_amount + ?, updated_at = ? WHERE year = ?",
            params![expense.amount, &now, year],
        )?;
    }
    
    Ok(Expense {
        id,
        title: expense.title,
        description: expense.description,
        amount: expense.amount,
        category: expense.category,
        department_id: expense.department_id,
        status: expense.status,
        date: expense.date,
        receipt_url: expense.receipt_url,
        recurrence: expense.recurrence,
        recurrence_end_date: expense.recurrence_end_date,
        created_at: now.clone(),
        updated_at: now,
    })
}

#[allow(dead_code)]
pub fn update_expense(id: String, updates: Expense) -> Result<Expense> {
    let conn = CONN.lock().unwrap();
    let conn = conn.as_ref().ok_or_else(|| anyhow!("Database not initialized"))?;
    
    let now = Utc::now().to_rfc3339();
    
    conn.execute(
        "UPDATE expenses SET title = ?1, description = ?2, amount = ?3, category = ?4, 
         department_id = ?5, status = ?6, date = ?7, receipt_url = ?8, recurrence = ?9, 
         recurrence_end_date = ?10, updated_at = ?11 WHERE id = ?12",
        params![
            updates.title,
            updates.description,
            updates.amount,
            updates.category,
            updates.department_id,
            updates.status,
            updates.date,
            updates.receipt_url,
            updates.recurrence,
            updates.recurrence_end_date,
            now,
            id
        ],
    )?;
    
    // Return updated expense
    let mut stmt = conn.prepare(
        "SELECT id, title, description, amount, category, department_id, status, date, 
         receipt_url, recurrence, recurrence_end_date, created_at, updated_at 
         FROM expenses WHERE id = ?1"
    )?;
    
    let expense = stmt.query_row(params![id], |row| {
        Ok(Expense {
            id: row.get(0)?,
            title: row.get(1)?,
            description: row.get(2)?,
            amount: row.get(3)?,
            category: row.get(4)?,
            department_id: row.get(5)?,
            status: row.get(6)?,
            date: row.get(7)?,
            receipt_url: row.get(8)?,
            recurrence: row.get(9)?,
            recurrence_end_date: row.get(10)?,
            created_at: row.get(11)?,
            updated_at: row.get(12)?,
        })
    })?;
    
    Ok(expense)
}

#[allow(dead_code)]
pub fn delete_expense(id: String) -> Result<bool> {
    let conn = CONN.lock().unwrap();
    let conn = conn.as_ref().ok_or_else(|| anyhow!("Database not initialized"))?;
    
    // Get the expense details before deleting
    let mut stmt = conn.prepare("SELECT amount, status, year FROM expenses WHERE id = ?1")?;
    let expense_data = stmt.query_row(params![&id], |row| {
        Ok((row.get::<_, f64>(0)?, row.get::<_, String>(1)?, row.get::<_, i64>(2)?))
    }).ok();
    
    let rows_affected = conn.execute("DELETE FROM expenses WHERE id = ?1", params![id])?;
    
    // If expense was paid or reimbursed, decrease the budget's spent_amount
    if let Some((amount, status, year)) = expense_data {
        if (status == "paid" || status == "reimbursed") && rows_affected > 0 {
            let now = Utc::now().to_rfc3339();
            conn.execute(
                "UPDATE budgets SET spent_amount = MAX(0, spent_amount - ?), updated_at = ? WHERE year = ?",
                params![amount, now, year],
            )?;
        }
    }
    
    Ok(rows_affected > 0)
}

#[allow(dead_code)]
pub fn update_expense_status(id: String, new_status: String) -> Result<Expense> {
    let conn = CONN.lock().unwrap();
    let conn = conn.as_ref().ok_or_else(|| anyhow!("Database not initialized"))?;
    
    let now = Utc::now().to_rfc3339();
    
    // Get the old status, amount, and year before updating
    let mut stmt = conn.prepare("SELECT status, amount, year FROM expenses WHERE id = ?1")?;
    let (old_status, amount, year): (String, f64, i64) = stmt.query_row(params![&id], |row| {
        Ok((row.get(0)?, row.get(1)?, row.get(2)?))
    })?;
    
    // Update the expense status
    conn.execute(
        "UPDATE expenses SET status = ?1, updated_at = ?2 WHERE id = ?3",
        params![&new_status, &now, &id],
    )?;
    
    // Update budget's spent_amount based on status change
    if old_status != new_status {
        let old_is_spent = old_status == "paid" || old_status == "reimbursed";
        let new_is_spent = new_status == "paid" || new_status == "reimbursed";
        
        if old_is_spent && !new_is_spent {
            // Was paid/reimbursed, now draft/submitted - decrease spent amount
            conn.execute(
                "UPDATE budgets SET spent_amount = MAX(0, spent_amount - ?), updated_at = ? WHERE year = ?",
                params![amount, &now, year],
            )?;
        } else if !old_is_spent && new_is_spent {
            // Was draft/submitted, now paid/reimbursed - increase spent amount
            conn.execute(
                "UPDATE budgets SET spent_amount = spent_amount + ?, updated_at = ? WHERE year = ?",
                params![amount, &now, year],
            )?;
        }
    }
    
    // Return updated expense
    let mut stmt = conn.prepare(
        "SELECT id, title, description, amount, category, department_id, status, date, 
         receipt_url, recurrence, recurrence_end_date, created_at, updated_at 
         FROM expenses WHERE id = ?1"
    )?;
    
    let expense = stmt.query_row(params![id], |row| {
        Ok(Expense {
            id: row.get(0)?,
            title: row.get(1)?,
            description: row.get(2)?,
            amount: row.get(3)?,
            category: row.get(4)?,
            department_id: row.get(5)?,
            status: row.get(6)?,
            date: row.get(7)?,
            receipt_url: row.get(8)?,
            recurrence: row.get(9)?,
            recurrence_end_date: row.get(10)?,
            created_at: row.get(11)?,
            updated_at: row.get(12)?,
        })
    })?;
    
    Ok(expense)
}