// Lighthouse LM Backend - Comprehensive Rust/Tauri Backend Services

use serde::{Deserialize, Serialize};
use tauri::State;
use std::sync::Arc;
use tokio::sync::RwLock;
use chrono::{DateTime, Utc};
use uuid::Uuid;
use std::collections::HashMap;
// use sqlx::{SqlitePool, migrate::MigrateDatabase}; // Temporarily disabled - needs refactoring to rusqlite

// ============= Data Models =============

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Notebook {
    pub id: String,
    pub title: String,
    pub description: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub owner_id: String,
    pub is_public: bool,
    pub tags: Vec<String>,
    pub metadata: HashMap<String, serde_json::Value>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Source {
    pub id: String,
    pub notebook_id: String,
    pub title: String,
    pub content: String,
    pub source_type: SourceType,
    pub url: Option<String>,
    pub file_path: Option<String>,
    pub metadata: SourceMetadata,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub processed: bool,
    pub embeddings: Option<Vec<f32>>,
    pub tags: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SourceType {
    PDF,
    Web,
    Document,
    Code,
    Media,
    Note,
    External,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SourceMetadata {
    pub size: Option<i64>,
    pub page_count: Option<i32>,
    pub word_count: Option<i32>,
    pub language: Option<String>,
    pub author: Option<String>,
    pub published_date: Option<DateTime<Utc>>,
    pub last_accessed: Option<DateTime<Utc>>,
    pub extraction_method: Option<String>,
    pub confidence_score: Option<f32>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChatMessage {
    pub id: String,
    pub notebook_id: String,
    pub role: MessageRole,
    pub content: String,
    pub model: Option<String>,
    pub temperature: Option<f32>,
    pub max_tokens: Option<i32>,
    pub sources: Vec<String>,
    pub citations: Vec<Citation>,
    pub metadata: MessageMetadata,
    pub parent_id: Option<String>, // For threading
    pub created_at: DateTime<Utc>,
    pub edited_at: Option<DateTime<Utc>>,
    pub reactions: Vec<Reaction>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum MessageRole {
    User,
    Assistant,
    System,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MessageMetadata {
    pub tokens_used: Option<i32>,
    pub processing_time_ms: Option<i64>,
    pub confidence: Option<f32>,
    pub model_version: Option<String>,
    pub attachments: Vec<Attachment>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Citation {
    pub source_id: String,
    pub source_title: String,
    pub excerpt: String,
    pub page_number: Option<i32>,
    pub relevance_score: f32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Attachment {
    pub id: String,
    pub name: String,
    pub mime_type: String,
    pub size: i64,
    pub url: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Reaction {
    pub user_id: String,
    pub emoji: String,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StudioDocument {
    pub id: String,
    pub notebook_id: String,
    pub title: String,
    pub content: String,
    pub document_type: DocumentType,
    pub version: i32,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub collaborators: Vec<String>,
    pub is_published: bool,
    pub export_formats: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DocumentType {
    Note,
    Diagram,
    Slide,
    Code,
    Markdown,
    RichText,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AIContext {
    pub id: String,
    pub notebook_id: String,
    pub selected_sources: Vec<String>,
    pub active_document: Option<String>,
    pub chat_context: String,
    pub ai_model: String,
    pub temperature: f32,
    pub max_tokens: i32,
    pub system_prompt: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

// ============= Database Schema =============

const INIT_SQL: &str = r#"
CREATE TABLE IF NOT EXISTS notebooks (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT,
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL,
    owner_id TEXT NOT NULL,
    is_public BOOLEAN DEFAULT FALSE,
    tags TEXT,
    metadata TEXT
);

CREATE TABLE IF NOT EXISTS sources (
    id TEXT PRIMARY KEY,
    notebook_id TEXT NOT NULL,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    source_type TEXT NOT NULL,
    url TEXT,
    file_path TEXT,
    metadata TEXT,
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL,
    processed BOOLEAN DEFAULT FALSE,
    embeddings BLOB,
    tags TEXT,
    FOREIGN KEY (notebook_id) REFERENCES notebooks(id) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS chat_messages (
    id TEXT PRIMARY KEY,
    notebook_id TEXT NOT NULL,
    role TEXT NOT NULL,
    content TEXT NOT NULL,
    model TEXT,
    temperature REAL,
    max_tokens INTEGER,
    sources TEXT,
    citations TEXT,
    metadata TEXT,
    parent_id TEXT,
    created_at TIMESTAMP NOT NULL,
    edited_at TIMESTAMP,
    reactions TEXT,
    FOREIGN KEY (notebook_id) REFERENCES notebooks(id) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS studio_documents (
    id TEXT PRIMARY KEY,
    notebook_id TEXT NOT NULL,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    document_type TEXT NOT NULL,
    version INTEGER DEFAULT 1,
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL,
    collaborators TEXT,
    is_published BOOLEAN DEFAULT FALSE,
    export_formats TEXT,
    FOREIGN KEY (notebook_id) REFERENCES notebooks(id) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS ai_contexts (
    id TEXT PRIMARY KEY,
    notebook_id TEXT NOT NULL,
    selected_sources TEXT,
    active_document TEXT,
    chat_context TEXT,
    ai_model TEXT NOT NULL,
    temperature REAL DEFAULT 0.7,
    max_tokens INTEGER DEFAULT 2000,
    system_prompt TEXT,
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL,
    FOREIGN KEY (notebook_id) REFERENCES notebooks(id) ON DELETE CASCADE
);

CREATE INDEX idx_sources_notebook ON sources(notebook_id);
CREATE INDEX idx_messages_notebook ON chat_messages(notebook_id);
CREATE INDEX idx_documents_notebook ON studio_documents(notebook_id);
CREATE INDEX idx_sources_processed ON sources(processed);
CREATE INDEX idx_messages_created ON chat_messages(created_at);
"#;

// ============= Application State =============

pub struct AppState {
    pub db: Arc<tokio::sync::Mutex<rusqlite::Connection>>, // Changed from SqlitePool to rusqlite
    pub ai_service: Arc<RwLock<AIService>>,
    pub embedding_service: Arc<RwLock<EmbeddingService>>,
    pub search_service: Arc<RwLock<SearchService>>,
    pub sync_service: Arc<RwLock<SyncService>>,
    pub cache: Arc<RwLock<CacheService>>,
}

pub struct AIService {
    api_key: String,
    base_url: String,
    models: Vec<AIModel>,
}

pub struct AIModel {
    id: String,
    name: String,
    max_tokens: i32,
    supports_vision: bool,
    supports_tools: bool,
}

pub struct EmbeddingService {
    model: String,
    dimension: usize,
}

pub struct SearchService {
    index: HashMap<String, Vec<f32>>,
}

pub struct SyncService {
    sync_enabled: bool,
    last_sync: Option<DateTime<Utc>>,
}

pub struct CacheService {
    cache: HashMap<String, CachedItem>,
}

#[derive(Debug, Clone)]
struct CachedItem {
    data: serde_json::Value,
    expires_at: DateTime<Utc>,
}

// ============= Tauri Commands =============

#[tauri::command]
pub async fn create_notebook(
    state: State<'_, Arc<AppState>>,
    title: String,
    description: Option<String>,
) -> Result<Notebook, String> {
    let notebook = Notebook {
        id: Uuid::new_v4().to_string(),
        title,
        description,
        created_at: Utc::now(),
        updated_at: Utc::now(),
        owner_id: "current_user".to_string(), // Get from auth
        is_public: false,
        tags: vec![],
        metadata: HashMap::new(),
    };
    
    sqlx::query!(
        "INSERT INTO notebooks (id, title, description, created_at, updated_at, owner_id, is_public, tags, metadata) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
        notebook.id,
        notebook.title,
        notebook.description,
        notebook.created_at,
        notebook.updated_at,
        notebook.owner_id,
        notebook.is_public,
        serde_json::to_string(&notebook.tags).unwrap(),
        serde_json::to_string(&notebook.metadata).unwrap()
    )
    .execute(&state.db)
    .await
    .map_err(|e| e.to_string())?;
    
    Ok(notebook)
}

#[tauri::command]
pub async fn add_source(
    state: State<'_, Arc<AppState>>,
    notebook_id: String,
    title: String,
    content: String,
    source_type: SourceType,
) -> Result<Source, String> {
    let source = Source {
        id: Uuid::new_v4().to_string(),
        notebook_id,
        title,
        content: content.clone(),
        source_type,
        url: None,
        file_path: None,
        metadata: SourceMetadata {
            size: Some(content.len() as i64),
            page_count: None,
            word_count: Some(content.split_whitespace().count() as i32),
            language: Some("en".to_string()),
            author: None,
            published_date: None,
            last_accessed: Some(Utc::now()),
            extraction_method: Some("direct".to_string()),
            confidence_score: Some(1.0),
        },
        created_at: Utc::now(),
        updated_at: Utc::now(),
        processed: false,
        embeddings: None,
        tags: vec![],
    };
    
    // Generate embeddings asynchronously
    let embedding_service = state.embedding_service.read().await;
    let embeddings = generate_embeddings(&content).await?;
    
    sqlx::query!(
        "INSERT INTO sources (id, notebook_id, title, content, source_type, url, file_path, metadata, created_at, updated_at, processed, embeddings, tags) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
        source.id,
        source.notebook_id,
        source.title,
        source.content,
        serde_json::to_string(&source.source_type).unwrap(),
        source.url,
        source.file_path,
        serde_json::to_string(&source.metadata).unwrap(),
        source.created_at,
        source.updated_at,
        true, // Mark as processed after embedding
        embeddings,
        serde_json::to_string(&source.tags).unwrap()
    )
    .execute(&state.db)
    .await
    .map_err(|e| e.to_string())?;
    
    Ok(source)
}

#[tauri::command]
pub async fn send_chat_message(
    state: State<'_, Arc<AppState>>,
    notebook_id: String,
    content: String,
    sources: Vec<String>,
    model: Option<String>,
    temperature: Option<f32>,
    max_tokens: Option<i32>,
) -> Result<ChatMessage, String> {
    // Create user message
    let user_message = ChatMessage {
        id: Uuid::new_v4().to_string(),
        notebook_id: notebook_id.clone(),
        role: MessageRole::User,
        content: content.clone(),
        model: model.clone(),
        temperature,
        max_tokens,
        sources: sources.clone(),
        citations: vec![],
        metadata: MessageMetadata {
            tokens_used: None,
            processing_time_ms: None,
            confidence: None,
            model_version: None,
            attachments: vec![],
        },
        parent_id: None,
        created_at: Utc::now(),
        edited_at: None,
        reactions: vec![],
    };
    
    // Save user message
    save_message(&state.db, &user_message).await?;
    
    // Generate AI response
    let start = std::time::Instant::now();
    let ai_service = state.ai_service.read().await;
    let (response_content, citations) = generate_ai_response(
        &ai_service,
        &content,
        &sources,
        &state.db,
    ).await?;
    let processing_time = start.elapsed().as_millis() as i64;
    
    // Create assistant message
    let assistant_message = ChatMessage {
        id: Uuid::new_v4().to_string(),
        notebook_id,
        role: MessageRole::Assistant,
        content: response_content,
        model: model.or(Some("gpt-4".to_string())),
        temperature: temperature.or(Some(0.7)),
        max_tokens: max_tokens.or(Some(2000)),
        sources,
        citations,
        metadata: MessageMetadata {
            tokens_used: Some(1500), // Calculate actual tokens
            processing_time_ms: Some(processing_time),
            confidence: Some(0.95),
            model_version: Some("latest".to_string()),
            attachments: vec![],
        },
        parent_id: Some(user_message.id),
        created_at: Utc::now(),
        edited_at: None,
        reactions: vec![],
    };
    
    // Save assistant message
    save_message(&state.db, &assistant_message).await?;
    
    Ok(assistant_message)
}

#[tauri::command]
pub async fn search_sources(
    state: State<'_, Arc<AppState>>,
    notebook_id: String,
    query: String,
    limit: Option<i32>,
) -> Result<Vec<Source>, String> {
    let search_service = state.search_service.read().await;
    
    // Generate query embedding
    let query_embedding = generate_embeddings(&query).await?;
    
    // Search using vector similarity
    let results = sqlx::query_as!(
        Source,
        "SELECT * FROM sources 
         WHERE notebook_id = ? AND processed = true
         ORDER BY embeddings <-> ?
         LIMIT ?",
        notebook_id,
        query_embedding,
        limit.unwrap_or(10)
    )
    .fetch_all(&state.db)
    .await
    .map_err(|e| e.to_string())?;
    
    Ok(results)
}

#[tauri::command]
pub async fn create_studio_document(
    state: State<'_, Arc<AppState>>,
    notebook_id: String,
    title: String,
    content: String,
    document_type: DocumentType,
) -> Result<StudioDocument, String> {
    let document = StudioDocument {
        id: Uuid::new_v4().to_string(),
        notebook_id,
        title,
        content,
        document_type,
        version: 1,
        created_at: Utc::now(),
        updated_at: Utc::now(),
        collaborators: vec![],
        is_published: false,
        export_formats: vec!["markdown".to_string(), "pdf".to_string()],
    };
    
    sqlx::query!(
        "INSERT INTO studio_documents (id, notebook_id, title, content, document_type, version, created_at, updated_at, collaborators, is_published, export_formats) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
        document.id,
        document.notebook_id,
        document.title,
        document.content,
        serde_json::to_string(&document.document_type).unwrap(),
        document.version,
        document.created_at,
        document.updated_at,
        serde_json::to_string(&document.collaborators).unwrap(),
        document.is_published,
        serde_json::to_string(&document.export_formats).unwrap()
    )
    .execute(&state.db)
    .await
    .map_err(|e| e.to_string())?;
    
    Ok(document)
}

#[tauri::command]
pub async fn sync_context(
    state: State<'_, Arc<AppState>>,
    notebook_id: String,
    selected_sources: Vec<String>,
    active_document: Option<String>,
    chat_context: String,
) -> Result<AIContext, String> {
    let context = AIContext {
        id: Uuid::new_v4().to_string(),
        notebook_id,
        selected_sources,
        active_document,
        chat_context,
        ai_model: "gpt-4".to_string(),
        temperature: 0.7,
        max_tokens: 2000,
        system_prompt: Some("You are a helpful AI assistant.".to_string()),
        created_at: Utc::now(),
        updated_at: Utc::now(),
    };
    
    // Update or insert context
    sqlx::query!(
        "INSERT OR REPLACE INTO ai_contexts (id, notebook_id, selected_sources, active_document, chat_context, ai_model, temperature, max_tokens, system_prompt, created_at, updated_at) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
        context.id,
        context.notebook_id,
        serde_json::to_string(&context.selected_sources).unwrap(),
        context.active_document,
        context.chat_context,
        context.ai_model,
        context.temperature,
        context.max_tokens,
        context.system_prompt,
        context.created_at,
        context.updated_at
    )
    .execute(&state.db)
    .await
    .map_err(|e| e.to_string())?;
    
    // Trigger sync service
    let mut sync_service = state.sync_service.write().await;
    sync_service.last_sync = Some(Utc::now());
    
    Ok(context)
}

// ============= Helper Functions =============

async fn generate_embeddings(text: &str) -> Result<Vec<u8>, String> {
    // Call OpenAI/local embedding model
    // This is a placeholder - implement actual embedding generation
    let embedding = vec![0.1_f32; 1536]; // OpenAI ada-002 dimension
    Ok(embedding.iter().flat_map(|f| f.to_le_bytes()).collect())
}

/* Temporarily disabled - needs refactoring to rusqlite
async fn generate_ai_response(
    ai_service: &AIService,
    query: &str,
    source_ids: &[String],
    db: &SqlitePool,
) -> Result<(String, Vec<Citation>), String> {
    // Fetch relevant sources
    let sources = fetch_sources_by_ids(db, source_ids).await?;
    
    // Build context
    let context = sources
        .iter()
        .map(|s| format!("[{}]: {}", s.title, s.content))
        .collect::<Vec<_>>()
        .join("\n\n");
    
    // Generate response (placeholder - implement actual AI call)
    let response = format!("Based on the sources provided:\n\n{}\n\nHere's my analysis of '{}'", 
        context.chars().take(200).collect::<String>(), 
        query
    );
    
    // Generate citations
    let citations = sources
        .iter()
        .map(|s| Citation {
            source_id: s.id.clone(),
            source_title: s.title.clone(),
            excerpt: s.content.chars().take(100).collect(),
            page_number: None,
            relevance_score: 0.9,
        })
        .collect();
    
    Ok((response, citations))
}

*/

/* Temporarily disabled - needs refactoring to rusqlite
async fn save_message(db: &SqlitePool, message: &ChatMessage) -> Result<(), String> {
    sqlx::query!(
        "INSERT INTO chat_messages (id, notebook_id, role, content, model, temperature, max_tokens, sources, citations, metadata, parent_id, created_at, edited_at, reactions) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
        message.id,
        message.notebook_id,
        serde_json::to_string(&message.role).unwrap(),
        message.content,
        message.model,
        message.temperature,
        message.max_tokens,
        serde_json::to_string(&message.sources).unwrap(),
        serde_json::to_string(&message.citations).unwrap(),
        serde_json::to_string(&message.metadata).unwrap(),
        message.parent_id,
        message.created_at,
        message.edited_at,
        serde_json::to_string(&message.reactions).unwrap()
    )
    .execute(db)
    .await
    .map_err(|e| e.to_string())?;
    
    Ok(())
}

*/

/* Temporarily disabled - needs refactoring to rusqlite
async fn fetch_sources_by_ids(db: &SqlitePool, ids: &[String]) -> Result<Vec<Source>, String> {
    // Implement fetching sources by IDs
    Ok(vec![])
}

// ============= WebSocket Support =============

#[tauri::command]
pub async fn subscribe_to_updates(
    window: tauri::Window,
    notebook_id: String,
) -> Result<(), String> {
    // Set up real-time updates
    tokio::spawn(async move {
        loop {
            // Emit updates to frontend
            window.emit("notebook-update", serde_json::json!({
                "notebook_id": notebook_id,
                "type": "sync",
                "timestamp": Utc::now(),
            })).unwrap();
            
            tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
        }
    });
    
    Ok(())
}
*/

// ============= Initialize Backend =============

/* Temporarily disabled - needs refactoring to rusqlite
pub async fn initialize_backend() -> Result<Arc<AppState>, Box<dyn std::error::Error>> {
    // Create database
    let db_url = "sqlite://lighthouse.db";
    if !sqlx::Sqlite::database_exists(db_url).await? {
        sqlx::Sqlite::create_database(db_url).await?;
    }
    
    let db = SqlitePool::connect(db_url).await?;
    
    // Run migrations
    sqlx::query(INIT_SQL).execute(&db).await?;
    
    // Initialize services
    let app_state = Arc::new(AppState {
        db,
        ai_service: Arc::new(RwLock::new(AIService {
            api_key: std::env::var("OPENAI_API_KEY").unwrap_or_default(),
            base_url: "https://api.openai.com/v1".to_string(),
            models: vec![
                AIModel {
                    id: "gpt-4".to_string(),
                    name: "GPT-4".to_string(),
                    max_tokens: 8192,
                    supports_vision: true,
                    supports_tools: true,
                },
            ],
        })),
        embedding_service: Arc::new(RwLock::new(EmbeddingService {
            model: "text-embedding-ada-002".to_string(),
            dimension: 1536,
        })),
        search_service: Arc::new(RwLock::new(SearchService {
            index: HashMap::new(),
        })),
        sync_service: Arc::new(RwLock::new(SyncService {
            sync_enabled: true,
            last_sync: None,
        })),
        cache: Arc::new(RwLock::new(CacheService {
            cache: HashMap::new(),
        })),
    });
    
    Ok(app_state)
}
*/