//! Common types and utilities shared across the Beacon backend

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use chrono::{DateTime, Utc};

/// Standard result type for backend operations
pub type BeaconResult<T> = Result<T, BeaconError>;

/// Unified error type for all backend operations
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum BeaconError {
    DatabaseError(String),
    ValidationError(String),
    NotFound(String),
    AIServiceError(String),
    NetworkError(String),
    SerializationError(String),
    PermissionDenied(String),
    InternalError(String),
    ServiceError(String),
    SearchError(String),
    CacheError(String),
}

impl std::fmt::Display for BeaconError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            BeaconError::DatabaseError(msg) => write!(f, "Database error: {}", msg),
            BeaconError::ValidationError(msg) => write!(f, "Validation error: {}", msg),
            BeaconError::NotFound(msg) => write!(f, "Not found: {}", msg),
            BeaconError::AIServiceError(msg) => write!(f, "AI service error: {}", msg),
            BeaconError::NetworkError(msg) => write!(f, "Network error: {}", msg),
            BeaconError::SerializationError(msg) => write!(f, "Serialization error: {}", msg),
            BeaconError::PermissionDenied(msg) => write!(f, "Permission denied: {}", msg),
            BeaconError::InternalError(msg) => write!(f, "Internal error: {}", msg),
            BeaconError::ServiceError(msg) => write!(f, "Service error: {}", msg),
            BeaconError::SearchError(msg) => write!(f, "Search error: {}", msg),
            BeaconError::CacheError(msg) => write!(f, "Cache error: {}", msg),
        }
    }
}

impl std::error::Error for BeaconError {}

/// Standard response wrapper for API operations
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    pub data: Option<T>,
    pub error: Option<String>,
    pub timestamp: DateTime<Utc>,
}

impl<T> ApiResponse<T> {
    pub fn success(data: T) -> Self {
        Self {
            success: true,
            data: Some(data),
            error: None,
            timestamp: Utc::now(),
        }
    }

    pub fn error(error: BeaconError) -> Self {
        Self {
            success: false,
            data: None,
            error: Some(error.to_string()),
            timestamp: Utc::now(),
        }
    }
}

/// Pagination parameters for list operations
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaginationParams {
    pub page: u32,
    pub limit: u32,
    pub sort_by: Option<String>,
    pub sort_order: Option<SortOrder>,
}

impl Default for PaginationParams {
    fn default() -> Self {
        Self {
            page: 1,
            limit: 20,
            sort_by: None,
            sort_order: Some(SortOrder::Desc),
        }
    }
}

/// Sort order for queries
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SortOrder {
    Asc,
    Desc,
}

/// Paginated response wrapper
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaginatedResponse<T> {
    pub items: Vec<T>,
    pub total: u32,
    pub page: u32,
    pub limit: u32,
    pub has_next: bool,
    pub has_prev: bool,
}

/// Generic metadata structure
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct Metadata {
    pub fields: HashMap<String, serde_json::Value>,
}

impl Metadata {
    pub fn new() -> Self {
        Self {
            fields: HashMap::new(),
        }
    }

    pub fn insert<T: Serialize>(&mut self, key: String, value: T) -> BeaconResult<()> {
        let json_value = serde_json::to_value(value)
            .map_err(|e| BeaconError::SerializationError(e.to_string()))?;
        self.fields.insert(key, json_value);
        Ok(())
    }

    pub fn get<T: for<'de> Deserialize<'de>>(&self, key: &str) -> BeaconResult<Option<T>> {
        match self.fields.get(key) {
            Some(value) => {
                let result = serde_json::from_value(value.clone())
                    .map_err(|e| BeaconError::SerializationError(e.to_string()))?;
                Ok(Some(result))
            }
            None => Ok(None),
        }
    }
}

/// Common timestamp fields
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Timestamps {
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

impl Default for Timestamps {
    fn default() -> Self {
        let now = Utc::now();
        Self {
            created_at: now,
            updated_at: now,
        }
    }
}

impl Timestamps {
    pub fn new() -> Self {
        Self::default()
    }

    pub fn touch(&mut self) {
        self.updated_at = Utc::now();
    }
}

/// Status enum for various entities
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum Status {
    Active,
    Inactive,
    Pending,
    Archived,
    Deleted,
}

impl Default for Status {
    fn default() -> Self {
        Status::Active
    }
}

/// Priority levels
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum Priority {
    Low,
    Medium,
    High,
    Critical,
}

impl Default for Priority {
    fn default() -> Self {
        Priority::Medium
    }
}