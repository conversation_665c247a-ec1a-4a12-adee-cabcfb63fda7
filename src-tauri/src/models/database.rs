//! Database-specific models and conversion utilities
//! This module handles the conversion between domain models and database representations

use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use std::collections::HashMap;

use super::common::{BeaconResult, BeaconError};
use super::beacon as domain;
use super::lighthouse as ai;

/// Database representation of a Page
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DbPage {
    pub id: String,
    pub title: String,
    pub content: String,
    pub tags: String, // JSON serialized Vec<String>
    pub metadata: String, // JSON serialized Metadata
    pub settings: String, // JSON serialized PageSettings
    pub template_id: Option<String>,
    pub project_id: Option<String>,
    pub status: String,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

impl TryFrom<domain::Page> for DbPage {
    type Error = BeaconError;

    fn try_from(page: domain::Page) -> BeaconResult<Self> {
        Ok(Self {
            id: page.id,
            title: page.title,
            content: page.content,
            tags: serde_json::to_string(&page.tags)
                .map_err(|e| BeaconError::SerializationError(e.to_string()))?,
            metadata: serde_json::to_string(&page.metadata)
                .map_err(|e| BeaconError::SerializationError(e.to_string()))?,
            settings: serde_json::to_string(&page.settings)
                .map_err(|e| BeaconError::SerializationError(e.to_string()))?,
            template_id: page.template_id,
            project_id: page.project_id,
            status: serde_json::to_string(&page.status)
                .map_err(|e| BeaconError::SerializationError(e.to_string()))?,
            created_at: page.timestamps.created_at,
            updated_at: page.timestamps.updated_at,
        })
    }
}

impl TryFrom<DbPage> for domain::Page {
    type Error = BeaconError;

    fn try_from(db_page: DbPage) -> BeaconResult<Self> {
        Ok(Self {
            id: db_page.id,
            title: db_page.title,
            content: db_page.content,
            tags: serde_json::from_str(&db_page.tags)
                .map_err(|e| BeaconError::SerializationError(e.to_string()))?,
            metadata: serde_json::from_str(&db_page.metadata)
                .map_err(|e| BeaconError::SerializationError(e.to_string()))?,
            settings: serde_json::from_str(&db_page.settings)
                .map_err(|e| BeaconError::SerializationError(e.to_string()))?,
            template_id: db_page.template_id,
            project_id: db_page.project_id,
            status: serde_json::from_str(&db_page.status)
                .map_err(|e| BeaconError::SerializationError(e.to_string()))?,
            timestamps: super::common::Timestamps {
                created_at: db_page.created_at,
                updated_at: db_page.updated_at,
            },
        })
    }
}

/// Database representation of a Project
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DbProject {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub metadata: String, // JSON serialized
    pub settings: String, // JSON serialized
    pub status: String,
    pub priority: String,
    pub owner_id: String,
    pub collaborators: String, // JSON serialized Vec<String>
    pub page_count: u32,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

impl TryFrom<domain::Project> for DbProject {
    type Error = BeaconError;

    fn try_from(project: domain::Project) -> BeaconResult<Self> {
        Ok(Self {
            id: project.id,
            name: project.name,
            description: project.description,
            metadata: serde_json::to_string(&project.metadata)
                .map_err(|e| BeaconError::SerializationError(e.to_string()))?,
            settings: serde_json::to_string(&project.settings)
                .map_err(|e| BeaconError::SerializationError(e.to_string()))?,
            status: serde_json::to_string(&project.status)
                .map_err(|e| BeaconError::SerializationError(e.to_string()))?,
            priority: serde_json::to_string(&project.priority)
                .map_err(|e| BeaconError::SerializationError(e.to_string()))?,
            owner_id: project.owner_id,
            collaborators: serde_json::to_string(&project.collaborators)
                .map_err(|e| BeaconError::SerializationError(e.to_string()))?,
            page_count: project.page_count,
            created_at: project.timestamps.created_at,
            updated_at: project.timestamps.updated_at,
        })
    }
}

impl TryFrom<DbProject> for domain::Project {
    type Error = BeaconError;

    fn try_from(db_project: DbProject) -> BeaconResult<Self> {
        Ok(Self {
            id: db_project.id,
            name: db_project.name,
            description: db_project.description,
            metadata: serde_json::from_str(&db_project.metadata)
                .map_err(|e| BeaconError::SerializationError(e.to_string()))?,
            settings: serde_json::from_str(&db_project.settings)
                .map_err(|e| BeaconError::SerializationError(e.to_string()))?,
            status: serde_json::from_str(&db_project.status)
                .map_err(|e| BeaconError::SerializationError(e.to_string()))?,
            priority: serde_json::from_str(&db_project.priority)
                .map_err(|e| BeaconError::SerializationError(e.to_string()))?,
            owner_id: db_project.owner_id,
            collaborators: serde_json::from_str(&db_project.collaborators)
                .map_err(|e| BeaconError::SerializationError(e.to_string()))?,
            page_count: db_project.page_count,
            timestamps: super::common::Timestamps {
                created_at: db_project.created_at,
                updated_at: db_project.updated_at,
            },
        })
    }
}

/// Database representation of a Source
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DbSource {
    pub id: String,
    pub title: String,
    pub content: String,
    pub source_type: String,
    pub url: Option<String>,
    pub file_path: Option<String>,
    pub metadata: String, // JSON serialized SourceMetadata
    pub tags: String, // JSON serialized Vec<String>
    pub project_id: Option<String>,
    pub status: String,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

impl TryFrom<domain::Source> for DbSource {
    type Error = BeaconError;

    fn try_from(source: domain::Source) -> BeaconResult<Self> {
        Ok(Self {
            id: source.id,
            title: source.title,
            content: source.content,
            source_type: serde_json::to_string(&source.source_type)
                .map_err(|e| BeaconError::SerializationError(e.to_string()))?,
            url: source.url,
            file_path: source.file_path,
            metadata: serde_json::to_string(&source.metadata)
                .map_err(|e| BeaconError::SerializationError(e.to_string()))?,
            tags: serde_json::to_string(&source.tags)
                .map_err(|e| BeaconError::SerializationError(e.to_string()))?,
            project_id: source.project_id,
            status: serde_json::to_string(&source.status)
                .map_err(|e| BeaconError::SerializationError(e.to_string()))?,
            created_at: source.timestamps.created_at,
            updated_at: source.timestamps.updated_at,
        })
    }
}

impl TryFrom<DbSource> for domain::Source {
    type Error = BeaconError;

    fn try_from(db_source: DbSource) -> BeaconResult<Self> {
        Ok(Self {
            id: db_source.id,
            title: db_source.title,
            content: db_source.content,
            source_type: serde_json::from_str(&db_source.source_type)
                .map_err(|e| BeaconError::SerializationError(e.to_string()))?,
            url: db_source.url,
            file_path: db_source.file_path,
            metadata: serde_json::from_str(&db_source.metadata)
                .map_err(|e| BeaconError::SerializationError(e.to_string()))?,
            tags: serde_json::from_str(&db_source.tags)
                .map_err(|e| BeaconError::SerializationError(e.to_string()))?,
            project_id: db_source.project_id,
            status: serde_json::from_str(&db_source.status)
                .map_err(|e| BeaconError::SerializationError(e.to_string()))?,
            timestamps: super::common::Timestamps {
                created_at: db_source.created_at,
                updated_at: db_source.updated_at,
            },
        })
    }
}

/// Database representation of a ChatMessage
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DbChatMessage {
    pub id: String,
    pub conversation_id: String,
    pub role: String,
    pub content: String,
    pub metadata: String, // JSON serialized MessageMetadata
    pub attachments: String, // JSON serialized Vec<Attachment>
    pub citations: String, // JSON serialized Vec<Citation>
    pub reactions: String, // JSON serialized Vec<Reaction>
    pub status: String,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

impl TryFrom<ai::ChatMessage> for DbChatMessage {
    type Error = BeaconError;

    fn try_from(message: ai::ChatMessage) -> BeaconResult<Self> {
        Ok(Self {
            id: message.id,
            conversation_id: message.conversation_id,
            role: serde_json::to_string(&message.role)
                .map_err(|e| BeaconError::SerializationError(e.to_string()))?,
            content: message.content,
            metadata: serde_json::to_string(&message.metadata)
                .map_err(|e| BeaconError::SerializationError(e.to_string()))?,
            attachments: serde_json::to_string(&message.attachments)
                .map_err(|e| BeaconError::SerializationError(e.to_string()))?,
            citations: serde_json::to_string(&message.citations)
                .map_err(|e| BeaconError::SerializationError(e.to_string()))?,
            reactions: serde_json::to_string(&message.reactions)
                .map_err(|e| BeaconError::SerializationError(e.to_string()))?,
            status: serde_json::to_string(&message.status)
                .map_err(|e| BeaconError::SerializationError(e.to_string()))?,
            created_at: message.timestamps.created_at,
            updated_at: message.timestamps.updated_at,
        })
    }
}

impl TryFrom<DbChatMessage> for ai::ChatMessage {
    type Error = BeaconError;

    fn try_from(db_message: DbChatMessage) -> BeaconResult<Self> {
        Ok(Self {
            id: db_message.id,
            conversation_id: db_message.conversation_id,
            role: serde_json::from_str(&db_message.role)
                .map_err(|e| BeaconError::SerializationError(e.to_string()))?,
            content: db_message.content,
            metadata: serde_json::from_str(&db_message.metadata)
                .map_err(|e| BeaconError::SerializationError(e.to_string()))?,
            attachments: serde_json::from_str(&db_message.attachments)
                .map_err(|e| BeaconError::SerializationError(e.to_string()))?,
            citations: serde_json::from_str(&db_message.citations)
                .map_err(|e| BeaconError::SerializationError(e.to_string()))?,
            reactions: serde_json::from_str(&db_message.reactions)
                .map_err(|e| BeaconError::SerializationError(e.to_string()))?,
            status: serde_json::from_str(&db_message.status)
                .map_err(|e| BeaconError::SerializationError(e.to_string()))?,
            timestamps: super::common::Timestamps {
                created_at: db_message.created_at,
                updated_at: db_message.updated_at,
            },
        })
    }
}

/// Database connection pool configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DatabaseConfig {
    pub database_url: String,
    pub max_connections: u32,
    pub min_connections: u32,
    pub connection_timeout: std::time::Duration,
    pub idle_timeout: Option<std::time::Duration>,
    pub max_lifetime: Option<std::time::Duration>,
}

impl Default for DatabaseConfig {
    fn default() -> Self {
        Self {
            database_url: "sqlite://lightbulb.db".to_string(),
            max_connections: 10,
            min_connections: 1,
            connection_timeout: std::time::Duration::from_secs(30),
            idle_timeout: Some(std::time::Duration::from_secs(600)),
            max_lifetime: Some(std::time::Duration::from_secs(1800)),
        }
    }
}

/// Database query builder helpers
pub struct QueryBuilder {
    query: String,
    params: Vec<serde_json::Value>,
}

impl QueryBuilder {
    pub fn new() -> Self {
        Self {
            query: String::new(),
            params: Vec::new(),
        }
    }

    pub fn select(mut self, columns: &[&str]) -> Self {
        self.query = format!("SELECT {}", columns.join(", "));
        self
    }

    pub fn from(mut self, table: &str) -> Self {
        self.query.push_str(&format!(" FROM {}", table));
        self
    }

    pub fn where_clause(mut self, condition: &str) -> Self {
        if self.query.contains(" WHERE ") {
            self.query.push_str(&format!(" AND {}", condition));
        } else {
            self.query.push_str(&format!(" WHERE {}", condition));
        }
        self
    }

    pub fn order_by(mut self, column: &str, direction: &str) -> Self {
        self.query.push_str(&format!(" ORDER BY {} {}", column, direction));
        self
    }

    pub fn limit(mut self, count: u32) -> Self {
        self.query.push_str(&format!(" LIMIT {}", count));
        self
    }

    pub fn offset(mut self, count: u32) -> Self {
        self.query.push_str(&format!(" OFFSET {}", count));
        self
    }

    pub fn build(self) -> (String, Vec<serde_json::Value>) {
        (self.query, self.params)
    }
}

/// Database transaction helper
pub struct Transaction {
    pub id: String,
    pub operations: Vec<String>,
    pub started_at: DateTime<Utc>,
}

impl Transaction {
    pub fn new() -> Self {
        Self {
            id: uuid::Uuid::new_v4().to_string(),
            operations: Vec::new(),
            started_at: Utc::now(),
        }
    }

    pub fn add_operation(&mut self, operation: String) {
        self.operations.push(operation);
    }
}