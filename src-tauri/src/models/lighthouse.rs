//! Lighthouse AI service models
//! Consolidated from lighthouse_backend.rs

use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use std::collections::HashMap;
use uuid::Uuid;

use super::common::{Metadata, Timestamps, Status, BeaconResult};

/// AI Context for maintaining conversation state
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AIContext {
    pub id: String,
    pub session_id: String,
    pub user_id: String,
    pub context_data: HashMap<String, serde_json::Value>,
    pub conversation_history: Vec<String>,
    pub active_sources: Vec<String>,
    pub preferences: AIPreferences,
    pub timestamps: Timestamps,
}

impl AIContext {
    pub fn new(session_id: String, user_id: String) -> Self {
        Self {
            id: Uuid::new_v4().to_string(),
            session_id,
            user_id,
            context_data: HashMap::new(),
            conversation_history: Vec::new(),
            active_sources: Vec::new(),
            preferences: AIPreferences::default(),
            timestamps: Timestamps::new(),
        }
    }

    pub fn add_message(&mut self, message_id: String) {
        self.conversation_history.push(message_id);
        self.timestamps.touch();
    }

    pub fn add_source(&mut self, source_id: String) {
        if !self.active_sources.contains(&source_id) {
            self.active_sources.push(source_id);
            self.timestamps.touch();
        }
    }
}

/// AI preferences for personalization
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AIPreferences {
    pub model: String,
    pub temperature: f32,
    pub max_tokens: u32,
    pub response_style: ResponseStyle,
    pub language: String,
    pub include_sources: bool,
    pub context_window: u32,
}

impl Default for AIPreferences {
    fn default() -> Self {
        Self {
            model: "claude-3-sonnet".to_string(),
            temperature: 0.7,
            max_tokens: 4000,
            response_style: ResponseStyle::Balanced,
            language: "en".to_string(),
            include_sources: true,
            context_window: 10,
        }
    }
}

/// AI response styles
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ResponseStyle {
    Concise,
    Balanced,
    Detailed,
    Creative,
    Technical,
}

/// Chat message model
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChatMessage {
    pub id: String,
    pub conversation_id: String,
    pub role: MessageRole,
    pub content: String,
    pub metadata: MessageMetadata,
    pub attachments: Vec<Attachment>,
    pub citations: Vec<Citation>,
    pub reactions: Vec<Reaction>,
    pub status: MessageStatus,
    pub timestamps: Timestamps,
}

impl ChatMessage {
    pub fn new_user_message(conversation_id: String, content: String) -> Self {
        Self {
            id: Uuid::new_v4().to_string(),
            conversation_id,
            role: MessageRole::User,
            content,
            metadata: MessageMetadata::default(),
            attachments: Vec::new(),
            citations: Vec::new(),
            reactions: Vec::new(),
            status: MessageStatus::Sent,
            timestamps: Timestamps::new(),
        }
    }

    pub fn new_assistant_message(conversation_id: String, content: String) -> Self {
        Self {
            id: Uuid::new_v4().to_string(),
            conversation_id,
            role: MessageRole::Assistant,
            content,
            metadata: MessageMetadata::default(),
            attachments: Vec::new(),
            citations: Vec::new(),
            reactions: Vec::new(),
            status: MessageStatus::Sent,
            timestamps: Timestamps::new(),
        }
    }

    pub fn add_citation(&mut self, citation: Citation) {
        self.citations.push(citation);
        self.timestamps.touch();
    }

    pub fn add_reaction(&mut self, reaction: Reaction) {
        // Remove existing reaction from same user if exists
        self.reactions.retain(|r| r.user_id != reaction.user_id);
        self.reactions.push(reaction);
        self.timestamps.touch();
    }
}

/// Message roles in conversation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum MessageRole {
    User,
    Assistant,
    System,
}

/// Message status
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum MessageStatus {
    Pending,
    Sent,
    Delivered,
    Read,
    Failed(String),
}

/// Message metadata
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct MessageMetadata {
    pub model_used: Option<String>,
    pub tokens_used: Option<u32>,
    pub processing_time_ms: Option<u64>,
    pub confidence_score: Option<f32>,
    pub source_count: Option<u32>,
    pub custom_fields: HashMap<String, serde_json::Value>,
}

/// File attachment
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Attachment {
    pub id: String,
    pub filename: String,
    pub file_path: String,
    pub mime_type: String,
    pub file_size: u64,
    pub thumbnail_path: Option<String>,
    pub processed: bool,
    pub timestamps: Timestamps,
}

/// Source citation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Citation {
    pub id: String,
    pub source_id: String,
    pub source_title: String,
    pub excerpt: String,
    pub relevance_score: f32,
    pub page_number: Option<u32>,
    pub line_number: Option<u32>,
    pub url: Option<String>,
}

/// User reaction to message
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Reaction {
    pub user_id: String,
    pub reaction_type: ReactionType,
    pub timestamp: DateTime<Utc>,
}

/// Types of reactions
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ReactionType {
    Like,
    Dislike,
    Helpful,
    NotHelpful,
    Accurate,
    Inaccurate,
}

/// Notebook for organizing content
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Notebook {
    pub id: String,
    pub title: String,
    pub description: Option<String>,
    pub owner_id: String,
    pub collaborators: Vec<String>,
    pub tags: Vec<String>,
    pub metadata: Metadata,
    pub settings: NotebookSettings,
    pub page_count: u32,
    pub status: Status,
    pub timestamps: Timestamps,
}

impl Notebook {
    pub fn new(title: String, owner_id: String) -> Self {
        Self {
            id: Uuid::new_v4().to_string(),
            title,
            description: None,
            owner_id,
            collaborators: Vec::new(),
            tags: Vec::new(),
            metadata: Metadata::new(),
            settings: NotebookSettings::default(),
            page_count: 0,
            status: Status::Active,
            timestamps: Timestamps::new(),
        }
    }
}

/// Notebook settings
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NotebookSettings {
    pub is_public: bool,
    pub allow_collaboration: bool,
    pub auto_save: bool,
    pub version_control: bool,
    pub ai_assistance: bool,
    pub export_formats: Vec<ExportFormat>,
}

impl Default for NotebookSettings {
    fn default() -> Self {
        Self {
            is_public: false,
            allow_collaboration: false,
            auto_save: true,
            version_control: true,
            ai_assistance: true,
            export_formats: vec![ExportFormat::Markdown, ExportFormat::PDF],
        }
    }
}

/// Export format options
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ExportFormat {
    Markdown,
    PDF,
    HTML,
    DOCX,
    JSON,
    XML,
}

/// Studio document for advanced editing
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StudioDocument {
    pub id: String,
    pub title: String,
    pub content: String,
    pub document_type: DocumentType,
    pub version: u32,
    pub parent_id: Option<String>,
    pub notebook_id: Option<String>,
    pub metadata: Metadata,
    pub collaborators: Vec<String>,
    pub status: Status,
    pub timestamps: Timestamps,
}

impl StudioDocument {
    pub fn new(title: String, content: String, document_type: DocumentType) -> Self {
        Self {
            id: Uuid::new_v4().to_string(),
            title,
            content,
            document_type,
            version: 1,
            parent_id: None,
            notebook_id: None,
            metadata: Metadata::new(),
            collaborators: Vec::new(),
            status: Status::Active,
            timestamps: Timestamps::new(),
        }
    }

    pub fn create_version(&self, new_content: String) -> Self {
        let mut new_doc = self.clone();
        new_doc.id = Uuid::new_v4().to_string();
        new_doc.content = new_content;
        new_doc.version = self.version + 1;
        new_doc.parent_id = Some(self.id.clone());
        new_doc.timestamps = Timestamps::new();
        new_doc
    }
}

/// Document types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DocumentType {
    Note,
    Article,
    Research,
    Report,
    Presentation,
    Code,
    Diagram,
}

/// Embedding vector for semantic search
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EmbeddingVector {
    pub id: String,
    pub source_id: String,
    pub chunk_index: u32,
    pub content: String,
    pub vector: Vec<f32>,
    pub model: String,
    pub dimensions: u32,
    pub timestamps: Timestamps,
}

/// Search result with relevance scoring
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchResult {
    pub source_id: String,
    pub title: String,
    pub excerpt: String,
    pub relevance_score: f32,
    pub source_type: String,
    pub metadata: HashMap<String, serde_json::Value>,
    pub highlights: Vec<String>,
}