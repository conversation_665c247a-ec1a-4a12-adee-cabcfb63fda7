//! Beacon-specific data models
//! Consolidated from commands/beacon.rs and beacon_db.rs

use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use std::collections::HashMap;
use uuid::Uuid;

use super::common::{Metadata, Timestamps, Status, Priority, BeaconResult};

/// Unified Page model
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Page {
    pub id: String,
    pub title: String,
    pub content: String,
    pub tags: Vec<String>,
    pub metadata: Metadata,
    pub settings: PageSettings,
    pub template_id: Option<String>,
    pub project_id: Option<String>,
    pub status: Status,
    pub timestamps: Timestamps,
}

impl Page {
    pub fn new(title: String, content: String) -> Self {
        Self {
            id: Uuid::new_v4().to_string(),
            title,
            content,
            tags: Vec::new(),
            metadata: Metadata::new(),
            settings: PageSettings::default(),
            template_id: None,
            project_id: None,
            status: Status::Active,
            timestamps: Timestamps::new(),
        }
    }

    pub fn add_tag(&mut self, tag: String) {
        if !self.tags.contains(&tag) {
            self.tags.push(tag);
            self.timestamps.touch();
        }
    }

    pub fn remove_tag(&mut self, tag: &str) {
        if let Some(pos) = self.tags.iter().position(|t| t == tag) {
            self.tags.remove(pos);
            self.timestamps.touch();
        }
    }
}

/// Page settings configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PageSettings {
    pub is_public: bool,
    pub allow_comments: bool,
    pub auto_save: bool,
    pub theme: Option<String>,
    pub layout: PageLayout,
    pub custom_css: Option<String>,
}

impl Default for PageSettings {
    fn default() -> Self {
        Self {
            is_public: false,
            allow_comments: true,
            auto_save: true,
            theme: None,
            layout: PageLayout::Standard,
            custom_css: None,
        }
    }
}

/// Page layout options
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PageLayout {
    Standard,
    TwoColumn,
    ThreeColumn,
    Sidebar,
    FullWidth,
}

/// Page template model
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PageTemplate {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub content_template: String,
    pub default_settings: PageSettings,
    pub category: String,
    pub is_system: bool,
    pub usage_count: u32,
    pub timestamps: Timestamps,
}

impl PageTemplate {
    pub fn new(name: String, content_template: String, category: String) -> Self {
        Self {
            id: Uuid::new_v4().to_string(),
            name,
            description: None,
            content_template,
            default_settings: PageSettings::default(),
            category,
            is_system: false,
            usage_count: 0,
            timestamps: Timestamps::new(),
        }
    }

    pub fn increment_usage(&mut self) {
        self.usage_count += 1;
        self.timestamps.touch();
    }
}

/// Project model
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Project {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub metadata: Metadata,
    pub settings: ProjectSettings,
    pub status: Status,
    pub priority: Priority,
    pub owner_id: String,
    pub collaborators: Vec<String>,
    pub page_count: u32,
    pub timestamps: Timestamps,
}

impl Project {
    pub fn new(name: String, owner_id: String) -> Self {
        Self {
            id: Uuid::new_v4().to_string(),
            name,
            description: None,
            metadata: Metadata::new(),
            settings: ProjectSettings::default(),
            status: Status::Active,
            priority: Priority::Medium,
            owner_id,
            collaborators: Vec::new(),
            page_count: 0,
            timestamps: Timestamps::new(),
        }
    }

    pub fn add_collaborator(&mut self, user_id: String) -> BeaconResult<()> {
        if !self.collaborators.contains(&user_id) && user_id != self.owner_id {
            self.collaborators.push(user_id);
            self.timestamps.touch();
        }
        Ok(())
    }
}

/// Project settings
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProjectSettings {
    pub is_public: bool,
    pub allow_collaboration: bool,
    pub auto_backup: bool,
    pub backup_frequency: BackupFrequency,
    pub default_page_template: Option<String>,
}

impl Default for ProjectSettings {
    fn default() -> Self {
        Self {
            is_public: false,
            allow_collaboration: false,
            auto_backup: true,
            backup_frequency: BackupFrequency::Daily,
            default_page_template: None,
        }
    }
}

/// Backup frequency options
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum BackupFrequency {
    Never,
    Hourly,
    Daily,
    Weekly,
    Monthly,
}

/// Unified Source model (consolidating from lighthouse_backend)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Source {
    pub id: String,
    pub title: String,
    pub content: String,
    pub source_type: SourceType,
    pub url: Option<String>,
    pub file_path: Option<String>,
    pub metadata: SourceMetadata,
    pub tags: Vec<String>,
    pub project_id: Option<String>,
    pub status: Status,
    pub timestamps: Timestamps,
}

impl Source {
    pub fn new(title: String, content: String, source_type: SourceType) -> Self {
        Self {
            id: Uuid::new_v4().to_string(),
            title,
            content,
            source_type,
            url: None,
            file_path: None,
            metadata: SourceMetadata::default(),
            tags: Vec::new(),
            project_id: None,
            status: Status::Active,
            timestamps: Timestamps::new(),
        }
    }
}

/// Source type enumeration
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum SourceType {
    Document,
    Webpage,
    Video,
    Audio,
    Image,
    Code,
    Note,
    Reference,
}

/// Source metadata
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct SourceMetadata {
    pub author: Option<String>,
    pub published_date: Option<DateTime<Utc>>,
    pub word_count: Option<u32>,
    pub language: Option<String>,
    pub file_size: Option<u64>,
    pub mime_type: Option<String>,
    pub embedding_status: EmbeddingStatus,
    pub custom_fields: HashMap<String, serde_json::Value>,
}

/// Embedding processing status
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum EmbeddingStatus {
    Pending,
    Processing,
    Completed,
    Failed(String),
}

impl Default for EmbeddingStatus {
    fn default() -> Self {
        EmbeddingStatus::Pending
    }
}

/// Saved search model
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SavedSearch {
    pub id: String,
    pub name: String,
    pub query: String,
    pub filters: SearchFilters,
    pub user_id: String,
    pub is_public: bool,
    pub usage_count: u32,
    pub timestamps: Timestamps,
}

/// Search filters
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct SearchFilters {
    pub source_types: Vec<SourceType>,
    pub tags: Vec<String>,
    pub date_range: Option<DateRange>,
    pub projects: Vec<String>,
    pub status: Option<Status>,
}

/// Date range for filtering
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DateRange {
    pub start: DateTime<Utc>,
    pub end: DateTime<Utc>,
}

/// Supported file format
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SupportedFormat {
    pub id: String,
    pub name: String,
    pub extensions: Vec<String>,
    pub mime_types: Vec<String>,
    pub category: FormatCategory,
    pub processor: String,
    pub is_enabled: bool,
    pub max_file_size: Option<u64>,
}

/// Format categories
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum FormatCategory {
    Document,
    Image,
    Video,
    Audio,
    Archive,
    Code,
    Data,
}

/// User pattern for AI learning
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserPattern {
    pub id: String,
    pub user_id: String,
    pub pattern_type: PatternType,
    pub data: serde_json::Value,
    pub confidence: f32,
    pub usage_count: u32,
    pub timestamps: Timestamps,
}

/// Types of user patterns
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PatternType {
    SearchBehavior,
    ContentPreference,
    WorkflowPattern,
    TaggingPattern,
    TimePattern,
}