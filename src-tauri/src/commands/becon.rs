use serde::{Deserialize, Serialize};
use std::sync::Mutex;
use std::collections::HashMap;
use std::time::{SystemTime, UNIX_EPOCH};
use rusqlite::{params, Connection};
// use crate::document_processing::{DocumentProcessor, ProcessedDocument}; // Temporarily disabled

// Database connection
use crate::db::get_connection;

// For backward compatibility, keep the conversation storage in memory for now
lazy_static::lazy_static! {
    static ref CONVERSATIONS: Mutex<HashMap<String, Vec<ChatMessage>>> = Mutex::new(HashMap::new());
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct KnowledgeItem {
    pub id: String,
    pub title: String,
    pub content: String,
    pub tags: Vec<String>,
    pub created_at: u64,
    pub updated_at: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChatMessage {
    pub id: String,
    pub content: String,
    pub is_user: bool,
    pub timestamp: u64,
    pub context_ids: Option<Vec<String>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SaveContentRequest {
    pub title: String,
    pub content: String,
    pub tags: Option<Vec<String>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChatRequest {
    pub message: String,
    pub context_ids: Option<Vec<String>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserCountResponse {
    pub count: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProcessUrlRequest {
    pub url: String,
    pub content_type: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProcessFileRequest {
    pub file_path: String,
    pub use_ocr: Option<bool>,
}

#[tauri::command]
pub fn get_user_count() -> Result<UserCountResponse, String> {
    let conn = get_connection()
        .map_err(|e| format!("Database error: {}", e))?;
    
    let count: i64 = conn
        .query_row(
            "SELECT COUNT(*) FROM beacon_knowledge_items",
            [],
            |row| row.get(0),
        )
        .unwrap_or(0);
    
    // Add a base count for demo purposes
    let total_count = 13523 + count as u32;
    
    Ok(UserCountResponse { count: total_count })
}

#[tauri::command]
pub fn save_content(request: SaveContentRequest) -> Result<KnowledgeItem, String> {
    let conn = get_connection()
        .map_err(|e| format!("Database error: {}", e))?;
    
    let id = format!("item_{}", uuid::Uuid::new_v4());
    let now = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .map_err(|_| "Failed to get timestamp".to_string())?
        .as_secs();
    
    let tags = request.tags.unwrap_or_default();
    let tags_json = serde_json::to_string(&tags)
        .map_err(|e| format!("Failed to serialize tags: {}", e))?;
    
    // Insert into database
    conn.execute(
        "INSERT INTO beacon_knowledge_items (id, title, content, tags, source_type, created_at, updated_at) 
         VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7)",
        params![
            &id,
            &request.title,
            &request.content,
            &tags_json,
            "manual",
            now as i64,
            now as i64
        ],
    ).map_err(|e| format!("Failed to save content: {}", e))?;
    
    let item = KnowledgeItem {
        id: id.clone(),
        title: request.title,
        content: request.content,
        tags,
        created_at: now,
        updated_at: now,
    };
    
    Ok(item)
}

#[tauri::command]
pub fn get_all_content() -> Result<Vec<KnowledgeItem>, String> {
    let conn = get_connection()
        .map_err(|e| format!("Database error: {}", e))?;
    
    let mut stmt = conn
        .prepare(
            "SELECT id, title, content, tags, created_at, updated_at 
             FROM beacon_knowledge_items 
             ORDER BY created_at DESC"
        )
        .map_err(|e| format!("Failed to prepare statement: {}", e))?;
    
    let items = stmt
        .query_map([], |row| {
            let tags_json: String = row.get(3)?;
            let tags: Vec<String> = serde_json::from_str(&tags_json).unwrap_or_default();
            
            Ok(KnowledgeItem {
                id: row.get(0)?,
                title: row.get(1)?,
                content: row.get(2)?,
                tags,
                created_at: row.get::<_, i64>(4)? as u64,
                updated_at: row.get::<_, i64>(5)? as u64,
            })
        })
        .map_err(|e| format!("Failed to query items: {}", e))?
        .collect::<Result<Vec<_>, _>>()
        .map_err(|e| format!("Failed to collect items: {}", e))?;
    
    Ok(items)
}

#[tauri::command]
pub fn get_content_by_id(id: String) -> Result<Option<KnowledgeItem>, String> {
    let conn = get_connection()
        .map_err(|e| format!("Database error: {}", e))?;
    
    let result = conn
        .query_row(
            "SELECT id, title, content, tags, created_at, updated_at 
             FROM beacon_knowledge_items 
             WHERE id = ?1",
            params![&id],
            |row| {
                let tags_json: String = row.get(3)?;
                let tags: Vec<String> = serde_json::from_str(&tags_json).unwrap_or_default();
                
                Ok(KnowledgeItem {
                    id: row.get(0)?,
                    title: row.get(1)?,
                    content: row.get(2)?,
                    tags,
                    created_at: row.get::<_, i64>(4)? as u64,
                    updated_at: row.get::<_, i64>(5)? as u64,
                })
            }
        );
    
    match result {
        Ok(item) => Ok(Some(item)),
        Err(rusqlite::Error::QueryReturnedNoRows) => Ok(None),
        Err(e) => Err(format!("Failed to get item: {}", e))
    }
}

#[tauri::command]
pub fn delete_content(id: String) -> Result<(), String> {
    let conn = get_connection()
        .map_err(|e| format!("Database error: {}", e))?;
    
    conn.execute(
        "DELETE FROM beacon_knowledge_items WHERE id = ?1",
        params![&id],
    )
    .map_err(|e| format!("Failed to delete content: {}", e))?;
    
    Ok(())
}

// Generate AI tags for content
#[tauri::command]
pub fn generate_tags(content: String, title: Option<String>, max_tags: Option<usize>) -> Result<Vec<String>, String> {
    // Simple keyword extraction for now
    // In production, this would use the AI service
    let text = format!("{} {}", title.unwrap_or_default(), content).to_lowercase();
    
    let keywords = vec![
        ("javascript", "programming"),
        ("typescript", "programming"),
        ("python", "programming"),
        ("rust", "programming"),
        ("react", "frontend"),
        ("vue", "frontend"),
        ("angular", "frontend"),
        ("database", "backend"),
        ("sql", "database"),
        ("api", "backend"),
        ("rest", "api"),
        ("graphql", "api"),
        ("design", "ui/ux"),
        ("css", "styling"),
        ("html", "markup"),
        ("meeting", "business"),
        ("strategy", "planning"),
        ("budget", "finance"),
        ("todo", "task"),
        ("task", "productivity"),
        ("idea", "brainstorm"),
        ("bug", "issue"),
        ("feature", "enhancement"),
        ("documentation", "docs"),
        ("test", "testing"),
        ("deployment", "devops"),
        ("docker", "containerization"),
        ("kubernetes", "orchestration"),
        ("git", "version-control"),
        ("github", "collaboration"),
    ];
    
    let mut tags = Vec::new();
    for (keyword, tag) in keywords {
        if text.contains(keyword) && !tags.contains(&tag.to_string()) {
            tags.push(tag.to_string());
        }
    }
    
    // Add content type tags
    if text.contains("http") || text.contains("www") {
        tags.push("link".to_string());
    }
    if text.contains("```") || text.contains("function") || text.contains("class") {
        tags.push("code".to_string());
    }
    if text.len() > 500 {
        tags.push("document".to_string());
    }
    if text.contains("note") || text.contains("remember") {
        tags.push("note".to_string());
    }
    
    // Limit tags
    let max = max_tags.unwrap_or(5);
    tags.truncate(max);
    
    Ok(tags)
}

#[tauri::command]
pub fn chat_with_ai(request: ChatRequest) -> Result<ChatMessage, String> {
    let now = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .map_err(|_| "Failed to get timestamp".to_string())?
        .as_secs();
    
    // Add user message to conversation
    let _user_message = ChatMessage {
        id: format!("user_{}", uuid::Uuid::new_v4()),
        content: request.message.clone(),
        is_user: true,
        timestamp: now,
        context_ids: request.context_ids.clone(),
    };
    
    // Get relevant context if provided
    let mut context_content = String::new();
    if let Some(context_ids) = &request.context_ids {
        let conn = get_connection()
            .map_err(|e| format!("Database error: {}", e))?;
        
        for id in context_ids {
            if let Ok(item) = conn.query_row(
                "SELECT title, content FROM beacon_knowledge_items WHERE id = ?1",
                params![id],
                |row| Ok((row.get::<_, String>(0)?, row.get::<_, String>(1)?))
            ) {
                context_content.push_str(&format!("{}\n{}\n\n", item.0, item.1));
            }
        }
    }
    
    // Generate AI response (mock implementation)
    let ai_response = if !context_content.is_empty() {
        format!(
            "Based on your saved content about \"{}\", I can provide insights on \"{}\". This is a simulated AI response. In a real implementation, this would connect to an AI service like Claude or GPT.",
            if context_content.len() > 50 { 
                format!("{}...", &context_content[..50]) 
            } else { 
                context_content.clone() 
            },
            request.message
        )
    } else {
        format!(
            "I've processed your query: \"{}\". This is a simulated AI response. In a real implementation, this would connect to an AI service.",
            request.message
        )
    };
    
    let ai_message = ChatMessage {
        id: format!("ai_{}", uuid::Uuid::new_v4()),
        content: ai_response,
        is_user: false,
        timestamp: now + 1, // Simulate processing time
        context_ids: request.context_ids,
    };
    
    Ok(ai_message)
}

#[tauri::command]
pub fn get_conversation_history() -> Result<Vec<ChatMessage>, String> {
    // In a real implementation, we would track conversations per session
    // For now, we'll return a simple history
    let conversations = CONVERSATIONS.lock()
        .map_err(|_| "Failed to access conversations".to_string())?;
    
    // Flatten all conversations for simplicity
    let mut all_messages: Vec<ChatMessage> = vec![];
    for conversation in conversations.values() {
        all_messages.extend(conversation.clone());
    }
    
    // Sort by timestamp
    all_messages.sort_by(|a, b| a.timestamp.cmp(&b.timestamp));
    
    Ok(all_messages)
}

/*
#[tauri::command]
pub async fn process_url_content(request: ProcessUrlRequest) -> Result<ProcessedDocument, String> {
    let processor = DocumentProcessor::new().await
        .map_err(|e| format!("Failed to initialize document processor: {}", e))?;
    
    processor.process_url(&request.url).await
        .map_err(|e| format!("Failed to process URL: {}", e))
}
*/

/*
#[tauri::command]
pub async fn process_file_content(request: ProcessFileRequest) -> Result<ProcessedDocument, String> {
    let processor = DocumentProcessor::new().await
        .map_err(|e| format!("Failed to initialize document processor: {}", e))?;
    
    let path = Path::new(&request.file_path);
    let extension = path.extension()
        .and_then(|e| e.to_str())
        .unwrap_or("")
        .to_lowercase();

    match extension.as_str() {
        "pdf" => {
            if request.use_ocr.unwrap_or(false) {
                processor.process_pdf_with_ocr(path).await
                    .map_err(|e| format!("Failed to process PDF with OCR: {}", e))
            } else {
                processor.process_document(path).await
                    .map_err(|e| format!("Failed to process PDF: {}", e))
            }
        },
        "jpg" | "jpeg" | "png" | "bmp" | "tiff" => {
            processor.process_image_with_ocr(path).await
                .map_err(|e| format!("Failed to process image with OCR: {}", e))
        },
        _ => {
            processor.process_document(path).await
                .map_err(|e| format!("Failed to process document: {}", e))
        }
    }
}
*/

/*
#[tauri::command]
pub async fn transcribe_audio_file(file_path: String) -> Result<ProcessedDocument, String> {
    let processor = DocumentProcessor::new().await
        .map_err(|e| format!("Failed to initialize document processor: {}", e))?;
    
    let path = Path::new(&file_path);
    processor.process_document(path).await
        .map_err(|e| format!("Failed to transcribe audio: {}", e))
}
*/

#[tauri::command]
pub async fn get_supported_formats() -> Result<HashMap<String, Vec<String>>, String> {
    let mut formats = HashMap::new();
    
    formats.insert("video".to_string(), vec![
        "mp4".to_string(), "avi".to_string(), "mov".to_string(), 
        "webm".to_string(), "mkv".to_string()
    ]);
    
    formats.insert("audio".to_string(), vec![
        "mp3".to_string(), "wav".to_string(), "m4a".to_string(), 
        "flac".to_string(), "ogg".to_string()
    ]);
    
    formats.insert("image".to_string(), vec![
        "jpg".to_string(), "jpeg".to_string(), "png".to_string(), 
        "bmp".to_string(), "tiff".to_string(), "gif".to_string()
    ]);
    
    formats.insert("document".to_string(), vec![
        "pdf".to_string(), "txt".to_string(), "md".to_string(), 
        "docx".to_string(), "rtf".to_string()
    ]);
    
    formats.insert("code".to_string(), vec![
        "rs".to_string(), "py".to_string(), "js".to_string(), 
        "ts".to_string(), "java".to_string(), "cpp".to_string()
    ]);
    
    formats.insert("urls".to_string(), vec![
        "youtube.com".to_string(), "youtu.be".to_string(),
        "tiktok.com".to_string(), "loom.com".to_string(),
        "github.com".to_string(), "twitter.com".to_string()
    ]);
    
    Ok(formats)
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::sync::Mutex;
    use std::collections::HashMap;

    #[test]
    fn test_save_content() {
        // Reset the knowledge base for testing
        *KNOWLEDGE_BASE.lock().unwrap() = HashMap::new();
        
        let request = SaveContentRequest {
            title: "Test Item".to_string(),
            content: "This is a test item".to_string(),
            tags: Some(vec!["test".to_string()]),
        };
        
        let result = save_content(request);
        assert!(result.is_ok());
        
        let saved_item = result.unwrap();
        assert_eq!(saved_item.title, "Test Item");
        assert_eq!(saved_item.content, "This is a test item");
        assert_eq!(saved_item.tags, vec!["test"]);
    }

    #[test]
    fn test_get_all_content() {
        // Reset the knowledge base for testing
        *KNOWLEDGE_BASE.lock().unwrap() = HashMap::new();
        
        // Add a test item
        let request = SaveContentRequest {
            title: "Test Item".to_string(),
            content: "This is a test item".to_string(),
            tags: Some(vec!["test".to_string()]),
        };
        
        let _ = save_content(request);
        
        // Get all content
        let result = get_all_content();
        assert!(result.is_ok());
        
        let content = result.unwrap();
        assert_eq!(content.len(), 1);
        assert_eq!(content[0].title, "Test Item");
    }

    #[test]
    fn test_chat_with_ai() {
        let request = ChatRequest {
            message: "Hello, AI!".to_string(),
            context_ids: None,
        };
        
        let result = chat_with_ai(request);
        assert!(result.is_ok());
        
        let response = result.unwrap();
        assert!(!response.content.is_empty());
        assert!(!response.is_user);
    }

    #[test]
    fn test_get_user_count() {
        let initial_count = USER_COUNT.lock().unwrap().clone();
        let result = get_user_count();
        assert!(result.is_ok());
        
        let response = result.unwrap();
        assert_eq!(response.count, initial_count);
    }
}