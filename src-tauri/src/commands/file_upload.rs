use std::process::Command;
use std::path::{Path, PathBuf};
use tauri::Manager;
use tauri_plugin_dialog::DialogExt;
use serde::{Deserialize, Serialize};
use serde_json;
use crate::database::{Database, TrainingRepository};
use anyhow::{anyhow, Result};
use std::fs;
use uuid::Uuid;
use tauri::{command, State};

#[derive(Debug, Serialize, Deserialize)]
pub struct FileUploadResult {
    pub success: bool,
    pub file_path: Option<String>,
    pub error: Option<String>,
    pub metadata: Option<serde_json::Value>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct TrainingFileUploadRequest {
    pub file_name: String,
    pub file_content: Vec<u8>,
    pub file_type: String,
    pub upload_type: String, // "template", "competency_matrix", "assessment"
    pub metadata: Option<serde_json::Value>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct TrainingFileUploadResponse {
    pub success: bool,
    pub file_id: String,
    pub file_path: String,
    pub processed_data: Option<serde_json::Value>,
    pub message: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ProcessedCompetencyMatrix {
    pub id: String,
    pub name: String,
    pub description: String,
    pub category: String,
    pub levels: Vec<CompetencyLevel>,
    pub business_criticality: String,
    pub industry_standard: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CompetencyLevel {
    pub level: i32,
    pub name: String,
    pub description: String,
    pub behaviors: Vec<String>,
    pub assessment_criteria: Vec<String>,
    pub development_activities: Vec<String>,
}

/// Handle file upload using native file dialog - most efficient approach
/// The file is never loaded into memory in JavaScript
#[tauri::command]
pub async fn upload_file_native(
    app_handle: tauri::AppHandle,
    notebook_id: String,
    source_id: String,
) -> Result<FileUploadResult, String> {
    // Show file dialog and get selected file path
    let file_path = app_handle.dialog()
        .file()
        .add_filter("Documents", &["pdf", "txt", "md", "csv", "json", "doc", "docx"])
        .add_filter("All Files", &["*"])
        .blocking_pick_file();
    
    match file_path {
        Some(path) => {
            // Process the file directly without loading it into memory
            // FilePath is a wrapper type - convert to PathBuf
            let path_buf = match path.as_path() {
                Some(p) => p.to_path_buf(),
                None => return Ok(FileUploadResult {
                    success: false,
                    file_path: None,
                    error: Some("Invalid file path".to_string()),
                    metadata: None,
                })
            };
            process_file_with_python(app_handle, path_buf, notebook_id, source_id).await
        }
        None => {
            Ok(FileUploadResult {
                success: false,
                file_path: None,
                error: Some("No file selected".to_string()),
                metadata: None,
            })
        }
    }
}

/// Process file using Python script for better performance with large files
async fn process_file_with_python(
    app_handle: tauri::AppHandle,
    file_path: PathBuf,
    notebook_id: String,
    source_id: String,
) -> Result<FileUploadResult, String> {
    let app_data_dir = app_handle.path()
        .app_data_dir()
        .map_err(|e| format!("Failed to get app data dir: {}", e))?;
    
    // Get the Python script path
    let script_path = app_handle.path()
        .resource_dir()
        .map_err(|e| format!("Failed to get resource dir: {}", e))?
        .join("scripts")
        .join("file_handler.py");
    
    // Call Python script to handle the file
    let output = Command::new("python3")
        .arg(&script_path)
        .arg("upload")
        .arg("--app-data-dir")
        .arg(&app_data_dir)
        .arg("--file-path")
        .arg(&file_path)
        .arg("--notebook-id")
        .arg(&notebook_id)
        .arg("--source-id")
        .arg(&source_id)
        .output()
        .map_err(|e| format!("Failed to execute Python script: {}", e))?;
    
    if output.status.success() {
        let result: serde_json::Value = serde_json::from_slice(&output.stdout)
            .map_err(|e| format!("Failed to parse Python output: {}", e))?;
        
        Ok(FileUploadResult {
            success: result["success"].as_bool().unwrap_or(false),
            file_path: result["file_path"].as_str().map(String::from),
            error: result["error"].as_str().map(String::from),
            metadata: Some(result["metadata"].clone()),
        })
    } else {
        let error = String::from_utf8_lossy(&output.stderr);
        Err(format!("Python script failed: {}", error))
    }
}

/// Alternative: Use pure Rust for file handling (faster than Python)
#[tauri::command]
pub async fn upload_file_rust(
    app_handle: tauri::AppHandle,
    file_path: String,
    notebook_id: String,
    source_id: String,
) -> Result<String, String> {
    use std::fs;
    
    let source_path = Path::new(&file_path);
    
    if !source_path.exists() {
        return Err(format!("File not found: {}", file_path));
    }
    
    let app_data_dir = app_handle.path()
        .app_data_dir()
        .map_err(|e| format!("Failed to get app data dir: {}", e))?;
    
    // Create destination directory
    let uploads_dir = app_data_dir.join("uploads").join(&notebook_id);
    fs::create_dir_all(&uploads_dir)
        .map_err(|e| format!("Failed to create directory: {}", e))?;
    
    // Get file extension
    let extension = source_path.extension()
        .and_then(|ext| ext.to_str())
        .unwrap_or("bin");
    
    // Create destination path
    let dest_filename = format!("{}.{}", source_id, extension);
    let dest_path = uploads_dir.join(&dest_filename);
    
    // Copy file efficiently (doesn't load entire file into memory)
    fs::copy(&source_path, &dest_path)
        .map_err(|e| format!("Failed to copy file: {}", e))?;
    
    Ok(format!("uploads/{}/{}", notebook_id, dest_filename))
}

/// Extract text from various file formats using Rust libraries
#[tauri::command]
pub async fn extract_text_rust(
    app_handle: tauri::AppHandle,
    file_path: String,
    file_type: String,
) -> Result<String, String> {
    let app_data_dir = app_handle.path()
        .app_data_dir()
        .map_err(|e| format!("Failed to get app data dir: {}", e))?;
    
    let full_path = if Path::new(&file_path).is_absolute() {
        PathBuf::from(&file_path)
    } else {
        app_data_dir.join(&file_path)
    };
    
    if !full_path.exists() {
        return Err(format!("File not found: {}", file_path));
    }
    
    match file_type.as_str() {
        "pdf" => extract_pdf_text(&full_path),
        "text" | "txt" | "md" => extract_plain_text(&full_path),
        "csv" => extract_csv_text(&full_path),
        "json" => extract_json_text(&full_path),
        _ => extract_plain_text(&full_path),
    }
}

fn extract_plain_text(path: &Path) -> Result<String, String> {
    std::fs::read_to_string(path)
        .map_err(|e| format!("Failed to read file: {}", e))
}

fn extract_pdf_text(_path: &Path) -> Result<String, String> {
    // For PDF, we might still want to use Python or an external tool
    // since Rust PDF libraries are not as mature
    Err("PDF extraction not implemented in Rust. Use Python handler.".to_string())
}

fn extract_csv_text(path: &Path) -> Result<String, String> {
    use std::fs::File;
    use std::io::{BufRead, BufReader};
    
    let file = File::open(path)
        .map_err(|e| format!("Failed to open CSV: {}", e))?;
    
    let reader = BufReader::new(file);
    let mut content = String::new();
    let mut line_count = 0;
    
    for line in reader.lines() {
        if line_count >= 100 {
            content.push_str("\n... (truncated, showing first 100 lines)");
            break;
        }
        
        let line = line.map_err(|e| format!("Failed to read line: {}", e))?;
        content.push_str(&line);
        content.push('\n');
        line_count += 1;
    }
    
    Ok(content)
}

fn extract_json_text(path: &Path) -> Result<String, String> {
    let content = std::fs::read_to_string(path)
        .map_err(|e| format!("Failed to read JSON: {}", e))?;
    
    // Validate and pretty-print JSON
    let value: serde_json::Value = serde_json::from_str(&content)
        .map_err(|e| format!("Invalid JSON: {}", e))?;
    
    serde_json::to_string_pretty(&value)
        .map_err(|e| format!("Failed to serialize JSON: {}", e))
}

/// Upload and process training template files
#[command]
pub async fn upload_training_template(
    db: State<'_, Database>,
    request: TrainingFileUploadRequest,
) -> Result<TrainingFileUploadResponse, String> {
    let file_id = Uuid::new_v4().to_string();
    
    // Validate file type
    let allowed_types = vec!["application/json", "text/csv", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "application/pdf"];
    if !allowed_types.contains(&request.file_type.as_str()) {
        return Err("Unsupported file type. Only JSON, CSV, Excel, and PDF files are allowed.".to_string());
    }
    
    // Validate file size (max 10MB)
    if request.file_content.len() > 10 * 1024 * 1024 {
        return Err("File size exceeds 10MB limit.".to_string());
    }
    
    // Create uploads directory if it doesn't exist
    let uploads_dir = std::env::current_dir()
        .map_err(|e| e.to_string())?
        .join("uploads")
        .join("templates");
    
    fs::create_dir_all(&uploads_dir)
        .map_err(|e| format!("Failed to create uploads directory: {}", e))?;
    
    // Save file
    let file_extension = Path::new(&request.file_name)
        .extension()
        .and_then(|ext| ext.to_str())
        .unwrap_or("bin");
    
    let file_name = format!("{}.{}", file_id, file_extension);
    let file_path = uploads_dir.join(&file_name);
    
    fs::write(&file_path, &request.file_content)
        .map_err(|e| format!("Failed to save file: {}", e))?;
    
    // Process file based on type
    let processed_data = match request.file_type.as_str() {
        "application/json" => process_json_template(&request.file_content)?,
        "text/csv" => process_csv_template(&request.file_content)?,
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" => {
            process_excel_template(&file_path)?
        }
        "application/pdf" => process_pdf_template(&file_path)?,
        _ => return Err("Unsupported file type for processing.".to_string()),
    };
    
    Ok(TrainingFileUploadResponse {
        success: true,
        file_id,
        file_path: file_path.to_string_lossy().to_string(),
        processed_data: Some(processed_data),
        message: "Template uploaded and processed successfully.".to_string(),
    })
}

/// Upload and process competency matrix files
#[command]
pub async fn upload_competency_matrix(
    db: State<'_, Database>,
    request: TrainingFileUploadRequest,
) -> Result<TrainingFileUploadResponse, String> {
    let file_id = Uuid::new_v4().to_string();
    
    // Validate file type
    let allowed_types = vec!["application/json", "text/csv", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"];
    if !allowed_types.contains(&request.file_type.as_str()) {
        return Err("Unsupported file type. Only JSON, CSV, and Excel files are allowed for competency matrices.".to_string());
    }
    
    // Validate file size (max 5MB)
    if request.file_content.len() > 5 * 1024 * 1024 {
        return Err("File size exceeds 5MB limit.".to_string());
    }
    
    // Create uploads directory
    let uploads_dir = std::env::current_dir()
        .map_err(|e| e.to_string())?
        .join("uploads")
        .join("competency_matrices");
    
    fs::create_dir_all(&uploads_dir)
        .map_err(|e| format!("Failed to create uploads directory: {}", e))?;
    
    // Save file
    let file_extension = Path::new(&request.file_name)
        .extension()
        .and_then(|ext| ext.to_str())
        .unwrap_or("bin");
    
    let file_name = format!("{}.{}", file_id, file_extension);
    let file_path = uploads_dir.join(&file_name);
    
    fs::write(&file_path, &request.file_content)
        .map_err(|e| format!("Failed to save file: {}", e))?;
    
    // Process competency matrix
    let processed_matrix = match request.file_type.as_str() {
        "application/json" => process_json_competency_matrix(&request.file_content)?,
        "text/csv" => process_csv_competency_matrix(&request.file_content)?,
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" => {
            process_excel_competency_matrix(&file_path)?
        }
        _ => return Err("Unsupported file type for competency matrix processing.".to_string()),
    };
    
    // Store in database using the training repository
    let repo = TrainingRepository::new(db.inner().clone());
    let competency_model = crate::training::models::CompetencyModel {
        id: None,
        name: processed_matrix.name.clone(),
        description: processed_matrix.description.clone(),
        category: processed_matrix.category.clone(),
        levels: serde_json::to_string(&processed_matrix.levels)
            .map_err(|e| format!("Failed to serialize levels: {}", e))?,
        business_criticality: processed_matrix.business_criticality.clone(),
        industry_standard: processed_matrix.industry_standard.clone(),
        created_at: chrono::Utc::now().naive_utc(),
        updated_at: chrono::Utc::now().naive_utc(),
    };
    
    repo.create_competency_model(competency_model)
        .await
        .map_err(|e| format!("Failed to save competency model: {}", e))?;
    
    Ok(TrainingFileUploadResponse {
        success: true,
        file_id,
        file_path: file_path.to_string_lossy().to_string(),
        processed_data: Some(serde_json::to_value(processed_matrix).unwrap()),
        message: "Competency matrix uploaded and processed successfully.".to_string(),
    })
}

// Helper functions for processing different file types

fn process_json_template(content: &[u8]) -> Result<serde_json::Value, String> {
    let json_str = String::from_utf8(content.to_vec())
        .map_err(|e| format!("Invalid UTF-8 content: {}", e))?;
    
    serde_json::from_str(&json_str)
        .map_err(|e| format!("Invalid JSON format: {}", e))
}

fn process_csv_template(content: &[u8]) -> Result<serde_json::Value, String> {
    let csv_str = String::from_utf8(content.to_vec())
        .map_err(|e| format!("Invalid UTF-8 content: {}", e))?;
    
    // Parse CSV and convert to JSON structure
    let mut reader = csv::Reader::from_reader(csv_str.as_bytes());
    let mut records = Vec::new();
    
    for result in reader.records() {
        let record = result.map_err(|e| format!("CSV parsing error: {}", e))?;
        let row: Vec<String> = record.iter().map(|field| field.to_string()).collect();
        records.push(row);
    }
    
    Ok(serde_json::json!({
        "type": "csv_template",
        "data": records
    }))
}

fn process_excel_template(file_path: &Path) -> Result<serde_json::Value, String> {
    // For now, return a placeholder. In a real implementation, you'd use a crate like calamine
    Ok(serde_json::json!({
        "type": "excel_template",
        "message": "Excel processing not yet implemented",
        "file_path": file_path.to_string_lossy()
    }))
}

fn process_pdf_template(file_path: &Path) -> Result<serde_json::Value, String> {
    // For now, return a placeholder. In a real implementation, you'd use a PDF processing crate
    Ok(serde_json::json!({
        "type": "pdf_template",
        "message": "PDF processing not yet implemented",
        "file_path": file_path.to_string_lossy()
    }))
}

fn process_json_competency_matrix(content: &[u8]) -> Result<ProcessedCompetencyMatrix, String> {
    let json_str = String::from_utf8(content.to_vec())
        .map_err(|e| format!("Invalid UTF-8 content: {}", e))?;
    
    let json_value: serde_json::Value = serde_json::from_str(&json_str)
        .map_err(|e| format!("Invalid JSON format: {}", e))?;
    
    // Extract competency matrix data from JSON
    let name = json_value["name"].as_str().unwrap_or("Imported Matrix").to_string();
    let description = json_value["description"].as_str().unwrap_or("Imported competency matrix").to_string();
    let category = json_value["category"].as_str().unwrap_or("technical").to_string();
    
    // Create default levels if not provided
    let levels = vec![
        CompetencyLevel {
            level: 1,
            name: "Beginner".to_string(),
            description: "Basic understanding".to_string(),
            behaviors: vec!["Follows established procedures".to_string()],
            assessment_criteria: vec!["Can complete basic tasks with supervision".to_string()],
            development_activities: vec!["Training courses".to_string(), "Mentoring".to_string()],
        },
        CompetencyLevel {
            level: 2,
            name: "Intermediate".to_string(),
            description: "Practical application".to_string(),
            behaviors: vec!["Works independently".to_string()],
            assessment_criteria: vec!["Completes tasks with minimal supervision".to_string()],
            development_activities: vec!["Advanced training".to_string(), "Project assignments".to_string()],
        },
        CompetencyLevel {
            level: 3,
            name: "Advanced".to_string(),
            description: "Expert level proficiency".to_string(),
            behaviors: vec!["Leads others".to_string(), "Innovates processes".to_string()],
            assessment_criteria: vec!["Mentors others".to_string(), "Drives improvement".to_string()],
            development_activities: vec!["Leadership programs".to_string(), "Strategic projects".to_string()],
        },
    ];
    
    Ok(ProcessedCompetencyMatrix {
        id: Uuid::new_v4().to_string(),
        name,
        description,
        category,
        levels,
        business_criticality: "important".to_string(),
        industry_standard: None,
    })
}

fn process_csv_competency_matrix(content: &[u8]) -> Result<ProcessedCompetencyMatrix, String> {
    let csv_str = String::from_utf8(content.to_vec())
        .map_err(|e| format!("Invalid UTF-8 content: {}", e))?;
    
    // Parse CSV and extract competency data
    let mut reader = csv::Reader::from_reader(csv_str.as_bytes());
    let _headers = reader.headers()
        .map_err(|e| format!("Failed to read CSV headers: {}", e))?
        .clone();
    
    // Create a basic competency matrix from CSV data
    let levels = vec![
        CompetencyLevel {
            level: 1,
            name: "Beginner".to_string(),
            description: "Basic level from CSV import".to_string(),
            behaviors: vec!["CSV imported behavior".to_string()],
            assessment_criteria: vec!["CSV imported criteria".to_string()],
            development_activities: vec!["CSV imported activities".to_string()],
        },
    ];
    
    Ok(ProcessedCompetencyMatrix {
        id: Uuid::new_v4().to_string(),
        name: "CSV Imported Matrix".to_string(),
        description: "Competency matrix imported from CSV file".to_string(),
        category: "technical".to_string(),
        levels,
        business_criticality: "important".to_string(),
        industry_standard: None,
    })
}

fn process_excel_competency_matrix(file_path: &Path) -> Result<ProcessedCompetencyMatrix, String> {
    // Placeholder for Excel processing
    Ok(ProcessedCompetencyMatrix {
        id: Uuid::new_v4().to_string(),
        name: "Excel Imported Matrix".to_string(),
        description: "Competency matrix imported from Excel file".to_string(),
        category: "technical".to_string(),
        levels: vec![
            CompetencyLevel {
                level: 1,
                name: "Beginner".to_string(),
                description: "Basic level from Excel import".to_string(),
                behaviors: vec!["Excel imported behavior".to_string()],
                assessment_criteria: vec!["Excel imported criteria".to_string()],
                development_activities: vec!["Excel imported activities".to_string()],
            },
        ],
        business_criticality: "important".to_string(),
        industry_standard: None,
    })
}