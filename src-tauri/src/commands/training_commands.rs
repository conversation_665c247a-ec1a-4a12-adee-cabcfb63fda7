//! Training Commands
//! Tauri commands for training needs analysis, competency models, and analytics

use crate::database::{Database, TrainingRepository};
use crate::training::models::*;
use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tauri::{command, State};

// Training Needs Commands
#[command]
pub async fn create_training_need(
    db: State<'_, Database>,
    training_need: TrainingNeed,
) -> Result<i64, String> {
    let repo = TrainingRepository::new(db.inner().clone());
    repo.create_training_need(&training_need)
        .map_err(|e| e.to_string())
}

#[command]
pub async fn get_training_need(
    db: State<'_, Database>,
    id: i64,
) -> Result<Option<TrainingNeed>, String> {
    let repo = TrainingRepository::new(db.inner().clone());
    repo.get_training_need(id)
        .map_err(|e| e.to_string())
}

#[command]
pub async fn get_training_needs_by_employee(
    db: State<'_, Database>,
    employee_id: String,
) -> Result<Vec<TrainingNeed>, String> {
    let repo = TrainingRepository::new(db.inner().clone());
    repo.get_training_needs_by_employee(&employee_id)
        .map_err(|e| e.to_string())
}

#[command]
pub async fn get_training_needs_by_department(
    db: State<'_, Database>,
    department: String,
) -> Result<Vec<TrainingNeed>, String> {
    let repo = TrainingRepository::new(db.inner().clone());
    repo.get_training_needs_by_department(&department)
        .map_err(|e| e.to_string())
}

#[command]
pub async fn update_training_need(
    db: State<'_, Database>,
    id: i64,
    training_need: TrainingNeed,
) -> Result<(), String> {
    let repo = TrainingRepository::new(db.inner().clone());
    repo.update_training_need(id, &training_need)
        .map_err(|e| e.to_string())
}

#[command]
pub async fn delete_training_need(
    db: State<'_, Database>,
    id: i64,
) -> Result<(), String> {
    let repo = TrainingRepository::new(db.inner().clone());
    repo.delete_training_need(id)
        .map_err(|e| e.to_string())
}

// Training Programs Commands
#[command]
pub async fn create_training_program(
    db: State<'_, Database>,
    program: TrainingProgram,
) -> Result<i64, String> {
    let repo = TrainingRepository::new(db.inner().clone());
    repo.create_training_program(&program)
        .map_err(|e| e.to_string())
}

#[command]
pub async fn get_training_programs(
    db: State<'_, Database>,
) -> Result<Vec<TrainingProgram>, String> {
    let repo = TrainingRepository::new(db.inner().clone());
    repo.get_training_programs()
        .map_err(|e| e.to_string())
}

#[command]
pub async fn get_training_programs_by_category(
    db: State<'_, Database>,
    category: TrainingCategory,
) -> Result<Vec<TrainingProgram>, String> {
    let repo = TrainingRepository::new(db.inner().clone());
    repo.get_training_programs_by_category(&category)
        .map_err(|e| e.to_string())
}

// Graduate Trainee Programs Commands
#[command]
pub async fn create_graduate_trainee_program(
    db: State<'_, Database>,
    program: GraduateTraineeProgram,
) -> Result<i64, String> {
    let repo = TrainingRepository::new(db.inner().clone());
    repo.create_graduate_trainee_program(&program)
        .map_err(|e| e.to_string())
}

#[command]
pub async fn get_graduate_trainee_programs(
    db: State<'_, Database>,
) -> Result<Vec<GraduateTraineeProgram>, String> {
    let repo = TrainingRepository::new(db.inner().clone());
    repo.get_graduate_trainee_programs()
        .map_err(|e| e.to_string())
}

#[command]
pub async fn get_graduate_trainee_program_by_employee(
    db: State<'_, Database>,
    employee_id: String,
) -> Result<Option<GraduateTraineeProgram>, String> {
    let repo = TrainingRepository::new(db.inner().clone());
    repo.get_graduate_trainee_program_by_employee(&employee_id)
        .map_err(|e| e.to_string())
}

// Analytics Commands
#[command]
pub async fn get_training_metrics(
    db: State<'_, Database>,
) -> Result<TrainingMetrics, String> {
    let repo = TrainingRepository::new(db.inner().clone());
    repo.get_training_metrics()
        .map_err(|e| e.to_string())
}

// Competency Models Commands
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CompetencyModelData {
    pub id: Option<i64>,
    pub name: String,
    pub description: Option<String>,
    pub category: String,
    pub department: Option<String>,
    pub role_level: Option<String>,
    pub competencies: Vec<CompetencyData>,
    pub business_criticality: Option<String>,
    pub industry_standard: Option<String>,
    pub version: String,
    pub is_active: bool,
    pub created_by: Option<String>,
    pub approved_by: Option<String>,
    pub approval_date: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CompetencyData {
    pub name: String,
    pub description: String,
    pub category: String,
    pub required_level: i32,
    pub current_level: Option<i32>,
    pub importance: String,
    pub behavioral_indicators: Vec<String>,
}

#[command]
pub async fn create_competency_model(
    db: State<'_, Database>,
    model: CompetencyModelData,
) -> Result<i64, String> {
    db.with_connection(|conn| {
        let mut stmt = conn.prepare(
            "INSERT INTO competency_models (
                name, description, category, department, role_level, competencies,
                business_criticality, industry_standard, version, is_active,
                created_by, approved_by, approval_date
            ) VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10, ?11, ?12, ?13)"
        )?;

        stmt.execute(rusqlite::params![
            model.name,
            model.description,
            model.category,
            model.department,
            model.role_level,
            serde_json::to_string(&model.competencies)?,
            model.business_criticality,
            model.industry_standard,
            model.version,
            model.is_active,
            model.created_by,
            model.approved_by,
            model.approval_date
        ])?;

        Ok(conn.last_insert_rowid())
    })
    .map_err(|e| e.to_string())
}

#[command]
pub async fn get_competency_models(
    db: State<'_, Database>,
) -> Result<Vec<CompetencyModelData>, String> {
    db.with_connection(|conn| {
        let mut stmt = conn.prepare(
            "SELECT * FROM competency_models WHERE is_active = 1 ORDER BY name"
        )?;

        let rows = stmt.query_map([], |row| {
            let competencies_json: String = row.get("competencies")?;
            let competencies: Vec<CompetencyData> = serde_json::from_str(&competencies_json)
                .unwrap_or_default();

            Ok(CompetencyModelData {
                id: Some(row.get("id")?),
                name: row.get("name")?,
                description: row.get("description")?,
                category: row.get("category")?,
                department: row.get("department")?,
                role_level: row.get("role_level")?,
                competencies,
                business_criticality: row.get("business_criticality")?,
                industry_standard: row.get("industry_standard")?,
                version: row.get("version")?,
                is_active: row.get("is_active")?,
                created_by: row.get("created_by")?,
                approved_by: row.get("approved_by")?,
                approval_date: row.get("approval_date")?,
            })
        })?;

        let mut models = Vec::new();
        for row in rows {
            models.push(row?);
        }

        Ok(models)
    })
    .map_err(|e| e.to_string())
}

// Skill Matrix Commands
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SkillMatrixData {
    pub id: Option<i64>,
    pub name: String,
    pub description: Option<String>,
    pub department: Option<String>,
    pub role: Option<String>,
    pub skills: Vec<SkillData>,
    pub proficiency_levels: Vec<ProficiencyLevel>,
    pub assessment_criteria: Option<AssessmentCriteria>,
    pub created_by: Option<String>,
    pub is_template: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SkillData {
    pub name: String,
    pub category: String,
    pub description: String,
    pub required_level: i32,
    pub current_level: Option<i32>,
    pub importance: String,
    pub learning_resources: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProficiencyLevel {
    pub level: i32,
    pub name: String,
    pub description: String,
    pub criteria: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AssessmentCriteria {
    pub method: String,
    pub frequency: String,
    pub assessors: Vec<String>,
    pub scoring_rubric: HashMap<String, String>,
}

#[command]
pub async fn create_skill_matrix(
    db: State<'_, Database>,
    matrix: SkillMatrixData,
) -> Result<i64, String> {
    db.with_connection(|conn| {
        let mut stmt = conn.prepare(
            "INSERT INTO skill_matrices (
                name, description, department, role, skills, proficiency_levels,
                assessment_criteria, created_by, is_template
            ) VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9)"
        )?;

        stmt.execute(rusqlite::params![
            matrix.name,
            matrix.description,
            matrix.department,
            matrix.role,
            serde_json::to_string(&matrix.skills)?,
            serde_json::to_string(&matrix.proficiency_levels)?,
            serde_json::to_string(&matrix.assessment_criteria)?,
            matrix.created_by,
            matrix.is_template
        ])?;

        Ok(conn.last_insert_rowid())
    })
    .map_err(|e| e.to_string())
}

#[command]
pub async fn get_skill_matrices(
    db: State<'_, Database>,
) -> Result<Vec<SkillMatrixData>, String> {
    db.with_connection(|conn| {
        let mut stmt = conn.prepare(
            "SELECT * FROM skill_matrices ORDER BY name"
        )?;

        let rows = stmt.query_map([], |row| {
            let skills_json: String = row.get("skills")?;
            let proficiency_levels_json: String = row.get("proficiency_levels")?;
            let assessment_criteria_json: Option<String> = row.get("assessment_criteria")?;

            let skills: Vec<SkillData> = serde_json::from_str(&skills_json)
                .unwrap_or_default();
            let proficiency_levels: Vec<ProficiencyLevel> = serde_json::from_str(&proficiency_levels_json)
                .unwrap_or_default();
            let assessment_criteria: Option<AssessmentCriteria> = assessment_criteria_json
                .and_then(|json| serde_json::from_str(&json).ok());

            Ok(SkillMatrixData {
                id: Some(row.get("id")?),
                name: row.get("name")?,
                description: row.get("description")?,
                department: row.get("department")?,
                role: row.get("role")?,
                skills,
                proficiency_levels,
                assessment_criteria,
                created_by: row.get("created_by")?,
                is_template: row.get("is_template")?,
            })
        })?;

        let mut matrices = Vec::new();
        for row in rows {
            matrices.push(row?);
        }

        Ok(matrices)
    })
    .map_err(|e| e.to_string())
}

#[command]
pub async fn get_skill_matrix_templates(
    db: State<'_, Database>,
) -> Result<Vec<SkillMatrixData>, String> {
    db.with_connection(|conn| {
        let mut stmt = conn.prepare(
            "SELECT * FROM skill_matrices WHERE is_template = 1 ORDER BY name"
        )?;

        let rows = stmt.query_map([], |row| {
            let skills_json: String = row.get("skills")?;
            let proficiency_levels_json: String = row.get("proficiency_levels")?;
            let assessment_criteria_json: Option<String> = row.get("assessment_criteria")?;

            let skills: Vec<SkillData> = serde_json::from_str(&skills_json)
                .unwrap_or_default();
            let proficiency_levels: Vec<ProficiencyLevel> = serde_json::from_str(&proficiency_levels_json)
                .unwrap_or_default();
            let assessment_criteria: Option<AssessmentCriteria> = assessment_criteria_json
                .and_then(|json| serde_json::from_str(&json).ok());

            Ok(SkillMatrixData {
                id: Some(row.get("id")?),
                name: row.get("name")?,
                description: row.get("description")?,
                department: row.get("department")?,
                role: row.get("role")?,
                skills,
                proficiency_levels,
                assessment_criteria,
                created_by: row.get("created_by")?,
                is_template: row.get("is_template")?,
            })
        })?;

        let mut templates = Vec::new();
        for row in rows {
            templates.push(row?);
        }

        Ok(templates)
    })
    .map_err(|e| e.to_string())
}

// Assessment Commands
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AssessmentData {
    pub id: Option<i64>,
    pub employee_id: String,
    pub competency_id: Option<i64>,
    pub skill_id: String,
    pub score: f64,
    pub max_score: f64,
    pub assessment_date: String,
    pub assessor_id: String,
    pub assessment_type: String,
    pub notes: Option<String>,
}

#[command]
pub async fn create_assessment(
    db: State<'_, Database>,
    assessment: AssessmentData,
) -> Result<i64, String> {
    db.with_connection(|conn| {
        let mut stmt = conn.prepare(
            "INSERT INTO employee_assessments (
                employee_id, competency_id, skill_id, score, max_score,
                assessment_date, assessor_id, assessment_type, notes
            ) VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9)"
        )?;

        stmt.execute(rusqlite::params![
            assessment.employee_id,
            assessment.competency_id,
            assessment.skill_id,
            assessment.score,
            assessment.max_score,
            assessment.assessment_date,
            assessment.assessor_id,
            assessment.assessment_type,
            assessment.notes
        ])?;

        Ok(conn.last_insert_rowid())
    })
    .map_err(|e| e.to_string())
}

#[command]
pub async fn get_assessments_by_employee(
    db: State<'_, Database>,
    employee_id: String,
) -> Result<Vec<AssessmentData>, String> {
    db.with_connection(|conn| {
        let mut stmt = conn.prepare(
            "SELECT * FROM employee_assessments WHERE employee_id = ?1 ORDER BY assessment_date DESC"
        )?;

        let rows = stmt.query_map([employee_id], |row| {
            Ok(AssessmentData {
                id: Some(row.get("id")?),
                employee_id: row.get("employee_id")?,
                competency_id: row.get("competency_id")?,
                skill_id: row.get("skill_id")?,
                score: row.get("score")?,
                max_score: row.get("max_score")?,
                assessment_date: row.get("assessment_date")?,
                assessor_id: row.get("assessor_id")?,
                assessment_type: row.get("assessment_type")?,
                notes: row.get("notes")?,
            })
        })?;

        let mut assessments = Vec::new();
        for row in rows {
            assessments.push(row?);
        }

        Ok(assessments)
    })
    .map_err(|e| e.to_string())
}