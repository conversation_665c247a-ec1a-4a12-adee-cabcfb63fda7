// Beacon Becon Database Implementation
// This module provides proper database integration for the Beacon knowledge management system

use anyhow::Result;
use rusqlite::{params, Connection, OptionalExtension};
use serde::{Deserialize, Serialize};
use std::sync::Mutex;
use std::time::{SystemTime, UNIX_EPOCH};

// Thread-safe database connection
lazy_static::lazy_static! {
    static ref DB_CONNECTION: Mutex<Option<Connection>> = Mutex::new(None);
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct KnowledgeItem {
    pub id: String,
    pub title: String,
    pub content: String,
    pub tags: Vec<String>,
    pub created_at: u64,
    pub updated_at: u64,
    pub source_type: Option<String>,
    pub source_id: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SaveContentRequest {
    pub title: String,
    pub content: String,
    pub tags: Option<Vec<String>>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct UserCountResponse {
    pub count: u32,
}

// Initialize database connection
pub fn init_database() -> Result<()> {
    let conn = Connection::open("beacon.db")?;
    
    // Execute schema creation
    conn.execute_batch(include_str!("../../db/db_beacon_schema.sql"))?;
    
    // Store connection in global state
    let mut db = DB_CONNECTION.lock().unwrap();
    *db = Some(conn);
    
    Ok(())
}

// Get database connection
fn get_db() -> Result<std::sync::MutexGuard<'static, Option<Connection>>, String> {
    DB_CONNECTION.lock()
        .map_err(|_| "Failed to access database".to_string())
}

// Get user count (count of knowledge items as a metric)
#[tauri::command]
pub fn get_user_count() -> Result<UserCountResponse, String> {
    let db = get_db()?;
    let conn = db.as_ref().ok_or("Database not initialized")?;
    
    let count: i64 = conn
        .query_row(
            "SELECT COUNT(*) FROM beacon_knowledge_items",
            [],
            |row| row.get(0),
        )
        .unwrap_or(0);
    
    // Add a base count for demo purposes
    let total_count = 13523 + count as u32;
    
    Ok(UserCountResponse { count: total_count })
}

// Save content to database
#[tauri::command]
pub fn save_content(request: SaveContentRequest) -> Result<KnowledgeItem, String> {
    let db = get_db()?;
    let conn = db.as_ref().ok_or("Database not initialized")?;
    
    let id = format!("item_{}", uuid::Uuid::new_v4());
    let now = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .map_err(|_| "Failed to get timestamp".to_string())?
        .as_secs();
    
    let tags_json = serde_json::to_string(&request.tags.unwrap_or_default())
        .map_err(|e| format!("Failed to serialize tags: {}", e))?;
    
    // Insert into database
    conn.execute(
        "INSERT INTO beacon_knowledge_items (id, title, content, tags, source_type, created_at, updated_at) 
         VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7)",
        params![
            &id,
            &request.title,
            &request.content,
            &tags_json,
            "manual",
            now as i64,
            now as i64
        ],
    ).map_err(|e| format!("Failed to save content: {}", e))?;
    
    let item = KnowledgeItem {
        id: id.clone(),
        title: request.title,
        content: request.content,
        tags: request.tags.unwrap_or_default(),
        created_at: now,
        updated_at: now,
        source_type: Some("manual".to_string()),
        source_id: None,
    };
    
    Ok(item)
}

// Get all content from database
#[tauri::command]
pub fn get_all_content() -> Result<Vec<KnowledgeItem>, String> {
    let db = get_db()?;
    let conn = db.as_ref().ok_or("Database not initialized")?;
    
    let mut stmt = conn
        .prepare(
            "SELECT id, title, content, tags, created_at, updated_at, source_type, source_id 
             FROM beacon_knowledge_items 
             ORDER BY created_at DESC"
        )
        .map_err(|e| format!("Failed to prepare statement: {}", e))?;
    
    let items = stmt
        .query_map([], |row| {
            let tags_json: String = row.get(3)?;
            let tags: Vec<String> = serde_json::from_str(&tags_json).unwrap_or_default();
            
            Ok(KnowledgeItem {
                id: row.get(0)?,
                title: row.get(1)?,
                content: row.get(2)?,
                tags,
                created_at: row.get::<_, i64>(4)? as u64,
                updated_at: row.get::<_, i64>(5)? as u64,
                source_type: row.get(6)?,
                source_id: row.get(7)?,
            })
        })
        .map_err(|e| format!("Failed to query items: {}", e))?
        .collect::<Result<Vec<_>, _>>()
        .map_err(|e| format!("Failed to collect items: {}", e))?;
    
    Ok(items)
}

// Get content by ID
#[tauri::command]
pub fn get_content_by_id(id: String) -> Result<Option<KnowledgeItem>, String> {
    let db = get_db()?;
    let conn = db.as_ref().ok_or("Database not initialized")?;
    
    let result = conn
        .query_row(
            "SELECT id, title, content, tags, created_at, updated_at, source_type, source_id 
             FROM beacon_knowledge_items 
             WHERE id = ?1",
            params![&id],
            |row| {
                let tags_json: String = row.get(3)?;
                let tags: Vec<String> = serde_json::from_str(&tags_json).unwrap_or_default();
                
                Ok(KnowledgeItem {
                    id: row.get(0)?,
                    title: row.get(1)?,
                    content: row.get(2)?,
                    tags,
                    created_at: row.get::<_, i64>(4)? as u64,
                    updated_at: row.get::<_, i64>(5)? as u64,
                    source_type: row.get(6)?,
                    source_id: row.get(7)?,
                })
            },
        )
        .optional()
        .map_err(|e| format!("Failed to get content: {}", e))?;
    
    Ok(result)
}

// Delete content
#[tauri::command]
pub fn delete_content(id: String) -> Result<(), String> {
    let db = get_db()?;
    let conn = db.as_ref().ok_or("Database not initialized")?;
    
    conn.execute(
        "DELETE FROM beacon_knowledge_items WHERE id = ?1",
        params![&id],
    )
    .map_err(|e| format!("Failed to delete content: {}", e))?;
    
    Ok(())
}

// Search content with full-text search
#[tauri::command]
pub fn search_content(query: String) -> Result<Vec<KnowledgeItem>, String> {
    let db = get_db()?;
    let conn = db.as_ref().ok_or("Database not initialized")?;
    
    let mut stmt = conn
        .prepare(
            "SELECT k.id, k.title, k.content, k.tags, k.created_at, k.updated_at, k.source_type, k.source_id
             FROM beacon_knowledge_items k
             JOIN beacon_knowledge_items_fts fts ON k.rowid = fts.rowid
             WHERE beacon_knowledge_items_fts MATCH ?1
             ORDER BY rank"
        )
        .map_err(|e| format!("Failed to prepare search statement: {}", e))?;
    
    let items = stmt
        .query_map([&query], |row| {
            let tags_json: String = row.get(3)?;
            let tags: Vec<String> = serde_json::from_str(&tags_json).unwrap_or_default();
            
            Ok(KnowledgeItem {
                id: row.get(0)?,
                title: row.get(1)?,
                content: row.get(2)?,
                tags,
                created_at: row.get::<_, i64>(4)? as u64,
                updated_at: row.get::<_, i64>(5)? as u64,
                source_type: row.get(6)?,
                source_id: row.get(7)?,
            })
        })
        .map_err(|e| format!("Failed to search items: {}", e))?
        .collect::<Result<Vec<_>, _>>()
        .map_err(|e| format!("Failed to collect search results: {}", e))?;
    
    Ok(items)
}

// Update content
#[tauri::command]
pub fn update_content(id: String, title: String, content: String, tags: Vec<String>) -> Result<KnowledgeItem, String> {
    let db = get_db()?;
    let conn = db.as_ref().ok_or("Database not initialized")?;
    
    let now = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .map_err(|_| "Failed to get timestamp".to_string())?
        .as_secs();
    
    let tags_json = serde_json::to_string(&tags)
        .map_err(|e| format!("Failed to serialize tags: {}", e))?;
    
    conn.execute(
        "UPDATE beacon_knowledge_items 
         SET title = ?1, content = ?2, tags = ?3, updated_at = ?4 
         WHERE id = ?5",
        params![&title, &content, &tags_json, now as i64, &id],
    )
    .map_err(|e| format!("Failed to update content: {}", e))?;
    
    // Return updated item
    get_content_by_id(id.clone())?.ok_or("Item not found after update".to_string())
}

// Get all sources
#[tauri::command]
pub fn get_all_sources() -> Result<Vec<serde_json::Value>, String> {
    let db = get_db()?;
    let conn = db.as_ref().ok_or("Database not initialized")?;
    
    let mut stmt = conn
        .prepare(
            "SELECT id, name, type, content, url, file_path, metadata, uploaded_at, processed_at, status 
             FROM beacon_sources 
             ORDER BY uploaded_at DESC"
        )
        .map_err(|e| format!("Failed to prepare statement: {}", e))?;
    
    let sources = stmt
        .query_map([], |row| {
            Ok(serde_json::json!({
                "id": row.get::<_, String>(0)?,
                "name": row.get::<_, String>(1)?,
                "type": row.get::<_, String>(2)?,
                "content": row.get::<_, Option<String>>(3)?,
                "url": row.get::<_, Option<String>>(4)?,
                "file_path": row.get::<_, Option<String>>(5)?,
                "metadata": row.get::<_, Option<String>>(6)?,
                "uploaded_at": row.get::<_, i64>(7)?,
                "processed_at": row.get::<_, Option<i64>>(8)?,
                "status": row.get::<_, String>(9)?
            }))
        })
        .map_err(|e| format!("Failed to query sources: {}", e))?
        .collect::<Result<Vec<_>, _>>()
        .map_err(|e| format!("Failed to collect sources: {}", e))?;
    
    Ok(sources)
}

// Generate AI tags (placeholder for now)
#[tauri::command]
pub fn generate_tags(content: String, title: Option<String>, max_tags: Option<usize>) -> Result<Vec<String>, String> {
    // For now, return simple keyword extraction
    let text = format!("{} {}", title.unwrap_or_default(), content).to_lowercase();
    
    let keywords = vec![
        ("javascript", "programming"),
        ("python", "programming"),
        ("rust", "programming"),
        ("react", "frontend"),
        ("database", "backend"),
        ("api", "backend"),
        ("design", "ui/ux"),
        ("meeting", "business"),
        ("todo", "task"),
        ("idea", "brainstorm"),
    ];
    
    let mut tags = Vec::new();
    for (keyword, tag) in keywords {
        if text.contains(keyword) && !tags.contains(&tag.to_string()) {
            tags.push(tag.to_string());
        }
    }
    
    // Limit tags
    let max = max_tags.unwrap_or(5);
    tags.truncate(max);
    
    Ok(tags)
}