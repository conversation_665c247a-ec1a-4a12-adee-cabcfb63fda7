#[cfg(test)]
mod tests {
    use super::*;
    use std::sync::Mutex;
    use std::collections::HashMap;

    #[test]
    fn test_save_content() {
        // Reset the knowledge base for testing
        *KNOWLEDGE_BASE.lock().unwrap() = HashMap::new();
        
        let request = SaveContentRequest {
            title: "Test Item".to_string(),
            content: "This is a test item".to_string(),
            tags: Some(vec!["test".to_string()]),
        };
        
        let result = save_content(request);
        assert!(result.is_ok());
        
        let saved_item = result.unwrap();
        assert_eq!(saved_item.title, "Test Item");
        assert_eq!(saved_item.content, "This is a test item");
        assert_eq!(saved_item.tags, vec!["test"]);
    }

    #[test]
    fn test_get_all_content() {
        // Reset the knowledge base for testing
        *KNOWLEDGE_BASE.lock().unwrap() = HashMap::new();
        
        // Add a test item
        let request = SaveContentRequest {
            title: "Test Item".to_string(),
            content: "This is a test item".to_string(),
            tags: Some(vec!["test".to_string()]),
        };
        
        let _ = save_content(request);
        
        // Get all content
        let result = get_all_content();
        assert!(result.is_ok());
        
        let content = result.unwrap();
        assert_eq!(content.len(), 1);
        assert_eq!(content[0].title, "Test Item");
    }

    #[test]
    fn test_chat_with_ai() {
        let request = ChatRequest {
            message: "Hello, AI!".to_string(),
            context_ids: None,
        };
        
        let result = chat_with_ai(request);
        assert!(result.is_ok());
        
        let response = result.unwrap();
        assert!(!response.content.is_empty());
        assert!(!response.is_user);
    }

    #[test]
    fn test_get_user_count() {
        let initial_count = USER_COUNT.lock().unwrap().clone();
        let result = get_user_count();
        assert!(result.is_ok());
        
        let response = result.unwrap();
        assert_eq!(response.count, initial_count);
    }
}