use anyhow::{Context, Result};
use serde::{Deserialize, Serialize};
use std::fs;
use std::io::{<PERSON><PERSON><PERSON><PERSON>, <PERSON>ufReader};
use std::path::PathBuf;
use std::process::Stdio;
use std::sync::Arc;
use std::time::SystemTime;
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Emitter, Manager};
use tokio::process::{Child, Command};
use tokio::sync::Mutex;
use rusqlite::OptionalExtension;


/// Global state to track current Claude process
pub struct ClaudeProcessState {
    pub current_process: Arc<Mutex<Option<Child>>>,
}

impl Default for ClaudeProcessState {
    fn default() -> Self {
        Self {
            current_process: Arc::new(Mutex::new(None)),
        }
    }
}

/// Represents a project in the ~/.claude/projects directory
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Project {
    /// The project ID (derived from the directory name)
    pub id: String,
    /// The original project path (decoded from the directory name)
    pub path: String,
    /// List of session IDs (JSONL file names without extension)
    pub sessions: Vec<String>,
    /// Unix timestamp when the project directory was created
    pub created_at: u64,
}

/// Represents a session with its metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Session {
    /// The session ID (UUID)
    pub id: String,
    /// The project ID this session belongs to
    pub project_id: String,
    /// The project path
    pub project_path: String,
    /// Optional todo data associated with this session
    pub todo_data: Option<serde_json::Value>,
    /// Unix timestamp when the session file was created
    pub created_at: u64,
    /// First user message content (if available)
    pub first_message: Option<String>,
    /// Timestamp of the first user message (if available)
    pub message_timestamp: Option<String>,
}

/// Represents a message entry in the JSONL file
#[derive(Debug, Deserialize)]
struct JsonlEntry {
    #[serde(rename = "type")]
    #[allow(dead_code)]
    entry_type: Option<String>,
    message: Option<MessageContent>,
    timestamp: Option<String>,
}

/// Represents the message content
#[derive(Debug, Deserialize)]
struct MessageContent {
    role: Option<String>,
    content: Option<String>,
}

/// Represents the settings from ~/.claude/settings.json
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ClaudeSettings {
    #[serde(flatten)]
    pub data: serde_json::Value,
}

impl Default for ClaudeSettings {
    fn default() -> Self {
        Self {
            data: serde_json::json!({}),
        }
    }
}

/// Represents the Claude Code version status
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ClaudeVersionStatus {
    /// Whether Claude Code is installed and working
    pub is_installed: bool,
    /// The version string if available
    pub version: Option<String>,
    /// The full output from the command
    pub output: String,
}

/// Represents a CLAUDE.md file found in the project
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ClaudeMdFile {
    /// Relative path from the project root
    pub relative_path: String,
    /// Absolute path to the file
    pub absolute_path: String,
    /// File size in bytes
    pub size: u64,
    /// Last modified timestamp
    pub modified: u64,
}

/// Represents a discovered and indexed context file
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ContextFile {
    pub id: Option<i32>,
    pub file_path: String,
    pub project_root: String,
    pub relative_path: String,
    pub content_hash: String,
    pub parsed_content: Option<ParsedContext>,
    pub template_type: Option<String>,
    pub parent_context_id: Option<i32>,
    pub is_active: bool,
    pub created_at: String,
    pub updated_at: String,
    pub last_accessed_at: Option<String>,
    pub access_count: i32,
}

/// Represents parsed context content with structured data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ParsedContext {
    pub sections: Vec<ContextSection>,
    pub metadata: ContextMetadata,
    pub variables: std::collections::HashMap<String, String>,
    pub inheritance: Vec<String>, // Parent context references
}

/// A section within a context file
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ContextSection {
    pub title: String,
    pub content: String,
    pub section_type: String, // system_prompt, instructions, examples, etc.
    pub priority: i32,
}

/// Metadata extracted from context files
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ContextMetadata {
    pub title: Option<String>,
    pub description: Option<String>,
    pub version: Option<String>,
    pub author: Option<String>,
    pub tags: Vec<String>,
    pub language: Option<String>,
    pub framework: Option<String>,
}

/// Context template for different project types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ContextTemplate {
    pub id: Option<i32>,
    pub name: String,
    pub description: Option<String>,
    pub category: String,
    pub template_content: String,
    pub schema_definition: Option<String>,
    pub variables: Vec<TemplateVariable>,
    pub is_default: bool,
    pub sort_order: i32,
    pub created_at: String,
    pub updated_at: String,
}

/// Variable definition for context templates
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TemplateVariable {
    pub name: String,
    pub description: String,
    pub var_type: String, // string, number, boolean, array
    pub default_value: Option<String>,
    pub required: bool,
}

/// Context history entry for versioning
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ContextHistory {
    pub id: i32,
    pub context_id: i32,
    pub version: i32,
    pub content_hash: String,
    pub content: String,
    pub change_description: Option<String>,
    pub changed_by: Option<String>,
    pub created_at: String,
}

/// Context inheritance relationship
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ContextInheritance {
    pub id: i32,
    pub child_context_id: i32,
    pub parent_context_id: i32,
    pub inheritance_type: String,
    pub priority: i32,
    pub created_at: String,
}

/// Represents a file or directory entry
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileEntry {
    /// The name of the file or directory
    pub name: String,
    /// The full path
    pub path: String,
    /// Whether this is a directory
    pub is_directory: bool,
    /// File size in bytes (0 for directories)
    pub size: u64,
    /// File extension (if applicable)
    pub extension: Option<String>,
}


/// Gets the path to the ~/.claude directory
fn get_claude_dir() -> Result<PathBuf> {
    dirs::home_dir()
        .context("Could not find home directory")?
        .join(".claude")
        .canonicalize()
        .context("Could not find ~/.claude directory")
}

/// Gets the actual project path by reading the cwd from the first JSONL entry
fn get_project_path_from_sessions(project_dir: &PathBuf) -> Result<String, String> {
    // Try to read any JSONL file in the directory
    let entries = fs::read_dir(project_dir)
        .map_err(|e| format!("Failed to read project directory: {}", e))?;

    for entry in entries {
        if let Ok(entry) = entry {
            let path = entry.path();
            if path.is_file() && path.extension().and_then(|s| s.to_str()) == Some("jsonl") {
                // Read the first line of the JSONL file
                if let Ok(file) = fs::File::open(&path) {
                    let reader = BufReader::new(file);
                    if let Some(Ok(first_line)) = reader.lines().next() {
                        // Parse the JSON and extract cwd
                        if let Ok(json) = serde_json::from_str::<serde_json::Value>(&first_line) {
                            if let Some(cwd) = json.get("cwd").and_then(|v| v.as_str()) {
                                return Ok(cwd.to_string());
                            }
                        }
                    }
                }
            }
        }
    }

    Err("Could not determine project path from session files".to_string())
}

/// Decodes a project directory name back to its original path
/// The directory names in ~/.claude/projects are encoded paths
/// DEPRECATED: Use get_project_path_from_sessions instead when possible
fn decode_project_path(encoded: &str) -> String {
    // This is a fallback - the encoding isn't reversible when paths contain hyphens
    // For example: -Users-mufeedvh-dev-jsonl-viewer could be /Users/<USER>/dev/jsonl-viewer
    // or /Users/<USER>/dev/jsonl/viewer
    encoded.replace('-', "/")
}

/// Extracts the first valid user message from a JSONL file
fn extract_first_user_message(jsonl_path: &PathBuf) -> (Option<String>, Option<String>) {
    let file = match fs::File::open(jsonl_path) {
        Ok(file) => file,
        Err(_) => return (None, None),
    };

    let reader = BufReader::new(file);

    for line in reader.lines() {
        if let Ok(line) = line {
            if let Ok(entry) = serde_json::from_str::<JsonlEntry>(&line) {
                if let Some(message) = entry.message {
                    if message.role.as_deref() == Some("user") {
                        if let Some(content) = message.content {
                            // Skip if it contains the caveat message
                            if content.contains("Caveat: The messages below were generated by the user while running local commands") {
                                continue;
                            }

                            // Skip if it starts with command tags
                            if content.starts_with("<command-name>")
                                || content.starts_with("<local-command-stdout>")
                            {
                                continue;
                            }

                            // Found a valid user message
                            return (Some(content), entry.timestamp);
                        }
                    }
                }
            }
        }
    }

    (None, None)
}

/// Creates a system binary command with the given arguments
pub fn create_system_command(
    claude_path: &str,
    args: Vec<String>,
    project_path: &str,
) -> Command {
    let mut cmd = crate::claude_binary::create_tokio_command_with_env(claude_path);
    
    // Add all arguments
    for arg in args {
        cmd.arg(arg);
    }
    
    cmd.current_dir(project_path)
        .stdout(Stdio::piped())
        .stderr(Stdio::piped());
    
    cmd
}

/// Lists all projects in the ~/.claude/projects directory
#[tauri::command]
pub async fn list_projects() -> Result<Vec<Project>, String> {
    log::info!("Listing projects from ~/.claude/projects");

    let claude_dir = get_claude_dir().map_err(|e| e.to_string())?;
    let projects_dir = claude_dir.join("projects");

    if !projects_dir.exists() {
        log::warn!("Projects directory does not exist: {:?}", projects_dir);
        return Ok(Vec::new());
    }

    let mut projects = Vec::new();

    // Read all directories in the projects folder
    let entries = fs::read_dir(&projects_dir)
        .map_err(|e| format!("Failed to read projects directory: {}", e))?;

    for entry in entries {
        let entry = entry.map_err(|e| format!("Failed to read directory entry: {}", e))?;
        let path = entry.path();

        if path.is_dir() {
            let dir_name = path
                .file_name()
                .and_then(|n| n.to_str())
                .ok_or_else(|| "Invalid directory name".to_string())?;

            // Get directory creation time
            let metadata = fs::metadata(&path)
                .map_err(|e| format!("Failed to read directory metadata: {}", e))?;

            let created_at = metadata
                .created()
                .or_else(|_| metadata.modified())
                .unwrap_or(SystemTime::UNIX_EPOCH)
                .duration_since(SystemTime::UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs();

            // Get the actual project path from JSONL files
            let project_path = match get_project_path_from_sessions(&path) {
                Ok(path) => path,
                Err(e) => {
                    log::warn!("Failed to get project path from sessions for {}: {}, falling back to decode", dir_name, e);
                    decode_project_path(dir_name)
                }
            };

            // List all JSONL files (sessions) in this project directory
            let mut sessions = Vec::new();
            if let Ok(session_entries) = fs::read_dir(&path) {
                for session_entry in session_entries.flatten() {
                    let session_path = session_entry.path();
                    if session_path.is_file()
                        && session_path.extension().and_then(|s| s.to_str()) == Some("jsonl")
                    {
                        if let Some(session_id) = session_path.file_stem().and_then(|s| s.to_str())
                        {
                            sessions.push(session_id.to_string());
                        }
                    }
                }
            }

            projects.push(Project {
                id: dir_name.to_string(),
                path: project_path,
                sessions,
                created_at,
            });
        }
    }

    // Sort projects by creation time (newest first)
    projects.sort_by(|a, b| b.created_at.cmp(&a.created_at));

    log::info!("Found {} projects", projects.len());
    Ok(projects)
}

/// Gets sessions for a specific project
#[tauri::command]
pub async fn get_project_sessions(project_id: String) -> Result<Vec<Session>, String> {
    log::info!("Getting sessions for project: {}", project_id);

    let claude_dir = get_claude_dir().map_err(|e| e.to_string())?;
    let project_dir = claude_dir.join("projects").join(&project_id);
    let todos_dir = claude_dir.join("todos");

    if !project_dir.exists() {
        return Err(format!("Project directory not found: {}", project_id));
    }

    // Get the actual project path from JSONL files
    let project_path = match get_project_path_from_sessions(&project_dir) {
        Ok(path) => path,
        Err(e) => {
            log::warn!(
                "Failed to get project path from sessions for {}: {}, falling back to decode",
                project_id,
                e
            );
            decode_project_path(&project_id)
        }
    };

    let mut sessions = Vec::new();

    // Read all JSONL files in the project directory
    let entries = fs::read_dir(&project_dir)
        .map_err(|e| format!("Failed to read project directory: {}", e))?;

    for entry in entries {
        let entry = entry.map_err(|e| format!("Failed to read directory entry: {}", e))?;
        let path = entry.path();

        if path.is_file() && path.extension().and_then(|s| s.to_str()) == Some("jsonl") {
            if let Some(session_id) = path.file_stem().and_then(|s| s.to_str()) {
                // Get file creation time
                let metadata = fs::metadata(&path)
                    .map_err(|e| format!("Failed to read file metadata: {}", e))?;

                let created_at = metadata
                    .created()
                    .or_else(|_| metadata.modified())
                    .unwrap_or(SystemTime::UNIX_EPOCH)
                    .duration_since(SystemTime::UNIX_EPOCH)
                    .unwrap_or_default()
                    .as_secs();

                // Extract first user message and timestamp
                let (first_message, message_timestamp) = extract_first_user_message(&path);

                // Try to load associated todo data
                let todo_path = todos_dir.join(format!("{}.json", session_id));
                let todo_data = if todo_path.exists() {
                    fs::read_to_string(&todo_path)
                        .ok()
                        .and_then(|content| serde_json::from_str(&content).ok())
                } else {
                    None
                };

                sessions.push(Session {
                    id: session_id.to_string(),
                    project_id: project_id.clone(),
                    project_path: project_path.clone(),
                    todo_data,
                    created_at,
                    first_message,
                    message_timestamp,
                });
            }
        }
    }

    // Sort sessions by creation time (newest first)
    sessions.sort_by(|a, b| b.created_at.cmp(&a.created_at));

    log::info!(
        "Found {} sessions for project {}",
        sessions.len(),
        project_id
    );
    Ok(sessions)
}

/// Reads the Claude settings file
#[tauri::command]
pub async fn get_claude_settings() -> Result<ClaudeSettings, String> {
    log::info!("Reading Claude settings");

    let claude_dir = get_claude_dir().map_err(|e| e.to_string())?;
    let settings_path = claude_dir.join("settings.json");

    if !settings_path.exists() {
        log::warn!("Settings file not found, returning empty settings");
        return Ok(ClaudeSettings {
            data: serde_json::json!({}),
        });
    }

    let content = fs::read_to_string(&settings_path)
        .map_err(|e| format!("Failed to read settings file: {}", e))?;

    let data: serde_json::Value = serde_json::from_str(&content)
        .map_err(|e| format!("Failed to parse settings JSON: {}", e))?;

    Ok(ClaudeSettings { data })
}

/// Opens a new Claude Code session by executing the claude command
#[tauri::command]
pub async fn open_new_session(app: AppHandle, path: Option<String>) -> Result<String, String> {
    log::info!("Opening new Claude Code session at path: {:?}", path);

    #[cfg(not(debug_assertions))]
    let _claude_path = crate::claude_binary::find_claude_binary(&app)?;

    #[cfg(debug_assertions)]
    let claude_path = crate::claude_binary::find_claude_binary(&app)?;

    // In production, we can't use std::process::Command directly
    // The user should launch Claude Code through other means or use the execute_claude_code command
    #[cfg(not(debug_assertions))]
    {
        log::error!("Cannot spawn processes directly in production builds");
        return Err("Direct process spawning is not available in production builds. Please use Claude Code directly or use the integrated execution commands.".to_string());
    }

    #[cfg(debug_assertions)]
    {
        let mut cmd = std::process::Command::new(claude_path);

        // If a path is provided, use it; otherwise use current directory
        if let Some(project_path) = path {
            cmd.current_dir(&project_path);
        }

        // Execute the command
        match cmd.spawn() {
            Ok(_) => {
                log::info!("Successfully launched Claude Code");
                Ok("Claude Code session started".to_string())
            }
            Err(e) => {
                log::error!("Failed to launch Claude Code: {}", e);
                Err(format!("Failed to launch Claude Code: {}", e))
            }
        }
    }
}

/// Reads the CLAUDE.md system prompt file
#[tauri::command]
pub async fn get_system_prompt() -> Result<String, String> {
    log::info!("Reading CLAUDE.md system prompt");

    let claude_dir = get_claude_dir().map_err(|e| e.to_string())?;
    let claude_md_path = claude_dir.join("CLAUDE.md");

    if !claude_md_path.exists() {
        log::warn!("CLAUDE.md not found");
        return Ok(String::new());
    }

    fs::read_to_string(&claude_md_path).map_err(|e| format!("Failed to read CLAUDE.md: {}", e))
}

/// Checks if Claude Code is installed and gets its version
#[tauri::command]
pub async fn check_claude_version(app: AppHandle) -> Result<ClaudeVersionStatus, String> {
    log::info!("Checking Claude Code version");

    let claude_path = match crate::claude_binary::find_claude_binary(&app) {
        Ok(path) => path,
        Err(e) => {
            return Ok(ClaudeVersionStatus {
                is_installed: false,
                version: None,
                output: e,
            });
        }
    };

    use log::debug;debug!("Claude path: {}", claude_path);

    // In production builds, we can't check the version directly
    #[cfg(not(debug_assertions))]
    {
        log::warn!("Cannot check claude version in production build");
        // If we found a path (either stored or in common locations), assume it's installed
        if claude_path != "claude" && PathBuf::from(&claude_path).exists() {
            return Ok(ClaudeVersionStatus {
                is_installed: true,
                version: None,
                output: "Claude binary found at: ".to_string() + &claude_path,
            });
        } else {
            return Ok(ClaudeVersionStatus {
                is_installed: false,
                version: None,
                output: "Cannot verify Claude installation in production build. Please ensure Claude Code is installed.".to_string(),
            });
        }
    }

    #[cfg(debug_assertions)]
    {
        let output = std::process::Command::new(claude_path)
            .arg("--version")
            .output();

        match output {
            Ok(output) => {
                let stdout = String::from_utf8_lossy(&output.stdout).to_string();
                let stderr = String::from_utf8_lossy(&output.stderr).to_string();
                
                // Use regex to directly extract version pattern (e.g., "1.0.41")
                let version_regex = regex::Regex::new(r"(\d+\.\d+\.\d+(?:-[a-zA-Z0-9.-]+)?(?:\+[a-zA-Z0-9.-]+)?)").ok();
                
                let version = if let Some(regex) = version_regex {
                    regex.captures(&stdout)
                        .and_then(|captures| captures.get(1))
                        .map(|m| m.as_str().to_string())
                } else {
                    None
                };
                
                let full_output = if stderr.is_empty() {
                    stdout.clone()
                } else {
                    format!("{}\n{}", stdout, stderr)
                };

                // Check if the output matches the expected format
                // Expected format: "1.0.17 (Claude Code)" or similar
                let is_valid = stdout.contains("(Claude Code)") || stdout.contains("Claude Code");

                Ok(ClaudeVersionStatus {
                    is_installed: is_valid && output.status.success(),
                    version,
                    output: full_output.trim().to_string(),
                })
            }
            Err(e) => {
                log::error!("Failed to run claude command: {}", e);
                Ok(ClaudeVersionStatus {
                    is_installed: false,
                    version: None,
                    output: format!("Command not found: {}", e),
                })
            }
        }
    }
}

/// Saves the CLAUDE.md system prompt file
#[tauri::command]
pub async fn save_system_prompt(content: String) -> Result<String, String> {
    log::info!("Saving CLAUDE.md system prompt");

    let claude_dir = get_claude_dir().map_err(|e| e.to_string())?;
    let claude_md_path = claude_dir.join("CLAUDE.md");

    fs::write(&claude_md_path, content).map_err(|e| format!("Failed to write CLAUDE.md: {}", e))?;

    Ok("System prompt saved successfully".to_string())
}

/// Saves the Claude settings file
#[tauri::command]
pub async fn save_claude_settings(settings: serde_json::Value) -> Result<String, String> {
    log::info!("Saving Claude settings");

    let claude_dir = get_claude_dir().map_err(|e| e.to_string())?;
    let settings_path = claude_dir.join("settings.json");

    // Pretty print the JSON with 2-space indentation
    let json_string = serde_json::to_string_pretty(&settings)
        .map_err(|e| format!("Failed to serialize settings: {}", e))?;

    fs::write(&settings_path, json_string)
        .map_err(|e| format!("Failed to write settings file: {}", e))?;

    Ok("Settings saved successfully".to_string())
}

/// Recursively finds all CLAUDE.md files in a project directory
#[tauri::command]
pub async fn find_claude_md_files(project_path: String) -> Result<Vec<ClaudeMdFile>, String> {
    log::info!("Finding CLAUDE.md files in project: {}", project_path);

    let path = PathBuf::from(&project_path);
    if !path.exists() {
        return Err(format!("Project path does not exist: {}", project_path));
    }

    let mut claude_files = Vec::new();
    find_claude_md_recursive(&path, &path, &mut claude_files)?;

    // Sort by relative path
    claude_files.sort_by(|a, b| a.relative_path.cmp(&b.relative_path));

    log::info!("Found {} CLAUDE.md files", claude_files.len());
    Ok(claude_files)
}

/// Helper function to recursively find CLAUDE.md files
fn find_claude_md_recursive(
    current_path: &PathBuf,
    project_root: &PathBuf,
    claude_files: &mut Vec<ClaudeMdFile>,
) -> Result<(), String> {
    let entries = fs::read_dir(current_path)
        .map_err(|e| format!("Failed to read directory {:?}: {}", current_path, e))?;

    for entry in entries {
        let entry = entry.map_err(|e| format!("Failed to read directory entry: {}", e))?;
        let path = entry.path();

        // Skip hidden files/directories
        if let Some(name) = path.file_name().and_then(|n| n.to_str()) {
            if name.starts_with('.') {
                continue;
            }
        }

        if path.is_dir() {
            // Skip common directories that shouldn't be searched
            if let Some(dir_name) = path.file_name().and_then(|n| n.to_str()) {
                if matches!(
                    dir_name,
                    "node_modules" | "target" | ".git" | "dist" | "build" | ".next" | "__pycache__"
                ) {
                    continue;
                }
            }

            find_claude_md_recursive(&path, project_root, claude_files)?;
        } else if path.is_file() {
            // Check if it's a CLAUDE.md file (case insensitive)
            if let Some(file_name) = path.file_name().and_then(|n| n.to_str()) {
                if file_name.eq_ignore_ascii_case("CLAUDE.md") {
                    let metadata = fs::metadata(&path)
                        .map_err(|e| format!("Failed to read file metadata: {}", e))?;

                    let relative_path = path
                        .strip_prefix(project_root)
                        .map_err(|e| format!("Failed to get relative path: {}", e))?
                        .to_string_lossy()
                        .to_string();

                    let modified = metadata
                        .modified()
                        .unwrap_or(SystemTime::UNIX_EPOCH)
                        .duration_since(SystemTime::UNIX_EPOCH)
                        .unwrap_or_default()
                        .as_secs();

                    claude_files.push(ClaudeMdFile {
                        relative_path,
                        absolute_path: path.to_string_lossy().to_string(),
                        size: metadata.len(),
                        modified,
                    });
                }
            }
        }
    }

    Ok(())
}

/// Reads a specific CLAUDE.md file by its absolute path
#[tauri::command]
pub async fn read_claude_md_file(file_path: String) -> Result<String, String> {
    log::info!("Reading CLAUDE.md file: {}", file_path);

    let path = PathBuf::from(&file_path);
    if !path.exists() {
        return Err(format!("File does not exist: {}", file_path));
    }

    fs::read_to_string(&path).map_err(|e| format!("Failed to read file: {}", e))
}

/// Saves a specific CLAUDE.md file by its absolute path
#[tauri::command]
pub async fn save_claude_md_file(file_path: String, content: String) -> Result<String, String> {
    log::info!("Saving CLAUDE.md file: {}", file_path);

    let path = PathBuf::from(&file_path);

    // Ensure the parent directory exists
    if let Some(parent) = path.parent() {
        fs::create_dir_all(parent)
            .map_err(|e| format!("Failed to create parent directory: {}", e))?;
    }

    fs::write(&path, content).map_err(|e| format!("Failed to write file: {}", e))?;

    Ok("File saved successfully".to_string())
}

/// Loads the JSONL history for a specific session
#[tauri::command]
pub async fn load_session_history(
    session_id: String,
    project_id: String,
) -> Result<Vec<serde_json::Value>, String> {
    log::info!(
        "Loading session history for session: {} in project: {}",
        session_id,
        project_id
    );

    // Validate inputs
    if session_id.is_empty() {
        return Err("Session ID cannot be empty".to_string());
    }
    if project_id.is_empty() {
        return Err("Project ID cannot be empty".to_string());
    }

    let claude_dir = get_claude_dir().map_err(|e| {
        log::error!("Failed to get Claude directory: {}", e);
        format!("Failed to get Claude directory: {}", e)
    })?;
    let session_path = claude_dir
        .join("projects")
        .join(&project_id)
        .join(format!("{}.jsonl", session_id));

    if !session_path.exists() {
        log::error!("Session file not found at path: {:?}", session_path);
        return Err(format!(
            "Session file not found for session_id: {} in project: {}. Path: {:?}",
            session_id, project_id, session_path
        ));
    }

    let file = fs::File::open(&session_path).map_err(|e| {
        log::error!("Failed to open session file at {:?}: {}", session_path, e);
        format!("Failed to open session file: {}", e)
    })?;

    let reader = BufReader::new(file);
    let mut messages = Vec::new();

    for line in reader.lines() {
        if let Ok(line) = line {
            if let Ok(json) = serde_json::from_str::<serde_json::Value>(&line) {
                messages.push(json);
            }
        }
    }

    Ok(messages)
}



/// Execute a new interactive Claude Code session with streaming output
#[tauri::command]
pub async fn execute_claude_code(
    app: AppHandle,
    project_path: String,
    prompt: String,
    model: String,
) -> Result<(), String> {
    log::info!(
        "Starting new Claude Code session in: {} with model: {}",
        project_path,
        model
    );

    let claude_path = crate::claude_binary::find_claude_binary(&app)?;
    
    // Map frontend model names to actual API model names
    let mapped_model = match model.as_str() {
        "opus-4.1" => "claude-opus-4-1-20250805".to_string(),
        "sonnet" => "claude-sonnet-4-20250514".to_string(),
        _ => model.clone(),
    };
    
    let args = vec![
        "-p".to_string(),
        prompt.clone(),
        "--model".to_string(),
        mapped_model.clone(),
        "--output-format".to_string(),
        "stream-json".to_string(),
        "--verbose".to_string(),
        "--dangerously-skip-permissions".to_string(),
    ];

    let cmd = create_system_command(&claude_path, args, &project_path);
    spawn_claude_process(app, cmd, prompt, mapped_model, project_path).await
}

/// Continue an existing Claude Code conversation with streaming output
#[tauri::command]
pub async fn continue_claude_code(
    app: AppHandle,
    project_path: String,
    prompt: String,
    model: String,
) -> Result<(), String> {
    log::info!(
        "Continuing Claude Code conversation in: {} with model: {}",
        project_path,
        model
    );

    let claude_path = crate::claude_binary::find_claude_binary(&app)?;
    
    // Map frontend model names to actual API model names
    let mapped_model = match model.as_str() {
        "opus-4.1" => "claude-opus-4-1-20250805".to_string(),
        "sonnet" => "claude-sonnet-4-20250514".to_string(),
        _ => model.clone(),
    };
    
    let args = vec![
        "-c".to_string(), // Continue flag
        "-p".to_string(),
        prompt.clone(),
        "--model".to_string(),
        mapped_model.clone(),
        "--output-format".to_string(),
        "stream-json".to_string(),
        "--verbose".to_string(),
        "--dangerously-skip-permissions".to_string(),
    ];

    let cmd = create_system_command(&claude_path, args, &project_path);
    spawn_claude_process(app, cmd, prompt, mapped_model, project_path).await
}

/// Resume an existing Claude Code session by ID with streaming output
#[tauri::command]
pub async fn resume_claude_code(
    app: AppHandle,
    project_path: String,
    session_id: String,
    prompt: String,
    model: String,
) -> Result<(), String> {
    log::info!(
        "Resuming Claude Code session: {} in: {} with model: {}",
        session_id,
        project_path,
        model
    );

    let claude_path = crate::claude_binary::find_claude_binary(&app)?;
    
    // Map frontend model names to actual API model names
    let mapped_model = match model.as_str() {
        "opus-4.1" => "claude-opus-4-1-20250805".to_string(),
        "sonnet" => "claude-sonnet-4-20250514".to_string(),
        _ => model.clone(),
    };
    
    let args = vec![
        "--resume".to_string(),
        session_id.clone(),
        "-p".to_string(),
        prompt.clone(),
        "--model".to_string(),
        mapped_model.clone(),
        "--output-format".to_string(),
        "stream-json".to_string(),
        "--verbose".to_string(),
        "--dangerously-skip-permissions".to_string(),
    ];

    let cmd = create_system_command(&claude_path, args, &project_path);
    spawn_claude_process(app, cmd, prompt, mapped_model, project_path).await
}

/// Cancel the currently running Claude Code execution
#[tauri::command]
pub async fn cancel_claude_execution(
    app: AppHandle,
    session_id: Option<String>,
) -> Result<(), String> {
    log::info!(
        "Cancelling Claude Code execution for session: {:?}",
        session_id
    );

    let mut killed = false;
    let mut attempted_methods = Vec::new();

    // Method 1: Try to find and kill via ProcessRegistry using session ID
    if let Some(sid) = &session_id {
        let registry = app.state::<crate::process::ProcessRegistryState>();
        match registry.0.get_claude_session_by_id(sid) {
            Ok(Some(process_info)) => {
                log::info!("Found process in registry for session {}: run_id={}, PID={}", 
                    sid, process_info.run_id, process_info.pid);
                match registry.0.kill_process(process_info.run_id).await {
                    Ok(success) => {
                        if success {
                            log::info!("Successfully killed process via registry");
                            killed = true;
                        } else {
                            log::warn!("Registry kill returned false");
                        }
                    }
                    Err(e) => {
                        log::warn!("Failed to kill via registry: {}", e);
                    }
                }
                attempted_methods.push("registry");
            }
            Ok(None) => {
                log::warn!("Session {} not found in ProcessRegistry", sid);
            }
            Err(e) => {
                log::error!("Error querying ProcessRegistry: {}", e);
            }
        }
    }

    // Method 2: Try the legacy approach via ClaudeProcessState
    if !killed {
        let claude_state = app.state::<ClaudeProcessState>();
        let mut current_process = claude_state.current_process.lock().await;

        if let Some(mut child) = current_process.take() {
            // Try to get the PID before killing
            let pid = child.id();
            log::info!("Attempting to kill Claude process via ClaudeProcessState with PID: {:?}", pid);

            // Kill the process
            match child.kill().await {
                Ok(_) => {
                    log::info!("Successfully killed Claude process via ClaudeProcessState");
                    killed = true;
                }
                Err(e) => {
                    log::error!("Failed to kill Claude process via ClaudeProcessState: {}", e);
                    
                    // Method 3: If we have a PID, try system kill as last resort
                    if let Some(pid) = pid {
                        log::info!("Attempting system kill as last resort for PID: {}", pid);
                        let kill_result = if cfg!(target_os = "windows") {
                            std::process::Command::new("taskkill")
                                .args(["/F", "/PID", &pid.to_string()])
                                .output()
                        } else {
                            std::process::Command::new("kill")
                                .args(["-KILL", &pid.to_string()])
                                .output()
                        };
                        
                        match kill_result {
                            Ok(output) if output.status.success() => {
                                log::info!("Successfully killed process via system command");
                                killed = true;
                            }
                            Ok(output) => {
                                let stderr = String::from_utf8_lossy(&output.stderr);
                                log::error!("System kill failed: {}", stderr);
                            }
                            Err(e) => {
                                log::error!("Failed to execute system kill command: {}", e);
                            }
                        }
                    }
                }
            }
            attempted_methods.push("claude_state");
        } else {
            log::warn!("No active Claude process in ClaudeProcessState");
        }
    }

    if !killed && attempted_methods.is_empty() {
        log::warn!("No active Claude process found to cancel");
    }

    // Always emit cancellation events for UI consistency
    if let Some(sid) = session_id {
        let _ = app.emit(&format!("claude-cancelled:{}", sid), true);
        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
        let _ = app.emit(&format!("claude-complete:{}", sid), false);
    }
    
    // Also emit generic events for backward compatibility
    let _ = app.emit("claude-cancelled", true);
    tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
    let _ = app.emit("claude-complete", false);
    
    if killed {
        log::info!("Claude process cancellation completed successfully");
    } else if !attempted_methods.is_empty() {
        log::warn!("Claude process cancellation attempted but process may have already exited. Attempted methods: {:?}", attempted_methods);
    }
    
    Ok(())
}

/// Get all running Claude sessions
#[tauri::command]
pub async fn list_running_claude_sessions(
    registry: tauri::State<'_, crate::process::ProcessRegistryState>,
) -> Result<Vec<crate::process::ProcessInfo>, String> {
    registry.0.get_running_claude_sessions()
}

/// Get output from a Claude session (either live or from stored JSONL)
#[tauri::command]
pub async fn get_claude_session_output(
    registry: tauri::State<'_, crate::process::ProcessRegistryState>,
    session_id: String,
) -> Result<String, String> {
    // First try to find a running process by session ID
    if let Some(process_info) = registry.0.get_claude_session_by_id(&session_id)? {
        return registry.0.get_live_output(process_info.run_id);
    }
    
    // If no running process, try to load from stored JSONL file
    log::info!("No running process for session {}, checking for stored JSONL", session_id);
    
    // Find the JSONL file for this session
    let claude_dir = get_claude_dir().map_err(|e| e.to_string())?;
    let projects_dir = claude_dir.join("projects");
    
    // Search all project directories for this session's JSONL file
    if projects_dir.exists() {
        for entry in fs::read_dir(&projects_dir).map_err(|e| e.to_string())? {
            let entry = entry.map_err(|e| e.to_string())?;
            let project_path = entry.path();
            
            if project_path.is_dir() {
                let session_file = project_path.join(format!("{}.jsonl", session_id));
                if session_file.exists() {
                    log::info!("Found JSONL file for session: {:?}", session_file);
                    // Read the entire file
                    return fs::read_to_string(&session_file)
                        .map_err(|e| format!("Failed to read session file: {}", e));
                }
            }
        }
    }
    
    log::warn!("No JSONL file found for session: {}", session_id);
    Ok(String::new())
}

/// Helper function to spawn Claude process and handle streaming
pub async fn spawn_claude_process(app: AppHandle, mut cmd: Command, prompt: String, model: String, project_path: String) -> Result<(), String> {
    use tokio::io::{AsyncBufReadExt, BufReader};
    use std::sync::Mutex;

    // Spawn the process
    let mut child = cmd
        .spawn()
        .map_err(|e| format!("Failed to spawn Claude: {}", e))?;

    // Get stdout and stderr
    let stdout = child.stdout.take().ok_or("Failed to get stdout")?;
    let stderr = child.stderr.take().ok_or("Failed to get stderr")?;

    // Get the child PID for logging
    let pid = child.id().unwrap_or(0);
    log::info!(
        "Spawned Claude process with PID: {:?}",
        pid
    );

    // Create readers first (before moving child)
    let stdout_reader = BufReader::new(stdout);
    let stderr_reader = BufReader::new(stderr);

    // We'll extract the session ID from Claude's init message
    let session_id_holder: Arc<Mutex<Option<String>>> = Arc::new(Mutex::new(None));
    let run_id_holder: Arc<Mutex<Option<i64>>> = Arc::new(Mutex::new(None));

    // Store the child process in the global state (for backward compatibility)
    let claude_state = app.state::<ClaudeProcessState>();
    {
        let mut current_process = claude_state.current_process.lock().await;
        // If there's already a process running, kill it first
        if let Some(mut existing_child) = current_process.take() {
            log::warn!("Killing existing Claude process before starting new one");
            let _ = existing_child.kill().await;
        }
        *current_process = Some(child);
    }

    // Spawn tasks to read stdout and stderr
    let app_handle = app.clone();
    let session_id_holder_clone = session_id_holder.clone();
    let run_id_holder_clone = run_id_holder.clone();
    let registry = app.state::<crate::process::ProcessRegistryState>();
    let registry_clone = registry.0.clone();
    let project_path_clone = project_path.clone();
    let prompt_clone = prompt.clone();
    let model_clone = model.clone();
    let stdout_task = tokio::spawn(async move {
        let mut lines = stdout_reader.lines();
        while let Ok(Some(line)) = lines.next_line().await {
            log::debug!("Claude stdout: {}", line);
            
            // Parse the line to check for init message with session ID
            if let Ok(msg) = serde_json::from_str::<serde_json::Value>(&line) {
                if msg["type"] == "system" && msg["subtype"] == "init" {
                    if let Some(claude_session_id) = msg["session_id"].as_str() {
                        let mut session_id_guard = session_id_holder_clone.lock().unwrap();
                        if session_id_guard.is_none() {
                            *session_id_guard = Some(claude_session_id.to_string());
                            log::info!("Extracted Claude session ID: {}", claude_session_id);
                            
                            // Now register with ProcessRegistry using Claude's session ID
                            match registry_clone.register_claude_session(
                                claude_session_id.to_string(),
                                pid,
                                project_path_clone.clone(),
                                prompt_clone.clone(),
                                model_clone.clone(),
                            ) {
                                Ok(run_id) => {
                                    log::info!("Registered Claude session with run_id: {}", run_id);
                                    let mut run_id_guard = run_id_holder_clone.lock().unwrap();
                                    *run_id_guard = Some(run_id);
                                }
                                Err(e) => {
                                    log::error!("Failed to register Claude session: {}", e);
                                }
                            }
                        }
                    }
                }
            }
            
            // Store live output in registry if we have a run_id
            if let Some(run_id) = *run_id_holder_clone.lock().unwrap() {
                let _ = registry_clone.append_live_output(run_id, &line);
            }
            
            // Emit the line to the frontend with session isolation if we have session ID
            if let Some(ref session_id) = *session_id_holder_clone.lock().unwrap() {
                let _ = app_handle.emit(&format!("claude-output:{}", session_id), &line);
            }
            // Also emit to the generic event for backward compatibility
            let _ = app_handle.emit("claude-output", &line);
        }
    });

    let app_handle_stderr = app.clone();
    let session_id_holder_clone2 = session_id_holder.clone();
    let stderr_task = tokio::spawn(async move {
        let mut lines = stderr_reader.lines();
        while let Ok(Some(line)) = lines.next_line().await {
            log::error!("Claude stderr: {}", line);
            // Emit error lines to the frontend with session isolation if we have session ID
            if let Some(ref session_id) = *session_id_holder_clone2.lock().unwrap() {
                let _ = app_handle_stderr.emit(&format!("claude-error:{}", session_id), &line);
            }
            // Also emit to the generic event for backward compatibility
            let _ = app_handle_stderr.emit("claude-error", &line);
        }
    });

    // Wait for the process to complete
    let app_handle_wait = app.clone();
    let claude_state_wait = claude_state.current_process.clone();
    let session_id_holder_clone3 = session_id_holder.clone();
    let run_id_holder_clone2 = run_id_holder.clone();
    let registry_clone2 = registry.0.clone();
    tokio::spawn(async move {
        let _ = stdout_task.await;
        let _ = stderr_task.await;

        // Get the child from the state to wait on it
        let mut current_process = claude_state_wait.lock().await;
        if let Some(mut child) = current_process.take() {
            match child.wait().await {
                Ok(status) => {
                    log::info!("Claude process exited with status: {}", status);
                    // Add a small delay to ensure all messages are processed
                    tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
                    if let Some(ref session_id) = *session_id_holder_clone3.lock().unwrap() {
                        let _ = app_handle_wait.emit(
                            &format!("claude-complete:{}", session_id),
                            status.success(),
                        );
                    }
                    // Also emit to the generic event for backward compatibility
                    let _ = app_handle_wait.emit("claude-complete", status.success());
                }
                Err(e) => {
                    log::error!("Failed to wait for Claude process: {}", e);
                    // Add a small delay to ensure all messages are processed
                    tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
                    if let Some(ref session_id) = *session_id_holder_clone3.lock().unwrap() {
                        let _ = app_handle_wait
                            .emit(&format!("claude-complete:{}", session_id), false);
                    }
                    // Also emit to the generic event for backward compatibility
                    let _ = app_handle_wait.emit("claude-complete", false);
                }
            }
        }

        // Unregister from ProcessRegistry if we have a run_id
        if let Some(run_id) = *run_id_holder_clone2.lock().unwrap() {
            let _ = registry_clone2.unregister_process(run_id);
        }

        // Clear the process from state
        *current_process = None;
    });

    Ok(())
}


/// Lists files and directories in a given path
#[tauri::command]
pub async fn list_directory_contents(directory_path: String) -> Result<Vec<FileEntry>, String> {
    log::info!("Listing directory contents: '{}'", directory_path);

    // Check if path is empty
    if directory_path.trim().is_empty() {
        log::error!("Directory path is empty or whitespace");
        return Err("Directory path cannot be empty".to_string());
    }

    let path = PathBuf::from(&directory_path);
    log::debug!("Resolved path: {:?}", path);

    if !path.exists() {
        log::error!("Path does not exist: {:?}", path);
        return Err(format!("Path does not exist: {}", directory_path));
    }

    if !path.is_dir() {
        log::error!("Path is not a directory: {:?}", path);
        return Err(format!("Path is not a directory: {}", directory_path));
    }

    let mut entries = Vec::new();

    let dir_entries =
        fs::read_dir(&path).map_err(|e| format!("Failed to read directory: {}", e))?;

    for entry in dir_entries {
        let entry = entry.map_err(|e| format!("Failed to read entry: {}", e))?;
        let entry_path = entry.path();
        let metadata = entry
            .metadata()
            .map_err(|e| format!("Failed to read metadata: {}", e))?;

        // Skip hidden files/directories unless they are .claude directories
        if let Some(name) = entry_path.file_name().and_then(|n| n.to_str()) {
            if name.starts_with('.') && name != ".claude" {
                continue;
            }
        }

        let name = entry_path
            .file_name()
            .and_then(|n| n.to_str())
            .unwrap_or("")
            .to_string();

        let extension = if metadata.is_file() {
            entry_path
                .extension()
                .and_then(|e| e.to_str())
                .map(|e| e.to_string())
        } else {
            None
        };

        entries.push(FileEntry {
            name,
            path: entry_path.to_string_lossy().to_string(),
            is_directory: metadata.is_dir(),
            size: metadata.len(),
            extension,
        });
    }

    // Sort: directories first, then files, alphabetically within each group
    entries.sort_by(|a, b| match (a.is_directory, b.is_directory) {
        (true, false) => std::cmp::Ordering::Less,
        (false, true) => std::cmp::Ordering::Greater,
        _ => a.name.to_lowercase().cmp(&b.name.to_lowercase()),
    });

    Ok(entries)
}

/// Search for files and directories matching a pattern
#[tauri::command]
pub async fn search_files(base_path: String, query: String) -> Result<Vec<FileEntry>, String> {
    log::info!("Searching files in '{}' for: '{}'", base_path, query);

    // Check if path is empty
    if base_path.trim().is_empty() {
        log::error!("Base path is empty or whitespace");
        return Err("Base path cannot be empty".to_string());
    }

    // Check if query is empty
    if query.trim().is_empty() {
        log::warn!("Search query is empty, returning empty results");
        return Ok(Vec::new());
    }

    let path = PathBuf::from(&base_path);
    log::debug!("Resolved search base path: {:?}", path);

    if !path.exists() {
        log::error!("Base path does not exist: {:?}", path);
        return Err(format!("Path does not exist: {}", base_path));
    }

    let query_lower = query.to_lowercase();
    let mut results = Vec::new();

    search_files_recursive(&path, &path, &query_lower, &mut results, 0)?;

    // Sort by relevance: exact matches first, then by name
    results.sort_by(|a, b| {
        let a_exact = a.name.to_lowercase() == query_lower;
        let b_exact = b.name.to_lowercase() == query_lower;

        match (a_exact, b_exact) {
            (true, false) => std::cmp::Ordering::Less,
            (false, true) => std::cmp::Ordering::Greater,
            _ => a.name.to_lowercase().cmp(&b.name.to_lowercase()),
        }
    });

    // Limit results to prevent overwhelming the UI
    results.truncate(50);

    Ok(results)
}

fn search_files_recursive(
    current_path: &PathBuf,
    base_path: &PathBuf,
    query: &str,
    results: &mut Vec<FileEntry>,
    depth: usize,
) -> Result<(), String> {
    // Limit recursion depth to prevent excessive searching
    if depth > 5 || results.len() >= 50 {
        return Ok(());
    }

    let entries = fs::read_dir(current_path)
        .map_err(|e| format!("Failed to read directory {:?}: {}", current_path, e))?;

    for entry in entries {
        let entry = entry.map_err(|e| format!("Failed to read entry: {}", e))?;
        let entry_path = entry.path();

        // Skip hidden files/directories
        if let Some(name) = entry_path.file_name().and_then(|n| n.to_str()) {
            if name.starts_with('.') {
                continue;
            }

            // Check if name matches query
            if name.to_lowercase().contains(query) {
                let metadata = entry
                    .metadata()
                    .map_err(|e| format!("Failed to read metadata: {}", e))?;

                let extension = if metadata.is_file() {
                    entry_path
                        .extension()
                        .and_then(|e| e.to_str())
                        .map(|e| e.to_string())
                } else {
                    None
                };

                results.push(FileEntry {
                    name: name.to_string(),
                    path: entry_path.to_string_lossy().to_string(),
                    is_directory: metadata.is_dir(),
                    size: metadata.len(),
                    extension,
                });
            }
        }

        // Recurse into directories
        if entry_path.is_dir() {
            // Skip common directories that shouldn't be searched
            if let Some(dir_name) = entry_path.file_name().and_then(|n| n.to_str()) {
                if matches!(
                    dir_name,
                    "node_modules" | "target" | ".git" | "dist" | "build" | ".next" | "__pycache__"
                ) {
                    continue;
                }
            }

            search_files_recursive(&entry_path, base_path, query, results, depth + 1)?;
        }
    }

    Ok(())
}

/// Creates a checkpoint for the current session state
#[tauri::command]
pub async fn create_checkpoint(
    checkpoint_state: tauri::State<'_, crate::checkpoint::state::CheckpointState>,
    session_id: String,
    project_id: String,
    project_path: String,
    message_index: Option<usize>,
    description: Option<String>,
) -> Result<crate::checkpoint::CheckpointResult, String> {
    log::info!(
        "Creating checkpoint for session: {} in project: {}",
        session_id,
        project_id
    );

    let manager = checkpoint_state
        .get_or_create_manager(
            session_id.clone(),
            project_id.clone(),
            PathBuf::from(&project_path),
        )
        .await
        .map_err(|e| format!("Failed to get checkpoint manager: {}", e))?;

    // Always load current session messages from the JSONL file
    let session_path = get_claude_dir()
        .map_err(|e| e.to_string())?
        .join("projects")
        .join(&project_id)
        .join(format!("{}.jsonl", session_id));

    if session_path.exists() {
        let file = fs::File::open(&session_path)
            .map_err(|e| format!("Failed to open session file: {}", e))?;
        let reader = BufReader::new(file);

        let mut line_count = 0;
        for line in reader.lines() {
            if let Some(index) = message_index {
                if line_count > index {
                    break;
                }
            }
            if let Ok(line) = line {
                manager
                    .track_message(line)
                    .await
                    .map_err(|e| format!("Failed to track message: {}", e))?;
            }
            line_count += 1;
        }
    }

    manager
        .create_checkpoint(description, None)
        .await
        .map_err(|e| format!("Failed to create checkpoint: {}", e))
}

/// Restores a session to a specific checkpoint
#[tauri::command]
pub async fn restore_checkpoint(
    checkpoint_state: tauri::State<'_, crate::checkpoint::state::CheckpointState>,
    checkpoint_id: String,
    session_id: String,
    project_id: String,
    project_path: String,
) -> Result<crate::checkpoint::CheckpointResult, String> {
    log::info!(
        "Restoring checkpoint: {} for session: {}",
        checkpoint_id,
        session_id
    );

    let manager = checkpoint_state
        .get_or_create_manager(
            session_id.clone(),
            project_id.clone(),
            PathBuf::from(&project_path),
        )
        .await
        .map_err(|e| format!("Failed to get checkpoint manager: {}", e))?;

    let result = manager
        .restore_checkpoint(&checkpoint_id)
        .await
        .map_err(|e| format!("Failed to restore checkpoint: {}", e))?;

    // Update the session JSONL file with restored messages
    let claude_dir = get_claude_dir().map_err(|e| e.to_string())?;
    let session_path = claude_dir
        .join("projects")
        .join(&result.checkpoint.project_id)
        .join(format!("{}.jsonl", session_id));

    // The manager has already restored the messages internally,
    // but we need to update the actual session file
    let (_, _, messages) = manager
        .storage
        .load_checkpoint(&result.checkpoint.project_id, &session_id, &checkpoint_id)
        .map_err(|e| format!("Failed to load checkpoint data: {}", e))?;

    fs::write(&session_path, messages)
        .map_err(|e| format!("Failed to update session file: {}", e))?;

    Ok(result)
}

/// Lists all checkpoints for a session
#[tauri::command]
pub async fn list_checkpoints(
    checkpoint_state: tauri::State<'_, crate::checkpoint::state::CheckpointState>,
    session_id: String,
    project_id: String,
    project_path: String,
) -> Result<Vec<crate::checkpoint::Checkpoint>, String> {
    log::info!(
        "Listing checkpoints for session: {} in project: {}",
        session_id,
        project_id
    );

    let manager = checkpoint_state
        .get_or_create_manager(session_id, project_id, PathBuf::from(&project_path))
        .await
        .map_err(|e| format!("Failed to get checkpoint manager: {}", e))?;

    Ok(manager.list_checkpoints().await)
}

/// Forks a new timeline branch from a checkpoint
#[tauri::command]
pub async fn fork_from_checkpoint(
    checkpoint_state: tauri::State<'_, crate::checkpoint::state::CheckpointState>,
    checkpoint_id: String,
    session_id: String,
    project_id: String,
    project_path: String,
    new_session_id: String,
    description: Option<String>,
) -> Result<crate::checkpoint::CheckpointResult, String> {
    log::info!(
        "Forking from checkpoint: {} to new session: {}",
        checkpoint_id,
        new_session_id
    );

    let claude_dir = get_claude_dir().map_err(|e| e.to_string())?;

    // First, copy the session file to the new session
    let source_session_path = claude_dir
        .join("projects")
        .join(&project_id)
        .join(format!("{}.jsonl", session_id));
    let new_session_path = claude_dir
        .join("projects")
        .join(&project_id)
        .join(format!("{}.jsonl", new_session_id));

    if source_session_path.exists() {
        fs::copy(&source_session_path, &new_session_path)
            .map_err(|e| format!("Failed to copy session file: {}", e))?;
    }

    // Create manager for the new session
    let manager = checkpoint_state
        .get_or_create_manager(
            new_session_id.clone(),
            project_id,
            PathBuf::from(&project_path),
        )
        .await
        .map_err(|e| format!("Failed to get checkpoint manager: {}", e))?;

    manager
        .fork_from_checkpoint(&checkpoint_id, description)
        .await
        .map_err(|e| format!("Failed to fork checkpoint: {}", e))
}

/// Gets the timeline for a session
#[tauri::command]
pub async fn get_session_timeline(
    checkpoint_state: tauri::State<'_, crate::checkpoint::state::CheckpointState>,
    session_id: String,
    project_id: String,
    project_path: String,
) -> Result<crate::checkpoint::SessionTimeline, String> {
    log::info!(
        "Getting timeline for session: {} in project: {}",
        session_id,
        project_id
    );

    let manager = checkpoint_state
        .get_or_create_manager(session_id, project_id, PathBuf::from(&project_path))
        .await
        .map_err(|e| format!("Failed to get checkpoint manager: {}", e))?;

    Ok(manager.get_timeline().await)
}

/// Updates checkpoint settings for a session
#[tauri::command]
pub async fn update_checkpoint_settings(
    checkpoint_state: tauri::State<'_, crate::checkpoint::state::CheckpointState>,
    session_id: String,
    project_id: String,
    project_path: String,
    auto_checkpoint_enabled: bool,
    checkpoint_strategy: String,
) -> Result<(), String> {
    use crate::checkpoint::CheckpointStrategy;

    log::info!("Updating checkpoint settings for session: {}", session_id);

    let strategy = match checkpoint_strategy.as_str() {
        "manual" => CheckpointStrategy::Manual,
        "per_prompt" => CheckpointStrategy::PerPrompt,
        "per_tool_use" => CheckpointStrategy::PerToolUse,
        "smart" => CheckpointStrategy::Smart,
        _ => {
            return Err(format!(
                "Invalid checkpoint strategy: {}",
                checkpoint_strategy
            ))
        }
    };

    let manager = checkpoint_state
        .get_or_create_manager(session_id, project_id, PathBuf::from(&project_path))
        .await
        .map_err(|e| format!("Failed to get checkpoint manager: {}", e))?;

    manager
        .update_settings(auto_checkpoint_enabled, strategy)
        .await
        .map_err(|e| format!("Failed to update settings: {}", e))
}

/// Gets diff between two checkpoints
#[tauri::command]
pub async fn get_checkpoint_diff(
    from_checkpoint_id: String,
    to_checkpoint_id: String,
    session_id: String,
    project_id: String,
) -> Result<crate::checkpoint::CheckpointDiff, String> {
    use crate::checkpoint::storage::CheckpointStorage;

    log::info!(
        "Getting diff between checkpoints: {} -> {}",
        from_checkpoint_id,
        to_checkpoint_id
    );

    let claude_dir = get_claude_dir().map_err(|e| e.to_string())?;
    let storage = CheckpointStorage::new(claude_dir);

    // Load both checkpoints
    let (from_checkpoint, from_files, _) = storage
        .load_checkpoint(&project_id, &session_id, &from_checkpoint_id)
        .map_err(|e| format!("Failed to load source checkpoint: {}", e))?;
    let (to_checkpoint, to_files, _) = storage
        .load_checkpoint(&project_id, &session_id, &to_checkpoint_id)
        .map_err(|e| format!("Failed to load target checkpoint: {}", e))?;

    // Build file maps
    let mut from_map: std::collections::HashMap<PathBuf, &crate::checkpoint::FileSnapshot> =
        std::collections::HashMap::new();
    for file in &from_files {
        from_map.insert(file.file_path.clone(), file);
    }

    let mut to_map: std::collections::HashMap<PathBuf, &crate::checkpoint::FileSnapshot> =
        std::collections::HashMap::new();
    for file in &to_files {
        to_map.insert(file.file_path.clone(), file);
    }

    // Calculate differences
    let mut modified_files = Vec::new();
    let mut added_files = Vec::new();
    let mut deleted_files = Vec::new();

    // Check for modified and deleted files
    for (path, from_file) in &from_map {
        if let Some(to_file) = to_map.get(path) {
            if from_file.hash != to_file.hash {
                // File was modified
                let additions = to_file.content.lines().count();
                let deletions = from_file.content.lines().count();

                // Generate a simple diff representation
                let diff_content = Some(format!(
                    "--- {}\n+++ {}\n@@ -{} +{} @@\nFile modified",
                    path.display(), path.display(), deletions, additions
                ));
                
                modified_files.push(crate::checkpoint::FileDiff {
                    path: path.clone(),
                    additions,
                    deletions,
                    diff_content,
                });
            }
        } else {
            // File was deleted
            deleted_files.push(path.clone());
        }
    }

    // Check for added files
    for (path, _) in &to_map {
        if !from_map.contains_key(path) {
            added_files.push(path.clone());
        }
    }

    // Calculate token delta
    let token_delta = (to_checkpoint.metadata.total_tokens as i64)
        - (from_checkpoint.metadata.total_tokens as i64);

    Ok(crate::checkpoint::CheckpointDiff {
        from_checkpoint_id,
        to_checkpoint_id,
        modified_files,
        added_files,
        deleted_files,
        token_delta,
    })
}

/// Tracks a message for checkpointing
#[tauri::command]
pub async fn track_checkpoint_message(
    checkpoint_state: tauri::State<'_, crate::checkpoint::state::CheckpointState>,
    session_id: String,
    project_id: String,
    project_path: String,
    message: String,
) -> Result<(), String> {
    log::info!("Tracking message for session: {}", session_id);

    let manager = checkpoint_state
        .get_or_create_manager(session_id, project_id, PathBuf::from(project_path))
        .await
        .map_err(|e| format!("Failed to get checkpoint manager: {}", e))?;

    manager
        .track_message(message)
        .await
        .map_err(|e| format!("Failed to track message: {}", e))
}

/// Checks if auto-checkpoint should be triggered
#[tauri::command]
pub async fn check_auto_checkpoint(
    checkpoint_state: tauri::State<'_, crate::checkpoint::state::CheckpointState>,
    session_id: String,
    project_id: String,
    project_path: String,
    message: String,
) -> Result<bool, String> {
    log::info!("Checking auto-checkpoint for session: {}", session_id);

    let manager = checkpoint_state
        .get_or_create_manager(session_id.clone(), project_id, PathBuf::from(project_path))
        .await
        .map_err(|e| format!("Failed to get checkpoint manager: {}", e))?;

    Ok(manager.should_auto_checkpoint(&message).await)
}

/// Get checkpoint manager info
#[tauri::command]
pub async fn get_checkpoint_manager_info(
    checkpoint_state: tauri::State<'_, crate::checkpoint::state::CheckpointState>,
    session_id: String,
    project_id: String,
    project_path: String,
) -> Result<serde_json::Value, String> {
    let manager = checkpoint_state
        .get_or_create_manager(session_id.clone(), project_id.clone(), PathBuf::from(project_path))
        .await
        .map_err(|e| format!("Failed to get checkpoint manager: {}", e))?;
    
    let messages = manager.get_current_messages().await;
    let timeline = manager.get_timeline().await;
    
    Ok(serde_json::json!({
        "session_id": manager.get_session_id(),
        "project_id": manager.get_project_id(),
        "project_path": manager.get_project_path().display().to_string(),
        "message_count": messages.len(),
        "timeline": timeline,
    }))
}

/// Triggers cleanup of old checkpoints
#[tauri::command]
pub async fn cleanup_old_checkpoints(
    checkpoint_state: tauri::State<'_, crate::checkpoint::state::CheckpointState>,
    session_id: String,
    project_id: String,
    project_path: String,
    keep_count: usize,
) -> Result<usize, String> {
    log::info!(
        "Cleaning up old checkpoints for session: {}, keeping {}",
        session_id,
        keep_count
    );

    let manager = checkpoint_state
        .get_or_create_manager(
            session_id.clone(),
            project_id.clone(),
            PathBuf::from(project_path),
        )
        .await
        .map_err(|e| format!("Failed to get checkpoint manager: {}", e))?;

    manager
        .storage
        .cleanup_old_checkpoints(&project_id, &session_id, keep_count)
        .map_err(|e| format!("Failed to cleanup checkpoints: {}", e))
}

/// Gets checkpoint settings for a session
#[tauri::command]
pub async fn get_checkpoint_settings(
    checkpoint_state: tauri::State<'_, crate::checkpoint::state::CheckpointState>,
    session_id: String,
    project_id: String,
    project_path: String,
) -> Result<serde_json::Value, String> {
    log::info!("Getting checkpoint settings for session: {}", session_id);

    let manager = checkpoint_state
        .get_or_create_manager(session_id, project_id, PathBuf::from(project_path))
        .await
        .map_err(|e| format!("Failed to get checkpoint manager: {}", e))?;

    let timeline = manager.get_timeline().await;

    Ok(serde_json::json!({
        "auto_checkpoint_enabled": timeline.auto_checkpoint_enabled,
        "checkpoint_strategy": timeline.checkpoint_strategy,
        "total_checkpoints": timeline.total_checkpoints,
        "current_checkpoint_id": timeline.current_checkpoint_id,
    }))
}

/// Clears checkpoint manager for a session (cleanup on session end)
#[tauri::command]
pub async fn clear_checkpoint_manager(
    checkpoint_state: tauri::State<'_, crate::checkpoint::state::CheckpointState>,
    session_id: String,
) -> Result<(), String> {
    log::info!("Clearing checkpoint manager for session: {}", session_id);

    checkpoint_state.remove_manager(&session_id).await;
    Ok(())
}

/// Gets checkpoint state statistics (for debugging/monitoring)
#[tauri::command]
pub async fn get_checkpoint_state_stats(
    checkpoint_state: tauri::State<'_, crate::checkpoint::state::CheckpointState>,
) -> Result<serde_json::Value, String> {
    let active_count = checkpoint_state.active_count().await;
    let active_sessions = checkpoint_state.list_active_sessions().await;

    Ok(serde_json::json!({
        "active_managers": active_count,
        "active_sessions": active_sessions,
    }))
}

/// Gets files modified in the last N minutes for a session
#[tauri::command]
pub async fn get_recently_modified_files(
    checkpoint_state: tauri::State<'_, crate::checkpoint::state::CheckpointState>,
    session_id: String,
    project_id: String,
    project_path: String,
    minutes: i64,
) -> Result<Vec<String>, String> {
    use chrono::{Duration, Utc};

    log::info!(
        "Getting files modified in the last {} minutes for session: {}",
        minutes,
        session_id
    );

    let manager = checkpoint_state
        .get_or_create_manager(session_id, project_id, PathBuf::from(project_path))
        .await
        .map_err(|e| format!("Failed to get checkpoint manager: {}", e))?;

    let since = Utc::now() - Duration::minutes(minutes);
    let modified_files = manager.get_files_modified_since(since).await;

    // Also log the last modification time
    if let Some(last_mod) = manager.get_last_modification_time().await {
        log::info!("Last file modification was at: {}", last_mod);
    }

    Ok(modified_files
        .into_iter()
        .map(|p| p.to_string_lossy().to_string())
        .collect())
}

/// Track session messages from the frontend for checkpointing
#[tauri::command]
pub async fn track_session_messages(
    state: tauri::State<'_, crate::checkpoint::state::CheckpointState>,
    session_id: String,
    project_id: String,
    project_path: String,
    messages: Vec<String>,
) -> Result<(), String> {
    log::info!(
        "Tracking {} messages for session {}",
        messages.len(),
        session_id
    );

    let manager = state
        .get_or_create_manager(
            session_id.clone(),
            project_id.clone(),
            PathBuf::from(&project_path),
        )
        .await
        .map_err(|e| format!("Failed to get checkpoint manager: {}", e))?;

    for message in messages {
        manager
            .track_message(message)
            .await
            .map_err(|e| format!("Failed to track message: {}", e))?;
    }

    Ok(())
}

/// Gets hooks configuration from settings at specified scope
#[tauri::command]
pub async fn get_hooks_config(scope: String, project_path: Option<String>) -> Result<serde_json::Value, String> {
    log::info!("Getting hooks config for scope: {}, project: {:?}", scope, project_path);

    let settings_path = match scope.as_str() {
        "user" => {
            get_claude_dir()
                .map_err(|e| e.to_string())?
                .join("settings.json")
        },
        "project" => {
            let path = project_path.ok_or("Project path required for project scope")?;
            PathBuf::from(path).join(".claude").join("settings.json")
        },
        "local" => {
            let path = project_path.ok_or("Project path required for local scope")?;
            PathBuf::from(path).join(".claude").join("settings.local.json")
        },
        _ => return Err("Invalid scope".to_string())
    };

    if !settings_path.exists() {
        log::info!("Settings file does not exist at {:?}, returning empty hooks", settings_path);
        return Ok(serde_json::json!({}));
    }

    let content = fs::read_to_string(&settings_path)
        .map_err(|e| format!("Failed to read settings: {}", e))?;
    
    let settings: serde_json::Value = serde_json::from_str(&content)
        .map_err(|e| format!("Failed to parse settings: {}", e))?;
    
    Ok(settings.get("hooks").cloned().unwrap_or(serde_json::json!({})))
}

/// Updates hooks configuration in settings at specified scope
#[tauri::command]
pub async fn update_hooks_config(
    scope: String, 
    hooks: serde_json::Value,
    project_path: Option<String>
) -> Result<String, String> {
    log::info!("Updating hooks config for scope: {}, project: {:?}", scope, project_path);

    let settings_path = match scope.as_str() {
        "user" => {
            get_claude_dir()
                .map_err(|e| e.to_string())?
                .join("settings.json")
        },
        "project" => {
            let path = project_path.ok_or("Project path required for project scope")?;
            let claude_dir = PathBuf::from(path).join(".claude");
            fs::create_dir_all(&claude_dir)
                .map_err(|e| format!("Failed to create .claude directory: {}", e))?;
            claude_dir.join("settings.json")
        },
        "local" => {
            let path = project_path.ok_or("Project path required for local scope")?;
            let claude_dir = PathBuf::from(path).join(".claude");
            fs::create_dir_all(&claude_dir)
                .map_err(|e| format!("Failed to create .claude directory: {}", e))?;
            claude_dir.join("settings.local.json")
        },
        _ => return Err("Invalid scope".to_string())
    };

    // Read existing settings or create new
    let mut settings = if settings_path.exists() {
        let content = fs::read_to_string(&settings_path)
            .map_err(|e| format!("Failed to read settings: {}", e))?;
        serde_json::from_str(&content)
            .map_err(|e| format!("Failed to parse settings: {}", e))?
    } else {
        serde_json::json!({})
    };

    // Update hooks section
    settings["hooks"] = hooks;

    // Write back with pretty formatting
    let json_string = serde_json::to_string_pretty(&settings)
        .map_err(|e| format!("Failed to serialize settings: {}", e))?;
    
    fs::write(&settings_path, json_string)
        .map_err(|e| format!("Failed to write settings: {}", e))?;

    Ok("Hooks configuration updated successfully".to_string())
}

/// Validates a hook command by dry-running it
#[tauri::command]
pub async fn validate_hook_command(command: String) -> Result<serde_json::Value, String> {
    log::info!("Validating hook command syntax");

    // Validate syntax without executing
    let mut cmd = std::process::Command::new("bash");
    cmd.arg("-n") // Syntax check only
       .arg("-c")
       .arg(&command);
    
    match cmd.output() {
        Ok(output) => {
            if output.status.success() {
                Ok(serde_json::json!({
                    "valid": true,
                    "message": "Command syntax is valid"
                }))
            } else {
                let stderr = String::from_utf8_lossy(&output.stderr);
                Ok(serde_json::json!({
                    "valid": false,
                    "message": format!("Syntax error: {}", stderr)
                }))
            }
        }
        Err(e) => Err(format!("Failed to validate command: {}", e))
    }
}

/// Discovers and indexes all CLAUDE.md context files in a project directory
#[tauri::command]
pub async fn discover_claude_contexts(project_path: String) -> Result<Vec<ContextFile>, String> {
    log::info!("Discovering CLAUDE.md context files in: {}", project_path);

    let path = PathBuf::from(&project_path);
    if !path.exists() {
        return Err(format!("Project path does not exist: {}", project_path));
    }

    let mut context_files = Vec::new();
    discover_contexts_recursive(&path, &path, &mut context_files)?;

    // Index discovered files in database
    let conn = crate::db::conn().map_err(|e| e.to_string())?;
    let now = chrono::Utc::now().to_rfc3339();

    for context_file in &mut context_files {
        // Calculate content hash
        let content = fs::read_to_string(&context_file.file_path)
            .map_err(|e| format!("Failed to read context file: {}", e))?;
        
        let content_hash = format!("{:x}", md5::compute(&content));
        context_file.content_hash = content_hash.clone();

        // Parse context content
        context_file.parsed_content = parse_context_content(&content).ok();

        // Check if context already exists in database
        let existing: Option<i32> = conn.query_row(
            "SELECT id FROM claude_contexts WHERE file_path = ?1",
            rusqlite::params![&context_file.file_path],
            |row| row.get(0)
        ).optional().map_err(|e| e.to_string())?;

        if let Some(id) = existing {
            // Update existing context
            conn.execute(
                "UPDATE claude_contexts SET content_hash = ?1, parsed_content = ?2, updated_at = ?3, access_count = access_count + 1 WHERE id = ?4",
                rusqlite::params![
                    &content_hash,
                    serde_json::to_string(&context_file.parsed_content).unwrap_or_default(),
                    &now,
                    id
                ]
            ).map_err(|e| e.to_string())?;
            context_file.id = Some(id);
        } else {
            // Insert new context
            conn.execute(
                "INSERT INTO claude_contexts (file_path, project_root, relative_path, content_hash, parsed_content, template_type, is_active, created_at, updated_at, access_count) VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10)",
                rusqlite::params![
                    &context_file.file_path,
                    &context_file.project_root,
                    &context_file.relative_path,
                    &content_hash,
                    serde_json::to_string(&context_file.parsed_content).unwrap_or_default(),
                    &context_file.template_type,
                    context_file.is_active,
                    &now,
                    &now,
                    1
                ]
            ).map_err(|e| e.to_string())?;
            
            let id = conn.last_insert_rowid() as i32;
            context_file.id = Some(id);
        }
    }

    log::info!("Discovered and indexed {} context files", context_files.len());
    Ok(context_files)
}

/// Helper function to recursively discover context files
fn discover_contexts_recursive(
    current_path: &PathBuf,
    project_root: &PathBuf,
    context_files: &mut Vec<ContextFile>,
) -> Result<(), String> {
    let entries = fs::read_dir(current_path)
        .map_err(|e| format!("Failed to read directory {:?}: {}", current_path, e))?;

    for entry in entries {
        let entry = entry.map_err(|e| format!("Failed to read directory entry: {}", e))?;
        let path = entry.path();

        // Skip hidden files/directories
        if let Some(name) = path.file_name().and_then(|n| n.to_str()) {
            if name.starts_with('.') {
                continue;
            }
        }

        if path.is_dir() {
            // Skip common directories that shouldn't be searched
            if let Some(dir_name) = path.file_name().and_then(|n| n.to_str()) {
                if matches!(
                    dir_name,
                    "node_modules" | "target" | ".git" | "dist" | "build" | ".next" | "__pycache__" | "vendor"
                ) {
                    continue;
                }
            }

            discover_contexts_recursive(&path, project_root, context_files)?;
        } else if path.is_file() {
            // Check if it's a CLAUDE.md file (case insensitive)
            if let Some(file_name) = path.file_name().and_then(|n| n.to_str()) {
                if file_name.eq_ignore_ascii_case("CLAUDE.md") {
                    let _metadata = fs::metadata(&path)
                        .map_err(|e| format!("Failed to read file metadata: {}", e))?;

                    let relative_path = path
                        .strip_prefix(project_root)
                        .map_err(|e| format!("Failed to get relative path: {}", e))?
                        .to_string_lossy()
                        .to_string();

                    let now = chrono::Utc::now().to_rfc3339();

                    context_files.push(ContextFile {
                        id: None,
                        file_path: path.to_string_lossy().to_string(),
                        project_root: project_root.to_string_lossy().to_string(),
                        relative_path,
                        content_hash: String::new(), // Will be calculated later
                        parsed_content: None, // Will be parsed later
                        template_type: detect_template_type(&path),
                        parent_context_id: None,
                        is_active: true,
                        created_at: now.clone(),
                        updated_at: now,
                        last_accessed_at: None,
                        access_count: 0,
                    });
                }
            }
        }
    }

    Ok(())
}

/// Detects the template type based on file location and content
fn detect_template_type(file_path: &PathBuf) -> Option<String> {
    let path_str = file_path.to_string_lossy().to_lowercase();
    
    if path_str.contains("react") || path_str.contains("frontend") {
        Some("react".to_string())
    } else if path_str.contains("rust") || path_str.contains("tauri") {
        Some("rust".to_string())
    } else if path_str.contains("python") || path_str.contains(".py") {
        Some("python".to_string())
    } else if path_str.contains("node") || path_str.contains("backend") {
        Some("nodejs".to_string())
    } else {
        Some("generic".to_string())
    }
}

/// Parses CLAUDE.md content into structured format
fn parse_context_content(content: &str) -> Result<ParsedContext, String> {
    let mut sections = Vec::new();
    let mut metadata = ContextMetadata {
        title: None,
        description: None,
        version: None,
        author: None,
        tags: Vec::new(),
        language: None,
        framework: None,
    };
    let mut variables = std::collections::HashMap::new();
    let mut inheritance = Vec::new();

    let lines: Vec<&str> = content.lines().collect();
    let mut current_section: Option<ContextSection> = None;
    let mut in_frontmatter = false;
    let mut frontmatter_lines = Vec::new();

    for (i, line) in lines.iter().enumerate() {
        // Handle YAML frontmatter
        if i == 0 && line.trim() == "---" {
            in_frontmatter = true;
            continue;
        }
        if in_frontmatter && line.trim() == "---" {
            in_frontmatter = false;
            // Parse frontmatter
            let frontmatter = frontmatter_lines.join("\n");
            if let Ok(yaml) = serde_yaml::from_str::<serde_yaml::Value>(&frontmatter) {
                parse_frontmatter(&yaml, &mut metadata, &mut variables, &mut inheritance);
            }
            continue;
        }
        if in_frontmatter {
            frontmatter_lines.push(*line);
            continue;
        }

        // Handle markdown sections
        if line.starts_with('#') {
            // Save previous section
            if let Some(section) = current_section.take() {
                sections.push(section);
            }

            // Start new section
            let title = line.trim_start_matches('#').trim().to_string();
            let section_type = determine_section_type(&title);
            current_section = Some(ContextSection {
                title,
                content: String::new(),
                section_type,
                priority: sections.len() as i32,
            });
        } else if let Some(ref mut section) = current_section {
            if !section.content.is_empty() {
                section.content.push('\n');
            }
            section.content.push_str(line);
        }
    }

    // Save last section
    if let Some(section) = current_section {
        sections.push(section);
    }

    Ok(ParsedContext {
        sections,
        metadata,
        variables,
        inheritance,
    })
}

/// Parses YAML frontmatter into metadata
fn parse_frontmatter(
    yaml: &serde_yaml::Value,
    metadata: &mut ContextMetadata,
    variables: &mut std::collections::HashMap<String, String>,
    inheritance: &mut Vec<String>,
) {
    if let Some(mapping) = yaml.as_mapping() {
        for (key, value) in mapping {
            if let Some(key_str) = key.as_str() {
                match key_str {
                    "title" => metadata.title = value.as_str().map(|s| s.to_string()),
                    "description" => metadata.description = value.as_str().map(|s| s.to_string()),
                    "version" => metadata.version = value.as_str().map(|s| s.to_string()),
                    "author" => metadata.author = value.as_str().map(|s| s.to_string()),
                    "language" => metadata.language = value.as_str().map(|s| s.to_string()),
                    "framework" => metadata.framework = value.as_str().map(|s| s.to_string()),
                    "tags" => {
                        if let Some(seq) = value.as_sequence() {
                            metadata.tags = seq.iter()
                                .filter_map(|v| v.as_str())
                                .map(|s| s.to_string())
                                .collect();
                        }
                    },
                    "extends" | "inherits" => {
                        if let Some(seq) = value.as_sequence() {
                            inheritance.extend(seq.iter()
                                .filter_map(|v| v.as_str())
                                .map(|s| s.to_string()));
                        } else if let Some(s) = value.as_str() {
                            inheritance.push(s.to_string());
                        }
                    },
                    "variables" => {
                        if let Some(mapping) = value.as_mapping() {
                            for (var_key, var_value) in mapping {
                                if let (Some(k), Some(v)) = (var_key.as_str(), var_value.as_str()) {
                                    variables.insert(k.to_string(), v.to_string());
                                }
                            }
                        }
                    },
                    _ => {}
                }
            }
        }
    }
}

/// Determines section type based on title
fn determine_section_type(title: &str) -> String {
    let title_lower = title.to_lowercase();
    
    if title_lower.contains("system") || title_lower.contains("prompt") {
        "system_prompt".to_string()
    } else if title_lower.contains("instruction") {
        "instructions".to_string()
    } else if title_lower.contains("example") {
        "examples".to_string()
    } else if title_lower.contains("rule") || title_lower.contains("guideline") {
        "rules".to_string()
    } else if title_lower.contains("context") {
        "context".to_string()
    } else if title_lower.contains("tool") {
        "tools".to_string()
    } else {
        "content".to_string()
    }
}

/// Gets available context templates
#[tauri::command]
pub async fn get_context_templates() -> Result<Vec<ContextTemplate>, String> {
    log::info!("Getting context templates");

    let conn = crate::db::conn().map_err(|e| e.to_string())?;
    let mut stmt = conn.prepare(
        "SELECT id, name, description, category, template_content, schema_definition, variables, is_default, sort_order, created_at, updated_at FROM context_templates ORDER BY sort_order, name"
    ).map_err(|e| e.to_string())?;

    let rows = stmt.query_map([], |row| {
        let variables_json: String = row.get(6)?;
        let variables: Vec<TemplateVariable> = serde_json::from_str(&variables_json).unwrap_or_default();

        Ok(ContextTemplate {
            id: Some(row.get(0)?),
            name: row.get(1)?,
            description: row.get(2)?,
            category: row.get(3)?,
            template_content: row.get(4)?,
            schema_definition: row.get(5)?,
            variables,
            is_default: row.get(7)?,
            sort_order: row.get(8)?,
            created_at: row.get(9)?,
            updated_at: row.get(10)?,
        })
    }).map_err(|e| e.to_string())?;

    let templates: Result<Vec<_>, _> = rows.collect();
    templates.map_err(|e| e.to_string())
}

/// Loads merged project context with inheritance
#[tauri::command]
pub async fn load_project_context(project_path: String) -> Result<ParsedContext, String> {
    log::info!("Loading project context for: {}", project_path);

    let conn = crate::db::conn().map_err(|e| e.to_string())?;
    
    // Get all active contexts for the project
    let mut stmt = conn.prepare(
        "SELECT id, file_path, parsed_content, parent_context_id FROM claude_contexts WHERE project_root = ?1 AND is_active = 1 ORDER BY relative_path"
    ).map_err(|e| e.to_string())?;

    let rows = stmt.query_map(rusqlite::params![&project_path], |row| {
        let parsed_content_json: String = row.get(2)?;
        let parsed_content: Option<ParsedContext> = serde_json::from_str(&parsed_content_json).ok();
        
        Ok((
            row.get::<_, i32>(0)?, // id
            row.get::<_, String>(1)?, // file_path
            parsed_content,
            row.get::<_, Option<i32>>(3)?, // parent_context_id
        ))
    }).map_err(|e| e.to_string())?;

    let contexts: Result<Vec<_>, _> = rows.collect();
    let contexts = contexts.map_err(|e| e.to_string())?;

    // Merge contexts with inheritance
    merge_contexts(contexts)
}

/// Performs topological sort on contexts to ensure proper inheritance order
fn topological_sort_contexts(contexts: Vec<(i32, String, Option<ParsedContext>, Option<i32>)>) -> Result<Vec<(i32, String, Option<ParsedContext>, Option<i32>)>, String> {
    use std::collections::{HashMap, VecDeque};
    
    // Build adjacency list and in-degree count
    let mut graph: HashMap<i32, Vec<i32>> = HashMap::new();
    let mut in_degree: HashMap<i32, usize> = HashMap::new();
    let mut context_map: HashMap<i32, (i32, String, Option<ParsedContext>, Option<i32>)> = HashMap::new();
    
    // Initialize data structures
    for context in contexts {
        let (id, path, _parsed, parent_id) = context.clone();
        context_map.insert(id, context);
        in_degree.insert(id, 0);
        graph.insert(id, Vec::new());
        
        log::debug!("Context {} ({}): parent = {:?}", id, path, parent_id);
    }
    
    // Build the dependency graph
    for (id, _, _, parent_id) in context_map.values() {
        if let Some(parent) = parent_id {
            // parent -> child edge (parent must come before child)
            if let Some(children) = graph.get_mut(parent) {
                children.push(*id);
            }
            // Increase in-degree for child
            if let Some(degree) = in_degree.get_mut(id) {
                *degree += 1;
            }
            log::debug!("Added edge: {} -> {}", parent, id);
        }
    }
    
    // Kahn's algorithm for topological sorting
    let mut queue: VecDeque<i32> = VecDeque::new();
    let mut result = Vec::new();
    
    // Start with nodes that have no incoming edges (roots)
    for (&id, &degree) in &in_degree {
        if degree == 0 {
            queue.push_back(id);
            log::debug!("Root context: {}", id);
        }
    }
    
    // Process nodes in topological order
    while let Some(current_id) = queue.pop_front() {
        // Add current context to result
        if let Some(context) = context_map.remove(&current_id) {
            log::debug!("Processing context {} in topological order", current_id);
            result.push(context);
        }
        
        // Process all children of current node
        if let Some(children) = graph.get(&current_id) {
            for &child_id in children {
                // Decrease in-degree of child
                if let Some(degree) = in_degree.get_mut(&child_id) {
                    *degree -= 1;
                    // If child has no more dependencies, add to queue
                    if *degree == 0 {
                        queue.push_back(child_id);
                        log::debug!("Child context {} ready for processing", child_id);
                    }
                }
            }
        }
    }
    
    // Check for circular dependencies
    if result.len() != context_map.len() + result.len() {
        log::warn!("Possible circular dependency detected in context inheritance");
        // Add remaining contexts in arbitrary order
        for (_, context) in context_map {
            result.push(context);
        }
    }
    
    log::debug!("Topological sort result: {:?}", result.iter().map(|(id, path, _, parent_id)| (id, path, parent_id)).collect::<Vec<_>>());
    Ok(result)
}

/// Merges multiple contexts respecting inheritance hierarchy
pub fn merge_contexts(contexts: Vec<(i32, String, Option<ParsedContext>, Option<i32>)>) -> Result<ParsedContext, String> {
    let mut merged = ParsedContext {
        sections: Vec::new(),
        metadata: ContextMetadata {
            title: None,
            description: None,
            version: None,
            author: None,
            tags: Vec::new(),
            language: None,
            framework: None,
        },
        variables: std::collections::HashMap::new(),
        inheritance: Vec::new(),
    };

    // Sort contexts by inheritance hierarchy using topological sort
    let sorted_contexts = topological_sort_contexts(contexts)?;
    
    log::debug!("After topological sort - contexts: {:?}", sorted_contexts.iter().map(|(id, path, _, parent_id)| (id, path, parent_id)).collect::<Vec<_>>());

    for (context_id, file_path, parsed_context, parent_id) in sorted_contexts {
        log::debug!("Processing context {} from {} (parent: {:?})", context_id, file_path, parent_id);
        if let Some(context) = parsed_context {
            // Merge sections
            for section in context.sections {
                // Check if section already exists
                if let Some(existing) = merged.sections.iter_mut().find(|s| s.title == section.title) {
                    // Append content to existing section
                    existing.content.push_str("\n\n");
                    existing.content.push_str(&section.content);
                } else {
                    merged.sections.push(section);
                }
            }

            // Merge metadata (later contexts override earlier ones)
            log::debug!("Before metadata merge - current title: {:?}, new title: {:?}", merged.metadata.title, context.metadata.title);
            if context.metadata.title.is_some() {
                log::debug!("Overriding title from {:?} to {:?}", merged.metadata.title, context.metadata.title);
                merged.metadata.title = context.metadata.title;
            }
            if context.metadata.description.is_some() {
                merged.metadata.description = context.metadata.description;
            }
            if context.metadata.version.is_some() {
                merged.metadata.version = context.metadata.version;
            }
            if context.metadata.author.is_some() {
                merged.metadata.author = context.metadata.author;
            }
            if context.metadata.language.is_some() {
                merged.metadata.language = context.metadata.language;
            }
            if context.metadata.framework.is_some() {
                merged.metadata.framework = context.metadata.framework;
            }

            // Merge tags (deduplicate)
            for tag in context.metadata.tags {
                if !merged.metadata.tags.contains(&tag) {
                    merged.metadata.tags.push(tag);
                }
            }

            // Merge variables (later contexts override earlier ones)
            log::debug!("Before variable merge - current variables: {:?}", merged.variables);
            for (key, value) in context.variables {
                log::debug!("Setting variable {} = {} (was: {:?})", key, value, merged.variables.get(&key));
                merged.variables.insert(key, value);
            }
            log::debug!("After variable merge - variables: {:?}", merged.variables);

            // Track inheritance
            merged.inheritance.push(file_path);
        }
    }

    Ok(merged)
}

/// Creates a new context file from template
#[tauri::command]
pub async fn create_context_from_template(
    template_name: String,
    target_path: String,
    variables: std::collections::HashMap<String, String>,
) -> Result<ContextFile, String> {
    log::info!("Creating context from template: {} at {}", template_name, target_path);

    let conn = crate::db::conn().map_err(|e| e.to_string())?;
    
    // Get template
    let template: ContextTemplate = conn.query_row(
        "SELECT id, name, description, category, template_content, schema_definition, variables, is_default, sort_order, created_at, updated_at FROM context_templates WHERE name = ?1",
        rusqlite::params![&template_name],
        |row| {
            let variables_json: String = row.get(6)?;
            let template_variables: Vec<TemplateVariable> = serde_json::from_str(&variables_json).unwrap_or_default();

            Ok(ContextTemplate {
                id: Some(row.get(0)?),
                name: row.get(1)?,
                description: row.get(2)?,
                category: row.get(3)?,
                template_content: row.get(4)?,
                schema_definition: row.get(5)?,
                variables: template_variables,
                is_default: row.get(7)?,
                sort_order: row.get(8)?,
                created_at: row.get(9)?,
                updated_at: row.get(10)?,
            })
        }
    ).optional().map_err(|e| e.to_string())?
    .ok_or_else(|| format!("Template not found: {}", template_name))?;

    // Process template content with variables
    let mut content = template.template_content.clone();
    for (key, value) in &variables {
        let placeholder = format!("{{{{{}}}}}", key);
        content = content.replace(&placeholder, value);
    }

    // Ensure target directory exists
    let target_path_buf = PathBuf::from(&target_path);
    if let Some(parent) = target_path_buf.parent() {
        fs::create_dir_all(parent)
            .map_err(|e| format!("Failed to create target directory: {}", e))?;
    }

    // Write content to file
    fs::write(&target_path, &content)
        .map_err(|e| format!("Failed to write context file: {}", e))?;

    // Create context file record
    let project_root = find_project_root(&target_path_buf)?;
    let relative_path = target_path_buf
        .strip_prefix(&project_root)
        .map_err(|e| format!("Failed to get relative path: {}", e))?
        .to_string_lossy()
        .to_string();

    let content_hash = format!("{:x}", md5::compute(&content));
    let parsed_content = parse_context_content(&content).ok();
    let now = chrono::Utc::now().to_rfc3339();

    // Insert into database
    conn.execute(
        "INSERT INTO claude_contexts (file_path, project_root, relative_path, content_hash, parsed_content, template_type, is_active, created_at, updated_at, access_count) VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10)",
        rusqlite::params![
            &target_path,
            project_root.to_string_lossy(),
            &relative_path,
            &content_hash,
            serde_json::to_string(&parsed_content).unwrap_or_default(),
            &template.category,
            true,
            &now,
            &now,
            0
        ]
    ).map_err(|e| e.to_string())?;

    let id = conn.last_insert_rowid() as i32;

    Ok(ContextFile {
        id: Some(id),
        file_path: target_path,
        project_root: project_root.to_string_lossy().to_string(),
        relative_path,
        content_hash,
        parsed_content,
        template_type: Some(template.category),
        parent_context_id: None,
        is_active: true,
        created_at: now.clone(),
        updated_at: now,
        last_accessed_at: None,
        access_count: 0,
    })
}

/// Finds the project root directory
fn find_project_root(path: &PathBuf) -> Result<PathBuf, String> {
    let mut current = path.clone();
    
    loop {
        // Check for common project root indicators
        let indicators = [
            ".git",
            "package.json",
            "Cargo.toml",
            "pyproject.toml",
            "requirements.txt",
            ".project",
        ];

        for indicator in &indicators {
            if current.join(indicator).exists() {
                return Ok(current);
            }
        }

        if let Some(parent) = current.parent() {
            current = parent.to_path_buf();
        } else {
            break;
        }
    }

    // If no project root found, use the directory containing the file
    if let Some(parent) = path.parent() {
        Ok(parent.to_path_buf())
    } else {
        Err("Could not determine project root".to_string())
    }
}

/// Gets context history for a specific context file
#[tauri::command]
pub async fn get_context_history(context_id: i32) -> Result<Vec<ContextHistory>, String> {
    log::info!("Getting context history for context ID: {}", context_id);

    let conn = crate::db::conn().map_err(|e| e.to_string())?;
    let mut stmt = conn.prepare(
        "SELECT id, context_id, version, content_hash, content, change_description, changed_by, created_at FROM context_history WHERE context_id = ?1 ORDER BY version DESC"
    ).map_err(|e| e.to_string())?;

    let rows = stmt.query_map(rusqlite::params![context_id], |row| {
        Ok(ContextHistory {
            id: row.get(0)?,
            context_id: row.get(1)?,
            version: row.get(2)?,
            content_hash: row.get(3)?,
            content: row.get(4)?,
            change_description: row.get(5)?,
            changed_by: row.get(6)?,
            created_at: row.get(7)?,
        })
    }).map_err(|e| e.to_string())?;

    let history: Result<Vec<_>, _> = rows.collect();
    history.map_err(|e| e.to_string())
}

/// Updates context inheritance relationships
#[tauri::command]
pub async fn update_context_inheritance(
    child_context_id: i32,
    parent_context_ids: Vec<i32>,
    inheritance_type: String,
) -> Result<(), String> {
    log::info!("Updating context inheritance for context ID: {}", child_context_id);

    let conn = crate::db::conn().map_err(|e| e.to_string())?;
    let now = chrono::Utc::now().to_rfc3339();

    // Remove existing inheritance relationships
    conn.execute(
        "DELETE FROM context_inheritance WHERE child_context_id = ?1",
        rusqlite::params![child_context_id]
    ).map_err(|e| e.to_string())?;

    // Add new inheritance relationships
    for (priority, parent_id) in parent_context_ids.iter().enumerate() {
        conn.execute(
            "INSERT INTO context_inheritance (child_context_id, parent_context_id, inheritance_type, priority, created_at) VALUES (?1, ?2, ?3, ?4, ?5)",
            rusqlite::params![
                child_context_id,
                parent_id,
                &inheritance_type,
                priority as i32,
                &now
            ]
        ).map_err(|e| e.to_string())?;
    }

    Ok(())
}

/// Send a Claude session to the background
#[tauri::command]
pub async fn send_to_background(app: AppHandle, session_id: String) -> Result<(), String> {
    log::info!("Sending session {} to background", session_id);
    
    let registry = app.state::<crate::process::ProcessRegistryState>();
    match registry.0.set_claude_session_background(&session_id, true) {
        Ok(true) => {
            // Emit event to notify UI
            app.emit("session-background-changed", serde_json::json!({
                "sessionId": session_id,
                "isBackground": true
            })).map_err(|e| e.to_string())?;
            Ok(())
        }
        Ok(false) => Err(format!("Session {} not found", session_id)),
        Err(e) => Err(format!("Failed to send session to background: {}", e))
    }
}

/// Bring a Claude session to the foreground
#[tauri::command]
pub async fn bring_to_foreground(app: AppHandle, session_id: String) -> Result<(), String> {
    log::info!("Bringing session {} to foreground", session_id);
    
    let registry = app.state::<crate::process::ProcessRegistryState>();
    match registry.0.set_claude_session_background(&session_id, false) {
        Ok(true) => {
            // Emit event to notify UI
            app.emit("session-background-changed", serde_json::json!({
                "sessionId": session_id,
                "isBackground": false
            })).map_err(|e| e.to_string())?;
            Ok(())
        }
        Ok(false) => Err(format!("Session {} not found", session_id)),
        Err(e) => Err(format!("Failed to bring session to foreground: {}", e))
    }
}

/// Approve plan execution for a Claude session
#[tauri::command]
pub async fn approve_plan_execution(app: AppHandle, session_id: String) -> Result<(), String> {
    log::info!("Approving plan execution for session {}", session_id);
    
    // Emit an event that the Claude process can listen for
    app.emit(&format!("plan-approval:{}", session_id), serde_json::json!({
        "approved": true,
        "sessionId": session_id
    })).map_err(|e| e.to_string())?;
    
    Ok(())
}

/// Reject plan execution for a Claude session
#[tauri::command]
pub async fn reject_plan_execution(app: AppHandle, session_id: String) -> Result<(), String> {
    log::info!("Rejecting plan execution for session {}", session_id);
    
    // Emit an event that the Claude process can listen for
    app.emit(&format!("plan-approval:{}", session_id), serde_json::json!({
        "approved": false,
        "sessionId": session_id
    })).map_err(|e| e.to_string())?;
    
    Ok(())
}

/// Delete a checkpoint
#[tauri::command]
pub async fn delete_checkpoint(
    checkpoint_id: String,
    session_id: String,
    project_id: String,
) -> Result<(), String> {
    log::info!(
        "Deleting checkpoint {} for session {} in project {}",
        checkpoint_id,
        session_id,
        project_id
    );
    
    // Build paths
    let claude_dir = dirs::home_dir()
        .ok_or_else(|| "Could not find home directory".to_string())?
        .join(".claude");
    
    // Get checkpoint storage
    let checkpoint_storage = crate::checkpoint::storage::CheckpointStorage::new(claude_dir.clone());
    let paths = crate::checkpoint::CheckpointPaths::new(&claude_dir, &project_id, &session_id);
    
    // Remove the checkpoint
    checkpoint_storage.remove_checkpoint(&paths, &checkpoint_id)
        .map_err(|e| format!("Failed to delete checkpoint: {}", e))?;
    
    // Optionally run garbage collection to clean up unreferenced content
    match checkpoint_storage.garbage_collect_content(&project_id, &session_id) {
        Ok(removed) => {
            if removed > 0 {
                log::info!("Garbage collected {} unreferenced content files", removed);
            }
        }
        Err(e) => {
            log::warn!("Garbage collection failed: {}", e);
            // Don't fail the operation if GC fails
        }
    }
    
    Ok(())
}

/// Initializes default context templates in the database
#[tauri::command]
pub async fn initialize_context_templates() -> Result<(), String> {
    log::info!("Initializing default context templates");

    let conn = crate::db::conn().map_err(|e| e.to_string())?;
    let now = chrono::Utc::now().to_rfc3339();

    // Check if templates already exist
    let count: i32 = conn.query_row(
        "SELECT COUNT(*) FROM context_templates",
        [],
        |row| row.get(0)
    ).map_err(|e| e.to_string())?;

    if count > 0 {
        log::info!("Templates already exist, skipping initialization");
        return Ok(());
    }

    // Define default templates
    let templates = vec![
        (
            "React TypeScript",
            "Context template for React TypeScript frontend applications",
            "react",
            include_str!("../../../src/templates/react-typescript.md"),
            r#"[{"name":"project_name","description":"Name of the project","var_type":"string","default_value":"","required":true},{"name":"description","description":"Project description","var_type":"string","default_value":"","required":true},{"name":"author","description":"Project author","var_type":"string","default_value":"","required":false}]"#,
            true,
            0
        ),
        (
            "Rust Tauri",
            "Context template for Rust Tauri desktop applications",
            "rust",
            include_str!("../../../src/templates/rust-tauri.md"),
            r#"[{"name":"project_name","description":"Name of the project","var_type":"string","default_value":"","required":true},{"name":"description","description":"Project description","var_type":"string","default_value":"","required":true},{"name":"author","description":"Project author","var_type":"string","default_value":"","required":false}]"#,
            true,
            1
        ),
        (
            "Python",
            "Context template for Python applications",
            "python",
            include_str!("../../../src/templates/python.md"),
            r#"[{"name":"project_name","description":"Name of the project","var_type":"string","default_value":"","required":true},{"name":"description","description":"Project description","var_type":"string","default_value":"","required":true},{"name":"author","description":"Project author","var_type":"string","default_value":"","required":false}]"#,
            false,
            2
        ),
        (
            "Generic",
            "Generic context template for any project type",
            "generic",
            include_str!("../../../src/templates/generic.md"),
            r#"[{"name":"project_name","description":"Name of the project","var_type":"string","default_value":"","required":true},{"name":"description","description":"Project description","var_type":"string","default_value":"","required":true},{"name":"author","description":"Project author","var_type":"string","default_value":"","required":false},{"name":"language","description":"Programming language","var_type":"string","default_value":"","required":true}]"#,
            false,
            3
        ),
    ];

    // Log template count before iteration (diagnostic)
    log::debug!("About to process {} templates", templates.len());

    // Insert templates - use reference to avoid moving the vector
    for (name, description, category, content, variables, is_default, sort_order) in &templates {
        conn.execute(
            "INSERT INTO context_templates (name, description, category, template_content, schema_definition, variables, is_default, sort_order, created_at, updated_at) VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10)",
            rusqlite::params![
                name,
                description,
                category,
                content,
                "", // schema_definition - can be added later
                variables,
                is_default,
                sort_order,
                &now,
                &now
            ]
        ).map_err(|e| e.to_string())?;
    }

    // Now templates.len() is accessible because we didn't move the vector
    log::info!("Successfully initialized {} context templates", templates.len());
    Ok(())
}

// Include tests module
#[cfg(test)]
mod tests;
