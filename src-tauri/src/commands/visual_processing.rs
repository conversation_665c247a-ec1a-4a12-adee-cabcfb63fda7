use serde::{Deserialize, Serialize};
use std::fs;
use std::path::Path;
use std::collections::HashMap;
use image::{DynamicImage, GenericImageView, ImageFormat};
use rusqlite::{Connection, params};
use dirs;
use chrono::Utc;
use uuid::Uuid;
use base64::{Engine as _, engine::general_purpose};

use crate::commands::enhanced_content::{ProcessedDocument, DocumentChunk, DocumentMetadata};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VisualProcessingResult {
    pub id: String,
    pub source: String,
    pub content_type: String,
    pub dimensions: (u32, u32),
    pub format: String,
    pub color_mode: String,
    pub extracted_text: Option<String>,
    pub detected_objects: Vec<DetectedObject>,
    pub metadata: HashMap<String, serde_json::Value>,
    pub thumbnail_base64: Option<String>,
    pub processed_at: String,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct DetectedObject {
    pub label: String,
    pub confidence: f32,
    pub bounding_box: BoundingBox,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BoundingBox {
    pub x: u32,
    pub y: u32,
    pub width: u32,
    pub height: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VideoProcessingResult {
    pub id: String,
    pub source: String,
    pub duration_seconds: f64,
    pub fps: f32,
    pub resolution: (u32, u32),
    pub codec: String,
    pub extracted_frames: Vec<ExtractedFrame>,
    pub transcript: Option<String>,
    pub metadata: HashMap<String, serde_json::Value>,
    pub processed_at: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExtractedFrame {
    pub timestamp_seconds: f64,
    pub frame_number: u32,
    pub thumbnail_base64: String,
    pub detected_objects: Vec<DetectedObject>,
}

// Initialize visual processing database
pub fn init_visual_db() -> Result<Connection, String> {
    let app_dir = dirs::data_dir()
        .ok_or_else(|| "Failed to get app data directory".to_string())?
        .join("sanity")
        .join("visual_processing");
    
    fs::create_dir_all(&app_dir)
        .map_err(|e| format!("Failed to create directory: {}", e))?;
    
    let db_path = app_dir.join("visual.db");
    let conn = Connection::open(db_path)
        .map_err(|e| format!("Failed to open database: {}", e))?;
    
    // Create tables for visual content
    conn.execute(
        "CREATE TABLE IF NOT EXISTS visual_content (
            id TEXT PRIMARY KEY,
            source TEXT NOT NULL,
            content_type TEXT NOT NULL,
            width INTEGER,
            height INTEGER,
            format TEXT,
            color_mode TEXT,
            extracted_text TEXT,
            detected_objects TEXT,
            thumbnail BLOB,
            metadata TEXT DEFAULT '{}',
            processed_at TEXT NOT NULL,
            created_at TEXT NOT NULL
        )",
        [],
    ).map_err(|e| format!("Failed to create visual_content table: {}", e))?;
    
    // Create video content table
    conn.execute(
        "CREATE TABLE IF NOT EXISTS video_content (
            id TEXT PRIMARY KEY,
            source TEXT NOT NULL,
            duration_seconds REAL,
            fps REAL,
            width INTEGER,
            height INTEGER,
            codec TEXT,
            transcript TEXT,
            metadata TEXT DEFAULT '{}',
            processed_at TEXT NOT NULL,
            created_at TEXT NOT NULL
        )",
        [],
    ).map_err(|e| format!("Failed to create video_content table: {}", e))?;
    
    // Create extracted frames table
    conn.execute(
        "CREATE TABLE IF NOT EXISTS extracted_frames (
            id TEXT PRIMARY KEY,
            video_id TEXT NOT NULL,
            timestamp_seconds REAL,
            frame_number INTEGER,
            thumbnail BLOB,
            detected_objects TEXT,
            created_at TEXT NOT NULL,
            FOREIGN KEY (video_id) REFERENCES video_content(id) ON DELETE CASCADE
        )",
        [],
    ).map_err(|e| format!("Failed to create extracted_frames table: {}", e))?;
    
    // Create indexes
    conn.execute("CREATE INDEX IF NOT EXISTS idx_visual_content_source ON visual_content(source)", [])
        .map_err(|e| format!("Failed to create index: {}", e))?;
    conn.execute("CREATE INDEX IF NOT EXISTS idx_visual_content_type ON visual_content(content_type)", [])
        .map_err(|e| format!("Failed to create index: {}", e))?;
    conn.execute("CREATE INDEX IF NOT EXISTS idx_video_content_source ON video_content(source)", [])
        .map_err(|e| format!("Failed to create index: {}", e))?;
    
    Ok(conn)
}

// Process image file
#[tauri::command]
pub async fn process_image_file(file_path: String, use_ocr: Option<bool>) -> Result<VisualProcessingResult, String> {
    let path = Path::new(&file_path);
    
    if !path.exists() {
        return Err(format!("Image file not found: {}", file_path));
    }
    
    // Load image
    let img = image::open(path)
        .map_err(|e| format!("Failed to open image: {}", e))?;
    
    let (width, height) = img.dimensions();
    let color_mode = match img.color() {
        image::ColorType::L8 => "Grayscale",
        image::ColorType::La8 => "GrayscaleAlpha",
        image::ColorType::Rgb8 => "RGB",
        image::ColorType::Rgba8 => "RGBA",
        _ => "Unknown",
    };
    
    // Generate thumbnail (max 200x200)
    let thumbnail = img.thumbnail(200, 200);
    let mut thumbnail_bytes = Vec::new();
    thumbnail.write_to(&mut std::io::Cursor::new(&mut thumbnail_bytes), ImageFormat::Png)
        .map_err(|e| format!("Failed to create thumbnail: {}", e))?;
    let thumbnail_base64 = general_purpose::STANDARD.encode(&thumbnail_bytes);
    
    // Extract text if OCR is requested
    let extracted_text = if use_ocr.unwrap_or(false) {
        // In production, integrate with Tesseract or cloud OCR service
        Some(format!("OCR text extraction would be performed here for image: {}", path.file_name().unwrap_or_default().to_string_lossy()))
    } else {
        None
    };
    
    // Detect objects (placeholder - integrate with ML model)
    let detected_objects = detect_objects_in_image(&img);
    
    let result = VisualProcessingResult {
        id: Uuid::new_v4().to_string(),
        source: file_path.clone(),
        content_type: "Image".to_string(),
        dimensions: (width, height),
        format: path.extension()
            .and_then(|e| e.to_str())
            .unwrap_or("unknown")
            .to_uppercase(),
        color_mode: color_mode.to_string(),
        extracted_text: extracted_text.clone(),
        detected_objects: detected_objects.clone(),
        metadata: HashMap::new(),
        thumbnail_base64: Some(thumbnail_base64.clone()),
        processed_at: Utc::now().to_rfc3339(),
    };
    
    // Store in database
    store_visual_processing_result(&result)?;
    
    Ok(result)
}

// Placeholder object detection
fn detect_objects_in_image(_img: &DynamicImage) -> Vec<DetectedObject> {
    // In production, integrate with YOLO, TensorFlow, or cloud vision API
    vec![]
}

// Store visual processing result
fn store_visual_processing_result(result: &VisualProcessingResult) -> Result<(), String> {
    let conn = init_visual_db()?;
    
    let detected_objects_json = serde_json::to_string(&result.detected_objects)
        .map_err(|e| format!("Failed to serialize objects: {}", e))?;
    
    let metadata_json = serde_json::to_string(&result.metadata)
        .map_err(|e| format!("Failed to serialize metadata: {}", e))?;
    
    let thumbnail_blob = result.thumbnail_base64.as_ref()
        .map(|b| general_purpose::STANDARD.decode(b).unwrap_or_default());
    
    conn.execute(
        "INSERT INTO visual_content (
            id, source, content_type, width, height, format, color_mode,
            extracted_text, detected_objects, thumbnail, metadata,
            processed_at, created_at
        ) VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10, ?11, ?12, ?13)",
        params![
            result.id,
            result.source,
            result.content_type,
            result.dimensions.0,
            result.dimensions.1,
            result.format,
            result.color_mode,
            result.extracted_text,
            detected_objects_json,
            thumbnail_blob,
            metadata_json,
            result.processed_at,
            Utc::now().to_rfc3339()
        ],
    ).map_err(|e| format!("Failed to store visual content: {}", e))?;
    
    Ok(())
}

// Process video file
#[tauri::command]
pub async fn process_video_file(file_path: String, extract_frames: Option<bool>) -> Result<VideoProcessingResult, String> {
    let path = Path::new(&file_path);
    
    if !path.exists() {
        return Err(format!("Video file not found: {}", file_path));
    }
    
    // For now, return placeholder data
    // In production, integrate with FFmpeg or video processing library
    let result = VideoProcessingResult {
        id: Uuid::new_v4().to_string(),
        source: file_path.clone(),
        duration_seconds: 120.0, // Placeholder
        fps: 30.0,
        resolution: (1920, 1080),
        codec: "H.264".to_string(),
        extracted_frames: if extract_frames.unwrap_or(false) {
            vec![
                ExtractedFrame {
                    timestamp_seconds: 0.0,
                    frame_number: 0,
                    thumbnail_base64: "".to_string(),
                    detected_objects: vec![],
                },
                ExtractedFrame {
                    timestamp_seconds: 60.0,
                    frame_number: 1800,
                    thumbnail_base64: "".to_string(),
                    detected_objects: vec![],
                },
            ]
        } else {
            vec![]
        },
        transcript: Some("Video transcript would be extracted here using speech-to-text".to_string()),
        metadata: HashMap::new(),
        processed_at: Utc::now().to_rfc3339(),
    };
    
    // Store in database
    store_video_processing_result(&result)?;
    
    Ok(result)
}

// Store video processing result
fn store_video_processing_result(result: &VideoProcessingResult) -> Result<(), String> {
    let conn = init_visual_db()?;
    
    let metadata_json = serde_json::to_string(&result.metadata)
        .map_err(|e| format!("Failed to serialize metadata: {}", e))?;
    
    // Store video info
    conn.execute(
        "INSERT INTO video_content (
            id, source, duration_seconds, fps, width, height, codec,
            transcript, metadata, processed_at, created_at
        ) VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10, ?11)",
        params![
            result.id,
            result.source,
            result.duration_seconds,
            result.fps,
            result.resolution.0,
            result.resolution.1,
            result.codec,
            result.transcript,
            metadata_json,
            result.processed_at,
            Utc::now().to_rfc3339()
        ],
    ).map_err(|e| format!("Failed to store video content: {}", e))?;
    
    // Store extracted frames
    for frame in &result.extracted_frames {
        let frame_id = Uuid::new_v4().to_string();
        let objects_json = serde_json::to_string(&frame.detected_objects)
            .map_err(|e| format!("Failed to serialize frame objects: {}", e))?;
        
        conn.execute(
            "INSERT INTO extracted_frames (
                id, video_id, timestamp_seconds, frame_number,
                thumbnail, detected_objects, created_at
            ) VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7)",
            params![
                frame_id,
                result.id,
                frame.timestamp_seconds,
                frame.frame_number,
                general_purpose::STANDARD.decode(&frame.thumbnail_base64).ok(),
                objects_json,
                Utc::now().to_rfc3339()
            ],
        ).map_err(|e| format!("Failed to store frame: {}", e))?;
    }
    
    Ok(())
}

// Get all processed visual content
#[tauri::command]
pub async fn get_visual_content(limit: Option<usize>) -> Result<Vec<VisualProcessingResult>, String> {
    let conn = init_visual_db()?;
    let limit = limit.unwrap_or(50);
    
    let mut stmt = conn.prepare(
        "SELECT id, source, content_type, width, height, format, color_mode,
         extracted_text, detected_objects, thumbnail, metadata, processed_at
         FROM visual_content
         ORDER BY created_at DESC
         LIMIT ?1"
    ).map_err(|e| format!("Failed to prepare statement: {}", e))?;
    
    let results = stmt.query_map(params![limit], |row| {
        let objects_json: String = row.get(8)?;
        let metadata_json: String = row.get(10)?;
        let thumbnail_blob: Option<Vec<u8>> = row.get(9)?;
        
        Ok(VisualProcessingResult {
            id: row.get(0)?,
            source: row.get(1)?,
            content_type: row.get(2)?,
            dimensions: (row.get(3)?, row.get(4)?),
            format: row.get(5)?,
            color_mode: row.get(6)?,
            extracted_text: row.get(7)?,
            detected_objects: serde_json::from_str(&objects_json).unwrap_or_default(),
            metadata: serde_json::from_str(&metadata_json).unwrap_or_default(),
            thumbnail_base64: thumbnail_blob.map(|b| general_purpose::STANDARD.encode(&b)),
            processed_at: row.get(11)?,
        })
    }).map_err(|e| format!("Failed to query visual content: {}", e))?;
    
    let mut visual_results = Vec::new();
    for result in results {
        visual_results.push(result.map_err(|e| format!("Failed to parse result: {}", e))?);
    }
    
    Ok(visual_results)
}

// Search visual content by text
#[tauri::command]
pub async fn search_visual_content(query: String) -> Result<Vec<VisualProcessingResult>, String> {
    let conn = init_visual_db()?;
    
    let mut stmt = conn.prepare(
        "SELECT id, source, content_type, width, height, format, color_mode,
         extracted_text, detected_objects, thumbnail, metadata, processed_at
         FROM visual_content
         WHERE extracted_text LIKE ?1 OR source LIKE ?1 OR detected_objects LIKE ?1
         ORDER BY created_at DESC
         LIMIT 50"
    ).map_err(|e| format!("Failed to prepare statement: {}", e))?;
    
    let search_pattern = format!("%{}%", query);
    let results = stmt.query_map(params![search_pattern], |row| {
        let objects_json: String = row.get(8)?;
        let metadata_json: String = row.get(10)?;
        let thumbnail_blob: Option<Vec<u8>> = row.get(9)?;
        
        Ok(VisualProcessingResult {
            id: row.get(0)?,
            source: row.get(1)?,
            content_type: row.get(2)?,
            dimensions: (row.get(3)?, row.get(4)?),
            format: row.get(5)?,
            color_mode: row.get(6)?,
            extracted_text: row.get(7)?,
            detected_objects: serde_json::from_str(&objects_json).unwrap_or_default(),
            metadata: serde_json::from_str(&metadata_json).unwrap_or_default(),
            thumbnail_base64: thumbnail_blob.map(|b| general_purpose::STANDARD.encode(&b)),
            processed_at: row.get(11)?,
        })
    }).map_err(|e| format!("Failed to search visual content: {}", e))?;
    
    let mut visual_results = Vec::new();
    for result in results {
        visual_results.push(result.map_err(|e| format!("Failed to parse result: {}", e))?);
    }
    
    Ok(visual_results)
}

// Convert visual result to ProcessedDocument for compatibility
pub fn visual_to_processed_document(visual: &VisualProcessingResult) -> ProcessedDocument {
    let content = format!(
        "Image: {}\nDimensions: {}x{}\nFormat: {}\nColor Mode: {}\n{}",
        visual.source,
        visual.dimensions.0,
        visual.dimensions.1,
        visual.format,
        visual.color_mode,
        visual.extracted_text.as_ref().unwrap_or(&"No text extracted".to_string())
    );
    
    ProcessedDocument {
        source: visual.source.clone(),
        content_type: visual.content_type.clone(),
        raw_content: content.clone(),
        processed_content: content.clone(),
        chunks: vec![DocumentChunk {
            id: visual.id.clone(),
            content: content.clone(),
            start_index: 0,
            end_index: content.len(),
            token_count: content.split_whitespace().count(),
            metadata: HashMap::new(),
        }],
        metadata: DocumentMetadata {
            title: Some(Path::new(&visual.source).file_name()
                .and_then(|n| n.to_str())
                .unwrap_or("Unknown").to_string()),
            author: None,
            created_at: Some(visual.processed_at.clone()),
            modified_at: None,
            language: None,
            word_count: Some(content.split_whitespace().count()),
            page_count: None,
            tags: vec![visual.format.clone(), visual.color_mode.clone()],
            custom: visual.metadata.clone(),
        },
        embeddings: None,
    }
}