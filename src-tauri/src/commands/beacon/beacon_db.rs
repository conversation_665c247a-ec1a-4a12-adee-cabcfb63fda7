use anyhow::Result;
use rusqlite::{params, Connection, OptionalExtension};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

// Beacon Database Models
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Page {
    pub id: String,
    pub title: String,
    pub content: String,
    pub tags: Vec<String>,
    pub parent_id: Option<String>,
    pub template_id: Option<String>,
    pub metadata: PageMetadata,
    pub settings: PageSettings,
    pub created_at: i64,
    pub updated_at: i64,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct PageMetadata {
    pub word_count: usize,
    pub reading_time: usize,
    pub version: i32,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct PageSettings {
    pub is_public: bool,
    pub allow_comments: bool,
    pub enable_collaboration: bool,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct PageTemplate {
    pub id: String,
    pub name: String,
    pub description: String,
    pub content: String,
    pub category: String,
    pub tags: Vec<String>,
    pub preview_image: Option<String>,
    pub created_at: i64,
    pub updated_at: i64,
}

#[derive(Debug, Serialize, Deserialize, <PERSON>lone)]
pub struct Project {
    pub id: String,
    pub name: String,
    pub description: String,
    pub tags: Vec<String>,
    pub color: String,
    pub icon: String,
    pub status: String,
    pub priority: String,
    pub created_at: i64,
    pub updated_at: i64,
    pub metadata: ProjectMetadata,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ProjectMetadata {
    pub page_count: usize,
    pub last_activity: i64,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ProjectTemplate {
    pub id: String,
    pub name: String,
    pub description: String,
    pub default_structure: String,
    pub default_tags: Vec<String>,
    pub icon: String,
    pub created_at: i64,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct SavedSearch {
    pub id: String,
    pub name: String,
    pub filters: SearchFilters,
    pub created_at: i64,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct SearchFilters {
    pub query: String,
    pub tags: Vec<String>,
    pub content_type: Vec<String>,
    pub date_range: Option<DateRange>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct DateRange {
    pub start: i64,
    pub end: i64,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Source {
    pub id: String,
    pub name: String,
    pub source_type: String, // pdf, txt, docx, url, note
    pub content: Option<String>,
    pub url: Option<String>,
    pub file_path: Option<String>,
    pub metadata: Option<serde_json::Value>,
    pub uploaded_at: i64,
    pub processed_at: Option<i64>,
    pub status: String, // pending, processing, completed, failed
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct KnowledgeItem {
    pub id: String,
    pub title: String,
    pub content: String,
    pub tags: Vec<String>,
    pub source_type: Option<String>, // manual, document, url, note
    pub source_id: Option<String>,
    pub created_at: i64,
    pub updated_at: i64,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ChatMessage {
    pub id: String,
    pub content: String,
    pub is_user: bool,
    pub context_ids: Option<Vec<String>>,
    pub created_at: i64,
}

// Database Functions

pub fn get_connection() -> Result<Connection> {
    crate::db::get_connection()
}

pub fn create_page(page: Page) -> Result<Page> {
    let conn = get_connection()?;
    let metadata_json = serde_json::to_string(&page.metadata)?;
    let settings_json = serde_json::to_string(&page.settings)?;
    let tags_json = serde_json::to_string(&page.tags)?;

    conn.execute(
        "INSERT INTO beacon_pages 
         (id, title, content, tags, parent_id, template_id, metadata, settings, created_at, updated_at) 
         VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10)",
        params![
            &page.id, &page.title, &page.content, &tags_json,
            &page.parent_id, &page.template_id, &metadata_json, &settings_json,
            page.created_at, page.updated_at
        ],
    )?;

    Ok(page)
}

pub fn get_page(page_id: &str) -> Result<Option<Page>> {
    let conn = get_connection()?;
    let result = conn.query_row(
        "SELECT id, title, content, tags, parent_id, template_id, metadata, settings, created_at, updated_at 
         FROM beacon_pages WHERE id = ?1",
        params![page_id],
        |row| {
            let tags_json: String = row.get(3)?;
            let metadata_json: String = row.get(6)?;
            let settings_json: String = row.get(7)?;
            
            Ok(Page {
                id: row.get(0)?,
                title: row.get(1)?,
                content: row.get(2)?,
                tags: serde_json::from_str(&tags_json).unwrap_or_default(),
                parent_id: row.get(4)?,
                template_id: row.get(5)?,
                metadata: serde_json::from_str(&metadata_json).unwrap_or(PageMetadata {
                    word_count: 0,
                    reading_time: 0,
                    version: 1,
                }),
                settings: serde_json::from_str(&settings_json).unwrap_or(PageSettings {
                    is_public: false,
                    allow_comments: true,
                    enable_collaboration: false,
                }),
                created_at: row.get(8)?,
                updated_at: row.get(9)?,
            })
        }
    ).optional()?;

    Ok(result)
}

pub fn get_all_pages() -> Result<Vec<Page>> {
    let conn = get_connection()?;
    let mut stmt = conn.prepare(
        "SELECT id, title, content, tags, parent_id, template_id, metadata, settings, created_at, updated_at 
         FROM beacon_pages ORDER BY updated_at DESC"
    )?;

    let pages = stmt.query_map([], |row| {
        let tags_json: String = row.get(3)?;
        let metadata_json: String = row.get(6)?;
        let settings_json: String = row.get(7)?;
        
        Ok(Page {
            id: row.get(0)?,
            title: row.get(1)?,
            content: row.get(2)?,
            tags: serde_json::from_str(&tags_json).unwrap_or_default(),
            parent_id: row.get(4)?,
            template_id: row.get(5)?,
            metadata: serde_json::from_str(&metadata_json).unwrap_or(PageMetadata {
                word_count: 0,
                reading_time: 0,
                version: 1,
            }),
            settings: serde_json::from_str(&settings_json).unwrap_or(PageSettings {
                is_public: false,
                allow_comments: true,
                enable_collaboration: false,
            }),
            created_at: row.get(8)?,
            updated_at: row.get(9)?,
        })
    })?;

    let mut result = Vec::new();
    for page in pages {
        result.push(page?);
    }

    Ok(result)
}

pub fn update_page(page: Page) -> Result<Page> {
    let conn = get_connection()?;
    let metadata_json = serde_json::to_string(&page.metadata)?;
    let settings_json = serde_json::to_string(&page.settings)?;
    let tags_json = serde_json::to_string(&page.tags)?;

    conn.execute(
        "UPDATE beacon_pages 
         SET title = ?1, content = ?2, tags = ?3, parent_id = ?4, template_id = ?5, 
             metadata = ?6, settings = ?7, updated_at = ?8 
         WHERE id = ?9",
        params![
            &page.title, &page.content, &tags_json, &page.parent_id, &page.template_id,
            &metadata_json, &settings_json, page.updated_at, &page.id
        ],
    )?;

    Ok(page)
}

pub fn delete_page(page_id: &str) -> Result<bool> {
    let conn = get_connection()?;
    let result = conn.execute("DELETE FROM beacon_pages WHERE id = ?1", params![page_id])?;
    Ok(result > 0)
}

pub fn create_page_template(template: PageTemplate) -> Result<PageTemplate> {
    let conn = get_connection()?;
    let tags_json = serde_json::to_string(&template.tags)?;

    conn.execute(
        "INSERT INTO beacon_page_templates 
         (id, name, description, content, category, tags, preview_image, created_at, updated_at) 
         VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9)",
        params![
            &template.id, &template.name, &template.description, &template.content,
            &template.category, &tags_json, &template.preview_image,
            template.created_at, template.updated_at
        ],
    )?;

    Ok(template)
}

pub fn get_page_templates() -> Result<Vec<PageTemplate>> {
    let conn = get_connection()?;
    let mut stmt = conn.prepare(
        "SELECT id, name, description, content, category, tags, preview_image, created_at, updated_at 
         FROM beacon_page_templates ORDER BY created_at DESC"
    )?;

    let templates = stmt.query_map([], |row| {
        let tags_json: String = row.get(5)?;
        
        Ok(PageTemplate {
            id: row.get(0)?,
            name: row.get(1)?,
            description: row.get(2)?,
            content: row.get(3)?,
            category: row.get(4)?,
            tags: serde_json::from_str(&tags_json).unwrap_or_default(),
            preview_image: row.get(6)?,
            created_at: row.get(7)?,
            updated_at: row.get(8)?,
        })
    })?;

    let mut result = Vec::new();
    for template in templates {
        result.push(template?);
    }

    Ok(result)
}

pub fn create_project(project: Project) -> Result<Project> {
    let conn = get_connection()?;
    let tags_json = serde_json::to_string(&project.tags)?;
    let metadata_json = serde_json::to_string(&project.metadata)?;

    conn.execute(
        "INSERT INTO beacon_projects 
         (id, name, description, tags, color, icon, status, priority, created_at, updated_at, metadata) 
         VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10, ?11)",
        params![
            &project.id, &project.name, &project.description, &tags_json,
            &project.color, &project.icon, &project.status, &project.priority,
            project.created_at, project.updated_at, &metadata_json
        ],
    )?;

    Ok(project)
}

pub fn get_all_projects() -> Result<Vec<Project>> {
    let conn = get_connection()?;
    let mut stmt = conn.prepare(
        "SELECT id, name, description, tags, color, icon, status, priority, created_at, updated_at, metadata 
         FROM beacon_projects ORDER BY updated_at DESC"
    )?;

    let projects = stmt.query_map([], |row| {
        let tags_json: String = row.get(3)?;
        let metadata_json: String = row.get(10)?;
        
        Ok(Project {
            id: row.get(0)?,
            name: row.get(1)?,
            description: row.get(2)?,
            tags: serde_json::from_str(&tags_json).unwrap_or_default(),
            color: row.get(4)?,
            icon: row.get(5)?,
            status: row.get(6)?,
            priority: row.get(7)?,
            created_at: row.get(8)?,
            updated_at: row.get(9)?,
            metadata: serde_json::from_str(&metadata_json).unwrap_or(ProjectMetadata {
                page_count: 0,
                last_activity: 0,
            }),
        })
    })?;

    let mut result = Vec::new();
    for project in projects {
        result.push(project?);
    }

    Ok(result)
}

pub fn get_project_templates() -> Result<Vec<ProjectTemplate>> {
    let conn = get_connection()?;
    let mut stmt = conn.prepare(
        "SELECT id, name, description, default_structure, default_tags, icon, created_at 
         FROM beacon_project_templates ORDER BY created_at DESC"
    )?;

    let templates = stmt.query_map([], |row| {
        let structure_json: String = row.get(3)?;
        let tags_json: String = row.get(4)?;
        
        Ok(ProjectTemplate {
            id: row.get(0)?,
            name: row.get(1)?,
            description: row.get(2)?,
            default_structure: structure_json,
            default_tags: serde_json::from_str(&tags_json).unwrap_or_default(),
            icon: row.get(5)?,
            created_at: row.get(6)?,
        })
    })?;

    let mut result = Vec::new();
    for template in templates {
        result.push(template?);
    }

    Ok(result)
}

pub fn get_project(project_id: &str) -> Result<Option<Project>> {
    let conn = get_connection()?;
    let mut stmt = conn.prepare(
        "SELECT id, name, description, tags, color, icon, status, priority, created_at, updated_at, metadata 
         FROM beacon_projects WHERE id = ?1"
    )?;
    
    let project = stmt.query_row([project_id], |row| {
        let tags_json: String = row.get(3)?;
        let metadata_json: String = row.get(10)?;
        
        Ok(Project {
            id: row.get(0)?,
            name: row.get(1)?,
            description: row.get(2)?,
            tags: serde_json::from_str(&tags_json).unwrap_or_default(),
            color: row.get(4)?,
            icon: row.get(5)?,
            status: row.get(6)?,
            priority: row.get(7)?,
            created_at: row.get(8)?,
            updated_at: row.get(9)?,
            metadata: serde_json::from_str(&metadata_json).unwrap_or(ProjectMetadata {
                page_count: 0,
                last_activity: 0,
            }),
        })
    }).optional()?;
    
    Ok(project)
}

pub fn get_project_template(template_id: &str) -> Result<Option<ProjectTemplate>> {
    let conn = get_connection()?;
    let mut stmt = conn.prepare(
        "SELECT id, name, description, default_structure, default_tags, icon, created_at 
         FROM beacon_project_templates WHERE id = ?1"
    )?;
    
    let template = stmt.query_row([template_id], |row| {
        let structure_json: String = row.get(3)?;
        let tags_json: String = row.get(4)?;
        
        Ok(ProjectTemplate {
            id: row.get(0)?,
            name: row.get(1)?,
            description: row.get(2)?,
            default_structure: structure_json,
            default_tags: serde_json::from_str(&tags_json).unwrap_or_default(),
            icon: row.get(5)?,
            created_at: row.get(6)?,
        })
    }).optional()?;
    
    Ok(template)
}

pub fn update_project(project: Project) -> Result<()> {
    let conn = get_connection()?;
    let tags_json = serde_json::to_string(&project.tags)?;
    let metadata_json = serde_json::to_string(&project.metadata)?;
    
    conn.execute(
        "UPDATE beacon_projects 
         SET name = ?2, description = ?3, tags = ?4, color = ?5, icon = ?6, 
             status = ?7, priority = ?8, updated_at = ?9, metadata = ?10
         WHERE id = ?1",
        params![
            &project.id, &project.name, &project.description, &tags_json,
            &project.color, &project.icon, &project.status, &project.priority,
            project.updated_at, &metadata_json
        ],
    )?;
    
    Ok(())
}

pub fn delete_project(project_id: &str) -> Result<bool> {
    let conn = get_connection()?;
    
    // Delete all pages associated with the project first
    conn.execute(
        "DELETE FROM beacon_pages WHERE parent_id = ?1",
        params![project_id],
    )?;
    
    // Delete the project
    let deleted = conn.execute(
        "DELETE FROM beacon_projects WHERE id = ?1",
        params![project_id],
    )?;
    
    Ok(deleted > 0)
}

pub fn get_all_sources() -> Result<Vec<Source>> {
    let conn = get_connection()?;
    let mut stmt = conn.prepare(
        "SELECT id, name, type, content, url, file_path, metadata, uploaded_at, processed_at, status 
         FROM beacon_sources ORDER BY uploaded_at DESC"
    )?;

    let sources = stmt.query_map([], |row| {
        Ok(Source {
            id: row.get(0)?,
            name: row.get(1)?,
            source_type: row.get(2)?,
            content: row.get(3)?,
            url: row.get(4)?,
            file_path: row.get(5)?,
            metadata: row.get(6)?,
            uploaded_at: row.get(7)?,
            processed_at: row.get(8)?,
            status: row.get(9)?,
        })
    })?;

    let mut result = Vec::new();
    for source in sources {
        result.push(source?);
    }

    Ok(result)
}

pub fn get_user_patterns() -> Result<HashMap<String, f32>> {
    // Mock implementation - in a real app this would be stored in the database
    let mut patterns = HashMap::new();
    patterns.insert("work_manual".to_string(), 0.8);
    patterns.insert("learning_document".to_string(), 0.7);
    patterns.insert("technical_manual".to_string(), 0.9);
    patterns.insert("personal_note".to_string(), 0.6);
    Ok(patterns)
}

pub fn get_saved_searches() -> Result<Vec<SavedSearch>> {
    let conn = get_connection()?;
    let mut stmt = conn.prepare(
        "SELECT id, name, filters, created_at 
         FROM beacon_saved_searches ORDER BY created_at DESC"
    )?;

    let searches = stmt.query_map([], |row| {
        let filters_json: String = row.get(2)?;
        
        Ok(SavedSearch {
            id: row.get(0)?,
            name: row.get(1)?,
            filters: serde_json::from_str(&filters_json).unwrap_or(SearchFilters {
                query: String::new(),
                tags: vec![],
                content_type: vec![],
                date_range: None,
            }),
            created_at: row.get(3)?,
        })
    })?;

    let mut result = Vec::new();
    for search in searches {
        result.push(search?);
    }

    Ok(result)
}

pub fn get_beacon_supported_formats() -> Result<Vec<super::SupportedFormat>> {
    Ok(vec![
        super::SupportedFormat {
            id: "pdf".to_string(),
            name: "PDF".to_string(),
            extensions: vec!["pdf".to_string()],
            mime_types: vec!["application/pdf".to_string()],
            category: super::FormatCategory::Document,
            processor: "pdf_processor".to_string(),
            is_enabled: true,
            max_file_size: Some(50 * 1024 * 1024),
        },
        super::SupportedFormat {
            id: "word".to_string(),
            name: "Word Document".to_string(),
            extensions: vec!["docx".to_string(), "doc".to_string()],
            mime_types: vec![
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document".to_string(),
                "application/msword".to_string(),
            ],
            category: super::FormatCategory::Document,
            processor: "word_processor".to_string(),
            is_enabled: true,
            max_file_size: Some(50 * 1024 * 1024),
        },
        super::SupportedFormat {
            id: "text".to_string(),
            name: "Text".to_string(),
            extensions: vec!["txt".to_string(), "md".to_string()],
            mime_types: vec!["text/plain".to_string()],
            category: super::FormatCategory::Document,
            processor: "text_processor".to_string(),
            is_enabled: true,
            max_file_size: Some(10 * 1024 * 1024),
        },
        super::SupportedFormat {
            id: "image".to_string(),
            name: "Image".to_string(),
            extensions: vec!["jpg".to_string(), "jpeg".to_string(), "png".to_string(), "gif".to_string(), "webp".to_string()],
            mime_types: vec![
                "image/jpeg".to_string(),
                "image/png".to_string(),
                "image/gif".to_string(),
                "image/webp".to_string(),
            ],
            category: super::FormatCategory::Image,
            processor: "image_processor".to_string(),
            is_enabled: true,
            max_file_size: Some(25 * 1024 * 1024),
        },
        super::SupportedFormat {
            id: "video".to_string(),
            name: "Video".to_string(),
            extensions: vec!["mp4".to_string(), "webm".to_string(), "ogg".to_string()],
            mime_types: vec![
                "video/mp4".to_string(),
                "video/webm".to_string(),
                "video/ogg".to_string(),
            ],
            category: super::FormatCategory::Video,
            processor: "video_processor".to_string(),
            is_enabled: true,
            max_file_size: Some(500 * 1024 * 1024),
        },
        super::SupportedFormat {
            id: "audio".to_string(),
            name: "Audio".to_string(),
            extensions: vec!["mp3".to_string(), "wav".to_string(), "ogg".to_string(), "weba".to_string()],
            mime_types: vec![
                "audio/mpeg".to_string(),
                "audio/wav".to_string(),
                "audio/ogg".to_string(),
                "audio/webm".to_string(),
            ],
            category: super::FormatCategory::Audio,
            processor: "audio_processor".to_string(),
            is_enabled: true,
            max_file_size: Some(100 * 1024 * 1024),
        },
    ])
}