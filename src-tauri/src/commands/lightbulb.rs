use anyhow::Result;
use serde::{Deserialize, Serialize};
use serde_json::{json, Value as JsonValue};
use std::collections::HashMap;
use chrono::Utc;

// Import the database module
mod lightbulb_db;
use lightbulb_db::*;

// Types for Lightbulb functionality
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Page {
    pub id: String,
    pub title: String,
    pub content: String,
    pub tags: Vec<String>,
    pub created_at: i64,
    pub updated_at: i64,
    pub parent_id: Option<String>,
    pub template_id: Option<String>,
    pub metadata: PageMetadata,
    pub settings: PageSettings,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct PageMetadata {
    pub word_count: usize,
    pub reading_time: usize,
    pub version: i32,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct PageSettings {
    pub is_public: bool,
    pub allow_comments: bool,
    pub enable_collaboration: bool,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct PageTemplate {
    pub id: String,
    pub name: String,
    pub description: String,
    pub default_content: String,
    pub default_tags: Vec<String>,
    pub icon: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct LightbulbProject {
    pub id: String,
    pub name: String,
    pub description: String,
    pub tags: Vec<String>,
    pub created_at: i64,
    pub updated_at: i64,
    pub status: String,
    pub priority: String,
    pub pages: Vec<String>,
    pub sources: Vec<String>,
    pub metadata: ProjectMetadata,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ProjectMetadata {
    pub page_count: usize,
    pub last_activity: i64,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ProjectTemplate {
    pub id: String,
    pub name: String,
    pub description: String,
    pub default_structure: Vec<String>,
    pub default_tags: Vec<String>,
    pub icon: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct SavedSearch {
    pub id: String,
    pub name: String,
    pub filters: SearchFilters,
    pub created_at: i64,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct SearchFilters {
    pub query: String,
    pub tags: Vec<String>,
    pub content_type: Vec<String>,
    pub date_range: Option<DateRange>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct DateRange {
    pub start: i64,
    pub end: i64,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct SupportedFormat {
    pub format: String,
    pub mime_types: Vec<String>,
    pub extensions: Vec<String>,
    pub max_size_mb: usize,
}

// Commands for Sources
#[tauri::command]
pub async fn get_all_sources() -> Result<Vec<JsonValue>, String> {
    match lightbulb_db::get_all_sources() {
        Ok(sources) => {
            let json_sources: Vec<JsonValue> = sources.into_iter().map(|source| {
                json!({
                    "id": source.id,
                    "name": source.name,
                    "type": source.source_type,
                    "content": source.content,
                    "url": source.url,
                    "file_path": source.file_path,
                    "metadata": source.metadata,
                    "uploaded_at": source.uploaded_at,
                    "processed_at": source.processed_at,
                    "status": source.status,
                })
            }).collect();
            Ok(json_sources)
        }
        Err(e) => Err(format!("Failed to get sources: {}", e)),
    }
}

// Commands for User Patterns
#[tauri::command]
pub async fn get_user_patterns() -> Result<HashMap<String, f32>, String> {
    match lightbulb_db::get_user_patterns() {
        Ok(patterns) => Ok(patterns),
        Err(e) => Err(format!("Failed to get user patterns: {}", e)),
    }
}

// Commands for Pages
#[tauri::command]
pub async fn get_all_pages() -> Result<JsonValue, String> {
    match lightbulb_db::get_all_pages() {
        Ok(pages) => {
            let json_pages: Vec<JsonValue> = pages.into_iter().map(|page| {
                json!({
                    "id": page.id,
                    "title": page.title,
                    "content": page.content,
                    "tags": page.tags,
                    "created_at": page.created_at,
                    "updated_at": page.updated_at,
                    "parent_id": page.parent_id,
                    "template_id": page.template_id,
                    "metadata": page.metadata,
                    "settings": page.settings,
                })
            }).collect();
            
            Ok(json!({
                "pages": json_pages
            }))
        }
        Err(e) => Err(format!("Failed to get pages: {}", e)),
    }
}

#[tauri::command]
pub async fn get_page_templates() -> Result<JsonValue, String> {
    match lightbulb_db::get_page_templates() {
        Ok(templates) => {
            let json_templates: Vec<JsonValue> = templates.into_iter().map(|template| {
                json!({
                    "id": template.id,
                    "name": template.name,
                    "description": template.description,
                    "default_content": template.content,
                    "default_tags": template.tags,
                    "icon": template.icon,
                    "category": template.category,
                    "preview_image": template.preview_image,
                    "created_at": template.created_at,
                    "updated_at": template.updated_at,
                })
            }).collect();
            
            Ok(json!({
                "templates": json_templates
            }))
        }
        Err(e) => Err(format!("Failed to get page templates: {}", e)),
    }
}

// Commands for Projects
#[tauri::command]
pub async fn get_all_projects() -> Result<Vec<LightbulbProject>, String> {
    match lightbulb_db::get_all_projects() {
        Ok(projects) => Ok(projects),
        Err(e) => Err(format!("Failed to get projects: {}", e)),
    }
}

#[tauri::command]
pub async fn get_project_templates() -> Result<Vec<ProjectTemplate>, String> {
    match lightbulb_db::get_project_templates() {
        Ok(templates) => Ok(templates),
        Err(e) => Err(format!("Failed to get project templates: {}", e)),
    }
}

// Commands for Saved Searches
#[tauri::command]
pub async fn get_saved_searches() -> Result<Vec<SavedSearch>, String> {
    match lightbulb_db::get_saved_searches() {
        Ok(searches) => Ok(searches),
        Err(e) => Err(format!("Failed to get saved searches: {}", e)),
    }
}

// Commands for Supported Formats
#[tauri::command]
pub async fn get_lightbulb_supported_formats() -> Result<Vec<SupportedFormat>, String> {
    match lightbulb_db::get_lightbulb_supported_formats() {
        Ok(formats) => Ok(formats),
        Err(e) => Err(format!("Failed to get supported formats: {}", e)),
    }
}

// Additional Commands for Page Management
#[tauri::command]
pub async fn create_page(title: String, content: String, tags: Option<Vec<String>>) -> Result<Page, String> {
    let now = Utc::now().timestamp();
    let page = lightbulb_db::Page {
        id: format!("page_{}", uuid::Uuid::new_v4()),
        title,
        content,
        tags: tags.unwrap_or_default(),
        parent_id: None,
        template_id: None,
        metadata: PageMetadata {
            word_count: 0,
            reading_time: 0,
            version: 1,
        },
        settings: PageSettings {
            is_public: false,
            allow_comments: true,
            enable_collaboration: false,
        },
        created_at: now,
        updated_at: now,
    };
    
    match lightbulb_db::create_page(page.clone()) {
        Ok(_) => Ok(page),
        Err(e) => Err(format!("Failed to create page: {}", e)),
    }
}

#[tauri::command]
pub async fn update_page(page: Page) -> Result<Page, String> {
    let updated_page = Page {
        updated_at: Utc::now().timestamp(),
        ..page
    };
    
    match lightbulb_db::update_page(updated_page.clone()) {
        Ok(_) => Ok(updated_page),
        Err(e) => Err(format!("Failed to update page: {}", e)),
    }
}

#[tauri::command]
pub async fn delete_page(page_id: String) -> Result<bool, String> {
    match lightbulb_db::delete_page(&page_id) {
        Ok(result) => Ok(result),
        Err(e) => Err(format!("Failed to delete page: {}", e)),
    }
}

#[tauri::command]
pub async fn get_page(page_id: String) -> Result<Option<Page>, String> {
    match lightbulb_db::get_page(&page_id) {
        Ok(page) => Ok(page),
        Err(e) => Err(format!("Failed to get page: {}", e)),
    }
}

#[tauri::command]
pub async fn create_page_template(template: PageTemplate) -> Result<PageTemplate, String> {
    let now = Utc::now().timestamp();
    let db_template = lightbulb_db::PageTemplate {
        id: template.id,
        name: template.name,
        description: template.description,
        content: template.default_content,
        category: "custom".to_string(),
        tags: template.default_tags,
        preview_image: None,
        created_at: now,
        updated_at: now,
    };
    
    match lightbulb_db::create_page_template(db_template) {
        Ok(_) => Ok(template),
        Err(e) => Err(format!("Failed to create page template: {}", e)),
    }
}