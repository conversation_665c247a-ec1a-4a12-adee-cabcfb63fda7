use anyhow::Result;
use serde::{Deserialize, Serialize};
use serde_json::{json, Value as JsonValue};
use std::collections::HashMap;
use chrono::{Utc, DateTime};
use uuid::Uuid;

// Import the new consolidated modules
use crate::models::*;
use crate::models::common::{Status, Timestamps, Metadata};
use crate::models::beacon::{PageLayout, PageSettings};
use crate::services::ServiceContainer;
use crate::database::Database;

// Import the legacy database module for backward compatibility
mod beacon_db;
use beacon_db as legacy_db;
use beacon_db::{ProjectMetadata, ProjectTemplate, PageMetadata};

// Legacy type aliases for backward compatibility
// These will be gradually replaced with the new consolidated models
pub type LegacyPage = Page;
pub type LegacyProject = Project;
pub type LegacySavedSearch = SavedSearch;
pub type LegacySupportedFormat = SupportedFormat;

// Helper function to get or create service container
async fn get_service_container() -> Result<ServiceContainer, String> {
    ServiceContainer::new().await.map_err(|e| format!("Failed to initialize services: {}", e))
}

// Helper function to get database connection
fn get_database() -> Result<Database, String> {
    Database::new().map_err(|e| format!("Failed to connect to database: {}", e))
}

// Legacy project type for backward compatibility
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct BeaconProject {
    pub id: String,
    pub name: String,
    pub description: String,
    pub tags: Vec<String>,
    pub created_at: i64,
    pub updated_at: i64,
    pub status: String,
    pub priority: String,
    pub pages: Vec<String>,
    pub sources: Vec<String>,
    pub metadata: LegacyProjectMetadata,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct LegacyProjectMetadata {
    pub page_count: usize,
    pub last_activity: i64,
}

// Convert from new Project model to legacy BeaconProject
impl From<Project> for BeaconProject {
    fn from(project: Project) -> Self {
        Self {
            id: project.id,
            name: project.name,
            description: project.description.unwrap_or_default(),
            tags: Vec::new(), // Projects don't have tags in the new model
            created_at: project.timestamps.created_at.timestamp(),
            updated_at: project.timestamps.updated_at.timestamp(),
            status: format!("{:?}", project.status),
            priority: format!("{:?}", project.priority),
            pages: Vec::new(), // Will be populated separately
            sources: Vec::new(), // Will be populated separately
            metadata: LegacyProjectMetadata {
                page_count: 0, // Will be calculated
                last_activity: project.timestamps.updated_at.timestamp(),
            },
        }
    }
}

// Commands for Sources
#[tauri::command]
pub async fn get_all_sources() -> Result<Vec<JsonValue>, String> {
    match beacon_db::get_all_sources() {
        Ok(sources) => {
            let json_sources: Vec<JsonValue> = sources.into_iter().map(|source| {
                json!({
                    "id": source.id,
                    "name": source.name,
                    "type": source.source_type,
                    "content": source.content,
                    "url": source.url,
                    "file_path": source.file_path,
                    "metadata": source.metadata,
                    "uploaded_at": source.uploaded_at,
                    "processed_at": source.processed_at,
                    "status": source.status,
                })
            }).collect();
            Ok(json_sources)
        }
        Err(e) => Err(format!("Failed to get sources: {}", e)),
    }
}

// Commands for User Patterns
#[tauri::command]
pub async fn get_user_patterns() -> Result<HashMap<String, f32>, String> {
    match beacon_db::get_user_patterns() {
        Ok(patterns) => Ok(patterns),
        Err(e) => Err(format!("Failed to get user patterns: {}", e)),
    }
}

// Commands for Pages
#[tauri::command]
pub async fn get_all_pages() -> Result<JsonValue, String> {
    match beacon_db::get_all_pages() {
        Ok(pages) => {
            let json_pages: Vec<JsonValue> = pages.into_iter().map(|page| {
                json!({
                    "id": page.id,
                    "title": page.title,
                    "content": page.content,
                    "tags": page.tags,
                    "created_at": page.created_at,
                    "updated_at": page.updated_at,
                    "parent_id": page.parent_id,
                    "template_id": page.template_id,
                    "metadata": page.metadata,
                    "settings": page.settings,
                })
            }).collect();
            
            Ok(json!({
                "pages": json_pages
            }))
        }
        Err(e) => Err(format!("Failed to get pages: {}", e)),
    }
}

#[tauri::command]
pub async fn get_page_templates() -> Result<JsonValue, String> {
    match beacon_db::get_page_templates() {
        Ok(templates) => {
            let json_templates: Vec<JsonValue> = templates.into_iter().map(|template| {
                json!({
                    "id": template.id,
                    "name": template.name,
                    "description": template.description,
                    "default_content": template.content,
                    "default_tags": template.tags,
                    // "icon": template.icon, // Field doesn't exist in beacon_db::PageTemplate
                    "category": template.category,
                    "preview_image": template.preview_image,
                    "created_at": template.created_at,
                    "updated_at": template.updated_at,
                })
            }).collect();
            
            Ok(json!({
                "templates": json_templates
            }))
        }
        Err(e) => Err(format!("Failed to get page templates: {}", e)),
    }
}

// Commands for Projects
#[tauri::command]
pub async fn get_all_projects() -> Result<Vec<BeaconProject>, String> {
    match beacon_db::get_all_projects() {
        Ok(projects) => {
            // Convert from beacon_db::Project to BeaconProject
            let converted: Vec<BeaconProject> = projects.into_iter().map(|p| {
                BeaconProject {
                    id: p.id,
                    name: p.name,
                    description: p.description,
                    tags: p.tags,
                    created_at: p.created_at,
                    updated_at: p.updated_at,
                    status: p.status,
                    priority: p.priority,
                    pages: Vec::new(), // These fields need to be populated separately
                    sources: Vec::new(),
                    metadata: LegacyProjectMetadata {
                        page_count: 0,
                        last_activity: Utc::now().timestamp(),
                    },
                }
            }).collect();
            Ok(converted)
        },
        Err(e) => Err(format!("Failed to get projects: {}", e)),
    }
}

#[tauri::command]
pub async fn get_project_templates() -> Result<Vec<ProjectTemplate>, String> {
    match beacon_db::get_project_templates() {
        Ok(templates) => {
            // Convert from beacon_db::ProjectTemplate to lightbulb::ProjectTemplate
            let converted: Vec<ProjectTemplate> = templates.into_iter().map(|t| {
                ProjectTemplate {
                    id: t.id,
                    name: t.name,
                    description: t.description,
                    default_structure: t.default_structure,
                    default_tags: t.default_tags,
                    icon: t.icon,
                    created_at: t.created_at,
                }
            }).collect();
            Ok(converted)
        },
        Err(e) => Err(format!("Failed to get project templates: {}", e)),
    }
}

// Commands for Saved Searches
#[tauri::command]
pub async fn get_saved_searches() -> Result<Vec<SavedSearch>, String> {
    match beacon_db::get_saved_searches() {
        Ok(searches) => {
            // Convert from beacon_db::SavedSearch to lightbulb::SavedSearch
            let converted: Vec<SavedSearch> = searches.into_iter().map(|s| {
                SavedSearch {
                    id: s.id,
                    name: s.name,
                    query: s.filters.query,  // Extract query from filters
                    filters: SearchFilters {
                        source_types: vec![],  // Convert from content_type if needed
                        tags: s.filters.tags,
                        date_range: s.filters.date_range.map(|dr| DateRange {
                            start: DateTime::from_timestamp(dr.start, 0).unwrap_or_else(|| Utc::now()),
                            end: DateTime::from_timestamp(dr.end, 0).unwrap_or_else(|| Utc::now()),
                        }),
                        projects: vec![],
                        status: None,
                    },
                    user_id: String::new(),  // Default user ID
                    is_public: false,
                    usage_count: 0,
                    timestamps: Timestamps {
                        created_at: DateTime::from_timestamp(s.created_at, 0).unwrap_or_else(|| Utc::now()),
                        updated_at: DateTime::from_timestamp(s.created_at, 0).unwrap_or_else(|| Utc::now()),
                    },
                }
            }).collect();
            Ok(converted)
        },
        Err(e) => Err(format!("Failed to get saved searches: {}", e)),
    }
}

// Commands for Supported Formats
#[tauri::command]
pub async fn get_beacon_supported_formats() -> Result<Vec<SupportedFormat>, String> {
    match beacon_db::get_beacon_supported_formats() {
        Ok(formats) => Ok(formats),
        Err(e) => Err(format!("Failed to get supported formats: {}", e)),
    }
}

// Additional Commands for Page Management
#[tauri::command]
pub async fn create_page(title: String, content: String, tags: Option<Vec<String>>) -> Result<Page, String> {
    let now = Utc::now().timestamp();
    let page = beacon_db::Page {
        id: format!("page_{}", uuid::Uuid::new_v4()),
        title,
        content,
        tags: tags.unwrap_or_default(),
        parent_id: None,
        template_id: None,
        metadata: beacon_db::PageMetadata {
            word_count: 0,
            reading_time: 0,
            version: 1,
        },
        settings: beacon_db::PageSettings {
            is_public: false,
            allow_comments: true,
            enable_collaboration: false,
        },
        created_at: now,
        updated_at: now,
    };
    
    match beacon_db::create_page(page.clone()) {
        Ok(_) => {
            // Convert from beacon_db::Page to lightbulb::Page
            Ok(Page {
                id: page.id,
                title: page.title,
                content: page.content,
                tags: page.tags,
                metadata: Metadata::new(),
                settings: PageSettings {
                    is_public: page.settings.is_public,
                    allow_comments: page.settings.allow_comments,
                    auto_save: true,
                    theme: None,
                    layout: PageLayout::Standard,
                    custom_css: None,
                },
                template_id: page.template_id,
                project_id: None,
                status: Status::Active,
                timestamps: Timestamps {
                    created_at: DateTime::from_timestamp(page.created_at, 0).unwrap_or_else(|| Utc::now()),
                    updated_at: DateTime::from_timestamp(page.updated_at, 0).unwrap_or_else(|| Utc::now()),
                },
            })
        },
        Err(e) => Err(format!("Failed to create page: {}", e)),
    }
}

#[tauri::command]
pub async fn update_page(page: Page) -> Result<Page, String> {
    // Convert to beacon_db::Page for database operation
    let db_page = beacon_db::Page {
        id: page.id.clone(),
        title: page.title.clone(),
        content: page.content.clone(),
        tags: page.tags.clone(),
        parent_id: None,  // models::Page doesn't have parent_id
        template_id: page.template_id.clone(),
        metadata: beacon_db::PageMetadata {
            word_count: 0,  // models::Metadata doesn't have word_count
            reading_time: 0,  // models::Metadata doesn't have reading_time
            version: 1,  // models::Metadata doesn't have version
        },
        settings: beacon_db::PageSettings {
            is_public: page.settings.is_public,
            allow_comments: page.settings.allow_comments,
            enable_collaboration: false,  // models::PageSettings doesn't have enable_collaboration
        },
        created_at: page.timestamps.created_at.timestamp(),
        updated_at: Utc::now().timestamp(),
    };
    
    match beacon_db::update_page(db_page) {
        Ok(_) => Ok(Page {
            timestamps: Timestamps {
                created_at: page.timestamps.created_at,
                updated_at: Utc::now(),
            },
            ..page
        }),
        Err(e) => Err(format!("Failed to update page: {}", e)),
    }
}

#[tauri::command]
pub async fn delete_page(page_id: String) -> Result<bool, String> {
    match beacon_db::delete_page(&page_id) {
        Ok(result) => Ok(result),
        Err(e) => Err(format!("Failed to delete page: {}", e)),
    }
}

#[tauri::command]
pub async fn get_page(page_id: String) -> Result<Option<Page>, String> {
    match beacon_db::get_page(&page_id) {
        Ok(Some(page)) => {
            // Convert from beacon_db::Page to lightbulb::Page
            Ok(Some(Page {
                id: page.id,
                title: page.title,
                content: page.content,
                tags: page.tags,
                metadata: Metadata::new(),
                settings: PageSettings {
                    is_public: page.settings.is_public,
                    allow_comments: page.settings.allow_comments,
                    auto_save: true,
                    theme: None,
                    layout: PageLayout::Standard,
                    custom_css: None,
                },
                template_id: page.template_id,
                project_id: None,
                status: Status::Active,
                timestamps: Timestamps {
                    created_at: DateTime::from_timestamp(page.created_at, 0).unwrap_or_else(|| Utc::now()),
                    updated_at: DateTime::from_timestamp(page.updated_at, 0).unwrap_or_else(|| Utc::now()),
                },
            }))
        },
        Ok(None) => Ok(None),
        Err(e) => Err(format!("Failed to get page: {}", e)),
    }
}

#[tauri::command]
pub async fn create_project(name: String, description: String, template_id: Option<String>) -> Result<BeaconProject, String> {
    let now = Utc::now().timestamp();
    let project_id = format!("project_{}", uuid::Uuid::new_v4());
    
    // If template_id is provided, get the template
    let (tags, status, priority) = if let Some(tid) = template_id {
        match beacon_db::get_project_template(&tid) {
            Ok(Some(template)) => {
                (template.default_tags, "active".to_string(), "medium".to_string())
            },
            _ => (Vec::new(), "active".to_string(), "medium".to_string())
        }
    } else {
        (Vec::new(), "active".to_string(), "medium".to_string())
    };
    
    let project = beacon_db::Project {
        id: project_id.clone(),
        name,
        description,
        tags,
        color: "#3B82F6".to_string(), // Default blue color
        icon: "folder".to_string(), // Default icon
        created_at: now,
        updated_at: now,
        status,
        priority,
        metadata: beacon_db::ProjectMetadata {
            page_count: 0,
            last_activity: now,
        },
    };
    
    match beacon_db::create_project(project.clone()) {
        Ok(_) => {
            Ok(BeaconProject {
                id: project.id,
                name: project.name,
                description: project.description,
                tags: project.tags,
                created_at: project.created_at,
                updated_at: project.updated_at,
                status: project.status,
                priority: project.priority,
                pages: Vec::new(),
                sources: Vec::new(),
                metadata: LegacyProjectMetadata {
                    page_count: 0,
                    last_activity: now,
                },
            })
        },
        Err(e) => Err(format!("Failed to create project: {}", e)),
    }
}

#[tauri::command]
pub async fn create_project_from_template(template_id: String, name: String, description: Option<String>) -> Result<BeaconProject, String> {
    // Get the template
    let template = match beacon_db::get_project_template(&template_id) {
        Ok(Some(t)) => t,
        Ok(None) => return Err(format!("Template not found: {}", template_id)),
        Err(e) => return Err(format!("Failed to get template: {}", e)),
    };
    
    let now = Utc::now().timestamp();
    let project_id = format!("project_{}", uuid::Uuid::new_v4());
    
    let project = beacon_db::Project {
        id: project_id.clone(),
        name,
        description: description.unwrap_or(template.description),
        tags: template.default_tags,
        color: "#3B82F6".to_string(), // Default blue color
        icon: template.icon.clone(), // Use template icon
        created_at: now,
        updated_at: now,
        status: "active".to_string(),
        priority: "medium".to_string(),
        metadata: beacon_db::ProjectMetadata {
            page_count: 0,
            last_activity: now,
        },
    };
    
    match beacon_db::create_project(project.clone()) {
        Ok(_) => {
            // Create default pages from template structure
            if let Ok(structure) = serde_json::from_str::<Vec<String>>(&template.default_structure) {
                for page_title in structure {
                    let page = beacon_db::Page {
                        id: format!("page_{}", uuid::Uuid::new_v4()),
                        title: page_title,
                        content: String::new(),
                        tags: Vec::new(),
                        parent_id: Some(project_id.clone()),
                        template_id: Some(template_id.clone()),
                        metadata: beacon_db::PageMetadata {
                            word_count: 0,
                            reading_time: 0,
                            version: 1,
                        },
                        settings: beacon_db::PageSettings {
                            is_public: false,
                            allow_comments: true,
                            enable_collaboration: false,
                        },
                        created_at: now,
                        updated_at: now,
                    };
                    
                    let _ = beacon_db::create_page(page);
                }
            }
            
            Ok(BeaconProject {
                id: project.id,
                name: project.name,
                description: project.description,
                tags: project.tags,
                created_at: project.created_at,
                updated_at: project.updated_at,
                status: project.status,
                priority: project.priority,
                pages: Vec::new(),
                sources: Vec::new(),
                metadata: LegacyProjectMetadata {
                    page_count: 0,
                    last_activity: now,
                },
            })
        },
        Err(e) => Err(format!("Failed to create project from template: {}", e)),
    }
}

#[tauri::command]
pub async fn update_project(project: BeaconProject) -> Result<BeaconProject, String> {
    let db_project = beacon_db::Project {
        id: project.id.clone(),
        name: project.name.clone(),
        description: project.description.clone(),
        tags: project.tags.clone(),
        color: "#3B82F6".to_string(), // Keep default color for now
        icon: "folder".to_string(), // Keep default icon for now
        created_at: project.created_at,
        updated_at: Utc::now().timestamp(),
        status: project.status.clone(),
        priority: project.priority.clone(),
        metadata: beacon_db::ProjectMetadata {
            page_count: project.metadata.page_count,
            last_activity: Utc::now().timestamp(),
        },
    };
    
    match beacon_db::update_project(db_project) {
        Ok(_) => {
            Ok(BeaconProject {
                updated_at: Utc::now().timestamp(),
                metadata: LegacyProjectMetadata {
                    page_count: project.metadata.page_count,
                    last_activity: Utc::now().timestamp(),
                },
                ..project
            })
        },
        Err(e) => Err(format!("Failed to update project: {}", e)),
    }
}

#[tauri::command]
pub async fn delete_project(project_id: String) -> Result<bool, String> {
    match beacon_db::delete_project(&project_id) {
        Ok(result) => Ok(result),
        Err(e) => Err(format!("Failed to delete project: {}", e)),
    }
}

#[tauri::command]
pub async fn get_project(project_id: String) -> Result<Option<BeaconProject>, String> {
    match beacon_db::get_project(&project_id) {
        Ok(Some(project)) => {
            Ok(Some(BeaconProject {
                id: project.id,
                name: project.name,
                description: project.description,
                tags: project.tags,
                created_at: project.created_at,
                updated_at: project.updated_at,
                status: project.status,
                priority: project.priority,
                pages: Vec::new(),
                sources: Vec::new(),
                metadata: LegacyProjectMetadata {
                    page_count: 0,
                    last_activity: project.updated_at,
                },
            }))
        },
        Ok(None) => Ok(None),
        Err(e) => Err(format!("Failed to get project: {}", e)),
    }
}

#[tauri::command]
pub async fn create_page_template(template: PageTemplate) -> Result<PageTemplate, String> {
    let now = Utc::now().timestamp();
    let db_template = beacon_db::PageTemplate {
        id: template.id.clone(),
        name: template.name.clone(),
        description: template.description.clone().unwrap_or_default(),
        content: template.content_template.clone(),
        category: "custom".to_string(),
        tags: Vec::new(),
        preview_image: None,
        created_at: now,
        updated_at: now,
    };
    
    match beacon_db::create_page_template(db_template) {
        Ok(_) => Ok(template),
        Err(e) => Err(format!("Failed to create page template: {}", e)),
    }
}