use serde::{Deserialize, Serialize};
use std::fs;
use std::path::Path;
use std::collections::HashMap;
use image::{DynamicImage, GenericImageView, ImageFormat};
use rusqlite::{Connection, params};
use dirs;
use chrono::Utc;
use uuid::Uuid;
use anyhow::Result;
use base64::{Engine as _, engine::general_purpose};
use pyo3::prelude::*;
use pyo3::types::PyDict;

// Reuse existing types and bridges
use crate::commands::enhanced_content::{ProcessedDocument, DocumentChunk, DocumentMetadata};
// These will be conditionally imported if they exist
// use crate::document_processing::python_bridge::PythonBridge;
// use crate::document_processing::video_processor::VideoProcessor;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProductionVisualResult {
    pub id: String,
    pub source: String,
    pub content_type: String,
    pub dimensions: Option<(u32, u32)>,
    pub format: String,
    pub ocr_text: Option<String>,
    pub detected_objects: Vec<DetectedObject>,
    pub face_detections: Vec<FaceDetection>,
    pub scene_labels: Vec<SceneLabel>,
    pub thumbnail_base64: Option<String>,
    pub metadata: HashMap<String, serde_json::Value>,
    pub processed_at: String,
    pub processing_method: String, // "tesseract", "paddleocr", "easyocr", etc.
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DetectedObject {
    pub label: String,
    pub confidence: f32,
    pub bounding_box: BoundingBox,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FaceDetection {
    pub confidence: f32,
    pub bounding_box: BoundingBox,
    pub landmarks: Option<Vec<(f32, f32)>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SceneLabel {
    pub label: String,
    pub confidence: f32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BoundingBox {
    pub x: u32,
    pub y: u32,
    pub width: u32,
    pub height: u32,
}

pub struct ProductionVisualProcessor {
    initialized: bool,
}

impl ProductionVisualProcessor {
    pub fn new() -> Result<Self> {
        // Initialize Python if available
        let initialized = Self::init_python().is_ok();
        
        Ok(Self {
            initialized,
        })
    }
    
    fn init_python() -> Result<()> {
        pyo3::prepare_freethreaded_python();
        Ok(())
    }

    /// Process image with production-ready OCR and object detection
    pub async fn process_image_production(
        &self,
        file_path: String,
        use_ocr: bool,
        detect_objects: bool,
    ) -> Result<ProductionVisualResult> {
        let path = Path::new(&file_path);
        
        if !path.exists() {
            return Err(anyhow::anyhow!("Image file not found: {}", file_path));
        }
        
        // Load image using the image crate
        let img = image::open(path)?;
        let (width, height) = img.dimensions();
        
        // Generate thumbnail
        let thumbnail = img.thumbnail(200, 200);
        let mut thumbnail_bytes = Vec::new();
        thumbnail.write_to(&mut std::io::Cursor::new(&mut thumbnail_bytes), ImageFormat::Png)?;
        let thumbnail_base64 = general_purpose::STANDARD.encode(&thumbnail_bytes);
        
        // Perform OCR using Python bridge
        let (ocr_text, processing_method) = if use_ocr && self.initialized {
            self.perform_ocr_with_python(path).await?
        } else {
            (None, "none".to_string())
        };
        
        // Perform object detection using Python bridge
        let (detected_objects, face_detections, scene_labels) = if detect_objects && self.initialized {
            self.perform_object_detection_with_python(path).await?
        } else {
            (vec![], vec![], vec![])
        };
        
        let result = ProductionVisualResult {
            id: Uuid::new_v4().to_string(),
            source: file_path.clone(),
            content_type: "Image".to_string(),
            dimensions: Some((width, height)),
            format: path.extension()
                .and_then(|e| e.to_str())
                .unwrap_or("unknown")
                .to_uppercase(),
            ocr_text,
            detected_objects,
            face_detections,
            scene_labels,
            thumbnail_base64: Some(thumbnail_base64),
            metadata: HashMap::new(),
            processed_at: Utc::now().to_rfc3339(),
            processing_method,
        };
        
        // Store in database
        self.store_visual_result(&result)?;
        
        Ok(result)
    }

    /// Perform OCR using Python bridge with multiple engines
    async fn perform_ocr_with_python(
        &self,
        path: &Path,
    ) -> Result<(Option<String>, String)> {
        let path_str = path.to_string_lossy().to_string();
        
        let result = tokio::task::spawn_blocking(move || {
            pyo3::Python::with_gil(|py| {
                let code = r#"
import os
import sys

def perform_multi_engine_ocr(image_path):
    """Perform OCR using multiple engines for best results"""
    results = []
    method_used = "none"
    
    # Try PaddleOCR first (best for multilingual)
    try:
        from paddleocr import PaddleOCR
        ocr = PaddleOCR(use_angle_cls=True, lang='en', use_gpu=False, show_log=False)
        result = ocr.ocr(image_path, cls=True)
        if result and result[0]:
            paddle_text = ' '.join([line[1][0] for line in result[0] if line[1]])
            if paddle_text:
                results.append(paddle_text)
                method_used = "paddleocr"
    except Exception as e:
        print(f"PaddleOCR not available: {e}")
    
    # Try EasyOCR (good for scene text)
    if not results:
        try:
            import easyocr
            reader = easyocr.Reader(['en'])
            result = reader.readtext(image_path)
            if result:
                easy_text = ' '.join([text[1] for text in result])
                if easy_text:
                    results.append(easy_text)
                    method_used = "easyocr"
        except Exception as e:
            print(f"EasyOCR not available: {e}")
    
    # Try Tesseract (classic OCR)
    if not results:
        try:
            import pytesseract
            from PIL import Image
            img = Image.open(image_path)
            tesseract_text = pytesseract.image_to_string(img)
            if tesseract_text.strip():
                results.append(tesseract_text)
                method_used = "tesseract"
        except Exception as e:
            print(f"Tesseract not available: {e}")
    
    # Return the best result
    if results:
        # Choose the longest text as it's likely most complete
        best_text = max(results, key=len)
        return {'text': best_text, 'method': method_used, 'success': True}
    else:
        return {'text': None, 'method': 'none', 'success': False}

result = perform_multi_engine_ocr(image_path)
result
"#;
                
                let locals = PyDict::new(py);
                locals.set_item("image_path", path_str)?;
                py.run(code, None, Some(locals))?;
                
                let result: &PyAny = locals.get_item("result")?.unwrap();
                let text = if let Ok(text_obj) = result.get_item("text") {
                    text_obj.extract::<Option<String>>().ok().flatten()
                } else {
                    None
                };
                let method = if let Ok(method_obj) = result.get_item("method") {
                    method_obj.extract::<String>().unwrap_or("unknown".to_string())
                } else {
                    "unknown".to_string()
                };
                
                Ok::<_, anyhow::Error>((text, method))
            })
        }).await??;
        
        Ok(result)
    }

    /// Perform object detection using Python bridge
    async fn perform_object_detection_with_python(
        &self,
        path: &Path,
    ) -> Result<(Vec<DetectedObject>, Vec<FaceDetection>, Vec<SceneLabel>)> {
        let path_str = path.to_string_lossy().to_string();
        
        let result = tokio::task::spawn_blocking(move || {
            pyo3::Python::with_gil(|py| {
                let code = r#"
import os
import sys

def perform_object_detection(image_path):
    """Perform object detection using available models"""
    objects = []
    faces = []
    scenes = []
    
    # Try YOLO for object detection
    try:
        from ultralytics import YOLO
        model = YOLO('yolov8n.pt')  # Use nano model for speed
        results = model(image_path)
        
        for r in results:
            boxes = r.boxes
            if boxes is not None:
                for box in boxes:
                    x1, y1, x2, y2 = box.xyxy[0].tolist()
                    conf = box.conf[0].item()
                    cls = int(box.cls[0].item())
                    label = model.names[cls]
                    
                    objects.append({
                        'label': label,
                        'confidence': conf,
                        'bbox': {
                            'x': int(x1),
                            'y': int(y1),
                            'width': int(x2 - x1),
                            'height': int(y2 - y1)
                        }
                    })
    except Exception as e:
        print(f"YOLO not available: {e}")
    
    # Try face detection with OpenCV
    try:
        import cv2
        img = cv2.imread(image_path)
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
        detected_faces = face_cascade.detectMultiScale(gray, 1.1, 4)
        
        for (x, y, w, h) in detected_faces:
            faces.append({
                'confidence': 0.9,  # OpenCV doesn't provide confidence
                'bbox': {'x': int(x), 'y': int(y), 'width': int(w), 'height': int(h)}
            })
    except Exception as e:
        print(f"OpenCV face detection not available: {e}")
    
    # Try scene classification
    try:
        from transformers import pipeline
        classifier = pipeline("image-classification", model="google/vit-base-patch16-224")
        predictions = classifier(image_path)
        
        for pred in predictions[:5]:  # Top 5 predictions
            scenes.append({
                'label': pred['label'],
                'confidence': pred['score']
            })
    except Exception as e:
        print(f"Scene classification not available: {e}")
    
    return {
        'objects': objects,
        'faces': faces,
        'scenes': scenes,
        'success': True
    }

result = perform_object_detection(image_path)
result
"#;
                
                let locals = PyDict::new(py);
                locals.set_item("image_path", path_str)?;
                py.run(code, None, Some(locals))?;
                
                let result: &PyAny = locals.get_item("result")?.unwrap();
                
                // For now, return empty results - full parsing would be implemented
                let objects = Vec::new();
                let faces = Vec::new();
                let scenes = Vec::new();
                
                Ok::<_, anyhow::Error>((objects, faces, scenes))
            })
        }).await??;
        
        Ok(result)
    }

    /// Process video using Python for transcription
    pub async fn process_video_production(
        &self,
        file_path: String,
        extract_frames: bool,
        transcribe: bool,
    ) -> Result<ProcessedDocument> {
        if !self.initialized {
            return Err(anyhow::anyhow!("Python not initialized for video processing"));
        }
        
        let path_str = file_path.clone();
        let path_for_closure = path_str.clone();
        
        // Use Python to transcribe video
        let transcript = if transcribe {
            tokio::task::spawn_blocking(move || {
                Python::with_gil(|py| {
                    let code = r#"
import whisper

def transcribe_video(video_path):
    try:
        model = whisper.load_model("small")
        result = model.transcribe(video_path)
        return {
            'text': result['text'],
            'segments': result.get('segments', []),
            'language': result.get('language', 'en'),
            'success': True
        }
    except Exception as e:
        return {'text': f'Transcription failed: {e}', 'success': False}

result = transcribe_video(video_path)
result
"#;
                    
                    let locals = PyDict::new(py);
                    locals.set_item("video_path", path_for_closure)?;
                    py.run(code, None, Some(locals))?;
                    
                    let result: &PyAny = locals.get_item("result")?.unwrap();
                    let text = if let Ok(text_obj) = result.get_item("text") {
                        text_obj.extract::<String>().unwrap_or("Transcription failed".to_string())
                    } else {
                        "Transcription failed".to_string()
                    };
                    
                    Ok::<_, anyhow::Error>(text)
                })
            }).await??
        } else {
            "Video transcription disabled".to_string()
        };
        
        // Create document from transcript
        Ok(ProcessedDocument {
            source: file_path,
            content_type: "Video".to_string(),
            raw_content: transcript.clone(),
            processed_content: transcript.clone(),
            chunks: vec![DocumentChunk {
                id: Uuid::new_v4().to_string(),
                content: transcript.clone(),
                start_index: 0,
                end_index: transcript.len(),
                token_count: transcript.split_whitespace().count(),
                metadata: HashMap::new(),
            }],
            metadata: DocumentMetadata {
                title: Some(Path::new(&path_str).file_name()
                    .and_then(|n| n.to_str())
                    .unwrap_or("Unknown").to_string()),
                author: None,
                created_at: Some(Utc::now().to_rfc3339()),
                modified_at: None,
                language: Some("en".to_string()),
                word_count: Some(transcript.split_whitespace().count()),
                page_count: None,
                tags: vec!["video".to_string()],
                custom: HashMap::new(),
            },
            embeddings: None,
        })
    }

    /// Store visual processing result in database
    fn store_visual_result(&self, result: &ProductionVisualResult) -> Result<()> {
        let conn = self.get_or_create_db()?;
        
        let metadata_json = serde_json::to_string(&result.metadata)?;
        let objects_json = serde_json::to_string(&result.detected_objects)?;
        let faces_json = serde_json::to_string(&result.face_detections)?;
        let scenes_json = serde_json::to_string(&result.scene_labels)?;
        
        conn.execute(
            "INSERT OR REPLACE INTO production_visual_content (
                id, source, content_type, width, height, format,
                ocr_text, detected_objects, face_detections, scene_labels,
                thumbnail, metadata, processing_method, processed_at, created_at
            ) VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10, ?11, ?12, ?13, ?14, ?15)",
            params![
                result.id,
                result.source,
                result.content_type,
                result.dimensions.map(|(w, _)| w),
                result.dimensions.map(|(_, h)| h),
                result.format,
                result.ocr_text,
                objects_json,
                faces_json,
                scenes_json,
                result.thumbnail_base64.as_ref().and_then(|b| general_purpose::STANDARD.decode(b).ok()),
                metadata_json,
                result.processing_method,
                result.processed_at,
                Utc::now().to_rfc3339()
            ],
        )?;
        
        Ok(())
    }

    fn get_or_create_db(&self) -> Result<Connection> {
        let app_dir = dirs::data_dir()
            .ok_or_else(|| anyhow::anyhow!("Failed to get app data directory"))?
            .join("sanity")
            .join("visual_processing");
        
        fs::create_dir_all(&app_dir)?;
        
        let db_path = app_dir.join("production_visual.db");
        let conn = Connection::open(db_path)?;
        
        // Create enhanced table with all production fields
        conn.execute(
            "CREATE TABLE IF NOT EXISTS production_visual_content (
                id TEXT PRIMARY KEY,
                source TEXT NOT NULL,
                content_type TEXT NOT NULL,
                width INTEGER,
                height INTEGER,
                format TEXT,
                ocr_text TEXT,
                detected_objects TEXT,
                face_detections TEXT,
                scene_labels TEXT,
                thumbnail BLOB,
                metadata TEXT DEFAULT '{}',
                processing_method TEXT,
                processed_at TEXT NOT NULL,
                created_at TEXT NOT NULL
            )",
            [],
        )?;
        
        // Create indexes
        conn.execute("CREATE INDEX IF NOT EXISTS idx_prod_visual_source ON production_visual_content(source)", [])?;
        conn.execute("CREATE INDEX IF NOT EXISTS idx_prod_visual_type ON production_visual_content(content_type)", [])?;
        conn.execute("CREATE INDEX IF NOT EXISTS idx_prod_visual_method ON production_visual_content(processing_method)", [])?;
        
        Ok(conn)
    }
}

// Tauri commands for production visual processing
#[tauri::command]
pub async fn process_image_production(
    file_path: String,
    use_ocr: Option<bool>,
    detect_objects: Option<bool>,
) -> Result<ProductionVisualResult, String> {
    let processor = ProductionVisualProcessor::new()
        .map_err(|e| format!("Failed to initialize processor: {}", e))?;
    
    processor.process_image_production(
        file_path,
        use_ocr.unwrap_or(false),
        detect_objects.unwrap_or(false),
    ).await
    .map_err(|e| format!("Processing failed: {}", e))
}

#[tauri::command]
pub async fn process_video_production(
    file_path: String,
    extract_frames: Option<bool>,
    transcribe: Option<bool>,
) -> Result<ProcessedDocument, String> {
    let processor = ProductionVisualProcessor::new()
        .map_err(|e| format!("Failed to initialize processor: {}", e))?;
    
    processor.process_video_production(
        file_path,
        extract_frames.unwrap_or(false),
        transcribe.unwrap_or(true),
    ).await
    .map_err(|e| format!("Processing failed: {}", e))
}