/// Common utilities for command modules
use tokio::process::{Command, Child};
use std::process::Stdio;

/// Build a Claude command with arguments and optional project path
pub fn build_claude_command(
    claude_path: &str,
    args: Vec<String>,
    project_path: Option<&str>,
) -> Command {
    let mut cmd = crate::claude_binary::create_tokio_command_with_env(claude_path);
    
    // Add all arguments
    for arg in args {
        cmd.arg(arg);
    }
    
    // Set working directory if provided
    if let Some(path) = project_path {
        cmd.current_dir(path);
    }
    
    // Set up stdio
    cmd.stdout(Stdio::piped())
        .stderr(Stdio::piped());
    
    cmd
}

/// Cross-platform process termination
pub fn kill_process_cross_platform(pid: u32) -> Result<(), String> {
    let result = if cfg!(target_os = "windows") {
        std::process::Command::new("taskkill")
            .args(["/F", "/PID", &pid.to_string()])
            .output()
    } else {
        std::process::Command::new("kill")
            .args(["-KILL", &pid.to_string()])
            .output()
    };
    
    match result {
        Ok(output) => {
            if output.status.success() {
                Ok(())
            } else {
                let stderr = String::from_utf8_lossy(&output.stderr);
                Err(format!("Failed to kill process: {}", stderr))
            }
        }
        Err(e) => Err(format!("Failed to execute kill command: {}", e)),
    }
}

/// Terminate a tokio child process gracefully
pub async fn terminate_child_process(mut child: Child) -> Result<(), String> {
    // Try graceful shutdown first
    if let Some(id) = child.id() {
        // Try to kill using the process ID
        if let Err(e) = kill_process_cross_platform(id) {
            log::warn!("Failed to kill process {}: {}", id, e);
        }
    }
    
    // Force kill if still running
    match child.kill().await {
        Ok(_) => Ok(()),
        Err(e) => {
            // Process might already be dead, which is fine
            if e.kind() != std::io::ErrorKind::InvalidInput {
                Err(format!("Failed to kill child process: {}", e))
            } else {
                Ok(())
            }
        }
    }
}

/// Check if a process is still running
pub fn is_process_running(pid: u32) -> bool {
    if cfg!(target_os = "windows") {
        // On Windows, use tasklist to check if process exists
        std::process::Command::new("tasklist")
            .args(["/FI", &format!("PID eq {}", pid)])
            .output()
            .map(|output| {
                let stdout = String::from_utf8_lossy(&output.stdout);
                stdout.contains(&pid.to_string())
            })
            .unwrap_or(false)
    } else {
        // On Unix-like systems, use kill -0 to check if process exists
        std::process::Command::new("kill")
            .args(["-0", &pid.to_string()])
            .output()
            .map(|output| output.status.success())
            .unwrap_or(false)
    }
}