// This file contains the fixes for the remaining compilation errors

// 1. Fix for lightbulb_db.rs SupportedFormat creation
// Replace the get_lightbulb_supported_formats function with:

use crate::models::lightbulb::{SupportedFormat, FormatCategory};

pub fn get_supported_formats() -> Vec<SupportedFormat> {
    vec![
        SupportedFormat {
            id: "pdf".to_string(),
            name: "PDF".to_string(),
            extensions: vec!["pdf".to_string()],
            mime_types: vec!["application/pdf".to_string()],
            category: FormatCategory::Document,
            processor: "pdf_processor".to_string(),
            is_enabled: true,
            max_file_size: Some(50 * 1024 * 1024), // 50MB in bytes
        },
        SupportedFormat {
            id: "docx".to_string(),
            name: "Word Document".to_string(),
            extensions: vec!["docx".to_string(), "doc".to_string()],
            mime_types: vec![
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document".to_string(),
                "application/msword".to_string(),
            ],
            category: FormatCategory::Document,
            processor: "word_processor".to_string(),
            is_enabled: true,
            max_file_size: Some(50 * 1024 * 1024),
        },
        SupportedFormat {
            id: "text".to_string(),
            name: "Text".to_string(),
            extensions: vec!["txt".to_string(), "md".to_string()],
            mime_types: vec!["text/plain".to_string()],
            category: FormatCategory::Document,
            processor: "text_processor".to_string(),
            is_enabled: true,
            max_file_size: Some(10 * 1024 * 1024),
        },
        SupportedFormat {
            id: "image".to_string(),
            name: "Image".to_string(),
            extensions: vec!["jpg".to_string(), "jpeg".to_string(), "png".to_string(), "gif".to_string(), "webp".to_string()],
            mime_types: vec![
                "image/jpeg".to_string(),
                "image/png".to_string(),
                "image/gif".to_string(),
                "image/webp".to_string(),
            ],
            category: FormatCategory::Image,
            processor: "image_processor".to_string(),
            is_enabled: true,
            max_file_size: Some(25 * 1024 * 1024),
        },
        SupportedFormat {
            id: "video".to_string(),
            name: "Video".to_string(),
            extensions: vec!["mp4".to_string(), "webm".to_string(), "ogg".to_string()],
            mime_types: vec![
                "video/mp4".to_string(),
                "video/webm".to_string(),
                "video/ogg".to_string(),
            ],
            category: FormatCategory::Video,
            processor: "video_processor".to_string(),
            is_enabled: true,
            max_file_size: Some(500 * 1024 * 1024),
        },
        SupportedFormat {
            id: "audio".to_string(),
            name: "Audio".to_string(),
            extensions: vec!["mp3".to_string(), "wav".to_string(), "ogg".to_string(), "weba".to_string()],
            mime_types: vec![
                "audio/mpeg".to_string(),
                "audio/wav".to_string(),
                "audio/ogg".to_string(),
                "audio/webm".to_string(),
            ],
            category: FormatCategory::Audio,
            processor: "audio_processor".to_string(),
            is_enabled: true,
            max_file_size: Some(100 * 1024 * 1024),
        },
    ]
}

// 2. Fix for SourceType comparison - add PartialEq and Eq derives to SourceType enum
// In models/lightbulb.rs, update the SourceType enum:
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum SourceType {
    Document,
    Webpage,
    Video,
    Audio,
    Image,
    Code,
    Database,
    API,
}

// 3. Fix for search.rs - convert SourceMetadata to HashMap
// In services/search.rs, update the index_document call:
use std::collections::HashMap;
use serde_json::{json, Value};

// Convert SourceMetadata to HashMap<String, Value>
let metadata_map: HashMap<String, Value> = HashMap::from([
    ("source_type".to_string(), json!(source.source_type)),
    ("file_size".to_string(), json!(source.metadata.file_size)),
    ("created_at".to_string(), json!(source.metadata.created_at)),
    // Add other metadata fields as needed
]);

self.index_document(
    &source.id,
    &source.title,
    &source.content,
    Some(metadata_map),
).await?;

// 4. Fix for search.rs - source_type comparison
// Update the filter to compare with the correct type:
if !filters.source_types.is_empty() {
    filtered_results.retain(|result| {
        // Parse the source_type string back to SourceType enum
        if let Ok(source_type) = serde_json::from_str::<SourceType>(&format!("\"{}\"", result.source_type)) {
            filters.source_types.contains(&source_type)
        } else {
            false
        }
    });
}