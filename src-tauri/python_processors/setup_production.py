#!/usr/bin/env python3
"""
Setup script for production visual processing dependencies.
Installs ML models and dependencies in a controlled manner.
"""

import subprocess
import sys
import os
import platform
from pathlib import Path

def check_python_version():
    """Ensure Python 3.8+ is being used"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        sys.exit(1)
    print(f"✅ Python {sys.version} detected")

def install_package(package, extra_args=None):
    """Install a package with pip"""
    try:
        cmd = [sys.executable, "-m", "pip", "install", package]
        if extra_args:
            cmd.extend(extra_args)
        subprocess.check_call(cmd)
        print(f"✅ Installed {package}")
        return True
    except subprocess.CalledProcessError:
        print(f"⚠️  Failed to install {package}")
        return False

def setup_core_dependencies():
    """Install core dependencies first"""
    print("\n📦 Installing core dependencies...")
    
    core_packages = [
        "numpy",
        "Pillow",
        "requests",
        "beautifulsoup4",
        "pydantic",
    ]
    
    for package in core_packages:
        install_package(package)

def setup_ocr_engines():
    """Setup OCR engines"""
    print("\n🔤 Setting up OCR engines...")
    
    # Tesseract
    if platform.system() == "Darwin":  # macOS
        print("Installing Tesseract via Homebrew...")
        subprocess.run(["brew", "install", "tesseract"], check=False)
    elif platform.system() == "Linux":
        print("Installing Tesseract via apt...")
        subprocess.run(["sudo", "apt-get", "install", "-y", "tesseract-ocr"], check=False)
    
    # Python OCR packages
    install_package("pytesseract")
    install_package("paddlepaddle", ["--no-deps"])  # Avoid dependency conflicts
    install_package("paddleocr")
    
    # EasyOCR (lightweight alternative)
    install_package("easyocr")

def setup_video_processing():
    """Setup video processing tools"""
    print("\n🎥 Setting up video processing...")
    
    # FFmpeg
    if platform.system() == "Darwin":  # macOS
        print("Installing FFmpeg via Homebrew...")
        subprocess.run(["brew", "install", "ffmpeg"], check=False)
    elif platform.system() == "Linux":
        print("Installing FFmpeg via apt...")
        subprocess.run(["sudo", "apt-get", "install", "-y", "ffmpeg"], check=False)
    
    # Python packages
    install_package("ffmpeg-python")
    
    # Whisper for transcription
    print("Installing OpenAI Whisper (this may take a while)...")
    install_package("openai-whisper")

def setup_ml_models():
    """Setup ML models for object detection and classification"""
    print("\n🤖 Setting up ML models...")
    
    # Install PyTorch (CPU version for compatibility)
    if platform.system() == "Darwin" and platform.machine() == "arm64":
        # Apple Silicon
        install_package("torch", ["--index-url", "https://download.pytorch.org/whl/cpu"])
        install_package("torchvision", ["--index-url", "https://download.pytorch.org/whl/cpu"])
    else:
        # Intel/AMD
        install_package("torch")
        install_package("torchvision")
    
    # YOLO for object detection
    install_package("ultralytics")
    
    # Transformers for scene classification
    install_package("transformers")
    
    # OpenCV for face detection
    install_package("opencv-python")
    
    print("\n📥 Downloading pre-trained models...")
    download_models()

def download_models():
    """Download pre-trained models"""
    import os
    os.makedirs("models", exist_ok=True)
    
    # Download YOLO model
    try:
        from ultralytics import YOLO
        print("Downloading YOLOv8 nano model...")
        model = YOLO('yolov8n.pt')
        model.export(format='onnx')  # Export to ONNX for faster inference
        print("✅ YOLO model ready")
    except Exception as e:
        print(f"⚠️  Could not download YOLO model: {e}")
    
    # Download Whisper model
    try:
        import whisper
        print("Downloading Whisper small model...")
        model = whisper.load_model("small")
        print("✅ Whisper model ready")
    except Exception as e:
        print(f"⚠️  Could not download Whisper model: {e}")
    
    # Download scene classification model
    try:
        from transformers import pipeline
        print("Downloading ViT scene classification model...")
        classifier = pipeline("image-classification", model="google/vit-base-patch16-224")
        print("✅ Scene classification model ready")
    except Exception as e:
        print(f"⚠️  Could not download scene model: {e}")

def verify_installation():
    """Verify all components are working"""
    print("\n🔍 Verifying installation...")
    
    components = {
        "NumPy": "numpy",
        "Pillow": "PIL",
        "OpenCV": "cv2",
        "PyTesseract": "pytesseract",
        "PaddleOCR": "paddleocr",
        "EasyOCR": "easyocr",
        "Whisper": "whisper",
        "YOLO": "ultralytics",
        "Transformers": "transformers",
    }
    
    working = []
    failed = []
    
    for name, module in components.items():
        try:
            __import__(module)
            working.append(name)
        except ImportError:
            failed.append(name)
    
    print(f"\n✅ Working components: {', '.join(working)}")
    if failed:
        print(f"⚠️  Failed components: {', '.join(failed)}")
    
    return len(failed) == 0

def main():
    """Main setup function"""
    print("🚀 Sanity Visual Processing Production Setup")
    print("=" * 50)
    
    check_python_version()
    
    # Ask user what to install
    print("\nSelect installation mode:")
    print("1. Full installation (all features)")
    print("2. OCR only")
    print("3. Video processing only")
    print("4. Object detection only")
    print("5. Minimal (core only)")
    
    choice = input("\nEnter choice (1-5): ").strip()
    
    # Always install core
    setup_core_dependencies()
    
    if choice == "1":
        setup_ocr_engines()
        setup_video_processing()
        setup_ml_models()
    elif choice == "2":
        setup_ocr_engines()
    elif choice == "3":
        setup_video_processing()
    elif choice == "4":
        setup_ml_models()
    elif choice == "5":
        print("Installing minimal dependencies only")
    else:
        print("Invalid choice, installing core only")
    
    # Verify
    if verify_installation():
        print("\n✅ Setup completed successfully!")
    else:
        print("\n⚠️  Setup completed with some components missing")
        print("You can still use the available features")
    
    print("\n📝 Note: Some features may require additional system dependencies:")
    print("  - Tesseract: brew install tesseract (macOS) or apt install tesseract-ocr (Linux)")
    print("  - FFmpeg: brew install ffmpeg (macOS) or apt install ffmpeg (Linux)")

if __name__ == "__main__":
    main()