# eLearning Backend API

## Overview
The eLearning backend provides RESTful APIs for managing industrial training content including Standard Management Procedures (SMPs), Standard Operating Procedures (SOPs), and Risk Assessments.

## Modules

### TrainingModule
Represents a complete training module with content, assessments, and media assets.

#### Properties
- `id`: Unique identifier
- `title`: Module title
- `description`: Module description
- `type`: Module type (SMP, SOP, RiskAssessment)
- `duration_minutes`: Estimated completion time
- `learning_objectives`: List of learning objectives
- `content`: Main module content
- `assessments`: Assessment questions
- `media_assets`: Associated media files
- `compliance_tags`: Compliance requirements
- `target_roles`: Intended audience roles
- `status`: Current status (Draft, InReview, Published)
- `created_by`: Creator identifier
- `created_at`: Creation timestamp
- `updated_at`: Last update timestamp

### ContentCreator
Represents a content creator with expertise tracking.

#### Properties
- `id`: Unique identifier
- `name`: Creator name
- `email`: Contact email
- `role`: Job role
- `expertise_areas`: Areas of expertise
- `status`: Current status (Active, Inactive)
- `created_at`: Creation timestamp
- `updated_at`: Last update timestamp

### ContentProject
Represents a content creation project.

#### Properties
- `id`: Unique identifier
- `title`: Project title
- `description`: Project description
- `type`: Content type (SMP, SOP, RiskAssessment)
- `status`: Current status (Planning, Creating, InReview, Published)
- `assigned_to`: Assigned creator ID
- `due_date`: Project deadline
- `progress_percentage`: Completion percentage
- `tags`: Project tags
- `created_at`: Creation timestamp
- `updated_at`: Last update timestamp

## API Endpoints

### Training Modules
- `POST /elearning/modules` - Create a new training module
- `GET /elearning/modules` - List training modules with filtering
- `GET /elearning/modules/{id}` - Get a specific training module
- `PUT /elearning/modules/{id}` - Update a training module
- `DELETE /elearning/modules/{id}` - Delete a training module

### Content Creators
- `POST /elearning/creators` - Create a new content creator
- `GET /elearning/creators` - List content creators
- `GET /elearning/creators/{id}` - Get a specific content creator
- `PUT /elearning/creators/{id}` - Update a content creator
- `DELETE /elearning/creators/{id}` - Delete a content creator

### Content Projects
- `POST /elearning/projects` - Create a new content project
- `GET /elearning/projects` - List content projects with filtering
- `GET /elearning/projects/{id}` - Get a specific content project
- `PUT /elearning/projects/{id}` - Update a content project
- `DELETE /elearning/projects/{id}` - Delete a content project

### Analytics
- `GET /elearning/metrics` - Get eLearning metrics and statistics

## Authentication
All endpoints require authentication via JWT tokens.

## Error Handling
All endpoints return standard HTTP status codes:
- `200`: Success
- `201`: Created
- `400`: Bad Request
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `500`: Internal Server Error

## Rate Limiting
API requests are limited to 1000 requests per hour per user.

## Data Validation
All input data is validated using JSON Schema validators.