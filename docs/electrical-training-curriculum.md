# Electrical & Electrician Training Curriculum

## Overview
This document outlines the comprehensive electrical training curriculum for industrial electricians, covering basic, advanced, and expertise levels with specialized content on VFD 750/755 drives and Rockwell Automation PLCs.

## Curriculum Structure

### 1. Basic Electrical Training
**Target Audience**: Electrician Apprentices, Maintenance Technicians
**Duration**: 120 minutes
**Key Topics**:
- Electrical fundamentals (voltage, current, resistance, power)
- Ohm's law and basic circuit analysis
- Series and parallel circuits
- Basic electrical safety (OSHA, NFPA 70E)

### 2. Advanced Electrical Systems
**Target Audience**: Journeyman Electricians, Maintenance Supervisors
**Duration**: 180 minutes
**Key Topics**:
- Three-phase power systems (Wye/Delta configurations)
- Transformers (operation, losses, applications)
- Motor control circuits (contactors, relays, overload protection)

### 3. Electrical Expertise - Industrial Applications
**Target Audience**: Master Electricians, Electrical Engineers
**Duration**: 240 minutes
**Key Topics**:
- Advanced troubleshooting techniques
- Arc flash safety and protection
- Industrial electrical distribution design
- Grounding systems and power quality

## VFD Training Modules

### VFD 750 Series - Basic Operation
**Target Audience**: Electrician Apprentices, Maintenance Technicians
**Duration**: 90 minutes
**Key Topics**:
- VFD fundamentals and benefits
- Keypad navigation and display interpretation
- Basic parameter setup (motor nameplate data)
- Run/stop commands and speed references

### VFD 755 Series - Advanced Features
**Target Audience**: Journeyman Electricians, Control Systems Technicians
**Duration**: 150 minutes
**Key Topics**:
- Advanced control methods (analog/digital inputs)
- Serial communication (Modbus protocol)
- Preset speed programming and sequences
- Fault code interpretation and troubleshooting

## PLC Training Modules - Rockwell Automation

### PLC Basics (RSLogix 5000)
**Target Audience**: Electrician Apprentices, Automation Technicians
**Duration**: 180 minutes
**Key Topics**:
- PLC architecture and scan cycle operation
- RSLogix 5000 software interface
- Basic ladder logic programming
- Input/output module configuration

### PLC Advanced Programming
**Target Audience**: Control Systems Engineers, Senior Automation Technicians
**Duration**: 240 minutes
**Key Topics**:
- Structured Text programming language
- Sequential Function Chart (SFC) for complex sequences
- User-defined data types and arrays
- HMI integration with PanelView

### PLC Expertise - System Integration
**Target Audience**: Control Systems Engineers, Automation Specialists
**Duration**: 300 minutes
**Key Topics**:
- ControlLogix/CompactLogix system architecture
- EtherNet/IP industrial networking
- Redundancy and scalability strategies
- Advanced diagnostics and predictive maintenance

## Compliance Standards
All training modules incorporate relevant safety standards:
- OSHA electrical safety requirements
- NFPA 70E (Electrical Safety in the Workplace)
- NFPA 70 (National Electrical Code)
- IEEE standards for industrial automation

## Target Roles by Level
- **Basic Level**: Electrician Apprentice, Maintenance Technician
- **Advanced Level**: Journeyman Electrician, Maintenance Supervisor
- **Expertise Level**: Master Electrician, Electrical Engineer, Control Systems Engineer

## Assessment Methods
Each module includes:
- Knowledge check quizzes
- Practical scenario questions
- Hands-on lab exercises (where applicable)
- Final competency evaluations

## Media Assets
Training modules incorporate:
- Circuit diagrams and wiring schematics
- Video demonstrations of equipment operation
- Interactive simulations
- Reference documentation and datasheets

## Prerequisites
- Basic mathematical skills
- Fundamental understanding of electrical concepts for advanced/expert modules
- Computer literacy for PLC programming modules