import { createTheme, ThemeOptions } from '@mui/material/styles';
import { red, green, blue, orange, grey } from '@mui/material/colors';

// Custom color palette
const customColors = {
  primary: {
    main: '#5e72e4',
    light: '#8896ff',
    dark: '#1a48b2',
    contrastText: '#fff',
  },
  secondary: {
    main: '#f4f5f7',
    light: '#ffffff',
    dark: '#c1c2c5',
    contrastText: '#344767',
  },
  info: {
    main: '#2dcecc',
    light: '#6dffff',
    dark: '#009c9b',
  },
  success: {
    main: '#2dce89',
    light: '#69ffba',
    dark: '#009c5a',
  },
  warning: {
    main: '#fb6340',
    light: '#ff956d',
    dark: '#c32f14',
  },
  error: {
    main: '#f5365c',
    light: '#ff688a',
    dark: '#bd0032',
  },
  dark: {
    main: '#344767',
    light: '#617394',
    dark: '#0a1f3b',
  }
};

// Light theme configuration
export const lightTheme = createTheme({
  palette: {
    mode: 'light',
    ...customColors,
    background: {
      default: '#f8f9fa',
      paper: '#ffffff',
    },
    text: {
      primary: '#344767',
      secondary: '#7b809a',
    },
  },
  typography: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontSize: '3rem',
      fontWeight: 700,
      lineHeight: 1.2,
    },
    h2: {
      fontSize: '2.25rem',
      fontWeight: 700,
      lineHeight: 1.3,
    },
    h3: {
      fontSize: '1.875rem',
      fontWeight: 700,
      lineHeight: 1.375,
    },
    h4: {
      fontSize: '1.5rem',
      fontWeight: 600,
      lineHeight: 1.4,
    },
    h5: {
      fontSize: '1.25rem',
      fontWeight: 600,
      lineHeight: 1.5,
    },
    h6: {
      fontSize: '1rem',
      fontWeight: 600,
      lineHeight: 1.625,
    },
    body1: {
      fontSize: '1rem',
      fontWeight: 400,
      lineHeight: 1.625,
    },
    body2: {
      fontSize: '0.875rem',
      fontWeight: 400,
      lineHeight: 1.6,
    },
    button: {
      textTransform: 'none',
      fontWeight: 600,
    },
  },
  shape: {
    borderRadius: 8,
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          padding: '10px 24px',
          boxShadow: 'none',
          '&:hover': {
            boxShadow: '0 4px 6px rgba(50,50,93,.11), 0 1px 3px rgba(0,0,0,.08)',
          },
        },
        contained: {
          boxShadow: '0 4px 6px rgba(50,50,93,.11), 0 1px 3px rgba(0,0,0,.08)',
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          boxShadow: '0 20px 27px 0 rgba(0,0,0,0.05)',
          borderRadius: 16,
          padding: 0,
        },
      },
    },
    MuiCardContent: {
      styleOverrides: {
        root: {
          padding: 24,
          '&:last-child': {
            paddingBottom: 24,
          },
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          boxShadow: '0 20px 27px 0 rgba(0,0,0,0.05)',
        },
        rounded: {
          borderRadius: 16,
        },
      },
    },
    MuiTableCell: {
      styleOverrides: {
        root: {
          borderBottom: '1px solid #e9ecef',
          padding: '12px 16px',
        },
        head: {
          fontWeight: 700,
          textTransform: 'uppercase',
          fontSize: '0.65rem',
          letterSpacing: 1,
          color: '#8898aa',
        },
      },
    },
    MuiChip: {
      styleOverrides: {
        root: {
          borderRadius: 6,
          fontWeight: 600,
          fontSize: '0.75rem',
        },
      },
    },
    MuiLinearProgress: {
      styleOverrides: {
        root: {
          borderRadius: 3,
          height: 6,
        },
        bar: {
          borderRadius: 3,
        },
      },
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            borderRadius: 8,
            '& fieldset': {
              borderColor: '#dee2e6',
            },
            '&:hover fieldset': {
              borderColor: '#adb5bd',
            },
          },
        },
      },
    },
  },
} as ThemeOptions);

// Dark theme configuration
export const darkTheme = createTheme({
  palette: {
    mode: 'dark',
    ...customColors,
    background: {
      default: '#171923',
      paper: '#1a202c',
    },
    text: {
      primary: '#f7fafc',
      secondary: '#a0aec0',
    },
  },
  typography: lightTheme.typography,
  shape: lightTheme.shape,
  components: {
    ...lightTheme.components,
    MuiCard: {
      styleOverrides: {
        root: {
          boxShadow: '0 0 2rem 0 rgba(136,152,170,.15)',
          borderRadius: 16,
          backgroundColor: '#1a202c',
          padding: 0,
        },
      },
    },
    MuiTableCell: {
      styleOverrides: {
        root: {
          borderBottom: '1px solid #2d3748',
          padding: '12px 16px',
        },
        head: {
          fontWeight: 700,
          textTransform: 'uppercase',
          fontSize: '0.65rem',
          letterSpacing: 1,
          color: '#718096',
        },
      },
    },
  },
} as ThemeOptions);

// Export default theme
export default lightTheme;