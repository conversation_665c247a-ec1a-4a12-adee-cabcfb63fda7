import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

// Import session components
import { SessionList } from '@/components/sessions/SessionList';
import { RunningClaudeSessions } from '@/components/sessions/RunningClaudeSessions';
import { SessionManager } from '@/components/sessions/claude-code-session/SessionManager';
import { SessionMetrics } from '@/components/sessions/claude-code-session/SessionMetrics';
import { StatusIndicators } from '@/components/sessions/claude-code-session/StatusIndicators';
import { SessionPanels } from '@/components/sessions/claude-code-session/SessionPanels';

// Mock data
const mockSessions = [
  {
    id: 'sess-001',
    name: 'React Dashboard',
    projectPath: '/Users/<USER>/projects/dashboard',
    status: 'active' as const,
    createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
    lastActivity: new Date(Date.now() - 5 * 60 * 1000),
    messageCount: 45,
    tokenCount: 12500,
    agentsUsed: ['code-reviewer', 'test-generator'],
    mcpServers: ['filesystem', 'git'],
    isStarred: true,
    isBackground: false,
    created_at: Date.now() - 2 * 60 * 60 * 1000,
    project_id: 'proj-dashboard',
    project_path: '/Users/<USER>/projects/dashboard'
  },
  {
    id: 'sess-002',
    name: 'API Integration',
    projectPath: '/Users/<USER>/projects/api-client',
    status: 'paused' as const,
    createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000),
    lastActivity: new Date(Date.now() - 3 * 60 * 60 * 1000),
    messageCount: 28,
    tokenCount: 8900,
    agentsUsed: ['api-designer'],
    mcpServers: ['http-client'],
    isStarred: false,
    isBackground: true,
    created_at: Date.now() - 24 * 60 * 60 * 1000,
    project_id: 'proj-api',
    project_path: '/Users/<USER>/projects/api-client'
  },
  {
    id: 'sess-003',
    name: 'Database Migration',
    projectPath: '/Users/<USER>/projects/migration',
    status: 'completed' as const,
    createdAt: new Date(Date.now() - 48 * 60 * 60 * 1000),
    lastActivity: new Date(Date.now() - 12 * 60 * 60 * 1000),
    messageCount: 67,
    tokenCount: 18750,
    agentsUsed: ['database-expert', 'migration-helper'],
    mcpServers: ['database', 'filesystem'],
    isStarred: true,
    isBackground: false,
    created_at: Date.now() - 48 * 60 * 60 * 1000,
    project_id: 'proj-migration',
    project_path: '/Users/<USER>/projects/migration'
  }
];

const mockMetrics = {
  duration: 7200, // 2 hours
  messageCount: 45,
  tokenCount: 12500,
  toolsExecuted: 23,
  filesModified: 8,
  agentsUsed: 2,
  mcpCalls: 15,
  errorRate: 2.3,
  avgResponseTime: 1.8
};

const mockRunningProcesses = [
  {
    pid: 12345,
    started_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
    project_path: '/Users/<USER>/projects/dashboard',
    process_type: {
      ClaudeSession: {
        session_id: 'sess-001'
      }
    },
    message_count: 45,
    agent_active: true
  },
  {
    pid: 12346,
    started_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
    project_path: '/Users/<USER>/projects/api-client',
    process_type: {
      ClaudeSession: {
        session_id: 'sess-002'
      }
    },
    message_count: 28,
    agent_active: false
  }
];

export default function SessionsShowcase() {
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedSession, setSelectedSession] = useState<any>(null);

  const handleSessionClick = (session: any) => {
    setSelectedSession(session);
    console.log('Session clicked:', session);
  };

  const handleSessionAction = (sessionId: string, action: string) => {
    console.log('Session action:', sessionId, action);
  };

  const handleCreateSession = () => {
    console.log('Create new session');
  };

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center space-y-4"
        >
          <h1 className="text-heading-xl">Enhanced Sessions UI</h1>
          <p className="text-body text-muted-foreground max-w-2xl mx-auto">
            A comprehensive showcase of the enhanced Claude Code session management interface 
            with modern design patterns, improved hierarchy, and consistent color schemes.
          </p>
        </motion.div>

        {/* Navigation Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="manager">Session Manager</TabsTrigger>
            <TabsTrigger value="running">Running Sessions</TabsTrigger>
            <TabsTrigger value="metrics">Metrics</TabsTrigger>
            <TabsTrigger value="components">Components</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="session-card">
                <CardHeader>
                  <CardTitle>Design System Features</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <h4 className="font-medium">Visual Enhancements</h4>
                    <ul className="text-sm text-muted-foreground space-y-1">
                      <li>• Glassmorphism effects with backdrop blur</li>
                      <li>• Consistent color tokens for all states</li>
                      <li>• Enhanced typography hierarchy</li>
                      <li>• Smooth animations and transitions</li>
                    </ul>
                  </div>
                  <Separator />
                  <div className="space-y-2">
                    <h4 className="font-medium">Component Architecture</h4>
                    <ul className="text-sm text-muted-foreground space-y-1">
                      <li>• Modular component structure</li>
                      <li>• Reusable status indicators</li>
                      <li>• Responsive panel system</li>
                      <li>• Accessible interactions</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>

              <Card className="session-card">
                <CardHeader>
                  <CardTitle>Status Indicators Demo</CardTitle>
                </CardHeader>
                <CardContent>
                  <StatusIndicators
                    detectedAgentOpportunity={{
                      agentType: 'code-reviewer',
                      confidence: 0.85,
                      reason: 'Detected code review patterns in conversation'
                    }}
                    activeAgent={{
                      type: 'test-generator',
                      name: 'Test Generator Agent',
                      taskId: 'task-123',
                      success: undefined
                    }}
                    isWaitingForResponse={false}
                    isPlanMode={true}
                    isExtendedThinking={false}
                    onExecuteAgent={() => console.log('Execute agent')}
                    onDismissOpportunity={() => console.log('Dismiss opportunity')}
                  />
                </CardContent>
              </Card>
            </div>

            <Card className="session-card">
              <CardHeader>
                <CardTitle>Session List Preview</CardTitle>
              </CardHeader>
              <CardContent>
                <SessionList
                  sessions={mockSessions}
                  projectPath="/Users/<USER>/projects"
                  onBack={() => console.log('Back clicked')}
                  onSessionClick={handleSessionClick}
                  onEditClaudeFile={(file) => console.log('Edit file:', file)}
                />
              </CardContent>
            </Card>
          </TabsContent>

          {/* Session Manager Tab */}
          <TabsContent value="manager" className="space-y-6">
            <SessionManager
              sessions={mockSessions}
              onCreateSession={handleCreateSession}
              onSessionClick={handleSessionClick}
              onSessionAction={handleSessionAction}
            />
          </TabsContent>

          {/* Running Sessions Tab */}
          <TabsContent value="running" className="space-y-6">
            <RunningClaudeSessions
              onSessionClick={handleSessionClick}
              showCompact={false}
            />
            
            <Card className="session-card">
              <CardHeader>
                <CardTitle>Compact View</CardTitle>
              </CardHeader>
              <CardContent>
                <RunningClaudeSessions
                  onSessionClick={handleSessionClick}
                  showCompact={true}
                />
              </CardContent>
            </Card>
          </TabsContent>

          {/* Metrics Tab */}
          <TabsContent value="metrics" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <SessionMetrics
                metrics={mockMetrics}
                isCompact={false}
              />
              
              <Card className="session-card">
                <CardHeader>
                  <CardTitle>Compact Metrics</CardTitle>
                </CardHeader>
                <CardContent>
                  <SessionMetrics
                    metrics={mockMetrics}
                    isCompact={true}
                  />
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Components Tab */}
          <TabsContent value="components" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* Status Badges */}
              <Card className="session-card">
                <CardHeader>
                  <CardTitle className="text-base">Status Badges</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Badge className="session-active">Active</Badge>
                  <Badge className="session-paused">Paused</Badge>
                  <Badge className="session-completed">Completed</Badge>
                  <Badge className="session-error">Error</Badge>
                  <Badge className="agent-indicator">Agent</Badge>
                  <Badge className="mcp-indicator">MCP</Badge>
                </CardContent>
              </Card>

              {/* Interactive Cards */}
              <Card className="interactive-card">
                <CardHeader>
                  <CardTitle className="text-base">Interactive Card</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">
                    Hover and click to see enhanced interactions
                  </p>
                </CardContent>
              </Card>

              {/* Glass Panel */}
              <Card className="glass-panel">
                <CardHeader>
                  <CardTitle className="text-base">Glass Panel</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">
                    Glassmorphism effect with backdrop blur
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Typography Showcase */}
            <Card className="session-card">
              <CardHeader>
                <CardTitle>Typography System</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h1 className="session-title">Session Title</h1>
                  <p className="session-subtitle">Session subtitle with enhanced styling</p>
                </div>
                <Separator />
                <div className="space-y-2">
                  <h2 className="text-heading-md">Heading Medium</h2>
                  <h3 className="text-heading-sm">Heading Small</h3>
                  <p className="text-body">Body text with proper line height and spacing</p>
                  <p className="text-body-sm">Small body text for secondary information</p>
                  <p className="text-caption">Caption text for metadata</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Footer */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
          className="text-center py-8"
        >
          <p className="text-sm text-muted-foreground">
            Enhanced Sessions UI - Modern design system for Claude Code sessions
          </p>
        </motion.div>
      </div>
    </div>
  );
}