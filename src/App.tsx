import { useState, useEffect, Suspense } from "react";
import { QueryClientProvider } from "@tanstack/react-query";
import { OutputCacheProvider } from "@/lib/outputCache";
import { TabProvider } from "@/contexts/TabContext";
import { ThemeProvider } from "@/contexts/ThemeContext";
import { Topbar } from "@/components/navigation/Topbar";
import { CommandPalette } from "@/components/navigation/CommandPalette";
import { ClaudeBinaryDialog } from "@/components/settings/ClaudeBinaryDialog";
import { Toast, ToastContainer } from "@/components/ui/toast";
import { TabManager } from "@/components/navigation/TabManager";
import { TabContent } from "@/components/navigation/TabContent";
import { useTabState } from "@/hooks/useTabState";
import { AppErrorBoundary } from "@/components/common/AppErrorBoundary";
import { createQueryClient } from "@/lib/queryClient";
import { BrowserRouter } from "react-router-dom";
import { LoadingSkeleton } from "@/components/common/LoadingSkeleton";
import "@/components/common/LoadingSkeleton.css";
import "@/lib/windowApi"; // Initialize window API
import { agentAnalytics } from "@/services/agentAnalyticsService"; // Initialize analytics
// AgentsModal removed - now using unified AgentsHub in tabs

/**
 * AppContent component - Contains the main app logic, wrapped by providers
 */
function AppContent() {
  const { createClaudeMdTab, createSettingsTab, createUsageTab, createMCPTab, createTrainingBudgetTab, createTrainingDashboardTab, createLDProfessionalTNATab, createGraduateTraineeTrackerTab, createAgentsHubTab, createDataCenterTab, createELearningDesignBoardTab, createContentCreatorTab, createLighthouseLMHubTab, createLightbulbTab, createContentCreatorsHubTab, createAnalyticsTab, createWorkflowDesignerTab } = useTabState();
  const [showClaudeBinaryDialog, setShowClaudeBinaryDialog] = useState(false);
  const [showCommandPalette, setShowCommandPalette] = useState(false);
  // Remove agents modal state since we're using tabs now
  const [toast, setToast] = useState<{ message: string; type: "success" | "error" | "info" } | null>(null);
  
  // Initialize analytics service
  useEffect(() => {
    agentAnalytics.initialize().catch(() => {});
  }, []);

  // Handle toast events from child components
  useEffect(() => {
    const handleShowToast = (event: CustomEvent) => {
      const { message, type } = event.detail;
      setToast({ message, type });
    };

    window.addEventListener('show-toast', handleShowToast as EventListener);
    return () => {
      window.removeEventListener('show-toast', handleShowToast as EventListener);
    };
  }, []);

  // Keyboard shortcuts for tab navigation and command palette
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      const isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0;
      const modKey = isMac ? e.metaKey : e.ctrlKey;
      
      if (modKey) {
        switch (e.key) {
          case 'k':
            e.preventDefault();
            setShowCommandPalette(prev => !prev);
            break;
          case 't':
            e.preventDefault();
            window.dispatchEvent(new CustomEvent('create-chat-tab'));
            break;
          case 'w':
            e.preventDefault();
            window.dispatchEvent(new CustomEvent('close-current-tab'));
            break;
          case 'Tab':
            e.preventDefault();
            if (e.shiftKey) {
              window.dispatchEvent(new CustomEvent('switch-to-previous-tab'));
            } else {
              window.dispatchEvent(new CustomEvent('switch-to-next-tab'));
            }
            break;
          default:
            // Handle number keys 1-9
            if (e.key >= '1' && e.key <= '9') {
              e.preventDefault();
              const index = parseInt(e.key) - 1;
              window.dispatchEvent(new CustomEvent('switch-to-tab-by-index', { detail: { index } }));
            }
            break;
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  // Listen for Claude not found events
  useEffect(() => {
    const handleClaudeNotFound = () => {
      setShowClaudeBinaryDialog(true);
    };

    window.addEventListener('claude-not-found', handleClaudeNotFound as EventListener);
    return () => {
      window.removeEventListener('claude-not-found', handleClaudeNotFound as EventListener);
    };
  }, []);


  return (
    <div className="h-screen bg-background flex flex-col">
      <Topbar
        onCommandPaletteClick={() => setShowCommandPalette(true)}
      />
      
      {/* Command Palette */}
      <CommandPalette
        open={showCommandPalette}
        onOpenChange={setShowCommandPalette}
        onClaudeClick={() => createClaudeMdTab()}
        onSettingsClick={() => createSettingsTab()}
        onUsageClick={() => createUsageTab()}
        onMCPClick={() => createMCPTab()}
        onAgentsClick={() => createAgentsHubTab()}
        onAnalyticsClick={() => createAnalyticsTab()}
        onWorkflowClick={() => createWorkflowDesignerTab()}
        onTrainingBudgetClick={() => createTrainingBudgetTab()}
        onTrainingDashboardClick={() => createTrainingDashboardTab()}
        onLDProfessionalTNAClick={() => createLDProfessionalTNATab()}
        onGraduateTraineeTrackerClick={() => createGraduateTraineeTrackerTab()}
        onDataCenterClick={() => createDataCenterTab()}
        onContentCreatorsClick={() => createContentCreatorsHubTab()}
        onLighthouseLMClick={() => createLighthouseLMHubTab()}
        onLightbulbClick={() => createLightbulbTab()}
      />
      
      {/* Tab Interface */}
      <TabManager />
      <div className="flex-1 overflow-hidden">
        <TabContent />
      </div>
      {/* Agents Modal removed - now using tabs */}
      <ClaudeBinaryDialog
        open={showClaudeBinaryDialog}
        onOpenChange={setShowClaudeBinaryDialog}
        onSuccess={() => { setToast({ message: "Claude binary path saved successfully", type: "success" }); }}
        onError={(message) => setToast({ message, type: "error" })}
      />
      <ToastContainer>
        {toast && (
          <Toast message={toast.message} type={toast.type} onDismiss={() => setToast(null)} />
        )}
      </ToastContainer>
    </div>
  );
}

// Create enhanced QueryClient instance with error handling
const queryClient = createQueryClient();

/**
 * Main App component - Wraps the app with providers
 */
function App() {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Hide the HTML splash screen and show React loading skeleton
    const splash = document.getElementById('splash-screen');
    if (splash) {
      splash.style.display = 'none';
    }

    // Simulate initial load time (you can tie this to actual initialization)
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 5000);

    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return <LoadingSkeleton />;
  }

  return (
    <AppErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <OutputCacheProvider>
          <ThemeProvider>
            <TabProvider>
              <BrowserRouter>
                <Suspense fallback={<LoadingSkeleton />}>
                  <AppContent />
                </Suspense>
              </BrowserRouter>
            </TabProvider>
          </ThemeProvider>
        </OutputCacheProvider>
      </QueryClientProvider>
    </AppErrorBoundary>
  );
}

export default App;
