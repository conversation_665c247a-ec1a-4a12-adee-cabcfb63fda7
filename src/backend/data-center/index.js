// Data Center Backend Service
import express from 'express';
import { WebSocketServer } from 'ws';
import multer from 'multer';
import { Queue } from 'bull';
import Redis from 'ioredis';
import { Pool } from 'pg';
import { DataSourceManager } from './services/DataSourceManager.js';
import { JobProcessor } from './services/JobProcessor.js';
import { MetricsCollector } from './services/MetricsCollector.js';
import { DataQualityEngine } from './services/DataQualityEngine.js';
import { DataFlowOrchestrator } from './services/DataFlowOrchestrator.js';
import { QueryEngine } from './services/QueryEngine.js';
import { PythonExecutor } from './services/PythonExecutor.js';
import { FileProcessor } from './services/FileProcessor.js';

class DataCenterBackend {
  constructor(config) {
    this.config = config;
    this.app = express.Router();
    this.redis = new Redis(config.redis);
    this.db = new Pool(config.database);
    this.jobQueue = new Queue('data-processing', config.redis);
    this.upload = multer({ dest: config.uploadPath });
    
    this.initializeServices();
    this.setupRoutes();
    this.setupWebSocket();
  }

  initializeServices() {
    this.dataSourceManager = new DataSourceManager(this.db);
    this.jobProcessor = new JobProcessor(this.jobQueue, this.db);
    this.metricsCollector = new MetricsCollector(this.db, this.redis);
    this.dataQualityEngine = new DataQualityEngine(this.db);
    this.dataFlowOrchestrator = new DataFlowOrchestrator(this.db, this.jobQueue);
    this.queryEngine = new QueryEngine(this.db);
    this.pythonExecutor = new PythonExecutor(this.config.pythonPath);
    this.fileProcessor = new FileProcessor(this.db);
  }

  setupRoutes() {
    // Data Sources
    this.app.get('/sources', this.handleGetSources.bind(this));
    this.app.post('/sources', this.handleCreateSource.bind(this));
    this.app.put('/sources/:id', this.handleUpdateSource.bind(this));
    this.app.delete('/sources/:id', this.handleDeleteSource.bind(this));
    this.app.post('/sources/:id/test', this.handleTestConnection.bind(this));
    this.app.post('/sources/:id/sync', this.handleSyncSource.bind(this));

    // Processing Jobs
    this.app.get('/jobs', this.handleGetJobs.bind(this));
    this.app.post('/jobs', this.handleCreateJob.bind(this));
    this.app.get('/jobs/:id', this.handleGetJob.bind(this));
    this.app.post('/jobs/:id/cancel', this.handleCancelJob.bind(this));
    this.app.get('/jobs/:id/logs', this.handleGetJobLogs.bind(this));

    // Metrics
    this.app.get('/metrics/system', this.handleGetSystemMetrics.bind(this));
    this.app.get('/metrics/quality', this.handleGetQualityMetrics.bind(this));
    this.app.get('/metrics/performance', this.handleGetPerformanceMetrics.bind(this));

    // Data Flow
    this.app.get('/flow', this.handleGetDataFlow.bind(this));
    this.app.post('/flow', this.handleCreateFlow.bind(this));
    this.app.put('/flow/:id', this.handleUpdateFlow.bind(this));
    this.app.post('/flow/:id/execute', this.handleExecuteFlow.bind(this));

    // Query Execution
    this.app.post('/query', this.handleExecuteQuery.bind(this));
    this.app.get('/query/history', this.handleGetQueryHistory.bind(this));

    // File Upload
    this.app.post('/upload', this.upload.single('file'), this.handleFileUpload.bind(this));
    
    // Python Execution
    this.app.post('/python/execute', this.handlePythonExecute.bind(this));
  }

  setupWebSocket() {
    this.wss = new WebSocketServer({ noServer: true });
    
    this.wss.on('connection', (ws, req) => {
      const sessionId = req.url.split('/').pop();
      
      ws.on('message', async (message) => {
        const data = JSON.parse(message);
        await this.handleWebSocketMessage(ws, sessionId, data);
      });

      // Subscribe to real-time updates
      this.subscribeToUpdates(ws, sessionId);
    });
  }

  // Data Source Handlers
  async handleGetSources(req, res) {
    try {
      const sources = await this.dataSourceManager.getSources(req.query);
      res.json(sources);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async handleCreateSource(req, res) {
    try {
      const source = await this.dataSourceManager.createSource(req.body);
      res.status(201).json(source);
    } catch (error) {
      res.status(400).json({ error: error.message });
    }
  }

  async handleTestConnection(req, res) {
    try {
      const result = await this.dataSourceManager.testConnection(req.params.id);
      res.json(result);
    } catch (error) {
      res.status(400).json({ error: error.message });
    }
  }

  // Job Processing Handlers
  async handleCreateJob(req, res) {
    try {
      const job = await this.jobProcessor.createJob(req.body);
      res.status(201).json(job);
    } catch (error) {
      res.status(400).json({ error: error.message });
    }
  }

  async handleGetJob(req, res) {
    try {
      const job = await this.jobProcessor.getJob(req.params.id);
      res.json(job);
    } catch (error) {
      res.status(404).json({ error: 'Job not found' });
    }
  }

  // Metrics Handlers
  async handleGetSystemMetrics(req, res) {
    try {
      const metrics = await this.metricsCollector.getSystemMetrics();
      res.json(metrics);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async handleGetQualityMetrics(req, res) {
    try {
      const metrics = await this.dataQualityEngine.getMetrics(req.query);
      res.json(metrics);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  // Query Execution Handler
  async handleExecuteQuery(req, res) {
    try {
      const result = await this.queryEngine.execute(req.body.query, req.body.params);
      res.json(result);
    } catch (error) {
      res.status(400).json({ error: error.message });
    }
  }

  // File Upload Handler
  async handleFileUpload(req, res) {
    try {
      const result = await this.fileProcessor.processFile(req.file, req.body);
      const job = await this.jobProcessor.createJob({
        type: 'file_import',
        payload: result
      });
      res.json({ jobId: job.id, ...result });
    } catch (error) {
      res.status(400).json({ error: error.message });
    }
  }

  // Python Execution Handler
  async handlePythonExecute(req, res) {
    try {
      const result = await this.pythonExecutor.execute(req.body.code, req.body.context);
      res.json(result);
    } catch (error) {
      res.status(400).json({ error: error.message });
    }
  }

  // WebSocket Message Handler
  async handleWebSocketMessage(ws, sessionId, data) {
    switch (data.type) {
      case 'subscribe':
        await this.addSubscription(sessionId, data.channel, data.filters);
        break;
      case 'unsubscribe':
        await this.removeSubscription(sessionId, data.channel);
        break;
      case 'execute':
        await this.executeRealtimeQuery(ws, data.query);
        break;
    }
  }

  // Real-time Updates
  subscribeToUpdates(ws, sessionId) {
    // Job status updates
    this.jobQueue.on('progress', (job, progress) => {
      ws.send(JSON.stringify({
        type: 'job_progress',
        jobId: job.id,
        progress
      }));
    });

    // Metrics updates
    setInterval(async () => {
      const metrics = await this.metricsCollector.getRealtimeMetrics();
      ws.send(JSON.stringify({
        type: 'metrics_update',
        metrics
      }));
    }, 5000);
  }

  // Remaining handler stubs
  async handleUpdateSource(req, res) {
    const result = await this.dataSourceManager.updateSource(req.params.id, req.body);
    res.json(result);
  }

  async handleDeleteSource(req, res) {
    await this.dataSourceManager.deleteSource(req.params.id);
    res.status(204).send();
  }

  async handleSyncSource(req, res) {
    const job = await this.dataSourceManager.syncSource(req.params.id);
    res.json(job);
  }

  async handleGetJobs(req, res) {
    const jobs = await this.jobProcessor.getJobs(req.query);
    res.json(jobs);
  }

  async handleCancelJob(req, res) {
    await this.jobProcessor.cancelJob(req.params.id);
    res.json({ status: 'cancelled' });
  }

  async handleGetJobLogs(req, res) {
    const logs = await this.jobProcessor.getJobLogs(req.params.id);
    res.json(logs);
  }

  async handleGetPerformanceMetrics(req, res) {
    const metrics = await this.metricsCollector.getPerformanceMetrics();
    res.json(metrics);
  }

  async handleGetDataFlow(req, res) {
    const flow = await this.dataFlowOrchestrator.getFlow(req.query);
    res.json(flow);
  }

  async handleCreateFlow(req, res) {
    const flow = await this.dataFlowOrchestrator.createFlow(req.body);
    res.status(201).json(flow);
  }

  async handleUpdateFlow(req, res) {
    const flow = await this.dataFlowOrchestrator.updateFlow(req.params.id, req.body);
    res.json(flow);
  }

  async handleExecuteFlow(req, res) {
    const execution = await this.dataFlowOrchestrator.executeFlow(req.params.id);
    res.json(execution);
  }

  async handleGetQueryHistory(req, res) {
    const history = await this.queryEngine.getHistory(req.query);
    res.json(history);
  }

  async addSubscription(sessionId, channel, filters) {
    await this.redis.set(`sub:${sessionId}:${channel}`, JSON.stringify(filters));
  }

  async removeSubscription(sessionId, channel) {
    await this.redis.del(`sub:${sessionId}:${channel}`);
  }

  async executeRealtimeQuery(ws, query) {
    const result = await this.queryEngine.executeStreaming(query);
    ws.send(JSON.stringify({ type: 'query_result', result }));
  }
}

export default DataCenterBackend;