// Data Center Configuration
export const dataCenterConfig = {
  // Database Configuration
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    database: process.env.DB_NAME || 'datacenter',
    user: process.env.DB_USER || 'datacenter_user',
    password: process.env.DB_PASSWORD,
    max: 20,
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 2000,
  },

  // Redis Configuration for Queue & Caching
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: process.env.REDIS_PORT || 6379,
    password: process.env.REDIS_PASSWORD,
    db: process.env.REDIS_DB || 0,
    maxRetriesPerRequest: 3,
    enableReadyCheck: true,
    lazyConnect: false,
  },

  // Job Queue Configuration
  queue: {
    defaultJobOptions: {
      removeOnComplete: 100,
      removeOnFail: 500,
      attempts: 3,
      backoff: {
        type: 'exponential',
        delay: 2000,
      },
    },
    concurrency: {
      data_import: 5,
      data_export: 10,
      data_transform: 8,
      data_validation: 15,
      data_sync: 3,
      batch_process: 5,
    },
  },

  // File Processing
  fileProcessing: {
    uploadPath: process.env.UPLOAD_PATH || './uploads',
    tempPath: process.env.TEMP_PATH || './temp',
    maxFileSize: 500 * 1024 * 1024, // 500MB
    supportedFormats: [
      'csv', 'json', 'xlsx', 'xml', 
      'parquet', 'avro', 'tsv', 'txt'
    ],
    chunkSize: 10000, // Records per chunk
  },

  // Python Integration
  python: {
    pythonPath: process.env.PYTHON_PATH || 'python3',
    scriptPath: './python_scripts',
    timeout: 300000, // 5 minutes
    maxMemory: '2GB',
  },

  // Data Source Defaults
  dataSources: {
    connectionTimeout: 10000,
    queryTimeout: 60000,
    poolSize: 5,
    retryAttempts: 3,
    supportedTypes: [
      'postgresql', 'mysql', 'mongodb', 'redis',
      'elasticsearch', 'cassandra', 'snowflake',
      'bigquery', 'redshift', 'api', 'file',
      's3', 'azure_blob', 'gcs', 'ftp', 'sftp'
    ],
  },

  // Metrics Collection
  metrics: {
    collectInterval: 5000, // 5 seconds
    retentionDays: 30,
    aggregationIntervals: ['1m', '5m', '1h', '1d'],
    systemMetrics: ['cpu', 'memory', 'disk', 'network'],
    qualityMetrics: [
      'completeness', 'accuracy', 'consistency',
      'timeliness', 'validity', 'uniqueness'
    ],
  },

  // Data Quality Rules
  dataQuality: {
    rules: {
      completeness: {
        threshold: 0.95,
        critical: true,
      },
      accuracy: {
        threshold: 0.98,
        critical: true,
      },
      consistency: {
        threshold: 0.99,
        critical: false,
      },
      timeliness: {
        maxAge: 24 * 60 * 60 * 1000, // 24 hours
        critical: false,
      },
    },
  },

  // WebSocket Configuration
  websocket: {
    port: process.env.WS_PORT || 8080,
    pingInterval: 30000,
    pingTimeout: 5000,
    maxConnections: 1000,
    messageRateLimit: 100, // per second
  },

  // Security
  security: {
    encryption: {
      algorithm: 'aes-256-gcm',
      keyDerivation: 'pbkdf2',
      iterations: 100000,
    },
    authentication: {
      jwtSecret: process.env.JWT_SECRET,
      jwtExpiry: '24h',
      refreshTokenExpiry: '7d',
    },
    rateLimit: {
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 100, // requests per window
    },
  },

  // Logging
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    format: 'json',
    transports: ['console', 'file'],
    filePath: './logs/datacenter.log',
    maxSize: '100m',
    maxFiles: '14d',
  },

  // Performance
  performance: {
    caching: {
      ttl: 300, // 5 minutes default
      checkPeriod: 60, // Check for expired cache every 60 seconds
    },
    pagination: {
      defaultLimit: 100,
      maxLimit: 1000,
    },
    timeout: {
      api: 30000, // 30 seconds
      query: 120000, // 2 minutes
      job: 3600000, // 1 hour
    },
  },

  // Monitoring & Alerting
  monitoring: {
    healthCheck: {
      interval: 60000, // 1 minute
      timeout: 5000,
      endpoints: [
        { name: 'database', check: 'db_health' },
        { name: 'redis', check: 'redis_health' },
        { name: 'queue', check: 'queue_health' },
      ],
    },
    alerts: {
      enabled: true,
      channels: ['email', 'slack', 'webhook'],
      thresholds: {
        errorRate: 0.05, // 5% error rate
        responseTime: 5000, // 5 seconds
        queueSize: 10000, // max queue size
        diskUsage: 0.9, // 90% disk usage
      },
    },
  },

  // Data Governance
  governance: {
    retention: {
      defaultDays: 90,
      archiveAfterDays: 30,
      deleteAfterDays: 365,
    },
    compliance: {
      gdpr: true,
      hipaa: false,
      pci: false,
    },
    audit: {
      enabled: true,
      logAll: false,
      sensitiveFields: ['password', 'ssn', 'credit_card'],
    },
  },

  // Development & Testing
  development: {
    debug: process.env.NODE_ENV !== 'production',
    mockData: process.env.USE_MOCK_DATA === 'true',
    seedDatabase: process.env.SEED_DB === 'true',
    testMode: process.env.NODE_ENV === 'test',
  },
};

// Environment-specific overrides
if (process.env.NODE_ENV === 'production') {
  dataCenterConfig.queue.concurrency.data_import = 10;
  dataCenterConfig.queue.concurrency.data_export = 20;
  dataCenterConfig.logging.level = 'warn';
  dataCenterConfig.performance.caching.ttl = 600;
} else if (process.env.NODE_ENV === 'development') {
  dataCenterConfig.logging.level = 'debug';
  dataCenterConfig.development.debug = true;
}

export default dataCenterConfig;