// Data Center Backend Service - TypeScript
import express, { Router, Request, Response, NextFunction } from 'express';
import { WebSocketServer, WebSocket } from 'ws';
import multer, { Multer } from 'multer';
import Bull, { Queue, Job } from 'bull';
import Redis from 'ioredis';
import { Pool } from 'pg';
import { DataSourceManager } from './services/DataSourceManager';
import { JobProcessor } from './services/JobProcessor';
import { MetricsCollector } from './services/MetricsCollector';
import { DataQualityEngine } from './services/DataQualityEngine';
import { DataFlowOrchestrator } from './services/DataFlowOrchestrator';
import { QueryEngine } from './services/QueryEngine';
import { PythonExecutor } from './services/PythonExecutor';
import { RustProcessor } from './services/RustProcessor';
import { FileProcessor } from './services/FileProcessor';
import { DataCenterConfig } from './types/config';
import { DataSource, ProcessingJob, Metrics, DataFlow } from './types/models';
import { invoke } from '@tauri-apps/api/tauri';

export class DataCenterBackend {
  private app: Router;
  private redis: Redis;
  private db: Pool;
  private jobQueue: Queue<ProcessingJob>;
  private upload: Multer;
  private wss: WebSocketServer;
  
  // Services
  private dataSourceManager: DataSourceManager;
  private jobProcessor: JobProcessor;
  private metricsCollector: MetricsCollector;
  private dataQualityEngine: DataQualityEngine;
  private dataFlowOrchestrator: DataFlowOrchestrator;
  private queryEngine: QueryEngine;
  private pythonExecutor: PythonExecutor;
  private rustProcessor: RustProcessor;
  private fileProcessor: FileProcessor;
  
  private activeConnections: Map<string, WebSocket> = new Map();

  constructor(private config: DataCenterConfig) {
    this.app = express.Router();
    this.redis = new Redis(config.redis);
    this.db = new Pool(config.database);
    this.jobQueue = new Bull('data-processing', {
      redis: config.redis as any
    });
    this.upload = multer({ 
      dest: config.fileProcessing.uploadPath,
      limits: { fileSize: config.fileProcessing.maxFileSize }
    });
    
    this.initializeServices();
    this.setupRoutes();
    this.setupWebSocket();
    this.initializeRustBindings();
  }

  private initializeServices(): void {
    this.dataSourceManager = new DataSourceManager(this.db);
    this.jobProcessor = new JobProcessor(this.jobQueue, this.db);
    this.metricsCollector = new MetricsCollector(this.db, this.redis);
    this.dataQualityEngine = new DataQualityEngine(this.db);
    this.dataFlowOrchestrator = new DataFlowOrchestrator(this.db, this.jobQueue);
    this.queryEngine = new QueryEngine(this.db);
    this.pythonExecutor = new PythonExecutor(this.config.python);
    this.rustProcessor = new RustProcessor();
    this.fileProcessor = new FileProcessor(this.db, this.rustProcessor);
  }

  private async initializeRustBindings(): Promise<void> {
    try {
      // Initialize Rust data processing engine via Tauri
      await invoke('init_data_processor', {
        config: {
          maxThreads: navigator.hardwareConcurrency || 4,
          chunkSize: this.config.fileProcessing.chunkSize,
          enableSimd: true
        }
      });
    } catch (error) {
      console.error('Failed to initialize Rust processor:', error);
    }
  }

  private setupRoutes(): void {
    // Data Sources
    this.app.get('/sources', this.asyncHandler(this.handleGetSources));
    this.app.post('/sources', this.asyncHandler(this.handleCreateSource));
    this.app.put('/sources/:id', this.asyncHandler(this.handleUpdateSource));
    this.app.delete('/sources/:id', this.asyncHandler(this.handleDeleteSource));
    this.app.post('/sources/:id/test', this.asyncHandler(this.handleTestConnection));
    this.app.post('/sources/:id/sync', this.asyncHandler(this.handleSyncSource));

    // Processing Jobs
    this.app.get('/jobs', this.asyncHandler(this.handleGetJobs));
    this.app.post('/jobs', this.asyncHandler(this.handleCreateJob));
    this.app.get('/jobs/:id', this.asyncHandler(this.handleGetJob));
    this.app.post('/jobs/:id/cancel', this.asyncHandler(this.handleCancelJob));
    this.app.get('/jobs/:id/logs', this.asyncHandler(this.handleGetJobLogs));

    // Metrics
    this.app.get('/metrics/system', this.asyncHandler(this.handleGetSystemMetrics));
    this.app.get('/metrics/quality', this.asyncHandler(this.handleGetQualityMetrics));
    this.app.get('/metrics/performance', this.asyncHandler(this.handleGetPerformanceMetrics));

    // Data Flow
    this.app.get('/flow', this.asyncHandler(this.handleGetDataFlow));
    this.app.post('/flow', this.asyncHandler(this.handleCreateFlow));
    this.app.put('/flow/:id', this.asyncHandler(this.handleUpdateFlow));
    this.app.post('/flow/:id/execute', this.asyncHandler(this.handleExecuteFlow));

    // Query Execution
    this.app.post('/query', this.asyncHandler(this.handleExecuteQuery));
    this.app.get('/query/history', this.asyncHandler(this.handleGetQueryHistory));

    // File Upload - Process with Rust for performance
    this.app.post('/upload', 
      this.upload.single('file'), 
      this.asyncHandler(this.handleFileUpload)
    );
    
    // Python Execution for Data Science
    this.app.post('/python/execute', this.asyncHandler(this.handlePythonExecute));
    
    // Rust Processing for High Performance
    this.app.post('/rust/process', this.asyncHandler(this.handleRustProcess));
  }

  private setupWebSocket(): void {
    this.wss = new WebSocketServer({ noServer: true });
    
    this.wss.on('connection', (ws: WebSocket, req: any) => {
      const sessionId = this.extractSessionId(req.url);
      this.activeConnections.set(sessionId, ws);
      
      ws.on('message', async (message: Buffer) => {
        try {
          const data = JSON.parse(message.toString());
          await this.handleWebSocketMessage(ws, sessionId, data);
        } catch (error) {
          ws.send(JSON.stringify({ type: 'error', message: error.message }));
        }
      });

      ws.on('close', () => {
        this.activeConnections.delete(sessionId);
      });

      this.subscribeToUpdates(ws, sessionId);
    });
  }

  // Handler Methods with TypeScript types
  private handleGetSources = async (req: Request, res: Response): Promise<void> => {
    const sources = await this.dataSourceManager.getSources(req.query);
    res.json(sources);
  };

  private handleCreateSource = async (req: Request, res: Response): Promise<void> => {
    const source: DataSource = await this.dataSourceManager.createSource(req.body);
    res.status(201).json(source);
  };

  private handleUpdateSource = async (req: Request, res: Response): Promise<void> => {
    const source = await this.dataSourceManager.updateSource(req.params.id, req.body);
    res.json(source);
  };

  private handleDeleteSource = async (req: Request, res: Response): Promise<void> => {
    await this.dataSourceManager.deleteSource(req.params.id);
    res.status(204).send();
  };

  private handleTestConnection = async (req: Request, res: Response): Promise<void> => {
    const result = await this.dataSourceManager.testConnection(req.params.id);
    res.json(result);
  };

  private handleSyncSource = async (req: Request, res: Response): Promise<void> => {
    const job = await this.dataSourceManager.syncSource(req.params.id);
    res.json(job);
  };

  private handleCreateJob = async (req: Request, res: Response): Promise<void> => {
    const job: ProcessingJob = await this.jobProcessor.createJob(req.body);
    res.status(201).json(job);
  };

  private handleGetJob = async (req: Request, res: Response): Promise<void> => {
    const job = await this.jobProcessor.getJob(req.params.id);
    if (!job) {
      res.status(404).json({ error: 'Job not found' });
      return;
    }
    res.json(job);
  };

  private handleGetJobs = async (req: Request, res: Response): Promise<void> => {
    const jobs = await this.jobProcessor.getJobs(req.query);
    res.json(jobs);
  };

  private handleCancelJob = async (req: Request, res: Response): Promise<void> => {
    await this.jobProcessor.cancelJob(req.params.id);
    res.json({ status: 'cancelled' });
  };

  private handleGetJobLogs = async (req: Request, res: Response): Promise<void> => {
    const logs = await this.jobProcessor.getJobLogs(req.params.id);
    res.json(logs);
  };

  private handleGetSystemMetrics = async (req: Request, res: Response): Promise<void> => {
    const metrics: Metrics = await this.metricsCollector.getSystemMetrics();
    res.json(metrics);
  };

  private handleGetQualityMetrics = async (req: Request, res: Response): Promise<void> => {
    const metrics = await this.dataQualityEngine.getMetrics(req.query);
    res.json(metrics);
  };

  private handleGetPerformanceMetrics = async (req: Request, res: Response): Promise<void> => {
    const metrics = await this.metricsCollector.getPerformanceMetrics();
    res.json(metrics);
  };

  private handleGetDataFlow = async (req: Request, res: Response): Promise<void> => {
    const flow = await this.dataFlowOrchestrator.getFlow(req.query);
    res.json(flow);
  };

  private handleCreateFlow = async (req: Request, res: Response): Promise<void> => {
    const flow: DataFlow = await this.dataFlowOrchestrator.createFlow(req.body);
    res.status(201).json(flow);
  };

  private handleUpdateFlow = async (req: Request, res: Response): Promise<void> => {
    const flow = await this.dataFlowOrchestrator.updateFlow(req.params.id, req.body);
    res.json(flow);
  };

  private handleExecuteFlow = async (req: Request, res: Response): Promise<void> => {
    const execution = await this.dataFlowOrchestrator.executeFlow(req.params.id);
    res.json(execution);
  };

  private handleExecuteQuery = async (req: Request, res: Response): Promise<void> => {
    const result = await this.queryEngine.execute(req.body.query, req.body.params);
    res.json(result);
  };

  private handleGetQueryHistory = async (req: Request, res: Response): Promise<void> => {
    const history = await this.queryEngine.getHistory(req.query);
    res.json(history);
  };

  private handleFileUpload = async (req: Request, res: Response): Promise<void> => {
    if (!req.file) {
      res.status(400).json({ error: 'No file uploaded' });
      return;
    }

    // Use Rust for high-performance file processing
    const result = await this.rustProcessor.processFile(req.file.path, {
      format: req.body.format || 'auto',
      chunkSize: parseInt(req.body.chunkSize) || 10000,
      parallel: req.body.parallel !== 'false'
    });

    const job = await this.jobProcessor.createJob({
      type: 'file_import',
      payload: { ...result, originalName: req.file.originalname },
      userId: req.body.userId
    });

    res.json({ jobId: job.id, ...result });
  };

  private handlePythonExecute = async (req: Request, res: Response): Promise<void> => {
    const { code, context, requirements } = req.body;
    
    // Install requirements if needed
    if (requirements && requirements.length > 0) {
      await this.pythonExecutor.installPackages(requirements);
    }

    const result = await this.pythonExecutor.execute(code, context);
    res.json(result);
  };

  private handleRustProcess = async (req: Request, res: Response): Promise<void> => {
    const { operation, data, options } = req.body;
    
    // Use Tauri to invoke Rust functions for heavy processing
    const result = await invoke(`process_${operation}`, {
      data,
      options: options || {}
    });
    
    res.json(result);
  };

  // WebSocket message handler
  private async handleWebSocketMessage(
    ws: WebSocket, 
    sessionId: string, 
    data: any
  ): Promise<void> {
    switch (data.type) {
      case 'subscribe':
        await this.addSubscription(sessionId, data.channel, data.filters);
        break;
      case 'unsubscribe':
        await this.removeSubscription(sessionId, data.channel);
        break;
      case 'execute':
        await this.executeRealtimeQuery(ws, data.query);
        break;
      case 'rust_stream':
        await this.startRustStream(ws, data.operation, data.params);
        break;
      case 'python_notebook':
        await this.executePythonNotebook(ws, data.cells);
        break;
    }
  }

  private async startRustStream(
    ws: WebSocket, 
    operation: string, 
    params: any
  ): Promise<void> {
    // Stream data processing from Rust
    const stream = await this.rustProcessor.createStream(operation, params);
    
    stream.on('data', (chunk: any) => {
      ws.send(JSON.stringify({
        type: 'stream_data',
        operation,
        data: chunk
      }));
    });

    stream.on('end', () => {
      ws.send(JSON.stringify({
        type: 'stream_end',
        operation
      }));
    });
  }

  private async executePythonNotebook(
    ws: WebSocket, 
    cells: any[]
  ): Promise<void> {
    for (const cell of cells) {
      const result = await this.pythonExecutor.executeCell(cell);
      ws.send(JSON.stringify({
        type: 'notebook_output',
        cellId: cell.id,
        output: result
      }));
    }
  }

  private subscribeToUpdates(ws: WebSocket, sessionId: string): void {
    // Job progress updates
    this.jobProcessor.on('job:progress', (event: any) => {
      ws.send(JSON.stringify({
        type: 'job_progress',
        ...event
      }));
    });

    // Real-time metrics
    const metricsInterval = setInterval(async () => {
      const metrics = await this.metricsCollector.getRealtimeMetrics();
      ws.send(JSON.stringify({
        type: 'metrics_update',
        metrics
      }));
    }, 5000);

    ws.on('close', () => {
      clearInterval(metricsInterval);
    });
  }

  // Utility methods
  private asyncHandler(fn: Function) {
    return (req: Request, res: Response, next: NextFunction) => {
      Promise.resolve(fn.call(this, req, res, next)).catch(next);
    };
  }

  private extractSessionId(url: string): string {
    return url.split('/').pop() || 'default';
  }

  private async addSubscription(
    sessionId: string, 
    channel: string, 
    filters: any
  ): Promise<void> {
    await this.redis.set(
      `sub:${sessionId}:${channel}`, 
      JSON.stringify(filters)
    );
  }

  private async removeSubscription(
    sessionId: string, 
    channel: string
  ): Promise<void> {
    await this.redis.del(`sub:${sessionId}:${channel}`);
  }

  private async executeRealtimeQuery(ws: WebSocket, query: string): Promise<void> {
    const result = await this.queryEngine.executeStreaming(query);
    ws.send(JSON.stringify({ type: 'query_result', result }));
  }

  public getRouter(): Router {
    return this.app;
  }

  public getWebSocketServer(): WebSocketServer {
    return this.wss;
  }

  public async shutdown(): Promise<void> {
    await this.jobQueue.close();
    await this.db.end();
    await this.redis.quit();
    this.wss.close();
  }
}

export default DataCenterBackend;