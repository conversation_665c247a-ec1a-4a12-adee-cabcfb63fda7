import { Router } from 'express';
import { CompetencyController } from '../controllers/CompetencyController';
import { validateCompetencyModel, validateSkillMatrix } from '../middleware/validation';
import { authenticateUser } from '../middleware/auth';

const router = Router();
const competencyController = new CompetencyController();

// Competency Models Routes
router.get('/models', authenticateUser, competencyController.getAllModels);
router.get('/models/:id', authenticateUser, competencyController.getModelById);
router.post('/models', authenticateUser, validateCompetencyModel, competencyController.createModel);
router.put('/models/:id', authenticateUser, validateCompetencyModel, competencyController.updateModel);
router.delete('/models/:id', authenticateUser, competencyController.deleteModel);

// Skills Matrix Routes
router.get('/matrices', authenticateUser, competencyController.getAllMatrices);
router.get('/matrices/:id', authenticateUser, competencyController.getMatrixById);
router.post('/matrices', authenticateUser, validateSkillMatrix, competencyController.createMatrix);
router.put('/matrices/:id', authenticateUser, validateSkillMatrix, competencyController.updateMatrix);
router.delete('/matrices/:id', authenticateUser, competencyController.deleteMatrix);

// Competency Assessment Routes
router.post('/models/:id/assess', authenticateUser, competencyController.assessCompetency);
router.get('/models/:id/gaps', authenticateUser, competencyController.getSkillsGaps);
router.get('/models/:id/analytics', authenticateUser, competencyController.getCompetencyAnalytics);

// Import/Export Routes
router.post('/models/import', authenticateUser, competencyController.importModel);
router.get('/models/:id/export', authenticateUser, competencyController.exportModel);
router.post('/matrices/import', authenticateUser, competencyController.importMatrix);
router.get('/matrices/:id/export', authenticateUser, competencyController.exportMatrix);

export default router;