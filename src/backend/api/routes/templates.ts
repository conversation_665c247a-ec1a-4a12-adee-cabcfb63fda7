import { Router } from 'express';
import { TemplateController } from '../controllers/TemplateController';
import { requireAuth, requireWritePermission, requireDeletePermission, uploadRateLimit } from '../middleware/auth';
import { validateTemplate, validatePagination, validateId, validateFileUpload } from '../middleware/validation';
import multer from 'multer';

const router = Router();
const templateController = new TemplateController();

// Configure multer for file uploads
const upload = multer({
  dest: 'uploads/templates/',
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  }
});

// Template CRUD routes
router.get('/', 
  validatePagination,
  templateController.getAllTemplates
);

router.get('/:id', 
  validateId,
  templateController.getTemplateById
);

router.post('/', 
  requireWritePermission,
  validateTemplate,
  templateController.createTemplate
);

router.put('/:id', 
  requireWritePermission,
  validateId,
  validateTemplate,
  templateController.updateTemplate
);

router.delete('/:id', 
  requireDeletePermission,
  validateId,
  templateController.deleteTemplate
);

// Template search and filtering
router.get('/search/:query', 
  templateController.searchTemplates
);

router.get('/category/:category', 
  validatePagination,
  templateController.getTemplatesByCategory
);

router.get('/popular/top', 
  templateController.getPopularTemplates
);

router.get('/recent/latest', 
  validatePagination,
  templateController.getRecentTemplates
);

// Template usage and analytics
router.post('/:id/use', 
  requireAuth,
  validateId,
  templateController.recordTemplateUsage
);

router.get('/:id/analytics', 
  requireAuth,
  validateId,
  templateController.getTemplateAnalytics
);

router.get('/:id/versions', 
  validateId,
  templateController.getTemplateVersions
);

// Template file operations
router.post('/upload', 
  requireWritePermission,
  uploadRateLimit,
  upload.single('template'),
  validateFileUpload(['application/json', 'application/zip', 'text/plain'], 10 * 1024 * 1024),
  templateController.uploadTemplate
);

router.get('/:id/download', 
  requireAuth,
  validateId,
  templateController.downloadTemplate
);

router.post('/:id/export', 
  requireAuth,
  validateId,
  templateController.exportTemplate
);

// Template collaboration
router.post('/:id/share', 
  requireWritePermission,
  validateId,
  templateController.shareTemplate
);

router.post('/:id/clone', 
  requireWritePermission,
  validateId,
  templateController.cloneTemplate
);

router.get('/:id/comments', 
  validateId,
  templateController.getTemplateComments
);

router.post('/:id/comments', 
  requireAuth,
  validateId,
  templateController.addTemplateComment
);

// Template ratings and reviews
router.post('/:id/rate', 
  requireAuth,
  validateId,
  templateController.rateTemplate
);

router.get('/:id/ratings', 
  validateId,
  templateController.getTemplateRatings
);

export default router;