import { Request, Response, NextFunction } from 'express';
import { ApiResponse } from '../../types/api';

export interface ValidationRule {
  field: string;
  required?: boolean;
  type?: 'string' | 'number' | 'boolean' | 'array' | 'object';
  minLength?: number;
  maxLength?: number;
  min?: number;
  max?: number;
  pattern?: RegExp;
  custom?: (value: any) => boolean | string;
}

export interface ValidationError {
  field: string;
  message: string;
  value?: any;
}

export class ValidationMiddleware {
  static validate(rules: ValidationRule[]) {
    return (req: Request, res: Response, next: NextFunction) => {
      const errors: ValidationError[] = [];
      const data = { ...req.body, ...req.query, ...req.params };

      for (const rule of rules) {
        const value = data[rule.field];
        const fieldErrors = ValidationMiddleware.validateField(rule, value);
        errors.push(...fieldErrors);
      }

      if (errors.length > 0) {
        const response: ApiResponse<null> = {
          success: false,
          error: 'Validation failed',
          details: JSON.stringify(errors)
        };
        return res.status(400).json(response);
      }

      next();
    };
  }

  private static validateField(rule: ValidationRule, value: any): ValidationError[] {
    const errors: ValidationError[] = [];
    const { field, required, type, minLength, maxLength, min, max, pattern, custom } = rule;

    // Check if field is required
    if (required && (value === undefined || value === null || value === '')) {
      errors.push({
        field,
        message: `${field} is required`,
        value
      });
      return errors;
    }

    // Skip further validation if field is not provided and not required
    if (value === undefined || value === null) {
      return errors;
    }

    // Type validation
    if (type) {
      const actualType = Array.isArray(value) ? 'array' : typeof value;
      if (actualType !== type) {
        errors.push({
          field,
          message: `${field} must be of type ${type}`,
          value
        });
        return errors;
      }
    }

    // String validations
    if (typeof value === 'string') {
      if (minLength !== undefined && value.length < minLength) {
        errors.push({
          field,
          message: `${field} must be at least ${minLength} characters long`,
          value
        });
      }

      if (maxLength !== undefined && value.length > maxLength) {
        errors.push({
          field,
          message: `${field} must be no more than ${maxLength} characters long`,
          value
        });
      }

      if (pattern && !pattern.test(value)) {
        errors.push({
          field,
          message: `${field} format is invalid`,
          value
        });
      }
    }

    // Number validations
    if (typeof value === 'number') {
      if (min !== undefined && value < min) {
        errors.push({
          field,
          message: `${field} must be at least ${min}`,
          value
        });
      }

      if (max !== undefined && value > max) {
        errors.push({
          field,
          message: `${field} must be no more than ${max}`,
          value
        });
      }
    }

    // Custom validation
    if (custom) {
      const customResult = custom(value);
      if (customResult !== true) {
        errors.push({
          field,
          message: typeof customResult === 'string' ? customResult : `${field} is invalid`,
          value
        });
      }
    }

    return errors;
  }

  // Predefined validation rules
  static competencyModelRules: ValidationRule[] = [
    { field: 'name', required: true, type: 'string', minLength: 2, maxLength: 100 },
    { field: 'description', required: true, type: 'string', minLength: 10, maxLength: 500 },
    { field: 'category', required: true, type: 'string', minLength: 2, maxLength: 50 },
    { field: 'competencies', required: true, type: 'array' },
    { field: 'version', required: true, type: 'string', pattern: /^\d+\.\d+$/ },
    { field: 'status', required: true, type: 'string', custom: (value) => ['draft', 'published', 'archived'].includes(value) },
    { field: 'tags', required: false, type: 'array' }
  ];

  static skillMatrixRules: ValidationRule[] = [
    { field: 'name', required: true, type: 'string', minLength: 2, maxLength: 100 },
    { field: 'description', required: true, type: 'string', minLength: 10, maxLength: 500 },
    { field: 'roles', required: true, type: 'array' },
    { field: 'skills', required: true, type: 'array' }
  ];

  static assessmentRules: ValidationRule[] = [
    { field: 'userId', required: true, type: 'string', minLength: 1 },
    { field: 'competencyModelId', required: true, type: 'string', minLength: 1 },
    { field: 'assessmentType', required: true, type: 'string', custom: (value) => ['self', 'manager', '360', 'peer'].includes(value) },
    { field: 'responses', required: true, type: 'array' }
  ];

  static paginationRules: ValidationRule[] = [
    { field: 'page', required: false, type: 'number', min: 1 },
    { field: 'limit', required: false, type: 'number', min: 1, max: 100 },
    { field: 'search', required: false, type: 'string', maxLength: 200 }
  ];

  static idRules: ValidationRule[] = [
    { field: 'id', required: true, type: 'string', minLength: 1, maxLength: 50 }
  ];

  static templateRules: ValidationRule[] = [
    { field: 'name', required: true, type: 'string', minLength: 2, maxLength: 100 },
    { field: 'description', required: true, type: 'string', minLength: 10, maxLength: 500 },
    { field: 'category', required: true, type: 'string', minLength: 2, maxLength: 50 },
    { field: 'format', required: true, type: 'string', custom: (value) => ['document', 'presentation', 'spreadsheet', 'video', 'interactive'].includes(value) },
    { field: 'tags', required: false, type: 'array' },
    { field: 'content', required: true, type: 'string', minLength: 1 }
  ];
}

// Convenience functions for common validations
export const validateCompetencyModel = ValidationMiddleware.validate(ValidationMiddleware.competencyModelRules);
export const validateSkillMatrix = ValidationMiddleware.validate(ValidationMiddleware.skillMatrixRules);
export const validateAssessment = ValidationMiddleware.validate(ValidationMiddleware.assessmentRules);
export const validatePagination = ValidationMiddleware.validate(ValidationMiddleware.paginationRules);
export const validateId = ValidationMiddleware.validate(ValidationMiddleware.idRules);
export const validateTemplate = ValidationMiddleware.validate(ValidationMiddleware.templateRules);

// File upload validation
export const validateFileUpload = (allowedTypes: string[], maxSize: number = 5 * 1024 * 1024) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const file = req.file;
    
    if (!file) {
      return res.status(400).json({
        success: false,
        error: 'No file uploaded'
      });
    }

    if (!allowedTypes.includes(file.mimetype)) {
      return res.status(400).json({
        success: false,
        error: `File type not allowed. Allowed types: ${allowedTypes.join(', ')}`
      });
    }

    if (file.size > maxSize) {
      return res.status(400).json({
        success: false,
        error: `File size too large. Maximum size: ${maxSize / (1024 * 1024)}MB`
      });
    }

    next();
  };
};