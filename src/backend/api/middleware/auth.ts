import { Request, Response, NextFunction } from 'express';
import { ApiResponse } from '../../types/api';

// Extend Express Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email: string;
        role: string;
        permissions: string[];
      };
    }
  }
}

export interface AuthUser {
  id: string;
  email: string;
  role: string;
  permissions: string[];
}

export class AuthMiddleware {
  // Mock authentication - in a real app, this would validate JWT tokens
  static authenticate = (req: Request, res: Response, next: NextFunction) => {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      const response: ApiResponse<null> = {
        success: false,
        error: 'Authentication required',
        details: 'Please provide a valid Bearer token'
      };
      return res.status(401).json(response);
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix
    
    // Mock token validation - in a real app, this would verify JWT
    if (token === 'mock-valid-token') {
      req.user = {
        id: 'user-123',
        email: '<EMAIL>',
        role: 'admin',
        permissions: ['read', 'write', 'delete', 'admin']
      };
      next();
    } else {
      const response: ApiResponse<null> = {
        success: false,
        error: 'Invalid token',
        details: 'The provided token is invalid or expired'
      };
      return res.status(401).json(response);
    }
  };

  // Authorization middleware - check if user has required permissions
  static authorize = (requiredPermissions: string[]) => {
    return (req: Request, res: Response, next: NextFunction) => {
      if (!req.user) {
        const response: ApiResponse<null> = {
          success: false,
          error: 'Authentication required'
        };
        return res.status(401).json(response);
      }

      const hasPermission = requiredPermissions.every(permission => 
        req.user!.permissions.includes(permission)
      );

      if (!hasPermission) {
        const response: ApiResponse<null> = {
          success: false,
          error: 'Insufficient permissions',
          details: `Required permissions: ${requiredPermissions.join(', ')}`
        };
        return res.status(403).json(response);
      }

      next();
    };
  };

  // Role-based authorization
  static requireRole = (allowedRoles: string[]) => {
    return (req: Request, res: Response, next: NextFunction) => {
      if (!req.user) {
        const response: ApiResponse<null> = {
          success: false,
          error: 'Authentication required'
        };
        return res.status(401).json(response);
      }

      if (!allowedRoles.includes(req.user.role)) {
        const response: ApiResponse<null> = {
          success: false,
          error: 'Access denied',
          details: `Required roles: ${allowedRoles.join(', ')}`
        };
        return res.status(403).json(response);
      }

      next();
    };
  };

  // Optional authentication - doesn't fail if no token provided
  static optionalAuth = (req: Request, res: Response, next: NextFunction) => {
    const authHeader = req.headers.authorization;
    
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      
      // Mock token validation
      if (token === 'mock-valid-token') {
        req.user = {
          id: 'user-123',
          email: '<EMAIL>',
          role: 'admin',
          permissions: ['read', 'write', 'delete', 'admin']
        };
      }
    }
    
    next();
  };

  // Rate limiting middleware
  static rateLimit = (maxRequests: number, windowMs: number) => {
    const requests = new Map<string, { count: number; resetTime: number }>();
    
    return (req: Request, res: Response, next: NextFunction) => {
      const clientId = req.ip || 'unknown';
      const now = Date.now();
      
      const clientData = requests.get(clientId);
      
      if (!clientData || now > clientData.resetTime) {
        requests.set(clientId, {
          count: 1,
          resetTime: now + windowMs
        });
        next();
      } else if (clientData.count < maxRequests) {
        clientData.count++;
        next();
      } else {
        const response: ApiResponse<null> = {
          success: false,
          error: 'Rate limit exceeded',
          details: `Maximum ${maxRequests} requests per ${windowMs / 1000} seconds`
        };
        res.status(429).json(response);
      }
    };
  };

  // API key authentication (alternative to JWT)
  static apiKeyAuth = (req: Request, res: Response, next: NextFunction) => {
    const apiKey = req.headers['x-api-key'] as string;
    
    if (!apiKey) {
      const response: ApiResponse<null> = {
        success: false,
        error: 'API key required',
        details: 'Please provide a valid API key in the x-api-key header'
      };
      return res.status(401).json(response);
    }

    // Mock API key validation
    if (apiKey === 'mock-api-key-123') {
      req.user = {
        id: 'api-user',
        email: '<EMAIL>',
        role: 'api',
        permissions: ['read', 'write']
      };
      next();
    } else {
      const response: ApiResponse<null> = {
        success: false,
        error: 'Invalid API key'
      };
      return res.status(401).json(response);
    }
  };
}

// Convenience exports for common permission combinations
export const requireAuth = AuthMiddleware.authenticate;
export const requireAdmin = [AuthMiddleware.authenticate, AuthMiddleware.requireRole(['admin'])];
export const requireReadPermission = [AuthMiddleware.authenticate, AuthMiddleware.authorize(['read'])];
export const requireWritePermission = [AuthMiddleware.authenticate, AuthMiddleware.authorize(['write'])];
export const requireDeletePermission = [AuthMiddleware.authenticate, AuthMiddleware.authorize(['delete'])];

// Rate limiting presets
export const standardRateLimit = AuthMiddleware.rateLimit(100, 60 * 1000); // 100 requests per minute
export const strictRateLimit = AuthMiddleware.rateLimit(10, 60 * 1000); // 10 requests per minute
export const uploadRateLimit = AuthMiddleware.rateLimit(5, 60 * 1000); // 5 uploads per minute