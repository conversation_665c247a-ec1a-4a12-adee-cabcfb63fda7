import { Template, TemplateComment, TemplateRating, TemplateVersion, TemplateUsageRecord, TemplateExportOptions, TemplateSearchFilters } from '../../types/template';
import { PaginatedResponse, PaginationInfo } from '../../types/api';

export class TemplateService {
  private templates: Template[] = [];
  private comments: TemplateComment[] = [];
  private ratings: TemplateRating[] = [];
  private versions: TemplateVersion[] = [];
  private usageRecords: TemplateUsageRecord[] = [];

  constructor() {
    this.initializeMockData();
  }

  private initializeMockData(): void {
    // Initialize with some mock templates
    this.templates = [
      {
        id: '1',
        name: 'Leadership Development Program',
        description: 'Comprehensive leadership training template',
        category: 'Leadership',
        format: 'presentation',
        tags: ['leadership', 'management', 'development'],
        content: {
          type: 'html',
          data: '<h1>Leadership Development Program</h1><p>Content here...</p>',
          thumbnailUrl: '/thumbnails/leadership.jpg'
        },
        metadata: {
          title: 'Leadership Development Program',
          author: '<PERSON>',
          duration: 120,
          difficulty: 'intermediate',
          learningObjectives: ['Develop leadership skills', 'Improve team management'],
          prerequisites: ['Basic management experience'],
          targetAudience: ['Managers', 'Team leads'],
          language: 'English',
          fileSize: 2048000,
          lastModified: new Date()
        },
        permissions: {
          isPublic: true,
          allowDownload: true,
          allowClone: true,
          allowComment: true,
          allowRating: true,
          sharedWith: [],
          createdBy: 'user1'
        },
        analytics: {
          views: 150,
          downloads: 45,
          clones: 12,
          shares: 8,
          averageRating: 4.5,
          totalRatings: 23,
          usageByMonth: [
            { month: '2024-01', views: 50, downloads: 15, clones: 4 },
            { month: '2024-02', views: 100, downloads: 30, clones: 8 }
          ],
          popularityScore: 85,
          engagementRate: 0.3
        },
        createdAt: new Date('2024-01-15'),
        updatedAt: new Date('2024-02-01'),
        createdBy: 'user1',
        version: '1.2',
        isPublic: true,
        downloadCount: 45,
        rating: 4.5,
        ratingCount: 23
      },
      {
        id: '2',
        name: 'Technical Skills Assessment',
        description: 'Template for evaluating technical competencies',
        category: 'Assessment',
        format: 'spreadsheet',
        tags: ['assessment', 'technical', 'skills'],
        content: {
          type: 'json',
          data: { sections: ['Programming', 'System Design', 'Problem Solving'] },
          thumbnailUrl: '/thumbnails/assessment.jpg'
        },
        metadata: {
          title: 'Technical Skills Assessment',
          author: 'Jane Smith',
          duration: 60,
          difficulty: 'advanced',
          learningObjectives: ['Assess technical skills', 'Identify skill gaps'],
          prerequisites: ['Technical background'],
          targetAudience: ['Developers', 'Technical leads'],
          language: 'English',
          fileSize: 1024000,
          lastModified: new Date()
        },
        permissions: {
          isPublic: true,
          allowDownload: true,
          allowClone: true,
          allowComment: true,
          allowRating: true,
          sharedWith: [],
          createdBy: 'user2'
        },
        analytics: {
          views: 89,
          downloads: 32,
          clones: 7,
          shares: 5,
          averageRating: 4.2,
          totalRatings: 18,
          usageByMonth: [
            { month: '2024-01', views: 40, downloads: 12, clones: 3 },
            { month: '2024-02', views: 49, downloads: 20, clones: 4 }
          ],
          popularityScore: 72,
          engagementRate: 0.36
        },
        createdAt: new Date('2024-01-20'),
        updatedAt: new Date('2024-01-25'),
        createdBy: 'user2',
        version: '1.0',
        isPublic: true,
        downloadCount: 32,
        rating: 4.2,
        ratingCount: 18
      }
    ];
  }

  // Template CRUD operations
  async getAllTemplates(options: {
    page: number;
    limit: number;
    category?: string;
    search?: string;
    format?: string;
  }): Promise<PaginatedResponse<Template>> {
    let filteredTemplates = [...this.templates];

    // Apply filters
    if (options.category) {
      filteredTemplates = filteredTemplates.filter(t => t.category === options.category);
    }
    if (options.format) {
      filteredTemplates = filteredTemplates.filter(t => t.format === options.format);
    }
    if (options.search) {
      const searchLower = options.search.toLowerCase();
      filteredTemplates = filteredTemplates.filter(t => 
        t.name.toLowerCase().includes(searchLower) ||
        t.description.toLowerCase().includes(searchLower) ||
        t.tags.some(tag => tag.toLowerCase().includes(searchLower))
      );
    }

    // Pagination
    const startIndex = (options.page - 1) * options.limit;
    const endIndex = startIndex + options.limit;
    const paginatedTemplates = filteredTemplates.slice(startIndex, endIndex);

    const pagination: PaginationInfo = {
      page: options.page,
      limit: options.limit,
      total: filteredTemplates.length,
      totalPages: Math.ceil(filteredTemplates.length / options.limit),
      hasNext: options.page < Math.ceil(filteredTemplates.length / options.limit),
      hasPrev: options.page > 1
    };

    return {
      data: paginatedTemplates,
      pagination
    };
  }

  async getTemplateById(id: string): Promise<Template | null> {
    return this.templates.find(t => t.id === id) || null;
  }

  async createTemplate(templateData: Omit<Template, 'id' | 'createdAt' | 'updatedAt'>): Promise<Template> {
    const newTemplate: Template = {
      ...templateData,
      id: `template_${Date.now()}`,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.templates.push(newTemplate);
    return newTemplate;
  }

  async updateTemplate(id: string, updateData: Partial<Template>): Promise<Template | null> {
    const templateIndex = this.templates.findIndex(t => t.id === id);
    if (templateIndex === -1) return null;

    this.templates[templateIndex] = {
      ...this.templates[templateIndex],
      ...updateData,
      updatedAt: new Date()
    };

    return this.templates[templateIndex];
  }

  async deleteTemplate(id: string): Promise<boolean> {
    const templateIndex = this.templates.findIndex(t => t.id === id);
    if (templateIndex === -1) return false;

    this.templates.splice(templateIndex, 1);
    // Also remove related data
    this.comments = this.comments.filter(c => c.templateId !== id);
    this.ratings = this.ratings.filter(r => r.templateId !== id);
    this.versions = this.versions.filter(v => v.templateId !== id);
    this.usageRecords = this.usageRecords.filter(u => u.templateId !== id);

    return true;
  }

  // Search and filtering
  async searchTemplates(query: string, options: {
    page: number;
    limit: number;
    category?: string;
    format?: string;
  }): Promise<PaginatedResponse<Template>> {
    return this.getAllTemplates({ ...options, search: query });
  }

  async getTemplatesByCategory(category: string, options: {
    page: number;
    limit: number;
  }): Promise<PaginatedResponse<Template>> {
    return this.getAllTemplates({ ...options, category });
  }

  async getPopularTemplates(limit: number): Promise<Template[]> {
    return this.templates
      .sort((a, b) => b.analytics.popularityScore - a.analytics.popularityScore)
      .slice(0, limit);
  }

  async getRecentTemplates(options: {
    page: number;
    limit: number;
  }): Promise<PaginatedResponse<Template>> {
    const sortedTemplates = this.templates
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

    const startIndex = (options.page - 1) * options.limit;
    const endIndex = startIndex + options.limit;
    const paginatedTemplates = sortedTemplates.slice(startIndex, endIndex);

    const pagination: PaginationInfo = {
      page: options.page,
      limit: options.limit,
      total: sortedTemplates.length,
      totalPages: Math.ceil(sortedTemplates.length / options.limit),
      hasNext: options.page < Math.ceil(sortedTemplates.length / options.limit),
      hasPrev: options.page > 1
    };

    return {
      data: paginatedTemplates,
      pagination
    };
  }

  // Usage and analytics
  async recordTemplateUsage(templateId: string, userId: string): Promise<void> {
    const template = this.templates.find(t => t.id === templateId);
    if (!template) throw new Error('Template not found');

    // Record usage
    const usageRecord: TemplateUsageRecord = {
      id: `usage_${Date.now()}`,
      templateId,
      userId,
      action: 'view',
      timestamp: new Date()
    };
    this.usageRecords.push(usageRecord);

    // Update analytics
    template.analytics.views += 1;
    template.updatedAt = new Date();
  }

  async getTemplateAnalytics(templateId: string): Promise<any> {
    const template = this.templates.find(t => t.id === templateId);
    if (!template) throw new Error('Template not found');

    const usageRecords = this.usageRecords.filter(u => u.templateId === templateId);
    
    return {
      ...template.analytics,
      recentUsage: usageRecords.slice(-10),
      usageByDay: this.calculateDailyUsage(usageRecords),
      topUsers: this.getTopUsers(usageRecords)
    };
  }

  async getTemplateVersions(templateId: string): Promise<TemplateVersion[]> {
    return this.versions.filter(v => v.templateId === templateId)
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }

  // File operations
  async uploadTemplate(file: any): Promise<any> {
    // Mock file upload processing
    const templateId = `template_${Date.now()}`;
    
    // Simulate file processing
    const processedTemplate: Template = {
      id: templateId,
      name: file.originalname || 'Uploaded Template',
      description: 'Template uploaded from file',
      category: 'Uploaded',
      format: this.getFormatFromFile(file),
      tags: ['uploaded'],
      content: {
        type: 'binary',
        data: 'File content would be processed here',
        fileUrl: `/uploads/${templateId}/${file.originalname}`
      },
      metadata: {
        title: file.originalname || 'Uploaded Template',
        author: 'Unknown',
        difficulty: 'beginner',
        learningObjectives: [],
        prerequisites: [],
        targetAudience: [],
        language: 'English',
        fileSize: file.size || 0,
        lastModified: new Date()
      },
      permissions: {
        isPublic: false,
        allowDownload: true,
        allowClone: true,
        allowComment: true,
        allowRating: true,
        sharedWith: [],
        createdBy: 'current_user'
      },
      analytics: {
        views: 0,
        downloads: 0,
        clones: 0,
        shares: 0,
        averageRating: 0,
        totalRatings: 0,
        usageByMonth: [],
        popularityScore: 0,
        engagementRate: 0
      },
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: 'current_user',
      version: '1.0',
      isPublic: false,
      downloadCount: 0,
      rating: 0,
      ratingCount: 0
    };

    this.templates.push(processedTemplate);
    
    return {
      templateId,
      fileName: file.originalname,
      fileSize: file.size,
      uploadedAt: new Date(),
      status: 'success'
    };
  }

  async downloadTemplate(templateId: string, format: string): Promise<Buffer> {
    const template = this.templates.find(t => t.id === templateId);
    if (!template) throw new Error('Template not found');

    // Mock file generation based on format
    let content: string;
    switch (format) {
      case 'json':
        content = JSON.stringify(template, null, 2);
        break;
      case 'pdf':
        content = `PDF content for ${template.name}`;
        break;
      default:
        content = JSON.stringify(template, null, 2);
    }

    // Update download count
    template.analytics.downloads += 1;
    template.downloadCount += 1;

    return Buffer.from(content, 'utf-8');
  }

  async exportTemplate(templateId: string, options: TemplateExportOptions): Promise<any> {
    const template = this.templates.find(t => t.id === templateId);
    if (!template) throw new Error('Template not found');

    const exportData: any = { ...template };

    if (options.includeAnalytics) {
      exportData.detailedAnalytics = await this.getTemplateAnalytics(templateId);
    }

    if (options.includeComments) {
      exportData.comments = this.comments.filter(c => c.templateId === templateId);
    }

    if (options.includeRatings) {
      exportData.ratings = this.ratings.filter(r => r.templateId === templateId);
    }

    return {
      template: exportData,
      exportedAt: new Date(),
      format: options.format
    };
  }

  // Collaboration
  async shareTemplate(templateId: string, userIds: string[], permissions: string): Promise<any> {
    const template = this.templates.find(t => t.id === templateId);
    if (!template) throw new Error('Template not found');

    // Add shared users
    const sharedUsers = userIds.map(userId => ({
      userId,
      permission: permissions as 'view' | 'edit' | 'admin',
      sharedAt: new Date(),
      sharedBy: 'current_user'
    }));

    template.permissions.sharedWith.push(...sharedUsers);
    template.analytics.shares += userIds.length;

    return {
      templateId,
      sharedWith: userIds,
      permissions,
      sharedAt: new Date()
    };
  }

  async cloneTemplate(templateId: string, userId: string): Promise<Template> {
    const originalTemplate = this.templates.find(t => t.id === templateId);
    if (!originalTemplate) throw new Error('Template not found');

    const clonedTemplate: Template = {
      ...originalTemplate,
      id: `template_${Date.now()}`,
      name: `${originalTemplate.name} (Copy)`,
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: userId,
      analytics: {
        views: 0,
        downloads: 0,
        clones: 0,
        shares: 0,
        averageRating: 0,
        totalRatings: 0,
        usageByMonth: [],
        popularityScore: 0,
        engagementRate: 0
      },
      downloadCount: 0,
      rating: 0,
      ratingCount: 0
    };

    this.templates.push(clonedTemplate);
    
    // Update original template clone count
    originalTemplate.analytics.clones += 1;

    return clonedTemplate;
  }

  // Comments
  async getTemplateComments(templateId: string, options: {
    page: number;
    limit: number;
  }): Promise<PaginatedResponse<TemplateComment>> {
    const templateComments = this.comments.filter(c => c.templateId === templateId)
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

    const startIndex = (options.page - 1) * options.limit;
    const endIndex = startIndex + options.limit;
    const paginatedComments = templateComments.slice(startIndex, endIndex);

    const pagination: PaginationInfo = {
      page: options.page,
      limit: options.limit,
      total: templateComments.length,
      totalPages: Math.ceil(templateComments.length / options.limit),
      hasNext: options.page < Math.ceil(templateComments.length / options.limit),
      hasPrev: options.page > 1
    };

    return {
      data: paginatedComments,
      pagination
    };
  }

  async addTemplateComment(templateId: string, userId: string, content: string): Promise<TemplateComment> {
    const template = this.templates.find(t => t.id === templateId);
    if (!template) throw new Error('Template not found');

    const comment: TemplateComment = {
      id: `comment_${Date.now()}`,
      templateId,
      userId,
      userName: `User ${userId}`,
      content,
      createdAt: new Date(),
      updatedAt: new Date(),
      isEdited: false
    };

    this.comments.push(comment);
    return comment;
  }

  // Ratings
  async rateTemplate(templateId: string, userId: string, rating: number, review?: string): Promise<TemplateRating> {
    const template = this.templates.find(t => t.id === templateId);
    if (!template) throw new Error('Template not found');

    // Check if user already rated
    const existingRatingIndex = this.ratings.findIndex(r => r.templateId === templateId && r.userId === userId);
    
    const templateRating: TemplateRating = {
      id: existingRatingIndex >= 0 ? this.ratings[existingRatingIndex].id : `rating_${Date.now()}`,
      templateId,
      userId,
      userName: `User ${userId}`,
      rating,
      review,
      createdAt: existingRatingIndex >= 0 ? this.ratings[existingRatingIndex].createdAt : new Date(),
      updatedAt: new Date(),
      isVerified: false,
      helpfulCount: 0
    };

    if (existingRatingIndex >= 0) {
      this.ratings[existingRatingIndex] = templateRating;
    } else {
      this.ratings.push(templateRating);
      template.analytics.totalRatings += 1;
      template.ratingCount += 1;
    }

    // Recalculate average rating
    const templateRatings = this.ratings.filter(r => r.templateId === templateId);
    const averageRating = templateRatings.reduce((sum, r) => sum + r.rating, 0) / templateRatings.length;
    template.analytics.averageRating = averageRating;
    template.rating = averageRating;

    return templateRating;
  }

  async getTemplateRatings(templateId: string, options: {
    page: number;
    limit: number;
  }): Promise<PaginatedResponse<TemplateRating>> {
    const templateRatings = this.ratings.filter(r => r.templateId === templateId)
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

    const startIndex = (options.page - 1) * options.limit;
    const endIndex = startIndex + options.limit;
    const paginatedRatings = templateRatings.slice(startIndex, endIndex);

    const pagination: PaginationInfo = {
      page: options.page,
      limit: options.limit,
      total: templateRatings.length,
      totalPages: Math.ceil(templateRatings.length / options.limit),
      hasNext: options.page < Math.ceil(templateRatings.length / options.limit),
      hasPrev: options.page > 1
    };

    return {
      data: paginatedRatings,
      pagination
    };
  }

  // Helper methods
  private getFormatFromFile(file: any): Template['format'] {
    const extension = file.originalname?.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'ppt':
      case 'pptx':
        return 'presentation';
      case 'xls':
      case 'xlsx':
        return 'spreadsheet';
      case 'mp4':
      case 'avi':
      case 'mov':
        return 'video';
      case 'html':
      case 'htm':
        return 'interactive';
      default:
        return 'document';
    }
  }

  private calculateDailyUsage(usageRecords: TemplateUsageRecord[]): any[] {
    const dailyUsage: Record<string, number> = {};
    
    usageRecords.forEach(record => {
      const date = record.timestamp.toISOString().split('T')[0];
      dailyUsage[date] = (dailyUsage[date] || 0) + 1;
    });

    return Object.entries(dailyUsage).map(([date, count]) => ({ date, count }));
  }

  private getTopUsers(usageRecords: TemplateUsageRecord[]): any[] {
    const userUsage: Record<string, number> = {};
    
    usageRecords.forEach(record => {
      userUsage[record.userId] = (userUsage[record.userId] || 0) + 1;
    });

    return Object.entries(userUsage)
      .map(([userId, count]) => ({ userId, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);
  }
}