import { Request, Response } from 'express';
import { CompetencyService } from '../services/CompetencyService';
import { CompetencyModel, SkillMatrix } from '../../types/competency';
import { ApiResponse } from '../../types/api';

export class CompetencyController {
  private competencyService: CompetencyService;

  constructor() {
    this.competencyService = new CompetencyService();
  }

  // Competency Models
  getAllModels = async (req: Request, res: Response): Promise<void> => {
    try {
      const { page = 1, limit = 10, category, search } = req.query;
      const models = await this.competencyService.getAllModels({
        page: Number(page),
        limit: Number(limit),
        category: category as string,
        search: search as string
      });
      
      const response: ApiResponse<CompetencyModel[]> = {
        success: true,
        data: models.data,
        pagination: models.pagination
      };
      
      res.json(response);
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Failed to fetch competency models',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  getModelById = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const model = await this.competencyService.getModelById(id);
      
      if (!model) {
        res.status(404).json({
          success: false,
          error: 'Competency model not found'
        });
        return;
      }
      
      const response: ApiResponse<CompetencyModel> = {
        success: true,
        data: model
      };
      
      res.json(response);
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Failed to fetch competency model',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  createModel = async (req: Request, res: Response): Promise<void> => {
    try {
      const modelData = req.body as Omit<CompetencyModel, 'id' | 'createdAt' | 'updatedAt'>;
      const model = await this.competencyService.createModel(modelData);
      
      const response: ApiResponse<CompetencyModel> = {
        success: true,
        data: model,
        message: 'Competency model created successfully'
      };
      
      res.status(201).json(response);
    } catch (error) {
      res.status(400).json({
        success: false,
        error: 'Failed to create competency model',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  updateModel = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const updateData = req.body as Partial<CompetencyModel>;
      const model = await this.competencyService.updateModel(id, updateData);
      
      if (!model) {
        res.status(404).json({
          success: false,
          error: 'Competency model not found'
        });
        return;
      }
      
      const response: ApiResponse<CompetencyModel> = {
        success: true,
        data: model,
        message: 'Competency model updated successfully'
      };
      
      res.json(response);
    } catch (error) {
      res.status(400).json({
        success: false,
        error: 'Failed to update competency model',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  deleteModel = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const deleted = await this.competencyService.deleteModel(id);
      
      if (!deleted) {
        res.status(404).json({
          success: false,
          error: 'Competency model not found'
        });
        return;
      }
      
      const response: ApiResponse<null> = {
        success: true,
        data: null,
        message: 'Competency model deleted successfully'
      };
      
      res.json(response);
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Failed to delete competency model',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  // Skills Matrix Methods
  getAllMatrices = async (req: Request, res: Response): Promise<void> => {
    try {
      const { page = 1, limit = 10, search } = req.query;
      const matrices = await this.competencyService.getAllMatrices({
        page: Number(page),
        limit: Number(limit),
        search: search as string
      });
      
      const response: ApiResponse<SkillMatrix[]> = {
        success: true,
        data: matrices.data,
        pagination: matrices.pagination
      };
      
      res.json(response);
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Failed to fetch skill matrices',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  getMatrixById = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const matrix = await this.competencyService.getMatrixById(id);
      
      if (!matrix) {
        res.status(404).json({
          success: false,
          error: 'Skill matrix not found'
        });
        return;
      }
      
      const response: ApiResponse<SkillMatrix> = {
        success: true,
        data: matrix
      };
      
      res.json(response);
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Failed to fetch skill matrix',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  createMatrix = async (req: Request, res: Response): Promise<void> => {
    try {
      const matrixData = req.body as Omit<SkillMatrix, 'id' | 'createdAt' | 'updatedAt'>;
      const matrix = await this.competencyService.createMatrix(matrixData);
      
      const response: ApiResponse<SkillMatrix> = {
        success: true,
        data: matrix,
        message: 'Skill matrix created successfully'
      };
      
      res.status(201).json(response);
    } catch (error) {
      res.status(400).json({
        success: false,
        error: 'Failed to create skill matrix',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  updateMatrix = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const updateData = req.body as Partial<SkillMatrix>;
      const matrix = await this.competencyService.updateMatrix(id, updateData);
      
      if (!matrix) {
        res.status(404).json({
          success: false,
          error: 'Skill matrix not found'
        });
        return;
      }
      
      const response: ApiResponse<SkillMatrix> = {
        success: true,
        data: matrix,
        message: 'Skill matrix updated successfully'
      };
      
      res.json(response);
    } catch (error) {
      res.status(400).json({
        success: false,
        error: 'Failed to update skill matrix',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  deleteMatrix = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const deleted = await this.competencyService.deleteMatrix(id);
      
      if (!deleted) {
        res.status(404).json({
          success: false,
          error: 'Skill matrix not found'
        });
        return;
      }
      
      const response: ApiResponse<null> = {
        success: true,
        data: null,
        message: 'Skill matrix deleted successfully'
      };
      
      res.json(response);
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Failed to delete skill matrix',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  // Assessment and Analytics
  assessCompetency = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const assessmentData = req.body;
      const result = await this.competencyService.assessCompetency(id, assessmentData);
      
      const response: ApiResponse<any> = {
        success: true,
        data: result,
        message: 'Competency assessment completed'
      };
      
      res.json(response);
    } catch (error) {
      res.status(400).json({
        success: false,
        error: 'Failed to assess competency',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  getSkillsGaps = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const gaps = await this.competencyService.getSkillsGaps(id);
      
      const response: ApiResponse<any> = {
        success: true,
        data: gaps
      };
      
      res.json(response);
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Failed to fetch skills gaps',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  getCompetencyAnalytics = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const analytics = await this.competencyService.getCompetencyAnalytics(id);
      
      const response: ApiResponse<any> = {
        success: true,
        data: analytics
      };
      
      res.json(response);
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Failed to fetch competency analytics',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  // Import/Export
  importModel = async (req: Request, res: Response): Promise<void> => {
    try {
      const file = req.file;
      if (!file) {
        res.status(400).json({
          success: false,
          error: 'No file provided'
        });
        return;
      }
      
      const result = await this.competencyService.importModel(file);
      
      const response: ApiResponse<any> = {
        success: true,
        data: result,
        message: 'Competency model imported successfully'
      };
      
      res.json(response);
    } catch (error) {
      res.status(400).json({
        success: false,
        error: 'Failed to import competency model',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  exportModel = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const { format = 'json' } = req.query;
      const exportData = await this.competencyService.exportModel(id, format as string);
      
      res.setHeader('Content-Type', 'application/octet-stream');
      res.setHeader('Content-Disposition', `attachment; filename="competency-model-${id}.${format}"`);
      res.send(exportData);
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Failed to export competency model',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  importMatrix = async (req: Request, res: Response): Promise<void> => {
    try {
      const file = req.file;
      if (!file) {
        res.status(400).json({
          success: false,
          error: 'No file provided'
        });
        return;
      }
      
      const result = await this.competencyService.importMatrix(file);
      
      const response: ApiResponse<any> = {
        success: true,
        data: result,
        message: 'Skill matrix imported successfully'
      };
      
      res.json(response);
    } catch (error) {
      res.status(400).json({
        success: false,
        error: 'Failed to import skill matrix',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  exportMatrix = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const { format = 'json' } = req.query;
      const exportData = await this.competencyService.exportMatrix(id, format as string);
      
      res.setHeader('Content-Type', 'application/octet-stream');
      res.setHeader('Content-Disposition', `attachment; filename="skill-matrix-${id}.${format}"`);
      res.send(exportData);
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Failed to export skill matrix',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };
}