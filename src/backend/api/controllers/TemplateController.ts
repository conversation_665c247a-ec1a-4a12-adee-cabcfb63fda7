import { Request, Response } from 'express';
import { TemplateService } from '../services/TemplateService';
import { Template, TemplateComment, TemplateRating } from '../../types/template';
import { ApiResponse } from '../../types/api';

export class TemplateController {
  private templateService: TemplateService;

  constructor() {
    this.templateService = new TemplateService();
  }

  // Template CRUD operations
  getAllTemplates = async (req: Request, res: Response): Promise<void> => {
    try {
      const { page = 1, limit = 10, category, search, format } = req.query;
      const templates = await this.templateService.getAllTemplates({
        page: Number(page),
        limit: Number(limit),
        category: category as string,
        search: search as string,
        format: format as string
      });
      
      const response: ApiResponse<Template[]> = {
        success: true,
        data: templates.data,
        pagination: templates.pagination
      };
      
      res.json(response);
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Failed to fetch templates',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  getTemplateById = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const template = await this.templateService.getTemplateById(id);
      
      if (!template) {
        res.status(404).json({
          success: false,
          error: 'Template not found'
        });
        return;
      }
      
      const response: ApiResponse<Template> = {
        success: true,
        data: template
      };
      
      res.json(response);
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Failed to fetch template',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  createTemplate = async (req: Request, res: Response): Promise<void> => {
    try {
      const templateData = req.body as Omit<Template, 'id' | 'createdAt' | 'updatedAt'>;
      const template = await this.templateService.createTemplate(templateData);
      
      const response: ApiResponse<Template> = {
        success: true,
        data: template,
        message: 'Template created successfully'
      };
      
      res.status(201).json(response);
    } catch (error) {
      res.status(400).json({
        success: false,
        error: 'Failed to create template',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  updateTemplate = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const updateData = req.body as Partial<Template>;
      const template = await this.templateService.updateTemplate(id, updateData);
      
      if (!template) {
        res.status(404).json({
          success: false,
          error: 'Template not found'
        });
        return;
      }
      
      const response: ApiResponse<Template> = {
        success: true,
        data: template,
        message: 'Template updated successfully'
      };
      
      res.json(response);
    } catch (error) {
      res.status(400).json({
        success: false,
        error: 'Failed to update template',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  deleteTemplate = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const deleted = await this.templateService.deleteTemplate(id);
      
      if (!deleted) {
        res.status(404).json({
          success: false,
          error: 'Template not found'
        });
        return;
      }
      
      const response: ApiResponse<null> = {
        success: true,
        data: null,
        message: 'Template deleted successfully'
      };
      
      res.json(response);
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Failed to delete template',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  // Search and filtering
  searchTemplates = async (req: Request, res: Response): Promise<void> => {
    try {
      const { query } = req.params;
      const { page = 1, limit = 10, category, format } = req.query;
      
      const results = await this.templateService.searchTemplates(query, {
        page: Number(page),
        limit: Number(limit),
        category: category as string,
        format: format as string
      });
      
      const response: ApiResponse<Template[]> = {
        success: true,
        data: results.data,
        pagination: results.pagination
      };
      
      res.json(response);
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Failed to search templates',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  getTemplatesByCategory = async (req: Request, res: Response): Promise<void> => {
    try {
      const { category } = req.params;
      const { page = 1, limit = 10 } = req.query;
      
      const templates = await this.templateService.getTemplatesByCategory(category, {
        page: Number(page),
        limit: Number(limit)
      });
      
      const response: ApiResponse<Template[]> = {
        success: true,
        data: templates.data,
        pagination: templates.pagination
      };
      
      res.json(response);
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Failed to fetch templates by category',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  getPopularTemplates = async (req: Request, res: Response): Promise<void> => {
    try {
      const { limit = 10 } = req.query;
      const templates = await this.templateService.getPopularTemplates(Number(limit));
      
      const response: ApiResponse<Template[]> = {
        success: true,
        data: templates
      };
      
      res.json(response);
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Failed to fetch popular templates',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  getRecentTemplates = async (req: Request, res: Response): Promise<void> => {
    try {
      const { page = 1, limit = 10 } = req.query;
      const templates = await this.templateService.getRecentTemplates({
        page: Number(page),
        limit: Number(limit)
      });
      
      const response: ApiResponse<Template[]> = {
        success: true,
        data: templates.data,
        pagination: templates.pagination
      };
      
      res.json(response);
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Failed to fetch recent templates',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  // Usage and analytics
  recordTemplateUsage = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const userId = req.user?.id;
      
      if (!userId) {
        res.status(401).json({
          success: false,
          error: 'User authentication required'
        });
        return;
      }
      
      await this.templateService.recordTemplateUsage(id, userId);
      
      const response: ApiResponse<null> = {
        success: true,
        data: null,
        message: 'Template usage recorded'
      };
      
      res.json(response);
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Failed to record template usage',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  getTemplateAnalytics = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const analytics = await this.templateService.getTemplateAnalytics(id);
      
      const response: ApiResponse<any> = {
        success: true,
        data: analytics
      };
      
      res.json(response);
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Failed to fetch template analytics',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  getTemplateVersions = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const versions = await this.templateService.getTemplateVersions(id);
      
      const response: ApiResponse<any[]> = {
        success: true,
        data: versions
      };
      
      res.json(response);
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Failed to fetch template versions',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  // File operations
  uploadTemplate = async (req: Request, res: Response): Promise<void> => {
    try {
      const file = req.file;
      if (!file) {
        res.status(400).json({
          success: false,
          error: 'No file provided'
        });
        return;
      }
      
      const result = await this.templateService.uploadTemplate(file);
      
      const response: ApiResponse<any> = {
        success: true,
        data: result,
        message: 'Template uploaded successfully'
      };
      
      res.json(response);
    } catch (error) {
      res.status(400).json({
        success: false,
        error: 'Failed to upload template',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  downloadTemplate = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const { format = 'json' } = req.query;
      
      const templateData = await this.templateService.downloadTemplate(id, format as string);
      
      res.setHeader('Content-Type', 'application/octet-stream');
      res.setHeader('Content-Disposition', `attachment; filename="template-${id}.${format}"`);
      res.send(templateData);
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Failed to download template',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  exportTemplate = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const { format = 'json', includeAnalytics = false } = req.body;
      
      const exportData = await this.templateService.exportTemplate(id, {
        format,
        includeAnalytics: Boolean(includeAnalytics)
      });
      
      const response: ApiResponse<any> = {
        success: true,
        data: exportData,
        message: 'Template exported successfully'
      };
      
      res.json(response);
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Failed to export template',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  // Collaboration
  shareTemplate = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const { userIds, permissions } = req.body;
      
      const result = await this.templateService.shareTemplate(id, userIds, permissions);
      
      const response: ApiResponse<any> = {
        success: true,
        data: result,
        message: 'Template shared successfully'
      };
      
      res.json(response);
    } catch (error) {
      res.status(400).json({
        success: false,
        error: 'Failed to share template',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  cloneTemplate = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const userId = req.user?.id;
      
      if (!userId) {
        res.status(401).json({
          success: false,
          error: 'User authentication required'
        });
        return;
      }
      
      const clonedTemplate = await this.templateService.cloneTemplate(id, userId);
      
      const response: ApiResponse<Template> = {
        success: true,
        data: clonedTemplate,
        message: 'Template cloned successfully'
      };
      
      res.status(201).json(response);
    } catch (error) {
      res.status(400).json({
        success: false,
        error: 'Failed to clone template',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  // Comments
  getTemplateComments = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const { page = 1, limit = 10 } = req.query;
      
      const comments = await this.templateService.getTemplateComments(id, {
        page: Number(page),
        limit: Number(limit)
      });
      
      const response: ApiResponse<TemplateComment[]> = {
        success: true,
        data: comments.data,
        pagination: comments.pagination
      };
      
      res.json(response);
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Failed to fetch template comments',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  addTemplateComment = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const { content } = req.body;
      const userId = req.user?.id;
      
      if (!userId) {
        res.status(401).json({
          success: false,
          error: 'User authentication required'
        });
        return;
      }
      
      const comment = await this.templateService.addTemplateComment(id, userId, content);
      
      const response: ApiResponse<TemplateComment> = {
        success: true,
        data: comment,
        message: 'Comment added successfully'
      };
      
      res.status(201).json(response);
    } catch (error) {
      res.status(400).json({
        success: false,
        error: 'Failed to add comment',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  // Ratings
  rateTemplate = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const { rating, review } = req.body;
      const userId = req.user?.id;
      
      if (!userId) {
        res.status(401).json({
          success: false,
          error: 'User authentication required'
        });
        return;
      }
      
      const templateRating = await this.templateService.rateTemplate(id, userId, rating, review);
      
      const response: ApiResponse<TemplateRating> = {
        success: true,
        data: templateRating,
        message: 'Template rated successfully'
      };
      
      res.json(response);
    } catch (error) {
      res.status(400).json({
        success: false,
        error: 'Failed to rate template',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  getTemplateRatings = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const { page = 1, limit = 10 } = req.query;
      
      const ratings = await this.templateService.getTemplateRatings(id, {
        page: Number(page),
        limit: Number(limit)
      });
      
      const response: ApiResponse<TemplateRating[]> = {
        success: true,
        data: ratings.data,
        pagination: ratings.pagination
      };
      
      res.json(response);
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Failed to fetch template ratings',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };
}