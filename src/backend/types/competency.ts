export interface CompetencyModel {
  id: string;
  name: string;
  description: string;
  category: string;
  competencies: Competency[];
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  version: string;
  status: 'draft' | 'published' | 'archived';
  tags: string[];
}

export interface Competency {
  id: string;
  name: string;
  description: string;
  level: CompetencyLevel;
  skills: Skill[];
  behavioralIndicators: string[];
  assessmentCriteria: AssessmentCriteria[];
  weight?: number;
}

export interface Skill {
  id: string;
  name: string;
  description: string;
  category: string;
  proficiencyLevels: ProficiencyLevel[];
  prerequisites?: string[];
  relatedSkills?: string[];
}

export interface ProficiencyLevel {
  level: number;
  name: string;
  description: string;
  criteria: string[];
}

export interface CompetencyLevel {
  id: string;
  name: string;
  description: string;
  order: number;
}

export interface AssessmentCriteria {
  id: string;
  description: string;
  weight: number;
  assessmentMethod: 'observation' | 'test' | 'project' | 'peer-review' | 'self-assessment';
  passingScore: number;
}

export interface SkillMatrix {
  id: string;
  name: string;
  description: string;
  roles: Role[];
  skills: Skill[];
  assessments: SkillAssessment[];
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  organizationId?: string;
}

export interface Role {
  id: string;
  name: string;
  description: string;
  department: string;
  level: 'entry' | 'intermediate' | 'senior' | 'expert';
  requiredSkills: RequiredSkill[];
  responsibilities: string[];
}

export interface RequiredSkill {
  skillId: string;
  minimumLevel: number;
  importance: 'critical' | 'important' | 'nice-to-have';
  weight: number;
}

export interface SkillAssessment {
  id: string;
  userId: string;
  skillId: string;
  currentLevel: number;
  targetLevel: number;
  assessmentDate: Date;
  assessedBy: string;
  notes?: string;
  evidenceLinks?: string[];
  nextReviewDate?: Date;
}

export interface CompetencyGap {
  skillId: string;
  skillName: string;
  currentLevel: number;
  requiredLevel: number;
  gap: number;
  priority: 'high' | 'medium' | 'low';
  developmentActions: DevelopmentAction[];
}

export interface DevelopmentAction {
  id: string;
  type: 'training' | 'mentoring' | 'project' | 'certification' | 'self-study';
  title: string;
  description: string;
  estimatedDuration: string;
  cost?: number;
  provider?: string;
  completionCriteria: string[];
}

export interface CompetencyAnalytics {
  totalCompetencies: number;
  averageCompetencyLevel: number;
  competencyDistribution: { [level: string]: number };
  skillsGapAnalysis: CompetencyGap[];
  topSkillsNeeded: string[];
  competencyTrends: CompetencyTrend[];
  benchmarkComparison?: BenchmarkData;
}

export interface CompetencyTrend {
  period: string;
  averageLevel: number;
  improvementRate: number;
  skillsAcquired: number;
  skillsImproved: number;
}

export interface BenchmarkData {
  industry: string;
  averageLevel: number;
  percentile: number;
  comparison: 'above' | 'at' | 'below';
}

export interface CompetencyAssessmentRequest {
  userId: string;
  competencyModelId: string;
  assessmentType: 'self' | 'manager' | '360' | 'peer';
  responses: AssessmentResponse[];
  notes?: string;
}

export interface AssessmentResponse {
  competencyId: string;
  skillId: string;
  rating: number;
  evidence?: string;
  comments?: string;
}

export interface CompetencyAssessmentResult {
  id: string;
  userId: string;
  competencyModelId: string;
  overallScore: number;
  competencyScores: CompetencyScore[];
  skillsGaps: CompetencyGap[];
  recommendations: string[];
  assessmentDate: Date;
  nextAssessmentDate?: Date;
}

export interface CompetencyScore {
  competencyId: string;
  competencyName: string;
  score: number;
  maxScore: number;
  percentage: number;
  level: string;
  skillScores: SkillScore[];
}

export interface SkillScore {
  skillId: string;
  skillName: string;
  score: number;
  maxScore: number;
  percentage: number;
  level: number;
}