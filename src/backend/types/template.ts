export interface Template {
  id: string;
  name: string;
  description: string;
  category: string;
  format: 'document' | 'presentation' | 'spreadsheet' | 'video' | 'interactive';
  tags: string[];
  content: TemplateContent;
  metadata: TemplateMetadata;
  permissions: TemplatePermissions;
  analytics: TemplateAnalytics;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  version: string;
  isPublic: boolean;
  downloadCount: number;
  rating: number;
  ratingCount: number;
}

export interface TemplateContent {
  type: 'text' | 'html' | 'markdown' | 'json' | 'binary';
  data: string | object;
  fileUrl?: string;
  thumbnailUrl?: string;
  previewUrl?: string;
}

export interface TemplateMetadata {
  title: string;
  author: string;
  duration?: number; // in minutes
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  learningObjectives: string[];
  prerequisites: string[];
  targetAudience: string[];
  language: string;
  fileSize?: number; // in bytes
  lastModified: Date;
}

export interface TemplatePermissions {
  isPublic: boolean;
  allowDownload: boolean;
  allowClone: boolean;
  allowComment: boolean;
  allowRating: boolean;
  sharedWith: SharedUser[];
  createdBy: string;
}

export interface SharedUser {
  userId: string;
  permission: 'view' | 'edit' | 'admin';
  sharedAt: Date;
  sharedBy: string;
}

export interface TemplateAnalytics {
  views: number;
  downloads: number;
  clones: number;
  shares: number;
  averageRating: number;
  totalRatings: number;
  usageByMonth: MonthlyUsage[];
  popularityScore: number;
  engagementRate: number;
}

export interface MonthlyUsage {
  month: string; // YYYY-MM format
  views: number;
  downloads: number;
  clones: number;
}

export interface TemplateComment {
  id: string;
  templateId: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  content: string;
  createdAt: Date;
  updatedAt: Date;
  parentId?: string; // for nested comments
  replies?: TemplateComment[];
  isEdited: boolean;
}

export interface TemplateRating {
  id: string;
  templateId: string;
  userId: string;
  userName: string;
  rating: number; // 1-5 scale
  review?: string;
  createdAt: Date;
  updatedAt: Date;
  isVerified: boolean;
  helpfulCount: number;
}

export interface TemplateVersion {
  id: string;
  templateId: string;
  version: string;
  changes: string;
  createdAt: Date;
  createdBy: string;
  content: TemplateContent;
  isActive: boolean;
}

export interface TemplateCategory {
  id: string;
  name: string;
  description: string;
  icon?: string;
  color?: string;
  parentId?: string;
  subcategories?: TemplateCategory[];
  templateCount: number;
}

export interface TemplateSearchFilters {
  category?: string;
  format?: string;
  difficulty?: string;
  language?: string;
  tags?: string[];
  rating?: number;
  dateRange?: {
    start: Date;
    end: Date;
  };
  author?: string;
}

export interface TemplateUsageRecord {
  id: string;
  templateId: string;
  userId: string;
  action: 'view' | 'download' | 'clone' | 'share' | 'rate' | 'comment';
  timestamp: Date;
  metadata?: Record<string, any>;
}

export interface TemplateExportOptions {
  format: 'json' | 'pdf' | 'docx' | 'pptx' | 'xlsx';
  includeAnalytics?: boolean;
  includeComments?: boolean;
  includeRatings?: boolean;
  includeVersionHistory?: boolean;
}

export interface TemplateImportResult {
  success: boolean;
  templateId?: string;
  errors?: string[];
  warnings?: string[];
  importedAt: Date;
}

export interface TemplateBulkOperation {
  operation: 'delete' | 'update' | 'export' | 'share';
  templateIds: string[];
  parameters?: Record<string, any>;
}

export interface TemplateBulkResult {
  success: boolean;
  processed: number;
  failed: number;
  errors: Array<{
    templateId: string;
    error: string;
  }>;
}

// Request/Response types
export interface CreateTemplateRequest {
  name: string;
  description: string;
  category: string;
  format: Template['format'];
  tags: string[];
  content: TemplateContent;
  metadata: Omit<TemplateMetadata, 'lastModified'>;
  isPublic: boolean;
}

export interface UpdateTemplateRequest {
  name?: string;
  description?: string;
  category?: string;
  tags?: string[];
  content?: TemplateContent;
  metadata?: Partial<TemplateMetadata>;
  isPublic?: boolean;
}

export interface TemplateSearchRequest {
  query?: string;
  filters?: TemplateSearchFilters;
  sortBy?: 'name' | 'createdAt' | 'rating' | 'downloads' | 'popularity';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

export interface TemplateShareRequest {
  userIds: string[];
  permissions: 'view' | 'edit' | 'admin';
  message?: string;
  expiresAt?: Date;
}

export interface AddCommentRequest {
  content: string;
  parentId?: string;
}

export interface RateTemplateRequest {
  rating: number;
  review?: string;
}