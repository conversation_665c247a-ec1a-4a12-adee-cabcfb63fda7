/**
 * Advanced Visual Workflow Designer
 * Interactive canvas for creating and editing workflows
 */

import React, { useState, useCallback, useRef, useEffect } from 'react';
import {
  Search,
  Save,
  Play,
  Pause,
  RotateCcw,
  Download,
  Upload,
  Zap,
  GitBranch,
  Bot,
  Code,
  Database,
  Shield,
  Brain,
  Clock,
  AlertCircle,
  CheckCircle,
  XCircle,
  Plus,
  Trash2,
  Settings,
  Eye,
  EyeOff,
  Layers,
  Activity,
  TrendingUp,
  Copy,
  Share2,
} from 'lucide-react';
import { api } from '@/lib/api';
import {
  AdvancedWorkflow,
  AdvancedWorkflowNode,
  AdvancedWorkflowEdge,
  AdvancedNodeType,
  WorkflowStatus,
  WorkflowVariable,
  WorkflowTrigger,
  WorkflowSettings,
  NodeConfiguration,
  WorkflowPort,
  OptimizationSuggestion,
  WorkflowAnalytics,
  NodeMetadata,
  TransformConfiguration,
  IntegrationConfig,
} from '@/types/advanced-workflow';
import { workflowEngine } from '@/services/workflowExecutionEngine';
import { cn } from '@/lib/utils';

interface WorkflowDesignerProps {
  workflowId?: string;
  onSave?: (workflow: AdvancedWorkflow) => void;
  onExecute?: (workflow: AdvancedWorkflow) => void;
  className?: string;
}

interface CanvasNode extends AdvancedWorkflowNode {
  selected?: boolean;
  executing?: boolean;
  error?: string;
}

interface CanvasEdge extends AdvancedWorkflowEdge {
  selected?: boolean;
  animated?: boolean;
}

interface NodeTemplate {
  type: AdvancedNodeType;
  name: string;
  icon: React.ReactNode;
  description: string;
  defaultConfig: Partial<NodeConfiguration>;
  category: string;
}

const nodeTemplates: NodeTemplate[] = [
  {
    type: 'start',
    name: 'Start',
    icon: <Play className="w-4 h-4" />,
    description: 'Workflow entry point',
    defaultConfig: {},
    category: 'Control',
  },
  {
    type: 'end',
    name: 'End',
    icon: <CheckCircle className="w-4 h-4" />,
    description: 'Workflow exit point',
    defaultConfig: {},
    category: 'Control',
  },
  {
    type: 'agent',
    name: 'Agent',
    icon: <Bot className="w-4 h-4" />,
    description: 'Execute an AI agent',
    defaultConfig: { agentId: '' },
    category: 'Execution',
  },
  {
    type: 'condition',
    name: 'Condition',
    icon: <GitBranch className="w-4 h-4" />,
    description: 'Conditional branching',
    defaultConfig: { condition: { type: 'simple' } },
    category: 'Logic',
  },
  {
    type: 'loop',
    name: 'Loop',
    icon: <RotateCcw className="w-4 h-4" />,
    description: 'Iterate over items',
    defaultConfig: { loopConfig: { type: 'foreach' } },
    category: 'Logic',
  },
  {
    type: 'parallel',
    name: 'Parallel',
    icon: <Zap className="w-4 h-4" />,
    description: 'Execute in parallel',
    defaultConfig: { parallelConfig: { maxConcurrency: 5 } },
    category: 'Optimization',
  },
  {
    type: 'transform',
    name: 'Transform',
    icon: <Code className="w-4 h-4" />,
    description: 'Transform data',
    defaultConfig: { transformConfig: { type: 'map', expression: 'item' } as TransformConfiguration },
    category: 'Data',
  },
  {
    type: 'validation',
    name: 'Validation',
    icon: <Shield className="w-4 h-4" />,
    description: 'Validate data',
    defaultConfig: { validationRules: [] },
    category: 'Data',
  },
  {
    type: 'integration',
    name: 'Integration',
    icon: <Database className="w-4 h-4" />,
    description: 'External integration',
    defaultConfig: { integrationConfig: { service: '', method: '' } as IntegrationConfig },
    category: 'Integration',
  },
  {
    type: 'ai-decision',
    name: 'AI Decision',
    icon: <Brain className="w-4 h-4" />,
    description: 'AI-powered decision',
    defaultConfig: { aiConfig: { model: 'gpt-4' } },
    category: 'AI',
  },
  {
    type: 'cache',
    name: 'Cache',
    icon: <Database className="w-4 h-4" />,
    description: 'Cache results',
    defaultConfig: { cacheConfig: { ttl: 300000 } },
    category: 'Optimization',
  },
  {
    type: 'error-handler',
    name: 'Error Handler',
    icon: <AlertCircle className="w-4 h-4" />,
    description: 'Handle errors',
    defaultConfig: { errorHandling: { strategy: 'retry' } },
    category: 'Error',
  },
];

export const AdvancedWorkflowDesigner: React.FC<WorkflowDesignerProps> = ({
  workflowId,
  onSave,
  onExecute,
  className,
}) => {
  const canvasRef = useRef<HTMLDivElement>(null);
  const [workflow, setWorkflow] = useState<AdvancedWorkflow>({
    id: workflowId || `workflow-${Date.now()}`,
    name: 'New Workflow',
    description: '',
    version: '1.0.0',
    status: 'draft',
    nodes: [],
    edges: [],
    variables: [],
    triggers: [],
    settings: {},
    metadata: {},
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    createdBy: 'user',
    tags: [],
  });

  const [selectedNodes, setSelectedNodes] = useState<Set<string>>(new Set());
  const [selectedEdges, setSelectedEdges] = useState<Set<string>>(new Set());
  const [draggedNode, setDraggedNode] = useState<NodeTemplate | null>(null);
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectingFrom, setConnectingFrom] = useState<{ nodeId: string; portId: string } | null>(null);
  const [showGrid, setShowGrid] = useState(true);
  const [showMinimap, setShowMinimap] = useState(true);
  const [showOptimizations, setShowOptimizations] = useState(false);
  const [optimizations, setOptimizations] = useState<OptimizationSuggestion[]>([]);
  const [analytics, setAnalytics] = useState<WorkflowAnalytics | null>(null);
  const [executionStatus, setExecutionStatus] = useState<'idle' | 'running' | 'paused'>('idle');
  const [nodeStates, setNodeStates] = useState<Map<string, { executing?: boolean; error?: string }>>(new Map());
  const [zoom, setZoom] = useState(1);
  const [pan, setPan] = useState({ x: 0, y: 0 });
  const [showVariables, setShowVariables] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [testMode, setTestMode] = useState(false);

  // Load workflow if ID provided
  useEffect(() => {
    if (workflowId) {
      loadWorkflow(workflowId);
    }
  }, [workflowId]);

  // Auto-save
  useEffect(() => {
    const timer = setTimeout(() => {
      if (workflow.status !== 'draft') {
        saveWorkflow();
      }
    }, 5000);
    return () => clearTimeout(timer);
  }, [workflow]);

  const loadWorkflow = async (id: string) => {
    try {
      const loaded = await api.loadWorkflow(id) as AdvancedWorkflow;
      setWorkflow(loaded);
      
      // Load analytics
      const analytics = await api.getAdvancedWorkflowAnalytics(id, { 
        start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(), 
        end: new Date().toISOString() 
      }) as WorkflowAnalytics;
      setAnalytics(analytics);
      
      // Load optimizations
      const optimizations = await api.getOptimizationSuggestions(id) as OptimizationSuggestion[];
      setOptimizations(optimizations);
    } catch (error) {
    }
  };

  const saveWorkflow = async () => {
    try {
      await api.saveWorkflow(workflow);
      onSave?.(workflow);
    } catch (error) {
    }
  };

  const executeWorkflow = async () => {
    if (executionStatus === 'running') {
      setExecutionStatus('paused');
      return;
    }

    setExecutionStatus('running');
    try {
      const result = await workflowEngine.executeWorkflow(
        workflow,
        { id: 'manual', type: 'manual', enabled: true, config: {} },
        {}
      );
      
      onExecute?.(workflow);
    } catch (error) {
    } finally {
      setExecutionStatus('idle');
    }
  };

  const addNode = useCallback((template: NodeTemplate, position: { x: number; y: number }) => {
    const newNode: CanvasNode = {
      id: `node-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type: template.type,
      name: template.name,
      description: template.description,
      position,
      config: { ...template.defaultConfig },
      inputs: generatePorts(template.type, 'input'),
      outputs: generatePorts(template.type, 'output'),
      metadata: {
        icon: template.name,
        color: getCategoryColor(template.category),
        estimatedDuration: 1000,
        cost: 0,
      },
    };

    setWorkflow(prev => ({
      ...prev,
      nodes: [...prev.nodes, newNode],
      updatedAt: new Date().toISOString(),
    }));
  }, []);

  const generatePorts = (nodeType: AdvancedNodeType, portType: 'input' | 'output'): WorkflowPort[] => {
    const ports: WorkflowPort[] = [];
    
    if (portType === 'input') {
      if (nodeType !== 'start') {
        ports.push({
          id: 'in-default',
          name: 'Input',
          type: 'input',
          dataType: 'any',
          required: true,
          multiple: false,
          validation: [],
        });
      }
    } else {
      if (nodeType !== 'end') {
        ports.push({
          id: 'out-default',
          name: 'Output',
          type: 'output',
          dataType: 'any',
          required: false,
          multiple: true,
          validation: [],
        });
      }
      
      if (nodeType === 'condition') {
        ports.push(
          {
            id: 'out-true',
            name: 'True',
            type: 'output',
            dataType: 'any',
            required: false,
            multiple: false,
            validation: [],
          },
          {
            id: 'out-false',
            name: 'False',
            type: 'output',
            dataType: 'any',
            required: false,
            multiple: false,
            validation: [],
          }
        );
      }
    }
    
    return ports;
  };

  const getCategoryColor = (category: string): string => {
    const colors: Record<string, string> = {
      Control: '#10b981',
      Execution: '#3b82f6',
      Logic: '#8b5cf6',
      Data: '#f59e0b',
      Integration: '#ef4444',
      AI: '#ec4899',
      Optimization: '#06b6d4',
      Error: '#dc2626',
    };
    return colors[category] || '#6b7280';
  };

  const deleteNode = useCallback((nodeId: string) => {
    setWorkflow(prev => ({
      ...prev,
      nodes: prev.nodes.filter(n => n.id !== nodeId),
      edges: prev.edges.filter(e => e.source !== nodeId && e.target !== nodeId),
      updatedAt: new Date().toISOString(),
    }));
    setSelectedNodes(prev => {
      const next = new Set(prev);
      next.delete(nodeId);
      return next;
    });
  }, []);

  const deleteEdge = useCallback((edgeId: string) => {
    setWorkflow(prev => ({
      ...prev,
      edges: prev.edges.filter(e => e.id !== edgeId),
      updatedAt: new Date().toISOString(),
    }));
    setSelectedEdges(prev => {
      const next = new Set(prev);
      next.delete(edgeId);
      return next;
    });
  }, []);

  const connectNodes = useCallback((from: { nodeId: string; portId: string }, to: { nodeId: string; portId: string }) => {
    const newEdge: CanvasEdge = {
      id: `edge-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      source: from.nodeId,
      sourcePort: from.portId,
      target: to.nodeId,
      targetPort: to.portId,
      metadata: {
        animated: true,
      },
    };

    setWorkflow(prev => ({
      ...prev,
      edges: [...prev.edges, newEdge],
      updatedAt: new Date().toISOString(),
    }));
  }, []);

  const handleNodeDragStart = (e: React.DragEvent, template: NodeTemplate) => {
    setDraggedNode(template);
    e.dataTransfer.effectAllowed = 'copy';
  };

  const handleCanvasDrop = (e: React.DragEvent) => {
    e.preventDefault();
    if (!draggedNode || !canvasRef.current) return;

    const rect = canvasRef.current.getBoundingClientRect();
    const position = {
      x: (e.clientX - rect.left - pan.x) / zoom,
      y: (e.clientY - rect.top - pan.y) / zoom,
    };

    addNode(draggedNode, position);
    setDraggedNode(null);
  };

  const handleCanvasDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'copy';
  };

  const handleNodeClick = (nodeId: string, e: React.MouseEvent) => {
    if (e.ctrlKey || e.metaKey) {
      setSelectedNodes(prev => {
        const next = new Set(prev);
        if (next.has(nodeId)) {
          next.delete(nodeId);
        } else {
          next.add(nodeId);
        }
        return next;
      });
    } else {
      setSelectedNodes(new Set([nodeId]));
    }
  };

  const handleEdgeClick = (edgeId: string, e: React.MouseEvent) => {
    if (e.ctrlKey || e.metaKey) {
      setSelectedEdges(prev => {
        const next = new Set(prev);
        if (next.has(edgeId)) {
          next.delete(edgeId);
        } else {
          next.add(edgeId);
        }
        return next;
      });
    } else {
      setSelectedEdges(new Set([edgeId]));
    }
  };

  const handlePortClick = (nodeId: string, portId: string, portType: 'input' | 'output') => {
    if (!isConnecting) {
      if (portType === 'output') {
        setIsConnecting(true);
        setConnectingFrom({ nodeId, portId });
      }
    } else {
      if (portType === 'input' && connectingFrom) {
        connectNodes(connectingFrom, { nodeId, portId });
      }
      setIsConnecting(false);
      setConnectingFrom(null);
    }
  };

  const handleOptimizationApply = async (suggestion: OptimizationSuggestion) => {
    try {
      const optimized = await api.applyOptimization(workflow, suggestion) as AdvancedWorkflow;
      setWorkflow(optimized);
    } catch (error) {
    }
  };

  const exportWorkflow = async () => {
    try {
      const exported = await api.exportWorkflow(workflow) as string;
      const blob = new Blob([exported], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${workflow.name}.json`;
      a.click();
      URL.revokeObjectURL(url);
    } catch (error) {
    }
  };

  const importWorkflow = async () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (!file) return;

      const text = await file.text();
      try {
        const imported = JSON.parse(text) as AdvancedWorkflow;
        setWorkflow(imported);
      } catch (error) {
      }
    };
    input.click();
  };

  const duplicateWorkflow = async () => {
    const duplicated = {
      ...workflow,
      id: `workflow-${Date.now()}`,
      name: `${workflow.name} (Copy)`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    setWorkflow(duplicated);
  };

  const shareWorkflow = async () => {
    try {
      const shareUrl = await api.shareWorkflow(workflow) as string;
      navigator.clipboard.writeText(shareUrl);
    } catch (error) {
    }
  };

  return (
    <div className={cn("flex h-full", className)}>
      {/* Sidebar - Node Palette */}
      <div className="w-64 bg-card border-r p-4 overflow-y-auto">
        <h3 className="font-semibold mb-4">Nodes</h3>
        <div className="space-y-4">
          {Object.entries(
            nodeTemplates.reduce((acc, template) => {
              if (!acc[template.category]) acc[template.category] = [];
              acc[template.category].push(template);
              return acc;
            }, {} as Record<string, NodeTemplate[]>)
          ).map(([category, templates]) => (
            <div key={category}>
              <h4 className="text-sm font-medium text-muted-foreground mb-2">{category}</h4>
              <div className="space-y-1">
                {templates.map(template => (
                  <div
                    key={template.type}
                    draggable
                    onDragStart={(e) => handleNodeDragStart(e, template)}
                    className="flex items-center gap-2 p-2 rounded hover:bg-accent cursor-move"
                  >
                    <div className="flex-shrink-0" style={{ color: getCategoryColor(category) }}>
                      {template.icon}
                    </div>
                    <div className="flex-1">
                      <div className="text-sm font-medium">{template.name}</div>
                      <div className="text-xs text-muted-foreground">{template.description}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Main Canvas */}
      <div className="flex-1 flex flex-col">
        {/* Toolbar */}
        <div className="h-12 bg-card border-b flex items-center justify-between px-4">
          <div className="flex items-center gap-2">
            <input
              type="text"
              value={workflow.name}
              onChange={(e) => setWorkflow(prev => ({ ...prev, name: e.target.value }))}
              className="bg-transparent font-semibold text-lg border-none outline-none"
            />
            <span className="text-xs text-muted-foreground">v{workflow.version}</span>
            <span className={cn(
              "px-2 py-0.5 rounded text-xs font-medium",
              workflow.status === 'active' && "bg-green-500/20 text-green-500",
              workflow.status === 'draft' && "bg-yellow-500/20 text-yellow-500",
              workflow.status === 'archived' && "bg-gray-500/20 text-gray-500",
            )}>
              {workflow.status}
            </span>
          </div>

          <div className="flex items-center gap-1">
            <button
              onClick={executeWorkflow}
              className="p-1.5 hover:bg-accent rounded"
              title={executionStatus === 'running' ? 'Pause' : 'Execute'}
            >
              {executionStatus === 'running' ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
            </button>
            <button
              onClick={saveWorkflow}
              className="p-1.5 hover:bg-accent rounded"
              title="Save"
            >
              <Save className="w-4 h-4" />
            </button>
            <button
              onClick={exportWorkflow}
              className="p-1.5 hover:bg-accent rounded"
              title="Export"
            >
              <Download className="w-4 h-4" />
            </button>
            <button
              onClick={importWorkflow}
              className="p-1.5 hover:bg-accent rounded"
              title="Import"
            >
              <Upload className="w-4 h-4" />
            </button>
            <button
              onClick={duplicateWorkflow}
              className="p-1.5 hover:bg-accent rounded"
              title="Duplicate"
            >
              <Copy className="w-4 h-4" />
            </button>
            <button
              onClick={shareWorkflow}
              className="p-1.5 hover:bg-accent rounded"
              title="Share"
            >
              <Share2 className="w-4 h-4" />
            </button>
            <div className="w-px h-6 bg-border mx-1" />
            <button
              onClick={() => setShowGrid(!showGrid)}
              className={cn("p-1.5 hover:bg-accent rounded", showGrid && "bg-accent")}
              title="Toggle Grid"
            >
              <Layers className="w-4 h-4" />
            </button>
            <button
              onClick={() => setShowMinimap(!showMinimap)}
              className={cn("p-1.5 hover:bg-accent rounded", showMinimap && "bg-accent")}
              title="Toggle Minimap"
            >
              {showMinimap ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
            </button>
            <button
              onClick={() => setShowOptimizations(!showOptimizations)}
              className={cn("p-1.5 hover:bg-accent rounded", showOptimizations && "bg-accent")}
              title="Show Optimizations"
            >
              <TrendingUp className="w-4 h-4" />
            </button>
            <button
              onClick={() => setShowVariables(!showVariables)}
              className={cn("p-1.5 hover:bg-accent rounded", showVariables && "bg-accent")}
              title="Variables"
            >
              <Code className="w-4 h-4" />
            </button>
            <button
              onClick={() => setShowSettings(!showSettings)}
              className={cn("p-1.5 hover:bg-accent rounded", showSettings && "bg-accent")}
              title="Settings"
            >
              <Settings className="w-4 h-4" />
            </button>
            <button
              onClick={() => setTestMode(!testMode)}
              className={cn("p-1.5 hover:bg-accent rounded", testMode && "bg-accent")}
              title="Test Mode"
            >
              <Activity className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Canvas Area */}
        <div className="flex-1 relative overflow-hidden">
          <div
            ref={canvasRef}
            className="absolute inset-0"
            onDrop={handleCanvasDrop}
            onDragOver={handleCanvasDragOver}
            onClick={() => {
              setSelectedNodes(new Set());
              setSelectedEdges(new Set());
              setIsConnecting(false);
              setConnectingFrom(null);
            }}
          >
            {/* Grid Background */}
            {showGrid && (
              <svg
                className="absolute inset-0 w-full h-full"
                style={{ transform: `translate(${pan.x}px, ${pan.y}px) scale(${zoom})` }}
              >
                <defs>
                  <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
                    <path d="M 20 0 L 0 0 0 20" fill="none" stroke="currentColor" strokeWidth="0.5" opacity="0.1" />
                  </pattern>
                </defs>
                <rect width="100%" height="100%" fill="url(#grid)" />
              </svg>
            )}

            {/* Edges */}
            <svg
              className="absolute inset-0 w-full h-full pointer-events-none"
              style={{ transform: `translate(${pan.x}px, ${pan.y}px) scale(${zoom})` }}
            >
              {workflow.edges.map(edge => {
                const sourceNode = workflow.nodes.find(n => n.id === edge.source);
                const targetNode = workflow.nodes.find(n => n.id === edge.target);
                if (!sourceNode || !targetNode) return null;

                const sourcePort = sourceNode.outputs.find(p => p.id === edge.sourcePort);
                const targetPort = targetNode.inputs.find(p => p.id === edge.targetPort);
                if (!sourcePort || !targetPort) return null;

                const x1 = sourceNode.position.x + 150; // Approximate node width
                const y1 = sourceNode.position.y + 30; // Approximate port position
                const x2 = targetNode.position.x;
                const y2 = targetNode.position.y + 30;

                const cx1 = x1 + (x2 - x1) * 0.5;
                const cy1 = y1;
                const cx2 = x1 + (x2 - x1) * 0.5;
                const cy2 = y2;

                return (
                  <g key={edge.id}>
                    <path
                      d={`M ${x1} ${y1} C ${cx1} ${cy1}, ${cx2} ${cy2}, ${x2} ${y2}`}
                      fill="none"
                      stroke={selectedEdges.has(edge.id) ? 'rgb(59, 130, 246)' : 'rgb(107, 114, 128)'}
                      strokeWidth={selectedEdges.has(edge.id) ? 2 : 1}
                      className="pointer-events-auto cursor-pointer"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleEdgeClick(edge.id, e);
                      }}
                    />
                    {edge.metadata?.animated && (
                      <circle r="3" fill="rgb(59, 130, 246)">
                        <animateMotion dur="2s" repeatCount="indefinite">
                          <mpath href={`#edge-path-${edge.id}`} />
                        </animateMotion>
                      </circle>
                    )}
                  </g>
                );
              })}
            </svg>

            {/* Nodes */}
            <div
              className="absolute inset-0"
              style={{ transform: `translate(${pan.x}px, ${pan.y}px) scale(${zoom})` }}
            >
              {workflow.nodes.map(node => (
                <div
                  key={node.id}
                  className={cn(
                    "absolute bg-card border rounded-lg shadow-sm cursor-move select-none",
                    selectedNodes.has(node.id) && "ring-2 ring-primary",
                    nodeStates.get(node.id)?.executing && "animate-pulse",
                    nodeStates.get(node.id)?.error && "border-destructive",
                  )}
                  style={{
                    left: node.position.x,
                    top: node.position.y,
                    minWidth: '150px',
                  }}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleNodeClick(node.id, e);
                  }}
                >
                  <div
                    className="flex items-center gap-2 p-2 border-b"
                    style={{ backgroundColor: node.metadata.color + '20' }}
                  >
                    <div style={{ color: node.metadata.color }}>
                      {nodeTemplates.find(t => t.type === node.type)?.icon}
                    </div>
                    <div className="flex-1 text-sm font-medium truncate">{node.name}</div>
                    {selectedNodes.has(node.id) && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          deleteNode(node.id);
                        }}
                        className="p-0.5 hover:bg-destructive/20 rounded"
                      >
                        <Trash2 className="w-3 h-3 text-destructive" />
                      </button>
                    )}
                  </div>
                  <div className="p-2 space-y-1">
                    {/* Input Ports */}
                    {node.inputs.map(port => (
                      <div
                        key={port.id}
                        className="flex items-center gap-1"
                        onClick={(e) => {
                          e.stopPropagation();
                          handlePortClick(node.id, port.id, 'input');
                        }}
                      >
                        <div className="w-2 h-2 rounded-full bg-primary cursor-pointer" />
                        <span className="text-xs text-muted-foreground">{port.name}</span>
                      </div>
                    ))}
                    {/* Output Ports */}
                    {node.outputs.map(port => (
                      <div
                        key={port.id}
                        className="flex items-center gap-1 justify-end"
                        onClick={(e) => {
                          e.stopPropagation();
                          handlePortClick(node.id, port.id, 'output');
                        }}
                      >
                        <span className="text-xs text-muted-foreground">{port.name}</span>
                        <div className="w-2 h-2 rounded-full bg-primary cursor-pointer" />
                      </div>
                    ))}
                  </div>
                  {nodeStates.get(node.id)?.error && (
                    <div className="p-2 border-t">
                      <div className="flex items-center gap-1 text-xs text-destructive">
                        <XCircle className="w-3 h-3" />
                        <span className="truncate">{nodeStates.get(node.id)?.error}</span>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>

            {/* Minimap */}
            {showMinimap && (
              <div className="absolute bottom-4 right-4 w-48 h-32 bg-card border rounded shadow-lg">
                <div className="relative w-full h-full overflow-hidden">
                  {/* Minimap content */}
                  <div className="absolute inset-0 p-2">
                    <svg className="w-full h-full">
                      {workflow.nodes.map(node => (
                        <rect
                          key={node.id}
                          x={node.position.x / 10}
                          y={node.position.y / 10}
                          width="10"
                          height="6"
                          fill={node.metadata.color}
                          opacity="0.5"
                        />
                      ))}
                    </svg>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Right Sidebar - Properties/Optimizations */}
      {(selectedNodes.size > 0 || showOptimizations || showVariables || showSettings) && (
        <div className="w-80 bg-card border-l overflow-y-auto">
          {selectedNodes.size === 1 && (
            <div className="p-4 border-b">
              <h3 className="font-semibold mb-4">Node Properties</h3>
              {/* Node configuration form */}
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium">Name</label>
                  <input
                    type="text"
                    value={workflow.nodes.find(n => selectedNodes.has(n.id))?.name || ''}
                    onChange={(e) => {
                      const nodeId = Array.from(selectedNodes)[0];
                      setWorkflow(prev => ({
                        ...prev,
                        nodes: prev.nodes.map(n =>
                          n.id === nodeId ? { ...n, name: e.target.value } : n
                        ),
                      }));
                    }}
                    className="w-full mt-1 px-3 py-1.5 bg-background border rounded"
                  />
                </div>
                {/* Additional configuration fields based on node type */}
              </div>
            </div>
          )}

          {showOptimizations && optimizations.length > 0 && (
            <div className="p-4 border-b">
              <h3 className="font-semibold mb-4">Optimizations</h3>
              <div className="space-y-2">
                {optimizations.map(suggestion => (
                  <div
                    key={suggestion.id}
                    className="p-3 bg-accent/50 rounded space-y-2"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="text-sm font-medium">{suggestion.type}</div>
                        <div className="text-xs text-muted-foreground">{suggestion.description}</div>
                      </div>
                      <button
                        onClick={() => handleOptimizationApply(suggestion)}
                        className="px-2 py-1 text-xs bg-primary text-primary-foreground rounded hover:bg-primary/90"
                      >
                        Apply
                      </button>
                    </div>
                    <div className="flex items-center gap-4 text-xs">
                      <span className={cn(
                        "font-medium",
                        suggestion.impact === 'high' && "text-green-500",
                        suggestion.impact === 'medium' && "text-yellow-500",
                        suggestion.impact === 'low' && "text-gray-500",
                      )}>
                        Impact: {suggestion.impact}
                      </span>
                      <span className={cn(
                        "font-medium",
                        suggestion.effort === 'low' && "text-green-500",
                        suggestion.effort === 'medium' && "text-yellow-500",
                        suggestion.effort === 'high' && "text-red-500",
                      )}>
                        Effort: {suggestion.effort}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {showVariables && (
            <div className="p-4 border-b">
              <h3 className="font-semibold mb-4">Variables</h3>
              <div className="space-y-2">
                {workflow.variables.map(variable => (
                  <div key={variable.id} className="p-2 bg-accent/50 rounded">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">{variable.name}</span>
                      <span className="text-xs text-muted-foreground">{variable.type}</span>
                    </div>
                    {variable.defaultValue && (
                      <div className="text-xs text-muted-foreground mt-1">
                        Default: {JSON.stringify(variable.defaultValue)}
                      </div>
                    )}
                  </div>
                ))}
                <button className="w-full px-3 py-1.5 bg-primary text-primary-foreground rounded text-sm hover:bg-primary/90">
                  <Plus className="w-3 h-3 inline mr-1" />
                  Add Variable
                </button>
              </div>
            </div>
          )}

          {showSettings && (
            <div className="p-4">
              <h3 className="font-semibold mb-4">Workflow Settings</h3>
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium">Timeout (ms)</label>
                  <input
                    type="number"
                    value={workflow.settings.timeout || 60000}
                    onChange={(e) => setWorkflow(prev => ({
                      ...prev,
                      settings: { ...prev.settings, timeout: parseInt(e.target.value) },
                    }))}
                    className="w-full mt-1 px-3 py-1.5 bg-background border rounded"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Max Retries</label>
                  <input
                    type="number"
                    value={workflow.settings.maxRetries || 3}
                    onChange={(e) => setWorkflow(prev => ({
                      ...prev,
                      settings: { ...prev.settings, maxRetries: parseInt(e.target.value) },
                    }))}
                    className="w-full mt-1 px-3 py-1.5 bg-background border rounded"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Parallelism</label>
                  <input
                    type="number"
                    value={workflow.settings.parallelism || 5}
                    onChange={(e) => setWorkflow(prev => ({
                      ...prev,
                      settings: { ...prev.settings, parallelism: parseInt(e.target.value) },
                    }))}
                    className="w-full mt-1 px-3 py-1.5 bg-background border rounded"
                  />
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};