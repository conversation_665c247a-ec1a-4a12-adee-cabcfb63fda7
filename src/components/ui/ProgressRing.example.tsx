import React, { useState, useEffect } from 'react';
import { ProgressRing } from './ProgressRing';

export const ProgressRingExamples: React.FC = () => {
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) return 0;
        return prev + 10;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="p-8 space-y-8">
      <h2 className="text-2xl font-bold mb-4">ProgressRing Examples</h2>
      
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Different Sizes</h3>
        <div className="flex items-center space-x-8">
          <div className="text-center">
            <p className="text-sm mb-2">Small</p>
            <ProgressRing value={75} size="sm" />
          </div>
          <div className="text-center">
            <p className="text-sm mb-2">Medium</p>
            <ProgressRing value={75} size="md" />
          </div>
          <div className="text-center">
            <p className="text-sm mb-2">Large</p>
            <ProgressRing value={75} size="lg" />
          </div>
          <div className="text-center">
            <p className="text-sm mb-2">Extra Large</p>
            <ProgressRing value={75} size="xl" />
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Different Colors</h3>
        <div className="flex items-center space-x-8">
          <ProgressRing value={80} color="primary" />
          <ProgressRing value={80} color="success" />
          <ProgressRing value={80} color="warning" />
          <ProgressRing value={80} color="error" />
          <ProgressRing value={80} color="secondary" />
        </div>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Without Percentage Display</h3>
        <ProgressRing value={60} showValue={false} />
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Custom Max Value</h3>
        <ProgressRing value={150} max={200} />
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Animated Progress</h3>
        <div className="flex items-center space-x-4">
          <ProgressRing value={progress} />
          <span className="text-sm text-muted-foreground">
            Current: {progress}%
          </span>
        </div>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Custom Stroke Width</h3>
        <div className="flex items-center space-x-8">
          <ProgressRing value={90} strokeWidth={2} />
          <ProgressRing value={90} strokeWidth={4} />
          <ProgressRing value={90} strokeWidth={6} />
        </div>
      </div>
    </div>
  );
};

export default ProgressRingExamples;