import React from 'react';
import { LoadingSpinner } from './LoadingSpinner';

export const LoadingSpinnerExamples: React.FC = () => {
  return (
    <div className="p-8 space-y-8">
      <h2 className="text-2xl font-bold mb-4">LoadingSpinner Examples</h2>
      
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Different Sizes</h3>
        <div className="flex items-center space-x-4">
          <LoadingSpinner size="sm" />
          <LoadingSpinner size="md" />
          <LoadingSpinner size="lg" />
          <LoadingSpinner size="xl" />
        </div>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Different Colors</h3>
        <div className="flex items-center space-x-4">
          <LoadingSpinner color="primary" />
          <LoadingSpinner color="secondary" />
          <LoadingSpinner color="accent" />
          <LoadingSpinner color="muted" />
        </div>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold">With Custom Label</h3>
        <LoadingSpinner label="Loading data..." />
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Centered in Container</h3>
        <div className="h-32 border rounded-lg flex items-center justify-center">
          <LoadingSpinner size="lg" />
        </div>
      </div>
    </div>
  );
};

export default LoadingSpinnerExamples;