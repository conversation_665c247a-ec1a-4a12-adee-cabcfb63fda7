import React, { useRef, useEffect, useState } from 'react';
import { cn } from '@/lib/utils';

interface LiquidGlassCardProps extends React.HTMLAttributes<HTMLDivElement> {
  children?: React.ReactNode;
  glowIntensity?: 'sm' | 'md' | 'lg';
  shadowIntensity?: 'sm' | 'md' | 'lg';
  borderRadius?: string;
  blurIntensity?: 'sm' | 'md' | 'lg';
  draggable?: boolean;
  className?: string;
}

export const LiquidGlassCard = React.forwardRef<HTMLDivElement, LiquidGlassCardProps>(
  (
    {
      children,
      glowIntensity = 'md',
      shadowIntensity = 'md',
      borderRadius = '16px',
      blurIntensity = 'md',
      draggable = false,
      className,
      style,
      ...props
    },
    ref
  ) => {
    const cardRef = useRef<HTMLDivElement>(null);
    const [position, setPosition] = useState({ x: 0, y: 0 });
    const [isDragging, setIsDragging] = useState(false);
    const [dragStart, setDragStart] = useState({ x: 0, y: 0 });

    const blurValues = {
      sm: '8px',
      md: '12px',
      lg: '20px',
    };

    const glowValues = {
      sm: '0 0 20px rgba(255, 255, 255, 0.1)',
      md: '0 0 40px rgba(255, 255, 255, 0.15)',
      lg: '0 0 60px rgba(255, 255, 255, 0.2)',
    };

    const shadowValues = {
      sm: '0 8px 32px rgba(0, 0, 0, 0.1)',
      md: '0 8px 32px rgba(0, 0, 0, 0.2)',
      lg: '0 8px 32px rgba(0, 0, 0, 0.3)',
    };

    useEffect(() => {
      if (!draggable || !isDragging) return;

      const handleMouseMove = (e: MouseEvent) => {
        setPosition({
          x: e.clientX - dragStart.x,
          y: e.clientY - dragStart.y,
        });
      };

      const handleMouseUp = () => {
        setIsDragging(false);
      };

      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);

      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }, [isDragging, dragStart, draggable]);

    const handleMouseDown = (e: React.MouseEvent) => {
      if (!draggable) return;
      
      const rect = cardRef.current?.getBoundingClientRect();
      if (rect) {
        setDragStart({
          x: e.clientX - position.x,
          y: e.clientY - position.y,
        });
        setIsDragging(true);
      }
    };

    return (
      <div
        ref={ref || cardRef}
        className={cn(
          'relative backdrop-blur-md bg-gradient-to-br from-white/10 to-white/5 border border-white/20 transition-all duration-300',
          isDragging && 'cursor-grabbing scale-105',
          draggable && !isDragging && 'cursor-grab',
          className
        )}
        style={{
          borderRadius,
          backdropFilter: `blur(${blurValues[blurIntensity]})`,
          WebkitBackdropFilter: `blur(${blurValues[blurIntensity]})`,
          boxShadow: `${glowValues[glowIntensity]}, ${shadowValues[shadowIntensity]}`,
          transform: draggable ? `translate(${position.x}px, ${position.y}px)` : undefined,
          ...style,
        }}
        onMouseDown={handleMouseDown}
        {...props}
      >
        <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent rounded-[inherit] pointer-events-none" />
        <div className="relative z-10">
          {children}
        </div>
      </div>
    );
  }
);

LiquidGlassCard.displayName = 'LiquidGlassCard';