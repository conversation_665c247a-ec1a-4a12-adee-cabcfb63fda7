import React from 'react';
import { Timeline } from './Timeline';
import { Button } from './button';

export const TimelineExample: React.FC = () => {
  const verticalItems = [
    {
      id: '1',
      title: 'Project Started',
      description: 'Initial planning and requirements gathering',
      timestamp: '2 hours ago',
      status: 'completed' as const,
      icon: '🚀',
    },
    {
      id: '2',
      title: 'Design Phase',
      description: 'Creating wireframes and mockups',
      timestamp: '1 hour ago',
      status: 'active' as const,
      icon: '🎨',
    },
    {
      id: '3',
      title: 'Development',
      description: 'Building the core features',
      timestamp: '30 minutes ago',
      status: 'pending' as const,
      icon: '💻',
    },
    {
      id: '4',
      title: 'Testing',
      description: 'Quality assurance and bug fixes',
      timestamp: 'Scheduled for tomorrow',
      status: 'pending' as const,
      icon: '🧪',
    },
  ];

  const horizontalItems = [
    {
      id: '1',
      title: 'Step 1',
      description: 'Account Setup',
      timestamp: 'Completed',
      status: 'completed' as const,
    },
    {
      id: '2',
      title: 'Step 2',
      description: 'Profile Info',
      timestamp: 'In Progress',
      status: 'active' as const,
    },
    {
      id: '3',
      title: 'Step 3',
      description: 'Preferences',
      timestamp: 'Pending',
      status: 'pending' as const,
    },
    {
      id: '4',
      title: 'Step 4',
      description: 'Complete',
      timestamp: 'Pending',
      status: 'pending' as const,
    },
  ];

  const orderItems = [
    {
      id: '1',
      title: 'Order Placed',
      description: 'Order #12345 has been received',
      timestamp: 'Today, 2:30 PM',
      status: 'completed' as const,
      action: <Button size="sm" variant="outline">View Details</Button>,
    },
    {
      id: '2',
      title: 'Processing',
      description: 'Your order is being prepared',
      timestamp: 'Today, 3:15 PM',
      status: 'active' as const,
      action: <Button size="sm" variant="outline">Track</Button>,
    },
    {
      id: '3',
      title: 'Shipped',
      description: 'Package is on its way',
      timestamp: 'Today, 4:45 PM',
      status: 'pending' as const,
      action: <Button size="sm" variant="outline" disabled>Pending</Button>,
    },
  ];

  return (
    <div className="p-8 space-y-8">
      <div>
        <h2 className="text-2xl font-bold mb-4">Timeline Examples</h2>
        <p className="text-gray-600 mb-6">Different timeline configurations and use cases</p>
      </div>

      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-semibold mb-4">Vertical Timeline</h3>
          <div className="max-w-2xl">
            <Timeline items={verticalItems} />
          </div>
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-4">Horizontal Timeline</h3>
          <div className="border rounded-lg p-4">
            <Timeline 
              items={horizontalItems} 
              orientation="horizontal" 
              className="pb-4"
            />
          </div>
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-4">Dense Timeline</h3>
          <div className="max-w-2xl">
            <Timeline 
              items={verticalItems.slice(0, 3)} 
              dense={true}
              className="border rounded-lg p-4"
            />
          </div>
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-4">Order Tracking Timeline</h3>
          <div className="max-w-2xl">
            <Timeline 
              items={orderItems} 
              className="border rounded-lg p-4"
            />
          </div>
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-4">Without Connectors</h3>
          <div className="max-w-2xl">
            <Timeline 
              items={verticalItems.slice(0, 2)} 
              showConnector={false}
              className="border rounded-lg p-4"
            />
          </div>
        </div>
      </div>
    </div>
  );
};