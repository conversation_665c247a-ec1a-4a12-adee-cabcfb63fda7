import React from 'react';
import { cn } from '@/lib/utils';

export interface TimelineItem {
  id: string;
  title: string;
  description?: string;
  timestamp: string;
  status?: 'completed' | 'active' | 'pending' | 'error';
  icon?: React.ReactNode;
  action?: React.ReactNode;
}

export interface TimelineProps {
  items: TimelineItem[];
  orientation?: 'vertical' | 'horizontal';
  className?: string;
  itemClassName?: string;
  showConnector?: boolean;
  dense?: boolean;
}

const statusColors = {
  completed: 'bg-green-500 border-green-500',
  active: 'bg-blue-500 border-blue-500',
  pending: 'bg-gray-400 border-gray-400',
  error: 'bg-red-500 border-red-500',
};

const statusTextColors = {
  completed: 'text-green-600',
  active: 'text-blue-600',
  pending: 'text-gray-500',
  error: 'text-red-600',
};

export const Timeline: React.FC<TimelineProps> = ({
  items,
  orientation = 'vertical',
  className,
  itemClassName,
  showConnector = true,
  dense = false,
}) => {
  if (orientation === 'horizontal') {
    return (
      <div className={cn('flex space-x-4 overflow-x-auto', className)}>
        {items.map((item, index) => (
          <div key={item.id} className={cn('flex-shrink-0', itemClassName)}>
            <div className="flex items-center">
              <div
                className={cn(
                  'w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-medium',
                  statusColors[item.status || 'pending']
                )}
              >
                {item.icon || index + 1}
              </div>
              {index < items.length - 1 && showConnector && (
                <div className="w-16 h-0.5 bg-gray-300 mx-2" />
              )}
            </div>
            <div className="mt-2 text-center min-w-[120px]">
              <h4 className={cn('font-medium text-sm', statusTextColors[item.status || 'pending'])}>
                {item.title}
              </h4>
              {item.description && (
                <p className="text-xs text-gray-600 mt-1">{item.description}</p>
              )}
              <p className="text-xs text-gray-500 mt-1">{item.timestamp}</p>
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className={cn('space-y-0', className)}>
      {items.map((item, index) => (
        <div key={item.id} className={cn('relative', itemClassName)}>
          <div className="flex">
            <div className="flex-shrink-0 w-12">
              <div className="relative">
                <div
                  className={cn(
                    'w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-medium',
                    statusColors[item.status || 'pending']
                  )}
                >
                  {item.icon || index + 1}
                </div>
                {index < items.length - 1 && showConnector && (
                  <div className="absolute top-8 left-1/2 transform -translate-x-1/2 w-0.5 h-full bg-gray-300" />
                )}
              </div>
            </div>
            <div className={cn('flex-1 pb-8', dense && 'pb-4')}>
              <div className="bg-white rounded-lg shadow-sm border p-4">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <h4 className={cn('font-medium', statusTextColors[item.status || 'pending'])}>
                      {item.title}
                    </h4>
                    {item.description && (
                      <p className="text-gray-600 text-sm mt-1">{item.description}</p>
                    )}
                    <p className="text-xs text-gray-500 mt-2">{item.timestamp}</p>
                  </div>
                  {item.action && <div className="ml-4">{item.action}</div>}
                </div>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};