import React from 'react';
import { Badge } from './badge';

export const BadgeExample: React.FC = () => {
  return (
    <div className="p-8 space-y-6">
      <div>
        <h2 className="text-2xl font-bold mb-4">Badge Examples</h2>
        <p className="text-gray-600 mb-6">Various badge styles and use cases</p>
      </div>

      <div className="space-y-4">
        <div>
          <h3 className="text-lg font-semibold mb-2">Variants</h3>
          <div className="flex flex-wrap gap-2">
            <Badge variant="default">Default</Badge>
            <Badge variant="secondary">Secondary</Badge>
            <Badge variant="destructive">Destructive</Badge>
            <Badge variant="outline">Outline</Badge>
          </div>
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-2">Status Indicators</h3>
          <div className="flex flex-wrap gap-2">
            <Badge className="bg-green-500 hover:bg-green-600">Online</Badge>
            <Badge className="bg-yellow-500 hover:bg-yellow-600">Away</Badge>
            <Badge className="bg-red-500 hover:bg-red-600">Offline</Badge>
            <Badge className="bg-blue-500 hover:bg-blue-600">Active</Badge>
          </div>
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-2">Notification Badges</h3>
          <div className="flex flex-wrap gap-2 items-center">
            <Badge className="bg-red-500">99+</Badge>
            <Badge className="bg-blue-500">New</Badge>
            <Badge className="bg-purple-500">Pro</Badge>
            <Badge className="bg-orange-500">Hot</Badge>
          </div>
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-2">With Icons</h3>
          <div className="flex flex-wrap gap-2">
            <Badge variant="default">
              <span className="mr-1">🔥</span>
              Trending
            </Badge>
            <Badge variant="secondary">
              <span className="mr-1">✨</span>
              New
            </Badge>
            <Badge className="bg-green-100 text-green-800">
              <span className="mr-1">✓</span>
              Verified
            </Badge>
          </div>
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-2">Sizes</h3>
          <div className="flex flex-wrap gap-2 items-center">
            <Badge className="text-xs px-1.5 py-0.5">Small</Badge>
            <Badge className="text-sm px-2 py-1">Medium</Badge>
            <Badge className="text-base px-3 py-1.5">Large</Badge>
          </div>
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-2">Interactive Badges</h3>
          <div className="flex flex-wrap gap-2">
            <Badge 
              variant="outline" 
              className="cursor-pointer hover:bg-gray-100 transition-colors"
              onClick={() => alert('Badge clicked!')}
            >
              Clickable
            </Badge>
            <Badge 
              className="cursor-pointer hover:scale-110 transition-transform bg-gradient-to-r from-purple-500 to-pink-500"
              onClick={() => alert('Special badge!')}
            >
              Gradient
            </Badge>
          </div>
        </div>
      </div>
    </div>
  );
};