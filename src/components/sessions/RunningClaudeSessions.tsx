import React, { useState, useEffect, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { 
  Play, 
  Loader2, 
  Terminal, 
  AlertCircle, 
  Layers,
  RefreshCw,
  Pause,
  Square,
  Activity,
  Cpu,
  Clock,
  FolderOpen,
  Zap,
  Minimize2,
  Maximize2,
  Eye,
  EyeOff,
  MoreVertical,
  ExternalLink,
  MessageSquare,
  Bot
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { api, type ProcessInfo, type Session } from "@/lib/api";
import { cn } from "@/lib/utils";
import { formatISOTimestamp } from "@/lib/date-utils";

interface RunningClaudeSessionsProps {
  onSessionClick?: (session: Session) => void;
  className?: string;
  showCompact?: boolean;
}

const SessionCard: React.FC<{
  session: ProcessInfo;
  isBackground: boolean;
  onResume: () => void;
  onToggleBackground: () => void;
  onStop: () => void;
  index: number;
}> = ({ session, isBackground, onResume, onToggleBackground, onStop, index }) => {
  const [isHovered, setIsHovered] = useState(false);
  const sessionId = 'ClaudeSession' in session.process_type 
    ? session.process_type.ClaudeSession.session_id 
    : '';

  const getRuntime = () => {
    const start = new Date(session.started_at);
    const now = new Date();
    const diff = now.getTime() - start.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `${days}d ${hours % 24}h`;
    if (hours > 0) return `${hours}h ${minutes % 60}m`;
    return `${minutes}m`;
  };

  const getCpuUsage = () => {
    // Simulated CPU usage - in real app, get from backend
    return Math.floor(Math.random() * 30 + 10);
  };

  const getMemoryUsage = () => {
    // Simulated memory usage - in real app, get from backend
    return Math.floor(Math.random() * 200 + 50);
  };

  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: 20 }}
      transition={{ duration: 0.2, delay: index * 0.05 }}
      whileHover={{ scale: 1.02 }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <Card className={cn(
        "transition-all duration-200",
        "border-border/50 bg-card/80 backdrop-blur-sm",
        isBackground ? "opacity-60" : "hover:shadow-lg hover:border-primary/30",
        !isBackground && "cursor-pointer"
      )}>
        <CardContent className="p-4">
          <div className="flex items-start justify-between gap-3">
            {/* Session Info */}
            <div className="flex-1 space-y-3">
              {/* Header */}
              <div className="flex items-start gap-3">
                <div className={cn(
                  "h-10 w-10 rounded-xl flex items-center justify-center flex-shrink-0",
                  isBackground 
                    ? "bg-muted/50" 
                    : "bg-gradient-to-br from-green-500/20 to-green-600/20"
                )}>
                  {isBackground ? (
                    <EyeOff className="h-5 w-5 text-muted-foreground" />
                  ) : (
                    <Activity className="h-5 w-5 text-green-500" />
                  )}
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2">
                    <h4 className="font-medium text-sm truncate">
                      Session {sessionId.slice(0, 8)}
                    </h4>
                    <div className="flex items-center gap-1">
                      {!isBackground && (
                        <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                      )}
                      <Badge 
                        variant={isBackground ? "secondary" : "default"}
                        className={cn(
                          "text-xs",
                          !isBackground && "bg-green-500/10 text-green-600 dark:text-green-400 border-green-500/30"
                        )}
                      >
                        {isBackground ? "Background" : "Active"}
                      </Badge>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3 mt-1 text-xs text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      <span>{getRuntime()}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <FolderOpen className="h-3 w-3" />
                      <span className="truncate max-w-[150px]">
                        {session.project_path.split('/').pop()}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-3 gap-2">
                <div className="space-y-1">
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-muted-foreground">CPU</span>
                    <span className="text-xs font-medium">{getCpuUsage()}%</span>
                  </div>
                  <Progress value={getCpuUsage()} className="h-1" />
                </div>
                
                <div className="space-y-1">
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-muted-foreground">Memory</span>
                    <span className="text-xs font-medium">{getMemoryUsage()}MB</span>
                  </div>
                  <Progress value={getMemoryUsage() / 5} className="h-1" />
                </div>
                
                <div className="space-y-1">
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-muted-foreground">PID</span>
                    <span className="text-xs font-medium">{session.pid}</span>
                  </div>
                  <div className="h-1 bg-muted rounded-full" />
                </div>
              </div>

              {/* Additional Info */}
              <div className="flex items-center gap-2 flex-wrap">
                <Badge variant="outline" className="text-xs">
                  <Terminal className="h-3 w-3 mr-1" />
                  Claude CLI
                </Badge>
                {session.message_count && (
                  <Badge variant="outline" className="text-xs">
                    <MessageSquare className="h-3 w-3 mr-1" />
                    {session.message_count} msgs
                  </Badge>
                )}
                {session.agent_active && (
                  <Badge variant="outline" className="text-xs bg-blue-500/10">
                    <Bot className="h-3 w-3 mr-1" />
                    Agent Active
                  </Badge>
                )}
              </div>
            </div>

            {/* Actions */}
            <div className="flex flex-col gap-1">
              {!isBackground && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        size="sm"
                        variant="default"
                        className="h-8 px-3"
                        onClick={onResume}
                      >
                        <ExternalLink className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Open session</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      size="sm"
                      variant="ghost"
                      className="h-8 w-8 p-0"
                      onClick={onToggleBackground}
                    >
                      {isBackground ? (
                        <Eye className="h-4 w-4" />
                      ) : (
                        <EyeOff className="h-4 w-4" />
                      )}
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    {isBackground ? "Bring to foreground" : "Send to background"}
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    size="sm"
                    variant="ghost"
                    className="h-8 w-8 p-0"
                  >
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={onResume}>
                    <Play className="h-4 w-4 mr-2" />
                    Resume
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={onToggleBackground}>
                    {isBackground ? (
                      <>
                        <Maximize2 className="h-4 w-4 mr-2" />
                        Foreground
                      </>
                    ) : (
                      <>
                        <Minimize2 className="h-4 w-4 mr-2" />
                        Background
                      </>
                    )}
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem 
                    className="text-destructive"
                    onClick={onStop}
                  >
                    <Square className="h-4 w-4 mr-2" />
                    Stop Session
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export const RunningClaudeSessions: React.FC<RunningClaudeSessionsProps> = ({
  onSessionClick,
  className,
  showCompact = false,
}) => {
  const [runningSessions, setRunningSessions] = useState<ProcessInfo[]>([]);
  const [backgroundSessions, setBackgroundSessions] = useState<Set<string>>(new Set());
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());

  useEffect(() => {
    loadRunningSessions();
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(() => {
      loadRunningSessions(true);
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyPress = async (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 'b') {
        e.preventDefault();
        const activeSession = runningSessions.find(s => 
          'ClaudeSession' in s.process_type && 
          !backgroundSessions.has(s.process_type.ClaudeSession.session_id)
        );
        
        if (activeSession && 'ClaudeSession' in activeSession.process_type) {
          const sessionId = activeSession.process_type.ClaudeSession.session_id;
          await toggleBackground(sessionId);
        }
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [runningSessions, backgroundSessions]);

  const loadRunningSessions = async (silent = false) => {
    if (!silent) setLoading(true);
    setIsRefreshing(true);
    
    try {
      const sessions = await api.listRunningClaudeSessions();
      setRunningSessions(sessions);
      setError(null);
      setLastRefresh(new Date());
    } catch (err) {
      setError("Failed to load running sessions");
    } finally {
      setLoading(false);
      setIsRefreshing(false);
    }
  };

  const toggleBackground = async (sessionId: string) => {
    try {
      if (backgroundSessions.has(sessionId)) {
        await api.bringToForeground(sessionId);
        setBackgroundSessions(prev => {
          const newSet = new Set(prev);
          newSet.delete(sessionId);
          return newSet;
        });
      } else {
        await api.sendToBackground(sessionId);
        setBackgroundSessions(prev => {
          const newSet = new Set(prev);
          newSet.add(sessionId);
          return newSet;
        });
      }
    } catch (err) {
    }
  };

  const handleResumeSession = (processInfo: ProcessInfo) => {
    if ('ClaudeSession' in processInfo.process_type) {
      const sessionId = processInfo.process_type.ClaudeSession.session_id;
      
      const session: Session = {
        id: sessionId,
        project_id: processInfo.project_path.replace(/[^a-zA-Z0-9]/g, '-'),
        project_path: processInfo.project_path,
        created_at: new Date(processInfo.started_at).getTime() / 1000,
      };
      
      const event = new CustomEvent('claude-session-selected', { 
        detail: { session, projectPath: processInfo.project_path } 
      });
      window.dispatchEvent(event);
      
      onSessionClick?.(session);
    }
  };

  const handleStopSession = async (processInfo: ProcessInfo) => {
    if ('ClaudeSession' in processInfo.process_type) {
      const sessionId = processInfo.process_type.ClaudeSession.session_id;
      try {
        await api.stopSession(sessionId);
        await loadRunningSessions();
      } catch (err) {
      }
    }
  };

  if (loading && (!runningSessions || runningSessions.length === 0)) {
    return (
      <div className={cn("flex items-center justify-center py-8", className)}>
        <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
      </div>
    );
  }

  if (error) {
    return (
      <Card className={cn("border-destructive/50 bg-destructive/5", className)}>
        <CardContent className="py-6">
          <div className="flex items-center gap-2 text-destructive">
            <AlertCircle className="h-5 w-5" />
            <span className="text-sm font-medium">{error}</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!runningSessions || runningSessions.length === 0) {
    return null;
  }

  const activeSessions = (runningSessions || []).filter(s => 
    'ClaudeSession' in s.process_type && 
    !backgroundSessions.has(s.process_type.ClaudeSession.session_id)
  );
  
  const bgSessions = (runningSessions || []).filter(s => 
    'ClaudeSession' in s.process_type && 
    backgroundSessions.has(s.process_type.ClaudeSession.session_id)
  );

  if (showCompact) {
    return (
      <Card className={cn("border-primary/20 bg-card/80 backdrop-blur-sm", className)}>
        <CardContent className="py-3 px-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="relative">
                <Activity className="h-4 w-4 text-green-500" />
                <div className="absolute -top-1 -right-1 w-2 h-2 bg-green-500 rounded-full animate-pulse" />
              </div>
              <span className="text-sm font-medium">
                {runningSessions?.length || 0} Active Session{runningSessions?.length !== 1 ? 's' : ''}
              </span>
            </div>
            <Button
              size="sm"
              variant="ghost"
              onClick={() => loadRunningSessions()}
              disabled={isRefreshing}
            >
              <RefreshCw className={cn(
                "h-3.5 w-3.5",
                isRefreshing && "animate-spin"
              )} />
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* Header */}
      <Card className="border-primary/20 bg-card/80 backdrop-blur-sm">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="relative">
                <Cpu className="h-5 w-5 text-primary" />
                <motion.div 
                  className="absolute inset-0"
                  animate={{ rotate: 360 }}
                  transition={{ duration: 10, repeat: Infinity, ease: "linear" }}
                >
                  <div className="h-1 w-1 bg-primary rounded-full absolute -top-1 -right-1" />
                </motion.div>
              </div>
              <CardTitle className="text-base">Running Sessions</CardTitle>
              <Badge variant="default" className="text-xs">
                {activeSessions.length} active
              </Badge>
              {bgSessions.length > 0 && (
                <Badge variant="secondary" className="text-xs">
                  {bgSessions.length} background
                </Badge>
              )}
            </div>

            <div className="flex items-center gap-2">
              <span className="text-xs text-muted-foreground">
                Last updated: {lastRefresh.toLocaleTimeString()}
              </span>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => loadRunningSessions()}
                disabled={isRefreshing}
              >
                <RefreshCw className={cn(
                  "h-4 w-4",
                  isRefreshing && "animate-spin"
                )} />
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Active Sessions */}
      {activeSessions.length > 0 && (
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-muted-foreground flex items-center gap-2">
            <Zap className="h-4 w-4" />
            Active Sessions
          </h4>
          <div className="space-y-2">
            <AnimatePresence mode="popLayout">
              {activeSessions.map((session, index) => {
                const sessionId = 'ClaudeSession' in session.process_type 
                  ? session.process_type.ClaudeSession.session_id 
                  : '';
                
                return (
                  <SessionCard
                    key={sessionId}
                    session={session}
                    isBackground={false}
                    onResume={() => handleResumeSession(session)}
                    onToggleBackground={() => toggleBackground(sessionId)}
                    onStop={() => handleStopSession(session)}
                    index={index}
                  />
                );
              })}
            </AnimatePresence>
          </div>
        </div>
      )}

      {/* Background Sessions */}
      {bgSessions.length > 0 && (
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-muted-foreground flex items-center gap-2">
            <Layers className="h-4 w-4" />
            Background Sessions
          </h4>
          <div className="space-y-2">
            <AnimatePresence mode="popLayout">
              {bgSessions.map((session, index) => {
                const sessionId = 'ClaudeSession' in session.process_type 
                  ? session.process_type.ClaudeSession.session_id 
                  : '';
                
                return (
                  <SessionCard
                    key={sessionId}
                    session={session}
                    isBackground={true}
                    onResume={() => handleResumeSession(session)}
                    onToggleBackground={() => toggleBackground(sessionId)}
                    onStop={() => handleStopSession(session)}
                    index={index}
                  />
                );
              })}
            </AnimatePresence>
          </div>
        </div>
      )}

      {/* Keyboard Shortcut Hint */}
      <div className="text-xs text-muted-foreground text-center">
        Press <kbd className="px-1.5 py-0.5 bg-muted rounded text-xs">Ctrl+B</kbd> to toggle background mode
      </div>
    </div>
  );
};

RunningClaudeSessions.displayName = "RunningClaudeSessions";