/**
 * Refactored ClaudeCodeSession component using new abstractions
 * This version uses centralized types, utilities, hooks, and services
 */

import React, { useState, useEffect, useRef, useMemo, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { 
  FolderOpen,
  ChevronUp,
  ChevronDown,
  X,
  Hash,
  MessageCircle,
  Sparkles,
  BarChart3,
  TrendingUp,
  Minimize2,
  Maximize2
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Popover } from "@/components/ui/popover";
import { api, type Session } from "@/lib/api";
import { cn } from "@/lib/utils";
import { open } from "@tauri-apps/plugin-dialog";
import { FloatingPromptInput, type FloatingPromptInputRef } from "../inputs/FloatingPromptInput";
import { ErrorBoundary } from "../common/ErrorBoundary";
import { TimelineNavigator } from "../navigation/TimelineNavigator";
import { CheckpointSettings } from "../settings/CheckpointSettings";
import { SlashCommandsManager } from "../editors/SlashCommandsManager";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { SplitPane } from "@/components/ui/split-pane";
import { WebviewPreview } from "../display/WebviewPreview";
import { PlanModeIndicator } from "../display/PlanModeIndicator";
import { ExtendedThinkingIndicator } from "../display/ExtendedThinkingIndicator";
import { SuperClaudePanel } from "./superclaude/ui/SuperClaudePanel";
import { useTrackEvent, useComponentMetrics, useWorkflowTracking } from "@/hooks";

// Import new centralized types
import type { 
  ClaudeStreamMessage, 
  SessionMetadata, 
  SessionUIState,
  SessionErrorDetails 
} from "@/types/session";

// Import new utilities
import { 
  detectQuestion, 
  extractMessageText, 
  calculateTokenUsage,
  extractSessionId,
  sanitizePrompt,
  shouldAutoCheckpoint
} from "@/utils/sessionUtils";

// Import new custom hook
import { useClaudeEventListeners, useSessionEvents } from "@/hooks/useClaudeEventListeners";

// Import new service layer
import { sessionService } from "@/services/sessionService";

// Import extracted components and hooks
import { MessageList } from "./claude-code-session/MessageList";
import { PromptQueue } from "./claude-code-session/PromptQueue";
import { SessionHeader } from "./claude-code-session/SessionHeader";
import { useCheckpoints } from "./claude-code-session/useCheckpoints";
import { usePlanMode } from "./claude-code-session/PlanModeManager";
import { useExtendedThinking } from "./claude-code-session/ExtendedThinkingManager";
import { useSessionMetrics } from "./claude-code-session/SessionMetricsTracker";
import { usePreview } from "./claude-code-session/PreviewManager";
import { SessionMetricsVisualizer } from "./claude-code-session/SessionMetricsVisualizer";
import { ErrorRecoverySystem, type ErrorDetails } from "./claude-code-session/ErrorRecoverySystem";

interface ClaudeCodeSessionProps {
  session?: Session;
  initialProjectPath?: string;
  onHideRequest?: () => void;
}

export const ClaudeCodeSessionRefactored: React.FC<ClaudeCodeSessionProps> = ({ 
  session, 
  initialProjectPath,
  onHideRequest
}) => {
  // Track component metrics
  const componentMetrics = useComponentMetrics('ClaudeCodeSession');
  const trackEvent = useTrackEvent();
  const workflowTracking = useWorkflowTracking();
  
  // Core state
  const [projectPath, setProjectPath] = useState(initialProjectPath || session?.project_path || "");
  const [messages, setMessages] = useState<ClaudeStreamMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [rawJsonlOutput, setRawJsonlOutput] = useState<string[]>([]);
  const [isFirstPrompt, setIsFirstPrompt] = useState(!session);
  const [resumeFlag, setResumeFlag] = useState(false);
  const [totalTokens, setTotalTokens] = useState(0);
  const [extractedSessionInfo, setExtractedSessionInfo] = useState<{ sessionId: string; projectId: string } | null>(null);
  const [claudeSessionId, setClaudeSessionId] = useState<string | null>(null);
  const [currentError, setCurrentError] = useState<ErrorDetails | null>(null);
  
  // UI state - now using the SessionUIState type
  const [uiState, setUIState] = useState<SessionUIState>({
    isLoading: false,
    error: null,
    showTimeline: false,
    showSettings: false,
    showForkDialog: false,
    showSlashCommandsSettings: false,
    showSuperClaude: false,
    isInBackground: false,
    copyPopoverOpen: false
  });
  
  // Fork dialog state
  const [forkCheckpointId, setForkCheckpointId] = useState<string | null>(null);
  const [forkSessionName, setForkSessionName] = useState("");
  
  // Queue state
  const [queuedPrompts, setQueuedPrompts] = useState<Array<{ prompt: string; timestamp: number }>>([]);
  const queuedPromptsRef = useRef(queuedPrompts);
  
  // Refs
  const floatingPromptRef = useRef<FloatingPromptInputRef>(null);
  const isMountedRef = useRef(true);
  const messagesRef = useRef<ClaudeStreamMessage[]>([]);
  const sessionStartTime = useRef<number>(Date.now());
  
  // Use the new session metrics hook
  const {
    metrics: sessionMetrics,
    trackPrompt,
    trackToolExecution,
    trackFileOperation,
    trackError,
    trackCheckpoint,
    getMetricsSummary
  } = useSessionMetrics(!!session);
  
  // Use checkpoint hook
  const {
    checkpoints,
    createCheckpoint,
    restoreCheckpoint,
    deleteCheckpoint,
    isCheckpointEnabled,
    autoCheckpointThreshold
  } = useCheckpoints(claudeSessionId || '', projectPath);
  
  // Use plan mode hook
  const {
    isPlanMode,
    planBuffer,
    enablePlanMode,
    disablePlanMode,
    addToPlanBuffer,
    clearPlanBuffer,
    approvePlan,
    rejectPlan
  } = usePlanMode();
  
  // Use extended thinking hook
  const {
    isExtendedThinking,
    thinkingDuration,
    enableExtendedThinking,
    disableExtendedThinking,
    updateThinkingDuration
  } = useExtendedThinking();
  
  // Use preview hook
  const {
    previewUrl,
    isPreviewOpen,
    setPreviewUrl,
    openPreview,
    closePreview
  } = usePreview();
  
  // Event handlers
  const handleStreamMessage = useCallback((payload: string) => {
    try {
      if (!isMountedRef.current) return;
      
      setRawJsonlOutput((prev) => [...prev, payload]);
      const message = JSON.parse(payload) as ClaudeStreamMessage;
      
      // Extract session ID if present
      const sessionId = extractSessionId(message);
      if (sessionId && !claudeSessionId) {
        setClaudeSessionId(sessionId);
      }
      
      // Track metrics
      if (message.type === "assistant") {
        sessionMetrics.current.codeBlocksGenerated++;
      }
      
      // Check for auto-checkpoint
      if (shouldAutoCheckpoint(message, messages.length)) {
        createCheckpoint('auto', 'minor');
      }
      
      // Update messages
      setMessages((prev) => [...prev, message]);
      messagesRef.current = [...messagesRef.current, message];
      
      // Update token count
      const tokens = calculateTokenUsage([message]);
      setTotalTokens((prev) => prev + tokens);
      
    } catch (err) {
      console.error('Failed to parse stream message:', err);
      trackError(err as Error);
    }
  }, [claudeSessionId, messages.length, createCheckpoint, trackError]);
  
  const handleError = useCallback((errorPayload: string) => {
    console.error("Claude error:", errorPayload);
    if (isMountedRef.current) {
      setError(errorPayload);
      setCurrentError({
        code: 'CLAUDE_ERROR',
        message: errorPayload,
        timestamp: Date.now(),
        recoverable: true
      });
      trackError(new Error(errorPayload));
    }
  }, [trackError]);
  
  const handleComplete = useCallback(async (payload: boolean) => {
    console.log('[ClaudeCodeSession] Session complete:', payload);
    if (isMountedRef.current) {
      setIsLoading(false);
      
      // Process queued prompts
      if (queuedPromptsRef.current.length > 0) {
        const nextPrompt = queuedPromptsRef.current.shift();
        if (nextPrompt) {
          await handleSubmit(nextPrompt.prompt);
        }
      }
    }
  }, []);
  
  // Use the new event listeners hook
  const { cleanup: cleanupListeners, updateSessionId } = useSessionEvents(
    claudeSessionId || undefined,
    handleStreamMessage,
    handleError,
    handleComplete
  );
  
  // Submit handler using the new service layer
  const handleSubmit = useCallback(async (prompt: string) => {
    if (!prompt.trim() || !projectPath) return;
    
    const sanitizedPrompt = sanitizePrompt(prompt);
    setIsLoading(true);
    setError(null);
    
    trackPrompt(sanitizedPrompt);
    
    try {
      const model = api.getDefaultModel() || "claude-3-5-sonnet-20241022";
      
      if (isFirstPrompt) {
        await sessionService.executeClaudeCode({
          projectPath,
          prompt: sanitizedPrompt,
          model
        });
        setIsFirstPrompt(false);
      } else if (resumeFlag && session?.id) {
        await sessionService.resumeClaudeCode({
          projectPath,
          sessionId: session.id,
          prompt: sanitizedPrompt,
          model
        });
        setResumeFlag(false);
      } else {
        await sessionService.continueClaudeCode({
          projectPath,
          prompt: sanitizedPrompt,
          model
        });
      }
      
    } catch (error) {
      console.error("Failed to execute Claude code:", error);
      setError(error instanceof Error ? error.message : String(error));
      setIsLoading(false);
      trackError(error as Error);
    }
  }, [projectPath, isFirstPrompt, resumeFlag, session, trackPrompt, trackError]);
  
  // Folder selection
  const selectFolder = async () => {
    try {
      const selectedPath = await open({
        directory: true,
        multiple: false,
        title: "Select Project Folder"
      });
      
      if (selectedPath && typeof selectedPath === 'string') {
        setProjectPath(selectedPath);
        trackEvent('session_folder_selected', { path: selectedPath });
      }
    } catch (error) {
      console.error("Failed to select folder:", error);
    }
  };
  
  // Cleanup on unmount
  useEffect(() => {
    isMountedRef.current = true;
    
    return () => {
      isMountedRef.current = false;
      cleanupListeners();
    };
  }, [cleanupListeners]);
  
  // Update refs when state changes
  useEffect(() => {
    queuedPromptsRef.current = queuedPrompts;
  }, [queuedPrompts]);
  
  useEffect(() => {
    messagesRef.current = messages;
  }, [messages]);
  
  // Load session history if resuming
  useEffect(() => {
    if (session?.id && projectPath) {
      sessionService.loadSessionHistory(session.id, projectPath)
        .then((history) => {
          setMessages(history);
          messagesRef.current = history;
          setResumeFlag(true);
        })
        .catch((error) => {
          console.error("Failed to load session history:", error);
        });
    }
  }, [session, projectPath]);
  
  return (
    <ErrorBoundary>
      <div className="flex flex-col h-full bg-background">
        <SessionHeader
          projectPath={projectPath}
          isInBackground={uiState.isInBackground}
          onSelectFolder={selectFolder}
          onToggleBackground={() => setUIState(prev => ({ ...prev, isInBackground: !prev.isInBackground }))}
          onShowTimeline={() => setUIState(prev => ({ ...prev, showTimeline: true }))}
          onShowSettings={() => setUIState(prev => ({ ...prev, showSettings: true }))}
          onShowMetrics={() => setUIState(prev => ({ ...prev, showMetrics: true }))}
          onHide={onHideRequest}
        />
        
        <div className="flex-1 overflow-hidden">
          <MessageList
            messages={messages}
            isLoading={isLoading}
            error={error}
            detectQuestion={detectQuestion}
            extractMessageText={extractMessageText}
          />
        </div>
        
        <FloatingPromptInput
          ref={floatingPromptRef}
          onSubmit={handleSubmit}
          disabled={isLoading || !projectPath}
          projectPath={projectPath}
          isFirstPrompt={isFirstPrompt}
          onQueuePrompt={(prompt) => {
            setQueuedPrompts(prev => [...prev, { prompt, timestamp: Date.now() }]);
          }}
        />
        
        {queuedPrompts.length > 0 && (
          <PromptQueue
            prompts={queuedPrompts}
            onRemove={(index) => {
              setQueuedPrompts(prev => prev.filter((_, i) => i !== index));
            }}
            onClear={() => setQueuedPrompts([])}
          />
        )}
        
        {currentError && (
          <ErrorRecoverySystem
            error={currentError}
            onRecover={() => {
              setCurrentError(null);
              setError(null);
            }}
            onDismiss={() => {
              setCurrentError(null);
            }}
          />
        )}
        
        {/* Dialogs and panels */}
        <AnimatePresence>
          {uiState.showTimeline && (
            <TimelineNavigator
              sessionId={claudeSessionId || ''}
              projectPath={projectPath}
              checkpoints={checkpoints}
              onClose={() => setUIState(prev => ({ ...prev, showTimeline: false }))}
              onRestore={restoreCheckpoint}
              onFork={(checkpointId) => {
                setForkCheckpointId(checkpointId);
                setUIState(prev => ({ ...prev, showForkDialog: true }))}
              }
            />
          )}
          
          {uiState.showSettings && (
            <CheckpointSettings
              sessionId={claudeSessionId || ''}
              projectPath={projectPath}
              onClose={() => setUIState(prev => ({ ...prev, showSettings: false }))}
            />
          )}
          
          {uiState.showSuperClaude && (
            <SuperClaudePanel
              sessionId={claudeSessionId || ''}
              projectPath={projectPath}
              onClose={() => setUIState(prev => ({ ...prev, showSuperClaude: false }))}
            />
          )}
        </AnimatePresence>
        
        {/* Fork Dialog */}
        <Dialog open={uiState.showForkDialog} onOpenChange={(open) => setUIState(prev => ({ ...prev, showForkDialog: open }))}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Fork from Checkpoint</DialogTitle>
              <DialogDescription>
                Create a new session branch from this checkpoint
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="fork-name">Session Name</Label>
                <Input
                  id="fork-name"
                  value={forkSessionName}
                  onChange={(e) => setForkSessionName(e.target.value)}
                  placeholder="Enter a name for the forked session"
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setUIState(prev => ({ ...prev, showForkDialog: false }))}>
                Cancel
              </Button>
              <Button
                onClick={async () => {
                  if (forkCheckpointId && forkSessionName) {
                    try {
                      const newSessionId = await sessionService.forkFromCheckpoint(
                        forkCheckpointId,
                        forkSessionName,
                        projectPath
                      );
                      trackEvent('session_forked', { 
                        checkpointId: forkCheckpointId,
                        newSessionId 
                      });
                      setUIState(prev => ({ ...prev, showForkDialog: false }));
                    } catch (error) {
                      console.error("Failed to fork checkpoint:", error);
                    }
                  }
                }}
                disabled={!forkSessionName.trim()}
              >
                Fork Session
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
        
        {/* Metrics Visualizer */}
        {uiState.showMetrics && (
          <SessionMetricsVisualizer
            metrics={getMetricsSummary()}
            onClose={() => setUIState(prev => ({ ...prev, showMetrics: false }))}
          />
        )}
        
        {/* Plan Mode Indicator */}
        {isPlanMode && (
          <PlanModeIndicator
            onApprove={approvePlan}
            onReject={rejectPlan}
            planBuffer={planBuffer}
          />
        )}
        
        {/* Extended Thinking Indicator */}
        {isExtendedThinking && (
          <ExtendedThinkingIndicator
            duration={thinkingDuration}
            onStop={disableExtendedThinking}
          />
        )}
      </div>
    </ErrorBoundary>
  );
};