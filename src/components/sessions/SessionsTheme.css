/* Enhanced Sessions Theme - Modern UI Design System */

@layer utilities {
  /* Session-specific color tokens */
  .session-active {
    @apply bg-green-500/10 text-green-600 border-green-500/30;
  }
  
  .session-paused {
    @apply bg-yellow-500/10 text-yellow-600 border-yellow-500/30;
  }
  
  .session-completed {
    @apply bg-blue-500/10 text-blue-600 border-blue-500/30;
  }
  
  .session-error {
    @apply bg-red-500/10 text-red-600 border-red-500/30;
  }
  
  .session-background {
    @apply bg-gray-500/10 text-gray-600 border-gray-500/30;
  }

  /* Agent and MCP specific colors */
  .agent-indicator {
    @apply bg-purple-500/10 text-purple-600 border-purple-500/30;
  }
  
  .mcp-indicator {
    @apply bg-blue-500/10 text-blue-600 border-blue-500/30;
  }
  
  .thinking-indicator {
    @apply bg-orange-500/10 text-orange-600 border-orange-500/30;
  }

  /* Enhanced card variants */
  .session-card {
    @apply bg-card border border-border shadow-lg rounded-lg transition-all duration-300;
    @apply hover:shadow-2xl hover:border-primary/40;
    @apply bg-card/90 backdrop-blur-md;
  }
  
  .session-card-starred {
    @apply border-yellow-500/40 bg-yellow-500/5;
    @apply shadow-yellow-500/10;
  }
  
  .session-card-active {
    @apply border-green-500/40 bg-green-500/5;
    @apply shadow-green-500/10;
  }

  /* Message bubble styles */
  .message-user {
    @apply bg-primary/10 border border-primary/20 rounded-2xl;
    @apply ml-auto max-w-[85%];
  }
  
  .message-assistant {
    @apply bg-muted/50 border border-border/50 rounded-2xl;
    @apply mr-auto max-w-[85%];
  }
  
  .message-system {
    @apply bg-orange-500/5 border border-orange-500/20 rounded-2xl;
    @apply mx-auto max-w-[90%];
  }

  /* Status indicators */
  .status-pulse {
    @apply animate-pulse;
  }
  
  .status-active::before {
    content: '';
    @apply absolute -top-1 -right-1 w-3 h-3;
    @apply bg-green-500 rounded-full animate-pulse;
  }
  
  .status-thinking::before {
    content: '';
    @apply absolute -top-1 -right-1 w-3 h-3;
    @apply bg-orange-500 rounded-full animate-pulse;
  }

  /* Enhanced glassmorphism */
  .glass-panel {
    background: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(12px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    @apply shadow-2xl;
  }
  
  .glass-card-dark {
    background: rgba(0, 0, 0, 0.08);
    backdrop-filter: blur(12px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Floating elements */
  .floating-input {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    @apply shadow-2xl border-primary/20;
    @apply backdrop-blur-xl bg-background/95;
  }
  
  .floating-button {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    @apply shadow-lg hover:shadow-xl;
    @apply transition-all duration-200 hover:scale-105;
  }

  /* Enhanced animations */
  .slide-in-right {
    animation: slideInRight 0.3s ease-out;
  }
  
  .slide-out-right {
    animation: slideOutRight 0.3s ease-in;
  }
  
  .bounce-in {
    animation: bounceIn 0.4s ease-out;
  }
  
  .fade-in-up {
    animation: fadeInUp 0.5s ease-out;
  }

  /* Gradient backgrounds */
  .gradient-primary {
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary)/80 100%);
  }
  
  .gradient-success {
    background: linear-gradient(135deg, var(--success) 0%, var(--success)/80 100%);
  }
  
  .gradient-warning {
    background: linear-gradient(135deg, var(--warning) 0%, var(--warning)/80 100%);
  }

  /* Interactive states */
  .interactive-card {
    @apply transition-all duration-200 ease-in-out;
    @apply hover:scale-[1.02] hover:shadow-xl;
    @apply active:scale-[0.98];
    @apply cursor-pointer;
  }
  
  .interactive-button {
    @apply transition-all duration-150 ease-in-out;
    @apply hover:scale-105 active:scale-95;
    @apply hover:shadow-lg;
  }

  /* Typography enhancements */
  .session-title {
    @apply text-xl font-bold tracking-tight;
    font-family: 'Playfair Display', Georgia, serif;
    @apply bg-gradient-to-r from-foreground to-foreground/80;
    @apply bg-clip-text text-transparent;
  }
  
  .session-subtitle {
    @apply text-sm leading-relaxed text-muted-foreground;
    @apply font-medium tracking-wide;
  }

  /* Layout utilities */
  .session-grid {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
  }
  
  .session-panel {
    @apply fixed right-0 top-0 h-full w-full sm:w-96;
    @apply bg-background border-l border-border shadow-2xl z-30;
    @apply overflow-hidden;
  }
  
  .session-panel-wide {
    @apply sm:w-[600px];
  }

  /* Scrollbar styling */
  .session-scrollbar::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  .session-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .session-scrollbar::-webkit-scrollbar-thumb {
    background: hsl(var(--muted-foreground) / 0.3);
    border-radius: 4px;
  }
  
  .session-scrollbar::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--muted-foreground) / 0.5);
  }

  /* Loading states */
  .session-skeleton {
    @apply animate-pulse bg-muted/50 rounded-lg;
  }
  
  .session-loading {
    @apply relative overflow-hidden;
  }
  
  .session-loading::after {
    content: '';
    @apply absolute inset-0;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.1),
      transparent
    );
    animation: shimmer 2s infinite;
  }
}

/* Keyframe animations */
@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideOutRight {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(100%);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Dark mode enhancements */
.dark {
  .glass-panel {
    background: rgba(0, 0, 0, 0.12);
    border: 1px solid rgba(255, 255, 255, 0.08);
  }
  
  .session-card {
    @apply bg-card/95;
  }
  
  .floating-input {
    @apply bg-background/98;
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .session-grid {
    @apply grid-cols-1 gap-4;
  }
  
  .session-panel {
    @apply w-full;
  }
  
  .message-user,
  .message-assistant {
    @apply max-w-[95%];
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .session-card {
    @apply border-2;
  }
  
  .status-active::before,
  .status-thinking::before {
    @apply border-2 border-background;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .interactive-card,
  .interactive-button,
  .status-pulse,
  .session-loading::after {
    animation: none;
    transition: none;
  }
  
  .interactive-card:hover,
  .interactive-button:hover {
    transform: none;
  }
}