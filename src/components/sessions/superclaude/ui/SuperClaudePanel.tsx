import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Command, 
  Clock, 
  TrendingUp, 
  User, 
  Activity,
  ChevronRight,
  RefreshCw,
  History,
  Sparkles
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { api } from '@/lib/api';
import { PersonaBadge, PersonaBadgeCompact } from './PersonaBadge';
import type { 
  SuperClaudeCommand, 
  SuperClaudePersona,
  CommandHistoryEntry,
  SuperClaudeStats 
} from '@/lib/api';

interface SuperClaudePanelProps {
  sessionId: string;
  projectId: string;
  className?: string;
  onCommandClick?: (command: SuperClaudeCommand) => void;
  onPersonaClick?: (persona: string) => void;
}

export const SuperClaudePanel: React.FC<SuperClaudePanelProps> = ({
  sessionId,
  projectId,
  className,
  onCommandClick,
  onPersonaClick
}) => {
  const [activePersonas, setActivePersonas] = useState<string[]>([]);
  const [commandHistory, setCommandHistory] = useState<CommandHistoryEntry[]>([]);
  const [stats, setStats] = useState<SuperClaudeStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');

  // Load initial data
  useEffect(() => {
    if (sessionId && projectId) {
      loadData();
    } else {
      setIsLoading(false);
    }
  }, [sessionId, projectId]);

  // Set up event listener for command execution
  useEffect(() => {
    const handleCommandExecuted = (event: CustomEvent) => {
      // Reload data after a small delay to ensure backend has saved
      setTimeout(() => {
        if (sessionId && projectId) {
          loadData();
        }
      }, 500);
    };

    window.addEventListener('superclaude-command-executed' as any, handleCommandExecuted);

    return () => {
      window.removeEventListener('superclaude-command-executed' as any, handleCommandExecuted);
    };
  }, [sessionId, projectId]);

  const loadData = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Load all data in parallel
      const [personas, history, projectStats] = await Promise.all([
        api.getSessionPersonas(sessionId, projectId).catch(() => []),
        api.getCommandHistory(sessionId, projectId, 10).catch(() => []),
        api.getSuperClaudeStats(projectId).catch(() => null)
      ]);
      

      setActivePersonas(personas || []);
      setCommandHistory(history || []);
      setStats(projectStats || {
        total_commands_executed: 0,
        commands_by_category: {},
        most_used_personas: [],
        last_command_timestamp: null
      });
      setError(null);
    } catch (err) {
      // Don't show error, just use empty state
      setActivePersonas([]);
      setCommandHistory([]);
      setStats({
        total_commands_executed: 0,
        commands_by_category: {},
        most_used_personas: [],
        last_command_timestamp: null
      });
      setError(null);
    } finally {
      setIsLoading(false);
    }
  };

  const removePersona = async (personaId: string) => {
    try {
      const newPersonas = activePersonas.filter(p => p !== personaId);
      await api.activatePersonas(sessionId, projectId, newPersonas);
      setActivePersonas(newPersonas);
    } catch (err) {
    }
  };

  const formatTimestamp = (timestamp?: string) => {
    if (!timestamp) return 'Just now';
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    
    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours}h ago`;
    const days = Math.floor(hours / 24);
    return `${days}d ago`;
  };

  // Show waiting state if no session/project IDs yet
  if (!sessionId || !projectId) {
    return (
      <Card className={cn("", className)}>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-primary" />
            SuperClaude Assistant
          </CardTitle>
        </CardHeader>
        <CardContent className="p-4">
          <div className="text-center py-8">
            <Sparkles className="h-12 w-12 text-muted-foreground/30 mx-auto mb-3" />
            <p className="text-sm text-muted-foreground mb-2">
              SuperClaude will activate when you start chatting
            </p>
            <p className="text-xs text-muted-foreground">
              Type <code className="px-1 py-0.5 bg-muted rounded">/sc:</code> to see available commands
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (isLoading) {
    return (
      <Card className={cn("animate-pulse", className)}>
        <CardContent className="p-6">
          <div className="space-y-3">
            <div className="h-4 bg-muted rounded w-3/4" />
            <div className="h-4 bg-muted rounded w-1/2" />
            <div className="h-4 bg-muted rounded w-2/3" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={cn("border-destructive/50", className)}>
        <CardContent className="p-6">
          <p className="text-sm text-destructive">{error}</p>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={loadData}
            className="mt-2"
          >
            <RefreshCw className="h-3 w-3 mr-1" />
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn("", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-primary" />
            SuperClaude Assistant
          </CardTitle>
          <Button
            variant="ghost"
            size="icon"
            onClick={loadData}
            className="h-8 w-8"
          >
            <RefreshCw className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>

      <CardContent className="p-0">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="w-full justify-start rounded-none border-b">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="history">History</TabsTrigger>
            <TabsTrigger value="stats">Stats</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="p-4 space-y-4">
            {/* Active Personas */}
            <div>
              <h3 className="text-sm font-medium mb-2 flex items-center gap-1">
                <User className="h-3.5 w-3.5" />
                Active Personas
              </h3>
              {activePersonas.length > 0 ? (
                <PersonaBadge 
                  personaIds={activePersonas}
                  onRemove={removePersona}
                />
              ) : (
                <p className="text-xs text-muted-foreground">
                  No personas active. They'll activate automatically based on your commands.
                </p>
              )}
            </div>

            {/* Recent Commands */}
            <div>
              <h3 className="text-sm font-medium mb-2 flex items-center gap-1">
                <Clock className="h-3.5 w-3.5" />
                Recent Commands
              </h3>
              <ScrollArea className="h-32">
                <div className="space-y-1">
                  {commandHistory.slice(0, 5).map((entry, index) => (
                    <motion.div
                      key={`${entry.command}-${entry.executed_at || entry.timestamp || index}`}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.05 }}
                      className={cn(
                        "flex items-center justify-between",
                        "px-2 py-1.5 rounded-md",
                        "hover:bg-muted/50 transition-colors",
                        "text-xs"
                      )}
                    >
                      <div className="flex items-center gap-2 flex-1 min-w-0">
                        <Command className="h-3 w-3 text-muted-foreground flex-shrink-0" />
                        <span className="font-medium truncate">{entry.command}</span>
                        {entry.success ? (
                          <Badge variant="secondary" className="h-4 px-1 text-[10px]">
                            Success
                          </Badge>
                        ) : (
                          <Badge variant="destructive" className="h-4 px-1 text-[10px]">
                            Failed
                          </Badge>
                        )}
                      </div>
                      <span className="text-[10px] text-muted-foreground">
                        {formatTimestamp(entry.executed_at || entry.timestamp)}
                      </span>
                    </motion.div>
                  ))}
                  {commandHistory.length === 0 && (
                    <p className="text-xs text-muted-foreground text-center py-2">
                      No commands executed yet
                    </p>
                  )}
                </div>
              </ScrollArea>
            </div>

            {/* Quick Stats */}
            {stats && (
              <div className="grid grid-cols-2 gap-2">
                <div className="p-2 rounded-md bg-muted/50">
                  <div className="text-xs text-muted-foreground">Total Commands</div>
                  <div className="text-lg font-semibold">{stats.total_commands_executed}</div>
                </div>
                <div className="p-2 rounded-md bg-muted/50">
                  <div className="text-xs text-muted-foreground">Most Used</div>
                  <div className="text-sm font-medium truncate">
                    {stats.most_used_personas?.[0]?.[0] || 'None'}
                  </div>
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="history" className="p-4">
            <ScrollArea className="h-64">
              <div className="space-y-2">
                {commandHistory.map((entry, index) => (
                  <motion.div
                    key={`history-${index}-${entry.executed_at || entry.timestamp || 'no-timestamp'}`}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.03 }}
                    className="border rounded-lg p-3 hover:bg-muted/30 transition-colors"
                  >
                    <div className="flex items-start justify-between mb-1">
                      <div className="flex items-center gap-2">
                        <Command className="h-3.5 w-3.5 text-primary mt-0.5" />
                        <span className="font-medium text-sm">{entry.command}</span>
                      </div>
                      <span className="text-[10px] text-muted-foreground">
                        {formatTimestamp(entry.executed_at || entry.timestamp)}
                      </span>
                    </div>
                    {((entry.args && entry.args.trim()) || (entry.parameters && entry.parameters.length > 0)) && (
                      <div className="text-xs text-muted-foreground ml-5">
                        Parameters: {entry.args || (entry.parameters ? entry.parameters.join(', ') : '')}
                      </div>
                    )}
                    {(entry.output || entry.result) && (
                      <div className="text-xs text-muted-foreground ml-5 mt-1">
                        {entry.output || entry.result}
                      </div>
                    )}
                  </motion.div>
                ))}
                {commandHistory.length === 0 && (
                  <div className="text-center py-8">
                    <History className="h-8 w-8 text-muted-foreground/50 mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">No command history</p>
                  </div>
                )}
              </div>
            </ScrollArea>
          </TabsContent>

          <TabsContent value="stats" className="p-4">
            {stats ? (
              <div className="space-y-4">
                {/* Command Categories */}
                <div>
                  <h3 className="text-sm font-medium mb-2">Commands by Category</h3>
                  <div className="space-y-1">
                    {stats.commands_by_category && Object.entries(stats.commands_by_category).map(([category, count]) => (
                      <div key={category} className="flex items-center justify-between">
                        <span className="text-xs capitalize">{category}</span>
                        <div className="flex items-center gap-2">
                          <div className="w-24 h-2 bg-muted rounded-full overflow-hidden">
                            <div 
                              className="h-full bg-primary transition-all"
                              style={{ 
                                width: `${(count / stats.total_commands_executed) * 100}%` 
                              }}
                            />
                          </div>
                          <span className="text-xs text-muted-foreground w-8 text-right">
                            {count}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Most Used Personas */}
                <div>
                  <h3 className="text-sm font-medium mb-2">Top Personas</h3>
                  <div className="space-y-1">
                    {stats.most_used_personas && stats.most_used_personas.slice(0, 5).map(([persona, count]) => (
                      <div key={persona} className="flex items-center justify-between">
                        <span className="text-xs">{persona}</span>
                        <Badge variant="secondary" className="h-5">
                          {count}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center py-8">
                <Activity className="h-8 w-8 text-muted-foreground/50 mx-auto mb-2" />
                <p className="text-sm text-muted-foreground">No statistics available</p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};