import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Command, ChevronRight, Hash } from 'lucide-react';
import { cn } from '@/lib/utils';
import { CommandSuggestion } from '../core/types';

interface CommandSuggestionsProps {
  suggestions: CommandSuggestion[];
  selectedIndex: number;
  onSelect: (suggestion: CommandSuggestion) => void;
  className?: string;
}

export const CommandSuggestions: React.FC<CommandSuggestionsProps> = ({
  suggestions,
  selectedIndex,
  onSelect,
  className
}) => {
  if (suggestions.length === 0) return null;

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'development':
        return 'text-blue-500';
      case 'analysis':
        return 'text-green-500';
      case 'quality':
        return 'text-orange-500';
      case 'documentation':
        return 'text-purple-500';
      case 'meta':
        return 'text-gray-500';
      default:
        return 'text-muted-foreground';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'development':
        return '⚡';
      case 'analysis':
        return '🔍';
      case 'quality':
        return '✨';
      case 'documentation':
        return '📝';
      case 'meta':
        return '🎯';
      default:
        return '📌';
    }
  };

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -10 }}
        className={cn(
          "absolute bottom-full left-0 right-0 mb-2 z-50",
          "bg-background border rounded-lg shadow-lg",
          "max-h-64 overflow-y-auto",
          className
        )}
      >
        <div className="p-2">
          <div className="text-xs text-muted-foreground px-2 py-1 mb-1">
            SuperClaude Commands
          </div>
          
          {suggestions.map((suggestion, index) => (
            <motion.button
              key={suggestion.command.trigger}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.02 }}
              onClick={() => onSelect(suggestion)}
              className={cn(
                "w-full text-left px-3 py-2 rounded-md",
                "flex items-center gap-3 group",
                "transition-colors duration-150",
                selectedIndex === index
                  ? "bg-primary/10 text-primary"
                  : "hover:bg-muted"
              )}
            >
              <span className="text-lg">
                {getCategoryIcon(suggestion.command.category)}
              </span>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2">
                  <span className="font-medium">
                    {suggestion.command.name}
                  </span>
                  <code className="text-xs text-muted-foreground">
                    {suggestion.command.trigger}
                  </code>
                </div>
                <p className="text-xs text-muted-foreground truncate">
                  {suggestion.command.description}
                </p>
              </div>
              
              <div className="flex items-center gap-1">
                <span className={cn(
                  "text-xs font-medium",
                  getCategoryColor(suggestion.command.category)
                )}>
                  {suggestion.command.category}
                </span>
                <ChevronRight className="h-3 w-3 opacity-0 group-hover:opacity-100 transition-opacity" />
              </div>
            </motion.button>
          ))}
          
          {suggestions.length > 0 && (
            <div className="mt-2 pt-2 border-t">
              <div className="px-2 py-1 text-xs text-muted-foreground">
                <kbd className="px-1 py-0.5 bg-muted rounded text-xs">Tab</kbd> to select •{' '}
                <kbd className="px-1 py-0.5 bg-muted rounded text-xs">↑↓</kbd> to navigate •{' '}
                <kbd className="px-1 py-0.5 bg-muted rounded text-xs">Esc</kbd> to close
              </div>
            </div>
          )}
        </div>
      </motion.div>
    </AnimatePresence>
  );
};