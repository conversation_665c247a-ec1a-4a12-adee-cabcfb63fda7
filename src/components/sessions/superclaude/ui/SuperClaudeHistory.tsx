import React, { useEffect, useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  History, 
  Clock, 
  Command, 
  User, 
  Filter,
  Calendar,
  ChevronRight,
  RefreshCw,
  TrendingUp,
  Activity,
  Search,
  X
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { api } from '@/lib/api';
import { PersonaBadgeCompact } from './PersonaBadge';
import type { CommandHistoryEntry, SuperClaudeStats } from '@/lib/api';

interface SuperClaudeHistoryProps {
  sessionId?: string;
  projectId: string;
  className?: string;
  onCommandClick?: (command: CommandHistoryEntry) => void;
  maxItems?: number;
}

export const SuperClaudeHistory: React.FC<SuperClaudeHistoryProps> = ({
  sessionId,
  projectId,
  className,
  onCommandClick,
  maxItems = 50
}) => {
  const [history, setHistory] = useState<CommandHistoryEntry[]>([]);
  const [stats, setStats] = useState<SuperClaudeStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedTimeRange, setSelectedTimeRange] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'list' | 'timeline' | 'stats'>('list');

  // Load history data
  useEffect(() => {
    loadHistory();
  }, [sessionId, projectId]);

  const loadHistory = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const [historyData, projectStats] = await Promise.all([
        sessionId 
          ? api.getCommandHistory(sessionId, projectId, maxItems)
          : api.getProjectCommandHistory(projectId, maxItems),
        api.getSuperClaudeStats(projectId)
      ]);

      setHistory(historyData);
      setStats(projectStats);
    } catch (err) {
      setError('Failed to load history');
    } finally {
      setIsLoading(false);
    }
  };

  // Filter history based on search and filters
  const filteredHistory = useMemo(() => {
    let filtered = [...history];

    // Search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(entry => 
        entry.command.toLowerCase().includes(query) ||
        entry.parameters.some(p => p.toLowerCase().includes(query)) ||
        entry.result?.toLowerCase().includes(query)
      );
    }

    // Category filter (would need to parse command to get category)
    if (selectedCategory !== 'all') {
      // This would require mapping commands to categories
      // For now, we'll use a simple heuristic based on command name
      filtered = filtered.filter(entry => {
        const command = entry.command.toLowerCase();
        switch (selectedCategory) {
          case 'development':
            return command.includes('build') || command.includes('implement') || command.includes('git');
          case 'analysis':
            return command.includes('analyze') || command.includes('troubleshoot') || command.includes('explain');
          case 'quality':
            return command.includes('test') || command.includes('improve') || command.includes('cleanup');
          case 'documentation':
            return command.includes('document');
          default:
            return true;
        }
      });
    }

    // Time range filter
    if (selectedTimeRange !== 'all') {
      const now = new Date();
      const cutoff = new Date();
      
      switch (selectedTimeRange) {
        case 'today':
          cutoff.setHours(0, 0, 0, 0);
          break;
        case 'week':
          cutoff.setDate(now.getDate() - 7);
          break;
        case 'month':
          cutoff.setMonth(now.getMonth() - 1);
          break;
      }

      filtered = filtered.filter(entry => 
        new Date(entry.timestamp) >= cutoff
      );
    }

    return filtered;
  }, [history, searchQuery, selectedCategory, selectedTimeRange]);

  // Group history by date for timeline view
  const groupedHistory = useMemo(() => {
    const groups: Record<string, CommandHistoryEntry[]> = {};
    
    filteredHistory.forEach(entry => {
      const date = new Date(entry.timestamp).toLocaleDateString();
      if (!groups[date]) {
        groups[date] = [];
      }
      groups[date].push(entry);
    });

    return groups;
  }, [filteredHistory]);

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleString();
  };

  const formatRelativeTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    
    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours}h ago`;
    const days = Math.floor(hours / 24);
    if (days < 7) return `${days}d ago`;
    const weeks = Math.floor(days / 7);
    return `${weeks}w ago`;
  };

  if (isLoading) {
    return (
      <Card className={cn("animate-pulse", className)}>
        <CardContent className="p-6">
          <div className="space-y-3">
            <div className="h-4 bg-muted rounded w-3/4" />
            <div className="h-4 bg-muted rounded w-1/2" />
            <div className="h-4 bg-muted rounded w-2/3" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={cn("border-destructive/50", className)}>
        <CardContent className="p-6">
          <p className="text-sm text-destructive">{error}</p>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={loadHistory}
            className="mt-2"
          >
            <RefreshCw className="h-3 w-3 mr-1" />
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn("", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <History className="h-5 w-5 text-primary" />
            Command History
          </CardTitle>
          <div className="flex items-center gap-2">
            <Badge variant="secondary">
              {filteredHistory.length} commands
            </Badge>
            <Button
              variant="ghost"
              size="icon"
              onClick={loadHistory}
              className="h-8 w-8"
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="p-0">
        {/* Filters */}
        <div className="p-4 border-b space-y-3">
          <div className="flex items-center gap-2">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search commands..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-8 h-9"
              />
              {searchQuery && (
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setSearchQuery('')}
                  className="absolute right-1 top-1 h-7 w-7"
                >
                  <X className="h-3 w-3" />
                </Button>
              )}
            </div>
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-32 h-9">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All</SelectItem>
                <SelectItem value="development">Development</SelectItem>
                <SelectItem value="analysis">Analysis</SelectItem>
                <SelectItem value="quality">Quality</SelectItem>
                <SelectItem value="documentation">Docs</SelectItem>
              </SelectContent>
            </Select>
            <Select value={selectedTimeRange} onValueChange={setSelectedTimeRange}>
              <SelectTrigger className="w-28 h-9">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Time</SelectItem>
                <SelectItem value="today">Today</SelectItem>
                <SelectItem value="week">This Week</SelectItem>
                <SelectItem value="month">This Month</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* View Tabs */}
        <Tabs value={viewMode} onValueChange={(v) => setViewMode(v as any)}>
          <TabsList className="w-full justify-start rounded-none border-b">
            <TabsTrigger value="list">List</TabsTrigger>
            <TabsTrigger value="timeline">Timeline</TabsTrigger>
            <TabsTrigger value="stats">Statistics</TabsTrigger>
          </TabsList>

          <TabsContent value="list" className="p-4">
            <ScrollArea className="h-96">
              <div className="space-y-2">
                <AnimatePresence>
                  {filteredHistory.map((entry, index) => (
                    <motion.div
                      key={`${entry.command}-${entry.timestamp}-${index}`}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: 20 }}
                      transition={{ delay: index * 0.02 }}
                      className={cn(
                        "p-3 rounded-lg border transition-colors cursor-pointer",
                        "hover:bg-muted/50"
                      )}
                      onClick={() => onCommandClick?.(entry)}
                    >
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <Command className="h-4 w-4 text-primary mt-0.5" />
                          <span className="font-medium text-sm">{entry.command}</span>
                          {entry.success ? (
                            <Badge variant="secondary" className="h-5">
                              Success
                            </Badge>
                          ) : (
                            <Badge variant="destructive" className="h-5">
                              Failed
                            </Badge>
                          )}
                        </div>
                        <span className="text-xs text-muted-foreground">
                          {formatRelativeTime(entry.timestamp)}
                        </span>
                      </div>
                      {entry.parameters && entry.parameters.length > 0 && (
                        <div className="text-xs text-muted-foreground ml-6 mb-1">
                          Parameters: {entry.parameters.join(', ')}
                        </div>
                      )}
                      {entry.detected_personas && entry.detected_personas.length > 0 && (
                        <div className="ml-6">
                          <PersonaBadgeCompact personaIds={entry.detected_personas} />
                        </div>
                      )}
                    </motion.div>
                  ))}
                </AnimatePresence>
                {filteredHistory.length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    No commands found
                  </div>
                )}
              </div>
            </ScrollArea>
          </TabsContent>

          <TabsContent value="timeline" className="p-4">
            <ScrollArea className="h-96">
              <div className="space-y-4">
                {Object.entries(groupedHistory).map(([date, entries]) => (
                  <div key={date}>
                    <div className="flex items-center gap-2 mb-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium">{date}</span>
                      <Badge variant="secondary" className="h-5">
                        {entries.length}
                      </Badge>
                    </div>
                    <div className="ml-6 space-y-1 border-l-2 border-muted pl-4">
                      {entries.map((entry, index) => (
                        <motion.div
                          key={`${entry.timestamp}-${index}`}
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          transition={{ delay: index * 0.05 }}
                          className="py-2"
                        >
                          <div className="flex items-center gap-2">
                            <Clock className="h-3 w-3 text-muted-foreground" />
                            <span className="text-xs text-muted-foreground">
                              {new Date(entry.timestamp).toLocaleTimeString()}
                            </span>
                            <span className="text-sm">{entry.command}</span>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </TabsContent>

          <TabsContent value="stats" className="p-4">
            {stats && (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-3">
                  <div className="p-3 rounded-lg bg-muted/50">
                    <div className="flex items-center gap-2 mb-1">
                      <TrendingUp className="h-4 w-4 text-primary" />
                      <span className="text-xs text-muted-foreground">Total Commands</span>
                    </div>
                    <div className="text-2xl font-bold">{stats.total_commands_executed}</div>
                  </div>
                  <div className="p-3 rounded-lg bg-muted/50">
                    <div className="flex items-center gap-2 mb-1">
                      <Activity className="h-4 w-4 text-primary" />
                      <span className="text-xs text-muted-foreground">Success Rate</span>
                    </div>
                    <div className="text-2xl font-bold">
                      {stats.total_commands_executed > 0 
                        ? Math.round((filteredHistory.filter(h => h.success).length / filteredHistory.length) * 100)
                        : 0}%
                    </div>
                  </div>
                </div>

                {/* Command frequency chart */}
                <div>
                  <h3 className="text-sm font-medium mb-2">Command Frequency</h3>
                  <div className="space-y-2">
                    {Object.entries(
                      filteredHistory.reduce((acc, entry) => {
                        const cmd = entry.command.split(' ')[0];
                        acc[cmd] = (acc[cmd] || 0) + 1;
                        return acc;
                      }, {} as Record<string, number>)
                    )
                      .sort(([, a], [, b]) => b - a)
                      .slice(0, 5)
                      .map(([command, count]) => (
                        <div key={command} className="flex items-center justify-between">
                          <span className="text-sm">{command}</span>
                          <div className="flex items-center gap-2">
                            <div className="w-32 h-2 bg-muted rounded-full overflow-hidden">
                              <div 
                                className="h-full bg-primary transition-all"
                                style={{ 
                                  width: `${(count / filteredHistory.length) * 100}%` 
                                }}
                              />
                            </div>
                            <Badge variant="secondary" className="h-5">
                              {count}
                            </Badge>
                          </div>
                        </div>
                      ))}
                  </div>
                </div>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};