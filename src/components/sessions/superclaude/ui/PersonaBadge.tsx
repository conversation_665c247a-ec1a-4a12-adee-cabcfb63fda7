import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X } from 'lucide-react';
import { cn } from '@/lib/utils';
import { SuperClaudePersona } from '../core/types';
import { getPersonaById } from '../core/personas';

interface PersonaBadgeProps {
  personaIds: string[];
  onRemove?: (id: string) => void;
  className?: string;
}

export const PersonaBadge: React.FC<PersonaBadgeProps> = ({
  personaIds,
  onRemove,
  className
}) => {
  if (personaIds.length === 0) return null;

  const personas = personaIds
    .map(id => getPersonaById(id))
    .filter((p): p is SuperClaudePersona => p !== undefined);

  if (personas.length === 0) return null;

  return (
    <div className={cn("flex items-center gap-2 flex-wrap", className)}>
      <AnimatePresence mode="popLayout">
        {personas.map((persona, index) => (
          <motion.div
            key={persona.id}
            initial={{ opacity: 0, scale: 0.8, y: -10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: -10 }}
            transition={{ 
              type: "spring",
              stiffness: 500,
              damping: 25,
              delay: index * 0.05 
            }}
            className={cn(
              "inline-flex items-center gap-1.5 px-2.5 py-1",
              "bg-gradient-to-r rounded-full",
              "text-xs font-medium",
              "border shadow-sm",
              "transition-all duration-200",
              "hover:shadow-md hover:scale-105",
              getPersonaStyle(persona.id)
            )}
          >
            <span className="text-base leading-none">{persona.icon}</span>
            <span className="truncate max-w-[100px]">{persona.name}</span>
            {onRemove && (
              <button
                onClick={() => onRemove(persona.id)}
                className={cn(
                  "ml-0.5 -mr-1 p-0.5 rounded-full",
                  "hover:bg-white/20 transition-colors",
                  "focus:outline-none focus:ring-2 focus:ring-white/50"
                )}
              >
                <X className="h-2.5 w-2.5" />
              </button>
            )}
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  );
};

// Helper function to get persona-specific styles
const getPersonaStyle = (personaId: string): string => {
  const styles: Record<string, string> = {
    architect: "from-blue-500/20 to-indigo-500/20 border-blue-500/30 text-blue-700 dark:text-blue-300",
    frontend: "from-purple-500/20 to-pink-500/20 border-purple-500/30 text-purple-700 dark:text-purple-300",
    backend: "from-green-500/20 to-emerald-500/20 border-green-500/30 text-green-700 dark:text-green-300",
    security: "from-red-500/20 to-orange-500/20 border-red-500/30 text-red-700 dark:text-red-300",
    performance: "from-yellow-500/20 to-amber-500/20 border-yellow-500/30 text-yellow-700 dark:text-yellow-300",
    analyzer: "from-cyan-500/20 to-teal-500/20 border-cyan-500/30 text-cyan-700 dark:text-cyan-300",
    qa: "from-lime-500/20 to-green-500/20 border-lime-500/30 text-lime-700 dark:text-lime-300",
    refactorer: "from-indigo-500/20 to-purple-500/20 border-indigo-500/30 text-indigo-700 dark:text-indigo-300",
    devops: "from-orange-500/20 to-red-500/20 border-orange-500/30 text-orange-700 dark:text-orange-300",
    mentor: "from-blue-500/20 to-cyan-500/20 border-blue-500/30 text-blue-700 dark:text-blue-300",
    scribe: "from-gray-500/20 to-slate-500/20 border-gray-500/30 text-gray-700 dark:text-gray-300"
  };

  return styles[personaId] || "from-gray-500/20 to-slate-500/20 border-gray-500/30 text-gray-700 dark:text-gray-300";
};

// Export a compact version for inline display
export const PersonaBadgeCompact: React.FC<{
  personaIds: string[];
  className?: string;
}> = ({ personaIds, className }) => {
  if (!personaIds || personaIds.length === 0) return null;

  const personas = personaIds
    .map(id => getPersonaById(id))
    .filter((p): p is SuperClaudePersona => p !== undefined)
    .slice(0, 3); // Show max 3 personas

  if (personas.length === 0) return null;

  return (
    <div className={cn("inline-flex items-center gap-1", className)}>
      {personas.map((persona) => (
        <span
          key={persona.id}
          className="text-base leading-none"
          title={`${persona.name}: ${persona.role}`}
        >
          {persona.icon}
        </span>
      ))}
      {personaIds.length > 3 && (
        <span className="text-xs text-muted-foreground">
          +{personaIds.length - 3}
        </span>
      )}
    </div>
  );
};