export interface SuperClaudeCommand {
  trigger: string;
  name: string;
  description: string;
  category: 'development' | 'analysis' | 'quality' | 'documentation' | 'meta';
  personas: string[];
  aliases?: string[];
  parameters?: CommandParameter[];
  examples?: string[];
}

export interface CommandParameter {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'file' | 'directory';
  required: boolean;
  description: string;
  default?: any;
}

export interface SuperClaudePersona {
  id: string;
  name: string;
  role: string;
  icon: string;
  keywords: string[];
  systemPrompt: string;
  specialties: string[];
  priority: number;
  activationThreshold?: number;
}

export interface ParsedCommand {
  command: string;
  trigger: string;
  args: string;
  personas: string[];
  category: string;
  parameters?: Record<string, any>;
}

export interface CommandSuggestion {
  command: SuperClaudeCommand;
  score: number;
  matchType: 'exact' | 'prefix' | 'fuzzy';
}

export interface PersonaActivation {
  persona: SuperClaudePersona;
  confidence: number;
  reason: string;
}

export type CommandCategory = 'development' | 'analysis' | 'quality' | 'documentation' | 'meta';

export interface SuperClaudeContext {
  activeCommand: ParsedCommand | null;
  activePersonas: string[];
  commandHistory: ParsedCommand[];
  personaHistory: PersonaActivation[];
}