import React, { use<PERSON>em<PERSON>, use<PERSON><PERSON>back, useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { 
  FileText, 
  ArrowLeft, 
  Calendar, 
  Clock, 
  Command, 
  Sparkles,
  Search,
  Filter,
  SortAsc,
  FolderOpen,
  MessageSquare,
  Bot,
  Zap,
  Hash,
  ChevronRight,
  MoreVertical,
  Trash2,
  Archive,
  Star,
  StarOff,
  Copy,
  ExternalLink
} from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ontent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Pagination } from "@/components/ui/pagination";
import { ClaudeMemoriesDropdown } from "@/components/settings/ClaudeMemoriesDropdown";
import { PersonaBadgeCompact } from "@/components/sessions/superclaude/ui/PersonaBadge";
import { cn } from "@/lib/utils";
import { formatUnixTimestamp, formatISOTimestamp } from "@/lib/date-utils";
import { usePagination } from "@/hooks/usePagination";
import { api } from "@/lib/api";
import type { Session, ClaudeMdFile } from "@/lib/api";

interface EnhancedSessionListProps {
  sessions: Session[];
  projectPath: string;
  onBack: () => void;
  onSessionClick?: (session: Session) => void;
  onEditClaudeFile?: (file: ClaudeMdFile) => void;
  className?: string;
}

// Enhanced Session Card Component
const EnhancedSessionCard = React.memo<{
  session: Session;
  projectPath: string;
  onClick?: () => void;
  onEditClaudeFile?: (file: ClaudeMdFile) => void;
  onDelete?: () => void;
  isStarred?: boolean;
  onToggleStar?: () => void;
  index: number;
}>(({ session, projectPath, onClick, onEditClaudeFile, onDelete, isStarred, onToggleStar, index }) => {
  const [superClaudeData, setSuperClaudeData] = useState<{
    personas: string[];
    commandCount: number;
  } | null>(null);
  const [isLoadingSuperClaude, setIsLoadingSuperClaude] = useState(true);
  const [showActions, setShowActions] = useState(false);

  // Load SuperClaude data
  useEffect(() => {
    const loadSuperClaudeData = async () => {
      try {
        const [personas, history] = await Promise.all([
          api.getSessionPersonas(session.id, session.project_id).catch(() => []),
          api.getCommandHistory(session.id, session.project_id, 1).catch(() => [])
        ]);

        setSuperClaudeData({
          personas: personas || [],
          commandCount: history?.length || 0
        });
      } catch (err) {
        setSuperClaudeData({ personas: [], commandCount: 0 });
      } finally {
        setIsLoadingSuperClaude(false);
      }
    };

    loadSuperClaudeData();
  }, [session.id, session.project_id]);

  const formatTime = useCallback((timestamp: string | number | undefined) => {
    if (!timestamp) return "Unknown time";
    
    if (typeof timestamp === "string") {
      return formatISOTimestamp(timestamp);
    } else {
      return formatUnixTimestamp(timestamp);
    }
  }, []);

  const getSessionAge = (timestamp: string | number | undefined) => {
    if (!timestamp) return "";
    const date = typeof timestamp === "string" 
      ? new Date(timestamp) 
      : new Date(timestamp * 1000);
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(hours / 24);
    
    if (days > 0) return `${days}d ago`;
    if (hours > 0) return `${hours}h ago`;
    return "Just now";
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20, scale: 0.95 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      exit={{ opacity: 0, y: -20, scale: 0.95 }}
      transition={{ duration: 0.2, delay: index * 0.03 }}
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
      className="relative"
    >
      <Card 
        className={cn(
          "cursor-pointer transition-all duration-200",
          "hover:shadow-xl hover:border-primary/30",
          "bg-card/80 backdrop-blur-sm",
          "border-border/50",
          isStarred && "border-yellow-500/30 bg-yellow-500/5"
        )}
        onClick={onClick}
      >
        <CardContent className="p-5">
          <div className="flex items-start justify-between gap-4">
            <div className="flex-1 space-y-3">
              {/* Session Header */}
              <div className="flex items-start gap-3">
                <div className={cn(
                  "h-10 w-10 rounded-xl flex items-center justify-center",
                  "bg-gradient-to-br from-primary/20 to-primary/10",
                  "flex-shrink-0"
                )}>
                  <FileText className="h-5 w-5 text-primary" />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2">
                    <h3 className="font-semibold text-base truncate">
                      Session {session.id.slice(0, 8)}
                    </h3>
                    {isStarred && (
                      <Star className="h-4 w-4 text-yellow-500 fill-yellow-500" />
                    )}
                  </div>
                  <p className="text-xs text-muted-foreground mt-0.5">
                    {getSessionAge(session.created_at)}
                  </p>
                </div>
              </div>

              {/* Session Stats */}
              <div className="flex flex-wrap gap-2">
                <Badge variant="secondary" className="text-xs">
                  <Calendar className="h-3 w-3 mr-1" />
                  {formatTime(session.created_at)}
                </Badge>
                {session.message_count && (
                  <Badge variant="outline" className="text-xs">
                    <MessageSquare className="h-3 w-3 mr-1" />
                    {session.message_count} messages
                  </Badge>
                )}
                {session.token_count && (
                  <Badge variant="outline" className="text-xs">
                    <Hash className="h-3 w-3 mr-1" />
                    {(session.token_count / 1000).toFixed(1)}k tokens
                  </Badge>
                )}
              </div>

              {/* SuperClaude Features */}
              {!isLoadingSuperClaude && superClaudeData && (
                <AnimatePresence>
                  {(superClaudeData.personas.length > 0 || superClaudeData.commandCount > 0) && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: "auto" }}
                      exit={{ opacity: 0, height: 0 }}
                      className="flex items-center gap-2 flex-wrap"
                    >
                      {superClaudeData.personas.length > 0 && (
                        <div className="flex items-center gap-1.5 px-2 py-1 bg-purple-500/10 rounded-lg">
                          <Bot className="h-3.5 w-3.5 text-purple-500" />
                          <PersonaBadgeCompact 
                            personaIds={superClaudeData.personas}
                            className="text-xs"
                          />
                        </div>
                      )}
                      {superClaudeData.commandCount > 0 && (
                        <Badge 
                          variant="secondary" 
                          className="bg-blue-500/10 text-blue-600 dark:text-blue-400 border-blue-500/30"
                        >
                          <Zap className="h-3 w-3 mr-1" />
                          {superClaudeData.commandCount} commands
                        </Badge>
                      )}
                    </motion.div>
                  )}
                </AnimatePresence>
              )}

              {/* Claude File if exists */}
              {session.claude_file_path && (
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="text-xs">
                    <Sparkles className="h-3 w-3 mr-1" />
                    CLAUDE.md configured
                  </Badge>
                </div>
              )}
            </div>

            {/* Action Buttons */}
            <AnimatePresence>
              {showActions && (
                <motion.div
                  initial={{ opacity: 0, x: 10 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 10 }}
                  className="flex flex-col gap-1"
                  onClick={(e) => e.stopPropagation()}
                >
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          size="sm"
                          variant="ghost"
                          className="h-8 w-8 p-0"
                          onClick={(e) => {
                            e.stopPropagation();
                            onToggleStar?.();
                          }}
                        >
                          {isStarred ? (
                            <StarOff className="h-4 w-4" />
                          ) : (
                            <Star className="h-4 w-4" />
                          )}
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        {isStarred ? 'Unstar' : 'Star'} session
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>

                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-8 w-8 p-0"
                      >
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem>
                        <Copy className="h-4 w-4 mr-2" />
                        Copy Session ID
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Archive className="h-4 w-4 mr-2" />
                        Archive
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem 
                        className="text-destructive"
                        onClick={(e) => {
                          e.stopPropagation();
                          onDelete?.();
                        }}
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
});

EnhancedSessionCard.displayName = "EnhancedSessionCard";

export const EnhancedSessionList: React.FC<EnhancedSessionListProps> = ({
  sessions,
  projectPath,
  onBack,
  onSessionClick,
  onEditClaudeFile,
  className,
}) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [sortBy, setSortBy] = useState<"recent" | "messages" | "tokens">("recent");
  const [filterByPersona, setFilterByPersona] = useState<string | "all">("all");
  const [starredSessions, setStarredSessions] = useState<Set<string>>(new Set());

  // Get unique personas from all sessions
  const allPersonas = useMemo(() => {
    const personaSet = new Set<string>();
    // This would need to be populated from actual session data
    return Array.from(personaSet);
  }, [sessions]);

  // Filter and sort sessions
  const filteredSessions = useMemo(() => {
    let filtered = [...sessions];

    // Search filter
    if (searchQuery) {
      filtered = filtered.filter(session => 
        session.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
        session.project_path?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Sort
    filtered.sort((a, b) => {
      switch (sortBy) {
        case "messages":
          return (b.message_count || 0) - (a.message_count || 0);
        case "tokens":
          return (b.token_count || 0) - (a.token_count || 0);
        case "recent":
        default:
          const aTime = typeof a.created_at === 'string' 
            ? new Date(a.created_at).getTime() 
            : (a.created_at || 0) * 1000;
          const bTime = typeof b.created_at === 'string'
            ? new Date(b.created_at).getTime()
            : (b.created_at || 0) * 1000;
          return bTime - aTime;
      }
    });

    // Starred sessions first
    filtered.sort((a, b) => {
      const aStarred = starredSessions.has(a.id);
      const bStarred = starredSessions.has(b.id);
      if (aStarred && !bStarred) return -1;
      if (!aStarred && bStarred) return 1;
      return 0;
    });

    return filtered;
  }, [sessions, searchQuery, sortBy, starredSessions]);

  // Pagination
  const itemsPerPage = 9;
  const {
    currentItems: paginatedSessions,
    currentPage,
    totalPages,
    goToPage,
  } = usePagination(filteredSessions, itemsPerPage);

  const toggleStar = useCallback((sessionId: string) => {
    setStarredSessions(prev => {
      const newSet = new Set(prev);
      if (newSet.has(sessionId)) {
        newSet.delete(sessionId);
      } else {
        newSet.add(sessionId);
      }
      return newSet;
    });
  }, []);

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Button
            variant="ghost"
            size="sm"
            onClick={onBack}
            className="gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back
          </Button>
          <Separator orientation="vertical" className="h-6" />
          <div className="flex items-center gap-2">
            <FolderOpen className="h-5 w-5 text-muted-foreground" />
            <span className="text-sm font-medium truncate max-w-[200px]">
              {projectPath}
            </span>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Badge variant="secondary">
            {filteredSessions.length} sessions
          </Badge>
          <ClaudeMemoriesDropdown projectPath={projectPath} />
        </div>
      </div>

      {/* Filters and Search */}
      <div className="flex items-center gap-3 flex-wrap">
        <div className="relative flex-1 min-w-[200px]">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search sessions..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-9"
          />
        </div>

        <Select value={sortBy} onValueChange={(v: any) => setSortBy(v)}>
          <SelectTrigger className="w-[150px]">
            <SortAsc className="h-4 w-4 mr-2" />
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="recent">Most Recent</SelectItem>
            <SelectItem value="messages">Most Messages</SelectItem>
            <SelectItem value="tokens">Most Tokens</SelectItem>
          </SelectContent>
        </Select>

        {allPersonas.length > 0 && (
          <Select value={filterByPersona} onValueChange={setFilterByPersona}>
            <SelectTrigger className="w-[150px]">
              <Filter className="h-4 w-4 mr-2" />
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Personas</SelectItem>
              {allPersonas.map(persona => (
                <SelectItem key={persona} value={persona}>
                  {persona}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}
      </div>

      {/* Sessions Grid */}
      {paginatedSessions.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <AnimatePresence mode="popLayout">
            {paginatedSessions.map((session, index) => (
              <EnhancedSessionCard
                key={session.id}
                session={session}
                projectPath={projectPath}
                onClick={() => onSessionClick?.(session)}
                onEditClaudeFile={onEditClaudeFile}
                onDelete={() => {
                  // Handle delete
                }}
                isStarred={starredSessions.has(session.id)}
                onToggleStar={() => toggleStar(session.id)}
                index={index}
              />
            ))}
          </AnimatePresence>
        </div>
      ) : (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center py-12"
        >
          <div className="h-16 w-16 rounded-full bg-muted/50 flex items-center justify-center mx-auto mb-4">
            <FileText className="h-8 w-8 text-muted-foreground" />
          </div>
          <h3 className="text-lg font-medium mb-1">No sessions found</h3>
          <p className="text-sm text-muted-foreground">
            {searchQuery 
              ? "Try adjusting your search or filters"
              : "Start a new Claude session to see it here"}
          </p>
        </motion.div>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={goToPage}
          />
        </div>
      )}
    </div>
  );
};

EnhancedSessionList.displayName = "EnhancedSessionList";