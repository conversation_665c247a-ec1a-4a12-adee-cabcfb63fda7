import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { 
  Plus,
  Search,
  Filter,
  SortAsc,
  MoreVertical,
  Play,
  Pause,
  Square,
  Archive,
  Trash2,
  Star,
  StarOff,
  Copy,
  ExternalLink,
  Folder<PERSON><PERSON>,
  Clock,
  MessageSquare,
  Hash,
  Activity,
  Layers,
  Bot,
  Server,
  Zap
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface Session {
  id: string;
  name: string;
  projectPath: string;
  status: 'active' | 'paused' | 'completed' | 'error';
  createdAt: Date;
  lastActivity: Date;
  messageCount: number;
  tokenCount: number;
  agentsUsed: string[];
  mcpServers: string[];
  isStarred: boolean;
  isBackground: boolean;
}

interface SessionManagerProps {
  sessions: Session[];
  onCreateSession: () => void;
  onSessionClick: (session: Session) => void;
  onSessionAction: (sessionId: string, action: string) => void;
  className?: string;
}

const SessionCard: React.FC<{
  session: Session;
  onClick: () => void;
  onAction: (action: string) => void;
  index: number;
}> = ({ session, onClick, onAction, index }) => {
  const [showActions, setShowActions] = useState(false);

  const getStatusColor = (status: Session['status']) => {
    switch (status) {
      case 'active': return 'bg-green-500/10 text-green-600 border-green-500/30';
      case 'paused': return 'bg-yellow-500/10 text-yellow-600 border-yellow-500/30';
      case 'completed': return 'bg-blue-500/10 text-blue-600 border-blue-500/30';
      case 'error': return 'bg-red-500/10 text-red-600 border-red-500/30';
      default: return 'bg-gray-500/10 text-gray-600 border-gray-500/30';
    }
  };

  const getStatusIcon = (status: Session['status']) => {
    switch (status) {
      case 'active': return <Activity className="h-3 w-3" />;
      case 'paused': return <Pause className="h-3 w-3" />;
      case 'completed': return <Play className="h-3 w-3" />;
      case 'error': return <Square className="h-3 w-3" />;
      default: return <Clock className="h-3 w-3" />;
    }
  };

  const formatTime = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(hours / 24);
    
    if (days > 0) return `${days}d ago`;
    if (hours > 0) return `${hours}h ago`;
    return 'Just now';
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20, scale: 0.95 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      exit={{ opacity: 0, y: -20, scale: 0.95 }}
      transition={{ duration: 0.2, delay: index * 0.03 }}
      whileHover={{ scale: 1.02 }}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
      className="relative"
    >
      <Card 
        className={cn(
          "cursor-pointer transition-all duration-200",
          "hover:shadow-xl hover:border-primary/30",
          "bg-card/80 backdrop-blur-sm border-border/50",
          session.isStarred && "border-yellow-500/30 bg-yellow-500/5",
          session.isBackground && "opacity-60"
        )}
        onClick={onClick}
      >
        <CardContent className="p-5">
          <div className="flex items-start justify-between gap-4">
            <div className="flex-1 space-y-3">
              {/* Session Header */}
              <div className="flex items-start gap-3">
                <div className={cn(
                  "h-10 w-10 rounded-xl flex items-center justify-center flex-shrink-0",
                  session.status === 'active' 
                    ? "bg-gradient-to-br from-green-500/20 to-green-600/20"
                    : "bg-gradient-to-br from-primary/20 to-primary/10"
                )}>
                  {getStatusIcon(session.status)}
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2">
                    <h3 className="font-semibold text-base truncate">
                      {session.name || `Session ${session.id.slice(0, 8)}`}
                    </h3>
                    {session.isStarred && (
                      <Star className="h-4 w-4 text-yellow-500 fill-yellow-500" />
                    )}
                  </div>
                  <p className="text-xs text-muted-foreground mt-0.5">
                    {formatTime(session.lastActivity)}
                  </p>
                </div>
              </div>

              {/* Project Path */}
              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                <FolderOpen className="h-3 w-3" />
                <span className="truncate">{session.projectPath}</span>
              </div>

              {/* Session Stats */}
              <div className="flex flex-wrap gap-2">
                <Badge 
                  variant="outline" 
                  className={cn("text-xs", getStatusColor(session.status))}
                >
                  {getStatusIcon(session.status)}
                  <span className="ml-1 capitalize">{session.status}</span>
                </Badge>
                
                <Badge variant="outline" className="text-xs">
                  <MessageSquare className="h-3 w-3 mr-1" />
                  {session.messageCount}
                </Badge>
                
                <Badge variant="outline" className="text-xs">
                  <Hash className="h-3 w-3 mr-1" />
                  {(session.tokenCount / 1000).toFixed(1)}k
                </Badge>
              </div>

              {/* Enhanced Features */}
              {(session.agentsUsed.length > 0 || session.mcpServers.length > 0) && (
                <div className="flex items-center gap-2 flex-wrap">
                  {session.agentsUsed.length > 0 && (
                    <Badge variant="outline" className="text-xs bg-purple-500/10">
                      <Bot className="h-3 w-3 mr-1" />
                      {session.agentsUsed.length} agent{session.agentsUsed.length > 1 ? 's' : ''}
                    </Badge>
                  )}
                  {session.mcpServers.length > 0 && (
                    <Badge variant="outline" className="text-xs bg-blue-500/10">
                      <Server className="h-3 w-3 mr-1" />
                      {session.mcpServers.length} MCP
                    </Badge>
                  )}
                </div>
              )}
            </div>

            {/* Action Buttons */}
            <AnimatePresence>
              {showActions && (
                <motion.div
                  initial={{ opacity: 0, x: 10 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 10 }}
                  className="flex flex-col gap-1"
                  onClick={(e) => e.stopPropagation()}
                >
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          size="sm"
                          variant="ghost"
                          className="h-8 w-8 p-0"
                          onClick={() => onAction('star')}
                        >
                          {session.isStarred ? (
                            <StarOff className="h-4 w-4" />
                          ) : (
                            <Star className="h-4 w-4" />
                          )}
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        {session.isStarred ? 'Unstar' : 'Star'} session
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>

                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        size="sm"
                        variant="ghost"
                        className="h-8 w-8 p-0"
                      >
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => onAction('open')}>
                        <ExternalLink className="h-4 w-4 mr-2" />
                        Open Session
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => onAction('copy')}>
                        <Copy className="h-4 w-4 mr-2" />
                        Copy ID
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={() => onAction('archive')}>
                        <Archive className="h-4 w-4 mr-2" />
                        Archive
                      </DropdownMenuItem>
                      <DropdownMenuItem 
                        className="text-destructive"
                        onClick={() => onAction('delete')}
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export const SessionManager: React.FC<SessionManagerProps> = ({
  sessions,
  onCreateSession,
  onSessionClick,
  onSessionAction,
  className
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'recent' | 'name' | 'activity'>('recent');
  const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'paused' | 'completed'>('all');
  const [showStarredOnly, setShowStarredOnly] = useState(false);

  // Filter and sort sessions
  const filteredSessions = sessions
    .filter(session => {
      if (searchQuery && !session.name.toLowerCase().includes(searchQuery.toLowerCase()) &&
          !session.projectPath.toLowerCase().includes(searchQuery.toLowerCase())) {
        return false;
      }
      if (filterStatus !== 'all' && session.status !== filterStatus) {
        return false;
      }
      if (showStarredOnly && !session.isStarred) {
        return false;
      }
      return true;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'activity':
          return b.lastActivity.getTime() - a.lastActivity.getTime();
        case 'recent':
        default:
          return b.createdAt.getTime() - a.createdAt.getTime();
      }
    });

  const handleSessionAction = useCallback((sessionId: string, action: string) => {
    onSessionAction(sessionId, action);
  }, [onSessionAction]);

  const activeCount = sessions.filter(s => s.status === 'active').length;
  const backgroundCount = sessions.filter(s => s.isBackground).length;

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <Card className="border-primary/20 bg-card/80 backdrop-blur-sm">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Layers className="h-5 w-5 text-primary" />
              <CardTitle className="text-lg">Session Manager</CardTitle>
              <div className="flex items-center gap-2">
                <Badge variant="default" className="text-xs">
                  {activeCount} active
                </Badge>
                {backgroundCount > 0 && (
                  <Badge variant="secondary" className="text-xs">
                    {backgroundCount} background
                  </Badge>
                )}
              </div>
            </div>

            <Button onClick={onCreateSession} className="gap-2">
              <Plus className="h-4 w-4" />
              New Session
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Filters and Search */}
      <div className="flex items-center gap-3 flex-wrap">
        <div className="relative flex-1 min-w-[200px]">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search sessions..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-9"
          />
        </div>

        <Select value={sortBy} onValueChange={(v: any) => setSortBy(v)}>
          <SelectTrigger className="w-[150px]">
            <SortAsc className="h-4 w-4 mr-2" />
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="recent">Most Recent</SelectItem>
            <SelectItem value="activity">Last Activity</SelectItem>
            <SelectItem value="name">Name</SelectItem>
          </SelectContent>
        </Select>

        <Select value={filterStatus} onValueChange={(v: any) => setFilterStatus(v)}>
          <SelectTrigger className="w-[120px]">
            <Filter className="h-4 w-4 mr-2" />
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="active">Active</SelectItem>
            <SelectItem value="paused">Paused</SelectItem>
            <SelectItem value="completed">Completed</SelectItem>
          </SelectContent>
        </Select>

        <Button
          variant={showStarredOnly ? "default" : "outline"}
          size="sm"
          onClick={() => setShowStarredOnly(!showStarredOnly)}
          className="gap-2"
        >
          <Star className={cn("h-4 w-4", showStarredOnly && "fill-current")} />
          Starred
        </Button>
      </div>

      {/* Sessions Grid */}
      {filteredSessions.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <AnimatePresence mode="popLayout">
            {filteredSessions.map((session, index) => (
              <SessionCard
                key={session.id}
                session={session}
                onClick={() => onSessionClick(session)}
                onAction={(action) => handleSessionAction(session.id, action)}
                index={index}
              />
            ))}
          </AnimatePresence>
        </div>
      ) : (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center py-12"
        >
          <div className="h-16 w-16 rounded-full bg-muted/50 flex items-center justify-center mx-auto mb-4">
            <Layers className="h-8 w-8 text-muted-foreground" />
          </div>
          <h3 className="text-lg font-medium mb-1">No sessions found</h3>
          <p className="text-sm text-muted-foreground mb-4">
            {searchQuery || filterStatus !== 'all' || showStarredOnly
              ? "Try adjusting your search or filters"
              : "Create your first Claude session to get started"}
          </p>
          <Button onClick={onCreateSession} className="gap-2">
            <Plus className="h-4 w-4" />
            Create Session
          </Button>
        </motion.div>
      )}
    </div>
  );
};