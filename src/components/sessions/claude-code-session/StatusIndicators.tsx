import React from 'react';
import { motion } from 'framer-motion';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  <PERSON><PERSON>, 
  Play, 
  MessageCircle, 
  Sparkles, 
  Brain,
  Zap,
  CheckCircle2,
  Al<PERSON><PERSON>riangle,
  Clock
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface StatusIndicatorsProps {
  // Agent state
  detectedAgentOpportunity?: {
    agentType: string;
    confidence: number;
    reason: string;
  } | null;
  activeAgent?: {
    type: string;
    name: string;
    taskId?: string;
    success?: boolean;
  } | null;
  
  // Session state
  isWaitingForResponse?: boolean;
  isPlanMode?: boolean;
  isExtendedThinking?: boolean;
  thinkingProgress?: number;
  thinkingStage?: string;
  
  // Actions
  onExecuteAgent?: () => void;
  onDismissOpportunity?: () => void;
}

export const StatusIndicators: React.FC<StatusIndicatorsProps> = ({
  detectedAgentOpportunity,
  activeAgent,
  isWaitingForResponse,
  isPlanMode,
  isExtendedThinking,
  thinkingProgress,
  thinkingStage,
  onExecuteAgent,
  onDismissOpportunity
}) => {
  return (
    <div className="space-y-2">
      {/* Agent Opportunity Alert */}
      {detectedAgentOpportunity && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          className="mx-4 p-3 rounded-lg border status-info"
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Bot className="h-5 w-5 text-info" />
              <span className="text-sm font-medium">Agent Opportunity Detected</span>
              <Badge variant="secondary" className="text-xs">
                {detectedAgentOpportunity.agentType}
              </Badge>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-xs text-muted-foreground">
                Confidence: {Math.round(detectedAgentOpportunity.confidence * 100)}%
              </span>
              <Button
                size="sm"
                variant="outline"
                onClick={onDismissOpportunity}
              >
                Dismiss
              </Button>
              <Button
                size="sm"
                variant="default"
                onClick={onExecuteAgent}
                className="bg-info hover:bg-info/90"
              >
                <Play className="h-4 w-4 mr-1" />
                Execute Agent
              </Button>
            </div>
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            {detectedAgentOpportunity.reason}
          </p>
        </motion.div>
      )}
      
      {/* Active Agent Status */}
      {activeAgent && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          className="mx-4 p-3 rounded-lg border status-info"
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Bot className="h-5 w-5 text-info animate-pulse" />
              <span className="text-sm font-medium">Agent Running</span>
              <Badge variant="outline" className="text-xs">
                {activeAgent.name}
              </Badge>
            </div>
            {activeAgent.success !== undefined && (
              <Badge variant={activeAgent.success ? "default" : "destructive"}>
                {activeAgent.success ? (
                  <><CheckCircle2 className="h-3 w-3 mr-1" />Completed</>
                ) : (
                  <><AlertTriangle className="h-3 w-3 mr-1" />Failed</>
                )}
              </Badge>
            )}
          </div>
          {activeAgent.taskId && (
            <p className="text-xs text-muted-foreground mt-1">
              Task ID: {activeAgent.taskId}
            </p>
          )}
        </motion.div>
      )}

      {/* Extended Thinking Indicator */}
      {isExtendedThinking && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          className="mx-4 p-3 rounded-lg border status-warning"
        >
          <div className="flex items-center gap-3">
            <Brain className="h-5 w-5 text-warning animate-pulse" />
            <div className="flex-1">
              <div className="flex items-center justify-between mb-1">
                <span className="text-sm font-medium">Extended Thinking</span>
                {thinkingProgress && (
                  <span className="text-xs text-muted-foreground">
                    {Math.round(thinkingProgress)}%
                  </span>
                )}
              </div>
              {thinkingStage && (
                <p className="text-xs text-muted-foreground">{thinkingStage}</p>
              )}
            </div>
          </div>
        </motion.div>
      )}

      {/* Plan Mode Indicator */}
      {isPlanMode && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          className="mx-4 p-3 rounded-lg border status-info"
        >
          <div className="flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-info" />
            <span className="text-sm font-medium">Plan Mode Active</span>
            <Badge variant="outline" className="text-xs">
              Review Required
            </Badge>
          </div>
        </motion.div>
      )}

      {/* Waiting for Response */}
      {isWaitingForResponse && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          className="mx-4 p-3 rounded-lg border status-warning"
        >
          <div className="flex items-center gap-2">
            <MessageCircle className="h-5 w-5 text-warning animate-pulse" />
            <span className="text-sm font-medium">Claude is waiting for your response...</span>
          </div>
        </motion.div>
      )}
    </div>
  );
};