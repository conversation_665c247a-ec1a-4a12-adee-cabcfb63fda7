import { useState, useCallback, useEffect } from 'react';
import { api } from '@/lib/api';

export interface TauriMCPServer {
  name: string;
  transport: string;
  command?: string;
  args: string[];
  env: Record<string, string>;
  url?: string;
  scope: string;
  is_active: boolean;
  status: {
    running: boolean;
    error?: string;
    last_checked?: number;
  };
}

export interface MCPProjectConfig {
  mcp_servers: Record<string, {
    command: string;
    args: string[];
    env: Record<string, string>;
  }>;
}

export const useTauriMCP = (projectPath: string) => {
  const [servers, setServers] = useState<TauriMCPServer[]>([]);
  const [projectConfig, setProjectConfig] = useState<MCPProjectConfig | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load MCP servers and project config
  const loadMCPData = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Load MCP servers
      const serverList = await api.mcpList();
      
      // Load project config
      const config = await api.mcpReadProjectConfig(projectPath);
      
      setServers(serverList);
      setProjectConfig(config);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load MCP data');
    } finally {
      setIsLoading(false);
    }
  }, [projectPath]);

  // Add a new MCP server
  const addServer = useCallback(async (
    name: string,
    transport: string,
    command?: string,
    args: string[] = [],
    env: Record<string, string> = {},
    url?: string,
    scope = 'local'
  ) => {
    try {
      const result = await api.mcpAdd(name, transport, command, args, env, url, scope);
      if (result.success) {
        await loadMCPData();
        return { success: true, message: result.message };
      } else {
        return { success: false, message: result.message };
      }
    } catch (err) {
      return { success: false, message: err instanceof Error ? err.message : 'Failed to add server' };
    }
  }, [loadMCPData]);

  // Remove an MCP server
  const removeServer = useCallback(async (name: string) => {
    try {
      const result = await api.mcpRemove(name);
      await loadMCPData();
      return { success: true, message: result };
    } catch (err) {
      return { success: false, message: err instanceof Error ? err.message : 'Failed to remove server' };
    }
  }, [loadMCPData]);

  // Test connection to a server
  const testConnection = useCallback(async (name: string) => {
    try {
      const result = await api.mcpTestConnection(name);
      return { success: true, message: result };
    } catch (err) {
      return { success: false, message: err instanceof Error ? err.message : 'Connection test failed' };
    }
  }, []);

  // Start MCP server
  const startServer = useCallback(async () => {
    try {
      const result = await api.mcpServe();
      return { success: true, message: result };
    } catch (err) {
      return { success: false, message: err instanceof Error ? err.message : 'Failed to start server' };
    }
  }, []);

  // Save project config
  const saveProjectConfig = useCallback(async (config: MCPProjectConfig) => {
    try {
      const result = await api.mcpSaveProjectConfig(projectPath, config);
      setProjectConfig(config);
      return { success: true, message: result };
    } catch (err) {
      return { success: false, message: err instanceof Error ? err.message : 'Failed to save config' };
    }
  }, [projectPath]);

  // Import from Claude Desktop
  const importFromClaudeDesktop = useCallback(async (scope = 'local') => {
    try {
      const result = await api.mcpAddFromClaudeDesktop(scope);
      await loadMCPData();
      return { success: true, imported: result.imported_count, failed: result.failed_count };
    } catch (err) {
      return { success: false, message: err instanceof Error ? err.message : 'Import failed' };
    }
  }, [loadMCPData]);

  // Reset project choices
  const resetProjectChoices = useCallback(async () => {
    try {
      const result = await api.mcpResetProjectChoices();
      return { success: true, message: result };
    } catch (err) {
      return { success: false, message: err instanceof Error ? err.message : 'Reset failed' };
    }
  }, []);

  // Load data on mount
  useEffect(() => {
    loadMCPData();
  }, [loadMCPData]);

  return {
    servers,
    projectConfig,
    isLoading,
    error,
    loadMCPData,
    addServer,
    removeServer,
    testConnection,
    startServer,
    saveProjectConfig,
    importFromClaudeDesktop,
    resetProjectChoices
  };
};