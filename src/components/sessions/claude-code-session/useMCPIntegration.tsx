import { useState, useCallback, useEffect, useRef } from 'react';
import { api, type MCPServer } from '@/lib/api';

export interface MCPTool {
  name: string;
  description: string;
  inputSchema?: any;
  outputSchema?: any;
}

export interface MCPResource {
  uri: string;
  name: string;
  mimeType?: string;
  description?: string;
}

export interface MCPServerState {
  id: string;
  name: string;
  uri: string;
  status: 'connected' | 'disconnected' | 'connecting' | 'error';
  tools: MCPTool[];
  resources: MCPResource[];
  lastPing?: number;
  error?: string;
  metrics?: {
    requests: number;
    responseTime: number;
    uptime: number;
  };
}

export interface MCPIntegrationState {
  servers: MCPServerState[];
  activeServers: string[];
  isLoading: boolean;
  error: string | null;
  connectionStatus: Record<string, 'connected' | 'disconnected' | 'connecting' | 'error'>;
}

export const useMCPIntegration = (sessionId: string) => {
  const [state, setState] = useState<MCPIntegrationState>({
    servers: [],
    activeServers: [],
    isLoading: false,
    error: null,
    connectionStatus: {},
  });

  const pingInterval = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttempts = useRef<Record<string, number>>({});

  // Load available MCP servers
  const loadServers = useCallback(async () => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));
    try {
      const serverList = await api.mcpList();
      const servers: MCPServerState[] = serverList.map(server => ({
        id: server.id || `mcp-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        name: server.name,
        uri: server.uri || '',
        status: 'disconnected' as const,
        tools: [],
        resources: [],
        metrics: {
          requests: 0,
          responseTime: 0,
          uptime: 0
        }
      }));

      setState(prev => ({
        ...prev,
        servers,
        isLoading: false,
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to load MCP servers',
        isLoading: false,
      }));
    }
  }, []);

  // Connect to an MCP server
  const connectServer = useCallback(async (serverId: string) => {
    const server = state.servers.find(s => s.id === serverId);
    if (!server) {
      setState(prev => ({
        ...prev,
        error: `Server ${serverId} not found`,
      }));
      return;
    }

    // Update connection status
    setState(prev => ({
      ...prev,
      connectionStatus: {
        ...prev.connectionStatus,
        [serverId]: 'connecting',
      },
      servers: prev.servers.map(s =>
        s.id === serverId ? { ...s, status: 'connecting' as const } : s
      ),
    }));

    try {
      // Attempt to connect to the server
      await api.mcpConnect?.(serverId);

      // Fetch tools and resources after connection
      const [tools, resources] = await Promise.all([
        api.mcpGetTools?.(serverId) || [],
        api.mcpGetResources?.(serverId) || [],
      ]);

      // Get server metrics
      const metrics = await api.getMCPMetrics?.(serverId) || {
        requests: 0,
        responseTime: 0,
        uptime: 0
      };

      setState(prev => ({
        ...prev,
        connectionStatus: {
          ...prev.connectionStatus,
          [serverId]: 'connected',
        },
        servers: prev.servers.map(s =>
          s.id === serverId
            ? {
                ...s,
                status: 'connected' as const,
                tools: tools as MCPTool[],
                resources: resources as MCPResource[],
                lastPing: Date.now(),
                metrics
              }
            : s
        ),
        activeServers: [...prev.activeServers, serverId],
      }));

      // Reset reconnect attempts on successful connection
      reconnectAttempts.current[serverId] = 0;
    } catch (error) {
      setState(prev => ({
        ...prev,
        connectionStatus: {
          ...prev.connectionStatus,
          [serverId]: 'error',
        },
        servers: prev.servers.map(s =>
          s.id === serverId
            ? {
                ...s,
                status: 'error' as const,
                error: error instanceof Error ? error.message : 'Connection failed',
              }
            : s
        ),
      }));

      // Schedule reconnect attempt
      scheduleReconnect(serverId);
    }
  }, [state.servers]);

  // Disconnect from an MCP server
  const disconnectServer = useCallback(async (serverId: string) => {
    try {
      await api.mcpDisconnect?.(serverId);
    } catch (error) {
    }

    setState(prev => ({
      ...prev,
      connectionStatus: {
        ...prev.connectionStatus,
        [serverId]: 'disconnected',
      },
      servers: prev.servers.map(s =>
        s.id === serverId
          ? { ...s, status: 'disconnected' as const, tools: [], resources: [] }
          : s
      ),
      activeServers: prev.activeServers.filter(id => id !== serverId),
    }));
  }, []);

  // Execute a tool on an MCP server
  const executeTool = useCallback(async (
    serverId: string,
    toolName: string,
    input: any
  ) => {
    const server = state.servers.find(s => s.id === serverId);
    if (!server || server.status !== 'connected') {
      throw new Error(`Server ${serverId} is not connected`);
    }

    const tool = server.tools.find(t => t.name === toolName);
    if (!tool) {
      throw new Error(`Tool ${toolName} not found on server ${serverId}`);
    }

    try {
      const result = await api.mcpExecuteTool?.(serverId, toolName, input);
      return result;
    } catch (error) {
      throw error;
    }
  }, [state.servers]);

  // Fetch a resource from an MCP server
  const fetchResource = useCallback(async (
    serverId: string,
    resourceUri: string
  ) => {
    const server = state.servers.find(s => s.id === serverId);
    if (!server || server.status !== 'connected') {
      throw new Error(`Server ${serverId} is not connected`);
    }

    try {
      const content = await api.mcpFetchResource?.(serverId, resourceUri);
      return content;
    } catch (error) {
      throw error;
    }
  }, [state.servers]);

  // Schedule reconnection attempt
  const scheduleReconnect = useCallback((serverId: string) => {
    const attempts = reconnectAttempts.current[serverId] || 0;
    if (attempts >= 3) {
      return;
    }

    const delay = Math.min(1000 * Math.pow(2, attempts), 30000); // Exponential backoff
    reconnectAttempts.current[serverId] = attempts + 1;

    setTimeout(() => {
      connectServer(serverId);
    }, delay);
  }, [connectServer]);

  // Ping servers to maintain connection
  const pingServers = useCallback(async () => {
    for (const server of state.servers) {
      if (server.status === 'connected') {
        try {
          await api.mcpPing?.(server.id);
          setState(prev => ({
            ...prev,
            servers: prev.servers.map(s =>
              s.id === server.id
                ? { ...s, lastPing: Date.now() }
                : s
            ),
          }));
        } catch (error) {
          // Mark as disconnected and attempt reconnect
          setState(prev => ({
            ...prev,
            servers: prev.servers.map(s =>
              s.id === server.id
                ? { ...s, status: 'disconnected' as const }
                : s
            ),
          }));
          scheduleReconnect(server.id);
        }
      }
    }
  }, [state.servers, scheduleReconnect]);

  // Get all available tools across connected servers
  const getAllTools = useCallback(() => {
    const tools: Array<MCPTool & { serverId: string }> = [];
    for (const server of state.servers) {
      if (server.status === 'connected') {
        tools.push(...server.tools.map(tool => ({
          ...tool,
          serverId: server.id,
        })));
      }
    }
    return tools;
  }, [state.servers]);

  // Get all available resources across connected servers
  const getAllResources = useCallback(() => {
    const resources: Array<MCPResource & { serverId: string }> = [];
    for (const server of state.servers) {
      if (server.status === 'connected') {
        resources.push(...server.resources.map(resource => ({
          ...resource,
          serverId: server.id,
        })));
      }
    }
    return resources;
  }, [state.servers]);

  // Auto-connect to favorite servers
  const autoConnect = useCallback(async () => {
    try {
      const favoriteServers = await api.mcpGetFavorites?.() || [];
      for (const serverId of favoriteServers) {
        if (state.servers.find(s => s.id === serverId)) {
          await connectServer(serverId);
        }
      }
    } catch (error) {
    }
  }, [state.servers, connectServer]);

  // Load servers on mount
  useEffect(() => {
    loadServers();
  }, [loadServers]);

  // Set up ping interval
  useEffect(() => {
    if (state.activeServers.length > 0) {
      pingInterval.current = setInterval(pingServers, 30000); // Ping every 30 seconds
    } else if (pingInterval.current) {
      clearInterval(pingInterval.current);
      pingInterval.current = null;
    }

    return () => {
      if (pingInterval.current) {
        clearInterval(pingInterval.current);
      }
    };
  }, [state.activeServers, pingServers]);

  // Auto-connect on servers load
  useEffect(() => {
    if (state.servers.length > 0 && state.activeServers.length === 0) {
      autoConnect();
    }
  }, [state.servers, state.activeServers, autoConnect]);

  return {
    servers: state.servers,
    activeServers: state.activeServers,
    isLoading: state.isLoading,
    error: state.error,
    connectionStatus: state.connectionStatus,
    connectServer,
    disconnectServer,
    executeTool,
    fetchResource,
    getAllTools,
    getAllResources,
    reloadServers: loadServers,
  };
};