import React, { useRef, useEffect, useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { StreamMessage } from '../../display/StreamMessage';
import { 
  Terminal, 
  Bot,
  User,
  Clock,
  Copy,
  Check,
  ArrowDown,
  Loader2,
  Sparkles,
  Code2,
  FileText,
  Settings,
  AlertCircle,
  Package,
  Cpu,
  Server,
  Search,
  GitBranch,
  Database,
  Globe,
  Shield,
  Zap,
  Activity,
  Play,
  Pause,
  CheckCircle2,
  XCircle,
  AlertTriangle,
  Layers,
  Network,
  Box
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import type { ClaudeStreamMessage } from '../../agents/AgentExecution';

interface EnhancedCleanMessageListProps {
  messages: ClaudeStreamMessage[];
  projectPath: string;
  isStreaming: boolean;
  onLinkDetected?: (url: string) => void;
  className?: string;
  sessionId?: string;
  activeAgents?: string[];
  mcpServers?: string[];
}

interface AgentCall {
  name: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  startTime: number;
  endTime?: number;
  result?: any;
  error?: string;
  progress?: number;
}

export const EnhancedCleanMessageList: React.FC<EnhancedCleanMessageListProps> = React.memo(({
  messages,
  projectPath,
  isStreaming,
  onLinkDetected,
  className,
  sessionId,
  activeAgents = [],
  mcpServers = []
}) => {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const shouldAutoScrollRef = useRef(true);
  const userHasScrolledRef = useRef(false);
  const [showScrollButton, setShowScrollButton] = useState(false);
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null);
  const [agentCalls, setAgentCalls] = useState<Map<string, AgentCall>>(new Map());
  const [expandedAgents, setExpandedAgents] = useState<Set<string>>(new Set());

  // Track agent calls
  useEffect(() => {
    messages.forEach((message, idx) => {
      if (message.toolCalls) {
        message.toolCalls.forEach(tool => {
          const isAgent = tool.name.toLowerCase().includes('agent') || 
                         tool.name.toLowerCase() === 'task' ||
                         tool.name.startsWith('agent_');
          
          if (isAgent) {
            const callId = `${idx}-${tool.name}`;
            setAgentCalls(prev => {
              const newCalls = new Map(prev);
              if (!newCalls.has(callId)) {
                newCalls.set(callId, {
                  name: tool.name,
                  status: 'running',
                  startTime: Date.now(),
                  progress: 0
                });
              }
              return newCalls;
            });
          }
        });
      }
    });
  }, [messages]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (shouldAutoScrollRef.current && scrollContainerRef.current) {
      const scrollElement = scrollContainerRef.current;
      scrollElement.scrollTop = scrollElement.scrollHeight;
    }
  }, [messages]);

  // Handle scroll events
  const handleScroll = useCallback(() => {
    if (!scrollContainerRef.current) return;
    
    const scrollElement = scrollContainerRef.current;
    const isAtBottom = 
      Math.abs(scrollElement.scrollHeight - scrollElement.scrollTop - scrollElement.clientHeight) < 50;
    
    setShowScrollButton(!isAtBottom);
    
    if (!isAtBottom) {
      userHasScrolledRef.current = true;
      shouldAutoScrollRef.current = false;
    } else if (userHasScrolledRef.current) {
      shouldAutoScrollRef.current = true;
      userHasScrolledRef.current = false;
    }
  }, []);

  // Scroll to bottom function
  const scrollToBottom = useCallback(() => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollTo({
        top: scrollContainerRef.current.scrollHeight,
        behavior: 'smooth'
      });
    }
  }, []);

  // Copy message content
  const copyMessage = useCallback((message: ClaudeStreamMessage, index: number) => {
    const content = typeof message.message === 'string' 
      ? message.message 
      : JSON.stringify(message.message, null, 2);
    
    navigator.clipboard.writeText(content);
    setCopiedIndex(index);
    setTimeout(() => setCopiedIndex(null), 2000);
  }, []);

  // Format timestamp
  const formatTimestamp = (timestamp?: number) => {
    if (!timestamp) return '';
    const date = new Date(timestamp);
    return date.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit'
    });
  };

  // Get tool/agent icon and color
  const getToolInfo = (toolName: string) => {
    const name = toolName.toLowerCase();
    
    // Agent types
    if (name.includes('agent') || name === 'task') {
      return { 
        icon: <Bot className="h-3 w-3" />, 
        color: 'blue',
        label: 'Agent',
        bgColor: 'bg-blue-500/10',
        borderColor: 'border-blue-500/30',
        textColor: 'text-blue-600 dark:text-blue-400'
      };
    }
    
    // MCP servers
    if (name.includes('mcp') || name.startsWith('mcp__')) {
      return { 
        icon: <Server className="h-3 w-3" />, 
        color: 'purple',
        label: name.replace('mcp__', '').replace(/_/g, ' '),
        bgColor: 'bg-purple-500/10',
        borderColor: 'border-purple-500/30',
        textColor: 'text-purple-600 dark:text-purple-400'
      };
    }
    
    // File operations
    if (name.includes('read') || name.includes('write') || name.includes('edit')) {
      return { 
        icon: <FileText className="h-3 w-3" />, 
        color: 'green',
        label: name,
        bgColor: 'bg-green-500/10',
        borderColor: 'border-green-500/30',
        textColor: 'text-green-600 dark:text-green-400'
      };
    }
    
    // Terminal/Bash
    if (name.includes('bash') || name.includes('terminal') || name.includes('shell')) {
      return { 
        icon: <Terminal className="h-3 w-3" />, 
        color: 'orange',
        label: name,
        bgColor: 'bg-orange-500/10',
        borderColor: 'border-orange-500/30',
        textColor: 'text-orange-600 dark:text-orange-400'
      };
    }
    
    // Search
    if (name.includes('search') || name.includes('grep') || name.includes('find')) {
      return { 
        icon: <Search className="h-3 w-3" />, 
        color: 'yellow',
        label: name,
        bgColor: 'bg-yellow-500/10',
        borderColor: 'border-yellow-500/30',
        textColor: 'text-yellow-600 dark:text-yellow-400'
      };
    }
    
    // Git
    if (name.includes('git')) {
      return { 
        icon: <GitBranch className="h-3 w-3" />, 
        color: 'pink',
        label: name,
        bgColor: 'bg-pink-500/10',
        borderColor: 'border-pink-500/30',
        textColor: 'text-pink-600 dark:text-pink-400'
      };
    }
    
    // Database
    if (name.includes('database') || name.includes('db') || name.includes('sql')) {
      return { 
        icon: <Database className="h-3 w-3" />, 
        color: 'indigo',
        label: name,
        bgColor: 'bg-indigo-500/10',
        borderColor: 'border-indigo-500/30',
        textColor: 'text-indigo-600 dark:text-indigo-400'
      };
    }
    
    // Web/Network
    if (name.includes('web') || name.includes('fetch') || name.includes('http')) {
      return { 
        icon: <Globe className="h-3 w-3" />, 
        color: 'cyan',
        label: name,
        bgColor: 'bg-cyan-500/10',
        borderColor: 'border-cyan-500/30',
        textColor: 'text-cyan-600 dark:text-cyan-400'
      };
    }
    
    // Default
    return { 
      icon: <Code2 className="h-3 w-3" />, 
      color: 'gray',
      label: name,
      bgColor: 'bg-gray-500/10',
      borderColor: 'border-gray-500/30',
      textColor: 'text-gray-600 dark:text-gray-400'
    };
  };

  if (messages.length === 0) {
    return (
      <div className={cn("flex-1 flex items-center justify-center p-8", className)}>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center space-y-4 max-w-md"
        >
          <motion.div 
            className="h-16 w-16 rounded-2xl bg-muted/50 flex items-center justify-center mx-auto"
            animate={{ rotate: [0, 5, -5, 0] }}
            transition={{ duration: 4, repeat: Infinity }}
          >
            <Sparkles className="h-8 w-8 text-muted-foreground" />
          </motion.div>
          <div>
            <h3 className="text-lg font-medium mb-1">Ready to assist</h3>
            <p className="text-sm text-muted-foreground">
              {projectPath 
                ? "Ask me anything about your code"
                : "Select a project folder to begin"}
            </p>
          </div>
          
          {/* Show active agents and MCP servers */}
          {(activeAgents.length > 0 || mcpServers.length > 0) && (
            <div className="flex items-center justify-center gap-2 flex-wrap mt-4">
              {activeAgents.length > 0 && (
                <Badge variant="outline" className="text-xs">
                  <Bot className="h-3 w-3 mr-1" />
                  {activeAgents.length} Agent{activeAgents.length > 1 ? 's' : ''} ready
                </Badge>
              )}
              {mcpServers.length > 0 && (
                <Badge variant="outline" className="text-xs">
                  <Server className="h-3 w-3 mr-1" />
                  {mcpServers.length} MCP server{mcpServers.length > 1 ? 's' : ''} connected
                </Badge>
              )}
            </div>
          )}
        </motion.div>
      </div>
    );
  }

  return (
    <div className={cn("flex-1 flex flex-col relative overflow-hidden", className)}>
      {/* Messages Container */}
      <div
        ref={scrollContainerRef}
        onScroll={handleScroll}
        className="flex-1 overflow-y-auto overflow-x-hidden"
        style={{
          scrollbarWidth: 'thin',
          scrollbarColor: 'rgba(155, 155, 155, 0.3) transparent'
        }}
      >
        <div className="max-w-4xl mx-auto px-4 sm:px-6 py-6 space-y-6">
          <AnimatePresence initial={false}>
            {messages.map((message, index) => {
              const isUser = message.type === 'user';
              const isAssistant = message.type === 'assistant';
              const isSystem = message.type === 'system';
              const isCopied = copiedIndex === index;
              const hasTools = message.toolCalls && message.toolCalls.length > 0;
              
              return (
                <motion.div
                  key={`msg-${index}-${message.timestamp || index}`}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3, ease: "easeOut" }}
                  className={cn(
                    "group relative",
                    isUser && "flex justify-end"
                  )}
                >
                  <div className={cn(
                    "relative max-w-[85%]",
                    isUser ? "ml-auto" : "mr-auto"
                  )}>
                    {/* Message Header */}
                    <div className={cn(
                      "flex items-center gap-2 mb-2",
                      isUser && "justify-end"
                    )}>
                      <div className={cn(
                        "flex items-center gap-2",
                        isUser && "flex-row-reverse"
                      )}>
                        <div className={cn(
                          "h-7 w-7 rounded-lg flex items-center justify-center",
                          isUser && "bg-primary/10 text-primary",
                          isAssistant && "bg-muted text-muted-foreground",
                          isSystem && "bg-orange-500/10 text-orange-600 dark:text-orange-400"
                        )}>
                          {isUser ? <User className="h-4 w-4" /> :
                           isAssistant ? <Bot className="h-4 w-4" /> :
                           <Settings className="h-3.5 w-3.5" />}
                        </div>
                        <span className="text-xs font-medium text-muted-foreground">
                          {message.type === 'user' ? 'You' : 
                           message.type === 'assistant' ? 'Claude' : 
                           'System'}
                        </span>
                        {message.timestamp && (
                          <span className="text-xs text-muted-foreground/60">
                            {formatTimestamp(message.timestamp)}
                          </span>
                        )}
                      </div>
                    </div>

                    {/* Message Content */}
                    <div className={cn(
                      "relative rounded-2xl px-4 py-3",
                      isAssistant && "bg-muted/50 border border-border/50",
                      isSystem && "bg-orange-500/5 border border-orange-500/20"
                    )}>
                      {/* Copy button */}
                      <div className={cn(
                        "absolute top-2 opacity-0 group-hover:opacity-100 transition-opacity",
                        isUser ? "left-2" : "right-2"
                      )}>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                size="sm"
                                variant="ghost"
                                className="h-7 w-7 p-0"
                                onClick={() => copyMessage(message, index)}
                              >
                                {isCopied ? (
                                  <Check className="h-3.5 w-3.5" />
                                ) : (
                                  <Copy className="h-3.5 w-3.5" />
                                )}
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              {isCopied ? 'Copied!' : 'Copy'}
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>

                      {/* Stream Message Content */}
                      <div className="prose prose-sm max-w-none dark:prose-invert">
                        <StreamMessage 
                          message={message}
                          streamMessages={messages}
                          onLinkDetected={onLinkDetected}
                        />
                      </div>

                      {/* Enhanced Tool/Agent Call Visualization */}
                      {hasTools && (
                        <div className="mt-3 pt-3 border-t border-border/30">
                          <div className="space-y-2">
                            {message.toolCalls.map((tool, idx) => {
                              const toolInfo = getToolInfo(tool.name);
                              const callId = `${index}-${tool.name}`;
                              const agentCall = agentCalls.get(callId);
                              const isExpanded = expandedAgents.has(callId);
                              const isAgent = tool.name.toLowerCase().includes('agent') || 
                                             tool.name.toLowerCase() === 'task';
                              
                              return (
                                <motion.div
                                  key={idx}
                                  initial={{ opacity: 0, x: -10 }}
                                  animate={{ opacity: 1, x: 0 }}
                                  transition={{ delay: idx * 0.05 }}
                                  className="space-y-2"
                                >
                                  <div className="flex items-center gap-2">
                                    <Badge 
                                      variant="outline"
                                      className={cn(
                                        "text-xs py-1 px-2 cursor-pointer transition-all",
                                        toolInfo.bgColor,
                                        toolInfo.borderColor,
                                        toolInfo.textColor,
                                        isAgent && "hover:scale-105"
                                      )}
                                      onClick={() => {
                                        if (isAgent) {
                                          setExpandedAgents(prev => {
                                            const newSet = new Set(prev);
                                            if (newSet.has(callId)) {
                                              newSet.delete(callId);
                                            } else {
                                              newSet.add(callId);
                                            }
                                            return newSet;
                                          });
                                        }
                                      }}
                                    >
                                      <div className="flex items-center gap-1.5">
                                        {toolInfo.icon}
                                        <span>{toolInfo.label}</span>
                                        {isAgent && agentCall && (
                                          <>
                                            {agentCall.status === 'running' && (
                                              <Loader2 className="h-3 w-3 animate-spin" />
                                            )}
                                            {agentCall.status === 'completed' && (
                                              <CheckCircle2 className="h-3 w-3 text-green-500" />
                                            )}
                                            {agentCall.status === 'failed' && (
                                              <XCircle className="h-3 w-3 text-red-500" />
                                            )}
                                          </>
                                        )}
                                        {isAgent && (
                                          <ChevronDown className={cn(
                                            "h-3 w-3 transition-transform",
                                            isExpanded && "rotate-180"
                                          )} />
                                        )}
                                      </div>
                                    </Badge>
                                    
                                    {/* Agent execution time */}
                                    {agentCall && agentCall.startTime && (
                                      <span className="text-xs text-muted-foreground">
                                        {agentCall.endTime 
                                          ? `${((agentCall.endTime - agentCall.startTime) / 1000).toFixed(1)}s`
                                          : 'Running...'}
                                      </span>
                                    )}
                                  </div>
                                  
                                  {/* Expanded agent details */}
                                  <AnimatePresence>
                                    {isAgent && isExpanded && (
                                      <motion.div
                                        initial={{ height: 0, opacity: 0 }}
                                        animate={{ height: "auto", opacity: 1 }}
                                        exit={{ height: 0, opacity: 0 }}
                                        transition={{ duration: 0.2 }}
                                        className="ml-4 p-2 rounded-lg bg-muted/30 border border-border/30 overflow-hidden"
                                      >
                                        <div className="space-y-1.5">
                                          {agentCall && agentCall.status === 'running' && (
                                            <div className="flex items-center gap-2">
                                              <Activity className="h-3 w-3 text-primary animate-pulse" />
                                              <span className="text-xs">Processing task...</span>
                                              {agentCall.progress && (
                                                <Progress value={agentCall.progress} className="flex-1 h-1" />
                                              )}
                                            </div>
                                          )}
                                          
                                          {tool.input && (
                                            <div>
                                              <span className="text-xs font-medium text-muted-foreground">Input:</span>
                                              <pre className="text-xs mt-1 p-2 rounded bg-background/50 overflow-x-auto">
                                                {JSON.stringify(tool.input, null, 2)}
                                              </pre>
                                            </div>
                                          )}
                                          
                                          {agentCall?.result && (
                                            <div>
                                              <span className="text-xs font-medium text-muted-foreground">Result:</span>
                                              <pre className="text-xs mt-1 p-2 rounded bg-background/50 overflow-x-auto">
                                                {JSON.stringify(agentCall.result, null, 2)}
                                              </pre>
                                            </div>
                                          )}
                                          
                                          {agentCall?.error && (
                                            <div className="flex items-start gap-2 text-red-500">
                                              <AlertTriangle className="h-3 w-3 mt-0.5" />
                                              <span className="text-xs">{agentCall.error}</span>
                                            </div>
                                          )}
                                        </div>
                                      </motion.div>
                                    )}
                                  </AnimatePresence>
                                </motion.div>
                              );
                            })}
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Message Status */}
                    {message.status && (
                      <div className={cn(
                        "flex items-center gap-1 mt-1",
                        isUser && "justify-end"
                      )}>
                        {message.status === 'error' && (
                          <div className="flex items-center gap-1 text-xs text-red-500">
                            <AlertCircle className="h-3 w-3" />
                            <span>Error</span>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </motion.div>
              );
            })}
          </AnimatePresence>

          {/* Enhanced Streaming Indicator */}
          {isStreaming && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="flex justify-start"
            >
              <div className="bg-muted/50 rounded-2xl px-4 py-3">
                <div className="flex items-center gap-3">
                  <div className="h-7 w-7 rounded-lg bg-muted flex items-center justify-center">
                    <Bot className="h-4 w-4 text-muted-foreground" />
                  </div>
                  <div className="flex items-center gap-2">
                    <Loader2 className="h-4 w-4 animate-spin text-primary" />
                    <span className="text-sm text-muted-foreground">Claude is thinking</span>
                    {activeAgents.length > 0 && (
                      <Badge variant="secondary" className="text-xs">
                        <Layers className="h-3 w-3 mr-1" />
                        with {activeAgents.length} agent{activeAgents.length > 1 ? 's' : ''}
                      </Badge>
                    )}
                    <div className="flex gap-0.5 ml-2">
                      <motion.span
                        className="w-1.5 h-1.5 bg-primary/60 rounded-full"
                        animate={{ opacity: [0.3, 1, 0.3] }}
                        transition={{ duration: 1.4, repeat: Infinity, delay: 0 }}
                      />
                      <motion.span
                        className="w-1.5 h-1.5 bg-primary/60 rounded-full"
                        animate={{ opacity: [0.3, 1, 0.3] }}
                        transition={{ duration: 1.4, repeat: Infinity, delay: 0.2 }}
                      />
                      <motion.span
                        className="w-1.5 h-1.5 bg-primary/60 rounded-full"
                        animate={{ opacity: [0.3, 1, 0.3] }}
                        transition={{ duration: 1.4, repeat: Infinity, delay: 0.4 }}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          )}

          {/* Bottom spacing for floating input */}
          <div className="h-32" />
        </div>
      </div>

      {/* Scroll to bottom button */}
      <AnimatePresence>
        {showScrollButton && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 10 }}
            transition={{ duration: 0.2 }}
            className="absolute bottom-6 right-6"
          >
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={scrollToBottom}
                    className="h-9 w-9 rounded-full shadow-lg border-border/50"
                  >
                    <ArrowDown className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Jump to bottom</TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
});

EnhancedCleanMessageList.displayName = 'EnhancedCleanMessageList';