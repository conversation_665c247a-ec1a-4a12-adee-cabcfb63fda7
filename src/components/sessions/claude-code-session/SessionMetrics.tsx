import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { 
  Activity, 
  Clock, 
  MessageSquare, 
  Hash, 
  Zap, 
  FileText,
  Bot,
  Server,
  TrendingUp,
  Target,
  Cpu,
  BarChart3
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface SessionMetricsProps {
  metrics: {
    duration: number;
    messageCount: number;
    tokenCount: number;
    toolsExecuted: number;
    filesModified: number;
    agentsUsed: number;
    mcpCalls: number;
    errorRate: number;
    avgResponseTime: number;
  };
  isCompact?: boolean;
  className?: string;
}

const MetricCard: React.FC<{
  icon: React.ReactNode;
  label: string;
  value: string | number;
  change?: number;
  color?: string;
  progress?: number;
  className?: string;
}> = ({ icon, label, value, change, color = 'primary', progress, className }) => (
  <motion.div
    initial={{ opacity: 0, scale: 0.95 }}
    animate={{ opacity: 1, scale: 1 }}
    transition={{ duration: 0.2 }}
    className={cn(
      "p-4 rounded-xl border border-border/50 bg-card/50 backdrop-blur-sm",
      "hover:shadow-lg hover:border-primary/30 transition-all duration-200",
      className
    )}
  >
    <div className="flex items-center justify-between mb-2">
      <div className={cn(
        "h-8 w-8 rounded-lg flex items-center justify-center",
        color === 'primary' && "bg-primary/10 text-primary",
        color === 'success' && "bg-success/10 text-success",
        color === 'warning' && "bg-warning/10 text-warning",
        color === 'info' && "bg-info/10 text-info"
      )}>
        {icon}
      </div>
      {change !== undefined && (
        <Badge 
          variant={change >= 0 ? "default" : "destructive"}
          className="text-xs"
        >
          {change >= 0 ? '+' : ''}{change}%
        </Badge>
      )}
    </div>
    
    <div className="space-y-1">
      <p className="text-2xl font-bold">{value}</p>
      <p className="text-xs text-muted-foreground">{label}</p>
      {progress !== undefined && (
        <Progress value={progress} className="h-1 mt-2" />
      )}
    </div>
  </motion.div>
);

export const SessionMetrics: React.FC<SessionMetricsProps> = ({
  metrics,
  isCompact = false,
  className
}) => {
  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) return `${hours}h ${minutes}m`;
    if (minutes > 0) return `${minutes}m ${secs}s`;
    return `${secs}s`;
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  if (isCompact) {
    return (
      <Card className={cn("border-primary/20 bg-card/80 backdrop-blur-sm", className)}>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Activity className="h-5 w-5 text-primary" />
              <div>
                <p className="font-medium text-sm">Session Active</p>
                <p className="text-xs text-muted-foreground">
                  {formatDuration(metrics.duration)} • {metrics.messageCount} msgs
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="text-xs">
                <Hash className="h-3 w-3 mr-1" />
                {formatNumber(metrics.tokenCount)}
              </Badge>
              {metrics.agentsUsed > 0 && (
                <Badge variant="outline" className="text-xs bg-purple-500/10">
                  <Bot className="h-3 w-3 mr-1" />
                  {metrics.agentsUsed}
                </Badge>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <Card className="border-primary/20 bg-card/80 backdrop-blur-sm">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <BarChart3 className="h-5 w-5 text-primary" />
            Session Analytics
          </CardTitle>
        </CardHeader>
      </Card>

      {/* Core Metrics Grid */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <MetricCard
          icon={<Clock className="h-4 w-4" />}
          label="Duration"
          value={formatDuration(metrics.duration)}
          color="primary"
        />
        
        <MetricCard
          icon={<MessageSquare className="h-4 w-4" />}
          label="Messages"
          value={metrics.messageCount}
          color="success"
        />
        
        <MetricCard
          icon={<Hash className="h-4 w-4" />}
          label="Tokens"
          value={formatNumber(metrics.tokenCount)}
          color="info"
        />
        
        <MetricCard
          icon={<Zap className="h-4 w-4" />}
          label="Tools Used"
          value={metrics.toolsExecuted}
          color="warning"
        />
      </div>

      {/* Advanced Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <MetricCard
          icon={<FileText className="h-4 w-4" />}
          label="Files Modified"
          value={metrics.filesModified}
          color="success"
        />
        
        {metrics.agentsUsed > 0 && (
          <MetricCard
            icon={<Bot className="h-4 w-4" />}
            label="Agents Used"
            value={metrics.agentsUsed}
            color="primary"
          />
        )}
        
        {metrics.mcpCalls > 0 && (
          <MetricCard
            icon={<Server className="h-4 w-4" />}
            label="MCP Calls"
            value={metrics.mcpCalls}
            color="info"
          />
        )}
      </div>

      {/* Performance Metrics */}
      <Card className="border-border/50 bg-card/50 backdrop-blur-sm">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-base">
            <TrendingUp className="h-4 w-4 text-primary" />
            Performance
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Error Rate</span>
                <span className="text-sm font-medium">{metrics.errorRate.toFixed(1)}%</span>
              </div>
              <Progress 
                value={metrics.errorRate} 
                className={cn(
                  "h-2",
                  metrics.errorRate > 10 && "bg-destructive/20"
                )}
              />
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Avg Response</span>
                <span className="text-sm font-medium">{metrics.avgResponseTime.toFixed(1)}s</span>
              </div>
              <Progress 
                value={Math.min(metrics.avgResponseTime * 10, 100)} 
                className="h-2"
              />
            </div>
          </div>
          
          <Separator />
          
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Efficiency Score</span>
            <div className="flex items-center gap-2">
              <Target className="h-4 w-4 text-success" />
              <span className="font-medium text-success">
                {Math.max(0, 100 - metrics.errorRate * 2 - metrics.avgResponseTime * 5).toFixed(0)}%
              </span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};