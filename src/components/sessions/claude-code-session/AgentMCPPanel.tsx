import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Bo<PERSON>,
  Server,
  Play,
  Pause,
  Settings,
  Activity,
  Zap,
  Link,
  AlertCircle,
  CheckCircle,
  XCircle,
  Loader2,
  ChevronRight,
  ChevronDown,
  Plus,
  Trash2,
  RefreshCw,
  Cpu,
  Database,
  Network,
  Cog,
  ExternalLink,
  Terminal,
  GitBranch,
  BarChart3,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardHeader, CardTitle, CardContent, CardDescription } from '@/components/ui/card';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import type { Agent, AgentExecution } from './useAgentOrchestration';
import type { MCPServerState } from './useMCPIntegration';
import { api } from '@/lib/api';

interface AgentMCPPanelProps {
  agents: Agent[];
  activeAgents: string[];
  executions: AgentExecution[];
  servers: MCPServerState[];
  activeServers: string[];
  sessionId: string;
  projectPath: string;
  onActivateAgent: (agentId: string) => void;
  onDeactivateAgent: (agentId: string) => void;
  onExecuteAgent: (agentId: string, input: any) => void;
  onConnectServer: (serverId: string) => void;
  onDisconnectServer: (serverId: string) => void;
  onExecuteTool: (serverId: string, toolName: string, input: any) => void;
  onRefreshServers?: () => void;
  onRefreshAgents?: () => void;
  className?: string;
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
}

export const AgentMCPPanel: React.FC<AgentMCPPanelProps> = ({
  agents,
  activeAgents,
  executions,
  servers,
  activeServers,
  sessionId,
  projectPath,
  onActivateAgent,
  onDeactivateAgent,
  onExecuteAgent,
  onConnectServer,
  onDisconnectServer,
  onExecuteTool,
  onRefreshServers,
  onRefreshAgents,
  className,
  isCollapsed = false,
  onToggleCollapse,
}) => {
  const [activeTab, setActiveTab] = useState('agents');
  const [expandedAgents, setExpandedAgents] = useState<string[]>([]);
  const [expandedServers, setExpandedServers] = useState<string[]>([]);
  const [serverMetrics, setServerMetrics] = useState<Record<string, any>>({});
  const [agentWorkflows, setAgentWorkflows] = useState<Record<string, any>>({});

  // Load server metrics and agent workflows
  useEffect(() => {
    const loadMetrics = async () => {
      try {
        // Load server metrics
        const metrics: Record<string, any> = {};
        for (const server of servers) {
          try {
            // Get server-specific metrics if available
            const serverMetrics = await api.getMCPMetrics?.(server.id) || {};
            metrics[server.id] = serverMetrics;
          } catch (error) {
            // Silently fail for individual server metrics
          }
        }
        setServerMetrics(metrics);
        
        // Load agent workflows
        const workflows: Record<string, any> = {};
        for (const agent of agents) {
          try {
            // Get agent workflow history if available
            const agentWorkflows = await api.getAgentWorkflows?.(agent.id) || [];
            workflows[agent.id] = agentWorkflows.slice(0, 3); // Only show recent 3
          } catch (error) {
            // Silently fail for individual agent workflows
          }
        }
        setAgentWorkflows(workflows);
      } catch (error) {
        // Silently fail metrics loading
      }
    };
    
    if (servers.length > 0 || agents.length > 0) {
      loadMetrics();
    }
  }, [servers, agents]);

  const toggleAgentExpansion = (agentId: string) => {
    setExpandedAgents(prev =>
      prev.includes(agentId)
        ? prev.filter(id => id !== agentId)
        : [...prev, agentId]
    );
  };

  const toggleServerExpansion = (serverId: string) => {
    setExpandedServers(prev =>
      prev.includes(serverId)
        ? prev.filter(id => id !== serverId)
        : [...prev, serverId]
    );
  };

  // Get server metrics display
  const getServerMetricsDisplay = (serverId: string) => {
    const metrics = serverMetrics[serverId];
    if (!metrics) return null;
    
    return (
      <div className="grid grid-cols-2 gap-2 text-xs">
        {metrics.requests && (
          <div className="flex items-center gap-1">
            <Terminal className="h-3 w-3" />
            <span>{metrics.requests} req</span>
          </div>
        )}
        {metrics.responseTime && (
          <div className="flex items-center gap-1">
            <BarChart3 className="h-3 w-3" />
            <span>{metrics.responseTime}ms</span>
          </div>
        )}
        {metrics.uptime && (
          <div className="flex items-center gap-1">
            <Activity className="h-3 w-3" />
            <span>{metrics.uptime}%</span>
          </div>
        )}
      </div>
    );
  };

  // Get agent workflow history
  const getAgentWorkflowHistory = (agentId: string) => {
    const workflows = agentWorkflows[agentId];
    if (!workflows || workflows.length === 0) return null;
    
    return (
      <div className="space-y-1">
        <div className="text-xs font-medium flex items-center gap-1">
          <GitBranch className="h-3 w-3" />
          Recent Workflows:
        </div>
        {workflows.map((workflow: any, index: number) => (
          <div key={index} className="text-xs text-muted-foreground flex items-center justify-between">
            <span className="truncate">{workflow.name}</span>
            <Badge 
              variant={workflow.status === 'completed' ? 'default' : workflow.status === 'failed' ? 'destructive' : 'secondary'}
              className="h-4 px-1 text-[10px]"
            >
              {workflow.status}
            </Badge>
          </div>
        ))}
      </div>
    );
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
      case 'connected':
        return <Activity className="w-4 h-4 text-green-500 animate-pulse" />;
      case 'paused':
      case 'connecting':
        return <Loader2 className="w-4 h-4 text-yellow-500 animate-spin" />;
      case 'stopped':
      case 'disconnected':
        return <XCircle className="w-4 h-4 text-gray-400" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      case 'available':
        return <CheckCircle className="w-4 h-4 text-blue-500" />;
      default:
        return <Activity className="w-4 h-4 text-gray-400" />;
    }
  };

  const getExecutionStatus = (status: string) => {
    switch (status) {
      case 'completed':
        return { icon: <CheckCircle className="w-3 h-3" />, color: 'text-green-500' };
      case 'failed':
        return { icon: <XCircle className="w-3 h-3" />, color: 'text-red-500' };
      case 'running':
        return { icon: <Loader2 className="w-3 h-3 animate-spin" />, color: 'text-blue-500' };
      case 'pending':
        return { icon: <Activity className="w-3 h-3" />, color: 'text-gray-400' };
      default:
        return { icon: <Activity className="w-3 h-3" />, color: 'text-gray-400' };
    }
  };

  if (isCollapsed) {
    return (
      <div className={cn("w-12 bg-background border-l", className)}>
        <div className="flex flex-col items-center py-4 space-y-4">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={onToggleCollapse}
                  className="w-8 h-8"
                >
                  <ChevronRight className="w-4 h-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="left">
                Expand Agent & MCP Panel
              </TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <div className="relative">
                  <Bot className="w-5 h-5 text-muted-foreground" />
                  {activeAgents.length > 0 && (
                    <span className="absolute -top-1 -right-1 w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                  )}
                </div>
              </TooltipTrigger>
              <TooltipContent side="left">
                {activeAgents.length} Active Agents
              </TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <div className="relative">
                  <Server className="w-5 h-5 text-muted-foreground" />
                  {activeServers.length > 0 && (
                    <span className="absolute -top-1 -right-1 w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                  )}
                </div>
              </TooltipTrigger>
              <TooltipContent side="left">
                {activeServers.length} Connected Servers
              </TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={onRefreshAgents}
                  className="w-8 h-8"
                >
                  <RefreshCw className="w-4 h-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="left">
                Refresh Agents
              </TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={onRefreshServers}
                  className="w-8 h-8"
                >
                  <RefreshCw className="w-4 h-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="left">
                Refresh MCP Servers
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("w-80 bg-background border-l flex flex-col", className)}>
      <div className="p-4 border-b flex items-center justify-between">
        <h3 className="font-semibold text-sm">Agent & MCP Control</h3>
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="icon"
            onClick={onRefreshAgents}
            className="w-6 h-6"
          >
            <RefreshCw className="w-3 h-3" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={onRefreshServers}
            className="w-6 h-6"
          >
            <RefreshCw className="w-3 h-3" />
          </Button>
          {onToggleCollapse && (
            <Button
              variant="ghost"
              size="icon"
              onClick={onToggleCollapse}
              className="w-6 h-6"
            >
              <ChevronDown className="w-4 h-4" />
            </Button>
          )}
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
        <TabsList className="grid w-full grid-cols-2 px-4">
          <TabsTrigger value="agents" className="text-xs">
            <Bot className="w-3 h-3 mr-1" />
            Agents ({activeAgents.length})
          </TabsTrigger>
          <TabsTrigger value="mcp" className="text-xs">
            <Server className="w-3 h-3 mr-1" />
            MCP ({activeServers.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="agents" className="flex-1 p-4 space-y-3">
          <ScrollArea className="h-[calc(100vh-200px)]">
            <div className="space-y-2">
              {agents.map((agent) => {
                const isActive = activeAgents.includes(agent.id);
                const isExpanded = expandedAgents.includes(agent.id);
                const agentExecutions = executions.filter(e => e.agentId === agent.id);
                const workflowHistory = getAgentWorkflowHistory(agent.id);

                return (
                  <Card key={agent.id} className="overflow-hidden">
                    <CardHeader className="p-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="ghost"
                            size="icon"
                            className="w-5 h-5"
                            onClick={() => toggleAgentExpansion(agent.id)}
                          >
                            {isExpanded ? (
                              <ChevronDown className="w-3 h-3" />
                            ) : (
                              <ChevronRight className="w-3 h-3" />
                            )}
                          </Button>
                          {getStatusIcon(agent.status)}
                          <span className="text-sm font-medium">{agent.name}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Button
                            variant="ghost"
                            size="icon"
                            className="w-6 h-6"
                            onClick={() => {}}
                          >
                            <Settings className="w-3 h-3" />
                          </Button>
                          <Button
                            variant={isActive ? "destructive" : "default"}
                            size="icon"
                            className="w-6 h-6"
                            onClick={() =>
                              isActive
                                ? onDeactivateAgent(agent.id)
                                : onActivateAgent(agent.id)
                            }
                          >
                            {isActive ? (
                              <Pause className="w-3 h-3" />
                            ) : (
                              <Play className="w-3 h-3" />
                            )}
                          </Button>
                        </div>
                      </div>
                    </CardHeader>

                    <AnimatePresence>
                      {isExpanded && (
                        <motion.div
                          initial={{ height: 0 }}
                          animate={{ height: 'auto' }}
                          exit={{ height: 0 }}
                          transition={{ duration: 0.2 }}
                        >
                          <CardContent className="p-3 pt-0 space-y-2">
                            <p className="text-xs text-muted-foreground">
                              {agent.description}
                            </p>
                            
                            <div className="flex items-center justify-between text-xs">
                              <span className="text-muted-foreground">Category:</span>
                              <Badge variant="secondary" className="text-xs">
                                {agent.category}
                              </Badge>
                            </div>

                            {agent.metrics && (
                              <div className="space-y-1">
                                <div className="flex justify-between text-xs">
                                  <span>Success Rate:</span>
                                  <span className="font-medium">
                                    {agent.metrics.successRate.toFixed(1)}%
                                  </span>
                                </div>
                                <Progress value={agent.metrics.successRate} className="h-1" />
                              </div>
                            )}

                            {workflowHistory}

                            {agentExecutions.length > 0 && (
                              <div className="space-y-1">
                                <div className="text-xs font-medium">Recent Executions:</div>
                                <div className="space-y-1">
                                  {agentExecutions.slice(0, 3).map((execution) => {
                                    const status = getExecutionStatus(execution.status);
                                    return (
                                      <div
                                        key={execution.id}
                                        className="flex items-center justify-between text-xs"
                                      >
                                        <div className={cn("flex items-center gap-1", status.color)}>
                                          {status.icon}
                                          <span>{execution.status}</span>
                                        </div>
                                        {execution.executionTimeMs && (
                                          <span className="text-muted-foreground">
                                            {(execution.executionTimeMs / 1000).toFixed(2)}s
                                          </span>
                                        )}
                                      </div>
                                    );
                                  })}
                                </div>
                              </div>
                            )}

                            <div className="flex gap-1">
                              <Button
                                variant="outline"
                                size="sm"
                                className="text-xs h-6"
                                onClick={() => onExecuteAgent(agent.id, {})}
                                disabled={!isActive}
                              >
                                <Zap className="w-3 h-3 mr-1" />
                                Execute
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                className="text-xs h-6"
                                onClick={() => {
                                  // Open agent configuration
                                }}
                              >
                                <Cog className="w-3 h-3 mr-1" />
                                Configure
                              </Button>
                            </div>
                          </CardContent>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </Card>
                );
              })}
            </div>
          </ScrollArea>
        </TabsContent>

        <TabsContent value="mcp" className="flex-1 p-4 space-y-3">
          <ScrollArea className="h-[calc(100vh-200px)]">
            <div className="space-y-2">
              {servers.map((server) => {
                const isActive = activeServers.includes(server.id);
                const isExpanded = expandedServers.includes(server.id);

                return (
                  <Card key={server.id} className="overflow-hidden">
                    <CardHeader className="p-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="ghost"
                            size="icon"
                            className="w-5 h-5"
                            onClick={() => toggleServerExpansion(server.id)}
                          >
                            {isExpanded ? (
                              <ChevronDown className="w-3 h-3" />
                            ) : (
                              <ChevronRight className="w-3 h-3" />
                            )}
                          </Button>
                          {getStatusIcon(server.status)}
                          <span className="text-sm font-medium">{server.name}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Button
                            variant="ghost"
                            size="icon"
                            className="w-6 h-6"
                            onClick={() => {
                              // Open server configuration
                            }}
                          >
                            <Settings className="w-3 h-3" />
                          </Button>
                          <Button
                            variant={isActive ? "destructive" : "default"}
                            size="icon"
                            className="w-6 h-6"
                            onClick={() =>
                              isActive
                                ? onDisconnectServer(server.id)
                                : onConnectServer(server.id)
                            }
                          >
                            {isActive ? (
                              <Link className="w-3 h-3" />
                            ) : (
                              <Link className="w-3 h-3 opacity-50" />
                            )}
                          </Button>
                        </div>
                      </div>
                    </CardHeader>

                    <AnimatePresence>
                      {isExpanded && (
                        <motion.div
                          initial={{ height: 0 }}
                          animate={{ height: 'auto' }}
                          exit={{ height: 0 }}
                          transition={{ duration: 0.2 }}
                        >
                          <CardContent className="p-3 pt-0 space-y-2">
                            <div className="text-xs text-muted-foreground flex items-center gap-1">
                              <Terminal className="h-3 w-3" />
                              {server.uri}
                            </div>

                            {server.error && (
                              <div className="text-xs text-red-500 flex items-center gap-1">
                                <AlertCircle className="w-3 h-3" />
                                {server.error}
                              </div>
                            )}

                            {server.tools.length > 0 && (
                              <div className="space-y-1">
                                <div className="text-xs font-medium flex items-center gap-1">
                                  <Cpu className="h-3 w-3" />
                                  Tools ({server.tools.length}):
                                </div>
                                <div className="space-y-1">
                                  {server.tools.slice(0, 3).map((tool) => (
                                    <div
                                      key={tool.name}
                                      className="flex items-center justify-between text-xs"
                                    >
                                      <span className="truncate">{tool.name}</span>
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        className="h-5 px-1"
                                        onClick={() => onExecuteTool(server.id, tool.name, {})}
                                        disabled={!isActive}
                                      >
                                        <Play className="w-3 h-3" />
                                      </Button>
                                    </div>
                                  ))}
                                  {server.tools.length > 3 && (
                                    <div className="text-xs text-muted-foreground">
                                      +{server.tools.length - 3} more
                                    </div>
                                  )}
                                </div>
                              </div>
                            )}

                            {server.resources.length > 0 && (
                              <div className="space-y-1">
                                <div className="text-xs font-medium flex items-center gap-1">
                                  <Database className="h-3 w-3" />
                                  Resources ({server.resources.length})
                                </div>
                                <div className="space-y-1">
                                  {server.resources.slice(0, 2).map((resource) => (
                                    <div key={resource.uri} className="text-xs text-muted-foreground flex items-center gap-1">
                                      <Database className="h-3 w-3" />
                                      <span className="truncate">{resource.name}</span>
                                    </div>
                                  ))}
                                  {server.resources.length > 2 && (
                                    <div className="text-xs text-muted-foreground">
                                      +{server.resources.length - 2} more
                                    </div>
                                  )}
                                </div>
                              </div>
                            )}

                            {server.lastPing && (
                              <div className="text-xs text-muted-foreground">
                                Last ping: {new Date(server.lastPing).toLocaleTimeString()}
                              </div>
                            )}

                            <div className="flex gap-1">
                              <Button
                                variant="outline"
                                size="sm"
                                className="text-xs h-6"
                                onClick={() => onConnectServer(server.id)}
                                disabled={isActive}
                              >
                                <RefreshCw className="w-3 h-3 mr-1" />
                                Reconnect
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                className="text-xs h-6"
                                onClick={() => {
                                  // Test connection
                                }}
                              >
                                <ExternalLink className="w-3 h-3 mr-1" />
                                Test
                              </Button>
                            </div>
                          </CardContent>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </Card>
                );
              })}
            </div>
          </ScrollArea>
        </TabsContent>
      </Tabs>
    </div>
  );
};