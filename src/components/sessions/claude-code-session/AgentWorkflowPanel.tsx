/**
 * Agent Workflow Panel
 * UI for managing and executing multi-agent workflows within Claude sessions
 */

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Bot,
  Play,
  Pause,
  RotateCcw,
  Plus,
  Trash2,
  GitBranch,
  Zap,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  ChevronRight,
  ChevronDown,
  Settings,
  Save,
  FileText,
  Shuffle,
  X,
  Workflow,
  Target,
  Layers,
  TrendingUp,
  BarChart3,
  Calendar,
  Hash,
  Cpu
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { api } from '@/lib/api';
import { agentOrchestrator } from '@/services/agentOrchestrationService';
import {
  type AgentWorkflow,
  type AgentTask,
  type WorkflowTemplate,
  BUILT_IN_WORKFLOWS,
  WorkflowBuilder
} from '@/types/agent-orchestration';
import { cn } from '@/lib/utils';

interface AgentWorkflowPanelProps {
  sessionId: string;
  projectPath: string;
  messages: any[];
  onClose?: () => void;
  className?: string;
}

export const AgentWorkflowPanel: React.FC<AgentWorkflowPanelProps> = ({
  sessionId,
  projectPath,
  messages,
  onClose,
  className
}) => {
  const [activeTab, setActiveTab] = useState<'templates' | 'builder' | 'running' | 'analytics'>('templates');
  const [selectedTemplate, setSelectedTemplate] = useState<WorkflowTemplate | null>(null);
  const [customWorkflow, setCustomWorkflow] = useState<Partial<AgentWorkflow>>({
    name: '',
    description: '',
    tasks: [],
    executionMode: 'sequential'
  });
  const [availableAgents, setAvailableAgents] = useState<any[]>([]);
  const [runningWorkflows, setRunningWorkflows] = useState<AgentWorkflow[]>([]);
  const [workflowResults, setWorkflowResults] = useState<Map<string, any>>(new Map());
  const [expandedWorkflows, setExpandedWorkflows] = useState<Set<string>>(new Set());
  const [isExecuting, setIsExecuting] = useState(false);
  const [workflowAnalytics, setWorkflowAnalytics] = useState<any>(null);

  // Load available agents and analytics
  useEffect(() => {
    loadAvailableAgents();
    loadWorkflowAnalytics();
  }, []);

  const loadAvailableAgents = async () => {
    try {
      const agents = await api.listAgents();
      setAvailableAgents(agents);
    } catch (error) {
    }
  };

  const loadWorkflowAnalytics = async () => {
    try {
      // Load workflow analytics for the session
      const analytics = await api.getSessionWorkflowAnalytics?.(sessionId) || null;
      setWorkflowAnalytics(analytics);
    } catch (error) {
    }
  };

  // Execute workflow from template
  const executeTemplate = async (template: WorkflowTemplate) => {
    const builder = new WorkflowBuilder()
      .setName(template.name)
      .setDescription(template.description)
      .setContext({
        sessionId,
        projectPath,
        messages: messages.slice(-10),
        variables: {},
        sharedMemory: {}
      });

    // Map template agents to actual agents
    for (const agentRole of template.agents) {
      const agent = availableAgents.find(a =>
        a.name.toLowerCase().includes(agentRole.suggestedAgent?.toLowerCase() || '')
      );

      if (agent) {
        builder.addTask({
          agentId: agent.id,
          agentName: agent.name,
          task: `${agentRole.role} for ${projectPath}`,
          dependencies: [] // Will be set based on execution pattern
        });
      }
    }

    const workflow = builder.build();
    await executeWorkflow(workflow);
  };

  // Execute a workflow
  const executeWorkflow = async (workflow: AgentWorkflow) => {
    setIsExecuting(true);
    setActiveTab('running');
    
    // Add to running workflows
    setRunningWorkflows(prev => [...prev, workflow]);
    
    try {
      const result = await agentOrchestrator.executeWorkflow(workflow);
      
      // Store results
      setWorkflowResults(prev => new Map(prev).set(workflow.id, result));
      
      // Update workflow status in UI
      setRunningWorkflows(prev =>
        prev.map(w => w.id === workflow.id
          ? { ...w, status: result.success ? 'completed' : 'failed' }
          : w
        )
      );
    } catch (error) {
      
      // Update workflow status to failed
      setRunningWorkflows(prev =>
        prev.map(w => w.id === workflow.id
          ? { ...w, status: 'failed' }
          : w
        )
      );
    } finally {
      setIsExecuting(false);
    }
  };

  // Cancel a workflow
  const cancelWorkflow = async (workflowId: string) => {
    try {
      await agentOrchestrator.cancelWorkflow(workflowId);
      
      setRunningWorkflows(prev =>
        prev.map(w => w.id === workflowId
          ? { ...w, status: 'cancelled' }
          : w
        )
      );
    } catch (error) {
    }
  };

  // Retry a failed task
  const retryTask = async (workflowId: string, taskId: string) => {
    try {
      await agentOrchestrator.retryTask(workflowId, taskId);
    } catch (error) {
    }
  };

  // Add task to custom workflow
  const addTaskToWorkflow = () => {
    const newTask: AgentTask = {
      id: `task-${Date.now()}`,
      agentId: 0,
      agentName: '',
      task: '',
      dependencies: [],
      status: 'pending',
      retryCount: 0,
      maxRetries: 3
    };

    setCustomWorkflow(prev => ({
      ...prev,
      tasks: [...(prev.tasks || []), newTask]
    }));
  };

  // Remove task from custom workflow
  const removeTaskFromWorkflow = (taskId: string) => {
    setCustomWorkflow(prev => ({
      ...prev,
      tasks: prev.tasks?.filter(t => t.id !== taskId) || []
    }));
  };

  // Toggle workflow expansion
  const toggleWorkflowExpansion = (workflowId: string) => {
    setExpandedWorkflows(prev => {
      const next = new Set(prev);
      if (next.has(workflowId)) {
        next.delete(workflowId);
      } else {
        next.add(workflowId);
      }
      return next;
    });
  };

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'running':
        return <Clock className="h-4 w-4 text-blue-500 animate-spin" />;
      case 'cancelled':
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  // Get execution mode icon
  const getExecutionModeIcon = (mode: string) => {
    switch (mode) {
      case 'parallel':
        return <Shuffle className="h-4 w-4" />;
      case 'sequential':
        return <GitBranch className="h-4 w-4" />;
      default:
        return <Zap className="h-4 w-4" />;
    }
  };

  return (
    <Card className={cn("w-full h-full flex flex-col", className)}>
      <CardHeader className="flex-shrink-0">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Bot className="h-5 w-5" />
            Agent Workflows
          </CardTitle>
          {onClose && (
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </CardHeader>

      <CardContent className="flex-1 overflow-hidden">
        <Tabs value={activeTab} onValueChange={setActiveTab as any} className="h-full flex flex-col">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="templates">Templates</TabsTrigger>
            <TabsTrigger value="builder">Builder</TabsTrigger>
            <TabsTrigger value="running">
              Running
              {runningWorkflows.filter(w => w.status === 'running').length > 0 && (
                <Badge variant="secondary" className="ml-2">
                  {runningWorkflows.filter(w => w.status === 'running').length}
                </Badge>
              )}
            </TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          <TabsContent value="templates" className="flex-1 overflow-auto">
            <div className="space-y-4">
              {BUILT_IN_WORKFLOWS.map(template => (
                <Card key={template.id} className="p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h3 className="font-medium">{template.name}</h3>
                      <p className="text-sm text-muted-foreground mt-1">
                        {template.description}
                      </p>
                      <div className="flex items-center gap-2 mt-2">
                        <Badge variant="outline">{template.category}</Badge>
                        <span className="text-xs text-muted-foreground">
                          {template.agents.length} agents
                        </span>
                      </div>
                      <div className="mt-2 text-xs font-mono text-muted-foreground">
                        Pattern: {template.executionPattern}
                      </div>
                    </div>
                    <Button
                      size="sm"
                      onClick={() => executeTemplate(template)}
                      disabled={isExecuting}
                    >
                      <Play className="h-4 w-4 mr-1" />
                      Run
                    </Button>
                  </div>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="builder" className="flex-1 overflow-auto">
            <div className="space-y-4">
              <div>
                <Label htmlFor="workflow-name">Workflow Name</Label>
                <Input
                  id="workflow-name"
                  value={customWorkflow.name}
                  onChange={(e) => setCustomWorkflow(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Enter workflow name"
                />
              </div>

              <div>
                <Label htmlFor="workflow-description">Description</Label>
                <Textarea
                  id="workflow-description"
                  value={customWorkflow.description}
                  onChange={(e) => setCustomWorkflow(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Describe the workflow"
                  rows={2}
                />
              </div>

              <div>
                <Label htmlFor="execution-mode">Execution Mode</Label>
                <Select
                  value={customWorkflow.executionMode}
                  onValueChange={(value) => setCustomWorkflow(prev => ({ ...prev, executionMode: value as any }))}
                >
                  <SelectTrigger id="execution-mode">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="sequential">Sequential</SelectItem>
                    <SelectItem value="parallel">Parallel</SelectItem>
                    <SelectItem value="mixed">Mixed</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Separator />

              <div>
                <div className="flex items-center justify-between mb-2">
                  <Label>Tasks</Label>
                  <Button size="sm" variant="outline" onClick={addTaskToWorkflow}>
                    <Plus className="h-4 w-4 mr-1" />
                    Add Task
                  </Button>
                </div>

                <div className="space-y-2">
                  {customWorkflow.tasks?.map((task, index) => (
                    <Card key={task.id} className="p-3">
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">Task {index + 1}</span>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => removeTaskFromWorkflow(task.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                        
                        <Select
                          value={task.agentId.toString()}
                          onValueChange={(value) => {
                            const agent = availableAgents.find(a => a.id.toString() === value);
                            setCustomWorkflow(prev => ({
                              ...prev,
                              tasks: prev.tasks?.map(t =>
                                t.id === task.id
                                  ? { ...t, agentId: parseInt(value), agentName: agent?.name || '' }
                                  : t
                              )
                            }));
                          }}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select agent" />
                          </SelectTrigger>
                          <SelectContent>
                            {availableAgents.map(agent => (
                              <SelectItem key={agent.id} value={agent.id.toString()}>
                                {agent.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>

                        <Input
                          value={task.task}
                          onChange={(e) => {
                            setCustomWorkflow(prev => ({
                              ...prev,
                              tasks: prev.tasks?.map(t =>
                                t.id === task.id
                                  ? { ...t, task: e.target.value }
                                  : t
                              )
                            }));
                          }}
                          placeholder="Task description"
                        />
                      </div>
                    </Card>
                  ))}
                </div>
              </div>

              <Button
                className="w-full"
                onClick={() => {
                  if (customWorkflow.name && customWorkflow.tasks && customWorkflow.tasks.length > 0) {
                    const workflow = new WorkflowBuilder()
                      .setName(customWorkflow.name)
                      .setDescription(customWorkflow.description || '')
                      .setExecutionMode(customWorkflow.executionMode as any)
                      .setContext({
                        sessionId,
                        projectPath,
                        messages: messages.slice(-10),
                        variables: {},
                        sharedMemory: {}
                      })
                      .build();
                    
                    workflow.tasks = customWorkflow.tasks;
                    executeWorkflow(workflow);
                  }
                }}
                disabled={!customWorkflow.name || !customWorkflow.tasks?.length || isExecuting}
              >
                <Play className="h-4 w-4 mr-1" />
                Execute Workflow
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="running" className="flex-1 overflow-auto">
            <ScrollArea className="h-full">
              <div className="space-y-4">
                {runningWorkflows.length === 0 ? (
                  <Alert>
                    <AlertDescription>No workflows running</AlertDescription>
                  </Alert>
                ) : (
                  runningWorkflows.map(workflow => {
                    const isExpanded = expandedWorkflows.has(workflow.id);
                    const result = workflowResults.get(workflow.id);
                    
                    return (
                      <Card key={workflow.id} className="p-4">
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => toggleWorkflowExpansion(workflow.id)}
                              >
                                {isExpanded ? (
                                  <ChevronDown className="h-4 w-4" />
                                ) : (
                                  <ChevronRight className="h-4 w-4" />
                                )}
                              </Button>
                              <span className="font-medium">{workflow.name}</span>
                              {getStatusIcon(workflow.status)}
                              <Badge variant="outline">
                                {getExecutionModeIcon(workflow.executionMode)}
                                <span className="ml-1">{workflow.executionMode}</span>
                              </Badge>
                            </div>
                            
                            {workflow.status === 'running' && (
                              <Button
                                size="sm"
                                variant="destructive"
                                onClick={() => cancelWorkflow(workflow.id)}
                              >
                                <Pause className="h-4 w-4 mr-1" />
                                Cancel
                              </Button>
                            )}
                          </div>

                          {result && (
                            <div className="text-sm">
                              <div className="flex items-center gap-4">
                                <span className="text-muted-foreground">
                                  Time: {result.executionTime}ms
                                </span>
                                <span className="text-muted-foreground">
                                  Tasks: {result.metrics.completedTasks}/{result.metrics.totalTasks}
                                </span>
                                {result.metrics.failedTasks > 0 && (
                                  <Badge variant="destructive">
                                    {result.metrics.failedTasks} failed
                                  </Badge>
                                )}
                              </div>
                            </div>
                          )}

                          {isExpanded && (
                            <div className="space-y-2 pl-6">
                              {workflow.tasks.map(task => (
                                <div key={task.id} className="flex items-center justify-between p-2 bg-muted/50 rounded">
                                  <div className="flex items-center gap-2">
                                    {getStatusIcon(task.status)}
                                    <span className="text-sm">{task.agentName}</span>
                                    <span className="text-xs text-muted-foreground">{task.task}</span>
                                  </div>
                                  
                                  {task.status === 'failed' && (
                                    <Button
                                      size="sm"
                                      variant="outline"
                                      onClick={() => retryTask(workflow.id, task.id)}
                                    >
                                      <RotateCcw className="h-3 w-3" />
                                    </Button>
                                  )}
                                </div>
                              ))}
                              
                              {result && result.errors.length > 0 && (
                                <Alert variant="destructive">
                                  <AlertDescription>
                                    {result.errors.map((err: any) => (
                                      <div key={err.taskId}>
                                        {err.taskId}: {err.error}
                                      </div>
                                    ))}
                                  </AlertDescription>
                                </Alert>
                              )}
                            </div>
                          )}
                        </div>
                      </Card>
                    );
                  })
                )}
              </div>
            </ScrollArea>
          </TabsContent>
        <TabsContent value="analytics" className="flex-1 overflow-auto">
            <ScrollArea className="h-full">
              <div className="space-y-4 p-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm flex items-center gap-2">
                        <Workflow className="h-4 w-4" />
                        Total Workflows
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">
                        {workflowAnalytics?.totalWorkflows || 0}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        Executed in this session
                      </p>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm flex items-center gap-2">
                        <CheckCircle className="h-4 w-4" />
                        Success Rate
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">
                        {workflowAnalytics?.successRate ? `${workflowAnalytics.successRate.toFixed(1)}%` : '0%'}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        Completed successfully
                      </p>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm flex items-center gap-2">
                        <BarChart3 className="h-4 w-4" />
                        Avg. Duration
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">
                        {workflowAnalytics?.avgDuration ? `${(workflowAnalytics.avgDuration / 1000).toFixed(1)}s` : '0s'}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        Average execution time
                      </p>
                    </CardContent>
                  </Card>
                </div>
                
                {workflowAnalytics?.recentWorkflows && workflowAnalytics.recentWorkflows.length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        Recent Workflows
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        {workflowAnalytics.recentWorkflows.map((workflow: any, index: number) => (
                          <div key={index} className="flex items-center justify-between p-2 bg-muted/50 rounded">
                            <div className="flex items-center gap-2">
                              <Badge variant="outline" className="text-xs">
                                {workflow.mode === 'parallel' ? (
                                  <Shuffle className="h-3 w-3 mr-1" />
                                ) : workflow.mode === 'sequential' ? (
                                  <GitBranch className="h-3 w-3 mr-1" />
                                ) : (
                                  <Zap className="h-3 w-3 mr-1" />
                                )}
                                {workflow.mode}
                              </Badge>
                              <span className="text-sm font-medium">{workflow.name}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Badge 
                                variant={workflow.status === 'completed' ? 'default' : workflow.status === 'failed' ? 'destructive' : 'secondary'}
                                className="text-xs"
                              >
                                {workflow.status}
                              </Badge>
                              <span className="text-xs text-muted-foreground">
                                {(workflow.duration / 1000).toFixed(1)}s
                              </span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}
                
                {workflowAnalytics?.agentUsage && Object.keys(workflowAnalytics.agentUsage).length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm flex items-center gap-2">
                        <Cpu className="h-4 w-4" />
                        Agent Usage
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        {Object.entries(workflowAnalytics.agentUsage).map(([agentName, usage]: [string, any]) => (
                          <div key={agentName} className="flex items-center justify-between">
                            <span className="text-sm">{agentName}</span>
                            <div className="flex items-center gap-2">
                              <div className="w-20 h-2 bg-muted rounded-full overflow-hidden">
                                <div 
                                  className="h-full bg-primary transition-all"
                                  style={{ width: `${Math.min(100, (usage.count / (workflowAnalytics.totalAgentCalls || 1)) * 100)}%` }}
                                />
                              </div>
                              <span className="text-xs text-muted-foreground w-8 text-right">
                                {usage.count}
                              </span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            </ScrollArea>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};