import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  ArrowLeft, 
  Terminal, 
  FolderOpen, 
  Copy, 
  GitBranch,
  Settings,
  Hash,
  Command,
  Pause,
  Play,
  RotateCcw,
  ChevronDown,
  Sparkles,
  Minimize2,
  Maximize2,
  Activity,
  Clock,
  Zap,
  Brain,
  MessageSquare,
  TrendingUp,
  MoreVertical,
  FileText,
  Download,
  Share2,
  Info,
  Eye,
  EyeOff,
  Cpu,
  Bot,
  Code
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuSeparator,
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';

interface EnhancedSessionHeaderProps {
  projectPath: string;
  claudeSessionId: string | null;
  totalTokens: number;
  tokenRate?: number;
  elapsedSeconds?: number;
  resumeFlag?: boolean;
  isLoading?: boolean;
  showSettings?: boolean;
  showSuperClaude?: boolean;
  showTimeline?: boolean;
  hasSession?: boolean;
  isInBackground?: boolean;
  messageCount?: number;
  activeAgents?: string[];
  mcpServers?: string[];
  onPause?: () => void;
  onResume?: () => void;
  onSendToBackground?: () => void;
  onBringToForeground?: () => void;
  canPause?: boolean;
  isStreaming: boolean;
  hasMessages: boolean;
  copyPopoverOpen: boolean;
  onBack: () => void;
  onSelectPath: () => void;
  onCopyAsJsonl: () => void;
  onCopyAsMarkdown: () => void;
  onToggleTimeline: () => void;
  onToggleSettings?: () => void;
  onToggleSuperClaude?: () => void;
  onProjectSettings?: () => void;
  onSlashCommandsSettings?: () => void;
  onUsageDashboard?: () => void;
  onRetryLastPrompt?: () => void;
  setCopyPopoverOpen: (open: boolean) => void;
}

export const EnhancedSessionHeader: React.FC<EnhancedSessionHeaderProps> = React.memo(({
  projectPath,
  claudeSessionId,
  totalTokens,
  tokenRate,
  elapsedSeconds = 0,
  resumeFlag,
  isLoading,
  showSettings,
  showSuperClaude,
  showTimeline,
  hasSession,
  isInBackground,
  isStreaming,
  hasMessages,
  messageCount = 0,
  activeAgents = [],
  mcpServers = [],
  copyPopoverOpen,
  onBack,
  onSelectPath,
  onCopyAsJsonl,
  onCopyAsMarkdown,
  onToggleTimeline,
  onToggleSettings,
  onToggleSuperClaude,
  onProjectSettings,
  onSlashCommandsSettings,
  onUsageDashboard,
  onRetryLastPrompt,
  setCopyPopoverOpen,
  onSendToBackground,
  onBringToForeground,
  canPause,
  onPause,
  onResume
}) => {
  const [showStats, setShowStats] = useState(false);
  const [cpuUsage, setCpuUsage] = useState(0);

  // Simulate CPU usage
  useEffect(() => {
    if (isStreaming) {
      const interval = setInterval(() => {
        setCpuUsage(Math.floor(Math.random() * 30 + 20));
      }, 1000);
      return () => clearInterval(interval);
    } else {
      setCpuUsage(0);
    }
  }, [isStreaming]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const formatTokens = (tokens: number) => {
    if (tokens > 1000000) return `${(tokens / 1000000).toFixed(1)}M`;
    if (tokens > 1000) return `${(tokens / 1000).toFixed(1)}k`;
    return tokens.toString();
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="border-b border-border/50 bg-background/95 backdrop-blur-xl"
    >
      {/* Main Header */}
      <div className="flex items-center justify-between px-4 py-3">
        <div className="flex items-center gap-3">
          {/* Back Button */}
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={onBack}
                  className="h-9 w-9 rounded-lg"
                >
                  <ArrowLeft className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Back to projects</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <Separator orientation="vertical" className="h-8" />

          {/* Session Info */}
          <div className="flex items-center gap-3">
            <div className={cn(
              "h-10 w-10 rounded-xl flex items-center justify-center",
              "bg-gradient-to-br from-primary/20 to-primary/10",
              isStreaming && "animate-pulse"
            )}>
              <Terminal className="h-5 w-5 text-primary" />
            </div>
            
            <div>
              <div className="flex items-center gap-2">
                <h1 className="text-lg font-semibold">
                  Claude Code Session
                </h1>
                {resumeFlag && (
                  <Badge variant="secondary" className="text-xs">
                    <RotateCcw className="h-3 w-3 mr-1" />
                    Resumed
                  </Badge>
                )}
                {isStreaming && (
                  <Badge variant="default" className="text-xs bg-green-500/10 text-green-600 border-green-500/30">
                    <Activity className="h-3 w-3 mr-1 animate-pulse" />
                    Active
                  </Badge>
                )}
                {isInBackground && (
                  <Badge variant="outline" className="text-xs">
                    <EyeOff className="h-3 w-3 mr-1" />
                    Background
                  </Badge>
                )}
              </div>
              
              <div className="flex items-center gap-3 mt-0.5">
                <button 
                  onClick={onSelectPath}
                  className="flex items-center gap-1.5 text-sm text-muted-foreground hover:text-foreground transition-colors"
                >
                  <FolderOpen className="h-3.5 w-3.5" />
                  <span className="truncate max-w-[300px]">
                    {projectPath || "No project selected"}
                  </span>
                </button>
                
                {claudeSessionId && (
                  <span className="text-xs text-muted-foreground">
                    ID: {claudeSessionId.slice(0, 8)}
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center gap-2">
          {/* Stats Toggle */}
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setShowStats(!showStats)}
                  className="h-9 w-9"
                >
                  <TrendingUp className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Toggle statistics</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          {/* Background/Foreground Toggle */}
          {hasSession && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={isInBackground ? onBringToForeground : onSendToBackground}
                    className="h-9 w-9"
                  >
                    {isInBackground ? (
                      <Eye className="h-4 w-4" />
                    ) : (
                      <EyeOff className="h-4 w-4" />
                    )}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  {isInBackground ? "Bring to foreground" : "Send to background"}
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}

          {/* Pause/Resume */}
          {canPause && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={isStreaming ? onPause : onResume}
                    disabled={!hasSession}
                    className="h-9 w-9"
                  >
                    {isStreaming ? (
                      <Pause className="h-4 w-4" />
                    ) : (
                      <Play className="h-4 w-4" />
                    )}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  {isStreaming ? "Pause session" : "Resume session"}
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}

          <Separator orientation="vertical" className="h-6" />

          {/* Quick Actions */}
          <div className="flex items-center gap-1">
            {/* Timeline */}
            {onToggleTimeline && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant={showTimeline ? "secondary" : "ghost"}
                      size="icon"
                      onClick={onToggleTimeline}
                      className="h-9 w-9"
                    >
                      <GitBranch className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Timeline</TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}

            {/* SuperClaude */}
            {onToggleSuperClaude && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant={showSuperClaude ? "secondary" : "ghost"}
                      size="icon"
                      onClick={onToggleSuperClaude}
                      className="h-9 w-9"
                    >
                      <Brain className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>SuperClaude Panel</TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}

            {/* Settings */}
            {onToggleSettings && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant={showSettings ? "secondary" : "ghost"}
                      size="icon"
                      onClick={onToggleSettings}
                      className="h-9 w-9"
                    >
                      <Settings className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Settings</TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </div>

          <Separator orientation="vertical" className="h-6" />

          {/* Copy Menu */}
          <Popover open={copyPopoverOpen} onOpenChange={setCopyPopoverOpen}>
            <PopoverTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                disabled={!hasMessages}
                className="gap-2"
              >
                <Copy className="h-4 w-4" />
                Copy
                <ChevronDown className="h-3 w-3" />
              </Button>
            </PopoverTrigger>
            <PopoverContent align="end" className="w-48">
              <div className="space-y-1">
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full justify-start"
                  onClick={() => {
                    onCopyAsMarkdown();
                    setCopyPopoverOpen(false);
                  }}
                >
                  <FileText className="h-4 w-4 mr-2" />
                  As Markdown
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full justify-start"
                  onClick={() => {
                    onCopyAsJsonl();
                    setCopyPopoverOpen(false);
                  }}
                >
                  <Code className="h-4 w-4 mr-2" />
                  As JSONL
                </Button>
              </div>
            </PopoverContent>
          </Popover>

          {/* More Menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-9 w-9">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              {onProjectSettings && (
                <DropdownMenuItem onClick={onProjectSettings}>
                  <Settings className="h-4 w-4 mr-2" />
                  Project Hooks
                </DropdownMenuItem>
              )}
              {onSlashCommandsSettings && (
                <DropdownMenuItem onClick={onSlashCommandsSettings}>
                  <Command className="h-4 w-4 mr-2" />
                  Slash Commands
                </DropdownMenuItem>
              )}
              {onUsageDashboard && (
                <DropdownMenuItem onClick={onUsageDashboard}>
                  <TrendingUp className="h-4 w-4 mr-2" />
                  Usage Dashboard
                </DropdownMenuItem>
              )}
              <DropdownMenuSeparator />
              {onRetryLastPrompt && (
                <DropdownMenuItem onClick={onRetryLastPrompt} disabled={!hasMessages}>
                  <RotateCcw className="h-4 w-4 mr-2" />
                  Retry Last Prompt
                </DropdownMenuItem>
              )}
              <DropdownMenuItem>
                <Download className="h-4 w-4 mr-2" />
                Export Session
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Share2 className="h-4 w-4 mr-2" />
                Share Session
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Stats Bar */}
      <AnimatePresence>
        {showStats && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="border-t border-border/30 bg-muted/30"
          >
            <div className="px-4 py-2">
              <div className="flex items-center justify-between gap-6">
                {/* Session Stats */}
                <div className="flex items-center gap-6">
                  <div className="flex items-center gap-2">
                    <Clock className="h-3.5 w-3.5 text-muted-foreground" />
                    <span className="text-xs text-muted-foreground">Duration:</span>
                    <span className="text-xs font-medium">{formatTime(elapsedSeconds)}</span>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <MessageSquare className="h-3.5 w-3.5 text-muted-foreground" />
                    <span className="text-xs text-muted-foreground">Messages:</span>
                    <span className="text-xs font-medium">{messageCount}</span>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Hash className="h-3.5 w-3.5 text-muted-foreground" />
                    <span className="text-xs text-muted-foreground">Tokens:</span>
                    <span className="text-xs font-medium">{formatTokens(totalTokens)}</span>
                    {tokenRate && tokenRate > 0 && (
                      <Badge variant="secondary" className="text-xs h-5">
                        {tokenRate.toFixed(0)} tok/s
                      </Badge>
                    )}
                  </div>
                  
                  {isStreaming && (
                    <div className="flex items-center gap-2">
                      <Cpu className="h-3.5 w-3.5 text-muted-foreground" />
                      <span className="text-xs text-muted-foreground">CPU:</span>
                      <div className="w-20">
                        <Progress value={cpuUsage} className="h-1.5" />
                      </div>
                      <span className="text-xs font-medium">{cpuUsage}%</span>
                    </div>
                  )}
                </div>

                {/* Active Features */}
                <div className="flex items-center gap-2">
                  {activeAgents.length > 0 && (
                    <Badge variant="outline" className="text-xs bg-blue-500/10 border-blue-500/30">
                      <Bot className="h-3 w-3 mr-1" />
                      {activeAgents.length} Agent{activeAgents.length > 1 ? 's' : ''}
                    </Badge>
                  )}
                  
                  {mcpServers.length > 0 && (
                    <Badge variant="outline" className="text-xs bg-purple-500/10 border-purple-500/30">
                      <Zap className="h-3 w-3 mr-1" />
                      {mcpServers.length} MCP
                    </Badge>
                  )}
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
});

EnhancedSessionHeader.displayName = 'EnhancedSessionHeader';