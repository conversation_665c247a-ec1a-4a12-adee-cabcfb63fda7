import { useState, useCallback, useEffect, useRef } from 'react';
import { api } from '@/lib/api';
import type { Session } from '@/lib/api';

export interface AgentCapability {
  type: string;
  description: string;
  config?: any;
}

export interface AgentConfiguration {
  maxExecutionTimeMs: number;
  maxMemoryMb: number;
  maxConcurrentTasks: number;
  temperature: number;
  modelPreferences: string[];
  environmentVariables: Record<string, string>;
}

export interface Agent {
  id: string;
  name: string;
  description: string;
  version: string;
  author: string;
  category: string;
  status: 'available' | 'running' | 'paused' | 'stopped' | 'error';
  capabilities: AgentCapability[];
  configuration: AgentConfiguration;
  metrics?: {
    executionTime: number;
    memoryUsage: number;
    successRate: number;
    totalRuns: number;
  };
  createdAt: string;
  updatedAt: string;
}

export interface AgentExecution {
  id: string;
  agentId: string;
  sessionId: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  input: any;
  output?: any;
  error?: string;
  startedAt: string;
  completedAt?: string;
  executionTimeMs?: number;
}

export interface AgentWorkflow {
  id: string;
  name: string;
  description: string;
  sessionId?: string;
  projectId?: string;
  tasks: AgentTask[];
  executionMode: 'sequential' | 'parallel' | 'mixed';
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  context?: WorkflowContext;
  startedAt?: Date;
  completedAt?: Date;
}

export interface AgentTask {
  id: string;
  agentId: number;
  agentName: string;
  task: string;
  dependencies: string[];
  status: 'pending' | 'running' | 'completed' | 'failed';
  result?: any;
  error?: string;
  startTime?: Date;
  endTime?: Date;
  retryCount: number;
  maxRetries: number;
}

export interface WorkflowContext {
  sessionId: string;
  projectPath: string;
  messages: any[];
  variables: Record<string, any>;
  sharedMemory: Record<string, any>;
}

export interface AgentOrchestrationState {
  agents: Agent[];
  activeAgents: string[];
  executions: AgentExecution[];
  workflows: AgentWorkflow[];
  isLoading: boolean;
  error: string | null;
}

export const useAgentOrchestration = (sessionId: string) => {
  const [state, setState] = useState<AgentOrchestrationState>({
    agents: [],
    activeAgents: [],
    executions: [],
    workflows: [],
    isLoading: false,
    error: null,
  });

  const executionQueue = useRef<AgentExecution[]>([]);
  const processingRef = useRef(false);

  // Load available agents
  const loadAgents = useCallback(async () => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));
    try {
      // This would connect to your backend API
      const response = await api.listAgents?.() || [];
      setState(prev => ({
        ...prev,
        agents: response as Agent[],
        isLoading: false,
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to load agents',
        isLoading: false,
      }));
    }
  }, []);

  // Activate an agent for the current session
  const activateAgent = useCallback(async (agentId: string) => {
    const agent = state.agents.find(a => a.id === agentId);
    if (!agent) {
      setState(prev => ({
        ...prev,
        error: `Agent ${agentId} not found`,
      }));
      return;
    }

    setState(prev => ({
      ...prev,
      activeAgents: [...prev.activeAgents, agentId],
      agents: prev.agents.map(a =>
        a.id === agentId ? { ...a, status: 'running' as const } : a
      ),
    }));

    // Notify backend about agent activation
    try {
      await api.activateAgent?.(sessionId, agentId);
    } catch (error) {
    }
  }, [sessionId, state.agents]);

  // Deactivate an agent
  const deactivateAgent = useCallback(async (agentId: string) => {
    setState(prev => ({
      ...prev,
      activeAgents: prev.activeAgents.filter(id => id !== agentId),
      agents: prev.agents.map(a =>
        a.id === agentId ? { ...a, status: 'stopped' as const } : a
      ),
    }));

    try {
      await api.deactivateAgent?.(sessionId, agentId);
    } catch (error) {
    }
  }, [sessionId]);

  // Execute a task with an agent
  const executeAgent = useCallback(async (
    agentId: string,
    input: any,
    priority: 'low' | 'normal' | 'high' = 'normal'
  ): Promise<AgentExecution> => {
    const execution: AgentExecution = {
      id: `exec-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      agentId,
      sessionId,
      status: 'pending',
      input,
      startedAt: new Date().toISOString(),
    };

    // Add to queue based on priority
    if (priority === 'high') {
      executionQueue.current.unshift(execution);
    } else if (priority === 'low') {
      executionQueue.current.push(execution);
    } else {
      // Normal priority - add after high priority items
      const highPriorityCount = executionQueue.current.filter(
        e => e.status === 'pending'
      ).length;
      executionQueue.current.splice(highPriorityCount, 0, execution);
    }

    setState(prev => ({
      ...prev,
      executions: [...prev.executions, execution],
    }));

    // Process queue
    processExecutionQueue();

    return execution;
  }, [sessionId]);

  // Process the execution queue
  const processExecutionQueue = useCallback(async () => {
    if (processingRef.current || executionQueue.current.length === 0) {
      return;
    }

    processingRef.current = true;

    while (executionQueue.current.length > 0) {
      const execution = executionQueue.current.shift();
      if (!execution) break;

      // Update execution status to running
      setState(prev => ({
        ...prev,
        executions: prev.executions.map(e =>
          e.id === execution.id ? { ...e, status: 'running' as const } : e
        ),
      }));

      try {
        // Execute the agent task
        const result = await api.executeAgent?.(
          sessionId,
          execution.agentId,
          execution.input
        );

        // Update execution with result
        setState(prev => ({
          ...prev,
          executions: prev.executions.map(e =>
            e.id === execution.id
              ? {
                  ...e,
                  status: 'completed' as const,
                  output: result,
                  completedAt: new Date().toISOString(),
                  executionTimeMs: Date.now() - new Date(e.startedAt).getTime(),
                }
              : e
          ),
        }));
      } catch (error) {
        // Update execution with error
        setState(prev => ({
          ...prev,
          executions: prev.executions.map(e =>
            e.id === execution.id
              ? {
                  ...e,
                  status: 'failed' as const,
                  error: error instanceof Error ? error.message : 'Execution failed',
                  completedAt: new Date().toISOString(),
                  executionTimeMs: Date.now() - new Date(e.startedAt).getTime(),
                }
              : e
          ),
        }));
      }
    }

    processingRef.current = false;
  }, [sessionId]);

  // Get agent metrics
  const getAgentMetrics = useCallback((agentId: string) => {
    const agentExecutions = state.executions.filter(e => e.agentId === agentId);
    const completed = agentExecutions.filter(e => e.status === 'completed');
    const failed = agentExecutions.filter(e => e.status === 'failed');
    
    const totalExecutionTime = completed.reduce(
      (sum, e) => sum + (e.executionTimeMs || 0),
      0
    );

    return {
      totalRuns: agentExecutions.length,
      successRate: agentExecutions.length > 0
        ? (completed.length / agentExecutions.length) * 100
        : 0,
      averageExecutionTime: completed.length > 0
        ? totalExecutionTime / completed.length
        : 0,
      failedRuns: failed.length,
    };
  }, [state.executions]);

  // Chain multiple agents together
  const chainAgents = useCallback(async (
    agentChain: Array<{ agentId: string; transform?: (data: any) => any }>
  ) => {
    let result: any = null;

    for (const { agentId, transform } of agentChain) {
      const input = transform ? transform(result) : result;
      const execution = await executeAgent(agentId, input);
      
      // Wait for execution to complete
      await new Promise<void>((resolve) => {
        const checkInterval = setInterval(() => {
          const exec = state.executions.find(e => e.id === execution.id);
          if (exec && (exec.status === 'completed' || exec.status === 'failed')) {
            clearInterval(checkInterval);
            result = exec.output;
            resolve();
          }
        }, 100);
      });
    }

    return result;
  }, [executeAgent, state.executions]);

  // Load agents on mount
  useEffect(() => {
    loadAgents();
  }, [loadAgents]);

  return {
    agents: state.agents,
    activeAgents: state.activeAgents,
    executions: state.executions,
    isLoading: state.isLoading,
    error: state.error,
    activateAgent,
    deactivateAgent,
    executeAgent,
    getAgentMetrics,
    chainAgents,
    reloadAgents: loadAgents,
  };
};