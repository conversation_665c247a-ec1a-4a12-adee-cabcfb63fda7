import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  X, 
  GitBranch, 
  Sparkles, 
  Bot, 
  Server,
  Workflow,
  Settings,
  Activity,
  Layers,
  Network,
  Zap
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface PanelProps {
  title: string;
  icon: React.ReactNode;
  onClose: () => void;
  children: React.ReactNode;
  className?: string;
}

const Panel: React.FC<PanelProps> = ({ title, icon, onClose, children, className }) => (
  <motion.div
    initial={{ x: "100%" }}
    animate={{ x: 0 }}
    exit={{ x: "100%" }}
    transition={{ type: "spring", damping: 20, stiffness: 300 }}
    className={cn(
      "fixed right-0 top-0 h-full w-full sm:w-96",
      "bg-background border-l border-border shadow-xl z-30 overflow-hidden",
      className
    )}
  >
    <div className="h-full flex flex-col">
      {/* Panel Header */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        <div className="flex items-center gap-2">
          {icon}
          <h3 className="text-lg font-semibold">{title}</h3>
        </div>
        <Button
          variant="ghost"
          size="icon"
          onClick={onClose}
          className="h-8 w-8"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>
      
      {/* Panel Content */}
      <div className="flex-1 overflow-y-auto">
        {children}
      </div>
    </div>
  </motion.div>
);

interface TimelinePanelProps {
  sessionId: string;
  projectId: string;
  projectPath: string;
  onClose: () => void;
  children: React.ReactNode;
}

export const TimelinePanel: React.FC<TimelinePanelProps> = ({
  sessionId,
  projectId,
  projectPath,
  onClose,
  children
}) => (
  <Panel
    title="Session Timeline"
    icon={<GitBranch className="h-5 w-5 text-primary" />}
    onClose={onClose}
  >
    <div className="p-4">
      {children}
    </div>
  </Panel>
);

interface SuperClaudePanelProps {
  sessionId: string;
  projectId: string;
  onClose: () => void;
  children: React.ReactNode;
}

export const SuperClaudePanel: React.FC<SuperClaudePanelProps> = ({
  sessionId,
  projectId,
  onClose,
  children
}) => (
  <Panel
    title="SuperClaude Assistant"
    icon={<Sparkles className="h-5 w-5 text-primary" />}
    onClose={onClose}
  >
    <div className="p-4">
      {children}
    </div>
  </Panel>
);

interface AgentWorkflowPanelProps {
  sessionId: string;
  projectPath: string;
  onClose: () => void;
  children: React.ReactNode;
}

export const AgentWorkflowPanel: React.FC<AgentWorkflowPanelProps> = ({
  sessionId,
  projectPath,
  onClose,
  children
}) => (
  <Panel
    title="Agent Workflow"
    icon={<Workflow className="h-5 w-5 text-primary" />}
    onClose={onClose}
    className="sm:w-[600px]"
  >
    <div className="p-4">
      {children}
    </div>
  </Panel>
);

interface AgentMCPPanelProps {
  agents: any[];
  activeAgents: string[];
  servers: any[];
  activeServers: string[];
  sessionId: string;
  projectPath: string;
  isCollapsed: boolean;
  onToggleCollapse: () => void;
  onClose?: () => void;
  className?: string;
}

export const AgentMCPPanel: React.FC<AgentMCPPanelProps> = ({
  agents,
  activeAgents,
  servers,
  activeServers,
  sessionId,
  projectPath,
  isCollapsed,
  onToggleCollapse,
  onClose,
  className
}) => {
  if (isCollapsed) {
    return (
      <motion.div
        initial={{ width: 0 }}
        animate={{ width: "auto" }}
        exit={{ width: 0 }}
        className={cn("border-l border-border bg-card/50", className)}
      >
        <div className="p-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggleCollapse}
            className="w-full justify-center"
          >
            <div className="flex flex-col items-center gap-1">
              <div className="relative">
                <Bot className="h-4 w-4" />
                <Server className="h-2 w-2 absolute -bottom-0.5 -right-0.5" />
              </div>
              <span className="text-xs">Agents</span>
            </div>
          </Button>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ width: 0 }}
      animate={{ width: 320 }}
      exit={{ width: 0 }}
      className={cn("border-l border-border bg-card/50 overflow-hidden", className)}
    >
      <div className="h-full flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-border">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="relative">
                <Bot className="h-5 w-5 text-primary" />
                <Server className="h-3 w-3 absolute -bottom-1 -right-1 text-info" />
              </div>
              <h3 className="font-semibold">Agents & MCP</h3>
            </div>
            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="icon"
                onClick={onToggleCollapse}
                className="h-8 w-8"
              >
                <Layers className="h-4 w-4" />
              </Button>
              {onClose && (
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={onClose}
                  className="h-8 w-8"
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {/* Active Agents */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium">Active Agents</h4>
              <Badge variant="outline" className="text-xs">
                {activeAgents.length}
              </Badge>
            </div>
            
            {activeAgents.length > 0 ? (
              <div className="space-y-2">
                {activeAgents.map((agentId, index) => (
                  <motion.div
                    key={agentId}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.05 }}
                    className="p-2 rounded-lg bg-purple-500/10 border border-purple-500/20"
                  >
                    <div className="flex items-center gap-2">
                      <Bot className="h-4 w-4 text-purple-500" />
                      <span className="text-sm font-medium">{agentId}</span>
                      <div className="ml-auto">
                        <Activity className="h-3 w-3 text-purple-500 animate-pulse" />
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            ) : (
              <p className="text-xs text-muted-foreground">No active agents</p>
            )}
          </div>

          <Separator />

          {/* MCP Servers */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium">MCP Servers</h4>
              <Badge variant="outline" className="text-xs">
                {activeServers.length}
              </Badge>
            </div>
            
            {activeServers.length > 0 ? (
              <div className="space-y-2">
                {activeServers.map((serverId, index) => (
                  <motion.div
                    key={serverId}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.05 }}
                    className="p-2 rounded-lg bg-blue-500/10 border border-blue-500/20"
                  >
                    <div className="flex items-center gap-2">
                      <Server className="h-4 w-4 text-blue-500" />
                      <span className="text-sm font-medium">{serverId}</span>
                      <div className="ml-auto">
                        <Network className="h-3 w-3 text-blue-500" />
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            ) : (
              <p className="text-xs text-muted-foreground">No connected servers</p>
            )}
          </div>

          <Separator />

          {/* Quick Actions */}
          <div className="space-y-2">
            <h4 className="text-sm font-medium">Quick Actions</h4>
            <div className="grid grid-cols-2 gap-2">
              <Button variant="outline" size="sm" className="text-xs">
                <Zap className="h-3 w-3 mr-1" />
                Refresh
              </Button>
              <Button variant="outline" size="sm" className="text-xs">
                <Settings className="h-3 w-3 mr-1" />
                Configure
              </Button>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};