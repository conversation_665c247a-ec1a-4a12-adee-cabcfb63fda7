import React, { useRef, useEffect, useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { StreamMessage } from '../../display/StreamMessage';
import { 
  Terminal, 
  ChevronDown, 
  Search, 
  Copy, 
  Check, 
  MessageSquare,
  Bot,
  User,
  Clock,
  Sparkles,
  AlertCircle,
  Code2,
  FileText,
  Zap,
  Brain,
  ArrowDown,
  Filter,
  Download,
  Share2,
  Bookmark,
  MoreVertical
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import type { ClaudeStreamMessage } from '../../agents/AgentExecution';

interface EnhancedMessageListProps {
  messages: ClaudeStreamMessage[];
  projectPath: string;
  isStreaming: boolean;
  onLinkDetected?: (url: string) => void;
  className?: string;
  sessionId?: string;
}

export const EnhancedMessageList: React.FC<EnhancedMessageListProps> = React.memo(({
  messages,
  projectPath,
  isStreaming,
  onLinkDetected,
  className,
  sessionId
}) => {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const shouldAutoScrollRef = useRef(true);
  const userHasScrolledRef = useRef(false);
  const [showScrollButton, setShowScrollButton] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [showSearch, setShowSearch] = useState(false);
  const [copiedMessageId, setCopiedMessageId] = useState<string | null>(null);
  const [bookmarkedMessages, setBookmarkedMessages] = useState<Set<string>>(new Set());
  const [messageFilter, setMessageFilter] = useState<'all' | 'user' | 'assistant' | 'system'>('all');

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (shouldAutoScrollRef.current && scrollContainerRef.current) {
      const scrollElement = scrollContainerRef.current;
      scrollElement.scrollTop = scrollElement.scrollHeight;
    }
  }, [messages]);

  // Handle scroll events
  const handleScroll = useCallback(() => {
    if (!scrollContainerRef.current) return;
    
    const scrollElement = scrollContainerRef.current;
    const isAtBottom = 
      Math.abs(scrollElement.scrollHeight - scrollElement.scrollTop - scrollElement.clientHeight) < 50;
    
    setShowScrollButton(!isAtBottom);
    
    if (!isAtBottom) {
      userHasScrolledRef.current = true;
      shouldAutoScrollRef.current = false;
    } else if (userHasScrolledRef.current) {
      shouldAutoScrollRef.current = true;
      userHasScrolledRef.current = false;
    }
  }, []);

  // Scroll to bottom function
  const scrollToBottom = useCallback(() => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollTo({
        top: scrollContainerRef.current.scrollHeight,
        behavior: 'smooth'
      });
    }
  }, []);

  // Copy message content
  const copyMessage = useCallback((message: ClaudeStreamMessage, messageId: string) => {
    const content = typeof message.message === 'string' 
      ? message.message 
      : JSON.stringify(message.message, null, 2);
    
    navigator.clipboard.writeText(content);
    setCopiedMessageId(messageId);
    setTimeout(() => setCopiedMessageId(null), 2000);
  }, []);

  // Toggle bookmark
  const toggleBookmark = useCallback((messageId: string) => {
    setBookmarkedMessages(prev => {
      const newSet = new Set(prev);
      if (newSet.has(messageId)) {
        newSet.delete(messageId);
      } else {
        newSet.add(messageId);
      }
      return newSet;
    });
  }, []);

  // Filter messages
  const filteredMessages = messages.filter(msg => {
    // Apply type filter
    if (messageFilter !== 'all') {
      if (messageFilter === 'user' && msg.type !== 'user') return false;
      if (messageFilter === 'assistant' && msg.type !== 'assistant') return false;
      if (messageFilter === 'system' && msg.type !== 'system') return false;
    }
    
    // Apply search filter
    if (searchQuery) {
      const content = typeof msg.message === 'string' 
        ? msg.message.toLowerCase()
        : JSON.stringify(msg.message).toLowerCase();
      if (!content.includes(searchQuery.toLowerCase())) return false;
    }
    
    return true;
  });

  // Get message icon
  const getMessageIcon = (type: string) => {
    switch (type) {
      case 'user':
        return <User className="h-4 w-4" />;
      case 'assistant':
        return <Bot className="h-4 w-4" />;
      case 'system':
        return <Terminal className="h-4 w-4" />;
      default:
        return <MessageSquare className="h-4 w-4" />;
    }
  };

  // Get message type color
  const getMessageTypeColor = (type: string) => {
    switch (type) {
      case 'user':
        return 'from-blue-500/20 to-blue-600/20 border-blue-500/30';
      case 'assistant':
        return 'from-purple-500/20 to-purple-600/20 border-purple-500/30';
      case 'system':
        return 'from-gray-500/20 to-gray-600/20 border-gray-500/30';
      default:
        return 'from-gray-500/20 to-gray-600/20 border-gray-500/30';
    }
  };

  // Format timestamp
  const formatTimestamp = (timestamp?: number) => {
    if (!timestamp) return '';
    const date = new Date(timestamp);
    return date.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit',
      second: '2-digit'
    });
  };

  if (messages.length === 0) {
    return (
      <div className={cn("flex-1 flex items-center justify-center", className)}>
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center space-y-6 max-w-md"
        >
          <div className="relative">
            <div className="h-20 w-20 bg-gradient-to-br from-primary/20 to-purple-500/20 rounded-full flex items-center justify-center mx-auto">
              <Brain className="h-10 w-10 text-primary" />
            </div>
            <motion.div
              className="absolute inset-0"
              animate={{ rotate: 360 }}
              transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
            >
              <Sparkles className="h-5 w-5 text-primary absolute top-0 right-2" />
            </motion.div>
          </div>
          <div>
            <h3 className="text-xl font-bold mb-2 bg-gradient-to-r from-primary to-purple-500 bg-clip-text text-transparent">
              Ready to start coding
            </h3>
            <p className="text-sm text-muted-foreground">
              {projectPath 
                ? "Enter a prompt below to begin your Claude Code session"
                : "Select a project folder to begin"}
            </p>
          </div>
          <div className="flex items-center justify-center gap-2 text-xs text-muted-foreground">
            <Zap className="h-3 w-3" />
            <span>Powered by Claude AI</span>
          </div>
        </motion.div>
      </div>
    );
  }

  return (
    <div className={cn("flex-1 flex flex-col relative", className)}>
      {/* Header Toolbar */}
      <div className="sticky top-0 z-20 bg-background/95 backdrop-blur-xl border-b border-border/50">
        <div className="px-4 py-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="gap-1">
                <MessageSquare className="h-3 w-3" />
                {filteredMessages.length} messages
              </Badge>
              
              {/* Filter buttons */}
              <div className="flex items-center gap-1">
                <Button
                  size="sm"
                  variant={messageFilter === 'all' ? 'default' : 'ghost'}
                  onClick={() => setMessageFilter('all')}
                  className="h-7 px-2 text-xs"
                >
                  All
                </Button>
                <Button
                  size="sm"
                  variant={messageFilter === 'user' ? 'default' : 'ghost'}
                  onClick={() => setMessageFilter('user')}
                  className="h-7 px-2 text-xs"
                >
                  User
                </Button>
                <Button
                  size="sm"
                  variant={messageFilter === 'assistant' ? 'default' : 'ghost'}
                  onClick={() => setMessageFilter('assistant')}
                  className="h-7 px-2 text-xs"
                >
                  Assistant
                </Button>
                <Button
                  size="sm"
                  variant={messageFilter === 'system' ? 'default' : 'ghost'}
                  onClick={() => setMessageFilter('system')}
                  className="h-7 px-2 text-xs"
                >
                  System
                </Button>
              </div>
            </div>

            <div className="flex items-center gap-2">
              {/* Search toggle */}
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => setShowSearch(!showSearch)}
                      className="h-8 w-8 p-0"
                    >
                      <Search className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Search messages</TooltipContent>
                </Tooltip>
              </TooltipProvider>

              {/* Export button */}
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      size="sm"
                      variant="ghost"
                      className="h-8 w-8 p-0"
                      onClick={() => {
                        const content = JSON.stringify(messages, null, 2);
                        const blob = new Blob([content], { type: 'application/json' });
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `claude-session-${sessionId || 'export'}.json`;
                        a.click();
                      }}
                    >
                      <Download className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Export conversation</TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>

          {/* Search bar */}
          <AnimatePresence>
            {showSearch && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: 'auto', opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                className="pt-2"
              >
                <Input
                  type="search"
                  placeholder="Search messages..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="h-8 text-sm"
                />
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>

      {/* Messages Container */}
      <div
        ref={scrollContainerRef}
        onScroll={handleScroll}
        className="flex-1 overflow-y-auto scroll-smooth scrollbar-thin scrollbar-thumb-primary/20 scrollbar-track-transparent"
      >
        <div className="w-full max-w-5xl mx-auto px-4 py-8 space-y-4 pb-32">
          <AnimatePresence mode="popLayout">
            {filteredMessages.map((message, index) => {
              const messageId = `msg-${index}-${message.type}-${message.timestamp || index}`;
              const isBookmarked = bookmarkedMessages.has(messageId);
              const isCopied = copiedMessageId === messageId;
              
              return (
                <motion.div
                  key={messageId}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, scale: 0.95 }}
                  transition={{ duration: 0.2 }}
                  className="w-full group"
                >
                  <div className={cn(
                    "relative rounded-xl border bg-gradient-to-r p-[1px]",
                    getMessageTypeColor(message.type)
                  )}>
                    <div className="rounded-xl bg-background p-4">
                      {/* Message Header */}
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center gap-2">
                          <div className={cn(
                            "h-8 w-8 rounded-lg flex items-center justify-center",
                            "bg-gradient-to-br",
                            message.type === 'user' ? 'from-blue-500/20 to-blue-600/20' :
                            message.type === 'assistant' ? 'from-purple-500/20 to-purple-600/20' :
                            'from-gray-500/20 to-gray-600/20'
                          )}>
                            {getMessageIcon(message.type)}
                          </div>
                          <div>
                            <span className="font-medium text-sm capitalize">
                              {message.type}
                            </span>
                            {message.timestamp && (
                              <span className="text-xs text-muted-foreground ml-2">
                                <Clock className="h-3 w-3 inline mr-1" />
                                {formatTimestamp(message.timestamp)}
                              </span>
                            )}
                          </div>
                        </div>

                        {/* Message Actions */}
                        <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  className="h-7 w-7 p-0"
                                  onClick={() => copyMessage(message, messageId)}
                                >
                                  {isCopied ? (
                                    <Check className="h-3.5 w-3.5 text-green-500" />
                                  ) : (
                                    <Copy className="h-3.5 w-3.5" />
                                  )}
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                {isCopied ? 'Copied!' : 'Copy message'}
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>

                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  className="h-7 w-7 p-0"
                                  onClick={() => toggleBookmark(messageId)}
                                >
                                  <Bookmark 
                                    className={cn(
                                      "h-3.5 w-3.5",
                                      isBookmarked && "fill-current text-yellow-500"
                                    )} 
                                  />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                {isBookmarked ? 'Remove bookmark' : 'Bookmark message'}
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>

                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button
                                size="sm"
                                variant="ghost"
                                className="h-7 w-7 p-0"
                              >
                                <MoreVertical className="h-3.5 w-3.5" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => copyMessage(message, messageId)}>
                                <Copy className="h-4 w-4 mr-2" />
                                Copy
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => toggleBookmark(messageId)}>
                                <Bookmark className="h-4 w-4 mr-2" />
                                {isBookmarked ? 'Unbookmark' : 'Bookmark'}
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem>
                                <Share2 className="h-4 w-4 mr-2" />
                                Share
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </div>

                      {/* Message Content */}
                      <div className="relative">
                        <StreamMessage 
                          message={message}
                          streamMessages={messages}
                          onLinkDetected={onLinkDetected}
                        />
                      </div>

                      {/* Tool usage indicators */}
                      {message.toolCalls && message.toolCalls.length > 0 && (
                        <div className="mt-3 pt-3 border-t border-border/50">
                          <div className="flex items-center gap-2 flex-wrap">
                            <span className="text-xs text-muted-foreground">Tools used:</span>
                            {message.toolCalls.map((tool, idx) => (
                              <Badge key={idx} variant="secondary" className="text-xs">
                                <Code2 className="h-3 w-3 mr-1" />
                                {tool.name}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </AnimatePresence>

          {/* Streaming indicator */}
          {isStreaming && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="flex items-center justify-center py-8"
            >
              <div className="relative">
                <div className="flex items-center gap-3 px-4 py-3 bg-primary/10 rounded-xl border border-primary/30">
                  <div className="relative">
                    <Brain className="h-5 w-5 text-primary" />
                    <motion.div
                      className="absolute inset-0"
                      animate={{ scale: [1, 1.2, 1] }}
                      transition={{ duration: 1.5, repeat: Infinity }}
                    >
                      <Brain className="h-5 w-5 text-primary opacity-40" />
                    </motion.div>
                  </div>
                  <span className="text-sm font-medium">Claude is thinking...</span>
                  <div className="flex gap-1">
                    <motion.div
                      className="w-2 h-2 bg-primary rounded-full"
                      animate={{ opacity: [0.3, 1, 0.3] }}
                      transition={{ duration: 1.5, repeat: Infinity, delay: 0 }}
                    />
                    <motion.div
                      className="w-2 h-2 bg-primary rounded-full"
                      animate={{ opacity: [0.3, 1, 0.3] }}
                      transition={{ duration: 1.5, repeat: Infinity, delay: 0.3 }}
                    />
                    <motion.div
                      className="w-2 h-2 bg-primary rounded-full"
                      animate={{ opacity: [0.3, 1, 0.3] }}
                      transition={{ duration: 1.5, repeat: Infinity, delay: 0.6 }}
                    />
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </div>
      </div>

      {/* Scroll to bottom button */}
      <AnimatePresence>
        {showScrollButton && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className="absolute bottom-4 right-4 z-30"
          >
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    size="sm"
                    onClick={scrollToBottom}
                    className="h-10 w-10 rounded-full shadow-lg"
                  >
                    <ArrowDown className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Scroll to bottom</TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
});

EnhancedMessageList.displayName = 'EnhancedMessageList';