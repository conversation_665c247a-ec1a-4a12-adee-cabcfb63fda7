import React, { useState, useRef, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Send,
  Paperclip,
  Image,
  Code,
  Sparkles,
  Zap,
  Brain,
  ChevronDown,
  X,
  AlertCircle,
  FileText,
  Hash,
  Mic,
  Settings,
  Loader2,
  Arrow<PERSON><PERSON>
} from "lucide-react";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Separator } from "@/components/ui/separator";
import { FilePicker } from "./FilePicker";
import { SlashCommandPicker } from "./SlashCommandPicker";
import { ImagePreview } from "../display/ImagePreview";
import { api, type FileEntry, type SlashCommand, type ModelUsageInfo } from "@/lib/api";
import { useSuperClaude } from "@/components/sessions/superclaude/hooks/useSuperClaude";
import { CommandSuggestions } from "@/components/sessions/superclaude/ui/CommandSuggestions";
import { PersonaBadgeCompact } from "@/components/sessions/superclaude/ui/PersonaBadge";
import { getCurrentWebviewWindow } from "@tauri-apps/api/webviewWindow";

interface FloatingPromptInputProps {
  onSend: (prompt: string, model: "claude-sonnet-4-20250514" | "claude-opus-4-1-20250805") => void;
  isLoading?: boolean;
  disabled?: boolean;
  defaultModel?: "claude-sonnet-4-20250514" | "claude-opus-4-1-20250805";
  projectPath?: string;
  className?: string;
  onCancel?: () => void;
  sessionId?: string;
  projectId?: string;
  onContinue?: () => void;
  isFirstPrompt?: boolean;
}

export interface FloatingPromptInputRef {
  addImage: (imagePath: string) => void;
  focus: () => void;
}

type ThinkingMode = "auto" | "think" | "think_hard" | "think_harder" | "ultrathink";

type ThinkingModeConfig = {
  id: ThinkingMode;
  name: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  phrase?: string;
};

const THINKING_MODES: ThinkingModeConfig[] = [
  {
    id: "auto",
    name: "Auto",
    description: "Let Claude decide",
    icon: <Sparkles className="h-3.5 w-3.5" />,
    color: "text-muted-foreground"
  },
  {
    id: "think",
    name: "Think",
    description: "Basic reasoning",
    icon: <Brain className="h-3.5 w-3.5" />,
    color: "text-blue-500",
    phrase: "think"
  },
  {
    id: "think_hard",
    name: "Deep Think",
    description: "Complex analysis",
    icon: <Brain className="h-3.5 w-3.5" />,
    color: "text-purple-500",
    phrase: "think hard"
  },
  {
    id: "think_harder",
    name: "Advanced Think",
    description: "Multi-step reasoning",
    icon: <Brain className="h-3.5 w-3.5" />,
    color: "text-orange-500",
    phrase: "think harder"
  },
  {
    id: "ultrathink",
    name: "Ultra Think",
    description: "Maximum analysis",
    icon: <Zap className="h-3.5 w-3.5" />,
    color: "text-red-500",
    phrase: "ultrathink"
  }
];

const MODELS = [
  {
    id: "claude-sonnet-4-20250514",
    name: "Claude Sonnet 4",
    shortName: "Sonnet 4",
    icon: <Sparkles className="h-3.5 w-3.5" />,
    description: "Fast & efficient",
    color: "text-blue-500"
  },
  {
    id: "claude-opus-4-1-20250805",
    name: "Claude Opus 4.1",
    shortName: "Opus 4.1",
    icon: <Zap className="h-3.5 w-3.5" />,
    description: "Most capable",
    color: "text-purple-500"
  }
];

export const FloatingPromptInputImproved = React.forwardRef<
  FloatingPromptInputRef,
  FloatingPromptInputProps
>((props, ref) => {
  const {
    onSend,
    isLoading = false,
    disabled = false,
    defaultModel = "claude-sonnet-4-20250514",
    projectPath,
    className,
    onCancel,
    sessionId,
    projectId,
    onContinue,
    isFirstPrompt = false
  } = props;

  const [prompt, setPrompt] = useState("");
  const [selectedModel, setSelectedModel] = useState<"claude-sonnet-4-20250514" | "claude-opus-4-1-20250805">(defaultModel);
  const [selectedThinkingMode, setSelectedThinkingMode] = useState<ThinkingMode>("auto");
  const [showModelPicker, setShowModelPicker] = useState(false);
  const [showThinkingPicker, setShowThinkingPicker] = useState(false);
  const [showFilePicker, setShowFilePicker] = useState(false);
  const [showSlashCommands, setShowSlashCommands] = useState(false);
  const [embeddedImages, setEmbeddedImages] = useState<string[]>([]);
  const [dragActive, setDragActive] = useState(false);
  const [usageInfo, setUsageInfo] = useState<ModelUsageInfo | null>(null);
  const [isFocused, setIsFocused] = useState(false);
  const [showPersonaActivation, setShowPersonaActivation] = useState(false);
  const [commandHistory, setCommandHistory] = useState<string[]>([]);

  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // SuperClaude integration
  const {
    activePersonas,
    suggestions: superClaudeSuggestions,
    selectedSuggestionIndex: superClaudeSelectedIndex,
    showSuggestions: showSuperClaudeSuggestions,
    processInput: processSuperClaudeInput,
    executeCommand: executeSuperClaudeCommand,
    selectCurrentSuggestion: selectCurrentSuperClaudeSuggestion,
    navigateSuggestions: navigateSuperClaudeSuggestions,
    clearSuggestions: clearSuperClaudeSuggestions,
    enhancePrompt: enhanceSuperClaudePrompt,
    removePersona,
    mightBeCommand: mightBeSuperClaudeCommand
  } = useSuperClaude({
    sessionId,
    projectId,
    useBackend: !!(sessionId && projectId),
    onCommandExecute: (command) => {
      // Add to command history
      setCommandHistory(prev => [...prev.slice(-9), command.trigger]);
      
      // Visual feedback
      setShowPersonaActivation(true);
      setTimeout(() => setShowPersonaActivation(false), 2000);
      
      window.dispatchEvent(new CustomEvent('superclaude-command-executed', {
        detail: command
      }));
    },
    onPersonaActivate: (personas) => {
      if (personas.length > 0) {
        setShowPersonaActivation(true);
        setTimeout(() => setShowPersonaActivation(false), 2500);
      }
    }
  });

  // Expose methods via ref
  React.useImperativeHandle(
    ref,
    () => ({
      addImage: (imagePath: string) => {
        setPrompt(currentPrompt => {
          const mention = imagePath.includes(' ') ? `@"${imagePath}"` : `@${imagePath}`;
          return currentPrompt + (currentPrompt.endsWith(' ') || currentPrompt === '' ? '' : ' ') + mention + ' ';
        });
      },
      focus: () => {
        textareaRef.current?.focus();
      }
    }),
    []
  );

  // Fetch usage info
  useEffect(() => {
    const fetchUsageInfo = async () => {
      try {
        const info = await api.getModelUsageInfo();
        setUsageInfo(info);
        if (!defaultModel || defaultModel === "auto") {
          setSelectedModel(info.current_model as "sonnet" | "opus-4.1");
        }
      } catch (error) {
      }
    };

    fetchUsageInfo();
    const interval = setInterval(fetchUsageInfo, 30000);
    return () => clearInterval(interval);
  }, [defaultModel]);

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      const newHeight = Math.min(textareaRef.current.scrollHeight, 200);
      textareaRef.current.style.height = `${newHeight}px`;
    }
  }, [prompt]);

  const handleSend = () => {
    if (!prompt.trim() || disabled) return;

    let finalPrompt = prompt;
    
    // Add thinking mode phrase if selected
    const thinkingMode = THINKING_MODES.find(m => m.id === selectedThinkingMode);
    if (thinkingMode?.phrase) {
      finalPrompt = `${thinkingMode.phrase} ${finalPrompt}`;
    }

    // Add SuperClaude enhancements
    if (activePersonas.length > 0) {
      finalPrompt = enhanceSuperClaudePrompt(finalPrompt);
    }

    onSend(finalPrompt, selectedModel);
    setPrompt("");
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Handle SuperClaude suggestions navigation first (priority)
    if (showSuperClaudeSuggestions && superClaudeSuggestions.length > 0) {
      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          navigateSuperClaudeSuggestions('down');
          return;
        case 'ArrowUp':
          e.preventDefault();
          navigateSuperClaudeSuggestions('up');
          return;
        case 'Tab':
        case 'Enter':
          if (superClaudeSelectedIndex >= 0) {
            e.preventDefault();
            const selected = superClaudeSuggestions[superClaudeSelectedIndex];
            if (selected) {
              executeSuperClaudeCommand(selected.command.trigger);
              setPrompt('');
              clearSuperClaudeSuggestions();
            }
            return;
          }
          break;
        case 'Escape':
          e.preventDefault();
          clearSuperClaudeSuggestions();
          return;
      }
    }
    
    // Handle command history navigation with Ctrl/Cmd + Up/Down
    if ((e.metaKey || e.ctrlKey) && commandHistory.length > 0) {
      if (e.key === 'ArrowUp' && commandHistory.length > 0) {
        e.preventDefault();
        const lastCommand = commandHistory[commandHistory.length - 1];
        setPrompt(lastCommand + ' ');
        processSuperClaudeInput(lastCommand + ' ');
        return;
      }
    }
    
    // Regular enter to send
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    setPrompt(newValue);
    
    // Smart command detection
    const hasSuperClaudeCommand = newValue.includes('/sc:');
    const hasRegularCommand = newValue.startsWith('/') && !hasSuperClaudeCommand;
    
    if (hasSuperClaudeCommand) {
      processSuperClaudeInput(newValue);
      setShowSlashCommands(false);
    } else if (hasRegularCommand) {
      setShowSlashCommands(true);
      clearSuperClaudeSuggestions();
    } else {
      setShowSlashCommands(false);
      // Still check for personas in regular text
      if (mightBeSuperClaudeCommand && mightBeSuperClaudeCommand(newValue)) {
        processSuperClaudeInput(newValue);
      }
    }
  };

  const selectedModelData = MODELS.find(m => m.id === selectedModel) || MODELS[0];
  const selectedThinkingData = THINKING_MODES.find(m => m.id === selectedThinkingMode) || THINKING_MODES[0];
  
  // Calculate usage warning level
  const usageWarning = usageInfo && usageInfo.usage_percentage >= 80;
  const usageCritical = usageInfo && usageInfo.usage_percentage >= 95;

  return (
    <>
      {/* SuperClaude Command Suggestions */}
      {showSuperClaudeSuggestions && superClaudeSuggestions.length > 0 && (
        <div className="fixed bottom-20 left-1/2 -translate-x-1/2 z-50 w-full max-w-2xl px-4">
          <CommandSuggestions
            suggestions={superClaudeSuggestions}
            selectedIndex={superClaudeSelectedIndex}
            onSelect={(suggestion) => executeSuperClaudeCommand(suggestion.command.trigger)}
            onClose={clearSuperClaudeSuggestions}
          />
        </div>
      )}

      {/* Main Input Container */}
      <div
        ref={containerRef}
        className={cn(
          "fixed bottom-0 left-0 right-0 z-40",
          "bg-gradient-to-t from-background via-background to-background/95",
          "border-t border-border/50 backdrop-blur-xl",
          dragActive && "ring-2 ring-primary/50 ring-offset-2",
          className
        )}
      >
        <div className="max-w-4xl mx-auto">
          {/* Usage Warning Bar */}
          <AnimatePresence>
            {usageInfo && usageInfo.usage_percentage > 70 && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: 'auto', opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                className="overflow-hidden"
              >
                <div className={cn(
                  "px-4 py-2 flex items-center justify-between text-xs",
                  usageWarning && !usageCritical && "bg-yellow-500/10 text-yellow-600 dark:text-yellow-400",
                  usageCritical && "bg-red-500/10 text-red-600 dark:text-red-400"
                )}>
                  <div className="flex items-center gap-2">
                    <AlertCircle className="h-3 w-3" />
                    <span>
                      {Math.round(usageInfo.usage_percentage)}% of {Math.round(usageInfo.total_limit / 1000)}K tokens used
                    </span>
                  </div>
                  {usageCritical && (
                    <Badge variant="destructive" className="text-xs">
                      Limit approaching
                    </Badge>
                  )}
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Enhanced Active Personas with Visual Feedback */}
          <AnimatePresence>
            {activePersonas.length > 0 && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: 'auto', opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                transition={{ duration: 0.2 }}
                className="px-4 py-2 bg-gradient-to-r from-primary/5 to-purple-500/5"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="flex items-center gap-2">
                      <Brain className="h-3.5 w-3.5 text-primary animate-pulse" />
                      <span className="text-xs font-medium bg-gradient-to-r from-primary to-purple-500 bg-clip-text text-transparent">
                        AI Personas:
                      </span>
                    </div>
                    <PersonaBadgeCompact personaIds={activePersonas} />
                    {showPersonaActivation && (
                      <motion.span
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0.8 }}
                        className="text-xs text-primary font-semibold flex items-center gap-1"
                      >
                        <Sparkles className="h-3 w-3 animate-spin" />
                        Enhanced!
                      </motion.span>
                    )}
                  </div>
                  <button
                    onClick={() => activePersonas.forEach(p => removePersona(p))}
                    className="text-xs text-muted-foreground hover:text-foreground transition-colors"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Image Previews */}
          <AnimatePresence>
            {embeddedImages.length > 0 && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: 'auto', opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                className="px-4 pt-3 overflow-hidden"
              >
                <ImagePreview
                  images={embeddedImages}
                  onRemove={(img) => {
                    setEmbeddedImages(prev => prev.filter(i => i !== img));
                    // Remove from prompt
                    setPrompt(prev => prev.replace(`@"${img}"`, '').replace(`@${img}`, '').trim());
                  }}
                  className="pb-2"
                />
              </motion.div>
            )}
          </AnimatePresence>

          {/* Main Input Area */}
          <div className="p-4">
            <div className={cn(
              "relative flex items-end gap-2 rounded-2xl",
              "bg-muted/50 backdrop-blur-sm",
              "border border-border/50",
              "transition-all duration-200",
              isFocused && "border-primary/50 shadow-lg shadow-primary/5",
              "hover:border-border"
            )}>
              {/* Left Action Buttons */}
              <div className="flex items-center gap-1 p-2 pb-3">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 rounded-xl hover:bg-background"
                        onClick={() => setShowFilePicker(true)}
                        disabled={disabled || !projectPath}
                      >
                        <Paperclip className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Attach files</TooltipContent>
                  </Tooltip>

                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 rounded-xl hover:bg-background"
                        disabled={disabled}
                      >
                        <Image className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Add image</TooltipContent>
                  </Tooltip>

                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 rounded-xl hover:bg-background"
                        onClick={() => setShowSlashCommands(true)}
                        disabled={disabled}
                      >
                        <Hash className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Commands</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>

              {/* Textarea */}
              <div className="flex-1 relative">
                <textarea
                  ref={textareaRef}
                  value={prompt}
                  onChange={handleTextChange}
                  onKeyDown={handleKeyDown}
                  onFocus={() => setIsFocused(true)}
                  onBlur={() => setIsFocused(false)}
                  placeholder={
                    isFirstPrompt 
                      ? "Start a new conversation..." 
                      : "Type your message..."
                  }
                  className={cn(
                    "w-full resize-none bg-transparent",
                    "text-sm leading-relaxed",
                    "placeholder:text-muted-foreground/60",
                    "focus:outline-none",
                    "py-3 pr-2",
                    "min-h-[44px] max-h-[200px]",
                    "scrollbar-thin scrollbar-thumb-border scrollbar-track-transparent"
                  )}
                  disabled={disabled}
                  rows={1}
                />
              </div>

              {/* Right Controls */}
              <div className="flex items-center gap-1 p-2 pb-3">
                {/* Model & Thinking Mode Selectors */}
                <div className="flex items-center gap-1 mr-1">
                  <TooltipProvider>
                    {/* Model Selector */}
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className={cn(
                            "h-7 px-2 gap-1.5 rounded-lg text-xs font-medium",
                            "hover:bg-background",
                            selectedModelData.color
                          )}
                          onClick={() => setShowModelPicker(!showModelPicker)}
                          disabled={disabled}
                        >
                          {selectedModelData.icon}
                          <span>{selectedModelData.shortName}</span>
                          <ChevronDown className="h-3 w-3 opacity-50" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>{selectedModelData.description}</TooltipContent>
                    </Tooltip>

                    {/* Thinking Mode Selector */}
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className={cn(
                            "h-7 px-2 gap-1.5 rounded-lg text-xs font-medium",
                            "hover:bg-background",
                            selectedThinkingData.color
                          )}
                          onClick={() => setShowThinkingPicker(!showThinkingPicker)}
                          disabled={disabled}
                        >
                          {selectedThinkingData.icon}
                          <ChevronDown className="h-3 w-3 opacity-50" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        {selectedThinkingData.name}: {selectedThinkingData.description}
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>

                <Separator orientation="vertical" className="h-6 mx-1" />

                {/* Send/Continue Button */}
                {isLoading ? (
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 rounded-xl"
                    onClick={onCancel}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                ) : (
                  <>
                    {onContinue && !prompt.trim() && (
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8 rounded-xl hover:bg-primary hover:text-primary-foreground"
                              onClick={onContinue}
                              disabled={disabled}
                            >
                              <ArrowUp className="h-4 w-4" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>Continue</TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    )}
                    
                    <Button
                      size="icon"
                      className={cn(
                        "h-8 w-8 rounded-xl",
                        "bg-primary hover:bg-primary/90",
                        "transition-all duration-200",
                        prompt.trim() && "animate-in fade-in-0 zoom-in-95"
                      )}
                      onClick={handleSend}
                      disabled={!prompt.trim() || disabled}
                    >
                      {isLoading ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <Send className="h-4 w-4" />
                      )}
                    </Button>
                  </>
                )}
              </div>
            </div>

            {/* Helper Text */}
            <div className="mt-2 px-2 flex items-center justify-between">
              <div className="flex items-center gap-4 text-xs text-muted-foreground">
                <span>⌘ + Enter to send</span>
                <span>Shift + Enter for new line</span>
                {projectPath && (
                  <span className="flex items-center gap-1">
                    <FileText className="h-3 w-3" />
                    {projectPath.split('/').pop()}
                  </span>
                )}
              </div>
              
              {usageInfo && (
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <span>{Math.round(usageInfo.tokens_used).toLocaleString()} tokens</span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Model Picker Dropdown */}
        <AnimatePresence>
          {showModelPicker && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 10 }}
              className="absolute bottom-full mb-2 left-1/2 -translate-x-1/2 z-50"
            >
              <div className="bg-popover border border-border rounded-xl shadow-xl p-2 min-w-[240px]">
                {MODELS.map((model) => (
                  <button
                    key={model.id}
                    onClick={() => {
                      setSelectedModel(model.id as "sonnet" | "opus-4.1");
                      setShowModelPicker(false);
                    }}
                    className={cn(
                      "w-full flex items-center gap-3 px-3 py-2.5 rounded-lg",
                      "hover:bg-accent transition-colors",
                      "text-left",
                      selectedModel === model.id && "bg-accent"
                    )}
                  >
                    <div className={cn("flex-shrink-0", model.color)}>
                      {model.icon}
                    </div>
                    <div className="flex-1">
                      <div className="font-medium text-sm">{model.name}</div>
                      <div className="text-xs text-muted-foreground">
                        {model.description}
                      </div>
                    </div>
                    {selectedModel === model.id && (
                      <div className="h-2 w-2 rounded-full bg-primary" />
                    )}
                  </button>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Thinking Mode Picker Dropdown */}
        <AnimatePresence>
          {showThinkingPicker && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 10 }}
              className="absolute bottom-full mb-2 left-1/2 -translate-x-1/2 z-50"
            >
              <div className="bg-popover border border-border rounded-xl shadow-xl p-2 min-w-[240px]">
                {THINKING_MODES.map((mode) => (
                  <button
                    key={mode.id}
                    onClick={() => {
                      setSelectedThinkingMode(mode.id);
                      setShowThinkingPicker(false);
                    }}
                    className={cn(
                      "w-full flex items-center gap-3 px-3 py-2.5 rounded-lg",
                      "hover:bg-accent transition-colors",
                      "text-left",
                      selectedThinkingMode === mode.id && "bg-accent"
                    )}
                  >
                    <div className={cn("flex-shrink-0", mode.color)}>
                      {mode.icon}
                    </div>
                    <div className="flex-1">
                      <div className="font-medium text-sm">{mode.name}</div>
                      <div className="text-xs text-muted-foreground">
                        {mode.description}
                      </div>
                    </div>
                    {selectedThinkingMode === mode.id && (
                      <div className="h-2 w-2 rounded-full bg-primary" />
                    )}
                  </button>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </>
  );
});

FloatingPromptInputImproved.displayName = "FloatingPromptInputImproved";