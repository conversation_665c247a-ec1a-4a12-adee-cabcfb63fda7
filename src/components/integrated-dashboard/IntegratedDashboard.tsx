import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { IntegratedNavigation, NavigationView } from '../shared';
import { TrainingBudget } from '../budget';
import { FinancialDashboard } from '../financial-dashboard';
import { mockRootProps } from '../financial-dashboard/FinancialDashboardMockData';

interface IntegratedDashboardProps {
  defaultView?: NavigationView;
}

export const IntegratedDashboard: React.FC<IntegratedDashboardProps> = ({
  defaultView = 'budget'
}) => {
  const [activeView, setActiveView] = useState<NavigationView>(defaultView);
  const [isDarkMode, setIsDarkMode] = useState(true);

  // Set dark theme on mount for financial dashboard
  useEffect(() => {
    if (activeView === 'financial') {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
    
    return () => {
      document.documentElement.classList.remove('dark');
    };
  }, [activeView]);

  const handleViewChange = (view: NavigationView) => {
    setActiveView(view);
  };

  const renderContent = () => {
    switch (activeView) {
      case 'budget':
        return <TrainingBudget />;
      
      case 'financial':
        return (
          <FinancialDashboard
            {...mockRootProps}
            onTimeFrameChange={(timeFrame) => console.log('Time frame changed:', timeFrame)}
            onBuyStock={() => console.log('Buy stock clicked')}
            onSellStock={() => console.log('Sell stock clicked')}
            onDeposit={() => console.log('Deposit clicked')}
            onWithdraw={() => console.log('Withdraw clicked')}
            onVisitWebsite={(url) => window.open(url, '_blank')}
          />
        );
      
      case 'analytics':
        return (
          <Card className="p-8 text-center">
            <h2 className="text-2xl font-bold mb-4">Analytics Dashboard</h2>
            <p className="text-muted-foreground">
              Comprehensive analytics combining budget and financial data will be displayed here.
            </p>
          </Card>
        );
      
      case 'settings':
        return (
          <Card className="p-8 text-center">
            <h2 className="text-2xl font-bold mb-4">Settings</h2>
            <p className="text-muted-foreground">
              Configuration options for both budget and financial dashboard will be available here.
            </p>
          </Card>
        );
      
      default:
        return <TrainingBudget />;
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto p-4 space-y-6">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Integrated Dashboard
            </h1>
            <p className="text-muted-foreground">
              Unified view of budget management and financial portfolio
            </p>
          </div>
        </div>

        {/* Navigation */}
        <IntegratedNavigation
          activeView={activeView}
          onViewChange={handleViewChange}
          variant="tabs"
        />

        {/* Content */}
        <div className="min-h-[600px]">
          {renderContent()}
        </div>
      </div>
    </div>
  );
};