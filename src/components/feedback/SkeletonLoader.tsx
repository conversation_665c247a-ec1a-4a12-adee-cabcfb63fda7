import React from 'react';

interface SkeletonLoaderProps {
  variant?: 'text' | 'card' | 'avatar' | 'button' | 'custom';
  width?: string;
  height?: string;
  className?: string;
  count?: number;
}

export const SkeletonLoader: React.FC<SkeletonLoaderProps> = ({
  variant = 'text',
  width,
  height,
  className = '',
  count = 1
}) => {
  const baseClasses = 'animate-pulse bg-muted rounded';

  const variantClasses = {
    text: 'h-4 w-full',
    card: 'h-32 w-full',
    avatar: 'h-10 w-10 rounded-full',
    button: 'h-10 w-24',
    custom: ''
  };

  const skeletonStyle = {
    width: width || undefined,
    height: height || undefined
  };

  const SkeletonElement = () => (
    <div
      className={`${baseClasses} ${variantClasses[variant]} ${className}`}
      style={skeletonStyle}
    />
  );

  if (variant === 'card') {
    return (
      <div className="space-y-4">
        {Array.from({ length: count }).map((_, index) => (
          <div key={index} className="space-y-3">
            <div className="flex items-center space-x-3">
              <div className="animate-pulse bg-muted rounded-full h-10 w-10" />
              <div className="space-y-2 flex-1">
                <div className="animate-pulse bg-muted rounded h-4 w-3/4" />
                <div className="animate-pulse bg-muted rounded h-3 w-1/2" />
              </div>
            </div>
            <div className="animate-pulse bg-muted rounded h-32 w-full" />
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-2">
      {Array.from({ length: count }).map((_, index) => (
        <SkeletonElement key={index} />
      ))}
    </div>
  );
};