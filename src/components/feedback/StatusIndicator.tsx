import React from 'react';
import { CheckCircle, AlertCircle, XCircle, Clock, Zap } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

export type StatusType = 'success' | 'warning' | 'error' | 'pending' | 'processing';

interface StatusIndicatorProps {
  status: StatusType;
  label: string;
  description?: string;
  showIcon?: boolean;
  variant?: 'dot' | 'badge' | 'full';
  className?: string;
}

const statusConfig = {
  success: {
    icon: CheckCircle,
    color: 'text-green-600',
    bgColor: 'bg-green-100 dark:bg-green-900/20',
    dotColor: 'bg-green-500',
    badgeVariant: 'default' as const
  },
  warning: {
    icon: AlertCircle,
    color: 'text-yellow-600',
    bgColor: 'bg-yellow-100 dark:bg-yellow-900/20',
    dotColor: 'bg-yellow-500',
    badgeVariant: 'secondary' as const
  },
  error: {
    icon: XCircle,
    color: 'text-red-600',
    bgColor: 'bg-red-100 dark:bg-red-900/20',
    dotColor: 'bg-red-500',
    badgeVariant: 'destructive' as const
  },
  pending: {
    icon: Clock,
    color: 'text-blue-600',
    bgColor: 'bg-blue-100 dark:bg-blue-900/20',
    dotColor: 'bg-blue-500',
    badgeVariant: 'outline' as const
  },
  processing: {
    icon: Zap,
    color: 'text-purple-600',
    bgColor: 'bg-purple-100 dark:bg-purple-900/20',
    dotColor: 'bg-purple-500 animate-pulse',
    badgeVariant: 'secondary' as const
  }
};

export const StatusIndicator: React.FC<StatusIndicatorProps> = ({
  status,
  label,
  description,
  showIcon = true,
  variant = 'full',
  className = ''
}) => {
  const config = statusConfig[status];
  const Icon = config.icon;

  if (variant === 'dot') {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <div className={`w-2 h-2 rounded-full ${config.dotColor}`} />
        <span className="text-sm font-medium">{label}</span>
      </div>
    );
  }

  if (variant === 'badge') {
    return (
      <Badge variant={config.badgeVariant} className={className}>
        {showIcon && <Icon className="w-3 h-3 mr-1" />}
        {label}
      </Badge>
    );
  }

  return (
    <div className={`flex items-start gap-3 p-3 rounded-lg ${config.bgColor} ${className}`}>
      {showIcon && (
        <Icon className={`w-5 h-5 ${config.color} mt-0.5 flex-shrink-0`} />
      )}
      <div className="flex-1 min-w-0">
        <p className={`font-medium ${config.color}`}>{label}</p>
        {description && (
          <p className="text-sm text-muted-foreground mt-1">{description}</p>
        )}
      </div>
    </div>
  );
};