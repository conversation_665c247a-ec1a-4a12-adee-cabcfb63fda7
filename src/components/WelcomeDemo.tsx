import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Welcome } from './navigation/welcome/Welcome';
import { OnboardingWelcome } from './data-center/onboarding/OnboardingWelcome';
import { WelcomeEnhanced } from './navigation/welcome/WelcomeEnhanced';
import { StatusIndicator } from './feedback/StatusIndicator';
import { LoadingSpinner } from './feedback/LoadingSpinner';
import { SkeletonLoader } from './feedback/SkeletonLoader';
import { ProgressRing } from './progress/ProgressRing';
import { SearchInput } from './search/SearchInput';
import { Breadcrumb } from './navigation/Breadcrumb';

export const WelcomeDemo: React.FC = () => {
  const [showOnboarding, setShowOnboarding] = useState(false);
  const [currentVariant, setCurrentVariant] = useState<'default' | 'minimal' | 'professional' | 'creative' | 'dark'>('default');
  const [userProgress, setUserProgress] = useState({
    completedTutorials: ['getting-started'],
    totalProgress: 35
  });
  const [demoMode, setDemoMode] = useState<'basic' | 'enhanced' | 'components'>('enhanced');
  const [searchValue, setSearchValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleNewSession = () => {
    console.log('Starting new session...');
    // In a real app, this would navigate to the session creation flow
  };

  const handleStartTutorial = () => {
    console.log('Starting tutorial...');
    setShowOnboarding(false);
    // In a real app, this would start the tutorial flow
  };

  const handleSkipOnboarding = () => {
    console.log('Skipping onboarding...');
    setShowOnboarding(false);
  };

  const variants = [
    { value: 'default', label: 'Default' },
    { value: 'minimal', label: 'Minimal' },
    { value: 'professional', label: 'Professional' },
    { value: 'creative', label: 'Creative' },
    { value: 'dark', label: 'Dark' }
  ] as const;

  return (
    <div className="min-h-screen bg-background">
      {/* Demo Controls */}
      <div className="sticky top-0 z-50 bg-background/95 backdrop-blur border-b">
        <div className="container mx-auto px-4 py-3">
          <div className="flex flex-wrap items-center justify-between gap-4">
            <div>
              <h1 className="text-lg font-semibold">Welcome Component Demo</h1>
              <p className="text-sm text-muted-foreground">
                Interactive demonstration of improved welcome components
              </p>
            </div>
            <div className="flex flex-wrap items-center gap-3">
              <div className="flex items-center gap-2">
                <label className="text-sm font-medium">Mode:</label>
                <select 
                  value={demoMode}
                  onChange={(e) => setDemoMode(e.target.value as any)}
                  className="px-3 py-1 border border-input rounded-md bg-background text-sm"
                >
                  <option value="enhanced">Enhanced Welcome</option>
                  <option value="basic">Basic Welcome</option>
                  <option value="components">Component Showcase</option>
                </select>
              </div>
              <div className="flex items-center gap-2">
                <label className="text-sm font-medium">Variant:</label>
                <select 
                  value={currentVariant}
                  onChange={(e) => setCurrentVariant(e.target.value as any)}
                  className="px-3 py-1 border border-input rounded-md bg-background text-sm"
                >
                  {variants.map(variant => (
                    <option key={variant.value} value={variant.value}>
                      {variant.label}
                    </option>
                  ))}
                </select>
              </div>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => setShowOnboarding(true)}
              >
                Show Onboarding
              </Button>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => setUserProgress(prev => ({
                  ...prev,
                  totalProgress: Math.min(100, prev.totalProgress + 25)
                }))}
              >
                Increase Progress
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Improvements Summary */}
      <div className="container mx-auto px-4 py-6">
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Badge variant="secondary">Improvements</Badge>
              Welcome Component Analysis & Enhancements
            </CardTitle>
            <CardDescription>
              Key improvements implemented based on UI/UX analysis
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="space-y-2">
                <h4 className="font-semibold text-green-600">✅ Accessibility</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• ARIA labels and roles</li>
                  <li>• Keyboard navigation</li>
                  <li>• Focus management</li>
                  <li>• Screen reader support</li>
                </ul>
              </div>
              <div className="space-y-2">
                <h4 className="font-semibold text-blue-600">✅ User Experience</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Smooth animations</li>
                  <li>• Loading states</li>
                  <li>• Progress tracking</li>
                  <li>• Clear visual hierarchy</li>
                </ul>
              </div>
              <div className="space-y-2">
                <h4 className="font-semibold text-purple-600">✅ Responsive Design</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Mobile-first approach</li>
                  <li>• Touch-friendly targets</li>
                  <li>• Flexible layouts</li>
                  <li>• Consistent spacing</li>
                </ul>
              </div>
              <div className="space-y-2">
                <h4 className="font-semibold text-orange-600">✅ Code Quality</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• TypeScript interfaces</li>
                  <li>• Error handling</li>
                  <li>• Reusable components</li>
                  <li>• Clean architecture</li>
                </ul>
              </div>
              <div className="space-y-2">
                <h4 className="font-semibold text-red-600">✅ Styling</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Unified Tailwind approach</li>
                  <li>• CSS variable theming</li>
                  <li>• Consistent design tokens</li>
                  <li>• Dark mode support</li>
                </ul>
              </div>
              <div className="space-y-2">
                <h4 className="font-semibold text-teal-600">✅ Performance</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Lazy loading</li>
                  <li>• Optimized animations</li>
                  <li>• Efficient re-renders</li>
                  <li>• Memory management</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Based on Demo Mode */}
      {demoMode === 'enhanced' && (
        <WelcomeEnhanced
          onNewSession={handleNewSession}
          variant={currentVariant}
        />
      )}

      {demoMode === 'basic' && (
        <Welcome
          onNewSession={handleNewSession}
          variant={currentVariant}
          userProgress={userProgress}
          showOnboarding={showOnboarding}
        />
      )}

      {demoMode === 'components' && (
        <div className="container mx-auto px-4 py-8 space-y-8">
          {/* Component Showcase */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            
            {/* Status Indicators */}
            <Card>
              <CardHeader>
                <CardTitle>Status Indicators</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <StatusIndicator status="success" label="Connected" variant="badge" />
                <StatusIndicator status="warning" label="Limited Access" variant="dot" />
                <StatusIndicator status="error" label="Connection Failed" description="Please check your network" />
                <StatusIndicator status="processing" label="Syncing..." variant="badge" />
              </CardContent>
            </Card>

            {/* Loading States */}
            <Card>
              <CardHeader>
                <CardTitle>Loading States</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <LoadingSpinner size="sm" text="Loading..." />
                <LoadingSpinner size="md" variant="dots" />
                <LoadingSpinner size="lg" variant="pulse" />
                <Button 
                  onClick={() => {
                    setIsLoading(true);
                    setTimeout(() => setIsLoading(false), 2000);
                  }}
                  disabled={isLoading}
                >
                  {isLoading ? <LoadingSpinner size="sm" /> : 'Test Loading'}
                </Button>
              </CardContent>
            </Card>

            {/* Progress Ring */}
            <Card>
              <CardHeader>
                <CardTitle>Progress Tracking</CardTitle>
              </CardHeader>
              <CardContent className="flex items-center justify-center">
                <ProgressRing 
                  progress={userProgress.totalProgress} 
                  size={100}
                  color="rgb(59, 130, 246)"
                />
              </CardContent>
            </Card>

            {/* Search Input */}
            <Card>
              <CardHeader>
                <CardTitle>Search Components</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <SearchInput
                  placeholder="Search features..."
                  value={searchValue}
                  onChange={setSearchValue}
                  size="sm"
                />
                <SearchInput
                  placeholder="Search with filter..."
                  showFilter
                  onFilterClick={() => console.log('Filter clicked')}
                />
              </CardContent>
            </Card>

            {/* Breadcrumb */}
            <Card>
              <CardHeader>
                <CardTitle>Navigation</CardTitle>
              </CardHeader>
              <CardContent>
                <Breadcrumb
                  items={[
                    { label: 'Home', onClick: () => console.log('Home') },
                    { label: 'Components', onClick: () => console.log('Components') },
                    { label: 'Demo', isActive: true }
                  ]}
                />
              </CardContent>
            </Card>

            {/* Skeleton Loaders */}
            <Card>
              <CardHeader>
                <CardTitle>Skeleton Loaders</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <SkeletonLoader variant="text" count={3} />
                <SkeletonLoader variant="card" />
                <div className="flex items-center space-x-3">
                  <SkeletonLoader variant="avatar" />
                  <div className="flex-1">
                    <SkeletonLoader variant="text" width="60%" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {/* Onboarding Modal */}
      <OnboardingWelcome
        isOpen={showOnboarding}
        onStartTutorial={handleStartTutorial}
        onSkip={handleSkipOnboarding}
        onClose={() => setShowOnboarding(false)}
        progress={userProgress}
        variant="modal"
      />
    </div>
  );
};

export default WelcomeDemo;