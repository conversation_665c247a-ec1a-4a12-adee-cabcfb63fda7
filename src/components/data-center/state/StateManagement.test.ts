import { DataPipelineStateSchema, validateDataPipelineState } from './StateSchema';
import { OptimisticUpdateManager } from './OptimisticUpdates';
import { MultiLevelCache } from './StateCache';
import { StateOptimizer } from './StateOptimizer';

// Mock initial state for testing
const mockInitialState = {
  fileName: 'test.csv',
  fileType: 'csv',
  fileSize: 1024,
  uploadDate: new Date(),
  dataPreview: {
    headers: ['name', 'age', 'city'],
    rows: [
      ['John', 30, 'New York'],
      ['Jane', 25, 'Los Angeles']
    ],
    totalRows: 2
  },
  processingResult: null,
  pythonScript: '',
  pythonOutput: '',
  selectedTemplate: '',
  sqlResult: null,
  currentStage: 'upload' as const,
  pipelineHistory: [],
  storageFile: null,
  preferences: {
    viewMode: 'grid' as const,
    theme: 'system' as const,
    showSchema: true,
    autoRefresh: true
  },
  filters: {},
  savePoints: [],
  context: {
    breadcrumbPath: [],
    currentData: null,
    activeFilters: {},
    processingMetadata: {}
  },
  performance: {
    cacheEnabled: true,
    virtualizationEnabled: true,
    backgroundProcessing: true,
    workerPoolSize: 3,
    cacheStats: {
      hitRate: 0,
      missRate: 0,
      cacheSize: 0
    }
  }
};

describe('Enhanced State Management System', () => {
  describe('State Schema Validation', () => {
    it('should validate correct state', () => {
      const result = validateDataPipelineState(mockInitialState);
      expect(result.valid).toBe(true);
    });

    it('should reject invalid state', () => {
      const invalidState = {
        ...mockInitialState,
        fileName: 123 // Should be string
      };
      
      const result = validateDataPipelineState(invalidState);
      expect(result.valid).toBe(false);
      expect(result.errors).toBeDefined();
    });
  });

  describe('Optimistic Updates', () => {
    it('should apply and confirm updates', () => {
      const manager = new OptimisticUpdateManager(mockInitialState);
      
      // Apply optimistic update
      const updateId = manager.applyOptimisticUpdate(
        'update_file',
        { ...mockInitialState, fileName: 'updated.csv' },
        mockInitialState
      );
      
      expect(updateId).toBeDefined();
      expect(manager.getPendingUpdates().length).toBe(1);
      
      // Confirm update
      const confirmed = manager.confirmUpdate(updateId);
      expect(confirmed).toBe(true);
      expect(manager.getPendingUpdates().length).toBe(0);
    });

    it('should handle update failures', () => {
      const manager = new OptimisticUpdateManager(mockInitialState);
      
      // Apply optimistic update
      const updateId = manager.applyOptimisticUpdate(
        'update_file',
        { ...mockInitialState, fileName: 'updated.csv' },
        mockInitialState
      );
      
      // Handle failure
      const failed = manager.handleUpdateFailure(updateId, 'Network error');
      expect(failed).toBe(true);
      expect(manager.getCurrentState().fileName).toBe('test.csv'); // Should rollback
    });
  });

  describe('Caching System', () => {
    it('should set and get cache values', () => {
      const cache = new MultiLevelCache();
      
      // Set value
      cache.set('test-key', 'test-value', { ttl: 1000 });
      
      // Get value
      const value = cache.get('test-key');
      expect(value).toBe('test-value');
    });

    it('should handle cache expiration', () => {
      const cache = new MultiLevelCache();
      
      // Set value with short TTL
      cache.set('test-key', 'test-value', { ttl: 1 });
      
      // Wait for expiration
      setTimeout(() => {
        const value = cache.get('test-key');
        expect(value).toBeNull();
      }, 10);
    });

    it('should invalidate cache by tag', () => {
      const cache = new MultiLevelCache();
      
      // Set values with tags
      cache.set('key1', 'value1', { tags: ['tag1'] });
      cache.set('key2', 'value2', { tags: ['tag1', 'tag2'] });
      cache.set('key3', 'value3', { tags: ['tag2'] });
      
      // Invalidate by tag
      const invalidated = cache.invalidateByTag('tag1');
      expect(invalidated).toBe(2);
      
      // Check remaining values
      expect(cache.get('key1')).toBeNull();
      expect(cache.get('key2')).toBeNull();
      expect(cache.get('key3')).toBe('value3');
    });
  });

  describe('State Optimization', () => {
    it('should chunk large data arrays', () => {
      const optimizer = new StateOptimizer(mockInitialState);
      
      // Create large array
      const largeArray = Array.from({ length: 10000 }, (_, i) => i);
      
      // Chunk the array
      const chunkIds = optimizer.chunkData('largeArray' as any, largeArray as any);
      
      expect(chunkIds.length).toBeGreaterThan(0);
      expect(optimizer.getChunk(chunkIds[0])).toBeDefined();
    });

    it('should compute and cache values', () => {
      const optimizer = new StateOptimizer(mockInitialState);
      
      // Compute a value
      const computed = optimizer.computeState('test-computation', () => {
        return mockInitialState.fileName.toUpperCase();
      }, ['fileName']);
      
      expect(computed).toBe('TEST.CSV');
    });
  });
});

export {};