import React, { useState } from 'react';
import { 
  useEnhanced<PERSON>ataC<PERSON>,
  StateDebugger,
  StateVisualizer,
  validateDataPipelineState
} from './index';

// Demo component showing enhanced state management features
export const StateManagementDemo: React.FC = () => {
  const { 
    pipelineState, 
    updatePipelineState,
    validateState,
    applyOptimisticUpdate,
    getCache,
    setCache,
    getPerformanceStats,
    measurePerformance
  } = useEnhancedDataCenter();
  
  const [fileName, setFileName] = useState('');
  const [cacheKey, setCacheKey] = useState('');
  const [cacheValue, setCacheValue] = useState('');
  const [validationResult, setValidationResult] = useState<{valid: boolean; errors?: any[]} | null>(null);

  // Handle file upload with optimistic update
  const handleFileUpload = async () => {
    if (!fileName) return;
    
    try {
      // Apply optimistic update
      const updateId = await applyOptimisticUpdate('upload_file', {
        fileName,
        uploadDate: new Date(),
        currentStage: 'preview'
      });
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Confirm update
      // In a real app, this would be called after successful API response
      console.log('Optimistic update applied with ID:', updateId);
    } catch (error) {
      console.error('File upload failed:', error);
    }
  };

  // Handle cache operations
  const handleSetCache = () => {
    if (cacheKey && cacheValue) {
      setCache(cacheKey, cacheValue, { ttl: 300000 }); // 5 minutes TTL
    }
  };

  // Handle cache get
  const handleGetCache = () => {
    if (cacheKey) {
      const value = getCache(cacheKey);
      console.log('Cache value:', value);
    }
  };

  // Validate current state
  const handleValidateState = () => {
    const result = validateState();
    setValidationResult(result);
  };

  // Measure performance
  const handleMeasurePerformance = () => {
    const stats = measurePerformance('demo_operation', () => {
      // Simulate some work
      let sum = 0;
      for (let i = 0; i < 1000000; i++) {
        sum += i;
      }
      return sum;
    });
    
    console.log('Performance result:', stats);
  };

  return (
    <div className="p-6 space-y-6">
      <h2 className="text-2xl font-bold">Enhanced State Management Demo</h2>
      
      {/* File Upload Section */}
      <div className="border rounded-lg p-4">
        <h3 className="text-lg font-semibold mb-3">Optimistic Updates</h3>
        <div className="flex gap-2">
          <input
            type="text"
            value={fileName}
            onChange={(e) => setFileName(e.target.value)}
            placeholder="Enter file name"
            className="flex-1 p-2 border rounded"
          />
          <button
            onClick={handleFileUpload}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Upload File
          </button>
        </div>
      </div>
      
      {/* Cache Operations */}
      <div className="border rounded-lg p-4">
        <h3 className="text-lg font-semibold mb-3">Caching</h3>
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <input
              type="text"
              value={cacheKey}
              onChange={(e) => setCacheKey(e.target.value)}
              placeholder="Cache key"
              className="w-full p-2 border rounded"
            />
            <input
              type="text"
              value={cacheValue}
              onChange={(e) => setCacheValue(e.target.value)}
              placeholder="Cache value"
              className="w-full p-2 border rounded"
            />
            <button
              onClick={handleSetCache}
              className="w-full px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
            >
              Set Cache
            </button>
          </div>
          <div className="space-y-2">
            <input
              type="text"
              value={cacheKey}
              onChange={(e) => setCacheKey(e.target.value)}
              placeholder="Cache key"
              className="w-full p-2 border rounded"
            />
            <button
              onClick={handleGetCache}
              className="w-full px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600"
            >
              Get Cache
            </button>
          </div>
        </div>
      </div>
      
      {/* Validation */}
      <div className="border rounded-lg p-4">
        <h3 className="text-lg font-semibold mb-3">State Validation</h3>
        <div className="space-y-3">
          <button
            onClick={handleValidateState}
            className="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600"
          >
            Validate State
          </button>
          
          {validationResult && (
            <div className={`p-3 rounded ${validationResult.valid ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
              <p className="font-medium">
                {validationResult.valid ? '✓ State is valid' : '✗ State validation failed'}
              </p>
              {!validationResult.valid && validationResult.errors && (
                <ul className="mt-2 list-disc pl-5">
                  {validationResult.errors.map((error, index) => (
                    <li key={index}>{error.message} (Path: {error.path.join('.')})</li>
                  ))}
                </ul>
              )}
            </div>
          )}
        </div>
      </div>
      
      {/* Performance Monitoring */}
      <div className="border rounded-lg p-4">
        <h3 className="text-lg font-semibold mb-3">Performance</h3>
        <div className="space-y-3">
          <button
            onClick={handleMeasurePerformance}
            className="px-4 py-2 bg-indigo-500 text-white rounded hover:bg-indigo-600"
          >
            Measure Performance
          </button>
          
          <div>
            <h4 className="font-medium mb-2">Performance Stats:</h4>
            <pre className="bg-gray-100 p-2 rounded text-sm">
              {JSON.stringify(getPerformanceStats(), null, 2)}
            </pre>
          </div>
        </div>
      </div>
      
      {/* State Visualization */}
      <div className="border rounded-lg p-4">
        <h3 className="text-lg font-semibold mb-3">Current State</h3>
        <div className="max-h-64 overflow-y-auto">
          <StateVisualizer state={pipelineState} />
        </div>
      </div>
      
      {/* State Debugger */}
      <StateDebugger show={true} />
    </div>
  );
};

// Wrapper component to provide the enhanced context
export const StateManagementDemoWrapper: React.FC<{children: React.ReactNode}> = ({ children }) => {
  return (
    <div className="max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold text-center my-8">Data Center Enhanced State Management</h1>
      {children}
      <div className="mt-8 p-4 bg-blue-50 border rounded-lg">
        <h2 className="text-xl font-semibold mb-2">Enhanced State Management Features</h2>
        <ul className="list-disc pl-5 space-y-1">
          <li>State Validation with Zod schemas</li>
          <li>Optimistic Updates with rollback support</li>
          <li>Multi-level Caching System</li>
          <li>Performance Optimization for Large Datasets</li>
          <li>Development Debugging Tools</li>
        </ul>
      </div>
    </div>
  );
};