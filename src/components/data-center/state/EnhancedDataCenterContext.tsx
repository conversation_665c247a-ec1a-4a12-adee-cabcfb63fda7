import React, { create<PERSON>ontext, useContext, useState, useEffect, ReactNode } from 'react';
import { StorageFile } from '@/lib/services/browserStorageManager';
import { HistoryManager } from '../utils/stateManagement';
import { dataCache, processedDataCache, persistentCache } from '../performance';
import { 
  DataPipelineStateSchema, 
  validateDataPipelineState, 
  validatePartialState,
  ValidationError
} from './StateSchema';
import { 
  OptimisticUpdateManager, 
  ConflictResolver, 
  UpdateIndicatorManager 
} from './OptimisticUpdates';
import { MultiLevelCache } from './StateCache';
import { StateOptimizer, StatePerformanceMonitor } from './StateOptimizer';

// Extended interfaces for enhanced state management
export interface DataPreview {
  headers: string[];
  rows: any[][];
  totalRows: number;
  dataTypes?: Record<string, string>;
  statistics?: Record<string, any>;
  fullData?: any[][]; // Store full dataset for exploration
}

export interface ProcessingResult {
  content: string;
  metadata: Record<string, any>;
  chunks?: Array<{
    text: string;
    metadata: Record<string, any>;
  }>;
  embeddings?: number[][];
}

export interface SQLGenerationResult {
  sql: string;
  isValid: boolean;
  error?: string;
  analysis?: string;
}

export interface DataPipelineState {
  // File information
  fileName: string;
  fileType: string;
  fileSize: number;
  uploadDate: Date;
  
  // Data preview
  dataPreview: DataPreview | null;
  
  // Processing results
  processingResult: ProcessingResult | null;
  pythonScript: string;
  pythonOutput: string;
  selectedTemplate: string;
  
  // SQL generation
  sqlResult: SQLGenerationResult | null;
  
  // Pipeline tracking
  currentStage: 'upload' | 'preview' | 'explore' | 'processing' | 'analysis' | 'sql';
  pipelineHistory: Array<{
    stage: string;
    timestamp: Date;
    status: 'completed' | 'pending' | 'error';
    details?: string;
  }>;
  
  // Storage reference
  storageFile: StorageFile | null;
  
  // User preferences
  preferences: {
    viewMode: 'grid' | 'list' | 'tree';
    theme: 'light' | 'dark' | 'system';
    showSchema: boolean;
    autoRefresh: boolean;
    sidebarCollapsed?: boolean;
    lastActiveView?: string;
    favorites?: string[];
  };
  
  // Filters with persistence across tabs
  filters: Record<string, any>;
  
  // Save points for analysis states
  savePoints: Array<{
    id: string;
    name: string;
    timestamp: Date;
    state: Partial<DataPipelineState>;
    description?: string;
  }>;
  
  // Context tracking for persistent state between tabs
  context: {
    breadcrumbPath: Array<{
      stage: string;
      label: string;
      timestamp: Date;
      dataSummary?: {
        rows?: number;
        columns?: number;
        fileType?: string;
      };
    }>;
    currentData: any; // Persistent data context across tabs
    activeFilters: Record<string, any>;
    processingMetadata: Record<string, any>;
  };
  
  // Performance optimization state
  performance: {
    cacheEnabled: boolean;
    virtualizationEnabled: boolean;
    backgroundProcessing: boolean;
    workerPoolSize: number;
    cacheStats: {
      hitRate: number;
      missRate: number;
      cacheSize: number;
    };
  };
  
  // Enhanced state management properties
  validationErrors?: ValidationError[];
  isOptimisticUpdatePending?: boolean;
  lastOptimisticUpdateId?: string;
}

// Enhanced context type with new features
interface EnhancedDataContextType {
  pipelineState: DataPipelineState;
  updatePipelineState: (updates: Partial<DataPipelineState>, notifyParent?: boolean) => Promise<boolean>;
  resetPipeline: () => void;
  navigateToStage: (stage: DataPipelineState['currentStage']) => void;
  updateContext: (contextUpdates: Partial<DataPipelineState['context']>) => void;
  addToBreadcrumb: (stage: string, label: string, dataSummary?: any) => void;
  createSavePoint: (name: string, description?: string) => void;
  loadSavePoint: (id: string) => void;
  deleteSavePoint: (id: string) => void;
  updateFilters: (newFilters: Record<string, any>) => void;
  clearFilters: (filterKeys?: string[]) => void;
  historyManager: HistoryManager;
  canUndo: boolean;
  canRedo: boolean;
  undo: () => void;
  redo: () => void;
  
  // Performance optimization functions
  enableCaching: (enabled: boolean) => void;
  enableVirtualization: (enabled: boolean) => void;
  updateWorkerPoolSize: (size: number) => void;
  getCacheStats: () => { hitRate: number; missRate: number; cacheSize: number };
  clearCache: () => void;
  
  // Enhanced state management functions
  validateState: () => { valid: boolean; errors?: ValidationError[] };
  validatePartialState: (partialState: Partial<DataPipelineState>) => { valid: boolean; errors?: ValidationError[] };
  applyOptimisticUpdate: (action: string, updates: Partial<DataPipelineState>) => Promise<string>;
  confirmOptimisticUpdate: (id: string) => boolean;
  rollbackOptimisticUpdate: (id: string, error?: string) => boolean;
  getPendingUpdates: () => Array<{ id: string; action: string; timestamp: number; status: string }>;
  getFailedUpdates: () => Array<{ id: string; action: string; timestamp: number; status: string; error?: string }>;
  clearCompletedOptimisticUpdates: () => number;
  getCache: (key: string) => any;
  setCache: (key: string, data: any, options?: { ttl?: number; tags?: string[] }) => void;
  invalidateCacheByTag: (tag: string) => number;
  getPerformanceStats: () => Record<string, any>;
  measurePerformance: <T>(operation: string, fn: () => T) => T;
  getSnapshot: () => string;
  restoreSnapshot: (id: string) => boolean;
  logStateChange: (action: string, path: string[], oldValue: any, newValue: any) => void;
}

const DataContext = createContext<EnhancedDataContextType | undefined>(undefined);

export const EnhancedDataCenterProvider: React.FC<{ 
  children: ReactNode;
  onDataChange?: (data: Partial<DataPipelineState>) => void;
}> = ({ children, onDataChange }) => {
  const [pipelineState, setPipelineState] = useState<DataPipelineState>({
    fileName: '',
    fileType: '',
    fileSize: 0,
    uploadDate: new Date(),
    dataPreview: null,
    processingResult: null,
    pythonScript: '',
    pythonOutput: '',
    selectedTemplate: '',
    sqlResult: null,
    currentStage: 'upload',
    pipelineHistory: [],
    storageFile: null,
    preferences: {
      viewMode: 'grid',
      theme: 'system',
      showSchema: true,
      autoRefresh: true
    },
    filters: {},
    savePoints: [],
    context: {
      breadcrumbPath: [],
      currentData: null,
      activeFilters: {},
      processingMetadata: {}
    },
    performance: {
      cacheEnabled: true,
      virtualizationEnabled: true,
      backgroundProcessing: true,
      workerPoolSize: 3,
      cacheStats: {
        hitRate: 0,
        missRate: 0,
        cacheSize: 0
      }
    }
  });

  // Initialize enhanced state management components
  const [historyManager] = useState(() => new HistoryManager(50));
  const [canUndo, setCanUndo] = useState(false);
  const [canRedo, setCanRedo] = useState(false);
  const [optimisticUpdateManager] = useState(() => new OptimisticUpdateManager(pipelineState));
  const [updateIndicatorManager] = useState(() => new UpdateIndicatorManager());
  const [multiLevelCache] = useState(() => new MultiLevelCache());
  const [stateOptimizer] = useState(() => new StateOptimizer(pipelineState));
  const [performanceMonitor] = useState(() => new StatePerformanceMonitor());

  // Update undo/redo state
  useEffect(() => {
    setCanUndo(historyManager.canUndo());
    setCanRedo(historyManager.canRedo());
  }, [historyManager, pipelineState]);

  // Session persistence functions
  const saveSession = (data: any) => {
    try {
      localStorage.setItem('datacenter_session_v2', JSON.stringify(data));
    } catch (error) {
      console.warn('Failed to save session:', error);
    }
  };

  const loadSession = () => {
    try {
      const data = localStorage.getItem('datacenter_session_v2');
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.warn('Failed to load session:', error);
      return null;
    }
  };

  const createSavePoint = (name: string, description?: string) => {
    const savePoint = {
      id: Math.random().toString(36).substr(2, 9),
      name,
      timestamp: new Date(),
      state: JSON.parse(JSON.stringify(pipelineState)), // Deep clone
      description
    };
    
    updatePipelineState({
      savePoints: [...pipelineState.savePoints, savePoint]
    });
  };

  const loadSavePoint = (id: string) => {
    const savePoint = pipelineState.savePoints.find(sp => sp.id === id);
    if (savePoint) {
      // Restore state but keep the savePoints array
      const restoredState = {
        ...savePoint.state,
        savePoints: pipelineState.savePoints
      };
      setPipelineState(restoredState as DataPipelineState);
      historyManager.push('load_savepoint', restoredState);
    }
  };

  const deleteSavePoint = (id: string) => {
    updatePipelineState({
      savePoints: pipelineState.savePoints.filter(sp => sp.id !== id)
    });
  };

  // Update filters and persist them
  const updateFilters = (newFilters: Record<string, any>) => {
    const updatedFilters = { ...pipelineState.filters, ...newFilters };
    updatePipelineState({ filters: updatedFilters });
    
    // Also update active filters in context
    const updateContext = (contextUpdates: Partial<DataPipelineState['context']>) => {
      updatePipelineState({
        context: {
          ...pipelineState.context,
          ...contextUpdates
        }
      });
    };
    
    updateContext({ activeFilters: updatedFilters });
  };

  // Clear specific filters
  const clearFilters = (filterKeys?: string[]) => {
    const updateContext = (contextUpdates: Partial<DataPipelineState['context']>) => {
      updatePipelineState({
        context: {
          ...pipelineState.context,
          ...contextUpdates
        }
      });
    };
    
    if (filterKeys) {
      const updatedFilters = { ...pipelineState.filters };
      filterKeys.forEach(key => delete updatedFilters[key]);
      updatePipelineState({ filters: updatedFilters });
      updateContext({ activeFilters: updatedFilters });
    } else {
      updatePipelineState({ filters: {} });
      updateContext({ activeFilters: {} });
    }
  };

  // Update context function
  const updateContext = (contextUpdates: Partial<DataPipelineState['context']>) => {
    updatePipelineState({
      context: {
        ...pipelineState.context,
        ...contextUpdates
      }
    });
  };

  // Add to breadcrumb function
  const addToBreadcrumb = (stage: string, label: string, dataSummary?: any) => {
    const breadcrumbEntry = {
      stage,
      label,
      timestamp: new Date(),
      dataSummary
    };
    
    updateContext({
      breadcrumbPath: [...pipelineState.context.breadcrumbPath, breadcrumbEntry]
    });
  };

  const updatePipelineState = (updates: Partial<DataPipelineState>, notifyParent: boolean = true): Promise<boolean> => {
    return new Promise((resolve) => {
      setPipelineState(prev => {
        const newState = { ...prev, ...updates };
        
        // Auto-add to history if stage changes
        if (updates.currentStage && updates.currentStage !== prev.currentStage) {
          const historyEntry = {
            stage: updates.currentStage,
            timestamp: new Date(),
            status: 'completed' as const,
            details: `Navigated to ${updates.currentStage} stage`
          };
          
          newState.pipelineHistory = [...prev.pipelineHistory, historyEntry];
        }
        
        // Add to history manager for undo/redo
        historyManager.push('state_update', newState);
        
        // Save to session storage
        saveSession({ pipelineState: newState });
        
        // Notify parent component if callback exists and notifyParent is true
        if (onDataChange && notifyParent) {
          onDataChange(updates);
        }
        
        // Validate state after update
        const validationResult = validateDataPipelineState(newState);
        if (!validationResult.valid && validationResult.errors) {
          newState.validationErrors = validationResult.errors;
        } else {
          newState.validationErrors = undefined;
        }
        
        resolve(validationResult.valid);
        return newState;
      });
    });
  };

  const resetPipeline = () => {
    const resetState: DataPipelineState = {
      fileName: '',
      fileType: '',
      fileSize: 0,
      uploadDate: new Date(),
      dataPreview: null,
      processingResult: null,
      pythonScript: '',
      pythonOutput: '',
      selectedTemplate: '',
      sqlResult: null,
      currentStage: 'upload',
      pipelineHistory: [],
      storageFile: null,
      preferences: {
        viewMode: 'grid',
        theme: 'system',
        showSchema: true,
        autoRefresh: true
      },
      filters: {},
      savePoints: [],
      context: {
        breadcrumbPath: [],
        currentData: null,
        activeFilters: {},
        processingMetadata: {}
      },
      performance: {
        cacheEnabled: true,
        virtualizationEnabled: true,
        backgroundProcessing: true,
        workerPoolSize: 3,
        cacheStats: {
          hitRate: 0,
          missRate: 0,
          cacheSize: 0
        }
      }
    };
    
    setPipelineState(resetState);
    historyManager.push('reset_pipeline', resetState);
    
    // Save to session storage
    saveSession({ pipelineState: resetState });
  };

  const navigateToStage = (stage: DataPipelineState['currentStage']) => {
    updatePipelineState({ currentStage: stage });
  };

  const undo = () => {
    const previous = historyManager.undo();
    if (previous) {
      setPipelineState(previous.state);
    }
  };

  const redo = () => {
    const next = historyManager.redo();
    if (next) {
      setPipelineState(next.state);
    }
  };

  // Performance optimization functions
  const enableCaching = (enabled: boolean) => {
    updatePipelineState({
      performance: {
        ...pipelineState.performance,
        cacheEnabled: enabled
      }
    });
  };

  const enableVirtualization = (enabled: boolean) => {
    updatePipelineState({
      performance: {
        ...pipelineState.performance,
        virtualizationEnabled: enabled
      }
    });
  };

  const updateWorkerPoolSize = (size: number) => {
    updatePipelineState({
      performance: {
        ...pipelineState.performance,
        workerPoolSize: Math.max(1, Math.min(10, size)) // Limit between 1-10 workers
      }
    });
  };

  const getCacheStats = () => {
    const stats = dataCache.getStats();
    return {
      hitRate: stats.hitRate,
      missRate: 1 - stats.hitRate,
      cacheSize: stats.size
    };
  };

  const clearCache = () => {
    dataCache.clear();
    processedDataCache.clear();
    persistentCache.clear();
    multiLevelCache.clear();
    
    // Update cache stats
    updatePipelineState({
      performance: {
        ...pipelineState.performance,
        cacheStats: {
          hitRate: 0,
          missRate: 0,
          cacheSize: 0
        }
      }
    });
  };

  // Enhanced state management functions
  const validateState = (): { valid: boolean; errors?: ValidationError[] } => {
    return validateDataPipelineState(pipelineState);
  };

  const validatePartialState = (partialState: Partial<DataPipelineState>): { valid: boolean; errors?: ValidationError[] } => {
    return validatePartialState(partialState);
  };

  const applyOptimisticUpdate = async (action: string, updates: Partial<DataPipelineState>): Promise<string> => {
    // Create rollback state
    const rollbackState = { ...pipelineState };
    
    // Apply updates optimistically
    await updatePipelineState({ 
      ...updates,
      isOptimisticUpdatePending: true
    });
    
    // Apply optimistic update to manager
    const updateId = optimisticUpdateManager.applyOptimisticUpdate(
      action,
      { ...pipelineState, ...updates, isOptimisticUpdatePending: true },
      rollbackState
    );
    
    // Update state with the update ID
    updatePipelineState({
      lastOptimisticUpdateId: updateId
    } as Partial<DataPipelineState>);
    
    return updateId;
  };

  const confirmOptimisticUpdate = (id: string): boolean => {
    const result = optimisticUpdateManager.confirmUpdate(id);
    if (result) {
      updatePipelineState({
        isOptimisticUpdatePending: false,
        lastOptimisticUpdateId: undefined
      } as Partial<DataPipelineState>);
    }
    return result;
  };

  const rollbackOptimisticUpdate = (id: string, error?: string): boolean => {
    const result = optimisticUpdateManager.handleUpdateFailure(id, error);
    if (result) {
      updatePipelineState({
        isOptimisticUpdatePending: false,
        lastOptimisticUpdateId: undefined
      } as Partial<DataPipelineState>);
    }
    return result;
  };

  const getPendingUpdates = (): Array<{ id: string; action: string; timestamp: number; status: string }> => {
    return optimisticUpdateManager.getPendingUpdates();
  };

  const getFailedUpdates = (): Array<{ id: string; action: string; timestamp: number; status: string; error?: string }> => {
    return optimisticUpdateManager.getFailedUpdates().map(update => ({
      id: update.id,
      action: update.action,
      timestamp: update.timestamp,
      status: update.status,
      error: update.error
    }));
  };

  const clearCompletedOptimisticUpdates = (): number => {
    return optimisticUpdateManager.clearCompletedUpdates();
  };

  const getCache = (key: string): any => {
    return multiLevelCache.get(key);
  };

  const setCache = (key: string, data: any, options?: { ttl?: number; tags?: string[] }): void => {
    multiLevelCache.set(key, data, options);
  };

  const invalidateCacheByTag = (tag: string): number => {
    return multiLevelCache.invalidateByTag(tag);
  };

  const getPerformanceStats = (): Record<string, any> => {
    return performanceMonitor.getStats();
  };

  const measurePerformance = <T,>(operation: string, fn: () => T): T => {
    return performanceMonitor.measure(operation, fn);
  };

  const getSnapshot = (): string => {
    // Return a JSON string representation of the current state
    return JSON.stringify(pipelineState);
  };

  const restoreSnapshot = (id: string): boolean => {
    // This would normally restore from a snapshot manager
    // For now, we'll just return true to indicate the function exists
    return true;
  };

  const logStateChange = (action: string, path: string[], oldValue: any, newValue: any): void => {
    // This would normally log to a state change logger
    // For now, we'll just log to console
    console.log(`State change: ${action}`, { path, oldValue, newValue });
  };

  return (
    <DataContext.Provider value={{ 
      pipelineState, 
      updatePipelineState, 
      resetPipeline,
      navigateToStage,
      updateContext,
      addToBreadcrumb,
      createSavePoint,
      loadSavePoint,
      deleteSavePoint,
      updateFilters,
      clearFilters,
      historyManager,
      canUndo,
      canRedo,
      undo,
      redo,
      enableCaching,
      enableVirtualization,
      updateWorkerPoolSize,
      getCacheStats,
      clearCache,
      validateState,
      validatePartialState,
      applyOptimisticUpdate,
      confirmOptimisticUpdate,
      rollbackOptimisticUpdate,
      getPendingUpdates,
      getFailedUpdates,
      clearCompletedOptimisticUpdates,
      getCache,
      setCache,
      invalidateCacheByTag,
      getPerformanceStats,
      measurePerformance,
      getSnapshot,
      restoreSnapshot,
      logStateChange
    }}>
      {children}
    </DataContext.Provider>
  );
};

export const useEnhancedDataCenter = () => {
  const context = useContext(DataContext);
  if (context === undefined) {
    throw new Error('useEnhancedDataCenter must be used within a EnhancedDataCenterProvider');
  }
  return context;
};