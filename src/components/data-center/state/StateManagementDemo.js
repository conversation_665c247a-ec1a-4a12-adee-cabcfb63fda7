// Simple demo script to showcase the enhanced state management features
// This file demonstrates how to use the new state management system

// Import the core components
import { DataPipelineStateSchema, validateDataPipelineState } from './StateSchema';
import { OptimisticUpdateManager } from './OptimisticUpdates';
import { MultiLevelCache } from './StateCache';
import { StateOptimizer } from './StateOptimizer';

// Mock initial state for demonstration
const mockInitialState = {
  fileName: 'demo.csv',
  fileType: 'csv',
  fileSize: 2048,
  uploadDate: new Date(),
  dataPreview: {
    headers: ['id', 'name', 'email'],
    rows: [
      [1, '<PERSON>', '<EMAIL>'],
      [2, '<PERSON>', '<EMAIL>']
    ],
    totalRows: 2
  },
  processingResult: null,
  pythonScript: '',
  pythonOutput: '',
  selectedTemplate: '',
  sqlResult: null,
  currentStage: 'upload',
  pipelineHistory: [],
  storageFile: null,
  preferences: {
    viewMode: 'grid',
    theme: 'system',
    showSchema: true,
    autoRefresh: true
  },
  filters: {},
  savePoints: [],
  context: {
    breadcrumbPath: [],
    currentData: null,
    activeFilters: {},
    processingMetadata: {}
  },
  performance: {
    cacheEnabled: true,
    virtualizationEnabled: true,
    backgroundProcessing: true,
    workerPoolSize: 3,
    cacheStats: {
      hitRate: 0,
      missRate: 0,
      cacheSize: 0
    }
  }
};

// Demo function to showcase state validation
function demoStateValidation() {
  console.log('=== State Validation Demo ===');
  
  // Validate correct state
  const validResult = validateDataPipelineState(mockInitialState);
  console.log('Valid state validation:', validResult.valid);
  
  // Validate invalid state
  const invalidState = {
    ...mockInitialState,
    fileName: 123 // Invalid type
  };
  
  const invalidResult = validateDataPipelineState(invalidState);
  console.log('Invalid state validation:', invalidResult.valid);
  if (invalidResult.errors) {
    console.log('Validation errors:', invalidResult.errors.map(e => e.message));
  }
}

// Demo function to showcase optimistic updates
function demoOptimisticUpdates() {
  console.log('\n=== Optimistic Updates Demo ===');
  
  const manager = new OptimisticUpdateManager(mockInitialState);
  
  // Apply optimistic update
  console.log('Applying optimistic update...');
  const updateId = manager.applyOptimisticUpdate(
    'update_file',
    { ...mockInitialState, fileName: 'updated-demo.csv' },
    mockInitialState
  );
  
  console.log('Update ID:', updateId);
  console.log('Pending updates:', manager.getPendingUpdates().length);
  
  // Confirm update
  console.log('Confirming update...');
  const confirmed = manager.confirmUpdate(updateId);
  console.log('Update confirmed:', confirmed);
  console.log('Pending updates after confirmation:', manager.getPendingUpdates().length);
}

// Demo function to showcase caching
function demoCaching() {
  console.log('\n=== Caching Demo ===');
  
  const cache = new MultiLevelCache();
  
  // Set cache values
  console.log('Setting cache values...');
  cache.set('user-1', { name: 'John', email: '<EMAIL>' }, { ttl: 5000, tags: ['users'] });
  cache.set('user-2', { name: 'Jane', email: '<EMAIL>' }, { ttl: 5000, tags: ['users'] });
  cache.set('config', { theme: 'dark', language: 'en' }, { ttl: 30000, tags: ['config'] });
  
  console.log('Cache size:', cache.size());
  
  // Get cache values
  console.log('Getting cache values...');
  const user1 = cache.get('user-1');
  console.log('User 1:', user1);
  
  const config = cache.get('config');
  console.log('Config:', config);
  
  // Invalidate by tag
  console.log('Invalidating by tag "users"...');
  const invalidated = cache.invalidateByTag('users');
  console.log('Invalidated entries:', invalidated);
  console.log('Cache size after invalidation:', cache.size());
}

// Demo function to showcase state optimization
function demoStateOptimization() {
  console.log('\n=== State Optimization Demo ===');
  
  const optimizer = new StateOptimizer(mockInitialState);
  
  // Compute cached values
  console.log('Computing cached values...');
  const upperFileName = optimizer.computeState('upperFileName', () => {
    return mockInitialState.fileName.toUpperCase();
  }, ['fileName']);
  
  console.log('Uppercase filename:', upperFileName);
}

// Run all demos
function runAllDemos() {
  console.log('Starting Enhanced State Management Demos\n');
  
  demoStateValidation();
  demoOptimisticUpdates();
  demoCaching();
  demoStateOptimization();
  
  console.log('\n=== All Demos Completed ===');
}

// Export for use in other modules
export {
  demoStateValidation,
  demoOptimisticUpdates,
  demoCaching,
  demoStateOptimization,
  runAllDemos,
  mockInitialState
};