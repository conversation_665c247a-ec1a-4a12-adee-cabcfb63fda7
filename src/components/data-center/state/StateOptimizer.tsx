// State Optimization for Large Datasets in Data Center
interface SelectiveSubscription {
  selector: string;
  lastUpdated: number;
  dependencies: string[];
}

interface StateChunk<T> {
  id: string;
  data: T;
  lastAccessed: number;
  accessCount: number;
  dependencies: string[];
}

interface ComputedStateCache<T> {
  value: T;
  dependencies: string[];
  lastComputed: number;
  ttl: number;
}

interface StateDiff<T> {
  path: string[];
  oldValue: T;
  newValue: T;
  timestamp: number;
}

export class StateOptimizer<T extends Record<string, any>> {
  private state: T;
  private subscriptions: Map<string, SelectiveSubscription> = new Map();
  private chunks: Map<string, StateChunk<any>> = new Map();
  private computedCache: Map<string, ComputedStateCache<any>> = new Map();
  private stateHistory: StateDiff<any>[] = [];
  private maxHistorySize: number = 1000;
  private chunkSize: number = 1000; // Default chunk size

  constructor(initialState: T, options?: { maxHistorySize?: number; chunkSize?: number }) {
    this.state = { ...initialState };
    if (options?.maxHistorySize) this.maxHistorySize = options.maxHistorySize;
    if (options?.chunkSize) this.chunkSize = options.chunkSize;
  }

  // Selective state subscription
  subscribe<K extends keyof T>(selector: K, callback: (value: T[K]) => void): () => void {
    const subscriptionId = `${String(selector)}_${Date.now()}`;
    
    // Track subscription
    this.subscriptions.set(subscriptionId, {
      selector: String(selector),
      lastUpdated: Date.now(),
      dependencies: [String(selector)]
    });

    // Return unsubscribe function
    return () => {
      this.subscriptions.delete(subscriptionId);
    };
  }

  // Update state with selective notification
  updateState(updates: Partial<T>): void {
    const oldState = { ...this.state };
    this.state = { ...this.state, ...updates };

    // Record state changes
    Object.entries(updates).forEach(([key, newValue]) => {
      const oldValue = oldState[key as keyof T];
      if (oldValue !== newValue) {
        this.recordStateChange([key], oldValue, newValue);
      }
    });

    // Notify relevant subscribers
    this.notifySubscribers(updates);
  }

  // Get state value with subscription tracking
  getState<K extends keyof T>(key: K): T[K] {
    // Update subscription access time
    this.subscriptions.forEach((subscription, id) => {
      if (subscription.selector === String(key)) {
        subscription.lastUpdated = Date.now();
      }
    });

    return this.state[key];
  }

  // Chunk large data structures
  chunkData<K extends keyof T>(key: K, data: T[K] extends any[] ? T[K] : never): string[] {
    if (!Array.isArray(data)) {
      throw new Error('Chunking is only supported for array data');
    }

    const chunkIds: string[] = [];
    
    for (let i = 0; i < data.length; i += this.chunkSize) {
      const chunk = data.slice(i, i + this.chunkSize);
      const chunkId = `${String(key)}_chunk_${i / this.chunkSize}`;
      
      this.chunks.set(chunkId, {
        id: chunkId,
        data: chunk,
        lastAccessed: Date.now(),
        accessCount: 0,
        dependencies: [String(key)]
      });
      
      chunkIds.push(chunkId);
    }

    return chunkIds;
  }

  // Get chunked data
  getChunk(chunkId: string): any[] | null {
    const chunk = this.chunks.get(chunkId);
    if (!chunk) return null;

    chunk.lastAccessed = Date.now();
    chunk.accessCount++;

    return chunk.data;
  }

  // Computed state with memoization
  computeState<K>(
    key: string,
    computeFn: () => K,
    dependencies: string[] = []
  ): K {
    const cached = this.computedCache.get(key);
    const now = Date.now();

    // Check if cached value is still valid
    if (cached && now - cached.lastComputed < cached.ttl) {
      return cached.value;
    }

    // Compute new value
    const value = computeFn();
    
    // Cache the computed value
    this.computedCache.set(key, {
      value,
      dependencies,
      lastComputed: now,
      ttl: 300000 // 5 minutes default TTL
    });

    return value;
  }

  // State diffing for efficient updates
  diffStates(oldState: T, newState: T): StateDiff<any>[] {
    const diffs: StateDiff<any>[] = [];
    
    Object.keys(newState).forEach(key => {
      const oldValue = oldState[key as keyof T];
      const newValue = newState[key as keyof T];
      
      if (oldValue !== newValue) {
        diffs.push({
          path: [key],
          oldValue,
          newValue,
          timestamp: Date.now()
        });
      }
    });
    
    return diffs;
  }

  // Apply state diffs
  applyDiffs(diffs: StateDiff<any>[]): void {
    diffs.forEach(diff => {
      // For simple top-level changes
      if (diff.path.length === 1) {
        const key = diff.path[0];
        (this.state as any)[key] = diff.newValue;
      }
      // For nested changes, we would need a more complex implementation
    });
  }

  // Get state history
  getStateHistory(): StateDiff<any>[] {
    return [...this.stateHistory];
  }

  // Clear old state history
  clearOldHistory(maxAge: number = 3600000): number { // 1 hour default
    const now = Date.now();
    const oldLength = this.stateHistory.length;
    
    this.stateHistory = this.stateHistory.filter(
      diff => now - diff.timestamp < maxAge
    );
    
    return oldLength - this.stateHistory.length;
  }

  // Clear computed cache
  clearComputedCache(): void {
    this.computedCache.clear();
  }

  // Clear chunks
  clearChunks(): void {
    this.chunks.clear();
  }

  // Get optimizer statistics
  getStats(): {
    subscriptions: number;
    chunks: number;
    computedCache: number;
    history: number;
    chunkSize: number;
  } {
    return {
      subscriptions: this.subscriptions.size,
      chunks: this.chunks.size,
      computedCache: this.computedCache.size,
      history: this.stateHistory.length,
      chunkSize: this.chunkSize
    };
  }

  // Notify subscribers of relevant changes
  private notifySubscribers(updates: Partial<T>): void {
    Object.keys(updates).forEach(key => {
      this.subscriptions.forEach((subscription, id) => {
        if (subscription.selector === key) {
          // In a real implementation, this would trigger the actual callback
          // For now, we just update the lastUpdated time
          subscription.lastUpdated = Date.now();
        }
      });
    });
  }

  // Record state change for history
  private recordStateChange(path: string[], oldValue: any, newValue: any): void {
    this.stateHistory.push({
      path,
      oldValue,
      newValue,
      timestamp: Date.now()
    });

    // Trim history if it exceeds max size
    if (this.stateHistory.length > this.maxHistorySize) {
      this.stateHistory.shift();
    }
  }

  // Get stale subscriptions
  getStaleSubscriptions(maxAge: number = 300000): string[] { // 5 minutes default
    const now = Date.now();
    const stale: string[] = [];
    
    this.subscriptions.forEach((subscription, id) => {
      if (now - subscription.lastUpdated > maxAge) {
        stale.push(id);
      }
    });
    
    return stale;
  }

  // Remove stale subscriptions
  cleanupStaleSubscriptions(maxAge: number = 300000): number {
    const stale = this.getStaleSubscriptions(maxAge);
    stale.forEach(id => this.subscriptions.delete(id));
    return stale.length;
  }
}

// Performance monitoring for state operations
export class StatePerformanceMonitor {
  private operationTimings: Map<string, number[]> = new Map();
  private operationCounts: Map<string, number> = new Map();

  // Measure operation performance
  measure<T>(operation: string, fn: () => T): T {
    const start = performance.now();
    try {
      const result = fn();
      const end = performance.now();
      this.recordTiming(operation, end - start);
      return result;
    } catch (error) {
      const end = performance.now();
      this.recordTiming(operation, end - start);
      throw error;
    }
  }

  // Record timing for an operation
  private recordTiming(operation: string, time: number): void {
    if (!this.operationTimings.has(operation)) {
      this.operationTimings.set(operation, []);
    }
    
    const timings = this.operationTimings.get(operation)!;
    timings.push(time);
    
    // Keep only last 100 timings
    if (timings.length > 100) {
      timings.shift();
    }
    
    // Update operation count
    this.operationCounts.set(operation, (this.operationCounts.get(operation) || 0) + 1);
  }

  // Get performance statistics
  getStats(): Record<string, {
    count: number;
    averageTime: number;
    minTime: number;
    maxTime: number;
    totalTime: number;
  }> {
    const stats: Record<string, any> = {};
    
    this.operationTimings.forEach((timings, operation) => {
      const count = this.operationCounts.get(operation) || 0;
      const totalTime = timings.reduce((sum, time) => sum + time, 0);
      const averageTime = totalTime / timings.length;
      const minTime = Math.min(...timings);
      const maxTime = Math.max(...timings);
      
      stats[operation] = {
        count,
        averageTime,
        minTime,
        maxTime,
        totalTime
      };
    });
    
    return stats;
  }

  // Clear statistics
  clearStats(): void {
    this.operationTimings.clear();
    this.operationCounts.clear();
  }
}

// Selective state subscription hook (for React integration)
export class SelectiveStateSubscription<T> {
  private subscriptions: Map<string, Set<keyof T>> = new Map();
  private callbacks: Map<string, (state: Partial<T>) => void> = new Map();

  // Subscribe to specific state keys
  subscribe(
    id: string,
    keys: (keyof T)[],
    callback: (state: Partial<T>) => void
  ): () => void {
    this.subscriptions.set(id, new Set(keys));
    this.callbacks.set(id, callback);
    
    return () => {
      this.subscriptions.delete(id);
      this.callbacks.delete(id);
    };
  }

  // Notify subscribers of relevant changes
  notifySubscribers(state: T, changedKeys: (keyof T)[]): void {
    const changedKeySet = new Set(changedKeys);
    
    this.subscriptions.forEach((subscribedKeys, id) => {
      // Check if any subscribed keys were changed
      const hasRelevantChanges = Array.from(subscribedKeys).some(key => 
        changedKeySet.has(key)
      );
      
      if (hasRelevantChanges) {
        const callback = this.callbacks.get(id);
        if (callback) {
          // Only pass the relevant state keys
          const relevantState = {} as Partial<T>;
          subscribedKeys.forEach(key => {
            (relevantState as any)[key] = state[key];
          });
          callback(relevantState);
        }
      }
    });
  }
}

// Export types
export type {
  SelectiveSubscription,
  StateChunk,
  ComputedStateCache,
  StateDiff
};