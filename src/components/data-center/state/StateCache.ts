// Intelligent Caching Strategies for Data Center State Management
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
  hits: number;
  lastAccessed: number;
  size: number; // Estimated size in bytes
  tags: string[]; // Tags for grouping and invalidation
}

interface CacheStats {
  hitRate: number;
  missRate: number;
  size: number;
  maxSize: number;
  totalHits: number;
  totalMisses: number;
  evictionCount: number;
  averageEntrySize: number;
}

interface CacheConfig {
  maxSize: number;
  defaultTTL: number;
  evictionStrategy: 'lru' | 'lfu' | 'fifo' | 'ttl';
  enableCompression: boolean;
  enablePersistence: boolean;
  persistenceKey?: string;
}

export class StateCache<T = any> {
  private cache: Map<string, CacheEntry<T>> = new Map();
  private config: CacheConfig;
  private stats: CacheStats;
  private evictionCount: number = 0;
  private totalEntrySize: number = 0;

  constructor(config?: Partial<CacheConfig>) {
    this.config = {
      maxSize: config?.maxSize || 1000,
      defaultTTL: config?.defaultTTL || 300000, // 5 minutes
      evictionStrategy: config?.evictionStrategy || 'lru',
      enableCompression: config?.enableCompression || false,
      enablePersistence: config?.enablePersistence || false,
      persistenceKey: config?.persistenceKey || 'state_cache'
    };

    this.stats = {
      hitRate: 0,
      missRate: 0,
      size: 0,
      maxSize: this.config.maxSize,
      totalHits: 0,
      totalMisses: 0,
      evictionCount: 0,
      averageEntrySize: 0
    };

    // Load from persistence if enabled
    if (this.config.enablePersistence) {
      this.loadFromStorage();
    }
  }

  // Set a value in the cache
  set(key: string, data: T, options?: { ttl?: number; tags?: string[]; size?: number }): string {
    // Check if we need to evict entries
    if (this.cache.size >= this.config.maxSize) {
      this.evictEntries();
    }

    const ttl = options?.ttl || this.config.defaultTTL;
    const tags = options?.tags || [];
    const size = options?.size || this.estimateSize(data);

    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl,
      hits: 0,
      lastAccessed: Date.now(),
      size,
      tags
    };

    this.cache.set(key, entry);
    this.totalEntrySize += size;
    this.updateStats();

    // Save to persistence if enabled
    if (this.config.enablePersistence) {
      this.saveToStorage();
    }

    return key;
  }

  // Get a value from the cache
  get(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      this.stats.totalMisses++;
      this.updateStats();
      return null;
    }

    // Check if entry is expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      this.totalEntrySize -= entry.size;
      this.stats.totalMisses++;
      this.updateStats();
      return null;
    }

    // Update hit count and last accessed time
    entry.hits++;
    entry.lastAccessed = Date.now();
    this.stats.totalHits++;
    this.updateStats();

    return entry.data;
  }

  // Check if a key exists in the cache
  has(key: string): boolean {
    return this.get(key) !== null;
  }

  // Delete a key from the cache
  delete(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) return false;

    this.cache.delete(key);
    this.totalEntrySize -= entry.size;
    this.updateStats();

    // Save to persistence if enabled
    if (this.config.enablePersistence) {
      this.saveToStorage();
    }

    return true;
  }

  // Clear the entire cache
  clear(): void {
    this.cache.clear();
    this.totalEntrySize = 0;
    this.evictionCount = 0;
    this.updateStats();

    // Clear persistence if enabled
    if (this.config.enablePersistence) {
      try {
        localStorage.removeItem(this.config.persistenceKey!);
      } catch (error) {
        console.warn('Failed to clear persistent cache:', error);
      }
    }
  }

  // Get cache statistics
  getStats(): CacheStats {
    return { ...this.stats };
  }

  // Invalidate entries by tag
  invalidateByTag(tag: string): number {
    let count = 0;
    this.cache.forEach((entry, key) => {
      if (entry.tags.includes(tag)) {
        this.cache.delete(key);
        this.totalEntrySize -= entry.size;
        count++;
      }
    });
    this.updateStats();

    // Save to persistence if enabled
    if (this.config.enablePersistence) {
      this.saveToStorage();
    }

    return count;
  }

  // Invalidate entries by multiple tags
  invalidateByTags(tags: string[]): number {
    let count = 0;
    this.cache.forEach((entry, key) => {
      if (tags.some(tag => entry.tags.includes(tag))) {
        this.cache.delete(key);
        this.totalEntrySize -= entry.size;
        count++;
      }
    });
    this.updateStats();

    // Save to persistence if enabled
    if (this.config.enablePersistence) {
      this.saveToStorage();
    }

    return count;
  }

  // Warm cache with frequently accessed data
  async warmCache(
    warmer: (keys: string[]) => Promise<Record<string, T>>,
    keys: string[]
  ): Promise<void> {
    try {
      const data = await warmer(keys);
      Object.entries(data).forEach(([key, value]) => {
        this.set(key, value);
      });
    } catch (error) {
      console.warn('Cache warming failed:', error);
    }
  }

  // Get cache keys
  getKeys(): string[] {
    return Array.from(this.cache.keys());
  }

  // Get cache size
  size(): number {
    return this.cache.size;
  }

  // Get entry details for debugging
  getEntryDetails(key: string): {
    key: string;
    hits: number;
    age: number;
    expiresAt: number;
    size: number;
    tags: string[];
  } | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    return {
      key,
      hits: entry.hits,
      age: Date.now() - entry.timestamp,
      expiresAt: entry.timestamp + entry.ttl,
      size: entry.size,
      tags: [...entry.tags]
    };
  }

  // Evict entries based on strategy
  private evictEntries(): void {
    if (this.cache.size === 0) return;

    let keysToEvict: string[] = [];

    switch (this.config.evictionStrategy) {
      case 'lru': // Least Recently Used
        keysToEvict = this.getLRUKeys(1);
        break;
      case 'lfu': // Least Frequently Used
        keysToEvict = this.getLFUKeys(1);
        break;
      case 'fifo': // First In First Out
        keysToEvict = this.getFIFOKeys(1);
        break;
      case 'ttl': // Time To Live
        keysToEvict = this.getTTLOldestKeys(1);
        break;
    }

    keysToEvict.forEach(key => {
      const entry = this.cache.get(key);
      if (entry) {
        this.cache.delete(key);
        this.totalEntrySize -= entry.size;
        this.evictionCount++;
      }
    });

    this.updateStats();
  }

  // Get LRU keys
  private getLRUKeys(count: number): string[] {
    return Array.from(this.cache.entries())
      .sort(([, a], [, b]) => a.lastAccessed - b.lastAccessed)
      .slice(0, count)
      .map(([key]) => key);
  }

  // Get LFU keys
  private getLFUKeys(count: number): string[] {
    return Array.from(this.cache.entries())
      .sort(([, a], [, b]) => a.hits - b.hits)
      .slice(0, count)
      .map(([key]) => key);
  }

  // Get FIFO keys
  private getFIFOKeys(count: number): string[] {
    return Array.from(this.cache.entries())
      .sort(([, a], [, b]) => a.timestamp - b.timestamp)
      .slice(0, count)
      .map(([key]) => key);
  }

  // Get TTL oldest keys
  private getTTLOldestKeys(count: number): string[] {
    return Array.from(this.cache.entries())
      .sort(([, a], [, b]) => (a.timestamp + a.ttl) - (b.timestamp + b.ttl))
      .slice(0, count)
      .map(([key]) => key);
  }

  // Update cache statistics
  private updateStats(): void {
    const totalRequests = this.stats.totalHits + this.stats.totalMisses;
    this.stats.hitRate = totalRequests > 0 ? this.stats.totalHits / totalRequests : 0;
    this.stats.missRate = 1 - this.stats.hitRate;
    this.stats.size = this.cache.size;
    this.stats.maxSize = this.config.maxSize;
    this.stats.evictionCount = this.evictionCount;
    this.stats.averageEntrySize = this.cache.size > 0 ? this.totalEntrySize / this.cache.size : 0;
  }

  // Estimate size of data (simplified)
  private estimateSize(data: any): number {
    try {
      const json = JSON.stringify(data);
      return new Blob([json]).size;
    } catch (error) {
      return 0;
    }
  }

  // Load cache from localStorage
  private loadFromStorage(): void {
    try {
      const stored = localStorage.getItem(this.config.persistenceKey!);
      if (stored) {
        const parsed = JSON.parse(stored);
        this.cache = new Map(parsed);
        this.totalEntrySize = Array.from(this.cache.values()).reduce((sum, entry) => sum + entry.size, 0);
        this.updateStats();
      }
    } catch (error) {
      console.warn('Failed to load cache from storage:', error);
    }
  }

  // Save cache to localStorage
  private saveToStorage(): void {
    try {
      const serializable = Array.from(this.cache.entries());
      localStorage.setItem(this.config.persistenceKey!, JSON.stringify(serializable));
    } catch (error) {
      console.warn('Failed to save cache to storage:', error);
    }
  }
}

// Multi-level cache implementation
export class MultiLevelCache<T = any> {
  private memoryCache: StateCache<T>;
  private sessionCache: StateCache<T>;
  private persistentCache: StateCache<T>;

  constructor(
    memoryConfig?: Partial<CacheConfig>,
    sessionConfig?: Partial<CacheConfig>,
    persistentConfig?: Partial<CacheConfig>
  ) {
    // Memory cache - fastest, smallest
    this.memoryCache = new StateCache<T>({
      maxSize: memoryConfig?.maxSize || 100,
      defaultTTL: memoryConfig?.defaultTTL || 300000, // 5 minutes
      evictionStrategy: memoryConfig?.evictionStrategy || 'lru',
      enableCompression: memoryConfig?.enableCompression || false,
      enablePersistence: false,
      ...memoryConfig
    });

    // Session cache - medium speed, medium size
    this.sessionCache = new StateCache<T>({
      maxSize: sessionConfig?.maxSize || 500,
      defaultTTL: sessionConfig?.defaultTTL || 1800000, // 30 minutes
      evictionStrategy: sessionConfig?.evictionStrategy || 'lru',
      enableCompression: sessionConfig?.enableCompression || false,
      enablePersistence: false,
      ...sessionConfig
    });

    // Persistent cache - slowest, largest
    this.persistentCache = new StateCache<T>({
      maxSize: persistentConfig?.maxSize || 1000,
      defaultTTL: persistentConfig?.defaultTTL || 86400000, // 24 hours
      evictionStrategy: persistentConfig?.evictionStrategy || 'ttl',
      enableCompression: persistentConfig?.enableCompression || true,
      enablePersistence: true,
      persistenceKey: persistentConfig?.persistenceKey || 'datacenter_persistent_cache_v2',
      ...persistentConfig
    });
  }

  // Get value from cache hierarchy
  get(key: string): T | null {
    // Try memory cache first
    let result = this.memoryCache.get(key);
    if (result !== null) return result;

    // Try session cache
    result = this.sessionCache.get(key);
    if (result !== null) {
      // Promote to higher level cache
      this.memoryCache.set(key, result);
      return result;
    }

    // Try persistent cache
    result = this.persistentCache.get(key);
    if (result !== null) {
      // Promote to higher level caches
      this.sessionCache.set(key, result);
      this.memoryCache.set(key, result);
      return result;
    }

    return null;
  }

  // Set value in all cache levels
  set(key: string, data: T, options?: { ttl?: number; tags?: string[]; size?: number }): void {
    const opts = options || {};
    this.memoryCache.set(key, data, opts);
    this.sessionCache.set(key, data, opts);
    this.persistentCache.set(key, data, opts);
  }

  // Delete value from all cache levels
  delete(key: string): boolean {
    const result1 = this.memoryCache.delete(key);
    const result2 = this.sessionCache.delete(key);
    const result3 = this.persistentCache.delete(key);
    return result1 || result2 || result3;
  }

  // Clear all cache levels
  clear(): void {
    this.memoryCache.clear();
    this.sessionCache.clear();
    this.persistentCache.clear();
  }

  // Invalidate by tag in all cache levels
  invalidateByTag(tag: string): number {
    const count1 = this.memoryCache.invalidateByTag(tag);
    const count2 = this.sessionCache.invalidateByTag(tag);
    const count3 = this.persistentCache.invalidateByTag(tag);
    return count1 + count2 + count3;
  }

  // Get statistics from all cache levels
  getStats(): {
    memory: CacheStats;
    session: CacheStats;
    persistent: CacheStats;
  } {
    return {
      memory: this.memoryCache.getStats(),
      session: this.sessionCache.getStats(),
      persistent: this.persistentCache.getStats()
    };
  }
}

// Export types
export type {
  CacheEntry,
  CacheStats,
  CacheConfig
};