import { z } from 'zod';
import { DataPipelineState } from '../DataCenterContext';

// Schema for DataPreview
export const DataPreviewSchema = z.object({
  headers: z.array(z.string()),
  rows: z.array(z.array(z.any())),
  totalRows: z.number(),
  dataTypes: z.record(z.string()).optional(),
  statistics: z.record(z.any()).optional(),
  fullData: z.array(z.array(z.any())).optional(),
});

// Schema for ProcessingResult
export const ProcessingResultSchema = z.object({
  content: z.string(),
  metadata: z.record(z.any()),
  chunks: z.array(z.object({
    text: z.string(),
    metadata: z.record(z.any()),
  })).optional(),
  embeddings: z.array(z.array(z.number())).optional(),
});

// Schema for SQLGenerationResult
export const SQLGenerationResultSchema = z.object({
  sql: z.string(),
  isValid: z.boolean(),
  error: z.string().optional(),
  analysis: z.string().optional(),
});

// Schema for Pipeline History items
export const PipelineHistoryItemSchema = z.object({
  stage: z.string(),
  timestamp: z.date(),
  status: z.enum(['completed', 'pending', 'error']),
  details: z.string().optional(),
});

// Schema for Save Points
export const SavePointSchema = z.object({
  id: z.string(),
  name: z.string(),
  timestamp: z.date(),
  state: z.record(z.any()), // This will be validated separately
  description: z.string().optional(),
});

// Schema for Breadcrumb Path items
export const BreadcrumbItemSchema = z.object({
  stage: z.string(),
  label: z.string(),
  timestamp: z.date(),
  dataSummary: z.object({
    rows: z.number().optional(),
    columns: z.number().optional(),
    fileType: z.string().optional(),
  }).optional(),
});

// Schema for User Preferences
export const UserPreferencesSchema = z.object({
  viewMode: z.enum(['grid', 'list', 'tree']),
  theme: z.enum(['light', 'dark', 'system']),
  showSchema: z.boolean(),
  autoRefresh: z.boolean(),
  sidebarCollapsed: z.boolean().optional(),
  lastActiveView: z.string().optional(),
  favorites: z.array(z.string()).optional(),
});

// Schema for Performance Settings
export const PerformanceSettingsSchema = z.object({
  cacheEnabled: z.boolean(),
  virtualizationEnabled: z.boolean(),
  backgroundProcessing: z.boolean(),
  workerPoolSize: z.number().min(1).max(10),
  cacheStats: z.object({
    hitRate: z.number(),
    missRate: z.number(),
    cacheSize: z.number(),
  }),
});

// Main DataPipelineState schema
const dataPipelineStateSchemaInternal = z.object({
  fileName: z.string(),
  fileType: z.string(),
  fileSize: z.number(),
  uploadDate: z.date(),
  dataPreview: DataPreviewSchema.nullable(),
  processingResult: ProcessingResultSchema.nullable(),
  pythonScript: z.string(),
  pythonOutput: z.string(),
  selectedTemplate: z.string(),
  sqlResult: SQLGenerationResultSchema.nullable(),
  currentStage: z.enum(['upload', 'preview', 'explore', 'processing', 'analysis', 'sql']),
  pipelineHistory: z.array(PipelineHistoryItemSchema),
  storageFile: z.any().nullable(), // StorageFile type from browserStorageManager
  preferences: UserPreferencesSchema,
  filters: z.record(z.any()),
  savePoints: z.array(SavePointSchema),
  context: z.object({
    breadcrumbPath: z.array(BreadcrumbItemSchema),
    currentData: z.any(),
    activeFilters: z.record(z.any()),
    processingMetadata: z.record(z.any()),
  }),
  performance: PerformanceSettingsSchema,
});

// Schema for validation errors
export const ValidationErrorSchema = z.object({
  path: z.array(z.string()),
  message: z.string(),
  code: z.string(),
});

// Type definitions
export type DataPreview = z.infer<typeof DataPreviewSchema>;
export type ProcessingResult = z.infer<typeof ProcessingResultSchema>;
export type SQLGenerationResult = z.infer<typeof SQLGenerationResultSchema>;
export type PipelineHistoryItem = z.infer<typeof PipelineHistoryItemSchema>;
export type SavePoint = z.infer<typeof SavePointSchema>;
export type BreadcrumbItem = z.infer<typeof BreadcrumbItemSchema>;
export type UserPreferences = z.infer<typeof UserPreferencesSchema>;
export type PerformanceSettings = z.infer<typeof PerformanceSettingsSchema>;
export type ValidationError = z.infer<typeof ValidationErrorSchema>;

// Validation functions
export const validateDataPipelineState = (state: any): { valid: boolean; errors?: ValidationError[] } => {
  try {
    dataPipelineStateSchemaInternal.parse(state);
    return { valid: true };
  } catch (error: any) {
    if (error instanceof z.ZodError) {
      const errors: ValidationError[] = error.errors.map(err => ({
        path: err.path.map(String),
        message: err.message,
        code: err.code,
      }));
      return { valid: false, errors };
    }
    return { valid: false, errors: [{ path: [], message: 'Unknown validation error', code: 'UNKNOWN' }] };
  }
};

export const validatePartialState = (partialState: Partial<DataPipelineState>): { valid: boolean; errors?: ValidationError[] } => {
  try {
    // For partial updates, we need to validate each field individually
    const errors: ValidationError[] = [];
    
    Object.entries(partialState).forEach(([key, value]) => {
      try {
        const schema = dataPipelineStateSchemaInternal.shape[key as keyof DataPipelineState];
        if (schema) {
          schema.parse(value);
        }
      } catch (error: any) {
        if (error instanceof z.ZodError) {
          error.errors.forEach(err => {
            errors.push({
              path: [key, ...err.path.map(String)],
              message: err.message,
              code: err.code,
            });
          });
        }
      }
    });
    
    return errors.length > 0 ? { valid: false, errors } : { valid: true };
  } catch (error) {
    return { valid: false, errors: [{ path: [], message: 'Unknown validation error', code: 'UNKNOWN' }] };
  }
};

// Schema validation utilities
export class SchemaValidator {
  static validateState(state: any): { valid: boolean; errors?: ValidationError[] } {
    return validateDataPipelineState(state);
  }
  
  static validatePartialState(partialState: Partial<DataPipelineState>): { valid: boolean; errors?: ValidationError[] } {
    return validatePartialState(partialState);
  }
  
  static formatErrors(errors: ValidationError[]): string {
    return errors.map(error => 
      `Path: ${error.path.join('.')}, Message: ${error.message}, Code: ${error.code}`
    ).join('\n');
  }
  
  static hasErrors(errors: ValidationError[] | undefined): boolean {
    return !!errors && errors.length > 0;
  }
}

// Export Zod schemas for custom validation
export {
  z,
  dataPipelineStateSchemaInternal as DataPipelineStateSchema,
};