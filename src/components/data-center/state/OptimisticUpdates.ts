// Optimistic Updates System for Data Center State Management
interface OptimisticUpdateInternal<T> {
  id: string;
  action: string;
  optimisticState: T;
  rollbackState: T;
  timestamp: number;
  status: 'pending' | 'completed' | 'failed';
  error?: string;
}

interface PendingUpdateInternal {
  id: string;
  action: string;
  timestamp: number;
  status: 'pending' | 'completed' | 'failed';
  error?: string;
}

export class OptimisticUpdateManager<T> {
  private updates: Map<string, OptimisticUpdateInternal<T>> = new Map();
  private maxUpdates = 100;
  private onStateChange?: (state: T) => void;
  private onError?: (error: string, update: OptimisticUpdateInternal<T>) => void;

  constructor(
    private currentState: T,
    options?: {
      maxUpdates?: number;
      onStateChange?: (state: T) => void;
      onError?: (error: string, update: OptimisticUpdateInternal<T>) => void;
    }
  ) {
    if (options?.maxUpdates) this.maxUpdates = options.maxUpdates;
    if (options?.onStateChange) this.onStateChange = options.onStateChange;
    if (options?.onError) this.onError = options.onError;
  }

  // Apply an optimistic update
  applyOptimisticUpdate(
    action: string,
    optimisticState: T,
    rollbackState: T
  ): string {
    const id = this.generateId();
    const update: OptimisticUpdateInternal<T> = {
      id,
      action,
      optimisticState,
      rollbackState,
      timestamp: Date.now(),
      status: 'pending'
    };

    // Apply the optimistic state immediately
    this.currentState = optimisticState;
    this.onStateChange?.(optimisticState);

    // Store the update
    this.updates.set(id, update);

    // Clean up old updates if we exceed the limit
    this.cleanupOldUpdates();

    return id;
  }

  // Confirm that an update was successful
  confirmUpdate(id: string): boolean {
    const update = this.updates.get(id);
    if (!update) return false;

    update.status = 'completed';
    return true;
  }

  // Handle update failure and rollback
  handleUpdateFailure(id: string, error?: string): boolean {
    const update = this.updates.get(id);
    if (!update) return false;

    // Mark as failed
    update.status = 'failed';
    update.error = error;

    // Rollback to previous state
    this.currentState = update.rollbackState;
    this.onStateChange?.(update.rollbackState);

    // Notify error handler
    this.onError?.(error || 'Update failed', update);

    return true;
  }

  // Get pending updates for UI indicators
  getPendingUpdates(): PendingUpdateInternal[] {
    return Array.from(this.updates.values())
      .filter(update => update.status === 'pending')
      .map(({ id, action, timestamp, status }) => ({
        id,
        action,
        timestamp,
        status
      }));
  }

  // Get failed updates for user feedback
  getFailedUpdates(): OptimisticUpdateInternal<T>[] {
    return Array.from(this.updates.values())
      .filter(update => update.status === 'failed');
  }

  // Clear completed updates
  clearCompletedUpdates(): number {
    let count = 0;
    this.updates.forEach((update, id) => {
      if (update.status === 'completed') {
        this.updates.delete(id);
        count++;
      }
    });
    return count;
  }

  // Get current state
  getCurrentState(): T {
    return this.currentState;
  }

  // Set current state (for external updates)
  setCurrentState(state: T): void {
    this.currentState = state;
  }

  // Get update by ID
  getUpdate(id: string): OptimisticUpdateInternal<T> | undefined {
    return this.updates.get(id);
  }

  // Get all updates
  getAllUpdates(): OptimisticUpdateInternal<T>[] {
    return Array.from(this.updates.values());
  }

  // Clear all updates
  clearAllUpdates(): void {
    this.updates.clear();
  }

  // Retry a failed update
  retryUpdate(id: string): boolean {
    const update = this.updates.get(id);
    if (!update || update.status !== 'failed') return false;

    // Reset to pending
    update.status = 'pending';
    update.error = undefined;

    // Re-apply the optimistic state
    this.currentState = update.optimisticState;
    this.onStateChange?.(update.optimisticState);

    return true;
  }

  // Generate unique ID for updates
  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  // Clean up old updates to prevent memory leaks
  private cleanupOldUpdates(): void {
    if (this.updates.size <= this.maxUpdates) return;

    // Convert to array and sort by timestamp
    const updatesArray = Array.from(this.updates.entries())
      .sort(([, a], [, b]) => a.timestamp - b.timestamp);

    // Remove oldest entries
    const excess = updatesArray.length - this.maxUpdates;
    for (let i = 0; i < excess; i++) {
      const [id] = updatesArray[i];
      this.updates.delete(id);
    }
  }

  // Get update statistics
  getStats(): {
    total: number;
    pending: number;
    completed: number;
    failed: number;
  } {
    const updates = Array.from(this.updates.values());
    return {
      total: updates.length,
      pending: updates.filter(u => u.status === 'pending').length,
      completed: updates.filter(u => u.status === 'completed').length,
      failed: updates.filter(u => u.status === 'failed').length
    };
  }
}

// Conflict resolution utilities
export class ConflictResolver {
  // Resolve conflicts between concurrent updates
  static resolveConflicts<T>(
    currentState: T,
    updates: Array<{ id: string; update: Partial<T> }>
  ): T {
    // Simple merge strategy - apply updates in order
    // In a real implementation, this would be more sophisticated
    let resolvedState = { ...currentState as any };
    
    updates.forEach(({ update }) => {
      resolvedState = { ...resolvedState, ...update };
    });
    
    return resolvedState as T;
  }

  // Detect conflicts between updates
  static detectConflicts<T>(
    updates: Array<{ id: string; update: Partial<T> }>
  ): string[] {
    // Simple conflict detection - check if same keys are being updated
    const keyUpdates: Record<string, string[]> = {};
    const conflicts: string[] = [];
    
    updates.forEach(({ id, update }) => {
      Object.keys(update as any).forEach(key => {
        if (!keyUpdates[key]) {
          keyUpdates[key] = [];
        }
        keyUpdates[key].push(id);
        
        // If multiple updates affect the same key, it's a conflict
        if (keyUpdates[key].length > 1) {
          conflicts.push(...keyUpdates[key]);
        }
      });
    });
    
    // Return unique conflict IDs
    return [...new Set(conflicts)];
  }
}

// Pending state indicators
interface UpdateIndicatorInternal {
  id: string;
  action: string;
  message: string;
  progress?: number;
  timestamp: number;
}

export class UpdateIndicatorManager {
  private indicators: Map<string, UpdateIndicatorInternal> = new Map();

  showIndicator(id: string, action: string, message: string, progress?: number): void {
    this.indicators.set(id, {
      id,
      action,
      message,
      progress,
      timestamp: Date.now()
    });
  }

  updateProgress(id: string, progress: number): boolean {
    const indicator = this.indicators.get(id);
    if (!indicator) return false;
    
    indicator.progress = progress;
    return true;
  }

  hideIndicator(id: string): boolean {
    return this.indicators.delete(id);
  }

  getIndicators(): UpdateIndicatorInternal[] {
    return Array.from(this.indicators.values())
      .sort((a, b) => b.timestamp - a.timestamp);
  }

  clearOldIndicators(maxAge = 300000): number { // 5 minutes default
    const now = Date.now();
    let count = 0;
    
    this.indicators.forEach((indicator, id) => {
      if (now - indicator.timestamp > maxAge) {
        this.indicators.delete(id);
        count++;
      }
    });
    
    return count;
  }
}

// Export types for external use
export type OptimisticUpdate<T> = OptimisticUpdateInternal<T>;
export type PendingUpdate = PendingUpdateInternal;
export type UpdateIndicator = UpdateIndicatorInternal;