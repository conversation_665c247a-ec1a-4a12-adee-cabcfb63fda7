import React, { useState, useEffect } from 'react';
import { useDataCenter } from '../DataCenterContext';
import { SchemaValidator, ValidationError } from './StateSchema';

// State debugging panel component
export const StateDebugger: React.FC<{
  className?: string;
  show?: boolean;
}> = ({ className = '', show = false }) => {
  const { pipelineState, historyManager } = useDataCenter();
  const [isVisible, setIsVisible] = useState(show);
  const [activeTab, setActiveTab] = useState('state');
  const [validationErrors, setValidationErrors] = useState<ValidationError[]>([]);
  const [history, setHistory] = useState<any[]>([]);
  const [performanceStats, setPerformanceStats] = useState<Record<string, any>>({});

  // Validate state on change
  useEffect(() => {
    const result = SchemaValidator.validateState(pipelineState);
    if (!result.valid && result.errors) {
      setValidationErrors(result.errors);
    } else {
      setValidationErrors([]);
    }
  }, [pipelineState]);

  // Get history on change
  useEffect(() => {
    setHistory(historyManager.getHistory());
  }, [historyManager]);

  // Mock performance stats (in a real implementation, this would connect to the actual performance monitor)
  useEffect(() => {
    setPerformanceStats({
      'state-updates': { count: 15, averageTime: 2.3, totalTime: 34.5 },
      'validation': { count: 15, averageTime: 1.1, totalTime: 16.5 },
      'rendering': { count: 42, averageTime: 8.7, totalTime: 365.4 }
    });
  }, []);

  const toggleVisibility = () => {
    setIsVisible(!isVisible);
  };

  const exportState = () => {
    const dataStr = JSON.stringify(pipelineState, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `datacenter-state-${new Date().toISOString()}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  const copyState = () => {
    navigator.clipboard.writeText(JSON.stringify(pipelineState, null, 2));
  };

  if (!isVisible) {
    return (
      <button
        onClick={toggleVisibility}
        className="fixed bottom-4 right-4 z-50 bg-blue-500 text-white p-2 rounded-full shadow-lg hover:bg-blue-600 transition-colors"
        aria-label="Open State Debugger"
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
        </svg>
      </button>
    );
  }

  return (
    <div className={`fixed bottom-4 right-4 z-50 w-full max-w-md ${className}`}>
      <div className="bg-card border rounded-lg shadow-lg">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <h3 className="font-semibold">State Debugger</h3>
          <div className="flex gap-2">
            <button
              onClick={exportState}
              className="p-1 text-muted-foreground hover:text-foreground"
              aria-label="Export State"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
              </svg>
            </button>
            <button
              onClick={copyState}
              className="p-1 text-muted-foreground hover:text-foreground"
              aria-label="Copy State"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
              </svg>
            </button>
            <button
              onClick={toggleVisibility}
              className="p-1 text-muted-foreground hover:text-foreground"
              aria-label="Close Debugger"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Tabs */}
        <div className="flex border-b">
          <button
            className={`px-4 py-2 text-sm font-medium ${activeTab === 'state' ? 'border-b-2 border-blue-500 text-blue-600' : 'text-muted-foreground'}`}
            onClick={() => setActiveTab('state')}
          >
            State
          </button>
          <button
            className={`px-4 py-2 text-sm font-medium ${activeTab === 'validation' ? 'border-b-2 border-blue-500 text-blue-600' : 'text-muted-foreground'}`}
            onClick={() => setActiveTab('validation')}
          >
            Validation
          </button>
          <button
            className={`px-4 py-2 text-sm font-medium ${activeTab === 'history' ? 'border-b-2 border-blue-500 text-blue-600' : 'text-muted-foreground'}`}
            onClick={() => setActiveTab('history')}
          >
            History
          </button>
          <button
            className={`px-4 py-2 text-sm font-medium ${activeTab === 'performance' ? 'border-b-2 border-blue-500 text-blue-600' : 'text-muted-foreground'}`}
            onClick={() => setActiveTab('performance')}
          >
            Performance
          </button>
        </div>

        {/* Tab Content */}
        <div className="p-4 max-h-96 overflow-y-auto">
          {activeTab === 'state' && (
            <div className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">Current State</h4>
                <pre className="text-xs bg-muted p-2 rounded overflow-x-auto">
                  {JSON.stringify(pipelineState, null, 2)}
                </pre>
              </div>
              <div>
                <h4 className="font-medium mb-2">State Summary</h4>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div className="bg-muted p-2 rounded">
                    <p className="text-muted-foreground">Stage</p>
                    <p className="font-medium">{pipelineState.currentStage}</p>
                  </div>
                  <div className="bg-muted p-2 rounded">
                    <p className="text-muted-foreground">File</p>
                    <p className="font-medium">{pipelineState.fileName || 'None'}</p>
                  </div>
                  <div className="bg-muted p-2 rounded">
                    <p className="text-muted-foreground">History Items</p>
                    <p className="font-medium">{history.length}</p>
                  </div>
                  <div className="bg-muted p-2 rounded">
                    <p className="text-muted-foreground">Save Points</p>
                    <p className="font-medium">{pipelineState.savePoints.length}</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'validation' && (
            <div className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">Validation Status</h4>
                {validationErrors.length === 0 ? (
                  <div className="bg-green-500/10 border border-green-500/20 p-3 rounded">
                    <p className="text-green-700 dark:text-green-300">State is valid</p>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <div className="bg-red-500/10 border border-red-500/20 p-3 rounded">
                      <p className="text-red-700 dark:text-red-300">
                        {validationErrors.length} validation error(s) found
                      </p>
                    </div>
                    <div className="max-h-48 overflow-y-auto">
                      {validationErrors.map((error, index) => (
                        <div key={index} className="bg-muted p-2 rounded text-sm mb-2">
                          <p className="font-medium">{error.message}</p>
                          <p className="text-muted-foreground">Path: {error.path.join(' > ')}</p>
                          <p className="text-muted-foreground">Code: {error.code}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {activeTab === 'history' && (
            <div className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">State History ({history.length} items)</h4>
                <div className="max-h-64 overflow-y-auto space-y-2">
                  {history.slice().reverse().map((item, index) => (
                    <div key={item.id} className="bg-muted p-2 rounded text-sm">
                      <div className="flex justify-between">
                        <span className="font-medium">{item.action}</span>
                        <span className="text-muted-foreground">
                          {new Date(item.timestamp).toLocaleTimeString()}
                        </span>
                      </div>
                      {item.data && (
                        <pre className="text-xs mt-1 overflow-x-auto">
                          {JSON.stringify(item.data, null, 2).substring(0, 100)}...
                        </pre>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'performance' && (
            <div className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">Performance Metrics</h4>
                <div className="space-y-3">
                  {Object.entries(performanceStats).map(([operation, stats]) => (
                    <div key={operation} className="bg-muted p-3 rounded">
                      <div className="flex justify-between mb-1">
                        <span className="font-medium">{operation}</span>
                        <span className="text-muted-foreground">{stats.count} calls</span>
                      </div>
                      <div className="grid grid-cols-3 gap-2 text-sm">
                        <div>
                          <p className="text-muted-foreground">Avg Time</p>
                          <p className="font-medium">{stats.averageTime.toFixed(2)}ms</p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">Total Time</p>
                          <p className="font-medium">{stats.totalTime.toFixed(2)}ms</p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">Per Call</p>
                          <p className="font-medium">{(stats.totalTime / stats.count).toFixed(2)}ms</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// State snapshot manager
class StateSnapshotManagerInternal {
  private snapshots: Map<string, any> = new Map();
  private maxSnapshots = 10;

  createSnapshot(name: string, state: any): string {
    const id = `${name}-${Date.now()}`;
    this.snapshots.set(id, {
      id,
      name,
      state: JSON.parse(JSON.stringify(state)),
      timestamp: Date.now()
    });

    // Clean up old snapshots
    this.cleanupOldSnapshots();

    return id;
  }

  restoreSnapshot(id: string): any | null {
    const snapshot = this.snapshots.get(id);
    return snapshot ? snapshot.state : null;
  }

  deleteSnapshot(id: string): boolean {
    return this.snapshots.delete(id);
  }

  listSnapshots(): Array<{ id: string; name: string; timestamp: number }> {
    return Array.from(this.snapshots.values())
      .map(snapshot => ({
        id: snapshot.id,
        name: snapshot.name,
        timestamp: snapshot.timestamp
      }))
      .sort((a, b) => b.timestamp - a.timestamp);
  }

  private cleanupOldSnapshots(): void {
    if (this.snapshots.size <= this.maxSnapshots) return;

    const snapshotsArray = Array.from(this.snapshots.values())
      .sort((a, b) => b.timestamp - a.timestamp);

    const excess = snapshotsArray.length - this.maxSnapshots;
    for (let i = 0; i < excess; i++) {
      this.snapshots.delete(snapshotsArray[snapshotsArray.length - 1 - i].id);
    }
  }
}

// State change logger
class StateChangeLoggerInternal {
  private logs: Array<{
    timestamp: number;
    action: string;
    path: string[];
    oldValue: any;
    newValue: any;
    component?: string;
  }> = [];
  private maxLogs = 1000;

  logChange(
    action: string,
    path: string[],
    oldValue: any,
    newValue: any,
    component?: string
  ): void {
    this.logs.push({
      timestamp: Date.now(),
      action,
      path,
      oldValue,
      newValue,
      component
    });

    // Trim logs if exceeding max size
    if (this.logs.length > this.maxLogs) {
      this.logs.shift();
    }
  }

  getLogs(since?: number): Array<any> {
    if (since) {
      return this.logs.filter(log => log.timestamp >= since);
    }
    return [...this.logs];
  }

  clearLogs(): void {
    this.logs = [];
  }

  getLogsByComponent(component: string): Array<any> {
    return this.logs.filter(log => log.component === component);
  }

  getLogsByAction(action: string): Array<any> {
    return this.logs.filter(log => log.action === action);
  }
}

// State visualization tools
export const StateVisualizer: React.FC<{
  state: any;
  className?: string;
}> = ({ state, className = '' }) => {
  const [expandedPaths, setExpandedPaths] = useState<Set<string>>(new Set());

  const togglePath = (path: string) => {
    const newExpanded = new Set(expandedPaths);
    if (newExpanded.has(path)) {
      newExpanded.delete(path);
    } else {
      newExpanded.add(path);
    }
    setExpandedPaths(newExpanded);
  };

  const renderValue = (value: any, path = ''): React.ReactNode => {
    if (value === null) {
      return <span className="text-muted-foreground">null</span>;
    }
    
    if (value === undefined) {
      return <span className="text-muted-foreground">undefined</span>;
    }

    if (typeof value === 'string') {
      return <span className="text-green-600 dark:text-green-400">"{value}"</span>;
    }

    if (typeof value === 'number') {
      return <span className="text-blue-600 dark:text-blue-400">{value}</span>;
    }

    if (typeof value === 'boolean') {
      return <span className="text-purple-600 dark:text-purple-400">{value.toString()}</span>;
    }

    if (Array.isArray(value)) {
      if (value.length === 0) {
        return <span className="text-muted-foreground">[]</span>;
      }

      const isExpanded = expandedPaths.has(path);
      return (
        <div>
          <button
            onClick={() => togglePath(path)}
            className="text-muted-foreground hover:text-foreground text-left"
          >
            Array[{value.length}] {isExpanded ? '▼' : '▶'}
          </button>
          {isExpanded && (
            <div className="ml-4 mt-1 space-y-1">
              {value.map((item, index) => (
                <div key={index} className="text-sm">
                  <span className="text-muted-foreground">{index}:</span> {renderValue(item, `${path}[${index}]`)}
                </div>
              ))}
            </div>
          )}
        </div>
      );
    }

    if (typeof value === 'object') {
      const keys = Object.keys(value);
      if (keys.length === 0) {
        return <span className="text-muted-foreground">{'{}'}</span>;
      }

      const isExpanded = expandedPaths.has(path);
      return (
        <div>
          <button
            onClick={() => togglePath(path)}
            className="text-muted-foreground hover:text-foreground text-left"
          >
            Object({keys.length}) {isExpanded ? '▼' : '▶'}
          </button>
          {isExpanded && (
            <div className="ml-4 mt-1 space-y-1">
              {keys.map(key => (
                <div key={key} className="text-sm">
                  <span className="text-muted-foreground">{key}:</span> {renderValue(value[key], `${path}.${key}`)}
                </div>
              ))}
            </div>
          )}
        </div>
      );
    }

    return <span>{String(value)}</span>;
  };

  return (
    <div className={`font-mono text-sm ${className}`}>
      {renderValue(state)}
    </div>
  );
};

// Export classes
export const StateSnapshotManager = StateSnapshotManagerInternal;
export const StateChangeLogger = StateChangeLoggerInternal;