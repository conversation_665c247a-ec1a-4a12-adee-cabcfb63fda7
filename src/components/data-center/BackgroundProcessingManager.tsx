import React, { useState, useCallback, useRef, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Play, 
  Square, 
  Trash2, 
  Download, 
  AlertCircle, 
  CheckCircle, 
  Clock,
  Cpu,
  Activity,
  FileText,
  Database
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useBackgroundProcessor } from './hooks/useBackgroundProcessor';

interface ProcessingJob {
  id: string;
  type: 'data-processing' | 'file-analysis' | 'computation' | 'export';
  name: string;
  status: 'pending' | 'running' | 'completed' | 'error' | 'cancelled';
  progress: number;
  startTime?: number;
  endTime?: number;
  data?: any;
  result?: any;
  error?: string;
  logs: string[];
}

interface BackgroundProcessingManagerProps {
  onJobComplete?: (job: ProcessingJob) => void;
  onJobError?: (job: ProcessingJob, error: string) => void;
  className?: string;
}

const SAMPLE_JOBS = {
  'large-dataset-analysis': {
    name: 'Large Dataset Analysis',
    type: 'data-processing' as const,
    description: 'Process and analyze a large CSV dataset with statistical computations',
    data: {
      operation: 'analyze',
      rows: 100000,
      columns: 20,
      computations: ['mean', 'std', 'correlation', 'outliers']
    }
  },
  'file-batch-processing': {
    name: 'Batch File Processing',
    type: 'file-analysis' as const,
    description: 'Process multiple files for metadata extraction and content analysis',
    data: {
      operation: 'batch-process',
      fileCount: 50,
      operations: ['metadata', 'content-analysis', 'similarity']
    }
  },
  'complex-computation': {
    name: 'Complex Mathematical Computation',
    type: 'computation' as const,
    description: 'Perform intensive mathematical calculations with progress tracking',
    data: {
      operation: 'compute',
      iterations: 10000,
      algorithm: 'monte-carlo-simulation'
    }
  },
  'data-export': {
    name: 'Large Data Export',
    type: 'export' as const,
    description: 'Export processed data to multiple formats with compression',
    data: {
      operation: 'export',
      formats: ['csv', 'json', 'parquet'],
      compression: true,
      records: 50000
    }
  }
};

export const BackgroundProcessingManager: React.FC<BackgroundProcessingManagerProps> = ({
  onJobComplete,
  onJobError,
  className
}) => {
  const [jobs, setJobs] = useState<ProcessingJob[]>([]);
  const [activeTab, setActiveTab] = useState('queue');
  const { processData, getJobStatus, cancelJob } = useBackgroundProcessor();
  const jobIdCounter = useRef(0);

  // Simulate background processing with realistic progress
  const simulateBackgroundJob = useCallback(async (job: ProcessingJob): Promise<any> => {
    const { type, data } = job;
    let totalSteps = 100;
    let currentStep = 0;
    
    const updateProgress = (step: number, message: string) => {
      const progress = (step / totalSteps) * 100;
      setJobs(prev => prev.map(j => 
        j.id === job.id 
          ? { 
              ...j, 
              progress, 
              logs: [...j.logs, `[${new Date().toLocaleTimeString()}] ${message}`]
            }
          : j
      ));
    };

    try {
      switch (type) {
        case 'data-processing':
          totalSteps = data.rows / 1000; // Simulate processing 1000 rows at a time
          updateProgress(currentStep++, 'Initializing data processing...');
          
          for (let i = 0; i < totalSteps; i++) {
            await new Promise(resolve => setTimeout(resolve, 50 + Math.random() * 100));
            currentStep++;
            updateProgress(currentStep, `Processing batch ${i + 1}/${totalSteps}`);
            
            // Check if job was cancelled
            const currentJob = jobs.find(j => j.id === job.id);
            if (currentJob?.status === 'cancelled') {
              throw new Error('Job cancelled by user');
            }
          }
          
          updateProgress(totalSteps, 'Finalizing analysis results...');
          return {
            processedRows: data.rows,
            computations: data.computations,
            insights: [
              'Strong correlation detected between variables A and B',
              '15 outliers identified and flagged',
              'Data distribution follows normal pattern'
            ],
            executionTime: Date.now() - (job.startTime || Date.now())
          };

        case 'file-analysis':
          totalSteps = data.fileCount;
          updateProgress(currentStep++, 'Starting file analysis...');
          
          for (let i = 0; i < data.fileCount; i++) {
            await new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 200));
            currentStep++;
            updateProgress(currentStep, `Analyzing file ${i + 1}/${data.fileCount}`);
          }
          
          return {
            filesProcessed: data.fileCount,
            totalSize: '2.3 GB',
            formats: ['PDF', 'DOCX', 'TXT', 'CSV'],
            duplicates: 3,
            insights: ['Most files are text-based', 'Average file size: 46MB']
          };

        case 'computation':
          totalSteps = data.iterations / 100; // Process in batches of 100
          updateProgress(currentStep++, 'Initializing computation engine...');
          
          for (let i = 0; i < totalSteps; i++) {
            await new Promise(resolve => setTimeout(resolve, 30 + Math.random() * 70));
            currentStep++;
            updateProgress(currentStep, `Computing iteration ${(i + 1) * 100}/${data.iterations}`);
          }
          
          return {
            algorithm: data.algorithm,
            iterations: data.iterations,
            result: Math.PI, // Mock result
            accuracy: '99.97%',
            convergence: 'Achieved after 8,750 iterations'
          };

        case 'export':
          totalSteps = data.formats.length * 2; // 2 steps per format (process + compress)
          updateProgress(currentStep++, 'Preparing data for export...');
          
          for (const format of data.formats) {
            await new Promise(resolve => setTimeout(resolve, 200 + Math.random() * 300));
            currentStep++;
            updateProgress(currentStep, `Exporting to ${format.toUpperCase()}...`);
            
            if (data.compression) {
              await new Promise(resolve => setTimeout(resolve, 150 + Math.random() * 200));
              currentStep++;
              updateProgress(currentStep, `Compressing ${format.toUpperCase()} file...`);
            }
          }
          
          return {
            formats: data.formats,
            records: data.records,
            files: data.formats.map((f: string) => `export_${Date.now()}.${f}${data.compression ? '.gz' : ''}`),
            totalSize: '156 MB',
            compressionRatio: data.compression ? '3.2:1' : '1:1'
          };

        default:
          throw new Error(`Unknown job type: ${type}`);
      }
    } catch (error) {
      throw error;
    }
  }, [jobs]);

  const startJob = useCallback(async (jobTemplate: keyof typeof SAMPLE_JOBS) => {
    const template = SAMPLE_JOBS[jobTemplate];
    const jobId = `job_${++jobIdCounter.current}_${Date.now()}`;
    
    const newJob: ProcessingJob = {
      id: jobId,
      type: template.type,
      name: template.name,
      status: 'pending',
      progress: 0,
      startTime: Date.now(),
      data: template.data,
      logs: [`[${new Date().toLocaleTimeString()}] Job queued: ${template.name}`]
    };
    
    setJobs(prev => [...prev, newJob]);
    
    // Start processing
    setTimeout(async () => {
      setJobs(prev => prev.map(j => 
        j.id === jobId ? { ...j, status: 'running' as const } : j
      ));
      
      try {
        const result = await simulateBackgroundJob(newJob);
        
        setJobs(prev => prev.map(j => 
          j.id === jobId 
            ? { 
                ...j, 
                status: 'completed' as const, 
                progress: 100, 
                endTime: Date.now(),
                result,
                logs: [...j.logs, `[${new Date().toLocaleTimeString()}] Job completed successfully`]
              }
            : j
        ));
        
        const completedJob = { ...newJob, status: 'completed' as const, result };
        onJobComplete?.(completedJob);
        
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        
        setJobs(prev => prev.map(j => 
          j.id === jobId 
            ? { 
                ...j, 
                status: 'error' as const, 
                endTime: Date.now(),
                error: errorMessage,
                logs: [...j.logs, `[${new Date().toLocaleTimeString()}] Job failed: ${errorMessage}`]
              }
            : j
        ));
        
        const failedJob = { ...newJob, status: 'error' as const, error: errorMessage };
        onJobError?.(failedJob, errorMessage);
      }
    }, 100);
  }, [simulateBackgroundJob, onJobComplete, onJobError]);

  const cancelJobById = useCallback((jobId: string) => {
    setJobs(prev => prev.map(j => 
      j.id === jobId && j.status === 'running'
        ? { 
            ...j, 
            status: 'cancelled' as const, 
            endTime: Date.now(),
            logs: [...j.logs, `[${new Date().toLocaleTimeString()}] Job cancelled by user`]
          }
        : j
    ));
  }, []);

  const removeJob = useCallback((jobId: string) => {
    setJobs(prev => prev.filter(j => j.id !== jobId));
  }, []);

  const clearCompletedJobs = useCallback(() => {
    setJobs(prev => prev.filter(j => j.status === 'running' || j.status === 'pending'));
  }, []);

  const downloadJobResult = useCallback((job: ProcessingJob) => {
    if (!job.result) return;
    
    const content = JSON.stringify(job.result, null, 2);
    const blob = new Blob([content], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${job.name.toLowerCase().replace(/\s+/g, '_')}_result.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, []);

  const getJobIcon = (type: ProcessingJob['type']) => {
    switch (type) {
      case 'data-processing': return <Database className="h-4 w-4" />;
      case 'file-analysis': return <FileText className="h-4 w-4" />;
      case 'computation': return <Cpu className="h-4 w-4" />;
      case 'export': return <Download className="h-4 w-4" />;
      default: return <Activity className="h-4 w-4" />;
    }
  };

  const getStatusIcon = (status: ProcessingJob['status']) => {
    switch (status) {
      case 'running': return <Clock className="h-4 w-4 animate-spin" />;
      case 'completed': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error': case 'cancelled': return <AlertCircle className="h-4 w-4 text-red-500" />;
      default: return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  const runningJobs = jobs.filter(j => j.status === 'running');
  const completedJobs = jobs.filter(j => j.status === 'completed');
  const failedJobs = jobs.filter(j => j.status === 'error' || j.status === 'cancelled');

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Background Processing Manager
            {runningJobs.length > 0 && (
              <Badge variant="secondary" className="animate-pulse">
                {runningJobs.length} running
              </Badge>
            )}
          </CardTitle>
          
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={clearCompletedJobs}
              disabled={completedJobs.length === 0 && failedJobs.length === 0}
            >
              <Trash2 className="h-4 w-4 mr-1" />
              Clear Completed
            </Button>
          </div>
        </div>
        
        {/* Quick Stats */}
        <div className="grid grid-cols-4 gap-4 text-sm">
          <div className="text-center p-2 bg-blue-50 dark:bg-blue-900/20 rounded">
            <div className="font-semibold text-blue-600 dark:text-blue-400">{jobs.filter(j => j.status === 'pending').length}</div>
            <div className="text-blue-600/70 dark:text-blue-400/70">Pending</div>
          </div>
          <div className="text-center p-2 bg-yellow-50 dark:bg-yellow-900/20 rounded">
            <div className="font-semibold text-yellow-600 dark:text-yellow-400">{runningJobs.length}</div>
            <div className="text-yellow-600/70 dark:text-yellow-400/70">Running</div>
          </div>
          <div className="text-center p-2 bg-green-50 dark:bg-green-900/20 rounded">
            <div className="font-semibold text-green-600 dark:text-green-400">{completedJobs.length}</div>
            <div className="text-green-600/70 dark:text-green-400/70">Completed</div>
          </div>
          <div className="text-center p-2 bg-red-50 dark:bg-red-900/20 rounded">
            <div className="font-semibold text-red-600 dark:text-red-400">{failedJobs.length}</div>
            <div className="text-red-600/70 dark:text-red-400/70">Failed</div>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="queue">Job Queue</TabsTrigger>
            <TabsTrigger value="templates">Start New Job</TabsTrigger>
            <TabsTrigger value="monitor">System Monitor</TabsTrigger>
          </TabsList>
          
          <TabsContent value="queue" className="space-y-4">
            {jobs.length === 0 ? (
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  No background jobs in queue. Use the "Start New Job" tab to create processing jobs.
                </AlertDescription>
              </Alert>
            ) : (
              <ScrollArea className="h-96">
                <div className="space-y-3">
                  {jobs.map((job) => (
                    <Card key={job.id} className="p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          {getJobIcon(job.type)}
                          <span className="font-medium">{job.name}</span>
                          {getStatusIcon(job.status)}
                          <Badge variant={job.status === 'completed' ? 'default' : 
                                        job.status === 'running' ? 'secondary' : 
                                        job.status === 'error' ? 'destructive' : 'outline'}>
                            {job.status}
                          </Badge>
                        </div>
                        
                        <div className="flex items-center gap-1">
                          {job.status === 'running' && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => cancelJobById(job.id)}
                            >
                              <Square className="h-3 w-3" />
                            </Button>
                          )}
                          
                          {job.result && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => downloadJobResult(job)}
                            >
                              <Download className="h-3 w-3" />
                            </Button>
                          )}
                          
                          {(job.status === 'completed' || job.status === 'error' || job.status === 'cancelled') && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => removeJob(job.id)}
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          )}
                        </div>
                      </div>
                      
                      {job.status === 'running' && (
                        <div className="mb-2">
                          <div className="flex justify-between text-xs mb-1">
                            <span>Progress: {job.progress.toFixed(1)}%</span>
                            <span>Runtime: {job.startTime ? ((Date.now() - job.startTime) / 1000).toFixed(1) : 0}s</span>
                          </div>
                          <Progress value={job.progress} className="h-1" />
                        </div>
                      )}
                      
                      {job.logs.length > 0 && (
                        <ScrollArea className="h-20 text-xs font-mono bg-gray-50 dark:bg-gray-800 p-2 rounded">
                          {job.logs.slice(-3).map((log, index) => (
                            <div key={index} className={cn(
                              log.includes('Error:') || log.includes('failed:') ? 'text-red-600' :
                              log.includes('completed') ? 'text-green-600' :
                              log.includes('cancelled') ? 'text-yellow-600' :
                              'text-gray-600 dark:text-gray-400'
                            )}>
                              {log}
                            </div>
                          ))}
                        </ScrollArea>
                      )}
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            )}
          </TabsContent>
          
          <TabsContent value="templates" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {Object.entries(SAMPLE_JOBS).map(([key, template]) => (
                <Card key={key} className="p-4 hover:shadow-md transition-shadow cursor-pointer"
                      onClick={() => startJob(key as keyof typeof SAMPLE_JOBS)}>
                  <div className="flex items-start gap-3">
                    {getJobIcon(template.type)}
                    <div className="flex-1">
                      <h4 className="font-medium mb-1">{template.name}</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                        {template.description}
                      </p>
                      <Badge variant="outline" className="text-xs">
                        {template.type}
                      </Badge>
                    </div>
                    <Button size="sm" variant="outline">
                      <Play className="h-3 w-3" />
                    </Button>
                  </div>
                </Card>
              ))}
            </div>
          </TabsContent>
          
          <TabsContent value="monitor" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card className="p-4">
                <h4 className="font-medium mb-3 flex items-center gap-2">
                  <Cpu className="h-4 w-4" />
                  Worker Status
                </h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Available Workers:</span>
                    <span className="font-medium">4</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Active Workers:</span>
                    <span className="font-medium">{runningJobs.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Worker Support:</span>
                    <span className="font-medium text-green-600">✓ Available</span>
                  </div>
                </div>
              </Card>
              
              <Card className="p-4">
                <h4 className="font-medium mb-3 flex items-center gap-2">
                  <Activity className="h-4 w-4" />
                  Performance Metrics
                </h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Total Jobs Processed:</span>
                    <span className="font-medium">{completedJobs.length + failedJobs.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Success Rate:</span>
                    <span className="font-medium text-green-600">
                      {completedJobs.length + failedJobs.length > 0 
                        ? ((completedJobs.length / (completedJobs.length + failedJobs.length)) * 100).toFixed(1)
                        : 0}%
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Avg. Processing Time:</span>
                    <span className="font-medium">
                      {completedJobs.length > 0
                        ? (completedJobs.reduce((acc, job) => 
                            acc + ((job.endTime || 0) - (job.startTime || 0)), 0) / completedJobs.length / 1000).toFixed(1)
                        : 0}s
                    </span>
                  </div>
                </div>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default BackgroundProcessingManager;