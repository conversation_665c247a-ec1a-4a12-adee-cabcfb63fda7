import React from 'react';
import { 
  X, 
  HelpCircle, 
  HardDrive, 
  FileText, 
  Calendar, 
  User, 
  Hash,
  BarChart
} from 'lucide-react';
import { MetadataSidebarProps } from '../types';
import { Button, Card, Typography } from '../ui/StyledComponents';

/**
 * Metadata sidebar component for the Data Center
 * Shows file information, contextual help, and storage overview
 */
export const MetadataSidebar: React.FC<MetadataSidebarProps> = ({
  selectedFile,
  onClose,
  showStorageOverview = true,
  showContextualHelp = true
}) => {
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (date: Date): string => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  return (
    <div className="h-full p-4 overflow-y-auto space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <Typography.H3>Details</Typography.H3>
        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
        >
          <X />
        </Button>
      </div>

      {/* File Metadata */}
      {selectedFile && (
        <Card variant="default">
          <div className="flex items-center gap-2 mb-3">
            <FileText />
            <Typography.H3>File Information</Typography.H3>
          </div>
          
          <div className="space-y-3">
            <div>
              <Typography.Caption>Name</Typography.Caption>
              <Typography.Body>{selectedFile.name}</Typography.Body>
            </div>
            
            <div>
              <Typography.Caption>Size</Typography.Caption>
              <Typography.Body>{formatFileSize(selectedFile.size)}</Typography.Body>
            </div>
            
            <div>
              <Typography.Caption>Type</Typography.Caption>
              <Typography.Body>{selectedFile.type || 'Unknown'}</Typography.Body>
            </div>
            
            <div>
              <Typography.Caption>Last Modified</Typography.Caption>
              <Typography.Body>
                {formatDate(new Date(selectedFile.lastModified))}
              </Typography.Body>
            </div>
            
            {/* Additional metadata if available */}
            <div className="pt-2 border-t">
              <div className="flex items-center gap-2 mb-2">
                <BarChart />
                <Typography.Caption>Statistics</Typography.Caption>
              </div>
              <div className="text-xs text-muted-foreground">
                Processing statistics will appear here after data analysis
              </div>
            </div>
          </div>
        </Card>
      )}

      {/* Contextual Help */}
      {showContextualHelp && (
        <Card variant="info">
          <div className="flex items-center gap-2 mb-3">
            <HelpCircle />
            <Typography.H3>Help & Tips</Typography.H3>
          </div>
          
          <div className="space-y-3">
            <div>
              <Typography.Caption>Current Stage</Typography.Caption>
              <Typography.Body>Upload your data files to get started</Typography.Body>
            </div>
            
            <div>
              <Typography.Caption>Supported Formats</Typography.Caption>
              <div className="text-sm text-muted-foreground">
                • CSV files (.csv)
                • Excel files (.xlsx, .xls)
                • JSON files (.json)
                • Text files (.txt)
              </div>
            </div>
            
            <div>
              <Typography.Caption>Quick Tips</Typography.Caption>
              <div className="text-sm text-muted-foreground">
                • Drag and drop files for quick upload
                • Use Ctrl+V to paste data
                • Press F1 for keyboard shortcuts
              </div>
            </div>
          </div>
        </Card>
      )}

      {/* Storage Overview */}
      {showStorageOverview && (
        <Card variant="default">
          <div className="flex items-center gap-2 mb-3">
            <HardDrive />
            <Typography.H3>Storage Overview</Typography.H3>
          </div>
          
          <div className="space-y-3">
            <div>
              <Typography.Caption>Local Storage</Typography.Caption>
              <div className="flex justify-between items-center">
                <Typography.Body>Used: 2.4 GB</Typography.Body>
                <Typography.Caption>of 10 GB</Typography.Caption>
              </div>
              <div className="w-full bg-muted rounded-full h-2 mt-1">
                <div className="bg-primary h-2 rounded-full" style={{ width: '24%' }} />
              </div>
            </div>
            
            <div>
              <Typography.Caption>Recent Files</Typography.Caption>
              <div className="text-sm text-muted-foreground">
                • sales_data.csv (1.2 MB)
                • customer_info.xlsx (800 KB)
                • analytics.json (450 KB)
              </div>
            </div>
            
            <div className="pt-2 border-t">
              <Button
                variant="ghost"
                size="sm"
              >
                <HardDrive />
                Manage Storage
              </Button>
            </div>
          </div>
        </Card>
      )}

      {/* Performance Metrics */}
      <Card variant="data">
        <div className="flex items-center gap-2 mb-3">
          <BarChart />
          <Typography.H3>Performance</Typography.H3>
        </div>
        
        <div className="space-y-2">
          <div className="flex justify-between">
            <Typography.Caption>Processing Speed</Typography.Caption>
            <Typography.Caption>1.2k rows/sec</Typography.Caption>
          </div>
          <div className="flex justify-between">
            <Typography.Caption>Memory Usage</Typography.Caption>
            <Typography.Caption>45 MB</Typography.Caption>
          </div>
          <div className="flex justify-between">
            <Typography.Caption>Cache Hit Rate</Typography.Caption>
            <Typography.Caption>87%</Typography.Caption>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default MetadataSidebar;