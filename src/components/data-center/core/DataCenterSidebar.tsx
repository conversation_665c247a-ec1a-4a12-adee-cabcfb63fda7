import React from 'react';
import { 
  Upload, 
  Eye, 
  Play, 
  Database, 
  Download,
  ChevronLeft,
  ChevronRight,
  CheckCircle,
  Circle
} from 'lucide-react';
import { DataCenterSidebarProps, StageType } from '../types';
import { Button } from '../ui/StyledComponents';

/**
 * Sidebar component for the Data Center
 * Contains navigation, stage indicators, and quick actions
 */
export const DataCenterSidebar: React.FC<DataCenterSidebarProps> = ({
  currentStage,
  onStageChange,
  collapsed,
  onToggleCollapse,
  stages
}) => {
  const defaultStages: Array<{
    id: StageType;
    label: string;
    icon: React.ComponentType;
    completed: boolean;
  }> = [
    { id: 'upload', label: 'Upload Data', icon: Upload, completed: false },
    { id: 'preview', label: 'Preview & Validate', icon: Eye, completed: false },
    { id: 'process', label: 'Process Data', icon: Play, completed: false },
    { id: 'sql', label: 'Query & Analyze', icon: Database, completed: false },
    { id: 'export', label: 'Export Results', icon: Download, completed: false },
  ];

  const stageList = stages || defaultStages;

  return (
    <div className={`h-full bg-muted/30 border-r transition-all duration-300 ${collapsed ? 'w-16' : 'w-64'}`}>
      {/* Collapse Toggle */}
      <div className="p-4 border-b flex justify-between items-center">
        {!collapsed && (
          <h2 className="font-semibold text-sm text-muted-foreground uppercase tracking-wide">
            Data Pipeline
          </h2>
        )}
        <Button
          variant="ghost"
          size="sm"
          onClick={onToggleCollapse}
        >
          {collapsed ? <ChevronRight /> : <ChevronLeft />}
        </Button>
      </div>

      {/* Stage Navigation */}
      <div className="p-4 space-y-2">
        {stageList.map((stage, index) => {
          const Icon = stage.icon;
          const isActive = currentStage === stage.id;
          const isCompleted = stage.completed;
          
          return (
            <div key={stage.id} className="relative">
              {/* Stage Button */}
              <button
                onClick={() => onStageChange(stage.id)}
                className={`
                  w-full flex items-center gap-3 p-3 rounded-lg transition-all duration-200
                  ${isActive 
                    ? 'bg-primary/10 text-primary border border-primary/20' 
                    : 'hover:bg-muted/50 text-muted-foreground hover:text-foreground'
                  }
                  ${collapsed ? 'justify-center' : 'justify-start'}
                `}
              >
                {/* Stage Icon */}
                <div className="relative flex-shrink-0">
                  <Icon />
                  {isCompleted && (
                    <CheckCircle className="absolute -top-1 -right-1 w-3 h-3 text-success bg-background rounded-full" />
                  )}
                </div>
                
                {/* Stage Label */}
                {!collapsed && (
                  <div className="flex-1 text-left">
                    <div className="font-medium text-sm">{stage.label}</div>
                    {isActive && (
                      <div className="text-xs text-muted-foreground mt-1">
                        Current step
                      </div>
                    )}
                  </div>
                )}
              </button>
              
              {/* Connection Line */}
              {!collapsed && index < stageList.length - 1 && (
                <div className="absolute left-6 top-12 w-px h-4 bg-border" />
              )}
            </div>
          );
        })}
      </div>

      {/* Quick Actions */}
      {!collapsed && (
        <div className="p-4 border-t mt-auto">
          <h3 className="font-medium text-sm mb-3 text-muted-foreground">
            Quick Actions
          </h3>
          <div className="space-y-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onStageChange('upload')}
            >
              <Upload />
              New Upload
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onStageChange('sql')}
            >
              <Database />
              Query Builder
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default DataCenterSidebar;