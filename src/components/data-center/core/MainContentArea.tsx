import React, { Suspense } from 'react';
import { MainContentRendererProps } from '../types';
import { Card, Typography } from '../ui/StyledComponents';
import { Loader2 } from 'lucide-react';
import { FileUploadZone } from '../features/upload';
import { DataPreviewPanel } from '../features/preview';
import { ProcessingPanel } from '../features/processing';
import { SQLQueryPanel } from '../features/sql';
import { DashboardView } from '../features/dashboard';

// Placeholder components - will be replaced with actual view components
const PlaceholderView: React.FC<{ title: string }> = ({ title }) => (
  <Card variant="default">
    <div className="text-center p-8">
      <Typography.H3>{title}</Typography.H3>
      <Typography.Body className="mt-2 text-muted-foreground">
        This view is under development
      </Typography.Body>
    </div>
  </Card>
);

/**
 * Loading component for lazy-loaded views
 */
const LoadingFallback: React.FC = () => (
  <div className="flex items-center justify-center h-64">
    <div className="flex items-center gap-2 text-muted-foreground">
      <Loader2 className="animate-spin" />
      <Typography.Body>Loading...</Typography.Body>
    </div>
  </div>
);

/**
 * Error boundary component for handling view errors
 */
class ViewErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('View Error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <Card variant="error">
          <div className="text-center p-8">
            <Typography.H3>Something went wrong</Typography.H3>
            <Typography.Body className="mt-2 text-muted-foreground">
              {this.state.error?.message || 'An unexpected error occurred'}
            </Typography.Body>
            <button
              onClick={() => this.setState({ hasError: false, error: undefined })}
              className="mt-4 px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
            >
              Try Again
            </button>
          </div>
        </Card>
      );
    }

    return this.props.children;
  }
}

/**
 * Main content area component for the Data Center
 * Renders different views based on current stage and active view
 */
export const MainContentArea: React.FC<MainContentRendererProps> = ({
  currentStage,
  activeView,
  data,
  processingResult,
  sqlResult,
  onStageChange
}) => {
  /**
   * Render the appropriate view based on current stage and active view
   */
  const renderView = () => {
    // Render view based on active view or current stage
    const viewToRender = activeView || currentStage;

    switch (viewToRender) {
      case 'dashboard':
        return (
          <DashboardView
            widgets={['data-summary', 'column-stats']}
            data={data}
            onWidgetToggle={(widget) => console.log('Widget toggled:', widget)}
          />
        );

      case 'upload':
        return (
          <FileUploadZone
              onFileUpload={(files) => console.log('Files uploaded:', files)}
              acceptedTypes=".csv,.json,.xlsx"
              maxFileSize={10 * 1024 * 1024} // 10MB
              multiple={false}
            />
        );

      case 'preview':
        return data ? (
          <DataPreviewPanel
            data={data}
            onDataChange={(newData) => console.log('Data changed:', newData)}
            showStatistics={true}
            showFilters={true}
            showPagination={true}
            pageSize={50}
            isLoading={false}
          />
        ) : (
          <Card variant="default">
            <div className="text-center p-8">
              <Typography.H3>Preview View</Typography.H3>
              <Typography.Body className="mt-2 text-muted-foreground">
                No data available for preview
              </Typography.Body>
            </div>
          </Card>
        );

      case 'process':
        return data ? (
          <ProcessingPanel
            data={data}
            onProcessingComplete={(result) => console.log('Processing complete:', result)}
            templates={{}}
            pythonEnvStatus={{
              isInstalled: true,
              isActive: true,
              pythonVersion: '3.9.0'
            }}
            isProcessing={false}
          />
        ) : (
          <Card variant="default">
            <div className="text-center p-8">
              <Typography.H3>Processing View</Typography.H3>
              <Typography.Body className="mt-2 text-muted-foreground">
                No data available for processing
              </Typography.Body>
            </div>
          </Card>
        );

      case 'sql':
        return data ? (
          <SQLQueryPanel
            data={data}
            onQueryGenerate={(sql) => console.log('Query generated:', sql)}
            onQueryExecute={(sql) => console.log('Query executed:', sql)}
            currentQuery={sqlResult?.sql}
            queryResult={sqlResult ? { error: sqlResult.error } : undefined}
            isExecuting={false}
          />
        ) : (
          <Card variant="default">
            <div className="text-center p-8">
              <Typography.H3>SQL View</Typography.H3>
              <Typography.Body className="mt-2 text-muted-foreground">
                No data available for SQL operations
              </Typography.Body>
            </div>
          </Card>
        );

      default:
        return (
          <Card variant="default">
            <div className="text-center p-8">
              <Typography.H3>Welcome to Data Center</Typography.H3>
              <Typography.Body className="mt-2 text-muted-foreground">
                Select a stage from the sidebar to get started
              </Typography.Body>
            </div>
          </Card>
        );
    }
  };

  return (
    <div className="flex-1 p-6 overflow-auto">
      <ViewErrorBoundary>
        <Suspense fallback={<LoadingFallback />}>
          {renderView()}
        </Suspense>
      </ViewErrorBoundary>
    </div>
  );
};

export default MainContentArea;