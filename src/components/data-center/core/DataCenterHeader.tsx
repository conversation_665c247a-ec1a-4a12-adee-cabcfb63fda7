import React from 'react';
import { 
  ArrowLeft, 
  Settings, 
  Home, 
  LayoutGrid, 
  Database, 
  Code,
  BarChart
} from 'lucide-react';
import { DataCenterHeaderProps, ViewType } from '../types';
import { Button } from '../ui/StyledComponents';

/**
 * Header component for the Data Center
 * Contains navigation, breadcrumbs, view switcher, and action buttons
 */
export const DataCenterHeader: React.FC<DataCenterHeaderProps> = ({
  activeView,
  onViewChange,
  onBack,
  showControlPanel,
  onToggleControlPanel,
  breadcrumbs = []
}) => {
  const viewOptions: Array<{ id: ViewType; label: string; icon: React.ComponentType }> = [
    { id: 'dashboard', label: 'Dashboard', icon: Home },
    { id: 'grid', label: 'Data Grid', icon: LayoutGrid },
    { id: 'sql', label: 'SQL Query', icon: Database },
    { id: 'upload', label: 'Upload', icon: BarChart },
  ];

  return (
    <header className="flex items-center justify-between p-4 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      {/* Left Section - Back button and Breadcrumbs */}
      <div className="flex items-center gap-4">
        {onBack && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onBack}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            Back
          </Button>
        )}
        
        {/* Breadcrumbs */}
        {breadcrumbs.length > 0 && (
          <nav className="flex items-center space-x-2 text-sm text-muted-foreground">
            {breadcrumbs.map((crumb, index) => (
              <React.Fragment key={index}>
                {index > 0 && <span>/</span>}
                {crumb.onClick ? (
                  <button
                    onClick={crumb.onClick}
                    className="hover:text-foreground transition-colors"
                  >
                    {crumb.label}
                  </button>
                ) : (
                  <span className={index === breadcrumbs.length - 1 ? 'text-foreground font-medium' : ''}>
                    {crumb.label}
                  </span>
                )}
              </React.Fragment>
            ))}
          </nav>
        )}
      </div>

      {/* Center Section - View Switcher */}
      <div className="flex items-center gap-1 bg-muted/50 rounded-lg p-1">
        {viewOptions.map(({ id, label, icon: Icon }) => (
          <Button
            key={id}
            variant={activeView === id ? 'primary' : 'ghost'}
            size="sm"
            onClick={() => onViewChange(id)}
          >
            <Icon />
            <span className="hidden sm:inline">{label}</span>
          </Button>
        ))}
      </div>

      {/* Right Section - Action buttons */}
      <div className="flex items-center gap-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={onToggleControlPanel}
          className={`${showControlPanel ? 'bg-muted' : ''}`}
          title="Settings"
        >
          <Settings className="w-4 h-4" />
        </Button>
      </div>
    </header>
  );
};

export default DataCenterHeader;