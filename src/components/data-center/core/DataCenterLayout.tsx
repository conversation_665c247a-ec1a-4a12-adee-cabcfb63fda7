import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { DataCenterLayoutProps } from '../types';

/**
 * Main layout component for the Data Center
 * Handles the overall structure with sidebar management and responsive design
 */
export const DataCenterLayout: React.FC<DataCenterLayoutProps> = ({
  children,
  sidebar,
  metadataSidebar,
  showSidebar,
  showMetadataSidebar,
  onToggleSidebar,
  onToggleMetadataSidebar,
  className = ''
}) => {
  return (
    <div className={`flex h-screen bg-background text-foreground ${className}`}>
      {/* Left Sidebar */}
      <AnimatePresence>
        {showSidebar && (
          <motion.div
            initial={{ width: 0, opacity: 0 }}
            animate={{ width: 'auto', opacity: 1 }}
            exit={{ width: 0, opacity: 0 }}
            transition={{ duration: 0.3, ease: 'easeInOut' }}
            className="flex-shrink-0 overflow-hidden"
          >
            <div className="w-64 h-full bg-muted/30 border-r">
              {sidebar}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col min-w-0">
        {children}
      </div>

      {/* Right Metadata Sidebar */}
      <AnimatePresence>
        {showMetadataSidebar && (
          <motion.div
            initial={{ width: 0, opacity: 0 }}
            animate={{ width: 320, opacity: 1 }}
            exit={{ width: 0, opacity: 0 }}
            transition={{ duration: 0.3, ease: 'easeInOut' }}
            className="flex-shrink-0 overflow-hidden"
          >
            <div className="w-80 h-full bg-muted/30 border-l">
              {metadataSidebar}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default DataCenterLayout;