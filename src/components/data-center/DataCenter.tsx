import React, { useState, useCallback, useEffect, useRef } from 'react';
import { motion, AnimatePresence as FramerAnimatePresence } from 'framer-motion';
import { 
  Upload, FileSpreadsheet, Database, Code, Play, AlertCircle, 
  CheckCircle, Download, Terminal, Check, Loader2, FileText,
  FileImage, FilePlus, Brain, Layers, Zap, Search, Settings,
  FileJson, FileCode, BookOpen, Package, ChevronRight, HardDrive,
  Home, GitBranch, Eye, Wrench, FileOutput, MoreHorizontal, Edit,
  Trash, Copy, Share, Filter, SortAsc, BarChart
} from 'lucide-react';
import { useDropzone } from 'react-dropzone';
import Papa from 'papaparse';
import * as XLSX from 'xlsx';
import { api } from '@/lib/api';
import { Command } from '@tauri-apps/plugin-shell';
import { save } from '@tauri-apps/plugin-dialog';
import { writeTextFile } from '@tauri-apps/plugin-fs';
import DocumentProcessor from '@/lib/services/documentProcessor';
import { ADVANCED_PYTHON_TEMPLATES } from '@/lib/services/pythonTemplates';
import { TESTED_PYTHON_TEMPLATES } from '@/lib/services/testPythonTemplates';
import DataExplorationDashboard from './DataExplorationDashboard';
import StorageDashboard from './StorageDashboard';
import storageManager from '@/lib/services/browserStorageManager';
import reportScheduler from '@/lib/services/reportScheduler';
import { DataCenterProvider, useDataCenter } from './DataCenterContext';
import { Card, Button, Badge, Breadcrumb, BreadcrumbItem, Divider, Typography } from './ui/StyledComponents';
import { ContextMenu } from './ui/ContextMenu';

// Types
interface DataPreview {
  headers: string[];
  rows: any[][];
  totalRows: number;
  dataTypes?: Record<string, string>;
  statistics?: Record<string, any>;
  fullData?: any[][]; // Store full dataset for exploration
}

interface ProcessingResult {
  content: string;
  metadata: Record<string, any>;
  chunks?: Array<{
    text: string;
    metadata: Record<string, any>;
  }>;
  embeddings?: number[][];
}

interface SQLGenerationResult {
  sql: string;
  isValid: boolean;
  error?: string;
  analysis?: string;
}

interface DataCenterProps {
  onBack?: () => void;
  onDataChange?: (data: Partial<DataPipelineState>) => void;
}

interface PythonEnvStatus {
  isInstalled: boolean;
  isActive: boolean;
  pythonVersion?: string;
  pipPackages?: string[];
  error?: string;
}

// Merge with advanced and tested templates
const PYTHON_TEMPLATES = {
  ...TESTED_PYTHON_TEMPLATES, // Use tested templates first
  ...ADVANCED_PYTHON_TEMPLATES,
  
  namedEntityRecognition: `# Named Entity Recognition (NER) Template
import pandas as pd
import json
import re

def extract_entities(text):
    """Extract named entities from text"""
    
    entities = {
        'persons': [],
        'organizations': [],
        'locations': [],
        'dates': [],
        'money': [],
        'emails': [],
        'phones': [],
        'urls': []
    }
    
    # Extract emails
    email_pattern = r'\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Z|a-z]{2,}\\b'
    entities['emails'] = re.findall(email_pattern, text)
    
    # Extract URLs
    url_pattern = r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+'
    entities['urls'] = re.findall(url_pattern, text)
    
    # Extract phone numbers
    phone_pattern = r'\\b\\d{3}[-.]?\\d{3}[-.]?\\d{4}\\b'
    entities['phones'] = re.findall(phone_pattern, text)
    
    # Extract money amounts
    money_pattern = r'\\$[\\d,]+\\.?\\d*[MBK]?'
    entities['money'] = re.findall(money_pattern, text)
    
    # Extract dates (simple pattern)
    date_pattern = r'\\b\\d{1,2}[/-]\\d{1,2}[/-]\\d{2,4}\\b|\\b(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]* \\d{1,2},? \\d{4}\\b'
    entities['dates'] = re.findall(date_pattern, text, re.IGNORECASE)
    
    # Simple organization detection (capitalized words)
    org_pattern = r'\\b[A-Z][a-zA-Z]+(?:\\s+[A-Z][a-zA-Z]+)*(?:\\s+(?:Inc|Corp|LLC|Ltd|Company|Co|Group|Industries|Services))\\.?\\b'
    entities['organizations'] = re.findall(org_pattern, text)
    
    # Simple person name detection (Title + Name pattern)
    person_pattern = r'\\b(?:Mr|Mrs|Ms|Dr|Prof)\\.?\\s+[A-Z][a-z]+(?:\\s+[A-Z][a-z]+)*\\b'
    entities['persons'] = re.findall(person_pattern, text)
    
    return entities

# Process the data
if 'df' in locals():
    all_entities = []
    text_columns = df.select_dtypes(include=['object']).columns
    
    for col in text_columns:
        for idx, text in df[col].items():
            if pd.notna(text):
                entities = extract_entities(str(text))
                if any(entities.values()):
                    all_entities.append({
                        'source_column': col,
                        'source_row': idx,
                        'entities': entities
                    })
    
    # Generate SQL for entity storage
    sql = """
CREATE TABLE IF NOT EXISTS named_entities (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    entity_type VARCHAR(50),
    entity_value TEXT,
    source_column VARCHAR(100),
    source_row INTEGER,
    confidence FLOAT DEFAULT 1.0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

"""
    
    for record in all_entities:
        for entity_type, values in record['entities'].items():
            for value in values:
                value_escaped = value.replace("'", "''")
                sql += f"""INSERT INTO named_entities (entity_type, entity_value, source_column, source_row)
VALUES ('{entity_type}', '{value_escaped}', '{record['source_column']}', {record['source_row']});
"""
    
    print(f"Extracted {len(all_entities)} records with entities")
else:
    sql = "-- No data available for NER"

print(sql)
`,
  
  knowledgeGraphGeneration: `# Knowledge Graph Generation Template
import pandas as pd
import json
from collections import defaultdict

def build_knowledge_graph(df):
    """Build a knowledge graph from relational data"""
    
    graph = {
        'nodes': [],
        'edges': [],
        'communities': []
    }
    
    # Create nodes from unique values
    node_id_map = {}
    node_counter = 0
    
    for col in df.columns:
        unique_values = df[col].dropna().unique()
        for value in unique_values[:100]:  # Limit for performance
            if str(value) not in node_id_map:
                node_id = f"node_{node_counter}"
                node_id_map[str(value)] = node_id
                graph['nodes'].append({
                    'id': node_id,
                    'label': str(value),
                    'type': col,
                    'properties': {
                        'column': col,
                        'frequency': int(df[col].value_counts().get(value, 1))
                    }
                })
                node_counter += 1
    
    # Create edges based on co-occurrence
    for idx, row in df.iterrows():
        values = [str(v) for v in row.dropna().values]
        for i in range(len(values)):
            for j in range(i+1, len(values)):
                if values[i] in node_id_map and values[j] in node_id_map:
                    graph['edges'].append({
                        'source': node_id_map[values[i]],
                        'target': node_id_map[values[j]],
                        'weight': 1,
                        'type': 'co-occurrence'
                    })
    
    # Detect communities (simple approach - by column)
    for col in df.columns:
        community_nodes = [n for n in graph['nodes'] if n['type'] == col]
        if community_nodes:
            graph['communities'].append({
                'id': f"community_{col}",
                'label': col,
                'nodes': [n['id'] for n in community_nodes],
                'size': len(community_nodes)
            })
    
    return graph

# Process the data
if 'df' in locals():
    graph = build_knowledge_graph(df.head(100))  # Limit rows for performance
    
    # Generate SQL for graph storage
    sql = """
CREATE TABLE IF NOT EXISTS graph_nodes (
    node_id VARCHAR(50) PRIMARY KEY,
    label TEXT,
    node_type VARCHAR(100),
    properties JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS graph_edges (
    edge_id INTEGER PRIMARY KEY AUTOINCREMENT,
    source_node VARCHAR(50),
    target_node VARCHAR(50),
    edge_type VARCHAR(50),
    weight FLOAT DEFAULT 1.0,
    properties JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (source_node) REFERENCES graph_nodes(node_id),
    FOREIGN KEY (target_node) REFERENCES graph_nodes(node_id)
);

CREATE INDEX idx_source ON graph_edges(source_node);
CREATE INDEX idx_target ON graph_edges(target_node);

"""
    
    # Insert nodes
    for node in graph['nodes']:
        label_escaped = node['label'].replace("'", "''")
        props_json = json.dumps(node['properties']).replace("'", "''")
        sql += f"""INSERT INTO graph_nodes (node_id, label, node_type, properties)
VALUES ('{node['id']}', '{label_escaped}', '{node['type']}', '{props_json}');
"""
    
    # Insert edges
    for edge in graph['edges'][:500]:  # Limit edges for SQL size
        sql += f"""INSERT INTO graph_edges (source_node, target_node, edge_type, weight)
VALUES ('{edge['source']}', '{edge['target']}', '{edge['type']}', {edge['weight']});
"""
    
    print(f"Generated graph with {len(graph['nodes'])} nodes and {len(graph['edges'])} edges")
else:
    sql = "-- No data available for knowledge graph"

print(sql)
`,

  documentExtraction: `# Document Extraction Template (Unstructured.io style)
import pandas as pd
import json
from pathlib import Path

# Install required packages:
# uv pip install unstructured pypdf2 python-docx pillow pytesseract

try:
    from unstructured.partition.auto import partition
    from unstructured.chunking.title import chunk_by_title
    from unstructured.staging.base import convert_to_dataframe
except ImportError:
    print("Installing unstructured...")
    import subprocess
    subprocess.check_call(["uv", "pip", "install", "unstructured[all-docs]"])
    from unstructured.partition.auto import partition
    from unstructured.chunking.title import chunk_by_title

def extract_document(file_path):
    """Extract structured data from various document formats"""
    
    # Partition the document into elements
    elements = partition(filename=file_path)
    
    # Convert to structured format
    structured_data = []
    for element in elements:
        structured_data.append({
            "type": element.category,
            "text": str(element),
            "metadata": element.metadata.to_dict() if hasattr(element.metadata, 'to_dict') else {}
        })
    
    # Create DataFrame
    df = pd.DataFrame(structured_data)
    
    # Generate SQL for document storage
    table_name = 'extracted_documents'
    sql = f"""
CREATE TABLE IF NOT EXISTS {table_name} (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    type VARCHAR(50),
    text TEXT,
    metadata JSON,
    page_number INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
"""
    
    for idx, row in df.iterrows():
        metadata_json = json.dumps(row['metadata'])
        text_escaped = row['text'].replace("'", "''")
        sql += f"""
INSERT INTO {table_name} (type, text, metadata, page_number)
VALUES ('{row['type']}', '{text_escaped}', '{metadata_json}', {row.get('page_number', 0)});
"""
    
    return sql
`,

  chunkingPipeline: `# Advanced Chunking Pipeline
import pandas as pd
import numpy as np
import json
import hashlib

def create_chunks(df, chunk_size=1000, overlap=200):
    """Create overlapping chunks for vector databases"""
    
    chunks = []
    text_column = df.columns[0] if 'text' not in df.columns else 'text'
    
    for idx, row in df.iterrows():
        text = str(row[text_column])
        
        # Split into chunks with overlap
        for i in range(0, len(text), chunk_size - overlap):
            chunk_text = text[i:i + chunk_size]
            
            if len(chunk_text) > 100:  # Min chunk size
                chunk_id = hashlib.md5(chunk_text.encode()).hexdigest()
                chunks.append({
                    'chunk_id': chunk_id,
                    'source_id': idx,
                    'text': chunk_text,
                    'start_pos': i,
                    'end_pos': min(i + chunk_size, len(text)),
                    'metadata': json.dumps({
                        'source_row': idx,
                        'chunk_number': i // (chunk_size - overlap)
                    })
                })
    
    # Create chunks DataFrame
    chunks_df = pd.DataFrame(chunks)
    
    # Generate SQL for vector database
    sql = f"""
CREATE TABLE IF NOT EXISTS document_chunks (
    chunk_id VARCHAR(32) PRIMARY KEY,
    source_id INTEGER,
    text TEXT,
    start_pos INTEGER,
    end_pos INTEGER,
    metadata JSON,
    embedding BLOB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_source_id ON document_chunks(source_id);
"""
    
    for _, chunk in chunks_df.iterrows():
        text_escaped = chunk['text'].replace("'", "''")
        sql += f"""
INSERT INTO document_chunks (chunk_id, source_id, text, start_pos, end_pos, metadata)
VALUES ('{chunk['chunk_id']}', {chunk['source_id']}, '{text_escaped}', 
        {chunk['start_pos']}, {chunk['end_pos']}, '{chunk['metadata']}');
"""
    
    return sql
`,

  embeddingPipeline: `# Embedding Generation Pipeline
import pandas as pd
import numpy as np
import json

# For actual embeddings, install:
# uv pip install sentence-transformers openai tiktoken

def generate_embeddings(df):
    """Generate embeddings for text data"""
    
    try:
        from sentence_transformers import SentenceTransformer
        model = SentenceTransformer('all-MiniLM-L6-v2')
        use_transformer = True
    except ImportError:
        print("Using mock embeddings. Install sentence-transformers for real embeddings.")
        use_transformer = False
    
    text_column = df.columns[0] if 'text' not in df.columns else 'text'
    texts = df[text_column].astype(str).tolist()
    
    if use_transformer:
        embeddings = model.encode(texts)
    else:
        # Mock embeddings for demo
        embeddings = np.random.randn(len(texts), 384)
    
    # Create table for embeddings
    sql = f"""
CREATE TABLE IF NOT EXISTS text_embeddings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    text TEXT,
    embedding BLOB,
    embedding_model VARCHAR(100),
    dimension INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_embeddings_created ON text_embeddings(created_at);
"""
    
    for idx, (text, embedding) in enumerate(zip(texts, embeddings)):
        text_escaped = text[:1000].replace("'", "''")  # Truncate long texts
        embedding_bytes = embedding.tobytes()
        embedding_hex = embedding_bytes.hex()
        
        sql += f"""
INSERT INTO text_embeddings (text, embedding, embedding_model, dimension)
VALUES ('{text_escaped}', X'{embedding_hex}', 'all-MiniLM-L6-v2', {len(embedding)});
"""
    
    return sql
`,

  namedEntityRecognition: `# Named Entity Recognition Pipeline
import pandas as pd
import json
import re

def extract_entities(df):
    """Extract named entities from text"""
    
    # Simple regex-based entity extraction (for demo)
    # For production, use spaCy or transformers
    
    entities = []
    text_column = df.columns[0] if 'text' not in df.columns else 'text'
    
    for idx, row in df.iterrows():
        text = str(row[text_column])
        
        # Extract emails
        emails = re.findall(r'\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Z|a-z]{2,}\\b', text)
        
        # Extract phone numbers
        phones = re.findall(r'\\b\\d{3}[-.]?\\d{3}[-.]?\\d{4}\\b', text)
        
        # Extract URLs
        urls = re.findall(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', text)
        
        # Extract potential names (capitalized words)
        names = re.findall(r'\\b[A-Z][a-z]+ [A-Z][a-z]+\\b', text)
        
        # Extract numbers with potential currency
        amounts = re.findall(r'\\$?\\d+(?:,\\d{3})*(?:\\.\\d{2})?', text)
        
        for email in emails:
            entities.append({'source_id': idx, 'entity_type': 'EMAIL', 'entity_value': email})
        for phone in phones:
            entities.append({'source_id': idx, 'entity_type': 'PHONE', 'entity_value': phone})
        for url in urls:
            entities.append({'source_id': idx, 'entity_type': 'URL', 'entity_value': url})
        for name in names[:5]:  # Limit names
            entities.append({'source_id': idx, 'entity_type': 'PERSON', 'entity_value': name})
        for amount in amounts[:5]:  # Limit amounts
            entities.append({'source_id': idx, 'entity_type': 'MONEY', 'entity_value': amount})
    
    entities_df = pd.DataFrame(entities)
    
    # Generate SQL
    sql = f"""
CREATE TABLE IF NOT EXISTS named_entities (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    source_id INTEGER,
    entity_type VARCHAR(50),
    entity_value TEXT,
    confidence FLOAT DEFAULT 1.0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_entity_type ON named_entities(entity_type);
CREATE INDEX idx_source_id ON named_entities(source_id);
"""
    
    for _, entity in entities_df.iterrows():
        value_escaped = str(entity['entity_value']).replace("'", "''")
        sql += f"""
INSERT INTO named_entities (source_id, entity_type, entity_value)
VALUES ({entity['source_id']}, '{entity['entity_type']}', '{value_escaped}');
"""
    
    return sql
`,

  knowledgeGraphGeneration: `# Knowledge Graph Generation
import pandas as pd
import json
from collections import defaultdict

def build_knowledge_graph(df):
    """Build a knowledge graph from structured data"""
    
    # Create nodes and edges
    nodes = {}
    edges = []
    
    text_column = df.columns[0] if 'text' not in df.columns else 'text'
    
    for idx, row in df.iterrows():
        text = str(row[text_column])
        
        # Create document node
        doc_node_id = f"doc_{idx}"
        nodes[doc_node_id] = {
            'id': doc_node_id,
            'type': 'document',
            'label': f"Document {idx}",
            'properties': {'text_preview': text[:100]}
        }
        
        # Extract entities (simplified)
        words = text.split()
        for word in words:
            if word.istitle() and len(word) > 3:
                entity_id = f"entity_{word.lower()}"
                if entity_id not in nodes:
                    nodes[entity_id] = {
                        'id': entity_id,
                        'type': 'entity',
                        'label': word,
                        'properties': {'count': 0}
                    }
                nodes[entity_id]['properties']['count'] += 1
                
                # Create edge
                edges.append({
                    'source': doc_node_id,
                    'target': entity_id,
                    'relationship': 'CONTAINS'
                })
    
    # Generate SQL for graph database
    sql = f"""
-- Knowledge Graph Tables
CREATE TABLE IF NOT EXISTS kg_nodes (
    node_id VARCHAR(100) PRIMARY KEY,
    node_type VARCHAR(50),
    label TEXT,
    properties JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS kg_edges (
    edge_id INTEGER PRIMARY KEY AUTOINCREMENT,
    source_id VARCHAR(100),
    target_id VARCHAR(100),
    relationship VARCHAR(50),
    properties JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (source_id) REFERENCES kg_nodes(node_id),
    FOREIGN KEY (target_id) REFERENCES kg_nodes(node_id)
);

CREATE INDEX idx_kg_node_type ON kg_nodes(node_type);
CREATE INDEX idx_kg_edge_rel ON kg_edges(relationship);
"""
    
    # Insert nodes
    for node_id, node in nodes.items():
        props_json = json.dumps(node['properties'])
        label_escaped = node['label'].replace("'", "''")
        sql += f"""
INSERT INTO kg_nodes (node_id, node_type, label, properties)
VALUES ('{node_id}', '{node['type']}', '{label_escaped}', '{props_json}');
"""
    
    # Insert edges
    for edge in edges[:1000]:  # Limit edges for performance
        sql += f"""
INSERT INTO kg_edges (source_id, target_id, relationship)
VALUES ('{edge['source']}', '{edge['target']}', '{edge['relationship']}');
"""
    
    return sql
`,

  dataQualityAnalysis: `# Data Quality Analysis
import pandas as pd
import numpy as np
import json

def analyze_data_quality(df):
    """Comprehensive data quality analysis"""
    
    quality_report = {
        'total_rows': len(df),
        'total_columns': len(df.columns),
        'column_analysis': {}
    }
    
    for col in df.columns:
        col_analysis = {
            'data_type': str(df[col].dtype),
            'null_count': df[col].isnull().sum(),
            'null_percentage': (df[col].isnull().sum() / len(df)) * 100,
            'unique_count': df[col].nunique(),
            'unique_percentage': (df[col].nunique() / len(df)) * 100
        }
        
        if df[col].dtype in ['int64', 'float64']:
            col_analysis.update({
                'mean': float(df[col].mean()),
                'median': float(df[col].median()),
                'std': float(df[col].std()),
                'min': float(df[col].min()),
                'max': float(df[col].max())
            })
        else:
            # Text analysis
            text_lengths = df[col].astype(str).str.len()
            col_analysis.update({
                'avg_length': float(text_lengths.mean()),
                'max_length': int(text_lengths.max()),
                'min_length': int(text_lengths.min())
            })
        
        quality_report['column_analysis'][col] = col_analysis
    
    # Generate SQL for quality metrics
    sql = f"""
CREATE TABLE IF NOT EXISTS data_quality_metrics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    dataset_name VARCHAR(100),
    column_name VARCHAR(100),
    metric_name VARCHAR(50),
    metric_value TEXT,
    analysis_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_quality_dataset ON data_quality_metrics(dataset_name);
CREATE INDEX idx_quality_column ON data_quality_metrics(column_name);
"""
    
    dataset_name = 'imported_data'
    for col, metrics in quality_report['column_analysis'].items():
        for metric_name, metric_value in metrics.items():
            sql += f"""
INSERT INTO data_quality_metrics (dataset_name, column_name, metric_name, metric_value)
VALUES ('{dataset_name}', '{col}', '{metric_name}', '{metric_value}');
"""
    
    # Add overall metrics
    sql += f"""
INSERT INTO data_quality_metrics (dataset_name, column_name, metric_name, metric_value)
VALUES ('{dataset_name}', '_overall', 'total_rows', '{quality_report['total_rows']}');

INSERT INTO data_quality_metrics (dataset_name, column_name, metric_name, metric_value)
VALUES ('{dataset_name}', '_overall', 'total_columns', '{quality_report['total_columns']}');
"""
    
    return sql
`
};

export const DataCenter: React.FC<DataCenterProps> = ({ onBack, onDataChange }) => {
  const {
    pipelineState,
    updatePipelineState,
    resetPipeline,
    navigateToStage,
    updateContext,
    addToBreadcrumb,
    canUndo,
    canRedo,
    undo,
    redo
  } = useDataCenter();
  
  const [isProcessing, setIsProcessing] = useState(false);
  const [envStatus, setEnvStatus] = useState<PythonEnvStatus>({
    isInstalled: false,
    isActive: false
  });
  const [isSettingUpEnv, setIsSettingUpEnv] = useState(false);
  const [processingStatus, setProcessingStatus] = useState<string>('');
  
  // Context menu state
  const [contextMenu, setContextMenu] = useState<{
    visible: boolean;
    x: number;
    y: number;
    context: any;
  }>({
    visible: false,
    x: 0,
    y: 0,
    context: null
  });

  // Initialize Python environment and report scheduler on mount
  useEffect(() => {
    checkAndSetupPythonEnv();
    
    // Initialize storage and report scheduler
    const initializeServices = async () => {
      try {
        await storageManager.initialize();
        await reportScheduler.initialize();
        console.log('DataCenter services initialized');
      } catch (error) {
        console.error('Failed to initialize DataCenter services:', error);
      }
    };
    
    initializeServices();
    
    // Cleanup on unmount
    return () => {
      reportScheduler.stop();
    };
  }, []);

  // Context menu handlers
  const showContextMenu = (e: React.MouseEvent, context: any) => {
    e.preventDefault();
    setContextMenu({
      visible: true,
      x: e.clientX,
      y: e.clientY,
      context
    });
  };

  const hideContextMenu = () => {
    setContextMenu({
      visible: false,
      x: 0,
      y: 0,
      context: null
    });
  };

  const getContextActions = () => {
    const actions = [];
    
    if (pipelineState.currentStage === 'preview' && pipelineState.dataPreview) {
      actions.push(
        {
          label: 'Apply Filter',
          icon: <Filter className="w-4 h-4" />,
          action: () => {
            // Implement filter logic
            console.log('Apply filter to data');
          }
        },
        {
          label: 'Sort Column',
          icon: <SortAsc className="w-4 h-4" />,
          action: () => {
            // Implement sort logic
            console.log('Sort column');
          }
        },
        {
          label: 'Generate Chart',
          icon: <BarChart className="w-4 h-4" />,
          action: () => {
            // Navigate to explore tab for visualization
            navigateToStage('explore');
          }
        }
      );
    }
    
    if (pipelineState.currentStage === 'explore') {
      actions.push(
        {
          label: 'Export Selection',
          icon: <Download className="w-4 h-4" />,
          action: () => {
            // Implement export logic
            console.log('Export selection');
          }
        },
        {
          label: 'Apply Template',
          icon: <FileCode className="w-4 h-4" />,
          action: () => {
            // Navigate to processing tab
            navigateToStage('processing');
          }
        }
      );
    }
    
    if (pipelineState.currentStage === 'processing') {
      actions.push(
        {
          label: 'Run Template',
          icon: <Play className="w-4 h-4" />,
          action: () => {
            // Run the selected template
            generateSQL();
          }
        },
        {
          label: 'Edit Template',
          icon: <Edit className="w-4 h-4" />,
          action: () => {
            // Focus on template editor
            console.log('Edit template');
          }
        }
      );
    }
    
    return actions;
  };

  // Execute shell command helper - Updated to avoid shell permission issues
  const executeCommand = async (commandStr: string): Promise<string> => {
    try {
      // Parse command string to separate command and arguments
      const parts = commandStr.split(' ');
      const cmd = parts[0];
      const args = parts.slice(1);
      
      // Special handling for complex commands that need shell
      if (commandStr.includes('|') || commandStr.includes('&&') || commandStr.includes(';')) {
        // For complex commands, try to use python to execute
        const pythonCmd = new Command('python3', ['-c', `import os; os.system('''${commandStr}''')`]);
        const output = await pythonCmd.execute();
        return output.stdout;
      }
      
      // Direct command execution without shell
      const command = new Command(cmd, args);
      const output = await command.execute();
      
      if (output.code !== 0) {
        throw new Error(output.stderr || `Command failed with code ${output.code}`);
      }
      
      return output.stdout;
    } catch (error) {
      console.error('Command execution error:', error);
      // Fallback to python execution for compatibility
      try {
        const pythonCmd = new Command('python3', ['-c', `import subprocess; result = subprocess.run('''${commandStr}''', shell=True, capture_output=True, text=True); print(result.stdout)`]);
        const output = await pythonCmd.execute();
        return output.stdout;
      } catch (fallbackError) {
        throw error;
      }
    }
  };

  // Write file helper - Using Python for reliable file writing
  const writeFile = async (path: string, contents: string): Promise<void> => {
    try {
      // Use Python to write file reliably
      const pythonScript = `
import os
content = '''${contents.replace(/'/g, "\\'")}'''
with open('${path}', 'w') as f:
    f.write(content)
print(f'File written to {path}')
`;
      const command = new Command('python3', ['-c', pythonScript]);
      await command.execute();
    } catch (error) {
      console.error('File write error:', error);
      throw error;
    }
  };

  // Check and setup Python environment with uv
  const checkAndSetupPythonEnv = async () => {
    setIsSettingUpEnv(true);
    try {
      // Check if uv is installed
      let uvInstalled = false;
      try {
        // Try to run uv directly (it might be in PATH)
        const uvCommand = new Command('uv', ['--version']);
        const uvOutput = await uvCommand.execute();
        if (uvOutput.code === 0) {
          uvInstalled = true;
          console.log('uv is installed:', uvOutput.stdout);
        }
      } catch (error) {
        console.log('uv not found, will install');
      }

      if (!uvInstalled) {
        console.log('Installing uv...');
        // Use curl to download and install uv
        const curlCommand = new Command('curl', ['-LsSf', 'https://astral.sh/uv/install.sh']);
        const installScript = await curlCommand.execute();
        if (installScript.code === 0) {
          // Execute the install script
          const shCommand = new Command('sh', ['-c', installScript.stdout]);
          await shCommand.execute();
        }
      }

      // Create and activate virtual environment for DataCenter
      const venvPath = '.venv-datacenter';
      
      // Create venv if it doesn't exist using Python check
      try {
        const checkVenvScript = `
import os
import sys
venv_path = '${venvPath}'
if not os.path.exists(venv_path):
    print('Creating virtual environment...')
    os.system(f'uv venv {venv_path}')
else:
    print('Virtual environment already exists')
`;
        const checkCommand = new Command('python3', ['-c', checkVenvScript]);
        await checkCommand.execute();
        console.log('Virtual environment ready');
      } catch (error) {
        console.log('Venv creation note:', error);
        // Try direct uv command as fallback
        try {
          const uvVenvCommand = new Command('uv', ['venv', venvPath]);
          await uvVenvCommand.execute();
        } catch (fallbackError) {
          console.log('Could not create venv:', fallbackError);
        }
      }

      // Install required packages
      console.log('Installing Python packages...');
      const packages = [
        'pandas', 'numpy', 'openpyxl', 'xlsxwriter', 'sqlalchemy',
        'pypdf2', 'python-docx', 'pillow', 'beautifulsoup4', 'lxml',
        'scikit-learn', 'nltk', 'spacy', 'transformers', 'sentence-transformers'
      ];
      
      // Install packages using direct uv commands
      for (let i = 0; i < packages.length; i += 5) {
        const batch = packages.slice(i, i + 5);
        try {
          const uvInstallCommand = new Command('uv', ['pip', 'install', '--python', venvPath, ...batch]);
          await uvInstallCommand.execute();
        } catch (error) {
          console.log(`Some packages in batch failed to install: ${batch.join(' ')}`, error);
        }
      }

      // Check Python version using direct command
      let pythonVersion = 'Unknown';
      try {
        const versionCommand = new Command('uv', ['run', '--python', venvPath, 'python', '--version']);
        const versionOutput = await versionCommand.execute();
        pythonVersion = versionOutput.stdout.trim();
      } catch (error) {
        console.log('Could not get Python version:', error);
      }

      // List installed packages using direct command
      let installedPackages = '[]';
      try {
        const listCommand = new Command('uv', ['pip', 'list', '--python', venvPath, '--format=json']);
        const listOutput = await listCommand.execute();
        installedPackages = listOutput.stdout;
      } catch (error) {
        console.log('Could not list packages:', error);
      }

      setEnvStatus({
        isInstalled: true,
        isActive: true,
        pythonVersion: pythonVersion.trim(),
        pipPackages: JSON.parse(installedPackages).map((pkg: any) => pkg.name)
      });
      
      console.log('Python environment ready:', pythonVersion);
    } catch (error) {
      console.error('Failed to setup Python environment:', error);
      setEnvStatus({
        isInstalled: false,
        isActive: false,
        error: error instanceof Error ? error.message : 'Failed to setup environment'
      });
    } finally {
      setIsSettingUpEnv(false);
    }
  };

  // Parse uploaded file
  const parseFile = async (file: File) => {
    setIsProcessing(true);
    
    try {
      // Save file to storage
      const category = file.name.endsWith('.csv') || file.name.endsWith('.xlsx') ? 'data' : 'document';
      const tags = [file.type, new Date().toISOString().split('T')[0]];
      
      const storedFile = await storageManager.storeFile(file, category, tags, {
        uploadedAt: new Date(),
        source: 'DataCenter Upload',
        originalSize: file.size
      });
      
      updatePipelineState({
        fileName: file.name,
        fileType: file.type || file.name.split('.').pop() || 'unknown',
        fileSize: file.size,
        uploadDate: new Date(),
        storageFile: storedFile
      });
      
      // Add to breadcrumb
      addToBreadcrumb('upload', file.name, {
        fileType: file.type || file.name.split('.').pop() || 'unknown',
        size: `${(file.size / 1024).toFixed(2)} KB`
      });
      
      console.log(`File saved to storage: ${file.name}`);
      
      if (file.name.endsWith('.csv')) {
        // Parse CSV
        Papa.parse(file, {
          header: false,
          skipEmptyLines: true,
          complete: (results) => {
            if (results.data.length > 0) {
              const headers = results.data[0] as string[];
              const rows = results.data.slice(1) as any[][];
              
              // Detect data types
              const dataTypes: Record<string, string> = {};
              headers.forEach((header, idx) => {
                const columnData = rows.map(row => row[idx]);
                dataTypes[header] = detectDataType(columnData);
              });
              
              updatePipelineState({
                dataPreview: {
                  headers,
                  rows: rows.slice(0, 10),
                  totalRows: rows.length,
                  dataTypes,
                  statistics: calculateStatistics(headers, rows),
                  fullData: rows // Store full dataset
                },
                currentStage: 'preview'
              });
              
              // Add to breadcrumb
              addToBreadcrumb('preview', 'Data Preview', {
                rows: rows.length,
                columns: headers.length,
                fileType: 'CSV'
              });
            }
            setIsProcessing(false);
          },
          error: (error) => {
            console.error('CSV parsing error:', error);
            setIsProcessing(false);
          }
        });
      } else if (file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
        // Parse Excel
        const data = await file.arrayBuffer();
        const workbook = XLSX.read(data);
        const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
        const jsonData = XLSX.utils.sheet_to_json(firstSheet, { header: 1 });
        
        if (jsonData.length > 0) {
          const headers = jsonData[0] as string[];
          const rows = jsonData.slice(1) as any[][];
          
          // Detect data types
          const dataTypes: Record<string, string> = {};
          headers.forEach((header, idx) => {
            const columnData = rows.map(row => row[idx]);
            dataTypes[header] = detectDataType(columnData);
          });
          
          updatePipelineState({
            dataPreview: {
              headers,
              rows: rows.slice(0, 10),
              totalRows: rows.length,
              dataTypes,
              statistics: calculateStatistics(headers, rows),
              fullData: rows // Store full dataset
            },
            currentStage: 'preview'
          });
          
          // Add to breadcrumb
          addToBreadcrumb('preview', 'Data Preview', {
            rows: rows.length,
            columns: headers.length,
            fileType: file.name.endsWith('.xlsx') ? 'Excel' : 'Spreadsheet'
          });
        }
        setIsProcessing(false);
      } else if (file.name.endsWith('.json')) {
        // Parse JSON
        const text = await file.text();
        const jsonData = JSON.parse(text);
        
        // Convert JSON to tabular format
        const flat = Array.isArray(jsonData) ? jsonData : [jsonData];
        if (flat.length > 0) {
          const headers = Object.keys(flat[0]);
          const rows = flat.map(item => headers.map(h => item[h]));
          
          updatePipelineState({
            dataPreview: {
              headers,
              rows: rows.slice(0, 10),
              totalRows: rows.length,
              fullData: rows // Store full dataset
            },
            currentStage: 'preview'
          });
          
          // Add to breadcrumb
          addToBreadcrumb('preview', 'Data Preview', {
            rows: rows.length,
            columns: headers.length,
            fileType: 'JSON'
          });
        }
        setIsProcessing(false);
      } else {
        // For other file types (PDF, DOCX, images, etc.), use DocumentProcessor
        try {
          const processor = DocumentProcessor.getInstance();
          const tempPath = `/tmp/datacenter_upload_${Date.now()}_${file.name}`;
          const buffer = await file.arrayBuffer();
          const bytes = new Uint8Array(buffer);
          
          // Convert to base64 for safe transfer
          const base64 = btoa(String.fromCharCode(...bytes));
          
          // For now, set basic preview and use Python processing
          // Full DocumentProcessor integration would require file system access
          setDataPreview({
            headers: ['Document Type', 'Status', 'Details'],
            rows: [
              [file.type || 'Unknown', 'Ready for Processing', file.name],
              ['Size', `${(file.size / 1024).toFixed(2)} KB`, ''],
              ['Processing', 'Use Python templates for extraction', '']
            ],
            totalRows: 3,
            statistics: {
              fileName: file.name,
              fileSize: file.size,
              fileType: file.type,
              uploadTime: new Date().toISOString()
            }
          });
          
          // Store file data for Python processing
          setProcessingResult({
            content: base64,
            metadata: {
              fileName: file.name,
              fileType: file.type,
              fileSize: file.size,
              tempPath
            }
          });
          
          setActiveTab('processing');
          setIsProcessing(false);
        } catch (error) {
          console.error('Document processing error:', error);
          setProcessingStatus(`Error: ${error instanceof Error ? error.message : String(error)}`);
          setIsProcessing(false);
        }
      }
    } catch (error) {
      console.error('File parsing error:', error);
      setIsProcessing(false);
    }
  };

  // Detect data type of a column
  const detectDataType = (columnData: any[]): string => {
    const sample = columnData.filter(v => v !== null && v !== undefined).slice(0, 100);
    if (sample.length === 0) return 'empty';
    
    const allNumbers = sample.every(v => !isNaN(Number(v)));
    if (allNumbers) return 'number';
    
    const allDates = sample.every(v => !isNaN(Date.parse(v)));
    if (allDates) return 'date';
    
    const allBooleans = sample.every(v => 
      typeof v === 'boolean' || v === 'true' || v === 'false' || v === '0' || v === '1'
    );
    if (allBooleans) return 'boolean';
    
    return 'text';
  };

  // Calculate statistics for numerical columns
  const calculateStatistics = (headers: string[], rows: any[][]): Record<string, any> => {
    const stats: Record<string, any> = {};
    
    headers.forEach((header, idx) => {
      const columnData = rows.map(row => row[idx]).filter(v => v !== null && v !== undefined);
      const numbers = columnData.filter(v => !isNaN(Number(v))).map(Number);
      
      if (numbers.length > 0) {
        stats[header] = {
          count: numbers.length,
          mean: numbers.reduce((a, b) => a + b, 0) / numbers.length,
          min: Math.min(...numbers),
          max: Math.max(...numbers),
          nulls: rows.length - columnData.length
        };
      }
    });
    
    return stats;
  };

  // Handle file drop
  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles.length > 0) {
      parseFile(acceptedFiles[0]);
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/csv': ['.csv'],
      'application/vnd.ms-excel': ['.xls'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'application/json': ['.json'],
      'application/pdf': ['.pdf'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'text/plain': ['.txt'],
      'text/html': ['.html'],
      'image/*': ['.png', '.jpg', '.jpeg', '.gif']
    },
    maxFiles: 1
  });

  // Execute Python script with uv
  const executePythonScript = async (script: string, dataPath?: string) => {
    if (!envStatus.isActive) {
      throw new Error('Python environment is not active. Please wait for setup to complete.');
    }

    const tempScriptPath = `/tmp/datacenter_${Date.now()}.py`;
    
    // Prepare the full Python script with data loading
    const fullScript = `
import pandas as pd
import numpy as np
import json
import sys
import warnings
warnings.filterwarnings('ignore')

# Load the data
${dataPath ? `
try:
    if '${dataPath}'.endswith('.csv'):
        df = pd.read_csv('${dataPath}')
    elif '${dataPath}'.endswith('.json'):
        df = pd.read_json('${dataPath}')
    elif '${dataPath}'.endswith(('.xlsx', '.xls')):
        df = pd.read_excel('${dataPath}')
    else:
        # For other formats, create a simple DataFrame
        df = pd.DataFrame({'file': ['${dataPath}']})
except Exception as e:
    print(f"Error loading data: {e}")
    df = pd.DataFrame()
` : 'df = pd.DataFrame()'}

# User script
${script}

# If the script doesn't explicitly return SQL, try to generate it
if 'sql' not in locals():
    sql = "-- No SQL generated"

print(sql)
`;

    // Write the script to a temporary file
    await writeFile(tempScriptPath, fullScript);

    // Execute the script with uv using direct command
    const venvPath = '.venv-datacenter';
    const uvRunCommand = new Command('uv', ['run', '--python', venvPath, 'python', tempScriptPath]);
    const runOutput = await uvRunCommand.execute();
    const output = runOutput.stdout;

    // Clean up the temporary file using Python
    const cleanupCommand = new Command('python3', ['-c', `import os; os.remove('${tempScriptPath}')`]);
    await cleanupCommand.execute();

    return output;
  };

  // Generate SQL from Python script
  const generateSQL = async () => {
    if (!pipelineState.pythonScript.trim() || !pipelineState.dataPreview) return;
    
    setIsProcessing(true);
    
    try {
      // Save data to temporary CSV for Python processing
      const tempDataPath = `/tmp/datacenter_data_${Date.now()}.csv`;
      const csvContent = [
        pipelineState.dataPreview.headers.join(','),
        ...pipelineState.dataPreview.rows.map(row => row.join(','))
      ].join('\n');
      
      await writeFile(tempDataPath, csvContent);

      // Execute the Python script with uv
      const output = await executePythonScript(pipelineState.pythonScript, tempDataPath);
      
      // Clean up temporary data file using Python
      const cleanupDataCommand = new Command('python3', ['-c', `import os; os.remove('${tempDataPath}')`]);
      await cleanupDataCommand.execute();

      updatePipelineState({
        pythonOutput: output,
        sqlResult: {
          sql: output,
          isValid: true,
          analysis: `Generated SQL using Python with ${pipelineState.dataPreview.totalRows} rows and ${pipelineState.dataPreview.headers.length} columns.`
        },
        currentStage: 'sql'
      });
      
      // Add to breadcrumb
      addToBreadcrumb('sql', 'SQL Generated', {
        rows: pipelineState.dataPreview.totalRows,
        columns: pipelineState.dataPreview.headers.length,
        template: pipelineState.selectedTemplate
      });
      
      // Save generated SQL to storage
      const sqlBlob = new Blob([output], { type: 'text/sql' });
      const sqlFile = new File([sqlBlob], `${pipelineState.fileName}_generated.sql`, { type: 'text/sql' });
      await storageManager.storeFile(sqlFile, 'export', ['sql', 'generated'], {
        sourceFile: pipelineState.fileName,
        generatedAt: new Date(),
        template: pipelineState.selectedTemplate,
        rowCount: pipelineState.dataPreview.totalRows,
        columnCount: pipelineState.dataPreview.headers.length
      });
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to generate SQL';
      updatePipelineState({
        pythonOutput: `Error: ${errorMessage}`,
        sqlResult: {
          sql: '',
          isValid: false,
          error: errorMessage
        }
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Download SQL file
  const downloadSQL = async () => {
    if (!sqlResult?.sql) return;
    
    try {
      const filePath = await save({
        defaultPath: `${fileName.replace(/\.[^/.]+$/, '')}_generated.sql`,
        filters: [{
          name: 'SQL',
          extensions: ['sql']
        }]
      });
      
      if (filePath) {
        await writeTextFile(filePath, sqlResult.sql);
        console.log('SQL file saved to:', filePath);
      }
    } catch (error) {
      console.error('Failed to save SQL file:', error);
      // Fallback to browser download
      const blob = new Blob([sqlResult.sql], { type: 'text/sql' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${fileName.replace(/\.[^/.]+$/, '')}_generated.sql`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  // Export to various formats
  const exportData = async (format: 'csv' | 'json' | 'parquet') => {
    if (!dataPreview) return;
    
    let content = '';
    let extension: string = format;
    let mimeType = 'text/plain';
    
    switch (format) {
      case 'csv':
        content = [
          dataPreview.headers.join(','),
          ...dataPreview.rows.map(row => row.join(','))
        ].join('\n');
        mimeType = 'text/csv';
        break;
        
      case 'json':
        const jsonData = dataPreview.rows.map(row => {
          const obj: Record<string, any> = {};
          dataPreview.headers.forEach((header, idx) => {
            obj[header] = row[idx];
          });
          return obj;
        });
        content = JSON.stringify(jsonData, null, 2);
        mimeType = 'application/json';
        break;
        
      case 'parquet':
        // Parquet export would require Python backend
        content = '-- Parquet export requires Python backend processing';
        extension = 'txt';
        break;
    }
    
    try {
      const filePath = await save({
        defaultPath: `${fileName.replace(/\.[^/.]+$/, '')}_export.${extension}`,
        filters: [{
          name: format.toUpperCase(),
          extensions: [extension]
        }]
      });
      
      if (filePath) {
        await writeTextFile(filePath, content);
        console.log(`${format.toUpperCase()} file saved to:`, filePath);
      }
    } catch (error) {
      console.error(`Failed to save ${format} file:`, error);
    }
  };

  return (
    <div className="flex flex-col h-full bg-background">
      {/* Header with Environment Status and Breadcrumb */}
      <div className="flex flex-col border-b bg-card">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-4">
            <h2 className="text-xl font-semibold flex items-center gap-2">
              <Database className="w-5 h-5 text-dataOperation" />
              Data Processing Center
            </h2>
            
            {/* Python Environment Status */}
            <div className="flex items-center gap-2 px-3 py-1 rounded-full bg-muted text-sm">
              {isSettingUpEnv ? (
                <>
                  <Loader2 className="w-3 h-3 animate-spin" />
                  <span>Setting up Python environment...</span>
                </>
              ) : envStatus.isActive ? (
                <>
                  <Check className="w-3 h-3 text-success" />
                  <span className="text-success">Python {envStatus.pythonVersion} (uv)</span>
                </>
              ) : (
                <>
                  <AlertCircle className="w-3 h-3 text-warning" />
                  <span className="text-warning">Python environment not ready</span>
                </>
              )}
            </div>
            
            {/* File Type Indicator */}
            {pipelineState.fileName && (
              <div className="flex items-center gap-2 px-3 py-1 rounded-full bg-muted text-sm">
                {pipelineState.fileType.includes('pdf') ? <FileText className="w-3 h-3 text-dataOperation" /> :
                 pipelineState.fileType.includes('image') ? <FileImage className="w-3 h-3 text-dataOperation" /> :
                 pipelineState.fileType.includes('json') ? <FileJson className="w-3 h-3 text-dataOperation" /> :
                 <FileSpreadsheet className="w-3 h-3 text-dataOperation" />}
                <span className="text-dataOperation">{pipelineState.fileType.split('/').pop() || 'file'}</span>
              </div>
            )}
          </div>
          
          {onBack && (
            <button
              onClick={onBack}
              className="px-3 py-1 text-sm rounded-md hover:bg-muted transition-colors"
            >
              Back
            </button>
          )}
        </div>
        
        {/* Breadcrumb Navigation */}
        {pipelineState.fileName && (
          <div className="px-4 pb-3">
            <Breadcrumb>
              {pipelineState.context.breadcrumbPath.map((crumb, index) => (
                <React.Fragment key={index}>
                  <BreadcrumbItem 
                    completed={true}
                    onClick={() => navigateToStage(crumb.stage as any)}
                    className="cursor-pointer hover:underline"
                    variant={crumb.stage === 'upload' ? 'data' : 
                           crumb.stage === 'preview' ? 'data' : 
                           crumb.stage === 'explore' ? 'data' : 
                           crumb.stage === 'processing' ? 'warning' : 
                           crumb.stage === 'sql' ? 'success' : 'info'}
                  >
                    {crumb.stage === 'upload' && <Home className="w-3 h-3 mr-1" />}
                    {crumb.stage === 'preview' && <Eye className="w-3 h-3 mr-1" />}
                    {crumb.stage === 'explore' && <Zap className="w-3 h-3 mr-1" />}
                    {crumb.stage === 'processing' && <Wrench className="w-3 h-3 mr-1" />}
                    {crumb.stage === 'sql' && <FileOutput className="w-3 h-3 mr-1" />}
                    {crumb.label}
                    {crumb.dataSummary && (
                      <span className="ml-1 text-xs text-muted-foreground">
                        ({crumb.dataSummary.rows} rows, {crumb.dataSummary.columns} cols)
                      </span>
                    )}
                  </BreadcrumbItem>
                  {index < pipelineState.context.breadcrumbPath.length - 1 && (
                    <ChevronRight className="w-4 h-4 text-muted-foreground mx-1" />
                  )}
                </React.Fragment>
              ))}
            </Breadcrumb>
          </div>
        )}
      </div>

      {/* Navigation Tabs */}
      <div className="flex border-b bg-card/50 overflow-x-auto">
        {[
          { id: 'upload', label: 'Upload', icon: Upload, color: 'text-dataOperation' },
          { id: 'storage', label: 'Storage', icon: HardDrive, color: 'text-dataOperation' },
          { id: 'preview', label: 'Preview', icon: Search, color: 'text-dataOperation' },
          { id: 'explore', label: 'Explore', icon: Zap, color: 'text-warning' },
          { id: 'processing', label: 'Processing', icon: Layers, color: 'text-warning' },
          { id: 'analysis', label: 'Analysis', icon: Brain, color: 'text-warning' },
          { id: 'sql', label: 'SQL Output', icon: Database, color: 'text-success' }
        ].map(({ id, label, icon: Icon, color }) => (
          <button
            key={id}
            onClick={() => navigateToStage(id as any)}
            className={`px-4 py-3 text-sm font-medium transition-all flex items-center gap-2 ${
              pipelineState.currentStage === id
                ? `${color} border-b-2 border-current bg-card`
                : 'text-muted-foreground hover:text-foreground hover:bg-muted/50'
            }`}
            disabled={
              (id === 'preview' && !pipelineState.dataPreview) ||
              (id === 'explore' && !pipelineState.dataPreview) ||
              (id === 'sql' && !pipelineState.sqlResult)
            }
          >
            <Icon className={`w-4 h-4 ${color}`} />
            {label}
          </button>
        ))}
      </div>

      {/* Content Area */}
      <div className="flex-1 overflow-auto">
        <FramerAnimatePresence mode="wait">
          {/* Upload Tab */}
          {pipelineState.currentStage === 'upload' && (
            <motion.div
              key="upload"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="p-6"
            >
              <div className="max-w-4xl mx-auto">
                <div
                  {...getRootProps()}
                  className={`p-12 border-2 border-dashed rounded-lg text-center cursor-pointer transition-all ${
                    isDragActive ? 'border-primary bg-primary/5' : 'border-muted-foreground/25 hover:border-primary hover:bg-muted/50'
                  }`}
                >
                  <input {...getInputProps()} />
                  <Upload className="w-16 h-16 mx-auto mb-4 text-muted-foreground" />
                  <p className="text-lg font-medium mb-2">
                    {isDragActive ? 'Drop the file here' : 'Drag & drop files here, or click to select'}
                  </p>
                  <p className="text-sm text-muted-foreground mb-4">
                    Supports: CSV, Excel, JSON, PDF, Word, Images, HTML, and more
                  </p>
                  <div className="flex flex-wrap gap-2 justify-center">
                    {[
                      { icon: FileSpreadsheet, label: 'Spreadsheets' },
                      { icon: FileText, label: 'Documents' },
                      { icon: FileJson, label: 'JSON' },
                      { icon: FileImage, label: 'Images' },
                      { icon: FileCode, label: 'Code' }
                    ].map(({ icon: Icon, label }) => (
                      <div key={label} className="flex items-center gap-1 px-3 py-1 bg-muted rounded-full text-xs">
                        <Icon className="w-3 h-3" />
                        {label}
                      </div>
                    ))}
                  </div>
                </div>
                
                {fileName && (
                  <div className="mt-4 p-4 bg-muted rounded-lg flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <FileSpreadsheet className="w-5 h-5 text-primary" />
                      <div>
                        <p className="font-medium">{fileName}</p>
                        <p className="text-sm text-muted-foreground">Ready for processing</p>
                      </div>
                    </div>
                    <button
                      onClick={() => setActiveTab('preview')}
                      className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors flex items-center gap-2"
                    >
                      Process File
                      <ChevronRight className="w-4 h-4" />
                    </button>
                  </div>
                )}
                
                {/* Quick Start Templates */}
                <div className="mt-8">
                  <h3 className="text-lg font-semibold mb-4">Quick Start Templates</h3>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    {Object.entries(PYTHON_TEMPLATES).map(([key, template]) => (
                      <button
                        key={key}
                        onClick={() => {
                          setPythonScript(template);
                          setSelectedTemplate(key);
                          setActiveTab('analysis');
                        }}
                        className="p-4 bg-card border rounded-lg hover:bg-muted/50 transition-colors text-left"
                      >
                        <Package className="w-5 h-5 text-primary mb-2" />
                        <p className="font-medium text-sm">{key.replace(/([A-Z])/g, ' $1').trim()}</p>
                        <p className="text-xs text-muted-foreground mt-1">
                          {key === 'documentExtraction' && 'Extract structured data from docs'}
                          {key === 'chunkingPipeline' && 'Smart document chunking'}
                          {key === 'dataQualityAnalysis' && 'Comprehensive quality report'}
                          {key === 'correlationAnalysis' && 'Find data relationships'}
                          {key === 'anomalyDetection' && 'Detect outliers & anomalies'}
                          {key === 'embeddingPipeline' && 'Generate embeddings'}
                          {key === 'namedEntityRecognition' && 'Extract entities'}
                          {key === 'knowledgeGraphGeneration' && 'Build knowledge graphs'}
                          {key === 'vectorDatabaseIntegration' && 'Store in vector DBs'}
                          {key === 'knowledgeGraphExtraction' && 'Extract graph data'}
                          {key === 'multiModalProcessing' && 'Process images & audio'}
                          {key === 'dataQualityValidation' && 'Validate & clean data'}
                        </p>
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            </motion.div>
          )}

          {/* Storage Tab */}
          {activeTab === 'storage' && (
            <motion.div
              key="storage"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="h-full"
            >
              <StorageDashboard
                onFileSelect={async (file) => {
                  // Load file from storage for processing
                  try {
                    const { content } = await storageManager.getFile(file.id);
                    // Handle file based on type
                    if (file.type.includes('csv')) {
                      // Parse CSV data
                      const blob = new Blob([atob(content)], { type: file.type });
                      const newFile = new File([blob], file.name, { type: file.type });
                      await parseFile(newFile);
                      setActiveTab('preview');
                    } else if (file.type.includes('json')) {
                      // Parse JSON data
                      const jsonData = JSON.parse(atob(content));
                      // Convert to table format
                      if (Array.isArray(jsonData)) {
                        const headers = Object.keys(jsonData[0] || {});
                        const rows = jsonData.map(item => headers.map(h => item[h]));
                        setDataPreview({
                          headers,
                          rows,
                          totalRows: rows.length,
                          dataTypes: {},
                          statistics: calculateStatistics(headers, rows),
                          fullData: rows
                        });
                        setActiveTab('preview');
                      }
                    }
                  } catch (error) {
                    console.error('Failed to load file from storage:', error);
                  }
                }}
                onGenerateReport={() => {
                  console.log('Monthly report generated');
                }}
              />
            </motion.div>
          )}

          {/* Preview Tab */}
          {pipelineState.currentStage === 'preview' && pipelineState.dataPreview && (
            <motion.div
              key="preview"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="p-6"
            >
              <div className="mb-4 flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className="text-sm text-muted-foreground">
                    Showing {Math.min(10, pipelineState.dataPreview.rows.length)} of {pipelineState.dataPreview.totalRows} rows
                  </div>
                  {pipelineState.dataPreview.statistics && (
                    <div className="flex items-center gap-2 text-sm">
                      <span className="px-2 py-1 bg-dataOperation/10 text-dataOperation rounded">
                        {Object.keys(pipelineState.dataPreview.statistics).length} numeric columns
                      </span>
                      <span className="px-2 py-1 bg-success/10 text-success rounded">
                        {pipelineState.dataPreview.headers.length} total columns
                      </span>
                    </div>
                  )}
                </div>
                <div className="flex gap-2">
                  <button
                    onClick={() => exportData('csv')}
                    className="px-3 py-1 text-sm rounded-md bg-primary text-primary-foreground hover:bg-primary/90 transition-colors flex items-center gap-2"
                  >
                    <Download className="w-3 h-3" />
                    CSV
                  </button>
                  <button
                    onClick={() => exportData('json')}
                    className="px-3 py-1 text-sm rounded-md bg-primary text-primary-foreground hover:bg-primary/90 transition-colors flex items-center gap-2"
                  >
                    <Download className="w-3 h-3" />
                    JSON
                  </button>
                </div>
              </div>
              
              {/* Data Types Row */}
              {dataPreview.dataTypes && (
                <div className="mb-4 p-3 bg-muted rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <Settings className="w-4 h-4" />
                    <span className="text-sm font-medium">Detected Data Types</span>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {Object.entries(dataPreview.dataTypes).map(([column, type]) => (
                      <span key={column} className="px-2 py-1 bg-background rounded text-xs">
                        <strong>{column}:</strong> {type}
                      </span>
                    ))}
                  </div>
                </div>
              )}
              
              <div className="overflow-x-auto bg-card rounded-lg border">
                <table className="w-full">
                  <thead>
                    <tr className="bg-muted/50">
                      {pipelineState.dataPreview.headers.map((header, idx) => (
                        <th 
                          key={idx} 
                          className="px-4 py-3 text-left text-sm font-medium border-b cursor-pointer hover:bg-muted"
                          onContextMenu={(e) => showContextMenu(e, { type: 'column', header, index: idx })}
                        >
                          <div className="flex items-center gap-2">
                            {header}
                            {pipelineState.dataPreview?.dataTypes?.[header] === 'number' && (
                              <span className="text-xs text-dataOperation">📊</span>
                            )}
                            {pipelineState.dataPreview?.dataTypes?.[header] === 'date' && (
                              <span className="text-xs text-warning">📅</span>
                            )}
                          </div>
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    {pipelineState.dataPreview.rows.map((row, rowIdx) => (
                      <tr 
                        key={rowIdx} 
                        className="hover:bg-muted/25 transition-colors"
                        onContextMenu={(e) => showContextMenu(e, { type: 'row', rowIndex: rowIdx, data: row })}
                      >
                        {row.map((cell, cellIdx) => (
                          <td 
                            key={cellIdx} 
                            className="px-4 py-2 text-sm border-b cursor-pointer"
                            onContextMenu={(e) => showContextMenu(e, { type: 'cell', rowIndex: rowIdx, columnIndex: cellIdx, value: cell })}
                          >
                            {String(cell || '')}
                          </td>
                        ))}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              
              {/* Statistics Panel */}
              {dataPreview.statistics && Object.keys(dataPreview.statistics).length > 0 && (
                <div className="mt-4 p-4 bg-muted rounded-lg">
                  <h3 className="font-medium mb-3 flex items-center gap-2">
                    <Brain className="w-4 h-4" />
                    Column Statistics
                  </h3>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    {Object.entries(dataPreview.statistics).slice(0, 8).map(([column, stats]: [string, any]) => (
                      <div key={column} className="bg-background p-3 rounded">
                        <p className="text-xs text-muted-foreground mb-1">{column}</p>
                        <div className="space-y-1">
                          <div className="flex justify-between text-xs">
                            <span>Mean:</span>
                            <span className="font-mono text-dataOperation">{stats.mean?.toFixed(2)}</span>
                          </div>
                          <div className="flex justify-between text-xs">
                            <span>Range:</span>
                            <span className="font-mono text-dataOperation">{stats.min?.toFixed(0)} - {stats.max?.toFixed(0)}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </motion.div>
          )}

          {/* Explore Tab - Interactive Data Exploration */}
          {activeTab === 'explore' && dataPreview && (
            <motion.div
              key="explore"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="h-full"
            >
              <DataExplorationDashboard
                headers={dataPreview.headers}
                rows={dataPreview.fullData || dataPreview.rows}
                dataTypes={dataPreview.dataTypes}
                onFilterApplied={(filteredRows) => {
                  // Update preview with filtered data
                  setDataPreview({
                    ...dataPreview,
                    rows: filteredRows.slice(0, 10),
                    totalRows: filteredRows.length,
                    fullData: filteredRows
                  });
                }}
              />
            </motion.div>
          )}

          {/* Processing Tab - Unstructured.io Style */}
          {activeTab === 'processing' && (
            <motion.div
              key="processing"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="p-6"
            >
              <div className="max-w-6xl mx-auto">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {/* Document Processing Pipeline */}
                  <div className="bg-card p-6 rounded-lg border">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="p-2 bg-primary/10 rounded">
                        <FileText className="w-5 h-5 text-primary" />
                      </div>
                      <div>
                        <h3 className="font-semibold">Document Extraction</h3>
                        <p className="text-xs text-muted-foreground">Extract structured data</p>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <button
                        onClick={() => {
                          setPythonScript(PYTHON_TEMPLATES.documentExtraction);
                          setActiveTab('analysis');
                        }}
                        className="w-full px-3 py-2 text-sm bg-muted hover:bg-muted/70 rounded transition-colors text-left"
                      >
                        Extract Text & Metadata
                      </button>
                      <button
                        onClick={() => {
                          setPythonScript(PYTHON_TEMPLATES.namedEntityRecognition);
                          setActiveTab('analysis');
                        }}
                        className="w-full px-3 py-2 text-sm bg-muted hover:bg-muted/70 rounded transition-colors text-left"
                      >
                        Named Entity Recognition
                      </button>
                    </div>
                  </div>

                  {/* Chunking & Vectorization */}
                  <div className="bg-card p-6 rounded-lg border">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="p-2 bg-purple-500/10 rounded">
                        <Layers className="w-5 h-5 text-purple-600" />
                      </div>
                      <div>
                        <h3 className="font-semibold">Chunking & Vectors</h3>
                        <p className="text-xs text-muted-foreground">Prepare for AI/ML</p>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <button
                        onClick={() => {
                          setPythonScript(PYTHON_TEMPLATES.chunkingPipeline);
                          setActiveTab('analysis');
                        }}
                        className="w-full px-3 py-2 text-sm bg-muted hover:bg-muted/70 rounded transition-colors text-left"
                      >
                        Smart Chunking
                      </button>
                      <button
                        onClick={() => {
                          setPythonScript(PYTHON_TEMPLATES.embeddingPipeline);
                          setActiveTab('analysis');
                        }}
                        className="w-full px-3 py-2 text-sm bg-muted hover:bg-muted/70 rounded transition-colors text-left"
                      >
                        Generate Embeddings
                      </button>
                    </div>
                  </div>

                  {/* Knowledge Graph */}
                  <div className="bg-card p-6 rounded-lg border">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="p-2 bg-green-500/10 rounded">
                        <Brain className="w-5 h-5 text-green-600" />
                      </div>
                      <div>
                        <h3 className="font-semibold">Knowledge Graph</h3>
                        <p className="text-xs text-muted-foreground">Build connections</p>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <button
                        onClick={() => {
                          setPythonScript(PYTHON_TEMPLATES.knowledgeGraphGeneration);
                          setActiveTab('analysis');
                        }}
                        className="w-full px-3 py-2 text-sm bg-muted hover:bg-muted/70 rounded transition-colors text-left"
                      >
                        Build Graph
                      </button>
                      <button
                        onClick={() => {
                          setPythonScript(PYTHON_TEMPLATES.dataQualityAnalysis);
                          setActiveTab('analysis');
                        }}
                        className="w-full px-3 py-2 text-sm bg-muted hover:bg-muted/70 rounded transition-colors text-left"
                      >
                        Quality Analysis
                      </button>
                    </div>
                  </div>
                </div>

                {/* Processing Pipeline Visualization */}
                <div className="mt-8 p-6 bg-muted/30 rounded-lg">
                  <h3 className="font-semibold mb-4 flex items-center gap-2">
                    <Zap className="w-5 h-5 text-yellow-600" />
                    Processing Pipeline
                  </h3>
                  <div className="flex items-center justify-between">
                    {[
                      { icon: Upload, label: 'Input', status: fileName ? 'complete' : 'pending' },
                      { icon: FileText, label: 'Extract', status: 'pending' },
                      { icon: Layers, label: 'Transform', status: 'pending' },
                      { icon: Brain, label: 'Enrich', status: 'pending' },
                      { icon: Database, label: 'Output', status: 'pending' }
                    ].map(({ icon: Icon, label, status }, idx) => (
                      <div key={label} className="flex items-center">
                        <div className={`flex flex-col items-center ${
                          status === 'complete' ? 'text-green-600' : 'text-muted-foreground'
                        }`}>
                          <div className={`p-3 rounded-full ${
                            status === 'complete' ? 'bg-green-500/10' : 'bg-muted'
                          }`}>
                            <Icon className="w-5 h-5" />
                          </div>
                          <span className="text-xs mt-2">{label}</span>
                        </div>
                        {idx < 4 && (
                          <ChevronRight className="w-5 h-5 mx-2 text-muted-foreground" />
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </motion.div>
          )}

          {/* Analysis Tab */}
          {pipelineState.currentStage === 'analysis' && (
            <motion.div
              key="analysis"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="p-6"
            >
              <div className="max-w-6xl mx-auto">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {/* Template Selector */}
                  <div className="lg:col-span-1">
                    <h3 className="font-semibold mb-3">Processing Templates</h3>
                    <div className="space-y-2">
                      {Object.entries(PYTHON_TEMPLATES).map(([key, template]) => (
                        <button
                          key={key}
                          onClick={() => {
                            updatePipelineState({
                              pythonScript: template,
                              selectedTemplate: key
                            });
                          }}
                          className={`w-full px-3 py-2 text-sm rounded-lg transition-colors text-left flex items-center gap-2 ${
                            pipelineState.selectedTemplate === key 
                              ? 'bg-primary text-primary-foreground' 
                              : 'bg-muted hover:bg-muted/70'
                          }`}
                        >
                          <BookOpen className="w-4 h-4" />
                          {key.replace(/([A-Z])/g, ' $1').trim()}
                        </button>
                      ))}
                    </div>
                  </div>
                  
                  {/* Script Editor */}
                  <div className="lg:col-span-2">
                    <div className="flex items-center justify-between mb-3">
                      <label className="font-semibold">Python Script Editor</label>
                      <div className="flex gap-2">
                        <button
                          onClick={() => updatePipelineState({ pythonScript: '' })}
                          className="px-3 py-1 text-sm bg-muted hover:bg-muted/70 rounded transition-colors"
                        >
                          Clear
                        </button>
                        <button
                          onClick={generateSQL}
                          disabled={!pipelineState.pythonScript.trim() || !pipelineState.dataPreview || isProcessing || !envStatus.isActive}
                          className="px-4 py-1 rounded-md bg-primary text-primary-foreground hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 text-sm"
                        >
                          {isProcessing ? (
                            <>
                              <Loader2 className="w-3 h-3 animate-spin" />
                              Processing...
                            </>
                          ) : (
                            <>
                              <Play className="w-3 h-3" />
                              Execute
                            </>
                          )}
                        </button>
                      </div>
                    </div>
                    
                    <div className="relative">
                      <textarea
                        value={pipelineState.pythonScript}
                        onChange={(e) => updatePipelineState({ pythonScript: e.target.value })}
                        placeholder="Write your Python script here... The DataFrame is available as 'df'"
                        className="w-full h-96 px-4 py-3 font-mono text-sm border rounded-lg bg-background resize-none"
                        spellCheck={false}
                      />
                      {pipelineState.selectedTemplate && (
                        <div className="absolute top-2 right-2 px-2 py-1 bg-primary/10 text-primary text-xs rounded">
                          {pipelineState.selectedTemplate.replace(/([A-Z])/g, ' $1').trim()}
                        </div>
                      )}
                    </div>
                    
                    <div className="mt-2 flex items-center justify-between text-sm text-muted-foreground">
                      <div>
                        {pipelineState.dataPreview ? `Data loaded: ${pipelineState.dataPreview.totalRows} rows × ${pipelineState.dataPreview.headers.length} columns` : 'No data loaded'}
                      </div>
                      <div className="flex items-center gap-2">
                        <Code className="w-4 h-4" />
                        Python 3.12 with pandas, numpy, scikit-learn
                      </div>
                    </div>
                  </div>
                </div>

                {/* Python Output */}
                {pipelineState.pythonOutput && (
                  <div className="mt-6 p-4 bg-muted rounded-lg">
                    <div className="flex items-center gap-2 mb-3">
                      <Terminal className="w-4 h-4" />
                      <span className="font-medium">Python Output</span>
                    </div>
                    <pre className="text-xs font-mono whitespace-pre-wrap bg-background p-3 rounded">
                      {pipelineState.pythonOutput}
                    </pre>
                  </div>
                )}
              </div>
            </motion.div>
          )}

          {/* SQL Tab */}
          {pipelineState.currentStage === 'sql' && pipelineState.sqlResult && (
            <motion.div
              key="sql"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="p-6"
            >
              <div className="max-w-6xl mx-auto">
                <div className="mb-4 flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    {pipelineState.sqlResult.isValid ? (
                      <>
                        <div className="p-2 bg-success/10 rounded">
                          <CheckCircle className="w-5 h-5 text-success" />
                        </div>
                        <div>
                          <p className="font-medium">SQL Generated Successfully</p>
                          <p className="text-sm text-muted-foreground">Ready to execute in your database</p>
                        </div>
                      </>
                    ) : (
                      <>
                        <div className="p-2 bg-error/10 rounded">
                          <AlertCircle className="w-5 h-5 text-error" />
                        </div>
                        <div>
                          <p className="font-medium">Generation Failed</p>
                          <p className="text-sm text-error">{sqlResult.error}</p>
                        </div>
                      </>
                    )}
                  </div>
                  <button
                    onClick={downloadSQL}
                    disabled={!sqlResult.isValid}
                    className="px-4 py-2 text-sm rounded-md bg-primary text-primary-foreground hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
                  >
                    <Download className="w-4 h-4" />
                    Download SQL
                  </button>
                </div>
                
                {sqlResult.analysis && (
                  <div className="mb-4 p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg">
                    <p className="text-sm">{sqlResult.analysis}</p>
                  </div>
                )}
                
                <div className="relative">
                  <div className="absolute top-3 right-3 flex gap-2">
                    <button
                      onClick={() => {
                        navigator.clipboard.writeText(sqlResult.sql);
                      }}
                      className="px-3 py-1 text-xs bg-muted hover:bg-muted/70 rounded transition-colors"
                    >
                      Copy
                    </button>
                  </div>
                  <pre className="p-6 bg-muted rounded-lg overflow-x-auto">
                    <code className="text-sm font-mono">{sqlResult.sql || '-- No SQL generated'}</code>
                  </pre>
                </div>
                
                {/* SQL Statistics */}
                {sqlResult.isValid && (
                  <div className="mt-4 grid grid-cols-3 gap-4">
                    <div className="p-4 bg-card rounded-lg border">
                      <p className="text-sm text-muted-foreground">Tables Created</p>
                      <p className="text-2xl font-bold">
                        {(sqlResult.sql.match(/CREATE TABLE/gi) || []).length}
                      </p>
                    </div>
                    <div className="p-4 bg-card rounded-lg border">
                      <p className="text-sm text-muted-foreground">Insert Statements</p>
                      <p className="text-2xl font-bold">
                        {(sqlResult.sql.match(/INSERT INTO/gi) || []).length}
                      </p>
                    </div>
                    <div className="p-4 bg-card rounded-lg border">
                      <p className="text-sm text-muted-foreground">Total Lines</p>
                      <p className="text-2xl font-bold">
                        {sqlResult.sql.split('\n').length}
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </motion.div>
          )}
        </FramerAnimatePresence>
      </div>
      
      {/* Context Menu */}
      <ContextMenu
        options={getContextActions()}
        visible={contextMenu.visible}
        x={contextMenu.x}
        y={contextMenu.y}
        onClose={hideContextMenu}
      />
    </div>
  );
};

const AnimatePresence = motion.div;