import React, { createContext, useContext, useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Contrast,
  Eye,
  Palette,
  Settings,
  Sun,
  Moon,
  Monitor,
  Check
} from 'lucide-react';
import { cn } from '@/lib/utils';

type ContrastLevel = 'normal' | 'high' | 'maximum';
type ColorScheme = 'light' | 'dark' | 'auto';

interface HighContrastSettings {
  enabled: boolean;
  level: ContrastLevel;
  colorScheme: ColorScheme;
  reducedMotion: boolean;
  largeText: boolean;
  boldText: boolean;
  underlineLinks: boolean;
  focusIndicators: boolean;
}

interface HighContrastContextType {
  settings: HighContrastSettings;
  updateSettings: (updates: Partial<HighContrastSettings>) => void;
  toggleHighContrast: () => void;
  applySettings: () => void;
}

const defaultSettings: HighContrastSettings = {
  enabled: false,
  level: 'normal',
  colorScheme: 'auto',
  reducedMotion: false,
  largeText: false,
  boldText: false,
  underlineLinks: false,
  focusIndicators: true
};

const HighContrastContext = createContext<HighContrastContextType | undefined>(undefined);

export const useHighContrast = () => {
  const context = useContext(HighContrastContext);
  if (!context) {
    throw new Error('useHighContrast must be used within a HighContrastProvider');
  }
  return context;
};

// High Contrast CSS Variables and Classes
const getContrastStyles = (settings: HighContrastSettings) => {
  const styles: Record<string, string> = {};

  if (settings.enabled) {
    // Base high contrast colors
    if (settings.level === 'high') {
      styles['--hc-bg-primary'] = settings.colorScheme === 'dark' ? '#000000' : '#ffffff';
      styles['--hc-bg-secondary'] = settings.colorScheme === 'dark' ? '#1a1a1a' : '#f5f5f5';
      styles['--hc-text-primary'] = settings.colorScheme === 'dark' ? '#ffffff' : '#000000';
      styles['--hc-text-secondary'] = settings.colorScheme === 'dark' ? '#e0e0e0' : '#333333';
      styles['--hc-border'] = settings.colorScheme === 'dark' ? '#ffffff' : '#000000';
      styles['--hc-accent'] = settings.colorScheme === 'dark' ? '#00ffff' : '#0000ff';
      styles['--hc-success'] = settings.colorScheme === 'dark' ? '#00ff00' : '#008000';
      styles['--hc-warning'] = settings.colorScheme === 'dark' ? '#ffff00' : '#ff8000';
      styles['--hc-error'] = settings.colorScheme === 'dark' ? '#ff0000' : '#cc0000';
    } else if (settings.level === 'maximum') {
      styles['--hc-bg-primary'] = settings.colorScheme === 'dark' ? '#000000' : '#ffffff';
      styles['--hc-bg-secondary'] = settings.colorScheme === 'dark' ? '#000000' : '#ffffff';
      styles['--hc-text-primary'] = settings.colorScheme === 'dark' ? '#ffffff' : '#000000';
      styles['--hc-text-secondary'] = settings.colorScheme === 'dark' ? '#ffffff' : '#000000';
      styles['--hc-border'] = settings.colorScheme === 'dark' ? '#ffffff' : '#000000';
      styles['--hc-accent'] = settings.colorScheme === 'dark' ? '#ffffff' : '#000000';
      styles['--hc-success'] = settings.colorScheme === 'dark' ? '#ffffff' : '#000000';
      styles['--hc-warning'] = settings.colorScheme === 'dark' ? '#ffffff' : '#000000';
      styles['--hc-error'] = settings.colorScheme === 'dark' ? '#ffffff' : '#000000';
    }

    // Typography adjustments
    if (settings.largeText) {
      styles['--hc-font-size-base'] = '1.125rem';
      styles['--hc-font-size-sm'] = '1rem';
      styles['--hc-font-size-lg'] = '1.25rem';
    }

    if (settings.boldText) {
      styles['--hc-font-weight'] = '600';
    }

    // Focus indicators
    if (settings.focusIndicators) {
      styles['--hc-focus-ring'] = '3px solid ' + (settings.colorScheme === 'dark' ? '#00ffff' : '#0000ff');
      styles['--hc-focus-offset'] = '2px';
    }
  }

  return styles;
};

const getContrastClasses = (settings: HighContrastSettings) => {
  const classes: string[] = [];

  if (settings.enabled) {
    classes.push('high-contrast-mode');
    classes.push(`contrast-${settings.level}`);
    classes.push(`scheme-${settings.colorScheme}`);

    if (settings.reducedMotion) classes.push('reduced-motion');
    if (settings.largeText) classes.push('large-text');
    if (settings.boldText) classes.push('bold-text');
    if (settings.underlineLinks) classes.push('underline-links');
    if (settings.focusIndicators) classes.push('enhanced-focus');
  }

  return classes;
};

// High Contrast Provider Component
interface HighContrastProviderProps {
  children: React.ReactNode;
}

export const HighContrastProvider: React.FC<HighContrastProviderProps> = ({ children }) => {
  const [settings, setSettings] = useState<HighContrastSettings>(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('high-contrast-settings');
      if (saved) {
        try {
          return { ...defaultSettings, ...JSON.parse(saved) };
        } catch {
          return defaultSettings;
        }
      }
    }
    return defaultSettings;
  });

  const updateSettings = (updates: Partial<HighContrastSettings>) => {
    setSettings(prev => ({ ...prev, ...updates }));
  };

  const toggleHighContrast = () => {
    updateSettings({ enabled: !settings.enabled });
  };

  const applySettings = () => {
    if (typeof window === 'undefined') return;

    // Save to localStorage
    localStorage.setItem('high-contrast-settings', JSON.stringify(settings));

    // Apply CSS custom properties
    const styles = getContrastStyles(settings);
    const root = document.documentElement;
    
    Object.entries(styles).forEach(([property, value]) => {
      root.style.setProperty(property, value);
    });

    // Apply CSS classes
    const classes = getContrastClasses(settings);
    document.body.className = document.body.className
      .split(' ')
      .filter(cls => !cls.startsWith('high-contrast') && !cls.startsWith('contrast-') && 
                     !cls.startsWith('scheme-') && !cls.includes('reduced-motion') &&
                     !cls.includes('large-text') && !cls.includes('bold-text') &&
                     !cls.includes('underline-links') && !cls.includes('enhanced-focus'))
      .concat(classes)
      .join(' ');

    // Handle color scheme preference
    if (settings.colorScheme === 'auto') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      document.documentElement.setAttribute('data-theme', mediaQuery.matches ? 'dark' : 'light');
    } else {
      document.documentElement.setAttribute('data-theme', settings.colorScheme);
    }

    // Handle reduced motion
    if (settings.reducedMotion) {
      document.documentElement.style.setProperty('--animation-duration', '0s');
      document.documentElement.style.setProperty('--transition-duration', '0s');
    } else {
      document.documentElement.style.removeProperty('--animation-duration');
      document.documentElement.style.removeProperty('--transition-duration');
    }
  };

  useEffect(() => {
    applySettings();
  }, [settings]);

  useEffect(() => {
    // Listen for system color scheme changes
    if (settings.colorScheme === 'auto') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      const handleChange = () => applySettings();
      
      mediaQuery.addEventListener('change', handleChange);
      return () => mediaQuery.removeEventListener('change', handleChange);
    }
  }, [settings.colorScheme]);

  return (
    <HighContrastContext.Provider value={{
      settings,
      updateSettings,
      toggleHighContrast,
      applySettings
    }}>
      {children}
    </HighContrastContext.Provider>
  );
};

// High Contrast Toggle Button
interface HighContrastToggleProps {
  className?: string;
  showLabel?: boolean;
}

export const HighContrastToggle: React.FC<HighContrastToggleProps> = ({
  className,
  showLabel = true
}) => {
  const { settings, toggleHighContrast } = useHighContrast();

  return (
    <Button
      variant={settings.enabled ? "default" : "outline"}
      size="sm"
      onClick={toggleHighContrast}
      className={cn(
        "flex items-center gap-2",
        settings.enabled && "bg-yellow-500 hover:bg-yellow-600 text-black",
        className
      )}
      aria-label={`${settings.enabled ? 'Disable' : 'Enable'} high contrast mode`}
    >
      <Contrast className="h-4 w-4" />
      {showLabel && (
        <span>{settings.enabled ? 'High Contrast On' : 'High Contrast Off'}</span>
      )}
    </Button>
  );
};

// High Contrast Settings Panel
interface HighContrastSettingsProps {
  className?: string;
}

export const HighContrastSettings: React.FC<HighContrastSettingsProps> = ({ className }) => {
  const { settings, updateSettings } = useHighContrast();

  const contrastLevels: { value: ContrastLevel; label: string; description: string }[] = [
    {
      value: 'normal',
      label: 'Normal',
      description: 'Standard contrast levels'
    },
    {
      value: 'high',
      label: 'High',
      description: 'Increased contrast for better visibility'
    },
    {
      value: 'maximum',
      label: 'Maximum',
      description: 'Maximum contrast with black and white only'
    }
  ];

  const colorSchemes: { value: ColorScheme; label: string; icon: React.ReactNode }[] = [
    { value: 'light', label: 'Light', icon: <Sun className="h-4 w-4" /> },
    { value: 'dark', label: 'Dark', icon: <Moon className="h-4 w-4" /> },
    { value: 'auto', label: 'Auto', icon: <Monitor className="h-4 w-4" /> }
  ];

  return (
    <Card className={cn("w-full max-w-2xl", className)}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Eye className="h-5 w-5" />
          High Contrast & Accessibility Settings
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Enable High Contrast */}
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <label className="text-sm font-medium">Enable High Contrast Mode</label>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              Improves visibility for users with visual impairments
            </p>
          </div>
          <Switch
            checked={settings.enabled}
            onCheckedChange={(enabled) => updateSettings({ enabled })}
            aria-label="Toggle high contrast mode"
          />
        </div>

        <Separator />

        {/* Contrast Level */}
        <div className="space-y-3">
          <label className="text-sm font-medium">Contrast Level</label>
          <div className="grid grid-cols-1 gap-2">
            {contrastLevels.map((level) => (
              <button
                key={level.value}
                onClick={() => updateSettings({ level: level.value })}
                className={cn(
                  "flex items-center justify-between p-3 rounded-lg border text-left transition-colors",
                  "hover:bg-gray-50 dark:hover:bg-gray-800",
                  settings.level === level.value
                    ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
                    : "border-gray-200 dark:border-gray-700"
                )}
                disabled={!settings.enabled}
              >
                <div>
                  <div className="font-medium text-sm">{level.label}</div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {level.description}
                  </div>
                </div>
                {settings.level === level.value && (
                  <Check className="h-4 w-4 text-blue-500" />
                )}
              </button>
            ))}
          </div>
        </div>

        <Separator />

        {/* Color Scheme */}
        <div className="space-y-3">
          <label className="text-sm font-medium">Color Scheme</label>
          <div className="flex gap-2">
            {colorSchemes.map((scheme) => (
              <button
                key={scheme.value}
                onClick={() => updateSettings({ colorScheme: scheme.value })}
                className={cn(
                  "flex items-center gap-2 px-3 py-2 rounded-lg border text-sm transition-colors",
                  "hover:bg-gray-50 dark:hover:bg-gray-800",
                  settings.colorScheme === scheme.value
                    ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
                    : "border-gray-200 dark:border-gray-700"
                )}
                disabled={!settings.enabled}
              >
                {scheme.icon}
                {scheme.label}
              </button>
            ))}
          </div>
        </div>

        <Separator />

        {/* Additional Options */}
        <div className="space-y-4">
          <label className="text-sm font-medium">Additional Options</label>
          
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-sm">Reduce Motion</div>
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  Minimize animations and transitions
                </div>
              </div>
              <Switch
                checked={settings.reducedMotion}
                onCheckedChange={(reducedMotion) => updateSettings({ reducedMotion })}
                disabled={!settings.enabled}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <div className="text-sm">Large Text</div>
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  Increase base font size
                </div>
              </div>
              <Switch
                checked={settings.largeText}
                onCheckedChange={(largeText) => updateSettings({ largeText })}
                disabled={!settings.enabled}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <div className="text-sm">Bold Text</div>
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  Make text bolder for better readability
                </div>
              </div>
              <Switch
                checked={settings.boldText}
                onCheckedChange={(boldText) => updateSettings({ boldText })}
                disabled={!settings.enabled}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <div className="text-sm">Underline Links</div>
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  Always underline clickable links
                </div>
              </div>
              <Switch
                checked={settings.underlineLinks}
                onCheckedChange={(underlineLinks) => updateSettings({ underlineLinks })}
                disabled={!settings.enabled}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <div className="text-sm">Enhanced Focus Indicators</div>
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  Stronger focus outlines for keyboard navigation
                </div>
              </div>
              <Switch
                checked={settings.focusIndicators}
                onCheckedChange={(focusIndicators) => updateSettings({ focusIndicators })}
                disabled={!settings.enabled}
              />
            </div>
          </div>
        </div>

        {/* Status Badge */}
        {settings.enabled && (
          <div className="flex items-center gap-2 pt-4">
            <Badge variant="outline" className="bg-yellow-50 text-yellow-800 border-yellow-200">
              <Contrast className="h-3 w-3 mr-1" />
              High Contrast Active
            </Badge>
            <Badge variant="outline">
              {settings.level.charAt(0).toUpperCase() + settings.level.slice(1)} Level
            </Badge>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default HighContrastProvider;