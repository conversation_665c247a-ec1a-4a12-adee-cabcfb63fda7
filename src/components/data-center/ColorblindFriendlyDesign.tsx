import React, { createContext, useContext, useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Palette,
  Eye,
  TestTube,
  Info,
  CheckCircle,
  AlertTriangle,
  XCircle,
  Circle,
  Square,
  Triangle,
  Diamond,
  Star,
  Heart
} from 'lucide-react';
import { cn } from '@/lib/utils';

type ColorblindType = 'none' | 'protanopia' | 'deuteranopia' | 'tritanopia' | 'achromatopsia';

interface ColorblindSettings {
  enabled: boolean;
  type: ColorblindType;
  usePatterns: boolean;
  useShapes: boolean;
  useTextures: boolean;
  highContrast: boolean;
  alternativeColors: boolean;
}

interface ColorblindContextType {
  settings: ColorblindSettings;
  updateSettings: (updates: Partial<ColorblindSettings>) => void;
  getAccessibleColor: (color: string, context?: string) => string;
  getPatternForColor: (color: string) => React.ReactNode;
  getShapeForColor: (color: string) => React.ReactNode;
}

const defaultSettings: ColorblindSettings = {
  enabled: false,
  type: 'none',
  usePatterns: true,
  useShapes: true,
  useTextures: false,
  highContrast: false,
  alternativeColors: true
};

const ColorblindContext = createContext<ColorblindContextType | undefined>(undefined);

export const useColorblindFriendly = () => {
  const context = useContext(ColorblindContext);
  if (!context) {
    throw new Error('useColorblindFriendly must be used within a ColorblindFriendlyProvider');
  }
  return context;
};

// Colorblind-friendly color palettes
const colorPalettes = {
  // Safe colors that work for all types of colorblindness
  safe: {
    primary: '#0066CC',    // Blue
    secondary: '#FF6600',  // Orange
    success: '#228B22',    // Forest Green
    warning: '#FFD700',    // Gold
    error: '#DC143C',      // Crimson
    info: '#4169E1',       // Royal Blue
    neutral: '#708090'     // Slate Gray
  },
  
  // High contrast alternatives
  highContrast: {
    primary: '#000080',    // Navy
    secondary: '#FF4500',  // Orange Red
    success: '#006400',    // Dark Green
    warning: '#B8860B',    // Dark Goldenrod
    error: '#8B0000',      // Dark Red
    info: '#191970',       // Midnight Blue
    neutral: '#2F4F4F'     // Dark Slate Gray
  },
  
  // Alternative color scheme
  alternative: {
    primary: '#4B0082',    // Indigo
    secondary: '#FF8C00',  // Dark Orange
    success: '#32CD32',    // Lime Green
    warning: '#DAA520',    // Goldenrod
    error: '#B22222',      // Fire Brick
    info: '#6495ED',       // Cornflower Blue
    neutral: '#696969'     // Dim Gray
  }
};

// Pattern definitions for different colors
const colorPatterns = {
  primary: 'diagonal-lines',
  secondary: 'dots',
  success: 'horizontal-lines',
  warning: 'vertical-lines',
  error: 'cross-hatch',
  info: 'waves',
  neutral: 'solid'
};

// Shape definitions for different colors
const colorShapes = {
  primary: Circle,
  secondary: Square,
  success: CheckCircle,
  warning: Triangle,
  error: XCircle,
  info: Info,
  neutral: Diamond
};

// Colorblind simulation filters (simplified)
const getSimulatedColor = (color: string, type: ColorblindType): string => {
  if (type === 'none') return color;
  
  // This is a simplified simulation - in a real app, you'd use more sophisticated color conversion
  const colorMap: Record<string, Record<ColorblindType, string>> = {
    '#FF0000': { // Red
      protanopia: '#7F7F00',
      deuteranopia: '#7F7F00', 
      tritanopia: '#FF0040',
      achromatopsia: '#808080',
      none: '#FF0000'
    },
    '#00FF00': { // Green
      protanopia: '#FFFF00',
      deuteranopia: '#FFFF00',
      tritanopia: '#00FF40',
      achromatopsia: '#808080',
      none: '#00FF00'
    },
    '#0000FF': { // Blue
      protanopia: '#0000FF',
      deuteranopia: '#0000FF',
      tritanopia: '#004080',
      achromatopsia: '#808080',
      none: '#0000FF'
    }
  };
  
  return colorMap[color]?.[type] || color;
};

// Pattern components
const PatternSVG: React.FC<{ pattern: string; className?: string }> = ({ pattern, className }) => {
  const patterns = {
    'diagonal-lines': (
      <svg className={cn("w-4 h-4", className)} viewBox="0 0 16 16">
        <defs>
          <pattern id="diagonal" patternUnits="userSpaceOnUse" width="4" height="4">
            <path d="M 0,4 l 4,-4 M -1,1 l 2,-2 M 3,5 l 2,-2" stroke="currentColor" strokeWidth="1"/>
          </pattern>
        </defs>
        <rect width="16" height="16" fill="url(#diagonal)" />
      </svg>
    ),
    'dots': (
      <svg className={cn("w-4 h-4", className)} viewBox="0 0 16 16">
        <defs>
          <pattern id="dots" patternUnits="userSpaceOnUse" width="4" height="4">
            <circle cx="2" cy="2" r="1" fill="currentColor"/>
          </pattern>
        </defs>
        <rect width="16" height="16" fill="url(#dots)" />
      </svg>
    ),
    'horizontal-lines': (
      <svg className={cn("w-4 h-4", className)} viewBox="0 0 16 16">
        <defs>
          <pattern id="horizontal" patternUnits="userSpaceOnUse" width="4" height="4">
            <path d="M 0,2 l 4,0" stroke="currentColor" strokeWidth="1"/>
          </pattern>
        </defs>
        <rect width="16" height="16" fill="url(#horizontal)" />
      </svg>
    ),
    'vertical-lines': (
      <svg className={cn("w-4 h-4", className)} viewBox="0 0 16 16">
        <defs>
          <pattern id="vertical" patternUnits="userSpaceOnUse" width="4" height="4">
            <path d="M 2,0 l 0,4" stroke="currentColor" strokeWidth="1"/>
          </pattern>
        </defs>
        <rect width="16" height="16" fill="url(#vertical)" />
      </svg>
    ),
    'cross-hatch': (
      <svg className={cn("w-4 h-4", className)} viewBox="0 0 16 16">
        <defs>
          <pattern id="crosshatch" patternUnits="userSpaceOnUse" width="4" height="4">
            <path d="M 0,2 l 4,0 M 2,0 l 0,4" stroke="currentColor" strokeWidth="1"/>
          </pattern>
        </defs>
        <rect width="16" height="16" fill="url(#crosshatch)" />
      </svg>
    ),
    'waves': (
      <svg className={cn("w-4 h-4", className)} viewBox="0 0 16 16">
        <defs>
          <pattern id="waves" patternUnits="userSpaceOnUse" width="8" height="4">
            <path d="M 0,2 Q 2,0 4,2 T 8,2" stroke="currentColor" strokeWidth="1" fill="none"/>
          </pattern>
        </defs>
        <rect width="16" height="16" fill="url(#waves)" />
      </svg>
    ),
    'solid': (
      <svg className={cn("w-4 h-4", className)} viewBox="0 0 16 16">
        <rect width="16" height="16" fill="currentColor" />
      </svg>
    )
  };
  
  return patterns[pattern as keyof typeof patterns] || patterns.solid;
};

// Colorblind-friendly component variants
interface AccessibleBadgeProps {
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info' | 'neutral';
  children: React.ReactNode;
  showPattern?: boolean;
  showShape?: boolean;
  className?: string;
}

export const AccessibleBadge: React.FC<AccessibleBadgeProps> = ({
  variant = 'primary',
  children,
  showPattern = false,
  showShape = false,
  className
}) => {
  const { settings, getAccessibleColor, getPatternForColor, getShapeForColor } = useColorblindFriendly();
  
  const color = getAccessibleColor(variant, 'badge');
  const pattern = settings.usePatterns && showPattern ? getPatternForColor(variant) : null;
  const ShapeIcon = settings.useShapes && showShape ? colorShapes[variant] : null;
  
  return (
    <Badge
      className={cn(
        "flex items-center gap-1",
        className
      )}
      style={{
        backgroundColor: color,
        color: settings.highContrast ? '#ffffff' : undefined
      }}
    >
      {ShapeIcon && <ShapeIcon className="w-3 h-3" />}
      {children}
      {pattern && <span className="ml-1">{pattern}</span>}
    </Badge>
  );
};

// Accessible Status Indicator
interface AccessibleStatusProps {
  status: 'success' | 'warning' | 'error' | 'info' | 'neutral';
  label: string;
  showIcon?: boolean;
  showPattern?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const AccessibleStatus: React.FC<AccessibleStatusProps> = ({
  status,
  label,
  showIcon = true,
  showPattern = false,
  size = 'md',
  className
}) => {
  const { settings, getAccessibleColor, getPatternForColor } = useColorblindFriendly();
  
  const color = getAccessibleColor(status, 'status');
  const pattern = settings.usePatterns && showPattern ? getPatternForColor(status) : null;
  const IconComponent = colorShapes[status];
  
  const sizeClasses = {
    sm: 'text-xs px-2 py-1',
    md: 'text-sm px-3 py-1.5',
    lg: 'text-base px-4 py-2'
  };
  
  return (
    <div
      className={cn(
        "inline-flex items-center gap-2 rounded-full font-medium",
        sizeClasses[size],
        className
      )}
      style={{
        backgroundColor: `${color}20`,
        borderColor: color,
        borderWidth: '1px',
        color: settings.highContrast ? color : undefined
      }}
    >
      {showIcon && IconComponent && (
        <IconComponent 
          className={cn(
            size === 'sm' ? 'w-3 h-3' : size === 'md' ? 'w-4 h-4' : 'w-5 h-5'
          )}
          style={{ color }}
        />
      )}
      <span>{label}</span>
      {pattern && <span className="ml-1">{pattern}</span>}
    </div>
  );
};

// Color Palette Preview
interface ColorPalettePreviewProps {
  palette: Record<string, string>;
  title: string;
  className?: string;
}

const ColorPalettePreview: React.FC<ColorPalettePreviewProps> = ({ palette, title, className }) => {
  const { settings } = useColorblindFriendly();
  
  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="pb-3">
        <CardTitle className="text-sm">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-4 gap-2">
          {Object.entries(palette).map(([name, color]) => {
            const simulatedColor = getSimulatedColor(color, settings.type);
            return (
              <div key={name} className="text-center">
                <div
                  className="w-12 h-12 rounded-lg border mx-auto mb-1"
                  style={{ backgroundColor: simulatedColor }}
                  title={`${name}: ${color}`}
                />
                <div className="text-xs text-gray-600 dark:text-gray-400 capitalize">
                  {name}
                </div>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
};

// Colorblind-friendly Provider
interface ColorblindFriendlyProviderProps {
  children: React.ReactNode;
}

export const ColorblindFriendlyProvider: React.FC<ColorblindFriendlyProviderProps> = ({ children }) => {
  const [settings, setSettings] = useState<ColorblindSettings>(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('colorblind-settings');
      if (saved) {
        try {
          return { ...defaultSettings, ...JSON.parse(saved) };
        } catch {
          return defaultSettings;
        }
      }
    }
    return defaultSettings;
  });

  const updateSettings = (updates: Partial<ColorblindSettings>) => {
    const newSettings = { ...settings, ...updates };
    setSettings(newSettings);
    
    if (typeof window !== 'undefined') {
      localStorage.setItem('colorblind-settings', JSON.stringify(newSettings));
    }
  };

  const getAccessibleColor = (color: string, context?: string): string => {
    if (!settings.enabled) return color;
    
    let palette = colorPalettes.safe;
    if (settings.highContrast) {
      palette = colorPalettes.highContrast;
    } else if (settings.alternativeColors) {
      palette = colorPalettes.alternative;
    }
    
    return palette[color as keyof typeof palette] || color;
  };

  const getPatternForColor = (color: string): React.ReactNode => {
    if (!settings.usePatterns) return null;
    
    const pattern = colorPatterns[color as keyof typeof colorPatterns] || 'solid';
    return <PatternSVG pattern={pattern} />;
  };

  const getShapeForColor = (color: string): React.ReactNode => {
    if (!settings.useShapes) return null;
    
    const ShapeComponent = colorShapes[color as keyof typeof colorShapes] || Circle;
    return <ShapeComponent className="w-4 h-4" />;
  };

  useEffect(() => {
    if (typeof window === 'undefined') return;
    
    // Apply CSS custom properties for colorblind-friendly design
    const root = document.documentElement;
    
    if (settings.enabled) {
      const palette = settings.highContrast 
        ? colorPalettes.highContrast 
        : settings.alternativeColors 
        ? colorPalettes.alternative 
        : colorPalettes.safe;
      
      Object.entries(palette).forEach(([name, color]) => {
        root.style.setProperty(`--color-${name}`, color);
      });
      
      // Add colorblind-friendly class
      document.body.classList.add('colorblind-friendly');
      if (settings.usePatterns) document.body.classList.add('use-patterns');
      if (settings.useShapes) document.body.classList.add('use-shapes');
      if (settings.highContrast) document.body.classList.add('colorblind-high-contrast');
    } else {
      // Remove custom properties and classes
      document.body.classList.remove(
        'colorblind-friendly',
        'use-patterns', 
        'use-shapes',
        'colorblind-high-contrast'
      );
    }
  }, [settings]);

  return (
    <ColorblindContext.Provider value={{
      settings,
      updateSettings,
      getAccessibleColor,
      getPatternForColor,
      getShapeForColor
    }}>
      {children}
    </ColorblindContext.Provider>
  );
};

// Colorblind Settings Panel
interface ColorblindSettingsProps {
  className?: string;
}

export const ColorblindSettings: React.FC<ColorblindSettingsProps> = ({ className }) => {
  const { settings, updateSettings } = useColorblindFriendly();

  const colorblindTypes: { value: ColorblindType; label: string; description: string }[] = [
    {
      value: 'none',
      label: 'None',
      description: 'No color vision deficiency'
    },
    {
      value: 'protanopia',
      label: 'Protanopia',
      description: 'Red-blind (missing L-cones)'
    },
    {
      value: 'deuteranopia',
      label: 'Deuteranopia', 
      description: 'Green-blind (missing M-cones)'
    },
    {
      value: 'tritanopia',
      label: 'Tritanopia',
      description: 'Blue-blind (missing S-cones)'
    },
    {
      value: 'achromatopsia',
      label: 'Achromatopsia',
      description: 'Complete color blindness'
    }
  ];

  return (
    <div className={cn("space-y-6", className)}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="h-5 w-5" />
            Colorblind-Friendly Design
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Enable Colorblind-Friendly Mode */}
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <label className="text-sm font-medium">Enable Colorblind-Friendly Mode</label>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Use accessible colors, patterns, and shapes for better visibility
              </p>
            </div>
            <Switch
              checked={settings.enabled}
              onCheckedChange={(enabled) => updateSettings({ enabled })}
            />
          </div>

          <Separator />

          {/* Colorblind Type Simulation */}
          <div className="space-y-3">
            <label className="text-sm font-medium">Simulate Color Vision Type</label>
            <div className="grid grid-cols-1 gap-2">
              {colorblindTypes.map((type) => (
                <button
                  key={type.value}
                  onClick={() => updateSettings({ type: type.value })}
                  className={cn(
                    "flex items-center justify-between p-3 rounded-lg border text-left transition-colors",
                    "hover:bg-gray-50 dark:hover:bg-gray-800",
                    settings.type === type.value
                      ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
                      : "border-gray-200 dark:border-gray-700"
                  )}
                  disabled={!settings.enabled}
                >
                  <div>
                    <div className="font-medium text-sm">{type.label}</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {type.description}
                    </div>
                  </div>
                  {settings.type === type.value && (
                    <CheckCircle className="h-4 w-4 text-blue-500" />
                  )}
                </button>
              ))}
            </div>
          </div>

          <Separator />

          {/* Additional Options */}
          <div className="space-y-4">
            <label className="text-sm font-medium">Accessibility Enhancements</label>
            
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-sm">Use Patterns</div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    Add visual patterns to distinguish colors
                  </div>
                </div>
                <Switch
                  checked={settings.usePatterns}
                  onCheckedChange={(usePatterns) => updateSettings({ usePatterns })}
                  disabled={!settings.enabled}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <div className="text-sm">Use Shapes</div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    Add distinctive shapes to identify different states
                  </div>
                </div>
                <Switch
                  checked={settings.useShapes}
                  onCheckedChange={(useShapes) => updateSettings({ useShapes })}
                  disabled={!settings.enabled}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <div className="text-sm">High Contrast Colors</div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    Use higher contrast color combinations
                  </div>
                </div>
                <Switch
                  checked={settings.highContrast}
                  onCheckedChange={(highContrast) => updateSettings({ highContrast })}
                  disabled={!settings.enabled}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <div className="text-sm">Alternative Color Palette</div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    Use alternative colors that are more distinguishable
                  </div>
                </div>
                <Switch
                  checked={settings.alternativeColors}
                  onCheckedChange={(alternativeColors) => updateSettings({ alternativeColors })}
                  disabled={!settings.enabled}
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Color Palette Previews */}
      {settings.enabled && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <ColorPalettePreview
            palette={colorPalettes.safe}
            title="Safe Colors"
          />
          <ColorPalettePreview
            palette={colorPalettes.highContrast}
            title="High Contrast"
          />
          <ColorPalettePreview
            palette={colorPalettes.alternative}
            title="Alternative Palette"
          />
        </div>
      )}

      {/* Status Examples */}
      {settings.enabled && (
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Accessibility Examples</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium mb-2 block">Status Indicators</label>
                <div className="flex flex-wrap gap-2">
                  <AccessibleStatus status="success" label="Success" showIcon showPattern />
                  <AccessibleStatus status="warning" label="Warning" showIcon showPattern />
                  <AccessibleStatus status="error" label="Error" showIcon showPattern />
                  <AccessibleStatus status="info" label="Info" showIcon showPattern />
                </div>
              </div>
              
              <div>
                <label className="text-sm font-medium mb-2 block">Badges</label>
                <div className="flex flex-wrap gap-2">
                  <AccessibleBadge variant="primary" showPattern showShape>Primary</AccessibleBadge>
                  <AccessibleBadge variant="secondary" showPattern showShape>Secondary</AccessibleBadge>
                  <AccessibleBadge variant="success" showPattern showShape>Success</AccessibleBadge>
                  <AccessibleBadge variant="warning" showPattern showShape>Warning</AccessibleBadge>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default ColorblindFriendlyProvider;