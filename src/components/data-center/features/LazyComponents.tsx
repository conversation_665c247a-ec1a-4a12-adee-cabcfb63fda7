/**
 * Lazy Loading Components
 * Implements code splitting for feature components to improve performance
 */

import React, { Suspense, lazy, Component, ErrorInfo, ReactNode } from 'react';

// Lazy load feature components
const LazyFileUploadZone = lazy(() => import('./upload/FileUploadZone').then(module => ({ default: module.FileUploadZone })));
const LazyDataPreviewPanel = lazy(() => import('./preview/DataPreviewPanel').then(module => ({ default: module.DataPreviewPanel })));
const LazyProcessingPanel = lazy(() => import('./processing/ProcessingPanel').then(module => ({ default: module.ProcessingPanel })));
const LazySQLQueryPanel = lazy(() => import('./sql/SQLQueryPanel').then(module => ({ default: module.SQLQueryPanel })));
const LazyDashboardView = lazy(() => import('./dashboard/DashboardView').then(module => ({ default: module.DashboardView })));

// Loading fallback component
const LoadingFallback: React.FC<{ componentName?: string }> = ({ componentName }) => (
  <div className="flex items-center justify-center p-8 bg-gray-50 rounded-lg border-2 border-dashed border-gray-200">
    <div className="text-center">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
      <p className="text-sm text-gray-600">
        Loading {componentName || 'component'}...
      </p>
    </div>
  </div>
);

// Error boundary component
interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback: (error: Error, resetError: () => void) => ReactNode;
  onReset?: () => void;
}

class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Lazy component error:', error, errorInfo);
  }

  resetError = () => {
    this.setState({ hasError: false, error: undefined });
    this.props.onReset?.();
  };

  render() {
    if (this.state.hasError && this.state.error) {
      return this.props.fallback(this.state.error, this.resetError);
    }

    return this.props.children;
  }
}

// Error fallback component
const ErrorFallback: React.FC<{ error: Error; resetErrorBoundary: () => void; componentName?: string }> = ({ 
  error, 
  resetErrorBoundary, 
  componentName 
}) => (
  <div className="flex items-center justify-center p-8 bg-red-50 rounded-lg border border-red-200">
    <div className="text-center">
      <div className="text-red-600 mb-2">
        <svg className="w-8 h-8 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
      </div>
      <h3 className="text-sm font-medium text-red-800 mb-1">
        Failed to load {componentName || 'component'}
      </h3>
      <p className="text-xs text-red-600 mb-3">
        {error.message}
      </p>
      <button
        onClick={resetErrorBoundary}
        className="px-3 py-1 text-xs bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
      >
        Try Again
      </button>
    </div>
  </div>
);

// Higher-order component for lazy loading with error boundary
const withLazyLoading = <P extends object>(
  LazyComponent: React.LazyExoticComponent<React.ComponentType<P>>,
  componentName: string
) => {
  return React.forwardRef<any, P>((props, ref) => (
    <ErrorBoundary
      fallback={(error: Error, resetError: () => void) => (
        <ErrorFallback error={error} resetErrorBoundary={resetError} componentName={componentName} />
      )}
      onReset={() => {
        // Clear any cached modules on reset
        if ('webpackChunkName' in LazyComponent) {
          // Force re-import on next render
          window.location.reload();
        }
      }}
    >
      <Suspense fallback={<LoadingFallback componentName={componentName} />}>
        <LazyComponent {...props} ref={ref} />
      </Suspense>
    </ErrorBoundary>
  ));
};

// Export lazy-loaded components with error boundaries
export const FileUploadZone = withLazyLoading(LazyFileUploadZone, 'File Upload Zone');
export const DataPreviewPanel = withLazyLoading(LazyDataPreviewPanel, 'Data Preview Panel');
export const ProcessingPanel = withLazyLoading(LazyProcessingPanel, 'Processing Panel');
export const SQLQueryPanel = withLazyLoading(LazySQLQueryPanel, 'SQL Query Panel');
export const DashboardView = withLazyLoading(LazyDashboardView, 'Dashboard View');

// Preload functions for better UX
export const preloadComponents = {
  fileUpload: () => import('./upload/FileUploadZone'),
  dataPreview: () => import('./preview/DataPreviewPanel'),
  processing: () => import('./processing/ProcessingPanel'),
  sqlQuery: () => import('./sql/SQLQueryPanel'),
  dashboard: () => import('./dashboard/DashboardView'),
};

// Preload all components
export const preloadAllComponents = () => {
  Object.values(preloadComponents).forEach(preload => {
    preload().catch(error => {
      console.warn('Failed to preload component:', error);
    });
  });
};

// Preload components based on user interaction patterns
export const preloadByStage = (currentStage: string) => {
  const preloadMap: Record<string, (() => Promise<any>)[]> = {
    upload: [preloadComponents.dataPreview],
    preview: [preloadComponents.processing, preloadComponents.dashboard],
    processing: [preloadComponents.sqlQuery, preloadComponents.dashboard],
    sql: [preloadComponents.dashboard],
    dashboard: [preloadComponents.processing, preloadComponents.sqlQuery],
  };

  const componentsToPreload = preloadMap[currentStage] || [];
  componentsToPreload.forEach(preload => {
    preload().catch(error => {
      console.warn('Failed to preload component for stage:', currentStage, error);
    });
  });
};