import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>, Settings, Eye, EyeOff, BarChart3, <PERSON><PERSON><PERSON>, TrendingUp, Database } from 'lucide-react';
import { DashboardViewProps } from '../../types';
import { <PERSON><PERSON>, Card, Badge } from '../../ui/StyledComponents';

/**
 * Dashboard view component for displaying data widgets and visualizations
 */
export const DashboardView: React.FC<DashboardViewProps> = ({
  widgets,
  data,
  onWidgetToggle,
  customWidgets
}) => {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showSettings, setShowSettings] = useState(false);

  const availableWidgets = [
    {
      id: 'data-summary',
      name: 'Data Summary',
      icon: Database,
      description: 'Overview of data structure and statistics',
      category: 'analysis'
    },
    {
      id: 'column-stats',
      name: 'Column Statistics',
      icon: BarChart3,
      description: 'Statistical analysis of each column',
      category: 'analysis'
    },
    {
      id: 'data-types',
      name: 'Data Types',
      icon: <PERSON><PERSON><PERSON>,
      description: 'Distribution of data types across columns',
      category: 'visualization'
    },
    {
      id: 'trends',
      name: 'Data Trends',
      icon: TrendingUp,
      description: 'Identify patterns and trends in the data',
      category: 'visualization'
    }
  ];

  const isWidgetEnabled = (widgetId: string) => widgets.includes(widgetId);

  const renderDataSummaryWidget = () => {
    if (!data) return <div style={{ padding: '16px', textAlign: 'center', color: '#666' }}>No data available</div>;

    return (
      <div style={{ padding: '16px' }}>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))', gap: '16px' }}>
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: '24px', fontWeight: '600', color: '#0ea5e9' }}>
              {data.totalRows || data.rows?.length || 0}
            </div>
            <div style={{ fontSize: '14px', color: '#666' }}>Total Rows</div>
          </div>
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: '24px', fontWeight: '600', color: '#10b981' }}>
              {data.headers?.length || 0}
            </div>
            <div style={{ fontSize: '14px', color: '#666' }}>Columns</div>
          </div>
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: '24px', fontWeight: '600', color: '#f59e0b' }}>
              {data.dataTypes ? Object.keys(data.dataTypes).length : 0}
            </div>
            <div style={{ fontSize: '14px', color: '#666' }}>Data Types</div>
          </div>
        </div>
      </div>
    );
  };

  const renderColumnStatsWidget = () => {
    if (!data || !data.headers) return <div style={{ padding: '16px', textAlign: 'center', color: '#666' }}>No data available</div>;

    return (
      <div style={{ padding: '16px' }}>
        <div style={{ maxHeight: '300px', overflow: 'auto' }}>
          {data.headers.map((header, index) => (
            <div key={header} style={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'center',
              padding: '8px 0',
              borderBottom: index < data.headers.length - 1 ? '1px solid #f0f0f0' : 'none'
            }}>
              <span style={{ fontWeight: '500' }}>{header}</span>
              <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
                <Badge variant="secondary">
                  {data.dataTypes?.[header] || 'unknown'}
                </Badge>
                <span style={{ fontSize: '14px', color: '#666' }}>
                  {data.rows ? `${data.rows.length} values` : 'N/A'}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  const renderDataTypesWidget = () => {
    if (!data || !data.dataTypes) return <div style={{ padding: '16px', textAlign: 'center', color: '#666' }}>No data types available</div>;

    const typeCount = Object.values(data.dataTypes).reduce((acc, type) => {
      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const colors = ['#0ea5e9', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'];

    return (
      <div style={{ padding: '16px' }}>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
          {Object.entries(typeCount).map(([type, count], index) => (
            <div key={type} style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <div 
                style={{ 
                  width: '12px', 
                  height: '12px', 
                  borderRadius: '50%', 
                  backgroundColor: colors[index % colors.length] 
                }}
              />
              <span style={{ flex: 1, fontSize: '14px' }}>{type}</span>
              <span style={{ fontSize: '14px', fontWeight: '600' }}>{count}</span>
            </div>
          ))}
        </div>
      </div>
    );
  };

  const renderTrendsWidget = () => {
    return (
      <div style={{ padding: '16px', textAlign: 'center', color: '#666' }}>
        <TrendingUp size={48} style={{ margin: '0 auto 16px', opacity: 0.5 }} />
        <div>Trend analysis coming soon</div>
        <div style={{ fontSize: '14px', marginTop: '8px' }}>Advanced pattern detection and visualization</div>
      </div>
    );
  };

  const renderWidget = (widgetId: string) => {
    // Check for custom widgets first
    if (customWidgets && customWidgets[widgetId]) {
      const CustomWidget = customWidgets[widgetId] as React.ComponentType<any>;
      return <CustomWidget data={data} />;
    }

    // Render built-in widgets
    switch (widgetId) {
      case 'data-summary':
        return renderDataSummaryWidget();
      case 'column-stats':
        return renderColumnStatsWidget();
      case 'data-types':
        return renderDataTypesWidget();
      case 'trends':
        return renderTrendsWidget();
      default:
        return <div style={{ padding: '16px', textAlign: 'center', color: '#666' }}>Widget not found</div>;
    }
  };

  const enabledWidgets = availableWidgets.filter(widget => isWidgetEnabled(widget.id));

  return (
    <div style={{ padding: '24px', height: '100%', overflow: 'auto' }}>
      {/* Header */}
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
        <h2 style={{ margin: 0, fontSize: '24px', fontWeight: '600' }}>Dashboard</h2>
        <div style={{ display: 'flex', gap: '8px' }}>
          <Button
            variant={viewMode === 'grid' ? 'primary' : 'ghost'}
            onClick={() => setViewMode('grid')}
            style={{ display: 'flex', alignItems: 'center', gap: '8px' }}
          >
            <Grid size={16} />
            Grid
          </Button>
          <Button
            variant={viewMode === 'list' ? 'primary' : 'ghost'}
            onClick={() => setViewMode('list')}
            style={{ display: 'flex', alignItems: 'center', gap: '8px' }}
          >
            <List size={16} />
            List
          </Button>
          <Button
            variant="ghost"
            onClick={() => setShowSettings(!showSettings)}
            style={{ display: 'flex', alignItems: 'center', gap: '8px' }}
          >
            <Settings size={16} />
            Settings
          </Button>
        </div>
      </div>

      {/* Widget Settings Panel */}
      {showSettings && (
        <Card style={{ marginBottom: '24px', padding: '16px' }}>
          <h3 style={{ margin: '0 0 16px 0', fontSize: '18px', fontWeight: '600' }}>Widget Settings</h3>
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fill, minmax(250px, 1fr))', gap: '12px' }}>
            {availableWidgets.map(widget => {
              const IconComponent = widget.icon;
              const enabled = isWidgetEnabled(widget.id);
              
              return (
                <div
                  key={widget.id}
                  style={{
                    padding: '12px',
                    border: '1px solid #e0e0e0',
                    borderRadius: '8px',
                    cursor: 'pointer',
                    backgroundColor: enabled ? '#f0f9ff' : '#fff',
                    borderColor: enabled ? '#0ea5e9' : '#e0e0e0'
                  }}
                  onClick={() => onWidgetToggle(widget.id)}
                >
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '8px' }}>
                    <IconComponent size={16} />
                    <span style={{ fontWeight: '500' }}>{widget.name}</span>
                    {enabled ? <Eye size={14} /> : <EyeOff size={14} />}
                  </div>
                  <p style={{ margin: 0, fontSize: '14px', color: '#666' }}>
                    {widget.description}
                  </p>
                  <Badge 
                    variant="secondary" 
                    style={{ marginTop: '8px', fontSize: '12px' }}
                  >
                    {widget.category}
                  </Badge>
                </div>
              );
            })}
          </div>
        </Card>
      )}

      {/* Widgets Display */}
      {enabledWidgets.length === 0 ? (
        <Card style={{ padding: '48px', textAlign: 'center' }}>
          <Settings size={48} style={{ margin: '0 auto 16px', opacity: 0.5 }} />
          <h3 style={{ margin: '0 0 8px 0', fontSize: '18px', fontWeight: '600' }}>No Widgets Enabled</h3>
          <p style={{ margin: '0 0 16px 0', color: '#666' }}>Enable widgets from the settings panel to start visualizing your data.</p>
          <Button
            variant="primary"
            onClick={() => setShowSettings(true)}
            style={{ display: 'flex', alignItems: 'center', gap: '8px', margin: '0 auto' }}
          >
            <Settings size={16} />
            Open Settings
          </Button>
        </Card>
      ) : (
        <div 
          style={{
            display: 'grid',
            gridTemplateColumns: viewMode === 'grid' 
              ? 'repeat(auto-fit, minmax(400px, 1fr))' 
              : '1fr',
            gap: '24px'
          }}
        >
          {enabledWidgets.map(widget => {
            const IconComponent = widget.icon;
            
            return (
              <Card key={widget.id} style={{ overflow: 'hidden' }}>
                <div style={{ 
                  padding: '16px', 
                  borderBottom: '1px solid #f0f0f0',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between'
                }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <IconComponent size={18} />
                    <h3 style={{ margin: 0, fontSize: '16px', fontWeight: '600' }}>{widget.name}</h3>
                  </div>
                  <Button
                    variant="ghost"
                    onClick={() => onWidgetToggle(widget.id)}
                    style={{ padding: '4px' }}
                  >
                    <EyeOff size={14} />
                  </Button>
                </div>
                {renderWidget(widget.id)}
              </Card>
            );
          })}
        </div>
      )}
    </div>
  );
};