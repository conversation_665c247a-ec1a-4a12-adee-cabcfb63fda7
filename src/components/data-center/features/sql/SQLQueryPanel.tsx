import React, { useState, useEffect } from 'react';
import { Play, Copy, Download, RefreshCw, Database, Code, AlertCircle, CheckCircle } from 'lucide-react';
import { SQLQueryPanelProps } from '../../types';
import { <PERSON><PERSON>, Card, Badge } from '../../ui/StyledComponents';

/**
 * SQL Query panel component for generating and executing SQL queries
 */
export const SQLQueryPanel: React.FC<SQLQueryPanelProps> = ({
  data,
  onQueryGenerate,
  onQueryExecute,
  currentQuery,
  queryResult,
  isExecuting
}) => {
  const [generatedQuery, setGeneratedQuery] = useState<string>(currentQuery || '');
  const [queryType, setQueryType] = useState<'select' | 'insert' | 'update' | 'delete'>('select');
  const [queryOptions, setQueryOptions] = useState({
    includeHeaders: true,
    limitRows: 100,
    orderBy: '',
    whereClause: ''
  });

  useEffect(() => {
    if (currentQuery) {
      setGeneratedQuery(currentQuery);
    }
  }, [currentQuery]);

  const handleGenerateQuery = () => {
    if (!data || !data.headers) return;

    let query = '';
    const tableName = 'data_table';
    const columns = data.headers.join(', ');

    switch (queryType) {
      case 'select':
        query = `SELECT ${columns}\nFROM ${tableName}`;
        if (queryOptions.whereClause) {
          query += `\nWHERE ${queryOptions.whereClause}`;
        }
        if (queryOptions.orderBy) {
          query += `\nORDER BY ${queryOptions.orderBy}`;
        }
        if (queryOptions.limitRows > 0) {
          query += `\nLIMIT ${queryOptions.limitRows}`;
        }
        break;
      case 'insert':
        const sampleValues = data.headers.map(() => '?').join(', ');
        query = `INSERT INTO ${tableName} (${columns})\nVALUES (${sampleValues})`;
        break;
      case 'update':
        const updateClauses = data.headers.map(col => `${col} = ?`).join(',\n    ');
        query = `UPDATE ${tableName}\nSET ${updateClauses}\nWHERE id = ?`;
        break;
      case 'delete':
        query = `DELETE FROM ${tableName}\nWHERE ${queryOptions.whereClause || 'id = ?'}`;
        break;
    }

    setGeneratedQuery(query);
    onQueryGenerate(query);
  };

  const handleExecuteQuery = () => {
    if (generatedQuery.trim()) {
      onQueryExecute(generatedQuery);
    }
  };

  const handleCopyQuery = async () => {
    try {
      await navigator.clipboard.writeText(generatedQuery);
      // Could add a toast notification here
    } catch (err) {
      console.error('Failed to copy query:', err);
    }
  };

  const handleDownloadQuery = () => {
    const blob = new Blob([generatedQuery], { type: 'text/sql' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'query.sql';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getExecutionStatusIcon = () => {
    if (isExecuting) return <RefreshCw size={16} className="animate-spin" />;
    if (queryResult && !queryResult.error) return <CheckCircle size={16} />;
    if (queryResult && queryResult.error) return <AlertCircle size={16} />;
    return null;
  };

  const getExecutionStatusColor = () => {
    if (isExecuting) return 'warning';
    if (queryResult && !queryResult.error) return 'success';
    if (queryResult && queryResult.error) return 'error';
    return 'secondary';
  };

  return (
    <div style={{ padding: '24px', height: '100%', overflow: 'auto' }}>
      {/* Query Generation Section */}
      <Card style={{ marginBottom: '24px', padding: '16px' }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '16px' }}>
          <Database size={18} />
          <h3 style={{ margin: 0, fontSize: '18px', fontWeight: '600' }}>SQL Query Generator</h3>
        </div>

        {/* Query Type Selection */}
        <div style={{ marginBottom: '16px' }}>
          <label style={{ display: 'block', marginBottom: '8px', fontSize: '14px', fontWeight: '500' }}>
            Query Type
          </label>
          <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
            {(['select', 'insert', 'update', 'delete'] as const).map(type => (
              <Button
                key={type}
                variant={queryType === type ? 'primary' : 'ghost'}
                onClick={() => setQueryType(type)}
                style={{ textTransform: 'uppercase', fontSize: '12px' }}
              >
                {type}
              </Button>
            ))}
          </div>
        </div>

        {/* Query Options */}
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '16px', marginBottom: '16px' }}>
          {queryType === 'select' && (
            <>
              <div>
                <label style={{ display: 'block', marginBottom: '4px', fontSize: '14px' }}>Limit Rows</label>
                <input
                  type="number"
                  value={queryOptions.limitRows}
                  onChange={(e) => setQueryOptions(prev => ({ ...prev, limitRows: parseInt(e.target.value) || 0 }))}
                  style={{ width: '100%', padding: '8px', border: '1px solid #e0e0e0', borderRadius: '4px' }}
                />
              </div>
              <div>
                <label style={{ display: 'block', marginBottom: '4px', fontSize: '14px' }}>Order By</label>
                <select
                  value={queryOptions.orderBy}
                  onChange={(e) => setQueryOptions(prev => ({ ...prev, orderBy: e.target.value }))}
                  style={{ width: '100%', padding: '8px', border: '1px solid #e0e0e0', borderRadius: '4px' }}
                >
                  <option value="">None</option>
                  {data?.headers?.map(header => (
                    <option key={header} value={header}>{header}</option>
                  ))}
                </select>
              </div>
            </>
          )}
          
          {(queryType === 'select' || queryType === 'delete') && (
            <div style={{ gridColumn: '1 / -1' }}>
              <label style={{ display: 'block', marginBottom: '4px', fontSize: '14px' }}>WHERE Clause</label>
              <input
                type="text"
                value={queryOptions.whereClause}
                onChange={(e) => setQueryOptions(prev => ({ ...prev, whereClause: e.target.value }))}
                placeholder="e.g., column_name = 'value'"
                style={{ width: '100%', padding: '8px', border: '1px solid #e0e0e0', borderRadius: '4px' }}
              />
            </div>
          )}
        </div>

        <Button
          variant="primary"
          onClick={handleGenerateQuery}
          disabled={!data || !data.headers}
          style={{ display: 'flex', alignItems: 'center', gap: '8px' }}
        >
          <Code size={16} />
          Generate Query
        </Button>
      </Card>

      {/* Query Editor */}
      <Card style={{ marginBottom: '24px', padding: '16px' }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '16px' }}>
          <h3 style={{ margin: 0, fontSize: '18px', fontWeight: '600' }}>SQL Query</h3>
          <div style={{ display: 'flex', gap: '8px' }}>
            <Button
              variant="ghost"
              onClick={handleCopyQuery}
              disabled={!generatedQuery.trim()}
              style={{ display: 'flex', alignItems: 'center', gap: '8px' }}
            >
              <Copy size={16} />
              Copy
            </Button>
            <Button
              variant="ghost"
              onClick={handleDownloadQuery}
              disabled={!generatedQuery.trim()}
              style={{ display: 'flex', alignItems: 'center', gap: '8px' }}
            >
              <Download size={16} />
              Download
            </Button>
          </div>
        </div>

        <textarea
          value={generatedQuery}
          onChange={(e) => setGeneratedQuery(e.target.value)}
          placeholder="Enter your SQL query here or use the generator above..."
          style={{
            width: '100%',
            height: '200px',
            padding: '12px',
            border: '1px solid #e0e0e0',
            borderRadius: '8px',
            fontFamily: 'Monaco, Consolas, "Courier New", monospace',
            fontSize: '14px',
            resize: 'vertical'
          }}
        />

        <div style={{ display: 'flex', gap: '12px', marginTop: '16px' }}>
          <Button
            variant="primary"
            onClick={handleExecuteQuery}
            disabled={!generatedQuery.trim() || isExecuting}
            style={{ display: 'flex', alignItems: 'center', gap: '8px' }}
          >
            <Play size={16} />
            {isExecuting ? 'Executing...' : 'Execute Query'}
          </Button>
        </div>
      </Card>

      {/* Execution Status */}
      <Card style={{ marginBottom: '24px', padding: '16px' }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '16px' }}>
          <h3 style={{ margin: 0, fontSize: '18px', fontWeight: '600' }}>Execution Status</h3>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            {getExecutionStatusIcon()}
            <Badge variant={getExecutionStatusColor()}>
              {isExecuting ? 'Executing' :
               queryResult && !queryResult.error ? 'Success' :
               queryResult && queryResult.error ? 'Error' : 'Ready'}
            </Badge>
          </div>
        </div>

        {queryResult && (
          <div style={{ fontSize: '14px' }}>
            {queryResult.error ? (
              <div style={{ color: '#dc3545' }}>
                <strong>Error:</strong> {queryResult.error}
              </div>
            ) : (
              <div style={{ color: '#28a745' }}>
                <strong>Success:</strong> Query executed successfully
                {queryResult.rowCount && (
                  <span> - {queryResult.rowCount} rows affected</span>
                )}
              </div>
            )}
          </div>
        )}
      </Card>

      {/* Data Summary */}
      {data && (
        <Card style={{ padding: '16px' }}>
          <h3 style={{ margin: '0 0 12px 0', fontSize: '16px', fontWeight: '600' }}>Data Summary</h3>
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', gap: '12px' }}>
            <div>
              <div style={{ fontSize: '14px', color: '#666' }}>Total Rows</div>
              <div style={{ fontSize: '18px', fontWeight: '600' }}>{data.totalRows || data.rows?.length || 0}</div>
            </div>
            <div>
              <div style={{ fontSize: '14px', color: '#666' }}>Columns</div>
              <div style={{ fontSize: '18px', fontWeight: '600' }}>{data.headers?.length || 0}</div>
            </div>
            <div>
              <div style={{ fontSize: '14px', color: '#666' }}>Data Types</div>
              <div style={{ fontSize: '18px', fontWeight: '600' }}>
                {data.dataTypes ? Object.keys(data.dataTypes).length : 0}
              </div>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};