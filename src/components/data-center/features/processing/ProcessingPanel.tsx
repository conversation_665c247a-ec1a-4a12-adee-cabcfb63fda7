import React, { useState } from 'react';
import { <PERSON>, Pause, RotateCcw, <PERSON><PERSON>s, AlertCircle, CheckCircle, Clock } from 'lucide-react';
import { ProcessingPanelProps, ProcessingResult } from '../../types';
import { <PERSON><PERSON>, Card, Badge } from '../../ui/StyledComponents';

/**
 * Processing panel component for data transformation and analysis
 */
export const ProcessingPanel: React.FC<ProcessingPanelProps> = ({
  data,
  onProcessingComplete,
  templates,
  pythonEnvStatus,
  isProcessing
}) => {
  const [selectedOperations, setSelectedOperations] = useState<string[]>([]);
  const [processingConfig, setProcessingConfig] = useState({
    cleanData: true,
    normalizeValues: false,
    detectOutliers: true,
    generateStats: true
  });

  const availableOperations = [
    { id: 'clean', label: 'Data Cleaning', description: 'Remove duplicates and handle missing values' },
    { id: 'normalize', label: 'Normalization', description: 'Standardize data formats and values' },
    { id: 'outliers', label: 'Outlier Detection', description: 'Identify and flag unusual data points' },
    { id: 'stats', label: 'Statistical Analysis', description: 'Generate descriptive statistics' },
    { id: 'transform', label: 'Data Transformation', description: 'Apply custom transformations' }
  ];

  const handleOperationToggle = (operationId: string) => {
    setSelectedOperations(prev => 
      prev.includes(operationId)
        ? prev.filter(id => id !== operationId)
        : [...prev, operationId]
    );
  };

  const handleStartProcessing = () => {
    if (selectedOperations.length > 0) {
      // Mock processing result for now
      const mockResult: ProcessingResult = {
        content: 'Processed data content',
        metadata: {
          operations: selectedOperations,
          config: processingConfig,
          processedAt: new Date().toISOString()
        }
      };
      onProcessingComplete(mockResult);
    }
  };

  const getStatusIcon = () => {
    if (isProcessing) return <Clock size={16} />;
    if (pythonEnvStatus?.isActive) return <CheckCircle size={16} />;
    if (pythonEnvStatus?.error) return <AlertCircle size={16} />;
    return null;
  };

  const getStatusColor = () => {
    if (isProcessing) return 'warning';
    if (pythonEnvStatus?.isActive) return 'success';
    if (pythonEnvStatus?.error) return 'error';
    return 'secondary';
  };

  return (
    <div style={{ padding: '24px', height: '100%', overflow: 'auto' }}>
      {/* Processing Status */}
      <Card style={{ marginBottom: '24px', padding: '16px' }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '16px' }}>
          <h3 style={{ margin: 0, fontSize: '18px', fontWeight: '600' }}>Processing Status</h3>
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            {getStatusIcon()}
            <Badge variant={getStatusColor()}>
              {isProcessing ? 'Processing...' : 
               pythonEnvStatus?.isActive ? 'Ready' :
               pythonEnvStatus?.error ? 'Error' : 'Not Ready'}
            </Badge>
          </div>
        </div>
        
        {pythonEnvStatus && (
          <div style={{ fontSize: '14px', color: '#666' }}>
            <p>Python Environment: {pythonEnvStatus.isActive ? 'Active' : 'Inactive'}</p>
            {pythonEnvStatus.error && (
              <div style={{ marginTop: '8px' }}>
                <strong>Error:</strong>
                <p style={{ color: '#dc3545', margin: '4px 0' }}>{pythonEnvStatus.error}</p>
              </div>
            )}
          </div>
        )}
      </Card>

      {/* Operation Selection */}
      <Card style={{ marginBottom: '24px', padding: '16px' }}>
        <h3 style={{ margin: '0 0 16px 0', fontSize: '18px', fontWeight: '600' }}>Processing Operations</h3>
        <div style={{ display: 'grid', gap: '12px' }}>
          {availableOperations.map(operation => (
            <div
              key={operation.id}
              style={{
                padding: '12px',
                border: '1px solid #e0e0e0',
                borderRadius: '8px',
                cursor: 'pointer',
                backgroundColor: selectedOperations.includes(operation.id) ? '#f0f9ff' : '#fff',
                borderColor: selectedOperations.includes(operation.id) ? '#0ea5e9' : '#e0e0e0'
              }}
              onClick={() => handleOperationToggle(operation.id)}
            >
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '4px' }}>
                <input
                  type="checkbox"
                  checked={selectedOperations.includes(operation.id)}
                  onChange={() => handleOperationToggle(operation.id)}
                  style={{ margin: 0 }}
                />
                <strong>{operation.label}</strong>
              </div>
              <p style={{ margin: 0, fontSize: '14px', color: '#666' }}>
                {operation.description}
              </p>
            </div>
          ))}
        </div>
      </Card>

      {/* Processing Configuration */}
      <Card style={{ marginBottom: '24px', padding: '16px' }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '16px' }}>
          <Settings size={18} />
          <h3 style={{ margin: 0, fontSize: '18px', fontWeight: '600' }}>Configuration</h3>
        </div>
        
        <div style={{ display: 'grid', gap: '12px' }}>
          {Object.entries(processingConfig).map(([key, value]) => (
            <label key={key} style={{ display: 'flex', alignItems: 'center', gap: '8px', cursor: 'pointer' }}>
              <input
                type="checkbox"
                checked={value}
                onChange={(e) => setProcessingConfig(prev => ({
                  ...prev,
                  [key]: e.target.checked
                }))}
              />
              <span style={{ textTransform: 'capitalize' }}>
                {key.replace(/([A-Z])/g, ' $1').toLowerCase()}
              </span>
            </label>
          ))}
        </div>
      </Card>

      {/* Action Buttons */}
      <div style={{ display: 'flex', gap: '12px', flexWrap: 'wrap' }}>
        <Button
          variant="primary"
          onClick={handleStartProcessing}
          disabled={isProcessing || selectedOperations.length === 0 || !data}
          style={{ display: 'flex', alignItems: 'center', gap: '8px' }}
        >
          <Play size={16} />
          Start Processing
        </Button>
        
        {isProcessing && (
          <Button
            variant="secondary"
            disabled
            style={{ display: 'flex', alignItems: 'center', gap: '8px' }}
          >
            <Pause size={16} />
            Processing...
          </Button>
        )}
        
        <Button
          variant="ghost"
          onClick={() => {
            setSelectedOperations([]);
            setProcessingConfig({
              cleanData: true,
              normalizeValues: false,
              detectOutliers: true,
              generateStats: true
            });
          }}
          disabled={isProcessing}
          style={{ display: 'flex', alignItems: 'center', gap: '8px' }}
        >
          <RotateCcw size={16} />
          Reset
        </Button>
      </div>

      {/* Data Summary */}
      {data && (
        <Card style={{ marginTop: '24px', padding: '16px' }}>
          <h3 style={{ margin: '0 0 12px 0', fontSize: '16px', fontWeight: '600' }}>Data Summary</h3>
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', gap: '12px' }}>
            <div>
              <div style={{ fontSize: '14px', color: '#666' }}>Total Rows</div>
              <div style={{ fontSize: '18px', fontWeight: '600' }}>{data.rows?.length || 0}</div>
            </div>
            <div>
              <div style={{ fontSize: '14px', color: '#666' }}>Columns</div>
              <div style={{ fontSize: '18px', fontWeight: '600' }}>{data.headers?.length || 0}</div>
            </div>
            <div>
              <div style={{ fontSize: '14px', color: '#666' }}>Data Types</div>
              <div style={{ fontSize: '18px', fontWeight: '600' }}>
                {data.dataTypes ? Object.keys(data.dataTypes).length : 0}
              </div>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};