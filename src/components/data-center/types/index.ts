/**
 * Shared TypeScript types for the Data Center component system
 */

// Core data types
export interface DataPreview {
  headers: string[];
  rows: any[][];
  totalRows: number;
  dataTypes?: Record<string, string>;
  statistics?: Record<string, any>;
  fullData?: any[][];
}

export interface ProcessingResult {
  content: string;
  metadata: Record<string, any>;
  chunks?: Array<{
    text: string;
    metadata: Record<string, any>;
  }>;
  embeddings?: number[][];
}

export interface SQLGenerationResult {
  sql: string;
  isValid: boolean;
  error?: string;
  analysis?: string;
}

export interface PythonEnvStatus {
  isInstalled: boolean;
  isActive: boolean;
  pythonVersion?: string;
  pipPackages?: string[];
  error?: string;
}

// UI State types
export type ViewType = 'dashboard' | 'grid' | 'sql' | 'upload' | 'preview' | 'process';
export type StageType = 'upload' | 'preview' | 'process' | 'sql' | 'export';

export interface DataCenterState {
  // File and data state
  selectedFile: File | null;
  dataPreview: DataPreview | null;
  processingResult: ProcessingResult | null;
  sqlResult: SQLGenerationResult | null;
  
  // UI state
  activeView: ViewType;
  currentStage: StageType;
  isProcessing: boolean;
  
  // Sidebar state
  sidebarCollapsed: boolean;
  showMetadataSidebar: boolean;
  showControlPanel: boolean;
  
  // Context menu state
  contextMenu: {
    visible: boolean;
    x: number;
    y: number;
  };
}

// Component Props interfaces
export interface DataCenterLayoutProps {
  children: React.ReactNode;
  sidebar?: React.ReactNode;
  metadataSidebar?: React.ReactNode;
  showSidebar: boolean;
  showMetadataSidebar: boolean;
  onToggleSidebar: () => void;
  onToggleMetadataSidebar: () => void;
  className?: string;
}

export interface DataCenterHeaderProps {
  activeView: ViewType;
  onViewChange: (view: ViewType) => void;
  onBack?: () => void;
  showControlPanel: boolean;
  onToggleControlPanel: () => void;
  breadcrumbs?: Array<{
    label: string;
    href?: string;
    onClick?: () => void;
  }>;
}

export interface DataCenterSidebarProps {
  currentStage: StageType;
  onStageChange: (stage: StageType) => void;
  collapsed: boolean;
  onToggleCollapse: () => void;
  stages?: Array<{
    id: StageType;
    label: string;
    icon: React.ComponentType;
    completed: boolean;
  }>;
}

export interface MetadataSidebarProps {
  selectedFile?: File;
  onClose: () => void;
  showStorageOverview?: boolean;
  showContextualHelp?: boolean;
}

export interface FileUploadZoneProps {
  onFileUpload: (files: File[]) => void;
  acceptedTypes: string[];
  maxSize: number;
  isProcessing: boolean;
  multiple?: boolean;
  disabled?: boolean;
}

export interface DataPreviewPanelProps {
  data: DataPreview;
  onDataChange: (data: DataPreview) => void;
  showStatistics: boolean;
  showFilters?: boolean;
  showPagination?: boolean;
  pageSize?: number;
}

export interface ProcessingPanelProps {
  data: DataPreview;
  onProcessingComplete: (result: ProcessingResult) => void;
  templates: Record<string, any>;
  pythonEnvStatus: PythonEnvStatus;
  isProcessing: boolean;
}

export interface SQLQueryPanelProps {
  data: DataPreview;
  onQueryGenerate: (sql: string) => void;
  onQueryExecute: (sql: string) => void;
  currentQuery?: string;
  queryResult?: any;
  isExecuting: boolean;
}

export interface DashboardViewProps {
  widgets: string[];
  data: DataPreview | null;
  onWidgetToggle: (widget: string) => void;
  customWidgets?: Record<string, React.ComponentType>;
}

export interface MainContentRendererProps {
  activeView: ViewType;
  currentStage: StageType;
  data: DataPreview | null;
  processingResult: ProcessingResult | null;
  sqlResult: SQLGenerationResult | null;
  onStageChange: (stage: StageType) => void;
}

// Action interfaces
export interface DataCenterActions {
  // File actions
  handleFileUpload: (files: File[]) => void;
  handleFileSelect: (file: File) => void;
  handleFileRemove: (file: File) => void;
  
  // Processing actions
  handleDataProcess: (template: string) => void;
  handleSQLGenerate: () => void;
  handleSQLExecute: (sql: string) => void;
  
  // UI actions
  handleViewChange: (view: ViewType) => void;
  handleStageChange: (stage: StageType) => void;
  handleSidebarToggle: () => void;
  handleMetadataSidebarToggle: () => void;
  handleControlPanelToggle: () => void;
  
  // Context menu actions
  showContextMenu: (x: number, y: number) => void;
  hideContextMenu: () => void;
}

// Provider Props
export interface DataCenterProvidersProps {
  config: any; // Will be properly typed when we refactor config
  children: React.ReactNode;
  onDataChange?: (data: any) => void;
}

// Hook return types
export interface UseDataCenterStateReturn {
  state: DataCenterState;
  actions: DataCenterActions;
}

// Event types
export interface FileUploadEvent {
  files: File[];
  timestamp: Date;
  source: 'drag-drop' | 'file-picker' | 'paste';
}

export interface ProcessingEvent {
  type: 'start' | 'progress' | 'complete' | 'error';
  data?: any;
  error?: string;
  progress?: number;
  timestamp: Date;
}

export interface ViewChangeEvent {
  from: ViewType;
  to: ViewType;
  timestamp: Date;
}

// Configuration types (simplified for now)
export interface DataCenterComponentConfig {
  enabledFeatures: string[];
  theme: 'light' | 'dark' | 'auto';
  accessibility: {
    highContrast: boolean;
    screenReader: boolean;
    keyboardNavigation: boolean;
  };
  performance: {
    virtualizeGrids: boolean;
    lazyLoadWidgets: boolean;
    cacheSize: number;
  };
}

// Error types
export interface DataCenterError {
  code: string;
  message: string;
  details?: any;
  timestamp: Date;
  recoverable: boolean;
}

// Context menu types
export interface ContextMenuOption {
  id: string;
  label: string;
  icon?: React.ComponentType;
  onClick: () => void;
  disabled?: boolean;
  separator?: boolean;
}

// Widget types
export interface WidgetConfig {
  id: string;
  name: string;
  component: React.ComponentType<any>;
  defaultProps?: Record<string, any>;
  category: 'visualization' | 'analysis' | 'utility' | 'export';
  dependencies?: string[];
}

// Export all types
// TODO: Add these exports when the respective files are created
// export * from './events';
// export * from './config';
// export * from './widgets';