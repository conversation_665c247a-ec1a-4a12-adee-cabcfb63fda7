/**
 * Enhanced TypeScript type definitions for Data Center Component Library
 * Provides strict type safety, comprehensive interfaces, and utility types
 */

// ============================================================================
// CORE TYPES
// ============================================================================

/**
 * Strict string literal types for better type safety
 */
export type DataCenterFeature = 
  | 'fileUpload'
  | 'dataPreview'
  | 'sqlGeneration'
  | 'pythonScripting'
  | 'dataVisualization'
  | 'dashboard'
  | 'machineLearning'
  | 'customWidgets'
  | 'collaboration'
  | 'security'
  | 'compliance'
  | 'audit'
  | 'performanceMonitoring'
  | 'apiIntegration'
  | 'dataExport'
  | 'scheduling'
  | 'notifications';

export type ThemeMode = 'light' | 'dark' | 'auto';
export type LayoutMode = 'compact' | 'comfortable' | 'spacious';
export type DataFormat = 'csv' | 'json' | 'xml' | 'parquet' | 'excel';
export type ChartType = 'bar' | 'line' | 'pie' | 'scatter' | 'heatmap' | 'histogram';
export type DatabaseType = 'postgresql' | 'mysql' | 'sqlite' | 'mongodb' | 'redis';

/**
 * Branded types for enhanced type safety
 */
export type FileId = string & { readonly __brand: 'FileId' };
export type UserId = string & { readonly __brand: 'UserId' };
export type SessionId = string & { readonly __brand: 'SessionId' };
export type QueryId = string & { readonly __brand: 'QueryId' };
export type WidgetId = string & { readonly __brand: 'WidgetId' };

/**
 * Utility type for creating branded strings
 */
export type Brand<T, B> = T & { readonly __brand: B };

// ============================================================================
// CONFIGURATION TYPES
// ============================================================================

/**
 * Enhanced configuration interface with strict typing
 */
export interface DataCenterConfig {
  readonly id: string;
  readonly version: string;
  readonly features: ReadonlyArray<DataCenterFeature>;
  readonly theme: ThemeConfig;
  readonly performance: PerformanceConfig;
  readonly accessibility: AccessibilityConfig;
  readonly security: SecurityConfig;
  readonly ui: UIConfig;
  readonly data: DataConfig;
  readonly integrations: IntegrationConfig;
  readonly metadata: ConfigMetadata;
}

/**
 * Theme configuration with comprehensive options
 */
export interface ThemeConfig {
  readonly mode: ThemeMode;
  readonly primaryColor: string;
  readonly secondaryColor: string;
  readonly accentColor: string;
  readonly customCSS?: string;
  readonly darkModeOverrides?: Partial<Pick<ThemeConfig, 'primaryColor' | 'secondaryColor' | 'accentColor'>>;
}

/**
 * Performance configuration with detailed options
 */
export interface PerformanceConfig {
  readonly virtualizeGrids: boolean;
  readonly lazyLoadWidgets: boolean;
  readonly cacheSize: number;
  readonly debounceMs: number;
  readonly maxConcurrentRequests: number;
  readonly enableServiceWorker: boolean;
  readonly compressionLevel: 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9;
  readonly memoryLimit: number; // in MB
}

/**
 * Accessibility configuration
 */
export interface AccessibilityConfig {
  readonly highContrast: boolean;
  readonly screenReader: boolean;
  readonly keyboardNavigation: boolean;
  readonly fontSize: 'small' | 'medium' | 'large' | 'extra-large';
  readonly reducedMotion: boolean;
  readonly focusIndicators: boolean;
  readonly ariaLabels: Record<string, string>;
}

/**
 * Security configuration
 */
export interface SecurityConfig {
  readonly enableCSP: boolean;
  readonly allowedOrigins: ReadonlyArray<string>;
  readonly encryptLocalStorage: boolean;
  readonly sessionTimeout: number; // in minutes
  readonly maxFileSize: number; // in bytes
  readonly allowedFileTypes: ReadonlyArray<DataFormat>;
  readonly sanitizeInput: boolean;
}

/**
 * UI configuration
 */
export interface UIConfig {
  readonly layout: LayoutMode;
  readonly showToolbar: boolean;
  readonly showSidebar: boolean;
  readonly showStatusBar: boolean;
  readonly compactMode: boolean;
  readonly animations: boolean;
  readonly tooltips: boolean;
  readonly breadcrumbs: boolean;
}

/**
 * Data configuration
 */
export interface DataConfig {
  readonly maxRows: number;
  readonly maxColumns: number;
  readonly defaultFormat: DataFormat;
  readonly autoDetectTypes: boolean;
  readonly preserveFormatting: boolean;
  readonly enableFiltering: boolean;
  readonly enableSorting: boolean;
  readonly enableGrouping: boolean;
}

/**
 * Integration configuration
 */
export interface IntegrationConfig {
  readonly databases: ReadonlyArray<DatabaseConfig>;
  readonly apis: ReadonlyArray<APIConfig>;
  readonly webhooks: ReadonlyArray<WebhookConfig>;
  readonly exportFormats: ReadonlyArray<DataFormat>;
}

/**
 * Configuration metadata
 */
export interface ConfigMetadata {
  readonly name: string;
  readonly description: string;
  readonly author: string;
  readonly createdAt: Date;
  readonly updatedAt: Date;
  readonly tags: ReadonlyArray<string>;
  readonly environment: 'development' | 'staging' | 'production';
}

// ============================================================================
// DATA TYPES
// ============================================================================

/**
 * Enhanced data file interface
 */
export interface DataFile {
  readonly id: FileId;
  readonly name: string;
  readonly size: number;
  readonly type: DataFormat;
  readonly lastModified: Date;
  readonly checksum: string;
  readonly metadata: FileMetadata;
  readonly content?: DataContent;
  readonly preview?: DataPreview;
}

/**
 * File metadata
 */
export interface FileMetadata {
  readonly encoding: string;
  readonly delimiter?: string;
  readonly hasHeader: boolean;
  readonly rowCount: number;
  readonly columnCount: number;
  readonly schema?: DataSchema;
  readonly tags: ReadonlyArray<string>;
}

/**
 * Data content with type safety
 */
export interface DataContent {
  readonly headers: ReadonlyArray<string>;
  readonly rows: ReadonlyArray<ReadonlyArray<DataValue>>;
  readonly types: ReadonlyArray<DataType>;
  readonly statistics?: DataStatistics;
}

/**
 * Data preview for large files
 */
export interface DataPreview {
  readonly sampleRows: ReadonlyArray<ReadonlyArray<DataValue>>;
  readonly totalRows: number;
  readonly isComplete: boolean;
  readonly loadedPercentage: number;
}

/**
 * Data schema definition
 */
export interface DataSchema {
  readonly columns: ReadonlyArray<ColumnSchema>;
  readonly primaryKey?: string;
  readonly foreignKeys: ReadonlyArray<ForeignKeySchema>;
  readonly indexes: ReadonlyArray<IndexSchema>;
}

/**
 * Column schema
 */
export interface ColumnSchema {
  readonly name: string;
  readonly type: DataType;
  readonly nullable: boolean;
  readonly unique: boolean;
  readonly defaultValue?: DataValue;
  readonly constraints: ReadonlyArray<ColumnConstraint>;
  readonly description?: string;
}

/**
 * Data types with strict typing
 */
export type DataType = 
  | 'string'
  | 'number'
  | 'integer'
  | 'float'
  | 'boolean'
  | 'date'
  | 'datetime'
  | 'time'
  | 'json'
  | 'array'
  | 'object'
  | 'null';

/**
 * Data value union type
 */
export type DataValue = 
  | string
  | number
  | boolean
  | Date
  | null
  | undefined
  | Record<string, unknown>
  | ReadonlyArray<unknown>;

/**
 * Column constraints
 */
export interface ColumnConstraint {
  readonly type: 'min' | 'max' | 'length' | 'pattern' | 'enum' | 'custom';
  readonly value: DataValue;
  readonly message?: string;
}

/**
 * Foreign key schema
 */
export interface ForeignKeySchema {
  readonly column: string;
  readonly referencedTable: string;
  readonly referencedColumn: string;
  readonly onDelete: 'cascade' | 'restrict' | 'set null' | 'no action';
  readonly onUpdate: 'cascade' | 'restrict' | 'set null' | 'no action';
}

/**
 * Index schema
 */
export interface IndexSchema {
  readonly name: string;
  readonly columns: ReadonlyArray<string>;
  readonly unique: boolean;
  readonly type: 'btree' | 'hash' | 'gin' | 'gist';
}

/**
 * Data statistics
 */
export interface DataStatistics {
  readonly rowCount: number;
  readonly columnCount: number;
  readonly nullCount: number;
  readonly duplicateCount: number;
  readonly memoryUsage: number;
  readonly columnStats: ReadonlyArray<ColumnStatistics>;
}

/**
 * Column statistics
 */
export interface ColumnStatistics {
  readonly name: string;
  readonly type: DataType;
  readonly nullCount: number;
  readonly uniqueCount: number;
  readonly min?: DataValue;
  readonly max?: DataValue;
  readonly mean?: number;
  readonly median?: number;
  readonly mode?: DataValue;
  readonly standardDeviation?: number;
}

// ============================================================================
// COMPONENT TYPES
// ============================================================================

/**
 * Enhanced component props with strict typing
 */
export interface DataCenterProps {
  readonly config: DataCenterConfig;
  readonly initialData?: ReadonlyArray<DataFile>;
  readonly onDataChange?: (data: ReadonlyArray<DataFile>) => void;
  readonly onError?: (error: DataCenterError) => void;
  readonly onConfigChange?: (config: DataCenterConfig) => void;
  readonly className?: string;
  readonly style?: React.CSSProperties;
  readonly testId?: string;
}

/**
 * Widget component props
 */
export interface WidgetProps<T = unknown> {
  readonly id: WidgetId;
  readonly title: string;
  readonly data: T;
  readonly config: WidgetConfig;
  readonly onUpdate?: (data: T) => void;
  readonly onRemove?: () => void;
  readonly onResize?: (size: WidgetSize) => void;
  readonly className?: string;
}

/**
 * Widget configuration
 */
export interface WidgetConfig {
  readonly type: string;
  readonly size: WidgetSize;
  readonly position: WidgetPosition;
  readonly settings: Record<string, unknown>;
  readonly permissions: WidgetPermissions;
}

/**
 * Widget size
 */
export interface WidgetSize {
  readonly width: number;
  readonly height: number;
  readonly minWidth?: number;
  readonly minHeight?: number;
  readonly maxWidth?: number;
  readonly maxHeight?: number;
}

/**
 * Widget position
 */
export interface WidgetPosition {
  readonly x: number;
  readonly y: number;
  readonly z?: number;
}

/**
 * Widget permissions
 */
export interface WidgetPermissions {
  readonly canEdit: boolean;
  readonly canDelete: boolean;
  readonly canMove: boolean;
  readonly canResize: boolean;
  readonly canShare: boolean;
}

// ============================================================================
// API TYPES
// ============================================================================

/**
 * API response wrapper
 */
export interface APIResponse<T> {
  readonly success: boolean;
  readonly data?: T;
  readonly error?: APIError;
  readonly metadata: ResponseMetadata;
}

/**
 * API error
 */
export interface APIError {
  readonly code: string;
  readonly message: string;
  readonly details?: Record<string, unknown>;
  readonly timestamp: Date;
  readonly requestId: string;
}

/**
 * Response metadata
 */
export interface ResponseMetadata {
  readonly requestId: string;
  readonly timestamp: Date;
  readonly duration: number;
  readonly version: string;
  readonly rateLimit?: RateLimitInfo;
}

/**
 * Rate limit information
 */
export interface RateLimitInfo {
  readonly limit: number;
  readonly remaining: number;
  readonly resetTime: Date;
}

/**
 * Database configuration
 */
export interface DatabaseConfig {
  readonly id: string;
  readonly name: string;
  readonly type: DatabaseType;
  readonly connectionString: string;
  readonly ssl: boolean;
  readonly poolSize: number;
  readonly timeout: number;
}

/**
 * API configuration
 */
export interface APIConfig {
  readonly id: string;
  readonly name: string;
  readonly baseUrl: string;
  readonly apiKey?: string;
  readonly headers: Record<string, string>;
  readonly timeout: number;
  readonly retries: number;
}

/**
 * Webhook configuration
 */
export interface WebhookConfig {
  readonly id: string;
  readonly name: string;
  readonly url: string;
  readonly events: ReadonlyArray<string>;
  readonly secret?: string;
  readonly active: boolean;
}

// ============================================================================
// ERROR TYPES
// ============================================================================

/**
 * Enhanced error types
 */
export abstract class DataCenterError extends Error {
  abstract readonly code: string;
  abstract readonly severity: 'low' | 'medium' | 'high' | 'critical';
  readonly timestamp: Date;
  readonly context?: Record<string, unknown>;

  constructor(message: string, context?: Record<string, unknown>) {
    super(message);
    this.name = this.constructor.name;
    this.timestamp = new Date();
    this.context = context;
  }
}

export class ValidationError extends DataCenterError {
  readonly code = 'VALIDATION_ERROR';
  readonly severity = 'medium' as const;
  readonly field?: string;
  readonly value?: unknown;

  constructor(message: string, field?: string, value?: unknown) {
    super(message, { field, value });
    this.field = field;
    this.value = value;
  }
}

export class ConfigurationError extends DataCenterError {
  readonly code = 'CONFIGURATION_ERROR';
  readonly severity = 'high' as const;
  readonly configPath?: string;

  constructor(message: string, configPath?: string) {
    super(message, { configPath });
    this.configPath = configPath;
  }
}

export class DataProcessingError extends DataCenterError {
  readonly code = 'DATA_PROCESSING_ERROR';
  readonly severity = 'medium' as const;
  readonly fileId?: FileId;
  readonly rowIndex?: number;

  constructor(message: string, fileId?: FileId, rowIndex?: number) {
    super(message, { fileId, rowIndex });
    this.fileId = fileId;
    this.rowIndex = rowIndex;
  }
}

export class SecurityError extends DataCenterError {
  readonly code = 'SECURITY_ERROR';
  readonly severity = 'critical' as const;
  readonly userId?: UserId;
  readonly action?: string;

  constructor(message: string, userId?: UserId, action?: string) {
    super(message, { userId, action });
    this.userId = userId;
    this.action = action;
  }
}

// ============================================================================
// UTILITY TYPES
// ============================================================================

/**
 * Utility type for making properties optional
 */
export type PartialBy<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

/**
 * Utility type for making properties required
 */
export type RequiredBy<T, K extends keyof T> = T & Required<Pick<T, K>>;

/**
 * Deep readonly utility type
 */
export type DeepReadonly<T> = {
  readonly [P in keyof T]: T[P] extends (infer U)[]
    ? ReadonlyArray<DeepReadonly<U>>
    : T[P] extends Record<string, unknown>
    ? DeepReadonly<T[P]>
    : T[P];
};

/**
 * Extract keys of a specific type
 */
export type KeysOfType<T, U> = {
  [K in keyof T]: T[K] extends U ? K : never;
}[keyof T];

/**
 * Create a type with only specific keys
 */
export type PickByType<T, U> = Pick<T, KeysOfType<T, U>>;

/**
 * Omit keys of a specific type
 */
export type OmitByType<T, U> = Omit<T, KeysOfType<T, U>>;

/**
 * Function type for event handlers
 */
export type EventHandler<T = unknown> = (event: T) => void | Promise<void>;

/**
 * Async function type
 */
export type AsyncFunction<T extends ReadonlyArray<unknown>, R> = (...args: T) => Promise<R>;

/**
 * Type guard function
 */
export type TypeGuard<T> = (value: unknown) => value is T;

/**
 * Predicate function
 */
export type Predicate<T> = (value: T) => boolean;

/**
 * Transformer function
 */
export type Transformer<T, R> = (value: T) => R;

/**
 * Validator function
 */
export type Validator<T> = (value: T) => ValidationResult;

/**
 * Validation result
 */
export interface ValidationResult {
  readonly valid: boolean;
  readonly errors: ReadonlyArray<string>;
  readonly warnings: ReadonlyArray<string>;
}

// ============================================================================
// HOOK TYPES
// ============================================================================

/**
 * Hook return types
 */
export interface UseDataCenterReturn {
  readonly config: DataCenterConfig;
  readonly data: ReadonlyArray<DataFile>;
  readonly loading: boolean;
  readonly error: DataCenterError | null;
  readonly updateConfig: (config: Partial<DataCenterConfig>) => void;
  readonly addData: (file: DataFile) => void;
  readonly removeData: (id: FileId) => void;
  readonly clearData: () => void;
  readonly exportData: (format: DataFormat) => Promise<Blob>;
}

export interface UseDataProcessingReturn {
  readonly processFile: (file: File) => Promise<DataFile>;
  readonly validateData: (data: DataContent) => ValidationResult;
  readonly transformData: <T>(data: DataContent, transformer: Transformer<DataContent, T>) => T;
  readonly filterData: (data: DataContent, predicate: Predicate<ReadonlyArray<DataValue>>) => DataContent;
  readonly sortData: (data: DataContent, column: string, direction: 'asc' | 'desc') => DataContent;
  readonly groupData: (data: DataContent, column: string) => Record<string, DataContent>;
}

// ============================================================================
// CONTEXT TYPES
// ============================================================================

/**
 * Context value types
 */
export interface DataCenterContextValue {
  readonly config: DataCenterConfig;
  readonly data: ReadonlyArray<DataFile>;
  readonly selectedFile: DataFile | null;
  readonly loading: boolean;
  readonly error: DataCenterError | null;
  readonly actions: DataCenterActions;
}

/**
 * Context actions
 */
export interface DataCenterActions {
  readonly updateConfig: (config: Partial<DataCenterConfig>) => void;
  readonly selectFile: (id: FileId) => void;
  readonly addFile: (file: DataFile) => void;
  readonly removeFile: (id: FileId) => void;
  readonly updateFile: (id: FileId, updates: Partial<DataFile>) => void;
  readonly clearFiles: () => void;
  readonly exportFiles: (format: DataFormat) => Promise<Blob>;
  readonly setError: (error: DataCenterError | null) => void;
  readonly setLoading: (loading: boolean) => void;
}

// ============================================================================
// EXPORT ALL TYPES
// ============================================================================

// All types are exported directly from this file
// Additional type modules can be added here as they are created