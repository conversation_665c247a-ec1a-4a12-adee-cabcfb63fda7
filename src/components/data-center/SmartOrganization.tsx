import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/card';
import { But<PERSON> } from '../ui/button';
import { Badge } from '../ui/badge';
import { ScrollArea } from '../ui/scroll-area';
import { Separator } from '../ui/separator';
import { Progress } from '../ui/progress';
import {
  FolderTree,
  Sparkles,
  FileText,
  Database,
  Image,
  Video,
  Music,
  Archive,
  Code,
  Calendar,
  Tag,
  TrendingUp,
  Users,
  MapPin,
  Clock,
  CheckCircle,
  XCircle,
  RefreshCw
} from 'lucide-react';

interface FileItem {
  id: string;
  name: string;
  type: string;
  size: number;
  uploadDate: Date;
  tags: string[];
  content?: string;
  metadata?: Record<string, any>;
}

interface OrganizationSuggestion {
  id: string;
  type: 'folder' | 'tag' | 'rename' | 'merge' | 'archive';
  title: string;
  description: string;
  confidence: number;
  files: FileItem[];
  suggestedPath?: string;
  suggestedName?: string;
  reasoning: string;
  benefits: string[];
  impact: 'low' | 'medium' | 'high';
}

interface SmartOrganizationProps {
  files: FileItem[];
  onApplySuggestion: (suggestion: OrganizationSuggestion) => void;
  onDismissSuggestion: (suggestionId: string) => void;
}

const getFileIcon = (type: string) => {
  const lowerType = type.toLowerCase();
  if (lowerType.includes('image')) return <Image className="w-4 h-4" />;
  if (lowerType.includes('video')) return <Video className="w-4 h-4" />;
  if (lowerType.includes('audio')) return <Music className="w-4 h-4" />;
  if (lowerType.includes('zip') || lowerType.includes('archive')) return <Archive className="w-4 h-4" />;
  if (lowerType.includes('code') || lowerType.includes('script')) return <Code className="w-4 h-4" />;
  if (lowerType.includes('database') || lowerType.includes('sql')) return <Database className="w-4 h-4" />;
  return <FileText className="w-4 h-4" />;
};

const getImpactColor = (impact: OrganizationSuggestion['impact']) => {
  switch (impact) {
    case 'high': return 'text-red-600 bg-red-50';
    case 'medium': return 'text-yellow-600 bg-yellow-50';
    case 'low': return 'text-green-600 bg-green-50';
    default: return 'text-gray-600 bg-gray-50';
  }
};

const getSuggestionIcon = (type: OrganizationSuggestion['type']) => {
  switch (type) {
    case 'folder': return <FolderTree className="w-4 h-4" />;
    case 'tag': return <Tag className="w-4 h-4" />;
    case 'rename': return <FileText className="w-4 h-4" />;
    case 'merge': return <TrendingUp className="w-4 h-4" />;
    case 'archive': return <Archive className="w-4 h-4" />;
    default: return <Sparkles className="w-4 h-4" />;
  }
};

// AI-powered organization logic
const generateOrganizationSuggestions = (files: FileItem[]): OrganizationSuggestion[] => {
  const suggestions: OrganizationSuggestion[] = [];
  
  // Group by file type
  const typeGroups = files.reduce((acc, file) => {
    const type = file.type.split('/')[0] || 'other';
    if (!acc[type]) acc[type] = [];
    acc[type].push(file);
    return acc;
  }, {} as Record<string, FileItem[]>);

  // Suggest folder organization by type
  Object.entries(typeGroups).forEach(([type, typeFiles]) => {
    if (typeFiles.length >= 3) {
      suggestions.push({
        id: `folder-${type}`,
        type: 'folder',
        title: `Organize ${type} files`,
        description: `Create a "${type}" folder for ${typeFiles.length} files`,
        confidence: 0.85,
        files: typeFiles,
        suggestedPath: `/${type}`,
        reasoning: `Files of the same type are easier to find when grouped together`,
        benefits: [
          'Improved file discovery',
          'Cleaner root directory',
          'Better organization'
        ],
        impact: typeFiles.length > 10 ? 'high' : 'medium'
      });
    }
  });

  // Group by date patterns
  const dateGroups = files.reduce((acc, file) => {
    const month = file.uploadDate.toISOString().slice(0, 7); // YYYY-MM
    if (!acc[month]) acc[month] = [];
    acc[month].push(file);
    return acc;
  }, {} as Record<string, FileItem[]>);

  Object.entries(dateGroups).forEach(([month, monthFiles]) => {
    if (monthFiles.length >= 5) {
      const [year, monthNum] = month.split('-');
      const monthName = new Date(parseInt(year), parseInt(monthNum) - 1).toLocaleString('default', { month: 'long' });
      
      suggestions.push({
        id: `folder-date-${month}`,
        type: 'folder',
        title: `Organize by date: ${monthName} ${year}`,
        description: `Group ${monthFiles.length} files from ${monthName} ${year}`,
        confidence: 0.75,
        files: monthFiles,
        suggestedPath: `/${year}/${monthName}`,
        reasoning: `Files from the same time period often relate to the same project or context`,
        benefits: [
          'Chronological organization',
          'Easy to find recent work',
          'Historical context'
        ],
        impact: 'medium'
      });
    }
  });

  // Suggest tags based on common patterns
  const commonWords = files.flatMap(file => 
    file.name.toLowerCase().split(/[^a-z0-9]+/).filter(word => word.length > 3)
  );
  
  const wordCounts = commonWords.reduce((acc, word) => {
    acc[word] = (acc[word] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const frequentWords = Object.entries(wordCounts)
    .filter(([_, count]) => count >= 3)
    .sort(([_, a], [__, b]) => b - a)
    .slice(0, 5);

  frequentWords.forEach(([word, count]) => {
    const relatedFiles = files.filter(file => 
      file.name.toLowerCase().includes(word)
    );
    
    suggestions.push({
      id: `tag-${word}`,
      type: 'tag',
      title: `Add "${word}" tag`,
      description: `Tag ${relatedFiles.length} files containing "${word}"`,
      confidence: 0.7,
      files: relatedFiles,
      reasoning: `Common naming patterns suggest related content`,
      benefits: [
        'Improved searchability',
        'Content categorization',
        'Quick filtering'
      ],
      impact: 'low'
    });
  });

  // Suggest renaming for unclear names
  const unclearFiles = files.filter(file => {
    const name = file.name.toLowerCase();
    return name.includes('untitled') || 
           name.includes('copy') || 
           name.match(/^(file|document|data)\d*\./i) ||
           name.length < 5;
  });

  if (unclearFiles.length > 0) {
    suggestions.push({
      id: 'rename-unclear',
      type: 'rename',
      title: 'Rename unclear files',
      description: `${unclearFiles.length} files have generic or unclear names`,
      confidence: 0.9,
      files: unclearFiles,
      reasoning: 'Descriptive file names improve discoverability and organization',
      benefits: [
        'Better file identification',
        'Improved search results',
        'Professional organization'
      ],
      impact: 'medium'
    });
  }

  // Suggest archiving old files
  const oldFiles = files.filter(file => {
    const daysSinceUpload = (Date.now() - file.uploadDate.getTime()) / (1000 * 60 * 60 * 24);
    return daysSinceUpload > 90; // Files older than 3 months
  });

  if (oldFiles.length >= 5) {
    suggestions.push({
      id: 'archive-old',
      type: 'archive',
      title: 'Archive old files',
      description: `${oldFiles.length} files are older than 3 months`,
      confidence: 0.6,
      files: oldFiles,
      suggestedPath: '/archive',
      reasoning: 'Archiving old files keeps the workspace clean while preserving data',
      benefits: [
        'Cleaner workspace',
        'Faster file browsing',
        'Preserved history'
      ],
      impact: 'low'
    });
  }

  return suggestions.sort((a, b) => b.confidence - a.confidence);
};

export const SmartOrganization: React.FC<SmartOrganizationProps> = ({
  files,
  onApplySuggestion,
  onDismissSuggestion
}) => {
  const [suggestions, setSuggestions] = useState<OrganizationSuggestion[]>([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [appliedSuggestions, setAppliedSuggestions] = useState<Set<string>>(new Set());

  useEffect(() => {
    if (files.length > 0) {
      setIsAnalyzing(true);
      // Simulate AI analysis delay
      setTimeout(() => {
        const newSuggestions = generateOrganizationSuggestions(files);
        setSuggestions(newSuggestions);
        setIsAnalyzing(false);
      }, 1500);
    }
  }, [files]);

  const handleApplySuggestion = (suggestion: OrganizationSuggestion) => {
    setAppliedSuggestions(prev => new Set([...prev, suggestion.id]));
    onApplySuggestion(suggestion);
  };

  const handleDismissSuggestion = (suggestionId: string) => {
    setSuggestions(prev => prev.filter(s => s.id !== suggestionId));
    onDismissSuggestion(suggestionId);
  };

  const refreshSuggestions = () => {
    setIsAnalyzing(true);
    setTimeout(() => {
      const newSuggestions = generateOrganizationSuggestions(files);
      setSuggestions(newSuggestions);
      setIsAnalyzing(false);
    }, 1000);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="w-5 h-5 text-purple-500" />
            Smart Organization
          </CardTitle>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={refreshSuggestions}
            disabled={isAnalyzing}
          >
            <RefreshCw className={`w-4 h-4 mr-1 ${isAnalyzing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
        <p className="text-sm text-gray-600">
          AI-powered suggestions to organize your {files.length} files
        </p>
      </CardHeader>
      
      <CardContent>
        {isAnalyzing ? (
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <RefreshCw className="w-4 h-4 animate-spin" />
              <span className="text-sm">Analyzing files...</span>
            </div>
            <Progress value={75} className="w-full" />
          </div>
        ) : suggestions.length === 0 ? (
          <div className="text-center py-8">
            <Sparkles className="w-12 h-12 text-gray-300 mx-auto mb-4" />
            <p className="text-gray-500">No organization suggestions available</p>
            <p className="text-sm text-gray-400 mt-1">
              Upload more files to get AI-powered organization suggestions
            </p>
          </div>
        ) : (
          <ScrollArea className="h-96">
            <div className="space-y-4">
              {suggestions.map((suggestion) => {
                const isApplied = appliedSuggestions.has(suggestion.id);
                
                return (
                  <Card key={suggestion.id} className={`${isApplied ? 'opacity-50' : ''}`}>
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center gap-2">
                          {getSuggestionIcon(suggestion.type)}
                          <h3 className="font-medium">{suggestion.title}</h3>
                          <Badge 
                            variant="secondary" 
                            className={`text-xs ${getImpactColor(suggestion.impact)}`}
                          >
                            {suggestion.impact} impact
                          </Badge>
                        </div>
                        
                        <div className="flex items-center gap-1">
                          <span className="text-xs text-gray-500">
                            {Math.round(suggestion.confidence * 100)}% confident
                          </span>
                        </div>
                      </div>
                      
                      <p className="text-sm text-gray-600 mb-3">
                        {suggestion.description}
                      </p>
                      
                      <div className="mb-3">
                        <p className="text-xs font-medium text-gray-700 mb-1">Reasoning:</p>
                        <p className="text-xs text-gray-600">{suggestion.reasoning}</p>
                      </div>
                      
                      <div className="mb-3">
                        <p className="text-xs font-medium text-gray-700 mb-1">Benefits:</p>
                        <div className="flex flex-wrap gap-1">
                          {suggestion.benefits.map((benefit, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {benefit}
                            </Badge>
                          ))}
                        </div>
                      </div>
                      
                      <div className="mb-4">
                        <p className="text-xs font-medium text-gray-700 mb-2">
                          Affected files ({suggestion.files.length}):
                        </p>
                        <div className="flex flex-wrap gap-1 max-h-20 overflow-y-auto">
                          {suggestion.files.slice(0, 10).map((file) => (
                            <div key={file.id} className="flex items-center gap-1 text-xs bg-gray-50 px-2 py-1 rounded">
                              {getFileIcon(file.type)}
                              <span className="truncate max-w-24" title={file.name}>
                                {file.name}
                              </span>
                            </div>
                          ))}
                          {suggestion.files.length > 10 && (
                            <span className="text-xs text-gray-500 px-2 py-1">
                              +{suggestion.files.length - 10} more
                            </span>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex gap-2">
                        {!isApplied ? (
                          <>
                            <Button 
                              size="sm" 
                              onClick={() => handleApplySuggestion(suggestion)}
                              className="flex-1"
                            >
                              <CheckCircle className="w-3 h-3 mr-1" />
                              Apply
                            </Button>
                            <Button 
                              size="sm" 
                              variant="outline" 
                              onClick={() => handleDismissSuggestion(suggestion.id)}
                            >
                              <XCircle className="w-3 h-3 mr-1" />
                              Dismiss
                            </Button>
                          </>
                        ) : (
                          <div className="flex items-center gap-2 text-sm text-green-600">
                            <CheckCircle className="w-4 h-4" />
                            Applied
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </ScrollArea>
        )}
      </CardContent>
    </Card>
  );
};

export default SmartOrganization;