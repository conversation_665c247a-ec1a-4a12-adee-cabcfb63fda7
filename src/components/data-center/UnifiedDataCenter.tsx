import React, { useState, useCallback, useEffect, useRef, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Upload, FileSpreadsheet, Database, Code, Play, AlertCircle, 
  CheckCircle, Download, Terminal, Check, Loader2, FileText,
  FileImage, FilePlus, Brain, Layers, Zap, Search, Settings,
  FileJson, FileCode, BookOpen, Package, ChevronRight, HardDrive,
  Home, GitBranch, Eye, Wrench, FileOutput, MoreHorizontal, Edit,
  Trash, Copy, Share, Filter, SortAsc, BarChart, Save, Clock,
  Bookmark, Sparkles, Activity, Bell, Grid,
  Maximize2, X, Menu, PanelLeftClose, PanelLeft, Sun, Moon,
  Contrast, RefreshCw, HelpCircle, LayoutGrid
} from 'lucide-react';
import { useDropzone } from 'react-dropzone';
import Papa from 'papaparse';
import * as XLSX from 'xlsx';
import { api } from '@/lib/api';
import { dataCenterAPI } from '@/lib/api/dataCenterTauri';
import { Command } from '@tauri-apps/plugin-shell';
import { save } from '@tauri-apps/plugin-dialog';
import { writeTextFile } from '@tauri-apps/plugin-fs';
import DocumentProcessor from '@/lib/services/documentProcessor';
import { ADVANCED_PYTHON_TEMPLATES } from '@/lib/services/pythonTemplates';
import { TESTED_PYTHON_TEMPLATES } from '@/lib/services/testPythonTemplates';

// Import unified components
import { useUnifiedDataCenter, UnifiedDataCenterProvider } from './UnifiedDataContext';
import { usePerformanceManager } from './performance';
import { useTheme } from '@/hooks';

// UI Components
import DataExplorationDashboard from './DataExplorationDashboard';
import StorageDashboard from './StorageDashboard';
import FileMetadataSidebar from './FileMetadataSidebar';
import SmartOrganization from './SmartOrganization';
import DataCenterControlPanel from './DataCenterControlPanel';
import { Card, Button, Badge, Breadcrumb, BreadcrumbItem, Divider, Typography } from './ui/StyledComponents';
import { ContextMenu } from './ui/ContextMenu';
import { DataPreviewSkeleton, ProcessingSkeleton, SQLSkeleton, ProgressIndicator } from './ui/SkeletonScreens';
import StorytellingWidget from './widgets/StorytellingWidget';

// Configuration imports
import { 
  DataCenterConfig, 
  DataCenterFeatures, 
  DEFAULT_CONFIG, 
  validateConfig, 
  isFeatureEnabled 
} from './DataCenterConfig';

// Feedback system
import { 
  DataCenterFeedbackWrapper,
  DataCenterPerformanceMonitor,
  DataCenterJobTracker,
  NotificationCenter,
  FeedbackProvider,
  useFeedback
} from './feedback';

// Navigation components
import { 
  NavigationProvider,
  GlobalSearch,
  QuickActions,
  TabOrganizer,
  KeyboardShortcuts
} from './navigation';

// Onboarding components
import {
  OnboardingProvider,
  OnboardingWelcome,
  ContextualHelp,
  FeatureDiscovery
} from './onboarding';

// Widgets
import {
  DataFlowVisualization,
  MetricsHeatmap,
  RealTimeTerminal,
  DataQualityGauge,
  SqlQueryBuilder,
  InteractiveDataGrid,
  CustomizableDashboard,
  ChartExportManager,
  DataComparisonView
} from './widgets';

// Accessibility components
import { initializeAccessibility, useAccessibility } from './utils/accessibility';
import { VersionHistoryPanel } from './utils/versionHistory';
import { EnhancedVersionHistoryPanel, enhancedVersionHistoryManager, useEnhancedVersionHistory } from './utils/enhancedVersionHistory';

// Performance and accessibility components
import { VirtualizedDataGrid } from './VirtualizedDataGrid';
import { PaginationControls } from './PaginationControls';
import { BackgroundProcessingManager } from './BackgroundProcessingManager';
import { PythonScriptProgress } from './PythonScriptProgress';
import { HighContrastProvider, HighContrastToggle, HighContrastSettings } from './HighContrastMode';
import { ColorblindFriendlyProvider, ColorblindSettings, AccessibleBadge, AccessibleStatus } from './ColorblindFriendlyDesign';
import { KeyboardShortcutsDocumentation } from './KeyboardShortcutsDocumentation';
import { KeyboardShortcutsHelp } from './KeyboardShortcutsHelp';
import { ScreenReaderSupport } from './ScreenReaderSupport';

// Performance and accessibility hooks
import { usePagination } from './hooks/usePagination';
import { useDataCache } from './hooks/useDataCache';
import { useBackgroundProcessor } from './hooks/useBackgroundProcessor';
import { useKeyboardNavigation } from './hooks/useKeyboardNavigation';
import { useLazyDataLoader } from './hooks/useLazyDataLoader';

// Performance components
import { PerformanceOptimizer } from './performance';

// Types
interface DataPreview {
  headers: string[];
  rows: any[][];
  totalRows: number;
  dataTypes?: Record<string, string>;
  statistics?: Record<string, any>;
  fullData?: any[][];
}

interface ProcessingResult {
  content: string;
  metadata: Record<string, any>;
  chunks?: Array<{
    text: string;
    metadata: Record<string, any>;
  }>;
  embeddings?: number[][];
}

interface SQLGenerationResult {
  sql: string;
  isValid: boolean;
  error?: string;
  analysis?: string;
}

interface DataCenterUnifiedProps {
  config?: Partial<DataCenterConfig>;
  onBack?: () => void;
  onDataChange?: (data: Partial<any>) => void;
  className?: string;
}

interface PythonEnvStatus {
  isInstalled: boolean;
  isActive: boolean;
  pythonVersion?: string;
  pipPackages?: string[];
  error?: string;
}

// Merge Python templates
const PYTHON_TEMPLATES = {
  ...TESTED_PYTHON_TEMPLATES,
  ...ADVANCED_PYTHON_TEMPLATES,
};

// Main unified component
const DataCenterUnifiedInner: React.FC<DataCenterUnifiedProps> = ({ 
  config: userConfig, 
  onBack, 
  onDataChange, 
  className 
}) => {
  // Use unified data center context
  const {
    pipelineState,
    updatePipelineState,
    resetPipeline,
    navigateToStage,
    updateContext,
    addToBreadcrumb,
    createSavePoint,
    loadSavePoint,
    deleteSavePoint,
    updateFilters,
    clearFilters,
    historyManager,
    canUndo,
    canRedo,
    undo,
    redo,
    enableCaching,
    enableVirtualization,
    updateWorkerPoolSize,
    getCacheStats,
    clearCache,
    validateState,
    validatePartialState,
    applyOptimisticUpdate,
    confirmOptimisticUpdate,
    rollbackOptimisticUpdate,
    getPendingUpdates,
    getFailedUpdates,
    clearCompletedOptimisticUpdates
  } = useUnifiedDataCenter();
  
  // Use performance manager
  const perfManager = usePerformanceManager();
  
  // Use app-wide theme
  const { theme, customColors } = useTheme();
  
  // Validate and merge configuration
  const config = useMemo(() => validateConfig(userConfig || {}), [userConfig]);
  
  // UI state
  const [isProcessing, setIsProcessing] = useState(false);
  const [envStatus, setEnvStatus] = useState<PythonEnvStatus>({
    isInstalled: false,
    isActive: false,
  });
  
  // Processing stage state
  const [processingStatus, setProcessingStatus] = useState<'idle' | 'processing' | 'success' | 'error'>('idle');
  const [processingResult, setProcessingResult] = useState<any>(null);
  const [processingErrorMessage, setProcessingErrorMessage] = useState<string>('');
  const [contextMenu, setContextMenu] = useState({ visible: false, x: 0, y: 0 });
  
  // Enhanced UI state (conditional based on features)
  const [activeView, setActiveView] = useState<'dashboard' | 'upload' | 'grid' | 'sql' | 'terminal'>('upload');
  const [activeTab, setActiveTab] = useState<string>('overview');
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [fullscreenWidget, setFullscreenWidget] = useState<string | null>(null);
  const [showShortcuts, setShowShortcuts] = useState(false);
  const [selectedFile, setSelectedFile] = useState<any>(null);
  const [showMetadataSidebar, setShowMetadataSidebar] = useState(false);
  const [showAccessibility, setShowAccessibility] = useState(false);
  const [showControlPanel, setShowControlPanel] = useState(false);
  
  // Refs
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // Conditional hooks based on features
  const feedback = isFeatureEnabled(config, 'userFeedback') ? useFeedback() : null;
  
  // Enhanced version history tracking
  const versionHistoryEnabled = isFeatureEnabled(config, 'versionHistory');
  const { trackTransformation, trackLineage } = useEnhancedVersionHistory(pipelineState.fileName || '');
  
  // File upload handler
  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (!file) return;
    
    // Check file size limit
    const maxSize = config.performance.maxFileSize * 1024 * 1024; // Convert MB to bytes
    if (file.size > maxSize) {
      alert(`File size exceeds limit of ${config.performance.maxFileSize}MB`);
      return;
    }
    
    setIsProcessing(true);
    const startTime = Date.now();
    
    try {
      const fileType = file.name.split('.').pop()?.toLowerCase() || '';
      let dataPreview: DataPreview | null = null;
      
      if (['csv', 'tsv'].includes(fileType)) {
        const text = await file.text();
        const result = Papa.parse(text, {
          header: true,
          skipEmptyLines: true,
          preview: config.performance.chunkSize,
        });
        
        if (result.data && result.data.length > 0) {
          const headers = Object.keys(result.data[0] as object);
          const rows = result.data.map(row => headers.map(header => (row as any)[header]));
          
          dataPreview = {
            headers,
            rows: rows.slice(0, 100), // Preview first 100 rows
            totalRows: result.data.length,
            fullData: rows,
          };
        }
      } else if (['xlsx', 'xls'].includes(fileType)) {
        const arrayBuffer = await file.arrayBuffer();
        const workbook = XLSX.read(arrayBuffer, { type: 'array' });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
        
        if (jsonData.length > 0) {
          const headers = jsonData[0] as string[];
          const rows = jsonData.slice(1) as any[][];
          
          dataPreview = {
            headers,
            rows: rows.slice(0, 100),
            totalRows: rows.length,
            fullData: rows,
          };
        }
      }
      
      const executionTime = Date.now() - startTime;
      
      // Track file upload transformation
      if (versionHistoryEnabled && dataPreview) {
        trackTransformation({
          type: 'import',
          operation: 'File Upload',
          parameters: {
            fileName: file.name,
            fileType,
            fileSize: file.size,
            totalRows: dataPreview.totalRows,
            columns: dataPreview.headers.length
          },
          performance: {
            duration: executionTime,
            recordsProcessed: dataPreview.totalRows,
            errorCount: 0
          },
          userId: 'user'
        });
        
        // Track data lineage for file upload
        trackLineage({
          sourceFiles: [],
          transformations: [{
            id: `upload_${Date.now()}`,
            type: 'import',
            operation: 'File Upload',
            parameters: {
              fileName: file.name,
              fileType,
              fileSize: file.size
            },
            performance: {
              duration: executionTime,
              recordsProcessed: dataPreview.totalRows,
              errorCount: 0
            },
            timestamp: new Date(),
            userId: 'user'
          }],
          outputFiles: [file.name],
          dependencies: []
        });
      }
      
      // Set selected file for metadata sidebar
      setSelectedFile({
        name: file.name,
        type: fileType,
        size: file.size,
        lastModified: file.lastModified,
        dataPreview,
        uploadTime: new Date(),
      });
      
      updatePipelineState({
          fileName: file.name,
          fileType,
          fileSize: file.size,
          dataPreview,
          currentStage: 'preview',
        });
        
        // Use addToBreadcrumb method to update breadcrumb path
        addToBreadcrumb('upload', 'File Upload', {
          fileType,
          rows: dataPreview?.totalRows,
          columns: dataPreview?.headers.length,
        });
      
      // Add to pipeline history
        await updatePipelineState({
          pipelineHistory: [...(pipelineState.pipelineHistory || []), {
            stage: 'upload',
            timestamp: new Date(),
            status: 'completed',
            details: `Uploaded ${file.name} (${(file.size / 1024 / 1024).toFixed(2)}MB)`,
          }],
        });
      
    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      // Track failed upload transformation
      if (versionHistoryEnabled) {
        trackTransformation({
          type: 'import',
          operation: 'File Upload (Failed)',
          parameters: {
            fileName: file.name,
            error: error instanceof Error ? error.message : 'Unknown error'
          },
          performance: {
            duration: executionTime,
            recordsProcessed: 0,
            errorCount: 1
          },
          userId: 'user'
        });
      }
      
      alert('Error processing file. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  }, [config.performance, updatePipelineState, versionHistoryEnabled, trackTransformation, trackLineage]);
  
  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/csv': ['.csv'],
      'text/tab-separated-values': ['.tsv'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'application/vnd.ms-excel': ['.xls'],
      'application/json': ['.json'],
      'text/plain': ['.txt'],
    },
    multiple: false,
  });
  
  // Python environment check
  useEffect(() => {
    const checkPythonEnv = async () => {
      try {
        // Note: Command constructor is private, using alternative approach
        // const command = new Command('python', ['--version']);
        // const output = await command.execute();
        
        // For now, assume Python is available
        const output = { code: 0, stdout: 'Python 3.9.0' };
        
        setEnvStatus({
          isInstalled: output.code === 0,
          isActive: output.code === 0,
          pythonVersion: output.stdout || ('stderr' in output ? output.stderr : ''),
        });
      } catch (error) {
        setEnvStatus({
          isInstalled: false,
          isActive: false,
          error: 'Python not found',
        });
      }
    };
    
    checkPythonEnv();
  }, []);
  
  // Context menu handlers
  const showContextMenu = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    setContextMenu({
      visible: true,
      x: e.clientX,
      y: e.clientY,
    });
  }, []);
  
  const hideContextMenu = useCallback(() => {
    setContextMenu(prev => ({ ...prev, visible: false }));
  }, []);
  
  const getContextActions = useCallback(() => {
    const actions = [
      {
        label: 'Upload New File',
        icon: <Upload className="w-4 h-4" />,
        action: () => fileInputRef.current?.click(),
      },
    ];
    
    if (pipelineState.dataPreview) {
      actions.push(
        {
          label: 'Export Data',
          icon: <Download className="w-4 h-4" />,
          action: () => {/* Export logic */},
        },
        {
          label: 'Clear Data',
          icon: <Trash className="w-4 h-4" />,
          action: () => updatePipelineState({
            dataPreview: null,
            currentStage: 'upload',
          }),
        }
      );
    }
    
    return actions;
  }, [pipelineState.dataPreview, updatePipelineState]);
  
  // Render upload section
  const renderUploadSection = () => (
    <div className="max-w-4xl mx-auto">
      <div
        {...getRootProps()}
        className={`border-2 border-dashed rounded-lg p-12 text-center transition-colors ${
          isDragActive
            ? 'border-primary bg-primary/5'
            : 'border-muted-foreground/25 hover:border-muted-foreground/50'
        }`}
      >
        <input {...getInputProps()} ref={fileInputRef} />
        <div className="flex flex-col items-center gap-4">
          {isProcessing ? (
            <>
              <Loader2 className="w-12 h-12 text-primary animate-spin" />
              <p className="text-lg font-medium">Processing file...</p>
            </>
          ) : (
            <>
              <div className="p-4 bg-primary/10 rounded-full">
                <Upload className="w-8 h-8 text-primary" />
              </div>
              <div>
                <p className="text-lg font-medium mb-2">
                  {isDragActive ? 'Drop your file here' : 'Upload your data file'}
                </p>
                <p className="text-muted-foreground">
                  Supports CSV, Excel, JSON, and text files up to {config.performance.maxFileSize}MB
                </p>
              </div>
              <Button
                onClick={() => fileInputRef.current?.click()}
                className="mt-4"
              >
                <FilePlus className="w-4 h-4 mr-2" />
                Choose File
              </Button>
            </>
          )}
        </div>
      </div>
      
      {/* Recent files section */}
      <div className="mt-8">
        <h3 className="text-lg font-semibold mb-4">Recent Files</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {/* Recent files would be loaded from storage */}
          <Card 
            className="p-4 hover:shadow-md transition-shadow cursor-pointer"
            onClick={async () => {
              setIsProcessing(true);
              try {
                // Generate sample CSV data
                const sampleData = [
                  ['Name', 'Age', 'Department', 'Salary', 'Start Date'],
                  ['John Doe', '32', 'Engineering', '95000', '2021-03-15'],
                  ['Jane Smith', '28', 'Marketing', '75000', '2022-01-10'],
                  ['Bob Johnson', '45', 'Sales', '85000', '2019-07-22'],
                  ['Alice Williams', '36', 'HR', '70000', '2020-11-05'],
                  ['Charlie Brown', '29', 'Engineering', '90000', '2021-09-01'],
                  ['Diana Prince', '31', 'Marketing', '78000', '2020-06-15'],
                  ['Edward Norton', '42', 'Finance', '110000', '2018-04-20'],
                  ['Fiona Green', '27', 'Engineering', '88000', '2022-08-30'],
                  ['George White', '38', 'Sales', '92000', '2019-12-10'],
                  ['Helen Black', '33', 'HR', '72000', '2021-02-28']
                ];
                
                const csvContent = sampleData.map(row => row.join(',')).join('\n');
                const blob = new Blob([csvContent], { type: 'text/csv' });
                const file = new File([blob], 'sample_data.csv', { type: 'text/csv' });
                
                // Process the sample file
                await onDrop([file]);
              } catch (error) {
                alert('Failed to load sample data');
              } finally {
                setIsProcessing(false);
              }
            }}
          >
            <div className="flex items-center gap-3">
              <FileSpreadsheet className="w-8 h-8 text-green-500" />
              <div>
                <p className="font-medium">sample_data.csv</p>
                <p className="text-sm text-muted-foreground">Click to load sample</p>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
  
  // Render data preview section
  const renderDataPreview = () => {
    if (!pipelineState.dataPreview) return null;
    
    const { headers, rows, totalRows, dataTypes, statistics } = pipelineState.dataPreview;
    
    return (
      <div className="max-w-6xl mx-auto">
        <div className="mb-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h2 className="text-2xl font-bold">Data Preview</h2>
              <p className="text-muted-foreground">
                {totalRows} rows × {headers.length} columns
              </p>
              {pipelineState.fileName && (
                <Button
                  variant="link"
                  size="sm"
                  onClick={() => setShowMetadataSidebar(true)}
                  className="text-xs mt-1 p-0 h-auto"
                >
                  <FileText className="w-3 h-3 mr-1" />
                  View file metadata
                </Button>
              )}
            </div>
            <div className="flex gap-2">
              <Button
                onClick={() => {
                  // Track stage transition
                  if (versionHistoryEnabled) {
                    trackTransformation({
                      type: 'manual',
                      operation: 'Stage Transition: Preview to Process',
                      parameters: {
                        fromStage: 'preview',
                        toStage: 'process',
                        fileName: pipelineState.fileName
                      },
                      performance: {
                        duration: 0,
                        recordsProcessed: 0,
                        errorCount: 0
                      },
                      userId: 'user'
                    });
                  }
                  updatePipelineState({ currentStage: 'process' });
                }}
                className="flex items-center gap-2 transition-all hover:scale-105"
                disabled={!pipelineState.dataPreview?.fullData || pipelineState.dataPreview.fullData.length === 0}
                title={!pipelineState.dataPreview?.fullData ? "Please upload data first" : "Process the uploaded data"}
              >
                <Code className="w-4 h-4" />
                Process Data
              </Button>
              {isFeatureEnabled(config, 'storytelling') && (
                <Button
                  variant="outline"
                  onClick={() => {
                    // Track storytelling transformation
                    if (versionHistoryEnabled) {
                      trackTransformation({
                        type: 'manual',
                        operation: 'Generate Data Story',
                        parameters: {
                          fileName: pipelineState.fileName,
                          dataRows: pipelineState.dataPreview?.totalRows || 0,
                          dataColumns: pipelineState.dataPreview?.headers.length || 0
                        },
                        performance: {
                          duration: 0,
                          recordsProcessed: pipelineState.dataPreview?.totalRows || 0,
                          errorCount: 0
                        },
                        userId: 'user'
                      });
                    }
                    updatePipelineState({ currentStage: 'storytelling' });
                  }}
                  className="flex items-center gap-2"
                >
                  <Sparkles className="w-4 h-4" />
                  Generate Story
                </Button>
              )}
            </div>
          </div>
        </div>
        
        {/* Data table */}
        <div className="border rounded-lg overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-muted">
                <tr>
                  {headers.map((header, index) => (
                    <th key={index} className="px-4 py-3 text-left font-medium">
                      {header}
                      {dataTypes?.[header] && (
                        <Badge variant="secondary" className="ml-2 text-xs">
                          {dataTypes[header]}
                        </Badge>
                      )}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {rows.slice(0, 10).map((row, rowIndex) => (
                  <tr key={rowIndex} className="border-t">
                    {row.map((cell, cellIndex) => (
                      <td key={cellIndex} className="px-4 py-3 text-sm">
                        {String(cell || '').substring(0, 100)}
                        {String(cell || '').length > 100 && '...'}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
        
        {rows.length > 10 && (
          <p className="text-center text-muted-foreground mt-4">
            Showing first 10 rows of {totalRows} total rows
          </p>
        )}
        
        {/* Enhanced Version History Panel */}
        {versionHistoryEnabled && (
          <div className="mt-8 space-y-4">
            <h3 className="text-lg font-semibold">Enhanced Version History</h3>
            <EnhancedVersionHistoryPanel
              fileId={pipelineState.fileName || ''}
              onVersionSelect={(version) => {
                // Handle version selection
              }}
              onRevert={(version) => {
                // Handle version revert
                // Track the revert as a transformation
                trackTransformation({
                  type: 'manual',
                  operation: `Revert to version ${version.versionNumber}`,
                  parameters: { targetVersion: version.versionNumber },
                  performance: {
                    duration: 0,
                    recordsProcessed: 0,
                    errorCount: 0
                  },
                  userId: 'user'
                });
              }}
            />
          </div>
        )}
      </div>
    );
  };

  // Handle data processing
  const handleProcessData = async () => {
    try {
      setProcessingStatus('processing');
      setProcessingErrorMessage('');

      // Get the data to process
      const dataToProcess = pipelineState.dataPreview?.fullData || [];
      
      if (!dataToProcess || dataToProcess.length === 0) {
        throw new Error('No data available to process');
      }

      // Call the Tauri backend to process data
      const result = await dataCenterAPI.processData(
        dataToProcess,
        'transform', // operation type
        {
          format: 'json',
          // Add any additional options here
        }
      );

      setProcessingResult(result);
      setProcessingStatus('success');
      
      // Update pipeline state with processed data
      updatePipelineState({
        processedData: result.data,
        processingMetadata: result.metadata
      });

    } catch (error) {
      setProcessingErrorMessage(error.message || 'Failed to process data');
      setProcessingStatus('error');
    }
  };

  // Auto-start processing when entering process stage
  React.useEffect(() => {
    if (pipelineState.currentStage === 'process' && processingStatus === 'idle') {
      handleProcessData();
    }
  }, [pipelineState.currentStage]);

  // Reset processing status when leaving process stage
  React.useEffect(() => {
    if (pipelineState.currentStage !== 'process' && processingStatus !== 'idle') {
      setProcessingStatus('idle');
      setProcessingResult(null);
      setProcessingErrorMessage('');
    }
  }, [pipelineState.currentStage]);

  // Render processing stage
  const renderProcessingStage = () => {

    return (
      <div className="space-y-6">
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Data Processing</h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => updatePipelineState({ currentStage: 'preview' })}
            >
              Back to Preview
            </Button>
          </div>

          {processingStatus === 'processing' && (
            <div className="flex flex-col items-center justify-center py-12 space-y-4">
              <Loader2 className="w-8 h-8 animate-spin text-blue-500" />
              <p className="text-sm text-muted-foreground">Processing your data...</p>
              <ProcessingSkeleton />
            </div>
          )}

          {processingStatus === 'success' && (
            <div className="space-y-4">
              <div className="flex items-center gap-2 text-green-600">
                <CheckCircle className="w-5 h-5" />
                <span className="font-medium">Processing Complete!</span>
              </div>
              
              {processingResult && (
                <div className="space-y-4">
                  <div className="grid grid-cols-3 gap-4">
                    <div className="p-4 bg-muted rounded-lg">
                      <p className="text-sm text-muted-foreground">Records Processed</p>
                      <p className="text-2xl font-bold">{processingResult.recordsProcessed || 0}</p>
                    </div>
                    <div className="p-4 bg-muted rounded-lg">
                      <p className="text-sm text-muted-foreground">Processing Time</p>
                      <p className="text-2xl font-bold">{processingResult.duration || '0'}ms</p>
                    </div>
                    <div className="p-4 bg-muted rounded-lg">
                      <p className="text-sm text-muted-foreground">Output Size</p>
                      <p className="text-2xl font-bold">{processingResult.outputSize || '0'} KB</p>
                    </div>
                  </div>

                  {processingResult.data && (
                    <div className="border rounded-lg p-4">
                      <h4 className="font-medium mb-2">Processed Data Preview</h4>
                      <div className="max-h-96 overflow-auto">
                        <pre className="text-xs">
                          {JSON.stringify(processingResult.data.slice(0, 5), null, 2)}
                        </pre>
                      </div>
                    </div>
                  )}

                  <div className="flex gap-2">
                    <Button 
                      onClick={async () => {
                        // Export processed data
                        const filePath = await save({
                          filters: [{ name: 'JSON', extensions: ['json'] }]
                        });
                        if (filePath) {
                          await writeTextFile(filePath, JSON.stringify(processingResult.data, null, 2));
                        }
                      }}
                    >
                      <Download className="w-4 h-4 mr-2" />
                      Export Processed Data
                    </Button>
                    <Button 
                      variant="outline"
                      onClick={() => setActiveView('sql')}
                    >
                      Query with SQL
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}

          {processingStatus === 'error' && (
            <div className="space-y-4">
              <div className="flex items-center gap-2 text-red-600">
                <AlertCircle className="w-5 h-5" />
                <span className="font-medium">Processing Failed</span>
              </div>
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-sm text-red-800">{processingErrorMessage}</p>
              </div>
              <Button onClick={handleProcessData} variant="outline">
                <RefreshCw className="w-4 h-4 mr-2" />
                Retry Processing
              </Button>
            </div>
          )}
        </Card>
      </div>
    );
  };
  
  // Main render logic
  const renderMainContent = () => {
    // Handle different views based on activeView
    if (activeView === 'dashboard' && isFeatureEnabled(config, 'dashboard')) {
      return (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-full">
          <div className="lg:col-span-2 overflow-auto">
            <DataExplorationDashboard 
              headers={pipelineState.dataPreview?.headers || []}
              rows={pipelineState.dataPreview?.rows || []}
              dataTypes={pipelineState.dataPreview?.dataTypes}
            />
          </div>
          <div className="h-full">
            <StorageDashboard />
          </div>
        </div>
      );
    }
    
    // Data Grid view
    if (activeView === 'grid') {
      return (
        <div className="max-w-7xl mx-auto p-6">
          <Card className="p-6">
            <h2 className="text-2xl font-bold mb-4">Data Grid</h2>
            {pipelineState.dataPreview ? (
              <div className="overflow-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      {pipelineState.dataPreview.headers.map((header, idx) => (
                        <th key={idx} className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {header}
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {pipelineState.dataPreview.rows.slice(0, 100).map((row, rowIdx) => (
                      <tr key={rowIdx}>
                        {row.map((cell, cellIdx) => (
                          <td key={cellIdx} className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {String(cell)}
                          </td>
                        ))}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <p className="text-gray-500">No data available. Please upload a file first.</p>
            )}
          </Card>
        </div>
      );
    }
    
    // SQL view
    if (activeView === 'sql') {
      return (
        <div className="max-w-7xl mx-auto p-6">
          <Card className="p-6">
            <h2 className="text-2xl font-bold mb-4">SQL Query Editor</h2>
            <div className="space-y-4">
              <textarea
                className="w-full h-32 p-3 border rounded-md font-mono text-sm"
                placeholder="Enter your SQL query here..."
                defaultValue="SELECT * FROM data LIMIT 10;"
              />
              <div className="flex gap-2">
                <Button>
                  <Play className="w-4 h-4 mr-2" />
                  Execute Query
                </Button>
                <Button variant="outline">
                  <Save className="w-4 h-4 mr-2" />
                  Save Query
                </Button>
              </div>
              <div className="mt-6 p-4 bg-gray-50 rounded-md">
                <p className="text-sm text-gray-600">Query results will appear here</p>
              </div>
            </div>
          </Card>
        </div>
      );
    }
    
    // Terminal view
    if (activeView === 'terminal') {
      return (
        <div className="max-w-7xl mx-auto p-6">
          <Card className="p-6 bg-gray-900 text-green-400">
            <h2 className="text-2xl font-bold mb-4 text-white">Terminal</h2>
            <div className="font-mono text-sm">
              <div className="mb-2">$ datacenter v1.0.0</div>
              <div className="mb-2">Ready for commands...</div>
              <div className="flex items-center">
                <span className="mr-2">$</span>
                <input
                  type="text"
                  className="flex-1 bg-transparent border-none outline-none text-green-400"
                  placeholder="Enter command..."
                  autoFocus
                />
              </div>
            </div>
            <div className="mt-4 text-xs text-gray-400">
              Type 'help' for available commands
            </div>
          </Card>
        </div>
      );
    }
    
    // Main pipeline stages (when activeView is 'upload' or not set)
    switch (pipelineState.currentStage) {
      case 'upload':
        return renderUploadSection();
      case 'preview':
        return renderDataPreview();
      case 'process':
        return renderProcessingStage();
      case 'storytelling':
        return isFeatureEnabled(config, 'storytelling') ? (
          <div className="max-w-6xl mx-auto">
            <StorytellingWidget data={pipelineState.dataPreview?.fullData || []} />
          </div>
        ) : renderDataPreview();
      default:
        return renderDataPreview();
    }
  };
  
  // Render navigation (if enabled)
  const renderNavigation = () => {
    if (!isFeatureEnabled(config, 'streamlinedNavigation')) return null;
    
    return (
      <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="flex h-14 items-center px-4">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
            >
              {sidebarCollapsed ? <PanelLeft className="w-4 h-4" /> : <PanelLeftClose className="w-4 h-4" />}
            </Button>
            
            <nav className="flex items-center space-x-1">
              {[
                { id: 'upload', label: 'Upload', icon: Upload },
                { id: 'dashboard', label: 'Dashboard', icon: LayoutGrid, requiresFeature: 'dashboard' },
                { id: 'grid', label: 'Data Grid', icon: Grid },
                { id: 'sql', label: 'SQL', icon: Database },
                { id: 'terminal', label: 'Terminal', icon: Terminal },
              ].map(({ id, label, icon: Icon, requiresFeature }) => {
                if (requiresFeature && !isFeatureEnabled(config, requiresFeature as keyof DataCenterFeatures)) {
                  return null;
                }
                
                return (
                  <Button
                    key={id}
                    variant={activeView === id ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setActiveView(id as any)}
                    className="flex items-center gap-2"
                  >
                    <Icon className="w-4 h-4" />
                    {label}
                  </Button>
                );
              })}
            </nav>
          </div>
          
          <div className="ml-auto flex items-center gap-2">
            {/* Global search */}
            <GlobalSearch />
            
            {/* Quick actions */}
            <QuickActions />
            
            {/* Tab organizer */}
            <TabOrganizer />
            
            {/* Control Panel Toggle */}
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => setShowControlPanel(!showControlPanel)}
              title="Toggle Control Panel"
            >
              <Settings className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>
    );
  };
  
  return (
    <div className={`min-h-screen bg-background ${className || ''}`} onContextMenu={showContextMenu}>
      {/* Navigation Bar */}
      {renderNavigation()}
      
      {/* Breadcrumb */}
      {pipelineState.context.breadcrumbPath.length > 0 && (
        <div className="border-b px-4 py-2">
          <Breadcrumb>
            {pipelineState.context.breadcrumbPath.map((item, index) => (
              <BreadcrumbItem key={index}>
                {item.label}
                {item.dataSummary && (
                  <span className="text-xs text-muted-foreground ml-2">
                    ({item.dataSummary.rows} rows, {item.dataSummary.columns} cols)
                  </span>
                )}
              </BreadcrumbItem>
            ))}
          </Breadcrumb>
        </div>
      )}
      
      {/* Main Grid Layout */}
      <div className="flex h-[calc(100vh-120px)]">
        {/* Left Sidebar - Navigation & Tools */}
        <div className="w-64 bg-muted/30 border-r p-4 overflow-y-auto">
          <div className="space-y-4">
            {/* Quick Actions */}
            <Card className="p-3">
              <h3 className="font-semibold text-sm mb-3 flex items-center gap-2">
                <Zap className="w-4 h-4" />
                Quick Actions
              </h3>
              <QuickActions />
            </Card>
            
            {/* Tab Organizer */}
            <Card className="p-3">
              <h3 className="font-semibold text-sm mb-3 flex items-center gap-2">
                <Layers className="w-4 h-4" />
                Open Tabs
              </h3>
              <TabOrganizer />
            </Card>
            
            {/* Feature Discovery */}
            <Card className="p-3">
              <h3 className="font-semibold text-sm mb-3 flex items-center gap-2">
                <Sparkles className="w-4 h-4" />
                Discover Features
              </h3>
              <FeatureDiscovery />
            </Card>
          </div>
        </div>
        
        {/* Center Content Area */}
        <div className="flex-1 flex flex-col">
          {/* Top Toolbar */}
          <div className="h-12 border-b flex items-center px-4 gap-2 bg-background">
            <GlobalSearch />
            <div className="ml-auto flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowMetadataSidebar(!showMetadataSidebar)}
                title="Toggle File Metadata"
              >
                <FileText className="w-4 h-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowControlPanel(!showControlPanel)}
                title="Settings"
              >
                <Settings className="w-4 h-4" />
              </Button>
            </div>
          </div>
          
          {/* Main Content Grid */}
          <div className="flex-1 p-6 overflow-y-auto">
            <AnimatePresence mode="wait">
              <motion.div
                key={`${activeView}-${pipelineState.currentStage}`}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.2 }}
              >
                {/* 2-Column Grid Layout for Main Content */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Main Pipeline View */}
                  <div className="lg:col-span-2">
                    {renderMainContent()}
                  </div>
                  
                  {/* Widget Grid */}
                  {activeView === 'dashboard' && (
                    <>
                      <Card className="p-4">
                        <h3 className="font-semibold mb-3">Data Flow Visualization</h3>
                        <DataFlowVisualization />
                      </Card>
                      
                      <Card className="p-4">
                        <h3 className="font-semibold mb-3">Metrics Heatmap</h3>
                        <MetricsHeatmap />
                      </Card>
                      
                      <Card className="p-4">
                        <h3 className="font-semibold mb-3">Data Quality</h3>
                        <DataQualityGauge />
                      </Card>
                      
                      <Card className="p-4">
                        <h3 className="font-semibold mb-3">Real-Time Terminal</h3>
                        <RealTimeTerminal />
                      </Card>
                    </>
                  )}
                </div>
              </motion.div>
            </AnimatePresence>
          </div>
        </div>
        
        {/* Right Sidebar - Context & Help */}
        <div className={`transition-all duration-300 ${showMetadataSidebar ? 'w-80' : 'w-0'} overflow-hidden`}>
          <div className="w-80 h-full bg-muted/30 border-l p-4 overflow-y-auto">
            <div className="space-y-4">
              {/* File Metadata */}
              {selectedFile && (
                <Card className="p-3">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="font-semibold text-sm">File Metadata</h3>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowMetadataSidebar(false)}
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>
                  <FileMetadataSidebar 
                    file={selectedFile}
                    onClose={() => setShowMetadataSidebar(false)}
                  />
                </Card>
              )}
              
              {/* Contextual Help */}
              <Card className="p-3">
                <h3 className="font-semibold text-sm mb-3 flex items-center gap-2">
                  <HelpCircle className="w-4 h-4" />
                  Help & Tips
                </h3>
                <ContextualHelp />
              </Card>
              
              {/* Storage Dashboard Mini */}
              <Card className="p-3">
                <h3 className="font-semibold text-sm mb-3 flex items-center gap-2">
                  <HardDrive className="w-4 h-4" />
                  Storage Overview
                </h3>
                <div className="max-h-64 overflow-y-auto">
                  <StorageDashboard />
                </div>
              </Card>
            </div>
          </div>
        </div>
      </div>
      
      {/* Context Menu */}
      <ContextMenu
        options={getContextActions()}
        visible={contextMenu.visible}
        x={contextMenu.x}
        y={contextMenu.y}
        onClose={hideContextMenu}
      />
      
      {/* Keyboard shortcuts */}
      <KeyboardShortcuts />
      
      {/* Notification Center - displays alerts and feedback */}
      <NotificationCenter />
      
      {/* Onboarding components - conditionally rendered */}
      {/* OnboardingWelcome requires props from OnboardingProvider context */}
      <ContextualHelp />
      <FeatureDiscovery />
      
      {/* File Metadata Sidebar - shows detailed file information */}
      {showMetadataSidebar && selectedFile && (
        <FileMetadataSidebar 
          file={selectedFile}
          onClose={() => setShowMetadataSidebar(false)}
        />
      )}
      
      {/* Smart Organization - intelligent file organization */}
      {/* SmartOrganization requires files prop - commenting out for now */}
      {/* <SmartOrganization files={[]} onApplySuggestion={() => {}} onDismissSuggestion={() => {}} /> */}
      
      {/* Data Center Control Panel - shows when settings clicked */}
      {showControlPanel && (
        <DataCenterControlPanel onClose={() => setShowControlPanel(false)} />
      )}
      
      {/* Quick actions bar - disabled for now */}
      {/* {isFeatureEnabled(config, 'quickActions') && (
        <QuickActionsBar actions={[]} />
      )} */}
    </div>
  );
};

// Wrapper component with providers
const DataCenterUnified: React.FC<DataCenterUnifiedProps> = (props) => {
  const config = useMemo(() => validateConfig(props.config || {}), [props.config]);
  
  let WrappedComponent = DataCenterUnifiedInner;
  
  // Enable Navigation Provider
  const NavWrappedComponent = WrappedComponent;
  WrappedComponent = (wrappedProps: DataCenterUnifiedProps) => (
    <NavigationProvider>
      <NavWrappedComponent {...wrappedProps} />
    </NavigationProvider>
  );
  
  // Enable Onboarding Provider
  const OnboardingWrappedComponent = WrappedComponent;
  WrappedComponent = (wrappedProps: DataCenterUnifiedProps) => (
    <OnboardingProvider>
      <OnboardingWrappedComponent {...wrappedProps} />
    </OnboardingProvider>
  );
  
  if (isFeatureEnabled(config, 'userFeedback')) {
    const PrevComponent = WrappedComponent;
    WrappedComponent = (wrappedProps: DataCenterUnifiedProps) => (
      <DataCenterFeedbackWrapper>
        <PrevComponent {...wrappedProps} />
      </DataCenterFeedbackWrapper>
    );
  }
  
  // Wrap with accessibility providers
  if (isFeatureEnabled(config, 'accessibilityFeatures')) {
    const PrevComponent = WrappedComponent;
    WrappedComponent = (wrappedProps: DataCenterUnifiedProps) => (
      <HighContrastProvider>
        <ColorblindFriendlyProvider>
          <ScreenReaderSupport>
            <PrevComponent {...wrappedProps} />
          </ScreenReaderSupport>
        </ColorblindFriendlyProvider>
      </HighContrastProvider>
    );
  }
  
  // Always wrap with UnifiedDataCenterProvider and FeedbackProvider
  const FinalComponent = WrappedComponent;
  return (
    <UnifiedDataCenterProvider onDataChange={props.onDataChange}>
      <FeedbackProvider>
        <FinalComponent {...props} />
      </FeedbackProvider>
    </UnifiedDataCenterProvider>
  );
};

export { DataCenterUnified };
export type { DataCenterUnifiedProps };