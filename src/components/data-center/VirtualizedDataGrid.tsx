import React, { useMemo, useCallback, useState, useEffect } from 'react';
import { FixedSizeList as List } from 'react-window';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Search, Filter, Download, Eye, EyeOff } from 'lucide-react';
import { cn } from '@/lib/utils';

interface VirtualizedDataGridProps {
  data: any[];
  columns: string[];
  height?: number;
  itemHeight?: number;
  onRowClick?: (row: any, index: number) => void;
  onExport?: () => void;
  className?: string;
}

interface RowProps {
  index: number;
  style: React.CSSProperties;
  data: {
    items: any[];
    columns: string[];
    visibleColumns: Set<string>;
    onRowClick?: (row: any, index: number) => void;
    searchTerm: string;
  };
}

const Row: React.FC<RowProps> = ({ index, style, data }) => {
  const { items, columns, visibleColumns, onRowClick, searchTerm } = data;
  const row = items[index];
  
  // Highlight search matches
  const highlightText = (text: string, search: string) => {
    if (!search) return text;
    const parts = text.toString().split(new RegExp(`(${search})`, 'gi'));
    return parts.map((part, i) => 
      part.toLowerCase() === search.toLowerCase() ? 
        <mark key={i} className="bg-yellow-200 dark:bg-yellow-800">{part}</mark> : part
    );
  };

  return (
    <div
      style={style}
      className={cn(
        "flex items-center border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer",
        index % 2 === 0 ? "bg-white dark:bg-gray-900" : "bg-gray-50 dark:bg-gray-800"
      )}
      onClick={() => onRowClick?.(row, index)}
    >
      {columns.map((column, colIndex) => {
        if (!visibleColumns.has(column)) return null;
        
        const cellValue = row[column] || '';
        return (
          <div
            key={colIndex}
            className="flex-1 px-4 py-2 text-sm truncate min-w-0"
            title={cellValue.toString()}
          >
            {highlightText(cellValue.toString(), searchTerm)}
          </div>
        );
      })}
    </div>
  );
};

const HeaderRow: React.FC<{
  columns: string[];
  visibleColumns: Set<string>;
  onToggleColumn: (column: string) => void;
}> = ({ columns, visibleColumns, onToggleColumn }) => {
  return (
    <div className="flex items-center border-b-2 border-gray-300 dark:border-gray-600 bg-gray-100 dark:bg-gray-800 font-semibold">
      {columns.map((column, index) => {
        if (!visibleColumns.has(column)) return null;
        
        return (
          <div
            key={index}
            className="flex-1 px-4 py-3 text-sm font-medium text-gray-900 dark:text-gray-100 truncate min-w-0 flex items-center justify-between group"
          >
            <span title={column}>{column}</span>
            <Button
              variant="ghost"
              size="sm"
              className="opacity-0 group-hover:opacity-100 transition-opacity h-6 w-6 p-0"
              onClick={(e) => {
                e.stopPropagation();
                onToggleColumn(column);
              }}
            >
              <EyeOff className="h-3 w-3" />
            </Button>
          </div>
        );
      })}
    </div>
  );
};

export const VirtualizedDataGrid: React.FC<VirtualizedDataGridProps> = ({
  data,
  columns,
  height = 600,
  itemHeight = 40,
  onRowClick,
  onExport,
  className
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [visibleColumns, setVisibleColumns] = useState<Set<string>>(new Set(columns));
  const [showColumnFilter, setShowColumnFilter] = useState(false);

  // Filter data based on search term
  const filteredData = useMemo(() => {
    if (!searchTerm) return data;
    
    return data.filter(row => 
      Object.values(row).some(value => 
        value?.toString().toLowerCase().includes(searchTerm.toLowerCase())
      )
    );
  }, [data, searchTerm]);

  const toggleColumn = useCallback((column: string) => {
    setVisibleColumns(prev => {
      const newSet = new Set(prev);
      if (newSet.has(column)) {
        // Don't allow hiding all columns
        if (newSet.size > 1) {
          newSet.delete(column);
        }
      } else {
        newSet.add(column);
      }
      return newSet;
    });
  }, []);

  const showAllColumns = useCallback(() => {
    setVisibleColumns(new Set(columns));
  }, [columns]);

  const itemData = useMemo(() => ({
    items: filteredData,
    columns,
    visibleColumns,
    onRowClick,
    searchTerm
  }), [filteredData, columns, visibleColumns, onRowClick, searchTerm]);

  // Reset visible columns when columns change
  useEffect(() => {
    setVisibleColumns(new Set(columns));
  }, [columns]);

  if (!data.length) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center h-64">
          <p className="text-gray-500 dark:text-gray-400">No data to display</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            Data Grid
            <Badge variant="secondary">
              {filteredData.length.toLocaleString()} rows
            </Badge>
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowColumnFilter(!showColumnFilter)}
            >
              <Filter className="h-4 w-4 mr-1" />
              Columns ({visibleColumns.size}/{columns.length})
            </Button>
            {onExport && (
              <Button variant="outline" size="sm" onClick={onExport}>
                <Download className="h-4 w-4 mr-1" />
                Export
              </Button>
            )}
          </div>
        </div>
        
        <div className="flex items-center gap-4">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search data..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          
          {showColumnFilter && (
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={showAllColumns}
              >
                Show All
              </Button>
            </div>
          )}
        </div>

        {showColumnFilter && (
          <div className="flex flex-wrap gap-2 pt-2 border-t">
            {columns.map(column => (
              <Button
                key={column}
                variant={visibleColumns.has(column) ? "default" : "outline"}
                size="sm"
                onClick={() => toggleColumn(column)}
                className="h-8"
              >
                {visibleColumns.has(column) ? (
                  <Eye className="h-3 w-3 mr-1" />
                ) : (
                  <EyeOff className="h-3 w-3 mr-1" />
                )}
                {column}
              </Button>
            ))}
          </div>
        )}
      </CardHeader>
      
      <CardContent className="p-0">
        <div className="border rounded-lg overflow-hidden">
          <HeaderRow
            columns={columns}
            visibleColumns={visibleColumns}
            onToggleColumn={toggleColumn}
          />
          
          <List
            height={height}
            width="100%"
            itemCount={filteredData.length}
            itemSize={itemHeight}
            itemData={itemData}
            className="scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600"
          >
            {Row}
          </List>
        </div>
        
        {filteredData.length === 0 && searchTerm && (
          <div className="flex items-center justify-center h-32 text-gray-500 dark:text-gray-400">
            No results found for "{searchTerm}"
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default VirtualizedDataGrid;