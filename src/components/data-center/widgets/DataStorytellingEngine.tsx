import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  BookOpen,
  Wand2,
  Download,
  Share2,
  Eye,
  Edit3,
  Save,
  RefreshCw,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  BarChart3,
  PieChart,
  LineChart,
  Lightbulb,
  Target,
  Users,
  Calendar,
  FileText,
  Sparkles,
  Brain,
  MessageSquare,
  Copy,
  ExternalLink,
  Trash2
} from 'lucide-react';
// Note: Using localStorage directly as browserStorageManager is not available
// import { browserStorageManager } from '../utils/browserStorageManager';

// Types and Interfaces
interface DataPoint {
  label: string;
  value: number;
  category?: string;
  timestamp?: string;
  metadata?: Record<string, any>;
}

interface DataInsight {
  id: string;
  type: 'trend' | 'anomaly' | 'correlation' | 'pattern' | 'outlier' | 'seasonal';
  title: string;
  description: string;
  confidence: number;
  impact: 'high' | 'medium' | 'low';
  data: DataPoint[];
  visualization?: 'line' | 'bar' | 'pie' | 'scatter';
  timestamp: string;
}

interface StoryTemplate {
  id: string;
  name: string;
  description: string;
  structure: string[];
  targetAudience: 'executive' | 'technical' | 'general' | 'stakeholder';
  tone: 'formal' | 'casual' | 'persuasive' | 'analytical';
  length: 'brief' | 'detailed' | 'comprehensive';
}

interface GeneratedStory {
  id: string;
  title: string;
  content: string;
  insights: DataInsight[];
  template: StoryTemplate;
  metadata: {
    generatedAt: string;
    wordCount: number;
    readingTime: number;
    keyTakeaways: string[];
    recommendations: string[];
  };
  visualizations: {
    type: string;
    config: any;
    data: DataPoint[];
  }[];
}

interface StorytellingEngineProps {
  data?: DataPoint[];
  insights?: DataInsight[];
  onStoryGenerated?: (story: GeneratedStory) => void;
  className?: string;
}

// Story Templates
const STORY_TEMPLATES: StoryTemplate[] = [
  {
    id: 'executive-summary',
    name: 'Executive Summary',
    description: 'High-level overview for leadership',
    structure: ['Executive Summary', 'Key Findings', 'Strategic Implications', 'Recommendations'],
    targetAudience: 'executive',
    tone: 'formal',
    length: 'brief'
  },
  {
    id: 'analytical-deep-dive',
    name: 'Analytical Deep Dive',
    description: 'Detailed technical analysis',
    structure: ['Introduction', 'Methodology', 'Findings', 'Analysis', 'Conclusions'],
    targetAudience: 'technical',
    tone: 'analytical',
    length: 'comprehensive'
  },
  {
    id: 'stakeholder-update',
    name: 'Stakeholder Update',
    description: 'Progress and performance update',
    structure: ['Current Status', 'Progress Highlights', 'Challenges', 'Next Steps'],
    targetAudience: 'stakeholder',
    tone: 'formal',
    length: 'detailed'
  },
  {
    id: 'trend-analysis',
    name: 'Trend Analysis',
    description: 'Focus on patterns and trends',
    structure: ['Trend Overview', 'Historical Context', 'Current Patterns', 'Future Projections'],
    targetAudience: 'general',
    tone: 'analytical',
    length: 'detailed'
  },
  {
    id: 'performance-report',
    name: 'Performance Report',
    description: 'KPI and metrics focused narrative',
    structure: ['Performance Overview', 'Key Metrics', 'Variance Analysis', 'Action Items'],
    targetAudience: 'stakeholder',
    tone: 'formal',
    length: 'detailed'
  }
];

// Data Storytelling Engine Manager
class DataStorytellingManager {
  private static instance: DataStorytellingManager;
  private stories: GeneratedStory[] = [];
  private templates: StoryTemplate[] = STORY_TEMPLATES;

  static getInstance(): DataStorytellingManager {
    if (!DataStorytellingManager.instance) {
      DataStorytellingManager.instance = new DataStorytellingManager();
    }
    return DataStorytellingManager.instance;
  }

  // Generate insights from data
  generateInsights(data: DataPoint[]): DataInsight[] {
    const insights: DataInsight[] = [];
    
    if (data.length === 0) return insights;

    // Trend analysis
    const trendInsight = this.analyzeTrend(data);
    if (trendInsight) insights.push(trendInsight);

    // Anomaly detection
    const anomalies = this.detectAnomalies(data);
    insights.push(...anomalies);

    // Pattern recognition
    const patterns = this.recognizePatterns(data);
    insights.push(...patterns);

    return insights;
  }

  private analyzeTrend(data: DataPoint[]): DataInsight | null {
    if (data.length < 3) return null;

    const values = data.map(d => d.value);
    const trend = this.calculateTrend(values);
    
    if (Math.abs(trend) < 0.1) return null;

    return {
      id: `trend-${Date.now()}`,
      type: 'trend',
      title: trend > 0 ? 'Positive Trend Detected' : 'Negative Trend Detected',
      description: `Data shows a ${trend > 0 ? 'upward' : 'downward'} trend with ${Math.abs(trend * 100).toFixed(1)}% change rate.`,
      confidence: Math.min(Math.abs(trend) * 100, 95),
      impact: Math.abs(trend) > 0.3 ? 'high' : Math.abs(trend) > 0.15 ? 'medium' : 'low',
      data,
      visualization: 'line',
      timestamp: new Date().toISOString()
    };
  }

  private detectAnomalies(data: DataPoint[]): DataInsight[] {
    const insights: DataInsight[] = [];
    const values = data.map(d => d.value);
    const mean = values.reduce((a, b) => a + b, 0) / values.length;
    const stdDev = Math.sqrt(values.reduce((sq, n) => sq + Math.pow(n - mean, 2), 0) / values.length);
    
    data.forEach((point, index) => {
      const zScore = Math.abs((point.value - mean) / stdDev);
      if (zScore > 2) {
        insights.push({
          id: `anomaly-${Date.now()}-${index}`,
          type: 'anomaly',
          title: 'Anomaly Detected',
          description: `${point.label} shows unusual value (${point.value}) that deviates significantly from the norm.`,
          confidence: Math.min(zScore * 30, 95),
          impact: zScore > 3 ? 'high' : 'medium',
          data: [point],
          visualization: 'scatter',
          timestamp: new Date().toISOString()
        });
      }
    });

    return insights;
  }

  private recognizePatterns(data: DataPoint[]): DataInsight[] {
    const insights: DataInsight[] = [];
    
    // Seasonal pattern detection (simplified)
    if (data.length >= 12) {
      const seasonalVariation = this.detectSeasonality(data);
      if (seasonalVariation > 0.2) {
        insights.push({
          id: `seasonal-${Date.now()}`,
          type: 'seasonal',
          title: 'Seasonal Pattern Identified',
          description: `Data exhibits seasonal variation with ${(seasonalVariation * 100).toFixed(1)}% fluctuation.`,
          confidence: Math.min(seasonalVariation * 200, 90),
          impact: seasonalVariation > 0.4 ? 'high' : 'medium',
          data,
          visualization: 'line',
          timestamp: new Date().toISOString()
        });
      }
    }

    return insights;
  }

  private calculateTrend(values: number[]): number {
    const n = values.length;
    const x = Array.from({ length: n }, (_, i) => i);
    const sumX = x.reduce((a, b) => a + b, 0);
    const sumY = values.reduce((a, b) => a + b, 0);
    const sumXY = x.reduce((sum, xi, i) => sum + xi * values[i], 0);
    const sumXX = x.reduce((sum, xi) => sum + xi * xi, 0);
    
    return (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
  }

  private detectSeasonality(data: DataPoint[]): number {
    const values = data.map(d => d.value);
    const mean = values.reduce((a, b) => a + b, 0) / values.length;
    const deviations = values.map(v => Math.abs(v - mean));
    return deviations.reduce((a, b) => a + b, 0) / (values.length * mean);
  }

  // Generate narrative story
  generateStory(insights: DataInsight[], template: StoryTemplate, customPrompt?: string): GeneratedStory {
    const story: GeneratedStory = {
      id: `story-${Date.now()}`,
      title: this.generateTitle(insights, template),
      content: this.generateContent(insights, template, customPrompt),
      insights,
      template,
      metadata: {
        generatedAt: new Date().toISOString(),
        wordCount: 0,
        readingTime: 0,
        keyTakeaways: this.extractKeyTakeaways(insights),
        recommendations: this.generateRecommendations(insights)
      },
      visualizations: this.generateVisualizations(insights)
    };

    // Calculate metadata
    story.metadata.wordCount = story.content.split(' ').length;
    story.metadata.readingTime = Math.ceil(story.metadata.wordCount / 200);

    this.stories.push(story);
    this.saveStories();

    return story;
  }

  private generateTitle(insights: DataInsight[], template: StoryTemplate): string {
    const highImpactInsights = insights.filter(i => i.impact === 'high');
    
    if (highImpactInsights.length > 0) {
      const insight = highImpactInsights[0];
      switch (template.targetAudience) {
        case 'executive':
          return `Executive Brief: ${insight.title} - Strategic Implications`;
        case 'technical':
          return `Technical Analysis: ${insight.title} and Data Patterns`;
        case 'stakeholder':
          return `Stakeholder Update: ${insight.title} Impact Assessment`;
        default:
          return `Data Story: ${insight.title} and Key Findings`;
      }
    }

    return `${template.name}: Data Analysis Report`;
  }

  private generateContent(insights: DataInsight[], template: StoryTemplate, customPrompt?: string): string {
    let content = '';
    
    template.structure.forEach((section, index) => {
      content += `## ${section}\n\n`;
      content += this.generateSectionContent(section, insights, template, customPrompt);
      content += '\n\n';
    });

    return content;
  }

  private generateSectionContent(section: string, insights: DataInsight[], template: StoryTemplate, customPrompt?: string): string {
    const highImpactInsights = insights.filter(i => i.impact === 'high');
    const mediumImpactInsights = insights.filter(i => i.impact === 'medium');
    
    switch (section.toLowerCase()) {
      case 'executive summary':
      case 'introduction':
        return this.generateIntroduction(insights, template);
      
      case 'key findings':
      case 'findings':
        return this.generateFindings(insights);
      
      case 'analysis':
      case 'methodology':
        return this.generateAnalysis(insights, template);
      
      case 'recommendations':
      case 'action items':
      case 'next steps':
        return this.generateActionItems(insights);
      
      case 'strategic implications':
      case 'conclusions':
        return this.generateConclusions(insights, template);
      
      default:
        return this.generateGenericSection(section, insights, template);
    }
  }

  private generateIntroduction(insights: DataInsight[], template: StoryTemplate): string {
    const totalInsights = insights.length;
    const highImpactCount = insights.filter(i => i.impact === 'high').length;
    
    let intro = `This ${template.name.toLowerCase()} presents an analysis of ${totalInsights} key data insights`;
    
    if (highImpactCount > 0) {
      intro += `, with ${highImpactCount} high-impact findings that require immediate attention`;
    }
    
    intro += '. The analysis reveals significant patterns and trends that provide valuable insights for decision-making.';
    
    return intro;
  }

  private generateFindings(insights: DataInsight[]): string {
    let findings = '';
    
    insights.forEach((insight, index) => {
      findings += `**${index + 1}. ${insight.title}**\n`;
      findings += `${insight.description} (Confidence: ${insight.confidence.toFixed(1)}%)\n\n`;
    });
    
    return findings;
  }

  private generateAnalysis(insights: DataInsight[], template: StoryTemplate): string {
    const trendInsights = insights.filter(i => i.type === 'trend');
    const anomalies = insights.filter(i => i.type === 'anomaly');
    const patterns = insights.filter(i => i.type === 'pattern' || i.type === 'seasonal');
    
    let analysis = '';
    
    if (trendInsights.length > 0) {
      analysis += '**Trend Analysis:**\n';
      analysis += `The data reveals ${trendInsights.length} significant trend(s) that indicate directional changes in performance metrics.\n\n`;
    }
    
    if (anomalies.length > 0) {
      analysis += '**Anomaly Detection:**\n';
      analysis += `${anomalies.length} anomalous data point(s) were identified, suggesting unusual events or data quality issues that warrant investigation.\n\n`;
    }
    
    if (patterns.length > 0) {
      analysis += '**Pattern Recognition:**\n';
      analysis += `Recurring patterns and seasonal variations were detected, providing insights into cyclical behaviors and predictable fluctuations.\n\n`;
    }
    
    return analysis;
  }

  private generateActionItems(insights: DataInsight[]): string {
    const recommendations = this.generateRecommendations(insights);
    let actionItems = '';
    
    recommendations.forEach((rec, index) => {
      actionItems += `${index + 1}. ${rec}\n`;
    });
    
    return actionItems;
  }

  private generateConclusions(insights: DataInsight[], template: StoryTemplate): string {
    const highImpactInsights = insights.filter(i => i.impact === 'high');
    
    let conclusions = 'Based on the comprehensive data analysis, several key conclusions emerge:\n\n';
    
    if (highImpactInsights.length > 0) {
      conclusions += `• ${highImpactInsights.length} high-impact insight(s) require immediate strategic attention\n`;
    }
    
    conclusions += `• The overall data quality and completeness support confident decision-making\n`;
    conclusions += `• Identified patterns provide a foundation for predictive modeling and forecasting\n`;
    
    if (template.targetAudience === 'executive') {
      conclusions += `\nThese findings present both opportunities and challenges that should be incorporated into strategic planning and resource allocation decisions.`;
    }
    
    return conclusions;
  }

  private generateGenericSection(section: string, insights: DataInsight[], template: StoryTemplate): string {
    return `This section covers ${section.toLowerCase()} based on the analysis of ${insights.length} data insights. The findings provide valuable context for understanding the current state and identifying opportunities for improvement.`;
  }

  private extractKeyTakeaways(insights: DataInsight[]): string[] {
    return insights
      .filter(i => i.impact === 'high' || i.confidence > 80)
      .slice(0, 5)
      .map(i => i.title);
  }

  private generateRecommendations(insights: DataInsight[]): string[] {
    const recommendations: string[] = [];
    
    insights.forEach(insight => {
      switch (insight.type) {
        case 'trend':
          if (insight.title.includes('Negative')) {
            recommendations.push('Investigate root causes of declining trend and implement corrective measures');
          } else {
            recommendations.push('Capitalize on positive trend by scaling successful initiatives');
          }
          break;
        case 'anomaly':
          recommendations.push('Investigate anomalous data points to identify potential issues or opportunities');
          break;
        case 'seasonal':
          recommendations.push('Develop seasonal strategies to optimize performance during peak and low periods');
          break;
        case 'pattern':
          recommendations.push('Leverage identified patterns for predictive modeling and resource planning');
          break;
      }
    });
    
    return [...new Set(recommendations)]; // Remove duplicates
  }

  private generateVisualizations(insights: DataInsight[]): GeneratedStory['visualizations'] {
    return insights.map(insight => ({
      type: insight.visualization || 'bar',
      config: {
        title: insight.title,
        description: insight.description,
        confidence: insight.confidence
      },
      data: insight.data
    }));
  }

  // Story management
  getStories(): GeneratedStory[] {
    return [...this.stories];
  }

  getStory(id: string): GeneratedStory | undefined {
    return this.stories.find(s => s.id === id);
  }

  deleteStory(id: string): void {
    this.stories = this.stories.filter(s => s.id !== id);
    this.saveStories();
  }

  updateStory(id: string, updates: Partial<GeneratedStory>): void {
    const index = this.stories.findIndex(s => s.id === id);
    if (index !== -1) {
      this.stories[index] = { ...this.stories[index], ...updates };
      this.saveStories();
    }
  }

  // Persistence
  private saveStories(): void {
    try {
      localStorage.setItem('data-storytelling-stories', JSON.stringify(this.stories));
    } catch (error) {
      console.error('Failed to save stories:', error);
    }
  }

  loadStories(): void {
    try {
      const saved = localStorage.getItem('data-storytelling-stories');
      if (saved) {
        this.stories = JSON.parse(saved);
      }
    } catch (error) {
      console.error('Failed to load stories:', error);
    }
  }

  // Export functionality
  exportStory(story: GeneratedStory, format: 'markdown' | 'html' | 'json' = 'markdown'): string {
    switch (format) {
      case 'html':
        return this.exportToHTML(story);
      case 'json':
        return JSON.stringify(story, null, 2);
      default:
        return this.exportToMarkdown(story);
    }
  }

  private exportToMarkdown(story: GeneratedStory): string {
    let markdown = `# ${story.title}\n\n`;
    markdown += `*Generated on ${new Date(story.metadata.generatedAt).toLocaleDateString()}*\n\n`;
    markdown += `**Reading Time:** ${story.metadata.readingTime} minutes\n\n`;
    markdown += `---\n\n`;
    markdown += story.content;
    markdown += `\n\n---\n\n`;
    markdown += `## Key Takeaways\n\n`;
    story.metadata.keyTakeaways.forEach(takeaway => {
      markdown += `• ${takeaway}\n`;
    });
    return markdown;
  }

  private exportToHTML(story: GeneratedStory): string {
    const markdown = this.exportToMarkdown(story);
    // Simple markdown to HTML conversion (would use a proper library in production)
    return markdown
      .replace(/^# (.*$)/gm, '<h1>$1</h1>')
      .replace(/^## (.*$)/gm, '<h2>$1</h2>')
      .replace(/^### (.*$)/gm, '<h3>$1</h3>')
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/^• (.*$)/gm, '<li>$1</li>')
      .replace(/\n/g, '<br>');
  }
}

// Main Component
export const DataStorytellingEngine: React.FC<StorytellingEngineProps> = ({
  data = [],
  insights: providedInsights = [],
  onStoryGenerated,
  className = ''
}) => {
  const [manager] = useState(() => DataStorytellingManager.getInstance());
  const [selectedTemplate, setSelectedTemplate] = useState<StoryTemplate>(STORY_TEMPLATES[0]);
  const [customPrompt, setCustomPrompt] = useState('');
  const [generatedStories, setGeneratedStories] = useState<GeneratedStory[]>([]);
  const [selectedStory, setSelectedStory] = useState<GeneratedStory | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [editingStory, setEditingStory] = useState<string | null>(null);
  const [editContent, setEditContent] = useState('');

  // Generate insights from data
  const insights = useMemo(() => {
    if (providedInsights.length > 0) return providedInsights;
    return manager.generateInsights(data);
  }, [data, providedInsights, manager]);

  // Load stories on mount
  useEffect(() => {
    manager.loadStories();
    setGeneratedStories(manager.getStories());
  }, [manager]);

  const handleGenerateStory = useCallback(async () => {
    if (insights.length === 0) return;

    setIsGenerating(true);
    try {
      // Simulate AI processing time
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const story = manager.generateStory(insights, selectedTemplate, customPrompt);
      setGeneratedStories(manager.getStories());
      setSelectedStory(story);
      onStoryGenerated?.(story);
    } catch (error) {
      console.error('Failed to generate story:', error);
    } finally {
      setIsGenerating(false);
    }
  }, [insights, selectedTemplate, customPrompt, manager, onStoryGenerated]);

  const handleEditStory = useCallback((story: GeneratedStory) => {
    setEditingStory(story.id);
    setEditContent(story.content);
  }, []);

  const handleSaveEdit = useCallback(() => {
    if (editingStory) {
      manager.updateStory(editingStory, { content: editContent });
      setGeneratedStories(manager.getStories());
      setEditingStory(null);
      
      // Update selected story if it's the one being edited
      if (selectedStory?.id === editingStory) {
        setSelectedStory({ ...selectedStory, content: editContent });
      }
    }
  }, [editingStory, editContent, manager, selectedStory]);

  const handleExportStory = useCallback((story: GeneratedStory, format: 'markdown' | 'html' | 'json') => {
    const exported = manager.exportStory(story, format);
    const blob = new Blob([exported], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${story.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.${format === 'json' ? 'json' : format === 'html' ? 'html' : 'md'}`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [manager]);

  const handleDeleteStory = useCallback((storyId: string) => {
    manager.deleteStory(storyId);
    setGeneratedStories(manager.getStories());
    if (selectedStory?.id === storyId) {
      setSelectedStory(null);
    }
  }, [manager, selectedStory]);

  return (
    <div className={`space-y-6 ${className}`}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookOpen className="w-5 h-5" />
            Data Storytelling Engine
          </CardTitle>
          <CardDescription>
            Transform your data insights into compelling narratives with AI-powered storytelling
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value="generate" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="generate">Generate Story</TabsTrigger>
              <TabsTrigger value="stories">My Stories</TabsTrigger>
              <TabsTrigger value="insights">Data Insights</TabsTrigger>
            </TabsList>

            <TabsContent value="generate" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="template">Story Template</Label>
                    <Select
                      value={selectedTemplate.id}
                      onValueChange={(value) => {
                        const template = STORY_TEMPLATES.find(t => t.id === value);
                        if (template) setSelectedTemplate(template);
                      }}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {STORY_TEMPLATES.map(template => (
                          <SelectItem key={template.id} value={template.id}>
                            {template.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="prompt">Custom Instructions (Optional)</Label>
                    <Textarea
                      id="prompt"
                      placeholder="Add specific requirements or focus areas for your story..."
                      value={customPrompt}
                      onChange={(e) => setCustomPrompt(e.target.value)}
                      rows={3}
                    />
                  </div>

                  <Button
                    onClick={handleGenerateStory}
                    disabled={isGenerating || insights.length === 0}
                    className="w-full"
                  >
                    {isGenerating ? (
                      <>
                        <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                        Generating Story...
                      </>
                    ) : (
                      <>
                        <Wand2 className="w-4 h-4 mr-2" />
                        Generate Story
                      </>
                    )}
                  </Button>
                </div>

                <div className="space-y-4">
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <h4 className="font-medium mb-2 flex items-center gap-2">
                      <Target className="w-4 h-4" />
                      Template Details
                    </h4>
                    <p className="text-sm text-gray-600 mb-2">{selectedTemplate.description}</p>
                    <div className="flex flex-wrap gap-2 mb-3">
                      <Badge variant="outline">{selectedTemplate.targetAudience}</Badge>
                      <Badge variant="outline">{selectedTemplate.tone}</Badge>
                      <Badge variant="outline">{selectedTemplate.length}</Badge>
                    </div>
                    <div>
                      <p className="text-xs font-medium text-gray-500 mb-1">Structure:</p>
                      <ul className="text-xs text-gray-600 space-y-1">
                        {selectedTemplate.structure.map((section, index) => (
                          <li key={index}>• {section}</li>
                        ))}
                      </ul>
                    </div>
                  </div>

                  <div className="p-4 bg-blue-50 rounded-lg">
                    <h4 className="font-medium mb-2 flex items-center gap-2">
                      <Brain className="w-4 h-4" />
                      Available Insights
                    </h4>
                    <p className="text-sm text-gray-600">
                      {insights.length} insights ready for storytelling
                    </p>
                    <div className="flex flex-wrap gap-1 mt-2">
                      {insights.slice(0, 3).map(insight => (
                        <Badge key={insight.id} variant="secondary" className="text-xs">
                          {insight.type}
                        </Badge>
                      ))}
                      {insights.length > 3 && (
                        <Badge variant="secondary" className="text-xs">
                          +{insights.length - 3} more
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="stories" className="space-y-4">
              {generatedStories.length === 0 ? (
                <div className="text-center py-12">
                  <FileText className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Stories Yet</h3>
                  <p className="text-gray-600 mb-4">Generate your first data story to get started</p>
                  <Button onClick={() => {
                    const generateTab = document.querySelector('[value="generate"]') as HTMLElement;
                    generateTab?.click();
                  }}>
                    <Wand2 className="w-4 h-4 mr-2" />
                    Create Story
                  </Button>
                </div>
              ) : (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <h3 className="font-medium">Generated Stories</h3>
                    {generatedStories.map(story => (
                      <Card key={story.id} className={`cursor-pointer transition-colors ${
                        selectedStory?.id === story.id ? 'ring-2 ring-blue-500' : 'hover:bg-gray-50'
                      }`} onClick={() => setSelectedStory(story)}>
                        <CardContent className="p-4">
                          <div className="flex items-start justify-between mb-2">
                            <h4 className="font-medium text-sm">{story.title}</h4>
                            <div className="flex gap-1">
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleEditStory(story);
                                }}
                              >
                                <Edit3 className="w-3 h-3" />
                              </Button>
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleDeleteStory(story.id);
                                }}
                              >
                                <Trash2 className="w-3 h-3" />
                              </Button>
                            </div>
                          </div>
                          <div className="flex items-center gap-4 text-xs text-gray-500">
                            <span>{story.metadata.wordCount} words</span>
                            <span>{story.metadata.readingTime} min read</span>
                            <span>{story.insights.length} insights</span>
                          </div>
                          <div className="flex flex-wrap gap-1 mt-2">
                            <Badge variant="outline" className="text-xs">
                              {story.template.name}
                            </Badge>
                            <Badge variant="outline" className="text-xs">
                              {story.template.targetAudience}
                            </Badge>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>

                  {selectedStory && (
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <h3 className="font-medium">Story Preview</h3>
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleExportStory(selectedStory, 'markdown')}
                          >
                            <Download className="w-3 h-3 mr-1" />
                            MD
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleExportStory(selectedStory, 'html')}
                          >
                            <Download className="w-3 h-3 mr-1" />
                            HTML
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => {
                              navigator.clipboard.writeText(selectedStory.content);
                            }}
                          >
                            <Copy className="w-3 h-3 mr-1" />
                            Copy
                          </Button>
                        </div>
                      </div>

                      <Card>
                        <CardContent className="p-6">
                          {editingStory === selectedStory.id ? (
                            <div className="space-y-4">
                              <Textarea
                                value={editContent}
                                onChange={(e) => setEditContent(e.target.value)}
                                rows={20}
                                className="font-mono text-sm"
                              />
                              <div className="flex gap-2">
                                <Button onClick={handleSaveEdit}>
                                  <Save className="w-4 h-4 mr-2" />
                                  Save Changes
                                </Button>
                                <Button variant="outline" onClick={() => setEditingStory(null)}>
                                  Cancel
                                </Button>
                              </div>
                            </div>
                          ) : (
                            <div className="prose prose-sm max-w-none">
                              <h1>{selectedStory.title}</h1>
                              <div className="text-sm text-gray-500 mb-4">
                                Generated on {new Date(selectedStory.metadata.generatedAt).toLocaleDateString()}
                              </div>
                              <div className="whitespace-pre-wrap">{selectedStory.content}</div>
                            </div>
                          )}
                        </CardContent>
                      </Card>

                      {selectedStory.metadata.keyTakeaways.length > 0 && (
                        <Card>
                          <CardContent className="p-4">
                            <h4 className="font-medium mb-2 flex items-center gap-2">
                              <Lightbulb className="w-4 h-4" />
                              Key Takeaways
                            </h4>
                            <ul className="space-y-1 text-sm">
                              {selectedStory.metadata.keyTakeaways.map((takeaway, index) => (
                                <li key={index} className="flex items-start gap-2">
                                  <CheckCircle className="w-3 h-3 mt-1 text-green-500 flex-shrink-0" />
                                  {takeaway}
                                </li>
                              ))}
                            </ul>
                          </CardContent>
                        </Card>
                      )}
                    </div>
                  )}
                </div>
              )}
            </TabsContent>

            <TabsContent value="insights" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {insights.map(insight => (
                  <Card key={insight.id}>
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center gap-2">
                          {insight.type === 'trend' && <TrendingUp className="w-4 h-4 text-blue-500" />}
                          {insight.type === 'anomaly' && <AlertTriangle className="w-4 h-4 text-red-500" />}
                          {insight.type === 'pattern' && <BarChart3 className="w-4 h-4 text-green-500" />}
                          {insight.type === 'seasonal' && <Calendar className="w-4 h-4 text-purple-500" />}
                          <Badge variant={insight.impact === 'high' ? 'destructive' : insight.impact === 'medium' ? 'default' : 'secondary'}>
                            {insight.impact}
                          </Badge>
                        </div>
                        <span className="text-xs text-gray-500">
                          {insight.confidence.toFixed(0)}%
                        </span>
                      </div>
                      <h4 className="font-medium text-sm mb-1">{insight.title}</h4>
                      <p className="text-xs text-gray-600">{insight.description}</p>
                      <div className="mt-2 text-xs text-gray-500">
                        {insight.data.length} data points
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {insights.length === 0 && (
                <div className="text-center py-12">
                  <Brain className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Insights Available</h3>
                  <p className="text-gray-600">Provide data to generate insights for storytelling</p>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default DataStorytellingEngine;
export { DataStorytellingManager, type DataInsight, type StoryTemplate, type GeneratedStory };