import React, { useCallback, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/components/ui/use-toast';
import {
  Download,
  FileImage,
  FileText,
  Database,
  FileSpreadsheet,
  Printer,
  Share2,
  Copy,
  Mail,
  ExternalLink,
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react';

export interface ExportData {
  chartData: any[];
  chartConfig: any;
  metadata: {
    title: string;
    description?: string;
    timestamp: string;
    dataSource: string;
    totalRecords: number;
  };
}

export interface ExportOptions {
  format: 'png' | 'svg' | 'pdf' | 'csv' | 'json' | 'xlsx';
  quality?: 'low' | 'medium' | 'high';
  includeMetadata?: boolean;
  includeRawData?: boolean;
  customFilename?: string;
}

export interface ExportJob {
  id: string;
  format: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  filename: string;
  downloadUrl?: string;
  error?: string;
  createdAt: Date;
}

export interface ChartExportManagerProps {
  data: ExportData;
  onExport?: (options: ExportOptions) => Promise<string>; // Returns download URL
  className?: string;
}

const EXPORT_FORMATS = {
  png: { label: 'PNG Image', icon: FileImage, description: 'High-quality raster image' },
  svg: { label: 'SVG Vector', icon: FileImage, description: 'Scalable vector graphics' },
  pdf: { label: 'PDF Document', icon: FileText, description: 'Portable document format' },
  csv: { label: 'CSV Data', icon: FileSpreadsheet, description: 'Comma-separated values' },
  json: { label: 'JSON Data', icon: Database, description: 'JavaScript object notation' },
  xlsx: { label: 'Excel File', icon: FileSpreadsheet, description: 'Microsoft Excel format' }
};

const QUALITY_OPTIONS = {
  low: { label: 'Low (Fast)', dpi: 72 },
  medium: { label: 'Medium', dpi: 150 },
  high: { label: 'High (Best)', dpi: 300 }
};

export const ChartExportManager: React.FC<ChartExportManagerProps> = ({
  data,
  onExport,
  className = ''
}) => {
  const [exportJobs, setExportJobs] = useState<ExportJob[]>([]);
  const [isExporting, setIsExporting] = useState(false);
  const { toast } = useToast();

  const generateFilename = useCallback((format: string, title: string) => {
    const timestamp = new Date().toISOString().slice(0, 19).replace(/[:.]/g, '-');
    const sanitizedTitle = title.replace(/[^a-zA-Z0-9]/g, '_').toLowerCase();
    return `${sanitizedTitle}_${timestamp}.${format}`;
  }, []);

  const handleExport = useCallback(async (options: ExportOptions) => {
    if (!onExport) {
      toast({
        title: 'Export not available',
        description: 'Export functionality is not configured.',
        variant: 'destructive'
      });
      return;
    }

    const jobId = `export_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const filename = options.customFilename || generateFilename(options.format, data.metadata.title);
    
    const newJob: ExportJob = {
      id: jobId,
      format: options.format.toUpperCase(),
      status: 'pending',
      progress: 0,
      filename,
      createdAt: new Date()
    };

    setExportJobs(prev => [newJob, ...prev]);
    setIsExporting(true);

    try {
      // Update job status to processing
      setExportJobs(prev => prev.map(job => 
        job.id === jobId ? { ...job, status: 'processing', progress: 25 } : job
      ));

      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setExportJobs(prev => prev.map(job => {
          if (job.id === jobId && job.status === 'processing' && job.progress < 90) {
            return { ...job, progress: job.progress + 10 };
          }
          return job;
        }));
      }, 200);

      const downloadUrl = await onExport(options);
      
      clearInterval(progressInterval);
      
      // Update job as completed
      setExportJobs(prev => prev.map(job => 
        job.id === jobId 
          ? { ...job, status: 'completed', progress: 100, downloadUrl }
          : job
      ));

      toast({
        title: 'Export completed',
        description: `${options.format.toUpperCase()} file is ready for download.`,
      });

      // Auto-download if browser supports it
      if (downloadUrl) {
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }

    } catch (error) {
      setExportJobs(prev => prev.map(job => 
        job.id === jobId 
          ? { 
              ...job, 
              status: 'failed', 
              progress: 0, 
              error: error instanceof Error ? error.message : 'Export failed' 
            }
          : job
      ));

      toast({
        title: 'Export failed',
        description: error instanceof Error ? error.message : 'An error occurred during export.',
        variant: 'destructive'
      });
    } finally {
      setIsExporting(false);
    }
  }, [onExport, data.metadata.title, generateFilename, toast]);

  const handleQuickExport = useCallback((format: ExportOptions['format']) => {
    handleExport({
      format,
      quality: 'medium',
      includeMetadata: true,
      includeRawData: format === 'csv' || format === 'json' || format === 'xlsx'
    });
  }, [handleExport]);

  const handleShare = useCallback(async (job: ExportJob) => {
    if (job.downloadUrl && navigator.share) {
      try {
        await navigator.share({
          title: `Chart Export: ${data.metadata.title}`,
          text: `Exported chart data in ${job.format} format`,
          url: job.downloadUrl
        });
      } catch (error) {
        // Fallback to clipboard
        navigator.clipboard.writeText(job.downloadUrl);
        toast({
          title: 'Link copied',
          description: 'Download link copied to clipboard.'
        });
      }
    } else if (job.downloadUrl) {
      navigator.clipboard.writeText(job.downloadUrl);
      toast({
        title: 'Link copied',
        description: 'Download link copied to clipboard.'
      });
    }
  }, [data.metadata.title, toast]);

  const clearCompletedJobs = useCallback(() => {
    setExportJobs(prev => prev.filter(job => job.status !== 'completed'));
  }, []);

  const retryFailedJob = useCallback((job: ExportJob) => {
    const options: ExportOptions = {
      format: job.format.toLowerCase() as ExportOptions['format'],
      quality: 'medium',
      includeMetadata: true,
      customFilename: job.filename
    };
    handleExport(options);
  }, [handleExport]);

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Quick Export Buttons */}
      <div className="flex flex-wrap gap-2">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="default" disabled={isExporting}>
              <Download className="h-4 w-4 mr-2" />
              Export Chart
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-56">
            <DropdownMenuLabel>Image Formats</DropdownMenuLabel>
            {(['png', 'svg', 'pdf'] as const).map(format => {
              const formatInfo = EXPORT_FORMATS[format];
              const Icon = formatInfo.icon;
              return (
                <DropdownMenuItem 
                  key={format} 
                  onClick={() => handleQuickExport(format)}
                >
                  <Icon className="h-4 w-4 mr-2" />
                  <div>
                    <div className="font-medium">{formatInfo.label}</div>
                    <div className="text-xs text-gray-500">{formatInfo.description}</div>
                  </div>
                </DropdownMenuItem>
              );
            })}
            
            <DropdownMenuSeparator />
            <DropdownMenuLabel>Data Formats</DropdownMenuLabel>
            {(['csv', 'json', 'xlsx'] as const).map(format => {
              const formatInfo = EXPORT_FORMATS[format];
              const Icon = formatInfo.icon;
              return (
                <DropdownMenuItem 
                  key={format} 
                  onClick={() => handleQuickExport(format)}
                >
                  <Icon className="h-4 w-4 mr-2" />
                  <div>
                    <div className="font-medium">{formatInfo.label}</div>
                    <div className="text-xs text-gray-500">{formatInfo.description}</div>
                  </div>
                </DropdownMenuItem>
              );
            })}
          </DropdownMenuContent>
        </DropdownMenu>

        <Button variant="outline" onClick={() => handleQuickExport('png')}>
          <FileImage className="h-4 w-4 mr-2" />
          PNG
        </Button>
        
        <Button variant="outline" onClick={() => handleQuickExport('csv')}>
          <FileSpreadsheet className="h-4 w-4 mr-2" />
          CSV
        </Button>
      </div>

      {/* Export Jobs Status */}
      {exportJobs.length > 0 && (
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Export History</CardTitle>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={clearCompletedJobs}
              className="text-xs"
            >
              Clear Completed
            </Button>
          </CardHeader>
          <CardContent className="space-y-3">
            {exportJobs.slice(0, 5).map(job => (
              <div key={job.id} className="flex items-center justify-between p-2 border rounded">
                <div className="flex items-center space-x-3">
                  <div className="flex items-center space-x-2">
                    {job.status === 'completed' && <CheckCircle className="h-4 w-4 text-green-500" />}
                    {job.status === 'failed' && <AlertCircle className="h-4 w-4 text-red-500" />}
                    {job.status === 'processing' && <Loader2 className="h-4 w-4 animate-spin text-blue-500" />}
                    
                    <div>
                      <div className="text-sm font-medium">{job.filename}</div>
                      <div className="text-xs text-gray-500">
                        {job.format} • {job.createdAt.toLocaleTimeString()}
                      </div>
                    </div>
                  </div>
                  
                  <Badge 
                    variant={job.status === 'completed' ? 'default' : 
                            job.status === 'failed' ? 'destructive' : 'secondary'}
                  >
                    {job.status}
                  </Badge>
                </div>
                
                <div className="flex items-center space-x-2">
                  {job.status === 'processing' && (
                    <div className="w-20">
                      <Progress value={job.progress} className="h-2" />
                    </div>
                  )}
                  
                  {job.status === 'completed' && job.downloadUrl && (
                    <>
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        onClick={() => handleShare(job)}
                      >
                        <Share2 className="h-4 w-4" />
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        onClick={() => window.open(job.downloadUrl, '_blank')}
                      >
                        <ExternalLink className="h-4 w-4" />
                      </Button>
                    </>
                  )}
                  
                  {job.status === 'failed' && (
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      onClick={() => retryFailedJob(job)}
                    >
                      <Download className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      )}

      {/* Data Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm font-medium">Export Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-500">Data Points:</span>
              <span className="ml-2 font-medium">{data.metadata.totalRecords}</span>
            </div>
            <div>
              <span className="text-gray-500">Source:</span>
              <span className="ml-2 font-medium">{data.metadata.dataSource}</span>
            </div>
            <div>
              <span className="text-gray-500">Generated:</span>
              <span className="ml-2 font-medium">
                {new Date(data.metadata.timestamp).toLocaleString()}
              </span>
            </div>
            <div>
              <span className="text-gray-500">Format:</span>
              <span className="ml-2 font-medium">Interactive Chart</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ChartExportManager;