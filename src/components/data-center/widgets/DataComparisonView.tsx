import React, { useState, use<PERSON>emo, useCallback } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Separator } from '@/components/ui/separator';
import {
  ResponsiveContainer,
  LineChart,
  Line,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ReferenceLine
} from 'recharts';
import {
  TrendingUp,
  TrendingDown,
  Minus,
  Plus,
  X,
  BarChart3,
  LineChart as LineChartIcon,
  Eye,
  EyeOff,
  ArrowUpDown,
  Filter,
  Download,
  RefreshCw
} from 'lucide-react';

export interface ComparisonDataset {
  id: string;
  name: string;
  description?: string;
  data: any[];
  color: string;
  metadata: {
    source: string;
    timestamp: string;
    recordCount: number;
    dataType: string;
  };
  visible: boolean;
}

export interface ComparisonMetrics {
  mean: number;
  median: number;
  min: number;
  max: number;
  stdDev: number;
  trend: 'up' | 'down' | 'stable';
  changePercent: number;
}

export interface ComparisonResult {
  dataset1: string;
  dataset2: string;
  correlation: number;
  similarity: number;
  significantDifferences: string[];
  recommendations: string[];
}

export interface DataComparisonViewProps {
  datasets: ComparisonDataset[];
  onAddDataset?: () => void;
  onRemoveDataset?: (id: string) => void;
  onExportComparison?: (format: 'png' | 'csv' | 'json') => void;
  className?: string;
}

const CHART_COLORS = [
  '#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#00ff00',
  '#0088fe', '#00c49f', '#ffbb28', '#ff8042', '#8dd1e1'
];

const COMPARISON_MODES = {
  overlay: 'Overlay View',
  sideBySide: 'Side by Side',
  difference: 'Difference Analysis',
  normalized: 'Normalized Comparison'
};

export const DataComparisonView: React.FC<DataComparisonViewProps> = ({
  datasets,
  onAddDataset,
  onRemoveDataset,
  onExportComparison,
  className = ''
}) => {
  const [comparisonMode, setComparisonMode] = useState<keyof typeof COMPARISON_MODES>('overlay');
  const [chartType, setChartType] = useState<'line' | 'bar'>('line');
  const [selectedMetric, setSelectedMetric] = useState<string>('value');
  const [normalizeData, setNormalizeData] = useState(false);
  const [showStatistics, setShowStatistics] = useState(true);
  const [selectedDatasets, setSelectedDatasets] = useState<string[]>(
    datasets.filter(d => d.visible).map(d => d.id)
  );

  // Calculate metrics for each dataset
  const datasetMetrics = useMemo(() => {
    const metrics: Record<string, ComparisonMetrics> = {};
    
    datasets.forEach(dataset => {
      if (!dataset.visible) return;
      
      const values = dataset.data.map(d => d[selectedMetric] || d.value || 0);
      const sortedValues = [...values].sort((a, b) => a - b);
      
      const mean = values.reduce((a, b) => a + b, 0) / values.length;
      const median = sortedValues[Math.floor(sortedValues.length / 2)];
      const min = Math.min(...values);
      const max = Math.max(...values);
      
      // Calculate standard deviation
      const variance = values.reduce((acc, val) => acc + Math.pow(val - mean, 2), 0) / values.length;
      const stdDev = Math.sqrt(variance);
      
      // Calculate trend
      const firstHalf = values.slice(0, Math.floor(values.length / 2));
      const secondHalf = values.slice(Math.floor(values.length / 2));
      const firstAvg = firstHalf.reduce((a, b) => a + b, 0) / firstHalf.length;
      const secondAvg = secondHalf.reduce((a, b) => a + b, 0) / secondHalf.length;
      const changePercent = ((secondAvg - firstAvg) / firstAvg) * 100;
      
      let trend: 'up' | 'down' | 'stable' = 'stable';
      if (changePercent > 5) trend = 'up';
      else if (changePercent < -5) trend = 'down';
      
      metrics[dataset.id] = {
        mean,
        median,
        min,
        max,
        stdDev,
        trend,
        changePercent
      };
    });
    
    return metrics;
  }, [datasets, selectedMetric]);

  // Prepare data for comparison charts
  const comparisonData = useMemo(() => {
    const visibleDatasets = datasets.filter(d => d.visible && selectedDatasets.includes(d.id));
    if (visibleDatasets.length === 0) return [];
    
    // Find common data points (by index or timestamp)
    const maxLength = Math.max(...visibleDatasets.map(d => d.data.length));
    const result = [];
    
    for (let i = 0; i < maxLength; i++) {
      const dataPoint: any = { index: i };
      
      visibleDatasets.forEach(dataset => {
        const value = dataset.data[i]?.[selectedMetric] || dataset.data[i]?.value || 0;
        
        if (normalizeData && datasetMetrics[dataset.id]) {
          // Normalize to 0-100 scale
          const metrics = datasetMetrics[dataset.id];
          const normalized = ((value - metrics.min) / (metrics.max - metrics.min)) * 100;
          dataPoint[dataset.id] = normalized;
        } else {
          dataPoint[dataset.id] = value;
        }
        
        // Add dataset name for reference
        dataPoint[`${dataset.id}_name`] = dataset.name;
      });
      
      result.push(dataPoint);
    }
    
    return result;
  }, [datasets, selectedDatasets, selectedMetric, normalizeData, datasetMetrics]);

  // Calculate comparison results between datasets
  const comparisonResults = useMemo(() => {
    const visibleDatasets = datasets.filter(d => d.visible && selectedDatasets.includes(d.id));
    const results: ComparisonResult[] = [];
    
    for (let i = 0; i < visibleDatasets.length; i++) {
      for (let j = i + 1; j < visibleDatasets.length; j++) {
        const dataset1 = visibleDatasets[i];
        const dataset2 = visibleDatasets[j];
        
        const values1 = dataset1.data.map(d => d[selectedMetric] || d.value || 0);
        const values2 = dataset2.data.map(d => d[selectedMetric] || d.value || 0);
        
        // Calculate correlation (simplified)
        const minLength = Math.min(values1.length, values2.length);
        const trimmed1 = values1.slice(0, minLength);
        const trimmed2 = values2.slice(0, minLength);
        
        const mean1 = trimmed1.reduce((a, b) => a + b, 0) / trimmed1.length;
        const mean2 = trimmed2.reduce((a, b) => a + b, 0) / trimmed2.length;
        
        let numerator = 0;
        let denominator1 = 0;
        let denominator2 = 0;
        
        for (let k = 0; k < minLength; k++) {
          const diff1 = trimmed1[k] - mean1;
          const diff2 = trimmed2[k] - mean2;
          numerator += diff1 * diff2;
          denominator1 += diff1 * diff1;
          denominator2 += diff2 * diff2;
        }
        
        const correlation = numerator / Math.sqrt(denominator1 * denominator2) || 0;
        
        // Calculate similarity (inverse of normalized difference)
        const avgDiff = trimmed1.reduce((acc, val, idx) => 
          acc + Math.abs(val - trimmed2[idx]), 0
        ) / minLength;
        const maxVal = Math.max(...trimmed1, ...trimmed2);
        const similarity = Math.max(0, 100 - (avgDiff / maxVal) * 100);
        
        const metrics1 = datasetMetrics[dataset1.id];
        const metrics2 = datasetMetrics[dataset2.id];
        
        const significantDifferences = [];
        const recommendations = [];
        
        if (Math.abs(metrics1.mean - metrics2.mean) > metrics1.stdDev) {
          significantDifferences.push('Significant difference in mean values');
        }
        
        if (metrics1.trend !== metrics2.trend) {
          significantDifferences.push('Different trend directions');
          recommendations.push('Investigate factors causing trend differences');
        }
        
        if (correlation > 0.7) {
          recommendations.push('Strong positive correlation - consider consolidating data sources');
        } else if (correlation < -0.7) {
          recommendations.push('Strong negative correlation - investigate inverse relationship');
        }
        
        results.push({
          dataset1: dataset1.name,
          dataset2: dataset2.name,
          correlation: Math.round(correlation * 100) / 100,
          similarity: Math.round(similarity * 100) / 100,
          significantDifferences,
          recommendations
        });
      }
    }
    
    return results;
  }, [datasets, selectedDatasets, selectedMetric, datasetMetrics]);

  const toggleDatasetVisibility = useCallback((datasetId: string) => {
    setSelectedDatasets(prev => 
      prev.includes(datasetId) 
        ? prev.filter(id => id !== datasetId)
        : [...prev, datasetId]
    );
  }, []);

  const renderChart = () => {
    const visibleDatasets = datasets.filter(d => d.visible && selectedDatasets.includes(d.id));
    
    if (comparisonData.length === 0) {
      return (
        <div className="flex items-center justify-center h-64 text-gray-500">
          No data available for comparison
        </div>
      );
    }

    const commonProps = {
      data: comparisonData,
      margin: { top: 20, right: 30, left: 20, bottom: 5 }
    };

    if (chartType === 'line') {
      return (
        <LineChart {...commonProps}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="index" />
          <YAxis />
          <Tooltip 
            formatter={(value: any, name: string) => {
              const dataset = visibleDatasets.find(d => d.id === name);
              return [value, dataset?.name || name];
            }}
          />
          <Legend />
          {visibleDatasets.map((dataset, index) => (
            <Line
              key={dataset.id}
              type="monotone"
              dataKey={dataset.id}
              stroke={dataset.color || CHART_COLORS[index % CHART_COLORS.length]}
              strokeWidth={2}
              dot={{ r: 3 }}
              name={dataset.name}
            />
          ))}
        </LineChart>
      );
    } else {
      return (
        <BarChart {...commonProps}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="index" />
          <YAxis />
          <Tooltip 
            formatter={(value: any, name: string) => {
              const dataset = visibleDatasets.find(d => d.id === name);
              return [value, dataset?.name || name];
            }}
          />
          <Legend />
          {visibleDatasets.map((dataset, index) => (
            <Bar
              key={dataset.id}
              dataKey={dataset.id}
              fill={dataset.color || CHART_COLORS[index % CHART_COLORS.length]}
              name={dataset.name}
            />
          ))}
        </BarChart>
      );
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header Controls */}
      <div className="flex flex-wrap items-center justify-between gap-4">
        <div className="flex items-center space-x-4">
          <h2 className="text-xl font-semibold">Data Comparison</h2>
          <Badge variant="secondary">
            {selectedDatasets.length} dataset{selectedDatasets.length !== 1 ? 's' : ''}
          </Badge>
        </div>
        
        <div className="flex items-center space-x-2">
          <Select value={comparisonMode} onValueChange={(value: any) => setComparisonMode(value)}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {Object.entries(COMPARISON_MODES).map(([key, label]) => (
                <SelectItem key={key} value={key}>{label}</SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => setChartType(chartType === 'line' ? 'bar' : 'line')}
          >
            {chartType === 'line' ? <BarChart3 className="h-4 w-4" /> : <LineChartIcon className="h-4 w-4" />}
          </Button>
          
          {onAddDataset && (
            <Button variant="outline" size="sm" onClick={onAddDataset}>
              <Plus className="h-4 w-4 mr-2" />
              Add Dataset
            </Button>
          )}
          
          {onExportComparison && (
            <Button variant="outline" size="sm" onClick={() => onExportComparison('png')}>
              <Download className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Dataset Selection Panel */}
        <Card className="lg:col-span-1">
          <CardHeader>
            <CardTitle className="text-sm font-medium">Datasets</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {datasets.map(dataset => {
              const metrics = datasetMetrics[dataset.id];
              return (
                <div key={dataset.id} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        checked={selectedDatasets.includes(dataset.id)}
                        onCheckedChange={() => toggleDatasetVisibility(dataset.id)}
                      />
                      <div 
                        className="w-3 h-3 rounded-full" 
                        style={{ backgroundColor: dataset.color }}
                      />
                      <span className="text-sm font-medium">{dataset.name}</span>
                    </div>
                    
                    {onRemoveDataset && (
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        onClick={() => onRemoveDataset(dataset.id)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    )}
                  </div>
                  
                  {metrics && selectedDatasets.includes(dataset.id) && (
                    <div className="text-xs text-gray-600 space-y-1 ml-5">
                      <div className="flex justify-between">
                        <span>Mean:</span>
                        <span>{metrics.mean.toFixed(2)}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span>Trend:</span>
                        <div className="flex items-center space-x-1">
                          {metrics.trend === 'up' && <TrendingUp className="h-3 w-3 text-green-500" />}
                          {metrics.trend === 'down' && <TrendingDown className="h-3 w-3 text-red-500" />}
                          {metrics.trend === 'stable' && <Minus className="h-3 w-3 text-gray-500" />}
                          <span>{metrics.changePercent.toFixed(1)}%</span>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
            
            <Separator />
            
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  checked={normalizeData}
                  onCheckedChange={setNormalizeData}
                />
                <span className="text-sm">Normalize data</span>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox
                  checked={showStatistics}
                  onCheckedChange={setShowStatistics}
                />
                <span className="text-sm">Show statistics</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Main Comparison Chart */}
        <Card className="lg:col-span-3">
          <CardHeader>
            <CardTitle className="text-sm font-medium">
              {COMPARISON_MODES[comparisonMode]}
              {normalizeData && ' (Normalized)'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-96">
              <ResponsiveContainer width="100%" height="100%">
                {renderChart()}
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Statistics and Analysis */}
      {showStatistics && (
        <Tabs defaultValue="statistics" className="w-full">
          <TabsList>
            <TabsTrigger value="statistics">Statistics</TabsTrigger>
            <TabsTrigger value="analysis">Analysis</TabsTrigger>
          </TabsList>
          
          <TabsContent value="statistics" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {Object.entries(datasetMetrics).map(([datasetId, metrics]) => {
                const dataset = datasets.find(d => d.id === datasetId);
                if (!dataset || !selectedDatasets.includes(datasetId)) return null;
                
                return (
                  <Card key={datasetId}>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium flex items-center space-x-2">
                        <div 
                          className="w-3 h-3 rounded-full" 
                          style={{ backgroundColor: dataset.color }}
                        />
                        <span>{dataset.name}</span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      <div className="grid grid-cols-2 gap-2 text-xs">
                        <div>
                          <span className="text-gray-500">Mean:</span>
                          <span className="ml-1 font-medium">{metrics.mean.toFixed(2)}</span>
                        </div>
                        <div>
                          <span className="text-gray-500">Median:</span>
                          <span className="ml-1 font-medium">{metrics.median.toFixed(2)}</span>
                        </div>
                        <div>
                          <span className="text-gray-500">Min:</span>
                          <span className="ml-1 font-medium">{metrics.min.toFixed(2)}</span>
                        </div>
                        <div>
                          <span className="text-gray-500">Max:</span>
                          <span className="ml-1 font-medium">{metrics.max.toFixed(2)}</span>
                        </div>
                        <div className="col-span-2">
                          <span className="text-gray-500">Std Dev:</span>
                          <span className="ml-1 font-medium">{metrics.stdDev.toFixed(2)}</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </TabsContent>
          
          <TabsContent value="analysis" className="space-y-4">
            {comparisonResults.map((result, index) => (
              <Card key={index}>
                <CardHeader>
                  <CardTitle className="text-sm font-medium">
                    {result.dataset1} vs {result.dataset2}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <span className="text-sm text-gray-500">Correlation:</span>
                      <Badge 
                        variant={Math.abs(result.correlation) > 0.7 ? 'default' : 'secondary'}
                        className="ml-2"
                      >
                        {result.correlation.toFixed(2)}
                      </Badge>
                    </div>
                    <div>
                      <span className="text-sm text-gray-500">Similarity:</span>
                      <Badge 
                        variant={result.similarity > 70 ? 'default' : 'secondary'}
                        className="ml-2"
                      >
                        {result.similarity.toFixed(1)}%
                      </Badge>
                    </div>
                  </div>
                  
                  {result.significantDifferences.length > 0 && (
                    <div>
                      <h4 className="text-sm font-medium mb-2">Significant Differences:</h4>
                      <ul className="text-sm text-gray-600 space-y-1">
                        {result.significantDifferences.map((diff, idx) => (
                          <li key={idx} className="flex items-start space-x-2">
                            <span className="text-orange-500">•</span>
                            <span>{diff}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                  
                  {result.recommendations.length > 0 && (
                    <div>
                      <h4 className="text-sm font-medium mb-2">Recommendations:</h4>
                      <ul className="text-sm text-gray-600 space-y-1">
                        {result.recommendations.map((rec, idx) => (
                          <li key={idx} className="flex items-start space-x-2">
                            <span className="text-blue-500">•</span>
                            <span>{rec}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
};

export default DataComparisonView;