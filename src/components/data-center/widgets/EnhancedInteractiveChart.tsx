import React, { useState, use<PERSON><PERSON>back, use<PERSON>emo } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  AreaChart,
  Bar,
  Line,
  Pie,
  Cell,
  Scatter,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ReferenceLine,
  Brush
} from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import {
  ZoomIn,
  ZoomOut,
  Download,
  Maximize2,
  <PERSON>mize2,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Filter,
  Tren<PERSON><PERSON>p,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON> as LineChartIcon,
  <PERSON><PERSON>hart as PieChartIcon,
  Activity,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';

export interface ChartDataPoint {
  id: string;
  name: string;
  value: number;
  category?: string;
  timestamp?: string;
  metadata?: Record<string, any>;
  drillDownData?: ChartDataPoint[];
}

export interface DrillDownLevel {
  level: number;
  title: string;
  data: ChartDataPoint[];
  parentId?: string;
}

export interface ChartConfig {
  type: 'bar' | 'line' | 'pie' | 'scatter' | 'area';
  title: string;
  xAxisKey: string;
  yAxisKey: string;
  colorScheme?: string[];
  enableDrillDown?: boolean;
  enableZoom?: boolean;
  enableBrush?: boolean;
  showTrendLine?: boolean;
  aggregationType?: 'sum' | 'avg' | 'count' | 'max' | 'min';
}

export interface EnhancedInteractiveChartProps {
  data: ChartDataPoint[];
  config: ChartConfig;
  onDataPointClick?: (dataPoint: ChartDataPoint, level: number) => void;
  onExport?: (format: 'png' | 'svg' | 'csv' | 'json') => void;
  className?: string;
}

const CHART_COLORS = [
  '#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#00ff00',
  '#0088fe', '#00c49f', '#ffbb28', '#ff8042', '#8dd1e1'
];

const TREND_COLORS = {
  positive: '#10b981',
  negative: '#ef4444',
  neutral: '#6b7280'
};

export const EnhancedInteractiveChart: React.FC<EnhancedInteractiveChartProps> = ({
  data,
  config,
  onDataPointClick,
  onExport,
  className = ''
}) => {
  const [drillDownStack, setDrillDownStack] = useState<DrillDownLevel[]>([
    { level: 0, title: config.title, data }
  ]);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [selectedTimeRange, setSelectedTimeRange] = useState<string>('all');
  const [chartType, setChartType] = useState<ChartConfig['type']>(config.type);
  const [zoomDomain, setZoomDomain] = useState<[number, number] | null>(null);
  const [hoveredDataPoint, setHoveredDataPoint] = useState<ChartDataPoint | null>(null);

  const currentLevel = drillDownStack[drillDownStack.length - 1];
  const canDrillUp = drillDownStack.length > 1;

  // Process data based on selected time range and filters
  const processedData = useMemo(() => {
    let filtered = currentLevel.data;
    
    if (selectedTimeRange !== 'all' && filtered[0]?.timestamp) {
      const now = new Date();
      const timeRanges = {
        '1h': 1 * 60 * 60 * 1000,
        '24h': 24 * 60 * 60 * 1000,
        '7d': 7 * 24 * 60 * 60 * 1000,
        '30d': 30 * 24 * 60 * 60 * 1000
      };
      
      const range = timeRanges[selectedTimeRange as keyof typeof timeRanges];
      if (range) {
        filtered = filtered.filter(item => {
          const itemTime = new Date(item.timestamp!).getTime();
          return now.getTime() - itemTime <= range;
        });
      }
    }

    return filtered;
  }, [currentLevel.data, selectedTimeRange]);

  // Calculate trend analysis
  const trendAnalysis = useMemo(() => {
    if (processedData.length < 2) return null;
    
    const values = processedData.map(d => d.value);
    const firstHalf = values.slice(0, Math.floor(values.length / 2));
    const secondHalf = values.slice(Math.floor(values.length / 2));
    
    const firstAvg = firstHalf.reduce((a, b) => a + b, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((a, b) => a + b, 0) / secondHalf.length;
    
    const change = ((secondAvg - firstAvg) / firstAvg) * 100;
    
    return {
      change: change.toFixed(1),
      trend: change > 5 ? 'positive' : change < -5 ? 'negative' : 'neutral',
      direction: change > 0 ? 'up' : change < 0 ? 'down' : 'stable'
    };
  }, [processedData]);

  const handleDataPointClick = useCallback((dataPoint: any, index: number) => {
    const clickedPoint = processedData[index];
    
    if (config.enableDrillDown && clickedPoint?.drillDownData) {
      const newLevel: DrillDownLevel = {
        level: currentLevel.level + 1,
        title: `${clickedPoint.name} Details`,
        data: clickedPoint.drillDownData,
        parentId: clickedPoint.id
      };
      
      setDrillDownStack(prev => [...prev, newLevel]);
    }
    
    onDataPointClick?.(clickedPoint, currentLevel.level);
  }, [processedData, config.enableDrillDown, currentLevel.level, onDataPointClick]);

  const handleDrillUp = useCallback(() => {
    if (canDrillUp) {
      setDrillDownStack(prev => prev.slice(0, -1));
    }
  }, [canDrillUp]);

  const handleZoomReset = useCallback(() => {
    setZoomDomain(null);
  }, []);

  const handleExport = useCallback((format: 'png' | 'svg' | 'csv' | 'json') => {
    onExport?.(format);
  }, [onExport]);

  const renderChart = () => {
    const commonProps = {
      data: processedData,
      margin: { top: 20, right: 30, left: 20, bottom: 5 },
      onClick: handleDataPointClick
    };

    const customTooltip = ({ active, payload, label }: any) => {
      if (active && payload && payload.length) {
        const data = payload[0].payload;
        return (
          <div className="bg-white p-3 border rounded-lg shadow-lg">
            <p className="font-semibold">{label}</p>
            <p className="text-blue-600">
              {config.yAxisKey}: {payload[0].value}
            </p>
            {data.category && (
              <p className="text-gray-600">Category: {data.category}</p>
            )}
            {config.enableDrillDown && data.drillDownData && (
              <p className="text-xs text-gray-500 mt-1">
                Click to drill down ({data.drillDownData.length} items)
              </p>
            )}
          </div>
        );
      }
      return null;
    };

    switch (chartType) {
      case 'bar':
        return (
          <BarChart {...commonProps}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey={config.xAxisKey} />
            <YAxis />
            <Tooltip content={customTooltip} />
            <Legend />
            <Bar 
              dataKey={config.yAxisKey} 
              fill={CHART_COLORS[0]}
              cursor="pointer"
            >
              {processedData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={CHART_COLORS[index % CHART_COLORS.length]} />
              ))}
            </Bar>
            {config.enableBrush && <Brush />}
          </BarChart>
        );

      case 'line':
        return (
          <LineChart {...commonProps}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey={config.xAxisKey} />
            <YAxis />
            <Tooltip content={customTooltip} />
            <Legend />
            <Line 
              type="monotone" 
              dataKey={config.yAxisKey} 
              stroke={CHART_COLORS[0]}
              strokeWidth={2}
              dot={{ r: 4, cursor: 'pointer' }}
              activeDot={{ r: 6 }}
            />
            {config.showTrendLine && trendAnalysis && (
              <ReferenceLine 
                stroke={TREND_COLORS[trendAnalysis.trend as keyof typeof TREND_COLORS]} 
                strokeDasharray="5 5" 
              />
            )}
            {config.enableBrush && <Brush />}
          </LineChart>
        );

      case 'pie':
        return (
          <PieChart {...commonProps}>
            <Pie
              data={processedData}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
              outerRadius={80}
              fill="#8884d8"
              dataKey={config.yAxisKey}
              onClick={handleDataPointClick}
              cursor="pointer"
            >
              {processedData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={CHART_COLORS[index % CHART_COLORS.length]} />
              ))}
            </Pie>
            <Tooltip content={customTooltip} />
          </PieChart>
        );

      case 'area':
        return (
          <AreaChart {...commonProps}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey={config.xAxisKey} />
            <YAxis />
            <Tooltip content={customTooltip} />
            <Legend />
            <Area 
              type="monotone" 
              dataKey={config.yAxisKey} 
              stroke={CHART_COLORS[0]}
              fill={CHART_COLORS[0]}
              fillOpacity={0.3}
              cursor="pointer"
            />
            {config.enableBrush && <Brush />}
          </AreaChart>
        );

      case 'scatter':
        return (
          <ScatterChart {...commonProps}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey={config.xAxisKey} />
            <YAxis dataKey={config.yAxisKey} />
            <Tooltip content={customTooltip} />
            <Legend />
            <Scatter 
              data={processedData} 
              fill={CHART_COLORS[0]}
              cursor="pointer"
            />
          </ScatterChart>
        );

      default:
        return null;
    }
  };

  return (
    <Card className={`${className} ${isFullscreen ? 'fixed inset-0 z-50' : ''}`}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div className="flex items-center space-x-2">
          {canDrillUp && (
            <Button variant="outline" size="sm" onClick={handleDrillUp}>
              <ChevronLeft className="h-4 w-4" />
              Back
            </Button>
          )}
          <CardTitle className="text-lg font-semibold">
            {currentLevel.title}
          </CardTitle>
          {trendAnalysis && (
            <Badge 
              variant={trendAnalysis.trend === 'positive' ? 'default' : 
                      trendAnalysis.trend === 'negative' ? 'destructive' : 'secondary'}
            >
              <TrendingUp className="h-3 w-3 mr-1" />
              {trendAnalysis.change}%
            </Badge>
          )}
        </div>
        
        <div className="flex items-center space-x-2">
          <Select value={selectedTimeRange} onValueChange={setSelectedTimeRange}>
            <SelectTrigger className="w-24">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All</SelectItem>
              <SelectItem value="1h">1H</SelectItem>
              <SelectItem value="24h">24H</SelectItem>
              <SelectItem value="7d">7D</SelectItem>
              <SelectItem value="30d">30D</SelectItem>
            </SelectContent>
          </Select>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                {chartType === 'bar' && <BarChart3 className="h-4 w-4" />}
                {chartType === 'line' && <LineChartIcon className="h-4 w-4" />}
                {chartType === 'pie' && <PieChartIcon className="h-4 w-4" />}
                {chartType === 'scatter' && <Activity className="h-4 w-4" />}
                {chartType === 'area' && <Activity className="h-4 w-4" />}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => setChartType('bar')}>
                <BarChart3 className="h-4 w-4 mr-2" /> Bar Chart
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setChartType('line')}>
                <LineChartIcon className="h-4 w-4 mr-2" /> Line Chart
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setChartType('pie')}>
                <PieChartIcon className="h-4 w-4 mr-2" /> Pie Chart
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setChartType('area')}>
                <Activity className="h-4 w-4 mr-2" /> Area Chart
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setChartType('scatter')}>
                <Activity className="h-4 w-4 mr-2" /> Scatter Plot
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          
          {config.enableZoom && (
            <>
              <Button variant="outline" size="sm" onClick={handleZoomReset}>
                <RotateCcw className="h-4 w-4" />
              </Button>
            </>
          )}
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => handleExport('png')}>
                Export as PNG
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleExport('svg')}>
                Export as SVG
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => handleExport('csv')}>
                Export as CSV
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleExport('json')}>
                Export as JSON
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => setIsFullscreen(!isFullscreen)}
          >
            {isFullscreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
          </Button>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="h-96">
          <ResponsiveContainer width="100%" height="100%">
            {renderChart() || <div>No chart available</div>}
          </ResponsiveContainer>
        </div>
        
        {drillDownStack.length > 1 && (
          <div className="mt-4 flex items-center space-x-2">
            <span className="text-sm text-gray-600">Drill-down path:</span>
            {drillDownStack.map((level, index) => (
              <React.Fragment key={level.level}>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setDrillDownStack(prev => prev.slice(0, index + 1))}
                  className="text-blue-600 hover:text-blue-800"
                >
                  {level.title}
                </Button>
                {index < drillDownStack.length - 1 && (
                  <ChevronRight className="h-4 w-4 text-gray-400" />
                )}
              </React.Fragment>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default EnhancedInteractiveChart;