import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Plus,
  Settings,
  Trash2,
  Copy,
  Move,
  Maximize2,
  Minimize2,
  RotateCcw,
  Save,
  Download,
  Upload,
  Grid,
  Layout,
  Palette,
  Eye,
  EyeOff,
  Lock,
  Unlock,
  Refresh<PERSON><PERSON>,
  MoreVertical,
  Edit3,
  Share2,
  Filter,
  Search,
  Layers,
  Monitor,
  Smartphone,
  Tablet,
  Activity
} from 'lucide-react';
import type { DashboardWidget, WidgetConfig, ChartDataPoint } from './DashboardWidgets';

// Enhanced widget interfaces
interface CustomWidget extends DashboardWidget {
  position: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  zIndex: number;
  isLocked: boolean;
  theme: WidgetTheme;
  animation: WidgetAnimation;
  interactions: WidgetInteraction[];
  conditions: WidgetCondition[];
  lastUpdated: Date;
  createdBy: string;
  tags: string[];
  category: string;
}

interface WidgetTheme {
  backgroundColor: string;
  textColor: string;
  borderColor: string;
  accentColor: string;
  borderRadius: number;
  shadow: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  opacity: number;
}

interface WidgetAnimation {
  entrance: 'none' | 'fade' | 'slide' | 'bounce' | 'zoom';
  duration: number;
  delay: number;
  easing: 'linear' | 'ease' | 'ease-in' | 'ease-out' | 'ease-in-out';
}

interface WidgetInteraction {
  trigger: 'click' | 'hover' | 'doubleClick' | 'rightClick';
  action: 'navigate' | 'modal' | 'tooltip' | 'highlight' | 'filter' | 'export';
  target?: string;
  parameters?: Record<string, any>;
}

interface WidgetCondition {
  field: string;
  operator: 'equals' | 'notEquals' | 'greaterThan' | 'lessThan' | 'contains';
  value: any;
  action: 'show' | 'hide' | 'highlight' | 'disable';
}

interface DashboardLayout {
  id: string;
  name: string;
  description?: string;
  widgets: CustomWidget[];
  gridSize: { columns: number; rows: number };
  breakpoints: {
    desktop: number;
    tablet: number;
    mobile: number;
  };
  theme: DashboardTheme;
  isPublic: boolean;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  tags: string[];
}

interface DashboardTheme {
  name: string;
  colors: {
    primary: string;
    secondary: string;
    background: string;
    surface: string;
    text: string;
    border: string;
  };
  typography: {
    fontFamily: string;
    fontSize: {
      xs: string;
      sm: string;
      md: string;
      lg: string;
      xl: string;
    };
  };
  spacing: {
    xs: number;
    sm: number;
    md: number;
    lg: number;
    xl: number;
  };
}

interface WidgetTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  icon: React.ComponentType<any>;
  defaultConfig: Partial<CustomWidget>;
  requiredData: string[];
  complexity: 'beginner' | 'intermediate' | 'advanced';
  tags: string[];
  preview?: string;
}

type ViewMode = 'desktop' | 'tablet' | 'mobile';
type EditMode = 'design' | 'preview' | 'code';

// Widget templates
const WIDGET_TEMPLATES: WidgetTemplate[] = [
  {
    id: 'metric-card',
    name: 'Metric Card',
    description: 'Display key metrics with trend indicators',
    category: 'metrics',
    icon: Monitor,
    defaultConfig: {
      type: 'metric',
      position: { x: 0, y: 0, width: 2, height: 1 },
      theme: {
        backgroundColor: '#ffffff',
        textColor: '#1f2937',
        borderColor: '#e5e7eb',
        accentColor: '#3b82f6',
        borderRadius: 8,
        shadow: 'sm',
        opacity: 1
      }
    },
    requiredData: ['value', 'label'],
    complexity: 'beginner',
    tags: ['metrics', 'kpi', 'numbers']
  },
  {
    id: 'line-chart',
    name: 'Line Chart',
    description: 'Time series data visualization',
    category: 'charts',
    icon: Grid,
    defaultConfig: {
      type: 'chart',
      position: { x: 0, y: 0, width: 4, height: 3 },
      config: { chartType: 'line' }
    },
    requiredData: ['x', 'y'],
    complexity: 'intermediate',
    tags: ['chart', 'time-series', 'trends']
  },
  {
    id: 'data-table',
    name: 'Data Table',
    description: 'Tabular data with sorting and filtering',
    category: 'data',
    icon: Layout,
    defaultConfig: {
      type: 'table',
      position: { x: 0, y: 0, width: 6, height: 4 }
    },
    requiredData: ['columns', 'rows'],
    complexity: 'intermediate',
    tags: ['table', 'data', 'list']
  },
  {
    id: 'progress-bar',
    name: 'Progress Bar',
    description: 'Show completion progress',
    category: 'indicators',
    icon: Layers,
    defaultConfig: {
      type: 'metric',
      position: { x: 0, y: 0, width: 3, height: 1 }
    },
    requiredData: ['current', 'total'],
    complexity: 'beginner',
    tags: ['progress', 'completion', 'status']
  }
];

// Default themes
const DEFAULT_THEMES: DashboardTheme[] = [
  {
    name: 'Light',
    colors: {
      primary: '#3b82f6',
      secondary: '#6b7280',
      background: '#f9fafb',
      surface: '#ffffff',
      text: '#1f2937',
      border: '#e5e7eb'
    },
    typography: {
      fontFamily: 'Inter, sans-serif',
      fontSize: {
        xs: '0.75rem',
        sm: '0.875rem',
        md: '1rem',
        lg: '1.125rem',
        xl: '1.25rem'
      }
    },
    spacing: {
      xs: 4,
      sm: 8,
      md: 16,
      lg: 24,
      xl: 32
    }
  },
  {
    name: 'Dark',
    colors: {
      primary: '#60a5fa',
      secondary: '#9ca3af',
      background: '#111827',
      surface: '#1f2937',
      text: '#f9fafb',
      border: '#374151'
    },
    typography: {
      fontFamily: 'Inter, sans-serif',
      fontSize: {
        xs: '0.75rem',
        sm: '0.875rem',
        md: '1rem',
        lg: '1.125rem',
        xl: '1.25rem'
      }
    },
    spacing: {
      xs: 4,
      sm: 8,
      md: 16,
      lg: 24,
      xl: 32
    }
  }
];

// Dashboard manager class
class CustomizableDashboardManager {
  private layouts: Map<string, DashboardLayout> = new Map();
  private currentLayoutId: string | null = null;
  private subscribers: Set<() => void> = new Set();
  private history: DashboardLayout[] = [];
  private historyIndex = -1;

  constructor() {
    this.loadLayouts();
  }

  // Layout management
  createLayout(name: string, description?: string): DashboardLayout {
    const layout: DashboardLayout = {
      id: `layout_${Date.now()}`,
      name,
      description,
      widgets: [],
      gridSize: { columns: 12, rows: 8 },
      breakpoints: {
        desktop: 1200,
        tablet: 768,
        mobile: 480
      },
      theme: DEFAULT_THEMES[0],
      isPublic: false,
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: 'user',
      tags: []
    };

    this.layouts.set(layout.id, layout);
    this.currentLayoutId = layout.id;
    this.saveToHistory(layout);
    this.saveLayouts();
    this.notifySubscribers();
    return layout;
  }

  getLayout(id: string): DashboardLayout | undefined {
    return this.layouts.get(id);
  }

  getCurrentLayout(): DashboardLayout | undefined {
    return this.currentLayoutId ? this.layouts.get(this.currentLayoutId) : undefined;
  }

  setCurrentLayout(id: string): void {
    if (this.layouts.has(id)) {
      this.currentLayoutId = id;
      this.notifySubscribers();
    }
  }

  updateLayout(id: string, updates: Partial<DashboardLayout>): void {
    const layout = this.layouts.get(id);
    if (layout) {
      const updatedLayout = { ...layout, ...updates, updatedAt: new Date() };
      this.layouts.set(id, updatedLayout);
      this.saveToHistory(updatedLayout);
      this.saveLayouts();
      this.notifySubscribers();
    }
  }

  deleteLayout(id: string): void {
    this.layouts.delete(id);
    if (this.currentLayoutId === id) {
      this.currentLayoutId = this.layouts.keys().next().value || null;
    }
    this.saveLayouts();
    this.notifySubscribers();
  }

  duplicateLayout(id: string, newName: string): DashboardLayout | undefined {
    const layout = this.layouts.get(id);
    if (layout) {
      const duplicate: DashboardLayout = {
        ...layout,
        id: `layout_${Date.now()}`,
        name: newName,
        createdAt: new Date(),
        updatedAt: new Date(),
        widgets: layout.widgets.map(widget => ({
          ...widget,
          id: `widget_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        }))
      };
      this.layouts.set(duplicate.id, duplicate);
      this.saveLayouts();
      this.notifySubscribers();
      return duplicate;
    }
    return undefined;
  }

  // Widget management
  addWidget(layoutId: string, template: WidgetTemplate, position?: { x: number; y: number }): CustomWidget {
    const layout = this.layouts.get(layoutId);
    if (!layout) throw new Error('Layout not found');

    const defaultPosition = template.defaultConfig.position || { x: 0, y: 0, width: 2, height: 2 };
    const widget: CustomWidget = {
      id: `widget_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      title: template.name,
      type: (template.defaultConfig as any).type || 'metric',
      size: 'medium',
      config: (template.defaultConfig as any).config || {},
      data: [],
      isVisible: true,
      position: {
        x: position?.x || defaultPosition.x,
        y: position?.y || defaultPosition.y,
        width: defaultPosition.width,
        height: defaultPosition.height
      },
      zIndex: layout.widgets.length,
      isLocked: false,
      theme: (template.defaultConfig as any).theme || {
        backgroundColor: '#ffffff',
        textColor: '#1f2937',
        borderColor: '#e5e7eb',
        accentColor: '#3b82f6',
        borderRadius: 8,
        shadow: 'sm',
        opacity: 1
      },
      animation: {
        entrance: 'fade',
        duration: 300,
        delay: 0,
        easing: 'ease-out'
      },
      interactions: [],
      conditions: [],
      lastUpdated: new Date(),
      createdBy: 'user',
      tags: template.tags,
      category: template.category
    };

    layout.widgets.push(widget);
    this.updateLayout(layoutId, layout);
    return widget;
  }

  updateWidget(layoutId: string, widgetId: string, updates: Partial<CustomWidget>): void {
    const layout = this.layouts.get(layoutId);
    if (!layout) return;

    const widgetIndex = layout.widgets.findIndex(w => w.id === widgetId);
    if (widgetIndex !== -1) {
      layout.widgets[widgetIndex] = {
        ...layout.widgets[widgetIndex],
        ...updates,
        lastUpdated: new Date()
      };
      this.updateLayout(layoutId, layout);
    }
  }

  removeWidget(layoutId: string, widgetId: string): void {
    const layout = this.layouts.get(layoutId);
    if (!layout) return;

    layout.widgets = layout.widgets.filter(w => w.id !== widgetId);
    this.updateLayout(layoutId, layout);
  }

  duplicateWidget(layoutId: string, widgetId: string): CustomWidget | undefined {
    const layout = this.layouts.get(layoutId);
    if (!layout) return undefined;

    const widget = layout.widgets.find(w => w.id === widgetId);
    if (!widget) return undefined;

    // Find an available position for the duplicate
    const findAvailablePosition = () => {
      const { columns, rows } = layout.gridSize;
      const occupiedPositions = layout.widgets.map(w => ({
        x: w.position.x,
        y: w.position.y,
        width: w.position.width,
        height: w.position.height
      }));

      // Try positions near the original widget first
      const candidates = [
        { x: widget.position.x + widget.position.width, y: widget.position.y },
        { x: widget.position.x, y: widget.position.y + widget.position.height },
        { x: widget.position.x + 1, y: widget.position.y + 1 }
      ];

      for (const candidate of candidates) {
        if (candidate.x + widget.position.width <= columns &&
            candidate.y + widget.position.height <= rows) {
          const isOccupied = occupiedPositions.some(pos => 
            candidate.x < pos.x + pos.width &&
            candidate.x + widget.position.width > pos.x &&
            candidate.y < pos.y + pos.height &&
            candidate.y + widget.position.height > pos.y
          );
          if (!isOccupied) {
            return candidate;
          }
        }
      }

      // Fallback: find any available position
      for (let y = 0; y <= rows - widget.position.height; y++) {
        for (let x = 0; x <= columns - widget.position.width; x++) {
          const isOccupied = occupiedPositions.some(pos => 
            x < pos.x + pos.width &&
            x + widget.position.width > pos.x &&
            y < pos.y + pos.height &&
            y + widget.position.height > pos.y
          );
          if (!isOccupied) {
            return { x, y };
          }
        }
      }

      return { x: 0, y: 0 }; // Last resort
    };

    const newPosition = findAvailablePosition();
    const duplicate: CustomWidget = {
      ...widget,
      id: `widget_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      position: {
        ...widget.position,
        x: newPosition.x,
        y: newPosition.y
      },
      title: `${widget.title} (Copy)`,
      lastUpdated: new Date()
    };

    layout.widgets.push(duplicate);
    this.updateLayout(layoutId, layout);
    return duplicate;
  }

  // History management
  private saveToHistory(layout: DashboardLayout): void {
    // Remove any history after current index
    this.history = this.history.slice(0, this.historyIndex + 1);
    
    // Add new state
    this.history.push(JSON.parse(JSON.stringify(layout)));
    this.historyIndex = this.history.length - 1;
    
    // Limit history size
    if (this.history.length > 50) {
      this.history = this.history.slice(-50);
      this.historyIndex = this.history.length - 1;
    }
  }

  undo(): boolean {
    if (this.historyIndex > 0) {
      this.historyIndex--;
      const layout = this.history[this.historyIndex];
      if (layout) {
        this.layouts.set(layout.id, layout);
        this.notifySubscribers();
        return true;
      }
    }
    return false;
  }

  redo(): boolean {
    if (this.historyIndex < this.history.length - 1) {
      this.historyIndex++;
      const layout = this.history[this.historyIndex];
      if (layout) {
        this.layouts.set(layout.id, layout);
        this.notifySubscribers();
        return true;
      }
    }
    return false;
  }

  canUndo(): boolean {
    return this.historyIndex > 0;
  }

  canRedo(): boolean {
    return this.historyIndex < this.history.length - 1;
  }

  // Import/Export
  exportLayout(id: string): string {
    const layout = this.layouts.get(id);
    if (!layout) throw new Error('Layout not found');
    return JSON.stringify(layout, null, 2);
  }

  importLayout(data: string): DashboardLayout {
    const layout = JSON.parse(data) as DashboardLayout;
    layout.id = `layout_${Date.now()}`;
    layout.createdAt = new Date();
    layout.updatedAt = new Date();
    
    // Generate new widget IDs
    layout.widgets = layout.widgets.map(widget => ({
      ...widget,
      id: `widget_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    }));
    
    this.layouts.set(layout.id, layout);
    this.saveLayouts();
    this.notifySubscribers();
    return layout;
  }

  // Persistence
  private saveLayouts(): void {
    try {
      const data = {
        layouts: Array.from(this.layouts.entries()),
        currentLayoutId: this.currentLayoutId
      };
      localStorage.setItem('customizable_dashboard_layouts', JSON.stringify(data));
    } catch (error) {
    }
  }

  private loadLayouts(): void {
    try {
      const data = localStorage.getItem('customizable_dashboard_layouts');
      if (data) {
        const parsed = JSON.parse(data);
        this.layouts = new Map(parsed.layouts);
        this.currentLayoutId = parsed.currentLayoutId;
      }
    } catch (error) {
    }
  }

  // Subscription
  subscribe(callback: () => void): () => void {
    this.subscribers.add(callback);
    return () => this.subscribers.delete(callback);
  }

  private notifySubscribers(): void {
    this.subscribers.forEach(callback => callback());
  }

  // Utility methods
  getAllLayouts(): DashboardLayout[] {
    return Array.from(this.layouts.values());
  }

  searchLayouts(query: string): DashboardLayout[] {
    const lowercaseQuery = query.toLowerCase();
    return this.getAllLayouts().filter(layout => 
      layout.name.toLowerCase().includes(lowercaseQuery) ||
      layout.description?.toLowerCase().includes(lowercaseQuery) ||
      layout.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery))
    );
  }

  getLayoutsByCategory(category: string): DashboardLayout[] {
    return this.getAllLayouts().filter(layout => 
      layout.widgets.some(widget => widget.category === category)
    );
  }
}

// Widget component wrapper
const CustomizableWidget: React.FC<{
  widget: CustomWidget;
  isEditing: boolean;
  isSelected: boolean;
  onSelect: (id: string) => void;
  onUpdate: (updates: Partial<CustomWidget>) => void;
  onRemove: () => void;
  onDuplicate: () => void;
}> = ({ widget, isEditing, isSelected, onSelect, onUpdate, onRemove, onDuplicate }) => {
  const [showMenu, setShowMenu] = useState(false);

  const getWidgetComponent = () => {
    const baseProps = {
      id: widget.id,
      title: widget.title,
      type: widget.type,
      size: widget.size,
      config: widget.config,
      data: widget.data,
      isVisible: widget.isVisible
    };

    return (
      <DashboardWidget {...baseProps} />
    );
  };

  const widgetStyle: React.CSSProperties = {
    backgroundColor: widget.theme.backgroundColor,
    color: widget.theme.textColor,
    borderColor: widget.theme.borderColor,
    borderRadius: widget.theme.borderRadius,
    opacity: widget.theme.opacity,
    boxShadow: {
      'none': 'none',
      'sm': '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
      'md': '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
      'lg': '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
      'xl': '0 20px 25px -5px rgba(0, 0, 0, 0.1)'
    }[widget.theme.shadow],
    zIndex: widget.zIndex,
    display: widget.isVisible ? 'block' : 'none'
  };

  return (
    <div
      className={`relative border-2 transition-all duration-200 ${
        isSelected ? 'border-blue-500 ring-2 ring-blue-200' : 'border-transparent'
      } ${isEditing ? 'cursor-move' : ''}`}
      style={widgetStyle}
      onClick={() => isEditing && onSelect(widget.id)}
    >
      {/* Widget content */}
      <div className="p-4 h-full">
        <div className="flex items-center justify-between mb-2">
          <h3 className="font-medium text-sm truncate">{widget.title}</h3>
          {isEditing && (
            <div className="flex items-center gap-1">
              {widget.isLocked && <Lock className="w-3 h-3 text-gray-400" />}
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setShowMenu(!showMenu);
                }}
                className="p-1 hover:bg-gray-100 rounded opacity-0 group-hover:opacity-100 transition-opacity"
              >
                <MoreVertical className="w-3 h-3" />
              </button>
            </div>
          )}
        </div>
        
        {/* Render actual widget content based on type */}
        <div className="h-full">
          {widget.type === 'metric' && (
            <div className="text-center">
              <div className="text-2xl font-bold" style={{ color: widget.theme.accentColor }}>
                {widget.data[0]?.value || '0'}
              </div>
              <div className="text-xs text-gray-500">
                {widget.data[0]?.label || 'Metric'}
              </div>
            </div>
          )}
          
          {widget.type === 'chart' && (
            <div className="h-full flex items-center justify-center bg-gray-50 rounded">
              <div className="text-center text-gray-500">
                <Grid className="w-8 h-8 mx-auto mb-2" />
                <p className="text-xs">Chart Widget</p>
              </div>
            </div>
          )}
          
          {widget.type === 'table' && (
            <div className="h-full flex items-center justify-center bg-gray-50 rounded">
              <div className="text-center text-gray-500">
                <Layout className="w-8 h-8 mx-auto mb-2" />
                <p className="text-xs">Table Widget</p>
              </div>
            </div>
          )}
          
          {widget.type === 'list' && (
            <div className="h-full flex items-center justify-center bg-gray-50 rounded">
              <div className="text-center text-gray-500">
                <Layers className="w-8 h-8 mx-auto mb-2" />
                <p className="text-xs">List Widget</p>
              </div>
            </div>
          )}
          
          {widget.type === 'table' && (
            <div className="h-full flex items-center justify-center bg-gray-50 rounded">
              <div className="text-center text-gray-500">
                <Layout className="w-8 h-8 mx-auto mb-2" />
                <p className="text-xs">Table Widget</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Resize handles */}
      {isEditing && isSelected && !widget.isLocked && (
        <>
          <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-blue-500 rounded cursor-se-resize" />
          <div className="absolute -bottom-1 -left-1 w-3 h-3 bg-blue-500 rounded cursor-sw-resize" />
          <div className="absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded cursor-ne-resize" />
          <div className="absolute -top-1 -left-1 w-3 h-3 bg-blue-500 rounded cursor-nw-resize" />
        </>
      )}

      {/* Context menu */}
      {showMenu && isEditing && (
        <div className="absolute top-8 right-0 bg-white border rounded-lg shadow-lg z-50 min-w-40">
          <button
            onClick={() => {
              onUpdate({ isLocked: !widget.isLocked });
              setShowMenu(false);
            }}
            className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2"
          >
            {widget.isLocked ? <Unlock className="w-3 h-3" /> : <Lock className="w-3 h-3" />}
            {widget.isLocked ? 'Unlock' : 'Lock'}
          </button>
          <button
            onClick={() => {
              onUpdate({ isVisible: !widget.isVisible });
              setShowMenu(false);
            }}
            className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2"
          >
            {widget.isVisible ? <EyeOff className="w-3 h-3" /> : <Eye className="w-3 h-3" />}
            {widget.isVisible ? 'Hide' : 'Show'}
          </button>
          <button
            onClick={() => {
              onDuplicate();
              setShowMenu(false);
            }}
            className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2"
          >
            <Copy className="w-3 h-3" />
            Duplicate
          </button>
          <hr className="my-1" />
          <button
            onClick={() => {
              onRemove();
              setShowMenu(false);
            }}
            className="w-full px-3 py-2 text-left text-sm hover:bg-red-50 text-red-600 flex items-center gap-2"
          >
            <Trash2 className="w-3 h-3" />
            Remove
          </button>
        </div>
      )}

      {/* Click outside to close menu */}
      {showMenu && (
        <div className="fixed inset-0 z-40" onClick={() => setShowMenu(false)} />
      )}
    </div>
  );
};

// Widget palette component
const WidgetPalette: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  onAddWidget: (template: WidgetTemplate) => void;
}> = ({ isOpen, onClose, onAddWidget }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const categories = useMemo(() => {
    const cats = new Set(WIDGET_TEMPLATES.map(t => t.category));
    return ['all', ...Array.from(cats)];
  }, []);

  const filteredTemplates = useMemo(() => {
    return WIDGET_TEMPLATES.filter(template => {
      const matchesSearch = !searchQuery || 
        template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        template.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
      
      const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory;
      
      return matchesSearch && matchesCategory;
    });
  }, [searchQuery, selectedCategory]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl h-3/4 flex flex-col">
        {/* Header */}
        <div className="p-4 border-b flex items-center justify-between">
          <h2 className="text-lg font-semibold">Widget Library</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg"
          >
            <Plus className="w-5 h-5 rotate-45" />
          </button>
        </div>

        {/* Search and filters */}
        <div className="p-4 border-b">
          <div className="flex gap-4 mb-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search widgets..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {categories.map(category => (
                <option key={category} value={category}>
                  {category.charAt(0).toUpperCase() + category.slice(1)}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Widget grid */}
        <div className="flex-1 p-4 overflow-y-auto">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredTemplates.map(template => {
              const Icon = template.icon;
              return (
                <div
                  key={template.id}
                  className="border rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer group"
                  onClick={() => {
                    onAddWidget(template);
                    onClose();
                  }}
                >
                  <div className="flex items-center gap-3 mb-3">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <Icon className="w-5 h-5 text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-medium text-sm">{template.name}</h3>
                      <p className="text-xs text-gray-500 capitalize">{template.category}</p>
                    </div>
                  </div>
                  
                  <p className="text-xs text-gray-600 mb-3 line-clamp-2">
                    {template.description}
                  </p>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex flex-wrap gap-1">
                      {template.tags.slice(0, 2).map(tag => (
                        <span
                          key={tag}
                          className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                    <span className={`text-xs px-2 py-1 rounded ${
                      template.complexity === 'beginner' ? 'bg-green-100 text-green-600' :
                      template.complexity === 'intermediate' ? 'bg-yellow-100 text-yellow-600' :
                      'bg-red-100 text-red-600'
                    }`}>
                      {template.complexity}
                    </span>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Widget Menu */}
      {showMenu && (
        <div className="absolute top-8 right-0 bg-white border rounded shadow-lg p-2 z-50 min-w-32">
          <div className="space-y-1">
            <button
              onClick={(e) => {
                e.stopPropagation();
                onUpdate({ isLocked: !widget.isLocked });
                setShowMenu(false);
              }}
              className="w-full text-left px-2 py-1 text-xs hover:bg-gray-100 rounded flex items-center gap-2"
            >
              {widget.isLocked ? <Unlock className="w-3 h-3" /> : <Lock className="w-3 h-3" />}
              {widget.isLocked ? 'Unlock' : 'Lock'}
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation();
                onDuplicate();
                setShowMenu(false);
              }}
              className="w-full text-left px-2 py-1 text-xs hover:bg-gray-100 rounded flex items-center gap-2"
            >
              <Copy className="w-3 h-3" />
              Duplicate
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation();
                onRemove();
                setShowMenu(false);
              }}
              className="w-full text-left px-2 py-1 text-xs hover:bg-red-100 text-red-600 rounded flex items-center gap-2"
            >
              <Trash2 className="w-3 h-3" />
              Remove
            </button>
          </div>
        </div>
      )}

      {/* Resize Handles */}
      {isEditing && isSelected && !widget.isLocked && (
        <>
          <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-blue-500 rounded cursor-se-resize" />
          <div className="absolute -bottom-1 -left-1 w-3 h-3 bg-blue-500 rounded cursor-sw-resize" />
          <div className="absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded cursor-ne-resize" />
          <div className="absolute -top-1 -left-1 w-3 h-3 bg-blue-500 rounded cursor-nw-resize" />
        </>
      )}
    </div>
  );
};

// Main customizable dashboard component
export const CustomizableDashboard: React.FC<{
  initialLayoutId?: string;
  onLayoutChange?: (layout: DashboardLayout) => void;
}> = ({ initialLayoutId, onLayoutChange }) => {
  const [manager] = useState(() => new CustomizableDashboardManager());
  const [currentLayout, setCurrentLayout] = useState<DashboardLayout | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [editMode, setEditMode] = useState<EditMode>('design');
  const [viewMode, setViewMode] = useState<ViewMode>('desktop');
  const [selectedWidgetId, setSelectedWidgetId] = useState<string | null>(null);
  const [showPalette, setShowPalette] = useState(false);
  const [draggedWidget, setDraggedWidget] = useState<string | null>(null);

  useEffect(() => {
    const unsubscribe = manager.subscribe(() => {
      const layout = manager.getCurrentLayout();
      setCurrentLayout(layout || null);
      if (layout) {
        onLayoutChange?.(layout);
      }
    });

    // Set initial layout
    if (initialLayoutId) {
      manager.setCurrentLayout(initialLayoutId);
    } else {
      const layouts = manager.getAllLayouts();
      if (layouts.length === 0) {
        manager.createLayout('Default Dashboard', 'My first dashboard');
      } else {
        manager.setCurrentLayout(layouts[0].id);
      }
    }

    return unsubscribe;
  }, [manager, initialLayoutId, onLayoutChange]);

  const handleMoveWidget = useCallback((widgetId: string, newPosition: { x: number; y: number }) => {
    if (!isEditing || !currentLayout) return;

    const widget = currentLayout.widgets.find(w => w.id === widgetId);
    if (!widget || widget.isLocked) return;

    const updatedPosition = {
      ...widget.position,
      x: newPosition.x,
      y: newPosition.y
    };

    handleUpdateWidget(widgetId, { position: updatedPosition });
  }, [isEditing, currentLayout]);

  const handleAddWidget = useCallback((template: WidgetTemplate) => {
    if (!currentLayout) return;
    
    // Find empty position
    const position = { x: 0, y: 0 };
    const existingPositions = currentLayout.widgets.map(w => ({ x: w.position.x, y: w.position.y }));
    
    // Simple positioning logic - find first available spot
    for (let y = 0; y < currentLayout.gridSize.rows; y++) {
      for (let x = 0; x < currentLayout.gridSize.columns; x++) {
        const occupied = existingPositions.some(pos => pos.x === x && pos.y === y);
        if (!occupied) {
          position.x = x;
          position.y = y;
          break;
        }
      }
      if (position.x !== 0 || position.y !== 0) break;
    }
    
    manager.addWidget(currentLayout.id, template, position);
  }, [currentLayout, manager]);

  const handleUpdateWidget = useCallback((widgetId: string, updates: Partial<CustomWidget>) => {
    if (!currentLayout) return;
    manager.updateWidget(currentLayout.id, widgetId, updates);
  }, [currentLayout, manager]);

  const handleRemoveWidget = useCallback((widgetId: string) => {
    if (!currentLayout) return;
    manager.removeWidget(currentLayout.id, widgetId);
    setSelectedWidgetId(null);
  }, [currentLayout, manager]);

  const handleDuplicateWidget = useCallback((widgetId: string) => {
    if (!currentLayout) return;
    manager.duplicateWidget(currentLayout.id, widgetId);
  }, [currentLayout, manager]);

  const getGridStyle = useCallback(() => {
    if (!currentLayout) return {};
    
    const { columns, rows } = currentLayout.gridSize;
    return {
      display: 'grid',
      gridTemplateColumns: `repeat(${columns}, 1fr)`,
      gridTemplateRows: `repeat(${rows}, 1fr)`,
      gap: '16px',
      minHeight: '600px'
    };
  }, [currentLayout]);

  if (!currentLayout) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="w-6 h-6 animate-spin text-blue-500" />
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* Toolbar */}
      <div className="bg-white border-b p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <h1 className="text-lg font-semibold">{currentLayout.name}</h1>
            
            <div className="flex items-center gap-2">
              <button
                onClick={() => setIsEditing(!isEditing)}
                className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-colors ${
                  isEditing 
                    ? 'bg-blue-600 text-white' 
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {isEditing ? 'Exit Edit' : 'Edit'}
              </button>
              
              {isEditing && (
                <button
                  onClick={() => setShowPalette(true)}
                  className="px-3 py-1.5 bg-green-600 text-white rounded-lg text-sm font-medium hover:bg-green-700 transition-colors flex items-center gap-2"
                >
                  <Plus className="w-4 h-4" />
                  Add Widget
                </button>
              )}
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            {/* View mode selector */}
            <div className="flex border rounded-lg">
              <button
                onClick={() => setViewMode('desktop')}
                className={`p-2 rounded-l-lg ${
                  viewMode === 'desktop' ? 'bg-blue-500 text-white' : 'hover:bg-gray-100'
                }`}
                title="Desktop View"
              >
                <Monitor className="w-4 h-4" />
              </button>
              <button
                onClick={() => setViewMode('tablet')}
                className={`p-2 ${
                  viewMode === 'tablet' ? 'bg-blue-500 text-white' : 'hover:bg-gray-100'
                }`}
                title="Tablet View"
              >
                <Tablet className="w-4 h-4" />
              </button>
              <button
                onClick={() => setViewMode('mobile')}
                className={`p-2 rounded-r-lg ${
                  viewMode === 'mobile' ? 'bg-blue-500 text-white' : 'hover:bg-gray-100'
                }`}
                title="Mobile View"
              >
                <Smartphone className="w-4 h-4" />
              </button>
            </div>
            
            {isEditing && (
              <>
                <button
                  onClick={() => manager.undo()}
                  disabled={!manager.canUndo()}
                  className="p-2 hover:bg-gray-100 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
                  title="Undo"
                >
                  <RotateCcw className="w-4 h-4" />
                </button>
                
                <button
                  onClick={() => manager.redo()}
                  disabled={!manager.canRedo()}
                  className="p-2 hover:bg-gray-100 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
                  title="Redo"
                >
                  <RotateCcw className="w-4 h-4 scale-x-[-1]" />
                </button>
              </>
            )}
            
            <button
              onClick={() => {
                const data = manager.exportLayout(currentLayout.id);
                const blob = new Blob([data], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `${currentLayout.name}.json`;
                a.click();
                URL.revokeObjectURL(url);
              }}
              className="p-2 hover:bg-gray-100 rounded-lg"
              title="Export Layout"
            >
              <Download className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Dashboard content */}
      <div className="flex-1 p-4 overflow-auto">
        <div className="relative" style={getGridStyle()}>
          {currentLayout.widgets.map((widget) => (
            <div
              key={widget.id}
              style={{
                gridColumn: `${widget.position.x + 1} / span ${widget.position.width}`,
                gridRow: `${widget.position.y + 1} / span ${widget.position.height}`
              }}
            >
              <CustomizableWidget
                widget={widget}
                isEditing={isEditing}
                isSelected={selectedWidgetId === widget.id}
                onSelect={setSelectedWidgetId}
                onUpdate={(updates) => handleUpdateWidget(widget.id, updates)}
                onRemove={() => handleRemoveWidget(widget.id)}
                onDuplicate={() => handleDuplicateWidget(widget.id)}
              />
            </div>
          ))}
        </div>
      </div>

      {/* Widget palette */}
      <WidgetPalette
        isOpen={showPalette}
        onClose={() => setShowPalette(false)}
        onAddWidget={handleAddWidget}
      />
    </div>
  );
};

export default CustomizableDashboard;
export { CustomizableDashboardManager, type CustomWidget, type DashboardLayout, type WidgetTemplate };
export { WIDGET_TEMPLATES, DEFAULT_THEMES };