import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { 
  Tag, 
  Plus, 
  X, 
  Edit3, 
  Search, 
  Filter, 
  CheckCircle, 
  Clock, 
  AlertCircle, 
  Archive, 
  Star, 
  Folder, 
  File, 
  Hash,
  Palette,
  Eye,
  EyeOff,
  MoreH<PERSON>zontal,
  Trash2,
  Co<PERSON>
} from 'lucide-react';
import { FileTag, FileTaggingManager } from '../utils/fileTagging';

// Enhanced interfaces
interface EnhancedFileTag extends FileTag {
  status: 'active' | 'archived' | 'pending' | 'deprecated';
  priority: 'low' | 'medium' | 'high' | 'critical';
  createdBy: string;
  lastModified: Date;
  usageCount: number;
  relatedTags: string[];
  metadata: {
    category?: string;
    autoGenerated?: boolean;
    rules?: string[];
  };
}

interface TagFilter {
  status?: string[];
  priority?: string[];
  category?: string[];
  search?: string;
  dateRange?: {
    start: Date;
    end: Date;
  };
}

interface TagAnalytics {
  totalTags: number;
  activeTags: number;
  mostUsedTags: { tag: EnhancedFileTag; count: number }[];
  recentActivity: {
    action: 'created' | 'modified' | 'deleted' | 'applied';
    tag: string;
    timestamp: Date;
    user: string;
  }[];
  categoryDistribution: { [category: string]: number };
}

// Enhanced File Tagging Manager
class EnhancedFileTaggingManager extends FileTaggingManager {
  private enhancedTags: Map<string, EnhancedFileTag> = new Map();
  private analytics: TagAnalytics;
  private subscribers: Set<() => void> = new Set();

  constructor() {
    super();
    this.initializeEnhancedTags();
    this.analytics = this.calculateAnalytics();
  }

  private initializeEnhancedTags(): void {
    // Convert existing tags to enhanced tags
    const existingTags = this.getTags();
    existingTags.forEach((tag: FileTag) => {
      const enhancedTag: EnhancedFileTag = {
        ...tag,
        status: 'active',
        priority: 'medium',
        createdBy: 'system',
        lastModified: new Date(),
        usageCount: Math.floor(Math.random() * 50),
        relatedTags: [],
        metadata: {
          category: this.inferCategory(tag.name),
          autoGenerated: false
        }
      };
      this.enhancedTags.set(tag.id, enhancedTag);
    });
  }

  private inferCategory(tagName: string): string {
    const categories = {
      'project': ['project', 'proj', 'work'],
      'status': ['todo', 'done', 'progress', 'review', 'urgent'],
      'type': ['document', 'image', 'video', 'audio', 'code'],
      'priority': ['high', 'low', 'critical', 'normal'],
      'department': ['hr', 'finance', 'marketing', 'dev', 'design']
    };

    for (const [category, keywords] of Object.entries(categories)) {
      if (keywords.some(keyword => tagName.toLowerCase().includes(keyword))) {
        return category;
      }
    }
    return 'general';
  }

  createEnhancedTag(tagData: { name: string; color: string; description?: string; status?: EnhancedFileTag['status']; priority?: EnhancedFileTag['priority'] }): EnhancedFileTag {
    const baseTag = this.createTag(tagData.name, tagData.color, tagData.description);
    const newTag: EnhancedFileTag = {
      ...baseTag,
      status: tagData.status || 'active',
      priority: tagData.priority || 'medium',
      createdBy: 'user',
      lastModified: new Date(),
      usageCount: 0,
      relatedTags: [],
      metadata: {
        category: this.inferCategory(tagData.name),
        autoGenerated: false,
        rules: []
      }
    };

    this.enhancedTags.set(newTag.id, newTag);
    this.notifySubscribers();
    return newTag;
  }

  getEnhancedTags(filter?: TagFilter): EnhancedFileTag[] {
    let tags = Array.from(this.enhancedTags.values());

    if (filter) {
      if (filter.status?.length) {
        tags = tags.filter(tag => filter.status!.includes(tag.status));
      }
      if (filter.priority?.length) {
        tags = tags.filter(tag => filter.priority!.includes(tag.priority));
      }
      if (filter.category?.length) {
        tags = tags.filter(tag => filter.category!.includes(tag.metadata.category || ''));
      }
      if (filter.search) {
        const searchLower = filter.search.toLowerCase();
        tags = tags.filter(tag => 
          tag.name.toLowerCase().includes(searchLower) ||
          tag.description?.toLowerCase().includes(searchLower)
        );
      }
    }

    return tags.sort((a, b) => b.usageCount - a.usageCount);
  }

  updateTagStatus(tagId: string, status: EnhancedFileTag['status']): void {
    const tag = this.enhancedTags.get(tagId);
    if (tag) {
      tag.status = status;
      tag.lastModified = new Date();
      this.notifySubscribers();
    }
  }

  getAnalytics(): TagAnalytics {
    return this.analytics;
  }

  private calculateAnalytics(): TagAnalytics {
    const tags = Array.from(this.enhancedTags.values());
    const activeTags = tags.filter(tag => tag.status === 'active');
    
    return {
      totalTags: tags.length,
      activeTags: activeTags.length,
      mostUsedTags: tags
        .sort((a, b) => b.usageCount - a.usageCount)
        .slice(0, 10)
        .map(tag => ({ tag, count: tag.usageCount })),
      recentActivity: [], // Would be populated from actual activity log
      categoryDistribution: tags.reduce((acc, tag) => {
        const category = tag.metadata.category || 'uncategorized';
        acc[category] = (acc[category] || 0) + 1;
        return acc;
      }, {} as { [key: string]: number })
    };
  }

  subscribe(callback: () => void): () => void {
    this.subscribers.add(callback);
    return () => this.subscribers.delete(callback);
  }

  private notifySubscribers(): void {
    this.analytics = this.calculateAnalytics();
    this.subscribers.forEach(callback => callback());
  }
}

// Status indicator component
const StatusIndicator: React.FC<{ status: EnhancedFileTag['status']; size?: 'sm' | 'md' | 'lg' }> = ({ 
  status, 
  size = 'md' 
}) => {
  const sizeClasses = {
    sm: 'w-2 h-2',
    md: 'w-3 h-3',
    lg: 'w-4 h-4'
  };

  const statusConfig = {
    active: { icon: CheckCircle, color: 'text-green-500', bg: 'bg-green-100' },
    pending: { icon: Clock, color: 'text-yellow-500', bg: 'bg-yellow-100' },
    archived: { icon: Archive, color: 'text-gray-500', bg: 'bg-gray-100' },
    deprecated: { icon: AlertCircle, color: 'text-red-500', bg: 'bg-red-100' }
  };

  const config = statusConfig[status];
  const Icon = config.icon;

  return (
    <div className={`inline-flex items-center gap-1 px-2 py-1 rounded-full ${config.bg}`}>
      <Icon className={`${sizeClasses[size]} ${config.color}`} />
      <span className={`text-xs font-medium ${config.color} capitalize`}>
        {status}
      </span>
    </div>
  );
};

// Priority indicator
const PriorityIndicator: React.FC<{ priority: EnhancedFileTag['priority'] }> = ({ priority }) => {
  const priorityConfig = {
    low: { color: 'bg-blue-500', label: 'Low' },
    medium: { color: 'bg-yellow-500', label: 'Med' },
    high: { color: 'bg-orange-500', label: 'High' },
    critical: { color: 'bg-red-500', label: 'Crit' }
  };

  const config = priorityConfig[priority];

  return (
    <div className={`w-2 h-6 ${config.color} rounded-sm`} title={`Priority: ${config.label}`} />
  );
};

// Enhanced tag component
const EnhancedTagComponent: React.FC<{
  tag: EnhancedFileTag;
  onEdit?: (tag: EnhancedFileTag) => void;
  onDelete?: (tagId: string) => void;
  onStatusChange?: (tagId: string, status: EnhancedFileTag['status']) => void;
  showDetails?: boolean;
  interactive?: boolean;
}> = ({ 
  tag, 
  onEdit, 
  onDelete, 
  onStatusChange, 
  showDetails = false, 
  interactive = true 
}) => {
  const [showMenu, setShowMenu] = useState(false);

  return (
    <div className="group relative">
      <div 
        className={`inline-flex items-center gap-2 px-3 py-2 rounded-lg border transition-all duration-200 ${
          interactive ? 'hover:shadow-md cursor-pointer' : ''
        }`}
        style={{ 
          backgroundColor: `${tag.color}15`,
          borderColor: tag.color
        }}
      >
        <PriorityIndicator priority={tag.priority} />
        
        <div className="flex items-center gap-2">
          <Hash className="w-3 h-3" style={{ color: tag.color }} />
          <span className="text-sm font-medium" style={{ color: tag.color }}>
            {tag.name}
          </span>
        </div>

        {showDetails && (
          <div className="flex items-center gap-2 text-xs text-gray-500">
            <span>({tag.usageCount})</span>
            <StatusIndicator status={tag.status} size="sm" />
          </div>
        )}

        {interactive && (
          <div className="relative">
            <button
              onClick={(e) => {
                e.stopPropagation();
                setShowMenu(!showMenu);
              }}
              className="opacity-0 group-hover:opacity-100 transition-opacity p-1 hover:bg-gray-100 rounded"
            >
              <MoreHorizontal className="w-3 h-3" />
            </button>

            {showMenu && (
              <div className="absolute right-0 top-full mt-1 bg-white border rounded-lg shadow-lg z-10 min-w-32">
                <button
                  onClick={() => {
                    onEdit?.(tag);
                    setShowMenu(false);
                  }}
                  className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2"
                >
                  <Edit3 className="w-3 h-3" />
                  Edit
                </button>
                <button
                  onClick={() => {
                    navigator.clipboard.writeText(tag.name);
                    setShowMenu(false);
                  }}
                  className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2"
                >
                  <Copy className="w-3 h-3" />
                  Copy
                </button>
                <hr className="my-1" />
                {(['active', 'pending', 'archived', 'deprecated'] as const).map(status => (
                  <button
                    key={status}
                    onClick={() => {
                      onStatusChange?.(tag.id, status);
                      setShowMenu(false);
                    }}
                    className={`w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2 ${
                      tag.status === status ? 'bg-blue-50 text-blue-600' : ''
                    }`}
                  >
                    <StatusIndicator status={status} size="sm" />
                    {status}
                  </button>
                ))}
                <hr className="my-1" />
                <button
                  onClick={() => {
                    onDelete?.(tag.id);
                    setShowMenu(false);
                  }}
                  className="w-full px-3 py-2 text-left text-sm hover:bg-red-50 text-red-600 flex items-center gap-2"
                >
                  <Trash2 className="w-3 h-3" />
                  Delete
                </button>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Click outside to close menu */}
      {showMenu && (
        <div 
          className="fixed inset-0 z-0" 
          onClick={() => setShowMenu(false)}
        />
      )}
    </div>
  );
};

// Main enhanced file tagging system
export const EnhancedFileTaggingSystem: React.FC = () => {
  const [manager] = useState(() => new EnhancedFileTaggingManager());
  const [tags, setTags] = useState<EnhancedFileTag[]>([]);
  const [filter, setFilter] = useState<TagFilter>({});
  const [analytics, setAnalytics] = useState<TagAnalytics | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingTag, setEditingTag] = useState<EnhancedFileTag | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  useEffect(() => {
    const unsubscribe = manager.subscribe(() => {
      setTags(manager.getEnhancedTags(filter));
      setAnalytics(manager.getAnalytics());
    });

    // Initial load
    setTags(manager.getEnhancedTags(filter));
    setAnalytics(manager.getAnalytics());

    return unsubscribe;
  }, [manager, filter]);

  const handleCreateTag = (tagData: { name: string; color: string; description?: string; status?: EnhancedFileTag['status']; priority?: EnhancedFileTag['priority'] }) => {
    manager.createEnhancedTag(tagData);
    setShowCreateForm(false);
  };

  const handleEditTag = (tag: EnhancedFileTag) => {
    setEditingTag(tag);
  };

  const handleDeleteTag = (tagId: string) => {
    if (confirm('Are you sure you want to delete this tag?')) {
      // Implementation would call manager.deleteTag(tagId)
      console.log('Delete tag:', tagId);
    }
  };

  const handleStatusChange = (tagId: string, status: EnhancedFileTag['status']) => {
    manager.updateTagStatus(tagId, status);
  };

  return (
    <div className="p-6 bg-white rounded-lg border">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Enhanced File Tagging</h2>
          <p className="text-sm text-gray-600 mt-1">
            Manage and organize your files with intelligent tagging system
          </p>
        </div>
        
        <div className="flex items-center gap-3">
          <button
            onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            title={`Switch to ${viewMode === 'grid' ? 'list' : 'grid'} view`}
          >
            {viewMode === 'grid' ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
          </button>
          
          <button
            onClick={() => setShowCreateForm(true)}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Plus className="w-4 h-4" />
            Create Tag
          </button>
        </div>
      </div>

      {/* Analytics Summary */}
      {analytics && (
        <div className="grid grid-cols-4 gap-4 mb-6">
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">{analytics.totalTags}</div>
            <div className="text-sm text-blue-600">Total Tags</div>
          </div>
          <div className="bg-green-50 p-4 rounded-lg">
            <div className="text-2xl font-bold text-green-600">{analytics.activeTags}</div>
            <div className="text-sm text-green-600">Active Tags</div>
          </div>
          <div className="bg-purple-50 p-4 rounded-lg">
            <div className="text-2xl font-bold text-purple-600">
              {Object.keys(analytics.categoryDistribution).length}
            </div>
            <div className="text-sm text-purple-600">Categories</div>
          </div>
          <div className="bg-orange-50 p-4 rounded-lg">
            <div className="text-2xl font-bold text-orange-600">
              {analytics.mostUsedTags[0]?.count || 0}
            </div>
            <div className="text-sm text-orange-600">Most Used</div>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="flex items-center gap-4 mb-6 p-4 bg-gray-50 rounded-lg">
        <div className="flex items-center gap-2">
          <Search className="w-4 h-4 text-gray-500" />
          <input
            type="text"
            placeholder="Search tags..."
            value={filter.search || ''}
            onChange={(e) => setFilter({ ...filter, search: e.target.value })}
            className="px-3 py-2 border rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        
        <div className="flex items-center gap-2">
          <Filter className="w-4 h-4 text-gray-500" />
          <select
            value={filter.status?.[0] || ''}
            onChange={(e) => setFilter({ 
              ...filter, 
              status: e.target.value ? [e.target.value] : undefined 
            })}
            className="px-3 py-2 border rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">All Status</option>
            <option value="active">Active</option>
            <option value="pending">Pending</option>
            <option value="archived">Archived</option>
            <option value="deprecated">Deprecated</option>
          </select>
        </div>
      </div>

      {/* Tags Display */}
      <div className={`${
        viewMode === 'grid' 
          ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4'
          : 'space-y-2'
      }`}>
        {tags.map(tag => (
          <EnhancedTagComponent
            key={tag.id}
            tag={tag}
            onEdit={handleEditTag}
            onDelete={handleDeleteTag}
            onStatusChange={handleStatusChange}
            showDetails={viewMode === 'list'}
            interactive={true}
          />
        ))}
      </div>

      {tags.length === 0 && (
        <div className="text-center py-12 text-gray-500">
          <Tag className="w-12 h-12 mx-auto mb-4 opacity-50" />
          <p>No tags found matching your criteria</p>
          <button
            onClick={() => setShowCreateForm(true)}
            className="mt-2 text-blue-600 hover:text-blue-700"
          >
            Create your first tag
          </button>
        </div>
      )}

      {/* Create/Edit Form Modal would go here */}
      {(showCreateForm || editingTag) && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg w-96 max-w-full">
            <h3 className="text-lg font-semibold mb-4">
              {editingTag ? 'Edit Tag' : 'Create New Tag'}
            </h3>
            {/* Form implementation would go here */}
            <div className="flex justify-end gap-2 mt-6">
              <button
                onClick={() => {
                  setShowCreateForm(false);
                  setEditingTag(null);
                }}
                className="px-4 py-2 text-gray-600 hover:text-gray-800"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  // Handle save
                  setShowCreateForm(false);
                  setEditingTag(null);
                }}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                {editingTag ? 'Update' : 'Create'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EnhancedFileTaggingSystem;
export { EnhancedFileTaggingManager, type EnhancedFileTag, type TagFilter, type TagAnalytics };