import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Book<PERSON>pen,
  Wand2,
  FileText,
  TrendingUp,
  AlertTriangle,
  BarChart3,
  Calendar,
  Brain,
  Sparkles,
  ExternalLink
} from 'lucide-react';
import { DataStorytellingEngine, DataStorytellingManager, type DataInsight, type GeneratedStory } from './DataStorytellingEngine';

interface StorytellingWidgetProps {
  data?: any[];
  className?: string;
  onStoryGenerated?: (story: GeneratedStory) => void;
}

// Sample data generator for demonstration
const generateSampleData = () => {
  const categories = ['Sales', 'Marketing', 'Support', 'Development', 'Operations'];
  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
  
  return months.map((month, index) => ({
    label: month,
    value: Math.floor(Math.random() * 1000) + 500 + (index * 50), // Trending upward with noise
    category: categories[Math.floor(Math.random() * categories.length)],
    timestamp: new Date(2024, index, 1).toISOString(),
    metadata: {
      target: 800 + (index * 60),
      variance: Math.random() * 0.2 - 0.1
    }
  }));
};

export const StorytellingWidget: React.FC<StorytellingWidgetProps> = ({
  data,
  className = '',
  onStoryGenerated
}) => {
  const [manager] = useState(() => DataStorytellingManager.getInstance());
  const [sampleData] = useState(() => data || generateSampleData());
  const [insights, setInsights] = useState<DataInsight[]>([]);
  const [recentStories, setRecentStories] = useState<GeneratedStory[]>([]);
  const [showFullEngine, setShowFullEngine] = useState(false);
  const [isGeneratingQuick, setIsGeneratingQuick] = useState(false);

  useEffect(() => {
    // Generate insights from sample data
    const generatedInsights = manager.generateInsights(sampleData);
    setInsights(generatedInsights);

    // Load recent stories
    manager.loadStories();
    const stories = manager.getStories();
    setRecentStories(stories.slice(-3)); // Show last 3 stories
  }, [manager, sampleData]);

  const handleQuickStory = async () => {
    if (insights.length === 0) return;

    setIsGeneratingQuick(true);
    try {
      // Use executive summary template for quick stories
      const template = {
        id: 'quick-summary',
        name: 'Quick Summary',
        description: 'Brief overview of key findings',
        structure: ['Key Findings', 'Recommendations'],
        targetAudience: 'general' as const,
        tone: 'casual' as const,
        length: 'brief' as const
      };

      await new Promise(resolve => setTimeout(resolve, 1500));
      const story = manager.generateStory(insights, template);
      setRecentStories([story, ...recentStories.slice(0, 2)]);
      onStoryGenerated?.(story);
    } catch (error) {
      console.error('Failed to generate quick story:', error);
    } finally {
      setIsGeneratingQuick(false);
    }
  };

  if (showFullEngine) {
    return (
      <div className={className}>
        <div className="mb-4">
          <Button
            variant="outline"
            onClick={() => setShowFullEngine(false)}
          >
            ← Back to Widget View
          </Button>
        </div>
        <DataStorytellingEngine
          data={sampleData}
          insights={insights}
          onStoryGenerated={onStoryGenerated}
        />
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Main Storytelling Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookOpen className="w-5 h-5" />
            Data Storytelling
          </CardTitle>
          <CardDescription>
            Transform your data into compelling narratives
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Quick Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-3 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{insights.length}</div>
              <div className="text-xs text-blue-600">Insights</div>
            </div>
            <div className="text-center p-3 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{sampleData.length}</div>
              <div className="text-xs text-green-600">Data Points</div>
            </div>
            <div className="text-center p-3 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">{recentStories.length}</div>
              <div className="text-xs text-purple-600">Stories</div>
            </div>
            <div className="text-center p-3 bg-orange-50 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">
                {insights.filter(i => i.impact === 'high').length}
              </div>
              <div className="text-xs text-orange-600">High Impact</div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2">
            <Button
              onClick={handleQuickStory}
              disabled={isGeneratingQuick || insights.length === 0}
              className="flex-1"
            >
              {isGeneratingQuick ? (
                <>
                  <Sparkles className="w-4 h-4 mr-2 animate-pulse" />
                  Generating...
                </>
              ) : (
                <>
                  <Wand2 className="w-4 h-4 mr-2" />
                  Quick Story
                </>
              )}
            </Button>
            <Button
              variant="outline"
              onClick={() => setShowFullEngine(true)}
            >
              <ExternalLink className="w-4 h-4 mr-2" />
              Full Engine
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Insights Preview */}
      {insights.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-sm flex items-center gap-2">
              <Brain className="w-4 h-4" />
              Key Insights
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {insights.slice(0, 3).map(insight => (
                <div key={insight.id} className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
                  <div className="flex-shrink-0 mt-0.5">
                    {insight.type === 'trend' && <TrendingUp className="w-4 h-4 text-blue-500" />}
                    {insight.type === 'anomaly' && <AlertTriangle className="w-4 h-4 text-red-500" />}
                    {insight.type === 'pattern' && <BarChart3 className="w-4 h-4 text-green-500" />}
                    {insight.type === 'seasonal' && <Calendar className="w-4 h-4 text-purple-500" />}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="text-sm font-medium truncate">{insight.title}</h4>
                      <Badge
                        variant={insight.impact === 'high' ? 'destructive' : insight.impact === 'medium' ? 'default' : 'secondary'}
                        className="text-xs"
                      >
                        {insight.impact}
                      </Badge>
                    </div>
                    <p className="text-xs text-gray-600 line-clamp-2">{insight.description}</p>
                    <div className="text-xs text-gray-500 mt-1">
                      {insight.confidence.toFixed(0)}% confidence
                    </div>
                  </div>
                </div>
              ))}
              {insights.length > 3 && (
                <div className="text-center">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowFullEngine(true)}
                  >
                    View {insights.length - 3} more insights
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Recent Stories */}
      {recentStories.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-sm flex items-center gap-2">
              <FileText className="w-4 h-4" />
              Recent Stories
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {recentStories.map(story => (
                <div key={story.id} className="p-3 border rounded-lg hover:bg-gray-50 transition-colors">
                  <div className="flex items-start justify-between mb-2">
                    <h4 className="text-sm font-medium line-clamp-1">{story.title}</h4>
                    <Badge variant="outline" className="text-xs ml-2">
                      {story.template.name}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-4 text-xs text-gray-500">
                    <span>{story.metadata.wordCount} words</span>
                    <span>{story.metadata.readingTime} min read</span>
                    <span>{story.insights.length} insights</span>
                  </div>
                  <div className="mt-2">
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => setShowFullEngine(true)}
                      className="text-xs h-6 px-2"
                    >
                      View Story
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Empty State */}
      {insights.length === 0 && recentStories.length === 0 && (
        <Card>
          <CardContent className="text-center py-12">
            <Brain className="w-12 h-12 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Ready to Create Stories</h3>
            <p className="text-gray-600 mb-4">
              Provide data to generate insights and create compelling narratives
            </p>
            <Button onClick={() => setShowFullEngine(true)}>
              <BookOpen className="w-4 h-4 mr-2" />
              Open Storytelling Engine
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default StorytellingWidget;