import React, { useState, useEffect, useMemo } from 'react';
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  Pie<PERSON>hart, 
  Activity, 
  TrendingUp, 
  Users, 
  Database,
  FileText,
  Clock,
  AlertCircle,
  CheckCircle,
  XCircle,
  MoreHorizontal,
  Download,
  Maximize2,
  Minimize2,
  Settings,
  RefreshCw
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

// Widget Types
export interface DashboardWidget {
  id: string;
  title: string;
  type: 'chart' | 'metric' | 'table' | 'list' | 'custom';
  size: 'small' | 'medium' | 'large' | 'full';
  data: any;
  config: WidgetConfig;
  position?: { x: number; y: number };
  isVisible: boolean;
}

export interface WidgetConfig {
  chartType?: 'bar' | 'line' | 'pie' | 'area';
  colors?: string[];
  showLegend?: boolean;
  showGrid?: boolean;
  dateFormat?: string;
  numberFormat?: string;
  refreshInterval?: number;
  showTrend?: boolean;
  actions?: WidgetAction[];
}

export interface WidgetAction {
  id: string;
  label: string;
  icon: React.ReactNode;
  onClick: () => void;
}

// Chart Data Types
export interface ChartDataPoint {
  label: string;
  value: number;
  color?: string;
  trend?: number; // percentage change
}

export interface TimeSeriesData {
  timestamp: Date;
  value: number;
  category?: string;
}

export interface TableData {
  headers: string[];
  rows: any[][];
  sortable?: boolean;
  filterable?: boolean;
}

// Base Widget Component
export const BaseWidget: React.FC<{
  widget: DashboardWidget;
  children: React.ReactNode;
  onResize?: (size: DashboardWidget['size']) => void;
  onMove?: (position: { x: number; y: number }) => void;
  onRemove?: () => void;
  onRefresh?: () => void;
}> = ({ widget, children, onResize, onMove, onRemove, onRefresh }) => {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isDragging, setIsDragging] = useState(false);

  return (
    <motion.div
      layout
      className={`bg-card border rounded-lg overflow-hidden ${
        isFullscreen ? 'fixed inset-0 z-50 m-0' : ''
      }`}
      drag={isDragging}
      onDragEnd={(_, info) => {
        onMove?.({ x: info.point.x, y: info.point.y });
        setIsDragging(false);
      }}
    >
      {/* Widget Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-2">
          <h3 className="font-semibold text-sm">{widget.title}</h3>
          {widget.config.showTrend && widget.data.trend && (
            <span className={`text-xs flex items-center gap-1 ${
              widget.data.trend > 0 ? 'text-green-600' : 'text-red-600'
            }`}>
              {widget.data.trend > 0 ? (
                <TrendingUp className="w-3 h-3" />
              ) : (
                <TrendingUp className="w-3 h-3 rotate-180" />
              )}
              {Math.abs(widget.data.trend)}%
            </span>
          )}
        </div>
        
        <div className="flex items-center gap-1">
          {widget.config.actions?.map((action) => (
            <button
              key={action.id}
              onClick={action.onClick}
              className="p-1 hover:bg-muted rounded"
              title={action.label}
            >
              {action.icon}
            </button>
          ))}
          {onRefresh && (
            <button
              onClick={onRefresh}
              className="p-1 hover:bg-muted rounded"
              title="Refresh"
            >
              <RefreshCw className="w-3 h-3" />
            </button>
          )}
          <button
            onClick={() => setIsFullscreen(!isFullscreen)}
            className="p-1 hover:bg-muted rounded"
            title={isFullscreen ? "Exit fullscreen" : "Fullscreen"}
          >
            {isFullscreen ? (
              <Minimize2 className="w-3 h-3" />
            ) : (
              <Maximize2 className="w-3 h-3" />
            )}
          </button>
          <button
            onClick={onRemove}
            className="p-1 hover:bg-muted rounded"
            title="Remove widget"
          >
            <XCircle className="w-3 h-3" />
          </button>
        </div>
      </div>

      {/* Widget Content */}
      <div className={`p-4 ${isFullscreen ? 'h-[calc(100vh-60px)]' : ''}`}>
        {children}
      </div>
    </motion.div>
  );
};

// Chart Widget Component
export const ChartWidget: React.FC<{
  widget: DashboardWidget & { type: 'chart' };
  onDataPointClick?: (point: ChartDataPoint) => void;
}> = ({ widget, onDataPointClick }) => {
  const { data, config } = widget;
  
  const renderChart = () => {
    switch (config.chartType) {
      case 'bar':
        return <BarChartComponent data={data.points} onPointClick={onDataPointClick} />;
      case 'line':
        return <LineChartComponent data={data.series} />;
      case 'pie':
        return <PieChartComponent data={data.segments} onSegmentClick={onDataPointClick} />;
      case 'area':
        return <AreaChartComponent data={data.series} />;
      default:
        return <BarChartComponent data={data.points} onPointClick={onDataPointClick} />;
    }
  };

  return (
    <BaseWidget widget={widget}>
      <div className="h-full">
        {renderChart()}
      </div>
    </BaseWidget>
  );
};

// Bar Chart Component
const BarChartComponent: React.FC<{
  data: ChartDataPoint[];
  onPointClick?: (point: ChartDataPoint) => void;
}> = ({ data, onPointClick }) => {
  const maxValue = Math.max(...data.map(d => d.value), 0);
  
  return (
    <div className="h-full flex flex-col">
      <div className="flex-1 flex items-end justify-between gap-2 pb-4">
        {data.map((point, index) => (
          <div key={index} className="flex flex-col items-center flex-1">
            <div className="relative w-full flex flex-col items-center">
              <motion.div
                initial={{ height: 0 }}
                animate={{ height: `${(point.value / maxValue) * 100}%` }}
                transition={{ delay: index * 0.1 }}
                className="w-3/4 bg-primary rounded-t cursor-pointer hover:opacity-80"
                style={{ backgroundColor: point.color }}
                onClick={() => onPointClick?.(point)}
              />
              {point.trend && (
                <div className={`absolute -top-6 text-xs flex items-center gap-1 ${
                  point.trend > 0 ? 'text-green-600' : 'text-red-600'
                }`}>
                  {point.trend > 0 ? '↑' : '↓'}{Math.abs(point.trend)}%
                </div>
              )}
            </div>
            <div className="mt-2 text-xs text-center text-muted-foreground truncate w-full">
              {point.label}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Line Chart Component
const LineChartComponent: React.FC<{
  data: TimeSeriesData[];
}> = ({ data }) => {
  const [hoveredPoint, setHoveredPoint] = useState<number | null>(null);
  
  // Simplified line chart rendering
  return (
    <div className="h-full relative">
      <svg className="w-full h-full" viewBox="0 0 400 200">
        {/* Grid lines */}
        <line x1="0" y1="180" x2="400" y2="180" stroke="#e5e7eb" strokeWidth="1" />
        <line x1="0" y1="120" x2="400" y2="120" stroke="#e5e7eb" strokeWidth="1" />
        <line x1="0" y1="60" x2="400" y2="60" stroke="#e5e7eb" strokeWidth="1" />
        
        {/* Data line */}
        <polyline
          fill="none"
          stroke="#3b82f6"
          strokeWidth="2"
          points={data.map((point, index) => 
            `${(index / (data.length - 1)) * 380 + 10},${180 - (point.value / Math.max(...data.map(d => d.value)) * 160)}`
          ).join(' ')}
        />
        
        {/* Data points */}
        {data.map((point, index) => (
          <circle
            key={index}
            cx={(index / (data.length - 1)) * 380 + 10}
            cy={180 - (point.value / Math.max(...data.map(d => d.value)) * 160)}
            r="4"
            fill="#3b82f6"
            className="cursor-pointer hover:r-6 transition-all"
            onMouseEnter={() => setHoveredPoint(index)}
            onMouseLeave={() => setHoveredPoint(null)}
          />
        ))}
      </svg>
      
      {/* Tooltip */}
      <AnimatePresence>
        {hoveredPoint !== null && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 10 }}
            className="absolute p-2 bg-card border rounded shadow-lg text-xs"
            style={{
              left: `${(hoveredPoint / (data.length - 1)) * 380 + 10}px`,
              top: `${180 - (data[hoveredPoint].value / Math.max(...data.map(d => d.value)) * 160) - 40}px`,
              transform: 'translateX(-50%)'
            }}
          >
            <div className="font-medium">{data[hoveredPoint].value}</div>
            <div className="text-muted-foreground">
              {data[hoveredPoint].timestamp.toLocaleDateString()}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

// Pie Chart Component
const PieChartComponent: React.FC<{
  data: ChartDataPoint[];
  onSegmentClick?: (point: ChartDataPoint) => void;
}> = ({ data, onSegmentClick }) => {
  const total = data.reduce((sum, point) => sum + point.value, 0);
  let currentAngle = 0;
  
  return (
    <div className="h-full flex items-center justify-center">
      <svg className="w-48 h-48" viewBox="0 0 200 200">
        {data.map((point, index) => {
          const percentage = (point.value / total) * 100;
          const angle = (percentage / 100) * 360;
          const startAngle = currentAngle;
          const endAngle = currentAngle + angle;
          currentAngle = endAngle;
          
          // Convert angles to radians
          const startRad = (startAngle * Math.PI) / 180;
          const endRad = (endAngle * Math.PI) / 180;
          
          // Calculate coordinates
          const x1 = 100 + 80 * Math.cos(startRad);
          const y1 = 100 + 80 * Math.sin(startRad);
          const x2 = 100 + 80 * Math.cos(endRad);
          const y2 = 100 + 80 * Math.sin(endRad);
          
          // Large arc flag
          const largeArcFlag = angle > 180 ? 1 : 0;
          
          const pathData = [
            `M 100 100`,
            `L ${x1} ${y1}`,
            `A 80 80 0 ${largeArcFlag} 1 ${x2} ${y2}`,
            'Z'
          ].join(' ');
          
          return (
            <path
              key={index}
              d={pathData}
              fill={point.color || `hsl(${index * 30}, 70%, 50%)`}
              className="cursor-pointer hover:opacity-80 transition-opacity"
              onClick={() => onSegmentClick?.(point)}
            />
          );
        })}
        
        {/* Center circle */}
        <circle cx="100" cy="100" r="30" fill="white" />
        <text x="100" y="105" textAnchor="middle" className="text-sm font-medium">
          {total}
        </text>
      </svg>
      
      {/* Legend */}
      <div className="ml-4 space-y-2">
        {data.map((point, index) => (
          <div key={index} className="flex items-center gap-2 text-sm">
            <div 
              className="w-3 h-3 rounded-full" 
              style={{ backgroundColor: point.color || `hsl(${index * 30}, 70%, 50%)` }}
            />
            <span className="truncate max-w-24">{point.label}</span>
            <span className="text-muted-foreground ml-auto">{point.value}</span>
          </div>
        ))}
      </div>
    </div>
  );
};

// Area Chart Component
const AreaChartComponent: React.FC<{
  data: TimeSeriesData[];
}> = ({ data }) => {
  const maxValue = Math.max(...data.map(d => d.value), 1);
  
  return (
    <div className="h-full">
      <svg className="w-full h-full" viewBox="0 0 400 200">
        {/* Grid */}
        <line x1="0" y1="180" x2="400" y2="180" stroke="#e5e7eb" strokeWidth="1" />
        <line x1="0" y1="120" x2="400" y2="120" stroke="#e5e7eb" strokeWidth="1" />
        <line x1="0" y1="60" x2="400" y2="60" stroke="#e5e7eb" strokeWidth="1" />
        
        {/* Area */}
        <polygon
          fill="url(#areaGradient)"
          stroke="none"
          points={[
            '10,180',
            ...data.map((point, index) => 
              `${(index / (data.length - 1)) * 380 + 10},${180 - (point.value / maxValue * 160)}`
            ),
            `${390},180`
          ].join(' ')}
        />
        
        {/* Line */}
        <polyline
          fill="none"
          stroke="#3b82f6"
          strokeWidth="2"
          points={data.map((point, index) => 
            `${(index / (data.length - 1)) * 380 + 10},${180 - (point.value / maxValue * 160)}`
          ).join(' ')}
        />
        
        <defs>
          <linearGradient id="areaGradient" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" stopColor="#3b82f6" stopOpacity="0.3" />
            <stop offset="100%" stopColor="#3b82f6" stopOpacity="0.05" />
          </linearGradient>
        </defs>
      </svg>
    </div>
  );
};

// Metric Widget Component
export const MetricWidget: React.FC<{
  widget: DashboardWidget & { type: 'metric' };
}> = ({ widget }) => {
  const { data } = widget;
  
  return (
    <BaseWidget widget={widget}>
      <div className="h-full flex flex-col items-center justify-center text-center">
        <div className="text-3xl font-bold mb-2">{data.value}</div>
        {data.subtitle && (
          <div className="text-sm text-muted-foreground mb-4">{data.subtitle}</div>
        )}
        {data.trend && (
          <div className={`flex items-center gap-1 text-sm ${
            data.trend > 0 ? 'text-green-600' : 'text-red-600'
          }`}>
            {data.trend > 0 ? (
              <TrendingUp className="w-4 h-4" />
            ) : (
              <TrendingUp className="w-4 h-4 rotate-180" />
            )}
            {Math.abs(data.trend)}% from last period
          </div>
        )}
      </div>
    </BaseWidget>
  );
};

// Table Widget Component
export const TableWidget: React.FC<{
  widget: DashboardWidget & { type: 'table' };
  onRowClick?: (row: any[]) => void;
}> = ({ widget, onRowClick }) => {
  const { data } = widget;
  
  return (
    <BaseWidget widget={widget}>
      <div className="h-full overflow-auto">
        <table className="w-full text-sm">
          <thead>
            <tr className="border-b">
              {data.headers.map((header: string, index: number) => (
                <th key={index} className="px-3 py-2 text-left font-medium text-muted-foreground">
                  {header}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {data.rows.map((row: any[], rowIndex: number) => (
              <tr 
                key={rowIndex} 
                className="border-b hover:bg-muted/30 cursor-pointer"
                onClick={() => onRowClick?.(row)}
              >
                {row.map((cell: any, cellIndex: number) => (
                  <td key={cellIndex} className="px-3 py-2">
                    {cell}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </BaseWidget>
  );
};

// List Widget Component
export const ListWidget: React.FC<{
  widget: DashboardWidget & { type: 'list' };
  onItemClick?: (item: any) => void;
}> = ({ widget, onItemClick }) => {
  const { data } = widget;
  
  return (
    <BaseWidget widget={widget}>
      <div className="h-full space-y-3">
        {data.items.map((item: any, index: number) => (
          <div
            key={index}
            className="flex items-center justify-between p-3 bg-muted/30 rounded-lg hover:bg-muted/50 cursor-pointer transition-colors"
            onClick={() => onItemClick?.(item)}
          >
            <div className="flex items-center gap-3">
              {item.icon && <div className="text-primary">{item.icon}</div>}
              <div>
                <div className="font-medium text-sm">{item.title}</div>
                {item.subtitle && (
                  <div className="text-xs text-muted-foreground">{item.subtitle}</div>
                )}
              </div>
            </div>
            {item.value && (
              <div className="text-sm font-medium">{item.value}</div>
            )}
          </div>
        ))}
      </div>
    </BaseWidget>
  );
};

// Dashboard Layout Manager
export class DashboardLayoutManager {
  private widgets: DashboardWidget[] = [];
  private layout: Record<string, { x: number; y: number; width: number; height: number }> = {};
  
  addWidget(widget: DashboardWidget): void {
    this.widgets.push(widget);
    this.updateLayout();
  }
  
  removeWidget(widgetId: string): void {
    this.widgets = this.widgets.filter(w => w.id !== widgetId);
    delete this.layout[widgetId];
  }
  
  updateWidget(widgetId: string, updates: Partial<DashboardWidget>): void {
    const widget = this.widgets.find(w => w.id === widgetId);
    if (widget) {
      Object.assign(widget, updates);
    }
  }
  
  getWidgets(): DashboardWidget[] {
    return [...this.widgets];
  }
  
  getLayout(): Record<string, { x: number; y: number; width: number; height: number }> {
    return { ...this.layout };
  }
  
  private updateLayout(): void {
    // Simple grid layout algorithm
    const grid: boolean[][] = Array(12).fill(null).map(() => Array(12).fill(false));
    let currentRow = 0;
    
    this.widgets.forEach((widget, index) => {
      const { size } = widget;
      let width = 4, height = 3;
      
      switch (size) {
        case 'small': width = 3; height = 2; break;
        case 'medium': width = 4; height = 3; break;
        case 'large': width = 6; height = 4; break;
        case 'full': width = 12; height = 5; break;
      }
      
      // Find available space
      let placed = false;
      for (let row = currentRow; row < 12; row++) {
        for (let col = 0; col <= 12 - width; col++) {
          if (this.canPlaceWidget(grid, row, col, width, height)) {
            this.placeWidget(grid, row, col, width, height);
            this.layout[widget.id] = { x: col, y: row, width, height };
            placed = true;
            break;
          }
        }
        if (placed) break;
      }
      
      if (!placed) {
        // Start new row
        currentRow = Math.max(...Object.values(this.layout).map(pos => pos.y + pos.height), 0);
        this.layout[widget.id] = { x: 0, y: currentRow, width, height };
      }
    });
  }
  
  private canPlaceWidget(grid: boolean[][], row: number, col: number, width: number, height: number): boolean {
    for (let r = row; r < row + height && r < 12; r++) {
      for (let c = col; c < col + width && c < 12; c++) {
        if (grid[r][c]) return false;
      }
    }
    return true;
  }
  
  private placeWidget(grid: boolean[][], row: number, col: number, width: number, height: number): void {
    for (let r = row; r < row + height && r < 12; r++) {
      for (let c = col; c < col + width && c < 12; c++) {
        grid[r][c] = true;
      }
    }
  }
  
  saveLayout(): void {
    try {
      localStorage.setItem('dashboard_layout_v1', JSON.stringify({
        widgets: this.widgets,
        layout: this.layout
      }));
    } catch (error) {
      console.warn('Failed to save dashboard layout:', error);
    }
  }
  
  loadLayout(): void {
    try {
      const saved = localStorage.getItem('dashboard_layout_v1');
      if (saved) {
        const { widgets, layout } = JSON.parse(saved);
        this.widgets = widgets;
        this.layout = layout;
      }
    } catch (error) {
      console.warn('Failed to load dashboard layout:', error);
    }
  }
}

// React Hook for Dashboard Management
export const useDashboard = () => {
  const [layoutManager] = useState(() => {
    const manager = new DashboardLayoutManager();
    manager.loadLayout();
    return manager;
  });
  
  const [widgets, setWidgets] = useState<DashboardWidget[]>(layoutManager.getWidgets());
  
  const addWidget = (widget: DashboardWidget) => {
    layoutManager.addWidget(widget);
    setWidgets(layoutManager.getWidgets());
    layoutManager.saveLayout();
  };
  
  const removeWidget = (widgetId: string) => {
    layoutManager.removeWidget(widgetId);
    setWidgets(layoutManager.getWidgets());
    layoutManager.saveLayout();
  };
  
  const updateWidget = (widgetId: string, updates: Partial<DashboardWidget>) => {
    layoutManager.updateWidget(widgetId, updates);
    setWidgets(layoutManager.getWidgets());
    layoutManager.saveLayout();
  };
  
  return {
    widgets,
    addWidget,
    removeWidget,
    updateWidget,
    layoutManager
  };
};

// Predefined Widget Templates
export const WIDGET_TEMPLATES = {
  dataFlow: {
    id: 'data-flow',
    title: 'Data Flow Pipeline',
    type: 'chart' as const,
    size: 'medium' as const,
    data: {
      points: [
        { label: 'Upload', value: 1247, color: '#3b82f6', trend: 12 },
        { label: 'Preview', value: 1247, color: '#10b981', trend: 8 },
        { label: 'Process', value: 892, color: '#f59e0b', trend: -5 },
        { label: 'Analyze', value: 634, color: '#8b5cf6', trend: 15 },
        { label: 'Export', value: 421, color: '#ef4444', trend: 3 }
      ]
    },
    config: {
      chartType: 'bar' as const,
      showTrend: true,
      actions: [
        { 
          id: 'refresh', 
          label: 'Refresh', 
          icon: <RefreshCw className="w-3 h-3" />,
          onClick: () => console.log('Refresh data flow')
        }
      ]
    },
    isVisible: true
  },
  
  storageUsage: {
    id: 'storage-usage',
    title: 'Storage Usage',
    type: 'chart' as const,
    size: 'small' as const,
    data: {
      segments: [
        { label: 'Documents', value: 45, color: '#3b82f6' },
        { label: 'Data Files', value: 30, color: '#10b981' },
        { label: 'Reports', value: 15, color: '#f59e0b' },
        { label: 'Free', value: 10, color: '#9ca3af' }
      ]
    },
    config: {
      chartType: 'pie' as const
    },
    isVisible: true
  },
  
  activeUsers: {
    id: 'active-users',
    title: 'Active Users',
    type: 'metric' as const,
    size: 'small' as const,
    data: {
      value: '24',
      subtitle: 'Currently Online',
      trend: 12
    },
    config: {
      showTrend: true
    },
    isVisible: true
  },
  
  processingQueue: {
    id: 'processing-queue',
    title: 'Processing Queue',
    type: 'list' as const,
    size: 'medium' as const,
    data: {
      items: [
        { 
          title: 'Sales Data Q4', 
          subtitle: 'Processing', 
          value: '65%', 
          icon: <Database className="w-4 h-4" /> 
        },
        { 
          title: 'Customer Reports', 
          subtitle: 'Queued', 
          value: 'Waiting', 
          icon: <FileText className="w-4 h-4" /> 
        },
        { 
          title: 'Financial Analysis', 
          subtitle: 'Completed', 
          value: '100%', 
          icon: <CheckCircle className="w-4 h-4 text-green-500" /> 
        }
      ]
    },
    config: {},
    isVisible: true
  },
  
  performanceMetrics: {
    id: 'performance-metrics',
    title: 'Performance Metrics',
    type: 'table' as const,
    size: 'large' as const,
    data: {
      headers: ['Metric', 'Current', 'Target', 'Status'],
      rows: [
        ['Throughput', '2.4k/s', '3.0k/s', 'Good'],
        ['Latency', '45ms', '50ms', 'Excellent'],
        ['Error Rate', '0.1%', '0.5%', 'Good'],
        ['Uptime', '99.9%', '99.5%', 'Excellent']
      ]
    },
    config: {
      sortable: true
    },
    isVisible: true
  }
};