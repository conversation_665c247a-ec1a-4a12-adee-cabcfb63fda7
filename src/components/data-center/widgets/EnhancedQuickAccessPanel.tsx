import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { 
  Clock, 
  Star, 
  File, 
  FileText, 
  FileSpreadsheet, 
  FileImage,
  Bookmark,
  Search,
  Filter,
  Calendar,
  TrendingUp,
  Pin,
  Archive,
  Download,
  Share2,
  MoreHorizontal,
  Grid,
  List,
  SortAsc,
  SortDesc,
  RefreshCw,
  Plus,
  Folder,
  Tag,
  Eye,
  Edit,
  Trash2,
  Copy,
  ExternalLink,
  History,
  Zap,
  Heart,
  Users,
  Activity
} from 'lucide-react';
import { StorageFile } from '@/lib/services/browserStorageManager';
import { fileTaggingManager } from '../utils/fileTagging';
import storageManager from '@/lib/services/browserStorageManager';

interface EnhancedQuickAccessPanelProps {
  onFileSelect?: (file: StorageFile) => void;
  onTemplateSelect?: (templateId: string) => void;
  onCreateTemplate?: () => void;
  onFileAction?: (action: string, file: StorageFile) => void;
  maxRecentFiles?: number;
  maxFavoriteTemplates?: number;
  showSearch?: boolean;
  showFilters?: boolean;
  showStats?: boolean;
  compactMode?: boolean;
}

interface EnhancedRecentFile extends StorageFile {
  accessedAt: Date;
  accessCount: number;
  isPinned: boolean;
  lastAction: 'viewed' | 'edited' | 'shared' | 'downloaded';
  collaborators?: string[];
  processingStatus?: 'idle' | 'processing' | 'completed' | 'error';
}

interface EnhancedFavoriteTemplate {
  id: string;
  name: string;
  description?: string;
  category: string;
  usedCount: number;
  lastUsed: Date;
  createdBy: string;
  tags: string[];
  rating: number;
  isPublic: boolean;
  thumbnail?: string;
  estimatedTime?: number; // in minutes
  complexity: 'beginner' | 'intermediate' | 'advanced';
}

interface QuickAccessStats {
  totalFiles: number;
  recentFiles: number;
  favoriteTemplates: number;
  pinnedFiles: number;
  sharedFiles: number;
  processingFiles: number;
  avgAccessTime: number;
  mostUsedCategory: string;
}

type ViewMode = 'grid' | 'list' | 'compact';
type SortBy = 'name' | 'date' | 'usage' | 'rating' | 'category';
type SortOrder = 'asc' | 'desc';
type FilterType = 'all' | 'recent' | 'favorites' | 'pinned' | 'shared' | 'processing';

// Enhanced Quick Access Manager
class EnhancedQuickAccessManager {
  private recentFiles: EnhancedRecentFile[] = [];
  private favoriteTemplates: EnhancedFavoriteTemplate[] = [];
  private stats: QuickAccessStats;
  private subscribers: Set<() => void> = new Set();

  constructor() {
    this.stats = this.calculateStats();
    this.loadData();
  }

  async loadData(): Promise<void> {
    try {
      // Load recent files with enhanced metadata
      const allFiles = await storageManager.listFiles();
      this.recentFiles = allFiles.map(file => ({
        ...file,
        accessedAt: new Date(file.modifiedAt),
        accessCount: Math.floor(Math.random() * 20) + 1,
        isPinned: Math.random() > 0.8,
        lastAction: ['viewed', 'edited', 'shared', 'downloaded'][Math.floor(Math.random() * 4)] as any,
        collaborators: Math.random() > 0.7 ? ['user1', 'user2'] : undefined,
        processingStatus: ['idle', 'processing', 'completed', 'error'][Math.floor(Math.random() * 4)] as any
      })).sort((a, b) => b.accessedAt.getTime() - a.accessedAt.getTime());

      // Load favorite templates with enhanced metadata
      const favoriteTag = fileTaggingManager.getTagById('favorite');
      if (favoriteTag) {
        const favoriteFileIds = fileTaggingManager.getFilesWithTag('favorite');
        const favoriteFiles = allFiles.filter(file => favoriteFileIds.includes(file.id));
        
        this.favoriteTemplates = favoriteFiles.map(file => ({
          id: file.id,
          name: file.name,
          description: `Template for ${file.category} processing`,
          category: file.category,
          usedCount: Math.floor(Math.random() * 100) + 1,
          lastUsed: new Date(file.modifiedAt),
          createdBy: 'user',
          tags: fileTaggingManager.getTagsForFile(file.id).map(tag => tag.name),
          rating: Math.floor(Math.random() * 5) + 1,
          isPublic: Math.random() > 0.6,
          estimatedTime: Math.floor(Math.random() * 60) + 5,
          complexity: ['beginner', 'intermediate', 'advanced'][Math.floor(Math.random() * 3)] as any
        }));
      }

      this.stats = this.calculateStats();
      this.notifySubscribers();
    } catch (error) {
    }
  }

  getRecentFiles(filter?: FilterType, sortBy?: SortBy, sortOrder?: SortOrder, limit?: number): EnhancedRecentFile[] {
    let files = [...this.recentFiles];

    // Apply filters
    if (filter && filter !== 'all') {
      switch (filter) {
        case 'pinned':
          files = files.filter(f => f.isPinned);
          break;
        case 'shared':
          files = files.filter(f => f.collaborators && f.collaborators.length > 0);
          break;
        case 'processing':
          files = files.filter(f => f.processingStatus === 'processing');
          break;
        case 'favorites':
          const favoriteIds = fileTaggingManager.getFilesWithTag('favorite');
          files = files.filter(f => favoriteIds.includes(f.id));
          break;
      }
    }

    // Apply sorting
    if (sortBy) {
      files.sort((a, b) => {
        let comparison = 0;
        switch (sortBy) {
          case 'name':
            comparison = a.name.localeCompare(b.name);
            break;
          case 'date':
            comparison = a.accessedAt.getTime() - b.accessedAt.getTime();
            break;
          case 'usage':
            comparison = a.accessCount - b.accessCount;
            break;
          case 'category':
            comparison = a.category.localeCompare(b.category);
            break;
        }
        return sortOrder === 'desc' ? -comparison : comparison;
      });
    }

    return limit ? files.slice(0, limit) : files;
  }

  getFavoriteTemplates(sortBy?: SortBy, sortOrder?: SortOrder, limit?: number): EnhancedFavoriteTemplate[] {
    const templates = [...this.favoriteTemplates];

    if (sortBy) {
      templates.sort((a, b) => {
        let comparison = 0;
        switch (sortBy) {
          case 'name':
            comparison = a.name.localeCompare(b.name);
            break;
          case 'date':
            comparison = a.lastUsed.getTime() - b.lastUsed.getTime();
            break;
          case 'usage':
            comparison = a.usedCount - b.usedCount;
            break;
          case 'rating':
            comparison = a.rating - b.rating;
            break;
          case 'category':
            comparison = a.category.localeCompare(b.category);
            break;
        }
        return sortOrder === 'desc' ? -comparison : comparison;
      });
    }

    return limit ? templates.slice(0, limit) : templates;
  }

  togglePin(fileId: string): void {
    const file = this.recentFiles.find(f => f.id === fileId);
    if (file) {
      file.isPinned = !file.isPinned;
      this.notifySubscribers();
    }
  }

  updateFileAccess(fileId: string, action: EnhancedRecentFile['lastAction']): void {
    const file = this.recentFiles.find(f => f.id === fileId);
    if (file) {
      file.accessedAt = new Date();
      file.accessCount += 1;
      file.lastAction = action;
      this.notifySubscribers();
    }
  }

  getStats(): QuickAccessStats {
    return this.stats;
  }

  private calculateStats(): QuickAccessStats {
    const totalFiles = this.recentFiles.length;
    const pinnedFiles = this.recentFiles.filter(f => f.isPinned).length;
    const sharedFiles = this.recentFiles.filter(f => f.collaborators?.length).length;
    const processingFiles = this.recentFiles.filter(f => f.processingStatus === 'processing').length;
    
    const categoryCount = this.recentFiles.reduce((acc, file) => {
      acc[file.category] = (acc[file.category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    const mostUsedCategory = Object.entries(categoryCount)
      .sort(([,a], [,b]) => b - a)[0]?.[0] || 'none';
    
    const avgAccessTime = this.recentFiles.length > 0 
      ? this.recentFiles.reduce((sum, f) => sum + f.accessCount, 0) / this.recentFiles.length 
      : 0;

    return {
      totalFiles,
      recentFiles: this.recentFiles.length,
      favoriteTemplates: this.favoriteTemplates.length,
      pinnedFiles,
      sharedFiles,
      processingFiles,
      avgAccessTime,
      mostUsedCategory
    };
  }

  subscribe(callback: () => void): () => void {
    this.subscribers.add(callback);
    return () => this.subscribers.delete(callback);
  }

  private notifySubscribers(): void {
    this.stats = this.calculateStats();
    this.subscribers.forEach(callback => callback());
  }
}

// File item component
const FileItem: React.FC<{
  file: EnhancedRecentFile;
  viewMode: ViewMode;
  onSelect: (file: StorageFile) => void;
  onAction: (action: string, file: StorageFile) => void;
  onTogglePin: (fileId: string) => void;
}> = ({ file, viewMode, onSelect, onAction, onTogglePin }) => {
  const [showMenu, setShowMenu] = useState(false);

  const getFileIcon = (type: string) => {
    if (type.includes('json')) return File;
    if (type.includes('sheet') || type.includes('csv')) return FileSpreadsheet;
    if (type.includes('image')) return FileImage;
    if (type.includes('pdf') || type.includes('doc')) return FileText;
    return File;
  };

  const getStatusColor = (status: EnhancedRecentFile['processingStatus']) => {
    switch (status) {
      case 'processing': return 'text-blue-500 bg-blue-100';
      case 'completed': return 'text-green-500 bg-green-100';
      case 'error': return 'text-red-500 bg-red-100';
      default: return 'text-gray-500 bg-gray-100';
    }
  };

  const getActionIcon = (action: EnhancedRecentFile['lastAction']) => {
    switch (action) {
      case 'viewed': return Eye;
      case 'edited': return Edit;
      case 'shared': return Share2;
      case 'downloaded': return Download;
      default: return Activity;
    }
  };

  const Icon = getFileIcon(file.type);
  const ActionIcon = getActionIcon(file.lastAction);

  if (viewMode === 'compact') {
    return (
      <div className="flex items-center gap-2 p-2 hover:bg-gray-50 rounded-lg cursor-pointer group"
           onClick={() => onSelect(file)}>
        <Icon className="w-4 h-4 text-gray-500 flex-shrink-0" />
        <span className="text-sm truncate flex-1">{file.name}</span>
        {file.isPinned && <Pin className="w-3 h-3 text-blue-500" />}
        <ActionIcon className="w-3 h-3 text-gray-400 opacity-0 group-hover:opacity-100" />
      </div>
    );
  }

  return (
    <div className={`relative group ${
      viewMode === 'grid' 
        ? 'p-4 border rounded-lg hover:shadow-md transition-shadow'
        : 'flex items-center gap-3 p-3 hover:bg-gray-50 rounded-lg'
    }`}>
      <div className={`flex items-center gap-3 ${
        viewMode === 'grid' ? 'flex-col text-center' : 'flex-1 cursor-pointer'
      }`} onClick={() => onSelect(file)}>
        <div className="relative">
          <Icon className={`${viewMode === 'grid' ? 'w-8 h-8' : 'w-5 h-5'} text-gray-500`} />
          {file.processingStatus !== 'idle' && (
            <div className={`absolute -top-1 -right-1 w-3 h-3 rounded-full ${
              getStatusColor(file.processingStatus)
            }`} />
          )}
        </div>
        
        <div className={viewMode === 'grid' ? 'w-full' : 'flex-1 min-w-0'}>
          <div className="flex items-center gap-2 mb-1">
            <p className={`font-medium truncate ${
              viewMode === 'grid' ? 'text-sm' : 'text-sm'
            }`}>
              {file.name}
            </p>
            {file.isPinned && (
              <Pin className="w-3 h-3 text-blue-500 flex-shrink-0" />
            )}
          </div>
          
          <div className={`flex items-center gap-2 text-xs text-gray-500 ${
            viewMode === 'grid' ? 'justify-center flex-wrap' : ''
          }`}>
            <span className="px-2 py-1 bg-gray-100 rounded">{file.category}</span>
            <span>{file.accessCount} uses</span>
            {file.collaborators && (
              <span className="flex items-center gap-1">
                <Users className="w-3 h-3" />
                {file.collaborators.length}
              </span>
            )}
          </div>
          
          {viewMode === 'list' && (
            <div className="flex items-center gap-2 mt-1 text-xs text-gray-400">
              <ActionIcon className="w-3 h-3" />
              <span>Last {file.lastAction}</span>
              <span>•</span>
              <span>{new Date(file.accessedAt).toLocaleDateString()}</span>
            </div>
          )}
        </div>
      </div>

      {/* Action menu */}
      <div className="relative">
        <button
          onClick={(e) => {
            e.stopPropagation();
            setShowMenu(!showMenu);
          }}
          className="opacity-0 group-hover:opacity-100 p-1 hover:bg-gray-200 rounded transition-opacity"
        >
          <MoreHorizontal className="w-4 h-4" />
        </button>

        {showMenu && (
          <div className="absolute right-0 top-full mt-1 bg-white border rounded-lg shadow-lg z-10 min-w-40">
            <button
              onClick={() => {
                onTogglePin(file.id);
                setShowMenu(false);
              }}
              className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2"
            >
              <Pin className="w-3 h-3" />
              {file.isPinned ? 'Unpin' : 'Pin'}
            </button>
            <button
              onClick={() => {
                onAction('share', file);
                setShowMenu(false);
              }}
              className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2"
            >
              <Share2 className="w-3 h-3" />
              Share
            </button>
            <button
              onClick={() => {
                onAction('download', file);
                setShowMenu(false);
              }}
              className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2"
            >
              <Download className="w-3 h-3" />
              Download
            </button>
            <hr className="my-1" />
            <button
              onClick={() => {
                onAction('delete', file);
                setShowMenu(false);
              }}
              className="w-full px-3 py-2 text-left text-sm hover:bg-red-50 text-red-600 flex items-center gap-2"
            >
              <Trash2 className="w-3 h-3" />
              Delete
            </button>
          </div>
        )}
      </div>

      {/* Click outside to close menu */}
      {showMenu && (
        <div className="fixed inset-0 z-0" onClick={() => setShowMenu(false)} />
      )}
    </div>
  );
};

// Template item component
const TemplateItem: React.FC<{
  template: EnhancedFavoriteTemplate;
  viewMode: ViewMode;
  onSelect: (templateId: string) => void;
}> = ({ template, viewMode, onSelect }) => {
  const getComplexityColor = (complexity: EnhancedFavoriteTemplate['complexity']) => {
    switch (complexity) {
      case 'beginner': return 'text-green-600 bg-green-100';
      case 'intermediate': return 'text-yellow-600 bg-yellow-100';
      case 'advanced': return 'text-red-600 bg-red-100';
    }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-3 h-3 ${
          i < rating ? 'text-yellow-500 fill-current' : 'text-gray-300'
        }`}
      />
    ));
  };

  if (viewMode === 'compact') {
    return (
      <div className="flex items-center gap-2 p-2 hover:bg-gray-50 rounded-lg cursor-pointer"
           onClick={() => onSelect(template.id)}>
        <TrendingUp className="w-4 h-4 text-gray-500 flex-shrink-0" />
        <span className="text-sm truncate flex-1">{template.name}</span>
        <div className="flex">{renderStars(template.rating)}</div>
      </div>
    );
  }

  return (
    <div className={`group cursor-pointer ${
      viewMode === 'grid'
        ? 'p-4 border rounded-lg hover:shadow-md transition-shadow'
        : 'flex items-center gap-3 p-3 hover:bg-gray-50 rounded-lg'
    }`} onClick={() => onSelect(template.id)}>
      <div className={`flex items-center gap-3 ${
        viewMode === 'grid' ? 'flex-col text-center w-full' : 'flex-1'
      }`}>
        <div className="relative">
          <TrendingUp className={`${viewMode === 'grid' ? 'w-8 h-8' : 'w-5 h-5'} text-blue-500`} />
          {template.isPublic && (
            <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full" />
          )}
        </div>
        
        <div className={viewMode === 'grid' ? 'w-full' : 'flex-1 min-w-0'}>
          <p className={`font-medium truncate mb-1 ${
            viewMode === 'grid' ? 'text-sm' : 'text-sm'
          }`}>
            {template.name}
          </p>
          
          {template.description && viewMode === 'grid' && (
            <p className="text-xs text-gray-600 mb-2 line-clamp-2">
              {template.description}
            </p>
          )}
          
          <div className={`flex items-center gap-2 text-xs ${
            viewMode === 'grid' ? 'justify-center flex-wrap' : ''
          }`}>
            <span className="px-2 py-1 bg-blue-100 text-blue-600 rounded">
              {template.category}
            </span>
            <span className={`px-2 py-1 rounded ${getComplexityColor(template.complexity)}`}>
              {template.complexity}
            </span>
            {template.estimatedTime && (
              <span className="text-gray-500">
                ~{template.estimatedTime}min
              </span>
            )}
          </div>
          
          <div className={`flex items-center gap-2 mt-2 ${
            viewMode === 'grid' ? 'justify-center' : ''
          }`}>
            <div className="flex">{renderStars(template.rating)}</div>
            <span className="text-xs text-gray-500">({template.usedCount} uses)</span>
          </div>
        </div>
      </div>
    </div>
  );
};

// Main enhanced quick access panel
export const EnhancedQuickAccessPanel: React.FC<EnhancedQuickAccessPanelProps> = ({
  onFileSelect,
  onTemplateSelect,
  onCreateTemplate,
  onFileAction,
  maxRecentFiles = 10,
  maxFavoriteTemplates = 6,
  showSearch = true,
  showFilters = true,
  showStats = true,
  compactMode = false
}) => {
  const [manager] = useState(() => new EnhancedQuickAccessManager());
  const [recentFiles, setRecentFiles] = useState<EnhancedRecentFile[]>([]);
  const [favoriteTemplates, setFavoriteTemplates] = useState<EnhancedFavoriteTemplate[]>([]);
  const [stats, setStats] = useState<QuickAccessStats | null>(null);
  const [loading, setLoading] = useState(true);
  
  // UI state
  const [searchQuery, setSearchQuery] = useState('');
  const [filter, setFilter] = useState<FilterType>('all');
  const [viewMode, setViewMode] = useState<ViewMode>(compactMode ? 'compact' : 'list');
  const [sortBy, setSortBy] = useState<SortBy>('date');
  const [sortOrder, setSortOrder] = useState<SortOrder>('desc');
  const [activeTab, setActiveTab] = useState<'files' | 'templates'>('files');

  useEffect(() => {
    const unsubscribe = manager.subscribe(() => {
      setRecentFiles(manager.getRecentFiles(filter, sortBy, sortOrder, maxRecentFiles));
      setFavoriteTemplates(manager.getFavoriteTemplates(sortBy, sortOrder, maxFavoriteTemplates));
      setStats(manager.getStats());
    });

    // Initial load
    manager.loadData().then(() => {
      setRecentFiles(manager.getRecentFiles(filter, sortBy, sortOrder, maxRecentFiles));
      setFavoriteTemplates(manager.getFavoriteTemplates(sortBy, sortOrder, maxFavoriteTemplates));
      setStats(manager.getStats());
      setLoading(false);
    });

    return unsubscribe;
  }, [manager, filter, sortBy, sortOrder, maxRecentFiles, maxFavoriteTemplates]);

  // Filter files by search query
  const filteredFiles = useMemo(() => {
    if (!searchQuery) return recentFiles;
    return recentFiles.filter(file => 
      file.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      file.category.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [recentFiles, searchQuery]);

  const filteredTemplates = useMemo(() => {
    if (!searchQuery) return favoriteTemplates;
    return favoriteTemplates.filter(template => 
      template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      template.category.toLowerCase().includes(searchQuery.toLowerCase()) ||
      template.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
    );
  }, [favoriteTemplates, searchQuery]);

  const handleFileAction = useCallback((action: string, file: StorageFile) => {
    if (action === 'view') {
      manager.updateFileAccess(file.id, 'viewed');
    }
    onFileAction?.(action, file);
  }, [manager, onFileAction]);

  const handleTogglePin = useCallback((fileId: string) => {
    manager.togglePin(fileId);
  }, [manager]);

  if (loading) {
    return (
      <div className="p-6 text-center">
        <RefreshCw className="w-6 h-6 animate-spin mx-auto mb-2 text-blue-500" />
        <p className="text-sm text-gray-600">Loading quick access...</p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg border">
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <Zap className="w-5 h-5 text-blue-500" />
            <h3 className="font-semibold text-gray-900">Quick Access</h3>
          </div>
          
          <div className="flex items-center gap-2">
            {/* View mode toggle */}
            <div className="flex border rounded-lg">
              <button
                onClick={() => setViewMode('list')}
                className={`p-1.5 rounded-l-lg ${
                  viewMode === 'list' ? 'bg-blue-500 text-white' : 'hover:bg-gray-100'
                }`}
              >
                <List className="w-4 h-4" />
              </button>
              <button
                onClick={() => setViewMode('grid')}
                className={`p-1.5 ${
                  viewMode === 'grid' ? 'bg-blue-500 text-white' : 'hover:bg-gray-100'
                }`}
              >
                <Grid className="w-4 h-4" />
              </button>
              <button
                onClick={() => setViewMode('compact')}
                className={`p-1.5 rounded-r-lg ${
                  viewMode === 'compact' ? 'bg-blue-500 text-white' : 'hover:bg-gray-100'
                }`}
              >
                <Archive className="w-4 h-4" />
              </button>
            </div>
            
            <button
              onClick={() => manager.loadData()}
              className="p-1.5 hover:bg-gray-100 rounded-lg"
              title="Refresh"
            >
              <RefreshCw className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Search and filters */}
        {showSearch && (
          <div className="flex items-center gap-3 mb-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search files and templates..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            {showFilters && (
              <div className="flex items-center gap-2">
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value as SortBy)}
                  className="px-3 py-2 border rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="date">Date</option>
                  <option value="name">Name</option>
                  <option value="usage">Usage</option>
                  <option value="rating">Rating</option>
                  <option value="category">Category</option>
                </select>
                
                <button
                  onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                  className="p-2 hover:bg-gray-100 rounded-lg"
                  title={`Sort ${sortOrder === 'asc' ? 'descending' : 'ascending'}`}
                >
                  {sortOrder === 'asc' ? <SortAsc className="w-4 h-4" /> : <SortDesc className="w-4 h-4" />}
                </button>
              </div>
            )}
          </div>
        )}

        {/* Tabs */}
        <div className="flex border-b">
          <button
            onClick={() => setActiveTab('files')}
            className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'files'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-600 hover:text-gray-900'
            }`}
          >
            Recent Files ({filteredFiles.length})
          </button>
          <button
            onClick={() => setActiveTab('templates')}
            className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'templates'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-600 hover:text-gray-900'
            }`}
          >
            Templates ({filteredTemplates.length})
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="p-4">
        {activeTab === 'files' && (
          <div>
            {/* Filter chips */}
            {showFilters && (
              <div className="flex flex-wrap gap-2 mb-4">
                {(['all', 'recent', 'favorites', 'pinned', 'shared', 'processing'] as FilterType[]).map(filterType => (
                  <button
                    key={filterType}
                    onClick={() => setFilter(filterType)}
                    className={`px-3 py-1 text-xs rounded-full border transition-colors ${
                      filter === filterType
                        ? 'bg-blue-500 text-white border-blue-500'
                        : 'bg-gray-100 text-gray-600 border-gray-200 hover:bg-gray-200'
                    }`}
                  >
                    {filterType.charAt(0).toUpperCase() + filterType.slice(1)}
                  </button>
                ))}
              </div>
            )}

            {/* Files list */}
            {filteredFiles.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <File className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p>No files found</p>
                <p className="text-sm mt-1">Try adjusting your search or filters</p>
              </div>
            ) : (
              <div className={`${
                viewMode === 'grid'
                  ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4'
                  : 'space-y-2'
              }`}>
                {filteredFiles.map(file => (
                  <FileItem
                    key={file.id}
                    file={file}
                    viewMode={viewMode}
                    onSelect={(f) => {
                      handleFileAction('view', f);
                      onFileSelect?.(f);
                    }}
                    onAction={handleFileAction}
                    onTogglePin={handleTogglePin}
                  />
                ))}
              </div>
            )}
          </div>
        )}

        {activeTab === 'templates' && (
          <div>
            {/* Create template button */}
            <div className="mb-4">
              <button
                onClick={onCreateTemplate}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <Plus className="w-4 h-4" />
                Create Template
              </button>
            </div>

            {/* Templates list */}
            {filteredTemplates.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <TrendingUp className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p>No templates found</p>
                <p className="text-sm mt-1">Create your first template to get started</p>
              </div>
            ) : (
              <div className={`${
                viewMode === 'grid'
                  ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4'
                  : 'space-y-2'
              }`}>
                {filteredTemplates.map(template => (
                  <TemplateItem
                    key={template.id}
                    template={template}
                    viewMode={viewMode}
                    onSelect={onTemplateSelect || (() => {})}
                  />
                ))}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Stats footer */}
      {showStats && stats && (
        <div className="p-4 border-t bg-gray-50">
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 text-center">
            <div>
              <p className="text-lg font-bold text-gray-900">{stats.recentFiles}</p>
              <p className="text-xs text-gray-600">Recent Files</p>
            </div>
            <div>
              <p className="text-lg font-bold text-blue-600">{stats.pinnedFiles}</p>
              <p className="text-xs text-gray-600">Pinned</p>
            </div>
            <div>
              <p className="text-lg font-bold text-green-600">{stats.favoriteTemplates}</p>
              <p className="text-xs text-gray-600">Templates</p>
            </div>
            <div>
              <p className="text-lg font-bold text-purple-600">{Math.round(stats.avgAccessTime)}</p>
              <p className="text-xs text-gray-600">Avg Uses</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EnhancedQuickAccessPanel;
export { EnhancedQuickAccessManager, type EnhancedRecentFile, type EnhancedFavoriteTemplate, type QuickAccessStats };