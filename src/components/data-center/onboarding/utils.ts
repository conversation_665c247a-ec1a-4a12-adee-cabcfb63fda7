import { OnboardingProgress, OnboardingPreferences, SmartDefaultsContext } from './types';

// Storage keys
export const STORAGE_KEYS = {
  PROGRESS: 'datacenter_onboarding_progress_v2',
  PREFERENCES: 'datacenter_onboarding_preferences_v2',
  ANALYTICS: 'datacenter_onboarding_analytics_v2'
} as const;

// Default preferences
export const DEFAULT_PREFERENCES: OnboardingPreferences = {
  autoShowTutorials: true,
  showTooltips: true,
  animationSpeed: 'normal',
  learningStyle: 'interactive',
  skipIntroductions: false,
  showHints: true,
  compactMode: false,
  highContrast: false
};

// Default progress state
export const DEFAULT_PROGRESS: OnboardingProgress = {
  completedTutorials: [],
  completedSteps: [],
  dismissedFeatures: [],
  totalProgress: 0,
  lastActivity: new Date(),
  preferences: DEFAULT_PREFERENCES
};

// Local storage utilities
export const storage = {
  get: <T>(key: string, defaultValue: T): T => {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : defaultValue;
    } catch (error) {
      return defaultValue;
    }
  },
  
  set: <T>(key: string, value: T): void => {
    try {
      localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
    }
  },
  
  remove: (key: string): void => {
    try {
      localStorage.removeItem(key);
    } catch (error) {
    }
  }
};

// Smart defaults logic
export const getSmartDefaults = (context: SmartDefaultsContext): Record<string, any> => {
  const defaults: Record<string, any> = {};
  
  // File type based defaults
  if (context.fileType) {
    switch (context.fileType) {
      case 'text/csv':
        defaults.suggestedView = 'preview';
        defaults.autoDetectSchema = true;
        defaults.recommendedTemplate = 'dataQualityAnalysis';
        break;
        
      case 'application/json':
        defaults.suggestedView = 'preview';
        defaults.formatJson = true;
        defaults.recommendedTemplate = 'documentExtraction';
        break;
        
      case 'application/pdf':
        defaults.suggestedView = 'processing';
        defaults.extractText = true;
        defaults.recommendedTemplate = 'documentExtraction';
        break;
        
      case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
      case 'application/vnd.ms-excel':
        defaults.suggestedView = 'preview';
        defaults.autoDetectSchema = true;
        defaults.recommendedTemplate = 'correlationAnalysis';
        break;
        
      default:
        defaults.suggestedView = 'upload';
        defaults.recommendedTemplate = 'dataQualityAnalysis';
    }
  }
  
  // User experience based defaults
  if (context.userExperience === 'beginner') {
    defaults.showDetailedTooltips = true;
    defaults.enableGuidedTour = true;
    defaults.suggestTutorials = true;
  } else if (context.userExperience === 'expert') {
    defaults.showDetailedTooltips = false;
    defaults.enableAdvancedFeatures = true;
    defaults.compactInterface = true;
  }
  
  // File size based defaults
  if (context.fileSize) {
    if (context.fileSize > 50 * 1024 * 1024) { // > 50MB
      defaults.enableProgressIndicator = true;
      defaults.suggestChunking = true;
    }
  }
  
  return defaults;
};

// Progress calculation
export const calculateProgress = (progress: OnboardingProgress, totalTutorials: number): number => {
  if (totalTutorials === 0) return 0;
  
  const completedWeight = 0.7;
  const activityWeight = 0.3;
  
  const completionScore = (progress.completedTutorials.length / totalTutorials) * completedWeight;
  const activityScore = Math.min(progress.completedSteps.length / 50, 1) * activityWeight;
  
  return Math.round((completionScore + activityScore) * 100);
};

// Element positioning utilities
export const getElementPosition = (element: Element) => {
  const rect = element.getBoundingClientRect();
  return {
    top: rect.top + window.scrollY,
    left: rect.left + window.scrollX,
    width: rect.width,
    height: rect.height,
    center: {
      x: rect.left + rect.width / 2,
      y: rect.top + rect.height / 2
    }
  };
};

// Tutorial step validation
export const validateStep = (stepId: string, completionCheck?: () => boolean): boolean => {
  if (!completionCheck) return true;
  
  try {
    return completionCheck();
  } catch (error) {
    return false;
  }
};

// Accessibility utilities
export const accessibilityUtils = {
  announceToScreenReader: (message: string) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    
    document.body.appendChild(announcement);
    
    setTimeout(() => {
      document.body.removeChild(announcement);
    }, 1000);
  },
  
  focusElement: (selector: string) => {
    const element = document.querySelector(selector) as HTMLElement;
    if (element) {
      element.focus();
      element.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  },
  
  addKeyboardNavigation: (element: HTMLElement, onEnter: () => void) => {
    element.setAttribute('tabindex', '0');
    element.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault();
        onEnter();
      }
    });
  }
};

// Animation utilities
export const animationUtils = {
  fadeIn: (duration = 300) => ({
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    transition: { duration: duration / 1000 }
  }),
  
  slideIn: (direction: 'left' | 'right' | 'up' | 'down' = 'up', duration = 300) => {
    const variants = {
      left: { x: -20 },
      right: { x: 20 },
      up: { y: -20 },
      down: { y: 20 }
    };
    
    return {
      initial: { opacity: 0, ...variants[direction] },
      animate: { opacity: 1, x: 0, y: 0 },
      transition: { duration: duration / 1000 }
    };
  },
  
  scale: (duration = 300) => ({
    initial: { opacity: 0, scale: 0.95 },
    animate: { opacity: 1, scale: 1 },
    transition: { duration: duration / 1000 }
  })
};

// Debounce utility
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func.apply(null, args), wait);
  };
};

// Event tracking utilities
export const trackEvent = (eventName: string, properties?: Record<string, any>) => {
  // In a real implementation, this would send to analytics service
  
  // Store locally for analytics
  const events: Array<{
    timestamp: string;
    event: string;
    properties?: Record<string, any>;
  }> = storage.get(STORAGE_KEYS.ANALYTICS, []);
  
  events.push({
    timestamp: new Date().toISOString(),
    event: eventName,
    properties
  });
  
  // Keep only last 1000 events
  if (events.length > 1000) {
    events.splice(0, events.length - 1000);
  }
  
  storage.set(STORAGE_KEYS.ANALYTICS, events);
};

// Feature detection utilities
export const featureDetection = {
  hasTouch: () => 'ontouchstart' in window || navigator.maxTouchPoints > 0,
  isMobile: () => window.innerWidth < 768,
  supportsIntersectionObserver: () => 'IntersectionObserver' in window,
  supportsLocalStorage: () => {
    try {
      localStorage.setItem('test', 'test');
      localStorage.removeItem('test');
      return true;
    } catch {
      return false;
    }
  }
};

// Tour step helpers
export const tourHelpers = {
  scrollToElement: (selector: string, offset = 0) => {
    const element = document.querySelector(selector);
    if (element) {
      const rect = element.getBoundingClientRect();
      const scrollTop = window.pageYOffset + rect.top - offset;
      window.scrollTo({ top: scrollTop, behavior: 'smooth' });
    }
  },
  
  highlightElement: (selector: string) => {
    const element = document.querySelector(selector) as HTMLElement;
    if (element) {
      element.style.transition = 'all 0.3s ease';
      element.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.5)';
      element.style.position = 'relative';
      element.style.zIndex = '1000';
      
      return () => {
        element.style.boxShadow = '';
        element.style.zIndex = '';
      };
    }
    return () => {};
  }
};