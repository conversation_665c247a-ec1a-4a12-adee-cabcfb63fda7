import React, { createContext, useContext, useState, useEffect, useReducer, ReactNode } from 'react';
import { 
  OnboardingContextType, 
  OnboardingProgress, 
  OnboardingPreferences, 
  OnboardingTutorial, 
  OnboardingEvent,
  SmartDefaultsContext 
} from './types';
import { 
  storage, 
  STORAGE_KEYS, 
  DEFAULT_PROGRESS, 
  DEFAULT_PREFERENCES, 
  calculateProgress, 
  getSmartDefaults,
  trackEvent 
} from './utils';
import { useDataCenter } from '../DataCenterContext';

// Onboarding state reducer
const onboardingReducer = (state: OnboardingProgress, action: OnboardingEvent): OnboardingProgress => {
  switch (action.type) {
    case 'TUTORIAL_START':
      trackEvent('tutorial_started', { tutorialId: action.payload.tutorialId });
      return {
        ...state,
        currentTutorial: action.payload.tutorialId,
        lastActivity: new Date()
      };
      
    case 'TUTORIAL_COMPLETE':
      const newCompletedTutorials = [...state.completedTutorials];
      if (!newCompletedTutorials.includes(action.payload.tutorialId)) {
        newCompletedTutorials.push(action.payload.tutorialId);
      }
      
      trackEvent('tutorial_completed', { tutorialId: action.payload.tutorialId });
      return {
        ...state,
        completedTutorials: newCompletedTutorials,
        currentTutorial: undefined,
        totalProgress: calculateProgress({ ...state, completedTutorials: newCompletedTutorials }, 10),
        lastActivity: new Date()
      };
      
    case 'STEP_COMPLETE':
      const newCompletedSteps = [...state.completedSteps];
      if (!newCompletedSteps.includes(action.payload.stepId)) {
        newCompletedSteps.push(action.payload.stepId);
      }
      
      trackEvent('step_completed', { stepId: action.payload.stepId });
      return {
        ...state,
        completedSteps: newCompletedSteps,
        totalProgress: calculateProgress({ ...state, completedSteps: newCompletedSteps }, 10),
        lastActivity: new Date()
      };
      
    case 'FEATURE_DISCOVERED':
      const newDismissedFeatures = [...state.dismissedFeatures];
      if (!newDismissedFeatures.includes(action.payload.featureId)) {
        newDismissedFeatures.push(action.payload.featureId);
      }
      
      trackEvent('feature_discovered', { featureId: action.payload.featureId });
      return {
        ...state,
        dismissedFeatures: newDismissedFeatures,
        lastActivity: new Date()
      };
      
    case 'PREFERENCES_UPDATE':
      return {
        ...state,
        preferences: { ...state.preferences, ...action.payload },
        lastActivity: new Date()
      };
      
    case 'PROGRESS_RESET':
      trackEvent('progress_reset');
      return DEFAULT_PROGRESS;
      
    default:
      return state;
  }
};

// Create context
const OnboardingContext = createContext<OnboardingContextType | undefined>(undefined);

// Tutorial definitions
const TUTORIALS: Record<string, OnboardingTutorial> = {
  dataCenterBasics: {
    id: 'dataCenterBasics',
    title: 'Data Center Basics',
    description: 'Learn the fundamentals of uploading, processing, and analyzing data',
    category: 'basics',
    duration: '5 minutes',
    difficulty: 'beginner',
    steps: [
      {
        id: 'welcome',
        title: 'Welcome to Data Center',
        description: 'Your comprehensive data processing platform',
        content: (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Welcome to the Data Center!</h3>
            <p>This platform helps you upload, process, and analyze various data formats including:</p>
            <ul className="list-disc pl-6 space-y-1">
              <li>CSV and Excel spreadsheets</li>
              <li>JSON data files</li>
              <li>PDF documents</li>
              <li>Images and multimedia</li>
            </ul>
            <p>Let's start with a quick tour of the main features.</p>
          </div>
        ),
        targetElement: '[data-tutorial="main-interface"]',
        position: 'bottom',
        skipable: false
      },
      {
        id: 'upload-intro',
        title: 'Upload Your Data',
        description: 'Learn how to upload files for processing',
        content: (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Upload Files</h3>
            <p>Drag and drop files or click to browse. The system automatically detects file types and suggests optimal processing templates.</p>
            <div className="bg-blue-50 p-3 rounded-lg">
              <p className="text-sm font-medium text-blue-800">Pro Tip:</p>
              <p className="text-sm text-blue-700">Large files are processed efficiently with progress indicators.</p>
            </div>
          </div>
        ),
        targetElement: '[data-tutorial="upload-section"]',
        position: 'bottom'
      },
      {
        id: 'preview-data',
        title: 'Preview Your Data',
        description: 'Explore data structure before processing',
        content: (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Data Preview</h3>
            <p>View your data with automatic type detection, statistics, and quality metrics.</p>
            <ul className="list-disc pl-6 space-y-1">
              <li>Column data types</li>
              <li>Statistical summaries</li>
              <li>Missing value detection</li>
              <li>Sample data preview</li>
            </ul>
          </div>
        ),
        targetElement: '[data-tutorial="preview-section"]',
        position: 'top'
      },
      {
        id: 'processing-templates',
        title: 'Processing Templates',
        description: 'Apply powerful transformations with Python',
        content: (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Smart Processing</h3>
            <p>Choose from pre-built templates or create custom Python scripts:</p>
            <ul className="list-disc pl-6 space-y-1">
              <li>Document extraction</li>
              <li>Data quality analysis</li>
              <li>Knowledge graph generation</li>
              <li>Named entity recognition</li>
            </ul>
          </div>
        ),
        targetElement: '[data-tutorial="processing-section"]',
        position: 'left'
      },
      {
        id: 'sql-generation',
        title: 'SQL Output',
        description: 'Generate SQL for database integration',
        content: (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">SQL Generation</h3>
            <p>Automatically generate SQL CREATE and INSERT statements for database integration.</p>
            <div className="bg-green-50 p-3 rounded-lg">
              <p className="text-sm font-medium text-green-800">Ready to Export:</p>
              <p className="text-sm text-green-700">Download SQL files or copy to clipboard for immediate use.</p>
            </div>
          </div>
        ),
        targetElement: '[data-tutorial="sql-section"]',
        position: 'top'
      }
    ]
  },
  
  advancedFeatures: {
    id: 'advancedFeatures',
    title: 'Advanced Features',
    description: 'Master advanced data processing and visualization',
    category: 'advanced',
    duration: '8 minutes',
    difficulty: 'intermediate',
    prerequisites: ['dataCenterBasics'],
    steps: [
      {
        id: 'data-exploration',
        title: 'Interactive Data Exploration',
        description: 'Deep dive into your data with visualizations',
        content: (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Advanced Exploration</h3>
            <p>Use the exploration dashboard to:</p>
            <ul className="list-disc pl-6 space-y-1">
              <li>Create interactive visualizations</li>
              <li>Apply complex filters</li>
              <li>Generate correlation matrices</li>
              <li>Export filtered datasets</li>
            </ul>
          </div>
        ),
        targetElement: '[data-tutorial="explore-section"]',
        position: 'bottom'
      },
      {
        id: 'custom-templates',
        title: 'Custom Processing Templates',
        description: 'Create and modify Python processing scripts',
        content: (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Custom Templates</h3>
            <p>Build powerful custom processing pipelines with Python libraries like pandas, scikit-learn, and more.</p>
            <div className="bg-yellow-50 p-3 rounded-lg">
              <p className="text-sm font-medium text-yellow-800">Advanced Feature:</p>
              <p className="text-sm text-yellow-700">Full Python environment with AI/ML libraries included.</p>
            </div>
          </div>
        ),
        targetElement: '[data-tutorial="custom-templates"]',
        position: 'right'
      }
    ]
  }
};

interface OnboardingProviderProps {
  children: ReactNode;
  autoStart?: boolean;
}

export const OnboardingProvider: React.FC<OnboardingProviderProps> = ({ 
  children, 
  autoStart = true 
}) => {
  // Load initial state from storage
  const [progress, dispatch] = useReducer(
    onboardingReducer,
    storage.get(STORAGE_KEYS.PROGRESS, DEFAULT_PROGRESS)
  );
  
  const [isActive, setIsActive] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [currentTutorial, setCurrentTutorial] = useState<OnboardingTutorial | undefined>();
  
  // Get data center context for smart defaults
  const dataCenterContext = useDataCenter();

  // Save progress to storage whenever it changes
  useEffect(() => {
    storage.set(STORAGE_KEYS.PROGRESS, progress);
  }, [progress]);

  // Auto-start onboarding for new users
  useEffect(() => {
    if (autoStart && progress.completedTutorials.length === 0 && progress.preferences.autoShowTutorials) {
      const timer = setTimeout(() => {
        startTutorial('dataCenterBasics');
      }, 2000);
      
      return () => clearTimeout(timer);
    }
  }, [autoStart, progress.completedTutorials.length, progress.preferences.autoShowTutorials]);

  // Context API methods
  const startTutorial = (tutorialId: string) => {
    const tutorial = TUTORIALS[tutorialId];
    if (!tutorial) {
      console.warn(`Tutorial ${tutorialId} not found`);
      return;
    }
    
    // Check prerequisites
    if (tutorial.prerequisites) {
      const missingPrereqs = tutorial.prerequisites.filter(
        prereq => !progress.completedTutorials.includes(prereq)
      );
      if (missingPrereqs.length > 0) {
        console.warn(`Prerequisites not met: ${missingPrereqs.join(', ')}`);
        return;
      }
    }
    
    setCurrentTutorial(tutorial);
    setCurrentStep(0);
    setIsActive(true);
    dispatch({ type: 'TUTORIAL_START', payload: { tutorialId } });
  };

  const stopTutorial = () => {
    setIsActive(false);
    setCurrentTutorial(undefined);
    setCurrentStep(0);
  };

  const nextStep = () => {
    if (!currentTutorial) return;
    
    const nextStepIndex = currentStep + 1;
    if (nextStepIndex < currentTutorial.steps.length) {
      setCurrentStep(nextStepIndex);
      
      // Mark previous step as completed
      const completedStep = currentTutorial.steps[currentStep];
      dispatch({ type: 'STEP_COMPLETE', payload: { stepId: completedStep.id } });
    } else {
      completeTutorial();
    }
  };

  const previousStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const skipStep = () => {
    if (!currentTutorial) return;
    
    const step = currentTutorial.steps[currentStep];
    if (step.skipable !== false) {
      nextStep();
    }
  };

  const skipTutorial = () => {
    if (currentTutorial) {
      trackEvent('tutorial_skipped', { 
        tutorialId: currentTutorial.id, 
        step: currentStep 
      });
    }
    stopTutorial();
  };

  const completeTutorial = () => {
    if (!currentTutorial) return;
    
    dispatch({ type: 'TUTORIAL_COMPLETE', payload: { tutorialId: currentTutorial.id } });
    stopTutorial();
  };

  const markStepComplete = (stepId: string) => {
    dispatch({ type: 'STEP_COMPLETE', payload: { stepId } });
  };

  const markFeatureDismissed = (featureId: string) => {
    dispatch({ type: 'FEATURE_DISCOVERED', payload: { featureId } });
  };

  const resetProgress = () => {
    dispatch({ type: 'PROGRESS_RESET' });
    stopTutorial();
  };

  const showFeature = (featureId: string) => {
    // Implementation for showing feature discovery
    console.log('Show feature:', featureId);
  };

  const hideFeature = (featureId: string) => {
    markFeatureDismissed(featureId);
  };

  const updatePreferences = (newPreferences: Partial<OnboardingPreferences>) => {
    dispatch({ type: 'PREFERENCES_UPDATE', payload: newPreferences });
  };

  const getSmartDefaultsForContext = (context: SmartDefaultsContext): Record<string, any> => {
    // Enhance context with user progress
    const enhancedContext = {
      ...context,
      userExperience: progress.completedTutorials.length === 0 ? 'beginner' as const :
                     progress.completedTutorials.length < 3 ? 'intermediate' as const : 
                     'expert' as const,
      previousActions: progress.completedSteps
    };
    
    return getSmartDefaults(enhancedContext);
  };

  const showContextualHelp = (helpId: string, context?: any) => {
    trackEvent('help_requested', { helpId, context });
    // Implementation for contextual help
    console.log('Show contextual help:', helpId, context);
  };

  const hideContextualHelp = () => {
    // Implementation for hiding contextual help
    console.log('Hide contextual help');
  };

  const contextValue: OnboardingContextType = {
    // State
    isActive,
    currentTutorial,
    currentStep,
    progress,
    
    // Tutorial control
    startTutorial,
    stopTutorial,
    nextStep,
    previousStep,
    skipStep,
    skipTutorial,
    completeTutorial,
    
    // Progress management
    markStepComplete,
    markFeatureDismissed,
    resetProgress,
    
    // Feature discovery
    showFeature,
    hideFeature,
    
    // Preferences
    updatePreferences,
    
    // Smart defaults
    getSmartDefaults: getSmartDefaultsForContext,
    
    // Contextual help
    showContextualHelp,
    hideContextualHelp
  };

  return (
    <OnboardingContext.Provider value={contextValue}>
      {children}
    </OnboardingContext.Provider>
  );
};

export const useOnboardingContext = (): OnboardingContextType => {
  const context = useContext(OnboardingContext);
  if (context === undefined) {
    throw new Error('useOnboardingContext must be used within an OnboardingProvider');
  }
  return context;
};