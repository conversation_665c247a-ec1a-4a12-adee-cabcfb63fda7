import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ChevronRight,
  ChevronDown,
  Lock,
  Unlock,
  Star,
  Zap,
  Eye,
  Wrench,
  Database,
  Code,
  BarChart3,
  Upload
} from 'lucide-react';
import { useOnboardingContext } from './OnboardingProvider';
import { useDataCenter } from '../DataCenterContext';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

interface FeatureSection {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  features: FeatureItem[];
  unlockCondition?: () => boolean;
  unlockMessage?: string;
  priority: 'high' | 'medium' | 'low';
}

interface FeatureItem {
  id: string;
  title: string;
  description: string;
  component: React.ReactNode;
  unlockCondition?: () => boolean;
  showOnce?: boolean;
  analyticsId?: string;
}

interface ProgressiveDisclosureProps {
  className?: string;
}

export const ProgressiveDisclosure: React.FC<ProgressiveDisclosureProps> = ({
  className = ''
}) => {
  const { progress, markFeatureDismissed } = useOnboardingContext();
  const { pipelineState } = useDataCenter();
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({});
  const [discoveredFeatures, setDiscoveredFeatures] = useState<Record<string, boolean>>({});

  // Feature sections organized by complexity
  const featureSections: FeatureSection[] = [
    {
      id: 'basics',
      title: 'Getting Started',
      description: 'Essential features to begin your data journey',
      icon: <Star className="w-5 h-5" />,
      priority: 'high',
      features: [
        {
          id: 'upload-data',
          title: 'Upload Files',
          description: 'Drag and drop or browse to upload data files',
          component: (
            <div className="p-4 bg-blue-50 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Upload className="w-4 h-4 text-blue-600" />
                <span className="font-medium">Supported Formats</span>
              </div>
              <div className="flex flex-wrap gap-2">
                <Badge variant="secondary" className="text-xs">CSV</Badge>
                <Badge variant="secondary" className="text-xs">Excel</Badge>
                <Badge variant="secondary" className="text-xs">JSON</Badge>
                <Badge variant="secondary" className="text-xs">PDF</Badge>
              </div>
            </div>
          ),
          analyticsId: 'feature_upload_discovered'
        },
        {
          id: 'preview-data',
          title: 'Data Preview',
          description: 'View and explore your data structure',
          component: (
            <div className="p-4 bg-green-50 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Eye className="w-4 h-4 text-green-600" />
                <span className="font-medium">Auto Analysis</span>
              </div>
              <p className="text-sm text-green-700">
                Automatic data type detection and quality metrics
              </p>
            </div>
          ),
          unlockCondition: () => !!pipelineState.dataPreview,
          analyticsId: 'feature_preview_discovered'
        }
      ]
    },
    {
      id: 'processing',
      title: 'Data Processing',
      description: 'Transform and enrich your data',
      icon: <Wrench className="w-5 h-5" />,
      priority: 'high',
      unlockCondition: () => progress.completedTutorials.includes('dataCenterBasics'),
      unlockMessage: 'Complete the basics tutorial to unlock processing features',
      features: [
        {
          id: 'templates',
          title: 'Processing Templates',
          description: 'Apply pre-built transformations',
          component: (
            <div className="p-4 bg-purple-50 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Zap className="w-4 h-4 text-purple-600" />
              </div>
              <p className="text-sm text-purple-700">
                Document extraction, entity recognition, and more
              </p>
            </div>
          ),
          unlockCondition: () => progress.completedTutorials.includes('dataCenterBasics'),
          analyticsId: 'feature_templates_discovered'
        },
        {
          id: 'custom-processing',
          title: 'Custom Processing',
          description: 'Write your own Python scripts',
          component: (
            <div className="p-4 bg-orange-50 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Code className="w-4 h-4 text-orange-600" />
                <span className="font-medium">Python Environment</span>
              </div>
              <p className="text-sm text-orange-700">
                Full pandas, scikit-learn, and AI/ML libraries
              </p>
            </div>
          ),
          unlockCondition: () => progress.completedTutorials.includes('advancedFeatures'),
          analyticsId: 'feature_custom_processing_discovered'
        }
      ]
    },
    {
      id: 'analysis',
      title: 'Advanced Analysis',
      description: 'Generate insights and visualizations',
      icon: <BarChart3 className="w-5 h-5" />,
      priority: 'medium',
      unlockCondition: () => progress.completedTutorials.includes('dataCenterBasics'),
      unlockMessage: 'Complete the basics tutorial to unlock analysis features',
      features: [
        {
          id: 'sql-generation',
          title: 'SQL Generation',
          description: 'Create database-ready SQL statements',
          component: (
            <div className="p-4 bg-teal-50 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Database className="w-4 h-4 text-teal-600" />
                <span className="font-medium">Export Ready</span>
              </div>
              <p className="text-sm text-teal-700">
                Download or copy generated SQL to clipboard
              </p>
            </div>
          ),
          unlockCondition: () => !!pipelineState.sqlResult,
          analyticsId: 'feature_sql_generation_discovered'
        },
        {
          id: 'data-exploration',
          title: 'Interactive Exploration',
          description: 'Visualize and explore your data',
          component: (
            <div className="p-4 bg-indigo-50 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <BarChart3 className="w-4 h-4 text-indigo-600" />
                <span className="font-medium">Visual Analytics</span>
              </div>
              <p className="text-sm text-indigo-700">
                Charts, correlations, and statistical analysis
              </p>
            </div>
          ),
          unlockCondition: () => progress.completedTutorials.includes('advancedFeatures'),
          analyticsId: 'feature_data_exploration_discovered'
        }
      ]
    }
  ];

  // Toggle section expansion
  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [sectionId]: !prev[sectionId]
    }));
  };

  // Mark feature as discovered
  const discoverFeature = (featureId: string, analyticsId?: string) => {
    setDiscoveredFeatures(prev => ({
      ...prev,
      [featureId]: true
    }));
    
    if (analyticsId) {
      // Track feature discovery
      console.log('Feature discovered:', analyticsId);
    }
    
    // Mark as dismissed in onboarding context
    markFeatureDismissed(featureId);
  };

  // Check if section should be shown based on user progress
  const shouldShowSection = (section: FeatureSection) => {
    if (!section.unlockCondition) return true;
    return section.unlockCondition();
  };

  // Check if feature should be shown
  const shouldShowFeature = (feature: FeatureItem) => {
    if (feature.showOnce && discoveredFeatures[feature.id]) {
      return false;
    }
    
    if (feature.unlockCondition) {
      return feature.unlockCondition();
    }
    
    return true;
  };

  // Filter sections based on user progress
  const availableSections = featureSections.filter(shouldShowSection);

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Feature Discovery</h3>
        <Badge variant="outline">
          {Object.keys(discoveredFeatures).length} of {
            featureSections.flatMap(s => s.features).length
          } features discovered
        </Badge>
      </div>
      
      <div className="space-y-3">
        {availableSections.map((section) => (
          <Card key={section.id} className="overflow-hidden">
            <button
              onClick={() => toggleSection(section.id)}
              className="w-full p-4 text-left flex items-center justify-between hover:bg-muted/50 transition-colors"
            >
              <div className="flex items-center gap-3">
                {section.icon}
                <div>
                  <h4 className="font-medium">{section.title}</h4>
                  <p className="text-sm text-muted-foreground">{section.description}</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                {expandedSections[section.id] ? (
                  <ChevronDown className="w-4 h-4" />
                ) : (
                  <ChevronRight className="w-4 h-4" />
                )}
              </div>
            </button>
            
            <AnimatePresence>
              {expandedSections[section.id] && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: 'auto', opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  transition={{ duration: 0.2 }}
                  className="overflow-hidden"
                >
                  <div className="p-4 pt-0 border-t">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {section.features
                        .filter(shouldShowFeature)
                        .map((feature) => (
                          <Card
                            key={feature.id}
                            className="p-4 hover:shadow-md transition-shadow"
                          >
                            <div className="flex items-start justify-between mb-2">
                              <h5 className="font-medium text-sm">{feature.title}</h5>
                              {!discoveredFeatures[feature.id] && (
                                <Badge
                                  variant="secondary"
                                  className="text-xs"
                                  onClick={() => discoverFeature(feature.id, feature.analyticsId)}
                                >
                                  New
                                </Badge>
                              )}
                            </div>
                            <p className="text-sm text-muted-foreground mb-3">
                              {feature.description}
                            </p>
                            {feature.component}
                            
                            {!discoveredFeatures[feature.id] && (
                              <Button
                                size="sm"
                                variant="outline"
                                className="mt-3 w-full text-xs"
                                onClick={() => discoverFeature(feature.id, feature.analyticsId)}
                              >
                                Mark as Discovered
                              </Button>
                            )}
                          </Card>
                        ))
                      }
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </Card>
        ))}
        
        {availableSections.length === 0 && (
          <Card className="p-6 text-center">
            <Lock className="w-12 h-12 mx-auto text-muted-foreground mb-3" />
            <h4 className="font-medium mb-1">Unlock More Features</h4>
            <p className="text-sm text-muted-foreground">
              Complete tutorials to unlock advanced features
            </p>
          </Card>
        )}
      </div>
    </div>
  );
};
               