import React, { useEffect } from 'react';
import { useOnboardingContext } from './OnboardingProvider';
import { useDataCenter } from '../DataCenterContext';

interface SmartDefaultsProps {
  children: React.ReactNode;
}

export const SmartDefaults: React.FC<SmartDefaultsProps> = ({ children }) => {
  const { getSmartDefaults } = useOnboardingContext();
  const { pipelineState, updatePipelineState } = useDataCenter();

  // Apply smart defaults when file is uploaded
  useEffect(() => {
    if (pipelineState.fileName && pipelineState.fileType) {
      const defaults = getSmartDefaults({
        fileType: pipelineState.fileType,
        fileName: pipelineState.fileName,
        fileSize: pipelineState.fileSize,
        userExperience: pipelineState.context.breadcrumbPath.length > 0 ? 'intermediate' : 'beginner'
      });

      // Apply view mode defaults
      if (defaults.suggestedView && pipelineState.currentStage !== defaults.suggestedView) {
        // Update to suggested view after a short delay to allow UI to settle
        const timer = setTimeout(() => {
          // We don't directly change stage here to avoid conflicts with user navigation
          console.log(`Suggested view for ${pipelineState.fileType}: ${defaults.suggestedView}`);
        }, 1000);
        
        return () => clearTimeout(timer);
      }

      // Apply template suggestions
      if (defaults.recommendedTemplate) {
        console.log(`Recommended template: ${defaults.recommendedTemplate}`);
        // In a full implementation, this would pre-select or suggest a template
      }
    }
  }, [pipelineState.fileName, pipelineState.fileType, pipelineState.fileSize, getSmartDefaults]);

  // Apply user preference defaults
  useEffect(() => {
    const preferences = getSmartDefaults({});
    
    // Apply interface preferences
    if (preferences.compactInterface !== undefined) {
      // Update UI density settings
      document.body.classList.toggle('compact-mode', preferences.compactInterface);
    }
    
    if (preferences.showDetailedTooltips !== undefined) {
      // Update tooltip visibility
      document.body.classList.toggle('show-detailed-tooltips', preferences.showDetailedTooltips);
    }
  }, [getSmartDefaults]);

  return <>{children}</>;
};

// Hook for getting smart defaults in components
export const useSmartDefaults = () => {
  const { getSmartDefaults } = useOnboardingContext();
  const { pipelineState } = useDataCenter();
  
  const getDefaultsForCurrentContext = () => {
    return getSmartDefaults({
      fileType: pipelineState.fileName ? pipelineState.fileType : undefined,
      fileName: pipelineState.fileName,
      fileSize: pipelineState.fileSize,
      userExperience: pipelineState.context.breadcrumbPath.length > 0 ? 'intermediate' : 'beginner'
    });
  };
  
  return {
    getDefaults: getDefaultsForCurrentContext,
    applyDefaults: (overrides: Record<string, any> = {}) => {
      const defaults = getDefaultsForCurrentContext();
      return { ...defaults, ...overrides };
    }
  };
};

// Component for displaying smart suggestions
export const SmartSuggestions: React.FC = () => {
  const { getDefaults } = useSmartDefaults();
  const { pipelineState } = useDataCenter();
  const defaults = getDefaults();

  if (!pipelineState.fileName) return null;

  return (
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
      <h4 className="font-medium text-blue-900 mb-2 flex items-center gap-2">
        <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
        Smart Suggestions
      </h4>
      
      <div className="space-y-2">
        {defaults.suggestedView && (
          <div className="flex items-center justify-between text-sm">
            <span className="text-blue-800">Recommended View:</span>
            <span className="font-medium text-blue-900 capitalize">{defaults.suggestedView}</span>
          </div>
        )}
        
        {defaults.recommendedTemplate && (
          <div className="flex items-center justify-between text-sm">
            <span className="text-blue-800">Suggested Template:</span>
            <span className="font-medium text-blue-900">
              {defaults.recommendedTemplate.replace(/([A-Z])/g, ' $1').trim()}
            </span>
          </div>
        )}
        
        {defaults.enableProgressIndicator && (
          <div className="flex items-center justify-between text-sm">
            <span className="text-blue-800">Large File Detected:</span>
            <span className="font-medium text-blue-900">Progress tracking enabled</span>
          </div>
        )}
      </div>
    </div>
  );
};

// Utility component for applying smart defaults to form fields
export const SmartDefaultField: React.FC<{
  name: string;
  value: any;
  onChange: (value: any) => void;
  children: (props: {
    value: any;
    onChange: (value: any) => void;
    smartValue: any;
    isSmartApplied: boolean;
    applySmartValue: () => void;
  }) => React.ReactNode;
}> = ({ name, value, onChange, children }) => {
  const { getDefaults } = useSmartDefaults();
  const defaults = getDefaults();
  const smartValue = defaults[name];
  const [isSmartApplied, setIsSmartApplied] = React.useState(false);

  const applySmartValue = () => {
    if (smartValue !== undefined) {
      onChange(smartValue);
      setIsSmartApplied(true);
    }
  };

  // Auto-apply smart defaults for new users
  React.useEffect(() => {
    if (smartValue !== undefined && value === undefined && !isSmartApplied) {
      applySmartValue();
    }
  }, [smartValue, value, isSmartApplied]);

  return (
    <>
      {children({
        value,
        onChange,
        smartValue,
        isSmartApplied,
        applySmartValue
      })}
      
      {smartValue !== undefined && value !== smartValue && !isSmartApplied && (
        <button
          onClick={applySmartValue}
          className="text-xs text-blue-600 hover:text-blue-800 mt-1"
        >
          Use smart default
        </button>
      )}
    </>
  );
};