import React, { useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Lightbulb, 
  Play, 
  BookOpen, 
  Zap, 
  Database, 
  BarChart3,
  Rocket,
  CheckCircle,
  X,
  ArrowRight,
  Users,
  Shield,
  Sparkles
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface OnboardingWelcomeProps {
  onStartTutorial: () => void;
  onSkip: () => void;
  onClose?: () => void;
  progress: {
    completedTutorials: string[];
    totalProgress: number;
  };
  isOpen?: boolean;
  variant?: 'modal' | 'inline';
}

export const OnboardingWelcome: React.FC<OnboardingWelcomeProps> = ({ 
  onStartTutorial, 
  onSkip,
  onClose,
  progress,
  isOpen = true,
  variant = 'modal'
}) => {
  const modalRef = useRef<HTMLDivElement>(null);

  // Handle escape key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && onClose) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      // Focus management
      modalRef.current?.focus();
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, [isOpen, onClose]);

  // Trap focus within modal
  useEffect(() => {
    if (!isOpen || variant !== 'modal') return;

    const modal = modalRef.current;
    if (!modal) return;

    const focusableElements = modal.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    const firstElement = focusableElements[0] as HTMLElement;
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key !== 'Tab') return;

      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          lastElement?.focus();
          e.preventDefault();
        }
      } else {
        if (document.activeElement === lastElement) {
          firstElement?.focus();
          e.preventDefault();
        }
      }
    };

    modal.addEventListener('keydown', handleTabKey);
    return () => modal.removeEventListener('keydown', handleTabKey);
  }, [isOpen, variant]);

  const features = [
    {
      icon: <Rocket className="w-5 h-5" />,
      title: "Quick Start",
      description: "Get up and running in minutes with guided tutorials",
      color: "text-blue-600 bg-blue-100 dark:bg-blue-900/20"
    },
    {
      icon: <Zap className="w-5 h-5" />,
      title: "Smart Processing",
      description: "Apply powerful transformations with Python templates",
      color: "text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20"
    },
    {
      icon: <Database className="w-5 h-5" />,
      title: "SQL Generation",
      description: "Create database-ready SQL statements automatically",
      color: "text-green-600 bg-green-100 dark:bg-green-900/20"
    },
    {
      icon: <BarChart3 className="w-5 h-5" />,
      title: "Data Visualization",
      description: "Create interactive charts and explore your data",
      color: "text-purple-600 bg-purple-100 dark:bg-purple-900/20"
    }
  ];

  const tutorials = [
    {
      id: 'dataCenterBasics',
      title: 'Data Center Basics',
      description: 'Learn the fundamentals of uploading and processing data',
      duration: '5 minutes',
      icon: <BookOpen className="w-4 h-4" />,
      difficulty: 'Beginner'
    },
    {
      id: 'advancedFeatures',
      title: 'Advanced Features',
      description: 'Master advanced data processing techniques',
      duration: '8 minutes',
      icon: <Zap className="w-4 h-4" />,
      difficulty: 'Intermediate'
    },
    {
      id: 'bestPractices',
      title: 'Best Practices',
      description: 'Learn industry best practices for data management',
      duration: '6 minutes',
      icon: <Shield className="w-4 h-4" />,
      difficulty: 'Advanced'
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0, scale: 0.9 },
    visible: { 
      opacity: 1, 
      scale: 1,
      transition: {
        duration: 0.3,
        staggerChildren: 0.1
      }
    },
    exit: { 
      opacity: 0, 
      scale: 0.9,
      transition: { duration: 0.2 }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  const content = (
    <motion.div
      ref={modalRef}
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      exit="exit"
      className={`${variant === 'modal' ? 'max-w-4xl w-full' : 'w-full'} overflow-hidden`}
      tabIndex={-1}
      role={variant === 'modal' ? 'dialog' : 'region'}
      aria-labelledby="onboarding-title"
      aria-describedby="onboarding-description"
    >
      <Card className="border-2 shadow-2xl">
        <CardHeader className="relative pb-6">
          {variant === 'modal' && onClose && (
            <Button
              variant="ghost"
              size="icon"
              className="absolute right-4 top-4 z-10"
              onClick={onClose}
              aria-label="Close onboarding"
            >
              <X className="w-4 h-4" />
            </Button>
          )}
          
          <motion.div variants={itemVariants} className="text-center">
            <div className="mx-auto w-20 h-20 bg-gradient-to-br from-blue-500 via-purple-600 to-pink-500 rounded-2xl flex items-center justify-center mb-6 shadow-lg">
              <Sparkles className="w-10 h-10 text-white" />
            </div>
            <CardTitle id="onboarding-title" className="text-3xl font-bold mb-3">
              Welcome to Data Center
            </CardTitle>
            <CardDescription id="onboarding-description" className="text-lg">
              Your comprehensive platform for data processing and analysis
            </CardDescription>
          </motion.div>
        </CardHeader>

        <CardContent className="space-y-8">
          {/* Progress Section */}
          {progress.totalProgress > 0 && (
            <motion.div variants={itemVariants}>
              <Card className="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-950/20 dark:to-blue-950/20 border-green-200 dark:border-green-800">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-green-800 dark:text-green-200 flex items-center gap-2">
                      <Users className="w-5 h-5" />
                      Your Progress
                    </h3>
                    <Badge variant="secondary" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                      {progress.totalProgress}% Complete
                    </Badge>
                  </div>
                  <div className="w-full bg-green-200 dark:bg-green-800 rounded-full h-3 mb-3">
                    <motion.div 
                      className="bg-gradient-to-r from-green-500 to-blue-500 h-3 rounded-full"
                      initial={{ width: 0 }}
                      animate={{ width: `${progress.totalProgress}%` }}
                      transition={{ duration: 1.5, ease: "easeOut" }}
                    />
                  </div>
                  <p className="text-sm text-green-700 dark:text-green-300">
                    Great job! You're making excellent progress on your data journey.
                  </p>
                </CardContent>
              </Card>
            </motion.div>
          )}

          {/* Key Features Grid */}
          <motion.div variants={itemVariants}>
            <h3 className="text-xl font-semibold mb-6 text-center">
              Powerful Features at Your Fingertips
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {features.map((feature, index) => (
                <motion.div
                  key={index}
                  variants={itemVariants}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Card className="h-full hover:shadow-lg transition-all duration-300 border-2 hover:border-primary/20">
                    <CardContent className="p-6 text-center">
                      <div className={`mx-auto w-14 h-14 ${feature.color} rounded-xl flex items-center justify-center mb-4 shadow-sm`}>
                        {feature.icon}
                      </div>
                      <h4 className="font-semibold text-base mb-2">{feature.title}</h4>
                      <p className="text-sm text-muted-foreground leading-relaxed">
                        {feature.description}
                      </p>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Tutorials Section */}
          <motion.div variants={itemVariants}>
            <h3 className="text-xl font-semibold mb-6 flex items-center gap-2">
              <BookOpen className="w-6 h-6 text-primary" />
              Recommended Learning Path
            </h3>
            <div className="space-y-4">
              {tutorials.map((tutorial, index) => {
                const isCompleted = progress.completedTutorials.includes(tutorial.id);
                
                return (
                  <motion.div 
                    key={tutorial.id}
                    variants={itemVariants}
                    whileHover={{ scale: 1.01 }}
                  >
                    <Card className="hover:shadow-md transition-all duration-200">
                      <CardContent className="p-5">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-4">
                            <div className={`p-3 rounded-lg ${
                              isCompleted 
                                ? 'bg-green-100 dark:bg-green-900/20' 
                                : 'bg-blue-100 dark:bg-blue-900/20'
                            }`}>
                              {isCompleted ? (
                                <CheckCircle className="w-5 h-5 text-green-600" />
                              ) : (
                                tutorial.icon
                              )}
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center gap-3 mb-1">
                                <h4 className="font-semibold text-base">{tutorial.title}</h4>
                                <Badge variant="outline" className="text-xs">
                                  {tutorial.difficulty}
                                </Badge>
                              </div>
                              <p className="text-sm text-muted-foreground mb-2">
                                {tutorial.description}
                              </p>
                              <div className="flex items-center gap-4 text-xs text-muted-foreground">
                                <span className="flex items-center gap-1">
                                  <Play className="w-3 h-3" />
                                  {tutorial.duration}
                                </span>
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center gap-3">
                            {isCompleted ? (
                              <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                <CheckCircle className="w-3 h-3 mr-1" />
                                Completed
                              </Badge>
                            ) : (
                              <Button variant="outline" size="sm" className="flex items-center gap-2">
                                Start
                                <ArrowRight className="w-3 h-3" />
                              </Button>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                );
              })}
            </div>
          </motion.div>

          {/* Action Buttons */}
          <motion.div variants={itemVariants} className="pt-4">
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                onClick={onStartTutorial}
                size="lg"
                className="flex-1 sm:flex-none min-w-[200px] shadow-lg hover:shadow-xl transition-shadow"
              >
                <Play className="w-5 h-5 mr-2" />
                Start Learning Journey
              </Button>
              <Button 
                variant="outline" 
                onClick={onSkip}
                size="lg"
                className="flex-1 sm:flex-none min-w-[150px]"
              >
                Skip for Now
              </Button>
            </div>
            <p className="text-center text-xs text-muted-foreground mt-4">
              You can always access tutorials from the help menu
            </p>
          </motion.div>
        </CardContent>
      </Card>
    </motion.div>
  );

  if (variant === 'inline') {
    return (
      <AnimatePresence>
        {isOpen && content}
      </AnimatePresence>
    );
  }

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          onClick={(e) => {
            if (e.target === e.currentTarget && onClose) {
              onClose();
            }
          }}
        >
          {content}
        </motion.div>
      )}
    </AnimatePresence>
  );
};