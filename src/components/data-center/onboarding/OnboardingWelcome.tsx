import React from 'react';
import { motion } from 'framer-motion';
import { 
  Lightbulb, 
  Play, 
  BookOpen, 
  Zap, 
  Database, 
  BarChart3,
  Rocket,
  CheckCircle
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface OnboardingWelcomeProps {
  onStartTutorial: () => void;
  onSkip: () => void;
  progress: {
    completedTutorials: string[];
    totalProgress: number;
  };
}

export const OnboardingWelcome: React.FC<OnboardingWelcomeProps> = ({ 
  onStartTutorial, 
  onSkip,
  progress
}) => {
  const features = [
    {
      icon: <Rocket className="w-5 h-5" />,
      title: "Quick Start",
      description: "Get up and running in minutes with guided tutorials"
    },
    {
      icon: <Zap className="w-5 h-5" />,
      title: "Smart Processing",
      description: "Apply powerful transformations with Python templates"
    },
    {
      icon: <Database className="w-5 h-5" />,
      title: "SQL Generation",
      description: "Create database-ready SQL statements automatically"
    },
    {
      icon: <BarChart3 className="w-5 h-5" />,
      title: "Data Visualization",
      description: "Create interactive charts and explore your data"
    }
  ];

  const tutorials = [
    {
      id: 'dataCenterBasics',
      title: 'Data Center Basics',
      description: 'Learn the fundamentals of uploading and processing data',
      duration: '5 minutes',
      icon: <BookOpen className="w-4 h-4" />
    },
    {
      id: 'advancedFeatures',
      title: 'Advanced Features',
      description: 'Master advanced data processing techniques',
      duration: '8 minutes',
      icon: <Zap className="w-4 h-4" />
    }
  ];

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
    >
      <Card className="max-w-2xl w-full overflow-hidden">
        <div className="p-6">
          {/* Header */}
          <div className="text-center mb-8">
            <div className="mx-auto w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mb-4">
              <Lightbulb className="w-8 h-8 text-white" />
            </div>
            <h2 className="text-2xl font-bold mb-2">Welcome to Data Center</h2>
            <p className="text-muted-foreground">
              Your comprehensive platform for data processing and analysis
            </p>
          </div>

          {/* Progress */}
          {progress.totalProgress > 0 && (
            <div className="mb-8 p-4 bg-muted/30 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium">Your Progress</span>
                <Badge variant="secondary">{progress.totalProgress}%</Badge>
              </div>
              <div className="w-full bg-muted rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full transition-all duration-500"
                  style={{ width: `${progress.totalProgress}%` }}
                />
              </div>
            </div>
          )}

          {/* Key Features */}
          <div className="grid grid-cols-2 gap-4 mb-8">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="p-4 bg-card border rounded-lg text-center"
              >
                <div className="mx-auto w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mb-3">
                  {feature.icon}
                </div>
                <h3 className="font-medium text-sm mb-1">{feature.title}</h3>
                <p className="text-xs text-muted-foreground">{feature.description}</p>
              </motion.div>
            ))}
          </div>

          {/* Tutorials */}
          <div className="mb-8">
            <h3 className="font-semibold mb-4 flex items-center gap-2">
              <BookOpen className="w-5 h-5" />
              Recommended Tutorials
            </h3>
            <div className="space-y-3">
              {tutorials.map((tutorial) => {
                const isCompleted = progress.completedTutorials.includes(tutorial.id);
                
                return (
                  <div 
                    key={tutorial.id} 
                    className="flex items-center justify-between p-3 bg-muted/30 rounded-lg"
                  >
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-background rounded-md">
                        {tutorial.icon}
                      </div>
                      <div>
                        <h4 className="font-medium text-sm">{tutorial.title}</h4>
                        <p className="text-xs text-muted-foreground">{tutorial.description}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-xs text-muted-foreground">{tutorial.duration}</span>
                      {isCompleted ? (
                        <CheckCircle className="w-5 h-5 text-green-500" />
                      ) : (
                        <Badge variant="outline" className="text-xs">New</Badge>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Actions */}
          <div className="flex flex-col sm:flex-row gap-3">
            <Button 
              onClick={onStartTutorial}
              className="flex-1 flex items-center justify-center gap-2"
            >
              <Play className="w-4 h-4" />
              Start Learning
            </Button>
            <Button 
              variant="outline" 
              onClick={onSkip}
              className="flex-1"
            >
              Skip for now
            </Button>
          </div>
        </div>
      </Card>
    </motion.div>
  );
};