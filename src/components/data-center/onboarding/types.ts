import React from 'react';

// Core onboarding types
export interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  targetElement?: string; // CSS selector
  position?: 'top' | 'bottom' | 'left' | 'right' | 'center';
  content: React.ReactNode;
  action?: () => void;
  skipable?: boolean;
  required?: boolean;
  completionCheck?: () => boolean;
  onEnter?: () => void;
  onExit?: () => void;
  actions?: Array<{
    id: string;
    label: string;
    onClick: () => void;
    primary?: boolean;
    icon?: React.ReactNode;
  }>;
  autoAdvance?: boolean;
  advanceDelay?: number;
}

export interface OnboardingTutorial {
  id: string;
  title: string;
  description: string;
  category: 'basics' | 'advanced' | 'feature';
  duration: string;
  steps: OnboardingStep[];
  prerequisites?: string[];
  icon?: React.ReactNode;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
}

export interface OnboardingFeature {
  id: string;
  title: string;
  description: string;
  icon?: React.ReactNode;
  targetElement?: string;
  position?: 'top' | 'bottom' | 'left' | 'right';
  trigger: 'hover' | 'click' | 'auto' | 'manual';
  priority: 'high' | 'medium' | 'low';
  showOnce?: boolean;
}

export interface OnboardingProgress {
  completedTutorials: string[];
  completedSteps: string[];
  dismissedFeatures: string[];
  currentTutorial?: string;
  currentStep?: number;
  totalProgress: number;
  lastActivity: Date;
  preferences: OnboardingPreferences;
}

export interface OnboardingPreferences {
  autoShowTutorials: boolean;
  showTooltips: boolean;
  animationSpeed: 'slow' | 'normal' | 'fast' | 'none';
  learningStyle: 'visual' | 'interactive' | 'guided' | 'self-paced';
  skipIntroductions: boolean;
  showHints: boolean;
  compactMode: boolean;
  highContrast: boolean;
}

export interface OnboardingContextType {
  // State
  isActive: boolean;
  currentTutorial?: OnboardingTutorial;
  currentStep: number;
  progress: OnboardingProgress;
  
  // Tutorial control
  startTutorial: (tutorialId: string) => void;
  stopTutorial: () => void;
  nextStep: () => void;
  previousStep: () => void;
  skipStep: () => void;
  skipTutorial: () => void;
  completeTutorial: () => void;
  
  // Progress management
  markStepComplete: (stepId: string) => void;
  markFeatureDismissed: (featureId: string) => void;
  resetProgress: () => void;
  
  // Feature discovery
  showFeature: (featureId: string) => void;
  hideFeature: (featureId: string) => void;
  
  // Preferences
  updatePreferences: (preferences: Partial<OnboardingPreferences>) => void;
  
  // Smart defaults
  getSmartDefaults: (context: SmartDefaultsContext) => Record<string, any>;
  
  // Contextual help
  showContextualHelp: (helpId: string, context?: any) => void;
  hideContextualHelp: () => void;
}

export interface SmartDefaultsContext {
  fileType?: string;
  fileName?: string;
  fileSize?: number;
  dataSchema?: Record<string, string>;
  userExperience?: 'beginner' | 'intermediate' | 'expert';
  previousActions?: string[];
}

export interface ContextualHelpItem {
  id: string;
  title: string;
  content: string;
  category: 'general' | 'feature' | 'troubleshooting';
  keywords: string[];
  relatedFeatures?: string[];
  icon?: React.ReactNode;
}

export interface ProgressiveDisclosureLevel {
  id: string;
  level: number;
  title: string;
  description: string;
  features: string[];
  requirements?: {
    completedTutorials?: string[];
    completedSteps?: string[];
    timeSpent?: number;
  };
}

export interface OnboardingAnalytics {
  tutorialStarted: (tutorialId: string) => void;
  tutorialCompleted: (tutorialId: string, timeSpent: number) => void;
  tutorialSkipped: (tutorialId: string, step: number) => void;
  stepCompleted: (stepId: string, timeSpent: number) => void;
  featureDiscovered: (featureId: string) => void;
  helpRequested: (helpId: string, context: string) => void;
  errorEncountered: (error: string, context: string) => void;
}

export type OnboardingEvent = 
  | { type: 'TUTORIAL_START'; payload: { tutorialId: string } }
  | { type: 'TUTORIAL_COMPLETE'; payload: { tutorialId: string } }
  | { type: 'STEP_COMPLETE'; payload: { stepId: string } }
  | { type: 'FEATURE_DISCOVERED'; payload: { featureId: string } }
  | { type: 'PREFERENCES_UPDATE'; payload: Partial<OnboardingPreferences> }
  | { type: 'PROGRESS_RESET' };