import React, { useState, useEffect } from 'react';
import { useOnboardingContext } from './OnboardingProvider';
import { TutorialSteps } from './TutorialSteps';
import { TutorialWelcome } from './TutorialSteps';
import { FeatureDiscovery } from './FeatureDiscovery';
import { ContextualHelp } from './ContextualHelp';
import { ProgressiveDisclosure } from './ProgressiveDisclosure';
import { SmartDefaults } from './SmartDefaults';
import { AchievementBadges } from './FeatureDiscovery';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Lightbulb, 
  BookOpen, 
  Settings, 
  X, 
  Play,
  CheckCircle,
  Star
} from 'lucide-react';

interface OnboardingOverlayProps {
  className?: string;
}

export const OnboardingOverlay: React.FC<OnboardingOverlayProps> = ({
  className = ''
}) => {
  const {
    progress,
    startTutorial,
    updatePreferences,
    resetProgress
  } = useOnboardingContext();
  
  const preferences = progress.preferences;
  
  const [showWelcome, setShowWelcome] = useState(false);
  const [showProgress, setShowProgress] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [nextTutorial, setNextTutorial] = useState<string | null>(null);

  // Determine next tutorial
  useEffect(() => {
    const tutorials = ['dataCenterBasics', 'advancedFeatures'];
    const next = tutorials.find(tutorial => 
      !progress.completedTutorials.includes(tutorial)
    );
    setNextTutorial(next || null);
  }, [progress.completedTutorials]);

  // Auto-show welcome for new users
  useEffect(() => {
    if (progress.completedTutorials.length === 0 && preferences.autoShowTutorials) {
      const timer = setTimeout(() => {
        setShowWelcome(true);
      }, 3000);
      
      return () => clearTimeout(timer);
    }
  }, [progress.completedTutorials.length, preferences.autoShowTutorials]);

  const handleStartTutorial = (tutorialId: string) => {
    setShowWelcome(false);
    startTutorial(tutorialId);
  };

  const handleSkipTutorial = () => {
    setShowWelcome(false);
  };

  const toggleSettings = () => {
    setShowSettings(!showSettings);
  };

  const handleResetProgress = () => {
    if (confirm('Reset all onboarding progress? This cannot be undone.')) {
      resetProgress();
      setShowSettings(false);
    }
  };

  return (
    <SmartDefaults>
      <div className={className}>
        {/* Tutorial Steps */}
        <TutorialSteps 
          tutorialId="dataCenterBasics" 
          onComplete={() => {}}
          onSkip={() => {}}
        />
        
        <TutorialSteps 
          tutorialId="advancedFeatures" 
          onComplete={() => {}}
          onSkip={() => {}}
        />
        
        {/* Tutorial Welcome */}
        <AnimatePresence>
          {showWelcome && nextTutorial && (
            <TutorialWelcome
              tutorialId={nextTutorial}
              onStart={() => handleStartTutorial(nextTutorial)}
              onSkip={handleSkipTutorial}
            />
          )}
        </AnimatePresence>
        
        {/* Feature Discovery */}
        <FeatureDiscovery />
        
        {/* Achievement Badges */}
        <AchievementBadges />
        
        {/* Contextual Help */}
        <ContextualHelp />
        
        {/* Quick Access Panel */}
        <div className="fixed top-4 left-4 z-30">
          <div className="flex flex-col gap-2">
            {/* Progress Indicator */}
            {progress.totalProgress > 0 && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                className="bg-card border rounded-lg p-3 shadow-lg"
              >
                <div className="flex items-center gap-2 mb-2">
                  <div className="p-1.5 bg-blue-100 rounded-md">
                    <Star className="w-4 h-4 text-blue-600" />
                  </div>
                  <div>
                    <p className="text-xs font-medium">Learning Progress</p>
                    <p className="text-xs text-muted-foreground">
                      {progress.totalProgress}% complete
                    </p>
                  </div>
                </div>
                <div className="w-full bg-muted rounded-full h-1.5">
                  <div 
                    className="bg-blue-600 h-1.5 rounded-full transition-all duration-300"
                    style={{ width: `${progress.totalProgress}%` }}
                  />
                </div>
              </motion.div>
            )}
            
            {/* Quick Actions */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.1 }}
              className="flex flex-col gap-2"
            >
              {nextTutorial && (
                <Button
                  variant="default"
                  size="sm"
                  className="flex items-center gap-2"
                  onClick={() => setShowWelcome(true)}
                >
                  <Play className="w-4 h-4" />
                  Start Tutorial
                </Button>
              )}
              
              <Button
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
                onClick={() => setShowProgress(!showProgress)}
              >
                <BookOpen className="w-4 h-4" />
                Progress
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
                onClick={toggleSettings}
              >
                <Settings className="w-4 h-4" />
                Settings
              </Button>
            </motion.div>
          </div>
        </div>
        
        {/* Progress Panel */}
        <AnimatePresence>
          {showProgress && (
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="fixed top-4 left-32 z-30 w-80"
            >
              <Card className="p-4 shadow-lg">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="font-semibold flex items-center gap-2">
                    <BookOpen className="w-5 h-5" />
                    Learning Progress
                  </h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="p-1 h-auto"
                    onClick={() => setShowProgress(false)}
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
                
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Overall Progress</span>
                    <Badge variant="secondary" className="text-xs">
                      {progress.totalProgress}%
                    </Badge>
                  </div>
                  
                  <div className="w-full bg-muted rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${progress.totalProgress}%` }}
                    />
                  </div>
                  
                  <div className="pt-2 border-t">
                    <h4 className="font-medium text-sm mb-2">Tutorials</h4>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Data Center Basics</span>
                        {progress.completedTutorials.includes('dataCenterBasics') ? (
                          <CheckCircle className="w-4 h-4 text-green-500" />
                        ) : (
                          <div className="w-4 h-4 rounded-full border-2 border-muted-foreground" />
                        )}
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Advanced Features</span>
                        {progress.completedTutorials.includes('advancedFeatures') ? (
                          <CheckCircle className="w-4 h-4 text-green-500" />
                        ) : (
                          <div className="w-4 h-4 rounded-full border-2 border-muted-foreground" />
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>
        
        {/* Settings Panel */}
        <AnimatePresence>
          {showSettings && (
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="fixed top-4 left-32 z-30 w-80"
            >
              <Card className="p-4 shadow-lg">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="font-semibold flex items-center gap-2">
                    <Settings className="w-5 h-5" />
                    Onboarding Settings
                  </h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="p-1 h-auto"
                    onClick={toggleSettings}
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
                
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium">Auto-show Tutorials</p>
                      <p className="text-xs text-muted-foreground">
                        Show tutorials automatically
                      </p>
                    </div>
                    <Button
                      variant={preferences.autoShowTutorials ? "default" : "outline"}
                      size="sm"
                      onClick={() => updatePreferences({ autoShowTutorials: !preferences.autoShowTutorials })}
                    >
                      {preferences.autoShowTutorials ? 'On' : 'Off'}
                    </Button>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium">Show Tooltips</p>
                      <p className="text-xs text-muted-foreground">
                        Display feature tooltips
                      </p>
                    </div>
                    <Button
                      variant={preferences.showTooltips ? "default" : "outline"}
                      size="sm"
                      onClick={() => updatePreferences({ showTooltips: !preferences.showTooltips })}
                    >
                      {preferences.showTooltips ? 'On' : 'Off'}
                    </Button>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium">Learning Style</p>
                      <p className="text-xs text-muted-foreground">
                        How you prefer to learn
                      </p>
                    </div>
                    <select
                      value={preferences.learningStyle}
                      onChange={(e) => updatePreferences({ 
                        learningStyle: e.target.value as any 
                      })}
                      className="text-sm border rounded px-2 py-1"
                    >
                      <option value="visual">Visual</option>
                      <option value="interactive">Interactive</option>
                      <option value="guided">Guided</option>
                      <option value="self-paced">Self-paced</option>
                    </select>
                  </div>
                  
                  <div className="pt-2 border-t">
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={handleResetProgress}
                      className="w-full"
                    >
                      Reset All Progress
                    </Button>
                  </div>
                </div>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>
        
        {/* Progressive Disclosure Panel */}
        <div className="fixed bottom-4 right-4 z-30">
          <ProgressiveDisclosure className="w-96" />
        </div>
      </div>
    </SmartDefaults>
  );
};