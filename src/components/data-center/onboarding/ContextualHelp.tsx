import React, { useState, useEffect, useRef } from 'react';
import {
  HelpCircle,
  X,
  Search,
  BookOpen,
  Lightbulb,
  AlertCircle,
  CheckCircle,
  Info,
  Eye,
  Zap,
  Database
} from 'lucide-react';
import { useOnboardingContext } from './OnboardingProvider';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card } from '@/components/ui/card';

interface HelpTopic {
  id: string;
  title: string;
  content: string;
  category: 'general' | 'feature' | 'troubleshooting' | 'best-practices';
  keywords: string[];
  related?: string[];
  icon: React.ReactNode;
  priority: 'high' | 'medium' | 'low';
}

interface ContextualHelpProps {
  className?: string;
}

export const ContextualHelp: React.FC<ContextualHelpProps> = ({ 
  className = '' 
}) => {
  const { showContextualHelp, hideContextualHelp } = useOnboardingContext();
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTopic, setActiveTopic] = useState<string | null>(null);
  const helpRef = useRef<HTMLDivElement>(null);

  // Help topics database
  const helpTopics: HelpTopic[] = [
    {
      id: 'upload-data',
      title: 'Uploading Data Files',
      content: `You can upload various file formats including CSV, Excel, JSON, and PDF documents.
      
Key features:
- Drag and drop files directly onto the upload area
- Click to browse and select files from your device
- Files are processed securely in your browser
- Automatic file type detection and processing suggestions

Supported formats:
- CSV (.csv)
- Excel (.xlsx, .xls)
- JSON (.json)
- PDF (.pdf)
- Images (.png, .jpg, .jpeg, .gif)
- Documents (.docx, .txt, .html)`,
      category: 'feature',
      keywords: ['upload', 'file', 'import', 'drag', 'drop', 'browse'],
      icon: <HelpCircle className="w-4 h-4" />,
      priority: 'high'
    },
    {
      id: 'data-preview',
      title: 'Data Preview and Analysis',
      content: `The data preview feature automatically analyzes your uploaded data:
      
Automatic features:
- Data type detection for each column
- Statistical summaries (mean, min, max, etc.)
- Missing value detection
- Duplicate row identification
- Data quality metrics

Navigation:
- Scroll through rows using the scrollbar
- Click column headers to sort data
- Use the export buttons to download processed data
- Apply filters to focus on specific data subsets`,
      category: 'feature',
      keywords: ['preview', 'analysis', 'statistics', 'columns', 'rows', 'sort', 'filter'],
      icon: <Eye className="w-4 h-4" />,
      priority: 'high'
    },
    {
      id: 'processing-templates',
      title: 'Processing Templates',
      content: `Processing templates are pre-built Python scripts for common data tasks:
      
Available templates:
- Document Extraction: Extract structured data from PDFs and documents
- Named Entity Recognition: Identify people, organizations, dates, etc.
- Data Quality Analysis: Comprehensive data quality reports
- Knowledge Graph Generation: Build relationships between data entities
- Chunking Pipeline: Prepare data for AI/ML processing
- Embedding Generation: Create vector representations of text

Custom templates:
- Write your own Python scripts using pandas, scikit-learn, and other libraries
- Save templates for reuse
- Share templates with your team`,
      category: 'feature',
      keywords: ['processing', 'templates', 'python', 'scripts', 'transform'],
      icon: <Zap className="w-4 h-4" />,
      priority: 'high'
    },
    {
      id: 'sql-generation',
      title: 'SQL Generation and Export',
      content: `Generate database-ready SQL from your processed data:
      
Features:
- Automatic CREATE TABLE statements with appropriate data types
- INSERT statements for all your data rows
- Index creation suggestions for performance
- Foreign key relationship detection
- Export options for various database systems

Usage:
- Click "Generate SQL" after processing your data
- Review the generated SQL in the editor
- Download as .sql file or copy to clipboard
- Execute directly in your database management tool`,
      category: 'feature',
      keywords: ['sql', 'database', 'export', 'create', 'insert', 'schema'],
      icon: <Database className="w-4 h-4" />,
      priority: 'medium'
    },
    {
      id: 'troubleshooting',
      title: 'Common Issues and Solutions',
      content: `Troubleshooting guide for common problems:
      
Upload issues:
- File too large: Try breaking into smaller chunks
- Unsupported format: Convert to CSV or Excel first
- Upload stuck: Refresh the page and try again

Processing errors:
- Python script fails: Check syntax and data types
- Memory issues: Process smaller datasets or use chunking
- Slow performance: Close other browser tabs

Browser compatibility:
- Works best in Chrome, Firefox, and Edge
- Enable JavaScript for full functionality
- Some features require modern browser capabilities`,
      category: 'troubleshooting',
      keywords: ['error', 'problem', 'fix', 'solution', 'issue', 'troubleshoot'],
      icon: <AlertCircle className="w-4 h-4" />,
      priority: 'high'
    }
  ];

  // Filter topics based on search
  const filteredTopics = helpTopics.filter(topic => 
    topic.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    topic.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
    topic.keywords.some(keyword => keyword.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  // Close help when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (helpRef.current && !helpRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setActiveTopic(null);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        setIsOpen(false);
        setActiveTopic(null);
      }
      
      if (event.ctrlKey && event.key === 'h') {
        event.preventDefault();
        setIsOpen(!isOpen);
        setActiveTopic(null);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen]);

  const openHelp = (topicId?: string) => {
    setIsOpen(true);
    if (topicId) {
      setActiveTopic(topicId);
    }
    showContextualHelp(topicId || 'help_center');
  };

  const closeHelp = () => {
    setIsOpen(false);
    setActiveTopic(null);
    hideContextualHelp();
  };

  const getCategoryIcon = (category: HelpTopic['category']) => {
    switch (category) {
      case 'general': return <Info className="w-4 h-4" />;
      case 'feature': return <Lightbulb className="w-4 h-4" />;
      case 'troubleshooting': return <AlertCircle className="w-4 h-4" />;
      case 'best-practices': return <CheckCircle className="w-4 h-4" />;
      default: return <HelpCircle className="w-4 h-4" />;
    }
  };

  const getCategoryColor = (category: HelpTopic['category']) => {
    switch (category) {
      case 'general': return 'text-blue-600';
      case 'feature': return 'text-yellow-600';
      case 'troubleshooting': return 'text-red-600';
      case 'best-practices': return 'text-green-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <div className={className}>
      {/* Help Button */}
      <Button
        variant="outline"
        size="sm"
        className="fixed bottom-4 right-4 z-40 rounded-full p-3 shadow-lg"
        onClick={() => openHelp()}
        aria-label="Open help center"
      >
        <HelpCircle className="w-5 h-5" />
      </Button>

      {/* Help Panel */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            ref={helpRef}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            className="fixed bottom-20 right-4 z-50 w-full max-w-md"
          >
            <Card className="bg-white border shadow-xl rounded-lg overflow-hidden">
              {/* Header */}
              <div className="p-4 border-b bg-muted/50">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="font-semibold flex items-center gap-2">
                    <BookOpen className="w-5 h-5" />
                    Help Center
                  </h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={closeHelp}
                    className="p-1 h-auto"
                    aria-label="Close help"
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
                
                {/* Search */}
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                  <Input
                    placeholder="Search help topics..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 pr-4 py-2"
                  />
                </div>
              </div>

              {/* Content */}
              <div className="max-h-96 overflow-y-auto">
                {activeTopic ? (
                  // Topic Detail View
                  <div className="p-4">
                    {(() => {
                      const topic = helpTopics.find(t => t.id === activeTopic);
                      if (!topic) return null;
                      
                      return (
                        <>
                          <div className="flex items-center gap-2 mb-3">
                            <button
                              onClick={() => setActiveTopic(null)}
                              className="text-sm text-muted-foreground hover:text-foreground"
                            >
                              ← Back to topics
                            </button>
                          </div>
                          
                          <div className="mb-4">
                            <div className="flex items-center gap-2 mb-2">
                              {topic.icon}
                              <h4 className="font-semibold">{topic.title}</h4>
                            </div>
                            <div className="flex items-center gap-2">
                              <span className={`text-xs px-2 py-1 rounded-full ${getCategoryColor(topic.category)} bg-opacity-10`}>
                                {topic.category}
                              </span>
                              <span className="text-xs text-muted-foreground">
                                Priority: {topic.priority}
                              </span>
                            </div>
                          </div>
                          
                          <div className="prose prose-sm max-w-none">
                            {topic.content.split('\n\n').map((paragraph, idx) => (
                              <p key={idx} className="mb-3">{paragraph}</p>
                            ))}
                          </div>
                          
                          {topic.related && topic.related.length > 0 && (
                            <div className="mt-4 pt-4 border-t">
                              <h5 className="font-medium text-sm mb-2">Related Topics</h5>
                              <div className="flex flex-wrap gap-2">
                                {topic.related.map(relatedId => {
                                  const relatedTopic = helpTopics.find(t => t.id === relatedId);
                                  return relatedTopic ? (
                                    <button
                                      key={relatedId}
                                      onClick={() => setActiveTopic(relatedId)}
                                      className="text-xs bg-muted hover:bg-muted/80 px-2 py-1 rounded"
                                    >
                                      {relatedTopic.title}
                                    </button>
                                  ) : null;
                                })}
                              </div>
                            </div>
                          )}
                        </>
                      );
                    })()}
                  </div>
                ) : (
                  // Topic List View
                  <div className="p-2">
                    {filteredTopics.length === 0 ? (
                      <div className="p-8 text-center text-muted-foreground">
                        <HelpCircle className="w-12 h-12 mx-auto mb-3" />
                        <p>No help topics found</p>
                        <p className="text-sm">Try a different search term</p>
                      </div>
                    ) : (
                      <div className="space-y-1">
                        {filteredTopics.map(topic => (
                          <button
                            key={topic.id}
                            onClick={() => setActiveTopic(topic.id)}
                            className="w-full text-left p-3 hover:bg-muted rounded-lg transition-colors flex items-start gap-3"
                          >
                            <div className={`mt-1 ${getCategoryColor(topic.category)}`}>
                              {getCategoryIcon(topic.category)}
                            </div>
                            <div className="flex-1">
                              <div className="font-medium text-sm">{topic.title}</div>
                              <div className="text-xs text-muted-foreground mt-1 line-clamp-2">
                                {topic.content.split('\n')[0]}
                              </div>
                              <div className="flex items-center gap-2 mt-2">
                                <span className={`text-xs px-1.5 py-0.5 rounded ${getCategoryColor(topic.category)} bg-opacity-10`}>
                                  {topic.category}
                                </span>
                                <span className="text-xs text-muted-foreground">
                                  {topic.priority}
                                </span>
                              </div>
                            </div>
                          </button>
                        ))}
                      </div>
                    )}
                  </div>
                )}
              </div>

              {/* Footer */}
              <div className="p-3 border-t text-xs text-muted-foreground text-center">
                Press Ctrl+H to toggle help
              </div>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

// Contextual Help Trigger Component
export const HelpTrigger: React.FC<{
  topicId: string;
  children: React.ReactNode;
  className?: string;
}> = ({ topicId, children, className = '' }) => {
  const { showContextualHelp } = useOnboardingContext();
  
  return (
    <span 
      className={`inline-flex items-center gap-1 cursor-pointer group ${className}`}
      onClick={() => showContextualHelp(topicId)}
      role="button"
      tabIndex={0}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          showContextualHelp(topicId);
        }
      }}
    >
      {children}
      <HelpCircle className="w-3 h-3 text-muted-foreground group-hover:text-foreground transition-colors" />
    </span>
  );
};

// Floating Help Tooltip
export const HelpTooltip: React.FC<{
  topicId: string;
  title: string;
  content: string;
  position?: 'top' | 'bottom' | 'left' | 'right';
  children: React.ReactNode;
}> = ({ topicId, title, content, position = 'top', children }) => {
  const { showContextualHelp } = useOnboardingContext();
  const [isVisible, setIsVisible] = useState(false);
  
  return (
    <div className="relative inline-block">
      <div 
        onMouseEnter={() => setIsVisible(true)}
        onMouseLeave={() => setIsVisible(false)}
        className="inline-block"
      >
        {children}
      </div>
      
      <AnimatePresence>
        {isVisible && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            className={`absolute z-50 p-3 bg-card border rounded-lg shadow-lg w-64 ${
              position === 'top' ? 'bottom-full left-1/2 transform -translate-x-1/2 mb-2' :
              position === 'bottom' ? 'top-full left-1/2 transform -translate-x-1/2 mt-2' :
              position === 'left' ? 'right-full top-1/2 transform -translate-y-1/2 mr-2' :
              'left-full top-1/2 transform -translate-y-1/2 ml-2'
            }`}
          >
            <div className="flex items-start justify-between mb-2">
              <h4 className="font-medium text-sm">{title}</h4>
              <Button
                variant="ghost"
                size="sm"
                className="p-1 h-auto"
                onClick={() => showContextualHelp(topicId)}
              >
                <HelpCircle className="w-3 h-3" />
              </Button>
            </div>
            <p className="text-xs text-muted-foreground">{content}</p>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};