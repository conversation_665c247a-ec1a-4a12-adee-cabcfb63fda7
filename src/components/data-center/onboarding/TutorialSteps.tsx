import React, { useState, useEffect, useRef } from 'react';
import {
  <PERSON>,
  <PERSON>R<PERSON>,
  ArrowLeft,
  Check,
  Play,
  Lightbulb,
  MousePointer,
  Keyboard,
  BookOpen
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { useOnboardingContext } from './OnboardingProvider';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { OnboardingStep } from './types';

interface TutorialStepsProps {
  tutorialId: string;
  onComplete?: () => void;
  onSkip?: () => void;
}

export const TutorialSteps: React.FC<TutorialStepsProps> = ({
  tutorialId,
  onComplete,
  onSkip
}) => {
  const {
    currentTutorial,
    currentStep,
    isActive,
    nextStep,
    previousStep,
    skipTutorial,
    completeTutorial,
    progress
  } = useOnboardingContext();
  
  const [highlightCleanup, setHighlightCleanup] = useState<(() => void) | null>(null);
  const [isAnimating, setIsAnimating] = useState(false);
  const stepRef = useRef<HTMLDivElement>(null);

  // Check if this is the active tutorial
  const isCurrentTutorial = isActive && currentTutorial?.id === tutorialId;

  // Handle tutorial completion
  const handleComplete = () => {
    completeTutorial();
    onComplete?.();
  };

  // Handle tutorial skip
  const handleSkip = () => {
    skipTutorial();
    onSkip?.();
  };

  // Handle step navigation with animation
  const handleNext = () => {
    if (isAnimating) return;
    
    setIsAnimating(true);
    // Clean up any previous highlights
    if (highlightCleanup) {
      highlightCleanup();
      setHighlightCleanup(null);
    }
    
    setTimeout(() => {
      nextStep();
      setIsAnimating(false);
    }, 150);
  };

  const handlePrevious = () => {
    if (isAnimating) return;
    
    setIsAnimating(true);
    // Clean up any previous highlights
    if (highlightCleanup) {
      highlightCleanup();
      setHighlightCleanup(null);
    }
    
    setTimeout(() => {
      previousStep();
      setIsAnimating(false);
    }, 150);
  };

  // Apply highlighting and positioning for current step
  useEffect(() => {
    if (!isCurrentTutorial || !currentTutorial || currentStep >= currentTutorial.steps.length) {
      return;
    }

    const step = currentTutorial.steps[currentStep] as OnboardingStep;
    
    // Execute onEnter callback
    if (step.onEnter) {
      step.onEnter();
    }

    // Apply highlighting if target element exists
    if (step.targetElement) {
      const timer = setTimeout(() => {
        const element = document.querySelector(step.targetElement!);
        if (element) {
          // Scroll to element
          element.scrollIntoView({
            behavior: 'smooth',
            block: step.position === 'top' ? 'center' : 'nearest',
            inline: 'nearest'
          });
          
          // Add highlight effect
          const el = element as HTMLElement;
          const originalStyle = {
            boxShadow: el.style.boxShadow,
            position: el.style.position,
            zIndex: el.style.zIndex
          };
          
          el.style.transition = 'all 0.3s ease';
          el.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.5)';
          el.style.position = 'relative';
          el.style.zIndex = '1000';
          
          // Store cleanup function
          setHighlightCleanup(() => () => {
            el.style.boxShadow = originalStyle.boxShadow;
            el.style.position = originalStyle.position;
            el.style.zIndex = originalStyle.zIndex;
          });
        }
      }, 300);
      
      return () => {
        clearTimeout(timer);
        if (highlightCleanup) {
          highlightCleanup();
          setHighlightCleanup(null);
        }
      };
    }
  }, [currentStep, currentTutorial, isCurrentTutorial, highlightCleanup]);

  // Clean up on unmount
  useEffect(() => {
    return () => {
      if (highlightCleanup) {
        highlightCleanup();
      }
    };
  }, [highlightCleanup]);

  if (!isCurrentTutorial || !currentTutorial) {
    return null;
  }

  const step = currentTutorial.steps[currentStep] as OnboardingStep;
  const isLastStep = currentStep === currentTutorial.steps.length - 1;
  const isFirstStep = currentStep === 0;

  return (
    <AnimatePresence>
      {isActive && (
        <motion.div
          ref={stepRef}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            transition={{ type: 'spring', damping: 25, stiffness: 300 }}
            className="bg-white rounded-xl shadow-2xl max-w-lg w-full overflow-hidden"
          >
            {/* Header */}
            <div className="p-5 border-b bg-gradient-to-r from-blue-50 to-indigo-50">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <Lightbulb className="w-5 h-5 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">{currentTutorial.title}</h3>
                    <p className="text-sm text-muted-foreground">{currentTutorial.description}</p>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleSkip}
                  className="p-1 h-auto"
                  aria-label="Skip tutorial"
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
              
              {/* Progress */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Badge variant="secondary" className="text-xs">
                    Step {currentStep + 1} of {currentTutorial.steps.length}
                  </Badge>
                  <Badge variant="outline" className="text-xs">
                    {currentTutorial.duration}
                  </Badge>
                </div>
                <div className="flex-1 mx-4">
                  <div className="w-full bg-muted rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${((currentStep + 1) / currentTutorial.steps.length) * 100}%` }}
                    />
                  </div>
                </div>
              </div>
            </div>
            
            {/* Content */}
            <div className="p-6">
              <div className="mb-6">
                <div className="flex items-center gap-2 mb-3">
                  <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
                    <span className="text-sm font-semibold text-blue-600">{currentStep + 1}</span>
                  </div>
                  <h4 className="font-semibold text-lg">{step.title}</h4>
                </div>
                
                <div className="prose prose-sm max-w-none">
                  {step.content}
                </div>
              </div>
              
              {/* Actions */}
              {step.actions && step.actions.length > 0 && (
                <div className="flex flex-wrap gap-2 mb-6">
                  {step.actions.map(action => (
                    <Button
                      key={action.id}
                      variant={action.primary ? "default" : "outline"}
                      size="sm"
                      onClick={action.onClick}
                      className="flex items-center gap-2"
                    >
                      {action.icon}
                      {action.label}
                    </Button>
                  ))}
                </div>
              )}
              
              {/* Navigation */}
              <div className="flex items-center justify-between">
                <div>
                  {!isFirstStep && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handlePrevious}
                      disabled={isAnimating}
                      className="flex items-center gap-2"
                    >
                      <ArrowLeft className="w-4 h-4" />
                      Previous
                    </Button>
                  )}
                </div>
                
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleSkip}
                    className="text-muted-foreground hover:text-foreground"
                  >
                    Skip Tutorial
                  </Button>
                  
                  {isLastStep ? (
                    <Button
                      onClick={handleComplete}
                      className="flex items-center gap-2"
                    >
                      <Check className="w-4 h-4" />
                      Complete Tutorial
                    </Button>
                  ) : (
                    <Button
                      onClick={handleNext}
                      disabled={isAnimating}
                      className="flex items-center gap-2"
                    >
                      Next
                      <ArrowRight className="w-4 h-4" />
                    </Button>
                  )}
                </div>
              </div>
            </div>
            
            {/* Footer */}
            <div className="p-4 border-t bg-muted/30 text-center">
              <div className="flex items-center justify-center gap-4 text-xs text-muted-foreground">
                <div className="flex items-center gap-1">
                  <MousePointer className="w-3 h-3" />
                  Click elements to interact
                </div>
                <div className="flex items-center gap-1">
                  <Keyboard className="w-3 h-3" />
                  Use keyboard shortcuts
                </div>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

// Tutorial Welcome Component
export const TutorialWelcome: React.FC<{
  tutorialId: string;
  onStart: () => void;
  onSkip: () => void;
}> = ({ tutorialId, onStart, onSkip }) => {
  const { progress } = useOnboardingContext();
  const isCompleted = progress.completedTutorials.includes(tutorialId);
  
  // Tutorial metadata
  const tutorialInfo = {
    dataCenterBasics: {
      title: 'Data Center Basics',
      description: 'Learn the fundamentals of uploading, processing, and analyzing data',
      duration: '5 minutes',
      icon: <BookOpen className="w-6 h-6" />,
      features: [
        'File Upload & Processing',
        'Data Preview & Analysis',
        'Template-based Processing',
        'SQL Generation & Export'
      ]
    },
    advancedFeatures: {
      title: 'Advanced Features',
      description: 'Master advanced data processing and visualization techniques',
      duration: '8 minutes',
      icon: <Lightbulb className="w-6 h-6" />,
      features: [
        'Custom Python Templates',
        'Interactive Data Exploration',
        'Advanced Analytics',
        'Performance Optimization'
      ]
    }
  }[tutorialId] || {
    title: 'Tutorial',
    description: 'Learn how to use this feature',
    duration: '5 minutes',
    icon: <BookOpen className="w-6 h-6" />,
    features: []
  };

  if (isCompleted) return null;

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
    >
      <Card className="max-w-md w-full overflow-hidden">
        <div className="p-6">
          <div className="text-center mb-6">
            <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
              {tutorialInfo.icon}
            </div>
            <h3 className="text-xl font-bold mb-2">{tutorialInfo.title}</h3>
            <p className="text-muted-foreground">{tutorialInfo.description}</p>
          </div>
          
          <div className="space-y-4 mb-6">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Duration</span>
              <span className="font-medium">{tutorialInfo.duration}</span>
            </div>
            
            {tutorialInfo.features.length > 0 && (
              <div>
                <h4 className="font-medium text-sm mb-2">What you'll learn:</h4>
                <ul className="space-y-2">
                  {tutorialInfo.features.map((feature, index) => (
                    <li key={index} className="flex items-center gap-2 text-sm">
                      <Check className="w-4 h-4 text-green-500" />
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
          
          <div className="flex flex-col gap-3">
            <Button 
              onClick={onStart}
              className="w-full flex items-center justify-center gap-2"
            >
              <Play className="w-4 h-4" />
              Start Tutorial
            </Button>
            <Button 
              variant="outline" 
              onClick={onSkip}
              className="w-full"
            >
              Skip for now
            </Button>
          </div>
        </div>
      </Card>
    </motion.div>
  );
};

// Tutorial Progress Tracker
export const TutorialProgress: React.FC = () => {
  const { progress } = useOnboardingContext();
  
  const tutorials = [
    { id: 'dataCenterBasics', title: 'Data Center Basics', category: 'Essential' },
    { id: 'advancedFeatures', title: 'Advanced Features', category: 'Advanced' }
  ];
  
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="font-semibold">Learning Progress</h3>
        <Badge variant="secondary">
          {progress.completedTutorials.length} of {tutorials.length} completed
        </Badge>
      </div>
      
      <div className="space-y-3">
        {tutorials.map(tutorial => {
          const isCompleted = progress.completedTutorials.includes(tutorial.id);
          
          return (
            <Card key={tutorial.id} className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="flex items-center gap-2 mb-1">
                    <h4 className="font-medium text-sm">{tutorial.title}</h4>
                    <Badge variant="outline" className="text-xs">
                      {tutorial.category}
                    </Badge>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {tutorial.id === 'dataCenterBasics' ? '5 minutes' : '8 minutes'}
                  </p>
                </div>
                
                {isCompleted ? (
                  <div className="p-2 bg-green-100 rounded-full">
                    <Check className="w-4 h-4 text-green-600" />
                  </div>
                ) : (
                  <div className="w-8 h-8 rounded-full border-2 border-dashed border-muted-foreground" />
                )}
              </div>
            </Card>
          );
        })}
      </div>
    </div>
  );
};