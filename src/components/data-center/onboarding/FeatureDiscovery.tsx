import React, { useState, useEffect, useRef } from 'react';
import { 
  Lightbulb, 
  X, 
  Star, 
  Check, 
  Eye,
  Zap,
  Database,
  Code,
  BarChart3,
  Wrench,
  FileText,
  Upload
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { useOnboardingContext } from './OnboardingProvider';
import { useDataCenter } from '../DataCenterContext';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

interface FeatureDiscoveryItem {
  id: string;
  title: string;
  description: string;
  targetElement: string;
  position: 'top' | 'bottom' | 'left' | 'right';
  icon: React.ReactNode;
  priority: 'high' | 'medium' | 'low';
  condition?: () => boolean;
  onDiscover?: () => void;
}

interface FeatureDiscoveryProps {
  className?: string;
}

export const FeatureDiscovery: React.FC<FeatureDiscoveryProps> = ({ 
  className = '' 
}) => {
  const { progress, markFeatureDismissed, hideFeature } = useOnboardingContext();
  const { pipelineState } = useDataCenter();
  const [activeFeature, setActiveFeature] = useState<string | null>(null);
  const [tooltipVisible, setTooltipVisible] = useState<Record<string, boolean>>({});
  const [badgesVisible, setBadgesVisible] = useState<Record<string, boolean>>({});
  const tooltipRef = useRef<HTMLDivElement>(null);

  // Feature definitions
  const features: FeatureDiscoveryItem[] = [
    {
      id: 'upload-section',
      title: 'File Upload',
      description: 'Upload CSV, Excel, JSON, and other file formats for processing',
      targetElement: '[data-tutorial="upload-section"]',
      position: 'bottom',
      icon: <Upload className="w-4 h-4" />,
      priority: 'high',
      condition: () => pipelineState.currentStage === 'upload'
    },
    {
      id: 'preview-data',
      title: 'Data Preview',
      description: 'View and explore your data with automatic type detection',
      targetElement: '[data-tutorial="preview-section"]',
      position: 'top',
      icon: <Eye className="w-4 h-4" />,
      priority: 'high',
      condition: () => !!pipelineState.dataPreview
    },
    {
      id: 'processing-templates',
      title: 'Processing Templates',
      description: 'Apply powerful transformations with pre-built Python templates',
      targetElement: '[data-tutorial="processing-section"]',
      position: 'left',
      icon: <Zap className="w-4 h-4" />,
      priority: 'high',
      condition: () => pipelineState.currentStage === 'processing'
    },
    {
      id: 'sql-generation',
      title: 'SQL Generation',
      description: 'Generate database-ready SQL statements from your processed data',
      targetElement: '[data-tutorial="sql-section"]',
      position: 'top',
      icon: <Database className="w-4 h-4" />,
      priority: 'high',
      condition: () => !!pipelineState.sqlResult
    },
    {
      id: 'data-exploration',
      title: 'Data Exploration',
      description: 'Create visualizations and analyze your data patterns',
      targetElement: '[data-tutorial="explore-section"]',
      position: 'bottom',
      icon: <BarChart3 className="w-4 h-4" />,
      priority: 'medium',
      condition: () => pipelineState.currentStage === 'explore'
    }
  ];

  // Filter features based on conditions and user progress
  const availableFeatures = features.filter(feature => {
    // Skip if already discovered
    if (progress.dismissedFeatures.includes(feature.id)) {
      return false;
    }
    
    // Check condition if provided
    if (feature.condition && !feature.condition()) {
      return false;
    }
    
    return true;
  });

  // Show badges for undiscovered features
  useEffect(() => {
    const newBadgesVisible: Record<string, boolean> = {};
    
    availableFeatures.forEach(feature => {
      const element = document.querySelector(feature.targetElement);
      if (element) {
        newBadgesVisible[feature.id] = true;
      }
    });
    
    setBadgesVisible(newBadgesVisible);
  }, [availableFeatures, pipelineState.currentStage]);

  // Handle click outside to close tooltips
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (tooltipRef.current && !tooltipRef.current.contains(event.target as Node)) {
        setActiveFeature(null);
      }
    };

    if (activeFeature) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [activeFeature]);

  // Handle feature discovery
  const discoverFeature = (featureId: string) => {
    markFeatureDismissed(featureId);
    setActiveFeature(null);
    
    // Call onDiscover callback if exists
    const feature = features.find(f => f.id === featureId);
    if (feature && feature.onDiscover) {
      feature.onDiscover();
    }
  };

  // Get position styles for tooltips
  const getTooltipPosition = (feature: FeatureDiscoveryItem) => {
    const element = document.querySelector(feature.targetElement);
    if (!element) return {};
    
    const rect = element.getBoundingClientRect();
    const tooltipPadding = 10;
    
    switch (feature.position) {
      case 'top':
        return {
          top: rect.top - tooltipPadding,
          left: rect.left + rect.width / 2,
          transform: 'translate(-50%, -100%)'
        };
      case 'bottom':
        return {
          top: rect.bottom + tooltipPadding,
          left: rect.left + rect.width / 2,
          transform: 'translate(-50%, 0)'
        };
      case 'left':
        return {
          top: rect.top + rect.height / 2,
          left: rect.left - tooltipPadding,
          transform: 'translate(-100%, -50%)'
        };
      case 'right':
        return {
          top: rect.top + rect.height / 2,
          left: rect.right + tooltipPadding,
          transform: 'translate(0, -50%)'
        };
      default:
        return {
          top: rect.top + rect.height / 2,
          left: rect.left + rect.width / 2,
          transform: 'translate(-50%, -50%)'
        };
    }
  };

  // Get icon for feature priority
  const getPriorityIcon = (priority: FeatureDiscoveryItem['priority']) => {
    switch (priority) {
      case 'high': return <Star className="w-3 h-3 text-yellow-500" />;
      case 'medium': return <Lightbulb className="w-3 h-3 text-blue-500" />;
      case 'low': return <Eye className="w-3 h-3 text-gray-500" />;
      default: return <Lightbulb className="w-3 h-3 text-gray-500" />;
    }
  };

  return (
    <div className={className}>
      {/* Feature Badges */}
      {availableFeatures.map(feature => {
        const element = document.querySelector(feature.targetElement);
        if (!element || !badgesVisible[feature.id]) return null;
        
        const rect = element.getBoundingClientRect();
        
        return (
          <motion.div
            key={feature.id}
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0, opacity: 0 }}
            className="fixed z-40 pointer-events-none"
            style={{
              top: rect.top - 8,
              left: rect.right - 8,
              transform: 'translate(50%, -50%)'
            }}
          >
            <Button
              variant="default"
              size="sm"
              className="pointer-events-auto rounded-full p-1.5 h-auto shadow-lg bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
              onClick={(e) => {
                e.stopPropagation();
                setActiveFeature(feature.id);
              }}
              aria-label={`Discover ${feature.title}`}
            >
              <Lightbulb className="w-3 h-3 text-white" />
            </Button>
          </motion.div>
        );
      })}
      
      {/* Feature Tooltips */}
      <AnimatePresence>
        {activeFeature && (
          <motion.div
            ref={tooltipRef}
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            className="fixed z-50 p-4 bg-card border rounded-lg shadow-xl w-72"
            style={getTooltipPosition(features.find(f => f.id === activeFeature)!)}
          >
            {(() => {
              const feature = features.find(f => f.id === activeFeature);
              if (!feature) return null;
              
              return (
                <>
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center gap-2">
                      <div className="p-1.5 bg-blue-100 rounded-md text-blue-600">
                        {feature.icon}
                      </div>
                      <div>
                        <h4 className="font-semibold text-sm">{feature.title}</h4>
                        <div className="flex items-center gap-1">
                          {getPriorityIcon(feature.priority)}
                          <span className="text-xs text-muted-foreground capitalize">
                            {feature.priority} priority
                          </span>
                        </div>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="p-1 h-auto"
                      onClick={() => setActiveFeature(null)}
                      aria-label="Close tooltip"
                    >
                      <X className="w-3 h-3" />
                    </Button>
                  </div>
                  
                  <p className="text-sm text-muted-foreground mb-4">
                    {feature.description}
                  </p>
                  
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1 text-xs"
                      onClick={() => setActiveFeature(null)}
                    >
                      Later
                    </Button>
                    <Button
                      size="sm"
                      className="flex-1 text-xs"
                      onClick={() => discoverFeature(feature.id)}
                    >
                      <Check className="w-3 h-3 mr-1" />
                      Got it
                    </Button>
                  </div>
                </>
              );
            })()}
          </motion.div>
        )}
      </AnimatePresence>
      
      {/* Progress Badge */}
      {availableFeatures.length > 0 && (
        <div className="fixed bottom-4 left-4 z-40">
          <Badge 
            variant="secondary" 
            className="px-3 py-2 text-sm cursor-pointer hover:bg-secondary/80"
            onClick={() => {
              // Show first available feature
              if (availableFeatures.length > 0) {
                setActiveFeature(availableFeatures[0].id);
              }
            }}
          >
            <Lightbulb className="w-4 h-4 mr-1" />
            {availableFeatures.length} features to discover
          </Badge>
        </div>
      )}
    </div>
  );
};

// Achievement Badges Component
export const AchievementBadges: React.FC = () => {
  const { progress } = useOnboardingContext();
  
  const achievements = [
    {
      id: 'first-upload',
      title: 'First Upload',
      description: 'Uploaded your first data file',
      icon: <Upload className="w-4 h-4" />,
      condition: () => !!progress.completedSteps.includes('upload-intro')
    },
    {
      id: 'data-previewed',
      title: 'Data Explorer',
      description: 'Previewed data successfully',
      icon: <Eye className="w-4 h-4" />,
      condition: () => !!progress.completedSteps.includes('preview-data')
    },
    {
      id: 'template-used',
      title: 'Template Master',
      description: 'Used a processing template',
      icon: <Zap className="w-4 h-4" />,
      condition: () => !!progress.completedSteps.includes('processing-templates')
    },
    {
      id: 'sql-generated',
      title: 'SQL Expert',
      description: 'Generated SQL from data',
      icon: <Database className="w-4 h-4" />,
      condition: () => !!progress.completedSteps.includes('sql-generation')
    }
  ];
  
  const earnedAchievements = achievements.filter(achievement => achievement.condition());
  
  if (earnedAchievements.length === 0) return null;
  
  return (
    <div className="fixed top-4 right-4 z-40 flex flex-col items-end gap-2">
      <AnimatePresence>
        {earnedAchievements.map((achievement, index) => (
          <motion.div
            key={achievement.id}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 20 }}
            transition={{ delay: index * 0.1 }}
            className="bg-card border rounded-lg p-3 shadow-lg flex items-center gap-2"
          >
            <div className="p-1.5 bg-green-100 rounded-md text-green-600">
              {achievement.icon}
            </div>
            <div>
              <p className="font-medium text-sm">{achievement.title}</p>
              <p className="text-xs text-muted-foreground">{achievement.description}</p>
            </div>
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  );
};

// Discovery Progress Component
export const DiscoveryProgress: React.FC = () => {
  const { progress } = useOnboardingContext();
  
  // Total possible features (from our feature definitions)
  const totalFeatures = 5;
  const discoveredFeatures = progress.dismissedFeatures.length;
  const progressPercentage = totalFeatures > 0 ? (discoveredFeatures / totalFeatures) * 100 : 0;
  
  return (
    <div className="p-4 bg-muted/30 rounded-lg">
      <div className="flex items-center justify-between mb-2">
        <h4 className="font-medium text-sm flex items-center gap-2">
          <Lightbulb className="w-4 h-4" />
          Discovery Progress
        </h4>
        <Badge variant="secondary" className="text-xs">
          {discoveredFeatures}/{totalFeatures}
        </Badge>
      </div>
      
      <div className="w-full bg-muted rounded-full h-2">
        <motion.div
          className="bg-blue-600 h-2 rounded-full"
          initial={{ width: 0 }}
          animate={{ width: `${progressPercentage}%` }}
          transition={{ duration: 0.5 }}
        />
      </div>
      
      <p className="text-xs text-muted-foreground mt-2">
        {progressPercentage === 100 
          ? "You've discovered all features! 🎉" 
          : `Discover ${totalFeatures - discoveredFeatures} more features`}
      </p>
    </div>
  );
};