import React, { useState, useEffect } from 'react';
import { useNavigation } from './NavigationProvider';
import { Card, Button, Badge } from '../ui/StyledComponents';
import { 
  Keyboard, 
  Search, 
  Upload, 
  Eye, 
  Wrench, 
  Layers, 
  Brain, 
  FileOutput,
  Settings,
  HelpCircle,
  X
} from 'lucide-react';

interface KeyboardShortcutsProps {
  isOpen: boolean;
  onClose: () => void;
}

export const KeyboardShortcuts: React.FC<KeyboardShortcutsProps> = ({ 
  isOpen, 
  onClose 
}) => {
  const { shortcuts } = useNavigation();
  const [activeCategory, setActiveCategory] = useState('all');
  
  // Group shortcuts by category
  const groupedShortcuts = shortcuts.reduce((acc, shortcut) => {
    const category = shortcut.scope || 'General';
    if (!acc[category]) acc[category] = [];
    acc[category].push(shortcut);
    return acc;
  }, {} as Record<string, typeof shortcuts>);
  
  const categories = Object.keys(groupedShortcuts);
  
  // Handle escape key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        onClose();
      }
    };
    
    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
    }
    
    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, [isOpen, onClose]);
  
  // Format shortcut for display
  const formatShortcut = (shortcut: any) => {
    const parts = [];
    if (shortcut.ctrl) parts.push('Ctrl');
    if (shortcut.shift) parts.push('Shift');
    if (shortcut.alt) parts.push('Alt');
    if (shortcut.meta) parts.push('Cmd');
    
    const key = shortcut.key === ' ' ? 'Space' : 
                shortcut.key === 'enter' ? 'Enter' :
                shortcut.key === 'escape' ? 'Esc' :
                shortcut.key.charAt(0).toUpperCase() + shortcut.key.slice(1);
    
    parts.push(key);
    return parts.join('+');
  };
  
  if (!isOpen) return null;
  
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black/50 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* Shortcuts Panel */}
      <div className="relative w-full max-w-3xl bg-card border rounded-lg shadow-xl overflow-hidden z-10 max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="p-4 border-b flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Keyboard className="w-5 h-5 text-primary" />
            <h3 className="text-lg font-semibold">Keyboard Shortcuts</h3>
          </div>
          <button
            onClick={onClose}
            className="p-1.5 hover:bg-muted rounded transition-colors"
            aria-label="Close"
          >
            <X className="w-5 h-5" />
          </button>
        </div>
        
        {/* Categories */}
        <div className="px-4 py-3 border-b flex gap-2 overflow-x-auto">
          <button
            onClick={() => setActiveCategory('all')}
            className={`px-3 py-1.5 text-sm rounded-full whitespace-nowrap ${
              activeCategory === 'all'
                ? 'bg-primary text-primary-foreground'
                : 'bg-muted hover:bg-muted/70'
            }`}
          >
            All Shortcuts
          </button>
          {categories.map(category => (
            <button
              key={category}
              onClick={() => setActiveCategory(category)}
              className={`px-3 py-1.5 text-sm rounded-full whitespace-nowrap ${
                activeCategory === category
                  ? 'bg-primary text-primary-foreground'
                  : 'bg-muted hover:bg-muted/70'
              }`}
            >
              {category}
            </button>
          ))}
        </div>
        
        {/* Shortcuts List */}
        <div className="flex-1 overflow-y-auto p-4">
          {activeCategory === 'all' ? (
            Object.entries(groupedShortcuts).map(([category, categoryShortcuts]) => (
              <div key={category} className="mb-6">
                <h4 className="text-sm font-medium text-foreground mb-3 border-b pb-1">
                  {category}
                </h4>
                <div className="space-y-2">
                  {categoryShortcuts.map((shortcut, index) => (
                    <div 
                      key={index} 
                      className="flex items-center justify-between p-3 bg-muted rounded-lg"
                    >
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-background rounded">
                          {category === 'navigation' && <Search className="w-4 h-4" />}
                          {category === 'file' && <Upload className="w-4 h-4" />}
                          {category === 'view' && <Eye className="w-4 h-4" />}
                          {category === 'edit' && <Wrench className="w-4 h-4" />}
                          {category === 'action' && <Layers className="w-4 h-4" />}
                          {category === 'system' && <Settings className="w-4 h-4" />}
                          {!['navigation', 'file', 'view', 'edit', 'action', 'system'].includes(category) && (
                            <HelpCircle className="w-4 h-4" />
                          )}
                        </div>
                        <div>
                          <p className="font-medium">{shortcut.description}</p>
                          <p className="text-sm text-muted-foreground">
                            {category}
                          </p>
                        </div>
                      </div>
                      <kbd className="px-2.5 py-1 text-sm font-mono bg-background border rounded">
                        {formatShortcut(shortcut)}
                      </kbd>
                    </div>
                  ))}
                </div>
              </div>
            ))
          ) : (
            <div className="space-y-4">
              {groupedShortcuts[activeCategory]?.map((shortcut, index) => (
                <div 
                  key={index} 
                  className="flex items-center justify-between p-3 bg-muted rounded-lg"
                >
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-background rounded">
                      {activeCategory === 'navigation' && <Search className="w-4 h-4" />}
                      {activeCategory === 'file' && <Upload className="w-4 h-4" />}
                      {activeCategory === 'view' && <Eye className="w-4 h-4" />}
                      {activeCategory === 'edit' && <Wrench className="w-4 h-4" />}
                      {activeCategory === 'action' && <Layers className="w-4 h-4" />}
                      {activeCategory === 'system' && <Settings className="w-4 h-4" />}
                      {!['navigation', 'file', 'view', 'edit', 'action', 'system'].includes(activeCategory) && (
                        <HelpCircle className="w-4 h-4" />
                      )}
                    </div>
                    <div>
                      <p className="font-medium">{shortcut.description}</p>
                    </div>
                  </div>
                  <kbd className="px-2.5 py-1 text-sm font-mono bg-background border rounded">
                    {formatShortcut(shortcut)}
                  </kbd>
                </div>
              )) || (
                <div className="text-center py-8">
                  <HelpCircle className="w-12 h-12 mx-auto text-muted-foreground/50" />
                  <p className="mt-2 text-muted-foreground">No shortcuts found in this category</p>
                </div>
              )}
            </div>
          )}
        </div>
        
        {/* Footer */}
        <div className="p-4 border-t bg-muted/30 text-sm text-muted-foreground flex items-center justify-between">
          <div className="flex items-center gap-4">
            <span>Press <kbd className="px-1.5 py-0.5 bg-background border rounded">Esc</kbd> to close</span>
            <span>Press <kbd className="px-1.5 py-0.5 bg-background border rounded">?</kbd> to toggle help</span>
          </div>
          <span>{shortcuts.length} shortcuts available</span>
        </div>
      </div>
    </div>
  );
};