import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { searchIndex, SearchableItem } from './SearchIndex';
import { DataPipelineState } from '../DataCenterContext';

interface NavigationContextType {
  // Search state
  searchQuery: string;
  searchResults: SearchableItem[];
  searchHistory: string[];
  searchSuggestions: string[];
  isSearching: boolean;
  
  // Quick actions state
  quickActions: QuickAction[];
  customActions: QuickAction[];
  
  // Tab organization state
  tabGroups: TabGroup[];
  activeTabGroup: string;
  
  // Keyboard shortcuts state
  shortcuts: KeyboardShortcut[];
  
  // Search functions
  setSearchQuery: (query: string) => void;
  performSearch: (query: string, filters?: { type?: string[]; category?: string[] }) => void;
  clearSearch: () => void;
  addToSearchHistory: (query: string) => void;
  
  // Quick actions functions
  addQuickAction: (action: QuickAction) => void;
  removeQuickAction: (id: string) => void;
  updateQuickAction: (id: string, updates: Partial<QuickAction>) => void;
  addCustomAction: (action: QuickAction) => void;
  
  // Tab organization functions
  addTabGroup: (group: TabGroup) => void;
  removeTabGroup: (id: string) => void;
  updateTabGroup: (id: string, updates: Partial<TabGroup>) => void;
  setActiveTabGroup: (id: string) => void;
  
  // Keyboard shortcuts functions
  registerShortcut: (shortcut: KeyboardShortcut) => void;
  unregisterShortcut: (id: string) => void;
  
  // Index pipeline state
  indexPipelineState: (state: DataPipelineState) => void;
}

interface QuickAction {
  id: string;
  label: string;
  icon: React.ReactNode;
  onClick: () => void;
  shortcut?: string;
  category?: string;
  disabled?: boolean;
  priority?: number;
}

interface TabGroup {
  id: string;
  name: string;
  tabs: string[];
  icon?: React.ReactNode;
  color?: string;
}

interface KeyboardShortcut {
  id: string;
  key: string;
  ctrl?: boolean;
  shift?: boolean;
  alt?: boolean;
  meta?: boolean;
  callback: () => void;
  description: string;
  scope?: string;
}

const NavigationContext = createContext<NavigationContextType | undefined>(undefined);

export const NavigationProvider: React.FC<{ 
  children: ReactNode;
  onDataChange?: (data: Partial<DataPipelineState>) => void;
}> = ({ children, onDataChange }) => {
  // Search state
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<SearchableItem[]>([]);
  const [searchHistory, setSearchHistory] = useState<string[]>([]);
  const [searchSuggestions, setSearchSuggestions] = useState<string[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  
  // Quick actions state
  const [quickActions, setQuickActions] = useState<QuickAction[]>([]);
  const [customActions, setCustomActions] = useState<QuickAction[]>([]);
  
  // Tab organization state
  const [tabGroups, setTabGroups] = useState<TabGroup[]>([
    {
      id: 'data-operations',
      name: 'Data Operations',
      tabs: ['upload', 'preview', 'explore'],
      color: 'dataOperation'
    },
    {
      id: 'processing',
      name: 'Processing',
      tabs: ['processing', 'analysis', 'sql'],
      color: 'warning'
    }
  ]);
  const [activeTabGroup, setActiveTabGroup] = useState('data-operations');
  
  // Keyboard shortcuts state
  const [shortcuts, setShortcuts] = useState<KeyboardShortcut[]>([]);
  
  // Initialize with default quick actions
  useEffect(() => {
    const defaultActions: QuickAction[] = [
      {
        id: 'search',
        label: 'Search',
        icon: <SearchIcon />,
        onClick: () => {},
        shortcut: 'Ctrl+K',
        category: 'navigation',
        priority: 1
      },
      {
        id: 'refresh',
        label: 'Refresh',
        icon: <RefreshIcon />,
        onClick: () => {},
        shortcut: 'Ctrl+R',
        category: 'view',
        priority: 2
      },
      {
        id: 'export',
        label: 'Export',
        icon: <ExportIcon />,
        onClick: () => {},
        shortcut: 'Ctrl+E',
        category: 'file',
        priority: 3
      },
      {
        id: 'settings',
        label: 'Settings',
        icon: <SettingsIcon />,
        onClick: () => {},
        category: 'system',
        priority: 4
      }
    ];
    
    setQuickActions(defaultActions);
  }, []);
  
  // Search functions
  const performSearch = (query: string, filters?: { type?: string[]; category?: string[] }) => {
    if (!query.trim()) {
      setSearchResults([]);
      setSearchSuggestions([]);
      return;
    }
    
    setIsSearching(true);
    try {
      const results = searchIndex.search(query, filters);
      setSearchResults(results);
      setSearchSuggestions(searchIndex.getSuggestions(query, 5));
    } finally {
      setIsSearching(false);
    }
  };
  
  const clearSearch = () => {
    setSearchQuery('');
    setSearchResults([]);
    setSearchSuggestions([]);
  };
  
  const addToSearchHistory = (query: string) => {
    searchIndex.addToSearchHistory(query);
    // In a real implementation, we would update searchHistory state from localStorage
  };
  
  // Quick actions functions
  const addQuickAction = (action: QuickAction) => {
    setQuickActions(prev => [...prev, action]);
  };
  
  const removeQuickAction = (id: string) => {
    setQuickActions(prev => prev.filter(action => action.id !== id));
  };
  
  const updateQuickAction = (id: string, updates: Partial<QuickAction>) => {
    setQuickActions(prev => 
      prev.map(action => 
        action.id === id ? { ...action, ...updates } : action
      )
    );
  };
  
  const addCustomAction = (action: QuickAction) => {
    setCustomActions(prev => [...prev, action]);
  };
  
  // Tab organization functions
  const addTabGroup = (group: TabGroup) => {
    setTabGroups(prev => [...prev, group]);
  };
  
  const removeTabGroup = (id: string) => {
    setTabGroups(prev => prev.filter(group => group.id !== id));
  };
  
  const updateTabGroup = (id: string, updates: Partial<TabGroup>) => {
    setTabGroups(prev => 
      prev.map(group => 
        group.id === id ? { ...group, ...updates } : group
      )
    );
  };
  
  // Keyboard shortcuts functions
  const registerShortcut = (shortcut: KeyboardShortcut) => {
    setShortcuts(prev => {
      // Remove existing shortcut with same id
      const filtered = prev.filter(s => s.id !== shortcut.id);
      return [...filtered, shortcut];
    });
  };
  
  const unregisterShortcut = (id: string) => {
    setShortcuts(prev => prev.filter(s => s.id !== id));
  };
  
  // Index pipeline state
  const indexPipelineState = (state: DataPipelineState) => {
    searchIndex.indexPipelineState(state);
  };
  
  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Don't trigger shortcuts when in input fields
      const target = event.target as HTMLElement;
      if (target?.tagName === 'INPUT' || target?.tagName === 'TEXTAREA' || target?.contentEditable === 'true') {
        return;
      }
      
      const matchingShortcut = shortcuts.find(shortcut => matchesShortcut(event, shortcut));
      
      if (matchingShortcut) {
        event.preventDefault();
        matchingShortcut.callback();
      }
    };
    
    const matchesShortcut = (event: KeyboardEvent, shortcut: KeyboardShortcut): boolean => {
      const eventKey = event.key.toLowerCase();
      const shortcutKey = shortcut.key.toLowerCase();
      
      return (
        eventKey === shortcutKey &&
        !!event.ctrlKey === !!shortcut.ctrl &&
        !!event.shiftKey === !!shortcut.shift &&
        !!event.altKey === !!shortcut.alt &&
        !!event.metaKey === !!shortcut.meta
      );
    };
    
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [shortcuts]);
  
  const value = {
    // Search state
    searchQuery,
    searchResults,
    searchHistory,
    searchSuggestions,
    isSearching,
    
    // Quick actions state
    quickActions,
    customActions,
    
    // Tab organization state
    tabGroups,
    activeTabGroup,
    
    // Keyboard shortcuts state
    shortcuts,
    
    // Search functions
    setSearchQuery,
    performSearch,
    clearSearch,
    addToSearchHistory,
    
    // Quick actions functions
    addQuickAction,
    removeQuickAction,
    updateQuickAction,
    addCustomAction,
    
    // Tab organization functions
    addTabGroup,
    removeTabGroup,
    updateTabGroup,
    setActiveTabGroup,
    
    // Keyboard shortcuts functions
    registerShortcut,
    unregisterShortcut,
    
    // Index pipeline state
    indexPipelineState
  };
  
  return (
    <NavigationContext.Provider value={value}>
      {children}
    </NavigationContext.Provider>
  );
};

export const useNavigation = () => {
  const context = useContext(NavigationContext);
  if (context === undefined) {
    throw new Error('useNavigation must be used within a NavigationProvider');
  }
  return context;
};

// Simple icon components for default actions
const SearchIcon = () => (
  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
    <circle cx="11" cy="11" r="8" />
    <path d="m21 21-4.3-4.3" />
  </svg>
);

const RefreshIcon = () => (
  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
    <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8" />
    <path d="M21 3v5h-5" />
    <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16" />
    <path d="M3 21v-5h5" />
  </svg>
);

const ExportIcon = () => (
  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
    <polyline points="7 10 12 15 17 10" />
    <line x1="12" y1="15" x2="12" y2="3" />
  </svg>
);

const SettingsIcon = () => (
  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
    <circle cx="12" cy="12" r="3" />
    <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z" />
  </svg>
);