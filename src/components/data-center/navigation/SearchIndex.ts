import { DataPipelineState } from '../DataCenterContext';

export interface SearchableItem {
  id: string;
  type: 'data' | 'template' | 'file' | 'result' | 'column' | 'table';
  title: string;
  description: string;
  content: string;
  tags: string[];
  metadata: Record<string, any>;
  timestamp: Date;
  category: string;
}

export interface SearchIndexStats {
  totalItems: number;
  itemsByType: Record<string, number>;
  lastIndexed: Date | null;
}

export class SearchIndex {
  private items: Map<string, SearchableItem> = new Map();
  private index: Map<string, Set<string>> = new Map(); // term -> itemIds
  private stats: SearchIndexStats = {
    totalItems: 0,
    itemsByType: {},
    lastIndexed: null
  };

  // Add item to index
  addItem(item: SearchableItem): void {
    this.items.set(item.id, item);
    this.indexItem(item);
    this.updateStats();
  }

  // Remove item from index
  removeItem(id: string): boolean {
    const item = this.items.get(id);
    if (!item) return false;
    
    this.items.delete(id);
    this.removeFromIndex(item);
    this.updateStats();
    return true;
  }

  // Update existing item
  updateItem(item: SearchableItem): void {
    const existingItem = this.items.get(item.id);
    if (existingItem) {
      this.removeFromIndex(existingItem);
    }
    
    this.items.set(item.id, item);
    this.indexItem(item);
    this.updateStats();
  }

  // Index an item by its content and metadata
  private indexItem(item: SearchableItem): void {
    const terms = this.extractTerms(item);
    
    terms.forEach(term => {
      if (!this.index.has(term)) {
        this.index.set(term, new Set());
      }
      this.index.get(term)!.add(item.id);
    });
  }

  // Remove item from index
  private removeFromIndex(item: SearchableItem): void {
    const terms = this.extractTerms(item);
    
    terms.forEach(term => {
      const itemIds = this.index.get(term);
      if (itemIds) {
        itemIds.delete(item.id);
        if (itemIds.size === 0) {
          this.index.delete(term);
        }
      }
    });
  }

  // Extract searchable terms from item
  private extractTerms(item: SearchableItem): string[] {
    const text = `${item.title} ${item.description} ${item.content} ${item.tags.join(' ')} ${item.category}`;
    const terms = text.toLowerCase().split(/\s+/).filter(term => term.length > 2);
    
    // Add metadata terms
    Object.values(item.metadata).forEach(value => {
      if (typeof value === 'string' && value.length > 2) {
        terms.push(...value.toLowerCase().split(/\s+/));
      }
    });
    
    // Remove duplicates and return
    return [...new Set(terms)];
  }

  // Search items by query
  search(query: string, filters?: { type?: string[]; category?: string[] }): SearchableItem[] {
    const terms = query.toLowerCase().split(/\s+/).filter(term => term.length > 2);
    
    if (terms.length === 0) {
      return this.getAllItems(filters);
    }
    
    // Find items matching all terms (AND search)
    let matchingItemIds = new Set<string>();
    let firstTerm = true;
    
    for (const term of terms) {
      const termMatches = this.getMatchesForTerm(term);
      
      if (firstTerm) {
        matchingItemIds = termMatches;
        firstTerm = false;
      } else {
        // Intersect with existing matches
        matchingItemIds = new Set([...matchingItemIds].filter(id => termMatches.has(id)));
      }
      
      // Early exit if no matches
      if (matchingItemIds.size === 0) break;
    }
    
    // Convert to items and apply filters
    const results = Array.from(matchingItemIds)
      .map(id => this.items.get(id)!)
      .filter(item => this.matchesFilters(item, filters));
    
    // Sort by relevance (simplified - could be enhanced with TF-IDF)
    return this.sortByRelevance(results, terms);
  }

  // Get matches for a single term
  private getMatchesForTerm(term: string): Set<string> {
    const exactMatches = this.index.get(term) || new Set();
    
    // Also find partial matches
    const partialMatches = new Set<string>();
    for (const [indexTerm, itemIds] of this.index.entries()) {
      if (indexTerm.includes(term)) {
        itemIds.forEach(id => partialMatches.add(id));
      }
    }
    
    // Combine exact and partial matches
    const allMatches = new Set(exactMatches);
    partialMatches.forEach(id => allMatches.add(id));
    
    return allMatches;
  }

  // Check if item matches filters
  private matchesFilters(item: SearchableItem, filters?: { type?: string[]; category?: string[] }): boolean {
    if (!filters) return true;
    
    if (filters.type && !filters.type.includes(item.type)) {
      return false;
    }
    
    if (filters.category && !filters.category.includes(item.category)) {
      return false;
    }
    
    return true;
  }

  // Sort results by relevance
  private sortByRelevance(items: SearchableItem[], terms: string[]): SearchableItem[] {
    return items.sort((a, b) => {
      const scoreA = this.calculateRelevanceScore(a, terms);
      const scoreB = this.calculateRelevanceScore(b, terms);
      return scoreB - scoreA; // Higher scores first
    });
  }

  // Calculate relevance score for an item
  private calculateRelevanceScore(item: SearchableItem, terms: string[]): number {
    let score = 0;
    const text = `${item.title} ${item.description} ${item.content} ${item.tags.join(' ')}`.toLowerCase();
    
    terms.forEach(term => {
      // Exact matches in title get higher score
      if (item.title.toLowerCase().includes(term)) {
        score += 10;
      }
      
      // Matches in description
      if (item.description.toLowerCase().includes(term)) {
        score += 5;
      }
      
      // Matches in content
      if (item.content.toLowerCase().includes(term)) {
        score += 3;
      }
      
      // Matches in tags
      if (item.tags.some(tag => tag.toLowerCase().includes(term))) {
        score += 7;
      }
      
      // Partial matches
      if (text.includes(term)) {
        score += 1;
      }
    });
    
    return score;
  }

  // Get all items with optional filters
  private getAllItems(filters?: { type?: string[]; category?: string[] }): SearchableItem[] {
    const items = Array.from(this.items.values());
    return items.filter(item => this.matchesFilters(item, filters));
  }

  // Get search suggestions based on partial query
  getSuggestions(query: string, limit: number = 5): string[] {
    if (!query) return [];
    
    const lowerQuery = query.toLowerCase();
    const suggestions = new Set<string>();
    
    // Look for terms that start with the query
    for (const term of this.index.keys()) {
      if (term.startsWith(lowerQuery)) {
        suggestions.add(term);
      }
    }
    
    // Also look for terms that contain the query
    if (suggestions.size < limit) {
      for (const term of this.index.keys()) {
        if (term.includes(lowerQuery) && !term.startsWith(lowerQuery)) {
          suggestions.add(term);
        }
      }
    }
    
    return Array.from(suggestions).slice(0, limit);
  }

  // Get search history (simplified - would use localStorage in real implementation)
  getSearchHistory(limit: number = 10): string[] {
    // This would be implemented with localStorage in a real app
    return [];
  }

  // Add to search history
  addToSearchHistory(query: string): void {
    // This would be implemented with localStorage in a real app
  }

  // Index data from pipeline state
  indexPipelineState(state: DataPipelineState): void {
    // Index file information
    if (state.fileName) {
      this.addItem({
        id: `file_${state.fileName}`,
        type: 'file',
        title: state.fileName,
        description: `Data file uploaded on ${state.uploadDate.toLocaleDateString()}`,
        content: `${state.fileName} ${state.fileType}`,
        tags: [state.fileType, 'uploaded'],
        metadata: {
          fileSize: state.fileSize,
          fileType: state.fileType,
          uploadDate: state.uploadDate
        },
        timestamp: state.uploadDate,
        category: 'uploaded-files'
      });
    }
    
    // Index data preview
    if (state.dataPreview) {
      this.addItem({
        id: `preview_${state.fileName || 'data'}`,
        type: 'data',
        title: `Data Preview: ${state.fileName || 'Dataset'}`,
        description: `Preview of ${state.dataPreview.totalRows} rows with ${state.dataPreview.headers.length} columns`,
        content: state.dataPreview.headers.join(' '),
        tags: ['preview', 'data', ...(state.dataPreview.headers || [])],
        metadata: {
          rows: state.dataPreview.totalRows,
          columns: state.dataPreview.headers.length,
          dataTypes: state.dataPreview.dataTypes
        },
        timestamp: new Date(),
        category: 'data-preview'
      });
      
      // Index columns
      state.dataPreview.headers.forEach((header, index) => {
        this.addItem({
          id: `column_${state.fileName || 'data'}_${index}`,
          type: 'column',
          title: `Column: ${header}`,
          description: `Column in ${state.fileName || 'dataset'}`,
          content: header,
          tags: ['column', state.dataPreview?.dataTypes?.[header] || 'unknown'],
          metadata: {
            columnIndex: index,
            dataType: state.dataPreview?.dataTypes?.[header] || 'unknown'
          },
          timestamp: new Date(),
          category: 'data-columns'
        });
      });
    }
    
    // Index SQL results
    if (state.sqlResult) {
      this.addItem({
        id: `sql_${Date.now()}`,
        type: 'result',
        title: 'Generated SQL Query',
        description: 'SQL query generated from data processing',
        content: state.sqlResult.sql,
        tags: ['sql', 'query', 'generated'],
        metadata: {
          isValid: state.sqlResult.isValid,
          error: state.sqlResult.error
        },
        timestamp: new Date(),
        category: 'sql-results'
      });
    }
    
    // Index processing templates
    if (state.selectedTemplate) {
      this.addItem({
        id: `template_${state.selectedTemplate}`,
        type: 'template',
        title: `Template: ${state.selectedTemplate}`,
        description: 'Processing template used for data transformation',
        content: state.pythonScript,
        tags: ['template', 'python', state.selectedTemplate],
        metadata: {
          templateName: state.selectedTemplate
        },
        timestamp: new Date(),
        category: 'processing-templates'
      });
    }
    
    this.updateStats();
  }

  // Update statistics
  private updateStats(): void {
    this.stats.totalItems = this.items.size;
    this.stats.itemsByType = {};
    this.stats.lastIndexed = new Date();
    
    for (const item of this.items.values()) {
      this.stats.itemsByType[item.type] = (this.stats.itemsByType[item.type] || 0) + 1;
    }
  }

  // Get index statistics
  getStats(): SearchIndexStats {
    return { ...this.stats };
  }

  // Clear index
  clear(): void {
    this.items.clear();
    this.index.clear();
    this.updateStats();
  }

  // Get items by type
  getItemsByType(type: string): SearchableItem[] {
    return Array.from(this.items.values()).filter(item => item.type === type);
  }
}

// Export singleton instance
export const searchIndex = new SearchIndex();