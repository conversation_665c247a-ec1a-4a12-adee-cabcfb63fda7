import React, { useState } from 'react';
import { 
  Plus, 
  Settings, 
  Search, 
  RefreshCw, 
  Download, 
  Save, 
  Play, 
  Filter,
  Edit,
  Trash2,
  Eye,
  Columns,
  Table,
  MoreHorizontal
} from 'lucide-react';
import { useNavigation } from './NavigationProvider';
import { Card, Button, Badge } from '../ui/StyledComponents';

interface QuickActionsProps {
  title?: string;
  className?: string;
}

export const QuickActions: React.FC<QuickActionsProps> = ({ 
  title = 'Quick Actions',
  className = ''
}) => {
  const { quickActions, customActions, addCustomAction } = useNavigation();
  const [showCustomization, setShowCustomization] = useState(false);
  const [newAction, setNewAction] = useState({
    label: '',
    icon: 'plus',
    category: 'custom'
  });
  
  // Combine default and custom actions
  const allActions = [...quickActions, ...customActions];
  
  // Group actions by category
  const groupedActions = allActions.reduce((acc, action) => {
    const category = action.category || 'General';
    if (!acc[category]) acc[category] = [];
    acc[category].push(action);
    return acc;
  }, {} as Record<string, typeof quickActions>);
  
  // Icon mapping
  const getIcon = (iconName: string) => {
    switch (iconName.toLowerCase()) {
      case 'search': return <Search className="w-4 h-4" />;
      case 'refresh': return <RefreshCw className="w-4 h-4" />;
      case 'download': return <Download className="w-4 h-4" />;
      case 'save': return <Save className="w-4 h-4" />;
      case 'play': return <Play className="w-4 h-4" />;
      case 'filter': return <Filter className="w-4 h-4" />;
      case 'edit': return <Edit className="w-4 h-4" />;
      case 'trash': return <Trash2 className="w-4 h-4" />;
      case 'eye': return <Eye className="w-4 h-4" />;
      case 'columns': return <Columns className="w-4 h-4" />;
      case 'table': return <Table className="w-4 h-4" />;
      case 'settings': return <Settings className="w-4 h-4" />;
      default: return <Plus className="w-4 h-4" />;
    }
  };
  
  const handleAddCustomAction = () => {
    if (!newAction.label.trim()) return;
    
    const action = {
      id: `custom_${Date.now()}`,
      label: newAction.label,
      icon: getIcon(newAction.icon),
      onClick: () => console.log(`Custom action: ${newAction.label}`),
      category: newAction.category,
      priority: 100
    };
    
    addCustomAction(action);
    setNewAction({ label: '', icon: 'plus', category: 'custom' });
    setShowCustomization(false);
  };
  
  return (
    <div className={`bg-card border-b ${className}`}>
      <div className="px-4 py-3">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-sm font-medium text-muted-foreground">{title}</h3>
          <div className="flex items-center gap-2">
            <span className="text-xs text-muted-foreground">
              {allActions.length} actions
            </span>
            <button
              onClick={() => setShowCustomization(!showCustomization)}
              className="p-1 text-muted-foreground hover:text-foreground transition-colors"
              aria-label="Customize actions"
            >
              <Settings className="w-4 h-4" />
            </button>
          </div>
        </div>
        
        {/* Actions List */}
        <div className="flex flex-wrap gap-2">
          {allActions
            .sort((a, b) => (a.priority || 0) - (b.priority || 0))
            .map((action) => (
              <button
                key={action.id}
                onClick={action.onClick}
                disabled={action.disabled}
                className="flex items-center gap-2 px-3 py-2 text-sm rounded-md bg-muted hover:bg-muted/70 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                title={action.shortcut ? `${action.label} (${action.shortcut})` : action.label}
              >
                {typeof action.icon === 'string' ? getIcon(action.icon) : action.icon}
                <span className="hidden sm:inline">{action.label}</span>
                {action.shortcut && (
                  <kbd className="hidden md:inline-flex px-1.5 py-0.5 text-xs font-mono bg-background border rounded">
                    {action.shortcut}
                  </kbd>
                )}
              </button>
            ))}
        </div>
        
        {/* Customization Panel */}
        {showCustomization && (
          <Card className="mt-4 p-4">
            <h4 className="font-medium mb-3">Customize Quick Actions</h4>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Add New Action */}
              <div className="space-y-3">
                <h5 className="text-sm font-medium">Add Custom Action</h5>
                <div className="space-y-2">
                  <input
                    type="text"
                    value={newAction.label}
                    onChange={(e) => setNewAction({...newAction, label: e.target.value})}
                    placeholder="Action name"
                    className="w-full px-3 py-2 text-sm border rounded-md bg-background"
                  />
                  
                  <select
                    value={newAction.icon}
                    onChange={(e) => setNewAction({...newAction, icon: e.target.value})}
                    className="w-full px-3 py-2 text-sm border rounded-md bg-background"
                  >
                    <option value="plus">Plus</option>
                    <option value="search">Search</option>
                    <option value="refresh">Refresh</option>
                    <option value="download">Download</option>
                    <option value="save">Save</option>
                    <option value="play">Play</option>
                    <option value="filter">Filter</option>
                    <option value="edit">Edit</option>
                    <option value="trash">Trash</option>
                    <option value="eye">Eye</option>
                    <option value="columns">Columns</option>
                    <option value="table">Table</option>
                    <option value="settings">Settings</option>
                  </select>
                  
                  <input
                    type="text"
                    value={newAction.category}
                    onChange={(e) => setNewAction({...newAction, category: e.target.value})}
                    placeholder="Category"
                    className="w-full px-3 py-2 text-sm border rounded-md bg-background"
                  />
                  
                  <Button 
                    onClick={handleAddCustomAction}
                    size="sm"
                    className="w-full"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Add Action
                  </Button>
                </div>
              </div>
              
              {/* Current Custom Actions */}
              <div className="space-y-3">
                <h5 className="text-sm font-medium">Custom Actions</h5>
                {customActions.length > 0 ? (
                  <div className="space-y-2 max-h-40 overflow-y-auto">
                    {customActions.map(action => (
                      <div 
                        key={action.id} 
                        className="flex items-center justify-between p-2 bg-muted rounded text-sm"
                      >
                        <div className="flex items-center gap-2">
                          {typeof action.icon === 'string' ? getIcon(action.icon) : action.icon}
                          <span>{action.label}</span>
                        </div>
                        <Badge variant="secondary" className="text-xs">
                          {action.category}
                        </Badge>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-xs text-muted-foreground">No custom actions yet</p>
                )}
              </div>
            </div>
          </Card>
        )}
      </div>
    </div>
  );
};