import React, { useState } from 'react';
import { 
  Database, 
  LayoutGrid, 
  Eye, 
  Wrench, 
  Layers, 
  Brain, 
  FileOutput,
  ChevronRight,
  Settings,
  GripVertical
} from 'lucide-react';
import { useNavigation } from './NavigationProvider';
import { Card, Button, Badge } from '../ui/StyledComponents';

interface TabOrganizerProps {
  activeTab: string;
  onTabChange: (tabId: string) => void;
  className?: string;
}

interface TabDefinition {
  id: string;
  label: string;
  icon: React.ReactNode;
  color: string;
  disabled?: boolean;
}

export const TabOrganizer: React.FC<TabOrganizerProps> = ({ 
  activeTab, 
  onTabChange,
  className = ''
}) => {
  const { tabGroups, activeTabGroup, setActiveTabGroup } = useNavigation();
  const [showCustomization, setShowCustomization] = useState(false);
  const [draggedTab, setDraggedTab] = useState<string | null>(null);
  
  // Define all available tabs
  const allTabs: TabDefinition[] = [
    { id: 'upload', label: 'Upload', icon: <LayoutGrid className="w-4 h-4" />, color: 'text-dataOperation' },
    { id: 'storage', label: 'Storage', icon: <Database className="w-4 h-4" />, color: 'text-dataOperation' },
    { id: 'preview', label: 'Preview', icon: <Eye className="w-4 h-4" />, color: 'text-dataOperation' },
    { id: 'explore', label: 'Explore', icon: <Wrench className="w-4 h-4" />, color: 'text-warning' },
    { id: 'processing', label: 'Processing', icon: <Layers className="w-4 h-4" />, color: 'text-warning' },
    { id: 'analysis', label: 'Analysis', icon: <Brain className="w-4 h-4" />, color: 'text-warning' },
    { id: 'sql', label: 'SQL Output', icon: <FileOutput className="w-4 h-4" />, color: 'text-success' }
  ];
  
  // Get tabs for active group
  const activeGroup = tabGroups.find(group => group.id === activeTabGroup);
  const groupTabs = activeGroup 
    ? allTabs.filter(tab => activeGroup.tabs.includes(tab.id))
    : allTabs;
  
  // Handle drag start
  const handleDragStart = (e: React.DragEvent, tabId: string) => {
    setDraggedTab(tabId);
    e.dataTransfer.setData('text/plain', tabId);
  };
  
  // Handle drag over
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };
  
  // Handle drop
  const handleDrop = (e: React.DragEvent, targetTabId: string) => {
    e.preventDefault();
    const draggedTabId = e.dataTransfer.getData('text/plain');
    
    if (draggedTabId && draggedTabId !== targetTabId && activeGroup) {
      // Reorder tabs within the group
      const newTabs = [...activeGroup.tabs];
      const draggedIndex = newTabs.indexOf(draggedTabId);
      const targetIndex = newTabs.indexOf(targetTabId);
      
      if (draggedIndex !== -1 && targetIndex !== -1) {
        // Remove dragged tab and insert at target position
        newTabs.splice(draggedIndex, 1);
        newTabs.splice(targetIndex, 0, draggedTabId);
        
        // Update tab group (in a real implementation, this would update the context)
        console.log('Reordered tabs:', newTabs);
      }
    }
    
    setDraggedTab(null);
  };
  
  return (
    <div className={`bg-card/50 overflow-x-auto ${className}`}>
      {/* Tab Groups */}
      <div className="flex border-b bg-card/50">
        {tabGroups.map(group => (
          <button
            key={group.id}
            onClick={() => setActiveTabGroup(group.id)}
            className={`px-4 py-3 text-sm font-medium transition-all flex items-center gap-2 border-b-2 ${
              activeTabGroup === group.id
                ? 'text-foreground border-primary'
                : 'text-muted-foreground hover:text-foreground hover:bg-muted/50 border-transparent'
            }`}
          >
            {group.icon && <span className="w-4 h-4">{group.icon}</span>}
            {group.name}
            <Badge variant="secondary" className="text-xs ml-1">
              {group.tabs.length}
            </Badge>
          </button>
        ))}
        
        <div className="flex-1" />
        
        <button
          onClick={() => setShowCustomization(!showCustomization)}
          className="px-3 py-3 text-muted-foreground hover:text-foreground transition-colors"
          aria-label="Customize tab groups"
        >
          <Settings className="w-4 h-4" />
        </button>
      </div>
      
      {/* Tabs */}
      <div className="flex">
        {groupTabs.map((tab) => (
          <button
            key={tab.id}
            draggable
            onDragStart={(e) => handleDragStart(e, tab.id)}
            onDragOver={handleDragOver}
            onDrop={(e) => handleDrop(e, tab.id)}
            onClick={() => onTabChange(tab.id)}
            className={`px-4 py-3 text-sm font-medium transition-all flex items-center gap-2 ${
              activeTab === tab.id
                ? `${tab.color} border-b-2 border-current bg-card`
                : 'text-muted-foreground hover:text-foreground hover:bg-muted/50'
            }`}
            disabled={tab.disabled}
          >
            <GripVertical className="w-3 h-3 cursor-grab" />
            {tab.icon}
            {tab.label}
          </button>
        ))}
      </div>
      
      {/* Customization Panel */}
      {showCustomization && (
        <Card className="m-4 p-4">
          <h4 className="font-medium mb-3">Tab Group Customization</h4>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Tab Groups */}
            <div className="space-y-3">
              <h5 className="text-sm font-medium">Tab Groups</h5>
              <div className="space-y-2">
                {tabGroups.map(group => (
                  <div 
                    key={group.id} 
                    className="flex items-center justify-between p-2 bg-muted rounded"
                  >
                    <div className="flex items-center gap-2">
                      {group.icon && <span className="w-4 h-4">{group.icon}</span>}
                      <span>{group.name}</span>
                    </div>
                    <Badge variant="secondary" className="text-xs">
                      {group.tabs.length} tabs
                    </Badge>
                  </div>
                ))}
              </div>
            </div>
            
            {/* All Tabs */}
            <div className="space-y-3">
              <h5 className="text-sm font-medium">Available Tabs</h5>
              <div className="space-y-2">
                {allTabs.map(tab => (
                  <div 
                    key={tab.id} 
                    className={`flex items-center gap-2 p-2 rounded text-sm ${
                      activeGroup?.tabs.includes(tab.id) 
                        ? 'bg-primary/10 text-primary' 
                        : 'bg-muted hover:bg-muted/70'
                    }`}
                  >
                    {tab.icon}
                    <span>{tab.label}</span>
                    {activeGroup?.tabs.includes(tab.id) && (
                      <Badge variant="secondary" className="text-xs ml-auto">
                        In group
                      </Badge>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};