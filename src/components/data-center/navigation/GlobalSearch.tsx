import React, { useState, useEffect, useRef } from 'react';
import { Search, X, Filter, Clock, FileText, Code, Database, Layers } from 'lucide-react';
import { useNavigation } from './NavigationProvider';
import { SearchableItem } from './SearchIndex';
import { Card, Button, Badge } from '../ui/StyledComponents';
import { colors } from '../ui/StyledComponents';

interface GlobalSearchProps {
  isOpen: boolean;
  onClose: () => void;
  onResultSelect?: (item: SearchableItem) => void;
}

export const GlobalSearch: React.FC<GlobalSearchProps> = ({ isOpen, onClose, onResultSelect }) => {
  const { 
    searchQuery, 
    setSearchQuery, 
    performSearch, 
    searchResults, 
    searchSuggestions, 
    isSearching,
    searchHistory,
    clearSearch
  } = useNavigation();
  
  const [selectedFilters, setSelectedFilters] = useState<string[]>([]);
  const [showFilters, setShowFilters] = useState(false);
  const searchInputRef = useRef<HTMLInputElement>(null);
  
  const filterOptions = [
    { id: 'data', label: 'Data', icon: <Database className="w-4 h-4" /> },
    { id: 'template', label: 'Templates', icon: <Layers className="w-4 h-4" /> },
    { id: 'file', label: 'Files', icon: <FileText className="w-4 h-4" /> },
    { id: 'result', label: 'Results', icon: <Code className="w-4 h-4" /> }
  ];
  
  // Focus search input when opened
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen]);
  
  // Handle search with debounce
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (searchQuery.trim()) {
        const filters = selectedFilters.length > 0 ? { type: selectedFilters } : undefined;
        performSearch(searchQuery, filters);
      } else {
        performSearch('');
      }
    }, 300);
    
    return () => clearTimeout(timeoutId);
  }, [searchQuery, selectedFilters, performSearch]);
  
  // Handle escape key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };
    
    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
    }
    
    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, [isOpen, onClose]);
  
  const handleResultSelect = (item: SearchableItem) => {
    onResultSelect?.(item);
    onClose();
  };
  
  const toggleFilter = (filterId: string) => {
    setSelectedFilters(prev => 
      prev.includes(filterId) 
        ? prev.filter(id => id !== filterId) 
        : [...prev, filterId]
    );
  };
  
  const clearFilters = () => {
    setSelectedFilters([]);
  };
  
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'data': return <Database className="w-4 h-4" />;
      case 'template': return <Layers className="w-4 h-4" />;
      case 'file': return <FileText className="w-4 h-4" />;
      case 'result': return <Code className="w-4 h-4" />;
      case 'column': return <Database className="w-4 h-4" />;
      case 'table': return <Database className="w-4 h-4" />;
      default: return <FileText className="w-4 h-4" />;
    }
  };
  
  const getTypeColor = (type: string) => {
    switch (type) {
      case 'data': return colors.dataOperation;
      case 'template': return colors.warning;
      case 'file': return colors.info;
      case 'result': return colors.success;
      case 'column': return colors.dataOperation;
      case 'table': return colors.dataOperation;
      default: return colors.textSecondary;
    }
  };
  
  if (!isOpen) return null;
  
  return (
    <div className="fixed inset-0 z-50 flex items-start justify-center pt-20 px-4">
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black/30 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* Search Container */}
      <div className="relative w-full max-w-2xl bg-card border rounded-lg shadow-xl overflow-hidden z-10">
        {/* Search Header */}
        <div className="p-4 border-b">
          <div className="flex items-center gap-3">
            <Search className="w-5 h-5 text-muted-foreground" />
            <input
              ref={searchInputRef}
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search data, templates, files, and results..."
              className="flex-1 bg-transparent border-none outline-none text-lg"
              aria-label="Global search"
            />
            <button
              onClick={onClose}
              className="p-1.5 hover:bg-muted rounded transition-colors"
              aria-label="Close search"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
          
          {/* Filters */}
          <div className="flex items-center gap-2 mt-3">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-1.5 px-2 py-1 text-xs rounded bg-muted hover:bg-muted/70 transition-colors"
            >
              <Filter className="w-3 h-3" />
              Filters
              {selectedFilters.length > 0 && (
                <span className="bg-primary text-primary-foreground rounded-full w-4 h-4 flex items-center justify-center text-[10px]">
                  {selectedFilters.length}
                </span>
              )}
            </button>
            
            {selectedFilters.length > 0 && (
              <button
                onClick={clearFilters}
                className="text-xs text-muted-foreground hover:text-foreground transition-colors"
              >
                Clear filters
              </button>
            )}
            
            <div className="flex-1" />
            
            {searchHistory.length > 0 && (
              <div className="flex items-center gap-1 text-xs text-muted-foreground">
                <Clock className="w-3 h-3" />
                <span>Recent</span>
              </div>
            )}
          </div>
          
          {/* Filter Options */}
          {showFilters && (
            <div className="flex flex-wrap gap-2 mt-3 pt-3 border-t">
              {filterOptions.map(option => (
                <button
                  key={option.id}
                  onClick={() => toggleFilter(option.id)}
                  className={`flex items-center gap-1.5 px-2 py-1 text-xs rounded transition-colors ${
                    selectedFilters.includes(option.id)
                      ? 'bg-primary text-primary-foreground'
                      : 'bg-muted hover:bg-muted/70'
                  }`}
                >
                  {option.icon}
                  {option.label}
                </button>
              ))}
            </div>
          )}
        </div>
        
        {/* Search Results */}
        <div className="max-h-96 overflow-y-auto">
          {isSearching ? (
            <div className="p-8 text-center">
              <div className="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
              <p className="mt-2 text-sm text-muted-foreground">Searching...</p>
            </div>
          ) : searchQuery.trim() ? (
            searchResults.length > 0 ? (
              <div className="divide-y">
                {searchResults.map((item, index) => (
                  <div
                    key={item.id}
                    onClick={() => handleResultSelect(item)}
                    className="p-4 hover:bg-muted/50 cursor-pointer transition-colors"
                  >
                    <div className="flex items-start gap-3">
                      <div 
                        className="p-2 rounded mt-0.5"
                        style={{ backgroundColor: `${getTypeColor(item.type)}15` }}
                      >
                        {getTypeIcon(item.type)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <h3 className="font-medium truncate">{item.title}</h3>
                          <Badge variant="secondary" className="text-xs">
                            {item.type}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                          {item.description}
                        </p>
                        <div className="flex items-center gap-2 mt-2">
                          <span 
                            className="text-xs px-1.5 py-0.5 rounded"
                            style={{ 
                              backgroundColor: `${getTypeColor(item.type)}15`,
                              color: getTypeColor(item.type)
                            }}
                          >
                            {item.category}
                          </span>
                          <span className="text-xs text-muted-foreground">
                            {item.timestamp.toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="p-8 text-center">
                <FileText className="w-12 h-12 mx-auto text-muted-foreground/50" />
                <p className="mt-2 text-sm text-muted-foreground">No results found for "{searchQuery}"</p>
                <p className="text-xs text-muted-foreground/70 mt-1">
                  Try different keywords or clear your filters
                </p>
              </div>
            )
          ) : searchSuggestions.length > 0 ? (
            <div className="p-2">
              <p className="px-2 py-1 text-xs text-muted-foreground">Suggestions</p>
              {searchSuggestions.map((suggestion, index) => (
                <button
                  key={index}
                  onClick={() => setSearchQuery(suggestion)}
                  className="w-full px-3 py-2 text-left text-sm hover:bg-muted rounded transition-colors flex items-center gap-2"
                >
                  <Search className="w-4 h-4 text-muted-foreground" />
                  {suggestion}
                </button>
              ))}
            </div>
          ) : (
            <div className="p-8 text-center">
              <Search className="w-12 h-12 mx-auto text-muted-foreground/50" />
              <p className="mt-2 text-sm text-muted-foreground">Start typing to search</p>
              <p className="text-xs text-muted-foreground/70 mt-1">
                Search across data, templates, files, and results
              </p>
            </div>
          )}
        </div>
        
        {/* Search Footer */}
        <div className="p-3 border-t bg-muted/30 text-xs text-muted-foreground flex items-center justify-between">
          <div className="flex items-center gap-3">
            <span>Press <kbd className="px-1.5 py-0.5 bg-background border rounded">↑</kbd> <kbd className="px-1.5 py-0.5 bg-background border rounded">↓</kbd> to navigate</span>
            <span>Press <kbd className="px-1.5 py-0.5 bg-background border rounded">Enter</kbd> to select</span>
          </div>
          <span>{searchResults.length} results</span>
        </div>
      </div>
    </div>
  );
};