import React, { createContext, useContext, useReducer, useCallback } from 'react';
import { 
  DataCenterState, 
  DataCenterActions, 
  ViewType, 
  StageType, 
  DataPreview,
  ProcessingResult,
  SQLGenerationResult,
  DataCenterProvidersProps
} from '../types';

/**
 * Initial state for the Data Center
 */
const initialState: DataCenterState = {
  selectedFile: null,
  dataPreview: null,
  processingResult: null,
  sqlResult: null,
  activeView: 'dashboard',
  currentStage: 'upload',
  isProcessing: false,
  sidebarCollapsed: false,
  showMetadataSidebar: false,
  showControlPanel: false,
  contextMenu: {
    visible: false,
    x: 0,
    y: 0
  }
};

/**
 * Action types for the reducer
 */
type DataCenterAction =
  | { type: 'SET_SELECTED_FILE'; payload: File | null }
  | { type: 'SET_DATA_PREVIEW'; payload: DataPreview | null }
  | { type: 'SET_PROCESSING_RESULT'; payload: ProcessingResult | null }
  | { type: 'SET_SQL_RESULT'; payload: SQLGenerationResult | null }
  | { type: 'SET_ACTIVE_VIEW'; payload: ViewType }
  | { type: 'SET_CURRENT_STAGE'; payload: StageType }
  | { type: 'SET_IS_PROCESSING'; payload: boolean }
  | { type: 'TOGGLE_SIDEBAR' }
  | { type: 'TOGGLE_METADATA_SIDEBAR' }
  | { type: 'TOGGLE_CONTROL_PANEL' }
  | { type: 'SHOW_CONTEXT_MENU'; payload: { x: number; y: number } }
  | { type: 'HIDE_CONTEXT_MENU' }
  | { type: 'RESET_STATE' };

/**
 * Reducer function for managing Data Center state
 */
const dataCenterReducer = (state: DataCenterState, action: DataCenterAction): DataCenterState => {
  switch (action.type) {
    case 'SET_SELECTED_FILE':
      return { ...state, selectedFile: action.payload };
    
    case 'SET_DATA_PREVIEW':
      return { ...state, dataPreview: action.payload };
    
    case 'SET_PROCESSING_RESULT':
      return { ...state, processingResult: action.payload };
    
    case 'SET_SQL_RESULT':
      return { ...state, sqlResult: action.payload };
    
    case 'SET_ACTIVE_VIEW':
      return { ...state, activeView: action.payload };
    
    case 'SET_CURRENT_STAGE':
      return { ...state, currentStage: action.payload };
    
    case 'SET_IS_PROCESSING':
      return { ...state, isProcessing: action.payload };
    
    case 'TOGGLE_SIDEBAR':
      return { ...state, sidebarCollapsed: !state.sidebarCollapsed };
    
    case 'TOGGLE_METADATA_SIDEBAR':
      return { ...state, showMetadataSidebar: !state.showMetadataSidebar };
    
    case 'TOGGLE_CONTROL_PANEL':
      return { ...state, showControlPanel: !state.showControlPanel };
    
    case 'SHOW_CONTEXT_MENU':
      return {
        ...state,
        contextMenu: {
          visible: true,
          x: action.payload.x,
          y: action.payload.y
        }
      };
    
    case 'HIDE_CONTEXT_MENU':
      return {
        ...state,
        contextMenu: {
          ...state.contextMenu,
          visible: false
        }
      };
    
    case 'RESET_STATE':
      return initialState;
    
    default:
      return state;
  }
};

/**
 * Context for Data Center state and actions
 */
interface DataCenterContextType {
  state: DataCenterState;
  actions: DataCenterActions;
}

const DataCenterContext = createContext<DataCenterContextType | undefined>(undefined);

/**
 * Data Center Provider component
 */
export const DataCenterProvider: React.FC<DataCenterProvidersProps> = ({
  children,
  config,
  onDataChange
}) => {
  const [state, dispatch] = useReducer(dataCenterReducer, initialState);

  // Action creators
  const actions: DataCenterActions = {
    // File operations
    handleFileUpload: useCallback((files: File[]) => {
      if (files.length > 0) {
        const file = files[0];
        dispatch({ type: 'SET_SELECTED_FILE', payload: file });
        dispatch({ type: 'SET_CURRENT_STAGE', payload: 'preview' });
        
        // Notify parent component
        onDataChange?.(file);
      }
    }, [onDataChange]),

    handleFileSelect: useCallback((file: File) => {
      dispatch({ type: 'SET_SELECTED_FILE', payload: file });
    }, []),

    handleFileRemove: useCallback((file: File) => {
      dispatch({ type: 'SET_SELECTED_FILE', payload: null });
      dispatch({ type: 'SET_DATA_PREVIEW', payload: null });
      dispatch({ type: 'SET_CURRENT_STAGE', payload: 'upload' });
    }, []),

    // Data operations
    handleDataProcess: useCallback((template: string) => {
      dispatch({ type: 'SET_IS_PROCESSING', payload: true });
      dispatch({ type: 'SET_CURRENT_STAGE', payload: 'process' });
      
      // Simulate processing
      setTimeout(() => {
        const mockResult: ProcessingResult = {
          content: 'Processed data content',
          metadata: {
            template,
            processedAt: new Date().toISOString(),
            recordCount: state.dataPreview?.totalRows || 0
          }
        };
        
        dispatch({ type: 'SET_PROCESSING_RESULT', payload: mockResult });
        dispatch({ type: 'SET_IS_PROCESSING', payload: false });
      }, 2000);
    }, [state.dataPreview]),

    handleSQLGenerate: useCallback(() => {
      if (state.dataPreview) {
        const mockSQL: SQLGenerationResult = {
          sql: `SELECT * FROM data_table LIMIT 10;`,
          isValid: true,
          analysis: 'Generated basic SELECT query for data exploration'
        };
        
        dispatch({ type: 'SET_SQL_RESULT', payload: mockSQL });
        dispatch({ type: 'SET_CURRENT_STAGE', payload: 'sql' });
      }
    }, [state.dataPreview]),

    handleSQLExecute: useCallback((sql: string) => {
      dispatch({ type: 'SET_IS_PROCESSING', payload: true });
      
      // Simulate SQL execution
      setTimeout(() => {
        dispatch({ type: 'SET_IS_PROCESSING', payload: false });
      }, 1000);
    }, []),

    // UI operations
    handleViewChange: useCallback((view: ViewType) => {
      dispatch({ type: 'SET_ACTIVE_VIEW', payload: view });
    }, []),

    handleStageChange: useCallback((stage: StageType) => {
      dispatch({ type: 'SET_CURRENT_STAGE', payload: stage });
    }, []),

    handleSidebarToggle: useCallback(() => {
      dispatch({ type: 'TOGGLE_SIDEBAR' });
    }, []),

    handleMetadataSidebarToggle: useCallback(() => {
      dispatch({ type: 'TOGGLE_METADATA_SIDEBAR' });
    }, []),

    handleControlPanelToggle: useCallback(() => {
      dispatch({ type: 'TOGGLE_CONTROL_PANEL' });
    }, []),

    // Context menu operations
    showContextMenu: useCallback((x: number, y: number) => {
      dispatch({ type: 'SHOW_CONTEXT_MENU', payload: { x, y } });
    }, []),

    hideContextMenu: useCallback(() => {
      dispatch({ type: 'HIDE_CONTEXT_MENU' });
    }, [])
  };

  const contextValue: DataCenterContextType = {
    state,
    actions
  };

  return (
    <DataCenterContext.Provider value={contextValue}>
      {children}
    </DataCenterContext.Provider>
  );
};

/**
 * Hook to use Data Center context
 */
export const useDataCenter = (): DataCenterContextType => {
  const context = useContext(DataCenterContext);
  if (!context) {
    throw new Error('useDataCenter must be used within a DataCenterProvider');
  }
  return context;
};

export default DataCenterProvider;