import React from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import {
  Keyboard,
  Navigation,
  Search,
  FileText,
  Eye,
  Database,
  Command
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface KeyboardShortcut {
  key: string;
  ctrlKey?: boolean;
  altKey?: boolean;
  shiftKey?: boolean;
  metaKey?: boolean;
  description: string;
  category: 'navigation' | 'data' | 'view' | 'file' | 'search';
}

interface KeyboardShortcutsHelpProps {
  isOpen: boolean;
  onClose: () => void;
  shortcuts: KeyboardShortcut[];
  className?: string;
}

const getCategoryIcon = (category: KeyboardShortcut['category']) => {
  switch (category) {
    case 'navigation': return <Navigation className="h-4 w-4" />;
    case 'search': return <Search className="h-4 w-4" />;
    case 'file': return <FileText className="h-4 w-4" />;
    case 'view': return <Eye className="h-4 w-4" />;
    case 'data': return <Database className="h-4 w-4" />;
    default: return <Command className="h-4 w-4" />;
  }
};

const getCategoryColor = (category: KeyboardShortcut['category']) => {
  switch (category) {
    case 'navigation': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
    case 'search': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
    case 'file': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
    case 'view': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
    case 'data': return 'bg-cyan-100 text-cyan-800 dark:bg-cyan-900 dark:text-cyan-200';
    default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
  }
};

const formatKeyCombo = (shortcut: KeyboardShortcut) => {
  const keys: string[] = [];
  
  if (shortcut.metaKey) keys.push('⌘');
  if (shortcut.ctrlKey) keys.push('Ctrl');
  if (shortcut.altKey) keys.push('Alt');
  if (shortcut.shiftKey) keys.push('⇧');
  
  // Format special keys
  let mainKey = shortcut.key;
  switch (shortcut.key.toLowerCase()) {
    case 'arrowup': mainKey = '↑'; break;
    case 'arrowdown': mainKey = '↓'; break;
    case 'arrowleft': mainKey = '←'; break;
    case 'arrowright': mainKey = '→'; break;
    case 'enter': mainKey = '↵'; break;
    case 'escape': mainKey = 'Esc'; break;
    case 'backspace': mainKey = '⌫'; break;
    case 'delete': mainKey = 'Del'; break;
    case 'tab': mainKey = 'Tab'; break;
    case 'space': mainKey = 'Space'; break;
    case 'home': mainKey = 'Home'; break;
    case 'end': mainKey = 'End'; break;
    case 'pageup': mainKey = 'PgUp'; break;
    case 'pagedown': mainKey = 'PgDn'; break;
    default: mainKey = shortcut.key.toUpperCase();
  }
  
  keys.push(mainKey);
  return keys;
};

const KeyCombo: React.FC<{ keys: string[] }> = ({ keys }) => (
  <div className="flex items-center gap-1">
    {keys.map((key, index) => (
      <React.Fragment key={index}>
        <kbd className="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg dark:bg-gray-600 dark:text-gray-100 dark:border-gray-500">
          {key}
        </kbd>
        {index < keys.length - 1 && (
          <span className="text-gray-400 text-xs">+</span>
        )}
      </React.Fragment>
    ))}
  </div>
);

export const KeyboardShortcutsHelp: React.FC<KeyboardShortcutsHelpProps> = ({
  isOpen,
  onClose,
  shortcuts,
  className
}) => {
  // Group shortcuts by category
  const groupedShortcuts = shortcuts.reduce((acc, shortcut) => {
    if (!acc[shortcut.category]) {
      acc[shortcut.category] = [];
    }
    acc[shortcut.category].push(shortcut);
    return acc;
  }, {} as Record<string, KeyboardShortcut[]>);

  // Default shortcuts that are always available
  const defaultShortcuts: KeyboardShortcut[] = [
    {
      key: 'F1',
      description: 'Show/hide this help dialog',
      category: 'navigation'
    },
    {
      key: 'Escape',
      description: 'Close dialogs and cancel operations',
      category: 'navigation'
    },
    {
      key: 'Tab',
      description: 'Navigate to next focusable element',
      category: 'navigation'
    },
    {
      key: 'Tab',
      shiftKey: true,
      description: 'Navigate to previous focusable element',
      category: 'navigation'
    },
    {
      key: 'Enter',
      description: 'Activate focused element',
      category: 'navigation'
    },
    {
      key: 'Space',
      description: 'Toggle checkboxes and buttons',
      category: 'navigation'
    },
    {
      key: 'Home',
      description: 'Navigate to first element',
      category: 'navigation'
    },
    {
      key: 'End',
      description: 'Navigate to last element',
      category: 'navigation'
    },
    {
      key: 'ArrowUp',
      description: 'Navigate up in lists and grids',
      category: 'navigation'
    },
    {
      key: 'ArrowDown',
      description: 'Navigate down in lists and grids',
      category: 'navigation'
    },
    {
      key: 'ArrowLeft',
      description: 'Navigate left in grids and tabs',
      category: 'navigation'
    },
    {
      key: 'ArrowRight',
      description: 'Navigate right in grids and tabs',
      category: 'navigation'
    },
    {
      key: 'f',
      ctrlKey: true,
      description: 'Open search dialog',
      category: 'search'
    },
    {
      key: 's',
      ctrlKey: true,
      description: 'Save current work',
      category: 'file'
    },
    {
      key: 'o',
      ctrlKey: true,
      description: 'Open file dialog',
      category: 'file'
    },
    {
      key: 'n',
      ctrlKey: true,
      description: 'Create new item',
      category: 'file'
    },
    {
      key: 'z',
      ctrlKey: true,
      description: 'Undo last action',
      category: 'data'
    },
    {
      key: 'y',
      ctrlKey: true,
      description: 'Redo last undone action',
      category: 'data'
    },
    {
      key: 'a',
      ctrlKey: true,
      description: 'Select all items',
      category: 'data'
    },
    {
      key: 'c',
      ctrlKey: true,
      description: 'Copy selected items',
      category: 'data'
    },
    {
      key: 'v',
      ctrlKey: true,
      description: 'Paste copied items',
      category: 'data'
    },
    {
      key: 'x',
      ctrlKey: true,
      description: 'Cut selected items',
      category: 'data'
    },
    {
      key: 'Delete',
      description: 'Delete selected items',
      category: 'data'
    },
    {
      key: '=',
      ctrlKey: true,
      description: 'Zoom in',
      category: 'view'
    },
    {
      key: '-',
      ctrlKey: true,
      description: 'Zoom out',
      category: 'view'
    },
    {
      key: '0',
      ctrlKey: true,
      description: 'Reset zoom to 100%',
      category: 'view'
    }
  ];

  // Merge default shortcuts with provided shortcuts
  const allShortcuts = [...defaultShortcuts, ...shortcuts];
  const allGroupedShortcuts = allShortcuts.reduce((acc, shortcut) => {
    if (!acc[shortcut.category]) {
      acc[shortcut.category] = [];
    }
    acc[shortcut.category].push(shortcut);
    return acc;
  }, {} as Record<string, KeyboardShortcut[]>);

  const categoryOrder: KeyboardShortcut['category'][] = ['navigation', 'search', 'file', 'data', 'view'];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className={cn("max-w-4xl max-h-[80vh]", className)}>
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Keyboard className="h-5 w-5" />
            Keyboard Shortcuts
          </DialogTitle>
          <DialogDescription>
            Use these keyboard shortcuts to navigate and interact with the Data Center efficiently.
            Press <kbd className="px-1 py-0.5 text-xs bg-gray-100 rounded">F1</kbd> anytime to toggle this help.
          </DialogDescription>
        </DialogHeader>
        
        <ScrollArea className="max-h-[60vh] pr-4">
          <div className="space-y-6">
            {categoryOrder.map(category => {
              const categoryShortcuts = allGroupedShortcuts[category];
              if (!categoryShortcuts || categoryShortcuts.length === 0) return null;
              
              return (
                <Card key={category}>
                  <CardHeader className="pb-3">
                    <CardTitle className="flex items-center gap-2 text-lg">
                      {getCategoryIcon(category)}
                      <span className="capitalize">{category}</span>
                      <Badge variant="outline" className={getCategoryColor(category)}>
                        {categoryShortcuts.length}
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {categoryShortcuts.map((shortcut, index) => {
                        const keys = formatKeyCombo(shortcut);
                        return (
                          <div key={index} className="flex items-center justify-between py-2">
                            <span className="text-sm text-gray-700 dark:text-gray-300 flex-1">
                              {shortcut.description}
                            </span>
                            <KeyCombo keys={keys} />
                          </div>
                        );
                      })}
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
          
          <Separator className="my-6" />
          
          <Card>
            <CardHeader>
              <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Tips for Better Navigation
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="text-sm space-y-2 text-gray-600 dark:text-gray-400">
                <li className="flex items-start gap-2">
                  <span className="text-blue-500 mt-1">•</span>
                  <span>Use <kbd className="px-1 py-0.5 bg-gray-100 rounded text-xs">Tab</kbd> to navigate between interactive elements</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-blue-500 mt-1">•</span>
                  <span>Arrow keys work within data grids, lists, and tab panels</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-blue-500 mt-1">•</span>
                  <span>Press <kbd className="px-1 py-0.5 bg-gray-100 rounded text-xs">Escape</kbd> to close dialogs and cancel operations</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-blue-500 mt-1">•</span>
                  <span>Most shortcuts work with standard modifier keys (Ctrl/Cmd, Alt, Shift)</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-blue-500 mt-1">•</span>
                  <span>Focus indicators show which element is currently selected</span>
                </li>
              </ul>
            </CardContent>
          </Card>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
};

export default KeyboardShortcutsHelp;