import { ViewType, StageType, ProcessingStatus, FileType, ComponentVariant } from './enums';

// Mock data for global state store
export const mockStore = {
  theme: 'dark' as const,
  sidebarCollapsed: false,
  showMetadataSidebar: false,
  showControlPanel: false,
  preferences: {
    viewMode: 'grid' as const,
    theme: 'dark' as const,
    showSchema: true,
    autoRefresh: true,
    sidebarCollapsed: false,
    lastActiveView: ViewType.DASHBOARD,
    favorites: ['sample_data.csv', 'user_analytics.xlsx']
  },
  performance: {
    cacheEnabled: true,
    virtualizationEnabled: true,
    backgroundProcessing: true,
    workerPoolSize: 3,
    cacheStats: {
      hitRate: 0.85,
      missRate: 0.15,
      cacheSize: 1024
    }
  }
};

// Mock data for API queries
export const mockQuery = {
  recentFiles: [
    {
      id: 'file-1',
      name: 'sample_data.csv',
      type: FileType.CSV,
      size: 2048576,
      lastModified: new Date('2024-01-15T10:30:00Z'),
      uploadTime: new Date('2024-01-15T10:30:00Z'),
      rowCount: 1250,
      columnCount: 8
    },
    {
      id: 'file-2', 
      name: 'user_analytics.xlsx',
      type: FileType.XLSX,
      size: 5242880,
      lastModified: new Date('2024-01-14T15:45:00Z'),
      uploadTime: new Date('2024-01-14T15:45:00Z'),
      rowCount: 3500,
      columnCount: 12
    },
    {
      id: 'file-3',
      name: 'product_catalog.json',
      type: FileType.JSON,
      size: 1048576,
      lastModified: new Date('2024-01-13T09:15:00Z'),
      uploadTime: new Date('2024-01-13T09:15:00Z'),
      rowCount: 850,
      columnCount: 15
    }
  ],
  sampleData: {
    headers: ['Name', 'Age', 'Department', 'Salary', 'Start Date', 'Email', 'Location', 'Status'],
    rows: [
      ['John Doe', 32, 'Engineering', 95000, '2021-03-15', '<EMAIL>', 'San Francisco', 'Active'],
      ['Jane Smith', 28, 'Marketing', 75000, '2022-01-10', '<EMAIL>', 'New York', 'Active'],
      ['Bob Johnson', 45, 'Sales', 85000, '2019-07-22', '<EMAIL>', 'Chicago', 'Active'],
      ['Alice Williams', 36, 'HR', 70000, '2020-11-05', '<EMAIL>', 'Austin', 'Active'],
      ['Charlie Brown', 29, 'Engineering', 90000, '2021-09-01', '<EMAIL>', 'Seattle', 'Active'],
      ['Diana Prince', 31, 'Marketing', 78000, '2020-06-15', '<EMAIL>', 'Los Angeles', 'Active'],
      ['Edward Norton', 42, 'Finance', 110000, '2018-04-20', '<EMAIL>', 'Boston', 'Active'],
      ['Fiona Green', 27, 'Engineering', 88000, '2022-08-30', '<EMAIL>', 'Portland', 'Active'],
      ['George White', 38, 'Sales', 92000, '2019-12-10', '<EMAIL>', 'Miami', 'Active'],
      ['Helen Black', 33, 'HR', 72000, '2021-02-28', '<EMAIL>', 'Denver', 'Active']
    ],
    totalRows: 10,
    dataTypes: {
      'Name': 'string',
      'Age': 'number',
      'Department': 'string', 
      'Salary': 'number',
      'Start Date': 'date',
      'Email': 'string',
      'Location': 'string',
      'Status': 'string'
    }
  },
  processingTemplates: [
    {
      id: 'ner',
      name: 'Named Entity Recognition',
      description: 'Extract entities from text data',
      category: 'NLP' as const
    },
    {
      id: 'sentiment',
      name: 'Sentiment Analysis', 
      description: 'Analyze sentiment in text data',
      category: 'NLP' as const
    },
    {
      id: 'clustering',
      name: 'Data Clustering',
      description: 'Group similar data points',
      category: 'ML' as const
    },
    {
      id: 'classification',
      name: 'Data Classification',
      description: 'Classify data into categories',
      category: 'ML' as const
    }
  ]
};

// Mock data for root component props
export const mockRootProps = {
  activeView: ViewType.DASHBOARD,
  currentStage: StageType.PREVIEW,
  isProcessing: false,
  processingStatus: ProcessingStatus.IDLE,
  selectedFile: {
    id: 'file-1',
    name: 'sample_data.csv',
    type: FileType.CSV,
    size: 2048576,
    lastModified: new Date('2024-01-15T10:30:00Z'),
    uploadTime: new Date('2024-01-15T10:30:00Z')
  },
  dataPreview: {
    headers: ['Name', 'Age', 'Department', 'Salary', 'Start Date'],
    rows: [
      ['John Doe', 32, 'Engineering', 95000, '2021-03-15'],
      ['Jane Smith', 28, 'Marketing', 75000, '2022-01-10'],
      ['Bob Johnson', 45, 'Sales', 85000, '2019-07-22']
    ],
    totalRows: 1250,
    dataTypes: {
      'Name': 'string',
      'Age': 'number', 
      'Department': 'string',
      'Salary': 'number',
      'Start Date': 'date'
    }
  },
  processingResult: {
    content: 'Processing completed successfully',
    metadata: {
      template: 'ner',
      processedAt: new Date('2024-01-15T11:00:00Z'),
      recordCount: 1250,
      duration: 2500,
      outputSize: 512000
    },
    recordsProcessed: 1250,
    duration: 2500,
    outputSize: 512
  },
  breadcrumbPath: [
    {
      stage: 'upload',
      label: 'File Upload',
      timestamp: new Date('2024-01-15T10:30:00Z'),
      dataSummary: {
        fileType: 'CSV',
        rows: 1250,
        columns: 5
      }
    },
    {
      stage: 'preview',
      label: 'Data Preview',
      timestamp: new Date('2024-01-15T10:35:00Z'),
      dataSummary: {
        rows: 1250,
        columns: 5
      }
    }
  ],
  contextMenu: {
    visible: false,
    x: 0,
    y: 0
  },
  config: {
    features: {
      dashboard: true,
      storytelling: true,
      streamlinedNavigation: true,
      userFeedback: true,
      accessibilityFeatures: true,
      versionHistory: true,
      quickActions: true
    },
    performance: {
      maxFileSize: 100,
      chunkSize: 1000,
      workerPoolSize: 3,
      cacheEnabled: true
    }
  }
};