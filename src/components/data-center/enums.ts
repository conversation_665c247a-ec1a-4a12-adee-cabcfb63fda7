// Enums for Data Center interface types and states

export enum ViewType {
  UPLOAD = 'upload',
  DASHBOARD = 'dashboard', 
  GRID = 'grid',
  SQL = 'sql',
  TERMINAL = 'terminal'
}

export enum StageType {
  UPLOAD = 'upload',
  PREVIEW = 'preview',
  EXPLORE = 'explore',
  PROCESSING = 'processing',
  ANALYSIS = 'analysis',
  SQL = 'sql',
  STORAGE = 'storage',
  STORYTELLING = 'storytelling'
}

export enum ProcessingStatus {
  IDLE = 'idle',
  PROCESSING = 'processing',
  SUCCESS = 'success',
  ERROR = 'error'
}

export enum FileType {
  CSV = 'csv',
  TSV = 'tsv',
  XLSX = 'xlsx',
  XLS = 'xls',
  JSON = 'json',
  TXT = 'txt'
}

export enum ComponentVariant {
  DEFAULT = 'default',
  DATA = 'data',
  SUCCESS = 'success',
  WARNING = 'warning',
  ERROR = 'error',
  INFO = 'info'
}

export enum ButtonVariant {
  DEFAULT = 'default',
  DESTRUCTIVE = 'destructive',
  OUTLINE = 'outline',
  SECONDARY = 'secondary',
  GHOST = 'ghost',
  LINK = 'link'
}

export enum ButtonSize {
  XS = 'xs',
  SM = 'sm', 
  DEFAULT = 'default',
  LG = 'lg',
  ICON = 'icon'
}