/**
 * Webpack Bundle Analyzer Configuration
 * Analyzes bundle size and identifies optimization opportunities
 */

const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;
const path = require('path');

module.exports = {
  mode: 'production',
  entry: {
    'data-center': './src/components/data-center/index.ts',
    'data-center-core': './src/components/data-center/core/index.ts',
    'data-center-features': './src/components/data-center/features/index.ts',
    'data-center-utils': './src/components/data-center/utils/index.ts',
  },
  output: {
    path: path.resolve(__dirname, '../../../dist/analysis'),
    filename: '[name].[contenthash].js',
    clean: true,
  },
  resolve: {
    extensions: ['.ts', '.tsx', '.js', '.jsx'],
    alias: {
      '@': path.resolve(__dirname, '../../../'),
    },
  },
  module: {
    rules: [
      {
        test: /\.(ts|tsx)$/,
        use: [
          {
            loader: 'ts-loader',
            options: {
              transpileOnly: true,
            },
          },
        ],
        exclude: /node_modules/,
      },
      {
        test: /\.(js|jsx)$/,
        use: {
          loader: 'babel-loader',
          options: {
            presets: [
              '@babel/preset-env',
              '@babel/preset-react',
              '@babel/preset-typescript',
            ],
          },
        },
        exclude: /node_modules/,
      },
      {
        test: /\.css$/,
        use: ['style-loader', 'css-loader'],
      },
    ],
  },
  plugins: [
    new BundleAnalyzerPlugin({
      analyzerMode: 'static',
      openAnalyzer: false,
      reportFilename: 'bundle-report.html',
      generateStatsFile: true,
      statsFilename: 'bundle-stats.json',
      logLevel: 'info',
    }),
  ],
  optimization: {
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all',
        },
        common: {
          name: 'common',
          minChunks: 2,
          chunks: 'all',
          enforce: true,
        },
        utils: {
          test: /[\\/]utils[\\/]/,
          name: 'utils',
          chunks: 'all',
        },
        features: {
          test: /[\\/]features[\\/]/,
          name: 'features',
          chunks: 'all',
        },
      },
    },
    usedExports: true,
    sideEffects: false,
  },
  externals: {
    react: 'React',
    'react-dom': 'ReactDOM',
  },
};