/**
 * Configuration Examples
 * Demonstrates how to use the new configuration system
 */

import { DataCenterConfigBuilder, ConfigUtils } from './builder';
import { CONFIG_PRESETS, PresetManager } from './presets';
import { ConfigMigrator, LegacyDataCenterConfig } from './migration';
import { DataCenterComponentConfig } from '../types';

// Example 1: Using presets
export const presetExamples = {
  // Simple setup for basic data viewing
  minimal: () => {
    return new DataCenterConfigBuilder()
      .preset('minimal')
      .build();
  },

  // Standard setup for most applications
  standard: () => {
    return new DataCenterConfigBuilder()
      .preset('standard')
      .theme('dark')
      .build();
  },

  // Analytics-focused configuration
  analytics: () => {
    return new DataCenterConfigBuilder()
      .preset('analytics')
      .addFeature('predictiveAnalytics')
      .performance({ cacheSize: 300 })
      .build();
  },

  // Enterprise configuration with security features
  enterprise: () => {
    return new DataCenterConfigBuilder()
      .preset('enterprise')
      .accessibility({ highContrast: true })
      .performance({ cacheSize: 1000 })
      .build();
  },

  // Developer mode with all features
  developer: () => {
    return new DataCenterConfigBuilder()
      .preset('developer')
      .addFeature('debugMode')
      .addFeature('performanceProfiler')
      .build();
  }
};

// Example 2: Custom configurations
export const customConfigExamples = {
  // Custom configuration from scratch
  customFromScratch: () => {
    return new DataCenterConfigBuilder()
      .features(['fileUpload', 'dataPreview', 'dashboard', 'pythonScripting'])
      .theme('auto')
      .accessibility({
        highContrast: false,
        screenReader: true,
        keyboardNavigation: true
      })
      .performance({
        virtualizeGrids: true,
        lazyLoadWidgets: true,
        cacheSize: 150
      })
      .build();
  },

  // Customizing an existing preset
  customizedPreset: () => {
    return new DataCenterConfigBuilder()
      .preset('standard')
      .addFeature('machineLearning')
      .removeFeature('pythonScripting')
      .theme('dark')
      .performance({ cacheSize: 250 })
      .build();
  },

  // Feature group-based configuration
  featureGroupBased: () => {
    return new DataCenterConfigBuilder()
      .featureGroups(['core', 'analytics', 'collaboration'])
      .theme('light')
      .build();
  }
};

// Example 3: Configuration validation
export const validationExamples = {
  // Validate a configuration
  validateConfig: (config: DataCenterComponentConfig) => {
    const builder = new DataCenterConfigBuilder(config);
    const validation = builder.validate();
    
    if (!validation.valid) {
      console.error('Configuration validation failed:', validation.errors);
      return false;
    }
    
    console.log('Configuration is valid');
    return true;
  },

  // Get configuration recommendations
  getRecommendations: (config: DataCenterComponentConfig) => {
    const recommendations = ConfigUtils.getRecommendations(config);
    console.log('Configuration recommendations:', recommendations);
    return recommendations;
  }
};

// Example 4: Configuration migration
export const migrationExamples = {
  // Migrate from legacy configuration
  migrateLegacyConfig: () => {
    const legacyConfig: LegacyDataCenterConfig = {
      features: {
        dataPreview: true,
        sqlGeneration: true,
        pythonExecution: true,
        dataVisualization: true,
        fileUpload: true,
        dataExport: true,
        customWidgets: true
      },
      ui: {
        theme: 'dark',
        layout: 'comfortable',
        showSidebar: true,
        enableAnimations: true
      },
      performance: {
        enableCaching: true,
        cacheSize: 200,
        lazyLoading: true,
        virtualScrolling: true
      },
      accessibility: {
        highContrast: false,
        screenReaderSupport: true,
        keyboardNavigation: true
      }
    };

    const migrationResult = ConfigMigrator.migrate(legacyConfig, {
      preserveCustomizations: true,
      suggestPresets: true,
      validateResult: true
    });

    if (migrationResult.success) {
      console.log('Migration successful!');
      console.log('Recommended preset:', migrationResult.recommendedPreset);
      console.log('Changes:', migrationResult.changes);
      
      if (migrationResult.warnings.length > 0) {
        console.warn('Migration warnings:', migrationResult.warnings);
      }
      
      return migrationResult.newConfig;
    } else {
      console.error('Migration failed:', migrationResult.warnings);
      return null;
    }
  },

  // Generate migration guide
  generateMigrationGuide: (legacyConfig: LegacyDataCenterConfig) => {
    const migrationResult = ConfigMigrator.migrate(legacyConfig);
    const guide = ConfigMigrator.generateMigrationGuide(migrationResult);
    
    console.log('Migration Guide:');
    console.log(guide);
    
    return guide;
  },

  // Batch migrate multiple configurations
  batchMigration: () => {
    const legacyConfigs = {
      development: {
        features: {
          dataPreview: true,
          sqlGeneration: true,
          pythonExecution: true,
          customWidgets: true
        }
      },
      production: {
        features: {
          dataPreview: true,
          dataVisualization: true,
          fileUpload: true
        },
        performance: {
          cacheSize: 500,
          virtualScrolling: true
        }
      }
    };

    const results = ConfigMigrator.batchMigrate(legacyConfigs);
    
    Object.entries(results).forEach(([name, result]) => {
      console.log(`Migration result for ${name}:`, {
        success: result.success,
        preset: result.recommendedPreset,
        warnings: result.warnings.length
      });
    });
    
    return results;
  }
};

// Example 5: Configuration comparison and merging
export const configUtilityExamples = {
  // Compare two configurations
  compareConfigs: () => {
    const config1 = new DataCenterConfigBuilder().preset('standard').build();
    const config2 = new DataCenterConfigBuilder().preset('analytics').build();
    
    const comparison = ConfigUtils.compare(config1, config2);
    
    console.log('Configuration comparison:', {
      identical: comparison.identical,
      featureDifferences: comparison.differences.features,
      themeDifferent: comparison.differences.theme
    });
    
    return comparison;
  },

  // Merge configurations
  mergeConfigs: () => {
    const baseConfig = new DataCenterConfigBuilder().preset('standard').build();
    const overrideConfig: Partial<DataCenterComponentConfig> = {
      theme: 'dark',
      enabledFeatures: ['machineLearning', 'customWidgets'],
      performance: {
        cacheSize: 300,
        virtualizeGrids: true,
        lazyLoadWidgets: true
      }
    };
    
    const mergedConfig = ConfigUtils.merge(baseConfig, overrideConfig);
    
    console.log('Merged configuration:', mergedConfig);
    return mergedConfig;
  },

  // Create configuration from preset utility
  createFromPreset: () => {
    const config = ConfigUtils.fromPreset('analytics');
    console.log('Configuration from preset:', config);
    return config;
  }
};

// Example 6: Preset management
export const presetManagementExamples = {
  // Get all available presets
  getAllPresets: () => {
    const presets = PresetManager.getAllPresets();
    console.log('Available presets:', presets.map(p => ({ id: p.id, name: p.name, complexity: p.complexity })));
    return presets;
  },

  // Get presets by complexity
  getPresetsByComplexity: () => {
    const beginnerPresets = PresetManager.getPresetsByComplexity('beginner');
    const advancedPresets = PresetManager.getPresetsByComplexity('advanced');
    
    console.log('Beginner presets:', beginnerPresets.map(p => p.name));
    console.log('Advanced presets:', advancedPresets.map(p => p.name));
    
    return { beginnerPresets, advancedPresets };
  },

  // Get recommended preset based on requirements
  getRecommendedPreset: () => {
    const recommendation = PresetManager.getRecommendedPreset({
      complexity: 'intermediate',
      performance: 'balanced',
      useCase: 'analysis'
    });
    
    console.log('Recommended preset:', recommendation.name);
    return recommendation;
  },

  // Create custom preset
  createCustomPreset: () => {
    const customPreset = PresetManager.createCustomPreset(
      'standard',
      {
        enabledFeatures: ['machineLearning', 'predictiveAnalytics'],
        theme: 'dark',
        performance: { 
          virtualizeGrids: true,
          lazyLoadWidgets: true,
          cacheSize: 400 
        }
      },
      {
        id: 'custom-analytics',
        name: 'Custom Analytics',
        description: 'Custom preset for advanced analytics',
        useCase: 'Advanced data analysis with ML'
      }
    );
    
    if (customPreset) {
      console.log('Custom preset created:', customPreset.name);
    }
    
    return customPreset;
  }
};

// Example usage in a React component
export const reactComponentExample = `
// Example: Using configuration in a React component
import React from 'react';
import { DataCenterComponent } from '@/components/data-center';
import { DataCenterConfigBuilder } from '@/components/data-center/config';

const MyDataCenterApp: React.FC = () => {
  // Create configuration using builder pattern
  const config = new DataCenterConfigBuilder()
    .preset('analytics')
    .addFeature('machineLearning')
    .theme('dark')
    .accessibility({ highContrast: true })
    .build();

  return (
    <DataCenterComponent 
      config={config}
      onConfigChange={(newConfig) => {
        console.log('Configuration changed:', newConfig);
      }}
    />
  );
};

export default MyDataCenterApp;
`;

// Export all examples
export const allExamples = {
  presetExamples,
  customConfigExamples,
  validationExamples,
  migrationExamples,
  configUtilityExamples,
  presetManagementExamples,
  reactComponentExample
};

// Helper function to run all examples
export const runAllExamples = () => {
  console.log('=== Configuration System Examples ===\n');
  
  console.log('1. Preset Examples:');
  Object.entries(presetExamples).forEach(([name, example]) => {
    console.log(`  ${name}:`, example());
  });
  
  console.log('\n2. Custom Configuration Examples:');
  Object.entries(customConfigExamples).forEach(([name, example]) => {
    console.log(`  ${name}:`, example());
  });
  
  console.log('\n3. Migration Examples:');
  migrationExamples.migrateLegacyConfig();
  
  console.log('\n4. Utility Examples:');
  configUtilityExamples.compareConfigs();
  configUtilityExamples.mergeConfigs();
  
  console.log('\n5. Preset Management Examples:');
  presetManagementExamples.getAllPresets();
  presetManagementExamples.getRecommendedPreset();
  
  console.log('\n=== Examples Complete ===');
};