# Data Center Configuration System

A simplified, preset-based configuration system for the Data Center component library. This system provides intuitive presets, a powerful builder pattern, migration utilities, and comprehensive validation.

## Table of Contents

- [Quick Start](#quick-start)
- [Configuration Presets](#configuration-presets)
- [Builder Pattern](#builder-pattern)
- [Migration from Legacy Configs](#migration-from-legacy-configs)
- [Feature Groups](#feature-groups)
- [Validation](#validation)
- [Examples](#examples)
- [API Reference](#api-reference)
- [Troubleshooting](#troubleshooting)

## Quick Start

### Using Presets (Recommended)

```typescript
import { DataCenterConfigBuilder } from '@/components/data-center/config';

// Simple setup for basic data viewing
const minimalConfig = new DataCenterConfigBuilder()
  .preset('minimal')
  .build();

// Standard setup for most applications
const standardConfig = new DataCenterConfigBuilder()
  .preset('standard')
  .theme('dark')
  .build();

// Enterprise setup with all security features
const enterpriseConfig = new DataCenterConfigBuilder()
  .preset('enterprise')
  .accessibility({ highContrast: true })
  .build();
```

### Custom Configuration

```typescript
// Build from scratch
const customConfig = new DataCenterConfigBuilder()
  .features(['fileUpload', 'dataPreview', 'dashboard'])
  .theme('auto')
  .performance({ cacheSize: 200 })
  .build();

// Customize existing preset
const customizedConfig = new DataCenterConfigBuilder()
  .preset('standard')
  .addFeature('machineLearning')
  .removeFeature('pythonScripting')
  .build();
```

## Configuration Presets

Presets provide pre-configured setups for common use cases:

### Available Presets

| Preset | Use Case | Features | Bundle Size | Complexity |
|--------|----------|----------|-------------|------------|
| `minimal` | Basic data viewing | File upload, data preview | Small | Beginner |
| `standard` | General data analysis | Core + analytics + processing | Medium | Intermediate |
| `analytics` | Data science & BI | Advanced analytics + ML | Large | Advanced |
| `enterprise` | Large organizations | Full feature set + security | Large | Advanced |
| `developer` | Development & testing | All features enabled | Large | Advanced |

### Preset Details

#### Minimal Preset
- **Features**: File upload, data preview
- **Performance**: Optimized for speed and small bundle size
- **Best for**: Simple data viewing applications

```typescript
const config = new DataCenterConfigBuilder().preset('minimal').build();
```

#### Standard Preset
- **Features**: Core functionality + basic analytics + Python scripting
- **Performance**: Balanced performance and features
- **Best for**: Most data analysis applications

```typescript
const config = new DataCenterConfigBuilder().preset('standard').build();
```

#### Analytics Preset
- **Features**: Advanced analytics, machine learning, custom widgets
- **Performance**: Optimized for data processing
- **Best for**: Data science and business intelligence

```typescript
const config = new DataCenterConfigBuilder().preset('analytics').build();
```

#### Enterprise Preset
- **Features**: Full feature set + security + compliance + audit
- **Performance**: High memory usage, comprehensive caching
- **Best for**: Large organizations with security requirements

```typescript
const config = new DataCenterConfigBuilder().preset('enterprise').build();
```

#### Developer Preset
- **Features**: All features + development tools + performance monitoring
- **Performance**: All optimizations disabled for testing
- **Best for**: Development, testing, and feature exploration

```typescript
const config = new DataCenterConfigBuilder().preset('developer').build();
```

## Builder Pattern

The `DataCenterConfigBuilder` provides a fluent API for creating configurations:

### Core Methods

```typescript
const builder = new DataCenterConfigBuilder();

// Set preset as base
builder.preset('standard');

// Configure features
builder.features(['feature1', 'feature2']); // Set exact features
builder.addFeature('newFeature');           // Add single feature
builder.removeFeature('oldFeature');        // Remove single feature

// Configure feature groups
builder.featureGroups(['core', 'analytics']);

// Configure theme
builder.theme('dark'); // 'light' | 'dark' | 'auto'

// Configure accessibility
builder.accessibility({
  highContrast: true,
  screenReader: true,
  keyboardNavigation: true
});

// Configure performance
builder.performance({
  virtualizeGrids: true,
  lazyLoadWidgets: true,
  cacheSize: 200
});

// Build final configuration
const config = builder.build();

// Validate configuration
const validation = builder.validate();
if (!validation.valid) {
  console.error('Validation errors:', validation.errors);
}

// Reset builder
builder.reset();
```

### Method Chaining

All builder methods return the builder instance, enabling method chaining:

```typescript
const config = new DataCenterConfigBuilder()
  .preset('standard')
  .addFeature('machineLearning')
  .theme('dark')
  .accessibility({ highContrast: true })
  .performance({ cacheSize: 300 })
  .build();
```

## Migration from Legacy Configs

The migration system helps transition from old configuration formats:

### Automatic Migration

```typescript
import { ConfigMigrator } from '@/components/data-center/config';

// Legacy configuration
const legacyConfig = {
  features: {
    dataPreview: true,
    sqlGeneration: true,
    pythonExecution: true,
    dataVisualization: true
  },
  ui: {
    theme: 'dark',
    layout: 'comfortable'
  },
  performance: {
    enableCaching: true,
    cacheSize: 200,
    virtualScrolling: true
  }
};

// Migrate to new format
const migrationResult = ConfigMigrator.migrate(legacyConfig, {
  preserveCustomizations: true,
  suggestPresets: true,
  validateResult: true
});

if (migrationResult.success) {
  console.log('Recommended preset:', migrationResult.recommendedPreset);
  console.log('New configuration:', migrationResult.newConfig);
  
  // Check for warnings
  if (migrationResult.warnings.length > 0) {
    console.warn('Migration warnings:', migrationResult.warnings);
  }
} else {
  console.error('Migration failed');
}
```

### Migration Guide Generation

```typescript
// Generate step-by-step migration guide
const guide = ConfigMigrator.generateMigrationGuide(migrationResult);
console.log(guide); // Markdown-formatted migration guide
```

### Batch Migration

```typescript
// Migrate multiple configurations at once
const configs = {
  development: legacyDevConfig,
  production: legacyProdConfig,
  testing: legacyTestConfig
};

const results = ConfigMigrator.batchMigrate(configs);
Object.entries(results).forEach(([name, result]) => {
  console.log(`${name}: ${result.success ? 'Success' : 'Failed'}`);
});
```

## Feature Groups

Feature groups provide logical groupings of related features:

### Available Feature Groups

- **core**: Essential functionality (file upload, data preview, basic processing)
- **analytics**: Data analysis and visualization
- **processing**: Python scripting and data transformation
- **database**: SQL query generation and database operations
- **collaboration**: Sharing, comments, and team features
- **advanced**: Machine learning, AI, and advanced analytics
- **enterprise**: Security, compliance, and enterprise integrations

### Using Feature Groups

```typescript
// Configure using feature groups
const config = new DataCenterConfigBuilder()
  .featureGroups(['core', 'analytics', 'collaboration'])
  .build();

// Get features for specific groups
import { FeatureGroupManager } from '@/components/data-center/config';

const analyticsFeatures = FeatureGroupManager.getFeaturesForGroups(['analytics']);
console.log('Analytics features:', analyticsFeatures);
```

## Validation

The configuration system includes comprehensive validation:

### Builder Validation

```typescript
const builder = new DataCenterConfigBuilder()
  .preset('standard')
  .addFeature('customFeature');

const validation = builder.validate();
if (!validation.valid) {
  console.error('Validation errors:', validation.errors);
}
```

### Complete Configuration Validation

```typescript
import { ConfigValidator } from '@/components/data-center/config';

const validation = ConfigValidator.validateComplete(config);
console.log('Validation result:', {
  valid: validation.valid,
  errors: validation.errors,
  warnings: validation.warnings,
  suggestions: validation.suggestions
});
```

### Feature Combination Validation

```typescript
const featureValidation = ConfigValidator.validateFeatureCombination([
  'machineLearning',
  'pythonScripting',
  'customWidgets'
]);

if (!featureValidation.valid) {
  console.warn('Feature conflicts:', featureValidation.conflicts);
}
```

## Examples

See [examples.ts](./examples.ts) for comprehensive usage examples including:

- Preset usage examples
- Custom configuration examples
- Migration examples
- Validation examples
- Utility function examples
- React component integration examples

## API Reference

### DataCenterConfigBuilder

```typescript
class DataCenterConfigBuilder {
  constructor(initialConfig?: Partial<DataCenterComponentConfig>)
  
  // Configuration methods
  preset(presetId: string): ConfigBuilder
  features(features: string[]): ConfigBuilder
  addFeature(feature: string): ConfigBuilder
  removeFeature(feature: string): ConfigBuilder
  featureGroups(groupIds: string[]): ConfigBuilder
  theme(theme: 'light' | 'dark' | 'auto'): ConfigBuilder
  accessibility(options: Partial<AccessibilityConfig>): ConfigBuilder
  performance(options: Partial<PerformanceConfig>): ConfigBuilder
  
  // Build and validation
  build(): DataCenterComponentConfig
  validate(): { valid: boolean; errors: string[] }
  reset(): ConfigBuilder
  
  // Utility methods
  getPreset(): ConfigPreset | undefined
  getConfig(): Partial<DataCenterComponentConfig>
  hasFeature(feature: string): boolean
  getEnabledFeatures(): string[]
  clone(): DataCenterConfigBuilder
}
```

### ConfigMigrator

```typescript
class ConfigMigrator {
  static migrate(
    legacyConfig: LegacyDataCenterConfig,
    options?: MigrationOptions
  ): MigrationResult
  
  static generateMigrationGuide(result: MigrationResult): string
  
  static batchMigrate(
    configs: Record<string, LegacyDataCenterConfig>,
    options?: MigrationOptions
  ): Record<string, MigrationResult>
}
```

### PresetManager

```typescript
class PresetManager {
  static getPreset(presetId: string): ConfigPreset | null
  static getAllPresets(): ConfigPreset[]
  static getPresetsByComplexity(complexity: string): ConfigPreset[]
  static getRecommendedPreset(requirements: Requirements): ConfigPreset
  static createCustomPreset(
    basePresetId: string,
    customizations: Partial<DataCenterComponentConfig>,
    metadata: PresetMetadata
  ): ConfigPreset | null
}
```

### ConfigUtils

```typescript
class ConfigUtils {
  static createBuilder(preset?: string): DataCenterConfigBuilder
  static fromPreset(presetId: string): DataCenterComponentConfig
  static merge(
    base: DataCenterComponentConfig,
    override: Partial<DataCenterComponentConfig>
  ): DataCenterComponentConfig
  static compare(
    config1: DataCenterComponentConfig,
    config2: DataCenterComponentConfig
  ): ConfigComparison
  static getRecommendations(config: DataCenterComponentConfig): string[]
}
```

## Troubleshooting

### Common Issues

#### Invalid Preset Error
```typescript
// Error: Unknown preset: 'invalid-preset'
const config = new DataCenterConfigBuilder().preset('invalid-preset');

// Solution: Use valid preset names
const validPresets = PresetManager.getAllPresets().map(p => p.id);
console.log('Valid presets:', validPresets);
```

#### Feature Validation Errors
```typescript
// Check if feature exists before adding
const builder = new DataCenterConfigBuilder();
if (builder.hasFeature('customFeature')) {
  builder.addFeature('customFeature');
}

// Or validate after building
const validation = builder.validate();
if (!validation.valid) {
  console.error('Fix these issues:', validation.errors);
}
```

#### Migration Warnings
```typescript
// Handle migration warnings
const result = ConfigMigrator.migrate(legacyConfig);
if (result.warnings.length > 0) {
  console.warn('Review these changes:', result.warnings);
  
  // Generate detailed migration guide
  const guide = ConfigMigrator.generateMigrationGuide(result);
  console.log(guide);
}
```

### Performance Considerations

- Use `minimal` or `standard` presets for better performance
- Enable `virtualizeGrids` for large datasets
- Set appropriate `cacheSize` based on available memory
- Use `lazyLoadWidgets` to improve initial load time

### Best Practices

1. **Start with presets**: Use presets as base configurations and customize as needed
2. **Validate configurations**: Always validate configurations before deployment
3. **Use feature groups**: Group related features for easier management
4. **Handle migration warnings**: Review and address migration warnings
5. **Test configurations**: Test configurations in development before production
6. **Document customizations**: Document any custom configurations for team members

## Contributing

To contribute to the configuration system:

1. Add new presets to `presets.ts`
2. Extend builder methods in `builder.ts`
3. Add migration logic in `migration.ts`
4. Update examples in `examples.ts`
5. Add tests for new functionality
6. Update this documentation

## License

This configuration system is part of the Data Center component library and follows the same license terms.