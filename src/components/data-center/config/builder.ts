/**
 * Configuration Builder
 * Utility for creating and customizing data center configurations
 */

import { DataCenterComponentConfig } from '../types';
import { ConfigPreset, PresetManager, FeatureGroupManager, FEATURE_GROUPS } from './presets';

// Configuration builder interface
export interface ConfigBuilder {
  preset(presetId: string): ConfigBuilder;
  features(features: string[]): ConfigBuilder;
  addFeature(feature: string): ConfigBuilder;
  removeFeature(feature: string): ConfigBuilder;
  theme(theme: 'light' | 'dark' | 'auto'): ConfigBuilder;
  accessibility(options: Partial<DataCenterComponentConfig['accessibility']>): ConfigBuilder;
  performance(options: Partial<DataCenterComponentConfig['performance']>): ConfigBuilder;
  featureGroups(groupIds: string[]): ConfigBuilder;
  build(): DataCenterComponentConfig;
  validate(): { valid: boolean; errors: string[] };
  reset(): ConfigBuilder;
}

// Configuration validation result
export interface ConfigValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
  suggestions: string[];
}

// Configuration builder implementation
export class DataCenterConfigBuilder implements ConfigBuilder {
  private config: Partial<DataCenterComponentConfig>;
  private selectedPreset?: ConfigPreset;

  constructor(initialConfig?: Partial<DataCenterComponentConfig>) {
    this.config = initialConfig || {
      enabledFeatures: [],
      theme: 'auto',
      accessibility: {
        highContrast: false,
        screenReader: true,
        keyboardNavigation: true
      },
      performance: {
        virtualizeGrids: true,
        lazyLoadWidgets: true,
        cacheSize: 100
      }
    };
  }

  preset(presetId: string): ConfigBuilder {
    const preset = PresetManager.getPreset(presetId);
    if (!preset) {
      throw new Error(`Unknown preset: ${presetId}`);
    }
    
    this.selectedPreset = preset;
    this.config = { ...preset.config };
    return this;
  }

  features(features: string[]): ConfigBuilder {
    this.config.enabledFeatures = [...features];
    return this;
  }

  addFeature(feature: string): ConfigBuilder {
    if (!this.config.enabledFeatures) {
      this.config.enabledFeatures = [];
    }
    if (!this.config.enabledFeatures.includes(feature)) {
      this.config.enabledFeatures.push(feature);
    }
    return this;
  }

  removeFeature(feature: string): ConfigBuilder {
    if (this.config.enabledFeatures) {
      this.config.enabledFeatures = this.config.enabledFeatures.filter(f => f !== feature);
    }
    return this;
  }

  theme(theme: 'light' | 'dark' | 'auto'): ConfigBuilder {
    this.config.theme = theme;
    return this;
  }

  accessibility(options: Partial<DataCenterComponentConfig['accessibility']>): ConfigBuilder {
    this.config.accessibility = {
      highContrast: false,
      screenReader: true,
      keyboardNavigation: true,
      ...this.config.accessibility,
      ...options
    };
    return this;
  }

  performance(options: Partial<DataCenterComponentConfig['performance']>): ConfigBuilder {
    this.config.performance = {
      virtualizeGrids: true,
      lazyLoadWidgets: true,
      cacheSize: 100,
      ...this.config.performance,
      ...options
    };
    return this;
  }

  featureGroups(groupIds: string[]): ConfigBuilder {
    const validation = FeatureGroupManager.validateFeatureGroups(groupIds);
    if (!validation.valid) {
      throw new Error(`Invalid feature groups: ${validation.errors.join(', ')}`);
    }
    
    const features = FeatureGroupManager.getFeaturesForGroups(groupIds);
    this.config.enabledFeatures = features;
    return this;
  }

  build(): DataCenterComponentConfig {
    const validation = this.validate();
    if (!validation.valid) {
      throw new Error(`Invalid configuration: ${validation.errors.join(', ')}`);
    }
    
    return {
      enabledFeatures: this.config.enabledFeatures || [],
      theme: this.config.theme || 'auto',
      accessibility: {
        highContrast: false,
        screenReader: true,
        keyboardNavigation: true,
        ...this.config.accessibility
      },
      performance: {
        virtualizeGrids: true,
        lazyLoadWidgets: true,
        cacheSize: 100,
        ...this.config.performance
      }
    };
  }

  validate(): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    // Validate required fields
    if (!this.config.enabledFeatures || this.config.enabledFeatures.length === 0) {
      errors.push('At least one feature must be enabled');
    }
    
    // Validate theme
    if (this.config.theme && !['light', 'dark', 'auto'].includes(this.config.theme)) {
      errors.push('Theme must be light, dark, or auto');
    }
    
    // Validate performance settings
    if (this.config.performance?.cacheSize && this.config.performance.cacheSize < 0) {
      errors.push('Cache size must be non-negative');
    }
    
    // Validate feature dependencies (basic validation)
    const enabledFeatures = this.config.enabledFeatures || [];
    if (enabledFeatures.includes('dashboard') && !enabledFeatures.includes('dataPreview')) {
      errors.push('Dashboard feature requires dataPreview feature');
    }
    
    if (enabledFeatures.includes('sqlGeneration') && !enabledFeatures.includes('dataPreview')) {
      errors.push('SQL generation feature requires dataPreview feature');
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }

  reset(): ConfigBuilder {
    this.config = {
      enabledFeatures: [],
      theme: 'auto',
      accessibility: {
        highContrast: false,
        screenReader: true,
        keyboardNavigation: true
      },
      performance: {
        virtualizeGrids: true,
        lazyLoadWidgets: true,
        cacheSize: 100
      }
    };
    this.selectedPreset = undefined;
    return this;
  }

  // Additional utility methods
  getPreset(): ConfigPreset | undefined {
    return this.selectedPreset;
  }

  getConfig(): Partial<DataCenterComponentConfig> {
    return { ...this.config };
  }

  hasFeature(feature: string): boolean {
    return this.config.enabledFeatures?.includes(feature) || false;
  }

  getEnabledFeatures(): string[] {
    return this.config.enabledFeatures || [];
  }

  clone(): DataCenterConfigBuilder {
    const cloned = new DataCenterConfigBuilder(this.config);
    cloned.selectedPreset = this.selectedPreset;
    return cloned;
  }
}

// Advanced configuration validator
export class ConfigValidator {
  static validateComplete(config: DataCenterComponentConfig): ConfigValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const suggestions: string[] = [];

    // Required field validation
    if (!config.enabledFeatures || config.enabledFeatures.length === 0) {
      errors.push('At least one feature must be enabled');
    }

    // Feature dependency validation
    const features = config.enabledFeatures || [];
    
    // Core dependencies
    if (features.includes('dashboard') && !features.includes('dataPreview')) {
      errors.push('Dashboard requires dataPreview feature');
    }
    
    if (features.includes('sqlGeneration') && !features.includes('dataPreview')) {
      errors.push('SQL generation requires dataPreview feature');
    }
    
    if (features.includes('machineLearning') && !features.includes('dashboard')) {
      warnings.push('Machine learning features work best with dashboard enabled');
    }

    // Performance validation
    if (config.performance.cacheSize < 0) {
      errors.push('Cache size must be non-negative');
    }
    
    if (config.performance.cacheSize > 1000) {
      warnings.push('Large cache size may impact memory usage');
    }
    
    if (!config.performance.virtualizeGrids && features.includes('dataPreview')) {
      warnings.push('Consider enabling grid virtualization for better performance with large datasets');
    }

    // Accessibility validation
    if (!config.accessibility.keyboardNavigation) {
      warnings.push('Keyboard navigation is recommended for accessibility');
    }
    
    if (!config.accessibility.screenReader) {
      warnings.push('Screen reader support is recommended for accessibility');
    }

    // Feature combination suggestions
    if (features.includes('fileUpload') && !features.includes('dataPreview')) {
      suggestions.push('Consider enabling dataPreview to view uploaded files');
    }
    
    if (features.includes('dataPreview') && !features.includes('dashboard')) {
      suggestions.push('Consider enabling dashboard for better data visualization');
    }
    
    if (features.length > 10) {
      suggestions.push('Consider using feature groups to organize many features');
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings,
      suggestions
    };
  }

  static validateFeatureCombination(features: string[]): { valid: boolean; conflicts: string[] } {
    const conflicts: string[] = [];
    
    // Example conflict rules (can be expanded)
    if (features.includes('basicMode') && features.includes('advancedMode')) {
      conflicts.push('Basic mode and advanced mode cannot be enabled together');
    }
    
    return {
      valid: conflicts.length === 0,
      conflicts
    };
  }
}

// Configuration utilities
export class ConfigUtils {
  static createBuilder(preset?: string): DataCenterConfigBuilder {
    const builder = new DataCenterConfigBuilder();
    if (preset) {
      builder.preset(preset);
    }
    return builder;
  }

  static fromPreset(presetId: string): DataCenterComponentConfig {
    return new DataCenterConfigBuilder().preset(presetId).build();
  }

  static merge(
    base: DataCenterComponentConfig,
    override: Partial<DataCenterComponentConfig>
  ): DataCenterComponentConfig {
    return {
      enabledFeatures: override.enabledFeatures || base.enabledFeatures,
      theme: override.theme || base.theme,
      accessibility: {
        ...base.accessibility,
        ...override.accessibility
      },
      performance: {
        ...base.performance,
        ...override.performance
      }
    };
  }

  static compare(
    config1: DataCenterComponentConfig,
    config2: DataCenterComponentConfig
  ): {
    identical: boolean;
    differences: {
      features: { added: string[]; removed: string[] };
      theme: boolean;
      accessibility: string[];
      performance: string[];
    };
  } {
    const features1 = new Set(config1.enabledFeatures);
    const features2 = new Set(config2.enabledFeatures);
    
    const addedFeatures = Array.from(features2).filter(f => !features1.has(f));
    const removedFeatures = Array.from(features1).filter(f => !features2.has(f));
    
    const accessibilityDiffs: string[] = [];
    if (config1.accessibility.highContrast !== config2.accessibility.highContrast) {
      accessibilityDiffs.push('highContrast');
    }
    if (config1.accessibility.screenReader !== config2.accessibility.screenReader) {
      accessibilityDiffs.push('screenReader');
    }
    if (config1.accessibility.keyboardNavigation !== config2.accessibility.keyboardNavigation) {
      accessibilityDiffs.push('keyboardNavigation');
    }
    
    const performanceDiffs: string[] = [];
    if (config1.performance.virtualizeGrids !== config2.performance.virtualizeGrids) {
      performanceDiffs.push('virtualizeGrids');
    }
    if (config1.performance.lazyLoadWidgets !== config2.performance.lazyLoadWidgets) {
      performanceDiffs.push('lazyLoadWidgets');
    }
    if (config1.performance.cacheSize !== config2.performance.cacheSize) {
      performanceDiffs.push('cacheSize');
    }
    
    const identical = 
      addedFeatures.length === 0 &&
      removedFeatures.length === 0 &&
      config1.theme === config2.theme &&
      accessibilityDiffs.length === 0 &&
      performanceDiffs.length === 0;
    
    return {
      identical,
      differences: {
        features: {
          added: addedFeatures,
          removed: removedFeatures
        },
        theme: config1.theme !== config2.theme,
        accessibility: accessibilityDiffs,
        performance: performanceDiffs
      }
    };
  }

  static getRecommendations(config: DataCenterComponentConfig): string[] {
    const recommendations: string[] = [];
    const features = config.enabledFeatures;
    
    // Performance recommendations
    if (features.length > 5 && !config.performance.lazyLoadWidgets) {
      recommendations.push('Enable lazy loading for better performance with many features');
    }
    
    if (features.includes('dataPreview') && !config.performance.virtualizeGrids) {
      recommendations.push('Enable grid virtualization for better performance with large datasets');
    }
    
    // Feature recommendations
    if (features.includes('fileUpload') && !features.includes('dataPreview')) {
      recommendations.push('Add dataPreview to view uploaded files');
    }
    
    if (features.includes('dataPreview') && !features.includes('dashboard')) {
      recommendations.push('Add dashboard for enhanced data visualization');
    }
    
    // Accessibility recommendations
    if (!config.accessibility.keyboardNavigation) {
      recommendations.push('Enable keyboard navigation for better accessibility');
    }
    
    return recommendations;
  }
}

// Export convenience functions
export const createConfig = ConfigUtils.createBuilder;
export const fromPreset = ConfigUtils.fromPreset;
export const mergeConfigs = ConfigUtils.merge;
export const compareConfigs = ConfigUtils.compare;