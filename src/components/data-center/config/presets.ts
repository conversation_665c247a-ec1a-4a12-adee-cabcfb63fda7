/**
 * Configuration Presets
 * Simplified configuration system with intuitive presets for different use cases
 */

import { DataCenterComponentConfig } from '../types';

// Feature group definitions
export interface FeatureGroup {
  id: string;
  name: string;
  description: string;
  features: string[];
  dependencies?: string[];
  conflicts?: string[];
}

// Configuration preset interface
export interface ConfigPreset {
  id: string;
  name: string;
  description: string;
  useCase: string;
  config: Partial<DataCenterComponentConfig>;
  featureGroups: string[];
  performance: {
    bundleSize: 'small' | 'medium' | 'large';
    loadTime: 'fast' | 'medium' | 'slow';
    memoryUsage: 'low' | 'medium' | 'high';
  };
  complexity: 'beginner' | 'intermediate' | 'advanced';
}

// Feature groups
export const FEATURE_GROUPS: Record<string, FeatureGroup> = {
  core: {
    id: 'core',
    name: 'Core Features',
    description: 'Essential data center functionality',
    features: ['fileUpload', 'dataPreview', 'basicProcessing']
  },
  
  analytics: {
    id: 'analytics',
    name: 'Data Analytics',
    description: 'Advanced data analysis and visualization',
    features: ['dashboard', 'charts', 'statistics', 'dataExploration'],
    dependencies: ['core']
  },
  
  processing: {
    id: 'processing',
    name: 'Data Processing',
    description: 'Python scripting and data transformation',
    features: ['pythonScripting', 'dataTransformation', 'backgroundProcessing'],
    dependencies: ['core']
  },
  
  database: {
    id: 'database',
    name: 'Database Integration',
    description: 'SQL query generation and database operations',
    features: ['sqlGeneration', 'queryBuilder', 'databaseConnection'],
    dependencies: ['core']
  },
  
  collaboration: {
    id: 'collaboration',
    name: 'Collaboration Tools',
    description: 'Sharing, comments, and team features',
    features: ['sharing', 'comments', 'versionHistory', 'teamWorkspace'],
    dependencies: ['core']
  },
  
  advanced: {
    id: 'advanced',
    name: 'Advanced Features',
    description: 'Machine learning, AI, and advanced analytics',
    features: ['machineLearning', 'aiInsights', 'predictiveAnalytics', 'customWidgets'],
    dependencies: ['core', 'analytics']
  },
  
  enterprise: {
    id: 'enterprise',
    name: 'Enterprise Features',
    description: 'Security, compliance, and enterprise integrations',
    features: ['sso', 'audit', 'compliance', 'enterpriseIntegrations', 'advancedSecurity'],
    dependencies: ['core']
  }
};

// Configuration presets
export const CONFIG_PRESETS: Record<string, ConfigPreset> = {
  minimal: {
    id: 'minimal',
    name: 'Minimal Setup',
    description: 'Basic file upload and preview functionality',
    useCase: 'Simple data viewing and basic operations',
    featureGroups: ['core'],
    performance: {
      bundleSize: 'small',
      loadTime: 'fast',
      memoryUsage: 'low'
    },
    complexity: 'beginner',
    config: {
      enabledFeatures: ['fileUpload', 'dataPreview'],
      theme: 'light',
      accessibility: {
        highContrast: false,
        screenReader: true,
        keyboardNavigation: true
      },
      performance: {
        virtualizeGrids: false,
        lazyLoadWidgets: true,
        cacheSize: 50
      }
    }
  },
  
  standard: {
    id: 'standard',
    name: 'Standard Setup',
    description: 'Balanced feature set for most use cases',
    useCase: 'General data analysis and visualization',
    featureGroups: ['core', 'analytics', 'processing'],
    performance: {
      bundleSize: 'medium',
      loadTime: 'medium',
      memoryUsage: 'medium'
    },
    complexity: 'intermediate',
    config: {
      enabledFeatures: ['fileUpload', 'dataPreview', 'basicProcessing', 'dashboard', 'pythonScripting', 'sqlGeneration', 'versionHistory', 'customWidgets'],
      theme: 'auto',
      accessibility: {
        highContrast: false,
        screenReader: true,
        keyboardNavigation: true
      },
      performance: {
        virtualizeGrids: true,
        lazyLoadWidgets: true,
        cacheSize: 100
      }
    }
  },
  
  analytics: {
    id: 'analytics',
    name: 'Analytics Focused',
    description: 'Optimized for data analysis and visualization',
    useCase: 'Data science and business intelligence',
    featureGroups: ['core', 'analytics', 'processing', 'advanced'],
    performance: {
      bundleSize: 'large',
      loadTime: 'medium',
      memoryUsage: 'high'
    },
    complexity: 'advanced',
    config: {
      enabledFeatures: ['fileUpload', 'dataPreview', 'basicProcessing', 'dashboard', 'pythonScripting', 'sqlGeneration', 'sharing', 'versionHistory', 'machineLearning', 'customWidgets'],
      theme: 'dark',
      accessibility: {
        highContrast: false,
        screenReader: true,
        keyboardNavigation: true
      },
      performance: {
        virtualizeGrids: true,
        lazyLoadWidgets: true,
        cacheSize: 200
      }
    }
  },
  
  enterprise: {
    id: 'enterprise',
    name: 'Enterprise Setup',
    description: 'Full feature set with enterprise capabilities',
    useCase: 'Large organizations with security and compliance needs',
    featureGroups: ['core', 'analytics', 'processing', 'database', 'collaboration', 'enterprise'],
    performance: {
      bundleSize: 'large',
      loadTime: 'slow',
      memoryUsage: 'high'
    },
    complexity: 'advanced',
    config: {
      enabledFeatures: ['fileUpload', 'dataPreview', 'basicProcessing', 'dashboard', 'pythonScripting', 'sqlGeneration', 'sharing', 'versionHistory', 'machineLearning', 'customWidgets', 'sso', 'audit', 'compliance'],
      theme: 'auto',
      accessibility: {
        highContrast: true,
        screenReader: true,
        keyboardNavigation: true
      },
      performance: {
        virtualizeGrids: true,
        lazyLoadWidgets: true,
        cacheSize: 500
      }
    }
  },
  
  developer: {
    id: 'developer',
    name: 'Developer Mode',
    description: 'All features enabled for development and testing',
    useCase: 'Development, testing, and feature exploration',
    featureGroups: ['core', 'analytics', 'processing', 'database', 'collaboration', 'advanced', 'enterprise'],
    performance: {
      bundleSize: 'large',
      loadTime: 'slow',
      memoryUsage: 'high'
    },
    complexity: 'advanced',
    config: {
      enabledFeatures: ['fileUpload', 'dataPreview', 'basicProcessing', 'dashboard', 'pythonScripting', 'sqlGeneration', 'sharing', 'versionHistory', 'machineLearning', 'customWidgets', 'devTools', 'performanceMonitoring'],
      theme: 'auto',
      accessibility: {
        highContrast: false,
        screenReader: true,
        keyboardNavigation: true
      },
      performance: {
        virtualizeGrids: true,
        lazyLoadWidgets: false, // Load everything for testing
        cacheSize: 1000
      }
    }
  }
};

// Preset utilities
export class PresetManager {
  static getPreset(presetId: string): ConfigPreset | null {
    return CONFIG_PRESETS[presetId] || null;
  }
  
  static getAllPresets(): ConfigPreset[] {
    return Object.values(CONFIG_PRESETS);
  }
  
  static getPresetsByComplexity(complexity: 'beginner' | 'intermediate' | 'advanced'): ConfigPreset[] {
    return Object.values(CONFIG_PRESETS).filter(preset => preset.complexity === complexity);
  }
  
  static getPresetsByPerformance(criteria: {
    bundleSize?: 'small' | 'medium' | 'large';
    loadTime?: 'fast' | 'medium' | 'slow';
    memoryUsage?: 'low' | 'medium' | 'high';
  }): ConfigPreset[] {
    return Object.values(CONFIG_PRESETS).filter(preset => {
      const { performance } = preset;
      return (
        (!criteria.bundleSize || performance.bundleSize === criteria.bundleSize) &&
        (!criteria.loadTime || performance.loadTime === criteria.loadTime) &&
        (!criteria.memoryUsage || performance.memoryUsage === criteria.memoryUsage)
      );
    });
  }
  
  static createCustomPreset(
    basePresetId: string,
    customizations: Partial<DataCenterComponentConfig>,
    metadata: {
      id: string;
      name: string;
      description: string;
      useCase: string;
    }
  ): ConfigPreset | null {
    const basePreset = this.getPreset(basePresetId);
    if (!basePreset) return null;
    
    return {
      ...basePreset,
      ...metadata,
      config: {
        ...basePreset.config,
        ...customizations
      }
    };
  }
  
  static validatePreset(preset: ConfigPreset): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    // Check required fields
    if (!preset.id) errors.push('Preset ID is required');
    if (!preset.name) errors.push('Preset name is required');
    if (!preset.config) errors.push('Preset config is required');
    
    // Check feature group dependencies
    for (const groupId of preset.featureGroups) {
      const group = FEATURE_GROUPS[groupId];
      if (!group) {
        errors.push(`Unknown feature group: ${groupId}`);
        continue;
      }
      
      // Check dependencies
      if (group.dependencies) {
        for (const depId of group.dependencies) {
          if (!preset.featureGroups.includes(depId)) {
            errors.push(`Feature group '${groupId}' requires '${depId}'`);
          }
        }
      }
      
      // Check conflicts
      if (group.conflicts) {
        for (const conflictId of group.conflicts) {
          if (preset.featureGroups.includes(conflictId)) {
            errors.push(`Feature group '${groupId}' conflicts with '${conflictId}'`);
          }
        }
      }
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }
  
  static getRecommendedPreset(requirements: {
    complexity?: 'beginner' | 'intermediate' | 'advanced';
    performance?: 'fast' | 'balanced' | 'feature-rich';
    useCase?: 'viewing' | 'analysis' | 'development' | 'enterprise';
  }): ConfigPreset {
    // Default to standard if no requirements
    if (!requirements.complexity && !requirements.performance && !requirements.useCase) {
      return CONFIG_PRESETS.standard;
    }
    
    // Map use cases to presets
    if (requirements.useCase) {
      switch (requirements.useCase) {
        case 'viewing':
          return CONFIG_PRESETS.minimal;
        case 'analysis':
          return CONFIG_PRESETS.analytics;
        case 'development':
          return CONFIG_PRESETS.developer;
        case 'enterprise':
          return CONFIG_PRESETS.enterprise;
      }
    }
    
    // Map performance requirements
    if (requirements.performance) {
      switch (requirements.performance) {
        case 'fast':
          return CONFIG_PRESETS.minimal;
        case 'balanced':
          return CONFIG_PRESETS.standard;
        case 'feature-rich':
          return CONFIG_PRESETS.analytics;
      }
    }
    
    // Map complexity requirements
    if (requirements.complexity) {
      switch (requirements.complexity) {
        case 'beginner':
          return CONFIG_PRESETS.minimal;
        case 'intermediate':
          return CONFIG_PRESETS.standard;
        case 'advanced':
          return CONFIG_PRESETS.analytics;
      }
    }
    
    return CONFIG_PRESETS.standard;
  }
}

// Feature group utilities
export class FeatureGroupManager {
  static getFeatureGroup(groupId: string): FeatureGroup | null {
    return FEATURE_GROUPS[groupId] || null;
  }
  
  static getAllFeatureGroups(): FeatureGroup[] {
    return Object.values(FEATURE_GROUPS);
  }
  
  static getFeaturesForGroups(groupIds: string[]): string[] {
    const features = new Set<string>();
    
    for (const groupId of groupIds) {
      const group = FEATURE_GROUPS[groupId];
      if (group) {
        group.features.forEach(feature => features.add(feature));
      }
    }
    
    return Array.from(features);
  }
  
  static validateFeatureGroups(groupIds: string[]): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    const processedGroups = new Set<string>();
    
    for (const groupId of groupIds) {
      const group = FEATURE_GROUPS[groupId];
      if (!group) {
        errors.push(`Unknown feature group: ${groupId}`);
        continue;
      }
      
      // Check dependencies
      if (group.dependencies) {
        for (const depId of group.dependencies) {
          if (!groupIds.includes(depId)) {
            errors.push(`Feature group '${groupId}' requires '${depId}'`);
          }
        }
      }
      
      // Check conflicts
      if (group.conflicts) {
        for (const conflictId of group.conflicts) {
          if (groupIds.includes(conflictId)) {
            errors.push(`Feature group '${groupId}' conflicts with '${conflictId}'`);
          }
        }
      }
      
      processedGroups.add(groupId);
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }
}

// Export default preset
export const DEFAULT_PRESET = CONFIG_PRESETS.standard;