/**
 * Configuration Migration Utilities
 * Helps migrate from legacy configurations to new preset-based system
 */

import { DataCenterComponentConfig } from '../types';
import { CONFIG_PRESETS, FEATURE_GROUPS } from './presets';
import { DataCenterConfigBuilder } from './builder';

// Legacy configuration interface (for migration purposes)
interface LegacyDataCenterConfig {
  // Old structure that might exist in user codebases
  features?: {
    dataPreview?: boolean;
    sqlGeneration?: boolean;
    pythonExecution?: boolean;
    dataVisualization?: boolean;
    fileUpload?: boolean;
    dataExport?: boolean;
    realTimeUpdates?: boolean;
    collaborativeEditing?: boolean;
    versionControl?: boolean;
    customWidgets?: boolean;
    advancedFiltering?: boolean;
    dataValidation?: boolean;
    performanceMonitoring?: boolean;
    auditLogging?: boolean;
    customThemes?: boolean;
  };
  ui?: {
    theme?: 'light' | 'dark' | 'auto';
    layout?: 'compact' | 'comfortable' | 'spacious';
    showSidebar?: boolean;
    showToolbar?: boolean;
    enableAnimations?: boolean;
  };
  performance?: {
    enableCaching?: boolean;
    cacheSize?: number;
    lazyLoading?: boolean;
    virtualScrolling?: boolean;
  };
  accessibility?: {
    highContrast?: boolean;
    screenReaderSupport?: boolean;
    keyboardNavigation?: boolean;
    reducedMotion?: boolean;
  };
}

interface MigrationResult {
  success: boolean;
  newConfig: DataCenterComponentConfig;
  recommendedPreset?: string;
  warnings: string[];
  changes: string[];
}

interface MigrationOptions {
  preserveCustomizations?: boolean;
  suggestPresets?: boolean;
  validateResult?: boolean;
}

export class ConfigMigrator {
  /**
   * Migrate legacy configuration to new preset-based system
   */
  static migrate(
    legacyConfig: LegacyDataCenterConfig,
    options: MigrationOptions = {}
  ): MigrationResult {
    const {
      preserveCustomizations = true,
      suggestPresets = true,
      validateResult = true
    } = options;

    const warnings: string[] = [];
    const changes: string[] = [];
    
    try {
      // Step 1: Analyze legacy config and suggest preset
      const recommendedPreset = suggestPresets ? this.suggestPreset(legacyConfig) : null;
      
      // Step 2: Start with recommended preset or basic config
      const builder = new DataCenterConfigBuilder();
      if (recommendedPreset && CONFIG_PRESETS[recommendedPreset]) {
        builder.preset(recommendedPreset);
        changes.push(`Applied '${recommendedPreset}' preset as base configuration`);
      } else {
        changes.push('Started with default configuration');
      }

      // Step 3: Apply legacy feature settings
      if (legacyConfig.features) {
        const enabledFeatures = this.extractEnabledFeatures(legacyConfig.features);
        if (enabledFeatures.length > 0) {
          builder.features(enabledFeatures);
          changes.push(`Enabled features: ${enabledFeatures.join(', ')}`);
        }
      }

      // Step 4: Apply UI settings
      if (legacyConfig.ui) {
        if (legacyConfig.ui.theme) {
          builder.theme(legacyConfig.ui.theme);
          changes.push(`Set theme to '${legacyConfig.ui.theme}'`);
        }
        
        // Handle deprecated UI properties
        if (legacyConfig.ui.layout) {
          warnings.push(`Layout setting '${legacyConfig.ui.layout}' is deprecated. Use theme variants instead.`);
        }
        if (legacyConfig.ui.showSidebar !== undefined) {
          warnings.push('showSidebar setting is now controlled by component props, not configuration.');
        }
        if (legacyConfig.ui.showToolbar !== undefined) {
          warnings.push('showToolbar setting is now controlled by component props, not configuration.');
        }
        if (legacyConfig.ui.enableAnimations !== undefined) {
          warnings.push('enableAnimations is now part of accessibility settings.');
        }
      }

      // Step 5: Apply performance settings
      if (legacyConfig.performance) {
        const perfConfig: any = {};
        
        if (legacyConfig.performance.lazyLoading !== undefined) {
          perfConfig.lazyLoadWidgets = legacyConfig.performance.lazyLoading;
          changes.push(`Set lazy loading to ${legacyConfig.performance.lazyLoading}`);
        }
        
        if (legacyConfig.performance.virtualScrolling !== undefined) {
          perfConfig.virtualizeGrids = legacyConfig.performance.virtualScrolling;
          changes.push(`Set virtual scrolling to ${legacyConfig.performance.virtualScrolling}`);
        }
        
        if (legacyConfig.performance.cacheSize !== undefined) {
          perfConfig.cacheSize = legacyConfig.performance.cacheSize;
          changes.push(`Set cache size to ${legacyConfig.performance.cacheSize}`);
        }
        
        if (legacyConfig.performance.enableCaching !== undefined) {
          warnings.push('enableCaching is deprecated. Caching is now always enabled with configurable size.');
        }
        
        builder.performance(perfConfig);
      }

      // Step 6: Apply accessibility settings
      if (legacyConfig.accessibility) {
        const a11yConfig: any = {};
        
        if (legacyConfig.accessibility.highContrast !== undefined) {
          a11yConfig.highContrast = legacyConfig.accessibility.highContrast;
        }
        if (legacyConfig.accessibility.screenReaderSupport !== undefined) {
          a11yConfig.screenReader = legacyConfig.accessibility.screenReaderSupport;
        }
        if (legacyConfig.accessibility.keyboardNavigation !== undefined) {
          a11yConfig.keyboardNavigation = legacyConfig.accessibility.keyboardNavigation;
        }
        
        if (legacyConfig.accessibility.reducedMotion !== undefined) {
          warnings.push('reducedMotion is now automatically detected from user preferences.');
        }
        
        builder.accessibility(a11yConfig);
        changes.push('Applied accessibility settings');
      }

      // Step 7: Build final configuration
      const newConfig = builder.build();
      
      // Step 8: Validate if requested
      if (validateResult) {
        const validation = builder.validate();
        if (!validation.valid) {
          warnings.push(...validation.errors.map((error: string) => `Validation error: ${error}`));
        }
      }

      return {
        success: true,
        newConfig,
        recommendedPreset: recommendedPreset || undefined,
        warnings,
        changes
      };
      
    } catch (error) {
      return {
        success: false,
        newConfig: new DataCenterConfigBuilder().build(),
        warnings: [...warnings, `Migration failed: ${error instanceof Error ? error.message : 'Unknown error'}`],
        changes
      };
    }
  }

  /**
   * Suggest the best preset based on legacy configuration
   */
  private static suggestPreset(legacyConfig: LegacyDataCenterConfig): string | null {
    const enabledFeatures = legacyConfig.features ? this.extractEnabledFeatures(legacyConfig.features) : [];
    const featureCount = enabledFeatures.length;
    
    // Analyze feature usage patterns
    const hasAdvancedFeatures = enabledFeatures.some(feature => 
      ['customWidgets', 'machineLearning', 'performanceMonitoring', 'audit'].includes(feature)
    );
    
    const hasCollaborativeFeatures = enabledFeatures.some(feature => 
      ['sharing', 'versionHistory', 'teamWorkspace'].includes(feature)
    );
    
    const hasDeveloperFeatures = enabledFeatures.some(feature => 
      ['sqlGeneration', 'pythonScripting', 'devTools'].includes(feature)
    );

    // Suggest preset based on patterns
    if (hasAdvancedFeatures && hasCollaborativeFeatures && featureCount > 10) {
      return 'enterprise';
    } else if (hasDeveloperFeatures || featureCount > 8) {
      return 'developer';
    } else if (featureCount > 5) {
      return 'analytics';
    } else if (featureCount > 2) {
      return 'standard';
    } else {
      return 'minimal';
    }
  }

  /**
   * Extract enabled features from legacy feature configuration
   */
  private static extractEnabledFeatures(features: LegacyDataCenterConfig['features']): string[] {
    if (!features) return [];
    
    const enabledFeatures: string[] = [];
    
    // Map legacy feature names to new feature names
    const featureMapping: Record<string, string> = {
      dataPreview: 'dataPreview',
      sqlGeneration: 'sqlGeneration',
      pythonExecution: 'pythonExecution',
      dataVisualization: 'dataVisualization',
      fileUpload: 'fileUpload',
      dataExport: 'dataExport',
      realTimeUpdates: 'realTimeUpdates',
      collaborativeEditing: 'collaborativeEditing',
      versionControl: 'versionControl',
      customWidgets: 'customWidgets',
      advancedFiltering: 'advancedFiltering',
      dataValidation: 'dataValidation',
      performanceMonitoring: 'performanceMonitoring',
      auditLogging: 'auditLogging',
      customThemes: 'customThemes'
    };
    
    Object.entries(features).forEach(([key, value]) => {
      if (value === true && featureMapping[key]) {
        enabledFeatures.push(featureMapping[key]);
      }
    });
    
    return enabledFeatures;
  }

  /**
   * Generate migration guide for users
   */
  static generateMigrationGuide(migrationResult: MigrationResult): string {
    const { newConfig, recommendedPreset, warnings, changes } = migrationResult;
    
    let guide = '# Data Center Configuration Migration Guide\n\n';
    
    if (recommendedPreset) {
      guide += `## Recommended Preset: \`${recommendedPreset}\`\n\n`;
      guide += `Based on your current configuration, we recommend using the '${recommendedPreset}' preset as your starting point.\n\n`;
    }
    
    guide += '## Migration Steps\n\n';
    guide += '1. **Replace your old configuration:**\n\n';
    guide += '```typescript\n';
    guide += '// Old configuration (remove this)\n';
    guide += '// const oldConfig = { ... };\n\n';
    guide += '// New configuration\n';
    if (recommendedPreset) {
      guide += `import { DataCenterConfigBuilder } from '@/components/data-center/config';\n\n`;
      guide += `const config = DataCenterConfigBuilder.fromPreset('${recommendedPreset}')\n`;
      
      // Add customizations if any
      const customFeatures = newConfig.enabledFeatures.filter(feature => 
        !CONFIG_PRESETS[recommendedPreset]?.config.enabledFeatures?.includes(feature)
      );
      
      if (customFeatures.length > 0) {
        guide += `  .enableFeatures([${customFeatures.map(f => `'${f}'`).join(', ')}])\n`;
      }
      
      guide += '  .build();\n';
    } else {
      guide += `const config = ${JSON.stringify(newConfig, null, 2)};\n`;
    }
    guide += '```\n\n';
    
    if (changes.length > 0) {
      guide += '## Changes Applied\n\n';
      changes.forEach(change => {
        guide += `- ${change}\n`;
      });
      guide += '\n';
    }
    
    if (warnings.length > 0) {
      guide += '## ⚠️ Warnings\n\n';
      warnings.forEach(warning => {
        guide += `- ${warning}\n`;
      });
      guide += '\n';
    }
    
    guide += '## Next Steps\n\n';
    guide += '1. Test your application with the new configuration\n';
    guide += '2. Review the [configuration documentation](./README.md) for advanced options\n';
    guide += '3. Consider using presets for consistent configurations across environments\n';
    guide += '4. Run the bundle optimizer to ensure optimal performance\n\n';
    
    guide += '## Need Help?\n\n';
    guide += 'If you encounter issues during migration:\n\n';
    guide += '- Check the [troubleshooting guide](./README.md#troubleshooting)\n';
    guide += '- Review the [migration examples](./examples/)\n';
    guide += '- Use the configuration validator: `DataCenterConfigBuilder.validate(config)`\n';
    
    return guide;
  }

  /**
   * Batch migrate multiple configurations
   */
  static batchMigrate(
    configs: Record<string, LegacyDataCenterConfig>,
    options: MigrationOptions = {}
  ): Record<string, MigrationResult> {
    const results: Record<string, MigrationResult> = {};
    
    Object.entries(configs).forEach(([name, config]) => {
      results[name] = this.migrate(config, options);
    });
    
    return results;
  }
}

// Export utility functions
export const migrateConfig = ConfigMigrator.migrate;
export const generateMigrationGuide = ConfigMigrator.generateMigrationGuide;
export const batchMigrateConfigs = ConfigMigrator.batchMigrate;

// Export types
export type { LegacyDataCenterConfig, MigrationResult, MigrationOptions };