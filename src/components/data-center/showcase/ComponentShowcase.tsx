import React, { useState } from 'react';
import { DataCenterUnified } from '../DataCenter';
import { DataCenterConfigBuilder } from '../config/builder';
import { CONFIG_PRESETS } from '../config/presets';
import type { DataCenterComponentConfig } from '../types';

// Mock data for demonstration
const mockDataFiles = [
  {
    id: '1',
    name: 'sales_data.csv',
    size: 1024000,
    type: 'text/csv',
    lastModified: new Date('2024-01-15'),
    path: '/data/sales_data.csv'
  },
  {
    id: '2', 
    name: 'user_analytics.json',
    size: 512000,
    type: 'application/json',
    lastModified: new Date('2024-01-14'),
    path: '/data/user_analytics.json'
  },
  {
    id: '3',
    name: 'product_catalog.xlsx',
    size: 2048000,
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    lastModified: new Date('2024-01-13'),
    path: '/data/product_catalog.xlsx'
  }
];

interface ShowcaseProps {
  className?: string;
}

export const ComponentShowcase: React.FC<ShowcaseProps> = ({ className }) => {
  const [selectedPreset, setSelectedPreset] = useState('default');
  const [selectedTheme, setSelectedTheme] = useState<'light' | 'dark' | 'auto'>('light');
  const [performanceMode, setPerformanceMode] = useState(false);

  // Build configuration based on selections
  const config = React.useMemo(() => {
    const builder = new DataCenterConfigBuilder()
      .preset(selectedPreset)
      .theme(selectedTheme);

    if (performanceMode) {
      builder.performance({
        virtualizeGrids: true,
        lazyLoadWidgets: true,
        cacheSize: 200
      });
    }

    return builder.build();
  }, [selectedPreset, selectedTheme, performanceMode]);

  return (
    <div className={`component-showcase ${className || ''}`}>
      <div className="showcase-header">
        <h1>Data Center Component Showcase</h1>
        <p>Interactive demonstration of the Data Center component library</p>
      </div>

      <div className="showcase-controls">
        <div className="control-group">
          <label htmlFor="preset-select">Configuration Preset:</label>
          <select 
            id="preset-select"
            value={selectedPreset} 
            onChange={(e) => setSelectedPreset(e.target.value)}
          >
            {CONFIG_PRESETS.map(preset => (
              <option key={preset.id} value={preset.id}>
                {preset.name}
              </option>
            ))}
          </select>
        </div>

        <div className="control-group">
          <label htmlFor="theme-select">Theme:</label>
          <select 
            id="theme-select"
            value={selectedTheme} 
            onChange={(e) => setSelectedTheme(e.target.value as 'light' | 'dark' | 'auto')}
          >
            <option value="light">Light</option>
            <option value="dark">Dark</option>
            <option value="auto">Auto</option>
          </select>
        </div>

        <div className="control-group">
          <label>
            <input 
              type="checkbox" 
              checked={performanceMode} 
              onChange={(e) => setPerformanceMode(e.target.checked)}
            />
            Performance Mode
          </label>
        </div>
      </div>

      <div className="showcase-info">
        <h3>Current Configuration</h3>
        <pre>{JSON.stringify(config, null, 2)}</pre>
      </div>

      <div className="showcase-component">
        <DataCenterUnified 
          config={config}
          initialData={mockDataFiles}
          onDataChange={(data: any) => console.log('Data changed:', data)}
          onError={(error: any) => console.error('Component error:', error)}
        />
      </div>

      <style>{`
        .component-showcase {
          padding: 2rem;
          max-width: 1200px;
          margin: 0 auto;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .showcase-header {
          text-align: center;
          margin-bottom: 2rem;
        }

        .showcase-header h1 {
          color: #333;
          margin-bottom: 0.5rem;
        }

        .showcase-header p {
          color: #666;
          font-size: 1.1rem;
        }

        .showcase-controls {
          display: flex;
          gap: 1rem;
          margin-bottom: 2rem;
          padding: 1rem;
          background: #f5f5f5;
          border-radius: 8px;
          flex-wrap: wrap;
        }

        .control-group {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;
        }

        .control-group label {
          font-weight: 500;
          color: #333;
        }

        .control-group select,
        .control-group input {
          padding: 0.5rem;
          border: 1px solid #ddd;
          border-radius: 4px;
          font-size: 0.9rem;
        }

        .showcase-info {
          margin-bottom: 2rem;
        }

        .showcase-info h3 {
          margin-bottom: 1rem;
          color: #333;
        }

        .showcase-info pre {
          background: #f8f9fa;
          padding: 1rem;
          border-radius: 4px;
          overflow-x: auto;
          font-size: 0.8rem;
          border: 1px solid #e9ecef;
        }

        .showcase-component {
          border: 1px solid #ddd;
          border-radius: 8px;
          overflow: hidden;
          min-height: 600px;
        }

        @media (max-width: 768px) {
          .showcase-controls {
            flex-direction: column;
          }
          
          .component-showcase {
            padding: 1rem;
          }
        }
      `}</style>
    </div>
  );
};

export default ComponentShowcase;