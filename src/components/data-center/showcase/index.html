<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Center Component Showcase</title>
    <style>
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
                'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
                sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            color: #333;
            margin: 0 0 10px 0;
        }
        
        .header p {
            color: #666;
            margin: 0;
        }
        
        .showcase-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .loading {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }
        
        .error {
            text-align: center;
            padding: 60px 20px;
            color: #d32f2f;
            background-color: #ffebee;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Data Center Component Showcase</h1>
            <p>Interactive demonstration of the refactored Data Center components</p>
        </div>
        
        <div class="showcase-container">
            <div id="root">
                <div class="loading">
                    Loading Data Center Showcase...
                </div>
            </div>
        </div>
    </div>
    
    <script type="module">
        import { ComponentShowcase } from './ComponentShowcase.tsx';
        import React from 'react';
        import ReactDOM from 'react-dom/client';
        
        const root = ReactDOM.createRoot(document.getElementById('root'));
        
        try {
            root.render(React.createElement(ComponentShowcase));
        } catch (error) {
            console.error('Failed to render ComponentShowcase:', error);
            document.getElementById('root').innerHTML = `
                <div class="error">
                    <h3>Failed to load showcase</h3>
                    <p>Please check the console for more details.</p>
                    <p>Make sure all dependencies are installed and the component is properly built.</p>
                </div>
            `;
        }
    </script>
</body>
</html>