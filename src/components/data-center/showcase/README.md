# Data Center Component Showcase

Interactive demonstration and development environment for the refactored Data Center components.

## Quick Start

### Using npm scripts (Recommended)

```bash
# Install dependencies
npm install

# Run the showcase
npm run showcase
```

### Manual Development

```bash
# Start the development server
npm run dev

# Navigate to the showcase
# Open: http://localhost:5173/src/components/data-center/showcase
```

## Features

### Interactive Configuration
- **Preset Selection**: Switch between different component presets
- **Theme Toggle**: Test light, dark, and auto themes
- **Performance Mode**: Toggle performance optimizations
- **Real-time Updates**: See changes instantly

### Component Testing
- **Mock Data**: Pre-loaded sample data for testing
- **Error Handling**: Test error scenarios
- **State Management**: Observe component state changes
- **Event Logging**: Monitor component events

## Available Presets

| Preset | Description | Features |
|--------|-------------|----------|
| **minimal** | Basic functionality only | Essential components, minimal UI |
| **standard** | Balanced feature set | Common widgets, standard performance |
| **advanced** | Full feature set | All widgets, advanced analytics |
| **performance** | Optimized for speed | Virtualization, lazy loading |
| **accessibility** | Enhanced a11y features | Screen reader support, keyboard nav |

## Development Workflow

### 1. Component Development
```bash
# Make changes to components
vim src/components/data-center/core/DataCenter.tsx

# Test in showcase
npm run showcase
```

### 2. Configuration Testing
```bash
# Modify configurations
vim src/components/data-center/config/presets.ts

# See changes in real-time
# Showcase automatically reloads
```

### 3. Performance Testing
```bash
# Enable performance mode in showcase
# Monitor browser dev tools for:
# - Bundle size
# - Render performance
# - Memory usage
```

## Code Quality

### Linting
```bash
# Lint all files
npm run lint

# Lint only data-center components
npm run lint:data-center

# Auto-fix issues
npm run lint:fix
```

### Type Checking
```bash
# Check types
npm run type-check

# Watch mode
npm run type-check:watch
```

## File Structure

```
showcase/
├── ComponentShowcase.tsx    # Main showcase component
├── index.ts                 # Export file
├── index.html              # Development HTML
└── README.md               # This file
```

## Customization

### Adding New Presets

1. **Define preset in config**:
```typescript
// src/components/data-center/config/presets.ts
export const customPreset: DataCenterPreset = {
  name: 'custom',
  features: { /* ... */ },
  theme: 'light',
  // ...
};
```

2. **Add to showcase**:
```typescript
// ComponentShowcase.tsx
const presets = [
  'minimal', 'standard', 'advanced', 
  'performance', 'accessibility', 'custom'
];
```

### Mock Data Customization

Modify the mock data in `ComponentShowcase.tsx`:

```typescript
const mockData = {
  files: [
    // Add your test files here
  ],
  analytics: {
    // Add your test analytics
  }
};
```

## Troubleshooting

### Common Issues

**Showcase won't load**
- Check console for errors
- Ensure all dependencies are installed
- Verify component exports

**Preset not working**
- Check preset configuration
- Verify preset is exported
- Check for TypeScript errors

**Performance issues**
- Enable performance mode
- Check bundle size
- Monitor memory usage

### Debug Mode

Enable debug logging:

```typescript
// In ComponentShowcase.tsx
const [debugMode, setDebugMode] = useState(true);
```

## Integration

### With Existing Projects

```typescript
import { ComponentShowcase } from '@/components/data-center/showcase';

// Use in your development environment
<ComponentShowcase />
```

### With Testing

```typescript
import { render } from '@testing-library/react';
import { ComponentShowcase } from './ComponentShowcase';

test('showcase renders without crashing', () => {
  render(<ComponentShowcase />);
});
```

## Contributing

1. **Test your changes** in the showcase
2. **Run linting** and type checking
3. **Update documentation** if needed
4. **Add new presets** for new features

## Resources

- [Development Guide](../docs/DEVELOPMENT.md)
- [API Documentation](../docs/API.md)
- [Migration Guide](../docs/MIGRATION.md)
- [Type Definitions](../docs/TYPES.md)