# Data Center Unified Data Flow Implementation

## Overview
This document describes the implementation of unified data flow features for the Data Center component, enabling persistent context between tabs and enhanced user experience.

## Features Implemented

### 1. Continuous Workflow with Persistent Context
- **Context Tracking**: Added `context` property to `DataPipelineState` to maintain state across tabs
- **Breadcrumb Path**: Tracks user's journey through the data processing pipeline
- **Data Summary**: Maintains metadata about processed data at each stage

### 2. Enhanced Breadcrumb Navigation
- **Dynamic Breadcrumbs**: Automatically generated based on user actions
- **Data Context**: Shows relevant information (row count, column count, file type)
- **Navigation History**: Full history of user's path through the pipeline

### 3. Inline Processing with Context Menu
- **Right-Click Actions**: Context-sensitive actions based on current view
- **Stage-Specific Options**: Different actions available in different stages
- **Quick Processing**: Direct access to templates, filters, and export options

## Implementation Details

### Data Context Structure
```typescript
interface DataContext {
  breadcrumbPath: Array<{
    stage: string;
    label: string;
    timestamp: Date;
    dataSummary?: {
      rows?: number;
      columns?: number;
      fileType?: string;
    };
  }>;
  currentData: any;
  activeFilters: Record<string, any>;
  processingMetadata: Record<string, any>;
}
```

### Key Functions
- `addToBreadcrumb()`: Add entries to navigation history
- `updateContext()`: Update persistent context data
- `navigateToStage()`: Navigate with automatic breadcrumb tracking
- `showContextMenu()`: Display context-sensitive actions

### Context Menu Actions
- **Preview Stage**: Filter, Sort, Generate Chart
- **Explore Stage**: Export Selection, Apply Template
- **Processing Stage**: Run Template, Edit Template

## Usage Examples

### Adding to Breadcrumb
```typescript
addToBreadcrumb('preview', 'Data Preview', {
  rows: dataPreview.totalRows,
  columns: dataPreview.headers.length,
  fileType: 'CSV'
});
```

### Updating Context
```typescript
updateContext({
  activeFilters: { name: 'filter1' },
  processingMetadata: { template: 'ner' }
});
```

### Context Menu Integration
Right-click on data elements to access stage-specific actions:
- Columns: Filter, Sort, Chart
- Rows: Export, Process
- Cells: Copy, Edit, Analyze

## Benefits
1. **Seamless Navigation**: Users can easily trace their steps through the pipeline
2. **Persistent State**: Data context maintained across tab switches
3. **Enhanced Productivity**: Quick access to common actions via context menu
4. **Improved UX**: Visual feedback and clear navigation path