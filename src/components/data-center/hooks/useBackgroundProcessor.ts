import { useRef, useCallback, useState, useEffect } from 'react';
import { ProcessingTask, ProcessingResult, ProgressUpdate } from '../workers/dataProcessor.worker';

interface ProcessingJob {
  id: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: number;
  message?: string;
  result?: any;
  error?: string;
  startTime: number;
  endTime?: number;
}

interface UseBackgroundProcessorReturn {
  jobs: Map<string, ProcessingJob>;
  isProcessing: boolean;
  processData: (task: Omit<ProcessingTask, 'id'>) => Promise<any>;
  cancelJob: (id: string) => void;
  clearCompletedJobs: () => void;
  getJobStatus: (id: string) => ProcessingJob | undefined;
}

export const useBackgroundProcessor = (): UseBackgroundProcessorReturn => {
  const workerRef = useRef<Worker | null>(null);
  const [jobs, setJobs] = useState<Map<string, ProcessingJob>>(new Map());
  const pendingPromises = useRef<Map<string, { resolve: (value: any) => void; reject: (error: any) => void }>>(new Map());

  // Initialize worker
  useEffect(() => {
    // Create worker from the TypeScript file
    const workerUrl = new URL('../workers/dataProcessor.worker.ts', import.meta.url);
    
    try {
      workerRef.current = new Worker(workerUrl, { type: 'module' });
      
      workerRef.current.onmessage = (event: MessageEvent<{
        type: 'progress' | 'result';
        payload: ProgressUpdate | ProcessingResult;
      }>) => {
        const { type, payload } = event.data;
        
        if (type === 'progress') {
          const progressPayload = payload as ProgressUpdate;
          setJobs(prev => {
            const newJobs = new Map(prev);
            const job = newJobs.get(progressPayload.id);
            if (job) {
              job.progress = progressPayload.progress;
              job.message = progressPayload.message;
              job.status = 'running';
            }
            return newJobs;
          });
        } else if (type === 'result') {
          const resultPayload = payload as ProcessingResult;
          const promise = pendingPromises.current.get(resultPayload.id);
          
          setJobs(prev => {
            const newJobs = new Map(prev);
            const job = newJobs.get(resultPayload.id);
            if (job) {
              job.status = resultPayload.success ? 'completed' : 'failed';
              job.progress = 100;
              job.endTime = Date.now();
              
              if (resultPayload.success) {
                job.result = resultPayload.data;
                job.message = 'Completed successfully';
              } else {
                job.error = resultPayload.error;
                job.message = `Failed: ${resultPayload.error}`;
              }
            }
            return newJobs;
          });
          
          if (promise) {
            if (resultPayload.success) {
              promise.resolve(resultPayload.data);
            } else {
              promise.reject(new Error(resultPayload.error || 'Processing failed'));
            }
            pendingPromises.current.delete(resultPayload.id);
          }
        }
      };
      
      workerRef.current.onerror = (error) => {
        console.error('Worker error:', error);
        // Reject all pending promises
        pendingPromises.current.forEach(({ reject }) => {
          reject(new Error('Worker error occurred'));
        });
        pendingPromises.current.clear();
      };
      
    } catch (error) {
      console.warn('Web Workers not supported, falling back to main thread processing');
    }
    
    return () => {
      if (workerRef.current) {
        workerRef.current.terminate();
      }
    };
  }, []);

  const processData = useCallback(async (task: Omit<ProcessingTask, 'id'>): Promise<any> => {
    const id = `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const fullTask: ProcessingTask = { ...task, id };
    
    // Create job entry
    const job: ProcessingJob = {
      id,
      status: 'pending',
      progress: 0,
      startTime: Date.now()
    };
    
    setJobs(prev => new Map(prev).set(id, job));
    
    return new Promise((resolve, reject) => {
      pendingPromises.current.set(id, { resolve, reject });
      
      if (workerRef.current) {
        // Use web worker
        workerRef.current.postMessage({ task: fullTask });
      } else {
        // Fallback to main thread processing
        setTimeout(async () => {
          try {
            let result: any;
            
            // Simple fallback implementations
            switch (task.type) {
              case 'parse':
                // Simple CSV parsing fallback
                const lines = task.data.split('\n').filter((line: string) => line.trim());
                const headers = lines[0]?.split(',').map((h: string) => h.trim().replace(/"/g, ''));
                result = lines.slice(1).map((line: string) => {
                  const values = line.split(',').map((v: string) => v.trim().replace(/"/g, ''));
                  const row: any = {};
                  headers?.forEach((header: string, i: number) => {
                    row[header] = values[i] || '';
                  });
                  return row;
                });
                break;
                
              case 'filter':
                result = task.data.filter((row: any) => {
                  return Object.entries(task.options?.filters || {}).every(([key, value]) => {
                    if (!value) return true;
                    return row[key]?.toString().toLowerCase().includes(value.toString().toLowerCase());
                  });
                });
                break;
                
              case 'sort':
                result = [...task.data].sort((a: any, b: any) => {
                  const sortConfig = task.options?.sortConfig || [];
                  for (const { key, direction } of sortConfig) {
                    const aVal = a[key];
                    const bVal = b[key];
                    let comparison = 0;
                    if (aVal < bVal) comparison = -1;
                    else if (aVal > bVal) comparison = 1;
                    if (comparison !== 0) {
                      return direction === 'asc' ? comparison : -comparison;
                    }
                  }
                  return 0;
                });
                break;
                
              default:
                result = task.data;
            }
            
            // Update job status
            setJobs(prev => {
              const newJobs = new Map(prev);
              const job = newJobs.get(id);
              if (job) {
                job.status = 'completed';
                job.progress = 100;
                job.result = result;
                job.endTime = Date.now();
                job.message = 'Completed successfully';
              }
              return newJobs;
            });
            
            resolve(result);
          } catch (error) {
            setJobs(prev => {
              const newJobs = new Map(prev);
              const job = newJobs.get(id);
              if (job) {
                job.status = 'failed';
                job.progress = 100;
                job.error = error instanceof Error ? error.message : 'Unknown error';
                job.endTime = Date.now();
                job.message = `Failed: ${job.error}`;
              }
              return newJobs;
            });
            
            reject(error);
          } finally {
            pendingPromises.current.delete(id);
          }
        }, 100);
      }
    });
  }, []);

  const cancelJob = useCallback((id: string) => {
    const promise = pendingPromises.current.get(id);
    if (promise) {
      promise.reject(new Error('Job cancelled'));
      pendingPromises.current.delete(id);
    }
    
    setJobs(prev => {
      const newJobs = new Map(prev);
      const job = newJobs.get(id);
      if (job && (job.status === 'pending' || job.status === 'running')) {
        job.status = 'failed';
        job.error = 'Cancelled by user';
        job.message = 'Cancelled';
        job.endTime = Date.now();
      }
      return newJobs;
    });
  }, []);

  const clearCompletedJobs = useCallback(() => {
    setJobs(prev => {
      const newJobs = new Map();
      prev.forEach((job, id) => {
        if (job.status === 'pending' || job.status === 'running') {
          newJobs.set(id, job);
        }
      });
      return newJobs;
    });
  }, []);

  const getJobStatus = useCallback((id: string): ProcessingJob | undefined => {
    return jobs.get(id);
  }, [jobs]);

  const isProcessing = Array.from(jobs.values()).some(job => 
    job.status === 'pending' || job.status === 'running'
  );

  return {
    jobs,
    isProcessing,
    processData,
    cancelJob,
    clearCompletedJobs,
    getJobStatus
  };
};

export default useBackgroundProcessor;