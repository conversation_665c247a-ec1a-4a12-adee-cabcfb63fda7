import { useState, useMemo, useCallback } from 'react';

export interface PaginationConfig {
  pageSize: number;
  initialPage?: number;
}

export interface PaginationResult<T> {
  currentPage: number;
  totalPages: number;
  pageSize: number;
  totalItems: number;
  currentData: T[];
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  goToPage: (page: number) => void;
  nextPage: () => void;
  previousPage: () => void;
  setPageSize: (size: number) => void;
  getPageInfo: () => {
    start: number;
    end: number;
    total: number;
  };
}

export const usePagination = <T>(
  data: T[],
  config: PaginationConfig
): PaginationResult<T> => {
  const { pageSize: initialPageSize, initialPage = 1 } = config;
  const [currentPage, setCurrentPage] = useState(initialPage);
  const [pageSize, setPageSizeState] = useState(initialPageSize);

  const totalItems = data.length;
  const totalPages = Math.ceil(totalItems / pageSize);

  // Ensure current page is valid when data or page size changes
  const validCurrentPage = useMemo(() => {
    if (totalPages === 0) return 1;
    return Math.min(currentPage, totalPages);
  }, [currentPage, totalPages]);

  // Get current page data
  const currentData = useMemo(() => {
    const startIndex = (validCurrentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return data.slice(startIndex, endIndex);
  }, [data, validCurrentPage, pageSize]);

  const hasNextPage = validCurrentPage < totalPages;
  const hasPreviousPage = validCurrentPage > 1;

  const goToPage = useCallback((page: number) => {
    const targetPage = Math.max(1, Math.min(page, totalPages));
    setCurrentPage(targetPage);
  }, [totalPages]);

  const nextPage = useCallback(() => {
    if (hasNextPage) {
      setCurrentPage(prev => prev + 1);
    }
  }, [hasNextPage]);

  const previousPage = useCallback(() => {
    if (hasPreviousPage) {
      setCurrentPage(prev => prev - 1);
    }
  }, [hasPreviousPage]);

  const setPageSize = useCallback((size: number) => {
    const newSize = Math.max(1, size);
    setPageSizeState(newSize);
    
    // Adjust current page to maintain roughly the same position
    const currentStartIndex = (validCurrentPage - 1) * pageSize;
    const newPage = Math.floor(currentStartIndex / newSize) + 1;
    setCurrentPage(Math.max(1, Math.min(newPage, Math.ceil(totalItems / newSize))));
  }, [validCurrentPage, pageSize, totalItems]);

  const getPageInfo = useCallback(() => {
    const start = totalItems === 0 ? 0 : (validCurrentPage - 1) * pageSize + 1;
    const end = Math.min(validCurrentPage * pageSize, totalItems);
    return {
      start,
      end,
      total: totalItems
    };
  }, [validCurrentPage, pageSize, totalItems]);

  return {
    currentPage: validCurrentPage,
    totalPages,
    pageSize,
    totalItems,
    currentData,
    hasNextPage,
    hasPreviousPage,
    goToPage,
    nextPage,
    previousPage,
    setPageSize,
    getPageInfo
  };
};

export default usePagination;