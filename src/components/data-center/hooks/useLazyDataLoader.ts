import { useState, useCallback, useRef, useEffect } from 'react';
import { useDataCache } from './useDataCache';

interface LazyLoadConfig {
  pageSize: number;
  preloadPages?: number; // Number of pages to preload ahead
  cacheSize?: number;
  enableInfiniteScroll?: boolean;
}

interface DataPage<T> {
  data: T[];
  pageNumber: number;
  totalItems: number;
  hasMore: boolean;
  isLoading: boolean;
  error?: string;
}

interface LazyDataLoaderResult<T> {
  pages: Map<number, DataPage<T>>;
  currentPage: number;
  totalPages: number;
  totalItems: number;
  isLoading: boolean;
  error: string | null;
  hasMore: boolean;
  loadPage: (pageNumber: number) => Promise<void>;
  loadNextPage: () => Promise<void>;
  loadPreviousPage: () => Promise<void>;
  preloadAdjacentPages: () => Promise<void>;
  getCurrentPageData: () => T[];
  getAllLoadedData: () => T[];
  clearCache: () => void;
  refreshPage: (pageNumber: number) => Promise<void>;
  setCurrentPage: (pageNumber: number) => void;
}

type DataLoader<T> = (page: number, pageSize: number) => Promise<{
  data: T[];
  totalItems: number;
  hasMore: boolean;
}>;

export const useLazyDataLoader = <T>(
  dataLoader: DataLoader<T>,
  config: LazyLoadConfig
): LazyDataLoaderResult<T> => {
  const {
    pageSize,
    preloadPages = 1,
    cacheSize = 50,
    enableInfiniteScroll = false
  } = config;

  const [pages, setPages] = useState<Map<number, DataPage<T>>>(new Map());
  const [currentPage, setCurrentPageState] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const loadingPages = useRef<Set<number>>(new Set());
  const cache = useDataCache<DataPage<T>>({
    maxSize: cacheSize,
    ttl: 10 * 60 * 1000, // 10 minutes
    enableLRU: true
  });

  const createCacheKey = useCallback((pageNumber: number) => {
    return `page_${pageNumber}_size_${pageSize}`;
  }, [pageSize]);

  const updatePageInState = useCallback((pageNumber: number, pageData: DataPage<T>) => {
    setPages(prev => {
      const newPages = new Map(prev);
      newPages.set(pageNumber, pageData);
      
      // Limit memory usage by keeping only recent pages
      if (newPages.size > cacheSize) {
        const sortedPages = Array.from(newPages.keys()).sort((a, b) => a - b);
        const pagesToRemove = sortedPages.slice(0, sortedPages.length - cacheSize);
        pagesToRemove.forEach(page => newPages.delete(page));
      }
      
      return newPages;
    });
  }, [cacheSize]);

  const loadPage = useCallback(async (pageNumber: number): Promise<void> => {
    if (loadingPages.current.has(pageNumber)) {
      return; // Already loading this page
    }

    const cacheKey = createCacheKey(pageNumber);
    const cachedPage = cache.get(cacheKey);
    
    if (cachedPage) {
      updatePageInState(pageNumber, cachedPage);
      return;
    }

    loadingPages.current.add(pageNumber);
    setIsLoading(true);
    setError(null);

    // Create loading page state
    const loadingPageData: DataPage<T> = {
      data: [],
      pageNumber,
      totalItems: 0,
      hasMore: false,
      isLoading: true
    };
    updatePageInState(pageNumber, loadingPageData);

    try {
      const result = await dataLoader(pageNumber, pageSize);
      
      const pageData: DataPage<T> = {
        data: result.data,
        pageNumber,
        totalItems: result.totalItems,
        hasMore: result.hasMore,
        isLoading: false
      };

      // Update global state
      setTotalItems(result.totalItems);
      setTotalPages(Math.ceil(result.totalItems / pageSize));
      
      // Cache and update state
      cache.set(cacheKey, pageData);
      updatePageInState(pageNumber, pageData);
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load data';
      setError(errorMessage);
      
      const errorPageData: DataPage<T> = {
        data: [],
        pageNumber,
        totalItems: 0,
        hasMore: false,
        isLoading: false,
        error: errorMessage
      };
      updatePageInState(pageNumber, errorPageData);
      
    } finally {
      loadingPages.current.delete(pageNumber);
      setIsLoading(loadingPages.current.size > 0);
    }
  }, [dataLoader, pageSize, createCacheKey, cache, updatePageInState]);

  const loadNextPage = useCallback(async (): Promise<void> => {
    const nextPage = currentPage + 1;
    if (nextPage <= totalPages || totalPages === 0) {
      await loadPage(nextPage);
      setCurrentPageState(nextPage);
    }
  }, [currentPage, totalPages, loadPage]);

  const loadPreviousPage = useCallback(async (): Promise<void> => {
    const prevPage = currentPage - 1;
    if (prevPage >= 1) {
      await loadPage(prevPage);
      setCurrentPageState(prevPage);
    }
  }, [currentPage, loadPage]);

  const preloadAdjacentPages = useCallback(async (): Promise<void> => {
    const pagesToPreload: number[] = [];
    
    // Preload pages before current page
    for (let i = 1; i <= preloadPages; i++) {
      const prevPage = currentPage - i;
      if (prevPage >= 1 && !pages.has(prevPage)) {
        pagesToPreload.push(prevPage);
      }
    }
    
    // Preload pages after current page
    for (let i = 1; i <= preloadPages; i++) {
      const nextPage = currentPage + i;
      if ((nextPage <= totalPages || totalPages === 0) && !pages.has(nextPage)) {
        pagesToPreload.push(nextPage);
      }
    }
    
    // Load pages in parallel
    await Promise.allSettled(pagesToPreload.map(page => loadPage(page)));
  }, [currentPage, preloadPages, totalPages, pages, loadPage]);

  const getCurrentPageData = useCallback((): T[] => {
    const page = pages.get(currentPage);
    return page?.data || [];
  }, [pages, currentPage]);

  const getAllLoadedData = useCallback((): T[] => {
    if (!enableInfiniteScroll) {
      return getCurrentPageData();
    }
    
    const allData: T[] = [];
    const sortedPages = Array.from(pages.keys()).sort((a, b) => a - b);
    
    sortedPages.forEach(pageNum => {
      const page = pages.get(pageNum);
      if (page && !page.isLoading && !page.error) {
        allData.push(...page.data);
      }
    });
    
    return allData;
  }, [pages, enableInfiniteScroll, getCurrentPageData]);

  const clearCache = useCallback(() => {
    cache.clear();
    setPages(new Map());
    setCurrentPageState(1);
    setTotalItems(0);
    setTotalPages(0);
    setError(null);
  }, [cache]);

  const refreshPage = useCallback(async (pageNumber: number): Promise<void> => {
    const cacheKey = createCacheKey(pageNumber);
    cache.remove(cacheKey);
    await loadPage(pageNumber);
  }, [createCacheKey, cache, loadPage]);

  const setCurrentPage = useCallback((pageNumber: number) => {
    setCurrentPageState(pageNumber);
    // Auto-load the page if not already loaded
    if (!pages.has(pageNumber)) {
      loadPage(pageNumber);
    }
  }, [pages, loadPage]);

  // Auto-preload adjacent pages when current page changes
  useEffect(() => {
    if (preloadPages > 0) {
      preloadAdjacentPages();
    }
  }, [currentPage, preloadAdjacentPages, preloadPages]);

  // Load initial page
  useEffect(() => {
    if (!pages.has(1)) {
      loadPage(1);
    }
  }, [loadPage, pages]);

  const currentPageData = pages.get(currentPage);
  const hasMore = currentPageData?.hasMore ?? true;

  return {
    pages,
    currentPage,
    totalPages,
    totalItems,
    isLoading,
    error,
    hasMore,
    loadPage,
    loadNextPage,
    loadPreviousPage,
    preloadAdjacentPages,
    getCurrentPageData,
    getAllLoadedData,
    clearCache,
    refreshPage,
    setCurrentPage
  };
};

export default useLazyDataLoader;