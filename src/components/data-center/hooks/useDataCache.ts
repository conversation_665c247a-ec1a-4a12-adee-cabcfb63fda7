import { useState, useCallback, useRef, useEffect } from 'react';

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  accessCount: number;
  lastAccessed: number;
}

interface CacheConfig {
  maxSize?: number;
  ttl?: number; // Time to live in milliseconds
  enableLRU?: boolean; // Least Recently Used eviction
}

interface CacheStats {
  size: number;
  hits: number;
  misses: number;
  hitRate: number;
}

export const useDataCache = <T>(config: CacheConfig = {}) => {
  const {
    maxSize = 100,
    ttl = 5 * 60 * 1000, // 5 minutes default
    enableLRU = true
  } = config;

  const cache = useRef<Map<string, CacheEntry<T>>>(new Map());
  const [stats, setStats] = useState<CacheStats>({
    size: 0,
    hits: 0,
    misses: 0,
    hitRate: 0
  });

  const updateStats = useCallback((hit: boolean) => {
    setStats(prev => {
      const newHits = hit ? prev.hits + 1 : prev.hits;
      const newMisses = hit ? prev.misses : prev.misses + 1;
      const total = newHits + newMisses;
      
      return {
        size: cache.current.size,
        hits: newHits,
        misses: newMisses,
        hitRate: total > 0 ? (newHits / total) * 100 : 0
      };
    });
  }, []);

  const isExpired = useCallback((entry: CacheEntry<T>): boolean => {
    return Date.now() - entry.timestamp > ttl;
  }, [ttl]);

  const evictExpired = useCallback(() => {
    const now = Date.now();
    const keysToDelete: string[] = [];
    
    cache.current.forEach((entry, key) => {
      if (now - entry.timestamp > ttl) {
        keysToDelete.push(key);
      }
    });
    
    keysToDelete.forEach(key => cache.current.delete(key));
    
    if (keysToDelete.length > 0) {
      setStats(prev => ({ ...prev, size: cache.current.size }));
    }
  }, [ttl]);

  const evictLRU = useCallback(() => {
    if (!enableLRU || cache.current.size <= maxSize) return;
    
    let oldestKey = '';
    let oldestTime = Date.now();
    
    cache.current.forEach((entry, key) => {
      if (entry.lastAccessed < oldestTime) {
        oldestTime = entry.lastAccessed;
        oldestKey = key;
      }
    });
    
    if (oldestKey) {
      cache.current.delete(oldestKey);
      setStats(prev => ({ ...prev, size: cache.current.size }));
    }
  }, [enableLRU, maxSize]);

  const get = useCallback((key: string): T | null => {
    evictExpired();
    
    const entry = cache.current.get(key);
    
    if (!entry || isExpired(entry)) {
      if (entry) {
        cache.current.delete(key);
      }
      updateStats(false);
      return null;
    }
    
    // Update access statistics
    entry.accessCount++;
    entry.lastAccessed = Date.now();
    
    updateStats(true);
    return entry.data;
  }, [evictExpired, isExpired, updateStats]);

  const set = useCallback((key: string, data: T): void => {
    evictExpired();
    
    const now = Date.now();
    const entry: CacheEntry<T> = {
      data,
      timestamp: now,
      accessCount: 1,
      lastAccessed: now
    };
    
    cache.current.set(key, entry);
    
    // Evict LRU if cache is full
    if (cache.current.size > maxSize) {
      evictLRU();
    }
    
    setStats(prev => ({ ...prev, size: cache.current.size }));
  }, [evictExpired, evictLRU, maxSize]);

  const has = useCallback((key: string): boolean => {
    evictExpired();
    const entry = cache.current.get(key);
    return entry !== undefined && !isExpired(entry);
  }, [evictExpired, isExpired]);

  const remove = useCallback((key: string): boolean => {
    const deleted = cache.current.delete(key);
    if (deleted) {
      setStats(prev => ({ ...prev, size: cache.current.size }));
    }
    return deleted;
  }, []);

  const clear = useCallback(() => {
    cache.current.clear();
    setStats({
      size: 0,
      hits: 0,
      misses: 0,
      hitRate: 0
    });
  }, []);

  const getOrSet = useCallback(async (
    key: string,
    factory: () => Promise<T> | T
  ): Promise<T> => {
    const cached = get(key);
    if (cached !== null) {
      return cached;
    }
    
    const data = await factory();
    set(key, data);
    return data;
  }, [get, set]);

  const keys = useCallback((): string[] => {
    evictExpired();
    return Array.from(cache.current.keys());
  }, [evictExpired]);

  const values = useCallback((): T[] => {
    evictExpired();
    return Array.from(cache.current.values()).map(entry => entry.data);
  }, [evictExpired]);

  const entries = useCallback((): Array<[string, T]> => {
    evictExpired();
    return Array.from(cache.current.entries()).map(([key, entry]) => [key, entry.data]);
  }, [evictExpired]);

  const getEntryInfo = useCallback((key: string) => {
    const entry = cache.current.get(key);
    if (!entry) return null;
    
    return {
      timestamp: entry.timestamp,
      accessCount: entry.accessCount,
      lastAccessed: entry.lastAccessed,
      age: Date.now() - entry.timestamp,
      isExpired: isExpired(entry)
    };
  }, [isExpired]);

  // Cleanup expired entries periodically
  useEffect(() => {
    const interval = setInterval(evictExpired, Math.min(ttl / 4, 60000)); // Check every quarter TTL or 1 minute
    return () => clearInterval(interval);
  }, [evictExpired, ttl]);

  return {
    get,
    set,
    has,
    remove,
    clear,
    getOrSet,
    keys,
    values,
    entries,
    getEntryInfo,
    stats,
    config: { maxSize, ttl, enableLRU }
  };
};

export default useDataCache;