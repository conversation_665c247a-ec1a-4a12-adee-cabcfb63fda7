import { useEffect, useCallback, useRef, useState } from 'react';

interface KeyboardShortcut {
  key: string;
  ctrlKey?: boolean;
  altKey?: boolean;
  shiftKey?: boolean;
  metaKey?: boolean;
  description: string;
  action: () => void;
  category: 'navigation' | 'data' | 'view' | 'file' | 'search';
}

interface FocusableElement {
  id: string;
  element: HTMLElement;
  priority: number;
  category: 'button' | 'input' | 'tab' | 'grid' | 'menu';
}

interface UseKeyboardNavigationOptions {
  enabled?: boolean;
  trapFocus?: boolean;
  enableShortcuts?: boolean;
  onShortcutHelp?: () => void;
}

interface UseKeyboardNavigationReturn {
  registerShortcut: (shortcut: KeyboardShortcut) => () => void;
  registerFocusable: (element: HTMLElement, options: Omit<FocusableElement, 'element'>) => () => void;
  focusNext: () => void;
  focusPrevious: () => void;
  focusFirst: () => void;
  focusLast: () => void;
  focusById: (id: string) => void;
  getCurrentFocus: () => string | null;
  shortcuts: KeyboardShortcut[];
  isHelpVisible: boolean;
  toggleHelp: () => void;
}

export const useKeyboardNavigation = ({
  enabled = true,
  trapFocus = false,
  enableShortcuts = true,
  onShortcutHelp
}: UseKeyboardNavigationOptions = {}): UseKeyboardNavigationReturn => {
  const [shortcuts, setShortcuts] = useState<KeyboardShortcut[]>([]);
  const [focusableElements, setFocusableElements] = useState<FocusableElement[]>([]);
  const [currentFocusId, setCurrentFocusId] = useState<string | null>(null);
  const [isHelpVisible, setIsHelpVisible] = useState(false);
  const containerRef = useRef<HTMLElement | null>(null);

  // Register a keyboard shortcut
  const registerShortcut = useCallback((shortcut: KeyboardShortcut) => {
    setShortcuts(prev => {
      // Remove existing shortcut with same key combination
      const filtered = prev.filter(s => 
        !(s.key === shortcut.key && 
          s.ctrlKey === shortcut.ctrlKey && 
          s.altKey === shortcut.altKey && 
          s.shiftKey === shortcut.shiftKey && 
          s.metaKey === shortcut.metaKey)
      );
      return [...filtered, shortcut];
    });

    // Return cleanup function
    return () => {
      setShortcuts(prev => prev.filter(s => s !== shortcut));
    };
  }, []);

  // Register a focusable element
  const registerFocusable = useCallback((element: HTMLElement, options: Omit<FocusableElement, 'element'>) => {
    const focusableElement: FocusableElement = {
      ...options,
      element
    };

    setFocusableElements(prev => {
      const filtered = prev.filter(f => f.id !== options.id);
      return [...filtered, focusableElement].sort((a, b) => a.priority - b.priority);
    });

    // Return cleanup function
    return () => {
      setFocusableElements(prev => prev.filter(f => f.id !== options.id));
    };
  }, []);

  // Focus navigation functions
  const focusNext = useCallback(() => {
    if (focusableElements.length === 0) return;

    const currentIndex = currentFocusId 
      ? focusableElements.findIndex(f => f.id === currentFocusId)
      : -1;
    
    const nextIndex = currentIndex < focusableElements.length - 1 ? currentIndex + 1 : 0;
    const nextElement = focusableElements[nextIndex];
    
    if (nextElement) {
      nextElement.element.focus();
      setCurrentFocusId(nextElement.id);
    }
  }, [focusableElements, currentFocusId]);

  const focusPrevious = useCallback(() => {
    if (focusableElements.length === 0) return;

    const currentIndex = currentFocusId 
      ? focusableElements.findIndex(f => f.id === currentFocusId)
      : 0;
    
    const prevIndex = currentIndex > 0 ? currentIndex - 1 : focusableElements.length - 1;
    const prevElement = focusableElements[prevIndex];
    
    if (prevElement) {
      prevElement.element.focus();
      setCurrentFocusId(prevElement.id);
    }
  }, [focusableElements, currentFocusId]);

  const focusFirst = useCallback(() => {
    if (focusableElements.length > 0) {
      const firstElement = focusableElements[0];
      firstElement.element.focus();
      setCurrentFocusId(firstElement.id);
    }
  }, [focusableElements]);

  const focusLast = useCallback(() => {
    if (focusableElements.length > 0) {
      const lastElement = focusableElements[focusableElements.length - 1];
      lastElement.element.focus();
      setCurrentFocusId(lastElement.id);
    }
  }, [focusableElements]);

  const focusById = useCallback((id: string) => {
    const element = focusableElements.find(f => f.id === id);
    if (element) {
      element.element.focus();
      setCurrentFocusId(id);
    }
  }, [focusableElements]);

  const getCurrentFocus = useCallback(() => currentFocusId, [currentFocusId]);

  const toggleHelp = useCallback(() => {
    setIsHelpVisible(prev => !prev);
    onShortcutHelp?.();
  }, [onShortcutHelp]);

  // Handle keyboard events
  useEffect(() => {
    if (!enabled) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      // Handle shortcuts
      if (enableShortcuts) {
        const matchingShortcut = shortcuts.find(shortcut => {
          const keyMatch = shortcut.key.toLowerCase() === event.key.toLowerCase();
          const ctrlMatch = !!shortcut.ctrlKey === event.ctrlKey;
          const altMatch = !!shortcut.altKey === event.altKey;
          const shiftMatch = !!shortcut.shiftKey === event.shiftKey;
          const metaMatch = !!shortcut.metaKey === event.metaKey;
          
          return keyMatch && ctrlMatch && altMatch && shiftMatch && metaMatch;
        });

        if (matchingShortcut) {
          event.preventDefault();
          matchingShortcut.action();
          return;
        }
      }

      // Handle navigation keys
      switch (event.key) {
        case 'Tab':
          if (trapFocus && focusableElements.length > 0) {
            event.preventDefault();
            if (event.shiftKey) {
              focusPrevious();
            } else {
              focusNext();
            }
          }
          break;

        case 'ArrowDown':
        case 'ArrowRight':
          if (event.target && focusableElements.some(f => f.element === event.target)) {
            event.preventDefault();
            focusNext();
          }
          break;

        case 'ArrowUp':
        case 'ArrowLeft':
          if (event.target && focusableElements.some(f => f.element === event.target)) {
            event.preventDefault();
            focusPrevious();
          }
          break;

        case 'Home':
          if (event.target && focusableElements.some(f => f.element === event.target)) {
            event.preventDefault();
            focusFirst();
          }
          break;

        case 'End':
          if (event.target && focusableElements.some(f => f.element === event.target)) {
            event.preventDefault();
            focusLast();
          }
          break;

        case 'F1':
          if (enableShortcuts) {
            event.preventDefault();
            toggleHelp();
          }
          break;

        case 'Escape':
          if (isHelpVisible) {
            event.preventDefault();
            setIsHelpVisible(false);
          }
          break;
      }
    };

    const handleFocusIn = (event: FocusEvent) => {
      const focusedElement = focusableElements.find(f => f.element === event.target);
      if (focusedElement) {
        setCurrentFocusId(focusedElement.id);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('focusin', handleFocusIn);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('focusin', handleFocusIn);
    };
  }, [enabled, enableShortcuts, shortcuts, focusableElements, trapFocus, focusNext, focusPrevious, focusFirst, focusLast, toggleHelp, isHelpVisible]);

  // Default shortcuts
  useEffect(() => {
    if (!enableShortcuts) return;

    const defaultShortcuts: KeyboardShortcut[] = [
      {
        key: 'F1',
        description: 'Show keyboard shortcuts help',
        action: toggleHelp,
        category: 'navigation'
      },
      {
        key: 'Escape',
        description: 'Close dialogs and overlays',
        action: () => setIsHelpVisible(false),
        category: 'navigation'
      },
      {
        key: 'Tab',
        description: 'Navigate to next element',
        action: focusNext,
        category: 'navigation'
      },
      {
        key: 'Tab',
        shiftKey: true,
        description: 'Navigate to previous element',
        action: focusPrevious,
        category: 'navigation'
      },
      {
        key: 'Home',
        description: 'Navigate to first element',
        action: focusFirst,
        category: 'navigation'
      },
      {
        key: 'End',
        description: 'Navigate to last element',
        action: focusLast,
        category: 'navigation'
      }
    ];

    const cleanupFunctions = defaultShortcuts.map(shortcut => registerShortcut(shortcut));

    return () => {
      cleanupFunctions.forEach(cleanup => cleanup());
    };
  }, [enableShortcuts, toggleHelp, focusNext, focusPrevious, focusFirst, focusLast, registerShortcut]);

  return {
    registerShortcut,
    registerFocusable,
    focusNext,
    focusPrevious,
    focusFirst,
    focusLast,
    focusById,
    getCurrentFocus,
    shortcuts,
    isHelpVisible,
    toggleHelp
  };
};

export default useKeyboardNavigation;