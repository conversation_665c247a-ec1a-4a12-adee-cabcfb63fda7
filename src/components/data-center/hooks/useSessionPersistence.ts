// src/components/data-center/hooks/useSessionPersistence.ts
import { useEffect } from 'react';
import { loadSession } from '../utils/stateManagement';
import { DataPipelineState } from '../DataCenterContext';

export const useSessionPersistence = (
  updatePipelineState: (updates: Partial<DataPipelineState>) => void,
  initialLoad: boolean,
  setInitialLoad: (loaded: boolean) => void
) => {
  useEffect(() => {
    if (!initialLoad) {
      const session = loadSession();
      if (session?.pipelineState) {
        // Merge session data with current state
        updatePipelineState({
          ...session.pipelineState,
          // Ensure we don't override current stage if user is in middle of workflow
          currentStage: session.pipelineState.currentStage || 'upload'
        });
      }
      setInitialLoad(true);
    }
  }, [initialLoad, setInitialLoad, updatePipelineState]);
};