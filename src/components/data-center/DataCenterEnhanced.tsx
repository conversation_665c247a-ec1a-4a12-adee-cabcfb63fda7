import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Database, LayoutGrid, Terminal, BarChart3, Shield,
  Code, Activity, Settings, Maximize2, X, Grid3x3,
  Layers, ChevronRight, Menu, PanelLeftClose, PanelLeft,
  Upload, FileSpreadsheet, Brain, Home, Eye, Wrench, FileOutput,
  AlertCircle, CheckCircle, Clock, HelpCircle, Search, RefreshCw,
  Play, Save, Download, Filter, Sun, Moon, Contrast, Accessibility
} from 'lucide-react';
import { DataCenter } from './DataCenter';
import { DataCenterControlPanel } from './DataCenterControlPanel';
import {
  DataFlowVisualization,
  MetricsHeatmap,
  RealTimeTerminal,
  DataQualityGauge,
  SqlQueryBuilder,
  InteractiveDataGrid
} from './widgets';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { DataCenterProvider } from './DataCenterContext';
import { ShortcutProvider, useShortcuts, COMMON_SHORTCUTS, ShortcutHelp } from './utils/shortcuts';
import { QuickActionsBar, COMMON_ACTIONS } from './ui/QuickActionsBar';
import { initializeAccessibility, useAccessibility } from './utils/accessibility';

interface Widget {
  id: string;
  component: React.ReactNode;
  title: string;
  icon: React.ReactNode;
  category: 'monitoring' | 'data' | 'analytics' | 'tools';
}

const DataCenterEnhancedInner: React.FC = () => {
  const [activeView, setActiveView] = useState<'dashboard' | 'upload' | 'grid' | 'sql' | 'terminal'>('dashboard');
  const [activeTab, setActiveTab] = useState<string>('overview');
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [fullscreenWidget, setFullscreenWidget] = useState<string | null>(null);
  const [showShortcuts, setShowShortcuts] = useState(false);
  const [showAccessibility, setShowAccessibility] = useState(false);
  
  // Data Pipeline Context State
  const [pipelineContext, setPipelineContext] = useState({
    fileName: '',
    fileType: '',
    fileSize: 0,
    dataPreview: null as any,
    processingResult: null as any,
    sqlResult: null as any,
    pipelineHistory: [] as Array<{
      stage: string;
      timestamp: Date;
      status: 'completed' | 'pending' | 'error';
      details?: string;
    }>,
    breadcrumbPath: [] as Array<{
      stage: string;
      label: string;
      timestamp: Date;
      dataSummary?: {
        rows?: number;
        columns?: number;
        fileType?: string;
      };
    }>,
    currentStage: 'upload',
    processingMetadata: {} as Record<string, any>
  });

  const { registerShortcut } = useShortcuts();

  // Register keyboard shortcuts
  useEffect(() => {
    // Navigation shortcuts
    registerShortcut({
      ...COMMON_SHORTCUTS.NAVIGATE_DASHBOARD,
      callback: () => setActiveView('dashboard'),
      scope: 'navigation'
    });
    
    registerShortcut({
      ...COMMON_SHORTCUTS.NAVIGATE_UPLOAD,
      callback: () => setActiveView('upload'),
      scope: 'navigation'
    });
    
    registerShortcut({
      ...COMMON_SHORTCUTS.NAVIGATE_GRID,
      callback: () => setActiveView('grid'),
      scope: 'navigation'
    });
    
    registerShortcut({
      ...COMMON_SHORTCUTS.NAVIGATE_SQL,
      callback: () => setActiveView('sql'),
      scope: 'navigation'
    });
    
    // View shortcuts
    registerShortcut({
      ...COMMON_SHORTCUTS.TOGGLE_FULLSCREEN,
      callback: () => {
        if (fullscreenWidget) {
          setFullscreenWidget(null);
        } else {
          // Toggle fullscreen for current view
        }
      },
      scope: 'view'
    });
    
    registerShortcut({
      ...COMMON_SHORTCUTS.SHOW_HELP,
      callback: () => setShowShortcuts(true),
      scope: 'help'
    });
    
    registerShortcut({
      key: 'escape',
      callback: () => {
        if (fullscreenWidget) {
          setFullscreenWidget(null);
        } else if (showShortcuts) {
          setShowShortcuts(false);
        }
      },
      description: 'Close modals',
      scope: 'general'
    });
  }, [fullscreenWidget, showShortcuts]);

  const widgets: Widget[] = [
    {
      id: 'data-flow',
      component: <DataFlowVisualization />,
      title: 'Data Flow Visualization',
      icon: <Activity className="w-4 h-4" />,
      category: 'monitoring'
    },
    {
      id: 'heatmap',
      component: <MetricsHeatmap />,
      title: 'Activity Heatmap',
      icon: <BarChart3 className="w-4 h-4" />,
      category: 'analytics'
    },
    {
      id: 'quality',
      component: <DataQualityGauge />,
      title: 'Data Quality Monitor',
      icon: <Shield className="w-4 h-4" />,
      category: 'monitoring'
    },
    {
      id: 'terminal',
      component: <RealTimeTerminal />,
      title: 'System Terminal',
      icon: <Terminal className="w-4 h-4" />,
      category: 'tools'
    },
    {
      id: 'sql',
      component: <SqlQueryBuilder />,
      title: 'SQL Query Builder',
      icon: <Code className="w-4 h-4" />,
      category: 'tools'
    },
    {
      id: 'data-grid',
      component: <InteractiveDataGrid />,
      title: 'Interactive Data Grid',
      icon: <Grid3x3 className="w-4 h-4" />,
      category: 'data'
    }
  ];

  const renderWidget = (widget: Widget, isFullscreen = false) => (
    <motion.div
      key={widget.id}
      layout
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.95 }}
      className={isFullscreen ? 'fixed inset-0 z-50 bg-background p-4' : ''}
    >
      <Card className={`h-full ${isFullscreen ? 'max-w-7xl mx-auto' : ''}`}>
        {/* Widget Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <div className="flex items-center gap-2">
            {widget.icon}
            <h3 className="font-semibold">{widget.title}</h3>
            <Badge variant="outline" className="text-xs">
              {widget.category}
            </Badge>
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={() => setFullscreenWidget(isFullscreen ? null : widget.id)}
              className="p-1.5 hover:bg-muted rounded transition-colors"
            >
              {isFullscreen ? <X className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
            </button>
          </div>
        </div>
        
        {/* Widget Content */}
        <div className={`p-4 overflow-auto ${isFullscreen ? 'h-[calc(100vh-120px)]' : 'h-[calc(100%-60px)]'}`}>
          {widget.component}
        </div>
      </Card>
    </motion.div>
  );

  return (
    <div className="data-center-enhanced flex h-screen bg-background">
      {/* Sidebar */}
      <motion.div
        initial={{ width: 240 }}
        animate={{ width: sidebarCollapsed ? 60 : 240 }}
        className="border-r bg-card flex flex-col"
      >
        {/* Sidebar Header */}
        <div className="p-4 border-b flex items-center justify-between">
          {!sidebarCollapsed && (
            <div className="flex items-center gap-2">
              <Database className="w-5 h-5 text-primary" />
              <span className="font-semibold">Data Center</span>
            </div>
          )}
          <button
            onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
            className="p-1.5 hover:bg-muted rounded transition-colors"
          >
            {sidebarCollapsed ? <PanelLeft className="w-4 h-4" /> : <PanelLeftClose className="w-4 h-4" />}
          </button>
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-4 space-y-2">
          {[
            { id: 'dashboard', label: 'Dashboard', icon: <LayoutGrid className="w-4 h-4" /> },
            { id: 'upload', label: 'Upload & Process', icon: <Upload className="w-4 h-4" /> },
            { id: 'grid', label: 'Data Grid', icon: <Grid3x3 className="w-4 h-4" /> },
            { id: 'sql', label: 'SQL Builder', icon: <Code className="w-4 h-4" /> },
            { id: 'terminal', label: 'Terminal', icon: <Terminal className="w-4 h-4" /> }
          ].map(item => (
            <button
              key={item.id}
              onClick={() => setActiveView(item.id as any)}
              className={`w-full flex items-center gap-3 px-3 py-2 rounded-lg transition-colors ${
                activeView === item.id
                  ? 'bg-primary text-primary-foreground'
                  : 'hover:bg-muted'
              }`}
            >
              {item.icon}
              {!sidebarCollapsed && <span className="text-sm">{item.label}</span>}
            </button>
          ))}
        </nav>

        {/* Sidebar Footer */}
        {!sidebarCollapsed && (
          <div className="p-4 border-t">
            <div className="space-y-2 text-xs text-muted-foreground">
              <div className="flex justify-between">
                <span>CPU Usage</span>
                <span className="text-foreground">65%</span>
              </div>
              <div className="flex justify-between">
                <span>Memory</span>
                <span className="text-foreground">4.2GB</span>
              </div>
              <div className="flex justify-between">
                <span>Storage</span>
                <span className="text-foreground">124GB</span>
              </div>
            </div>
          </div>
        )}
      </motion.div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <div className="border-b bg-card px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <h1 className="text-xl font-bold">
                {activeView === 'dashboard' && 'Control Dashboard'}
                {activeView === 'upload' && 'Upload & Process Documents'}
                {activeView === 'grid' && 'Interactive Data Grid'}
                {activeView === 'sql' && 'SQL Query Builder'}
                {activeView === 'terminal' && 'System Terminal'}
              </h1>
              <Badge variant="outline">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse mr-2" />
                Live
              </Badge>
            </div>
            
            <div className="flex items-center gap-3">
              <button 
                className="p-2 hover:bg-muted rounded transition-colors"
                onClick={() => setShowShortcuts(true)}
                aria-label="Show keyboard shortcuts"
              >
                <HelpCircle className="w-4 h-4" />
              </button>
              <button 
                className="p-2 hover:bg-muted rounded transition-colors"
                onClick={() => setShowAccessibility(true)}
                aria-label="Accessibility settings"
              >
                <Accessibility className="w-4 h-4" />
              </button>
              <button className="p-2 hover:bg-muted rounded transition-colors">
                <Settings className="w-4 h-4" />
              </button>
            </div>
          </div>
          
          {/* Breadcrumb Navigation */}
          <div className="flex items-center gap-2 mt-3 text-sm text-muted-foreground">
            <button 
              onClick={() => setActiveView('dashboard')}
              className="flex items-center gap-1 hover:text-foreground transition-colors"
            >
              <Home className="w-4 h-4" />
              <span>Dashboard</span>
            </button>
            <ChevronRight className="w-4 h-4" />
            <span className="text-foreground capitalize">{activeView}</span>
            
            {/* Pipeline Stage Indicator */}
            {activeView === 'upload' && (
              <>
                <ChevronRight className="w-4 h-4" />
                <div className="flex items-center gap-2">
                  <span className="flex items-center gap-1">
                    <Upload className="w-3 h-3" />
                    <span>Upload</span>
                  </span>
                  <ChevronRight className="w-3 h-3" />
                  <span className="flex items-center gap-1 text-muted-foreground">
                    <Eye className="w-3 h-3" />
                    <span>Preview</span>
                  </span>
                  <ChevronRight className="w-3 h-3" />
                  <span className="flex items-center gap-1 text-muted-foreground">
                    <Wrench className="w-3 h-3" />
                    <span>Process</span>
                  </span>
                  <ChevronRight className="w-3 h-3" />
                  <span className="flex items-center gap-1 text-muted-foreground">
                    <FileOutput className="w-3 h-3" />
                    <span>Output</span>
                  </span>
                </div>
              </>
            )}
          </div>
        </div>

        {/* Content Area */}
        <div className="flex-1 overflow-auto p-6">
          <AnimatePresence mode="wait">
            {activeView === 'dashboard' && (
              <motion.div
                key="dashboard"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                className="h-full flex flex-col"
              >
                <QuickActionsBar 
                  actions={[
                    {
                      ...COMMON_ACTIONS.refresh,
                      onClick: () => console.log('Refresh dashboard'),
                      shortcut: 'Ctrl+R'
                    },
                    {
                      ...COMMON_ACTIONS.search,
                      onClick: () => console.log('Search data'),
                      shortcut: 'Ctrl+K'
                    },
                    {
                      ...COMMON_ACTIONS.export,
                      onClick: () => console.log('Export data'),
                      shortcut: 'Ctrl+E'
                    },
                    {
                      ...COMMON_ACTIONS.settings,
                      onClick: () => console.log('Open settings')
                    }
                  ]}
                  title="Dashboard Actions"
                />
                
                <div className="flex-1 overflow-hidden">
                  <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full">
                    <TabsList className="mb-4">
                      <TabsTrigger value="overview">Overview</TabsTrigger>
                      <TabsTrigger value="monitoring">Monitoring</TabsTrigger>
                      <TabsTrigger value="analytics">Analytics</TabsTrigger>
                      <TabsTrigger value="control">Control Panel</TabsTrigger>
                    </TabsList>

                    <TabsContent value="overview" className="h-[calc(100%-60px)]">
                      <div className="grid grid-cols-2 gap-6 h-full">
                        <div className="h-full">
                          {renderWidget(widgets.find(w => w.id === 'data-flow')!)}
                        </div>
                        <div className="h-full">
                          {renderWidget(widgets.find(w => w.id === 'quality')!)}
                        </div>
                      </div>
                    </TabsContent>

                    <TabsContent value="monitoring" className="h-[calc(100%-60px)]">
                      <div className="grid grid-cols-2 gap-6 h-full">
                        <div className="h-full">
                          {renderWidget(widgets.find(w => w.id === 'heatmap')!)}
                        </div>
                        <div className="h-full">
                          {renderWidget(widgets.find(w => w.id === 'terminal')!)}
                        </div>
                      </div>
                    </TabsContent>

                    <TabsContent value="analytics" className="h-[calc(100%-60px)]">
                      <div className="h-full">
                        {renderWidget(widgets.find(w => w.id === 'data-grid')!)}
                      </div>
                    </TabsContent>

                    <TabsContent value="control" className="h-[calc(100%-60px)]">
                      <DataCenterControlPanel />
                    </TabsContent>
                  </Tabs>
                </div>
              </motion.div>
            )}

            {activeView === 'grid' && (
              <motion.div
                key="grid"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                className="h-full flex flex-col"
              >
                <QuickActionsBar 
                  actions={[
                    {
                      ...COMMON_ACTIONS.refresh,
                      onClick: () => console.log('Refresh grid'),
                      shortcut: 'Ctrl+R'
                    },
                    {
                      ...COMMON_ACTIONS.search,
                      onClick: () => console.log('Search grid'),
                      shortcut: 'Ctrl+K'
                    },
                    {
                      ...COMMON_ACTIONS.filter,
                      onClick: () => console.log('Filter data'),
                      shortcut: 'Ctrl+F'
                    },
                    {
                      ...COMMON_ACTIONS.export,
                      onClick: () => console.log('Export grid data'),
                      shortcut: 'Ctrl+E'
                    },
                    {
                      ...COMMON_ACTIONS.showColumns,
                      onClick: () => console.log('Manage columns')
                    }
                  ]}
                  title="Data Grid Actions"
                />
                
                <div className="flex-1 overflow-hidden">
                  <InteractiveDataGrid 
                    title="Master Data Grid"
                    pageSize={20}
                  />
                </div>
              </motion.div>
            )}

            {activeView === 'sql' && (
              <motion.div
                key="sql"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                className="h-full flex flex-col"
              >
                <QuickActionsBar 
                  actions={[
                    {
                      ...COMMON_ACTIONS.execute,
                      onClick: () => console.log('Execute query'),
                      shortcut: 'Ctrl+Enter'
                    },
                    {
                      ...COMMON_ACTIONS.save,
                      onClick: () => console.log('Save query'),
                      shortcut: 'Ctrl+S'
                    },
                    {
                      ...COMMON_ACTIONS.copy,
                      onClick: () => console.log('Copy query'),
                      shortcut: 'Ctrl+C'
                    },
                    {
                      ...COMMON_ACTIONS.refresh,
                      onClick: () => console.log('Refresh schema'),
                      shortcut: 'Ctrl+R'
                    },
                    {
                      ...COMMON_ACTIONS.export,
                      onClick: () => console.log('Export results'),
                      shortcut: 'Ctrl+E'
                    }
                  ]}
                  title="SQL Builder Actions"
                />
                
                <div className="flex-1 overflow-hidden">
                  <SqlQueryBuilder />
                </div>
              </motion.div>
            )}

            {activeView === 'terminal' && (
              <motion.div
                key="terminal"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                className="h-full flex flex-col"
              >
                <QuickActionsBar 
                  actions={[
                    {
                      ...COMMON_ACTIONS.refresh,
                      onClick: () => console.log('Clear terminal'),
                      shortcut: 'Ctrl+L'
                    },
                    {
                      ...COMMON_ACTIONS.copy,
                      onClick: () => console.log('Copy output'),
                      shortcut: 'Ctrl+C'
                    },
                    {
                      ...COMMON_ACTIONS.save,
                      onClick: () => console.log('Save logs'),
                      shortcut: 'Ctrl+S'
                    },
                    {
                      ...COMMON_ACTIONS.settings,
                      onClick: () => console.log('Terminal settings')
                    }
                  ]}
                  title="Terminal Actions"
                />
                
                <div className="flex-1 overflow-hidden">
                  <RealTimeTerminal />
                </div>
              </motion.div>
            )}

            {activeView === 'upload' && (
              <motion.div
                key="upload"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                className="h-full flex flex-col"
              >
                <QuickActionsBar 
                  actions={[
                    {
                      ...COMMON_ACTIONS.upload,
                      onClick: () => console.log('Upload file'),
                      shortcut: 'Ctrl+U'
                    },
                    {
                      ...COMMON_ACTIONS.refresh,
                      onClick: () => console.log('Refresh files'),
                      shortcut: 'Ctrl+R'
                    },
                    {
                      ...COMMON_ACTIONS.search,
                      onClick: () => console.log('Search files'),
                      shortcut: 'Ctrl+K'
                    },
                    {
                      ...COMMON_ACTIONS.settings,
                      onClick: () => console.log('Upload settings')
                    }
                  ]}
                  title="Upload Actions"
                />
                
                <div className="flex-1 overflow-hidden">
                  <DataCenterProvider onDataChange={(data) => {
                        // Update pipeline context with data from DataCenter
                        setPipelineContext(prev => ({
                          ...prev,
                          fileName: data.fileName || prev.fileName,
                          fileType: data.fileType || prev.fileType,
                          fileSize: data.fileSize || prev.fileSize,
                          dataPreview: data.dataPreview || prev.dataPreview,
                          processingResult: data.processingResult || prev.processingResult,
                          sqlResult: data.sqlResult || prev.sqlResult,
                          currentStage: data.currentStage || prev.currentStage
                        }));
                      }}>
                    <DataCenter onBack={() => setActiveView('dashboard')} />
                  </DataCenterProvider>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Fullscreen Widget */}
        <AnimatePresence>
          {fullscreenWidget && (
            <>
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="fixed inset-0 bg-black/50 z-40"
                onClick={() => setFullscreenWidget(null)}
              />
              {renderWidget(widgets.find(w => w.id === fullscreenWidget)!, true)}
            </>
          )}
        </AnimatePresence>

        {/* Shortcuts Help Panel */}
        <AnimatePresence>
          {showShortcuts && (
            <ShortcutHelp 
              onClose={() => setShowShortcuts(false)} 
              title="Data Center Keyboard Shortcuts"
            />
          )}
        </AnimatePresence>

        {/* Accessibility Settings Panel */}
        <AnimatePresence>
          {showAccessibility && (
            <AccessibilitySettingsPanel 
              onClose={() => setShowAccessibility(false)} 
            />
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

const DataCenterEnhancedWrapper: React.FC = () => {
  return (
    <ShortcutProvider>
      <AccessibilityProvider>
        <DataCenterEnhancedInner />
      </AccessibilityProvider>
    </ShortcutProvider>
  );
};

export { DataCenterEnhancedWrapper as DataCenterEnhanced };