// src/components/data-center/ui/SkeletonScreens.tsx
import React from 'react';
import { motion } from 'framer-motion';
import { Card } from './StyledComponents';

export const DataPreviewSkeleton: React.FC = () => {
  return (
    <div className="p-6 space-y-4">
      <div className="flex items-center justify-between">
        <div className="h-6 bg-gray-200 rounded w-1/4 animate-pulse"></div>
        <div className="h-8 bg-gray-200 rounded w-24 animate-pulse"></div>
      </div>
      
      <div className="overflow-hidden rounded-lg border">
        <div className="bg-gray-50 p-4 border-b">
          <div className="h-4 bg-gray-200 rounded w-1/6 animate-pulse"></div>
        </div>
        
        <div className="divide-y">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="p-4 flex space-x-4">
              {[...Array(4)].map((_, j) => (
                <div key={j} className="h-4 bg-gray-200 rounded w-full animate-pulse"></div>
              ))}
            </div>
          ))}
        </div>
      </div>
      
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="p-4">
            <div className="h-4 bg-gray-200 rounded w-3/4 animate-pulse mb-2"></div>
            <div className="h-6 bg-gray-200 rounded w-1/2 animate-pulse"></div>
          </Card>
        ))}
      </div>
    </div>
  );
};

export const ProcessingSkeleton: React.FC = () => {
  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center space-x-3">
        <div className="h-8 w-8 bg-gray-200 rounded-full animate-pulse"></div>
        <div className="h-6 bg-gray-200 rounded w-1/4 animate-pulse"></div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="p-6">
            <div className="space-y-4">
              <div className="h-6 bg-gray-200 rounded w-2/3 animate-pulse"></div>
              <div className="space-y-2">
                {[...Array(3)].map((_, j) => (
                  <div key={j} className="h-4 bg-gray-200 rounded animate-pulse"></div>
                ))}
              </div>
              <div className="h-10 bg-gray-200 rounded w-full animate-pulse"></div>
            </div>
          </Card>
        ))}
      </div>
      
      <Card className="p-6">
        <div className="h-6 bg-gray-200 rounded w-1/3 animate-pulse mb-4"></div>
        <div className="h-32 bg-gray-200 rounded animate-pulse"></div>
      </Card>
    </div>
  );
};

export const SQLSkeleton: React.FC = () => {
  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="h-8 w-8 bg-gray-200 rounded-full animate-pulse"></div>
          <div className="h-6 bg-gray-200 rounded w-1/3 animate-pulse"></div>
        </div>
        <div className="h-10 bg-gray-200 rounded w-24 animate-pulse"></div>
      </div>
      
      <Card className="p-6">
        <div className="h-4 bg-gray-200 rounded w-1/4 animate-pulse mb-4"></div>
        <div className="space-y-2">
          {[...Array(8)].map((_, i) => (
            <div key={i} className="h-4 bg-gray-200 rounded animate-pulse"></div>
          ))}
        </div>
      </Card>
      
      <div className="grid grid-cols-3 gap-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="p-4 text-center">
            <div className="h-4 bg-gray-200 rounded w-2/3 animate-pulse mx-auto mb-2"></div>
            <div className="h-8 bg-gray-200 rounded w-1/2 animate-pulse mx-auto"></div>
          </Card>
        ))}
      </div>
    </div>
  );
};

// Generic loading skeleton
export const LoadingSkeleton: React.FC<{ rows?: number }> = ({ rows = 3 }) => {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="w-full p-6"
    >
      <div className="space-y-4">
        {[...Array(rows)].map((_, i) => (
          <div key={i} className="h-16 bg-gray-200 rounded animate-pulse"></div>
        ))}
      </div>
    </motion.div>
  );
};

// Progress indicator component
export const ProgressIndicator: React.FC<{ 
  progress: number; 
  message: string;
  indeterminate?: boolean;
}> = ({ progress, message, indeterminate = false }) => {
  return (
    <div className="p-6 space-y-4">
      <div className="text-center">
        <div className="h-6 bg-gray-200 rounded w-1/2 animate-pulse mx-auto mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-3/4 animate-pulse mx-auto"></div>
      </div>
      
      <div className="w-full bg-gray-200 rounded-full h-2.5">
        <motion.div 
          className="bg-blue-600 h-2.5 rounded-full"
          initial={{ width: 0 }}
          animate={{ 
            width: indeterminate ? '100%' : `${progress}%` 
          }}
          transition={{ 
            duration: 0.5,
            repeat: indeterminate ? Infinity : 0,
            repeatType: indeterminate ? "reverse" : undefined
          }}
        ></motion.div>
      </div>
      
      <div className="text-center">
        <div className="h-4 bg-gray-200 rounded w-1/4 animate-pulse mx-auto"></div>
      </div>
    </div>
  );
};