import React, { useState, useEffect } from 'react';
import { 
  Clock, 
  Star, 
  File, 
  FileText, 
  FileSpreadsheet, 
  FileImage,
  Bookmark,
  Search,
  Filter,
  Calendar,
  TrendingUp
} from 'lucide-react';
import { StorageFile } from '@/lib/services/browserStorageManager';
// import { fileTaggingManager } from './fileTagging'; // TODO: Implement fileTagging module
import storageManager from '@/lib/services/browserStorageManager';

interface QuickAccessPanelProps {
  onFileSelect?: (file: StorageFile) => void;
  onTemplateSelect?: (templateId: string) => void;
}

interface RecentFile {
  id: string;
  name: string;
  path: string;
  size: number;
  type: string;
  category: 'document' | 'data' | 'report' | 'analysis' | 'export';
  tags: string[];
  createdAt: Date;
  modifiedAt: Date;
  metadata: Record<string, any>;
  content?: string;
  accessedAt: Date;
}

interface FavoriteTemplate {
  id: string;
  name: string;
  category: string;
  usedCount: number;
  lastUsed: Date;
}

export const QuickAccessPanel: React.FC<QuickAccessPanelProps> = ({ 
  onFileSelect,
  onTemplateSelect 
}) => {
  const [recentFiles, setRecentFiles] = useState<RecentFile[]>([]);
  const [favoriteTemplates, setFavoriteTemplates] = useState<FavoriteTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'favorites' | 'recent'>('all');

  useEffect(() => {
    loadQuickAccessData();
  }, []);

  const loadQuickAccessData = async () => {
    setLoading(true);
    try {
      // Load recent files (last 10 accessed)
      const allFiles = await storageManager.listFiles();
      const filesWithAccess = allFiles.map(file => ({
        ...file,
        accessedAt: new Date(file.modifiedAt) // Using modifiedAt as proxy for access
      }));
      
      const sortedFiles = filesWithAccess
        .sort((a, b) => b.accessedAt.getTime() - a.accessedAt.getTime())
        .slice(0, 10);
      
      setRecentFiles(sortedFiles);
      
      // Load favorite templates (using file tagging system)
      // const favoriteTag = fileTaggingManager.getTagById('favorite');
      // if (favoriteTag) {
      //   const favoriteFileIds = fileTaggingManager.getFilesWithTag('favorite');
        const favoriteFiles = allFiles.filter(file => 
          favoriteFileIds.includes(file.id)
        );
        
        const templates: FavoriteTemplate[] = favoriteFiles.map(file => ({
          id: file.id,
          name: file.name,
          category: file.category,
          usedCount: Math.floor(Math.random() * 50) + 1, // Mock data
          lastUsed: new Date(file.modifiedAt)
        }));
        
        setFavoriteTemplates(templates);
      // }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  const getFileIcon = (type: string) => {
    if (type.includes('json')) return File;
    if (type.includes('sheet') || type.includes('csv')) return FileSpreadsheet;
    if (type.includes('image')) return FileImage;
    if (type.includes('pdf') || type.includes('doc')) return FileText;
    return File;
  };

  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      document: 'text-blue-600 bg-blue-100',
      data: 'text-green-600 bg-green-100',
      report: 'text-purple-600 bg-purple-100',
      analysis: 'text-orange-600 bg-orange-100',
      export: 'text-pink-600 bg-pink-100'
    };
    return colors[category] || 'text-gray-600 bg-gray-100';
  };

  const filteredFiles = recentFiles.filter(file => {
    if (filter === 'favorites') {
      const tags: any[] = []; // fileTaggingManager.getTagsForFile(file.id);
      return tags.some(tag => tag.id === 'favorite');
    }
    return true;
  });

  if (loading) {
    return (
      <div className="p-4 text-center text-muted-foreground">
        <div className="w-6 h-6 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
        <p>Loading quick access...</p>
      </div>
    );
  }

  return (
    <div className="bg-card rounded-lg border p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="font-medium flex items-center gap-2">
          <Bookmark className="w-4 h-4 text-primary" />
          Quick Access
        </h3>
        
        <div className="flex gap-1">
          <button
            onClick={() => setFilter('all')}
            className={`px-2 py-1 text-xs rounded ${
              filter === 'all' ? 'bg-primary text-primary-foreground' : 'hover:bg-muted'
            }`}
          >
            All
          </button>
          <button
            onClick={() => setFilter('recent')}
            className={`px-2 py-1 text-xs rounded flex items-center gap-1 ${
              filter === 'recent' ? 'bg-primary text-primary-foreground' : 'hover:bg-muted'
            }`}
          >
            <Clock className="w-3 h-3" />
            Recent
          </button>
          <button
            onClick={() => setFilter('favorites')}
            className={`px-2 py-1 text-xs rounded flex items-center gap-1 ${
              filter === 'favorites' ? 'bg-primary text-primary-foreground' : 'hover:bg-muted'
            }`}
          >
            <Star className="w-3 h-3" />
            Favorites
          </button>
        </div>
      </div>

      {/* Recent Files */}
      <div className="mb-6">
        <h4 className="text-sm font-medium mb-3 flex items-center gap-2">
          <Clock className="w-4 h-4 text-muted-foreground" />
          Recently Accessed
        </h4>
        
        {filteredFiles.length === 0 ? (
          <p className="text-xs text-muted-foreground text-center py-4">
            No recent files
          </p>
        ) : (
          <div className="space-y-2">
            {filteredFiles.map(file => {
              const Icon = getFileIcon(file.type);
              const tags: any[] = []; // fileTaggingManager.getTagsForFile(file.id);
              const isFavorite = tags.some(tag => tag.id === 'favorite');
              
              return (
                <div
                  key={file.id}
                  className="flex items-center gap-3 p-2 rounded hover:bg-muted cursor-pointer transition-colors"
                  onClick={() => onFileSelect?.(file)}
                >
                  <Icon className="w-4 h-4 text-muted-foreground flex-shrink-0" />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate">{file.name}</p>
                    <div className="flex items-center gap-2 mt-1">
                      <span className={`inline-block px-1.5 py-0.5 text-xs rounded ${getCategoryColor(file.category)}`}>
                        {file.category}
                      </span>
                      <span className="text-xs text-muted-foreground">
                        {new Date(file.accessedAt).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                  {isFavorite && (
                    <Star className="w-4 h-4 text-yellow-500 fill-current" />
                  )}
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Favorite Templates */}
      <div>
        <h4 className="text-sm font-medium mb-3 flex items-center gap-2">
          <Star className="w-4 h-4 text-muted-foreground" />
          Favorite Templates
        </h4>
        
        {favoriteTemplates.length === 0 ? (
          <p className="text-xs text-muted-foreground text-center py-4">
            No favorite templates. Star templates to add them here.
          </p>
        ) : (
          <div className="space-y-2">
            {favoriteTemplates.map(template => (
              <div
                key={template.id}
                className="flex items-center gap-3 p-2 rounded hover:bg-muted cursor-pointer transition-colors"
                onClick={() => onTemplateSelect?.(template.id)}
              >
                <div className="w-4 h-4 text-muted-foreground flex-shrink-0">
                  <TrendingUp className="w-4 h-4" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium truncate">{template.name}</p>
                  <div className="flex items-center gap-2 mt-1">
                    <span className={`inline-block px-1.5 py-0.5 text-xs rounded ${getCategoryColor(template.category)}`}>
                      {template.category}
                    </span>
                    <span className="text-xs text-muted-foreground">
                      Used {template.usedCount} times
                    </span>
                  </div>
                </div>
                <Star className="w-4 h-4 text-yellow-500 fill-current" />
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Quick Stats */}
      <div className="mt-6 pt-4 border-t">
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <p className="text-2xl font-bold">{recentFiles.length}</p>
            <p className="text-xs text-muted-foreground">Recent Files</p>
          </div>
          <div>
            <p className="text-2xl font-bold">{favoriteTemplates.length}</p>
            <p className="text-xs text-muted-foreground">Favorites</p>
          </div>
          <div>
            <p className="text-2xl font-bold">
              {Math.round(
                (recentFiles.filter(f => {
                  const tags: any[] = []; // fileTaggingManager.getTagsForFile(f.id);
                  return tags.some(tag => tag.id === 'favorite');
                }).length / Math.max(recentFiles.length, 1)) * 100
              )}%
            </p>
            <p className="text-xs text-muted-foreground">Favorited</p>
          </div>
        </div>
      </div>
    </div>
  );
};