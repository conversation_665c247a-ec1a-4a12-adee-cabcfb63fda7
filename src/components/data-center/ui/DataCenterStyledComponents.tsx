import styled from 'styled-components';
import { colors, borderRadius, shadows, spacing } from '../budget/design-tokens';

// Consistent Card Styling for Data Center
export const DataCard = styled.div`
  background: ${colors.neutral[50]};
  border: 1px solid ${colors.neutral[200]};
  border-radius: ${borderRadius.lg};
  padding: ${spacing[4]};
  box-shadow: ${shadows.sm};
  transition: all 0.2s ${'cubic-bezier(0.4, 0.0, 0.2, 1)'};
  
  &:hover {
    box-shadow: ${shadows.md};
    border-color: ${colors.neutral[300]};
  }
  
  &.elevated {
    box-shadow: ${shadows.lg};
    background: white;
  }
  
  &.compact {
    padding: ${spacing[3]};
    border-radius: ${borderRadius.md};
  }
  
  &.outlined {
    background: transparent;
    border: 1px solid ${colors.neutral[300]};
    box-shadow: none;
  }
`;

// Status Card Variants
export const StatusCard = styled(DataCard)<{ status: 'success' | 'warning' | 'error' | 'info' | 'processing' }>`
  ${({ status }) => {
    switch (status) {
      case 'success':
        return `
          border-left: 4px solid ${colors.success[500]};
          background: ${colors.success[50]}20;
        `;
      case 'warning':
        return `
          border-left: 4px solid ${colors.warning[500]};
          background: ${colors.warning[50]}20;
        `;
      case 'error':
        return `
          border-left: 4px solid ${colors.error[500]};
          background: ${colors.error[50]}20;
        `;
      case 'processing':
        return `
          border-left: 4px solid ${colors.primary[500]};
          background: ${colors.primary[50]}20;
        `;
      case 'info':
        return `
          border-left: 4px solid ${colors.purple[500]};
          background: ${colors.purple[50]}20;
        `;
      default:
        return '';
    }
  }}
`;

// Data Center Section Header
export const SectionHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: ${spacing[4]};
  padding-bottom: ${spacing[2]};
  border-bottom: 1px solid ${colors.neutral[200]};
  
  h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: ${colors.neutral[900]};
    margin: 0;
  }
  
  .actions {
    display: flex;
    gap: ${spacing[2]};
  }
`;

// Data Center Button Variants
export const DataButton = styled.button<{ 
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
}>`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: ${borderRadius.sm};
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ${'cubic-bezier(0.4, 0.0, 0.2, 1)'};
  gap: ${spacing[1]};
  
  // Size variants
  ${({ size = 'md' }) => {
    switch (size) {
      case 'sm':
        return `
          padding: ${spacing[1]} ${spacing[2]};
          font-size: 0.75rem;
          line-height: 1rem;
          min-height: 28px;
        `;
      case 'lg':
        return `
          padding: ${spacing[3]} ${spacing[4]};
          font-size: 0.875rem;
          line-height: 1.25rem;
          min-height: 40px;
        `;
      default:
        return `
          padding: ${spacing[2]} ${spacing[3]};
          font-size: 0.8125rem;
          line-height: 1.25rem;
          min-height: 32px;
        `;
    }
  }}
  
  // Color variants
  ${({ variant = 'primary' }) => {
    switch (variant) {
      case 'primary':
        return `
          background: ${colors.primary[500]};
          color: white;
          &:hover {
            background: ${colors.primary[600]};
          }
          &:active {
            background: ${colors.primary[700]};
          }
          &:disabled {
            background: ${colors.neutral[300]};
            cursor: not-allowed;
          }
        `;
      case 'secondary':
        return `
          background: ${colors.neutral[100]};
          color: ${colors.neutral[900]};
          border: 1px solid ${colors.neutral[200]};
          &:hover {
            background: ${colors.neutral[200]};
            border-color: ${colors.neutral[300]};
          }
          &:active {
            background: ${colors.neutral[300]};
          }
        `;
      case 'success':
        return `
          background: ${colors.success[500]};
          color: white;
          &:hover {
            background: ${colors.success[600]};
          }
          &:active {
            background: ${colors.success[700]};
          }
        `;
      case 'warning':
        return `
          background: ${colors.warning[500]};
          color: white;
          &:hover {
            background: ${colors.warning[600]};
          }
          &:active {
            background: ${colors.warning[700]};
          }
        `;
      case 'error':
        return `
          background: ${colors.error[500]};
          color: white;
          &:hover {
            background: ${colors.error[600]};
          }
          &:active {
            background: ${colors.error[700]};
          }
        `;
      case 'ghost':
        return `
          background: transparent;
          color: ${colors.neutral[700]};
          &:hover {
            background: ${colors.neutral[100]};
          }
          &:active {
            background: ${colors.neutral[200]};
          }
        `;
      default:
        return `
          background: ${colors.primary[500]};
          color: white;
        `;
    }
  }}
  
  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px ${colors.primary[100]};
  }
`;

// Data Center Input Fields
export const DataInput = styled.input<{ 
  variant?: 'default' | 'compact';
  status?: 'normal' | 'error' | 'success';
}>`
  width: 100%;
  border: 1px solid ${colors.neutral[200]};
  border-radius: ${borderRadius.md};
  background: white;
  transition: all 0.2s ${'cubic-bezier(0.4, 0.0, 0.2, 1)'};
  
  ${({ variant = 'default' }) => {
    if (variant === 'compact') {
      return `
        padding: ${spacing[1]} ${spacing[2]};
        font-size: 0.75rem;
        line-height: 1rem;
        min-height: 28px;
      `;
    }
    return `
      padding: ${spacing[2]} ${spacing[3]};
      font-size: 0.8125rem;
      line-height: 1.25rem;
      min-height: 32px;
    `;
  }}
  
  ${({ status = 'normal' }) => {
    switch (status) {
      case 'error':
        return `
          border-color: ${colors.error[300]};
          box-shadow: 0 0 0 2px ${colors.error[100]};
          &:focus {
            border-color: ${colors.error[500]};
            box-shadow: 0 0 0 2px ${colors.error[200]};
          }
        `;
      case 'success':
        return `
          border-color: ${colors.success[300]};
          box-shadow: 0 0 0 2px ${colors.success[100]};
          &:focus {
            border-color: ${colors.success[500]};
            box-shadow: 0 0 0 2px ${colors.success[200]};
          }
        `;
      default:
        return `
          &:focus {
            border-color: ${colors.primary[500]};
            box-shadow: 0 0 0 2px ${colors.primary[100]};
          }
        `;
    }
  }}
  
  &:disabled {
    background: ${colors.neutral[100]};
    cursor: not-allowed;
  }
`;

// Data Center Badge System
export const DataBadge = styled.span<{ 
  variant?: 'primary' | 'success' | 'warning' | 'error' | 'info' | 'secondary';
  size?: 'sm' | 'md';
}>`
  display: inline-flex;
  align-items: center;
  border-radius: ${borderRadius.full};
  font-weight: 500;
  line-height: 1;
  white-space: nowrap;
  
  ${({ size = 'md' }) => {
    if (size === 'sm') {
      return `
        padding: 1px ${spacing[1]};
        font-size: 0.625rem;
      `;
    }
    return `
      padding: ${spacing[0.5]} ${spacing[1.5]};
      font-size: 0.75rem;
    `;
  }}
  
  ${({ variant = 'primary' }) => {
    switch (variant) {
      case 'primary':
        return `
          background: ${colors.primary[100]};
          color: ${colors.primary[700]};
          border: 1px solid ${colors.primary[200]};
        `;
      case 'success':
        return `
          background: ${colors.success[100]};
          color: ${colors.success[700]};
          border: 1px solid ${colors.success[200]};
        `;
      case 'warning':
        return `
          background: ${colors.warning[100]};
          color: ${colors.warning[700]};
          border: 1px solid ${colors.warning[200]};
        `;
      case 'error':
        return `
          background: ${colors.error[100]};
          color: ${colors.error[700]};
          border: 1px solid ${colors.error[200]};
        `;
      case 'info':
        return `
          background: ${colors.purple[100]};
          color: ${colors.purple[700]};
          border: 1px solid ${colors.purple[200]};
        `;
      case 'secondary':
        return `
          background: ${colors.neutral[100]};
          color: ${colors.neutral[700]};
          border: 1px solid ${colors.neutral[200]};
        `;
      default:
        return `
          background: ${colors.primary[100]};
          color: ${colors.primary[700]};
          border: 1px solid ${colors.primary[200]};
        `;
    }
  }}
`;

// Data Center Progress Bar
export const DataProgressBar = styled.div<{ 
  value: number; 
  variant?: 'primary' | 'success' | 'warning' | 'error';
  animated?: boolean;
}>`
  width: 100%;
  height: 6px;
  background: ${colors.neutral[200]};
  border-radius: ${borderRadius.full};
  overflow: hidden;
  
  .progress-fill {
    height: 100%;
    background: ${({ variant = 'primary' }) => {
      switch (variant) {
        case 'success': return colors.success[500];
        case 'warning': return colors.warning[500];
        case 'error': return colors.error[500];
        default: return colors.primary[500];
      }
    }};
    border-radius: ${borderRadius.full};
    transition: width 0.3s ${'cubic-bezier(0.4, 0.0, 0.2, 1)'};
    width: ${({ value }) => Math.min(value, 100)}%;
    
    ${({ animated }) => animated && `
      background-image: linear-gradient(
        45deg,
        rgba(255, 255, 255, 0.15) 25%,
        transparent 25%,
        transparent 50%,
        rgba(255, 255, 255, 0.15) 50%,
        rgba(255, 255, 255, 0.15) 75%,
        transparent 75%,
        transparent
      );
      background-size: 1rem 1rem;
      animation: progress-bar-stripes 1s linear infinite;
    `}
  }
  
  @keyframes progress-bar-stripes {
    0% { background-position: 1rem 0; }
    100% { background-position: 0 0; }
  }
`;