// src/components/data-center/ui/SavePointsPanel.tsx
import React from 'react';
import { Card, Button } from './StyledComponents';
import { Bookmark, Trash2, Download, Upload } from 'lucide-react';
import { DataPipelineState } from '../DataCenterContext';

interface SavePoint {
  id: string;
  name: string;
  timestamp: Date;
  state: Partial<DataPipelineState>;
  description?: string;
}

interface SavePointsPanelProps {
  savePoints: SavePoint[];
  onCreateSavePoint: (name: string, description?: string) => void;
  onLoadSavePoint: (id: string) => void;
  onDeleteSavePoint: (id: string) => void;
  onExportSavePoints?: () => void;
  onImportSavePoints?: () => void;
}

export const SavePointsPanel: React.FC<SavePointsPanelProps> = ({
  savePoints,
  onCreateSavePoint,
  onLoadSavePoint,
  onDeleteSavePoint,
  onExportSavePoints,
  onImportSavePoints
}) => {
  const [newSavePointName, setNewSavePointName] = React.useState('');
  const [newSavePointDescription, setNewSavePointDescription] = React.useState('');

  const handleCreateSavePoint = () => {
    if (newSavePointName.trim()) {
      onCreateSavePoint(newSavePointName, newSavePointDescription);
      setNewSavePointName('');
      setNewSavePointDescription('');
    }
  };

  const formatTimestamp = (timestamp: Date) => {
    return new Date(timestamp).toLocaleString();
  };

  return (
    <div className="space-y-4">
      {/* Create Save Point */}
      <Card className="p-4">
        <h3 className="font-medium mb-3 flex items-center gap-2">
          <Bookmark className="w-4 h-4" />
          Create Save Point
        </h3>
        <div className="space-y-3">
          <div>
            <label className="block text-sm font-medium mb-1">Name</label>
            <input
              type="text"
              value={newSavePointName}
              onChange={(e) => setNewSavePointName(e.target.value)}
              placeholder="Enter save point name"
              className="w-full px-3 py-2 border rounded-md text-sm"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Description (Optional)</label>
            <textarea
              value={newSavePointDescription}
              onChange={(e) => setNewSavePointDescription(e.target.value)}
              placeholder="Describe this save point"
              className="w-full px-3 py-2 border rounded-md text-sm"
              rows={2}
            />
          </div>
          <Button 
            onClick={handleCreateSavePoint}
            disabled={!newSavePointName.trim()}
            className="w-full"
          >
            <Bookmark className="w-4 h-4 mr-2" />
            Save Current State
          </Button>
        </div>
      </Card>

      {/* Save Points List */}
      {savePoints.length > 0 && (
        <Card className="p-4">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-medium flex items-center gap-2">
              <Bookmark className="w-4 h-4" />
              Saved States ({savePoints.length})
            </h3>
            <div className="flex gap-1">
              {onExportSavePoints && (
                <Button 
                  variant="ghost" 
                  size="sm"
                  onClick={onExportSavePoints}
                  title="Export Save Points"
                >
                  <Download className="w-3 h-3" />
                </Button>
              )}
              {onImportSavePoints && (
                <Button 
                  variant="ghost" 
                  size="sm"
                  onClick={onImportSavePoints}
                  title="Import Save Points"
                >
                  <Upload className="w-3 h-3" />
                </Button>
              )}
            </div>
          </div>
          
          <div className="space-y-2 max-h-60 overflow-y-auto">
            {savePoints.map((savePoint) => (
              <div 
                key={savePoint.id} 
                className="p-3 border rounded-md hover:bg-muted/50 transition-colors"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-sm truncate">{savePoint.name}</h4>
                    {savePoint.description && (
                      <p className="text-xs text-muted-foreground mt-1 truncate">
                        {savePoint.description}
                      </p>
                    )}
                    <p className="text-xs text-muted-foreground mt-1">
                      {formatTimestamp(savePoint.timestamp)}
                    </p>
                  </div>
                  <div className="flex gap-1 ml-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onLoadSavePoint(savePoint.id)}
                      title="Load Save Point"
                    >
                      <Download className="w-3 h-3" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onDeleteSavePoint(savePoint.id)}
                      title="Delete Save Point"
                    >
                      <Trash2 className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      {savePoints.length === 0 && (
        <Card className="p-8 text-center">
          <Bookmark className="w-8 h-8 mx-auto text-muted-foreground mb-2" />
          <p className="text-sm text-muted-foreground">
            No save points created yet. Save your current work to create a save point.
          </p>
        </Card>
      )}
    </div>
  );
};