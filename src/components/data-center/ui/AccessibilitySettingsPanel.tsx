import React from 'react';
import { 
  Accessibility, 
  Sun, 
  Moon, 
  Contrast, 
  Type,
  ZoomIn,
  ZoomOut,
  MousePointer,
  Keyboard
} from 'lucide-react';
import { useAccessibility } from './accessibility';

interface AccessibilitySettingsPanelProps {
  onClose: () => void;
}

export const AccessibilitySettingsPanel: React.FC<AccessibilitySettingsPanelProps> = ({ 
  onClose 
}) => {
  const { settings, updateSettings } = useAccessibility();

  const handleFontSizeChange = (size: 'small' | 'medium' | 'large' | 'x-large') => {
    updateSettings({ fontSize: size });
  };

  const handleThemeChange = (theme: 'light' | 'dark' | 'system') => {
    updateSettings({ theme });
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[80vh] overflow-hidden flex flex-col">
        <div className="flex items-center justify-between p-4 border-b">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <Accessibility className="w-5 h-5" />
            Accessibility Settings
          </h3>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full"
            aria-label="Close"
          >
            <X className="w-5 h-5" />
          </button>
        </div>
        
        <div className="flex-1 overflow-y-auto p-4 space-y-6">
          {/* Display Settings */}
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-3">Display</h4>
            <div className="space-y-4">
              {/* High Contrast */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Contrast className="w-5 h-5 text-gray-600" />
                  <div>
                    <p className="text-sm font-medium">High Contrast</p>
                    <p className="text-xs text-gray-500">Increase color contrast</p>
                  </div>
                </div>
                <button
                  onClick={() => updateSettings({ highContrast: !settings.highContrast })}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    settings.highContrast ? 'bg-primary' : 'bg-gray-200'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      settings.highContrast ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>

              {/* Colorblind Friendly */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-5 h-5 rounded-full bg-gradient-to-r from-red-500 to-green-500" />
                  <div>
                    <p className="text-sm font-medium">Colorblind Friendly</p>
                    <p className="text-xs text-gray-500">Optimize colors for visibility</p>
                  </div>
                </div>
                <button
                  onClick={() => updateSettings({ colorblindFriendly: !settings.colorblindFriendly })}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    settings.colorblindFriendly ? 'bg-primary' : 'bg-gray-200'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      settings.colorblindFriendly ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>

              {/* Reduced Motion */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-5 h-5 flex items-center justify-center">
                    <div className="w-3 h-3 border-2 border-gray-600 border-t-transparent rounded-full animate-spin" />
                  </div>
                  <div>
                    <p className="text-sm font-medium">Reduce Motion</p>
                    <p className="text-xs text-gray-500">Minimize animations</p>
                  </div>
                </div>
                <button
                  onClick={() => updateSettings({ reducedMotion: !settings.reducedMotion })}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    settings.reducedMotion ? 'bg-primary' : 'bg-gray-200'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      settings.reducedMotion ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>
            </div>
          </div>

          {/* Text Size */}
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-3">Text Size</h4>
            <div className="grid grid-cols-4 gap-2">
              {(['small', 'medium', 'large', 'x-large'] as const).map((size) => (
                <button
                  key={size}
                  onClick={() => handleFontSizeChange(size)}
                  className={`p-3 rounded-lg border text-center transition-colors ${
                    settings.fontSize === size
                      ? 'border-primary bg-primary/10 text-primary'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <Type className="w-4 h-4 mx-auto mb-1" />
                  <span className="text-xs capitalize">{size}</span>
                </button>
              ))}
            </div>
          </div>

          {/* Theme */}
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-3">Theme</h4>
            <div className="grid grid-cols-3 gap-2">
              <button
                onClick={() => handleThemeChange('light')}
                className={`p-3 rounded-lg border text-center transition-colors ${
                  settings.theme === 'light'
                    ? 'border-primary bg-primary/10 text-primary'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <Sun className="w-4 h-4 mx-auto mb-1" />
                <span className="text-xs">Light</span>
              </button>
              <button
                onClick={() => handleThemeChange('dark')}
                className={`p-3 rounded-lg border text-center transition-colors ${
                  settings.theme === 'dark'
                    ? 'border-primary bg-primary/10 text-primary'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <Moon className="w-4 h-4 mx-auto mb-1" />
                <span className="text-xs">Dark</span>
              </button>
              <button
                onClick={() => handleThemeChange('system')}
                className={`p-3 rounded-lg border text-center transition-colors ${
                  settings.theme === 'system'
                    ? 'border-primary bg-primary/10 text-primary'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div className="w-4 h-4 mx-auto mb-1 flex items-center justify-center">
                  <Sun className="w-3 h-3" />
                  <Moon className="w-3 h-3" />
                </div>
                <span className="text-xs">System</span>
              </button>
            </div>
          </div>

          {/* Keyboard Navigation */}
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-3">Navigation</h4>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Keyboard className="w-5 h-5 text-gray-600" />
                  <div>
                    <p className="text-sm font-medium">Keyboard Navigation</p>
                    <p className="text-xs text-gray-500">Enable tab navigation</p>
                  </div>
                </div>
                <button
                  onClick={() => updateSettings({ keyboardNavigation: !settings.keyboardNavigation })}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    settings.keyboardNavigation ? 'bg-primary' : 'bg-gray-200'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      settings.keyboardNavigation ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>
            </div>
          </div>
        </div>

        <div className="p-4 border-t bg-gray-50">
          <button
            onClick={() => updateSettings({
              highContrast: false,
              colorblindFriendly: false,
              keyboardNavigation: true,
              fontSize: 'medium',
              reducedMotion: false
            })}
            className="w-full px-4 py-2 text-sm text-gray-600 border border-gray-200 rounded-lg hover:bg-gray-100"
          >
            Reset to Defaults
          </button>
        </div>
      </div>
    </div>
  );
};