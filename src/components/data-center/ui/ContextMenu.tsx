import React, { useState, useEffect, useRef } from 'react';

interface ContextMenuOption {
  label: string;
  icon?: React.ReactNode;
  action: () => void;
  disabled?: boolean;
}

interface ContextMenuProps {
  options: ContextMenuOption[];
  visible: boolean;
  x: number;
  y: number;
  onClose: () => void;
}

export const ContextMenu: React.FC<ContextMenuProps> = ({ 
  options, 
  visible, 
  x, 
  y, 
  onClose 
}) => {
  const menuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (visible) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleEscape);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleEscape);
    };
  }, [visible, onClose]);

  if (!visible) return null;

  return (
    <div
      ref={menuRef}
      className="fixed z-50 min-w-[200px] bg-card border rounded-md shadow-lg py-1"
      style={{ 
        left: x, 
        top: y,
        transform: `translate(${x > window.innerWidth - 200 ? '-100%' : '0'}, ${y > window.innerHeight - 200 ? '-100%' : '0'})`
      }}
    >
      {options.map((option, index) => (
        <button
          key={index}
          onClick={(e) => {
            e.stopPropagation();
            if (!option.disabled) {
              option.action();
              onClose();
            }
          }}
          disabled={option.disabled}
          className={`w-full px-4 py-2 text-left text-sm flex items-center gap-2 transition-colors ${
            option.disabled 
              ? 'text-muted-foreground cursor-not-allowed' 
              : 'hover:bg-muted cursor-pointer'
          }`}
        >
          {option.icon && <span className="w-4 h-4">{option.icon}</span>}
          {option.label}
        </button>
      ))}
    </div>
  );
};