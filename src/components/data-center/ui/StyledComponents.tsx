import styled from 'styled-components';

// Color palette
export const colors = {
  // Primary colors
  primary: '#8884d8',
  primaryLight: '#a4a1e0',
  primaryDark: '#6a65b6',
  
  // Unified Color Coding System
  // Blue for data operations/processing
  dataOperation: '#8884d8',
  dataProcessing: '#6a65b6',
  
  // Green for success/completion
  success: '#82ca9d',
  successLight: '#a4d8b4',
  successDark: '#6cb887',
  completion: '#82ca9d',
  
  // Yellow for warnings/caution
  warning: '#ffc658',
  warningLight: '#ffdb8f',
  warningDark: '#e6b34d',
  caution: '#ffc658',
  
  // Red for errors/critical issues
  error: '#ff7c7c',
  errorLight: '#ffa4a4',
  errorDark: '#e66f6f',
  critical: '#ff7c7c',
  
  // Info/Neutral colors
  info: '#8dd1e1',
  infoLight: '#afe0ee',
  
  // Neutrals
  background: '#ffffff',
  surface: '#f8f9fa',
  border: '#e1e5e9',
  textPrimary: '#1a1a1a',
  textSecondary: '#666666',
  textDisabled: '#999999',
  
  // Shadows
  shadowLight: '0 1px 3px rgba(0,0,0,0.12)',
  shadowMedium: '0 4px 6px rgba(0,0,0,0.12)',
  shadowDark: '0 10px 20px rgba(0,0,0,0.12)'
};

// Spacing scale
export const spacing = {
  xs: '4px',
  sm: '8px',
  md: '16px',
  lg: '24px',
  xl: '32px',
  xxl: '48px'
};

// Typography
export const typography = {
  h1: {
    size: '24px',
    weight: '600',
    lineHeight: '1.3'
  },
  h2: {
    size: '20px',
    weight: '600',
    lineHeight: '1.3'
  },
  h3: {
    size: '18px',
    weight: '600',
    lineHeight: '1.4'
  },
  body: {
    size: '14px',
    weight: '400',
    lineHeight: '1.5'
  },
  caption: {
    size: '12px',
    weight: '400',
    lineHeight: '1.4'
  },
  small: {
    size: '11px',
    weight: '400',
    lineHeight: '1.4'
  }
};

// Border radius
export const borderRadius = {
  sm: '4px',
  md: '8px',
  lg: '12px',
  xl: '16px',
  full: '50%'
};

// Standardized Card Component
export const Card = styled.div<{
  variant?: 'default' | 'data' | 'success' | 'warning' | 'error' | 'info';
}>`
  background: ${colors.background};
  border: 1px solid ${colors.border};
  border-radius: ${borderRadius.md};
  padding: ${spacing.md};
  box-shadow: ${colors.shadowLight};
  transition: all 0.2s ease-in-out;
  
  // Variant-specific borders and backgrounds
  ${({ variant = 'default' }) => {
    switch (variant) {
      case 'data':
        return `
          border-left: 4px solid ${colors.dataOperation};
          &:hover {
            box-shadow: ${colors.shadowMedium};
            border-left-color: ${colors.dataProcessing};
          }
        `;
      case 'success':
        return `
          border-left: 4px solid ${colors.success};
          &:hover {
            box-shadow: ${colors.shadowMedium};
            border-left-color: ${colors.successDark};
          }
        `;
      case 'warning':
        return `
          border-left: 4px solid ${colors.warning};
          &:hover {
            box-shadow: ${colors.shadowMedium};
            border-left-color: ${colors.warningDark};
          }
        `;
      case 'error':
        return `
          border-left: 4px solid ${colors.error};
          &:hover {
            box-shadow: ${colors.shadowMedium};
            border-left-color: ${colors.errorDark};
          }
        `;
      case 'info':
        return `
          border-left: 4px solid ${colors.info};
          &:hover {
            box-shadow: ${colors.shadowMedium};
            border-left-color: #79c7d9;
          }
        `;
      default:
        return `
          &:hover {
            box-shadow: ${colors.shadowMedium};
          }
        `;
    }
  }}
  
  // Consistent hover effects
  &:hover {
    transform: translateY(-1px);
  }
`;

export const Button = styled.button<{
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'ghost' | 'data';
  size?: 'sm' | 'md' | 'lg';
}>`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: ${borderRadius.sm};
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  gap: ${spacing.xs};
  
  // Size variants
  ${({ size = 'md' }) => {
    switch (size) {
      case 'sm':
        return `
          padding: ${spacing.xs} ${spacing.sm};
          font-size: ${typography.small.size};
          line-height: ${typography.small.lineHeight};
        `;
      case 'lg':
        return `
          padding: ${spacing.md} ${spacing.lg};
          font-size: ${typography.body.size};
          line-height: ${typography.body.lineHeight};
        `;
      default:
        return `
          padding: ${spacing.sm} ${spacing.md};
          font-size: ${typography.caption.size};
          line-height: ${typography.caption.lineHeight};
        `;
    }
  }}
  
  // Color variants with unified color coding
  ${({ variant = 'primary' }) => {
    switch (variant) {
      case 'primary':
        return `
          background: ${colors.primary};
          color: white;
          &:hover {
            background: ${colors.primaryDark};
          }
          &:disabled {
            background: ${colors.textDisabled};
            cursor: not-allowed;
          }
        `;
      case 'secondary':
        return `
          background: ${colors.surface};
          color: ${colors.textPrimary};
          border: 1px solid ${colors.border};
          &:hover {
            background: ${colors.background};
            border-color: ${colors.primary};
          }
        `;
      case 'data': // Blue for data operations/processing
        return `
          background: ${colors.dataOperation};
          color: white;
          &:hover {
            background: ${colors.dataProcessing};
            transform: translateY(-1px);
          }
          &:disabled {
            background: ${colors.textDisabled};
            cursor: not-allowed;
          }
        `;
      case 'success': // Green for success/completion
        return `
          background: ${colors.success};
          color: white;
          &:hover {
            background: ${colors.successDark};
            transform: translateY(-1px);
          }
          &:disabled {
            background: ${colors.textDisabled};
            cursor: not-allowed;
          }
        `;
      case 'warning': // Yellow for warnings/caution
        return `
          background: ${colors.warning};
          color: white;
          &:hover {
            background: ${colors.warningDark};
            transform: translateY(-1px);
          }
          &:disabled {
            background: ${colors.textDisabled};
            cursor: not-allowed;
          }
        `;
      case 'error': // Red for errors/critical issues
        return `
          background: ${colors.error};
          color: white;
          &:hover {
            background: ${colors.errorDark};
            transform: translateY(-1px);
          }
          &:disabled {
            background: ${colors.textDisabled};
            cursor: not-allowed;
          }
        `;
      case 'ghost':
        return `
          background: transparent;
          color: ${colors.textPrimary};
          &:hover {
            background: ${colors.surface};
          }
        `;
      default:
        return `
          background: ${colors.primary};
          color: white;
        `;
    }
  }}
`;

export const Badge = styled.span<{
  variant?: 'primary' | 'success' | 'warning' | 'error' | 'info' | 'secondary' | 'data';
}>`
  display: inline-flex;
  align-items: center;
  padding: 2px ${spacing.xs};
  border-radius: ${borderRadius.full};
  font-size: ${typography.small.size};
  font-weight: 500;
  line-height: 1;
  
  ${({ variant = 'primary' }) => {
    switch (variant) {
      case 'primary':
        return `
          background: ${colors.primary}15;
          color: ${colors.primary};
        `;
      case 'data': // Blue for data operations/processing
        return `
          background: ${colors.dataOperation}15;
          color: ${colors.dataOperation};
        `;
      case 'success': // Green for success/completion
        return `
          background: ${colors.success}15;
          color: ${colors.success};
        `;
      case 'warning': // Yellow for warnings/caution
        return `
          background: ${colors.warning}15;
          color: ${colors.warning};
        `;
      case 'error': // Red for errors/critical issues
        return `
          background: ${colors.error}15;
          color: ${colors.error};
        `;
      case 'info':
        return `
          background: ${colors.info}15;
          color: ${colors.info};
        `;
      case 'secondary':
        return `
          background: ${colors.surface};
          color: ${colors.textSecondary};
          border: 1px solid ${colors.border};
        `;
      default:
        return `
          background: ${colors.primary}15;
          color: ${colors.primary};
        `;
    }
  }}
`;

export const Breadcrumb = styled.nav`
  display: flex;
  align-items: center;
  gap: ${spacing.sm};
  padding: ${spacing.sm} 0;
  font-size: ${typography.caption.size};
`;

export const BreadcrumbItem = styled.span<{
  active?: boolean;
  completed?: boolean;
  variant?: 'data' | 'success' | 'warning' | 'error' | 'info';
}>`
  color: ${({ active, completed, variant }) => {
    if (active) {
      switch (variant) {
        case 'data': return colors.dataOperation;
        case 'success': return colors.success;
        case 'warning': return colors.warning;
        case 'error': return colors.error;
        case 'info': return colors.info;
        default: return colors.primary;
      }
    }
    if (completed) {
      return colors.textPrimary;
    }
    return colors.textDisabled;
  }};
  font-weight: ${({ active }) => active ? '500' : '400'};
  cursor: ${({ completed }) => completed ? 'pointer' : 'default'};
  display: inline-flex;
  align-items: center;
  padding: ${spacing.xs} ${spacing.sm};
  border-radius: ${borderRadius.sm};
  transition: all 0.2s ease-in-out;
  
  &:hover {
    color: ${({ completed, variant }) => {
      if (completed) {
        switch (variant) {
          case 'data': return colors.dataOperation;
          case 'success': return colors.success;
          case 'warning': return colors.warning;
          case 'error': return colors.error;
          case 'info': return colors.info;
          default: return colors.primary;
        }
      }
      return 'inherit';
    }};
    background: ${({ completed }) => completed ? `${colors.surface}` : 'transparent'};
  }
  
  ${({ active, variant }) => active && `
    background: ${variant === 'data' ? colors.dataOperation + '15' : 
                 variant === 'success' ? colors.success + '15' : 
                 variant === 'warning' ? colors.warning + '15' : 
                 variant === 'error' ? colors.error + '15' : 
                 variant === 'info' ? colors.info + '15' : 
                 colors.primary + '15'};
  `}
`;

export const Divider = styled.div`
  height: 1px;
  background: ${colors.border};
  margin: ${spacing.md} 0;
`;

export const Typography = {
  H1: styled.h1`
    font-size: ${typography.h1.size};
    font-weight: ${typography.h1.weight};
    line-height: ${typography.h1.lineHeight};
    color: ${colors.textPrimary};
    margin: 0 0 ${spacing.md} 0;
  `,
  
  H2: styled.h2`
    font-size: ${typography.h2.size};
    font-weight: ${typography.h2.weight};
    line-height: ${typography.h2.lineHeight};
    color: ${colors.textPrimary};
    margin: 0 0 ${spacing.sm} 0;
  `,
  
  H3: styled.h3`
    font-size: ${typography.h3.size};
    font-weight: ${typography.h3.weight};
    line-height: ${typography.h3.lineHeight};
    color: ${colors.textPrimary};
    margin: 0 0 ${spacing.sm} 0;
  `,
  
  Body: styled.p`
    font-size: ${typography.body.size};
    font-weight: ${typography.body.weight};
    line-height: ${typography.body.lineHeight};
    color: ${colors.textPrimary};
    margin: 0 0 ${spacing.sm} 0;
  `,
  
  Caption: styled.span`
    font-size: ${typography.caption.size};
    font-weight: ${typography.caption.weight};
    line-height: ${typography.caption.lineHeight};
    color: ${colors.textSecondary};
  `
};