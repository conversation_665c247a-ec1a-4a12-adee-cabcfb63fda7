import React, { useState, useCallback, useRef, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Textarea } from '@/components/ui/textarea';
import { Play, Square, RotateCcw, Download, AlertCircle, CheckCircle, Clock } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ScriptExecution {
  id: string;
  script: string;
  status: 'idle' | 'running' | 'completed' | 'error' | 'cancelled';
  progress: number;
  startTime?: number;
  endTime?: number;
  output: string[];
  error?: string;
  result?: any;
}

interface PythonScriptProgressProps {
  onScriptResult?: (result: any) => void;
  onScriptError?: (error: string) => void;
  className?: string;
}

const SAMPLE_SCRIPTS = {
  'data-analysis': `import pandas as pd
import numpy as np
from datetime import datetime
import time

print("Starting data analysis...")
time.sleep(1)

# Simulate data processing
print("Loading data...")
data = np.random.randn(1000, 5)
df = pd.DataFrame(data, columns=['A', 'B', 'C', 'D', 'E'])
time.sleep(1)

print("Processing data...")
# Basic statistics
stats = df.describe()
print("Data statistics calculated")
time.sleep(1)

print("Generating insights...")
correlations = df.corr()
print("Correlation analysis complete")
time.sleep(1)

print("Analysis complete!")
result = {
    'rows': len(df),
    'columns': len(df.columns),
    'mean_values': df.mean().to_dict(),
    'correlations': correlations.to_dict()
}
print(f"Result: {result}")
`,
  'data-cleaning': `import pandas as pd
import numpy as np
import time

print("Starting data cleaning process...")
time.sleep(0.5)

# Simulate messy data
print("Creating sample messy data...")
data = {
    'name': ['John Doe', 'jane smith', 'BOB JOHNSON', None, 'Alice Brown'],
    'email': ['<EMAIL>', '<EMAIL>', 'bob@invalid', '<EMAIL>', None],
    'age': [25, 30, -5, 35, 150],
    'salary': [50000, 60000, None, 70000, 80000]
}
df = pd.DataFrame(data)
print(f"Original data shape: {df.shape}")
time.sleep(1)

print("Cleaning names...")
df['name'] = df['name'].str.title()
time.sleep(0.5)

print("Validating emails...")
df['email'] = df['email'].str.lower()
df = df[df['email'].str.contains('@', na=False)]
time.sleep(0.5)

print("Cleaning age data...")
df = df[(df['age'] > 0) & (df['age'] < 120)]
time.sleep(0.5)

print("Handling missing values...")
df = df.dropna()
time.sleep(0.5)

print("Data cleaning complete!")
result = {
    'original_rows': 5,
    'cleaned_rows': len(df),
    'cleaned_data': df.to_dict('records')
}
print(f"Cleaned {result['original_rows']} -> {result['cleaned_rows']} rows")
`,
  'visualization': `import matplotlib.pyplot as plt
import numpy as np
import time
import base64
from io import BytesIO

print("Starting data visualization...")
time.sleep(0.5)

# Generate sample data
print("Generating sample data...")
x = np.linspace(0, 10, 100)
y1 = np.sin(x)
y2 = np.cos(x)
time.sleep(1)

print("Creating plots...")
fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 8))

# Plot 1
ax1.plot(x, y1, 'b-', label='sin(x)')
ax1.plot(x, y2, 'r--', label='cos(x)')
ax1.set_title('Trigonometric Functions')
ax1.legend()
ax1.grid(True)
time.sleep(1)

# Plot 2
data = np.random.randn(1000)
ax2.hist(data, bins=30, alpha=0.7, color='green')
ax2.set_title('Random Data Distribution')
ax2.set_xlabel('Value')
ax2.set_ylabel('Frequency')
time.sleep(1)

print("Saving plots...")
plt.tight_layout()
buffer = BytesIO()
plt.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
buffer.seek(0)
image_base64 = base64.b64encode(buffer.getvalue()).decode()
plt.close()
time.sleep(0.5)

print("Visualization complete!")
result = {
    'plot_generated': True,
    'image_data': f"data:image/png;base64,{image_base64}",
    'data_points': len(x)
}
print("Plots saved successfully")
`
};

export const PythonScriptProgress: React.FC<PythonScriptProgressProps> = ({
  onScriptResult,
  onScriptError,
  className
}) => {
  const [execution, setExecution] = useState<ScriptExecution>({
    id: '',
    script: SAMPLE_SCRIPTS['data-analysis'],
    status: 'idle',
    progress: 0,
    output: []
  });
  
  const [selectedTemplate, setSelectedTemplate] = useState<keyof typeof SAMPLE_SCRIPTS>('data-analysis');
  const outputRef = useRef<HTMLDivElement>(null);
  const executionRef = useRef<{ cancelled: boolean }>({ cancelled: false });

  // Simulate Python script execution
  const simulateScriptExecution = useCallback(async (script: string): Promise<any> => {
    const lines = script.split('\n').filter(line => line.trim());
    const totalLines = lines.length;
    let currentLine = 0;
    
    executionRef.current.cancelled = false;
    
    for (const line of lines) {
      if (executionRef.current.cancelled) {
        throw new Error('Script execution cancelled');
      }
      
      currentLine++;
      const progress = (currentLine / totalLines) * 100;
      
      // Simulate execution time
      await new Promise(resolve => setTimeout(resolve, Math.random() * 500 + 100));
      
      // Update progress and output
      setExecution(prev => ({
        ...prev,
        progress,
        output: [...prev.output, `[${new Date().toLocaleTimeString()}] Executing: ${line.trim()}`]
      }));
      
      // Simulate print statements
      if (line.includes('print(')) {
        const match = line.match(/print\(["'`]([^"'`]*)["'`]\)/);
        if (match) {
          setExecution(prev => ({
            ...prev,
            output: [...prev.output, `> ${match[1]}`]
          }));
        }
      }
      
      // Simulate time.sleep
      if (line.includes('time.sleep(')) {
        const match = line.match(/time\.sleep\((\d+(?:\.\d+)?)\)/);
        if (match) {
          const sleepTime = parseFloat(match[1]) * 1000;
          await new Promise(resolve => setTimeout(resolve, sleepTime));
        }
      }
      
      // Simulate errors
      if (line.includes('raise Exception') || line.includes('1/0')) {
        throw new Error('Simulated script error');
      }
    }
    
    // Return mock result based on script type
    if (script.includes('data analysis')) {
      return {
        type: 'analysis',
        rows: 1000,
        columns: 5,
        insights: ['Strong correlation between A and B', 'Outliers detected in column C']
      };
    } else if (script.includes('data cleaning')) {
      return {
        type: 'cleaning',
        original_rows: 5,
        cleaned_rows: 4,
        issues_fixed: ['Invalid emails', 'Age outliers', 'Missing values']
      };
    } else {
      return {
        type: 'general',
        status: 'completed',
        execution_time: Date.now() - (execution.startTime || Date.now())
      };
    }
  }, [execution.startTime]);

  const runScript = useCallback(async () => {
    const executionId = `exec_${Date.now()}`;
    
    setExecution(prev => ({
      ...prev,
      id: executionId,
      status: 'running',
      progress: 0,
      startTime: Date.now(),
      endTime: undefined,
      output: [`[${new Date().toLocaleTimeString()}] Starting script execution...`],
      error: undefined,
      result: undefined
    }));
    
    try {
      const result = await simulateScriptExecution(execution.script);
      
      setExecution(prev => ({
        ...prev,
        status: 'completed',
        progress: 100,
        endTime: Date.now(),
        result,
        output: [...prev.output, `[${new Date().toLocaleTimeString()}] Script completed successfully`]
      }));
      
      onScriptResult?.(result);
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      setExecution(prev => ({
        ...prev,
        status: 'error',
        endTime: Date.now(),
        error: errorMessage,
        output: [...prev.output, `[${new Date().toLocaleTimeString()}] Error: ${errorMessage}`]
      }));
      
      onScriptError?.(errorMessage);
    }
  }, [execution.script, simulateScriptExecution, onScriptResult, onScriptError]);

  const cancelScript = useCallback(() => {
    executionRef.current.cancelled = true;
    setExecution(prev => ({
      ...prev,
      status: 'cancelled',
      endTime: Date.now(),
      output: [...prev.output, `[${new Date().toLocaleTimeString()}] Script execution cancelled`]
    }));
  }, []);

  const resetScript = useCallback(() => {
    setExecution(prev => ({
      ...prev,
      status: 'idle',
      progress: 0,
      startTime: undefined,
      endTime: undefined,
      output: [],
      error: undefined,
      result: undefined
    }));
  }, []);

  const loadTemplate = useCallback((template: keyof typeof SAMPLE_SCRIPTS) => {
    setSelectedTemplate(template);
    setExecution(prev => ({
      ...prev,
      script: SAMPLE_SCRIPTS[template],
      status: 'idle',
      progress: 0,
      output: [],
      error: undefined,
      result: undefined
    }));
  }, []);

  const downloadOutput = useCallback(() => {
    const content = execution.output.join('\n');
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `script_output_${execution.id}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [execution.output, execution.id]);

  // Auto-scroll output to bottom
  useEffect(() => {
    if (outputRef.current) {
      outputRef.current.scrollTop = outputRef.current.scrollHeight;
    }
  }, [execution.output]);

  const getStatusIcon = () => {
    switch (execution.status) {
      case 'running':
        return <Clock className="h-4 w-4 animate-spin" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
      case 'cancelled':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return null;
    }
  };

  const getExecutionTime = () => {
    if (!execution.startTime) return null;
    const endTime = execution.endTime || Date.now();
    return ((endTime - execution.startTime) / 1000).toFixed(2);
  };

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            Python Script Executor
            {getStatusIcon()}
            <Badge variant={execution.status === 'completed' ? 'default' : 
                          execution.status === 'running' ? 'secondary' : 
                          execution.status === 'error' ? 'destructive' : 'outline'}>
              {execution.status}
            </Badge>
          </CardTitle>
          
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => loadTemplate('data-analysis')}
              disabled={execution.status === 'running'}
            >
              Analysis
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => loadTemplate('data-cleaning')}
              disabled={execution.status === 'running'}
            >
              Cleaning
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => loadTemplate('visualization')}
              disabled={execution.status === 'running'}
            >
              Visualization
            </Button>
          </div>
        </div>
        
        {execution.status === 'running' && (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>Progress: {execution.progress.toFixed(1)}%</span>
              {execution.startTime && (
                <span>Runtime: {getExecutionTime()}s</span>
              )}
            </div>
            <Progress value={execution.progress} className="h-2" />
          </div>
        )}
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Script Editor */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Python Script:</label>
          <Textarea
            value={execution.script}
            onChange={(e) => setExecution(prev => ({ ...prev, script: e.target.value }))}
            placeholder="Enter your Python script here..."
            className="font-mono text-sm min-h-[200px]"
            disabled={execution.status === 'running'}
          />
        </div>
        
        {/* Controls */}
        <div className="flex items-center gap-2">
          <Button
            onClick={runScript}
            disabled={execution.status === 'running' || !execution.script.trim()}
            className="flex items-center gap-2"
          >
            <Play className="h-4 w-4" />
            Run Script
          </Button>
          
          {execution.status === 'running' && (
            <Button
              variant="destructive"
              onClick={cancelScript}
              className="flex items-center gap-2"
            >
              <Square className="h-4 w-4" />
              Cancel
            </Button>
          )}
          
          <Button
            variant="outline"
            onClick={resetScript}
            disabled={execution.status === 'running'}
            className="flex items-center gap-2"
          >
            <RotateCcw className="h-4 w-4" />
            Reset
          </Button>
          
          {execution.output.length > 0 && (
            <Button
              variant="outline"
              onClick={downloadOutput}
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              Download Output
            </Button>
          )}
        </div>
        
        {/* Output Console */}
        {execution.output.length > 0 && (
          <div className="space-y-2">
            <label className="text-sm font-medium">Output:</label>
            <ScrollArea className="h-64 w-full border rounded-md">
              <div ref={outputRef} className="p-4 font-mono text-sm space-y-1">
                {execution.output.map((line, index) => (
                  <div
                    key={index}
                    className={cn(
                      "whitespace-pre-wrap",
                      line.startsWith('[') && line.includes('] Error:') && "text-red-500",
                      line.startsWith('>') && "text-blue-600 dark:text-blue-400",
                      line.includes('completed successfully') && "text-green-600 dark:text-green-400"
                    )}
                  >
                    {line}
                  </div>
                ))}
              </div>
            </ScrollArea>
          </div>
        )}
        
        {/* Execution Summary */}
        {(execution.status === 'completed' || execution.status === 'error') && (
          <div className="p-4 border rounded-lg bg-gray-50 dark:bg-gray-800">
            <h4 className="font-medium mb-2">Execution Summary</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600 dark:text-gray-400">Status:</span>
                <span className={cn(
                  "ml-2 font-medium",
                  execution.status === 'completed' ? "text-green-600" : "text-red-600"
                )}>
                  {execution.status}
                </span>
              </div>
              <div>
                <span className="text-gray-600 dark:text-gray-400">Runtime:</span>
                <span className="ml-2 font-medium">{getExecutionTime()}s</span>
              </div>
              <div>
                <span className="text-gray-600 dark:text-gray-400">Output Lines:</span>
                <span className="ml-2 font-medium">{execution.output.length}</span>
              </div>
              {execution.result && (
                <div>
                  <span className="text-gray-600 dark:text-gray-400">Result Type:</span>
                  <span className="ml-2 font-medium">{execution.result.type}</span>
                </div>
              )}
            </div>
            
            {execution.error && (
              <div className="mt-3 p-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded">
                <span className="text-red-600 dark:text-red-400 text-sm font-medium">Error: {execution.error}</span>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default PythonScriptProgress;