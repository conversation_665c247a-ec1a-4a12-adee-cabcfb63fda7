import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import '@testing-library/jest-dom';
import { DataCenterUnified } from '../index';
import { DataCenterConfigBuilder } from '../config/builder';
import type { DataCenterConfig } from '../DataCenterConfig';
import type { DataCenterComponentConfig } from '../types';

// Mock data for testing
const mockFiles = [
  {
    id: '1',
    name: 'test-file.txt',
    size: 1024,
    type: 'text/plain',
    lastModified: new Date('2024-01-01'),
    path: '/test/test-file.txt'
  },
  {
    id: '2',
    name: 'image.jpg',
    size: 2048,
    type: 'image/jpeg',
    lastModified: new Date('2024-01-02'),
    path: '/test/image.jpg'
  }
];

const mockAnalytics = {
  totalFiles: 2,
  totalSize: 3072,
  fileTypes: {
    'text/plain': 1,
    'image/jpeg': 1
  },
  lastUpdated: new Date('2024-01-02')
};

// Mock configuration
const mockConfig: Partial<DataCenterConfig> = {
  features: {
    fileUpload: true,
    dataPreview: true,
    pythonProcessing: true,
    sqlGeneration: true,
    dashboard: true,
    widgets: true,
    realTimeMonitoring: false,
    performanceMonitoring: false,
    jobTracking: false,
    userFeedback: false,
    streamlinedNavigation: false,
    globalSearch: false,
    quickActions: false,
    keyboardShortcuts: false,
    onboardingFlow: false,
    interactiveTutorial: false,
    accessibilityFeatures: true,
    storytelling: false,
    narrativeGeneration: false,
    versionHistory: false,
    changeTracking: false,
    metadataSidebar: false,
    processingHistory: false,
    smartOrganization: false,
    aiSuggestions: false
  },
  theme: 'light',
  accessibility: {
    enabled: true,
    highContrast: false,
    screenReader: true,
    keyboardNavigation: true
  },
  performance: {
    enableLazyLoading: true,
    enableVirtualization: false,
    maxFileSize: 100,
    chunkSize: 1000
  }
};

// Mock handlers
const mockHandlers = {
  onDataChange: vi.fn(),
  onBack: vi.fn()
};

describe('DataCenterUnified', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Basic Rendering', () => {
    it('renders without crashing', () => {
      render(
        <DataCenterUnified
          config={mockConfig}
          {...mockHandlers}
        />
      );
      
      expect(document.body).toBeInTheDocument();
    });

    it('renders with minimal config', () => {
      render(
        <DataCenterUnified
          config={{}}
          {...mockHandlers}
        />
      );
      
      expect(document.body).toBeInTheDocument();
    });

    it('calls onBack when provided', () => {
      render(
        <DataCenterUnified
          config={mockConfig}
          {...mockHandlers}
        />
      );
      
      expect(mockHandlers.onBack).toBeDefined();
    });
  });

  describe('Configuration', () => {
    it('applies minimal preset correctly', () => {
      const minimalConfig: Partial<DataCenterConfig> = {
        features: {
          fileUpload: true,
          dataPreview: false,
          pythonProcessing: false,
          sqlGeneration: false,
          dashboard: false,
          widgets: false,
          realTimeMonitoring: false,
          performanceMonitoring: false,
          jobTracking: false,
          userFeedback: false,
          streamlinedNavigation: false,
          globalSearch: false,
          quickActions: false,
          keyboardShortcuts: false,
          onboardingFlow: false,
          interactiveTutorial: false,
          accessibilityFeatures: false,
          storytelling: false,
          narrativeGeneration: false,
          versionHistory: false,
          changeTracking: false,
          metadataSidebar: false,
          processingHistory: false,
          smartOrganization: false,
          aiSuggestions: false
        },
        theme: 'light',
        accessibility: {
          enabled: false,
          highContrast: false,
          screenReader: false,
          keyboardNavigation: true
        },
        performance: {
          enableLazyLoading: false,
          enableVirtualization: false,
          maxFileSize: 50,
          chunkSize: 500
        }
      };
      
      render(
        <DataCenterUnified
          config={minimalConfig}
          {...mockHandlers}
        />
      );
      
      expect(document.body).toBeInTheDocument();
    });

    it('applies advanced preset correctly', () => {
      const advancedConfig: Partial<DataCenterConfig> = {
        features: {
          fileUpload: true,
          dataPreview: true,
          pythonProcessing: true,
          sqlGeneration: true,
          dashboard: true,
          widgets: true,
          realTimeMonitoring: true,
          performanceMonitoring: true,
          jobTracking: false,
          userFeedback: false,
          streamlinedNavigation: false,
          globalSearch: false,
          quickActions: false,
          keyboardShortcuts: false,
          onboardingFlow: false,
          interactiveTutorial: false,
          accessibilityFeatures: true,
          storytelling: false,
          narrativeGeneration: false,
          versionHistory: false,
          changeTracking: false,
          metadataSidebar: false,
          processingHistory: false,
          smartOrganization: false,
          aiSuggestions: false
        },
        theme: 'dark',
        accessibility: {
          enabled: true,
          highContrast: true,
          screenReader: true,
          keyboardNavigation: true
        },
        performance: {
          enableLazyLoading: true,
          enableVirtualization: true,
          maxFileSize: 500,
          chunkSize: 5000
        }
      };
      
      render(
        <DataCenterUnified
          config={advancedConfig}
          {...mockHandlers}
        />
      );
      
      expect(document.body).toBeInTheDocument();
    });

    it('handles theme changes', async () => {
      const { rerender } = render(
        <DataCenterUnified
          config={{ ...mockConfig, theme: 'light' }}
          {...mockHandlers}
        />
      );
      
      expect(document.body).toBeInTheDocument();
      
      rerender(
        <DataCenterUnified
          config={{ ...mockConfig, theme: 'dark' }}
          {...mockHandlers}
        />
      );
      
      await waitFor(() => {
        expect(document.body).toBeInTheDocument();
      });
    });
  });

  describe('Data Handling', () => {
    it('handles data changes through callback', async () => {
      render(
        <DataCenterUnified
          config={mockConfig}
          {...mockHandlers}
        />
      );
      
      expect(document.body).toBeInTheDocument();
    });

    it('calls onDataChange when data is modified', async () => {
      render(
        <DataCenterUnified
          config={mockConfig}
          {...mockHandlers}
        />
      );
      
      expect(mockHandlers.onDataChange).toBeDefined();
    });

    it('manages internal state correctly', async () => {
      render(
        <DataCenterUnified
          config={mockConfig}
          {...mockHandlers}
        />
      );
      
      expect(document.body).toBeInTheDocument();
    });
  });

  describe('Performance', () => {
    it('enables virtualization when configured', () => {
      const performanceConfig = {
        ...mockConfig,
        performance: {
          enableLazyLoading: true,
          enableVirtualization: true,
          maxFileSize: 200,
          chunkSize: 2000
        }
      };
      
      render(
        <DataCenterUnified
          config={performanceConfig}
          {...mockHandlers}
        />
      );
      
      expect(document.body).toBeInTheDocument();
    });

    it('lazy loads widgets when configured', async () => {
      const performanceConfig = {
        ...mockConfig,
        performance: {
          enableLazyLoading: true,
          enableVirtualization: false,
          maxFileSize: 200,
          chunkSize: 1000
        }
      };
      
      render(
        <DataCenterUnified
          config={performanceConfig}
          {...mockHandlers}
        />
      );
      
      expect(document.body).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('supports keyboard navigation', async () => {
      render(
        <DataCenterUnified
          config={mockConfig}
          {...mockHandlers}
        />
      );
      
      expect(document.body).toBeInTheDocument();
    });

    it('provides proper ARIA labels', () => {
      render(
        <DataCenterUnified
          config={mockConfig}
          {...mockHandlers}
        />
      );
      
      expect(document.body).toBeInTheDocument();
    });

    it('supports screen readers', () => {
      render(
        <DataCenterUnified
          config={mockConfig}
          {...mockHandlers}
        />
      );
      
      expect(document.body).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('handles invalid configuration gracefully', () => {
      const invalidConfig = { ...mockConfig, theme: null as any };
      
      render(
        <DataCenterUnified
          config={invalidConfig}
          {...mockHandlers}
        />
      );
      
      expect(document.body).toBeInTheDocument();
    });

    it('handles missing handlers gracefully', () => {
      render(
        <DataCenterUnified
          config={mockConfig}
          onDataChange={mockHandlers.onDataChange}
          onBack={mockHandlers.onBack}
        />
      );
      
      expect(document.body).toBeInTheDocument();
    });
  });
});