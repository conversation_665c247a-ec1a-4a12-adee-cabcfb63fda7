import {
  validateFileType,
  formatFileSize,
  getFileExtension,
  isSupportedDataFile,
  parseCSV,
  parseJSON,
  readFileAsText,
  generateFileId
} from '../../utils/fileUtils';

describe('fileUtils', () => {
  describe('validateFileType', () => {
    it('should accept valid file types', () => {
      const csvFile = new File([''], 'test.csv', { type: 'text/csv' });
      const jsonFile = new File([''], 'data.json', { type: 'application/json' });
      expect(validateFileType(csvFile, '.csv,.json')).toBe(true);
      expect(validateFileType(jsonFile, '.csv,.json')).toBe(true);
    });

    it('should reject invalid file types', () => {
      const exeFile = new File([''], 'test.exe', { type: 'application/octet-stream' });
      const pdfFile = new File([''], 'document.pdf', { type: 'application/pdf' });
      expect(validateFileType(exeFile, '.csv,.json')).toBe(false);
      expect(validateFileType(pdfFile, '.csv,.json')).toBe(false);
    });

    it('should handle files without extensions', () => {
      const noExtFile = new File([''], 'README', { type: 'text/plain' });
      expect(validateFileType(noExtFile, '.csv,.json')).toBe(false);
    });
  });

  describe('formatFileSize', () => {
    it('should format bytes correctly', () => {
      expect(formatFileSize(0)).toBe('0 B');
      expect(formatFileSize(512)).toBe('512 B');
      expect(formatFileSize(1024)).toBe('1.0 KB');
      expect(formatFileSize(1536)).toBe('1.5 KB');
      expect(formatFileSize(1048576)).toBe('1.0 MB');
      expect(formatFileSize(1073741824)).toBe('1.0 GB');
    });

    it('should handle negative values', () => {
      expect(formatFileSize(-100)).toBe('0 B');
    });

    it('should handle very large values', () => {
      expect(formatFileSize(1099511627776)).toBe('1.0 TB');
    });
  });

  describe('getFileExtension', () => {
    it('should extract file extensions correctly', () => {
      expect(getFileExtension('test.csv')).toBe('csv');
      expect(getFileExtension('data.json')).toBe('json');
      expect(getFileExtension('file.CSV')).toBe('csv'); // lowercase
      expect(getFileExtension('archive.tar.gz')).toBe('gz');
    });

    it('should handle files without extensions', () => {
      expect(getFileExtension('README')).toBe('');
      expect(getFileExtension('')).toBe('');
      expect(getFileExtension('.')).toBe('');
    });

    it('should handle hidden files', () => {
      expect(getFileExtension('.gitignore')).toBe('');
      expect(getFileExtension('.env.local')).toBe('local');
    });
  });

  describe('isSupportedDataFile', () => {
    it('should identify supported data files', () => {
      const csvFile = new File([''], 'data.csv', { type: 'text/csv' });
      const jsonFile = new File([''], 'config.json', { type: 'application/json' });
      const tsvFile = new File([''], 'data.tsv', { type: 'text/tab-separated-values' });
      const txtFile = new File([''], 'data.txt', { type: 'text/plain' });
      
      expect(isSupportedDataFile(csvFile)).toBe(true);
      expect(isSupportedDataFile(jsonFile)).toBe(true);
      expect(isSupportedDataFile(tsvFile)).toBe(true);
      expect(isSupportedDataFile(txtFile)).toBe(true);
    });

    it('should reject unsupported files', () => {
      const pngFile = new File([''], 'image.png', { type: 'image/png' });
      const pdfFile = new File([''], 'document.pdf', { type: 'application/pdf' });
      const jsFile = new File([''], 'script.js', { type: 'text/javascript' });
      
      expect(isSupportedDataFile(pngFile)).toBe(false);
      expect(isSupportedDataFile(pdfFile)).toBe(false);
      expect(isSupportedDataFile(jsFile)).toBe(false);
    });
  });

  describe('parseCSV', () => {
    it('should parse valid CSV content', () => {
      const csvContent = 'name,age,city\nJohn,30,NYC\nJane,25,LA';
      const result = parseCSV(csvContent);
      
      expect(result.headers).toEqual(['name', 'age', 'city']);
      expect(result.rows).toEqual([
        ['John', '30', 'NYC'],
        ['Jane', '25', 'LA']
      ]);
    });

    it('should handle empty CSV', () => {
      const result = parseCSV('');
      expect(result.headers).toEqual([]);
      expect(result.rows).toEqual([]);
    });

    it('should handle CSV with multiple columns', () => {
      const csvContent = 'name,age,city,country\nJohn,30,NYC,USA';
      const result = parseCSV(csvContent);
      expect(result.headers).toEqual(['name', 'age', 'city', 'country']);
      expect(result.rows).toEqual([['John', '30', 'NYC', 'USA']]);
    });
  });

  describe('parseJSON', () => {
    it('should parse valid JSON array', () => {
      const jsonContent = '[{"name":"John","age":30},{"name":"Jane","age":25}]';
      const result = parseJSON(jsonContent);
      
      expect(result.headers).toEqual(['name', 'age']);
      expect(result.rows).toEqual([
        ['John', 30],
        ['Jane', 25]
      ]);
    });

    it('should handle empty JSON', () => {
      const result = parseJSON('');
      expect(result.headers).toEqual([]);
      expect(result.rows).toEqual([]);
    });
  });

  describe('readFileAsText', () => {
    it('should read file content as text', async () => {
      const content = 'Hello, World!';
      const file = new File([content], 'test.txt', { type: 'text/plain' });
      
      const result = await readFileAsText(file);
      expect(result.success).toBe(true);
      expect(result.content).toBe(content);
    });

    it('should handle file reading errors', async () => {
      // Create a mock file that will cause an error
      const mockFile = {
        name: 'test.txt',
        size: 1000,
        type: 'text/plain'
      } as File;
      
      // Mock FileReader to simulate error
      const originalFileReader = global.FileReader;
      global.FileReader = jest.fn(() => ({
        readAsText: jest.fn(function(this: any) {
          setTimeout(() => this.onerror?.(new Error('Read error')), 0);
        }),
        result: null,
        error: new Error('Read error'),
        onload: null,
        onerror: null
      })) as any;

      const result = await readFileAsText(mockFile);
      expect(result.success).toBe(false);
      expect(result.error).toContain('Failed to read file');

      global.FileReader = originalFileReader;
    });
  });

  describe('generateFileId', () => {
    it('should generate unique IDs for different files', () => {
      const file1 = new File(['content1'], 'file1.txt', { type: 'text/plain' });
      const file2 = new File(['content2'], 'file2.txt', { type: 'text/plain' });
      
      const id1 = generateFileId(file1);
      const id2 = generateFileId(file2);
      
      expect(id1).not.toBe(id2);
      expect(typeof id1).toBe('string');
      expect(typeof id2).toBe('string');
      expect(id1.length).toBeGreaterThan(0);
      expect(id2.length).toBeGreaterThan(0);
    });

    it('should generate consistent IDs for same file', () => {
      const file = new File(['content'], 'test.txt', { type: 'text/plain' });
      
      const id1 = generateFileId(file);
      const id2 = generateFileId(file);
      
      expect(id1).toBe(id2);
    });
  });
});