// DataCenter Feature Configuration System
// This file defines the configuration for the unified DataCenter component

export interface DataCenterFeatures {
  // Core features
  fileUpload: boolean;
  dataPreview: boolean;
  pythonProcessing: boolean;
  sqlGeneration: boolean;
  
  // Enhanced features
  dashboard: boolean;
  widgets: boolean;
  realTimeMonitoring: boolean;
  
  // Feedback features
  performanceMonitoring: boolean;
  jobTracking: boolean;
  userFeedback: boolean;
  
  // Navigation features
  streamlinedNavigation: boolean;
  globalSearch: boolean;
  quickActions: boolean;
  keyboardShortcuts: boolean;
  
  // Onboarding features
  onboardingFlow: boolean;
  interactiveTutorial: boolean;
  
  // Accessibility features
  accessibilityFeatures: boolean;
  
  // Data storytelling
  storytelling: boolean;
  narrativeGeneration: boolean;
  
  // Version history
  versionHistory: boolean;
  changeTracking: boolean;
  
  // Metadata sidebar
  metadataSidebar: boolean;
  processingHistory: boolean;
  
  // Smart organization
  smartOrganization: boolean;
  aiSuggestions: boolean;
}

export interface DataCenterConfig {
  features: DataCenterFeatures;
  theme: 'light' | 'dark' | 'auto';
  layout: 'default' | 'compact' | 'expanded';
  accessibility: {
    enabled: boolean;
    highContrast: boolean;
    screenReader: boolean;
    keyboardNavigation: boolean;
  };
  performance: {
    enableLazyLoading: boolean;
    enableVirtualization: boolean;
    maxFileSize: number; // in MB
    chunkSize: number;
  };
}

// Predefined configurations for different use cases
export const DATACENTER_PRESETS: Record<string, DataCenterConfig> = {
  // Basic configuration - minimal features
  basic: {
    features: {
      fileUpload: true,
      dataPreview: true,
      pythonProcessing: true,
      sqlGeneration: true,
      dashboard: false,
      widgets: false,
      realTimeMonitoring: false,
      performanceMonitoring: false,
      jobTracking: false,
      userFeedback: false,
      streamlinedNavigation: false,
      globalSearch: false,
      quickActions: false,
      keyboardShortcuts: false,
      onboardingFlow: false,
      interactiveTutorial: false,
      accessibilityFeatures: false,
      storytelling: false,
      narrativeGeneration: false,
      versionHistory: false,
      changeTracking: false,
      metadataSidebar: false,
      processingHistory: false,
      smartOrganization: false,
      aiSuggestions: false,
    },
    theme: 'auto',
    layout: 'default',
    accessibility: {
      enabled: true,
      highContrast: false,
      screenReader: true,
      keyboardNavigation: true,
    },
    performance: {
      enableLazyLoading: true,
      enableVirtualization: false,
      maxFileSize: 100,
      chunkSize: 1000,
    },
  },
  
  // Enhanced configuration - dashboard and widgets
  enhanced: {
    features: {
      fileUpload: true,
      dataPreview: true,
      pythonProcessing: true,
      sqlGeneration: true,
      dashboard: true,
      widgets: true,
      realTimeMonitoring: true,
      performanceMonitoring: false,
      jobTracking: false,
      userFeedback: false,
      streamlinedNavigation: true,
      globalSearch: true,
      quickActions: true,
      keyboardShortcuts: true,
      onboardingFlow: false,
      interactiveTutorial: false,
      accessibilityFeatures: true,
      storytelling: false,
      narrativeGeneration: false,
      versionHistory: false,
      changeTracking: false,
      metadataSidebar: false,
      processingHistory: false,
      smartOrganization: false,
      aiSuggestions: false,
    },
    theme: 'auto',
    layout: 'expanded',
    accessibility: {
      enabled: true,
      highContrast: false,
      screenReader: true,
      keyboardNavigation: true,
    },
    performance: {
      enableLazyLoading: true,
      enableVirtualization: true,
      maxFileSize: 500,
      chunkSize: 2000,
    },
  },
  
  // Professional configuration - all features enabled
  professional: {
    features: {
      fileUpload: true,
      dataPreview: true,
      pythonProcessing: true,
      sqlGeneration: true,
      dashboard: true,
      widgets: true,
      realTimeMonitoring: true,
      performanceMonitoring: true,
      jobTracking: true,
      userFeedback: true,
      streamlinedNavigation: true,
      globalSearch: true,
      quickActions: true,
      keyboardShortcuts: true,
      onboardingFlow: true,
      interactiveTutorial: true,
      accessibilityFeatures: true,
      storytelling: true,
      narrativeGeneration: true,
      versionHistory: true,
      changeTracking: true,
      metadataSidebar: true,
      processingHistory: true,
      smartOrganization: true,
      aiSuggestions: true,
    },
    theme: 'auto',
    layout: 'expanded',
    accessibility: {
      enabled: true,
      highContrast: true,
      screenReader: true,
      keyboardNavigation: true,
    },
    performance: {
      enableLazyLoading: true,
      enableVirtualization: true,
      maxFileSize: 1000,
      chunkSize: 5000,
    },
  },
  
  // Feedback-focused configuration
  feedback: {
    features: {
      fileUpload: true,
      dataPreview: true,
      pythonProcessing: true,
      sqlGeneration: true,
      dashboard: true,
      widgets: false,
      realTimeMonitoring: true,
      performanceMonitoring: true,
      jobTracking: true,
      userFeedback: true,
      streamlinedNavigation: false,
      globalSearch: false,
      quickActions: true,
      keyboardShortcuts: false,
      onboardingFlow: false,
      interactiveTutorial: false,
      accessibilityFeatures: true,
      storytelling: false,
      narrativeGeneration: false,
      versionHistory: true,
      changeTracking: true,
      metadataSidebar: true,
      processingHistory: true,
      smartOrganization: false,
      aiSuggestions: false,
    },
    theme: 'auto',
    layout: 'default',
    accessibility: {
      enabled: true,
      highContrast: false,
      screenReader: true,
      keyboardNavigation: true,
    },
    performance: {
      enableLazyLoading: true,
      enableVirtualization: false,
      maxFileSize: 200,
      chunkSize: 1500,
    },
  },
};

// Default configuration
export const DEFAULT_CONFIG: DataCenterConfig = DATACENTER_PRESETS.enhanced;

// Configuration validation
export function validateConfig(config: Partial<DataCenterConfig>): DataCenterConfig {
  return {
    features: {
      ...DEFAULT_CONFIG.features,
      ...config.features,
    },
    theme: config.theme || DEFAULT_CONFIG.theme,
    layout: config.layout || DEFAULT_CONFIG.layout,
    accessibility: {
      ...DEFAULT_CONFIG.accessibility,
      ...config.accessibility,
    },
    performance: {
      ...DEFAULT_CONFIG.performance,
      ...config.performance,
    },
  };
}

// Feature flag utilities
export function isFeatureEnabled(config: DataCenterConfig, feature: keyof DataCenterFeatures): boolean {
  return config.features[feature];
}

export function getEnabledFeatures(config: DataCenterConfig): (keyof DataCenterFeatures)[] {
  return Object.entries(config.features)
    .filter(([_, enabled]) => enabled)
    .map(([feature, _]) => feature as keyof DataCenterFeatures);
}

// Configuration presets for migration
export const MIGRATION_PRESETS = {
  fromBasic: DATACENTER_PRESETS.basic,
  fromEnhanced: DATACENTER_PRESETS.enhanced,
  fromFeedback: DATACENTER_PRESETS.feedback,
  fromOnboarding: {
    ...DATACENTER_PRESETS.enhanced,
    features: {
      ...DATACENTER_PRESETS.enhanced.features,
      onboardingFlow: true,
      interactiveTutorial: true,
    },
  },
  fromStreamlined: {
    ...DATACENTER_PRESETS.enhanced,
    features: {
      ...DATACENTER_PRESETS.enhanced.features,
      streamlinedNavigation: true,
      globalSearch: true,
      quickActions: true,
      keyboardShortcuts: true,
    },
  },
};