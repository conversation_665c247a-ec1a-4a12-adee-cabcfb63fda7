import React, { useState, useMemo } from 'react';
import { 
  Code, 
  <PERSON>rkles, 
  FileText, 
  ChevronLeft, 
  ChevronRight,
  Filter,
  SortAsc,
  Eye
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { DataPreview } from '../types';
import { formatRowCount } from '../utils/formatters';

interface DataPreviewPanelProps {
  data: DataPreview;
  onProcessData: () => void;
  onGenerateStory?: () => void;
  onShowMetadata?: () => void;
  fileName?: string;
  className?: string;
}

export const DataPreviewPanel: React.FC<DataPreviewPanelProps> = ({
  data,
  onProcessData,
  onGenerateStory,
  onShowMetadata,
  fileName,
  className
}) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);
  const [sortColumn, setSortColumn] = useState<string>('');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [filterValue, setFilterValue] = useState('');
  const [selectedColumn, setSelectedColumn] = useState<string>('');

  // Pagination logic
  const totalPages = Math.ceil(data.rows.length / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;

  // Filtered and sorted data
  const processedData = useMemo(() => {
    let filtered = data.rows;

    // Apply filter
    if (filterValue && selectedColumn) {
      const columnIndex = data.headers.indexOf(selectedColumn);
      if (columnIndex !== -1) {
        filtered = filtered.filter(row => 
          String(row[columnIndex]).toLowerCase().includes(filterValue.toLowerCase())
        );
      }
    }

    // Apply sorting
    if (sortColumn) {
      const columnIndex = data.headers.indexOf(sortColumn);
      if (columnIndex !== -1) {
        filtered = [...filtered].sort((a, b) => {
          const aVal = String(a[columnIndex]);
          const bVal = String(b[columnIndex]);
          const comparison = aVal.localeCompare(bVal);
          return sortDirection === 'asc' ? comparison : -comparison;
        });
      }
    }

    return filtered;
  }, [data.rows, data.headers, filterValue, selectedColumn, sortColumn, sortDirection]);

  const paginatedData = processedData.slice(startIndex, endIndex);

  const handleSort = (column: string) => {
    if (sortColumn === column) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortColumn(column);
      setSortDirection('asc');
    }
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(Math.max(1, Math.min(page, totalPages)));
  };

  return (
    <Card className={`card-data ${className || ''}`}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-2xl font-bold">Data Preview</CardTitle>
            <p className="text-muted-foreground mt-1">
              {formatRowCount(data.totalRows)} rows × {data.headers.length} columns
            </p>
            {fileName && (
              <Button
                variant="link"
                size="sm"
                onClick={onShowMetadata}
                className="text-xs mt-1 p-0 h-auto text-data-primary"
              >
                <FileText className="w-3 h-3 mr-1" />
                View file metadata
              </Button>
            )}
          </div>
          
          <div className="flex gap-2">
            <Button
              onClick={onProcessData}
              className="btn-data interactive-lift"
              disabled={!data.fullData || data.fullData.length === 0}
            >
              <Code className="w-4 h-4 mr-2" />
              Process Data
            </Button>
            
            {onGenerateStory && (
              <Button
                variant="outline"
                onClick={onGenerateStory}
                className="interactive-lift"
              >
                <Sparkles className="w-4 h-4 mr-2" />
                Generate Story
              </Button>
            )}
          </div>
        </div>

        {/* Filters and Controls */}
        <div className="flex gap-4 mt-4">
          <div className="flex-1">
            <div className="flex gap-2">
              <Select value={selectedColumn} onValueChange={setSelectedColumn}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Select column to filter" />
                </SelectTrigger>
                <SelectContent>
                  {data.headers.map((header) => (
                    <SelectItem key={header} value={header}>
                      {header}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              <Input
                placeholder="Filter value..."
                value={filterValue}
                onChange={(e) => setFilterValue(e.target.value)}
                className="w-48"
                disabled={!selectedColumn}
              />
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setFilterValue('');
                  setSelectedColumn('');
                }}
                disabled={!filterValue && !selectedColumn}
              >
                Clear
              </Button>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Filter className="w-4 h-4 text-muted-foreground" />
            <span className="text-sm text-muted-foreground">
              Showing {paginatedData.length} of {processedData.length} rows
            </span>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        {/* Data Table */}
        <div className="border rounded-lg overflow-hidden">
          <div className="overflow-x-auto scrollbar-modern">
            <Table>
              <TableHeader>
                <TableRow className="bg-muted/50">
                  {data.headers.map((header, index) => (
                    <TableHead 
                      key={index} 
                      className="font-medium cursor-pointer hover:bg-muted/70 transition-colors"
                      onClick={() => handleSort(header)}
                    >
                      <div className="flex items-center gap-2">
                        {header}
                        {data.dataTypes?.[header] && (
                          <Badge variant="secondary" className="text-xs badge-data">
                            {data.dataTypes[header]}
                          </Badge>
                        )}
                        {sortColumn === header && (
                          <SortAsc className={`w-3 h-3 transition-transform ${
                            sortDirection === 'desc' ? 'rotate-180' : ''
                          }`} />
                        )}
                      </div>
                    </TableHead>
                  ))}
                </TableRow>
              </TableHeader>
              <TableBody>
                {paginatedData.map((row, rowIndex) => (
                  <TableRow key={startIndex + rowIndex} className="hover:bg-muted/30">
                    {row.map((cell, cellIndex) => (
                      <TableCell key={cellIndex} className="text-sm">
                        <div className="max-w-48 truncate" title={String(cell || '')}>
                          {String(cell || '').substring(0, 100)}
                          {String(cell || '').length > 100 && '...'}
                        </div>
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between mt-4">
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
              >
                <ChevronLeft className="w-4 h-4" />
                Previous
              </Button>
              
              <div className="flex items-center gap-1">
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  const page = i + 1;
                  return (
                    <Button
                      key={page}
                      variant={currentPage === page ? "default" : "outline"}
                      size="sm"
                      onClick={() => handlePageChange(page)}
                      className="w-8 h-8 p-0"
                    >
                      {page}
                    </Button>
                  );
                })}
                {totalPages > 5 && (
                  <>
                    <span className="text-muted-foreground">...</span>
                    <Button
                      variant={currentPage === totalPages ? "default" : "outline"}
                      size="sm"
                      onClick={() => handlePageChange(totalPages)}
                      className="w-8 h-8 p-0"
                    >
                      {totalPages}
                    </Button>
                  </>
                )}
              </div>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
              >
                Next
                <ChevronRight className="w-4 h-4" />
              </Button>
            </div>
            
            <p className="text-sm text-muted-foreground">
              Page {currentPage} of {totalPages}
            </p>
          </div>
        )}

        {/* Summary */}
        <div className="mt-4 p-4 bg-muted/30 rounded-lg">
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <p className="text-2xl font-bold text-data-primary">{formatRowCount(data.totalRows)}</p>
              <p className="text-sm text-muted-foreground">Total Rows</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-data-primary">{data.headers.length}</p>
              <p className="text-sm text-muted-foreground">Columns</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-data-primary">
                {Object.keys(data.dataTypes || {}).length}
              </p>
              <p className="text-sm text-muted-foreground">Data Types</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};