import React from 'react';
import { ChevronRight, Home } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { BreadcrumbItem } from '../types';
import { formatTimestamp } from '../utils/formatters';

interface BreadcrumbNavigationProps {
  items: BreadcrumbItem[];
  onNavigate?: (stage: string) => void;
  className?: string;
}

export const BreadcrumbNavigation: React.FC<BreadcrumbNavigationProps> = ({
  items,
  onNavigate,
  className
}) => {
  if (items.length === 0) return null;

  return (
    <div className={`border-b px-4 py-3 bg-muted/20 ${className || ''}`}>
      <nav className="flex items-center space-x-2 text-sm">
        {/* Home */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onNavigate?.('upload')}
          className="h-8 px-2 text-muted-foreground hover:text-foreground"
        >
          <Home className="w-3 h-3" />
        </Button>
        
        <ChevronRight className="w-3 h-3 text-muted-foreground" />
        
        {/* Breadcrumb Items */}
        {items.map((item, index) => {
          const isLast = index === items.length - 1;
          const isClickable = !isLast && onNavigate;
          
          return (
            <React.Fragment key={index}>
              <div className="flex items-center gap-2">
                {isClickable ? (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onNavigate(item.stage)}
                    className="h-8 px-2 text-muted-foreground hover:text-data-primary transition-colors"
                  >
                    {item.label}
                  </Button>
                ) : (
                  <span className={`px-2 py-1 rounded-md font-medium ${
                    isLast 
                      ? 'text-data-primary bg-data-primary/10' 
                      : 'text-muted-foreground'
                  }`}>
                    {item.label}
                  </span>
                )}
                
                {/* Data Summary */}
                {item.dataSummary && (
                  <div className="flex items-center gap-1">
                    {item.dataSummary.fileType && (
                      <Badge variant="secondary" className="text-xs badge-data">
                        {item.dataSummary.fileType}
                      </Badge>
                    )}
                    {item.dataSummary.rows && (
                      <Badge variant="secondary" className="text-xs">
                        {item.dataSummary.rows.toLocaleString()} rows
                      </Badge>
                    )}
                    {item.dataSummary.columns && (
                      <Badge variant="secondary" className="text-xs">
                        {item.dataSummary.columns} cols
                      </Badge>
                    )}
                  </div>
                )}
                
                {/* Timestamp for last item */}
                {isLast && (
                  <span className="text-xs text-muted-foreground ml-2">
                    {formatTimestamp(item.timestamp)}
                  </span>
                )}
              </div>
              
              {!isLast && (
                <ChevronRight className="w-3 h-3 text-muted-foreground" />
              )}
            </React.Fragment>
          );
        })}
      </nav>
    </div>
  );
};