import React from 'react';
import { 
  <PERSON>ap, 
  <PERSON><PERSON>, 
  <PERSON>rkles, 
  FileText,
  Clock,
  Star,
  Plus,
  History,
  Bookmark
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';

interface SidebarProps {
  collapsed: boolean;
  className?: string;
}

const quickActions = [
  { id: 'upload', label: 'Upload File', icon: Plus, shortcut: 'Ctrl+U' },
  { id: 'process', label: 'Process Data', icon: Zap, shortcut: 'Ctrl+P' },
  { id: 'query', label: 'SQL Query', icon: FileText, shortcut: 'Ctrl+Q' },
  { id: 'export', label: 'Export Data', icon: FileText, shortcut: 'Ctrl+E' },
];

const recentFiles = [
  { name: 'sample_data.csv', size: '2.1 MB', time: '2 min ago', status: 'processed' },
  { name: 'user_analytics.xlsx', size: '5.2 MB', time: '1 hour ago', status: 'processing' },
  { name: 'product_catalog.json', size: '1.8 MB', time: '3 hours ago', status: 'completed' },
];

const features = [
  { name: 'Smart Templates', description: 'AI-powered processing templates', new: true },
  { name: 'Data Lineage', description: 'Track data transformations', popular: true },
  { name: 'Real-time Collaboration', description: 'Work together on datasets', new: true },
];

export const Sidebar: React.FC<SidebarProps> = ({ 
  collapsed, 
  className 
}) => {
  if (collapsed) {
    return (
      <div className={`w-16 bg-muted/30 border-r p-2 ${className || ''}`}>
        <div className="space-y-2">
          {quickActions.slice(0, 4).map((action) => (
            <Button
              key={action.id}
              variant="ghost"
              size="sm"
              className="w-full h-12 p-0 flex flex-col gap-1"
              title={action.label}
            >
              <action.icon className="w-4 h-4" />
            </Button>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className={`w-64 bg-muted/30 border-r ${className || ''}`}>
      <ScrollArea className="h-full p-4">
        <div className="space-y-6">
          {/* Quick Actions */}
          <Card className="glass-panel">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-semibold flex items-center gap-2">
                <Zap className="w-4 h-4 text-data-primary" />
                Quick Actions
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              {quickActions.map((action) => (
                <Button
                  key={action.id}
                  variant="ghost"
                  size="sm"
                  className="w-full justify-start interactive-lift"
                >
                  <action.icon className="w-4 h-4 mr-2" />
                  <span className="flex-1 text-left">{action.label}</span>
                  <kbd className="text-xs">{action.shortcut}</kbd>
                </Button>
              ))}
            </CardContent>
          </Card>

          {/* Recent Files */}
          <Card className="glass-panel">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-semibold flex items-center gap-2">
                <Clock className="w-4 h-4 text-success-primary" />
                Recent Files
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {recentFiles.map((file, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate" title={file.name}>
                        {file.name}
                      </p>
                      <div className="flex items-center gap-2 mt-1">
                        <span className="text-xs text-muted-foreground">{file.size}</span>
                        <span className="text-xs text-muted-foreground">•</span>
                        <span className="text-xs text-muted-foreground">{file.time}</span>
                      </div>
                    </div>
                    <Badge 
                      variant="secondary" 
                      className={`text-xs ml-2 badge-${
                        file.status === 'processed' ? 'success' : 
                        file.status === 'processing' ? 'warning' : 'data'
                      }`}
                    >
                      {file.status}
                    </Badge>
                  </div>
                  {index < recentFiles.length - 1 && <Separator />}
                </div>
              ))}
              
              <Button variant="ghost" size="sm" className="w-full mt-3">
                <History className="w-4 h-4 mr-2" />
                View All History
              </Button>
            </CardContent>
          </Card>

          {/* Feature Discovery */}
          <Card className="glass-panel">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-semibold flex items-center gap-2">
                <Sparkles className="w-4 h-4 text-warning-primary" />
                Discover Features
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {features.map((feature, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <p className="text-sm font-medium">{feature.name}</p>
                        {feature.new && (
                          <Badge variant="secondary" className="text-xs badge-success">
                            New
                          </Badge>
                        )}
                        {feature.popular && (
                          <Badge variant="secondary" className="text-xs badge-warning">
                            Popular
                          </Badge>
                        )}
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        {feature.description}
                      </p>
                    </div>
                  </div>
                  {index < features.length - 1 && <Separator />}
                </div>
              ))}
              
              <Button variant="ghost" size="sm" className="w-full mt-3">
                <Star className="w-4 h-4 mr-2" />
                Explore All Features
              </Button>
            </CardContent>
          </Card>

          {/* Bookmarks */}
          <Card className="glass-panel">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-semibold flex items-center gap-2">
                <Bookmark className="w-4 h-4 text-info-primary" />
                Bookmarks
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-4">
                <Bookmark className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
                <p className="text-sm text-muted-foreground">No bookmarks yet</p>
                <Button variant="ghost" size="sm" className="mt-2">
                  <Plus className="w-4 h-4 mr-2" />
                  Add Bookmark
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </ScrollArea>
    </div>
  );
};