import React, { useState } from 'react';
import { 
  CheckCircle, 
  AlertCircle, 
  Loader2, 
  Download, 
  Database,
  RefreshCw,
  Play,
  Settings
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ProcessingStatus } from '../enums';
import { ProcessingResult, ProcessingTemplate } from '../types';
import { formatProcessingTime, formatFileSize } from '../utils/formatters';

interface ProcessingPanelProps {
  status: ProcessingStatus;
  result?: ProcessingResult;
  errorMessage?: string;
  templates?: ProcessingTemplate[];
  selectedTemplate?: string;
  onTemplateChange?: (template: string) => void;
  onRetry: () => void;
  onExport?: () => void;
  onSQLQuery?: () => void;
  onBack?: () => void;
  progress?: number;
  className?: string;
}

export const ProcessingPanel: React.FC<ProcessingPanelProps> = ({
  status,
  result,
  errorMessage,
  templates = [],
  selectedTemplate,
  onTemplateChange,
  onRetry,
  onExport,
  onSQLQuery,
  onBack,
  progress = 0,
  className
}) => {
  const [showAdvanced, setShowAdvanced] = useState(false);

  const getStatusColor = (status: ProcessingStatus) => {
    switch (status) {
      case ProcessingStatus.SUCCESS:
        return 'success';
      case ProcessingStatus.ERROR:
        return 'error';
      case ProcessingStatus.PROCESSING:
        return 'warning';
      default:
        return 'data';
    }
  };

  const getStatusIcon = (status: ProcessingStatus) => {
    switch (status) {
      case ProcessingStatus.SUCCESS:
        return <CheckCircle className="w-5 h-5" />;
      case ProcessingStatus.ERROR:
        return <AlertCircle className="w-5 h-5" />;
      case ProcessingStatus.PROCESSING:
        return <Loader2 className="w-5 h-5 animate-spin" />;
      default:
        return <Play className="w-5 h-5" />;
    }
  };

  return (
    <Card className={`card-${getStatusColor(status)} ${className || ''}`}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            {getStatusIcon(status)}
            Data Processing
          </CardTitle>
          
          {onBack && (
            <Button variant="ghost" size="sm" onClick={onBack}>
              Back to Preview
            </Button>
          )}
        </div>

        {/* Template Selection */}
        {templates.length > 0 && status === ProcessingStatus.IDLE && (
          <div className="flex gap-4 mt-4">
            <div className="flex-1">
              <Select value={selectedTemplate} onValueChange={onTemplateChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Select processing template" />
                </SelectTrigger>
                <SelectContent>
                  {templates.map((template) => (
                    <SelectItem key={template.id} value={template.id}>
                      <div className="flex items-center gap-2">
                        <Badge variant="secondary" className="text-xs">
                          {template.category}
                        </Badge>
                        <span>{template.name}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowAdvanced(!showAdvanced)}
            >
              <Settings className="w-4 h-4 mr-2" />
              Advanced
            </Button>
          </div>
        )}
      </CardHeader>

      <CardContent>
        {/* Processing Status */}
        {status === ProcessingStatus.PROCESSING && (
          <div className="space-y-4">
            <div className="text-center py-8">
              <div className="processing-pulse p-4 rounded-full inline-block mb-4">
                <Loader2 className="w-8 h-8 animate-spin text-data-primary" />
              </div>
              <p className="text-lg font-medium">Processing your data...</p>
              <p className="text-sm text-muted-foreground mt-2">
                This may take a few moments depending on your data size
              </p>
            </div>
            
            {progress > 0 && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Progress</span>
                  <span>{Math.round(progress)}%</span>
                </div>
                <Progress value={progress} className="w-full" />
              </div>
            )}
          </div>
        )}

        {/* Success State */}
        {status === ProcessingStatus.SUCCESS && result && (
          <div className="space-y-6">
            <div className="flex items-center gap-2 text-success-primary">
              <CheckCircle className="w-5 h-5" />
              <span className="font-medium">Processing Complete!</span>
            </div>
            
            {/* Metrics */}
            <div className="grid grid-cols-3 gap-4">
              <div className="p-4 bg-muted/30 rounded-lg text-center">
                <p className="text-sm text-muted-foreground">Records Processed</p>
                <p className="text-2xl font-bold text-success-primary">
                  {result.recordsProcessed?.toLocaleString() || 0}
                </p>
              </div>
              <div className="p-4 bg-muted/30 rounded-lg text-center">
                <p className="text-sm text-muted-foreground">Processing Time</p>
                <p className="text-2xl font-bold text-success-primary">
                  {formatProcessingTime(result.duration || 0)}
                </p>
              </div>
              <div className="p-4 bg-muted/30 rounded-lg text-center">
                <p className="text-sm text-muted-foreground">Output Size</p>
                <p className="text-2xl font-bold text-success-primary">
                  {formatFileSize(result.outputSize || 0)}
                </p>
              </div>
            </div>

            {/* Preview */}
            {result.content && (
              <div className="border rounded-lg p-4 bg-muted/20">
                <h4 className="font-medium mb-2 flex items-center gap-2">
                  <Database className="w-4 h-4" />
                  Processing Results Preview
                </h4>
                <div className="max-h-48 overflow-auto scrollbar-modern">
                  <pre className="text-xs text-muted-foreground whitespace-pre-wrap">
                    {typeof result.content === 'string' 
                      ? result.content.substring(0, 500) + (result.content.length > 500 ? '...' : '')
                      : JSON.stringify(result.content, null, 2).substring(0, 500)
                    }
                  </pre>
                </div>
              </div>
            )}

            {/* Actions */}
            <div className="flex gap-2">
              {onExport && (
                <Button onClick={onExport} className="btn-success">
                  <Download className="w-4 h-4 mr-2" />
                  Export Results
                </Button>
              )}
              
              {onSQLQuery && (
                <Button variant="outline" onClick={onSQLQuery}>
                  <Database className="w-4 h-4 mr-2" />
                  Query with SQL
                </Button>
              )}
            </div>
          </div>
        )}

        {/* Error State */}
        {status === ProcessingStatus.ERROR && (
          <div className="space-y-4">
            <div className="flex items-center gap-2 text-error-primary">
              <AlertCircle className="w-5 h-5" />
              <span className="font-medium">Processing Failed</span>
            </div>
            
            <div className="p-4 bg-error-bg border border-error-primary/20 rounded-lg">
              <p className="text-sm text-error-primary">
                {errorMessage || 'An unexpected error occurred during processing'}
              </p>
            </div>
            
            <Button onClick={onRetry} variant="outline" className="w-full">
              <RefreshCw className="w-4 h-4 mr-2" />
              Retry Processing
            </Button>
          </div>
        )}

        {/* Idle State */}
        {status === ProcessingStatus.IDLE && (
          <div className="text-center py-8">
            <div className="p-4 bg-data-primary/10 rounded-full inline-block mb-4">
              <Play className="w-8 h-8 text-data-primary" />
            </div>
            <p className="text-lg font-medium">Ready to Process</p>
            <p className="text-sm text-muted-foreground mt-2">
              Select a processing template and click start to begin
            </p>
            
            <Button 
              onClick={onRetry} 
              className="mt-4 btn-data"
              disabled={!selectedTemplate}
            >
              <Play className="w-4 h-4 mr-2" />
              Start Processing
            </Button>
          </div>
        )}

        {/* Advanced Options */}
        {showAdvanced && status === ProcessingStatus.IDLE && (
          <div className="mt-6 p-4 border rounded-lg bg-muted/20">
            <h4 className="font-medium mb-3">Advanced Options</h4>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm">Enable parallel processing</span>
                <input type="checkbox" className="rounded" defaultChecked />
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Batch size</span>
                <Select defaultValue="1000">
                  <SelectTrigger className="w-24">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="500">500</SelectItem>
                    <SelectItem value="1000">1000</SelectItem>
                    <SelectItem value="2000">2000</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};