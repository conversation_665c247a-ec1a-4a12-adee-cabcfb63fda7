import React, { useState } from 'react';
import { 
  BarChart3, 
  TrendingUp, 
  Database, 
  Activity,
  Maximize2,
  Minimize2,
  Refresh<PERSON>w,
  Settings,
  Download
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import { Bar<PERSON><PERSON>, Bar, LineChart, Line, PieChart, Pie, Cell, XAxis, YAxis, ResponsiveContainer } from 'recharts';
import { DataPreview } from '../types';
import { formatRowCount } from '../utils/formatters';

interface DashboardViewProps {
  data?: DataPreview;
  className?: string;
}

// Mock chart data
const sampleBarData = [
  { name: 'Engineering', value: 45, count: 12 },
  { name: 'Marketing', value: 25, count: 8 },
  { name: 'Sales', value: 35, count: 10 },
  { name: 'HR', value: 15, count: 4 },
  { name: 'Finance', value: 20, count: 6 }
];

const sampleLineData = [
  { month: 'Jan', uploads: 12, processed: 10 },
  { month: 'Feb', uploads: 19, processed: 17 },
  { month: 'Mar', uploads: 15, processed: 14 },
  { month: 'Apr', uploads: 25, processed: 22 },
  { month: 'May', uploads: 32, processed: 28 },
  { month: 'Jun', uploads: 28, processed: 25 }
];

const samplePieData = [
  { name: 'CSV', value: 45, color: '#3b82f6' },
  { name: 'Excel', value: 30, color: '#10b981' },
  { name: 'JSON', value: 20, color: '#f59e0b' },
  { name: 'Text', value: 5, color: '#ef4444' }
];

export const DashboardView: React.FC<DashboardViewProps> = ({ 
  data, 
  className 
}) => {
  const [expandedWidget, setExpandedWidget] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  const handleRefresh = async () => {
    setRefreshing(true);
    // Simulate refresh
    setTimeout(() => setRefreshing(false), 1000);
  };

  const toggleExpand = (widgetId: string) => {
    setExpandedWidget(expandedWidget === widgetId ? null : widgetId);
  };

  return (
    <div className={`space-y-6 ${className || ''}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Data Center Dashboard</h1>
          <p className="text-muted-foreground">
            Overview of your data processing activities and insights
          </p>
        </div>
        
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={refreshing}
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button variant="outline" size="sm">
            <Settings className="w-4 h-4 mr-2" />
            Configure
          </Button>
          <Button variant="outline" size="sm">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="card-data">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Files</p>
                <p className="text-2xl font-bold text-data-primary">
                  {data ? formatRowCount(data.totalRows) : '1,250'}
                </p>
              </div>
              <Database className="w-8 h-8 text-data-primary/60" />
            </div>
            <div className="mt-2">
              <Badge variant="secondary" className="badge-success">
                +12% from last month
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card className="card-success">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Processed</p>
                <p className="text-2xl font-bold text-success-primary">98.5%</p>
              </div>
              <TrendingUp className="w-8 h-8 text-success-primary/60" />
            </div>
            <div className="mt-2">
              <Progress value={98.5} className="h-2" />
            </div>
          </CardContent>
        </Card>

        <Card className="card-warning">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Active Jobs</p>
                <p className="text-2xl font-bold text-warning-primary">3</p>
              </div>
              <Activity className="w-8 h-8 text-warning-primary/60 animate-pulse" />
            </div>
            <div className="mt-2">
              <Badge variant="secondary" className="badge-warning">
                2 in queue
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card className="card-info">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Storage Used</p>
                <p className="text-2xl font-bold text-info-primary">2.4 GB</p>
              </div>
              <BarChart3 className="w-8 h-8 text-info-primary/60" />
            </div>
            <div className="mt-2">
              <Progress value={65} className="h-2" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Department Distribution */}
        <Card className={`card-data transition-all ${expandedWidget === 'bar-chart' ? 'lg:col-span-2' : ''}`}>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="w-5 h-5" />
                Department Distribution
              </CardTitle>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => toggleExpand('bar-chart')}
              >
                {expandedWidget === 'bar-chart' ? (
                  <Minimize2 className="w-4 h-4" />
                ) : (
                  <Maximize2 className="w-4 h-4" />
                )}
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <ChartContainer
              config={{
                value: {
                  label: "Employees",
                  color: "var(--data-primary)",
                },
              }}
              className="h-64 w-full"
            >
              <BarChart data={sampleBarData}>
                <XAxis dataKey="name" />
                <YAxis />
                <ChartTooltip content={<ChartTooltipContent />} />
                <Bar dataKey="value" fill="var(--data-primary)" radius={[4, 4, 0, 0]} />
              </BarChart>
            </ChartContainer>
          </CardContent>
        </Card>

        {/* Processing Trends */}
        <Card className={`card-success transition-all ${expandedWidget === 'line-chart' ? 'lg:col-span-2' : ''}`}>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="w-5 h-5" />
                Processing Trends
              </CardTitle>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => toggleExpand('line-chart')}
              >
                {expandedWidget === 'line-chart' ? (
                  <Minimize2 className="w-4 h-4" />
                ) : (
                  <Maximize2 className="w-4 h-4" />
                )}
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <ChartContainer
              config={{
                uploads: {
                  label: "Uploads",
                  color: "var(--data-primary)",
                },
                processed: {
                  label: "Processed",
                  color: "var(--success-primary)",
                },
              }}
              className="h-64 w-full"
            >
              <LineChart data={sampleLineData}>
                <XAxis dataKey="month" />
                <YAxis />
                <ChartTooltip content={<ChartTooltipContent />} />
                <Line 
                  type="monotone" 
                  dataKey="uploads" 
                  stroke="var(--data-primary)" 
                  strokeWidth={2}
                  dot={{ fill: "var(--data-primary)", strokeWidth: 2, r: 4 }}
                />
                <Line 
                  type="monotone" 
                  dataKey="processed" 
                  stroke="var(--success-primary)" 
                  strokeWidth={2}
                  dot={{ fill: "var(--success-primary)", strokeWidth: 2, r: 4 }}
                />
              </LineChart>
            </ChartContainer>
          </CardContent>
        </Card>

        {/* File Types Distribution */}
        <Card className={`card-info transition-all ${expandedWidget === 'pie-chart' ? 'lg:col-span-2' : ''}`}>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Database className="w-5 h-5" />
                File Types
              </CardTitle>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => toggleExpand('pie-chart')}
              >
                {expandedWidget === 'pie-chart' ? (
                  <Minimize2 className="w-4 h-4" />
                ) : (
                  <Maximize2 className="w-4 h-4" />
                )}
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <ChartContainer
              config={{
                value: {
                  label: "Files",
                },
              }}
              className="h-64 w-full"
            >
              <PieChart>
                <Pie
                  data={samplePieData}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={100}
                  paddingAngle={5}
                  dataKey="value"
                >
                  {samplePieData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <ChartTooltip content={<ChartTooltipContent />} />
              </PieChart>
            </ChartContainer>
            
            {/* Legend */}
            <div className="flex justify-center gap-4 mt-4">
              {samplePieData.map((entry, index) => (
                <div key={index} className="flex items-center gap-2">
                  <div 
                    className="w-3 h-3 rounded-full" 
                    style={{ backgroundColor: entry.color }}
                  />
                  <span className="text-sm">{entry.name}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Data Quality Score */}
        <Card className="card-success">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="w-5 h-5" />
              Data Quality Score
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center space-y-4">
              <div className="relative w-32 h-32 mx-auto">
                <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 100 100">
                  <circle
                    cx="50"
                    cy="50"
                    r="40"
                    stroke="var(--color-muted)"
                    strokeWidth="8"
                    fill="none"
                  />
                  <circle
                    cx="50"
                    cy="50"
                    r="40"
                    stroke="var(--success-primary)"
                    strokeWidth="8"
                    fill="none"
                    strokeLinecap="round"
                    strokeDasharray={`${2 * Math.PI * 40 * 0.92} ${2 * Math.PI * 40}`}
                  />
                </svg>
                <div className="absolute inset-0 flex items-center justify-center">
                  <span className="text-2xl font-bold text-success-primary">92%</span>
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Completeness</span>
                  <span className="text-success-primary">95%</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Accuracy</span>
                  <span className="text-success-primary">89%</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Consistency</span>
                  <span className="text-success-primary">92%</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <Card className="card-data">
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[
              { action: 'File processed', file: 'user_analytics.xlsx', time: '2 minutes ago', status: 'success' },
              { action: 'Upload started', file: 'product_catalog.json', time: '5 minutes ago', status: 'processing' },
              { action: 'SQL query executed', file: 'sample_data.csv', time: '10 minutes ago', status: 'success' },
              { action: 'Processing failed', file: 'corrupted_file.csv', time: '15 minutes ago', status: 'error' },
            ].map((activity, index) => (
              <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-muted/30">
                <div className="flex items-center gap-3">
                  <div className={`w-2 h-2 rounded-full status-${activity.status}`} />
                  <div>
                    <p className="font-medium">{activity.action}</p>
                    <p className="text-sm text-muted-foreground">{activity.file}</p>
                  </div>
                </div>
                <span className="text-sm text-muted-foreground">{activity.time}</span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};