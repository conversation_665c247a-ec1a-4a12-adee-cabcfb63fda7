import React, { useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Upload, FileText, Loader2 } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { FileType } from '../enums';
import { formatFileSize } from '../utils/formatters';

interface FileUploadZoneProps {
  onFileUpload: (files: File[]) => void;
  acceptedTypes: string[];
  maxSize: number;
  isProcessing: boolean;
  className?: string;
}

export const FileUploadZone: React.FC<FileUploadZoneProps> = ({
  onFileUpload,
  acceptedTypes,
  maxSize,
  isProcessing,
  className
}) => {
  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles.length > 0) {
      onFileUpload(acceptedFiles);
    }
  }, [onFileUpload]);

  const { getRootProps, getInputProps, isDragActive, fileRejections } = useDropzone({
    onDrop,
    accept: {
      'text/csv': ['.csv'],
      'text/tab-separated-values': ['.tsv'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'application/vnd.ms-excel': ['.xls'],
      'application/json': ['.json'],
      'text/plain': ['.txt'],
    },
    multiple: false,
    maxSize: maxSize * 1024 * 1024, // Convert MB to bytes
  });

  const hasErrors = fileRejections.length > 0;

  return (
    <Card className={`card-data ${className || ''}`}>
      <CardContent className="p-8">
        <div
          {...getRootProps()}
          className={`
            border-2 border-dashed rounded-lg p-12 text-center transition-all cursor-pointer
            ${isDragActive 
              ? 'border-data-primary bg-data-primary-bg scale-105' 
              : hasErrors
                ? 'border-error-primary bg-error-bg'
                : 'border-muted-foreground/25 hover:border-data-primary/50 hover:bg-data-primary-bg/20'
            }
          `}
        >
          <input {...getInputProps()} />
          
          <div className="flex flex-col items-center gap-4">
            {isProcessing ? (
              <>
                <div className="p-4 bg-data-primary/10 rounded-full">
                  <Loader2 className="w-8 h-8 text-data-primary animate-spin" />
                </div>
                <div>
                  <p className="text-lg font-medium mb-2">Processing file...</p>
                  <p className="text-muted-foreground text-sm">
                    Please wait while we analyze your data
                  </p>
                </div>
              </>
            ) : (
              <>
                <div className={`p-4 rounded-full ${hasErrors ? 'bg-error-primary/10' : 'bg-data-primary/10'}`}>
                  <Upload className={`w-8 h-8 ${hasErrors ? 'text-error-primary' : 'text-data-primary'}`} />
                </div>
                
                <div>
                  <p className="text-lg font-medium mb-2">
                    {isDragActive 
                      ? 'Drop your file here' 
                      : hasErrors 
                        ? 'File upload failed'
                        : 'Upload your data file'
                    }
                  </p>
                  <p className="text-muted-foreground text-sm">
                    {hasErrors 
                      ? fileRejections[0]?.errors[0]?.message
                      : `Supports CSV, Excel, JSON, and text files up to ${maxSize}MB`
                    }
                  </p>
                </div>

                <Button 
                  variant="default" 
                  className="mt-4 btn-data"
                  disabled={isProcessing}
                >
                  <FileText className="w-4 h-4 mr-2" />
                  Choose File
                </Button>

                <div className="flex flex-wrap gap-2 mt-4">
                  <Badge variant="secondary" className="badge-data">CSV</Badge>
                  <Badge variant="secondary" className="badge-data">Excel</Badge>
                  <Badge variant="secondary" className="badge-data">JSON</Badge>
                  <Badge variant="secondary" className="badge-data">TXT</Badge>
                </div>
              </>
            )}
          </div>
        </div>

        {/* File size and type info */}
        <div className="mt-6 text-center">
          <p className="text-xs text-muted-foreground">
            Maximum file size: {formatFileSize(maxSize * 1024 * 1024)}
          </p>
        </div>
      </CardContent>
    </Card>
  );
};