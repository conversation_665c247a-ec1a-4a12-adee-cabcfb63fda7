import React from 'react';
import { 
  Upload,
  LayoutGrid,
  Database,
  Terminal,
  Settings,
  Search,
  PanelLeft,
  PanelLeftClose
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ViewType } from '../enums';
import { formatViewLabel } from '../utils/formatters';

interface NavigationBarProps {
  activeView: ViewType;
  onViewChange: (view: ViewType) => void;
  sidebarCollapsed: boolean;
  onToggleSidebar: () => void;
  onToggleControlPanel: () => void;
  className?: string;
}

const viewConfig = [
  { id: ViewType.UPLOAD, label: 'Upload', icon: Upload },
  { id: ViewType.DASHBOARD, label: 'Dashboard', icon: LayoutGrid },
  { id: ViewType.GRID, label: 'Data Grid', icon: LayoutGrid },
  { id: ViewType.SQL, label: 'SQL', icon: Database },
  { id: ViewType.TERMINAL, label: 'Terminal', icon: Terminal },
];

export const NavigationBar: React.FC<NavigationBarProps> = ({
  activeView,
  onViewChange,
  sidebarCollapsed,
  onToggleSidebar,
  onToggleControlPanel,
  className
}) => {
  return (
    <div className={`glass-nav border-b ${className || ''}`}>
      <div className="flex h-14 items-center px-4">
        <div className="flex items-center gap-4">
          {/* Sidebar Toggle */}
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggleSidebar}
            className="interactive-scale"
          >
            {sidebarCollapsed ? (
              <PanelLeft className="w-4 h-4" />
            ) : (
              <PanelLeftClose className="w-4 h-4" />
            )}
          </Button>
          
          {/* Navigation Tabs */}
          <nav className="flex items-center space-x-1">
            {viewConfig.map(({ id, label, icon: Icon }) => (
              <Button
                key={id}
                variant={activeView === id ? 'default' : 'ghost'}
                size="sm"
                onClick={() => onViewChange(id)}
                className={`flex items-center gap-2 transition-all ${
                  activeView === id 
                    ? 'btn-data shadow-md' 
                    : 'hover:bg-data-primary/10'
                }`}
              >
                <Icon className="w-4 h-4" />
                <span className="hidden sm:inline">{label}</span>
                {activeView === id && (
                  <Badge variant="secondary" className="ml-1 badge-data">
                    Active
                  </Badge>
                )}
              </Button>
            ))}
          </nav>
        </div>
        
        {/* Right Side Controls */}
        <div className="ml-auto flex items-center gap-2">
          {/* Global Search */}
          <div className="relative hidden md:block">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            <Input
              placeholder="Search files, data, queries..."
              className="pl-10 w-64 bg-muted/30 border-muted focus:bg-background"
            />
          </div>
          
          {/* Mobile Search Button */}
          <Button variant="ghost" size="sm" className="md:hidden">
            <Search className="w-4 h-4" />
          </Button>
          
          {/* Settings */}
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={onToggleControlPanel}
            className="interactive-scale"
            title="Settings & Control Panel"
          >
            <Settings className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};