import React, { useState, useEffect, useCallback, useRef } from 'react';

// Lazy loading hook for large datasets
export const useLazyLoading = <T,>(
  loadData: (start: number, count: number) => Promise<{ data: T[]; total: number }>,
  initialLoadCount = 50,
  bufferSize = 20
) => {
  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [totalItems, setTotalItems] = useState<number>(0);
  const [page, setPage] = useState(0);
  
  const isLoadingRef = useRef(false);

  const loadDataChunk = async (start: number, count: number, append = false) => {
    if (isLoadingRef.current) return;
    
    try {
      isLoadingRef.current = true;
      if (append) {
        setLoadingMore(true);
      } else {
        setLoading(true);
      }
      
      const result = await loadData(start, count);
      
      if (append) {
        setData(prev => [...prev, ...result.data]);
      } else {
        setData(result.data);
      }
      
      setTotalItems(result.total);
      
      if (result.data.length < count || data.length + result.data.length >= result.total) {
        setHasMore(false);
      } else {
        setHasMore(true);
      }
      
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load data');
      setHasMore(false);
    } finally {
      isLoadingRef.current = false;
      setLoading(false);
      setLoadingMore(false);
    }
  };

  const loadMore = useCallback(() => {
    if (!loading && !loadingMore && hasMore) {
      loadDataChunk(data.length, initialLoadCount, true);
      setPage(prev => prev + 1);
    }
  }, [data.length, hasMore, loading, loadingMore, initialLoadCount, loadData]);

  const reset = useCallback(() => {
    setData([]);
    setHasMore(true);
    setError(null);
    setPage(0);
    loadDataChunk(0, initialLoadCount, false);
  }, [initialLoadCount, loadData]);

  const refresh = useCallback(() => {
    reset();
  }, [reset]);

  const loadPage = useCallback((pageNumber: number) => {
    const start = pageNumber * initialLoadCount;
    loadDataChunk(start, initialLoadCount, false);
    setPage(pageNumber);
  }, [initialLoadCount, loadData]);

  useEffect(() => {
    reset();
  }, [reset]);

  return {
    data,
    loading,
    loadingMore,
    hasMore,
    error,
    totalItems,
    page,
    loadMore,
    reset,
    refresh,
    loadPage,
    isLoading: loading || loadingMore
  };
};

// Infinite scroll hook
export const useInfiniteScroll = (
  loadMore: () => void,
  hasMore: boolean,
  isLoading: boolean
) => {
  const observer = useRef<IntersectionObserver | null>(null);
  const sentinelRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!hasMore || isLoading) return;

    const element = sentinelRef.current;
    if (!element) return;

    // Create observer if it doesn't exist
    if (!observer.current) {
      observer.current = new IntersectionObserver(
        (entries) => {
          if (entries[0].isIntersecting) {
            loadMore();
          }
        },
        { threshold: 1.0 }
      );
    }

    observer.current.observe(element);

    return () => {
      if (observer.current) {
        observer.current.unobserve(element);
      }
    };
  }, [loadMore, hasMore, isLoading]);

  return sentinelRef;
};

// Lazy data loader component
export interface LazyDataLoaderProps<T> {
  loadData: (start: number, count: number) => Promise<{ data: T[]; total: number }>;
  renderItem: (item: T, index: number) => React.ReactNode;
  initialLoadCount?: number;
  bufferSize?: number;
  className?: string;
  loadingComponent?: React.ReactNode;
  errorComponent?: (error: string) => React.ReactNode;
  noMoreDataComponent?: React.ReactNode;
  endMessage?: string;
}

export const LazyDataLoader = <T,>({
  loadData,
  renderItem,
  initialLoadCount = 50,
  bufferSize = 20,
  className = '',
  loadingComponent = <div className="p-4 text-center">Loading...</div>,
  errorComponent = (error) => <div className="p-4 text-center text-red-500">Error: {error}</div>,
  noMoreDataComponent = <div className="p-4 text-center text-muted-foreground">No more data to load</div>,
  endMessage = "You've reached the end"
}: LazyDataLoaderProps<T>) => {
  const {
    data,
    loading,
    loadingMore,
    hasMore,
    error,
    loadMore,
    isLoading
  } = useLazyLoading(loadData, initialLoadCount, bufferSize);
  
  const sentinelRef = useInfiniteScroll(loadMore, hasMore, isLoading);

  if (loading && data.length === 0) {
    return <div className={className}>{loadingComponent}</div>;
  }

  if (error) {
    return <div className={className}>{errorComponent(error)}</div>;
  }

  return (
    <div className={className}>
      {data.map((item, index) => (
        <div key={index}>
          {renderItem(item, index)}
        </div>
      ))}
      
      {loadingMore && (
        <div className="p-4 text-center">
          {loadingComponent}
        </div>
      )}
      
      {hasMore && (
        <div ref={sentinelRef} className="h-1" />
      )}
      
      {!hasMore && data.length > 0 && (
        <div className="p-4 text-center text-muted-foreground">
          {endMessage}
        </div>
      )}
    </div>
  );
};

// Paginated data loader component
export interface PaginatedDataLoaderProps<T> {
  loadData: (page: number, pageSize: number) => Promise<{ data: T[]; total: number; totalPages: number }>;
  renderItem: (item: T, index: number) => React.ReactNode;
  pageSize?: number;
  className?: string;
  loadingComponent?: React.ReactNode;
  errorComponent?: (error: string) => React.ReactNode;
  paginationComponent?: (props: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    onPageChange: (page: number) => void;
    hasNext: boolean;
    hasPrev: boolean;
  }) => React.ReactNode;
}

export const PaginatedDataLoader = <T,>({
  loadData,
  renderItem,
  pageSize = 20,
  className = '',
  loadingComponent = <div className="p-4 text-center">Loading...</div>,
  errorComponent = (error) => <div className="p-4 text-center text-red-500">Error: {error}</div>,
  paginationComponent
}: PaginatedDataLoaderProps<T>) => {
  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(0);

  const loadDataPage = useCallback(async (page: number) => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await loadData(page, pageSize);
      setData(result.data);
      setTotalItems(result.total);
      setTotalPages(result.totalPages);
      setCurrentPage(page);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load data');
    } finally {
      setLoading(false);
    }
  }, [loadData, pageSize]);

  const handlePageChange = useCallback((page: number) => {
    loadDataPage(page);
  }, [loadDataPage]);

  useEffect(() => {
    loadDataPage(1);
  }, [loadDataPage]);

  if (loading && data.length === 0) {
    return <div className={className}>{loadingComponent}</div>;
  }

  if (error) {
    return <div className={className}>{errorComponent(error)}</div>;
  }

  const hasNext = currentPage < totalPages;
  const hasPrev = currentPage > 1;

  return (
    <div className={className}>
      <div>
        {data.map((item, index) => (
          <div key={index}>
            {renderItem(item, (currentPage - 1) * pageSize + index)}
          </div>
        ))}
      </div>
      
      {paginationComponent ? (
        paginationComponent({
          currentPage,
          totalPages,
          totalItems,
          onPageChange: handlePageChange,
          hasNext,
          hasPrev
        })
      ) : (
        <div className="flex items-center justify-between p-4 border-t">
          <div className="text-sm text-muted-foreground">
            Showing {(currentPage - 1) * pageSize + 1} to {Math.min(currentPage * pageSize, totalItems)} of {totalItems} entries
          </div>
          <div className="flex gap-2">
            <button
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={!hasPrev}
              className="px-3 py-1 text-sm border rounded disabled:opacity-50"
            >
              Previous
            </button>
            <span className="px-3 py-1 text-sm">
              Page {currentPage} of {totalPages}
            </span>
            <button
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={!hasNext}
              className="px-3 py-1 text-sm border rounded disabled:opacity-50"
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

// Export hooks and components
export default LazyDataLoader;