import React, { useState, useEffect } from 'react';
import { 
  Play, Database, Cpu, Activity, Settings, 
  BarChart3, Zap, Filter, <PERSON>freshCw, Trash2,
  FileText, FileSpreadsheet, FileJson
} from 'lucide-react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useDataCenter } from '../DataCenterContext';
import { 
  VirtualizedTable, 
  usePerformanceMonitor, 
  PerformanceOptimizer,
  backgroundProcessor,
  pythonBackgroundProcessor,
  useLazyLoading
} from './index';

// Demo data generator
const generateDemoData = (count: number) => {
  return Array.from({ length: count }, (_, i) => ({
    id: i + 1,
    name: `Record ${i + 1}`,
    category: ['A', 'B', 'C', 'D'][Math.floor(Math.random() * 4)],
    value: Math.floor(Math.random() * 1000),
    status: ['active', 'pending', 'completed'][Math.floor(Math.random() * 3)],
    date: new Date(Date.now() - Math.floor(Math.random() * 365) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    description: `This is a sample description for record ${i + 1}. It contains some detailed information about the data entry.`
  }));
};

// Performance demo component
export const PerformanceIntegrationDemo: React.FC = () => {
  const { 
    pipelineState, 
    updatePipelineState,
    enableCaching,
    enableVirtualization,
    updateWorkerPoolSize,
    getCacheStats,
    clearCache
  } = useDataCenter();
  
  const { metrics, isMonitoring, startMonitoring, stopMonitoring } = usePerformanceMonitor();
  const [demoData, setDemoData] = useState<any[]>([]);
  const [processingTaskId, setProcessingTaskId] = useState<string | null>(null);
  const [pythonTaskId, setPythonTaskId] = useState<string | null>(null);
  const [virtualizationEnabled, setVirtualizationEnabled] = useState(true);
  const [cachingEnabled, setCachingEnabled] = useState(true);
  
  // Generate demo data
  useEffect(() => {
    const data = generateDemoData(10000);
    setDemoData(data);
  }, []);

  // Lazy loading for large datasets
  const { data: paginatedData, loading, loadMore, hasMore } = useLazyLoading(
    () => Promise.resolve({ data: demoData, total: demoData.length }),
    100
  );

  // Handle background processing
  const handleBackgroundProcessing = () => {
    const taskId = backgroundProcessor.addTask(
      async () => {
        // Simulate heavy processing
        for (let i = 0; i <= 100; i += 10) {
          await new Promise(resolve => setTimeout(resolve, 100));
          backgroundProcessor.updateTaskProgress(taskId!, i, `Processing ${i}%`);
        }
        return 'Processing completed successfully';
      },
      1,
      (progress, message) => {
        console.log(`Progress: ${progress}% - ${message}`);
      },
      (result) => {
        console.log('Processing completed:', result);
        setProcessingTaskId(null);
      },
      (error) => {
        console.error('Processing failed:', error);
        setProcessingTaskId(null);
      }
    );
    
    setProcessingTaskId(taskId);
  };

  // Handle Python background processing
  const handlePythonProcessing = () => {
    const script = `
import pandas as pd
import numpy as np

# Generate sample data analysis
data = pd.DataFrame({
    'id': range(1000),
    'value': np.random.randn(1000),
    'category': np.random.choice(['A', 'B', 'C'], 1000)
})

# Perform analysis
summary = {
    'total_records': len(data),
    'mean_value': data['value'].mean(),
    'std_value': data['value'].std(),
    'category_counts': data['category'].value_counts().to_dict()
}

print(f"Analysis complete: {summary}")
`;
    
    const taskId = pythonBackgroundProcessor.executePythonScript(
      script,
      demoData.slice(0, 100),
      (progress, message) => {
        console.log(`Python Progress: ${progress}% - ${message}`);
      },
      (result) => {
        console.log('Python processing completed:', result);
        setPythonTaskId(null);
      },
      (error) => {
        console.error('Python processing failed:', error);
        setPythonTaskId(null);
      }
    );
    
    setPythonTaskId(taskId);
  };

  // Toggle virtualization
  const toggleVirtualization = () => {
    const newValue = !virtualizationEnabled;
    setVirtualizationEnabled(newValue);
    enableVirtualization(newValue);
    updatePipelineState({
      performance: {
        ...pipelineState.performance,
        virtualizationEnabled: newValue
      }
    });
  };

  // Toggle caching
  const toggleCaching = () => {
    const newValue = !cachingEnabled;
    setCachingEnabled(newValue);
    enableCaching(newValue);
    updatePipelineState({
      performance: {
        ...pipelineState.performance,
        cacheEnabled: newValue
      }
    });
  };

  // Get current cache stats
  const cacheStats = getCacheStats();

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold flex items-center gap-2">
          <Zap className="w-6 h-6 text-yellow-500" />
          Performance Optimization Demo
        </h2>
        <div className="flex gap-2">
          <Button 
            onClick={isMonitoring ? stopMonitoring : startMonitoring}
            variant="outline"
            size="sm"
          >
            {isMonitoring ? 'Stop Monitoring' : 'Start Monitoring'}
          </Button>
          <Button 
            onClick={clearCache}
            variant="outline"
            size="sm"
          >
            <Trash2 className="w-4 h-4 mr-2" />
            Clear Cache
          </Button>
        </div>
      </div>

      {/* Performance Metrics */}
      <Card className="p-4">
        <h3 className="font-semibold mb-3 flex items-center gap-2">
          <Activity className="w-4 h-4" />
          Performance Metrics
        </h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-muted/50 p-3 rounded">
            <p className="text-xs text-muted-foreground">Render Time</p>
            <p className="text-lg font-bold">{metrics.renderTime.toFixed(2)}ms</p>
          </div>
          <div className="bg-muted/50 p-3 rounded">
            <p className="text-xs text-muted-foreground">Memory Usage</p>
            <p className="text-lg font-bold">{metrics.memoryUsage}MB</p>
          </div>
          <div className="bg-muted/50 p-3 rounded">
            <p className="text-xs text-muted-foreground">FPS</p>
            <p className="text-lg font-bold">{metrics.fps}</p>
          </div>
          <div className="bg-muted/50 p-3 rounded">
            <p className="text-xs text-muted-foreground">Cache Hit Rate</p>
            <p className="text-lg font-bold">{(cacheStats.hitRate * 100).toFixed(1)}%</p>
          </div>
        </div>
      </Card>

      {/* Performance Controls */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="p-4">
          <h3 className="font-semibold mb-3 flex items-center gap-2">
            <Settings className="w-4 h-4" />
            Optimization Settings
          </h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span>Virtualization</span>
              <Button 
                onClick={toggleVirtualization}
                variant="outline"
                size="sm"
                className={virtualizationEnabled ? 'bg-green-500/20 text-green-700' : 'bg-red-500/20 text-red-700'}
              >
                {virtualizationEnabled ? 'Enabled' : 'Disabled'}
              </Button>
            </div>
            <div className="flex items-center justify-between">
              <span>Caching</span>
              <Button 
                onClick={toggleCaching}
                variant="outline"
                size="sm"
                className={cachingEnabled ? 'bg-green-500/20 text-green-700' : 'bg-red-500/20 text-red-700'}
              >
                {cachingEnabled ? 'Enabled' : 'Disabled'}
              </Button>
            </div>
            <div className="flex items-center justify-between">
              <span>Worker Pool Size</span>
              <div className="flex gap-2">
                <Button 
                  onClick={() => updateWorkerPoolSize(Math.max(1, pipelineState.performance.workerPoolSize - 1))}
                  variant="outline"
                  size="sm"
                >
                  -
                </Button>
                <span className="px-3 py-1 bg-muted rounded">
                  {pipelineState.performance.workerPoolSize}
                </span>
                <Button 
                  onClick={() => updateWorkerPoolSize(Math.min(10, pipelineState.performance.workerPoolSize + 1))}
                  variant="outline"
                  size="sm"
                >
                  +
                </Button>
              </div>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <h3 className="font-semibold mb-3 flex items-center gap-2">
            <Cpu className="w-4 h-4" />
            Background Processing
          </h3>
          <div className="space-y-3">
            <Button 
              onClick={handleBackgroundProcessing}
              disabled={!!processingTaskId}
              className="w-full"
            >
              <Play className="w-4 h-4 mr-2" />
              {processingTaskId ? 'Processing...' : 'Run Heavy Task'}
            </Button>
            <Button 
              onClick={handlePythonProcessing}
              disabled={!!pythonTaskId}
              className="w-full"
            >
              <FileText className="w-4 h-4 mr-2" />
              {pythonTaskId ? 'Running Python...' : 'Run Python Analysis'}
            </Button>
          </div>
        </Card>

        <Card className="p-4">
          <h3 className="font-semibold mb-3 flex items-center gap-2">
            <Database className="w-4 h-4" />
            Cache Statistics
          </h3>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Hit Rate:</span>
              <span className="font-mono">{(cacheStats.hitRate * 100).toFixed(1)}%</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>Miss Rate:</span>
              <span className="font-mono">{(cacheStats.missRate * 100).toFixed(1)}%</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>Cache Size:</span>
              <span className="font-mono">{cacheStats.cacheSize} items</span>
            </div>
          </div>
        </Card>
      </div>

      {/* Virtualized Data Table */}
      <Card className="p-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="font-semibold flex items-center gap-2">
            <BarChart3 className="w-4 h-4" />
            Virtualized Data Table
          </h3>
          <Badge variant="outline">
            {demoData.length.toLocaleString()} records
          </Badge>
        </div>
        
        {virtualizationEnabled ? (
          <VirtualizedTable
            data={demoData}
            columns={[
              { key: 'id', header: 'ID', width: 80 },
              { key: 'name', header: 'Name' },
              { key: 'category', header: 'Category', width: 100 },
              { key: 'value', header: 'Value', width: 100 },
              { key: 'status', header: 'Status', width: 120 },
              { key: 'date', header: 'Date', width: 120 }
            ]}
            rowHeight={40}
            windowHeight={400}
            className="border rounded-lg"
          />
        ) : (
          <div className="text-center py-8 text-muted-foreground">
            Virtualization is disabled. Enable it to see the virtualized table.
          </div>
        )}
      </Card>

      {/* Lazy Loading Demo */}
      <Card className="p-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="font-semibold flex items-center gap-2">
            <RefreshCw className="w-4 h-4" />
            Lazy Loading Demo
          </h3>
          <Badge variant="outline">
            {paginatedData.length} loaded
          </Badge>
        </div>
        
        <div className="space-y-2 max-h-60 overflow-y-auto">
          {paginatedData.slice(0, 20).map((item: any) => (
            <div key={item.id} className="flex items-center gap-3 p-2 border-b">
              <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                <FileSpreadsheet className="w-4 h-4 text-primary" />
              </div>
              <div className="flex-1">
                <p className="font-medium">{item.name}</p>
                <p className="text-sm text-muted-foreground">{item.description}</p>
              </div>
              <div className="flex gap-2">
                <Badge variant="outline">{item.category}</Badge>
                <Badge variant="outline">{item.status}</Badge>
              </div>
            </div>
          ))}
        </div>
        
        {hasMore && (
          <div className="mt-4 text-center">
            <Button 
              onClick={loadMore}
              disabled={loading}
              variant="outline"
            >
              {loading ? 'Loading...' : 'Load More'}
            </Button>
          </div>
        )}
      </Card>
    </div>
  );
};

export default PerformanceIntegrationDemo;