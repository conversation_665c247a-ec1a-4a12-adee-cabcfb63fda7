# Data Center Performance Optimizations

This directory contains performance optimization components for the Data Center application. These components are designed to improve the performance and user experience when working with large datasets and complex processing operations.

## Components Overview

### 1. Web Workers (`WebWorkerManager.ts`)
- **Purpose**: Offload heavy processing to background threads
- **Features**:
  - Worker pool management
  - Task queuing and prioritization
  - Progress tracking
  - Error handling
  - Automatic cleanup

### 2. Virtualization (`VirtualizedTable.tsx`)
- **Purpose**: Efficient rendering of large datasets
- **Features**:
  - Virtual scrolling for tables and lists
  - Dynamic row rendering
  - Memory-efficient display
  - Customizable row heights
  - Pagination support

### 3. Caching (`DataCache.ts`)
- **Purpose**: Reduce redundant computations and data fetching
- **Features**:
  - Multi-level caching (memory, persistent)
  - TTL (Time To Live) support
  - Cache statistics and monitoring
  - Automatic cleanup of expired entries
  - Size-limited caches

### 4. Performance Monitoring (`PerformanceOptimizer.tsx`)
- **Purpose**: Track and optimize application performance
- **Features**:
  - Render time measurement
  - Memory usage tracking
  - FPS monitoring
  - React optimization hooks
  - Performance-aware components

### 5. Background Processing (`BackgroundProcessor.ts`)
- **Purpose**: Handle long-running operations without blocking UI
- **Features**:
  - Task queue management
  - Progress updates
  - Cancellation support
  - Python script execution
  - Result handling

### 6. Lazy Loading (`LazyDataLoader.tsx`)
- **Purpose**: Load data on-demand to reduce initial load time
- **Features**:
  - Infinite scrolling support
  - Pagination helpers
  - Loading state management
  - Error handling
  - Customizable chunk sizes

## Integration with DataCenter

The performance components are integrated with the DataCenter through the `DataCenterContext`. The context provides:

- Performance state management
- Caching controls
- Virtualization toggles
- Worker pool configuration
- Performance metrics access

## Usage Examples

### Enabling Caching
```typescript
const { enableCaching } = useDataCenter();
enableCaching(true);
```

### Using Virtualized Tables
```tsx
<VirtualizedTable
  data={largeDataset}
  columns={tableColumns}
  rowHeight={40}
  windowHeight={600}
/>
```

### Background Processing
```typescript
const { backgroundProcessor } = useDataCenter();
const taskId = backgroundProcessor.addTask(
  heavyProcessingFunction,
  priority,
  onProgress,
  onComplete,
  onError
);
```

### Performance Monitoring
```typescript
const { usePerformanceMonitor } = useDataCenter();
const { metrics, startMonitoring, stopMonitoring } = usePerformanceMonitor();
```

## Best Practices

1. **Enable virtualization for large datasets** (>1000 rows)
2. **Use caching for expensive operations** that are repeated
3. **Offload heavy processing** to background workers
4. **Implement lazy loading** for paginated data
5. **Monitor performance metrics** to identify bottlenecks
6. **Clean up resources** when components unmount

## Configuration

Performance optimizations can be configured through the DataCenter UI or programmatically:

- Toggle virtualization on/off
- Adjust worker pool size (1-10 workers)
- Enable/disable caching
- Set cache TTL values
- Configure lazy loading chunk sizes

For more detailed implementation, see the `PerformanceIntegrationDemo.tsx` component.