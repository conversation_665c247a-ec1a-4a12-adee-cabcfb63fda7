// Background task processing system
export class BackgroundProcessor {
  private tasks: Map<string, {
    task: () => Promise<any>;
    priority: number;
    status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
    progress: number;
    result?: any;
    error?: string;
    createdAt: number;
    startedAt?: number;
    completedAt?: number;
  }> = new Map();
  
  private taskQueue: string[] = [];
  private activeWorkers: number = 0;
  private maxConcurrentTasks: number;
  private isProcessing: boolean = false;
  private progressCallbacks: Map<string, (progress: number, message?: string) => void> = new Map();
  private completionCallbacks: Map<string, (result: any) => void> = new Map();
  private errorCallbacks: Map<string, (error: string) => void> = new Map();

  constructor(maxConcurrentTasks: number = 3) {
    this.maxConcurrentTasks = maxConcurrentTasks;
  }

  // Add a task to the queue
  addTask<T>(
    task: () => Promise<T>,
    priority: number = 0,
    onProgress?: (progress: number, message?: string) => void,
    onComplete?: (result: T) => void,
    onError?: (error: string) => void
  ): string {
    const taskId = Math.random().toString(36).substr(2, 9);
    
    this.tasks.set(taskId, {
      task,
      priority,
      status: 'pending',
      progress: 0,
      createdAt: Date.now()
    });
    
    // Add to queue sorted by priority (higher priority first)
    this.taskQueue.push(taskId);
    this.taskQueue.sort((a, b) => {
      const taskA = this.tasks.get(a);
      const taskB = this.tasks.get(b);
      return (taskB?.priority || 0) - (taskA?.priority || 0);
    });
    
    // Store callbacks
    if (onProgress) this.progressCallbacks.set(taskId, onProgress);
    if (onComplete) this.completionCallbacks.set(taskId, onComplete);
    if (onError) this.errorCallbacks.set(taskId, onError);
    
    // Start processing if not already running
    if (!this.isProcessing) {
      this.startProcessing();
    }
    
    return taskId;
  }

  // Start processing tasks
  private async startProcessing(): Promise<void> {
    if (this.isProcessing) return;
    
    this.isProcessing = true;
    
    while (this.taskQueue.length > 0 && this.activeWorkers < this.maxConcurrentTasks) {
      await this.processNextTask();
    }
    
    this.isProcessing = false;
  }

  // Process the next task in the queue
  private async processNextTask(): Promise<void> {
    if (this.taskQueue.length === 0 || this.activeWorkers >= this.maxConcurrentTasks) {
      return;
    }
    
    const taskId = this.taskQueue.shift();
    if (!taskId) return;
    
    const taskEntry = this.tasks.get(taskId);
    if (!taskEntry || taskEntry.status !== 'pending') {
      return;
    }
    
    // Update task status
    taskEntry.status = 'running';
    taskEntry.startedAt = Date.now();
    this.activeWorkers++;
    
    try {
      // Execute the task
      const result = await taskEntry.task();
      
      // Update task status
      taskEntry.status = 'completed';
      taskEntry.result = result;
      taskEntry.completedAt = Date.now();
      taskEntry.progress = 100;
      
      // Call completion callback
      const onComplete = this.completionCallbacks.get(taskId);
      if (onComplete) {
        onComplete(result);
      }
      
      // Clean up callbacks
      this.progressCallbacks.delete(taskId);
      this.completionCallbacks.delete(taskId);
      this.errorCallbacks.delete(taskId);
    } catch (error) {
      // Update task status
      taskEntry.status = 'failed';
      taskEntry.error = error instanceof Error ? error.message : String(error);
      taskEntry.completedAt = Date.now();
      
      // Call error callback
      const onError = this.errorCallbacks.get(taskId);
      if (onError) {
        onError(taskEntry.error);
      }
      
      // Clean up callbacks
      this.progressCallbacks.delete(taskId);
      this.completionCallbacks.delete(taskId);
      this.errorCallbacks.delete(taskId);
    } finally {
      this.activeWorkers--;
      
      // Process next task if available
      if (this.taskQueue.length > 0) {
        setTimeout(() => this.processNextTask(), 0);
      }
    }
  }

  // Cancel a task
  cancelTask(taskId: string): boolean {
    const taskEntry = this.tasks.get(taskId);
    if (!taskEntry) return false;
    
    if (taskEntry.status === 'pending') {
      // Remove from queue
      const index = this.taskQueue.indexOf(taskId);
      if (index > -1) {
        this.taskQueue.splice(index, 1);
      }
      taskEntry.status = 'cancelled';
      return true;
    } else if (taskEntry.status === 'running') {
      // Cannot cancel running tasks
      return false;
    }
    
    return false;
  }

  // Get task status
  getTaskStatus(taskId: string): {
    status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
    progress: number;
    result?: any;
    error?: string;
    createdAt: number;
    startedAt?: number;
    completedAt?: number;
  } | null {
    const taskEntry = this.tasks.get(taskId);
    if (!taskEntry) return null;
    
    return {
      status: taskEntry.status,
      progress: taskEntry.progress,
      result: taskEntry.result,
      error: taskEntry.error,
      createdAt: taskEntry.createdAt,
      startedAt: taskEntry.startedAt,
      completedAt: taskEntry.completedAt
    };
  }

  // Update task progress
  updateTaskProgress(taskId: string, progress: number, message?: string): void {
    const taskEntry = this.tasks.get(taskId);
    if (!taskEntry) return;
    
    taskEntry.progress = Math.max(0, Math.min(100, progress));
    
    // Call progress callback
    const onProgress = this.progressCallbacks.get(taskId);
    if (onProgress) {
      onProgress(progress, message);
    }
  }

  // Get all tasks
  getAllTasks(): Array<{
    id: string;
    status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
    priority: number;
    progress: number;
    createdAt: number;
  }> {
    return Array.from(this.tasks.entries()).map(([id, task]) => ({
      id,
      status: task.status,
      priority: task.priority,
      progress: task.progress,
      createdAt: task.createdAt
    }));
  }

  // Get queue statistics
  getQueueStats(): {
    pending: number;
    running: number;
    completed: number;
    failed: number;
    cancelled: number;
    total: number;
  } {
    const stats = {
      pending: 0,
      running: 0,
      completed: 0,
      failed: 0,
      cancelled: 0,
      total: this.tasks.size
    };
    
    for (const task of this.tasks.values()) {
      stats[task.status]++;
    }
    
    return stats;
  }

  // Clear completed tasks
  clearCompletedTasks(): number {
    let clearedCount = 0;
    
    for (const [taskId, task] of this.tasks.entries()) {
      if (task.status === 'completed' || task.status === 'failed' || task.status === 'cancelled') {
        this.tasks.delete(taskId);
        clearedCount++;
      }
    }
    
    return clearedCount;
  }

  // Stop all processing
  stop(): void {
    this.isProcessing = false;
    this.taskQueue = [];
    this.activeWorkers = 0;
    
    // Cancel all pending tasks
    for (const [taskId, task] of this.tasks.entries()) {
      if (task.status === 'pending') {
        task.status = 'cancelled';
      }
    }
  }

  // Resume processing
  resume(): void {
    if (this.taskQueue.length > 0) {
      this.startProcessing();
    }
  }
}

// Specialized background processor for Python script execution
export class PythonBackgroundProcessor extends BackgroundProcessor {
  constructor(maxConcurrentTasks: number = 2) {
    super(maxConcurrentTasks);
  }

  // Execute Python script in background
  executePythonScript(
    script: string,
    data?: any,
    onProgress?: (progress: number, message?: string) => void,
    onComplete?: (result: string) => void,
    onError?: (error: string) => void
  ): string {
    const taskId = this.addTask<string>(
      async () => {
        // In a real implementation, this would call the actual Python execution
        // For now, we'll simulate the process
        
        // Simulate progress updates
        for (let i = 0; i <= 10; i++) {
          await new Promise(resolve => setTimeout(resolve, 200));
          this.updateTaskProgress(taskId, i * 10, `Processing step ${i + 1}/11`);
        }
        
        // Simulate result
        return `-- Generated SQL from Python script\n${script}\n-- Processed ${data?.length || 0} rows`;
      },
      1, // Default priority
      onProgress,
      onComplete,
      onError
    );
    
    return taskId;
  }

  // Execute data processing task
  processData(
    data: any[],
    processor: (item: any) => any,
    onProgress?: (progress: number, message?: string) => void,
    onComplete?: (result: any[]) => void,
    onError?: (error: string) => void
  ): string {
    const taskId = this.addTask<any[]>(
      async () => {
        const results: any[] = [];
        
        for (let i = 0; i < data.length; i++) {
          try {
            const result = processor(data[i]);
            results.push(result);
            
            // Update progress every 10 items
            if (i % 10 === 0 || i === data.length - 1) {
              const progress = Math.round((i / data.length) * 100);
              this.updateTaskProgress(taskId, progress, `Processed ${i + 1}/${data.length} items`);
            }
            
            // Yield to event loop periodically
            if (i % 100 === 0) {
              await new Promise(resolve => setTimeout(resolve, 0));
            }
          } catch (error) {
            throw new Error(`Error processing item ${i}: ${error instanceof Error ? error.message : String(error)}`);
          }
        }
        
        return results;
      },
      0, // Lower priority
      onProgress,
      onComplete,
      onError
    );
    
    return taskId;
  }
}

// Singleton instances
export const backgroundProcessor = new BackgroundProcessor(3);
export const pythonBackgroundProcessor = new PythonBackgroundProcessor(2);