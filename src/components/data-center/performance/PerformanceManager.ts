// Unified Performance System for Data Center
import { 
  WebWorkerManager, 
  webWorkerManager
} from './WebWorkerManager';

import {
  VirtualizedTable,
  EnhancedVirtualizedTable
} from './VirtualizedTable';

import { 
  DataCache,
  ProcessedDataCache,
  PersistentCache,
  dataCache,
  processedDataCache,
  persistentCache
} from './DataCache';

import { 
  usePerformanceMonitor,
  PerformanceOptimizer,
  PerformanceMonitor,
  optimizedMemo,
  useOptimizedMemo,
  useOptimizedCallback,
  performanceOptimizer
} from './PerformanceOptimizer';

import { 
  BackgroundProcessor,
  PythonBackgroundProcessor,
  backgroundProcessor,
  pythonBackgroundProcessor
} from './BackgroundProcessor';

import { 
  LazyDataLoader,
  PaginatedDataLoader,
  useLazyLoading,
  useInfiniteScroll
} from './LazyDataLoader';



// Export all performance components in a unified system
export {
  // Core Performance Infrastructure
  WebWorkerManager,
  webWorkerManager,
  VirtualizedTable,
  EnhancedVirtualizedTable,
  
  // Caching System
  DataCache,
  ProcessedDataCache,
  PersistentCache,
  dataCache,
  processedDataCache,
  persistentCache,
  
  // Performance Monitoring and Optimization
  usePerformanceMonitor,
  PerformanceOptimizer,
  PerformanceMonitor,
  optimizedMemo,
  useOptimizedMemo,
  useOptimizedCallback,
  performanceOptimizer,
  
  // Background Processing
  BackgroundProcessor,
  PythonBackgroundProcessor,
  backgroundProcessor,
  pythonBackgroundProcessor,
  
  // Data Loading Optimization
  LazyDataLoader,
  PaginatedDataLoader,
  useLazyLoading,
  useInfiniteScroll,
  

};

// Unified performance interface
export interface PerformanceConfig {
  cacheEnabled: boolean;
  virtualizationEnabled: boolean;
  backgroundProcessing: boolean;
  workerPoolSize: number;
  lazyLoading: boolean;
  pagination: boolean;
}

export interface PerformanceStats {
  cacheHitRate: number;
  cacheMissRate: number;
  cacheSize: number;
  workerPoolSize: number;
  activeWorkers: number;
  pendingTasks: number;
  processedTasks: number;
  failedTasks: number;
}

// Performance management class
export class PerformanceManager {
  private config: PerformanceConfig;
  private stats: PerformanceStats;
  
  constructor(initialConfig?: Partial<PerformanceConfig>) {
    this.config = {
      cacheEnabled: true,
      virtualizationEnabled: true,
      backgroundProcessing: true,
      workerPoolSize: 3,
      lazyLoading: true,
      pagination: true,
      ...initialConfig
    };
    
    this.stats = {
      cacheHitRate: 0,
      cacheMissRate: 0,
      cacheSize: 0,
      workerPoolSize: this.config.workerPoolSize,
      activeWorkers: 0,
      pendingTasks: 0,
      processedTasks: 0,
      failedTasks: 0
    };
  }
  
  // Configuration methods
  updateConfig(newConfig: Partial<PerformanceConfig>) {
    this.config = { ...this.config, ...newConfig };
    return this.config;
  }
  
  getConfig(): PerformanceConfig {
    return { ...this.config };
  }
  
  // Stats methods
  updateStats(newStats: Partial<PerformanceStats>) {
    this.stats = { ...this.stats, ...newStats };
    return this.stats;
  }
  
  getStats(): PerformanceStats {
    return { ...this.stats };
  }
  
  // Cache management
  clearAllCaches() {
    dataCache.clear();
    processedDataCache.clear();
    persistentCache.clear();
    this.updateStats({ cacheSize: 0, cacheHitRate: 0, cacheMissRate: 0 });
  }
  
  // Worker management
  updateWorkerPoolSize(size: number) {
    // Note: WebWorkerManager pool size is set at initialization
    // This method updates the stats to reflect the desired pool size
    this.updateStats({ workerPoolSize: size });
  }
  
  // Performance monitoring
  startMonitoring() {
    // Start performance monitoring if not already started
    return true;
  }
  
  stopMonitoring() {
    // Stop performance monitoring
    return true;
  }
}

// Singleton instance
export const performanceManager = new PerformanceManager();

// Hook for using performance management in components
export const usePerformanceManager = () => {
  return performanceManager;
};