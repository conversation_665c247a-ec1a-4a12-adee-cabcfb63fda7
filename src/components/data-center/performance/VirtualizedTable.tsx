import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';

interface VirtualizedTableProps<T> {
  data: T[];
  columns: Array<{
    key: string;
    header: string;
    render?: (item: T, index: number) => React.ReactNode;
    width?: number;
  }>;
  rowHeight?: number;
  windowHeight?: number;
  onRowClick?: (item: T, index: number) => void;
  className?: string;
  onScroll?: (scrollTop: number) => void;
  loading?: boolean;
  skeletonRows?: number;
}

export const VirtualizedTable = <T,>({
  data,
  columns,
  rowHeight = 40,
  windowHeight = 400,
  onRowClick,
  className = '',
  onScroll,
  loading = false,
  skeletonRows = 10
}: VirtualizedTableProps<T>) => {
  const [scrollTop, setScrollTop] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);
  const [selectedRow, setSelectedRow] = useState<number | null>(null);

  // Calculate visible rows
  const visibleStartIndex = Math.floor(scrollTop / rowHeight);
  const visibleEndIndex = Math.min(
    data.length - 1,
    Math.ceil((scrollTop + windowHeight) / rowHeight)
  );

  const totalHeight = data.length * rowHeight;
  const offsetY = visibleStartIndex * rowHeight;
  const visibleItems = data.slice(visibleStartIndex, visibleEndIndex + 1);

  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const newScrollTop = e.currentTarget.scrollTop;
    setScrollTop(newScrollTop);
    onScroll?.(newScrollTop);
  }, [onScroll]);

  const handleRowClick = (item: T, index: number) => {
    setSelectedRow(index);
    onRowClick?.(item, index);
  };

  // Memoized row renderer
  const renderRow = useCallback((item: T, index: number) => (
    <div
      className={`flex border-b hover:bg-muted/50 cursor-pointer transition-colors ${
        selectedRow === index ? 'bg-primary/10' : ''
      }`}
      style={{ height: rowHeight }}
      onClick={() => handleRowClick(item, index)}
    >
      {columns.map((column) => (
        <div
          key={column.key}
          className="px-3 py-2 text-sm flex items-center"
          style={{ width: column.width || 'auto', flex: column.width ? '0 0 auto' : '1' }}
        >
          {column.render ? column.render(item, index) : String((item as any)[column.key])}
        </div>
      ))}
    </div>
  ), [columns, rowHeight, selectedRow, onRowClick]);

  // Skeleton loader for loading state
  const renderSkeletonRow = (index: number) => (
    <div
      key={`skeleton-${index}`}
      className="flex border-b"
      style={{ height: rowHeight }}
    >
      {columns.map((column) => (
        <div
          key={column.key}
          className="px-3 py-2"
          style={{ width: column.width || 'auto', flex: column.width ? '0 0 auto' : '1' }}
        >
          <div className="h-4 bg-muted rounded animate-pulse" />
        </div>
      ))}
    </div>
  );

  if (loading) {
    return (
      <div className={`border rounded-lg ${className}`}>
        {/* Table Header */}
        <div className="flex bg-muted/50 border-b font-medium">
          {columns.map((column) => (
            <div
              key={column.key}
              className="px-3 py-2 text-sm"
              style={{ width: column.width || 'auto', flex: column.width ? '0 0 auto' : '1' }}
            >
              {column.header}
            </div>
          ))}
        </div>
        
        {/* Skeleton Rows */}
        <div 
          className="overflow-auto"
          style={{ height: windowHeight }}
        >
          {Array.from({ length: skeletonRows }).map((_, index) => renderSkeletonRow(index))}
        </div>
      </div>
    );
  }

  return (
    <div className={`border rounded-lg ${className}`}>
      {/* Table Header */}
      <div className="flex bg-muted/50 border-b font-medium">
        {columns.map((column) => (
          <div
            key={column.key}
            className="px-3 py-2 text-sm"
            style={{ width: column.width || 'auto', flex: column.width ? '0 0 auto' : '1' }}
          >
            {column.header}
          </div>
        ))}
      </div>
      
      {/* Virtualized Body */}
      <div
        ref={containerRef}
        className="overflow-auto"
        style={{ height: windowHeight }}
        onScroll={handleScroll}
      >
        <div style={{ height: totalHeight, position: 'relative' }}>
          <div style={{ transform: `translateY(${offsetY}px)` }}>
            {visibleItems.map((item, index) => (
              <div key={visibleStartIndex + index}>
                {renderRow(item, visibleStartIndex + index)}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

// Enhanced Virtualized Table with pagination for very large datasets
interface EnhancedVirtualizedTableProps<T> extends VirtualizedTableProps<T> {
  enablePagination?: boolean;
  itemsPerPage?: number;
  onPageChange?: (page: number) => void;
  currentPage?: number;
  totalItems?: number;
}

export const EnhancedVirtualizedTable = <T,>({
  data,
  columns,
  rowHeight = 40,
  windowHeight = 400,
  onRowClick,
  className = '',
  onScroll,
  loading = false,
  skeletonRows = 10,
  enablePagination = false,
  itemsPerPage = 100,
  onPageChange,
  currentPage = 1,
  totalItems
}: EnhancedVirtualizedTableProps<T>) => {
  const [scrollTop, setScrollTop] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);
  const [selectedRow, setSelectedRow] = useState<number | null>(null);

  // For pagination mode
  const totalPages = totalItems ? Math.ceil(totalItems / itemsPerPage) : 1;
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const pageData = enablePagination ? data.slice(0, itemsPerPage) : data;

  // Calculate visible rows for current page
  const visibleStartIndex = Math.floor(scrollTop / rowHeight);
  const visibleEndIndex = Math.min(
    pageData.length - 1,
    Math.ceil((scrollTop + windowHeight) / rowHeight)
  );

  const totalHeight = pageData.length * rowHeight;
  const offsetY = visibleStartIndex * rowHeight;
  const visibleItems = pageData.slice(visibleStartIndex, visibleEndIndex + 1);

  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const newScrollTop = e.currentTarget.scrollTop;
    setScrollTop(newScrollTop);
    onScroll?.(newScrollTop);
  }, [onScroll]);

  const handleRowClick = (item: T, index: number) => {
    setSelectedRow(index);
    onRowClick?.(item, index);
  };

  // Memoized row renderer
  const renderRow = useCallback((item: T, index: number) => (
    <div
      className={`flex border-b hover:bg-muted/50 cursor-pointer transition-colors ${
        selectedRow === index ? 'bg-primary/10' : ''
      }`}
      style={{ height: rowHeight }}
      onClick={() => handleRowClick(item, index)}
    >
      {columns.map((column) => (
        <div
          key={column.key}
          className="px-3 py-2 text-sm flex items-center"
          style={{ width: column.width || 'auto', flex: column.width ? '0 0 auto' : '1' }}
        >
          {column.render ? column.render(item, index) : String((item as any)[column.key])}
        </div>
      ))}
    </div>
  ), [columns, rowHeight, selectedRow, onRowClick]);

  // Skeleton loader for loading state
  const renderSkeletonRow = (index: number) => (
    <div
      key={`skeleton-${index}`}
      className="flex border-b"
      style={{ height: rowHeight }}
    >
      {columns.map((column) => (
        <div
          key={column.key}
          className="px-3 py-2"
          style={{ width: column.width || 'auto', flex: column.width ? '0 0 auto' : '1' }}
        >
          <div className="h-4 bg-muted rounded animate-pulse" />
        </div>
      ))}
    </div>
  );

  if (loading) {
    return (
      <div className={`border rounded-lg ${className}`}>
        {/* Table Header */}
        <div className="flex bg-muted/50 border-b font-medium">
          {columns.map((column) => (
            <div
              key={column.key}
              className="px-3 py-2 text-sm"
              style={{ width: column.width || 'auto', flex: column.width ? '0 0 auto' : '1' }}
            >
              {column.header}
            </div>
          ))}
        </div>
        
        {/* Skeleton Rows */}
        <div 
          className="overflow-auto"
          style={{ height: windowHeight }}
        >
          {Array.from({ length: skeletonRows }).map((_, index) => renderSkeletonRow(index))}
        </div>
      </div>
    );
  }

  return (
    <div className={`border rounded-lg ${className}`}>
      {/* Table Header */}
      <div className="flex bg-muted/50 border-b font-medium">
        {columns.map((column) => (
          <div
            key={column.key}
            className="px-3 py-2 text-sm"
            style={{ width: column.width || 'auto', flex: column.width ? '0 0 auto' : '1' }}
          >
            {column.header}
          </div>
        ))}
      </div>
      
      {/* Virtualized Body */}
      <div
        ref={containerRef}
        className="overflow-auto"
        style={{ height: windowHeight }}
        onScroll={handleScroll}
      >
        <div style={{ height: totalHeight, position: 'relative' }}>
          <div style={{ transform: `translateY(${offsetY}px)` }}>
            {visibleItems.map((item, index) => (
              <div key={visibleStartIndex + index}>
                {renderRow(item, visibleStartIndex + index)}
              </div>
            ))}
          </div>
        </div>
      </div>
      
      {/* Pagination Controls */}
      {enablePagination && totalPages > 1 && (
        <div className="flex items-center justify-between p-3 border-t">
          <div className="text-sm text-muted-foreground">
            Showing {startIndex + 1} to {Math.min(endIndex, totalItems || data.length)} of {totalItems || data.length} entries
          </div>
          <div className="flex gap-2">
            <button
              onClick={() => onPageChange?.(Math.max(1, currentPage - 1))}
              disabled={currentPage <= 1}
              className="px-3 py-1 text-sm border rounded disabled:opacity-50"
            >
              Previous
            </button>
            <span className="px-3 py-1 text-sm">
              Page {currentPage} of {totalPages}
            </span>
            <button
              onClick={() => onPageChange?.(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage >= totalPages}
              className="px-3 py-1 text-sm border rounded disabled:opacity-50"
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  );
};