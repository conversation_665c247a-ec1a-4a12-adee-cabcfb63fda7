// Caching system for Data Center with performance optimizations
class DataCache<T = any> {
  private cache: Map<string, { data: T; timestamp: number; ttl: number; hits: number }> = new Map();
  private maxSize: number;
  private totalHits: number = 0;
  private totalMisses: number = 0;

  constructor(maxSize: number = 100) {
    this.maxSize = maxSize;
  }

  set(key: string, data: T, ttl: number = 300000) { // 5 minutes default
    // Remove oldest entries if cache is full
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      if (firstKey) this.cache.delete(firstKey);
    }
    
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
      hits: 0
    });
  }

  get(key: string): T | null {
    const entry = this.cache.get(key);
    if (!entry) {
      this.totalMisses++;
      return null;
    }
    
    // Check if entry is expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      this.totalMisses++;
      return null;
    }
    
    // Update hit count
    entry.hits++;
    this.totalHits++;
    
    return entry.data;
  }

  has(key: string): boolean {
    return this.get(key) !== null;
  }

  clear() {
    this.cache.clear();
  }

  size(): number {
    return this.cache.size;
  }

  // Get cache statistics
  getStats() {
    const totalRequests = this.totalHits + this.totalMisses;
    const hitRate = totalRequests > 0 ? this.totalHits / totalRequests : 0;
    
    return {
      hitRate,
      missRate: 1 - hitRate,
      size: this.cache.size,
      totalHits: this.totalHits,
      totalMisses: this.totalMisses
    };
  }

  // Get cache entry details for debugging
  getEntryDetails(key: string) {
    const entry = this.cache.get(key);
    if (!entry) return null;
    
    return {
      key,
      hits: entry.hits,
      age: Date.now() - entry.timestamp,
      expiresAt: entry.timestamp + entry.ttl
    };
  }

  // Get all cache keys
  getKeys(): string[] {
    return Array.from(this.cache.keys());
  }
}

// Processed data cache for expensive operations
class ProcessedDataCache extends DataCache<any> {
  constructor() {
    super(50); // Smaller cache for processed data
  }

  // Cache processed data with automatic key generation
  cacheProcessedData(
    operation: string, 
    inputData: any, 
    result: any, 
    ttl: number = 600000 // 10 minutes default
  ): string {
    // Generate cache key based on operation and input
    const key = `${operation}_${JSON.stringify(inputData)}`;
    this.set(key, result, ttl);
    return key;
  }

  // Get processed data with cache hit tracking
  getProcessedData(operation: string, inputData: any): any | null {
    const key = `${operation}_${JSON.stringify(inputData)}`;
    return this.get(key);
  }
}

// Persistent cache for user preferences and small datasets
class PersistentCache extends DataCache<any> {
  private storageKey: string;
  private cacheMap: Map<string, { data: any; timestamp: number; ttl: number; hits: number }>;

  constructor(storageKey: string = 'datacenter_persistent_cache') {
    super(200); // Larger cache for persistent data
    this.storageKey = storageKey;
    this.cacheMap = new Map();
    this.loadFromStorage();
  }

  // Load cache from localStorage
  private loadFromStorage() {
    try {
      const stored = localStorage.getItem(this.storageKey);
      if (stored) {
        const parsed = JSON.parse(stored);
        this.cacheMap = new Map(parsed);
      }
    } catch (error) {
      console.warn('Failed to load persistent cache:', error);
    }
  }

  // Save cache to localStorage
  private saveToStorage() {
    try {
      const serializable = Array.from(this.cacheMap.entries());
      localStorage.setItem(this.storageKey, JSON.stringify(serializable));
    } catch (error) {
      console.warn('Failed to save persistent cache:', error);
    }
  }

  // Override set to include persistence
  set(key: string, data: any, ttl: number = 86400000) { // 24 hours default
    super.set(key, data, ttl);
    this.saveToStorage();
  }

  // Override clear to clear storage too
  clear() {
    super.clear();
    try {
      localStorage.removeItem(this.storageKey);
    } catch (error) {
      console.warn('Failed to clear persistent cache:', error);
    }
  }
}

// Singleton instances
export const dataCache = new DataCache<any>(100);
export const processedDataCache = new ProcessedDataCache();
export const persistentCache = new PersistentCache('datacenter_cache_v1');

// Export classes for custom instances
export { DataCache, ProcessedDataCache, PersistentCache };