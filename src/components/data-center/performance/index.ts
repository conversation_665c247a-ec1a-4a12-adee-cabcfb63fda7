// Performance optimization components for Data Center
export { WebWorkerManager, webWorkerManager } from './WebWorkerManager';
export { VirtualizedTable, EnhancedVirtualizedTable } from './VirtualizedTable';
export { 
  DataCache, 
  ProcessedDataCache, 
  PersistentCache, 
  dataCache, 
  processedDataCache, 
  persistentCache 
} from './DataCache';
export { 
  usePerformanceMonitor, 
  PerformanceOptimizer, 
  PerformanceMonitor, 
  optimizedMemo, 
  useOptimizedMemo, 
  useOptimizedCallback,
  performanceOptimizer 
} from './PerformanceOptimizer';
export { 
  BackgroundProcessor, 
  PythonBackgroundProcessor, 
  backgroundProcessor, 
  pythonBackgroundProcessor 
} from './BackgroundProcessor';
export { 
  LazyDataLoader, 
  PaginatedDataLoader, 
  useLazyLoading, 
  useInfiniteScroll 
} from './LazyDataLoader';


// Unified Performance Management System
export { 
  PerformanceManager, 
  performanceManager, 
  usePerformanceManager,
  type PerformanceConfig,
  type PerformanceStats
} from './PerformanceManager';