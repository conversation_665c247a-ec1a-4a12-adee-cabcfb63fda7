import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { dataCache, processedDataCache, persistentCache } from './DataCache';

// Performance metrics interface
export interface PerformanceMetrics {
  renderTime: number;
  memoryUsage: number;
  fps: number;
  cacheHitRate: number;
  activeWorkers: number;
  dataProcessingTime: number;
  componentRerenders: number;
}

// Performance monitoring hook
export const usePerformanceMonitor = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    renderTime: 0,
    memoryUsage: 0,
    fps: 0,
    cacheHitRate: 0,
    activeWorkers: 0,
    dataProcessingTime: 0,
    componentRerenders: 0
  });
  
  const [isMonitoring, setIsMonitoring] = useState(false);
  const frameCountRef = useRef(0);
  const lastFrameTimeRef = useRef(performance.now());
  const rerenderCountRef = useRef(0);

  // Measure render time for a function
  const measureRenderTime = useCallback(<T extends (...args: any[]) => any>(
    fn: T, 
    label = 'render'
  ): T => {
    return function(...args: any[]) {
      const start = performance.now();
      const result = fn(...args);
      const end = performance.now();
      
      setMetrics(prev => ({
        ...prev,
        renderTime: end - start
      }));
      
      return result;
    } as T;
  }, []);

  // Get memory usage (if available)
  const getMemoryUsage = useCallback(() => {
    if ('memory' in performance) {
      // @ts-ignore - Chrome-specific memory API
      const memory = (performance as any).memory;
      if (memory) {
        return {
          used: Math.round(memory.usedJSHeapSize / 1048576), // MB
          total: Math.round(memory.totalJSHeapSize / 1048576), // MB
          limit: Math.round(memory.jsHeapSizeLimit / 1048576) // MB
        };
      }
    }
    return { used: 0, total: 0, limit: 0 };
  }, []);

  // Calculate FPS
  const calculateFPS = useCallback(() => {
    const now = performance.now();
    const delta = now - lastFrameTimeRef.current;
    lastFrameTimeRef.current = now;
    
    frameCountRef.current++;
    
    if (delta > 1000) {
      const fps = Math.round((frameCountRef.current * 1000) / delta);
      frameCountRef.current = 0;
      
      setMetrics(prev => ({
        ...prev,
        fps
      }));
    }
  }, []);

  // Monitor performance
  useEffect(() => {
    if (!isMonitoring) return;

    let animationFrameId: number;
    
    const monitor = () => {
      // Calculate FPS
      calculateFPS();
      
      // Get memory usage
      const memory = getMemoryUsage();
      setMetrics(prev => ({
        ...prev,
        memoryUsage: memory.used
      }));
      
      // Get cache stats
      const cacheStats = dataCache.getStats();
      setMetrics(prev => ({
        ...prev,
        cacheHitRate: cacheStats.hitRate
      }));
      
      animationFrameId = requestAnimationFrame(monitor);
    };
    
    animationFrameId = requestAnimationFrame(monitor);
    
    return () => {
      if (animationFrameId) {
        cancelAnimationFrame(animationFrameId);
      }
    };
  }, [isMonitoring, calculateFPS, getMemoryUsage]);

  // Track component rerenders
  useEffect(() => {
    rerenderCountRef.current++;
    setMetrics(prev => ({
      ...prev,
      componentRerenders: rerenderCountRef.current
    }));
  });

  const startMonitoring = useCallback(() => {
    setIsMonitoring(true);
  }, []);

  const stopMonitoring = useCallback(() => {
    setIsMonitoring(false);
  }, []);

  const resetMetrics = useCallback(() => {
    setMetrics({
      renderTime: 0,
      memoryUsage: 0,
      fps: 0,
      cacheHitRate: 0,
      activeWorkers: 0,
      dataProcessingTime: 0,
      componentRerenders: 0
    });
    rerenderCountRef.current = 0;
  }, []);

  return {
    metrics,
    isMonitoring,
    startMonitoring,
    stopMonitoring,
    resetMetrics,
    measureRenderTime
  };
};

// Performance optimization utilities
export class PerformanceOptimizer {
  // Debounce function to limit how often a function can be called
  static debounce<T extends (...args: any[]) => any>(
    func: T, 
    wait: number
  ): (...args: Parameters<T>) => void {
    let timeout: NodeJS.Timeout | null = null;
    return function(...args: Parameters<T>) {
      if (timeout) {
        clearTimeout(timeout);
      }
      timeout = setTimeout(() => func(...args), wait);
    };
  }

  // Throttle function to limit function calls to once per time period
  static throttle<T extends (...args: any[]) => any>(
    func: T, 
    limit: number
  ): (...args: Parameters<T>) => void {
    let inThrottle = false;
    return function(...args: Parameters<T>) {
      if (!inThrottle) {
        func(...args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }

  // Memoize function results based on arguments
  static memoize<T extends (...args: any[]) => any>(
    func: T
  ): T {
    const cache = new Map<string, ReturnType<T>>();
    
    return function(...args: Parameters<T>): ReturnType<T> {
      const key = JSON.stringify(args);
      if (cache.has(key)) {
        return cache.get(key)!;
      }
      
      const result = func(...args);
      cache.set(key, result);
      return result;
    } as T;
  }

  // Optimize large array processing with chunking
  static async processInChunks<T, R>(
    array: T[], 
    processor: (item: T) => R,
    chunkSize = 100
  ): Promise<R[]> {
    const results: R[] = [];
    
    for (let i = 0; i < array.length; i += chunkSize) {
      const chunk = array.slice(i, i + chunkSize);
      const chunkResults = chunk.map(processor);
      results.push(...chunkResults);
      
      // Yield to event loop to prevent blocking
      if (i + chunkSize < array.length) {
        await new Promise(resolve => setTimeout(resolve, 0));
      }
    }
    
    return results;
  }

  // Optimize rendering by virtualizing large lists
  static virtualizeList<T>(
    items: T[],
    visibleStart: number,
    visibleEnd: number,
    bufferSize = 5
  ): { visibleItems: T[], startIndex: number, endIndex: number } {
    const start = Math.max(0, visibleStart - bufferSize);
    const end = Math.min(items.length - 1, visibleEnd + bufferSize);
    
    return {
      visibleItems: items.slice(start, end + 1),
      startIndex: start,
      endIndex: end
    };
  }

  // Optimize data loading with progressive enhancement
  static async progressiveLoad<T>(
    loaders: Array<() => Promise<T | null>>,
    timeout = 5000
  ): Promise<T | null> {
    for (const loader of loaders) {
      try {
        const result = await Promise.race([
          loader(),
          new Promise<null>(resolve => setTimeout(() => resolve(null), timeout))
        ]);
        
        if (result !== null) {
          return result;
        }
      } catch (error) {
      }
    }
    
    return null;
  }
}

// Performance monitoring component
export const PerformanceMonitor: React.FC<{
  className?: string;
}> = ({ className = '' }) => {
  const { metrics, isMonitoring, startMonitoring, stopMonitoring, resetMetrics } = usePerformanceMonitor();
  
  return (
    <div className={`bg-card border rounded-lg p-4 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="font-semibold">Performance Monitor</h3>
        <div className="flex gap-2">
          <button
            onClick={isMonitoring ? stopMonitoring : startMonitoring}
            className={`px-3 py-1 text-sm rounded ${
              isMonitoring 
                ? 'bg-red-500 text-white' 
                : 'bg-green-500 text-white'
            }`}
          >
            {isMonitoring ? 'Stop' : 'Start'}
          </button>
          <button
            onClick={resetMetrics}
            className="px-3 py-1 text-sm bg-muted hover:bg-muted/70 rounded"
          >
            Reset
          </button>
        </div>
      </div>
      
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="bg-muted/50 p-3 rounded">
          <p className="text-xs text-muted-foreground">Render Time</p>
          <p className="text-lg font-bold">{metrics.renderTime.toFixed(2)}ms</p>
        </div>
        
        <div className="bg-muted/50 p-3 rounded">
          <p className="text-xs text-muted-foreground">Memory Usage</p>
          <p className="text-lg font-bold">{metrics.memoryUsage}MB</p>
        </div>
        
        <div className="bg-muted/50 p-3 rounded">
          <p className="text-xs text-muted-foreground">FPS</p>
          <p className="text-lg font-bold">{metrics.fps}</p>
        </div>
        
        <div className="bg-muted/50 p-3 rounded">
          <p className="text-xs text-muted-foreground">Cache Hit Rate</p>
          <p className="text-lg font-bold">{(metrics.cacheHitRate * 100).toFixed(1)}%</p>
        </div>
      </div>
      
      {isMonitoring && (
        <div className="mt-4 p-3 bg-green-500/10 border border-green-500/20 rounded">
          <p className="text-sm text-green-700 dark:text-green-300">
            Monitoring active - Performance metrics updating in real-time
          </p>
        </div>
      )}
    </div>
  );
};

// React.memo optimization helper
export const optimizedMemo = <T extends React.ComponentType<any>>(
  component: T
): T => {
  return React.memo(component) as unknown as T;
};

// useMemo with dependency tracking
export const useOptimizedMemo = <T,>(
  factory: () => T,
  deps: React.DependencyList
): T => {
  return useMemo(factory, deps);
};

// useCallback with optimization
export const useOptimizedCallback = <T extends (...args: any[]) => any>(
  callback: T,
  deps: React.DependencyList
): T => {
  return useCallback(callback, deps);
};

// Export singleton instance
export const performanceOptimizer = new PerformanceOptimizer();