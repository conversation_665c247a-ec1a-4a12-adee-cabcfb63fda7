// Web Worker for Python script execution
const pythonWorkerCode = `
self.onmessage = async function(e) {
  const { script, data, taskId } = e.data;
  
  try {
    // For actual Python execution, we would use a Python bridge
    // This is a placeholder for demonstration
    let result = '';
    
    // Simulate Python processing with progress updates
    const steps = 10;
    for (let i = 0; i <= steps; i++) {
      // Simulate work
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // Send progress update
      self.postMessage({
        type: 'progress',
        taskId,
        progress: (i / steps) * 100,
        message: \`Processing step \${i + 1}/\${steps + 1}\`
      });
    }
    
    // Simulate result
    result = \`-- Generated SQL from Python script\\n\${script}\\n-- Processed \${data?.length || 0} rows\`;
    
    self.postMessage({
      type: 'complete',
      taskId,
      result
    });
  } catch (error) {
    self.postMessage({
      type: 'error',
      taskId,
      error: error.message || 'Unknown error'
    });
  }
};
`;

// Create blob URL for worker
const workerBlob = new Blob([pythonWorkerCode], { type: 'application/javascript' });
const workerUrl = URL.createObjectURL(workerBlob);

export class WebWorkerManager {
  private workers: Worker[] = [];
  private taskCallbacks: Map<string, {
    onProgress?: (progress: number, message: string) => void;
    onComplete?: (result: any) => void;
    onError?: (error: string) => void;
  }> = new Map();
  private activeTasks: Map<string, boolean> = new Map();
  private workerQueue: Worker[] = [];
  private maxWorkers: number;

  constructor(maxWorkers = 4) {
    this.maxWorkers = maxWorkers;
    this.initializeWorkers();
  }

  private initializeWorkers() {
    for (let i = 0; i < this.maxWorkers; i++) {
      const worker = new Worker(workerUrl);
      worker.onmessage = this.handleWorkerMessage.bind(this);
      this.workers.push(worker);
      this.workerQueue.push(worker);
    }
  }

  private handleWorkerMessage(e: MessageEvent) {
    const { type, taskId, progress, message, result, error } = e.data;
    
    const callbacks = this.taskCallbacks.get(taskId);
    if (!callbacks) return;

    switch (type) {
      case 'progress':
        callbacks.onProgress?.(progress, message);
        break;
      case 'complete':
        this.taskCallbacks.delete(taskId);
        this.activeTasks.delete(taskId);
        callbacks.onComplete?.(result);
        this.releaseWorker(e.target as Worker);
        break;
      case 'error':
        this.taskCallbacks.delete(taskId);
        this.activeTasks.delete(taskId);
        callbacks.onError?.(error);
        this.releaseWorker(e.target as Worker);
        break;
    }
  }

  private getAvailableWorker(): Worker | null {
    if (this.workerQueue.length > 0) {
      return this.workerQueue.shift() || null;
    }
    return null;
  }

  private releaseWorker(worker: Worker) {
    this.workerQueue.push(worker);
  }

  executePythonScript(
    script: string,
    data?: any,
    onProgress?: (progress: number, message: string) => void,
    onComplete?: (result: any) => void,
    onError?: (error: string) => void
  ): string {
    const taskId = Math.random().toString(36).substr(2, 9);
    const worker = this.getAvailableWorker();

    if (!worker) {
      onError?.('No workers available');
      return taskId;
    }

    // Store callbacks
    this.taskCallbacks.set(taskId, {
      onProgress,
      onComplete,
      onError
    });

    this.activeTasks.set(taskId, true);

    // Send task to worker
    worker.postMessage({
      script,
      data,
      taskId
    });

    return taskId;
  }

  cancelTask(taskId: string): boolean {
    if (this.activeTasks.has(taskId)) {
      this.activeTasks.delete(taskId);
      this.taskCallbacks.delete(taskId);
      return true;
    }
    return false;
  }

  getActiveTaskCount(): number {
    return this.activeTasks.size;
  }

  destroy() {
    // Terminate all workers
    this.workers.forEach(worker => {
      worker.terminate();
    });
    this.workers = [];
    this.workerQueue = [];
    this.taskCallbacks.clear();
    this.activeTasks.clear();
    
    // Revoke blob URL
    URL.revokeObjectURL(workerUrl);
  }
}

// Singleton instance
export const webWorkerManager = new WebWorkerManager(4);