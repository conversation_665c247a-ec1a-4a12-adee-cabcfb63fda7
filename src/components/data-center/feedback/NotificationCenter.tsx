import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Bell, 
  X, 
  CheckCircle, 
  AlertCircle, 
  Info, 
  AlertTriangle,
  Trash2,
  Check,
  Clock
} from 'lucide-react';
import { useFeedback } from './FeedbackProvider';
import { Card, Button, Badge } from '../ui/StyledComponents';
import { colors } from '../ui/StyledComponents';

interface NotificationCenterProps {
  position?: 'top-right' | 'bottom-right' | 'top-left' | 'bottom-left';
  maxNotifications?: number;
}

export const NotificationCenter: React.FC<NotificationCenterProps> = ({ 
  position = 'top-right',
  maxNotifications = 5
}) => {
  const { notifications, removeNotification, markAsRead, clearAllNotifications } = useFeedback();
  const [expanded, setExpanded] = useState(false);
  
  // Filter unread notifications for the bell badge
  const unreadCount = notifications.filter(n => !n.read).length;
  
  // Get position classes
  const getPositionClasses = () => {
    switch (position) {
      case 'top-right':
        return 'top-4 right-4';
      case 'bottom-right':
        return 'bottom-4 right-4';
      case 'top-left':
        return 'top-4 left-4';
      case 'bottom-left':
        return 'bottom-4 left-4';
      default:
        return 'top-4 right-4';
    }
  };

  // Get icon for notification type
  const getIcon = (type: string) => {
    switch (type) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-600" />;
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-600" />;
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-yellow-600" />;
      case 'info':
        return <Info className="w-5 h-5 text-blue-600" />;
      default:
        return <Info className="w-5 h-5 text-blue-600" />;
    }
  };

  // Get background color for notification
  const getBackgroundColor = (type: string) => {
    switch (type) {
      case 'success':
        return 'bg-green-50 border-green-200';
      case 'error':
        return 'bg-red-50 border-red-200';
      case 'warning':
        return 'bg-yellow-50 border-yellow-200';
      case 'info':
        return 'bg-blue-50 border-blue-200';
      default:
        return 'bg-blue-50 border-blue-200';
    }
  };

  // Format timestamp
  const formatTime = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    
    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    
    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours}h ago`;
    
    const days = Math.floor(hours / 24);
    return `${days}d ago`;
  };

  // Group notifications by date
  const groupNotificationsByDate = () => {
    const groups: Record<string, typeof notifications> = {};
    
    notifications.forEach(notification => {
      const date = notification.timestamp.toDateString();
      if (!groups[date]) {
        groups[date] = [];
      }
      groups[date].push(notification);
    });
    
    return groups;
  };

  const groupedNotifications = groupNotificationsByDate();

  return (
    <>
      {/* Notification Bell */}
      <div className={`fixed ${getPositionClasses()} z-50`}>
        <Button
          variant="secondary"
          size="sm"
          onClick={() => setExpanded(!expanded)}
          className="relative p-2 rounded-full shadow-lg"
        >
          <Bell className="w-5 h-5" />
          {unreadCount > 0 && (
            <Badge 
              variant="error"
              className="absolute -top-1 -right-1 w-5 h-5 rounded-full flex items-center justify-center text-xs p-0"
            >
              {unreadCount}
            </Badge>
          )}
        </Button>
      </div>

      {/* Notification Center Panel */}
      <AnimatePresence>
        {expanded && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: -20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: -20 }}
            className={`fixed ${getPositionClasses()} z-50 w-96 max-h-[80vh] overflow-hidden shadow-xl`}
          >
            <Card className="p-0">
              {/* Header */}
              <div className="flex items-center justify-between p-4 border-b">
                <h3 className="font-semibold flex items-center gap-2">
                  <Bell className="w-5 h-5" />
                  Notifications
                  {unreadCount > 0 && (
                    <Badge variant="primary">{unreadCount} unread</Badge>
                  )}
                </h3>
                <div className="flex gap-2">
                  {notifications.length > 0 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={clearAllNotifications}
                      className="p-1"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  )}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setExpanded(false)}
                    className="p-1"
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              </div>

              {/* Notifications List */}
              <div className="max-h-[60vh] overflow-y-auto">
                {notifications.length === 0 ? (
                  <div className="p-8 text-center text-muted-foreground">
                    <Bell className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <p className="text-sm">No notifications</p>
                  </div>
                ) : (
                  Object.entries(groupedNotifications).map(([date, groupNotifications]) => (
                    <div key={date}>
                      <div className="px-4 py-2 text-xs text-muted-foreground bg-muted/50 sticky top-0">
                        {new Date(date).toDateString() === new Date().toDateString() 
                          ? 'Today' 
                          : new Date(date).toDateString()}
                      </div>
                      {groupNotifications.map((notification) => (
                        <motion.div
                          key={notification.id}
                          initial={{ opacity: 0, x: 20 }}
                          animate={{ opacity: 1, x: 0 }}
                          className={`border-l-4 ${getBackgroundColor(notification.type)} p-4 hover:bg-muted/50 transition-colors`}
                        >
                          <div className="flex gap-3">
                            <div className="flex-shrink-0">
                              {getIcon(notification.type)}
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-start justify-between gap-2">
                                <h4 className="font-medium text-sm truncate">
                                  {notification.title}
                                </h4>
                                {!notification.read && (
                                  <Badge variant="primary" className="w-2 h-2 p-0 rounded-full" />
                                )}
                              </div>
                              <p className="text-sm text-muted-foreground mt-1">
                                {notification.message}
                              </p>
                              <div className="flex items-center justify-between mt-2">
                                <span className="text-xs text-muted-foreground flex items-center gap-1">
                                  <Clock className="w-3 h-3" />
                                  {formatTime(notification.timestamp)}
                                </span>
                                <div className="flex gap-1">
                                  {notification.actions?.map((action, index) => (
                                    <Button
                                      key={index}
                                      variant="ghost"
                                      size="sm"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        action.action();
                                        removeNotification(notification.id);
                                      }}
                                      className="text-xs h-6 px-2"
                                    >
                                      {action.label}
                                    </Button>
                                  ))}
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => removeNotification(notification.id)}
                                    className="text-xs h-6 px-2"
                                  >
                                    <X className="w-3 h-3" />
                                  </Button>
                                </div>
                              </div>
                            </div>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  ))
                )}
              </div>

              {/* Footer */}
              {notifications.length > 0 && (
                <div className="p-3 border-t bg-muted/50 text-center">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      notifications.forEach(n => markAsRead(n.id));
                    }}
                    className="text-xs"
                  >
                    <Check className="w-3 h-3 mr-1" />
                    Mark all as read
                  </Button>
                </div>
              )}
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

// Notification Toast Component (for individual notifications)
export const NotificationToast: React.FC<{
  notification: any;
  onDismiss: (id: string) => void;
}> = ({ notification, onDismiss }) => {
  const getIcon = (type: string) => {
    switch (type) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-600" />;
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-600" />;
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-yellow-600" />;
      case 'info':
        return <Info className="w-5 h-5 text-blue-600" />;
      default:
        return <Info className="w-5 h-5 text-blue-600" />;
    }
  };

  const getBackgroundColor = (type: string) => {
    switch (type) {
      case 'success':
        return 'bg-green-50 border-green-200';
      case 'error':
        return 'bg-red-50 border-red-200';
      case 'warning':
        return 'bg-yellow-50 border-yellow-200';
      case 'info':
        return 'bg-blue-50 border-blue-200';
      default:
        return 'bg-blue-50 border-blue-200';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className={`flex items-start gap-3 p-4 border rounded-lg shadow-lg ${getBackgroundColor(notification.type)}`}
    >
      {getIcon(notification.type)}
      <div className="flex-1">
        <h4 className="font-medium text-sm">{notification.title}</h4>
        <p className="text-sm text-muted-foreground mt-1">{notification.message}</p>
      </div>
      <button
        onClick={() => onDismiss(notification.id)}
        className="text-muted-foreground hover:text-foreground transition-colors"
      >
        <X className="w-4 h-4" />
      </button>
    </motion.div>
  );
};