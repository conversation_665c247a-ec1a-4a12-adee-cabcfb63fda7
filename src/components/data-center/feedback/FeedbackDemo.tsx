import React, { useState, useEffect } from 'react';
import { 
  FeedbackProvider,
  NotificationCenter,
  JobTracker,
  ActionFeedback,
  ErrorRecovery,
  PerformanceIndicators,
  useFeedback
} from './index';
import { <PERSON>, Button } from '../ui/StyledComponents';

// Demo component that shows how to use all feedback components
const FeedbackDemoContent: React.FC = () => {
  const { 
    addNotification, 
    addJob, 
    updateJob,
    addPerformanceMetric,
    setActionFeedback,
    addError
  } = useFeedback();
  
  const [demoJobId, setDemoJobId] = useState<string | null>(null);
  
  // Simulate a long-running job
  const startDemoJob = () => {
    const jobId = addJob({
      name: 'Data Processing Task',
      message: 'Processing large dataset'
    });
    
    setDemoJobId(jobId);
    
    // Simulate job progress
    let progress = 0;
    const interval = setInterval(() => {
      progress += 10;
      updateJob(jobId, { 
        progress, 
        status: progress < 100 ? 'running' : 'completed',
        message: progress < 100 
          ? `Processing... ${progress}%` 
          : 'Processing complete!',
        endTime: progress >= 100 ? new Date() : undefined,
        performance: {
          duration: progress >= 100 ? 1250 : undefined,
          memoryUsage: 45.2 * 1024 * 1024
        }
      });
      
      if (progress >= 100) {
        clearInterval(interval);
        // Add success notification
        addNotification({
          type: 'success',
          title: 'Processing Complete',
          message: 'Your data has been processed successfully',
          autoDismiss: true,
          dismissAfter: 5000,
          actions: [
            {
              label: 'View Results',
              action: () => console.log('Viewing results')
            }
          ]
        });
      }
    }, 500);
  };
  
  // Simulate an error
  const simulateError = () => {
    try {
      throw new Error('Failed to connect to data source');
    } catch (error) {
      addError(error as Error, {
        component: 'FeedbackDemo',
        action: 'simulateError'
      }, [
        'Check your internet connection',
        'Verify data source credentials',
        'Try again in a few minutes'
      ]);
    }
  };
  
  // Add performance metrics
  useEffect(() => {
    const interval = setInterval(() => {
      addPerformanceMetric({
        name: 'processingTime',
        value: Math.floor(Math.random() * 1000),
        unit: 'ms',
        threshold: 500,
        alert: Math.random() > 0.7
      });
      
      addPerformanceMetric({
        name: 'memoryUsage',
        value: Math.floor(Math.random() * 100),
        unit: 'MB',
        threshold: 80,
        alert: Math.random() > 0.8
      });
    }, 3000);
    
    return () => clearInterval(interval);
  }, [addPerformanceMetric]);
  
  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">Feedback System Demo</h2>
        <div className="flex gap-2">
          <Button
            variant="data"
            onClick={() => {
              setActionFeedback({
                lastAction: 'Demo Action',
                status: 'success',
                message: 'Action completed successfully'
              });
            }}
          >
            Show Action Feedback
          </Button>
          <Button
            variant="warning"
            onClick={startDemoJob}
            disabled={!!demoJobId}
          >
            Start Demo Job
          </Button>
          <Button
            variant="error"
            onClick={simulateError}
          >
            Simulate Error
          </Button>
        </div>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-4">
          <h3 className="font-medium mb-3">Performance Indicators</h3>
          <PerformanceIndicators />
        </Card>
        
        <Card className="p-4">
          <h3 className="font-medium mb-3">Job Tracker</h3>
          <JobTracker compact />
        </Card>
      </div>
      
      {/* Action Feedback will appear at the top */}
      <ActionFeedback />
      
      {/* Error Recovery will appear at the bottom right */}
      <ErrorRecovery />
      
      {/* Notification Center will appear at the top right when clicked */}
      <NotificationCenter />
    </div>
  );
};

// Wrapper component with FeedbackProvider
export const FeedbackDemo: React.FC = () => {
  return (
    <FeedbackProvider>
      <FeedbackDemoContent />
    </FeedbackProvider>
  );
};