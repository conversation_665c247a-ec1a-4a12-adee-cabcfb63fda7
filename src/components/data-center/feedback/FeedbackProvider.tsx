import React, { createContext, useContext, useState, useCallback, useEffect } from 'react';
import { v4 as uuidv4 } from 'uuid';

// Types
export type NotificationType = 'success' | 'error' | 'warning' | 'info';
export type JobStatus = 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';

export interface Notification {
  id: string;
  type: NotificationType;
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  actions?: Array<{
    label: string;
    action: () => void;
  }>;
  autoDismiss?: boolean;
  dismissAfter?: number; // in milliseconds
}

export interface Job {
  id: string;
  name: string;
  status: JobStatus;
  progress: number;
  message?: string;
  startTime?: Date;
  endTime?: Date;
  error?: string;
  dependencies?: string[];
  performance?: {
    duration?: number;
    memoryUsage?: number;
    cpuUsage?: number;
  };
}

export interface PerformanceMetric {
  id: string;
  name: string;
  value: number;
  unit: string;
  timestamp: Date;
  threshold?: number;
  alert?: boolean;
}

interface FeedbackContextType {
  // Notifications
  notifications: Notification[];
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => void;
  removeNotification: (id: string) => void;
  markAsRead: (id: string) => void;
  clearAllNotifications: () => void;
  
  // Jobs
  jobs: Job[];
  addJob: (job: Omit<Job, 'id' | 'status' | 'progress'>) => string;
  updateJob: (id: string, updates: Partial<Job>) => void;
  removeJob: (id: string) => void;
  cancelJob: (id: string) => void;
  
  // Performance
  performanceMetrics: PerformanceMetric[];
  addPerformanceMetric: (metric: Omit<PerformanceMetric, 'id' | 'timestamp'>) => void;
  clearPerformanceMetrics: () => void;
  
  // Action feedback
  actionFeedback: {
    lastAction: string;
    status: 'success' | 'error' | 'pending';
    message?: string;
  } | null;
  setActionFeedback: (feedback: FeedbackContextType['actionFeedback']) => void;
  
  // Error recovery
  errors: Array<{
    id: string;
    error: Error;
    context: Record<string, any>;
    timestamp: Date;
    recoverySuggestions?: string[];
    resolved: boolean;
  }>;
  addError: (error: Error, context?: Record<string, any>, recoverySuggestions?: string[]) => void;
  resolveError: (id: string) => void;
}

const FeedbackContext = createContext<FeedbackContextType | undefined>(undefined);

export const FeedbackProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Notifications state
  const [notifications, setNotifications] = useState<Notification[]>([]);
  
  // Jobs state
  const [jobs, setJobs] = useState<Job[]>([]);
  
  // Performance metrics state
  const [performanceMetrics, setPerformanceMetrics] = useState<PerformanceMetric[]>([]);
  
  // Action feedback state
  const [actionFeedback, setActionFeedback] = useState<FeedbackContextType['actionFeedback']>(null);
  
  // Errors state
  const [errors, setErrors] = useState<FeedbackContextType['errors']>([]);

  // Add notification
  const addNotification = useCallback((notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => {
    const newNotification: Notification = {
      id: uuidv4(),
      timestamp: new Date(),
      read: false,
      ...notification
    };
    
    setNotifications(prev => [newNotification, ...prev]);
    
    // Auto-dismiss if requested
    if (notification.autoDismiss && notification.dismissAfter) {
      setTimeout(() => {
        setNotifications(prev => prev.filter(n => n.id !== newNotification.id));
      }, notification.dismissAfter);
    }
  }, []);

  // Remove notification
  const removeNotification = useCallback((id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  }, []);

  // Mark notification as read
  const markAsRead = useCallback((id: string) => {
    setNotifications(prev => 
      prev.map(n => n.id === id ? { ...n, read: true } : n)
    );
  }, []);

  // Clear all notifications
  const clearAllNotifications = useCallback(() => {
    setNotifications([]);
  }, []);

  // Add job
  const addJob = useCallback((job: Omit<Job, 'id' | 'status' | 'progress'>) => {
    const jobId = uuidv4();
    const newJob: Job = {
      id: jobId,
      status: 'pending',
      progress: 0,
      ...job
    };
    
    setJobs(prev => [...prev, newJob]);
    return jobId;
  }, []);

  // Update job
  const updateJob = useCallback((id: string, updates: Partial<Job>) => {
    setJobs(prev => 
      prev.map(job => job.id === id ? { ...job, ...updates } : job)
    );
  }, []);

  // Remove job
  const removeJob = useCallback((id: string) => {
    setJobs(prev => prev.filter(job => job.id !== id));
  }, []);

  // Cancel job
  const cancelJob = useCallback((id: string) => {
    setJobs(prev => 
      prev.map(job => 
        job.id === id ? { ...job, status: 'cancelled', endTime: new Date() } : job
      )
    );
  }, []);

  // Add performance metric
  const addPerformanceMetric = useCallback((metric: Omit<PerformanceMetric, 'id' | 'timestamp'>) => {
    const newMetric: PerformanceMetric = {
      id: uuidv4(),
      timestamp: new Date(),
      ...metric
    };
    
    setPerformanceMetrics(prev => [newMetric, ...prev.slice(0, 99)]); // Keep last 100 metrics
  }, []);

  // Clear performance metrics
  const clearPerformanceMetrics = useCallback(() => {
    setPerformanceMetrics([]);
  }, []);

  // Add error
  const addError = useCallback((error: Error, context?: Record<string, any>, recoverySuggestions?: string[]) => {
    const newError = {
      id: uuidv4(),
      error,
      context: context || {},
      timestamp: new Date(),
      recoverySuggestions,
      resolved: false
    };
    
    setErrors(prev => [newError, ...prev]);
  }, []);

  // Resolve error
  const resolveError = useCallback((id: string) => {
    setErrors(prev => 
      prev.map(error => error.id === id ? { ...error, resolved: true } : error)
    );
  }, []);

  // Cleanup effect for old notifications
  useEffect(() => {
    const interval = setInterval(() => {
      const now = new Date();
      setNotifications(prev => 
        prev.filter(n => {
          // Keep notifications that are not auto-dismissed or are less than 1 hour old
          if (!n.autoDismiss) return true;
          return (now.getTime() - n.timestamp.getTime()) < 3600000; // 1 hour
        })
      );
    }, 60000); // Check every minute

    return () => clearInterval(interval);
  }, []);

  return (
    <FeedbackContext.Provider
      value={{
        notifications,
        addNotification,
        removeNotification,
        markAsRead,
        clearAllNotifications,
        jobs,
        addJob,
        updateJob,
        removeJob,
        cancelJob,
        performanceMetrics,
        addPerformanceMetric,
        clearPerformanceMetrics,
        actionFeedback,
        setActionFeedback,
        errors,
        addError,
        resolveError
      }}
    >
      {children}
    </FeedbackContext.Provider>
  );
};

export const useFeedback = () => {
  const context = useContext(FeedbackContext);
  if (context === undefined) {
    throw new Error('useFeedback must be used within a FeedbackProvider');
  }
  return context;
};