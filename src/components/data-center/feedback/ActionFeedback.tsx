import React, { useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  CheckCircle, 
  AlertCircle, 
  Info, 
  AlertTriangle,
  X,
  RotateCcw,
  Undo2
} from 'lucide-react';
import { useFeedback } from './FeedbackProvider';
import { Card, Button } from '../ui/StyledComponents';

interface ActionFeedbackProps {
  position?: 'top' | 'bottom';
  autoDismiss?: boolean;
  dismissAfter?: number; // in milliseconds
}

export const ActionFeedback: React.FC<ActionFeedbackProps> = ({ 
  position = 'top',
  autoDismiss = true,
  dismissAfter = 5000
}) => {
  const { actionFeedback, setActionFeedback } = useFeedback();
  
  // Auto-dismiss effect
  useEffect(() => {
    if (actionFeedback && autoDismiss) {
      const timer = setTimeout(() => {
        setActionFeedback(null);
      }, dismissAfter);
      
      return () => clearTimeout(timer);
    }
  }, [actionFeedback, autoDismiss, dismissAfter, setActionFeedback]);
  
  if (!actionFeedback) return null;
  
  // Get icon for feedback status
  const getIcon = () => {
    switch (actionFeedback.status) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-600" />;
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-600" />;
      case 'pending':
        return <Info className="w-5 h-5 text-blue-600" />;
      default:
        return <Info className="w-5 h-5 text-blue-600" />;
    }
  };
  
  // Get background color
  const getBackgroundColor = () => {
    switch (actionFeedback.status) {
      case 'success':
        return 'bg-green-50 border-green-200';
      case 'error':
        return 'bg-red-50 border-red-200';
      case 'pending':
        return 'bg-blue-50 border-blue-200';
      default:
        return 'bg-blue-50 border-blue-200';
    }
  };
  
  // Get position classes
  const getPositionClasses = () => {
    return position === 'top' 
      ? 'top-4 left-1/2 transform -translate-x-1/2' 
      : 'bottom-4 left-1/2 transform -translate-x-1/2';
  };
  
  return (
    <AnimatePresence>
      {actionFeedback && (
        <motion.div
          initial={{ opacity: 0, y: position === 'top' ? -20 : 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: position === 'top' ? -20 : 20 }}
          className={`fixed ${getPositionClasses()} z-50 w-full max-w-md`}
        >
          <Card className={`p-4 ${getBackgroundColor()} flex items-start gap-3`}>
            {getIcon()}
            <div className="flex-1 min-w-0">
              <h4 className="font-medium text-sm">
                {actionFeedback.lastAction}
              </h4>
              {actionFeedback.message && (
                <p className="text-sm text-muted-foreground mt-1">
                  {actionFeedback.message}
                </p>
              )}
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setActionFeedback(null)}
              className="h-6 w-6 p-0"
            >
              <X className="w-4 h-4" />
            </Button>
          </Card>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

// Success Animation Component
export const SuccessAnimation: React.FC<{
  message: string;
  onComplete: () => void;
}> = ({ message, onComplete }) => {
  useEffect(() => {
    const timer = setTimeout(onComplete, 2000);
    return () => clearTimeout(timer);
  }, [onComplete]);
  
  return (
    <motion.div
      initial={{ scale: 0.8, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      exit={{ scale: 0.8, opacity: 0 }}
      className="fixed inset-0 flex items-center justify-center z-50 bg-black/50"
    >
      <Card className="p-8 text-center bg-white">
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ 
            type: "spring", 
            stiffness: 200, 
            damping: 10 
          }}
          className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4"
        >
          <CheckCircle className="w-8 h-8 text-green-600" />
        </motion.div>
        <h3 className="font-medium text-lg">{message}</h3>
      </Card>
    </motion.div>
  );
};

// Undo/Redo Confirmation Component
export const UndoRedoConfirmation: React.FC<{
  action: string;
  onConfirm: () => void;
  onCancel: () => void;
  countdown?: number;
}> = ({ action, onConfirm, onCancel, countdown = 10 }) => {
  const [timeLeft, setTimeLeft] = React.useState(countdown);
  
  useEffect(() => {
    if (timeLeft > 0) {
      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);
      return () => clearTimeout(timer);
    } else {
      onCancel();
    }
  }, [timeLeft, onCancel]);
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="fixed bottom-4 right-4 z-50"
    >
      <Card className="p-4 bg-blue-50 border-blue-200 max-w-sm">
        <div className="flex items-start gap-3">
          <Undo2 className="w-5 h-5 text-blue-600 flex-shrink-0 mt-0.5" />
          <div className="flex-1">
            <h4 className="font-medium text-sm">Undo Action</h4>
            <p className="text-sm text-muted-foreground mt-1">
              Undo {action}? This will revert the changes.
            </p>
            <div className="flex items-center justify-between mt-3">
              <span className="text-xs text-muted-foreground">
                Auto-cancel in {timeLeft}s
              </span>
              <div className="flex gap-2">
                <Button
                  variant="success"
                  size="sm"
                  onClick={onConfirm}
                  className="h-8 text-xs"
                >
                  Undo
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onCancel}
                  className="h-8 text-xs"
                >
                  Cancel
                </Button>
              </div>
            </div>
          </div>
        </div>
      </Card>
    </motion.div>
  );
};

// Contextual Help Component
export const ContextualHelp: React.FC<{
  title: string;
  content: string;
  position?: { x: number; y: number };
}> = ({ title, content, position }) => {
  if (!position) return null;
  
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      className="fixed z-50"
      style={{ 
        left: position.x, 
        top: position.y,
        transform: 'translate(-50%, -100%)'
      }}
    >
      <Card className="p-4 bg-blue-50 border-blue-200 max-w-xs shadow-lg">
        <div className="flex items-start gap-2">
          <Info className="w-4 h-4 text-blue-600 flex-shrink-0 mt-0.5" />
          <div>
            <h4 className="font-medium text-sm">{title}</h4>
            <p className="text-xs text-muted-foreground mt-1">
              {content}
            </p>
          </div>
        </div>
      </Card>
    </motion.div>
  );
};

// Results Preview Component
export const ResultsPreview: React.FC<{
  data: any[];
  columns: string[];
  title: string;
  onConfirm: () => void;
  onCancel: () => void;
}> = ({ data, columns, title, onConfirm, onCancel }) => {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
    >
      <Card className="w-full max-w-2xl max-h-[80vh] overflow-hidden">
        <div className="p-4 border-b flex items-center justify-between">
          <h3 className="font-semibold">{title}</h3>
          <div className="flex gap-2">
            <Button
              variant="success"
              size="sm"
              onClick={onConfirm}
            >
              Confirm
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={onCancel}
            >
              Cancel
            </Button>
          </div>
        </div>
        
        <div className="overflow-auto max-h-[60vh]">
          <table className="w-full text-sm">
            <thead className="bg-muted sticky top-0">
              <tr>
                {columns.map((col, idx) => (
                  <th key={idx} className="p-2 text-left font-medium text-muted-foreground">
                    {col}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {data.slice(0, 10).map((row, rowIdx) => (
                <tr key={rowIdx} className="border-b hover:bg-muted/50">
                  {columns.map((col, colIdx) => (
                    <td key={colIdx} className="p-2">
                      {row[col] ?? row[colIdx] ?? 'N/A'}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
          
          {data.length > 10 && (
            <div className="p-2 text-center text-xs text-muted-foreground border-t">
              Showing 10 of {data.length} results
            </div>
          )}
        </div>
      </Card>
    </motion.div>
  );
};