import React from 'react';
import { motion } from 'framer-motion';
import { 
  Activity, 
  Clock, 
  Database, 
  Cpu, 
  AlertTriangle,
  TrendingUp,
  TrendingDown
} from 'lucide-react';
import { useFeedback } from './FeedbackProvider';
import { Card, Badge } from '../ui/StyledComponents';

interface PerformanceIndicatorsProps {
  compact?: boolean;
  showAlerts?: boolean;
}

export const PerformanceIndicators: React.FC<PerformanceIndicatorsProps> = ({ 
  compact = false,
  showAlerts = true
}) => {
  const { performanceMetrics } = useFeedback();
  
  // Get latest metrics
  const latestMetrics = performanceMetrics.reduce((acc, metric) => {
    if (!acc[metric.name] || metric.timestamp > acc[metric.name].timestamp) {
      acc[metric.name] = metric;
    }
    return acc;
  }, {} as Record<string, typeof performanceMetrics[0]>);
  
  // Get metrics with alerts
  const alertMetrics = Object.values(latestMetrics).filter(
    metric => metric.alert && metric.threshold
  );
  
  if (compact) {
    return (
      <div className="flex items-center gap-2">
        <div className="flex items-center gap-1 text-xs text-muted-foreground">
          <Activity className="w-3 h-3" />
          <span>Performance</span>
        </div>
        {alertMetrics.length > 0 && (
          <Badge variant="warning" className="text-xs">
            {alertMetrics.length} alerts
          </Badge>
        )}
      </div>
    );
  }
  
  return (
    <div className="space-y-3">
      {showAlerts && alertMetrics.length > 0 && (
        <Card className="p-3 bg-yellow-50 border-yellow-200">
          <div className="flex items-center gap-2">
            <AlertTriangle className="w-4 h-4 text-yellow-600 flex-shrink-0" />
            <div className="flex-1 min-w-0">
              <h4 className="font-medium text-sm text-yellow-700">
                {alertMetrics.length} Performance Alert{alertMetrics.length > 1 ? 's' : ''}
              </h4>
              <div className="flex flex-wrap gap-1 mt-1">
                {alertMetrics.slice(0, 3).map((metric, idx) => (
                  <Badge key={idx} variant="warning" className="text-xs">
                    {metric.name}: {metric.value}{metric.unit}
                  </Badge>
                ))}
                {alertMetrics.length > 3 && (
                  <Badge variant="warning" className="text-xs">
                    +{alertMetrics.length - 3} more
                  </Badge>
                )}
              </div>
            </div>
          </div>
        </Card>
      )}
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
        {Object.values(latestMetrics).map((metric) => (
          <motion.div
            key={metric.id}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="relative"
          >
            <Card className="p-4 hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between">
                <div>
                  <div className="flex items-center gap-2">
                    <div className="p-2 bg-muted rounded">
                      {metric.name.includes('time') || metric.name.includes('duration') ? (
                        <Clock className="w-4 h-4 text-blue-600" />
                      ) : metric.name.includes('memory') || metric.name.includes('storage') ? (
                        <Database className="w-4 h-4 text-green-600" />
                      ) : metric.name.includes('cpu') ? (
                        <Cpu className="w-4 h-4 text-purple-600" />
                      ) : (
                        <Activity className="w-4 h-4 text-gray-600" />
                      )}
                    </div>
                    <div>
                      <h4 className="font-medium text-sm capitalize">
                        {metric.name.replace(/([A-Z])/g, ' $1').trim()}
                      </h4>
                      <p className="text-xs text-muted-foreground">
                        {metric.value}{metric.unit}
                      </p>
                    </div>
                  </div>
                  
                  {metric.threshold && (
                    <div className="mt-2 flex items-center gap-1">
                      {metric.value > metric.threshold ? (
                        <TrendingUp className="w-3 h-3 text-red-600" />
                      ) : (
                        <TrendingDown className="w-3 h-3 text-green-600" />
                      )}
                      <span className="text-xs text-muted-foreground">
                        Threshold: {metric.threshold}{metric.unit}
                      </span>
                    </div>
                  )}
                </div>
                
                {metric.alert && (
                  <Badge variant="warning" className="absolute -top-2 -right-2">
                    <AlertTriangle className="w-3 h-3" />
                  </Badge>
                )}
              </div>
            </Card>
          </motion.div>
        ))}
      </div>
      
      {performanceMetrics.length > 0 && (
        <Card className="p-4">
          <h4 className="font-medium text-sm mb-3 flex items-center gap-2">
            <TrendingUp className="w-4 h-4" />
            Performance Trends
          </h4>
          <div className="h-32 flex items-end gap-1">
            {performanceMetrics
              .slice(-20) // Last 20 metrics
              .map((metric, idx) => (
                <div
                  key={idx}
                  className="flex-1 bg-blue-200 rounded-t hover:bg-blue-300 transition-colors"
                  style={{ 
                    height: `${Math.min(100, (metric.value / (metric.threshold || 100)) * 100)}%` 
                  }}
                  title={`${metric.name}: ${metric.value}${metric.unit}`}
                />
              ))}
          </div>
          <div className="flex justify-between text-xs text-muted-foreground mt-2">
            <span>20 data points</span>
            <span>Latest values</span>
          </div>
        </Card>
      )}
    </div>
  );
};

// Performance Comparison Component
export const PerformanceComparison: React.FC<{
  currentMetrics: Record<string, number>;
  previousMetrics: Record<string, number>;
  unit?: string;
}> = ({ currentMetrics, previousMetrics, unit = '' }) => {
  const metrics = Object.keys(currentMetrics);
  
  return (
    <Card className="p-4">
      <h4 className="font-medium text-sm mb-3">Performance Comparison</h4>
      <div className="space-y-3">
        {metrics.map((metric) => {
          const currentValue = currentMetrics[metric];
          const previousValue = previousMetrics[metric] || 0;
          const change = previousValue !== 0 
            ? ((currentValue - previousValue) / previousValue) * 100 
            : 0;
          
          return (
            <div key={metric} className="flex items-center justify-between">
              <span className="text-sm capitalize">
                {metric.replace(/([A-Z])/g, ' $1').trim()}
              </span>
              <div className="flex items-center gap-2">
                <span className="text-sm font-mono">
                  {currentValue}{unit}
                </span>
                {change !== 0 && (
                  <Badge 
                    variant={change > 0 ? 'warning' : 'success'}
                    className="text-xs"
                  >
                    {change > 0 ? '+' : ''}{change.toFixed(1)}%
                  </Badge>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </Card>
  );
};

// Processing Time Tracker Component
export const ProcessingTimeTracker: React.FC<{
  startTime: Date;
  onComplete?: (duration: number) => void;
}> = ({ startTime, onComplete }) => {
  const [elapsedTime, setElapsedTime] = React.useState(0);
  
  React.useEffect(() => {
    const interval = setInterval(() => {
      const now = new Date();
      const elapsed = (now.getTime() - startTime.getTime()) / 1000;
      setElapsedTime(elapsed);
      
      if (onComplete && elapsed > 300) { // 5 minutes
        onComplete(elapsed);
        clearInterval(interval);
      }
    }, 1000);
    
    return () => clearInterval(interval);
  }, [startTime, onComplete]);
  
  // Format time
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };
  
  return (
    <div className="flex items-center gap-2 text-sm">
      <Clock className="w-4 h-4 text-blue-600" />
      <span>Processing: {formatTime(elapsedTime)}</span>
      <div className="w-24 bg-gray-200 rounded-full h-2">
        <motion.div 
          className="bg-blue-600 h-2 rounded-full"
          initial={{ width: 0 }}
          animate={{ width: `${Math.min(100, (elapsedTime / 300) * 100)}%` }}
        />
      </div>
    </div>
  );
};