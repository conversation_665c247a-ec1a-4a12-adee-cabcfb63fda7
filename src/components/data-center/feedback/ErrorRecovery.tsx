import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  AlertTriangle, 
  X, 
  RotateCcw, 
  Copy, 
  ExternalLink,
  Check
} from 'lucide-react';
import { useFeedback } from './FeedbackProvider';
import { Card, Button } from '../ui/StyledComponents';

interface ErrorRecoveryProps {
  position?: 'bottom-right' | 'modal';
  showResolved?: boolean;
}

export const ErrorRecovery: React.FC<ErrorRecoveryProps> = ({ 
  position = 'bottom-right',
  showResolved = false
}) => {
  const { errors, resolveError } = useFeedback();
  const [expandedError, setExpandedError] = useState<string | null>(null);
  const [copiedErrorId, setCopiedErrorId] = useState<string | null>(null);
  
  // Filter errors based on showResolved flag
  const filteredErrors = showResolved 
    ? errors 
    : errors.filter(error => !error.resolved);
  
  // Copy error details to clipboard
  const copyErrorDetails = async (error: any) => {
    const errorDetails = `
Error: ${error.error.message}
Timestamp: ${error.timestamp.toLocaleString()}
Context: ${JSON.stringify(error.context, null, 2)}
Recovery Suggestions: ${error.recoverySuggestions?.join(', ') || 'None'}
    `;
    
    try {
      await navigator.clipboard.writeText(errorDetails);
      setCopiedErrorId(error.id);
      setTimeout(() => setCopiedErrorId(null), 2000);
    } catch (err) {
    }
  };
  
  // Get position classes
  const getPositionClasses = () => {
    switch (position) {
      case 'bottom-right':
        return 'fixed bottom-4 right-4 z-40';
      case 'modal':
        return 'fixed inset-0 z-50 flex items-center justify-center bg-black/50';
      default:
        return 'fixed bottom-4 right-4 z-40';
    }
  };
  
  if (filteredErrors.length === 0) return null;
  
  if (position === 'modal') {
    const error = filteredErrors[0]; // Show first error in modal
    
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className={getPositionClasses()}
      >
        <Card className="w-full max-w-2xl max-h-[80vh] overflow-hidden">
          <div className="p-4 border-b flex items-center justify-between">
            <div className="flex items-center gap-2">
              <AlertTriangle className="w-5 h-5 text-red-600" />
              <h3 className="font-semibold">Error Recovery</h3>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => resolveError(error.id)}
              className="h-8 w-8 p-0"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
          
          <div className="p-4 overflow-y-auto max-h-[60vh]">
            <div className="mb-4">
              <h4 className="font-medium text-red-700">{error.error.name || 'Error'}</h4>
              <p className="text-sm text-muted-foreground mt-1">
                {error.error.message}
              </p>
              <p className="text-xs text-muted-foreground mt-2">
                {error.timestamp.toLocaleString()}
              </p>
            </div>
            
            {error.context && Object.keys(error.context).length > 0 && (
              <div className="mb-4">
                <h5 className="font-medium text-sm mb-2">Context</h5>
                <div className="bg-muted p-3 rounded text-xs font-mono max-h-32 overflow-y-auto">
                  {Object.entries(error.context).map(([key, value]) => (
                    <div key={key} className="mb-1 last:mb-0">
                      <span className="text-blue-600">{key}:</span> {JSON.stringify(value)}
                    </div>
                  ))}
                </div>
              </div>
            )}
            
            {error.recoverySuggestions && error.recoverySuggestions.length > 0 && (
              <div className="mb-4">
                <h5 className="font-medium text-sm mb-2">Recovery Suggestions</h5>
                <ul className="space-y-2">
                  {error.recoverySuggestions.map((suggestion, idx) => (
                    <li key={idx} className="flex items-start gap-2 text-sm">
                      <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div>
                      <span>{suggestion}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
            
            <div className="flex flex-wrap gap-2">
              <Button
                variant="data"
                size="sm"
                onClick={() => copyErrorDetails(error)}
              >
                {copiedErrorId === error.id ? (
                  <>
                    <Check className="w-4 h-4 mr-2" />
                    Copied
                  </>
                ) : (
                  <>
                    <Copy className="w-4 h-4 mr-2" />
                    Copy Details
                  </>
                )}
              </Button>
              
              <Button
                variant="success"
                size="sm"
                onClick={() => {
                  // Try to retry the action if possible
                  resolveError(error.id);
                }}
              >
                <RotateCcw className="w-4 h-4 mr-2" />
                Retry Action
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  // Open external help documentation
                  window.open('https://docs.example.com/troubleshooting', '_blank');
                }}
              >
                <ExternalLink className="w-4 h-4 mr-2" />
                Help Docs
              </Button>
            </div>
          </div>
        </Card>
      </motion.div>
    );
  }
  
  // Bottom-right error notification
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 20 }}
      className={getPositionClasses()}
    >
      <Card className="p-4 bg-red-50 border-red-200 max-w-sm">
        <div className="flex items-start gap-3">
          <AlertTriangle className="w-5 h-5 text-red-600 flex-shrink-0 mt-0.5" />
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between">
              <h4 className="font-medium text-sm text-red-700">
                {filteredErrors.length} Error{filteredErrors.length > 1 ? 's' : ''} Detected
              </h4>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  // Resolve all errors
                  filteredErrors.forEach(error => resolveError(error.id));
                }}
                className="h-6 w-6 p-0"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
            
            <div className="mt-2 space-y-2 max-h-40 overflow-y-auto">
              {filteredErrors.map((error) => (
                <motion.div
                  key={error.id}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  className={`p-2 rounded text-xs ${
                    expandedError === error.id 
                      ? 'bg-red-100' 
                      : 'bg-red-50 hover:bg-red-100'
                  }`}
                >
                  <div 
                    className="flex items-center justify-between cursor-pointer"
                    onClick={() => setExpandedError(
                      expandedError === error.id ? null : error.id
                    )}
                  >
                    <span className="truncate">
                      {error.error.message.substring(0, 30)}
                      {error.error.message.length > 30 ? '...' : ''}
                    </span>
                    <div className="flex gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          resolveError(error.id);
                        }}
                        className="h-5 w-5 p-0"
                      >
                        <Check className="w-3 h-3" />
                      </Button>
                    </div>
                  </div>
                  
                  {expandedError === error.id && (
                    <div className="mt-2 pt-2 border-t border-red-200">
                      <p className="text-red-700 font-medium text-xs">
                        {error.error.name || 'Error'}
                      </p>
                      <p className="text-xs text-muted-foreground mt-1">
                        {error.timestamp.toLocaleTimeString()}
                      </p>
                      
                      {error.recoverySuggestions && error.recoverySuggestions.length > 0 && (
                        <div className="mt-2">
                          <p className="text-xs font-medium mb-1">Suggestions:</p>
                          <ul className="text-xs space-y-1">
                            {error.recoverySuggestions.slice(0, 2).map((suggestion, idx) => (
                              <li key={idx} className="flex items-start gap-1">
                                <div className="w-1.5 h-1.5 bg-red-600 rounded-full mt-1.5 flex-shrink-0"></div>
                                <span>{suggestion}</span>
                              </li>
                            ))}
                            {error.recoverySuggestions.length > 2 && (
                              <li className="text-xs text-muted-foreground">
                                +{error.recoverySuggestions.length - 2} more suggestions
                              </li>
                            )}
                          </ul>
                        </div>
                      )}
                    </div>
                  )}
                </motion.div>
              ))}
            </div>
            
            <div className="flex gap-2 mt-3">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  // Show modal with detailed error information
                }}
                className="h-7 text-xs flex-1"
              >
                <ExternalLink className="w-3 h-3 mr-1" />
                Details
              </Button>
              
              <Button
                variant="success"
                size="sm"
                onClick={() => {
                  // Try to resolve all errors
                  filteredErrors.forEach(error => resolveError(error.id));
                }}
                className="h-7 text-xs flex-1"
              >
                <Check className="w-3 h-3 mr-1" />
                Resolve All
              </Button>
            </div>
          </div>
        </div>
      </Card>
    </motion.div>
  );
};

// Error Boundary Component
export class ErrorBoundary extends React.Component<{
  children: React.ReactNode;
  fallback?: React.ReactNode;
}, {
  hasError: boolean;
  error?: Error;
}> {
  constructor(props: { children: React.ReactNode; fallback?: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // Log error to feedback system
    // In a real implementation, you would use the feedback context here
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || (
        <Card className="p-6 text-center">
          <AlertTriangle className="w-12 h-12 text-red-600 mx-auto mb-4" />
          <h3 className="font-medium text-lg mb-2">Something went wrong</h3>
          <p className="text-muted-foreground mb-4">
            We're sorry, but something unexpected happened. Please try again.
          </p>
          <Button
            variant="data"
            onClick={() => window.location.reload()}
          >
            <RotateCcw className="w-4 h-4 mr-2" />
            Reload Page
          </Button>
        </Card>
      );
    }

    return this.props.children;
  }
}