import React from 'react';
import { motion } from 'framer-motion';
import { 
  Play, 
  Pause, 
  CheckCircle, 
  XCircle, 
  Clock, 
  Loader2,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Trash2
} from 'lucide-react';
import { useFeedback } from './FeedbackProvider';
import { Card, Button, Badge } from '../ui/StyledComponents';
import { JobStatus } from './FeedbackProvider';

interface JobTrackerProps {
  jobId?: string; // Track specific job, or show all if not provided
  compact?: boolean; // Show compact view
}

export const JobTracker: React.FC<JobTrackerProps> = ({ jobId, compact = false }) => {
  const { jobs, updateJob, removeJob, cancelJob } = useFeedback();
  
  // Filter jobs if specific jobId is provided
  const filteredJobs = jobId ? jobs.filter(job => job.id === jobId) : jobs;
  
  // Get icon for job status
  const getStatusIcon = (status: JobStatus) => {
    switch (status) {
      case 'running':
        return <Loader2 className="w-4 h-4 animate-spin text-blue-600" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-600" />;
      case 'cancelled':
        return <XCircle className="w-4 h-4 text-gray-600" />;
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-600" />;
      default:
        return <Clock className="w-4 h-4 text-gray-600" />;
    }
  };

  // Get status badge variant
  const getStatusVariant = (status: JobStatus) => {
    switch (status) {
      case 'running':
        return 'data';
      case 'completed':
        return 'success';
      case 'failed':
        return 'error';
      case 'cancelled':
        return 'secondary';
      case 'pending':
        return 'warning';
      default:
        return 'secondary';
    }
  };

  // Format duration
  const formatDuration = (startTime?: Date, endTime?: Date) => {
    if (!startTime) return '';
    const end = endTime || new Date();
    const duration = end.getTime() - startTime.getTime();
    const seconds = Math.floor(duration / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  };

  // Retry job
  const retryJob = (id: string) => {
    updateJob(id, { 
      status: 'pending', 
      progress: 0, 
      startTime: new Date(),
      endTime: undefined,
      error: undefined
    });
  };

  if (compact) {
    return (
      <div className="space-y-2">
        {filteredJobs.map(job => (
          <motion.div
            key={job.id}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex items-center justify-between p-2 bg-muted rounded"
          >
            <div className="flex items-center gap-2 min-w-0">
              {getStatusIcon(job.status)}
              <div className="min-w-0">
                <p className="text-sm font-medium truncate">{job.name}</p>
                {job.message && (
                  <p className="text-xs text-muted-foreground truncate">{job.message}</p>
                )}
              </div>
            </div>
            <div className="flex items-center gap-1">
              {job.status === 'running' && (
                <span className="text-xs text-muted-foreground">{Math.round(job.progress)}%</span>
              )}
              <Badge variant={getStatusVariant(job.status)} className="text-xs">
                {job.status}
              </Badge>
            </div>
          </motion.div>
        ))}
      </div>
    );
  }

  return (
    <Card className="p-0">
      <div className="p-4 border-b">
        <h3 className="font-semibold flex items-center gap-2">
          <Loader2 className="w-5 h-5" />
          Job Tracker
        </h3>
        {jobs.length > 0 && (
          <p className="text-sm text-muted-foreground mt-1">
            {jobs.filter(j => j.status === 'running').length} running, 
            {jobs.filter(j => j.status === 'completed').length} completed
          </p>
        )}
      </div>
      
      <div className="max-h-[50vh] overflow-y-auto">
        {filteredJobs.length === 0 ? (
          <div className="p-8 text-center text-muted-foreground">
            <Loader2 className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p className="text-sm">No jobs running</p>
          </div>
        ) : (
          <div className="divide-y">
            {filteredJobs.map(job => (
              <motion.div
                key={job.id}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="p-4 hover:bg-muted/50 transition-colors"
              >
                <div className="flex items-start justify-between gap-3">
                  <div className="flex items-start gap-3 min-w-0">
                    {getStatusIcon(job.status)}
                    <div className="min-w-0 flex-1">
                      <div className="flex items-center gap-2">
                        <h4 className="font-medium text-sm truncate">{job.name}</h4>
                        <Badge variant={getStatusVariant(job.status)} className="text-xs">
                          {job.status}
                        </Badge>
                      </div>
                      
                      {job.message && (
                        <p className="text-sm text-muted-foreground mt-1">{job.message}</p>
                      )}
                      
                      <div className="mt-2 flex items-center gap-4 text-xs text-muted-foreground">
                        {job.startTime && (
                          <span>Started: {job.startTime.toLocaleTimeString()}</span>
                        )}
                        {job.endTime && (
                          <span>Duration: {formatDuration(job.startTime, job.endTime)}</span>
                        )}
                        {job.performance && (
                          <>
                            {job.performance.duration && (
                              <span>{job.performance.duration}ms</span>
                            )}
                            {job.performance.memoryUsage && (
                              <span>{(job.performance.memoryUsage / 1024 / 1024).toFixed(1)}MB</span>
                            )}
                          </>
                        )}
                      </div>
                      
                      {job.status === 'running' && (
                        <div className="mt-2">
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <motion.div 
                              className="bg-blue-600 h-2 rounded-full"
                              initial={{ width: 0 }}
                              animate={{ width: `${job.progress}%` }}
                              transition={{ duration: 0.3 }}
                            />
                          </div>
                          <div className="flex justify-between text-xs text-muted-foreground mt-1">
                            <span>{Math.round(job.progress)}% complete</span>
                          </div>
                        </div>
                      )}
                      
                      {job.error && (
                        <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-xs text-red-700">
                          <div className="flex items-center gap-1">
                            <AlertTriangle className="w-3 h-3" />
                            <span className="font-medium">Error:</span>
                          </div>
                          <p className="mt-1">{job.error}</p>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex flex-col gap-1">
                    {job.status === 'running' && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => cancelJob(job.id)}
                        className="h-8 w-8 p-0"
                      >
                        <XCircle className="w-4 h-4" />
                      </Button>
                    )}
                    
                    {job.status === 'failed' && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => retryJob(job.id)}
                        className="h-8 w-8 p-0"
                      >
                        <RotateCcw className="w-4 h-4" />
                      </Button>
                    )}
                    
                    {(job.status === 'completed' || job.status === 'cancelled' || job.status === 'failed') && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeJob(job.id)}
                        className="h-8 w-8 p-0"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    )}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </div>
    </Card>
  );
};

// Job Progress Bar Component
export const JobProgressBar: React.FC<{
  jobId: string;
  showLabel?: boolean;
}> = ({ jobId, showLabel = true }) => {
  const { jobs } = useFeedback();
  const job = jobs.find(j => j.id === jobId);
  
  if (!job) return null;
  
  return (
    <div className="w-full">
      {showLabel && (
        <div className="flex justify-between text-sm mb-1">
          <span>{job.name}</span>
          <span>{Math.round(job.progress)}%</span>
        </div>
      )}
      <div className="w-full bg-gray-200 rounded-full h-2">
        <motion.div 
          className={`h-2 rounded-full ${
            job.status === 'completed' ? 'bg-green-600' :
            job.status === 'failed' ? 'bg-red-600' :
            'bg-blue-600'
          }`}
          initial={{ width: 0 }}
          animate={{ width: `${job.progress}%` }}
          transition={{ duration: 0.3 }}
        />
      </div>
      {job.message && (
        <p className="text-xs text-muted-foreground mt-1">{job.message}</p>
      )}
    </div>
  );
};