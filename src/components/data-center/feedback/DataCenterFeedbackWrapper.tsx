import React, { useEffect } from 'react';
import { 
  FeedbackProvider,
  NotificationCenter,
  JobTracker,
  ActionFeedback,
  ErrorRecovery,
  PerformanceIndicators,
  useFeedback
} from './index';
import { useDataCenter } from '../DataCenterContext';

// Wrapper component that connects feedback system to DataCenterContext
const DataCenterFeedbackContent: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { 
    addNotification, 
    addJob, 
    updateJob,
    addPerformanceMetric,
    setActionFeedback,
    addError
  } = useFeedback();
  
  const { 
    pipelineState,
    updatePipelineState,
    createSavePoint,
    loadSavePoint,
    deleteSavePoint
  } = useDataCenter();
  
  // Monitor pipeline state changes and create notifications
  useEffect(() => {
    if (pipelineState.pipelineHistory.length > 0) {
      const lastHistoryItem = pipelineState.pipelineHistory[pipelineState.pipelineHistory.length - 1];
      
      // Create notifications for important pipeline events
      if (lastHistoryItem.status === 'completed') {
        addNotification({
          type: 'success',
          title: `Stage ${lastHistoryItem.stage} completed`,
          message: lastHistoryItem.details || `Successfully completed ${lastHistoryItem.stage} stage`,
          autoDismiss: true,
          dismissAfter: 3000
        });
      } else if (lastHistoryItem.status === 'error') {
        addNotification({
          type: 'error',
          title: `Stage ${lastHistoryItem.stage} failed`,
          message: lastHistoryItem.details || `Failed to complete ${lastHistoryItem.stage} stage`,
          autoDismiss: false
        });
      }
    }
  }, [pipelineState.pipelineHistory, addNotification]);
  
  // Monitor performance metrics from DataCenterContext
  useEffect(() => {
    // Add cache stats as performance metrics
    if (pipelineState.performance.cacheStats) {
      const { hitRate, missRate, cacheSize } = pipelineState.performance.cacheStats;
      
      addPerformanceMetric({
        name: 'cacheHitRate',
        value: hitRate * 100,
        unit: '%',
        threshold: 80,
        alert: hitRate < 0.7
      });
      
      addPerformanceMetric({
        name: 'cacheSize',
        value: cacheSize,
        unit: 'items',
        threshold: 1000,
        alert: cacheSize > 10000
      });
    }
  }, [pipelineState.performance.cacheStats, addPerformanceMetric]);
  
  // Monitor file upload and processing
  useEffect(() => {
    if (pipelineState.fileName && pipelineState.uploadDate) {
      addNotification({
        type: 'info',
        title: 'File Uploaded',
        message: `${pipelineState.fileName} (${(pipelineState.fileSize / 1024).toFixed(2)} KB) uploaded successfully`,
        autoDismiss: true,
        dismissAfter: 5000
      });
    }
  }, [pipelineState.fileName, pipelineState.fileSize, pipelineState.uploadDate, addNotification]);
  
  // Monitor SQL generation
  useEffect(() => {
    if (pipelineState.sqlResult) {
      if (pipelineState.sqlResult.isValid) {
        addNotification({
          type: 'success',
          title: 'SQL Generated',
          message: `SQL query generated with ${pipelineState.sqlResult.sql.split('\n').length} lines`,
          autoDismiss: true,
          dismissAfter: 5000,
          actions: [
            {
              label: 'Copy SQL',
              action: () => {
                navigator.clipboard.writeText(pipelineState.sqlResult?.sql || '');
              }
            },
            {
              label: 'Download',
              action: () => {
                // Trigger download in parent component
              }
            }
          ]
        });
      } else {
        addNotification({
          type: 'error',
          title: 'SQL Generation Failed',
          message: pipelineState.sqlResult.error || 'Failed to generate SQL query',
          autoDismiss: false
        });
      }
    }
  }, [pipelineState.sqlResult, addNotification]);
  
  // Monitor save points
  useEffect(() => {
    if (pipelineState.savePoints.length > 0) {
      const lastSavePoint = pipelineState.savePoints[pipelineState.savePoints.length - 1];
      
      addNotification({
        type: 'info',
        title: 'Save Point Created',
        message: `Save point "${lastSavePoint.name}" created`,
        autoDismiss: true,
        dismissAfter: 3000
      });
    }
  }, [pipelineState.savePoints, addNotification]);
  
  return (
    <>
      {children}
      
      {/* Global feedback components */}
      <ActionFeedback />
      <ErrorRecovery />
      <NotificationCenter />
    </>
  );
};

// Main wrapper component
export const DataCenterFeedbackWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <FeedbackProvider>
      <DataCenterFeedbackContent>
        {children}
      </DataCenterFeedbackContent>
    </FeedbackProvider>
  );
};

// Performance monitoring component for DataCenter
export const DataCenterPerformanceMonitor: React.FC = () => {
  return (
    <div className="p-4">
      <h3 className="font-medium mb-4">Performance Monitoring</h3>
      <PerformanceIndicators />
    </div>
  );
};

// Job tracking component for DataCenter
export const DataCenterJobTracker: React.FC = () => {
  return (
    <div className="p-4">
      <h3 className="font-medium mb-4">Background Jobs</h3>
      <JobTracker />
    </div>
  );
};

// Error monitoring component for DataCenter
export const DataCenterErrorMonitor: React.FC = () => {
  return (
    <div className="p-4">
      <h3 className="font-medium mb-4">Error Monitoring</h3>
      <ErrorRecovery showResolved />
    </div>
  );
};