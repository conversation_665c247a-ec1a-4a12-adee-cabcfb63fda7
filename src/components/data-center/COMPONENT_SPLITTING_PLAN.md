# Component Splitting Plan for UnifiedDataCenter.tsx

## Current State Analysis

**File Size**: 1,405 lines  
**Main Issues**:
- Single massive component handling multiple responsibilities
- 100+ imports from various modules
- Complex state management mixed with UI logic
- Multiple provider wrappers creating deep nesting
- Difficult to test and maintain

## Proposed Component Architecture

### 1. Core Layout Components

#### `DataCenterLayout.tsx` (Main Container)
```typescript
// Handles overall layout structure and sidebar management
interface DataCenterLayoutProps {
  children: React.ReactNode;
  sidebar?: React.ReactNode;
  metadataSidebar?: React.ReactNode;
  showSidebar: boolean;
  showMetadataSidebar: boolean;
  onToggleSidebar: () => void;
  onToggleMetadataSidebar: () => void;
}
```

#### `DataCenterHeader.tsx` (Top Navigation)
```typescript
// Contains breadcrumbs, view switcher, and action buttons
interface DataCenterHeaderProps {
  activeView: string;
  onViewChange: (view: string) => void;
  onBack?: () => void;
  showControlPanel: boolean;
  onToggleControlPanel: () => void;
}
```

#### `DataCenterSidebar.tsx` (Left Navigation)
```typescript
// Navigation, quick actions, and stage indicators
interface DataCenterSidebarProps {
  currentStage: string;
  onStageChange: (stage: string) => void;
  collapsed: boolean;
  onToggleCollapse: () => void;
}
```

#### `MetadataSidebar.tsx` (Right Panel)
```typescript
// File metadata, help, and storage overview
interface MetadataSidebarProps {
  selectedFile?: File;
  onClose: () => void;
}
```

### 2. Feature-Specific Components

#### `FileUploadZone.tsx`
```typescript
// Handles file upload, drag & drop, and initial processing
interface FileUploadZoneProps {
  onFileUpload: (files: File[]) => void;
  acceptedTypes: string[];
  maxSize: number;
  isProcessing: boolean;
}
```

#### `DataPreviewPanel.tsx`
```typescript
// Shows data preview with pagination and filtering
interface DataPreviewPanelProps {
  data: DataPreview;
  onDataChange: (data: DataPreview) => void;
  showStatistics: boolean;
}
```

#### `ProcessingPanel.tsx`
```typescript
// Handles data processing, Python execution, and results
interface ProcessingPanelProps {
  data: DataPreview;
  onProcessingComplete: (result: ProcessingResult) => void;
  templates: Record<string, any>;
}
```

#### `SQLQueryPanel.tsx`
```typescript
// SQL generation, editing, and execution
interface SQLQueryPanelProps {
  data: DataPreview;
  onQueryGenerate: (sql: string) => void;
  onQueryExecute: (sql: string) => void;
}
```

#### `DashboardView.tsx`
```typescript
// Dashboard with widgets and visualizations
interface DashboardViewProps {
  widgets: string[];
  data: DataPreview;
  onWidgetToggle: (widget: string) => void;
}
```

### 3. Provider Components

#### `DataCenterProviders.tsx`
```typescript
// Consolidates all providers into a single wrapper
interface DataCenterProvidersProps {
  config: DataCenterConfig;
  children: React.ReactNode;
  onDataChange?: (data: any) => void;
}
```

#### Individual Provider Wrappers:
- `AccessibilityProviders.tsx`
- `NavigationProviders.tsx`
- `FeedbackProviders.tsx`
- `OnboardingProviders.tsx`

### 4. Hook Abstractions

#### `useDataCenterState.tsx`
```typescript
// Consolidates all state management logic
interface DataCenterState {
  // File and data state
  selectedFile: File | null;
  dataPreview: DataPreview | null;
  processingResult: ProcessingResult | null;
  
  // UI state
  activeView: string;
  currentStage: string;
  isProcessing: boolean;
  
  // Sidebar state
  sidebarCollapsed: boolean;
  showMetadataSidebar: boolean;
  showControlPanel: boolean;
}
```

#### `useDataCenterActions.tsx`
```typescript
// Consolidates all action handlers
interface DataCenterActions {
  // File actions
  handleFileUpload: (files: File[]) => void;
  handleFileSelect: (file: File) => void;
  
  // Processing actions
  handleDataProcess: (template: string) => void;
  handleSQLGenerate: () => void;
  
  // UI actions
  handleViewChange: (view: string) => void;
  handleStageChange: (stage: string) => void;
}
```

### 5. View Components

#### `MainContentRenderer.tsx`
```typescript
// Renders different views based on activeView and currentStage
interface MainContentRendererProps {
  activeView: string;
  currentStage: string;
  data: DataPreview | null;
  processingResult: ProcessingResult | null;
}
```

#### Individual View Components:
- `UploadView.tsx`
- `PreviewView.tsx`
- `ProcessView.tsx`
- `SQLView.tsx`
- `DashboardView.tsx`
- `GridView.tsx`

## Implementation Strategy

### Phase 1: Extract Layout Components (Week 1)
1. Create `DataCenterLayout.tsx` with basic structure
2. Extract `DataCenterHeader.tsx` with navigation
3. Create `DataCenterSidebar.tsx` for left panel
4. Extract `MetadataSidebar.tsx` for right panel

### Phase 2: Extract Feature Components (Week 2)
1. Create `FileUploadZone.tsx` with drag & drop
2. Extract `DataPreviewPanel.tsx` with table view
3. Create `ProcessingPanel.tsx` with Python execution
4. Extract `SQLQueryPanel.tsx` with query builder

### Phase 3: Consolidate Providers (Week 3)
1. Create `DataCenterProviders.tsx` wrapper
2. Organize individual provider components
3. Simplify provider nesting and configuration

### Phase 4: Extract State Management (Week 4)
1. Create `useDataCenterState.tsx` hook
2. Create `useDataCenterActions.tsx` hook
3. Refactor components to use new hooks

### Phase 5: Create View System (Week 5)
1. Create `MainContentRenderer.tsx`
2. Extract individual view components
3. Implement view routing and transitions

## File Structure After Refactoring

```
data-center/
├── core/
│   ├── DataCenterLayout.tsx
│   ├── DataCenterHeader.tsx
│   ├── DataCenterSidebar.tsx
│   ├── MetadataSidebar.tsx
│   └── MainContentRenderer.tsx
├── features/
│   ├── upload/
│   │   └── FileUploadZone.tsx
│   ├── preview/
│   │   └── DataPreviewPanel.tsx
│   ├── processing/
│   │   └── ProcessingPanel.tsx
│   ├── sql/
│   │   └── SQLQueryPanel.tsx
│   └── dashboard/
│       └── DashboardView.tsx
├── providers/
│   ├── DataCenterProviders.tsx
│   ├── AccessibilityProviders.tsx
│   ├── NavigationProviders.tsx
│   ├── FeedbackProviders.tsx
│   └── OnboardingProviders.tsx
├── hooks/
│   ├── useDataCenterState.tsx
│   ├── useDataCenterActions.tsx
│   └── useDataCenterConfig.tsx
├── views/
│   ├── UploadView.tsx
│   ├── PreviewView.tsx
│   ├── ProcessView.tsx
│   ├── SQLView.tsx
│   ├── DashboardView.tsx
│   └── GridView.tsx
└── DataCenter.tsx (main entry point)
```

## Benefits of This Approach

1. **Maintainability**: Each component has a single responsibility
2. **Testability**: Smaller components are easier to unit test
3. **Reusability**: Components can be reused across different contexts
4. **Performance**: Better code splitting and lazy loading opportunities
5. **Developer Experience**: Easier to understand and modify
6. **Type Safety**: Better TypeScript inference with focused interfaces

## Migration Strategy

1. **Backward Compatibility**: Keep original `UnifiedDataCenter.tsx` as a wrapper
2. **Gradual Migration**: Extract components one by one
3. **Testing**: Ensure each extracted component works independently
4. **Documentation**: Update component documentation as we go
5. **Performance Monitoring**: Track bundle size and performance metrics

## Next Steps

1. Start with Phase 1 (Layout Components)
2. Create basic component shells with proper TypeScript interfaces
3. Gradually move logic from `UnifiedDataCenter.tsx` to new components
4. Update imports and dependencies
5. Test each component thoroughly before moving to the next phase