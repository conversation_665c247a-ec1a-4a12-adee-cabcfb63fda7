import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { StorageFile } from '@/lib/services/browserStorageManager';
import { HistoryManager } from './utils/stateManagement';

export interface DataPreview {
  headers: string[];
  rows: any[][];
  totalRows: number;
  dataTypes?: Record<string, string>;
  statistics?: Record<string, any>;
  fullData?: any[][]; // Store full dataset for exploration
}

export interface ProcessingResult {
  content: string;
  metadata: Record<string, any>;
  chunks?: Array<{
    text: string;
    metadata: Record<string, any>;
  }>;
  embeddings?: number[][];
}

export interface SQLGenerationResult {
  sql: string;
  isValid: boolean;
  error?: string;
  analysis?: string;
}

export interface DataPipelineState {
  // File information
  fileName: string;
  fileType: string;
  fileSize: number;
  uploadDate: Date;
  
  // Data preview
  dataPreview: DataPreview | null;
  
  // Processing results
  processingResult: ProcessingResult | null;
  pythonScript: string;
  pythonOutput: string;
  selectedTemplate: string;
  
  // SQL generation
  sqlResult: SQLGenerationResult | null;
  
  // Pipeline tracking
  currentStage: 'upload' | 'preview' | 'explore' | 'processing' | 'analysis' | 'sql';
  pipelineHistory: Array<{
    stage: string;
    timestamp: Date;
    status: 'completed' | 'pending' | 'error';
    details?: string;
  }>;
  
  // Storage reference
  storageFile: StorageFile | null;
  
  // User preferences
  preferences: {
    viewMode: 'grid' | 'list' | 'tree';
    theme: 'light' | 'dark' | 'system';
    showSchema: boolean;
    autoRefresh: boolean;
  };
  
  // Filters
  filters: Record<string, any>;
  
  // Context tracking for persistent state between tabs
  context: {
    breadcrumbPath: Array<{
      stage: string;
      label: string;
      timestamp: Date;
      dataSummary?: {
        rows?: number;
        columns?: number;
        fileType?: string;
      };
    }>;
    currentData: any; // Persistent data context across tabs
    activeFilters: Record<string, any>;
    processingMetadata: Record<string, any>;
  };
}

interface DataContextType {
  pipelineState: DataPipelineState;
  updatePipelineState: (updates: Partial<DataPipelineState>, notifyParent?: boolean) => void;
  resetPipeline: () => void;
  navigateToStage: (stage: DataPipelineState['currentStage']) => void;
  updateContext: (contextUpdates: Partial<DataPipelineState['context']>) => void;
  addToBreadcrumb: (stage: string, label: string, dataSummary?: any) => void;
  historyManager: HistoryManager;
  canUndo: boolean;
  canRedo: boolean;
  undo: () => void;
  redo: () => void;
}

const DataContext = createContext<DataContextType | undefined>(undefined);

export const DataCenterProvider: React.FC<{ 
  children: ReactNode;
  onDataChange?: (data: Partial<DataPipelineState>) => void;
}> = ({ children, onDataChange }) => {
  const [pipelineState, setPipelineState] = useState<DataPipelineState>({
    fileName: '',
    fileType: '',
    fileSize: 0,
    uploadDate: new Date(),
    dataPreview: null,
    processingResult: null,
    pythonScript: '',
    pythonOutput: '',
    selectedTemplate: '',
    sqlResult: null,
    currentStage: 'upload',
    pipelineHistory: [],
    storageFile: null,
    preferences: {
      viewMode: 'grid',
      theme: 'system',
      showSchema: true,
      autoRefresh: true
    },
    filters: {},
    context: {
      breadcrumbPath: [],
      currentData: null,
      activeFilters: {},
      processingMetadata: {}
    }
  });

  // Initialize history manager
  const [historyManager] = useState(() => new HistoryManager(50));
  const [canUndo, setCanUndo] = useState(false);
  const [canRedo, setCanRedo] = useState(false);

  // Update undo/redo state
  useEffect(() => {
    setCanUndo(historyManager.canUndo());
    setCanRedo(historyManager.canRedo());
  }, [historyManager, pipelineState]);

  const updateContext = (contextUpdates: Partial<DataPipelineState['context']>) => {
    updatePipelineState({
      context: {
        ...pipelineState.context,
        ...contextUpdates
      }
    });
  };

  const addToBreadcrumb = (stage: string, label: string, dataSummary?: any) => {
    const newBreadcrumb = {
      stage,
      label,
      timestamp: new Date(),
      dataSummary
    };
    
    updateContext({
      breadcrumbPath: [...pipelineState.context.breadcrumbPath, newBreadcrumb]
    });
  };

  const updatePipelineState = (updates: Partial<DataPipelineState>, notifyParent: boolean = true) => {
    setPipelineState(prev => {
      const newState = { ...prev, ...updates };
      
      // Auto-add to history if stage changes
      if (updates.currentStage && updates.currentStage !== prev.currentStage) {
        const historyEntry = {
          stage: updates.currentStage,
          timestamp: new Date(),
          status: 'completed' as const,
          details: `Navigated to ${updates.currentStage} stage`
        };
        
        newState.pipelineHistory = [...prev.pipelineHistory, historyEntry];
      }
      
      // Add to history manager for undo/redo
      historyManager.push('state_update', newState);
      
      // Notify parent component if callback exists and notifyParent is true
      if (onDataChange && notifyParent) {
        onDataChange(updates);
      }
      
      return newState;
    });
  };

  const resetPipeline = () => {
    const resetState = {
      fileName: '',
      fileType: '',
      fileSize: 0,
      uploadDate: new Date(),
      dataPreview: null,
      processingResult: null,
      pythonScript: '',
      pythonOutput: '',
      selectedTemplate: '',
      sqlResult: null,
      currentStage: 'upload',
      pipelineHistory: [],
      storageFile: null,
      preferences: {
        viewMode: 'grid' as const,
        theme: 'system' as const,
        showSchema: true,
        autoRefresh: true
      },
      filters: {},
      context: {
        breadcrumbPath: [],
        currentData: null,
        activeFilters: {},
        processingMetadata: {}
      }
    };
    
    setPipelineState(resetState);
    historyManager.push('reset_pipeline', resetState);
  };

  const navigateToStage = (stage: DataPipelineState['currentStage']) => {
    updatePipelineState({ currentStage: stage });
  };

  const undo = () => {
    const previous = historyManager.undo();
    if (previous) {
      setPipelineState(previous.state);
    }
  };

  const redo = () => {
    const next = historyManager.redo();
    if (next) {
      setPipelineState(next.state);
    }
  };

  return (
    <DataContext.Provider value={{ 
      pipelineState, 
      updatePipelineState, 
      resetPipeline,
      navigateToStage,
      updateContext,
      addToBreadcrumb,
      historyManager,
      canUndo,
      canRedo,
      undo,
      redo
    }}>
      {children}
    </DataContext.Provider>
  );
};

export const useDataCenter = () => {
  const context = useContext(DataContext);
  if (context === undefined) {
    throw new Error('useDataCenter must be used within a DataCenterProvider');
  }
  return context;
};