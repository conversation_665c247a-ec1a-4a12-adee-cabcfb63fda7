import { ViewType, StageType, ProcessingStatus, FileType } from './enums';

// Core data types
export interface DataPreview {
  headers: string[];
  rows: any[][];
  totalRows: number;
  dataTypes?: Record<string, string>;
  statistics?: Record<string, any>;
  fullData?: any[][];
}

export interface ProcessingResult {
  content: string;
  metadata: Record<string, any>;
  chunks?: Array<{
    text: string;
    metadata: Record<string, any>;
  }>;
  embeddings?: number[][];
  recordsProcessed?: number;
  duration?: number;
  outputSize?: number;
}

export interface SQLGenerationResult {
  sql: string;
  isValid: boolean;
  error?: string;
  analysis?: string;
}

export interface FileMetadata {
  id: string;
  name: string;
  type: FileType;
  size: number;
  lastModified: Date;
  uploadTime: Date;
  rowCount?: number;
  columnCount?: number;
}

export interface ProcessingTemplate {
  id: string;
  name: string;
  description: string;
  category: 'NLP' | 'ML' | 'Analytics';
}

// Component state types
export interface DataCenterState {
  selectedFile: File | null;
  dataPreview: DataPreview | null;
  processingResult: ProcessingResult | null;
  sqlResult: SQLGenerationResult | null;
  activeView: ViewType;
  currentStage: StageType;
  isProcessing: boolean;
  sidebarCollapsed: boolean;
  showMetadataSidebar: boolean;
  showControlPanel: boolean;
  contextMenu: {
    visible: boolean;
    x: number;
    y: number;
  };
}

export interface DataCenterActions {
  // File operations
  handleFileUpload: (files: File[]) => void;
  handleFileSelect: (file: File) => void;
  handleFileRemove: (file: File) => void;

  // Data operations
  handleDataProcess: (template: string) => void;
  handleSQLGenerate: () => void;
  handleSQLExecute: (sql: string) => void;

  // UI operations
  handleViewChange: (view: ViewType) => void;
  handleStageChange: (stage: StageType) => void;
  handleSidebarToggle: () => void;
  handleMetadataSidebarToggle: () => void;
  handleControlPanelToggle: () => void;

  // Context menu operations
  showContextMenu: (x: number, y: number) => void;
  hideContextMenu: () => void;
}

export interface UseDataCenterStateReturn {
  state: DataCenterState;
  actions: DataCenterActions;
}

// Component props types
export interface DataCenterProps {
  config?: Partial<DataCenterConfig>;
  onBack?: () => void;
  onDataChange?: (data: Partial<any>) => void;
  className?: string;
}

export interface DataCenterConfig {
  features: {
    dashboard: boolean;
    storytelling: boolean;
    streamlinedNavigation: boolean;
    userFeedback: boolean;
    accessibilityFeatures: boolean;
    versionHistory: boolean;
    quickActions: boolean;
  };
  performance: {
    maxFileSize: number;
    chunkSize: number;
    workerPoolSize: number;
    cacheEnabled: boolean;
  };
}

export interface BreadcrumbItem {
  stage: string;
  label: string;
  timestamp: Date;
  dataSummary?: {
    rows?: number;
    columns?: number;
    fileType?: string;
  };
}