import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FileText, X } from 'lucide-react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Sheet, SheetContent, SheetHeader, SheetTitle } from '@/components/ui/sheet';

// Components
import { FileUploadZone } from './components/FileUploadZone';
import { DataPreviewPanel } from './components/DataPreviewPanel';
import { ProcessingPanel } from './components/ProcessingPanel';
import { DashboardView } from './components/DashboardView';
import { NavigationBar } from './components/NavigationBar';
import { BreadcrumbNavigation } from './components/BreadcrumbNavigation';
import { Sidebar } from './components/Sidebar';

// Hooks and utilities
import { useDataCenterState } from './hooks/useDataCenterState';
import { ViewType, StageType, ProcessingStatus } from './enums';
import { mockQuery, mockRootProps } from './DataCenterMockData';

interface RedesignedDataCenterProps {
  className?: string;
  onBack?: () => void;
}

export const RedesignedDataCenter: React.FC<RedesignedDataCenterProps> = ({ 
  className,
  onBack 
}) => {
  const { state, actions } = useDataCenterState();
  
  // Additional UI state
  const [showMetadataSidebar, setShowMetadataSidebar] = useState(false);
  const [showControlPanel, setShowControlPanel] = useState(false);
  const [processingStatus, setProcessingStatus] = useState<ProcessingStatus>(ProcessingStatus.IDLE);
  const [processingProgress, setProcessingProgress] = useState(0);

  // Mock data
  const breadcrumbItems = mockRootProps.breadcrumbPath;
  const processingTemplates = mockQuery.processingTemplates;

  // File upload handler
  const handleFileUpload = useCallback(async (files: File[]) => {
    if (files.length === 0) return;
    
    const file = files[0];
    actions.handleFileUpload(files);
    
    // Simulate file processing
    setProcessingStatus(ProcessingStatus.PROCESSING);
    setProcessingProgress(0);
    
    const interval = setInterval(() => {
      setProcessingProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setProcessingStatus(ProcessingStatus.SUCCESS);
          return 100;
        }
        return prev + 10;
      });
    }, 200);
  }, [actions]);

  // Data processing handler
  const handleDataProcess = useCallback(() => {
    actions.handleStageChange(StageType.PROCESSING);
    setProcessingStatus(ProcessingStatus.PROCESSING);
    setProcessingProgress(0);
    
    // Simulate processing
    setTimeout(() => {
      setProcessingStatus(ProcessingStatus.SUCCESS);
      setProcessingProgress(100);
    }, 3000);
  }, [actions]);

  // Render main content based on current view and stage
  const renderMainContent = () => {
    if (state.activeView === ViewType.DASHBOARD) {
      return <DashboardView data={state.dataPreview} />;
    }

    // Stage-based rendering for other views
    switch (state.currentStage) {
      case StageType.UPLOAD:
        return (
          <div className="max-w-4xl mx-auto">
            <FileUploadZone
              onFileUpload={handleFileUpload}
              acceptedTypes={['csv', 'xlsx', 'json', 'txt']}
              maxSize={100}
              isProcessing={state.isProcessing}
            />
            
            {/* Recent Files */}
            <Card className="mt-8 card-data">
              <div className="p-6">
                <h3 className="text-lg font-semibold mb-4">Recent Files</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {mockQuery.recentFiles.map((file) => (
                    <Card 
                      key={file.id}
                      className="p-4 hover:shadow-md transition-all cursor-pointer interactive-lift"
                      onClick={() => {
                        // Load sample data
                        const mockFile = new File([''], file.name, { type: 'text/csv' });
                        actions.handleFileSelect(mockFile);
                        actions.handleStageChange(StageType.PREVIEW);
                      }}
                    >
                      <div className="flex items-center gap-3">
                        <FileText className="w-8 h-8 text-data-primary" />
                        <div>
                          <p className="font-medium">{file.name}</p>
                          <p className="text-sm text-muted-foreground">
                            {file.rowCount?.toLocaleString()} rows • {(file.size / 1024 / 1024).toFixed(1)} MB
                          </p>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              </div>
            </Card>
          </div>
        );

      case StageType.PREVIEW:
        return state.dataPreview ? (
          <div className="max-w-6xl mx-auto">
            <DataPreviewPanel
              data={state.dataPreview}
              onProcessData={handleDataProcess}
              onGenerateStory={() => actions.handleStageChange(StageType.STORYTELLING)}
              onShowMetadata={() => setShowMetadataSidebar(true)}
              fileName={state.selectedFile?.name}
            />
          </div>
        ) : null;

      case StageType.PROCESSING:
        return (
          <div className="max-w-4xl mx-auto">
            <ProcessingPanel
              status={processingStatus}
              result={state.processingResult}
              templates={processingTemplates}
              selectedTemplate="ner"
              onRetry={() => {
                setProcessingStatus(ProcessingStatus.PROCESSING);
                setTimeout(() => setProcessingStatus(ProcessingStatus.SUCCESS), 2000);
              }}
              onExport={() => console.log('Export')}
              onSQLQuery={() => actions.handleViewChange(ViewType.SQL)}
              onBack={() => actions.handleStageChange(StageType.PREVIEW)}
              progress={processingProgress}
            />
          </div>
        );

      case StageType.SQL:
        return (
          <div className="max-w-6xl mx-auto">
            <Card className="card-data">
              <div className="p-6">
                <h2 className="text-2xl font-bold mb-4">SQL Query Editor</h2>
                <div className="space-y-4">
                  <textarea
                    className="w-full h-32 p-3 border rounded-md font-mono text-sm bg-muted/30"
                    placeholder="Enter your SQL query here..."
                    defaultValue="SELECT * FROM data LIMIT 10;"
                  />
                  <div className="flex gap-2">
                    <Button className="btn-data">
                      Execute Query
                    </Button>
                    <Button variant="outline">
                      Save Query
                    </Button>
                  </div>
                  <div className="mt-6 p-4 bg-muted/30 rounded-md">
                    <p className="text-sm text-muted-foreground">Query results will appear here</p>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        );

      default:
        return state.dataPreview ? (
          <div className="max-w-6xl mx-auto">
            <DataPreviewPanel
              data={state.dataPreview}
              onProcessData={handleDataProcess}
              onShowMetadata={() => setShowMetadataSidebar(true)}
              fileName={state.selectedFile?.name}
            />
          </div>
        ) : (
          <div className="max-w-4xl mx-auto">
            <FileUploadZone
              onFileUpload={handleFileUpload}
              acceptedTypes={['csv', 'xlsx', 'json', 'txt']}
              maxSize={100}
              isProcessing={state.isProcessing}
            />
          </div>
        );
    }
  };

  return (
    <div className={`min-h-screen bg-background ${className || ''}`}>
      {/* Navigation Bar */}
      <NavigationBar
        activeView={state.activeView}
        onViewChange={actions.handleViewChange}
        sidebarCollapsed={state.sidebarCollapsed}
        onToggleSidebar={actions.handleSidebarToggle}
        onToggleControlPanel={() => setShowControlPanel(true)}
      />

      {/* Breadcrumb Navigation */}
      <BreadcrumbNavigation
        items={breadcrumbItems}
        onNavigate={(stage) => actions.handleStageChange(stage as StageType)}
      />

      {/* Main Layout */}
      <div className="flex h-[calc(100vh-120px)]">
        {/* Left Sidebar */}
        <Sidebar collapsed={state.sidebarCollapsed} />

        {/* Main Content Area */}
        <div className="flex-1 overflow-y-auto scrollbar-modern">
          <div className="p-6">
            <AnimatePresence mode="wait">
              <motion.div
                key={`${state.activeView}-${state.currentStage}`}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.2 }}
              >
                {renderMainContent()}
              </motion.div>
            </AnimatePresence>
          </div>
        </div>
      </div>

      {/* Metadata Sidebar */}
      <Sheet open={showMetadataSidebar} onOpenChange={setShowMetadataSidebar}>
        <SheetContent side="right" className="w-96">
          <SheetHeader>
            <SheetTitle className="flex items-center justify-between">
              File Metadata
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowMetadataSidebar(false)}
              >
                <X className="w-4 h-4" />
              </Button>
            </SheetTitle>
          </SheetHeader>
          
          <div className="mt-6 space-y-4">
            {state.selectedFile && (
              <div className="space-y-3">
                <div>
                  <p className="text-sm font-medium">File Name</p>
                  <p className="text-sm text-muted-foreground">{state.selectedFile.name}</p>
                </div>
                <div>
                  <p className="text-sm font-medium">File Size</p>
                  <p className="text-sm text-muted-foreground">
                    {(state.selectedFile.size / 1024 / 1024).toFixed(2)} MB
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium">Last Modified</p>
                  <p className="text-sm text-muted-foreground">
                    {state.selectedFile.lastModified 
                      ? new Date(state.selectedFile.lastModified).toLocaleString()
                      : 'Unknown'
                    }
                  </p>
                </div>
                {state.dataPreview && (
                  <>
                    <div>
                      <p className="text-sm font-medium">Rows</p>
                      <p className="text-sm text-muted-foreground">
                        {state.dataPreview.totalRows.toLocaleString()}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium">Columns</p>
                      <p className="text-sm text-muted-foreground">
                        {state.dataPreview.headers.length}
                      </p>
                    </div>
                  </>
                )}
              </div>
            )}
          </div>
        </SheetContent>
      </Sheet>

      {/* Control Panel */}
      <Sheet open={showControlPanel} onOpenChange={setShowControlPanel}>
        <SheetContent side="right" className="w-96">
          <SheetHeader>
            <SheetTitle>Settings & Control Panel</SheetTitle>
          </SheetHeader>
          
          <div className="mt-6 space-y-4">
            <Card className="p-4">
              <h4 className="font-medium mb-3">Appearance</h4>
              <div className="space-y-2">
                <Button variant="outline" size="sm" className="w-full justify-start">
                  Toggle Theme
                </Button>
                <Button variant="outline" size="sm" className="w-full justify-start">
                  High Contrast Mode
                </Button>
              </div>
            </Card>
            
            <Card className="p-4">
              <h4 className="font-medium mb-3">Performance</h4>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Enable Caching</span>
                  <input type="checkbox" defaultChecked />
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Virtualization</span>
                  <input type="checkbox" defaultChecked />
                </div>
              </div>
            </Card>
          </div>
        </SheetContent>
      </Sheet>
    </div>
  );
};