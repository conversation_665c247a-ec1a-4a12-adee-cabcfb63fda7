// Web Worker for processing large datasets in the background

export interface ProcessingTask {
  id: string;
  type: 'parse' | 'filter' | 'sort' | 'aggregate' | 'transform';
  data: any;
  options?: any;
}

export interface ProcessingResult {
  id: string;
  success: boolean;
  data?: any;
  error?: string;
  progress?: number;
}

export interface ProgressUpdate {
  id: string;
  progress: number;
  message?: string;
}

// Helper function to report progress
const reportProgress = (id: string, progress: number, message?: string) => {
  self.postMessage({
    type: 'progress',
    payload: { id, progress, message }
  } as { type: 'progress'; payload: ProgressUpdate });
};

// CSV parsing function
const parseCSV = (csvText: string, options: { delimiter?: string; hasHeader?: boolean } = {}): any[] => {
  const { delimiter = ',', hasHeader = true } = options;
  const lines = csvText.split('\n').filter(line => line.trim());
  
  if (lines.length === 0) return [];
  
  const headers = hasHeader ? lines[0].split(delimiter).map(h => h.trim().replace(/"/g, '')) : null;
  const dataLines = hasHeader ? lines.slice(1) : lines;
  
  return dataLines.map((line, index) => {
    const values = line.split(delimiter).map(v => v.trim().replace(/"/g, ''));
    
    if (headers) {
      const row: any = {};
      headers.forEach((header, i) => {
        row[header] = values[i] || '';
      });
      return row;
    } else {
      return values;
    }
  });
};

// Data filtering function
const filterData = (data: any[], filters: { [key: string]: any }): any[] => {
  return data.filter(row => {
    return Object.entries(filters).every(([key, value]) => {
      if (value === null || value === undefined || value === '') return true;
      
      const rowValue = row[key];
      if (typeof value === 'string') {
        return rowValue?.toString().toLowerCase().includes(value.toLowerCase());
      }
      
      return rowValue === value;
    });
  });
};

// Data sorting function
const sortData = (data: any[], sortConfig: { key: string; direction: 'asc' | 'desc' }[]): any[] => {
  return [...data].sort((a, b) => {
    for (const { key, direction } of sortConfig) {
      const aVal = a[key];
      const bVal = b[key];
      
      let comparison = 0;
      
      if (aVal < bVal) comparison = -1;
      else if (aVal > bVal) comparison = 1;
      
      if (comparison !== 0) {
        return direction === 'asc' ? comparison : -comparison;
      }
    }
    return 0;
  });
};

// Data aggregation function
const aggregateData = (data: any[], config: {
  groupBy: string[];
  aggregations: { [key: string]: 'sum' | 'avg' | 'count' | 'min' | 'max' };
}): any[] => {
  const { groupBy, aggregations } = config;
  const groups = new Map<string, any[]>();
  
  // Group data
  data.forEach(row => {
    const groupKey = groupBy.map(key => row[key]).join('|');
    if (!groups.has(groupKey)) {
      groups.set(groupKey, []);
    }
    groups.get(groupKey)!.push(row);
  });
  
  // Aggregate groups
  return Array.from(groups.entries()).map(([groupKey, groupData]) => {
    const result: any = {};
    
    // Add group by fields
    groupBy.forEach((key, index) => {
      result[key] = groupKey.split('|')[index];
    });
    
    // Add aggregations
    Object.entries(aggregations).forEach(([field, operation]) => {
      const values = groupData.map(row => parseFloat(row[field])).filter(v => !isNaN(v));
      
      switch (operation) {
        case 'sum':
          result[`${field}_sum`] = values.reduce((sum, val) => sum + val, 0);
          break;
        case 'avg':
          result[`${field}_avg`] = values.length > 0 ? values.reduce((sum, val) => sum + val, 0) / values.length : 0;
          break;
        case 'count':
          result[`${field}_count`] = values.length;
          break;
        case 'min':
          result[`${field}_min`] = values.length > 0 ? Math.min(...values) : 0;
          break;
        case 'max':
          result[`${field}_max`] = values.length > 0 ? Math.max(...values) : 0;
          break;
      }
    });
    
    return result;
  });
};

// Data transformation function
const transformData = (data: any[], transformations: {
  [key: string]: (value: any, row: any) => any;
}): any[] => {
  return data.map(row => {
    const newRow = { ...row };
    
    Object.entries(transformations).forEach(([key, transformer]) => {
      try {
        newRow[key] = transformer(row[key], row);
      } catch (error) {
        console.warn(`Transformation failed for key ${key}:`, error);
        newRow[key] = row[key]; // Keep original value on error
      }
    });
    
    return newRow;
  });
};

// Main message handler
self.onmessage = async (event: MessageEvent<{ task: ProcessingTask }>) => {
  const { task } = event.data;
  const { id, type, data, options = {} } = task;
  
  try {
    reportProgress(id, 0, `Starting ${type} operation...`);
    
    let result: any;
    
    switch (type) {
      case 'parse':
        reportProgress(id, 25, 'Parsing data...');
        result = parseCSV(data, options);
        reportProgress(id, 75, 'Processing parsed data...');
        break;
        
      case 'filter':
        reportProgress(id, 25, 'Applying filters...');
        result = filterData(data, options.filters || {});
        reportProgress(id, 75, 'Finalizing filtered data...');
        break;
        
      case 'sort':
        reportProgress(id, 25, 'Sorting data...');
        result = sortData(data, options.sortConfig || []);
        reportProgress(id, 75, 'Finalizing sorted data...');
        break;
        
      case 'aggregate':
        reportProgress(id, 25, 'Grouping data...');
        result = aggregateData(data, options);
        reportProgress(id, 75, 'Calculating aggregations...');
        break;
        
      case 'transform':
        reportProgress(id, 25, 'Applying transformations...');
        result = transformData(data, options.transformations || {});
        reportProgress(id, 75, 'Finalizing transformations...');
        break;
        
      default:
        throw new Error(`Unknown task type: ${type}`);
    }
    
    reportProgress(id, 100, 'Operation completed');
    
    // Send final result
    self.postMessage({
      type: 'result',
      payload: {
        id,
        success: true,
        data: result
      } as ProcessingResult
    });
    
  } catch (error) {
    self.postMessage({
      type: 'result',
      payload: {
        id,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      } as ProcessingResult
    });
  }
};

// Handle worker errors
self.onerror = (error) => {
  console.error('Worker error:', error);
};

export {};