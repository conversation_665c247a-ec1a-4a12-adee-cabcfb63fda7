import React, { useEffect, useRef, useState } from 'react';
import { cn } from '@/lib/utils';

interface ScreenReaderAnnouncementProps {
  message: string;
  priority?: 'polite' | 'assertive';
  delay?: number;
}

interface ScreenReaderSupportProps {
  children: React.ReactNode;
  className?: string;
}

interface AriaLiveRegionProps {
  message: string;
  priority: 'polite' | 'assertive';
  id?: string;
}

// Hook for managing screen reader announcements
export const useScreenReaderAnnouncement = () => {
  const [announcements, setAnnouncements] = useState<{
    id: string;
    message: string;
    priority: 'polite' | 'assertive';
    timestamp: number;
  }[]>([]);

  const announce = (message: string, priority: 'polite' | 'assertive' = 'polite', delay = 0) => {
    const id = `announcement-${Date.now()}-${Math.random()}`;
    
    setTimeout(() => {
      setAnnouncements(prev => [
        ...prev,
        {
          id,
          message,
          priority,
          timestamp: Date.now()
        }
      ]);

      // Clean up old announcements after 5 seconds
      setTimeout(() => {
        setAnnouncements(prev => prev.filter(a => a.id !== id));
      }, 5000);
    }, delay);
  };

  const clearAnnouncements = () => {
    setAnnouncements([]);
  };

  return {
    announcements,
    announce,
    clearAnnouncements
  };
};

// ARIA Live Region Component
const AriaLiveRegion: React.FC<AriaLiveRegionProps> = ({ message, priority, id }) => (
  <div
    id={id}
    aria-live={priority}
    aria-atomic="true"
    className="sr-only"
    role="status"
  >
    {message}
  </div>
);

// Screen Reader Announcement Component
export const ScreenReaderAnnouncement: React.FC<ScreenReaderAnnouncementProps> = ({
  message,
  priority = 'polite',
  delay = 0
}) => {
  const [shouldAnnounce, setShouldAnnounce] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setShouldAnnounce(true);
    }, delay);

    return () => clearTimeout(timer);
  }, [delay]);

  useEffect(() => {
    if (shouldAnnounce) {
      const cleanup = setTimeout(() => {
        setShouldAnnounce(false);
      }, 100);

      return () => clearTimeout(cleanup);
    }
  }, [shouldAnnounce]);

  if (!shouldAnnounce || !message) return null;

  return <AriaLiveRegion message={message} priority={priority} />;
};

// Enhanced Button with Screen Reader Support
interface AccessibleButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  description?: string;
  shortcut?: string;
  isPressed?: boolean;
  isExpanded?: boolean;
  controls?: string;
  hasPopup?: boolean | 'menu' | 'listbox' | 'tree' | 'grid' | 'dialog';
}

export const AccessibleButton: React.FC<AccessibleButtonProps> = ({
  children,
  description,
  shortcut,
  isPressed,
  isExpanded,
  controls,
  hasPopup,
  className,
  ...props
}) => {
  const ariaLabel = description || (typeof children === 'string' ? children : undefined);
  const ariaDescription = shortcut ? `Keyboard shortcut: ${shortcut}` : undefined;

  return (
    <button
      {...props}
      className={cn(
        "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",
        "transition-all duration-200",
        className
      )}
      aria-label={ariaLabel}
      aria-describedby={ariaDescription ? `${props.id}-desc` : undefined}
      aria-pressed={isPressed}
      aria-expanded={isExpanded}
      aria-controls={controls}
      aria-haspopup={hasPopup}
    >
      {children}
      {ariaDescription && (
        <span id={`${props.id}-desc`} className="sr-only">
          {ariaDescription}
        </span>
      )}
    </button>
  );
};

// Enhanced Input with Screen Reader Support
interface AccessibleInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label: string;
  description?: string;
  error?: string;
  required?: boolean;
}

export const AccessibleInput: React.FC<AccessibleInputProps> = ({
  label,
  description,
  error,
  required,
  className,
  id,
  ...props
}) => {
  const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;
  const descId = description ? `${inputId}-desc` : undefined;
  const errorId = error ? `${inputId}-error` : undefined;

  return (
    <div className="space-y-1">
      <label
        htmlFor={inputId}
        className="block text-sm font-medium text-gray-700 dark:text-gray-300"
      >
        {label}
        {required && (
          <span className="text-red-500 ml-1" aria-label="required">
            *
          </span>
        )}
      </label>
      
      {description && (
        <p id={descId} className="text-sm text-gray-500 dark:text-gray-400">
          {description}
        </p>
      )}
      
      <input
        {...props}
        id={inputId}
        className={cn(
          "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm",
          "focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
          "dark:border-gray-600 dark:bg-gray-700 dark:text-white",
          error && "border-red-500 focus:ring-red-500 focus:border-red-500",
          className
        )}
        aria-describedby={[descId, errorId].filter(Boolean).join(' ') || undefined}
        aria-invalid={error ? 'true' : undefined}
        aria-required={required}
      />
      
      {error && (
        <p id={errorId} className="text-sm text-red-600 dark:text-red-400" role="alert">
          {error}
        </p>
      )}
    </div>
  );
};

// Enhanced Table with Screen Reader Support
interface AccessibleTableProps {
  children: React.ReactNode;
  caption?: string;
  summary?: string;
  className?: string;
}

export const AccessibleTable: React.FC<AccessibleTableProps> = ({
  children,
  caption,
  summary,
  className
}) => (
  <div className="overflow-x-auto">
    <table
      className={cn("min-w-full divide-y divide-gray-200 dark:divide-gray-700", className)}
      role="table"
      aria-label={summary}
    >
      {caption && (
        <caption className="sr-only">
          {caption}
        </caption>
      )}
      {children}
    </table>
  </div>
);

// Enhanced Dialog with Screen Reader Support
interface AccessibleDialogProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  description?: string;
  children: React.ReactNode;
  className?: string;
}

export const AccessibleDialog: React.FC<AccessibleDialogProps> = ({
  isOpen,
  onClose,
  title,
  description,
  children,
  className
}) => {
  const dialogRef = useRef<HTMLDivElement>(null);
  const previousFocusRef = useRef<HTMLElement | null>(null);

  useEffect(() => {
    if (isOpen) {
      // Store the previously focused element
      previousFocusRef.current = document.activeElement as HTMLElement;
      
      // Focus the dialog
      setTimeout(() => {
        dialogRef.current?.focus();
      }, 100);
    } else {
      // Restore focus to the previously focused element
      if (previousFocusRef.current) {
        previousFocusRef.current.focus();
      }
    }
  }, [isOpen]);

  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center"
      role="dialog"
      aria-modal="true"
      aria-labelledby="dialog-title"
      aria-describedby={description ? "dialog-description" : undefined}
    >
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50"
        onClick={onClose}
        aria-hidden="true"
      />
      
      {/* Dialog Content */}
      <div
        ref={dialogRef}
        className={cn(
          "relative bg-white dark:bg-gray-800 rounded-lg shadow-xl",
          "max-w-md w-full mx-4 p-6",
          "focus:outline-none focus:ring-2 focus:ring-blue-500",
          className
        )}
        tabIndex={-1}
      >
        <h2 id="dialog-title" className="text-lg font-semibold mb-2">
          {title}
        </h2>
        
        {description && (
          <p id="dialog-description" className="text-sm text-gray-600 dark:text-gray-400 mb-4">
            {description}
          </p>
        )}
        
        {children}
      </div>
    </div>
  );
};

// Progress Indicator with Screen Reader Support
interface AccessibleProgressProps {
  value: number;
  max?: number;
  label?: string;
  description?: string;
  showPercentage?: boolean;
  className?: string;
}

export const AccessibleProgress: React.FC<AccessibleProgressProps> = ({
  value,
  max = 100,
  label,
  description,
  showPercentage = true,
  className
}) => {
  const percentage = Math.round((value / max) * 100);
  const ariaLabel = label || `Progress: ${percentage}%`;

  return (
    <div className={cn("space-y-2", className)}>
      {(label || description) && (
        <div className="flex justify-between items-center">
          {label && (
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {label}
            </span>
          )}
          {showPercentage && (
            <span className="text-sm text-gray-500 dark:text-gray-400">
              {percentage}%
            </span>
          )}
        </div>
      )}
      
      <div
        className="w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700"
        role="progressbar"
        aria-valuenow={value}
        aria-valuemin={0}
        aria-valuemax={max}
        aria-label={ariaLabel}
        aria-describedby={description ? `progress-desc-${Math.random()}` : undefined}
      >
        <div
          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
          style={{ width: `${percentage}%` }}
        />
      </div>
      
      {description && (
        <p className="text-xs text-gray-500 dark:text-gray-400">
          {description}
        </p>
      )}
    </div>
  );
};

// Main Screen Reader Support Provider
export const ScreenReaderSupport: React.FC<ScreenReaderSupportProps> = ({
  children,
  className
}) => {
  const { announcements } = useScreenReaderAnnouncement();

  return (
    <div className={cn("screen-reader-support", className)}>
      {/* Live regions for announcements */}
      <div aria-live="polite" aria-atomic="true" className="sr-only" id="polite-announcements">
        {announcements
          .filter(a => a.priority === 'polite')
          .map(announcement => (
            <div key={announcement.id}>{announcement.message}</div>
          ))
        }
      </div>
      
      <div aria-live="assertive" aria-atomic="true" className="sr-only" id="assertive-announcements">
        {announcements
          .filter(a => a.priority === 'assertive')
          .map(announcement => (
            <div key={announcement.id}>{announcement.message}</div>
          ))
        }
      </div>
      
      {/* Skip to main content link */}
      <a
        href="#main-content"
        className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 z-50 bg-blue-600 text-white px-4 py-2 rounded-md"
      >
        Skip to main content
      </a>
      
      {children}
    </div>
  );
};

export default ScreenReaderSupport;