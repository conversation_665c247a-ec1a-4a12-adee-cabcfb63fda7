import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import {
  Keyboard,
  Search,
  Filter,
  Download,
  Upload,
  Copy,
  Trash2,
  Edit,
  Save,
  Undo,
  Redo,
  ZoomIn,
  ZoomOut,
  RefreshCw,
  Settings,
  HelpCircle,
  ArrowUp,
  ArrowDown,
  ArrowLeft,
  ArrowRight,
  Home,
  BookOpen,
  Star,
  Eye,
  EyeOff,
  Plus,
  Minus,
  X
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface KeyboardShortcut {
  id: string;
  keys: string[];
  description: string;
  category: string;
  action: string;
  context?: string;
  icon?: React.ReactNode;
  enabled: boolean;
  customizable: boolean;
}

interface ShortcutCategory {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  shortcuts: KeyboardShortcut[];
}

// Default keyboard shortcuts
const defaultShortcuts: ShortcutCategory[] = [
  {
    id: 'navigation',
    name: 'Navigation',
    description: 'Navigate through the interface',
    icon: <ArrowUp className="w-4 h-4" />,
    shortcuts: [
      {
        id: 'nav-up',
        keys: ['ArrowUp'],
        description: 'Move selection up',
        category: 'navigation',
        action: 'navigate-up',
        icon: <ArrowUp className="w-4 h-4" />,
        enabled: true,
        customizable: true
      },
      {
        id: 'nav-down',
        keys: ['ArrowDown'],
        description: 'Move selection down',
        category: 'navigation',
        action: 'navigate-down',
        icon: <ArrowDown className="w-4 h-4" />,
        enabled: true,
        customizable: true
      },
      {
        id: 'nav-left',
        keys: ['ArrowLeft'],
        description: 'Move selection left',
        category: 'navigation',
        action: 'navigate-left',
        icon: <ArrowLeft className="w-4 h-4" />,
        enabled: true,
        customizable: true
      },
      {
        id: 'nav-right',
        keys: ['ArrowRight'],
        description: 'Move selection right',
        category: 'navigation',
        action: 'navigate-right',
        icon: <ArrowRight className="w-4 h-4" />,
        enabled: true,
        customizable: true
      },
      {
        id: 'nav-home',
        keys: ['Home'],
        description: 'Go to beginning',
        category: 'navigation',
        action: 'navigate-home',
        icon: <Home className="w-4 h-4" />,
        enabled: true,
        customizable: true
      },
      {
        id: 'nav-end',
        keys: ['End'],
        description: 'Go to end',
        category: 'navigation',
        action: 'navigate-end',
        icon: <ArrowRight className="w-4 h-4" />,
        enabled: true,
        customizable: true
      },
      {
        id: 'nav-page-up',
        keys: ['PageUp'],
        description: 'Page up',
        category: 'navigation',
        action: 'navigate-page-up',
        icon: <ArrowUp className="w-4 h-4" />,
        enabled: true,
        customizable: true
      },
      {
        id: 'nav-page-down',
        keys: ['PageDown'],
        description: 'Page down',
        category: 'navigation',
        action: 'navigate-page-down',
        icon: <ArrowDown className="w-4 h-4" />,
        enabled: true,
        customizable: true
      }
    ]
  },
  {
    id: 'data-operations',
    name: 'Data Operations',
    description: 'Perform operations on data',
    icon: <Search className="w-4 h-4" />,
    shortcuts: [
      {
        id: 'search',
        keys: ['Ctrl', 'f'],
        description: 'Search data',
        category: 'data-operations',
        action: 'search',
        icon: <Search className="w-4 h-4" />,
        enabled: true,
        customizable: true
      },
      {
        id: 'filter',
        keys: ['Ctrl', 'Shift', 'f'],
        description: 'Open filter panel',
        category: 'data-operations',
        action: 'filter',
        icon: <Filter className="w-4 h-4" />,
        enabled: true,
        customizable: true
      },
      {
        id: 'refresh',
        keys: ['F5'],
        description: 'Refresh data',
        category: 'data-operations',
        action: 'refresh',
        icon: <RefreshCw className="w-4 h-4" />,
        enabled: true,
        customizable: true
      },
      {
        id: 'export',
        keys: ['Ctrl', 'e'],
        description: 'Export data',
        category: 'data-operations',
        action: 'export',
        icon: <Download className="w-4 h-4" />,
        enabled: true,
        customizable: true
      },
      {
        id: 'import',
        keys: ['Ctrl', 'i'],
        description: 'Import data',
        category: 'data-operations',
        action: 'import',
        icon: <Upload className="w-4 h-4" />,
        enabled: true,
        customizable: true
      }
    ]
  },
  {
    id: 'editing',
    name: 'Editing',
    description: 'Edit and modify data',
    icon: <Edit className="w-4 h-4" />,
    shortcuts: [
      {
        id: 'copy',
        keys: ['Ctrl', 'c'],
        description: 'Copy selection',
        category: 'editing',
        action: 'copy',
        icon: <Copy className="w-4 h-4" />,
        enabled: true,
        customizable: false
      },
      {
        id: 'paste',
        keys: ['Ctrl', 'v'],
        description: 'Paste from clipboard',
        category: 'editing',
        action: 'paste',
        icon: <Copy className="w-4 h-4" />,
        enabled: true,
        customizable: false
      },
      {
        id: 'cut',
        keys: ['Ctrl', 'x'],
        description: 'Cut selection',
        category: 'editing',
        action: 'cut',
        icon: <Copy className="w-4 h-4" />,
        enabled: true,
        customizable: false
      },
      {
        id: 'undo',
        keys: ['Ctrl', 'z'],
        description: 'Undo last action',
        category: 'editing',
        action: 'undo',
        icon: <Undo className="w-4 h-4" />,
        enabled: true,
        customizable: false
      },
      {
        id: 'redo',
        keys: ['Ctrl', 'y'],
        description: 'Redo last undone action',
        category: 'editing',
        action: 'redo',
        icon: <Redo className="w-4 h-4" />,
        enabled: true,
        customizable: false
      },
      {
        id: 'save',
        keys: ['Ctrl', 's'],
        description: 'Save changes',
        category: 'editing',
        action: 'save',
        icon: <Save className="w-4 h-4" />,
        enabled: true,
        customizable: true
      },
      {
        id: 'delete',
        keys: ['Delete'],
        description: 'Delete selection',
        category: 'editing',
        action: 'delete',
        icon: <Trash2 className="w-4 h-4" />,
        enabled: true,
        customizable: true
      }
    ]
  },
  {
    id: 'view',
    name: 'View',
    description: 'Control view and display options',
    icon: <Eye className="w-4 h-4" />,
    shortcuts: [
      {
        id: 'zoom-in',
        keys: ['Ctrl', '+'],
        description: 'Zoom in',
        category: 'view',
        action: 'zoom-in',
        icon: <ZoomIn className="w-4 h-4" />,
        enabled: true,
        customizable: true
      },
      {
        id: 'zoom-out',
        keys: ['Ctrl', '-'],
        description: 'Zoom out',
        category: 'view',
        action: 'zoom-out',
        icon: <ZoomOut className="w-4 h-4" />,
        enabled: true,
        customizable: true
      },
      {
        id: 'toggle-sidebar',
        keys: ['Ctrl', 'b'],
        description: 'Toggle sidebar',
        category: 'view',
        action: 'toggle-sidebar',
        icon: <Eye className="w-4 h-4" />,
        enabled: true,
        customizable: true
      },
      {
        id: 'fullscreen',
        keys: ['F11'],
        description: 'Toggle fullscreen',
        category: 'view',
        action: 'fullscreen',
        icon: <Eye className="w-4 h-4" />,
        enabled: true,
        customizable: true
      }
    ]
  },
  {
    id: 'general',
    name: 'General',
    description: 'General application shortcuts',
    icon: <Settings className="w-4 h-4" />,
    shortcuts: [
      {
        id: 'help',
        keys: ['F1'],
        description: 'Show help',
        category: 'general',
        action: 'help',
        icon: <HelpCircle className="w-4 h-4" />,
        enabled: true,
        customizable: true
      },
      {
        id: 'shortcuts',
        keys: ['Ctrl', '?'],
        description: 'Show keyboard shortcuts',
        category: 'general',
        action: 'shortcuts',
        icon: <Keyboard className="w-4 h-4" />,
        enabled: true,
        customizable: true
      },
      {
        id: 'settings',
        keys: ['Ctrl', ','],
        description: 'Open settings',
        category: 'general',
        action: 'settings',
        icon: <Settings className="w-4 h-4" />,
        enabled: true,
        customizable: true
      },
      {
        id: 'escape',
        keys: ['Escape'],
        description: 'Cancel or close',
        category: 'general',
        action: 'escape',
        icon: <X className="w-4 h-4" />,
        enabled: true,
        customizable: false
      },
      {
        id: 'enter',
        keys: ['Enter'],
        description: 'Confirm or activate',
        category: 'general',
        action: 'enter',
        icon: <ArrowRight className="w-4 h-4" />,
        enabled: true,
        customizable: false
      }
    ]
  }
];

// Key display component
interface KeyDisplayProps {
  keys: string[];
  className?: string;
}

const KeyDisplay: React.FC<KeyDisplayProps> = ({ keys, className }) => {
  const getKeySymbol = (key: string): string => {
    const keyMap: Record<string, string> = {
      'Ctrl': '⌃',
      'Control': '⌃',
      'Cmd': '⌘',
      'Command': '⌘',
      'Alt': '⌥',
      'Option': '⌥',
      'Shift': '⇧',
      'Tab': '⇥',
      'Enter': '↵',
      'Return': '↵',
      'Space': '␣',
      'Escape': '⎋',
      'Backspace': '⌫',
      'Delete': '⌦',
      'ArrowUp': '↑',
      'ArrowDown': '↓',
      'ArrowLeft': '←',
      'ArrowRight': '→',
      'Home': '⇱',
      'End': '⇲',
      'PageUp': '⇞',
      'PageDown': '⇟'
    };
    
    return keyMap[key] || key;
  };

  return (
    <div className={cn("flex items-center gap-1", className)}>
      {keys.map((key, index) => (
        <React.Fragment key={key}>
          <kbd className="px-2 py-1 text-xs font-mono bg-gray-100 dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded">
            {getKeySymbol(key)}
          </kbd>
          {index < keys.length - 1 && (
            <span className="text-xs text-gray-400">+</span>
          )}
        </React.Fragment>
      ))}
    </div>
  );
};

// Shortcut item component
interface ShortcutItemProps {
  shortcut: KeyboardShortcut;
  onToggle?: (id: string, enabled: boolean) => void;
  onCustomize?: (id: string) => void;
  showCategory?: boolean;
}

const ShortcutItem: React.FC<ShortcutItemProps> = ({
  shortcut,
  onToggle,
  onCustomize,
  showCategory = false
}) => {
  return (
    <div className="flex items-center justify-between p-3 rounded-lg border border-gray-200 dark:border-gray-700">
      <div className="flex items-center gap-3">
        {shortcut.icon && (
          <div className="text-gray-500 dark:text-gray-400">
            {shortcut.icon}
          </div>
        )}
        <div className="flex-1">
          <div className="flex items-center gap-2">
            <span className="font-medium text-sm">{shortcut.description}</span>
            {showCategory && (
              <Badge variant="outline" className="text-xs">
                {shortcut.category}
              </Badge>
            )}
          </div>
          {shortcut.context && (
            <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              {shortcut.context}
            </div>
          )}
        </div>
      </div>
      
      <div className="flex items-center gap-3">
        <KeyDisplay keys={shortcut.keys} />
        
        {shortcut.customizable && onCustomize && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onCustomize(shortcut.id)}
            className="text-xs"
          >
            Customize
          </Button>
        )}
        
        {onToggle && (
          <input
            type="checkbox"
            checked={shortcut.enabled}
            onChange={(e) => onToggle(shortcut.id, e.target.checked)}
            className="w-4 h-4"
          />
        )}
      </div>
    </div>
  );
};

// Quick reference card
interface QuickReferenceProps {
  shortcuts: ShortcutCategory[];
  className?: string;
}

const QuickReference: React.FC<QuickReferenceProps> = ({ shortcuts, className }) => {
  const essentialShortcuts = shortcuts
    .flatMap(category => category.shortcuts)
    .filter(shortcut => [
      'search', 'help', 'shortcuts', 'save', 'copy', 'paste', 'undo', 'redo'
    ].includes(shortcut.id))
    .slice(0, 8);

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="pb-3">
        <CardTitle className="text-sm flex items-center gap-2">
          <Star className="w-4 h-4" />
          Quick Reference
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
          {essentialShortcuts.map(shortcut => (
            <div key={shortcut.id} className="flex items-center justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-400">
                {shortcut.description}
              </span>
              <KeyDisplay keys={shortcut.keys} className="ml-2" />
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

// Main keyboard shortcuts documentation component
interface KeyboardShortcutsDocumentationProps {
  className?: string;
}

export const KeyboardShortcutsDocumentation: React.FC<KeyboardShortcutsDocumentationProps> = ({
  className
}) => {
  const [shortcuts, setShortcuts] = useState<ShortcutCategory[]>(defaultShortcuts);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [isOpen, setIsOpen] = useState(false);

  // Load custom shortcuts from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('keyboard-shortcuts');
    if (saved) {
      try {
        const customShortcuts = JSON.parse(saved);
        setShortcuts(customShortcuts);
      } catch {
        // Use default shortcuts if parsing fails
      }
    }
  }, []);

  // Save shortcuts to localStorage
  const saveShortcuts = (newShortcuts: ShortcutCategory[]) => {
    setShortcuts(newShortcuts);
    localStorage.setItem('keyboard-shortcuts', JSON.stringify(newShortcuts));
  };

  // Toggle shortcut enabled state
  const toggleShortcut = (id: string, enabled: boolean) => {
    const newShortcuts = shortcuts.map(category => ({
      ...category,
      shortcuts: category.shortcuts.map(shortcut =>
        shortcut.id === id ? { ...shortcut, enabled } : shortcut
      )
    }));
    saveShortcuts(newShortcuts);
  };

  // Filter shortcuts based on search and category
  const filteredShortcuts = shortcuts
    .map(category => ({
      ...category,
      shortcuts: category.shortcuts.filter(shortcut => {
        const matchesSearch = searchQuery === '' ||
          shortcut.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
          shortcut.keys.some(key => key.toLowerCase().includes(searchQuery.toLowerCase()));
        
        const matchesCategory = selectedCategory === 'all' || category.id === selectedCategory;
        
        return matchesSearch && matchesCategory;
      })
    }))
    .filter(category => category.shortcuts.length > 0);

  // Get all shortcuts for search
  const allShortcuts = shortcuts.flatMap(category => 
    category.shortcuts.map(shortcut => ({ ...shortcut, categoryName: category.name }))
  );

  const filteredAllShortcuts = allShortcuts.filter(shortcut => {
    const matchesSearch = searchQuery === '' ||
      shortcut.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      shortcut.keys.some(key => key.toLowerCase().includes(searchQuery.toLowerCase()));
    
    return matchesSearch;
  });

  // Keyboard shortcut to open help
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if ((event.ctrlKey || event.metaKey) && event.key === '?') {
        event.preventDefault();
        setIsOpen(true);
      }
      if (event.key === 'F1') {
        event.preventDefault();
        setIsOpen(true);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  return (
    <div className={cn("space-y-4", className)}>
      {/* Quick Reference Card */}
      <QuickReference shortcuts={shortcuts} />
      
      {/* Help Dialog Trigger */}
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogTrigger asChild>
          <Button variant="outline" className="w-full">
            <Keyboard className="w-4 h-4 mr-2" />
            View All Keyboard Shortcuts
            <Badge variant="secondary" className="ml-2 text-xs">
              Ctrl + ?
            </Badge>
          </Button>
        </DialogTrigger>
        
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Keyboard className="w-5 h-5" />
              Keyboard Shortcuts
            </DialogTitle>
            <DialogDescription>
              Learn and customize keyboard shortcuts to work more efficiently
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            {/* Search and Filter */}
            <div className="flex gap-2">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <Input
                  placeholder="Search shortcuts..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800"
              >
                <option value="all">All Categories</option>
                {shortcuts.map(category => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>
            
            <Tabs value="by-category" onValueChange={() => {}} className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="by-category">By Category</TabsTrigger>
                <TabsTrigger value="all-shortcuts">All Shortcuts</TabsTrigger>
              </TabsList>
              
              <TabsContent value="by-category" className="mt-4">
                <ScrollArea className="h-[400px] pr-4">
                  <div className="space-y-6">
                    {filteredShortcuts.map(category => (
                      <div key={category.id}>
                        <div className="flex items-center gap-2 mb-3">
                          {category.icon}
                          <h3 className="font-semibold text-lg">{category.name}</h3>
                          <Badge variant="outline" className="text-xs">
                            {category.shortcuts.length}
                          </Badge>
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                          {category.description}
                        </p>
                        <div className="space-y-2">
                          {category.shortcuts.map(shortcut => (
                            <ShortcutItem
                              key={shortcut.id}
                              shortcut={shortcut}
                              onToggle={toggleShortcut}
                            />
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </TabsContent>
              
              <TabsContent value="all-shortcuts" className="mt-4">
                <ScrollArea className="h-[400px] pr-4">
                  <div className="space-y-2">
                    {filteredAllShortcuts.map(shortcut => (
                      <ShortcutItem
                        key={shortcut.id}
                        shortcut={shortcut}
                        onToggle={toggleShortcut}
                        showCategory
                      />
                    ))}
                  </div>
                </ScrollArea>
              </TabsContent>
            </Tabs>
            
            {/* Footer */}
            <div className="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
              <div className="text-xs text-gray-500 dark:text-gray-400">
                Press <KeyDisplay keys={['Escape']} className="inline-flex" /> to close
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setShortcuts(defaultShortcuts);
                    localStorage.removeItem('keyboard-shortcuts');
                  }}
                >
                  Reset to Defaults
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const data = JSON.stringify(shortcuts, null, 2);
                    const blob = new Blob([data], { type: 'application/json' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'keyboard-shortcuts.json';
                    a.click();
                    URL.revokeObjectURL(url);
                  }}
                >
                  <Download className="w-4 h-4 mr-1" />
                  Export
                </Button>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
      
      {/* Printable Reference */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm flex items-center gap-2">
            <BookOpen className="w-4 h-4" />
            Printable Reference
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
            Generate a printable reference card for offline use
          </p>
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              const printWindow = window.open('', '_blank');
              if (printWindow) {
                const html = `
                  <!DOCTYPE html>
                  <html>
                    <head>
                      <title>Keyboard Shortcuts Reference</title>
                      <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        .category { margin-bottom: 20px; }
                        .category h3 { border-bottom: 1px solid #ccc; padding-bottom: 5px; }
                        .shortcut { display: flex; justify-content: space-between; padding: 5px 0; }
                        .keys { font-family: monospace; background: #f5f5f5; padding: 2px 6px; border-radius: 3px; }
                        @media print { body { margin: 0; } }
                      </style>
                    </head>
                    <body>
                      <h1>Keyboard Shortcuts Reference</h1>
                      ${shortcuts.map(category => `
                        <div class="category">
                          <h3>${category.name}</h3>
                          ${category.shortcuts.filter(s => s.enabled).map(shortcut => `
                            <div class="shortcut">
                              <span>${shortcut.description}</span>
                              <span class="keys">${shortcut.keys.join(' + ')}</span>
                            </div>
                          `).join('')}
                        </div>
                      `).join('')}
                    </body>
                  </html>
                `;
                printWindow.document.write(html);
                printWindow.document.close();
                printWindow.print();
              }
            }}
          >
            Generate Printable Reference
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

export default KeyboardShortcutsDocumentation;