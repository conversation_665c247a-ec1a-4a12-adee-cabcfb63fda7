import { StorageFile } from './storageManager';
import { fileTaggingManager, TAG_COLORS } from '../../components/data-center/utils/fileTagging';

export interface FileOrganizationSuggestion {
  fileId: string;
  fileName: string;
  currentPath: string;
  suggestedPath: string;
  category: string;
  confidence: number; // 0-100
  reasoning: string;
  suggestedTags: string[];
  actions: {
    move: boolean;
    tag: boolean;
    rename?: string;
  };
}

export interface OrganizationRule {
  id: string;
  name: string;
  description: string;
  condition: (file: StorageFile) => boolean;
  action: (file: StorageFile) => {
    suggestedPath?: string;
    suggestedTags?: string[];
    suggestedName?: string;
  };
  priority: number;
  enabled: boolean;
}

export class SmartFileOrganizer {
  private rules: OrganizationRule[] = [];

  constructor() {
    this.initializeDefaultRules();
  }

  private initializeDefaultRules(): void {
    this.rules = [
      {
        id: 'large-files',
        name: 'Large Files',
        description: 'Move large files to archive folder',
        condition: (file) => file.size > 100 * 1024 * 1024, // 100MB
        action: (file) => ({
          suggestedPath: 'archive/large-files',
          suggestedTags: ['large', 'archive']
        }),
        priority: 10,
        enabled: true
      },
      {
        id: 'old-files',
        name: 'Old Files',
        description: 'Archive files older than 90 days',
        condition: (file) => {
          const ageInDays = (Date.now() - file.modifiedAt.getTime()) / (1000 * 60 * 60 * 24);
          return ageInDays > 90;
        },
        action: (file) => ({
          suggestedPath: 'archive/old-files',
          suggestedTags: ['old', 'archive']
        }),
        priority: 8,
        enabled: true
      },
      {
        id: 'data-files',
        name: 'Data Files',
        description: 'Organize data files by type',
        condition: (file) => {
          const dataTypes = ['.csv', '.xlsx', '.json', '.xml', '.sql'];
          return dataTypes.some(type => file.name.toLowerCase().endsWith(type));
        },
        action: (file) => {
          let subfolder = 'other';
          if (file.name.toLowerCase().endsWith('.csv')) subfolder = 'csv';
          if (file.name.toLowerCase().endsWith('.xlsx')) subfolder = 'excel';
          if (file.name.toLowerCase().endsWith('.json')) subfolder = 'json';
          if (file.name.toLowerCase().endsWith('.sql')) subfolder = 'sql';
          
          return {
            suggestedPath: `data/${subfolder}`,
            suggestedTags: ['data', subfolder]
          };
        },
        priority: 7,
        enabled: true
      },
      {
        id: 'document-files',
        name: 'Document Files',
        description: 'Organize documents by type',
        condition: (file) => {
          const docTypes = ['.pdf', '.doc', '.docx', '.txt', '.md'];
          return docTypes.some(type => file.name.toLowerCase().endsWith(type));
        },
        action: (file) => {
          let subfolder = 'other';
          if (file.name.toLowerCase().endsWith('.pdf')) subfolder = 'pdf';
          if (file.name.toLowerCase().endsWith('.doc') || file.name.toLowerCase().endsWith('.docx')) {
            subfolder = 'word';
          }
          if (file.name.toLowerCase().endsWith('.txt') || file.name.toLowerCase().endsWith('.md')) {
            subfolder = 'text';
          }
          
          return {
            suggestedPath: `documents/${subfolder}`,
            suggestedTags: ['document', subfolder]
          };
        },
        priority: 6,
        enabled: true
      },
      {
        id: 'image-files',
        name: 'Image Files',
        description: 'Organize images by type',
        condition: (file) => {
          const imageTypes = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg'];
          return imageTypes.some(type => file.name.toLowerCase().endsWith(type));
        },
        action: (file) => ({
          suggestedPath: 'media/images',
          suggestedTags: ['media', 'image']
        }),
        priority: 5,
        enabled: true
      },
      {
        id: 'duplicate-files',
        name: 'Duplicate Files',
        description: 'Tag potential duplicate files',
        condition: (file) => {
          // Simple duplicate detection based on name patterns
          return file.name.includes(' copy') || file.name.includes('_copy') || 
                 /\(\d+\)/.test(file.name);
        },
        action: (file) => ({
          suggestedTags: ['duplicate', 'review']
        }),
        priority: 9,
        enabled: true
      },
      {
        id: 'important-files',
        name: 'Important Files',
        description: 'Tag files with important keywords',
        condition: (file) => {
          const importantKeywords = ['important', 'critical', 'urgent', 'final', 'approved'];
          return importantKeywords.some(keyword => 
            file.name.toLowerCase().includes(keyword)
          );
        },
        action: (file) => ({
          suggestedTags: ['important', 'priority']
        }),
        priority: 11,
        enabled: true
      }
    ];
  }

  analyzeFile(file: StorageFile): FileOrganizationSuggestion | null {
    const applicableRules = this.rules
      .filter(rule => rule.enabled && rule.condition(file))
      .sort((a, b) => b.priority - a.priority);

    if (applicableRules.length === 0) {
      return null;
    }

    const highestPriorityRule = applicableRules[0];
    const action = highestPriorityRule.action(file);

    // Calculate confidence based on rule priority and file characteristics
    let confidence = Math.min(95, 50 + (highestPriorityRule.priority * 2));
    
    // Adjust confidence based on file size and age
    if (file.size > 500 * 1024 * 1024) { // 500MB
      confidence += 5; // More confident about large files
    }
    
    const ageInDays = (Date.now() - file.modifiedAt.getTime()) / (1000 * 60 * 60 * 24);
    if (ageInDays > 365) { // Over a year old
      confidence += 3;
    }

    return {
      fileId: file.id,
      fileName: file.name,
      currentPath: file.path,
      suggestedPath: action.suggestedPath || file.path,
      category: highestPriorityRule.name,
      confidence: Math.min(100, confidence),
      reasoning: `Matched rule: ${highestPriorityRule.name} (${highestPriorityRule.description})`,
      suggestedTags: action.suggestedTags || [],
      actions: {
        move: !!action.suggestedPath && action.suggestedPath !== file.path,
        tag: (action.suggestedTags?.length || 0) > 0,
        rename: action.suggestedName
      }
    };
  }

  analyzeAllFiles(files: StorageFile[]): FileOrganizationSuggestion[] {
    return files
      .map(file => this.analyzeFile(file))
      .filter((suggestion): suggestion is FileOrganizationSuggestion => suggestion !== null)
      .sort((a, b) => b.confidence - a.confidence);
  }

  getStatistics(files: StorageFile[]): {
    totalFiles: number;
    categorizedFiles: number;
    suggestions: number;
    highConfidence: number;
    categories: Record<string, number>;
  } {
    const suggestions = this.analyzeAllFiles(files);
    
    const categories: Record<string, number> = {};
    suggestions.forEach(suggestion => {
      categories[suggestion.category] = (categories[suggestion.category] || 0) + 1;
    });

    return {
      totalFiles: files.length,
      categorizedFiles: suggestions.length,
      suggestions: suggestions.length,
      highConfidence: suggestions.filter(s => s.confidence > 80).length,
      categories
    };
  }

  // Apply suggestions to files
  async applySuggestion(suggestion: FileOrganizationSuggestion): Promise<boolean> {
    try {
      // This would integrate with the actual storage manager
      // For now, we'll just simulate the application
      
      // Add suggested tags
      if (suggestion.suggestedTags.length > 0) {
        suggestion.suggestedTags.forEach(tagName => {
          // In a real implementation, this would integrate with the tagging system
          console.log(`Would tag file ${suggestion.fileId} with ${tagName}`);
        });
      }

      // Move file if suggested
      if (suggestion.actions.move && suggestion.suggestedPath !== suggestion.currentPath) {
        console.log(`Would move file ${suggestion.fileId} to ${suggestion.suggestedPath}`);
      }

      // Rename file if suggested
      if (suggestion.actions.rename) {
        console.log(`Would rename file ${suggestion.fileId} to ${suggestion.actions.rename}`);
      }

      return true;
    } catch (error) {
      console.error('Failed to apply suggestion:', error);
      return false;
    }
  }

  // Batch apply suggestions
  async applySuggestions(suggestions: FileOrganizationSuggestion[]): Promise<{
    success: number;
    failed: number;
    errors: string[];
  }> {
    let success = 0;
    let failed = 0;
    const errors: string[] = [];

    for (const suggestion of suggestions) {
      try {
        const result = await this.applySuggestion(suggestion);
        if (result) {
          success++;
        } else {
          failed++;
          errors.push(`Failed to apply suggestion for file ${suggestion.fileName}`);
        }
      } catch (error) {
        failed++;
        errors.push(`Error applying suggestion for file ${suggestion.fileName}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    return { success, failed, errors };
  }

  // Add custom rule
  addRule(rule: OrganizationRule): void {
    this.rules.push(rule);
    this.rules.sort((a, b) => b.priority - a.priority);
  }

  // Remove rule
  removeRule(ruleId: string): boolean {
    const initialLength = this.rules.length;
    this.rules = this.rules.filter(rule => rule.id !== ruleId);
    return this.rules.length < initialLength;
  }

  // Update rule
  updateRule(ruleId: string, updates: Partial<OrganizationRule>): boolean {
    const ruleIndex = this.rules.findIndex(rule => rule.id === ruleId);
    if (ruleIndex === -1) return false;
    
    this.rules[ruleIndex] = { ...this.rules[ruleIndex], ...updates };
    this.rules.sort((a, b) => b.priority - a.priority);
    return true;
  }

  // Get all rules
  getRules(): OrganizationRule[] {
    return [...this.rules];
  }

  // Enable/disable rule
  toggleRule(ruleId: string, enabled: boolean): boolean {
    const rule = this.rules.find(rule => rule.id === ruleId);
    if (!rule) return false;
    
    rule.enabled = enabled;
    return true;
  }
}

// Create a global instance
export const smartFileOrganizer = new SmartFileOrganizer();

// React hook for using the smart organizer
export const useSmartFileOrganization = (files: StorageFile[]) => {
  const [suggestions, setSuggestions] = React.useState<FileOrganizationSuggestion[]>([]);
  const [loading, setLoading] = React.useState(false);
  const [stats, setStats] = React.useState<ReturnType<SmartFileOrganizer['getStatistics']>>({
    totalFiles: 0,
    categorizedFiles: 0,
    suggestions: 0,
    highConfidence: 0,
    categories: {}
  });

  const analyzeFiles = React.useCallback(() => {
    setLoading(true);
    try {
      const fileSuggestions = smartFileOrganizer.analyzeAllFiles(files);
      setSuggestions(fileSuggestions);
      setStats(smartFileOrganizer.getStatistics(files));
    } catch (error) {
      console.error('Failed to analyze files:', error);
    } finally {
      setLoading(false);
    }
  }, [files]);

  const applySuggestion = React.useCallback(async (suggestion: FileOrganizationSuggestion) => {
    const result = await smartFileOrganizer.applySuggestion(suggestion);
    if (result) {
      // Re-analyze files after applying suggestion
      analyzeFiles();
    }
    return result;
  }, [analyzeFiles]);

  const applyAllSuggestions = React.useCallback(async (filter?: (suggestion: FileOrganizationSuggestion) => boolean) => {
    const suggestionsToApply = filter ? suggestions.filter(filter) : suggestions;
    const result = await smartFileOrganizer.applySuggestions(suggestionsToApply);
    analyzeFiles();
    return result;
  }, [suggestions, analyzeFiles]);

  React.useEffect(() => {
    if (files.length > 0) {
      analyzeFiles();
    }
  }, [files, analyzeFiles]);

  return {
    suggestions,
    loading,
    stats,
    analyzeFiles,
    applySuggestion,
    applyAllSuggestions,
    organizer: smartFileOrganizer
  };
};

// Smart Organization Panel Component
export const SmartOrganizationPanel: React.FC<{
  files: StorageFile[];
  onOrganizationComplete?: () => void;
}> = ({ files, onOrganizationComplete }) => {
  const {
    suggestions,
    loading,
    stats,
    analyzeFiles,
    applySuggestion,
    applyAllSuggestions
  } = useSmartFileOrganization(files);

  const highConfidenceSuggestions = suggestions.filter(s => s.confidence > 80);
  const mediumConfidenceSuggestions = suggestions.filter(s => s.confidence >= 50 && s.confidence <= 80);
  const lowConfidenceSuggestions = suggestions.filter(s => s.confidence < 50);

  if (loading) {
    return (
      <div className="p-4 text-center text-muted-foreground">
        <div className="w-6 h-6 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
        <p>Analyzing files for smart organization...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="bg-muted/30 p-3 rounded-lg text-center">
          <p className="text-2xl font-bold">{stats.totalFiles}</p>
          <p className="text-xs text-muted-foreground">Total Files</p>
        </div>
        <div className="bg-muted/30 p-3 rounded-lg text-center">
          <p className="text-2xl font-bold">{stats.suggestions}</p>
          <p className="text-xs text-muted-foreground">Suggestions</p>
        </div>
        <div className="bg-muted/30 p-3 rounded-lg text-center">
          <p className="text-2xl font-bold">{stats.highConfidence}</p>
          <p className="text-xs text-muted-foreground">High Confidence</p>
        </div>
        <div className="bg-muted/30 p-3 rounded-lg text-center">
          <p className="text-2xl font-bold">{Math.round((stats.suggestions / Math.max(stats.totalFiles, 1)) * 100)}%</p>
          <p className="text-xs text-muted-foreground">Coverage</p>
        </div>
      </div>

      {/* Organization Actions */}
      {suggestions.length > 0 && (
        <div className="bg-muted/30 p-4 rounded-lg">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-medium">Smart Organization Actions</h3>
            <div className="flex gap-2">
              <button
                onClick={analyzeFiles}
                className="px-3 py-1 text-sm border rounded hover:bg-muted"
              >
                Refresh
              </button>
              <button
                onClick={() => applyAllSuggestions(s => s.confidence > 80)}
                className="px-3 py-1 text-sm bg-primary text-primary-foreground rounded hover:bg-primary/90"
              >
                Apply High Confidence ({highConfidenceSuggestions.length})
              </button>
            </div>
          </div>

          <div className="space-y-3 max-h-64 overflow-y-auto">
            {highConfidenceSuggestions.slice(0, 5).map((suggestion, index) => (
              <div key={index} className="flex items-center gap-3 p-3 bg-background rounded border">
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <p className="text-sm font-medium">{suggestion.fileName}</p>
                    <span className="text-xs px-2 py-0.5 bg-green-100 text-green-800 rounded-full">
                      {suggestion.confidence}% confident
                    </span>
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    {suggestion.reasoning}
                  </p>
                  <div className="flex items-center gap-2 mt-2">
                    {suggestion.actions.move && (
                      <span className="text-xs px-2 py-0.5 bg-blue-100 text-blue-800 rounded">
                        Move to {suggestion.suggestedPath}
                      </span>
                    )}
                    {suggestion.suggestedTags.map(tag => (
                      <span key={tag} className="text-xs px-2 py-0.5 bg-purple-100 text-purple-800 rounded">
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
                <button
                  onClick={() => applySuggestion(suggestion)}
                  className="px-3 py-1 text-xs bg-primary text-primary-foreground rounded hover:bg-primary/90"
                >
                  Apply
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Category Breakdown */}
      {Object.keys(stats.categories).length > 0 && (
        <div>
          <h3 className="font-medium mb-3">Organization Categories</h3>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
            {Object.entries(stats.categories).map(([category, count]) => (
              <div key={category} className="p-3 bg-muted/30 rounded">
                <p className="text-sm font-medium">{category}</p>
                <p className="text-2xl font-bold">{count}</p>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Manual Refresh */}
      <div className="text-center">
        <button
          onClick={analyzeFiles}
          className="px-4 py-2 text-sm border rounded hover:bg-muted"
        >
          Re-analyze Files
        </button>
      </div>
    </div>
  );
};