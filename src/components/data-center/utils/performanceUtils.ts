/**
 * Performance Utilities
 * Tools for bundle optimization, tree shaking, and performance monitoring
 */

// Bundle analysis utilities
export interface BundleAnalysis {
  totalSize: number;
  gzippedSize: number;
  modules: Array<{
    name: string;
    size: number;
    chunks: string[];
  }>;
  chunks: Array<{
    name: string;
    size: number;
    modules: string[];
  }>;
  duplicates: Array<{
    module: string;
    occurrences: number;
    totalSize: number;
  }>;
}

// Performance metrics interface
export interface PerformanceMetrics {
  loadTime: number;
  renderTime: number;
  memoryUsage: number;
  bundleSize: number;
  cacheHitRate: number;
  componentCount: number;
}

// Tree shaking analysis
export interface TreeShakingReport {
  unusedExports: Array<{
    module: string;
    exports: string[];
    potentialSavings: number;
  }>;
  sideEffects: Array<{
    module: string;
    reason: string;
  }>;
  recommendations: string[];
}

// Performance monitoring class
export class PerformanceMonitor {
  private metrics: Map<string, number[]> = new Map();
  private observers: PerformanceObserver[] = [];

  constructor() {
    this.initializeObservers();
  }

  private initializeObservers() {
    if (typeof window !== 'undefined' && 'PerformanceObserver' in window) {
      // Monitor navigation timing
      const navObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'navigation') {
            const navEntry = entry as PerformanceNavigationTiming;
            this.recordMetric('loadTime', navEntry.loadEventEnd - navEntry.loadEventStart);
            this.recordMetric('domContentLoaded', navEntry.domContentLoadedEventEnd - navEntry.domContentLoadedEventStart);
          }
        }
      });
      navObserver.observe({ entryTypes: ['navigation'] });
      this.observers.push(navObserver);

      // Monitor resource timing
      const resourceObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'resource') {
            const resourceEntry = entry as PerformanceResourceTiming;
            this.recordMetric(`resource_${resourceEntry.name}`, resourceEntry.duration);
          }
        }
      });
      resourceObserver.observe({ entryTypes: ['resource'] });
      this.observers.push(resourceObserver);

      // Monitor long tasks
      const longTaskObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'longtask') {
            this.recordMetric('longTask', entry.duration);
          }
        }
      });
      longTaskObserver.observe({ entryTypes: ['longtask'] });
      this.observers.push(longTaskObserver);
    }
  }

  recordMetric(name: string, value: number) {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }
    this.metrics.get(name)!.push(value);
  }

  getMetrics(): Record<string, { avg: number; min: number; max: number; count: number }> {
    const result: Record<string, { avg: number; min: number; max: number; count: number }> = {};
    
    for (const [name, values] of this.metrics.entries()) {
      if (values.length > 0) {
        result[name] = {
          avg: values.reduce((sum, val) => sum + val, 0) / values.length,
          min: Math.min(...values),
          max: Math.max(...values),
          count: values.length
        };
      }
    }
    
    return result;
  }

  clearMetrics() {
    this.metrics.clear();
  }

  destroy() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
    this.metrics.clear();
  }
}

// Bundle size analyzer
export class BundleSizeAnalyzer {
  static async analyzeBundleSize(): Promise<BundleAnalysis> {
    // This would typically integrate with webpack-bundle-analyzer or similar
    // For now, we'll provide a mock implementation
    return {
      totalSize: 0,
      gzippedSize: 0,
      modules: [],
      chunks: [],
      duplicates: []
    };
  }

  static detectUnusedCode(): TreeShakingReport {
    // This would analyze the bundle for unused exports
    return {
      unusedExports: [],
      sideEffects: [],
      recommendations: [
        'Consider using dynamic imports for large components',
        'Remove unused utility functions',
        'Optimize third-party library imports'
      ]
    };
  }
}

// Memory usage monitor
export class MemoryMonitor {
  private static instance: MemoryMonitor;
  private measurements: Array<{ timestamp: number; usage: number }> = [];
  private intervalId?: number;

  static getInstance(): MemoryMonitor {
    if (!MemoryMonitor.instance) {
      MemoryMonitor.instance = new MemoryMonitor();
    }
    return MemoryMonitor.instance;
  }

  startMonitoring(intervalMs = 5000) {
    if (typeof window !== 'undefined' && 'performance' in window && 'memory' in (window.performance as any)) {
      this.intervalId = window.setInterval(() => {
        const memory = (window.performance as any).memory;
        this.measurements.push({
          timestamp: Date.now(),
          usage: memory.usedJSHeapSize
        });
        
        // Keep only last 100 measurements
        if (this.measurements.length > 100) {
          this.measurements = this.measurements.slice(-100);
        }
      }, intervalMs);
    }
  }

  stopMonitoring() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = undefined;
    }
  }

  getCurrentUsage(): number {
    if (typeof window !== 'undefined' && 'performance' in window && 'memory' in (window.performance as any)) {
      return (window.performance as any).memory.usedJSHeapSize;
    }
    return 0;
  }

  getUsageHistory(): Array<{ timestamp: number; usage: number }> {
    return [...this.measurements];
  }

  getMemoryStats() {
    if (this.measurements.length === 0) return null;
    
    const usages = this.measurements.map(m => m.usage);
    return {
      current: this.getCurrentUsage(),
      average: usages.reduce((sum, usage) => sum + usage, 0) / usages.length,
      peak: Math.max(...usages),
      minimum: Math.min(...usages)
    };
  }
}

// Component performance tracker
export class ComponentPerformanceTracker {
  private static renderTimes: Map<string, number[]> = new Map();
  private static mountTimes: Map<string, number> = new Map();

  static startRender(componentName: string) {
    this.mountTimes.set(componentName, performance.now());
  }

  static endRender(componentName: string) {
    const startTime = this.mountTimes.get(componentName);
    if (startTime) {
      const renderTime = performance.now() - startTime;
      
      if (!this.renderTimes.has(componentName)) {
        this.renderTimes.set(componentName, []);
      }
      
      this.renderTimes.get(componentName)!.push(renderTime);
      this.mountTimes.delete(componentName);
      
      return renderTime;
    }
    return 0;
  }

  static getComponentStats(componentName: string) {
    const times = this.renderTimes.get(componentName) || [];
    if (times.length === 0) return null;
    
    return {
      averageRenderTime: times.reduce((sum, time) => sum + time, 0) / times.length,
      minRenderTime: Math.min(...times),
      maxRenderTime: Math.max(...times),
      renderCount: times.length
    };
  }

  static getAllStats() {
    const stats: Record<string, any> = {};
    for (const [componentName] of this.renderTimes.entries()) {
      stats[componentName] = this.getComponentStats(componentName);
    }
    return stats;
  }

  static clearStats() {
    this.renderTimes.clear();
    this.mountTimes.clear();
  }
}

// Performance optimization recommendations
export class PerformanceOptimizer {
  static analyzePerformance(metrics: PerformanceMetrics): string[] {
    const recommendations: string[] = [];
    
    if (metrics.loadTime > 3000) {
      recommendations.push('Consider implementing code splitting to reduce initial bundle size');
    }
    
    if (metrics.renderTime > 100) {
      recommendations.push('Optimize component rendering with React.memo or useMemo');
    }
    
    if (metrics.memoryUsage > 50 * 1024 * 1024) { // 50MB
      recommendations.push('Monitor memory usage - consider implementing virtual scrolling for large datasets');
    }
    
    if (metrics.bundleSize > 1024 * 1024) { // 1MB
      recommendations.push('Bundle size is large - consider lazy loading non-critical components');
    }
    
    if (metrics.cacheHitRate < 0.8) {
      recommendations.push('Improve caching strategy to reduce redundant data fetching');
    }
    
    if (metrics.componentCount > 100) {
      recommendations.push('Consider component virtualization for better performance with many components');
    }
    
    return recommendations;
  }

  static generateOptimizationPlan(analysis: BundleAnalysis, metrics: PerformanceMetrics): {
    priority: 'high' | 'medium' | 'low';
    actions: string[];
  } {
    const actions: string[] = [];
    let priority: 'high' | 'medium' | 'low' = 'low';
    
    // High priority optimizations
    if (metrics.loadTime > 5000 || metrics.bundleSize > 2 * 1024 * 1024) {
      priority = 'high';
      actions.push('Implement aggressive code splitting');
      actions.push('Enable tree shaking for all dependencies');
      actions.push('Use dynamic imports for route-based splitting');
    }
    
    // Medium priority optimizations
    if (metrics.renderTime > 200 || analysis.duplicates.length > 5) {
      if (priority !== 'high') priority = 'medium';
      actions.push('Optimize component re-renders');
      actions.push('Remove duplicate dependencies');
      actions.push('Implement component memoization');
    }
    
    // Low priority optimizations
    if (metrics.cacheHitRate < 0.9) {
      actions.push('Improve caching strategies');
      actions.push('Implement service worker for asset caching');
    }
    
    return { priority, actions };
  }
}

// Export singleton instances
export const performanceMonitor = new PerformanceMonitor();
export const memoryMonitor = MemoryMonitor.getInstance();

// Utility functions
export const formatBytes = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

export const formatDuration = (ms: number): string => {
  if (ms < 1000) return `${ms.toFixed(1)}ms`;
  return `${(ms / 1000).toFixed(2)}s`;
};

export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};