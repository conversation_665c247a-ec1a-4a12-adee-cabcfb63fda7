/**
 * Validation utilities for the Data Center
 */

/**
 * Validation result interface
 */
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings?: string[];
}

/**
 * Validates file upload constraints
 */
export const validateFileUpload = (
  file: File,
  options: {
    maxSize?: number;
    acceptedTypes?: string[];
    minSize?: number;
  } = {}
): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  // Check file size
  if (options.maxSize && file.size > options.maxSize) {
    errors.push(`File size (${formatBytes(file.size)}) exceeds maximum allowed size (${formatBytes(options.maxSize)})`);
  }
  
  if (options.minSize && file.size < options.minSize) {
    errors.push(`File size (${formatBytes(file.size)}) is below minimum required size (${formatBytes(options.minSize)})`);
  }
  
  // Check file type
  if (options.acceptedTypes && options.acceptedTypes.length > 0) {
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
    const acceptedExtensions = options.acceptedTypes.map(type => type.toLowerCase());
    
    if (!acceptedExtensions.includes(fileExtension)) {
      errors.push(`File type '${fileExtension}' is not supported. Accepted types: ${options.acceptedTypes.join(', ')}`);
    }
  }
  
  // Check for empty file
  if (file.size === 0) {
    errors.push('File is empty');
  }
  
  // Warnings for large files
  if (file.size > 50 * 1024 * 1024) { // 50MB
    warnings.push('Large file detected. Processing may take longer than usual.');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

/**
 * Validates data structure for processing
 */
export const validateDataStructure = (data: {
  headers: string[];
  rows: any[][];
}): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  // Check for empty data
  if (!data.headers || data.headers.length === 0) {
    errors.push('No headers found in data');
  }
  
  if (!data.rows || data.rows.length === 0) {
    errors.push('No data rows found');
  }
  
  if (data.headers && data.rows) {
    // Check for consistent row lengths
    const expectedLength = data.headers.length;
    const inconsistentRows = data.rows.filter(row => row.length !== expectedLength);
    
    if (inconsistentRows.length > 0) {
      warnings.push(`${inconsistentRows.length} rows have inconsistent column count. Expected: ${expectedLength}`);
    }
    
    // Check for duplicate headers
    const duplicateHeaders = data.headers.filter((header, index) => 
      data.headers.indexOf(header) !== index
    );
    
    if (duplicateHeaders.length > 0) {
      warnings.push(`Duplicate headers found: ${duplicateHeaders.join(', ')}`);
    }
    
    // Check for empty headers
    const emptyHeaders = data.headers.filter(header => !header || header.trim() === '');
    if (emptyHeaders.length > 0) {
      warnings.push(`${emptyHeaders.length} empty headers found`);
    }
    
    // Check data quality
    if (data.rows.length > 1000) {
      warnings.push('Large dataset detected. Consider using pagination for better performance.');
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

/**
 * Validates SQL query syntax and safety
 */
export const validateSQLQuery = (sql: string): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  if (!sql || sql.trim() === '') {
    errors.push('SQL query is empty');
    return { isValid: false, errors, warnings };
  }
  
  const trimmedSQL = sql.trim().toUpperCase();
  
  // Check for dangerous operations
  const dangerousKeywords = ['DROP', 'DELETE', 'TRUNCATE', 'ALTER', 'CREATE', 'INSERT', 'UPDATE'];
  const foundDangerous = dangerousKeywords.filter(keyword => trimmedSQL.includes(keyword));
  
  if (foundDangerous.length > 0) {
    warnings.push(`Query contains potentially destructive operations: ${foundDangerous.join(', ')}`);
  }
  
  // Check for SQL injection patterns
  const injectionPatterns = [
    /;\s*(DROP|DELETE|INSERT|UPDATE)/i,
    /UNION\s+SELECT/i,
    /--/,
    /\/\*/,
    /xp_cmdshell/i,
    /sp_executesql/i
  ];
  
  const foundInjection = injectionPatterns.some(pattern => pattern.test(sql));
  if (foundInjection) {
    errors.push('Query contains potentially malicious patterns');
  }
  
  // Check for balanced parentheses
  const openParens = (sql.match(/\(/g) || []).length;
  const closeParens = (sql.match(/\)/g) || []).length;
  
  if (openParens !== closeParens) {
    errors.push('Unbalanced parentheses in query');
  }
  
  // Check for basic SQL structure
  const validStarters = ['SELECT', 'WITH', 'SHOW', 'DESCRIBE', 'EXPLAIN'];
  const firstWord = trimmedSQL.split(/\s+/)[0];
  
  if (!validStarters.includes(firstWord)) {
    warnings.push('Query should typically start with SELECT, WITH, SHOW, DESCRIBE, or EXPLAIN for data analysis');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

/**
 * Validates configuration object
 */
export const validateConfiguration = (config: Record<string, any>): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  // Check for required fields
  const requiredFields = ['theme', 'features'];
  const missingFields = requiredFields.filter(field => !(field in config));
  
  if (missingFields.length > 0) {
    errors.push(`Missing required configuration fields: ${missingFields.join(', ')}`);
  }
  
  // Validate theme
  if (config.theme && !['light', 'dark', 'auto'].includes(config.theme)) {
    errors.push(`Invalid theme value: ${config.theme}. Must be 'light', 'dark', or 'auto'`);
  }
  
  // Validate features array
  if (config.features && !Array.isArray(config.features)) {
    errors.push('Features must be an array');
  }
  
  // Check for deprecated options
  const deprecatedFields = ['oldTheme', 'legacyMode'];
  const foundDeprecated = deprecatedFields.filter(field => field in config);
  
  if (foundDeprecated.length > 0) {
    warnings.push(`Deprecated configuration fields found: ${foundDeprecated.join(', ')}`);
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

/**
 * Validates form input values
 */
export const validateFormInput = (
  value: any,
  rules: {
    required?: boolean;
    minLength?: number;
    maxLength?: number;
    pattern?: RegExp;
    type?: 'email' | 'url' | 'number' | 'integer';
    min?: number;
    max?: number;
  }
): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  // Required validation
  if (rules.required && (value === null || value === undefined || value === '')) {
    errors.push('This field is required');
    return { isValid: false, errors, warnings };
  }
  
  // Skip other validations if value is empty and not required
  if (!rules.required && (value === null || value === undefined || value === '')) {
    return { isValid: true, errors, warnings };
  }
  
  const stringValue = String(value);
  
  // Length validations
  if (rules.minLength && stringValue.length < rules.minLength) {
    errors.push(`Minimum length is ${rules.minLength} characters`);
  }
  
  if (rules.maxLength && stringValue.length > rules.maxLength) {
    errors.push(`Maximum length is ${rules.maxLength} characters`);
  }
  
  // Pattern validation
  if (rules.pattern && !rules.pattern.test(stringValue)) {
    errors.push('Invalid format');
  }
  
  // Type-specific validations
  if (rules.type) {
    switch (rules.type) {
      case 'email':
        const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailPattern.test(stringValue)) {
          errors.push('Invalid email format');
        }
        break;
        
      case 'url':
        try {
          new URL(stringValue);
        } catch {
          errors.push('Invalid URL format');
        }
        break;
        
      case 'number':
      case 'integer':
        const numValue = Number(value);
        if (isNaN(numValue)) {
          errors.push('Must be a valid number');
        } else {
          if (rules.type === 'integer' && !Number.isInteger(numValue)) {
            errors.push('Must be an integer');
          }
          
          if (rules.min !== undefined && numValue < rules.min) {
            errors.push(`Minimum value is ${rules.min}`);
          }
          
          if (rules.max !== undefined && numValue > rules.max) {
            errors.push(`Maximum value is ${rules.max}`);
          }
        }
        break;
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

/**
 * Helper function to format bytes
 */
const formatBytes = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Validates multiple values at once
 */
export const validateBatch = (
  values: Record<string, any>,
  rules: Record<string, any>
): Record<string, ValidationResult> => {
  const results: Record<string, ValidationResult> = {};
  
  Object.keys(values).forEach(key => {
    if (rules[key]) {
      results[key] = validateFormInput(values[key], rules[key]);
    }
  });
  
  return results;
};

/**
 * Checks if all validation results are valid
 */
export const isAllValid = (results: Record<string, ValidationResult>): boolean => {
  return Object.values(results).every(result => result.isValid);
};

/**
 * Collects all errors from validation results
 */
export const collectErrors = (results: Record<string, ValidationResult>): string[] => {
  return Object.values(results).flatMap(result => result.errors);
};

/**
 * Collects all warnings from validation results
 */
export const collectWarnings = (results: Record<string, ValidationResult>): string[] => {
  return Object.values(results).flatMap(result => result.warnings || []);
};