import React, { createContext, useContext, useEffect, useCallback } from 'react';

interface Shortcut {
  key: string;
  ctrl?: boolean;
  shift?: boolean;
  alt?: boolean;
  meta?: boolean; // Cmd on Mac, Ctrl on Windows
  callback: () => void;
  description: string;
  scope?: string; // Optional scope to limit where shortcut works
}

interface ShortcutContextType {
  registerShortcut: (shortcut: Shortcut) => void;
  unregisterShortcut: (key: string, scope?: string) => void;
  shortcuts: Shortcut[];
}

const ShortcutContext = createContext<ShortcutContextType | undefined>(undefined);

export const ShortcutProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [shortcuts, setShortcuts] = React.useState<Shortcut[]>([]);

  const normalizeKey = (event: KeyboardEvent): string => {
    const key = event.key.toLowerCase();
    const modifiers = [];
    
    if (event.ctrlKey) modifiers.push('ctrl');
    if (event.shiftKey) modifiers.push('shift');
    if (event.altKey) modifiers.push('alt');
    if (event.metaKey) modifiers.push('meta');
    
    return [...modifiers, key].join('+');
  };

  const matchesShortcut = (event: KeyboardEvent, shortcut: Shortcut): boolean => {
    const eventKey = event.key.toLowerCase();
    const shortcutKey = shortcut.key.toLowerCase();
    
    return (
      eventKey === shortcutKey &&
      !!event.ctrlKey === !!shortcut.ctrl &&
      !!event.shiftKey === !!shortcut.shift &&
      !!event.altKey === !!shortcut.alt &&
      !!event.metaKey === !!shortcut.meta
    );
  };

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    // Don't trigger shortcuts when in input fields
    const target = event.target as HTMLElement;
    if (target?.tagName === 'INPUT' || target?.tagName === 'TEXTAREA' || target?.contentEditable === 'true') {
      return;
    }

    const matchingShortcut = shortcuts.find(shortcut => matchesShortcut(event, shortcut));
    
    if (matchingShortcut) {
      event.preventDefault();
      matchingShortcut.callback();
    }
  }, [shortcuts]);

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown]);

  const registerShortcut = (shortcut: Shortcut) => {
    setShortcuts(prev => {
      // Remove existing shortcut with same key and scope
      const filtered = prev.filter(s => 
        !(s.key === shortcut.key && s.scope === shortcut.scope)
      );
      return [...filtered, shortcut];
    });
  };

  const unregisterShortcut = (key: string, scope?: string) => {
    setShortcuts(prev => prev.filter(s => !(s.key === key && s.scope === scope)));
  };

  return (
    <ShortcutContext.Provider value={{ registerShortcut, unregisterShortcut, shortcuts }}>
      {children}
    </ShortcutContext.Provider>
  );
};

export const useShortcuts = () => {
  const context = useContext(ShortcutContext);
  if (context === undefined) {
    throw new Error('useShortcuts must be used within a ShortcutProvider');
  }
  return context;
};

// Predefined common shortcuts
export const COMMON_SHORTCUTS = {
  // Navigation
  NAVIGATE_DASHBOARD: { key: 'd', ctrl: true, description: 'Navigate to Dashboard' },
  NAVIGATE_UPLOAD: { key: 'u', ctrl: true, description: 'Navigate to Upload' },
  NAVIGATE_GRID: { key: 'g', ctrl: true, description: 'Navigate to Data Grid' },
  NAVIGATE_SQL: { key: 's', ctrl: true, description: 'Navigate to SQL Builder' },
  
  // Actions
  EXECUTE_QUERY: { key: 'enter', ctrl: true, description: 'Execute current query' },
  SAVE_QUERY: { key: 's', ctrl: true, description: 'Save current query' },
  COPY_QUERY: { key: 'c', ctrl: true, description: 'Copy current query' },
  CLEAR_QUERY: { key: 'l', ctrl: true, description: 'Clear query editor' },
  
  // View
  TOGGLE_FULLSCREEN: { key: 'f', ctrl: true, description: 'Toggle fullscreen mode' },
  TOGGLE_SCHEMA: { key: 'b', ctrl: true, description: 'Toggle schema browser' },
  TOGGLE_SIDEBAR: { key: '.', ctrl: true, description: 'Toggle sidebar' },
  
  // Data operations
  REFRESH_DATA: { key: 'r', ctrl: true, description: 'Refresh data' },
  EXPORT_DATA: { key: 'e', ctrl: true, description: 'Export data' },
  SEARCH_DATA: { key: 'k', ctrl: true, description: 'Focus search' },
  
  // General
  SHOW_HELP: { key: '/', ctrl: true, description: 'Show help' },
  TOGGLE_SHORTCUTS: { key: '?', ctrl: true, description: 'Toggle shortcuts panel' }
};

// Helper function to format shortcut for display
export const formatShortcut = (shortcut: Shortcut): string => {
  const parts = [];
  if (shortcut.ctrl) parts.push('Ctrl');
  if (shortcut.shift) parts.push('Shift');
  if (shortcut.alt) parts.push('Alt');
  if (shortcut.meta) parts.push('Cmd'); // or 'Ctrl' based on platform
  
  const key = shortcut.key === ' ' ? 'Space' : 
              shortcut.key === 'enter' ? 'Enter' :
              shortcut.key === 'escape' ? 'Esc' :
              shortcut.key.charAt(0).toUpperCase() + shortcut.key.slice(1);
  
  parts.push(key);
  return parts.join('+');
};

// Component to display available shortcuts
export const ShortcutHelp: React.FC<{ 
  onClose: () => void;
  title?: string;
}> = ({ onClose, title = 'Keyboard Shortcuts' }) => {
  const { shortcuts } = useShortcuts();
  
  // Group shortcuts by category
  const groupedShortcuts = shortcuts.reduce((acc, shortcut) => {
    const scope = shortcut.scope || 'General';
    if (!acc[scope]) acc[scope] = [];
    acc[scope].push(shortcut);
    return acc;
  }, {} as Record<string, Shortcut[]>);

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[80vh] overflow-hidden flex flex-col">
        <div className="flex items-center justify-between p-4 border-b">
          <h3 className="text-lg font-semibold">{title}</h3>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full"
            aria-label="Close"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        <div className="flex-1 overflow-y-auto p-4">
          {Object.entries(groupedShortcuts).map(([category, categoryShortcuts]) => (
            <div key={category} className="mb-6">
              <h4 className="text-sm font-medium text-gray-900 mb-3 border-b pb-1">
                {category}
              </h4>
              <div className="space-y-2">
                {categoryShortcuts.map((shortcut, index) => (
                  <div key={index} className="flex items-center justify-between py-2 px-3 bg-gray-50 rounded">
                    <span className="text-sm text-gray-700">{shortcut.description}</span>
                    <kbd className="px-2 py-1 text-xs font-semibold text-gray-800 bg-white border border-gray-200 rounded">
                      {formatShortcut(shortcut)}
                    </kbd>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
        
        <div className="p-4 border-t bg-gray-50 text-sm text-gray-500">
          <p>Press <kbd className="px-1 py-0.5 text-xs font-semibold bg-white border border-gray-200 rounded">Esc</kbd> to close this panel</p>
        </div>
      </div>
    </div>
  );
};