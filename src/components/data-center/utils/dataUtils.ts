/**
 * Data processing utilities for the Data Center
 */

import { DataPreview } from '../types';

/**
 * Infers data types from sample data
 */
export const inferDataTypes = (headers: string[], rows: any[][]): Record<string, string> => {
  const types: Record<string, string> = {};
  
  headers.forEach((header, index) => {
    const sample = rows.slice(0, 10).map(row => row[index]).filter(val => val != null && val !== '');
    
    if (sample.length === 0) {
      types[header] = 'unknown';
      return;
    }
    
    // Check if all values are numbers
    const allNumbers = sample.every(val => !isNaN(Number(val)));
    if (allNumbers) {
      const hasDecimals = sample.some(val => String(val).includes('.'));
      types[header] = hasDecimals ? 'float' : 'integer';
      return;
    }
    
    // Check if all values are dates
    const allDates = sample.every(val => !isNaN(Date.parse(val)));
    if (allDates) {
      types[header] = 'date';
      return;
    }
    
    // Check if all values are booleans
    const allBooleans = sample.every(val => 
      ['true', 'false', '1', '0', 'yes', 'no'].includes(String(val).toLowerCase())
    );
    if (allBooleans) {
      types[header] = 'boolean';
      return;
    }
    
    // Default to string
    types[header] = 'string';
  });
  
  return types;
};

/**
 * Generates basic statistics for data
 */
export const generateStatistics = (data: DataPreview): Record<string, any> => {
  const stats: Record<string, any> = {
    totalRows: data.totalRows,
    totalColumns: data.headers.length,
    columnStats: {}
  };
  
  data.headers.forEach((header, index) => {
    const columnData = data.rows.map(row => row[index]).filter(val => val != null && val !== '');
    const dataType = data.dataTypes?.[header] || 'unknown';
    
    stats.columnStats[header] = {
      type: dataType,
      nonNullCount: columnData.length,
      nullCount: data.totalRows - columnData.length,
      uniqueCount: new Set(columnData).size
    };
    
    if (dataType === 'integer' || dataType === 'float') {
      const numbers = columnData.map(val => Number(val));
      stats.columnStats[header] = {
        ...stats.columnStats[header],
        min: Math.min(...numbers),
        max: Math.max(...numbers),
        mean: numbers.reduce((a, b) => a + b, 0) / numbers.length,
        median: getMedian(numbers)
      };
    }
    
    if (dataType === 'string') {
      const lengths = columnData.map(val => String(val).length);
      stats.columnStats[header] = {
        ...stats.columnStats[header],
        avgLength: lengths.reduce((a, b) => a + b, 0) / lengths.length,
        minLength: Math.min(...lengths),
        maxLength: Math.max(...lengths)
      };
    }
  });
  
  return stats;
};

/**
 * Calculates median of an array of numbers
 */
const getMedian = (numbers: number[]): number => {
  const sorted = [...numbers].sort((a, b) => a - b);
  const mid = Math.floor(sorted.length / 2);
  return sorted.length % 2 === 0 
    ? (sorted[mid - 1] + sorted[mid]) / 2 
    : sorted[mid];
};

/**
 * Filters data based on criteria
 */
export const filterData = (
  data: DataPreview, 
  filters: Record<string, any>
): DataPreview => {
  let filteredRows = [...data.rows];
  
  Object.entries(filters).forEach(([column, criteria]) => {
    const columnIndex = data.headers.indexOf(column);
    if (columnIndex === -1) return;
    
    filteredRows = filteredRows.filter(row => {
      const value = row[columnIndex];
      
      if (criteria.equals !== undefined) {
        return String(value).toLowerCase() === String(criteria.equals).toLowerCase();
      }
      
      if (criteria.contains !== undefined) {
        return String(value).toLowerCase().includes(String(criteria.contains).toLowerCase());
      }
      
      if (criteria.min !== undefined || criteria.max !== undefined) {
        const numValue = Number(value);
        if (isNaN(numValue)) return false;
        
        if (criteria.min !== undefined && numValue < criteria.min) return false;
        if (criteria.max !== undefined && numValue > criteria.max) return false;
      }
      
      return true;
    });
  });
  
  return {
    ...data,
    rows: filteredRows,
    totalRows: filteredRows.length
  };
};

/**
 * Sorts data by column
 */
export const sortData = (
  data: DataPreview, 
  column: string, 
  direction: 'asc' | 'desc' = 'asc'
): DataPreview => {
  const columnIndex = data.headers.indexOf(column);
  if (columnIndex === -1) return data;
  
  const sortedRows = [...data.rows].sort((a, b) => {
    const aVal = a[columnIndex];
    const bVal = b[columnIndex];
    
    // Handle null/undefined values
    if (aVal == null && bVal == null) return 0;
    if (aVal == null) return direction === 'asc' ? 1 : -1;
    if (bVal == null) return direction === 'asc' ? -1 : 1;
    
    // Try numeric comparison first
    const aNum = Number(aVal);
    const bNum = Number(bVal);
    
    if (!isNaN(aNum) && !isNaN(bNum)) {
      return direction === 'asc' ? aNum - bNum : bNum - aNum;
    }
    
    // String comparison
    const aStr = String(aVal).toLowerCase();
    const bStr = String(bVal).toLowerCase();
    
    if (direction === 'asc') {
      return aStr < bStr ? -1 : aStr > bStr ? 1 : 0;
    } else {
      return aStr > bStr ? -1 : aStr < bStr ? 1 : 0;
    }
  });
  
  return {
    ...data,
    rows: sortedRows
  };
};

/**
 * Paginates data
 */
export const paginateData = (
  data: DataPreview, 
  page: number, 
  pageSize: number
): DataPreview => {
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  
  return {
    ...data,
    rows: data.rows.slice(startIndex, endIndex)
  };
};

/**
 * Converts data to CSV format
 */
export const dataToCSV = (data: DataPreview): string => {
  const csvRows = [
    data.headers.join(','),
    ...data.rows.map(row => row.map(cell => 
      typeof cell === 'string' && cell.includes(',') 
        ? `"${cell}"` 
        : String(cell)
    ).join(','))
  ];
  
  return csvRows.join('\n');
};

/**
 * Converts data to JSON format
 */
export const dataToJSON = (data: DataPreview): string => {
  const jsonData = data.rows.map(row => {
    const obj: Record<string, any> = {};
    data.headers.forEach((header, index) => {
      obj[header] = row[index];
    });
    return obj;
  });
  
  return JSON.stringify(jsonData, null, 2);
};