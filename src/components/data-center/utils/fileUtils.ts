/**
 * File utility functions for the Data Center
 */

/**
 * Validates file type against accepted types
 */
export const validateFileType = (file: File, acceptedTypes: string): boolean => {
  const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
  const accepted = acceptedTypes.split(',').map(type => type.trim().toLowerCase());
  return accepted.includes(fileExtension);
};

/**
 * Formats file size in human readable format
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Gets file extension from filename
 */
export const getFileExtension = (filename: string): string => {
  return filename.split('.').pop()?.toLowerCase() || '';
};

/**
 * Checks if file is a supported data file
 */
export const isSupportedDataFile = (file: File): boolean => {
  const supportedTypes = ['csv', 'json', 'xlsx', 'xls', 'txt', 'tsv'];
  const extension = getFileExtension(file.name);
  return supportedTypes.includes(extension);
};

/**
 * Parses CSV content to array of objects
 */
export const parseCSV = (content: string): { headers: string[]; rows: any[][] } => {
  const lines = content.split('\n').filter(line => line.trim());
  if (lines.length === 0) return { headers: [], rows: [] };
  
  const headers = lines[0].split(',').map(header => header.trim().replace(/"/g, ''));
  const rows = lines.slice(1).map(line => 
    line.split(',').map(cell => cell.trim().replace(/"/g, ''))
  );
  
  return { headers, rows };
};

/**
 * Parses JSON content to structured data
 */
export const parseJSON = (content: string): { headers: string[]; rows: any[][] } => {
  try {
    const data = JSON.parse(content);
    
    if (Array.isArray(data) && data.length > 0) {
      const headers = Object.keys(data[0]);
      const rows = data.map(item => headers.map(header => item[header]));
      return { headers, rows };
    }
    
    return { headers: [], rows: [] };
  } catch (error) {
    throw new Error('Invalid JSON format');
  }
};

/**
 * Reads file content as text
 */
export const readFileAsText = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => resolve(e.target?.result as string);
    reader.onerror = (e) => reject(new Error('Failed to read file'));
    reader.readAsText(file);
  });
};

/**
 * Generates a unique file ID
 */
export const generateFileId = (file: File): string => {
  return `${file.name}-${file.size}-${file.lastModified}`;
};