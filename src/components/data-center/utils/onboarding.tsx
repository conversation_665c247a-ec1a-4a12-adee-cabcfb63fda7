import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON><PERSON>, 
  Check, 
  X, 
  Lightbulb, 
  MousePointer, 
  Keyboard,
  BookOpen,
  Star,
  Rocket,
  Eye,
  Zap,
  BarChart3,
  Database,
  Code,
  FileText,
  ArrowUpDown,
  Filter,
  Columns,
  Upload
} from 'lucide-react';

// Onboarding Feature Types
export interface OnboardingFeature {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  targetElement?: string; // CSS selector for highlighting
  position?: 'top' | 'bottom' | 'left' | 'right';
  steps?: OnboardingStep[];
}

export interface OnboardingStep {
  id: string;
  title: string;
  content: string;
  targetElement?: string;
  position?: 'top' | 'bottom' | 'left' | 'right';
  interactive?: boolean;
  action?: () => void;
}

// Tutorial Configuration
export const ONBOARDING_TUTORIALS = {
  dataCenterBasics: {
    id: 'dataCenterBasics',
    title: 'Data Center Basics',
    description: 'Learn how to upload, process, and analyze your data',
    duration: '5 min',
    features: [
      {
        id: 'uploadData',
        title: 'Upload Data',
        description: 'Upload CSV, Excel, JSON, and other file formats',
        icon: <Rocket className="w-5 h-5" />,
        targetElement: '[data-tutorial="upload-section"]'
      },
      {
        id: 'previewData',
        title: 'Preview Your Data',
        description: 'View and explore your data before processing',
        icon: <Eye className="w-5 h-5" />,
        targetElement: '[data-tutorial="preview-section"]'
      },
      {
        id: 'processData',
        title: 'Process Data',
        description: 'Apply transformations and extract insights',
        icon: <Zap className="w-5 h-5" />,
        targetElement: '[data-tutorial="process-section"]'
      },
      {
        id: 'analyzeData',
        title: 'Analyze Results',
        description: 'Generate reports and visualizations',
        icon: <BarChart3 className="w-5 h-5" />,
        targetElement: '[data-tutorial="analyze-section"]'
      }
    ]
  },
  
  sqlBuilder: {
    id: 'sqlBuilder',
    title: 'SQL Query Builder',
    description: 'Master the SQL query builder with templates and schema',
    duration: '3 min',
    features: [
      {
        id: 'schemaBrowser',
        title: 'Schema Browser',
        description: 'Explore your database schema and tables',
        icon: <Database className="w-5 h-5" />,
        targetElement: '[data-tutorial="schema-browser"]'
      },
      {
        id: 'queryEditor',
        title: 'Query Editor',
        description: 'Write and execute SQL queries',
        icon: <Code className="w-5 h-5" />,
        targetElement: '[data-tutorial="query-editor"]'
      },
      {
        id: 'templates',
        title: 'Query Templates',
        description: 'Use pre-built templates for common queries',
        icon: <FileText className="w-5 h-5" />,
        targetElement: '[data-tutorial="query-templates"]'
      }
    ]
  },
  
  dataGrid: {
    id: 'dataGrid',
    title: 'Interactive Data Grid',
    description: 'Navigate and manipulate large datasets efficiently',
    duration: '4 min',
    features: [
      {
        id: 'sorting',
        title: 'Column Sorting',
        description: 'Sort data by clicking column headers',
        icon: <ArrowUpDown className="w-5 h-5" />,
        targetElement: '[data-tutorial="grid-sorting"]'
      },
      {
        id: 'filtering',
        title: 'Data Filtering',
        description: 'Filter data using column filters',
        icon: <Filter className="w-5 h-5" />,
        targetElement: '[data-tutorial="grid-filtering"]'
      },
      {
        id: 'columnManagement',
        title: 'Column Management',
        description: 'Show/hide and reorder columns',
        icon: <Columns className="w-5 h-5" />,
        targetElement: '[data-tutorial="grid-columns"]'
      }
    ]
  }
};

// User Progress Tracking
export interface UserOnboardingProgress {
  completedTutorials: string[];
  completedFeatures: string[];
  dismissedTutorials: string[];
  lastTutorial?: string;
  tutorialStep?: number;
  preferences: {
    autoShowTutorials: boolean;
    showTooltips: boolean;
    preferredLearningStyle: 'visual' | 'interactive' | 'reading';
  };
}

// Onboarding Manager Class
export class OnboardingManager {
  private static readonly STORAGE_KEY = 'datacenter_onboarding_v1';
  private static instance: OnboardingManager;
  
  private progress: UserOnboardingProgress;
  
  private constructor() {
    this.progress = this.loadProgress();
  }
  
  static getInstance(): OnboardingManager {
    if (!OnboardingManager.instance) {
      OnboardingManager.instance = new OnboardingManager();
    }
    return OnboardingManager.instance;
  }
  
  private loadProgress(): UserOnboardingProgress {
    try {
      const saved = localStorage.getItem(OnboardingManager.STORAGE_KEY);
      return saved ? JSON.parse(saved) : {
        completedTutorials: [],
        completedFeatures: [],
        dismissedTutorials: [],
        preferences: {
          autoShowTutorials: true,
          showTooltips: true,
          preferredLearningStyle: 'interactive'
        }
      };
    } catch (error) {
      return {
        completedTutorials: [],
        completedFeatures: [],
        dismissedTutorials: [],
        preferences: {
          autoShowTutorials: true,
          showTooltips: true,
          preferredLearningStyle: 'interactive'
        }
      };
    }
  }
  
  private saveProgress(): void {
    try {
      localStorage.setItem(OnboardingManager.STORAGE_KEY, JSON.stringify(this.progress));
    } catch (error) {
    }
  }
  
  // Tutorial Management
  markTutorialCompleted(tutorialId: string): void {
    if (!this.progress.completedTutorials.includes(tutorialId)) {
      this.progress.completedTutorials.push(tutorialId);
      this.saveProgress();
    }
  }
  
  markTutorialDismissed(tutorialId: string): void {
    if (!this.progress.dismissedTutorials.includes(tutorialId)) {
      this.progress.dismissedTutorials.push(tutorialId);
      this.saveProgress();
    }
  }
  
  isTutorialCompleted(tutorialId: string): boolean {
    return this.progress.completedTutorials.includes(tutorialId);
  }
  
  isTutorialDismissed(tutorialId: string): boolean {
    return this.progress.dismissedTutorials.includes(tutorialId);
  }
  
  // Feature Management
  markFeatureCompleted(featureId: string): void {
    if (!this.progress.completedFeatures.includes(featureId)) {
      this.progress.completedFeatures.push(featureId);
      this.saveProgress();
    }
  }
  
  isFeatureCompleted(featureId: string): boolean {
    return this.progress.completedFeatures.includes(featureId);
  }
  
  // Preferences
  getPreferences(): UserOnboardingProgress['preferences'] {
    return { ...this.progress.preferences };
  }
  
  updatePreferences(preferences: Partial<UserOnboardingProgress['preferences']>): void {
    this.progress.preferences = { ...this.progress.preferences, ...preferences };
    this.saveProgress();
  }
  
  // Get next tutorial for user
  getNextTutorial(): string | null {
    const allTutorials = Object.keys(ONBOARDING_TUTORIALS);
    const nextTutorial = allTutorials.find(tutorialId => 
      !this.isTutorialCompleted(tutorialId) && !this.isTutorialDismissed(tutorialId)
    );
    return nextTutorial || null;
  }
  
  // Reset progress
  resetProgress(): void {
    this.progress = {
      completedTutorials: [],
      completedFeatures: [],
      dismissedTutorials: [],
      preferences: {
        autoShowTutorials: true,
        showTooltips: true,
        preferredLearningStyle: 'interactive'
      }
    };
    this.saveProgress();
  }
  
  // Get progress stats
  getProgressStats(): { completed: number; total: number; percentage: number } {
    const total = Object.keys(ONBOARDING_TUTORIALS).length;
    const completed = this.progress.completedTutorials.length;
    const percentage = total > 0 ? Math.round((completed / total) * 100) : 0;
    
    return { completed, total, percentage };
  }
}

// React Hook for Onboarding
export const useOnboarding = () => {
  const [manager] = useState(() => OnboardingManager.getInstance());
  const [progress, setProgress] = useState(() => manager.getProgressStats());
  const [preferences, setPreferences] = useState(() => manager.getPreferences());
  
  const refreshProgress = () => {
    setProgress(manager.getProgressStats());
    setPreferences(manager.getPreferences());
  };
  
  useEffect(() => {
    refreshProgress();
  }, []);
  
  return {
    manager,
    progress,
    preferences,
    refreshProgress,
    markTutorialCompleted: (tutorialId: string) => {
      manager.markTutorialCompleted(tutorialId);
      refreshProgress();
    },
    markTutorialDismissed: (tutorialId: string) => {
      manager.markTutorialDismissed(tutorialId);
      refreshProgress();
    },
    markFeatureCompleted: (featureId: string) => {
      manager.markFeatureCompleted(featureId);
      refreshProgress();
    },
    updatePreferences: (prefs: Partial<UserOnboardingProgress['preferences']>) => {
      manager.updatePreferences(prefs);
      setPreferences(manager.getPreferences());
    },
    resetProgress: () => {
      manager.resetProgress();
      refreshProgress();
    }
  };
};

// Onboarding Tutorial Component
export const OnboardingTutorial: React.FC<{
  tutorialId: string;
  onComplete?: () => void;
  onDismiss?: () => void;
  onStart?: () => void;
}> = ({ tutorialId, onComplete, onDismiss, onStart }) => {
  const { manager, markTutorialCompleted } = useOnboarding();
  const [isOpen, setIsOpen] = useState(true);
  const [currentStep, setCurrentStep] = useState(0);
  
  const tutorial = ONBOARDING_TUTORIALS[tutorialId as keyof typeof ONBOARDING_TUTORIALS];
  if (!tutorial) return null;
  
  const isCompleted = manager.isTutorialCompleted(tutorialId);
  const isDismissed = manager.isTutorialDismissed(tutorialId);
  
  if (isCompleted || isDismissed || !isOpen) return null;
  
  const handleStart = () => {
    setIsOpen(false);
    onStart?.();
  };
  
  const handleComplete = () => {
    markTutorialCompleted(tutorialId);
    setIsOpen(false);
    onComplete?.();
  };
  
  const handleDismiss = () => {
    manager.markTutorialDismissed(tutorialId);
    setIsOpen(false);
    onDismiss?.();
  };
  
  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-md w-full overflow-hidden">
        <div className="p-6">
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-primary/10 rounded-lg">
                <Lightbulb className="w-5 h-5 text-primary" />
              </div>
              <div>
                <h3 className="font-semibold text-lg">{tutorial.title}</h3>
                <p className="text-sm text-muted-foreground">{tutorial.description}</p>
              </div>
            </div>
            <button
              onClick={handleDismiss}
              className="p-1 hover:bg-muted rounded-full"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
          
          <div className="space-y-3 mb-6">
            {tutorial.features.map((feature, index) => (
              <div key={feature.id} className="flex items-start gap-3 p-3 bg-muted/30 rounded-lg">
                <div className="p-1.5 bg-background rounded-md text-primary">
                  {feature.icon}
                </div>
                <div>
                  <h4 className="font-medium text-sm">{feature.title}</h4>
                  <p className="text-xs text-muted-foreground">{feature.description}</p>
                </div>
              </div>
            ))}
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">{tutorial.duration} tutorial</span>
            <div className="flex gap-2">
              <button
                onClick={handleDismiss}
                className="px-4 py-2 text-sm border rounded-lg hover:bg-muted transition-colors"
              >
                Later
              </button>
              <button
                onClick={handleStart}
                className="px-4 py-2 text-sm bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors flex items-center gap-2"
              >
                Start Tutorial
                <ArrowRight className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Feature Discovery Tooltip
export const FeatureTooltip: React.FC<{
  featureId: string;
  title: string;
  description: string;
  position?: 'top' | 'bottom' | 'left' | 'right';
  targetRef: React.RefObject<HTMLElement>;
  onDismiss?: () => void;
}> = ({ featureId, title, description, position = 'top', targetRef, onDismiss }) => {
  const { manager, markFeatureCompleted } = useOnboarding();
  const [isVisible, setIsVisible] = useState(true);
  
  const isCompleted = manager.isFeatureCompleted(featureId);
  if (isCompleted || !isVisible) return null;
  
  const handleDismiss = () => {
    setIsVisible(false);
    markFeatureCompleted(featureId);
    onDismiss?.();
  };
  
  // Position calculation would be implemented here
  // For simplicity, we'll show it as a general tooltip
  
  return (
    <div className="fixed bottom-4 right-4 z-40 bg-card border rounded-lg shadow-lg p-4 max-w-xs">
      <div className="flex items-start justify-between mb-2">
        <h4 className="font-medium text-sm flex items-center gap-2">
          <Lightbulb className="w-4 h-4 text-yellow-500" />
          {title}
        </h4>
        <button
          onClick={handleDismiss}
          className="p-1 hover:bg-muted rounded-full"
        >
          <X className="w-3 h-3" />
        </button>
      </div>
      <p className="text-xs text-muted-foreground mb-3">{description}</p>
      <button
        onClick={handleDismiss}
        className="text-xs text-primary hover:underline"
      >
        Got it
      </button>
    </div>
  );
};

// Progress Dashboard Component
export const OnboardingDashboard: React.FC<{
  onTutorialSelect?: (tutorialId: string) => void;
}> = ({ onTutorialSelect }) => {
  const { progress, resetProgress } = useOnboarding();
  
  const handleReset = () => {
    if (confirm('Reset all onboarding progress?')) {
      resetProgress();
    }
  };
  
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="font-semibold text-lg">Learning Progress</h3>
          <p className="text-sm text-muted-foreground">
            {progress.completed} of {progress.total} tutorials completed
          </p>
        </div>
        <button
          onClick={handleReset}
          className="text-xs text-muted-foreground hover:text-foreground"
        >
          Reset Progress
        </button>
      </div>
      
      <div className="space-y-3">
        {Object.values(ONBOARDING_TUTORIALS).map((tutorial) => (
          <div
            key={tutorial.id}
            className="p-4 border rounded-lg hover:bg-muted/30 cursor-pointer transition-colors"
            onClick={() => onTutorialSelect?.(tutorial.id)}
          >
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-medium">{tutorial.title}</h4>
              {OnboardingManager.getInstance().isTutorialCompleted(tutorial.id) ? (
                <Check className="w-4 h-4 text-green-500" />
              ) : (
                <ArrowRight className="w-4 h-4 text-muted-foreground" />
              )}
            </div>
            <p className="text-sm text-muted-foreground mb-2">{tutorial.description}</p>
            <div className="flex items-center justify-between">
              <span className="text-xs text-muted-foreground">{tutorial.duration}</span>
              <div className="flex gap-1">
                {tutorial.features.slice(0, 3).map((feature) => (
                  <div
                    key={feature.id}
                    className="p-1 bg-muted rounded"
                    title={feature.title}
                  >
                    {feature.icon}
                  </div>
                ))}
                {tutorial.features.length > 3 && (
                  <span className="text-xs text-muted-foreground">
                    +{tutorial.features.length - 3}
                  </span>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Smart Defaults System
export const useSmartDefaults = () => {
  const { preferences } = useOnboarding();
  
  // File type based defaults
  const getFileTypeDefaults = (fileType: string) => {
    const fileTypeMap: Record<string, Record<string, any>> = {
      'text/csv': {
        viewMode: 'grid',
        autoDetectSchema: true,
        delimiter: ',',
        hasHeader: true
      },
      'application/json': {
        viewMode: 'tree',
        expandDepth: 2,
        formatJson: true
      },
      'application/pdf': {
        viewMode: 'preview',
        extractText: true,
        extractImages: true
      },
      'application/vnd.ms-excel': {
        viewMode: 'grid',
        sheetIndex: 0,
        autoDetectSchema: true
      },
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': {
        viewMode: 'grid',
        sheetIndex: 0,
        autoDetectSchema: true
      }
    };
    
    return fileTypeMap[fileType] || {};
  };
  
  // User preference based defaults
  const getPreferenceDefaults = () => {
    const defaults: Record<string, any> = {};
    
    switch (preferences.preferredLearningStyle) {
      case 'visual':
        defaults.showPreviews = true;
        defaults.animateTransitions = true;
        break;
      case 'interactive':
        defaults.enableQuickActions = true;
        defaults.showTooltips = true;
        break;
      case 'reading':
        defaults.showDetailedHelp = true;
        defaults.compactView = false;
        break;
    }
    
    return defaults;
  };
  
  return {
    getFileTypeDefaults,
    getPreferenceDefaults
  };
};

// Contextual Help System
export const useContextualHelp = () => {
  const [activeHelp, setActiveHelp] = useState<string | null>(null);
  
  const showHelp = (helpId: string) => {
    setActiveHelp(helpId);
  };
  
  const hideHelp = () => {
    setActiveHelp(null);
  };
  
  // Help content mapping
  const helpContent: Record<string, { title: string; content: string; icon?: React.ReactNode }> = {
    'upload': {
      title: 'File Upload',
      content: 'Upload various file formats including CSV, Excel, JSON, PDF, and documents. Files are processed securely in your browser.',
      icon: <Upload className="w-4 h-4" />
    },
    'preview': {
      title: 'Data Preview',
      content: 'Preview your data before processing. The system automatically detects data types and provides statistics.',
      icon: <Eye className="w-4 h-4" />
    },
    'process': {
      title: 'Data Processing',
      content: 'Apply transformations, extract entities, and generate insights using Python templates.',
      icon: <Zap className="w-4 h-4" />
    },
    'sql': {
      title: 'SQL Generation',
      content: 'Generate SQL queries from your processed data. Use templates or write custom queries.',
      icon: <Database className="w-4 h-4" />
    }
  };
  
  return {
    activeHelp,
    showHelp,
    hideHelp,
    getHelpContent: (helpId: string) => helpContent[helpId]
  };
};