/**
 * SQL utility functions for the Data Center
 */

import { DataPreview, SQLGenerationResult } from '../types';

/**
 * Generates SQL CREATE TABLE statement from data structure
 */
export const generateCreateTableSQL = (data: DataPreview, tableName = 'data_table'): string => {
  const columns = data.headers.map(header => {
    const dataType = data.dataTypes?.[header] || 'string';
    let sqlType = 'TEXT';
    
    switch (dataType) {
      case 'integer':
        sqlType = 'INTEGER';
        break;
      case 'float':
        sqlType = 'REAL';
        break;
      case 'date':
        sqlType = 'DATE';
        break;
      case 'boolean':
        sqlType = 'BOOLEAN';
        break;
      default:
        sqlType = 'TEXT';
    }
    
    return `  ${sanitizeColumnName(header)} ${sqlType}`;
  }).join(',\n');
  
  return `CREATE TABLE ${tableName} (\n${columns}\n);`;
};

/**
 * Generates SQL SELECT statement with various options
 */
export const generateSelectSQL = (
  data: DataPreview,
  options: {
    columns?: string[];
    limit?: number;
    orderBy?: { column: string; direction: 'ASC' | 'DESC' };
    where?: string;
    tableName?: string;
  } = {}
): SQLGenerationResult => {
  try {
    const tableName = options.tableName || 'data_table';
    const columns = options.columns?.length 
      ? options.columns.map(sanitizeColumnName).join(', ')
      : '*';
    
    let sql = `SELECT ${columns} FROM ${tableName}`;
    
    if (options.where) {
      sql += ` WHERE ${options.where}`;
    }
    
    if (options.orderBy) {
      sql += ` ORDER BY ${sanitizeColumnName(options.orderBy.column)} ${options.orderBy.direction}`;
    }
    
    if (options.limit) {
      sql += ` LIMIT ${options.limit}`;
    }
    
    return {
      sql: sql + ';',
      isValid: true,
      analysis: `Query will select ${columns === '*' ? 'all columns' : columns} from ${tableName}${options.limit ? ` (limited to ${options.limit} rows)` : ''}`
    };
  } catch (error) {
    return {
      sql: '',
      isValid: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

/**
 * Generates SQL INSERT statement
 */
export const generateInsertSQL = (
  data: DataPreview,
  options: {
    tableName?: string;
    includeData?: boolean;
    maxRows?: number;
  } = {}
): SQLGenerationResult => {
  try {
    const tableName = options.tableName || 'data_table';
    const columns = data.headers.map(sanitizeColumnName).join(', ');
    
    let sql = `INSERT INTO ${tableName} (${columns}) VALUES`;
    
    if (options.includeData) {
      const maxRows = options.maxRows || 10;
      const values = data.rows.slice(0, maxRows).map(row => 
        '(' + row.map(cell => formatSQLValue(cell)).join(', ') + ')'
      ).join(',\n  ');
      
      sql += `\n  ${values};`;
    } else {
      const placeholders = data.headers.map(() => '?').join(', ');
      sql += ` (${placeholders});`;
    }
    
    return {
      sql,
      isValid: true,
      analysis: `Insert statement for ${tableName} with ${data.headers.length} columns${options.includeData ? ` and ${Math.min(options.maxRows || 10, data.rows.length)} sample rows` : ''}`
    };
  } catch (error) {
    return {
      sql: '',
      isValid: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

/**
 * Generates SQL UPDATE statement
 */
export const generateUpdateSQL = (
  data: DataPreview,
  options: {
    tableName?: string;
    setColumns?: string[];
    whereCondition?: string;
  } = {}
): SQLGenerationResult => {
  try {
    const tableName = options.tableName || 'data_table';
    const setColumns = options.setColumns || data.headers.slice(0, 3); // Default to first 3 columns
    
    const setClause = setColumns
      .map(col => `${sanitizeColumnName(col)} = ?`)
      .join(', ');
    
    let sql = `UPDATE ${tableName} SET ${setClause}`;
    
    if (options.whereCondition) {
      sql += ` WHERE ${options.whereCondition}`;
    } else {
      sql += ' WHERE id = ?'; // Default condition
    }
    
    return {
      sql: sql + ';',
      isValid: true,
      analysis: `Update statement for ${tableName} affecting ${setColumns.length} columns`
    };
  } catch (error) {
    return {
      sql: '',
      isValid: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

/**
 * Generates SQL DELETE statement
 */
export const generateDeleteSQL = (
  options: {
    tableName?: string;
    whereCondition?: string;
  } = {}
): SQLGenerationResult => {
  try {
    const tableName = options.tableName || 'data_table';
    
    let sql = `DELETE FROM ${tableName}`;
    
    if (options.whereCondition) {
      sql += ` WHERE ${options.whereCondition}`;
    } else {
      sql += ' WHERE id = ?'; // Default condition for safety
    }
    
    return {
      sql: sql + ';',
      isValid: true,
      analysis: `Delete statement for ${tableName}${options.whereCondition ? ` with condition: ${options.whereCondition}` : ' with default id condition'}`
    };
  } catch (error) {
    return {
      sql: '',
      isValid: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

/**
 * Validates SQL syntax (basic validation)
 */
export const validateSQL = (sql: string): { isValid: boolean; error?: string } => {
  try {
    // Basic SQL validation
    const trimmedSQL = sql.trim();
    
    if (!trimmedSQL) {
      return { isValid: false, error: 'SQL query is empty' };
    }
    
    // Check for basic SQL keywords
    const sqlKeywords = ['SELECT', 'INSERT', 'UPDATE', 'DELETE', 'CREATE', 'DROP', 'ALTER'];
    const firstWord = trimmedSQL.split(/\s+/)[0].toUpperCase();
    
    if (!sqlKeywords.includes(firstWord)) {
      return { isValid: false, error: 'SQL must start with a valid keyword' };
    }
    
    // Check for balanced parentheses
    const openParens = (trimmedSQL.match(/\(/g) || []).length;
    const closeParens = (trimmedSQL.match(/\)/g) || []).length;
    
    if (openParens !== closeParens) {
      return { isValid: false, error: 'Unbalanced parentheses in SQL' };
    }
    
    // Check for SQL injection patterns (basic)
    const dangerousPatterns = [
      /;\s*(DROP|DELETE|UPDATE|INSERT)\s+/i,
      /UNION\s+SELECT/i,
      /--\s*$/m
    ];
    
    for (const pattern of dangerousPatterns) {
      if (pattern.test(trimmedSQL)) {
        return { isValid: false, error: 'Potentially dangerous SQL pattern detected' };
      }
    }
    
    return { isValid: true };
  } catch (error) {
    return {
      isValid: false,
      error: error instanceof Error ? error.message : 'Unknown validation error'
    };
  }
};

/**
 * Sanitizes column names for SQL
 */
export const sanitizeColumnName = (columnName: string): string => {
  // Remove special characters and spaces, replace with underscores
  return columnName
    .replace(/[^a-zA-Z0-9_]/g, '_')
    .replace(/^[0-9]/, '_$&') // Prefix with underscore if starts with number
    .toLowerCase();
};

/**
 * Formats a value for SQL insertion
 */
export const formatSQLValue = (value: any): string => {
  if (value === null || value === undefined) {
    return 'NULL';
  }
  
  if (typeof value === 'string') {
    // Escape single quotes
    return `'${value.replace(/'/g, "''")}'`;
  }
  
  if (typeof value === 'number') {
    return String(value);
  }
  
  if (typeof value === 'boolean') {
    return value ? '1' : '0';
  }
  
  // Default to string representation
  return `'${String(value).replace(/'/g, "''")}'`;
};

/**
 * Generates common SQL queries for data analysis
 */
export const generateAnalysisQueries = (data: DataPreview, tableName = 'data_table'): Record<string, string> => {
  const queries: Record<string, string> = {};
  
  // Row count
  queries['Row Count'] = `SELECT COUNT(*) as total_rows FROM ${tableName};`;
  
  // Column info
  queries['Column Info'] = `SELECT * FROM ${tableName} LIMIT 5;`;
  
  // Find numeric columns and generate stats
  const numericColumns = data.headers.filter(header => {
    const dataType = data.dataTypes?.[header];
    return dataType === 'integer' || dataType === 'float';
  });
  
  if (numericColumns.length > 0) {
    const statsColumns = numericColumns.map(col => {
      const sanitized = sanitizeColumnName(col);
      return `AVG(${sanitized}) as avg_${sanitized}, MIN(${sanitized}) as min_${sanitized}, MAX(${sanitized}) as max_${sanitized}`;
    }).join(', ');
    
    queries['Numeric Statistics'] = `SELECT ${statsColumns} FROM ${tableName};`;
  }
  
  // Find string columns and generate frequency analysis
  const stringColumns = data.headers.filter(header => {
    const dataType = data.dataTypes?.[header];
    return dataType === 'string';
  });
  
  if (stringColumns.length > 0) {
    const firstStringCol = sanitizeColumnName(stringColumns[0]);
    queries['Value Frequency'] = `SELECT ${firstStringCol}, COUNT(*) as frequency FROM ${tableName} GROUP BY ${firstStringCol} ORDER BY frequency DESC LIMIT 10;`;
  }
  
  return queries;
};