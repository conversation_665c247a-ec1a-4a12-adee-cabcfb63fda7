import React, { createContext, useContext, useState, useEffect } from 'react';

interface AccessibilitySettings {
  highContrast: boolean;
  colorblindFriendly: boolean;
  keyboardNavigation: boolean;
  fontSize: 'small' | 'medium' | 'large' | 'x-large';
  reducedMotion: boolean;
}

interface AccessibilityContextType {
  settings: AccessibilitySettings;
  updateSettings: (newSettings: Partial<AccessibilitySettings>) => void;
  applySettings: () => void;
}

const AccessibilityContext = createContext<AccessibilityContextType | undefined>(undefined);

export const AccessibilityProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [settings, setSettings] = useState<AccessibilitySettings>(() => {
    const saved = localStorage.getItem('accessibility_settings');
    return saved ? JSON.parse(saved) : {
      highContrast: false,
      colorblindFriendly: false,
      keyboardNavigation: true,
      fontSize: 'medium',
      reducedMotion: false
    };
  });

  useEffect(() => {
    localStorage.setItem('accessibility_settings', JSON.stringify(settings));
    applySettings();
  }, [settings]);

  const updateSettings = (newSettings: Partial<AccessibilitySettings>) => {
    setSettings(prev => ({ ...prev, ...newSettings }));
  };

  const applySettings = () => {
    // Apply high contrast
    if (settings.highContrast) {
      document.body.classList.add('high-contrast');
    } else {
      document.body.classList.remove('high-contrast');
    }

    // Apply colorblind friendly
    if (settings.colorblindFriendly) {
      document.body.classList.add('colorblind-friendly');
    } else {
      document.body.classList.remove('colorblind-friendly');
    }

    // Apply font size
    document.body.classList.remove('text-small', 'text-medium', 'text-large', 'text-x-large');
    document.body.classList.add(`text-${settings.fontSize}`);

    // Apply reduced motion
    if (settings.reducedMotion) {
      document.body.classList.add('reduced-motion');
    } else {
      document.body.classList.remove('reduced-motion');
    }
  };

  return (
    <AccessibilityContext.Provider value={{ settings, updateSettings, applySettings }}>
      {children}
    </AccessibilityContext.Provider>
  );
};

export const useAccessibility = () => {
  const context = useContext(AccessibilityContext);
  if (context === undefined) {
    throw new Error('useAccessibility must be used within an AccessibilityProvider');
  }
  return context;
};

// CSS Classes for Accessibility
export const accessibilityStyles = `
  /* High Contrast Mode */
  .high-contrast {
    --background: #000000;
    --foreground: #ffffff;
    --primary: #ffff00;
    --primary-foreground: #000000;
    --secondary: #00ffff;
    --secondary-foreground: #000000;
    --muted: #404040;
    --muted-foreground: #ffffff;
    --border: #ffffff;
  }

  .high-contrast,
  .high-contrast * {
    background-color: var(--background) !important;
    color: var(--foreground) !important;
    border-color: var(--border) !important;
  }

  .high-contrast button,
  .high-contrast .btn {
    background-color: var(--primary) !important;
    color: var(--primary-foreground) !important;
    border: 2px solid var(--border) !important;
  }

  /* Colorblind Friendly */
  .colorblind-friendly {
    --blue-500: #0072b2;
    --green-500: #009e73;
    --red-500: #d55e00;
    --yellow-500: #f0e442;
    --purple-500: #cc79a7;
  }

  /* Font Sizes */
  .text-small {
    font-size: 0.875rem;
  }

  .text-medium {
    font-size: 1rem;
  }

  .text-large {
    font-size: 1.125rem;
  }

  .text-x-large {
    font-size: 1.25rem;
  }

  .text-small *,
  .text-medium *,
  .text-large *,
  .text-x-large * {
    font-size: inherit;
  }

  /* Reduced Motion */
  .reduced-motion * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  /* Focus Styles */
  .keyboard-navigation :focus {
    outline: 2px solid #0066cc !important;
    outline-offset: 2px !important;
  }

  /* Skip Links */
  .skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: #000000;
    color: #ffffff;
    padding: 8px;
    z-index: 1000;
    transition: top 0.3s;
  }

  .skip-link:focus {
    top: 6px;
  }

  /* Screen Reader Only */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
  }

  /* ARIA Live Regions */
  .aria-live {
    position: absolute;
    left: -10000px;
    top: auto;
    width: 1px;
    height: 1px;
    overflow: hidden;
  }
`;

// Accessibility Helper Functions
export const announceToScreenReader = (message: string, priority: 'polite' | 'assertive' = 'polite') => {
  const liveRegion = document.querySelector('.aria-live') || createLiveRegion();
  liveRegion.setAttribute('aria-live', priority);
  liveRegion.textContent = message;
  
  // Clear after announcement
  setTimeout(() => {
    liveRegion.textContent = '';
  }, 1000);
};

const createLiveRegion = () => {
  const liveRegion = document.createElement('div');
  liveRegion.className = 'aria-live';
  liveRegion.setAttribute('aria-live', 'polite');
  liveRegion.setAttribute('aria-atomic', 'true');
  document.body.appendChild(liveRegion);
  return liveRegion;
};

// Keyboard Navigation Helper
export const setupKeyboardNavigation = () => {
  document.body.classList.add('keyboard-navigation');
  
  // Remove class when mouse is used
  document.addEventListener('mousedown', () => {
    document.body.classList.remove('keyboard-navigation');
  });
  
  // Add class back when keyboard is used
  document.addEventListener('keydown', (e) => {
    if (e.key === 'Tab') {
      document.body.classList.add('keyboard-navigation');
    }
  });
};

// Initialize accessibility features
export const initializeAccessibility = () => {
  setupKeyboardNavigation();
  
  // Add styles to document
  if (!document.getElementById('accessibility-styles')) {
    const style = document.createElement('style');
    style.id = 'accessibility-styles';
    style.textContent = accessibilityStyles;
    document.head.appendChild(style);
  }
};