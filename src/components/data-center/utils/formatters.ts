import { ViewType, StageType, ProcessingStatus, FileType } from '../enums';

export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

export const formatRowCount = (count: number): string => {
  if (count >= 1000000) {
    return (count / 1000000).toFixed(1) + 'M';
  } else if (count >= 1000) {
    return (count / 1000).toFixed(1) + 'K';
  }
  return count.toString();
};

export const formatProcessingTime = (milliseconds: number): string => {
  if (milliseconds < 1000) {
    return `${milliseconds}ms`;
  } else if (milliseconds < 60000) {
    return `${(milliseconds / 1000).toFixed(1)}s`;
  } else {
    return `${(milliseconds / 60000).toFixed(1)}m`;
  }
};

export const formatStageLabel = (stage: StageType): string => {
  switch (stage) {
    case StageType.UPLOAD:
      return 'File Upload';
    case StageType.PREVIEW:
      return 'Data Preview';
    case StageType.EXPLORE:
      return 'Data Exploration';
    case StageType.PROCESSING:
      return 'Data Processing';
    case StageType.ANALYSIS:
      return 'Data Analysis';
    case StageType.SQL:
      return 'SQL Query';
    case StageType.STORAGE:
      return 'Data Storage';
    case StageType.STORYTELLING:
      return 'Data Storytelling';
    default:
      return stage;
  }
};

export const formatViewLabel = (view: ViewType): string => {
  switch (view) {
    case ViewType.UPLOAD:
      return 'Upload';
    case ViewType.DASHBOARD:
      return 'Dashboard';
    case ViewType.GRID:
      return 'Data Grid';
    case ViewType.SQL:
      return 'SQL Editor';
    case ViewType.TERMINAL:
      return 'Terminal';
    default:
      return view;
  }
};

export const formatProcessingStatus = (status: ProcessingStatus): string => {
  switch (status) {
    case ProcessingStatus.IDLE:
      return 'Ready';
    case ProcessingStatus.PROCESSING:
      return 'Processing...';
    case ProcessingStatus.SUCCESS:
      return 'Completed';
    case ProcessingStatus.ERROR:
      return 'Failed';
    default:
      return status;
  }
};

export const formatFileTypeLabel = (fileType: FileType): string => {
  switch (fileType) {
    case FileType.CSV:
      return 'CSV File';
    case FileType.TSV:
      return 'TSV File';
    case FileType.XLSX:
      return 'Excel File (XLSX)';
    case FileType.XLS:
      return 'Excel File (XLS)';
    case FileType.JSON:
      return 'JSON File';
    case FileType.TXT:
      return 'Text File';
    default:
      return fileType.toUpperCase();
  }
};

export const formatTimestamp = (date: Date): string => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date);
};