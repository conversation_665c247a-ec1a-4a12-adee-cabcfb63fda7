import { StorageFile } from '@/lib/services/browserStorageManager';

export interface FileTag {
  id: string;
  name: string;
  color: string;
  description?: string;
  createdAt: Date;
}

export interface FileTaggingSystem {
  tags: FileTag[];
  fileTags: Map<string, string[]>; // fileId -> tagIds
}

// Predefined tag colors following the data center color system
export const TAG_COLORS = {
  blue: '#007AFF',
  green: '#34C759',
  orange: '#FF9500',
  red: '#FF3B30',
  purple: '#5856D6',
  pink: '#FF2D92',
  gray: '#8E8E93',
  teal: '#5AC8FA',
  yellow: '#FFCC00',
  brown: '#A2845E'
};

// Common file tags
export const COMMON_TAGS: FileTag[] = [
  {
    id: 'important',
    name: 'Important',
    color: TAG_COLORS.red,
    description: 'High priority files',
    createdAt: new Date()
  },
  {
    id: 'processing',
    name: 'Processing',
    color: TAG_COLORS.blue,
    description: 'Files currently being processed',
    createdAt: new Date()
  },
  {
    id: 'completed',
    name: 'Completed',
    color: TAG_COLORS.green,
    description: 'Processing completed',
    createdAt: new Date()
  },
  {
    id: 'review',
    name: 'Review',
    color: TAG_COLORS.orange,
    description: 'Files needing review',
    createdAt: new Date()
  },
  {
    id: 'archived',
    name: 'Archived',
    color: TAG_COLORS.gray,
    description: 'Archived files',
    createdAt: new Date()
  },
  {
    id: 'draft',
    name: 'Draft',
    color: TAG_COLORS.purple,
    description: 'Draft versions',
    createdAt: new Date()
  },
  {
    id: 'favorite',
    name: 'Favorite',
    color: TAG_COLORS.pink,
    description: 'Favorite files',
    createdAt: new Date()
  },
  {
    id: 'large',
    name: 'Large File',
    color: TAG_COLORS.brown,
    description: 'Large files (>100MB)',
    createdAt: new Date()
  },
  {
    id: 'recent',
    name: 'Recent',
    color: TAG_COLORS.teal,
    description: 'Recently accessed files',
    createdAt: new Date()
  }
];

export class FileTaggingManager {
  private tags: FileTag[] = [...COMMON_TAGS];
  private fileTags: Map<string, string[]> = new Map();

  // Create a new tag
  createTag(name: string, color: string, description?: string): FileTag {
    const newTag: FileTag = {
      id: `tag_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name,
      color,
      description,
      createdAt: new Date()
    };
    
    this.tags.push(newTag);
    return newTag;
  }

  // Get all tags
  getTags(): FileTag[] {
    return [...this.tags];
  }

  // Get tag by ID
  getTagById(tagId: string): FileTag | undefined {
    return this.tags.find(tag => tag.id === tagId);
  }

  // Update tag
  updateTag(tagId: string, updates: Partial<Omit<FileTag, 'id' | 'createdAt'>>): boolean {
    const index = this.tags.findIndex(tag => tag.id === tagId);
    if (index === -1) return false;
    
    this.tags[index] = { ...this.tags[index], ...updates };
    return true;
  }

  // Delete tag
  deleteTag(tagId: string): boolean {
    const index = this.tags.findIndex(tag => tag.id === tagId);
    if (index === -1) return false;
    
    // Remove tag from all files
    this.fileTags.forEach((tagIds, fileId) => {
      const newTagIds = tagIds.filter(id => id !== tagId);
      if (newTagIds.length === 0) {
        this.fileTags.delete(fileId);
      } else {
        this.fileTags.set(fileId, newTagIds);
      }
    });
    
    this.tags.splice(index, 1);
    return true;
  }

  // Add tag to file
  addTagToFile(fileId: string, tagId: string): boolean {
    const tag = this.getTagById(tagId);
    if (!tag) return false;
    
    const currentTags = this.fileTags.get(fileId) || [];
    if (!currentTags.includes(tagId)) {
      this.fileTags.set(fileId, [...currentTags, tagId]);
      return true;
    }
    return false;
  }

  // Remove tag from file
  removeTagFromFile(fileId: string, tagId: string): boolean {
    const currentTags = this.fileTags.get(fileId);
    if (!currentTags) return false;
    
    const newTags = currentTags.filter(id => id !== tagId);
    if (newTags.length === 0) {
      this.fileTags.delete(fileId);
    } else {
      this.fileTags.set(fileId, newTags);
    }
    return true;
  }

  // Get tags for a file
  getTagsForFile(fileId: string): FileTag[] {
    const tagIds = this.fileTags.get(fileId) || [];
    return tagIds
      .map(id => this.getTagById(id))
      .filter((tag): tag is FileTag => tag !== undefined);
  }

  // Get files with specific tags
  getFilesWithTag(tagId: string): string[] {
    const fileIds: string[] = [];
    this.fileTags.forEach((tagIds, fileId) => {
      if (tagIds.includes(tagId)) {
        fileIds.push(fileId);
      }
    });
    return fileIds;
  }

  // Get files with any of the specified tags
  getFilesWithAnyTags(tagIds: string[]): string[] {
    const fileIds = new Set<string>();
    tagIds.forEach(tagId => {
      const files = this.getFilesWithTag(tagId);
      files.forEach(fileId => fileIds.add(fileId));
    });
    return Array.from(fileIds);
  }

  // Get files with all of the specified tags
  getFilesWithAllTags(tagIds: string[]): string[] {
    const fileIds: string[] = [];
    this.fileTags.forEach((fileTagIds, fileId) => {
      if (tagIds.every(tagId => fileTagIds.includes(tagId))) {
        fileIds.push(fileId);
      }
    });
    return fileIds;
  }

  // Clear all tags from a file
  clearTagsFromFile(fileId: string): boolean {
    return this.fileTags.delete(fileId);
  }

  // Get tag statistics
  getTagStatistics(): Array<{ tag: FileTag; count: number }> {
    const stats = new Map<string, number>();
    
    this.fileTags.forEach(tagIds => {
      tagIds.forEach(tagId => {
        stats.set(tagId, (stats.get(tagId) || 0) + 1);
      });
    });
    
    return Array.from(stats.entries()).map(([tagId, count]) => {
      const tag = this.getTagById(tagId);
      return tag ? { tag, count } : null;
    }).filter((stat): stat is { tag: FileTag; count: number } => stat !== null);
  }

  // Export tagging data
  exportTaggingData(): FileTaggingSystem {
    return {
      tags: [...this.tags],
      fileTags: new Map(this.fileTags)
    };
  }

  // Import tagging data
  importTaggingData(data: FileTaggingSystem): void {
    this.tags = [...data.tags];
    this.fileTags = new Map(data.fileTags);
  }

  // Auto-tag file based on properties
  autoTagFile(file: StorageFile): string[] {
    const tagsToAdd: string[] = [];
    
    // Tag large files
    if (file.size > 100 * 1024 * 1024) { // 100MB
      const largeTag = this.tags.find(t => t.id === 'large');
      if (largeTag) tagsToAdd.push(largeTag.id);
    }
    
    // Tag by file type
    const fileType = file.type.toLowerCase();
    if (fileType.includes('image') || fileType.includes('photo')) {
      const tag = this.tags.find(t => t.name.toLowerCase() === 'image');
      if (tag) tagsToAdd.push(tag.id);
    } else if (fileType.includes('pdf') || fileType.includes('document')) {
      const tag = this.tags.find(t => t.name.toLowerCase() === 'document');
      if (tag) tagsToAdd.push(tag.id);
    } else if (fileType.includes('spreadsheet') || file.type.includes('excel')) {
      const tag = this.tags.find(t => t.name.toLowerCase() === 'data');
      if (tag) tagsToAdd.push(tag.id);
    }
    
    // Add tags to file
    tagsToAdd.forEach(tagId => {
      this.addTagToFile(file.id, tagId);
    });
    
    return tagsToAdd;
  }

  // Get recent tags (sorted by usage)
  getRecentTags(limit: number = 10): FileTag[] {
    const tagStats = this.getTagStatistics();
    return tagStats
      .sort((a, b) => b.count - a.count)
      .slice(0, limit)
      .map(stat => stat.tag);
  }

  // Search tags by name
  searchTags(query: string): FileTag[] {
    const lowerQuery = query.toLowerCase();
    return this.tags.filter(tag => 
      tag.name.toLowerCase().includes(lowerQuery) ||
      (tag.description && tag.description.toLowerCase().includes(lowerQuery))
    );
  }
}

// Create a global instance
export const fileTaggingManager = new FileTaggingManager();

// React hook for using the tagging system
export const useFileTagging = () => {
  const [tags, setTags] = React.useState<FileTag[]>(fileTaggingManager.getTags());
  
  const refreshTags = React.useCallback(() => {
    setTags(fileTaggingManager.getTags());
  }, []);
  
  React.useEffect(() => {
    refreshTags();
  }, [refreshTags]);
  
  return {
    tags,
    taggingManager: fileTaggingManager,
    refreshTags
  };
};

// Tag component for UI
export const FileTag: React.FC<{
  tag: FileTag;
  onRemove?: () => void;
  onClick?: () => void;
  removable?: boolean;
}> = ({ tag, onRemove, onClick, removable = false }) => {
  return (
    <span
      className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium cursor-pointer transition-colors"
      style={{
        backgroundColor: `${tag.color}20`,
        color: tag.color,
        borderColor: `${tag.color}40`,
        borderWidth: '1px',
        borderStyle: 'solid'
      }}
      onClick={onClick}
    >
      {tag.name}
      {removable && onRemove && (
        <button
          onClick={(e) => {
            e.stopPropagation();
            onRemove();
          }}
          className="ml-1 hover:opacity-70 transition-opacity"
          style={{ color: tag.color }}
        >
          ×
        </button>
      )}
    </span>
  );
};