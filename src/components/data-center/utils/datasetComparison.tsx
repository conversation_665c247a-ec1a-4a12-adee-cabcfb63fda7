import React, { useState, useMemo } from 'react';
import { 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON>rid, 
  Table,
  Git<PERSON>ompare,
  ArrowUpDown,
  Filter,
  Download,
  Eye,
  EyeOff,
  Settings,
  Check,
  X,
  AlertCircle
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

// Comparison Types
export interface Dataset {
  id: string;
  name: string;
  data: any[];
  schema: Array<{
    key: string;
    type: string;
    label: string;
  }>;
  metadata: {
    rowCount: number;
    columnCount: number;
    createdAt: Date;
    size: number;
    source?: string;
  };
}

export interface ComparisonResult {
  datasets: Dataset[];
  commonColumns: string[];
  differences: Array<{
    column: string;
    values: Array<{
      datasetId: string;
      value: any;
      type: string;
    }>;
    difference: boolean;
  }>;
  statistics: Record<string, any>;
}

// Column Comparison Result
export interface ColumnComparison {
  key: string;
  label: string;
  type: string;
  values: Array<{
    datasetId: string;
    value: any;
    formattedValue: string;
  }>;
  differences: boolean;
  stats: {
    min: any;
    max: any;
    avg?: number;
    uniqueCount?: number;
  };
}

// View Modes
type ComparisonViewMode = 'side-by-side' | 'overlay' | 'difference' | 'statistics';

// Comparison View Component
export const DatasetComparisonView: React.FC<{
  datasets: Dataset[];
  onClose?: () => void;
  onExport?: (format: 'csv' | 'json' | 'excel') => void;
}> = ({ datasets, onClose, onExport }) => {
  const [viewMode, setViewMode] = useState<ComparisonViewMode>('side-by-side');
  const [selectedColumns, setSelectedColumns] = useState<string[]>([]);
  const [sortConfig, setSortConfig] = useState<{ key: string; direction: 'asc' | 'desc' } | null>(null);
  const [filterConfig, setFilterConfig] = useState<Record<string, string>>({});
  const [showColumnSelector, setShowColumnSelector] = useState(false);

  // Calculate comparison results
  const comparisonResult = useMemo<ComparisonResult>(() => {
    if (datasets.length === 0) {
      return { datasets: [], commonColumns: [], differences: [], statistics: {} };
    }

    // Find common columns
    const allColumns = datasets.flatMap(ds => ds.schema.map(s => s.key));
    const commonColumns = [...new Set(allColumns)].filter(column =>
      datasets.every(ds => ds.schema.some(s => s.key === column))
    );

    // Calculate differences
    const differences: ComparisonResult['differences'] = [];
    
    commonColumns.forEach(column => {
      const columnValues = datasets.map(ds => {
        const sampleRow = ds.data[0];
        return {
          datasetId: ds.id,
          value: sampleRow ? sampleRow[column] : null,
          type: ds.schema.find(s => s.key === column)?.type || 'unknown'
        };
      });

      const hasDifference = columnValues.some((val, i, arr) => 
        i > 0 && JSON.stringify(val.value) !== JSON.stringify(arr[0].value)
      );

      differences.push({
        column,
        values: columnValues,
        difference: hasDifference
      });
    });

    return {
      datasets,
      commonColumns,
      differences,
      statistics: {} // Will be calculated based on data
    };
  }, [datasets]);

  // Process column comparisons
  const columnComparisons = useMemo<ColumnComparison[]>(() => {
    return comparisonResult.commonColumns.map(columnKey => {
      const columnSchema = datasets[0]?.schema.find(s => s.key === columnKey);
      
      const values = datasets.map(ds => {
        const sampleValue = ds.data[0]?.[columnKey];
        return {
          datasetId: ds.id,
          value: sampleValue,
          formattedValue: formatValue(sampleValue, columnSchema?.type)
        };
      });

      const hasDifferences = values.some((val, i, arr) => 
        i > 0 && val.formattedValue !== arr[0].formattedValue
      );

      // Calculate basic statistics
      const numericValues = values
        .map(v => parseFloat(v.value))
        .filter(v => !isNaN(v));

      const stats = {
        min: Math.min(...numericValues),
        max: Math.max(...numericValues),
        avg: numericValues.length > 0 ? numericValues.reduce((a, b) => a + b, 0) / numericValues.length : undefined,
        uniqueCount: new Set(values.map(v => v.formattedValue)).size
      };

      return {
        key: columnKey,
        label: columnSchema?.label || columnKey,
        type: columnSchema?.type || 'unknown',
        values,
        differences: hasDifferences,
        stats
      };
    });
  }, [comparisonResult, datasets]);

  // Filter and sort columns
  const filteredColumns = useMemo(() => {
    let filtered = columnComparisons;

    // Apply search filter
    const searchQuery = filterConfig.search?.toLowerCase();
    if (searchQuery) {
      filtered = filtered.filter(col => 
        col.label.toLowerCase().includes(searchQuery) ||
        col.key.toLowerCase().includes(searchQuery)
      );
    }

    // Apply difference filter
    if (filterConfig.differences === 'true') {
      filtered = filtered.filter(col => col.differences);
    }

    // Apply sorting
    if (sortConfig) {
      filtered = [...filtered].sort((a, b) => {
        let aValue, bValue;
        
        switch (sortConfig.key) {
          case 'name':
            aValue = a.label;
            bValue = b.label;
            break;
          case 'type':
            aValue = a.type;
            bValue = b.type;
            break;
          case 'differences':
            aValue = a.differences ? 1 : 0;
            bValue = b.differences ? 1 : 0;
            break;
          default:
            aValue = a.stats.avg || a.stats.min;
            bValue = b.stats.avg || b.stats.min;
        }

        if (aValue < bValue) {
          return sortConfig.direction === 'asc' ? -1 : 1;
        }
        if (aValue > bValue) {
          return sortConfig.direction === 'asc' ? 1 : -1;
        }
        return 0;
      });
    }

    return filtered;
  }, [columnComparisons, filterConfig, sortConfig]);

  // Handle sorting
  const handleSort = (key: string) => {
    setSortConfig(prev => ({
      key,
      direction: prev?.key === key && prev.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  // Toggle column selection
  const toggleColumnSelection = (columnKey: string) => {
    setSelectedColumns(prev => 
      prev.includes(columnKey)
        ? prev.filter(key => key !== columnKey)
        : [...prev, columnKey]
    );
  };

  // Select all columns
  const selectAllColumns = () => {
    setSelectedColumns(comparisonResult.commonColumns);
  };

  // Clear column selection
  const clearColumnSelection = () => {
    setSelectedColumns([]);
  };

  return (
    <div className="dataset-comparison-view flex flex-col h-full">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-3">
          <GitCompare className="w-5 h-5 text-primary" />
          <h2 className="text-lg font-semibold">Dataset Comparison</h2>
          <span className="text-sm text-muted-foreground">
            {datasets.length} datasets, {comparisonResult.commonColumns.length} common columns
          </span>
        </div>
        
        <div className="flex items-center gap-2">
          <div className="flex gap-1 p-1 bg-muted rounded-lg">
            {(['side-by-side', 'overlay', 'difference', 'statistics'] as ComparisonViewMode[]).map(mode => (
              <button
                key={mode}
                onClick={() => setViewMode(mode)}
                className={`px-3 py-1 text-xs rounded ${
                  viewMode === mode 
                    ? 'bg-primary text-primary-foreground' 
                    : 'hover:bg-muted/70'
                }`}
              >
                {mode.charAt(0).toUpperCase() + mode.slice(1)}
              </button>
            ))}
          </div>
          
          <button
            onClick={() => setShowColumnSelector(!showColumnSelector)}
            className="p-2 hover:bg-muted rounded"
            title="Column selector"
          >
            <Settings className="w-4 h-4" />
          </button>
          
          {onExport && (
            <div className="flex gap-1">
              <button
                onClick={() => onExport?.('csv')}
                className="p-2 hover:bg-muted rounded"
                title="Export CSV"
              >
                <Download className="w-4 h-4" />
              </button>
            </div>
          )}
          
          {onClose && (
            <button
              onClick={onClose}
              className="p-2 hover:bg-muted rounded"
              title="Close"
            >
              <X className="w-4 h-4" />
            </button>
          )}
        </div>
      </div>

      {/* Column Selector */}
      <AnimatePresence>
        {showColumnSelector && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            className="border-b overflow-hidden"
          >
            <div className="p-4">
              <div className="flex items-center justify-between mb-3">
                <h3 className="font-medium">Select Columns to Compare</h3>
                <div className="flex gap-2">
                  <button
                    onClick={selectAllColumns}
                    className="text-xs text-primary hover:underline"
                  >
                    Select All
                  </button>
                  <button
                    onClick={clearColumnSelection}
                    className="text-xs text-muted-foreground hover:underline"
                  >
                    Clear
                  </button>
                </div>
              </div>
              
              <div className="flex flex-wrap gap-2">
                {comparisonResult.commonColumns.map(columnKey => {
                  const column = columnComparisons.find(c => c.key === columnKey);
                  return (
                    <button
                      key={columnKey}
                      onClick={() => toggleColumnSelection(columnKey)}
                      className={`flex items-center gap-2 px-3 py-1 rounded-full text-sm ${
                        selectedColumns.includes(columnKey)
                          ? 'bg-primary text-primary-foreground'
                          : 'bg-muted hover:bg-muted/70'
                      }`}
                    >
                      {selectedColumns.includes(columnKey) ? (
                        <Check className="w-3 h-3" />
                      ) : (
                        <div className="w-3 h-3 border rounded-full" />
                      )}
                      {column?.label || columnKey}
                      {column?.differences && (
                        <AlertCircle className="w-3 h-3 text-yellow-500" />
                      )}
                    </button>
                  );
                })}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main Content */}
      <div className="flex-1 overflow-auto">
        <AnimatePresence mode="wait">
          {viewMode === 'side-by-side' && (
            <motion.div
              key="side-by-side"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="p-4"
            >
              <SideBySideComparison 
                datasets={datasets}
                columns={filteredColumns}
                selectedColumns={selectedColumns}
                onSort={handleSort}
                sortConfig={sortConfig}
              />
            </motion.div>
          )}

          {viewMode === 'difference' && (
            <motion.div
              key="difference"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="p-4"
            >
              <DifferenceView 
                datasets={datasets}
                columns={filteredColumns.filter(col => col.differences)}
              />
            </motion.div>
          )}

          {viewMode === 'statistics' && (
            <motion.div
              key="statistics"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="p-4"
            >
              <StatisticsView 
                datasets={datasets}
                columns={filteredColumns}
              />
            </motion.div>
          )}

          {viewMode === 'overlay' && (
            <motion.div
              key="overlay"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="p-4"
            >
              <OverlayView 
                datasets={datasets}
                columns={filteredColumns}
              />
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

// Side-by-Side Comparison Component
const SideBySideComparison: React.FC<{
  datasets: Dataset[];
  columns: ColumnComparison[];
  selectedColumns: string[];
  onSort: (key: string) => void;
  sortConfig: { key: string; direction: 'asc' | 'desc' } | null;
}> = ({ datasets, columns, selectedColumns, onSort, sortConfig }) => {
  const showAllColumns = selectedColumns.length === 0;
  const visibleColumns = showAllColumns 
    ? columns 
    : columns.filter(col => selectedColumns.includes(col.key));

  return (
    <div className="space-y-6">
      {/* Dataset Metadata */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {datasets.map(dataset => (
          <div key={dataset.id} className="p-4 bg-muted/30 rounded-lg">
            <h3 className="font-medium mb-2">{dataset.name}</h3>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Rows:</span>
                <span>{dataset.metadata.rowCount.toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Columns:</span>
                <span>{dataset.metadata.columnCount}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Size:</span>
                <span>{formatFileSize(dataset.metadata.size)}</span>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Column Comparison Table */}
      <div className="border rounded-lg overflow-hidden">
        <table className="w-full">
          <thead className="bg-muted">
            <tr>
              <th className="px-4 py-3 text-left text-sm font-medium">
                <button 
                  onClick={() => onSort('name')}
                  className="flex items-center gap-2 hover:text-foreground"
                >
                  Column
                  {sortConfig?.key === 'name' && (
                    <ArrowUpDown className={`w-3 h-3 ${sortConfig.direction === 'desc' ? 'rotate-180' : ''}`} />
                  )}
                </button>
              </th>
              {datasets.map(dataset => (
                <th key={dataset.id} className="px-4 py-3 text-left text-sm font-medium">
                  {dataset.name}
                </th>
              ))}
              <th className="px-4 py-3 text-left text-sm font-medium">
                <button 
                  onClick={() => onSort('differences')}
                  className="flex items-center gap-2 hover:text-foreground"
                >
                  Differences
                  {sortConfig?.key === 'differences' && (
                    <ArrowUpDown className={`w-3 h-3 ${sortConfig.direction === 'desc' ? 'rotate-180' : ''}`} />
                  )}
                </button>
              </th>
            </tr>
          </thead>
          <tbody>
            {visibleColumns.map((column, index) => (
              <motion.tr
                key={column.key}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: index * 0.05 }}
                className="border-b hover:bg-muted/30"
              >
                <td className="px-4 py-3">
                  <div className="flex items-center gap-2">
                    <span className="font-medium">{column.label}</span>
                    <span className="text-xs text-muted-foreground bg-muted px-1.5 py-0.5 rounded">
                      {column.type}
                    </span>
                  </div>
                </td>
                {column.values.map((value, valueIndex) => (
                  <td key={`${column.key}-${valueIndex}`} className="px-4 py-3">
                    <div className="font-mono text-sm">
                      {value.formattedValue}
                    </div>
                  </td>
                ))}
                <td className="px-4 py-3">
                  {column.differences ? (
                    <div className="flex items-center gap-2 text-orange-600">
                      <AlertCircle className="w-4 h-4" />
                      <span className="text-sm">Different</span>
                    </div>
                  ) : (
                    <div className="flex items-center gap-2 text-green-600">
                      <Check className="w-4 h-4" />
                      <span className="text-sm">Same</span>
                    </div>
                  )}
                </td>
              </motion.tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

// Difference View Component
const DifferenceView: React.FC<{
  datasets: Dataset[];
  columns: ColumnComparison[];
}> = ({ datasets, columns }) => {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Differences Only</h3>
        <span className="text-sm text-muted-foreground">
          {columns.length} columns with differences
        </span>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {columns.map(column => (
          <div key={column.key} className="p-4 border rounded-lg">
            <div className="flex items-center justify-between mb-4">
              <h4 className="font-medium">{column.label}</h4>
              <span className="text-xs text-muted-foreground bg-muted px-2 py-1 rounded">
                {column.type}
              </span>
            </div>
            
            <div className="space-y-3">
              {column.values.map((value, index) => {
                const dataset = datasets.find(ds => ds.id === value.datasetId);
                return (
                  <div key={index} className="flex items-center gap-3 p-3 bg-muted/30 rounded">
                    <div className="flex-1">
                      <div className="font-medium text-sm">{dataset?.name}</div>
                      <div className="font-mono text-sm mt-1">{value.formattedValue}</div>
                    </div>
                    {index > 0 && column.differences && (
                      <AlertCircle className="w-4 h-4 text-orange-500" />
                    )}
                  </div>
                );
              })}
            </div>
            
            {/* Statistics */}
            <div className="mt-4 pt-3 border-t">
              <div className="grid grid-cols-3 gap-2 text-xs">
                <div className="text-center">
                  <div className="text-muted-foreground">Min</div>
                  <div className="font-medium">{column.stats.min}</div>
                </div>
                <div className="text-center">
                  <div className="text-muted-foreground">Max</div>
                  <div className="font-medium">{column.stats.max}</div>
                </div>
                {column.stats.avg !== undefined && (
                  <div className="text-center">
                    <div className="text-muted-foreground">Avg</div>
                    <div className="font-medium">{column.stats.avg.toFixed(2)}</div>
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Statistics View Component
const StatisticsView: React.FC<{
  datasets: Dataset[];
  columns: ColumnComparison[];
}> = ({ datasets, columns }) => {
  return (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold">Statistical Comparison</h3>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Summary Statistics */}
        <div className="lg:col-span-1 p-4 bg-muted/30 rounded-lg">
          <h4 className="font-medium mb-4">Overview</h4>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Total Datasets</span>
              <span className="font-medium">{datasets.length}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Common Columns</span>
              <span className="font-medium">{columns.length}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Columns with Differences</span>
              <span className="font-medium text-orange-600">
                {columns.filter(c => c.differences).length}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Match Rate</span>
              <span className="font-medium">
                {columns.length > 0 
                  ? `${Math.round(((columns.length - columns.filter(c => c.differences).length) / columns.length) * 100)}%`
                  : '0%'
                }
              </span>
            </div>
          </div>
        </div>

        {/* Column Statistics */}
        <div className="lg:col-span-2">
          <div className="border rounded-lg overflow-hidden">
            <table className="w-full">
              <thead className="bg-muted">
                <tr>
                  <th className="px-4 py-3 text-left text-sm font-medium">Column</th>
                  <th className="px-4 py-3 text-left text-sm font-medium">Type</th>
                  <th className="px-4 py-3 text-left text-sm font-medium">Min</th>
                  <th className="px-4 py-3 text-left text-sm font-medium">Max</th>
                  <th className="px-4 py-3 text-left text-sm font-medium">Avg</th>
                  <th className="px-4 py-3 text-left text-sm font-medium">Unique Values</th>
                </tr>
              </thead>
              <tbody>
                {columns.map((column, index) => (
                  <tr key={column.key} className="border-b hover:bg-muted/30">
                    <td className="px-4 py-3">
                      <div className="flex items-center gap-2">
                        <span>{column.label}</span>
                        {column.differences && (
                          <AlertCircle className="w-3 h-3 text-orange-500" />
                        )}
                      </div>
                    </td>
                    <td className="px-4 py-3 text-sm text-muted-foreground">
                      {column.type}
                    </td>
                    <td className="px-4 py-3 font-mono text-sm">
                      {String(column.stats.min)}
                    </td>
                    <td className="px-4 py-3 font-mono text-sm">
                      {String(column.stats.max)}
                    </td>
                    <td className="px-4 py-3 font-mono text-sm">
                      {column.stats.avg?.toFixed(2) || '-'}
                    </td>
                    <td className="px-4 py-3 text-sm">
                      {column.stats.uniqueCount}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

// Overlay View Component
const OverlayView: React.FC<{
  datasets: Dataset[];
  columns: ColumnComparison[];
}> = ({ datasets, columns }) => {
  const [selectedDataset, setSelectedDataset] = useState(datasets[0]?.id || '');
  
  const currentDataset = datasets.find(ds => ds.id === selectedDataset);
  
  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <h3 className="text-lg font-semibold">Overlay Comparison</h3>
        <select
          value={selectedDataset}
          onChange={(e) => setSelectedDataset(e.target.value)}
          className="px-3 py-1 border rounded text-sm"
        >
          {datasets.map(dataset => (
            <option key={dataset.id} value={dataset.id}>
              {dataset.name}
            </option>
          ))}
        </select>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {columns.map(column => {
          const currentValue = column.values.find(v => v.datasetId === selectedDataset);
          const otherValues = column.values.filter(v => v.datasetId !== selectedDataset);
          
          return (
            <div key={column.key} className="p-4 border rounded-lg">
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium">{column.label}</h4>
                <span className="text-xs text-muted-foreground">{column.type}</span>
              </div>
              
              <div className="space-y-2">
                <div className="p-3 bg-primary/10 rounded border-l-4 border-primary">
                  <div className="text-sm text-muted-foreground">Current Dataset</div>
                  <div className="font-mono text-lg font-medium mt-1">
                    {currentValue?.formattedValue || '-'}
                  </div>
                </div>
                
                {otherValues.map((value, index) => {
                  const dataset = datasets.find(ds => ds.id === value.datasetId);
                  const isDifferent = currentValue?.formattedValue !== value.formattedValue;
                  
                  return (
                    <div 
                      key={index}
                      className={`p-3 rounded border-l-4 ${
                        isDifferent 
                          ? 'bg-orange-50 border-orange-500' 
                          : 'bg-green-50 border-green-500'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="text-sm text-muted-foreground">{dataset?.name}</div>
                        {isDifferent && (
                          <AlertCircle className="w-4 h-4 text-orange-500" />
                        )}
                      </div>
                      <div className={`font-mono text-lg font-medium mt-1 ${
                        isDifferent ? 'text-orange-700' : 'text-green-700'
                      }`}>
                        {value.formattedValue}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

// Utility Functions
const formatValue = (value: any, type?: string): string => {
  if (value === null || value === undefined) return 'NULL';
  
  switch (type) {
    case 'number':
      return typeof value === 'number' ? value.toLocaleString() : String(value);
    case 'date':
      return new Date(value).toLocaleDateString();
    case 'boolean':
      return value ? 'True' : 'False';
    default:
      return String(value);
  }
};

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// Hook for Dataset Comparison
export const useDatasetComparison = () => {
  const [datasets, setDatasets] = useState<Dataset[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadDatasets = async (datasetIds: string[]) => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Simulate loading datasets
      const loadedDatasets: Dataset[] = datasetIds.map(id => ({
        id,
        name: `Dataset ${id}`,
        data: Array.from({ length: 10 }, (_, i) => ({
          id: i + 1,
          name: `Record ${i + 1}`,
          value: Math.random() * 1000,
          status: ['active', 'pending', 'completed'][Math.floor(Math.random() * 3)]
        })),
        schema: [
          { key: 'id', type: 'number', label: 'ID' },
          { key: 'name', type: 'string', label: 'Name' },
          { key: 'value', type: 'number', label: 'Value' },
          { key: 'status', type: 'string', label: 'Status' }
        ],
        metadata: {
          rowCount: 1000,
          columnCount: 4,
          createdAt: new Date(),
          size: Math.floor(Math.random() * 1000000),
          source: 'Uploaded File'
        }
      }));
      
      setDatasets(loadedDatasets);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load datasets');
    } finally {
      setIsLoading(false);
    }
  };

  const exportComparison = async (format: 'csv' | 'json' | 'excel') => {
    // Implementation would go here
  };

  return {
    datasets,
    isLoading,
    error,
    loadDatasets,
    exportComparison
  };
};

// Comparison Panel Component
export const ComparisonPanel: React.FC<{
  datasetIds: string[];
  onClose?: () => void;
}> = ({ datasetIds, onClose }) => {
  const { datasets, isLoading, error, loadDatasets } = useDatasetComparison();
  
  React.useEffect(() => {
    if (datasetIds.length > 0) {
      loadDatasets(datasetIds);
    }
  }, [datasetIds, loadDatasets]);
  
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-3"></div>
          <p className="text-muted-foreground">Loading datasets for comparison...</p>
        </div>
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center text-red-600">
          <AlertCircle className="w-12 h-12 mx-auto mb-3" />
          <p className="text-lg font-medium mb-2">Error Loading Data</p>
          <p className="text-sm">{error}</p>
          <button
            onClick={() => loadDatasets(datasetIds)}
            className="mt-4 px-4 py-2 bg-primary text-primary-foreground rounded text-sm hover:bg-primary/90"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }
  
  if (datasets.length === 0) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center text-muted-foreground">
          <GitCompare className="w-12 h-12 mx-auto mb-3" />
          <p className="text-lg font-medium mb-2">No Datasets to Compare</p>
          <p className="text-sm">Select datasets to compare their contents</p>
        </div>
      </div>
    );
  }
  
  return (
    <DatasetComparisonView
      datasets={datasets}
      onClose={onClose}
      onExport={(format) => {}}
    />
  );
};