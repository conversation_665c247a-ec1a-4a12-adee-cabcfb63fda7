import React from 'react';
import { renderHook, act } from '@testing-library/react';
import { DataCenterProvider, useDataCenter } from '../DataCenterContext';

describe('Unified Data Flow', () => {
  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <DataCenterProvider>
      {children}
    </DataCenterProvider>
  );

  it('should maintain persistent context between stages', () => {
    const { result } = renderHook(() => useDataCenter(), { wrapper });

    // Add breadcrumb entries
    act(() => {
      result.current.addToBreadcrumb('upload', 'test.csv', { rows: 100, columns: 5 });
      result.current.addToBreadcrumb('preview', 'Data Preview', { rows: 100, columns: 5 });
    });

    expect(result.current.pipelineState.context.breadcrumbPath).toHaveLength(2);
    expect(result.current.pipelineState.context.breadcrumbPath[0].label).toBe('test.csv');
    expect(result.current.pipelineState.context.breadcrumbPath[1].stage).toBe('preview');
  });

  it('should update context data', () => {
    const { result } = renderHook(() => useDataCenter(), { wrapper });

    act(() => {
      result.current.updateContext({
        activeFilters: { name: 'test' },
        processingMetadata: { template: 'ner' }
      });
    });

    expect(result.current.pipelineState.context.activeFilters.name).toBe('test');
    expect(result.current.pipelineState.context.processingMetadata.template).toBe('ner');
  });

  it('should navigate between stages with breadcrumb tracking', () => {
    const { result } = renderHook(() => useDataCenter(), { wrapper });

    act(() => {
      result.current.navigateToStage('preview');
    });

    expect(result.current.pipelineState.currentStage).toBe('preview');
    // Check that breadcrumb was added
    expect(result.current.pipelineState.context.breadcrumbPath).toHaveLength(1);
  });
});