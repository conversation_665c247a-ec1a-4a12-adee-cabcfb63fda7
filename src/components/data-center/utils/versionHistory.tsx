import React from 'react';
import { StorageFile } from '@/lib/services/browserStorageManager';

export interface FileVersion {
  id: string;
  fileId: string;
  versionNumber: number;
  content: string; // Base64 encoded content
  size: number;
  createdAt: Date;
  createdBy: string;
  description?: string;
  tags: string[];
  metadata: Record<string, any>;
}

export interface VersionHistory {
  fileId: string;
  versions: FileVersion[];
  currentVersion: number;
  createdAt: Date;
  lastModified: Date;
}

export class VersionHistoryManager {
  private versionHistories: Map<string, VersionHistory> = new Map();
  private maxVersionsPerFile: number = 50; // Limit versions per file

  // Create initial version history for a file
  createVersionHistory(
    file: StorageFile,
    userId: string = 'system'
  ): VersionHistory {
    const version: FileVersion = {
      id: `ver_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      fileId: file.id,
      versionNumber: 1,
      content: file.content,
      size: file.size,
      createdAt: new Date(),
      createdBy: userId,
      tags: [...file.tags],
      metadata: { ...file.metadata }
    };

    const history: VersionHistory = {
      fileId: file.id,
      versions: [version],
      currentVersion: 1,
      createdAt: new Date(),
      lastModified: new Date()
    };

    this.versionHistories.set(file.id, history);
    return history;
  }

  // Add a new version
  addVersion(
    fileId: string,
    content: string,
    userId: string = 'system',
    description?: string,
    metadata?: Record<string, any>
  ): FileVersion | null {
    const history = this.versionHistories.get(fileId);
    if (!history) return null;

    const newVersionNumber = history.currentVersion + 1;
    const version: FileVersion = {
      id: `ver_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      fileId,
      versionNumber: newVersionNumber,
      content,
      size: content.length, // Approximate size
      createdAt: new Date(),
      createdBy: userId,
      description,
      tags: [], // Will be set when file is updated
      metadata: metadata || {}
    };

    // Add new version
    history.versions.push(version);
    history.currentVersion = newVersionNumber;
    history.lastModified = new Date();

    // Limit versions if necessary
    if (history.versions.length > this.maxVersionsPerFile) {
      // Keep the first version and the most recent ones
      const firstVersion = history.versions[0];
      const recentVersions = history.versions.slice(-this.maxVersionsPerFile + 1);
      history.versions = [firstVersion, ...recentVersions];
    }

    this.versionHistories.set(fileId, history);
    return version;
  }

  // Get version history for a file
  getVersionHistory(fileId: string): VersionHistory | null {
    return this.versionHistories.get(fileId) || null;
  }

  // Get specific version
  getVersion(fileId: string, versionNumber: number): FileVersion | null {
    const history = this.versionHistories.get(fileId);
    if (!history) return null;

    return history.versions.find(v => v.versionNumber === versionNumber) || null;
  }

  // Get current version
  getCurrentVersion(fileId: string): FileVersion | null {
    const history = this.versionHistories.get(fileId);
    if (!history) return null;

    return history.versions.find(v => v.versionNumber === history.currentVersion) || null;
  }

  // Get all versions for a file
  getAllVersions(fileId: string): FileVersion[] {
    const history = this.versionHistories.get(fileId);
    return history ? [...history.versions].sort((a, b) => b.versionNumber - a.versionNumber) : [];
  }

  // Revert to a specific version
  revertToVersion(fileId: string, versionNumber: number): FileVersion | null {
    const version = this.getVersion(fileId, versionNumber);
    if (!version) return null;

    const history = this.versionHistories.get(fileId);
    if (!history) return null;

    // Create a new version with the reverted content
    const revertedVersion = this.addVersion(
      fileId,
      version.content,
      'system',
      `Reverted to version ${versionNumber}`,
      { ...version.metadata, revertedFrom: versionNumber }
    );

    return revertedVersion;
  }

  // Compare two versions
  compareVersions(fileId: string, versionA: number, versionB: number): {
    added: string[];
    removed: string[];
    modified: string[];
  } | null {
    const version1 = this.getVersion(fileId, versionA);
    const version2 = this.getVersion(fileId, versionB);
    
    if (!version1 || !version2) return null;

    // Simple line-by-line comparison (for text content)
    const lines1 = atob(version1.content).split('\n');
    const lines2 = atob(version2.content).split('\n');

    const added: string[] = [];
    const removed: string[] = [];
    const modified: string[] = [];

    // Find added and removed lines
    const set1 = new Set(lines1);
    const set2 = new Set(lines2);

    lines2.forEach(line => {
      if (!set1.has(line)) {
        added.push(line);
      }
    });

    lines1.forEach(line => {
      if (!set2.has(line)) {
        removed.push(line);
      }
    });

    return { added, removed, modified };
  }

  // Get version statistics
  getVersionStatistics(fileId: string): {
    totalVersions: number;
    firstVersionDate: Date | null;
    lastVersionDate: Date | null;
    averageChangesPerVersion: number;
  } {
    const history = this.versionHistories.get(fileId);
    if (!history) {
      return {
        totalVersions: 0,
        firstVersionDate: null,
        lastVersionDate: null,
        averageChangesPerVersion: 0
      };
    }

    const versions = history.versions;
    const totalVersions = versions.length;
    const firstVersionDate = versions[0]?.createdAt || null;
    const lastVersionDate = versions[versions.length - 1]?.createdAt || null;

    // Calculate average changes (simplified)
    let totalSize = 0;
    versions.forEach(version => totalSize += version.size);
    const averageChangesPerVersion = totalVersions > 0 ? totalSize / totalVersions : 0;

    return {
      totalVersions,
      firstVersionDate,
      lastVersionDate,
      averageChangesPerVersion
    };
  }

  // Delete version history for a file
  deleteVersionHistory(fileId: string): boolean {
    return this.versionHistories.delete(fileId);
  }

  // Get all file histories
  getAllHistories(): VersionHistory[] {
    return Array.from(this.versionHistories.values());
  }

  // Export version histories
  exportVersionHistories(): Record<string, VersionHistory> {
    const histories: Record<string, VersionHistory> = {};
    this.versionHistories.forEach((history, fileId) => {
      histories[fileId] = { ...history };
    });
    return histories;
  }

  // Import version histories
  importVersionHistories(histories: Record<string, VersionHistory>): void {
    Object.entries(histories).forEach(([fileId, history]) => {
      this.versionHistories.set(fileId, {
        ...history,
        createdAt: new Date(history.createdAt),
        lastModified: new Date(history.lastModified),
        versions: history.versions.map(version => ({
          ...version,
          createdAt: new Date(version.createdAt)
        }))
      });
    });
  }

  // Get files with recent changes
  getFilesWithRecentChanges(hours: number = 24): string[] {
    const cutoffDate = new Date(Date.now() - hours * 60 * 60 * 1000);
    const fileIds: string[] = [];

    this.versionHistories.forEach((history, fileId) => {
      if (history.lastModified >= cutoffDate) {
        fileIds.push(fileId);
      }
    });

    return fileIds;
  }

  // Get file change frequency
  getFileChangeFrequency(fileId: string): {
    changesPerDay: number;
    totalDays: number;
    firstChange: Date | null;
    lastChange: Date | null;
  } {
    const history = this.versionHistories.get(fileId);
    if (!history || history.versions.length === 0) {
      return {
        changesPerDay: 0,
        totalDays: 0,
        firstChange: null,
        lastChange: null
      };
    }

    const firstChange = history.versions[0].createdAt;
    const lastChange = history.versions[history.versions.length - 1].createdAt;
    const totalDays = Math.max(1, (lastChange.getTime() - firstChange.getTime()) / (1000 * 60 * 60 * 24));
    const changesPerDay = history.versions.length / totalDays;

    return {
      changesPerDay,
      totalDays: Math.ceil(totalDays),
      firstChange,
      lastChange
    };
  }

  // Auto-version file on significant changes
  autoVersionFile(
    file: StorageFile,
    userId: string = 'system',
    changeThreshold: number = 0.1 // 10% content change
  ): FileVersion | null {
    const history = this.versionHistories.get(file.id);
    if (!history) {
      // Create initial version history
      this.createVersionHistory(file, userId);
      return null;
    }

    const currentVersion = this.getCurrentVersion(file.id);
    if (!currentVersion) return null;

    // Calculate content difference (simplified)
    const currentContent = atob(currentVersion.content);
    const newContent = atob(file.content);
    const diffRatio = Math.abs(newContent.length - currentContent.length) / Math.max(currentContent.length, 1);

    // Only version if change is significant
    if (diffRatio >= changeThreshold) {
      return this.addVersion(file.id, file.content, userId, 'Auto-versioned due to significant changes');
    }

    return null;
  }
}

// Create a global instance
export const versionHistoryManager = new VersionHistoryManager();

// React hook for using version history
export const useVersionHistory = (fileId: string) => {
  const [history, setHistory] = React.useState<VersionHistory | null>(null);
  const [loading, setLoading] = React.useState(false);

  const loadHistory = React.useCallback(async () => {
    setLoading(true);
    try {
      const versionHistory = versionHistoryManager.getVersionHistory(fileId);
      setHistory(versionHistory);
    } catch (error) {
      console.error('Failed to load version history:', error);
    } finally {
      setLoading(false);
    }
  }, [fileId]);

  const addVersion = React.useCallback((
    content: string,
    userId: string = 'system',
    description?: string
  ) => {
    const version = versionHistoryManager.addVersion(fileId, content, userId, description);
    loadHistory(); // Refresh history
    return version;
  }, [fileId, loadHistory]);

  const revertToVersion = React.useCallback((versionNumber: number) => {
    const version = versionHistoryManager.revertToVersion(fileId, versionNumber);
    loadHistory(); // Refresh history
    return version;
  }, [fileId, loadHistory]);

  React.useEffect(() => {
    loadHistory();
  }, [loadHistory]);

  return {
    history,
    loading,
    loadHistory,
    addVersion,
    revertToVersion,
    getVersion: (versionNumber: number) => versionHistoryManager.getVersion(fileId, versionNumber),
    getAllVersions: () => versionHistoryManager.getAllVersions(fileId),
    compareVersions: (versionA: number, versionB: number) => 
      versionHistoryManager.compareVersions(fileId, versionA, versionB)
  };
};

// Version History Component
export const VersionHistoryPanel: React.FC<{
  fileId: string;
  onVersionSelect?: (version: FileVersion) => void;
  onRevert?: (version: FileVersion) => void;
}> = ({ fileId, onVersionSelect, onRevert }) => {
  const { history, loading, revertToVersion } = useVersionHistory(fileId);

  if (loading) {
    return (
      <div className="p-4 text-center text-muted-foreground">
        Loading version history...
      </div>
    );
  }

  if (!history || history.versions.length === 0) {
    return (
      <div className="p-4 text-center text-muted-foreground">
        No version history available
      </div>
    );
  }

  const handleRevert = (version: FileVersion) => {
    if (confirm(`Revert to version ${version.versionNumber}? This will create a new version.`)) {
      const newVersion = revertToVersion(version.versionNumber);
      if (newVersion && onRevert) {
        onRevert(newVersion);
      }
    }
  };

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <h4 className="font-medium">Version History</h4>
        <span className="text-sm text-muted-foreground">
          {history.versions.length} versions
        </span>
      </div>
      
      <div className="space-y-2 max-h-64 overflow-y-auto">
        {history.versions
          .sort((a, b) => b.versionNumber - a.versionNumber)
          .map((version) => (
            <div
              key={version.id}
              className={`p-3 rounded-lg border text-sm ${
                version.versionNumber === history.currentVersion
                  ? 'border-primary bg-primary/5'
                  : 'border-muted hover:bg-muted/50'
              }`}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <span className="font-medium">v{version.versionNumber}</span>
                  {version.versionNumber === history.currentVersion && (
                    <span className="px-1.5 py-0.5 bg-primary text-primary-foreground text-xs rounded">
                      Current
                    </span>
                  )}
                </div>
                <div className="text-xs text-muted-foreground">
                  {version.createdAt.toLocaleDateString()}
                </div>
              </div>
              
              {version.description && (
                <p className="text-xs text-muted-foreground mt-1">
                  {version.description}
                </p>
              )}
              
              <div className="flex items-center justify-between mt-2">
                <div className="text-xs text-muted-foreground">
                  {Math.round(version.size / 1024)} KB
                </div>
                <div className="flex gap-1">
                  <button
                    onClick={() => onVersionSelect?.(version)}
                    className="px-2 py-1 text-xs rounded hover:bg-muted"
                  >
                    View
                  </button>
                  {version.versionNumber !== history.currentVersion && (
                    <button
                      onClick={() => handleRevert(version)}
                      className="px-2 py-1 text-xs rounded hover:bg-muted text-orange-600"
                    >
                      Revert
                    </button>
                  )}
                </div>
              </div>
            </div>
          ))}
      </div>
    </div>
  );
};