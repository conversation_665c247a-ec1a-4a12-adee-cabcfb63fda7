import { colors } from '../ui/StyledComponents';

// Unified Color Coding System
export const dataCenterColors = {
  // Data Operations/Processing (Blue)
  dataOperation: {
    primary: colors.dataOperation,
    light: colors.dataOperation + '15',
    dark: colors.dataProcessing,
    text: colors.textPrimary,
    border: colors.dataOperation + '25'
  },
  
  // Success/Completion (Green)
  success: {
    primary: colors.success,
    light: colors.success + '15',
    dark: colors.successDark,
    text: colors.textPrimary,
    border: colors.success + '25'
  },
  
  // Warnings/Caution (Yellow)
  warning: {
    primary: colors.warning,
    light: colors.warning + '15',
    dark: colors.warningDark,
    text: colors.textPrimary,
    border: colors.warning + '25'
  },
  
  // Errors/Critical Issues (Red)
  error: {
    primary: colors.error,
    light: colors.error + '15',
    dark: colors.errorDark,
    text: colors.textPrimary,
    border: colors.error + '25'
  },
  
  // Info (Light Blue)
  info: {
    primary: colors.info,
    light: colors.info + '15',
    dark: '#79c7d9',
    text: colors.textPrimary,
    border: colors.info + '25'
  }
};

// Status Badge Variants
export const statusBadges = {
  success: `bg-green-100 text-green-800 border-green-200`,
  warning: `bg-orange-100 text-orange-800 border-orange-200`,
  error: `bg-red-100 text-red-800 border-red-200`,
  info: `bg-blue-100 text-blue-800 border-blue-200`,
  processing: `bg-purple-100 text-purple-800 border-purple-200`,
  pending: `bg-gray-100 text-gray-800 border-gray-200`
};

// Data Type Colors
export const dataTypeColors = {
  number: 'text-blue-600 bg-blue-100',
  text: 'text-gray-600 bg-gray-100',
  date: 'text-green-600 bg-green-100',
  boolean: 'text-purple-600 bg-purple-100',
  json: 'text-pink-600 bg-pink-100',
  array: 'text-orange-600 bg-orange-100'
};

// File Type Colors
export const fileTypeColors = {
  csv: 'text-green-600 bg-green-100',
  json: 'text-pink-600 bg-pink-100',
  excel: 'text-blue-600 bg-blue-100',
  pdf: 'text-red-600 bg-red-100',
  document: 'text-purple-600 bg-purple-100',
  image: 'text-yellow-600 bg-yellow-100',
  code: 'text-gray-600 bg-gray-100'
};

// Stage Colors for Pipeline
export const stageColors = {
  upload: dataCenterColors.operations,
  preview: dataCenterColors.data,
  process: dataCenterColors.analysis,
  analyze: dataCenterColors.success,
  output: dataCenterColors.neutral
};

// CSS Classes for Color Coding
export const colorCodingClasses = `
  /* Operations/Processing - Blue */
  .bg-operations { background-color: ${dataCenterColors.operations.light}; }
  .border-operations { border-color: ${dataCenterColors.operations.border}; }
  .text-operations { color: ${dataCenterColors.operations.primary}; }
  .ring-operations { box-shadow: 0 0 0 2px ${dataCenterColors.operations.light}; }

  /* Success/Completion - Green */
  .bg-success-themed { background-color: ${dataCenterColors.success.light}; }
  .border-success-themed { border-color: ${dataCenterColors.success.border}; }
  .text-success-themed { color: ${dataCenterColors.success.primary}; }

  /* Warnings/Caution - Orange */
  .bg-warning-themed { background-color: ${dataCenterColors.warning.light}; }
  .border-warning-themed { border-color: ${dataCenterColors.warning.border}; }
  .text-warning-themed { color: ${dataCenterColors.warning.primary}; }

  /* Errors/Critical - Red */
  .bg-error-themed { background-color: ${dataCenterColors.error.light}; }
  .border-error-themed { border-color: ${dataCenterColors.error.border}; }
  .text-error-themed { color: ${dataCenterColors.error.primary}; }

  /* Data-specific - Purple */
  .bg-data-themed { background-color: ${dataCenterColors.data.light}; }
  .border-data-themed { border-color: ${dataCenterColors.data.border}; }
  .text-data-themed { color: ${dataCenterColors.data.primary}; }

  /* Analysis - Pink */
  .bg-analysis-themed { background-color: ${dataCenterColors.analysis.light}; }
  .border-analysis-themed { border-color: ${dataCenterColors.analysis.border}; }
  .text-analysis-themed { color: ${dataCenterColors.analysis.primary}; }
`;

// Initialize color coding styles
export const initializeColorCoding = () => {
  if (typeof document !== 'undefined' && !document.getElementById('datacenter-colors')) {
    const style = document.createElement('style');
    style.id = 'datacenter-colors';
    style.textContent = colorCodingClasses;
    document.head.appendChild(style);
  }
};