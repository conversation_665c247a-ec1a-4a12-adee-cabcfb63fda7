import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import {
  GitBranch,
  Clock,
  User,
  FileText,
  Zap,
  Database,
  ArrowRight,
  RotateCcw,
  Eye,
  Download,
  Filter,
  Search,
  TrendingUp,
  Activity,
  Target
} from 'lucide-react';
import { FileVersion, VersionHistory, versionHistoryManager } from './versionHistory';

// Enhanced interfaces for data transformation tracking
export interface DataTransformation {
  id: string;
  type: 'pipeline' | 'manual' | 'ai_generated' | 'import' | 'export' | 'cleanup';
  operation: string;
  parameters: Record<string, any>;
  inputSchema?: any;
  outputSchema?: any;
  performance: {
    duration: number; // milliseconds
    memoryUsage?: number; // bytes
    recordsProcessed?: number;
    errorCount?: number;
  };
  timestamp: Date;
  userId: string;
}

export interface DataLineage {
  sourceFiles: string[];
  transformations: DataTransformation[];
  outputFiles: string[];
  dependencies: string[];
}

export interface EnhancedFileVersion extends FileVersion {
  transformations: DataTransformation[];
  lineage: DataLineage;
  qualityMetrics: {
    completeness: number; // 0-1
    accuracy: number; // 0-1
    consistency: number; // 0-1
    validity: number; // 0-1
  };
  processingStats: {
    totalRecords: number;
    validRecords: number;
    errorRecords: number;
    duplicateRecords: number;
  };
}

export class EnhancedVersionHistoryManager {
  private transformationHistory: Map<string, DataTransformation[]> = new Map();
  private lineageGraph: Map<string, DataLineage> = new Map();

  // Track a data transformation
  trackTransformation(
    fileId: string,
    transformation: Omit<DataTransformation, 'id' | 'timestamp'>
  ): DataTransformation {
    const fullTransformation: DataTransformation = {
      ...transformation,
      id: `trans_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date()
    };

    const existing = this.transformationHistory.get(fileId) || [];
    existing.push(fullTransformation);
    this.transformationHistory.set(fileId, existing);

    return fullTransformation;
  }

  // Get transformation history for a file
  getTransformationHistory(fileId: string): DataTransformation[] {
    return this.transformationHistory.get(fileId) || [];
  }

  // Track data lineage
  trackLineage(fileId: string, lineage: DataLineage): void {
    this.lineageGraph.set(fileId, lineage);
  }

  // Get data lineage for a file
  getLineage(fileId: string): DataLineage | null {
    return this.lineageGraph.get(fileId) || null;
  }

  // Get transformation statistics
  getTransformationStats(fileId: string): {
    totalTransformations: number;
    transformationTypes: Record<string, number>;
    averagePerformance: {
      duration: number;
      recordsProcessed: number;
      errorRate: number;
    };
    recentActivity: DataTransformation[];
  } {
    const transformations = this.getTransformationHistory(fileId);
    
    const stats = {
      totalTransformations: transformations.length,
      transformationTypes: {} as Record<string, number>,
      averagePerformance: {
        duration: 0,
        recordsProcessed: 0,
        errorRate: 0
      },
      recentActivity: transformations.slice(-10).reverse()
    };

    if (transformations.length === 0) return stats;

    // Count transformation types
    transformations.forEach(t => {
      stats.transformationTypes[t.type] = (stats.transformationTypes[t.type] || 0) + 1;
    });

    // Calculate averages
    const totalDuration = transformations.reduce((sum, t) => sum + t.performance.duration, 0);
    const totalRecords = transformations.reduce((sum, t) => sum + (t.performance.recordsProcessed || 0), 0);
    const totalErrors = transformations.reduce((sum, t) => sum + (t.performance.errorCount || 0), 0);

    stats.averagePerformance = {
      duration: totalDuration / transformations.length,
      recordsProcessed: totalRecords / transformations.length,
      errorRate: totalRecords > 0 ? totalErrors / totalRecords : 0
    };

    return stats;
  }

  // Find files with similar transformation patterns
  findSimilarTransformationPatterns(fileId: string): string[] {
    const targetTransformations = this.getTransformationHistory(fileId);
    if (targetTransformations.length === 0) return [];

    const targetPattern = targetTransformations.map(t => t.type).join('->');
    const similarFiles: string[] = [];

    this.transformationHistory.forEach((transformations, otherFileId) => {
      if (otherFileId === fileId) return;
      
      const pattern = transformations.map(t => t.type).join('->');
      if (pattern === targetPattern) {
        similarFiles.push(otherFileId);
      }
    });

    return similarFiles;
  }

  // Export transformation history
  exportTransformationHistory(): Record<string, DataTransformation[]> {
    const exported: Record<string, DataTransformation[]> = {};
    this.transformationHistory.forEach((transformations, fileId) => {
      exported[fileId] = transformations;
    });
    return exported;
  }

  // Import transformation history
  importTransformationHistory(data: Record<string, DataTransformation[]>): void {
    Object.entries(data).forEach(([fileId, transformations]) => {
      this.transformationHistory.set(fileId, transformations.map(t => ({
        ...t,
        timestamp: new Date(t.timestamp)
      })));
    });
  }
}

export const enhancedVersionHistoryManager = new EnhancedVersionHistoryManager();

// Enhanced Version History Panel Component
export const EnhancedVersionHistoryPanel: React.FC<{
  fileId: string;
  onVersionSelect?: (version: FileVersion) => void;
  onRevert?: (version: FileVersion) => void;
}> = ({ fileId, onVersionSelect, onRevert }) => {
  const [selectedTab, setSelectedTab] = React.useState('versions');
  const [searchTerm, setSearchTerm] = React.useState('');
  const [filterType, setFilterType] = React.useState<string>('all');
  
  const history = versionHistoryManager.getVersionHistory(fileId);
  const transformations = enhancedVersionHistoryManager.getTransformationHistory(fileId);
  const lineage = enhancedVersionHistoryManager.getLineage(fileId);
  const stats = enhancedVersionHistoryManager.getTransformationStats(fileId);

  const filteredVersions = React.useMemo(() => {
    if (!history) return [];
    
    return history.versions.filter(version => {
      const matchesSearch = searchTerm === '' || 
        version.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        version.createdBy.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesFilter = filterType === 'all' || 
        version.metadata?.transformationType === filterType;
      
      return matchesSearch && matchesFilter;
    }).sort((a, b) => b.versionNumber - a.versionNumber);
  }, [history, searchTerm, filterType]);

  const filteredTransformations = React.useMemo(() => {
    return transformations.filter(t => 
      searchTerm === '' || 
      t.operation.toLowerCase().includes(searchTerm.toLowerCase()) ||
      t.type.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [transformations, searchTerm]);

  if (!history) {
    return (
      <Card>
        <CardContent className="p-6 text-center text-muted-foreground">
          No version history available
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <GitBranch className="h-5 w-5" />
          Enhanced Version History
        </CardTitle>
        <div className="flex items-center gap-2">
          <div className="relative flex-1">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <input
              type="text"
              placeholder="Search versions or transformations..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8 pr-4 py-2 w-full border rounded-md text-sm"
            />
          </div>
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
            className="px-3 py-2 border rounded-md text-sm"
          >
            <option value="all">All Types</option>
            <option value="pipeline">Pipeline</option>
            <option value="manual">Manual</option>
            <option value="ai_generated">AI Generated</option>
            <option value="import">Import</option>
            <option value="export">Export</option>
            <option value="cleanup">Cleanup</option>
          </select>
        </div>
      </CardHeader>
      
      <CardContent>
        <Tabs value={selectedTab} onValueChange={setSelectedTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="versions">Versions</TabsTrigger>
            <TabsTrigger value="transformations">Transformations</TabsTrigger>
            <TabsTrigger value="lineage">Data Lineage</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>
          
          <TabsContent value="versions" className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">
                {filteredVersions.length} of {history.versions.length} versions
              </span>
              <Button size="sm" variant="outline">
                <Download className="h-4 w-4 mr-2" />
                Export History
              </Button>
            </div>
            
            <ScrollArea className="h-96">
              <div className="space-y-3">
                {filteredVersions.map((version) => (
                  <Card key={version.id} className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <Badge variant={version.versionNumber === history.currentVersion ? 'default' : 'secondary'}>
                            v{version.versionNumber}
                          </Badge>
                          {version.metadata?.transformationType && (
                            <Badge variant="outline">
                              {version.metadata.transformationType}
                            </Badge>
                          )}
                        </div>
                        
                        <div className="text-sm">
                          <div className="font-medium">{version.description || 'No description'}</div>
                          <div className="text-muted-foreground flex items-center gap-4 mt-1">
                            <span className="flex items-center gap-1">
                              <User className="h-3 w-3" />
                              {version.createdBy}
                            </span>
                            <span className="flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              {version.createdAt.toLocaleString()}
                            </span>
                            <span className="flex items-center gap-1">
                              <FileText className="h-3 w-3" />
                              {(version.size / 1024).toFixed(1)} KB
                            </span>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => onVersionSelect?.(version)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        {version.versionNumber !== history.currentVersion && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => onRevert?.(version)}
                          >
                            <RotateCcw className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </ScrollArea>
          </TabsContent>
          
          <TabsContent value="transformations" className="space-y-4">
            <div className="text-sm text-muted-foreground">
              {filteredTransformations.length} transformations found
            </div>
            
            <ScrollArea className="h-96">
              <div className="space-y-3">
                {filteredTransformations.map((transformation) => (
                  <Card key={transformation.id} className="p-4">
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Badge variant="outline">{transformation.type}</Badge>
                          <span className="font-medium">{transformation.operation}</span>
                        </div>
                        <span className="text-sm text-muted-foreground">
                          {transformation.timestamp.toLocaleString()}
                        </span>
                      </div>
                      
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-muted-foreground">Duration:</span>
                          <span className="ml-2">{transformation.performance.duration}ms</span>
                        </div>
                        {transformation.performance.recordsProcessed && (
                          <div>
                            <span className="text-muted-foreground">Records:</span>
                            <span className="ml-2">{transformation.performance.recordsProcessed.toLocaleString()}</span>
                          </div>
                        )}
                        {transformation.performance.errorCount !== undefined && (
                          <div>
                            <span className="text-muted-foreground">Errors:</span>
                            <span className="ml-2">{transformation.performance.errorCount}</span>
                          </div>
                        )}
                        <div>
                          <span className="text-muted-foreground">User:</span>
                          <span className="ml-2">{transformation.userId}</span>
                        </div>
                      </div>
                      
                      {Object.keys(transformation.parameters).length > 0 && (
                        <details className="text-sm">
                          <summary className="cursor-pointer text-muted-foreground">Parameters</summary>
                          <pre className="mt-2 p-2 bg-muted rounded text-xs overflow-x-auto">
                            {JSON.stringify(transformation.parameters, null, 2)}
                          </pre>
                        </details>
                      )}
                    </div>
                  </Card>
                ))}
              </div>
            </ScrollArea>
          </TabsContent>
          
          <TabsContent value="lineage" className="space-y-4">
            {lineage ? (
              <div className="space-y-4">
                <Card className="p-4">
                  <h4 className="font-medium mb-3">Data Flow</h4>
                  <div className="space-y-3">
                    <div>
                      <span className="text-sm font-medium text-muted-foreground">Source Files:</span>
                      <div className="mt-1 flex flex-wrap gap-1">
                        {lineage.sourceFiles.map((file, idx) => (
                          <Badge key={idx} variant="outline">{file}</Badge>
                        ))}
                      </div>
                    </div>
                    
                    <ArrowRight className="h-4 w-4 text-muted-foreground mx-auto" />
                    
                    <div>
                      <span className="text-sm font-medium text-muted-foreground">Transformations:</span>
                      <div className="mt-1 space-y-1">
                        {lineage.transformations.map((t, idx) => (
                          <div key={idx} className="text-sm p-2 bg-muted rounded">
                            {t.operation} ({t.type})
                          </div>
                        ))}
                      </div>
                    </div>
                    
                    <ArrowRight className="h-4 w-4 text-muted-foreground mx-auto" />
                    
                    <div>
                      <span className="text-sm font-medium text-muted-foreground">Output Files:</span>
                      <div className="mt-1 flex flex-wrap gap-1">
                        {lineage.outputFiles.map((file, idx) => (
                          <Badge key={idx} variant="outline">{file}</Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                </Card>
                
                {lineage.dependencies.length > 0 && (
                  <Card className="p-4">
                    <h4 className="font-medium mb-3">Dependencies</h4>
                    <div className="flex flex-wrap gap-1">
                      {lineage.dependencies.map((dep, idx) => (
                        <Badge key={idx} variant="secondary">{dep}</Badge>
                      ))}
                    </div>
                  </Card>
                )}
              </div>
            ) : (
              <div className="text-center text-muted-foreground py-8">
                No data lineage information available
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="analytics" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card className="p-4">
                <div className="flex items-center gap-2 mb-3">
                  <Activity className="h-4 w-4" />
                  <h4 className="font-medium">Transformation Summary</h4>
                </div>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Total Transformations:</span>
                    <span className="font-medium">{stats.totalTransformations}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Avg Duration:</span>
                    <span className="font-medium">{Math.round(stats.averagePerformance.duration)}ms</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Avg Records:</span>
                    <span className="font-medium">{Math.round(stats.averagePerformance.recordsProcessed).toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Error Rate:</span>
                    <span className="font-medium">{(stats.averagePerformance.errorRate * 100).toFixed(2)}%</span>
                  </div>
                </div>
              </Card>
              
              <Card className="p-4">
                <div className="flex items-center gap-2 mb-3">
                  <TrendingUp className="h-4 w-4" />
                  <h4 className="font-medium">Transformation Types</h4>
                </div>
                <div className="space-y-2">
                  {Object.entries(stats.transformationTypes).map(([type, count]) => (
                    <div key={type} className="flex items-center justify-between text-sm">
                      <Badge variant="outline">{type}</Badge>
                      <span className="font-medium">{count}</span>
                    </div>
                  ))}
                </div>
              </Card>
            </div>
            
            <Card className="p-4">
              <div className="flex items-center gap-2 mb-3">
                <Target className="h-4 w-4" />
                <h4 className="font-medium">Recent Activity</h4>
              </div>
              <ScrollArea className="h-48">
                <div className="space-y-2">
                  {stats.recentActivity.map((activity) => (
                    <div key={activity.id} className="flex items-center justify-between p-2 bg-muted rounded text-sm">
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="text-xs">{activity.type}</Badge>
                        <span>{activity.operation}</span>
                      </div>
                      <span className="text-muted-foreground">
                        {activity.timestamp.toLocaleTimeString()}
                      </span>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </Card>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

// Hook for enhanced version history
export const useEnhancedVersionHistory = (fileId: string) => {
  const [transformations, setTransformations] = React.useState<DataTransformation[]>([]);
  const [lineage, setLineage] = React.useState<DataLineage | null>(null);
  const [stats, setStats] = React.useState<any>(null);

  const loadData = React.useCallback(() => {
    const transformationHistory = enhancedVersionHistoryManager.getTransformationHistory(fileId);
    const dataLineage = enhancedVersionHistoryManager.getLineage(fileId);
    const transformationStats = enhancedVersionHistoryManager.getTransformationStats(fileId);
    
    setTransformations(transformationHistory);
    setLineage(dataLineage);
    setStats(transformationStats);
  }, [fileId]);

  const trackTransformation = React.useCallback((transformation: Omit<DataTransformation, 'id' | 'timestamp'>) => {
    const tracked = enhancedVersionHistoryManager.trackTransformation(fileId, transformation);
    loadData();
    return tracked;
  }, [fileId, loadData]);

  const trackLineage = React.useCallback((lineageData: DataLineage) => {
    enhancedVersionHistoryManager.trackLineage(fileId, lineageData);
    loadData();
  }, [fileId, loadData]);

  React.useEffect(() => {
    loadData();
  }, [loadData]);

  return {
    transformations,
    lineage,
    stats,
    trackTransformation,
    trackLineage,
    loadData
  };
};