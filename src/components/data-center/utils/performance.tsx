import React, { useState, useEffect, useRef, useCallback } from 'react';

interface VirtualizedListProps<T> {
  items: T[];
  itemHeight: number;
  windowHeight: number;
  renderItem: (item: T, index: number) => React.ReactNode;
  onScroll?: (scrollTop: number) => void;
  className?: string;
}

export const VirtualizedList = <T,>({
  items,
  itemHeight,
  windowHeight,
  renderItem,
  onScroll,
  className = ''
}: VirtualizedListProps<T>) => {
  const [scrollTop, setScrollTop] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);

  const visibleStartIndex = Math.floor(scrollTop / itemHeight);
  const visibleEndIndex = Math.min(
    items.length - 1,
    Math.ceil((scrollTop + windowHeight) / itemHeight)
  );

  const totalHeight = items.length * itemHeight;
  const offsetY = visibleStartIndex * itemHeight;
  const visibleItems = items.slice(visibleStartIndex, visibleEndIndex + 1);

  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const newScrollTop = e.currentTarget.scrollTop;
    setScrollTop(newScrollTop);
    onScroll?.(newScrollTop);
  }, [onScroll]);

  return (
    <div
      ref={containerRef}
      className={`overflow-auto ${className}`}
      style={{ height: windowHeight }}
      onScroll={handleScroll}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        <div style={{ transform: `translateY(${offsetY}px)` }}>
          {visibleItems.map((item, index) => (
            <div
              key={visibleStartIndex + index}
              style={{ height: itemHeight }}
            >
              {renderItem(item, visibleStartIndex + index)}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

interface VirtualizedTableProps<T> {
  data: T[];
  columns: Array<{
    key: string;
    header: string;
    render?: (item: T, index: number) => React.ReactNode;
    width?: number;
  }>;
  rowHeight?: number;
  windowHeight?: number;
  onRowClick?: (item: T, index: number) => void;
  className?: string;
}

export const VirtualizedTable = <T,>({
  data,
  columns,
  rowHeight = 40,
  windowHeight = 400,
  onRowClick,
  className = ''
}: VirtualizedTableProps<T>) => {
  const [selectedRow, setSelectedRow] = useState<number | null>(null);

  const handleRowClick = (item: T, index: number) => {
    setSelectedRow(index);
    onRowClick?.(item, index);
  };

  const renderRow = (item: T, index: number) => (
    <div
      className={`flex border-b hover:bg-muted/50 cursor-pointer ${
        selectedRow === index ? 'bg-primary/10' : ''
      }`}
      style={{ height: rowHeight }}
      onClick={() => handleRowClick(item, index)}
    >
      {columns.map((column) => (
        <div
          key={column.key}
          className="px-3 py-2 text-sm flex items-center"
          style={{ width: column.width || 'auto', flex: column.width ? '0 0 auto' : '1' }}
        >
          {column.render ? column.render(item, index) : String((item as any)[column.key])}
        </div>
      ))}
    </div>
  );

  return (
    <div className={`border rounded-lg ${className}`}>
      {/* Table Header */}
      <div className="flex bg-muted/50 border-b font-medium">
        {columns.map((column) => (
          <div
            key={column.key}
            className="px-3 py-2 text-sm"
            style={{ width: column.width || 'auto', flex: column.width ? '0 0 auto' : '1' }}
          >
            {column.header}
          </div>
        ))}
      </div>
      
      {/* Virtualized Body */}
      <VirtualizedList
        items={data}
        itemHeight={rowHeight}
        windowHeight={windowHeight - 40} // Subtract header height
        renderItem={renderRow}
      />
    </div>
  );
};

// Pagination Hook for Large Datasets
export const usePagination = <T,>(
  data: T[],
  itemsPerPage = 20
) => {
  const [currentPage, setCurrentPage] = useState(1);
  
  const totalPages = Math.ceil(data.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentData = data.slice(startIndex, endIndex);
  
  const goToPage = (page: number) => {
    setCurrentPage(Math.max(1, Math.min(page, totalPages)));
  };
  
  const nextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };
  
  const prevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };
  
  return {
    currentData,
    currentPage,
    totalPages,
    startIndex,
    endIndex: Math.min(endIndex, data.length),
    totalItems: data.length,
    goToPage,
    nextPage,
    prevPage,
    canNextPage: currentPage < totalPages,
    canPrevPage: currentPage > 1
  };
};

// Lazy Loading Hook
export const useLazyLoading = <T,>(
  loadData: () => Promise<T[]>,
  initialLoadCount = 50
) => {
  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  const loadDataChunk = async (start: number, count: number) => {
    try {
      setLoading(true);
      const allData = await loadData();
      const chunk = allData.slice(start, start + count);
      
      if (chunk.length < count) {
        setHasMore(false);
      }
      
      setData(prev => [...prev, ...chunk]);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load data');
      setHasMore(false);
    } finally {
      setLoading(false);
    }
  };
  
  const loadMore = useCallback(() => {
    if (!loading && hasMore) {
      loadDataChunk(data.length, initialLoadCount);
    }
  }, [data.length, hasMore, loading, initialLoadCount, loadData]);
  
  const reset = useCallback(() => {
    setData([]);
    setHasMore(true);
    setError(null);
    loadDataChunk(0, initialLoadCount);
  }, [initialLoadCount, loadData]);
  
  useEffect(() => {
    reset();
  }, [reset]);
  
  return {
    data,
    loading,
    hasMore,
    error,
    loadMore,
    reset
  };
};

// Caching System for Processed Results
class DataCache<T = any> {
  private cache: Map<string, { data: T; timestamp: number; ttl: number }> = new Map();
  private maxSize: number;
  
  constructor(maxSize = 100) {
    this.maxSize = maxSize;
  }
  
  set(key: string, data: T, ttl = 300000) { // 5 minutes default
    // Remove oldest entries if cache is full
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      if (firstKey) this.cache.delete(firstKey);
    }
    
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }
  
  get(key: string): T | null {
    const entry = this.cache.get(key);
    if (!entry) return null;
    
    // Check if entry is expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }
    
    return entry.data;
  }
  
  has(key: string): boolean {
    return this.get(key) !== null;
  }
  
  clear() {
    this.cache.clear();
  }
  
  size(): number {
    return this.cache.size;
  }
}

export const dataCache = new DataCache<any>(100);

// Performance Monitoring Hook
export const usePerformanceMonitor = () => {
  const [metrics, setMetrics] = useState({
    renderTime: 0,
    memoryUsage: 0,
    fps: 0
  });
  
  const measureRenderTime = <T extends (...args: any[]) => any>(fn: T): T => {
    return function(...args: any[]) {
      const start = performance.now();
      const result = fn(...args);
      const end = performance.now();
      
      setMetrics(prev => ({
        ...prev,
        renderTime: end - start
      }));
      
      return result;
    } as T;
  };
  
  return {
    metrics,
    measureRenderTime
  };
};