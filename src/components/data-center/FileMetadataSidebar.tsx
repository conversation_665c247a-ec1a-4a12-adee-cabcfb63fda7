import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from '../ui/card';
import { Badge } from '../ui/badge';
import { But<PERSON> } from '../ui/button';
import { ScrollArea } from '../ui/scroll-area';
import { Separator } from '../ui/separator';
import { formatBytes, formatDate } from '../../lib/utils';
import {
  FileText,
  Calendar,
  HardDrive,
  Activity,
  Clock,
  Tag,
  Download,
  Eye,
  BarChart3,
  Database,
  Zap
} from 'lucide-react';

interface ProcessingStep {
  id: string;
  type: 'upload' | 'analysis' | 'transformation' | 'export' | 'visualization';
  name: string;
  timestamp: Date;
  duration?: number;
  status: 'completed' | 'failed' | 'in_progress';
  details?: string;
  outputSize?: number;
}

interface FileMetadata {
  name: string;
  size: number;
  type: string;
  uploadDate: Date;
  lastModified: Date;
  rows?: number;
  columns?: number;
  encoding?: string;
  delimiter?: string;
  processingHistory: ProcessingStep[];
  tags: string[];
  quality?: {
    completeness: number;
    accuracy: number;
    consistency: number;
  };
}

interface FileMetadataSidebarProps {
  metadata: FileMetadata | null;
  isOpen: boolean;
  onClose: () => void;
  onExport?: (format: string) => void;
  onVisualize?: () => void;
}

const getStepIcon = (type: ProcessingStep['type']) => {
  switch (type) {
    case 'upload': return <Download className="w-4 h-4" />;
    case 'analysis': return <BarChart3 className="w-4 h-4" />;
    case 'transformation': return <Zap className="w-4 h-4" />;
    case 'export': return <FileText className="w-4 h-4" />;
    case 'visualization': return <Eye className="w-4 h-4" />;
    default: return <Activity className="w-4 h-4" />;
  }
};

const getStatusColor = (status: ProcessingStep['status']) => {
  switch (status) {
    case 'completed': return 'bg-green-100 text-green-800';
    case 'failed': return 'bg-red-100 text-red-800';
    case 'in_progress': return 'bg-blue-100 text-blue-800';
    default: return 'bg-gray-100 text-gray-800';
  }
};

export const FileMetadataSidebar: React.FC<FileMetadataSidebarProps> = ({
  metadata,
  isOpen,
  onClose,
  onExport,
  onVisualize
}) => {
  if (!isOpen || !metadata) return null;

  const totalProcessingTime = metadata.processingHistory
    .filter(step => step.duration)
    .reduce((total, step) => total + (step.duration || 0), 0);

  return (
    <div className="fixed right-0 top-0 h-full w-96 bg-white border-l border-gray-200 shadow-lg z-50 overflow-hidden">
      <div className="flex items-center justify-between p-4 border-b">
        <h2 className="text-lg font-semibold">File Metadata</h2>
        <Button variant="ghost" size="sm" onClick={onClose}>
          ×
        </Button>
      </div>

      <ScrollArea className="h-full pb-20">
        <div className="p-4 space-y-6">
          {/* Basic File Info */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center gap-2">
                <FileText className="w-4 h-4" />
                File Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <p className="text-sm font-medium text-gray-900 truncate" title={metadata.name}>
                  {metadata.name}
                </p>
                <p className="text-xs text-gray-500">{metadata.type.toUpperCase()}</p>
              </div>
              
              <div className="grid grid-cols-2 gap-3 text-xs">
                <div>
                  <p className="text-gray-500">Size</p>
                  <p className="font-medium">{formatBytes(metadata.size)}</p>
                </div>
                <div>
                  <p className="text-gray-500">Encoding</p>
                  <p className="font-medium">{metadata.encoding || 'UTF-8'}</p>
                </div>
              </div>

              {metadata.rows && metadata.columns && (
                <div className="grid grid-cols-2 gap-3 text-xs">
                  <div>
                    <p className="text-gray-500">Rows</p>
                    <p className="font-medium">{metadata.rows.toLocaleString()}</p>
                  </div>
                  <div>
                    <p className="text-gray-500">Columns</p>
                    <p className="font-medium">{metadata.columns}</p>
                  </div>
                </div>
              )}

              <div className="grid grid-cols-1 gap-2 text-xs">
                <div>
                  <p className="text-gray-500 flex items-center gap-1">
                    <Calendar className="w-3 h-3" />
                    Uploaded
                  </p>
                  <p className="font-medium">{formatDate(metadata.uploadDate)}</p>
                </div>
                <div>
                  <p className="text-gray-500 flex items-center gap-1">
                    <Clock className="w-3 h-3" />
                    Modified
                  </p>
                  <p className="font-medium">{formatDate(metadata.lastModified)}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Data Quality */}
          {metadata.quality && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2">
                  <BarChart3 className="w-4 h-4" />
                  Data Quality
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="space-y-2">
                  <div className="flex justify-between text-xs">
                    <span>Completeness</span>
                    <span className="font-medium">{Math.round(metadata.quality.completeness * 100)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-green-500 h-2 rounded-full" 
                      style={{ width: `${metadata.quality.completeness * 100}%` }}
                    />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between text-xs">
                    <span>Accuracy</span>
                    <span className="font-medium">{Math.round(metadata.quality.accuracy * 100)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-500 h-2 rounded-full" 
                      style={{ width: `${metadata.quality.accuracy * 100}%` }}
                    />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between text-xs">
                    <span>Consistency</span>
                    <span className="font-medium">{Math.round(metadata.quality.consistency * 100)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-purple-500 h-2 rounded-full" 
                      style={{ width: `${metadata.quality.consistency * 100}%` }}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Tags */}
          {metadata.tags.length > 0 && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2">
                  <Tag className="w-4 h-4" />
                  Tags
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-1">
                  {metadata.tags.map((tag, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Processing History */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center gap-2">
                <Activity className="w-4 h-4" />
                Processing History
              </CardTitle>
              {totalProcessingTime > 0 && (
                <p className="text-xs text-gray-500">
                  Total time: {Math.round(totalProcessingTime / 1000)}s
                </p>
              )}
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {metadata.processingHistory.map((step, index) => (
                  <div key={step.id} className="relative">
                    {index < metadata.processingHistory.length - 1 && (
                      <div className="absolute left-2 top-8 w-px h-6 bg-gray-200" />
                    )}
                    
                    <div className="flex items-start gap-3">
                      <div className="flex-shrink-0 w-4 h-4 mt-0.5">
                        {getStepIcon(step.type)}
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {step.name}
                          </p>
                          <Badge 
                            className={`text-xs ${getStatusColor(step.status)}`}
                            variant="secondary"
                          >
                            {step.status}
                          </Badge>
                        </div>
                        
                        <p className="text-xs text-gray-500 mt-1">
                          {formatDate(step.timestamp)}
                        </p>
                        
                        {step.duration && (
                          <p className="text-xs text-gray-500">
                            Duration: {Math.round(step.duration / 1000)}s
                          </p>
                        )}
                        
                        {step.details && (
                          <p className="text-xs text-gray-600 mt-1">
                            {step.details}
                          </p>
                        )}
                        
                        {step.outputSize && (
                          <p className="text-xs text-gray-500">
                            Output: {formatBytes(step.outputSize)}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              {onExport && (
                <div className="space-y-2">
                  <p className="text-xs text-gray-500">Export as:</p>
                  <div className="grid grid-cols-2 gap-2">
                    <Button 
                      size="sm" 
                      variant="outline" 
                      onClick={() => onExport('csv')}
                      className="text-xs"
                    >
                      CSV
                    </Button>
                    <Button 
                      size="sm" 
                      variant="outline" 
                      onClick={() => onExport('json')}
                      className="text-xs"
                    >
                      JSON
                    </Button>
                    <Button 
                      size="sm" 
                      variant="outline" 
                      onClick={() => onExport('xlsx')}
                      className="text-xs"
                    >
                      Excel
                    </Button>
                    <Button 
                      size="sm" 
                      variant="outline" 
                      onClick={() => onExport('parquet')}
                      className="text-xs"
                    >
                      Parquet
                    </Button>
                  </div>
                </div>
              )}
              
              {onVisualize && (
                <>
                  <Separator />
                  <Button 
                    size="sm" 
                    onClick={onVisualize}
                    className="w-full text-xs"
                  >
                    <Eye className="w-3 h-3 mr-1" />
                    Create Visualization
                  </Button>
                </>
              )}
            </CardContent>
          </Card>
        </div>
      </ScrollArea>
    </div>
  );
};

export default FileMetadataSidebar;