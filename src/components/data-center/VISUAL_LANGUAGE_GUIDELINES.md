# Data Center Visual Language Guidelines

## Color Coding System

### Primary Colors
- **Data Operations/Processing**: Blue (`#8884d8`)
- **Success/Completion**: Green (`#82ca9d`)
- **Warnings/Caution**: Yellow (`#ffc658`)
- **Errors/Critical Issues**: Red (`#ff7c7c`)

### Usage Guidelines
- **Blue**: Used for data-related actions, processing buttons, and data flow indicators
- **Green**: Used for success states, completion indicators, and positive feedback
- **Yellow**: Used for warnings, caution messages, and attention-grabbing elements
- **Red**: Used for errors, critical issues, and destructive actions

## Component Styling Standards

### Cards
- **Default**: Standard white background with subtle shadow
- **Data Variant**: Blue left border for data-related content
- **Success Variant**: Green left border for successful operations
- **Warning Variant**: Yellow left border for cautionary content
- **Error Variant**: Red left border for error states
- **Info Variant**: Light blue left border for informational content

### Buttons
- **Primary**: Main action buttons (Blue)
- **Data**: Data processing actions (Blue)
- **Success**: Confirmation/submit actions (Green)
- **Warning**: Cautionary actions (Yellow)
- **Error**: Destructive actions (Red)
- **Secondary**: Alternative actions (Gray)
- **Ghost**: Minimal emphasis actions (Transparent)

### Badges
- **Data**: Blue background for data indicators
- **Success**: Green background for success indicators
- **Warning**: Yellow background for warning indicators
- **Error**: Red background for error indicators
- **Info**: Light blue background for informational badges

### Typography Hierarchy
1. **H1**: 24px, Semi-bold - Page titles
2. **H2**: 20px, Semi-bold - Section headers
3. **H3**: 18px, Semi-bold - Subsection headers
4. **Body**: 14px, Regular - Main content
5. **Caption**: 12px, Regular - Supporting text
6. **Small**: 11px, Regular - Meta information

## Spacing Scale
- **xs**: 4px
- **sm**: 8px
- **md**: 16px
- **lg**: 24px
- **xl**: 32px
- **xxl**: 48px

## Border Radius
- **sm**: 4px
- **md**: 8px
- **lg**: 12px
- **xl**: 16px
- **full**: 50%

## Shadows
- **Light**: `0 1px 3px rgba(0,0,0,0.12)`
- **Medium**: `0 4px 6px rgba(0,0,0,0.12)`
- **Dark**: `0 10px 20px rgba(0,0,0,0.12)`

## Hover Effects
- **Cards**: Subtle elevation and shadow enhancement
- **Buttons**: Color darkening and slight lift
- **Interactive Elements**: Background color change and cursor change

## Icon Usage Guidelines
- **Data Operations**: Database, File, Table icons (Blue)
- **Success**: Check, Thumbs up, Complete icons (Green)
- **Warnings**: Warning, Alert, Caution icons (Yellow)
- **Errors**: X, Error, Critical icons (Red)
- **Navigation**: Arrow, Chevron, Menu icons (Gray)

## Consistency Rules
1. Use the same color for the same type of action across all components
2. Maintain consistent spacing between elements
3. Follow the typography hierarchy strictly
4. Apply hover effects consistently
5. Use appropriate variants for different semantic meanings