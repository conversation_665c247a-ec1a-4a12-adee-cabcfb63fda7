import React, { createContext, useContext, useState, useEffect, ReactNode, useCallback } from 'react';
import { StorageFile } from '@/lib/services/browserStorageManager';
import { 
  DataPipelineStateSchema, 
  validateDataPipelineState, 
  validatePartialState,
  ValidationError
} from './state/StateSchema';
import { 
  OptimisticUpdateManager, 
  ConflictResolver, 
  UpdateIndicatorManager 
} from './state/OptimisticUpdates';
import { MultiLevelCache } from './state/StateCache';
import { StateOptimizer, StatePerformanceMonitor } from './state/StateOptimizer';
import { HistoryManager, saveSession, loadSession } from './utils/stateManagement';
import { dataCache, processedDataCache, persistentCache } from './performance';

// Extended interfaces for unified state management
export interface DataPreview {
  headers: string[];
  rows: any[][];
  totalRows: number;
  dataTypes?: Record<string, string>;
  statistics?: Record<string, any>;
  fullData?: any[][]; // Store full dataset for exploration
}

export interface ProcessingResult {
  content: string;
  metadata: Record<string, any>;
  chunks?: Array<{
    text: string;
    metadata: Record<string, any>;
  }>;
  embeddings?: number[][];
}

export interface SQLGenerationResult {
  sql: string;
  isValid: boolean;
  error?: string;
  analysis?: string;
}

export interface DataPipelineState {
  // File information
  fileName: string;
  fileType: string;
  fileSize: number;
  uploadDate: Date;
  
  // Data preview
  dataPreview: DataPreview | null;
  
  // Processing results
  processingResult: ProcessingResult | null;
  pythonScript: string;
  pythonOutput: string;
  selectedTemplate: string;
  
  // SQL generation
  sqlResult: SQLGenerationResult | null;
  
  // Pipeline tracking
  currentStage: 'upload' | 'preview' | 'explore' | 'processing' | 'analysis' | 'sql' | 'storage' | 'process' | 'storytelling';
  pipelineHistory: Array<{
    stage: string;
    timestamp: Date;
    status: 'completed' | 'pending' | 'error';
    details?: string;
  }>;
  
  // Storage reference
  storageFile: StorageFile | null;
  
  // Processing data
  processedData?: any;
  processingMetadata?: Record<string, any>;
  
  // User preferences
  preferences: {
    viewMode: 'grid' | 'list' | 'tree';
    theme: 'light' | 'dark' | 'system';
    showSchema: boolean;
    autoRefresh: boolean;
    sidebarCollapsed?: boolean;
    lastActiveView?: string;
    favorites?: string[];
  };
  
  // Filters with persistence across tabs
  filters: Record<string, any>;
  
  // Save points for analysis states
  savePoints: Array<{
    id: string;
    name: string;
    timestamp: Date;
    state: Partial<DataPipelineState>;
    description?: string;
  }>;
  
  // Context tracking for persistent state between tabs
  context: {
    breadcrumbPath: Array<{
      stage: string;
      label: string;
      timestamp: Date;
      dataSummary?: {
        rows?: number;
        columns?: number;
        fileType?: string;
      };
    }>;
    currentData: any; // Persistent data context across tabs
    activeFilters: Record<string, any>;
    processingMetadata: Record<string, any>;
  };
  
  // Performance optimization state
  performance: {
    cacheEnabled: boolean;
    virtualizationEnabled: boolean;
    backgroundProcessing: boolean;
    workerPoolSize: number;
    cacheStats: {
      hitRate: number;
      missRate: number;
      cacheSize: number;
    };
  };
  
  // Enhanced state management properties
  validationErrors?: ValidationError[];
  isOptimisticUpdatePending?: boolean;
  lastOptimisticUpdateId?: string;
}

// Unified context type with all features
interface UnifiedDataContextType {
  // Core state and navigation
  pipelineState: DataPipelineState;
  updatePipelineState: (updates: Partial<DataPipelineState>, notifyParent?: boolean) => Promise<boolean>;
  resetPipeline: () => void;
  navigateToStage: (stage: DataPipelineState['currentStage']) => void;
  updateContext: (contextUpdates: Partial<DataPipelineState['context']>) => void;
  addToBreadcrumb: (stage: string, label: string, dataSummary?: any) => void;
  
  // Save points and history
  createSavePoint: (name: string, description?: string) => void;
  loadSavePoint: (id: string) => void;
  deleteSavePoint: (id: string) => void;
  historyManager: HistoryManager;
  canUndo: boolean;
  canRedo: boolean;
  undo: () => void;
  redo: () => void;
  
  // Filtering
  updateFilters: (newFilters: Record<string, any>) => void;
  clearFilters: (filterKeys?: string[]) => void;
  
  // Performance optimization
  enableCaching: (enabled: boolean) => void;
  enableVirtualization: (enabled: boolean) => void;
  updateWorkerPoolSize: (size: number) => void;
  getCacheStats: () => { hitRate: number; missRate: number; cacheSize: number };
  clearCache: () => void;
  
  // State validation
  validateState: () => { valid: boolean; errors?: ValidationError[] };
  validatePartialState: (partialState: Partial<DataPipelineState>) => { valid: boolean; errors?: ValidationError[] };
  
  // Optimistic updates
  applyOptimisticUpdate: (action: string, updates: Partial<DataPipelineState>) => Promise<string>;
  confirmOptimisticUpdate: (id: string) => boolean;
  rollbackOptimisticUpdate: (id: string, error?: string) => boolean;
  getPendingUpdates: () => Array<{ id: string; action: string; timestamp: number; status: string }>;
  getFailedUpdates: () => Array<{ id: string; action: string; timestamp: number; status: string; error?: string }>;
  clearCompletedOptimisticUpdates: () => number;
  
  // Caching system
  getCache: (key: string) => any;
  setCache: (key: string, data: any, options?: { ttl?: number; tags?: string[] }) => void;
  invalidateCacheByTag: (tag: string) => number;
  
  // Performance monitoring
  getPerformanceStats: () => Record<string, any>;
  measurePerformance: <T>(operation: string, fn: () => T) => T;
  
  // Debugging and snapshots
  getSnapshot: () => string;
  restoreSnapshot: (id: string) => boolean;
  logStateChange: (action: string, path: string[], oldValue: any, newValue: any) => void;
}

const DataContext = createContext<UnifiedDataContextType | undefined>(undefined);

// Create default state helper
const createDefaultState = (): DataPipelineState => ({
  fileName: '',
  fileType: '',
  fileSize: 0,
  uploadDate: new Date(),
  dataPreview: null,
  processingResult: null,
  pythonScript: '',
  pythonOutput: '',
  selectedTemplate: '',
  sqlResult: null,
  currentStage: 'upload',
  pipelineHistory: [],
  storageFile: null,
  preferences: {
    viewMode: 'grid',
    theme: 'system',
    showSchema: true,
    autoRefresh: true
  },
  filters: {},
  savePoints: [],
  context: {
    breadcrumbPath: [],
    currentData: null,
    activeFilters: {},
    processingMetadata: {}
  },
  performance: {
    cacheEnabled: true,
    virtualizationEnabled: true,
    backgroundProcessing: true,
    workerPoolSize: 3,
    cacheStats: {
      hitRate: 0,
      missRate: 0,
      cacheSize: 0
    }
  }
});

export const UnifiedDataCenterProvider: React.FC<{ 
  children: ReactNode;
  onDataChange?: (data: Partial<DataPipelineState>) => void;
}> = ({ children, onDataChange }) => {
  const [pipelineState, setPipelineState] = useState<DataPipelineState>(() => {
    // Try to load from session storage
    const sessionData = loadSession();
    if (sessionData && sessionData.pipelineState) {
      return {
        ...createDefaultState(),
        ...sessionData.pipelineState
      };
    }
    return createDefaultState();
  });

  // Initialize enhanced state management components
  const [historyManager] = useState(() => new HistoryManager(50));
  const [canUndo, setCanUndo] = useState(false);
  const [canRedo, setCanRedo] = useState(false);
  const [optimisticUpdateManager] = useState(() => new OptimisticUpdateManager(pipelineState));
  const [updateIndicatorManager] = useState(() => new UpdateIndicatorManager());
  const [multiLevelCache] = useState(() => new MultiLevelCache());
  const [stateOptimizer] = useState(() => new StateOptimizer(pipelineState));
  const [performanceMonitor] = useState(() => new StatePerformanceMonitor());

  // Update undo/redo state
  useEffect(() => {
    setCanUndo(historyManager.canUndo());
    setCanRedo(historyManager.canRedo());
  }, [historyManager, pipelineState]);

  // Core state update function
  const updatePipelineState = useCallback((updates: Partial<DataPipelineState>, notifyParent = true): Promise<boolean> => {
    return new Promise((resolve) => {
      setPipelineState(prev => {
        const newState = { ...prev, ...updates };
        
        // Auto-add to history if stage changes
        if (updates.currentStage && updates.currentStage !== prev.currentStage) {
          const historyEntry = {
            stage: updates.currentStage,
            timestamp: new Date(),
            status: 'completed' as const,
            details: `Navigated to ${updates.currentStage} stage`
          };
          
          newState.pipelineHistory = [...prev.pipelineHistory, historyEntry];
        }
        
        // Add to history manager for undo/redo
        historyManager.push('state_update', newState);
        
        // Save to session storage
        saveSession({ pipelineState: newState });
        
        // Notify parent component if callback exists and notifyParent is true
        if (onDataChange && notifyParent) {
          onDataChange(updates);
        }
        
        // Validate state after update
        const validationResult = validateDataPipelineState(newState);
        if (!validationResult.valid && validationResult.errors) {
          newState.validationErrors = validationResult.errors;
        } else {
          newState.validationErrors = undefined;
        }
        
        resolve(validationResult.valid);
        return newState;
      });
    });
  }, [historyManager, onDataChange]);

  // Save points management
  const createSavePoint = useCallback((name: string, description?: string) => {
    setPipelineState(prev => {
      const savePoint = {
        id: Math.random().toString(36).substr(2, 9),
        name,
        timestamp: new Date(),
        state: JSON.parse(JSON.stringify(prev)), // Deep clone
        description
      };
      
      const newState = {
        ...prev,
        savePoints: [...prev.savePoints, savePoint]
      };
      
      // Add to history manager for undo/redo
      historyManager.push('state_update', newState);
      
      // Save to session storage
      saveSession({ pipelineState: newState });
      
      // Notify parent component if callback exists
      if (onDataChange) {
        onDataChange({ savePoints: newState.savePoints });
      }
      
      return newState;
    });
  }, [historyManager, onDataChange]);

  const loadSavePoint = useCallback((id: string) => {
    const savePoint = pipelineState.savePoints.find(sp => sp.id === id);
    if (savePoint) {
      // Restore state but keep the savePoints array
      const restoredState = {
        ...savePoint.state,
        savePoints: pipelineState.savePoints
      };
      setPipelineState(restoredState as DataPipelineState);
      historyManager.push('load_savepoint', restoredState);
    }
  }, [pipelineState, historyManager]);

  const deleteSavePoint = useCallback((id: string) => {
    setPipelineState(prev => {
      const newState = {
        ...prev,
        savePoints: prev.savePoints.filter(sp => sp.id !== id)
      };
      
      // Add to history manager for undo/redo
      historyManager.push('state_update', newState);
      
      // Save to session storage
      saveSession({ pipelineState: newState });
      
      // Notify parent component if callback exists
      if (onDataChange) {
        onDataChange({ savePoints: newState.savePoints });
      }
      
      return newState;
    });
  }, [historyManager, onDataChange]);

  // Filter management
  const updateFilters = useCallback((newFilters: Record<string, any>) => {
    setPipelineState(prev => {
      const updatedFilters = { ...prev.filters, ...newFilters };
      const newState = {
        ...prev,
        filters: updatedFilters,
        context: {
          ...prev.context,
          activeFilters: updatedFilters
        }
      };
      
      // Add to history manager for undo/redo
      historyManager.push('state_update', newState);
      
      // Save to session storage
      saveSession({ pipelineState: newState });
      
      // Notify parent component if callback exists
      if (onDataChange) {
        onDataChange({ filters: updatedFilters });
      }
      
      return newState;
    });
  }, [historyManager, onDataChange]);

  const clearFilters = useCallback((filterKeys?: string[]) => {
    setPipelineState(prev => {
      let updatedFilters: Record<string, any>;
      
      if (filterKeys) {
        updatedFilters = { ...prev.filters };
        filterKeys.forEach(key => delete updatedFilters[key]);
      } else {
        updatedFilters = {};
      }
      
      const newState = {
        ...prev,
        filters: updatedFilters,
        context: {
          ...prev.context,
          activeFilters: updatedFilters
        }
      };
      
      // Add to history manager for undo/redo
      historyManager.push('state_update', newState);
      
      // Save to session storage
      saveSession({ pipelineState: newState });
      
      // Notify parent component if callback exists
      if (onDataChange) {
        onDataChange({ filters: updatedFilters });
      }
      
      return newState;
    });
  }, [historyManager, onDataChange]);

  // Context management
  const updateContext = useCallback((contextUpdates: Partial<DataPipelineState['context']>) => {
    setPipelineState(prev => {
      const newState = {
        ...prev,
        context: {
          ...prev.context,
          ...contextUpdates
        }
      };
      
      // Add to history manager for undo/redo
      historyManager.push('state_update', newState);
      
      // Save to session storage
      saveSession({ pipelineState: newState });
      
      // Notify parent component if callback exists
      if (onDataChange) {
        onDataChange({ context: newState.context });
      }
      
      return newState;
    });
  }, [historyManager, onDataChange]);

  const addToBreadcrumb = useCallback((stage: string, label: string, dataSummary?: any) => {
    const breadcrumbEntry = {
      stage,
      label,
      timestamp: new Date(),
      dataSummary
    };
    
    updateContext({
      breadcrumbPath: [...pipelineState.context.breadcrumbPath, breadcrumbEntry]
    });
  }, [pipelineState, updateContext]);

  // Navigation and reset
  const resetPipeline = useCallback(() => {
    const resetState = createDefaultState();
    setPipelineState(resetState);
    historyManager.push('reset_pipeline', resetState);
    saveSession({ pipelineState: resetState });
  }, [historyManager]);

  const navigateToStage = useCallback((stage: DataPipelineState['currentStage']) => {
    updatePipelineState({ currentStage: stage });
  }, [updatePipelineState]);

  const undo = useCallback(() => {
    const previous = historyManager.undo();
    if (previous) {
      setPipelineState(previous.state);
    }
  }, [historyManager]);

  const redo = useCallback(() => {
    const next = historyManager.redo();
    if (next) {
      setPipelineState(next.state);
    }
  }, [historyManager]);

  // Performance optimization functions
  const enableCaching = useCallback((enabled: boolean) => {
    updatePipelineState({
      performance: {
        ...pipelineState.performance,
        cacheEnabled: enabled
      }
    });
  }, [pipelineState, updatePipelineState]);

  const enableVirtualization = useCallback((enabled: boolean) => {
    updatePipelineState({
      performance: {
        ...pipelineState.performance,
        virtualizationEnabled: enabled
      }
    });
  }, [pipelineState, updatePipelineState]);

  const updateWorkerPoolSize = useCallback((size: number) => {
    updatePipelineState({
      performance: {
        ...pipelineState.performance,
        workerPoolSize: Math.max(1, Math.min(10, size)) // Limit between 1-10 workers
      }
    });
  }, [pipelineState, updatePipelineState]);

  const getCacheStats = useCallback(() => {
    const stats = dataCache.getStats();
    return {
      hitRate: stats.hitRate,
      missRate: 1 - stats.hitRate,
      cacheSize: stats.size
    };
  }, []);

  const clearCache = useCallback(() => {
    dataCache.clear();
    processedDataCache.clear();
    persistentCache.clear();
    multiLevelCache.clear();
    
    // Update cache stats
    updatePipelineState({
      performance: {
        ...pipelineState.performance,
        cacheStats: {
          hitRate: 0,
          missRate: 0,
          cacheSize: 0
        }
      }
    });
  }, [pipelineState, updatePipelineState, multiLevelCache]);

  // State validation
  const validateState = useCallback((): { valid: boolean; errors?: ValidationError[] } => {
    return validateDataPipelineState(pipelineState);
  }, [pipelineState]);

  const validatePartialState = useCallback((partialState: Partial<DataPipelineState>): { valid: boolean; errors?: ValidationError[] } => {
    return validatePartialState(partialState);
  }, []);

  // Optimistic updates
  const applyOptimisticUpdate = useCallback(async (action: string, updates: Partial<DataPipelineState>): Promise<string> => {
    // Create rollback state
    const rollbackState = { ...pipelineState };
    
    // Apply updates optimistically
    await updatePipelineState({ 
      ...updates,
      isOptimisticUpdatePending: true
    });
    
    // Apply optimistic update to manager
    const updateId = optimisticUpdateManager.applyOptimisticUpdate(
      action,
      { ...pipelineState, ...updates, isOptimisticUpdatePending: true },
      rollbackState
    );
    
    // Update state with the update ID
    updatePipelineState({
      lastOptimisticUpdateId: updateId
    } as Partial<DataPipelineState>);
    
    return updateId;
  }, [pipelineState, updatePipelineState, optimisticUpdateManager]);

  const confirmOptimisticUpdate = useCallback((id: string): boolean => {
    const result = optimisticUpdateManager.confirmUpdate(id);
    if (result) {
      updatePipelineState({
        isOptimisticUpdatePending: false,
        lastOptimisticUpdateId: undefined
      } as Partial<DataPipelineState>);
    }
    return result;
  }, [optimisticUpdateManager, updatePipelineState]);

  const rollbackOptimisticUpdate = useCallback((id: string, error?: string): boolean => {
    const result = optimisticUpdateManager.handleUpdateFailure(id, error);
    if (result) {
      updatePipelineState({
        isOptimisticUpdatePending: false,
        lastOptimisticUpdateId: undefined
      } as Partial<DataPipelineState>);
    }
    return result;
  }, [optimisticUpdateManager, updatePipelineState]);

  const getPendingUpdates = useCallback((): Array<{ id: string; action: string; timestamp: number; status: string }> => {
    return optimisticUpdateManager.getPendingUpdates();
  }, [optimisticUpdateManager]);

  const getFailedUpdates = useCallback((): Array<{ id: string; action: string; timestamp: number; status: string; error?: string }> => {
    return optimisticUpdateManager.getFailedUpdates().map(update => ({
      id: update.id,
      action: update.action,
      timestamp: update.timestamp,
      status: update.status,
      error: update.error
    }));
  }, [optimisticUpdateManager]);

  const clearCompletedOptimisticUpdates = useCallback((): number => {
    return optimisticUpdateManager.clearCompletedUpdates();
  }, [optimisticUpdateManager]);

  // Cache management
  const getCache = useCallback((key: string): any => {
    return multiLevelCache.get(key);
  }, [multiLevelCache]);

  const setCache = useCallback((key: string, data: any, options?: { ttl?: number; tags?: string[] }): void => {
    multiLevelCache.set(key, data, options);
  }, [multiLevelCache]);

  const invalidateCacheByTag = useCallback((tag: string): number => {
    return multiLevelCache.invalidateByTag(tag);
  }, [multiLevelCache]);

  // Performance monitoring
  const getPerformanceStats = useCallback((): Record<string, any> => {
    return performanceMonitor.getStats();
  }, [performanceMonitor]);

  const measurePerformance = useCallback(<T,>(operation: string, fn: () => T): T => {
    return performanceMonitor.measure(operation, fn);
  }, [performanceMonitor]);

  // Debugging and snapshots
  const getSnapshot = useCallback((): string => {
    // Return a JSON string representation of the current state
    return JSON.stringify(pipelineState);
  }, [pipelineState]);

  const restoreSnapshot = useCallback((id: string): boolean => {
    // This would normally restore from a snapshot manager
    return true;
  }, []);

  const logStateChange = useCallback((action: string, path: string[], oldValue: any, newValue: any): void => {
    // This would normally log to a state change logger
  }, []);

  return (
    <DataContext.Provider value={{ 
      pipelineState, 
      updatePipelineState, 
      resetPipeline,
      navigateToStage,
      updateContext,
      addToBreadcrumb,
      createSavePoint,
      loadSavePoint,
      deleteSavePoint,
      updateFilters,
      clearFilters,
      historyManager,
      canUndo,
      canRedo,
      undo,
      redo,
      enableCaching,
      enableVirtualization,
      updateWorkerPoolSize,
      getCacheStats,
      clearCache,
      validateState,
      validatePartialState,
      applyOptimisticUpdate,
      confirmOptimisticUpdate,
      rollbackOptimisticUpdate,
      getPendingUpdates,
      getFailedUpdates,
      clearCompletedOptimisticUpdates,
      getCache,
      setCache,
      invalidateCacheByTag,
      getPerformanceStats,
      measurePerformance,
      getSnapshot,
      restoreSnapshot,
      logStateChange
    }}>
      {children}
    </DataContext.Provider>
  );
};

export const useUnifiedDataCenter = () => {
  const context = useContext(DataContext);
  if (context === undefined) {
    throw new Error('useUnifiedDataCenter must be used within a UnifiedDataCenterProvider');
  }
  return context;
};

// Export the legacy provider for backward compatibility
export const DataCenterProvider = UnifiedDataCenterProvider;
export const useDataCenter = useUnifiedDataCenter;