{"name": "data-center-optimization-scripts", "version": "1.0.0", "description": "Bundle optimization and performance analysis scripts for data-center component", "scripts": {"optimize": "ts-node optimize-bundle.ts", "analyze": "webpack --config ../config/webpack.analyzer.js", "build:optimized": "webpack --config ../config/webpack.optimized.js", "performance:monitor": "ts-node performance-monitor.ts", "bundle:report": "npm run analyze && npm run optimize"}, "dependencies": {"@types/node": "^18.0.0", "ts-node": "^10.9.0", "typescript": "^5.0.0", "webpack": "^5.88.0", "webpack-bundle-analyzer": "^4.9.0", "webpack-cli": "^5.1.0"}}