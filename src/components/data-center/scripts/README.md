# Data Center Optimization Scripts

This directory contains scripts for analyzing and optimizing the performance of the data-center component library.

## Scripts Overview

### 1. Bundle Optimizer (`optimize-bundle.ts`)

Analyzes bundle size, identifies unused code, and provides optimization recommendations.

**Features:**
- Bundle size analysis
- Tree shaking report generation
- Optimization recommendations
- Automated optimization application
- HTML and JSON report generation

**Usage:**
```bash
# Run bundle optimization
npm run optimize

# Or run directly with ts-node
ts-node optimize-bundle.ts
```

**Output:**
- `dist/optimization/optimization-report.html` - Visual report
- `dist/optimization/optimization-report.json` - Raw data
- `dist/optimization/code-splitting-suggestions.json` - Code splitting recommendations
- `dist/optimization/dependency-analysis.json` - Dependency optimization suggestions

### 2. Performance Monitor (`performance-monitor.ts`)

Monitors runtime performance metrics and generates comprehensive reports.

**Features:**
- Component render time tracking
- Memory usage monitoring
- API response time analysis
- Performance recommendations
- Multiple report formats (HTML, JSON, CSV)

**Usage:**
```bash
# Monitor for 60 seconds (default)
npm run performance:monitor

# Monitor for custom duration (in seconds)
ts-node performance-monitor.ts 120

# Stop monitoring with Ctrl+C
```

**Output:**
- `dist/performance/performance-report.html` - Visual dashboard
- `dist/performance/performance-report.json` - Raw metrics data
- `dist/performance/performance-metrics.csv` - CSV export for analysis

### 3. Webpack Analyzer (`../config/webpack.analyzer.js`)

Webpack configuration for bundle analysis and optimization.

**Usage:**
```bash
# Generate bundle analysis
npm run analyze

# Build optimized bundle
npm run build:optimized

# Run full analysis and optimization
npm run bundle:report
```

## Installation

1. Install dependencies:
```bash
cd scripts
npm install
```

2. Ensure TypeScript is configured:
```bash
# If tsconfig.json doesn't exist in scripts directory
echo '{
  "extends": "../../../tsconfig.json",
  "compilerOptions": {
    "module": "commonjs",
    "target": "es2020",
    "moduleResolution": "node",
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true
  },
  "include": ["*.ts"]
}' > tsconfig.json
```

## Available NPM Scripts

```json
{
  "optimize": "ts-node optimize-bundle.ts",
  "analyze": "webpack --config ../config/webpack.analyzer.js",
  "build:optimized": "webpack --config ../config/webpack.optimized.js",
  "performance:monitor": "ts-node performance-monitor.ts",
  "bundle:report": "npm run analyze && npm run optimize"
}
```

## Understanding the Reports

### Bundle Optimization Report

**Bundle Size Analysis:**
- Total bundle size before/after optimization
- Gzipped size comparison
- Size reduction percentage

**Tree Shaking Report:**
- Number of unused exports detected
- Side effects analysis
- Optimization recommendations

**Optimization Plan:**
- Priority level (high/medium/low)
- Specific actions to take
- Estimated impact

### Performance Monitoring Report

**Performance Metrics:**
- API response times (avg, min, max)
- Data processing times
- UI update times
- Request counts

**Component Performance:**
- Render times per component
- Re-render frequency
- Total rendering time

**Memory Usage:**
- Current memory consumption
- Peak usage during monitoring
- Average usage over time

**Recommendations:**
- Specific optimization suggestions
- Performance bottleneck identification
- Best practice recommendations

## Integration with CI/CD

Add these scripts to your CI/CD pipeline:

```yaml
# GitHub Actions example
- name: Run Bundle Analysis
  run: |
    cd src/components/data-center/scripts
    npm install
    npm run bundle:report
    
- name: Upload Reports
  uses: actions/upload-artifact@v3
  with:
    name: optimization-reports
    path: |
      src/components/data-center/dist/optimization/
      src/components/data-center/dist/performance/
```

## Customization

### Adding Custom Metrics

Extend the performance monitor to track custom metrics:

```typescript
// In your component
import { performanceMonitor } from '../utils/performanceUtils';

// Record custom metric
performanceMonitor.recordMetric('custom_operation_time', operationTime);
```

### Custom Optimization Rules

Modify the optimization recommendations in `optimize-bundle.ts`:

```typescript
// Add custom optimization logic
private generateCustomRecommendations(bundleAnalysis: BundleAnalysis): string[] {
  const recommendations: string[] = [];
  
  // Your custom logic here
  if (bundleAnalysis.totalSize > 1024 * 1024) { // 1MB
    recommendations.push('Bundle size exceeds 1MB - consider aggressive code splitting');
  }
  
  return recommendations;
}
```

## Troubleshooting

### Common Issues

1. **TypeScript compilation errors:**
   - Ensure `ts-node` is installed
   - Check `tsconfig.json` configuration
   - Verify import paths

2. **Webpack build failures:**
   - Check webpack configuration
   - Ensure all dependencies are installed
   - Verify entry points exist

3. **Permission errors:**
   - Ensure write permissions for output directories
   - Check file system permissions

4. **Memory issues during monitoring:**
   - Reduce monitoring duration
   - Increase Node.js memory limit: `node --max-old-space-size=4096`

### Debug Mode

Enable debug logging:

```bash
# Set debug environment variable
DEBUG=data-center:* npm run optimize
```

## Contributing

When adding new optimization scripts:

1. Follow the existing code structure
2. Add comprehensive error handling
3. Include progress logging
4. Generate both JSON and HTML reports
5. Update this README with usage instructions
6. Add tests in the `__tests__` directory

## Performance Benchmarks

### Target Metrics

- Bundle size: < 500KB (gzipped < 150KB)
- Component render time: < 100ms
- Memory usage: < 50MB
- API response time: < 300ms
- Tree shaking efficiency: > 80%

### Monitoring Schedule

- Run bundle analysis on every PR
- Performance monitoring in staging environment
- Weekly optimization reports
- Monthly performance trend analysis