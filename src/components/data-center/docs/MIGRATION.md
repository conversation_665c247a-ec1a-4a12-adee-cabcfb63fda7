# Data Center Migration Guide

This guide helps you migrate from the legacy Data Center implementation to the new modular architecture.

## Overview

The new Data Center architecture provides:
- **Modular Design**: Smaller, focused components
- **Better Performance**: Code splitting and lazy loading
- **Type Safety**: Enhanced TypeScript support
- **Configuration System**: Preset-based configuration
- **Developer Experience**: Better tooling and documentation

## Migration Steps

### 1. Update Imports

#### Before (Legacy)
```typescript
import { UnifiedDataCenter } from './components/UnifiedDataCenter';
import { UnifiedDataCenterProvider } from './components/UnifiedDataContext';
```

#### After (New)
```typescript
import { DataCenterUnified, DataCenterProvider } from './components/data-center';
// Or use the new modular approach
import { DataCenter } from './components/data-center/core';
import { DataCenterConfigBuilder } from './components/data-center/config';
```

### 2. Configuration Migration

#### Legacy Configuration
```typescript
const legacyConfig = {
  theme: 'dark',
  enableVirtualization: true,
  showPreview: true,
  enableSQL: true,
  cacheSize: 100,
  // ... many other options
};
```

#### New Configuration (Preset-based)
```typescript
import { DataCenterConfigBuilder } from './components/data-center/config';

// Option 1: Use presets
const config = new DataCenterConfigBuilder()
  .preset('analytics') // Includes SQL, preview, etc.
  .theme('dark')
  .performance({
    virtualizeGrids: true,
    cacheSize: 100
  })
  .build();

// Option 2: Migrate automatically
import { ConfigMigrator } from './components/data-center/config/migration';
const migratedConfig = ConfigMigrator.migrateFromLegacy(legacyConfig);
```

### 3. Component Usage Migration

#### Legacy Usage
```typescript
function App() {
  return (
    <UnifiedDataCenterProvider>
      <UnifiedDataCenter 
        config={legacyConfig}
        data={files}
        onFileSelect={handleFileSelect}
      />
    </UnifiedDataCenterProvider>
  );
}
```

#### New Usage (Backward Compatible)
```typescript
function App() {
  return (
    <DataCenterProvider>
      <DataCenterUnified 
        config={migratedConfig}
        initialData={files}
        onDataChange={handleDataChange}
      />
    </DataCenterProvider>
  );
}
```

#### New Usage (Modular)
```typescript
import { 
  DataCenter, 
  DataPreview, 
  SQLGenerator 
} from './components/data-center';

function App() {
  const config = new DataCenterConfigBuilder()
    .preset('analytics')
    .build();

  return (
    <DataCenterProvider config={config}>
      <div className="app-layout">
        <DataCenter initialData={files} />
        <DataPreview />
        <SQLGenerator />
      </div>
    </DataCenterProvider>
  );
}
```

### 4. Hook Migration

#### Legacy Hooks
```typescript
import { useUnifiedDataContext } from './UnifiedDataContext';

function MyComponent() {
  const { state, actions } = useUnifiedDataContext();
  // ...
}
```

#### New Hooks
```typescript
import { useDataCenter } from './components/data-center/hooks';

function MyComponent() {
  const { state, dispatch } = useDataCenter();
  // ...
}
```

### 5. Event Handler Migration

#### Legacy Events
```typescript
<UnifiedDataCenter 
  onFileSelect={(file) => console.log('Selected:', file)}
  onPreviewChange={(preview) => console.log('Preview:', preview)}
  onSQLGenerate={(sql) => console.log('SQL:', sql)}
/>
```

#### New Events
```typescript
<DataCenterUnified 
  onDataChange={(data) => console.log('Data changed:', data)}
  onStateChange={(state) => console.log('State:', state)}
  onError={(error) => console.error('Error:', error)}
/>
```

## Automated Migration

### Using the Migration Utility

```typescript
import { ConfigMigrator } from './components/data-center/config/migration';

// 1. Analyze your current configuration
const analysis = ConfigMigrator.analyzeConfig(legacyConfig);
console.log('Migration analysis:', analysis);

// 2. Get preset suggestions
const suggestion = ConfigMigrator.suggestPreset(legacyConfig);
console.log(`Recommended preset: ${suggestion.preset}`);
console.log(`Confidence: ${suggestion.confidence}`);
console.log(`Reasons: ${suggestion.reasons.join(', ')}`);

// 3. Migrate configuration
const newConfig = ConfigMigrator.migrateConfig(legacyConfig);

// 4. Generate migration guide
const guide = ConfigMigrator.generateMigrationGuide(legacyConfig, newConfig);
console.log(guide);
```

### Migration Script

Create a migration script to automate the process:

```typescript
// scripts/migrate-data-center.ts
import fs from 'fs';
import path from 'path';
import { ConfigMigrator } from '../src/components/data-center/config/migration';

interface MigrationOptions {
  sourceDir: string;
  outputDir: string;
  dryRun?: boolean;
}

class DataCenterMigrator {
  async migrateProject(options: MigrationOptions) {
    const { sourceDir, outputDir, dryRun = false } = options;
    
    // Find all files using legacy components
    const files = this.findLegacyUsage(sourceDir);
    
    for (const file of files) {
      const content = fs.readFileSync(file, 'utf-8');
      const migrated = this.migrateFileContent(content);
      
      if (!dryRun) {
        const outputPath = path.join(outputDir, path.relative(sourceDir, file));
        fs.writeFileSync(outputPath, migrated);
      } else {
        console.log(`Would migrate: ${file}`);
      }
    }
  }
  
  private findLegacyUsage(dir: string): string[] {
    // Implementation to find files with legacy imports
    return [];
  }
  
  private migrateFileContent(content: string): string {
    // Implementation to migrate file content
    return content
      .replace(/UnifiedDataCenter/g, 'DataCenterUnified')
      .replace(/UnifiedDataCenterProvider/g, 'DataCenterProvider')
      .replace(/useUnifiedDataContext/g, 'useDataCenter');
  }
}

// Usage
const migrator = new DataCenterMigrator();
migrator.migrateProject({
  sourceDir: './src',
  outputDir: './src-migrated',
  dryRun: true
});
```

## Configuration Mapping

### Legacy to New Configuration

| Legacy Property | New Configuration | Notes |
|----------------|-------------------|-------|
| `theme` | `theme` | Direct mapping |
| `enableVirtualization` | `performance.virtualizeGrids` | Moved to performance section |
| `showPreview` | `enabledFeatures: ['dataPreview']` | Now feature-based |
| `enableSQL` | `enabledFeatures: ['sqlGenerator']` | Now feature-based |
| `cacheSize` | `performance.cacheSize` | Moved to performance section |
| `lazyLoad` | `performance.lazyLoadWidgets` | Moved to performance section |
| `accessibility` | `accessibility.*` | Expanded options |

### Feature Mapping

| Legacy Feature | New Feature | Preset |
|---------------|-------------|--------|
| Basic file browsing | `['fileExplorer']` | `minimal` |
| Data preview | `['fileExplorer', 'dataPreview']` | `default` |
| SQL generation | `['dataPreview', 'sqlGenerator']` | `analytics` |
| Full analytics | All features | `enterprise` |

## Breaking Changes

### 1. Event Handler Signatures

```typescript
// Legacy
onFileSelect: (file: FileData) => void

// New
onDataChange: (data: FileData[]) => void
```

### 2. State Structure

```typescript
// Legacy state
interface LegacyState {
  selectedFile: FileData | null;
  previewData: any;
  sqlQuery: string;
}

// New state
interface DataCenterState {
  files: FileData[];
  selectedFiles: string[];
  preview: {
    data: any;
    metadata: PreviewMetadata;
  };
  sql: {
    query: string;
    result: SQLResult;
  };
}
```

### 3. Configuration Structure

```typescript
// Legacy
interface LegacyConfig {
  theme: string;
  enableVirtualization: boolean;
  // ... flat structure
}

// New
interface DataCenterConfig {
  enabledFeatures: string[];
  theme: 'light' | 'dark' | 'auto';
  performance: {
    virtualizeGrids: boolean;
    lazyLoadWidgets: boolean;
    cacheSize: number;
  };
  accessibility: {
    enabled: boolean;
    highContrast: boolean;
    screenReader: boolean;
    keyboardNavigation: boolean;
  };
}
```

## Performance Improvements

### Code Splitting

The new architecture automatically splits code:

```typescript
// Features are loaded on demand
const SQLGenerator = React.lazy(() => import('./features/SQLGenerator'));
const DataVisualization = React.lazy(() => import('./features/DataVisualization'));
```

### Bundle Size Reduction

- **Legacy bundle**: ~2.5MB
- **New minimal preset**: ~800KB
- **New default preset**: ~1.2MB
- **New enterprise preset**: ~2.0MB

### Memory Usage

- Improved garbage collection
- Better state management
- Optimized re-renders

## Testing Migration

### Update Test Files

```typescript
// Legacy tests
import { render } from '@testing-library/react';
import { UnifiedDataCenter } from '../UnifiedDataCenter';

// New tests
import { render } from '@testing-library/react';
import { DataCenterUnified, DataCenterProvider } from '../data-center';

const renderWithProvider = (component: React.ReactElement) => {
  return render(
    <DataCenterProvider>
      {component}
    </DataCenterProvider>
  );
};
```

## Rollback Plan

If you need to rollback:

1. **Keep legacy components**: The old components are still available
2. **Gradual migration**: Migrate one component at a time
3. **Feature flags**: Use feature flags to toggle between old and new

```typescript
const USE_NEW_DATA_CENTER = process.env.REACT_APP_USE_NEW_DATA_CENTER === 'true';

function App() {
  return USE_NEW_DATA_CENTER ? (
    <DataCenterUnified config={newConfig} />
  ) : (
    <UnifiedDataCenter config={legacyConfig} />
  );
}
```

## Common Issues and Solutions

### Issue 1: Type Errors

**Problem**: TypeScript errors after migration

**Solution**: Update type imports
```typescript
// Old
import { FileData } from './types';

// New
import { FileData } from './components/data-center/types';
```

### Issue 2: Missing Features

**Problem**: Some features not working after migration

**Solution**: Check enabled features
```typescript
const config = new DataCenterConfigBuilder()
  .preset('enterprise') // Includes all features
  .build();
```

### Issue 3: Performance Regression

**Problem**: Slower performance after migration

**Solution**: Enable performance optimizations
```typescript
const config = new DataCenterConfigBuilder()
  .preset('default')
  .performance({
    virtualizeGrids: true,
    lazyLoadWidgets: true,
    cacheSize: 200
  })
  .build();
```

### Issue 4: Styling Issues

**Problem**: Components look different

**Solution**: Check theme configuration
```typescript
const config = new DataCenterConfigBuilder()
  .preset('default')
  .theme('light') // or 'dark', 'auto'
  .build();
```

## Support and Resources

- **Documentation**: See `API.md` and `TYPES.md`
- **Examples**: Check `examples.ts` in the config directory
- **Component Showcase**: Use the interactive showcase for testing
- **Migration Tool**: Use the automated migration utility

## Timeline

### Phase 1: Preparation (Week 1)
- Review this migration guide
- Analyze current usage
- Plan migration strategy

### Phase 2: Migration (Week 2-3)
- Migrate configuration
- Update imports and usage
- Test functionality

### Phase 3: Optimization (Week 4)
- Performance tuning
- Feature optimization
- Final testing

### Phase 4: Cleanup (Week 5)
- Remove legacy code
- Update documentation
- Team training

## Checklist

- [ ] Analyzed current Data Center usage
- [ ] Reviewed new architecture documentation
- [ ] Created migration plan
- [ ] Updated imports and configuration
- [ ] Migrated component usage
- [ ] Updated event handlers
- [ ] Migrated custom hooks
- [ ] Updated tests
- [ ] Performance testing
- [ ] User acceptance testing
- [ ] Documentation updates
- [ ] Team training completed
- [ ] Legacy code cleanup

---

*For additional support, refer to the development guide or create an issue in the project repository.*