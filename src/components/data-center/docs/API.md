# Data Center Component Library - API Documentation

Comprehensive API documentation for the Data Center Component Library, including all components, hooks, utilities, and configuration options.

## Table of Contents

- [Quick Start](#quick-start)
- [Core Components](#core-components)
- [Configuration API](#configuration-api)
- [Hooks API](#hooks-api)
- [Utilities API](#utilities-api)
- [Type Definitions](#type-definitions)
- [Error Handling](#error-handling)
- [Performance Optimization](#performance-optimization)
- [Migration Guide](#migration-guide)
- [Examples](#examples)

## Quick Start

### Installation

```bash
npm install @sanity/data-center
# or
yarn add @sanity/data-center
```

### Basic Usage

```typescript
import { DataCenter, DataCenterConfigBuilder } from '@sanity/data-center';

// Create configuration
const config = new DataCenterConfigBuilder()
  .preset('standard')
  .theme('dark')
  .build();

// Use component
function App() {
  return (
    <DataCenter
      config={config}
      onDataChange={(data) => console.log('Data changed:', data)}
      onError={(error) => console.error('Error:', error)}
    />
  );
}
```

## Core Components

### DataCenter

The main component that provides the complete data center interface.

#### Props

```typescript
interface DataCenterProps {
  config: DataCenterConfig;
  initialData?: ReadonlyArray<DataFile>;
  onDataChange?: (data: ReadonlyArray<DataFile>) => void;
  onError?: (error: DataCenterError) => void;
  onConfigChange?: (config: DataCenterConfig) => void;
  className?: string;
  style?: React.CSSProperties;
  testId?: string;
}
```

#### Example

```typescript
import { DataCenter, DataCenterConfigBuilder } from '@sanity/data-center';

const config = new DataCenterConfigBuilder()
  .preset('analytics')
  .features(['fileUpload', 'dataVisualization', 'machineLearning'])
  .theme('auto')
  .performance({ cacheSize: 200, virtualizeGrids: true })
  .build();

function MyDataCenter() {
  const [data, setData] = useState<DataFile[]>([]);
  const [error, setError] = useState<DataCenterError | null>(null);

  return (
    <DataCenter
      config={config}
      initialData={data}
      onDataChange={setData}
      onError={setError}
      className="my-data-center"
      testId="data-center-main"
    />
  );
}
```

### DataPreview

Component for previewing data files with various formats.

#### Props

```typescript
interface DataPreviewProps {
  file: DataFile;
  maxRows?: number;
  maxColumns?: number;
  showStatistics?: boolean;
  onRowSelect?: (rowIndex: number) => void;
  onColumnSelect?: (columnName: string) => void;
  className?: string;
}
```

#### Example

```typescript
import { DataPreview } from '@sanity/data-center';

function FilePreview({ file }: { file: DataFile }) {
  return (
    <DataPreview
      file={file}
      maxRows={100}
      maxColumns={20}
      showStatistics
      onRowSelect={(index) => console.log('Selected row:', index)}
      onColumnSelect={(column) => console.log('Selected column:', column)}
    />
  );
}
```

### DataVisualization

Component for creating various data visualizations.

#### Props

```typescript
interface DataVisualizationProps {
  data: DataContent;
  chartType: ChartType;
  config: VisualizationConfig;
  onChartClick?: (dataPoint: DataPoint) => void;
  onConfigChange?: (config: VisualizationConfig) => void;
  className?: string;
}
```

#### Example

```typescript
import { DataVisualization } from '@sanity/data-center';

function ChartView({ data }: { data: DataContent }) {
  const config = {
    xAxis: 'date',
    yAxis: 'value',
    title: 'Sales Over Time',
    colors: ['#3b82f6', '#ef4444', '#10b981']
  };

  return (
    <DataVisualization
      data={data}
      chartType="line"
      config={config}
      onChartClick={(point) => console.log('Clicked:', point)}
    />
  );
}
```

### SQLGenerator

Component for generating and executing SQL queries.

#### Props

```typescript
interface SQLGeneratorProps {
  schema: DataSchema;
  onQueryGenerate?: (query: string) => void;
  onQueryExecute?: (query: string, results: QueryResults) => void;
  initialQuery?: string;
  readOnly?: boolean;
  className?: string;
}
```

#### Example

```typescript
import { SQLGenerator } from '@sanity/data-center';

function QueryBuilder({ schema }: { schema: DataSchema }) {
  return (
    <SQLGenerator
      schema={schema}
      onQueryGenerate={(query) => console.log('Generated:', query)}
      onQueryExecute={(query, results) => {
        console.log('Executed:', query);
        console.log('Results:', results);
      }}
      initialQuery="SELECT * FROM users WHERE active = true"
    />
  );
}
```

## Configuration API

### DataCenterConfigBuilder

Fluent API for building data center configurations.

#### Methods

```typescript
class DataCenterConfigBuilder {
  // Preset configuration
  preset(presetId: 'minimal' | 'standard' | 'analytics' | 'enterprise' | 'developer'): ConfigBuilder;
  
  // Feature configuration
  features(features: DataCenterFeature[]): ConfigBuilder;
  addFeature(feature: DataCenterFeature): ConfigBuilder;
  removeFeature(feature: DataCenterFeature): ConfigBuilder;
  featureGroups(groupIds: string[]): ConfigBuilder;
  
  // UI configuration
  theme(theme: ThemeMode): ConfigBuilder;
  accessibility(options: Partial<AccessibilityConfig>): ConfigBuilder;
  
  // Performance configuration
  performance(options: Partial<PerformanceConfig>): ConfigBuilder;
  
  // Build and validation
  build(): DataCenterConfig;
  validate(): ValidationResult;
  reset(): ConfigBuilder;
  
  // Utility methods
  getPreset(): ConfigPreset | undefined;
  getConfig(): Partial<DataCenterConfig>;
  hasFeature(feature: DataCenterFeature): boolean;
  getEnabledFeatures(): DataCenterFeature[];
  clone(): DataCenterConfigBuilder;
}
```

#### Examples

```typescript
// Basic configuration
const basicConfig = new DataCenterConfigBuilder()
  .preset('minimal')
  .theme('light')
  .build();

// Advanced configuration
const advancedConfig = new DataCenterConfigBuilder()
  .preset('analytics')
  .addFeature('machineLearning')
  .removeFeature('pythonScripting')
  .theme('dark')
  .accessibility({
    highContrast: true,
    screenReader: true,
    fontSize: 'large'
  })
  .performance({
    virtualizeGrids: true,
    cacheSize: 500,
    maxConcurrentRequests: 10
  })
  .build();

// Validation
const builder = new DataCenterConfigBuilder().preset('standard');
const validation = builder.validate();
if (!validation.valid) {
  console.error('Configuration errors:', validation.errors);
}
```

### PresetManager

Utility class for managing configuration presets.

#### Methods

```typescript
class PresetManager {
  static getPreset(presetId: string): ConfigPreset | null;
  static getAllPresets(): ConfigPreset[];
  static getPresetsByComplexity(complexity: 'beginner' | 'intermediate' | 'advanced'): ConfigPreset[];
  static getRecommendedPreset(requirements: Requirements): ConfigPreset;
  static createCustomPreset(
    basePresetId: string,
    customizations: Partial<DataCenterConfig>,
    metadata: PresetMetadata
  ): ConfigPreset | null;
}
```

#### Examples

```typescript
// Get all available presets
const presets = PresetManager.getAllPresets();
console.log('Available presets:', presets.map(p => p.id));

// Get preset by complexity
const beginnerPresets = PresetManager.getPresetsByComplexity('beginner');

// Get recommended preset
const requirements = {
  features: ['dataVisualization', 'sqlGeneration'],
  performance: 'high',
  complexity: 'intermediate'
};
const recommended = PresetManager.getRecommendedPreset(requirements);

// Create custom preset
const customPreset = PresetManager.createCustomPreset(
  'standard',
  { theme: { mode: 'dark' } },
  { name: 'Dark Standard', description: 'Standard preset with dark theme' }
);
```

## Hooks API

### useDataCenter

Main hook for managing data center state and operations.

#### Signature

```typescript
function useDataCenter(config: DataCenterConfig): UseDataCenterReturn;

interface UseDataCenterReturn {
  config: DataCenterConfig;
  data: ReadonlyArray<DataFile>;
  loading: boolean;
  error: DataCenterError | null;
  updateConfig: (config: Partial<DataCenterConfig>) => void;
  addData: (file: DataFile) => void;
  removeData: (id: FileId) => void;
  clearData: () => void;
  exportData: (format: DataFormat) => Promise<Blob>;
}
```

#### Example

```typescript
import { useDataCenter } from '@sanity/data-center';

function DataCenterContainer() {
  const config = new DataCenterConfigBuilder().preset('standard').build();
  const {
    data,
    loading,
    error,
    addData,
    removeData,
    exportData
  } = useDataCenter(config);

  const handleFileUpload = async (file: File) => {
    try {
      const dataFile = await processFile(file);
      addData(dataFile);
    } catch (err) {
      console.error('Failed to process file:', err);
    }
  };

  const handleExport = async () => {
    const blob = await exportData('csv');
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'data-export.csv';
    a.click();
  };

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <div>
      <input type="file" onChange={(e) => handleFileUpload(e.target.files?.[0])} />
      <button onClick={handleExport}>Export Data</button>
      <div>Files: {data.length}</div>
    </div>
  );
}
```

### useDataProcessing

Hook for data processing operations.

#### Signature

```typescript
function useDataProcessing(): UseDataProcessingReturn;

interface UseDataProcessingReturn {
  processFile: (file: File) => Promise<DataFile>;
  validateData: (data: DataContent) => ValidationResult;
  transformData: <T>(data: DataContent, transformer: Transformer<DataContent, T>) => T;
  filterData: (data: DataContent, predicate: Predicate<ReadonlyArray<DataValue>>) => DataContent;
  sortData: (data: DataContent, column: string, direction: 'asc' | 'desc') => DataContent;
  groupData: (data: DataContent, column: string) => Record<string, DataContent>;
}
```

#### Example

```typescript
import { useDataProcessing } from '@sanity/data-center';

function DataProcessor({ data }: { data: DataContent }) {
  const {
    validateData,
    transformData,
    filterData,
    sortData
  } = useDataProcessing();

  // Validate data
  const validation = validateData(data);
  if (!validation.valid) {
    console.warn('Data validation issues:', validation.errors);
  }

  // Filter data
  const filteredData = filterData(data, (row) => 
    row[0] !== null && row[0] !== undefined
  );

  // Sort data
  const sortedData = sortData(filteredData, 'name', 'asc');

  // Transform data
  const summary = transformData(sortedData, (data) => ({
    rowCount: data.rows.length,
    columnCount: data.headers.length,
    nullCount: data.rows.flat().filter(v => v === null).length
  }));

  return (
    <div>
      <p>Rows: {summary.rowCount}</p>
      <p>Columns: {summary.columnCount}</p>
      <p>Null values: {summary.nullCount}</p>
    </div>
  );
}
```

### useDataVisualization

Hook for creating and managing data visualizations.

#### Signature

```typescript
function useDataVisualization(data: DataContent): UseDataVisualizationReturn;

interface UseDataVisualizationReturn {
  createChart: (type: ChartType, config: ChartConfig) => ChartInstance;
  updateChart: (chartId: string, config: Partial<ChartConfig>) => void;
  removeChart: (chartId: string) => void;
  exportChart: (chartId: string, format: 'png' | 'svg' | 'pdf') => Promise<Blob>;
  charts: ReadonlyArray<ChartInstance>;
}
```

#### Example

```typescript
import { useDataVisualization } from '@sanity/data-center';

function VisualizationPanel({ data }: { data: DataContent }) {
  const { createChart, charts, exportChart } = useDataVisualization(data);

  const handleCreateChart = () => {
    const config = {
      xAxis: 'date',
      yAxis: 'sales',
      title: 'Sales Trend'
    };
    createChart('line', config);
  };

  const handleExportChart = async (chartId: string) => {
    const blob = await exportChart(chartId, 'png');
    // Handle blob download
  };

  return (
    <div>
      <button onClick={handleCreateChart}>Create Line Chart</button>
      {charts.map(chart => (
        <div key={chart.id}>
          <h3>{chart.config.title}</h3>
          <button onClick={() => handleExportChart(chart.id)}>Export</button>
        </div>
      ))}
    </div>
  );
}
```

## Utilities API

### FileUtils

Utilities for file operations and validation.

#### Functions

```typescript
// File validation
function validateFileType(file: File, allowedTypes: DataFormat[]): boolean;
function isSupportedDataFile(file: File): boolean;
function getFileExtension(filename: string): string;

// File processing
function readFileAsText(file: File): Promise<string>;
function parseCSV(content: string, options?: CSVParseOptions): DataContent;
function parseJSON(content: string): DataContent;
function parseXML(content: string): DataContent;

// File utilities
function formatFileSize(bytes: number): string;
function generateFileId(): FileId;
function calculateChecksum(content: string): string;
```

#### Examples

```typescript
import { FileUtils } from '@sanity/data-center';

// Validate file type
const isValid = FileUtils.validateFileType(file, ['csv', 'json']);
if (!isValid) {
  throw new Error('Unsupported file type');
}

// Process CSV file
const content = await FileUtils.readFileAsText(file);
const data = FileUtils.parseCSV(content, {
  delimiter: ',',
  hasHeader: true,
  skipEmptyLines: true
});

// Format file size
const sizeText = FileUtils.formatFileSize(file.size); // "1.2 MB"
```

### DataUtils

Utilities for data manipulation and analysis.

#### Functions

```typescript
// Data validation
function validateDataContent(data: DataContent): ValidationResult;
function detectDataTypes(data: DataContent): DataType[];
function validateSchema(data: DataContent, schema: DataSchema): ValidationResult;

// Data transformation
function normalizeData(data: DataContent): DataContent;
function cleanData(data: DataContent, options?: CleanOptions): DataContent;
function aggregateData(data: DataContent, groupBy: string, aggregations: Aggregation[]): DataContent;

// Data analysis
function calculateStatistics(data: DataContent): DataStatistics;
function findOutliers(data: DataContent, column: string): number[];
function correlationMatrix(data: DataContent): number[][];
```

#### Examples

```typescript
import { DataUtils } from '@sanity/data-center';

// Validate data
const validation = DataUtils.validateDataContent(data);
if (!validation.valid) {
  console.error('Data validation failed:', validation.errors);
}

// Clean data
const cleanedData = DataUtils.cleanData(data, {
  removeNulls: true,
  removeDuplicates: true,
  trimStrings: true
});

// Calculate statistics
const stats = DataUtils.calculateStatistics(cleanedData);
console.log('Data statistics:', stats);
```

### ConfigUtils

Utilities for configuration management.

#### Functions

```typescript
// Configuration creation
function createBuilder(preset?: string): DataCenterConfigBuilder;
function fromPreset(presetId: string): DataCenterConfig;
function merge(base: DataCenterConfig, override: Partial<DataCenterConfig>): DataCenterConfig;

// Configuration analysis
function compare(config1: DataCenterConfig, config2: DataCenterConfig): ConfigComparison;
function validate(config: DataCenterConfig): ValidationResult;
function getRecommendations(config: DataCenterConfig): string[];

// Configuration utilities
function serialize(config: DataCenterConfig): string;
function deserialize(json: string): DataCenterConfig;
function clone(config: DataCenterConfig): DataCenterConfig;
```

#### Examples

```typescript
import { ConfigUtils } from '@sanity/data-center';

// Create configuration from preset
const config = ConfigUtils.fromPreset('analytics');

// Merge configurations
const customConfig = ConfigUtils.merge(config, {
  theme: { mode: 'dark' },
  performance: { cacheSize: 1000 }
});

// Compare configurations
const comparison = ConfigUtils.compare(config, customConfig);
console.log('Differences:', comparison.differences);

// Get recommendations
const recommendations = ConfigUtils.getRecommendations(customConfig);
console.log('Optimization suggestions:', recommendations);
```

## Type Definitions

### Core Types

```typescript
// Feature types
type DataCenterFeature = 
  | 'fileUpload'
  | 'dataPreview'
  | 'sqlGeneration'
  | 'pythonScripting'
  | 'dataVisualization'
  | 'dashboard'
  | 'machineLearning'
  | 'customWidgets'
  | 'collaboration'
  | 'security'
  | 'compliance'
  | 'audit';

// Theme types
type ThemeMode = 'light' | 'dark' | 'auto';
type LayoutMode = 'compact' | 'comfortable' | 'spacious';

// Data types
type DataFormat = 'csv' | 'json' | 'xml' | 'parquet' | 'excel';
type DataType = 'string' | 'number' | 'boolean' | 'date' | 'null';
type ChartType = 'bar' | 'line' | 'pie' | 'scatter' | 'heatmap';

// Branded types for type safety
type FileId = string & { readonly __brand: 'FileId' };
type UserId = string & { readonly __brand: 'UserId' };
type SessionId = string & { readonly __brand: 'SessionId' };
```

### Configuration Types

```typescript
interface DataCenterConfig {
  readonly id: string;
  readonly version: string;
  readonly features: ReadonlyArray<DataCenterFeature>;
  readonly theme: ThemeConfig;
  readonly performance: PerformanceConfig;
  readonly accessibility: AccessibilityConfig;
  readonly security: SecurityConfig;
  readonly ui: UIConfig;
  readonly data: DataConfig;
  readonly integrations: IntegrationConfig;
  readonly metadata: ConfigMetadata;
}

interface ThemeConfig {
  readonly mode: ThemeMode;
  readonly primaryColor: string;
  readonly secondaryColor: string;
  readonly accentColor: string;
  readonly customCSS?: string;
}

interface PerformanceConfig {
  readonly virtualizeGrids: boolean;
  readonly lazyLoadWidgets: boolean;
  readonly cacheSize: number;
  readonly debounceMs: number;
  readonly maxConcurrentRequests: number;
}
```

### Data Types

```typescript
interface DataFile {
  readonly id: FileId;
  readonly name: string;
  readonly size: number;
  readonly type: DataFormat;
  readonly lastModified: Date;
  readonly checksum: string;
  readonly metadata: FileMetadata;
  readonly content?: DataContent;
  readonly preview?: DataPreview;
}

interface DataContent {
  readonly headers: ReadonlyArray<string>;
  readonly rows: ReadonlyArray<ReadonlyArray<DataValue>>;
  readonly types: ReadonlyArray<DataType>;
  readonly statistics?: DataStatistics;
}

interface DataSchema {
  readonly columns: ReadonlyArray<ColumnSchema>;
  readonly primaryKey?: string;
  readonly foreignKeys: ReadonlyArray<ForeignKeySchema>;
  readonly indexes: ReadonlyArray<IndexSchema>;
}
```

## Error Handling

### Error Types

The library provides specific error types for different scenarios:

```typescript
// Base error class
abstract class DataCenterError extends Error {
  abstract readonly code: string;
  abstract readonly severity: 'low' | 'medium' | 'high' | 'critical';
  readonly timestamp: Date;
  readonly context?: Record<string, unknown>;
}

// Specific error types
class ValidationError extends DataCenterError {
  readonly code = 'VALIDATION_ERROR';
  readonly severity = 'medium';
}

class ConfigurationError extends DataCenterError {
  readonly code = 'CONFIGURATION_ERROR';
  readonly severity = 'high';
}

class DataProcessingError extends DataCenterError {
  readonly code = 'DATA_PROCESSING_ERROR';
  readonly severity = 'medium';
}

class SecurityError extends DataCenterError {
  readonly code = 'SECURITY_ERROR';
  readonly severity = 'critical';
}
```

### Error Handling Examples

```typescript
import { DataCenterError, ValidationError } from '@sanity/data-center';

// Component error handling
function DataCenterWrapper() {
  const handleError = (error: DataCenterError) => {
    switch (error.severity) {
      case 'critical':
        // Log to error service and show user notification
        console.error('Critical error:', error);
        showErrorNotification(error.message);
        break;
      case 'high':
        // Log error and show warning
        console.warn('High severity error:', error);
        showWarningNotification(error.message);
        break;
      case 'medium':
      case 'low':
        // Log for debugging
        console.log('Error:', error);
        break;
    }
  };

  return (
    <DataCenter
      config={config}
      onError={handleError}
    />
  );
}

// Async operation error handling
async function processDataFile(file: File) {
  try {
    const dataFile = await FileUtils.processFile(file);
    return dataFile;
  } catch (error) {
    if (error instanceof ValidationError) {
      console.error('File validation failed:', error.message);
      throw new Error(`Invalid file: ${error.message}`);
    } else if (error instanceof DataProcessingError) {
      console.error('Processing failed:', error.message);
      throw new Error(`Processing failed: ${error.message}`);
    } else {
      console.error('Unexpected error:', error);
      throw error;
    }
  }
}
```

## Performance Optimization

### Best Practices

1. **Use appropriate presets**: Start with the right preset for your use case
2. **Enable virtualization**: For large datasets, enable grid virtualization
3. **Configure caching**: Set appropriate cache sizes based on available memory
4. **Lazy loading**: Enable lazy loading for widgets and features
5. **Debouncing**: Configure debounce timing for user interactions

### Performance Configuration

```typescript
// High-performance configuration
const performanceConfig = new DataCenterConfigBuilder()
  .preset('analytics')
  .performance({
    virtualizeGrids: true,
    lazyLoadWidgets: true,
    cacheSize: 1000, // MB
    debounceMs: 300,
    maxConcurrentRequests: 5,
    enableServiceWorker: true,
    compressionLevel: 6
  })
  .build();

// Memory-constrained configuration
const lightConfig = new DataCenterConfigBuilder()
  .preset('minimal')
  .performance({
    virtualizeGrids: true,
    lazyLoadWidgets: true,
    cacheSize: 100, // MB
    debounceMs: 500,
    maxConcurrentRequests: 2,
    compressionLevel: 9
  })
  .build();
```

### Performance Monitoring

```typescript
import { PerformanceMonitor } from '@sanity/data-center';

// Monitor component performance
const monitor = new PerformanceMonitor();

function DataCenterWithMonitoring() {
  useEffect(() => {
    monitor.startMonitoring();
    return () => monitor.stopMonitoring();
  }, []);

  const handleDataChange = (data: DataFile[]) => {
    monitor.recordMetric('dataProcessingTime', Date.now());
    // Handle data change
  };

  return (
    <DataCenter
      config={config}
      onDataChange={handleDataChange}
    />
  );
}

// Get performance report
const report = monitor.getReport();
console.log('Performance metrics:', report);
```

## Migration Guide

### From Legacy Configuration

```typescript
import { ConfigMigrator } from '@sanity/data-center';

// Legacy configuration
const legacyConfig = {
  features: {
    dataPreview: true,
    sqlGeneration: true,
    pythonExecution: true
  },
  ui: {
    theme: 'dark',
    layout: 'comfortable'
  }
};

// Migrate to new format
const migrationResult = ConfigMigrator.migrate(legacyConfig);
if (migrationResult.success) {
  const newConfig = migrationResult.newConfig;
  console.log('Migration successful:', newConfig);
} else {
  console.error('Migration failed:', migrationResult.errors);
}
```

### Breaking Changes

#### v2.0.0

- Configuration structure changed to use presets
- Component props simplified
- Hook signatures updated
- Error types restructured

#### Migration Steps

1. Update configuration using `ConfigMigrator`
2. Replace deprecated component props
3. Update hook usage
4. Handle new error types

## Examples

### Complete Application Example

```typescript
import React, { useState, useCallback } from 'react';
import {
  DataCenter,
  DataCenterConfigBuilder,
  useDataCenter,
  FileUtils,
  DataCenterError
} from '@sanity/data-center';

function App() {
  const [config] = useState(() => 
    new DataCenterConfigBuilder()
      .preset('analytics')
      .theme('auto')
      .accessibility({ highContrast: true })
      .performance({ virtualizeGrids: true, cacheSize: 500 })
      .build()
  );

  const {
    data,
    loading,
    error,
    addData,
    removeData,
    exportData
  } = useDataCenter(config);

  const handleFileUpload = useCallback(async (files: FileList) => {
    for (const file of Array.from(files)) {
      try {
        if (!FileUtils.isSupportedDataFile(file)) {
          throw new Error(`Unsupported file type: ${file.type}`);
        }
        
        const content = await FileUtils.readFileAsText(file);
        const dataContent = FileUtils.parseCSV(content);
        
        const dataFile: DataFile = {
          id: FileUtils.generateFileId(),
          name: file.name,
          size: file.size,
          type: 'csv',
          lastModified: new Date(file.lastModified),
          checksum: FileUtils.calculateChecksum(content),
          metadata: {
            encoding: 'utf-8',
            hasHeader: true,
            rowCount: dataContent.rows.length,
            columnCount: dataContent.headers.length,
            tags: []
          },
          content: dataContent
        };
        
        addData(dataFile);
      } catch (err) {
        console.error('Failed to process file:', err);
      }
    }
  }, [addData]);

  const handleExport = useCallback(async () => {
    try {
      const blob = await exportData('csv');
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'data-export.csv';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (err) {
      console.error('Export failed:', err);
    }
  }, [exportData]);

  const handleError = useCallback((error: DataCenterError) => {
    console.error('Data Center Error:', {
      code: error.code,
      message: error.message,
      severity: error.severity,
      timestamp: error.timestamp,
      context: error.context
    });
  }, []);

  return (
    <div className="app">
      <header>
        <h1>Data Analysis Platform</h1>
        <div className="controls">
          <input
            type="file"
            multiple
            accept=".csv,.json,.xlsx"
            onChange={(e) => e.target.files && handleFileUpload(e.target.files)}
          />
          <button onClick={handleExport} disabled={data.length === 0}>
            Export Data
          </button>
        </div>
      </header>
      
      <main>
        {loading && <div className="loading">Loading...</div>}
        {error && (
          <div className="error">
            Error: {error.message}
          </div>
        )}
        
        <DataCenter
          config={config}
          initialData={data}
          onError={handleError}
          className="data-center-main"
          testId="app-data-center"
        />
      </main>
      
      <footer>
        <p>Files loaded: {data.length}</p>
      </footer>
    </div>
  );
}

export default App;
```

### Custom Widget Example

```typescript
import React from 'react';
import { WidgetProps } from '@sanity/data-center';

interface CustomChartData {
  labels: string[];
  values: number[];
}

function CustomChartWidget({ 
  id, 
  title, 
  data, 
  config, 
  onUpdate, 
  onRemove 
}: WidgetProps<CustomChartData>) {
  const handleDataUpdate = (newData: CustomChartData) => {
    onUpdate?.(newData);
  };

  return (
    <div className="custom-chart-widget" data-widget-id={id}>
      <div className="widget-header">
        <h3>{title}</h3>
        <button onClick={onRemove}>×</button>
      </div>
      
      <div className="widget-content">
        <svg width={config.size.width} height={config.size.height}>
          {/* Custom chart implementation */}
          {data.values.map((value, index) => (
            <rect
              key={index}
              x={index * 40}
              y={config.size.height - value * 2}
              width={35}
              height={value * 2}
              fill="#3b82f6"
            />
          ))}
        </svg>
      </div>
    </div>
  );
}

export default CustomChartWidget;
```

## Support

For additional support and documentation:

- [GitHub Repository](https://github.com/sanity-io/data-center)
- [Issue Tracker](https://github.com/sanity-io/data-center/issues)
- [Discussions](https://github.com/sanity-io/data-center/discussions)
- [Documentation Site](https://data-center.sanity.io)

## License

This library is licensed under the MIT License. See [LICENSE](../LICENSE) for details.