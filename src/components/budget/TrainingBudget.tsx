import React, { lazy, Suspense, useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Wallet, Receipt, Activity, Flame, Calendar as CalendarIcon, Zap, FileText, RefreshCw, PiggyBank, TrendingUp, History, ChevronLeft, ChevronRight } from 'lucide-react';
import { Toast, ToastContainer } from '@/components/ui/toast';
import { format } from 'date-fns';

// Import design tokens
import { colors, typography, spacing, borderRadius, shadows, semanticColors } from './design-tokens';

// Components
import { YearSelector } from './components/YearSelector';
import { BudgetInputSection } from './components/BudgetInputSection';
import { TabManager } from './components/TabManager';
import { MobileResponsiveWrapper, MobileStatsGrid } from './components/mobile';
import { QuickActionsMenu } from './components/quickactions';
import { 
  QuickExpenseDialog,
  AdjustBudgetDialog,
  BudgetTemplatesDialog,
  QuarterlyPlanDialog,
  AutoRulesDialog,
  ReserveFundDialog,
  ScenarioPlannerDialog,
  CarryoverRulesDialog
} from './components/dialogs';
import { SpendingHeatmap } from './components/heatmap';
import { BudgetCalendar } from './components/calendar';
import { BudgetHealthScore } from './components/healthscore/BudgetHealthScore';

// Pages - Lazy loaded for performance
const BudgetTabPage = lazy(() => import('./pages/BudgetTabPage'));
const ExpensesTabPage = lazy(() => import('./pages/ExpensesTabPage'));
const AnalyticsTabPage = lazy(() => import('./pages/AnalyticsTabPage'));
const SettingsTabPage = lazy(() => import('./pages/SettingsTabPage'));

// Hooks
import { useBudgetData } from './hooks/useBudgetData';
import { useBudgetActions } from './hooks/useBudgetActions';
import { useBudgetTabs } from './hooks/useBudgetTabs';
import { useToast } from './hooks/useToast';
import { useBudgetHealth } from './hooks/useBudgetHealth';
import { useHeatmapData } from './hooks/useHeatmapData';
import { useCalendarEvents } from './hooks/useCalendarEvents';
import { useExpenseManagement } from './hooks/useExpenseManagement';
import { 
  useQuarterlyAllocations, 
  useDepartmentAllocations, 
  useCategoryLimits, 
  useExpenses, 
  useAllocationRules, 
  useBudgetAnalytics,
  useCreateExpense,
  useUpdateExpense,
  useDeleteExpense,
  useUpdateExpenseStatus,
  budgetKeys
} from '@/hooks/queries/useBudgetQueries';
import * as budgetApi from '@/lib/services/budgetApi';
import { useQueryClient } from '@tanstack/react-query';

// Utils
import { formatCurrency } from './utils/formatters';
import { cn } from '@/lib/utils';

// Types
import { QuickAction } from './components/quickactions';
import { MobileStat } from './components/mobile';
import { Expense } from './types/expense.types';

export const TrainingBudget: React.FC = () => {
  // Get query client for cache invalidation
  const queryClient = useQueryClient();
  
  // Core data and actions
  const { year, stats, budget, loading } = useBudgetData();
  const { updateBudget, isUpdating } = useBudgetActions();
  
  // Dialog states for quick actions
  const [adjustBudgetOpen, setAdjustBudgetOpen] = useState(false);
  const [templatesOpen, setTemplatesOpen] = useState(false);
  const [quarterlyPlanOpen, setQuarterlyPlanOpen] = useState(false);
  const [autoRulesOpen, setAutoRulesOpen] = useState(false);
  const [showReserveFund, setShowReserveFund] = useState(false);
  const [showScenarioPlanner, setShowScenarioPlanner] = useState(false);
  const [showCarryoverRules, setShowCarryoverRules] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  
  // Year management
  const changeYear = (newYear: number) => {
    // When changing year, we need to refetch all data for that year
    // The year change would typically be handled by updating a global state
    // or by navigating to a different URL with the year parameter
    window.location.hash = `#/budget/${newYear}`;
  };
  const { toast, showToast, hideToast } = useToast();
  
  // Tab management
  const { activeTab, setActiveTab } = useBudgetTabs('budget');
  
  // Sidebar view toggle
  const [sidebarView, setSidebarView] = React.useState<'health' | 'heatmap' | 'calendar'>('health');
  
  // React Query data
  const { data: quarterlyAllocations = [] } = useQuarterlyAllocations(year);
  const { data: departmentAllocations = [] } = useDepartmentAllocations(year);
  const { data: categoryLimits = [] } = useCategoryLimits(year);
  const { data: expenses = [], isLoading: expensesLoading } = useExpenses(year);
  const { data: allocationRules = [], isLoading: rulesLoading } = useAllocationRules();
  const { data: analytics = null, isLoading: analyticsLoading } = useBudgetAnalytics(year);
  
  // React Query mutations
  const createExpenseMutation = useCreateExpense();
  const updateExpenseMutation = useUpdateExpense();
  const deleteExpenseMutation = useDeleteExpense();
  const updateExpenseStatusMutation = useUpdateExpenseStatus();
  
  // Store actions implemented with API calls
  const updateQuarterlyAllocations = async (allocations: any) => {
    try {
      await budgetApi.updateQuarterlyAllocations(year, allocations);
      queryClient.invalidateQueries({ queryKey: budgetKeys.quarterlyAllocations(year) });
    } catch (error) {
      console.error('Failed to update quarterly allocations:', error);
      throw error;
    }
  };
  const updateDepartmentAllocation = async (departmentId: string, amount: number) => {
    try {
      await budgetApi.updateDepartmentAllocation(departmentId, amount);
      queryClient.invalidateQueries({ queryKey: budgetKeys.departmentAllocations(year) });
    } catch (error) {
      console.error('Failed to update department allocation:', error);
      throw error;
    }
  };
  const addDepartment = async (name: string, amount: number) => {
    try {
      await budgetApi.addDepartment(name, amount);
      queryClient.invalidateQueries({ queryKey: budgetKeys.departmentAllocations(year) });
    } catch (error) {
      console.error('Failed to add department:', error);
      throw error;
    }
  };
  const removeDepartment = async (departmentId: string) => {
    try {
      await budgetApi.removeDepartment(departmentId);
      queryClient.invalidateQueries({ queryKey: budgetKeys.departmentAllocations(year) });
    } catch (error) {
      console.error('Failed to remove department:', error);
      throw error;
    }
  };
  const setCategoryLimit = async (category: string, limit: number) => {
    try {
      await budgetApi.setCategoryLimit(category, limit);
      queryClient.invalidateQueries({ queryKey: budgetKeys.categoryLimits(year) });
    } catch (error) {
      console.error('Failed to set category limit:', error);
      throw error;
    }
  };
  const removeCategoryLimit = async (category: string) => {
    try {
      await budgetApi.removeCategoryLimit(category);
      queryClient.invalidateQueries({ queryKey: budgetKeys.categoryLimits(year) });
    } catch (error) {
      console.error('Failed to remove category limit:', error);
      throw error;
    }
  };
  const addExpense = async (data: any) => {
    try {
      await createExpenseMutation.mutateAsync(data);
      return true;
    } catch (error) {
      return false;
    }
  };
  const updateExpense = async (id: string, data: any) => {
    await updateExpenseMutation.mutateAsync({ id, updates: data });
  };
  const deleteExpense = async (id: string) => {
    await deleteExpenseMutation.mutateAsync(id);
  };
  const updateExpenseStatus = async (id: string, status: string) => {
    await updateExpenseStatusMutation.mutateAsync({ id, status });
  };
  const addAllocationRule = async (rule: any) => {
    try {
      await budgetApi.addAllocationRule(rule);
      queryClient.invalidateQueries({ queryKey: budgetKeys.allocationRules() });
    } catch (error) {
      console.error('Failed to add allocation rule:', error);
      throw error;
    }
  };
  const updateAllocationRule = async (id: string, updates: any) => {
    try {
      await budgetApi.updateAllocationRule(id, updates);
      queryClient.invalidateQueries({ queryKey: budgetKeys.allocationRules() });
    } catch (error) {
      console.error('Failed to update allocation rule:', error);
      throw error;
    }
  };
  const deleteAllocationRule = async (id: string) => {
    try {
      await budgetApi.deleteAllocationRule(id);
      queryClient.invalidateQueries({ queryKey: budgetKeys.allocationRules() });
    } catch (error) {
      console.error('Failed to delete allocation rule:', error);
      throw error;
    }
  };
  const toggleAllocationRule = async (id: string, enabled: boolean) => {
    try {
      await budgetApi.toggleAllocationRule(id, enabled);
      queryClient.invalidateQueries({ queryKey: budgetKeys.allocationRules() });
    } catch (error) {
      console.error('Failed to toggle allocation rule:', error);
      throw error;
    }
  };
  const executeAllocationRule = async (id: string) => {
    try {
      await budgetApi.executeAllocationRule(id);
      queryClient.invalidateQueries({ queryKey: budgetKeys.allocationRules() });
      queryClient.invalidateQueries({ queryKey: budgetKeys.stats(year) });
    } catch (error) {
      console.error('Failed to execute allocation rule:', error);
      throw error;
    }
  };
  
  // Calculated data with memoization (using type assertion for now)
  const healthMetrics = useBudgetHealth(stats as any, budget || null, year);
  const heatmapData = useHeatmapData(expenses);
  const calendarEvents = useCalendarEvents(expenses, stats as any, year);
  
  // Expense management (with corrected types)
  const expenseActions = {
    addExpense: async (data: any) => {
      try {
        await createExpenseMutation.mutateAsync(data);
      } catch (error) {
        console.error('Failed to add expense:', error);
      }
    },
    updateExpense,
    deleteExpense,
    updateExpenseStatus,
  };
  
  const {
    showExpenseForm,
    editingExpense,
    expenseTemplates,
    handleExpenseSubmit,
    handleSaveTemplate,
    handleExpenseEdit,
    handleExpenseDelete,
    handleStatusChange,
    handleViewReceipt,
    openExpenseForm,
    closeExpenseForm,
  } = useExpenseManagement({
    expenses,
    ...expenseActions,
  });


  // Data is automatically fetched by React Query hooks
  // No manual fetching needed as hooks handle this automatically

  const handleBudgetUpdate = async (amount: number) => {
    const result = await updateBudget(year, amount);
    
    if (result.success) {
      showToast('Budget updated successfully!', 'success');
    } else {
      showToast(result.error || 'Failed to update budget', 'error');
    }
    
    return result;
  };

  const handleRefresh = () => {
    // Data will be automatically refetched by React Query
  };

  // Quick Actions configuration
  const quickActions: QuickAction[] = [
    {
      id: 'adjust-budget',
      label: 'Adjust Budget',
      icon: <Wallet className="h-4 w-4" />,
      description: 'Modify total budget amount',
      category: 'budget',
      color: 'text-success-500',
      action: () => setAdjustBudgetOpen(true)
    },
    {
      id: 'budget-templates',
      label: 'Budget Templates',
      icon: <FileText className="h-4 w-4" />,
      description: 'Manage budget templates',
      category: 'budget',
      color: 'text-purple-500',
      action: () => setTemplatesOpen(true)
    },
    {
      id: 'quarterly-plan',
      label: 'Quarterly Plan',
      icon: <CalendarIcon className="h-4 w-4" />,
      description: 'Set quarterly allocations',
      category: 'budget',
      color: 'text-primary-500',
      action: () => setQuarterlyPlanOpen(true)
    },
    {
      id: 'auto-rules',
      label: 'Auto Rules',
      icon: <RefreshCw className="h-4 w-4" />,
      description: 'Configure allocation rules',
      category: 'budget',
      color: 'text-warning-500',
      action: () => setAutoRulesOpen(true)
    },
    {
      id: 'reserve-fund',
      label: 'Reserve Fund',
      icon: <PiggyBank className="h-4 w-4" />,
      description: 'Manage reserve funds',
      category: 'budget',
      color: 'text-pink-500',
      action: () => setShowReserveFund(true)
    },
    {
      id: 'scenarios',
      label: 'Scenarios',
      icon: <TrendingUp className="h-4 w-4" />,
      description: 'Budget scenario planning',
      category: 'budget',
      color: 'text-indigo-500',
      action: () => setShowScenarioPlanner(true)
    },
    {
      id: 'carryover',
      label: 'Carryover',
      icon: <History className="h-4 w-4" />,
      description: 'Configure carryover rules',
      category: 'budget',
      color: 'text-neutral-500',
      action: () => setShowCarryoverRules(true)
    },
    {
      id: 'add-expense',
      label: 'Add Expense',
      icon: <Receipt className="h-4 w-4" />,
      description: 'Record a new training expense',
      shortcut: 'Ctrl+E',
      category: 'expense',
      color: 'text-primary-500',
      action: () => {
        openExpenseForm();
        setActiveTab('expenses');
      }
    }
  ];

  // Mobile stats
  const mobileStats: MobileStat[] = [
    {
      id: 'total',
      label: 'Total Budget',
      value: `$${((stats?.total ?? 0) / 1000).toFixed(0)}k`,
      icon: <Wallet className="h-4 w-4" />,
      color: 'text-primary-500'
    },
    // ... more stats
  ];

  // Tab configurations
  const tabConfigs = [
    {
      value: 'budget',
      label: 'Budget',
      component: BudgetTabPage,
      props: {
        budget,
        stats,
        quarterlyAllocations,
        departmentAllocations,
        categoryLimits,
        allocationRules,
        loading,
        rulesLoading,
        onUpdateQuarterlyAllocations: updateQuarterlyAllocations,
        onUpdateDepartmentAllocation: updateDepartmentAllocation,
        onAddDepartment: addDepartment,
        onRemoveDepartment: removeDepartment,
        onSetCategoryLimit: setCategoryLimit,
        onRemoveCategoryLimit: removeCategoryLimit,
        onAddAllocationRule: addAllocationRule,
        onUpdateAllocationRule: updateAllocationRule,
        onDeleteAllocationRule: deleteAllocationRule,
        onToggleAllocationRule: toggleAllocationRule,
        onExecuteAllocationRule: executeAllocationRule,
        onTabChange: setActiveTab,
      }
    },
    {
      value: 'expenses',
      label: 'Expenses',
      component: ExpensesTabPage,
      props: {
        expenses,
        departmentAllocations,
        templates: [],
        expenseTemplates,
        loading: expensesLoading,
        onExpenseSubmit: handleExpenseSubmit,
        onExpenseEdit: handleExpenseEdit,
        onExpenseDelete: handleExpenseDelete,
        onStatusChange: handleStatusChange,
        onViewReceipt: handleViewReceipt,
        onSaveTemplate: handleSaveTemplate,
        addExpense,
        updateExpense,
      }
    },
    {
      value: 'analytics',
      label: 'Analytics',
      component: AnalyticsTabPage,
      props: {
        analytics,
        budget,
        stats,
        year,
        loading: analyticsLoading,
      }
    },
    {
      value: 'settings',
      label: 'Settings',
      component: SettingsTabPage,
      props: {
        onSave: () => {
          showToast('Settings saved successfully!', 'success');
          handleRefresh();
        }
      }
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-neutral-50 via-neutral-50 to-neutral-100/20">
      <ToastContainer>
        {toast.isVisible && (
          <Toast
            message={toast.message}
            type={toast.type}
            onDismiss={hideToast}
            duration={3000}
          />
        )}
      </ToastContainer>
      
      <MobileResponsiveWrapper
        currentTab={activeTab}
        onTabChange={setActiveTab}
        budgetStats={{
          total: stats?.total ?? 0,
          spent: stats?.spent ?? 0,
          remaining: stats?.remaining ?? 0
        }}
      >
        <div className="h-screen flex overflow-hidden">
          {/* Sidebar */}
          <div className={cn(
            "flex-shrink-0 bg-gradient-to-b from-neutral-100 to-neutral-100/80 border-r border-neutral-200 overflow-y-auto transition-all duration-300",
            sidebarCollapsed ? "w-0" : "w-96"
          )}>
            <div className={cn(
              "p-6 space-y-6 transition-opacity duration-300",
              sidebarCollapsed ? "opacity-0 pointer-events-none" : "opacity-100"
            )}>
              <div className="flex items-center justify-between">
                <h3 className="text-base font-medium flex items-center gap-2 text-white">
                  <Activity className="h-5 w-5 text-dashboard-chart-secondary" />
                  Activity Overview
                </h3>
                <button
                  onClick={() => setSidebarCollapsed(true)}
                  className="p-1.5 rounded-lg hover:bg-neutral-200 transition-colors text-neutral-400 hover:text-neutral-900"
                  aria-label="Collapse sidebar"
                >
                  <ChevronLeft className="h-4 w-4" />
                </button>
              </div>

              {/* Toggle Buttons */}
              <div className="flex gap-2 p-1 bg-neutral-200 rounded-lg">
                <button
                  onClick={() => setSidebarView('health')}
                  className={`flex-1 flex items-center justify-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    sidebarView === 'health'
                      ? 'bg-primary-500 text-white shadow-sm'
                      : 'text-neutral-400 hover:text-neutral-900'
                  }`}
                >
                  <Activity className="h-4 w-4" />
                  Health
                </button>
                <button
                  onClick={() => setSidebarView('heatmap')}
                  className={`flex-1 flex items-center justify-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    sidebarView === 'heatmap'
                      ? 'bg-primary-500 text-white shadow-sm'
                      : 'text-neutral-400 hover:text-neutral-900'
                  }`}
                >
                  <Flame className="h-4 w-4" />
                  Heatmap
                </button>
                <button
                  onClick={() => setSidebarView('calendar')}
                  className={`flex-1 flex items-center justify-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    sidebarView === 'calendar'
                      ? 'bg-primary-500 text-white shadow-sm'
                      : 'text-neutral-400 hover:text-neutral-900'
                  }`}
                >
                  <CalendarIcon className="h-4 w-4" />
                  Calendar
                </button>
              </div>

              {/* Conditional Content */}
              <div>
                {sidebarView === 'health' ? (
                  <>
                    <h4 className="text-sm font-medium mb-3 text-neutral-400">Budget Health Score</h4>
                    <BudgetHealthScore
                      metrics={{
                        totalBudget: stats?.total || 0,
                        totalSpent: stats?.spent || 0,
                        utilization: stats?.total ? ((stats.spent / stats.total) * 100) : 0,
                        burnRate: healthMetrics?.burnRate || 1.0,
                        projectedSpend: healthMetrics?.projectedSpend || 0,
                        riskScore: healthMetrics?.riskScore || 0,
                        savingsOpportunity: healthMetrics?.savingsOpportunity || 0,
                        complianceRate: healthMetrics?.complianceRate || 100,
                        efficiencyScore: healthMetrics?.efficiencyScore || 85,
                        categoryDistribution: {},
                        monthlyTrend: []
                      }}
                      showRecommendations={true}
                      compactMode={false}
                      loading={loading}
                    />
                  </>
                ) : sidebarView === 'heatmap' ? (
                  <>
                    <h4 className="text-sm font-medium mb-3 text-neutral-400">Spending Heatmap</h4>
                    {heatmapData ? (
                      <SpendingHeatmap
                        data={heatmapData}
                        loading={loading}
                        currentMonth={new Date()}
                        onDayClick={(date, data) => {
                          showToast(`Spent ${formatCurrency(data.amount)} on ${format(date, 'MMM d')}`, 'info');
                        }}
                      />
                    ) : (
                      <Card className="p-4 bg-neutral-100 border-neutral-200">
                        <div className="text-sm text-neutral-400 text-center">
                          {loading ? <Skeleton className="h-32 w-full" /> : 'No spending data available'}
                        </div>
                      </Card>
                    )}
                  </>
                ) : (
                  <>
                    <h4 className="text-sm font-medium mb-3 text-neutral-400">Budget Calendar</h4>
                    {calendarEvents && calendarEvents.length > 0 ? (
                      <BudgetCalendar
                        events={calendarEvents}
                        loading={loading}
                        currentMonth={new Date()}
                        onEventClick={(event) => {
                          showToast(`Event: ${event.title}`, 'info');
                        }}
                        onDateClick={(date) => {
                          console.log('Date clicked:', date);
                        }}
                      />
                    ) : (
                      <Card className="p-4 bg-neutral-100 border-neutral-200">
                        <div className="text-sm text-neutral-400 text-center">
                          {loading ? <Skeleton className="h-32 w-full" /> : 'No calendar events'}
                        </div>
                      </Card>
                    )}
                  </>
                )}
              </div>


            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1 flex flex-col overflow-hidden relative">
            {/* Sidebar Toggle Button - Only show when collapsed */}
            {sidebarCollapsed && (
              <button
                onClick={() => setSidebarCollapsed(false)}
                className="absolute left-2 top-6 z-20 p-2 rounded-lg bg-neutral-100/80 backdrop-blur border-neutral-200 hover:bg-neutral-200 transition-colors text-neutral-400 hover:text-neutral-900"
                aria-label="Expand sidebar"
              >
                <ChevronRight className="h-4 w-4" />
              </button>
            )}
            
            {/* Fixed Header Section */}
            <div className="flex-shrink-0 bg-gradient-to-r from-neutral-100 to-neutral-100/80 border-b border-neutral-200 sticky top-0 z-10">
              <div className="container mx-auto px-4 py-3">
            <div className="space-y-4">
              {/* Top row - Title and Year Selector */}
              <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                <motion.div 
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="flex items-center gap-4"
                >
                  <div className="flex items-center gap-3">
                    <Wallet className="h-8 w-8 text-primary-500" aria-hidden="true" />
                    <div>
                      <h1 className="text-xl font-semibold tracking-tight text-neutral-900">Training Budget</h1>
                      <p className="text-xs text-neutral-400">Fiscal Year {year}</p>
                    </div>
                  </div>
                </motion.div>
                
                <YearSelector 
                  year={year}
                  onYearChange={changeYear}
                />
              </div>

              {/* Bottom row - Budget Stats and Input */}
              <div className="space-y-4">
                {/* Budget Stats Cards */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 }}
                  className="grid grid-cols-3 gap-2"
                >
                  {/* Total Budget Card */}
                  <Card className="p-3 bg-neutral-100 border-neutral-200">
                    <div className="space-y-1">
                      <div className="flex items-center justify-between">
                        <p className="text-xs font-medium text-neutral-400">Total Budget</p>
                        <Badge variant="outline" className="text-xs text-success-500 border-success-500">
                          {healthMetrics?.status?.toUpperCase() || 'EXCELLENT'}
                        </Badge>
                      </div>
                      <div className="text-lg font-semibold text-neutral-900">
                        {loading ? (
                          <Skeleton className="h-5 w-20" />
                        ) : (
                          formatCurrency(stats?.total ?? 0)
                        )}
                      </div>
                    </div>
                  </Card>

                  {/* Spent Card */}
                  <Card className="p-3 bg-neutral-100 border-neutral-200">
                    <div className="space-y-1">
                      <p className="text-xs font-medium text-neutral-400">Spent</p>
                      <div className="text-lg font-semibold text-warning-500">
                        {loading ? (
                          <Skeleton className="h-5 w-20" />
                        ) : (
                          formatCurrency(stats?.spent ?? 0)
                        )}
                      </div>
                    </div>
                  </Card>

                  {/* Remaining Card */}
                  <Card className="p-3 bg-neutral-100 border-neutral-200">
                    <div className="space-y-1">
                      <p className="text-xs font-medium text-neutral-400">Remaining</p>
                      <div className="text-lg font-semibold text-success-500">
                        {loading ? (
                          <Skeleton className="h-5 w-20" />
                        ) : (
                          formatCurrency(stats?.remaining ?? 0)
                        )}
                      </div>
                    </div>
                  </Card>
                </motion.div>

                {/* Budget Input Section */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                >
                  <BudgetInputSection
                    currentBudget={budget?.total_amount || stats?.total || 0}
                    onUpdate={handleBudgetUpdate}
                    loading={loading}
                    isUpdating={isUpdating}
                  />
                </motion.div>
              </div>
              
              {/* Tabs Navigation - Part of fixed header */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="mt-4"
              >
                <div className="flex space-x-1 border-b border-neutral-200">
                  {tabConfigs.map((tab) => (
                    <button
                      key={tab.value}
                      onClick={() => setActiveTab(tab.value)}
                      className={`px-4 py-2 text-sm font-medium transition-colors hover:text-primary-600 ${
                        activeTab === tab.value
                          ? 'text-primary-500 border-b-2 border-primary-500'
                          : 'text-neutral-400'
                      }`}
                    >
                      {tab.label}
                    </button>
                  ))}
                </div>
              </motion.div>
            </div>
          </div>
        </div>

        {/* Scrollable Tab Content */}
        <div className="flex-1 overflow-y-auto">
          <div className="container mx-auto px-4 py-4">
            <TabManager
              tabs={tabConfigs}
              activeTab={activeTab}
              onTabChange={setActiveTab}
              showTabList={false}
              gridCols={6}
            />
          </div>
        </div>
      </div>
    </div>
        
        {/* Quick Actions Menu - FAB Style */}
        <div className="hidden md:block">
          <QuickActionsMenu
            actions={quickActions}
            position="bottom-right"
            fabStyle={true}
            onActionExecute={(actionId) => {
              showToast(`Action "${actionId}" executed`, 'success');
            }}
          />
        </div>
      </MobileResponsiveWrapper>
      
      {/* Quick Expense Dialog */}
      <QuickExpenseDialog
        open={showExpenseForm}
        onOpenChange={(open) => {
          if (!open) closeExpenseForm();
          else openExpenseForm();
        }}
        onSubmit={handleExpenseSubmit}
        departments={departmentAllocations}
        initialData={editingExpense || undefined}
        templates={expenseTemplates}
        onSaveAsTemplate={handleSaveTemplate}
      />
      
      {/* Budget Management Dialogs */}
      <AdjustBudgetDialog
        open={adjustBudgetOpen}
        onOpenChange={setAdjustBudgetOpen}
        currentBudget={stats?.total || 0}
        spentAmount={stats?.spent || 0}
        onAdjust={async (newBudget) => {
          const result = await updateBudget(year, newBudget);
          if (result.success) {
            showToast('Budget adjusted successfully!', 'success');
          }
        }}
      />
      
      <BudgetTemplatesDialog
        open={templatesOpen}
        onOpenChange={setTemplatesOpen}
        templates={[]}
        currentBudget={{
          total: stats?.total || 0,
          departments: departmentAllocations,
          categories: categoryLimits,
          allocations: quarterlyAllocations,
          rules: allocationRules,
        }}
        onApplyTemplate={async (templateId) => {
          await budgetApi.applyBudgetTemplate(templateId, year);
          queryClient.invalidateQueries({ queryKey: budgetKeys.budget(year) });
          queryClient.invalidateQueries({ queryKey: budgetKeys.stats(year) });
          showToast('Template applied successfully!', 'success');
        }}
        onSaveTemplate={async (template) => {
          console.log('Saving template:', template);
          showToast('Template saved successfully!', 'success');
        }}
        onDeleteTemplate={async (templateId) => {
          console.log('Deleting template:', templateId);
          showToast('Template deleted successfully!', 'success');
        }}
        onExportTemplate={async (templateId) => {
          console.log('Exporting template:', templateId);
        }}
        onImportTemplate={async (file) => {
          console.log('Importing template:', file);
          showToast('Template imported successfully!', 'success');
        }}
      />
      
      <QuarterlyPlanDialog
        open={quarterlyPlanOpen}
        onOpenChange={setQuarterlyPlanOpen}
        allocations={quarterlyAllocations}
        totalBudget={stats?.total || 0}
        onUpdate={async (allocations) => {
          await updateQuarterlyAllocations(allocations);
          showToast('Quarterly allocations updated!', 'success');
        }}
      />
      
      <AutoRulesDialog
        open={autoRulesOpen}
        onOpenChange={setAutoRulesOpen}
        rules={[]}
        departments={departmentAllocations}
        categories={stats?.by_category?.map(c => ({ 
          id: c.category, 
          name: c.category 
        })) || []}
        totalBudget={stats?.total || 0}
        currentSpent={stats?.spent || 0}
        onAddRule={async (rule) => {
          await addAllocationRule(rule);
          showToast('Allocation rule added!', 'success');
        }}
        onUpdateRule={async (id, updates) => {
          await updateAllocationRule(id, updates);
          showToast('Allocation rule updated!', 'success');
        }}
        onDeleteRule={async (id) => {
          await deleteAllocationRule(id);
          showToast('Allocation rule deleted!', 'success');
        }}
        onToggleRule={async (id, enabled) => {
          await toggleAllocationRule(id, enabled);
          showToast(`Rule ${enabled ? 'enabled' : 'disabled'}!`, 'success');
        }}
        onExecuteRule={async (id) => {
          await executeAllocationRule(id);
          showToast('Allocation rule executed!', 'success');
        }}
      />
      
      <ReserveFundDialog
        open={showReserveFund}
        onOpenChange={setShowReserveFund}
        year={year}
        onUpdateReserveFund={async (year, amount) => {
          await budgetApi.updateReserveFund(year, amount);
          queryClient.invalidateQueries({ queryKey: budgetKeys.budget(year) });
          showToast('Reserve fund updated!', 'success');
        }}
        onSetReserveTarget={async (year, target) => {
          await budgetApi.setReserveTarget(year, target);
          queryClient.invalidateQueries({ queryKey: budgetKeys.budget(year) });
          showToast('Reserve target set!', 'success');
        }}
      />
      
      <ScenarioPlannerDialog
        open={showScenarioPlanner}
        onOpenChange={setShowScenarioPlanner}
        currentBudget={{
          total: stats?.total || 0,
          spent: stats?.spent || 0,
          departments: departmentAllocations,
          categories: categoryLimits
        }}
        year={year}
        onSaveScenario={async (scenario) => {
          await budgetApi.saveBudgetScenario(year, scenario);
          queryClient.invalidateQueries({ queryKey: budgetKeys.analytics(year) });
          showToast('Scenario saved!', 'success');
        }}
        onApplyScenario={async (scenario) => {
          await budgetApi.applyBudgetScenario(year, scenario);
          queryClient.invalidateQueries({ queryKey: budgetKeys.budget(year) });
          queryClient.invalidateQueries({ queryKey: budgetKeys.stats(year) });
          showToast('Scenario applied!', 'success');
        }}
      />
      
      <CarryoverRulesDialog
        open={showCarryoverRules}
        onOpenChange={setShowCarryoverRules}
        year={year}
        remainingAmount={stats?.remaining || 0}
        stats={stats}
        onApplyCarryoverRules={async (fromYear, toYear, rules) => {
          await budgetApi.applyCarryoverRules(fromYear, toYear, rules);
          queryClient.invalidateQueries({ queryKey: budgetKeys.budget(toYear) });
          queryClient.invalidateQueries({ queryKey: budgetKeys.stats(toYear) });
          showToast('Carryover rules applied!', 'success');
        }}
      />
    </div>
  );
};

export default TrainingBudget;