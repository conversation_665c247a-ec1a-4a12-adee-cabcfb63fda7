<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>React Spring Budget Demo</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://unpkg.com/@react-spring/web@9.7.3/dist/react-spring-web.umd.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .demo-header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }
        
        .demo-header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .demo-header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .demo-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s ease;
        }
        
        .demo-card:hover {
            transform: translateY(-2px);
        }
        
        .demo-card h3 {
            color: #333;
            margin-bottom: 16px;
            font-size: 1.3rem;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
            margin: 8px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transition: width 0.3s ease;
        }
        
        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 8px 0;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .stat-item:last-child {
            border-bottom: none;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }
        
        .stat-value {
            color: #333;
            font-weight: 600;
            font-size: 1.1rem;
        }
        
        .expense-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            margin: 8px 0;
            background: #f8f9fa;
            border-radius: 8px;
            transition: background 0.2s ease;
        }
        
        .expense-item:hover {
            background: #e9ecef;
        }
        
        .controls {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .btn {
            background: white;
            color: #667eea;
            border: 2px solid #667eea;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            background: #667eea;
            color: white;
            transform: translateY(-1px);
        }
        
        .animated-element {
            opacity: 0;
            transform: translateY(20px);
            animation: fadeInUp 0.6s ease forwards;
        }
        
        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect } = React;
        const { useSpring, animated, useTransition, useTrail } = ReactSpring;

        // Demo data
        const demoData = {
            summary: {
                totalBudget: 150000,
                totalSpent: 87500,
                totalRemaining: 62500,
                utilization: 58.3,
                departmentCount: 8,
                activeCategories: 12,
                monthlyAverage: 7291.67,
                lastUpdated: new Date().toISOString()
            },
            progress: [
                { category: 'Training Materials', allocated: 25000, spent: 18000, percentage: 72 },
                { category: 'Instructor Fees', allocated: 45000, spent: 35000, percentage: 77.8 },
                { category: 'Venue Costs', allocated: 30000, spent: 15000, percentage: 50 }
            ],
            expenses: [
                { id: 1, title: 'Q1 Training Materials', amount: 5000, category: 'Training Materials', date: '2024-01-15' },
                { id: 2, title: 'Instructor Workshop', amount: 8000, category: 'Instructor Fees', date: '2024-01-20' },
                { id: 3, title: 'Venue Rental', amount: 3000, category: 'Venue Costs', date: '2024-01-25' }
            ]
        };

        // Animated Budget Summary Component
        function AnimatedBudgetSummary({ data }) {
            const props = useSpring({
                from: { opacity: 0, transform: 'translateY(30px)' },
                to: { opacity: 1, transform: 'translateY(0px)' },
                config: { tension: 200, friction: 20 }
            });

            const numberProps = useSpring({
                from: { number: 0 },
                to: { number: data.totalBudget },
                config: { duration: 1000 }
            });

            return (
                <animated.div style={props} className="demo-card">
                    <h3>💰 Budget Summary</h3>
                    <div className="stat-item">
                        <span className="stat-label">Total Budget</span>
                        <animated.span className="stat-value">
                            ${numberProps.number.to(n => Math.round(n).toLocaleString())}
                        </animated.span>
                    </div>
                    <div className="stat-item">
                        <span className="stat-label">Total Spent</span>
                        <span className="stat-value">${data.totalSpent.toLocaleString()}</span>
                    </div>
                    <div className="stat-item">
                        <span className="stat-label">Remaining</span>
                        <span className="stat-value">${data.totalRemaining.toLocaleString()}</span>
                    </div>
                    <div className="stat-item">
                        <span className="stat-label">Utilization</span>
                        <span className="stat-value">{data.utilization}%</span>
                    </div>
                    <div className="progress-bar">
                        <div 
                            className="progress-fill" 
                            style={{ width: `${data.utilization}%` }}
                        ></div>
                    </div>
                </animated.div>
            );
        }

        // Animated Progress Component
        function AnimatedBudgetProgress({ progress }) {
            const trail = useTrail(progress.length, {
                from: { opacity: 0, transform: 'translateX(-20px)' },
                to: { opacity: 1, transform: 'translateX(0px)' },
                config: { tension: 150, friction: 15 }
            });

            return (
                <div className="demo-card">
                    <h3>📊 Budget Progress</h3>
                    {trail.map((props, index) => (
                        <animated.div key={index} style={props}>
                            <div className="stat-item">
                                <div>
                                    <div className="stat-label">{progress[index].category}</div>
                                    <div className="stat-value">${progress[index].spent.toLocaleString()} / ${progress[index].allocated.toLocaleString()}</div>
                                </div>
                                <div style={{ textAlign: 'right' }}>
                                    <div className="stat-value">{progress[index].percentage}%</div>
                                    <div className="progress-bar" style={{ width: '100px' }}>
                                        <div 
                                            className="progress-fill" 
                                            style={{ width: `${progress[index].percentage}%` }}
                                        ></div>
                                    </div>
                                </div>
                            </div>
                        </animated.div>
                    ))}
                </div>
            );
        }

        // Animated Expense List Component
        function AnimatedExpenseList({ expenses }) {
            const transitions = useTransition(expenses, {
                from: { opacity: 0, transform: 'scale(0.9)' },
                enter: { opacity: 1, transform: 'scale(1)' },
                leave: { opacity: 0, transform: 'scale(0.9)' },
                config: { tension: 200, friction: 20 }
            });

            return (
                <div className="demo-card">
                    <h3>💰 Recent Expenses</h3>
                    {transitions((props, item) => (
                        <animated.div style={props}>
                            <div className="expense-item">
                                <div>
                                    <div style={{ fontWeight: 'bold', color: '#333' }}>{item.title}</div>
                                    <div style={{ color: '#666', fontSize: '0.9rem' }}>{item.category} • {item.date}</div>
                                </div>
                                <div style={{ fontWeight: 'bold', color: '#667eea' }}>
                                    ${item.amount.toLocaleString()}
                                </div>
                            </div>
                        </animated.div>
                    ))}
                </div>
            );
        }

        // Main Demo App
        function ReactSpringDemo() {
            const [expenses, setExpenses] = useState(demoData.expenses);
            const [loading, setLoading] = useState(false);

            const addExpense = () => {
                const newExpense = {
                    id: Date.now(),
                    title: `New Expense ${Math.floor(Math.random() * 100)}`,
                    amount: Math.floor(Math.random() * 5000) + 1000,
                    category: demoData.progress[Math.floor(Math.random() * demoData.progress.length)].category,
                    date: new Date().toISOString().split('T')[0]
                };
                setExpenses(prev => [...prev, newExpense]);
            };

            const refreshData = () => {
                setLoading(true);
                setTimeout(() => {
                    setLoading(false);
                    setExpenses(demoData.expenses);
                }, 1000);
            };

            return (
                <div className="demo-container">
                    <div className="demo-header">
                        <h1>React Spring Budget Demo</h1>
                        <p>Interactive animations for training budget visualization</p>
                    </div>

                    <div className="controls">
                        <button className="btn" onClick={refreshData} disabled={loading}>
                            {loading ? '⏳ Loading...' : '🔄 Refresh'}
                        </button>
                        <button className="btn" onClick={addExpense}>
                            ➕ Add Expense
                        </button>
                        <button className="btn" onClick={() => setExpenses(demoData.expenses)}>
                            🔄 Reset
                        </button>
                    </div>

                    <div className="demo-grid">
                        <AnimatedBudgetSummary data={demoData.summary} />
                        <AnimatedBudgetProgress progress={demoData.progress} />
                        <AnimatedExpenseList expenses={expenses} />
                    </div>

                    <div style={{ textAlign: 'center', color: 'white', marginTop: '40px' }}>
                        <h3>✨ Animation Features</h3>
                        <div style={{ display: 'flex', justifyContent: 'center', gap: '20px', flexWrap: 'wrap', marginTop: '20px' }}>
                            <span>🎯 Spring Physics</span>
                            <span>⚡ 60fps Animations</span>
                            <span>🎨 Smooth Transitions</span>
                            <span>📱 Responsive Design</span>
                            <span>🔄 Real-time Updates</span>
                        </div>
                    </div>
                </div>
            );
        }

        // Render the demo
        ReactDOM.render(<ReactSpringDemo />, document.getElementById('root'));
    </script>
</body>
</html>