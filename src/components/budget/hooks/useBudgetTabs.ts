import { useState, useCallback } from 'react';

export interface BudgetTab {
  value: string;
  label: string;
  enabled: boolean;
}

const DEFAULT_TABS: BudgetTab[] = [
  { value: 'overview', label: 'Overview', enabled: true },
  { value: 'budget', label: 'Budget', enabled: true },
  { value: 'expenses', label: 'Expenses', enabled: true },
  { value: 'analytics', label: 'Analytics', enabled: true },
  { value: 'settings', label: 'Settings', enabled: true },
];

export const useBudgetTabs = (initialTab: string = 'overview') => {
  const [activeTab, setActiveTab] = useState(initialTab);
  const [tabs] = useState<BudgetTab[]>(DEFAULT_TABS);
  const [tabHistory, setTabHistory] = useState<string[]>([initialTab]);

  const changeTab = useCallback((newTab: string) => {
    setActiveTab(newTab);
    setTabHistory(prev => [...prev, newTab].slice(-10)); // Keep last 10 tabs
  }, []);

  const goToPreviousTab = useCallback(() => {
    if (tabHistory.length > 1) {
      const newHistory = [...tabHistory];
      newHistory.pop(); // Remove current tab
      const previousTab = newHistory[newHistory.length - 1];
      setActiveTab(previousTab);
      setTabHistory(newHistory);
    }
  }, [tabHistory]);

  const isTabActive = useCallback((tabValue: string) => {
    return activeTab === tabValue;
  }, [activeTab]);

  const getTabLabel = useCallback((tabValue: string) => {
    const tab = tabs.find(t => t.value === tabValue);
    return tab?.label || tabValue;
  }, [tabs]);

  return {
    activeTab,
    tabs,
    tabHistory,
    setActiveTab: changeTab,
    goToPreviousTab,
    isTabActive,
    getTabLabel,
  };
};