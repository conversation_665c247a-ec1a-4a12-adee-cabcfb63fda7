import { useBudget, useBudgetStats } from '@/hooks/queries/useBudgetQueries';

/**
 * Compatibility hook that provides budget data using React Query
 * This replaces the previous store-based implementation
 */
export const useBudgetData = (year: number = new Date().getFullYear()) => {
  const { data: budget, isLoading: budgetLoading, error: budgetError } = useBudget(year);
  const { data: stats, isLoading: statsLoading, error: statsError } = useBudgetStats(year);

  const loading = budgetLoading || statsLoading;
  const hasError = budgetError || statsError;

  return {
    year,
    stats,
    budget,
    loading,
    error: hasError,
    isDataLoaded: !loading && stats !== null && budget !== null,
  };
};