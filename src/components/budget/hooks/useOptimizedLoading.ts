import { useState, useEffect, useCallback, useRef } from 'react';

interface LoadingState {
  isLoading: boolean;
  progress: number;
  error: string | null;
  phase: 'idle' | 'loading' | 'success' | 'error';
}

interface LoadingOptions {
  /** Minimum time to show loading state (prevents flash) */
  minLoadTime?: number;
  /** Delay before showing loading (prevents flash for fast operations) */
  loadingDelay?: number;
  /** Enable progress tracking */
  trackProgress?: boolean;
  /** Enable automatic retry on failure */
  enableRetry?: boolean;
  /** Max retry attempts */
  maxRetries?: number;
  /** Retry delay in ms */
  retryDelay?: number;
}

interface LoadingOperation<T> {
  id: string;
  promise: Promise<T>;
  onProgress?: (progress: number) => void;
  weight?: number; // For calculating overall progress
}

export const useOptimizedLoading = (options: LoadingOptions = {}) => {
  const {
    minLoadTime = 500,
    loadingDelay = 100,
    trackProgress = false,
    enableRetry = false,
    maxRetries = 3,
    retryDelay = 1000,
  } = options;

  const [state, setState] = useState<LoadingState>({
    isLoading: false,
    progress: 0,
    error: null,
    phase: 'idle',
  });

  const timersRef = useRef<{
    delayTimer?: NodeJS.Timeout;
    minTimeTimer?: NodeJS.Timeout;
  }>({});

  const retriesRef = useRef(0);
  const operationsRef = useRef<Map<string, LoadingOperation<any>>>(new Map());
  const startTimeRef = useRef<number>(0);

  // Clear timers on unmount
  useEffect(() => {
    return () => {
      Object.values(timersRef.current).forEach(timer => {
        if (timer) clearTimeout(timer);
      });
    };
  }, []);

  const updateProgress = useCallback((progress: number) => {
    if (trackProgress) {
      setState(prev => ({ ...prev, progress: Math.min(100, Math.max(0, progress)) }));
    }
  }, [trackProgress]);

  const calculateOverallProgress = useCallback(() => {
    if (!trackProgress || operationsRef.current.size === 0) return 0;

    let totalWeight = 0;
    let completedWeight = 0;

    operationsRef.current.forEach((operation) => {
      const weight = operation.weight || 1;
      totalWeight += weight;
      // Assume operation progress is tracked externally for simplicity
    });

    return totalWeight > 0 ? (completedWeight / totalWeight) * 100 : 0;
  }, [trackProgress]);

  const startLoading = useCallback(async (operation?: () => Promise<any>) => {
    startTimeRef.current = Date.now();
    retriesRef.current = 0;

    // Clear any existing timers
    Object.values(timersRef.current).forEach(timer => {
      if (timer) clearTimeout(timer);
    });

    // Set loading state with delay to prevent flash
    timersRef.current.delayTimer = setTimeout(() => {
      setState(prev => ({
        ...prev,
        isLoading: true,
        progress: 0,
        error: null,
        phase: 'loading',
      }));
    }, loadingDelay);

    if (operation) {
      try {
        const result = await operation();
        finishLoading();
        return result;
      } catch (error) {
        if (enableRetry && retriesRef.current < maxRetries) {
          retriesRef.current++;
          await new Promise(resolve => setTimeout(resolve, retryDelay));
          return startLoading(operation);
        } else {
          setError(error instanceof Error ? error.message : 'An error occurred');
        }
      }
    }
  }, [loadingDelay, enableRetry, maxRetries, retryDelay]);

  const finishLoading = useCallback(() => {
    const elapsed = Date.now() - startTimeRef.current;
    const remainingMinTime = Math.max(0, minLoadTime - elapsed);

    // Update progress to 100% immediately
    if (trackProgress) {
      setState(prev => ({ ...prev, progress: 100 }));
    }

    // Ensure minimum loading time
    timersRef.current.minTimeTimer = setTimeout(() => {
      setState({
        isLoading: false,
        progress: 100,
        error: null,
        phase: 'success',
      });
    }, remainingMinTime);
  }, [minLoadTime, trackProgress]);

  const setError = useCallback((error: string) => {
    // Clear any timers
    Object.values(timersRef.current).forEach(timer => {
      if (timer) clearTimeout(timer);
    });

    setState({
      isLoading: false,
      progress: 0,
      error,
      phase: 'error',
    });
  }, []);

  const reset = useCallback(() => {
    Object.values(timersRef.current).forEach(timer => {
      if (timer) clearTimeout(timer);
    });
    
    operationsRef.current.clear();
    retriesRef.current = 0;
    
    setState({
      isLoading: false,
      progress: 0,
      error: null,
      phase: 'idle',
    });
  }, []);

  const retry = useCallback(() => {
    reset();
    // Note: Would need to store the original operation to retry
    // This is a simplified version
  }, [reset]);

  // Batch operations management
  const addOperation = useCallback(<T>(operation: LoadingOperation<T>) => {
    operationsRef.current.set(operation.id, operation);
    
    operation.promise
      .then(() => {
        operationsRef.current.delete(operation.id);
        updateProgress(calculateOverallProgress());
      })
      .catch(() => {
        operationsRef.current.delete(operation.id);
      });
  }, [updateProgress, calculateOverallProgress]);

  const removeOperation = useCallback((id: string) => {
    operationsRef.current.delete(id);
    updateProgress(calculateOverallProgress());
  }, [updateProgress, calculateOverallProgress]);

  // Smart loading that adapts to content type
  const smartLoad = useCallback(async <T>(
    operation: () => Promise<T>,
    options?: {
      type?: 'instant' | 'fast' | 'normal' | 'slow';
      progressSteps?: number[];
      description?: string;
    }
  ): Promise<T> => {
    const { type = 'normal', progressSteps = [], description } = options || {};

    // Adapt loading behavior based on type
    const loadingConfig = {
      instant: { minLoadTime: 0, loadingDelay: 0 },
      fast: { minLoadTime: 200, loadingDelay: 50 },
      normal: { minLoadTime: 500, loadingDelay: 100 },
      slow: { minLoadTime: 1000, loadingDelay: 150 },
    };

    const config = loadingConfig[type];
    
    return startLoading(async () => {
      if (progressSteps.length > 0 && trackProgress) {
        // Simulate progress steps
        for (let i = 0; i < progressSteps.length; i++) {
          await new Promise(resolve => setTimeout(resolve, 100));
          updateProgress(progressSteps[i]);
        }
      }
      
      const result = await operation();
      return result;
    });
  }, [startLoading, trackProgress, updateProgress]);

  return {
    ...state,
    startLoading,
    finishLoading,
    updateProgress,
    setError,
    reset,
    retry,
    addOperation,
    removeOperation,
    smartLoad,
    // Computed properties
    isRetrying: retriesRef.current > 0,
    retryCount: retriesRef.current,
    canRetry: enableRetry && retriesRef.current < maxRetries,
    hasOperations: operationsRef.current.size > 0,
  };
};

// Hook for managing multiple parallel loading operations
export const useBatchLoading = () => {
  const [batchState, setBatchState] = useState({
    totalOperations: 0,
    completedOperations: 0,
    failedOperations: 0,
    isLoading: false,
  });

  const operations = useRef<Map<string, Promise<any>>>(new Map());

  const addBatchOperation = useCallback(<T>(id: string, promise: Promise<T>) => {
    operations.current.set(id, promise);
    setBatchState(prev => ({
      ...prev,
      totalOperations: prev.totalOperations + 1,
      isLoading: true,
    }));

    promise
      .then(() => {
        setBatchState(prev => {
          const newCompleted = prev.completedOperations + 1;
          const isFinished = newCompleted + prev.failedOperations >= prev.totalOperations;
          
          return {
            ...prev,
            completedOperations: newCompleted,
            isLoading: !isFinished,
          };
        });
      })
      .catch(() => {
        setBatchState(prev => {
          const newFailed = prev.failedOperations + 1;
          const isFinished = prev.completedOperations + newFailed >= prev.totalOperations;
          
          return {
            ...prev,
            failedOperations: newFailed,
            isLoading: !isFinished,
          };
        });
      })
      .finally(() => {
        operations.current.delete(id);
      });

    return promise;
  }, []);

  const resetBatch = useCallback(() => {
    operations.current.clear();
    setBatchState({
      totalOperations: 0,
      completedOperations: 0,
      failedOperations: 0,
      isLoading: false,
    });
  }, []);

  const progress = batchState.totalOperations > 0 
    ? ((batchState.completedOperations + batchState.failedOperations) / batchState.totalOperations) * 100
    : 0;

  return {
    ...batchState,
    progress,
    addBatchOperation,
    resetBatch,
    successRate: batchState.totalOperations > 0 
      ? (batchState.completedOperations / batchState.totalOperations) * 100 
      : 0,
  };
};

// Hook for perceived performance optimizations
export const usePerceivedPerformance = () => {
  const [metrics, setMetrics] = useState({
    startTime: 0,
    firstContentfulPaint: 0,
    loadComplete: 0,
    userInteractionTime: 0,
  });

  const markStart = useCallback(() => {
    setMetrics(prev => ({
      ...prev,
      startTime: performance.now(),
    }));
  }, []);

  const markFirstContent = useCallback(() => {
    setMetrics(prev => ({
      ...prev,
      firstContentfulPaint: performance.now(),
    }));
  }, []);

  const markComplete = useCallback(() => {
    setMetrics(prev => ({
      ...prev,
      loadComplete: performance.now(),
    }));
  }, []);

  const markUserInteraction = useCallback(() => {
    setMetrics(prev => ({
      ...prev,
      userInteractionTime: performance.now(),
    }));
  }, []);

  const getTimings = useCallback(() => ({
    timeToFirstContent: metrics.firstContentfulPaint - metrics.startTime,
    timeToComplete: metrics.loadComplete - metrics.startTime,
    timeToInteractive: metrics.userInteractionTime - metrics.startTime,
  }), [metrics]);

  return {
    markStart,
    markFirstContent,
    markComplete,
    markUserInteraction,
    getTimings,
    metrics,
  };
};