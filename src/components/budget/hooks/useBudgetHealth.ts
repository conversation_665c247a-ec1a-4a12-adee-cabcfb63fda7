import { useMemo } from 'react';
import { format } from 'date-fns';
import { BudgetStats, HealthMetrics } from '../types/budget.types';
import { Budget } from '@/stores/budgetStore';

export const useBudgetHealth = (
  stats: BudgetStats | null,
  budget: Budget | null,
  year: number
): HealthMetrics | null => {
  return useMemo(() => {
    if (!stats || !budget) return null;

    const totalBudget = budget.total_amount || stats.total || 0;
    const totalSpent = stats.spent || 0;
    const utilization = totalBudget > 0 ? (totalSpent / totalBudget) * 100 : 0;
    
    // Calculate burn rate (actual vs expected spending rate)
    const currentMonth = new Date().getMonth() + 1;
    const expectedSpent = (totalBudget / 12) * currentMonth;
    const burnRate = expectedSpent > 0 ? totalSpent / expectedSpent : 1;
    
    // Calculate projected spend
    const monthlyRate = currentMonth > 0 ? totalSpent / currentMonth : 0;
    const projectedSpend = monthlyRate * 12;
    
    // Risk score calculation
    let riskScore = 0;
    if (utilization > 100) riskScore += 40;
    else if (utilization > 90) riskScore += 25;
    if (burnRate > 1.5) riskScore += 30;
    else if (burnRate > 1.2) riskScore += 15;
    if (projectedSpend > totalBudget * 1.1) riskScore += 30;
    
    // Efficiency score (how well budget is being used)
    const efficiencyScore = Math.min(100, Math.max(0, 
      100 - Math.abs(burnRate - 1) * 50 - (utilization > 100 ? 50 : 0)
    ));
    
    // Compliance rate (placeholder - in real app would check policy compliance)
    const complianceRate = 95;
    
    // Savings opportunity
    const savingsOpportunity = Math.max(0, (totalBudget - projectedSpend) / totalBudget * 100);
    
    // Category distribution
    const categoryDistribution: Record<string, number> = {};
    stats.by_category?.forEach((cat) => {
      categoryDistribution[cat.category] = cat.percentage;
    });
    
    // Monthly trend
    const monthlyTrend = Array.from({ length: currentMonth }, (_, i) => ({
      month: format(new Date(year, i, 1), 'MMM'),
      spent: (totalSpent / currentMonth) * (i + 1),
      budget: (totalBudget / 12) * (i + 1)
    }));

    // Health score calculation
    let healthScore = 100;
    const expectedUtilization = (currentMonth / 12) * 100;
    const utilizationDiff = utilization - expectedUtilization;
    
    if (utilizationDiff > 20) healthScore -= 40;
    else if (utilizationDiff > 10) healthScore -= 25;
    else if (utilizationDiff > 5) healthScore -= 15;
    
    if (burnRate > 1.3) healthScore -= 30;
    else if (burnRate > 1.15) healthScore -= 20;
    else if (burnRate > 1.05) healthScore -= 10;
    
    if (utilizationDiff < -10) healthScore += 10;
    
    healthScore = Math.max(0, Math.min(100, healthScore));

    // Determine status
    let status: 'excellent' | 'good' | 'warning' | 'critical' | 'unknown' = 'unknown';
    if (healthScore >= 80) status = 'excellent';
    else if (healthScore >= 60) status = 'good';
    else if (healthScore >= 40) status = 'warning';
    else status = 'critical';

    // Generate recommendations
    const recommendations = [];
    if (burnRate > 1.2) {
      recommendations.push({
        type: 'warning' as const,
        message: 'Spending rate is higher than planned. Consider reviewing expenses.',
        action: 'review-expenses'
      });
    }
    if (utilization > 80) {
      recommendations.push({
        type: 'info' as const,
        message: 'Budget utilization is high. Plan for next period.',
        action: 'plan-next-period'
      });
    }
    if (savingsOpportunity > 20) {
      recommendations.push({
        type: 'success' as const,
        message: `Potential savings of ${savingsOpportunity.toFixed(0)}% identified.`,
        action: 'optimize-spending'
      });
    }
    
    return {
      totalBudget,
      totalSpent,
      utilization,
      burnRate,
      projectedSpend,
      riskScore,
      savingsOpportunity,
      complianceRate,
      efficiencyScore,
      categoryDistribution,
      monthlyTrend,
      score: healthScore,
      status,
      indicators: {
        spending: {
          value: utilization,
          status: utilization > 90 ? 'critical' : utilization > 70 ? 'warning' : 'healthy',
          trend: burnRate > 1 ? 'increasing' : 'stable'
        },
        efficiency: {
          value: efficiencyScore,
          status: efficiencyScore > 70 ? 'healthy' : efficiencyScore > 50 ? 'warning' : 'critical',
          trend: 'stable'
        },
        risk: {
          value: riskScore,
          status: riskScore > 60 ? 'critical' : riskScore > 30 ? 'warning' : 'healthy',
          trend: riskScore > 50 ? 'increasing' : 'stable'
        },
        compliance: {
          value: complianceRate,
          status: complianceRate > 90 ? 'healthy' : complianceRate > 80 ? 'warning' : 'critical',
          trend: 'stable'
        }
      },
      recommendations
    };
  }, [stats, budget, year]);
};