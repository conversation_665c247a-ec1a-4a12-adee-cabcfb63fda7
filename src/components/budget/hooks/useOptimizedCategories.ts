import { useMemo } from 'react';
import { CategoryTotal } from '@/stores/budgetStore';
import { sortCategoriesBySpending } from '../utils/budgetHelpers';

export const useOptimizedCategories = (categories: CategoryTotal[]) => {
  const sortedCategories = useMemo(
    () => sortCategoriesBySpending(categories),
    [categories]
  );

  const categoryStats = useMemo(() => {
    const totalSpending = categories.reduce((sum, cat) => sum + cat.total, 0);
    const highestSpending = Math.max(...categories.map(cat => cat.total), 0);
    const averageSpending = categories.length > 0 ? totalSpending / categories.length : 0;

    return {
      totalSpending,
      highestSpending,
      averageSpending,
      categoryCount: categories.length,
    };
  }, [categories]);

  return {
    sortedCategories,
    categoryStats,
  };
};