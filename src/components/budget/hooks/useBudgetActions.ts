import { useCallback } from 'react';
import { useSetBudget } from '@/hooks/queries/useBudgetQueries';
import { validateBudgetAmount } from '../utils/budgetHelpers';

interface UseBudgetActionsReturn {
  updateBudget: (year: number, amount: number) => Promise<{ success: boolean; error?: string }>;
  isUpdating: boolean;
}

/**
 * Hook for budget actions using React Query mutations
 * This replaces the previous store-based implementation
 */
export const useBudgetActions = (): UseBudgetActionsReturn => {
  const setBudgetMutation = useSetBudget();

  const updateBudget = useCallback(async (year: number, amount: number) => {
    const validation = validateBudgetAmount(amount);
    
    if (!validation.isValid) {
      return { success: false, error: validation.error };
    }

    try {
      await setBudgetMutation.mutateAsync({ year, totalAmount: amount });
      return { success: true };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to update budget' 
      };
    }
  }, [setBudgetMutation]);

  return {
    updateBudget,
    isUpdating: setBudgetMutation.isPending,
  };
};