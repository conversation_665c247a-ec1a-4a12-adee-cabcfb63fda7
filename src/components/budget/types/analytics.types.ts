// Analytics Types

export interface TrendDataPoint {
  month: string;
  spent: number;
  budget: number;
  categories?: Record<string, number>;
}

export interface YearlyTrend {
  year: number;
  budget: number;
  spent: number;
  growth?: number;
}

export interface CategoryInsight {
  category: string;
  amount: number;
  percentage: number;
}

export interface AnalyticsData {
  trends: {
    monthly?: TrendDataPoint[];
    yearly?: YearlyTrend[];
  };
  insights?: {
    top_categories?: CategoryInsight[];
    efficiency_score?: number;
    saving_opportunities?: number;
  };
}

export interface ROIMetric {
  id: string;
  name: string;
  investment: number;
  returnValue: number;
  roi: number;
  category: string;
  status: 'active' | 'completed' | 'pending';
  startDate?: string;
  endDate?: string;
  description?: string;
}

export interface BurndownDataPoint {
  date: string;
  budget: number;
  spent: number;
  projected: number;
  remaining: number;
}

export interface YearComparisonData {
  category: string;
  [year: string]: number | string;
}

export interface YearSummary {
  year: number;
  totalBudget: number;
  totalSpent: number;
  efficiency: number;
  growth: number;
  categories: Record<string, number>;
  departments: Record<string, number>;
}

export interface ForecastData {
  month: string;
  actual: number;
  budget: number;
  categories: Record<string, number>;
}