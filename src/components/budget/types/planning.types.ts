// Planning and Template Types

export interface BudgetTemplate {
  id: string;
  name: string;
  description: string;
  totalAmount: number;
  allocations: {
    quarterly?: QuarterlyAllocation[];
    departments?: DepartmentTemplateAllocation[];
    categories?: CategoryTemplateLimit[];
  };
  rules?: AllocationRuleTemplate[];
  tags?: string[];
  isDefault?: boolean;
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
}

export interface QuarterlyAllocation {
  quarter: number;
  amount: number;
  percentage: number;
}

export interface DepartmentTemplateAllocation {
  departmentName: string;
  amount: number;
  percentage: number;
}

export interface CategoryTemplateLimit {
  category: string;
  limit: number;
  isHardLimit: boolean;
}

export interface AllocationRuleTemplate {
  name: string;
  type: 'percentage' | 'fixed' | 'dynamic';
  value: number;
  condition?: string;
  enabled: boolean;
}

export interface Scenario {
  id: string;
  name: string;
  description: string;
  baselineId?: string;
  adjustments: {
    budgetChange: number;
    categoryChanges: Record<string, number>;
    departmentChanges: Record<string, number>;
  };
  results?: ScenarioResults;
  createdAt: string;
  updatedAt: string;
}

export interface ScenarioResults {
  totalBudget: number;
  categoryAllocations: Record<string, number>;
  departmentAllocations: Record<string, number>;
  feasibility: 'viable' | 'risky' | 'not-recommended';
  warnings: string[];
  opportunities: string[];
  score: number;
}

export interface ReserveFund {
  id: string;
  name: string;
  targetAmount: number;
  currentAmount: number;
  percentage: number;
  fundType?: 'emergency' | 'capital' | 'operational' | 'contingency';
  autoContribute?: boolean;
  contributionPercentage?: number;
  status: 'active' | 'inactive' | 'locked';
  rules?: ReserveFundRules;
  transactions: ReserveTransaction[];
  projections?: ReserveProjections;
}

export interface ReserveFundRules {
  minBalance: number;
  maxBalance: number;
  autoContribute: boolean;
  contributionPercentage?: number;
  eligibleCategories: string[];
}

export interface ReserveTransaction {
  id: string;
  type: 'deposit' | 'withdrawal';
  amount: number;
  date: string;
  description: string;
  category: string;
  approvedBy?: string;
  status: 'pending' | 'completed' | 'rejected';
  attachments?: string[];
}

export interface ReserveProjections {
  monthlyContribution: number;
  targetDate: string;
  riskLevel: 'low' | 'medium' | 'high';
}

export interface CarryoverRule {
  id: string;
  name: string;
  type: 'percentage' | 'fixed' | 'category-based';
  value?: number;
  categories?: string[];
  maxCarryover?: number;
  expirationMonths?: number;
  autoExecute: boolean;
  enabled: boolean;
  lastExecuted?: string;
  nextExecution?: string;
}

export interface CarryoverHistory {
  id: string;
  ruleId: string;
  amount: number;
  fromYear: number;
  toYear: number;
  executedAt: string;
  executedBy: string;
  details: Record<string, number>;
}

export interface CarryoverSimulation {
  totalUnspent: number;
  projectedCarryover: number;
  byCategory: Record<string, number>;
}