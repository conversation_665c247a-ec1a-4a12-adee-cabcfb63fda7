import { QuarterlyAllocation, DepartmentAllocation, CategoryLimit } from '@/stores/budgetStore';

export interface AllocationFormData {
  quarterly: QuarterlyAllocation[];
  departments: DepartmentAllocation[];
  categoryLimits: CategoryLimit[];
}

export interface QuarterlyBreakdownProps {
  allocations: QuarterlyAllocation[];
  totalBudget: number;
  onUpdate: (allocations: QuarterlyAllocation[]) => Promise<void>;
  loading?: boolean;
}

export interface DepartmentAllocationProps {
  departments: DepartmentAllocation[];
  totalBudget: number;
  onUpdate: (departmentId: string, amount: number) => Promise<void>;
  onAdd: (name: string, amount: number) => Promise<void>;
  onRemove: (departmentId: string) => Promise<void>;
  loading?: boolean;
}

export interface CategoryLimitsProps {
  limits: CategoryLimit[];
  categories: string[];
  onSetLimit: (category: string, limit: number) => Promise<void>;
  onRemoveLimit: (category: string) => Promise<void>;
  loading?: boolean;
}

export interface AllocationValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export const validateAllocations = (
  allocations: AllocationFormData,
  totalBudget: number
): AllocationValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Validate quarterly allocations
  const quarterlySum = allocations.quarterly.reduce((sum, q) => sum + q.percentage, 0);
  if (Math.abs(quarterlySum - 100) > 0.01) {
    errors.push('Quarterly allocations must sum to 100%');
  }

  // Validate department allocations
  const departmentSum = allocations.departments.reduce((sum, d) => sum + d.allocated_amount, 0);
  if (departmentSum > totalBudget) {
    errors.push('Department allocations exceed total budget');
  } else if (departmentSum < totalBudget * 0.9) {
    warnings.push('Less than 90% of budget is allocated to departments');
  }

  // Validate category limits
  const categoryLimitSum = allocations.categoryLimits.reduce((sum, c) => sum + c.limit_amount, 0);
  if (categoryLimitSum > totalBudget) {
    warnings.push('Sum of category limits exceeds total budget');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};

export interface AutoAllocationRules {
  useHistoricalData: boolean;
  favoriteCategories: string[];
  minDepartmentAllocation: number;
  maxCategoryPercentage: number;
  reservePercentage: number;
}

export const defaultAutoAllocationRules: AutoAllocationRules = {
  useHistoricalData: true,
  favoriteCategories: [],
  minDepartmentAllocation: 0.05, // 5% minimum per department
  maxCategoryPercentage: 0.3, // 30% max per category
  reservePercentage: 0.1, // 10% reserve fund
};