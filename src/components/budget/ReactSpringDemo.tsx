import React, { useState, useEffect } from 'react';
import { useSpring, animated, useTransition, useTrail } from '@react-spring/web';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  DollarSign, 
  TrendingUp, 
  TrendingDown, 
  Activity,
  RefreshCw,
  Play,
  Pause
} from 'lucide-react';

import {
  AnimatedBudgetSummary,
  AnimatedBudgetProgress,
  AnimatedExpenseList,
  AnimatedBudgetCard
} from './components';

// Sample data for demonstration
const sampleBudgetSummary = {
  totalBudget: 150000,
  totalSpent: 87500,
  totalRemaining: 62500,
  utilization: 58.3,
  departmentCount: 8,
  activeCategories: 12,
  monthlyAverage: 7291.67,
  lastUpdated: new Date().toISOString()
};

const sampleBudgetProgress = [
  {
    category: 'Training Materials',
    allocated: 25000,
    spent: 18000,
    remaining: 7000,
    percentage: 72,
    status: 'warning' as const
  },
  {
    category: 'Instructor Fees',
    allocated: 45000,
    spent: 35000,
    remaining: 10000,
    percentage: 77.8,
    status: 'warning' as const
  },
  {
    category: 'Venue Costs',
    allocated: 30000,
    spent: 15000,
    remaining: 15000,
    percentage: 50,
    status: 'healthy' as const
  },
  {
    category: 'Technology',
    allocated: 35000,
    spent: 12000,
    remaining: 23000,
    percentage: 34.3,
    status: 'healthy' as const
  },
  {
    category: 'Travel & Accommodation',
    allocated: 15000,
    spent: 7500,
    remaining: 7500,
    percentage: 50,
    status: 'healthy' as const
  }
];

const sampleExpenses = [
  {
    id: '1',
    title: 'Q1 Training Materials',
    amount: 5000,
    category: 'Training Materials',
    date: '2024-01-15',
    status: 'approved' as const,
    description: 'Materials for Q1 training sessions'
  },
  {
    id: '2',
    title: 'Instructor Workshop',
    amount: 8000,
    category: 'Instructor Fees',
    date: '2024-01-20',
    status: 'paid' as const,
    description: 'Advanced training workshop for instructors'
  },
  {
    id: '3',
    title: 'Online Platform Subscription',
    amount: 2500,
    category: 'Technology',
    date: '2024-01-25',
    status: 'pending' as const,
    description: 'Quarterly subscription for learning platform'
  },
  {
    id: '4',
    title: 'Venue Booking',
    amount: 3000,
    category: 'Venue Costs',
    date: '2024-02-01',
    status: 'approved' as const,
    description: 'Training room booking for February'
  },
  {
    id: '5',
    title: 'Travel Reimbursement',
    amount: 1500,
    category: 'Travel & Accommodation',
    date: '2024-02-05',
    status: 'paid' as const,
    description: 'Travel expenses for training trip'
  }
];

const ReactSpringDemo: React.FC = () => {
  const [isPlaying, setIsPlaying] = useState(true);
  const [demoData, setDemoData] = useState({
    summary: sampleBudgetSummary,
    progress: sampleBudgetProgress,
    expenses: sampleExpenses
  });

  // Animation controls
  const [animationSpeed, setAnimationSpeed] = useState(1);
  
  // Animated demo title
  const titleSpring = useSpring({
    from: { opacity: 0, transform: 'translateY(-20px)' },
    to: { opacity: 1, transform: 'translateY(0px)' },
    config: { tension: 200, friction: 20 },
    delay: 100
  });

  // Animated controls
  const controlsSpring = useSpring({
    from: { opacity: 0, transform: 'translateY(20px)' },
    to: { opacity: 1, transform: 'translateY(0px)' },
    config: { tension: 200, friction: 20 },
    delay: 200
  });

  // Simulate data updates for animation demo
  useEffect(() => {
    if (!isPlaying) return;

    const interval = setInterval(() => {
      setDemoData(prev => ({
        ...prev,
        summary: {
          ...prev.summary,
          totalSpent: prev.summary.totalSpent + Math.random() * 1000 - 500,
          utilization: Math.max(0, Math.min(100, prev.summary.utilization + (Math.random() * 2 - 1)))
        }
      }));
    }, 2000 / animationSpeed);

    return () => clearInterval(interval);
  }, [isPlaying, animationSpeed]);

  const resetData = () => {
    setDemoData({
      summary: sampleBudgetSummary,
      progress: sampleBudgetProgress,
      expenses: sampleExpenses
    });
  };

  const randomizeData = () => {
    setDemoData({
      summary: {
        ...sampleBudgetSummary,
        totalSpent: Math.random() * sampleBudgetSummary.totalBudget,
        utilization: Math.random() * 100,
        departmentCount: Math.floor(Math.random() * 10) + 5,
        activeCategories: Math.floor(Math.random() * 15) + 5
      },
      progress: sampleBudgetProgress.map(item => ({
        ...item,
        spent: Math.random() * item.allocated,
        percentage: Math.random() * 100
      })),
      expenses: sampleExpenses.map(expense => ({
        ...expense,
        amount: Math.random() * 10000 + 1000
      }))
    });
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Demo Header */}
        <animated.div style={titleSpring}>
          <Card>
            <CardHeader>
              <CardTitle className="text-2xl font-bold flex items-center gap-2">
                <Activity className="h-6 w-6" />
                React Spring Budget Animation Demo
              </CardTitle>
              <p className="text-muted-foreground">
                Interactive demonstration of smooth animations using React Spring
              </p>
            </CardHeader>
            <CardContent>
              <animated.div style={controlsSpring} className="flex items-center gap-4">
                <Button
                  variant={isPlaying ? "outline" : "default"}
                  size="sm"
                  onClick={() => setIsPlaying(!isPlaying)}
                >
                  {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                  {isPlaying ? 'Pause' : 'Play'} Animations
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={resetData}
                >
                  <RefreshCw className="h-4 w-4" />
                  Reset Data
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={randomizeData}
                >
                  <TrendingUp className="h-4 w-4" />
                  Randomize Data
                </Button>
                
                <div className="flex items-center gap-2">
                  <label className="text-sm font-medium">Speed:</label>
                  <input
                    type="range"
                    min="0.5"
                    max="3"
                    step="0.1"
                    value={animationSpeed}
                    onChange={(e) => setAnimationSpeed(parseFloat(e.target.value))}
                    className="w-20"
                  />
                  <span className="text-sm text-muted-foreground">{animationSpeed}x</span>
                </div>
              </animated.div>
            </CardContent>
          </Card>
        </animated.div>

        {/* Tabs for different views */}
        <Tabs defaultValue="summary" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="summary">Summary</TabsTrigger>
            <TabsTrigger value="progress">Progress</TabsTrigger>
            <TabsTrigger value="expenses">Expenses</TabsTrigger>
            <TabsTrigger value="cards">Cards</TabsTrigger>
          </TabsList>

          <TabsContent value="summary" className="space-y-4">
            <AnimatedBudgetSummary
              summary={demoData.summary}
              loading={false}
              onRefresh={randomizeData}
            />
          </TabsContent>

          <TabsContent value="progress" className="space-y-4">
            <AnimatedBudgetProgress
              budgetProgress={demoData.progress}
              totalBudget={demoData.summary.totalBudget}
              totalSpent={demoData.summary.totalSpent}
              showDetails={true}
            />
          </TabsContent>

          <TabsContent value="expenses" className="space-y-4">
            <AnimatedExpenseList
              expenses={demoData.expenses}
              loading={false}
              onExpenseClick={(expense) => {
              }}
              onStatusChange={(id, status) => {
              }}
            />
          </TabsContent>

          <TabsContent value="cards" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {demoData.progress.map((item, index) => (
                <AnimatedBudgetCard
                  key={item.category}
                  title={item.category}
                  amount={item.spent}
                  total={item.allocated}
                  percentage={item.percentage}
                  icon={<DollarSign className="h-4 w-4" />}
                  color={
                    item.percentage >= 90 ? 'text-red-600' :
                    item.percentage >= 75 ? 'text-yellow-600' : 'text-green-600'
                  }
                  trend={Math.random() * 20 - 10}
                />
              ))}
            </div>
          </TabsContent>
        </Tabs>

        {/* Performance Metrics */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Animation Performance</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">60 FPS</div>
                <p className="text-sm text-muted-foreground">Target Frame Rate</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">Smooth</div>
                <p className="text-sm text-muted-foreground">Animation Quality</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">React Spring</div>
                <p className="text-sm text-muted-foreground">Animation Library</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Code Examples */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Usage Examples</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h3 className="font-medium mb-2">Basic Usage</h3>
                <pre className="bg-gray-100 p-3 rounded-md text-sm overflow-x-auto">
{`<AnimatedBudgetSummary
  summary={budgetData}
  loading={isLoading}
  onRefresh={handleRefresh}
/>`}
                </pre>
              </div>
              
              <div>
                <h3 className="font-medium mb-2">With Progress Tracking</h3>
                <pre className="bg-gray-100 p-3 rounded-md text-sm overflow-x-auto">
{`<AnimatedBudgetProgress
  budgetProgress={categories}
  totalBudget={totalBudget}
  totalSpent={totalSpent}
  showDetails={true}
/>`}
                </pre>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ReactSpringDemo;