import React from 'react';
import ReactDOM from 'react-dom/client';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import ReactSpringDemo from './ReactSpringDemo';
import { BasicAnimatedSummary } from './examples/ReactSpringExamples';

// Test component to verify React Spring integration
const ReactSpringTest: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-center mb-8">
          React Spring Integration Test
        </h1>
        
        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">✅ Integration Status</h2>
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <span className="text-green-500">✓</span>
              <span>@react-spring/web installed</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-green-500">✓</span>
              <span>AnimatedBudgetSummary component created</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-green-500">✓</span>
              <span>AnimatedBudgetProgress component created</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-green-500">✓</span>
              <span>AnimatedExpenseList component created</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-green-500">✓</span>
              <span>AnimatedBudgetCard component created</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-green-500">✓</span>
              <span>TrainingBudget.tsx updated with animations</span>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Demo Components</h2>
          <div className="space-y-4">
            <p className="text-gray-600">
              The following components demonstrate React Spring animations:
            </p>
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li>Animated budget summary cards</li>
              <li>Smooth progress bar transitions</li>
              <li>Interactive expense list animations</li>
              <li>Real-time data update animations</li>
              <li>Hover effects and micro-interactions</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

// Quick test component
export const QuickTest: React.FC = () => {
  return (
    <div className="p-4">
      <BasicAnimatedSummary />
    </div>
  );
};

// Mount test
export const mountTest = () => {
  const queryClient = new QueryClient();
  
  const root = ReactDOM.createRoot(
    document.getElementById('root') as HTMLElement
  );
  
  root.render(
    <QueryClientProvider client={queryClient}>
      <ReactSpringTest />
    </QueryClientProvider>
  );
};

export default ReactSpringTest;