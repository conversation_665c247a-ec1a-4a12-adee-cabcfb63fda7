import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  TrendingUp,
  AlertCircle,
  Wallet,
  Target,
  Calendar,
  PieChart,
  Activity,
  ArrowUpRight,
  ArrowDownRight,
  Filter,
  Download,
  ChevronRight
} from 'lucide-react';
import { SpendingHeatmap } from '../components/heatmap';
import { 
  BudgetStats, 
  CategoryLimit, 
  HeatmapData,
  BudgetAction,
  HealthMetrics 
} from '../types/budget.types';
import { useBudgetCalculations, useBudgetMetrics } from './hooks/useBudgetCalculations';

interface BudgetOverviewProps {
  stats: BudgetStats;
  categoryLimits: CategoryLimit[];
  loading?: boolean;
  onViewDetails?: (section: string) => void;
  onQuickAction?: (action: BudgetAction) => void;
  heatmapData?: HeatmapData;
  year?: number;
  onHeatmapDayClick?: (date: string, data: any) => void;
  onHeatmapExport?: (format: string) => void;
}

export const BudgetOverview: React.FC<BudgetOverviewProps> = ({
  stats,
  categoryLimits,
  loading = false,
  onViewDetails,
  onQuickAction,
  heatmapData,
  year = new Date().getFullYear(),
  onHeatmapDayClick,
  onHeatmapExport
}) => {
  // Use custom hooks for calculations
  const calculations = useBudgetCalculations(
    stats.total,
    stats.spent,
    stats.committed
  );
  
  const healthMetrics = useBudgetMetrics(stats);

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getSpendingTrend = (): number => {
    if (!stats?.trend) return 0;
    return Math.abs(stats.trend);
  };

  if (loading) {
    return <LoadingSkeleton />;
  }

  return (
    <div className="space-y-4">
      {/* Section 1: Primary KPIs - Hero Metrics with Better Hierarchy */}
      <div className="grid grid-cols-12 gap-4">
        {/* Budget Health Card - Primary Focus (8 columns) */}
        <Card className="col-span-12 lg:col-span-8 bg-gradient-to-br from-primary/5 via-transparent to-transparent border-primary/20 shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Activity className="h-4 w-4 text-primary" />
                <CardTitle className="text-base font-semibold">Budget Health</CardTitle>
              </div>
              <Badge 
                variant={getHealthBadgeVariant(healthMetrics.score)}
                className="text-xs"
              >
                {getHealthStatus(healthMetrics.score)}
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-end justify-between">
                <div className="space-y-0.5">
                  <p className="text-2xl font-bold tracking-tight">{formatCurrency(calculations.remaining)}</p>
                  <p className="text-xs text-muted-foreground font-medium">Remaining Budget</p>
                </div>
                <div className="text-right space-y-0.5">
                  <p className="text-lg font-semibold">{calculations.utilization.toFixed(1)}%</p>
                  <p className="text-xs text-muted-foreground font-medium">Utilized</p>
                </div>
              </div>
              <Progress value={calculations.utilization} className="h-2" />
              {/* Removed budget details grid */}
            </div>
          </CardContent>
        </Card>

        {/* Spending Trend Card - Secondary (4 columns) */}
        <Card className="col-span-12 lg:col-span-4 border-l-4 border-l-blue-500">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-semibold">Spending Trend</CardTitle>
              <TrendingUp className="h-3.5 w-3.5 text-muted-foreground" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-baseline gap-1.5">
                <p className="text-xl font-bold tracking-tight">
                  {stats.trend > 0 ? '+' : '-'}{getSpendingTrend()}%
                </p>
                {stats.trend > 0 ? (
                  <ArrowUpRight className="h-5 w-5 text-red-500" />
                ) : (
                  <ArrowDownRight className="h-5 w-5 text-green-500" />
                )}
              </div>
              <p className="text-xs text-muted-foreground">vs. last month</p>
              <div className="pt-2">
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="w-full"
                  onClick={() => onViewDetails?.('analytics')}
                >
                  View Analytics
                  <ChevronRight className="h-4 w-4 ml-1" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Section 2: Quick Stats Grid - Compact Secondary Metrics */}
      <div className="grid grid-cols-12 gap-3">
        <div className="col-span-12">
          <QuickStatsGrid 
            stats={stats}
            calculations={calculations}
            categoryLimits={categoryLimits}
          />
        </div>
      </div>

      {/* Section 3: Spending Heatmap - Full Width Visual Focus */}
      {heatmapData && (
        <div className="w-full mt-6">
          <SpendingHeatmap
            data={heatmapData}
            currentMonth={new Date(year, new Date().getMonth(), 1)}
            onDayClick={onHeatmapDayClick || ((date, data) => {
              console.log('Day clicked:', date, data);
            })}
            onExport={onHeatmapExport || ((format) => {
              console.log('Export heatmap:', format);
            })}
            loading={loading}
          />
        </div>
      )}

      {/* Section 4: Main Content Grid - Asymmetric Layout for Hierarchy */}
      <div className="grid grid-cols-12 gap-4">
        {/* Category Breakdown - Primary Content (7 columns) */}
        <div className="col-span-12 lg:col-span-7">
          <CategoryBreakdown 
            categories={stats.by_category}
            categoryLimits={categoryLimits}
            onViewDetails={onViewDetails}
          />
        </div>

        {/* Budget Summary - Supporting Info (5 columns) */}
        <div className="col-span-12 lg:col-span-5">
          <BudgetSummaryCard 
            stats={stats}
            calculations={calculations}
            onViewDetails={onViewDetails}
          />
        </div>
      </div>

      {/* Section 5: Action Cards */}
      <ActionCards onQuickAction={onQuickAction} />
    </div>
  );
};

// Sub-components
const LoadingSkeleton: React.FC = () => (
  <div className="grid gap-6">
    {[1, 2, 3, 4].map((i) => (
      <Skeleton key={i} className="h-32 w-full" />
    ))}
  </div>
);

const QuickStatsGrid: React.FC<{
  stats: BudgetStats;
  calculations: ReturnType<typeof useBudgetCalculations>;
  categoryLimits: CategoryLimit[];
}> = ({ stats, calculations, categoryLimits }) => {
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
      {/* Average Daily Spend */}
      <Card className="hover:shadow-sm transition-all duration-200 border-l-2 border-l-transparent hover:border-l-primary">
        <CardContent className="p-3">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <p className="text-xs text-muted-foreground font-medium">Daily Average</p>
              <p className="text-lg font-semibold">
                {formatCurrency(stats.spent / 30)}
              </p>
              <p className="text-xs text-green-600">On track</p>
            </div>
            <div className="p-1.5 bg-primary/10 rounded">
              <Calendar className="h-4 w-4 text-primary" />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Top Category */}
      <Card className="hover:shadow-sm transition-all duration-200 border-l-2 border-l-transparent hover:border-l-orange-500">
        <CardContent className="p-3">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <p className="text-xs text-muted-foreground font-medium">Top Category</p>
              <p className="text-lg font-semibold truncate">
                {stats.by_category?.[0]?.category || 'N/A'}
              </p>
              <p className="text-xs text-orange-600">
                {formatCurrency(stats.by_category?.[0]?.amount || 0)}
              </p>
            </div>
            <div className="p-1.5 bg-orange-500/10 rounded">
              <PieChart className="h-4 w-4 text-orange-500" />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Budget Progress */}
      <Card className="hover:shadow-sm transition-all duration-200 border-l-2 border-l-transparent hover:border-l-green-500">
        <CardContent className="p-3">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <p className="text-xs text-muted-foreground font-medium">Progress</p>
              <p className="text-lg font-semibold">{Math.round(calculations.utilization)}%</p>
              <p className="text-xs text-muted-foreground">This month</p>
            </div>
            <div className="p-1.5 bg-green-500/10 rounded">
              <Activity className="h-4 w-4 text-green-500" />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Budget Goals */}
      <Card className="hover:shadow-sm transition-all duration-200 border-l-2 border-l-transparent hover:border-l-blue-500">
        <CardContent className="p-3">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <p className="text-xs text-muted-foreground font-medium">Active Goals</p>
              <p className="text-lg font-semibold">{categoryLimits.length}</p>
              <p className="text-xs text-blue-600">Categories set</p>
            </div>
            <div className="p-1.5 bg-blue-500/10 rounded">
              <Target className="h-4 w-4 text-blue-500" />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

const CategoryBreakdown: React.FC<{
  categories: BudgetStats['by_category'];
  categoryLimits: CategoryLimit[];
  onViewDetails?: (section: string) => void;
}> = ({ categories, categoryLimits, onViewDetails }) => {
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <Card className="h-full">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-sm font-semibold">Category Breakdown</CardTitle>
            <p className="text-xs text-muted-foreground mt-0.5">Top spending categories</p>
          </div>
          <Button variant="ghost" size="sm" className="h-7 px-2">
            <Filter className="h-3.5 w-3.5" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-3">
          {categories?.slice(0, 5).map((category, index) => {
            const limit = categoryLimits.find(l => l.category === category.category);
            const percentage = limit ? (category.amount / limit.limit) * 100 : 0;
            
            return (
              <div key={index} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">{category.category}</span>
                    {percentage > 90 && (
                      <AlertCircle className="h-3 w-3 text-orange-500" />
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-muted-foreground">
                      {formatCurrency(category.amount)}
                    </span>
                    {limit && (
                      <span className="text-xs text-muted-foreground">
                        / {formatCurrency(limit.limit)}
                      </span>
                    )}
                  </div>
                </div>
                <Progress 
                  value={percentage} 
                  className="h-2"
                />
              </div>
            );
          })}
        </div>
        <Button 
          variant="outline" 
          className="w-full mt-4"
          onClick={() => onViewDetails?.('categories')}
        >
          View All Categories
        </Button>
      </CardContent>
    </Card>
  );
};

const BudgetSummaryCard: React.FC<{
  stats: BudgetStats;
  calculations: ReturnType<typeof useBudgetCalculations>;
  onViewDetails?: (section: string) => void;
}> = ({ stats, calculations, onViewDetails }) => {
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <Card className="h-full">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-semibold">Budget Summary</CardTitle>
          <Badge variant="secondary" className="text-xs px-2 py-0.5">Q{Math.ceil((new Date().getMonth() + 1) / 3)}</Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Allocated</span>
              <span className="font-medium">{formatCurrency(stats.total)}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Utilized</span>
              <span className="font-medium text-orange-600">{formatCurrency(stats.spent)}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Committed</span>
              <span className="font-medium text-blue-600">{formatCurrency(stats.committed)}</span>
            </div>
            <div className="border-t pt-2 mt-2">
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground font-medium">Available</span>
                <span className="font-bold text-green-600">{formatCurrency(calculations.remaining)}</span>
              </div>
            </div>
          </div>
          <div className="pt-2">
            <div className="flex items-center justify-between mb-2">
              <span className="text-xs text-muted-foreground">Utilization Rate</span>
              <span className="text-xs font-medium">{calculations.utilization.toFixed(1)}%</span>
            </div>
            <Progress value={calculations.utilization} className="h-2" />
          </div>
        </div>
        <Button 
          variant="outline" 
          className="w-full mt-4"
          onClick={() => onViewDetails?.('budget')}
        >
          View Budget Details
        </Button>
      </CardContent>
    </Card>
  );
};

const ActionCards: React.FC<{
  onQuickAction?: (action: BudgetAction) => void;
}> = ({ onQuickAction }) => (
  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
    <Card className="border-dashed hover:border-solid transition-all cursor-pointer hover:shadow-md"
          onClick={() => onQuickAction?.('allocate-budget')}>
      <CardContent className="p-6 text-center">
        <Wallet className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
        <p className="font-medium">Allocate Budget</p>
        <p className="text-xs text-muted-foreground mt-1">Distribute funds</p>
      </CardContent>
    </Card>

    <Card className="border-dashed hover:border-solid transition-all cursor-pointer hover:shadow-md"
          onClick={() => onQuickAction?.('generate-report')}>
      <CardContent className="p-6 text-center">
        <Download className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
        <p className="font-medium">Generate Report</p>
        <p className="text-xs text-muted-foreground mt-1">Export budget data</p>
      </CardContent>
    </Card>

    <Card className="border-dashed hover:border-solid transition-all cursor-pointer hover:shadow-md"
          onClick={() => onQuickAction?.('set-goals')}>
      <CardContent className="p-6 text-center">
        <Target className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
        <p className="font-medium">Set Budget Goals</p>
        <p className="text-xs text-muted-foreground mt-1">Define spending limits</p>
      </CardContent>
    </Card>
  </div>
);

// Helper functions
function getHealthBadgeVariant(score: number): "default" | "secondary" | "destructive" {
  if (score >= 75) return "default";
  if (score >= 50) return "secondary";
  return "destructive";
}

function getHealthStatus(score: number): string {
  if (score >= 75) return "Excellent";
  if (score >= 50) return "Good";
  return "Needs Attention";
}

export default BudgetOverview;