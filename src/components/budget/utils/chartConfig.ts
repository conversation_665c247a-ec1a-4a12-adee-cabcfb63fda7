/**
 * Unified Chart Configuration
 * Implements 2025 finance dashboard visualization standards
 */

// Chart color palette following semantic meaning
export const CHART_COLORS = {
  // Primary palette
  primary: ['#007AFF', '#0056CC', '#003D9A', '#002766', '#001433'],
  
  // Success palette (gains/positive)
  success: ['#34C759', '#16A34A', '#15803D', '#14532D', '#0F3A1F'],
  
  // Warning palette (neutral/caution)
  warning: ['#FF9500', '#EA580C', '#C2410C', '#9A3412', '#7C2D12'],
  
  // Danger palette (losses/negative)
  danger: ['#FF3B30', '#DC2626', '#B91C1C', '#991B1B', '#7F1D1D'],
  
  // Neutral palette (secondary data)
  neutral: ['#6B7280', '#9CA3AF', '#D1D5DB', '#E5E7EB', '#F3F4F6'],
  
  // Extended palette for multiple series
  extended: [
    '#007AFF', // Primary blue
    '#34C759', // Success green
    '#FF9500', // Warning orange
    '#AF52DE', // Purple
    '#5AC8FA', // Cyan
    '#FFD60A', // Yellow
    '#FF3B30', // Danger red
    '#8E8E93', // Gray
  ]
};

// Semantic color mapping for financial data
export const SEMANTIC_COLORS = {
  revenue: CHART_COLORS.success[0],
  expenses: CHART_COLORS.danger[0],
  profit: CHART_COLORS.primary[0],
  budget: CHART_COLORS.neutral[0],
  forecast: CHART_COLORS.warning[0],
  actual: CHART_COLORS.primary[1],
  target: CHART_COLORS.success[1],
  variance: CHART_COLORS.warning[1],
};

// Chart configuration defaults
export const CHART_CONFIG = {
  // Animation settings
  animation: {
    duration: 350,
    easing: 'easeInOut',
  },
  
  // Responsive settings
  responsive: true,
  maintainAspectRatio: false,
  
  // Interaction settings
  interaction: {
    mode: 'index' as const,
    intersect: false,
  },
  
  // Plugin defaults
  plugins: {
    legend: {
      display: true,
      position: 'bottom' as const,
      align: 'center' as const,
      labels: {
        usePointStyle: true,
        padding: 12,
        font: {
          size: 12,
          family: '-apple-system, BlinkMacSystemFont, "SF Pro Display", sans-serif',
        },
      },
    },
    tooltip: {
      enabled: true,
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      titleColor: '#fff',
      bodyColor: '#fff',
      padding: 12,
      cornerRadius: 8,
      displayColors: true,
      borderColor: 'rgba(255, 255, 255, 0.1)',
      borderWidth: 1,
      titleFont: {
        size: 14,
        weight: '600',
      },
      bodyFont: {
        size: 13,
      },
      callbacks: {
        label: function(context: any) {
          let label = context.dataset.label || '';
          if (label) {
            label += ': ';
          }
          if (context.parsed.y !== null) {
            label += new Intl.NumberFormat('en-US', {
              style: 'currency',
              currency: 'USD',
              minimumFractionDigits: 0,
              maximumFractionDigits: 0,
            }).format(context.parsed.y);
          }
          return label;
        },
      },
    },
  },
  
  // Scale defaults
  scales: {
    x: {
      grid: {
        display: false,
      },
      ticks: {
        font: {
          size: 11,
        },
      },
    },
    y: {
      grid: {
        color: 'rgba(0, 0, 0, 0.05)',
        borderDash: [3, 3],
      },
      ticks: {
        font: {
          size: 11,
        },
        callback: function(value: any) {
          if (value >= 1000) {
            return '$' + (value / 1000).toFixed(0) + 'k';
          }
          return '$' + value;
        },
      },
    },
  },
};

// Chart type specific configurations
export const CHART_TYPE_CONFIG = {
  line: {
    tension: 0.4,
    borderWidth: 2,
    pointRadius: 0,
    pointHoverRadius: 4,
    pointBackgroundColor: '#fff',
    pointBorderWidth: 2,
  },
  bar: {
    borderRadius: 4,
    borderSkipped: false,
    barThickness: undefined,
    maxBarThickness: 40,
  },
  doughnut: {
    cutout: '65%',
    borderWidth: 0,
    spacing: 2,
  },
  area: {
    fill: true,
    tension: 0.4,
    borderWidth: 2,
    pointRadius: 0,
    backgroundColor: (context: any) => {
      const chart = context.chart;
      const {ctx, chartArea} = chart;
      if (!chartArea) {
        return null;
      }
      const gradient = ctx.createLinearGradient(0, chartArea.bottom, 0, chartArea.top);
      gradient.addColorStop(0, 'rgba(0, 122, 255, 0)');
      gradient.addColorStop(1, 'rgba(0, 122, 255, 0.1)');
      return gradient;
    },
  },
};

// Responsive breakpoints for charts
export const CHART_BREAKPOINTS = {
  mobile: {
    maxWidth: 640,
    config: {
      plugins: {
        legend: {
          position: 'bottom' as const,
        },
      },
      scales: {
        x: {
          ticks: {
            maxRotation: 45,
            minRotation: 45,
          },
        },
      },
    },
  },
  tablet: {
    maxWidth: 1024,
    config: {
      plugins: {
        legend: {
          position: 'bottom' as const,
        },
      },
    },
  },
  desktop: {
    maxWidth: Infinity,
    config: {
      plugins: {
        legend: {
          position: 'right' as const,
        },
      },
    },
  },
};

// Utility function to get color by index
export const getChartColor = (index: number, palette: keyof typeof CHART_COLORS = 'extended'): string => {
  const colors = CHART_COLORS[palette];
  return colors[index % colors.length];
};

// Utility function to format chart data values
export const formatChartValue = (value: number, type: 'currency' | 'percentage' | 'number' = 'currency'): string => {
  switch (type) {
    case 'currency':
      if (value >= 1000000) {
        return `$${(value / 1000000).toFixed(1)}M`;
      } else if (value >= 10000) {
        return `$${(value / 1000).toFixed(0)}k`;
      }
      return `$${value.toFixed(0)}`;
    case 'percentage':
      return `${value.toFixed(1)}%`;
    case 'number':
      return value.toLocaleString();
    default:
      return value.toString();
  }
};

// Generate consistent dataset configuration
export const generateDatasetConfig = (
  label: string,
  data: number[],
  type: 'line' | 'bar' | 'area' = 'line',
  colorIndex: number = 0
) => {
  const color = getChartColor(colorIndex);
  const baseConfig = {
    label,
    data,
    borderColor: color,
    backgroundColor: type === 'bar' ? color : `${color}20`,
  };
  
  return {
    ...baseConfig,
    ...CHART_TYPE_CONFIG[type === 'area' ? 'area' : type],
  };
};