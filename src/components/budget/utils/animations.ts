/**
 * Purposeful Animation System
 * Following Apple Human Interface Guidelines for meaningful motion
 */

import { Variants, Transition, MotionValue } from 'framer-motion';

// Apple-style easing curves
export const easings = {
  // Primary easing - most common, feels natural and responsive
  easeOut: [0.0, 0.0, 0.2, 1] as [number, number, number, number],
  
  // Emphasized easing - for important state changes
  emphasized: [0.4, 0.0, 0.2, 1] as [number, number, number, number],
  
  // Decelerated easing - for exits and dismissals
  decelerated: [0.0, 0.0, 0.2, 1] as [number, number, number, number],
  
  // Accelerated easing - for entrances
  accelerated: [0.4, 0.0, 1, 1] as [number, number, number, number],
  
  // Spring easing - for playful interactions
  spring: { type: "spring", stiffness: 300, damping: 30 } as const,
  
  // Gentle spring - for subtle feedback
  gentleSpring: { type: "spring", stiffness: 200, damping: 25 } as const,
} as const;

// Duration tokens following Apple's timing
export const durations = {
  // Fast - for micro-interactions and feedback
  fast: 0.15,
  
  // Default - for most UI transitions
  default: 0.25,
  
  // Slow - for complex state changes
  slow: 0.35,
  
  // Slower - for page transitions and major layout changes
  slower: 0.5,
  
  // Loading - for loading states and progress
  loading: 1.0,
} as const;

// Common animation variants following Apple design patterns
export const fadeVariants: Variants = {
  hidden: {
    opacity: 0,
  },
  visible: {
    opacity: 1,
    transition: {
      duration: durations.default,
      ease: easings.easeOut,
    },
  },
  exit: {
    opacity: 0,
    transition: {
      duration: durations.fast,
      ease: easings.accelerated,
    },
  },
};

export const slideUpVariants: Variants = {
  hidden: {
    opacity: 0,
    y: 20,
    scale: 0.95,
  },
  visible: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      duration: durations.default,
      ease: easings.easeOut,
    },
  },
  exit: {
    opacity: 0,
    y: -10,
    scale: 0.95,
    transition: {
      duration: durations.fast,
      ease: easings.accelerated,
    },
  },
};

export const slideDownVariants: Variants = {
  hidden: {
    opacity: 0,
    y: -20,
    scale: 0.95,
  },
  visible: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      duration: durations.default,
      ease: easings.easeOut,
    },
  },
  exit: {
    opacity: 0,
    y: 10,
    scale: 0.95,
    transition: {
      duration: durations.fast,
      ease: easings.accelerated,
    },
  },
};

export const scaleVariants: Variants = {
  hidden: {
    opacity: 0,
    scale: 0.8,
  },
  visible: {
    opacity: 1,
    scale: 1,
    transition: {
      duration: durations.default,
      ease: easings.easeOut,
    },
  },
  exit: {
    opacity: 0,
    scale: 0.9,
    transition: {
      duration: durations.fast,
      ease: easings.accelerated,
    },
  },
};

// Staggered children animations
export const staggerContainer: Variants = {
  hidden: {},
  visible: {
    transition: {
      staggerChildren: 0.05,
      delayChildren: 0.1,
    },
  },
  exit: {
    transition: {
      staggerChildren: 0.02,
      staggerDirection: -1,
    },
  },
};

export const staggerItem: Variants = {
  hidden: {
    opacity: 0,
    y: 10,
    scale: 0.95,
  },
  visible: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      duration: durations.default,
      ease: easings.easeOut,
    },
  },
  exit: {
    opacity: 0,
    y: -5,
    scale: 0.95,
    transition: {
      duration: durations.fast,
      ease: easings.accelerated,
    },
  },
};

// Loading and progress animations
export const pulseVariants: Variants = {
  visible: {
    scale: [1, 1.05, 1],
    opacity: [1, 0.8, 1],
    transition: {
      duration: durations.loading,
      repeat: Infinity,
      ease: easings.easeOut,
    },
  },
};

export const shimmerVariants: Variants = {
  visible: {
    x: ['-100%', '100%'],
    transition: {
      duration: durations.slower,
      repeat: Infinity,
      ease: 'linear',
    },
  },
};

// Hover and interaction animations
export const hoverVariants: Variants = {
  rest: {},
  hover: {
    scale: 1.02,
    y: -2,
    transition: {
      duration: durations.fast,
      ease: easings.easeOut,
    },
  },
  tap: {
    scale: 0.98,
    transition: {
      duration: durations.fast * 0.5,
      ease: easings.easeOut,
    },
  },
};

export const cardHoverVariants: Variants = {
  rest: {
    scale: 1,
    y: 0,
    boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
  },
  hover: {
    scale: 1.01,
    y: -4,
    boxShadow: '0 8px 25px rgba(0,0,0,0.15)',
    transition: {
      duration: durations.fast,
      ease: easings.easeOut,
    },
  },
  tap: {
    scale: 0.99,
    y: -2,
    transition: {
      duration: durations.fast * 0.5,
      ease: easings.easeOut,
    },
  },
};

// Modal and overlay animations
export const modalVariants: Variants = {
  hidden: {
    opacity: 0,
    scale: 0.95,
    y: 20,
  },
  visible: {
    opacity: 1,
    scale: 1,
    y: 0,
    transition: {
      duration: durations.default,
      ease: easings.emphasized,
    },
  },
  exit: {
    opacity: 0,
    scale: 0.95,
    y: 10,
    transition: {
      duration: durations.fast,
      ease: easings.accelerated,
    },
  },
};

export const backdropVariants: Variants = {
  hidden: {
    opacity: 0,
  },
  visible: {
    opacity: 1,
    transition: {
      duration: durations.default,
      ease: easings.easeOut,
    },
  },
  exit: {
    opacity: 0,
    transition: {
      duration: durations.fast,
      ease: easings.accelerated,
    },
  },
};

// Page transition animations
export const pageTransitionVariants: Variants = {
  initial: {
    opacity: 0,
    x: 20,
  },
  in: {
    opacity: 1,
    x: 0,
    transition: {
      duration: durations.slow,
      ease: easings.easeOut,
    },
  },
  out: {
    opacity: 0,
    x: -20,
    transition: {
      duration: durations.default,
      ease: easings.accelerated,
    },
  },
};

// Status change animations
export const statusChangeVariants: Variants = {
  success: {
    scale: [1, 1.1, 1],
    transition: {
      duration: durations.default,
      ease: easings.spring,
    },
  },
  error: {
    x: [0, -5, 5, -5, 5, 0],
    transition: {
      duration: durations.default,
      ease: easings.easeOut,
    },
  },
  warning: {
    y: [0, -3, 0],
    transition: {
      duration: durations.default,
      ease: easings.easeOut,
    },
  },
};

// Form field animations
export const fieldVariants: Variants = {
  focused: {
    scale: 1.01,
    transition: {
      duration: durations.fast,
      ease: easings.easeOut,
    },
  },
  error: {
    x: [0, -3, 3, -3, 3, 0],
    borderColor: '#ef4444',
    transition: {
      duration: durations.default,
      ease: easings.easeOut,
    },
  },
  success: {
    borderColor: '#10b981',
    transition: {
      duration: durations.default,
      ease: easings.easeOut,
    },
  },
};

// Data visualization animations
export const chartVariants: Variants = {
  hidden: {
    pathLength: 0,
    opacity: 0,
  },
  visible: {
    pathLength: 1,
    opacity: 1,
    transition: {
      pathLength: {
        duration: durations.slower,
        ease: easings.easeOut,
      },
      opacity: {
        duration: durations.default,
        ease: easings.easeOut,
      },
    },
  },
};

export const numberCountVariants = (from: number, to: number): Variants => ({
  hidden: { value: from },
  visible: {
    value: to,
    transition: {
      duration: durations.slow,
      ease: easings.easeOut,
    },
  },
});

// Utility functions
export const createStaggerDelay = (index: number, baseDelay: number = 0.05): number => {
  return baseDelay * index;
};

export const createSpringTransition = (
  stiffness: number = 300,
  damping: number = 30
): Transition => ({
  type: 'spring',
  stiffness,
  damping,
});

export const createEaseTransition = (
  duration: number = durations.default,
  ease: typeof easings.easeOut = easings.easeOut
): Transition => ({
  duration,
  ease,
});

// Animation orchestration helpers
export const sequenceAnimations = (animations: Variants[]): Variants => {
  return animations.reduce((acc, curr, index) => {
    return {
      ...acc,
      [index]: curr,
    };
  }, {});
};

// Reduced motion preferences
export const getReducedMotionVariant = (originalVariant: Variants): Variants => {
  return Object.fromEntries(
    Object.entries(originalVariant).map(([key, value]) => [
      key,
      {
        ...value,
        transition: {
          ...value.transition,
          duration: 0.01, // Near-instant for reduced motion
        },
      },
    ])
  );
};

// Animation presets for common components
export const animationPresets = {
  // Cards and panels
  card: cardHoverVariants,
  fadeIn: fadeVariants,
  slideUp: slideUpVariants,
  
  // Lists and collections
  list: staggerContainer,
  listItem: staggerItem,
  
  // Modals and overlays
  modal: modalVariants,
  backdrop: backdropVariants,
  
  // Form interactions
  field: fieldVariants,
  button: hoverVariants,
  
  // Status feedback
  success: statusChangeVariants.success,
  error: statusChangeVariants.error,
  warning: statusChangeVariants.warning,
  
  // Loading states
  pulse: pulseVariants,
  shimmer: shimmerVariants,
} as const;