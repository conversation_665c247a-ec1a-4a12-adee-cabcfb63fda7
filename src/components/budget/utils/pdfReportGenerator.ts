import jsPDF from 'jspdf';
import { format } from 'date-fns';
import { Budget, Stats, Expense, CategoryTotal, DepartmentAllocation } from '@/stores/budgetStore';
import { formatCurrency } from './formatters';
import { 
  generateExecutiveSummaryData, 
  generateDepartmentAnalysisData, 
  generateCategoryAnalysisData,
  ReportData 
} from './exportHelpers';

interface PDFReportOptions {
  title: string;
  subtitle?: string;
  author?: string;
  includeSummary?: boolean;
  includeCharts?: boolean;
  includeRecommendations?: boolean;
  includeForecast?: boolean;
}

export class PDFReportGenerator {
  private doc: jsPDF;
  private pageHeight: number;
  private pageWidth: number;
  private margin: number = 20;
  private currentY: number = 20;
  private lineHeight: number = 7;
  private primaryColor: [number, number, number] = [59, 130, 246]; // Blue
  private secondaryColor: [number, number, number] = [16, 185, 129]; // Green
  private dangerColor: [number, number, number] = [239, 68, 68]; // Red

  constructor() {
    this.doc = new jsPDF();
    this.pageHeight = this.doc.internal.pageSize.height;
    this.pageWidth = this.doc.internal.pageSize.width;
  }

  generateReport(data: ReportData, options: PDFReportOptions): jsPDF {
    this.addHeader(options.title, options.subtitle);
    
    if (options.includeSummary) {
      this.addExecutiveSummary(data);
    }

    this.addBudgetOverview(data);
    this.addCategoryAnalysis(data);
    this.addDepartmentBreakdown(data);
    
    if (options.includeRecommendations) {
      this.addRecommendations(data);
    }

    if (options.includeForecast) {
      this.addForecastSection(data);
    }

    this.addFooter();
    
    return this.doc;
  }

  private addHeader(title: string, subtitle?: string) {
    // Logo placeholder
    this.doc.setFillColor(...this.primaryColor);
    this.doc.rect(this.margin, this.margin, 40, 15, 'F');
    
    // Title
    this.doc.setFontSize(24);
    this.doc.setTextColor(0, 0, 0);
    this.doc.text(title, this.pageWidth / 2, this.margin + 10, { align: 'center' });
    
    if (subtitle) {
      this.doc.setFontSize(14);
      this.doc.setTextColor(100, 100, 100);
      this.doc.text(subtitle, this.pageWidth / 2, this.margin + 20, { align: 'center' });
    }
    
    this.currentY = this.margin + 35;
    
    // Date
    this.doc.setFontSize(10);
    this.doc.setTextColor(100, 100, 100);
    this.doc.text(`Generated: ${format(new Date(), 'MMMM dd, yyyy')}`, this.pageWidth - this.margin, this.currentY, { align: 'right' });
    
    this.currentY += 10;
    this.addSeparator();
  }

  private addExecutiveSummary(data: ReportData) {
    this.addSection('Executive Summary');
    
    const summary = generateExecutiveSummaryData(data);
    if (!summary) return;
    
    // Key Metrics Box
    this.doc.setFillColor(245, 245, 245);
    this.doc.rect(this.margin, this.currentY, this.pageWidth - 2 * this.margin, 40, 'F');
    
    const boxY = this.currentY + 5;
    const colWidth = (this.pageWidth - 2 * this.margin) / 4;
    
    // Metric 1: Total Budget
    this.addMetricBox('Total Budget', formatCurrency(summary.overview.totalBudget), this.margin + 5, boxY, colWidth - 10);
    
    // Metric 2: Spent
    this.addMetricBox('Total Spent', formatCurrency(summary.overview.totalSpent), this.margin + colWidth + 5, boxY, colWidth - 10);
    
    // Metric 3: Remaining
    this.addMetricBox('Remaining', formatCurrency(summary.overview.remaining), this.margin + 2 * colWidth + 5, boxY, colWidth - 10);
    
    // Metric 4: Utilization
    this.addMetricBox('Utilization', `${summary.overview.utilizationRate.toFixed(1)}%`, this.margin + 3 * colWidth + 5, boxY, colWidth - 10);
    
    this.currentY += 50;
    
    // Summary text
    this.doc.setFontSize(10);
    this.doc.setTextColor(60, 60, 60);
    
    const summaryText = [
      `During the reporting period, ${formatCurrency(summary.overview.periodSpent)} was spent across ${summary.keyMetrics.periodExpenses} expenses.`,
      `The average monthly spending rate is ${formatCurrency(summary.overview.avgMonthlySpend)}, which puts the budget ${summary.trends.budgetPace === 'ahead' ? 'ahead of schedule' : 'on track'}.`,
      `The top spending category is "${summary.keyMetrics.topCategory?.category}" with ${formatCurrency(summary.keyMetrics.topCategory?.total || 0)}.`
    ];
    
    summaryText.forEach(text => {
      const lines = this.doc.splitTextToSize(text, this.pageWidth - 2 * this.margin);
      this.doc.text(lines, this.margin, this.currentY);
      this.currentY += lines.length * this.lineHeight;
    });
    
    this.currentY += 10;
  }

  private addBudgetOverview(data: ReportData) {
    this.checkPageBreak();
    this.addSection('Budget Overview');
    
    const { budget, stats } = data;
    if (!budget || !stats) return;
    
    // Budget details table
    const tableData = [
      ['Fiscal Year', budget.year.toString()],
      ['Total Budget', formatCurrency(budget.total_amount)],
      ['Total Spent', formatCurrency(stats.spent)],
      ['Remaining Budget', formatCurrency(stats.remaining)],
      ['Budget Utilization', `${((stats.spent / budget.total_amount) * 100).toFixed(1)}%`],
      ['Total Expenses', stats.count_approved.toString()],
      ['Pending Approvals', stats.count_pending.toString()]
    ];
    
    this.addTable(['Metric', 'Value'], tableData);
  }

  private addCategoryAnalysis(data: ReportData) {
    this.checkPageBreak();
    this.addSection('Spending by Category');
    
    const categoryData = generateCategoryAnalysisData(data);
    if (!categoryData || categoryData.length === 0) return;
    
    const tableData = categoryData.slice(0, 10).map(cat => [
      cat.category,
      formatCurrency(cat.total),
      `${cat.percentage.toFixed(1)}%`,
      cat.expenseCount.toString(),
      formatCurrency(cat.avgExpenseAmount)
    ]);
    
    this.addTable(
      ['Category', 'Total Spent', '% of Budget', 'Expenses', 'Avg Amount'],
      tableData
    );
  }

  private addDepartmentBreakdown(data: ReportData) {
    this.checkPageBreak();
    this.addSection('Department Analysis');
    
    const deptData = generateDepartmentAnalysisData(data);
    if (!deptData || deptData.length === 0) return;
    
    const tableData = deptData.map(dept => [
      dept.name,
      formatCurrency(dept.allocated_amount),
      formatCurrency(dept.spent_amount),
      formatCurrency(dept.allocated_amount - dept.spent_amount),
      `${dept.efficiency.toFixed(1)}%`
    ]);
    
    this.addTable(
      ['Department', 'Allocated', 'Spent', 'Remaining', 'Efficiency'],
      tableData
    );
  }

  private addRecommendations(data: ReportData) {
    this.checkPageBreak();
    this.addSection('Recommendations');
    
    const recommendations = this.generateRecommendations(data);
    
    this.doc.setFontSize(10);
    recommendations.forEach((rec, index) => {
      // Recommendation number
      this.doc.setFillColor(...this.primaryColor);
      this.doc.circle(this.margin + 5, this.currentY + 3, 3, 'F');
      this.doc.setTextColor(255, 255, 255);
      this.doc.setFontSize(8);
      this.doc.text((index + 1).toString(), this.margin + 5, this.currentY + 5, { align: 'center' });
      
      // Recommendation text
      this.doc.setTextColor(60, 60, 60);
      this.doc.setFontSize(10);
      const lines = this.doc.splitTextToSize(rec, this.pageWidth - 2 * this.margin - 20);
      this.doc.text(lines, this.margin + 15, this.currentY);
      this.currentY += lines.length * this.lineHeight + 5;
    });
  }

  private addForecastSection(data: ReportData) {
    this.checkPageBreak();
    this.addSection('Budget Forecast');
    
    const { stats } = data;
    if (!stats) return;
    
    const currentMonth = new Date().getMonth() + 1;
    const monthlyRate = stats.spent / currentMonth;
    const projectedYearEnd = monthlyRate * 12;
    const projectedSurplus = data.budget!.total_amount - projectedYearEnd;
    
    this.doc.setFontSize(10);
    this.doc.setTextColor(60, 60, 60);
    
    const forecastText = [
      `Based on current spending patterns, the projected year-end expenditure is ${formatCurrency(projectedYearEnd)}.`,
      `This would result in a ${projectedSurplus > 0 ? 'surplus' : 'deficit'} of ${formatCurrency(Math.abs(projectedSurplus))}.`,
      `Current monthly spending rate: ${formatCurrency(monthlyRate)}`
    ];
    
    forecastText.forEach(text => {
      this.doc.text(text, this.margin, this.currentY);
      this.currentY += this.lineHeight;
    });
  }

  private addMetricBox(label: string, value: string, x: number, y: number, width: number) {
    this.doc.setFontSize(8);
    this.doc.setTextColor(100, 100, 100);
    this.doc.text(label, x, y + 5);
    
    this.doc.setFontSize(14);
    this.doc.setTextColor(0, 0, 0);
    this.doc.setFont(undefined, 'bold');
    this.doc.text(value, x, y + 15);
    this.doc.setFont(undefined, 'normal');
  }

  private addSection(title: string) {
    this.doc.setFontSize(16);
    this.doc.setTextColor(...this.primaryColor);
    this.doc.text(title, this.margin, this.currentY);
    this.currentY += 10;
    
    // Section underline
    this.doc.setDrawColor(...this.primaryColor);
    this.doc.setLineWidth(0.5);
    this.doc.line(this.margin, this.currentY - 5, this.margin + 50, this.currentY - 5);
    
    this.currentY += 5;
  }

  private addTable(headers: string[], data: string[][]) {
    const colWidth = (this.pageWidth - 2 * this.margin) / headers.length;
    const rowHeight = 8;
    
    // Headers
    this.doc.setFillColor(240, 240, 240);
    this.doc.rect(this.margin, this.currentY, this.pageWidth - 2 * this.margin, rowHeight, 'F');
    
    this.doc.setFontSize(9);
    this.doc.setTextColor(0, 0, 0);
    this.doc.setFont(undefined, 'bold');
    
    headers.forEach((header, index) => {
      this.doc.text(header, this.margin + index * colWidth + 3, this.currentY + 5);
    });
    
    this.currentY += rowHeight;
    this.doc.setFont(undefined, 'normal');
    
    // Data rows
    data.forEach((row, rowIndex) => {
      if (rowIndex % 2 === 0) {
        this.doc.setFillColor(250, 250, 250);
        this.doc.rect(this.margin, this.currentY, this.pageWidth - 2 * this.margin, rowHeight, 'F');
      }
      
      this.doc.setFontSize(8);
      this.doc.setTextColor(60, 60, 60);
      
      row.forEach((cell, cellIndex) => {
        const text = this.doc.splitTextToSize(cell, colWidth - 6);
        this.doc.text(text[0], this.margin + cellIndex * colWidth + 3, this.currentY + 5);
      });
      
      this.currentY += rowHeight;
      
      // Check for page break
      if (this.currentY > this.pageHeight - 40) {
        this.addNewPage();
      }
    });
    
    this.currentY += 10;
  }

  private addSeparator() {
    this.doc.setDrawColor(200, 200, 200);
    this.doc.setLineWidth(0.2);
    this.doc.line(this.margin, this.currentY, this.pageWidth - this.margin, this.currentY);
    this.currentY += 10;
  }

  private addFooter() {
    const footerY = this.pageHeight - 15;
    
    this.doc.setFontSize(8);
    this.doc.setTextColor(150, 150, 150);
    this.doc.text(
      'Generated by Training Budget Management System',
      this.pageWidth / 2,
      footerY,
      { align: 'center' }
    );
    
    // Page numbers
    const pageCount = this.doc.getNumberOfPages();
    for (let i = 1; i <= pageCount; i++) {
      this.doc.setPage(i);
      this.doc.text(
        `Page ${i} of ${pageCount}`,
        this.pageWidth - this.margin,
        footerY,
        { align: 'right' }
      );
    }
  }

  private checkPageBreak(requiredSpace: number = 50) {
    if (this.currentY + requiredSpace > this.pageHeight - 30) {
      this.addNewPage();
    }
  }

  private addNewPage() {
    this.doc.addPage();
    this.currentY = this.margin;
  }

  private generateRecommendations(data: ReportData): string[] {
    const recommendations: string[] = [];
    const { budget, stats } = data;
    
    if (!budget || !stats) return recommendations;
    
    const utilizationRate = (stats.spent / budget.total_amount) * 100;
    const currentMonth = new Date().getMonth() + 1;
    const expectedUtilization = (currentMonth / 12) * 100;
    
    // Budget utilization recommendations
    if (utilizationRate > expectedUtilization + 10) {
      recommendations.push(
        `Budget utilization (${utilizationRate.toFixed(1)}%) is running ahead of schedule. Consider reviewing upcoming expenses and potentially reallocating funds to high-priority areas.`
      );
    } else if (utilizationRate < expectedUtilization - 20) {
      recommendations.push(
        `Budget utilization (${utilizationRate.toFixed(1)}%) is below expected levels. Consider identifying training opportunities or strategic investments to maximize budget value.`
      );
    }
    
    // Category concentration
    const topCategory = stats.by_category[0];
    if (topCategory && (topCategory.total / stats.spent) > 0.4) {
      recommendations.push(
        `${topCategory.category} represents ${((topCategory.total / stats.spent) * 100).toFixed(1)}% of total spending. Consider diversifying training investments across other categories.`
      );
    }
    
    // Approval rate
    const totalRequests = stats.count_approved + stats.count_rejected;
    if (totalRequests > 0) {
      const approvalRate = (stats.count_approved / totalRequests) * 100;
      if (approvalRate < 70) {
        recommendations.push(
          `The approval rate (${approvalRate.toFixed(1)}%) is below optimal levels. Review rejection reasons to improve future request quality.`
        );
      }
    }
    
    // Pending approvals
    if (stats.count_pending > 5) {
      recommendations.push(
        `There are ${stats.count_pending} pending expense approvals. Process these promptly to maintain accurate budget tracking.`
      );
    }
    
    return recommendations.length > 0 ? recommendations : [
      'Budget performance is on track. Continue monitoring spending patterns and adjusting allocations as needed.'
    ];
  }

  saveReport(filename: string) {
    this.doc.save(`${filename}.pdf`);
  }

  getBlob(): Blob {
    return this.doc.output('blob');
  }
}