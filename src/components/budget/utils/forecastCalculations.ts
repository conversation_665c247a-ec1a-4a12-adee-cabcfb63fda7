/**
 * Forecast calculation utilities for budget predictions
 */

export interface HistoricalDataPoint {
  date: Date;
  value: number;
}

export interface SeasonalFactors {
  january: number;
  february: number;
  march: number;
  april: number;
  may: number;
  june: number;
  july: number;
  august: number;
  september: number;
  october: number;
  november: number;
  december: number;
}

export interface ForecastResult {
  value: number;
  confidence: number;
  upperBound: number;
  lowerBound: number;
  method: 'linear' | 'seasonal' | 'weighted';
}

/**
 * Calculate simple linear regression
 */
export function linearRegression(data: HistoricalDataPoint[]): {
  slope: number;
  intercept: number;
  r2: number;
} {
  const n = data.length;
  if (n < 2) {
    throw new Error('Need at least 2 data points for regression');
  }

  // Convert dates to numeric values (days since first date)
  const firstDate = data[0].date.getTime();
  const points = data.map(d => ({
    x: (d.date.getTime() - firstDate) / (1000 * 60 * 60 * 24), // days
    y: d.value
  }));

  // Calculate sums
  const sumX = points.reduce((sum, p) => sum + p.x, 0);
  const sumY = points.reduce((sum, p) => sum + p.y, 0);
  const sumXY = points.reduce((sum, p) => sum + p.x * p.y, 0);
  const sumX2 = points.reduce((sum, p) => sum + p.x * p.x, 0);
  const sumY2 = points.reduce((sum, p) => sum + p.y * p.y, 0);

  // Calculate regression coefficients
  const slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
  const intercept = (sumY - slope * sumX) / n;

  // Calculate R-squared
  const yMean = sumY / n;
  const ssTotal = points.reduce((sum, p) => sum + Math.pow(p.y - yMean, 2), 0);
  const ssResidual = points.reduce((sum, p) => {
    const predicted = slope * p.x + intercept;
    return sum + Math.pow(p.y - predicted, 2);
  }, 0);
  const r2 = 1 - (ssResidual / ssTotal);

  return { slope, intercept, r2 };
}

/**
 * Calculate seasonal factors from historical data
 */
export function calculateSeasonalFactors(
  data: HistoricalDataPoint[]
): SeasonalFactors {
  const monthlyAverages: Record<number, number[]> = {};

  // Group data by month
  data.forEach(point => {
    const month = point.date.getMonth();
    if (!monthlyAverages[month]) {
      monthlyAverages[month] = [];
    }
    monthlyAverages[month].push(point.value);
  });

  // Calculate average for each month
  const monthNames = [
    'january', 'february', 'march', 'april', 'may', 'june',
    'july', 'august', 'september', 'october', 'november', 'december'
  ];

  const factors: any = {};
  const overallAverage = data.reduce((sum, d) => sum + d.value, 0) / data.length;

  monthNames.forEach((name, index) => {
    const monthData = monthlyAverages[index] || [];
    const monthAverage = monthData.length > 0
      ? monthData.reduce((sum, v) => sum + v, 0) / monthData.length
      : overallAverage;
    
    factors[name] = monthAverage / overallAverage;
  });

  return factors as SeasonalFactors;
}

/**
 * Generate forecast using linear trend
 */
export function forecastLinear(
  historicalData: HistoricalDataPoint[],
  targetDate: Date
): ForecastResult {
  const { slope, intercept, r2 } = linearRegression(historicalData);
  
  // Calculate days from first data point
  const firstDate = historicalData[0].date.getTime();
  const daysFromStart = (targetDate.getTime() - firstDate) / (1000 * 60 * 60 * 24);
  
  // Calculate forecast
  const value = slope * daysFromStart + intercept;
  
  // Calculate confidence based on R-squared and data recency
  const confidence = Math.max(0.3, Math.min(0.95, r2 * 0.9));
  
  // Calculate confidence bounds (simplified)
  const stdError = Math.sqrt(
    historicalData.reduce((sum, d, i) => {
      const predicted = slope * i + intercept;
      return sum + Math.pow(d.value - predicted, 2);
    }, 0) / (historicalData.length - 2)
  );
  
  const margin = stdError * 1.96; // 95% confidence interval
  
  return {
    value: Math.max(0, value),
    confidence,
    upperBound: value + margin,
    lowerBound: Math.max(0, value - margin),
    method: 'linear'
  };
}

/**
 * Generate forecast using seasonal adjustment
 */
export function forecastSeasonal(
  historicalData: HistoricalDataPoint[],
  targetDate: Date,
  seasonalFactors: SeasonalFactors
): ForecastResult {
  // First get linear trend
  const linearForecast = forecastLinear(historicalData, targetDate);
  
  // Apply seasonal adjustment
  const monthName = Object.keys(seasonalFactors)[targetDate.getMonth()] as keyof SeasonalFactors;
  const seasonalFactor = seasonalFactors[monthName];
  
  const adjustedValue = linearForecast.value * seasonalFactor;
  
  return {
    value: Math.max(0, adjustedValue),
    confidence: linearForecast.confidence * 0.85, // Slightly lower confidence for seasonal
    upperBound: linearForecast.upperBound * seasonalFactor * 1.1,
    lowerBound: Math.max(0, linearForecast.lowerBound * seasonalFactor * 0.9),
    method: 'seasonal'
  };
}

/**
 * Generate forecast using weighted moving average
 */
export function forecastWeightedAverage(
  historicalData: HistoricalDataPoint[],
  weights: number[] = [0.5, 0.3, 0.2] // Recent to old
): ForecastResult {
  const recentData = historicalData.slice(-weights.length);
  
  if (recentData.length < weights.length) {
    // Not enough data, use simple average
    const avg = recentData.reduce((sum, d) => sum + d.value, 0) / recentData.length;
    return {
      value: avg,
      confidence: 0.5,
      upperBound: avg * 1.2,
      lowerBound: avg * 0.8,
      method: 'weighted'
    };
  }
  
  // Calculate weighted average
  const weightedSum = recentData.reduce((sum, d, i) => {
    return sum + d.value * weights[weights.length - 1 - i];
  }, 0);
  
  const value = weightedSum / weights.reduce((sum, w) => sum + w, 0);
  
  // Calculate variance for bounds
  const variance = recentData.reduce((sum, d) => {
    return sum + Math.pow(d.value - value, 2);
  }, 0) / recentData.length;
  
  const stdDev = Math.sqrt(variance);
  
  return {
    value: Math.max(0, value),
    confidence: 0.7,
    upperBound: value + stdDev * 1.5,
    lowerBound: Math.max(0, value - stdDev * 1.5),
    method: 'weighted'
  };
}

/**
 * Generate multiple scenario forecasts
 */
export function generateScenarios(
  historicalData: HistoricalDataPoint[],
  targetDate: Date,
  seasonalFactors?: SeasonalFactors
): {
  optimistic: ForecastResult;
  realistic: ForecastResult;
  pessimistic: ForecastResult;
} {
  const linearForecast = forecastLinear(historicalData, targetDate);
  const seasonalForecast = seasonalFactors
    ? forecastSeasonal(historicalData, targetDate, seasonalFactors)
    : linearForecast;
  const weightedForecast = forecastWeightedAverage(historicalData);
  
  // Realistic scenario: average of methods
  const realisticValue = (linearForecast.value + seasonalForecast.value + weightedForecast.value) / 3;
  
  return {
    optimistic: {
      value: Math.min(
        linearForecast.lowerBound,
        seasonalForecast.lowerBound,
        weightedForecast.lowerBound
      ),
      confidence: Math.max(linearForecast.confidence, seasonalForecast.confidence) * 0.8,
      upperBound: realisticValue * 0.9,
      lowerBound: realisticValue * 0.7,
      method: 'linear'
    },
    realistic: {
      value: realisticValue,
      confidence: (linearForecast.confidence + seasonalForecast.confidence + weightedForecast.confidence) / 3,
      upperBound: realisticValue * 1.1,
      lowerBound: realisticValue * 0.9,
      method: 'seasonal'
    },
    pessimistic: {
      value: Math.max(
        linearForecast.upperBound,
        seasonalForecast.upperBound,
        weightedForecast.upperBound
      ),
      confidence: Math.min(linearForecast.confidence, seasonalForecast.confidence) * 0.8,
      upperBound: realisticValue * 1.3,
      lowerBound: realisticValue * 1.1,
      method: 'weighted'
    }
  };
}

/**
 * Calculate budget alerts based on forecasts
 */
export function calculateAlerts(
  currentSpent: number,
  totalBudget: number,
  forecast: ForecastResult,
  daysRemaining: number
): {
  level: 'success' | 'warning' | 'danger';
  message: string;
  recommendations: string[];
} {
  const utilizationRate = (currentSpent / totalBudget) * 100;
  const forecastedUtilization = (forecast.value / totalBudget) * 100;
  const dailyBurnRate = currentSpent / (365 - daysRemaining);
  const recommendedDailyBurn = (totalBudget - currentSpent) / daysRemaining;
  
  let level: 'success' | 'warning' | 'danger' = 'success';
  let message = '';
  const recommendations: string[] = [];
  
  if (forecastedUtilization > 100) {
    level = 'danger';
    message = `Projected to exceed budget by ${((forecast.value - totalBudget) / totalBudget * 100).toFixed(1)}%`;
    recommendations.push('Immediate spending freeze recommended');
    recommendations.push('Review and cancel non-essential expenses');
    recommendations.push(`Reduce daily spending to ${recommendedDailyBurn.toFixed(0)} or less`);
  } else if (forecastedUtilization > 90) {
    level = 'warning';
    message = 'Budget utilization approaching limits';
    recommendations.push('Monitor spending closely');
    recommendations.push('Consider deferring optional expenses');
    recommendations.push(`Maintain daily spending below ${recommendedDailyBurn.toFixed(0)}`);
  } else {
    level = 'success';
    message = 'Budget on track';
    recommendations.push('Continue current spending patterns');
    if (forecastedUtilization < 70) {
      recommendations.push('Consider investing in additional training opportunities');
    }
  }
  
  return { level, message, recommendations };
}