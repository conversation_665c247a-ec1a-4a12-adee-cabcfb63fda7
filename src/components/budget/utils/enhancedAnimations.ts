import { Variants, AnimationControls } from 'framer-motion';

// Soft UI specific animations with GPU acceleration
export const softUIAnimations = {
  // Glass morphism fade in with GPU acceleration
  glassFadeIn: {
    initial: { 
      opacity: 0, 
      backdropFilter: 'blur(0px)',
      background: 'rgba(255, 255, 255, 0)',
      transform: 'translateZ(0)', // Force GPU acceleration
    },
    animate: { 
      opacity: 1, 
      backdropFilter: 'blur(20px)',
      background: 'rgba(255, 255, 255, 0.8)',
      transform: 'translateZ(0)',
      transition: {
        duration: 0.6,
        ease: [0.23, 1, 0.32, 1], // Smooth easing
      }
    },
  },

  // Floating animation for cards
  float: {
    animate: {
      y: [0, -10, 0],
      transition: {
        duration: 3,
        repeat: Infinity,
        ease: "easeInOut",
      }
    }
  },

  // Gradient shift animation
  gradientShift: {
    animate: {
      backgroundPosition: ['0% 50%', '100% 50%', '0% 50%'],
      transition: {
        duration: 5,
        repeat: Infinity,
        ease: "linear",
      }
    }
  },

  // Soft pulse for attention
  softPulse: {
    animate: {
      boxShadow: [
        '0 8px 32px 0 rgba(31,38,135,0.15)',
        '0 8px 40px 0 rgba(31,38,135,0.25)',
        '0 8px 32px 0 rgba(31,38,135,0.15)',
      ],
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: "easeInOut",
      }
    }
  },

  // Stagger children with soft entrance
  staggerSoft: {
    container: {
      hidden: { opacity: 0 },
      show: {
        opacity: 1,
        transition: {
          staggerChildren: 0.08,
          delayChildren: 0.2,
        }
      }
    },
    item: {
      hidden: { 
        opacity: 0, 
        y: 20,
        filter: 'blur(10px)',
        transform: 'translateZ(0) translateY(20px)', // GPU acceleration
      },
      show: { 
        opacity: 1, 
        y: 0,
        filter: 'blur(0px)',
        transform: 'translateZ(0) translateY(0px)',
        transition: {
          duration: 0.5,
          ease: [0.23, 1, 0.32, 1],
        }
      }
    }
  },

  // Page transition with blur
  pageTransition: {
    initial: { 
      opacity: 0, 
      scale: 0.98,
      filter: 'blur(20px)',
      transform: 'translateZ(0) scale(0.98)', // GPU acceleration
    },
    animate: { 
      opacity: 1, 
      scale: 1,
      filter: 'blur(0px)',
      transform: 'translateZ(0) scale(1)',
      transition: {
        duration: 0.4,
        ease: [0.23, 1, 0.32, 1],
      }
    },
    exit: { 
      opacity: 0, 
      scale: 1.02,
      filter: 'blur(20px)',
      transform: 'translateZ(0) scale(1.02)',
      transition: {
        duration: 0.3,
        ease: [0.23, 1, 0.32, 1],
      }
    }
  },

  // Card hover with 3D effect
  card3D: {
    initial: {
      transform: 'translateZ(0) rotateX(0deg) rotateY(0deg)',
      boxShadow: '0 8px 32px 0 rgba(31,38,135,0.15)',
    },
    hover: {
      transform: 'translateZ(50px) rotateX(5deg) rotateY(5deg)',
      boxShadow: '0 20px 60px 0 rgba(31,38,135,0.35)',
      transition: {
        duration: 0.3,
        ease: [0.23, 1, 0.32, 1],
      }
    }
  },

  // Smooth slide animations
  slideIn: {
    left: {
      initial: { x: -100, opacity: 0, transform: 'translateZ(0)' },
      animate: { 
        x: 0, 
        opacity: 1,
        transform: 'translateZ(0)',
        transition: { duration: 0.5, ease: [0.23, 1, 0.32, 1] }
      }
    },
    right: {
      initial: { x: 100, opacity: 0, transform: 'translateZ(0)' },
      animate: { 
        x: 0, 
        opacity: 1,
        transform: 'translateZ(0)',
        transition: { duration: 0.5, ease: [0.23, 1, 0.32, 1] }
      }
    },
    up: {
      initial: { y: 100, opacity: 0, transform: 'translateZ(0)' },
      animate: { 
        y: 0, 
        opacity: 1,
        transform: 'translateZ(0)',
        transition: { duration: 0.5, ease: [0.23, 1, 0.32, 1] }
      }
    },
    down: {
      initial: { y: -100, opacity: 0, transform: 'translateZ(0)' },
      animate: { 
        y: 0, 
        opacity: 1,
        transform: 'translateZ(0)',
        transition: { duration: 0.5, ease: [0.23, 1, 0.32, 1] }
      }
    }
  },

  // Scale animations
  scaleIn: {
    initial: { 
      scale: 0.8, 
      opacity: 0,
      transform: 'translateZ(0) scale(0.8)',
    },
    animate: { 
      scale: 1, 
      opacity: 1,
      transform: 'translateZ(0) scale(1)',
      transition: { 
        duration: 0.3, 
        ease: [0.23, 1, 0.32, 1] 
      }
    }
  },

  // Ripple effect
  ripple: {
    initial: { scale: 0, opacity: 1 },
    animate: { 
      scale: 4, 
      opacity: 0,
      transition: {
        duration: 1,
        ease: "easeOut",
      }
    }
  },

  // Morphing shapes
  morph: {
    initial: { borderRadius: '20px' },
    animate: {
      borderRadius: ['20px', '50%', '30px', '20px'],
      transition: {
        duration: 4,
        repeat: Infinity,
        ease: "easeInOut",
      }
    }
  },

  // Shimmer effect for loading
  shimmer: {
    animate: {
      backgroundPosition: ['200% 0', '-200% 0'],
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: "linear",
      }
    }
  },

  // Bounce attention
  bounce: {
    animate: {
      y: [0, -20, 0],
      transition: {
        duration: 0.6,
        repeat: Infinity,
        repeatDelay: 3,
        ease: [0.68, -0.55, 0.265, 1.55], // Elastic easing
      }
    }
  },

  // Glow effect
  glow: {
    initial: { boxShadow: '0 0 0 rgba(99, 102, 241, 0)' },
    animate: {
      boxShadow: [
        '0 0 0 rgba(99, 102, 241, 0)',
        '0 0 20px rgba(99, 102, 241, 0.5)',
        '0 0 40px rgba(99, 102, 241, 0.3)',
        '0 0 0 rgba(99, 102, 241, 0)',
      ],
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: "easeInOut",
      }
    }
  },

  // Tab switch animation
  tabSwitch: {
    initial: { opacity: 0, x: 20, transform: 'translateZ(0)' },
    animate: { 
      opacity: 1, 
      x: 0,
      transform: 'translateZ(0)',
      transition: { duration: 0.3, ease: [0.23, 1, 0.32, 1] }
    },
    exit: { 
      opacity: 0, 
      x: -20,
      transform: 'translateZ(0)',
      transition: { duration: 0.2, ease: [0.23, 1, 0.32, 1] }
    }
  },
};

// Animation orchestrator for complex sequences
export class AnimationOrchestrator {
  private animations: Map<string, AnimationControls> = new Map();

  async playSequence(sequence: Array<{
    controls: AnimationControls;
    animation: any;
    delay?: number;
  }>) {
    for (const { controls, animation, delay = 0 } of sequence) {
      await new Promise(resolve => setTimeout(resolve, delay * 1000));
      await controls.start(animation);
    }
  }

  async playParallel(animations: Array<{
    controls: AnimationControls;
    animation: any;
  }>) {
    await Promise.all(
      animations.map(({ controls, animation }) => 
        controls.start(animation)
      )
    );
  }

  // Stagger animation helper
  async staggerAnimate(
    elements: AnimationControls[],
    animation: any,
    staggerDelay: number = 0.1
  ) {
    for (let i = 0; i < elements.length; i++) {
      setTimeout(() => {
        elements[i].start(animation);
      }, i * staggerDelay * 1000);
    }
  }
}

// Performance-optimized spring configs
export const springConfigs = {
  wobbly: { stiffness: 180, damping: 12 },
  stiff: { stiffness: 300, damping: 20 },
  slow: { stiffness: 100, damping: 20 },
  molasses: { stiffness: 50, damping: 20 },
  quick: { stiffness: 400, damping: 25 },
};

// Common transition configs
export const transitions = {
  spring: {
    type: "spring",
    stiffness: 300,
    damping: 20,
  },
  smooth: {
    duration: 0.5,
    ease: [0.23, 1, 0.32, 1],
  },
  quick: {
    duration: 0.2,
    ease: "easeOut",
  },
  slow: {
    duration: 1,
    ease: "easeInOut",
  },
};

// Gesture animations
export const gestureAnimations = {
  tap: { scale: 0.95 },
  hover: { scale: 1.05 },
  drag: { scale: 1.1, rotate: 5 },
};

// Scroll-triggered animations
export const scrollAnimations = {
  fadeInUp: {
    initial: { opacity: 0, y: 50 },
    whileInView: { opacity: 1, y: 0 },
    viewport: { once: true, amount: 0.3 },
    transition: transitions.smooth,
  },
  scaleIn: {
    initial: { opacity: 0, scale: 0.8 },
    whileInView: { opacity: 1, scale: 1 },
    viewport: { once: true, amount: 0.3 },
    transition: transitions.smooth,
  },
  slideInLeft: {
    initial: { opacity: 0, x: -100 },
    whileInView: { opacity: 1, x: 0 },
    viewport: { once: true, amount: 0.3 },
    transition: transitions.smooth,
  },
  slideInRight: {
    initial: { opacity: 0, x: 100 },
    whileInView: { opacity: 1, x: 0 },
    viewport: { once: true, amount: 0.3 },
    transition: transitions.smooth,
  },
};