import { cn } from '@/lib/utils';

// Typography scale following Apple's Human Interface Guidelines
export const typography = {
  // Display styles - for hero sections and large headers
  display: {
    xl: 'text-5xl font-bold tracking-tight leading-none',
    lg: 'text-4xl font-bold tracking-tight leading-none',
    md: 'text-3xl font-semibold tracking-tight',
    sm: 'text-2xl font-semibold tracking-tight',
  },
  
  // Heading styles - for section headers
  heading: {
    h1: 'text-2xl font-semibold tracking-tight',
    h2: 'text-xl font-semibold tracking-tight',
    h3: 'text-lg font-semibold',
    h4: 'text-base font-semibold',
    h5: 'text-sm font-semibold',
    h6: 'text-xs font-semibold uppercase tracking-wider',
  },
  
  // Body text styles
  body: {
    lg: 'text-base leading-relaxed',
    base: 'text-sm leading-normal',
    sm: 'text-xs leading-normal',
  },
  
  // Label styles
  label: {
    lg: 'text-sm font-medium',
    base: 'text-xs font-medium',
    sm: 'text-[11px] font-medium',
  },
  
  // Caption styles
  caption: {
    base: 'text-xs text-muted-foreground',
    sm: 'text-[11px] text-muted-foreground',
  },
  
  // Specialized styles
  metric: {
    value: 'text-3xl font-bold tabular-nums',
    label: 'text-xs font-medium text-muted-foreground uppercase tracking-wider',
    change: 'text-sm font-medium tabular-nums',
  },
  
  code: {
    inline: 'font-mono text-sm bg-muted px-1 py-0.5 rounded',
    block: 'font-mono text-sm bg-muted p-3 rounded-lg',
  },
};

// Typography component props
interface TypographyProps {
  variant: keyof typeof typography;
  size?: string;
  className?: string;
  children: React.ReactNode;
  as?: React.ElementType;
}

// Typography component
export const Typography: React.FC<TypographyProps> = ({
  variant,
  size = 'base',
  className,
  children,
  as: Component = 'div',
}) => {
  const styles = typography[variant]?.[size] || typography.body.base;
  
  const DynamicComponent = Component as keyof JSX.IntrinsicElements;
  
  return React.createElement(DynamicComponent, {
    className: cn(styles, className),
  }, children);
};

// Semantic typography components
export const Display: React.FC<{
  size?: 'xl' | 'lg' | 'md' | 'sm';
  className?: string;
  children: React.ReactNode;
}> = ({ size = 'lg', className, children }) => {
  return React.createElement('h1', {
    className: cn(typography.display[size], className),
  }, children);
};

export const Heading: React.FC<{
  level?: 1 | 2 | 3 | 4 | 5 | 6;
  className?: string;
  children: React.ReactNode;
}> = ({ level = 2, className, children }) => {
  const Component = `h${level}` as keyof JSX.IntrinsicElements;
  const style = typography.heading[`h${level}` as keyof typeof typography.heading];
  
  return React.createElement(Component, {
    className: cn(style, className),
  }, children);
};

export const Text: React.FC<{
  size?: 'lg' | 'base' | 'sm';
  className?: string;
  children: React.ReactNode;
  muted?: boolean;
}> = ({ size = 'base', className, children, muted = false }) => {
  return React.createElement('p', {
    className: cn(
      typography.body[size],
      muted && 'text-muted-foreground',
      className
    ),
  }, children);
};

export const Label: React.FC<{
  size?: 'lg' | 'base' | 'sm';
  className?: string;
  children: React.ReactNode;
  htmlFor?: string;
}> = ({ size = 'base', className, children, htmlFor }) => {
  return React.createElement('label', {
    htmlFor: htmlFor,
    className: cn(typography.label[size], className),
  }, children);
};

export const Caption: React.FC<{
  size?: 'base' | 'sm';
  className?: string;
  children: React.ReactNode;
}> = ({ size = 'base', className, children }) => {
  return React.createElement('span', {
    className: cn(typography.caption[size], className),
  }, children);
};

export const Metric: React.FC<{
  value: string | number;
  label?: string;
  change?: string | number;
  changeType?: 'positive' | 'negative' | 'neutral';
  className?: string;
}> = ({ value, label, change, changeType = 'neutral', className }) => {
  const changeColors = {
    positive: 'text-green-600 dark:text-green-400',
    negative: 'text-red-600 dark:text-red-400',
    neutral: 'text-muted-foreground',
  };
  
  return React.createElement('div', {
    className: cn('space-y-1', className),
  }, [
    label && React.createElement('div', {
      className: typography.metric.label,
      key: 'label'
    }, label),
    React.createElement('div', {
      className: typography.metric.value,
      key: 'value'
    }, value),
    change && React.createElement('div', {
      className: cn(typography.metric.change, changeColors[changeType]),
      key: 'change'
    }, change)
  ].filter(Boolean));
};

// Visual hierarchy helper
export const createHierarchy = (level: number) => {
  const hierarchyMap = {
    1: typography.display.lg,
    2: typography.heading.h1,
    3: typography.heading.h2,
    4: typography.heading.h3,
    5: typography.heading.h4,
    6: typography.heading.h5,
    7: typography.body.base,
    8: typography.caption.base,
  };
  
  return hierarchyMap[level] || typography.body.base;
};

// Responsive typography helper
export const responsiveText = (
  mobile: string,
  tablet: string,
  desktop: string
) => {
  return cn(
    mobile,
    `md:${tablet}`,
    `lg:${desktop}`
  );
};

// Text truncation utilities
export const truncate = {
  single: 'truncate',
  multi: (lines: number) => `line-clamp-${lines}`,
};

// Reading optimized widths
export const readingWidth = {
  narrow: 'max-w-prose', // ~65ch
  normal: 'max-w-3xl',   // ~80ch
  wide: 'max-w-5xl',      // ~100ch
  full: 'max-w-none',
};