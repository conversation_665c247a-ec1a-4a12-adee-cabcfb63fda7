/**
 * Mobile Touch and Gesture Utilities
 * Following Apple Human Interface Guidelines for touch targets and gestures
 */

import { PanInfo, useAnimation } from 'framer-motion';
import { useCallback, useEffect, useState } from 'react';

// Apple HIG minimum touch target size (44px)
export const TOUCH_TARGET_SIZE = 44;

// Gesture thresholds
export const SWIPE_THRESHOLD = 50;
export const SWIPE_VELOCITY_THRESHOLD = 500;

export type SwipeDirection = 'left' | 'right' | 'up' | 'down';

export interface SwipeGesture {
  direction: SwipeDirection;
  distance: number;
  velocity: number;
}

/**
 * Ensures touch targets meet minimum accessibility requirements
 */
export const ensureTouchTarget = (currentSize: number): number => {
  return Math.max(currentSize, TOUCH_TARGET_SIZE);
};

/**
 * CSS class for minimum touch targets
 */
export const touchTargetClass = 'min-h-[44px] min-w-[44px]';

/**
 * Detect swipe gestures from pan info
 */
export const detectSwipe = (
  info: PanInfo,
  thresholds: {
    distance?: number;
    velocity?: number;
  } = {}
): SwipeGesture | null => {
  const { distance = SWIPE_THRESHOLD, velocity = SWIPE_VELOCITY_THRESHOLD } = thresholds;
  const { offset, velocity: vel } = info;
  
  const absX = Math.abs(offset.x);
  const absY = Math.abs(offset.y);
  const absVelX = Math.abs(vel.x);
  const absVelY = Math.abs(vel.y);
  
  // Check if gesture meets minimum requirements
  const hasMinDistance = Math.max(absX, absY) >= distance;
  const hasMinVelocity = Math.max(absVelX, absVelY) >= velocity;
  
  if (!hasMinDistance && !hasMinVelocity) {
    return null;
  }
  
  // Determine primary direction
  let direction: SwipeDirection;
  let swipeDistance: number;
  let swipeVelocity: number;
  
  if (absX > absY) {
    // Horizontal swipe
    direction = offset.x > 0 ? 'right' : 'left';
    swipeDistance = absX;
    swipeVelocity = absVelX;
  } else {
    // Vertical swipe
    direction = offset.y > 0 ? 'down' : 'up';
    swipeDistance = absY;
    swipeVelocity = absVelY;
  }
  
  return {
    direction,
    distance: swipeDistance,
    velocity: swipeVelocity,
  };
};

/**
 * Hook for swipe gesture detection
 */
export const useSwipeGesture = (
  onSwipe: (gesture: SwipeGesture) => void,
  thresholds?: { distance?: number; velocity?: number }
) => {
  const handlePanEnd = useCallback(
    (_: any, info: PanInfo) => {
      const gesture = detectSwipe(info, thresholds);
      if (gesture) {
        onSwipe(gesture);
      }
    },
    [onSwipe, thresholds]
  );

  return { onPanEnd: handlePanEnd };
};

/**
 * Hook for drag to dismiss functionality
 */
export const useDragToDismiss = (
  onDismiss: () => void,
  threshold: number = 150,
  axis: 'x' | 'y' = 'x'
) => {
  const controls = useAnimation();
  const [isDragging, setIsDragging] = useState(false);

  const handleDragStart = useCallback(() => {
    setIsDragging(true);
  }, []);

  const handleDragEnd = useCallback(
    (_: any, info: PanInfo) => {
      setIsDragging(false);
      const distance = axis === 'x' ? Math.abs(info.offset.x) : Math.abs(info.offset.y);
      
      if (distance > threshold) {
        // Animate off screen then dismiss
        const exitDistance = axis === 'x' ? 
          (info.offset.x > 0 ? 300 : -300) :
          (info.offset.y > 0 ? 300 : -300);
        
        controls.start({
          [axis]: exitDistance,
          opacity: 0,
          transition: { duration: 0.3, ease: [0.4, 0, 0.2, 1] }
        }).then(() => {
          onDismiss();
        });
      } else {
        // Spring back to original position
        controls.start({
          x: 0,
          y: 0,
          opacity: 1,
          transition: { type: 'spring', stiffness: 300, damping: 30 }
        });
      }
    },
    [controls, onDismiss, threshold, axis]
  );

  return {
    animate: controls,
    drag: axis,
    dragConstraints: { [axis === 'x' ? 'left' : 'top']: -threshold * 2, [axis === 'x' ? 'right' : 'bottom']: threshold * 2 },
    dragElastic: 0.2,
    onDragStart: handleDragStart,
    onDragEnd: handleDragEnd,
    isDragging,
  };
};

/**
 * Hook for long press detection
 */
export const useLongPress = (
  onLongPress: () => void,
  delay: number = 500
) => {
  const [isPressed, setIsPressed] = useState(false);

  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    if (isPressed) {
      timeoutId = setTimeout(() => {
        onLongPress();
        setIsPressed(false);
      }, delay);
    }

    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [isPressed, onLongPress, delay]);

  const handleStart = useCallback(() => {
    setIsPressed(true);
  }, []);

  const handleEnd = useCallback(() => {
    setIsPressed(false);
  }, []);

  return {
    onPointerDown: handleStart,
    onPointerUp: handleEnd,
    onPointerLeave: handleEnd,
    onPointerCancel: handleEnd,
    isPressed,
  };
};

/**
 * Haptic feedback simulation (visual feedback on web)
 */
export const simulateHapticFeedback = (
  type: 'light' | 'medium' | 'heavy' = 'light',
  element?: HTMLElement
) => {
  // On devices with haptic feedback, this would trigger actual haptics
  // For web, we provide visual feedback
  
  if (element) {
    const intensity = {
      light: 'scale-[1.02]',
      medium: 'scale-[1.05]',
      heavy: 'scale-[1.08]'
    }[type];
    
    element.classList.add(intensity);
    element.classList.add('transition-transform');
    element.classList.add('duration-75');
    
    setTimeout(() => {
      element.classList.remove(intensity);
      setTimeout(() => {
        element.classList.remove('transition-transform', 'duration-75');
      }, 75);
    }, 75);
  }
  
  // Try to use native haptic feedback if available
  if ('vibrate' in navigator) {
    const pattern = {
      light: [10],
      medium: [20],
      heavy: [30]
    }[type];
    
    navigator.vibrate(pattern);
  }
};

/**
 * Touch-friendly button props
 */
export const touchFriendlyProps = {
  className: touchTargetClass,
  style: { WebkitTapHighlightColor: 'transparent' } as React.CSSProperties,
};

/**
 * Check if device supports touch
 */
export const isTouchDevice = (): boolean => {
  return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
};

/**
 * Get optimal touch target size based on content
 */
export const getOptimalTouchSize = (contentSize: number, padding: number = 8): number => {
  const totalSize = contentSize + (padding * 2);
  return ensureTouchTarget(totalSize);
};