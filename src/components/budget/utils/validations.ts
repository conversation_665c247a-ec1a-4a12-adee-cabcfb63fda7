import { ExpenseFormData, ExpenseValidationRules, defaultExpenseValidationRules } from '../types/expense.types';

export interface ValidationError {
  field: string;
  message: string;
}

export const validateExpense = (
  data: Partial<ExpenseFormData>,
  rules: ExpenseValidationRules = defaultExpenseValidationRules
): ValidationError[] => {
  const errors: ValidationError[] = [];

  // Title validation
  if (rules.title.required && !data.title?.trim()) {
    errors.push({ field: 'title', message: 'Title is required' });
  } else if (data.title) {
    if (data.title.length < rules.title.minLength) {
      errors.push({ field: 'title', message: `Title must be at least ${rules.title.minLength} characters` });
    }
    if (data.title.length > rules.title.maxLength) {
      errors.push({ field: 'title', message: `Title must be less than ${rules.title.maxLength} characters` });
    }
  }

  // Amount validation
  if (rules.amount.required && (data.amount === undefined || data.amount === null)) {
    errors.push({ field: 'amount', message: 'Amount is required' });
  } else if (data.amount !== undefined) {
    if (data.amount < rules.amount.min) {
      errors.push({ field: 'amount', message: `Amount must be at least ${rules.amount.min}` });
    }
    if (data.amount > rules.amount.max) {
      errors.push({ field: 'amount', message: `Amount must be less than ${rules.amount.max}` });
    }
  }

  // Category validation
  if (rules.category.required && !data.category) {
    errors.push({ field: 'category', message: 'Category is required' });
  }

  // Date validation
  if (rules.date.required && !data.date) {
    errors.push({ field: 'date', message: 'Date is required' });
  } else if (data.date) {
    const date = new Date(data.date);
    if (date > rules.date.maxDate) {
      errors.push({ field: 'date', message: 'Date cannot be in the future' });
    }
  }

  // Description validation
  if (data.description && data.description.length > rules.description.maxLength) {
    errors.push({ field: 'description', message: `Description must be less than ${rules.description.maxLength} characters` });
  }

  // Recurrence validation
  if (data.recurrence && data.recurrence !== 'none' && !data.recurrence_end_date) {
    errors.push({ field: 'recurrence_end_date', message: 'End date is required for recurring expenses' });
  }

  return errors;
};

export const validateBudgetAmount = (amount: number, currentBudget?: number): ValidationError[] => {
  const errors: ValidationError[] = [];

  if (!amount || amount <= 0) {
    errors.push({ field: 'amount', message: 'Budget amount must be greater than 0' });
  }

  if (amount > 10000000) {
    errors.push({ field: 'amount', message: 'Budget amount seems too high' });
  }

  if (currentBudget && amount < currentBudget * 0.5) {
    errors.push({ field: 'amount', message: 'New budget is less than 50% of current budget. Are you sure?' });
  }

  return errors;
};

export const validateDepartmentName = (name: string, existingNames: string[]): ValidationError[] => {
  const errors: ValidationError[] = [];

  if (!name.trim()) {
    errors.push({ field: 'name', message: 'Department name is required' });
  }

  if (name.length < 2) {
    errors.push({ field: 'name', message: 'Department name must be at least 2 characters' });
  }

  if (name.length > 50) {
    errors.push({ field: 'name', message: 'Department name must be less than 50 characters' });
  }

  if (existingNames.includes(name.trim())) {
    errors.push({ field: 'name', message: 'Department name already exists' });
  }

  return errors;
};

export const validateCategoryLimit = (limit: number, totalBudget: number, categoryName: string): ValidationError[] => {
  const errors: ValidationError[] = [];

  if (limit <= 0) {
    errors.push({ field: 'limit', message: 'Limit must be greater than 0' });
  }

  if (limit > totalBudget) {
    errors.push({ field: 'limit', message: 'Limit cannot exceed total budget' });
  }

  if (limit > totalBudget * 0.5) {
    errors.push({ field: 'limit', message: `Warning: ${categoryName} limit is more than 50% of total budget` });
  }

  return errors;
};

export const validateQuarterlyPercentages = (percentages: number[]): ValidationError[] => {
  const errors: ValidationError[] = [];
  const sum = percentages.reduce((acc, p) => acc + p, 0);

  if (Math.abs(sum - 100) > 0.01) {
    errors.push({ field: 'percentages', message: 'Quarterly percentages must sum to 100%' });
  }

  percentages.forEach((p, index) => {
    if (p < 0) {
      errors.push({ field: `quarter${index + 1}`, message: `Q${index + 1} percentage cannot be negative` });
    }
    if (p > 100) {
      errors.push({ field: `quarter${index + 1}`, message: `Q${index + 1} percentage cannot exceed 100%` });
    }
  });

  return errors;
};