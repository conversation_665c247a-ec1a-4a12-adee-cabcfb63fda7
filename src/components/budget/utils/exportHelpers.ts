import { format } from 'date-fns';
import { Budget, Stats, Expense, CategoryTotal, DepartmentAllocation } from '@/stores/budgetStore';

// CSV Export Helpers
export const exportToCSV = (data: any[], filename: string) => {
  if (!data || data.length === 0) {
    console.warn('No data to export');
    return;
  }

  // Get headers from the first object
  const headers = Object.keys(data[0]);
  
  // Create CSV content
  const csvContent = [
    headers.join(','), // Header row
    ...data.map(row => 
      headers.map(header => {
        const value = row[header];
        // Handle special cases
        if (value === null || value === undefined) return '';
        if (typeof value === 'string' && value.includes(',')) {
          return `"${value.replace(/"/g, '""')}"`;
        }
        if (value instanceof Date) {
          return format(value, 'yyyy-MM-dd');
        }
        return value;
      }).join(',')
    )
  ].join('\n');

  // Create blob and download
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  
  link.setAttribute('href', url);
  link.setAttribute('download', `${filename}.csv`);
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

// Format data for CSV export
export const formatExpensesForCSV = (expenses: Expense[]) => {
  return expenses.map(expense => ({
    'ID': expense.id,
    'Title': expense.title,
    'Description': expense.description,
    'Amount': expense.amount,
    'Category': expense.category,
    'Department': expense.department_id || 'N/A',
    'Status': expense.status,
    'Date': format(new Date(expense.date), 'yyyy-MM-dd'),
    'Recurrence': expense.recurrence,
    'Created': format(new Date(expense.created_at), 'yyyy-MM-dd HH:mm'),
    'Updated': format(new Date(expense.updated_at), 'yyyy-MM-dd HH:mm')
  }));
};

export const formatBudgetSummaryForCSV = (budget: Budget | null, stats: Stats | null) => {
  if (!budget || !stats) return [];
  
  return [{
    'Year': budget.year,
    'Total Budget': budget.total_amount,
    'Total Spent': stats.spent,
    'Remaining': stats.remaining,
    'Utilization %': ((stats.spent / budget.total_amount) * 100).toFixed(2),
    'Pending Expenses': stats.count_pending,
    'Approved Expenses': stats.count_approved,
    'Rejected Expenses': stats.count_rejected
  }];
};

export const formatCategoryBreakdownForCSV = (categories: CategoryTotal[]) => {
  return categories.map(cat => ({
    'Category': cat.category,
    'Total Spent': cat.total,
    'Percentage': ((cat.total / categories.reduce((sum, c) => sum + c.total, 0)) * 100).toFixed(2) + '%'
  }));
};

export const formatDepartmentBreakdownForCSV = (departments: DepartmentAllocation[]) => {
  return departments.map(dept => ({
    'Department': dept.name,
    'Allocated Amount': dept.allocated_amount,
    'Spent Amount': dept.spent_amount,
    'Remaining': dept.allocated_amount - dept.spent_amount,
    'Utilization %': ((dept.spent_amount / dept.allocated_amount) * 100).toFixed(2),
    'Budget %': dept.percentage.toFixed(2)
  }));
};

// Report generation helpers
export interface ReportData {
  budget: Budget | null;
  stats: Stats | null;
  expenses: Expense[];
  departments: DepartmentAllocation[];
  analytics?: any;
  period: {
    start: Date;
    end: Date;
  };
}

export const generateReportFilename = (reportType: string, period: { start: Date; end: Date }) => {
  const dateRange = `${format(period.start, 'yyyyMMdd')}-${format(period.end, 'yyyyMMdd')}`;
  return `budget-report-${reportType}-${dateRange}`;
};

// Generate executive summary data
export const generateExecutiveSummaryData = (data: ReportData) => {
  const { budget, stats, expenses } = data;
  
  if (!budget || !stats) return null;
  
  const periodExpenses = expenses.filter(exp => {
    const expDate = new Date(exp.date);
    return expDate >= data.period.start && expDate <= data.period.end;
  });
  
  const periodSpent = periodExpenses.reduce((sum, exp) => sum + exp.amount, 0);
  const avgMonthlySpend = periodSpent / Math.max(1, 
    (data.period.end.getTime() - data.period.start.getTime()) / (30 * 24 * 60 * 60 * 1000)
  );
  
  return {
    overview: {
      totalBudget: budget.total_amount,
      totalSpent: stats.spent,
      remaining: stats.remaining,
      utilizationRate: (stats.spent / budget.total_amount) * 100,
      periodSpent,
      avgMonthlySpend
    },
    keyMetrics: {
      totalExpenses: expenses.length,
      periodExpenses: periodExpenses.length,
      avgExpenseAmount: periodSpent / Math.max(1, periodExpenses.length),
      topCategory: stats.by_category.reduce((max, cat) => 
        cat.total > (max?.total || 0) ? cat : max, null as CategoryTotal | null
      ),
      approvalRate: (stats.count_approved / Math.max(1, stats.count_approved + stats.count_rejected)) * 100
    },
    trends: {
      monthOverMonth: calculateMonthOverMonthGrowth(expenses),
      projectedYearEnd: avgMonthlySpend * 12,
      budgetPace: (stats.spent / budget.total_amount) > (new Date().getMonth() + 1) / 12 ? 'ahead' : 'on-track'
    }
  };
};

// Calculate month-over-month growth
const calculateMonthOverMonthGrowth = (expenses: Expense[]) => {
  const monthlyTotals = expenses.reduce((acc, exp) => {
    const month = format(new Date(exp.date), 'yyyy-MM');
    acc[month] = (acc[month] || 0) + exp.amount;
    return acc;
  }, {} as Record<string, number>);
  
  const months = Object.keys(monthlyTotals).sort();
  if (months.length < 2) return 0;
  
  const lastMonth = monthlyTotals[months[months.length - 1]];
  const previousMonth = monthlyTotals[months[months.length - 2]];
  
  return previousMonth > 0 ? ((lastMonth - previousMonth) / previousMonth) * 100 : 0;
};

// Generate department analysis data
export const generateDepartmentAnalysisData = (data: ReportData) => {
  const { departments, expenses } = data;
  
  const departmentStats = departments.map(dept => {
    const deptExpenses = expenses.filter(exp => exp.department_id === dept.id);
    const periodExpenses = deptExpenses.filter(exp => {
      const expDate = new Date(exp.date);
      return expDate >= data.period.start && expDate <= data.period.end;
    });
    
    return {
      ...dept,
      totalExpenses: deptExpenses.length,
      periodExpenses: periodExpenses.length,
      periodSpent: periodExpenses.reduce((sum, exp) => sum + exp.amount, 0),
      categories: groupByCategory(periodExpenses),
      efficiency: dept.allocated_amount > 0 ? (dept.spent_amount / dept.allocated_amount) * 100 : 0
    };
  });
  
  return departmentStats.sort((a, b) => b.periodSpent - a.periodSpent);
};

// Group expenses by category
const groupByCategory = (expenses: Expense[]) => {
  return expenses.reduce((acc, exp) => {
    acc[exp.category] = (acc[exp.category] || 0) + exp.amount;
    return acc;
  }, {} as Record<string, number>);
};

// Generate category analysis data
export const generateCategoryAnalysisData = (data: ReportData) => {
  const { stats, expenses } = data;
  
  if (!stats) return null;
  
  const periodExpenses = expenses.filter(exp => {
    const expDate = new Date(exp.date);
    return expDate >= data.period.start && expDate <= data.period.end;
  });
  
  const categoryData = stats.by_category.map(cat => {
    const catExpenses = periodExpenses.filter(exp => exp.category === cat.category);
    const avgExpense = catExpenses.length > 0 ? 
      catExpenses.reduce((sum, exp) => sum + exp.amount, 0) / catExpenses.length : 0;
    
    return {
      ...cat,
      expenseCount: catExpenses.length,
      avgExpenseAmount: avgExpense,
      percentage: (cat.total / stats.spent) * 100,
      trend: calculateCategoryTrend(expenses, cat.category)
    };
  });
  
  return categoryData.sort((a, b) => b.total - a.total);
};

// Calculate category spending trend
const calculateCategoryTrend = (expenses: Expense[], category: string): 'up' | 'down' | 'stable' => {
  const monthlyTotals = expenses
    .filter(exp => exp.category === category)
    .reduce((acc, exp) => {
      const month = format(new Date(exp.date), 'yyyy-MM');
      acc[month] = (acc[month] || 0) + exp.amount;
      return acc;
    }, {} as Record<string, number>);
  
  const months = Object.keys(monthlyTotals).sort();
  if (months.length < 3) return 'stable';
  
  const recent = months.slice(-3).map(m => monthlyTotals[m]);
  const avgRecent = recent.reduce((sum, val) => sum + val, 0) / recent.length;
  const avgPrevious = months.slice(0, -3).reduce((sum, m) => sum + monthlyTotals[m], 0) / Math.max(1, months.length - 3);
  
  const change = avgPrevious > 0 ? ((avgRecent - avgPrevious) / avgPrevious) * 100 : 0;
  
  if (change > 10) return 'up';
  if (change < -10) return 'down';
  return 'stable';
};