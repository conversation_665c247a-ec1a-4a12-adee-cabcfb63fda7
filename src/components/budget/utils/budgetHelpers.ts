import { CategoryTotal } from '@/stores/budgetStore';

export const calculatePercentage = (value: number, total: number): number => {
  if (total === 0) return 0;
  return Math.min(100, (value / total) * 100);
};

export const validateBudgetAmount = (amount: number): { isValid: boolean; error?: string } => {
  if (amount <= 0) {
    return { isValid: false, error: 'Budget amount must be greater than 0' };
  }
  
  if (amount > 1000000000) {
    return { isValid: false, error: 'Budget amount exceeds maximum allowed value' };
  }
  
  return { isValid: true };
};

export const sortCategoriesBySpending = (categories: CategoryTotal[]): CategoryTotal[] => {
  return [...categories].sort((a, b) => b.total - a.total);
};

export const getStatusColor = (status: 'pending' | 'approved' | 'rejected'): string => {
  const colors = {
    pending: 'text-yellow-600',
    approved: 'text-green-600',
    rejected: 'text-red-600',
  };
  
  return colors[status];
};

export const getBudgetHealthStatus = (spent: number, total: number): {
  status: 'healthy' | 'warning' | 'critical';
  message: string;
} => {
  const percentage = calculatePercentage(spent, total);
  
  if (percentage < 70) {
    return { status: 'healthy', message: 'Budget is on track' };
  } else if (percentage < 90) {
    return { status: 'warning', message: 'Approaching budget limit' };
  } else {
    return { status: 'critical', message: 'Budget nearly exhausted' };
  }
};