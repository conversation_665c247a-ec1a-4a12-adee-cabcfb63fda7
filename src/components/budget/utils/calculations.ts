/**
 * Budget calculation utilities
 */

/**
 * Calculate the percentage of budget utilization
 */
export function calculateUtilization(spent: number, total: number): number {
  if (total <= 0) return 0;
  return (spent / total) * 100;
}

/**
 * Calculate the burn rate based on current spending and time elapsed
 */
export function calculateBurnRate(
  spent: number,
  total: number,
  currentMonth: number = new Date().getMonth() + 1
): number {
  const expectedSpending = (total / 12) * currentMonth;
  if (expectedSpending <= 0) return 0;
  return spent / expectedSpending;
}

/**
 * Calculate remaining budget
 */
export function calculateRemaining(
  total: number,
  spent: number,
  committed: number = 0
): number {
  return total - spent - committed;
}

/**
 * Determine if budget is over allocated
 */
export function isOverBudget(
  total: number,
  spent: number,
  committed: number = 0
): boolean {
  return calculateRemaining(total, spent, committed) < 0;
}

/**
 * Calculate projected year-end spending based on current burn rate
 */
export function calculateProjectedYearEnd(
  currentSpent: number,
  currentMonth: number = new Date().getMonth() + 1
): number {
  if (currentMonth <= 0) return 0;
  return (currentSpent / currentMonth) * 12;
}

/**
 * Calculate variance between actual and planned spending
 */
export function calculateVariance(
  actual: number,
  planned: number
): { amount: number; percentage: number } {
  const amount = actual - planned;
  const percentage = planned > 0 ? (amount / planned) * 100 : 0;
  return { amount, percentage };
}

/**
 * Calculate average daily spending
 */
export function calculateDailyAverage(
  spent: number,
  days: number = new Date().getDate()
): number {
  if (days <= 0) return 0;
  return spent / days;
}

/**
 * Calculate monthly average spending
 */
export function calculateMonthlyAverage(
  spent: number,
  months: number = new Date().getMonth() + 1
): number {
  if (months <= 0) return 0;
  return spent / months;
}

/**
 * Get fiscal quarter from month
 */
export function getFiscalQuarter(month: number = new Date().getMonth() + 1): number {
  return Math.ceil(month / 3);
}

/**
 * Calculate days remaining in the year
 */
export function getDaysRemaining(): number {
  const now = new Date();
  const endOfYear = new Date(now.getFullYear(), 11, 31);
  const diffTime = endOfYear.getTime() - now.getTime();
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
}

/**
 * Calculate months remaining in the year
 */
export function getMonthsRemaining(): number {
  return 12 - (new Date().getMonth() + 1);
}

/**
 * Categorize spending level
 */
export function categorizeSpendingLevel(
  utilization: number
): 'low' | 'normal' | 'high' | 'critical' {
  if (utilization < 30) return 'low';
  if (utilization < 70) return 'normal';
  if (utilization < 90) return 'high';
  return 'critical';
}

/**
 * Calculate budget health score (0-100)
 */
export function calculateHealthScore(
  utilization: number,
  burnRate: number,
  monthsElapsed: number = new Date().getMonth() + 1
): number {
  let score = 100;
  
  // Deduct for over-utilization
  const expectedUtilization = (monthsElapsed / 12) * 100;
  const utilizationDiff = utilization - expectedUtilization;
  
  if (utilizationDiff > 20) score -= 40;
  else if (utilizationDiff > 10) score -= 25;
  else if (utilizationDiff > 5) score -= 15;
  
  // Deduct for high burn rate
  if (burnRate > 1.3) score -= 30;
  else if (burnRate > 1.15) score -= 20;
  else if (burnRate > 1.05) score -= 10;
  
  // Bonus for being under budget
  if (utilizationDiff < -10) score += 10;
  
  return Math.max(0, Math.min(100, score));
}