# Budget Component Design System

Apple-inspired design system for the Training Budget component, following Human Interface Guidelines and design principles.

## Philosophy

This design system is built around Apple's core design principles:

- **Clarity**: Clean typography and generous whitespace
- **Deference**: Content takes precedence over interface
- **Depth**: Subtle visual cues create hierarchy and vitality

## Structure

```
design-tokens/
├── index.ts              # Design tokens (JavaScript/TypeScript)
├── design-system.css     # CSS custom properties & utilities
├── tailwind.config.js    # Extended Tailwind configuration
└── README.md            # This file
```

## Usage

### 1. TypeScript/JavaScript

Import the design tokens in your components:

```typescript
import { colors, typography, spacing, shadows } from './design-tokens';

// Use in styled-components or inline styles
const StyledCard = styled.div`
  background: ${colors.neutral[50]};
  padding: ${spacing[6]};
  border-radius: ${borderRadius.lg};
  box-shadow: ${shadows.sm};
`;
```

### 2. CSS Custom Properties

Include the CSS file in your main stylesheet:

```css
@import './components/budget/design-tokens/design-system.css';

/* Use CSS custom properties */
.my-card {
  background: rgb(var(--color-background-secondary));
  padding: var(--spacing-6);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
}
```

### 3. Tailwind CSS Classes

Extend your Tailwind config with our tokens:

```javascript
// tailwind.config.js
const budgetDesignSystem = require('./src/components/budget/design-tokens/tailwind.config.js');

module.exports = {
  // ... your existing config
  theme: {
    extend: {
      ...budgetDesignSystem.theme.extend
    }
  }
}
```

Then use the classes:

```jsx
<div className="bg-neutral-50 p-6 rounded-lg shadow-sm">
  <h2 className="text-xl font-semibold text-neutral-900">
    Budget Overview
  </h2>
</div>
```

## Color System

### Primary Colors

Based on Apple's system colors, optimized for accessibility (WCAG 2.1 AA):

- **Blue** (`#007AFF`): Primary actions, links, selected states
- **Green** (`#34C759`): Success states, positive values
- **Orange** (`#FF9500`): Warnings, attention required
- **Red** (`#FF3B30`): Errors, destructive actions
- **Purple** (`#5856D6`): Accent color for special features
- **Pink** (`#FF2D92`): Secondary accent color

### Neutral Scale

50-step neutral scale for backgrounds, text, and borders:

```css
/* Light theme */
--color-neutral-50: #f9fafb;  /* Lightest background */
--color-neutral-900: #111827; /* Darkest text */

/* Dark theme automatically switches */
```

### Semantic Colors

Contextual color mappings:

```typescript
// Background colors
colors.background.primary    // Main background
colors.background.secondary  // Card backgrounds  
colors.background.tertiary   // Subtle backgrounds

// Text colors
colors.text.primary     // Main text (high contrast)
colors.text.secondary   // Secondary text (medium contrast)
colors.text.tertiary    // Placeholder text (low contrast)

// Interactive colors
colors.interactive.default  // Default state
colors.interactive.hover    // Hover state
colors.interactive.active   // Active/pressed state
```

## Typography

### Font Stack

Apple's system font stack for optimal readability:

```css
font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display", 
             "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
```

### Scale

6-level type scale based on modular scale principles:

| Size | Value | Usage |
|------|-------|-------|
| `xs` | 12px | Captions, metadata |
| `sm` | 14px | Body text, labels |
| `base` | 16px | Default text size |
| `lg` | 18px | Emphasized text |
| `xl` | 20px | Small headings |
| `2xl` | 24px | Section headings |
| `3xl` | 30px | Page headings |
| `4xl` | 36px | Hero headings |

### Weights

- **400** (Normal): Body text
- **500** (Medium): Emphasized text, labels
- **600** (Semibold): Headings, buttons
- **700** (Bold): Strong emphasis

## Spacing System

8px grid system for consistent spacing:

| Token | Value | Usage |
|-------|-------|-------|
| `1` | 4px | Tight spacing |
| `2` | 8px | Base unit |
| `3` | 12px | Small gaps |
| `4` | 16px | Standard spacing |
| `6` | 24px | Section spacing |
| `8` | 32px | Large spacing |
| `12` | 48px | Extra large spacing |

### Touch Targets

Minimum 44px (11 spacing units) for touch-friendly interfaces:

```css
.tap-target {
  min-height: var(--spacing-11); /* 44px */
  min-width: var(--spacing-11);  /* 44px */
}
```

## Shadows

Apple-style depth with subtle, layered shadows:

- **sm**: Subtle cards, buttons
- **md**: Elevated cards, dropdowns
- **lg**: Modals, overlays
- **xl**: Hero elements
- **float**: Floating action buttons
- **modal**: Modal dialogs

## Border Radius

Rounded corners matching Apple's design language:

- **sm** (4px): Buttons, badges
- **md** (8px): Input fields, small cards
- **lg** (12px): Cards, panels
- **xl** (16px): Modals, large elements
- **2xl** (24px): Hero elements

## Animations

Apple-style timing curves and durations:

### Easing Functions

- **ease-in-out**: `cubic-bezier(0.4, 0.0, 0.2, 1)` - Default
- **ease-out**: `cubic-bezier(0.0, 0.0, 0.2, 1)` - Entrances
- **ease-in**: `cubic-bezier(0.4, 0.0, 1, 1)` - Exits

### Durations

- **fast** (150ms): Micro-interactions
- **default** (250ms): Standard transitions
- **slow** (350ms): Page transitions
- **slower** (500ms): Complex animations

## Component Variants

### Buttons

```typescript
// Primary button
<button className="bg-primary-500 text-white hover:bg-primary-600 
                   px-4 py-2 rounded-md font-medium transition-colors">
  Primary Action
</button>

// Secondary button
<button className="bg-neutral-100 text-neutral-900 hover:bg-neutral-200 
                   border border-neutral-200 px-4 py-2 rounded-md font-medium">
  Secondary Action
</button>

// Ghost button
<button className="text-neutral-600 hover:bg-neutral-100 
                   px-4 py-2 rounded-md font-medium">
  Ghost Action
</button>
```

### Cards

```typescript
// Default card
<div className="bg-neutral-100 border border-neutral-200 
                rounded-lg shadow-sm p-6">
  Card content
</div>

// Elevated card
<div className="bg-white rounded-lg shadow-lg p-6">
  Elevated content
</div>

// Outlined card
<div className="bg-transparent border-2 border-neutral-300 
                rounded-lg p-6">
  Outlined content
</div>
```

## Accessibility

### WCAG 2.1 AA Compliance

- **Contrast ratios**: All text meets 4.5:1 minimum
- **Color blindness**: Semantic colors don't rely solely on hue
- **Focus indicators**: Clear, high-contrast focus rings
- **Touch targets**: Minimum 44px for interactive elements

### Dark Mode

Automatic dark mode support via CSS custom properties:

```css
@media (prefers-color-scheme: dark) {
  :root {
    --color-background-primary: var(--color-neutral-900);
    --color-text-primary: var(--color-neutral-50);
  }
}
```

## Best Practices

### Do's

✅ Use semantic color names (`text-primary` not `text-gray-900`)
✅ Follow the 8px spacing grid
✅ Use system font stack for consistency
✅ Apply proper contrast ratios
✅ Include focus states for all interactive elements
✅ Use appropriate timing curves for animations

### Don'ts

❌ Don't use arbitrary spacing values
❌ Don't hardcode colors - use tokens
❌ Don't forget mobile touch targets (44px minimum)
❌ Don't rely solely on color for meaning
❌ Don't use overly complex animations
❌ Don't ignore dark mode considerations

## Migration Guide

### From Existing Styles

1. **Replace hardcoded colors** with semantic tokens:
   ```css
   /* Before */
   background: #f3f4f6;
   color: #111827;
   
   /* After */
   background: rgb(var(--color-background-secondary));
   color: rgb(var(--color-text-primary));
   ```

2. **Update spacing** to use grid system:
   ```css
   /* Before */
   padding: 15px 20px;
   
   /* After */
   padding: var(--spacing-4) var(--spacing-5);
   ```

3. **Apply consistent border radius**:
   ```css
   /* Before */
   border-radius: 6px;
   
   /* After */
   border-radius: var(--radius-md);
   ```

### Component Updates

Update existing components to use the new system:

```typescript
// Old approach
const Card = styled.div`
  background: #f3f4f6;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
`;

// New approach
const Card = styled.div`
  background: rgb(var(--color-background-secondary));
  padding: var(--spacing-6);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
`;
```

## Examples

See the enhanced components for implementation examples:

- `SpendingTrendsChart.tsx` - Chart with Apple-style colors and interactions
- `BudgetHealthScore.tsx` - Card design and semantic colors
- `ExpenseForm.tsx` - Form inputs and validation states
- `BudgetOverviewCards.tsx` - Card variants and hover states

---

This design system provides a solid foundation for building beautiful, accessible, and consistent budget management interfaces that feel at home in Apple's ecosystem.