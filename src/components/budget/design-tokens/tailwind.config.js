/**
 * Extended Tailwind Config for Budget Component
 * Apple-inspired design system integration
 */

const { colors, typography, spacing, borderRadius, shadows, transitions, zIndex, breakpoints } = require('./index');

module.exports = {
  theme: {
    extend: {
      // Apple-inspired color palette
      colors: {
        // Primary brand colors
        primary: colors.primary,
        success: colors.success,
        warning: colors.warning,
        error: colors.error,
        neutral: colors.neutral,
        purple: colors.purple,
        pink: colors.pink,
        
        // Semantic aliases for better DX
        blue: colors.primary,
        green: colors.success,
        orange: colors.warning,
        red: colors.error,
        gray: colors.neutral,
      },
      
      // Typography system
      fontFamily: typography.fontFamily,
      fontSize: typography.fontSize,
      fontWeight: typography.fontWeight,
      letterSpacing: typography.letterSpacing,
      
      // Spacing (8px grid system)
      spacing: spacing,
      
      // Border radius (Apple-style rounded corners)
      borderRadius: borderRadius,
      
      // Box shadows (Apple-style depth)
      boxShadow: shadows,
      
      // Animation & transitions
      transitionTimingFunction: {
        'apple-easeInOut': transitions.easeInOut,
        'apple-easeOut': transitions.easeOut,
        'apple-easeIn': transitions.easeIn,
      },
      transitionDuration: transitions.duration,
      
      // Z-index scale
      zIndex: zIndex,
      
      // Breakpoints
      screens: breakpoints,
      
      // Custom animations
      animation: {
        'fade-in': 'fadeIn 0.25s ease-out',
        'slide-in': 'slideIn 0.25s ease-out',
        'scale-in': 'scaleIn 0.25s ease-out',
        'shimmer': 'shimmer 2s linear infinite',
      },
      
      // Keyframes for animations
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideIn: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        scaleIn: {
          '0%': { transform: 'scale(0.95)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
        shimmer: {
          '0%': { backgroundPosition: '-200% 0' },
          '100%': { backgroundPosition: '200% 0' },
        },
      },
      
      // Custom gradients
      backgroundImage: {
        'shimmer-gradient': 
          'linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent)',
        'apple-blue-gradient': 
          'linear-gradient(135deg, #007AFF 0%, #5856D6 100%)',
        'apple-green-gradient': 
          'linear-gradient(135deg, #34C759 0%, #30D158 100%)',
      },
      
      // Custom backdrop blur
      backdropBlur: {
        xs: '2px',
      },
      
      // Custom aspect ratios
      aspectRatio: {
        'card': '16 / 9',
        'square': '1 / 1',
      },
    },
  },
  
  // Plugins for additional utilities
  plugins: [
    // Custom utility classes
    function({ addUtilities, theme }) {
      const newUtilities = {
        // Apple-style focus ring
        '.focus-ring': {
          outline: 'none',
          'box-shadow': `0 0 0 2px ${theme('colors.primary.500')}, 0 0 0 4px ${theme('colors.primary.100')}`,
        },
        
        // Glass morphism effect
        '.glass': {
          'backdrop-filter': 'blur(20px) saturate(180%)',
          'background-color': 'rgba(255, 255, 255, 0.75)',
          'border': '1px solid rgba(209, 213, 219, 0.3)',
        },
        
        '.glass-dark': {
          'backdrop-filter': 'blur(20px) saturate(180%)',
          'background-color': 'rgba(17, 24, 39, 0.75)',
          'border': '1px solid rgba(75, 85, 99, 0.3)',
        },
        
        // Smooth scrolling
        '.smooth-scroll': {
          'scroll-behavior': 'smooth',
          '-webkit-overflow-scrolling': 'touch',
        },
        
        // Hide scrollbar while maintaining functionality
        '.hide-scrollbar': {
          '-ms-overflow-style': 'none',
          'scrollbar-width': 'none',
          '&::-webkit-scrollbar': {
            display: 'none',
          },
        },
        
        // Touch-friendly tap target
        '.tap-target': {
          'min-height': '44px',
          'min-width': '44px',
        },
        
        // Shimmer loading effect
        '.shimmer': {
          background: theme('colors.gray.200'),
          'background-image': theme('backgroundImage.shimmer-gradient'),
          'background-size': '200% 100%',
          'background-repeat': 'no-repeat',
          animation: theme('animation.shimmer'),
        },
        
        '.shimmer-dark': {
          background: theme('colors.gray.700'),
          'background-image': 'linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent)',
          'background-size': '200% 100%',
          'background-repeat': 'no-repeat',
          animation: theme('animation.shimmer'),
        },
      };
      
      addUtilities(newUtilities);
    },
  ],
};