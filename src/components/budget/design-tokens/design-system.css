/**
 * Budget Component Design System CSS
 * Professional, desktop-optimized design tokens
 * 
 * MIGRATION GUIDE FROM v1 to v2 (Compact Design):
 * ================================================
 * 
 * TYPOGRAPHY CHANGES:
 * - Base font: 16px → 14px (use var(--font-size-base))
 * - H1: 36px → 28px (use .heading-1 or var(--font-size-4xl))
 * - H2: 24px → 20px (use .heading-2 or var(--font-size-2xl))
 * - H3: 20px → 16px (use .heading-3 or var(--font-size-lg))
 * - Body: 16px → 14px (use .text-sm or var(--font-size-base))
 * 
 * SPACING CHANGES:
 * - Component padding: 24px → 16px (use var(--spacing-component-padding))
 * - Section gaps: 32px → 20px (use var(--spacing-section-gap))
 * - Element gaps: 16px → 12px (use var(--spacing-element-gap))
 * - Card padding: p-6 → p-4 in Tailwind classes
 * 
 * COMPONENT CHANGES:
 * - Button height: h-9 → h-8 (36px → 32px)
 * - Input height: h-9 → h-8 (36px → 32px)
 * - Touch targets: 44px → 36px desktop, 44px mobile
 * - Border radius: 12px → 8px for cards (var(--radius-lg))
 * 
 * RESPONSIVE APPROACH:
 * - Desktop-first design (compact by default)
 * - Mobile overrides with @media (max-width: 768px)
 * - Use .tap-target class for touch-friendly elements
 * 
 * NEW UTILITY CLASSES:
 * - .standard-card--compact: 12px padding variant
 * - .standard-card--dense: 8px padding variant
 * - .bento-grid--dense: 12px gap variant
 * - .p-compact, .p-standard, .p-relaxed: Spacing presets
 * - .gap-compact, .gap-standard, .gap-relaxed: Gap presets
 * 
 * QUICK CONVERSION:
 * - Replace text-base → text-sm for body text
 * - Replace p-6 → p-4 for card padding
 * - Replace gap-8 → gap-5 for section spacing
 * - Replace h-10 → h-9 for large buttons
 * - Replace rounded-xl → rounded-lg for cards
 */

:root {
  /* === COLOR SYSTEM === */
  
  /* Primary Colors (Apple Blue) */
  --color-primary-50: 239 246 255; /* #eff6ff */
  --color-primary-100: 219 234 254; /* #dbeafe */
  --color-primary-200: 191 219 254; /* #bfdbfe */
  --color-primary-300: 147 197 253; /* #93c5fd */
  --color-primary-400: 96 165 250; /* #60a5fa */
  --color-primary-500: 0 122 255; /* #007AFF - Apple Blue */
  --color-primary-600: 0 86 204; /* #0056CC */
  --color-primary-700: 0 61 154; /* #003d9a */
  --color-primary-800: 30 64 175; /* #1e40af */
  --color-primary-900: 30 58 138; /* #1e3a8a */
  
  /* Success Colors (Apple Green) */
  --color-success-50: 240 253 244; /* #f0fdf4 */
  --color-success-100: 220 252 231; /* #dcfce7 */
  --color-success-200: 187 247 208; /* #bbf7d0 */
  --color-success-300: 134 239 172; /* #86efac */
  --color-success-400: 74 222 128; /* #4ade80 */
  --color-success-500: 52 199 89; /* #34C759 - Apple Green */
  --color-success-600: 22 163 74; /* #16a34a */
  --color-success-700: 21 128 61; /* #15803d */
  --color-success-800: 22 101 52; /* #166534 */
  --color-success-900: 20 83 45; /* #14532d */
  
  /* Warning Colors (Apple Orange) */
  --color-warning-50: 255 251 235; /* #fffbeb */
  --color-warning-100: 254 243 199; /* #fef3c7 */
  --color-warning-200: 254 215 170; /* #fed7aa */
  --color-warning-300: 253 186 116; /* #fdba74 */
  --color-warning-400: 251 146 60; /* #fb923c */
  --color-warning-500: 255 149 0; /* #FF9500 - Apple Orange */
  --color-warning-600: 234 88 12; /* #ea580c */
  --color-warning-700: 194 65 12; /* #c2410c */
  --color-warning-800: 154 52 18; /* #9a3412 */
  --color-warning-900: 124 45 18; /* #7c2d12 */
  
  /* Error Colors (Apple Red) */
  --color-error-50: 254 242 242; /* #fef2f2 */
  --color-error-100: 254 226 226; /* #fee2e2 */
  --color-error-200: 254 202 202; /* #fecaca */
  --color-error-300: 252 165 165; /* #fca5a5 */
  --color-error-400: 248 113 113; /* #f87171 */
  --color-error-500: 255 59 48; /* #FF3B30 - Apple Red */
  --color-error-600: 220 38 38; /* #dc2626 */
  --color-error-700: 185 28 28; /* #b91c1c */
  --color-error-800: 153 27 27; /* #991b1b */
  --color-error-900: 127 29 29; /* #7f1d1d */
  
  /* Neutral Colors */
  --color-neutral-50: 249 250 251; /* #f9fafb */
  --color-neutral-100: 243 244 246; /* #f3f4f6 */
  --color-neutral-200: 229 231 235; /* #e5e7eb */
  --color-neutral-300: 209 213 219; /* #d1d5db */
  --color-neutral-400: 156 163 175; /* #9ca3af */
  --color-neutral-500: 107 114 128; /* #6b7280 */
  --color-neutral-600: 75 85 99; /* #4b5563 */
  --color-neutral-700: 55 65 81; /* #374151 */
  --color-neutral-800: 31 41 55; /* #1f2937 */
  --color-neutral-900: 17 24 39; /* #111827 */
  
  /* === SEMANTIC COLOR MAPPINGS === */
  
  /* Backgrounds */
  --color-background-primary: var(--color-neutral-50);
  --color-background-secondary: var(--color-neutral-100);
  --color-background-tertiary: var(--color-neutral-200);
  --color-background-inverse: var(--color-neutral-900);
  
  /* Text */
  --color-text-primary: var(--color-neutral-900);
  --color-text-secondary: var(--color-neutral-600);
  --color-text-tertiary: var(--color-neutral-400);
  --color-text-inverse: var(--color-neutral-50);
  --color-text-disabled: var(--color-neutral-300);
  
  /* Borders */
  --color-border-default: var(--color-neutral-200);
  --color-border-strong: var(--color-neutral-300);
  --color-border-subtle: var(--color-neutral-100);
  
  /* Interactive */
  --color-interactive-default: var(--color-primary-500);
  --color-interactive-hover: var(--color-primary-600);
  --color-interactive-active: var(--color-primary-700);
  --color-interactive-disabled: var(--color-neutral-300);
  --color-interactive-focus: var(--color-primary-500);
  
  /* === SEMANTIC COLOR MAPPINGS (2025 Finance Standards) === */
  --color-positive: var(--color-success-500); /* Green - Gains/Positive trends */
  --color-negative: var(--color-error-500); /* Red - Losses/Negative trends */
  --color-neutral: var(--color-warning-500); /* Orange - Neutral/Warning states */
  --color-primary-action: var(--color-primary-500); /* Blue - Primary CTAs */
  
  /* === TYPOGRAPHY === */
  --font-family-sans: -apple-system, BlinkMacSystemFont, "SF Pro Display", "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  --font-family-mono: "SF Mono", Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  
  /* Font Sizes - Reduced by 25-30% for desktop optimization */
  --font-size-xs: 0.625rem;  /* 10px - reduced from 12px */
  --font-size-sm: 0.75rem;   /* 12px - reduced from 14px */
  --font-size-base: 0.875rem; /* 14px - reduced from 16px */
  --font-size-lg: 1rem;       /* 16px - reduced from 18px */
  --font-size-xl: 1.125rem;   /* 18px - reduced from 20px */
  --font-size-2xl: 1.25rem;   /* 20px - reduced from 24px */
  --font-size-3xl: 1.5rem;    /* 24px - reduced from 30px */
  --font-size-4xl: 1.75rem;   /* 28px - reduced from 36px */
  --font-size-5xl: 2rem;      /* 32px - reduced from 48px */
  --font-size-6xl: 2.25rem;   /* 36px - reduced from 60px */
  
  /* Line Heights - Adjusted for new font sizes */
  --line-height-xs: 0.875rem;  /* 14px */
  --line-height-sm: 1rem;      /* 16px */
  --line-height-base: 1.25rem; /* 20px */
  --line-height-lg: 1.5rem;    /* 24px */
  --line-height-xl: 1.5rem;    /* 24px */
  --line-height-2xl: 1.75rem;  /* 28px */
  --line-height-3xl: 2rem;     /* 32px */
  --line-height-4xl: 2.25rem;  /* 36px */
  --line-height-5xl: 1.2;
  --line-height-6xl: 1.1;
  
  /* Font Weights */
  --font-weight-thin: 100;
  --font-weight-extralight: 200;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  --font-weight-black: 900;
  
  /* === SPACING (8px grid) === */
  --spacing-px: 1px;
  --spacing-0: 0;
  --spacing-0-5: 0.125rem; /* 2px */
  --spacing-1: 0.25rem; /* 4px */
  --spacing-1-5: 0.375rem; /* 6px */
  --spacing-2: 0.5rem; /* 8px - base unit */
  --spacing-2-5: 0.625rem; /* 10px */
  --spacing-3: 0.75rem; /* 12px */
  --spacing-3-5: 0.875rem; /* 14px */
  --spacing-4: 1rem; /* 16px */
  --spacing-5: 1.25rem; /* 20px */
  --spacing-6: 1.5rem; /* 24px */
  --spacing-7: 1.75rem; /* 28px */
  --spacing-8: 2rem; /* 32px */
  --spacing-9: 2.25rem; /* 36px */
  --spacing-10: 2.5rem; /* 40px */
  --spacing-11: 2.75rem; /* 44px - touch target */
  --spacing-12: 3rem; /* 48px */
  --spacing-14: 3.5rem; /* 56px */
  --spacing-16: 4rem; /* 64px */
  --spacing-20: 5rem; /* 80px */
  --spacing-24: 6rem; /* 96px */
  --spacing-28: 7rem; /* 112px */
  --spacing-32: 8rem; /* 128px */
  
  /* === STANDARDIZED COMPONENT SPACING - Reduced for desktop === */
  --spacing-component-padding: 1rem;    /* 16px - Reduced from 24px */
  --spacing-section-gap: 1.25rem;       /* 20px - Reduced from 32px */
  --spacing-element-gap: 0.75rem;       /* 12px - Reduced from 16px */
  --spacing-inline-gap: 0.5rem;         /* 8px - Inline elements */
  --spacing-compact-padding: 0.75rem;   /* 12px - For compact mode */
  --spacing-tight-padding: 0.5rem;      /* 8px - For very compact items */
  
  /* === BORDER RADIUS - Reduced for professional appearance === */
  --radius-none: 0;
  --radius-sm: 0.125rem;     /* 2px - subtle */
  --radius-default: 0.25rem;  /* 4px - default */
  --radius-md: 0.375rem;      /* 6px - medium */
  --radius-lg: 0.5rem;        /* 8px - cards default */
  --radius-xl: 0.75rem;       /* 12px - modals */
  --radius-2xl: 1rem;         /* 16px - large elements */
  --radius-3xl: 1.5rem;       /* 24px - hero elements */
  --radius-full: 9999px;      /* circular */
  
  /* === SHADOWS === */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-default: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  --shadow-none: none;
  --shadow-float: 0 8px 30px rgba(0, 0, 0, 0.12);
  --shadow-modal: 0 16px 70px rgba(0, 0, 0, 0.15);
  
  /* === TRANSITIONS === */
  --transition-fast: 150ms;
  --transition-default: 250ms;
  --transition-slow: 350ms;
  --transition-slower: 500ms;
  
  --easing-ease-in-out: cubic-bezier(0.4, 0.0, 0.2, 1);
  --easing-ease-out: cubic-bezier(0.0, 0.0, 0.2, 1);
  --easing-ease-in: cubic-bezier(0.4, 0.0, 1, 1);
  
  /* === Z-INDEX === */
  --z-auto: auto;
  --z-0: 0;
  --z-10: 10;
  --z-20: 20;
  --z-30: 30;
  --z-40: 40;
  --z-50: 50;
  --z-modal: 1000;
  --z-popover: 1010;
  --z-toast: 1020;
  --z-tooltip: 1030;
}

/* === DARK MODE OVERRIDES === */
@media (prefers-color-scheme: dark) {
  :root {
    /* Background overrides for dark mode */
    --color-background-primary: var(--color-neutral-900);
    --color-background-secondary: var(--color-neutral-800);
    --color-background-tertiary: var(--color-neutral-700);
    --color-background-inverse: var(--color-neutral-50);
    
    /* Text overrides for dark mode */
    --color-text-primary: var(--color-neutral-50);
    --color-text-secondary: var(--color-neutral-400);
    --color-text-tertiary: var(--color-neutral-600);
    --color-text-inverse: var(--color-neutral-900);
    --color-text-disabled: var(--color-neutral-700);
    
    /* Border overrides for dark mode */
    --color-border-default: var(--color-neutral-700);
    --color-border-strong: var(--color-neutral-600);
    --color-border-subtle: var(--color-neutral-800);
    
    /* Shadow overrides for dark mode (softer) */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow-default: 0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
    --shadow-float: 0 8px 30px rgba(0, 0, 0, 0.3);
    --shadow-modal: 0 16px 70px rgba(0, 0, 0, 0.4);
  }
}

/* Force dark mode class override */
.dark {
  /* Background overrides for dark mode */
  --color-background-primary: var(--color-neutral-900);
  --color-background-secondary: var(--color-neutral-800);
  --color-background-tertiary: var(--color-neutral-700);
  --color-background-inverse: var(--color-neutral-50);
  
  /* Text overrides for dark mode */
  --color-text-primary: var(--color-neutral-50);
  --color-text-secondary: var(--color-neutral-400);
  --color-text-tertiary: var(--color-neutral-600);
  --color-text-inverse: var(--color-neutral-900);
  --color-text-disabled: var(--color-neutral-700);
  
  /* Border overrides for dark mode */
  --color-border-default: var(--color-neutral-700);
  --color-border-strong: var(--color-neutral-600);
  --color-border-subtle: var(--color-neutral-800);
  
  /* Shadow overrides for dark mode */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-default: 0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
  --shadow-float: 0 8px 30px rgba(0, 0, 0, 0.3);
  --shadow-modal: 0 16px 70px rgba(0, 0, 0, 0.4);
}

/* === UTILITY CLASSES === */

/* Apple-style focus ring */
.focus-ring {
  outline: none;
  box-shadow: 0 0 0 2px rgb(var(--color-primary-500)), 0 0 0 4px rgb(var(--color-primary-100));
}

/* Glass morphism effects */
.glass {
  backdrop-filter: blur(20px) saturate(180%);
  background-color: rgba(255, 255, 255, 0.75);
  border: 1px solid rgba(209, 213, 219, 0.3);
}

.glass-dark {
  backdrop-filter: blur(20px) saturate(180%);
  background-color: rgba(17, 24, 39, 0.75);
  border: 1px solid rgba(75, 85, 99, 0.3);
}

/* Smooth scrolling */
.smooth-scroll {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* Hide scrollbar while maintaining functionality */
.hide-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

/* Touch-friendly tap targets - Desktop optimized with mobile fallback */
.tap-target {
  min-height: 2.25rem; /* 36px - desktop default */
  min-width: 2.25rem;  /* 36px - desktop default */
}

/* Mobile touch targets */
@media (max-width: 768px) {
  .tap-target {
    min-height: var(--spacing-11); /* 44px - mobile */
    min-width: var(--spacing-11);  /* 44px - mobile */
  }
}

/* Shimmer loading effect */
.shimmer {
  background: rgb(var(--color-neutral-200));
  background-image: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  background-size: 200% 100%;
  background-repeat: no-repeat;
  animation: shimmer 2s linear infinite;
}

.shimmer-dark {
  background: rgb(var(--color-neutral-700));
  background-image: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
  background-size: 200% 100%;
  background-repeat: no-repeat;
  animation: shimmer 2s linear infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* === COMPONENT BASE STYLES === */

/* Button base styles - Desktop optimized */
.btn-base {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-md);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-sm);
  padding: var(--spacing-1-5) var(--spacing-3); /* Reduced padding */
  transition: all var(--transition-default) var(--easing-ease-in-out);
  cursor: pointer;
  user-select: none;
  min-height: 2rem; /* 32px - desktop optimized */
}

/* Mobile button sizing */
@media (max-width: 768px) {
  .btn-base {
    min-height: var(--spacing-11); /* 44px touch target */
    padding: var(--spacing-2) var(--spacing-4);
  }
}

.btn-base:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

/* Card base styles - Compact design */
.card-base {
  border-radius: var(--radius-lg);  /* 8px */
  border: 1px solid rgb(var(--color-border-default));
  background-color: rgb(var(--color-background-secondary));
  box-shadow: var(--shadow-sm);
  padding: var(--spacing-component-padding); /* 16px instead of 24px */
}

/* Input base styles - Compact design */
.input-base {
  width: 100%;
  border-radius: var(--radius-md);
  border: 1px solid rgb(var(--color-border-default));
  background-color: rgb(var(--color-background-primary));
  color: rgb(var(--color-text-primary));
  padding: var(--spacing-1-5) var(--spacing-2-5); /* Reduced padding */
  font-size: var(--font-size-sm);
  line-height: var(--line-height-sm);
  transition: all var(--transition-fast) var(--easing-ease-in-out);
  min-height: 2rem; /* 32px - desktop optimized */
}

/* Mobile input sizing */
@media (max-width: 768px) {
  .input-base {
    min-height: 2.5rem; /* 40px for mobile */
    padding: var(--spacing-2) var(--spacing-3);
  }
}

.input-base:focus {
  outline: none;
  border-color: rgb(var(--color-interactive-focus));
  box-shadow: 0 0 0 2px rgba(var(--color-primary-100), 0.5);
}

.input-base:disabled {
  cursor: not-allowed;
  opacity: 0.5;
  background-color: rgb(var(--color-neutral-100));
}

/* === STANDARDIZED COMPONENT CLASSES (2025 Standards) === */

/* Standard card styling - Compact professional design */
.standard-card {
  padding: var(--spacing-component-padding); /* 16px - reduced from 24px */
  border-radius: var(--radius-lg); /* 8px */
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-default) var(--easing-ease-in-out);
  background: rgb(var(--color-background-secondary));
  border: 1px solid rgb(var(--color-border-default));
}

/* Compact card variant */
.standard-card--compact {
  padding: var(--spacing-compact-padding); /* 12px */
}

/* Dense card variant */
.standard-card--dense {
  padding: var(--spacing-tight-padding); /* 8px */
}

.standard-card:hover {
  box-shadow: var(--shadow-md);
  border-color: rgb(var(--color-border-strong));
}

/* Metric cards - Compact metrics display */
.metric-card {
  padding: var(--spacing-compact-padding); /* 12px for metrics */
  border-radius: var(--radius-lg); /* 8px */
  transition: all var(--transition-default) var(--easing-ease-in-out);
}

.metric-card--primary {
  background: linear-gradient(135deg, rgba(var(--color-primary-50), 0.5) 0%, transparent 100%);
  border: 1px solid rgba(var(--color-primary-200), 0.3);
}

.metric-card--success {
  background: linear-gradient(135deg, rgba(var(--color-success-50), 0.5) 0%, transparent 100%);
  border: 1px solid rgba(var(--color-success-200), 0.3);
}

.metric-card--warning {
  background: linear-gradient(135deg, rgba(var(--color-warning-50), 0.5) 0%, transparent 100%);
  border: 1px solid rgba(var(--color-warning-200), 0.3);
}

/* Bento grid system - Tighter spacing */
.bento-grid {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  gap: var(--spacing-section-gap); /* 20px - reduced from 32px */
}

/* Dense grid variant */
.bento-grid--dense {
  gap: var(--spacing-element-gap); /* 12px for tighter layouts */
}

.bento-item-full { grid-column: span 12; }
.bento-item-half { grid-column: span 6; }
.bento-item-third { grid-column: span 4; }
.bento-item-two-thirds { grid-column: span 8; }
.bento-item-quarter { grid-column: span 3; }

/* Responsive bento grid */
@media (max-width: 768px) {
  .bento-item-half,
  .bento-item-third,
  .bento-item-two-thirds,
  .bento-item-quarter {
    grid-column: span 12;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .bento-item-third,
  .bento-item-quarter {
    grid-column: span 6;
  }
}

/* Chart color classes */
.chart-color-primary { color: rgb(var(--color-primary-500)); }
.chart-color-success { color: rgb(var(--color-success-500)); }
.chart-color-warning { color: rgb(var(--color-warning-500)); }
.chart-color-danger { color: rgb(var(--color-error-500)); }
.chart-color-neutral { color: rgb(var(--color-neutral-500)); }

/* Loading state standardization */
.loading-shimmer {
  background: linear-gradient(90deg, 
    rgb(var(--color-neutral-200)) 0%,
    rgb(var(--color-neutral-100)) 50%,
    rgb(var(--color-neutral-200)) 100%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

/* Data display standards */
.data-positive { color: rgb(var(--color-positive)); }
.data-negative { color: rgb(var(--color-negative)); }
.data-neutral { color: rgb(var(--color-neutral)); }

/* Animation standards */
.transition-standard {
  transition: all var(--transition-default) var(--easing-ease-in-out);
}

.transition-fast {
  transition: all var(--transition-fast) var(--easing-ease-in-out);
}

.transition-slow {
  transition: all var(--transition-slow) var(--easing-ease-in-out);
}