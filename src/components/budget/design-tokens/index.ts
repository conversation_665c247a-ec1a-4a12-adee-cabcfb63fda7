/**
 * Design Tokens - Apple-inspired design system
 * Following Apple's Human Interface Guidelines and design principles
 */

// Apple Color System - Semantic and accessible
export const colors = {
  // Primary System Colors (Apple-style)
  primary: {
    50: '#eff6ff',   // lightest blue
    100: '#dbeafe',  // very light blue
    200: '#bfdbfe',  // light blue
    300: '#93c5fd',  // medium light blue
    400: '#60a5fa',  // medium blue
    500: '#007AFF',  // Apple blue (primary)
    600: '#0056CC',  // darker blue
    700: '#003d9a',  // dark blue
    800: '#1e40af',  // very dark blue
    900: '#1e3a8a',  // darkest blue
  },
  
  // Success Colors
  success: {
    50: '#f0fdf4',
    100: '#dcfce7',
    200: '#bbf7d0',
    300: '#86efac',
    400: '#4ade80',
    500: '#34C759',  // Apple green
    600: '#16a34a',
    700: '#15803d',
    800: '#166534',
    900: '#14532d',
  },
  
  // Warning Colors
  warning: {
    50: '#fffbeb',
    100: '#fef3c7',
    200: '#fed7aa',
    300: '#fdba74',
    400: '#fb923c',
    500: '#FF9500',  // Apple orange
    600: '#ea580c',
    700: '#c2410c',
    800: '#9a3412',
    900: '#7c2d12',
  },
  
  // Error/Destructive Colors
  error: {
    50: '#fef2f2',
    100: '#fee2e2',
    200: '#fecaca',
    300: '#fca5a5',
    400: '#f87171',
    500: '#FF3B30',  // Apple red
    600: '#dc2626',
    700: '#b91c1c',
    800: '#991b1b',
    900: '#7f1d1d',
  },
  
  // Neutral/Gray Scale (Apple-inspired)
  neutral: {
    50: '#f9fafb',   // lightest gray
    100: '#f3f4f6',  // very light gray
    200: '#e5e7eb',  // light gray
    300: '#d1d5db',  // medium light gray
    400: '#9ca3af',  // medium gray
    500: '#6b7280',  // medium dark gray
    600: '#4b5563',  // dark gray
    700: '#374151',  // very dark gray
    800: '#1f2937',  // darkest gray
    900: '#111827',  // almost black
  },
  
  // Purple (Apple accent)
  purple: {
    50: '#faf5ff',
    100: '#f3e8ff',
    200: '#e9d5ff',
    300: '#d8b4fe',
    400: '#c084fc',
    500: '#5856D6',  // Apple purple
    600: '#7c3aed',
    700: '#6d28d9',
    800: '#5b21b6',
    900: '#4c1d95',
  },
  
  // Pink (Apple accent)
  pink: {
    50: '#fdf2f8',
    100: '#fce7f3',
    200: '#fbcfe8',
    300: '#f9a8d4',
    400: '#f472b6',
    500: '#FF2D92',  // Apple pink
    600: '#db2777',
    700: '#be185d',
    800: '#9d174d',
    900: '#831843',
  },
};

// Typography System - Apple-inspired scale
export const typography = {
  fontFamily: {
    sans: [
      '-apple-system',
      'BlinkMacSystemFont', 
      'SF Pro Display',
      'Segoe UI',
      'Roboto',
      'Helvetica Neue',
      'Arial',
      'sans-serif'
    ],
    mono: [
      'SF Mono',
      'Monaco',
      'Consolas',
      'Liberation Mono',
      'Courier New',
      'monospace'
    ],
  },
  fontSize: {
    xs: ['0.625rem', { lineHeight: '0.875rem' }],   // 10px - reduced from 12px
    sm: ['0.75rem', { lineHeight: '1rem' }],        // 12px - reduced from 14px
    base: ['0.875rem', { lineHeight: '1.25rem' }],  // 14px - reduced from 16px (new base)
    lg: ['1rem', { lineHeight: '1.5rem' }],         // 16px - reduced from 18px
    xl: ['1.125rem', { lineHeight: '1.5rem' }],     // 18px - reduced from 20px
    '2xl': ['1.25rem', { lineHeight: '1.75rem' }],  // 20px - reduced from 24px
    '3xl': ['1.5rem', { lineHeight: '2rem' }],      // 24px - reduced from 30px
    '4xl': ['1.75rem', { lineHeight: '2.25rem' }],  // 28px - reduced from 36px
    '5xl': ['2rem', { lineHeight: '1.2' }],         // 32px - reduced from 48px
    '6xl': ['2.25rem', { lineHeight: '1.1' }],      // 36px - reduced from 60px
  },
  fontWeight: {
    thin: '100',
    extralight: '200',
    light: '300',
    normal: '400',     // regular text
    medium: '500',     // emphasis
    semibold: '600',   // headings
    bold: '700',       // strong emphasis
    extrabold: '800',
    black: '900',
  },
  letterSpacing: {
    tighter: '-0.05em',
    tight: '-0.025em',
    normal: '0em',
    wide: '0.025em',
    wider: '0.05em',
    widest: '0.1em',
  },
};

// Spacing System - 8px grid system (Desktop optimized)
export const spacing = {
  px: '1px',
  0: '0',
  0.5: '0.125rem',  // 2px
  1: '0.25rem',     // 4px
  1.5: '0.375rem',  // 6px
  2: '0.5rem',      // 8px  - base unit
  2.5: '0.625rem',  // 10px
  3: '0.75rem',     // 12px - compact spacing
  3.5: '0.875rem',  // 14px
  4: '1rem',        // 16px - standard component padding
  5: '1.25rem',     // 20px - section spacing
  6: '1.5rem',      // 24px - large spacing
  7: '1.75rem',     // 28px
  8: '2rem',        // 32px - desktop touch target
  9: '2.25rem',     // 36px - comfortable touch target
  10: '2.5rem',     // 40px
  11: '2.75rem',    // 44px - mobile touch target
  12: '3rem',       // 48px
  14: '3.5rem',     // 56px
  16: '4rem',       // 64px - extra large spacing
  20: '5rem',       // 80px
  24: '6rem',       // 96px
  28: '7rem',       // 112px
  32: '8rem',       // 128px
};

// Border Radius - Professional, subtle corners
export const borderRadius = {
  none: '0',
  sm: '0.125rem',      // 2px - very subtle
  DEFAULT: '0.25rem',  // 4px - default subtle rounding
  md: '0.375rem',      // 6px - medium rounding
  lg: '0.5rem',        // 8px - card default
  xl: '0.75rem',       // 12px - modal/dialog
  '2xl': '1rem',       // 16px - large elements
  '3xl': '1.5rem',     // 24px - hero elements
  full: '9999px',      // circular
};

// Shadows - Apple-style depth
export const shadows = {
  sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  DEFAULT: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
  md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
  '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
  inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
  none: 'none',
  // Apple-specific shadows
  float: '0 8px 30px rgba(0, 0, 0, 0.12)', // floating elements
  modal: '0 16px 70px rgba(0, 0, 0, 0.15)', // modals/dialogs
};

// Animation & Transitions - Apple-style timing
export const transitions = {
  // Easing functions (Apple's preferred curves)
  easeInOut: 'cubic-bezier(0.4, 0.0, 0.2, 1)',
  easeOut: 'cubic-bezier(0.0, 0.0, 0.2, 1)',
  easeIn: 'cubic-bezier(0.4, 0.0, 1, 1)',
  
  // Duration values
  duration: {
    fast: '150ms',      // micro-interactions
    DEFAULT: '250ms',   // standard transitions
    slow: '350ms',      // page transitions
    slower: '500ms',    // complex animations
  },
  
  // Common transition combinations
  all: 'all 250ms cubic-bezier(0.4, 0.0, 0.2, 1)',
  colors: 'color 250ms cubic-bezier(0.4, 0.0, 0.2, 1), background-color 250ms cubic-bezier(0.4, 0.0, 0.2, 1), border-color 250ms cubic-bezier(0.4, 0.0, 0.2, 1)',
  opacity: 'opacity 250ms cubic-bezier(0.4, 0.0, 0.2, 1)',
  transform: 'transform 250ms cubic-bezier(0.4, 0.0, 0.2, 1)',
};

// Z-Index Scale - Organized layering
export const zIndex = {
  auto: 'auto',
  0: '0',
  10: '10',      // dropdowns, tooltips
  20: '20',      // sticky elements
  30: '30',      // modals, dialogs
  40: '40',      // notifications, alerts
  50: '50',      // overlays, loading screens
  modal: '1000', // modal backdrop
  popover: '1010', // popovers, tooltips
  toast: '1020',   // toast notifications
  tooltip: '1030', // tooltips
};

// Semantic Color Mappings - For consistent usage
export const semanticColors = {
  // Backgrounds
  background: {
    primary: colors.neutral[50],      // main background
    secondary: colors.neutral[100],   // card background
    tertiary: colors.neutral[200],    // subtle background
    inverse: colors.neutral[900],     // dark theme primary
  },
  
  // Text
  text: {
    primary: colors.neutral[900],     // main text
    secondary: colors.neutral[600],   // secondary text
    tertiary: colors.neutral[400],    // placeholder text
    inverse: colors.neutral[50],      // text on dark background
    disabled: colors.neutral[300],    // disabled state
  },
  
  // Borders
  border: {
    DEFAULT: colors.neutral[200],     // standard borders
    strong: colors.neutral[300],      // emphasized borders
    subtle: colors.neutral[100],      // subtle borders
  },
  
  // Interactive states
  interactive: {
    DEFAULT: colors.primary[500],     // default interactive
    hover: colors.primary[600],       // hover state
    active: colors.primary[700],      // active/pressed state
    disabled: colors.neutral[300],    // disabled state
    focus: colors.primary[500],       // focus ring
  },
  
  // Status colors
  status: {
    success: colors.success[500],
    warning: colors.warning[500],
    error: colors.error[500],
    info: colors.primary[500],
  },
};

// Component Variants - Consistent component styling
export const componentVariants = {
  // Button variants - Compact sizing
  button: {
    primary: {
      background: semanticColors.interactive.DEFAULT,
      color: colors.neutral[50],
      hover: semanticColors.interactive.hover,
      active: semanticColors.interactive.active,
      padding: '0.375rem 0.75rem', // 6px 12px - compact
      height: '2rem', // 32px default
    },
    secondary: {
      background: colors.neutral[100],
      color: colors.neutral[900],
      border: colors.neutral[200],
      hover: colors.neutral[200],
      active: colors.neutral[300],
      padding: '0.375rem 0.75rem',
      height: '2rem',
    },
    ghost: {
      background: 'transparent',
      color: colors.neutral[600],
      hover: colors.neutral[100],
      active: colors.neutral[200],
      padding: '0.375rem 0.75rem',
      height: '2rem',
    },
    destructive: {
      background: colors.error[500],
      color: colors.neutral[50],
      hover: colors.error[600],
      active: colors.error[700],
      padding: '0.375rem 0.75rem',
      height: '2rem',
    },
  },
  
  // Card variants - Compact design
  card: {
    DEFAULT: {
      background: semanticColors.background.secondary,
      border: semanticColors.border.DEFAULT,
      shadow: shadows.sm,
      borderRadius: borderRadius.lg, // 8px
      padding: spacing[4], // 16px default
    },
    elevated: {
      background: semanticColors.background.primary,
      border: 'transparent',
      shadow: shadows.lg,
      borderRadius: borderRadius.lg,
      padding: spacing[4],
    },
    outlined: {
      background: 'transparent',
      border: semanticColors.border.strong,
      shadow: shadows.none,
      borderRadius: borderRadius.lg,
      padding: spacing[4],
    },
    compact: {
      background: semanticColors.background.secondary,
      border: semanticColors.border.DEFAULT,
      shadow: shadows.sm,
      borderRadius: borderRadius.md, // 6px
      padding: spacing[3], // 12px
    },
  },
  
  // Input variants - Compact sizing
  input: {
    DEFAULT: {
      background: semanticColors.background.primary,
      border: semanticColors.border.DEFAULT,
      borderRadius: borderRadius.md, // 6px
      padding: `${spacing[1.5]} ${spacing[2.5]}`, // 6px 10px - reduced
      height: '2rem', // 32px default
      fontSize: typography.fontSize.sm[0], // 12px
      focus: {
        border: semanticColors.interactive.focus,
        shadow: `0 0 0 2px ${colors.primary[100]}`,
      },
      error: {
        border: colors.error[300],
        shadow: `0 0 0 2px ${colors.error[100]}`,
      },
    },
    compact: {
      padding: `${spacing[1]} ${spacing[2]}`, // 4px 8px
      height: '1.75rem', // 28px
    },
  },
};

// Breakpoints - Responsive design
export const breakpoints = {
  sm: '640px',   // mobile landscape
  md: '768px',   // tablet
  lg: '1024px',  // desktop
  xl: '1280px',  // large desktop
  '2xl': '1536px', // extra large desktop
};

// Export everything as a cohesive design system
export const designTokens = {
  colors,
  typography,
  spacing,
  borderRadius,
  shadows,
  transitions,
  zIndex,
  semanticColors,
  componentVariants,
  breakpoints,
};

export default designTokens;