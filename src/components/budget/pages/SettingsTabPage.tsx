import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, TabsContent } from '@/components/ui/tabs';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { 
  Settings, 
  Palette, 
  Bell, 
  Database, 
  Shield, 
  Zap,
  Save,
  RotateCcw,
  Upload,
  Key,
  Webhook,
  Mail,
  Users
} from 'lucide-react';
import { useTheme } from '@/contexts/ThemeContext';
import { Toast, ToastContainer } from '@/components/ui/toast';
import { CSVImport } from '../components/integration/CSVImport';
import { APIEndpoints } from '../components/integration/APIEndpoints';
import { WebhookManager } from '../components/integration/WebhookManager';
import { TrainingProviderIntegration } from '../components/integration/TrainingProviderIntegration';
import { EmailNotifications } from '../components/reporting/EmailNotifications';

interface SettingsTabPageProps {
  onSave?: () => void;
}

export const SettingsTabPage: React.FC<SettingsTabPageProps> = ({ onSave }) => {
  const { theme, setTheme, customColors, setCustomColors } = useTheme();
  const [activeTab, setActiveTab] = useState('appearance');
  const [toast, setToast] = useState<{ message: string; type: 'success' | 'error' } | null>(null);
  const [apiKeys, setApiKeys] = useState<any[]>([]);
  const [webhooks, setWebhooks] = useState<any[]>([]);
  const [providers, setProviders] = useState<any[]>([]);
  const [emailSubscriptions, setEmailSubscriptions] = useState<any[]>([]);
  
  // Local settings state
  const [notifications, setNotifications] = useState({
    emailNotifications: true,
    pushNotifications: false,
    budgetAlerts: true,
    expenseReminders: true,
  });
  
  const [dataSettings, setDataSettings] = useState({
    autoBackup: true,
    backupFrequency: 'daily',
    dataRetention: 30,
  });
  
  const [performanceSettings, setPerformanceSettings] = useState({
    animations: true,
    lazyLoading: true,
    cacheEnabled: true,
  });

  const handleSave = () => {
    // Save settings logic here
    setToast({ message: 'Settings saved successfully!', type: 'success' });
    if (onSave) onSave();
  };

  const handleReset = () => {
    // Reset to defaults
    setTheme('dark');
    setToast({ message: 'Settings reset to defaults', type: 'success' });
  };

  return (
    <div className="space-y-6">
      <ToastContainer>
        {toast && (
          <Toast
            message={toast.message}
            type={toast.type}
            onDismiss={() => setToast(null)}
          />
        )}
      </ToastContainer>

      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-3 bg-secondary rounded-xl">
            <Settings className="h-6 w-6 text-secondary-foreground" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-card-foreground">Settings</h2>
            <p className="text-sm text-muted-foreground">
              Configure your training budget preferences
            </p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleReset}
            className="gap-2"
          >
            <RotateCcw className="h-4 w-4" />
            Reset
          </Button>
          <Button
            size="sm"
            onClick={handleSave}
            className="gap-2 bg-primary hover:bg-primary/90"
          >
            <Save className="h-4 w-4" />
            Save Changes
          </Button>
        </div>
      </div>

      {/* Settings Content */}
      <Card className="p-6">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-4 md:grid-cols-8 w-full">
            <TabsTrigger value="appearance" className="gap-1 text-xs">
              <Palette className="h-4 w-4" />
              <span className="hidden md:inline">Appearance</span>
            </TabsTrigger>
            <TabsTrigger value="notifications" className="gap-1 text-xs">
              <Bell className="h-4 w-4" />
              <span className="hidden md:inline">Notifications</span>
            </TabsTrigger>
            <TabsTrigger value="data" className="gap-1 text-xs">
              <Database className="h-4 w-4" />
              <span className="hidden md:inline">Data</span>
            </TabsTrigger>
            <TabsTrigger value="import" className="gap-1 text-xs">
              <Upload className="h-4 w-4" />
              <span className="hidden md:inline">Import</span>
            </TabsTrigger>
            <TabsTrigger value="api" className="gap-1 text-xs">
              <Key className="h-4 w-4" />
              <span className="hidden md:inline">API</span>
            </TabsTrigger>
            <TabsTrigger value="webhooks" className="gap-1 text-xs">
              <Webhook className="h-4 w-4" />
              <span className="hidden md:inline">Webhooks</span>
            </TabsTrigger>
            <TabsTrigger value="providers" className="gap-1 text-xs">
              <Users className="h-4 w-4" />
              <span className="hidden md:inline">Providers</span>
            </TabsTrigger>
            <TabsTrigger value="email" className="gap-1 text-xs">
              <Mail className="h-4 w-4" />
              <span className="hidden md:inline">Email</span>
            </TabsTrigger>
          </TabsList>

          {/* Appearance Tab */}
          <TabsContent value="appearance" className="space-y-6 mt-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Appearance Settings</h3>
              
              {/* Theme Selector */}
              <div className="space-y-2">
                <Label htmlFor="theme">Theme</Label>
                <Select
                  value={theme}
                  onValueChange={(value) => setTheme(value as any)}
                >
                  <SelectTrigger id="theme" className="w-full max-w-xs">
                    <SelectValue placeholder="Select a theme" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="dark">Dark</SelectItem>
                    <SelectItem value="light">Light</SelectItem>
                    <SelectItem value="gray">Gray</SelectItem>
                    <SelectItem value="custom">Custom</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground">
                  Choose your preferred color theme for the interface
                </p>
              </div>

              {/* Custom Theme Colors */}
              {theme === 'custom' && (
                <div className="space-y-4 p-4 border rounded-lg bg-muted/20">
                  <h4 className="text-sm font-medium">Custom Theme Colors</h4>
                  
                  <div className="grid grid-cols-2 gap-4">
                    {/* Background Color */}
                    <div className="space-y-2">
                      <Label htmlFor="color-background" className="text-xs">Background</Label>
                      <div className="flex gap-2">
                        <Input
                          id="color-background"
                          type="text"
                          value={customColors.background}
                          onChange={(e) => setCustomColors({ background: e.target.value })}
                          placeholder="oklch(0.12 0.01 240)"
                          className="font-mono text-xs"
                        />
                        <div 
                          className="w-10 h-10 rounded border"
                          style={{ backgroundColor: customColors.background }}
                        />
                      </div>
                    </div>
                    
                    {/* Foreground Color */}
                    <div className="space-y-2">
                      <Label htmlFor="color-foreground" className="text-xs">Foreground</Label>
                      <div className="flex gap-2">
                        <Input
                          id="color-foreground"
                          type="text"
                          value={customColors.foreground}
                          onChange={(e) => setCustomColors({ foreground: e.target.value })}
                          placeholder="oklch(0.98 0.01 240)"
                          className="font-mono text-xs"
                        />
                        <div 
                          className="w-10 h-10 rounded border"
                          style={{ backgroundColor: customColors.foreground }}
                        />
                      </div>
                    </div>
                    
                    {/* Primary Color */}
                    <div className="space-y-2">
                      <Label htmlFor="color-primary" className="text-xs">Primary</Label>
                      <div className="flex gap-2">
                        <Input
                          id="color-primary"
                          type="text"
                          value={customColors.primary}
                          onChange={(e) => setCustomColors({ primary: e.target.value })}
                          placeholder="oklch(0.98 0.01 240)"
                          className="font-mono text-xs"
                        />
                        <div 
                          className="w-10 h-10 rounded border"
                          style={{ backgroundColor: customColors.primary }}
                        />
                      </div>
                    </div>
                    
                    {/* Card Color */}
                    <div className="space-y-2">
                      <Label htmlFor="color-card" className="text-xs">Card</Label>
                      <div className="flex gap-2">
                        <Input
                          id="color-card"
                          type="text"
                          value={customColors.card}
                          onChange={(e) => setCustomColors({ card: e.target.value })}
                          placeholder="oklch(0.14 0.01 240)"
                          className="font-mono text-xs"
                        />
                        <div 
                          className="w-10 h-10 rounded border"
                          style={{ backgroundColor: customColors.card }}
                        />
                      </div>
                    </div>
                  </div>
                  
                  <p className="text-xs text-muted-foreground">
                    Use CSS color values (hex, rgb, oklch, etc.). Changes apply immediately.
                  </p>
                </div>
              )}

              {/* Additional Appearance Settings */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="compact-mode">Compact Mode</Label>
                    <p className="text-xs text-muted-foreground">
                      Reduce spacing between elements
                    </p>
                  </div>
                  <Switch id="compact-mode" />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="show-icons">Show Icons</Label>
                    <p className="text-xs text-muted-foreground">
                      Display icons in navigation and cards
                    </p>
                  </div>
                  <Switch id="show-icons" defaultChecked />
                </div>
              </div>
            </div>
          </TabsContent>

          {/* Notifications Tab */}
          <TabsContent value="notifications" className="space-y-6 mt-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Notification Preferences</h3>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="email-notifications">Email Notifications</Label>
                    <p className="text-xs text-muted-foreground">
                      Receive budget updates via email
                    </p>
                  </div>
                  <Switch 
                    id="email-notifications"
                    checked={notifications.emailNotifications}
                    onCheckedChange={(checked) => 
                      setNotifications(prev => ({ ...prev, emailNotifications: checked }))
                    }
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="push-notifications">Push Notifications</Label>
                    <p className="text-xs text-muted-foreground">
                      Browser push notifications for important updates
                    </p>
                  </div>
                  <Switch 
                    id="push-notifications"
                    checked={notifications.pushNotifications}
                    onCheckedChange={(checked) => 
                      setNotifications(prev => ({ ...prev, pushNotifications: checked }))
                    }
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="budget-alerts">Budget Alerts</Label>
                    <p className="text-xs text-muted-foreground">
                      Alert when budget thresholds are reached
                    </p>
                  </div>
                  <Switch 
                    id="budget-alerts"
                    checked={notifications.budgetAlerts}
                    onCheckedChange={(checked) => 
                      setNotifications(prev => ({ ...prev, budgetAlerts: checked }))
                    }
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="expense-reminders">Expense Reminders</Label>
                    <p className="text-xs text-muted-foreground">
                      Remind to submit pending expenses
                    </p>
                  </div>
                  <Switch 
                    id="expense-reminders"
                    checked={notifications.expenseReminders}
                    onCheckedChange={(checked) => 
                      setNotifications(prev => ({ ...prev, expenseReminders: checked }))
                    }
                  />
                </div>
              </div>
            </div>
          </TabsContent>

          {/* Data Tab */}
          <TabsContent value="data" className="space-y-6 mt-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Data & Storage</h3>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="auto-backup">Automatic Backup</Label>
                    <p className="text-xs text-muted-foreground">
                      Automatically backup your budget data
                    </p>
                  </div>
                  <Switch 
                    id="auto-backup"
                    checked={dataSettings.autoBackup}
                    onCheckedChange={(checked) => 
                      setDataSettings(prev => ({ ...prev, autoBackup: checked }))
                    }
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="backup-frequency">Backup Frequency</Label>
                  <Select
                    value={dataSettings.backupFrequency}
                    onValueChange={(value) => 
                      setDataSettings(prev => ({ ...prev, backupFrequency: value }))
                    }
                  >
                    <SelectTrigger id="backup-frequency" className="w-full max-w-xs">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="hourly">Hourly</SelectItem>
                      <SelectItem value="daily">Daily</SelectItem>
                      <SelectItem value="weekly">Weekly</SelectItem>
                      <SelectItem value="monthly">Monthly</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="data-retention">Data Retention (days)</Label>
                  <Input
                    id="data-retention"
                    type="number"
                    min="7"
                    max="365"
                    value={dataSettings.dataRetention}
                    onChange={(e) => 
                      setDataSettings(prev => ({ ...prev, dataRetention: parseInt(e.target.value) || 30 }))
                    }
                    className="w-full max-w-xs"
                  />
                  <p className="text-xs text-muted-foreground">
                    How long to keep historical data
                  </p>
                </div>
                
                <div className="pt-4 space-y-2">
                  <Button variant="outline" className="w-full max-w-xs">
                    Export Data
                  </Button>
                  <Button variant="outline" className="w-full max-w-xs">
                    Import Data
                  </Button>
                </div>
              </div>
            </div>
          </TabsContent>

          {/* Privacy Tab */}
          <TabsContent value="privacy" className="space-y-6 mt-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Privacy & Security</h3>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="analytics">Analytics</Label>
                    <p className="text-xs text-muted-foreground">
                      Help improve the app with anonymous usage data
                    </p>
                  </div>
                  <Switch id="analytics" />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="two-factor">Two-Factor Authentication</Label>
                    <p className="text-xs text-muted-foreground">
                      Add an extra layer of security
                    </p>
                  </div>
                  <Switch id="two-factor" />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="data-encryption">Data Encryption</Label>
                    <p className="text-xs text-muted-foreground">
                      Encrypt sensitive budget data
                    </p>
                  </div>
                  <Switch id="data-encryption" defaultChecked />
                </div>
                
                <div className="pt-4">
                  <Button variant="destructive" className="w-full max-w-xs">
                    Delete All Data
                  </Button>
                  <p className="text-xs text-muted-foreground mt-2">
                    This action cannot be undone
                  </p>
                </div>
              </div>
            </div>
          </TabsContent>

          {/* Performance Tab */}
          <TabsContent value="performance" className="space-y-6 mt-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Performance Settings</h3>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="animations">Animations</Label>
                    <p className="text-xs text-muted-foreground">
                      Enable smooth transitions and animations
                    </p>
                  </div>
                  <Switch 
                    id="animations"
                    checked={performanceSettings.animations}
                    onCheckedChange={(checked) => 
                      setPerformanceSettings(prev => ({ ...prev, animations: checked }))
                    }
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="lazy-loading">Lazy Loading</Label>
                    <p className="text-xs text-muted-foreground">
                      Load content as needed for better performance
                    </p>
                  </div>
                  <Switch 
                    id="lazy-loading"
                    checked={performanceSettings.lazyLoading}
                    onCheckedChange={(checked) => 
                      setPerformanceSettings(prev => ({ ...prev, lazyLoading: checked }))
                    }
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="cache">Enable Cache</Label>
                    <p className="text-xs text-muted-foreground">
                      Cache data for faster loading
                    </p>
                  </div>
                  <Switch 
                    id="cache"
                    checked={performanceSettings.cacheEnabled}
                    onCheckedChange={(checked) => 
                      setPerformanceSettings(prev => ({ ...prev, cacheEnabled: checked }))
                    }
                  />
                </div>
                
                <div className="pt-4">
                  <Button variant="outline" className="w-full max-w-xs">
                    Clear Cache
                  </Button>
                  <p className="text-xs text-muted-foreground mt-2">
                    Clear cached data to free up space
                  </p>
                </div>
              </div>
            </div>
          </TabsContent>

          {/* Import Tab */}
          <TabsContent value="import" className="space-y-6 mt-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Import Data</h3>
              <p className="text-sm text-muted-foreground">
                Import expenses and budget data from CSV files
              </p>
              <CSVImport
                onImportExpenses={(expenses) => {
                  console.log('Imported expenses:', expenses);
                  setToast({ message: `Imported ${expenses.length} expenses`, type: 'success' });
                }}
                onImportBudget={(budget) => {
                  console.log('Imported budget:', budget);
                  setToast({ message: 'Budget imported successfully', type: 'success' });
                }}
                showToast={(message, type) => setToast({ message, type })}
              />
            </div>
          </TabsContent>

          {/* API Tab */}
          <TabsContent value="api" className="space-y-6 mt-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">API Configuration</h3>
              <p className="text-sm text-muted-foreground">
                Manage API keys and endpoints for external integrations
              </p>
              <APIEndpoints
                apiKeys={apiKeys}
                onGenerateKey={async () => {
                  const newKey = {
                    id: Date.now().toString(),
                    name: `API Key ${apiKeys.length + 1}`,
                    key: `sk-${Math.random().toString(36).substring(2)}`,
                    permissions: ['read', 'write'],
                    createdAt: new Date().toISOString(),
                    active: true
                  };
                  setApiKeys([...apiKeys, newKey]);
                  return newKey;
                }}
                onDeleteKey={(id) => {
                  setApiKeys(apiKeys.filter(k => k.id !== id));
                }}
                showToast={(message, type) => setToast({ message, type })}
              />
            </div>
          </TabsContent>

          {/* Webhooks Tab */}
          <TabsContent value="webhooks" className="space-y-6 mt-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Webhook Management</h3>
              <p className="text-sm text-muted-foreground">
                Configure webhooks to receive real-time budget events
              </p>
              <WebhookManager
                webhooks={webhooks}
                onCreateWebhook={(webhook) => {
                  setWebhooks([...webhooks, { ...webhook, id: Date.now().toString() }]);
                }}
                onUpdateWebhook={(id, updates) => {
                  setWebhooks(webhooks.map(w => w.id === id ? { ...w, ...updates } : w));
                }}
                onDeleteWebhook={(id) => {
                  setWebhooks(webhooks.filter(w => w.id !== id));
                }}
                onTestWebhook={async (id) => {
                  setToast({ message: 'Webhook test sent', type: 'success' });
                }}
                showToast={(message, type) => setToast({ message, type })}
              />
            </div>
          </TabsContent>

          {/* Providers Tab */}
          <TabsContent value="providers" className="space-y-6 mt-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Training Providers</h3>
              <p className="text-sm text-muted-foreground">
                Connect and sync with external training providers
              </p>
              <TrainingProviderIntegration
                providers={providers}
                onAddProvider={(provider) => {
                  setProviders([...providers, { ...provider, id: Date.now().toString() }]);
                }}
                onUpdateProvider={(id, updates) => {
                  setProviders(providers.map(p => p.id === id ? { ...p, ...updates } : p));
                }}
                onDeleteProvider={(id) => {
                  setProviders(providers.filter(p => p.id !== id));
                }}
                onSyncProvider={async (id) => {
                  setToast({ message: 'Provider sync started', type: 'success' });
                  return { status: 'success', coursesFound: 0, coursesImported: 0 };
                }}
                onImportCourse={(course) => {
                  console.log('Importing course:', course);
                  setToast({ message: 'Course imported successfully', type: 'success' });
                }}
                showToast={(message, type) => setToast({ message, type })}
              />
            </div>
          </TabsContent>

          {/* Email Tab */}
          <TabsContent value="email" className="space-y-6 mt-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Email Notifications</h3>
              <p className="text-sm text-muted-foreground">
                Configure email alerts and notification preferences
              </p>
              <EmailNotifications
                subscriptions={emailSubscriptions}
                onCreateSubscription={(subscription) => {
                  setEmailSubscriptions([...emailSubscriptions, { ...subscription, id: Date.now().toString() }]);
                }}
                onUpdateSubscription={(id, updates) => {
                  setEmailSubscriptions(emailSubscriptions.map(s => s.id === id ? { ...s, ...updates } : s));
                }}
                onDeleteSubscription={(id) => {
                  setEmailSubscriptions(emailSubscriptions.filter(s => s.id !== id));
                }}
                onTestNotification={async (id) => {
                  setToast({ message: 'Test email sent', type: 'success' });
                }}
                showToast={(message, type) => setToast({ message, type })}
              />
            </div>
          </TabsContent>
        </Tabs>
      </Card>
    </div>
  );
};

export default SettingsTabPage;