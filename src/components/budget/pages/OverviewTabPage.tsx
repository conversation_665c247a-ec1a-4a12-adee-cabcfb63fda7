import React from 'react';
import { BudgetOverviewLayout } from '../components/layouts/BudgetOverviewLayout';
import { BudgetCalendar } from '../components/calendar/BudgetCalendar';
import { MobileOptimizedCard } from '../components/mobile/MobileOptimizedCard';
import { MobileOptimizedList } from '../components/mobile/MobileOptimizedList';
import { OnboardingFlow } from '../components/onboarding/OnboardingFlow';
import { BudgetStats, HealthMetrics, HeatmapData } from '../types/budget.types';
import { Budget } from '@/stores/budgetStore';
import { Expense } from '../types/expense.types';
import { useMediaQuery } from '@/hooks/useMediaQuery';

interface OverviewTabPageProps {
  budget: Budget | null;
  stats: BudgetStats | null;
  expenses: Expense[];
  healthMetrics: HealthMetrics | null;
  heatmapData: HeatmapData | null;
  loading: boolean;
  year: number;
  onViewDetails: (section: string) => void;
  onCardClick: (cardType: string) => void;
  onRefresh: () => void;
  onTabChange: (tab: string) => void;
}

export const OverviewTabPage: React.FC<OverviewTabPageProps> = ({
  budget,
  stats,
  expenses,
  healthMetrics,
  heatmapData,
  loading,
  onViewDetails,
  onCardClick,
  onRefresh,
  onTabChange
}) => {
  const isMobile = useMediaQuery('(max-width: 768px)');
  const [showOnboarding, setShowOnboarding] = React.useState(() => {
    return !localStorage.getItem('budget-onboarding-completed');
  });
  const handleViewDetails = (section: string) => {
    if (section === 'analytics') {
      onTabChange('analytics');
    } else if (section === 'expenses') {
      onTabChange('expenses');
    } else if (section === 'categories') {
      onTabChange('budget');
    } else if (['increase', 'reallocate', 'plan'].includes(section)) {
      onTabChange('budget');
    } else if (section === 'review') {
      onTabChange('expenses');
    }
    onViewDetails(section);
  };

  const handleCardClick = (cardType: string) => {
    if (cardType === 'total-budget') {
      console.log('Total budget card clicked');
    } else if (cardType === 'spent') {
      onTabChange('expenses');
    } else if (cardType === 'remaining') {
      console.log('Remaining budget card clicked');
    }
    onCardClick(cardType);
  };

  // Prepare budget data for the layout
  const budgetData = budget ? {
    total_amount: budget.total_amount,
    allocated_amount: 0, // Placeholder since this doesn't exist in Budget type
    spent_amount: budget.spent_amount || 0,
    remaining_amount: budget.total_amount - (budget.spent_amount || 0)
  } : {
    total_amount: 0,
    allocated_amount: 0,
    spent_amount: 0,
    remaining_amount: 0
  };

  // Default stats if not available
  const defaultStats: BudgetStats = {
    total: 0,
    spent: 0,
    remaining: 0,
    committed: 0,
    utilization: 0,
    trend: 0,
    by_category: []
  };

  // Default health metrics if not available
  const defaultHealthMetrics: HealthMetrics = {
    totalBudget: 0,
    totalSpent: 0,
    utilization: 0,
    burnRate: 0,
    projectedSpend: 0,
    riskScore: 0,
    savingsOpportunity: 0,
    complianceRate: 95,
    efficiencyScore: 0,
    categoryDistribution: {},
    monthlyTrend: [],
    score: 0,
    status: 'unknown',
    indicators: {
      spending: { value: 0, status: 'healthy', trend: 'stable' },
      efficiency: { value: 0, status: 'healthy', trend: 'stable' },
      risk: { value: 0, status: 'healthy', trend: 'stable' },
      compliance: { value: 95, status: 'healthy', trend: 'stable' }
    },
    recommendations: []
  };

  // Handle onboarding completion
  const handleOnboardingComplete = () => {
    localStorage.setItem('budget-onboarding-completed', 'true');
    setShowOnboarding(false);
  };

  // Prepare mobile list items from expenses
  const mobileListItems = expenses.slice(0, 10).map(expense => ({
    id: expense.id,
    title: expense.description,
    subtitle: expense.category,
    amount: expense.amount,
    status: expense.status as 'pending' | 'approved' | 'rejected',
    date: expense.date
  }));

  // Show onboarding for first-time users
  if (showOnboarding) {
    const onboardingSteps = [
      {
        id: 'welcome',
        title: 'Welcome to Budget Manager',
        content: 'Track and manage your training budget efficiently',
        icon: 'wallet',
        action: {
          label: 'Get Started',
          onClick: () => console.log('Welcome step')
        }
      },
      {
        id: 'overview',
        title: 'Budget Overview',
        content: 'View your total budget, spending, and remaining funds at a glance',
        icon: 'chart',
        action: {
          label: 'Next',
          onClick: () => console.log('Overview step')
        }
      },
      {
        id: 'expenses',
        title: 'Track Expenses',
        content: 'Log and categorize your training expenses easily',
        icon: 'receipt',
        action: {
          label: 'Next',
          onClick: () => console.log('Expenses step')
        }
      },
      {
        id: 'analytics',
        title: 'Analytics & Reports',
        content: 'Generate insights and reports to optimize your budget',
        icon: 'trending-up',
        action: {
          label: 'Finish',
          onClick: () => console.log('Analytics step')
        }
      }
    ];

    return (
      <OnboardingFlow
        steps={onboardingSteps}
        onComplete={handleOnboardingComplete}
        onSkip={() => setShowOnboarding(false)}
      />
    );
  }

  return (
    <div className="space-y-4">
      {/* Calendar removed - now shown in sidebar */}

      {/* Mobile Optimized View */}
      {isMobile ? (
        <div className="space-y-4">
          {/* Mobile Stats Cards */}
          <div className="grid grid-cols-1 gap-4">
            <MobileOptimizedCard
              title="Total Budget"
              value={`$${budgetData.total_amount.toLocaleString()}`}
              subtitle="Annual allocation"
              variant="primary"
              onClick={() => handleCardClick('total-budget')}
            />
            <MobileOptimizedCard
              title="Spent"
              value={`$${budgetData.spent_amount.toLocaleString()}`}
              subtitle={`${((budgetData.spent_amount / budgetData.total_amount) * 100).toFixed(1)}% used`}
              variant="warning"
              onClick={() => handleCardClick('spent')}
            />
            <MobileOptimizedCard
              title="Remaining"
              value={`$${budgetData.remaining_amount.toLocaleString()}`}
              subtitle="Available to spend"
              variant="success"
              onClick={() => handleCardClick('remaining')}
            />
          </div>

          {/* Mobile Expense List */}
          {mobileListItems.length > 0 && (
            <MobileOptimizedList
              items={mobileListItems}
              onItemClick={(item) => onViewDetails('expenses')}
              loading={loading}
              emptyMessage="No recent expenses"
            />
          )}
        </div>
      ) : (
        /* Desktop Layout */
        <BudgetOverviewLayout
          budget={budgetData}
          stats={stats || defaultStats}
          expenses={expenses}
          healthMetrics={healthMetrics || defaultHealthMetrics}
          heatmapData={heatmapData || undefined}
          loading={loading}
          onViewDetails={handleViewDetails}
          onCardClick={handleCardClick}
          onRefresh={onRefresh}
        />
      )}
    </div>
  );
};

export default OverviewTabPage;