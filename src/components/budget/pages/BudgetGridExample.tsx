import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  BudgetGridLayout,
  HeroSection,
  PrimaryCard,
  SecondaryCard,
  MetricsGrid,
  MetricCard,
  ContentGrid,
  MainContent,
  SidebarContent,
  FullWidthSection,
  SectionHeader,
  hierarchyClasses,
  spacing
} from '../components/layouts/BudgetGridLayout';
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Calendar,
  PieChart,
  AlertCircle
} from 'lucide-react';

/**
 * Example implementation of the Budget Grid Layout with proper visual hierarchy
 * This demonstrates the 4-level hierarchy system:
 * - Level 1: Hero metrics (largest, most prominent)
 * - Level 2: Supporting metrics (medium prominence)
 * - Level 3: Detail cards (smaller, regular weight)
 * - Level 4: Supplementary info (smallest, least prominent)
 */
export const BudgetGridExample: React.FC = () => {
  return (
    <BudgetGridLayout>
      {/* Level 1: Hero Section - Primary Focus */}
      <HeroSection>
        <PrimaryCard>
          <Card className={hierarchyClasses.level1.card}>
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <CardTitle className={hierarchyClasses.level1.title}>
                  Budget Overview
                </CardTitle>
                <Badge className="bg-green-500/10 text-green-600 border-green-200">
                  Healthy
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className={hierarchyClasses.level1.label}>Total Budget</p>
                  <p className={hierarchyClasses.level1.value}>$125,000</p>
                </div>
                <div>
                  <p className={hierarchyClasses.level1.label}>Remaining</p>
                  <p className={hierarchyClasses.level1.value}>$48,750</p>
                </div>
              </div>
              <Progress value={61} className="h-2 mt-4" />
              <p className={hierarchyClasses.level2.label + " mt-2"}>61% utilized</p>
            </CardContent>
          </Card>
        </PrimaryCard>
        
        <SecondaryCard>
          <Card className="h-full">
            <CardHeader className="pb-2">
              <CardTitle className={hierarchyClasses.level2.title}>
                Monthly Trend
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-2">
                <TrendingDown className="h-5 w-5 text-green-500" />
                <div>
                  <p className={hierarchyClasses.level2.value}>-12.5%</p>
                  <p className={hierarchyClasses.level2.label}>vs last month</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </SecondaryCard>
      </HeroSection>

      {/* Level 2: Metrics Grid - Supporting Information */}
      <MetricsGrid>
        <MetricCard variant="primary">
          <div className="flex items-center justify-between">
            <div>
              <p className={hierarchyClasses.level3.label}>Daily Avg</p>
              <p className={hierarchyClasses.level3.value}>$2,480</p>
            </div>
            <Calendar className="h-4 w-4 text-primary opacity-60" />
          </div>
        </MetricCard>
        
        <MetricCard variant="success">
          <div className="flex items-center justify-between">
            <div>
              <p className={hierarchyClasses.level3.label}>Saved</p>
              <p className={hierarchyClasses.level3.value}>$8,200</p>
            </div>
            <TrendingUp className="h-4 w-4 text-green-500 opacity-60" />
          </div>
        </MetricCard>
        
        <MetricCard variant="warning">
          <div className="flex items-center justify-between">
            <div>
              <p className={hierarchyClasses.level3.label}>At Risk</p>
              <p className={hierarchyClasses.level3.value}>$3,500</p>
            </div>
            <AlertCircle className="h-4 w-4 text-orange-500 opacity-60" />
          </div>
        </MetricCard>
        
        <MetricCard>
          <div className="flex items-center justify-between">
            <div>
              <p className={hierarchyClasses.level3.label}>Categories</p>
              <p className={hierarchyClasses.level3.value}>12</p>
            </div>
            <PieChart className="h-4 w-4 text-muted-foreground opacity-60" />
          </div>
        </MetricCard>
        
        <MetricCard>
          <div className="flex items-center justify-between">
            <div>
              <p className={hierarchyClasses.level3.label}>Transactions</p>
              <p className={hierarchyClasses.level3.value}>248</p>
            </div>
            <DollarSign className="h-4 w-4 text-muted-foreground opacity-60" />
          </div>
        </MetricCard>
        
        <MetricCard variant="danger">
          <div className="flex items-center justify-between">
            <div>
              <p className={hierarchyClasses.level3.label}>Over Budget</p>
              <p className={hierarchyClasses.level3.value}>2</p>
            </div>
            <TrendingUp className="h-4 w-4 text-red-500 opacity-60" />
          </div>
        </MetricCard>
      </MetricsGrid>

      {/* Level 3: Main Content Area - Detailed Information */}
      <ContentGrid>
        <MainContent>
          <Card>
            <CardHeader className="pb-3">
              <SectionHeader 
                title="Category Breakdown"
                description="Top spending categories this month"
              />
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {[
                  { name: 'Technology', spent: 28500, budget: 35000, color: 'blue' },
                  { name: 'Marketing', spent: 22000, budget: 25000, color: 'purple' },
                  { name: 'Operations', spent: 18750, budget: 20000, color: 'green' },
                  { name: 'HR & Training', spent: 12000, budget: 15000, color: 'orange' },
                  { name: 'Facilities', spent: 8500, budget: 10000, color: 'pink' },
                ].map((category, i) => (
                  <div key={i} className="space-y-1.5">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className={`w-2 h-2 rounded-full bg-${category.color}-500`} />
                        <span className={hierarchyClasses.level3.title}>{category.name}</span>
                      </div>
                      <div className="text-right">
                        <span className={hierarchyClasses.level3.value}>
                          ${category.spent.toLocaleString()}
                        </span>
                        <span className={hierarchyClasses.level4.label}>
                          {' '}/ ${category.budget.toLocaleString()}
                        </span>
                      </div>
                    </div>
                    <Progress 
                      value={(category.spent / category.budget) * 100} 
                      className="h-1.5"
                    />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </MainContent>
        
        <SidebarContent>
          <div className="space-y-4">
            {/* Summary Card */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className={hierarchyClasses.level2.title}>
                  Quick Actions
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-2">
                  {['Allocate', 'Transfer', 'Report', 'Export'].map((action) => (
                    <button
                      key={action}
                      className="px-3 py-2 text-xs font-medium border rounded hover:bg-accent transition-colors"
                    >
                      {action}
                    </button>
                  ))}
                </div>
              </CardContent>
            </Card>
            
            {/* Alerts Card */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className={hierarchyClasses.level2.title}>
                  Alerts
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex items-start gap-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-red-500 mt-1.5" />
                    <div>
                      <p className={hierarchyClasses.level4.title}>Marketing overspent</p>
                      <p className={hierarchyClasses.level4.label}>2 days ago</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-1.5 h-1.5 rounded-full bg-orange-500 mt-1.5" />
                    <div>
                      <p className={hierarchyClasses.level4.title}>Q4 planning due</p>
                      <p className={hierarchyClasses.level4.label}>In 5 days</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </SidebarContent>
      </ContentGrid>

      {/* Level 4: Full Width Section - Supporting Visualizations */}
      <FullWidthSection>
        <Card>
          <CardHeader className="pb-3">
            <SectionHeader 
              title="Spending Heatmap"
              description="Daily spending patterns for the current month"
            />
          </CardHeader>
          <CardContent>
            <div className="h-32 bg-gradient-to-r from-green-50 via-yellow-50 to-red-50 rounded flex items-center justify-center text-muted-foreground">
              Heatmap Visualization Placeholder
            </div>
          </CardContent>
        </Card>
      </FullWidthSection>
    </BudgetGridLayout>
  );
};

export default BudgetGridExample;