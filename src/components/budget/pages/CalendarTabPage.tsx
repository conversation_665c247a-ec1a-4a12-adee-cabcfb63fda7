import React from 'react';
import { BudgetCalendar, BudgetEvent } from '../components/calendar/BudgetCalendar';
import { Card } from '@/components/ui/card';
import { motion } from 'framer-motion';

interface CalendarTabPageProps {
  year: number;
  events: BudgetEvent[];
  loading?: boolean;
}

export const CalendarTabPage: React.FC<CalendarTabPageProps> = ({
  year,
  events,
  loading = false
}) => {
  const handleEventClick = (event: BudgetEvent) => {
  };

  const handleDateClick = (date: Date, events: BudgetEvent[]) => {
  };

  const handleAddEvent = (date: Date) => {
  };

  return (
    <div className="flex-1 flex flex-col min-h-0">
      <div className="flex justify-between items-center flex-shrink-0 mb-6">
        <h2 className="text-2xl font-bold">Budget Calendar</h2>
      </div>

      <div className="flex-1 overflow-y-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="h-full"
        >
          <Card className="h-full p-6">
            <BudgetCalendar
              events={events}
              onEventClick={handleEventClick}
              onDateClick={handleDateClick}
              onAddEvent={handleAddEvent}
              loading={loading}
              compactMode={false}
              showWeekends={true}
            />
          </Card>
        </motion.div>
      </div>
    </div>
  );
};

export default CalendarTabPage;