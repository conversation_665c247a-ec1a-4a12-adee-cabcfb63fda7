import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { ExpenseDialog } from '../components/dialogs/ExpenseDialog';
import { ExpenseList } from '../components/expenses/ExpenseList';
import { DepartmentAllocation } from '@/stores/budgetStore';
import { ExpenseFormData, Expense, ExpenseTemplate } from '../types/expense.types';

interface ExpensesTabPageProps {
  expenses: Expense[];
  departmentAllocations: DepartmentAllocation[];
  templates: ExpenseTemplate[];
  expenseTemplates: ExpenseTemplate[];
  loading: boolean;
  onExpenseSubmit: (data: ExpenseFormData) => Promise<void>;
  onExpenseEdit: (expense: Expense) => void;
  onExpenseDelete: (id: string) => Promise<void>;
  onStatusChange: (id: string, status: string) => Promise<void>;
  onViewReceipt: (url: string) => void;
  onSaveTemplate: (template: Omit<ExpenseTemplate, 'id'>) => Promise<void>;
  addExpense?: (data: ExpenseFormData) => Promise<void>;
  updateExpense?: (id: string, data: Partial<Expense>) => Promise<void>;
}

export const ExpensesTabPage: React.FC<ExpensesTabPageProps> = ({
  expenses,
  departmentAllocations,
  templates,
  expenseTemplates,
  loading,
  onExpenseSubmit,
  onExpenseEdit,
  onExpenseDelete,
  onStatusChange,
  onViewReceipt,
  onSaveTemplate,
  addExpense,
  updateExpense
}) => {
  const [showExpenseForm, setShowExpenseForm] = useState(false);
  const [editingExpense, setEditingExpense] = useState<Expense | null>(null);

  const handleExpenseSubmit = async (data: ExpenseFormData) => {
    try {
      if (editingExpense && updateExpense) {
        // Merge form data with existing expense data for update
        const expenseUpdate: Partial<Expense> = {
          ...editingExpense,
          ...data,
          id: editingExpense.id,
          updated_at: new Date().toISOString()
        };
        await updateExpense(editingExpense.id, expenseUpdate);
      } else if (!editingExpense && addExpense) {
        // Use direct addExpense for new expenses
        await addExpense(data);
      } else {
        // Fallback to parent's handleExpenseSubmit
        await onExpenseSubmit(data);
      }
      setEditingExpense(null);
      setShowExpenseForm(false);
    } catch (error) {
      console.error('Failed to save expense:', error);
      throw error;
    }
  };

  const handleExpenseEdit = (expense: Expense) => {
    setEditingExpense(expense);
    setShowExpenseForm(true);
    // Don't call parent's onExpenseEdit to avoid triggering global dialog
    // onExpenseEdit(expense);
  };

  const handleDialogChange = (open: boolean) => {
    setShowExpenseForm(open);
    if (!open) {
      setEditingExpense(null);
    }
  };

  return (
    <div className="space-y-6">
      {/* Add Expense Button */}
      <div className="flex justify-end">
        <Button
          onClick={() => setShowExpenseForm(true)}
          className="bg-primary hover:bg-primary/90"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add New Expense
        </Button>
      </div>

      {/* Expense Dialog */}
      <ExpenseDialog
        open={showExpenseForm}
        onOpenChange={handleDialogChange}
        onSubmit={handleExpenseSubmit}
        departments={departmentAllocations}
        initialData={editingExpense || undefined}
        isEditing={!!editingExpense}
        templates={[...templates, ...expenseTemplates]}
        onSaveAsTemplate={onSaveTemplate}
      />

      {/* Expense List */}
      <ExpenseList
        expenses={expenses}
        departments={departmentAllocations}
        onEdit={handleExpenseEdit}
        onDelete={onExpenseDelete}
        onStatusChange={onStatusChange}
        onViewReceipt={onViewReceipt}
        loading={loading}
      />
    </div>
  );
};

export default ExpensesTabPage;