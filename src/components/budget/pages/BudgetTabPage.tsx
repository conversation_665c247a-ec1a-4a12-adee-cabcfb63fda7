import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  Wallet, 
  FileText, 
  Calendar as CalendarIcon, 
  RefreshCw,
  PiggyBank,
  TrendingUp,
  Shuffle,
  History
} from 'lucide-react';
import { QuarterlyBreakdown } from '../components/allocation/QuarterlyBreakdown';
import { DepartmentAllocations } from '../components/allocation/DepartmentAllocations';
import { CategoryLimits } from '../components/allocation/CategoryLimits';
import { AutoAllocationRules } from '../components/allocation/AutoAllocationRules';
import { ReserveFund } from '../components/planning/ReserveFund';
import { ScenarioPlanner } from '../components/planning/ScenarioPlanner';
import { CarryoverRules } from '../components/planning/CarryoverRules';
import { BudgetTemplates } from '../components/planning/BudgetTemplates';
import { formatCurrency } from '../utils/formatters';
import { AdjustBudgetDialog } from '../components/dialogs/AdjustBudgetDialog';
import { BudgetTemplatesDialog } from '../components/dialogs/BudgetTemplatesDialog';
import { QuarterlyPlanDialog } from '../components/dialogs/QuarterlyPlanDialog';
import { AutoRulesDialog } from '../components/dialogs/AutoRulesDialog';
import { useBudgetTemplates, useSetBudget, useSaveBudgetTemplate, useDeleteBudgetTemplate, budgetKeys } from '@/hooks/queries/useBudgetQueries';
import { BudgetStats } from '../types/budget.types';
import { Budget, DepartmentAllocation, CategoryLimit } from '@/stores/budgetStore';
import type { AllocationRule } from '../components/allocation/AutoAllocationRules';
import * as budgetApi from '@/lib/services/budgetApi';
import { useQueryClient } from '@tanstack/react-query';

interface BudgetTabPageProps {
  budget: Budget | null;
  stats: BudgetStats | null;
  quarterlyAllocations: any[];
  departmentAllocations: DepartmentAllocation[];
  categoryLimits: CategoryLimit[];
  allocationRules: AllocationRule[];
  loading: boolean;
  rulesLoading: boolean;
  onUpdateQuarterlyAllocations: (allocations: any) => Promise<void>;
  onUpdateDepartmentAllocation: (departmentId: string, amount: number) => Promise<void>;
  onAddDepartment: (name: string, amount: number) => Promise<void>;
  onRemoveDepartment: (departmentId: string) => Promise<void>;
  onSetCategoryLimit: (category: string, limit: number) => Promise<void>;
  onRemoveCategoryLimit: (category: string) => Promise<void>;
  onAddAllocationRule: (rule: any) => Promise<void>;
  onUpdateAllocationRule: (id: string, updates: any) => Promise<void>;
  onDeleteAllocationRule: (id: string) => Promise<void>;
  onToggleAllocationRule: (id: string, enabled: boolean) => Promise<void>;
  onExecuteAllocationRule: (id: string) => Promise<void>;
  onTabChange: (tab: string) => void;
}

export const BudgetTabPage: React.FC<BudgetTabPageProps> = ({
  // Props are passed in but we'll also use the store for additional functionality
  budget,
  stats,
  quarterlyAllocations,
  departmentAllocations,
  categoryLimits,
  allocationRules,
  loading,
  rulesLoading,
  onUpdateQuarterlyAllocations,
  onUpdateDepartmentAllocation,
  onAddDepartment,
  onRemoveDepartment,
  onSetCategoryLimit,
  onRemoveCategoryLimit,
  onAddAllocationRule,
  onUpdateAllocationRule,
  onDeleteAllocationRule,
  onToggleAllocationRule,
  onExecuteAllocationRule,
  onTabChange
}) => {
  // Dialog states
  const [adjustBudgetOpen, setAdjustBudgetOpen] = useState(false);
  const [templatesOpen, setTemplatesOpen] = useState(false);
  const [quarterlyPlanOpen, setQuarterlyPlanOpen] = useState(false);
  const [autoRulesOpen, setAutoRulesOpen] = useState(false);
  const [showReserveFund, setShowReserveFund] = useState(false);
  const [showScenarioPlanner, setShowScenarioPlanner] = useState(false);
  const [showCarryoverRules, setShowCarryoverRules] = useState(false);
  
  // Get React Query client for cache invalidation
  const queryClient = useQueryClient();
  
  // Get React Query hooks for templates and budget operations
  const { data: templates = [] } = useBudgetTemplates();
  const setBudgetMutation = useSetBudget();
  const saveTemplateMutation = useSaveBudgetTemplate();
  const deleteTemplateMutation = useDeleteBudgetTemplate();

  // Template operations
  const setBudget = async (year: number, totalAmount: number) => {
    await setBudgetMutation.mutateAsync({ year, totalAmount });
  };

  const saveTemplate = async (template: any) => {
    await saveTemplateMutation.mutateAsync(template);
  };

  const deleteTemplate = async (templateId: string) => {
    await deleteTemplateMutation.mutateAsync(templateId);
  };

  // Template application function
  const applyTemplate = async (templateId: string) => {
    const currentYear = new Date().getFullYear();
    await budgetApi.applyBudgetTemplate(templateId, currentYear);
    // Refresh budget data after applying template
    queryClient.invalidateQueries({ queryKey: budgetKeys.budget(currentYear) });
    queryClient.invalidateQueries({ queryKey: budgetKeys.stats(currentYear) });
  };

  const exportTemplate = async (templateId: string) => {
    try {
      const template = templates.find(t => t.id === templateId);
      if (!template) {
        throw new Error('Template not found');
      }
      
      // Create a blob with the template data
      const blob = new Blob([JSON.stringify(template, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      
      // Create a download link
      const a = document.createElement('a');
      a.href = url;
      a.download = `budget-template-${template.name.toLowerCase().replace(/\s+/g, '-')}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Failed to export template:', error);
      throw error;
    }
  };

  const importTemplate = async (file: File) => {
    try {
      const text = await file.text();
      const templateData = JSON.parse(text);
      
      // Validate the template structure
      if (!templateData.name || !templateData.config) {
        throw new Error('Invalid template format');
      }
      
      // Generate new ID for imported template
      const importedTemplate = {
        ...templateData,
        id: `imported-${Date.now()}`,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      
      await saveTemplate(importedTemplate);
    } catch (error) {
      console.error('Failed to import template:', error);
      throw error;
    }
  };

  // Add missing quarterly allocations update function
  const storeUpdateQuarterlyAllocations = async (allocations: any) => {
    try {
      const currentYear = new Date().getFullYear();
      await budgetApi.updateQuarterlyAllocations(currentYear, allocations);
      queryClient.invalidateQueries({ queryKey: budgetKeys.quarterlyAllocations(currentYear) });
    } catch (error) {
      console.error('Failed to update quarterly allocations:', error);
      throw error;
    }
  };
  
  // Get current year (from budget or default to current year)
  const year = budget?.fiscal_year || new Date().getFullYear();
  
  const totalBudget = budget?.total_amount || stats?.total || 0;
  const spentAmount = stats?.spent || 0;
  const remainingAmount = stats?.remaining || (totalBudget - spentAmount);
  const utilizationPercentage = totalBudget > 0 ? (spentAmount / totalBudget) * 100 : 0;

  return (
    <div className="space-y-4">
      {/* Consistent 12-Column Grid Layout */}
      <div className="grid grid-cols-12 gap-4">
        {/* Quarterly Breakdown - Primary Focus (8 columns) */}
        <div className="col-span-12 lg:col-span-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="h-full"
          >
            <QuarterlyBreakdown
              allocations={quarterlyAllocations}
              totalBudget={totalBudget}
              onUpdate={onUpdateQuarterlyAllocations}
              loading={loading}
            />
          </motion.div>
        </div>

        {/* Department Allocations - Secondary Focus (4 columns) */}
        <div className="col-span-12 lg:col-span-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="h-full"
          >
            <DepartmentAllocations
              departments={departmentAllocations}
              totalBudget={totalBudget}
              onUpdate={onUpdateDepartmentAllocation}
              onAdd={onAddDepartment}
              onRemove={onRemoveDepartment}
              loading={loading}
            />
          </motion.div>
        </div>

        {/* Category Limits - Supporting Card (6 columns) */}
        <div className="col-span-12 md:col-span-6 lg:col-span-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="h-full"
          >
            <CategoryLimits
              limits={categoryLimits}
              categories={[]}
              totalBudget={totalBudget}
              onSetLimit={onSetCategoryLimit}
              onRemoveLimit={onRemoveCategoryLimit}
              loading={loading}
            />
          </motion.div>
        </div>

        {/* Auto-allocation Rules - Supporting Card (6 columns) */}
        <div className="col-span-12 md:col-span-6 lg:col-span-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="h-full"
          >
            <AutoAllocationRules
              rules={allocationRules}
              departments={departmentAllocations}
              categories={stats?.by_category?.map(c => ({ 
                id: c.category, 
                name: c.category 
              })) || []}
              onAddRule={onAddAllocationRule}
              onUpdateRule={onUpdateAllocationRule}
              onDeleteRule={onDeleteAllocationRule}
              onToggleRule={onToggleAllocationRule}
              onExecuteRule={onExecuteAllocationRule}
              loading={rulesLoading}
            />
          </motion.div>
        </div>

        {/* Reserve Fund Management - When visible */}
        {showReserveFund && (
          <div className="col-span-12 md:col-span-6 lg:col-span-4">
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.5 }}
              className="h-full"
            >
              <ReserveFund
          reserveFunds={[
            {
              id: 'default',
              name: 'General Reserve',
              targetAmount: totalBudget * 0.1,
              currentAmount: 0,
              percentage: 10,
              fundType: 'emergency',
              autoContribute: true,
              contributionPercentage: 5,
              status: 'active',
              transactions: []
            }
          ]}
          onCreateFund={async (fund) => {
            console.log('Creating reserve fund:', fund);
            // Implementation would go here
          }}
          onUpdateFund={async (id, fund) => {
            if (fund.currentAmount !== undefined) {
              await budgetApi.updateReserveFund(year, fund.currentAmount);
              queryClient.invalidateQueries({ queryKey: budgetKeys.budget(year) });
            }
            if (fund.targetAmount !== undefined) {
              await budgetApi.setReserveTarget(year, fund.targetAmount);
              queryClient.invalidateQueries({ queryKey: budgetKeys.budget(year) });
            }
          }}
          onDeleteFund={(id) => {
            console.log('Deleting reserve fund:', id);
          }}
          onTransaction={async (fundId, transaction) => {
            console.log('Processing transaction:', fundId, transaction);
          }}
              onApproveTransaction={(fundId, transactionId, approved) => {
                console.log('Approving transaction:', fundId, transactionId, approved);
              }}
              loading={loading}
            />
            </motion.div>
          </div>
        )}

        {/* Scenario Planner - When visible */}
        {showScenarioPlanner && (
          <div className="col-span-12 md:col-span-6 lg:col-span-4">
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.5 }}
              className="h-full"
            >
              <ScenarioPlanner
          currentBudget={{
            total: totalBudget,
            spent: spentAmount,
            departments: departmentAllocations,
            categories: categoryLimits
          }}
          onSaveScenario={async (scenario) => {
            try {
              await budgetApi.saveBudgetScenario(year, scenario);
              queryClient.invalidateQueries({ queryKey: budgetKeys.analytics(year) });
            } catch (error) {
              console.error('Failed to save scenario:', error);
              throw error;
            }
          }}
          onApplyScenario={async (scenario) => {
            try {
              await budgetApi.applyBudgetScenario(year, scenario);
              queryClient.invalidateQueries({ queryKey: budgetKeys.budget(year) });
              queryClient.invalidateQueries({ queryKey: budgetKeys.stats(year) });
              queryClient.invalidateQueries({ queryKey: budgetKeys.departmentAllocations(year) });
              queryClient.invalidateQueries({ queryKey: budgetKeys.categoryLimits(year) });
            } catch (error) {
              console.error('Failed to apply scenario:', error);
              throw error;
            }
          }}
        />
            </motion.div>
          </div>
        )}

        {/* Carryover Rules - When visible */}
        {showCarryoverRules && (
          <div className="col-span-12 md:col-span-6 lg:col-span-6">
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.6 }}
              className="h-full"
            >
              <CarryoverRules
          rules={[]}
          carryoverHistory={[]}
          onCreateRule={async (rule) => {
            console.log('Creating carryover rule:', rule);
            // Implementation would go here
          }}
          onUpdateRule={async (id, rule) => {
            console.log('Updating carryover rule:', id, rule);
            // Implementation would go here
          }}
          onDeleteRule={async (id) => {
            console.log('Deleting carryover rule:', id);
            // Implementation would go here
          }}
          onExecuteRule={async (ruleId) => {
            try {
              const nextYear = year + 1;
              await budgetApi.applyCarryoverRules(year, nextYear, { ruleId });
              queryClient.invalidateQueries({ queryKey: budgetKeys.budget(nextYear) });
              queryClient.invalidateQueries({ queryKey: budgetKeys.stats(nextYear) });
            } catch (error) {
              console.error('Failed to execute carryover rule:', error);
              throw error;
            }
          }}
          onSimulateCarryover={() => ({
            totalUnspent: remainingAmount,
            projectedCarryover: remainingAmount * 0.8, // 80% carryover simulation
            byCategory: stats?.by_category?.reduce((acc, cat) => ({
              ...acc,
              [cat.category]: (cat.total - cat.spent) || 0
            }), {}) || {}
          })}
          loading={loading}
        />
            </motion.div>
          </div>
        )}

        {/* Budget Templates - When visible */}
        {showCarryoverRules && (
          <div className="col-span-12 md:col-span-6 lg:col-span-6">
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.7 }}
              className="h-full"
            >
              <BudgetTemplates
                templates={templates}
                currentBudget={budget}
                onApplyTemplate={applyTemplate}
                onSaveTemplate={saveTemplate}
                onDeleteTemplate={deleteTemplate}
              />
            </motion.div>
          </div>
        )}
      </div>
      
      {/* Dialog Components */}
      <AdjustBudgetDialog
        open={adjustBudgetOpen}
        onOpenChange={setAdjustBudgetOpen}
        currentBudget={totalBudget}
        spentAmount={spentAmount}
        onAdjust={async (newBudget) => {
          await setBudget(newBudget, new Date().getFullYear());
        }}
      />
      
      <BudgetTemplatesDialog
        open={templatesOpen}
        onOpenChange={setTemplatesOpen}
        templates={templates}
        currentBudget={{
          total: totalBudget,
          departments: departmentAllocations,
          categories: categoryLimits,
          allocations: quarterlyAllocations,
          rules: allocationRules,
        }}
        onApplyTemplate={applyTemplate}
        onSaveTemplate={saveTemplate}
        onDeleteTemplate={deleteTemplate}
        onExportTemplate={exportTemplate}
        onImportTemplate={importTemplate}
      />
      
      <QuarterlyPlanDialog
        open={quarterlyPlanOpen}
        onOpenChange={setQuarterlyPlanOpen}
        allocations={quarterlyAllocations}
        totalBudget={totalBudget}
        onUpdate={async (allocations) => {
          await storeUpdateQuarterlyAllocations(allocations);
          onUpdateQuarterlyAllocations(allocations);
        }}
      />
      
      <AutoRulesDialog
        open={autoRulesOpen}
        onOpenChange={setAutoRulesOpen}
        rules={allocationRules}
        departments={departmentAllocations}
        categories={stats?.by_category?.map(c => ({ 
          id: c.category, 
          name: c.category 
        })) || []}
        totalBudget={totalBudget}
        currentSpent={spentAmount}
        onAddRule={onAddAllocationRule}
        onUpdateRule={onUpdateAllocationRule}
        onDeleteRule={onDeleteAllocationRule}
        onToggleRule={onToggleAllocationRule}
        onExecuteRule={onExecuteAllocationRule}
      />
    </div>
  );
};

export default BudgetTabPage;