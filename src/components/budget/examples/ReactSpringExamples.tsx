import React, { useState } from 'react';
import { useSpring, animated } from '@react-spring/web';
import { 
  AnimatedBudgetSummary, 
  AnimatedBudgetProgress, 
  AnimatedExpenseList, 
  AnimatedBudgetCard 
} from '../components';

// Example data for demonstrations
const exampleSummary = {
  totalBudget: 150000,
  totalSpent: 87500,
  totalRemaining: 62500,
  utilization: 58.3,
  departmentCount: 8,
  activeCategories: 12,
  monthlyAverage: 7291.67,
  lastUpdated: new Date().toISOString()
};

const exampleProgress = [
  {
    category: 'Training Materials',
    allocated: 25000,
    spent: 18000,
    remaining: 7000,
    percentage: 72,
    status: 'warning' as const
  },
  {
    category: 'Instructor Fees',
    allocated: 45000,
    spent: 35000,
    remaining: 10000,
    percentage: 77.8,
    status: 'warning' as const
  },
  {
    category: 'Venue Costs',
    allocated: 30000,
    spent: 15000,
    remaining: 15000,
    percentage: 50,
    status: 'healthy' as const
  }
];

const exampleExpenses = [
  {
    id: '1',
    title: 'Q1 Training Materials',
    amount: 5000,
    category: 'Training Materials',
    date: '2024-01-15',
    status: 'approved' as const,
    description: 'Materials for Q1 training sessions'
  },
  {
    id: '2',
    title: 'Instructor Workshop',
    amount: 8000,
    category: 'Instructor Fees',
    date: '2024-01-20',
    status: 'paid' as const,
    description: 'Advanced training workshop for instructors'
  }
];

// Basic usage example
export const BasicAnimatedSummary: React.FC = () => {
  const [loading, setLoading] = useState(false);
  
  const pageTransition = useSpring({
    from: { opacity: 0, transform: 'translateY(20px)' },
    to: { opacity: 1, transform: 'translateY(0px)' },
    config: { tension: 200, friction: 20 }
  });

  return (
    <animated.div style={pageTransition}>
      <AnimatedBudgetSummary
        summary={exampleSummary}
        loading={loading}
        onRefresh={() => {
          setLoading(true);
          setTimeout(() => setLoading(false), 1000);
        }}
      />
    </animated.div>
  );
};

// Progress tracking example
export const AnimatedProgressExample: React.FC = () => {
  const [showDetails, setShowDetails] = useState(true);
  
  return (
    <div className="space-y-4">
      <button 
        onClick={() => setShowDetails(!showDetails)}
        className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
      >
        Toggle Details
      </button>
      
      <AnimatedBudgetProgress
        budgetProgress={exampleProgress}
        totalBudget={exampleSummary.totalBudget}
        totalSpent={exampleSummary.totalSpent}
        showDetails={showDetails}
      />
    </div>
  );
};

// Expense list example
export const AnimatedExpensesExample: React.FC = () => {
  const [expenses, setExpenses] = useState(exampleExpenses);
  
  const addRandomExpense = () => {
    const newExpense = {
      id: Date.now().toString(),
      title: `Random Expense ${Math.floor(Math.random() * 100)}`,
      amount: Math.floor(Math.random() * 5000) + 1000,
      category: exampleProgress[Math.floor(Math.random() * exampleProgress.length)].category,
      date: new Date().toISOString().split('T')[0],
      status: 'pending' as const,
      description: 'Auto-generated expense'
    };
    setExpenses([...expenses, newExpense]);
  };

  return (
    <div className="space-y-4">
      <button 
        onClick={addRandomExpense}
        className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
      >
        Add Random Expense
      </button>
      
      <AnimatedExpenseList
        expenses={expenses}
        loading={false}
        onExpenseClick={(expense) => {}
        onStatusChange={(id, status) => {
          setExpenses(expenses.map(e => 
            e.id === id ? { ...e, status } : e
          ));
        }}
      />
    </div>
  );
};

// Budget cards grid example
export const AnimatedCardsExample: React.FC = () => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {exampleProgress.map((item) => (
        <AnimatedBudgetCard
          key={item.category}
          title={item.category}
          amount={item.spent}
          total={item.allocated}
          percentage={item.percentage}
          icon={<div className="w-4 h-4 bg-blue-500 rounded" />}
          color={
            item.percentage >= 90 ? 'text-red-600' :
            item.percentage >= 75 ? 'text-yellow-600' : 'text-green-600'
          }
          trend={Math.random() * 20 - 10}
        />
      ))}
    </div>
  );
};

// Complete dashboard example
export const AnimatedDashboardExample: React.FC = () => {
  const [activeSection, setActiveSection] = useState<'summary' | 'progress' | 'expenses'>('summary');
  
  const containerSpring = useSpring({
    from: { opacity: 0 },
    to: { opacity: 1 },
    config: { tension: 150, friction: 20 }
  });

  return (
    <animated.div style={containerSpring} className="space-y-6">
      <div className="flex space-x-2 mb-4">
        {(['summary', 'progress', 'expenses'] as const).map((section) => (
          <button
            key={section}
            onClick={() => setActiveSection(section)}
            className={`px-4 py-2 rounded ${
              activeSection === section 
                ? 'bg-blue-500 text-white' 
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            } transition-colors`}
          >
            {section.charAt(0).toUpperCase() + section.slice(1)}
          </button>
        ))}
      </div>

      {activeSection === 'summary' && <BasicAnimatedSummary />}
      {activeSection === 'progress' && <AnimatedProgressExample />}
      {activeSection === 'expenses' && <AnimatedExpensesExample />}
    </animated.div>
  );
};

// Props interface example
export interface AnimatedBudgetProps {
  summary: typeof exampleSummary;
  progress: typeof exampleProgress;
  expenses: typeof exampleExpenses;
  onRefresh: () => void;
  loading?: boolean;
}

// Full integration example
export const FullAnimatedBudgetIntegration: React.FC<AnimatedBudgetProps> = ({
  summary,
  progress,
  expenses,
  onRefresh,
  loading = false
}) => {
  const pageSpring = useSpring({
    from: { opacity: 0, transform: 'translateY(30px)' },
    to: { opacity: 1, transform: 'translateY(0px)' },
    config: { tension: 180, friction: 25 }
  });

  return (
    <animated.div style={pageSpring} className="space-y-8">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Training Budget Dashboard</h1>
        <button 
          onClick={onRefresh}
          disabled={loading}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
        >
          {loading ? 'Refreshing...' : 'Refresh'}
        </button>
      </div>

      {/* Summary */}
      <AnimatedBudgetSummary
        summary={summary}
        loading={loading}
        onRefresh={onRefresh}
      />

      {/* Progress Tracking */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div>
          <h2 className="text-xl font-semibold mb-4">Budget Progress</h2>
          <AnimatedBudgetProgress
            budgetProgress={progress}
            totalBudget={summary.totalBudget}
            totalSpent={summary.totalSpent}
            showDetails={true}
          />
        </div>
        
        <div>
          <h2 className="text-xl font-semibold mb-4">Recent Expenses</h2>
          <AnimatedExpenseList
            expenses={expenses.slice(0, 5)}
            loading={loading}
            onExpenseClick={(expense) => {}
          />
        </div>
      </div>

      {/* Budget Cards */}
      <div>
        <h2 className="text-xl font-semibold mb-4">Category Breakdown</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {progress.map((item) => (
            <AnimatedBudgetCard
              key={item.category}
              title={item.category}
              amount={item.spent}
              total={item.allocated}
              percentage={item.percentage}
              icon={<div className="w-4 h-4 bg-blue-500 rounded" />}
              color={
                item.percentage >= 90 ? 'text-red-600' :
                item.percentage >= 75 ? 'text-yellow-600' : 'text-green-600'
              }
              trend={Math.random() * 20 - 10}
            />
          ))}
        </div>
      </div>
    </animated.div>
  );
};

// Export all examples
export const ReactSpringExamples = {
  BasicAnimatedSummary,
  AnimatedProgressExample,
  AnimatedExpensesExample,
  AnimatedCardsExample,
  AnimatedDashboardExample,
  FullAnimatedBudgetIntegration
};

export default ReactSpringExamples;