import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface BentoGridProps {
  className?: string;
  children?: React.ReactNode;
}

export const BentoGrid: React.FC<BentoGridProps> = ({ className, children }) => {
  return (
    <div
      className={cn(
        "grid auto-rows-[22rem] grid-cols-3 gap-4",
        "md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8",
        className
      )}
    >
      {children}
    </div>
  );
};

interface BentoGridItemProps {
  className?: string;
  title?: string;
  description?: string;
  header?: React.ReactNode;
  icon?: React.ReactNode;
  children?: React.ReactNode;
  colSpan?: 1 | 2 | 3 | 4 | 6 | 8;
  rowSpan?: 1 | 2;
}

export const BentoGridItem: React.FC<BentoGridItemProps> = ({
  className,
  title,
  description,
  header,
  icon,
  children,
  colSpan = 1,
  rowSpan = 1,
}) => {
  const colSpanClass = {
    1: 'col-span-1',
    2: 'col-span-2',
    3: 'col-span-3',
    4: 'col-span-4',
    6: 'col-span-6',
    8: 'col-span-8',
  }[colSpan];

  const rowSpanClass = {
    1: 'row-span-1',
    2: 'row-span-2',
  }[rowSpan];

  return (
    <motion.div
      className={cn(
        colSpanClass,
        rowSpanClass,
        "group relative overflow-hidden rounded-2xl",
        "bg-gradient-to-br from-white/80 to-white/40",
        "backdrop-blur-xl backdrop-saturate-150",
        "border border-white/20",
        "shadow-[0_8px_32px_0_rgba(31,38,135,0.15)]",
        "hover:shadow-[0_8px_40px_0_rgba(31,38,135,0.25)]",
        "transition-all duration-300 ease-out",
        className
      )}
      whileHover={{ y: -4, scale: 1.01 }}
      transition={{ duration: 0.2, ease: [0.23, 1, 0.32, 1] }}
    >
      {/* Soft gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
      
      {/* Glass effect border */}
      <div className="absolute inset-[0] rounded-2xl ring-1 ring-inset ring-white/10" />
      
      <div className="relative h-full p-6 flex flex-col">
        {header && (
          <div className="mb-4">
            {header}
          </div>
        )}
        
        {(icon || title || description) && (
          <div className="flex items-start gap-4 mb-4">
            {icon && (
              <div className="flex-shrink-0 w-10 h-10 rounded-xl bg-gradient-to-br from-primary/10 to-primary/20 flex items-center justify-center">
                {icon}
              </div>
            )}
            <div className="flex-1">
              {title && (
                <h3 className="font-semibold text-gray-900 dark:text-white mb-1">
                  {title}
                </h3>
              )}
              {description && (
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {description}
                </p>
              )}
            </div>
          </div>
        )}
        
        <div className="flex-1">
          {children}
        </div>
      </div>
    </motion.div>
  );
};