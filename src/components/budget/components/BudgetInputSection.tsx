import React, { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import { parseCurrencyInput, formatCurrency } from '../utils/formatters';
import { validateBudgetAmount } from '../utils/budgetHelpers';
import { AlertCircle, CheckCircle } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface BudgetInputSectionProps {
  currentBudget?: number;
  onUpdate: (amount: number) => Promise<{ success: boolean; error?: string }>;
  loading?: boolean;
  isUpdating?: boolean;
}

export const BudgetInputSection: React.FC<BudgetInputSectionProps> = ({
  currentBudget = 0,
  onUpdate,
  loading = false,
  isUpdating = false,
}) => {
  const [inputValue, setInputValue] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [isDirty, setIsDirty] = useState(false);

  useEffect(() => {
    if (currentBudget > 0 && !isDirty) {
      setInputValue(currentBudget.toString());
    }
  }, [currentBudget, isDirty]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInputValue(value);
    setIsDirty(true);
    setError(null);
    setSuccess(false);

    // Real-time validation
    const parsed = parseCurrencyInput(value);
    if (value && parsed === null) {
      setError('Please enter a valid number');
    } else if (parsed !== null) {
      const validation = validateBudgetAmount(parsed);
      if (!validation.isValid) {
        setError(validation.error || 'Invalid amount');
      }
    }
  };

  const handleSubmit = useCallback(async () => {
    const parsed = parseCurrencyInput(inputValue);
    
    if (parsed === null) {
      setError('Please enter a valid number');
      return;
    }

    const validation = validateBudgetAmount(parsed);
    if (!validation.isValid) {
      setError(validation.error || 'Invalid amount');
      return;
    }

    const result = await onUpdate(parsed);
    
    if (result.success) {
      setSuccess(true);
      setError(null);
      setIsDirty(false);
      setTimeout(() => setSuccess(false), 3000);
    } else {
      setError(result.error || 'Failed to update budget');
      setSuccess(false);
    }
  }, [inputValue, onUpdate]);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !isUpdating && !error) {
      handleSubmit();
    }
  };

  if (loading) {
    return (
      <Card className="p-4">
        <Skeleton className="h-6 w-32 mb-3" />
        <div className="flex gap-3">
          <Skeleton className="h-10 flex-1" />
          <Skeleton className="h-10 w-32" />
        </div>
      </Card>
    );
  }

  return (
    <motion.div 
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ delay: 0.3 }}
      className="mb-6"
    >
      <Card className="p-4 bg-dashboard-card border-dashboard-border">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-white">Set Budget Amount</h3>
            {currentBudget > 0 && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <span className="text-sm text-gray-400">
                      Current: {formatCurrency(currentBudget)}
                    </span>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>The currently set budget amount</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="budget-input" className="sr-only">
              Budget Amount
            </Label>
            <div className="flex gap-3">
              <div className="relative flex-1">
                <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" aria-hidden="true">
                  $
                </span>
                <Input
                  id="budget-input"
                  type="text"
                  inputMode="decimal"
                  placeholder="Enter budget amount"
                  value={inputValue}
                  onChange={handleInputChange}
                  onKeyDown={handleKeyDown}
                  className={`pl-8 bg-dashboard-border border-dashboard-border text-white placeholder:text-gray-400 ${error ? 'border-red-500' : success ? 'border-dashboard-success' : ''}`}
                  aria-label="Budget amount in dollars"
                  aria-invalid={!!error}
                  aria-describedby={error ? 'budget-error' : success ? 'budget-success' : undefined}
                  disabled={isUpdating}
                />
              </div>
              <Button
                onClick={handleSubmit}
                disabled={isUpdating || !!error || !inputValue}
                aria-busy={isUpdating}
                className="bg-dashboard-accent text-white hover:bg-dashboard-accent/90"
              >
                {isUpdating ? 'Updating...' : 'Update Budget'}
              </Button>
            </div>
            
            {error && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="flex items-center gap-2 text-sm text-dashboard-accent"
                id="budget-error"
                role="alert"
              >
                <AlertCircle className="h-4 w-4" />
                <span>{error}</span>
              </motion.div>
            )}
            
            {success && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="flex items-center gap-2 text-sm text-dashboard-success"
                id="budget-success"
                role="status"
              >
                <CheckCircle className="h-4 w-4" />
                <span>Budget updated successfully!</span>
              </motion.div>
            )}
          </div>
        </div>
      </Card>
    </motion.div>
  );
};