import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Card } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  <PERSON><PERSON><PERSON>,
  Line,
  <PERSON><PERSON>hart,
  Area,
  XA<PERSON>s,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
} from 'recharts';
import {
  Shield,
  Plus,
  Minus,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  History,
  Lock,
  Unlock,
  DollarSign,
  Calendar,
  FileText,
  Download,
} from 'lucide-react';
import { formatCurrency, formatPercentage } from '../../utils/formatters';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useBudget, useBudgetStats } from '@/hooks/queries/useBudgetQueries';
import { toast } from 'sonner';

// Local interface - different from planning.types
export interface ReserveFundConfig {
  id: string;
  name: string;
  targetAmount: number;
  currentAmount: number;
  percentage: number; // of total budget
  status: 'active' | 'locked' | 'depleted';
  rules: {
    autoFunding: boolean;
    fundingPercentage: number; // percentage of savings to add
    minBalance: number;
    maxBalance: number;
    lockPeriod?: {
      start: string;
      end: string;
    };
    withdrawalRules: {
      requiresApproval: boolean;
      approvalThreshold: number;
      approvers: string[];
      maxWithdrawalPercentage: number;
    };
    eligibleCategories: string[]; // category IDs that can use reserve
  };
  transactions: ReserveTransaction[];
  projections: {
    monthlyContribution: number;
    targetDate: string;
    riskLevel: 'low' | 'medium' | 'high';
  };
}

export interface ReserveTransaction {
  id: string;
  type: 'deposit' | 'withdrawal' | 'interest' | 'adjustment';
  amount: number;
  date: string;
  description: string;
  category?: string;
  approvedBy?: string;
  status: 'pending' | 'approved' | 'rejected' | 'completed';
  attachments?: string[];
}

interface ReserveFundProps {
  reserveFunds: ReserveFundConfig[];
  onCreateFund: (fund: Omit<ReserveFundConfig, 'id' | 'transactions'>) => void;
  onUpdateFund: (id: string, fund: Partial<ReserveFundConfig>) => void;
  onDeleteFund: (id: string) => void;
  onTransaction: (fundId: string, transaction: Omit<ReserveTransaction, 'id'>) => void;
  onApproveTransaction: (fundId: string, transactionId: string, approved: boolean) => void;
  loading?: boolean;
}

const COLORS = ['#10b981', '#3b82f6', '#f59e0b', '#ef4444'];

const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-popover border rounded-lg shadow-lg p-3">
        <p className="font-medium mb-2">{label}</p>
        {payload.map((entry: any, index: number) => (
          <div key={index} className="flex items-center justify-between gap-4 text-sm">
            <span className="flex items-center gap-2">
              <span
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: entry.color }}
              />
              {entry.name}:
            </span>
            <span className="font-medium">{formatCurrency(entry.value)}</span>
          </div>
        ))}
      </div>
    );
  }
  return null;
};

export const ReserveFund: React.FC<ReserveFundProps> = ({
  reserveFunds,
  onCreateFund,
  onUpdateFund,
  onDeleteFund,
  onTransaction,
  onApproveTransaction,
  loading = false,
}) => {
  const currentYear = new Date().getFullYear();
  const { data: budget } = useBudget(currentYear);
  const { data: stats } = useBudgetStats(currentYear);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showTransactionDialog, setShowTransactionDialog] = useState(false);
  const [selectedFund, setSelectedFund] = useState<ReserveFundConfig | null>(null);
  const [transactionType, setTransactionType] = useState<'deposit' | 'withdrawal'>('deposit');

  const [formData, setFormData] = useState({
    name: '',
    targetAmount: 0,
    percentage: 5,
    autoFunding: true,
    fundingPercentage: 10,
    minBalance: 10000,
    maxBalance: 100000,
    requiresApproval: true,
    approvalThreshold: 5000,
    maxWithdrawalPercentage: 25,
    eligibleCategories: [] as string[],
  });

  const [transactionData, setTransactionData] = useState({
    amount: 0,
    description: '',
    category: '',
  });
  
  const [activeTab, setActiveTab] = useState('funds');

  const totalReserves = reserveFunds.reduce((sum, fund) => sum + fund.currentAmount, 0);
  const totalTarget = reserveFunds.reduce((sum, fund) => sum + fund.targetAmount, 0);
  const overallProgress = totalTarget > 0 ? (totalReserves / totalTarget) * 100 : 0;

  const handleCreateFund = () => {
    const currentAmount = (budget?.total_amount || 0) * (formData.percentage / 100);
    
    onCreateFund({
      name: formData.name,
      targetAmount: formData.targetAmount,
      currentAmount: currentAmount,
      percentage: formData.percentage,
      status: 'active',
      rules: {
        autoFunding: formData.autoFunding,
        fundingPercentage: formData.fundingPercentage,
        minBalance: formData.minBalance,
        maxBalance: formData.maxBalance,
        withdrawalRules: {
          requiresApproval: formData.requiresApproval,
          approvalThreshold: formData.approvalThreshold,
          approvers: ['Finance Manager', 'Budget Owner'],
          maxWithdrawalPercentage: formData.maxWithdrawalPercentage,
        },
        eligibleCategories: formData.eligibleCategories,
      },
      projections: {
        monthlyContribution: currentAmount * (formData.fundingPercentage / 100),
        targetDate: calculateTargetDate(currentAmount, formData.targetAmount, formData.fundingPercentage),
        riskLevel: calculateRiskLevel(formData.percentage),
      },
    });
    
    setShowCreateDialog(false);
    resetForm();
    toast.success('Reserve fund created successfully');
  };

  const handleTransaction = () => {
    if (!selectedFund) return;

    onTransaction(selectedFund.id, {
      type: transactionType,
      amount: transactionData.amount,
      date: new Date().toISOString(),
      description: transactionData.description,
      category: transactionData.category,
      status: transactionData.amount > selectedFund.rules.withdrawalRules.approvalThreshold 
        ? 'pending' 
        : 'completed',
    });

    setShowTransactionDialog(false);
    setTransactionData({ amount: 0, description: '', category: '' });
    toast.success(`${transactionType === 'deposit' ? 'Deposit' : 'Withdrawal'} initiated`);
  };

  const resetForm = () => {
    setFormData({
      name: '',
      targetAmount: 0,
      percentage: 5,
      autoFunding: true,
      fundingPercentage: 10,
      minBalance: 10000,
      maxBalance: 100000,
      requiresApproval: true,
      approvalThreshold: 5000,
      maxWithdrawalPercentage: 25,
      eligibleCategories: [],
    });
  };

  const calculateTargetDate = (current: number, target: number, rate: number): string => {
    if (current >= target) return 'Target Reached';
    const monthlyContribution = current * (rate / 100);
    const monthsNeeded = Math.ceil((target - current) / monthlyContribution);
    const targetDate = new Date();
    targetDate.setMonth(targetDate.getMonth() + monthsNeeded);
    return targetDate.toISOString().split('T')[0];
  };

  const calculateRiskLevel = (percentage: number): 'low' | 'medium' | 'high' => {
    if (percentage >= 10) return 'low';
    if (percentage >= 5) return 'medium';
    return 'high';
  };

  // Prepare data for charts
  const fundDistribution = reserveFunds.map(fund => ({
    name: fund.name,
    value: fund.currentAmount,
  }));

  const transactionHistory = reserveFunds.flatMap(fund => 
    fund.transactions
      .filter(t => t.status === 'completed')
      .map(t => ({
        date: new Date(t.date).toLocaleDateString(),
        amount: t.type === 'deposit' ? t.amount : -t.amount,
        fund: fund.name,
      }))
  ).sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

  if (loading) {
    return (
      <Card className="p-6">
        <Skeleton className="h-6 w-48 mb-4" />
        <Skeleton className="h-64 w-full" />
      </Card>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
    >
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <Shield className="h-5 w-5 text-muted-foreground" />
            <h3 className="text-lg font-semibold">Reserve Fund Management</h3>
          </div>
          <Button
            variant="default"
            size="sm"
            onClick={() => setShowCreateDialog(true)}
          >
            <Plus className="h-4 w-4 mr-2" />
            Create Fund
          </Button>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Card className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Reserves</p>
                <p className="text-2xl font-bold">{formatCurrency(totalReserves)}</p>
              </div>
              <Shield className="h-8 w-8 text-muted-foreground opacity-50" />
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Target Amount</p>
                <p className="text-2xl font-bold">{formatCurrency(totalTarget)}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-muted-foreground opacity-50" />
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Progress</p>
                <p className="text-2xl font-bold">{overallProgress.toFixed(1)}%</p>
              </div>
              <Progress value={overallProgress} className="w-16" />
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Active Funds</p>
                <p className="text-2xl font-bold">{reserveFunds.filter(f => f.status === 'active').length}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600 opacity-50" />
            </div>
          </Card>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="funds">Funds</TabsTrigger>
            <TabsTrigger value="transactions">Transactions</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="rules">Rules & Policies</TabsTrigger>
          </TabsList>

          <TabsContent value="funds" className="mt-6 space-y-4">
            {reserveFunds.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <Shield className="h-12 w-12 mx-auto mb-3 opacity-50" />
                <p>No reserve funds created yet</p>
                <p className="text-sm mt-1">Create your first reserve fund to protect against emergencies</p>
              </div>
            ) : (
              <div className="space-y-4">
                {reserveFunds.map((fund) => {
                  const progress = (fund.currentAmount / fund.targetAmount) * 100;
                  const isLocked = fund.status === 'locked';
                  
                  return (
                    <Card key={fund.id} className="p-4">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-center gap-3">
                          <div className={`p-2 rounded-lg ${isLocked ? 'bg-muted' : 'bg-green-100'}`}>
                            {isLocked ? <Lock className="h-5 w-5" /> : <Unlock className="h-5 w-5 text-green-600" />}
                          </div>
                          <div>
                            <div className="flex items-center gap-2">
                              <h4 className="font-medium">{fund.name}</h4>
                              <Badge variant={fund.status === 'active' ? 'default' : 'secondary'}>
                                {fund.status}
                              </Badge>
                              <Badge variant="outline">
                                {fund.projections.riskLevel} risk
                              </Badge>
                            </div>
                            <p className="text-sm text-muted-foreground">
                              {fund.percentage}% of total budget
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setSelectedFund(fund);
                              setTransactionType('deposit');
                              setShowTransactionDialog(true);
                            }}
                            disabled={isLocked}
                          >
                            <Plus className="h-4 w-4 mr-1" />
                            Deposit
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setSelectedFund(fund);
                              setTransactionType('withdrawal');
                              setShowTransactionDialog(true);
                            }}
                            disabled={isLocked || fund.currentAmount === 0}
                          >
                            <Minus className="h-4 w-4 mr-1" />
                            Withdraw
                          </Button>
                        </div>
                      </div>

                      <div className="space-y-3">
                        <div>
                          <div className="flex justify-between text-sm mb-1">
                            <span>Current / Target</span>
                            <span className="font-medium">
                              {formatCurrency(fund.currentAmount)} / {formatCurrency(fund.targetAmount)}
                            </span>
                          </div>
                          <Progress value={progress} className="h-2" />
                        </div>

                        <div className="grid grid-cols-3 gap-4 text-sm">
                          <div>
                            <p className="text-muted-foreground">Monthly Contribution</p>
                            <p className="font-medium">{formatCurrency(fund.projections.monthlyContribution)}</p>
                          </div>
                          <div>
                            <p className="text-muted-foreground">Target Date</p>
                            <p className="font-medium">{fund.projections.targetDate}</p>
                          </div>
                          <div>
                            <p className="text-muted-foreground">Available</p>
                            <p className="font-medium text-green-600">
                              {formatCurrency(fund.currentAmount - fund.rules.minBalance)}
                            </p>
                          </div>
                        </div>

                        {fund.rules.lockPeriod && (
                          <Alert>
                            <Lock className="h-4 w-4" />
                            <AlertDescription>
                              Locked until {new Date(fund.rules.lockPeriod.end).toLocaleDateString()}
                            </AlertDescription>
                          </Alert>
                        )}
                      </div>
                    </Card>
                  );
                })}
              </div>
            )}
          </TabsContent>

          <TabsContent value="transactions" className="mt-6">
            <div className="space-y-4">
              {/* Pending Approvals */}
              {reserveFunds.some(f => f.transactions.some(t => t.status === 'pending')) && (
                <Alert className="border-orange-200 bg-orange-50">
                  <AlertTriangle className="h-4 w-4 text-orange-600" />
                  <AlertTitle>Pending Approvals</AlertTitle>
                  <AlertDescription>
                    There are transactions awaiting approval
                  </AlertDescription>
                </Alert>
              )}

              {/* Transaction List */}
              <Card className="p-4">
                <h4 className="font-medium mb-4">Recent Transactions</h4>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date</TableHead>
                      <TableHead>Fund</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Description</TableHead>
                      <TableHead className="text-right">Amount</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {reserveFunds.flatMap(fund =>
                      fund.transactions.map(transaction => (
                        <TableRow key={transaction.id}>
                          <TableCell>
                            {new Date(transaction.date).toLocaleDateString()}
                          </TableCell>
                          <TableCell>{fund.name}</TableCell>
                          <TableCell>
                            <Badge variant={transaction.type === 'deposit' ? 'default' : 'secondary'}>
                              {transaction.type}
                            </Badge>
                          </TableCell>
                          <TableCell>{transaction.description}</TableCell>
                          <TableCell className={`text-right font-medium ${
                            transaction.type === 'deposit' ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {transaction.type === 'deposit' ? '+' : '-'}
                            {formatCurrency(transaction.amount)}
                          </TableCell>
                          <TableCell>
                            <Badge 
                              variant={
                                transaction.status === 'completed' ? 'default' :
                                transaction.status === 'pending' ? 'outline' :
                                transaction.status === 'approved' ? 'secondary' :
                                'destructive'
                              }
                            >
                              {transaction.status}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {transaction.status === 'pending' && (
                              <div className="flex gap-1">
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => onApproveTransaction(fund.id, transaction.id, true)}
                                >
                                  Approve
                                </Button>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => onApproveTransaction(fund.id, transaction.id, false)}
                                >
                                  Reject
                                </Button>
                              </div>
                            )}
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="analytics" className="mt-6 space-y-4">
            {/* Fund Distribution */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card className="p-4">
                <h4 className="font-medium mb-4">Fund Distribution</h4>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={fundDistribution}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ percent }) => `${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {fundDistribution.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip content={<CustomTooltip />} />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </Card>

              <Card className="p-4">
                <h4 className="font-medium mb-4">Reserve Growth</h4>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart data={transactionHistory}>
                      <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
                      <XAxis 
                        dataKey="date" 
                        className="text-xs"
                        tick={{ fill: 'currentColor' }}
                      />
                      <YAxis 
                        className="text-xs"
                        tick={{ fill: 'currentColor' }}
                        tickFormatter={(value) => `${(value / 1000).toFixed(0)}k`}
                      />
                      <Tooltip content={<CustomTooltip />} />
                      <Area
                        type="monotone"
                        dataKey="amount"
                        stroke="#10b981"
                        fill="#10b981"
                        fillOpacity={0.3}
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </div>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="rules" className="mt-6">
            <div className="space-y-4">
              {reserveFunds.map((fund) => (
                <Card key={fund.id} className="p-4">
                  <h4 className="font-medium mb-4">{fund.name} Rules</h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Auto-funding</span>
                        <Badge variant={fund.rules.autoFunding ? 'default' : 'secondary'}>
                          {fund.rules.autoFunding ? 'Enabled' : 'Disabled'}
                        </Badge>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Funding Rate</span>
                        <span className="font-medium">{fund.rules.fundingPercentage}%</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Min Balance</span>
                        <span className="font-medium">{formatCurrency(fund.rules.minBalance)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Max Balance</span>
                        <span className="font-medium">{formatCurrency(fund.rules.maxBalance)}</span>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Approval Required</span>
                        <Badge variant={fund.rules.withdrawalRules.requiresApproval ? 'default' : 'secondary'}>
                          {fund.rules.withdrawalRules.requiresApproval ? 'Yes' : 'No'}
                        </Badge>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Approval Threshold</span>
                        <span className="font-medium">
                          {formatCurrency(fund.rules.withdrawalRules.approvalThreshold)}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Max Withdrawal</span>
                        <span className="font-medium">
                          {fund.rules.withdrawalRules.maxWithdrawalPercentage}%
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Eligible Categories</span>
                        <span className="font-medium">{fund.rules.eligibleCategories.length}</span>
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </Card>

      {/* Create Fund Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Create Reserve Fund</DialogTitle>
            <DialogDescription>
              Set up a reserve fund to protect against budget overruns and emergencies.
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Fund Name</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="e.g., Emergency Training Fund"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="percentage">Budget Percentage</Label>
                <Input
                  id="percentage"
                  type="number"
                  value={formData.percentage}
                  onChange={(e) => setFormData({ ...formData, percentage: parseFloat(e.target.value) || 0 })}
                  min={0}
                  max={100}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="targetAmount">Target Amount ($)</Label>
                <Input
                  id="targetAmount"
                  type="number"
                  value={formData.targetAmount}
                  onChange={(e) => setFormData({ ...formData, targetAmount: parseFloat(e.target.value) || 0 })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="fundingPercentage">Auto-funding Rate (%)</Label>
                <Input
                  id="fundingPercentage"
                  type="number"
                  value={formData.fundingPercentage}
                  onChange={(e) => setFormData({ ...formData, fundingPercentage: parseFloat(e.target.value) || 0 })}
                  min={0}
                  max={100}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="minBalance">Minimum Balance ($)</Label>
                <Input
                  id="minBalance"
                  type="number"
                  value={formData.minBalance}
                  onChange={(e) => setFormData({ ...formData, minBalance: parseFloat(e.target.value) || 0 })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="approvalThreshold">Approval Threshold ($)</Label>
                <Input
                  id="approvalThreshold"
                  type="number"
                  value={formData.approvalThreshold}
                  onChange={(e) => setFormData({ ...formData, approvalThreshold: parseFloat(e.target.value) || 0 })}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label>Eligible Categories</Label>
              <div className="grid grid-cols-2 gap-2">
                {(stats?.by_category || []).map((cat) => (
                  <label key={cat.category} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={formData.eligibleCategories.includes(cat.category)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setFormData({
                            ...formData,
                            eligibleCategories: [...formData.eligibleCategories, cat.category],
                          });
                        } else {
                          setFormData({
                            ...formData,
                            eligibleCategories: formData.eligibleCategories.filter(id => id !== cat.category),
                          });
                        }
                      }}
                    />
                    <span className="text-sm">{cat.category}</span>
                  </label>
                ))}
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateFund} disabled={!formData.name || formData.targetAmount <= 0}>
              Create Fund
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Transaction Dialog */}
      <Dialog open={showTransactionDialog} onOpenChange={setShowTransactionDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {transactionType === 'deposit' ? 'Deposit to' : 'Withdraw from'} {selectedFund?.name}
            </DialogTitle>
            <DialogDescription>
              {transactionType === 'deposit' 
                ? 'Add funds to the reserve'
                : 'Withdraw funds for approved emergency expenses'}
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="amount">Amount ($)</Label>
              <Input
                id="amount"
                type="number"
                value={transactionData.amount}
                onChange={(e) => setTransactionData({ ...transactionData, amount: parseFloat(e.target.value) || 0 })}
                min={0}
                max={transactionType === 'withdrawal' ? selectedFund?.currentAmount : undefined}
              />
              {transactionType === 'withdrawal' && selectedFund && (
                <p className="text-sm text-muted-foreground">
                  Available: {formatCurrency(selectedFund.currentAmount - selectedFund.rules.minBalance)}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={transactionData.description}
                onChange={(e) => setTransactionData({ ...transactionData, description: e.target.value })}
                placeholder="Reason for transaction..."
                rows={3}
              />
            </div>

            {transactionType === 'withdrawal' && (
              <div className="space-y-2">
                <Label htmlFor="category">Category</Label>
                <Select
                  value={transactionData.category}
                  onValueChange={(value) => setTransactionData({ ...transactionData, category: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {selectedFund?.rules.eligibleCategories.map((catId) => {
                      const category = stats?.by_category?.find(c => c.category === catId);
                      return category ? (
                        <SelectItem key={catId} value={catId}>
                          {category.category}
                        </SelectItem>
                      ) : null;
                    })}
                  </SelectContent>
                </Select>
              </div>
            )}

            {selectedFund && transactionData.amount > selectedFund.rules.withdrawalRules.approvalThreshold && (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  This transaction requires approval from authorized personnel.
                </AlertDescription>
              </Alert>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowTransactionDialog(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleTransaction}
              disabled={transactionData.amount <= 0 || !transactionData.description}
            >
              {transactionType === 'deposit' ? 'Deposit' : 'Withdraw'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </motion.div>
  );
};