import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Switch } from '@/components/ui/switch';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  LineChart,
  Line,
  Area,
  ComposedChart,
} from 'recharts';
import {
  RefreshCw,
  Plus,
  Settings,
  Calendar,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Info,
  DollarSign,
  Percent,
  Clock,
  Edit,
  Trash2,
  Copy,
} from 'lucide-react';
import { formatCurrency, formatPercentage } from '../../utils/formatters';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useBudget, useBudgetStats, useDepartmentAllocations } from '@/hooks/queries/useBudgetQueries';
import { toast } from 'sonner';

// Local types - different from planning.types
interface CarryoverRule {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  priority: number;
  type: 'automatic' | 'manual' | 'conditional';
  conditions: {
    minUnspentAmount?: number;
    minUnspentPercentage?: number;
    maxCarryoverAmount?: number;
    maxCarryoverPercentage?: number;
    categories?: string[]; // specific categories only
    departments?: string[]; // specific departments only
    timeframe?: 'quarterly' | 'yearly' | 'custom';
    customPeriodDays?: number;
    requiresApproval?: boolean;
    approvalThreshold?: number;
  };
  actions: {
    carryoverPercentage: number; // percentage of unspent to carry
    destinationType: 'same_category' | 'specific_category' | 'reserve_fund' | 'redistribute';
    destinationId?: string; // category/fund ID if specific
    redistributionRules?: {
      method: 'proportional' | 'equal' | 'priority';
      targets: Array<{ id: string; percentage: number }>;
    };
    expiryPeriod?: number; // days until carried amount expires
    restrictions?: {
      maxUsagePerPeriod?: number;
      allowedCategories?: string[];
      blackoutPeriods?: Array<{ start: string; end: string }>;
    };
  };
  history: CarryoverHistory[];
}

interface CarryoverHistory {
  id: string;
  ruleId: string;
  executedDate: string;
  periodStart: string;
  periodEnd: string;
  totalUnspent: number;
  amountCarried: number;
  destinations: Array<{
    type: string;
    id: string;
    name: string;
    amount: number;
  }>;
  status: 'completed' | 'pending_approval' | 'expired' | 'partially_used';
  usageTracking?: {
    amountUsed: number;
    remainingAmount: number;
    expiryDate?: string;
  };
}

interface CarryoverRulesProps {
  rules: CarryoverRule[];
  carryoverHistory: CarryoverHistory[];
  onCreateRule: (rule: Omit<CarryoverRule, 'id' | 'history'>) => void;
  onUpdateRule: (id: string, rule: Partial<CarryoverRule>) => void;
  onDeleteRule: (id: string) => void;
  onExecuteRule: (ruleId: string) => void;
  onSimulateCarryover: () => { totalUnspent: number; projectedCarryover: number; byCategory: Record<string, number> };
  loading?: boolean;
}

const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-popover border rounded-lg shadow-lg p-3">
        <p className="font-medium mb-2">{label}</p>
        {payload.map((entry: any, index: number) => (
          <div key={index} className="flex items-center justify-between gap-4 text-sm">
            <span className="flex items-center gap-2">
              <span
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: entry.color }}
              />
              {entry.name}:
            </span>
            <span className="font-medium">{formatCurrency(entry.value)}</span>
          </div>
        ))}
      </div>
    );
  }
  return null;
};

export const CarryoverRules: React.FC<CarryoverRulesProps> = ({
  rules,
  carryoverHistory,
  onCreateRule,
  onUpdateRule,
  onDeleteRule,
  onExecuteRule,
  onSimulateCarryover,
  loading = false,
}) => {
  const currentYear = new Date().getFullYear();
  const { data: budget } = useBudget(currentYear);
  const { data: stats } = useBudgetStats(currentYear);
  const { data: departmentAllocations = [] } = useDepartmentAllocations(currentYear);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [editingRule, setEditingRule] = useState<CarryoverRule | null>(null);
  const [showSimulation, setShowSimulation] = useState(false);
  const [simulationResults, setSimulationResults] = useState<any>(null);
  const [activeTab, setActiveTab] = useState('rules');

  const [formData, setFormData] = useState<{
    name: string;
    description: string;
    enabled: boolean;
    priority: number;
    type: 'automatic' | 'manual' | 'conditional';
    conditions: {
      minUnspentPercentage: number;
      maxCarryoverPercentage: number;
      categories: string[];
      departments: string[];
      timeframe: 'quarterly' | 'yearly' | 'custom';
      requiresApproval: boolean;
      approvalThreshold: number;
    };
    actions: {
      carryoverPercentage: number;
      destinationType: 'same_category' | 'specific_category' | 'reserve_fund' | 'redistribute';
      expiryPeriod: number;
    };
  }>({
    name: '',
    description: '',
    enabled: true,
    priority: 1,
    type: 'automatic',
    conditions: {
      minUnspentPercentage: 10,
      maxCarryoverPercentage: 50,
      categories: [],
      departments: [],
      timeframe: 'yearly',
      requiresApproval: false,
      approvalThreshold: 10000,
    },
    actions: {
      carryoverPercentage: 100,
      destinationType: 'same_category',
      expiryPeriod: 90,
    },
  });

  const handleCreateRule = () => {
    const ruleData: Omit<CarryoverRule, 'id' | 'history'> = {
      name: formData.name,
      description: formData.description,
      enabled: formData.enabled,
      priority: formData.priority,
      type: formData.type,
      conditions: formData.conditions,
      actions: formData.actions,
    };
    onCreateRule(ruleData);
    setShowCreateDialog(false);
    resetForm();
    toast.success('Carryover rule created successfully');
  };

  const handleUpdateRule = () => {
    if (!editingRule) return;
    const updateData: Partial<CarryoverRule> = {
      name: formData.name,
      description: formData.description,
      enabled: formData.enabled,
      priority: formData.priority,
      type: formData.type,
      conditions: formData.conditions,
      actions: formData.actions,
    };
    onUpdateRule(editingRule.id, updateData);
    setEditingRule(null);
    resetForm();
    toast.success('Rule updated successfully');
  };

  const handleSimulate = () => {
    const results = onSimulateCarryover();
    setSimulationResults(results);
    setShowSimulation(true);
  };

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      enabled: true,
      priority: 1,
      type: 'automatic',
      conditions: {
        minUnspentPercentage: 10,
        maxCarryoverPercentage: 50,
        categories: [],
        departments: [],
        timeframe: 'yearly',
        requiresApproval: false,
        approvalThreshold: 10000,
      },
      actions: {
        carryoverPercentage: 100,
        destinationType: 'same_category',
        expiryPeriod: 90,
      },
    });
  };

  // Calculate carryover statistics
  const totalCarried = carryoverHistory
    .filter(h => h.status === 'completed')
    .reduce((sum, h) => sum + h.amountCarried, 0);

  const activeCarryovers = carryoverHistory.filter(
    h => h.status === 'completed' && h.usageTracking && h.usageTracking.remainingAmount > 0
  );

  const totalAvailable = activeCarryovers.reduce(
    (sum, h) => sum + (h.usageTracking?.remainingAmount || 0), 0
  );

  // Prepare data for charts
  const carryoverTrend = carryoverHistory
    .filter(h => h.status === 'completed')
    .map(h => ({
      period: new Date(h.periodEnd).toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
      unspent: h.totalUnspent,
      carried: h.amountCarried,
      percentage: (h.amountCarried / h.totalUnspent) * 100,
    }))
    .slice(-12); // Last 12 periods

  const categoryBreakdown = Object.entries(
    carryoverHistory
      .filter(h => h.status === 'completed')
      .flatMap(h => h.destinations)
      .reduce((acc, dest) => {
        acc[dest.name] = (acc[dest.name] || 0) + dest.amount;
        return acc;
      }, {} as Record<string, number>)
  ).map(([name, amount]) => ({ name, amount }));

  if (loading) {
    return (
      <Card className="p-6">
        <Skeleton className="h-6 w-48 mb-4" />
        <Skeleton className="h-64 w-full" />
      </Card>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
    >
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <RefreshCw className="h-5 w-5 text-muted-foreground" />
            <h3 className="text-lg font-semibold">Carryover Rules Engine</h3>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleSimulate}
            >
              <TrendingUp className="h-4 w-4 mr-2" />
              Simulate
            </Button>
            <Button
              variant="default"
              size="sm"
              onClick={() => setShowCreateDialog(true)}
            >
              <Plus className="h-4 w-4 mr-2" />
              Create Rule
            </Button>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Card className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Carried</p>
                <p className="text-2xl font-bold">{formatCurrency(totalCarried)}</p>
              </div>
              <RefreshCw className="h-8 w-8 text-muted-foreground opacity-50" />
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Available Now</p>
                <p className="text-2xl font-bold text-green-600">{formatCurrency(totalAvailable)}</p>
              </div>
              <DollarSign className="h-8 w-8 text-green-600 opacity-50" />
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Active Rules</p>
                <p className="text-2xl font-bold">{rules.filter(r => r.enabled).length}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-muted-foreground opacity-50" />
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Expiring Soon</p>
                <p className="text-2xl font-bold text-orange-600">
                  {activeCarryovers.filter(h => {
                    if (!h.usageTracking?.expiryDate) return false;
                    const daysUntilExpiry = Math.ceil(
                      (new Date(h.usageTracking.expiryDate).getTime() - Date.now()) / (1000 * 60 * 60 * 24)
                    );
                    return daysUntilExpiry <= 30;
                  }).length}
                </p>
              </div>
              <Clock className="h-8 w-8 text-orange-600 opacity-50" />
            </div>
          </Card>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="rules">Rules</TabsTrigger>
            <TabsTrigger value="history">History</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="active">Active Carryovers</TabsTrigger>
          </TabsList>

          <TabsContent value="rules" className="mt-6 space-y-4">
            {rules.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <RefreshCw className="h-12 w-12 mx-auto mb-3 opacity-50" />
                <p>No carryover rules defined yet</p>
                <p className="text-sm mt-1">Create rules to automatically handle unused budget</p>
              </div>
            ) : (
              <Accordion type="single" collapsible className="w-full space-y-2">
                {rules.map((rule) => (
                  <AccordionItem key={rule.id} value={rule.id} className="border rounded-lg">
                    <AccordionTrigger className="px-4 hover:no-underline">
                      <div className="flex items-center justify-between w-full pr-4">
                        <div className="flex items-center gap-3">
                          <Switch
                            checked={rule.enabled}
                            onCheckedChange={(checked) => onUpdateRule(rule.id, { enabled: checked })}
                            onClick={(e) => e.stopPropagation()}
                          />
                          <div className="text-left">
                            <div className="flex items-center gap-2">
                              <span className="font-medium">{rule.name}</span>
                              <Badge variant={rule.type === 'automatic' ? 'default' : 'secondary'}>
                                {rule.type}
                              </Badge>
                              <Badge variant="outline">Priority {rule.priority}</Badge>
                            </div>
                            <p className="text-sm text-muted-foreground">{rule.description}</p>
                          </div>
                        </div>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent className="px-4 pb-4">
                      <div className="space-y-4">
                        {/* Conditions */}
                        <div>
                          <h5 className="font-medium mb-2">Conditions</h5>
                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div className="space-y-1">
                              <div className="flex justify-between">
                                <span className="text-muted-foreground">Min Unspent</span>
                                <span>{rule.conditions.minUnspentPercentage}%</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-muted-foreground">Max Carryover</span>
                                <span>{rule.conditions.maxCarryoverPercentage}%</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-muted-foreground">Timeframe</span>
                                <span className="capitalize">{rule.conditions.timeframe}</span>
                              </div>
                            </div>
                            <div className="space-y-1">
                              <div className="flex justify-between">
                                <span className="text-muted-foreground">Approval Required</span>
                                <Badge variant={rule.conditions.requiresApproval ? 'default' : 'secondary'}>
                                  {rule.conditions.requiresApproval ? 'Yes' : 'No'}
                                </Badge>
                              </div>
                              {rule.conditions.categories && rule.conditions.categories.length > 0 && (
                                <div>
                                  <span className="text-muted-foreground">Categories</span>
                                  <div className="flex flex-wrap gap-1 mt-1">
                                    {rule.conditions.categories.map(catId => {
                                      const cat = stats?.by_category?.find(c => c.category === catId);
                                      return cat ? (
                                        <Badge key={catId} variant="outline" className="text-xs">
                                          {cat.category}
                                        </Badge>
                                      ) : null;
                                    })}
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>

                        {/* Actions */}
                        <div>
                          <h5 className="font-medium mb-2">Actions</h5>
                          <div className="space-y-2 text-sm">
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">Carryover Amount</span>
                              <span>{rule.actions.carryoverPercentage}% of unspent</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">Destination</span>
                              <Badge variant="outline">
                                {rule.actions.destinationType.replace(/_/g, ' ')}
                              </Badge>
                            </div>
                            {rule.actions.expiryPeriod && (
                              <div className="flex justify-between">
                                <span className="text-muted-foreground">Expiry Period</span>
                                <span>{rule.actions.expiryPeriod} days</span>
                              </div>
                            )}
                          </div>
                        </div>

                        {/* Actions */}
                        <div className="flex justify-end gap-2 pt-2 border-t">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setEditingRule(rule);
                              setFormData({
                                name: rule.name,
                                description: rule.description,
                                enabled: rule.enabled,
                                priority: rule.priority,
                                type: rule.type,
                                conditions: {
                                  minUnspentPercentage: rule.conditions.minUnspentPercentage || 10,
                                  maxCarryoverPercentage: rule.conditions.maxCarryoverPercentage || 50,
                                  categories: rule.conditions.categories || [],
                                  departments: rule.conditions.departments || [],
                                  timeframe: rule.conditions.timeframe || 'yearly',
                                  requiresApproval: rule.conditions.requiresApproval || false,
                                  approvalThreshold: rule.conditions.approvalThreshold || 10000,
                                },
                                actions: {
                                  carryoverPercentage: rule.actions.carryoverPercentage,
                                  destinationType: rule.actions.destinationType,
                                  expiryPeriod: rule.actions.expiryPeriod || 90,
                                },
                              });
                              setShowCreateDialog(true);
                            }}
                          >
                            <Edit className="h-4 w-4 mr-1" />
                            Edit
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onExecuteRule(rule.id)}
                            disabled={!rule.enabled}
                          >
                            <RefreshCw className="h-4 w-4 mr-1" />
                            Execute
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-destructive"
                            onClick={() => onDeleteRule(rule.id)}
                          >
                            <Trash2 className="h-4 w-4 mr-1" />
                            Delete
                          </Button>
                        </div>
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            )}
          </TabsContent>

          <TabsContent value="history" className="mt-6">
            <Card className="p-4">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Period</TableHead>
                    <TableHead>Rule</TableHead>
                    <TableHead>Unspent</TableHead>
                    <TableHead>Carried</TableHead>
                    <TableHead>Destination</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {carryoverHistory.map((history) => {
                    const rule = rules.find(r => r.id === history.ruleId);
                    return (
                      <TableRow key={history.id}>
                        <TableCell>
                          {new Date(history.periodEnd).toLocaleDateString('en-US', { 
                            month: 'short', 
                            year: 'numeric' 
                          })}
                        </TableCell>
                        <TableCell>{rule?.name || 'Unknown'}</TableCell>
                        <TableCell>{formatCurrency(history.totalUnspent)}</TableCell>
                        <TableCell className="font-medium text-green-600">
                          {formatCurrency(history.amountCarried)}
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-wrap gap-1">
                            {history.destinations.slice(0, 2).map((dest, idx) => (
                              <Badge key={idx} variant="outline" className="text-xs">
                                {dest.name}
                              </Badge>
                            ))}
                            {history.destinations.length > 2 && (
                              <Badge variant="outline" className="text-xs">
                                +{history.destinations.length - 2} more
                              </Badge>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge 
                            variant={
                              history.status === 'completed' ? 'default' :
                              history.status === 'pending_approval' ? 'outline' :
                              history.status === 'expired' ? 'destructive' :
                              'secondary'
                            }
                          >
                            {history.status.replace(/_/g, ' ')}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </Card>
          </TabsContent>

          <TabsContent value="analytics" className="mt-6 space-y-4">
            {/* Carryover Trend */}
            <Card className="p-4">
              <h4 className="font-medium mb-4">Carryover Trend</h4>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <ComposedChart data={carryoverTrend}>
                    <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
                    <XAxis 
                      dataKey="period" 
                      className="text-xs"
                      tick={{ fill: 'currentColor' }}
                    />
                    <YAxis 
                      yAxisId="left"
                      className="text-xs"
                      tick={{ fill: 'currentColor' }}
                      tickFormatter={(value) => `${(value / 1000).toFixed(0)}k`}
                    />
                    <YAxis 
                      yAxisId="right"
                      orientation="right"
                      className="text-xs"
                      tick={{ fill: 'currentColor' }}
                      tickFormatter={(value) => `${value}%`}
                    />
                    <Tooltip content={<CustomTooltip />} />
                    <Legend />
                    <Bar yAxisId="left" dataKey="unspent" fill="#94a3b8" name="Unspent" />
                    <Bar yAxisId="left" dataKey="carried" fill="#10b981" name="Carried" />
                    <Line 
                      yAxisId="right" 
                      type="monotone" 
                      dataKey="percentage" 
                      stroke="#3b82f6" 
                      strokeWidth={2}
                      dot={{ r: 4 }}
                      name="Carry %"
                    />
                  </ComposedChart>
                </ResponsiveContainer>
              </div>
            </Card>

            {/* Category Breakdown */}
            <Card className="p-4">
              <h4 className="font-medium mb-4">Carryover by Category</h4>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={categoryBreakdown}>
                    <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
                    <XAxis 
                      dataKey="name" 
                      className="text-xs"
                      tick={{ fill: 'currentColor' }}
                      angle={-45}
                      textAnchor="end"
                      height={80}
                    />
                    <YAxis 
                      className="text-xs"
                      tick={{ fill: 'currentColor' }}
                      tickFormatter={(value) => `${(value / 1000).toFixed(0)}k`}
                    />
                    <Tooltip content={<CustomTooltip />} />
                    <Bar dataKey="amount" fill="#3b82f6" radius={[8, 8, 0, 0]} />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </Card>
          </TabsContent>

          <TabsContent value="active" className="mt-6">
            {activeCarryovers.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <DollarSign className="h-12 w-12 mx-auto mb-3 opacity-50" />
                <p>No active carryover amounts</p>
                <p className="text-sm mt-1">Carried amounts will appear here when available</p>
              </div>
            ) : (
              <div className="space-y-3">
                {activeCarryovers.map((history) => {
                  const daysUntilExpiry = history.usageTracking?.expiryDate
                    ? Math.ceil((new Date(history.usageTracking.expiryDate).getTime() - Date.now()) / (1000 * 60 * 60 * 24))
                    : null;
                  
                  return (
                    <Card key={history.id} className="p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h5 className="font-medium">
                              {new Date(history.periodEnd).toLocaleDateString('en-US', { 
                                month: 'long', 
                                year: 'numeric' 
                              })} Carryover
                            </h5>
                            {daysUntilExpiry !== null && daysUntilExpiry <= 30 && (
                              <Badge variant="destructive">
                                Expires in {daysUntilExpiry} days
                              </Badge>
                            )}
                          </div>
                          
                          <div className="grid grid-cols-3 gap-4 text-sm">
                            <div>
                              <p className="text-muted-foreground">Original Amount</p>
                              <p className="font-medium">{formatCurrency(history.amountCarried)}</p>
                            </div>
                            <div>
                              <p className="text-muted-foreground">Used</p>
                              <p className="font-medium text-orange-600">
                                {formatCurrency(history.usageTracking?.amountUsed || 0)}
                              </p>
                            </div>
                            <div>
                              <p className="text-muted-foreground">Remaining</p>
                              <p className="font-medium text-green-600">
                                {formatCurrency(history.usageTracking?.remainingAmount || 0)}
                              </p>
                            </div>
                          </div>

                          <div className="mt-3">
                            <p className="text-sm text-muted-foreground mb-1">Available in:</p>
                            <div className="flex flex-wrap gap-1">
                              {history.destinations.map((dest, idx) => (
                                <Badge key={idx} variant="outline" className="text-xs">
                                  {dest.name}: {formatCurrency(dest.amount)}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        </div>
                      </div>
                    </Card>
                  );
                })}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </Card>

      {/* Create/Edit Rule Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={(open) => {
        setShowCreateDialog(open);
        if (!open) {
          setEditingRule(null);
          resetForm();
        }
      }}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>{editingRule ? 'Edit' : 'Create'} Carryover Rule</DialogTitle>
            <DialogDescription>
              Define how unused budget should be carried forward to future periods.
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Rule Name</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="e.g., Annual Training Carryover"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="type">Rule Type</Label>
                <Select
                  value={formData.type}
                  onValueChange={(value: any) => setFormData({ ...formData, type: value })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="automatic">Automatic</SelectItem>
                    <SelectItem value="manual">Manual</SelectItem>
                    <SelectItem value="conditional">Conditional</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Describe when and how this rule should apply..."
                rows={2}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="priority">Priority (1-10)</Label>
                <Input
                  id="priority"
                  type="number"
                  value={formData.priority}
                  onChange={(e) => setFormData({ ...formData, priority: parseInt(e.target.value) || 1 })}
                  min={1}
                  max={10}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="timeframe">Timeframe</Label>
                <Select
                  value={formData.conditions.timeframe}
                  onValueChange={(value: any) => setFormData({
                    ...formData,
                    conditions: { ...formData.conditions, timeframe: value }
                  })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="quarterly">Quarterly</SelectItem>
                    <SelectItem value="yearly">Yearly</SelectItem>
                    <SelectItem value="custom">Custom</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Conditions */}
            <div className="space-y-3">
              <h4 className="font-medium">Conditions</h4>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="minUnspent">Min Unspent %</Label>
                  <Input
                    id="minUnspent"
                    type="number"
                    value={formData.conditions.minUnspentPercentage}
                    onChange={(e) => setFormData({
                      ...formData,
                      conditions: { ...formData.conditions, minUnspentPercentage: parseFloat(e.target.value) || 0 }
                    })}
                    min={0}
                    max={100}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="maxCarryover">Max Carryover %</Label>
                  <Input
                    id="maxCarryover"
                    type="number"
                    value={formData.conditions.maxCarryoverPercentage}
                    onChange={(e) => setFormData({
                      ...formData,
                      conditions: { ...formData.conditions, maxCarryoverPercentage: parseFloat(e.target.value) || 0 }
                    })}
                    min={0}
                    max={100}
                  />
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="space-y-3">
              <h4 className="font-medium">Actions</h4>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="carryoverPercentage">Carryover % of Unspent</Label>
                  <Input
                    id="carryoverPercentage"
                    type="number"
                    value={formData.actions.carryoverPercentage}
                    onChange={(e) => setFormData({
                      ...formData,
                      actions: { ...formData.actions, carryoverPercentage: parseFloat(e.target.value) || 0 }
                    })}
                    min={0}
                    max={100}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="destination">Destination</Label>
                  <Select
                    value={formData.actions.destinationType}
                    onValueChange={(value: any) => setFormData({
                      ...formData,
                      actions: { ...formData.actions, destinationType: value }
                    })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="same_category">Same Category</SelectItem>
                      <SelectItem value="specific_category">Specific Category</SelectItem>
                      <SelectItem value="reserve_fund">Reserve Fund</SelectItem>
                      <SelectItem value="redistribute">Redistribute</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="expiryPeriod">Expiry Period (days)</Label>
                <Input
                  id="expiryPeriod"
                  type="number"
                  value={formData.actions.expiryPeriod}
                  onChange={(e) => setFormData({
                    ...formData,
                    actions: { ...formData.actions, expiryPeriod: parseInt(e.target.value) || 0 }
                  })}
                  min={0}
                />
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
              Cancel
            </Button>
            <Button onClick={editingRule ? handleUpdateRule : handleCreateRule} disabled={!formData.name}>
              {editingRule ? 'Update' : 'Create'} Rule
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Simulation Dialog */}
      <Dialog open={showSimulation} onOpenChange={setShowSimulation}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Carryover Simulation</DialogTitle>
            <DialogDescription>
              Preview how carryover rules would apply to current unspent budget.
            </DialogDescription>
          </DialogHeader>

          {simulationResults && (
            <div className="space-y-4 py-4">
              <Alert>
                <Info className="h-4 w-4" />
                <AlertTitle>Simulation Results</AlertTitle>
                <AlertDescription>
                  Based on current rules and unspent budget amounts
                </AlertDescription>
              </Alert>

              <div className="grid grid-cols-2 gap-4">
                <Card className="p-4">
                  <p className="text-sm text-muted-foreground">Total Unspent</p>
                  <p className="text-2xl font-bold">{formatCurrency(simulationResults.totalUnspent)}</p>
                </Card>
                <Card className="p-4">
                  <p className="text-sm text-muted-foreground">Projected Carryover</p>
                  <p className="text-2xl font-bold text-green-600">
                    {formatCurrency(simulationResults.projectedCarryover)}
                  </p>
                </Card>
              </div>

              <Card className="p-4">
                <h4 className="font-medium mb-3">Carryover by Category</h4>
                <div className="space-y-2">
                  {Object.entries(simulationResults.byCategory).map(([catId, amount]) => {
                    const category = stats?.by_category?.find(c => c.category === catId);
                    if (!category) return null;
                    return (
                      <div key={catId} className="flex justify-between items-center">
                        <span className="text-sm">{category.category}</span>
                        <span className="font-medium">{formatCurrency(amount as number)}</span>
                      </div>
                    );
                  })}
                </div>
              </Card>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowSimulation(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </motion.div>
  );
};