import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { BudgetCalendar } from './BudgetCalendar';
import type { BudgetEvent } from './BudgetCalendar';

describe('BudgetCalendar', () => {
  const mockEvents: BudgetEvent[] = [
    {
      id: '1',
      date: new Date().toISOString(),
      type: 'expense',
      title: 'Training Materials',
      amount: 500,
      status: 'completed',
      department: 'Engineering',
      category: 'Materials'
    },
    {
      id: '2',
      date: new Date().toISOString(),
      type: 'deadline',
      title: 'Q1 Budget Review',
      status: 'pending'
    }
  ];

  it('renders calendar with events', () => {
    render(
      <BudgetCalendar
        events={mockEvents}
        loading={false}
      />
    );

    // Check if calendar header is rendered
    expect(screen.getByText('Budget Calendar')).toBeInTheDocument();
    
    // Check if month navigation buttons are rendered
    expect(screen.getByText('Today')).toBeInTheDocument();
    
    // Check if summary cards are rendered
    expect(screen.getByText('Total Expenses')).toBeInTheDocument();
    expect(screen.getByText('Deadlines')).toBeInTheDocument();
  });

  it('handles month navigation', () => {
    render(
      <BudgetCalendar
        events={mockEvents}
        loading={false}
      />
    );

    const nextButton = screen.getByRole('button', { name: /next/i });
    const prevButton = screen.getByRole('button', { name: /previous/i });
    
    fireEvent.click(nextButton);
    fireEvent.click(prevButton);
    
    // Should still render without errors
    expect(screen.getByText('Budget Calendar')).toBeInTheDocument();
  });

  it('displays loading state', () => {
    render(
      <BudgetCalendar
        events={[]}
        loading={true}
      />
    );

    // Should show skeleton loaders
    const skeletons = screen.getAllByTestId('skeleton');
    expect(skeletons.length).toBeGreaterThan(0);
  });

  it('handles event click', () => {
    const handleEventClick = jest.fn();
    
    render(
      <BudgetCalendar
        events={mockEvents}
        onEventClick={handleEventClick}
        loading={false}
      />
    );

    // Click on an event
    const event = screen.getByText('Training Materials');
    fireEvent.click(event);
    
    expect(handleEventClick).toHaveBeenCalledWith(mockEvents[0]);
  });

  it('displays event type legend', () => {
    render(
      <BudgetCalendar
        events={mockEvents}
        loading={false}
      />
    );

    // Check if legend items are displayed
    expect(screen.getByText('Expense')).toBeInTheDocument();
    expect(screen.getByText('Deadline')).toBeInTheDocument();
    expect(screen.getByText('Report')).toBeInTheDocument();
    expect(screen.getByText('Alert')).toBeInTheDocument();
    expect(screen.getByText('Milestone')).toBeInTheDocument();
  });
});