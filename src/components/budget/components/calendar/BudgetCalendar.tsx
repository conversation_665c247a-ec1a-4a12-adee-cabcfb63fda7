import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { 
  Calendar as CalendarIcon,
  ChevronLeft,
  ChevronRight,
  DollarSign,
  FileText,
  Bell,
  TrendingUp,
  Clock,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import { 
  format, 
  startOfMonth, 
  endOfMonth, 
  eachDayOfInterval,
  isSameMonth,
  isSameDay,
  isToday,
  addMonths,
  subMonths,
  startOfWeek,
  endOfWeek,
  getDay,
  parseISO
} from 'date-fns';
import { cn } from '@/lib/utils';
import { formatCurrency } from '../../utils/formatters';

export interface BudgetEvent {
  id: string;
  date: string;
  type: 'expense' | 'deadline' | 'report' | 'alert' | 'milestone';
  title: string;
  description?: string;
  amount?: number;
  status?: 'completed' | 'pending' | 'overdue' | 'cancelled';
  priority?: 'high' | 'medium' | 'low';
  department?: string;
  category?: string;
  recurring?: {
    frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
    endDate?: string;
  };
}

interface BudgetCalendarProps {
  events: BudgetEvent[];
  onEventClick?: (event: BudgetEvent) => void;
  onAddEvent?: (date: Date) => void;
  onDateClick?: (date: Date, events: BudgetEvent[]) => void;
  currentMonth?: Date;
  showWeekends?: boolean;
  compactMode?: boolean;
  loading?: boolean;
}

const eventTypeConfig = {
  expense: {
    icon: <DollarSign className="h-3 w-3" />,
    color: 'bg-blue-500',
    label: 'Expense'
  },
  deadline: {
    icon: <Clock className="h-3 w-3" />,
    color: 'bg-orange-500',
    label: 'Deadline'
  },
  report: {
    icon: <FileText className="h-3 w-3" />,
    color: 'bg-purple-500',
    label: 'Report'
  },
  alert: {
    icon: <Bell className="h-3 w-3" />,
    color: 'bg-red-500',
    label: 'Alert'
  },
  milestone: {
    icon: <TrendingUp className="h-3 w-3" />,
    color: 'bg-green-500',
    label: 'Milestone'
  }
};

const statusConfig = {
  completed: {
    icon: <CheckCircle className="h-3 w-3" />,
    color: 'text-green-500'
  },
  pending: {
    icon: <Clock className="h-3 w-3" />,
    color: 'text-blue-500'
  },
  overdue: {
    icon: <AlertTriangle className="h-3 w-3" />,
    color: 'text-red-500'
  },
  cancelled: {
    icon: <AlertTriangle className="h-3 w-3" />,
    color: 'text-gray-500'
  }
};

export const BudgetCalendar: React.FC<BudgetCalendarProps> = ({
  events,
  onEventClick,
  onAddEvent,
  onDateClick,
  currentMonth: initialMonth = new Date(),
  showWeekends = true,
  compactMode = false,
  loading = false
}) => {
  const [currentMonth, setCurrentMonth] = useState(initialMonth);
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [selectedEvent, setSelectedEvent] = useState<BudgetEvent | null>(null);
  const [showEventDialog, setShowEventDialog] = useState(false);

  // Calculate calendar days including previous and next month padding
  const calendarDays = useMemo(() => {
    const start = startOfMonth(currentMonth);
    const end = endOfMonth(currentMonth);
    const startWeek = startOfWeek(start);
    const endWeek = endOfWeek(end);
    
    return eachDayOfInterval({ start: startWeek, end: endWeek });
  }, [currentMonth]);

  // Group events by date
  const eventsByDate = useMemo(() => {
    const grouped: Record<string, BudgetEvent[]> = {};
    
    events.forEach(event => {
      const dateKey = format(parseISO(event.date), 'yyyy-MM-dd');
      if (!grouped[dateKey]) {
        grouped[dateKey] = [];
      }
      grouped[dateKey].push(event);
    });
    
    return grouped;
  }, [events]);

  // Calculate summary statistics for the month
  const monthStats = useMemo(() => {
    const monthEvents = events.filter(event => 
      isSameMonth(parseISO(event.date), currentMonth)
    );
    
    const totalExpenses = monthEvents
      .filter(e => e.type === 'expense' && e.amount)
      .reduce((sum, e) => sum + (e.amount || 0), 0);
    
    const upcomingDeadlines = monthEvents
      .filter(e => e.type === 'deadline' && e.status === 'pending')
      .length;
    
    const completedTasks = monthEvents
      .filter(e => e.status === 'completed')
      .length;
    
    return {
      totalExpenses,
      upcomingDeadlines,
      completedTasks,
      totalEvents: monthEvents.length
    };
  }, [events, currentMonth]);

  const handlePreviousMonth = () => {
    setCurrentMonth(prev => subMonths(prev, 1));
  };

  const handleNextMonth = () => {
    setCurrentMonth(prev => addMonths(prev, 1));
  };

  const handleToday = () => {
    setCurrentMonth(new Date());
  };

  const handleDateClick = (date: Date) => {
    const dateKey = format(date, 'yyyy-MM-dd');
    const dateEvents = eventsByDate[dateKey] || [];
    
    setSelectedDate(date);
    
    if (onDateClick) {
      onDateClick(date, dateEvents);
    }
    
    if (dateEvents.length === 1) {
      handleEventClick(dateEvents[0]);
    } else if (dateEvents.length > 1) {
      // Show a list of events for that day
      setShowEventDialog(true);
    } else if (onAddEvent) {
      // No events, allow adding
      onAddEvent(date);
    }
  };

  const handleEventClick = (event: BudgetEvent, e?: React.MouseEvent) => {
    e?.stopPropagation();
    setSelectedEvent(event);
    setShowEventDialog(true);
    
    if (onEventClick) {
      onEventClick(event);
    }
  };

  const renderCalendarDay = (date: Date) => {
    const dateKey = format(date, 'yyyy-MM-dd');
    const dayEvents = eventsByDate[dateKey] || [];
    const isCurrentMonth = isSameMonth(date, currentMonth);
    const isWeekend = !showWeekends && (getDay(date) === 0 || getDay(date) === 6);
    
    if (isWeekend) return null;
    
    return (
      <motion.div
        key={dateKey}
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.2 }}
        className={cn(
          "relative min-h-[80px] p-2 border rounded-lg cursor-pointer transition-all",
          isCurrentMonth ? "bg-card hover:bg-muted" : "bg-muted/30",
          isToday(date) && "ring-2 ring-primary",
          selectedDate && isSameDay(date, selectedDate) && "bg-primary/10"
        )}
        onClick={() => handleDateClick(date)}
      >
        {/* Date Number */}
        <div className="flex justify-between items-start mb-1">
          <span className={cn(
            "text-sm font-medium",
            !isCurrentMonth && "text-muted-foreground",
            isToday(date) && "text-primary"
          )}>
            {format(date, 'd')}
          </span>
          {dayEvents.length > 0 && (
            <Badge variant="secondary" className="text-xs">
              {dayEvents.length}
            </Badge>
          )}
        </div>

        {/* Events */}
        {!compactMode && (
          <div className="space-y-1">
            {dayEvents.slice(0, 3).map((event) => (
              <TooltipProvider key={event.id}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div
                      className={cn(
                        "flex items-center gap-1 p-1 rounded text-xs cursor-pointer",
                        "hover:bg-muted transition-colors",
                        eventTypeConfig[event.type].color,
                        "bg-opacity-20"
                      )}
                      onClick={(e) => handleEventClick(event, e)}
                    >
                      {eventTypeConfig[event.type].icon}
                      <span className="truncate flex-1">{event.title}</span>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="font-medium">{event.title}</p>
                    {event.amount && (
                      <p className="text-sm">{formatCurrency(event.amount)}</p>
                    )}
                    {event.description && (
                      <p className="text-sm text-muted-foreground">{event.description}</p>
                    )}
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            ))}
            {dayEvents.length > 3 && (
              <div className="text-xs text-muted-foreground text-center">
                +{dayEvents.length - 3} more
              </div>
            )}
          </div>
        )}

        {/* Compact Mode - Just show dots */}
        {compactMode && dayEvents.length > 0 && (
          <div className="flex gap-1 justify-center mt-1">
            {dayEvents.slice(0, 3).map((event) => (
              <div
                key={event.id}
                className={cn(
                  "w-2 h-2 rounded-full",
                  eventTypeConfig[event.type].color
                )}
              />
            ))}
            {dayEvents.length > 3 && (
              <div className="w-2 h-2 rounded-full bg-muted-foreground" />
            )}
          </div>
        )}
      </motion.div>
    );
  };

  if (loading) {
    return (
      <Card className="p-6">
        <Skeleton className="h-8 w-48 mb-4" />
        <div className="grid grid-cols-7 gap-2">
          {Array.from({ length: 35 }).map((_, i) => (
            <Skeleton key={i} className="h-20 w-full" />
          ))}
        </div>
      </Card>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
    >
      <Card className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <CalendarIcon className="h-5 w-5 text-muted-foreground" />
            <h3 className="text-lg font-semibold">Budget Calendar</h3>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={handlePreviousMonth}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleToday}
            >
              Today
            </Button>
            <h4 className="font-medium w-32 text-center">
              {format(currentMonth, 'MMMM yyyy')}
            </h4>
            <Button
              variant="ghost"
              size="icon"
              onClick={handleNextMonth}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Month Summary */}
        <div className="grid grid-cols-4 gap-4 mb-6">
          <Card className="p-3">
            <div className="flex items-center gap-2">
              <DollarSign className="h-4 w-4 text-blue-500" />
              <div>
                <p className="text-xs text-muted-foreground">Total Expenses</p>
                <p className="font-medium">{formatCurrency(monthStats.totalExpenses)}</p>
              </div>
            </div>
          </Card>
          <Card className="p-3">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-orange-500" />
              <div>
                <p className="text-xs text-muted-foreground">Deadlines</p>
                <p className="font-medium">{monthStats.upcomingDeadlines}</p>
              </div>
            </div>
          </Card>
          <Card className="p-3">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <div>
                <p className="text-xs text-muted-foreground">Completed</p>
                <p className="font-medium">{monthStats.completedTasks}</p>
              </div>
            </div>
          </Card>
          <Card className="p-3">
            <div className="flex items-center gap-2">
              <CalendarIcon className="h-4 w-4 text-purple-500" />
              <div>
                <p className="text-xs text-muted-foreground">Total Events</p>
                <p className="font-medium">{monthStats.totalEvents}</p>
              </div>
            </div>
          </Card>
        </div>

        {/* Weekday Headers */}
        <div className={cn(
          "grid gap-2 mb-2",
          showWeekends ? "grid-cols-7" : "grid-cols-5"
        )}>
          {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']
            .filter((_, index) => showWeekends || (index !== 0 && index !== 6))
            .map((day) => (
              <div key={day} className="text-sm font-medium text-center text-muted-foreground">
                {day}
              </div>
            ))}
        </div>

        {/* Calendar Grid */}
        <div className={cn(
          "grid gap-2",
          showWeekends ? "grid-cols-7" : "grid-cols-5"
        )}>
          {calendarDays.map(renderCalendarDay)}
        </div>

        {/* Legend */}
        <div className="mt-6 flex flex-wrap gap-4 text-sm">
          {Object.entries(eventTypeConfig).map(([type, config]) => (
            <div key={type} className="flex items-center gap-2">
              <div className={cn("w-3 h-3 rounded", config.color)} />
              <span className="text-muted-foreground">{config.label}</span>
            </div>
          ))}
        </div>
      </Card>

      {/* Event Details Dialog */}
      <Dialog open={showEventDialog} onOpenChange={setShowEventDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {selectedEvent ? (
                <div className="flex items-center gap-2">
                  {eventTypeConfig[selectedEvent.type].icon}
                  {selectedEvent.title}
                </div>
              ) : selectedDate ? (
                `Events for ${format(selectedDate, 'MMMM d, yyyy')}`
              ) : (
                'Event Details'
              )}
            </DialogTitle>
            <DialogDescription>
              {selectedEvent?.description || 'View and manage budget events'}
            </DialogDescription>
          </DialogHeader>

          {selectedEvent ? (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Badge>{eventTypeConfig[selectedEvent.type].label}</Badge>
                {selectedEvent.status && (
                  <div className={cn("flex items-center gap-1", statusConfig[selectedEvent.status].color)}>
                    {statusConfig[selectedEvent.status].icon}
                    <span className="text-sm capitalize">{selectedEvent.status}</span>
                  </div>
                )}
              </div>

              {selectedEvent.amount && (
                <div>
                  <p className="text-sm text-muted-foreground">Amount</p>
                  <p className="text-lg font-semibold">{formatCurrency(selectedEvent.amount)}</p>
                </div>
              )}

              {selectedEvent.department && (
                <div>
                  <p className="text-sm text-muted-foreground">Department</p>
                  <p>{selectedEvent.department}</p>
                </div>
              )}

              {selectedEvent.category && (
                <div>
                  <p className="text-sm text-muted-foreground">Category</p>
                  <p>{selectedEvent.category}</p>
                </div>
              )}

              <div>
                <p className="text-sm text-muted-foreground">Date</p>
                <p>{format(parseISO(selectedEvent.date), 'MMMM d, yyyy')}</p>
              </div>

              {selectedEvent.recurring && (
                <div>
                  <p className="text-sm text-muted-foreground">Recurring</p>
                  <p className="capitalize">{selectedEvent.recurring.frequency}</p>
                </div>
              )}
            </div>
          ) : selectedDate && eventsByDate[format(selectedDate, 'yyyy-MM-dd')]?.length > 0 ? (
            <div className="space-y-2">
              {eventsByDate[format(selectedDate, 'yyyy-MM-dd')].map((event) => (
                <Card
                  key={event.id}
                  className="p-3 cursor-pointer hover:bg-muted"
                  onClick={() => {
                    setSelectedEvent(event);
                  }}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {eventTypeConfig[event.type].icon}
                      <div>
                        <p className="font-medium">{event.title}</p>
                        {event.amount && (
                          <p className="text-sm text-muted-foreground">
                            {formatCurrency(event.amount)}
                          </p>
                        )}
                      </div>
                    </div>
                    <Badge variant="secondary">{eventTypeConfig[event.type].label}</Badge>
                  </div>
                </Card>
              ))}
            </div>
          ) : (
            <p className="text-center text-muted-foreground py-8">
              No events for this date
            </p>
          )}
        </DialogContent>
      </Dialog>
    </motion.div>
  );
};