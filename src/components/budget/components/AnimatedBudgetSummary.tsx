import React, { useState, useEffect } from 'react';
import { useSpring, animated, useTransition, useTrail } from '@react-spring/web';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign,
  Calendar,
  Target,
  Activity,
  Users,
  Building,
  AlertTriangle
} from 'lucide-react';
import { format } from 'date-fns';
import AnimatedBudgetCard from './AnimatedBudgetCard';
import AnimatedBudgetProgress from './AnimatedBudgetProgress';
import AnimatedExpenseList from './AnimatedExpenseList';

interface BudgetSummary {
  totalBudget: number;
  totalSpent: number;
  totalRemaining: number;
  utilization: number;
  departmentCount: number;
  activeCategories: number;
  monthlyAverage: number;
  lastUpdated: string;
}

interface AnimatedBudgetSummaryProps {
  summary: BudgetSummary;
  onRefresh?: () => void;
  loading?: boolean;
}

export const AnimatedBudgetSummary: React.FC<AnimatedBudgetSummaryProps> = ({
  summary,
  onRefresh,
  loading = false
}) => {
  const [mounted, setMounted] = useState(false);
  const [activeView, setActiveView] = useState<'overview' | 'progress' | 'expenses'>('overview');

  useEffect(() => {
    setMounted(true);
  }, []);

  // Main container animation
  const containerSpring = useSpring({
    from: { opacity: 0, transform: 'translateY(20px)' },
    to: { opacity: 1, transform: 'translateY(0px)' },
    config: { tension: 200, friction: 20 },
    delay: 100
  });

  // Header animation
  const headerSpring = useSpring({
    from: { opacity: 0, transform: 'translateY(-10px)' },
    to: { opacity: 1, transform: 'translateY(0px)' },
    config: { tension: 200, friction: 20 },
    delay: 200
  });

  // Stats cards animation
  const statsTrail = useTrail(4, {
    from: { opacity: 0, transform: 'translateY(20px)' },
    to: { opacity: 1, transform: 'translateY(0px)' },
    config: { tension: 200, friction: 20 },
    delay: 300
  });

  // View transitions
  const viewTransition = useTransition(activeView, {
    keys: activeView,
    from: { opacity: 0, transform: 'translateX(20px)' },
    enter: { opacity: 1, transform: 'translateX(0px)' },
    leave: { opacity: 0, transform: 'translateX(-20px)' },
    config: { tension: 200, friction: 20 }
  });

  // Animated values
  const totalBudgetSpring = useSpring({
    from: { number: 0 },
    to: { number: summary.totalBudget },
    config: { tension: 200, friction: 20 },
    delay: 400
  });

  const totalSpentSpring = useSpring({
    from: { number: 0 },
    to: { number: summary.totalSpent },
    config: { tension: 200, friction: 20 },
    delay: 500
  });

  const utilizationSpring = useSpring({
    from: { number: 0 },
    to: { number: summary.utilization },
    config: { tension: 200, friction: 20 },
    delay: 600
  });

  const departmentCountSpring = useSpring({
    from: { number: 0 },
    to: { number: summary.departmentCount },
    config: { tension: 200, friction: 20 },
    delay: 700
  });

  const getUtilizationColor = (percentage: number) => {
    if (percentage >= 90) return 'text-red-600 bg-red-100';
    if (percentage >= 75) return 'text-yellow-600 bg-yellow-100';
    return 'text-green-600 bg-green-100';
  };

  const getUtilizationIcon = (percentage: number) => {
    if (percentage >= 90) return TrendingDown;
    if (percentage >= 75) return AlertTriangle;
    return TrendingUp;
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Budget Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="h-20 bg-gray-200 animate-pulse rounded-lg"></div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  const stats = [
    {
      title: 'Total Budget',
      value: totalBudgetSpring.number,
      icon: DollarSign,
      prefix: '$',
      color: 'text-blue-600 bg-blue-100'
    },
    {
      title: 'Total Spent',
      value: totalSpentSpring.number,
      icon: TrendingDown,
      prefix: '$',
      color: 'text-red-600 bg-red-100'
    },
    {
      title: 'Utilization',
      value: utilizationSpring.number,
      icon: getUtilizationIcon(summary.utilization),
      suffix: '%',
      color: getUtilizationColor(summary.utilization)
    },
    {
      title: 'Departments',
      value: departmentCountSpring.number,
      icon: Building,
      color: 'text-purple-600 bg-purple-100'
    }
  ];

  return (
    <animated.div style={containerSpring} className="space-y-6">
      {/* Header */}
      <animated.div style={headerSpring}>
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold">Budget Summary</h2>
            <p className="text-muted-foreground">
              Last updated: {format(new Date(summary.lastUpdated), 'MMM d, yyyy HH:mm')}
            </p>
          </div>
          <div className="flex gap-2">
            <Button
              variant={activeView === 'overview' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setActiveView('overview')}
            >
              Overview
            </Button>
            <Button
              variant={activeView === 'progress' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setActiveView('progress')}
            >
              Progress
            </Button>
            <Button
              variant={activeView === 'expenses' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setActiveView('expenses')}
            >
              Expenses
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={onRefresh}
              className="ml-2"
            >
              <Activity className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </animated.div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {statsTrail.map((props, index) => {
          const stat = stats[index];
          const Icon = stat.icon;
          return (
            <animated.div key={stat.title} style={props}>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">{stat.title}</p>
                      <p className="text-2xl font-bold">
                        {stat.prefix || ''}
                        <animated.span>
                          {stat.value && typeof stat.value.to === 'function' 
                            ? stat.value.to(n => n.toLocaleString())
                            : '0'}
                        </animated.span>
                        {stat.suffix || ''}
                      </p>
                    </div>
                    <div className={`p-2 rounded-lg ${stat.color}`}>
                      <Icon className="h-5 w-5" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </animated.div>
          );
        })}
      </div>

      {/* View Content */}
      {viewTransition((style, view) => (
        <animated.div style={style}>
          {view === 'overview' && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Budget Overview</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium">Total Budget</span>
                        <span className="text-lg font-bold">
                          $<animated.span>
                            {totalBudgetSpring.number && typeof totalBudgetSpring.number.to === 'function'
                              ? totalBudgetSpring.number.to(n => n.toLocaleString())
                              : '0'}
                          </animated.span>
                        </span>
                      </div>
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium">Total Spent</span>
                        <span className="text-lg font-bold">
                          $<animated.span>
                            {totalSpentSpring.number && typeof totalSpentSpring.number.to === 'function'
                              ? totalSpentSpring.number.to(n => n.toLocaleString())
                              : '0'}
                          </animated.span>
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Remaining</span>
                        <span className={`text-lg font-bold ${
                          summary.totalRemaining >= 0 ? 'text-green-600' : 'text-red-600'
                        }`}>
                          ${summary.totalRemaining.toLocaleString()}
                        </span>
                      </div>
                    </div>
                    
                    <div>
                      <Progress 
                        value={Math.min(summary.utilization, 100)}
                        className="h-3"
                      />
                      <div className="flex items-center justify-between mt-2 text-sm">
                        <span className="text-muted-foreground">
                          {summary.utilization.toFixed(1)}% used
                        </span>
                        <Badge className={getUtilizationColor(summary.utilization)}>
                          {React.createElement(getUtilizationIcon(summary.utilization), {
                            className: 'h-3 w-3 inline mr-1'
                          })}
                          {summary.utilization.toFixed(1)}%
                        </Badge>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Quick Stats</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Active Categories</span>
                      <Badge variant="secondary">{summary.activeCategories}</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Departments</span>
                      <Badge variant="secondary">{summary.departmentCount}</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Monthly Average</span>
                      <span className="font-medium">
                        ${summary.monthlyAverage.toLocaleString()}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {view === 'progress' && (
            <AnimatedBudgetProgress
              budgetProgress={[]}
              totalBudget={summary.totalBudget}
              totalSpent={summary.totalSpent}
            />
          )}

          {view === 'expenses' && (
            <AnimatedExpenseList
              expenses={[]}
              loading={loading}
            />
          )}
        </animated.div>
      ))}
    </animated.div>
  );
};

export default AnimatedBudgetSummary;