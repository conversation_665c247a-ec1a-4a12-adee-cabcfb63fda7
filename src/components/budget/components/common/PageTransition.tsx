import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { pageTransitionVariants, durations, easings } from '../../utils/animations';

interface PageTransitionProps {
  children: React.ReactNode;
  className?: string;
  direction?: 'horizontal' | 'vertical';
  duration?: number;
}

export const PageTransition: React.FC<PageTransitionProps> = ({
  children,
  className,
  direction = 'horizontal',
  duration = durations.slow,
}) => {
  const variants = {
    initial: {
      opacity: 0,
      ...(direction === 'horizontal' ? { x: 20 } : { y: 20 }),
    },
    in: {
      opacity: 1,
      ...(direction === 'horizontal' ? { x: 0 } : { y: 0 }),
      transition: {
        duration,
        ease: easings.easeOut,
      },
    },
    out: {
      opacity: 0,
      ...(direction === 'horizontal' ? { x: -20 } : { y: -20 }),
      transition: {
        duration: duration * 0.6,
        ease: easings.accelerated,
      },
    },
  };

  return (
    <motion.div
      className={className}
      initial="initial"
      animate="in"
      exit="out"
      variants={variants}
    >
      {children}
    </motion.div>
  );
};

interface FadeTransitionProps {
  children: React.ReactNode;
  className?: string;
  duration?: number;
}

export const FadeTransition: React.FC<FadeTransitionProps> = ({
  children,
  className,
  duration = durations.default,
}) => {
  return (
    <motion.div
      className={className}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{
        duration,
        ease: easings.easeOut,
      }}
    >
      {children}
    </motion.div>
  );
};

interface SlideTransitionProps {
  children: React.ReactNode;
  direction: 'up' | 'down' | 'left' | 'right';
  className?: string;
  distance?: number;
  duration?: number;
}

export const SlideTransition: React.FC<SlideTransitionProps> = ({
  children,
  direction,
  className,
  distance = 20,
  duration = durations.default,
}) => {
  const getInitialPosition = () => {
    switch (direction) {
      case 'up':
        return { y: distance };
      case 'down':
        return { y: -distance };
      case 'left':
        return { x: distance };
      case 'right':
        return { x: -distance };
      default:
        return {};
    }
  };

  const getExitPosition = () => {
    switch (direction) {
      case 'up':
        return { y: -distance / 2 };
      case 'down':
        return { y: distance / 2 };
      case 'left':
        return { x: -distance / 2 };
      case 'right':
        return { x: distance / 2 };
      default:
        return {};
    }
  };

  return (
    <motion.div
      className={className}
      initial={{
        opacity: 0,
        scale: 0.95,
        ...getInitialPosition(),
      }}
      animate={{
        opacity: 1,
        scale: 1,
        x: 0,
        y: 0,
        transition: {
          duration,
          ease: easings.easeOut,
        },
      }}
      exit={{
        opacity: 0,
        scale: 0.95,
        ...getExitPosition(),
        transition: {
          duration: duration * 0.6,
          ease: easings.accelerated,
        },
      }}
    >
      {children}
    </motion.div>
  );
};

interface ScaleTransitionProps {
  children: React.ReactNode;
  className?: string;
  from?: number;
  to?: number;
  duration?: number;
}

export const ScaleTransition: React.FC<ScaleTransitionProps> = ({
  children,
  className,
  from = 0.8,
  to = 1,
  duration = durations.default,
}) => {
  return (
    <motion.div
      className={className}
      initial={{
        opacity: 0,
        scale: from,
      }}
      animate={{
        opacity: 1,
        scale: to,
        transition: {
          duration,
          ease: easings.easeOut,
        },
      }}
      exit={{
        opacity: 0,
        scale: from + (to - from) * 0.5,
        transition: {
          duration: duration * 0.6,
          ease: easings.accelerated,
        },
      }}
    >
      {children}
    </motion.div>
  );
};

interface StaggeredTransitionProps {
  children: React.ReactNode;
  className?: string;
  staggerDelay?: number;
  childDelay?: number;
}

export const StaggeredTransition: React.FC<StaggeredTransitionProps> = ({
  children,
  className,
  staggerDelay = 0.05,
  childDelay = 0.1,
}) => {
  // Convert children to array if it isn't already
  const childrenArray = React.Children.toArray(children);
  
  return (
    <motion.div
      className={className}
      initial="hidden"
      animate="visible"
      exit="exit"
      variants={{
        hidden: {},
        visible: {
          transition: {
            staggerChildren: staggerDelay,
            delayChildren: childDelay,
          },
        },
        exit: {
          transition: {
            staggerChildren: staggerDelay * 0.5,
            staggerDirection: -1,
          },
        },
      }}
    >
      {childrenArray.map((child, index) => (
        <motion.div
          key={index}
          variants={{
            hidden: {
              opacity: 0,
              y: 10,
              scale: 0.95,
            },
            visible: {
              opacity: 1,
              y: 0,
              scale: 1,
              transition: {
                duration: durations.default,
                ease: easings.easeOut,
              },
            },
            exit: {
              opacity: 0,
              y: -5,
              scale: 0.95,
              transition: {
                duration: durations.fast,
                ease: easings.accelerated,
              },
            },
          }}
        >
          {child}
        </motion.div>
      ))}
    </motion.div>
  );
};

// Modal-specific transition
interface ModalTransitionProps {
  children: React.ReactNode;
  isOpen: boolean;
  onClose?: () => void;
}

export const ModalTransition: React.FC<ModalTransitionProps> = ({
  children,
  isOpen,
  onClose,
}) => {
  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            className="fixed inset-0 bg-black/20 backdrop-blur-sm z-40"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{
              duration: durations.default,
              ease: easings.easeOut,
            }}
            onClick={onClose}
          />
          
          {/* Modal content */}
          <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
            <motion.div
              className="relative bg-white dark:bg-gray-900 rounded-xl shadow-xl max-w-lg w-full"
              initial={{
                opacity: 0,
                scale: 0.95,
                y: 20,
              }}
              animate={{
                opacity: 1,
                scale: 1,
                y: 0,
                transition: {
                  duration: durations.default,
                  ease: easings.emphasized,
                },
              }}
              exit={{
                opacity: 0,
                scale: 0.95,
                y: 10,
                transition: {
                  duration: durations.fast,
                  ease: easings.accelerated,
                },
              }}
            >
              {children}
            </motion.div>
          </div>
        </>
      )}
    </AnimatePresence>
  );
};