import React from 'react';
import { Card } from '@/components/ui/card';
import { cn } from '@/lib/utils';

interface StandardCardProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'metric-primary' | 'metric-success' | 'metric-warning' | 'metric-danger';
  onClick?: () => void;
  hoverable?: boolean;
}

/**
 * StandardCard - Ensures consistent card styling across the application
 * Following 2025 UX standards with standardized padding and shadows
 */
export const StandardCard: React.FC<StandardCardProps> = ({
  children,
  className,
  variant = 'default',
  onClick,
  hoverable = false,
}) => {
  const baseClasses = 'p-6 transition-all duration-250';
  
  const variantClasses = {
    'default': 'standard-card',
    'metric-primary': 'metric-card metric-card--primary',
    'metric-success': 'metric-card metric-card--success',
    'metric-warning': 'metric-card metric-card--warning',
    'metric-danger': 'metric-card metric-card--danger',
  };
  
  const hoverClasses = hoverable ? 'hover:shadow-md hover:border-opacity-80 cursor-pointer' : '';
  
  return (
    <Card 
      className={cn(
        baseClasses,
        variantClasses[variant],
        hoverClasses,
        className
      )}
      onClick={onClick}
    >
      {children}
    </Card>
  );
};

export default StandardCard;