import React from 'react';
import { EnhancedSkeleton } from './EnhancedSkeleton';
import { cn } from '@/lib/utils';

interface StandardLoaderProps {
  type?: 'text' | 'card' | 'chart' | 'table' | 'list';
  lines?: number;
  className?: string;
}

/**
 * StandardLoader - Unified loading state component
 * Ensures consistent loading patterns across the application
 * Following 2025 UX standards
 */
export const StandardLoader: React.FC<StandardLoaderProps> = ({
  type = 'text',
  lines = 1,
  className,
}) => {
  // Standard loading patterns for different content types
  const renderLoader = () => {
    switch (type) {
      case 'card':
        return (
          <div className={cn('p-6 space-y-4', className)}>
            <div className="flex items-center justify-between">
              <EnhancedSkeleton variant="shimmer" width={120} height={20} />
              <EnhancedSkeleton variant="shimmer" width={60} height={20} />
            </div>
            <EnhancedSkeleton variant="shimmer" height={32} />
            <div className="space-y-2">
              <EnhancedSkeleton variant="shimmer" />
              <EnhancedSkeleton variant="shimmer" width="75%" />
            </div>
          </div>
        );
        
      case 'chart':
        return (
          <div className={cn('p-6 space-y-4', className)}>
            <EnhancedSkeleton variant="shimmer" width={150} height={24} />
            <div className="h-64">
              <EnhancedSkeleton variant="shimmer" height="100%" />
            </div>
          </div>
        );
        
      case 'table':
        return (
          <div className={cn('space-y-2', className)}>
            {/* Table header */}
            <div className="flex gap-4 p-3 border-b">
              <EnhancedSkeleton variant="shimmer" width={100} height={16} />
              <EnhancedSkeleton variant="shimmer" width={150} height={16} />
              <EnhancedSkeleton variant="shimmer" width={100} height={16} />
              <EnhancedSkeleton variant="shimmer" width={80} height={16} />
            </div>
            {/* Table rows */}
            {[...Array(lines || 5)].map((_, i) => (
              <div key={i} className="flex gap-4 p-3">
                <EnhancedSkeleton variant="shimmer" width={100} height={16} />
                <EnhancedSkeleton variant="shimmer" width={150} height={16} />
                <EnhancedSkeleton variant="shimmer" width={100} height={16} />
                <EnhancedSkeleton variant="shimmer" width={80} height={16} />
              </div>
            ))}
          </div>
        );
        
      case 'list':
        return (
          <div className={cn('space-y-3', className)}>
            {[...Array(lines || 3)].map((_, i) => (
              <div key={i} className="flex items-center gap-3">
                <EnhancedSkeleton variant="shimmer" width={40} height={40} className="rounded-full" />
                <div className="flex-1 space-y-2">
                  <EnhancedSkeleton variant="shimmer" width="60%" height={16} />
                  <EnhancedSkeleton variant="shimmer" width="40%" height={14} />
                </div>
                <EnhancedSkeleton variant="shimmer" width={60} height={20} />
              </div>
            ))}
          </div>
        );
        
      case 'text':
      default:
        return (
          <EnhancedSkeleton 
            variant="shimmer" 
            lines={lines} 
            className={className}
          />
        );
    }
  };
  
  return renderLoader();
};

// Utility component for inline loading
export const InlineLoader: React.FC<{ width?: number; height?: number }> = ({ 
  width = 60, 
  height = 20 
}) => (
  <EnhancedSkeleton variant="shimmer" width={width} height={height} />
);

// Utility component for metric loading
export const MetricLoader: React.FC = () => (
  <div className="space-y-2">
    <EnhancedSkeleton variant="shimmer" width={80} height={14} />
    <EnhancedSkeleton variant="shimmer" width={120} height={32} />
  </div>
);

export default StandardLoader;