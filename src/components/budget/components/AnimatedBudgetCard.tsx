import React from 'react';
import { useSpring, animated, config } from '@react-spring/web';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { LucideIcon } from 'lucide-react';

interface AnimatedBudgetCardProps {
  title: string;
  value: string | number;
  icon: LucideIcon;
  trend?: 'up' | 'down' | 'neutral';
  trendValue?: string;
  color?: string;
  delay?: number;
  onClick?: () => void;
}

export const AnimatedBudgetCard: React.FC<AnimatedBudgetCardProps> = ({
  title,
  value,
  icon: Icon,
  trend,
  trendValue,
  color = 'text-blue-600',
  delay = 0,
  onClick
}) => {
  const [props, api] = useSpring(() => ({
    from: { opacity: 0, transform: 'scale(0.9) translateY(20px)' },
    to: { opacity: 1, transform: 'scale(1) translateY(0px)' },
    delay,
    config: config.gentle
  }));

  const [hoverProps, hoverApi] = useSpring(() => ({
    transform: 'scale(1)',
    boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)'
  }));

  const [valueSpring, valueApi] = useSpring(() => ({
    number: 0,
    config: { tension: 200, friction: 20 }
  }));

  React.useEffect(() => {
    const numericValue = typeof value === 'string' ? parseFloat(value.replace(/[^0-9.-]/g, '')) : value;
    if (!isNaN(numericValue)) {
      valueApi.start({ number: numericValue });
    }
  }, [value, valueApi]);

  const handleMouseEnter = () => {
    hoverApi.start({ transform: 'scale(1.02)', boxShadow: '0 8px 25px rgba(0, 0, 0, 0.15)' });
  };

  const handleMouseLeave = () => {
    hoverApi.start({ transform: 'scale(1)', boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)' });
  };

  return (
    <animated.div
      style={{ ...props, ...hoverProps }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onClick={onClick}
      className={onClick ? 'cursor-pointer' : ''}
    >
      <Card className="h-full transition-all duration-200 hover:shadow-lg">
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <CardTitle className="text-sm font-medium text-gray-600">
            {title}
          </CardTitle>
          <div className={`p-2 rounded-lg ${color} bg-opacity-10`}>
            <Icon className={`h-4 w-4 ${color}`} />
          </div>
        </CardHeader>
        <CardContent>
          <animated.div className="text-2xl font-bold">
            {valueSpring.number.to((n) => {
              if (typeof value === 'string' && value.includes('$')) {
                return `$${n.toLocaleString('en-US', { maximumFractionDigits: 0 })}`;
              }
              return n.toLocaleString('en-US', { maximumFractionDigits: 0 });
            })}
          </animated.div>
          {trend && trendValue && (
            <div className="flex items-center text-xs mt-1">
              <span className={`${trend === 'up' ? 'text-green-600' : trend === 'down' ? 'text-red-600' : 'text-gray-600'}`}>
                {trendValue}
              </span>
            </div>
          )}
        </CardContent>
      </Card>
    </animated.div>
  );
};

export default AnimatedBudgetCard;