import React, { ReactNode, Suspense } from 'react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';

interface TabConfig {
  value: string;
  label: string;
  component: React.ComponentType<any>;
  props?: Record<string, any>;
  hidden?: boolean;
}

interface TabManagerProps {
  tabs: TabConfig[];
  activeTab: string;
  onTabChange: (value: string) => void;
  className?: string;
  showTabList?: boolean;
  gridCols?: number;
}

const TabLoadingSkeleton: React.FC = () => (
  <div className="space-y-4">
    <Skeleton className="h-32 w-full" />
    <Skeleton className="h-48 w-full" />
    <Skeleton className="h-24 w-full" />
  </div>
);

export const TabManager: React.FC<TabManagerProps> = ({
  tabs,
  activeTab,
  onTabChange,
  className = '',
  showTabList = true,
  gridCols = 5
}) => {
  const visibleTabs = tabs.filter(tab => !tab.hidden);
  
  return (
    <Tabs 
      value={activeTab} 
      onValueChange={onTabChange} 
      className={`w-full ${className}`}
    >
      {showTabList && (
        <TabsList className={`hidden md:grid w-full grid-cols-${gridCols}`}>
          {visibleTabs.map(tab => (
            <TabsTrigger key={tab.value} value={tab.value}>
              {tab.label}
            </TabsTrigger>
          ))}
        </TabsList>
      )}
      
      {tabs.map(tab => {
        const Component = tab.component;
        return (
          <TabsContent key={tab.value} value={tab.value} className="space-y-4">
            <Suspense fallback={<TabLoadingSkeleton />}>
              <Component {...(tab.props || {})} />
            </Suspense>
          </TabsContent>
        );
      })}
    </Tabs>
  );
};

export default TabManager;