import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  Plus,
  Zap,
  FileSpreadsheet,
  Download,
  Upload,
  Calculator,
  TrendingUp,
  Mail,
  Calendar,
  Settings,
  DollarSign,
  PieChart,
  Bell,
  FileText,
  BarChart3,
  X,
  ChevronUp,
  ChevronDown,
  Wallet,
  Receipt,
  Target,
  AlertTriangle
} from 'lucide-react';
import { cn } from '@/lib/utils';

export interface QuickAction {
  id: string;
  label: string;
  icon: React.ReactNode;
  description?: string;
  shortcut?: string;
  action: () => void;
  category: 'budget' | 'expense' | 'report' | 'analysis' | 'settings';
  color?: string;
  badge?: string | number;
  requiresPermission?: string;
}

export interface QuickActionsMenuProps {
  actions?: QuickAction[];
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  showLabels?: boolean;
  compactMode?: boolean;
  fabStyle?: boolean;
  onActionExecute?: (actionId: string) => void;
}

const defaultActions: QuickAction[] = [
  {
    id: 'add-expense',
    label: 'Add Expense',
    icon: <Receipt className="h-4 w-4" />,
    description: 'Record a new training expense',
    shortcut: 'Ctrl+E',
    category: 'expense',
    color: 'text-blue-500',
    action: () => console.log('Add expense')
  },
  {
    id: 'update-budget',
    label: 'Update Budget',
    icon: <Wallet className="h-4 w-4" />,
    description: 'Modify budget allocation',
    shortcut: 'Ctrl+B',
    category: 'budget',
    color: 'text-green-500',
    action: () => console.log('Update budget')
  },
  {
    id: 'generate-report',
    label: 'Generate Report',
    icon: <FileText className="h-4 w-4" />,
    description: 'Create monthly budget report',
    shortcut: 'Ctrl+R',
    category: 'report',
    color: 'text-purple-500',
    action: () => console.log('Generate report')
  },
  {
    id: 'export-data',
    label: 'Export Data',
    icon: <Download className="h-4 w-4" />,
    description: 'Export budget data to CSV/PDF',
    shortcut: 'Ctrl+D',
    category: 'report',
    action: () => console.log('Export data')
  },
  {
    id: 'import-expenses',
    label: 'Import Expenses',
    icon: <Upload className="h-4 w-4" />,
    description: 'Bulk import expense data',
    category: 'expense',
    action: () => console.log('Import expenses')
  },
  {
    id: 'budget-calculator',
    label: 'Budget Calculator',
    icon: <Calculator className="h-4 w-4" />,
    description: 'Quick budget calculations',
    category: 'budget',
    action: () => console.log('Open calculator')
  },
  {
    id: 'view-analytics',
    label: 'View Analytics',
    icon: <BarChart3 className="h-4 w-4" />,
    description: 'Open analytics dashboard',
    shortcut: 'Ctrl+A',
    category: 'analysis',
    color: 'text-orange-500',
    action: () => console.log('View analytics')
  },
  {
    id: 'set-alert',
    label: 'Set Alert',
    icon: <Bell className="h-4 w-4" />,
    description: 'Configure budget alerts',
    category: 'settings',
    badge: 3,
    action: () => console.log('Set alert')
  },
  {
    id: 'forecast',
    label: 'View Forecast',
    icon: <TrendingUp className="h-4 w-4" />,
    description: 'Budget projection analysis',
    category: 'analysis',
    action: () => console.log('View forecast')
  },
  {
    id: 'email-report',
    label: 'Email Report',
    icon: <Mail className="h-4 w-4" />,
    description: 'Send report via email',
    category: 'report',
    action: () => console.log('Email report')
  }
];

const categoryConfig = {
  budget: { label: 'Budget', color: 'text-green-500' },
  expense: { label: 'Expenses', color: 'text-blue-500' },
  report: { label: 'Reports', color: 'text-purple-500' },
  analysis: { label: 'Analysis', color: 'text-orange-500' },
  settings: { label: 'Settings', color: 'text-gray-500' }
};

export const QuickActionsMenu: React.FC<QuickActionsMenuProps> = ({
  actions = defaultActions,
  position = 'bottom-right',
  showLabels = true,
  compactMode = false,
  fabStyle = true,
  onActionExecute
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [recentActions, setRecentActions] = useState<string[]>([]);

  const handleAction = (action: QuickAction) => {
    action.action();
    onActionExecute?.(action.id);
    
    // Track recent actions
    setRecentActions(prev => {
      const updated = [action.id, ...prev.filter(id => id !== action.id)].slice(0, 3);
      return updated;
    });
    
    // Close menu after action in compact mode
    if (compactMode) {
      setIsOpen(false);
    }
  };

  const groupedActions = actions.reduce((acc, action) => {
    if (!acc[action.category]) {
      acc[action.category] = [];
    }
    acc[action.category].push(action);
    return acc;
  }, {} as Record<string, QuickAction[]>);

  const frequentActions = actions
    .filter(action => recentActions.includes(action.id))
    .sort((a, b) => recentActions.indexOf(a.id) - recentActions.indexOf(b.id));

  const positionClasses = {
    'bottom-right': 'bottom-6 right-6',
    'bottom-left': 'bottom-6 left-6',
    'top-right': 'top-20 right-6',
    'top-left': 'top-20 left-6'
  };

  const expandDirection = position.includes('bottom') ? 'up' : 'down';

  if (fabStyle) {
    return (
      <div className={cn("fixed z-50", positionClasses[position])}>
        <AnimatePresence>
          {isOpen && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8, y: expandDirection === 'up' ? 20 : -20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.8, y: expandDirection === 'up' ? 20 : -20 }}
              className={cn(
                "absolute",
                position.includes('right') ? 'right-0' : 'left-0',
                position.includes('bottom') ? 'bottom-16' : 'top-16',
                "w-72"
              )}
            >
              <Card className="p-4 shadow-lg">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="font-semibold text-sm">Quick Actions</h3>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6"
                    onClick={() => setIsOpen(false)}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>

                {/* Recent Actions */}
                {frequentActions.length > 0 && (
                  <>
                    <div className="mb-2">
                      <p className="text-xs text-muted-foreground mb-1">Recent</p>
                      <div className="space-y-1">
                        {frequentActions.map(action => (
                          <Button
                            key={action.id}
                            variant="ghost"
                            size="sm"
                            className="w-full justify-start"
                            onClick={() => handleAction(action)}
                          >
                            <span className={cn("mr-2", action.color)}>
                              {action.icon}
                            </span>
                            {action.label}
                            {action.badge && (
                              <span className="ml-auto text-xs bg-red-500 text-white rounded-full px-1.5">
                                {action.badge}
                              </span>
                            )}
                          </Button>
                        ))}
                      </div>
                    </div>
                    <div className="border-t my-2" />
                  </>
                )}

                {/* All Actions */}
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {Object.entries(groupedActions).map(([category, categoryActions]) => (
                    <div key={category}>
                      <p className={cn(
                        "text-xs font-medium mb-1",
                        categoryConfig[category as keyof typeof categoryConfig].color
                      )}>
                        {categoryConfig[category as keyof typeof categoryConfig].label}
                      </p>
                      <div className="space-y-1">
                        {categoryActions.map(action => (
                          <TooltipProvider key={action.id}>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="w-full justify-start"
                                  onClick={() => handleAction(action)}
                                >
                                  <span className={cn("mr-2", action.color)}>
                                    {action.icon}
                                  </span>
                                  {action.label}
                                  {action.shortcut && !compactMode && (
                                    <span className="ml-auto text-xs text-muted-foreground">
                                      {action.shortcut}
                                    </span>
                                  )}
                                  {action.badge && (
                                    <span className="ml-auto text-xs bg-red-500 text-white rounded-full px-1.5">
                                      {action.badge}
                                    </span>
                                  )}
                                </Button>
                              </TooltipTrigger>
                              {action.description && (
                                <TooltipContent>
                                  <p>{action.description}</p>
                                </TooltipContent>
                              )}
                            </Tooltip>
                          </TooltipProvider>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>

                {/* Expand/Collapse for long lists */}
                {actions.length > 8 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full mt-2"
                    onClick={() => setIsExpanded(!isExpanded)}
                  >
                    {isExpanded ? (
                      <>
                        <ChevronUp className="h-3 w-3 mr-1" />
                        Show Less
                      </>
                    ) : (
                      <>
                        <ChevronDown className="h-3 w-3 mr-1" />
                        Show More
                      </>
                    )}
                  </Button>
                )}
              </Card>
            </motion.div>
          )}
        </AnimatePresence>

        {/* FAB Button */}
        <motion.button
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          onClick={() => setIsOpen(!isOpen)}
          className={cn(
            "relative rounded-full shadow-lg transition-all",
            "bg-primary text-primary-foreground hover:bg-primary/90",
            "flex items-center justify-center",
            compactMode ? "h-12 w-12" : "h-14 w-14"
          )}
        >
          <AnimatePresence mode="wait">
            {isOpen ? (
              <motion.div
                key="close"
                initial={{ rotate: -90 }}
                animate={{ rotate: 0 }}
                exit={{ rotate: 90 }}
              >
                <X className={compactMode ? "h-5 w-5" : "h-6 w-6"} />
              </motion.div>
            ) : (
              <motion.div
                key="open"
                initial={{ rotate: 90 }}
                animate={{ rotate: 0 }}
                exit={{ rotate: -90 }}
              >
                <Zap className={compactMode ? "h-5 w-5" : "h-6 w-6"} />
              </motion.div>
            )}
          </AnimatePresence>
          
          {/* Notification Badge */}
          {!isOpen && actions.some(a => a.badge) && (
            <span className="absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
              {actions.reduce((sum, a) => sum + (typeof a.badge === 'number' ? a.badge : 0), 0)}
            </span>
          )}
        </motion.button>
      </div>
    );
  }

  // Dropdown Menu Style (non-FAB)
  return (
    <div className={cn("fixed z-50", positionClasses[position])}>
      <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm">
            <Zap className="h-4 w-4 mr-2" />
            Quick Actions
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-56">
          <DropdownMenuLabel>Quick Actions</DropdownMenuLabel>
          <DropdownMenuSeparator />
          
          {Object.entries(groupedActions).map(([category, categoryActions], index) => (
            <React.Fragment key={category}>
              {index > 0 && <DropdownMenuSeparator />}
              <DropdownMenuGroup>
                {categoryActions.map(action => (
                  <DropdownMenuItem
                    key={action.id}
                    onClick={() => handleAction(action)}
                  >
                    <span className={cn("mr-2", action.color)}>
                      {action.icon}
                    </span>
                    {action.label}
                    {action.shortcut && (
                      <DropdownMenuShortcut>{action.shortcut}</DropdownMenuShortcut>
                    )}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuGroup>
            </React.Fragment>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};