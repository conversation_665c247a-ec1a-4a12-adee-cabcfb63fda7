import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QuickActionsMenu } from './QuickActionsMenu';
import type { QuickAction } from './QuickActionsMenu';

describe('QuickActionsMenu', () => {
  const mockActions: QuickAction[] = [
    {
      id: 'test-action-1',
      label: 'Test Action 1',
      icon: <span>Icon1</span>,
      description: 'Test description 1',
      shortcut: 'Ctrl+1',
      category: 'budget',
      action: jest.fn()
    },
    {
      id: 'test-action-2',
      label: 'Test Action 2',
      icon: <span>Icon2</span>,
      category: 'expense',
      badge: 3,
      action: jest.fn()
    },
    {
      id: 'test-action-3',
      label: 'Test Action 3',
      icon: <span>Icon3</span>,
      category: 'report',
      color: 'text-purple-500',
      action: jest.fn()
    }
  ];

  beforeEach(() => {
    // Reset all mocks
    mockActions.forEach(action => {
      (action.action as jest.Mock).mockClear();
    });
  });

  it('renders FAB button in closed state', () => {
    render(
      <QuickActionsMenu
        actions={mockActions}
        fabStyle={true}
      />
    );

    // Should show FAB button
    const fabButton = screen.getByRole('button');
    expect(fabButton).toBeInTheDocument();
    
    // Should not show menu content
    expect(screen.queryByText('Quick Actions')).not.toBeInTheDocument();
  });

  it('opens menu when FAB is clicked', async () => {
    render(
      <QuickActionsMenu
        actions={mockActions}
        fabStyle={true}
      />
    );

    const fabButton = screen.getByRole('button');
    fireEvent.click(fabButton);

    // Should show menu
    await waitFor(() => {
      expect(screen.getByText('Quick Actions')).toBeInTheDocument();
    });

    // Should show actions
    expect(screen.getByText('Test Action 1')).toBeInTheDocument();
    expect(screen.getByText('Test Action 2')).toBeInTheDocument();
    expect(screen.getByText('Test Action 3')).toBeInTheDocument();
  });

  it('groups actions by category', async () => {
    render(
      <QuickActionsMenu
        actions={mockActions}
        fabStyle={true}
      />
    );

    const fabButton = screen.getByRole('button');
    fireEvent.click(fabButton);

    await waitFor(() => {
      // Should show category labels
      expect(screen.getByText('Budget')).toBeInTheDocument();
      expect(screen.getByText('Expenses')).toBeInTheDocument();
      expect(screen.getByText('Reports')).toBeInTheDocument();
    });
  });

  it('executes action when clicked', async () => {
    const onActionExecute = jest.fn();
    
    render(
      <QuickActionsMenu
        actions={mockActions}
        fabStyle={true}
        onActionExecute={onActionExecute}
      />
    );

    const fabButton = screen.getByRole('button');
    fireEvent.click(fabButton);

    await waitFor(() => {
      const actionButton = screen.getByText('Test Action 1');
      fireEvent.click(actionButton);
    });

    expect(mockActions[0].action).toHaveBeenCalled();
    expect(onActionExecute).toHaveBeenCalledWith('test-action-1');
  });

  it('shows badges on actions', async () => {
    render(
      <QuickActionsMenu
        actions={mockActions}
        fabStyle={true}
      />
    );

    const fabButton = screen.getByRole('button');
    fireEvent.click(fabButton);

    await waitFor(() => {
      // Should show badge on action 2
      const badge = screen.getByText('3');
      expect(badge).toBeInTheDocument();
      expect(badge).toHaveClass('bg-red-500');
    });
  });

  it('shows notification badge on FAB when closed', () => {
    render(
      <QuickActionsMenu
        actions={mockActions}
        fabStyle={true}
      />
    );

    // Should show total badge count
    const badge = screen.getByText('3');
    expect(badge).toBeInTheDocument();
    expect(badge.parentElement).toHaveClass('absolute');
  });

  it('tracks and shows recent actions', async () => {
    render(
      <QuickActionsMenu
        actions={mockActions}
        fabStyle={true}
      />
    );

    const fabButton = screen.getByRole('button');
    fireEvent.click(fabButton);

    // Click an action
    await waitFor(() => {
      const actionButton = screen.getByText('Test Action 1');
      fireEvent.click(actionButton);
    });

    // Re-open menu
    fireEvent.click(fabButton);
    fireEvent.click(fabButton);

    await waitFor(() => {
      // Should show recent section
      expect(screen.getByText('Recent')).toBeInTheDocument();
    });
  });

  it('renders in dropdown style when fabStyle is false', () => {
    render(
      <QuickActionsMenu
        actions={mockActions}
        fabStyle={false}
      />
    );

    // Should show button with text
    expect(screen.getByText('Quick Actions')).toBeInTheDocument();
    
    // Should not show FAB
    const buttons = screen.getAllByRole('button');
    expect(buttons[0]).toHaveTextContent('Quick Actions');
  });

  it('closes menu when X button is clicked', async () => {
    render(
      <QuickActionsMenu
        actions={mockActions}
        fabStyle={true}
      />
    );

    const fabButton = screen.getByRole('button');
    fireEvent.click(fabButton);

    await waitFor(() => {
      expect(screen.getByText('Quick Actions')).toBeInTheDocument();
    });

    // Find and click X button
    const closeButton = screen.getAllByRole('button').find(btn => 
      btn.querySelector('[class*="X"]') || btn.textContent === ''
    );
    
    if (closeButton) {
      fireEvent.click(closeButton);
    }

    await waitFor(() => {
      expect(screen.queryByText('Quick Actions')).not.toBeInTheDocument();
    });
  });

  it('applies custom position classes', () => {
    const { container } = render(
      <QuickActionsMenu
        actions={mockActions}
        position="top-left"
        fabStyle={true}
      />
    );

    const wrapper = container.querySelector('.fixed');
    expect(wrapper).toHaveClass('top-20', 'left-6');
  });

  it('shows shortcuts when not in compact mode', async () => {
    render(
      <QuickActionsMenu
        actions={mockActions}
        fabStyle={true}
        compactMode={false}
      />
    );

    const fabButton = screen.getByRole('button');
    fireEvent.click(fabButton);

    await waitFor(() => {
      expect(screen.getByText('Ctrl+1')).toBeInTheDocument();
    });
  });
});