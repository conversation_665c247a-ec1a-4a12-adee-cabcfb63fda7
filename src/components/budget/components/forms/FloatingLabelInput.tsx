import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import { AlertCircle, CheckCircle, Eye, EyeOff } from 'lucide-react';

interface FloatingLabelInputProps {
  id: string;
  label: string;
  value: string | number;
  onChange: (value: string | number) => void;
  onBlur?: () => void;
  onFocus?: () => void;
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'textarea';
  placeholder?: string;
  error?: string;
  success?: boolean;
  required?: boolean;
  disabled?: boolean;
  className?: string;
  inputClassName?: string;
  icon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  rows?: number; // for textarea
  min?: number;
  max?: number;
  step?: number;
  autoComplete?: string;
  'data-testid'?: string;
}

export const FloatingLabelInput: React.FC<FloatingLabelInputProps> = ({
  id,
  label,
  value,
  onChange,
  onBlur,
  onFocus,
  type = 'text',
  placeholder,
  error,
  success = false,
  required = false,
  disabled = false,
  className,
  inputClassName,
  icon,
  rightIcon,
  rows = 4,
  min,
  max,
  step,
  autoComplete,
  'data-testid': testId,
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const inputRef = useRef<HTMLInputElement | HTMLTextAreaElement>(null);
  
  // Check if input has value or is focused (for floating label)
  const hasValue = value !== '' && value !== null && value !== undefined;
  const shouldFloat = isFocused || hasValue || placeholder;
  
  useEffect(() => {
    // Auto-focus if needed
    if (isFocused && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isFocused]);

  const handleFocus = (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setIsFocused(true);
    onFocus?.();
  };

  const handleBlur = (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setIsFocused(false);
    onBlur?.();
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const val = e.target.value;
    if (type === 'number') {
      const numVal = val === '' ? '' : parseFloat(val);
      onChange(numVal);
    } else {
      onChange(val);
    }
  };

  const getInputType = () => {
    if (type === 'password') {
      return showPassword ? 'text' : 'password';
    }
    return type === 'textarea' ? 'text' : type;
  };

  const inputProps = {
    id,
    ref: inputRef as any,
    value: value || '',
    onChange: handleChange,
    onFocus: handleFocus,
    onBlur: handleBlur,
    type: getInputType(),
    disabled,
    required,
    min: type === 'number' ? min : undefined,
    max: type === 'number' ? max : undefined,
    step: type === 'number' ? step : undefined,
    autoComplete,
    'data-testid': testId,
    placeholder: isFocused ? placeholder : '',
    'aria-describedby': error ? `${id}-error` : success ? `${id}-success` : undefined,
    'aria-invalid': !!error,
    'aria-required': required,
    className: cn(
      // Base styles
      'w-full border-0 bg-transparent px-0 py-0 text-base transition-colors duration-200',
      'placeholder:text-gray-400 dark:placeholder:text-gray-500',
      'focus:outline-none focus:ring-0',
      'disabled:cursor-not-allowed disabled:opacity-50',
      // Padding adjustments for floating label
      type === 'textarea' ? 'pt-6 pb-2 resize-none' : 'pt-6 pb-2 h-auto',
      // Text color
      'text-gray-900 dark:text-gray-100',
      inputClassName
    ),
  };

  const InputComponent = type === 'textarea' ? Textarea : Input;

  return (
    <div className={cn('relative', className)}>
      {/* Main container with border and background */}
      <div className={cn(
        'relative rounded-xl border-2 transition-all duration-300 ease-out',
        'bg-gray-50/50 dark:bg-gray-900/50',
        // Border states
        error 
          ? 'border-red-300 dark:border-red-700' 
          : success
          ? 'border-green-300 dark:border-green-700'
          : isFocused 
          ? 'border-blue-500 dark:border-blue-400' 
          : 'border-gray-200 dark:border-gray-700',
        // Focus and hover states
        !disabled && !error && !success && 'hover:border-gray-300 dark:hover:border-gray-600',
        // Focus ring
        isFocused && !error && 'ring-4 ring-blue-100 dark:ring-blue-900/30',
        error && isFocused && 'ring-4 ring-red-100 dark:ring-red-900/30',
        success && isFocused && 'ring-4 ring-green-100 dark:ring-green-900/30',
        // Disabled state
        disabled && 'opacity-60 cursor-not-allowed'
      )}>
        
        {/* Left icon */}
        {icon && (
          <div className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 dark:text-gray-500">
            {icon}
          </div>
        )}

        {/* Floating label */}
        <Label htmlFor={id} className="absolute left-3 transition-all duration-300 ease-out cursor-text pointer-events-none">
          <motion.span
            animate={{
              y: shouldFloat ? -8 : 8,
              scale: shouldFloat ? 0.85 : 1,
              color: error 
                ? '#ef4444' 
                : success 
                ? '#10b981'
                : isFocused 
                ? '#3b82f6' 
                : '#6b7280'
            }}
            transition={{ duration: 0.2, ease: [0.4, 0, 0.2, 1] }}
            className={cn(
              'origin-left font-medium select-none',
              shouldFloat && 'text-xs',
              !shouldFloat && 'text-base'
            )}
          >
            {label}
            {required && (
              <span className="ml-1 text-red-500 dark:text-red-400">*</span>
            )}
          </motion.span>
        </Label>

        {/* Input/Textarea */}
        <div className={cn(
          'px-3',
          icon && 'pl-10',
          (rightIcon || type === 'password') && 'pr-10'
        )}>
          {type === 'textarea' ? (
            <Textarea {...inputProps} rows={rows} />
          ) : (
            <Input {...inputProps} />
          )}
        </div>

        {/* Right icons */}
        <div className="absolute right-3 top-1/2 -translate-y-1/2 flex items-center gap-1">
          {/* Password toggle */}
          {type === 'password' && (
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="p-1 text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-200"
              aria-label={showPassword ? 'Hide password' : 'Show password'}
              tabIndex={-1}
            >
              {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            </button>
          )}
          
          {/* Status icons */}
          {error && (
            <AlertCircle className="w-4 h-4 text-red-500 dark:text-red-400" />
          )}
          {success && !error && (
            <CheckCircle className="w-4 h-4 text-green-500 dark:text-green-400" />
          )}
          
          {/* Custom right icon */}
          {rightIcon && (
            <div className="text-gray-400 dark:text-gray-500">
              {rightIcon}
            </div>
          )}
        </div>
      </div>

      {/* Error message with animation */}
      <AnimatePresence mode="wait">
        {error && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2, ease: [0.4, 0, 0.2, 1] }}
            className="mt-2"
          >
            <div 
              id={`${id}-error`}
              className="flex items-center gap-2 text-sm text-red-600 dark:text-red-400"
              role="alert"
            >
              <AlertCircle className="w-4 h-4 flex-shrink-0" />
              <span>{error}</span>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Success message with animation */}
      <AnimatePresence mode="wait">
        {success && !error && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2, ease: [0.4, 0, 0.2, 1] }}
            className="mt-2"
          >
            <div 
              id={`${id}-success`}
              className="flex items-center gap-2 text-sm text-green-600 dark:text-green-400"
              role="status"
            >
              <CheckCircle className="w-4 h-4 flex-shrink-0" />
              <span>Looks good!</span>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Focus indicator line */}
      <div className={cn(
        'absolute bottom-0 left-0 h-0.5 bg-gradient-to-r transition-all duration-300 ease-out',
        error 
          ? 'from-red-500 to-red-600' 
          : success
          ? 'from-green-500 to-green-600'
          : 'from-blue-500 to-blue-600',
        isFocused ? 'w-full opacity-100' : 'w-0 opacity-0'
      )} />
    </div>
  );
};