import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import { AlertCircle, CheckCircle, ChevronDown } from 'lucide-react';

interface SelectOption {
  value: string;
  label: string;
  icon?: React.ReactNode;
  color?: string;
  disabled?: boolean;
}

interface FloatingLabelSelectProps {
  id: string;
  label: string;
  value: string;
  onValueChange: (value: string) => void;
  options: SelectOption[];
  placeholder?: string;
  error?: string;
  success?: boolean;
  required?: boolean;
  disabled?: boolean;
  className?: string;
  icon?: React.ReactNode;
  'data-testid'?: string;
}

export const FloatingLabelSelect: React.FC<FloatingLabelSelectProps> = ({
  id,
  label,
  value,
  onValueChange,
  options,
  placeholder = 'Select an option',
  error,
  success = false,
  required = false,
  disabled = false,
  className,
  icon,
  'data-testid': testId,
}) => {
  const [isFocused, setIsFocused] = useState(false);
  
  // Check if select has value (for floating label)
  const hasValue = value !== '' && value !== null && value !== undefined;
  const shouldFloat = isFocused || hasValue;
  
  const selectedOption = options.find(opt => opt.value === value);

  return (
    <div className={cn('relative', className)}>
      {/* Main container with border and background */}
      <div className={cn(
        'relative rounded-xl border-2 transition-all duration-300 ease-out',
        'bg-gray-50/50 dark:bg-gray-900/50',
        // Border states
        error 
          ? 'border-red-300 dark:border-red-700' 
          : success
          ? 'border-green-300 dark:border-green-700'
          : isFocused 
          ? 'border-blue-500 dark:border-blue-400' 
          : 'border-gray-200 dark:border-gray-700',
        // Focus and hover states
        !disabled && !error && !success && 'hover:border-gray-300 dark:hover:border-gray-600',
        // Focus ring
        isFocused && !error && 'ring-4 ring-blue-100 dark:ring-blue-900/30',
        error && isFocused && 'ring-4 ring-red-100 dark:ring-red-900/30',
        success && isFocused && 'ring-4 ring-green-100 dark:ring-green-900/30',
        // Disabled state
        disabled && 'opacity-60 cursor-not-allowed'
      )}>
        
        {/* Left icon */}
        {icon && (
          <div className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 dark:text-gray-500 z-10">
            {icon}
          </div>
        )}

        {/* Floating label */}
        <Label htmlFor={id} className="absolute left-3 transition-all duration-300 ease-out cursor-text pointer-events-none z-10">
          <motion.span
            animate={{
              y: shouldFloat ? -8 : 8,
              scale: shouldFloat ? 0.85 : 1,
              color: error 
                ? '#ef4444' 
                : success 
                ? '#10b981'
                : isFocused 
                ? '#3b82f6' 
                : '#6b7280'
            }}
            transition={{ duration: 0.2, ease: [0.4, 0, 0.2, 1] }}
            className={cn(
              'origin-left font-medium select-none bg-gray-50/50 dark:bg-gray-900/50 px-1 rounded',
              shouldFloat && 'text-xs',
              !shouldFloat && 'text-base'
            )}
          >
            {label}
            {required && (
              <span className="ml-1 text-red-500 dark:text-red-400">*</span>
            )}
          </motion.span>
        </Label>

        {/* Select component */}
        <Select
          value={value}
          onValueChange={onValueChange}
          disabled={disabled}
          onOpenChange={setIsFocused}
        >
          <SelectTrigger 
            className={cn(
              'border-0 bg-transparent shadow-none focus:ring-0 pt-6 pb-2 h-auto',
              'text-gray-900 dark:text-gray-100',
              icon && 'pl-10',
              'pr-8' // Space for chevron
            )}
            id={id}
            data-testid={testId}
            aria-describedby={error ? `${id}-error` : success ? `${id}-success` : undefined}
            aria-invalid={!!error}
            aria-required={required}
          >
            <div className="flex items-center gap-2 w-full">
              {selectedOption?.icon && (
                <div className="flex-shrink-0">
                  {selectedOption.icon}
                </div>
              )}
              <SelectValue placeholder={shouldFloat ? placeholder : ''} />
            </div>
          </SelectTrigger>
          
          <SelectContent 
            className={cn(
              'min-w-[var(--radix-select-trigger-width)]',
              'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700',
              'shadow-xl rounded-xl p-1'
            )}
          >
            {options.map((option) => (
              <SelectItem 
                key={option.value} 
                value={option.value}
                disabled={option.disabled}
                className={cn(
                  'rounded-lg px-3 py-2 cursor-pointer transition-colors duration-150',
                  'hover:bg-gray-100 dark:hover:bg-gray-700',
                  'focus:bg-gray-100 dark:focus:bg-gray-700',
                  'data-[state=checked]:bg-blue-50 dark:data-[state=checked]:bg-blue-900/30',
                  'data-[state=checked]:text-blue-700 dark:data-[state=checked]:text-blue-300'
                )}
              >
                <div className="flex items-center gap-2 w-full">
                  {option.icon && (
                    <div className="flex-shrink-0">
                      {option.icon}
                    </div>
                  )}
                  {option.color && (
                    <div 
                      className="w-3 h-3 rounded-full flex-shrink-0" 
                      style={{ backgroundColor: option.color }}
                    />
                  )}
                  <span className="truncate">{option.label}</span>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* Status icons */}
        <div className="absolute right-8 top-1/2 -translate-y-1/2 flex items-center gap-1 pointer-events-none">
          {error && (
            <AlertCircle className="w-4 h-4 text-red-500 dark:text-red-400" />
          )}
          {success && !error && (
            <CheckCircle className="w-4 h-4 text-green-500 dark:text-green-400" />
          )}
        </div>
      </div>

      {/* Error message with animation */}
      <AnimatePresence mode="wait">
        {error && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2, ease: [0.4, 0, 0.2, 1] }}
            className="mt-2"
          >
            <div 
              id={`${id}-error`}
              className="flex items-center gap-2 text-sm text-red-600 dark:text-red-400"
              role="alert"
            >
              <AlertCircle className="w-4 h-4 flex-shrink-0" />
              <span>{error}</span>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Success message with animation */}
      <AnimatePresence mode="wait">
        {success && !error && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2, ease: [0.4, 0, 0.2, 1] }}
            className="mt-2"
          >
            <div 
              id={`${id}-success`}
              className="flex items-center gap-2 text-sm text-green-600 dark:text-green-400"
              role="status"
            >
              <CheckCircle className="w-4 h-4 flex-shrink-0" />
              <span>Selection confirmed!</span>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Focus indicator line */}
      <div className={cn(
        'absolute bottom-0 left-0 h-0.5 bg-gradient-to-r transition-all duration-300 ease-out',
        error 
          ? 'from-red-500 to-red-600' 
          : success
          ? 'from-green-500 to-green-600'
          : 'from-blue-500 to-blue-600',
        isFocused ? 'w-full opacity-100' : 'w-0 opacity-0'
      )} />
    </div>
  );
};