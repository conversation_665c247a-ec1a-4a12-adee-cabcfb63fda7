import React, { useState, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { AlertCircle, CheckCircle, Info } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

interface StandardInputProps {
  label: string;
  value: string | number;
  onChange: (value: string) => void;
  type?: 'text' | 'number' | 'email' | 'password' | 'currency';
  placeholder?: string;
  error?: string;
  success?: boolean;
  helpText?: string;
  required?: boolean;
  disabled?: boolean;
  className?: string;
  id?: string;
  autoFocus?: boolean;
  prefix?: React.ReactNode;
  suffix?: React.ReactNode;
}

/**
 * StandardInput - Unified input component with floating label
 * Implements 2025 form design patterns with consistent validation
 */
export const StandardInput: React.FC<StandardInputProps> = ({
  label,
  value,
  onChange,
  type = 'text',
  placeholder,
  error,
  success,
  helpText,
  required = false,
  disabled = false,
  className,
  id,
  autoFocus = false,
  prefix,
  suffix,
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [hasValue, setHasValue] = useState(!!value);
  const inputRef = useRef<HTMLInputElement>(null);
  
  useEffect(() => {
    setHasValue(!!value);
  }, [value]);
  
  const handleFocus = () => setIsFocused(true);
  const handleBlur = () => setIsFocused(false);
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let newValue = e.target.value;
    
    // Format currency inputs
    if (type === 'currency') {
      // Remove non-numeric characters except decimal
      newValue = newValue.replace(/[^0-9.]/g, '');
      // Ensure only one decimal point
      const parts = newValue.split('.');
      if (parts.length > 2) {
        newValue = parts[0] + '.' + parts.slice(1).join('');
      }
    }
    
    onChange(newValue);
  };
  
  const formatDisplayValue = (val: string | number): string => {
    if (type === 'currency' && val && !isFocused) {
      const numValue = typeof val === 'string' ? parseFloat(val) : val;
      if (!isNaN(numValue)) {
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD',
          minimumFractionDigits: 0,
          maximumFractionDigits: 2,
        }).format(numValue);
      }
    }
    return String(val);
  };
  
  const inputId = id || `input-${label.toLowerCase().replace(/\s+/g, '-')}`;
  
  return (
    <div className={cn('relative', className)}>
      {/* Floating label */}
      <label
        htmlFor={inputId}
        className={cn(
          'absolute left-3 transition-all duration-200 pointer-events-none',
          'text-muted-foreground',
          (isFocused || hasValue) ? 
            'top-1 text-xs font-medium' : 
            'top-3.5 text-sm',
          prefix && 'left-10',
          error && 'text-destructive',
          success && 'text-success',
          disabled && 'opacity-50'
        )}
      >
        {label}
        {required && <span className="text-destructive ml-1">*</span>}
      </label>
      
      {/* Input container */}
      <div className="relative">
        {/* Prefix */}
        {prefix && (
          <div className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
            {prefix}
          </div>
        )}
        
        {/* Input field */}
        <input
          ref={inputRef}
          id={inputId}
          type={type === 'currency' ? 'text' : type}
          value={formatDisplayValue(value)}
          onChange={handleChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder={isFocused ? placeholder : ''}
          required={required}
          disabled={disabled}
          autoFocus={autoFocus}
          className={cn(
            'w-full px-3 pt-5 pb-1.5 rounded-lg',
            'border transition-all duration-200',
            'bg-background text-foreground',
            'focus:outline-none focus:ring-2 focus:ring-offset-0',
            'placeholder:text-muted-foreground/50',
            // Normal state
            !error && !success && 'border-input focus:border-primary focus:ring-primary/20',
            // Error state
            error && 'border-destructive focus:border-destructive focus:ring-destructive/20',
            // Success state
            success && 'border-success focus:border-success focus:ring-success/20',
            // Disabled state
            disabled && 'bg-muted cursor-not-allowed opacity-50',
            // With prefix/suffix
            prefix && 'pl-10',
            suffix && 'pr-10'
          )}
        />
        
        {/* Suffix */}
        {suffix && (
          <div className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground">
            {suffix}
          </div>
        )}
        
        {/* Status icons */}
        <AnimatePresence>
          {(error || success) && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              className={cn(
                'absolute right-3 top-1/2 -translate-y-1/2',
                suffix && 'right-10'
              )}
            >
              {error && <AlertCircle className="h-4 w-4 text-destructive" />}
              {success && !error && <CheckCircle className="h-4 w-4 text-success" />}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
      
      {/* Help text / Error message */}
      <AnimatePresence mode="wait">
        {(error || helpText) && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.2 }}
            className="mt-1.5 text-xs"
          >
            {error ? (
              <p className="flex items-center gap-1 text-destructive">
                <AlertCircle className="h-3 w-3" />
                {error}
              </p>
            ) : helpText ? (
              <p className="flex items-center gap-1 text-muted-foreground">
                <Info className="h-3 w-3" />
                {helpText}
              </p>
            ) : null}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

// Currency input variant
export const CurrencyInput: React.FC<Omit<StandardInputProps, 'type' | 'prefix'>> = (props) => {
  return (
    <StandardInput
      {...props}
      type="currency"
      prefix={<span className="text-muted-foreground">$</span>}
    />
  );
};

// Percentage input variant
export const PercentageInput: React.FC<Omit<StandardInputProps, 'type' | 'suffix'>> = (props) => {
  return (
    <StandardInput
      {...props}
      type="number"
      suffix={<span className="text-muted-foreground">%</span>}
    />
  );
};

export default StandardInput;