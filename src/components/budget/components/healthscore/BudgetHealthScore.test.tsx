import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { BudgetHealthScore } from './BudgetHealthScore';
import type { BudgetMetrics } from './BudgetHealthScore';

describe('BudgetHealthScore', () => {
  const mockMetrics: BudgetMetrics = {
    totalBudget: 100000,
    totalSpent: 75000,
    utilization: 75,
    burnRate: 1.1,
    projectedSpend: 90000,
    riskScore: 15,
    savingsOpportunity: 10,
    complianceRate: 95,
    efficiencyScore: 85,
    categoryDistribution: {
      'Technical Training': 40,
      'Soft Skills': 30,
      'Conferences': 20,
      'Certifications': 10
    },
    monthlyTrend: [
      { month: 'Jan', spent: 8000, budget: 8333 },
      { month: 'Feb', spent: 16000, budget: 16666 },
      { month: 'Mar', spent: 24000, budget: 25000 }
    ]
  };

  it('renders budget health score with correct values', () => {
    render(
      <BudgetHealthScore
        metrics={mockMetrics}
        loading={false}
      />
    );

    // Check if health score is displayed
    expect(screen.getByText('Budget Health Score')).toBeInTheDocument();
    
    // Check if main score is displayed (should be calculated)
    expect(screen.getByText(/out of 100/)).toBeInTheDocument();
    
    // Check if key indicators are displayed
    expect(screen.getByText('Budget Utilization')).toBeInTheDocument();
    expect(screen.getByText('Burn Rate')).toBeInTheDocument();
    expect(screen.getByText('Risk Score')).toBeInTheDocument();
    expect(screen.getByText('Compliance Rate')).toBeInTheDocument();
  });

  it('displays loading state', () => {
    render(
      <BudgetHealthScore
        metrics={mockMetrics}
        loading={true}
      />
    );

    // Should show skeleton loaders
    const skeletons = document.querySelectorAll('[class*="skeleton"]');
    expect(skeletons.length).toBeGreaterThan(0);
  });

  it('shows compact mode when specified', () => {
    render(
      <BudgetHealthScore
        metrics={mockMetrics}
        compactMode={true}
        loading={false}
      />
    );

    // Should show compact version with less detail
    expect(screen.getByText('Budget Health')).toBeInTheDocument();
    
    // Should not show detailed indicators
    expect(screen.queryByText('Budget Utilization')).not.toBeInTheDocument();
  });

  it('handles refresh action', () => {
    const handleRefresh = jest.fn();
    
    render(
      <BudgetHealthScore
        metrics={mockMetrics}
        onRefresh={handleRefresh}
        loading={false}
      />
    );

    const refreshButton = screen.getByText('Refresh');
    fireEvent.click(refreshButton);
    
    expect(handleRefresh).toHaveBeenCalled();
  });

  it('opens detailed metrics dialog', () => {
    render(
      <BudgetHealthScore
        metrics={mockMetrics}
        loading={false}
      />
    );

    const viewAllButton = screen.getByText('View All Metrics');
    fireEvent.click(viewAllButton);
    
    // Should show dialog with all metrics
    expect(screen.getByText('Detailed Budget Metrics')).toBeInTheDocument();
    expect(screen.getAllByText('Budget Utilization').length).toBeGreaterThan(1);
  });

  it('shows recommendations when available', () => {
    const criticalMetrics: BudgetMetrics = {
      ...mockMetrics,
      utilization: 95,
      burnRate: 1.5,
      riskScore: 60
    };

    render(
      <BudgetHealthScore
        metrics={criticalMetrics}
        loading={false}
      />
    );

    // Should show recommendations section
    expect(screen.getByText('Recommendations')).toBeInTheDocument();
    expect(screen.getByText(/Budget Limit Approaching/)).toBeInTheDocument();
  });

  it('calculates health score correctly', () => {
    render(
      <BudgetHealthScore
        metrics={mockMetrics}
        loading={false}
      />
    );

    // With the given metrics, score should be in good range (60-80)
    const scoreElement = screen.getByText(/^\d+$/);
    const score = parseInt(scoreElement.textContent || '0');
    expect(score).toBeGreaterThanOrEqual(60);
    expect(score).toBeLessThanOrEqual(100);
  });

  it('shows different status based on score', () => {
    // Test excellent score
    const excellentMetrics: BudgetMetrics = {
      ...mockMetrics,
      utilization: 70,
      burnRate: 1.0,
      riskScore: 10,
      efficiencyScore: 95
    };

    const { rerender } = render(
      <BudgetHealthScore
        metrics={excellentMetrics}
        loading={false}
      />
    );

    expect(screen.getByText(/excellent/i)).toBeInTheDocument();

    // Test critical score
    const criticalMetrics: BudgetMetrics = {
      ...mockMetrics,
      utilization: 110,
      burnRate: 1.8,
      riskScore: 80,
      efficiencyScore: 30
    };

    rerender(
      <BudgetHealthScore
        metrics={criticalMetrics}
        loading={false}
      />
    );

    expect(screen.getByText(/critical/i)).toBeInTheDocument();
  });
});