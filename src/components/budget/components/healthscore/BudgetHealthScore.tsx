import React, { useMemo } from 'react';
import { motion } from 'framer-motion';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  HeartPulse,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  Info,
  Activity,
  Target,
  Calendar,
  DollarSign,
  FileText,
  ChevronRight,
  Shield,
  AlertCircle,
  Zap
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { formatCurrency, formatPercentage } from '../../utils/formatters';

export interface BudgetMetrics {
  totalBudget: number;
  totalSpent: number;
  utilization: number;
  burnRate: number;
  projectedSpend: number;
  riskScore: number;
  savingsOpportunity: number;
  complianceRate: number;
  efficiencyScore: number;
  categoryDistribution: Record<string, number>;
  monthlyTrend: { month: string; spent: number; budget: number }[];
}

export interface HealthIndicator {
  label: string;
  value: number;
  status: 'excellent' | 'good' | 'warning' | 'critical';
  description: string;
  recommendation?: string;
  icon: React.ReactNode;
}

export interface BudgetHealthScoreProps {
  metrics: BudgetMetrics | null;
  onRefresh?: () => void;
  onViewDetails?: (metric: string) => void;
  showRecommendations?: boolean;
  compactMode?: boolean;
  loading?: boolean;
}

const calculateHealthScore = (metrics: BudgetMetrics | null | undefined): number => {
  if (!metrics) return 0;
  
  // Weight each factor
  const weights = {
    utilization: 0.25,
    burnRate: 0.20,
    riskScore: 0.15,
    complianceRate: 0.15,
    efficiencyScore: 0.15,
    savings: 0.10
  };

  // Calculate individual scores (0-100) with null checks
  const utilization = metrics.utilization ?? 0;
  const utilizationScore = utilization <= 85 ? 100 : 
    utilization <= 95 ? 75 : 
    utilization <= 100 ? 50 : 25;

  const burnRate = metrics.burnRate ?? 1;
  const burnRateScore = Math.abs(burnRate - 1) <= 0.1 ? 100 :
    Math.abs(burnRate - 1) <= 0.2 ? 75 :
    Math.abs(burnRate - 1) <= 0.3 ? 50 : 25;

  const riskScore = 100 - (metrics.riskScore ?? 0);

  const complianceScore = metrics.complianceRate ?? 100;

  const efficiencyScore = metrics.efficiencyScore ?? 100;

  const savingsOpportunity = metrics.savingsOpportunity ?? 0;
  const savingsScore = savingsOpportunity >= 10 ? 100 :
    savingsOpportunity >= 5 ? 75 :
    savingsOpportunity >= 2 ? 50 : 25;

  // Calculate weighted total
  const totalScore = 
    (utilizationScore * weights.utilization) +
    (burnRateScore * weights.burnRate) +
    (riskScore * weights.riskScore) +
    (complianceScore * weights.complianceRate) +
    (efficiencyScore * weights.efficiencyScore) +
    (savingsScore * weights.savings);

  return Math.round(totalScore);
};

const getHealthStatus = (score: number): {
  status: 'excellent' | 'good' | 'warning' | 'critical';
  color: string;
  icon: React.ReactNode;
  message: string;
} => {
  if (score >= 80) {
    return {
      status: 'excellent',
      color: 'text-green-500',
      icon: <CheckCircle className="h-5 w-5" />,
      message: 'Budget health is excellent'
    };
  } else if (score >= 60) {
    return {
      status: 'good',
      color: 'text-blue-500',
      icon: <Activity className="h-5 w-5" />,
      message: 'Budget health is good'
    };
  } else if (score >= 40) {
    return {
      status: 'warning',
      color: 'text-orange-500',
      icon: <AlertTriangle className="h-5 w-5" />,
      message: 'Budget needs attention'
    };
  } else {
    return {
      status: 'critical',
      color: 'text-red-500',
      icon: <AlertCircle className="h-5 w-5" />,
      message: 'Budget health is critical'
    };
  }
};

const getIndicatorStatus = (
  value: number,
  thresholds: { excellent: number; good: number; warning: number }
): 'excellent' | 'good' | 'warning' | 'critical' => {
  if (value >= thresholds.excellent) return 'excellent';
  if (value >= thresholds.good) return 'good';
  if (value >= thresholds.warning) return 'warning';
  return 'critical';
};

export const BudgetHealthScore: React.FC<BudgetHealthScoreProps> = ({
  metrics,
  onRefresh,
  onViewDetails,
  showRecommendations = true,
  compactMode = false,
  loading = false
}) => {
  const healthScore = useMemo(() => calculateHealthScore(metrics), [metrics]);
  const healthStatus = useMemo(() => getHealthStatus(healthScore), [healthScore]);

  const indicators: HealthIndicator[] = useMemo(() => {
    if (!metrics) return [];
    
    const utilization = metrics.utilization ?? 0;
    const burnRate = metrics.burnRate ?? 1;
    
    return [
      {
        label: 'Budget Utilization',
        value: utilization,
        status: getIndicatorStatus(100 - utilization, { excellent: 20, good: 10, warning: 5 }),
        description: `${formatPercentage(utilization)}% of budget used`,
        recommendation: utilization > 90 ? 'Consider requesting budget increase' : undefined,
        icon: <Target className="h-4 w-4" />
      },
      {
        label: 'Burn Rate',
        value: burnRate * 100,
        status: getIndicatorStatus(100 - Math.abs(burnRate - 1) * 100, { excellent: 90, good: 80, warning: 70 }),
        description: `${burnRate.toFixed(2)}x expected rate`,
        recommendation: burnRate > 1.2 ? 'Spending faster than planned' : undefined,
        icon: <Zap className="h-4 w-4" />
      },
      {
        label: 'Risk Score',
        value: metrics.riskScore ?? 0,
        status: getIndicatorStatus(100 - (metrics.riskScore ?? 0), { excellent: 80, good: 60, warning: 40 }),
        description: `${metrics.riskScore ?? 0}% risk level`,
        recommendation: (metrics.riskScore ?? 0) > 50 ? 'Review high-risk areas' : undefined,
        icon: <Shield className="h-4 w-4" />
      },
      {
        label: 'Compliance Rate',
        value: metrics.complianceRate ?? 100,
        status: getIndicatorStatus(metrics.complianceRate ?? 100, { excellent: 95, good: 85, warning: 75 }),
        description: `${formatPercentage(metrics.complianceRate ?? 100)}% compliant`,
        recommendation: (metrics.complianceRate ?? 100) < 90 ? 'Review compliance policies' : undefined,
        icon: <FileText className="h-4 w-4" />
      },
      {
        label: 'Efficiency Score',
        value: metrics.efficiencyScore ?? 100,
        status: getIndicatorStatus(metrics.efficiencyScore ?? 100, { excellent: 85, good: 70, warning: 55 }),
        description: `${metrics.efficiencyScore ?? 100}% efficiency`,
        recommendation: (metrics.efficiencyScore ?? 100) < 75 ? 'Optimize spending patterns' : undefined,
        icon: <Activity className="h-4 w-4" />
      },
      {
        label: 'Savings Opportunity',
        value: metrics.savingsOpportunity ?? 0,
        status: getIndicatorStatus(metrics.savingsOpportunity ?? 0, { excellent: 10, good: 5, warning: 2 }),
        description: `${formatPercentage(metrics.savingsOpportunity ?? 0)}% potential savings`,
        recommendation: (metrics.savingsOpportunity ?? 0) > 5 ? 'Explore optimization options' : undefined,
        icon: <DollarSign className="h-4 w-4" />
      }
    ];
  }, [metrics]);

  const topRecommendations = useMemo(() => {
    if (!metrics) return [];
    
    const recommendations = [];

    if ((metrics.utilization ?? 0) > 90) {
      recommendations.push({
        priority: 'high',
        title: 'Budget Limit Approaching',
        action: 'Review and prioritize remaining expenses',
        impact: 'Prevent overspending'
      });
    }

    if ((metrics.burnRate ?? 1) > 1.2) {
      recommendations.push({
        priority: 'high',
        title: 'High Burn Rate Detected',
        action: 'Slow down non-critical spending',
        impact: 'Extend budget runway'
      });
    }

    if ((metrics.riskScore ?? 0) > 50) {
      recommendations.push({
        priority: 'medium',
        title: 'Elevated Risk Level',
        action: 'Implement risk mitigation strategies',
        impact: 'Reduce budget volatility'
      });
    }

    if ((metrics.efficiencyScore ?? 100) < 75) {
      recommendations.push({
        priority: 'medium',
        title: 'Efficiency Improvement Needed',
        action: 'Consolidate vendors and negotiate rates',
        impact: `Save up to ${formatCurrency((metrics.totalBudget ?? 0) * (metrics.savingsOpportunity ?? 0) / 100)}`
      });
    }

    return recommendations.slice(0, 3);
  }, [metrics]);

  if (loading) {
    return (
      <Card className="p-6">
        <Skeleton className="h-8 w-48 mb-4" />
        <Skeleton className="h-32 w-full mb-4" />
        <div className="space-y-2">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-3/4" />
        </div>
      </Card>
    );
  }

  if (!metrics) {
    return (
      <Card className="p-6">
        <div className="flex items-center justify-center h-32">
          <p className="text-muted-foreground">No budget health data available</p>
        </div>
      </Card>
    );
  }

  if (compactMode) {
    return (
      <Card className="p-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className={cn("p-1.5 rounded-lg", healthStatus.color, "bg-opacity-10")}>
              <HeartPulse className="h-4 w-4" />
            </div>
            <div>
              <p className="text-xs text-muted-foreground">Budget Health</p>
              <div className="flex items-center gap-2">
                <span className="text-xl font-bold">{healthScore}</span>
                <Badge variant={healthStatus.status === 'excellent' ? 'default' : 
                  healthStatus.status === 'good' ? 'secondary' : 
                  healthStatus.status === 'warning' ? 'secondary' : 'destructive'}
                  className="text-xs px-2 py-0">
                  {healthStatus.status}
                </Badge>
              </div>
            </div>
          </div>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="icon" className="h-7 w-7">
                  <Info className="h-3 w-3" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>{healthStatus.message}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </Card>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      data-testid="budget-health-score"
    >
      <Card className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <HeartPulse className="h-5 w-5 text-muted-foreground" />
            <h3 className="text-lg font-semibold">Budget Health Score</h3>
          </div>
          {onRefresh && (
            <Button variant="ghost" size="sm" onClick={onRefresh}>
              <Activity className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          )}
        </div>

        {/* Main Score Display */}
        <div className="flex items-center justify-center mb-8">
          <div className="relative">
            <div className="w-32 h-32 rounded-full bg-muted flex items-center justify-center">
              <div className="text-center">
                <div className={cn("text-4xl font-bold", healthStatus.color)}>
                  {healthScore}
                </div>
                <div className="text-sm text-muted-foreground">out of 100</div>
              </div>
            </div>
            <div className={cn("absolute -bottom-2 left-1/2 -translate-x-1/2 flex items-center gap-1 px-3 py-1 rounded-full", 
              healthStatus.color, "bg-opacity-10")}>
              {healthStatus.icon}
              <span className="text-sm font-medium capitalize">{healthStatus.status}</span>
            </div>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mb-6">
          <div className="relative h-3 w-full overflow-hidden rounded-full bg-secondary">
            <div 
              className={cn(
                "h-full w-full flex-1 transition-all",
                healthScore >= 80 ? "bg-green-500" :
                healthScore >= 60 ? "bg-blue-500" :
                healthScore >= 40 ? "bg-orange-500" : "bg-red-500"
              )}
              style={{ transform: `translateX(-${100 - (healthScore || 0)}%)` }}
            />
          </div>
        </div>

        {/* Key Indicators */}
        <div className="grid grid-cols-2 gap-4 mb-6">
          {indicators.slice(0, 4).map((indicator) => (
            <div
              key={indicator.label}
              className="flex items-start gap-3 p-3 rounded-lg bg-muted/30 cursor-pointer hover:bg-muted/50"
              onClick={() => onViewDetails?.(indicator.label)}
            >
              <div className={cn("p-2 rounded-lg", 
                indicator.status === 'excellent' ? "bg-green-500/10 text-green-500" :
                indicator.status === 'good' ? "bg-blue-500/10 text-blue-500" :
                indicator.status === 'warning' ? "bg-orange-500/10 text-orange-500" :
                "bg-red-500/10 text-red-500"
              )}>
                {indicator.icon}
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium">{indicator.label}</p>
                <p className="text-xs text-muted-foreground">{indicator.description}</p>
                {indicator.recommendation && (
                  <p className="text-xs text-orange-500 mt-1">{indicator.recommendation}</p>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Detailed Metrics Dialog */}
        <Dialog>
          <DialogTrigger asChild>
            <Button variant="outline" className="w-full">
              View All Metrics
              <ChevronRight className="h-4 w-4 ml-2" />
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Detailed Budget Metrics</DialogTitle>
              <DialogDescription>
                Comprehensive view of all budget health indicators
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 mt-4">
              {indicators.map((indicator) => (
                <div key={indicator.label} className="p-4 rounded-lg border">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      {indicator.icon}
                      <span className="font-medium">{indicator.label}</span>
                    </div>
                    <Badge variant={
                      indicator.status === 'excellent' ? 'default' :
                      indicator.status === 'good' ? 'secondary' :
                      indicator.status === 'warning' ? 'secondary' :
                      'destructive'
                    }>
                      {indicator.status}
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground mb-2">{indicator.description}</p>
                  <div className="relative h-2 w-full overflow-hidden rounded-full bg-secondary">
                    <div 
                      className={cn(
                        "h-full w-full flex-1 transition-all",
                        indicator.status === 'excellent' ? "bg-green-500" :
                        indicator.status === 'good' ? "bg-blue-500" :
                        indicator.status === 'warning' ? "bg-orange-500" :
                        "bg-red-500"
                      )}
                      style={{ transform: `translateX(-${100 - (indicator.value || 0)}%)` }}
                    />
                  </div>
                  {indicator.recommendation && (
                    <p className="text-xs text-orange-500 mt-2 flex items-center gap-1">
                      <Info className="h-3 w-3" />
                      {indicator.recommendation}
                    </p>
                  )}
                </div>
              ))}
            </div>
          </DialogContent>
        </Dialog>

        {/* Recommendations */}
        {showRecommendations && topRecommendations.length > 0 && (
          <div className="mt-6 space-y-3">
            <h4 className="text-sm font-medium text-muted-foreground">Recommendations</h4>
            {topRecommendations.map((rec, index) => (
              <div
                key={index}
                className="flex items-start gap-3 p-3 rounded-lg bg-muted/30"
              >
                <div className={cn("p-1 rounded", 
                  rec.priority === 'high' ? "bg-red-500/10 text-red-500" :
                  "bg-orange-500/10 text-orange-500"
                )}>
                  {rec.priority === 'high' ? 
                    <AlertTriangle className="h-4 w-4" /> :
                    <Info className="h-4 w-4" />
                  }
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium">{rec.title}</p>
                  <p className="text-xs text-muted-foreground">{rec.action}</p>
                  <p className="text-xs text-green-500 mt-1">Impact: {rec.impact}</p>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Trend Indicator */}
        <div className="mt-6 flex items-center justify-between text-sm">
          <span className="text-muted-foreground">vs Last Month</span>
          <div className="flex items-center gap-1">
            {healthScore > 75 ? (
              <>
                <TrendingUp className="h-4 w-4 text-green-500" />
                <span className="text-green-500">+5 points</span>
              </>
            ) : (
              <>
                <TrendingDown className="h-4 w-4 text-red-500" />
                <span className="text-red-500">-3 points</span>
              </>
            )}
          </div>
        </div>
      </Card>
    </motion.div>
  );
};