import React from 'react';
import { motion, HTMLMotionProps } from 'framer-motion';
import { cn } from '@/lib/utils';

interface SoftCardProps extends HTMLMotionProps<"div"> {
  variant?: 'default' | 'gradient' | 'glass' | 'neumorphic';
  glowColor?: string;
  children: React.ReactNode;
  className?: string;
}

export const SoftCard: React.FC<SoftCardProps> = ({
  variant = 'default',
  glowColor = 'rgba(99, 102, 241, 0.3)',
  children,
  className,
  ...props
}) => {
  const variants = {
    default: "bg-white/80 shadow-soft",
    gradient: "bg-gradient-to-br from-white/90 to-white/60",
    glass: "bg-white/30 backdrop-blur-2xl backdrop-saturate-200",
    neumorphic: "bg-gray-50 shadow-neumorphic-up hover:shadow-neumorphic-down",
  };

  return (
    <motion.div
      className={cn(
        "relative rounded-2xl p-6",
        "border border-white/20",
        "transition-all duration-300",
        variants[variant],
        className
      )}
      whileHover={{
        scale: 1.02,
        boxShadow: `0 20px 40px -15px ${glowColor}`,
      }}
      transition={{ type: "spring", stiffness: 300, damping: 20 }}
      {...props}
    >
      {/* Soft inner glow */}
      <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-white/50 via-transparent to-transparent opacity-50 pointer-events-none" />
      
      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>
    </motion.div>
  );
};

// Animated Counter Component
export const AnimatedCounter: React.FC<{
  value: number;
  prefix?: string;
  suffix?: string;
  duration?: number;
  className?: string;
  decimals?: number;
}> = ({ value, prefix = '', suffix = '', duration = 2, className, decimals = 0 }) => {
  const [count, setCount] = React.useState(0);
  
  React.useEffect(() => {
    let startTime: number | null = null;
    let animationFrame: number;
    
    const animate = (timestamp: number) => {
      if (!startTime) startTime = timestamp;
      const progress = Math.min((timestamp - startTime) / (duration * 1000), 1);
      
      // Easing function for smooth animation
      const easeOutQuart = 1 - Math.pow(1 - progress, 4);
      setCount(value * easeOutQuart);
      
      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate);
      }
    };
    
    animationFrame = requestAnimationFrame(animate);
    
    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
    };
  }, [value, duration]);

  const formattedCount = decimals > 0 
    ? count.toFixed(decimals)
    : Math.floor(count).toLocaleString();

  return (
    <span className={cn("tabular-nums", className)}>
      {prefix}{formattedCount}{suffix}
    </span>
  );
};

// Soft Button Component
export const SoftButton: React.FC<{
  variant?: 'primary' | 'secondary' | 'ghost' | 'gradient';
  size?: 'sm' | 'md' | 'lg';
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
  disabled?: boolean;
}> = ({ variant = 'primary', size = 'md', children, className, onClick, disabled = false }) => {
  const variants = {
    primary: "bg-gradient-to-r from-indigo-500 to-purple-600 text-white shadow-soft-primary",
    secondary: "bg-white/80 text-gray-700 shadow-soft border border-gray-200/50",
    ghost: "bg-transparent hover:bg-white/50 text-gray-600",
    gradient: "bg-gradient-to-r from-pink-500 to-rose-500 text-white shadow-soft-gradient",
  };

  const sizes = {
    sm: "px-3 py-1.5 text-sm",
    md: "px-4 py-2 text-base",
    lg: "px-6 py-3 text-lg",
  };

  return (
    <motion.button
      className={cn(
        "relative rounded-xl font-medium",
        "transition-all duration-200",
        "hover:scale-105 active:scale-95",
        "disabled:opacity-50 disabled:cursor-not-allowed",
        variants[variant],
        sizes[size],
        className
      )}
      whileHover={!disabled ? { y: -2 } : {}}
      whileTap={!disabled ? { scale: 0.98 } : {}}
      onClick={onClick}
      disabled={disabled}
    >
      <span className="relative z-10">{children}</span>
      {variant === 'primary' && (
        <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-indigo-600 to-purple-700 opacity-0 hover:opacity-100 transition-opacity duration-200" />
      )}
    </motion.button>
  );
};

// Progress Bar Component
export const SoftProgressBar: React.FC<{
  value: number;
  max?: number;
  className?: string;
  showLabel?: boolean;
  color?: 'primary' | 'success' | 'warning' | 'danger';
}> = ({ value, max = 100, className, showLabel = true, color = 'primary' }) => {
  const percentage = Math.min((value / max) * 100, 100);
  
  const colors = {
    primary: 'from-indigo-500 to-purple-600',
    success: 'from-green-500 to-emerald-600',
    warning: 'from-amber-500 to-orange-600',
    danger: 'from-red-500 to-rose-600',
  };

  return (
    <div className={cn("relative", className)}>
      <div className="w-full bg-gray-200/50 rounded-full h-2 overflow-hidden backdrop-blur-sm">
        <motion.div
          className={cn(
            "h-full bg-gradient-to-r rounded-full",
            colors[color]
          )}
          initial={{ width: 0 }}
          animate={{ width: `${percentage}%` }}
          transition={{ duration: 1, ease: [0.23, 1, 0.32, 1] }}
        />
      </div>
      {showLabel && (
        <span className="absolute -top-6 right-0 text-xs text-gray-600">
          {percentage.toFixed(0)}%
        </span>
      )}
    </div>
  );
};

// Soft Badge Component
export const SoftBadge: React.FC<{
  variant?: 'default' | 'success' | 'warning' | 'danger' | 'info';
  children: React.ReactNode;
  className?: string;
}> = ({ variant = 'default', children, className }) => {
  const variants = {
    default: 'bg-gray-100 text-gray-700',
    success: 'bg-green-100 text-green-700',
    warning: 'bg-amber-100 text-amber-700',
    danger: 'bg-red-100 text-red-700',
    info: 'bg-blue-100 text-blue-700',
  };

  return (
    <span
      className={cn(
        "inline-flex items-center px-2.5 py-0.5 rounded-lg text-xs font-medium",
        "backdrop-blur-sm border border-white/20",
        variants[variant],
        className
      )}
    >
      {children}
    </span>
  );
};

// Skeleton Loader with Soft UI
export const SoftSkeleton: React.FC<{
  className?: string;
  variant?: 'text' | 'circular' | 'rectangular';
}> = ({ className, variant = 'rectangular' }) => {
  const variants = {
    text: 'h-4 rounded',
    circular: 'rounded-full',
    rectangular: 'rounded-lg',
  };

  return (
    <div
      className={cn(
        "animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200",
        "background-size-200 bg-[length:200%_100%]",
        variants[variant],
        className
      )}
      style={{
        backgroundSize: '200% 100%',
        animation: 'shimmer 2s ease-in-out infinite',
      }}
    />
  );
};

// Floating Action Button
export const SoftFAB: React.FC<{
  icon: React.ReactNode;
  onClick?: () => void;
  className?: string;
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
}> = ({ icon, onClick, className, position = 'bottom-right' }) => {
  const positions = {
    'bottom-right': 'bottom-6 right-6',
    'bottom-left': 'bottom-6 left-6',
    'top-right': 'top-6 right-6',
    'top-left': 'top-6 left-6',
  };

  return (
    <motion.button
      className={cn(
        "fixed z-50 w-14 h-14 rounded-full",
        "bg-gradient-to-r from-indigo-500 to-purple-600",
        "text-white shadow-soft-primary",
        "flex items-center justify-center",
        "hover:shadow-lg transition-shadow",
        positions[position],
        className
      )}
      whileHover={{ scale: 1.1, rotate: 90 }}
      whileTap={{ scale: 0.9 }}
      onClick={onClick}
    >
      {icon}
    </motion.button>
  );
};