import React from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Calendar, ChevronLeft, ChevronRight } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface YearSelectorProps {
  year: number;
  onYearChange: (year: number) => void;
  minYear?: number;
  maxYear?: number;
}

export const YearSelector = React.memo<YearSelectorProps>(({
  year,
  onYearChange,
  minYear = 2020,
  maxYear = new Date().getFullYear() + 1,
}) => {
  const canGoPrevious = year > minYear;
  const canGoNext = year < maxYear;

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'ArrowLeft' && canGoPrevious) {
      onYearChange(year - 1);
    } else if (e.key === 'ArrowRight' && canGoNext) {
      onYearChange(year + 1);
    }
  };

  return (
    <motion.div 
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ delay: 0.1 }}
      className="flex items-center gap-4"
      role="navigation"
      aria-label="Year navigation"
      onKeyDown={handleKeyDown}
    >
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onYearChange(year - 1)}
              disabled={!canGoPrevious}
              aria-label={`Go to year ${year - 1}`}
            >
              <ChevronLeft className="h-4 w-4 mr-1" />
              {year - 1}
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>View previous year's budget</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      <div className="flex items-center gap-2">
        <Calendar className="h-5 w-5 text-muted-foreground" aria-hidden="true" />
        <span className="font-semibold text-lg" aria-live="polite" aria-atomic="true">
          {year}
        </span>
      </div>

      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onYearChange(year + 1)}
              disabled={!canGoNext}
              aria-label={`Go to year ${year + 1}`}
            >
              {year + 1}
              <ChevronRight className="h-4 w-4 ml-1" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>View next year's budget</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </motion.div>
  );
});

YearSelector.displayName = 'YearSelector';