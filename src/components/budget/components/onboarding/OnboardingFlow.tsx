import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  ChevronRight, 
  ChevronLeft,
  Check,
  X,
  Sparkles,
  Target,
  TrendingUp,
  <PERSON><PERSON>hart,
  FileText,
  Settings
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { cn } from '@/lib/utils';
import { Card } from '@/components/ui/card';

interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  content: React.ReactNode;
  action?: {
    label: string;
    onClick: () => void;
  };
  validation?: () => boolean;
}

interface OnboardingFlowProps {
  steps: OnboardingStep[];
  onComplete: () => void;
  onSkip?: () => void;
  className?: string;
}

export const OnboardingFlow: React.FC<OnboardingFlowProps> = ({
  steps = [],
  onComplete,
  onSkip,
  className,
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<Set<number>>(new Set());
  const [isExiting, setIsExiting] = useState(false);

  // Return early if no steps provided
  if (!steps || steps.length === 0) {
    console.warn('OnboardingFlow: No steps provided');
    onComplete();
    return null;
  }

  const progress = ((currentStep + 1) / steps.length) * 100;
  const isLastStep = currentStep === steps.length - 1;
  const currentStepData = steps[currentStep];

  const handleNext = () => {
    if (currentStepData.validation && !currentStepData.validation()) {
      return;
    }

    setCompletedSteps(prev => new Set(prev).add(currentStep));

    if (isLastStep) {
      setIsExiting(true);
      setTimeout(onComplete, 300);
    } else {
      setCurrentStep(prev => prev + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(prev => prev - 1);
    }
  };

  const handleSkip = () => {
    setIsExiting(true);
    setTimeout(() => {
      onSkip?.();
    }, 300);
  };

  return (
    <AnimatePresence mode="wait">
      {!isExiting && (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          className={cn(
            "fixed inset-0 z-50 flex items-center justify-center p-4",
            "bg-background/80 backdrop-blur-sm",
            className
          )}
        >
          <Card className="w-full max-w-2xl">
            <div className="p-6 space-y-6">
              {/* Header */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Sparkles className="h-5 w-5 text-primary" />
                  <h2 className="text-lg font-semibold">Welcome to Budget Manager</h2>
                </div>
                {onSkip && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleSkip}
                  >
                    Skip
                  </Button>
                )}
              </div>

              {/* Progress */}
              <div className="space-y-2">
                <div className="flex items-center justify-between text-xs text-muted-foreground">
                  <span>Step {currentStep + 1} of {steps.length}</span>
                  <span>{Math.round(progress)}% Complete</span>
                </div>
                <Progress value={progress} className="h-2" />
              </div>

              {/* Step Content */}
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentStep}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.2 }}
                  className="space-y-4"
                >
                  <div className="flex items-start gap-4">
                    <div className="p-3 rounded-lg bg-primary/10 text-primary">
                      {currentStepData.icon}
                    </div>
                    <div className="flex-1 space-y-2">
                      <h3 className="font-semibold text-lg">{currentStepData.title}</h3>
                      <p className="text-sm text-muted-foreground">
                        {currentStepData.description}
                      </p>
                    </div>
                  </div>

                  <div className="min-h-[200px]">
                    {currentStepData.content}
                  </div>

                  {currentStepData.action && (
                    <div className="flex justify-center">
                      <Button
                        onClick={currentStepData.action.onClick}
                        className="min-w-[200px]"
                      >
                        {currentStepData.action.label}
                      </Button>
                    </div>
                  )}
                </motion.div>
              </AnimatePresence>

              {/* Navigation */}
              <div className="flex items-center justify-between pt-4 border-t">
                <Button
                  variant="outline"
                  onClick={handlePrevious}
                  disabled={currentStep === 0}
                >
                  <ChevronLeft className="h-4 w-4 mr-1" />
                  Previous
                </Button>

                <div className="flex gap-1">
                  {steps.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentStep(index)}
                      className={cn(
                        "w-2 h-2 rounded-full transition-all",
                        index === currentStep
                          ? "w-8 bg-primary"
                          : completedSteps.has(index)
                          ? "bg-primary/50"
                          : "bg-muted"
                      )}
                      aria-label={`Go to step ${index + 1}`}
                    />
                  ))}
                </div>

                <Button onClick={handleNext}>
                  {isLastStep ? (
                    <>
                      Complete
                      <Check className="h-4 w-4 ml-1" />
                    </>
                  ) : (
                    <>
                      Next
                      <ChevronRight className="h-4 w-4 ml-1" />
                    </>
                  )}
                </Button>
              </div>
            </div>
          </Card>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

// Interactive onboarding tour
interface TourStep {
  target: string; // CSS selector
  title: string;
  content: string;
  placement?: 'top' | 'bottom' | 'left' | 'right';
  highlightPadding?: number;
}

interface InteractiveTourProps {
  steps: TourStep[];
  onComplete: () => void;
  onSkip?: () => void;
}

export const InteractiveTour: React.FC<InteractiveTourProps> = ({
  steps,
  onComplete,
  onSkip,
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [highlightPosition, setHighlightPosition] = useState<DOMRect | null>(null);

  const currentTourStep = steps[currentStep];
  const isLastStep = currentStep === steps.length - 1;

  useEffect(() => {
    if (currentTourStep?.target) {
      const element = document.querySelector(currentTourStep.target);
      if (element) {
        const rect = element.getBoundingClientRect();
        setHighlightPosition(rect);
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    }
  }, [currentTourStep]);

  const handleNext = () => {
    if (isLastStep) {
      onComplete();
    } else {
      setCurrentStep(prev => prev + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(prev => prev - 1);
    }
  };

  const getTooltipPosition = () => {
    if (!highlightPosition) return { top: '50%', left: '50%' };

    const placement = currentTourStep.placement || 'bottom';
    const padding = 16;

    switch (placement) {
      case 'top':
        return {
          top: highlightPosition.top - padding,
          left: highlightPosition.left + highlightPosition.width / 2,
          transform: 'translate(-50%, -100%)',
        };
      case 'bottom':
        return {
          top: highlightPosition.bottom + padding,
          left: highlightPosition.left + highlightPosition.width / 2,
          transform: 'translateX(-50%)',
        };
      case 'left':
        return {
          top: highlightPosition.top + highlightPosition.height / 2,
          left: highlightPosition.left - padding,
          transform: 'translate(-100%, -50%)',
        };
      case 'right':
        return {
          top: highlightPosition.top + highlightPosition.height / 2,
          left: highlightPosition.right + padding,
          transform: 'translateY(-50%)',
        };
      default:
        return {
          top: highlightPosition.bottom + padding,
          left: highlightPosition.left + highlightPosition.width / 2,
          transform: 'translateX(-50%)',
        };
    }
  };

  if (!currentTourStep) return null;

  return (
    <>
      {/* Overlay */}
      <div className="fixed inset-0 z-40">
        <div className="absolute inset-0 bg-black/60" />
        {highlightPosition && (
          <div
            className="absolute border-2 border-primary rounded-lg"
            style={{
              top: highlightPosition.top - (currentTourStep.highlightPadding || 4),
              left: highlightPosition.left - (currentTourStep.highlightPadding || 4),
              width: highlightPosition.width + (currentTourStep.highlightPadding || 4) * 2,
              height: highlightPosition.height + (currentTourStep.highlightPadding || 4) * 2,
            }}
          />
        )}
      </div>

      {/* Tooltip */}
      <div
        className="fixed z-50 max-w-sm"
        style={getTooltipPosition()}
      >
        <Card className="p-4 shadow-xl">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-xs text-muted-foreground">
                {currentStep + 1} / {steps.length}
              </span>
              {onSkip && (
                <button
                  onClick={onSkip}
                  className="text-muted-foreground hover:text-foreground"
                >
                  <X className="h-4 w-4" />
                </button>
              )}
            </div>

            <div>
              <h4 className="font-semibold mb-1">{currentTourStep.title}</h4>
              <p className="text-sm text-muted-foreground">
                {currentTourStep.content}
              </p>
            </div>

            <div className="flex gap-2">
              <Button
                size="sm"
                variant="outline"
                onClick={handlePrevious}
                disabled={currentStep === 0}
              >
                Back
              </Button>
              <Button size="sm" onClick={handleNext} className="flex-1">
                {isLastStep ? 'Finish' : 'Next'}
              </Button>
            </div>
          </div>
        </Card>
      </div>
    </>
  );
};

// Predefined onboarding steps
export const defaultOnboardingSteps: OnboardingStep[] = [
  {
    id: 'welcome',
    title: 'Welcome to Budget Manager',
    description: 'Let\'s get you started with managing your budgets effectively',
    icon: <Sparkles className="h-5 w-5" />,
    content: (
      <div className="space-y-4">
        <p className="text-sm">
          This quick tour will help you understand the key features and get you up and running in minutes.
        </p>
        <div className="grid grid-cols-2 gap-4">
          <div className="p-3 rounded-lg bg-accent">
            <Target className="h-5 w-5 mb-2 text-primary" />
            <p className="text-sm font-medium">Set Goals</p>
            <p className="text-xs text-muted-foreground">Define budget targets</p>
          </div>
          <div className="p-3 rounded-lg bg-accent">
            <TrendingUp className="h-5 w-5 mb-2 text-primary" />
            <p className="text-sm font-medium">Track Progress</p>
            <p className="text-xs text-muted-foreground">Monitor spending</p>
          </div>
          <div className="p-3 rounded-lg bg-accent">
            <PieChart className="h-5 w-5 mb-2 text-primary" />
            <p className="text-sm font-medium">Analyze</p>
            <p className="text-xs text-muted-foreground">Get insights</p>
          </div>
          <div className="p-3 rounded-lg bg-accent">
            <FileText className="h-5 w-5 mb-2 text-primary" />
            <p className="text-sm font-medium">Report</p>
            <p className="text-xs text-muted-foreground">Generate reports</p>
          </div>
        </div>
      </div>
    ),
  },
  {
    id: 'setup-budget',
    title: 'Create Your First Budget',
    description: 'Set up your annual budget and allocate funds to different categories',
    icon: <Target className="h-5 w-5" />,
    content: (
      <div className="space-y-4">
        <p className="text-sm">
          Start by setting your total annual budget. You can then allocate funds to different departments and categories.
        </p>
        <div className="p-4 border rounded-lg bg-accent/20">
          <p className="text-sm font-medium mb-2">Quick Tips:</p>
          <ul className="space-y-1 text-xs text-muted-foreground">
            <li>• Use historical data to inform your budget</li>
            <li>• Consider seasonal variations</li>
            <li>• Leave room for unexpected expenses</li>
            <li>• Review and adjust quarterly</li>
          </ul>
        </div>
      </div>
    ),
    action: {
      label: 'Create Budget',
      onClick: () => console.log('Create budget'),
    },
  },
  {
    id: 'track-expenses',
    title: 'Track Your Expenses',
    description: 'Learn how to add and manage expenses efficiently',
    icon: <TrendingUp className="h-5 w-5" />,
    content: (
      <div className="space-y-4">
        <p className="text-sm">
          Track expenses in real-time to stay within budget. You can add expenses manually or import them in bulk.
        </p>
        <div className="grid grid-cols-3 gap-2">
          <button className="p-3 text-center border rounded-lg hover:bg-accent transition-colors">
            <p className="text-xs font-medium">Quick Entry</p>
          </button>
          <button className="p-3 text-center border rounded-lg hover:bg-accent transition-colors">
            <p className="text-xs font-medium">Bulk Import</p>
          </button>
          <button className="p-3 text-center border rounded-lg hover:bg-accent transition-colors">
            <p className="text-xs font-medium">Templates</p>
          </button>
        </div>
      </div>
    ),
  },
  {
    id: 'customize',
    title: 'Customize Your Dashboard',
    description: 'Personalize your experience for maximum productivity',
    icon: <Settings className="h-5 w-5" />,
    content: (
      <div className="space-y-4">
        <p className="text-sm">
          Customize your dashboard to show the metrics that matter most to you.
        </p>
        <div className="space-y-2">
          <label className="flex items-center gap-2">
            <input type="checkbox" className="rounded" defaultChecked />
            <span className="text-sm">Show budget health score</span>
          </label>
          <label className="flex items-center gap-2">
            <input type="checkbox" className="rounded" defaultChecked />
            <span className="text-sm">Display spending trends</span>
          </label>
          <label className="flex items-center gap-2">
            <input type="checkbox" className="rounded" defaultChecked />
            <span className="text-sm">Enable alerts</span>
          </label>
          <label className="flex items-center gap-2">
            <input type="checkbox" className="rounded" />
            <span className="text-sm">Show advanced analytics</span>
          </label>
        </div>
      </div>
    ),
  },
];