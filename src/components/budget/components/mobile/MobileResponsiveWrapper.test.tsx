import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { MobileResponsiveWrapper } from './MobileResponsiveWrapper';

// Mock window.innerWidth
const mockWindowWidth = (width: number) => {
  Object.defineProperty(window, 'innerWidth', {
    writable: true,
    configurable: true,
    value: width
  });
  window.dispatchEvent(new Event('resize'));
};

describe('MobileResponsiveWrapper', () => {
  const mockOnTabChange = jest.fn();
  const defaultProps = {
    currentTab: 'overview',
    onTabChange: mockOnTabChange,
    children: <div>Test Content</div>
  };

  beforeEach(() => {
    mockOnTabChange.mockClear();
    // Reset to desktop width
    mockWindowWidth(1024);
  });

  it('renders children without wrapper on desktop', () => {
    render(<MobileResponsiveWrapper {...defaultProps} />);
    
    expect(screen.getByText('Test Content')).toBeInTheDocument();
    expect(screen.queryByText('Budget')).not.toBeInTheDocument();
  });

  it('renders mobile wrapper on mobile devices', async () => {
    mockWindowWidth(375); // iPhone width
    
    render(<MobileResponsiveWrapper {...defaultProps} />);
    
    await waitFor(() => {
      expect(screen.getByText('Budget')).toBeInTheDocument();
    });
  });

  it('shows budget stats when provided', async () => {
    mockWindowWidth(375);
    
    render(
      <MobileResponsiveWrapper
        {...defaultProps}
        showQuickStats={true}
        budgetStats={{
          total: 100000,
          spent: 45000,
          remaining: 55000
        }}
      />
    );
    
    await waitFor(() => {
      expect(screen.getByText('Total')).toBeInTheDocument();
      expect(screen.getByText('$100k')).toBeInTheDocument();
      expect(screen.getByText('$45k')).toBeInTheDocument();
      expect(screen.getByText('$55k')).toBeInTheDocument();
    });
  });

  it('opens menu sheet when menu button is clicked', async () => {
    mockWindowWidth(375);
    
    render(<MobileResponsiveWrapper {...defaultProps} />);
    
    await waitFor(() => {
      const menuButton = screen.getByRole('button', { name: /menu/i });
      fireEvent.click(menuButton);
    });
    
    expect(screen.getByText('Training Budget')).toBeInTheDocument();
    expect(screen.getByText('Overview')).toBeInTheDocument();
    expect(screen.getByText('Allocations')).toBeInTheDocument();
  });

  it('changes tab when nav item is clicked', async () => {
    mockWindowWidth(375);
    
    render(<MobileResponsiveWrapper {...defaultProps} />);
    
    await waitFor(() => {
      const menuButton = screen.getByRole('button', { name: /menu/i });
      fireEvent.click(menuButton);
    });
    
    const expensesButton = screen.getByText('Expenses');
    fireEvent.click(expensesButton);
    
    expect(mockOnTabChange).toHaveBeenCalledWith('expenses');
  });

  it('highlights current tab in navigation', async () => {
    mockWindowWidth(375);
    
    render(
      <MobileResponsiveWrapper
        {...defaultProps}
        currentTab="analytics"
      />
    );
    
    await waitFor(() => {
      const menuButton = screen.getByRole('button', { name: /menu/i });
      fireEvent.click(menuButton);
    });
    
    const analyticsButton = screen.getByRole('button', { name: /analytics/i });
    expect(analyticsButton).toHaveClass('secondary');
  });

  it('shows bottom navigation on mobile', async () => {
    mockWindowWidth(375);
    
    render(<MobileResponsiveWrapper {...defaultProps} />);
    
    await waitFor(() => {
      // Bottom nav should show first 4 items
      const bottomNav = screen.getByRole('navigation');
      expect(bottomNav).toBeInTheDocument();
      
      // Check for bottom nav items
      const buttons = screen.getAllByRole('button');
      const bottomNavButtons = buttons.filter(btn => 
        btn.textContent?.includes('Overview') ||
        btn.textContent?.includes('Allocations') ||
        btn.textContent?.includes('Expenses') ||
        btn.textContent?.includes('Analytics')
      );
      
      expect(bottomNavButtons.length).toBeGreaterThan(0);
    });
  });

  it('responds to window resize', async () => {
    render(<MobileResponsiveWrapper {...defaultProps} />);
    
    // Initially desktop
    expect(screen.queryByText('Budget')).not.toBeInTheDocument();
    
    // Resize to mobile
    mockWindowWidth(375);
    
    await waitFor(() => {
      expect(screen.getByText('Budget')).toBeInTheDocument();
    });
    
    // Resize back to desktop
    mockWindowWidth(1024);
    
    await waitFor(() => {
      expect(screen.queryByText('Budget')).not.toBeInTheDocument();
    });
  });

  it('calculates header height correctly with stats', async () => {
    mockWindowWidth(375);
    
    const { container } = render(
      <MobileResponsiveWrapper
        {...defaultProps}
        showQuickStats={true}
        budgetStats={{
          total: 100000,
          spent: 45000,
          remaining: 55000
        }}
      />
    );
    
    await waitFor(() => {
      const main = container.querySelector('main');
      expect(main).toHaveClass('pt-32');
    });
  });

  it('calculates header height correctly without stats', async () => {
    mockWindowWidth(375);
    
    const { container } = render(
      <MobileResponsiveWrapper
        {...defaultProps}
        showQuickStats={false}
      />
    );
    
    await waitFor(() => {
      const main = container.querySelector('main');
      expect(main).toHaveClass('pt-20');
    });
  });
});