import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { MobileOptimizedList } from './MobileOptimizedList';
import type { MobileListItem } from './MobileOptimizedList';

describe('MobileOptimizedList', () => {
  const mockItems: MobileListItem[] = [
    {
      id: '1',
      title: 'Item 1',
      subtitle: 'Subtitle 1',
      value: '$100',
      status: 'active',
      onClick: jest.fn()
    },
    {
      id: '2',
      title: 'Item 2',
      subtitle: 'Subtitle 2',
      value: -50,
      status: 'error',
      actions: [
        {
          label: 'Edit',
          onClick: jest.fn()
        },
        {
          label: 'Delete',
          onClick: jest.fn(),
          variant: 'destructive'
        }
      ]
    },
    {
      id: '3',
      title: 'Item 3',
      icon: <span data-testid="test-icon">Icon</span>
    }
  ];

  beforeEach(() => {
    // Clear all mocks
    mockItems.forEach(item => {
      if (item.onClick) {
        (item.onClick as jest.Mock).mockClear();
      }
      if (item.actions) {
        item.actions.forEach(action => {
          (action.onClick as jest.Mock).mockClear();
        });
      }
    });
  });

  it('renders list with all items', () => {
    render(<MobileOptimizedList items={mockItems} />);
    
    expect(screen.getByText('Item 1')).toBeInTheDocument();
    expect(screen.getByText('Item 2')).toBeInTheDocument();
    expect(screen.getByText('Item 3')).toBeInTheDocument();
  });

  it('renders title when provided', () => {
    render(<MobileOptimizedList items={mockItems} title="Test List" />);
    
    expect(screen.getByText('Test List')).toBeInTheDocument();
  });

  it('shows empty message when no items', () => {
    render(
      <MobileOptimizedList 
        items={[]} 
        emptyMessage="No items found"
      />
    );
    
    expect(screen.getByText('No items found')).toBeInTheDocument();
  });

  it('shows loading state', () => {
    render(<MobileOptimizedList items={[]} loading={true} />);
    
    // Should show skeleton loaders
    const skeletons = screen.getAllByRole('generic').filter(el => 
      el.className.includes('animate-pulse')
    );
    expect(skeletons.length).toBeGreaterThan(0);
  });

  it('renders item subtitles', () => {
    render(<MobileOptimizedList items={mockItems} />);
    
    expect(screen.getByText('Subtitle 1')).toBeInTheDocument();
    expect(screen.getByText('Subtitle 2')).toBeInTheDocument();
  });

  it('renders item values with correct styling', () => {
    render(<MobileOptimizedList items={mockItems} />);
    
    const positiveValue = screen.getByText('$100');
    const negativeValue = screen.getByText('-50');
    
    expect(positiveValue).toBeInTheDocument();
    expect(negativeValue).toHaveClass('text-red-500');
  });

  it('renders status indicators with correct colors', () => {
    render(<MobileOptimizedList items={mockItems} />);
    
    const items = screen.getAllByRole('generic').filter(el => 
      el.className.includes('rounded-full')
    );
    
    expect(items[0]).toHaveClass('bg-green-500'); // active
    expect(items[1]).toHaveClass('bg-red-500'); // error
  });

  it('calls onClick when item is clicked', () => {
    render(<MobileOptimizedList items={mockItems} />);
    
    const firstItem = screen.getByText('Item 1').closest('div')?.parentElement;
    fireEvent.click(firstItem!);
    
    expect(mockItems[0].onClick).toHaveBeenCalled();
  });

  it('renders item actions dropdown', () => {
    render(<MobileOptimizedList items={mockItems} />);
    
    // Find the dropdown trigger for item 2
    const dropdownButtons = screen.getAllByRole('button');
    const actionButton = dropdownButtons.find(btn => 
      btn.querySelector('[class*="MoreVertical"]')
    );
    
    expect(actionButton).toBeInTheDocument();
  });

  it('executes action when clicked', () => {
    render(<MobileOptimizedList items={mockItems} />);
    
    // Open dropdown for item 2
    const dropdownButtons = screen.getAllByRole('button');
    const actionButton = dropdownButtons.find(btn => 
      btn.querySelector('[class*="MoreVertical"]')
    );
    
    fireEvent.click(actionButton!);
    
    // Click edit action
    const editButton = screen.getByText('Edit');
    fireEvent.click(editButton);
    
    expect(mockItems[1].actions![0].onClick).toHaveBeenCalled();
  });

  it('renders icons when provided', () => {
    render(<MobileOptimizedList items={mockItems} />);
    
    expect(screen.getByTestId('test-icon')).toBeInTheDocument();
  });

  it('shows load more button when hasMore is true', () => {
    const mockLoadMore = jest.fn();
    
    render(
      <MobileOptimizedList 
        items={mockItems} 
        hasMore={true}
        onLoadMore={mockLoadMore}
      />
    );
    
    const loadMoreButton = screen.getByText('Load More');
    expect(loadMoreButton).toBeInTheDocument();
    
    fireEvent.click(loadMoreButton);
    expect(mockLoadMore).toHaveBeenCalled();
  });

  it('applies custom className', () => {
    const { container } = render(
      <MobileOptimizedList 
        items={mockItems} 
        className="custom-class"
      />
    );
    
    const card = container.querySelector('.custom-class');
    expect(card).toBeInTheDocument();
  });

  it('shows dividers when showDividers is true', () => {
    const { container } = render(
      <MobileOptimizedList 
        items={mockItems} 
        showDividers={true}
      />
    );
    
    const dividers = container.querySelectorAll('.divide-y');
    expect(dividers.length).toBeGreaterThan(0);
  });

  it('prevents action click from triggering item click', () => {
    render(<MobileOptimizedList items={mockItems} />);
    
    // Find and click the dropdown button
    const dropdownButtons = screen.getAllByRole('button');
    const actionButton = dropdownButtons.find(btn => 
      btn.querySelector('[class*="MoreVertical"]')
    );
    
    fireEvent.click(actionButton!);
    
    // Item onClick should not be called
    expect(mockItems[1].onClick).not.toHaveBeenCalled();
  });

  it('shows chevron for clickable items without actions', () => {
    render(<MobileOptimizedList items={mockItems} />);
    
    // Item 1 has onClick but no actions, should show chevron
    const item1 = screen.getByText('Item 1').closest('div')?.parentElement;
    const chevron = item1?.querySelector('[class*="ChevronRight"]');
    
    expect(chevron).toBeInTheDocument();
  });
});