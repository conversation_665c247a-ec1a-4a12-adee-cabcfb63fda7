import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { MobileOptimizedCard } from './MobileOptimizedCard';

describe('MobileOptimizedCard', () => {
  const defaultProps = {
    title: 'Test Card',
    children: <div>Card Content</div>
  };

  it('renders card with title and content', () => {
    render(<MobileOptimizedCard {...defaultProps} />);
    
    expect(screen.getByText('Test Card')).toBeInTheDocument();
    expect(screen.getByText('Card Content')).toBeInTheDocument();
  });

  it('renders icon when provided', () => {
    render(
      <MobileOptimizedCard
        {...defaultProps}
        icon={<span data-testid="test-icon">Icon</span>}
      />
    );
    
    expect(screen.getByTestId('test-icon')).toBeInTheDocument();
  });

  it('renders badge when provided', () => {
    render(
      <MobileOptimizedCard
        {...defaultProps}
        badge="5"
      />
    );
    
    expect(screen.getByText('5')).toBeInTheDocument();
    expect(screen.getByText('5')).toHaveClass('bg-primary/10');
  });

  it('shows chevron and toggles content when collapsible', () => {
    render(
      <MobileOptimizedCard
        {...defaultProps}
        collapsible={true}
        defaultExpanded={true}
      />
    );
    
    // Content should be visible initially
    expect(screen.getByText('Card Content')).toBeInTheDocument();
    
    // Click to collapse
    const header = screen.getByText('Test Card').closest('div')?.parentElement;
    fireEvent.click(header!);
    
    // Content should still be in DOM (animation)
    expect(screen.getByText('Card Content')).toBeInTheDocument();
  });

  it('starts collapsed when defaultExpanded is false', () => {
    render(
      <MobileOptimizedCard
        {...defaultProps}
        collapsible={true}
        defaultExpanded={false}
      />
    );
    
    // Content should not be visible
    expect(screen.queryByText('Card Content')).not.toBeInTheDocument();
  });

  it('calls onClick when clicked and not collapsible', () => {
    const mockOnClick = jest.fn();
    
    render(
      <MobileOptimizedCard
        {...defaultProps}
        onClick={mockOnClick}
      />
    );
    
    const header = screen.getByText('Test Card').closest('div')?.parentElement;
    fireEvent.click(header!);
    
    expect(mockOnClick).toHaveBeenCalled();
  });

  it('renders actions when provided and not collapsible', () => {
    const actions = <button>Action Button</button>;
    
    render(
      <MobileOptimizedCard
        {...defaultProps}
        actions={actions}
        collapsible={false}
      />
    );
    
    expect(screen.getByText('Action Button')).toBeInTheDocument();
  });

  it('does not render actions when collapsible', () => {
    const actions = <button>Action Button</button>;
    
    render(
      <MobileOptimizedCard
        {...defaultProps}
        actions={actions}
        collapsible={true}
      />
    );
    
    expect(screen.queryByText('Action Button')).not.toBeInTheDocument();
  });

  it('applies custom className', () => {
    const { container } = render(
      <MobileOptimizedCard
        {...defaultProps}
        className="custom-class"
      />
    );
    
    const card = container.querySelector('.custom-class');
    expect(card).toBeInTheDocument();
  });

  it('applies custom contentClassName', () => {
    const { container } = render(
      <MobileOptimizedCard
        {...defaultProps}
        contentClassName="custom-content-class"
      />
    );
    
    const content = container.querySelector('.custom-content-class');
    expect(content).toBeInTheDocument();
  });

  it('shows hover state when clickable', () => {
    const { container } = render(
      <MobileOptimizedCard
        {...defaultProps}
        onClick={() => {}}
      />
    );
    
    const header = container.querySelector('.cursor-pointer');
    expect(header).toBeInTheDocument();
    expect(header).toHaveClass('hover:bg-muted/50');
  });

  it('shows chevron for non-collapsible clickable cards', () => {
    render(
      <MobileOptimizedCard
        {...defaultProps}
        onClick={() => {}}
        collapsible={false}
      />
    );
    
    // Should show ChevronRight for navigation
    const chevrons = screen.getAllByRole('img', { hidden: true });
    expect(chevrons.length).toBeGreaterThan(0);
  });
});