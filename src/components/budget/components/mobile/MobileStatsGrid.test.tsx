import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { MobileStatsGrid } from './MobileStatsGrid';
import type { MobileStat } from './MobileStatsGrid';

describe('MobileStatsGrid', () => {
  const mockStats: MobileStat[] = [
    {
      id: 'stat1',
      label: 'Total Budget',
      value: '$100k',
      icon: <span data-testid="icon1">Icon1</span>,
      color: 'text-primary'
    },
    {
      id: 'stat2',
      label: 'Spent',
      value: '$45k',
      trend: {
        value: 15,
        direction: 'up'
      },
      color: 'text-orange-500'
    },
    {
      id: 'stat3',
      label: 'Remaining',
      value: '$55k',
      trend: {
        value: -5,
        direction: 'down'
      },
      onClick: jest.fn()
    },
    {
      id: 'stat4',
      label: 'Efficiency',
      value: '85%',
      trend: {
        value: 0,
        direction: 'neutral'
      }
    }
  ];

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders all stats in grid', () => {
    render(<MobileStatsGrid stats={mockStats} />);
    
    expect(screen.getByText('Total Budget')).toBeInTheDocument();
    expect(screen.getByText('Spent')).toBeInTheDocument();
    expect(screen.getByText('Remaining')).toBeInTheDocument();
    expect(screen.getByText('Efficiency')).toBeInTheDocument();
  });

  it('renders values correctly', () => {
    render(<MobileStatsGrid stats={mockStats} />);
    
    expect(screen.getByText('$100k')).toBeInTheDocument();
    expect(screen.getByText('$45k')).toBeInTheDocument();
    expect(screen.getByText('$55k')).toBeInTheDocument();
    expect(screen.getByText('85%')).toBeInTheDocument();
  });

  it('renders icons when provided', () => {
    render(<MobileStatsGrid stats={mockStats} />);
    
    expect(screen.getByTestId('icon1')).toBeInTheDocument();
  });

  it('applies custom colors', () => {
    render(<MobileStatsGrid stats={mockStats} />);
    
    const primaryStat = screen.getByText('$100k');
    const orangeStat = screen.getByText('$45k');
    
    expect(primaryStat).toHaveClass('text-primary');
    expect(orangeStat).toHaveClass('text-orange-500');
  });

  it('renders trend indicators with correct direction', () => {
    render(<MobileStatsGrid stats={mockStats} />);
    
    // Up trend
    expect(screen.getByText('+15%')).toBeInTheDocument();
    expect(screen.getByText('+15%')).toHaveClass('text-green-500');
    
    // Down trend
    expect(screen.getByText('-5%')).toBeInTheDocument();
    expect(screen.getByText('-5%').parentElement).toHaveClass('text-red-500');
    
    // Neutral trend
    expect(screen.getByText('0%')).toBeInTheDocument();
    expect(screen.getByText('0%').parentElement).toHaveClass('text-muted-foreground');
  });

  it('calls onClick when clickable stat is clicked', () => {
    render(<MobileStatsGrid stats={mockStats} />);
    
    const remainingCard = screen.getByText('Remaining').closest('div')?.parentElement?.parentElement;
    fireEvent.click(remainingCard!);
    
    expect(mockStats[2].onClick).toHaveBeenCalled();
  });

  it('renders with 2 columns by default', () => {
    const { container } = render(<MobileStatsGrid stats={mockStats} />);
    
    const grid = container.querySelector('.grid');
    expect(grid).toHaveClass('grid-cols-2');
  });

  it('renders with 3 columns when specified', () => {
    const { container } = render(
      <MobileStatsGrid stats={mockStats} columns={3} />
    );
    
    const grid = container.querySelector('.grid');
    expect(grid).toHaveClass('grid-cols-3');
  });

  it('renders loading state', () => {
    render(<MobileStatsGrid stats={[]} loading={true} />);
    
    const skeletons = screen.getAllByRole('generic').filter(el => 
      el.className.includes('animate-pulse')
    );
    expect(skeletons.length).toBeGreaterThan(0);
  });

  it('renders compact variant', () => {
    const { container } = render(
      <MobileStatsGrid stats={mockStats} variant="compact" />
    );
    
    const cards = container.querySelectorAll('.p-3');
    expect(cards.length).toBeGreaterThan(0);
  });

  it('renders minimal variant', () => {
    render(
      <MobileStatsGrid stats={mockStats} variant="minimal" />
    );
    
    // In minimal variant, stats are centered and have no cards
    const labels = screen.getAllByText(/Total Budget|Spent|Remaining|Efficiency/);
    labels.forEach(label => {
      expect(label.parentElement).toHaveClass('text-center');
    });
  });

  it('applies custom className', () => {
    const { container } = render(
      <MobileStatsGrid stats={mockStats} className="custom-class" />
    );
    
    const grid = container.querySelector('.custom-class');
    expect(grid).toBeInTheDocument();
  });

  it('shows hover state for clickable items', () => {
    const { container } = render(<MobileStatsGrid stats={mockStats} />);
    
    const clickableCard = screen.getByText('Remaining')
      .closest('div')?.parentElement?.parentElement;
    
    expect(clickableCard).toHaveClass('cursor-pointer');
    expect(clickableCard).toHaveClass('hover:bg-muted/50');
  });

  it('renders trend icons correctly', () => {
    render(<MobileStatsGrid stats={mockStats} />);
    
    // Should have TrendingUp, TrendingDown, and Minus icons
    const trendContainers = screen.getAllByRole('generic').filter(el => 
      el.className.includes('gap-1') && 
      (el.textContent?.includes('%') || false)
    );
    
    expect(trendContainers.length).toBeGreaterThanOrEqual(3);
  });

  it('handles empty stats array', () => {
    const { container } = render(<MobileStatsGrid stats={[]} />);
    
    const grid = container.querySelector('.grid');
    expect(grid).toBeInTheDocument();
    expect(grid?.children.length).toBe(0);
  });
});