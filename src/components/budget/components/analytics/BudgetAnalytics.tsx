import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  Tag,
  AlertCircle,
  RefreshCw,
  Activity,
  LineChart,
  Info
} from 'lucide-react';
import { formatCurrency } from '../../utils/formatters';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Progress } from '@/components/ui/progress';

export interface BudgetAnalytics {
  trends: {
    monthly: Array<{
      month: string;
      budget: number;
      spent: number;
      categories: Record<string, number>;
    }>;
    quarterly: Array<{
      quarter: string;
      budget: number;
      spent: number;
      efficiency: number;
    }>;
    yearly: Array<{
      year: number;
      budget: number;
      spent: number;
      growth: number;
    }>;
  };
  insights: {
    topCategories: Array<{
      category: string;
      amount: number;
      percentage: number;
      trend: 'up' | 'down' | 'stable';
    }>;
    topDepartments: Array<{
      department: string;
      amount: number;
      percentage: number;
      efficiency: number;
    }>;
    anomalies: Array<{
      type: 'overspend' | 'underspend' | 'unusual_pattern';
      entity: string;
      description: string;
      severity: 'low' | 'medium' | 'high';
      amount?: number;
    }>;
  };
  forecasts: {
    endOfYear: {
      projected: number;
      confidence: number;
      scenarios: {
        optimistic: number;
        realistic: number;
        pessimistic: number;
      };
    };
    nextQuarter: {
      recommended: number;
      basedOn: string[];
    };
  };
  recommendations: Array<{
    id: string;
    type: 'savings' | 'reallocation' | 'optimization';
    title: string;
    description: string;
    impact: number;
    effort: 'low' | 'medium' | 'high';
  }>;
}

interface BudgetAnalyticsProps {
  year: number;
  analytics?: BudgetAnalytics;
  onRefresh: () => Promise<void>;
  onExport: (format: 'pdf' | 'csv' | 'json') => void;
  loading?: boolean;
}

const METRIC_CARDS = [
  {
    title: 'Budget Efficiency',
    getValue: (analytics: BudgetAnalytics) => {
      const total = analytics.trends.monthly.reduce((sum, m) => sum + m.budget, 0);
      const spent = analytics.trends.monthly.reduce((sum, m) => sum + m.spent, 0);
      return total > 0 ? (spent / total) * 100 : 0;
    },
    format: (value: number) => `${value.toFixed(1)}%`,
    icon: Activity,
    color: 'text-blue-600',
    description: 'Overall budget utilization rate'
  },
  {
    title: 'YoY Growth',
    getValue: (analytics: BudgetAnalytics) => {
      const current = analytics.trends.yearly[analytics.trends.yearly.length - 1];
      return current?.growth || 0;
    },
    format: (value: number) => `${value > 0 ? '+' : ''}${value.toFixed(1)}%`,
    icon: TrendingUp,
    color: 'text-green-600',
    description: 'Year-over-year budget growth'
  },
  {
    title: 'Forecast Confidence',
    getValue: (analytics: BudgetAnalytics) => analytics.forecasts.endOfYear.confidence,
    format: (value: number) => `${value.toFixed(0)}%`,
    icon: LineChart,
    color: 'text-purple-600',
    description: 'Accuracy of spending projections'
  },
  {
    title: 'Anomalies Detected',
    getValue: (analytics: BudgetAnalytics) => analytics.insights.anomalies.length,
    format: (value: number) => value.toString(),
    icon: AlertCircle,
    color: 'text-orange-600',
    description: 'Unusual spending patterns found'
  },
];

export const BudgetAnalytics: React.FC<BudgetAnalyticsProps> = ({
  analytics,
  onRefresh,
  onExport,
  loading = false,
}) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedPeriod, setSelectedPeriod] = useState('monthly');
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await onRefresh();
    } finally {
      setIsRefreshing(false);
    }
  };

  if (loading || !analytics) {
    return (
      <Card className="p-6">
        <Skeleton className="h-6 w-48 mb-4" />
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          {[...Array(4)].map((_, idx) => (
            <Skeleton key={idx} className="h-24 w-full" />
          ))}
        </div>
        <Skeleton className="h-64 w-full" />
      </Card>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.1 }}
    >
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <BarChart3 className="h-5 w-5 text-muted-foreground" />
            <h3 className="text-lg font-semibold">Budget Analytics & Insights</h3>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={isRefreshing}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <Select value="pdf" onValueChange={(format: any) => onExport(format)}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="pdf">Export PDF</SelectItem>
                <SelectItem value="csv">Export CSV</SelectItem>
                <SelectItem value="json">Export JSON</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Metric Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <TooltipProvider>
            {METRIC_CARDS.map((metric, idx) => {
              const value = metric.getValue(analytics);
              const Icon = metric.icon;
              
              return (
                <Tooltip key={metric.title}>
                  <TooltipTrigger asChild>
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.1 * idx }}
                    >
                      <div className="p-4 border rounded-lg hover:shadow-md transition-shadow cursor-default">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm text-muted-foreground">{metric.title}</span>
                          <Icon className={`h-4 w-4 ${metric.color}`} />
                        </div>
                        <p className={`text-2xl font-bold ${metric.color}`}>
                          {metric.format(value)}
                        </p>
                      </div>
                    </motion.div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{metric.description}</p>
                  </TooltipContent>
                </Tooltip>
              );
            })}
          </TooltipProvider>
        </div>

        {/* Analytics Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="trends">Trends</TabsTrigger>
            <TabsTrigger value="insights">Insights</TabsTrigger>
            <TabsTrigger value="forecasts">Forecasts</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4 mt-4">
            {/* Top Categories */}
            <div className="space-y-3">
              <h4 className="font-medium flex items-center gap-2">
                <Tag className="h-4 w-4" />
                Top Spending Categories
              </h4>
              {analytics.insights.topCategories.map((category, idx) => (
                <motion.div
                  key={category.category}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.05 * idx }}
                  className="flex items-center justify-between p-3 border rounded-lg"
                >
                  <div className="flex items-center gap-3">
                    <span className="font-medium">{category.category}</span>
                    {category.trend === 'up' ? (
                      <TrendingUp className="h-4 w-4 text-green-500" />
                    ) : category.trend === 'down' ? (
                      <TrendingDown className="h-4 w-4 text-red-500" />
                    ) : null}
                  </div>
                  <div className="flex items-center gap-4">
                    <Progress value={category.percentage} className="w-24" />
                    <span className="text-sm font-medium w-20 text-right">
                      {formatCurrency(category.amount)}
                    </span>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Recommendations */}
            <div className="space-y-3">
              <h4 className="font-medium flex items-center gap-2">
                <Info className="h-4 w-4" />
                Recommendations
              </h4>
              {analytics.recommendations.map((rec) => (
                <motion.div
                  key={rec.id}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="p-4 border rounded-lg"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h5 className="font-medium mb-1">{rec.title}</h5>
                      <p className="text-sm text-muted-foreground mb-2">{rec.description}</p>
                      <div className="flex items-center gap-4">
                        <Badge variant={rec.type === 'savings' ? 'default' : 'secondary'}>
                          {rec.type}
                        </Badge>
                        <span className="text-sm">
                          Impact: {formatCurrency(rec.impact)}
                        </span>
                        <span className="text-sm">
                          Effort: <Badge variant="outline">{rec.effort}</Badge>
                        </span>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="trends" className="space-y-4 mt-4">
            <div className="flex items-center justify-between mb-4">
              <h4 className="font-medium">Spending Trends</h4>
              <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="monthly">Monthly</SelectItem>
                  <SelectItem value="quarterly">Quarterly</SelectItem>
                  <SelectItem value="yearly">Yearly</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Trend visualization would go here - simplified for this example */}
            <div className="h-64 flex items-center justify-center border rounded-lg bg-muted/20">
              <div className="text-center text-muted-foreground">
                <LineChart className="h-12 w-12 mx-auto mb-2" />
                <p>Trend chart visualization</p>
                <p className="text-sm">Connect to charting library for full visualization</p>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="insights" className="space-y-4 mt-4">
            {/* Anomalies */}
            <div className="space-y-3">
              <h4 className="font-medium flex items-center gap-2">
                <AlertCircle className="h-4 w-4" />
                Detected Anomalies
              </h4>
              {analytics.insights.anomalies.length === 0 ? (
                <div className="text-center py-4 text-muted-foreground">
                  <p>No anomalies detected</p>
                </div>
              ) : (
                analytics.insights.anomalies.map((anomaly, idx) => (
                  <motion.div
                    key={idx}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.05 * idx }}
                    className="p-3 border rounded-lg"
                  >
                    <div className="flex items-start justify-between">
                      <div>
                        <div className="flex items-center gap-2 mb-1">
                          <Badge
                            variant={
                              anomaly.severity === 'high' ? 'destructive' :
                              anomaly.severity === 'medium' ? 'default' : 'secondary'
                            }
                          >
                            {anomaly.severity}
                          </Badge>
                          <span className="font-medium">{anomaly.entity}</span>
                        </div>
                        <p className="text-sm text-muted-foreground">{anomaly.description}</p>
                      </div>
                      {anomaly.amount && (
                        <span className="text-sm font-medium">
                          {formatCurrency(anomaly.amount)}
                        </span>
                      )}
                    </div>
                  </motion.div>
                ))
              )}
            </div>
          </TabsContent>

          <TabsContent value="forecasts" className="space-y-4 mt-4">
            {/* End of Year Forecast */}
            <div className="space-y-4">
              <h4 className="font-medium">End of Year Projections</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card className="p-4">
                  <h5 className="text-sm text-muted-foreground mb-2">Optimistic Scenario</h5>
                  <p className="text-xl font-bold text-green-600">
                    {formatCurrency(analytics.forecasts.endOfYear.scenarios.optimistic)}
                  </p>
                </Card>
                <Card className="p-4">
                  <h5 className="text-sm text-muted-foreground mb-2">Realistic Projection</h5>
                  <p className="text-xl font-bold">
                    {formatCurrency(analytics.forecasts.endOfYear.scenarios.realistic)}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {analytics.forecasts.endOfYear.confidence}% confidence
                  </p>
                </Card>
                <Card className="p-4">
                  <h5 className="text-sm text-muted-foreground mb-2">Pessimistic Scenario</h5>
                  <p className="text-xl font-bold text-red-600">
                    {formatCurrency(analytics.forecasts.endOfYear.scenarios.pessimistic)}
                  </p>
                </Card>
              </div>

              {/* Next Quarter Recommendation */}
              <Card className="p-4">
                <h5 className="font-medium mb-2">Next Quarter Recommendation</h5>
                <p className="text-2xl font-bold mb-2">
                  {formatCurrency(analytics.forecasts.nextQuarter.recommended)}
                </p>
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">Based on:</p>
                  <ul className="text-sm text-muted-foreground ml-4 list-disc">
                    {analytics.forecasts.nextQuarter.basedOn.map((factor, idx) => (
                      <li key={idx}>{factor}</li>
                    ))}
                  </ul>
                </div>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </Card>
    </motion.div>
  );
};