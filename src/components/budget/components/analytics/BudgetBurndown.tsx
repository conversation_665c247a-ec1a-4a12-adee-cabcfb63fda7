import React from 'react';
import { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, ReferenceLine } from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Calendar, DollarSign, TrendingDown, AlertCircle } from 'lucide-react';

interface BurndownData {
  date: string;
  remaining: number;
  idealRemaining: number;
  projectedRemaining: number;
}

interface BudgetBurndownProps {
  data: BurndownData[];
  totalBudget?: number;
  currentSpend?: number;
  daysRemaining?: number;
  burnRate?: number;
}

export const BudgetBurndown: React.FC<BudgetBurndownProps> = ({ 
  data, 
  totalBudget = 0,
  currentSpend = 0,
  daysRemaining = 0,
  burnRate = 0
}) => {
  const remainingBudget = totalBudget - currentSpend;
  const utilizationPercentage = (currentSpend / totalBudget) * 100;
  const projectedOverspend = data[data.length - 1]?.projectedRemaining < 0;
  const daysUntilDepletion = burnRate > 0 ? Math.floor(remainingBudget / burnRate) : daysRemaining;

  const getStatusColor = (percentage: number) => {
    if (percentage < 60) return 'text-green-600';
    if (percentage < 80) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getStatusBadge = (percentage: number) => {
    if (percentage < 60) return { text: 'On Track', variant: 'default' as const };
    if (percentage < 80) return { text: 'Caution', variant: 'secondary' as const };
    return { text: 'Critical', variant: 'destructive' as const };
  };

  const status = getStatusBadge(utilizationPercentage);

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload[0]) {
      return (
        <div className="bg-background border rounded-lg shadow-lg p-3">
          <p className="font-medium mb-2">{label}</p>
          <p className="text-sm">
            <span className="text-muted-foreground">Remaining:</span> ${payload[0].value.toLocaleString()}
          </p>
          {payload[1] && (
            <p className="text-sm">
              <span className="text-muted-foreground">Ideal:</span> ${payload[1].value.toLocaleString()}
            </p>
          )}
          {payload[2] && (
            <p className="text-sm">
              <span className="text-muted-foreground">Projected:</span> ${payload[2].value.toLocaleString()}
            </p>
          )}
        </div>
      );
    }
    return null;
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Budget Burndown</CardTitle>
          <Badge variant={status.variant}>{status.text}</Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-muted-foreground">
              <DollarSign className="w-4 h-4" />
              <span className="text-sm">Remaining</span>
            </div>
            <p className={`text-2xl font-semibold ${getStatusColor(utilizationPercentage)}`}>
              ${(remainingBudget / 1000).toFixed(1)}k
            </p>
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-muted-foreground">
              <Calendar className="w-4 h-4" />
              <span className="text-sm">Days Left</span>
            </div>
            <p className="text-2xl font-semibold">
              {daysRemaining}
            </p>
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-muted-foreground">
              <TrendingDown className="w-4 h-4" />
              <span className="text-sm">Burn Rate</span>
            </div>
            <p className="text-2xl font-semibold">
              ${(burnRate / 1000).toFixed(1)}k<span className="text-sm text-muted-foreground">/day</span>
            </p>
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-muted-foreground">
              <AlertCircle className="w-4 h-4" />
              <span className="text-sm">Depletion In</span>
            </div>
            <p className={`text-2xl font-semibold ${daysUntilDepletion < daysRemaining ? 'text-red-600' : 'text-green-600'}`}>
              {daysUntilDepletion} days
            </p>
          </div>
        </div>

        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Budget Utilization</span>
            <span className="font-medium">{utilizationPercentage.toFixed(1)}%</span>
          </div>
          <Progress value={utilizationPercentage} className="h-2" />
        </div>

        <ResponsiveContainer width="100%" height={300}>
          <AreaChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
            <XAxis 
              dataKey="date" 
              className="text-xs"
              tick={{ fill: 'currentColor' }}
            />
            <YAxis 
              className="text-xs"
              tick={{ fill: 'currentColor' }}
              tickFormatter={(value) => `$${(value / 1000).toFixed(0)}k`}
            />
            <Tooltip content={<CustomTooltip />} />
            
            <Area
              type="monotone"
              dataKey="remaining"
              stroke="hsl(var(--primary))"
              strokeWidth={2}
              fill="hsl(var(--primary))"
              fillOpacity={0.3}
              name="Actual Remaining"
            />
            
            <Area
              type="monotone"
              dataKey="idealRemaining"
              stroke="hsl(var(--muted-foreground))"
              strokeWidth={1}
              strokeDasharray="5 5"
              fill="none"
              name="Ideal Burndown"
            />
            
            <Area
              type="monotone"
              dataKey="projectedRemaining"
              stroke="hsl(var(--chart-3))"
              strokeWidth={1}
              strokeDasharray="3 3"
              fill="none"
              name="Projected"
            />
            
            <ReferenceLine y={0} stroke="hsl(var(--destructive))" strokeDasharray="3 3" />
          </AreaChart>
        </ResponsiveContainer>

        {projectedOverspend && (
          <div className="flex items-center gap-2 p-3 bg-destructive/10 text-destructive rounded-lg">
            <AlertCircle className="w-4 h-4" />
            <p className="text-sm">
              Current burn rate projects budget depletion before period end. Consider reducing expenses.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};