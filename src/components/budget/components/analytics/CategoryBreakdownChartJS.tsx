import React from 'react';
import {
  Chart as ChartJS,
  ArcElement,
  Toolt<PERSON>,
  Legend,
  ChartOptions
} from 'chart.js';
import { Doughnut } from 'react-chartjs-2';
import { Box, Paper, Typography, useTheme, alpha, Grid, Chip } from '@mui/material';
import { formatCurrency } from '../../utils/formatters';
import chroma from 'chroma-js';

ChartJS.register(ArcElement, Tooltip, Legend);

interface CategoryBreakdownChartProps {
  data: Array<{
    category: string;
    amount: number;
    percentage: number;
    color?: string;
  }>;
}

export const CategoryBreakdownChartJS: React.FC<CategoryBreakdownChartProps> = ({ data }) => {
  const theme = useTheme();

  // Generate a color palette using chroma-js
  const colors = chroma
    .scale([
      theme.palette.primary.main,
      theme.palette.secondary.main,
      theme.palette.success.main,
      theme.palette.warning.main,
      theme.palette.info.main
    ])
    .mode('lch')
    .colors(data.length);

  const chartData = {
    labels: data.map(d => d.category),
    datasets: [
      {
        data: data.map(d => d.amount),
        backgroundColor: colors.map(c => alpha(c, 0.8)),
        borderColor: colors,
        borderWidth: 2,
        hoverOffset: 4,
        hoverBorderWidth: 3
      }
    ]
  };

  const options: ChartOptions<'doughnut'> = {
    responsive: true,
    maintainAspectRatio: false,
    cutout: '65%',
    plugins: {
      legend: {
        display: false
      },
      tooltip: {
        backgroundColor: alpha(theme.palette.background.paper, 0.95),
        titleColor: theme.palette.text.primary,
        bodyColor: theme.palette.text.secondary,
        borderColor: theme.palette.divider,
        borderWidth: 1,
        padding: 12,
        callbacks: {
          label: function(context) {
            const label = context.label || '';
            const value = formatCurrency(context.parsed);
            const percentage = ((context.parsed / context.dataset.data.reduce((a: any, b: any) => a + b, 0)) * 100).toFixed(1);
            return [`${label}`, `${value} (${percentage}%)`];
          }
        }
      }
    }
  };

  const totalAmount = data.reduce((sum, item) => sum + item.amount, 0);

  return (
    <Paper
      elevation={0}
      sx={{
        p: 3,
        borderRadius: 2,
        border: `1px solid ${alpha(theme.palette.divider, 0.1)}`
      }}
    >
      <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
        Budget Distribution by Category
      </Typography>
      <Typography variant="body2" color="text.secondary" gutterBottom>
        Total Budget: {formatCurrency(totalAmount)}
      </Typography>
      
      <Grid container spacing={3} sx={{ mt: 2 }}>
        <Grid item xs={12} md={6}>
          <Box sx={{ height: 300, position: 'relative' }}>
            <Doughnut data={chartData} options={options} />
            <Box
              sx={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                textAlign: 'center',
                pointerEvents: 'none'
              }}
            >
              <Typography variant="h4" sx={{ fontWeight: 700, color: theme.palette.primary.main }}>
                {data.length}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Categories
              </Typography>
            </Box>
          </Box>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Box sx={{ maxHeight: 300, overflowY: 'auto' }}>
            {data.map((item, index) => (
              <Box
                key={item.category}
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  py: 1.5,
                  px: 2,
                  borderRadius: 1,
                  '&:hover': {
                    bgcolor: alpha(theme.palette.action.hover, 0.04)
                  }
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
                  <Box
                    sx={{
                      width: 12,
                      height: 12,
                      borderRadius: '50%',
                      bgcolor: colors[index]
                    }}
                  />
                  <Box>
                    <Typography variant="body2" sx={{ fontWeight: 600 }}>
                      {item.category}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {formatCurrency(item.amount)}
                    </Typography>
                  </Box>
                </Box>
                <Chip
                  label={`${item.percentage.toFixed(1)}%`}
                  size="small"
                  sx={{
                    fontWeight: 600,
                    bgcolor: alpha(colors[index], 0.1),
                    color: colors[index]
                  }}
                />
              </Box>
            ))}
          </Box>
        </Grid>
      </Grid>
    </Paper>
  );
};