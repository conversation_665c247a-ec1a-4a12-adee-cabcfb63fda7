import React from 'react';
import { AreaChart, Area, XAxis, <PERSON><PERSON><PERSON>s, CartesianGrid, Tooltip, ResponsiveContainer, ReferenceLine } from 'recharts';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertTriangle, TrendingUp, Info } from 'lucide-react';

interface ForecastData {
  month: string;
  actual?: number;
  predicted: number;
  upperBound: number;
  lowerBound: number;
  budget: number;
}

interface ForecastProjectionsProps {
  data: ForecastData[];
}

export const ForecastProjections: React.FC<ForecastProjectionsProps> = ({ data }) => {
  const currentMonthIndex = data.findIndex(d => d.actual === undefined) - 1;
  const currentMonth = currentMonthIndex >= 0 ? data[currentMonthIndex] : null;
  
  const projectedOverspend = data.some(d => d.predicted > d.budget);
  const confidenceInterval = currentMonth 
    ? ((currentMonth.upperBound - currentMonth.lowerBound) / currentMonth.predicted) * 100
    : 0;

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload[0]) {
      const data = payload[0].payload;
      return (
        <div className="bg-background border rounded-lg shadow-lg p-3">
          <p className="font-medium mb-2">{label}</p>
          {data.actual !== undefined && (
            <p className="text-sm">
              <span className="text-muted-foreground">Actual:</span> ${data.actual.toLocaleString()}
            </p>
          )}
          <p className="text-sm">
            <span className="text-muted-foreground">Forecast:</span> ${data.predicted.toLocaleString()}
          </p>
          <p className="text-sm">
            <span className="text-muted-foreground">Range:</span> ${data.lowerBound.toLocaleString()} - ${data.upperBound.toLocaleString()}
          </p>
          <p className="text-sm">
            <span className="text-muted-foreground">Budget:</span> ${data.budget.toLocaleString()}
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Forecast Projections</CardTitle>
        <CardDescription>
          Predictive spending analysis with confidence intervals
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {projectedOverspend && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Projected to exceed budget in upcoming months. Consider adjusting spending plans.
            </AlertDescription>
          </Alert>
        )}

        <div className="grid grid-cols-3 gap-4 mb-4">
          <div className="space-y-1">
            <p className="text-sm text-muted-foreground">Confidence Level</p>
            <p className="text-2xl font-semibold">
              {(100 - confidenceInterval).toFixed(0)}%
            </p>
          </div>
          <div className="space-y-1">
            <p className="text-sm text-muted-foreground">Projected EOY</p>
            <p className="text-2xl font-semibold">
              ${(data[data.length - 1]?.predicted / 1000).toFixed(0)}k
            </p>
          </div>
          <div className="space-y-1">
            <p className="text-sm text-muted-foreground">Budget Variance</p>
            <p className="text-2xl font-semibold">
              {((data[data.length - 1]?.predicted - data[data.length - 1]?.budget) / data[data.length - 1]?.budget * 100).toFixed(1)}%
            </p>
          </div>
        </div>

        <ResponsiveContainer width="100%" height={350}>
          <AreaChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
            <XAxis 
              dataKey="month" 
              className="text-xs"
              tick={{ fill: 'currentColor' }}
            />
            <YAxis 
              className="text-xs"
              tick={{ fill: 'currentColor' }}
              tickFormatter={(value) => `$${(value / 1000).toFixed(0)}k`}
            />
            <Tooltip content={<CustomTooltip />} />
            
            <Area
              type="monotone"
              dataKey="upperBound"
              stackId="1"
              stroke="none"
              fill="hsl(var(--muted))"
              fillOpacity={0.3}
            />
            <Area
              type="monotone"
              dataKey="lowerBound"
              stackId="2"
              stroke="none"
              fill="hsl(var(--background))"
              fillOpacity={1}
            />
            <Area
              type="monotone"
              dataKey="predicted"
              stroke="hsl(var(--primary))"
              strokeWidth={2}
              fill="hsl(var(--primary))"
              fillOpacity={0.6}
            />
            <Area
              type="monotone"
              dataKey="actual"
              stroke="hsl(var(--chart-2))"
              strokeWidth={2}
              fill="hsl(var(--chart-2))"
              fillOpacity={0.8}
            />
            
            {currentMonthIndex >= 0 && (
              <ReferenceLine 
                x={data[currentMonthIndex]?.month} 
                stroke="hsl(var(--muted-foreground))"
                strokeDasharray="5 5"
                label="Current"
              />
            )}
          </AreaChart>
        </ResponsiveContainer>

        <div className="flex items-start gap-2 mt-4 p-3 bg-muted/50 rounded-lg">
          <Info className="w-4 h-4 mt-0.5 text-muted-foreground" />
          <p className="text-sm text-muted-foreground">
            Forecasts are based on historical spending patterns and seasonal adjustments. 
            The shaded area represents the confidence interval.
          </p>
        </div>
      </CardContent>
    </Card>
  );
};