import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>A<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, <PERSON>, ResponsiveContainer } from 'recharts';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ArrowUp, ArrowDown, Minus } from 'lucide-react';

interface YearData {
  month: string;
  currentYear: number;
  previousYear: number;
  yearBeforeLast?: number;
  variance: number;
}

interface YearOverYearComparisonProps {
  data: YearData[];
  currentYear?: number;
  previousYear?: number;
}

export const YearOverYearComparison: React.FC<YearOverYearComparisonProps> = ({ 
  data,
  currentYear = new Date().getFullYear(),
  previousYear = new Date().getFullYear() - 1
}) => {
  const [viewMode, setViewMode] = React.useState<'absolute' | 'percentage'>('absolute');
  const [selectedMetric, setSelectedMetric] = React.useState<'spending' | 'variance'>('spending');

  const totalCurrentYear = data.reduce((sum, d) => sum + (d.currentYear || 0), 0);
  const totalPreviousYear = data.reduce((sum, d) => sum + (d.previousYear || 0), 0);
  const totalVariance = ((totalCurrentYear - totalPreviousYear) / totalPreviousYear) * 100;

  const getVarianceIcon = (variance: number) => {
    if (variance > 5) return <ArrowUp className="w-4 h-4 text-red-500" />;
    if (variance < -5) return <ArrowDown className="w-4 h-4 text-green-500" />;
    return <Minus className="w-4 h-4 text-gray-500" />;
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload[0]) {
      return (
        <div className="bg-background border rounded-lg shadow-lg p-3">
          <p className="font-medium mb-2">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              <span className="text-muted-foreground">{entry.name}:</span> ${entry.value.toLocaleString()}
            </p>
          ))}
          {payload[0].payload.variance !== undefined && (
            <p className="text-sm mt-1 pt-1 border-t">
              <span className="text-muted-foreground">Variance:</span> {payload[0].payload.variance.toFixed(1)}%
            </p>
          )}
        </div>
      );
    }
    return null;
  };

  const processedData = viewMode === 'percentage' 
    ? data.map(d => ({
        ...d,
        currentYear: d.previousYear ? (d.currentYear / d.previousYear) * 100 - 100 : 0,
        previousYear: 0
      }))
    : data;

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Year-over-Year Comparison</CardTitle>
            <CardDescription>
              {currentYear} vs {previousYear} spending analysis
            </CardDescription>
          </div>
          <div className="flex gap-2">
            <Select value={viewMode} onValueChange={(v) => setViewMode(v as 'absolute' | 'percentage')}>
              <SelectTrigger className="w-[140px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="absolute">Absolute</SelectItem>
                <SelectItem value="percentage">Percentage</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-3 gap-4">
          <div className="space-y-1">
            <p className="text-sm text-muted-foreground">{currentYear} Total</p>
            <p className="text-2xl font-semibold">
              ${(totalCurrentYear / 1000000).toFixed(2)}M
            </p>
          </div>
          <div className="space-y-1">
            <p className="text-sm text-muted-foreground">{previousYear} Total</p>
            <p className="text-2xl font-semibold">
              ${(totalPreviousYear / 1000000).toFixed(2)}M
            </p>
          </div>
          <div className="space-y-1">
            <p className="text-sm text-muted-foreground">YoY Change</p>
            <div className="flex items-center gap-2">
              <p className={`text-2xl font-semibold ${totalVariance > 0 ? 'text-red-600' : 'text-green-600'}`}>
                {totalVariance > 0 ? '+' : ''}{totalVariance.toFixed(1)}%
              </p>
              {getVarianceIcon(totalVariance)}
            </div>
          </div>
        </div>

        <ResponsiveContainer width="100%" height={350}>
          <BarChart data={processedData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
            <XAxis 
              dataKey="month" 
              className="text-xs"
              tick={{ fill: 'currentColor' }}
            />
            <YAxis 
              className="text-xs"
              tick={{ fill: 'currentColor' }}
              tickFormatter={(value) => 
                viewMode === 'percentage' 
                  ? `${value}%` 
                  : `$${(value / 1000).toFixed(0)}k`
              }
            />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            
            {viewMode === 'absolute' ? (
              <>
                <Bar 
                  dataKey="currentYear" 
                  fill="hsl(var(--primary))" 
                  name={currentYear.toString()}
                  radius={[4, 4, 0, 0]}
                />
                <Bar 
                  dataKey="previousYear" 
                  fill="hsl(var(--muted-foreground))" 
                  name={previousYear.toString()}
                  radius={[4, 4, 0, 0]}
                />
                {data[0]?.yearBeforeLast !== undefined && (
                  <Bar 
                    dataKey="yearBeforeLast" 
                    fill="hsl(var(--muted))" 
                    name={(previousYear - 1).toString()}
                    radius={[4, 4, 0, 0]}
                  />
                )}
              </>
            ) : (
              <Bar 
                dataKey="currentYear" 
                fill={(data: any) => data.currentYear > 0 ? 'hsl(var(--destructive))' : 'hsl(var(--chart-2))'}
                name="% Change"
                radius={[4, 4, 0, 0]}
              />
            )}
          </BarChart>
        </ResponsiveContainer>

        <div className="grid grid-cols-2 gap-4">
          <div className="p-4 bg-muted/50 rounded-lg">
            <p className="text-sm font-medium mb-2">Highest Increase</p>
            {(() => {
              const maxIncrease = data.reduce((max, d) => d.variance > max.variance ? d : max, data[0]);
              return (
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">{maxIncrease.month}</span>
                  <span className="text-sm font-medium text-red-600">
                    +{maxIncrease.variance.toFixed(1)}%
                  </span>
                </div>
              );
            })()}
          </div>
          
          <div className="p-4 bg-muted/50 rounded-lg">
            <p className="text-sm font-medium mb-2">Highest Decrease</p>
            {(() => {
              const maxDecrease = data.reduce((min, d) => d.variance < min.variance ? d : min, data[0]);
              return (
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">{maxDecrease.month}</span>
                  <span className="text-sm font-medium text-green-600">
                    {maxDecrease.variance.toFixed(1)}%
                  </span>
                </div>
              );
            })()}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};