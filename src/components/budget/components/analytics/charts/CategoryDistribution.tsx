import React from 'react';
import { Card } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Legend, Tooltip } from 'recharts';
import { formatCurrency, formatPercentage } from '../../../utils/formatters';

interface CategoryData {
  category: string;
  amount: number;
  percentage?: number;
}

interface CategoryDistributionProps {
  data: CategoryData[];
  loading?: boolean;
  title?: string;
}

const COLORS = [
  '#007AFF', // Apple blue
  '#34C759', // Apple green
  '#FF9500', // Apple orange
  '#FF3B30', // Apple red
  '#5856D6', // Apple purple
  '#FF2D92', // Apple pink
  '#64D2FF', // Apple cyan
  '#30D158', // Apple mint
];

const CustomTooltip = ({ active, payload }: any) => {
  if (active && payload && payload.length) {
    const data = payload[0];
    return (
      <div className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-3">
        <p className="font-semibold text-sm">{data.name}</p>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          {formatCurrency(data.value)}
        </p>
        {data.payload.percentage && (
          <p className="text-xs text-gray-500 dark:text-gray-500">
            {formatPercentage(data.payload.percentage)}%
          </p>
        )}
      </div>
    );
  }
  return null;
};

const RADIAN = Math.PI / 180;
const renderCustomizedLabel = ({
  cx,
  cy,
  midAngle,
  innerRadius,
  outerRadius,
  percent,
}: any) => {
  const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
  const x = cx + radius * Math.cos(-midAngle * RADIAN);
  const y = cy + radius * Math.sin(-midAngle * RADIAN);

  if (percent < 0.05) return null; // Don't show label for small slices

  return (
    <text
      x={x}
      y={y}
      fill="white"
      textAnchor={x > cx ? 'start' : 'end'}
      dominantBaseline="central"
      className="text-xs font-semibold"
    >
      {`${(percent * 100).toFixed(0)}%`}
    </text>
  );
};

export const CategoryDistribution: React.FC<CategoryDistributionProps> = ({
  data,
  loading = false,
  title = 'Category Distribution'
}) => {
  if (loading) {
    return (
      <Card className="p-6">
        <Skeleton className="h-8 w-48 mb-4" />
        <Skeleton className="h-64 w-full" />
      </Card>
    );
  }

  if (!data || data.length === 0) {
    return (
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">{title}</h3>
        <div className="flex items-center justify-center h-64">
          <p className="text-muted-foreground">No data available</p>
        </div>
      </Card>
    );
  }

  // Calculate percentages if not provided
  const total = data.reduce((sum, item) => sum + item.amount, 0);
  const chartData = data.map(item => ({
    ...item,
    name: item.category,
    value: item.amount,
    percentage: item.percentage || ((item.amount / total) * 100)
  }));

  return (
    <Card className="p-6">
      <h3 className="text-lg font-semibold mb-4">{title}</h3>
      <ResponsiveContainer width="100%" height={300}>
        <PieChart>
          <Pie
            data={chartData}
            cx="50%"
            cy="50%"
            labelLine={false}
            label={renderCustomizedLabel}
            outerRadius={100}
            fill="#8884d8"
            dataKey="value"
          >
            {chartData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
            ))}
          </Pie>
          <Tooltip content={<CustomTooltip />} />
          <Legend 
            verticalAlign="bottom" 
            height={36}
            formatter={(value: string) => (
              <span className="text-sm">{value}</span>
            )}
          />
        </PieChart>
      </ResponsiveContainer>
      
      {/* Category List */}
      <div className="mt-4 space-y-2">
        {chartData.slice(0, 5).map((item, index) => (
          <div key={item.category} className="flex items-center justify-between text-sm">
            <div className="flex items-center gap-2">
              <div 
                className="w-3 h-3 rounded-full" 
                style={{ backgroundColor: COLORS[index % COLORS.length] }}
              />
              <span className="text-muted-foreground">{item.category}</span>
            </div>
            <span className="font-medium">{formatCurrency(item.amount)}</span>
          </div>
        ))}
      </div>
    </Card>
  );
};