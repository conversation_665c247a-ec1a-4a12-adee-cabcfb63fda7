import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  ReferenceLine,
} from 'recharts';
import { TrendingUp, Calendar, Download, DollarSign, Target } from 'lucide-react';
import { formatCurrency } from '../../../utils/formatters';
import { Tooltip as UITooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { AnimatedCounter } from '../../common/AnimatedCounter';
import { FadeTransition } from '../../common/PageTransition';
import { chartVariants, fadeVariants } from '../../../utils/animations';

export interface TrendData {
  period: string;
  budget: number;
  spent: number;
  categories: Record<string, number>;
}

interface SpendingTrendsChartProps {
  data: TrendData[];
  period: 'monthly' | 'quarterly' | 'yearly';
  onPeriodChange: (period: 'monthly' | 'quarterly' | 'yearly') => void;
  onExport?: () => void;
  loading?: boolean;
  showCategories?: boolean;
}

// Apple-inspired color palette - clean, accessible, semantic
const CHART_COLORS = {
  budget: '#007AFF', // Apple blue - primary
  spent: '#34C759', // Apple green - success 
  warning: '#FF9500', // Apple orange - warning
  error: '#FF3B30', // Apple red - error
  categories: [
    '#5856D6', // Apple purple
    '#FF9500', // Apple orange
    '#FF2D92', // Apple pink
    '#64D2FF', // Apple cyan
    '#30D158', // Apple mint
    '#BF5AF2', // Apple purple variant
  ],
};

// Apple-style tooltip with enhanced accessibility and cleaner design
const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-xl shadow-2xl p-4 backdrop-blur-sm"
        role="tooltip"
        aria-label={`Chart data for ${label}`}
      >
        <p className="font-semibold text-gray-900 dark:text-gray-100 mb-3 text-sm">{label}</p>
        <div className="space-y-2">
          {payload
            .sort((a: any, b: any) => b.value - a.value) // Sort by value descending
            .map((entry: any, index: number) => (
              <div key={index} className="flex items-center justify-between gap-6 text-sm">
                <span className="flex items-center gap-2.5 min-w-0">
                  <span
                    className="w-3 h-3 rounded-full flex-shrink-0 ring-2 ring-white dark:ring-gray-900"
                    style={{ backgroundColor: entry.color }}
                    aria-hidden="true"
                  />
                  <span className="text-gray-700 dark:text-gray-300 truncate">
                    {entry.name}
                  </span>
                </span>
                <span className="font-semibold text-gray-900 dark:text-gray-100 tabular-nums">
                  {formatCurrency(entry.value)}
                </span>
              </div>
            ))}
        </div>
        {/* Progressive disclosure: Show variance for budget vs spent */}
        {payload.length >= 2 && payload[0].name === 'Budget' && payload[1].name === 'Spent' && (
          <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between text-xs">
              <span className="text-gray-500 dark:text-gray-400">Variance</span>
              <span className={`font-medium tabular-nums ${
                payload[1].value <= payload[0].value 
                  ? 'text-green-600 dark:text-green-400' 
                  : 'text-red-600 dark:text-red-400'
              }`}>
                {payload[0].value > 0 
                  ? `${((payload[1].value / payload[0].value - 1) * 100).toFixed(1)}%`
                  : 'N/A'
                }
              </span>
            </div>
          </div>
        )}
      </motion.div>
    );
  }
  return null;
};

export const SpendingTrendsChart: React.FC<SpendingTrendsChartProps> = ({
  data,
  period,
  onPeriodChange,
  onExport,
  loading = false,
  showCategories = false,
}) => {
  const [chartType, setChartType] = useState<'line' | 'area'>('line');
  const [focusedDataKey, setFocusedDataKey] = useState<string | null>(null);

  if (loading) {
    return (
      <Card className="p-6">
        <Skeleton className="h-6 w-48 mb-4" />
        <Skeleton className="h-64 w-full" />
      </Card>
    );
  }

  if (!data || data.length === 0) {
    return (
      <Card className="p-6">
        <div className="text-center py-8 text-muted-foreground">
          <TrendingUp className="h-12 w-12 mx-auto mb-3 opacity-50" />
          <p>No spending trend data available</p>
        </div>
      </Card>
    );
  }

  // Calculate average values for reference lines
  const avgBudget = data.reduce((sum, d) => sum + d.budget, 0) / data.length;
  const avgSpent = data.reduce((sum, d) => sum + d.spent, 0) / data.length;

  // Extract unique categories if showing category breakdown
  const categories = showCategories
    ? Array.from(new Set(data.flatMap(d => Object.keys(d.categories || {}))))
    : [];

  const ChartComponent = chartType === 'area' ? AreaChart : LineChart;
  const DataComponent = chartType === 'area' ? Area : Line;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.1 }}
    >
      <Card className="p-6">
        {/* Apple-style clean header with progressive disclosure */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-8">
          <div className="space-y-1">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-blue-50 dark:bg-blue-950 text-blue-600 dark:text-blue-400">
                <TrendingUp className="h-4 w-4" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                Spending Trends
              </h3>
            </div>
            <p className="text-sm text-gray-500 dark:text-gray-400 ml-11">
              Track budget performance over time
            </p>
          </div>
          
          {/* Clean, minimal controls */}
          <div className="flex items-center gap-3">
            <div className="flex items-center bg-gray-100 dark:bg-gray-800 rounded-xl p-1">
              <button
                className={`px-3 py-1.5 text-sm font-medium rounded-lg transition-all ${
                  chartType === 'line' 
                    ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm' 
                    : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200'
                }`}
                onClick={() => setChartType('line')}
                aria-label="Line chart view"
              >
                Line
              </button>
              <button
                className={`px-3 py-1.5 text-sm font-medium rounded-lg transition-all ${
                  chartType === 'area' 
                    ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm' 
                    : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200'
                }`}
                onClick={() => setChartType('area')}
                aria-label="Area chart view"
              >
                Area
              </button>
            </div>

            <Select value={period} onValueChange={onPeriodChange}>
              <SelectTrigger className="w-32 border-gray-200 dark:border-gray-700">
                <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="monthly">Monthly</SelectItem>
                <SelectItem value="quarterly">Quarterly</SelectItem>
                <SelectItem value="yearly">Yearly</SelectItem>
              </SelectContent>
            </Select>
            
            {onExport && (
              <TooltipProvider>
                <UITooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      size="icon"
                      className="border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800"
                      onClick={onExport}
                    >
                      <Download className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Export chart data</TooltipContent>
                </UITooltip>
              </TooltipProvider>
            )}
          </div>
        </div>

        {/* Apple-style clean chart container */}
        <div className="h-80 bg-gray-50/50 dark:bg-gray-900/50 rounded-xl p-4 border border-gray-100 dark:border-gray-800">
          <ResponsiveContainer width="100%" height="100%">
            <ChartComponent
              data={data}
              margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
              onMouseMove={(e) => {
                if (e?.activeLabel) {
                  // Could implement focus interactions here
                }
              }}
            >
              <CartesianGrid 
                strokeDasharray="2 4" 
                className="stroke-gray-200 dark:stroke-gray-700" 
                strokeOpacity={0.3}
              />
              <XAxis
                dataKey="period"
                className="text-xs"
                tick={{ 
                  fill: 'currentColor', 
                  fontSize: 12,
                }}
                tickLine={{ stroke: 'currentColor', strokeOpacity: 0.2 }}
                axisLine={{ stroke: 'currentColor', strokeOpacity: 0.2 }}
              />
              <YAxis
                className="text-xs"
                tick={{ 
                  fill: 'currentColor', 
                  fontSize: 12,
                }}
                tickLine={{ stroke: 'currentColor', strokeOpacity: 0.2 }}
                axisLine={{ stroke: 'currentColor', strokeOpacity: 0.2 }}
                tickFormatter={(value) => `$${(value / 1000).toFixed(0)}k`}
              />
              <Tooltip 
                content={<CustomTooltip />}
                cursor={{ 
                  stroke: 'rgba(59, 130, 246, 0.1)', 
                  strokeWidth: 2,
                  fill: 'rgba(59, 130, 246, 0.05)' 
                }}
              />
              <Legend
                wrapperStyle={{ 
                  paddingTop: '20px',
                  fontSize: '14px'
                }}
                iconType="rect"
                iconSize={12}
              />

              {/* Reference lines for averages */}
              <ReferenceLine
                y={avgBudget}
                stroke={CHART_COLORS.budget}
                strokeDasharray="5 5"
                opacity={0.5}
              />
              <ReferenceLine
                y={avgSpent}
                stroke={CHART_COLORS.spent}
                strokeDasharray="5 5"
                opacity={0.5}
              />

              {/* Main data lines/areas */}
              {chartType === 'area' ? (
                <>
                  <Area
                    type="monotone"
                    dataKey="budget"
                    stroke={CHART_COLORS.budget}
                    fill={CHART_COLORS.budget}
                    fillOpacity={0.3}
                    strokeWidth={2}
                    name="Budget"
                    dot={{ r: 4 }}
                    activeDot={{ r: 6 }}
                  />
                  <Area
                    type="monotone"
                    dataKey="spent"
                    stroke={CHART_COLORS.spent}
                    fill={CHART_COLORS.spent}
                    fillOpacity={0.3}
                    strokeWidth={2}
                    name="Spent"
                    dot={{ r: 4 }}
                    activeDot={{ r: 6 }}
                  />
                </>
              ) : (
                <>
                  <Line
                    type="monotone"
                    dataKey="budget"
                    stroke={CHART_COLORS.budget}
                    strokeWidth={2}
                    name="Budget"
                    dot={{ r: 4 }}
                    activeDot={{ r: 6 }}
                  />
                  <Line
                    type="monotone"
                    dataKey="spent"
                    stroke={CHART_COLORS.spent}
                    strokeWidth={2}
                    name="Spent"
                    dot={{ r: 4 }}
                    activeDot={{ r: 6 }}
                  />
                </>
              )}

              {/* Category lines if enabled */}
              {showCategories &&
                categories.slice(0, 6).map((category, index) => 
                  chartType === 'area' ? (
                    <Area
                      key={category}
                      type="monotone"
                      dataKey={`categories.${category}`}
                      stroke={CHART_COLORS.categories[index % CHART_COLORS.categories.length]}
                      fill={CHART_COLORS.categories[index % CHART_COLORS.categories.length]}
                      fillOpacity={0.2}
                      strokeWidth={1.5}
                      name={category}
                      dot={{ r: 3 }}
                      strokeDasharray="5 5"
                    />
                  ) : (
                    <Line
                      key={category}
                      type="monotone"
                      dataKey={`categories.${category}`}
                      stroke={CHART_COLORS.categories[index % CHART_COLORS.categories.length]}
                      strokeWidth={1.5}
                      name={category}
                      dot={{ r: 3 }}
                      strokeDasharray="5 5"
                    />
                  )
                )}
            </ChartComponent>
          </ResponsiveContainer>
        </div>

        {/* Apple-style summary cards with progressive disclosure */}
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mt-8">
          <motion.div 
            className="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900 rounded-xl p-4 border border-blue-200/50 dark:border-blue-800/50"
            whileHover={{ scale: 1.02 }}
            transition={{ duration: 0.2 }}
          >
            <div className="flex items-center justify-between mb-2">
              <div className="w-8 h-8 rounded-lg bg-blue-500 flex items-center justify-center">
                <TrendingUp className="w-4 h-4 text-white" />
              </div>
              <span className="text-xs font-medium text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-800 px-2 py-1 rounded-full">
                AVG
              </span>
            </div>
            <div>
              <p className="text-sm font-medium text-blue-700 dark:text-blue-300">Average Budget</p>
              <AnimatedCounter
                value={avgBudget}
                formatter={formatCurrency}
                className="text-2xl font-bold text-blue-900 dark:text-blue-100 tabular-nums"
                duration={1.0}
              />
            </div>
          </motion.div>

          <motion.div 
            className="bg-gradient-to-r from-green-50 to-green-100 dark:from-green-950 dark:to-green-900 rounded-xl p-4 border border-green-200/50 dark:border-green-800/50"
            whileHover={{ scale: 1.02 }}
            transition={{ duration: 0.2 }}
          >
            <div className="flex items-center justify-between mb-2">
              <div className="w-8 h-8 rounded-lg bg-green-500 flex items-center justify-center">
                <DollarSign className="w-4 h-4 text-white" />
              </div>
              <span className="text-xs font-medium text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-800 px-2 py-1 rounded-full">
                SPENT
              </span>
            </div>
            <div>
              <p className="text-sm font-medium text-green-700 dark:text-green-300">Average Spent</p>
              <AnimatedCounter
                value={avgSpent}
                formatter={formatCurrency}
                className="text-2xl font-bold text-green-900 dark:text-green-100 tabular-nums"
                duration={1.0}
              />
            </div>
          </motion.div>

          <motion.div 
            className={`bg-gradient-to-r rounded-xl p-4 border ${
              avgBudget > 0 && (avgSpent / avgBudget) > 0.9
                ? 'from-orange-50 to-orange-100 dark:from-orange-950 dark:to-orange-900 border-orange-200/50 dark:border-orange-800/50'
                : 'from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 border-gray-200/50 dark:border-gray-700/50'
            }`}
            whileHover={{ scale: 1.02 }}
            transition={{ duration: 0.2 }}
          >
            <div className="flex items-center justify-between mb-2">
              <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                avgBudget > 0 && (avgSpent / avgBudget) > 0.9
                  ? 'bg-orange-500'
                  : 'bg-gray-500'
              }`}>
                <Target className="w-4 h-4 text-white" />
              </div>
              <span className={`text-xs font-medium px-2 py-1 rounded-full ${
                avgBudget > 0 && (avgSpent / avgBudget) > 0.9
                  ? 'text-orange-600 dark:text-orange-400 bg-orange-100 dark:bg-orange-800'
                  : 'text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-700'
              }`}>
                USAGE
              </span>
            </div>
            <div>
              <p className={`text-sm font-medium ${
                avgBudget > 0 && (avgSpent / avgBudget) > 0.9
                  ? 'text-orange-700 dark:text-orange-300'
                  : 'text-gray-700 dark:text-gray-300'
              }`}>
                Utilization Rate
              </p>
              <div className="flex items-baseline gap-2">
                <p className={`text-2xl font-bold tabular-nums ${
                  avgBudget > 0 && (avgSpent / avgBudget) > 0.9
                    ? 'text-orange-900 dark:text-orange-100'
                    : 'text-gray-900 dark:text-gray-100'
                }`}>
                  {avgBudget > 0 ? `${((avgSpent / avgBudget) * 100).toFixed(1)}%` : '0%'}
                </p>
                {avgBudget > 0 && (avgSpent / avgBudget) > 0.9 && (
                  <span className="text-xs text-orange-500 dark:text-orange-400">High</span>
                )}
              </div>
            </div>
          </motion.div>
        </div>
      </Card>
    </motion.div>
  );
};