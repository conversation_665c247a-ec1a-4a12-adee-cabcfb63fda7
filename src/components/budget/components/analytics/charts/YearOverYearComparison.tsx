import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  BarChart,
  Bar,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  ComposedChart,
} from 'recharts';
import { TrendingUp, TrendingDown, Calendar, BarChart3 } from 'lucide-react';
import { formatCurrency, formatPercentage } from '../../../utils/formatters';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

export interface YearComparisonData {
  category: string;
  [year: string]: number | string; // Dynamic years as keys
}

export interface YearSummary {
  year: number;
  totalBudget: number;
  totalSpent: number;
  categories: Record<string, number>;
  departments: Record<string, number>;
  growth: number;
  efficiency: number;
}

interface YearOverYearComparisonProps {
  data: YearComparisonData[];
  summaries: YearSummary[];
  currentYear: number;
  comparisonYears: number[];
  viewType?: 'category' | 'department';
  onViewTypeChange?: (type: 'category' | 'department') => void;
  loading?: boolean;
}

const COLORS = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#ec4899'];

const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-popover border rounded-lg shadow-lg p-3">
        <p className="font-medium mb-2">{label}</p>
        {payload.map((entry: any, index: number) => (
          <div key={index} className="flex items-center justify-between gap-4 text-sm">
            <span className="flex items-center gap-2">
              <span
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: entry.color }}
              />
              {entry.name}:
            </span>
            <span className="font-medium">{formatCurrency(entry.value)}</span>
          </div>
        ))}
      </div>
    );
  }
  return null;
};

export const YearOverYearComparison: React.FC<YearOverYearComparisonProps> = ({
  data,
  summaries,
  currentYear,
  comparisonYears,
  viewType = 'category',
  onViewTypeChange,
  loading = false,
}) => {
  const [chartType, setChartType] = useState<'bar' | 'line' | 'composed'>('bar');
  const [selectedMetric, setSelectedMetric] = useState<'amount' | 'growth' | 'percentage'>('amount');
  const [activeTab, setActiveTab] = useState('chart');

  if (loading) {
    return (
      <Card className="p-6">
        <Skeleton className="h-6 w-48 mb-4" />
        <Skeleton className="h-64 w-full" />
      </Card>
    );
  }

  if (!data || data.length === 0) {
    return (
      <Card className="p-6">
        <div className="text-center py-8 text-muted-foreground">
          <BarChart3 className="h-12 w-12 mx-auto mb-3 opacity-50" />
          <p>No comparison data available</p>
        </div>
      </Card>
    );
  }

  // Calculate variance data
  const varianceData = data.map(item => {
    const current = item[currentYear.toString()] as number || 0;
    const previous = item[(currentYear - 1).toString()] as number || 0;
    const variance = previous > 0 ? ((current - previous) / previous) * 100 : 0;
    
    return {
      ...item,
      variance,
      current,
      previous,
    };
  });

  // Sort by current year value
  const sortedData = [...varianceData].sort((a, b) => b.current - a.current);

  // Get top gainers and losers
  const topGainers = [...varianceData]
    .sort((a, b) => b.variance - a.variance)
    .slice(0, 3);
  const topLosers = [...varianceData]
    .sort((a, b) => a.variance - b.variance)
    .slice(0, 3)
    .filter(item => item.variance < 0);

  const renderChart = () => {
    switch (chartType) {
      case 'line':
        return (
          <LineChart data={sortedData}>
            <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
            <XAxis
              dataKey="category"
              className="text-xs"
              tick={{ fill: 'currentColor' }}
              angle={-45}
              textAnchor="end"
              height={80}
            />
            <YAxis
              className="text-xs"
              tick={{ fill: 'currentColor' }}
              tickFormatter={(value) => `${(value / 1000).toFixed(0)}k`}
            />
            <Tooltip content={<CustomTooltip />} />
            <Legend wrapperStyle={{ paddingTop: '20px' }} />
            {comparisonYears.map((year, index) => (
              <Line
                key={year}
                type="monotone"
                dataKey={year.toString()}
                stroke={COLORS[index % COLORS.length]}
                strokeWidth={2}
                dot={{ r: 4 }}
                activeDot={{ r: 6 }}
                name={`Year ${year}`}
              />
            ))}
          </LineChart>
        );

      case 'composed':
        return (
          <ComposedChart data={sortedData}>
            <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
            <XAxis
              dataKey="category"
              className="text-xs"
              tick={{ fill: 'currentColor' }}
              angle={-45}
              textAnchor="end"
              height={80}
            />
            <YAxis
              yAxisId="left"
              className="text-xs"
              tick={{ fill: 'currentColor' }}
              tickFormatter={(value) => `${(value / 1000).toFixed(0)}k`}
            />
            <YAxis
              yAxisId="right"
              orientation="right"
              className="text-xs"
              tick={{ fill: 'currentColor' }}
              tickFormatter={(value) => `${value}%`}
            />
            <Tooltip content={<CustomTooltip />} />
            <Legend wrapperStyle={{ paddingTop: '20px' }} />
            {comparisonYears.map((year, index) => (
              <Bar
                key={year}
                yAxisId="left"
                dataKey={year.toString()}
                fill={COLORS[index % COLORS.length]}
                opacity={0.8}
                name={`Year ${year}`}
              />
            ))}
            <Line
              yAxisId="right"
              type="monotone"
              dataKey="variance"
              stroke="#ef4444"
              strokeWidth={2}
              dot={{ r: 4 }}
              name="YoY Change %"
            />
          </ComposedChart>
        );

      default: // bar
        return (
          <BarChart data={sortedData}>
            <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
            <XAxis
              dataKey="category"
              className="text-xs"
              tick={{ fill: 'currentColor' }}
              angle={-45}
              textAnchor="end"
              height={80}
            />
            <YAxis
              className="text-xs"
              tick={{ fill: 'currentColor' }}
              tickFormatter={(value) => `${(value / 1000).toFixed(0)}k`}
            />
            <Tooltip content={<CustomTooltip />} />
            <Legend wrapperStyle={{ paddingTop: '20px' }} />
            {comparisonYears.map((year, index) => (
              <Bar
                key={year}
                dataKey={year.toString()}
                fill={COLORS[index % COLORS.length]}
                name={`Year ${year}`}
              />
            ))}
          </BarChart>
        );
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.3 }}
    >
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <Calendar className="h-5 w-5 text-muted-foreground" />
            <h3 className="text-lg font-semibold">Year-over-Year Comparison</h3>
          </div>
          <div className="flex items-center gap-2">
            {onViewTypeChange && (
              <Select value={viewType} onValueChange={onViewTypeChange}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="category">By Category</SelectItem>
                  <SelectItem value="department">By Department</SelectItem>
                </SelectContent>
              </Select>
            )}
            <Select value={chartType} onValueChange={(v: any) => setChartType(v)}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="bar">Bar Chart</SelectItem>
                <SelectItem value="line">Line Chart</SelectItem>
                <SelectItem value="composed">Combined</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="chart">Comparison Chart</TabsTrigger>
            <TabsTrigger value="variance">Variance Analysis</TabsTrigger>
            <TabsTrigger value="summary">Year Summary</TabsTrigger>
          </TabsList>

          <TabsContent value="chart" className="mt-4">
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                {renderChart()}
              </ResponsiveContainer>
            </div>
          </TabsContent>

          <TabsContent value="variance" className="mt-4 space-y-4">
            {/* Top Gainers */}
            <div>
              <h4 className="font-medium mb-3 flex items-center gap-2">
                <TrendingUp className="h-4 w-4 text-green-600" />
                Top Growth Categories
              </h4>
              <div className="space-y-2">
                {topGainers.map((item, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-3 border rounded-lg"
                  >
                    <div>
                      <p className="font-medium">{item.category}</p>
                      <p className="text-sm text-muted-foreground">
                        {formatCurrency(item.previous)} → {formatCurrency(item.current)}
                      </p>
                    </div>
                    <Badge variant="secondary" className="bg-green-100 text-green-700">
                      +{item.variance.toFixed(1)}%
                    </Badge>
                  </div>
                ))}
              </div>
            </div>

            {/* Top Losers */}
            {topLosers.length > 0 && (
              <div>
                <h4 className="font-medium mb-3 flex items-center gap-2">
                  <TrendingDown className="h-4 w-4 text-red-600" />
                  Decreased Spending
                </h4>
                <div className="space-y-2">
                  {topLosers.map((item, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-3 border rounded-lg"
                    >
                      <div>
                        <p className="font-medium">{item.category}</p>
                        <p className="text-sm text-muted-foreground">
                          {formatCurrency(item.previous)} → {formatCurrency(item.current)}
                        </p>
                      </div>
                      <Badge variant="secondary" className="bg-red-100 text-red-700">
                        {item.variance.toFixed(1)}%
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="summary" className="mt-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {summaries.map((summary) => (
                <Card key={summary.year} className="p-4">
                  <h5 className="font-semibold mb-3 flex items-center justify-between">
                    Year {summary.year}
                    {summary.year === currentYear && (
                      <Badge variant="default">Current</Badge>
                    )}
                  </h5>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Budget:</span>
                      <span className="font-medium">{formatCurrency(summary.totalBudget)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Spent:</span>
                      <span className="font-medium">{formatCurrency(summary.totalSpent)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Efficiency:</span>
                      <span className="font-medium">{formatPercentage(summary.efficiency)}</span>
                    </div>
                    {summary.growth !== 0 && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Growth:</span>
                        <span className={`font-medium ${summary.growth > 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {summary.growth > 0 ? '+' : ''}{formatPercentage(summary.growth)}
                        </span>
                      </div>
                    )}
                  </div>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </Card>
    </motion.div>
  );
};