import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  LineChart,
  Line,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  ReferenceLine,
  ComposedChart,
} from 'recharts';
import { 
  TrendingUp, 
  AlertTriangle, 
  CheckCircle, 
  Brain,
  Calendar,
  Info
} from 'lucide-react';
import { formatCurrency } from '../../../utils/formatters';
import { 
  generateScenarios, 
  calculateAlerts,
  HistoricalDataPoint,
  calculateSeasonalFactors
} from '../../../utils/forecastCalculations';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

export interface ForecastData {
  date: string;
  actual?: number;
  forecast?: number;
  optimistic?: number;
  realistic?: number;
  pessimistic?: number;
  confidence?: number;
}

interface ForecastProjectionsProps {
  historicalData: ForecastData[];
  totalBudget: number;
  currentSpent: number;
  remainingDays: number;
  onWhatIfAnalysis?: () => void;
  loading?: boolean;
}

const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-popover border rounded-lg shadow-lg p-3">
        <p className="font-medium mb-2">{label}</p>
        {payload.map((entry: any, index: number) => (
          <div key={index} className="flex items-center justify-between gap-4 text-sm">
            <span className="flex items-center gap-2">
              <span
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: entry.color }}
              />
              {entry.name}:
            </span>
            <span className="font-medium">
              {entry.value ? formatCurrency(entry.value) : 'N/A'}
            </span>
          </div>
        ))}
      </div>
    );
  }
  return null;
};

export const ForecastProjections: React.FC<ForecastProjectionsProps> = ({
  historicalData,
  totalBudget,
  currentSpent,
  remainingDays,
  onWhatIfAnalysis,
  loading = false,
}) => {
  const [forecastMethod, setForecastMethod] = useState<'linear' | 'seasonal' | 'ml'>('seasonal');
  const [showConfidenceInterval, setShowConfidenceInterval] = useState(true);
  const [activeTab, setActiveTab] = useState('projection');

  // Calculate forecasts
  const forecasts = useMemo(() => {
    if (!historicalData || historicalData.length < 3) return null;

    // Convert to format for calculations
    const historicalPoints: HistoricalDataPoint[] = historicalData
      .filter(d => d.actual !== undefined)
      .map(d => ({
        date: new Date(d.date),
        value: d.actual!
      }));

    if (historicalPoints.length < 3) return null;

    // Calculate seasonal factors
    const seasonalFactors = calculateSeasonalFactors(historicalPoints);

    // Generate future dates
    const lastDate = new Date(historicalPoints[historicalPoints.length - 1].date);
    const endOfYear = new Date(lastDate.getFullYear(), 11, 31);

    // Generate scenarios
    const scenarios = generateScenarios(historicalPoints, endOfYear, seasonalFactors);

    // Calculate alerts
    const alerts = calculateAlerts(
      currentSpent,
      totalBudget,
      scenarios.realistic,
      remainingDays
    );

    return {
      scenarios,
      alerts,
      seasonalFactors
    };
  }, [historicalData, currentSpent, totalBudget, remainingDays]);

  if (loading) {
    return (
      <Card className="p-6">
        <Skeleton className="h-6 w-48 mb-4" />
        <Skeleton className="h-64 w-full" />
      </Card>
    );
  }

  if (!historicalData || historicalData.length < 3 || !forecasts) {
    return (
      <Card className="p-6">
        <div className="text-center py-8 text-muted-foreground">
          <Brain className="h-12 w-12 mx-auto mb-3 opacity-50" />
          <p>Insufficient data for forecasting</p>
          <p className="text-sm mt-1">Need at least 3 months of historical data</p>
        </div>
      </Card>
    );
  }

  const { scenarios, alerts } = forecasts;

  // Prepare chart data
  const chartData = historicalData.map(point => ({
    ...point,
    optimistic: point.forecast ? scenarios.optimistic.value : undefined,
    realistic: point.forecast ? scenarios.realistic.value : undefined,
    pessimistic: point.forecast ? scenarios.pessimistic.value : undefined,
  }));

  const getAlertIcon = () => {
    switch (alerts.level) {
      case 'success': return <CheckCircle className="h-4 w-4" />;
      case 'warning': return <AlertTriangle className="h-4 w-4" />;
      case 'danger': return <AlertTriangle className="h-4 w-4" />;
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.4 }}
    >
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <Brain className="h-5 w-5 text-muted-foreground" />
            <h3 className="text-lg font-semibold">Forecast Projections</h3>
            <Badge variant="outline">
              {forecastMethod === 'ml' ? 'Machine Learning' : forecastMethod}
            </Badge>
          </div>
          <div className="flex items-center gap-2">
            <Select value={forecastMethod} onValueChange={(v: any) => setForecastMethod(v)}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="linear">Linear</SelectItem>
                <SelectItem value="seasonal">Seasonal</SelectItem>
                <SelectItem value="ml">ML-Based</SelectItem>
              </SelectContent>
            </Select>
            {onWhatIfAnalysis && (
              <Button variant="outline" size="sm" onClick={onWhatIfAnalysis}>
                What-if Analysis
              </Button>
            )}
          </div>
        </div>

        {/* Alert Section */}
        <Alert className={`mb-4 ${
          alerts.level === 'danger' ? 'border-destructive' :
          alerts.level === 'warning' ? 'border-orange-500' :
          'border-green-500'
        }`}>
          <div className="flex items-start gap-2">
            {getAlertIcon()}
            <div className="flex-1">
              <AlertTitle>{alerts.message}</AlertTitle>
              <AlertDescription>
                <ul className="list-disc list-inside mt-2 space-y-1">
                  {alerts.recommendations.map((rec, idx) => (
                    <li key={idx}>{rec}</li>
                  ))}
                </ul>
              </AlertDescription>
            </div>
          </div>
        </Alert>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="projection">Projection Chart</TabsTrigger>
            <TabsTrigger value="scenarios">Scenario Analysis</TabsTrigger>
            <TabsTrigger value="confidence">Confidence Metrics</TabsTrigger>
          </TabsList>

          <TabsContent value="projection" className="mt-4">
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <ComposedChart
                  data={chartData}
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <defs>
                    <linearGradient id="confidenceGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.3} />
                      <stop offset="95%" stopColor="#3b82f6" stopOpacity={0.05} />
                    </linearGradient>
                  </defs>

                  <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
                  <XAxis
                    dataKey="date"
                    className="text-xs"
                    tick={{ fill: 'currentColor' }}
                  />
                  <YAxis
                    className="text-xs"
                    tick={{ fill: 'currentColor' }}
                    tickFormatter={(value) => `${(value / 1000).toFixed(0)}k`}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Legend wrapperStyle={{ paddingTop: '20px' }} />

                  {/* Budget limit */}
                  <ReferenceLine
                    y={totalBudget}
                    stroke="#ef4444"
                    strokeDasharray="5 5"
                    label="Budget Limit"
                  />

                  {/* Current spending level */}
                  <ReferenceLine
                    y={currentSpent}
                    stroke="#10b981"
                    strokeDasharray="3 3"
                    label="Current"
                  />

                  {/* Confidence interval */}
                  {showConfidenceInterval && (
                    <>
                      <Area
                        type="monotone"
                        dataKey="pessimistic"
                        stackId="1"
                        stroke="none"
                        fill="url(#confidenceGradient)"
                        name="Upper Bound"
                      />
                      <Area
                        type="monotone"
                        dataKey="optimistic"
                        stackId="2"
                        stroke="none"
                        fill="url(#confidenceGradient)"
                        name="Lower Bound"
                      />
                    </>
                  )}

                  {/* Actual spending */}
                  <Line
                    type="monotone"
                    dataKey="actual"
                    stroke="#10b981"
                    strokeWidth={3}
                    dot={{ r: 4 }}
                    name="Actual"
                  />

                  {/* Forecast lines */}
                  <Line
                    type="monotone"
                    dataKey="realistic"
                    stroke="#3b82f6"
                    strokeWidth={2}
                    strokeDasharray="5 5"
                    dot={false}
                    name="Forecast"
                  />
                </ComposedChart>
              </ResponsiveContainer>
            </div>
          </TabsContent>

          <TabsContent value="scenarios" className="mt-4 space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card className="p-4 border-green-200 bg-green-50/50">
                <h4 className="font-medium text-green-700 mb-2">Optimistic Scenario</h4>
                <p className="text-2xl font-bold text-green-700">
                  {formatCurrency(scenarios.optimistic.value)}
                </p>
                <p className="text-sm text-green-600 mt-1">
                  {((scenarios.optimistic.value / totalBudget) * 100).toFixed(1)}% of budget
                </p>
                <p className="text-xs text-muted-foreground mt-2">
                  Best case with reduced spending
                </p>
              </Card>

              <Card className="p-4 border-blue-200 bg-blue-50/50">
                <h4 className="font-medium text-blue-700 mb-2">Realistic Projection</h4>
                <p className="text-2xl font-bold text-blue-700">
                  {formatCurrency(scenarios.realistic.value)}
                </p>
                <p className="text-sm text-blue-600 mt-1">
                  {((scenarios.realistic.value / totalBudget) * 100).toFixed(1)}% of budget
                </p>
                <p className="text-xs text-muted-foreground mt-2">
                  Most likely outcome
                </p>
              </Card>

              <Card className="p-4 border-orange-200 bg-orange-50/50">
                <h4 className="font-medium text-orange-700 mb-2">Pessimistic Scenario</h4>
                <p className="text-2xl font-bold text-orange-700">
                  {formatCurrency(scenarios.pessimistic.value)}
                </p>
                <p className="text-sm text-orange-600 mt-1">
                  {((scenarios.pessimistic.value / totalBudget) * 100).toFixed(1)}% of budget
                </p>
                <p className="text-xs text-muted-foreground mt-2">
                  Worst case with increased spending
                </p>
              </Card>
            </div>

            {/* Key Factors */}
            <Card className="p-4">
              <h4 className="font-medium mb-3 flex items-center gap-2">
                <Info className="h-4 w-4" />
                Key Factors Influencing Forecast
              </h4>
              <ul className="space-y-2 text-sm">
                <li className="flex items-start gap-2">
                  <span className="text-muted-foreground">•</span>
                  <span>Historical spending patterns over the last {historicalData.filter(d => d.actual).length} periods</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-muted-foreground">•</span>
                  <span>Seasonal adjustments based on previous year data</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-muted-foreground">•</span>
                  <span>{remainingDays} days remaining in the budget period</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="text-muted-foreground">•</span>
                  <span>Current utilization rate: {((currentSpent / totalBudget) * 100).toFixed(1)}%</span>
                </li>
              </ul>
            </Card>
          </TabsContent>

          <TabsContent value="confidence" className="mt-4 space-y-4">
            <div className="space-y-4">
              {/* Confidence Scores */}
              <Card className="p-4">
                <h4 className="font-medium mb-3">Forecast Confidence Levels</h4>
                <div className="space-y-3">
                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="text-sm">Optimistic Scenario</span>
                      <span className="text-sm font-medium">
                        {(scenarios.optimistic.confidence * 100).toFixed(0)}%
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-green-600 h-2 rounded-full"
                        style={{ width: `${scenarios.optimistic.confidence * 100}%` }}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="text-sm">Realistic Projection</span>
                      <span className="text-sm font-medium">
                        {(scenarios.realistic.confidence * 100).toFixed(0)}%
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full"
                        style={{ width: `${scenarios.realistic.confidence * 100}%` }}
                      />
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between mb-1">
                      <span className="text-sm">Pessimistic Scenario</span>
                      <span className="text-sm font-medium">
                        {(scenarios.pessimistic.confidence * 100).toFixed(0)}%
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-orange-600 h-2 rounded-full"
                        style={{ width: `${scenarios.pessimistic.confidence * 100}%` }}
                      />
                    </div>
                  </div>
                </div>
              </Card>

              {/* Method Explanation */}
              <Card className="p-4">
                <h4 className="font-medium mb-3">Forecasting Method: {forecastMethod}</h4>
                <p className="text-sm text-muted-foreground">
                  {forecastMethod === 'linear' && 
                    'Uses simple linear regression to project future spending based on historical trend.'}
                  {forecastMethod === 'seasonal' && 
                    'Applies seasonal adjustments based on historical monthly patterns to improve accuracy.'}
                  {forecastMethod === 'ml' && 
                    'Uses machine learning algorithms to identify complex patterns and generate predictions.'}
                </p>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </Card>
    </motion.div>
  );
};