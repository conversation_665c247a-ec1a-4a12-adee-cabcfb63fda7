import React, { useState, useRef } from 'react';
import { motion } from 'framer-motion';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  ReferenceLine,
  ReferenceArea,
} from 'recharts';
import { Flame, Download, AlertCircle, TrendingDown } from 'lucide-react';
import { formatCurrency } from '../../../utils/formatters';
import { Tooltip as UITooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import html2canvas from 'html2canvas';

export interface BurndownData {
  date: string;
  planned: number;
  actual: number;
  projected: number;
  milestones?: {
    label: string;
    value: number;
  }[];
}

interface BudgetBurndownProps {
  data: BurndownData[];
  totalBudget: number;
  currentSpent: number;
  endDate: string;
  milestones?: Array<{ date: string; label: string }>;
  loading?: boolean;
}

const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-popover border rounded-lg shadow-lg p-3">
        <p className="font-medium mb-2">{label}</p>
        {payload.map((entry: any, index: number) => (
          <div key={index} className="flex items-center justify-between gap-4 text-sm">
            <span className="flex items-center gap-2">
              <span
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: entry.color }}
              />
              {entry.name}:
            </span>
            <span className="font-medium">{formatCurrency(entry.value)}</span>
          </div>
        ))}
      </div>
    );
  }
  return null;
};

export const BudgetBurndown: React.FC<BudgetBurndownProps> = ({
  data,
  totalBudget,
  currentSpent,
  endDate,
  milestones = [],
  loading = false,
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const [isExporting, setIsExporting] = useState(false);

  const handleExport = async () => {
    if (!chartRef.current) return;
    
    setIsExporting(true);
    try {
      const canvas = await html2canvas(chartRef.current, {
        backgroundColor: '#ffffff',
        scale: 2,
      } as any);
      
      // Convert to image and download
      const link = document.createElement('a');
      link.download = `budget-burndown-${new Date().toISOString().split('T')[0]}.png`;
      link.href = canvas.toDataURL();
      link.click();
    } catch (error) {
      console.error('Failed to export chart:', error);
    } finally {
      setIsExporting(false);
    }
  };

  if (loading) {
    return (
      <Card className="p-6">
        <Skeleton className="h-6 w-48 mb-4" />
        <Skeleton className="h-64 w-full" />
      </Card>
    );
  }

  if (!data || data.length === 0) {
    return (
      <Card className="p-6">
        <div className="text-center py-8 text-muted-foreground">
          <Flame className="h-12 w-12 mx-auto mb-3 opacity-50" />
          <p>No burndown data available</p>
        </div>
      </Card>
    );
  }

  // Calculate key metrics
  const remainingBudget = totalBudget - currentSpent;
  const burnRate = currentSpent / data.length; // Average daily burn
  const projectedEnd = data[data.length - 1]?.projected || currentSpent;
  const isOverBudget = projectedEnd > totalBudget;
  const utilizationRate = (currentSpent / totalBudget) * 100;

  // Determine budget health status
  const getHealthStatus = () => {
    if (utilizationRate > 90) return { color: 'destructive', label: 'Critical' };
    if (utilizationRate > 75) return { color: 'default', label: 'Warning' };
    return { color: 'secondary', label: 'Healthy' };
  };

  const healthStatus = getHealthStatus();

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.2 }}
    >
      <Card className="p-6" ref={chartRef}>
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <Flame className="h-5 w-5 text-muted-foreground" />
            <h3 className="text-lg font-semibold">Budget Burndown</h3>
            <Badge variant={healthStatus.color as any}>
              {healthStatus.label}
            </Badge>
          </div>
          <div className="flex items-center gap-2">
            <TooltipProvider>
              <UITooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={handleExport}
                    disabled={isExporting}
                  >
                    <Download className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Export as image</TooltipContent>
              </UITooltip>
            </TooltipProvider>
          </div>
        </div>

        {/* Alert if over budget projected */}
        {isOverBudget && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-4 p-3 bg-destructive/10 border border-destructive/20 rounded-lg flex items-center gap-2"
          >
            <AlertCircle className="h-4 w-4 text-destructive" />
            <p className="text-sm">
              Projected to exceed budget by {formatCurrency(projectedEnd - totalBudget)}
            </p>
          </motion.div>
        )}

        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart
              data={data}
              margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
            >
              <defs>
                <linearGradient id="colorPlanned" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.8} />
                  <stop offset="95%" stopColor="#3b82f6" stopOpacity={0.1} />
                </linearGradient>
                <linearGradient id="colorActual" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#10b981" stopOpacity={0.8} />
                  <stop offset="95%" stopColor="#10b981" stopOpacity={0.1} />
                </linearGradient>
                <linearGradient id="colorProjected" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#f59e0b" stopOpacity={0.8} />
                  <stop offset="95%" stopColor="#f59e0b" stopOpacity={0.1} />
                </linearGradient>
              </defs>

              <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
              <XAxis
                dataKey="date"
                className="text-xs"
                tick={{ fill: 'currentColor' }}
              />
              <YAxis
                className="text-xs"
                tick={{ fill: 'currentColor' }}
                tickFormatter={(value) => `${(value / 1000).toFixed(0)}k`}
              />
              <Tooltip content={<CustomTooltip />} />
              <Legend
                wrapperStyle={{ paddingTop: '20px' }}
                iconType="line"
              />

              {/* Budget limit line */}
              <ReferenceLine
                y={totalBudget}
                stroke="#ef4444"
                strokeDasharray="5 5"
                label={{ value: "Budget Limit", position: "right" }}
              />

              {/* Danger zone */}
              <ReferenceArea
                y1={totalBudget * 0.9}
                y2={totalBudget}
                stroke="#f59e0b"
                strokeOpacity={0.3}
                fill="#f59e0b"
                fillOpacity={0.1}
              />

              {/* Critical zone */}
              <ReferenceArea
                y1={totalBudget}
                y2={totalBudget * 1.2}
                stroke="#ef4444"
                strokeOpacity={0.3}
                fill="#ef4444"
                fillOpacity={0.1}
              />

              {/* Milestone lines */}
              {milestones.map((milestone, index) => (
                <ReferenceLine
                  key={index}
                  x={milestone.date}
                  stroke="#6366f1"
                  strokeDasharray="3 3"
                  label={{ value: milestone.label, position: "top", angle: -45 }}
                />
              ))}

              {/* Data areas */}
              <Area
                type="monotone"
                dataKey="planned"
                stroke="#3b82f6"
                fillOpacity={1}
                fill="url(#colorPlanned)"
                strokeWidth={2}
                name="Planned"
              />
              <Area
                type="monotone"
                dataKey="actual"
                stroke="#10b981"
                fillOpacity={1}
                fill="url(#colorActual)"
                strokeWidth={2}
                name="Actual"
              />
              <Area
                type="monotone"
                dataKey="projected"
                stroke="#f59e0b"
                fillOpacity={1}
                fill="url(#colorProjected)"
                strokeWidth={2}
                strokeDasharray="5 5"
                name="Projected"
              />
            </AreaChart>
          </ResponsiveContainer>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-4 gap-4 mt-6 pt-6 border-t">
          <div>
            <p className="text-sm text-muted-foreground">Total Budget</p>
            <p className="text-lg font-semibold">{formatCurrency(totalBudget)}</p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Current Spent</p>
            <p className="text-lg font-semibold text-green-600">
              {formatCurrency(currentSpent)}
            </p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Remaining</p>
            <p className={`text-lg font-semibold ${remainingBudget < 0 ? 'text-destructive' : ''}`}>
              {formatCurrency(Math.abs(remainingBudget))}
            </p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Burn Rate</p>
            <div className="flex items-center gap-1">
              <TrendingDown className="h-4 w-4" />
              <p className="text-lg font-semibold">
                {formatCurrency(burnRate)}/day
              </p>
            </div>
          </div>
        </div>

        {/* Projection Summary */}
        <div className="mt-4 p-4 bg-muted/50 rounded-lg">
          <p className="text-sm">
            At current burn rate, budget will be{' '}
            <span className={`font-semibold ${isOverBudget ? 'text-destructive' : 'text-green-600'}`}>
              {isOverBudget ? 'exceeded' : 'within limits'}
            </span>{' '}
            by {endDate}
          </p>
        </div>
      </Card>
    </motion.div>
  );
};