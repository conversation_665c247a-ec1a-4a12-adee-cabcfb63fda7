import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  RadialBarChart,
  RadialBar,
  PolarAngleAxis,
} from 'recharts';
import { 
  TrendingUp, 
  Users, 
  Target, 
  DollarSign,
  Plus,
  Award,
  BookOpen,
  Clock
} from 'lucide-react';
import { formatCurrency, formatPercentage } from '../../../utils/formatters';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';

export interface ROIMetric {
  id: string;
  trainingName: string;
  category: string;
  totalCost: number;
  employeesTrained: number;
  completionRate: number;
  performanceImprovement: number;
  estimatedValueGenerated: number;
  timeToROI: number; // in months
  skillsAcquired: string[];
  measurementDate: string;
}

interface ROITrackingProps {
  metrics: ROIMetric[];
  onAddMetric: (metric: Omit<ROIMetric, 'id'>) => void;
  onUpdateMetric: (id: string, metric: Partial<ROIMetric>) => void;
  loading?: boolean;
}

const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-popover border rounded-lg shadow-lg p-3">
        <p className="font-medium mb-2">{label}</p>
        {payload.map((entry: any, index: number) => (
          <div key={index} className="flex items-center justify-between gap-4 text-sm">
            <span className="flex items-center gap-2">
              <span
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: entry.color }}
              />
              {entry.name}:
            </span>
            <span className="font-medium">
              {entry.name.includes('%') 
                ? formatPercentage(entry.value / 100)
                : entry.name.includes('$') || entry.name === 'ROI'
                ? formatCurrency(entry.value)
                : entry.value}
            </span>
          </div>
        ))}
      </div>
    );
  }
  return null;
};

export const ROITracking: React.FC<ROITrackingProps> = ({
  metrics,
  onAddMetric,
  onUpdateMetric,
  loading = false,
}) => {
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [formData, setFormData] = useState({
    trainingName: '',
    category: '',
    totalCost: 0,
    employeesTrained: 0,
    completionRate: 85,
    performanceImprovement: 0,
    estimatedValueGenerated: 0,
    timeToROI: 6,
    skillsAcquired: [] as string[],
  });
  
  const [activeTab, setActiveTab] = useState('overview');

  const handleSubmit = () => {
    onAddMetric({
      ...formData,
      measurementDate: new Date().toISOString().split('T')[0],
    });
    setShowAddDialog(false);
    setFormData({
      trainingName: '',
      category: '',
      totalCost: 0,
      employeesTrained: 0,
      completionRate: 85,
      performanceImprovement: 0,
      estimatedValueGenerated: 0,
      timeToROI: 6,
      skillsAcquired: [],
    });
  };

  if (loading) {
    return (
      <Card className="p-6">
        <Skeleton className="h-6 w-48 mb-4" />
        <Skeleton className="h-64 w-full" />
      </Card>
    );
  }

  // Calculate aggregate metrics
  const aggregateMetrics = {
    totalInvestment: metrics.reduce((sum, m) => sum + m.totalCost, 0),
    totalValue: metrics.reduce((sum, m) => sum + m.estimatedValueGenerated, 0),
    totalEmployees: metrics.reduce((sum, m) => sum + m.employeesTrained, 0),
    avgCompletion: metrics.length > 0
      ? metrics.reduce((sum, m) => sum + m.completionRate, 0) / metrics.length
      : 0,
    avgImprovement: metrics.length > 0
      ? metrics.reduce((sum, m) => sum + m.performanceImprovement, 0) / metrics.length
      : 0,
    overallROI: 0,
  };
  
  aggregateMetrics.overallROI = aggregateMetrics.totalInvestment > 0
    ? ((aggregateMetrics.totalValue - aggregateMetrics.totalInvestment) / aggregateMetrics.totalInvestment) * 100
    : 0;

  // Prepare data for charts
  const roiByTraining = metrics.map(m => ({
    name: m.trainingName,
    roi: m.totalCost > 0 ? ((m.estimatedValueGenerated - m.totalCost) / m.totalCost) * 100 : 0,
    cost: m.totalCost,
    value: m.estimatedValueGenerated,
  })).sort((a, b) => b.roi - a.roi);

  const performanceData = metrics.map(m => ({
    name: m.trainingName,
    completion: m.completionRate,
    improvement: m.performanceImprovement,
    employees: m.employeesTrained,
  }));

  const radialData = [
    {
      name: 'Overall ROI',
      value: Math.min(aggregateMetrics.overallROI, 200),
      fill: aggregateMetrics.overallROI > 100 ? '#10b981' : '#f59e0b',
    },
  ];

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.5 }}
    >
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <TrendingUp className="h-5 w-5 text-muted-foreground" />
            <h3 className="text-lg font-semibold">ROI Tracking</h3>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowAddDialog(true)}
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Metric
          </Button>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <Card className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Investment</p>
                <p className="text-2xl font-bold">{formatCurrency(aggregateMetrics.totalInvestment)}</p>
              </div>
              <DollarSign className="h-8 w-8 text-muted-foreground opacity-50" />
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Value Generated</p>
                <p className="text-2xl font-bold text-green-600">
                  {formatCurrency(aggregateMetrics.totalValue)}
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-600 opacity-50" />
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Employees Trained</p>
                <p className="text-2xl font-bold">{aggregateMetrics.totalEmployees}</p>
              </div>
              <Users className="h-8 w-8 text-muted-foreground opacity-50" />
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Avg Performance +</p>
                <p className="text-2xl font-bold">{aggregateMetrics.avgImprovement.toFixed(1)}%</p>
              </div>
              <Target className="h-8 w-8 text-muted-foreground opacity-50" />
            </div>
          </Card>
        </div>

        {metrics.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <Award className="h-12 w-12 mx-auto mb-3 opacity-50" />
            <p>No ROI metrics tracked yet</p>
            <p className="text-sm mt-1">Add metrics to track training effectiveness</p>
          </div>
        ) : (
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="overview">ROI Overview</TabsTrigger>
              <TabsTrigger value="performance">Performance</TabsTrigger>
              <TabsTrigger value="details">Training Details</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="mt-4 space-y-4">
              {/* Overall ROI Gauge */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card className="p-4">
                  <h4 className="font-medium mb-4 text-center">Overall ROI</h4>
                  <div className="h-48">
                    <ResponsiveContainer width="100%" height="100%">
                      <RadialBarChart
                        cx="50%"
                        cy="50%"
                        innerRadius="60%"
                        outerRadius="90%"
                        data={radialData}
                        startAngle={180}
                        endAngle={0}
                      >
                        <PolarAngleAxis
                          type="number"
                          domain={[0, 200]}
                          angleAxisId={0}
                          tick={false}
                        />
                        <RadialBar
                          background
                          dataKey="value"
                          cornerRadius={10}
                          fill="#10b981"
                        />
                      </RadialBarChart>
                    </ResponsiveContainer>
                    <div className="text-center -mt-20">
                      <p className="text-3xl font-bold">
                        {aggregateMetrics.overallROI.toFixed(1)}%
                      </p>
                      <p className="text-sm text-muted-foreground">Return on Investment</p>
                    </div>
                  </div>
                </Card>

                <Card className="p-4">
                  <h4 className="font-medium mb-4">Cost-Benefit Analysis</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Investment</span>
                      <span className="font-medium">{formatCurrency(aggregateMetrics.totalInvestment)}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Value Generated</span>
                      <span className="font-medium text-green-600">
                        {formatCurrency(aggregateMetrics.totalValue)}
                      </span>
                    </div>
                    <div className="border-t pt-3">
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium">Net Benefit</span>
                        <span className="font-bold text-lg">
                          {formatCurrency(aggregateMetrics.totalValue - aggregateMetrics.totalInvestment)}
                        </span>
                      </div>
                    </div>
                  </div>
                </Card>
              </div>

              {/* ROI by Training */}
              <Card className="p-4">
                <h4 className="font-medium mb-4">ROI by Training Program</h4>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={roiByTraining}>
                      <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
                      <XAxis
                        dataKey="name"
                        className="text-xs"
                        tick={{ fill: 'currentColor' }}
                        angle={-45}
                        textAnchor="end"
                        height={80}
                      />
                      <YAxis
                        className="text-xs"
                        tick={{ fill: 'currentColor' }}
                        tickFormatter={(value) => `${value}%`}
                      />
                      <Tooltip content={<CustomTooltip />} />
                      <Bar
                        dataKey="roi"
                        fill="#3b82f6"
                        name="ROI %"
                        radius={[8, 8, 0, 0]}
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </Card>
            </TabsContent>

            <TabsContent value="performance" className="mt-4">
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={performanceData}>
                    <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
                    <XAxis
                      dataKey="name"
                      className="text-xs"
                      tick={{ fill: 'currentColor' }}
                      angle={-45}
                      textAnchor="end"
                      height={80}
                    />
                    <YAxis
                      className="text-xs"
                      tick={{ fill: 'currentColor' }}
                      tickFormatter={(value) => `${value}%`}
                    />
                    <Tooltip content={<CustomTooltip />} />
                    <Legend wrapperStyle={{ paddingTop: '20px' }} />
                    <Bar
                      dataKey="completion"
                      fill="#10b981"
                      name="Completion Rate %"
                    />
                    <Bar
                      dataKey="improvement"
                      fill="#3b82f6"
                      name="Performance Improvement %"
                    />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </TabsContent>

            <TabsContent value="details" className="mt-4">
              <div className="space-y-3">
                {metrics.map((metric) => {
                  const roi = metric.totalCost > 0
                    ? ((metric.estimatedValueGenerated - metric.totalCost) / metric.totalCost) * 100
                    : 0;
                  const costPerEmployee = metric.employeesTrained > 0
                    ? metric.totalCost / metric.employeesTrained
                    : 0;

                  return (
                    <Card key={metric.id} className="p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <BookOpen className="h-5 w-5 text-muted-foreground" />
                            <h5 className="font-medium">{metric.trainingName}</h5>
                            <Badge variant="secondary">{metric.category}</Badge>
                            <Badge 
                              variant={roi > 100 ? 'default' : 'secondary'}
                              className={roi > 100 ? 'bg-green-500' : ''}
                            >
                              ROI: {roi.toFixed(1)}%
                            </Badge>
                          </div>
                          
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                            <div>
                              <p className="text-muted-foreground">Investment</p>
                              <p className="font-medium">{formatCurrency(metric.totalCost)}</p>
                            </div>
                            <div>
                              <p className="text-muted-foreground">Value Generated</p>
                              <p className="font-medium text-green-600">
                                {formatCurrency(metric.estimatedValueGenerated)}
                              </p>
                            </div>
                            <div>
                              <p className="text-muted-foreground">Employees</p>
                              <p className="font-medium">{metric.employeesTrained}</p>
                            </div>
                            <div>
                              <p className="text-muted-foreground">Cost/Employee</p>
                              <p className="font-medium">{formatCurrency(costPerEmployee)}</p>
                            </div>
                          </div>

                          <div className="flex items-center gap-6 mt-3 text-sm">
                            <div className="flex items-center gap-1">
                              <Target className="h-4 w-4 text-muted-foreground" />
                              <span>{metric.completionRate}% completion</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <TrendingUp className="h-4 w-4 text-muted-foreground" />
                              <span>+{metric.performanceImprovement}% performance</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Clock className="h-4 w-4 text-muted-foreground" />
                              <span>{metric.timeToROI} months to ROI</span>
                            </div>
                          </div>

                          {metric.skillsAcquired.length > 0 && (
                            <div className="mt-3">
                              <p className="text-sm text-muted-foreground mb-1">Skills Acquired:</p>
                              <div className="flex flex-wrap gap-1">
                                {metric.skillsAcquired.map((skill, idx) => (
                                  <Badge key={idx} variant="outline" className="text-xs">
                                    {skill}
                                  </Badge>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </Card>
                  );
                })}
              </div>
            </TabsContent>
          </Tabs>
        )}
      </Card>

      {/* Add Metric Dialog */}
      <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Track Training ROI</DialogTitle>
            <DialogDescription>
              Add metrics to track the return on investment for training programs.
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="trainingName">Training Name</Label>
                <Input
                  id="trainingName"
                  value={formData.trainingName}
                  onChange={(e) => setFormData({ ...formData, trainingName: e.target.value })}
                  placeholder="e.g., React Advanced Workshop"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="category">Category</Label>
                <Input
                  id="category"
                  value={formData.category}
                  onChange={(e) => setFormData({ ...formData, category: e.target.value })}
                  placeholder="e.g., Technical Skills"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="totalCost">Total Cost ($)</Label>
                <Input
                  id="totalCost"
                  type="number"
                  value={formData.totalCost}
                  onChange={(e) => setFormData({ ...formData, totalCost: parseFloat(e.target.value) || 0 })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="employeesTrained">Employees Trained</Label>
                <Input
                  id="employeesTrained"
                  type="number"
                  value={formData.employeesTrained}
                  onChange={(e) => setFormData({ ...formData, employeesTrained: parseInt(e.target.value) || 0 })}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="completionRate">Completion Rate (%)</Label>
                <Input
                  id="completionRate"
                  type="number"
                  min={0}
                  max={100}
                  value={formData.completionRate}
                  onChange={(e) => setFormData({ ...formData, completionRate: parseFloat(e.target.value) || 0 })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="performanceImprovement">Performance Improvement (%)</Label>
                <Input
                  id="performanceImprovement"
                  type="number"
                  value={formData.performanceImprovement}
                  onChange={(e) => setFormData({ ...formData, performanceImprovement: parseFloat(e.target.value) || 0 })}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="estimatedValueGenerated">Estimated Value Generated ($)</Label>
                <Input
                  id="estimatedValueGenerated"
                  type="number"
                  value={formData.estimatedValueGenerated}
                  onChange={(e) => setFormData({ ...formData, estimatedValueGenerated: parseFloat(e.target.value) || 0 })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="timeToROI">Time to ROI (months)</Label>
                <Input
                  id="timeToROI"
                  type="number"
                  value={formData.timeToROI}
                  onChange={(e) => setFormData({ ...formData, timeToROI: parseInt(e.target.value) || 0 })}
                />
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAddDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleSubmit} disabled={!formData.trainingName || formData.totalCost <= 0}>
              Add Metric
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </motion.div>
  );
};