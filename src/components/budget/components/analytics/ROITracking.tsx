import React from 'react';
import { RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar, ResponsiveContainer, Legend } from 'recharts';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { TrendingUp, Target, Award, DollarSign } from 'lucide-react';

interface ROIMetric {
  category: string;
  currentROI: number;
  targetROI: number;
  investment: number;
  return: number;
}

interface ROITrackingProps {
  data: ROIMetric[];
  overallROI?: number;
  totalInvestment?: number;
  totalReturn?: number;
}

export const ROITracking: React.FC<ROITrackingProps> = ({ 
  data,
  overallROI = 0,
  totalInvestment = 0,
  totalReturn = 0
}) => {
  const radarData = data.map(item => ({
    category: item.category,
    current: (item.currentROI / item.targetROI) * 100,
    target: 100
  }));

  const topPerformers = [...data]
    .sort((a, b) => b.currentROI - a.currentROI)
    .slice(0, 3);

  const underperformers = [...data]
    .filter(item => item.currentROI < item.targetROI)
    .sort((a, b) => a.currentROI - b.currentROI)
    .slice(0, 3);

  const getROIStatus = (current: number, target: number) => {
    const percentage = (current / target) * 100;
    if (percentage >= 100) return { color: 'text-green-600', badge: 'Exceeding' };
    if (percentage >= 80) return { color: 'text-yellow-600', badge: 'On Track' };
    return { color: 'text-red-600', badge: 'Below Target' };
  };

  const overallStatus = getROIStatus(overallROI, 100);

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>ROI Tracking</CardTitle>
            <CardDescription>
              Return on Investment analysis by category
            </CardDescription>
          </div>
          <Badge variant={overallROI >= 100 ? 'default' : 'secondary'}>
            {overallStatus.badge}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-3 gap-4">
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-muted-foreground">
              <TrendingUp className="w-4 h-4" />
              <span className="text-sm">Overall ROI</span>
            </div>
            <p className={`text-2xl font-semibold ${overallStatus.color}`}>
              {overallROI.toFixed(1)}%
            </p>
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-muted-foreground">
              <DollarSign className="w-4 h-4" />
              <span className="text-sm">Total Investment</span>
            </div>
            <p className="text-2xl font-semibold">
              ${(totalInvestment / 1000).toFixed(0)}k
            </p>
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-muted-foreground">
              <Award className="w-4 h-4" />
              <span className="text-sm">Total Return</span>
            </div>
            <p className="text-2xl font-semibold">
              ${(totalReturn / 1000).toFixed(0)}k
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <ResponsiveContainer width="100%" height={300}>
            <RadarChart data={radarData}>
              <PolarGrid className="stroke-muted" />
              <PolarAngleAxis 
                dataKey="category" 
                className="text-xs"
                tick={{ fill: 'currentColor' }}
              />
              <PolarRadiusAxis 
                angle={90} 
                domain={[0, 120]}
                className="text-xs"
                tick={{ fill: 'currentColor' }}
              />
              <Radar
                name="Current Performance"
                dataKey="current"
                stroke="hsl(var(--primary))"
                fill="hsl(var(--primary))"
                fillOpacity={0.6}
              />
              <Radar
                name="Target"
                dataKey="target"
                stroke="hsl(var(--muted-foreground))"
                fill="none"
                strokeDasharray="5 5"
              />
              <Legend />
            </RadarChart>
          </ResponsiveContainer>

          <div className="space-y-4">
            <div>
              <h4 className="text-sm font-medium mb-3">Category Performance</h4>
              {data.map((item) => {
                const status = getROIStatus(item.currentROI, item.targetROI);
                const percentage = Math.min((item.currentROI / item.targetROI) * 100, 100);
                
                return (
                  <div key={item.category} className="space-y-2 mb-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">{item.category}</span>
                      <span className={`text-sm ${status.color}`}>
                        {item.currentROI.toFixed(1)}% / {item.targetROI}%
                      </span>
                    </div>
                    <Progress value={percentage} className="h-2" />
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
            <div className="flex items-center gap-2 mb-3">
              <Target className="w-4 h-4 text-green-600" />
              <h4 className="text-sm font-medium">Top Performers</h4>
            </div>
            {topPerformers.map((item) => (
              <div key={item.category} className="flex items-center justify-between mb-2">
                <span className="text-sm text-muted-foreground">{item.category}</span>
                <span className="text-sm font-medium text-green-600">
                  {item.currentROI.toFixed(1)}%
                </span>
              </div>
            ))}
          </div>

          <div className="p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
            <div className="flex items-center gap-2 mb-3">
              <Target className="w-4 h-4 text-red-600" />
              <h4 className="text-sm font-medium">Need Attention</h4>
            </div>
            {underperformers.map((item) => (
              <div key={item.category} className="flex items-center justify-between mb-2">
                <span className="text-sm text-muted-foreground">{item.category}</span>
                <span className="text-sm font-medium text-red-600">
                  {item.currentROI.toFixed(1)}%
                </span>
              </div>
            ))}
          </div>
        </div>

        <div className="p-4 bg-muted/50 rounded-lg">
          <h4 className="text-sm font-medium mb-2">ROI Insights</h4>
          <ul className="space-y-1 text-sm text-muted-foreground">
            <li>• {topPerformers[0]?.category} shows the highest ROI at {topPerformers[0]?.currentROI.toFixed(1)}%</li>
            <li>• {underperformers.length} categories are below target ROI</li>
            <li>• Average ROI across all categories: {(data.reduce((sum, d) => sum + d.currentROI, 0) / data.length).toFixed(1)}%</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};