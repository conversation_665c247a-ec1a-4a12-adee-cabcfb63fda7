import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, <PERSON>, ResponsiveContainer } from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { TrendingUp, TrendingDown, Minus } from 'lucide-react';

interface SpendingTrendsChartProps {
  data: Array<{
    month: string;
    actual: number;
    budget: number;
    forecast?: number;
  }>;
}

export const SpendingTrendsChart: React.FC<SpendingTrendsChartProps> = ({ data }) => {
  const calculateTrend = () => {
    if (!data || data.length < 2) return { trend: 'stable', percentage: 0 };
    
    const recent = data.slice(-3);
    const avgRecent = recent.reduce((sum, d) => sum + d.actual, 0) / recent.length;
    const avgPrevious = data.slice(-6, -3).reduce((sum, d) => sum + d.actual, 0) / 3;
    
    const change = ((avgRecent - avgPrevious) / avgPrevious) * 100;
    
    if (change > 5) return { trend: 'increasing', percentage: change };
    if (change < -5) return { trend: 'decreasing', percentage: change };
    return { trend: 'stable', percentage: change };
  };

  const { trend, percentage } = calculateTrend();

  const TrendIcon = trend === 'increasing' ? TrendingUp : trend === 'decreasing' ? TrendingDown : Minus;
  const trendColor = trend === 'increasing' ? 'text-red-500' : trend === 'decreasing' ? 'text-green-500' : 'text-gray-500';

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Spending Trends</CardTitle>
          <div className={`flex items-center gap-2 ${trendColor}`}>
            <TrendIcon className="w-4 h-4" />
            <span className="text-sm font-medium">
              {Math.abs(percentage).toFixed(1)}%
            </span>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={350}>
          <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
            <XAxis 
              dataKey="month" 
              className="text-xs"
              tick={{ fill: 'currentColor' }}
            />
            <YAxis 
              className="text-xs"
              tick={{ fill: 'currentColor' }}
              tickFormatter={(value) => `$${(value / 1000).toFixed(0)}k`}
            />
            <Tooltip 
              formatter={(value: number) => `$${value.toLocaleString()}`}
              contentStyle={{ 
                backgroundColor: 'hsl(var(--background))',
                border: '1px solid hsl(var(--border))',
                borderRadius: '6px'
              }}
            />
            <Legend />
            <Line 
              type="monotone" 
              dataKey="actual" 
              stroke="hsl(var(--primary))" 
              strokeWidth={2}
              dot={{ fill: 'hsl(var(--primary))', r: 4 }}
              activeDot={{ r: 6 }}
              name="Actual Spending"
            />
            <Line 
              type="monotone" 
              dataKey="budget" 
              stroke="hsl(var(--muted-foreground))" 
              strokeWidth={2}
              strokeDasharray="5 5"
              dot={{ fill: 'hsl(var(--muted-foreground))', r: 3 }}
              name="Budget"
            />
            {data[0]?.forecast !== undefined && (
              <Line 
                type="monotone" 
                dataKey="forecast" 
                stroke="hsl(var(--accent))" 
                strokeWidth={2}
                strokeDasharray="3 3"
                dot={{ fill: 'hsl(var(--accent))', r: 3 }}
                name="Forecast"
              />
            )}
          </LineChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
};