import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON>po<PERSON>,
  <PERSON>bs,
  Tab,
  <PERSON>ert,
  Alert<PERSON><PERSON>le,
  Skeleton,
  Grid,
  Paper,
  useTheme,
  alpha,
  IconButton,
  Chip,
  Stack,
  LinearProgress,
  Badge,
  Tooltip,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Button,
  Divider
} from '@mui/material';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  RefreshRounded,
  DownloadRounded,
  FilterListRounded,
  TrendingDown,
  InfoOutlined,
  WarningAmberRounded,
  AttachMoneyRounded,
  AssessmentRounded
} from '@mui/icons-material';
import { useBudgetAnalytics } from '@/hooks/queries/useBudgetQueries';
import { formatCurrency } from '../../utils/formatters';
import CountUp from 'react-countup';

interface BudgetAnalyticsProps {
  year: number;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`analytics-tabpanel-${index}`}
      aria-labelledby={`analytics-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
    </div>
  );
}

interface MetricCardProps {
  title: string;
  value: number | string;
  change?: number;
  icon: React.ReactNode;
  color: string;
  format?: 'currency' | 'percent' | 'number';
}

const MetricCard: React.FC<MetricCardProps> = ({ 
  title, 
  value, 
  change, 
  icon, 
  color,
  format = 'number' 
}) => {
  const theme = useTheme();
  
  const formatValue = (val: number | string) => {
    if (typeof val === 'string') return val;
    switch (format) {
      case 'currency':
        return formatCurrency(val);
      case 'percent':
        return `${val.toFixed(1)}%`;
      default:
        return val.toLocaleString();
    }
  };

  return (
    <Paper
      elevation={0}
      sx={{
        p: 2.5,
        borderRadius: 2,
        border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
        background: `linear-gradient(135deg, ${alpha(color, 0.03)} 0%, ${alpha(color, 0.01)} 100%)`,
        transition: 'all 0.3s ease',
        '&:hover': {
          transform: 'translateY(-2px)',
          boxShadow: theme.shadows[4],
          borderColor: alpha(color, 0.2)
        }
      }}
    >
      <Box sx={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between' }}>
        <Box>
          <Typography variant="caption" color="text.secondary" sx={{ fontWeight: 500 }}>
            {title}
          </Typography>
          <Typography variant="h4" sx={{ fontWeight: 700, mt: 0.5, color }}>
            {typeof value === 'number' ? (
              <CountUp end={value} duration={1.5} decimals={format === 'percent' ? 1 : 0} />
            ) : value}
            {format === 'percent' && '%'}
          </Typography>
          {change !== undefined && (
            <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
              {change > 0 ? (
                <TrendingUp sx={{ fontSize: 16, color: theme.palette.success.main, mr: 0.5 }} />
              ) : (
                <TrendingDown sx={{ fontSize: 16, color: theme.palette.error.main, mr: 0.5 }} />
              )}
              <Typography variant="caption" color={change > 0 ? 'success.main' : 'error.main'}>
                {change > 0 ? '+' : ''}{change.toFixed(1)}%
              </Typography>
            </Box>
          )}
        </Box>
        <Box
          sx={{
            width: 48,
            height: 48,
            borderRadius: 2,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            bgcolor: alpha(color, 0.1),
            color
          }}
        >
          {icon}
        </Box>
      </Box>
    </Paper>
  );
};

export const BudgetAnalyticsMUI: React.FC<BudgetAnalyticsProps> = ({ year }) => {
  const theme = useTheme();
  const { data: analytics, isLoading, error, refetch } = useBudgetAnalytics(year);
  const [tabValue, setTabValue] = useState(0);
  const [exportFormat, setExportFormat] = useState('pdf');

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleExport = () => {
    console.log(`Exporting as ${exportFormat}`);
  };

  if (isLoading) {
    return (
      <Stack spacing={2}>
        <Skeleton variant="rectangular" height={120} sx={{ borderRadius: 2 }} />
        <Grid container spacing={2}>
          {[1, 2, 3, 4].map((i) => (
            <Grid item xs={12} sm={6} md={3} key={i}>
              <Skeleton variant="rectangular" height={140} sx={{ borderRadius: 2 }} />
            </Grid>
          ))}
        </Grid>
        <Skeleton variant="rectangular" height={400} sx={{ borderRadius: 2 }} />
      </Stack>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ borderRadius: 2 }}>
        <AlertTitle>Error Loading Analytics</AlertTitle>
        Failed to load analytics data: {error.message}
      </Alert>
    );
  }

  if (!analytics) {
    return (
      <Alert severity="info" sx={{ borderRadius: 2 }}>
        <AlertTitle>No Data Available</AlertTitle>
        No analytics data available for fiscal year {year}
      </Alert>
    );
  }

  const currentQuarter = Math.ceil((new Date().getMonth() + 1) / 3);

  return (
    <Box sx={{ width: '100%' }}>
      {/* Header Section */}
      <Paper
        elevation={0}
        sx={{
          p: 3,
          mb: 3,
          borderRadius: 2,
          background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.08)} 0%, ${alpha(theme.palette.primary.light, 0.03)} 100%)`,
          border: `1px solid ${alpha(theme.palette.primary.main, 0.08)}`
        }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', flexWrap: 'wrap', gap: 2 }}>
          <Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
              <AssessmentRounded sx={{ color: theme.palette.primary.main }} />
              <Typography variant="h5" fontWeight={700} color="primary.main">
                Budget Analytics Dashboard
              </Typography>
              <Chip
                label={`FY ${year}`}
                size="small"
                color="primary"
                sx={{ fontWeight: 600 }}
              />
              <Chip
                label={`Q${currentQuarter}`}
                size="small"
                variant="outlined"
                color="primary"
              />
            </Box>
            <Typography variant="body2" color="text.secondary">
              Real-time financial insights and performance metrics
            </Typography>
          </Box>
          <Stack direction="row" spacing={1}>
            <FormControl size="small" sx={{ minWidth: 100 }}>
              <Select
                value={exportFormat}
                onChange={(e) => setExportFormat(e.target.value)}
                sx={{ bgcolor: 'background.paper' }}
              >
                <MenuItem value="pdf">PDF</MenuItem>
                <MenuItem value="csv">CSV</MenuItem>
                <MenuItem value="json">JSON</MenuItem>
              </Select>
            </FormControl>
            <Button
              variant="outlined"
              size="small"
              startIcon={<DownloadRounded />}
              onClick={handleExport}
            >
              Export
            </Button>
            <Tooltip title="Refresh Data">
              <IconButton onClick={() => refetch()} color="primary">
                <RefreshRounded />
              </IconButton>
            </Tooltip>
            <Tooltip title="Filter Options">
              <IconButton color="primary">
                <FilterListRounded />
              </IconButton>
            </Tooltip>
          </Stack>
        </Box>
      </Paper>

      {/* Metrics Grid */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Total Budget"
            value={analytics.metrics.totalBudget}
            change={12.5}
            icon={<AttachMoneyRounded />}
            color={theme.palette.primary.main}
            format="currency"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Total Spent"
            value={analytics.metrics.totalSpent}
            change={-8.3}
            icon={<TrendingUp />}
            color={theme.palette.success.main}
            format="currency"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Utilization Rate"
            value={analytics.metrics.utilizationRate}
            change={5.2}
            icon={<PieChart />}
            color={theme.palette.info.main}
            format="percent"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Variance"
            value={Math.abs(analytics.metrics.variance)}
            change={analytics.metrics.variance > 0 ? 3.1 : -3.1}
            icon={<BarChart />}
            color={theme.palette.warning.main}
            format="currency"
          />
        </Grid>
      </Grid>

      {/* Main Content Tabs */}
      <Paper elevation={0} sx={{ borderRadius: 2, overflow: 'hidden' }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider', bgcolor: 'background.paper' }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            variant="fullWidth"
            sx={{
              '& .MuiTab-root': {
                textTransform: 'none',
                fontWeight: 600,
                fontSize: '0.875rem',
                minHeight: 56,
                '&.Mui-selected': {
                  color: theme.palette.primary.main,
                }
              },
              '& .MuiTabs-indicator': {
                height: 3,
                borderRadius: '3px 3px 0 0'
              }
            }}
          >
            <Tab
              icon={<ShowChart />}
              iconPosition="start"
              label="Spending Trends"
              sx={{ gap: 1 }}
            />
            <Tab
              icon={<PieChart />}
              iconPosition="start"
              label="Category Breakdown"
              sx={{ gap: 1 }}
            />
            <Tab
              icon={<BarChart />}
              iconPosition="start"
              label="Department Analysis"
              sx={{ gap: 1 }}
            />
            <Tab
              icon={<TrendingUp />}
              iconPosition="start"
              label="Forecast & Predictions"
              sx={{ gap: 1 }}
            />
          </Tabs>
        </Box>

        <Box sx={{ p: 3, minHeight: 400 }}>
          <TabPanel value={tabValue} index={0}>
            <Typography variant="h6" gutterBottom>
              Monthly Spending Trends
            </Typography>
            <Box sx={{ height: 350, display: 'flex', alignItems: 'center', justifyContent: 'center', bgcolor: 'background.default', borderRadius: 2 }}>
              <Typography color="text.secondary">
                Spending Trends Chart - Connect React ChartJS 2
              </Typography>
            </Box>
          </TabPanel>

          <TabPanel value={tabValue} index={1}>
            <Typography variant="h6" gutterBottom>
              Budget Distribution by Category
            </Typography>
            <Grid container spacing={2}>
              {analytics.categoryBreakdown?.map((category: any, index: number) => (
                <Grid item xs={12} md={6} key={index}>
                  <Paper sx={{ p: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2" fontWeight={600}>
                        {category.category}
                      </Typography>
                      <Typography variant="body2" color="primary">
                        {formatCurrency(category.amount)}
                      </Typography>
                    </Box>
                    <LinearProgress
                      variant="determinate"
                      value={category.percentage}
                      sx={{ height: 8, borderRadius: 4 }}
                    />
                    <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5 }}>
                      {category.percentage.toFixed(1)}% of total budget
                    </Typography>
                  </Paper>
                </Grid>
              ))}
            </Grid>
          </TabPanel>

          <TabPanel value={tabValue} index={2}>
            <Typography variant="h6" gutterBottom>
              Department Performance Metrics
            </Typography>
            <Box sx={{ height: 350, display: 'flex', alignItems: 'center', justifyContent: 'center', bgcolor: 'background.default', borderRadius: 2 }}>
              <Typography color="text.secondary">
                Department Comparison Chart - Connect React ChartJS 2
              </Typography>
            </Box>
          </TabPanel>

          <TabPanel value={tabValue} index={3}>
            <Typography variant="h6" gutterBottom>
              Financial Forecasting
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} md={4}>
                <Paper sx={{ p: 2, textAlign: 'center' }}>
                  <Typography variant="caption" color="text.secondary">
                    Projected Year-End
                  </Typography>
                  <Typography variant="h4" color="primary" sx={{ fontWeight: 700, mt: 1 }}>
                    {formatCurrency(analytics.forecast?.projectedYearEnd || 0)}
                  </Typography>
                  <Chip
                    label={`${analytics.forecast?.confidence || 0}% Confidence`}
                    size="small"
                    color="success"
                    sx={{ mt: 1 }}
                  />
                </Paper>
              </Grid>
              <Grid item xs={12} md={8}>
                <Box sx={{ height: 250, display: 'flex', alignItems: 'center', justifyContent: 'center', bgcolor: 'background.default', borderRadius: 2 }}>
                  <Typography color="text.secondary">
                    Forecast Chart - Connect React ChartJS 2
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </TabPanel>
        </Box>
      </Paper>
    </Box>
  );
};