import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  ComposedChart,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Brush,
  ReferenceLine,
  ReferenceArea,
  TooltipProps,
} from 'recharts';
import {
  Play,
  Pause,
  RefreshCw,
  Activity,
  LineChart as LineChartIcon,
  BarChart3,
  Maximize,
  Minimize,
  MoreVertical,
  Download,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
} from 'lucide-react';
import * as DropdownMenu from '@radix-ui/react-dropdown-menu';
import { cn } from '@/lib/utils';
import { motion, AnimatePresence } from 'framer-motion';

interface DataPoint {
  timestamp: string;
  value: number;
  prediction?: number;
  target?: number;
  category?: string;
  metadata?: Record<string, any>;
}

interface RealTimeChartProps {
  title: string;
  dataKey: string;
  predictionKey?: string;
  interval?: number;
  maxDataPoints?: number;
  chartType?: 'line' | 'area' | 'bar' | 'composed';
  showBrush?: boolean;
  showPrediction?: boolean;
  showTarget?: boolean;
  onDataUpdate?: (data: DataPoint[]) => void;
  dataSource?: () => Promise<DataPoint>;
  thresholds?: {
    warning?: number;
    critical?: number;
    success?: number;
  };
  enableExport?: boolean;
  enableFullscreen?: boolean;
  animationDuration?: number;
  customColors?: {
    primary?: string;
    secondary?: string;
    warning?: string;
    success?: string;
    error?: string;
  };
}

const CustomTooltip: React.FC<TooltipProps<number, string>> = ({ active, payload, label }) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-white/95 backdrop-blur-sm border border-gray-200 rounded-lg p-3 shadow-lg">
        <p className="text-sm text-gray-600 mb-2">{label}</p>
        {payload.map((entry, index) => (
          <div key={index} className="flex items-center gap-2">
            <div
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: entry.color }}
            />
            <span className="text-sm">
              {entry.name}: {typeof entry.value === 'number' ? entry.value.toFixed(2) : entry.value}
            </span>
          </div>
        ))}
      </div>
    );
  }
  
  return null;
};

export const RealTimeChart: React.FC<RealTimeChartProps> = ({
  title,
  dataKey,
  predictionKey = 'prediction',
  interval = 1000,
  maxDataPoints = 50,
  chartType = 'line',
  showBrush = false,
  showPrediction = false,
  showTarget = false,
  onDataUpdate,
  dataSource,
  thresholds,
  enableExport = true,
  enableFullscreen = false,
  animationDuration = 300,
  customColors,
}) => {
  const [data, setData] = useState<DataPoint[]>([]);
  const [isLive, setIsLive] = useState(true);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [selectedChartType, setSelectedChartType] = useState(chartType);
  const [isLoading, setIsLoading] = useState(true);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const chartRef = useRef<HTMLDivElement>(null);

  const colors = {
    primary: customColors?.primary || '#3b82f6',
    secondary: customColors?.secondary || '#8b5cf6',
    warning: customColors?.warning || '#f59e0b',
    success: customColors?.success || '#10b981',
    error: customColors?.error || '#ef4444',
  };

  const generateMockData = useCallback((): DataPoint => {
    const now = new Date();
    const baseValue = 50;
    const variation = Math.sin(Date.now() / 10000) * 20;
    const noise = (Math.random() - 0.5) * 10;
    const value = baseValue + variation + noise;
    
    return {
      timestamp: now.toLocaleTimeString(),
      value: Math.max(0, value),
      prediction: showPrediction ? value + (Math.random() - 0.5) * 5 : undefined,
      target: showTarget ? 55 : undefined,
      metadata: {
        trend: value > baseValue ? 'up' : 'down',
        quality: value > 60 ? 'high' : value > 40 ? 'medium' : 'low',
      },
    };
  }, [showPrediction, showTarget]);

  const fetchData = useCallback(async () => {
    try {
      const newDataPoint = dataSource ? await dataSource() : generateMockData();
      
      setData(prevData => {
        const updatedData = [...prevData, newDataPoint];
        const trimmedData = updatedData.slice(-maxDataPoints);
        
        if (onDataUpdate) {
          onDataUpdate(trimmedData);
        }
        
        return trimmedData;
      });
      
      if (isLoading) {
        setIsLoading(false);
      }
    } catch (error) {
      console.error('Error fetching data:', error);
    }
  }, [dataSource, generateMockData, maxDataPoints, onDataUpdate, isLoading]);

  useEffect(() => {
    if (isLive) {
      fetchData();
      intervalRef.current = setInterval(fetchData, interval);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isLive, interval, fetchData]);

  const handlePlayPause = () => {
    setIsLive(!isLive);
  };

  const handleRefresh = () => {
    setData([]);
    setIsLoading(true);
    fetchData();
  };

  const handleChartTypeChange = (newType: string) => {
    setSelectedChartType(newType as typeof chartType);
  };

  const handleExport = (format: 'csv' | 'json' | 'png') => {
    switch (format) {
      case 'csv':
        const csv = [
          Object.keys(data[0] || {}).join(','),
          ...data.map(row => Object.values(row).join(',')),
        ].join('\n');
        downloadFile(csv, `${title}-${Date.now()}.csv`, 'text/csv');
        break;
      case 'json':
        downloadFile(JSON.stringify(data, null, 2), `${title}-${Date.now()}.json`, 'application/json');
        break;
      case 'png':
        // Convert chart to PNG using canvas
        const chartElement = document.querySelector('.recharts-wrapper svg');
        if (chartElement) {
          const svgData = new XMLSerializer().serializeToString(chartElement);
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');
          const img = new Image();
          
          img.onload = () => {
            canvas.width = img.width;
            canvas.height = img.height;
            ctx?.drawImage(img, 0, 0);
            canvas.toBlob((blob) => {
              if (blob) {
                const url = URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = `${title}-${Date.now()}.png`;
                link.click();
                URL.revokeObjectURL(url);
              }
            }, 'image/png');
          };
          
          img.src = 'data:image/svg+xml;base64,' + btoa(unescape(encodeURIComponent(svgData)));
        }
        break;
    }
  };

  const downloadFile = (content: string, filename: string, type: string) => {
    const blob = new Blob([content], { type });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.click();
    URL.revokeObjectURL(url);
  };

  const renderChart = () => {
    const commonProps = {
      data,
      margin: { top: 5, right: 30, left: 20, bottom: 5 },
    };

    const lineElement = (
      <Line
        type="monotone"
        dataKey={dataKey}
        stroke={colors.primary}
        strokeWidth={2}
        dot={false}
        animationDuration={animationDuration}
        activeDot={{ r: 6 }}
      />
    );

    const predictionLine = showPrediction && (
      <Line
        type="monotone"
        dataKey={predictionKey}
        stroke={colors.secondary}
        strokeWidth={2}
        strokeDasharray="5 5"
        dot={false}
        animationDuration={animationDuration}
      />
    );

    const targetLine = showTarget && (
      <ReferenceLine
        y={data[0]?.target || 0}
        stroke={colors.warning}
        strokeDasharray="3 3"
        label="Target"
      />
    );

    const commonChildren = (
      <>
        <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
        <XAxis dataKey="timestamp" stroke="#6b7280" />
        <YAxis stroke="#6b7280" />
        <Tooltip content={<CustomTooltip />} />
        <Legend />
        {targetLine}
        {thresholds?.warning && (
          <ReferenceArea
            y1={thresholds.warning}
            y2={thresholds.critical || 100}
            fill={colors.warning}
            fillOpacity={0.1}
          />
        )}
        {thresholds?.success && (
          <ReferenceLine
            y={thresholds.success}
            stroke={colors.success}
            strokeDasharray="3 3"
            label="Success"
          />
        )}
        {showBrush && <Brush dataKey="timestamp" height={30} stroke={colors.primary} />}
      </>
    );

    switch (selectedChartType) {
      case 'area':
        return (
          <AreaChart {...commonProps}>
            {commonChildren}
            <Area
              type="monotone"
              dataKey={dataKey}
              stroke={colors.primary}
              fill={`${colors.primary}30`}
              animationDuration={animationDuration}
            />
            {showPrediction && (
              <Area
                type="monotone"
                dataKey={predictionKey}
                stroke={colors.secondary}
                fill={`${colors.secondary}1a`}
                strokeDasharray="5 5"
                animationDuration={animationDuration}
              />
            )}
          </AreaChart>
        );
      case 'bar':
        return (
          <BarChart {...commonProps}>
            {commonChildren}
            <Bar dataKey={dataKey} fill={colors.primary} animationDuration={animationDuration} />
            {showPrediction && (
              <Bar dataKey={predictionKey} fill={`${colors.secondary}80`} animationDuration={animationDuration} />
            )}
          </BarChart>
        );
      case 'composed':
        return (
          <ComposedChart {...commonProps}>
            {commonChildren}
            <Bar dataKey={dataKey} fill={`${colors.primary}80`} animationDuration={animationDuration} />
            {lineElement}
            {predictionLine}
          </ComposedChart>
        );
      default:
        return (
          <LineChart {...commonProps}>
            {commonChildren}
            {lineElement}
            {predictionLine}
          </LineChart>
        );
    }
  };

  const getStatusIcon = () => {
    if (!data.length) return null;
    const latestValue = data[data.length - 1].value;
    
    if (thresholds?.critical && latestValue >= thresholds.critical) {
      return <AlertTriangle className="text-red-500" size={20} />;
    }
    if (thresholds?.warning && latestValue >= thresholds.warning) {
      return <AlertTriangle className="text-yellow-500" size={20} />;
    }
    if (thresholds?.success && latestValue >= thresholds.success) {
      return <CheckCircle className="text-green-500" size={20} />;
    }
    
    const trend = data.length > 1 ? data[data.length - 1].value - data[data.length - 2].value : 0;
    return trend > 0 ? <TrendingUp className="text-blue-500" size={20} /> : <TrendingDown className="text-purple-500" size={20} />;
  };

  return (
    <>
      <div
        ref={chartRef}
        className={cn(
          "bg-white rounded-lg shadow-sm border border-gray-200 p-6",
          isFullscreen && "fixed inset-0 z-50 h-screen w-screen"
        )}
      >
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center gap-3">
              <h3 className="text-lg font-semibold">{title}</h3>
              {getStatusIcon()}
              {isLive && (
                <AnimatePresence>
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    exit={{ scale: 0 }}
                    className="flex items-center gap-1 px-2 py-1 bg-red-100 text-red-600 rounded-full text-xs font-medium"
                  >
                    <span className="w-2 h-2 bg-red-500 rounded-full animate-pulse" />
                    LIVE
                  </motion.div>
                </AnimatePresence>
              )}
            </div>
            
            <div className="flex items-center gap-2">
              <div className="flex rounded-lg border border-gray-200 p-1">
                <button
                  onClick={() => handleChartTypeChange('line')}
                  className={cn(
                    "p-1.5 rounded transition-colors",
                    selectedChartType === 'line' ? "bg-blue-100 text-blue-600" : "text-gray-600 hover:bg-gray-100"
                  )}
                  aria-label="line chart"
                >
                  <LineChartIcon size={16} />
                </button>
                <button
                  onClick={() => handleChartTypeChange('area')}
                  className={cn(
                    "p-1.5 rounded transition-colors",
                    selectedChartType === 'area' ? "bg-blue-100 text-blue-600" : "text-gray-600 hover:bg-gray-100"
                  )}
                  aria-label="area chart"
                >
                  <Activity size={16} />
                </button>
                <button
                  onClick={() => handleChartTypeChange('bar')}
                  className={cn(
                    "p-1.5 rounded transition-colors",
                    selectedChartType === 'bar' ? "bg-blue-100 text-blue-600" : "text-gray-600 hover:bg-gray-100"
                  )}
                  aria-label="bar chart"
                >
                  <BarChart3 size={16} />
                </button>
              </div>
              
              <button
                onClick={handlePlayPause}
                className={cn(
                  "p-2 rounded-lg transition-colors",
                  isLive ? "text-blue-600 hover:bg-blue-50" : "text-gray-600 hover:bg-gray-100"
                )}
              >
                {isLive ? <Pause size={16} /> : <Play size={16} />}
              </button>
              
              <button
                onClick={handleRefresh}
                className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <RefreshCw size={16} />
              </button>
              
              {enableFullscreen && (
                <button
                  onClick={() => setIsFullscreen(!isFullscreen)}
                  className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  {isFullscreen ? <Minimize size={16} /> : <Maximize size={16} />}
                </button>
              )}
              
              {enableExport && (
                <DropdownMenu.Root>
                  <DropdownMenu.Trigger asChild>
                    <button className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
                      <MoreVertical size={16} />
                    </button>
                  </DropdownMenu.Trigger>
                  <DropdownMenu.Portal>
                    <DropdownMenu.Content
                      className="bg-white rounded-lg shadow-lg border border-gray-200 p-1 min-w-[160px] z-50"
                      sideOffset={5}
                    >
                      <DropdownMenu.Item
                        className="flex items-center gap-2 px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded cursor-pointer outline-none"
                        onClick={() => handleExport('csv')}
                      >
                        <Download size={14} /> Export CSV
                      </DropdownMenu.Item>
                      <DropdownMenu.Item
                        className="flex items-center gap-2 px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded cursor-pointer outline-none"
                        onClick={() => handleExport('json')}
                      >
                        <Download size={14} /> Export JSON
                      </DropdownMenu.Item>
                      <DropdownMenu.Item
                        className="flex items-center gap-2 px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded cursor-pointer outline-none"
                        onClick={() => handleExport('png')}
                      >
                        <Download size={14} /> Export PNG
                      </DropdownMenu.Item>
                    </DropdownMenu.Content>
                  </DropdownMenu.Portal>
                </DropdownMenu.Root>
              )}
            </div>
          </div>
          
          <div className={cn("w-full", isFullscreen ? "h-[calc(100vh-150px)]" : "h-[400px]")}>
            {isLoading ? (
              <div className="w-full h-full bg-gray-100 animate-pulse rounded" />
            ) : (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5 }}
                className="w-full h-full"
              >
                <ResponsiveContainer width="100%" height="100%">
                  {renderChart()}
                </ResponsiveContainer>
              </motion.div>
            )}
          </div>
          
          {data.length > 0 && (
            <div className="flex justify-center items-center gap-3 mt-4">
              <span className="px-3 py-1 text-sm border border-blue-200 text-blue-700 rounded-full">
                Latest: {data[data.length - 1].value.toFixed(2)}
              </span>
              <span className="px-3 py-1 text-sm border border-gray-200 text-gray-700 rounded-full">
                Points: {data.length}
              </span>
              {showPrediction && data[data.length - 1].prediction && (
                <span className="px-3 py-1 text-sm border border-purple-200 text-purple-700 rounded-full">
                  Prediction: {data[data.length - 1].prediction!.toFixed(2)}
                </span>
              )}
            </div>
          )}
      </div>
      
      {isFullscreen && (
        <div
          className="fixed inset-0 bg-black/50 z-40"
          onClick={() => setIsFullscreen(false)}
        />
      )}
    </>
  );
};

export default RealTimeChart;