import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Calendar,
  Clock,
  AlertCircle,
  ArrowRight,
  DollarSign,
  FileText,
  Users,
  TrendingUp
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { format, isToday, isTomorrow, isPast, differenceInDays } from 'date-fns';

interface BudgetEvent {
  id: string;
  title: string;
  date: Date | string;
  type: 'payment' | 'review' | 'deadline' | 'meeting' | 'report';
  amount?: number;
  priority?: 'high' | 'medium' | 'low';
  description?: string;
}

interface BudgetCalendarPanelProps {
  events: BudgetEvent[];
  loading?: boolean;
  onViewDetails?: (section: string) => void;
}

export const BudgetCalendarPanel: React.FC<BudgetCalendarPanelProps> = ({
  events = [],
  loading = false,
  onViewDetails
}) => {
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getEventIcon = (type: BudgetEvent['type']) => {
    switch (type) {
      case 'payment':
        return DollarSign;
      case 'review':
        return TrendingUp;
      case 'deadline':
        return AlertCircle;
      case 'meeting':
        return Users;
      case 'report':
        return FileText;
      default:
        return Calendar;
    }
  };

  const getEventColor = (type: BudgetEvent['type'], priority?: string) => {
    if (priority === 'high') return 'text-red-600 bg-red-50';
    if (priority === 'medium') return 'text-orange-600 bg-orange-50';
    
    switch (type) {
      case 'payment':
        return 'text-blue-600 bg-blue-50';
      case 'review':
        return 'text-purple-600 bg-purple-50';
      case 'deadline':
        return 'text-red-600 bg-red-50';
      case 'meeting':
        return 'text-green-600 bg-green-50';
      case 'report':
        return 'text-indigo-600 bg-indigo-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  const getDateLabel = (date: Date | string) => {
    const eventDate = typeof date === 'string' ? new Date(date) : date;
    
    if (isToday(eventDate)) return 'Today';
    if (isTomorrow(eventDate)) return 'Tomorrow';
    if (isPast(eventDate)) return 'Past';
    
    const daysUntil = differenceInDays(eventDate, new Date());
    if (daysUntil <= 7) return `In ${daysUntil} days`;
    
    return format(eventDate, 'MMM d');
  };

  const getDateBadgeVariant = (date: Date | string) => {
    const eventDate = typeof date === 'string' ? new Date(date) : date;
    
    if (isPast(eventDate)) return 'secondary';
    if (isToday(eventDate)) return 'default';
    if (isTomorrow(eventDate)) return 'outline';
    
    const daysUntil = differenceInDays(eventDate, new Date());
    if (daysUntil <= 3) return 'destructive';
    if (daysUntil <= 7) return 'outline';
    
    return 'secondary';
  };

  if (loading) {
    return (
      <div className="space-y-3">
        <Skeleton className="h-8 w-full rounded-lg" />
        {[1, 2, 3, 4].map((i) => (
          <Skeleton key={i} className="h-16 w-full rounded-lg" />
        ))}
      </div>
    );
  }

  // Sort events by date
  const sortedEvents = [...events].sort((a, b) => {
    const dateA = typeof a.date === 'string' ? new Date(a.date) : a.date;
    const dateB = typeof b.date === 'string' ? new Date(b.date) : b.date;
    return dateA.getTime() - dateB.getTime();
  });

  // Get upcoming events (next 10)
  const upcomingEvents = sortedEvents
    .filter(event => {
      const eventDate = typeof event.date === 'string' ? new Date(event.date) : event.date;
      return !isPast(eventDate) || isToday(eventDate);
    })
    .slice(0, 10);

  // Count events by type
  const eventCounts = events.reduce((acc, event) => {
    acc[event.type] = (acc[event.type] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  if (events.length === 0) {
    return (
      <Card className="p-4">
        <div className="text-center space-y-2">
          <Calendar className="h-8 w-8 mx-auto text-muted-foreground/50" />
          <p className="text-sm text-muted-foreground">No upcoming events</p>
          <Button
            variant="outline"
            size="sm"
            className="mt-2"
            onClick={() => onViewDetails?.('calendar')}
          >
            Add Event
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <div className="space-y-3">
      {/* Summary Card */}
      <Card className="p-3">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-primary" />
            <span className="text-sm font-medium">Upcoming Events</span>
          </div>
          <Badge variant="outline" className="text-xs">
            {upcomingEvents.length} events
          </Badge>
        </div>
        
        {/* Quick Stats */}
        <div className="grid grid-cols-2 gap-2 text-xs">
          {Object.entries(eventCounts).slice(0, 4).map(([type, count]) => {
            const Icon = getEventIcon(type as BudgetEvent['type']);
            return (
              <div key={type} className="flex items-center gap-1.5">
                <Icon className="h-3 w-3 text-muted-foreground" />
                <span className="text-muted-foreground capitalize">{type}</span>
                <span className="font-medium ml-auto">{count}</span>
              </div>
            );
          })}
        </div>
      </Card>

      {/* Events List */}
      <ScrollArea className="h-[300px]">
        <div className="space-y-2 pr-3">
          {upcomingEvents.map((event) => {
            const Icon = getEventIcon(event.type);
            const eventDate = typeof event.date === 'string' ? new Date(event.date) : event.date;
            const colorClass = getEventColor(event.type, event.priority);
            
            return (
              <Card 
                key={event.id}
                className={cn(
                  "p-3 cursor-pointer hover:shadow-sm transition-all",
                  isPast(eventDate) && !isToday(eventDate) && "opacity-60"
                )}
                onClick={() => onViewDetails?.('calendar')}
              >
                <div className="flex items-start gap-3">
                  <div className={cn("p-1.5 rounded-lg", colorClass)}>
                    <Icon className="h-3.5 w-3.5" />
                  </div>
                  
                  <div className="flex-1 min-w-0 space-y-1">
                    <div className="flex items-start justify-between gap-2">
                      <p className="text-sm font-medium truncate">{event.title}</p>
                      <Badge 
                        variant={getDateBadgeVariant(event.date) as any}
                        className="text-xs shrink-0"
                      >
                        {getDateLabel(event.date)}
                      </Badge>
                    </div>
                    
                    {event.description && (
                      <p className="text-xs text-muted-foreground truncate">
                        {event.description}
                      </p>
                    )}
                    
                    <div className="flex items-center gap-3 text-xs">
                      <div className="flex items-center gap-1 text-muted-foreground">
                        <Clock className="h-3 w-3" />
                        <span>{format(eventDate, 'h:mm a')}</span>
                      </div>
                      
                      {event.amount && (
                        <span className="font-medium">
                          {formatCurrency(event.amount)}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </Card>
            );
          })}
        </div>
      </ScrollArea>

      {/* View All Button */}
      <Button
        variant="outline"
        size="sm"
        className="w-full justify-between"
        onClick={() => onViewDetails?.('calendar')}
      >
        <span>View Full Calendar</span>
        <ArrowRight className="h-3 w-3" />
      </Button>
    </div>
  );
};

export default BudgetCalendarPanel;