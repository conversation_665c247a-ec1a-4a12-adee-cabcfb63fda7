import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { 
  Flame,
  TrendingUp,
  ArrowRight,
  Calendar,
  Info
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { format, startOfMonth, endOfMonth, eachDayOfInterval, isSameMonth } from 'date-fns';
import type { HeatmapData } from '../../../types/budget.types';

interface SpendingHeatmapPanelProps {
  data: HeatmapData | null;
  loading?: boolean;
  onViewDetails?: (section: string) => void;
}

export const SpendingHeatmapPanel: React.FC<SpendingHeatmapPanelProps> = ({
  data,
  loading = false,
  onViewDetails
}) => {
  const currentDate = new Date();
  const monthStart = startOfMonth(currentDate);
  const monthEnd = endOfMonth(currentDate);
  const days = eachDayOfInterval({ start: monthStart, end: monthEnd });

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="space-y-3">
        <Skeleton className="h-8 w-full rounded-lg" />
        <Skeleton className="h-48 w-full rounded-lg" />
        <Skeleton className="h-16 w-full rounded-lg" />
      </div>
    );
  }

  if (!data) {
    return (
      <Card className="p-4">
        <div className="text-center space-y-2">
          <Flame className="h-8 w-8 mx-auto text-muted-foreground/50" />
          <p className="text-sm text-muted-foreground">No spending data available</p>
        </div>
      </Card>
    );
  }

  // Calculate statistics
  const totalSpending = Object.values(data.daily || {}).reduce((sum, val) => sum + val, 0);
  const avgDailySpending = totalSpending / days.length;
  const maxDailySpending = Math.max(...Object.values(data.daily || {}), 0);
  const highSpendingDays = Object.entries(data.daily || {})
    .filter(([_, amount]) => amount > avgDailySpending * 1.5)
    .length;

  // Generate intensity levels for heatmap
  const getIntensity = (amount: number): string => {
    if (amount === 0) return 'bg-gray-100';
    const percentage = (amount / maxDailySpending) * 100;
    if (percentage <= 25) return 'bg-emerald-200';
    if (percentage <= 50) return 'bg-emerald-400';
    if (percentage <= 75) return 'bg-orange-400';
    return 'bg-red-500';
  };

  // Group days by weeks
  const weeks: Date[][] = [];
  let currentWeek: Date[] = [];
  
  // Add padding for the first week
  const firstDayOfWeek = monthStart.getDay();
  for (let i = 0; i < firstDayOfWeek; i++) {
    currentWeek.push(new Date(0)); // Placeholder for empty days
  }

  days.forEach((day) => {
    currentWeek.push(day);
    if (currentWeek.length === 7) {
      weeks.push(currentWeek);
      currentWeek = [];
    }
  });
  
  if (currentWeek.length > 0) {
    // Pad the last week
    while (currentWeek.length < 7) {
      currentWeek.push(new Date(0));
    }
    weeks.push(currentWeek);
  }

  return (
    <div className="space-y-3">
      {/* Header */}
      <Card className="p-3">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <Flame className="h-4 w-4 text-orange-500" />
            <span className="text-sm font-medium">
              {format(currentDate, 'MMMM yyyy')}
            </span>
          </div>
          <Badge variant="outline" className="text-xs">
            {days.filter(d => data.daily?.[format(d, 'yyyy-MM-dd')]).length} active days
          </Badge>
        </div>

        {/* Mini Heatmap Grid */}
        <div className="space-y-1">
          {/* Day labels */}
          <div className="grid grid-cols-7 gap-1 mb-1">
            {['S', 'M', 'T', 'W', 'T', 'F', 'S'].map((day, i) => (
              <div key={i} className="text-xs text-center text-muted-foreground font-medium">
                {day}
              </div>
            ))}
          </div>

          {/* Calendar grid */}
          {weeks.map((week, weekIndex) => (
            <div key={weekIndex} className="grid grid-cols-7 gap-1">
              {week.map((day, dayIndex) => {
                const isPlaceholder = day.getTime() === 0;
                const dateKey = !isPlaceholder ? format(day, 'yyyy-MM-dd') : '';
                const amount = data.daily?.[dateKey] || 0;
                const isToday = !isPlaceholder && format(day, 'yyyy-MM-dd') === format(currentDate, 'yyyy-MM-dd');

                return (
                  <div
                    key={dayIndex}
                    className={cn(
                      "aspect-square rounded-sm relative group cursor-pointer transition-all",
                      isPlaceholder ? "bg-transparent" : getIntensity(amount),
                      isToday && "ring-2 ring-primary ring-offset-1",
                      !isPlaceholder && "hover:scale-110 hover:z-10"
                    )}
                    title={!isPlaceholder ? `${format(day, 'MMM d')}: ${formatCurrency(amount)}` : ''}
                  >
                    {!isPlaceholder && (
                      <span className="text-xs absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 bg-background/90 rounded-sm font-medium">
                        {format(day, 'd')}
                      </span>
                    )}
                  </div>
                );
              })}
            </div>
          ))}
        </div>

        {/* Legend */}
        <div className="flex items-center justify-between mt-3 pt-3 border-t">
          <span className="text-xs text-muted-foreground">Less</span>
          <div className="flex gap-1">
            <div className="w-3 h-3 bg-gray-100 rounded-sm" />
            <div className="w-3 h-3 bg-emerald-200 rounded-sm" />
            <div className="w-3 h-3 bg-emerald-400 rounded-sm" />
            <div className="w-3 h-3 bg-orange-400 rounded-sm" />
            <div className="w-3 h-3 bg-red-500 rounded-sm" />
          </div>
          <span className="text-xs text-muted-foreground">More</span>
        </div>
      </Card>

      {/* Statistics */}
      <Card className="p-3 space-y-2">
        <h4 className="text-xs font-medium text-muted-foreground uppercase tracking-wider">
          Spending Insights
        </h4>
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-xs text-muted-foreground">Total This Month</span>
            <span className="text-xs font-semibold">{formatCurrency(totalSpending)}</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-xs text-muted-foreground">Daily Average</span>
            <span className="text-xs font-semibold">{formatCurrency(avgDailySpending)}</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-xs text-muted-foreground">Peak Day</span>
            <span className="text-xs font-semibold">{formatCurrency(maxDailySpending)}</span>
          </div>
          {highSpendingDays > 0 && (
            <div className="flex items-center gap-1 text-xs text-orange-600">
              <TrendingUp className="h-3 w-3" />
              <span>{highSpendingDays} high spending day{highSpendingDays > 1 ? 's' : ''}</span>
            </div>
          )}
        </div>
      </Card>

      {/* View Details Button */}
      <Button
        variant="outline"
        size="sm"
        className="w-full justify-between"
        onClick={() => onViewDetails?.('heatmap')}
      >
        <span>View Full Heatmap</span>
        <ArrowRight className="h-3 w-3" />
      </Button>
    </div>
  );
};

export default SpendingHeatmapPanel;