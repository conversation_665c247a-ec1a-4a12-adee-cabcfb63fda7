import React from 'react';
import { Card } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  Activity,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Info,
  ArrowRight,
  Zap
} from 'lucide-react';
import { cn } from '@/lib/utils';
import type { HealthMetrics, BudgetStats } from '../../../types/budget.types';

interface BudgetHealthPanelProps {
  metrics: HealthMetrics | null;
  stats: BudgetStats | null;
  loading?: boolean;
  onViewDetails?: (section: string) => void;
}

interface HealthIndicator {
  label: string;
  value: number;
  status: 'good' | 'warning' | 'critical';
  description: string;
}

export const BudgetHealthPanel: React.FC<BudgetHealthPanelProps> = ({
  metrics,
  stats,
  loading = false,
  onViewDetails
}) => {
  if (loading) {
    return (
      <div className="space-y-3">
        <Skeleton className="h-24 w-full rounded-lg" />
        <Skeleton className="h-32 w-full rounded-lg" />
        <Skeleton className="h-20 w-full rounded-lg" />
      </div>
    );
  }

  if (!metrics || !stats) {
    return (
      <Card className="p-4">
        <div className="text-center text-sm text-muted-foreground">
          Health metrics unavailable
        </div>
      </Card>
    );
  }

  const overallScore = calculateOverallHealth(metrics);
  const healthStatus = getHealthStatus(overallScore);
  const recommendations = getRecommendations(metrics, stats);

  const indicators: HealthIndicator[] = [
    {
      label: 'Burn Rate',
      value: metrics.burnRate * 100,
      status: metrics.burnRate > 1.2 ? 'critical' : metrics.burnRate > 1 ? 'warning' : 'good',
      description: `${(metrics.burnRate * 100).toFixed(0)}% of expected`
    },
    {
      label: 'Efficiency',
      value: metrics.efficiencyScore,
      status: metrics.efficiencyScore < 70 ? 'critical' : metrics.efficiencyScore < 85 ? 'warning' : 'good',
      description: `${metrics.efficiencyScore}% efficient`
    },
    {
      label: 'Compliance',
      value: metrics.complianceRate,
      status: metrics.complianceRate < 80 ? 'critical' : metrics.complianceRate < 95 ? 'warning' : 'good',
      description: `${metrics.complianceRate}% compliant`
    },
    {
      label: 'Risk Level',
      value: 100 - metrics.riskScore,
      status: metrics.riskScore > 70 ? 'critical' : metrics.riskScore > 40 ? 'warning' : 'good',
      description: getRiskLevel(metrics.riskScore)
    }
  ];

  return (
    <div className="space-y-3">
      {/* Overall Health Score */}
      <Card className="p-4 space-y-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Activity className="h-4 w-4 text-primary" />
            <span className="text-sm font-medium">Health Score</span>
          </div>
          <Badge 
            variant={healthStatus.variant as any}
            className="text-xs"
          >
            {healthStatus.label}
          </Badge>
        </div>
        
        <div className="space-y-2">
          <div className="flex items-baseline justify-between">
            <span className="text-2xl font-bold">{overallScore}</span>
            <span className="text-xs text-muted-foreground">/ 100</span>
          </div>
          <Progress 
            value={overallScore} 
            className={cn(
              "h-2",
              overallScore < 50 && "bg-red-100",
              overallScore >= 50 && overallScore < 75 && "bg-orange-100",
              overallScore >= 75 && "bg-green-100"
            )}
          />
        </div>
      </Card>

      {/* Health Indicators */}
      <Card className="p-3 space-y-2">
        <h4 className="text-xs font-medium text-muted-foreground uppercase tracking-wider">
          Key Indicators
        </h4>
        <div className="space-y-2">
          {indicators.map((indicator, index) => (
            <div key={index} className="flex items-center justify-between py-1">
              <div className="flex items-center gap-2">
                <div className={cn(
                  "w-2 h-2 rounded-full",
                  indicator.status === 'good' && "bg-green-500",
                  indicator.status === 'warning' && "bg-orange-500",
                  indicator.status === 'critical' && "bg-red-500"
                )} />
                <span className="text-xs font-medium">{indicator.label}</span>
              </div>
              <span className="text-xs text-muted-foreground">
                {indicator.description}
              </span>
            </div>
          ))}
        </div>
      </Card>

      {/* Recommendations */}
      {recommendations.length > 0 && (
        <Card className="p-3 space-y-2">
          <div className="flex items-center gap-2">
            <Zap className="h-4 w-4 text-orange-500" />
            <h4 className="text-xs font-medium">Recommendations</h4>
          </div>
          <div className="space-y-1.5">
            {recommendations.slice(0, 3).map((rec, index) => (
              <div 
                key={index}
                className="flex items-start gap-2 text-xs text-muted-foreground"
              >
                <div className={cn(
                  "mt-0.5 w-1.5 h-1.5 rounded-full flex-shrink-0",
                  rec.priority === 'high' && "bg-red-500",
                  rec.priority === 'medium' && "bg-orange-500",
                  rec.priority === 'low' && "bg-blue-500"
                )} />
                <span className="leading-relaxed">{rec.message}</span>
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* Action Button */}
      <Button
        variant="outline"
        size="sm"
        className="w-full justify-between"
        onClick={() => onViewDetails?.('health')}
      >
        <span>View Detailed Analysis</span>
        <ArrowRight className="h-3 w-3" />
      </Button>
    </div>
  );
};

// Helper functions
function calculateOverallHealth(metrics: HealthMetrics): number {
  const weights = {
    burnRate: 0.25,
    efficiency: 0.25,
    compliance: 0.25,
    risk: 0.25
  };

  const burnRateScore = Math.max(0, 100 - Math.abs(metrics.burnRate - 1) * 100);
  const efficiencyScore = metrics.efficiencyScore;
  const complianceScore = metrics.complianceRate;
  const riskScore = 100 - metrics.riskScore;

  const weightedScore = 
    burnRateScore * weights.burnRate +
    efficiencyScore * weights.efficiency +
    complianceScore * weights.compliance +
    riskScore * weights.risk;

  return Math.round(weightedScore);
}

function getHealthStatus(score: number) {
  if (score >= 85) return { label: 'Excellent', variant: 'default' };
  if (score >= 70) return { label: 'Good', variant: 'secondary' };
  if (score >= 50) return { label: 'Fair', variant: 'outline' };
  return { label: 'Needs Attention', variant: 'destructive' };
}

function getRiskLevel(riskScore: number): string {
  if (riskScore <= 30) return 'Low risk';
  if (riskScore <= 60) return 'Moderate risk';
  return 'High risk';
}

function getRecommendations(metrics: HealthMetrics, stats: BudgetStats) {
  const recommendations: Array<{ message: string; priority: 'high' | 'medium' | 'low' }> = [];

  if (metrics.burnRate > 1.2) {
    recommendations.push({
      message: 'Spending rate exceeds budget allocation. Consider reviewing expenses.',
      priority: 'high'
    });
  }

  if (metrics.efficiencyScore < 70) {
    recommendations.push({
      message: 'Budget efficiency is low. Identify cost-saving opportunities.',
      priority: 'medium'
    });
  }

  if (metrics.complianceRate < 90) {
    recommendations.push({
      message: 'Some expenses may not comply with budget policies.',
      priority: 'medium'
    });
  }

  if (metrics.riskScore > 60) {
    recommendations.push({
      message: 'High budget risk detected. Review spending patterns.',
      priority: 'high'
    });
  }

  const utilization = stats.total > 0 ? (stats.spent / stats.total) * 100 : 0;
  if (utilization > 90) {
    recommendations.push({
      message: 'Budget nearly depleted. Plan for remaining period.',
      priority: 'high'
    });
  }

  return recommendations;
}

export default BudgetHealthPanel;