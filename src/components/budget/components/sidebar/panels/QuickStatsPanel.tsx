import React from 'react';
import { Card } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  PiggyBank,
  AlertCircle,
  ArrowRight
} from 'lucide-react';
import { cn } from '@/lib/utils';
import type { BudgetStats } from '../../../types/budget.types';

interface QuickStatsPanelProps {
  stats: BudgetStats | null;
  loading?: boolean;
  onViewDetails?: (section: string) => void;
}

export const QuickStatsPanel: React.FC<QuickStatsPanelProps> = ({
  stats,
  loading = false,
  onViewDetails
}) => {
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (value: number): string => {
    return `${value.toFixed(1)}%`;
  };

  if (loading) {
    return (
      <div className="space-y-3">
        {[1, 2, 3, 4].map((i) => (
          <Skeleton key={i} className="h-16 w-full rounded-lg" />
        ))}
      </div>
    );
  }

  if (!stats) {
    return (
      <Card className="p-4">
        <div className="text-center text-sm text-muted-foreground">
          No budget data available
        </div>
      </Card>
    );
  }

  const utilization = stats.total > 0 ? (stats.spent / stats.total) * 100 : 0;
  const remaining = stats.total - stats.spent - stats.committed;
  const isOverBudget = remaining < 0;
  const trend = stats.trend || 0;

  const quickStats = [
    {
      label: 'Total Budget',
      value: formatCurrency(stats.total),
      icon: DollarSign,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      label: 'Spent',
      value: formatCurrency(stats.spent),
      subValue: formatPercentage(utilization),
      icon: TrendingUp,
      color: utilization > 80 ? 'text-orange-600' : 'text-green-600',
      bgColor: utilization > 80 ? 'bg-orange-50' : 'bg-green-50',
    },
    {
      label: 'Remaining',
      value: formatCurrency(Math.abs(remaining)),
      icon: PiggyBank,
      color: isOverBudget ? 'text-red-600' : 'text-emerald-600',
      bgColor: isOverBudget ? 'bg-red-50' : 'bg-emerald-50',
      alert: isOverBudget,
    },
    {
      label: 'Trend',
      value: `${trend > 0 ? '+' : ''}${trend.toFixed(1)}%`,
      icon: trend > 0 ? TrendingUp : TrendingDown,
      color: trend > 0 ? 'text-red-600' : 'text-green-600',
      bgColor: trend > 0 ? 'bg-red-50' : 'bg-green-50',
      subValue: 'vs last month',
    },
  ];

  return (
    <div className="space-y-4">
      {/* Overall Progress */}
      <Card className="p-4 space-y-3">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-muted-foreground">Budget Utilization</span>
          <span className="text-sm font-bold">{formatPercentage(utilization)}</span>
        </div>
        <Progress 
          value={utilization} 
          className={cn(
            "h-3",
            utilization > 90 && "bg-red-100",
            utilization > 75 && utilization <= 90 && "bg-orange-100"
          )}
        />
        {utilization > 75 && (
          <div className="flex items-center gap-2 text-sm text-orange-600">
            <AlertCircle className="h-4 w-4" />
            <span>High utilization</span>
          </div>
        )}
      </Card>

      {/* Quick Stats Grid */}
      <div className="space-y-3">
        {quickStats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <Card 
              key={index}
              className="p-4 hover:shadow-md transition-all cursor-pointer hover:scale-[1.02]"
              onClick={() => onViewDetails?.('overview')}
            >
              <div className="flex items-center gap-4">
                <div className={cn("p-3 rounded-lg", stat.bgColor)}>
                  <Icon className={cn("h-5 w-5", stat.color)} />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm text-muted-foreground font-medium">{stat.label}</p>
                  <div className="flex items-baseline gap-2">
                    <p className="text-lg font-bold truncate">{stat.value}</p>
                    {stat.subValue && (
                      <span className="text-sm text-muted-foreground">{stat.subValue}</span>
                    )}
                  </div>
                </div>
                {stat.alert && (
                  <AlertCircle className="h-4 w-4 text-red-500 flex-shrink-0" />
                )}
              </div>
            </Card>
          );
        })}
      </div>

      {/* View Details Button */}
      <Button
        variant="outline"
        size="sm"
        className="w-full justify-between"
        onClick={() => onViewDetails?.('overview')}
      >
        <span>View Full Dashboard</span>
        <ArrowRight className="h-3 w-3" />
      </Button>
    </div>
  );
};

export default QuickStatsPanel;