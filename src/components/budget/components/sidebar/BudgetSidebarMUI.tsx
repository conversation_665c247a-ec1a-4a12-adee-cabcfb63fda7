import React, { useState } from 'react';
import {
  Box,
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  IconButton,
  Typography,
  Divider,
  Tooltip,
  Badge,
  Chip,
  Stack,
  Collapse,
  useTheme,
  alpha,
  Paper
} from '@mui/material';
import {
  ChevronLeft,
  ChevronRight,
  Dashboard,
  Assessment,
  Whatshot,
  CalendarMonth,
  TrendingUp,
  HealthAndSafety,
  Notifications,
  Settings
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';

// Sub-components
import { BudgetHealthPanel } from './panels/BudgetHealthPanel';
import { SpendingHeatmapPanel } from './panels/SpendingHeatmapPanel';
import { BudgetCalendarPanel } from './panels/BudgetCalendarPanel';
import { QuickStatsPanel } from './panels/QuickStatsPanel';

// Types
import type { BudgetStats, HealthMetrics, HeatmapData } from '../../types/budget.types';

export interface BudgetSidebarMUIProps {
  stats: BudgetStats | null;
  healthMetrics: HealthMetrics | null;
  heatmapData: HeatmapData | null;
  calendarEvents: any[];
  loading?: boolean;
  collapsed?: boolean;
  onCollapsedChange?: (collapsed: boolean) => void;
  onViewDetails?: (section: string) => void;
}

type SidebarView = 'overview' | 'health' | 'heatmap' | 'calendar';

const drawerWidth = 280;
const collapsedWidth = 72;

export const BudgetSidebarMUI: React.FC<BudgetSidebarMUIProps> = ({
  stats,
  healthMetrics,
  heatmapData,
  calendarEvents,
  loading = false,
  collapsed = false,
  onCollapsedChange,
  onViewDetails
}) => {
  const theme = useTheme();
  const [activeView, setActiveView] = useState<SidebarView>('overview');

  const menuItems = [
    {
      id: 'overview' as const,
      label: 'Overview',
      icon: <Dashboard />,
      description: 'Quick budget summary',
      badge: null
    },
    {
      id: 'health' as const,
      label: 'Health Score',
      icon: <HealthAndSafety />,
      description: 'Budget health metrics',
      badge: healthMetrics?.score ? (
        <Chip
          label={`${healthMetrics.score}%`}
          size="small"
          color={healthMetrics.score > 80 ? 'success' : healthMetrics.score > 60 ? 'warning' : 'error'}
          sx={{ height: 20, fontSize: '0.7rem' }}
        />
      ) : null
    },
    {
      id: 'heatmap' as const,
      label: 'Spending Heat',
      icon: <Whatshot />,
      description: 'Spending patterns',
      badge: <Badge color="error" variant="dot" />
    },
    {
      id: 'calendar' as const,
      label: 'Calendar',
      icon: <CalendarMonth />,
      description: 'Upcoming events',
      badge: calendarEvents?.length > 0 ? (
        <Chip label={calendarEvents.length} size="small" sx={{ height: 20, fontSize: '0.7rem' }} />
      ) : null
    }
  ];

  const handleToggleCollapse = () => {
    onCollapsedChange?.(!collapsed);
  };

  return (
    <Drawer
      variant="permanent"
      sx={{
        width: collapsed ? collapsedWidth : drawerWidth,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: collapsed ? collapsedWidth : drawerWidth,
          boxSizing: 'border-box',
          transition: theme.transitions.create('width', {
            easing: theme.transitions.easing.sharp,
            duration: theme.transitions.duration.leavingScreen,
          }),
          overflowX: 'hidden',
          borderRight: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
          bgcolor: theme.palette.background.paper,
          backgroundImage: `linear-gradient(180deg, ${alpha(theme.palette.primary.main, 0.02)} 0%, transparent 100%)`
        }
      }}
    >
      {/* Header */}
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: collapsed ? 'center' : 'space-between',
          height: 64,
          px: collapsed ? 1 : 2,
          borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`
        }}
      >
        {!collapsed && (
          <Stack direction="row" spacing={1} alignItems="center">
            <Assessment sx={{ color: theme.palette.primary.main }} />
            <Typography variant="subtitle1" fontWeight={600}>
              Budget Hub
            </Typography>
          </Stack>
        )}
        <IconButton
          onClick={handleToggleCollapse}
          size="small"
          sx={{
            bgcolor: alpha(theme.palette.action.hover, 0.04),
            '&:hover': {
              bgcolor: alpha(theme.palette.action.hover, 0.08)
            }
          }}
        >
          {collapsed ? <ChevronRight /> : <ChevronLeft />}
        </IconButton>
      </Box>

      {/* Navigation Menu */}
      <List sx={{ px: collapsed ? 0.5 : 1, py: 1 }}>
        {menuItems.map((item) => {
          const isActive = activeView === item.id;
          return (
            <ListItem
              key={item.id}
              disablePadding
              sx={{ mb: 0.5 }}
            >
              <Tooltip
                title={collapsed ? item.label : ''}
                placement="right"
                arrow
              >
                <ListItemButton
                  selected={isActive}
                  onClick={() => setActiveView(item.id)}
                  sx={{
                    borderRadius: 2,
                    mx: collapsed ? 0.5 : 0,
                    minHeight: 48,
                    justifyContent: collapsed ? 'center' : 'initial',
                    px: collapsed ? 0 : 2,
                    bgcolor: isActive ? alpha(theme.palette.primary.main, 0.08) : 'transparent',
                    '&:hover': {
                      bgcolor: alpha(theme.palette.primary.main, 0.12)
                    },
                    '&.Mui-selected': {
                      bgcolor: alpha(theme.palette.primary.main, 0.08),
                      borderLeft: `3px solid ${theme.palette.primary.main}`,
                      '&:hover': {
                        bgcolor: alpha(theme.palette.primary.main, 0.12)
                      }
                    }
                  }}
                >
                  <ListItemIcon
                    sx={{
                      minWidth: 0,
                      mr: collapsed ? 0 : 2,
                      justifyContent: 'center',
                      color: isActive ? theme.palette.primary.main : theme.palette.text.secondary
                    }}
                  >
                    {item.icon}
                  </ListItemIcon>
                  {!collapsed && (
                    <>
                      <ListItemText
                        primary={item.label}
                        primaryTypographyProps={{
                          fontSize: '0.875rem',
                          fontWeight: isActive ? 600 : 500
                        }}
                      />
                      {item.badge}
                    </>
                  )}
                </ListItemButton>
              </Tooltip>
            </ListItem>
          );
        })}
      </List>

      <Divider sx={{ mx: 2 }} />

      {/* Content Area */}
      <Box sx={{ flex: 1, overflow: 'auto', p: collapsed ? 1 : 2 }}>
        {!collapsed && (
          <AnimatePresence mode="wait">
            <motion.div
              key={activeView}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              transition={{ duration: 0.2 }}
            >
              {activeView === 'overview' && (
                <QuickStatsMUI stats={stats} loading={loading} />
              )}
              {activeView === 'health' && (
                <BudgetHealthMUI metrics={healthMetrics} loading={loading} />
              )}
              {activeView === 'heatmap' && (
                <SpendingHeatmapMUI data={heatmapData} loading={loading} />
              )}
              {activeView === 'calendar' && (
                <BudgetCalendarMUI events={calendarEvents} loading={loading} />
              )}
            </motion.div>
          </AnimatePresence>
        )}
      </Box>

      {/* Footer */}
      {!collapsed && (
        <Box
          sx={{
            p: 2,
            borderTop: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
            bgcolor: alpha(theme.palette.background.default, 0.4)
          }}
        >
          <Stack direction="row" justifyContent="space-between" alignItems="center">
            <Typography variant="caption" color="text.secondary">
              Last sync
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {loading ? 'Updating...' : '2 min ago'}
            </Typography>
          </Stack>
        </Box>
      )}
    </Drawer>
  );
};

// Quick Stats Panel with MUI
const QuickStatsMUI: React.FC<{ stats: BudgetStats | null; loading: boolean }> = ({ stats, loading }) => {
  const theme = useTheme();
  
  if (loading || !stats) {
    return <Typography variant="body2">Loading...</Typography>;
  }

  const items = [
    { label: 'Total Budget', value: stats.totalBudget, format: 'currency' },
    { label: 'Spent', value: stats.totalSpent, format: 'currency' },
    { label: 'Remaining', value: stats.remaining, format: 'currency' },
    { label: 'Utilization', value: stats.utilizationRate, format: 'percent' }
  ];

  return (
    <Stack spacing={2}>
      <Typography variant="subtitle2" fontWeight={600}>
        Quick Stats
      </Typography>
      {items.map((item) => (
        <Paper
          key={item.label}
          elevation={0}
          sx={{
            p: 1.5,
            borderRadius: 1.5,
            border: `1px solid ${alpha(theme.palette.divider, 0.08)}`,
            bgcolor: alpha(theme.palette.background.default, 0.4)
          }}
        >
          <Typography variant="caption" color="text.secondary">
            {item.label}
          </Typography>
          <Typography variant="h6" fontWeight={700} color="primary">
            {item.format === 'currency' 
              ? `$${(item.value / 1000).toFixed(1)}k`
              : `${item.value}%`
            }
          </Typography>
        </Paper>
      ))}
    </Stack>
  );
};

// Budget Health Panel with MUI
const BudgetHealthMUI: React.FC<{ metrics: HealthMetrics | null; loading: boolean }> = ({ metrics, loading }) => {
  const theme = useTheme();
  
  if (loading || !metrics) {
    return <Typography variant="body2">Loading...</Typography>;
  }

  const getHealthColor = (score: number) => {
    if (score >= 80) return theme.palette.success.main;
    if (score >= 60) return theme.palette.warning.main;
    return theme.palette.error.main;
  };

  return (
    <Stack spacing={2}>
      <Typography variant="subtitle2" fontWeight={600}>
        Health Score
      </Typography>
      <Paper
        elevation={0}
        sx={{
          p: 2,
          borderRadius: 2,
          border: `2px solid ${getHealthColor(metrics.score)}`,
          bgcolor: alpha(getHealthColor(metrics.score), 0.05)
        }}
      >
        <Typography variant="h3" fontWeight={700} color={getHealthColor(metrics.score)}>
          {metrics.score}%
        </Typography>
        <Typography variant="body2" color="text.secondary">
          {metrics.status}
        </Typography>
      </Paper>
      
      <Stack spacing={1}>
        {metrics.recommendations?.map((rec, idx) => (
          <Chip
            key={idx}
            label={rec}
            size="small"
            variant="outlined"
            sx={{ height: 'auto', py: 0.5 }}
          />
        ))}
      </Stack>
    </Stack>
  );
};

// Spending Heatmap Panel with MUI
const SpendingHeatmapMUI: React.FC<{ data: HeatmapData | null; loading: boolean }> = ({ data, loading }) => {
  const theme = useTheme();
  
  if (loading || !data) {
    return <Typography variant="body2">Loading...</Typography>;
  }

  return (
    <Stack spacing={2}>
      <Typography variant="subtitle2" fontWeight={600}>
        Spending Heatmap
      </Typography>
      <Box
        sx={{
          display: 'grid',
          gridTemplateColumns: 'repeat(7, 1fr)',
          gap: 0.5
        }}
      >
        {data.days?.slice(0, 28).map((day, idx) => (
          <Tooltip key={idx} title={`$${day.amount}`}>
            <Box
              sx={{
                aspectRatio: '1',
                borderRadius: 0.5,
                bgcolor: alpha(
                  theme.palette.error.main,
                  Math.min(day.amount / 1000, 1) * 0.8
                ),
                cursor: 'pointer',
                transition: 'transform 0.2s',
                '&:hover': {
                  transform: 'scale(1.1)'
                }
              }}
            />
          </Tooltip>
        ))}
      </Box>
    </Stack>
  );
};

// Budget Calendar Panel with MUI
const BudgetCalendarMUI: React.FC<{ events: any[]; loading: boolean }> = ({ events, loading }) => {
  const theme = useTheme();
  
  if (loading) {
    return <Typography variant="body2">Loading...</Typography>;
  }

  return (
    <Stack spacing={2}>
      <Typography variant="subtitle2" fontWeight={600}>
        Upcoming Events
      </Typography>
      {events?.length === 0 ? (
        <Typography variant="body2" color="text.secondary">
          No upcoming events
        </Typography>
      ) : (
        <Stack spacing={1}>
          {events?.slice(0, 5).map((event, idx) => (
            <Paper
              key={idx}
              elevation={0}
              sx={{
                p: 1,
                borderRadius: 1,
                border: `1px solid ${alpha(theme.palette.divider, 0.08)}`
              }}
            >
              <Typography variant="caption" fontWeight={600}>
                {event.title}
              </Typography>
              <Typography variant="caption" display="block" color="text.secondary">
                {event.date}
              </Typography>
            </Paper>
          ))}
        </Stack>
      )}
    </Stack>
  );
};

export default BudgetSidebarMUI;