import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { 
  Activity,
  Flame,
  Calendar,
  ChevronLeft,
  ChevronRight,
  TrendingUp,
  AlertCircle,
  Info
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

// Sub-components
import { BudgetHealthPanel } from './panels/BudgetHealthPanel';
import { SpendingHeatmapPanel } from './panels/SpendingHeatmapPanel';
import { BudgetCalendarPanel } from './panels/BudgetCalendarPanel';
import { QuickStatsPanel } from './panels/QuickStatsPanel';

// Types
import type { BudgetStats, HealthMetrics, HeatmapData } from '../../types/budget.types';

export interface BudgetSidebarProps {
  stats: BudgetStats | null;
  healthMetrics: HealthMetrics | null;
  heatmapData: HeatmapData | null;
  calendarEvents: any[];
  loading?: boolean;
  collapsed?: boolean;
  onCollapsedChange?: (collapsed: boolean) => void;
  onViewDetails?: (section: string) => void;
  className?: string;
}

type SidebarView = 'overview' | 'health' | 'heatmap' | 'calendar';

export const BudgetSidebar: React.FC<BudgetSidebarProps> = ({
  stats,
  healthMetrics,
  heatmapData,
  calendarEvents,
  loading = false,
  collapsed = false,
  onCollapsedChange,
  onViewDetails,
  className
}) => {
  const [activeView, setActiveView] = useState<SidebarView>('overview');

  const viewConfig = [
    { 
      id: 'overview' as const, 
      label: 'Overview', 
      icon: TrendingUp,
      description: 'Quick budget summary'
    },
    { 
      id: 'health' as const, 
      label: 'Health', 
      icon: Activity,
      description: 'Budget health metrics'
    },
    { 
      id: 'heatmap' as const, 
      label: 'Heatmap', 
      icon: Flame,
      description: 'Spending patterns'
    },
    { 
      id: 'calendar' as const, 
      label: 'Calendar', 
      icon: Calendar,
      description: 'Upcoming events'
    }
  ];

  const handleToggleCollapse = () => {
    onCollapsedChange?.(!collapsed);
  };

  return (
    <TooltipProvider>
      <aside
        className={cn(
          "relative flex flex-col bg-background border-r border-border transition-all duration-300 h-full",
          collapsed ? "w-16" : "w-80", // Increased to 320px width when expanded, 64px when collapsed
          className
        )}
        aria-label="Budget sidebar"
        aria-expanded={!collapsed}
      >
        {/* Header */}
        <div className="flex items-center justify-between h-14 px-3 border-b border-border">
          <AnimatePresence mode="wait">
            {!collapsed && (
              <motion.div
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -10 }}
                transition={{ duration: 0.2 }}
                className="flex items-center gap-3"
              >
                <Activity className="h-5 w-5 text-primary" />
                <span className="text-base font-semibold">Budget Activity</span>
              </motion.div>
            )}
          </AnimatePresence>
          
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8"
            onClick={handleToggleCollapse}
            aria-label={collapsed ? "Expand sidebar" : "Collapse sidebar"}
          >
            {collapsed ? (
              <ChevronRight className="h-4 w-4" />
            ) : (
              <ChevronLeft className="h-4 w-4" />
            )}
          </Button>
        </div>

        {/* Navigation Tabs */}
        {!collapsed && (
          <div className="flex flex-col gap-2 p-3 border-b border-border">
            {viewConfig.map((view) => {
              const Icon = view.icon;
              const isActive = activeView === view.id;
              
              return (
                <Tooltip key={view.id}>
                  <TooltipTrigger asChild>
                    <Button
                      variant={isActive ? "secondary" : "ghost"}
                      size="default"
                      className={cn(
                        "w-full justify-start gap-3 h-12",
                        isActive && "bg-secondary font-semibold"
                      )}
                      onClick={() => setActiveView(view.id)}
                    >
                      <Icon className="h-5 w-5 flex-shrink-0" />
                      <span className="text-sm truncate">{view.label}</span>
                      {isActive && (
                        <div className="ml-auto w-2 h-5 bg-primary rounded-full" />
                      )}
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="right">
                    <p className="text-sm">{view.description}</p>
                  </TooltipContent>
                </Tooltip>
              );
            })}
          </div>
        )}

        {/* Collapsed State Icons */}
        {collapsed && (
          <div className="flex flex-col gap-1 p-2">
            {viewConfig.map((view) => {
              const Icon = view.icon;
              const isActive = activeView === view.id;
              
              return (
                <Tooltip key={view.id}>
                  <TooltipTrigger asChild>
                    <Button
                      variant={isActive ? "secondary" : "ghost"}
                      size="icon"
                      className={cn(
                        "h-10 w-10",
                        isActive && "bg-secondary"
                      )}
                      onClick={() => setActiveView(view.id)}
                    >
                      <Icon className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="right">
                    <p className="font-medium">{view.label}</p>
                    <p className="text-xs text-muted-foreground">{view.description}</p>
                  </TooltipContent>
                </Tooltip>
              );
            })}
          </div>
        )}

        {/* Content Area */}
        {!collapsed && (
          <ScrollArea className="flex-1">
            <div className="p-4">
              <AnimatePresence mode="wait">
                <motion.div
                  key={activeView}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  transition={{ duration: 0.2 }}
                >
                  {activeView === 'overview' && (
                    <QuickStatsPanel
                      stats={stats}
                      loading={loading}
                      onViewDetails={onViewDetails}
                    />
                  )}
                  
                  {activeView === 'health' && (
                    <BudgetHealthPanel
                      metrics={healthMetrics}
                      stats={stats}
                      loading={loading}
                      onViewDetails={onViewDetails}
                    />
                  )}
                  
                  {activeView === 'heatmap' && (
                    <SpendingHeatmapPanel
                      data={heatmapData}
                      loading={loading}
                      onViewDetails={onViewDetails}
                    />
                  )}
                  
                  {activeView === 'calendar' && (
                    <BudgetCalendarPanel
                      events={calendarEvents}
                      loading={loading}
                      onViewDetails={onViewDetails}
                    />
                  )}
                </motion.div>
              </AnimatePresence>
            </div>
          </ScrollArea>
        )}

        {/* Footer Status */}
        {!collapsed && (
          <div className="p-3 border-t border-border">
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <span>Last updated</span>
              <span>{loading ? 'Updating...' : 'Just now'}</span>
            </div>
          </div>
        )}
      </aside>
    </TooltipProvider>
  );
};

export default BudgetSidebar;