import React, { useState } from 'react';
import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction';
import { Paper, Box, Typography, Chip, Stack, useTheme, alpha, Button } from '@mui/material';
import { motion } from 'framer-motion';
import Swal from 'sweetalert2';
import withReactContent from 'sweetalert2-react-content';
import { v4 as uuidv4 } from 'uuid';
import { formatCurrency } from '../../utils/formatters';

const MySwal = withReactContent(Swal);

interface BudgetEvent {
  id: string;
  title: string;
  start: Date | string;
  end?: Date | string;
  amount?: number;
  category?: string;
  type: 'expense' | 'deadline' | 'review' | 'allocation';
  color?: string;
}

export const InteractiveCalendar: React.FC = () => {
  const theme = useTheme();
  const [events, setEvents] = useState<BudgetEvent[]>([
    {
      id: uuidv4(),
      title: 'Q1 Budget Review',
      start: '2024-03-15',
      type: 'review',
      color: theme.palette.info.main
    },
    {
      id: uuidv4(),
      title: 'Training Expense: $5,000',
      start: '2024-03-20',
      amount: 5000,
      type: 'expense',
      category: 'Training',
      color: theme.palette.success.main
    },
    {
      id: uuidv4(),
      title: 'Budget Allocation Deadline',
      start: '2024-03-25',
      type: 'deadline',
      color: theme.palette.warning.main
    },
    {
      id: uuidv4(),
      title: 'Department Budget Meeting',
      start: '2024-03-28T10:00:00',
      end: '2024-03-28T11:30:00',
      type: 'review',
      color: theme.palette.primary.main
    }
  ]);

  const handleDateClick = (arg: any) => {
    MySwal.fire({
      title: 'Add Budget Event',
      html: `
        <div style="text-align: left;">
          <label>Event Title:</label>
          <input id="event-title" class="swal2-input" placeholder="Enter event title">
          <label>Amount (optional):</label>
          <input id="event-amount" type="number" class="swal2-input" placeholder="Enter amount">
          <label>Type:</label>
          <select id="event-type" class="swal2-input">
            <option value="expense">Expense</option>
            <option value="deadline">Deadline</option>
            <option value="review">Review</option>
            <option value="allocation">Allocation</option>
          </select>
        </div>
      `,
      showCancelButton: true,
      confirmButtonText: 'Add Event',
      confirmButtonColor: theme.palette.primary.main,
      preConfirm: () => {
        const title = (document.getElementById('event-title') as HTMLInputElement).value;
        const amount = (document.getElementById('event-amount') as HTMLInputElement).value;
        const type = (document.getElementById('event-type') as HTMLSelectElement).value;
        
        if (!title) {
          Swal.showValidationMessage('Please enter a title');
          return false;
        }
        
        return { title, amount: amount ? parseFloat(amount) : undefined, type };
      }
    }).then((result) => {
      if (result.isConfirmed) {
        const newEvent: BudgetEvent = {
          id: uuidv4(),
          title: result.value.amount 
            ? `${result.value.title}: ${formatCurrency(result.value.amount)}`
            : result.value.title,
          start: arg.dateStr,
          amount: result.value.amount,
          type: result.value.type,
          color: getEventColor(result.value.type)
        };
        
        setEvents([...events, newEvent]);
        
        MySwal.fire({
          icon: 'success',
          title: 'Event Added!',
          text: `${result.value.title} has been added to the calendar`,
          timer: 2000,
          showConfirmButton: false
        });
      }
    });
  };

  const handleEventClick = (clickInfo: any) => {
    const event = clickInfo.event;
    
    MySwal.fire({
      title: event.title,
      html: `
        <div style="text-align: left;">
          <p><strong>Date:</strong> ${event.start.toLocaleDateString()}</p>
          <p><strong>Type:</strong> ${event.extendedProps.type || 'General'}</p>
          ${event.extendedProps.amount ? `<p><strong>Amount:</strong> ${formatCurrency(event.extendedProps.amount)}</p>` : ''}
          ${event.extendedProps.category ? `<p><strong>Category:</strong> ${event.extendedProps.category}</p>` : ''}
        </div>
      `,
      showCancelButton: true,
      confirmButtonText: 'Edit',
      cancelButtonText: 'Delete',
      cancelButtonColor: theme.palette.error.main,
      confirmButtonColor: theme.palette.primary.main
    }).then((result) => {
      if (result.isConfirmed) {
        // Handle edit
        console.log('Edit event:', event.id);
      } else if (result.dismiss === Swal.DismissReason.cancel) {
        // Handle delete
        clickInfo.event.remove();
        setEvents(events.filter(e => e.id !== event.id));
        
        MySwal.fire({
          icon: 'success',
          title: 'Deleted!',
          text: 'Event has been removed',
          timer: 1500,
          showConfirmButton: false
        });
      }
    });
  };

  const getEventColor = (type: string) => {
    switch (type) {
      case 'expense': return theme.palette.success.main;
      case 'deadline': return theme.palette.warning.main;
      case 'review': return theme.palette.info.main;
      case 'allocation': return theme.palette.primary.main;
      default: return theme.palette.grey[500];
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Paper
        sx={{
          p: 3,
          borderRadius: 3,
          background: alpha(theme.palette.background.paper, 0.9),
          backdropFilter: 'blur(10px)',
          border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
          boxShadow: '0 20px 40px rgba(0,0,0,0.05)'
        }}
      >
        <Box sx={{ mb: 3 }}>
          <Typography variant="h5" fontWeight={700} gutterBottom>
            Budget Calendar
          </Typography>
          <Stack direction="row" spacing={1} sx={{ mt: 2 }}>
            <Chip
              size="small"
              label="Expense"
              sx={{ bgcolor: alpha(theme.palette.success.main, 0.1), color: theme.palette.success.main }}
            />
            <Chip
              size="small"
              label="Deadline"
              sx={{ bgcolor: alpha(theme.palette.warning.main, 0.1), color: theme.palette.warning.main }}
            />
            <Chip
              size="small"
              label="Review"
              sx={{ bgcolor: alpha(theme.palette.info.main, 0.1), color: theme.palette.info.main }}
            />
            <Chip
              size="small"
              label="Allocation"
              sx={{ bgcolor: alpha(theme.palette.primary.main, 0.1), color: theme.palette.primary.main }}
            />
          </Stack>
        </Box>
        
        <Box sx={{ 
          '& .fc': {
            fontFamily: theme.typography.fontFamily
          },
          '& .fc-toolbar-title': {
            fontSize: '1.5rem',
            fontWeight: 600
          },
          '& .fc-button': {
            backgroundColor: theme.palette.primary.main,
            borderColor: theme.palette.primary.main,
            '&:hover': {
              backgroundColor: theme.palette.primary.dark,
              borderColor: theme.palette.primary.dark
            },
            '&:disabled': {
              backgroundColor: theme.palette.action.disabledBackground,
              borderColor: theme.palette.action.disabled
            }
          },
          '& .fc-button-active': {
            backgroundColor: theme.palette.primary.dark,
            borderColor: theme.palette.primary.dark
          },
          '& .fc-daygrid-day-number': {
            color: theme.palette.text.primary,
            fontWeight: 500
          },
          '& .fc-col-header-cell-cushion': {
            color: theme.palette.text.secondary,
            fontWeight: 600
          },
          '& .fc-event': {
            borderRadius: 4,
            border: 'none',
            padding: '2px 4px',
            fontSize: '0.85rem',
            fontWeight: 500,
            cursor: 'pointer',
            transition: 'transform 0.2s',
            '&:hover': {
              transform: 'scale(1.05)'
            }
          },
          '& .fc-daygrid-day': {
            '&:hover': {
              backgroundColor: alpha(theme.palette.primary.main, 0.04)
            }
          },
          '& .fc-day-today': {
            backgroundColor: alpha(theme.palette.primary.main, 0.08),
            '& .fc-daygrid-day-number': {
              color: theme.palette.primary.main,
              fontWeight: 700
            }
          }
        }}>
          <FullCalendar
            plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
            initialView="dayGridMonth"
            headerToolbar={{
              left: 'prev,next today',
              center: 'title',
              right: 'dayGridMonth,timeGridWeek,timeGridDay'
            }}
            events={events}
            dateClick={handleDateClick}
            eventClick={handleEventClick}
            editable={true}
            selectable={true}
            selectMirror={true}
            dayMaxEvents={true}
            weekends={true}
            height="auto"
            eventDisplay="block"
          />
        </Box>
      </Paper>
    </motion.div>
  );
};

export default InteractiveCalendar;