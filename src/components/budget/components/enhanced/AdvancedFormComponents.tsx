import React, { useState, useCallback } from 'react';
import {
  Paper,
  Box,
  Typography,
  Grid,
  Button,
  Stack,
  Chip,
  Avatar,
  IconButton,
  useTheme,
  alpha,
  TextField,
  InputAdornment
} from '@mui/material';
import { Formik, Form, Field } from 'formik';
import * as Yup from 'yup';
import Select from 'react-select';
import CreatableSelect from 'react-select/creatable';
import AsyncSelect from 'react-select/async';
import { TagsInput } from 'react-tag-input-component';
import Flatpickr from 'react-flatpickr';
import 'flatpickr/dist/themes/material_blue.css';
import { useDropzone } from 'react-dropzone';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import { motion, AnimatePresence } from 'framer-motion';
import {
  CloudUpload,
  AttachFile,
  Close,
  CalendarMonth,
  Save,
  Category,
  Label,
  Description,
  AttachMoney
} from '@mui/icons-material';
import Swal from 'sweetalert2';
import { v4 as uuidv4 } from 'uuid';

// Custom React Select Theme
const customSelectStyles = (theme: any) => ({
  control: (provided: any, state: any) => ({
    ...provided,
    backgroundColor: theme.palette.background.paper,
    borderColor: state.isFocused ? theme.palette.primary.main : alpha(theme.palette.divider, 0.2),
    boxShadow: state.isFocused ? `0 0 0 2px ${alpha(theme.palette.primary.main, 0.2)}` : 'none',
    borderRadius: 12,
    padding: '4px',
    transition: 'all 0.2s',
    '&:hover': {
      borderColor: theme.palette.primary.main
    }
  }),
  menu: (provided: any) => ({
    ...provided,
    backgroundColor: theme.palette.background.paper,
    borderRadius: 12,
    boxShadow: '0 10px 40px rgba(0,0,0,0.1)',
    overflow: 'hidden'
  }),
  option: (provided: any, state: any) => ({
    ...provided,
    backgroundColor: state.isSelected 
      ? theme.palette.primary.main 
      : state.isFocused 
      ? alpha(theme.palette.primary.main, 0.1)
      : 'transparent',
    color: state.isSelected ? '#fff' : theme.palette.text.primary,
    padding: '12px 16px',
    cursor: 'pointer',
    transition: 'all 0.2s',
    '&:active': {
      backgroundColor: alpha(theme.palette.primary.main, 0.2)
    }
  }),
  multiValue: (provided: any) => ({
    ...provided,
    backgroundColor: alpha(theme.palette.primary.main, 0.1),
    borderRadius: 8
  }),
  multiValueLabel: (provided: any) => ({
    ...provided,
    color: theme.palette.primary.main,
    fontWeight: 500
  }),
  multiValueRemove: (provided: any) => ({
    ...provided,
    color: theme.palette.primary.main,
    '&:hover': {
      backgroundColor: alpha(theme.palette.primary.main, 0.2),
      color: theme.palette.primary.dark
    }
  })
});

// Validation Schema
const budgetFormSchema = Yup.object().shape({
  title: Yup.string().required('Title is required'),
  amount: Yup.number().positive('Amount must be positive').required('Amount is required'),
  category: Yup.object().required('Category is required'),
  department: Yup.object().required('Department is required'),
  date: Yup.date().required('Date is required'),
  tags: Yup.array().min(1, 'At least one tag is required'),
  description: Yup.string().min(10, 'Description must be at least 10 characters'),
  attachments: Yup.array()
});

// Dropzone Component
const FileDropzone: React.FC<{ onDrop: (files: File[]) => void; files: File[] }> = ({ onDrop, files }) => {
  const theme = useTheme();
  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.png', '.jpg', '.jpeg', '.gif'],
      'application/pdf': ['.pdf'],
      'application/vnd.ms-excel': ['.xls', '.xlsx'],
      'text/csv': ['.csv']
    },
    maxSize: 5242880 // 5MB
  });

  return (
    <Box>
      <Box
        {...getRootProps()}
        sx={{
          border: `2px dashed ${isDragActive ? theme.palette.primary.main : alpha(theme.palette.divider, 0.3)}`,
          borderRadius: 3,
          p: 3,
          textAlign: 'center',
          cursor: 'pointer',
          backgroundColor: isDragActive ? alpha(theme.palette.primary.main, 0.05) : 'transparent',
          transition: 'all 0.3s',
          '&:hover': {
            backgroundColor: alpha(theme.palette.primary.main, 0.02),
            borderColor: theme.palette.primary.main
          }
        }}
      >
        <input {...getInputProps()} />
        <CloudUpload sx={{ fontSize: 48, color: theme.palette.primary.main, mb: 2 }} />
        <Typography variant="h6" gutterBottom>
          {isDragActive ? 'Drop files here' : 'Drag & drop files here'}
        </Typography>
        <Typography variant="body2" color="text.secondary">
          or click to select files (Max 5MB)
        </Typography>
      </Box>
      
      {files.length > 0 && (
        <Stack spacing={1} sx={{ mt: 2 }}>
          <Typography variant="subtitle2">Uploaded Files:</Typography>
          {files.map((file, index) => (
            <Chip
              key={index}
              icon={<AttachFile />}
              label={file.name}
              onDelete={() => {}}
              variant="outlined"
              sx={{ justifyContent: 'flex-start' }}
            />
          ))}
        </Stack>
      )}
    </Box>
  );
};

export const AdvancedFormComponents: React.FC = () => {
  const theme = useTheme();
  const [tags, setTags] = useState<string[]>(['training', 'q1-2024']);
  const [selectedDate, setSelectedDate] = useState<Date[]>([new Date()]);
  const [files, setFiles] = useState<File[]>([]);
  const [richText, setRichText] = useState('');

  // Sample data for selects
  const categoryOptions = [
    { value: 'training', label: 'Training & Development', color: theme.palette.primary.main },
    { value: 'software', label: 'Software Licenses', color: theme.palette.success.main },
    { value: 'conferences', label: 'Conferences', color: theme.palette.warning.main },
    { value: 'certifications', label: 'Certifications', color: theme.palette.info.main },
    { value: 'materials', label: 'Learning Materials', color: theme.palette.secondary.main }
  ];

  const departmentOptions = [
    { value: 'engineering', label: 'Engineering' },
    { value: 'marketing', label: 'Marketing' },
    { value: 'sales', label: 'Sales' },
    { value: 'hr', label: 'Human Resources' },
    { value: 'finance', label: 'Finance' }
  ];

  // Async load function for AsyncSelect
  const loadOptions = (inputValue: string) => {
    return new Promise<any[]>((resolve) => {
      setTimeout(() => {
        resolve(
          departmentOptions.filter(dept =>
            dept.label.toLowerCase().includes(inputValue.toLowerCase())
          )
        );
      }, 1000);
    });
  };

  const handleDrop = useCallback((acceptedFiles: File[]) => {
    setFiles(prev => [...prev, ...acceptedFiles]);
  }, []);

  const quillModules = {
    toolbar: [
      [{ 'header': [1, 2, 3, false] }],
      ['bold', 'italic', 'underline', 'strike'],
      [{ 'list': 'ordered'}, { 'list': 'bullet' }],
      ['link', 'image'],
      ['clean']
    ]
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <Paper
        sx={{
          p: 4,
          borderRadius: 3,
          background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.95)} 0%, ${alpha(theme.palette.background.paper, 0.98)} 100%)`,
          backdropFilter: 'blur(20px)',
          border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
          boxShadow: '0 20px 40px rgba(0,0,0,0.05)'
        }}
      >
        <Typography variant="h5" fontWeight={700} gutterBottom>
          Advanced Budget Form
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 4 }}>
          Create a new budget allocation with advanced controls
        </Typography>

        <Formik
          initialValues={{
            title: '',
            amount: '',
            category: null,
            department: null,
            date: new Date(),
            tags: [],
            description: '',
            attachments: []
          }}
          validationSchema={budgetFormSchema}
          onSubmit={(values, { setSubmitting }) => {
            setTimeout(() => {
              Swal.fire({
                icon: 'success',
                title: 'Budget Created!',
                text: `Budget "${values.title}" has been successfully created`,
                confirmButtonColor: theme.palette.primary.main
              });
              setSubmitting(false);
            }, 1000);
          }}
        >
          {({ values, errors, touched, handleChange, handleBlur, isSubmitting, setFieldValue }) => (
            <Form>
              <Grid container spacing={3}>
                {/* Title Field */}
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    name="title"
                    label="Budget Title"
                    value={values.title}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={touched.title && Boolean(errors.title)}
                    helperText={touched.title && errors.title}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Label />
                        </InputAdornment>
                      )
                    }}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2
                      }
                    }}
                  />
                </Grid>

                {/* Amount Field */}
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    type="number"
                    name="amount"
                    label="Amount"
                    value={values.amount}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={touched.amount && Boolean(errors.amount)}
                    helperText={touched.amount && errors.amount}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <AttachMoney />
                        </InputAdornment>
                      )
                    }}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2
                      }
                    }}
                  />
                </Grid>

                {/* Category Select */}
                <Grid item xs={12} md={6}>
                  <Box>
                    <Typography variant="subtitle2" gutterBottom>
                      Category
                    </Typography>
                    <Select
                      name="category"
                      options={categoryOptions}
                      styles={customSelectStyles(theme)}
                      placeholder="Select category..."
                      isClearable
                      onChange={(option) => setFieldValue('category', option)}
                      formatOptionLabel={(option: any) => (
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Box
                            sx={{
                              width: 8,
                              height: 8,
                              borderRadius: '50%',
                              bgcolor: option.color
                            }}
                          />
                          <span>{option.label}</span>
                        </Box>
                      )}
                    />
                    {touched.category && errors.category && (
                      <Typography variant="caption" color="error" sx={{ mt: 0.5 }}>
                        {errors.category as string}
                      </Typography>
                    )}
                  </Box>
                </Grid>

                {/* Department Async Select */}
                <Grid item xs={12} md={6}>
                  <Box>
                    <Typography variant="subtitle2" gutterBottom>
                      Department
                    </Typography>
                    <AsyncSelect
                      name="department"
                      cacheOptions
                      defaultOptions={departmentOptions}
                      loadOptions={loadOptions}
                      styles={customSelectStyles(theme)}
                      placeholder="Search department..."
                      onChange={(option) => setFieldValue('department', option)}
                    />
                    {touched.department && errors.department && (
                      <Typography variant="caption" color="error" sx={{ mt: 0.5 }}>
                        {errors.department as string}
                      </Typography>
                    )}
                  </Box>
                </Grid>

                {/* Date Picker */}
                <Grid item xs={12} md={6}>
                  <Box>
                    <Typography variant="subtitle2" gutterBottom>
                      Date
                    </Typography>
                    <Flatpickr
                      data-enable-time
                      value={selectedDate}
                      onChange={(dates) => {
                        setSelectedDate(dates);
                        setFieldValue('date', dates[0]);
                      }}
                      options={{
                        dateFormat: 'Y-m-d H:i',
                        altInput: true,
                        altFormat: 'F j, Y at h:i K',
                        theme: 'material_blue'
                      }}
                      render={({ defaultValue, ...props }, ref) => (
                        <TextField
                          {...props}
                          fullWidth
                          inputRef={ref}
                          defaultValue={defaultValue}
                          InputProps={{
                            startAdornment: (
                              <InputAdornment position="start">
                                <CalendarMonth />
                              </InputAdornment>
                            )
                          }}
                          sx={{
                            '& .MuiOutlinedInput-root': {
                              borderRadius: 2
                            }
                          }}
                        />
                      )}
                    />
                  </Box>
                </Grid>

                {/* Tags Input */}
                <Grid item xs={12} md={6}>
                  <Box>
                    <Typography variant="subtitle2" gutterBottom>
                      Tags
                    </Typography>
                    <Box
                      sx={{
                        '& .rti--container': {
                          borderRadius: 2,
                          border: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
                          padding: '8px',
                          minHeight: 56,
                          '&:focus-within': {
                            borderColor: theme.palette.primary.main,
                            boxShadow: `0 0 0 2px ${alpha(theme.palette.primary.main, 0.2)}`
                          }
                        },
                        '& .rti--tag': {
                          backgroundColor: alpha(theme.palette.primary.main, 0.1),
                          color: theme.palette.primary.main,
                          borderRadius: 8,
                          padding: '4px 8px',
                          margin: '2px'
                        },
                        '& .rti--input': {
                          fontSize: '1rem'
                        }
                      }}
                    >
                      <TagsInput
                        value={tags}
                        onChange={(newTags) => {
                          setTags(newTags);
                          setFieldValue('tags', newTags);
                        }}
                        name="tags"
                        placeHolder="Add tags..."
                      />
                    </Box>
                    {touched.tags && errors.tags && (
                      <Typography variant="caption" color="error" sx={{ mt: 0.5 }}>
                        {errors.tags as string}
                      </Typography>
                    )}
                  </Box>
                </Grid>

                {/* Rich Text Editor */}
                <Grid item xs={12}>
                  <Box>
                    <Typography variant="subtitle2" gutterBottom>
                      Description
                    </Typography>
                    <Box
                      sx={{
                        '& .quill': {
                          backgroundColor: theme.palette.background.paper,
                          borderRadius: 2,
                          '& .ql-toolbar': {
                            borderTopLeftRadius: 8,
                            borderTopRightRadius: 8,
                            borderColor: alpha(theme.palette.divider, 0.2)
                          },
                          '& .ql-container': {
                            borderBottomLeftRadius: 8,
                            borderBottomRightRadius: 8,
                            borderColor: alpha(theme.palette.divider, 0.2),
                            minHeight: 150
                          }
                        }
                      }}
                    >
                      <ReactQuill
                        theme="snow"
                        value={richText}
                        onChange={(content) => {
                          setRichText(content);
                          setFieldValue('description', content);
                        }}
                        modules={quillModules}
                        placeholder="Enter detailed description..."
                      />
                    </Box>
                  </Box>
                </Grid>

                {/* File Upload */}
                <Grid item xs={12}>
                  <Box>
                    <Typography variant="subtitle2" gutterBottom>
                      Attachments
                    </Typography>
                    <FileDropzone onDrop={handleDrop} files={files} />
                  </Box>
                </Grid>

                {/* Submit Button */}
                <Grid item xs={12}>
                  <Stack direction="row" spacing={2} justifyContent="flex-end">
                    <Button
                      variant="outlined"
                      size="large"
                      sx={{ borderRadius: 2 }}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      variant="contained"
                      size="large"
                      startIcon={<Save />}
                      disabled={isSubmitting}
                      sx={{
                        borderRadius: 2,
                        background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
                        boxShadow: '0 4px 15px rgba(0,0,0,0.1)'
                      }}
                    >
                      {isSubmitting ? 'Creating...' : 'Create Budget'}
                    </Button>
                  </Stack>
                </Grid>
              </Grid>
            </Form>
          )}
        </Formik>
      </Paper>
    </motion.div>
  );
};

export default AdvancedFormComponents;