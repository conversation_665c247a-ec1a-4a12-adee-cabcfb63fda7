import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  IconButton,
  Stack,
  Paper,
  useTheme,
  alpha,
  Chip,
  Avatar,
  LinearProgress,
  Button,
  Tooltip,
  Badge,
  Container
} from '@mui/material';
import { motion, useAnimation, useInView } from 'framer-motion';
import CountUp from 'react-countup';
import VanillaTilt from 'vanilla-tilt';
import { Line, Doughnut, Bar, Radar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  RadialLinearScale,
  ArcElement,
  Title,
  Tooltip as ChartTooltip,
  Legend,
  Filler
} from 'chart.js';
import {
  TrendingUp,
  TrendingDown,
  AccountBalance,
  ShowChart,
  PieChart,
  Assessment,
  Savings,
  Receipt,
  CalendarMonth,
  AutoGraph,
  Insights,
  Analytics
} from '@mui/icons-material';
import chroma from 'chroma-js';
import { v4 as uuidv4 } from 'uuid';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  RadialLinearScale,
  ArcElement,
  Title,
  ChartTooltip,
  Legend,
  Filler
);

// Soft UI Card Component with Tilt Effect
const SoftUICard: React.FC<{ children: React.ReactNode; gradient?: boolean; color?: string }> = ({ 
  children, 
  gradient = false,
  color = 'primary' 
}) => {
  const theme = useTheme();
  const cardRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (cardRef.current) {
      VanillaTilt.init(cardRef.current, {
        max: 10,
        speed: 400,
        glare: true,
        'max-glare': 0.2,
        gyroscope: true
      });
    }
    return () => {
      if (cardRef.current?.vanillaTilt) {
        cardRef.current.vanillaTilt.destroy();
      }
    };
  }, []);

  const gradientColors = {
    primary: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.9)} 0%, ${alpha(theme.palette.primary.dark, 0.8)} 100%)`,
    success: `linear-gradient(135deg, ${alpha(theme.palette.success.main, 0.9)} 0%, ${alpha(theme.palette.success.dark, 0.8)} 100%)`,
    warning: `linear-gradient(135deg, ${alpha(theme.palette.warning.main, 0.9)} 0%, ${alpha(theme.palette.warning.dark, 0.8)} 100%)`,
    info: `linear-gradient(135deg, ${alpha(theme.palette.info.main, 0.9)} 0%, ${alpha(theme.palette.info.dark, 0.8)} 100%)`
  };

  return (
    <Card
      ref={cardRef}
      sx={{
        borderRadius: 3,
        boxShadow: '0 20px 27px 0 rgba(0,0,0,0.05)',
        background: gradient ? gradientColors[color as keyof typeof gradientColors] : theme.palette.background.paper,
        backdropFilter: 'blur(10px)',
        border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
        overflow: 'visible',
        transformStyle: 'preserve-3d',
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        '&:hover': {
          boxShadow: '0 25px 40px 0 rgba(0,0,0,0.15)',
          transform: 'translateY(-5px)'
        }
      }}
    >
      {children}
    </Card>
  );
};

// Animated Metric Card
const AnimatedMetricCard: React.FC<{
  title: string;
  value: number;
  prefix?: string;
  suffix?: string;
  icon: React.ReactNode;
  color: string;
  trend?: number;
  delay?: number;
}> = ({ title, value, prefix = '', suffix = '', icon, color, trend, delay = 0 }) => {
  const theme = useTheme();
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true });
  const controls = useAnimation();

  useEffect(() => {
    if (isInView) {
      controls.start('visible');
    }
  }, [isInView, controls]);

  return (
    <motion.div
      ref={ref}
      initial="hidden"
      animate={controls}
      variants={{
        hidden: { opacity: 0, y: 50 },
        visible: { opacity: 1, y: 0 }
      }}
      transition={{ duration: 0.5, delay }}
    >
      <SoftUICard gradient color={color}>
        <CardContent sx={{ position: 'relative', zIndex: 1 }}>
          <Stack spacing={2}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
              <Box>
                <Typography variant="caption" sx={{ color: 'rgba(255,255,255,0.8)', fontWeight: 500 }}>
                  {title}
                </Typography>
                <Typography variant="h4" sx={{ fontWeight: 700, color: '#fff', mt: 1 }}>
                  <CountUp
                    start={0}
                    end={value}
                    duration={2}
                    separator=","
                    prefix={prefix}
                    suffix={suffix}
                    decimals={suffix === '%' ? 1 : 0}
                  />
                </Typography>
              </Box>
              <Avatar
                sx={{
                  bgcolor: 'rgba(255,255,255,0.2)',
                  backdropFilter: 'blur(20px)',
                  width: 48,
                  height: 48
                }}
              >
                {icon}
              </Avatar>
            </Box>
            {trend !== undefined && (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                {trend > 0 ? (
                  <TrendingUp sx={{ fontSize: 16, color: '#4caf50' }} />
                ) : (
                  <TrendingDown sx={{ fontSize: 16, color: '#f44336' }} />
                )}
                <Typography variant="caption" sx={{ color: 'rgba(255,255,255,0.9)', fontWeight: 600 }}>
                  {Math.abs(trend)}% from last month
                </Typography>
              </Box>
            )}
          </Stack>
        </CardContent>
      </SoftUICard>
    </motion.div>
  );
};

// Glassmorphism Chart Card
const GlassChartCard: React.FC<{
  title: string;
  subtitle?: string;
  children: React.ReactNode;
  action?: React.ReactNode;
}> = ({ title, subtitle, children, action }) => {
  const theme = useTheme();

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5 }}
    >
      <Paper
        sx={{
          p: 3,
          borderRadius: 3,
          background: alpha(theme.palette.background.paper, 0.8),
          backdropFilter: 'blur(20px)',
          border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
          boxShadow: '0 8px 32px 0 rgba(31, 38, 135, 0.1)',
          overflow: 'hidden',
          position: 'relative',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: 4,
            background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
            borderRadius: '3px 3px 0 0'
          }
        }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Box>
            <Typography variant="h6" fontWeight={700}>
              {title}
            </Typography>
            {subtitle && (
              <Typography variant="caption" color="text.secondary">
                {subtitle}
              </Typography>
            )}
          </Box>
          {action}
        </Box>
        {children}
      </Paper>
    </motion.div>
  );
};

// Main Soft UI Budget Dashboard
export const SoftUIBudgetDashboard: React.FC<{ year: number }> = ({ year }) => {
  const theme = useTheme();
  const [selectedPeriod, setSelectedPeriod] = useState('monthly');

  // Generate color scales using chroma.js
  const colorScale = chroma
    .scale(['#667eea', '#764ba2', '#f093fb', '#fecfef'])
    .mode('lch')
    .colors(6);

  // Sample data for charts
  const lineChartData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    datasets: [
      {
        label: 'Budget',
        data: [50000, 52000, 51000, 53000, 52000, 54000],
        borderColor: theme.palette.primary.main,
        backgroundColor: alpha(theme.palette.primary.main, 0.1),
        borderWidth: 3,
        fill: true,
        tension: 0.4,
        pointRadius: 6,
        pointHoverRadius: 8,
        pointBackgroundColor: '#fff',
        pointBorderColor: theme.palette.primary.main,
        pointBorderWidth: 3
      },
      {
        label: 'Actual',
        data: [48000, 51000, 49000, 52000, 53000, 51000],
        borderColor: theme.palette.success.main,
        backgroundColor: alpha(theme.palette.success.main, 0.1),
        borderWidth: 3,
        fill: true,
        tension: 0.4,
        pointRadius: 6,
        pointHoverRadius: 8,
        pointBackgroundColor: '#fff',
        pointBorderColor: theme.palette.success.main,
        pointBorderWidth: 3
      }
    ]
  };

  const doughnutData = {
    labels: ['Training', 'Conferences', 'Certifications', 'Materials', 'Online Courses'],
    datasets: [{
      data: [35, 25, 20, 10, 10],
      backgroundColor: colorScale.map(c => alpha(c, 0.8)),
      borderColor: colorScale,
      borderWidth: 2,
      hoverOffset: 4
    }]
  };

  const barChartData = {
    labels: ['Q1', 'Q2', 'Q3', 'Q4'],
    datasets: [
      {
        label: 'Planned',
        data: [150000, 160000, 155000, 165000],
        backgroundColor: alpha(theme.palette.primary.main, 0.8),
        borderRadius: 8,
        barThickness: 30
      },
      {
        label: 'Actual',
        data: [145000, 158000, 152000, 0],
        backgroundColor: alpha(theme.palette.success.main, 0.8),
        borderRadius: 8,
        barThickness: 30
      }
    ]
  };

  const radarData = {
    labels: ['Efficiency', 'ROI', 'Utilization', 'Savings', 'Growth', 'Quality'],
    datasets: [{
      label: 'Performance',
      data: [85, 78, 92, 70, 88, 95],
      backgroundColor: alpha(theme.palette.info.main, 0.2),
      borderColor: theme.palette.info.main,
      borderWidth: 2,
      pointBackgroundColor: theme.palette.info.main,
      pointBorderColor: '#fff',
      pointHoverBackgroundColor: '#fff',
      pointHoverBorderColor: theme.palette.info.main
    }]
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: true,
        position: 'top' as const,
        labels: {
          usePointStyle: true,
          padding: 15,
          font: {
            size: 12,
            weight: '600'
          }
        }
      },
      tooltip: {
        backgroundColor: alpha(theme.palette.background.paper, 0.95),
        titleColor: theme.palette.text.primary,
        bodyColor: theme.palette.text.secondary,
        borderColor: theme.palette.divider,
        borderWidth: 1,
        padding: 12,
        cornerRadius: 8,
        displayColors: true,
        callbacks: {
          label: (context: any) => {
            const label = context.dataset.label || '';
            const value = context.parsed.y || context.parsed;
            return `${label}: $${value.toLocaleString()}`;
          }
        }
      }
    },
    scales: {
      x: {
        grid: {
          display: false
        },
        ticks: {
          font: {
            size: 11
          }
        }
      },
      y: {
        grid: {
          color: alpha(theme.palette.divider, 0.1)
        },
        ticks: {
          font: {
            size: 11
          },
          callback: (value: any) => `$${(value / 1000).toFixed(0)}k`
        }
      }
    }
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        {/* Header Section */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h3" fontWeight={800} sx={{ 
            background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            mb: 1
          }}>
            Budget Analytics {year}
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Real-time financial insights powered by AI
          </Typography>
        </Box>

        {/* Metric Cards Grid */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <AnimatedMetricCard
              title="Total Budget"
              value={640000}
              prefix="$"
              icon={<AccountBalance sx={{ color: '#fff' }} />}
              color="primary"
              trend={12.5}
              delay={0}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <AnimatedMetricCard
              title="Spent to Date"
              value={455000}
              prefix="$"
              icon={<Receipt sx={{ color: '#fff' }} />}
              color="success"
              trend={-8.3}
              delay={0.1}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <AnimatedMetricCard
              title="Utilization Rate"
              value={71.1}
              suffix="%"
              icon={<ShowChart sx={{ color: '#fff' }} />}
              color="warning"
              trend={5.2}
              delay={0.2}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <AnimatedMetricCard
              title="Projected Savings"
              value={85000}
              prefix="$"
              icon={<Savings sx={{ color: '#fff' }} />}
              color="info"
              trend={15.7}
              delay={0.3}
            />
          </Grid>
        </Grid>

        {/* Charts Grid */}
        <Grid container spacing={3}>
          {/* Spending Trends */}
          <Grid item xs={12} md={8}>
            <GlassChartCard
              title="Spending Trends"
              subtitle="Monthly budget vs actual spending"
              action={
                <Stack direction="row" spacing={1}>
                  {['monthly', 'quarterly', 'yearly'].map(period => (
                    <Chip
                      key={period}
                      label={period}
                      size="small"
                      onClick={() => setSelectedPeriod(period)}
                      color={selectedPeriod === period ? 'primary' : 'default'}
                      sx={{ textTransform: 'capitalize' }}
                    />
                  ))}
                </Stack>
              }
            >
              <Box sx={{ height: 350 }}>
                <Line data={lineChartData} options={chartOptions} />
              </Box>
            </GlassChartCard>
          </Grid>

          {/* Category Distribution */}
          <Grid item xs={12} md={4}>
            <GlassChartCard
              title="Budget Distribution"
              subtitle="By category"
            >
              <Box sx={{ height: 350, position: 'relative' }}>
                <Doughnut
                  data={doughnutData}
                  options={{
                    ...chartOptions,
                    cutout: '65%',
                    plugins: {
                      ...chartOptions.plugins,
                      legend: {
                        ...chartOptions.plugins.legend,
                        position: 'bottom'
                      }
                    }
                  }}
                />
                <Box sx={{
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                  textAlign: 'center'
                }}>
                  <Typography variant="h4" fontWeight={700}>
                    100%
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    Allocated
                  </Typography>
                </Box>
              </Box>
            </GlassChartCard>
          </Grid>

          {/* Quarterly Performance */}
          <Grid item xs={12} md={6}>
            <GlassChartCard
              title="Quarterly Performance"
              subtitle="Planned vs Actual"
            >
              <Box sx={{ height: 300 }}>
                <Bar data={barChartData} options={chartOptions} />
              </Box>
            </GlassChartCard>
          </Grid>

          {/* Performance Radar */}
          <Grid item xs={12} md={6}>
            <GlassChartCard
              title="Performance Metrics"
              subtitle="Multi-dimensional analysis"
            >
              <Box sx={{ height: 300 }}>
                <Radar
                  data={radarData}
                  options={{
                    ...chartOptions,
                    scales: {
                      r: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                          stepSize: 20
                        },
                        grid: {
                          color: alpha(theme.palette.divider, 0.1)
                        }
                      }
                    }
                  }}
                />
              </Box>
            </GlassChartCard>
          </Grid>
        </Grid>

        {/* Progress Indicators */}
        <Grid container spacing={3} sx={{ mt: 2 }}>
          <Grid item xs={12}>
            <Paper
              sx={{
                p: 3,
                borderRadius: 3,
                background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.secondary.main, 0.05)} 100%)`,
                border: `1px solid ${alpha(theme.palette.divider, 0.1)}`
              }}
            >
              <Typography variant="h6" fontWeight={700} gutterBottom>
                Department Budget Progress
              </Typography>
              <Stack spacing={2}>
                {[
                  { name: 'Engineering', progress: 78, color: 'primary' },
                  { name: 'Marketing', progress: 65, color: 'success' },
                  { name: 'Sales', progress: 82, color: 'warning' },
                  { name: 'HR', progress: 45, color: 'info' }
                ].map((dept, index) => (
                  <motion.div
                    key={dept.name}
                    initial={{ opacity: 0, x: -50 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2" fontWeight={600}>
                        {dept.name}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {dept.progress}%
                      </Typography>
                    </Box>
                    <LinearProgress
                      variant="determinate"
                      value={dept.progress}
                      sx={{
                        height: 8,
                        borderRadius: 4,
                        bgcolor: alpha(theme.palette[dept.color as 'primary' | 'success' | 'warning' | 'info'].main, 0.1),
                        '& .MuiLinearProgress-bar': {
                          borderRadius: 4,
                          background: `linear-gradient(90deg, ${theme.palette[dept.color as 'primary' | 'success' | 'warning' | 'info'].light}, ${theme.palette[dept.color as 'primary' | 'success' | 'warning' | 'info'].main})`
                        }
                      }}
                    />
                  </motion.div>
                ))}
              </Stack>
            </Paper>
          </Grid>
        </Grid>
      </motion.div>
    </Container>
  );
};

export default SoftUIBudgetDashboard;