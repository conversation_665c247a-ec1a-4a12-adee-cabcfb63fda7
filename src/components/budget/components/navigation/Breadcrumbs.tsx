import React from 'react';
import { ChevronRight, Home } from 'lucide-react';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';

export interface BreadcrumbItem {
  label: string;
  href?: string;
  onClick?: () => void;
  icon?: React.ReactNode;
}

interface BreadcrumbsProps {
  items: BreadcrumbItem[];
  className?: string;
  separator?: React.ReactNode;
  showHome?: boolean;
  maxItems?: number;
  variant?: 'default' | 'minimal' | 'pills';
}

export const Breadcrumbs: React.FC<BreadcrumbsProps> = ({
  items,
  className,
  separator = <ChevronRight className="h-4 w-4" />,
  showHome = true,
  maxItems = 0,
  variant = 'default',
}) => {
  const allItems = showHome 
    ? [{ label: 'Home', icon: <Home className="h-4 w-4" /> }, ...items]
    : items;

  let displayItems = allItems;
  
  // Truncate if maxItems is set
  if (maxItems > 0 && allItems.length > maxItems) {
    displayItems = [
      allItems[0],
      { label: '...', onClick: undefined },
      ...allItems.slice(-(maxItems - 2))
    ];
  }

  const variantStyles = {
    default: 'text-sm',
    minimal: 'text-xs',
    pills: 'text-sm',
  };

  const itemStyles = {
    default: 'hover:text-primary transition-colors',
    minimal: 'hover:text-primary transition-colors',
    pills: 'px-3 py-1 rounded-full hover:bg-accent transition-colors',
  };

  return (
    <nav 
      aria-label="Breadcrumb"
      className={cn('flex items-center gap-2', variantStyles[variant], className)}
    >
      <ol className="flex items-center gap-2">
        {displayItems.map((item, index) => {
          const isLast = index === displayItems.length - 1;
          const isClickable = item.onClick || item.href;

          return (
            <motion.li
              key={`${item.label}-${index}`}
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.05 }}
              className="flex items-center gap-2"
            >
              {isClickable && !isLast ? (
                <button
                  onClick={item.onClick}
                  className={cn(
                    'flex items-center gap-1 text-muted-foreground',
                    itemStyles[variant]
                  )}
                  aria-current={isLast ? 'page' : undefined}
                >
                  {item.icon}
                  <span>{item.label}</span>
                </button>
              ) : (
                <span 
                  className={cn(
                    'flex items-center gap-1',
                    isLast ? 'text-foreground font-medium' : 'text-muted-foreground',
                    variant === 'pills' && isLast && 'px-3 py-1 bg-primary/10 text-primary rounded-full'
                  )}
                  aria-current={isLast ? 'page' : undefined}
                >
                  {item.icon}
                  <span>{item.label}</span>
                </span>
              )}
              
              {!isLast && (
                <span className="text-muted-foreground/50" aria-hidden="true">
                  {separator}
                </span>
              )}
            </motion.li>
          );
        })}
      </ol>
    </nav>
  );
};

// Context-aware breadcrumb hook
export const useBreadcrumbs = (currentPath: string): BreadcrumbItem[] => {
  const pathSegments = currentPath.split('/').filter(Boolean);
  
  return pathSegments.map((segment, index) => {
    const path = '/' + pathSegments.slice(0, index + 1).join('/');
    const label = segment
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
    
    return {
      label,
      href: path,
      onClick: () => {
        // Handle navigation
        console.log('Navigate to:', path);
      }
    };
  });
};

// Mobile breadcrumb with dropdown for overflow
interface MobileBreadcrumbsProps {
  items: BreadcrumbItem[];
  className?: string;
}

export const MobileBreadcrumbs: React.FC<MobileBreadcrumbsProps> = ({
  items,
  className,
}) => {
  if (items.length <= 2) {
    return <Breadcrumbs items={items} className={className} variant="minimal" />;
  }

  const firstItem = items[0];
  const lastItem = items[items.length - 1];
  const middleItems = items.slice(1, -1);

  return (
    <nav 
      aria-label="Breadcrumb"
      className={cn('flex items-center gap-2 text-sm', className)}
    >
      <ol className="flex items-center gap-2">
        <li>
          <button
            onClick={firstItem.onClick}
            className="text-muted-foreground hover:text-primary transition-colors"
          >
            {firstItem.icon || <Home className="h-4 w-4" />}
          </button>
        </li>
        
        <ChevronRight className="h-4 w-4 text-muted-foreground/50" />
        
        {middleItems.length > 0 && (
          <>
            <li>
              <button className="text-muted-foreground">...</button>
            </li>
            <ChevronRight className="h-4 w-4 text-muted-foreground/50" />
          </>
        )}
        
        <li>
          <span className="font-medium">{lastItem.label}</span>
        </li>
      </ol>
    </nav>
  );
};