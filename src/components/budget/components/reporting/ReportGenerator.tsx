import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Calendar } from '@/components/ui/calendar';
import { Root as PopoverRoot, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { 
  FileText, 
  Download, 
  Calendar as CalendarIcon, 
  Clock,
  CheckCircle,
  AlertCircle,
  TrendingUp,
  Users,
  PieChart,
  Activity,
  Mail
} from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { useBudget, useBudgetStats, useBudgetAnalytics } from '@/hooks/queries/useBudgetQueries';

export interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  type: 'executive' | 'department' | 'category' | 'trends' | 'custom';
  icon: React.ReactNode;
  sections: string[];
  frequency?: 'monthly' | 'quarterly' | 'yearly' | 'custom';
  lastGenerated?: string;
  format: 'pdf' | 'csv' | 'both';
}

export interface GeneratedReport {
  id: string;
  templateId: string;
  name: string;
  generatedDate: string;
  period: {
    start: string;
    end: string;
  };
  status: 'generating' | 'completed' | 'failed';
  downloadUrl?: string;
  size?: string;
  format: 'pdf' | 'csv';
}

interface ReportGeneratorProps {
  templates: ReportTemplate[];
  generatedReports: GeneratedReport[];
  onGenerateReport: (template: ReportTemplate, options: any) => Promise<GeneratedReport>;
  onScheduleReport: (template: ReportTemplate, schedule: any) => void;
  onDownloadReport: (reportId: string) => void;
  showToast?: (message: string, type: 'success' | 'error') => void;
  loading?: boolean;
}

export const ReportGenerator: React.FC<ReportGeneratorProps> = ({
  templates,
  generatedReports,
  onGenerateReport,
  onScheduleReport,
  onDownloadReport,
  showToast = () => {},
  loading = false
}) => {
  const [selectedTemplate, setSelectedTemplate] = useState<ReportTemplate | null>(null);
  const [showGenerateDialog, setShowGenerateDialog] = useState(false);
  const [showScheduleDialog, setShowScheduleDialog] = useState(false);
  const [generating, setGenerating] = useState(false);
  
  const [reportOptions, setReportOptions] = useState({
    period: 'last-month',
    customStart: new Date(),
    customEnd: new Date(),
    includeSummary: true,
    includeCharts: true,
    includeRecommendations: true,
    includeForecast: false,
    format: 'pdf' as 'pdf' | 'csv' | 'both'
  });

  const [scheduleOptions, setScheduleOptions] = useState({
    frequency: 'monthly',
    dayOfWeek: 1,
    dayOfMonth: 1,
    time: '09:00',
    recipients: [] as string[],
    enabled: true
  });

  const currentYear = new Date().getFullYear();
  const { data: budget } = useBudget(currentYear);
  const { data: stats } = useBudgetStats(currentYear);
  const { data: analytics } = useBudgetAnalytics(currentYear);

  const handleGenerateReport = async () => {
    if (!selectedTemplate) return;
    
    setGenerating(true);
    try {
      const report = await onGenerateReport(selectedTemplate, reportOptions);
      showToast(`Report "${report.name}" generated successfully`, 'success');
      setShowGenerateDialog(false);
    } catch (error) {
      showToast('Failed to generate report', 'error');
    } finally {
      setGenerating(false);
    }
  };

  const handleScheduleReport = () => {
    if (!selectedTemplate) return;
    
    onScheduleReport(selectedTemplate, scheduleOptions);
    showToast('Report scheduled successfully', 'success');
    setShowScheduleDialog(false);
  };

  const getStatusIcon = (status: GeneratedReport['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'generating':
        return <Clock className="h-4 w-4 text-blue-500 animate-spin" />;
      case 'failed':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <Card className="p-6">
        <Skeleton className="h-6 w-48 mb-4" />
        <Skeleton className="h-64 w-full" />
      </Card>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
    >
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <FileText className="h-5 w-5 text-muted-foreground" />
            <h3 className="text-lg font-semibold">Report Generator</h3>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowScheduleDialog(true)}
          >
            <Clock className="h-4 w-4 mr-2" />
            Schedule Reports
          </Button>
        </div>

        {/* Report Templates */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          {templates.map((template) => (
            <Card
              key={template.id}
              className="p-4 cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => {
                setSelectedTemplate(template);
                setShowGenerateDialog(true);
              }}
            >
              <div className="flex items-start gap-3">
                <div className="p-2 bg-muted rounded-lg">
                  {template.icon}
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-1">
                    <h4 className="font-medium">{template.name}</h4>
                    <Badge variant="secondary" className="text-xs">
                      {template.type}
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground mb-2">
                    {template.description}
                  </p>
                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                    <CalendarIcon className="h-3 w-3" />
                    <span>{template.frequency}</span>
                    <span className="mx-1">•</span>
                    <span>{template.format.toUpperCase()}</span>
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>

        {/* Recent Reports */}
        <div>
          <h4 className="font-medium mb-3">Recent Reports</h4>
          {generatedReports.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <FileText className="h-12 w-12 mx-auto mb-3 opacity-50" />
              <p>No reports generated yet</p>
              <p className="text-sm mt-1">Click on a template above to generate your first report</p>
            </div>
          ) : (
            <div className="space-y-2">
              {generatedReports.map((report) => (
                <Card key={report.id} className="p-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {getStatusIcon(report.status)}
                      <div>
                        <p className="font-medium text-sm">{report.name}</p>
                        <p className="text-xs text-muted-foreground">
                          Generated {new Date(report.generatedDate).toLocaleDateString()}
                          {report.size && ` • ${report.size}`}
                        </p>
                      </div>
                    </div>
                    {report.status === 'completed' && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onDownloadReport(report.id)}
                      >
                        <Download className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </Card>
              ))}
            </div>
          )}
        </div>
      </Card>

      {/* Generate Report Dialog */}
      <Dialog open={showGenerateDialog} onOpenChange={setShowGenerateDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Generate {selectedTemplate?.name}</DialogTitle>
            <DialogDescription>
              Configure report parameters and generate your report
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            {/* Period Selection */}
            <div className="space-y-2">
              <Label>Report Period</Label>
              <Select
                value={reportOptions.period}
                onValueChange={(value) => setReportOptions({ ...reportOptions, period: value })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="last-month">Last Month</SelectItem>
                  <SelectItem value="last-quarter">Last Quarter</SelectItem>
                  <SelectItem value="year-to-date">Year to Date</SelectItem>
                  <SelectItem value="last-year">Last Year</SelectItem>
                  <SelectItem value="custom">Custom Period</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Custom Date Range */}
            {reportOptions.period === 'custom' && (
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Start Date</Label>
                  <PopoverRoot>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          'w-full justify-start text-left font-normal',
                          !reportOptions.customStart && 'text-muted-foreground'
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {reportOptions.customStart ? (
                          format(reportOptions.customStart, 'PPP')
                        ) : (
                          <span>Pick a date</span>
                        )}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={reportOptions.customStart}
                        onSelect={(date) => date && setReportOptions({ ...reportOptions, customStart: date })}
                        initialFocus
                      />
                    </PopoverContent>
                  </PopoverRoot>
                </div>
                <div className="space-y-2">
                  <Label>End Date</Label>
                  <PopoverRoot>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          'w-full justify-start text-left font-normal',
                          !reportOptions.customEnd && 'text-muted-foreground'
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {reportOptions.customEnd ? (
                          format(reportOptions.customEnd, 'PPP')
                        ) : (
                          <span>Pick a date</span>
                        )}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={reportOptions.customEnd}
                        onSelect={(date) => date && setReportOptions({ ...reportOptions, customEnd: date })}
                        initialFocus
                      />
                    </PopoverContent>
                  </PopoverRoot>
                </div>
              </div>
            )}

            {/* Report Options */}
            <div className="space-y-3">
              <Label>Include in Report</Label>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="summary"
                    checked={reportOptions.includeSummary}
                    onCheckedChange={(checked: boolean) =>
                      setReportOptions({ ...reportOptions, includeSummary: checked })
                    }
                  />
                  <Label htmlFor="summary" className="text-sm font-normal cursor-pointer">
                    Executive Summary
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="charts"
                    checked={reportOptions.includeCharts}
                    onCheckedChange={(checked: boolean) =>
                      setReportOptions({ ...reportOptions, includeCharts: checked })
                    }
                  />
                  <Label htmlFor="charts" className="text-sm font-normal cursor-pointer">
                    Charts and Visualizations
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="recommendations"
                    checked={reportOptions.includeRecommendations}
                    onCheckedChange={(checked: boolean) =>
                      setReportOptions({ ...reportOptions, includeRecommendations: checked })
                    }
                  />
                  <Label htmlFor="recommendations" className="text-sm font-normal cursor-pointer">
                    Recommendations
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="forecast"
                    checked={reportOptions.includeForecast}
                    onCheckedChange={(checked: boolean) =>
                      setReportOptions({ ...reportOptions, includeForecast: checked })
                    }
                  />
                  <Label htmlFor="forecast" className="text-sm font-normal cursor-pointer">
                    Future Projections
                  </Label>
                </div>
              </div>
            </div>

            {/* Format Selection */}
            <div className="space-y-2">
              <Label>Report Format</Label>
              <Select
                value={reportOptions.format}
                onValueChange={(value: any) => setReportOptions({ ...reportOptions, format: value })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pdf">PDF Document</SelectItem>
                  <SelectItem value="csv">CSV Spreadsheet</SelectItem>
                  <SelectItem value="both">Both PDF and CSV</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowGenerateDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleGenerateReport} disabled={generating}>
              {generating ? (
                <>
                  <Clock className="h-4 w-4 mr-2 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <FileText className="h-4 w-4 mr-2" />
                  Generate Report
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Schedule Report Dialog */}
      <Dialog open={showScheduleDialog} onOpenChange={setShowScheduleDialog}>
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle>Schedule Automated Reports</DialogTitle>
            <DialogDescription>
              Set up automated report generation and delivery
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            {/* Template Selection */}
            <div className="space-y-2">
              <Label>Report Template</Label>
              <Select
                value={selectedTemplate?.id}
                onValueChange={(value) => {
                  const template = templates.find(t => t.id === value);
                  setSelectedTemplate(template || null);
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a template" />
                </SelectTrigger>
                <SelectContent>
                  {templates.map((template) => (
                    <SelectItem key={template.id} value={template.id}>
                      {template.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Frequency */}
            <div className="space-y-2">
              <Label>Frequency</Label>
              <Select
                value={scheduleOptions.frequency}
                onValueChange={(value) => setScheduleOptions({ ...scheduleOptions, frequency: value })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="weekly">Weekly</SelectItem>
                  <SelectItem value="monthly">Monthly</SelectItem>
                  <SelectItem value="quarterly">Quarterly</SelectItem>
                  <SelectItem value="yearly">Yearly</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Time */}
            <div className="space-y-2">
              <Label>Delivery Time</Label>
              <input
                type="time"
                value={scheduleOptions.time}
                onChange={(e) => setScheduleOptions({ ...scheduleOptions, time: e.target.value })}
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
              />
            </div>

            {/* Email Recipients */}
            <div className="space-y-2">
              <Label>Email Recipients</Label>
              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <p className="text-sm text-muted-foreground">
                  Reports will be sent to your registered email
                </p>
              </div>
            </div>

            {/* Enable/Disable */}
            <div className="flex items-center space-x-2">
              <Checkbox
                id="enabled"
                checked={scheduleOptions.enabled}
                onCheckedChange={(checked: boolean) =>
                  setScheduleOptions({ ...scheduleOptions, enabled: checked })
                }
              />
              <Label htmlFor="enabled" className="text-sm font-normal cursor-pointer">
                Enable automated report generation
              </Label>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowScheduleDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleScheduleReport}>
              Save Schedule
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </motion.div>
  );
};