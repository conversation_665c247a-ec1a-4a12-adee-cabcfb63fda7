import React, { useState } from 'react';
import { motion, Reorder } from 'framer-motion';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  Layout, 
  Plus, 
  Trash2, 
  GripVertical,
  Eye,
  Save,
  MoreVertical,
  BarChart3,
  Pie<PERSON>hart,
  LineChart,
  Table2,
  FileText,
  TrendingUp,
  Users,
  DollarSign,
  Filter,
  Settings2
} from 'lucide-react';
import { cn } from '@/lib/utils';

export interface ReportSection {
  id: string;
  type: 'summary' | 'chart' | 'table' | 'metric' | 'text';
  title: string;
  config: any;
  width: 'full' | 'half' | 'third';
  order: number;
}

export interface CustomReport {
  id: string;
  name: string;
  description: string;
  sections: ReportSection[];
  filters: {
    dateRange: 'custom' | 'month' | 'quarter' | 'year';
    departments: string[];
    categories: string[];
  };
  layout: 'single' | 'two-column' | 'dashboard';
  created: string;
  updated: string;
}

interface CustomReportBuilderProps {
  reports: CustomReport[];
  onSaveReport: (report: Omit<CustomReport, 'id' | 'created' | 'updated'>) => void;
  onPreviewReport: (report: Partial<CustomReport>) => void;
  existingReport?: CustomReport;
  availableMetrics?: Array<{ id: string; name: string; type: string }>;
  showToast?: (message: string, type: 'success' | 'error') => void;
  loading?: boolean;
}

const sectionTypes = [
  { id: 'summary', label: 'Summary', icon: <FileText className="h-4 w-4" /> },
  { id: 'chart', label: 'Chart', icon: <BarChart3 className="h-4 w-4" /> },
  { id: 'table', label: 'Table', icon: <Table2 className="h-4 w-4" /> },
  { id: 'metric', label: 'Metric', icon: <TrendingUp className="h-4 w-4" /> },
  { id: 'text', label: 'Text', icon: <FileText className="h-4 w-4" /> },
];

const chartTypes = [
  { id: 'bar', label: 'Bar Chart', icon: <BarChart3 className="h-4 w-4" /> },
  { id: 'line', label: 'Line Chart', icon: <LineChart className="h-4 w-4" /> },
  { id: 'pie', label: 'Pie Chart', icon: <PieChart className="h-4 w-4" /> },
  { id: 'area', label: 'Area Chart', icon: <BarChart3 className="h-4 w-4" /> },
];

const metricTypes = [
  { id: 'total-budget', label: 'Total Budget', icon: <DollarSign className="h-4 w-4" /> },
  { id: 'total-spent', label: 'Total Spent', icon: <DollarSign className="h-4 w-4" /> },
  { id: 'utilization', label: 'Budget Utilization', icon: <TrendingUp className="h-4 w-4" /> },
  { id: 'department-count', label: 'Active Departments', icon: <Users className="h-4 w-4" /> },
  { id: 'expense-count', label: 'Total Expenses', icon: <FileText className="h-4 w-4" /> },
  { id: 'avg-expense', label: 'Average Expense', icon: <DollarSign className="h-4 w-4" /> },
];

export const CustomReportBuilder: React.FC<CustomReportBuilderProps> = ({
  reports,
  onSaveReport,
  onPreviewReport,
  existingReport,
  availableMetrics = [],
  showToast = () => {},
  loading = false
}) => {
  const [activeTab, setActiveTab] = useState('design');
  const [editingSection, setEditingSection] = useState<ReportSection | null>(null);
  const [showSectionDialog, setShowSectionDialog] = useState(false);
  
  const [report, setReport] = useState<Partial<CustomReport>>({
    name: existingReport?.name || '',
    description: existingReport?.description || '',
    sections: existingReport?.sections || [],
    filters: existingReport?.filters || {
      dateRange: 'month',
      departments: [],
      categories: []
    },
    layout: existingReport?.layout || 'single'
  });

  const [newSection, setNewSection] = useState<Partial<ReportSection>>({
    type: 'summary',
    title: '',
    width: 'full',
    config: {}
  });

  const handleAddSection = () => {
    const section: ReportSection = {
      id: Date.now().toString(),
      type: newSection.type as ReportSection['type'],
      title: newSection.title || `New ${newSection.type} section`,
      config: newSection.config || {},
      width: newSection.width as ReportSection['width'],
      order: report.sections?.length || 0
    };

    setReport({
      ...report,
      sections: [...(report.sections || []), section]
    });

    setNewSection({
      type: 'summary',
      title: '',
      width: 'full',
      config: {}
    });
    setShowSectionDialog(false);
    showToast('Section added successfully', 'success');
  };

  // const handleUpdateSection = (sectionId: string, updates: Partial<ReportSection>) => {
  //   setReport({
  //     ...report,
  //     sections: report.sections?.map(section =>
  //       section.id === sectionId ? { ...section, ...updates } : section
  //     ) || []
  //   });
  // };

  const handleRemoveSection = (sectionId: string) => {
    setReport({
      ...report,
      sections: report.sections?.filter(section => section.id !== sectionId) || []
    });
    showToast('Section removed', 'success');
  };

  const handleReorderSections = (newOrder: ReportSection[]) => {
    setReport({
      ...report,
      sections: newOrder.map((section, index) => ({
        ...section,
        order: index
      }))
    });
  };

  const handleSaveReport = () => {
    if (!report.name) {
      showToast('Please enter a report name', 'error');
      return;
    }

    onSaveReport({
      name: report.name,
      description: report.description || '',
      sections: report.sections || [],
      filters: report.filters!,
      layout: report.layout!
    });
    showToast('Report saved successfully', 'success');
  };

  const handlePreview = () => {
    onPreviewReport(report);
    // In a real app, this would open a preview dialog
  };

  const getSectionIcon = (type: string) => {
    switch (type) {
      case 'chart': return <BarChart3 className="h-4 w-4" />;
      case 'table': return <Table2 className="h-4 w-4" />;
      case 'metric': return <TrendingUp className="h-4 w-4" />;
      case 'text': return <FileText className="h-4 w-4" />;
      default: return <FileText className="h-4 w-4" />;
    }
  };

  if (loading) {
    return (
      <Card className="p-6">
        <Skeleton className="h-6 w-48 mb-4" />
        <Skeleton className="h-96 w-full" />
      </Card>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
    >
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <Layout className="h-5 w-5 text-muted-foreground" />
            <h3 className="text-lg font-semibold">Custom Report Builder</h3>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handlePreview}
              disabled={!report.sections?.length}
            >
              <Eye className="h-4 w-4 mr-2" />
              Preview
            </Button>
            <Button
              size="sm"
              onClick={handleSaveReport}
              disabled={!report.name || !report.sections?.length}
            >
              <Save className="h-4 w-4 mr-2" />
              Save Report
            </Button>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="design">Design</TabsTrigger>
            <TabsTrigger value="filters">Filters</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="design" className="space-y-4">
            {/* Report Details */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Report Name</Label>
                <Input
                  id="name"
                  value={report.name}
                  onChange={(e) => setReport({ ...report, name: e.target.value })}
                  placeholder="Monthly Performance Report"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="layout">Layout</Label>
                <Select
                  value={report.layout}
                  onValueChange={(value: any) => setReport({ ...report, layout: value })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="single">Single Column</SelectItem>
                    <SelectItem value="two-column">Two Column</SelectItem>
                    <SelectItem value="dashboard">Dashboard Grid</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={report.description}
                onChange={(e) => setReport({ ...report, description: e.target.value })}
                placeholder="Describe the purpose of this report..."
                rows={2}
              />
            </div>

            {/* Section Builder */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="font-medium">Report Sections</h4>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowSectionDialog(true)}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Section
                </Button>
              </div>

              {report.sections?.length === 0 ? (
                <Card className="p-8 text-center border-dashed">
                  <Layout className="h-12 w-12 mx-auto mb-3 text-muted-foreground opacity-50" />
                  <p className="text-muted-foreground">No sections added yet</p>
                  <p className="text-sm text-muted-foreground mt-1">
                    Click "Add Section" to start building your report
                  </p>
                </Card>
              ) : (
                <Reorder.Group
                  axis="y"
                  values={report.sections || []}
                  onReorder={handleReorderSections}
                  className="space-y-2"
                >
                  {report.sections?.map((section) => (
                    <Reorder.Item key={section.id} value={section}>
                      <Card className="p-4">
                        <div className="flex items-start gap-3">
                          <div className="cursor-move mt-1">
                            <GripVertical className="h-4 w-4 text-muted-foreground" />
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center justify-between mb-2">
                              <div className="flex items-center gap-2">
                                {getSectionIcon(section.type)}
                                <h5 className="font-medium">{section.title}</h5>
                                <Badge variant="secondary" className="text-xs">
                                  {section.type}
                                </Badge>
                                <Badge variant="outline" className="text-xs">
                                  {section.width}
                                </Badge>
                              </div>
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="icon">
                                    <MoreVertical className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuItem
                                    onClick={() => {
                                      setEditingSection(section);
                                      setShowSectionDialog(true);
                                    }}
                                  >
                                    <Settings2 className="h-4 w-4 mr-2" />
                                    Configure
                                  </DropdownMenuItem>
                                  <DropdownMenuItem
                                    className="text-destructive"
                                    onClick={() => handleRemoveSection(section.id)}
                                  >
                                    <Trash2 className="h-4 w-4 mr-2" />
                                    Remove
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                            {section.type === 'chart' && section.config.chartType && (
                              <p className="text-sm text-muted-foreground">
                                {chartTypes.find(t => t.id === section.config.chartType)?.label}
                              </p>
                            )}
                            {section.type === 'metric' && section.config.metricType && (
                              <p className="text-sm text-muted-foreground">
                                {metricTypes.find(t => t.id === section.config.metricType)?.label}
                              </p>
                            )}
                          </div>
                        </div>
                      </Card>
                    </Reorder.Item>
                  ))}
                </Reorder.Group>
              )}
            </div>
          </TabsContent>

          <TabsContent value="filters" className="space-y-4">
            <Card className="p-4">
              <h4 className="font-medium mb-4 flex items-center gap-2">
                <Filter className="h-4 w-4" />
                Report Filters
              </h4>
              
              <div className="space-y-4">
                {/* Date Range Filter */}
                <div className="space-y-2">
                  <Label>Date Range</Label>
                  <Select
                    value={report.filters?.dateRange}
                    onValueChange={(value: any) => 
                      setReport({ 
                        ...report, 
                        filters: { ...report.filters!, dateRange: value }
                      })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="month">Current Month</SelectItem>
                      <SelectItem value="quarter">Current Quarter</SelectItem>
                      <SelectItem value="year">Current Year</SelectItem>
                      <SelectItem value="custom">Custom Range</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Department Filter */}
                <div className="space-y-2">
                  <Label>Departments</Label>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="all-departments" />
                    <Label htmlFor="all-departments" className="text-sm font-normal">
                      Include all departments
                    </Label>
                  </div>
                </div>

                {/* Category Filter */}
                <div className="space-y-2">
                  <Label>Categories</Label>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="all-categories" />
                    <Label htmlFor="all-categories" className="text-sm font-normal">
                      Include all categories
                    </Label>
                  </div>
                </div>
              </div>
            </Card>
          </TabsContent>

          <TabsContent value="settings" className="space-y-4">
            <Card className="p-4">
              <h4 className="font-medium mb-4 flex items-center gap-2">
                <Settings2 className="h-4 w-4" />
                Report Settings
              </h4>
              
              <div className="space-y-4">
                {/* Export Options */}
                <div className="space-y-2">
                  <Label>Export Format</Label>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox id="pdf" defaultChecked />
                      <Label htmlFor="pdf" className="text-sm font-normal">
                        PDF Document
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="csv" />
                      <Label htmlFor="csv" className="text-sm font-normal">
                        CSV Spreadsheet
                      </Label>
                    </div>
                  </div>
                </div>

                {/* Branding Options */}
                <div className="space-y-2">
                  <Label>Branding</Label>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox id="logo" defaultChecked />
                      <Label htmlFor="logo" className="text-sm font-normal">
                        Include company logo
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="footer" defaultChecked />
                      <Label htmlFor="footer" className="text-sm font-normal">
                        Include footer with generation date
                      </Label>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </TabsContent>
        </Tabs>
      </Card>

      {/* Add/Edit Section Dialog */}
      <Dialog open={showSectionDialog} onOpenChange={setShowSectionDialog}>
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle>
              {editingSection ? 'Edit Section' : 'Add Section'}
            </DialogTitle>
            <DialogDescription>
              Configure the section properties and content
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            {/* Section Type */}
            <div className="space-y-2">
              <Label>Section Type</Label>
              <div className="grid grid-cols-2 gap-2">
                {sectionTypes.map((type) => (
                  <Card
                    key={type.id}
                    className={cn(
                      "p-3 cursor-pointer hover:shadow-md transition-shadow",
                      newSection.type === type.id && "ring-2 ring-primary"
                    )}
                    onClick={() => setNewSection({ ...newSection, type: type.id as any })}
                  >
                    <div className="flex items-center gap-2">
                      {type.icon}
                      <span className="text-sm font-medium">{type.label}</span>
                    </div>
                  </Card>
                ))}
              </div>
            </div>

            {/* Section Title */}
            <div className="space-y-2">
              <Label>Title</Label>
              <Input
                value={newSection.title}
                onChange={(e) => setNewSection({ ...newSection, title: e.target.value })}
                placeholder="Enter section title"
              />
            </div>

            {/* Section Width */}
            <div className="space-y-2">
              <Label>Width</Label>
              <Select
                value={newSection.width}
                onValueChange={(value: any) => setNewSection({ ...newSection, width: value })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="full">Full Width</SelectItem>
                  <SelectItem value="half">Half Width</SelectItem>
                  <SelectItem value="third">One Third</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Type-specific configuration */}
            {newSection.type === 'chart' && (
              <div className="space-y-2">
                <Label>Chart Type</Label>
                <Select
                  value={newSection.config?.chartType}
                  onValueChange={(value) => 
                    setNewSection({ 
                      ...newSection, 
                      config: { ...newSection.config, chartType: value }
                    })
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select chart type" />
                  </SelectTrigger>
                  <SelectContent>
                    {chartTypes.map((chart) => (
                      <SelectItem key={chart.id} value={chart.id}>
                        <div className="flex items-center gap-2">
                          {chart.icon}
                          <span>{chart.label}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {newSection.type === 'metric' && (
              <div className="space-y-2">
                <Label>Metric</Label>
                <Select
                  value={newSection.config?.metricType}
                  onValueChange={(value) => 
                    setNewSection({ 
                      ...newSection, 
                      config: { ...newSection.config, metricType: value }
                    })
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select metric" />
                  </SelectTrigger>
                  <SelectContent>
                    {metricTypes.map((metric) => (
                      <SelectItem key={metric.id} value={metric.id}>
                        <div className="flex items-center gap-2">
                          {metric.icon}
                          <span>{metric.label}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {newSection.type === 'text' && (
              <div className="space-y-2">
                <Label>Content</Label>
                <Textarea
                  value={newSection.config?.content || ''}
                  onChange={(e) => 
                    setNewSection({ 
                      ...newSection, 
                      config: { ...newSection.config, content: e.target.value }
                    })
                  }
                  placeholder="Enter text content..."
                  rows={4}
                />
              </div>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowSectionDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleAddSection}>
              {editingSection ? 'Update Section' : 'Add Section'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </motion.div>
  );
};