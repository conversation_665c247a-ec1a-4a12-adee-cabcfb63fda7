import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { 
  Bell, 
  Mail, 
  AlertTriangle,
  TrendingUp,
  Calendar,
  DollarSign,
  Settings,
  Plus,
  Trash2,
  Edit2,
  CheckCircle,
  XCircle,
  Clock,
  Zap
} from 'lucide-react';
import { formatCurrency } from '../../utils/formatters';

export interface NotificationRule {
  id: string;
  name: string;
  type: 'threshold' | 'milestone' | 'report' | 'approval' | 'custom';
  enabled: boolean;
  trigger: {
    condition: string;
    value: number | string;
    operator: 'greater' | 'less' | 'equals' | 'between';
  };
  action: {
    email: boolean;
    inApp: boolean;
    frequency: 'immediate' | 'daily' | 'weekly';
  };
  recipients: string[];
  lastTriggered?: string;
  triggerCount: number;
}

interface EmailNotificationsProps {
  notificationRules: NotificationRule[];
  onAddRule: (rule: Omit<NotificationRule, 'id' | 'lastTriggered' | 'triggerCount'>) => void;
  onUpdateRule: (id: string, updates: Partial<NotificationRule>) => void;
  onDeleteRule: (id: string) => void;
  onTestRule: (id: string) => void;
  showToast?: (message: string, type: 'success' | 'error') => void;
  loading?: boolean;
}

const notificationTypes = [
  { 
    id: 'threshold', 
    label: 'Budget Threshold', 
    icon: <AlertTriangle className="h-4 w-4" />,
    description: 'Alert when budget usage reaches a certain percentage'
  },
  { 
    id: 'milestone', 
    label: 'Milestone', 
    icon: <TrendingUp className="h-4 w-4" />,
    description: 'Notify when specific goals are achieved'
  },
  { 
    id: 'report', 
    label: 'Report Ready', 
    icon: <Calendar className="h-4 w-4" />,
    description: 'Alert when scheduled reports are generated'
  },
  { 
    id: 'approval', 
    label: 'Approval Request', 
    icon: <CheckCircle className="h-4 w-4" />,
    description: 'Notify when expenses need approval'
  },
];

const defaultRules: Partial<NotificationRule>[] = [
  {
    name: '80% Budget Alert',
    type: 'threshold',
    enabled: true,
    trigger: {
      condition: 'budget_percentage',
      value: 80,
      operator: 'greater'
    },
    action: {
      email: true,
      inApp: true,
      frequency: 'immediate'
    }
  },
  {
    name: '90% Budget Warning',
    type: 'threshold',
    enabled: true,
    trigger: {
      condition: 'budget_percentage',
      value: 90,
      operator: 'greater'
    },
    action: {
      email: true,
      inApp: true,
      frequency: 'immediate'
    }
  },
  {
    name: 'Monthly Report',
    type: 'report',
    enabled: true,
    trigger: {
      condition: 'report_generated',
      value: 'monthly',
      operator: 'equals'
    },
    action: {
      email: true,
      inApp: false,
      frequency: 'immediate'
    }
  }
];

export const EmailNotifications: React.FC<EmailNotificationsProps> = ({
  notificationRules,
  onAddRule,
  onUpdateRule,
  onDeleteRule,
  onTestRule,
  showToast = () => {},
  loading = false
}) => {
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [editingRule, setEditingRule] = useState<NotificationRule | null>(null);
  const [showTestDialog, setShowTestDialog] = useState(false);
  const [testingRuleId, setTestingRuleId] = useState<string | null>(null);
  
  const [formData, setFormData] = useState<Partial<NotificationRule>>({
    name: '',
    type: 'threshold',
    enabled: true,
    trigger: {
      condition: 'budget_percentage',
      value: 80,
      operator: 'greater'
    },
    action: {
      email: true,
      inApp: true,
      frequency: 'immediate'
    },
    recipients: []
  });

  const handleAddRule = () => {
    if (!formData.name) {
      showToast('Please enter a rule name', 'error');
      return;
    }

    onAddRule({
      name: formData.name,
      type: formData.type as NotificationRule['type'],
      enabled: formData.enabled!,
      trigger: formData.trigger!,
      action: formData.action!,
      recipients: formData.recipients || []
    });

    setFormData({
      name: '',
      type: 'threshold',
      enabled: true,
      trigger: {
        condition: 'budget_percentage',
        value: 80,
        operator: 'greater'
      },
      action: {
        email: true,
        inApp: true,
        frequency: 'immediate'
      },
      recipients: []
    });
    setShowAddDialog(false);
    showToast('Notification rule added successfully', 'success');
  };

  const handleUpdateRule = () => {
    if (!editingRule) return;

    onUpdateRule(editingRule.id, {
      name: formData.name,
      type: formData.type,
      enabled: formData.enabled,
      trigger: formData.trigger,
      action: formData.action,
      recipients: formData.recipients
    });

    setEditingRule(null);
    setShowAddDialog(false);
    showToast('Notification rule updated successfully', 'success');
  };

  const handleTestRule = (ruleId: string) => {
    setTestingRuleId(ruleId);
    setShowTestDialog(true);
    onTestRule(ruleId);
    
    // Simulate test completion
    setTimeout(() => {
      setShowTestDialog(false);
      showToast('Test notification sent successfully', 'success');
    }, 2000);
  };

  const getTypeIcon = (type: string) => {
    const typeConfig = notificationTypes.find(t => t.id === type);
    return typeConfig?.icon || <Bell className="h-4 w-4" />;
  };

  const getTriggerDescription = (rule: NotificationRule) => {
    const { condition, value, operator } = rule.trigger;
    
    switch (condition) {
      case 'budget_percentage':
        return `Budget usage ${operator} ${value}%`;
      case 'expense_amount':
        return `Expense amount ${operator} ${formatCurrency(value as number)}`;
      case 'report_generated':
        return `${value} report generated`;
      case 'approval_pending':
        return `${value} approvals pending`;
      default:
        return `${condition} ${operator} ${value}`;
    }
  };

  if (loading) {
    return (
      <Card className="p-6">
        <Skeleton className="h-6 w-48 mb-4" />
        <Skeleton className="h-32 w-full" />
      </Card>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
    >
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <Bell className="h-5 w-5 text-muted-foreground" />
            <h3 className="text-lg font-semibold">Email Notifications</h3>
            <Badge variant="secondary">{notificationRules.length} rules</Badge>
          </div>
          <Button
            size="sm"
            onClick={() => {
              setEditingRule(null);
              setFormData({
                name: '',
                type: 'threshold',
                enabled: true,
                trigger: {
                  condition: 'budget_percentage',
                  value: 80,
                  operator: 'greater'
                },
                action: {
                  email: true,
                  inApp: true,
                  frequency: 'immediate'
                },
                recipients: []
              });
              setShowAddDialog(true);
            }}
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Rule
          </Button>
        </div>

        {/* Quick Setup */}
        {notificationRules.length === 0 && (
          <Card className="p-6 mb-6 bg-muted/50">
            <h4 className="font-medium mb-3">Quick Setup</h4>
            <p className="text-sm text-muted-foreground mb-4">
              Get started with recommended notification rules
            </p>
            <div className="flex flex-wrap gap-2">
              {defaultRules.map((rule, index) => (
                <Button
                  key={index}
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    onAddRule({
                      name: rule.name!,
                      type: rule.type as NotificationRule['type'],
                      enabled: rule.enabled!,
                      trigger: rule.trigger!,
                      action: rule.action!,
                      recipients: []
                    });
                  }}
                >
                  <Zap className="h-3 w-3 mr-1" />
                  {rule.name}
                </Button>
              ))}
            </div>
          </Card>
        )}

        {/* Notification Rules List */}
        <div className="space-y-3">
          {notificationRules.map((rule) => (
            <Card key={rule.id} className="p-4">
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-3">
                  <div className={`p-2 rounded-lg ${rule.enabled ? 'bg-muted' : 'bg-muted/50'}`}>
                    {getTypeIcon(rule.type)}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-medium">{rule.name}</h4>
                      <Badge variant={rule.enabled ? 'default' : 'secondary'} className="text-xs">
                        {rule.enabled ? 'Active' : 'Inactive'}
                      </Badge>
                      {rule.lastTriggered && (
                        <span className="text-xs text-muted-foreground">
                          Last triggered {new Date(rule.lastTriggered).toLocaleDateString()}
                        </span>
                      )}
                    </div>
                    <p className="text-sm text-muted-foreground mb-2">
                      {getTriggerDescription(rule)}
                    </p>
                    <div className="flex items-center gap-4 text-xs text-muted-foreground">
                      {rule.action.email && (
                        <span className="flex items-center gap-1">
                          <Mail className="h-3 w-3" />
                          Email
                        </span>
                      )}
                      {rule.action.inApp && (
                        <span className="flex items-center gap-1">
                          <Bell className="h-3 w-3" />
                          In-app
                        </span>
                      )}
                      <span className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        {rule.action.frequency}
                      </span>
                      <span>
                        Triggered {rule.triggerCount} times
                      </span>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <Switch
                    checked={rule.enabled}
                    onCheckedChange={(checked) => onUpdateRule(rule.id, { enabled: checked })}
                  />
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleTestRule(rule.id)}
                  >
                    Test
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => {
                      setEditingRule(rule);
                      setFormData(rule);
                      setShowAddDialog(true);
                    }}
                  >
                    <Edit2 className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => {
                      onDeleteRule(rule.id);
                      showToast('Notification rule deleted', 'success');
                    }}
                  >
                    <Trash2 className="h-4 w-4 text-destructive" />
                  </Button>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </Card>

      {/* Add/Edit Rule Dialog */}
      <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle>
              {editingRule ? 'Edit Notification Rule' : 'Add Notification Rule'}
            </DialogTitle>
            <DialogDescription>
              Configure when and how you want to be notified
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            {/* Rule Name */}
            <div className="space-y-2">
              <Label>Rule Name</Label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                placeholder="e.g., High Budget Alert"
              />
            </div>

            {/* Notification Type */}
            <div className="space-y-2">
              <Label>Notification Type</Label>
              <Select
                value={formData.type}
                onValueChange={(value: any) => setFormData({ ...formData, type: value })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {notificationTypes.map((type) => (
                    <SelectItem key={type.id} value={type.id}>
                      <div className="flex items-center gap-2">
                        {type.icon}
                        <div>
                          <p className="font-medium">{type.label}</p>
                          <p className="text-xs text-muted-foreground">{type.description}</p>
                        </div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Trigger Configuration */}
            {formData.type === 'threshold' && (
              <div className="space-y-2">
                <Label>Budget Threshold (%)</Label>
                <div className="space-y-2">
                  <Slider
                    value={[formData.trigger?.value as number || 80]}
                    onValueChange={(value) => setFormData({
                      ...formData,
                      trigger: { ...formData.trigger!, value: value[0] }
                    })}
                    min={0}
                    max={100}
                    step={5}
                  />
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>0%</span>
                    <span className="font-medium">{formData.trigger?.value}%</span>
                    <span>100%</span>
                  </div>
                </div>
              </div>
            )}

            {/* Action Configuration */}
            <div className="space-y-2">
              <Label>Notification Channels</Label>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="email" className="text-sm font-normal cursor-pointer">
                    Email Notification
                  </Label>
                  <Switch
                    id="email"
                    checked={formData.action?.email}
                    onCheckedChange={(checked) => setFormData({
                      ...formData,
                      action: { ...formData.action!, email: checked }
                    })}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="inapp" className="text-sm font-normal cursor-pointer">
                    In-App Notification
                  </Label>
                  <Switch
                    id="inapp"
                    checked={formData.action?.inApp}
                    onCheckedChange={(checked) => setFormData({
                      ...formData,
                      action: { ...formData.action!, inApp: checked }
                    })}
                  />
                </div>
              </div>
            </div>

            {/* Frequency */}
            <div className="space-y-2">
              <Label>Notification Frequency</Label>
              <Select
                value={formData.action?.frequency}
                onValueChange={(value: any) => setFormData({
                  ...formData,
                  action: { ...formData.action!, frequency: value }
                })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="immediate">Immediate</SelectItem>
                  <SelectItem value="daily">Daily Digest</SelectItem>
                  <SelectItem value="weekly">Weekly Summary</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAddDialog(false)}>
              Cancel
            </Button>
            <Button onClick={editingRule ? handleUpdateRule : handleAddRule}>
              {editingRule ? 'Update Rule' : 'Add Rule'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Test Notification Dialog */}
      <Dialog open={showTestDialog} onOpenChange={setShowTestDialog}>
        <DialogContent className="max-w-sm">
          <DialogHeader>
            <DialogTitle>Testing Notification</DialogTitle>
            <DialogDescription>
              Sending test notification to verify your settings
            </DialogDescription>
          </DialogHeader>
          <div className="py-8 text-center">
            <div className="animate-spin h-8 w-8 mx-auto mb-4 border-2 border-primary border-t-transparent rounded-full" />
            <p className="text-sm text-muted-foreground">
              Sending test notification...
            </p>
          </div>
        </DialogContent>
      </Dialog>
    </motion.div>
  );
};