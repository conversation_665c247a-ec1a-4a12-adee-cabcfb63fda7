import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { APIEndpoints } from './APIEndpoints';
import type { APIKey } from './types';

describe('APIEndpoints', () => {
  const mockOnCreateKey = jest.fn();
  const mockOnDeleteKey = jest.fn();
  const mockShowToast = jest.fn();
  
  const mockApiKeys: APIKey[] = [
    {
      id: '1',
      name: 'Production API Key',
      key: 'sk_live_abc123...',
      createdAt: '2024-01-15T00:00:00Z',
      lastUsed: '2024-03-20T10:30:00Z',
      permissions: ['read:expenses', 'write:expenses', 'read:budgets']
    },
    {
      id: '2', 
      name: 'Development API Key',
      key: 'sk_test_xyz789...',
      createdAt: '2024-02-01T00:00:00Z',
      lastUsed: null,
      permissions: ['read:expenses']
    }
  ];
  
  const defaultProps = {
    apiKeys: mockApiKeys,
    onCreateKey: mockOnCreateKey,
    onDeleteKey: mockOnDeleteKey,
    showToast: mockShowToast,
    loading: false
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders API endpoints documentation', () => {
    render(<APIEndpoints {...defaultProps} />);
    
    expect(screen.getByText('API Endpoints')).toBeInTheDocument();
    expect(screen.getByText('Budget API Documentation')).toBeInTheDocument();
    expect(screen.getByText('Base URL:')).toBeInTheDocument();
    expect(screen.getByText('Authentication')).toBeInTheDocument();
  });

  it('displays existing API keys', () => {
    render(<APIEndpoints {...defaultProps} />);
    
    expect(screen.getByText('Production API Key')).toBeInTheDocument();
    expect(screen.getByText('Development API Key')).toBeInTheDocument();
    expect(screen.getByText('sk_live_abc123...')).toBeInTheDocument();
    expect(screen.getByText('sk_test_xyz789...')).toBeInTheDocument();
  });

  it('toggles between showing and hiding API keys', () => {
    render(<APIEndpoints {...defaultProps} />);
    
    // Keys should be hidden by default
    const hideButtons = screen.getAllByText('Hide');
    expect(hideButtons).toHaveLength(2);
    
    // Click to hide first key
    fireEvent.click(hideButtons[0]);
    
    // Check if key is now masked
    expect(screen.getByText('••••••••••••••••')).toBeInTheDocument();
    expect(screen.getByText('Show')).toBeInTheDocument();
  });

  it('creates a new API key', async () => {
    render(<APIEndpoints {...defaultProps} />);
    
    const createButton = screen.getByText('Create API Key');
    fireEvent.click(createButton);
    
    // Fill in the form
    const nameInput = screen.getByPlaceholderText('My API Key');
    fireEvent.change(nameInput, { target: { value: 'Test Key' } });
    
    // Select permissions
    const readExpenses = screen.getByLabelText('Read Expenses');
    const writeExpenses = screen.getByLabelText('Write Expenses');
    fireEvent.click(readExpenses);
    fireEvent.click(writeExpenses);
    
    // Submit
    const submitButton = screen.getByText('Generate Key');
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(mockOnCreateKey).toHaveBeenCalledWith({
        name: 'Test Key',
        permissions: ['read:expenses', 'write:expenses']
      });
    });
  });

  it('validates API key creation form', async () => {
    render(<APIEndpoints {...defaultProps} />);
    
    const createButton = screen.getByText('Create API Key');
    fireEvent.click(createButton);
    
    // Try to submit without name
    const submitButton = screen.getByText('Generate Key');
    fireEvent.click(submitButton);
    
    expect(mockShowToast).toHaveBeenCalledWith(
      'Please provide a key name and select permissions',
      'error'
    );
  });

  it('deletes an API key with confirmation', async () => {
    render(<APIEndpoints {...defaultProps} />);
    
    const deleteButtons = screen.getAllByLabelText(/delete/i);
    fireEvent.click(deleteButtons[0]);
    
    // Confirm deletion
    await waitFor(() => {
      expect(mockOnDeleteKey).toHaveBeenCalledWith('1');
    });
  });

  it('copies API key to clipboard', async () => {
    // Mock clipboard API
    Object.assign(navigator, {
      clipboard: {
        writeText: jest.fn().mockResolvedValue(undefined)
      }
    });
    
    render(<APIEndpoints {...defaultProps} />);
    
    const copyButtons = screen.getAllByLabelText(/copy/i);
    fireEvent.click(copyButtons[0]);
    
    await waitFor(() => {
      expect(navigator.clipboard.writeText).toHaveBeenCalledWith('sk_live_abc123...');
      expect(mockShowToast).toHaveBeenCalledWith('API key copied to clipboard', 'default');
    });
  });

  it('displays endpoint documentation with examples', () => {
    render(<APIEndpoints {...defaultProps} />);
    
    // Check if endpoints are listed
    expect(screen.getByText('GET /api/v1/expenses')).toBeInTheDocument();
    expect(screen.getByText('POST /api/v1/expenses')).toBeInTheDocument();
    expect(screen.getByText('GET /api/v1/budgets')).toBeInTheDocument();
    expect(screen.getByText('PUT /api/v1/budgets/:id')).toBeInTheDocument();
  });

  it('shows interactive API tester', () => {
    render(<APIEndpoints {...defaultProps} />);
    
    const endpoint = screen.getByText('GET /api/v1/expenses');
    fireEvent.click(endpoint);
    
    // Check if details are expanded
    expect(screen.getByText('Query Parameters:')).toBeInTheDocument();
    expect(screen.getByText('Response Example:')).toBeInTheDocument();
  });

  it('generates curl commands', () => {
    render(<APIEndpoints {...defaultProps} />);
    
    const endpoint = screen.getByText('GET /api/v1/expenses');
    fireEvent.click(endpoint);
    
    const tryButton = screen.getByText('Try it');
    fireEvent.click(tryButton);
    
    // Check if curl command is displayed
    expect(screen.getByText(/curl -X GET/)).toBeInTheDocument();
    expect(screen.getByText(/-H "Authorization: Bearer/)).toBeInTheDocument();
  });

  it('handles loading state', () => {
    render(<APIEndpoints {...defaultProps} loading={true} />);
    
    const createButton = screen.getByText('Create API Key');
    expect(createButton).toHaveAttribute('disabled');
  });

  it('displays permission descriptions', () => {
    render(<APIEndpoints {...defaultProps} />);
    
    const createButton = screen.getByText('Create API Key');
    fireEvent.click(createButton);
    
    expect(screen.getByText('View expense records')).toBeInTheDocument();
    expect(screen.getByText('Create and update expenses')).toBeInTheDocument();
    expect(screen.getByText('View budget information')).toBeInTheDocument();
    expect(screen.getByText('Modify budget allocations')).toBeInTheDocument();
  });

  it('shows API key usage statistics', () => {
    render(<APIEndpoints {...defaultProps} />);
    
    // Check if last used dates are displayed
    expect(screen.getByText(/Last used:.*Mar 20/)).toBeInTheDocument();
    expect(screen.getByText('Never used')).toBeInTheDocument();
  });

  it('filters endpoints by permission', () => {
    render(<APIEndpoints {...defaultProps} />);
    
    // Select a filter
    const filterSelect = screen.getByLabelText('Filter by permission');
    fireEvent.change(filterSelect, { target: { value: 'read:expenses' } });
    
    // Check if endpoints are filtered
    expect(screen.getByText('GET /api/v1/expenses')).toBeInTheDocument();
    expect(screen.queryByText('POST /api/v1/expenses')).not.toBeInTheDocument();
  });

  it('exports API documentation', () => {
    // Mock URL.createObjectURL
    global.URL.createObjectURL = jest.fn(() => 'blob:mock-url');
    
    render(<APIEndpoints {...defaultProps} />);
    
    const exportButton = screen.getByText('Export Documentation');
    fireEvent.click(exportButton);
    
    expect(global.URL.createObjectURL).toHaveBeenCalled();
  });
});