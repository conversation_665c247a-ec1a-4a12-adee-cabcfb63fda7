import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Webhook, 
  Plus, 
  Trash2, 
  Edit2, 
  Save, 
  X, 
  CheckCircle, 
  AlertCircle,
  RefreshCw,
  Clock,
  Send,
  Shield,
  Zap,
  Info,
  Copy,
  ExternalLink,
  TestTube
} from 'lucide-react';
import { format } from 'date-fns';
import { WebhookConfig, WebhookEvent, WebhookDelivery } from './types';

interface WebhookManagerProps {
  webhooks?: WebhookConfig[];
  onCreateWebhook?: (webhook: Omit<WebhookConfig, 'id' | 'createdAt'>) => Promise<WebhookConfig>;
  onUpdateWebhook?: (id: string, updates: Partial<WebhookConfig>) => Promise<void>;
  onDeleteWebhook?: (id: string) => Promise<void>;
  onTestWebhook?: (id: string) => Promise<WebhookDelivery>;
  showToast?: (message: string, type: 'default' | 'success' | 'error') => void;
  loading?: boolean;
}

export const WebhookManager: React.FC<WebhookManagerProps> = ({
  webhooks = [],
  onCreateWebhook,
  onUpdateWebhook,
  onDeleteWebhook,
  onTestWebhook,
  showToast,
  loading = false
}) => {
  const [localWebhooks, setLocalWebhooks] = useState<WebhookConfig[]>(webhooks);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [testingId, setTestingId] = useState<string | null>(null);
  const [selectedWebhook, setSelectedWebhook] = useState<WebhookConfig | null>(null);
  
  // Form state
  const [formData, setFormData] = useState({
    name: '',
    url: '',
    events: [] as WebhookEvent[],
    headers: {} as Record<string, string>,
    active: true,
    retryEnabled: true,
    maxRetries: 3
  });

  // Event types
  const eventTypes: { value: WebhookEvent; label: string; description: string }[] = [
    { 
      value: 'expense.created', 
      label: 'Expense Created',
      description: 'Triggered when a new expense is created'
    },
    { 
      value: 'expense.updated', 
      label: 'Expense Updated',
      description: 'Triggered when an expense is updated'
    },
    { 
      value: 'expense.deleted', 
      label: 'Expense Deleted',
      description: 'Triggered when an expense is deleted'
    },
    { 
      value: 'expense.approved', 
      label: 'Expense Approved',
      description: 'Triggered when an expense is approved'
    },
    { 
      value: 'budget.updated', 
      label: 'Budget Updated',
      description: 'Triggered when budget configuration changes'
    },
    { 
      value: 'budget.threshold_reached', 
      label: 'Budget Threshold Reached',
      description: 'Triggered when spending reaches defined thresholds'
    },
    { 
      value: 'allocation.changed', 
      label: 'Allocation Changed',
      description: 'Triggered when budget allocations are modified'
    },
    { 
      value: 'report.generated', 
      label: 'Report Generated',
      description: 'Triggered when a new report is generated'
    }
  ];

  useEffect(() => {
    setLocalWebhooks(webhooks);
  }, [webhooks]);

  // Handle webhook creation
  const handleCreate = async () => {
    if (!formData.name || !formData.url || formData.events.length === 0) {
      if (showToast) {
        showToast('Please fill in all required fields', 'error');
      }
      return;
    }

    try {
      const newWebhook = await onCreateWebhook?.({
        ...formData,
        secret: generateSecret()
      }) || {
        ...formData,
        id: Date.now().toString(),
        secret: generateSecret(),
        createdAt: new Date().toISOString()
      };

      setLocalWebhooks([...localWebhooks, newWebhook]);
      setShowAddForm(false);
      resetForm();
      
      if (showToast) {
        showToast('Webhook created successfully', 'success');
      }
    } catch (error) {
      if (showToast) {
        showToast('Failed to create webhook', 'error');
      }
    }
  };

  // Handle webhook update
  const handleUpdate = async (id: string) => {
    const webhook = localWebhooks.find(w => w.id === id);
    if (!webhook) return;

    try {
      await onUpdateWebhook?.(id, formData);
      
      setLocalWebhooks(localWebhooks.map(w => 
        w.id === id ? { ...w, ...formData } : w
      ));
      
      setEditingId(null);
      resetForm();
      
      if (showToast) {
        showToast('Webhook updated successfully', 'success');
      }
    } catch (error) {
      if (showToast) {
        showToast('Failed to update webhook', 'error');
      }
    }
  };

  // Handle webhook deletion
  const handleDelete = async (id: string) => {
    try {
      await onDeleteWebhook?.(id);
      setLocalWebhooks(localWebhooks.filter(w => w.id !== id));
      
      if (showToast) {
        showToast('Webhook deleted successfully', 'success');
      }
    } catch (error) {
      if (showToast) {
        showToast('Failed to delete webhook', 'error');
      }
    }
  };

  // Test webhook
  const handleTest = async (id: string) => {
    setTestingId(id);
    
    try {
      const delivery = await onTestWebhook?.(id) || {
        id: Date.now().toString(),
        webhookId: id,
        event: 'test.event' as WebhookEvent,
        url: localWebhooks.find(w => w.id === id)?.url || '',
        status: 'success' as const,
        statusCode: 200,
        timestamp: new Date().toISOString(),
        duration: 123,
        retryCount: 0
      };

      if (showToast) {
        showToast(
          delivery.status === 'success' 
            ? 'Webhook test successful' 
            : 'Webhook test failed',
          delivery.status === 'success' ? 'success' : 'error'
        );
      }
    } catch (error) {
      if (showToast) {
        showToast('Failed to test webhook', 'error');
      }
    } finally {
      setTestingId(null);
    }
  };

  // Generate webhook secret
  const generateSecret = () => {
    return `whsec_${Array.from({ length: 32 }, () => 
      Math.random().toString(36).charAt(2)
    ).join('')}`;
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      name: '',
      url: '',
      events: [],
      headers: {},
      active: true,
      retryEnabled: true,
      maxRetries: 3
    });
  };

  // Start editing
  const startEdit = (webhook: WebhookConfig) => {
    setEditingId(webhook.id);
    setFormData({
      name: webhook.name,
      url: webhook.url,
      events: webhook.events,
      headers: webhook.headers || {},
      active: webhook.active,
      retryEnabled: webhook.retryEnabled,
      maxRetries: webhook.maxRetries
    });
  };

  // Copy webhook URL
  const copyUrl = (url: string) => {
    navigator.clipboard.writeText(url);
    if (showToast) {
      showToast('URL copied to clipboard', 'success');
    }
  };

  // Copy webhook secret
  const copySecret = (secret: string) => {
    navigator.clipboard.writeText(secret);
    if (showToast) {
      showToast('Secret copied to clipboard', 'success');
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-card rounded-lg p-6 border"
      >
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <Webhook className="h-5 w-5 text-muted-foreground" />
            <h3 className="text-lg font-semibold">Webhook Management</h3>
          </div>
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => setShowAddForm(true)}
            className="flex items-center gap-2 px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
          >
            <Plus className="h-4 w-4" />
            Add Webhook
          </motion.button>
        </div>

        {/* Info Box */}
        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 flex items-start gap-3">
          <Info className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5" />
          <div className="text-sm text-blue-700 dark:text-blue-300">
            <p className="font-medium mb-1">Webhook Integration</p>
            <p className="text-xs">
              Webhooks allow you to receive real-time notifications when events occur in the budget system. 
              Configure endpoints to integrate with external services and automate workflows.
            </p>
          </div>
        </div>
      </motion.div>

      {/* Add/Edit Form */}
      <AnimatePresence>
        {(showAddForm || editingId) && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="bg-card rounded-lg p-6 border"
          >
            <h4 className="text-md font-semibold mb-4">
              {editingId ? 'Edit Webhook' : 'Create New Webhook'}
            </h4>

            <div className="space-y-4">
              {/* Name and URL */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">
                    Webhook Name <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    placeholder="Production Budget Updates"
                    className="w-full px-3 py-2 border rounded-md bg-background"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">
                    Endpoint URL <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="url"
                    value={formData.url}
                    onChange={(e) => setFormData({ ...formData, url: e.target.value })}
                    placeholder="https://api.example.com/webhooks/budget"
                    className="w-full px-3 py-2 border rounded-md bg-background"
                  />
                </div>
              </div>

              {/* Events */}
              <div>
                <label className="block text-sm font-medium mb-2">
                  Events to Subscribe <span className="text-red-500">*</span>
                </label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {eventTypes.map((event) => (
                    <label
                      key={event.value}
                      className="flex items-start gap-2 p-3 border rounded-md hover:bg-muted/50 cursor-pointer transition-colors"
                    >
                      <input
                        type="checkbox"
                        checked={formData.events.includes(event.value)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setFormData({ 
                              ...formData, 
                              events: [...formData.events, event.value] 
                            });
                          } else {
                            setFormData({ 
                              ...formData, 
                              events: formData.events.filter(ev => ev !== event.value) 
                            });
                          }
                        }}
                        className="mt-1"
                      />
                      <div className="flex-1">
                        <p className="text-sm font-medium">{event.label}</p>
                        <p className="text-xs text-muted-foreground">{event.description}</p>
                      </div>
                    </label>
                  ))}
                </div>
              </div>

              {/* Settings */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={formData.active}
                    onChange={(e) => setFormData({ ...formData, active: e.target.checked })}
                  />
                  <span className="text-sm">Active</span>
                </label>
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={formData.retryEnabled}
                    onChange={(e) => setFormData({ ...formData, retryEnabled: e.target.checked })}
                  />
                  <span className="text-sm">Enable Retries</span>
                </label>
                {formData.retryEnabled && (
                  <div className="flex items-center gap-2">
                    <label className="text-sm">Max Retries:</label>
                    <input
                      type="number"
                      min="1"
                      max="10"
                      value={formData.maxRetries}
                      onChange={(e) => setFormData({ ...formData, maxRetries: parseInt(e.target.value) })}
                      className="w-16 px-2 py-1 border rounded text-sm"
                    />
                  </div>
                )}
              </div>

              {/* Action Buttons */}
              <div className="flex justify-end gap-2 pt-4">
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => {
                    setShowAddForm(false);
                    setEditingId(null);
                    resetForm();
                  }}
                  className="px-4 py-2 text-sm border rounded-md hover:bg-muted transition-colors"
                >
                  Cancel
                </motion.button>
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => editingId ? handleUpdate(editingId) : handleCreate()}
                  className="flex items-center gap-2 px-4 py-2 text-sm bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
                >
                  <Save className="h-4 w-4" />
                  {editingId ? 'Update' : 'Create'}
                </motion.button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Webhooks List */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="bg-card rounded-lg p-6 border"
      >
        {localWebhooks.length === 0 ? (
          <div className="text-center py-12 text-muted-foreground">
            <Webhook className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p className="text-sm">No webhooks configured yet</p>
            <p className="text-xs mt-1">Create a webhook to start receiving event notifications</p>
          </div>
        ) : (
          <div className="space-y-4">
            {localWebhooks.map((webhook) => (
              <motion.div
                key={webhook.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                className="border rounded-lg p-4"
              >
                <div className="flex items-start justify-between mb-3">
                  <div>
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-medium">{webhook.name}</h4>
                      <span className={`text-xs px-2 py-0.5 rounded-full ${
                        webhook.active 
                          ? 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300' 
                          : 'bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-300'
                      }`}>
                        {webhook.active ? 'Active' : 'Inactive'}
                      </span>
                      {webhook.retryEnabled && (
                        <span className="text-xs px-2 py-0.5 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300">
                          Retries: {webhook.maxRetries}
                        </span>
                      )}
                    </div>
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <code className="bg-muted px-2 py-0.5 rounded">{webhook.url}</code>
                      <motion.button
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        onClick={() => copyUrl(webhook.url)}
                        className="p-1 hover:bg-muted rounded transition-colors"
                      >
                        <Copy className="h-3 w-3" />
                      </motion.button>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => handleTest(webhook.id)}
                      disabled={testingId === webhook.id}
                      className="p-2 hover:bg-muted rounded transition-colors"
                    >
                      {testingId === webhook.id ? (
                        <RefreshCw className="h-4 w-4 animate-spin" />
                      ) : (
                        <TestTube className="h-4 w-4" />
                      )}
                    </motion.button>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => startEdit(webhook)}
                      className="p-2 hover:bg-muted rounded transition-colors"
                    >
                      <Edit2 className="h-4 w-4" />
                    </motion.button>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => handleDelete(webhook.id)}
                      className="p-2 text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded transition-colors"
                    >
                      <Trash2 className="h-4 w-4" />
                    </motion.button>
                  </div>
                </div>

                {/* Events */}
                <div className="mb-3">
                  <p className="text-xs text-muted-foreground mb-1">Subscribed Events:</p>
                  <div className="flex flex-wrap gap-1">
                    {webhook.events.map((event) => (
                      <span
                        key={event}
                        className="text-xs px-2 py-0.5 bg-muted rounded"
                      >
                        {eventTypes.find(e => e.value === event)?.label || event}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Secret */}
                <div className="flex items-center gap-2 text-xs">
                  <Shield className="h-3 w-3 text-muted-foreground" />
                  <span className="text-muted-foreground">Secret:</span>
                  <code className="bg-muted px-2 py-0.5 rounded">
                    {webhook.secret.substring(0, 20)}...
                  </code>
                  <motion.button
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={() => copySecret(webhook.secret)}
                    className="p-1 hover:bg-muted rounded transition-colors"
                  >
                    <Copy className="h-3 w-3" />
                  </motion.button>
                </div>

                {/* Last Delivery */}
                {webhook.lastDelivery && (
                  <div className="mt-3 pt-3 border-t">
                    <div className="flex items-center justify-between text-xs">
                      <div className="flex items-center gap-2">
                        <Clock className="h-3 w-3 text-muted-foreground" />
                        <span className="text-muted-foreground">
                          Last delivery: {format(new Date(webhook.lastDelivery.timestamp), 'MMM d, h:mm a')}
                        </span>
                      </div>
                      <div className="flex items-center gap-1">
                        {webhook.lastDelivery.status === 'success' ? (
                          <CheckCircle className="h-3 w-3 text-green-600" />
                        ) : webhook.lastDelivery.status === 'pending' ? (
                          <Clock className="h-3 w-3 text-orange-600" />
                        ) : (
                          <AlertCircle className="h-3 w-3 text-red-600" />
                        )}
                        <span className={`text-xs ${
                          webhook.lastDelivery.status === 'success' 
                            ? 'text-green-600' 
                            : webhook.lastDelivery.status === 'pending'
                            ? 'text-orange-600'
                            : 'text-red-600'
                        }`}>
                          {webhook.lastDelivery.status === 'success' ? 'Success' : 
                           webhook.lastDelivery.status === 'pending' ? 'Pending' : 'Failed'}
                          {webhook.lastDelivery.statusCode && ` (${webhook.lastDelivery.statusCode})`}
                        </span>
                        <span className="text-muted-foreground">
                          • {webhook.lastDelivery.duration}ms
                        </span>
                      </div>
                    </div>
                  </div>
                )}
              </motion.div>
            ))}
          </div>
        )}
      </motion.div>

      {/* Documentation */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="bg-muted/50 rounded-lg p-4"
      >
        <div className="flex items-center gap-3 mb-3">
          <Zap className="h-5 w-5 text-muted-foreground" />
          <h4 className="text-sm font-medium">Webhook Payload Example</h4>
        </div>
        <pre className="bg-card rounded p-3 text-xs overflow-x-auto">
{`{
  "event": "expense.created",
  "timestamp": "2024-03-15T10:30:00Z",
  "data": {
    "expense": {
      "id": "<expense_id>",
      "amount": "<amount>",
      "description": "<description>",
      "category": "Technical Training",
      "status": "submitted"
    }
  },
  "signature": "sha256=..."
}`}
        </pre>
      </motion.div>
    </div>
  );
};