import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { WebhookManager } from './WebhookManager';
import type { WebhookConfig, WebhookDelivery } from './types';

describe('WebhookManager', () => {
  const mockOnCreateWebhook = jest.fn();
  const mockOnUpdateWebhook = jest.fn();
  const mockOnDeleteWebhook = jest.fn();
  const mockOnTestWebhook = jest.fn();
  const mockShowToast = jest.fn();
  
  const mockWebhooks: WebhookConfig[] = [
    {
      id: '1',
      name: 'Slack Notifications',
      url: 'https://hooks.slack.com/services/ABC123',
      secret: 'webhook_secret_123',
      events: ['expense.created', 'budget.exceeded'],
      headers: { 'X-Custom-Header': 'value' },
      active: true,
      retryEnabled: true,
      maxRetries: 3,
      createdAt: '2024-01-15T00:00:00Z',
      lastDelivery: {
        id: 'd1',
        webhookId: '1',
        event: 'expense.created',
        url: 'https://hooks.slack.com/services/ABC123',
        status: 'success',
        statusCode: 200,
        response: '{"ok":true}',
        error: null,
        timestamp: '2024-03-20T10:30:00Z',
        duration: 245,
        retryCount: 0
      }
    },
    {
      id: '2',
      name: 'Teams Integration',
      url: 'https://webhook.office.com/webhookb2/xyz',
      secret: 'webhook_secret_456',
      events: ['expense.approved'],
      headers: {},
      active: false,
      retryEnabled: false,
      maxRetries: 0,
      createdAt: '2024-02-01T00:00:00Z'
    }
  ];
  
  const defaultProps = {
    webhooks: mockWebhooks,
    onCreateWebhook: mockOnCreateWebhook,
    onUpdateWebhook: mockOnUpdateWebhook,
    onDeleteWebhook: mockOnDeleteWebhook,
    onTestWebhook: mockOnTestWebhook,
    showToast: mockShowToast,
    loading: false
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders webhook manager with existing webhooks', () => {
    render(<WebhookManager {...defaultProps} />);
    
    expect(screen.getByText('Webhook Manager')).toBeInTheDocument();
    expect(screen.getByText('Slack Notifications')).toBeInTheDocument();
    expect(screen.getByText('Teams Integration')).toBeInTheDocument();
  });

  it('shows webhook status indicators', () => {
    render(<WebhookManager {...defaultProps} />);
    
    // Check active/inactive status
    expect(screen.getByText('Active')).toBeInTheDocument();
    expect(screen.getByText('Inactive')).toBeInTheDocument();
    
    // Check last delivery status
    expect(screen.getByText('200')).toBeInTheDocument();
    expect(screen.getByText('245ms')).toBeInTheDocument();
  });

  it('creates a new webhook', async () => {
    render(<WebhookManager {...defaultProps} />);
    
    const addButton = screen.getByText('Add Webhook');
    fireEvent.click(addButton);
    
    // Fill in the form
    const nameInput = screen.getByPlaceholderText('My Webhook');
    const urlInput = screen.getByPlaceholderText('https://example.com/webhook');
    
    fireEvent.change(nameInput, { target: { value: 'Test Webhook' } });
    fireEvent.change(urlInput, { target: { value: 'https://test.com/webhook' } });
    
    // Select events
    const expenseCreated = screen.getByLabelText('Expense Created');
    fireEvent.click(expenseCreated);
    
    // Submit
    const submitButton = screen.getByText('Create Webhook');
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(mockOnCreateWebhook).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'Test Webhook',
          url: 'https://test.com/webhook',
          events: ['expense.created'],
          active: true,
          retryEnabled: true,
          maxRetries: 3
        })
      );
    });
  });

  it('validates webhook URL format', async () => {
    render(<WebhookManager {...defaultProps} />);
    
    const addButton = screen.getByText('Add Webhook');
    fireEvent.click(addButton);
    
    const urlInput = screen.getByPlaceholderText('https://example.com/webhook');
    fireEvent.change(urlInput, { target: { value: 'invalid-url' } });
    
    const submitButton = screen.getByText('Create Webhook');
    fireEvent.click(submitButton);
    
    expect(mockShowToast).toHaveBeenCalledWith(
      'Please enter a valid webhook URL',
      'error'
    );
  });

  it('edits an existing webhook', async () => {
    render(<WebhookManager {...defaultProps} />);
    
    const editButtons = screen.getAllByLabelText(/edit/i);
    fireEvent.click(editButtons[0]);
    
    // Change the name
    const nameInput = screen.getByDisplayValue('Slack Notifications');
    fireEvent.change(nameInput, { target: { value: 'Updated Slack Hook' } });
    
    // Save changes
    const saveButton = screen.getByText('Update Webhook');
    fireEvent.click(saveButton);
    
    await waitFor(() => {
      expect(mockOnUpdateWebhook).toHaveBeenCalledWith(
        '1',
        expect.objectContaining({
          name: 'Updated Slack Hook'
        })
      );
    });
  });

  it('toggles webhook active status', async () => {
    render(<WebhookManager {...defaultProps} />);
    
    const toggleButtons = screen.getAllByRole('switch');
    fireEvent.click(toggleButtons[1]); // Toggle the inactive webhook
    
    await waitFor(() => {
      expect(mockOnUpdateWebhook).toHaveBeenCalledWith(
        '2',
        expect.objectContaining({
          active: true
        })
      );
    });
  });

  it('tests a webhook', async () => {
    mockOnTestWebhook.mockResolvedValue({
      id: 'test-1',
      webhookId: '1',
      event: 'test.event',
      status: 'success',
      statusCode: 200,
      timestamp: new Date().toISOString(),
      duration: 150
    });
    
    render(<WebhookManager {...defaultProps} />);
    
    const testButtons = screen.getAllByLabelText(/test/i);
    fireEvent.click(testButtons[0]);
    
    await waitFor(() => {
      expect(mockOnTestWebhook).toHaveBeenCalledWith('1');
      expect(mockShowToast).toHaveBeenCalledWith(
        'Webhook test completed successfully',
        'success'
      );
    });
  });

  it('handles failed webhook test', async () => {
    mockOnTestWebhook.mockResolvedValue({
      id: 'test-2',
      webhookId: '1',
      event: 'test.event',
      status: 'failed',
      statusCode: 500,
      error: 'Internal Server Error',
      timestamp: new Date().toISOString(),
      duration: 1500
    });
    
    render(<WebhookManager {...defaultProps} />);
    
    const testButtons = screen.getAllByLabelText(/test/i);
    fireEvent.click(testButtons[0]);
    
    await waitFor(() => {
      expect(mockShowToast).toHaveBeenCalledWith(
        'Webhook test failed: 500',
        'error'
      );
    });
  });

  it('deletes a webhook with confirmation', async () => {
    render(<WebhookManager {...defaultProps} />);
    
    const deleteButtons = screen.getAllByLabelText(/delete/i);
    fireEvent.click(deleteButtons[0]);
    
    await waitFor(() => {
      expect(mockOnDeleteWebhook).toHaveBeenCalledWith('1');
    });
  });

  it('manages custom headers', () => {
    render(<WebhookManager {...defaultProps} />);
    
    const addButton = screen.getByText('Add Webhook');
    fireEvent.click(addButton);
    
    // Add a custom header
    const addHeaderButton = screen.getByText('Add Header');
    fireEvent.click(addHeaderButton);
    
    const keyInput = screen.getByPlaceholderText('Header Name');
    const valueInput = screen.getByPlaceholderText('Header Value');
    
    fireEvent.change(keyInput, { target: { value: 'X-API-Key' } });
    fireEvent.change(valueInput, { target: { value: 'secret123' } });
    
    // Should show the header
    expect(screen.getByDisplayValue('X-API-Key')).toBeInTheDocument();
    expect(screen.getByDisplayValue('secret123')).toBeInTheDocument();
  });

  it('displays webhook event descriptions', () => {
    render(<WebhookManager {...defaultProps} />);
    
    const addButton = screen.getByText('Add Webhook');
    fireEvent.click(addButton);
    
    // Check event descriptions
    expect(screen.getByText('Triggered when a new expense is created')).toBeInTheDocument();
    expect(screen.getByText('Triggered when an expense is approved')).toBeInTheDocument();
    expect(screen.getByText('Triggered when budget limit is exceeded')).toBeInTheDocument();
  });

  it('shows webhook secret management', () => {
    render(<WebhookManager {...defaultProps} />);
    
    const editButtons = screen.getAllByLabelText(/edit/i);
    fireEvent.click(editButtons[0]);
    
    // Check if secret is masked
    const secretInput = screen.getByLabelText('Webhook Secret');
    expect(secretInput).toHaveAttribute('type', 'password');
    
    // Toggle visibility
    const showButton = screen.getByLabelText('Show secret');
    fireEvent.click(showButton);
    
    expect(secretInput).toHaveAttribute('type', 'text');
  });

  it('configures retry settings', () => {
    render(<WebhookManager {...defaultProps} />);
    
    const addButton = screen.getByText('Add Webhook');
    fireEvent.click(addButton);
    
    // Toggle retry
    const retryCheckbox = screen.getByLabelText('Enable automatic retry');
    fireEvent.click(retryCheckbox);
    
    // Change max retries
    const retriesInput = screen.getByLabelText('Max retries');
    fireEvent.change(retriesInput, { target: { value: '5' } });
    
    expect(retriesInput).toHaveValue(5);
  });

  it('displays delivery history', () => {
    render(<WebhookManager {...defaultProps} />);
    
    // Expand webhook details
    const webhook = screen.getByText('Slack Notifications');
    fireEvent.click(webhook);
    
    // Check if delivery history is shown
    expect(screen.getByText('Last Delivery')).toBeInTheDocument();
    expect(screen.getByText('expense.created')).toBeInTheDocument();
    expect(screen.getByText('Success')).toBeInTheDocument();
  });

  it('filters webhooks by status', () => {
    render(<WebhookManager {...defaultProps} />);
    
    const filterSelect = screen.getByLabelText('Filter by status');
    fireEvent.change(filterSelect, { target: { value: 'active' } });
    
    // Should only show active webhooks
    expect(screen.getByText('Slack Notifications')).toBeInTheDocument();
    expect(screen.queryByText('Teams Integration')).not.toBeInTheDocument();
  });

  it('generates webhook signature example', () => {
    render(<WebhookManager {...defaultProps} />);
    
    const editButtons = screen.getAllByLabelText(/edit/i);
    fireEvent.click(editButtons[0]);
    
    // Should show signature info
    expect(screen.getByText(/HMAC-SHA256/)).toBeInTheDocument();
    expect(screen.getByText('X-Webhook-Signature')).toBeInTheDocument();
  });

  it('handles loading state', () => {
    render(<WebhookManager {...defaultProps} loading={true} />);
    
    const addButton = screen.getByText('Add Webhook');
    expect(addButton).toHaveAttribute('disabled');
    
    const testButtons = screen.getAllByLabelText(/test/i);
    testButtons.forEach(button => {
      expect(button).toHaveAttribute('disabled');
    });
  });
});