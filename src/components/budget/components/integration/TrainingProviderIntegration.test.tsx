import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { TrainingProviderIntegration } from './TrainingProviderIntegration';
import type { TrainingProvider, ProviderCourse, ProviderSync } from './types';

describe('TrainingProviderIntegration', () => {
  const mockOnAddProvider = jest.fn();
  const mockOnUpdateProvider = jest.fn();
  const mockOnDeleteProvider = jest.fn();
  const mockOnSyncProvider = jest.fn();
  const mockOnImportCourse = jest.fn();
  const mockShowToast = jest.fn();
  
  const mockProviders: TrainingProvider[] = [
    {
      id: '1',
      name: 'Udemy Business',
      type: 'udemy',
      apiEndpoint: 'https://api.udemy.com/v1',
      credentials: {
        apiKey: 'udemy_key_123',
        clientId: '',
        clientSecret: ''
      },
      syncEnabled: true,
      lastSync: '2024-03-15T10:30:00Z',
      settings: {
        autoImportPurchases: true,
        trackProgress: true,
        importCategories: true,
        defaultCategory: 'Online Courses',
        defaultDepartment: 'Engineering'
      }
    },
    {
      id: '2',
      name: 'Coursera Enterprise',
      type: 'coursera',
      apiEndpoint: 'https://api.coursera.com/v1',
      credentials: {
        apiKey: 'coursera_key_456',
        clientId: 'client123',
        clientSecret: 'secret456'
      },
      syncEnabled: false,
      lastSync: undefined,
      settings: {
        autoImportPurchases: false,
        trackProgress: true,
        importCategories: false,
        defaultCategory: 'Training',
        defaultDepartment: 'HR'
      }
    }
  ];
  
  const defaultProps = {
    providers: mockProviders,
    onAddProvider: mockOnAddProvider,
    onUpdateProvider: mockOnUpdateProvider,
    onDeleteProvider: mockOnDeleteProvider,
    onSyncProvider: mockOnSyncProvider,
    onImportCourse: mockOnImportCourse,
    showToast: mockShowToast,
    loading: false
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders training provider integration component', () => {
    render(<TrainingProviderIntegration {...defaultProps} />);
    
    expect(screen.getByText('Training Provider Integration')).toBeInTheDocument();
    expect(screen.getByText('Automated Course Tracking')).toBeInTheDocument();
    expect(screen.getByText('Add Provider')).toBeInTheDocument();
  });

  it('displays existing providers with correct logos', () => {
    render(<TrainingProviderIntegration {...defaultProps} />);
    
    expect(screen.getByText('Udemy Business')).toBeInTheDocument();
    expect(screen.getByText('Coursera Enterprise')).toBeInTheDocument();
    expect(screen.getByText('🎓')).toBeInTheDocument(); // Udemy logo
    expect(screen.getByText('📚')).toBeInTheDocument(); // Coursera logo
  });

  it('shows provider connection status', () => {
    render(<TrainingProviderIntegration {...defaultProps} />);
    
    // Check connected/disconnected status
    const connectedElements = screen.getAllByText('Connected');
    const disconnectedElements = screen.getAllByText('Disconnected');
    
    expect(connectedElements).toHaveLength(1);
    expect(disconnectedElements).toHaveLength(1);
  });

  it('creates a new provider', async () => {
    mockOnAddProvider.mockResolvedValue({
      id: '3',
      name: 'LinkedIn Learning',
      type: 'linkedin',
      ...defaultProps.providers[0]
    });
    
    render(<TrainingProviderIntegration {...defaultProps} />);
    
    const addButton = screen.getByText('Add Provider');
    fireEvent.click(addButton);
    
    // Select provider type
    const typeSelect = screen.getByLabelText(/Provider Type/);
    fireEvent.change(typeSelect, { target: { value: 'linkedin' } });
    
    // Fill in name
    const nameInput = screen.getByPlaceholderText('LinkedIn Learning');
    fireEvent.change(nameInput, { target: { value: 'My LinkedIn Learning' } });
    
    // Fill in API key
    const apiKeyInput = screen.getByLabelText('API Key');
    fireEvent.change(apiKeyInput, { target: { value: 'linkedin_key_789' } });
    
    // Submit
    const submitButton = screen.getByText('Add Provider');
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(mockOnAddProvider).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'My LinkedIn Learning',
          type: 'linkedin',
          credentials: expect.objectContaining({
            apiKey: 'linkedin_key_789'
          })
        })
      );
      expect(mockShowToast).toHaveBeenCalledWith(
        'Training provider added successfully',
        'success'
      );
    });
  });

  it('validates provider creation form', async () => {
    render(<TrainingProviderIntegration {...defaultProps} />);
    
    const addButton = screen.getByText('Add Provider');
    fireEvent.click(addButton);
    
    // Try to submit without required fields
    const submitButton = screen.getByText('Add Provider');
    fireEvent.click(submitButton);
    
    expect(mockShowToast).toHaveBeenCalledWith(
      'Please fill in all required fields',
      'error'
    );
  });

  it('edits an existing provider', async () => {
    render(<TrainingProviderIntegration {...defaultProps} />);
    
    const editButtons = screen.getAllByLabelText(/edit/i);
    fireEvent.click(editButtons[0]);
    
    // Change name
    const nameInput = screen.getByDisplayValue('Udemy Business');
    fireEvent.change(nameInput, { target: { value: 'Udemy Enterprise' } });
    
    // Update sync settings
    const autoImportCheckbox = screen.getByLabelText('Auto-import new purchases');
    fireEvent.click(autoImportCheckbox);
    
    // Save
    const saveButton = screen.getByText('Update Provider');
    fireEvent.click(saveButton);
    
    await waitFor(() => {
      expect(mockOnUpdateProvider).toHaveBeenCalledWith(
        '1',
        expect.objectContaining({
          name: 'Udemy Enterprise'
        })
      );
    });
  });

  it('syncs courses from a provider', async () => {
    const mockSync: ProviderSync = {
      id: 'sync-1',
      providerId: '1',
      startedAt: new Date().toISOString(),
      completedAt: new Date().toISOString(),
      status: 'completed',
      coursesFound: 5,
      coursesImported: 3,
      expensesCreated: 3,
      errors: []
    };
    
    mockOnSyncProvider.mockResolvedValue(mockSync);
    
    render(<TrainingProviderIntegration {...defaultProps} />);
    
    const syncButtons = screen.getAllByLabelText(/sync/i);
    fireEvent.click(syncButtons[0]);
    
    await waitFor(() => {
      expect(mockOnSyncProvider).toHaveBeenCalledWith('1');
      expect(mockShowToast).toHaveBeenCalledWith(
        'Sync completed: 3 courses imported',
        'success'
      );
    });
  });

  it('shows sync progress indicator', async () => {
    render(<TrainingProviderIntegration {...defaultProps} />);
    
    const syncButtons = screen.getAllByLabelText(/sync/i);
    fireEvent.click(syncButtons[0]);
    
    // Check for spinning icon
    const spinningIcon = screen.getByTestId('sync-spinner-1');
    expect(spinningIcon).toHaveClass('animate-spin');
  });

  it('displays synced courses', async () => {
    // Mock sync that returns courses
    const mockSync: ProviderSync = {
      id: 'sync-2',
      providerId: '1',
      startedAt: new Date().toISOString(),
      completedAt: new Date().toISOString(),
      status: 'completed',
      coursesFound: 3,
      coursesImported: 3,
      expensesCreated: 0,
      errors: []
    };
    
    mockOnSyncProvider.mockResolvedValue(mockSync);
    
    render(<TrainingProviderIntegration {...defaultProps} />);
    
    // Sync provider
    const syncButtons = screen.getAllByLabelText(/sync/i);
    fireEvent.click(syncButtons[0]);
    
    await waitFor(() => {
      expect(screen.getByText('Advanced React Development')).toBeInTheDocument();
      expect(screen.getByText('Leadership in Tech')).toBeInTheDocument();
      expect(screen.getByText('AWS Solutions Architect')).toBeInTheDocument();
    });
    
    // Check course details
    expect(screen.getByText('40h')).toBeInTheDocument();
    expect(screen.getByText('65% complete')).toBeInTheDocument();
    expect(screen.getByText('$89.99')).toBeInTheDocument();
  });

  it('imports a course as expense', async () => {
    // First sync to get courses
    const mockSync: ProviderSync = {
      id: 'sync-3',
      providerId: '1',
      startedAt: new Date().toISOString(),
      completedAt: new Date().toISOString(),
      status: 'completed',
      coursesFound: 1,
      coursesImported: 1,
      expensesCreated: 0,
      errors: []
    };
    
    mockOnSyncProvider.mockResolvedValue(mockSync);
    
    render(<TrainingProviderIntegration {...defaultProps} />);
    
    // Sync provider
    const syncButtons = screen.getAllByLabelText(/sync/i);
    fireEvent.click(syncButtons[0]);
    
    await waitFor(() => {
      expect(screen.getByText('Advanced React Development')).toBeInTheDocument();
    });
    
    // Import course
    const importButtons = screen.getAllByLabelText(/import as expense/i);
    fireEvent.click(importButtons[0]);
    
    await waitFor(() => {
      expect(mockOnImportCourse).toHaveBeenCalledWith('1', '1');
      expect(mockShowToast).toHaveBeenCalledWith(
        'Course imported as expense',
        'success'
      );
    });
  });

  it('deletes a provider', async () => {
    render(<TrainingProviderIntegration {...defaultProps} />);
    
    const deleteButtons = screen.getAllByLabelText(/delete/i);
    fireEvent.click(deleteButtons[0]);
    
    await waitFor(() => {
      expect(mockOnDeleteProvider).toHaveBeenCalledWith('1');
      expect(mockShowToast).toHaveBeenCalledWith(
        'Provider removed successfully',
        'success'
      );
    });
  });

  it('handles custom provider configuration', () => {
    render(<TrainingProviderIntegration {...defaultProps} />);
    
    const addButton = screen.getByText('Add Provider');
    fireEvent.click(addButton);
    
    // Select custom provider
    const typeSelect = screen.getByLabelText(/Provider Type/);
    fireEvent.change(typeSelect, { target: { value: 'custom' } });
    
    // Should show API endpoint field
    expect(screen.getByLabelText('API Endpoint')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('https://api.example.com/v1')).toBeInTheDocument();
  });

  it('manages provider credentials securely', () => {
    render(<TrainingProviderIntegration {...defaultProps} />);
    
    const editButtons = screen.getAllByLabelText(/edit/i);
    fireEvent.click(editButtons[0]);
    
    // Check if credentials are masked
    const apiKeyInput = screen.getByLabelText('API Key');
    expect(apiKeyInput).toHaveAttribute('type', 'password');
    
    const clientSecretInput = screen.getByLabelText('Client Secret');
    expect(clientSecretInput).toHaveAttribute('type', 'password');
  });

  it('configures default import settings', () => {
    render(<TrainingProviderIntegration {...defaultProps} />);
    
    const addButton = screen.getByText('Add Provider');
    fireEvent.click(addButton);
    
    // Configure defaults
    const categoryInput = screen.getByLabelText('Default Category');
    const departmentInput = screen.getByLabelText('Default Department');
    
    fireEvent.change(categoryInput, { target: { value: 'Professional Development' } });
    fireEvent.change(departmentInput, { target: { value: 'IT Department' } });
    
    expect(categoryInput).toHaveValue('Professional Development');
    expect(departmentInput).toHaveValue('IT Department');
  });

  it('shows provider-specific API notes', () => {
    render(<TrainingProviderIntegration {...defaultProps} />);
    
    const addButton = screen.getByText('Add Provider');
    fireEvent.click(addButton);
    
    // Check Udemy note
    const typeSelect = screen.getByLabelText(/Provider Type/);
    fireEvent.change(typeSelect, { target: { value: 'udemy' } });
    expect(screen.getByText('Requires Business API access')).toBeInTheDocument();
    
    // Check Coursera note
    fireEvent.change(typeSelect, { target: { value: 'coursera' } });
    expect(screen.getByText('Enterprise API required')).toBeInTheDocument();
  });

  it('displays integration guide', () => {
    render(<TrainingProviderIntegration {...defaultProps} />);
    
    expect(screen.getByText('Integration Guide')).toBeInTheDocument();
    expect(screen.getByText('Supported Features:')).toBeInTheDocument();
    expect(screen.getByText('API Requirements:')).toBeInTheDocument();
    
    // Check feature list
    expect(screen.getByText(/Automatic purchase detection/)).toBeInTheDocument();
    expect(screen.getByText(/Progress tracking/)).toBeInTheDocument();
    expect(screen.getByText(/Expense categorization/)).toBeInTheDocument();
  });

  it('shows completion certificates for completed courses', async () => {
    const mockSync: ProviderSync = {
      id: 'sync-4',
      providerId: '1',
      startedAt: new Date().toISOString(),
      completedAt: new Date().toISOString(),
      status: 'completed',
      coursesFound: 1,
      coursesImported: 1,
      expensesCreated: 0,
      errors: []
    };
    
    mockOnSyncProvider.mockResolvedValue(mockSync);
    
    render(<TrainingProviderIntegration {...defaultProps} />);
    
    // Sync provider
    const syncButtons = screen.getAllByLabelText(/sync/i);
    fireEvent.click(syncButtons[0]);
    
    await waitFor(() => {
      // Check for certificate icon on completed course
      const certificateIcons = screen.getAllByTestId('certificate-icon');
      expect(certificateIcons).toHaveLength(1);
    });
  });

  it('handles empty state', () => {
    render(<TrainingProviderIntegration {...defaultProps} providers={[]} />);
    
    expect(screen.getByText('No training providers connected')).toBeInTheDocument();
    expect(screen.getByText('Add a provider to start syncing courses')).toBeInTheDocument();
  });

  it('handles loading state', () => {
    render(<TrainingProviderIntegration {...defaultProps} loading={true} />);
    
    const addButton = screen.getByText('Add Provider');
    expect(addButton).toHaveAttribute('disabled');
  });
});