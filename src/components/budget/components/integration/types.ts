// CSV Import Types
export interface ImportedExpense {
  date: string;
  description: string;
  amount: number;
  category: string;
  department?: string;
  status?: string;
  notes?: string;
}

export interface ImportedBudget {
  year: number;
  totalAmount: number;
  quarterlyAllocations?: Array<{
    quarter: number;
    percentage: number;
  }>;
  departmentAllocations?: Array<{
    name: string;
    amount: number;
  }>;
  categoryLimits?: Array<{
    category: string;
    limit: number;
  }>;
}

// API Endpoint Types
export interface APIEndpoint {
  id: string;
  path: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  description: string;
  authentication: 'none' | 'apiKey' | 'bearer' | 'oauth';
  rateLimit?: {
    requests: number;
    period: 'second' | 'minute' | 'hour';
  };
  parameters?: Array<{
    name: string;
    type: 'path' | 'query' | 'body';
    required: boolean;
    description: string;
  }>;
  response?: {
    type: string;
    example: any;
  };
}

export interface APIKey {
  id: string;
  name: string;
  key: string;
  permissions: string[];
  createdAt: string;
  lastUsed?: string;
  expiresAt?: string;
  active: boolean;
}

// Webhook Types
export type WebhookEvent = 
  | 'expense.created'
  | 'expense.updated'
  | 'expense.deleted'
  | 'expense.approved'
  | 'budget.updated'
  | 'budget.threshold_reached'
  | 'allocation.changed'
  | 'report.generated';

export interface WebhookConfig {
  id: string;
  name: string;
  url: string;
  secret: string;
  events: WebhookEvent[];
  headers?: Record<string, string>;
  active: boolean;
  retryEnabled: boolean;
  maxRetries: number;
  createdAt: string;
  lastDelivery?: WebhookDelivery;
}

export interface WebhookDelivery {
  id: string;
  webhookId: string;
  event: WebhookEvent;
  url: string;
  status: 'pending' | 'success' | 'failed';
  statusCode?: number;
  response?: string;
  error?: string;
  timestamp: string;
  duration: number;
  retryCount: number;
}

// Training Provider Integration Types
export interface TrainingProvider {
  id: string;
  name: string;
  type: 'udemy' | 'coursera' | 'pluralsight' | 'linkedin' | 'custom';
  apiEndpoint?: string;
  credentials?: {
    apiKey?: string;
    clientId?: string;
    clientSecret?: string;
  };
  syncEnabled: boolean;
  lastSync?: string;
  settings: {
    autoImportPurchases: boolean;
    trackProgress: boolean;
    importCategories: boolean;
    defaultCategory?: string;
    defaultDepartment?: string;
  };
}

export interface ProviderCourse {
  id: string;
  providerId: string;
  externalId: string;
  title: string;
  description?: string;
  price: number;
  currency: string;
  category?: string;
  duration?: number;
  instructor?: string;
  rating?: number;
  enrollmentDate?: string;
  completionDate?: string;
  progress?: number;
  certificateUrl?: string;
}

export interface ProviderSync {
  id: string;
  providerId: string;
  startedAt: string;
  completedAt?: string;
  status: 'running' | 'completed' | 'failed';
  coursesFound: number;
  coursesImported: number;
  expensesCreated: number;
  errors?: string[];
}