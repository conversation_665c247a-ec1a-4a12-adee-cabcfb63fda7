import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  GraduationCap, 
  Plus, 
  Trash2, 
  Edit2, 
  Save, 
  X, 
  CheckCircle, 
  AlertCircle,
  RefreshCw,
  Link,
  Unlink,
  Download,
  Upload,
  Calendar,
  DollarSign,
  Award,
  TrendingUp,
  ExternalLink,
  Key,
  Shield
} from 'lucide-react';
import { format } from 'date-fns';
import { TrainingProvider, ProviderCourse, ProviderSync } from './types';

interface TrainingProviderIntegrationProps {
  providers?: TrainingProvider[];
  onAddProvider?: (provider: Omit<TrainingProvider, 'id'>) => Promise<TrainingProvider>;
  onUpdateProvider?: (id: string, updates: Partial<TrainingProvider>) => Promise<void>;
  onDeleteProvider?: (id: string) => Promise<void>;
  onSyncProvider?: (id: string) => Promise<ProviderSync>;
  onImportCourse?: (providerId: string, courseId: string) => Promise<void>;
  showToast?: (message: string, type: 'default' | 'success' | 'error') => void;
  loading?: boolean;
}

// Provider logos/icons
const providerLogos: Record<string, string> = {
  udemy: '🎓',
  coursera: '📚',
  pluralsight: '💡',
  linkedin: '💼',
  custom: '🔧'
};

export const TrainingProviderIntegration: React.FC<TrainingProviderIntegrationProps> = ({
  providers = [],
  onAddProvider,
  onUpdateProvider,
  onDeleteProvider,
  onSyncProvider,
  onImportCourse,
  showToast,
  loading = false
}) => {
  const [localProviders, setLocalProviders] = useState<TrainingProvider[]>(providers);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [syncingId, setSyncingId] = useState<string | null>(null);
  const [selectedProvider, setSelectedProvider] = useState<TrainingProvider | null>(null);
  const [courses, setCourses] = useState<Record<string, ProviderCourse[]>>({});
  const [syncHistory, setSyncHistory] = useState<Record<string, ProviderSync[]>>({});
  
  // Form state
  const [formData, setFormData] = useState({
    name: '',
    type: 'custom' as TrainingProvider['type'],
    apiEndpoint: '',
    credentials: {
      apiKey: '',
      clientId: '',
      clientSecret: ''
    },
    syncEnabled: true,
    settings: {
      autoImportPurchases: true,
      trackProgress: true,
      importCategories: true,
      defaultCategory: 'Online Courses',
      defaultDepartment: ''
    }
  });

  useEffect(() => {
    setLocalProviders(providers);
  }, [providers]);

  // Handle provider creation
  const handleCreate = async () => {
    if (!formData.name || !formData.type) {
      if (showToast) {
        showToast('Please fill in all required fields', 'error');
      }
      return;
    }

    try {
      const newProvider = await onAddProvider?.({
        ...formData,
        lastSync: undefined
      }) || {
        ...formData,
        id: Date.now().toString(),
        lastSync: undefined
      };

      setLocalProviders([...localProviders, newProvider]);
      setShowAddForm(false);
      resetForm();
      
      if (showToast) {
        showToast('Training provider added successfully', 'success');
      }
    } catch (error) {
      if (showToast) {
        showToast('Failed to add training provider', 'error');
      }
    }
  };

  // Handle provider update
  const handleUpdate = async (id: string) => {
    try {
      await onUpdateProvider?.(id, formData);
      
      setLocalProviders(localProviders.map(p => 
        p.id === id ? { ...p, ...formData } : p
      ));
      
      setEditingId(null);
      resetForm();
      
      if (showToast) {
        showToast('Provider updated successfully', 'success');
      }
    } catch (error) {
      if (showToast) {
        showToast('Failed to update provider', 'error');
      }
    }
  };

  // Handle provider deletion
  const handleDelete = async (id: string) => {
    try {
      await onDeleteProvider?.(id);
      setLocalProviders(localProviders.filter(p => p.id !== id));
      
      if (showToast) {
        showToast('Provider removed successfully', 'success');
      }
    } catch (error) {
      if (showToast) {
        showToast('Failed to remove provider', 'error');
      }
    }
  };

  // Handle provider sync
  const handleSync = async (id: string) => {
    setSyncingId(id);
    
    try {
      const sync = await onSyncProvider?.(id) || {
        id: Date.now().toString(),
        providerId: id,
        startedAt: new Date().toISOString(),
        completedAt: new Date().toISOString(),
        status: 'completed' as const,
        coursesFound: 5,
        coursesImported: 3,
        expensesCreated: 3,
        errors: []
      };

      // Update sync history
      setSyncHistory({
        ...syncHistory,
        [id]: [...(syncHistory[id] || []), sync]
      });

      // Real implementation would fetch courses from provider API
      // For now, return empty array until API integration is complete
      const fetchedCourses: ProviderCourse[] = [];

      setCourses({
        ...courses,
        [id]: fetchedCourses
      });

      // Update provider last sync
      setLocalProviders(localProviders.map(p => 
        p.id === id ? { ...p, lastSync: new Date().toISOString() } : p
      ));

      if (showToast) {
        showToast(`Sync completed: ${sync.coursesImported} courses imported`, 'success');
      }
    } catch (error) {
      if (showToast) {
        showToast('Sync failed', 'error');
      }
    } finally {
      setSyncingId(null);
    }
  };

  // Handle course import
  const handleCourseImport = async (providerId: string, courseId: string) => {
    try {
      await onImportCourse?.(providerId, courseId);
      
      if (showToast) {
        showToast('Course imported as expense', 'success');
      }
    } catch (error) {
      if (showToast) {
        showToast('Failed to import course', 'error');
      }
    }
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      name: '',
      type: 'custom',
      apiEndpoint: '',
      credentials: {
        apiKey: '',
        clientId: '',
        clientSecret: ''
      },
      syncEnabled: true,
      settings: {
        autoImportPurchases: true,
        trackProgress: true,
        importCategories: true,
        defaultCategory: 'Online Courses',
        defaultDepartment: ''
      }
    });
  };

  // Start editing
  const startEdit = (provider: TrainingProvider) => {
    setEditingId(provider.id);
    setFormData({
      name: provider.name,
      type: provider.type,
      apiEndpoint: provider.apiEndpoint || '',
      credentials: {
        apiKey: provider.credentials?.apiKey || '',
        clientId: provider.credentials?.clientId || '',
        clientSecret: provider.credentials?.clientSecret || ''
      },
      syncEnabled: provider.syncEnabled,
      settings: {
        autoImportPurchases: provider.settings?.autoImportPurchases || false,
        trackProgress: provider.settings?.trackProgress || false,
        importCategories: provider.settings?.importCategories || false,
        defaultCategory: provider.settings?.defaultCategory || '',
        defaultDepartment: provider.settings?.defaultDepartment || ''
      }
    });
  };

  // Get provider display info
  const getProviderInfo = (type: TrainingProvider['type']) => {
    const info = {
      udemy: {
        name: 'Udemy',
        description: 'Connect to Udemy Business account',
        apiNote: 'Requires Business API access'
      },
      coursera: {
        name: 'Coursera',
        description: 'Sync Coursera for Business courses',
        apiNote: 'Enterprise API required'
      },
      pluralsight: {
        name: 'Pluralsight',
        description: 'Import Pluralsight Skills data',
        apiNote: 'Skills API access needed'
      },
      linkedin: {
        name: 'LinkedIn Learning',
        description: 'Track LinkedIn Learning progress',
        apiNote: 'Enterprise integration'
      },
      custom: {
        name: 'Custom Provider',
        description: 'Connect any training platform',
        apiNote: 'Custom API configuration'
      }
    };
    return info[type];
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-card rounded-lg p-6 border"
      >
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <GraduationCap className="h-5 w-5 text-muted-foreground" />
            <h3 className="text-lg font-semibold">Training Provider Integration</h3>
          </div>
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => setShowAddForm(true)}
            className="flex items-center gap-2 px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
          >
            <Plus className="h-4 w-4" />
            Add Provider
          </motion.button>
        </div>

        {/* Info Box */}
        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 flex items-start gap-3">
          <Shield className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5" />
          <div className="text-sm text-blue-700 dark:text-blue-300">
            <p className="font-medium mb-1">Automated Course Tracking</p>
            <p className="text-xs">
              Connect your training platforms to automatically import course purchases, track progress, 
              and generate expense reports. Supports major platforms and custom integrations.
            </p>
          </div>
        </div>
      </motion.div>

      {/* Add/Edit Form */}
      <AnimatePresence>
        {(showAddForm || editingId) && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="bg-card rounded-lg p-6 border"
          >
            <h4 className="text-md font-semibold mb-4">
              {editingId ? 'Edit Provider' : 'Add Training Provider'}
            </h4>

            <div className="space-y-4">
              {/* Provider Type Selection */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">
                    Provider Type <span className="text-red-500">*</span>
                  </label>
                  <select
                    value={formData.type}
                    onChange={(e) => setFormData({ 
                      ...formData, 
                      type: e.target.value as TrainingProvider['type'] 
                    })}
                    className="w-full px-3 py-2 border rounded-md bg-background"
                  >
                    <option value="udemy">Udemy</option>
                    <option value="coursera">Coursera</option>
                    <option value="pluralsight">Pluralsight</option>
                    <option value="linkedin">LinkedIn Learning</option>
                    <option value="custom">Custom Provider</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">
                    Provider Name <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    placeholder={getProviderInfo(formData.type).name}
                    className="w-full px-3 py-2 border rounded-md bg-background"
                  />
                </div>
              </div>

              {/* API Configuration */}
              <div>
                <h5 className="text-sm font-medium mb-2">API Configuration</h5>
                <p className="text-xs text-muted-foreground mb-3">
                  {getProviderInfo(formData.type).apiNote}
                </p>
                
                {formData.type === 'custom' && (
                  <div className="mb-3">
                    <label className="block text-sm font-medium mb-1">API Endpoint</label>
                    <input
                      type="url"
                      value={formData.apiEndpoint}
                      onChange={(e) => setFormData({ ...formData, apiEndpoint: e.target.value })}
                      placeholder="https://api.example.com/v1"
                      className="w-full px-3 py-2 border rounded-md bg-background"
                    />
                  </div>
                )}

                <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                  <div>
                    <label className="block text-sm font-medium mb-1">API Key</label>
                    <input
                      type="password"
                      value={formData.credentials.apiKey}
                      onChange={(e) => setFormData({ 
                        ...formData, 
                        credentials: { ...formData.credentials, apiKey: e.target.value }
                      })}
                      placeholder="••••••••••••"
                      className="w-full px-3 py-2 border rounded-md bg-background"
                    />
                  </div>
                  {(formData.type === 'coursera' || formData.type === 'custom') && (
                    <>
                      <div>
                        <label className="block text-sm font-medium mb-1">Client ID</label>
                        <input
                          type="text"
                          value={formData.credentials.clientId}
                          onChange={(e) => setFormData({ 
                            ...formData, 
                            credentials: { ...formData.credentials, clientId: e.target.value }
                          })}
                          placeholder="client_id"
                          className="w-full px-3 py-2 border rounded-md bg-background"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium mb-1">Client Secret</label>
                        <input
                          type="password"
                          value={formData.credentials.clientSecret}
                          onChange={(e) => setFormData({ 
                            ...formData, 
                            credentials: { ...formData.credentials, clientSecret: e.target.value }
                          })}
                          placeholder="••••••••••••"
                          className="w-full px-3 py-2 border rounded-md bg-background"
                        />
                      </div>
                    </>
                  )}
                </div>
              </div>

              {/* Sync Settings */}
              <div>
                <h5 className="text-sm font-medium mb-2">Sync Settings</h5>
                <div className="space-y-2">
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={formData.syncEnabled}
                      onChange={(e) => setFormData({ ...formData, syncEnabled: e.target.checked })}
                    />
                    <span className="text-sm">Enable automatic sync</span>
                  </label>
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={formData.settings.autoImportPurchases}
                      onChange={(e) => setFormData({ 
                        ...formData, 
                        settings: { ...formData.settings, autoImportPurchases: e.target.checked }
                      })}
                    />
                    <span className="text-sm">Auto-import new purchases</span>
                  </label>
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={formData.settings.trackProgress}
                      onChange={(e) => setFormData({ 
                        ...formData, 
                        settings: { ...formData.settings, trackProgress: e.target.checked }
                      })}
                    />
                    <span className="text-sm">Track course progress</span>
                  </label>
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={formData.settings.importCategories}
                      onChange={(e) => setFormData({ 
                        ...formData, 
                        settings: { ...formData.settings, importCategories: e.target.checked }
                      })}
                    />
                    <span className="text-sm">Import course categories</span>
                  </label>
                </div>
              </div>

              {/* Default Settings */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">Default Category</label>
                  <input
                    type="text"
                    value={formData.settings.defaultCategory}
                    onChange={(e) => setFormData({ 
                      ...formData, 
                      settings: { ...formData.settings, defaultCategory: e.target.value }
                    })}
                    placeholder="Online Courses"
                    className="w-full px-3 py-2 border rounded-md bg-background"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">Default Department</label>
                  <input
                    type="text"
                    value={formData.settings.defaultDepartment}
                    onChange={(e) => setFormData({ 
                      ...formData, 
                      settings: { ...formData.settings, defaultDepartment: e.target.value }
                    })}
                    placeholder="Engineering"
                    className="w-full px-3 py-2 border rounded-md bg-background"
                  />
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex justify-end gap-2 pt-4">
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => {
                    setShowAddForm(false);
                    setEditingId(null);
                    resetForm();
                  }}
                  className="px-4 py-2 text-sm border rounded-md hover:bg-muted transition-colors"
                >
                  Cancel
                </motion.button>
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => editingId ? handleUpdate(editingId) : handleCreate()}
                  className="flex items-center gap-2 px-4 py-2 text-sm bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
                >
                  <Save className="h-4 w-4" />
                  {editingId ? 'Update' : 'Add'} Provider
                </motion.button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Providers List */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="bg-card rounded-lg p-6 border"
      >
        {localProviders.length === 0 ? (
          <div className="text-center py-12 text-muted-foreground">
            <GraduationCap className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p className="text-sm">No training providers connected</p>
            <p className="text-xs mt-1">Add a provider to start syncing courses</p>
          </div>
        ) : (
          <div className="space-y-4">
            {localProviders.map((provider) => (
              <motion.div
                key={provider.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                className="border rounded-lg p-4"
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center gap-3">
                    <span className="text-2xl">{providerLogos[provider.type]}</span>
                    <div>
                      <h4 className="font-medium">{provider.name}</h4>
                      <p className="text-xs text-muted-foreground">
                        {getProviderInfo(provider.type).description}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => handleSync(provider.id)}
                      disabled={syncingId === provider.id}
                      className="p-2 hover:bg-muted rounded transition-colors"
                    >
                      {syncingId === provider.id ? (
                        <RefreshCw className="h-4 w-4 animate-spin" />
                      ) : (
                        <RefreshCw className="h-4 w-4" />
                      )}
                    </motion.button>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => startEdit(provider)}
                      className="p-2 hover:bg-muted rounded transition-colors"
                    >
                      <Edit2 className="h-4 w-4" />
                    </motion.button>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => handleDelete(provider.id)}
                      className="p-2 text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded transition-colors"
                    >
                      <Trash2 className="h-4 w-4" />
                    </motion.button>
                  </div>
                </div>

                {/* Status and Last Sync */}
                <div className="flex items-center gap-4 text-xs text-muted-foreground mb-3">
                  <div className="flex items-center gap-1">
                    {provider.syncEnabled ? (
                      <Link className="h-3 w-3 text-green-600" />
                    ) : (
                      <Unlink className="h-3 w-3 text-gray-400" />
                    )}
                    <span>{provider.syncEnabled ? 'Connected' : 'Disconnected'}</span>
                  </div>
                  {provider.lastSync && (
                    <div className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      <span>Last sync: {format(new Date(provider.lastSync), 'MMM d, h:mm a')}</span>
                    </div>
                  )}
                </div>

                {/* Course Summary */}
                {courses[provider.id] && courses[provider.id].length > 0 && (
                  <div className="bg-muted/50 rounded-lg p-3 mb-3">
                    <div className="flex items-center justify-between mb-2">
                      <h5 className="text-sm font-medium">Synced Courses</h5>
                      <span className="text-xs text-muted-foreground">
                        {courses[provider.id].length} courses
                      </span>
                    </div>
                    <div className="space-y-2">
                      {courses[provider.id].slice(0, 3).map((course) => (
                        <div key={course.id} className="flex items-center justify-between text-xs">
                          <div className="flex-1">
                            <p className="font-medium">{course.title}</p>
                            <div className="flex items-center gap-3 text-muted-foreground">
                              <span>{course.instructor}</span>
                              <span>•</span>
                              <span>{course.duration}h</span>
                              {course.progress !== undefined && (
                                <>
                                  <span>•</span>
                                  <span>{course.progress}% complete</span>
                                </>
                              )}
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <span className="font-medium">${course.price}</span>
                            {course.completionDate && (
                              <Award className="h-3 w-3 text-green-600" />
                            )}
                            <motion.button
                              whileHover={{ scale: 1.05 }}
                              whileTap={{ scale: 0.95 }}
                              onClick={() => handleCourseImport(provider.id, course.id)}
                              className="p-1 hover:bg-muted rounded transition-colors"
                              title="Import as expense"
                            >
                              <Download className="h-3 w-3" />
                            </motion.button>
                          </div>
                        </div>
                      ))}
                    </div>
                    {courses[provider.id].length > 3 && (
                      <button
                        onClick={() => setSelectedProvider(provider)}
                        className="text-xs text-primary hover:underline mt-2"
                      >
                        View all courses →
                      </button>
                    )}
                  </div>
                )}

                {/* Sync History */}
                {syncHistory[provider.id] && syncHistory[provider.id].length > 0 && (
                  <div className="text-xs text-muted-foreground">
                    <div className="flex items-center gap-2">
                      <TrendingUp className="h-3 w-3" />
                      <span>
                        Last sync: {syncHistory[provider.id][syncHistory[provider.id].length - 1].coursesImported} courses imported
                      </span>
                    </div>
                  </div>
                )}
              </motion.div>
            ))}
          </div>
        )}
      </motion.div>

      {/* Provider Documentation */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="bg-muted/50 rounded-lg p-4"
      >
        <div className="flex items-center gap-3 mb-3">
          <Key className="h-5 w-5 text-muted-foreground" />
          <h4 className="text-sm font-medium">Integration Guide</h4>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-xs">
          <div>
            <p className="font-medium mb-1">Supported Features:</p>
            <ul className="space-y-1 text-muted-foreground">
              <li>• Automatic purchase detection</li>
              <li>• Progress tracking and completion certificates</li>
              <li>• Expense categorization by course type</li>
              <li>• Bulk import with duplicate detection</li>
            </ul>
          </div>
          <div>
            <p className="font-medium mb-1">API Requirements:</p>
            <ul className="space-y-1 text-muted-foreground">
              <li>• Business/Enterprise account required</li>
              <li>• API credentials from provider dashboard</li>
              <li>• Read access to user enrollments</li>
              <li>• Webhook support for real-time updates</li>
            </ul>
          </div>
        </div>
      </motion.div>
    </div>
  );
};