import { describe, it, expect } from 'vitest';

/**
 * Phase 4 Integration Test Suite
 * 
 * This file runs all unit tests for Phase 4 components:
 * - CSVImport: Excel/CSV import functionality
 * - APIEndpoints: API documentation and key management
 * - WebhookManager: Webhook configuration and testing
 * - TrainingProviderIntegration: Training platform sync
 */

describe('Phase 4 - Integration & Testing Components', () => {
  it('should have CSVImport tests', async () => {
    const tests = await import('./CSVImport.test');
    expect(tests).toBeDefined();
  });

  it('should have APIEndpoints tests', async () => {
    const tests = await import('./APIEndpoints.test');
    expect(tests).toBeDefined();
  });

  it('should have WebhookManager tests', async () => {
    const tests = await import('./WebhookManager.test');
    expect(tests).toBeDefined();
  });

  it('should have TrainingProviderIntegration tests', async () => {
    const tests = await import('./TrainingProviderIntegration.test');
    expect(tests).toBeDefined();
  });

  it('should export all required components', async () => {
    const components = await import('./index');
    
    expect(components.CSVImport).toBeDefined();
    expect(components.APIEndpoints).toBeDefined();
    expect(components.WebhookManager).toBeDefined();
    expect(components.TrainingProviderIntegration).toBeDefined();
  });

  it('should export all required types', async () => {
    const types = await import('./types');
    
    // Check main types exist
    expect(types).toHaveProperty('ImportedExpense');
    expect(types).toHaveProperty('ImportedBudget');
    expect(types).toHaveProperty('APIEndpoint');
    expect(types).toHaveProperty('WebhookConfig');
    expect(types).toHaveProperty('TrainingProvider');
  });
});