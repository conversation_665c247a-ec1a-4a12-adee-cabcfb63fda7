import React, { useState, useCallback, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Upload, FileSpreadsheet, AlertCircle, CheckCircle, X, Download } from 'lucide-react';
import { useDropzone } from 'react-dropzone';
import { format } from 'date-fns';
import Papa from 'papaparse';

// Types
interface ImportedExpense {
  date: string;
  description: string;
  amount: number;
  category: string;
  department?: string;
  status?: string;
  notes?: string;
}

interface ImportedBudget {
  year: number;
  totalAmount: number;
  quarterlyAllocations?: Array<{
    quarter: number;
    percentage: number;
  }>;
  departmentAllocations?: Array<{
    name: string;
    amount: number;
  }>;
  categoryLimits?: Array<{
    category: string;
    limit: number;
  }>;
}

interface ValidationError {
  row: number;
  field: string;
  message: string;
}

interface ImportPreview {
  type: 'expenses' | 'budget';
  data: ImportedExpense[] | ImportedBudget;
  validationErrors: ValidationError[];
  duplicates: number[];
  totalRows: number;
  validRows: number;
}

interface CSVImportProps {
  onImportExpenses?: (expenses: ImportedExpense[]) => Promise<void>;
  onImportBudget?: (budget: ImportedBudget) => Promise<void>;
  existingExpenses?: any[];
  currentYear: number;
  loading?: boolean;
  showToast?: (message: string, type: 'default' | 'success' | 'error') => void;
}

export const CSVImport: React.FC<CSVImportProps> = ({
  onImportExpenses,
  onImportBudget,
  existingExpenses = [],
  currentYear,
  loading = false,
  showToast
}) => {
  const [importing, setImporting] = useState(false);
  const [preview, setPreview] = useState<ImportPreview | null>(null);
  const [showPreview, setShowPreview] = useState(false);
  const [importType, setImportType] = useState<'expenses' | 'budget'>('expenses');
  const [columnMapping, setColumnMapping] = useState<Record<string, string>>({});
  const [csvHeaders, setCsvHeaders] = useState<string[]>([]);
  const [parsedData, setParsedData] = useState<any[] | null>(null);

  // CSV templates with headers only
  const expenseTemplate = `Date,Description,Amount,Category,Department,Status,Notes`;

  const budgetTemplate = `Year,Total Budget,Q1%,Q2%,Q3%,Q4%

Department,Amount

Category,Limit`;

  // Re-validate when column mapping changes
  useEffect(() => {
    if (parsedData && parsedData.length > 0) {
      if (importType === 'expenses') {
        const { valid, errors } = validateExpenses(parsedData, columnMapping);
        const duplicates = checkDuplicates(valid);
        
        setPreview({
          type: 'expenses',
          data: valid,
          validationErrors: errors,
          duplicates,
          totalRows: parsedData.length,
          validRows: valid.length
        });
      } else {
        // For budget, we don't need to re-validate on column mapping since it's not applicable
      }
    }
  }, [columnMapping, parsedData, importType]);

  // Validate expense data
  const validateExpenses = (data: any[], mapping: Record<string, string> = columnMapping): { valid: ImportedExpense[], errors: ValidationError[] } => {
    const valid: ImportedExpense[] = [];
    const errors: ValidationError[] = [];

    data.forEach((row, index) => {
      const rowNum = index + 2; // Account for header row
      
      // Apply column mapping
      const mappedRow: any = {};
      Object.keys(mapping).forEach(key => {
        mappedRow[key] = row[mapping[key]];
      });
      
      // For unmapped columns, use the original row values
      Object.keys(row).forEach(key => {
        if (!Object.values(mapping).includes(key)) {
          mappedRow[key] = row[key];
        }
      });
      
      const expense: ImportedExpense = {
        date: '',
        description: '',
        amount: 0,
        category: '',
        department: mappedRow['Department'] || row.Department || '',
        status: mappedRow['Status'] || row.Status || 'submitted',
        notes: mappedRow['Notes'] || row.Notes || ''
      };

      // Validate date
      try {
        const dateValue = mappedRow['Date'] || row.Date;
        if (!dateValue) {
          errors.push({ row: rowNum, field: 'Date', message: 'Date is required' });
        } else {
          const parsedDate = new Date(dateValue);
          if (isNaN(parsedDate.getTime())) {
            errors.push({ row: rowNum, field: 'Date', message: 'Invalid date format' });
          } else {
            expense.date = parsedDate.toISOString();
          }
        }
      } catch (e) {
        errors.push({ row: rowNum, field: 'Date', message: 'Date parsing error' });
      }

      // Validate description
      const descriptionValue = mappedRow['Description'] || row.Description;
      if (!descriptionValue || descriptionValue.trim() === '') {
        errors.push({ row: rowNum, field: 'Description', message: 'Description is required' });
      } else {
        expense.description = descriptionValue.trim();
      }

      // Validate amount
      const amountValue = mappedRow['Amount'] || row.Amount;
      const amount = parseFloat(amountValue);
      if (isNaN(amount) || amount <= 0) {
        errors.push({ row: rowNum, field: 'Amount', message: 'Amount must be a positive number' });
      } else {
        expense.amount = amount;
      }

      // Validate category
      const categoryValue = mappedRow['Category'] || row.Category;
      if (!categoryValue || categoryValue.trim() === '') {
        errors.push({ row: rowNum, field: 'Category', message: 'Category is required' });
      } else {
        expense.category = categoryValue.trim();
      }

      // Validate status if provided
      const statusValue = mappedRow['Status'] || row.Status;
      if (statusValue) {
        const validStatuses = ['draft', 'submitted', 'paid', 'reimbursed', 'approved', 'rejected'];
        if (!validStatuses.includes(statusValue.toLowerCase())) {
          errors.push({ row: rowNum, field: 'Status', message: `Invalid status. Must be one of: ${validStatuses.join(', ')}` });
        } else {
          expense.status = statusValue.toLowerCase();
        }
      }

      if (errors.filter(e => e.row === rowNum).length === 0) {
        valid.push(expense);
      }
    });

    return { valid, errors };
  };

  // Validate budget data
  const validateBudget = (data: any[]): { valid: ImportedBudget | null, errors: ValidationError[] } => {
    const errors: ValidationError[] = [];
    const budget: ImportedBudget = {
      year: currentYear,
      totalAmount: 0
    };

    // Parse main budget info
    if (data.length > 0) {
      const mainRow = data[0];
      
      // Validate year
      const year = parseInt(mainRow.Year);
      if (!isNaN(year) && year >= 2020 && year <= 2030) {
        budget.year = year;
      } else {
        errors.push({ row: 1, field: 'Year', message: 'Invalid year' });
      }

      // Validate total budget
      const totalBudget = parseFloat(mainRow['Total Budget']);
      if (!isNaN(totalBudget) && totalBudget > 0) {
        budget.totalAmount = totalBudget;
      } else {
        errors.push({ row: 1, field: 'Total Budget', message: 'Total budget must be positive' });
      }

      // Parse quarterly allocations
      const quarters: any[] = [];
      let totalPercentage = 0;
      for (let q = 1; q <= 4; q++) {
        const percentage = parseFloat(mainRow[`Q${q}%`]);
        if (!isNaN(percentage) && percentage >= 0) {
          quarters.push({ quarter: q, percentage });
          totalPercentage += percentage;
        }
      }

      if (quarters.length === 4) {
        if (Math.abs(totalPercentage - 100) > 0.01) {
          errors.push({ row: 1, field: 'Quarterly Allocations', message: 'Quarterly percentages must sum to 100%' });
        } else {
          budget.quarterlyAllocations = quarters;
        }
      }
    }

    // Parse department allocations
    const deptStartIndex = data.findIndex(row => row.Department && row.Amount);
    if (deptStartIndex !== -1) {
      const departments: any[] = [];
      let totalDeptAmount = 0;

      for (let i = deptStartIndex; i < data.length; i++) {
        const row = data[i];
        if (row.Department && row.Amount) {
          const amount = parseFloat(row.Amount);
          if (!isNaN(amount) && amount >= 0) {
            departments.push({ name: row.Department, amount });
            totalDeptAmount += amount;
          } else {
            errors.push({ row: i + 2, field: 'Amount', message: 'Department amount must be non-negative' });
          }
        } else if (row.Category && row.Limit) {
          break; // Reached category limits section
        }
      }

      if (departments.length > 0) {
        if (totalDeptAmount > budget.totalAmount) {
          errors.push({ row: deptStartIndex + 2, field: 'Department Allocations', message: 'Total department allocations exceed budget' });
        } else {
          budget.departmentAllocations = departments;
        }
      }
    }

    // Parse category limits
    const catStartIndex = data.findIndex(row => row.Category && row.Limit);
    if (catStartIndex !== -1) {
      const categories: any[] = [];
      let totalCatLimit = 0;

      for (let i = catStartIndex; i < data.length; i++) {
        const row = data[i];
        if (row.Category && row.Limit) {
          const limit = parseFloat(row.Limit);
          if (!isNaN(limit) && limit >= 0) {
            categories.push({ category: row.Category, limit });
            totalCatLimit += limit;
          } else {
            errors.push({ row: i + 2, field: 'Limit', message: 'Category limit must be non-negative' });
          }
        }
      }

      if (categories.length > 0) {
        budget.categoryLimits = categories;
      }
    }

    return { valid: errors.length === 0 ? budget : null, errors };
  };

  // Check for duplicate expenses
  const checkDuplicates = (expenses: ImportedExpense[]): number[] => {
    const duplicates: number[] = [];
    
    expenses.forEach((expense, index) => {
      const isDuplicate = existingExpenses.some(existing => {
        return (
          existing.description === expense.description &&
          existing.amount === expense.amount &&
          format(new Date(existing.date), 'yyyy-MM-dd') === format(new Date(expense.date), 'yyyy-MM-dd')
        );
      });

      if (isDuplicate) {
        duplicates.push(index);
      }
    });

    return duplicates;
  };

  // Parse CSV file
  const parseFile = (file: File) => {
    Papa.parse(file, {
      header: true,
      skipEmptyLines: true,
      complete: (results) => {
        if (results.errors.length > 0) {
          if (showToast) {
            showToast(`CSV Parsing Error: ${results.errors[0].message}`, 'error');
          }
          return;
        }

        // Store headers for column mapping
        if (results.meta && results.meta.fields) {
          setCsvHeaders(results.meta.fields);
          setParsedData(results.data);
          // Initialize column mapping with potential matches
          const initialMapping: Record<string, string> = {};
          const standardFields = ['Date', 'Description', 'Amount', 'Category', 'Department', 'Status', 'Notes'];
          
          results.meta.fields.forEach(header => {
            const normalizedHeader = header.toLowerCase().replace(/\s+/g, '');
            if (normalizedHeader.includes('date') || normalizedHeader.includes('day')) {
              initialMapping['Date'] = header;
            } else if (normalizedHeader.includes('description') || normalizedHeader.includes('desc')) {
              initialMapping['Description'] = header;
            } else if (normalizedHeader.includes('amount') || normalizedHeader.includes('cost') || normalizedHeader.includes('price')) {
              initialMapping['Amount'] = header;
            } else if (normalizedHeader.includes('category')) {
              initialMapping['Category'] = header;
            } else if (normalizedHeader.includes('department') || normalizedHeader.includes('dept')) {
              initialMapping['Department'] = header;
            } else if (normalizedHeader.includes('status')) {
              initialMapping['Status'] = header;
            } else if (normalizedHeader.includes('note')) {
              initialMapping['Notes'] = header;
            }
          });
          
          setColumnMapping(initialMapping);
        }

        if (importType === 'expenses') {
          const { valid, errors } = validateExpenses(results.data, columnMapping);
          const duplicates = checkDuplicates(valid);
          
          setPreview({
            type: 'expenses',
            data: valid,
            validationErrors: errors,
            duplicates,
            totalRows: results.data.length,
            validRows: valid.length
          });
        } else {
          const { valid, errors } = validateBudget(results.data);
          
          setPreview({
            type: 'budget',
            data: valid || { year: currentYear, totalAmount: 0 },
            validationErrors: errors,
            duplicates: [],
            totalRows: results.data.length,
            validRows: valid ? 1 : 0
          });
        }

        setShowPreview(true);
      }
    });
  };

  // Handle file drop
  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles.length > 0) {
      parseFile(acceptedFiles[0]);
    }
  }, [importType]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/csv': ['.csv'],
      'application/vnd.ms-excel': ['.xls'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx']
    },
    maxFiles: 1
  });

  // Download template
  const downloadTemplate = () => {
    const template = importType === 'expenses' ? expenseTemplate : budgetTemplate;
    const blob = new Blob([template], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${importType}-template.csv`;
    a.click();
    URL.revokeObjectURL(url);
  };

  // Handle import
  const handleImport = async () => {
    if (!preview || preview.validRows === 0) return;

    setImporting(true);
    try {
      if (preview.type === 'expenses' && onImportExpenses) {
        const expenses = preview.data as ImportedExpense[];
        const nonDuplicates = expenses.filter((_, index) => !preview.duplicates.includes(index));
        await onImportExpenses(nonDuplicates);
        if (showToast) {
          showToast(`Successfully imported ${nonDuplicates.length} expenses`, 'success');
        }
      } else if (preview.type === 'budget' && onImportBudget) {
        await onImportBudget(preview.data as ImportedBudget);
        if (showToast) {
          showToast('Budget configuration imported successfully', 'success');
        }
      }
      
      setShowPreview(false);
      setPreview(null);
    } catch (error) {
      if (showToast) {
        showToast(
          error instanceof Error ? error.message : 'Import failed - Unknown error occurred',
          'error'
        );
      }
    } finally {
      setImporting(false);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-card rounded-lg p-6 border"
    >
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <FileSpreadsheet className="h-5 w-5 text-muted-foreground" />
          <h3 className="text-lg font-semibold">Import Data</h3>
        </div>
        <div className="flex items-center gap-2">
          <select
            value={importType}
            onChange={(e) => setImportType(e.target.value as 'expenses' | 'budget')}
            className="px-3 py-1.5 text-sm border rounded-md bg-background"
          >
            <option value="expenses">Expenses</option>
            <option value="budget">Budget Configuration</option>
          </select>
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={downloadTemplate}
            className="flex items-center gap-2 px-3 py-1.5 text-sm border rounded-md hover:bg-muted transition-colors"
          >
            <Download className="h-4 w-4" />
            Download Template
          </motion.button>
        </div>
      </div>

      {!showPreview ? (
        <div>
          <div
            {...getRootProps()}
            className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
              isDragActive ? 'border-primary bg-primary/5' : 'border-muted-foreground/25 hover:border-primary/50'
            }`}
          >
            <input {...getInputProps()} />
            <Upload className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <p className="text-sm font-medium mb-1">
              {isDragActive ? 'Drop the file here' : 'Drag & drop a CSV file here'}
            </p>
            <p className="text-xs text-muted-foreground">
              or click to select a file
            </p>
            <p className="text-xs text-muted-foreground mt-2">
              Supports CSV, XLS, and XLSX formats
            </p>
          </div>

          {/* Column Mapping Interface */}
          {csvHeaders.length > 0 && (
            <div className="mt-6 bg-muted/30 rounded-lg p-4">
              <h4 className="text-sm font-medium mb-3">Column Mapping</h4>
              <p className="text-xs text-muted-foreground mb-3">
                Map your CSV columns to the expected fields. We've tried to auto-map based on column names.
              </p>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                {['Date', 'Description', 'Amount', 'Category', 'Department', 'Status', 'Notes'].map((field) => (
                  <div key={field} className="space-y-1">
                    <label className="text-xs font-medium">{field}</label>
                    <select
                      value={columnMapping[field] || ''}
                      onChange={(e) => setColumnMapping(prev => ({
                        ...prev,
                        [field]: e.target.value
                      }))}
                      className="w-full text-xs border rounded px-2 py-1 bg-background"
                    >
                      <option value="">Select column</option>
                      {csvHeaders.map((header) => (
                        <option key={header} value={header}>{header}</option>
                      ))}
                    </select>
                  </div>
                ))}
              </div>
              <div className="mt-3 pt-3 border-t flex justify-end">
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => {
                    // Reset to auto-mapping
                    const initialMapping: Record<string, string> = {};
                    const standardFields = ['Date', 'Description', 'Amount', 'Category', 'Department', 'Status', 'Notes'];
                    
                    csvHeaders.forEach(header => {
                      const normalizedHeader = header.toLowerCase().replace(/\s+/g, '');
                      if (normalizedHeader.includes('date') || normalizedHeader.includes('day')) {
                        initialMapping['Date'] = header;
                      } else if (normalizedHeader.includes('description') || normalizedHeader.includes('desc')) {
                        initialMapping['Description'] = header;
                      } else if (normalizedHeader.includes('amount') || normalizedHeader.includes('cost') || normalizedHeader.includes('price')) {
                        initialMapping['Amount'] = header;
                      } else if (normalizedHeader.includes('category')) {
                        initialMapping['Category'] = header;
                      } else if (normalizedHeader.includes('department') || normalizedHeader.includes('dept')) {
                        initialMapping['Department'] = header;
                      } else if (normalizedHeader.includes('status')) {
                        initialMapping['Status'] = header;
                      } else if (normalizedHeader.includes('note')) {
                        initialMapping['Notes'] = header;
                      }
                    });
                    
                    setColumnMapping(initialMapping);
                  }}
                  className="text-xs text-primary hover:underline"
                >
                  Reset to Auto-Mapping
                </motion.button>
              </div>
            </div>
          )}
        </div>
      ) : preview ? (
        <div className="space-y-4">
          {/* Preview Summary */}
          <div className="bg-muted/50 rounded-lg p-4">
            <div className="grid grid-cols-3 gap-4 text-sm">
              <div>
                <p className="text-muted-foreground">Total Rows</p>
                <p className="font-semibold">{preview.totalRows}</p>
              </div>
              <div>
                <p className="text-muted-foreground">Valid Rows</p>
                <p className="font-semibold text-green-600">{preview.validRows}</p>
              </div>
              <div>
                <p className="text-muted-foreground">Errors</p>
                <p className="font-semibold text-red-600">{preview.validationErrors.length}</p>
              </div>
            </div>
            {preview.duplicates.length > 0 && (
              <div className="mt-3 pt-3 border-t">
                <p className="text-sm text-orange-600">
                  <AlertCircle className="h-4 w-4 inline mr-1" />
                  {preview.duplicates.length} duplicate {preview.duplicates.length === 1 ? 'entry' : 'entries'} found
                </p>
              </div>
            )}
          </div>

          {/* Validation Errors */}
          {preview.validationErrors.length > 0 && (
            <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-4">
              <h4 className="text-sm font-medium text-red-800 dark:text-red-200 mb-2">
                Validation Errors
              </h4>
              <div className="space-y-1 max-h-32 overflow-y-auto">
                {preview.validationErrors.slice(0, 5).map((error, index) => (
                  <p key={index} className="text-xs text-red-700 dark:text-red-300">
                    Row {error.row}, {error.field}: {error.message}
                  </p>
                ))}
                {preview.validationErrors.length > 5 && (
                  <p className="text-xs text-red-700 dark:text-red-300 italic">
                    ...and {preview.validationErrors.length - 5} more errors
                  </p>
                )}
              </div>
            </div>
          )}

          {/* Data Preview */}
          {preview.validRows > 0 && (
            <div className="bg-muted/30 rounded-lg p-4">
              <h4 className="text-sm font-medium mb-2">Data Preview</h4>
              {preview.type === 'expenses' ? (
                <div className="space-y-1 max-h-48 overflow-y-auto">
                  {(preview.data as ImportedExpense[]).slice(0, 5).map((expense, index) => (
                    <div
                      key={index}
                      className={`text-xs p-2 rounded ${
                        preview.duplicates.includes(index) ? 'bg-orange-100 dark:bg-orange-900/20' : 'bg-background'
                      }`}
                    >
                      <div className="flex justify-between">
                        <span className="font-medium">{expense.description}</span>
                        <span>${expense.amount.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between text-muted-foreground">
                        <span>{format(new Date(expense.date), 'MMM d, yyyy')}</span>
                        <span>{expense.category}</span>
                      </div>
                    </div>
                  ))}
                  {(preview.data as ImportedExpense[]).length > 5 && (
                    <p className="text-xs text-muted-foreground text-center italic">
                      ...and {(preview.data as ImportedExpense[]).length - 5} more expenses
                    </p>
                  )}
                </div>
              ) : (
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Year:</span>
                    <span className="font-medium">{(preview.data as ImportedBudget).year}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Total Budget:</span>
                    <span className="font-medium">${((preview.data as ImportedBudget).totalAmount || 0).toLocaleString()}</span>
                  </div>
                  {(preview.data as ImportedBudget).departmentAllocations && (
                    <div className="flex justify-between">
                      <span>Departments:</span>
                      <span className="font-medium">{(preview.data as ImportedBudget).departmentAllocations?.length || 0}</span>
                    </div>
                  )}
                  {(preview.data as ImportedBudget).categoryLimits && (
                    <div className="flex justify-between">
                      <span>Category Limits:</span>
                      <span className="font-medium">{(preview.data as ImportedBudget).categoryLimits?.length || 0}</span>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end gap-2">
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => {
                setShowPreview(false);
                setPreview(null);
                setParsedData(null);
                setCsvHeaders([]);
                setColumnMapping({});
              }}
              className="px-4 py-2 text-sm border rounded-md hover:bg-muted transition-colors"
            >
              Cancel
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={handleImport}
              disabled={preview.validRows === 0 || importing}
              className="px-4 py-2 text-sm bg-primary text-primary-foreground rounded-md hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center gap-2"
            >
              {importing ? (
                <>
                  <div className="h-4 w-4 border-2 border-primary-foreground/30 border-t-primary-foreground rounded-full animate-spin" />
                  Importing...
                </>
              ) : (
                <>
                  <CheckCircle className="h-4 w-4" />
                  Import {preview.validRows} {preview.type === 'expenses' ? 'Expenses' : 'Configuration'}
                </>
              )}
            </motion.button>
          </div>
        </div>
      ) : (
        <div className="text-center py-8 text-muted-foreground">
          <AlertCircle className="h-12 w-12 mx-auto mb-4" />
          <p>No preview available. Please upload a CSV file.</p>
        </div>
      )}
    </motion.div>
  );
};