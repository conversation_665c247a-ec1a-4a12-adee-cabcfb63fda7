import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Code2, 
  Key, 
  Copy, 
  Eye, 
  EyeOff, 
  Plus, 
  Trash2,
  RefreshCw,
  Shield,
  Clock,
  CheckCircle,
  AlertCircle,
  ExternalLink,
  Terminal
} from 'lucide-react';
import { format } from 'date-fns';
import { APIEndpoint, APIKey } from './types';

interface APIEndpointsProps {
  baseUrl?: string;
  onGenerateKey?: () => Promise<APIKey>;
  onRevokeKey?: (keyId: string) => Promise<void>;
  showToast?: (message: string, type: 'default' | 'success' | 'error') => void;
  loading?: boolean;
}

export const APIEndpoints: React.FC<APIEndpointsProps> = ({
  baseUrl = window.location.origin,
  onGenerateKey,
  onRevokeKey,
  showToast,
  loading = false
}) => {
  const [apiKeys, setApiKeys] = useState<APIKey[]>([]);
  const [showKeys, setShowKeys] = useState<Record<string, boolean>>({});
  const [selectedEndpoint, setSelectedEndpoint] = useState<APIEndpoint | null>(null);
  const [generatingKey, setGeneratingKey] = useState(false);
  const [testingEndpoint, setTestingEndpoint] = useState<string | null>(null);

  // Define available endpoints
  const endpoints: APIEndpoint[] = [
    {
      id: 'get-budget',
      path: '/api/budget/{year}',
      method: 'GET',
      description: 'Get budget information for a specific year',
      authentication: 'bearer',
      parameters: [
        { name: 'year', type: 'path', required: true, description: 'Budget year (e.g., 2024)' }
      ],
      response: {
        type: 'object',
        example: {
          id: 1,
          year: 2024,
          total_amount: 100000,
          spent_amount: 25000,
          remaining_amount: 75000,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-03-15T10:30:00Z'
        }
      }
    },
    {
      id: 'update-budget',
      path: '/api/budget/{year}',
      method: 'PUT',
      description: 'Update budget allocation for a specific year',
      authentication: 'bearer',
      parameters: [
        { name: 'year', type: 'path', required: true, description: 'Budget year' },
        { name: 'total_amount', type: 'body', required: true, description: 'New total budget amount' }
      ],
      response: {
        type: 'object',
        example: { success: true, budget: {} }
      }
    },
    {
      id: 'get-expenses',
      path: '/api/expenses',
      method: 'GET',
      description: 'List all expenses with optional filtering',
      authentication: 'bearer',
      parameters: [
        { name: 'year', type: 'query', required: false, description: 'Filter by year' },
        { name: 'category', type: 'query', required: false, description: 'Filter by category' },
        { name: 'department', type: 'query', required: false, description: 'Filter by department' },
        { name: 'status', type: 'query', required: false, description: 'Filter by status' },
        { name: 'limit', type: 'query', required: false, description: 'Limit results (default: 100)' },
        { name: 'offset', type: 'query', required: false, description: 'Pagination offset' }
      ],
      response: {
        type: 'array',
        example: [
          {
            id: '123',
            description: 'AWS Training Course',
            amount: 299.99,
            category: 'Technical Training',
            department: 'Engineering',
            status: 'approved',
            date: '2024-03-01T00:00:00Z'
          }
        ]
      }
    },
    {
      id: 'create-expense',
      path: '/api/expenses',
      method: 'POST',
      description: 'Create a new expense entry',
      authentication: 'bearer',
      parameters: [
        { name: 'description', type: 'body', required: true, description: 'Expense description' },
        { name: 'amount', type: 'body', required: true, description: 'Expense amount' },
        { name: 'category', type: 'body', required: true, description: 'Expense category' },
        { name: 'department', type: 'body', required: false, description: 'Department' },
        { name: 'date', type: 'body', required: true, description: 'Expense date (ISO 8601)' },
        { name: 'notes', type: 'body', required: false, description: 'Additional notes' }
      ],
      response: {
        type: 'object',
        example: { success: true, expense: {} }
      }
    },
    {
      id: 'get-analytics',
      path: '/api/analytics/{year}',
      method: 'GET',
      description: 'Get budget analytics and insights',
      authentication: 'bearer',
      rateLimit: { requests: 100, period: 'hour' },
      parameters: [
        { name: 'year', type: 'path', required: true, description: 'Analytics year' },
        { name: 'include', type: 'query', required: false, description: 'Include specific data (trends,forecasts,insights)' }
      ],
      response: {
        type: 'object',
        example: {
          trends: { monthly: [], quarterly: [] },
          insights: { topCategories: [], anomalies: [] },
          forecasts: { endOfYear: {} }
        }
      }
    },
    {
      id: 'get-departments',
      path: '/api/departments',
      method: 'GET',
      description: 'List all department allocations',
      authentication: 'bearer',
      parameters: [
        { name: 'year', type: 'query', required: false, description: 'Filter by year' }
      ],
      response: {
        type: 'array',
        example: []
      }
    },
    {
      id: 'export-data',
      path: '/api/export',
      method: 'POST',
      description: 'Export budget data in various formats',
      authentication: 'bearer',
      rateLimit: { requests: 10, period: 'hour' },
      parameters: [
        { name: 'format', type: 'body', required: true, description: 'Export format (csv, json, pdf)' },
        { name: 'type', type: 'body', required: true, description: 'Data type (budget, expenses, analytics)' },
        { name: 'year', type: 'body', required: false, description: 'Year to export' },
        { name: 'filters', type: 'body', required: false, description: 'Additional filters' }
      ],
      response: {
        type: 'object',
        example: { download_url: 'https://api.example.com/downloads/abc123' }
      }
    }
  ];

  // API key generation handler
  const handleGenerateKey = async () => {
    setGeneratingKey(true);
    try {
      if (onGenerateKey) {
        // Use the provided API key generation function
        const generatedKey = await onGenerateKey();
        setApiKeys([...apiKeys, generatedKey]);
      } else {
        // No generation function provided - show error
        showToast('API key generation not configured', 'error');
      }

      if (showToast) {
        showToast('API key generated successfully', 'success');
      }
    } catch (error) {
      if (showToast) {
        showToast('Failed to generate API key', 'error');
      }
    } finally {
      setGeneratingKey(false);
    }
  };

  // Handle key revocation
  const handleRevokeKey = async (keyId: string) => {
    try {
      if (onRevokeKey) {
        await onRevokeKey(keyId);
      }
      
      setApiKeys(apiKeys.filter(key => key.id !== keyId));
      
      if (showToast) {
        showToast('API key revoked successfully', 'success');
      }
    } catch (error) {
      if (showToast) {
        showToast('Failed to revoke API key', 'error');
      }
    }
  };

  // Copy to clipboard
  const copyToClipboard = (text: string, type: string) => {
    navigator.clipboard.writeText(text);
    if (showToast) {
      showToast(`${type} copied to clipboard`, 'success');
    }
  };

  // Generate curl command
  const generateCurlCommand = (endpoint: APIEndpoint, apiKey?: string) => {
    const url = `${baseUrl}${endpoint.path.replace('{year}', '2024')}`;
    const headers = apiKey ? `-H "Authorization: Bearer ${apiKey}"` : '';
    
    if (endpoint.method === 'GET') {
      return `curl -X GET "${url}" ${headers}`;
    } else if (endpoint.method === 'POST' || endpoint.method === 'PUT') {
      const bodyParams = endpoint.parameters?.filter(p => p.type === 'body') || [];
      const requestBody = bodyParams.reduce((acc, param) => {
        acc[param.name] = `<${param.name}>`;
        return acc;
      }, {} as Record<string, any>);
      
      return `curl -X ${endpoint.method} "${url}" ${headers} \\
  -H "Content-Type: application/json" \\
  -d '${JSON.stringify(requestBody, null, 2)}'`;
    }
    
    return `curl -X ${endpoint.method} "${url}" ${headers}`;
  };

  // Test endpoint
  const testEndpoint = async (endpoint: APIEndpoint) => {
    setTestingEndpoint(endpoint.id);
    
    // Simulate API call
    setTimeout(() => {
      if (showToast) {
        showToast(`Endpoint ${endpoint.path} tested successfully`, 'success');
      }
      setTestingEndpoint(null);
    }, 1500);
  };

  return (
    <div className="space-y-6">
      {/* API Keys Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-card rounded-lg p-6 border"
      >
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <Key className="h-5 w-5 text-muted-foreground" />
            <h3 className="text-lg font-semibold">API Keys</h3>
          </div>
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={handleGenerateKey}
            disabled={generatingKey}
            className="flex items-center gap-2 px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 disabled:opacity-50 transition-colors"
          >
            {generatingKey ? (
              <RefreshCw className="h-4 w-4 animate-spin" />
            ) : (
              <Plus className="h-4 w-4" />
            )}
            Generate New Key
          </motion.button>
        </div>

        {apiKeys.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <Key className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p className="text-sm">No API keys generated yet</p>
            <p className="text-xs mt-1">Generate a key to start using the API</p>
          </div>
        ) : (
          <div className="space-y-3">
            {apiKeys.map((apiKey) => (
              <motion.div
                key={apiKey.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                className="bg-muted/30 rounded-lg p-4"
              >
                <div className="flex items-start justify-between mb-2">
                  <div>
                    <h4 className="font-medium">{apiKey.name}</h4>
                    <p className="text-xs text-muted-foreground mt-1">
                      Created {format(new Date(apiKey.createdAt), 'MMM d, yyyy h:mm a')}
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      apiKey.active 
                        ? 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300' 
                        : 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300'
                    }`}>
                      {apiKey.active ? 'Active' : 'Revoked'}
                    </span>
                    {apiKey.active && (
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => handleRevokeKey(apiKey.id)}
                        className="text-red-600 hover:text-red-700 transition-colors"
                      >
                        <Trash2 className="h-4 w-4" />
                      </motion.button>
                    )}
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <code className="flex-1 bg-muted rounded px-3 py-2 text-xs font-mono overflow-hidden">
                    {showKeys[apiKey.id] ? apiKey.key : '••••••••••••••••••••••••••••••••'}
                  </code>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => setShowKeys({ ...showKeys, [apiKey.id]: !showKeys[apiKey.id] })}
                    className="p-2 hover:bg-muted rounded transition-colors"
                  >
                    {showKeys[apiKey.id] ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </motion.button>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => copyToClipboard(apiKey.key, 'API key')}
                    className="p-2 hover:bg-muted rounded transition-colors"
                  >
                    <Copy className="h-4 w-4" />
                  </motion.button>
                </div>

                {apiKey.lastUsed && (
                  <p className="text-xs text-muted-foreground mt-2">
                    Last used: {format(new Date(apiKey.lastUsed), 'MMM d, yyyy h:mm a')}
                  </p>
                )}
              </motion.div>
            ))}
          </div>
        )}
      </motion.div>

      {/* Endpoints Documentation */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="bg-card rounded-lg p-6 border"
      >
        <div className="flex items-center gap-3 mb-6">
          <Code2 className="h-5 w-5 text-muted-foreground" />
          <h3 className="text-lg font-semibold">API Endpoints</h3>
        </div>

        <div className="space-y-3">
          {endpoints.map((endpoint) => (
            <motion.div
              key={endpoint.id}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                selectedEndpoint?.id === endpoint.id ? 'border-primary bg-primary/5' : 'hover:border-muted-foreground/50'
              }`}
              onClick={() => setSelectedEndpoint(endpoint.id === selectedEndpoint?.id ? null : endpoint)}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <span className={`text-xs font-mono px-2 py-1 rounded ${
                    endpoint.method === 'GET' ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300' :
                    endpoint.method === 'POST' ? 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300' :
                    endpoint.method === 'PUT' ? 'bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-300' :
                    'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300'
                  }`}>
                    {endpoint.method}
                  </span>
                  <code className="text-sm font-mono">{endpoint.path}</code>
                </div>
                <div className="flex items-center gap-2">
                  {endpoint.rateLimit && (
                    <div className="flex items-center gap-1 text-xs text-muted-foreground">
                      <Clock className="h-3 w-3" />
                      {endpoint.rateLimit.requests}/{endpoint.rateLimit.period}
                    </div>
                  )}
                  {endpoint.authentication !== 'none' && (
                    <Shield className="h-4 w-4 text-muted-foreground" />
                  )}
                </div>
              </div>
              
              <p className="text-sm text-muted-foreground mt-2">{endpoint.description}</p>

              <AnimatePresence>
                {selectedEndpoint?.id === endpoint.id && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: 'auto', opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    className="mt-4 space-y-4 overflow-hidden"
                  >
                    {/* Parameters */}
                    {endpoint.parameters && endpoint.parameters.length > 0 && (
                      <div>
                        <h4 className="text-sm font-medium mb-2">Parameters</h4>
                        <div className="space-y-1">
                          {endpoint.parameters.map((param) => (
                            <div key={param.name} className="flex items-start gap-2 text-xs">
                              <code className="bg-muted px-2 py-0.5 rounded">
                                {param.name}
                              </code>
                              <span className={`px-2 py-0.5 rounded ${
                                param.required 
                                  ? 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300' 
                                  : 'bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-300'
                              }`}>
                                {param.required ? 'required' : 'optional'}
                              </span>
                              <span className="text-muted-foreground">
                                ({param.type}) - {param.description}
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Response Example */}
                    {endpoint.response && (
                      <div>
                        <h4 className="text-sm font-medium mb-2">Response Example</h4>
                        <pre className="bg-muted rounded p-3 text-xs overflow-x-auto">
                          {JSON.stringify(endpoint.response.example, null, 2)}
                        </pre>
                      </div>
                    )}

                    {/* Curl Example */}
                    <div>
                      <h4 className="text-sm font-medium mb-2">Example Request</h4>
                      <div className="relative">
                        <pre className="bg-muted rounded p-3 text-xs overflow-x-auto pr-12">
                          {generateCurlCommand(endpoint, apiKeys.find(k => k.active)?.key)}
                        </pre>
                        <motion.button
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          onClick={(e) => {
                            e.stopPropagation();
                            copyToClipboard(generateCurlCommand(endpoint, apiKeys.find(k => k.active)?.key), 'Command');
                          }}
                          className="absolute top-2 right-2 p-1.5 hover:bg-muted-foreground/10 rounded transition-colors"
                        >
                          <Copy className="h-3.5 w-3.5" />
                        </motion.button>
                      </div>
                    </div>

                    {/* Test Button */}
                    <div className="flex justify-end">
                      <motion.button
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        onClick={(e) => {
                          e.stopPropagation();
                          testEndpoint(endpoint);
                        }}
                        disabled={testingEndpoint === endpoint.id || !apiKeys.some(k => k.active)}
                        className="flex items-center gap-2 px-3 py-1.5 text-sm bg-primary text-primary-foreground rounded hover:bg-primary/90 disabled:opacity-50 transition-colors"
                      >
                        {testingEndpoint === endpoint.id ? (
                          <>
                            <RefreshCw className="h-3.5 w-3.5 animate-spin" />
                            Testing...
                          </>
                        ) : (
                          <>
                            <Terminal className="h-3.5 w-3.5" />
                            Test Endpoint
                          </>
                        )}
                      </motion.button>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* API Documentation Link */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="bg-muted/50 rounded-lg p-4 flex items-center justify-between"
      >
        <div className="flex items-center gap-3">
          <ExternalLink className="h-5 w-5 text-muted-foreground" />
          <div>
            <p className="text-sm font-medium">Full API Documentation</p>
            <p className="text-xs text-muted-foreground">
              View comprehensive API documentation with examples and SDKs
            </p>
          </div>
        </div>
        <motion.a
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          href="/api/docs"
          target="_blank"
          rel="noopener noreferrer"
          className="px-4 py-2 text-sm border rounded-md hover:bg-muted transition-colors"
        >
          View Docs
        </motion.a>
      </motion.div>
    </div>
  );
};