import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { CSVImport } from './CSVImport';
import type { ImportedExpense, ImportedBudget } from './types';

// Mock file reading
const mockFileContent = `Date,Description,Amount,Category,Department,Status
2024-01-15,Training Materials,500.00,Materials,Engineering,paid
2024-01-20,Online Course,299.99,Courses,Engineering,pending`;

const mockBudgetContent = `Department,Budget Type,Amount,Fiscal Year
Engineering,Training,50000,2024
Marketing,Training,30000,2024`;

// Mock FileReader
global.FileReader = jest.fn().mockImplementation(() => ({
  readAsText: jest.fn(function(this: FileReader) {
    setTimeout(() => {
      if (this.onload) {
        this.onload({ target: { result: mockFileContent } } as any);
      }
    }, 0);
  }),
  result: mockFileContent
})) as any;

describe('CSVImport', () => {
  const mockOnImportExpenses = jest.fn();
  const mockOnImportBudget = jest.fn();
  const mockShowToast = jest.fn();
  
  const defaultProps = {
    onImportExpenses: mockOnImportExpenses,
    onImportBudget: mockOnImportBudget,
    existingExpenses: [],
    currentYear: 2024,
    loading: false,
    showToast: mockShowToast
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders import component with default state', () => {
    render(<CSVImport {...defaultProps} />);
    
    expect(screen.getByText('CSV/Excel Import')).toBeInTheDocument();
    expect(screen.getByText('Expenses')).toBeInTheDocument();
    expect(screen.getByText('Budget Configuration')).toBeInTheDocument();
    expect(screen.getByText('Drag & drop CSV/Excel files here')).toBeInTheDocument();
  });

  it('switches between expense and budget import modes', () => {
    render(<CSVImport {...defaultProps} />);
    
    const budgetTab = screen.getByText('Budget Configuration');
    fireEvent.click(budgetTab);
    
    expect(screen.getByText('Import budget allocations from CSV/Excel files')).toBeInTheDocument();
  });

  it('handles file drop for expenses', async () => {
    render(<CSVImport {...defaultProps} />);
    
    const dropzone = screen.getByText('Drag & drop CSV/Excel files here').parentElement;
    const file = new File([mockFileContent], 'expenses.csv', { type: 'text/csv' });
    
    fireEvent.drop(dropzone!, {
      dataTransfer: {
        files: [file],
        types: ['Files']
      }
    });
    
    await waitFor(() => {
      expect(screen.getByText('Preview (2 rows)')).toBeInTheDocument();
    });
    
    // Check if preview shows parsed data
    expect(screen.getByText('Training Materials')).toBeInTheDocument();
    expect(screen.getByText('Online Course')).toBeInTheDocument();
  });

  it('validates expense data before import', async () => {
    render(<CSVImport {...defaultProps} />);
    
    const invalidContent = `Date,Description,Amount
invalid-date,Missing fields,not-a-number`;
    
    global.FileReader = jest.fn().mockImplementation(() => ({
      readAsText: jest.fn(function(this: FileReader) {
        setTimeout(() => {
          if (this.onload) {
            this.onload({ target: { result: invalidContent } } as any);
          }
        }, 0);
      }),
      result: invalidContent
    })) as any;
    
    const dropzone = screen.getByText('Drag & drop CSV/Excel files here').parentElement;
    const file = new File([invalidContent], 'invalid.csv', { type: 'text/csv' });
    
    fireEvent.drop(dropzone!, {
      dataTransfer: {
        files: [file],
        types: ['Files']
      }
    });
    
    await waitFor(() => {
      expect(mockShowToast).toHaveBeenCalledWith(
        expect.stringContaining('validation errors'),
        'error'
      );
    });
  });

  it('imports valid expense data', async () => {
    render(<CSVImport {...defaultProps} />);
    
    const dropzone = screen.getByText('Drag & drop CSV/Excel files here').parentElement;
    const file = new File([mockFileContent], 'expenses.csv', { type: 'text/csv' });
    
    fireEvent.drop(dropzone!, {
      dataTransfer: {
        files: [file],
        types: ['Files']
      }
    });
    
    await waitFor(() => {
      expect(screen.getByText('Preview (2 rows)')).toBeInTheDocument();
    });
    
    const importButton = screen.getByText('Import 2 Expenses');
    fireEvent.click(importButton);
    
    await waitFor(() => {
      expect(mockOnImportExpenses).toHaveBeenCalledWith([
        expect.objectContaining({
          description: 'Training Materials',
          amount: 500,
          category: 'Materials',
          department: 'Engineering',
          status: 'paid'
        }),
        expect.objectContaining({
          description: 'Online Course',
          amount: 299.99,
          category: 'Courses',
          department: 'Engineering',
          status: 'pending'
        })
      ]);
    });
    
    expect(mockShowToast).toHaveBeenCalledWith(
      'Successfully imported 2 expenses',
      'success'
    );
  });

  it('detects and handles duplicate expenses', async () => {
    const existingExpenses = [
      {
        id: '1',
        date: '2024-01-15',
        description: 'Training Materials',
        amount: 500,
        category: 'Materials',
        department: 'Engineering',
        status: 'paid' as const
      }
    ];
    
    render(<CSVImport {...defaultProps} existingExpenses={existingExpenses} />);
    
    const dropzone = screen.getByText('Drag & drop CSV/Excel files here').parentElement;
    const file = new File([mockFileContent], 'expenses.csv', { type: 'text/csv' });
    
    fireEvent.drop(dropzone!, {
      dataTransfer: {
        files: [file],
        types: ['Files']
      }
    });
    
    await waitFor(() => {
      expect(screen.getByText('1 duplicate detected')).toBeInTheDocument();
    });
  });

  it('handles budget configuration import', async () => {
    render(<CSVImport {...defaultProps} />);
    
    // Switch to budget tab
    const budgetTab = screen.getByText('Budget Configuration');
    fireEvent.click(budgetTab);
    
    global.FileReader = jest.fn().mockImplementation(() => ({
      readAsText: jest.fn(function(this: FileReader) {
        setTimeout(() => {
          if (this.onload) {
            this.onload({ target: { result: mockBudgetContent } } as any);
          }
        }, 0);
      }),
      result: mockBudgetContent
    })) as any;
    
    const dropzone = screen.getByText('Drag & drop CSV/Excel files here').parentElement;
    const file = new File([mockBudgetContent], 'budget.csv', { type: 'text/csv' });
    
    fireEvent.drop(dropzone!, {
      dataTransfer: {
        files: [file],
        types: ['Files']
      }
    });
    
    await waitFor(() => {
      expect(screen.getByText('Preview (2 departments)')).toBeInTheDocument();
    });
    
    const importButton = screen.getByText('Import Budget Configuration');
    fireEvent.click(importButton);
    
    await waitFor(() => {
      expect(mockOnImportBudget).toHaveBeenCalledWith([
        expect.objectContaining({
          department: 'Engineering',
          budgetType: 'Training',
          amount: 50000,
          fiscalYear: 2024
        }),
        expect.objectContaining({
          department: 'Marketing',
          budgetType: 'Training',
          amount: 30000,
          fiscalYear: 2024
        })
      ]);
    });
  });

  it('downloads expense template', () => {
    render(<CSVImport {...defaultProps} />);
    
    // Create a mock for URL.createObjectURL
    global.URL.createObjectURL = jest.fn(() => 'blob:mock-url');
    
    const templateButton = screen.getByText('Download Template');
    fireEvent.click(templateButton);
    
    // Check if download was triggered
    expect(global.URL.createObjectURL).toHaveBeenCalled();
  });

  it('clears import data', async () => {
    render(<CSVImport {...defaultProps} />);
    
    const dropzone = screen.getByText('Drag & drop CSV/Excel files here').parentElement;
    const file = new File([mockFileContent], 'expenses.csv', { type: 'text/csv' });
    
    fireEvent.drop(dropzone!, {
      dataTransfer: {
        files: [file],
        types: ['Files']
      }
    });
    
    await waitFor(() => {
      expect(screen.getByText('Preview (2 rows)')).toBeInTheDocument();
    });
    
    const clearButton = screen.getByText('Clear');
    fireEvent.click(clearButton);
    
    expect(screen.queryByText('Preview (2 rows)')).not.toBeInTheDocument();
    expect(screen.getByText('Drag & drop CSV/Excel files here')).toBeInTheDocument();
  });

  it('shows loading state during import', async () => {
    render(<CSVImport {...defaultProps} loading={true} />);
    
    const dropzone = screen.getByText('Drag & drop CSV/Excel files here').parentElement;
    const file = new File([mockFileContent], 'expenses.csv', { type: 'text/csv' });
    
    fireEvent.drop(dropzone!, {
      dataTransfer: {
        files: [file],
        types: ['Files']
      }
    });
    
    await waitFor(() => {
      expect(screen.getByText('Preview (2 rows)')).toBeInTheDocument();
    });
    
    const importButton = screen.getByText('Import 2 Expenses');
    expect(importButton).toHaveAttribute('disabled');
  });

  it('handles batch processing for large files', async () => {
    // Create large CSV content
    let largeContent = 'Date,Description,Amount,Category,Department,Status\n';
    for (let i = 0; i < 150; i++) {
      largeContent += `2024-01-${(i % 30) + 1},Expense ${i},${100 + i},Category,Department,paid\n`;
    }
    
    global.FileReader = jest.fn().mockImplementation(() => ({
      readAsText: jest.fn(function(this: FileReader) {
        setTimeout(() => {
          if (this.onload) {
            this.onload({ target: { result: largeContent } } as any);
          }
        }, 0);
      }),
      result: largeContent
    })) as any;
    
    render(<CSVImport {...defaultProps} />);
    
    const dropzone = screen.getByText('Drag & drop CSV/Excel files here').parentElement;
    const file = new File([largeContent], 'large.csv', { type: 'text/csv' });
    
    fireEvent.drop(dropzone!, {
      dataTransfer: {
        files: [file],
        types: ['Files']
      }
    });
    
    await waitFor(() => {
      expect(screen.getByText('Preview (100 of 150 rows)')).toBeInTheDocument();
      expect(screen.getByText('Large file will be imported in batches')).toBeInTheDocument();
    });
  });
});