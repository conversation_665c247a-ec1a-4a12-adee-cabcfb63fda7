import React, { useState, useEffect } from 'react';
import { useSpring, animated, useTransition, useTrail, config } from '@react-spring/web';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Calendar,
  FileText,
  AlertCircle
} from 'lucide-react';
import { format } from 'date-fns';

interface Expense {
  id: string;
  title: string;
  amount: number;
  category: string;
  date: string;
  status: 'pending' | 'approved' | 'paid' | 'rejected';
  description?: string;
  receipt?: string;
}

interface AnimatedExpenseListProps {
  expenses: Expense[];
  loading?: boolean;
  onExpenseClick?: (expense: Expense) => void;
  onStatusChange?: (id: string, status: string) => void;
}

export const AnimatedExpenseList: React.FC<AnimatedExpenseListProps> = ({
  expenses = [],
  loading = false,
  onExpenseClick,
  onStatusChange
}) => {
  const [mounted, setMounted] = useState(false);
  const [filter, setFilter] = useState<string>('all');

  useEffect(() => {
    setMounted(true);
  }, []);

  // Filter expenses based on status
  const filteredExpenses = filter === 'all' 
    ? expenses 
    : expenses.filter(expense => expense.status === filter);

  // List transition animation
  const transitions = useTransition(filteredExpenses, {
    keys: expense => expense.id,
    from: { opacity: 0, transform: 'translateY(20px) scale(0.95)' },
    enter: { opacity: 1, transform: 'translateY(0px) scale(1)' },
    leave: { opacity: 0, transform: 'translateY(-20px) scale(0.95)' },
    update: { opacity: 1, transform: 'translateY(0px) scale(1)' },
    config: config.gentle,
    trail: 50
  });

  // Header animation
  const headerSpring = useSpring({
    from: { opacity: 0, transform: 'translateY(-10px)' },
    to: { opacity: 1, transform: 'translateY(0px)' },
    config: config.slow,
    delay: 100
  });

  // Filter buttons animation
  const filterTrail = useTrail(['all', 'pending', 'approved', 'paid', 'rejected'].length, {
    from: { opacity: 0, transform: 'translateY(10px)' },
    to: { opacity: 1, transform: 'translateY(0px)' },
    config: config.wobbly,
    delay: 200
  });

  // Total amount animation
  const totalSpring = useSpring({
    from: { number: 0 },
    to: { number: filteredExpenses.reduce((sum, exp) => sum + exp.amount, 0) },
    config: { tension: 200, friction: 20 },
    delay: 300
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'approved': return 'bg-blue-100 text-blue-800';
      case 'paid': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return AlertCircle;
      case 'approved': return TrendingUp;
      case 'paid': return DollarSign;
      case 'rejected': return TrendingDown;
      default: return FileText;
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Expenses</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-20 bg-gray-200 animate-pulse rounded-lg"></div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <animated.div style={headerSpring} className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg">Recent Expenses</CardTitle>
            <p className="text-sm text-muted-foreground">
              {filteredExpenses.length} expenses • 
              <animated.span className="font-medium">
                ${totalSpring.number.to(n => n.toLocaleString())}
              </animated.span>
            </p>
          </div>
        </animated.div>

        {/* Filter Buttons */}
        <div className="flex flex-wrap gap-2 mt-4">
          {filterTrail.map((props, index) => {
            const status = ['all', 'pending', 'approved', 'paid', 'rejected'][index];
            return (
              <animated.div key={status} style={props}>
                <Button
                  variant={filter === status ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setFilter(status)}
                >
                  {status.charAt(0).toUpperCase() + status.slice(1)}
                </Button>
              </animated.div>
            );
          })}
        </div>
      </CardHeader>

      <CardContent>
        <div className="space-y-3">
          {transitions((style, expense) => (
            <animated.div style={style}>
              <div
                className="p-4 border rounded-lg hover:shadow-md transition-shadow cursor-pointer"
                onClick={() => onExpenseClick?.(expense)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium">{expense.title}</h4>
                      <span className="font-bold text-lg">
                        ${expense.amount.toLocaleString()}
                      </span>
                    </div>
                    <div className="flex items-center gap-4 mt-2 text-sm text-muted-foreground">
                      <span className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        {format(new Date(expense.date), 'MMM d, yyyy')}
                      </span>
                      <Badge className={getStatusColor(expense.status)}>
                        {expense.status}
                      </Badge>
                      <span>{expense.category}</span>
                    </div>
                    {expense.description && (
                      <p className="mt-2 text-sm text-gray-600">
                        {expense.description}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </animated.div>
          ))}
          
          {filteredExpenses.length === 0 && (
            <animated.div
              style={useSpring({
                from: { opacity: 0, transform: 'translateY(10px)' },
                to: { opacity: 1, transform: 'translateY(0px)' },
                config: config.gentle
              })}
              className="text-center py-8 text-muted-foreground"
            >
              <FileText className="h-12 w-12 mx-auto mb-3 opacity-50" />
              <p>No expenses found</p>
            </animated.div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default AnimatedExpenseList;