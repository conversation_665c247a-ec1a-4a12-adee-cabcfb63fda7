import React, { useMemo } from 'react';
import { createColumnHelper } from '@tanstack/react-table';
import { ShadcnDataTable } from '@/components/features/ShadcnDataTable';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { format } from 'date-fns';
import {
  MoreVertical,
  Edit,
  Trash2,
  Receipt,
  Eye,
  FileText,
  Clock,
  CheckCircle,
  DollarSign,
  Copy,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Expense } from '../../types/expense.types';

interface ExpenseDataTableProps {
  expenses: Expense[];
  departments: any[];
  onEdit: (expense: Expense) => void;
  onDelete: (id: string) => void;
  onStatusChange: (id: string, status: Expense['status']) => void;
  onViewReceipt: (url: string) => void;
  loading?: boolean;
}

const statusConfig = {
  draft: { 
    label: 'Draft', 
    color: 'bg-gray-100 text-gray-700 border-gray-200',
    icon: FileText
  },
  submitted: { 
    label: 'Submitted', 
    color: 'bg-blue-100 text-blue-700 border-blue-200',
    icon: Clock
  },
  paid: { 
    label: 'Paid', 
    color: 'bg-green-100 text-green-700 border-green-200',
    icon: CheckCircle
  },
  reimbursed: { 
    label: 'Reimbursed', 
    color: 'bg-purple-100 text-purple-700 border-purple-200',
    icon: DollarSign
  },
};

const categoryColors: Record<string, string> = {
  'Online Courses': 'bg-blue-500',
  'Conferences': 'bg-purple-500',
  'Certifications': 'bg-yellow-500',
  'Books & Resources': 'bg-green-500',
  'Workshops': 'bg-orange-500',
  'Coaching': 'bg-pink-500',
  'Software & Tools': 'bg-indigo-500',
  'Other': 'bg-gray-500',
};

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};

const columnHelper = createColumnHelper<Expense>();

export const ExpenseDataTable: React.FC<ExpenseDataTableProps> = ({
  expenses,
  departments,
  onEdit,
  onDelete,
  onStatusChange,
  onViewReceipt,
  loading = false,
}) => {
  const columns = useMemo(() => [
    columnHelper.accessor('date', {
      header: 'Date',
      cell: (info) => {
        const date = new Date(info.getValue());
        return (
          <div className="text-sm">
            {format(date, 'MMM d, yyyy')}
          </div>
        );
      },
      sortingFn: 'datetime',
      filterFn: 'includesString',
    }),
    columnHelper.accessor('title', {
      header: 'Title',
      cell: (info) => {
        const expense = info.row.original;
        return (
          <div>
            <p className="font-medium text-sm">{info.getValue()}</p>
            {expense.description && (
              <p className="text-xs text-muted-foreground line-clamp-1">
                {expense.description}
              </p>
            )}
          </div>
        );
      },
      filterFn: 'includesString',
    }),
    columnHelper.accessor('category', {
      header: 'Category',
      cell: (info) => {
        const category = info.getValue();
        return (
          <div className="flex items-center gap-2">
            <div className={cn('h-2 w-2 rounded-full', categoryColors[category] || 'bg-gray-500')} />
            <span className="text-sm">{category}</span>
          </div>
        );
      },
      filterFn: (row, columnId, filterValue) => {
        return row.getValue(columnId) === filterValue;
      },
    }),
    columnHelper.accessor('department_id', {
      header: 'Department',
      cell: (info) => {
        const departmentId = info.getValue();
        const department = departments.find(d => d.id === departmentId);
        return (
          <span className="text-sm text-muted-foreground">
            {department?.name || 'No Department'}
          </span>
        );
      },
      filterFn: (row, columnId, filterValue) => {
        const departmentId = row.getValue(columnId);
        const department = departments.find(d => d.id === departmentId);
        return department?.name?.toLowerCase().includes(filterValue.toLowerCase()) || false;
      },
    }),
    columnHelper.accessor('amount', {
      header: 'Amount',
      cell: (info) => (
        <span className="font-semibold text-sm">
          {formatCurrency(info.getValue())}
        </span>
      ),
      sortingFn: 'basic',
      filterFn: 'inNumberRange',
    }),
    columnHelper.accessor('status', {
      header: 'Status',
      cell: (info) => {
        const status = info.getValue();
        const config = statusConfig[status];
        const StatusIcon = config.icon;
        return (
          <Badge className={cn('gap-1 text-xs', config.color)}>
            <StatusIcon className="h-3 w-3" />
            {config.label}
          </Badge>
        );
      },
      filterFn: (row, columnId, filterValue) => {
        return row.getValue(columnId) === filterValue;
      },
    }),
    columnHelper.display({
      id: 'actions',
      header: '',
      cell: (info) => {
        const expense = info.row.original;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onEdit(expense)}>
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </DropdownMenuItem>
              {expense.receipt_url && (
                <DropdownMenuItem onClick={() => onViewReceipt(expense.receipt_url!)}>
                  <Eye className="h-4 w-4 mr-2" />
                  View Receipt
                </DropdownMenuItem>
              )}
              <DropdownMenuItem onClick={() => navigator.clipboard.writeText(expense.id)}>
                <Copy className="h-4 w-4 mr-2" />
                Copy ID
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                onClick={() => onDelete(expense.id)}
                className="text-red-600 focus:text-red-600"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
      enableSorting: false,
      enableColumnFilter: false,
    }),
  ], [departments, onEdit, onDelete, onViewReceipt]);

  const handleEdit = (expense: Expense) => {
    onEdit(expense);
  };

  const handleDelete = (expense: Expense) => {
    onDelete(expense.id);
  };

  const handleView = (expense: Expense) => {
    if (expense.receipt_url) {
      onViewReceipt(expense.receipt_url);
    }
  };

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-3 bg-secondary rounded-xl">
            <FileText className="h-6 w-6 text-secondary-foreground" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-card-foreground">Expenses</h2>
            <p className="text-sm text-muted-foreground">
              Manage your training and development expenses
            </p>
          </div>
        </div>
      </div>

      {/* TanStack DataTable */}
      <ShadcnDataTable
        data={expenses}
        columns={columns}
        loading={loading}
        onEdit={handleEdit}
        onDelete={handleDelete}
        onView={handleView}
        enableGlobalFilter={true}
        enableRowActions={true}
        title="Expenses"
        defaultPageSize={10}
        pageSizeOptions={[5, 10, 25, 50]}
      />
    </div>
  );
};