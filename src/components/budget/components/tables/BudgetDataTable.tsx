import React, { useMemo, useState } from 'react';
import {
  useReactTable,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  getFilteredRowModel,
  flexRender,
  ColumnDef,
  SortingState,
  ColumnFiltersState,
  PaginationState
} from '@tanstack/react-table';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  TablePagination,
  TextField,
  Box,
  IconButton,
  Tooltip,
  Chip,
  Typography,
  useTheme,
  alpha,
  InputAdornment,
  Menu,
  MenuItem,
  Checkbox,
  ListItemText,
  Button,
  Stack
} from '@mui/material';
import {
  ArrowUpward,
  ArrowDownward,
  Search,
  FilterList,
  Download,
  Refresh,
  ViewColumn,
  MoreVert
} from '@mui/icons-material';
import { formatCurrency } from '../../utils/formatters';

interface BudgetDataTableProps {
  data: any[];
  columns: ColumnDef<any>[];
  title?: string;
  onRefresh?: () => void;
  onExport?: (format: string) => void;
  enableColumnVisibility?: boolean;
  enableGlobalFilter?: boolean;
  enableSorting?: boolean;
  enablePagination?: boolean;
}

export const BudgetDataTable: React.FC<BudgetDataTableProps> = ({
  data,
  columns,
  title,
  onRefresh,
  onExport,
  enableColumnVisibility = true,
  enableGlobalFilter = true,
  enableSorting = true,
  enablePagination = true
}) => {
  const theme = useTheme();
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState('');
  const [columnVisibility, setColumnVisibility] = useState({});
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10
  });
  
  const [columnMenuAnchor, setColumnMenuAnchor] = useState<null | HTMLElement>(null);
  const [actionMenuAnchor, setActionMenuAnchor] = useState<null | HTMLElement>(null);

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
      columnFilters,
      globalFilter,
      columnVisibility,
      pagination
    },
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onGlobalFilterChange: setGlobalFilter,
    onColumnVisibilityChange: setColumnVisibility,
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: enablePagination ? getPaginationRowModel() : undefined,
    getSortedRowModel: enableSorting ? getSortedRowModel() : undefined,
    getFilteredRowModel: getFilteredRowModel()
  });

  const handleChangePage = (event: unknown, newPage: number) => {
    setPagination(prev => ({ ...prev, pageIndex: newPage }));
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setPagination({
      pageIndex: 0,
      pageSize: parseInt(event.target.value, 10)
    });
  };

  const visibleColumns = table.getAllLeafColumns().filter(column => column.getCanHide());

  return (
    <Paper
      elevation={0}
      sx={{
        borderRadius: 2,
        border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
        overflow: 'hidden'
      }}
    >
      {/* Header */}
      <Box
        sx={{
          p: 2,
          borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
          bgcolor: alpha(theme.palette.primary.main, 0.02)
        }}
      >
        <Stack direction="row" justifyContent="space-between" alignItems="center" spacing={2}>
          <Box>
            {title && (
              <Typography variant="h6" sx={{ fontWeight: 600 }}>
                {title}
              </Typography>
            )}
            <Typography variant="caption" color="text.secondary">
              {table.getFilteredRowModel().rows.length} of {data.length} records
            </Typography>
          </Box>
          
          <Stack direction="row" spacing={1} alignItems="center">
            {enableGlobalFilter && (
              <TextField
                size="small"
                placeholder="Search..."
                value={globalFilter ?? ''}
                onChange={e => setGlobalFilter(e.target.value)}
                sx={{ width: 250 }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search fontSize="small" />
                    </InputAdornment>
                  )
                }}
              />
            )}
            
            {enableColumnVisibility && (
              <>
                <Tooltip title="Column Visibility">
                  <IconButton
                    size="small"
                    onClick={(e) => setColumnMenuAnchor(e.currentTarget)}
                  >
                    <ViewColumn />
                  </IconButton>
                </Tooltip>
                <Menu
                  anchorEl={columnMenuAnchor}
                  open={Boolean(columnMenuAnchor)}
                  onClose={() => setColumnMenuAnchor(null)}
                >
                  {visibleColumns.map(column => (
                    <MenuItem key={column.id} dense>
                      <Checkbox
                        checked={column.getIsVisible()}
                        onChange={column.getToggleVisibilityHandler()}
                        size="small"
                      />
                      <ListItemText primary={column.columnDef.header as string} />
                    </MenuItem>
                  ))}
                </Menu>
              </>
            )}
            
            {onRefresh && (
              <Tooltip title="Refresh">
                <IconButton size="small" onClick={onRefresh}>
                  <Refresh />
                </IconButton>
              </Tooltip>
            )}
            
            {onExport && (
              <>
                <Tooltip title="Export">
                  <IconButton
                    size="small"
                    onClick={(e) => setActionMenuAnchor(e.currentTarget)}
                  >
                    <Download />
                  </IconButton>
                </Tooltip>
                <Menu
                  anchorEl={actionMenuAnchor}
                  open={Boolean(actionMenuAnchor)}
                  onClose={() => setActionMenuAnchor(null)}
                >
                  <MenuItem onClick={() => { onExport('csv'); setActionMenuAnchor(null); }}>
                    Export as CSV
                  </MenuItem>
                  <MenuItem onClick={() => { onExport('json'); setActionMenuAnchor(null); }}>
                    Export as JSON
                  </MenuItem>
                  <MenuItem onClick={() => { onExport('pdf'); setActionMenuAnchor(null); }}>
                    Export as PDF
                  </MenuItem>
                </Menu>
              </>
            )}
          </Stack>
        </Stack>
      </Box>

      {/* Table */}
      <TableContainer sx={{ maxHeight: 600 }}>
        <Table stickyHeader size="small">
          <TableHead>
            {table.getHeaderGroups().map(headerGroup => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map(header => (
                  <TableCell
                    key={header.id}
                    align={header.column.columnDef.meta?.align || 'left'}
                    sx={{
                      fontWeight: 600,
                      bgcolor: theme.palette.background.paper,
                      borderBottom: `2px solid ${theme.palette.divider}`,
                      cursor: header.column.getCanSort() ? 'pointer' : 'default',
                      userSelect: 'none',
                      '&:hover': {
                        bgcolor: header.column.getCanSort() 
                          ? alpha(theme.palette.action.hover, 0.04)
                          : 'inherit'
                      }
                    }}
                    onClick={header.column.getToggleSortingHandler()}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                      {flexRender(
                        header.column.columnDef.header,
                        header.getContext()
                      )}
                      {header.column.getCanSort() && (
                        <>
                          {header.column.getIsSorted() === 'asc' && (
                            <ArrowUpward sx={{ fontSize: 16 }} />
                          )}
                          {header.column.getIsSorted() === 'desc' && (
                            <ArrowDownward sx={{ fontSize: 16 }} />
                          )}
                          {!header.column.getIsSorted() && enableSorting && (
                            <Box sx={{ width: 16 }} />
                          )}
                        </>
                      )}
                    </Box>
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableHead>
          <TableBody>
            {table.getRowModel().rows.map(row => (
              <TableRow
                key={row.id}
                hover
                sx={{
                  '&:hover': {
                    bgcolor: alpha(theme.palette.action.hover, 0.04)
                  }
                }}
              >
                {row.getVisibleCells().map(cell => (
                  <TableCell
                    key={cell.id}
                    align={cell.column.columnDef.meta?.align || 'left'}
                  >
                    {flexRender(
                      cell.column.columnDef.cell,
                      cell.getContext()
                    )}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Pagination */}
      {enablePagination && (
        <TablePagination
          component="div"
          count={table.getFilteredRowModel().rows.length}
          page={pagination.pageIndex}
          onPageChange={handleChangePage}
          rowsPerPage={pagination.pageSize}
          onRowsPerPageChange={handleChangeRowsPerPage}
          rowsPerPageOptions={[5, 10, 25, 50, 100]}
          sx={{
            borderTop: `1px solid ${alpha(theme.palette.divider, 0.1)}`
          }}
        />
      )}
    </Paper>
  );
};

// Example columns helper for budget data
export const createBudgetColumns = (): ColumnDef<any>[] => [
  {
    accessorKey: 'department',
    header: 'Department',
    cell: info => (
      <Typography variant="body2" sx={{ fontWeight: 500 }}>
        {info.getValue() as string}
      </Typography>
    )
  },
  {
    accessorKey: 'category',
    header: 'Category',
    cell: info => (
      <Chip
        label={info.getValue() as string}
        size="small"
        variant="outlined"
      />
    )
  },
  {
    accessorKey: 'budget',
    header: 'Budget',
    cell: info => (
      <Typography variant="body2" sx={{ fontWeight: 600, color: 'primary.main' }}>
        {formatCurrency(info.getValue() as number)}
      </Typography>
    ),
    meta: { align: 'right' }
  },
  {
    accessorKey: 'spent',
    header: 'Spent',
    cell: info => (
      <Typography variant="body2">
        {formatCurrency(info.getValue() as number)}
      </Typography>
    ),
    meta: { align: 'right' }
  },
  {
    accessorKey: 'variance',
    header: 'Variance',
    cell: info => {
      const value = info.getValue() as number;
      const isPositive = value >= 0;
      return (
        <Chip
          label={formatCurrency(Math.abs(value))}
          size="small"
          color={isPositive ? 'success' : 'error'}
          sx={{ fontWeight: 600 }}
        />
      );
    },
    meta: { align: 'right' }
  },
  {
    accessorKey: 'utilization',
    header: 'Utilization',
    cell: info => {
      const value = info.getValue() as number;
      const color = value > 90 ? 'error' : value > 70 ? 'warning' : 'success';
      return (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Box
            sx={{
              width: 60,
              height: 6,
              borderRadius: 3,
              bgcolor: alpha(theme => theme.palette[color].main, 0.2),
              position: 'relative',
              overflow: 'hidden'
            }}
          >
            <Box
              sx={{
                position: 'absolute',
                top: 0,
                left: 0,
                height: '100%',
                width: `${value}%`,
                bgcolor: color + '.main',
                borderRadius: 3
              }}
            />
          </Box>
          <Typography variant="caption" sx={{ fontWeight: 600 }}>
            {value}%
          </Typography>
        </Box>
      );
    },
    meta: { align: 'center' }
  },
  {
    id: 'actions',
    header: 'Actions',
    cell: info => (
      <IconButton size="small">
        <MoreVert fontSize="small" />
      </IconButton>
    ),
    meta: { align: 'center' }
  }
];