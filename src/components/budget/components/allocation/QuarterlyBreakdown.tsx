import React, { useState, useCallback, useMemo } from 'react';
import { motion } from 'framer-motion';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Input } from '@/components/ui/input';
import { Skeleton } from '@/components/ui/skeleton';
import { Calendar, TrendingUp, AlertCircle, Lock, Unlock } from 'lucide-react';
import { QuarterlyAllocation } from '@/stores/budgetStore';
import { formatCurrency, formatPercentage } from '../../utils/formatters';
import { validateQuarterlyPercentages } from '../../utils/validations';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface QuarterlyBreakdownProps {
  allocations: QuarterlyAllocation[];
  totalBudget: number;
  onUpdate: (allocations: QuarterlyAllocation[]) => Promise<void>;
  loading?: boolean;
}

const QUARTER_NAMES = ['Q1', 'Q2', 'Q3', 'Q4'];
const QUARTER_MONTHS = [
  'Jan - Mar',
  'Apr - Jun',
  'Jul - Sep',
  'Oct - Dec'
];

export const QuarterlyBreakdown: React.FC<QuarterlyBreakdownProps> = ({
  allocations,
  totalBudget,
  onUpdate,
  loading = false,
}) => {
  const [editMode, setEditMode] = useState(false);
  const [tempAllocations, setTempAllocations] = useState(allocations);
  const [lockedQuarters, setLockedQuarters] = useState<Set<number>>(new Set());
  const [isUpdating, setIsUpdating] = useState(false);

  // Calculate total percentage
  const totalPercentage = useMemo(() => 
    tempAllocations.reduce((sum, a) => sum + (a.allocated / totalBudget) * 100, 0),
    [tempAllocations]
  );

  // Validation errors
  const validationErrors = useMemo(() => 
    validateQuarterlyPercentages(tempAllocations.map(a => (a.allocated / totalBudget) * 100)),
    [tempAllocations]
  );

  const handlePercentageChange = useCallback((quarter: number, newPercentage: number) => {
    if (lockedQuarters.has(quarter)) return;

    const newAllocations = [...tempAllocations];
    const oldPercentage = (newAllocations[quarter - 1].allocated / totalBudget) * 100;
    const difference = newPercentage - oldPercentage;

    // Update the changed quarter
    newAllocations[quarter - 1] = {
      ...newAllocations[quarter - 1],
      allocated: (totalBudget * newPercentage) / 100,
    };

    // Auto-adjust unlocked quarters
    const unlockedQuarters = newAllocations
      .map((_, idx) => idx + 1)
      .filter(q => q !== quarter && !lockedQuarters.has(q));

    if (unlockedQuarters.length > 0) {
      const adjustmentPerQuarter = -difference / unlockedQuarters.length;
      
      unlockedQuarters.forEach(q => {
        const idx = q - 1;
        const adjustedPercentage = Math.max(0, Math.min(100, (newAllocations[idx].allocated / totalBudget) * 100 + adjustmentPerQuarter));
        newAllocations[idx] = {
          ...newAllocations[idx],
          allocated: (totalBudget * adjustedPercentage) / 100,
        };
      });
    }

    setTempAllocations(newAllocations);
  }, [tempAllocations, totalBudget, lockedQuarters]);

  const handleSave = async () => {
    if (validationErrors.length > 0) return;

    setIsUpdating(true);
    try {
      await onUpdate(tempAllocations);
      setEditMode(false);
    } catch (error) {
      console.error('Failed to update quarterly allocations:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleCancel = () => {
    setTempAllocations(allocations);
    setEditMode(false);
    setLockedQuarters(new Set());
  };

  const toggleLock = (quarter: number) => {
    const newLocked = new Set(lockedQuarters);
    if (newLocked.has(quarter)) {
      newLocked.delete(quarter);
    } else {
      newLocked.add(quarter);
    }
    setLockedQuarters(newLocked);
  };

  const distributeEvenly = () => {
    const newAllocations = tempAllocations.map((alloc) => ({
      ...alloc,
      allocated: totalBudget * 0.25,
    }));
    setTempAllocations(newAllocations);
  };

  if (loading) {
    return (
      <Card className="p-6">
        <Skeleton className="h-6 w-48 mb-4" />
        <div className="space-y-4">
          {[...Array(4)].map((_, idx) => (
            <div key={idx} className="space-y-2">
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-10 w-full" />
            </div>
          ))}
        </div>
      </Card>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.1 }}
    >
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <Calendar className="h-5 w-5 text-muted-foreground" />
            <h3 className="text-lg font-semibold">Quarterly Budget Breakdown</h3>
          </div>
          {!editMode ? (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setEditMode(true)}
            >
              Edit Allocations
            </Button>
          ) : (
            <div className="flex gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={distributeEvenly}
              >
                Distribute Evenly
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCancel}
                disabled={isUpdating}
              >
                Cancel
              </Button>
              <Button
                size="sm"
                onClick={handleSave}
                disabled={validationErrors.length > 0 || isUpdating}
              >
                {isUpdating ? 'Saving...' : 'Save'}
              </Button>
            </div>
          )}
        </div>

        {validationErrors.length > 0 && editMode && (
          <div className="mb-4 p-3 bg-destructive/10 border border-destructive rounded-md">
            <div className="flex items-center gap-2 text-destructive">
              <AlertCircle className="h-4 w-4" />
              <span className="text-sm font-medium">
                {validationErrors[0].message}
              </span>
            </div>
          </div>
        )}

        <div className="space-y-4">
          <TooltipProvider>
            {(editMode ? tempAllocations : allocations).map((allocation, idx) => {
              const quarter = allocation.quarter;
              const isLocked = lockedQuarters.has(quarter);
              const percentageUsed = allocation.spent > 0 ? (allocation.spent / allocation.allocated) * 100 : 0;

              return (
                <motion.div
                  key={quarter}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.05 * idx }}
                  className="space-y-2"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{QUARTER_NAMES[idx]}</span>
                      <span className="text-sm text-muted-foreground">
                        {QUARTER_MONTHS[idx]}
                      </span>
                      {editMode && (
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-6 w-6"
                              onClick={() => toggleLock(quarter)}
                            >
                              {isLocked ? (
                                <Lock className="h-3 w-3" />
                              ) : (
                                <Unlock className="h-3 w-3" />
                              )}
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            {isLocked ? 'Unlock quarter' : 'Lock quarter'}
                          </TooltipContent>
                        </Tooltip>
                      )}
                    </div>
                    <div className="text-right">
                      <div className="font-medium">
                        {formatCurrency(allocation.allocated)}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {formatPercentage((allocation.allocated / totalBudget) * 100)}
                      </div>
                    </div>
                  </div>

                  {editMode ? (
                    <div className="flex items-center gap-4">
                      <Slider
                        value={[(allocation.allocated / totalBudget) * 100]}
                        onValueChange={([value]) => handlePercentageChange(quarter, value)}
                        max={100}
                        min={0}
                        step={0.5}
                        disabled={isLocked}
                        className="flex-1"
                      />
                      <Input
                        type="number"
                        value={((allocation.allocated / totalBudget) * 100).toFixed(1)}
                        onChange={(e) => handlePercentageChange(quarter, parseFloat(e.target.value) || 0)}
                        className="w-20 text-center"
                        disabled={isLocked}
                        min={0}
                        max={100}
                        step={0.5}
                      />
                      <span className="text-sm">%</span>
                    </div>
                  ) : (
                    <div className="space-y-1">
                      <div className="h-2 bg-secondary rounded-full overflow-hidden">
                        <motion.div
                          initial={{ width: 0 }}
                          animate={{ width: `${(allocation.allocated / totalBudget) * 100}%` }}
                          transition={{ duration: 0.5, ease: "easeOut", delay: 0.2 + (0.05 * idx) }}
                          className="h-full bg-primary relative"
                        >
                          {percentageUsed > 0 && (
                            <div
                              className="absolute inset-y-0 left-0 bg-primary-foreground/20"
                              style={{ width: `${percentageUsed}%` }}
                            />
                          )}
                        </motion.div>
                      </div>
                      <div className="flex justify-between text-xs text-muted-foreground">
                        <span>Spent: {formatCurrency(allocation.spent)}</span>
                        <span>Remaining: {formatCurrency(allocation.allocated - allocation.spent)}</span>
                      </div>
                    </div>
                  )}
                </motion.div>
              );
            })}
          </TooltipProvider>
        </div>

        {editMode && (
          <div className="mt-4 pt-4 border-t">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Total Allocation</span>
              <span className={`font-medium ${Math.abs(totalPercentage - 100) > 0.01 ? 'text-destructive' : 'text-green-600'}`}>
                {formatPercentage(totalPercentage)}
              </span>
            </div>
          </div>
        )}

        {!editMode && allocations.some(a => a.spent > a.allocated) && (
          <div className="mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md">
            <div className="flex items-center gap-2 text-yellow-800 dark:text-yellow-200">
              <TrendingUp className="h-4 w-4" />
              <span className="text-sm">
                Some quarters have exceeded their allocated budget
              </span>
            </div>
          </div>
        )}
      </Card>
    </motion.div>
  );
};