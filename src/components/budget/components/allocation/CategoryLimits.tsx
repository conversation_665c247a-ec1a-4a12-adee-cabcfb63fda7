import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import {
  ShieldCheck,
  Plus,
  Trash2,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Tag,
  TrendingUp
} from 'lucide-react';
import { CategoryLimit } from '@/stores/budgetStore';
import { formatCurrency, formatPercentage } from '../../utils/formatters';
import { validateCategoryLimit } from '../../utils/validations';
import { defaultExpenseCategories } from '../../types/expense.types';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface CategoryLimitsProps {
  limits: CategoryLimit[];
  categories: string[];
  totalBudget: number;
  onSetLimit: (category: string, limit: number) => Promise<void>;
  onRemoveLimit: (category: string) => Promise<void>;
  loading?: boolean;
}

export const CategoryLimits: React.FC<CategoryLimitsProps> = ({
  limits,
  categories,
  totalBudget,
  onSetLimit,
  onRemoveLimit,
  loading = false,
}) => {
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('');
  const [limitAmount, setLimitAmount] = useState('');
  const [editingCategory, setEditingCategory] = useState<string | null>(null);
  const [editAmount, setEditAmount] = useState('');
  const [isUpdating, setIsUpdating] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Get available categories (not already limited)
  const limitedCategories = limits.map(l => l.category);
  const availableCategories = [...defaultExpenseCategories.map(c => c.name), ...categories]
    .filter(cat => !limitedCategories.includes(cat));

  // Calculate total limits
  const totalLimitsAmount = limits.reduce((sum, limit) => sum + limit.limit, 0);
  const totalLimitsPercentage = (totalLimitsAmount / totalBudget) * 100;

  const handleAddLimit = async () => {
    const amount = parseFloat(limitAmount);
    const validationErrors = validateCategoryLimit(amount, totalBudget, selectedCategory);

    if (validationErrors.length > 0) {
      const errorMap: Record<string, string> = {};
      validationErrors.forEach(err => {
        errorMap[err.field] = err.message;
      });
      setErrors(errorMap);
      return;
    }

    setIsUpdating(true);
    try {
      await onSetLimit(selectedCategory, amount);
      setSelectedCategory('');
      setLimitAmount('');
      setShowAddDialog(false);
      setErrors({});
    } catch (error) {
      console.error('Failed to add category limit:', error);
      setErrors({ form: 'Failed to add limit' });
    } finally {
      setIsUpdating(false);
    }
  };

  const handleEditLimit = async (category: string) => {
    const amount = parseFloat(editAmount);
    const validationErrors = validateCategoryLimit(amount, totalBudget, category);

    if (validationErrors.length > 0) {
      const errorMap: Record<string, string> = {};
      validationErrors.forEach(err => {
        errorMap[`${category}_${err.field}`] = err.message;
      });
      setErrors(errorMap);
      return;
    }

    setIsUpdating(true);
    try {
      await onSetLimit(category, amount);
      setEditingCategory(null);
      setEditAmount('');
      setErrors({});
    } catch (error) {
      console.error('Failed to update category limit:', error);
      setErrors({ [`${category}_form`]: 'Failed to update limit' });
    } finally {
      setIsUpdating(false);
    }
  };

  const handleRemoveLimit = async (category: string) => {
    const limit = limits.find(l => l.category === category);
    if (!limit) return;

    if (limit.spent > 0) {
      const confirmed = window.confirm(
        `${category} has ${formatCurrency(limit.spent)} in expenses. Remove the limit?`
      );
      if (!confirmed) return;
    }

    setIsUpdating(true);
    try {
      await onRemoveLimit(category);
    } catch (error) {
      console.error('Failed to remove category limit:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  const getCategoryIcon = (category: string) => {
    const categoryData = defaultExpenseCategories.find(c => c.name === category);
    return categoryData?.color || 'bg-gray-500';
  };

  const getCategoryStatus = (limit: CategoryLimit) => {
    const percentage = limit.limit > 0 ? (limit.spent / limit.limit) * 100 : 0;
    
    if (percentage >= 100) {
      return { icon: XCircle, color: 'text-destructive', label: 'Over limit' };
    } else if (percentage >= 80) {
      return { icon: AlertTriangle, color: 'text-yellow-600', label: 'Near limit' };
    } else {
      return { icon: CheckCircle, color: 'text-green-600', label: 'Within limit' };
    }
  };

  if (loading) {
    return (
      <Card className="p-6 standard-card">
        <Skeleton className="h-6 w-48 mb-4" />
        <div className="space-y-4">
          {[...Array(3)].map((_, idx) => (
            <div key={idx} className="space-y-2">
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-2 w-full" />
            </div>
          ))}
        </div>
      </Card>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.3 }}
    >
      <Card className="p-6 standard-card">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <ShieldCheck className="h-5 w-5 text-muted-foreground" />
            <h3 className="text-lg font-semibold">Category Spending Limits</h3>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowAddDialog(true)}
            disabled={availableCategories.length === 0}
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Limit
          </Button>
        </div>

        {/* Summary */}
        {limits.length > 0 && (
          <div className="mb-4 p-3 bg-muted rounded-lg">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Total Limits Set</span>
              <div className="text-right">
                <span className="font-medium">{formatCurrency(totalLimitsAmount)}</span>
                <span className="text-muted-foreground ml-2">
                  ({formatPercentage(totalLimitsPercentage)} of budget)
                </span>
              </div>
            </div>
            {totalLimitsPercentage > 100 && (
              <div className="flex items-center gap-1 text-xs text-yellow-600 mt-1">
                <AlertTriangle className="h-3 w-3" />
                <span>Total limits exceed budget</span>
              </div>
            )}
          </div>
        )}

        {/* Category Limits List */}
        <div className="space-y-3">
          <AnimatePresence>
            {limits.map((limit) => {
              const isEditing = editingCategory === limit.category;
              const spentPercentage = limit.limit > 0 
                ? (limit.spent / limit.limit) * 100 
                : 0;
              const status = getCategoryStatus(limit);
              const categoryColor = getCategoryIcon(limit.category);

              return (
                <motion.div
                  key={limit.category}
                  layout
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  className="border rounded-lg p-4"
                >
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <div className={`w-3 h-3 rounded-full ${categoryColor}`} />
                      <div>
                        <h4 className="font-medium flex items-center gap-2">
                          {limit.category}
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger>
                                <status.icon className={`h-4 w-4 ${status.color}`} />
                              </TooltipTrigger>
                              <TooltipContent>{status.label}</TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </h4>
                        <p className="text-sm text-muted-foreground">
                          {formatCurrency(limit.spent)} of {formatCurrency(limit.limit)} spent
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {isEditing ? (
                        <>
                          <Input
                            type="number"
                            value={editAmount}
                            onChange={(e) => setEditAmount(e.target.value)}
                            className="w-32"
                            placeholder="Limit amount"
                            autoFocus
                          />
                          <Button
                            size="sm"
                            onClick={() => handleEditLimit(limit.category)}
                            disabled={isUpdating}
                          >
                            Save
                          </Button>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => {
                              setEditingCategory(null);
                              setEditAmount('');
                              setErrors({});
                            }}
                          >
                            Cancel
                          </Button>
                        </>
                      ) : (
                        <>
                          <Badge 
                            variant={spentPercentage >= 100 ? 'destructive' : 'secondary'}
                          >
                            {formatPercentage(spentPercentage)}
                          </Badge>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => {
                              setEditingCategory(limit.category);
                              setEditAmount(limit.limit.toString());
                            }}
                          >
                            Edit
                          </Button>
                          <Button
                            size="icon"
                            variant="ghost"
                            onClick={() => handleRemoveLimit(limit.category)}
                            disabled={isUpdating}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </>
                      )}
                    </div>
                  </div>

                  {errors[`${limit.category}_limit`] && (
                    <p className="text-sm text-destructive mb-2">
                      {errors[`${limit.category}_limit`]}
                    </p>
                  )}

                  <div className="space-y-2">
                    <Progress 
                      value={Math.min(spentPercentage, 100)} 
                      className={`h-2 ${spentPercentage >= 100 ? '[&>div]:bg-destructive' : ''}`}
                    />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>Remaining: {formatCurrency(limit.limit - limit.spent)}</span>
                      <span>{formatPercentage((limit.limit / totalBudget) * 100)} of total budget</span>
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </AnimatePresence>

          {limits.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <Tag className="h-12 w-12 mx-auto mb-3 opacity-50" />
              <p>No category limits set</p>
              <p className="text-sm">Add limits to control spending by category</p>
            </div>
          )}
        </div>

        {/* Recommendations */}
        {limits.length > 0 && limits.some(l => l.spent > l.limit * 0.8) && (
          <div className="mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md">
            <div className="flex items-start gap-2">
              <TrendingUp className="h-4 w-4 text-yellow-800 dark:text-yellow-200 mt-0.5" />
              <div className="text-sm text-yellow-800 dark:text-yellow-200">
                <p className="font-medium">Budget Alert</p>
                <p>Some categories are approaching or exceeding their limits. Consider adjusting limits or reducing spending.</p>
              </div>
            </div>
          </div>
        )}
      </Card>

      {/* Add Limit Dialog */}
      <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Set Category Limit</DialogTitle>
            <DialogDescription>
              Set a spending limit for a specific training category.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="category">Category</Label>
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a category" />
                </SelectTrigger>
                <SelectContent>
                  {availableCategories.map((cat) => {
                    const categoryData = defaultExpenseCategories.find(c => c.name === cat);
                    return (
                      <SelectItem key={cat} value={cat}>
                        <div className="flex items-center gap-2">
                          <div className={`w-3 h-3 rounded-full ${categoryData?.color || 'bg-gray-500'}`} />
                          {cat}
                        </div>
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="limit">Limit Amount</Label>
              <Input
                id="limit"
                type="number"
                value={limitAmount}
                onChange={(e) => setLimitAmount(e.target.value)}
                placeholder="0.00"
              />
              <p className="text-sm text-muted-foreground">
                Total budget: {formatCurrency(totalBudget)}
              </p>
              {errors.limit && (
                <p className="text-sm text-destructive">{errors.limit}</p>
              )}
            </div>
            {errors.form && (
              <p className="text-sm text-destructive">{errors.form}</p>
            )}
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setShowAddDialog(false);
                setSelectedCategory('');
                setLimitAmount('');
                setErrors({});
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleAddLimit}
              disabled={isUpdating || !selectedCategory || !limitAmount}
            >
              {isUpdating ? 'Setting...' : 'Set Limit'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </motion.div>
  );
};