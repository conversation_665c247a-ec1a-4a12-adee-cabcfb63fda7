import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import { Progress } from '@/components/ui/progress';
import { 
  Users, 
  Plus, 
  Trash2, 
  Edit2, 
  Check, 
  X, 
  AlertCircle,
  Building 
} from 'lucide-react';
import { DepartmentAllocation } from '@/stores/budgetStore';
import { formatCurrency, formatPercentage } from '../../utils/formatters';
import { validateDepartmentName } from '../../utils/validations';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

interface DepartmentAllocationsProps {
  departments: DepartmentAllocation[];
  totalBudget: number;
  onUpdate: (departmentId: string, amount: number) => Promise<void>;
  onAdd: (name: string, amount: number) => Promise<void>;
  onRemove: (departmentId: string) => Promise<void>;
  loading?: boolean;
}

interface EditingDepartment {
  id: string;
  amount: string;
}

export const DepartmentAllocations: React.FC<DepartmentAllocationsProps> = ({
  departments,
  totalBudget,
  onUpdate,
  onAdd,
  onRemove,
  loading = false,
}) => {
  const [editingDept, setEditingDept] = useState<EditingDepartment | null>(null);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [newDeptName, setNewDeptName] = useState('');
  const [newDeptAmount, setNewDeptAmount] = useState('');
  const [isUpdating, setIsUpdating] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Calculate totals
  const totalAllocated = departments.reduce((sum, dept) => sum + dept.amount, 0);
  const totalSpent = departments.reduce((sum, dept) => sum + dept.spent, 0);
  const unallocated = totalBudget - totalAllocated;

  const handleEdit = (dept: DepartmentAllocation) => {
    setEditingDept({
      id: dept.id,
      amount: dept.amount.toString(),
    });
  };

  const handleSaveEdit = async () => {
    if (!editingDept) return;

    const amount = parseFloat(editingDept.amount);
    if (isNaN(amount) || amount < 0) {
      setErrors({ [editingDept.id]: 'Invalid amount' });
      return;
    }

    setIsUpdating(true);
    try {
      await onUpdate(editingDept.id, amount);
      setEditingDept(null);
      setErrors({});
    } catch (error) {
      console.error('Failed to update department allocation:', error);
      setErrors({ [editingDept.id]: 'Failed to update' });
    } finally {
      setIsUpdating(false);
    }
  };

  const handleCancelEdit = () => {
    setEditingDept(null);
    setErrors({});
  };

  const handleAddDepartment = async () => {
    const existingNames = departments.map(d => d.name);
    const nameErrors = validateDepartmentName(newDeptName, existingNames);
    
    const amount = parseFloat(newDeptAmount);
    const amountErrors = [];
    if (isNaN(amount) || amount <= 0) {
      amountErrors.push({ field: 'amount', message: 'Amount must be greater than 0' });
    }
    if (amount > unallocated) {
      amountErrors.push({ field: 'amount', message: 'Amount exceeds unallocated budget' });
    }

    const allErrors = [...nameErrors, ...amountErrors];
    if (allErrors.length > 0) {
      const errorMap: Record<string, string> = {};
      allErrors.forEach(err => {
        errorMap[err.field] = err.message;
      });
      setErrors(errorMap);
      return;
    }

    setIsUpdating(true);
    try {
      await onAdd(newDeptName.trim(), amount);
      setNewDeptName('');
      setNewDeptAmount('');
      setShowAddDialog(false);
      setErrors({});
    } catch (error) {
      console.error('Failed to add department:', error);
      setErrors({ form: 'Failed to add department' });
    } finally {
      setIsUpdating(false);
    }
  };

  const handleRemoveDepartment = async (deptId: string) => {
    const dept = departments.find(d => d.id === deptId);
    if (!dept) return;

    if (dept.spent > 0) {
      const confirmed = window.confirm(
        `${dept.name} has ${formatCurrency(dept.spent)} in expenses. Are you sure you want to remove it?`
      );
      if (!confirmed) return;
    }

    setIsUpdating(true);
    try {
      await onRemove(deptId);
    } catch (error) {
      console.error('Failed to remove department:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  if (loading) {
    return (
      <Card className="p-6">
        <Skeleton className="h-6 w-48 mb-4" />
        <div className="space-y-4">
          {[...Array(3)].map((_, idx) => (
            <div key={idx} className="flex items-center justify-between">
              <Skeleton className="h-10 w-32" />
              <Skeleton className="h-8 w-24" />
            </div>
          ))}
        </div>
      </Card>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.2 }}
    >
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <Building className="h-5 w-5 text-muted-foreground" />
            <h3 className="text-lg font-semibold">Department Allocations</h3>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowAddDialog(true)}
            disabled={unallocated <= 0}
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Department
          </Button>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="p-3 bg-muted rounded-lg">
            <p className="text-sm text-muted-foreground">Total Allocated</p>
            <p className="text-lg font-semibold">{formatCurrency(totalAllocated)}</p>
            <p className="text-xs text-muted-foreground">
              {formatPercentage((totalAllocated / totalBudget) * 100)} of budget
            </p>
          </div>
          <div className="p-3 bg-muted rounded-lg">
            <p className="text-sm text-muted-foreground">Total Spent</p>
            <p className="text-lg font-semibold">{formatCurrency(totalSpent)}</p>
            <p className="text-xs text-muted-foreground">
              {formatPercentage(totalAllocated > 0 ? (totalSpent / totalAllocated) * 100 : 0)} of allocated
            </p>
          </div>
          <div className="p-3 bg-muted rounded-lg">
            <p className="text-sm text-muted-foreground">Unallocated</p>
            <p className={`text-lg font-semibold ${unallocated < 0 ? 'text-destructive' : ''}`}>
              {formatCurrency(Math.abs(unallocated))}
            </p>
            {unallocated < 0 && (
              <p className="text-xs text-destructive">Over-allocated!</p>
            )}
          </div>
        </div>

        {/* Department List */}
        <div className="space-y-3">
          <AnimatePresence>
            {departments.map((dept) => {
              const isEditing = editingDept?.id === dept.id;
              const spentPercentage = dept.amount > 0 
                ? (dept.spent / dept.amount) * 100 
                : 0;
              const isOverBudget = dept.spent > dept.amount;

              return (
                <motion.div
                  key={dept.id}
                  layout
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  className="border rounded-lg p-4"
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-3">
                      <Users className="h-5 w-5 text-muted-foreground" />
                      <div>
                        <h4 className="font-medium">{dept.name}</h4>
                        <p className="text-sm text-muted-foreground">
                          {formatPercentage((dept.amount / totalBudget) * 100)} of total budget
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {isEditing ? (
                        <>
                          <Input
                            type="number"
                            value={editingDept.amount}
                            onChange={(e) => setEditingDept({
                              ...editingDept,
                              amount: e.target.value
                            })}
                            className="w-32"
                            placeholder="Amount"
                            autoFocus
                          />
                          <Button
                            size="icon"
                            variant="ghost"
                            onClick={handleSaveEdit}
                            disabled={isUpdating}
                          >
                            <Check className="h-4 w-4" />
                          </Button>
                          <Button
                            size="icon"
                            variant="ghost"
                            onClick={handleCancelEdit}
                            disabled={isUpdating}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </>
                      ) : (
                        <>
                          <div className="text-right">
                            <p className="font-medium">{formatCurrency(dept.amount)}</p>
                            <p className="text-sm text-muted-foreground">
                              Spent: {formatCurrency(dept.spent)}
                            </p>
                          </div>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  size="icon"
                                  variant="ghost"
                                  onClick={() => handleEdit(dept)}
                                >
                                  <Edit2 className="h-4 w-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>Edit allocation</TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  size="icon"
                                  variant="ghost"
                                  onClick={() => handleRemoveDepartment(dept.id)}
                                  disabled={isUpdating}
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>Remove department</TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </>
                      )}
                    </div>
                  </div>

                  {errors[dept.id] && (
                    <p className="text-sm text-destructive mb-2">{errors[dept.id]}</p>
                  )}

                  <div className="space-y-1">
                    <Progress 
                      value={Math.min(spentPercentage, 100)} 
                      className="h-2"
                    />
                    {isOverBudget && (
                      <div className="flex items-center gap-1 text-xs text-destructive">
                        <AlertCircle className="h-3 w-3" />
                        <span>Over budget by {formatCurrency(dept.spent - dept.amount)}</span>
                      </div>
                    )}
                  </div>
                </motion.div>
              );
            })}
          </AnimatePresence>

          {departments.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <Users className="h-12 w-12 mx-auto mb-3 opacity-50" />
              <p>No departments configured</p>
              <p className="text-sm">Add departments to allocate budget</p>
            </div>
          )}
        </div>
      </Card>

      {/* Add Department Dialog */}
      <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add New Department</DialogTitle>
            <DialogDescription>
              Create a new department and allocate budget to it.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="name">Department Name</Label>
              <Input
                id="name"
                value={newDeptName}
                onChange={(e) => setNewDeptName(e.target.value)}
                placeholder="e.g., Engineering, Marketing"
              />
              {errors.name && (
                <p className="text-sm text-destructive">{errors.name}</p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="amount">Allocated Amount</Label>
              <Input
                id="amount"
                type="number"
                value={newDeptAmount}
                onChange={(e) => setNewDeptAmount(e.target.value)}
                placeholder="0.00"
              />
              <p className="text-sm text-muted-foreground">
                Available: {formatCurrency(unallocated)}
              </p>
              {errors.amount && (
                <p className="text-sm text-destructive">{errors.amount}</p>
              )}
            </div>
            {errors.form && (
              <p className="text-sm text-destructive">{errors.form}</p>
            )}
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setShowAddDialog(false);
                setNewDeptName('');
                setNewDeptAmount('');
                setErrors({});
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleAddDepartment}
              disabled={isUpdating || !newDeptName || !newDeptAmount}
            >
              {isUpdating ? 'Adding...' : 'Add Department'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </motion.div>
  );
};