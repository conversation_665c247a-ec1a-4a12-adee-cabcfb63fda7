import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { 
  Sparkles, 
  Plus, 
  Trash2, 
  Edit2, 
  AlertCircle,
  TrendingUp,
  Calendar,
  Zap
} from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

export interface AllocationRule {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  priority: number;
  trigger: {
    type: 'time_based' | 'threshold_based' | 'event_based';
    condition: string;
    value: any;
  };
  action: {
    type: 'redistribute' | 'reallocate' | 'lock' | 'notify';
    targets: string[];
    parameters: Record<string, any>;
  };
  constraints?: {
    minPercentage?: number;
    maxPercentage?: number;
    protectedCategories?: string[];
    protectedDepartments?: string[];
  };
  lastTriggered?: Date;
  createdAt: Date;
  updatedAt: Date;
}

interface AutoAllocationRulesProps {
  rules: AllocationRule[];
  departments: Array<{ id: string; name: string }>;
  categories: Array<{ id: string; name: string }>;
  onAddRule: (rule: Partial<AllocationRule>) => Promise<void>;
  onUpdateRule: (id: string, updates: Partial<AllocationRule>) => Promise<void>;
  onDeleteRule: (id: string) => Promise<void>;
  onToggleRule: (id: string, enabled: boolean) => Promise<void>;
  onExecuteRule: (id: string) => Promise<void>;
  loading?: boolean;
}

const TRIGGER_TYPES = [
  { value: 'time_based', label: 'Time-based', icon: Calendar },
  { value: 'threshold_based', label: 'Threshold-based', icon: TrendingUp },
  { value: 'event_based', label: 'Event-based', icon: Zap },
];

const ACTION_TYPES = [
  { value: 'redistribute', label: 'Redistribute Budget' },
  { value: 'reallocate', label: 'Reallocate to Departments' },
  { value: 'lock', label: 'Lock Allocations' },
  { value: 'notify', label: 'Send Notification' },
];

const TIME_CONDITIONS = [
  { value: 'quarterly', label: 'Start of Quarter' },
  { value: 'monthly', label: 'Start of Month' },
  { value: 'weekly', label: 'Start of Week' },
  { value: 'custom', label: 'Custom Date' },
];

const THRESHOLD_CONDITIONS = [
  { value: 'budget_used', label: 'Budget Used %' },
  { value: 'budget_remaining', label: 'Budget Remaining %' },
  { value: 'overspend', label: 'Overspend Amount' },
  { value: 'underspend', label: 'Underspend Amount' },
];

export const AutoAllocationRules: React.FC<AutoAllocationRulesProps> = ({
  rules,
  onAddRule,
  onUpdateRule,
  onDeleteRule,
  onToggleRule,
  onExecuteRule,
  loading = false,
}) => {
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [editingRule, setEditingRule] = useState<AllocationRule | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);
  
  // Form state
  const [formData, setFormData] = useState<Partial<AllocationRule>>({
    name: '',
    description: '',
    enabled: true,
    priority: 1,
    trigger: {
      type: 'time_based',
      condition: 'quarterly',
      value: null,
    },
    action: {
      type: 'redistribute',
      targets: [],
      parameters: {},
    },
    constraints: {
      minPercentage: 5,
      maxPercentage: 50,
      protectedCategories: [],
      protectedDepartments: [],
    },
  });

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      enabled: true,
      priority: 1,
      trigger: {
        type: 'time_based',
        condition: 'quarterly',
        value: null,
      },
      action: {
        type: 'redistribute',
        targets: [],
        parameters: {},
      },
      constraints: {
        minPercentage: 5,
        maxPercentage: 50,
        protectedCategories: [],
        protectedDepartments: [],
      },
    });
  };

  const handleSubmit = async () => {
    if (!formData.name || !formData.trigger?.condition) return;

    setIsUpdating(true);
    try {
      if (editingRule) {
        await onUpdateRule(editingRule.id, formData);
      } else {
        await onAddRule(formData);
      }
      setShowAddDialog(false);
      setEditingRule(null);
      resetForm();
    } catch (error) {
      console.error('Failed to save rule:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleEdit = (rule: AllocationRule) => {
    setEditingRule(rule);
    setFormData(rule);
    setShowAddDialog(true);
  };

  const handleDelete = async (id: string) => {
    const confirmed = window.confirm('Are you sure you want to delete this rule?');
    if (!confirmed) return;

    setIsUpdating(true);
    try {
      await onDeleteRule(id);
    } catch (error) {
      console.error('Failed to delete rule:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleToggle = async (id: string, enabled: boolean) => {
    try {
      await onToggleRule(id, enabled);
    } catch (error) {
      console.error('Failed to toggle rule:', error);
    }
  };

  const handleExecute = async (id: string) => {
    const confirmed = window.confirm('Are you sure you want to execute this rule now?');
    if (!confirmed) return;

    setIsUpdating(true);
    try {
      await onExecuteRule(id);
    } catch (error) {
      console.error('Failed to execute rule:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  const getTriggerIcon = (type: string) => {
    const trigger = TRIGGER_TYPES.find(t => t.value === type);
    return trigger ? trigger.icon : Zap;
  };

  const getActionBadgeColor = (type: string) => {
    switch (type) {
      case 'redistribute': return 'bg-blue-500';
      case 'reallocate': return 'bg-green-500';
      case 'lock': return 'bg-orange-500';
      case 'notify': return 'bg-purple-500';
      default: return 'bg-gray-500';
    }
  };

  if (loading) {
    return (
      <Card className="p-6">
        <Skeleton className="h-6 w-48 mb-4" />
        <div className="space-y-4">
          {[...Array(3)].map((_, idx) => (
            <Skeleton key={idx} className="h-24 w-full" />
          ))}
        </div>
      </Card>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.3 }}
    >
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <Sparkles className="h-5 w-5 text-muted-foreground" />
            <h3 className="text-lg font-semibold">Auto-allocation Rules</h3>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              resetForm();
              setEditingRule(null);
              setShowAddDialog(true);
            }}
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Rule
          </Button>
        </div>

        {/* Rules List */}
        <div className="space-y-3">
          <AnimatePresence>
            {rules.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <Sparkles className="h-12 w-12 mx-auto mb-3 opacity-50" />
                <p>No allocation rules configured</p>
                <p className="text-sm">Create rules to automate budget management</p>
              </div>
            ) : (
              rules.map((rule) => {
                const TriggerIcon = getTriggerIcon(rule.trigger?.type || 'time_based');
                
                return (
                  <motion.div
                    key={rule.id}
                    layout
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 20 }}
                    className="border rounded-lg p-4"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <TriggerIcon className="h-5 w-5 text-muted-foreground" />
                          <h4 className="font-medium">{rule.name}</h4>
                          <Badge className={`${getActionBadgeColor(rule.action?.type || 'notify')} text-white`}>
                            {ACTION_TYPES.find(a => a.value === (rule.action?.type || 'notify'))?.label}
                          </Badge>
                          {!rule.enabled && (
                            <Badge variant="secondary">Disabled</Badge>
                          )}
                          <Badge variant="outline">Priority {rule.priority}</Badge>
                        </div>
                        <p className="text-sm text-muted-foreground mb-2">
                          {rule.description}
                        </p>
                        <div className="flex items-center gap-4 text-xs text-muted-foreground">
                          <span>
                            Trigger: {rule.trigger.condition}
                          </span>
                          {rule.lastTriggered && (
                            <span>
                              Last triggered: {new Date(rule.lastTriggered).toLocaleDateString()}
                            </span>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Switch
                          checked={rule.enabled}
                          onCheckedChange={(checked) => handleToggle(rule.id, checked)}
                        />
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                size="icon"
                                variant="ghost"
                                onClick={() => handleExecute(rule.id)}
                                disabled={!rule.enabled || isUpdating}
                              >
                                <Zap className="h-4 w-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>Execute rule now</TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                size="icon"
                                variant="ghost"
                                onClick={() => handleEdit(rule)}
                              >
                                <Edit2 className="h-4 w-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>Edit rule</TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                size="icon"
                                variant="ghost"
                                onClick={() => handleDelete(rule.id)}
                                disabled={isUpdating}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>Delete rule</TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                    </div>
                  </motion.div>
                );
              })
            )}
          </AnimatePresence>
        </div>
      </Card>

      {/* Add/Edit Rule Dialog */}
      <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              {editingRule ? 'Edit Allocation Rule' : 'Create New Allocation Rule'}
            </DialogTitle>
            <DialogDescription>
              Set up automated rules to manage budget allocations based on conditions.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            {/* Basic Info */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Rule Name</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="e.g., Quarterly Rebalancing"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="priority">Priority</Label>
                <Input
                  id="priority"
                  type="number"
                  min={1}
                  max={10}
                  value={formData.priority}
                  onChange={(e) => setFormData({ 
                    ...formData, 
                    priority: parseInt(e.target.value) || 1 
                  })}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Input
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Describe what this rule does"
              />
            </div>

            {/* Trigger Configuration */}
            <div className="space-y-3">
              <h4 className="font-medium flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Trigger Configuration
              </h4>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Trigger Type</Label>
                  <Select
                    value={formData.trigger?.type}
                    onValueChange={(value) => setFormData({
                      ...formData,
                      trigger: { ...formData.trigger!, type: value as any }
                    })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {TRIGGER_TYPES.map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label>Condition</Label>
                  <Select
                    value={formData.trigger?.condition}
                    onValueChange={(value) => setFormData({
                      ...formData,
                      trigger: { ...formData.trigger!, condition: value }
                    })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {(formData.trigger?.type === 'time_based' ? TIME_CONDITIONS : THRESHOLD_CONDITIONS).map((cond) => (
                        <SelectItem key={cond.value} value={cond.value}>
                          {cond.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            {/* Action Configuration */}
            <div className="space-y-3">
              <h4 className="font-medium flex items-center gap-2">
                <Zap className="h-4 w-4" />
                Action Configuration
              </h4>
              <div className="space-y-2">
                <Label>Action Type</Label>
                <Select
                  value={formData.action?.type}
                  onValueChange={(value) => setFormData({
                    ...formData,
                    action: { ...formData.action!, type: value as any }
                  })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {ACTION_TYPES.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Constraints */}
            <div className="space-y-3">
              <h4 className="font-medium flex items-center gap-2">
                <AlertCircle className="h-4 w-4" />
                Constraints
              </h4>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Min Allocation %</Label>
                  <Input
                    type="number"
                    min={0}
                    max={100}
                    value={formData.constraints?.minPercentage || 5}
                    onChange={(e) => setFormData({
                      ...formData,
                      constraints: {
                        ...formData.constraints,
                        minPercentage: parseFloat(e.target.value) || 5
                      }
                    })}
                  />
                </div>
                <div className="space-y-2">
                  <Label>Max Allocation %</Label>
                  <Input
                    type="number"
                    min={0}
                    max={100}
                    value={formData.constraints?.maxPercentage || 50}
                    onChange={(e) => setFormData({
                      ...formData,
                      constraints: {
                        ...formData.constraints,
                        maxPercentage: parseFloat(e.target.value) || 50
                      }
                    })}
                  />
                </div>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setShowAddDialog(false);
                setEditingRule(null);
                resetForm();
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={isUpdating || !formData.name}
            >
              {isUpdating ? 'Saving...' : editingRule ? 'Update Rule' : 'Create Rule'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </motion.div>
  );
};