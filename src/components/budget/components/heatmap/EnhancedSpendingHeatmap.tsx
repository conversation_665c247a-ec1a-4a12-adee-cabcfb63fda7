import React, { useMemo, useState } from 'react';
import { motion } from 'framer-motion';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { 
  Flame,
  Calendar,
  TrendingUp,
  Filter,
  Info,
  Download,
  ChevronLeft,
  ChevronRight,
  DollarSign,
  Activity
} from 'lucide-react';
import { 
  format, 
  startOfMonth, 
  endOfMonth, 
  eachDayOfInterval,
  getDay,
  startOfWeek,
  endOfWeek,
  isToday,
  isSameMonth,
  addMonths,
  subMonths
} from 'date-fns';
import { cn } from '@/lib/utils';
import { formatCurrency } from '../../utils/formatters';
import { SoftCard, AnimatedCounter, SoftBadge } from '../ui/SoftUIComponents';

export interface DailySpending {
  date: string;
  amount: number;
  transactions: number;
  categories: Record<string, number>;
  departments?: Record<string, number>;
  topExpense?: {
    description: string;
    amount: number;
    category: string;
  };
}

export interface HeatmapData {
  dailyData: DailySpending[];
  minAmount: number;
  maxAmount: number;
  totalAmount: number;
  averageDaily: number;
  peakDay: string;
  categories: string[];
  departments?: string[];
}

export interface EnhancedSpendingHeatmapProps {
  data: HeatmapData;
  currentMonth?: Date;
  viewMode?: 'month' | 'week' | 'quarter';
  colorScheme?: 'blue' | 'green' | 'orange' | 'purple' | 'red' | 'soft';
  showWeekends?: boolean;
  showLegend?: boolean;
  showStats?: boolean;
  onDayClick?: (date: string, data: DailySpending) => void;
  onExport?: (format: 'png' | 'csv') => void;
  loading?: boolean;
}

// Enhanced color schemes with better contrast and visibility
const enhancedColorSchemes = {
  blue: [
    { bg: 'bg-blue-50', text: 'text-blue-900', border: 'border-blue-200' },
    { bg: 'bg-blue-100', text: 'text-blue-900', border: 'border-blue-300' },
    { bg: 'bg-blue-300', text: 'text-blue-900', border: 'border-blue-400' },
    { bg: 'bg-blue-500', text: 'text-white', border: 'border-blue-600' },
    { bg: 'bg-blue-700', text: 'text-white', border: 'border-blue-800' },
  ],
  green: [
    { bg: 'bg-emerald-50', text: 'text-emerald-900', border: 'border-emerald-200' },
    { bg: 'bg-emerald-100', text: 'text-emerald-900', border: 'border-emerald-300' },
    { bg: 'bg-emerald-300', text: 'text-emerald-900', border: 'border-emerald-400' },
    { bg: 'bg-emerald-500', text: 'text-white', border: 'border-emerald-600' },
    { bg: 'bg-emerald-700', text: 'text-white', border: 'border-emerald-800' },
  ],
  soft: [
    { 
      bg: 'bg-gradient-to-br from-purple-50 to-indigo-50', 
      text: 'text-gray-700', 
      border: 'border-purple-100',
      shadow: 'shadow-sm'
    },
    { 
      bg: 'bg-gradient-to-br from-purple-100 to-indigo-100', 
      text: 'text-gray-800', 
      border: 'border-purple-200',
      shadow: 'shadow-md shadow-purple-100/50'
    },
    { 
      bg: 'bg-gradient-to-br from-purple-200 to-indigo-200', 
      text: 'text-gray-900', 
      border: 'border-purple-300',
      shadow: 'shadow-md shadow-purple-200/50'
    },
    { 
      bg: 'bg-gradient-to-br from-purple-400 to-indigo-400', 
      text: 'text-white', 
      border: 'border-purple-500',
      shadow: 'shadow-lg shadow-purple-300/50'
    },
    { 
      bg: 'bg-gradient-to-br from-purple-600 to-indigo-600', 
      text: 'text-white', 
      border: 'border-purple-700',
      shadow: 'shadow-xl shadow-purple-400/50'
    },
  ],
  orange: [
    { bg: 'bg-orange-50', text: 'text-orange-900', border: 'border-orange-200' },
    { bg: 'bg-orange-100', text: 'text-orange-900', border: 'border-orange-300' },
    { bg: 'bg-orange-300', text: 'text-orange-900', border: 'border-orange-400' },
    { bg: 'bg-orange-500', text: 'text-white', border: 'border-orange-600' },
    { bg: 'bg-orange-700', text: 'text-white', border: 'border-orange-800' },
  ],
  purple: [
    { bg: 'bg-purple-50', text: 'text-purple-900', border: 'border-purple-200' },
    { bg: 'bg-purple-100', text: 'text-purple-900', border: 'border-purple-300' },
    { bg: 'bg-purple-300', text: 'text-purple-900', border: 'border-purple-400' },
    { bg: 'bg-purple-500', text: 'text-white', border: 'border-purple-600' },
    { bg: 'bg-purple-700', text: 'text-white', border: 'border-purple-800' },
  ],
  red: [
    { bg: 'bg-red-50', text: 'text-red-900', border: 'border-red-200' },
    { bg: 'bg-red-100', text: 'text-red-900', border: 'border-red-300' },
    { bg: 'bg-red-300', text: 'text-red-900', border: 'border-red-400' },
    { bg: 'bg-red-500', text: 'text-white', border: 'border-red-600' },
    { bg: 'bg-red-700', text: 'text-white', border: 'border-red-800' },
  ],
};

const getIntensityLevel = (amount: number, min: number, max: number): number => {
  if (amount === 0) return -1; // No spending
  if (max === min) return 2; // All values are the same
  
  const normalized = (amount - min) / (max - min);
  if (normalized < 0.2) return 0;
  if (normalized < 0.4) return 1;
  if (normalized < 0.6) return 2;
  if (normalized < 0.8) return 3;
  return 4;
};

export const EnhancedSpendingHeatmap: React.FC<EnhancedSpendingHeatmapProps> = ({
  data,
  currentMonth: initialMonth = new Date(),
  viewMode = 'month',
  colorScheme = 'soft',
  showWeekends = true,
  showLegend = true,
  showStats = true,
  onDayClick,
  onExport,
  loading = false
}) => {
  const [selectedMonth, setSelectedMonth] = useState(initialMonth);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [hoveredDay, setHoveredDay] = useState<string | null>(null);

  // Create a map of daily spending for quick lookup
  const dailyDataMap = useMemo(() => {
    const map: Record<string, DailySpending> = {};
    data.dailyData.forEach(day => {
      map[day.date] = day;
    });
    return map;
  }, [data.dailyData]);

  // Filter data by category if selected
  const filteredData = useMemo(() => {
    if (selectedCategory === 'all') return data;

    const filtered = data.dailyData.map(day => ({
      ...day,
      amount: day.categories[selectedCategory] || 0
    }));

    const amounts = filtered.map(d => d.amount).filter(a => a > 0);
    const minAmount = Math.min(...amounts, 0);
    const maxAmount = Math.max(...amounts, 0);
    const totalAmount = filtered.reduce((sum, d) => sum + d.amount, 0);
    const averageDaily = totalAmount / filtered.length;
    
    let peakDay = filtered[0]?.date || '';
    let peakAmount = filtered[0]?.amount || 0;
    filtered.forEach(day => {
      if (day.amount > peakAmount) {
        peakAmount = day.amount;
        peakDay = day.date;
      }
    });

    return {
      ...data,
      dailyData: filtered,
      minAmount,
      maxAmount,
      totalAmount,
      averageDaily,
      peakDay
    };
  }, [data, selectedCategory]);

  // Calculate calendar days
  const calendarDays = useMemo(() => {
    const start = startOfMonth(selectedMonth);
    const end = endOfMonth(selectedMonth);
    const startWeek = startOfWeek(start);
    const endWeek = endOfWeek(end);
    
    return eachDayOfInterval({ start: startWeek, end: endWeek });
  }, [selectedMonth]);

  // Calculate intensity statistics
  const intensityStats = useMemo(() => {
    const monthDays = calendarDays.filter(day => isSameMonth(day, selectedMonth));
    const intensities = monthDays.map(day => {
      const dateKey = format(day, 'yyyy-MM-dd');
      const dayData = dailyDataMap[dateKey];
      return dayData ? getIntensityLevel(dayData.amount, filteredData.minAmount, filteredData.maxAmount) : -1;
    });

    const levels = Array(5).fill(0);
    let zeroDays = 0;
    
    intensities.forEach(level => {
      if (level === -1) zeroDays++;
      else levels[level]++;
    });

    return { levels, zeroDays };
  }, [calendarDays, selectedMonth, dailyDataMap, filteredData]);

  const handlePreviousMonth = () => {
    setSelectedMonth(prev => subMonths(prev, 1));
  };

  const handleNextMonth = () => {
    setSelectedMonth(prev => addMonths(prev, 1));
  };

  const handleToday = () => {
    setSelectedMonth(new Date());
  };

  const renderDay = (date: Date) => {
    const dateKey = format(date, 'yyyy-MM-dd');
    const dayData = dailyDataMap[dateKey];
    const isCurrentMonth = isSameMonth(date, selectedMonth);
    const isWeekend = !showWeekends && (getDay(date) === 0 || getDay(date) === 6);
    
    if (isWeekend) return null;

    const amount = dayData?.amount || 0;
    const intensity = amount > 0 ? getIntensityLevel(amount, filteredData.minAmount, filteredData.maxAmount) : -1;
    const colorStyle = intensity >= 0 
      ? enhancedColorSchemes[colorScheme as keyof typeof enhancedColorSchemes][intensity] 
      : { bg: 'bg-gray-50', text: 'text-gray-400', border: 'border-gray-200' };

    // Format amount for display
    const displayAmount = amount > 0 ? (
      amount >= 1000 ? `${(amount / 1000).toFixed(1)}k` : amount.toFixed(0)
    ) : '';

    return (
      <TooltipProvider key={dateKey}>
        <Tooltip>
          <TooltipTrigger asChild>
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              whileHover={{ scale: 1.08, zIndex: 10 }}
              transition={{ duration: 0.2 }}
              className={cn(
                "relative aspect-square rounded-xl cursor-pointer transition-all",
                "flex flex-col items-center justify-center",
                "border-2",
                colorStyle.bg,
                colorStyle.border,
                (colorStyle as any).shadow || '',
                isCurrentMonth ? "" : "opacity-30",
                isToday(date) && "ring-2 ring-primary ring-offset-2",
                hoveredDay === dateKey && "shadow-2xl z-20 transform"
              )}
              onClick={() => dayData && onDayClick?.(dateKey, dayData)}
              onMouseEnter={() => setHoveredDay(dateKey)}
              onMouseLeave={() => setHoveredDay(null)}
            >
              {/* Day number - always visible */}
              <span className={cn(
                "text-lg font-bold",
                colorStyle.text
              )}>
                {format(date, 'd')}
              </span>
              
              {/* Amount display - enhanced visibility */}
              {displayAmount && (
                <span className={cn(
                  "text-xs font-semibold mt-0.5",
                  colorStyle.text,
                  "drop-shadow-sm"
                )}>
                  ${displayAmount}
                </span>
              )}
              
              {/* Transaction indicator */}
              {dayData && dayData.transactions > 0 && (
                <div className="absolute top-1 right-1">
                  <div className={cn(
                    "w-2 h-2 rounded-full animate-pulse",
                    intensity >= 3 ? "bg-white/80" : "bg-primary"
                  )} />
                </div>
              )}
            </motion.div>
          </TooltipTrigger>
          <TooltipContent className="max-w-xs p-4 bg-white/95 backdrop-blur-xl border-white/20 shadow-xl">
            <div className="space-y-2">
              <p className="font-semibold text-base">{format(date, 'MMMM d, yyyy')}</p>
              {dayData ? (
                <>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Total Spent:</span>
                    <span className="font-bold text-base">{formatCurrency(dayData.amount)}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Transactions:</span>
                    <SoftBadge variant="info">{dayData.transactions}</SoftBadge>
                  </div>
                  {dayData.topExpense && (
                    <div className="text-sm text-gray-600 mt-2 pt-2 border-t border-gray-200">
                      <p className="font-medium text-gray-900 mb-1">Largest expense:</p>
                      <p className="text-gray-700">{dayData.topExpense.description}</p>
                      <div className="flex items-center justify-between mt-1">
                        <SoftBadge variant="default">{dayData.topExpense.category}</SoftBadge>
                        <span className="font-semibold">{formatCurrency(dayData.topExpense.amount)}</span>
                      </div>
                    </div>
                  )}
                </>
              ) : (
                <p className="text-sm text-gray-500 italic">No spending recorded</p>
              )}
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  };

  if (loading) {
    return (
      <SoftCard variant="glass" className="p-6">
        <Skeleton className="h-8 w-48 mb-4" />
        <div className="grid grid-cols-7 gap-2">
          {Array.from({ length: 35 }).map((_, i) => (
            <Skeleton key={i} className="aspect-square rounded-xl" />
          ))}
        </div>
      </SoftCard>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, ease: [0.23, 1, 0.32, 1] }}
    >
      <SoftCard variant="glass" className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-xl bg-gradient-to-br from-orange-400 to-red-500">
              <Flame className="h-5 w-5 text-white" />
            </div>
            <h3 className="text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
              Spending Heatmap
            </h3>
          </div>
          <div className="flex items-center gap-2">
            {/* Category Filter */}
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-40 backdrop-blur-sm bg-white/50">
                <SelectValue placeholder="All Categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  <SelectLabel>Filter by Category</SelectLabel>
                  <SelectItem value="all">All Categories</SelectItem>
                  {data.categories.map(category => (
                    <SelectItem key={category} value={category}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectGroup>
              </SelectContent>
            </Select>

            {/* Export Button */}
            {onExport && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onExport('png')}
                className="backdrop-blur-sm bg-white/50"
              >
                <Download className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>

        {/* Month Navigation */}
        <div className="flex items-center justify-between mb-6 p-3 rounded-xl bg-gradient-to-r from-gray-50 to-gray-100/50">
          <Button
            variant="ghost"
            size="icon"
            onClick={handlePreviousMonth}
            className="hover:bg-white/50"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <div className="flex items-center gap-3">
            <h4 className="text-lg font-semibold">
              {format(selectedMonth, 'MMMM yyyy')}
            </h4>
            <Button
              variant="outline"
              size="sm"
              onClick={handleToday}
              className="backdrop-blur-sm bg-white/50"
            >
              Today
            </Button>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={handleNextMonth}
            className="hover:bg-white/50"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>

        {/* Statistics Cards */}
        {showStats && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-6">
            <SoftCard variant="gradient" className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-blue-500/10">
                  <DollarSign className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <p className="text-xs text-gray-600 font-medium">Total Spent</p>
                  <p className="text-lg font-bold">
                    <AnimatedCounter value={filteredData.totalAmount} prefix="$" />
                  </p>
                </div>
              </div>
            </SoftCard>
            <SoftCard variant="gradient" className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-green-500/10">
                  <Activity className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <p className="text-xs text-gray-600 font-medium">Daily Average</p>
                  <p className="text-lg font-bold">
                    <AnimatedCounter value={filteredData.averageDaily} prefix="$" />
                  </p>
                </div>
              </div>
            </SoftCard>
            <SoftCard variant="gradient" className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-orange-500/10">
                  <TrendingUp className="h-5 w-5 text-orange-600" />
                </div>
                <div>
                  <p className="text-xs text-gray-600 font-medium">Peak Day</p>
                  <p className="text-lg font-bold">
                    {filteredData.peakDay ? format(new Date(filteredData.peakDay), 'MMM d') : 'N/A'}
                  </p>
                </div>
              </div>
            </SoftCard>
            <SoftCard variant="gradient" className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-purple-500/10">
                  <Calendar className="h-5 w-5 text-purple-600" />
                </div>
                <div>
                  <p className="text-xs text-gray-600 font-medium">Active Days</p>
                  <p className="text-lg font-bold">
                    {intensityStats.levels.reduce((sum, count) => sum + count, 0)}
                  </p>
                </div>
              </div>
            </SoftCard>
          </div>
        )}

        {/* Weekday Headers */}
        <div className={cn(
          "grid gap-2 mb-3",
          showWeekends ? "grid-cols-7" : "grid-cols-5"
        )}>
          {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']
            .filter((_, index) => showWeekends || (index !== 0 && index !== 6))
            .map((day) => (
              <div key={day} className="text-sm font-semibold text-center text-gray-600">
                {day}
              </div>
            ))}
        </div>

        {/* Calendar Grid */}
        <div className={cn(
          "grid gap-2",
          showWeekends ? "grid-cols-7" : "grid-cols-5"
        )}>
          {calendarDays.map(renderDay)}
        </div>

        {/* Enhanced Legend */}
        {showLegend && (
          <div className="mt-6 p-4 rounded-xl bg-gradient-to-r from-gray-50 to-gray-100/50">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <span className="text-sm font-medium text-gray-600">Less</span>
                <div className="flex gap-1.5">
                  <div className="w-8 h-8 rounded-lg bg-gray-50 border-2 border-gray-200 flex items-center justify-center">
                    <span className="text-xs text-gray-400">$0</span>
                  </div>
                  {enhancedColorSchemes[colorScheme as keyof typeof enhancedColorSchemes].map((style, index) => (
                    <div 
                      key={index} 
                      className={cn(
                        "w-8 h-8 rounded-lg border-2 flex items-center justify-center",
                        style.bg,
                        style.border,
                        (style as any).shadow || ''
                      )}
                    >
                      <span className={cn("text-xs font-bold", style.text)}>
                        {index + 1}
                      </span>
                    </div>
                  ))}
                </div>
                <span className="text-sm font-medium text-gray-600">More</span>
              </div>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="ghost" size="icon" className="hover:bg-white/50">
                      <Info className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="text-sm">Click on any day to view detailed spending information</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
        )}

        {/* Intensity Distribution */}
        {showStats && (
          <div className="mt-4 p-4 bg-gradient-to-r from-gray-50/50 to-gray-100/30 rounded-xl">
            <p className="text-sm font-semibold mb-3 text-gray-700">Spending Distribution</p>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2 text-sm">
              <div className="flex justify-between p-2 rounded-lg bg-white/50">
                <span className="text-gray-600">No spending</span>
                <span className="font-semibold">{intensityStats.zeroDays} days</span>
              </div>
              {['Low', 'Medium-Low', 'Medium', 'Medium-High', 'High'].map((label, index) => (
                <div key={label} className="flex justify-between p-2 rounded-lg bg-white/50">
                  <span className="text-gray-600">{label}</span>
                  <span className="font-semibold">{intensityStats.levels[index]} days</span>
                </div>
              ))}
            </div>
          </div>
        )}
      </SoftCard>
    </motion.div>
  );
};