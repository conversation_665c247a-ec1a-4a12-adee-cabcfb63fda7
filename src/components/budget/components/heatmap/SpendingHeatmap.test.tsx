import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { SpendingHeatmap } from './SpendingHeatmap';
import type { HeatmapData } from './SpendingHeatmap';

describe('SpendingHeatmap', () => {
  const mockData: HeatmapData = {
    dailyData: [
      {
        date: '2024-01-15',
        amount: 1500,
        transactions: 3,
        categories: {
          'Technical Training': 1000,
          'Conferences': 500
        },
        topExpense: {
          description: 'AWS Certification',
          amount: 800,
          category: 'Technical Training'
        }
      },
      {
        date: '2024-01-20',
        amount: 2500,
        transactions: 2,
        categories: {
          'Soft Skills': 2000,
          'Materials': 500
        },
        topExpense: {
          description: 'Leadership Workshop',
          amount: 2000,
          category: 'Soft Skills'
        }
      },
      {
        date: '2024-01-25',
        amount: 500,
        transactions: 1,
        categories: {
          'Materials': 500
        }
      }
    ],
    minAmount: 500,
    maxAmount: 2500,
    totalAmount: 4500,
    averageDaily: 1500,
    peakDay: '2024-01-20',
    categories: ['Technical Training', 'Conferences', 'Soft Skills', 'Materials']
  };

  it('renders spending heatmap with correct title', () => {
    render(
      <SpendingHeatmap
        data={mockData}
        loading={false}
      />
    );

    expect(screen.getByText('Spending Heatmap')).toBeInTheDocument();
  });

  it('displays loading state', () => {
    render(
      <SpendingHeatmap
        data={mockData}
        loading={true}
      />
    );

    // Should show skeleton loaders
    const skeletons = document.querySelectorAll('[class*="skeleton"]');
    expect(skeletons.length).toBeGreaterThan(0);
  });

  it('shows statistics cards when enabled', () => {
    render(
      <SpendingHeatmap
        data={mockData}
        showStats={true}
        loading={false}
      />
    );

    expect(screen.getByText('Total Spent')).toBeInTheDocument();
    expect(screen.getByText('$4,500.00')).toBeInTheDocument();
    expect(screen.getByText('Daily Average')).toBeInTheDocument();
    expect(screen.getByText('$1,500.00')).toBeInTheDocument();
    expect(screen.getByText('Peak Day')).toBeInTheDocument();
    expect(screen.getByText('Jan 20')).toBeInTheDocument();
  });

  it('handles month navigation', () => {
    render(
      <SpendingHeatmap
        data={mockData}
        currentMonth={new Date(2024, 0, 1)}
        loading={false}
      />
    );

    expect(screen.getByText('January 2024')).toBeInTheDocument();

    // Click next month
    const nextButton = screen.getAllByRole('button').find(btn => 
      btn.querySelector('[class*="ChevronRight"]')
    );
    if (nextButton) {
      fireEvent.click(nextButton);
    }

    // Should show February
    expect(screen.getByText('February 2024')).toBeInTheDocument();
  });

  it('shows legend when enabled', () => {
    render(
      <SpendingHeatmap
        data={mockData}
        showLegend={true}
        loading={false}
      />
    );

    expect(screen.getByText('Less')).toBeInTheDocument();
    expect(screen.getByText('More')).toBeInTheDocument();
  });

  it('handles category filtering', () => {
    render(
      <SpendingHeatmap
        data={mockData}
        loading={false}
      />
    );

    // Click on category filter
    const selectTrigger = screen.getByRole('combobox');
    fireEvent.click(selectTrigger);

    // Should show category options
    expect(screen.getByText('All Categories')).toBeInTheDocument();
    expect(screen.getByText('Technical Training')).toBeInTheDocument();
    expect(screen.getByText('Soft Skills')).toBeInTheDocument();
  });

  it('handles day click events', () => {
    const handleDayClick = jest.fn();

    render(
      <SpendingHeatmap
        data={mockData}
        onDayClick={handleDayClick}
        loading={false}
      />
    );

    // Find a day with data and click it
    const dayElements = screen.getAllByText('15');
    if (dayElements.length > 0) {
      fireEvent.click(dayElements[0]);
      
      expect(handleDayClick).toHaveBeenCalledWith(
        '2024-01-15',
        expect.objectContaining({
          amount: 1500,
          transactions: 3
        })
      );
    }
  });

  it('displays correct color intensity based on spending', () => {
    render(
      <SpendingHeatmap
        data={mockData}
        colorScheme="blue"
        loading={false}
      />
    );

    // Should have different intensity colors for different spending levels
    const dayElements = document.querySelectorAll('[class*="bg-blue"]');
    expect(dayElements.length).toBeGreaterThan(0);
  });

  it('hides weekends when disabled', () => {
    render(
      <SpendingHeatmap
        data={mockData}
        showWeekends={false}
        loading={false}
      />
    );

    // Should only show 5 weekday headers
    const weekdayHeaders = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri'];
    weekdayHeaders.forEach(day => {
      expect(screen.getByText(day)).toBeInTheDocument();
    });
    
    // Should not show weekend headers
    expect(screen.queryByText('Sun')).not.toBeInTheDocument();
    expect(screen.queryByText('Sat')).not.toBeInTheDocument();
  });

  it('shows spending distribution statistics', () => {
    render(
      <SpendingHeatmap
        data={mockData}
        showStats={true}
        loading={false}
      />
    );

    expect(screen.getByText('Spending Distribution')).toBeInTheDocument();
    expect(screen.getByText('No spending')).toBeInTheDocument();
    expect(screen.getByText(/days/)).toBeInTheDocument();
  });
});