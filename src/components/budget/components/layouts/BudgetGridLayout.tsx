import React from 'react';
import { cn } from '@/lib/utils';

interface BudgetGridLayoutProps {
  children: React.ReactNode;
  className?: string;
}

/**
 * Hierarchical Grid Layout System for Budget Components
 * Uses a 12-column grid with defined visual hierarchy levels
 */
export const BudgetGridLayout: React.FC<BudgetGridLayoutProps> = ({ children, className }) => {
  return (
    <div className={cn("space-y-4", className)}>
      {children}
    </div>
  );
};

// Primary Hero Section - Most Important Information
export const HeroSection: React.FC<{ children: React.ReactNode; className?: string }> = ({ 
  children, 
  className 
}) => {
  return (
    <section className={cn("grid grid-cols-12 gap-4", className)}>
      {children}
    </section>
  );
};

// Primary Card - 8 columns (2/3 width)
export const PrimaryCard: React.FC<{ children: React.ReactNode; className?: string }> = ({ 
  children, 
  className 
}) => {
  return (
    <div className={cn(
      "col-span-12 lg:col-span-8",
      "order-1 lg:order-1",
      className
    )}>
      {children}
    </div>
  );
};

// Secondary Card - 4 columns (1/3 width)
export const SecondaryCard: React.FC<{ children: React.ReactNode; className?: string }> = ({ 
  children, 
  className 
}) => {
  return (
    <div className={cn(
      "col-span-12 lg:col-span-4",
      "order-2 lg:order-2",
      className
    )}>
      {children}
    </div>
  );
};

// Metrics Grid - For small stat cards
export const MetricsGrid: React.FC<{ children: React.ReactNode; className?: string }> = ({ 
  children, 
  className 
}) => {
  return (
    <section className={cn(
      "grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-3",
      className
    )}>
      {children}
    </section>
  );
};

// Content Grid - For main content area
export const ContentGrid: React.FC<{ children: React.ReactNode; className?: string }> = ({ 
  children, 
  className 
}) => {
  return (
    <section className={cn("grid grid-cols-12 gap-4", className)}>
      {children}
    </section>
  );
};

// Main Content Area - 7-8 columns
export const MainContent: React.FC<{ children: React.ReactNode; className?: string }> = ({ 
  children, 
  className 
}) => {
  return (
    <div className={cn(
      "col-span-12 lg:col-span-7",
      className
    )}>
      {children}
    </div>
  );
};

// Sidebar Content - 4-5 columns
export const SidebarContent: React.FC<{ children: React.ReactNode; className?: string }> = ({ 
  children, 
  className 
}) => {
  return (
    <div className={cn(
      "col-span-12 lg:col-span-5",
      className
    )}>
      {children}
    </div>
  );
};

// Full Width Section
export const FullWidthSection: React.FC<{ children: React.ReactNode; className?: string }> = ({ 
  children, 
  className 
}) => {
  return (
    <section className={cn("w-full", className)}>
      {children}
    </section>
  );
};

// Compact Metric Card
export const MetricCard: React.FC<{ 
  children: React.ReactNode; 
  variant?: 'default' | 'primary' | 'success' | 'warning' | 'danger';
  className?: string;
}> = ({ 
  children, 
  variant = 'default',
  className 
}) => {
  const variantStyles = {
    default: 'border-l-2 border-l-gray-400',
    primary: 'border-l-2 border-l-primary',
    success: 'border-l-2 border-l-green-500',
    warning: 'border-l-2 border-l-orange-500',
    danger: 'border-l-2 border-l-red-500',
  };

  return (
    <div className={cn(
      "bg-card rounded-lg border shadow-sm hover:shadow-md transition-all duration-200",
      "p-3",
      variantStyles[variant],
      className
    )}>
      {children}
    </div>
  );
};

// Section Header
export const SectionHeader: React.FC<{ 
  title: string;
  description?: string;
  action?: React.ReactNode;
  className?: string;
}> = ({ 
  title, 
  description,
  action,
  className 
}) => {
  return (
    <div className={cn("flex items-center justify-between mb-3", className)}>
      <div>
        <h2 className="text-base font-semibold text-foreground">{title}</h2>
        {description && (
          <p className="text-xs text-muted-foreground mt-0.5">{description}</p>
        )}
      </div>
      {action && (
        <div className="flex items-center gap-2">
          {action}
        </div>
      )}
    </div>
  );
};

// Visual Hierarchy Levels
export const hierarchyClasses = {
  // Level 1 - Primary Focus (Hero metrics, main KPIs)
  level1: {
    title: "text-lg font-bold",
    value: "text-2xl font-bold tracking-tight",
    label: "text-xs text-muted-foreground font-medium",
    card: "shadow-sm border-primary/20 bg-gradient-to-br from-primary/5 to-transparent"
  },
  
  // Level 2 - Secondary Information (Supporting metrics)
  level2: {
    title: "text-sm font-semibold",
    value: "text-lg font-semibold",
    label: "text-xs text-muted-foreground",
    card: "shadow-sm"
  },
  
  // Level 3 - Tertiary Information (Details, small stats)
  level3: {
    title: "text-xs font-medium",
    value: "text-sm font-semibold",
    label: "text-xs text-muted-foreground",
    card: "border shadow-xs"
  },
  
  // Level 4 - Supporting Elements (Labels, descriptions)
  level4: {
    title: "text-xs font-medium text-muted-foreground",
    value: "text-xs",
    label: "text-xs text-muted-foreground",
    card: "border"
  }
};

// Spacing System
export const spacing = {
  section: "mb-6",      // Between major sections
  component: "mb-4",    // Between components
  element: "mb-3",      // Between elements
  tight: "mb-2",        // Tight spacing
  micro: "mb-1"         // Micro spacing
};