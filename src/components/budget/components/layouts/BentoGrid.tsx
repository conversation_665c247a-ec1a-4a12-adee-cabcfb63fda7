import React from 'react';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';

interface BentoGridProps {
  children: React.ReactNode;
  className?: string;
  animate?: boolean;
}

interface BentoItemProps {
  children: React.ReactNode;
  className?: string;
  colSpan?: 1 | 2 | 3 | 4 | 6 | 8 | 12;
  rowSpan?: 1 | 2 | 3 | 4;
  order?: number;
  animate?: boolean;
  delay?: number;
}

/**
 * BentoGrid - Modern grid layout following 2025 dashboard patterns
 * Implements responsive bento box design for financial dashboards
 */
export const BentoGrid: React.FC<BentoGridProps> = ({
  children,
  className,
  animate = true,
}) => {
  return (
    <div 
      className={cn(
        'bento-grid',
        'grid grid-cols-12 gap-6',
        'max-w-[1920px] mx-auto',
        className
      )}
    >
      {children}
    </div>
  );
};

/**
 * BentoItem - Individual item within the bento grid
 * Responsive by default with configurable spans
 */
export const BentoItem: React.FC<BentoItemProps> = ({
  children,
  className,
  colSpan = 12,
  rowSpan = 1,
  order,
  animate = true,
  delay = 0,
}) => {
  const getResponsiveClasses = () => {
    // Mobile first approach
    const classes = ['col-span-12']; // Full width on mobile
    
    // Tablet and desktop spans
    if (colSpan === 12) {
      classes.push('md:col-span-12');
    } else if (colSpan === 8) {
      classes.push('md:col-span-12 lg:col-span-8');
    } else if (colSpan === 6) {
      classes.push('md:col-span-6 lg:col-span-6');
    } else if (colSpan === 4) {
      classes.push('md:col-span-6 lg:col-span-4');
    } else if (colSpan === 3) {
      classes.push('md:col-span-6 lg:col-span-3');
    } else if (colSpan === 2) {
      classes.push('md:col-span-4 lg:col-span-2');
    }
    
    // Row spans
    if (rowSpan > 1) {
      classes.push(`row-span-${rowSpan}`);
    }
    
    // Order
    if (order) {
      classes.push(`order-${order}`);
    }
    
    return classes.join(' ');
  };
  
  const content = (
    <div className={cn(getResponsiveClasses(), className)}>
      {children}
    </div>
  );
  
  if (animate) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ 
          duration: 0.35,
          delay: delay,
          ease: [0.4, 0.0, 0.2, 1.0]
        }}
        className={cn(getResponsiveClasses(), className)}
      >
        {children}
      </motion.div>
    );
  }
  
  return content;
};

// Preset layouts for common dashboard patterns
export const BentoLayouts = {
  // Overview layout: 3 metric cards + main content + sidebar
  overview: {
    metrics: { colSpan: 4 as const, component: 'metric-cards' },
    mainContent: { colSpan: 8 as const, component: 'main-chart' },
    sidebar: { colSpan: 4 as const, component: 'quick-stats' },
  },
  
  // Analytics layout: full width chart + 4 stat cards
  analytics: {
    chart: { colSpan: 12 as const, component: 'full-chart' },
    stats: { colSpan: 3 as const, component: 'stat-card' },
  },
  
  // Comparison layout: two equal panels
  comparison: {
    leftPanel: { colSpan: 6 as const, component: 'comparison-panel' },
    rightPanel: { colSpan: 6 as const, component: 'comparison-panel' },
  },
  
  // Detail layout: 2/3 main + 1/3 sidebar
  detail: {
    main: { colSpan: 8 as const, component: 'detail-content' },
    side: { colSpan: 4 as const, component: 'detail-sidebar' },
  },
};

// Helper component for metric cards in bento grid
export const BentoMetricCard: React.FC<{
  title: string;
  value: string | number;
  change?: number;
  icon?: React.ReactNode;
  variant?: 'primary' | 'success' | 'warning' | 'danger';
}> = ({ title, value, change, icon, variant = 'primary' }) => {
  const variantClasses = {
    primary: 'metric-card--primary',
    success: 'metric-card--success',
    warning: 'metric-card--warning',
    danger: 'metric-card--danger',
  };
  
  return (
    <div className={cn('p-6 metric-card', variantClasses[variant])}>
      <div className="flex items-start justify-between">
        <div className="space-y-2">
          <p className="text-sm font-medium text-muted-foreground">{title}</p>
          <p className="text-2xl font-bold">{value}</p>
          {change !== undefined && (
            <p className={cn(
              'text-sm font-medium',
              change >= 0 ? 'data-positive' : 'data-negative'
            )}>
              {change >= 0 ? '+' : ''}{change}%
            </p>
          )}
        </div>
        {icon && (
          <div className="p-2 rounded-lg bg-background/50">
            {icon}
          </div>
        )}
      </div>
    </div>
  );
};

// Helper component for feature cards in bento grid
export const BentoFeatureCard: React.FC<{
  title: string;
  description: string;
  action?: () => void;
  actionLabel?: string;
  icon?: React.ReactNode;
}> = ({ title, description, action, actionLabel = 'View', icon }) => {
  return (
    <div className="p-6 standard-card h-full flex flex-col">
      <div className="flex items-start gap-3 mb-4">
        {icon && (
          <div className="p-2 rounded-lg bg-primary/10">
            {icon}
          </div>
        )}
        <div className="flex-1">
          <h3 className="font-semibold text-lg">{title}</h3>
          <p className="text-sm text-muted-foreground mt-1">{description}</p>
        </div>
      </div>
      {action && (
        <button
          onClick={action}
          className="mt-auto text-sm font-medium text-primary hover:underline text-left"
        >
          {actionLabel} →
        </button>
      )}
    </div>
  );
};

export default BentoGrid;