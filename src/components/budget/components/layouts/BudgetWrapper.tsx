import React from 'react';
import { cn } from '@/lib/utils';
import { BudgetSidebar } from '../sidebar/BudgetSidebar';

// Types
import type { BudgetStats, HealthMetrics, HeatmapData } from '../../types/budget.types';

export interface BudgetWrapperProps {
  children: React.ReactNode;
  stats: BudgetStats | null;
  healthMetrics: HealthMetrics | null;
  heatmapData: HeatmapData | null;
  calendarEvents: any[];
  loading?: boolean;
  sidebarCollapsed?: boolean;
  onSidebarCollapsedChange?: (collapsed: boolean) => void;
  onViewDetails?: (section: string) => void;
  className?: string;
}

/**
 * BudgetWrapper Component
 * 
 * A comprehensive wrapper component that provides a consistent layout structure
 * for budget-related components with integrated sidebar navigation.
 * 
 * Features:
 * - Responsive flexbox layout with autofitting behavior
 * - Integrated BudgetSidebar with collapsible functionality
 * - Proper scroll handling for both sidebar and main content
 * - Mobile-responsive design
 * - Consistent spacing and styling
 */
export const BudgetWrapper: React.FC<BudgetWrapperProps> = ({
  children,
  stats,
  healthMetrics,
  heatmapData,
  calendarEvents,
  loading = false,
  sidebarCollapsed = false,
  onSidebarCollapsedChange,
  onViewDetails,
  className
}) => {
  return (
    <div className={cn("flex h-full w-full bg-background", className)}>
      {/* Sidebar - Fixed width, collapsible */}
      <BudgetSidebar
        stats={stats}
        healthMetrics={healthMetrics}
        heatmapData={heatmapData}
        calendarEvents={calendarEvents}
        loading={loading}
        collapsed={sidebarCollapsed}
        onCollapsedChange={onSidebarCollapsedChange}
        onViewDetails={onViewDetails}
        className="flex-shrink-0 border-r border-border"
      />

      {/* Main Content Area - Flexible width with scroll */}
      <main className="flex-1 flex flex-col min-w-0">
        {/* Content wrapper with proper scroll handling */}
        <div className="flex-1 overflow-y-auto">
          <div className="h-full">
            {children}
          </div>
        </div>
      </main>
    </div>
  );
};

/**
 * BudgetWrapperWithSidebar Component
 * 
 * A simplified version of BudgetWrapper that includes sidebar state management
 * for easier integration into existing components.
 */
export const BudgetWrapperWithSidebar: React.FC<Omit<BudgetWrapperProps, 'sidebarCollapsed' | 'onSidebarCollapsedChange'>> = ({
  children,
  ...sidebarProps
}) => {
  const [sidebarCollapsed, setSidebarCollapsed] = React.useState(false);

  return (
    <BudgetWrapper
      sidebarCollapsed={sidebarCollapsed}
      onSidebarCollapsedChange={setSidebarCollapsed}
      {...sidebarProps}
    >
      {children}
    </BudgetWrapper>
  );
};