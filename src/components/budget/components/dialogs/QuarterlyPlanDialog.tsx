import React, { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Slider } from '@/components/ui/slider';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import {
  Calendar,
  Lock,
  Unlock,
  TrendingUp,
  AlertCircle,
  Check,
  RotateCcw,
  Percent,
} from 'lucide-react';
import { formatCurrency, formatPercentage } from '../../utils/formatters';
import { toast } from 'sonner';
import { QuarterlyAllocation } from '@/stores/budgetStore';

export interface QuarterlyPlanDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  allocations: QuarterlyAllocation[];
  totalBudget: number;
  onUpdate: (allocations: QuarterlyAllocation[]) => Promise<void>;
}

const QUARTER_INFO = [
  { name: 'Q1', months: 'January - March', color: 'bg-blue-500' },
  { name: 'Q2', months: 'April - June', color: 'bg-green-500' },
  { name: 'Q3', months: 'July - September', color: 'bg-yellow-500' },
  { name: 'Q4', months: 'October - December', color: 'bg-purple-500' },
];

export const QuarterlyPlanDialog: React.FC<QuarterlyPlanDialogProps> = ({
  open,
  onOpenChange,
  allocations,
  totalBudget,
  onUpdate,
}) => {
  const [tempAllocations, setTempAllocations] = useState<QuarterlyAllocation[]>([]);
  const [lockedQuarters, setLockedQuarters] = useState<Set<number>>(new Set());
  const [autoBalance, setAutoBalance] = useState(true);
  const [isUpdating, setIsUpdating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (allocations.length === 0) {
      // Initialize with equal distribution
      const quarterlyAmount = totalBudget / 4;
      setTempAllocations([
        { quarter: 1, allocated: quarterlyAmount, spent: 0, year: new Date().getFullYear() },
        { quarter: 2, allocated: quarterlyAmount, spent: 0, year: new Date().getFullYear() },
        { quarter: 3, allocated: quarterlyAmount, spent: 0, year: new Date().getFullYear() },
        { quarter: 4, allocated: quarterlyAmount, spent: 0, year: new Date().getFullYear() },
      ]);
    } else {
      setTempAllocations([...allocations]);
    }
    setError(null);
  }, [allocations, totalBudget, open]);

  const calculatePercentages = useCallback(() => {
    return tempAllocations.map(a => ((a.allocated || 0) / totalBudget) * 100);
  }, [tempAllocations, totalBudget]);

  const totalPercentage = calculatePercentages().reduce((sum, p) => sum + p, 0);

  const handlePercentageChange = (quarterIndex: number, newPercentage: number) => {
    if (lockedQuarters.has(quarterIndex)) return;

    const newAllocations = [...tempAllocations];
    const newAmount = (totalBudget * newPercentage) / 100;
    
    // Check if new amount is less than already spent
    const spent = newAllocations[quarterIndex].spent || 0;
    if (newAmount < spent) {
      setError(`Q${quarterIndex + 1} allocation cannot be less than already spent (${formatCurrency(spent)})`);
      return;
    }
    
    setError(null);
    newAllocations[quarterIndex] = {
      ...newAllocations[quarterIndex],
      allocated: newAmount,
    };

    // Auto-balance if enabled
    if (autoBalance) {
      const unlockedIndices = newAllocations
        .map((_, idx) => idx)
        .filter(idx => idx !== quarterIndex && !lockedQuarters.has(idx));

      if (unlockedIndices.length > 0) {
        const remainingBudget = totalBudget - newAmount - 
          newAllocations
            .filter((_, idx) => lockedQuarters.has(idx) && idx !== quarterIndex)
            .reduce((sum, a) => sum + (a.allocated || 0), 0);
        
        const perQuarterAmount = remainingBudget / unlockedIndices.length;
        
        unlockedIndices.forEach(idx => {
          newAllocations[idx] = {
            ...newAllocations[idx],
            allocated: Math.max(0, perQuarterAmount),
          };
        });
      }
    }

    setTempAllocations(newAllocations);
  };

  const handleAmountChange = (quarterIndex: number, value: string) => {
    const amount = parseFloat(value) || 0;
    const percentage = (amount / totalBudget) * 100;
    handlePercentageChange(quarterIndex, percentage);
  };

  const toggleLock = (quarterIndex: number) => {
    const newLocked = new Set(lockedQuarters);
    if (newLocked.has(quarterIndex)) {
      newLocked.delete(quarterIndex);
    } else {
      newLocked.add(quarterIndex);
    }
    setLockedQuarters(newLocked);
  };

  const resetToEqual = () => {
    const quarterlyAmount = totalBudget / 4;
    setTempAllocations(tempAllocations.map((a, idx) => ({
      ...a,
      allocated: quarterlyAmount,
    })));
    setLockedQuarters(new Set());
    setError(null);
  };

  const applyProgressive = () => {
    // Apply a progressive distribution: 20%, 25%, 27%, 28%
    const progressivePercentages = [0.20, 0.25, 0.27, 0.28];
    setTempAllocations(tempAllocations.map((a, idx) => ({
      ...a,
      allocated: totalBudget * progressivePercentages[idx],
    })));
    setLockedQuarters(new Set());
    setError(null);
  };

  const applyFrontLoaded = () => {
    // Front-loaded distribution: 35%, 30%, 20%, 15%
    const frontLoadedPercentages = [0.35, 0.30, 0.20, 0.15];
    setTempAllocations(tempAllocations.map((a, idx) => ({
      ...a,
      allocated: totalBudget * frontLoadedPercentages[idx],
    })));
    setLockedQuarters(new Set());
    setError(null);
  };

  const handleSubmit = async () => {
    if (Math.abs(totalPercentage - 100) > 0.01) {
      setError('Total allocation must equal 100%');
      return;
    }

    setIsUpdating(true);
    try {
      await onUpdate(tempAllocations);
      toast.success('Quarterly allocations updated successfully');
      onOpenChange(false);
    } catch (err) {
      toast.error('Failed to update quarterly allocations');
      setError('Failed to update. Please try again.');
    } finally {
      setIsUpdating(false);
    }
  };

  const currentQuarter = Math.floor((new Date().getMonth() / 3)) + 1;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl max-h-[70vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Quarterly Budget Plan
          </DialogTitle>
          <DialogDescription>
            Allocate your budget across quarters for better planning and control
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Quick Actions */}
          <div className="flex items-center justify-between">
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={resetToEqual}
              >
                <RotateCcw className="h-4 w-4 mr-2" />
                Equal Split
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={applyProgressive}
              >
                <TrendingUp className="h-4 w-4 mr-2" />
                Progressive
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={applyFrontLoaded}
              >
                <Calendar className="h-4 w-4 mr-2" />
                Front-loaded
              </Button>
            </div>
            <div className="flex items-center gap-2">
              <Label htmlFor="auto-balance" className="text-sm">
                Auto-balance
              </Label>
              <Switch
                id="auto-balance"
                checked={autoBalance}
                onCheckedChange={setAutoBalance}
              />
            </div>
          </div>

          {/* Quarterly Allocations */}
          <div className="space-y-4">
            {tempAllocations.map((allocation, idx) => {
              const allocated = allocation.allocated || 0;
              const spent = allocation.spent || 0;
              const percentage = (allocated / totalBudget) * 100;
              const isLocked = lockedQuarters.has(idx);
              const isCurrentQuarter = idx + 1 === currentQuarter;
              const utilizationRate = spent > 0 && allocated > 0
                ? (spent / allocated) * 100 
                : 0;

              return (
                <Card key={idx} className={`p-4 ${isCurrentQuarter ? 'ring-2 ring-primary' : ''}`}>
                  <div className="space-y-3">
                    {/* Header */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className={`w-3 h-3 rounded-full ${QUARTER_INFO[idx].color}`} />
                        <div>
                          <h4 className="font-semibold flex items-center gap-2">
                            {QUARTER_INFO[idx].name}
                            {isCurrentQuarter && (
                              <Badge variant="secondary" className="text-xs">
                                Current
                              </Badge>
                            )}
                          </h4>
                          <p className="text-xs text-muted-foreground">
                            {QUARTER_INFO[idx].months}
                          </p>
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => toggleLock(idx)}
                      >
                        {isLocked ? (
                          <Lock className="h-4 w-4 text-muted-foreground" />
                        ) : (
                          <Unlock className="h-4 w-4 text-muted-foreground" />
                        )}
                      </Button>
                    </div>

                    {/* Slider */}
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-muted-foreground">Allocation</span>
                        <span className="font-medium">{percentage.toFixed(1)}%</span>
                      </div>
                      <Slider
                        value={[percentage]}
                        onValueChange={([value]) => handlePercentageChange(idx, value)}
                        max={100}
                        step={0.5}
                        disabled={isLocked}
                        className="py-2"
                      />
                    </div>

                    {/* Amount Input */}
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-1">
                        <Label className="text-xs">Amount</Label>
                        <div className="relative">
                          <span className="absolute left-3 top-2 text-muted-foreground text-sm">$</span>
                          <Input
                            type="number"
                            value={allocation.allocated?.toFixed(0) || '0'}
                            onChange={(e) => handleAmountChange(idx, e.target.value)}
                            disabled={isLocked}
                            className="pl-8 h-8"
                          />
                        </div>
                      </div>
                      <div className="space-y-1">
                        <Label className="text-xs">Already Spent</Label>
                        <div className="relative">
                          <span className="absolute left-3 top-2 text-muted-foreground text-sm">$</span>
                          <Input
                            type="number"
                            value={allocation.spent?.toFixed(0) || '0'}
                            disabled
                            className="pl-8 h-8 bg-muted"
                          />
                        </div>
                      </div>
                    </div>

                    {/* Utilization Bar */}
                    {spent > 0 && (
                      <div className="space-y-1">
                        <div className="flex justify-between text-xs">
                          <span className="text-muted-foreground">Utilization</span>
                          <span className={utilizationRate > 90 ? 'text-red-600' : utilizationRate > 75 ? 'text-orange-600' : 'text-green-600'}>
                            {utilizationRate.toFixed(1)}%
                          </span>
                        </div>
                        <Progress value={utilizationRate} className="h-1" />
                      </div>
                    )}
                  </div>
                </Card>
              );
            })}
          </div>

          {/* Total Summary */}
          <Card className={`p-4 ${Math.abs(totalPercentage - 100) > 0.01 ? 'border-red-500' : 'border-green-500'}`}>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Percent className="h-4 w-4 text-muted-foreground" />
                <span className="font-semibold">Total Allocation</span>
              </div>
              <div className="flex items-center gap-4">
                <span className={`font-semibold ${Math.abs(totalPercentage - 100) > 0.01 ? 'text-red-600' : 'text-green-600'}`}>
                  {totalPercentage.toFixed(1)}%
                </span>
                <span className="text-muted-foreground">|</span>
                <span className="font-semibold">
                  {formatCurrency(tempAllocations.reduce((sum, a) => sum + (a.allocated || 0), 0))}
                </span>
              </div>
            </div>
          </Card>

          {/* Error Alert */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isUpdating}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={isUpdating || Math.abs(totalPercentage - 100) > 0.01}
          >
            {isUpdating ? (
              <>
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  className="mr-2"
                >
                  <Calendar className="h-4 w-4" />
                </motion.div>
                Updating...
              </>
            ) : (
              <>
                <Check className="h-4 w-4 mr-2" />
                Save Plan
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};