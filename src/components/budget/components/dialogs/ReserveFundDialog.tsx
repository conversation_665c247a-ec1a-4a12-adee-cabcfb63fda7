import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { ReserveFund } from '../planning/ReserveFund';

interface ReserveFundDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  year: number;
  onUpdateReserveFund: (year: number, amount: number) => Promise<void>;
  onSetReserveTarget: (year: number, target: number) => Promise<void>;
}

export const ReserveFundDialog: React.FC<ReserveFundDialogProps> = ({
  open,
  onOpenChange,
  year,
  onUpdateReserveFund,
  onSetReserveTarget,
}) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Reserve Fund Management</DialogTitle>
        </DialogHeader>
        <div className="mt-4">
          <ReserveFund
            reserveFunds={[
              {
                id: 'default',
                name: 'General Reserve',
                targetAmount: 15000,
                currentAmount: 0,
                percentage: 5,
                fundType: 'emergency',
                autoContribute: true,
                contributionPercentage: 5,
                status: 'active',
                transactions: []
              }
            ]}
            onCreateFund={async (fund) => {
              console.log('Creating reserve fund:', fund);
            }}
            onUpdateFund={async (id, fund) => {
              if (fund.currentAmount !== undefined) {
                await onUpdateReserveFund(year, fund.currentAmount);
              }
              if (fund.targetAmount !== undefined) {
                await onSetReserveTarget(year, fund.targetAmount);
              }
            }}
            onDeleteFund={(id) => {
              console.log('Deleting reserve fund:', id);
            }}
            onTransaction={async (fundId, transaction) => {
              console.log('Processing transaction:', fundId, transaction);
            }}
            onApproveTransaction={(fundId, transactionId, approved) => {
              console.log('Approving transaction:', fundId, transactionId, approved);
            }}
            loading={false}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};