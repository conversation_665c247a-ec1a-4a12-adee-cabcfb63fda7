import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  Di<PERSON>Header,
  DialogTitle,
} from '@/components/ui/dialog';
import { CarryoverRules } from '../planning/CarryoverRules';

interface CarryoverRulesDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  year: number;
  remainingAmount: number;
  stats: any;
  onApplyCarryoverRules: (fromYear: number, toYear: number, rules: any) => Promise<void>;
}

export const CarryoverRulesDialog: React.FC<CarryoverRulesDialogProps> = ({
  open,
  onOpenChange,
  year,
  remainingAmount,
  stats,
  onApplyCarryoverRules,
}) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Carryover Rules Configuration</DialogTitle>
        </DialogHeader>
        <div className="mt-4">
          <CarryoverRules
            rules={[]}
            carryoverHistory={[]}
            onCreateRule={async (rule) => {
              console.log('Creating carryover rule:', rule);
            }}
            onUpdateRule={async (id, rule) => {
              console.log('Updating carryover rule:', id, rule);
            }}
            onDeleteRule={async (id) => {
              console.log('Deleting carryover rule:', id);
            }}
            onExecuteRule={async (ruleId) => {
              const nextYear = year + 1;
              await onApplyCarryoverRules(year, nextYear, { ruleId });
            }}
            onSimulateCarryover={() => ({
              totalUnspent: remainingAmount,
              projectedCarryover: remainingAmount * 0.8,
              byCategory: stats?.by_category?.reduce((acc: any, cat: any) => ({
                ...acc,
                [cat.category]: (cat.total - cat.spent) || 0
              }), {}) || {}
            })}
            loading={false}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};