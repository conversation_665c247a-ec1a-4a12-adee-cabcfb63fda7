import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { ScenarioPlanner } from '../planning/ScenarioPlanner';

interface ScenarioPlannerDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  currentBudget: {
    total: number;
    spent: number;
    departments: any[];
    categories: any[];
  };
  year: number;
  onSaveScenario: (scenario: any) => Promise<void>;
  onApplyScenario: (scenario: any) => Promise<void>;
}

export const ScenarioPlannerDialog: React.FC<ScenarioPlannerDialogProps> = ({
  open,
  onOpenChange,
  currentBudget,
  year,
  onSaveScenario,
  onApplyScenario,
}) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl max-h-[85vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Budget Scenario Planning</DialogTitle>
        </DialogHeader>
        <div className="mt-4">
          <ScenarioPlanner
            currentBudget={currentBudget}
            onSaveScenario={onSaveScenario}
            onApplyScenario={onApplyScenario}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};