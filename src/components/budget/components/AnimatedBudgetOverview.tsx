import React, { useState, useEffect } from 'react';
import { useSpring, animated, useTransition, useTrail, config } from '@react-spring/web';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { 
  TrendingUp, 
  AlertCircle, 
  Wallet, 
  Target, 
  Calendar,
  PieChart,
  Activity
} from 'lucide-react';
import { AnimatedBudgetCard } from './AnimatedBudgetCard';

interface BudgetOverviewProps {
  budgetData: any;
  loading?: boolean;
  onYearChange?: (year: number) => void;
  currentYear?: number;
}

export const AnimatedBudgetOverview: React.FC<BudgetOverviewProps> = ({
  budgetData,
  loading = false,
  onYearChange,
  currentYear = new Date().getFullYear()
}) => {
  const [mounted, setMounted] = useState(false);
  
  // Page transition animation
  const pageTransition = useSpring({
    from: { opacity: 0, transform: 'translateY(30px)' },
    to: { opacity: 1, transform: 'translateY(0px)' },
    config: config.gentle,
    delay: 100
  });

  // Header animation
  const headerSpring = useSpring({
    from: { opacity: 0, transform: 'translateY(-20px)' },
    to: { opacity: 1, transform: 'translateY(0px)' },
    config: config.slow,
    delay: 200
  });

  // Stats cards trail animation
  const stats = budgetData ? [
    { title: 'Total Budget', value: budgetData.totalBudget || 0, icon: Wallet, color: 'text-blue-600' },
    { title: 'Spent', value: budgetData.spent || 0, icon: Target, color: 'text-orange-600' },
    { title: 'Remaining', value: budgetData.remaining || 0, icon: PieChart, color: 'text-green-600' },
    { title: 'Utilization', value: `${budgetData.utilization || 0}%`, icon: Activity, color: 'text-purple-600' }
  ] : [];

  const trail = useTrail(stats.length, {
    from: { opacity: 0, transform: 'translateY(20px)' },
    to: { opacity: 1, transform: 'translateY(0px)' },
    config: config.wobbly,
    delay: 300
  });

  // Progress bar animation
  const progressSpring = useSpring({
    from: { width: '0%' },
    to: { width: `${budgetData?.utilization || 0}%` },
    config: config.slow,
    delay: 600
  });

  // Year selector animation
  const yearTransition = useTransition([currentYear], {
    from: { opacity: 0, transform: 'scale(0.8)' },
    enter: { opacity: 1, transform: 'scale(1)' },
    leave: { opacity: 0, transform: 'scale(0.8)' },
    config: config.gentle
  });

  useEffect(() => {
    setMounted(true);
  }, []);

  if (loading) {
    return (
      <div className="space-y-6">
        <Skeleton className="h-8 w-64" />
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Skeleton key={i} className="h-32 w-full" />
          ))}
        </div>
      </div>
    );
  }

  return (
    <animated.div style={pageTransition} className="space-y-6">
      {/* Header */}
      <animated.div style={headerSpring} className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Training Budget</h1>
          <p className="text-muted-foreground">
            Manage and track your training budget for {currentYear}
          </p>
        </div>
        {yearTransition((style, year) => (
          <animated.div style={style}>
            <Badge variant="outline" className="text-lg">
              {year}
            </Badge>
          </animated.div>
        ))}
      </animated.div>

      {/* Stats Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {trail.map((props, index) => (
          <animated.div key={index} style={props}>
            <AnimatedBudgetCard
              title={stats[index].title}
              value={stats[index].value}
              icon={stats[index].icon}
              color={stats[index].color}
              delay={index * 100}
            />
          </animated.div>
        ))}
      </div>

      {/* Budget Progress */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Budget Utilization</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex justify-between text-sm">
              <span>Spent</span>
              <span className="font-medium">{budgetData?.utilization || 0}%</span>
            </div>
            <div className="relative">
              <Progress value={budgetData?.utilization || 0} className="h-2" />
              <animated.div 
                className="absolute top-0 left-0 h-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full"
                style={{ width: progressSpring.width }}
              />
            </div>
            <div className="text-sm text-muted-foreground">
              ${budgetData?.spent?.toLocaleString() || 0} of ${budgetData?.totalBudget?.toLocaleString() || 0} used
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <animated.div
        style={useSpring({
          from: { opacity: 0, transform: 'translateY(20px)' },
          to: { opacity: 1, transform: 'translateY(0px)' },
          delay: 800,
          config: config.gentle
        })}
      >
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              <Button variant="outline" size="sm">
                Add Expense
              </Button>
              <Button variant="outline" size="sm">
                Adjust Budget
              </Button>
              <Button variant="outline" size="sm">
                View Reports
              </Button>
              <Button variant="outline" size="sm">
                Export Data
              </Button>
            </div>
          </CardContent>
        </Card>
      </animated.div>
    </animated.div>
  );
};

export default AnimatedBudgetOverview;