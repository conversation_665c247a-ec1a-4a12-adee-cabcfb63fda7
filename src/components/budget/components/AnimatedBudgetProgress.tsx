import React, { useState, useEffect } from 'react';
import { useSpring, animated, useTransition } from '@react-spring/web';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign,
  Target,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';

interface BudgetProgress {
  category: string;
  allocated: number;
  spent: number;
  remaining: number;
  percentage: number;
  status: 'healthy' | 'warning' | 'danger';
  icon?: React.ReactNode;
}

interface AnimatedBudgetProgressProps {
  budgetProgress: BudgetProgress[];
  totalBudget: number;
  totalSpent: number;
  showDetails?: boolean;
}

export const AnimatedBudgetProgress: React.FC<AnimatedBudgetProgressProps> = ({
  budgetProgress = [],
  totalBudget = 0,
  totalSpent = 0,
  showDetails = true
}) => {
  const [mounted, setMounted] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Overall progress animation
  const overallProgress = useSpring({
    from: { percentage: 0, spent: 0 },
    to: { 
      percentage: totalBudget > 0 ? (totalSpent / totalBudget) * 100 : 0,
      spent: totalSpent
    },
    config: { tension: 200, friction: 20 },
    delay: 100
  });

  // Category animations
  const categoryTransitions = useTransition(
    budgetProgress.sort((a, b) => b.percentage - a.percentage),
    {
      keys: item => item.category,
      from: { opacity: 0, transform: 'translateX(-20px)', height: 0 },
      enter: { opacity: 1, transform: 'translateX(0px)', height: 'auto' },
      leave: { opacity: 0, transform: 'translateX(-20px)', height: 0 },
      update: { opacity: 1, transform: 'translateX(0px)', height: 'auto' },
      config: { tension: 200, friction: 20 },
      trail: 100
    }
  );

  // Header animation
  const headerSpring = useSpring({
    from: { opacity: 0, transform: 'translateY(-10px)' },
    to: { opacity: 1, transform: 'translateY(0px)' },
    config: { tension: 200, friction: 20 },
    delay: 50
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-600 bg-green-100';
      case 'warning': return 'text-yellow-600 bg-yellow-100';
      case 'danger': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy': return CheckCircle;
      case 'warning': return AlertTriangle;
      case 'danger': return AlertTriangle;
      default: return DollarSign;
    }
  };

  const getProgressColor = (percentage: number) => {
    if (percentage >= 90) return 'bg-red-500';
    if (percentage >= 75) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  const remainingBudget = totalBudget - totalSpent;
  const overallPercentage = totalBudget > 0 ? (totalSpent / totalBudget) * 100 : 0;

  return (
    <Card>
      <CardHeader>
        <animated.div style={headerSpring}>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Budget Progress Overview
          </CardTitle>
          <p className="text-sm text-muted-foreground mt-1">
            Track spending across all categories
          </p>
        </animated.div>
      </CardHeader>

      <CardContent>
        {/* Overall Progress */}
        <div className="mb-6 p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium">Total Budget</span>
            <animated.span className="text-lg font-bold">
              ${overallProgress.spent.to(n => n.toLocaleString())} / ${totalBudget.toLocaleString()}
            </animated.span>
          </div>
          
          <div className="relative">
            <Progress 
              value={overallProgress.percentage.to(n => Math.min(n, 100))}
              className="h-3"
            />
            <animated.div 
              className={`absolute top-0 left-0 h-3 rounded-full transition-colors ${
                getProgressColor(overallPercentage)
              }`}
              style={{
                width: overallProgress.percentage.to(n => `${Math.min(n, 100)}%`)
              }}
            />
          </div>
          
          <div className="flex items-center justify-between mt-2 text-sm">
            <span className="text-muted-foreground">
              {overallPercentage.toFixed(1)}% used
            </span>
            <span className={`font-medium ${
              remainingBudget >= 0 ? 'text-green-600' : 'text-red-600'
            }`}>
              ${Math.abs(remainingBudget).toLocaleString()} {remainingBudget >= 0 ? 'remaining' : 'over'}
            </span>
          </div>
        </div>

        {/* Category Progress */}
        <div className="space-y-4">
          {categoryTransitions((style, category) => (
            <animated.div style={style}>
              <div 
                className={`p-4 border rounded-lg cursor-pointer transition-all ${
                  selectedCategory === category.category 
                    ? 'border-blue-500 bg-blue-50' 
                    : 'hover:border-gray-300'
                }`}
                onClick={() => setSelectedCategory(
                  selectedCategory === category.category ? null : category.category
                )}
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <span className="font-medium">{category.category}</span>
                    <Badge className={getStatusColor(category.status)}>
                      {React.createElement(getStatusIcon(category.status), {
                        className: 'h-3 w-3 inline mr-1'
                      })}
                      {category.status}
                    </Badge>
                  </div>
                  <span className="text-sm font-medium">
                    ${category.spent.toLocaleString()} / ${category.allocated.toLocaleString()}
                  </span>
                </div>
                
                <div className="relative">
                  <Progress 
                    value={Math.min(category.percentage, 100)}
                    className="h-2"
                  />
                  <animated.div 
                    className={`absolute top-0 left-0 h-2 rounded-full transition-colors ${
                      getProgressColor(category.percentage)
                    }`}
                    style={{
                      width: `${Math.min(category.percentage, 100)}%`
                    }}
                  />
                </div>
                
                <div className="flex items-center justify-between mt-2 text-sm">
                  <span className="text-muted-foreground">
                    {category.percentage.toFixed(1)}% used
                  </span>
                  <span className={`font-medium ${
                    category.remaining >= 0 ? 'text-green-600' : 'text-red-600'
                  }`}>
                    ${Math.abs(category.remaining).toLocaleString()} {category.remaining >= 0 ? 'left' : 'over'}
                  </span>
                </div>

                {selectedCategory === category.category && (
                  <animated.div
                    className="mt-3 pt-3 border-t"
                    style={useSpring({
                      from: { opacity: 0, height: 0 },
                      to: { opacity: 1, height: 'auto' },
                      config: { tension: 200, friction: 20 }
                    })}
                  >
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">Allocated:</span>
                        <span className="ml-1 font-medium">
                          ${category.allocated.toLocaleString()}
                        </span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Spent:</span>
                        <span className="ml-1 font-medium">
                          ${category.spent.toLocaleString()}
                        </span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Remaining:</span>
                        <span className={`ml-1 font-medium ${
                          category.remaining >= 0 ? 'text-green-600' : 'text-red-600'
                        }`}>
                          ${Math.abs(category.remaining).toLocaleString()}
                        </span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Utilization:</span>
                        <span className="ml-1 font-medium">
                          {category.percentage.toFixed(1)}%
                        </span>
                      </div>
                    </div>
                  </animated.div>
                )}
              </div>
            </animated.div>
          ))}
        </div>

        {budgetProgress.length === 0 && (
          <animated.div
            style={useSpring({
              from: { opacity: 0, transform: 'translateY(10px)' },
              to: { opacity: 1, transform: 'translateY(0px)' },
              config: { tension: 200, friction: 20 }
            })}
            className="text-center py-8 text-muted-foreground"
          >
            <Target className="h-12 w-12 mx-auto mb-3 opacity-50" />
            <p>No budget categories found</p>
          </animated.div>
        )}
      </CardContent>
    </Card>
  );
};

export default AnimatedBudgetProgress;