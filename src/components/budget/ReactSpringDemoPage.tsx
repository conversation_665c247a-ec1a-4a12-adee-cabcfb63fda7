import React, { useState } from 'react';
import { useSpring, animated, useTransition, useTrail } from '@react-spring/web';
import { 
  AnimatedBudgetSummary, 
  AnimatedBudgetProgress, 
  AnimatedExpenseList, 
  AnimatedBudgetCard 
} from './components';

// Demo data for React Spring integration
const demoSummary = {
  totalBudget: 150000,
  totalSpent: 87500,
  totalRemaining: 62500,
  utilization: 58.3,
  departmentCount: 8,
  activeCategories: 12,
  monthlyAverage: 7291.67,
  lastUpdated: new Date().toISOString()
};

const demoProgress = [
  {
    category: 'Training Materials',
    allocated: 25000,
    spent: 18000,
    remaining: 7000,
    percentage: 72,
    status: 'warning' as const
  },
  {
    category: 'Instructor Fees',
    allocated: 45000,
    spent: 35000,
    remaining: 10000,
    percentage: 77.8,
    status: 'warning' as const
  },
  {
    category: 'Venue Costs',
    allocated: 30000,
    spent: 15000,
    remaining: 15000,
    percentage: 50,
    status: 'healthy' as const
  }
];

const demoExpenses = [
  {
    id: '1',
    title: 'Q1 Training Materials',
    amount: 5000,
    category: 'Training Materials',
    date: '2024-01-15',
    status: 'approved' as const,
    description: 'Materials for Q1 training sessions'
  },
  {
    id: '2',
    title: 'Instructor Workshop',
    amount: 8000,
    category: 'Instructor Fees',
    date: '2024-01-20',
    status: 'paid' as const,
    description: 'Advanced training workshop for instructors'
  },
  {
    id: '3',
    title: 'Venue Rental',
    amount: 3000,
    category: 'Venue Costs',
    date: '2024-01-25',
    status: 'pending' as const,
    description: 'Conference room rental for training'
  }
];

const ReactSpringDemoPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'summary' | 'progress' | 'expenses' | 'cards'>('summary');
  const [loading, setLoading] = useState(false);
  const [demoExpensesData, setDemoExpensesData] = useState(demoExpenses);

  // Page entrance animation
  const pageSpring = useSpring({
    from: { opacity: 0, transform: 'translateY(30px)' },
    to: { opacity: 1, transform: 'translateY(0px)' },
    config: { tension: 180, friction: 25 }
  });

  // Tab button animations
  const tabTransition = useTransition([activeTab], {
    from: { opacity: 0, transform: 'scale(0.95)' },
    enter: { opacity: 1, transform: 'scale(1)' },
    config: { tension: 200, friction: 20 }
  });

  // Demo actions
  const handleRefresh = () => {
    setLoading(true);
    setTimeout(() => setLoading(false), 1500);
  };

  const addRandomExpense = () => {
    const newExpense = {
      id: Date.now().toString(),
      title: `New Expense ${Math.floor(Math.random() * 100)}`,
      amount: Math.floor(Math.random() * 5000) + 1000,
      category: demoProgress[Math.floor(Math.random() * demoProgress.length)].category,
      date: new Date().toISOString().split('T')[0],
      status: 'pending' as const,
      description: 'Auto-generated demo expense'
    };
    setDemoExpensesData(prev => [...prev, newExpense]);
  };

  const tabs = [
    { id: 'summary', label: 'Budget Summary', icon: '📊' },
    { id: 'progress', label: 'Progress Tracking', icon: '📈' },
    { id: 'expenses', label: 'Expense List', icon: '💰' },
    { id: 'cards', label: 'Budget Cards', icon: '💳' }
  ];

  return (
    <animated.div style={pageSpring} className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-8">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-800 mb-2">
            React Spring Budget Demo
          </h1>
          <p className="text-gray-600">
            Interactive animations for training budget components
          </p>
        </div>

        {/* Navigation Tabs */}
        <div className="bg-white rounded-lg shadow-lg p-4 mb-6">
          <div className="flex flex-wrap gap-2 justify-center">
            {tabs.map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                  activeTab === tab.id
                    ? 'bg-blue-500 text-white shadow-md'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </div>
        </div>

        {/* Demo Controls */}
        <div className="bg-white rounded-lg shadow-lg p-4 mb-6">
          <div className="flex flex-wrap gap-4 justify-center">
            <button
              onClick={handleRefresh}
              disabled={loading}
              className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 transition-colors"
            >
              {loading ? '⏳ Refreshing...' : '🔄 Refresh Data'}
            </button>
            <button
              onClick={addRandomExpense}
              className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
            >
              ➕ Add Random Expense
            </button>
            <button
              onClick={() => setDemoExpensesData(demoExpenses)}
              className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
            >
              🗑️ Reset Expenses
            </button>
          </div>
        </div>

        {/* Content Area */}
        <div className="space-y-6">
          {tabTransition((style, item) => (
            <animated.div style={style}>
              {item === 'summary' && (
                <div>
                  <h2 className="text-2xl font-bold mb-4">Budget Overview</h2>
                  <AnimatedBudgetSummary
                    summary={demoSummary}
                    loading={loading}
                    onRefresh={handleRefresh}
                  />
                </div>
              )}

              {item === 'progress' && (
                <div>
                  <h2 className="text-2xl font-bold mb-4">Budget Progress</h2>
                  <AnimatedBudgetProgress
                    budgetProgress={demoProgress}
                    totalBudget={demoSummary.totalBudget}
                    totalSpent={demoSummary.totalSpent}
                    showDetails={true}
                  />
                </div>
              )}

              {item === 'expenses' && (
                <div>
                  <h2 className="text-2xl font-bold mb-4">Recent Expenses</h2>
                  <AnimatedExpenseList
                    expenses={demoExpensesData}
                    loading={loading}
                    onExpenseClick={(expense) => {}
                    onStatusChange={(id, status) => {
                      setDemoExpensesData(prev => 
                        prev.map(e => e.id === id ? { ...e, status } : e)
                      );
                    }}
                  />
                </div>
              )}

              {item === 'cards' && (
                <div>
                  <h2 className="text-2xl font-bold mb-4">Budget Cards</h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {demoProgress.map((item, index) => (
                      <AnimatedBudgetCard
                      key={item.category}
                      title={item.category}
                      amount={item.spent}
                      total={item.allocated}
                      percentage={item.percentage}
                      icon={<div className="w-6 h-6 bg-blue-500 rounded-full" />}
                      color={
                        item.percentage >= 90 ? 'text-red-600' :
                        item.percentage >= 75 ? 'text-yellow-600' : 'text-green-600'
                      }
                      trend={Math.random() > 0.5 ? 'up' : Math.random() > 0.3 ? 'down' : 'neutral'}
                    />
                    ))}
                  </div>
                </div>
              )}
            </animated.div>
          ))}
        </div>

        {/* Performance Info */}
        <div className="bg-white rounded-lg shadow-lg p-6 mt-8">
          <h3 className="text-lg font-semibold mb-4">Animation Features</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
            <div className="bg-blue-50 p-3 rounded">
              <strong>Spring Physics:</strong> Natural, physics-based animations
            </div>
            <div className="bg-green-50 p-3 rounded">
              <strong>Smooth Transitions:</strong> 60fps animations with easing
            </div>
            <div className="bg-purple-50 p-3 rounded">
              <strong>Interactive:</strong> Hover effects and micro-interactions
            </div>
            <div className="bg-yellow-50 p-3 rounded">
              <strong>Performance:</strong> Optimized for smooth rendering
            </div>
            <div className="bg-red-50 p-3 rounded">
              <strong>Responsive:</strong> Adapts to screen size changes
            </div>
            <div className="bg-indigo-50 p-3 rounded">
              <strong>Real-time:</strong> Live data update animations
            </div>
          </div>
        </div>
      </div>
    </animated.div>
  );
};

export default ReactSpringDemoPage;