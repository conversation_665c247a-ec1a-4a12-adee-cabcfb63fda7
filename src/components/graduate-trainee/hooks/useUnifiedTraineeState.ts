import { useState, useCallback, useMemo, useEffect } from 'react';
import { 
  GraduateTraineeProgram, 
  QuarterlyReview, 
  SkillDevelopment,
  Certification 
} from '@/lib/api/graduateTrainee';
import { useTraineeData } from './useTraineeData';
import { useTraineeMetrics } from './useTraineeMetrics';
import { useTraineeExport } from './useTraineeExport';
import { DEFAULT_FILTERS, VIEW_MODES } from '../constants/traineeConstants';

interface DialogState {
  traineeForm: boolean;
  traineeDetail: boolean;
  quarterlyReview: boolean;
  skillsDevelopment: boolean;
  certification: boolean;
  deleteConfirm: boolean;
  filter: boolean;
}

interface UnifiedTraineeState {
  // Data
  trainees: GraduateTraineeProgram[];
  selectedTrainee: GraduateTraineeProgram | null;
  loading: boolean;
  error: string | null;
  
  // UI State
  activeTab: string;
  viewMode: string;
  dialogState: DialogState;
  filters: typeof DEFAULT_FILTERS;
  
  // Metrics
  metrics: any;
  
  // Actions
  setActiveTab: (tab: string) => void;
  setViewMode: (mode: string) => void;
  selectTrainee: (trainee: GraduateTraineeProgram | null) => void;
  
  // Dialog Management
  openDialog: (dialog: keyof DialogState) => void;
  closeDialog: (dialog: keyof DialogState) => void;
  closeAllDialogs: () => void;
  
  // Filter Management
  setFilter: (key: string, value: any) => void;
  clearFilters: () => void;
  
  // CRUD Operations
  createTrainee: (data: Partial<GraduateTraineeProgram>) => Promise<void>;
  updateTrainee: (id: string, data: Partial<GraduateTraineeProgram>) => Promise<void>;
  deleteTrainee: (id: string) => Promise<void>;
  
  // Business Operations
  scheduleReview: (traineeId: string, review: QuarterlyReview) => Promise<void>;
  updateSkill: (traineeId: string, skill: SkillDevelopment) => Promise<void>;
  addCertification: (traineeId: string, cert: Certification) => Promise<void>;
  
  // Export Operations
  exportData: (type: string) => void;
  
  // Refresh
  refreshData: () => Promise<void>;
}

export const useUnifiedTraineeState = (): UnifiedTraineeState => {
  // Core Data
  const { trainees: rawTrainees, loading, error, refetch } = useTraineeData();
  const { exportToCSV } = useTraineeExport();
  
  // State Management
  const [selectedTrainee, setSelectedTrainee] = useState<GraduateTraineeProgram | null>(null);
  const [activeTab, setActiveTab] = useState('dashboard');
  const [viewMode, setViewMode] = useState(VIEW_MODES.GRID);
  const [filters, setFilters] = useState(DEFAULT_FILTERS);
  const [dialogState, setDialogState] = useState<DialogState>({
    traineeForm: false,
    traineeDetail: false,
    quarterlyReview: false,
    skillsDevelopment: false,
    certification: false,
    deleteConfirm: false,
    filter: false,
  });

  // Filtered Trainees
  const trainees = useMemo(() => {
    let filtered = [...(rawTrainees || [])];
    
    if (filters.status) {
      filtered = filtered.filter(t => t.status === filters.status);
    }
    if (filters.department) {
      filtered = filtered.filter(t => t.department === filters.department);
    }
    if (filters.manager) {
      filtered = filtered.filter(t => t.manager === filters.manager);
    }
    if (filters.searchQuery) {
      const query = filters.searchQuery.toLowerCase();
      filtered = filtered.filter(t => 
        t.employeeName.toLowerCase().includes(query) ||
        t.employeeId.toLowerCase().includes(query) ||
        t.department.toLowerCase().includes(query)
      );
    }
    
    return filtered;
  }, [rawTrainees, filters]);

  // Metrics
  const metrics = useTraineeMetrics(trainees);

  // Dialog Management
  const openDialog = useCallback((dialog: keyof DialogState) => {
    setDialogState(prev => ({ ...prev, [dialog]: true }));
  }, []);

  const closeDialog = useCallback((dialog: keyof DialogState) => {
    setDialogState(prev => ({ ...prev, [dialog]: false }));
  }, []);

  const closeAllDialogs = useCallback(() => {
    setDialogState({
      traineeForm: false,
      traineeDetail: false,
      quarterlyReview: false,
      skillsDevelopment: false,
      certification: false,
      deleteConfirm: false,
      filter: false,
    });
  }, []);

  // Trainee Selection
  const selectTrainee = useCallback((trainee: GraduateTraineeProgram | null) => {
    setSelectedTrainee(trainee);
    if (trainee) {
      openDialog('traineeDetail');
    }
  }, [openDialog]);

  // Filter Management
  const setFilter = useCallback((key: string, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  }, []);

  const clearFilters = useCallback(() => {
    setFilters(DEFAULT_FILTERS);
  }, []);

  // CRUD Operations
  const createTrainee = useCallback(async (data: Partial<GraduateTraineeProgram>) => {
    try {
      // API call implementation
      console.log('Creating trainee:', data);
      await refetch();
      closeDialog('traineeForm');
    } catch (error) {
      console.error('Failed to create trainee:', error);
      throw error;
    }
  }, [refetch, closeDialog]);

  const updateTrainee = useCallback(async (id: string, data: Partial<GraduateTraineeProgram>) => {
    try {
      // API call implementation
      console.log('Updating trainee:', id, data);
      await refetch();
      if (selectedTrainee?.id === id) {
        setSelectedTrainee(prev => prev ? { ...prev, ...data } : null);
      }
    } catch (error) {
      console.error('Failed to update trainee:', error);
      throw error;
    }
  }, [refetch, selectedTrainee]);

  const deleteTrainee = useCallback(async (id: string) => {
    try {
      // API call implementation
      console.log('Deleting trainee:', id);
      await refetch();
      if (selectedTrainee?.id === id) {
        setSelectedTrainee(null);
        closeDialog('traineeDetail');
      }
      closeDialog('deleteConfirm');
    } catch (error) {
      console.error('Failed to delete trainee:', error);
      throw error;
    }
  }, [refetch, selectedTrainee, closeDialog]);

  // Business Operations
  const scheduleReview = useCallback(async (traineeId: string, review: QuarterlyReview) => {
    try {
      console.log('Scheduling review:', traineeId, review);
      await updateTrainee(traineeId, {
        quarterlyReviews: [...(selectedTrainee?.quarterlyReviews || []), review]
      });
      closeDialog('quarterlyReview');
    } catch (error) {
      console.error('Failed to schedule review:', error);
      throw error;
    }
  }, [updateTrainee, selectedTrainee, closeDialog]);

  const updateSkill = useCallback(async (traineeId: string, skill: SkillDevelopment) => {
    try {
      console.log('Updating skill:', traineeId, skill);
      const trainee = trainees.find(t => t.id === traineeId);
      if (trainee) {
        const skills = trainee.skillsDevelopment || [];
        const existingIndex = skills.findIndex(s => s.skillName === skill.skillName);
        
        let updatedSkills;
        if (existingIndex >= 0) {
          updatedSkills = [...skills];
          updatedSkills[existingIndex] = skill;
        } else {
          updatedSkills = [...skills, skill];
        }
        
        await updateTrainee(traineeId, { skillsDevelopment: updatedSkills });
      }
      closeDialog('skillsDevelopment');
    } catch (error) {
      console.error('Failed to update skill:', error);
      throw error;
    }
  }, [trainees, updateTrainee, closeDialog]);

  const addCertification = useCallback(async (traineeId: string, cert: Certification) => {
    try {
      console.log('Adding certification:', traineeId, cert);
      const trainee = trainees.find(t => t.id === traineeId);
      if (trainee) {
        await updateTrainee(traineeId, {
          certifications: [...(trainee.certifications || []), cert]
        });
      }
      closeDialog('certification');
    } catch (error) {
      console.error('Failed to add certification:', error);
      throw error;
    }
  }, [trainees, updateTrainee, closeDialog]);

  // Export Operations
  const exportData = useCallback((type: string) => {
    exportToCSV(trainees, type);
  }, [trainees, exportToCSV]);

  // Refresh Data
  const refreshData = useCallback(async () => {
    await refetch();
  }, [refetch]);

  return {
    // Data
    trainees,
    selectedTrainee,
    loading,
    error,
    
    // UI State
    activeTab,
    viewMode,
    dialogState,
    filters,
    
    // Metrics
    metrics,
    
    // Actions
    setActiveTab,
    setViewMode: (mode: string) => setViewMode(mode),
    selectTrainee,
    
    // Dialog Management
    openDialog,
    closeDialog,
    closeAllDialogs,
    
    // Filter Management
    setFilter,
    clearFilters,
    
    // CRUD Operations
    createTrainee,
    updateTrainee,
    deleteTrainee,
    
    // Business Operations
    scheduleReview,
    updateSkill,
    addCertification,
    
    // Export Operations
    exportData,
    
    // Refresh
    refreshData,
  };
};