import { useCallback } from 'react';
import { 
  GraduateTraineeProgram, 
  QuarterlyReview, 
  SkillDevelopment,
  Certification 
} from '@/lib/api/graduateTrainee';
import { useTraineeStore } from '../store/traineeStore';
import { useToast } from '@/components/ui/use-toast';

export const useUnifiedTraineeState = () => {
  const store = useTraineeStore();
  const { toast } = useToast();

  // CRUD Operations with proper ID handling
  const createTrainee = useCallback(async (data: Partial<GraduateTraineeProgram>) => {
    try {
      await store.addTrainee(data as Omit<GraduateTraineeProgram, 'id'>);
      toast({
        title: "Success",
        description: "Trainee program created successfully",
      });
    } catch (error) {
      console.error('Failed to create trainee:', error);
      toast({
        title: "Error",
        description: "Failed to create trainee program",
        variant: "destructive"
      });
      throw error;
    }
  }, [store, toast]);

  const updateTrainee = useCallback(async (id: number | string, data: Partial<GraduateTraineeProgram>) => {
    try {
      const numericId = typeof id === 'string' ? parseInt(id) : id;
      await store.updateTrainee(numericId, data);
      toast({
        title: "Success",
        description: "Trainee program updated successfully",
      });
    } catch (error) {
      console.error('Failed to update trainee:', error);
      toast({
        title: "Error",
        description: "Failed to update trainee program",
        variant: "destructive"
      });
      throw error;
    }
  }, [store, toast]);

  const deleteTrainee = useCallback(async (id: number | string) => {
    try {
      const numericId = typeof id === 'string' ? parseInt(id) : id;
      await store.deleteTrainee(numericId);
      toast({
        title: "Success",
        description: "Trainee program deleted successfully",
      });
    } catch (error) {
      console.error('Failed to delete trainee:', error);
      toast({
        title: "Error",
        description: "Failed to delete trainee program",
        variant: "destructive"
      });
      throw error;
    }
  }, [store, toast]);

  // Business Operations
  const scheduleReview = useCallback(async (traineeId: number | string, review: QuarterlyReview) => {
    try {
      const numericId = typeof traineeId === 'string' ? parseInt(traineeId) : traineeId;
      const trainee = store.trainees.find(t => t.id === numericId);
      if (trainee) {
        await updateTrainee(numericId, {
          quarterlyReviews: [...(trainee.quarterlyReviews || []), review]
        });
      }
    } catch (error) {
      console.error('Failed to schedule review:', error);
      throw error;
    }
  }, [store.trainees, updateTrainee]);

  const updateSkill = useCallback(async (traineeId: number | string, skill: SkillDevelopment) => {
    try {
      const numericId = typeof traineeId === 'string' ? parseInt(traineeId) : traineeId;
      const trainee = store.trainees.find(t => t.id === numericId);
      if (trainee) {
        const skills = trainee.skillsDevelopment || [];
        const existingIndex = skills.findIndex(s => s.skillName === skill.skillName);
        
        let updatedSkills;
        if (existingIndex >= 0) {
          updatedSkills = [...skills];
          updatedSkills[existingIndex] = skill;
        } else {
          updatedSkills = [...skills, skill];
        }
        
        await updateTrainee(numericId, { skillsDevelopment: updatedSkills });
      }
    } catch (error) {
      console.error('Failed to update skill:', error);
      throw error;
    }
  }, [store.trainees, updateTrainee]);

  const addCertification = useCallback(async (traineeId: number | string, cert: Certification) => {
    try {
      const numericId = typeof traineeId === 'string' ? parseInt(traineeId) : traineeId;
      const trainee = store.trainees.find(t => t.id === numericId);
      if (trainee) {
        await updateTrainee(numericId, {
          certifications: [...(trainee.certifications || []), cert]
        });
      }
    } catch (error) {
      console.error('Failed to add certification:', error);
      throw error;
    }
  }, [store.trainees, updateTrainee]);

  // Handle dialog actions
  const handleDialogAction = useCallback(async (action: string, data?: any) => {
    try {
      switch (action) {
        case 'create':
          if (data) {
            await createTrainee(data);
            store.closeDialog();
          }
          break;
        case 'update':
          if (data && store.selectedTrainee) {
            await updateTrainee(store.selectedTrainee.id, data);
            store.closeDialog();
          }
          break;
        case 'delete':
          if (store.selectedTrainee) {
            await deleteTrainee(store.selectedTrainee.id);
            store.closeDialog();
          }
          break;
        default:
          break;
      }
    } catch (error) {
      console.error('Dialog action failed:', error);
      throw error;
    }
  }, [createTrainee, updateTrainee, deleteTrainee, store]);

  return {
    // CRUD Operations
    createTrainee,
    updateTrainee,
    deleteTrainee,
    
    // Business Operations
    scheduleReview,
    updateSkill,
    addCertification,
    
    // Dialog handling
    handleDialogAction,
    
    // Store access
    loading: store.ui.isLoading,
    error: store.ui.error
  };
};