import { useState, useEffect, useCallback } from 'react';
import { GraduateTraineeProgram, graduateTraineeApi } from '@/lib/api/graduateTrainee';
import { getMockTrainees, addMockTrainee, updateMockTrainee, deleteMockTrainee } from '@/lib/api/graduateTraineeMock';
import { useToast } from '@/components/ui/use-toast';

const USE_MOCK_DATA = true; // Toggle this for development

export const useTraineeData = () => {
  const [trainees, setTrainees] = useState<GraduateTraineeProgram[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  const fetchTrainees = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      if (USE_MOCK_DATA) {
        // Use mock data in development
        setTimeout(() => {
          const data = getMockTrainees();
          setTrainees(data);
          setLoading(false);
        }, 500); // Simulate network delay
      } else {
        const data = await graduateTraineeApi.getAllPrograms();
        setTrainees(data);
        setLoading(false);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch trainees';
      setError(errorMessage);
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
      setLoading(false);
    }
  }, [toast]);

  const createTrainee = useCallback(async (traineeData: any) => {
    try {
      let newTrainee;
      if (USE_MOCK_DATA) {
        newTrainee = addMockTrainee(traineeData);
      } else {
        newTrainee = await graduateTraineeApi.createProgram(traineeData);
      }
      setTrainees(prev => [...prev, newTrainee]);
      toast({
        title: 'Success',
        description: 'Trainee program created successfully',
      });
      return newTrainee;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create trainee';
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
      throw err;
    }
  }, [toast]);

  const updateTrainee = useCallback(async (id: number, traineeData: any) => {
    try {
      let updatedTrainee;
      if (USE_MOCK_DATA) {
        updatedTrainee = updateMockTrainee(id, traineeData);
        if (!updatedTrainee) throw new Error('Trainee not found');
      } else {
        updatedTrainee = await graduateTraineeApi.updateProgram({ id, ...traineeData });
      }
      setTrainees(prev => prev.map(t => t.id === id ? updatedTrainee : t));
      toast({
        title: 'Success',
        description: 'Trainee program updated successfully',
      });
      return updatedTrainee;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update trainee';
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
      throw err;
    }
  }, [toast]);

  const deleteTrainee = useCallback(async (id: number) => {
    try {
      if (USE_MOCK_DATA) {
        const success = deleteMockTrainee(id);
        if (!success) throw new Error('Trainee not found');
      } else {
        await graduateTraineeApi.deleteProgram(id);
      }
      setTrainees(prev => prev.filter(t => t.id !== id));
      toast({
        title: 'Success',
        description: 'Trainee program deleted successfully',
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete trainee';
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
      throw err;
    }
  }, [toast]);

  const refreshTrainee = useCallback(async (id: number) => {
    try {
      let trainee;
      if (USE_MOCK_DATA) {
        trainee = getMockTrainees().find(t => t.id === id);
        if (!trainee) throw new Error('Trainee not found');
      } else {
        trainee = await graduateTraineeApi.getProgram(id);
      }
      setTrainees(prev => prev.map(t => t.id === id ? trainee! : t));
      return trainee;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to refresh trainee data';
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
      throw err;
    }
  }, [toast]);

  useEffect(() => {
    fetchTrainees();
  }, [fetchTrainees]);

  return {
    trainees,
    loading,
    error,
    refetch: fetchTrainees,
    fetchTrainees,
    createTrainee,
    updateTrainee,
    deleteTrainee,
    refreshTrainee,
  };
};