import { useState, useEffect } from 'react';
import type { GraduateTraineeProgram } from '@/lib/api/graduateTrainee';
import { graduateTraineeApi } from '@/lib/api/graduateTrainee';
import { getMockTrainees } from '@/lib/api/graduateTraineeMock';

interface UseTraineeDataReturn {
  trainees: GraduateTraineeProgram[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export const useTraineeData = (): UseTraineeDataReturn => {
  const [trainees, setTrainees] = useState<GraduateTraineeProgram[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchTrainees = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Use mock data in development
      if (process.env.NODE_ENV === 'development') {
        await new Promise(resolve => setTimeout(resolve, 500)); // Simulate network delay
        const mockData = getMockTrainees();
        setTrainees(mockData);
      } else {
        const data = await graduateTraineeApi.getAllPrograms();
        setTrainees(data);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch trainees');
      console.error('Failed to fetch trainees:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTrainees();
  }, []);

  return {
    trainees,
    loading,
    error,
    refetch: fetchTrainees
  };
};