import { useCallback } from 'react';
import type { GraduateTraineeProgram } from '@/lib/api/graduateTrainee';

export const useTraineeExport = () => {
  const exportToCSV = useCallback((trainees: GraduateTraineeProgram[], type: string = 'all') => {
    if (!trainees || trainees.length === 0) {
      console.warn('No trainees to export');
      return;
    }

    // Define CSV headers
    const headers = [
      'Employee ID',
      'Employee Name', 
      'Department',
      'Position',
      'Manager',
      'Mentor',
      'Start Date',
      'Expected End Date',
      'Status',
      'Overall Progress (%)',
      'Training Hours Completed',
      'Training Hours Required',
      'Notes'
    ];

    // Convert trainees to CSV rows
    const rows = trainees.map(trainee => [
      trainee.employeeId,
      trainee.employeeName,
      trainee.department,
      trainee.position,
      trainee.manager,
      trainee.mentor || '',
      trainee.startDate,
      trainee.expectedEndDate,
      trainee.status,
      trainee.overallProgress,
      trainee.trainingHoursCompleted,
      trainee.trainingHoursRequired,
      trainee.notes || ''
    ]);

    // Create CSV content
    const csvContent = [
      headers.join(','),
      ...rows.map(row => row.map(field => `"${field}"`).join(','))
    ].join('\n');

    // Create and download file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    
    link.setAttribute('href', url);
    link.setAttribute('download', `trainee-export-${type}-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    console.log(`Exported ${trainees.length} trainees to CSV`);
  }, []);

  const exportToJSON = useCallback((trainees: GraduateTraineeProgram[], type: string = 'all') => {
    if (!trainees || trainees.length === 0) {
      console.warn('No trainees to export');
      return;
    }

    const jsonContent = JSON.stringify(trainees, null, 2);
    const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    
    link.setAttribute('href', url);
    link.setAttribute('download', `trainee-export-${type}-${new Date().toISOString().split('T')[0]}.json`);
    link.style.visibility = 'hidden';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    console.log(`Exported ${trainees.length} trainees to JSON`);
  }, []);

  return {
    exportToCSV,
    exportToJSON
  };
};