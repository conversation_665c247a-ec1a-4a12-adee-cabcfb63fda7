import { GraduateTraineeProgram } from '@/lib/api/graduateTrainee';
import { useToast } from '@/components/ui/use-toast';

export const useTraineeExport = () => {
  const { toast } = useToast();

  const exportToCSV = (data: GraduateTraineeProgram[], type: string) => {
    let csvContent = '';
    
    switch(type) {
      case 'summary':
        csvContent = generateSummaryReport(data);
        break;
      case 'detailed':
        csvContent = generateDetailedReport(data);
        break;
      case 'progress':
        csvContent = generateProgressReport(data);
        break;
      case 'skills':
        csvContent = generateSkillsReport(data);
        break;
      case 'certifications':
        csvContent = generateCertificationsReport(data);
        break;
      case 'reviews':
        csvContent = generateReviewsReport(data);
        break;
      default:
        csvContent = generateSummaryReport(data);
    }
    
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `graduate-trainee-${type}-report-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
    
    toast({
      title: "Success",
      description: `${type.charAt(0).toUpperCase() + type.slice(1)} report exported successfully`,
    });
  };

  const generateSummaryReport = (data: GraduateTraineeProgram[]) => {
    const headers = ['Employee ID', 'Name', 'Department', 'Status', 'Progress %', 'Start Date', 'Expected End Date'];
    const rows = data.map(p => [
      p.employeeId,
      p.employeeName,
      p.department,
      p.status,
      p.overallProgress,
      p.startDate,
      p.expectedEndDate
    ]);
    return [headers, ...rows].map(row => row.join(',')).join('\n');
  };

  const generateDetailedReport = (data: GraduateTraineeProgram[]) => {
    const headers = ['Employee ID', 'Name', 'Department', 'Position', 'Manager', 'Mentor', 'Status', 'Progress %', 
                    'Start Date', 'Expected End Date', 'Training Hours Completed', 'Training Hours Required',
                    'Budget Allocated', 'Budget Spent', 'Notes'];
    const rows = data.map(p => [
      p.employeeId,
      p.employeeName,
      p.department,
      p.position,
      p.manager,
      p.mentor || '',
      p.status,
      p.overallProgress,
      p.startDate,
      p.expectedEndDate,
      p.trainingHoursCompleted,
      p.trainingHoursRequired,
      p.budgetAllocated,
      p.budgetSpent,
      p.notes || ''
    ]);
    return [headers, ...rows].map(row => row.join(',')).join('\n');
  };

  const generateProgressReport = (data: GraduateTraineeProgram[]) => {
    const headers = ['Employee Name', 'Department', 'Overall Progress %', 'Training Hours Progress %', 'Budget Utilization %'];
    const rows = data.map(p => [
      p.employeeName,
      p.department,
      p.overallProgress,
      Math.round((p.trainingHoursCompleted / p.trainingHoursRequired) * 100),
      Math.round((p.budgetSpent / p.budgetAllocated) * 100)
    ]);
    return [headers, ...rows].map(row => row.join(',')).join('\n');
  };

  const generateSkillsReport = (data: GraduateTraineeProgram[]) => {
    const headers = ['Employee Name', 'Skill Name', 'Category', 'Current Level', 'Target Level', 'Progress %'];
    const rows = data.flatMap(p => 
      p.skillsDevelopment.map(s => [
        p.employeeName,
        s.skillName,
        s.category,
        s.currentLevel,
        s.targetLevel,
        s.progressPercentage
      ])
    );
    return [headers, ...rows].map(row => row.join(',')).join('\n');
  };

  const generateCertificationsReport = (data: GraduateTraineeProgram[]) => {
    const headers = ['Employee Name', 'Certification', 'Issuing Body', 'Status', 'Date Achieved', 'Expiry Date'];
    const rows = data.flatMap(p => 
      p.certifications.map(c => [
        p.employeeName,
        c.name,
        c.issuingBody,
        c.status,
        c.dateAchieved || '',
        c.expiryDate || ''
      ])
    );
    return [headers, ...rows].map(row => row.join(',')).join('\n');
  };

  const generateReviewsReport = (data: GraduateTraineeProgram[]) => {
    const headers = ['Employee Name', 'Review Quarter', 'Reviewer', 'Rating', 'Status', 'Date Completed'];
    const rows = data.flatMap(p => 
      p.quarterlyReviews.map(r => [
        p.employeeName,
        `Q${r.reviewNumber}`,
        r.reviewer,
        r.overallRating || '',
        r.completedDate ? 'Completed' : 'Pending',
        r.completedDate || r.dueDate
      ])
    );
    return [headers, ...rows].map(row => row.join(',')).join('\n');
  };

  return {
    exportToCSV,
    generateSummaryReport,
    generateDetailedReport,
    generateProgressReport,
    generateSkillsReport,
    generateCertificationsReport,
    generateReviewsReport
  };
};