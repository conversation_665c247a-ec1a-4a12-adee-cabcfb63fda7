import { useMemo } from 'react';
import type { GraduateTraineeProgram } from '@/lib/api/graduateTrainee';
import type { TraineeMetrics } from '../types/trainee.types';

export const useTraineeMetrics = (trainees: GraduateTraineeProgram[]): TraineeMetrics => {
  return useMemo(() => {
    if (!trainees || trainees.length === 0) {
      return {
        total: 0,
        active: 0,
        completed: 0,
        onHold: 0,
        notStarted: 0,
        terminated: 0,
        avgProgress: 0,
        reviewsDue: 0,
        certificationsExpiring: 0
      };
    }

    const metrics: TraineeMetrics = {
      total: trainees.length,
      active: trainees.filter(t => t.status === 'in_progress').length,
      completed: trainees.filter(t => t.status === 'completed').length,
      onHold: trainees.filter(t => t.status === 'on_hold').length,
      notStarted: trainees.filter(t => t.status === 'not_started').length,
      terminated: trainees.filter(t => t.status === 'terminated').length,
      avgProgress: Math.round(
        trainees.reduce((sum, t) => sum + t.overallProgress, 0) / trainees.length
      ),
      reviewsDue: trainees.filter(t => {
        return t.quarterlyReviews?.some(r => !r.completedDate && new Date(r.dueDate) <= new Date()) || false;
      }).length,
      certificationsExpiring: trainees.reduce((count, t) => {
        const expiringCount = t.certifications?.filter(c => {
          if (!c.expiryDate || c.status !== 'completed') return false;
          const expiry = new Date(c.expiryDate);
          const thirtyDaysFromNow = new Date();
          thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
          return expiry <= thirtyDaysFromNow;
        }).length || 0;
        return count + expiringCount;
      }, 0)
    };

    return metrics;
  }, [trainees]);
};