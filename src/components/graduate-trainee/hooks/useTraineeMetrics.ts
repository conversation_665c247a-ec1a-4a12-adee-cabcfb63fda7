import { useMemo } from 'react';
import { GraduateTraineeProgram } from '@/lib/api/graduateTrainee';

export interface TraineeMetrics {
  totalTrainees: number;
  activeTrainees: number;
  completedTrainees: number;
  averageProgress: number;
  totalBudgetAllocated: number;
  totalBudgetSpent: number;
  budgetUtilization: number;
  averageTrainingHours: number;
  completionRate: number;
  departmentStats: Record<string, {
    count: number;
    active: number;
    completed: number;
    avgProgress: number;
  }>;
  statusDistribution: Record<string, number>;
  skillProgress: {
    totalSkills: number;
    completedSkills: number;
    averageSkillProgress: number;
  };
  certificationProgress: {
    totalCertifications: number;
    completedCertifications: number;
    averageCertificationProgress: number;
  };
}

export const useTraineeMetrics = (trainees: GraduateTraineeProgram[] = []) => {
  const metrics = useMemo((): TraineeMetrics => {
    // Handle undefined or null trainees array
    const safeTrainees = trainees || [];
    const totalTrainees = safeTrainees.length;
    const activeTrainees = safeTrainees.filter(t => t.status === 'in_progress').length;
    const completedTrainees = safeTrainees.filter(t => t.status === 'completed').length;
    
    const totalBudgetAllocated = safeTrainees.reduce((sum, t) => sum + t.budgetAllocated, 0);
    const totalBudgetSpent = safeTrainees.reduce((sum, t) => sum + t.budgetSpent, 0);
    const budgetUtilization = totalBudgetAllocated > 0 ? (totalBudgetSpent / totalBudgetAllocated) * 100 : 0;
    
    const averageProgress = totalTrainees > 0 
      ? safeTrainees.reduce((sum, t) => sum + t.overallProgress, 0) / totalTrainees 
      : 0;
    
    const averageTrainingHours = totalTrainees > 0 
      ? safeTrainees.reduce((sum, t) => sum + t.trainingHoursCompleted, 0) / totalTrainees 
      : 0;
    
    const completionRate = totalTrainees > 0 ? (completedTrainees / totalTrainees) * 100 : 0;

    const departmentStats = safeTrainees.reduce((acc, trainee) => {
      if (!acc[trainee.department]) {
        acc[trainee.department] = { count: 0, active: 0, completed: 0, avgProgress: 0 };
      }
      
      acc[trainee.department].count++;
      if (trainee.status === 'in_progress') acc[trainee.department].active++;
      if (trainee.status === 'completed') acc[trainee.department].completed++;
      acc[trainee.department].avgProgress += trainee.overallProgress;
      
      return acc;
    }, {} as Record<string, any>);

    // Calculate average progress per department
    Object.keys(departmentStats).forEach(dept => {
      if (departmentStats[dept].count > 0) {
        departmentStats[dept].avgProgress = departmentStats[dept].avgProgress / departmentStats[dept].count;
      }
    });

    const statusDistribution = safeTrainees.reduce((acc, trainee) => {
      acc[trainee.status] = (acc[trainee.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const allSkills = safeTrainees.flatMap(t => t.skillsDevelopment || []);
    const totalSkills = allSkills.length;
    const completedSkills = allSkills.filter(s => s.progressPercentage === 100).length;
    const averageSkillProgress = totalSkills > 0 
      ? allSkills.reduce((sum, s) => sum + s.progressPercentage, 0) / totalSkills 
      : 0;

    const allCertifications = safeTrainees.flatMap(t => t.certifications || []);
    const totalCertifications = allCertifications.length;
    const completedCertifications = allCertifications.filter(c => c.status === 'completed').length;
    const averageCertificationProgress = totalCertifications > 0 
      ? (completedCertifications / totalCertifications) * 100 
      : 0;

    return {
      totalTrainees,
      activeTrainees,
      completedTrainees,
      averageProgress: Math.round(averageProgress),
      totalBudgetAllocated,
      totalBudgetSpent,
      budgetUtilization: Math.round(budgetUtilization),
      averageTrainingHours: Math.round(averageTrainingHours),
      completionRate: Math.round(completionRate),
      departmentStats,
      statusDistribution,
      skillProgress: {
        totalSkills,
        completedSkills,
        averageSkillProgress: Math.round(averageSkillProgress)
      },
      certificationProgress: {
        totalCertifications,
        completedCertifications,
        averageCertificationProgress: Math.round(averageCertificationProgress)
      }
    };
  }, [trainees]);

  const getTrendData = useMemo(() => {
    const safeTrainees = trainees || [];
    const monthlyData = safeTrainees.reduce((acc, trainee) => {
      const monthYear = new Date(trainee.startDate).toISOString().slice(0, 7);
      if (!acc[monthYear]) {
        acc[monthYear] = { newTrainees: 0, completedTrainees: 0, totalProgress: 0, count: 0 };
      }
      
      acc[monthYear].newTrainees++;
      acc[monthYear].count++;
      acc[monthYear].totalProgress += trainee.overallProgress;
      
      if (trainee.status === 'completed' && trainee.actualEndDate) {
        const endMonthYear = new Date(trainee.actualEndDate).toISOString().slice(0, 7);
        if (!acc[endMonthYear]) {
          acc[endMonthYear] = { newTrainees: 0, completedTrainees: 0, totalProgress: 0, count: 0 };
        }
        acc[endMonthYear].completedTrainees++;
      }
      
      return acc;
    }, {} as Record<string, any>);

    return Object.entries(monthlyData)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([month, data]) => ({
        month,
        newTrainees: data.newTrainees,
        completedTrainees: data.completedTrainees,
        avgProgress: data.count > 0 ? Math.round(data.totalProgress / data.count) : 0
      }));
  }, [trainees]);

  const getQuarterlyPerformance = useMemo(() => {
    const safeTrainees = trainees || [];
    const quarterlyData = safeTrainees.reduce((acc, trainee) => {
      const reviews = trainee.quarterlyReviews || [];
      reviews.forEach(review => {
        const quarter = `Q${review.reviewNumber}`;
        if (!acc[quarter]) {
          acc[quarter] = { totalReviews: 0, completedReviews: 0, avgRating: 0, satisfactory: 0 };
        }
        
        acc[quarter].totalReviews++;
        if (review.completedDate) {
          acc[quarter].completedReviews++;
          if (review.overallRating) {
            acc[quarter].avgRating += review.overallRating;
          }
          if (review.isSatisfactory) {
            acc[quarter].satisfactory++;
          }
        }
      });
      
      return acc;
    }, {} as Record<string, any>);

    return Object.entries(quarterlyData)
      .map(([quarter, data]) => ({
        quarter,
        completionRate: data.totalReviews > 0 ? Math.round((data.completedReviews / data.totalReviews) * 100) : 0,
        avgRating: data.completedReviews > 0 ? Math.round(data.avgRating / data.completedReviews) : 0,
        satisfactionRate: data.completedReviews > 0 ? Math.round((data.satisfactory / data.completedReviews) * 100) : 0
      }))
      .sort((a, b) => a.quarter.localeCompare(b.quarter));
  }, [trainees]);

  return {
    metrics,
    getTrendData,
    getQuarterlyPerformance,
  };
};