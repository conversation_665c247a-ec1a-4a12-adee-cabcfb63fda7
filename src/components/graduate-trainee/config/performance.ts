/**
 * Performance Configuration
 * Optimizations for reducing lag in the Graduate Trainee Module
 */

export const performanceConfig = {
  // Animation settings
  animations: {
    // Reduced animation durations for snappier feel
    duration: {
      fast: 150,
      normal: 300,
      slow: 500,
    },
    // Simpler easing functions
    easing: 'ease-out',
    // Disable animations on low-end devices
    reducedMotion: window.matchMedia('(prefers-reduced-motion: reduce)').matches,
  },

  // Virtualization settings
  virtualization: {
    // Number of items to render in view
    itemsPerPage: 20,
    // Buffer for off-screen items
    overscan: 5,
    // Enable virtual scrolling for lists with more items
    threshold: 50,
  },

  // Debounce/throttle timings
  timings: {
    searchDebounce: 300,
    scrollThrottle: 100,
    resizeDebounce: 250,
  },

  // Image optimization
  images: {
    lazyLoad: true,
    placeholder: 'blur',
    quality: 85,
  },

  // Component optimization
  components: {
    // Use React.memo for these components
    memoize: [
      'TraineeCard',
      'TraineeListItem',
      'StatCard',
      'GlassCard',
      'GlassButton',
    ],
    // Components that should use virtualization
    virtualize: [
      'TraineeGrid',
      'TraineeList',
      'ReportsList',
    ],
  },

  // State management
  state: {
    // Batch state updates
    batchUpdates: true,
    // Use immer for immutable updates
    useImmer: true,
    // Persist only essential data
    persistKeys: ['filters', 'viewMode', 'preferences'],
  },

  // CSS optimizations
  css: {
    // Reduce blur intensity
    backdropBlur: {
      sm: 'blur(4px)',
      md: 'blur(8px)',
      lg: 'blur(12px)',
    },
    // Simplified gradients
    useSimpleGradients: true,
    // Reduce shadow complexity
    reduceShadows: true,
  },
};

// Performance monitoring utilities
export const measurePerformance = (name: string, fn: () => void) => {
  if (process.env.NODE_ENV === 'development') {
    performance.mark(`${name}-start`);
    fn();
    performance.mark(`${name}-end`);
    performance.measure(name, `${name}-start`, `${name}-end`);
    const measure = performance.getEntriesByName(name)[0];
    console.log(`⚡ ${name}: ${measure.duration.toFixed(2)}ms`);
  } else {
    fn();
  }
};

// Check if user has low-end device
export const isLowEndDevice = () => {
  return (
    navigator.hardwareConcurrency <= 4 ||
    navigator.deviceMemory <= 4 ||
    window.matchMedia('(prefers-reduced-motion: reduce)').matches
  );
};

// Apply performance optimizations based on device
export const getOptimizedConfig = () => {
  if (isLowEndDevice()) {
    return {
      ...performanceConfig,
      animations: {
        ...performanceConfig.animations,
        duration: {
          fast: 100,
          normal: 200,
          slow: 300,
        },
      },
      css: {
        ...performanceConfig.css,
        backdropBlur: {
          sm: 'blur(2px)',
          md: 'blur(4px)',
          lg: 'blur(6px)',
        },
      },
    };
  }
  return performanceConfig;
};

export default performanceConfig;