import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';

export const SettingsSection: React.FC = () => {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Program Settings</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium">Default Program Duration</h3>
                <p className="text-sm text-muted-foreground">Standard duration for new programs</p>
              </div>
              <select className="px-3 py-2 border rounded">
                <option value="12">12 months</option>
                <option value="18">18 months</option>
                <option value="24">24 months</option>
              </select>
            </div>
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium">Review Frequency</h3>
                <p className="text-sm text-muted-foreground">How often reviews are scheduled</p>
              </div>
              <select className="px-3 py-2 border rounded">
                <option value="monthly">Monthly</option>
                <option value="quarterly">Quarterly</option>
                <option value="biannually">Bi-annually</option>
              </select>
            </div>
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium">Auto-archive Completed Programs</h3>
                <p className="text-sm text-muted-foreground">Archive programs after completion</p>
              </div>
              <input type="checkbox" className="toggle" />
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>Notification Settings</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium">Review Reminders</h3>
                <p className="text-sm text-muted-foreground">Send reminders before reviews are due</p>
              </div>
              <input type="checkbox" className="toggle" defaultChecked />
            </div>
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium">Progress Updates</h3>
                <p className="text-sm text-muted-foreground">Weekly progress summary emails</p>
              </div>
              <input type="checkbox" className="toggle" defaultChecked />
            </div>
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium">Certification Expiry Alerts</h3>
                <p className="text-sm text-muted-foreground">Alert before certifications expire</p>
              </div>
              <input type="checkbox" className="toggle" defaultChecked />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};