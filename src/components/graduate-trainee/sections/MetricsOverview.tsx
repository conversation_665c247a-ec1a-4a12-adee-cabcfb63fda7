import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Users, GraduationCap, BarChart3, Calendar } from 'lucide-react';

interface MetricsOverviewProps {
  metrics: {
    totalTrainees: number;
    newThisMonth: number;
    activePrograms: number;
    completionRate: number;
    avgProgress: number;
    progressTrend: number;
    reviewsDue: number;
    overdueReviews: number;
  };
}

export const MetricsOverview: React.FC<MetricsOverviewProps> = ({ metrics }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground">Total Trainees</p>
              <p className="text-2xl font-bold">{metrics.totalTrainees}</p>
              <p className="text-xs text-green-600">+{metrics.newThisMonth} this month</p>
            </div>
            <Users className="h-8 w-8 text-muted-foreground" />
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground">Active Programs</p>
              <p className="text-2xl font-bold">{metrics.activePrograms}</p>
              <p className="text-xs text-blue-600">{metrics.completionRate}% completion rate</p>
            </div>
            <GraduationCap className="h-8 w-8 text-muted-foreground" />
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground">Avg Progress</p>
              <p className="text-2xl font-bold">{metrics.avgProgress}%</p>
              <p className="text-xs text-orange-600">↑ {metrics.progressTrend}% trend</p>
            </div>
            <BarChart3 className="h-8 w-8 text-muted-foreground" />
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground">Reviews Due</p>
              <p className="text-2xl font-bold">{metrics.reviewsDue}</p>
              <p className="text-xs text-red-600">{metrics.overdueReviews} overdue</p>
            </div>
            <Calendar className="h-8 w-8 text-muted-foreground" />
          </div>
        </CardContent>
      </Card>
    </div>
  );
};