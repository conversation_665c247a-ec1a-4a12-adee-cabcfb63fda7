import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { FileText } from 'lucide-react';

interface ReportsSectionProps {
  programs: any[];
  onExport: (type: string) => void;
}

export const ReportsSection: React.FC<ReportsSectionProps> = ({ programs, onExport }) => {
  
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Report Generation</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <Button 
              onClick={() => onExport('summary')}
              className="justify-start"
            >
              <FileText className="mr-2 h-4 w-4" />
              Summary Report
            </Button>
            <Button 
              onClick={() => onExport('detailed')}
              className="justify-start"
            >
              <FileText className="mr-2 h-4 w-4" />
              Detailed Report
            </Button>
            <Button 
              onClick={() => onExport('progress')}
              className="justify-start"
            >
              <FileText className="mr-2 h-4 w-4" />
              Progress Report
            </Button>
            <Button 
              onClick={() => onExport('skills')}
              className="justify-start"
            >
              <FileText className="mr-2 h-4 w-4" />
              Skills Report
            </Button>
            <Button 
              onClick={() => onExport('certifications')}
              className="justify-start"
            >
              <FileText className="mr-2 h-4 w-4" />
              Certifications Report
            </Button>
            <Button 
              onClick={() => onExport('reviews')}
              className="justify-start"
            >
              <FileText className="mr-2 h-4 w-4" />
              Reviews Report
            </Button>
          </div>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>Scheduled Reports</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <h3 className="font-medium">Monthly Progress Summary</h3>
                <p className="text-sm text-muted-foreground">Next: End of month</p>
              </div>
              <Button size="sm" variant="outline">Configure</Button>
            </div>
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <h3 className="font-medium">Quarterly Review Report</h3>
                <p className="text-sm text-muted-foreground">Next: End of quarter</p>
              </div>
              <Button size="sm" variant="outline">Configure</Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};