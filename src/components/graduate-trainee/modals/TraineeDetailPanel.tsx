import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>er, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  Calendar, 
  User, 
  Building, 
  Clock, 
  Target, 
  Award, 
  TrendingUp, 
  Edit, 
  Plus,
  CheckCircle,
  AlertCircle,
  XCircle
} from 'lucide-react';
import { GraduateTraineeProgram, QuarterlyReview, SkillDevelopment, Certification } from '@/lib/api/graduateTrainee';
import { QuarterlyReviewForm } from './QuarterlyReviewForm';

interface TraineeDetailPanelProps {
  trainee: GraduateTraineeProgram;
  onClose: () => void;
  onEdit: () => void;
  onUpdateReview: (review: QuarterlyReview) => void;
  onUpdateSkill: (skill: SkillDevelopment) => void;
  onUpdateCertification: (cert: Certification) => void;
}

export const TraineeDetailPanel: React.FC<TraineeDetailPanelProps> = ({
  trainee,
  onClose,
  onEdit,
  onUpdateReview,
  onUpdateSkill,
  onUpdateCertification
}) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [showReviewForm, setShowReviewForm] = useState(false);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'terminated': return <XCircle className="h-4 w-4 text-red-500" />;
      case 'on_hold': return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      default: return <Clock className="h-4 w-4 text-blue-500" />;
    }
  };

  const getCertificationStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'expired': return <XCircle className="h-4 w-4 text-red-500" />;
      case 'in_progress': return <Clock className="h-4 w-4 text-blue-500" />;
      default: return <AlertCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="sticky top-0 bg-white border-b px-6 py-4 flex justify-between items-center">
          <h2 className="text-2xl font-bold">Trainee Details</h2>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={onEdit}>
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Button>
            <Button variant="outline" size="sm" onClick={onClose}>Close</Button>
          </div>
        </div>

        <div className="p-6">
          {/* Trainee Header */}
          <Card className="mb-6">
            <CardContent className="pt-6">
              <div className="flex flex-wrap items-center justify-between gap-4">
                <div className="flex items-center gap-4">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                    <User className="h-8 w-8 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold">{trainee.employeeName}</h3>
                    <p className="text-gray-600">ID: {trainee.employeeId}</p>
                  </div>
                </div>
                <div className="flex items-center gap-4">
                  <Badge variant="secondary" className="flex items-center gap-1">
                    {getStatusIcon(trainee.status)}
                    {trainee.status.replace('_', ' ')}
                  </Badge>
                  <div className="text-right">
                    <p className="text-sm text-gray-600">Program Progress</p>
                    <p className="text-lg font-semibold">{Math.round((trainee.overallProgress || 0) * 100)}%</p>
                  </div>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
                <div className="flex items-center gap-2">
                  <Building className="h-4 w-4 text-gray-500" />
                  <span>{trainee.department}</span>
                </div>
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-gray-500" />
                  <span>Manager: {trainee.manager}</span>
                </div>
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-gray-500" />
                  <span>Mentor: {trainee.mentor || 'Not assigned'}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span>Start: {new Date(trainee.startDate).toLocaleDateString()}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span>End: {new Date(trainee.expectedEndDate).toLocaleDateString()}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-gray-500" />
                  <span>{trainee.programDurationMonths} months</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Tabs */}
          <div className="border-b">
            <nav className="-mb-px flex space-x-8">
              <button
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'overview'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
                onClick={() => setActiveTab('overview')}
              >
                Overview
              </button>
              <button
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'reviews'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
                onClick={() => setActiveTab('reviews')}
              >
                Quarterly Reviews
              </button>
              <button
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'skills'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
                onClick={() => setActiveTab('skills')}
              >
                Skills Development
              </button>
              <button
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'certifications'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
                onClick={() => setActiveTab('certifications')}
              >
                Certifications
              </button>
            </nav>
          </div>

          {/* Overview Tab */}
          {activeTab === 'overview' && (
            <div className="mt-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <h3 className="text-lg font-semibold">Training Progress</h3>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <div className="flex justify-between mb-1">
                          <span className="text-sm">Hours Completed</span>
                          <span className="text-sm">{trainee.trainingHoursCompleted} / {trainee.trainingHoursRequired}</span>
                        </div>
                        <Progress value={(trainee.trainingHoursCompleted / trainee.trainingHoursRequired) * 100} />
                      </div>
                      <div>
                        <div className="flex justify-between mb-1">
                          <span className="text-sm">Budget Utilization</span>
                          <span className="text-sm">${trainee.budgetSpent} / ${trainee.budgetAllocated}</span>
                        </div>
                        <Progress value={(trainee.budgetSpent / trainee.budgetAllocated) * 100} />
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <h3 className="text-lg font-semibold">Program Notes</h3>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-700">{trainee.notes || 'No notes available.'}</p>
                  </CardContent>
                </Card>
              </div>
            </div>
          )}

          {/* Reviews Tab */}
          {activeTab === 'reviews' && (
            <div className="mt-6">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                  <h3 className="text-lg font-semibold">Quarterly Reviews</h3>
                  <Button size="sm" onClick={() => setShowReviewForm(true)}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Review
                  </Button>
                </CardHeader>
                <CardContent>
                  {showReviewForm ? (
                    <div className="mb-6">
                      <QuarterlyReviewForm 
                        onSubmit={(review) => {
                          onUpdateReview(review);
                          setShowReviewForm(false);
                        }} 
                        onCancel={() => setShowReviewForm(false)}
                      />
                    </div>
                  ) : null}
                  
                  {trainee.quarterlyReviews && trainee.quarterlyReviews.length > 0 ? (
                    <div className="space-y-4">
                      {trainee.quarterlyReviews
                        .sort((a, b) => a.reviewNumber - b.reviewNumber)
                        .map((review) => (
                          <div key={review.reviewNumber} className="border rounded-lg p-4">
                            <div className="flex justify-between items-start">
                              <div>
                                <h4 className="font-semibold">Quarter {review.reviewNumber}</h4>
                                <p className="text-sm text-gray-600">
                                  Due: {new Date(review.dueDate).toLocaleDateString()}
                                  {review.completedDate && ` | Completed: ${new Date(review.completedDate).toLocaleDateString()}`}
                                </p>
                              </div>
                              <Badge variant={review.isSatisfactory ? 'success' : 'destructive'}>
                                {review.isSatisfactory ? 'Satisfactory' : 'Needs Improvement'}
                              </Badge>
                            </div>
                            <div className="mt-3">
                              <p className="text-sm"><strong>Rating:</strong> {review.overallRating}/5</p>
                              <p className="text-sm mt-1"><strong>Reviewer:</strong> {review.reviewer}</p>
                              <p className="text-sm mt-2"><strong>Feedback:</strong> {review.feedback}</p>
                              <div className="mt-3 grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                                <div>
                                  <p><strong>Goals Achieved:</strong></p>
                                  <ul className="list-disc list-inside">
                                    {review.goalsAchieved?.map((goal, idx) => (
                                      <li key={idx}>{goal}</li>
                                    )) || <li>None</li>}
                                  </ul>
                                </div>
                                <div>
                                  <p><strong>Areas for Improvement:</strong></p>
                                  <ul className="list-disc list-inside">
                                    {review.areasForImprovement?.map((area, idx) => (
                                      <li key={idx}>{area}</li>
                                    )) || <li>None</li>}
                                  </ul>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))
                      }
                    </div>
                  ) : (
                    <p className="text-gray-500 text-center py-8">No quarterly reviews recorded yet.</p>
                  )}
                </CardContent>
              </Card>
            </div>
          )}

          {/* Skills Tab */}
          {activeTab === 'skills' && (
            <div className="mt-6">
              <Card>
                <CardHeader>
                  <h3 className="text-lg font-semibold">Skills Development</h3>
                </CardHeader>
                <CardContent>
                  {trainee.skillsDevelopment && trainee.skillsDevelopment.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {trainee.skillsDevelopment.map((skill, index) => (
                        <div key={index} className="border rounded-lg p-4">
                          <div className="flex justify-between items-start">
                            <div>
                              <h4 className="font-semibold">{skill.skillName}</h4>
                              <p className="text-sm text-gray-600">{skill.category}</p>
                            </div>
                            <Badge variant="secondary">{skill.progressPercentage}%</Badge>
                          </div>
                          <div className="mt-3">
                            <div className="flex justify-between mb-1">
                              <span className="text-sm">Level Progress</span>
                              <span className="text-sm">{skill.currentLevel} / {skill.targetLevel}</span>
                            </div>
                            <Progress value={skill.progressPercentage} />
                            <p className="text-sm mt-2"><strong>Next Steps:</strong> {skill.nextSteps || 'Not specified'}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-500 text-center py-8">No skills development data available.</p>
                  )}
                </CardContent>
              </Card>
            </div>
          )}

          {/* Certifications Tab */}
          {activeTab === 'certifications' && (
            <div className="mt-6">
              <Card>
                <CardHeader>
                  <h3 className="text-lg font-semibold">Certifications</h3>
                </CardHeader>
                <CardContent>
                  {trainee.certifications && trainee.certifications.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {trainee.certifications.map((cert, index) => (
                        <div key={index} className="border rounded-lg p-4">
                          <div className="flex justify-between items-start">
                            <div>
                              <h4 className="font-semibold">{cert.name}</h4>
                              <p className="text-sm text-gray-600">{cert.issuingBody}</p>
                            </div>
                            <div className="flex items-center gap-1">
                              {getCertificationStatusIcon(cert.status)}
                              <Badge variant={cert.status === 'completed' ? 'success' : cert.status === 'expired' ? 'destructive' : 'secondary'}>
                                {cert.status.replace('_', ' ')}
                              </Badge>
                            </div>
                          </div>
                          <div className="mt-3 text-sm">
                            <p><strong>Mandatory:</strong> {cert.isMandatory ? 'Yes' : 'No'}</p>
                            {cert.dateAchieved && (
                              <p><strong>Achieved:</strong> {new Date(cert.dateAchieved).toLocaleDateString()}</p>
                            )}
                            {cert.expiryDate && (
                              <p><strong>Expires:</strong> {new Date(cert.expiryDate).toLocaleDateString()}</p>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-500 text-center py-8">No certifications recorded.</p>
                  )}
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};