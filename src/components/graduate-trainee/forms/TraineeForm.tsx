import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  User, 
  Building, 
  Clock, 
  Target, 
  DollarSign,
  Calendar,
  Plus,
  X,
  Briefcase,
  Award
} from 'lucide-react';
import { 
  GraduateTraineeProgram, 
  CreateGraduateTraineeProgramRequest, 
  UpdateGraduateTraineeProgramRequest,
  SkillDevelopment,
  Certification
} from '@/lib/api/graduateTrainee';

interface TraineeFormProps {
  trainee?: GraduateTraineeProgram | null;
  onSubmit: (data: CreateGraduateTraineeProgramRequest | UpdateGraduateTraineeProgramRequest) => void;
  onCancel: () => void;
  isSubmitting?: boolean;
}

export const TraineeForm: React.FC<TraineeFormProps> = ({
  trainee,
  onSubmit,
  onCancel,
  isSubmitting = false
}) => {
  const isEditing = !!trainee?.id;
  const [activeTab, setActiveTab] = useState('basic');
  
  const [formData, setFormData] = useState({
    employeeId: trainee?.employeeId || '',
    employeeName: trainee?.employeeName || '',
    department: trainee?.department || '',
    position: trainee?.position || '',
    manager: trainee?.manager || '',
    mentor: trainee?.mentor || '',
    startDate: trainee?.startDate || new Date().toISOString().split('T')[0],
    expectedEndDate: trainee?.expectedEndDate || new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    programDurationMonths: trainee?.programDurationMonths || 12,
    trainingHoursRequired: trainee?.trainingHoursRequired || 240,
    budgetAllocated: trainee?.budgetAllocated || 10000,
    notes: trainee?.notes || '',
    status: trainee?.status || 'not_started',
    overallProgress: trainee?.overallProgress || 0,
    trainingHoursCompleted: trainee?.trainingHoursCompleted || 0,
    budgetSpent: trainee?.budgetSpent || 0,
    actualEndDate: trainee?.actualEndDate || '',
  });

  const [skills, setSkills] = useState<SkillDevelopment[]>(trainee?.skillsDevelopment || []);
  const [certifications, setCertifications] = useState<Certification[]>(trainee?.certifications || []);

  const departments = ['Engineering', 'Marketing', 'Finance', 'HR', 'Operations', 'Sales'];
  const positions = ['Software Engineer', 'Marketing Specialist', 'Financial Analyst', 'HR Coordinator', 'Operations Manager', 'Sales Representative'];

  const updateFormData = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const submitData: any = {
      ...formData,
      skillsDevelopment: skills,
      certifications: certifications
    };

    if (isEditing) {
      submitData.id = trainee.id;
    }

    onSubmit(submitData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4 py-4">
      {/* Use Tabs for better organization */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className={`grid w-full ${isEditing ? 'grid-cols-3' : 'grid-cols-2'} h-10`}>
          <TabsTrigger value="basic" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">
            Basic Info
          </TabsTrigger>
          <TabsTrigger value="program" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">
            Program Details
          </TabsTrigger>
          {isEditing && (
            <TabsTrigger value="progress" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">
              Progress
            </TabsTrigger>
          )}
        </TabsList>

        {/* Basic Information Tab */}
        <TabsContent value="basic" className="space-y-4 mt-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="employeeId">Employee ID *</Label>
              <Input
                id="employeeId"
                value={formData.employeeId}
                onChange={(e) => updateFormData('employeeId', e.target.value)}
                required
                disabled={isEditing}
                placeholder="EMP001"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="employeeName">Employee Name *</Label>
              <Input
                id="employeeName"
                value={formData.employeeName}
                onChange={(e) => updateFormData('employeeName', e.target.value)}
                required
                placeholder="John Doe"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="department">Department *</Label>
              <Select value={formData.department} onValueChange={(value) => updateFormData('department', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select department" />
                </SelectTrigger>
                <SelectContent>
                  {departments.map(dept => (
                    <SelectItem key={dept} value={dept}>{dept}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="position">Position *</Label>
              <Select value={formData.position} onValueChange={(value) => updateFormData('position', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select position" />
                </SelectTrigger>
                <SelectContent>
                  {positions.map(pos => (
                    <SelectItem key={pos} value={pos}>{pos}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="manager">Manager *</Label>
              <Input
                id="manager"
                value={formData.manager}
                onChange={(e) => updateFormData('manager', e.target.value)}
                required
                placeholder="Manager Name"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="mentor">Mentor</Label>
              <Input
                id="mentor"
                value={formData.mentor}
                onChange={(e) => updateFormData('mentor', e.target.value)}
                placeholder="Mentor Name (Optional)"
              />
            </div>
          </div>
        </TabsContent>

        {/* Program Details Tab */}
        <TabsContent value="program" className="space-y-4 mt-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="startDate">Start Date *</Label>
              <Input
                id="startDate"
                type="date"
                value={formData.startDate}
                onChange={(e) => updateFormData('startDate', e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="expectedEndDate">Expected End Date *</Label>
              <Input
                id="expectedEndDate"
                type="date"
                value={formData.expectedEndDate}
                onChange={(e) => updateFormData('expectedEndDate', e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="programDurationMonths">Duration (Months) *</Label>
              <Input
                id="programDurationMonths"
                type="number"
                value={formData.programDurationMonths}
                onChange={(e) => updateFormData('programDurationMonths', parseInt(e.target.value))}
                required
                min="1"
                max="36"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="trainingHoursRequired">Training Hours Required *</Label>
              <Input
                id="trainingHoursRequired"
                type="number"
                value={formData.trainingHoursRequired}
                onChange={(e) => updateFormData('trainingHoursRequired', parseInt(e.target.value))}
                required
                min="0"
              />
            </div>
            <div className="space-y-2 col-span-2">
              <Label htmlFor="budgetAllocated">Budget Allocated ($) *</Label>
              <Input
                id="budgetAllocated"
                type="number"
                value={formData.budgetAllocated}
                onChange={(e) => updateFormData('budgetAllocated', parseFloat(e.target.value))}
                required
                min="0"
                step="100"
              />
            </div>
            <div className="space-y-2 col-span-2">
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                id="notes"
                value={formData.notes}
                onChange={(e) => updateFormData('notes', e.target.value)}
                placeholder="Additional notes or comments..."
                rows={3}
              />
            </div>
          </div>
        </TabsContent>

        {/* Progress Tab (only for editing) */}
        {isEditing && (
          <TabsContent value="progress" className="space-y-4 mt-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select value={formData.status} onValueChange={(value) => updateFormData('status', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="not_started">Not Started</SelectItem>
                    <SelectItem value="in_progress">In Progress</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="on_hold">On Hold</SelectItem>
                    <SelectItem value="terminated">Terminated</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="overallProgress">Overall Progress (%)</Label>
                <Input
                  id="overallProgress"
                  type="number"
                  value={formData.overallProgress}
                  onChange={(e) => updateFormData('overallProgress', parseInt(e.target.value))}
                  min="0"
                  max="100"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="trainingHoursCompleted">Training Hours Completed</Label>
                <Input
                  id="trainingHoursCompleted"
                  type="number"
                  value={formData.trainingHoursCompleted}
                  onChange={(e) => updateFormData('trainingHoursCompleted', parseInt(e.target.value))}
                  min="0"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="budgetSpent">Budget Spent ($)</Label>
                <Input
                  id="budgetSpent"
                  type="number"
                  value={formData.budgetSpent}
                  onChange={(e) => updateFormData('budgetSpent', parseFloat(e.target.value))}
                  min="0"
                  step="100"
                />
              </div>
              {formData.status === 'completed' && (
                <div className="space-y-2 col-span-2">
                  <Label htmlFor="actualEndDate">Actual End Date</Label>
                  <Input
                    id="actualEndDate"
                    type="date"
                    value={formData.actualEndDate}
                    onChange={(e) => updateFormData('actualEndDate', e.target.value)}
                  />
                </div>
              )}
            </div>
          </TabsContent>
        )}
      </Tabs>

      {/* Form Actions */}
      <div className="flex justify-end gap-3 pt-4 mt-6 border-t border-border">
        <Button 
          type="button" 
          variant="outline" 
          onClick={onCancel}
          disabled={isSubmitting}
        >
          Cancel
        </Button>
        <Button 
          type="submit" 
          disabled={isSubmitting}
          className="min-w-[120px]"
        >
          {isSubmitting ? (
            <span className="flex items-center gap-2">
              <span className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></span>
              Saving...
            </span>
          ) : (
            <span>{isEditing ? 'Update' : 'Create'} Trainee</span>
          )}
        </Button>
      </div>
    </form>
  );
};