import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  User, 
  Building, 
  Clock, 
  Target, 
  DollarSign,
  Calendar,
  Plus,
  X
} from 'lucide-react';
import { 
  GraduateTraineeProgram, 
  CreateGraduateTraineeProgramRequest, 
  UpdateGraduateTraineeProgramRequest,
  SkillDevelopment,
  Certification
} from '@/lib/api/graduateTrainee';

interface TraineeFormProps {
  trainee?: GraduateTraineeProgram;
  onSubmit: (data: CreateGraduateTraineeProgramRequest | UpdateGraduateTraineeProgramRequest) => void;
  onCancel: () => void;
  isSubmitting?: boolean;
}

export const TraineeForm: React.FC<TraineeFormProps> = ({
  trainee,
  onSubmit,
  onCancel,
  isSubmitting = false
}) => {
  const isEditing = !!trainee?.id;
  
  const [formData, setFormData] = useState({
    employeeId: trainee?.employeeId || '',
    employeeName: trainee?.employeeName || '',
    department: trainee?.department || '',
    position: trainee?.position || '',
    manager: trainee?.manager || '',
    mentor: trainee?.mentor || '',
    startDate: trainee?.startDate || new Date().toISOString().split('T')[0],
    expectedEndDate: trainee?.expectedEndDate || new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    programDurationMonths: trainee?.programDurationMonths || 12,
    trainingHoursRequired: trainee?.trainingHoursRequired || 240,
    budgetAllocated: trainee?.budgetAllocated || 10000,
    notes: trainee?.notes || '',
    status: trainee?.status || 'not_started',
    overallProgress: trainee?.overallProgress || 0,
    trainingHoursCompleted: trainee?.trainingHoursCompleted || 0,
    budgetSpent: trainee?.budgetSpent || 0,
    actualEndDate: trainee?.actualEndDate || '',
  });

  const [skills, setSkills] = useState<SkillDevelopment[]>(trainee?.skillsDevelopment || []);
  const [certifications, setCertifications] = useState<Certification[]>(trainee?.certifications || []);

  const [newSkill, setNewSkill] = useState({
    skillName: '',
    category: '',
    baselineLevel: 1,
    targetLevel: 5,
    currentLevel: 1,
    progressPercentage: 0,
    trainingCompleted: [] as string[],
    nextSteps: ''
  });

  const [newCertification, setNewCertification] = useState({
    name: '',
    issuingBody: '',
    dateAchieved: '',
    expiryDate: '',
    isMandatory: false,
    status: 'not_started' as 'not_started' | 'in_progress' | 'completed' | 'expired' | 'waived'
  });

  const departments = ['Engineering', 'Marketing', 'Finance', 'HR', 'Operations', 'Sales'];
  const positions = ['Software Engineer', 'Marketing Specialist', 'Financial Analyst', 'HR Coordinator', 'Operations Manager', 'Sales Representative'];
  const skillCategories = ['Technical', 'Leadership', 'Communication', 'Project Management', 'Domain Knowledge'];

  const updateFormData = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const submitData: any = {
      ...formData,
      skillsDevelopment: skills,
      certifications: certifications
    };

    if (isEditing) {
      submitData.id = trainee.id;
    }

    onSubmit(submitData);
  };

  const addSkill = () => {
    if (newSkill.skillName && newSkill.category) {
      setSkills(prev => [...prev, { ...newSkill, progressPercentage: 0 }]);
      setNewSkill({
        skillName: '',
        category: '',
        baselineLevel: 1,
        targetLevel: 5,
        currentLevel: 1,
        progressPercentage: 0,
        trainingCompleted: [],
        nextSteps: ''
      });
    }
  };

  const removeSkill = (index: number) => {
    setSkills(prev => prev.filter((_, i) => i !== index));
  };

  const addCertification = () => {
    if (newCertification.name && newCertification.issuingBody) {
      setCertifications(prev => [...prev, newCertification]);
      setNewCertification({
        name: '',
        issuingBody: '',
        dateAchieved: '',
        expiryDate: '',
        isMandatory: false,
        status: 'not_started'
      });
    }
  };

  const removeCertification = (index: number) => {
    setCertifications(prev => prev.filter((_, i) => i !== index));
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="sticky top-0 bg-white border-b px-6 py-4 flex justify-between items-center">
          <h2 className="text-2xl font-bold">
            {isEditing ? 'Edit Trainee Program' : 'Create New Trainee Program'}
          </h2>
          <Button variant="outline" onClick={onCancel}>Cancel</Button>
        </div>

        <form onSubmit={handleSubmit} className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Basic Information */}
            <Card className="md:col-span-2">
              <CardHeader>
                <h3 className="text-lg font-semibold">Basic Information</h3>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="employeeId">Employee ID *</Label>
                    <Input
                      id="employeeId"
                      value={formData.employeeId}
                      onChange={(e) => updateFormData('employeeId', e.target.value)}
                      required
                      disabled={isEditing}
                    />
                  </div>
                  <div>
                    <Label htmlFor="employeeName">Employee Name *</Label>
                    <Input
                      id="employeeName"
                      value={formData.employeeName}
                      onChange={(e) => updateFormData('employeeName', e.target.value)}
                      required
                      disabled={isEditing}
                    />
                  </div>
                  <div>
                    <Label htmlFor="department">Department *</Label>
                    <Select value={formData.department} onValueChange={(value) => updateFormData('department', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select department" />
                      </SelectTrigger>
                      <SelectContent>
                        {departments.map(dept => (
                          <SelectItem key={dept} value={dept}>{dept}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="position">Position *</Label>
                    <Select value={formData.position} onValueChange={(value) => updateFormData('position', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select position" />
                      </SelectTrigger>
                      <SelectContent>
                        {positions.map(pos => (
                          <SelectItem key={pos} value={pos}>{pos}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="manager">Manager *</Label>
                    <Input
                      id="manager"
                      value={formData.manager}
                      onChange={(e) => updateFormData('manager', e.target.value)}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="mentor">Mentor</Label>
                    <Input
                      id="mentor"
                      value={formData.mentor}
                      onChange={(e) => updateFormData('mentor', e.target.value)}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Program Timeline */}
            <Card>
              <CardHeader>
                <h3 className="text-lg font-semibold">Program Timeline</h3>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="startDate">Start Date *</Label>
                  <Input
                    id="startDate"
                    type="date"
                    value={formData.startDate}
                    onChange={(e) => updateFormData('startDate', e.target.value)}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="expectedEndDate">Expected End Date *</Label>
                  <Input
                    id="expectedEndDate"
                    type="date"
                    value={formData.expectedEndDate}
                    onChange={(e) => updateFormData('expectedEndDate', e.target.value)}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="programDurationMonths">Program Duration (months) *</Label>
                  <Input
                    id="programDurationMonths"
                    type="number"
                    min="1"
                    max="36"
                    value={formData.programDurationMonths}
                    onChange={(e) => updateFormData('programDurationMonths', parseInt(e.target.value))}
                    required
                  />
                </div>
                {isEditing && (
                  <div>
                    <Label htmlFor="actualEndDate">Actual End Date</Label>
                    <Input
                      id="actualEndDate"
                      type="date"
                      value={formData.actualEndDate || ''}
                      onChange={(e) => updateFormData('actualEndDate', e.target.value)}
                    />
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Budget & Requirements */}
            <Card>
              <CardHeader>
                <h3 className="text-lg font-semibold">Budget & Requirements</h3>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="trainingHoursRequired">Training Hours Required *</Label>
                  <Input
                    id="trainingHoursRequired"
                    type="number"
                    min="0"
                    value={formData.trainingHoursRequired}
                    onChange={(e) => updateFormData('trainingHoursRequired', parseInt(e.target.value))}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="budgetAllocated">Budget Allocated ($) *</Label>
                  <Input
                    id="budgetAllocated"
                    type="number"
                    min="0"
                    step="100"
                    value={formData.budgetAllocated}
                    onChange={(e) => updateFormData('budgetAllocated', parseInt(e.target.value))}
                    required
                  />
                </div>
                {isEditing && (
                  <>
                    <div>
                      <Label htmlFor="trainingHoursCompleted">Training Hours Completed</Label>
                      <Input
                        id="trainingHoursCompleted"
                        type="number"
                        min="0"
                        value={formData.trainingHoursCompleted}
                        onChange={(e) => updateFormData('trainingHoursCompleted', parseInt(e.target.value))}
                      />
                    </div>
                    <div>
                      <Label htmlFor="budgetSpent">Budget Spent ($)</Label>
                      <Input
                        id="budgetSpent"
                        type="number"
                        min="0"
                        value={formData.budgetSpent}
                        onChange={(e) => updateFormData('budgetSpent', parseInt(e.target.value))}
                      />
                    </div>
                  </>
                )}
              </CardContent>
            </Card>

            {/* Status (Editing Only) */}
            {isEditing && (
              <Card className="md:col-span-2">
                <CardHeader>
                  <h3 className="text-lg font-semibold">Program Status</h3>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="status">Status</Label>
                      <Select value={formData.status} onValueChange={(value) => updateFormData('status', value)}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="not_started">Not Started</SelectItem>
                          <SelectItem value="in_progress">In Progress</SelectItem>
                          <SelectItem value="on_hold">On Hold</SelectItem>
                          <SelectItem value="completed">Completed</SelectItem>
                          <SelectItem value="terminated">Terminated</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="overallProgress">Overall Progress (%)</Label>
                      <Input
                        id="overallProgress"
                        type="number"
                        min="0"
                        max="100"
                        value={Math.round(formData.overallProgress * 100)}
                        onChange={(e) => updateFormData('overallProgress', parseInt(e.target.value) / 100)}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Skills Development */}
            <Card className="md:col-span-2">
              <CardHeader>
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-semibold">Skills Development</h3>
                  <Button type="button" variant="outline" size="sm" onClick={addSkill}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Skill
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {/* Add New Skill Form */}
                <div className="bg-gray-50 p-4 rounded-lg mb-4">
                  <h4 className="font-medium mb-3">Add New Skill</h4>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
                    <div>
                      <Label htmlFor="newSkillName">Skill Name</Label>
                      <Input
                        id="newSkillName"
                        value={newSkill.skillName}
                        onChange={(e) => setNewSkill(prev => ({ ...prev, skillName: e.target.value }))}
                        placeholder="e.g., JavaScript"
                      />
                    </div>
                    <div>
                      <Label htmlFor="newSkillCategory">Category</Label>
                      <Select value={newSkill.category} onValueChange={(value) => setNewSkill(prev => ({ ...prev, category: value }))}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                        <SelectContent>
                          {skillCategories.map(cat => (
                            <SelectItem key={cat} value={cat}>{cat}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="newSkillTargetLevel">Target Level (1-5)</Label>
                      <Input
                        id="newSkillTargetLevel"
                        type="number"
                        min="1"
                        max="5"
                        value={newSkill.targetLevel}
                        onChange={(e) => setNewSkill(prev => ({ ...prev, targetLevel: parseInt(e.target.value) }))}
                      />
                    </div>
                    <div className="flex items-end">
                      <Button type="button" onClick={addSkill} className="w-full">Add</Button>
                    </div>
                  </div>
                </div>

                {/* Skills List */}
                {skills.length > 0 ? (
                  <div className="space-y-3">
                    {skills.map((skill, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                        <div>
                          <div className="font-medium">{skill.skillName}</div>
                          <div className="text-sm text-gray-600">
                            {skill.category} • Target: Level {skill.targetLevel}
                          </div>
                        </div>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeSkill(index)}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 text-center py-4">No skills added yet.</p>
                )}
              </CardContent>
            </Card>

            {/* Certifications */}
            <Card className="md:col-span-2">
              <CardHeader>
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-semibold">Certifications</h3>
                  <Button type="button" variant="outline" size="sm" onClick={addCertification}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Certification
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {/* Add New Certification Form */}
                <div className="bg-gray-50 p-4 rounded-lg mb-4">
                  <h4 className="font-medium mb-3">Add New Certification</h4>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
                    <div>
                      <Label htmlFor="newCertName">Certification Name</Label>
                      <Input
                        id="newCertName"
                        value={newCertification.name}
                        onChange={(e) => setNewCertification(prev => ({ ...prev, name: e.target.value }))}
                        placeholder="e.g., AWS Certified Developer"
                      />
                    </div>
                    <div>
                      <Label htmlFor="newCertIssuingBody">Issuing Body</Label>
                      <Input
                        id="newCertIssuingBody"
                        value={newCertification.issuingBody}
                        onChange={(e) => setNewCertification(prev => ({ ...prev, issuingBody: e.target.value }))}
                        placeholder="e.g., Amazon Web Services"
                      />
                    </div>
                    <div>
                      <Label htmlFor="newCertMandatory">Mandatory</Label>
                      <Select 
                        value={newCertification.isMandatory ? "true" : "false"} 
                        onValueChange={(value) => setNewCertification(prev => ({ ...prev, isMandatory: value === "true" }))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="true">Yes</SelectItem>
                          <SelectItem value="false">No</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="flex items-end">
                      <Button type="button" onClick={addCertification} className="w-full">Add</Button>
                    </div>
                  </div>
                </div>

                {/* Certifications List */}
                {certifications.length > 0 ? (
                  <div className="space-y-3">
                    {certifications.map((cert, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                        <div>
                          <div className="font-medium">{cert.name}</div>
                          <div className="text-sm text-gray-600">
                            {cert.issuingBody} • {cert.isMandatory ? 'Mandatory' : 'Optional'}
                          </div>
                        </div>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeCertification(index)}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 text-center py-4">No certifications added yet.</p>
                )}
              </CardContent>
            </Card>

            {/* Notes */}
            <Card className="md:col-span-2">
              <CardHeader>
                <h3 className="text-lg font-semibold">Notes</h3>
              </CardHeader>
              <CardContent>
                <Textarea
                  value={formData.notes}
                  onChange={(e) => updateFormData('notes', e.target.value)}
                  placeholder="Add any additional notes about this trainee program..."
                  rows={4}
                />
              </CardContent>
            </Card>
          </div>

          <div className="flex justify-end space-x-3 mt-6">
            <Button type="button" variant="outline" onClick={onCancel} disabled={isSubmitting}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Saving...' : (isEditing ? 'Update Program' : 'Create Program')}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};