import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent } from '@/components/ui/card';
import { QuarterlyReview } from '@/lib/api/graduateTrainee';

interface QuarterlyReviewFormProps {
  onSubmit: (review: QuarterlyReview) => void;
  initialData?: QuarterlyReview;
  onCancel?: () => void;
}

export const QuarterlyReviewForm: React.FC<QuarterlyReviewFormProps> = ({
  onSubmit,
  initialData,
  onCancel
}) => {
  const [formData, setFormData] = useState<QuarterlyReview>({
    reviewNumber: initialData?.reviewNumber || 1,
    dueDate: initialData?.dueDate || new Date().toISOString().split('T')[0],
    completedDate: initialData?.completedDate || '',
    reviewer: initialData?.reviewer || '',
    overallRating: initialData?.overallRating || 3,
    feedback: initialData?.feedback || '',
    goalsAchieved: initialData?.goalsAchieved || [],
    goalsPending: initialData?.goalsPending || [],
    skillsImproved: initialData?.skillsImproved || [],
    areasForImprovement: initialData?.areasForImprovement || [],
    nextQuarterGoals: initialData?.nextQuarterGoals || [],
    supportNeeded: initialData?.supportNeeded || [],
    isSatisfactory: initialData?.isSatisfactory || true,
    continuationRecommendation: initialData?.continuationRecommendation || true,
    ...initialData
  });

  const [newItems, setNewItems] = useState({
    goal: '',
    pendingGoal: '',
    skill: '',
    improvement: '',
    nextGoal: '',
    support: ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  const updateFormData = (field: keyof QuarterlyReview, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const addItem = (field: keyof typeof newItems, arrayField: keyof QuarterlyReview) => {
    if (newItems[field].trim()) {
      updateFormData(arrayField, [...(formData[arrayField] as string[]), newItems[field].trim()]);
      setNewItems(prev => ({ ...prev, [field]: '' }));
    }
  };

  const removeItem = (arrayField: keyof QuarterlyReview, index: number) => {
    const newArray = [...(formData[arrayField] as string[])];
    newArray.splice(index, 1);
    updateFormData(arrayField, newArray);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="reviewNumber">Review Number</Label>
          <Input
            id="reviewNumber"
            type="number"
            min="1"
            max="4"
            value={formData.reviewNumber}
            onChange={(e) => updateFormData('reviewNumber', parseInt(e.target.value))}
            required
          />
        </div>
        <div>
          <Label htmlFor="reviewer">Reviewer Name</Label>
          <Input
            id="reviewer"
            value={formData.reviewer}
            onChange={(e) => updateFormData('reviewer', e.target.value)}
            required
          />
        </div>
        <div>
          <Label htmlFor="dueDate">Due Date</Label>
          <Input
            id="dueDate"
            type="date"
            value={formData.dueDate}
            onChange={(e) => updateFormData('dueDate', e.target.value)}
            required
          />
        </div>
        <div>
          <Label htmlFor="completedDate">Completed Date</Label>
          <Input
            id="completedDate"
            type="date"
            value={formData.completedDate || ''}
            onChange={(e) => updateFormData('completedDate', e.target.value)}
          />
        </div>
        <div>
          <Label htmlFor="overallRating">Overall Rating (1-5)</Label>
          <Input
            id="overallRating"
            type="number"
            min="1"
            max="5"
            value={formData.overallRating}
            onChange={(e) => updateFormData('overallRating', parseInt(e.target.value))}
            required
          />
        </div>
      </div>

      <div>
        <Label htmlFor="feedback">Feedback</Label>
        <Textarea
          id="feedback"
          value={formData.feedback}
          onChange={(e) => updateFormData('feedback', e.target.value)}
          rows={4}
          required
        />
      </div>

      {/* Goals Achieved */}
      <Card>
        <CardContent className="pt-4">
          <Label>Goals Achieved</Label>
          <div className="flex gap-2 mt-2">
            <Input
              value={newItems.goal}
              onChange={(e) => setNewItems(prev => ({ ...prev, goal: e.target.value }))}
              placeholder="Add achieved goal"
            />
            <Button type="button" onClick={() => addItem('goal', 'goalsAchieved')} variant="outline">
              Add
            </Button>
          </div>
          <div className="mt-2 space-y-1">
            {formData.goalsAchieved?.map((goal, index) => (
              <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                <span>{goal}</span>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => removeItem('goalsAchieved', index)}
                >
                  Remove
                </Button>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Areas for Improvement */}
      <Card>
        <CardContent className="pt-4">
          <Label>Areas for Improvement</Label>
          <div className="flex gap-2 mt-2">
            <Input
              value={newItems.improvement}
              onChange={(e) => setNewItems(prev => ({ ...prev, improvement: e.target.value }))}
              placeholder="Add area for improvement"
            />
            <Button type="button" onClick={() => addItem('improvement', 'areasForImprovement')} variant="outline">
              Add
            </Button>
          </div>
          <div className="mt-2 space-y-1">
            {formData.areasForImprovement?.map((area, index) => (
              <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                <span>{area}</span>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => removeItem('areasForImprovement', index)}
                >
                  Remove
                </Button>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Next Quarter Goals */}
      <Card>
        <CardContent className="pt-4">
          <Label>Next Quarter Goals</Label>
          <div className="flex gap-2 mt-2">
            <Input
              value={newItems.nextGoal}
              onChange={(e) => setNewItems(prev => ({ ...prev, nextGoal: e.target.value }))}
              placeholder="Add next quarter goal"
            />
            <Button type="button" onClick={() => addItem('nextGoal', 'nextQuarterGoals')} variant="outline">
              Add
            </Button>
          </div>
          <div className="mt-2 space-y-1">
            {formData.nextQuarterGoals?.map((goal, index) => (
              <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                <span>{goal}</span>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => removeItem('nextQuarterGoals', index)}
                >
                  Remove
                </Button>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Support Needed */}
      <Card>
        <CardContent className="pt-4">
          <Label>Support Needed</Label>
          <div className="flex gap-2 mt-2">
            <Input
              value={newItems.support}
              onChange={(e) => setNewItems(prev => ({ ...prev, support: e.target.value }))}
              placeholder="Add support needed"
            />
            <Button type="button" onClick={() => addItem('support', 'supportNeeded')} variant="outline">
              Add
            </Button>
          </div>
          <div className="mt-2 space-y-1">
            {formData.supportNeeded?.map((support, index) => (
              <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                <span>{support}</span>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => removeItem('supportNeeded', index)}
                >
                  Remove
                </Button>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <div className="flex items-center space-x-2">
        <Checkbox
          id="isSatisfactory"
          checked={formData.isSatisfactory}
          onCheckedChange={(checked) => updateFormData('isSatisfactory', checked as boolean)}
        />
        <Label htmlFor="isSatisfactory">Is Satisfactory</Label>
      </div>

      <div className="flex items-center space-x-2">
        <Checkbox
          id="continuationRecommendation"
          checked={formData.continuationRecommendation}
          onCheckedChange={(checked) => updateFormData('continuationRecommendation', checked as boolean)}
        />
        <Label htmlFor="continuationRecommendation">Recommend for Continuation</Label>
      </div>

      <div className="flex justify-end space-x-2">
        {onCancel && (
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
        )}
        <Button type="submit">Save Review</Button>
      </div>
    </form>
  );
};