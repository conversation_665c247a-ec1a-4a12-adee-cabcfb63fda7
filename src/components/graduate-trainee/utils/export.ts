import { GraduateTraineeProgram } from '@/lib/api/graduateTrainee';

export const exportTraineesToCSV = (trainees: GraduateTraineeProgram[], filename?: string): void => {
  const headers = [
    'Employee ID',
    'Employee Name',
    'Department',
    'Position',
    'Manager',
    'Mentor',
    'Status',
    'Overall Progress',
    'Start Date',
    'Expected End Date',
    'Actual End Date',
    'Training Hours Required',
    'Training Hours Completed',
    'Budget Allocated',
    'Budget Spent',
    'Notes'
  ];

  const csvContent = [
    headers.join(','),
    ...trainees.map(trainee => [
      trainee.employeeId,
      `"${trainee.employeeName}"`,
      trainee.department,
      trainee.position,
      `"${trainee.manager}"`,
      `"${trainee.mentor || ''}"`,
      trainee.status,
      trainee.overallProgress,
      new Date(trainee.startDate).toISOString().split('T')[0],
      new Date(trainee.expectedEndDate).toISOString().split('T')[0],
      trainee.actualEndDate ? new Date(trainee.actualEndDate).toISOString().split('T')[0] : '',
      trainee.trainingHoursRequired,
      trainee.trainingHoursCompleted,
      trainee.budgetAllocated,
      trainee.budgetSpent,
      `"${trainee.notes || ''}"`
    ].join(','))
  ].join('\n');

  downloadFile(csvContent, filename || 'graduate-trainees.csv', 'text/csv');
};

export const exportTraineesToJSON = (trainees: GraduateTraineeProgram[], filename?: string): void => {
  const jsonContent = JSON.stringify(trainees, null, 2);
  downloadFile(jsonContent, filename || 'graduate-trainees.json', 'application/json');
};

export const exportSkillsReport = (trainees: GraduateTraineeProgram[], filename?: string): void => {
  const headers = [
    'Employee Name',
    'Department',
    'Skill Name',
    'Category',
    'Current Level',
    'Target Level',
    'Progress %',
    'Next Steps',
    'Training Completed'
  ];

  const skillsData = trainees.flatMap(trainee => 
    (trainee.skillsDevelopment || []).map(skill => [
      `"${trainee.employeeName}"`,
      trainee.department,
      `"${skill.skillName}"`,
      `"${skill.category}"`,
      `"${skill.currentLevel}"`,
      `"${skill.targetLevel}"`,
      skill.progressPercentage,
      `"${skill.nextSteps}"`,
      `"${skill.trainingCompleted.join('; ')}"`
    ])
  );

  const csvContent = [headers.join(','), ...skillsData.map(row => row.join(','))].join('\n');
  downloadFile(csvContent, filename || 'skills-report.csv', 'text/csv');
};

export const exportReviewsReport = (trainees: GraduateTraineeProgram[], filename?: string): void => {
  const headers = [
    'Employee Name',
    'Review Number',
    'Reviewer',
    'Due Date',
    'Completed Date',
    'Overall Rating',
    'Is Satisfactory',
    'Continuation Recommended',
    'Comments'
  ];

  const reviewsData = trainees.flatMap(trainee => 
    (trainee.quarterlyReviews || []).map(review => [
      `"${trainee.employeeName}"`,
      review.reviewNumber,
      `"${review.reviewer}"`,
      new Date(review.dueDate).toISOString().split('T')[0],
      review.completedDate ? new Date(review.completedDate).toISOString().split('T')[0] : '',
      review.overallRating || '',
      review.isSatisfactory ? 'Yes' : 'No',
      review.continuationRecommendation ? 'Yes' : 'No',
      `"${review.feedback || ''}"`
    ])
  );

  const csvContent = [headers.join(','), ...reviewsData.map(row => row.join(','))].join('\n');
  downloadFile(csvContent, filename || 'reviews-report.csv', 'text/csv');
};

const downloadFile = (content: string, filename: string, mimeType: string): void => {
  const blob = new Blob([content], { type: mimeType });
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

export const generateReportSummary = (trainees: GraduateTraineeProgram[]): string => {
  const total = trainees.length;
  const active = trainees.filter(t => t.status === 'in_progress').length;
  const completed = trainees.filter(t => t.status === 'completed').length;
  const avgProgress = trainees.reduce((sum, t) => sum + t.overallProgress, 0) / total;
  const totalBudget = trainees.reduce((sum, t) => sum + t.budgetAllocated, 0);
  const spentBudget = trainees.reduce((sum, t) => sum + t.budgetSpent, 0);

  return `Graduate Trainee Program Summary Report
Generated: ${new Date().toLocaleDateString()}

Total Trainees: ${total}
Active Trainees: ${active}
Completed Trainees: ${completed}
Average Progress: ${Math.round(avgProgress)}%
Total Budget Allocated: $${totalBudget.toLocaleString()}
Total Budget Spent: $${spentBudget.toLocaleString()}
Budget Utilization: ${Math.round((spentBudget / totalBudget) * 100)}%

Department Breakdown:
${Object.entries(
  trainees.reduce((acc, t) => {
    acc[t.department] = (acc[t.department] || 0) + 1;
    return acc;
  }, {} as Record<string, number>)
).map(([dept, count]) => `  ${dept}: ${count}`).join('\n')}

Status Distribution:
${Object.entries(
  trainees.reduce((acc, t) => {
    acc[t.status] = (acc[t.status] || 0) + 1;
    return acc;
  }, {} as Record<string, number>)
).map(([status, count]) => `  ${status}: ${count}`).join('\n')}`;
};