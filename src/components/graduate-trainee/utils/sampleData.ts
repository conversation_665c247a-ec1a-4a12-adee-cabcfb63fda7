import type { GraduateTraineeProgram } from '@/lib/api/graduateTrainee';

export const generateSampleTrainees = (): GraduateTraineeProgram[] => {
  const departments = ['Engineering', 'Marketing', 'Sales', 'Finance', 'HR'];
  const statuses = ['in_progress', 'completed', 'on_hold', 'not_started'];
  const managers = ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'];
  const names = [
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'
  ];

  return names.map((name, index) => {
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - Math.floor(Math.random() * 12));
    
    const endDate = new Date(startDate);
    endDate.setMonth(endDate.getMonth() + 18);

    const progress = Math.floor(Math.random() * 100);
    const budgetAllocated = 50000 + Math.floor(Math.random() * 50000);
    const budgetSpent = Math.floor(budgetAllocated * (progress / 100) * (0.8 + Math.random() * 0.4));
    const trainingHoursRequired = 200 + Math.floor(Math.random() * 300);
    const trainingHoursCompleted = Math.floor(trainingHoursRequired * (progress / 100));

    return {
      id: index + 1,
      employeeId: `EMP${String(index + 1).padStart(3, '0')}`,
      employeeName: name,
      department: departments[index % departments.length],
      position: 'Graduate Trainee',
      manager: managers[index % managers.length],
      mentor: managers[(index + 1) % managers.length],
      startDate: startDate.toISOString().split('T')[0],
      expectedEndDate: endDate.toISOString().split('T')[0],
      programDurationMonths: 18,
      status: statuses[index % statuses.length] as any,
      overallProgress: progress,
      trainingHoursRequired,
      trainingHoursCompleted,
      budgetAllocated,
      budgetSpent,
      quarterlyReviews: [
        {
          quarter: 1,
          scheduledDate: new Date(startDate.getTime() + 3 * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          completed: progress > 25,
          completedDate: progress > 25 ? new Date(startDate.getTime() + 3 * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] : null,
          rating: progress > 25 ? Math.floor(3 + Math.random() * 2) : null,
          feedback: progress > 25 ? 'Good progress shown. Demonstrating strong technical aptitude and eagerness to learn.' : ''
        },
        {
          quarter: 2,
          scheduledDate: new Date(startDate.getTime() + 6 * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          completed: progress > 50,
          completedDate: progress > 50 ? new Date(startDate.getTime() + 6 * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] : null,
          rating: progress > 50 ? Math.floor(3 + Math.random() * 2) : null,
          feedback: progress > 50 ? 'Meeting expectations. Showing improvement in communication and teamwork skills.' : ''
        },
        {
          quarter: 3,
          scheduledDate: new Date(startDate.getTime() + 9 * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          completed: progress > 75,
          completedDate: progress > 75 ? new Date(startDate.getTime() + 9 * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] : null,
          rating: progress > 75 ? Math.floor(4 + Math.random() * 1) : null,
          feedback: progress > 75 ? 'Exceeding expectations. Ready for more complex assignments and responsibilities.' : ''
        },
        {
          quarter: 4,
          scheduledDate: new Date(startDate.getTime() + 12 * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          completed: progress > 95,
          completedDate: progress > 95 ? new Date(startDate.getTime() + 12 * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] : null,
          rating: progress > 95 ? 5 : null,
          feedback: progress > 95 ? 'Outstanding performance. Ready for full-time role transition.' : ''
        }
      ],
      skillsDevelopment: [
        {
          skillName: 'Technical Skills',
          targetLevel: 5,
          currentLevel: Math.min(5, Math.floor(1 + (progress / 100) * 4)),
          assessmentDate: new Date(startDate.getTime() + Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
        },
        {
          skillName: 'Communication',
          targetLevel: 4,
          currentLevel: Math.min(4, Math.floor(2 + (progress / 100) * 2)),
          assessmentDate: new Date(startDate.getTime() + Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
        },
        {
          skillName: 'Problem Solving',
          targetLevel: 5,
          currentLevel: Math.min(5, Math.floor(1 + (progress / 100) * 4)),
          assessmentDate: new Date(startDate.getTime() + Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
        },
        {
          skillName: 'Leadership',
          targetLevel: 3,
          currentLevel: Math.min(3, Math.floor((progress / 100) * 3)),
          assessmentDate: new Date(startDate.getTime() + Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
        },
        {
          skillName: 'Project Management',
          targetLevel: 4,
          currentLevel: Math.min(4, Math.floor(1 + (progress / 100) * 3)),
          assessmentDate: new Date(startDate.getTime() + Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
        }
      ],
      certifications: [
        {
          name: 'Professional Certification',
          provider: 'Industry Provider',
          status: progress > 75 ? 'completed' : progress > 40 ? 'in_progress' : 'not_started',
          targetDate: new Date(startDate.getTime() + 10 * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          completionDate: progress > 75 ? new Date(startDate.getTime() + 8 * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] : null,
          cost: 2500
        },
        {
          name: 'Technical Specialization',
          provider: 'Tech Academy',
          status: progress > 60 ? 'completed' : progress > 30 ? 'in_progress' : 'not_started',
          targetDate: new Date(startDate.getTime() + 8 * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          completionDate: progress > 60 ? new Date(startDate.getTime() + 6 * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] : null,
          cost: 1800
        },
        {
          name: 'Soft Skills Training',
          provider: 'Corporate Training Institute',
          status: progress > 40 ? 'completed' : 'not_started',
          targetDate: new Date(startDate.getTime() + 5 * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          completionDate: progress > 40 ? new Date(startDate.getTime() + 4 * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] : null,
          cost: 1200
        }
      ],
      notes: 'Sample trainee record for demonstration',
      createdAt: startDate.toISOString(),
      updatedAt: new Date().toISOString()
    };
  });
};