import { GraduateTraineeProgram, TraineeStatus } from '@/lib/api/graduateTrainee';
import { TraineeFormData, TraineeValidationError } from '../types';

export const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
};

export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(amount);
};

export const calculateDaysRemaining = (endDate: string): number => {
  const end = new Date(endDate);
  const today = new Date();
  const diffTime = end.getTime() - today.getTime();
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};

export const getStatusColor = (status: TraineeStatus): string => {
  const colors = {
    in_progress: 'text-blue-600 bg-blue-50',
    completed: 'text-green-600 bg-green-50',
    on_hold: 'text-yellow-600 bg-yellow-50',
    terminated: 'text-red-600 bg-red-50',
    not_started: 'text-gray-600 bg-gray-50',
  };
  return colors[status] || 'text-gray-600 bg-gray-50';
};

export const getStatusLabel = (status: TraineeStatus): string => {
  const labels = {
    in_progress: 'In Progress',
    completed: 'Completed',
    on_hold: 'On Hold',
    terminated: 'Terminated',
    not_started: 'Not Started',
  };
  return labels[status] || status;
};

export const calculateOverallProgress = (trainee: GraduateTraineeProgram): number => {
  const weights = {
    trainingHours: 0.3,
    skills: 0.4,
    certifications: 0.3,
  };

  const trainingProgress = Math.min(
    (trainee.trainingHoursCompleted / trainee.trainingHoursRequired) * 100,
    100
  );

  const skillsProgress = trainee.skillsDevelopment?.length > 0
    ? trainee.skillsDevelopment.reduce((sum, skill) => sum + skill.progressPercentage, 0) / trainee.skillsDevelopment.length
    : 0;

  const certificationsProgress = trainee.certifications?.length > 0
    ? (trainee.certifications.filter(c => c.status === 'completed').length / trainee.certifications.length) * 100
    : 0;

  return Math.round(
    (trainingProgress * weights.trainingHours) +
    (skillsProgress * weights.skills) +
    (certificationsProgress * weights.certifications)
  );
};

export const validateTraineeForm = (data: TraineeFormData): TraineeValidationError[] => {
  const errors: TraineeValidationError[] = [];

  if (!data.employeeId.trim()) {
    errors.push({ field: 'employeeId', message: 'Employee ID is required' });
  }

  if (!data.employeeName.trim()) {
    errors.push({ field: 'employeeName', message: 'Employee name is required' });
  }

  if (!data.department.trim()) {
    errors.push({ field: 'department', message: 'Department is required' });
  }

  if (!data.position.trim()) {
    errors.push({ field: 'position', message: 'Position is required' });
  }

  if (!data.manager.trim()) {
    errors.push({ field: 'manager', message: 'Manager is required' });
  }

  if (!data.startDate) {
    errors.push({ field: 'startDate', message: 'Start date is required' });
  }

  if (!data.expectedEndDate) {
    errors.push({ field: 'expectedEndDate', message: 'Expected end date is required' });
  }

  if (data.startDate && data.expectedEndDate) {
    const start = new Date(data.startDate);
    const end = new Date(data.expectedEndDate);
    if (start >= end) {
      errors.push({ field: 'expectedEndDate', message: 'Expected end date must be after start date' });
    }
  }

  if (data.trainingHoursRequired <= 0) {
    errors.push({ field: 'trainingHoursRequired', message: 'Training hours must be greater than 0' });
  }


  return errors;
};

export const searchTrainees = (
  trainees: GraduateTraineeProgram[],
  query: string
): GraduateTraineeProgram[] => {
  if (!query.trim()) return trainees;

  const searchTerms = query.toLowerCase().split(' ');
  
  return trainees.filter(trainee => {
    const searchableText = [
      trainee.employeeId,
      trainee.employeeName,
      trainee.department,
      trainee.position,
      trainee.manager,
      trainee.mentor || '',
    ].join(' ').toLowerCase();

    return searchTerms.every(term => searchableText.includes(term));
  });
};

export const sortTrainees = (
  trainees: GraduateTraineeProgram[],
  key: keyof GraduateTraineeProgram,
  direction: 'asc' | 'desc'
): GraduateTraineeProgram[] => {
  return [...trainees].sort((a, b) => {
    let aValue = a[key] || '';
    let bValue = b[key] || '';

    if (typeof aValue === 'string') {
      aValue = aValue.toLowerCase();
      bValue = String(bValue).toLowerCase();
    }

    if (aValue < bValue) return direction === 'asc' ? -1 : 1;
    if (aValue > bValue) return direction === 'asc' ? 1 : -1;
    return 0;
  });
};

export const generateTraineeId = (): string => {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substring(2, 8);
  return `GT-${timestamp}-${random}`.toUpperCase();
};

export const getOverdueTrainees = (trainees: GraduateTraineeProgram[]): GraduateTraineeProgram[] => {
  const today = new Date();
  return trainees.filter(trainee => {
    const expectedEnd = new Date(trainee.expectedEndDate);
    return trainee.status === 'in_progress' && expectedEnd < today;
  });
};

export const getUpcomingReviews = (trainees: GraduateTraineeProgram[]): Array<{
  trainee: GraduateTraineeProgram;
  review: any;
  daysUntilDue: number;
}> => {
  const today = new Date();
  const upcoming: Array<{
    trainee: GraduateTraineeProgram;
    review: any;
    daysUntilDue: number;
  }> = [];

  trainees.forEach(trainee => {
    trainee.quarterlyReviews?.forEach(review => {
      if (!review.completedDate) {
        const dueDate = new Date(review.dueDate);
        const daysUntilDue = Math.ceil((dueDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
        
        if (daysUntilDue >= 0 && daysUntilDue <= 7) {
          upcoming.push({ trainee, review, daysUntilDue });
        }
      }
    });
  });

  return upcoming.sort((a, b) => a.daysUntilDue - b.daysUntilDue);
};

export const formatDuration = (startDate: string, endDate?: string): string => {
  const start = new Date(startDate);
  const end = endDate ? new Date(endDate) : new Date();
  
  const diffTime = Math.abs(end.getTime() - start.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  const diffMonths = Math.round(diffDays / 30);
  
  if (diffMonths >= 12) {
    const years = Math.floor(diffMonths / 12);
    const months = diffMonths % 12;
    return months > 0 ? `${years}y ${months}m` : `${years}y`;
  }
  
  return `${diffMonths}m`;
};

export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let timeoutId: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
};

export const formatFileSize = (bytes: number): string => {
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  if (bytes === 0) return '0 Bytes';
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
};

export * from './export';
export * from './constants';