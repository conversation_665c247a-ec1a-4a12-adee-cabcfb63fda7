/**
 * ThemeProvider - 2025 Design System
 * Provides theme context and management for the application
 */

import React, { createContext, useContext, useEffect, useState } from 'react';
import { ThemeConfig, ThemeMode, getTheme, applyTheme } from '../design-system/themes/theme.config';

interface ThemeContextValue {
  theme: ThemeConfig;
  mode: ThemeMode;
  setMode: (mode: ThemeMode) => void;
  toggleMode: () => void;
  customizeTheme: (custom: Partial<ThemeConfig['custom']>) => void;
}

const ThemeContext = createContext<ThemeContextValue | undefined>(undefined);

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

interface ThemeProviderProps {
  children: React.ReactNode;
  defaultMode?: ThemeMode;
  storageKey?: string;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({
  children,
  defaultMode = 'dark',
  storageKey = 'gt-theme-mode',
}) => {
  const [mode, setModeState] = useState<ThemeMode>(() => {
    const stored = localStorage.getItem(storageKey);
    return (stored as ThemeMode) || defaultMode;
  });

  const [theme, setTheme] = useState<ThemeConfig>(() => getTheme(mode));

  useEffect(() => {
    const newTheme = getTheme(mode);
    setTheme(newTheme);
    applyTheme(newTheme);
    localStorage.setItem(storageKey, mode);

    // Listen for system theme changes if in auto mode
    if (mode === 'auto') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      const handleChange = () => {
        const updatedTheme = getTheme('auto');
        setTheme(updatedTheme);
        applyTheme(updatedTheme);
      };

      mediaQuery.addEventListener('change', handleChange);
      return () => mediaQuery.removeEventListener('change', handleChange);
    }
  }, [mode, storageKey]);

  const setMode = (newMode: ThemeMode) => {
    setModeState(newMode);
  };

  const toggleMode = () => {
    setModeState((prev) => {
      if (prev === 'light') return 'dark';
      if (prev === 'dark') return 'auto';
      return 'light';
    });
  };

  const customizeTheme = (custom: Partial<ThemeConfig['custom']>) => {
    setTheme((prev) => {
      const newTheme = {
        ...prev,
        custom: {
          ...prev.custom,
          ...custom,
        },
      };
      applyTheme(newTheme);
      return newTheme;
    });
  };

  return (
    <ThemeContext.Provider
      value={{
        theme,
        mode,
        setMode,
        toggleMode,
        customizeTheme,
      }}
    >
      {children}
    </ThemeContext.Provider>
  );
};

export default ThemeProvider;