/**
 * Effects Design Tokens - 2025 Design System
 * Shadows, blurs, transitions, and visual effects
 */

export const effects = {
  // Shadows
  shadow: {
    none: 'none',
    xs: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    sm: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
    '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
    '3xl': '0 35px 60px -15px rgba(0, 0, 0, 0.3)',
    inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
    
    // Glass shadows
    glass: {
      sm: '0 2px 8px rgba(0, 0, 0, 0.04)',
      md: '0 4px 16px rgba(0, 0, 0, 0.08)',
      lg: '0 8px 32px rgba(0, 0, 0, 0.12)',
      xl: '0 12px 48px rgba(0, 0, 0, 0.16)',
    },

    // Colored shadows
    colored: {
      primary: '0 10px 40px -10px rgba(99, 102, 241, 0.35)',
      accent: '0 10px 40px -10px rgba(236, 72, 213, 0.35)',
      success: '0 10px 40px -10px rgba(16, 185, 129, 0.35)',
      warning: '0 10px 40px -10px rgba(245, 158, 11, 0.35)',
      error: '0 10px 40px -10px rgba(239, 68, 68, 0.35)',
    },

    // Neomorphic shadows
    neomorphic: {
      flat: '5px 5px 10px rgba(0, 0, 0, 0.15), -5px -5px 10px rgba(255, 255, 255, 0.7)',
      concave: 'inset 5px 5px 10px rgba(0, 0, 0, 0.15), inset -5px -5px 10px rgba(255, 255, 255, 0.7)',
      convex: '10px 10px 20px rgba(0, 0, 0, 0.15), -10px -10px 20px rgba(255, 255, 255, 0.7)',
    },
  },

  // Blur effects
  blur: {
    none: '0',
    xs: 'blur(2px)',
    sm: 'blur(4px)',
    md: 'blur(8px)',
    lg: 'blur(12px)',
    xl: 'blur(16px)',
    '2xl': 'blur(24px)',
    '3xl': 'blur(40px)',
    glass: 'blur(10px)',
    backdrop: 'blur(20px)',
  },

  // Border radius
  borderRadius: {
    none: '0',
    sm: '0.25rem',
    md: '0.5rem',
    lg: '0.75rem',
    xl: '1rem',
    '2xl': '1.5rem',
    '3xl': '2rem',
    full: '9999px',
    
    // Component specific
    button: '0.75rem',
    card: '1.25rem',
    modal: '1.5rem',
    input: '0.5rem',
  },

  // Transitions
  transition: {
    none: 'none',
    all: 'all 150ms cubic-bezier(0.4, 0, 0.2, 1)',
    
    // Duration
    fast: '100ms',
    base: '200ms',
    slow: '300ms',
    slower: '500ms',
    
    // Timing functions
    ease: {
      in: 'cubic-bezier(0.4, 0, 1, 1)',
      out: 'cubic-bezier(0, 0, 0.2, 1)',
      inOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
      bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
      smooth: 'cubic-bezier(0.25, 0.1, 0.25, 1)',
    },
    
    // Presets
    colors: 'background-color 200ms, border-color 200ms, color 200ms, fill 200ms, stroke 200ms',
    opacity: 'opacity 200ms cubic-bezier(0.4, 0, 0.2, 1)',
    shadow: 'box-shadow 200ms cubic-bezier(0.4, 0, 0.2, 1)',
    transform: 'transform 200ms cubic-bezier(0.4, 0, 0.2, 1)',
  },

  // Animations
  animation: {
    spin: 'spin 1s linear infinite',
    ping: 'ping 1s cubic-bezier(0, 0, 0.2, 1) infinite',
    pulse: 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
    bounce: 'bounce 1s infinite',
    fadeIn: 'fadeIn 200ms ease-out',
    fadeOut: 'fadeOut 200ms ease-in',
    slideUp: 'slideUp 300ms cubic-bezier(0.4, 0, 0.2, 1)',
    slideDown: 'slideDown 300ms cubic-bezier(0.4, 0, 0.2, 1)',
    slideLeft: 'slideLeft 300ms cubic-bezier(0.4, 0, 0.2, 1)',
    slideRight: 'slideRight 300ms cubic-bezier(0.4, 0, 0.2, 1)',
    scaleIn: 'scaleIn 200ms cubic-bezier(0.4, 0, 0.2, 1)',
    scaleOut: 'scaleOut 200ms cubic-bezier(0.4, 0, 0.2, 1)',
    shimmer: 'shimmer 2s linear infinite',
    glow: 'glow 2s ease-in-out infinite',
  },

  // Opacity
  opacity: {
    0: '0',
    5: '0.05',
    10: '0.1',
    20: '0.2',
    25: '0.25',
    30: '0.3',
    40: '0.4',
    50: '0.5',
    60: '0.6',
    70: '0.7',
    75: '0.75',
    80: '0.8',
    90: '0.9',
    95: '0.95',
    100: '1',
  },

  // Z-index layers
  zIndex: {
    hide: -1,
    auto: 'auto',
    base: 0,
    dropdown: 10,
    sticky: 20,
    fixed: 30,
    overlay: 40,
    modal: 50,
    popover: 60,
    toast: 70,
    tooltip: 80,
    max: 9999,
  },
} as const;

export type Effects = typeof effects;
export type EffectsKey = keyof Effects;