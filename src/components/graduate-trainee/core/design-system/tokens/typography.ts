/**
 * Typography Design Tokens - 2025 Design System
 * Modern fluid typography with clamp() for responsive scaling
 */

export const typography = {
  // Font Families
  fontFamily: {
    display: '"SF Pro Display", -apple-system, BlinkMacSystemFont, system-ui, sans-serif',
    body: '"Inter", -apple-system, BlinkMacSystemFont, system-ui, sans-serif',
    mono: '"JetBrains Mono", "SF Mono", Monaco, Consolas, monospace',
  },

  // Font Sizes with Fluid Typography
  fontSize: {
    xs: 'clamp(0.75rem, 2vw, 0.875rem)',
    sm: 'clamp(0.875rem, 2.5vw, 1rem)',
    base: 'clamp(1rem, 3vw, 1.125rem)',
    lg: 'clamp(1.125rem, 3.5vw, 1.25rem)',
    xl: 'clamp(1.25rem, 4vw, 1.5rem)',
    '2xl': 'clamp(1.5rem, 5vw, 1.875rem)',
    '3xl': 'clamp(1.875rem, 6vw, 2.25rem)',
    '4xl': 'clamp(2.25rem, 7vw, 3rem)',
    '5xl': 'clamp(3rem, 8vw, 3.75rem)',
    '6xl': 'clamp(3.75rem, 9vw, 4.5rem)',
  },

  // Font Weights
  fontWeight: {
    thin: '100',
    extralight: '200',
    light: '300',
    regular: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
    extrabold: '800',
    black: '900',
  },

  // Line Heights
  lineHeight: {
    none: '1',
    tight: '1.25',
    snug: '1.375',
    normal: '1.5',
    relaxed: '1.625',
    loose: '2',
  },

  // Letter Spacing
  letterSpacing: {
    tighter: '-0.05em',
    tight: '-0.025em',
    normal: '0',
    wide: '0.025em',
    wider: '0.05em',
    widest: '0.1em',
  },

  // Text Styles (Compound Styles)
  textStyles: {
    // Headings
    h1: {
      fontFamily: '"SF Pro Display", system-ui, sans-serif',
      fontSize: 'clamp(2.25rem, 7vw, 3rem)',
      fontWeight: '700',
      lineHeight: '1.25',
      letterSpacing: '-0.025em',
    },
    h2: {
      fontFamily: '"SF Pro Display", system-ui, sans-serif',
      fontSize: 'clamp(1.875rem, 6vw, 2.25rem)',
      fontWeight: '600',
      lineHeight: '1.375',
      letterSpacing: '-0.025em',
    },
    h3: {
      fontFamily: '"SF Pro Display", system-ui, sans-serif',
      fontSize: 'clamp(1.5rem, 5vw, 1.875rem)',
      fontWeight: '600',
      lineHeight: '1.375',
      letterSpacing: '-0.025em',
    },
    h4: {
      fontFamily: '"SF Pro Display", system-ui, sans-serif',
      fontSize: 'clamp(1.25rem, 4vw, 1.5rem)',
      fontWeight: '600',
      lineHeight: '1.5',
      letterSpacing: '0',
    },
    h5: {
      fontFamily: '"SF Pro Display", system-ui, sans-serif',
      fontSize: 'clamp(1.125rem, 3.5vw, 1.25rem)',
      fontWeight: '600',
      lineHeight: '1.5',
      letterSpacing: '0',
    },
    h6: {
      fontFamily: '"SF Pro Display", system-ui, sans-serif',
      fontSize: 'clamp(1rem, 3vw, 1.125rem)',
      fontWeight: '600',
      lineHeight: '1.5',
      letterSpacing: '0',
    },

    // Body Text
    bodyLarge: {
      fontFamily: '"Inter", system-ui, sans-serif',
      fontSize: 'clamp(1.125rem, 3.5vw, 1.25rem)',
      fontWeight: '400',
      lineHeight: '1.625',
      letterSpacing: '0',
    },
    body: {
      fontFamily: '"Inter", system-ui, sans-serif',
      fontSize: 'clamp(1rem, 3vw, 1.125rem)',
      fontWeight: '400',
      lineHeight: '1.625',
      letterSpacing: '0',
    },
    bodySmall: {
      fontFamily: '"Inter", system-ui, sans-serif',
      fontSize: 'clamp(0.875rem, 2.5vw, 1rem)',
      fontWeight: '400',
      lineHeight: '1.5',
      letterSpacing: '0',
    },

    // Special Text
    label: {
      fontFamily: '"Inter", system-ui, sans-serif',
      fontSize: 'clamp(0.875rem, 2.5vw, 1rem)',
      fontWeight: '500',
      lineHeight: '1.5',
      letterSpacing: '0.025em',
      textTransform: 'uppercase' as const,
    },
    caption: {
      fontFamily: '"Inter", system-ui, sans-serif',
      fontSize: 'clamp(0.75rem, 2vw, 0.875rem)',
      fontWeight: '400',
      lineHeight: '1.5',
      letterSpacing: '0.025em',
    },
    overline: {
      fontFamily: '"Inter", system-ui, sans-serif',
      fontSize: 'clamp(0.75rem, 2vw, 0.875rem)',
      fontWeight: '600',
      lineHeight: '1.5',
      letterSpacing: '0.1em',
      textTransform: 'uppercase' as const,
    },
    code: {
      fontFamily: '"JetBrains Mono", monospace',
      fontSize: 'clamp(0.875rem, 2.5vw, 1rem)',
      fontWeight: '400',
      lineHeight: '1.5',
      letterSpacing: '0',
    },
  },
} as const;

export type Typography = typeof typography;
export type TypographyKey = keyof Typography;