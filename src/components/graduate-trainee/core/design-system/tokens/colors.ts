/**
 * Color Design Tokens - 2025 Design System
 * Modern color palette with glassmorphism support
 */

export const colors = {
  // Primary Palette
  primary: {
    50: '#EEF2FF',
    100: '#E0E7FF',
    200: '#C7D2FE',
    300: '#A5B4FC',
    400: '#818CF8',
    500: '#6366F1',
    600: '#4F46E5',
    700: '#4338CA',
    800: '#3730A3',
    900: '#312E81',
    950: '#1E1B4B',
    gradient: 'linear-gradient(135deg, #667EEA 0%, #764BA2 100%)',
  },

  // Accent Palette
  accent: {
    50: '#FFF1FE',
    100: '#FFE4FC',
    200: '#FCC8F8',
    300: '#FAA2F2',
    400: '#F670E7',
    500: '#EC48D5',
    600: '#D428B7',
    700: '#B31D94',
    800: '#941979',
    900: '#771862',
    gradient: 'linear-gradient(135deg, #F093FB 0%, #F5576C 100%)',
  },

  // Semantic Colors
  semantic: {
    success: {
      light: '#86EFAC',
      DEFAULT: '#10B981',
      dark: '#059669',
    },
    warning: {
      light: '#FDE047',
      DEFAULT: '#F59E0B',
      dark: '#D97706',
    },
    error: {
      light: '#FCA5A5',
      DEFAULT: '#EF4444',
      dark: '#DC2626',
    },
    info: {
      light: '#93C5FD',
      DEFAULT: '#3B82F6',
      dark: '#2563EB',
    },
  },

  // Neutral Scale
  gray: {
    50: '#FAFAFA',
    100: '#F5F5F5',
    200: '#E5E5E5',
    300: '#D4D4D4',
    400: '#A3A3A3',
    500: '#737373',
    600: '#525252',
    700: '#404040',
    800: '#262626',
    900: '#171717',
    950: '#0A0A0A',
  },

  // Glass Effect Colors
  glass: {
    white: {
      5: 'rgba(255, 255, 255, 0.05)',
      10: 'rgba(255, 255, 255, 0.1)',
      20: 'rgba(255, 255, 255, 0.2)',
      30: 'rgba(255, 255, 255, 0.3)',
      40: 'rgba(255, 255, 255, 0.4)',
    },
    black: {
      5: 'rgba(0, 0, 0, 0.05)',
      10: 'rgba(0, 0, 0, 0.1)',
      20: 'rgba(0, 0, 0, 0.2)',
      30: 'rgba(0, 0, 0, 0.3)',
      40: 'rgba(0, 0, 0, 0.4)',
    },
    border: 'rgba(255, 255, 255, 0.18)',
    borderDark: 'rgba(0, 0, 0, 0.18)',
  },

  // Background Gradients
  gradients: {
    primary: 'linear-gradient(135deg, #667EEA 0%, #764BA2 100%)',
    accent: 'linear-gradient(135deg, #F093FB 0%, #F5576C 100%)',
    success: 'linear-gradient(135deg, #56F39A 0%, #59D5E0 100%)',
    warning: 'linear-gradient(135deg, #FAD961 0%, #F76B1C 100%)',
    error: 'linear-gradient(135deg, #FF5E5B 0%, #FF2E63 100%)',
    dark: 'linear-gradient(135deg, #232526 0%, #414345 100%)',
    light: 'linear-gradient(135deg, #F5F7FA 0%, #C3CFE2 100%)',
    aurora: 'linear-gradient(135deg, #00F260 0%, #0575E6 100%)',
    sunset: 'linear-gradient(135deg, #FA709A 0%, #FEE140 100%)',
    ocean: 'linear-gradient(135deg, #330867 0%, #30CFD0 100%)',
  },

  // Special Effects
  glow: {
    primary: '0 0 40px rgba(99, 102, 241, 0.5)',
    accent: '0 0 40px rgba(236, 72, 213, 0.5)',
    success: '0 0 40px rgba(16, 185, 129, 0.5)',
    warning: '0 0 40px rgba(245, 158, 11, 0.5)',
    error: '0 0 40px rgba(239, 68, 68, 0.5)',
  },
} as const;

export type Colors = typeof colors;
export type ColorKey = keyof Colors;