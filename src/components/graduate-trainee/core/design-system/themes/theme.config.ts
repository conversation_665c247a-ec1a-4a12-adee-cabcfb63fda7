/**
 * Theme Configuration - 2025 Design System
 * Central theme configuration for light, dark, and auto modes
 */

import { colors, typography, spacing, effects } from '../tokens';

export interface ThemeConfig {
  mode: 'light' | 'dark' | 'auto';
  colors: typeof colors;
  typography: typeof typography;
  spacing: typeof spacing;
  effects: typeof effects;
  custom: {
    primaryColor?: string;
    accentColor?: string;
    fontFamily?: string;
  };
}

export const lightTheme: ThemeConfig = {
  mode: 'light',
  colors: {
    ...colors,
    // Override specific colors for light mode
    glass: {
      ...colors.glass,
      white: {
        5: 'rgba(0, 0, 0, 0.02)',
        10: 'rgba(0, 0, 0, 0.05)',
        20: 'rgba(0, 0, 0, 0.1)',
        30: 'rgba(0, 0, 0, 0.15)',
        40: 'rgba(0, 0, 0, 0.2)',
      },
    },
  },
  typography,
  spacing,
  effects: {
    ...effects,
    // Lighter shadows for light mode
    shadow: {
      ...effects.shadow,
      glass: {
        sm: '0 2px 8px rgba(0, 0, 0, 0.08)',
        md: '0 4px 16px rgba(0, 0, 0, 0.12)',
        lg: '0 8px 32px rgba(0, 0, 0, 0.16)',
        xl: '0 12px 48px rgba(0, 0, 0, 0.20)',
      },
    },
  },
  custom: {},
};

export const darkTheme: ThemeConfig = {
  mode: 'dark',
  colors,
  typography,
  spacing,
  effects,
  custom: {},
};

export const autoTheme: ThemeConfig = {
  mode: 'auto',
  colors,
  typography,
  spacing,
  effects,
  custom: {},
};

export const themes = {
  light: lightTheme,
  dark: darkTheme,
  auto: autoTheme,
} as const;

export type ThemeMode = keyof typeof themes;

// Theme utilities
export const getTheme = (mode: ThemeMode = 'dark'): ThemeConfig => {
  if (mode === 'auto') {
    const isDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    return isDark ? darkTheme : lightTheme;
  }
  return themes[mode];
};

export const applyTheme = (theme: ThemeConfig) => {
  const root = document.documentElement;
  
  // Apply CSS variables
  Object.entries(theme.colors.primary).forEach(([key, value]) => {
    if (typeof value === 'string') {
      root.style.setProperty(`--color-primary-${key}`, value);
    }
  });

  Object.entries(theme.colors.semantic).forEach(([key, value]) => {
    if (typeof value === 'object') {
      Object.entries(value).forEach(([subKey, subValue]) => {
        root.style.setProperty(`--color-${key}-${subKey}`, subValue);
      });
    }
  });

  // Apply theme mode class
  root.classList.remove('light', 'dark');
  root.classList.add(theme.mode === 'auto' ? (getTheme('auto').mode as string) : theme.mode);
};

export default themes;