/**
 * GlassInput Component - 2025 Design System
 * Modern glassmorphism input with floating label and animations
 */

import React, { forwardRef, useState } from 'react';
import { cn } from '@/lib/utils';
import { motion, AnimatePresence } from 'framer-motion';
import { AlertCircle, Check, X } from 'lucide-react';

export interface GlassInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  success?: boolean;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  floatingLabel?: boolean;
  hint?: string;
}

export const GlassInput = forwardRef<HTMLInputElement, GlassInputProps>(
  (
    {
      label,
      error,
      success,
      icon,
      iconPosition = 'left',
      floatingLabel = true,
      hint,
      className,
      onFocus,
      onBlur,
      ...props
    },
    ref
  ) => {
    const [focused, setFocused] = useState(false);
    const [hasValue, setHasValue] = useState(!!props.value || !!props.defaultValue);

    const handleFocus = (e: React.FocusEvent<HTMLInputElement>) => {
      setFocused(true);
      onFocus?.(e);
    };

    const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
      setFocused(false);
      setHasValue(!!e.target.value);
      onBlur?.(e);
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setHasValue(!!e.target.value);
      props.onChange?.(e);
    };

    const baseStyles = cn(
      'relative w-full px-4 py-3 rounded-xl',
      'bg-white/10 backdrop-blur-md',
      'border border-white/20',
      'text-white placeholder-white/40',
      'transition-all duration-300',
      'focus:outline-none focus:ring-2 focus:ring-primary-500/50',
      'focus:border-primary-400/50 focus:bg-white/15',
      icon && iconPosition === 'left' && 'pl-12',
      icon && iconPosition === 'right' && 'pr-12',
      error && 'border-red-500/50 focus:ring-red-500/50',
      success && 'border-green-500/50 focus:ring-green-500/50',
      className
    );

    const labelStyles = cn(
      'absolute left-4 transition-all duration-300 pointer-events-none',
      'text-white/60',
      floatingLabel && (focused || hasValue)
        ? 'top-0 -translate-y-1/2 text-xs bg-gray-900 px-2'
        : 'top-1/2 -translate-y-1/2',
      icon && iconPosition === 'left' && 'left-12'
    );

    return (
      <div className="relative">
        <motion.div
          className="relative"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          {icon && (
            <span
              className={cn(
                'absolute top-1/2 -translate-y-1/2 text-white/60',
                iconPosition === 'left' ? 'left-4' : 'right-4'
              )}
            >
              {icon}
            </span>
          )}

          <input
            ref={ref}
            className={baseStyles}
            onFocus={handleFocus}
            onBlur={handleBlur}
            onChange={handleChange}
            {...props}
          />

          {floatingLabel && label && (
            <label className={labelStyles}>{label}</label>
          )}

          <AnimatePresence>
            {error && (
              <motion.span
                className="absolute right-4 top-1/2 -translate-y-1/2 text-red-400"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                transition={{ duration: 0.2 }}
              >
                <AlertCircle className="w-5 h-5" />
              </motion.span>
            )}

            {success && !error && (
              <motion.span
                className="absolute right-4 top-1/2 -translate-y-1/2 text-green-400"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                transition={{ duration: 0.2 }}
              >
                <Check className="w-5 h-5" />
              </motion.span>
            )}
          </AnimatePresence>

          {focused && (
            <motion.div
              className="absolute inset-0 rounded-xl pointer-events-none"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
            >
              <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-primary-500/10 to-accent-500/10" />
            </motion.div>
          )}
        </motion.div>

        <AnimatePresence>
          {error && (
            <motion.p
              className="mt-2 text-sm text-red-400"
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.2 }}
            >
              {error}
            </motion.p>
          )}

          {hint && !error && (
            <motion.p
              className="mt-2 text-sm text-white/40"
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.2 }}
            >
              {hint}
            </motion.p>
          )}
        </AnimatePresence>
      </div>
    );
  }
);

GlassInput.displayName = 'GlassInput';