/**
 * GlassCard Component - 2025 Design System
 * Modern glassmorphism card with depth and light effects
 */

import React, { forwardRef, memo } from 'react';
import { cn } from '@/lib/utils';
import { motion, HTMLMotionProps } from 'framer-motion';

export interface GlassCardProps extends HTMLMotionProps<'div'> {
  variant?: 'flat' | 'elevated' | 'inset';
  blur?: 'sm' | 'md' | 'lg' | 'xl';
  intensity?: 'subtle' | 'medium' | 'strong';
  glow?: boolean;
  gradient?: boolean;
  interactive?: boolean;
  children: React.ReactNode;
}

export const GlassCard = memo(forwardRef<HTMLDivElement, GlassCardProps>(
  (
    {
      variant = 'elevated',
      blur = 'md',
      intensity = 'medium',
      glow = false,
      gradient = false,
      interactive = true,
      className,
      children,
      ...props
    },
    ref
  ) => {
    const blurMap = {
      sm: 'backdrop-blur-sm',
      md: 'backdrop-blur',
      lg: 'backdrop-blur-md',
      xl: 'backdrop-blur-lg',
    };

    const intensityMap = {
      subtle: 'bg-white/5 border-white/10',
      medium: 'bg-white/10 border-white/20',
      strong: 'bg-white/20 border-white/30',
    };

    const variantStyles = {
      flat: 'shadow-glass-md',
      elevated: 'shadow-glass-lg hover:shadow-glass-xl',
      inset: 'shadow-inner border-2',
    };

    const baseStyles = cn(
      'relative overflow-hidden rounded-2xl border transition-all duration-300',
      blurMap[blur],
      intensityMap[intensity],
      variantStyles[variant],
      interactive && 'cursor-pointer hover:scale-[1.01]',
      glow && 'before:absolute before:inset-0 before:rounded-2xl before:bg-gradient-to-r before:from-primary-500/10 before:to-accent-500/10 before:opacity-0 hover:before:opacity-100 before:transition-opacity',
      className
    );

    const gradientOverlay = gradient && (
      <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-white/5 pointer-events-none" />
    );

    const lightReflection = (
      <div className="absolute -top-24 -right-24 w-48 h-48 bg-white/10 rounded-full blur-3xl pointer-events-none" />
    );

    return (
      <motion.div
        ref={ref}
        className={baseStyles}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3 }}
        whileHover={interactive ? { y: -2 } : undefined}
        {...props}
      >
        {lightReflection}
        {gradientOverlay}
        <div className="relative z-10">{children}</div>
      </motion.div>
    );
  }
));

GlassCard.displayName = 'GlassCard';