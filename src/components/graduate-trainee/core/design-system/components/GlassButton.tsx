/**
 * GlassButton Component - 2025 Design System
 * Modern glassmorphism button with micro-interactions
 */

import React, { forwardRef, memo } from 'react';
import { cn } from '@/lib/utils';
import { motion, HTMLMotionProps } from 'framer-motion';
import { Loader2 } from 'lucide-react';

export interface GlassButtonProps extends Omit<HTMLMotionProps<'button'>, 'size'> {
  variant?: 'primary' | 'secondary' | 'accent' | 'ghost' | 'danger';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  glow?: boolean;
  loading?: boolean;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  fullWidth?: boolean;
}

export const GlassButton = memo(forwardRef<HTMLButtonElement, GlassButtonProps>(
  (
    {
      variant = 'primary',
      size = 'md',
      glow = true,
      loading = false,
      icon,
      iconPosition = 'left',
      fullWidth = false,
      className,
      children,
      disabled,
      ...props
    },
    ref
  ) => {
    const variantStyles = {
      primary: cn(
        'bg-gradient-to-r from-primary-500/20 to-primary-600/20',
        'border-primary-500/30 text-primary-100',
        'hover:from-primary-500/30 hover:to-primary-600/30',
        'hover:border-primary-400/40',
        glow && 'hover:shadow-primary-glow'
      ),
      secondary: cn(
        'bg-white/10 border-white/20 text-white',
        'hover:bg-white/20 hover:border-white/30',
        glow && 'hover:shadow-white-glow'
      ),
      accent: cn(
        'bg-gradient-to-r from-accent-500/20 to-accent-600/20',
        'border-accent-500/30 text-accent-100',
        'hover:from-accent-500/30 hover:to-accent-600/30',
        'hover:border-accent-400/40',
        glow && 'hover:shadow-accent-glow'
      ),
      ghost: cn(
        'bg-transparent border-transparent text-gray-300',
        'hover:bg-white/5 hover:border-white/10'
      ),
      danger: cn(
        'bg-red-500/20 border-red-500/30 text-red-100',
        'hover:bg-red-500/30 hover:border-red-400/40',
        glow && 'hover:shadow-red-glow'
      ),
    };

    const sizeStyles = {
      xs: 'px-3 py-1.5 text-xs gap-1.5',
      sm: 'px-4 py-2 text-sm gap-2',
      md: 'px-5 py-2.5 text-base gap-2.5',
      lg: 'px-6 py-3 text-lg gap-3',
      xl: 'px-8 py-4 text-xl gap-4',
    };

    const baseStyles = cn(
      'relative inline-flex items-center justify-center',
      'font-medium rounded-xl border backdrop-blur-md',
      'transition-all duration-300 transform-gpu',
      'active:scale-95',
      fullWidth && 'w-full',
      (disabled || loading) && 'opacity-50 cursor-not-allowed',
      variantStyles[variant],
      sizeStyles[size],
      className
    );

    const renderIcon = () => {
      if (loading) {
        return <Loader2 className="animate-spin" />;
      }
      return icon;
    };

    return (
      <motion.button
        ref={ref}
        className={baseStyles}
        disabled={disabled || loading}
        whileHover={!disabled && !loading ? { scale: 1.02 } : undefined}
        whileTap={!disabled && !loading ? { scale: 0.98 } : undefined}
        {...props}
      >
        <span className="absolute inset-0 rounded-xl bg-gradient-to-r from-white/0 via-white/5 to-white/0 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
        
        {iconPosition === 'left' && renderIcon()}
        {children && <span className="relative z-10">{children}</span>}
        {iconPosition === 'right' && renderIcon()}

        {glow && (
          <span className="absolute inset-0 rounded-xl opacity-0 hover:opacity-100 transition-opacity duration-300 pointer-events-none">
            <span className="absolute inset-0 rounded-xl animate-pulse bg-gradient-to-r from-transparent via-white/10 to-transparent" />
          </span>
        )}
      </motion.button>
    );
  }
));

GlassButton.displayName = 'GlassButton';