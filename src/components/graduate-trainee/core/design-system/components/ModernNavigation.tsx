/**
 * ModernNavigation Component - 2025 Design System
 * Glass-morphism navigation with smooth animations
 */

import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import { motion, AnimatePresence } from 'framer-motion';
import {
  LayoutDashboard,
  Users,
  FileText,
  Settings,
  ChevronRight,
  Search,
  Bell,
  Moon,
  Sun,
  Monitor,
} from 'lucide-react';
import { GlassButton } from './GlassButton';
import { GlassInput } from './GlassInput';
import { useTheme } from '../../providers/ThemeProvider';

interface NavItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  badge?: number;
  active?: boolean;
}

interface ModernNavigationProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  className?: string;
}

export const ModernNavigation: React.FC<ModernNavigationProps> = ({
  activeTab,
  onTabChange,
  className,
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [showNotifications, setShowNotifications] = useState(false);
  const { mode, toggleMode } = useTheme();

  const navItems: NavItem[] = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: <LayoutDashboard className="w-5 h-5" />,
      badge: 0,
    },
    {
      id: 'trainees',
      label: 'Trainees',
      icon: <Users className="w-5 h-5" />,
      badge: 3,
    },
    {
      id: 'reports',
      label: 'Reports',
      icon: <FileText className="w-5 h-5" />,
      badge: 0,
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: <Settings className="w-5 h-5" />,
      badge: 0,
    },
  ];

  const ThemeIcon = () => {
    if (mode === 'light') return <Sun className="w-5 h-5" />;
    if (mode === 'dark') return <Moon className="w-5 h-5" />;
    return <Monitor className="w-5 h-5" />;
  };

  return (
    <motion.nav
      className={cn(
        'relative w-full px-6 py-4',
        'bg-gradient-to-r from-gray-900/95 via-gray-800/95 to-gray-900/95',
        'backdrop-blur-xl border-b border-white/10',
        'shadow-glass-lg',
        className
      )}
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.5, ease: 'easeOut' }}
    >
      <div className="flex items-center justify-between">
        {/* Logo and Brand */}
        <motion.div
          className="flex items-center space-x-4"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.1 }}
        >
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-primary rounded-xl blur-lg opacity-50" />
            <div className="relative bg-gradient-primary rounded-xl p-2">
              <Users className="w-8 h-8 text-white" />
            </div>
          </div>
          <div>
            <h1 className="text-xl font-bold text-white">Graduate Trainees</h1>
            <p className="text-xs text-white/60">Talent Development Platform</p>
          </div>
        </motion.div>

        {/* Navigation Items */}
        <motion.div
          className="flex items-center space-x-2"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2 }}
        >
          {navItems.map((item, index) => {
            const isActive = activeTab === item.id;
            return (
              <motion.button
                key={item.id}
                onClick={() => onTabChange(item.id)}
                className={cn(
                  'relative px-4 py-2.5 rounded-xl',
                  'flex items-center space-x-2',
                  'transition-all duration-300',
                  'hover:bg-white/10',
                  isActive && 'bg-white/15'
                )}
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 * index }}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <span
                  className={cn(
                    'transition-colors duration-300',
                    isActive ? 'text-primary-400' : 'text-white/70'
                  )}
                >
                  {item.icon}
                </span>
                <span
                  className={cn(
                    'font-medium transition-colors duration-300',
                    isActive ? 'text-white' : 'text-white/70'
                  )}
                >
                  {item.label}
                </span>
                {item.badge > 0 && (
                  <motion.span
                    className="absolute -top-1 -right-1 w-5 h-5 bg-accent-500 rounded-full flex items-center justify-center text-xs font-bold text-white"
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ type: 'spring', stiffness: 500 }}
                  >
                    {item.badge}
                  </motion.span>
                )}
                {isActive && (
                  <motion.div
                    className="absolute inset-0 rounded-xl border border-primary-400/30 pointer-events-none"
                    layoutId="activeTab"
                    transition={{ type: 'spring', stiffness: 300, damping: 30 }}
                  />
                )}
              </motion.button>
            );
          })}
        </motion.div>

        {/* Right Section: Search, Notifications, Theme */}
        <motion.div
          className="flex items-center space-x-4"
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.3 }}
        >
          {/* Search */}
          <div className="relative">
            <GlassInput
              type="text"
              placeholder="Search..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              icon={<Search className="w-4 h-4" />}
              className="w-64 py-2 text-sm"
              floatingLabel={false}
            />
          </div>

          {/* Notifications */}
          <div className="relative">
            <GlassButton
              variant="ghost"
              size="sm"
              icon={<Bell className="w-5 h-5" />}
              onClick={() => setShowNotifications(!showNotifications)}
              className="relative"
            />
            <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full animate-pulse" />
            
            <AnimatePresence>
              {showNotifications && (
                <motion.div
                  className="absolute right-0 top-12 w-80 p-4 bg-gray-900/95 backdrop-blur-xl rounded-xl border border-white/10 shadow-glass-xl"
                  initial={{ opacity: 0, y: -10, scale: 0.95 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  exit={{ opacity: 0, y: -10, scale: 0.95 }}
                  transition={{ duration: 0.2 }}
                >
                  <h3 className="text-sm font-semibold text-white mb-3">Notifications</h3>
                  <div className="space-y-2">
                    <div className="p-3 bg-white/5 rounded-lg">
                      <p className="text-sm text-white/80">3 new trainees added</p>
                      <p className="text-xs text-white/50 mt-1">2 hours ago</p>
                    </div>
                    <div className="p-3 bg-white/5 rounded-lg">
                      <p className="text-sm text-white/80">Quarterly review completed</p>
                      <p className="text-xs text-white/50 mt-1">5 hours ago</p>
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Theme Toggle */}
          <GlassButton
            variant="ghost"
            size="sm"
            icon={<ThemeIcon />}
            onClick={toggleMode}
          />
        </motion.div>
      </div>
    </motion.nav>
  );
};