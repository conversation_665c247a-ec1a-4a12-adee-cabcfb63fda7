import type { GraduateTraineeProgram, QuarterlyReview, SkillDevelopment, Certification } from '@/lib/api/graduateTrainee';

// MWS-specific departments and teams
const MWS_DEPARTMENTS = [
  'Cloud Infrastructure',
  'DevOps Engineering',
  'Security Operations',
  'Data Analytics',
  'AI/ML Engineering',
  'Platform Engineering',
  'Site Reliability',
  'Solutions Architecture',
  'Technical Support',
  'Product Development'
];

const MWS_POSITIONS = [
  'Cloud Engineer',
  'DevOps Engineer',
  'Security Analyst',
  'Data Engineer',
  'ML Engineer',
  'Platform Developer',
  'SRE Engineer',
  'Solutions Architect',
  'Technical Consultant',
  'Product Engineer'
];

const MWS_MANAGERS = [
  { name: '<PERSON>', department: 'Cloud Infrastructure' },
  { name: '<PERSON>', department: 'DevOps Engineering' },
  { name: '<PERSON>', department: 'Security Operations' },
  { name: '<PERSON>', department: 'Data Analytics' },
  { name: '<PERSON>', department: 'AI/ML Engineering' },
  { name: '<PERSON>', department: 'Platform Engineering' },
  { name: '<PERSON>', department: 'Site Reliability' },
  { name: '<PERSON>', department: 'Solutions Architecture' },
  { name: '<PERSON>', department: 'Technical Support' },
  { name: '<PERSON>', department: 'Product Development' }
];

const MWS_MENTORS = [
  '<PERSON>',
  'Benjamin Hayes',
  'Charlotte Murphy',
  '<PERSON> O\'Brien',
  '<PERSON> Watson',
  'Frederick Zhang',
  'Grace Kim',
  'Henry Patel',
  'Isabella Nguyen',
  'Jackson Smith'
];

// MWS training programs and certifications
const MWS_CERTIFICATIONS = [
  { name: 'AWS Solutions Architect Associate', issuer: 'Amazon Web Services', mandatory: true },
  { name: 'Azure Fundamentals', issuer: 'Microsoft', mandatory: true },
  { name: 'Google Cloud Professional', issuer: 'Google Cloud', mandatory: false },
  { name: 'Certified Kubernetes Administrator', issuer: 'CNCF', mandatory: false },
  { name: 'CompTIA Security+', issuer: 'CompTIA', mandatory: true },
  { name: 'Certified Scrum Master', issuer: 'Scrum Alliance', mandatory: false },
  { name: 'ITIL Foundation', issuer: 'AXELOS', mandatory: true },
  { name: 'Terraform Associate', issuer: 'HashiCorp', mandatory: false },
  { name: 'Docker Certified Associate', issuer: 'Docker', mandatory: false },
  { name: 'Certified Ethical Hacker', issuer: 'EC-Council', mandatory: false }
];

const MWS_SKILLS = [
  { name: 'Cloud Architecture', category: 'Technical' },
  { name: 'Infrastructure as Code', category: 'Technical' },
  { name: 'CI/CD Pipelines', category: 'Technical' },
  { name: 'Container Orchestration', category: 'Technical' },
  { name: 'Security Best Practices', category: 'Technical' },
  { name: 'System Design', category: 'Technical' },
  { name: 'Monitoring & Observability', category: 'Technical' },
  { name: 'Database Management', category: 'Technical' },
  { name: 'API Development', category: 'Technical' },
  { name: 'Microservices', category: 'Technical' },
  { name: 'Project Management', category: 'Soft Skills' },
  { name: 'Client Communication', category: 'Soft Skills' },
  { name: 'Technical Documentation', category: 'Soft Skills' },
  { name: 'Problem Solving', category: 'Core' },
  { name: 'Team Collaboration', category: 'Soft Skills' },
  { name: 'Presentation Skills', category: 'Soft Skills' },
  { name: 'Agile Methodologies', category: 'Process' },
  { name: 'Risk Management', category: 'Process' },
  { name: 'Change Management', category: 'Process' },
  { name: 'Innovation & Creativity', category: 'Core' }
];

// Generate comprehensive MWS trainee data
export const generateMWSTrainees = (count: number = 50): GraduateTraineeProgram[] => {
  const trainees: GraduateTraineeProgram[] = [];
  const currentDate = new Date();

  for (let i = 1; i <= count; i++) {
    // Generate start date within last 24 months
    const monthsAgo = Math.floor(Math.random() * 24);
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - monthsAgo);
    
    // MWS program is 24 months
    const programDuration = 24;
    const expectedEndDate = new Date(startDate);
    expectedEndDate.setMonth(expectedEndDate.getMonth() + programDuration);

    // Calculate progress based on time elapsed
    const elapsedMonths = Math.min(monthsAgo, programDuration);
    const baseProgress = Math.floor((elapsedMonths / programDuration) * 100);
    
    // Add some variation to progress
    const progressVariation = Math.floor(Math.random() * 10) - 5;
    const overallProgress = Math.max(0, Math.min(100, baseProgress + progressVariation));

    // Determine status based on progress and dates
    let status: GraduateTraineeProgram['status'];
    if (overallProgress >= 100) {
      status = 'completed';
    } else if (overallProgress === 0) {
      status = 'not_started';
    } else if (Math.random() < 0.1) {
      status = 'on_hold';
    } else if (Math.random() < 0.05) {
      status = 'terminated';
    } else {
      status = 'in_progress';
    }

    // Generate quarterly reviews
    const quarterlyReviews = generateQuarterlyReviews(startDate, overallProgress, programDuration);
    
    // Generate skills development
    const skillsDevelopment = generateSkillsDevelopment(overallProgress);
    
    // Generate certifications
    const certifications = generateCertifications(overallProgress);

    // Training hours (MWS requires more training)
    const trainingHoursRequired = 320;
    const trainingHoursCompleted = Math.floor((overallProgress / 100) * trainingHoursRequired * (0.9 + Math.random() * 0.2));

    const trainee: GraduateTraineeProgram = {
      id: i,
      employeeId: `MWS-${String(i).padStart(5, '0')}`,
      employeeName: generateEmployeeName(i),
      department: MWS_DEPARTMENTS[i % MWS_DEPARTMENTS.length],
      position: MWS_POSITIONS[i % MWS_POSITIONS.length],
      manager: MWS_MANAGERS[i % MWS_MANAGERS.length].name,
      mentor: i % 2 === 0 ? MWS_MENTORS[i % MWS_MENTORS.length] : undefined,
      startDate: startDate.toISOString().split('T')[0],
      expectedEndDate: expectedEndDate.toISOString().split('T')[0],
      actualEndDate: status === 'completed' ? expectedEndDate.toISOString().split('T')[0] : undefined,
      programDurationMonths: programDuration,
      status,
      overallProgress,
      quarterlyReviews,
      skillsDevelopment,
      certifications,
      trainingHoursCompleted,
      trainingHoursRequired,
      notes: generateNotes(i, status, overallProgress),
      createdAt: startDate.toISOString(),
      updatedAt: currentDate.toISOString()
    };

    trainees.push(trainee);
  }

  return trainees;
};

// Helper function to generate employee names
function generateEmployeeName(index: number): string {
  const firstNames = [
    'Olivia', 'Liam', 'Emma', 'Noah', 'Ava', 'Elijah', 'Sophia', 'Lucas',
    'Isabella', 'Mason', 'Mia', 'Ethan', 'Charlotte', 'Oliver', 'Amelia',
    'James', 'Harper', 'Benjamin', 'Evelyn', 'William', 'Abigail', 'Alexander',
    'Emily', 'Michael', 'Elizabeth', 'Daniel', 'Sofia', 'Henry', 'Avery', 'Jackson',
    'Ella', 'Sebastian', 'Madison', 'Aiden', 'Scarlett', 'Matthew', 'Victoria',
    'Samuel', 'Aria', 'David', 'Grace', 'Joseph', 'Chloe', 'Carter', 'Camila',
    'Owen', 'Penelope', 'Wyatt', 'Luna', 'John'
  ];
  
  const lastNames = [
    'Johnson', 'Smith', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis',
    'Rodriguez', 'Martinez', 'Hernandez', 'Lopez', 'Gonzalez', 'Wilson', 'Anderson',
    'Thomas', 'Taylor', 'Moore', 'Jackson', 'Martin', 'Lee', 'Perez', 'Thompson',
    'White', 'Harris', 'Sanchez', 'Clark', 'Ramirez', 'Lewis', 'Robinson',
    'Walker', 'Young', 'Allen', 'King', 'Wright', 'Scott', 'Torres', 'Nguyen',
    'Hill', 'Flores', 'Green', 'Adams', 'Nelson', 'Baker', 'Hall', 'Rivera',
    'Campbell', 'Mitchell', 'Carter', 'Roberts'
  ];

  return `${firstNames[index % firstNames.length]} ${lastNames[Math.floor(index / firstNames.length) % lastNames.length]}`;
}

// Generate quarterly reviews based on progress
function generateQuarterlyReviews(startDate: Date, progress: number, duration: number): QuarterlyReview[] {
  const reviews: QuarterlyReview[] = [];
  const quartersCompleted = Math.floor((progress / 100) * (duration / 3));
  
  for (let quarter = 1; quarter <= Math.min(8, quartersCompleted + 1); quarter++) {
    const dueDate = new Date(startDate);
    dueDate.setMonth(dueDate.getMonth() + (quarter * 3));
    
    const isCompleted = quarter <= quartersCompleted;
    const completedDate = isCompleted ? new Date(dueDate) : undefined;
    if (completedDate) {
      completedDate.setDate(completedDate.getDate() - Math.floor(Math.random() * 7));
    }

    const rating = 3 + Math.floor(Math.random() * 2) + (progress > 75 ? 1 : 0);

    const review: QuarterlyReview = {
      reviewNumber: quarter,
      dueDate: dueDate.toISOString().split('T')[0],
      completedDate: completedDate?.toISOString().split('T')[0],
      reviewer: MWS_MANAGERS[Math.floor(Math.random() * MWS_MANAGERS.length)].name,
      overallRating: Math.min(5, rating),
      feedback: generateReviewFeedback(quarter, progress),
      goalsAchieved: generateGoalsAchieved(quarter),
      goalsPending: isCompleted ? [] : generateGoalsPending(quarter),
      skillsImproved: generateSkillsImproved(quarter),
      areasForImprovement: generateAreasForImprovement(quarter),
      nextQuarterGoals: generateNextQuarterGoals(quarter),
      supportNeeded: generateSupportNeeded(quarter),
      isSatisfactory: rating >= 3,
      continuationRecommendation: rating >= 3
    };

    reviews.push(review);
  }

  return reviews;
}

// Generate review feedback based on quarter and progress
function generateReviewFeedback(quarter: number, progress: number): string {
  const feedbackTemplates = [
    'Excellent progress in cloud technologies. Shows strong initiative in learning AWS and Azure platforms.',
    'Good understanding of DevOps principles. Successfully implemented CI/CD pipelines for team projects.',
    'Demonstrates strong problem-solving skills. Effectively troubleshoots complex infrastructure issues.',
    'Outstanding contribution to security assessments. Identified and resolved critical vulnerabilities.',
    'Impressive growth in technical skills. Mastered containerization and orchestration technologies.',
    'Strong collaboration with cross-functional teams. Excellent communication in technical discussions.',
    'Shows leadership potential. Successfully mentored junior team members on cloud best practices.',
    'Exceptional performance in client projects. Delivered solutions ahead of schedule with high quality.'
  ];

  return feedbackTemplates[(quarter - 1) % feedbackTemplates.length];
}

function generateGoalsAchieved(quarter: number): string[] {
  const goals = [
    ['Complete AWS fundamentals training', 'Set up development environment', 'Shadow senior engineers'],
    ['Deploy first cloud application', 'Implement CI/CD pipeline', 'Complete security training'],
    ['Lead infrastructure migration project', 'Obtain AWS certification', 'Mentor new team members'],
    ['Architect multi-cloud solution', 'Optimize system performance', 'Present at tech meetup'],
    ['Implement disaster recovery plan', 'Automate deployment processes', 'Complete advanced certifications'],
    ['Design microservices architecture', 'Establish monitoring systems', 'Lead client workshops'],
    ['Develop cost optimization strategies', 'Implement security automation', 'Contribute to open source'],
    ['Lead enterprise transformation', 'Establish best practices', 'Train client teams']
  ];

  return goals[(quarter - 1) % goals.length];
}

function generateGoalsPending(quarter: number): string[] {
  const pending = [
    ['Complete remaining certifications'],
    ['Finalize project documentation'],
    ['Implement advanced monitoring'],
    ['Optimize resource utilization'],
    ['Complete security audit'],
    ['Establish governance framework'],
    ['Develop automation scripts'],
    ['Create knowledge base']
  ];

  return pending[(quarter - 1) % pending.length];
}

function generateSkillsImproved(quarter: number): string[] {
  const skills = [
    ['Cloud Architecture', 'Problem Solving'],
    ['DevOps Practices', 'Team Collaboration'],
    ['Security Protocols', 'Client Communication'],
    ['Data Analytics', 'Project Management'],
    ['Automation', 'Technical Documentation'],
    ['System Design', 'Leadership'],
    ['Performance Tuning', 'Innovation'],
    ['Risk Management', 'Strategic Planning']
  ];

  return skills[(quarter - 1) % skills.length];
}

function generateAreasForImprovement(quarter: number): string[] {
  const areas = [
    ['Time management', 'Documentation skills'],
    ['Presentation skills', 'Cost optimization'],
    ['Advanced networking', 'Database optimization'],
    ['Machine learning basics', 'Business acumen'],
    ['Stakeholder management', 'Risk assessment'],
    ['Financial planning', 'Vendor management'],
    ['Change management', 'Compliance knowledge'],
    ['Strategic thinking', 'Innovation mindset']
  ];

  return areas[(quarter - 1) % areas.length];
}

function generateNextQuarterGoals(quarter: number): string[] {
  const goals = [
    ['Complete Azure fundamentals', 'Deploy production application', 'Participate in on-call rotation'],
    ['Obtain Kubernetes certification', 'Lead small project', 'Improve automation scripts'],
    ['Design scalable architecture', 'Implement cost controls', 'Present technical solutions'],
    ['Complete security framework', 'Optimize performance metrics', 'Develop training materials'],
    ['Lead transformation project', 'Establish governance', 'Mentor junior engineers'],
    ['Implement ML operations', 'Create disaster recovery', 'Build client relationships'],
    ['Develop innovation framework', 'Establish metrics dashboard', 'Lead technical workshops'],
    ['Complete program milestones', 'Transition to full role', 'Document best practices']
  ];

  return goals[(quarter - 1) % goals.length];
}

function generateSupportNeeded(quarter: number): string[] {
  const support = [
    ['Additional cloud training resources'],
    ['Mentorship on complex projects'],
    ['Access to certification prep materials'],
    ['Time for focused learning'],
    ['Exposure to enterprise projects'],
    ['Leadership development training'],
    ['Advanced technical workshops'],
    ['Executive shadowing opportunities']
  ];

  return [support[(quarter - 1) % support.length]];
}

// Generate skills development data
function generateSkillsDevelopment(progress: number): SkillDevelopment[] {
  return MWS_SKILLS.slice(0, 8).map((skill, index) => {
    const baselineLevel = 1 + Math.floor(Math.random() * 2);
    const targetLevel = 4 + Math.floor(Math.random() * 2);
    const progressFactor = progress / 100;
    const currentLevel = Math.min(targetLevel, Math.floor(baselineLevel + (targetLevel - baselineLevel) * progressFactor * (0.8 + Math.random() * 0.4)));
    
    const skillProgress = Math.min(100, Math.floor(((currentLevel - baselineLevel) / (targetLevel - baselineLevel)) * 100));

    const trainingModules = [
      'Fundamentals Course',
      'Intermediate Workshop',
      'Advanced Training',
      'Expert Certification',
      'Master Class'
    ];

    const completedCount = Math.floor((skillProgress / 100) * trainingModules.length);
    const trainingCompleted = trainingModules.slice(0, completedCount);

    return {
      skillName: skill.name,
      category: skill.category,
      baselineLevel,
      targetLevel,
      currentLevel,
      progressPercentage: skillProgress,
      trainingCompleted,
      nextSteps: skillProgress < 100 ? `Complete ${trainingModules[completedCount] || 'final assessment'}` : undefined
    };
  });
}

// Generate certifications data
function generateCertifications(progress: number): Certification[] {
  return MWS_CERTIFICATIONS.slice(0, 5).map((cert, index) => {
    const threshold = cert.mandatory ? 50 : 75;
    const isAchieved = progress >= threshold + (index * 5);
    const achievedDate = isAchieved ? new Date(Date.now() - Math.floor(Math.random() * 180 * 24 * 60 * 60 * 1000)) : undefined;
    const expiryDate = achievedDate ? new Date(achievedDate.getTime() + (365 * 2 * 24 * 60 * 60 * 1000)) : undefined;

    let status: Certification['status'];
    if (isAchieved) {
      status = 'completed';
    } else if (progress >= threshold - 20) {
      status = 'in_progress';
    } else {
      status = 'not_started';
    }

    return {
      name: cert.name,
      issuingBody: cert.issuer,
      dateAchieved: achievedDate?.toISOString().split('T')[0],
      expiryDate: expiryDate?.toISOString().split('T')[0],
      isMandatory: cert.mandatory,
      status
    };
  });
}

// Generate contextual notes
function generateNotes(index: number, status: string, progress: number): string | undefined {
  if (status === 'terminated') {
    return 'Program terminated due to personal reasons. Eligible for re-entry.';
  }
  if (status === 'on_hold') {
    return 'Program temporarily on hold due to project requirements. Will resume next quarter.';
  }
  if (progress >= 90) {
    return 'Outstanding performance. Recommended for fast-track to senior position.';
  }
  if (progress >= 75 && index % 3 === 0) {
    return 'Excellent technical skills. Consider for specialized cloud architecture track.';
  }
  if (progress >= 50 && index % 4 === 0) {
    return 'Strong progress in security certifications. Potential for security team placement.';
  }
  if (index % 5 === 0) {
    return 'Shows exceptional leadership qualities. Consider for team lead opportunities.';
  }
  if (index % 7 === 0) {
    return 'Client feedback very positive. Strong consulting potential.';
  }
  return undefined;
}

// Generate metrics for MWS trainees
export const generateMWSMetrics = (trainees: GraduateTraineeProgram[]) => {
  const total = trainees.length;
  const byStatus = {
    active: trainees.filter(t => t.status === 'in_progress').length,
    completed: trainees.filter(t => t.status === 'completed').length,
    onHold: trainees.filter(t => t.status === 'on_hold').length,
    notStarted: trainees.filter(t => t.status === 'not_started').length,
    terminated: trainees.filter(t => t.status === 'terminated').length
  };

  const avgProgress = trainees.length > 0 
    ? Math.round(trainees.reduce((sum, t) => sum + t.overallProgress, 0) / trainees.length)
    : 0;

  const reviewsDue = trainees.filter(t => 
    t.quarterlyReviews?.some(r => !r.completedDate && new Date(r.dueDate) <= new Date())
  ).length;

  const certificationsExpiring = trainees.reduce((count, t) => {
    const expiring = t.certifications?.filter(c => {
      if (!c.expiryDate || c.status !== 'completed') return false;
      const expiry = new Date(c.expiryDate);
      const ninetyDaysFromNow = new Date();
      ninetyDaysFromNow.setDate(ninetyDaysFromNow.getDate() + 90);
      return expiry <= ninetyDaysFromNow;
    }).length || 0;
    return count + expiring;
  }, 0);

  // Department distribution
  const departmentDistribution = MWS_DEPARTMENTS.reduce((acc, dept) => {
    acc[dept] = trainees.filter(t => t.department === dept).length;
    return acc;
  }, {} as Record<string, number>);

  // Certification completion rates
  const certificationStats = MWS_CERTIFICATIONS.map(cert => {
    const total = trainees.filter(t => 
      t.certifications?.some(c => c.name === cert.name)
    ).length;
    const completed = trainees.filter(t => 
      t.certifications?.some(c => c.name === cert.name && c.status === 'completed')
    ).length;
    return {
      name: cert.name,
      completionRate: total > 0 ? Math.round((completed / total) * 100) : 0,
      mandatory: cert.mandatory
    };
  });

  // Training hours statistics
  const trainingStats = {
    totalRequired: trainees.reduce((sum, t) => sum + t.trainingHoursRequired, 0),
    totalCompleted: trainees.reduce((sum, t) => sum + t.trainingHoursCompleted, 0),
    avgCompletionRate: trainees.length > 0
      ? Math.round(trainees.reduce((sum, t) => 
          sum + (t.trainingHoursCompleted / t.trainingHoursRequired) * 100, 0
        ) / trainees.length)
      : 0
  };

  return {
    total,
    ...byStatus,
    avgProgress,
    reviewsDue,
    certificationsExpiring,
    departmentDistribution,
    certificationStats,
    trainingStats,
    topPerformers: trainees
      .filter(t => t.overallProgress >= 80 && t.status === 'in_progress')
      .sort((a, b) => b.overallProgress - a.overallProgress)
      .slice(0, 5)
      .map(t => ({ id: t.id, name: t.employeeName, progress: t.overallProgress })),
    atRisk: trainees
      .filter(t => t.status === 'in_progress' && t.overallProgress < 30)
      .map(t => ({ id: t.id, name: t.employeeName, progress: t.overallProgress }))
  };
};

// Export default mock data
export const MWS_MOCK_DATA = {
  trainees: generateMWSTrainees(50),
  departments: MWS_DEPARTMENTS,
  positions: MWS_POSITIONS,
  managers: MWS_MANAGERS,
  mentors: MWS_MENTORS,
  certifications: MWS_CERTIFICATIONS,
  skills: MWS_SKILLS
};

// Function to get filtered trainees
export const getFilteredMWSTrainees = (
  filters: {
    department?: string;
    status?: string;
    manager?: string;
    searchQuery?: string;
  } = {}
) => {
  let filteredTrainees = [...MWS_MOCK_DATA.trainees];

  if (filters.department) {
    filteredTrainees = filteredTrainees.filter(t => t.department === filters.department);
  }

  if (filters.status) {
    filteredTrainees = filteredTrainees.filter(t => t.status === filters.status);
  }

  if (filters.manager) {
    filteredTrainees = filteredTrainees.filter(t => t.manager === filters.manager);
  }

  if (filters.searchQuery) {
    const query = filters.searchQuery.toLowerCase();
    filteredTrainees = filteredTrainees.filter(t => 
      t.employeeName.toLowerCase().includes(query) ||
      t.employeeId.toLowerCase().includes(query) ||
      t.department.toLowerCase().includes(query) ||
      t.position.toLowerCase().includes(query)
    );
  }

  return filteredTrainees;
};

// Function to get trainee by ID
export const getMWSTraineeById = (id: number): GraduateTraineeProgram | undefined => {
  return MWS_MOCK_DATA.trainees.find(t => t.id === id);
};

// Function to simulate API delay
export const simulateAPIDelay = (ms: number = 500) => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

// Export for easy access
export default {
  generateMWSTrainees,
  generateMWSMetrics,
  getFilteredMWSTrainees,
  getMWSTraineeById,
  simulateAPIDelay,
  MWS_MOCK_DATA
};