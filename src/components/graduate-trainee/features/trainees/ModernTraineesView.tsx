/**
 * ModernTraineesView - 2025 Redesign
 * Trainee management with glass-morphism and modern UI
 */

import React, { useState, useMemo, useCallback, memo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Users,
  Plus,
  Search,
  Filter,
  Grid,
  List,
  Download,
  Upload,
  Eye,
  Edit,
  Trash2,
  UserPlus,
  Award,
  TrendingUp,
  Calendar,
  MoreVertical,
  ChevronDown
} from 'lucide-react';
import { GlassCard } from '../../core/design-system/components/GlassCard';
import { GlassButton } from '../../core/design-system/components/GlassButton';
import { GlassInput } from '../../core/design-system/components/GlassInput';
import { useTraineeStore } from '../../store/traineeStore';
import { cn } from '@/lib/utils';
import type { GraduateTraineeProgramExtended } from '../../types/adapter';

type ViewMode = 'grid' | 'list';
type FilterStatus = 'all' | 'active' | 'completed' | 'on_hold' | 'not_started';

const ModernTraineesView: React.FC = () => {
  const { trainees, openDialog } = useTraineeStore();
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<FilterStatus>('all');
  const [departmentFilter, setDepartmentFilter] = useState<string>('all');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedTrainees, setSelectedTrainees] = useState<number[]>([]);

  // Filter trainees
  const filteredTrainees = useMemo(() => {
    return trainees.filter(trainee => {
      const matchesSearch = trainee.employeeName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          trainee.department.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          trainee.position.toLowerCase().includes(searchQuery.toLowerCase());
      
      const matchesStatus = statusFilter === 'all' || trainee.status === statusFilter;
      const matchesDepartment = departmentFilter === 'all' || trainee.department === departmentFilter;
      
      return matchesSearch && matchesStatus && matchesDepartment;
    });
  }, [trainees, searchQuery, statusFilter, departmentFilter]);

  // Get unique departments
  const departments = useMemo(() => {
    const depts = Array.from(new Set(trainees.map(t => t.department)));
    return ['all', ...depts];
  }, [trainees]);

  // Status badge colors
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'completed': return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      case 'on_hold': return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      case 'not_started': return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  // Handle trainee actions
  const handleViewTrainee = useCallback((trainee: GraduateTraineeProgramExtended) => {
    openDialog('view', trainee);
  }, [openDialog]);

  const handleEditTrainee = useCallback((trainee: GraduateTraineeProgramExtended) => {
    openDialog('edit', trainee);
  }, [openDialog]);

  const handleDeleteTrainee = useCallback((trainee: GraduateTraineeProgramExtended) => {
    openDialog('delete', trainee);
  }, [openDialog]);

  const handleBulkAction = (action: string) => {
    // Handle bulk actions
    console.log('Bulk action:', action, selectedTrainees);
  };

  const TraineeCard = memo(({ trainee }: { trainee: GraduateTraineeProgramExtended }) => (
    <motion.div
      layout
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      whileHover={{ y: -4 }}
      transition={{ duration: 0.2 }}
    >
      <GlassCard variant="elevated" className="p-5 h-full">
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 rounded-full bg-gradient-to-br from-primary-500/20 to-accent-500/20 flex items-center justify-center">
              <Users className="w-6 h-6 text-white" />
            </div>
            <div>
              <h3 className="font-medium text-white">{trainee.employeeName}</h3>
              <p className="text-sm text-white/60">{trainee.position}</p>
            </div>
          </div>
          <div className="relative">
            <button className="p-1 rounded-lg hover:bg-white/10 transition-colors">
              <MoreVertical className="w-4 h-4 text-white/60" />
            </button>
          </div>
        </div>

        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm text-white/60">Department</span>
            <span className="text-sm text-white">{trainee.department}</span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-sm text-white/60">Progress</span>
            <div className="flex items-center gap-2">
              <div className="w-24 h-2 bg-white/10 rounded-full overflow-hidden">
                <motion.div
                  className="h-full bg-gradient-to-r from-primary-400 to-accent-400"
                  initial={{ width: 0 }}
                  animate={{ width: `${trainee.currentProgress || 0}%` }}
                  transition={{ duration: 1, ease: 'easeOut' }}
                />
              </div>
              <span className="text-sm text-white">{trainee.currentProgress || 0}%</span>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm text-white/60">Status</span>
            <span className={cn(
              'px-2 py-1 text-xs rounded-lg border',
              getStatusColor(trainee.status)
            )}>
              {trainee.status.replace('_', ' ')}
            </span>
          </div>
        </div>

        <div className="flex items-center gap-2 mt-4 pt-4 border-t border-white/10">
          <GlassButton
            variant="ghost"
            size="sm"
            icon={<Eye className="w-4 h-4" />}
            onClick={() => handleViewTrainee(trainee)}
            className="flex-1"
          >
            View
          </GlassButton>
          <GlassButton
            variant="ghost"
            size="sm"
            icon={<Edit className="w-4 h-4" />}
            onClick={() => handleEditTrainee(trainee)}
            className="flex-1"
          >
            Edit
          </GlassButton>
        </div>
      </GlassCard>
    </motion.div>
  ));

  const TraineeListItem = memo(({ trainee }: { trainee: GraduateTraineeProgramExtended }) => (
    <motion.div
      layout
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      className="group"
    >
      <GlassCard variant="flat" className="p-4 hover:bg-white/5 transition-colors">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4 flex-1">
            <div className="w-10 h-10 rounded-full bg-gradient-to-br from-primary-500/20 to-accent-500/20 flex items-center justify-center">
              <Users className="w-5 h-5 text-white" />
            </div>
            
            <div className="grid grid-cols-5 gap-4 flex-1">
              <div>
                <p className="font-medium text-white">{trainee.employeeName}</p>
                <p className="text-sm text-white/60">{trainee.employeeId}</p>
              </div>
              
              <div>
                <p className="text-sm text-white/60">Department</p>
                <p className="text-sm text-white">{trainee.department}</p>
              </div>
              
              <div>
                <p className="text-sm text-white/60">Position</p>
                <p className="text-sm text-white">{trainee.position}</p>
              </div>
              
              <div>
                <p className="text-sm text-white/60">Progress</p>
                <div className="flex items-center gap-2">
                  <div className="w-16 h-1.5 bg-white/10 rounded-full overflow-hidden">
                    <div
                      className="h-full bg-gradient-to-r from-primary-400 to-accent-400"
                      style={{ width: `${trainee.currentProgress || 0}%` }}
                    />
                  </div>
                  <span className="text-sm text-white">{trainee.currentProgress || 0}%</span>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <span className={cn(
                  'px-2 py-1 text-xs rounded-lg border',
                  getStatusColor(trainee.status)
                )}>
                  {trainee.status.replace('_', ' ')}
                </span>
              </div>
            </div>
          </div>
          
          <div className="flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
            <button
              onClick={() => handleViewTrainee(trainee)}
              className="p-2 rounded-lg hover:bg-white/10 transition-colors"
            >
              <Eye className="w-4 h-4 text-white/60" />
            </button>
            <button
              onClick={() => handleEditTrainee(trainee)}
              className="p-2 rounded-lg hover:bg-white/10 transition-colors"
            >
              <Edit className="w-4 h-4 text-white/60" />
            </button>
            <button
              onClick={() => handleDeleteTrainee(trainee)}
              className="p-2 rounded-lg hover:bg-white/10 transition-colors"
            >
              <Trash2 className="w-4 h-4 text-white/60" />
            </button>
          </div>
        </div>
      </GlassCard>
    </motion.div>
  ));

  return (
    <div className="h-full overflow-hidden flex flex-col">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-white">Trainees Management</h2>
            <p className="text-white/60 mt-1">Manage and track graduate trainee programs</p>
          </div>
          
          <div className="flex items-center gap-3">
            <GlassButton
              variant="secondary"
              size="md"
              icon={<Download className="w-4 h-4" />}
            >
              Export
            </GlassButton>
            <GlassButton
              variant="primary"
              size="md"
              icon={<UserPlus className="w-4 h-4" />}
              onClick={() => openDialog('create', null)}
            >
              Add Trainee
            </GlassButton>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="space-y-4 mb-6">
        <div className="flex items-center gap-4">
          <div className="flex-1">
            <GlassInput
              type="text"
              placeholder="Search trainees..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              icon={<Search className="w-4 h-4" />}
              className="w-full"
            />
          </div>
          
          <div className="flex items-center gap-2">
            <GlassButton
              variant={showFilters ? 'primary' : 'ghost'}
              size="md"
              icon={<Filter className="w-4 h-4" />}
              onClick={() => setShowFilters(!showFilters)}
            >
              Filters
            </GlassButton>
            
            <div className="flex bg-white/10 backdrop-blur-md rounded-xl p-1">
              <button
                onClick={() => setViewMode('grid')}
                className={cn(
                  'p-2 rounded-lg transition-colors',
                  viewMode === 'grid' ? 'bg-white/20 text-white' : 'text-white/60 hover:text-white'
                )}
              >
                <Grid className="w-4 h-4" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={cn(
                  'p-2 rounded-lg transition-colors',
                  viewMode === 'list' ? 'bg-white/20 text-white' : 'text-white/60 hover:text-white'
                )}
              >
                <List className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>

        {/* Filter Options */}
        <AnimatePresence>
          {showFilters && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.2 }}
            >
              <GlassCard variant="flat" className="p-4">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div>
                    <label className="text-sm text-white/60 mb-2 block">Status</label>
                    <select
                      value={statusFilter}
                      onChange={(e) => setStatusFilter(e.target.value as FilterStatus)}
                      className="w-full px-3 py-2 bg-white/10 backdrop-blur-md border border-white/20 rounded-lg text-white"
                    >
                      <option value="all">All Status</option>
                      <option value="active">Active</option>
                      <option value="completed">Completed</option>
                      <option value="on_hold">On Hold</option>
                      <option value="not_started">Not Started</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="text-sm text-white/60 mb-2 block">Department</label>
                    <select
                      value={departmentFilter}
                      onChange={(e) => setDepartmentFilter(e.target.value)}
                      className="w-full px-3 py-2 bg-white/10 backdrop-blur-md border border-white/20 rounded-lg text-white"
                    >
                      {departments.map(dept => (
                        <option key={dept} value={dept}>
                          {dept === 'all' ? 'All Departments' : dept}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
              </GlassCard>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Results Summary */}
      <div className="flex items-center justify-between mb-4">
        <p className="text-sm text-white/60">
          Showing <span className="text-white font-medium">{filteredTrainees.length}</span> of{' '}
          <span className="text-white font-medium">{trainees.length}</span> trainees
        </p>
        
        {selectedTrainees.length > 0 && (
          <div className="flex items-center gap-2">
            <span className="text-sm text-white/60">
              {selectedTrainees.length} selected
            </span>
            <GlassButton
              variant="ghost"
              size="sm"
              onClick={() => handleBulkAction('export')}
            >
              Export Selected
            </GlassButton>
            <GlassButton
              variant="ghost"
              size="sm"
              onClick={() => setSelectedTrainees([])}
            >
              Clear
            </GlassButton>
          </div>
        )}
      </div>

      {/* Trainees Grid/List */}
      <div className="flex-1 overflow-y-auto custom-scrollbar">
        <AnimatePresence mode="wait">
          {viewMode === 'grid' ? (
            <motion.div
              key="grid"
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              {filteredTrainees.map((trainee, index) => (
                <TraineeCard key={trainee.id || trainee.traineeId || `trainee-${index}`} trainee={trainee} />
              ))}
            </motion.div>
          ) : (
            <motion.div
              key="list"
              className="space-y-2"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              {filteredTrainees.map((trainee, index) => (
                <TraineeListItem key={trainee.id || trainee.traineeId || `trainee-list-${index}`} trainee={trainee} />
              ))}
            </motion.div>
          )}
        </AnimatePresence>

        {filteredTrainees.length === 0 && (
          <div className="flex flex-col items-center justify-center py-12">
            <Users className="w-12 h-12 text-white/20 mb-4" />
            <p className="text-white/60">No trainees found</p>
            <p className="text-white/40 text-sm mt-1">Try adjusting your filters or search query</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ModernTraineesView;