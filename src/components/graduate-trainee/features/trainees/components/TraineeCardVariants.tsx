import React from 'react';
import {
  User,
  Building,
  Calendar,
  Clock,
  TrendingUp,
  MoreVertical,
  Eye,
  Edit2,
  Trash2,
  CheckCircle,
  XCircle,
  PauseCircle,
  PlayCircle,
  AlertCircle
} from 'lucide-react';
import { Card } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';
import type { GraduateTraineeProgram } from '@/lib/api/graduateTrainee';
// ActionMenu removed - using inline dropdown instead

interface TraineeCardProps {
  trainee: GraduateTraineeProgram;
  onView: () => void;
  onEdit: () => void;
  onDelete: () => void;
  variant?: 'grid' | 'list' | 'compact' | 'kanban';
}

const statusConfig = {
  in_progress: {
    icon: PlayCircle,
    color: 'text-blue-500',
    bg: 'bg-blue-50 dark:bg-blue-900/20',
    label: 'In Progress'
  },
  completed: {
    icon: CheckCircle,
    color: 'text-green-500',
    bg: 'bg-green-50 dark:bg-green-900/20',
    label: 'Completed'
  },
  on_hold: {
    icon: PauseCircle,
    color: 'text-yellow-500',
    bg: 'bg-yellow-50 dark:bg-yellow-900/20',
    label: 'On Hold'
  },
  not_started: {
    icon: Clock,
    color: 'text-gray-500',
    bg: 'bg-gray-50 dark:bg-gray-900/20',
    label: 'Not Started'
  },
  terminated: {
    icon: XCircle,
    color: 'text-red-500',
    bg: 'bg-red-50 dark:bg-red-900/20',
    label: 'Terminated'
  }
};

// Grid View Card
export const GridCard: React.FC<TraineeCardProps> = ({ trainee, onView, onEdit, onDelete }) => {
  const status = statusConfig[trainee.status] || statusConfig.not_started;
  const StatusIcon = status.icon;

  return (
    <Card className="glass-card p-4 hover:shadow-lg transition-all cursor-pointer group">
      <div onClick={onView} className="space-y-3">
        {/* Header */}
        <div className="flex items-start justify-between">
          <Avatar className="h-10 w-10">
            <AvatarFallback className="text-xs bg-primary/10">
              {trainee.employeeName.split(' ').map(n => n[0]).join('')}
            </AvatarFallback>
          </Avatar>
          <DropdownMenu>
            <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={(e) => { e.stopPropagation(); onView(); }}>
                <Eye className="h-3 w-3 mr-2" /> View
              </DropdownMenuItem>
              <DropdownMenuItem onClick={(e) => { e.stopPropagation(); onEdit(); }}>
                <Edit2 className="h-3 w-3 mr-2" /> Edit
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={(e) => { e.stopPropagation(); onDelete(); }} className="text-red-600">
                <Trash2 className="h-3 w-3 mr-2" /> Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* Name & ID */}
        <div>
          <h3 className="font-medium text-sm truncate">{trainee.employeeName}</h3>
          <p className="text-xs text-muted-foreground">{trainee.employeeId}</p>
        </div>

        {/* Department & Position */}
        <div className="space-y-1">
          <div className="flex items-center gap-1.5 text-xs text-muted-foreground">
            <Building className="h-3 w-3" />
            <span className="truncate">{trainee.department}</span>
          </div>
          <div className="flex items-center gap-1.5 text-xs text-muted-foreground">
            <User className="h-3 w-3" />
            <span className="truncate">{trainee.position}</span>
          </div>
        </div>

        {/* Progress */}
        <div className="space-y-1.5">
          <div className="flex items-center justify-between">
            <span className="text-xs text-muted-foreground">Progress</span>
            <span className="text-xs font-medium">{trainee.overallProgress}%</span>
          </div>
          <Progress value={trainee.overallProgress} className="h-1.5" />
        </div>

        {/* Status Badge */}
        <div className={cn("inline-flex items-center gap-1.5 px-2 py-1 rounded-full text-xs", status.bg)}>
          <StatusIcon className={cn("h-3 w-3", status.color)} />
          <span className={status.color}>{status.label}</span>
        </div>
      </div>
    </Card>
  );
};

// List View Card
export const ListCard: React.FC<TraineeCardProps> = ({ trainee, onView, onEdit, onDelete }) => {
  const status = statusConfig[trainee.status] || statusConfig.not_started;
  const StatusIcon = status.icon;

  return (
    <Card className="glass-card p-4 hover:shadow-md transition-all cursor-pointer">
      <div onClick={onView} className="flex items-center gap-4">
        {/* Avatar */}
        <Avatar className="h-12 w-12">
          <AvatarFallback className="text-sm bg-primary/10">
            {trainee.employeeName.split(' ').map(n => n[0]).join('')}
          </AvatarFallback>
        </Avatar>

        {/* Main Content */}
        <div className="flex-1 grid grid-cols-1 md:grid-cols-4 gap-4 items-center">
          {/* Name & ID */}
          <div>
            <h3 className="font-medium text-sm">{trainee.employeeName}</h3>
            <p className="text-xs text-muted-foreground">{trainee.employeeId}</p>
          </div>

          {/* Department & Position */}
          <div className="text-xs text-muted-foreground">
            <div className="flex items-center gap-1.5">
              <Building className="h-3 w-3" />
              {trainee.department}
            </div>
            <div className="flex items-center gap-1.5 mt-0.5">
              <User className="h-3 w-3" />
              {trainee.position}
            </div>
          </div>

          {/* Progress */}
          <div className="flex items-center gap-3">
            <Progress value={trainee.overallProgress} className="h-2 flex-1" />
            <span className="text-xs font-medium">{trainee.overallProgress}%</span>
          </div>

          {/* Status */}
          <div className="flex items-center justify-between">
            <div className={cn("inline-flex items-center gap-1.5 px-2 py-1 rounded-full text-xs", status.bg)}>
              <StatusIcon className={cn("h-3 w-3", status.color)} />
              <span className={status.color}>{status.label}</span>
            </div>

            {/* Actions */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={(e) => { e.stopPropagation(); onView(); }}>
                  <Eye className="h-3 w-3 mr-2" /> View
                </DropdownMenuItem>
                <DropdownMenuItem onClick={(e) => { e.stopPropagation(); onEdit(); }}>
                  <Edit2 className="h-3 w-3 mr-2" /> Edit
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={(e) => { e.stopPropagation(); onDelete(); }} className="text-red-600">
                  <Trash2 className="h-3 w-3 mr-2" /> Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
    </Card>
  );
};

// Compact View Card
export const CompactCard: React.FC<TraineeCardProps> = ({ trainee, onView, onEdit, onDelete }) => {
  const status = statusConfig[trainee.status] || statusConfig.not_started;
  const StatusIcon = status.icon;

  return (
    <div 
      onClick={onView}
      className="flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-accent/5 transition-colors cursor-pointer group"
    >
      <Avatar className="h-8 w-8">
        <AvatarFallback className="text-xs bg-primary/10">
          {trainee.employeeName.split(' ').map(n => n[0]).join('')}
        </AvatarFallback>
      </Avatar>

      <div className="flex-1 flex items-center gap-4">
        <div className="min-w-0 flex-1">
          <span className="font-medium text-sm">{trainee.employeeName}</span>
          <span className="text-xs text-muted-foreground ml-2">#{trainee.employeeId}</span>
        </div>

        <Badge variant="outline" className="text-xs">
          {trainee.department}
        </Badge>

        <div className="flex items-center gap-2">
          <Progress value={trainee.overallProgress} className="h-1 w-20" />
          <span className="text-xs text-muted-foreground">{trainee.overallProgress}%</span>
        </div>

        <StatusIcon className={cn("h-4 w-4", status.color)} />

        <Button 
          variant="ghost" 
          size="sm" 
          className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100"
          onClick={(e) => { e.stopPropagation(); onEdit(); }}
        >
          <Edit2 className="h-3 w-3" />
        </Button>
      </div>
    </div>
  );
};

// Kanban Card
export const KanbanCard: React.FC<TraineeCardProps> = ({ trainee, onView, onEdit, onDelete }) => {
  return (
    <Card 
      className="glass-card p-3 hover:shadow-md transition-all cursor-pointer"
      onClick={onView}
    >
      <div className="space-y-2">
        <div className="flex items-start justify-between">
          <Avatar className="h-8 w-8">
            <AvatarFallback className="text-xs bg-primary/10">
              {trainee.employeeName.split(' ').map(n => n[0]).join('')}
            </AvatarFallback>
          </Avatar>
          <Button 
            variant="ghost" 
            size="sm" 
            className="h-6 w-6 p-0"
            onClick={(e) => { e.stopPropagation(); onEdit(); }}
          >
            <Edit2 className="h-3 w-3" />
          </Button>
        </div>

        <div>
          <h4 className="font-medium text-xs truncate">{trainee.employeeName}</h4>
          <p className="text-xs text-muted-foreground truncate">{trainee.position}</p>
        </div>

        <div className="flex items-center justify-between">
          <Badge variant="outline" className="text-xs">
            {trainee.department}
          </Badge>
          <span className="text-xs text-muted-foreground">{trainee.overallProgress}%</span>
        </div>

        <Progress value={trainee.overallProgress} className="h-1" />
      </div>
    </Card>
  );
};

// Main export that selects the right variant
export const TraineeCardVariant: React.FC<TraineeCardProps> = ({ variant = 'grid', ...props }) => {
  switch (variant) {
    case 'list':
      return <ListCard {...props} />;
    case 'compact':
      return <CompactCard {...props} />;
    case 'kanban':
      return <KanbanCard {...props} />;
    case 'grid':
    default:
      return <GridCard {...props} />;
  }
};