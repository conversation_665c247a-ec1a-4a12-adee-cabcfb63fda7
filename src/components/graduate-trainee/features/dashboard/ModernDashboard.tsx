/**
 * ModernDashboard - 2025 Redesign
 * AI-powered dashboard with advanced visualizations
 */

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { motion } from 'framer-motion';
import { 
  TrendingUp, 
  Users, 
  Target, 
  Award,
  Brain,
  Sparkles,
  ArrowUp,
  ArrowDown,
  Activity,
  Clock,
  Calendar,
  AlertCircle
} from 'lucide-react';
import { GlassCard } from '../../core/design-system/components/GlassCard';
import { GlassButton } from '../../core/design-system/components/GlassButton';
import { StatCard } from './components/StatCard';
import { ProgressRadialChart } from './components/ProgressRadialChart';
import { ActivityTimeline } from './components/ActivityTimeline';
import { PerformanceHeatmap } from './components/PerformanceHeatmap';
import { AIInsightsPanel } from './components/AIInsightsPanel';
import { InteractiveChart } from './components/InteractiveChart';
import { useTraineeStore } from '../../store/traineeStore';

const ModernDashboard: React.FC = () => {
  const { trainees, metrics, ui: { isLoading: loading } } = useTraineeStore();
  const [timeRange, setTimeRange] = useState<'week' | 'month' | 'quarter' | 'year'>('month');
  const [selectedMetric, setSelectedMetric] = useState<'performance' | 'progress' | 'engagement'>('performance');

  // Calculate metrics - memoized to prevent recalculation on every render
  const currentMetrics = useMemo(() => {
    const total = trainees.length;
    const active = trainees.filter(t => t.status === 'active').length;
    const avgProgress = trainees.reduce((acc, t) => acc + (t.currentProgress || 0), 0) / (total || 1);
    const completionRate = trainees.filter(t => t.status === 'completed').length / (total || 1) * 100;

    return {
      total,
      active,
      avgProgress: Math.round(avgProgress),
      completionRate: Math.round(completionRate),
      trend: avgProgress > 75 ? 'up' : avgProgress > 50 ? 'stable' : 'down'
    };
  }, [trainees]);

  // Memoize stats data to prevent recreation on every render
  const statsData = useMemo(() => [
    {
      title: 'Total Trainees',
      value: currentMetrics.total,
      change: '+12%',
      trend: 'up' as const,
      icon: <Users className="w-5 h-5" />,
      color: 'primary' as const,
      sparklineData: [40, 45, 50, 48, 55, 60, 58, 65]
    },
    {
      title: 'Active Programs',
      value: currentMetrics.active,
      change: '+8%',
      trend: 'up' as const,
      icon: <Activity className="w-5 h-5" />,
      color: 'accent' as const,
      sparklineData: [20, 22, 21, 24, 26, 25, 28, 30]
    },
    {
      title: 'Avg Progress',
      value: `${currentMetrics.avgProgress}%`,
      change: '+15%',
      trend: 'up' as const,
      icon: <Target className="w-5 h-5" />,
      color: 'success' as const,
      sparklineData: [60, 62, 65, 68, 70, 72, 75, 78]
    },
    {
      title: 'Completion Rate',
      value: `${currentMetrics.completionRate}%`,
      change: '-3%',
      trend: 'down' as const,
      icon: <Award className="w-5 h-5" />,
      color: 'warning' as const,
      sparklineData: [88, 87, 86, 85, 84, 85, 84, 83]
    }
  ], [currentMetrics]);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        ease: 'easeOut'
      }
    }
  };

  return (
    <div className="h-full overflow-hidden">
      <div className="h-full overflow-y-auto custom-scrollbar">
        <motion.div
          className="space-y-6"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {/* Header Section */}
          <motion.div variants={itemVariants} className="flex items-center justify-between">
            <div>
              <h2 className="text-3xl font-bold text-white flex items-center gap-3">
                <Sparkles className="w-8 h-8 text-primary-400" />
                AI-Powered Dashboard
              </h2>
              <p className="text-white/60 mt-1">Real-time insights and predictive analytics</p>
            </div>
            
            <div className="flex items-center gap-3">
              <div className="flex bg-white/10 backdrop-blur-md rounded-xl p-1">
                {(['week', 'month', 'quarter', 'year'] as const).map((range) => (
                  <GlassButton
                    key={range}
                    variant={timeRange === range ? 'primary' : 'ghost'}
                    size="sm"
                    onClick={() => setTimeRange(range)}
                    className="capitalize"
                  >
                    {range}
                  </GlassButton>
                ))}
              </div>
            </div>
          </motion.div>

          {/* Stats Grid */}
          <motion.div 
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"
            variants={itemVariants}
          >
            {statsData.map((stat, index) => (
              <StatCard key={index} {...stat} delay={index * 0.1} />
            ))}
          </motion.div>

          {/* AI Insights Panel */}
          <motion.div variants={itemVariants}>
            <AIInsightsPanel trainees={trainees} />
          </motion.div>

          {/* Main Content Grid */}
          <motion.div 
            className="grid grid-cols-1 lg:grid-cols-3 gap-6"
            variants={itemVariants}
          >
            {/* Performance Overview - 2 columns */}
            <div className="lg:col-span-2">
              <GlassCard variant="elevated" className="p-6 h-full">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-xl font-semibold text-white flex items-center gap-2">
                    <Activity className="w-5 h-5 text-primary-400" />
                    Performance Overview
                  </h3>
                  <div className="flex gap-2">
                    {(['performance', 'progress', 'engagement'] as const).map((metric) => (
                      <GlassButton
                        key={metric}
                        variant={selectedMetric === metric ? 'primary' : 'ghost'}
                        size="xs"
                        onClick={() => setSelectedMetric(metric)}
                        className="capitalize"
                      >
                        {metric}
                      </GlassButton>
                    ))}
                  </div>
                </div>
                <InteractiveChart 
                  data={trainees} 
                  metric={selectedMetric} 
                  timeRange={timeRange}
                />
              </GlassCard>
            </div>

            {/* Progress Radial - 1 column */}
            <div>
              <GlassCard variant="elevated" className="p-6 h-full">
                <h3 className="text-xl font-semibold text-white mb-6 flex items-center gap-2">
                  <Target className="w-5 h-5 text-accent-400" />
                  Overall Progress
                </h3>
                <ProgressRadialChart 
                  progress={currentMetrics.avgProgress}
                  target={85}
                  label="Average Progress"
                />
              </GlassCard>
            </div>
          </motion.div>

          {/* Bottom Grid */}
          <motion.div 
            className="grid grid-cols-1 lg:grid-cols-2 gap-6"
            variants={itemVariants}
          >
            {/* Performance Heatmap */}
            <GlassCard variant="elevated" className="p-6">
              <h3 className="text-xl font-semibold text-white mb-6 flex items-center gap-2">
                <Brain className="w-5 h-5 text-primary-400" />
                Performance Heatmap
              </h3>
              <PerformanceHeatmap trainees={trainees} />
            </GlassCard>

            {/* Activity Timeline */}
            <GlassCard variant="elevated" className="p-6">
              <h3 className="text-xl font-semibold text-white mb-6 flex items-center gap-2">
                <Clock className="w-5 h-5 text-accent-400" />
                Recent Activity
              </h3>
              <ActivityTimeline />
            </GlassCard>
          </motion.div>

          {/* Quick Stats Footer */}
          <motion.div 
            className="grid grid-cols-2 md:grid-cols-4 gap-4"
            variants={itemVariants}
          >
            <GlassCard variant="flat" className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/60 text-sm">Reviews Due</p>
                  <p className="text-2xl font-bold text-white">7</p>
                </div>
                <Calendar className="w-8 h-8 text-warning-400 opacity-50" />
              </div>
            </GlassCard>

            <GlassCard variant="flat" className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/60 text-sm">Certifications</p>
                  <p className="text-2xl font-bold text-white">24</p>
                </div>
                <Award className="w-8 h-8 text-success-400 opacity-50" />
              </div>
            </GlassCard>

            <GlassCard variant="flat" className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/60 text-sm">At Risk</p>
                  <p className="text-2xl font-bold text-white">3</p>
                </div>
                <AlertCircle className="w-8 h-8 text-error-400 opacity-50" />
              </div>
            </GlassCard>

            <GlassCard variant="flat" className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/60 text-sm">Trending Up</p>
                  <p className="text-2xl font-bold text-white">18</p>
                </div>
                <TrendingUp className="w-8 h-8 text-primary-400 opacity-50" />
              </div>
            </GlassCard>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
};

export default ModernDashboard;