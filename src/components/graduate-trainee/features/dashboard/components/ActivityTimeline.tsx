/**
 * ActivityTimeline Component - Recent Activity Feed
 * Shows recent events and activities with animations
 */

import React from 'react';
import { motion } from 'framer-motion';
import {
  UserPlus,
  Award,
  FileCheck,
  AlertCircle,
  TrendingUp,
  Calendar,
  Star,
  CheckCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface TimelineItem {
  id: string;
  type: 'trainee_added' | 'review_completed' | 'certification' | 'milestone' | 'warning';
  title: string;
  description: string;
  timestamp: Date;
  user?: string;
  icon: React.ReactNode;
  color: string;
}

export const ActivityTimeline: React.FC = () => {
  const activities: TimelineItem[] = [
    {
      id: '1',
      type: 'trainee_added',
      title: 'New Trainee Added',
      description: '<PERSON> joined the program',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
      user: 'Admin',
      icon: <UserPlus className="w-4 h-4" />,
      color: 'bg-blue-500'
    },
    {
      id: '2',
      type: 'review_completed',
      title: 'Quarterly Review Completed',
      description: '<PERSON> received an excellent rating',
      timestamp: new Date(Date.now() - 5 * 60 * 60 * 1000),
      user: 'Manager',
      icon: <FileCheck className="w-4 h-4" />,
      color: 'bg-green-500'
    },
    {
      id: '3',
      type: 'certification',
      title: 'Certification Achieved',
      description: 'Emily Chen completed AWS certification',
      timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000),
      icon: <Award className="w-4 h-4" />,
      color: 'bg-purple-500'
    },
    {
      id: '4',
      type: 'milestone',
      title: 'Milestone Reached',
      description: '90% completion rate for Q1 cohort',
      timestamp: new Date(Date.now() - 48 * 60 * 60 * 1000),
      icon: <Star className="w-4 h-4" />,
      color: 'bg-yellow-500'
    },
    {
      id: '5',
      type: 'warning',
      title: 'Action Required',
      description: '3 trainees need immediate review',
      timestamp: new Date(Date.now() - 72 * 60 * 60 * 1000),
      icon: <AlertCircle className="w-4 h-4" />,
      color: 'bg-red-500'
    }
  ];

  const formatTimestamp = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(hours / 24);

    if (hours < 1) return 'Just now';
    if (hours < 24) return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    if (days < 7) return `${days} day${days > 1 ? 's' : ''} ago`;
    return date.toLocaleDateString();
  };

  return (
    <div className="relative">
      <div className="space-y-4 max-h-[400px] overflow-y-auto custom-scrollbar pr-2">
        {activities.map((activity, index) => (
          <motion.div
            key={activity.id}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: index * 0.1 }}
            className="relative flex gap-3"
          >
            {/* Timeline line */}
            {index !== activities.length - 1 && (
              <div className="absolute left-5 top-10 bottom-0 w-[2px] bg-white/10" />
            )}

            {/* Icon */}
            <motion.div
              className={cn(
                'relative flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center',
                'ring-4 ring-gray-900/50',
                activity.color
              )}
              whileHover={{ scale: 1.1 }}
              transition={{ type: 'spring', stiffness: 400 }}
            >
              <span className="text-white">{activity.icon}</span>
              {index === 0 && (
                <motion.div
                  className="absolute inset-0 rounded-full bg-white/30"
                  animate={{ scale: [1, 1.5, 1] }}
                  transition={{ duration: 2, repeat: Infinity }}
                />
              )}
            </motion.div>

            {/* Content */}
            <div className="flex-1 min-w-0">
              <motion.div
                className="bg-white/5 backdrop-blur-sm rounded-lg p-3 border border-white/10"
                whileHover={{ backgroundColor: 'rgba(255, 255, 255, 0.08)' }}
                transition={{ duration: 0.2 }}
              >
                <div className="flex items-start justify-between gap-2">
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-white text-sm">{activity.title}</h4>
                    <p className="text-white/60 text-xs mt-1">{activity.description}</p>
                  </div>
                  <span className="text-white/40 text-xs whitespace-nowrap">
                    {formatTimestamp(activity.timestamp)}
                  </span>
                </div>
                {activity.user && (
                  <div className="flex items-center gap-1 mt-2">
                    <div className="w-4 h-4 rounded-full bg-white/20" />
                    <span className="text-white/50 text-xs">{activity.user}</span>
                  </div>
                )}
              </motion.div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Fade effect at bottom */}
      <div className="absolute bottom-0 left-0 right-0 h-12 bg-gradient-to-t from-gray-900/50 to-transparent pointer-events-none" />
    </div>
  );
};