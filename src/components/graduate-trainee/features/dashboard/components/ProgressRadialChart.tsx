/**
 * ProgressRadialChart Component - Circular Progress Visualization
 * Modern radial chart with animations
 */

import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface ProgressRadialChartProps {
  progress: number;
  target: number;
  label: string;
  size?: number;
  strokeWidth?: number;
}

export const ProgressRadialChart: React.FC<ProgressRadialChartProps> = ({
  progress,
  target,
  label,
  size = 200,
  strokeWidth = 12,
}) => {
  const [animatedProgress, setAnimatedProgress] = useState(0);
  
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const progressOffset = circumference - (animatedProgress / 100) * circumference;
  const targetOffset = circumference - (target / 100) * circumference;

  useEffect(() => {
    const timer = setTimeout(() => {
      setAnimatedProgress(progress);
    }, 100);
    return () => clearTimeout(timer);
  }, [progress]);

  const getProgressColor = () => {
    if (progress >= target) return '#10B981'; // Success
    if (progress >= target * 0.75) return '#F59E0B'; // Warning
    return '#EF4444'; // Error
  };

  return (
    <div className="flex flex-col items-center justify-center">
      <div className="relative">
        <svg
          width={size}
          height={size}
          className="transform -rotate-90"
        >
          {/* Background circle */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke="rgba(255, 255, 255, 0.1)"
            strokeWidth={strokeWidth}
            fill="none"
          />
          
          {/* Target indicator */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke="rgba(255, 255, 255, 0.2)"
            strokeWidth={strokeWidth + 4}
            fill="none"
            strokeDasharray={`2 ${circumference}`}
            strokeDashoffset={targetOffset}
            className="opacity-50"
          />
          
          {/* Progress circle */}
          <motion.circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke="url(#progressGradient)"
            strokeWidth={strokeWidth}
            fill="none"
            strokeLinecap="round"
            strokeDasharray={circumference}
            initial={{ strokeDashoffset: circumference }}
            animate={{ strokeDashoffset: progressOffset }}
            transition={{ duration: 1.5, ease: 'easeInOut' }}
          />
          
          {/* Gradient definition */}
          <defs>
            <linearGradient id="progressGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor={getProgressColor()} stopOpacity="1" />
              <stop offset="100%" stopColor={getProgressColor()} stopOpacity="0.7" />
            </linearGradient>
          </defs>
        </svg>
        
        {/* Center content */}
        <div className="absolute inset-0 flex flex-col items-center justify-center">
          <motion.div
            className="text-4xl font-bold text-white"
            initial={{ opacity: 0, scale: 0.5 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.5, duration: 0.5 }}
          >
            {animatedProgress}%
          </motion.div>
          <p className="text-white/60 text-sm mt-1">{label}</p>
          <p className="text-white/40 text-xs mt-1">Target: {target}%</p>
        </div>
      </div>
      
      {/* Legend */}
      <div className="flex items-center gap-4 mt-6">
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 rounded-full bg-gradient-to-r from-primary-400 to-primary-600" />
          <span className="text-xs text-white/60">Current</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 rounded-full bg-white/20" />
          <span className="text-xs text-white/60">Target</span>
        </div>
      </div>
    </div>
  );
};