/**
 * PerformanceHeatmap Component - Visual Performance Matrix
 * Displays performance data as an interactive heatmap
 */

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import type { GraduateTraineeProgram } from '@/lib/api/graduateTrainee';

interface PerformanceHeatmapProps {
  trainees: GraduateTraineeProgram[];
}

interface HeatmapCell {
  traineeId: number;
  traineeName: string;
  category: string;
  value: number;
  label: string;
}

export const PerformanceHeatmap: React.FC<PerformanceHeatmapProps> = ({ trainees }) => {
  const [hoveredCell, setHoveredCell] = useState<HeatmapCell | null>(null);

  // Performance categories
  const categories = [
    'Technical Skills',
    'Communication',
    'Leadership',
    'Problem Solving',
    'Teamwork',
    'Time Management'
  ];

  // Generate heatmap data
  const generateHeatmapData = (): HeatmapCell[] => {
    const data: HeatmapCell[] = [];
    
    // Limit to first 8 trainees for visualization
    const displayTrainees = trainees.slice(0, 8);
    
    displayTrainees.forEach((trainee) => {
      categories.forEach((category) => {
        // Simulate performance scores
        const baseScore = trainee.currentProgress || 50;
        const variance = Math.random() * 30 - 15;
        const value = Math.max(0, Math.min(100, baseScore + variance));
        
        data.push({
          traineeId: trainee.traineeId,
          traineeName: trainee.employeeName,
          category,
          value: Math.round(value),
          label: `${Math.round(value)}%`
        });
      });
    });
    
    return data;
  };

  const heatmapData = generateHeatmapData();
  const displayTrainees = trainees.slice(0, 8);

  // Get color based on value
  const getColor = (value: number) => {
    if (value >= 80) return 'bg-green-500';
    if (value >= 60) return 'bg-yellow-500';
    if (value >= 40) return 'bg-orange-500';
    return 'bg-red-500';
  };

  const getOpacity = (value: number) => {
    return 0.2 + (value / 100) * 0.8;
  };

  return (
    <div className="relative">
      {/* Heatmap Grid */}
      <div className="overflow-x-auto custom-scrollbar">
        <div className="min-w-[600px]">
          {/* Categories Header */}
          <div className="grid grid-cols-7 gap-2 mb-2">
            <div /> {/* Empty cell for trainee names */}
            {categories.map((category, index) => (
              <motion.div
                key={category}
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.05 }}
                className="text-xs text-white/60 text-center truncate"
              >
                {category}
              </motion.div>
            ))}
          </div>

          {/* Heatmap Rows */}
          {displayTrainees.map((trainee, rowIndex) => (
            <motion.div
              key={trainee.traineeId}
              className="grid grid-cols-7 gap-2 mb-2"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: rowIndex * 0.1 }}
            >
              {/* Trainee Name */}
              <div className="text-xs text-white/80 truncate pr-2 flex items-center">
                {trainee.employeeName}
              </div>

              {/* Performance Cells */}
              {categories.map((category, colIndex) => {
                const cell = heatmapData.find(
                  d => d.traineeId === trainee.traineeId && d.category === category
                );
                
                if (!cell) return null;

                return (
                  <motion.div
                    key={`${trainee.traineeId}-${category}`}
                    className={cn(
                      'relative rounded-lg aspect-square flex items-center justify-center',
                      'cursor-pointer transition-all duration-300',
                      'hover:scale-110 hover:z-10',
                      getColor(cell.value)
                    )}
                    style={{ opacity: getOpacity(cell.value) }}
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ 
                      delay: (rowIndex * categories.length + colIndex) * 0.02,
                      type: 'spring',
                      stiffness: 300
                    }}
                    whileHover={{ scale: 1.1 }}
                    onMouseEnter={() => setHoveredCell(cell)}
                    onMouseLeave={() => setHoveredCell(null)}
                  >
                    <span className="text-xs font-medium text-white">
                      {cell.value}
                    </span>
                  </motion.div>
                );
              })}
            </motion.div>
          ))}
        </div>
      </div>

      {/* Legend */}
      <div className="flex items-center justify-center gap-4 mt-6 pt-4 border-t border-white/10">
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-red-500 rounded opacity-60" />
          <span className="text-xs text-white/60">Low (0-40%)</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-orange-500 rounded opacity-70" />
          <span className="text-xs text-white/60">Medium (40-60%)</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-yellow-500 rounded opacity-80" />
          <span className="text-xs text-white/60">Good (60-80%)</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-green-500 rounded opacity-90" />
          <span className="text-xs text-white/60">Excellent (80-100%)</span>
        </div>
      </div>

      {/* Hover Tooltip */}
      {hoveredCell && (
        <motion.div
          className="absolute top-0 right-0 bg-gray-900/95 backdrop-blur-xl p-3 rounded-lg border border-white/20 shadow-glass-xl z-20"
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.9 }}
        >
          <p className="text-white font-medium text-sm">{hoveredCell.traineeName}</p>
          <p className="text-white/60 text-xs mt-1">{hoveredCell.category}</p>
          <p className="text-2xl font-bold text-white mt-2">{hoveredCell.value}%</p>
        </motion.div>
      )}
    </div>
  );
};