/**
 * InteractiveChart Component - Advanced Data Visualization
 * Interactive chart with multiple metrics and animations
 */

import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
  ReferenceLine,
} from 'recharts';
import { cn } from '@/lib/utils';
import type { GraduateTraineeProgram } from '@/lib/api/graduateTrainee';

interface InteractiveChartProps {
  data: GraduateTraineeProgram[];
  metric: 'performance' | 'progress' | 'engagement';
  timeRange: 'week' | 'month' | 'quarter' | 'year';
  chartType?: 'line' | 'area' | 'bar';
}

export const InteractiveChart: React.FC<InteractiveChartProps> = ({
  data,
  metric,
  timeRange,
  chartType = 'area'
}) => {
  const [activeIndex, setActiveIndex] = useState<number | null>(null);

  // Generate chart data based on metric and time range
  const chartData = useMemo(() => {
    const periods = {
      week: 7,
      month: 30,
      quarter: 90,
      year: 365
    };

    const days = periods[timeRange];
    const dataPoints = Math.min(days, 12); // Limit data points for readability

    return Array.from({ length: dataPoints }, (_, index) => {
      const date = new Date();
      date.setDate(date.getDate() - (days - (index * (days / dataPoints))));
      
      // Simulate data based on metric
      const baseValue = metric === 'performance' ? 75 : metric === 'progress' ? 60 : 85;
      const variance = Math.random() * 20 - 10;
      const trend = index * (metric === 'progress' ? 2 : 1);
      
      return {
        date: date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
        current: Math.round(baseValue + variance + trend),
        target: Math.round(baseValue + 10),
        previous: Math.round(baseValue + variance - 5),
      };
    });
  }, [metric, timeRange]);

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="bg-gray-900/95 backdrop-blur-xl p-3 rounded-lg border border-white/20 shadow-glass-lg"
        >
          <p className="text-white font-medium mb-2">{label}</p>
          {payload.map((entry: any, index: number) => (
            <div key={index} className="flex items-center gap-2 text-sm">
              <div className={cn('w-3 h-3 rounded-full', entry.color)} style={{ backgroundColor: entry.color }} />
              <span className="text-white/60">{entry.name}:</span>
              <span className="text-white font-medium">{entry.value}%</span>
            </div>
          ))}
        </motion.div>
      );
    }
    return null;
  };

  const renderChart = () => {
    const commonProps = {
      data: chartData,
      margin: { top: 5, right: 5, left: 5, bottom: 5 },
      onMouseMove: (state: any) => {
        if (state?.activeTooltipIndex !== undefined) {
          setActiveIndex(state.activeTooltipIndex);
        }
      },
      onMouseLeave: () => setActiveIndex(null),
    };

    const axisProps = {
      stroke: 'rgba(255, 255, 255, 0.2)',
      fontSize: 12,
      fontFamily: 'Inter, system-ui, sans-serif',
    };

    switch (chartType) {
      case 'line':
        return (
          <ResponsiveContainer width="100%" height={300}>
            <LineChart {...commonProps}>
              <defs>
                <linearGradient id="colorCurrent" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#6366F1" stopOpacity={0.8}/>
                  <stop offset="95%" stopColor="#6366F1" stopOpacity={0.1}/>
                </linearGradient>
              </defs>
              <CartesianGrid strokeDasharray="3 3" stroke="rgba(255, 255, 255, 0.05)" />
              <XAxis dataKey="date" {...axisProps} />
              <YAxis {...axisProps} domain={[0, 100]} />
              <Tooltip content={<CustomTooltip />} />
              <Legend 
                verticalAlign="top" 
                height={36}
                iconType="line"
                wrapperStyle={{ color: 'rgba(255, 255, 255, 0.6)' }}
              />
              <ReferenceLine y={85} stroke="rgba(16, 185, 129, 0.5)" strokeDasharray="5 5" />
              <Line
                type="monotone"
                dataKey="current"
                stroke="#6366F1"
                strokeWidth={3}
                dot={{ fill: '#6366F1', strokeWidth: 2, r: activeIndex !== null ? 6 : 4 }}
                activeDot={{ r: 8 }}
                name="Current"
              />
              <Line
                type="monotone"
                dataKey="target"
                stroke="#10B981"
                strokeWidth={2}
                strokeDasharray="5 5"
                dot={false}
                name="Target"
              />
            </LineChart>
          </ResponsiveContainer>
        );

      case 'bar':
        return (
          <ResponsiveContainer width="100%" height={300}>
            <BarChart {...commonProps}>
              <defs>
                <linearGradient id="colorBar" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#EC48D5" stopOpacity={0.8}/>
                  <stop offset="95%" stopColor="#EC48D5" stopOpacity={0.3}/>
                </linearGradient>
              </defs>
              <CartesianGrid strokeDasharray="3 3" stroke="rgba(255, 255, 255, 0.05)" />
              <XAxis dataKey="date" {...axisProps} />
              <YAxis {...axisProps} domain={[0, 100]} />
              <Tooltip content={<CustomTooltip />} />
              <Legend 
                verticalAlign="top" 
                height={36}
                wrapperStyle={{ color: 'rgba(255, 255, 255, 0.6)' }}
              />
              <Bar
                dataKey="current"
                fill="url(#colorBar)"
                radius={[8, 8, 0, 0]}
                name="Current"
              />
              <Bar
                dataKey="previous"
                fill="rgba(255, 255, 255, 0.2)"
                radius={[8, 8, 0, 0]}
                name="Previous"
              />
            </BarChart>
          </ResponsiveContainer>
        );

      case 'area':
      default:
        return (
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart {...commonProps}>
              <defs>
                <linearGradient id="colorArea" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#6366F1" stopOpacity={0.5}/>
                  <stop offset="95%" stopColor="#6366F1" stopOpacity={0}/>
                </linearGradient>
                <linearGradient id="colorPrevious" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#EC48D5" stopOpacity={0.3}/>
                  <stop offset="95%" stopColor="#EC48D5" stopOpacity={0}/>
                </linearGradient>
              </defs>
              <CartesianGrid strokeDasharray="3 3" stroke="rgba(255, 255, 255, 0.05)" />
              <XAxis dataKey="date" {...axisProps} />
              <YAxis {...axisProps} domain={[0, 100]} />
              <Tooltip content={<CustomTooltip />} />
              <Legend 
                verticalAlign="top" 
                height={36}
                wrapperStyle={{ color: 'rgba(255, 255, 255, 0.6)' }}
              />
              <ReferenceLine y={85} stroke="rgba(16, 185, 129, 0.5)" strokeDasharray="5 5" />
              <Area
                type="monotone"
                dataKey="previous"
                stroke="#EC48D5"
                strokeWidth={1}
                fill="url(#colorPrevious)"
                name="Previous"
              />
              <Area
                type="monotone"
                dataKey="current"
                stroke="#6366F1"
                strokeWidth={2}
                fill="url(#colorArea)"
                name="Current"
                activeDot={{ r: 6 }}
              />
            </AreaChart>
          </ResponsiveContainer>
        );
    }
  };

  return (
    <div className="w-full">
      {renderChart()}
    </div>
  );
};