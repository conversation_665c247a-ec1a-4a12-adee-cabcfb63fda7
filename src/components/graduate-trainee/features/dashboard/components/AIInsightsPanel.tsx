/**
 * AIInsightsPanel Component - AI-Powered Insights
 * Displays intelligent recommendations and predictions
 */

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Brain, 
  TrendingUp, 
  AlertTriangle, 
  Lightbulb,
  ChevronRight,
  Sparkles,
  Target,
  Users,
  Clock
} from 'lucide-react';
import { GlassCard } from '../../../core/design-system/components/GlassCard';
import { GlassButton } from '../../../core/design-system/components/GlassButton';
import { cn } from '@/lib/utils';
import type { GraduateTraineeProgram } from '@/lib/api/graduateTrainee';

interface AIInsight {
  id: string;
  type: 'recommendation' | 'warning' | 'opportunity' | 'trend';
  priority: 'high' | 'medium' | 'low';
  title: string;
  description: string;
  impact: string;
  action?: string;
  icon: React.ReactNode;
  color: string;
}

interface AIInsightsPanelProps {
  trainees: GraduateTraineeProgram[];
}

export const AIInsightsPanel: React.FC<AIInsightsPanelProps> = ({ trainees }) => {
  const [insights, setInsights] = useState<AIInsight[]>([]);
  const [selectedInsight, setSelectedInsight] = useState<AIInsight | null>(null);
  const [isProcessing, setIsProcessing] = useState(true);

  useEffect(() => {
    // Simulate AI processing
    const timer = setTimeout(() => {
      setInsights(generateInsights(trainees));
      setIsProcessing(false);
    }, 1500);

    return () => clearTimeout(timer);
  }, [trainees]);

  const generateInsights = (trainees: GraduateTraineeProgram[]): AIInsight[] => {
    const insights: AIInsight[] = [];
    
    // Performance Analysis
    const avgProgress = trainees.reduce((acc, t) => acc + (t.currentProgress || 0), 0) / (trainees.length || 1);
    if (avgProgress < 50) {
      insights.push({
        id: '1',
        type: 'warning',
        priority: 'high',
        title: 'Low Overall Progress Detected',
        description: `Average progress is at ${Math.round(avgProgress)}%, which is below the expected threshold.`,
        impact: 'May affect program completion timeline',
        action: 'Review individual trainee progress and provide additional support',
        icon: <AlertTriangle className="w-5 h-5" />,
        color: 'text-red-400'
      });
    }

    // Trend Analysis
    const activeCount = trainees.filter(t => t.status === 'active').length;
    if (activeCount > trainees.length * 0.7) {
      insights.push({
        id: '2',
        type: 'trend',
        priority: 'medium',
        title: 'High Engagement Rate',
        description: `${Math.round((activeCount / trainees.length) * 100)}% of trainees are actively engaged.`,
        impact: 'Positive indicator for program success',
        icon: <TrendingUp className="w-5 h-5" />,
        color: 'text-green-400'
      });
    }

    // Opportunity Detection
    const highPerformers = trainees.filter(t => (t.currentProgress || 0) > 80);
    if (highPerformers.length > 0) {
      insights.push({
        id: '3',
        type: 'opportunity',
        priority: 'medium',
        title: `${highPerformers.length} High Performers Identified`,
        description: 'These trainees show exceptional progress and could mentor others.',
        impact: 'Potential for peer learning and leadership development',
        action: 'Consider pairing with struggling trainees',
        icon: <Lightbulb className="w-5 h-5" />,
        color: 'text-yellow-400'
      });
    }

    // Predictive Recommendation
    insights.push({
      id: '4',
      type: 'recommendation',
      priority: 'low',
      title: 'Optimize Review Schedule',
      description: 'AI suggests adjusting quarterly review timing based on progress patterns.',
      impact: 'Could improve feedback effectiveness by 23%',
      action: 'View suggested schedule',
      icon: <Brain className="w-5 h-5" />,
      color: 'text-blue-400'
    });

    return insights;
  };

  const priorityColors = {
    high: 'bg-red-500/20 border-red-500/30',
    medium: 'bg-yellow-500/20 border-yellow-500/30',
    low: 'bg-blue-500/20 border-blue-500/30'
  };

  return (
    <GlassCard variant="elevated" className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="p-2 rounded-xl bg-gradient-to-br from-primary-500/20 to-accent-500/20">
            <Brain className="w-6 h-6 text-white" />
          </div>
          <div>
            <h3 className="text-xl font-semibold text-white">AI Insights</h3>
            <p className="text-white/60 text-sm">Powered by predictive analytics</p>
          </div>
        </div>
        
        <motion.div
          animate={{ rotate: isProcessing ? 360 : 0 }}
          transition={{ duration: 2, repeat: isProcessing ? Infinity : 0, ease: 'linear' }}
        >
          <Sparkles className={cn(
            'w-5 h-5',
            isProcessing ? 'text-primary-400' : 'text-white/40'
          )} />
        </motion.div>
      </div>

      <AnimatePresence mode="wait">
        {isProcessing ? (
          <motion.div
            key="processing"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="flex flex-col items-center justify-center py-12"
          >
            <div className="relative">
              <motion.div
                className="w-16 h-16 border-4 border-primary-500/20 rounded-full"
                animate={{ rotate: 360 }}
                transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}
              />
              <motion.div
                className="absolute inset-0 w-16 h-16 border-4 border-transparent border-t-primary-500 rounded-full"
                animate={{ rotate: -360 }}
                transition={{ duration: 1.5, repeat: Infinity, ease: 'linear' }}
              />
            </div>
            <p className="text-white/60 mt-4">Analyzing data patterns...</p>
          </motion.div>
        ) : (
          <motion.div
            key="insights"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="space-y-3"
          >
            {insights.map((insight, index) => (
              <motion.div
                key={insight.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ scale: 1.02 }}
                onClick={() => setSelectedInsight(insight)}
                className={cn(
                  'p-4 rounded-xl border backdrop-blur-md cursor-pointer',
                  'transition-all duration-300 hover:bg-white/5',
                  priorityColors[insight.priority]
                )}
              >
                <div className="flex items-start gap-3">
                  <div className={cn('mt-1', insight.color)}>
                    {insight.icon}
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium text-white mb-1">{insight.title}</h4>
                    <p className="text-white/60 text-sm mb-2">{insight.description}</p>
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-white/40">Impact: {insight.impact}</span>
                      {insight.action && (
                        <GlassButton
                          variant="ghost"
                          size="xs"
                          className="gap-1"
                          onClick={(e) => {
                            e.stopPropagation();
                            // Handle action
                          }}
                        >
                          {insight.action}
                          <ChevronRight className="w-3 h-3" />
                        </GlassButton>
                      )}
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Quick Stats */}
      <div className="grid grid-cols-3 gap-3 mt-6 pt-6 border-t border-white/10">
        <div className="text-center">
          <Target className="w-5 h-5 text-primary-400 mx-auto mb-1" />
          <p className="text-2xl font-bold text-white">{insights.filter(i => i.type === 'recommendation').length}</p>
          <p className="text-xs text-white/60">Recommendations</p>
        </div>
        <div className="text-center">
          <AlertTriangle className="w-5 h-5 text-yellow-400 mx-auto mb-1" />
          <p className="text-2xl font-bold text-white">{insights.filter(i => i.priority === 'high').length}</p>
          <p className="text-xs text-white/60">High Priority</p>
        </div>
        <div className="text-center">
          <Clock className="w-5 h-5 text-accent-400 mx-auto mb-1" />
          <p className="text-2xl font-bold text-white">24h</p>
          <p className="text-xs text-white/60">Next Update</p>
        </div>
      </div>
    </GlassCard>
  );
};