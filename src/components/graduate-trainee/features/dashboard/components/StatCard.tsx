/**
 * StatCard Component - Modern Statistics Card
 * Displays key metrics with sparkline and animations
 */

import React from 'react';
import { motion } from 'framer-motion';
import { ArrowUp, ArrowDown, Minus } from 'lucide-react';
import { GlassCard } from '../../../core/design-system/components/GlassCard';
import { cn } from '@/lib/utils';

interface StatCardProps {
  title: string;
  value: string | number;
  change: string;
  trend: 'up' | 'down' | 'stable';
  icon: React.ReactNode;
  color: 'primary' | 'accent' | 'success' | 'warning' | 'error';
  sparklineData: number[];
  delay?: number;
}

export const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  change,
  trend,
  icon,
  color,
  sparklineData,
  delay = 0,
}) => {
  const colorMap = {
    primary: 'from-primary-400 to-primary-600',
    accent: 'from-accent-400 to-accent-600',
    success: 'from-green-400 to-green-600',
    warning: 'from-yellow-400 to-yellow-600',
    error: 'from-red-400 to-red-600',
  };

  const trendIcon = {
    up: <ArrowUp className="w-4 h-4" />,
    down: <ArrowDown className="w-4 h-4" />,
    stable: <Minus className="w-4 h-4" />,
  };

  const trendColor = {
    up: 'text-green-400',
    down: 'text-red-400',
    stable: 'text-yellow-400',
  };

  // Create sparkline path
  const maxValue = Math.max(...sparklineData);
  const minValue = Math.min(...sparklineData);
  const range = maxValue - minValue || 1;
  const width = 100;
  const height = 40;
  
  const points = sparklineData.map((value, index) => {
    const x = (index / (sparklineData.length - 1)) * width;
    const y = height - ((value - minValue) / range) * height;
    return `${x},${y}`;
  }).join(' ');

  const path = `M ${points}`;
  const areaPath = `M 0,${height} ${points} L ${width},${height} Z`;

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5, delay }}
      whileHover={{ scale: 1.02 }}
    >
      <GlassCard variant="elevated" className="p-5 relative overflow-hidden group">
        {/* Background gradient effect */}
        <div className={cn(
          'absolute inset-0 bg-gradient-to-br opacity-5 group-hover:opacity-10 transition-opacity duration-500',
          colorMap[color]
        )} />

        <div className="relative z-10">
          <div className="flex items-start justify-between mb-4">
            <div className={cn('p-2 rounded-xl bg-gradient-to-br', colorMap[color])}>
              <div className="text-white">{icon}</div>
            </div>
            
            <div className={cn('flex items-center gap-1 text-sm font-medium', trendColor[trend])}>
              {trendIcon[trend]}
              <span>{change}</span>
            </div>
          </div>

          <div className="mb-3">
            <p className="text-white/60 text-sm mb-1">{title}</p>
            <motion.p 
              className="text-3xl font-bold text-white"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: delay + 0.2 }}
            >
              {value}
            </motion.p>
          </div>

          {/* Sparkline */}
          <div className="relative h-10 mt-2">
            <svg
              width="100%"
              height="100%"
              viewBox={`0 0 ${width} ${height}`}
              preserveAspectRatio="none"
              className="overflow-visible"
            >
              {/* Area fill */}
              <path
                d={areaPath}
                fill="url(#gradient)"
                opacity="0.1"
              />
              
              {/* Line */}
              <path
                d={path}
                fill="none"
                stroke="url(#gradient)"
                strokeWidth="2"
                vectorEffect="non-scaling-stroke"
              />
              
              {/* Gradient definition */}
              <defs>
                <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                  <stop offset="0%" className={cn('text-primary-400')} stopColor="currentColor" />
                  <stop offset="100%" className={cn('text-accent-400')} stopColor="currentColor" />
                </linearGradient>
              </defs>
              
              {/* Animated dot at the end */}
              <motion.circle
                cx={width}
                cy={height - ((sparklineData[sparklineData.length - 1] - minValue) / range) * height}
                r="3"
                className="fill-white"
                initial={{ scale: 0 }}
                animate={{ scale: [1, 1.5, 1] }}
                transition={{ 
                  duration: 2,
                  repeat: Infinity,
                  delay: delay + 0.5
                }}
              />
            </svg>
          </div>
        </div>
      </GlassCard>
    </motion.div>
  );
};