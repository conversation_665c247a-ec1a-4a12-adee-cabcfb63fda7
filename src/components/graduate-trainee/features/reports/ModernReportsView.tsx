/**
 * ModernReportsView - 2025 Redesign
 * Advanced reporting with data visualization
 */

import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FileText,
  Download,
  Filter,
  Calendar,
  TrendingUp,
  Users,
  DollarSign,
  Clock,
  PieChart,
  BarChart3,
  LineChart,
  FileSpreadsheet,
  Printer,
  Mail,
  Share2,
  ChevronDown,
  ArrowUp,
  ArrowDown
} from 'lucide-react';
import { GlassCard } from '../../core/design-system/components/GlassCard';
import { GlassButton } from '../../core/design-system/components/GlassButton';
import { GlassInput } from '../../core/design-system/components/GlassInput';
import { InteractiveChart } from '../dashboard/components/InteractiveChart';
import { useTraineeStore } from '../../store/traineeStore';
import { cn } from '@/lib/utils';
import type { GraduateTraineeProgram } from '@/lib/api/graduateTrainee';

type ReportType = 'performance' | 'progress' | 'financial' | 'compliance';
type TimeRange = 'week' | 'month' | 'quarter' | 'year' | 'custom';

const ModernReportsView: React.FC = () => {
  const { trainees } = useTraineeStore();
  const [selectedReport, setSelectedReport] = useState<ReportType>('performance');
  const [timeRange, setTimeRange] = useState<TimeRange>('month');
  const [showFilters, setShowFilters] = useState(false);
  const [exportFormat, setExportFormat] = useState<'pdf' | 'csv' | 'excel'>('pdf');

  // Calculate report metrics
  const reportMetrics = useMemo(() => {
    const total = trainees.length || 1;
    const active = trainees.filter(t => t.status === 'active').length;
    const completed = trainees.filter(t => t.status === 'completed').length;
    const avgProgress = trainees.reduce((acc, t) => acc + (t.currentProgress || 0), 0) / total;
    
    // Calculate costs (simulated)
    const totalCost = trainees.length * 50000; // Example cost per trainee
    const costPerCompletion = completed > 0 ? totalCost / completed : 0;
    
    // Calculate time metrics
    const avgDuration = trainees.reduce((acc, t) => {
      const start = new Date(t.startDate);
      const end = new Date(t.expectedEndDate);
      return acc + (end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24);
    }, 0) / total;

    return {
      total,
      active,
      completed,
      avgProgress: Math.round(avgProgress),
      totalCost,
      costPerCompletion: Math.round(costPerCompletion),
      avgDuration: Math.round(avgDuration),
      completionRate: Math.round((completed / total) * 100),
      retentionRate: Math.round(((total - trainees.filter(t => t.status === 'terminated').length) / total) * 100)
    };
  }, [trainees]);

  const reportTypes = [
    {
      id: 'performance' as ReportType,
      label: 'Performance Report',
      icon: <TrendingUp className="w-5 h-5" />,
      description: 'Track trainee performance and progress metrics'
    },
    {
      id: 'progress' as ReportType,
      label: 'Progress Report',
      icon: <LineChart className="w-5 h-5" />,
      description: 'Monitor program completion and milestones'
    },
    {
      id: 'financial' as ReportType,
      label: 'Financial Report',
      icon: <DollarSign className="w-5 h-5" />,
      description: 'Analyze costs and budget utilization'
    },
    {
      id: 'compliance' as ReportType,
      label: 'Compliance Report',
      icon: <FileSpreadsheet className="w-5 h-5" />,
      description: 'Review compliance and certification status'
    }
  ];

  const MetricCard = ({ 
    title, 
    value, 
    change, 
    icon, 
    trend,
    color = 'primary' 
  }: {
    title: string;
    value: string | number;
    change?: number;
    icon: React.ReactNode;
    trend?: 'up' | 'down' | 'neutral';
    color?: 'primary' | 'accent' | 'success' | 'warning' | 'error';
  }) => {
    const colorMap = {
      primary: 'from-primary-400 to-primary-600',
      accent: 'from-accent-400 to-accent-600',
      success: 'from-green-400 to-green-600',
      warning: 'from-yellow-400 to-yellow-600',
      error: 'from-red-400 to-red-600'
    };

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        whileHover={{ scale: 1.02 }}
      >
        <GlassCard variant="elevated" className="p-5">
          <div className="flex items-start justify-between mb-4">
            <div className={cn('p-2 rounded-xl bg-gradient-to-br', colorMap[color])}>
              <div className="text-white">{icon}</div>
            </div>
            {change !== undefined && (
              <div className={cn(
                'flex items-center gap-1 text-sm font-medium',
                trend === 'up' ? 'text-green-400' : trend === 'down' ? 'text-red-400' : 'text-gray-400'
              )}>
                {trend === 'up' ? <ArrowUp className="w-4 h-4" /> : <ArrowDown className="w-4 h-4" />}
                {Math.abs(change)}%
              </div>
            )}
          </div>
          <p className="text-white/60 text-sm mb-1">{title}</p>
          <p className="text-2xl font-bold text-white">{value}</p>
        </GlassCard>
      </motion.div>
    );
  };

  const handleExport = () => {
    console.log(`Exporting ${selectedReport} report as ${exportFormat}`);
    // Implement export logic
  };

  const handlePrint = () => {
    window.print();
  };

  const handleShare = () => {
    console.log('Sharing report');
    // Implement share logic
  };

  return (
    <div className="h-full overflow-hidden flex flex-col">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-white">Reports & Analytics</h2>
            <p className="text-white/60 mt-1">Generate comprehensive reports and insights</p>
          </div>
          
          <div className="flex items-center gap-3">
            <GlassButton
              variant="ghost"
              size="md"
              icon={<Share2 className="w-4 h-4" />}
              onClick={handleShare}
            />
            <GlassButton
              variant="ghost"
              size="md"
              icon={<Printer className="w-4 h-4" />}
              onClick={handlePrint}
            />
            <GlassButton
              variant="primary"
              size="md"
              icon={<Download className="w-4 h-4" />}
              onClick={handleExport}
            >
              Export Report
            </GlassButton>
          </div>
        </div>
      </div>

      {/* Report Type Selector */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        {reportTypes.map(report => (
          <motion.button
            key={report.id}
            onClick={() => setSelectedReport(report.id)}
            className={cn(
              'text-left transition-all duration-200',
              selectedReport === report.id && 'scale-[0.98]'
            )}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <GlassCard 
              variant={selectedReport === report.id ? 'elevated' : 'flat'}
              className={cn(
                'p-4 h-full',
                selectedReport === report.id && 'border-primary-500/50 bg-primary-500/10'
              )}
            >
              <div className="flex items-start gap-3">
                <div className={cn(
                  'p-2 rounded-lg',
                  selectedReport === report.id 
                    ? 'bg-primary-500/20 text-primary-400' 
                    : 'bg-white/10 text-white/60'
                )}>
                  {report.icon}
                </div>
                <div className="flex-1">
                  <h3 className="font-medium text-white">{report.label}</h3>
                  <p className="text-xs text-white/50 mt-1">{report.description}</p>
                </div>
              </div>
            </GlassCard>
          </motion.button>
        ))}
      </div>

      {/* Time Range and Filters */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="flex bg-white/10 backdrop-blur-md rounded-xl p-1">
            {(['week', 'month', 'quarter', 'year'] as TimeRange[]).map(range => (
              <GlassButton
                key={range}
                variant={timeRange === range ? 'primary' : 'ghost'}
                size="sm"
                onClick={() => setTimeRange(range)}
                className="capitalize"
              >
                {range}
              </GlassButton>
            ))}
          </div>
          
          <GlassButton
            variant={showFilters ? 'primary' : 'ghost'}
            size="md"
            icon={<Filter className="w-4 h-4" />}
            onClick={() => setShowFilters(!showFilters)}
          >
            Filters
          </GlassButton>
        </div>

        <div className="flex items-center gap-3">
          <select
            value={exportFormat}
            onChange={(e) => setExportFormat(e.target.value as typeof exportFormat)}
            className="px-3 py-2 bg-white/10 backdrop-blur-md border border-white/20 rounded-lg text-white text-sm"
          >
            <option value="pdf">PDF</option>
            <option value="csv">CSV</option>
            <option value="excel">Excel</option>
          </select>
        </div>
      </div>

      {/* Report Content */}
      <div className="flex-1 overflow-y-auto custom-scrollbar space-y-6">
        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <MetricCard
            title="Total Trainees"
            value={reportMetrics.total}
            change={12}
            trend="up"
            icon={<Users className="w-5 h-5" />}
            color="primary"
          />
          <MetricCard
            title="Average Progress"
            value={`${reportMetrics.avgProgress}%`}
            change={8}
            trend="up"
            icon={<TrendingUp className="w-5 h-5" />}
            color="success"
          />
          <MetricCard
            title="Completion Rate"
            value={`${reportMetrics.completionRate}%`}
            change={-3}
            trend="down"
            icon={<PieChart className="w-5 h-5" />}
            color="warning"
          />
          <MetricCard
            title="Retention Rate"
            value={`${reportMetrics.retentionRate}%`}
            change={5}
            trend="up"
            icon={<Users className="w-5 h-5" />}
            color="accent"
          />
        </div>

        {/* Main Chart */}
        <GlassCard variant="elevated" className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-semibold text-white">
              {selectedReport === 'performance' && 'Performance Trends'}
              {selectedReport === 'progress' && 'Progress Overview'}
              {selectedReport === 'financial' && 'Financial Analysis'}
              {selectedReport === 'compliance' && 'Compliance Status'}
            </h3>
            <div className="flex items-center gap-2">
              <span className="text-sm text-white/60">View:</span>
              <div className="flex bg-white/10 backdrop-blur-md rounded-lg p-1">
                <button className="px-3 py-1 text-sm text-white rounded">Line</button>
                <button className="px-3 py-1 text-sm text-white/60 hover:text-white">Bar</button>
                <button className="px-3 py-1 text-sm text-white/60 hover:text-white">Area</button>
              </div>
            </div>
          </div>
          <InteractiveChart 
            data={trainees}
            metric="performance"
            timeRange={timeRange === 'custom' ? 'month' : timeRange}
            chartType="area"
          />
        </GlassCard>

        {/* Additional Report Sections */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Department Breakdown */}
          <GlassCard variant="elevated" className="p-6">
            <h3 className="text-lg font-semibold text-white mb-4">Department Breakdown</h3>
            <div className="space-y-3">
              {Array.from(new Set(trainees.map(t => t.department))).slice(0, 5).map(dept => {
                const deptTrainees = trainees.filter(t => t.department === dept);
                const percentage = (deptTrainees.length / trainees.length) * 100;
                
                return (
                  <div key={dept} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 rounded-lg bg-primary-500/20 flex items-center justify-center">
                        <Users className="w-4 h-4 text-primary-400" />
                      </div>
                      <span className="text-sm text-white">{dept}</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="w-32 h-2 bg-white/10 rounded-full overflow-hidden">
                        <motion.div
                          className="h-full bg-gradient-to-r from-primary-400 to-accent-400"
                          initial={{ width: 0 }}
                          animate={{ width: `${percentage}%` }}
                          transition={{ duration: 1, ease: 'easeOut' }}
                        />
                      </div>
                      <span className="text-sm text-white/60 w-12 text-right">
                        {Math.round(percentage)}%
                      </span>
                    </div>
                  </div>
                );
              })}
            </div>
          </GlassCard>

          {/* Recent Activity */}
          <GlassCard variant="elevated" className="p-6">
            <h3 className="text-lg font-semibold text-white mb-4">Report History</h3>
            <div className="space-y-3">
              {[
                { name: 'Q3 Performance Report', date: '2 days ago', type: 'Performance' },
                { name: 'Monthly Progress Review', date: '1 week ago', type: 'Progress' },
                { name: 'Budget Analysis', date: '2 weeks ago', type: 'Financial' },
                { name: 'Compliance Audit', date: '3 weeks ago', type: 'Compliance' },
                { name: 'Q2 Summary Report', date: '1 month ago', type: 'Performance' }
              ].map((report, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="flex items-center justify-between p-3 bg-white/5 rounded-lg hover:bg-white/10 transition-colors cursor-pointer"
                >
                  <div className="flex items-center gap-3">
                    <FileText className="w-4 h-4 text-white/60" />
                    <div>
                      <p className="text-sm text-white">{report.name}</p>
                      <p className="text-xs text-white/40">{report.type}</p>
                    </div>
                  </div>
                  <span className="text-xs text-white/40">{report.date}</span>
                </motion.div>
              ))}
            </div>
          </GlassCard>
        </div>
      </div>
    </div>
  );
};

export default ModernReportsView;