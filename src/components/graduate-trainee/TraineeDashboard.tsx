import React, { useState } from 'react';
import { <PERSON>, CardHeader, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Plus, Search, User } from 'lucide-react';
import { useTraineeData } from '@/hooks/queries/useTraineeQueries';
import { TraineeCard } from './components';
import { TraineeForm } from './forms';
import { TraineeDetailPanel } from './modals';
import { GraduateTraineeProgram } from '@/lib/api/graduateTrainee';

export const TraineeDashboard: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({ department: '', status: '' });
  const [showTraineeForm, setShowTraineeForm] = useState(false);
  const [selectedTrainee, setSelectedTrainee] = useState<GraduateTraineeProgram | null>(null);
  const [showDetailPanel, setShowDetailPanel] = useState(false);
  const { data: trainees = [], isLoading } = useTraineeData();

  const filteredTrainees = trainees.filter(trainee =>
    (trainee.employeeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
     trainee.employeeId.includes(searchTerm)) &&
    (filters.department ? trainee.department === filters.department : true) &&
    (filters.status ? trainee.status === filters.status : true)
  );

  const handleViewTrainee = (trainee: GraduateTraineeProgram) => {
    setSelectedTrainee(trainee);
    setShowDetailPanel(true);
  };

  const handleEditTrainee = (trainee: GraduateTraineeProgram) => {
    setSelectedTrainee(trainee);
    setShowTraineeForm(true);
  };

  const handleCreateTrainee = () => {
    setSelectedTrainee(null);
    setShowTraineeForm(true);
  };

  const handleSaveTrainee = async (data: any) => {
    // Implementation would go here
    console.log('Saving trainee:', data);
    setShowTraineeForm(false);
  };

  const handleCloseDetailPanel = () => {
    setShowDetailPanel(false);
    setSelectedTrainee(null);
  };

  const handleUpdateReview = (review: any) => {
    // Implementation would go here
    console.log('Updating review:', review);
  };

  const handleUpdateSkill = (skill: any) => {
    // Implementation would go here
    console.log('Updating skill:', skill);
  };

  const handleUpdateCertification = (cert: any) => {
    // Implementation would go here
    console.log('Updating certification:', cert);
  };

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold">Graduate Trainee Programs</h1>
          <Button onClick={handleCreateTrainee}>
            <Plus className="h-4 w-4 mr-2" />
            Add Trainee
          </Button>
        </div>
        
        {/* Search and Filters */}
        <div className="flex flex-wrap gap-4">
          <div className="relative flex-1 min-w-64">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search by name or ID"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <div className="flex gap-2">
            <select
              value={filters.department}
              onChange={(e) => setFilters({ ...filters, department: e.target.value })}
              className="px-3 py-2 border rounded"
            >
              <option value="">All Departments</option>
              <option value="Engineering">Engineering</option>
              <option value="Marketing">Marketing</option>
              <option value="Finance">Finance</option>
            </select>
            <select
              value={filters.status}
              onChange={(e) => setFilters({ ...filters, status: e.target.value })}
              className="px-3 py-2 border rounded"
            >
              <option value="">All Statuses</option>
              <option value="not_started">Not Started</option>
              <option value="in_progress">In Progress</option>
              <option value="on_hold">On Hold</option>
              <option value="completed">Completed</option>
              <option value="terminated">Terminated</option>
            </select>
          </div>
        </div>
      </div>

      {/* Trainee Cards Grid */}
      <Card>
        <CardHeader>
          <h2 className="text-xl font-bold">Trainee Programs</h2>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="border rounded-lg p-6 animate-pulse">
                  <div className="flex items-center justify-between mb-4">
                    <div className="w-12 h-12 bg-gray-300 rounded-full"></div>
                    <div className="w-20 h-6 bg-gray-300 rounded"></div>
                  </div>
                  <div className="space-y-3">
                    <div className="w-3/4 h-4 bg-gray-300 rounded"></div>
                    <div className="w-1/2 h-4 bg-gray-300 rounded"></div>
                    <div className="w-2/3 h-4 bg-gray-300 rounded"></div>
                    <div className="w-full h-2 bg-gray-300 rounded mt-4"></div>
                    <div className="flex gap-2 mt-4">
                      <div className="w-20 h-8 bg-gray-300 rounded"></div>
                      <div className="w-20 h-8 bg-gray-300 rounded"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : filteredTrainees.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredTrainees.map(trainee => (
                <TraineeCard
                  key={trainee.id}
                  trainee={trainee}
                  onView={handleViewTrainee}
                  onEdit={handleEditTrainee}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <User className="h-12 w-12 mx-auto" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No trainees found</h3>
              <p className="text-gray-500 mb-4">Get started by adding a new graduate trainee program.</p>
              <Button onClick={handleCreateTrainee}>
                <Plus className="h-4 w-4 mr-2" />
                Add First Trainee
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Trainee Form Modal */}
      {showTraineeForm && (
        <TraineeForm
          trainee={selectedTrainee || undefined}
          onSubmit={handleSaveTrainee}
          onCancel={() => setShowTraineeForm(false)}
        />
      )}

      {/* Trainee Detail Panel */}
      {showDetailPanel && selectedTrainee && (
        <TraineeDetailPanel
          trainee={selectedTrainee}
          onClose={handleCloseDetailPanel}
          onEdit={() => {
            handleCloseDetailPanel();
            handleEditTrainee(selectedTrainee);
          }}
          onUpdateReview={handleUpdateReview}
          onUpdateSkill={handleUpdateSkill}
          onUpdateCertification={handleUpdateCertification}
        />
      )}
    </div>
  );
};