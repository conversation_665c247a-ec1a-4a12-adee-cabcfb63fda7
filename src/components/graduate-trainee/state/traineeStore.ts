/**
 * Graduate Trainee Store - Zustand State Management
 * Single source of truth for all trainee-related state
 */

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import type {
  GraduateTraineeProgram,
  FilterState,
  UIState,
  DialogState,
  TraineeMetrics,
  CacheState,
  TabType,
  ViewMode,
  DialogType
} from '../types/trainee.types';
import { getMockTrainees } from '@/lib/api/graduateTraineeMock';
import { graduateTraineeApi } from '@/lib/api/graduateTrainee';

// Store interface
interface TraineeStore {
  // Data
  trainees: GraduateTraineeProgram[];
  selectedTrainee: GraduateTraineeProgram | null;
  
  // UI State
  ui: UIState;
  
  // Filters
  filters: FilterState;
  searchQuery: string;
  
  // Dialog
  dialog: DialogState;
  
  // Cache
  cache: CacheState;
  
  // Computed values (getters)
  get filteredTrainees(): GraduateTraineeProgram[];
  get metrics(): TraineeMetrics;
  
  // Actions - Data
  fetchTrainees: () => Promise<void>;
  addTrainee: (trainee: Omit<GraduateTraineeProgram, 'id'>) => Promise<void>;
  updateTrainee: (id: number, data: Partial<GraduateTraineeProgram>) => Promise<void>;
  deleteTrainee: (id: number) => Promise<void>;
  selectTrainee: (trainee: GraduateTraineeProgram | null) => void;
  
  // Actions - UI
  setActiveTab: (tab: TabType) => void;
  setViewMode: (mode: ViewMode) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // Actions - Filters
  setSearchQuery: (query: string) => void;
  setFilter: (key: keyof FilterState, value: string) => void;
  setFilters: (filters: Partial<FilterState>) => void;
  resetFilters: () => void;
  
  // Actions - Dialog
  openDialog: (type: DialogType, data?: any) => void;
  closeDialog: () => void;
  
  // Actions - Cache
  invalidateCache: () => void;
  
  // Utils
  reset: () => void;
}

// Initial states
const initialUI: UIState = {
  activeTab: 'dashboard',
  viewMode: 'grid',
  isLoading: false,
  error: null
};

const initialFilters: FilterState = {
  department: 'all',
  status: 'all',
  manager: 'all'
};

const initialDialog: DialogState = {
  isOpen: false,
  type: null,
  data: undefined
};

const initialCache: CacheState = {
  trainees: null,
  metrics: null,
  lastSync: 0
};

// Development mode flag
const USE_MOCK_DATA = process.env.NODE_ENV === 'development';

// Create store with middleware
export const useTraineeStore = create<TraineeStore>()(
  devtools(
    persist(
      immer((set, get) => ({
        // Initial data
        trainees: [],
        selectedTrainee: null,
        ui: initialUI,
        filters: initialFilters,
        searchQuery: '',
        dialog: initialDialog,
        cache: initialCache,
        
        // Computed getters
        get filteredTrainees() {
          const state = get();
          let filtered = [...state.trainees];
          
          // Apply search
          if (state.searchQuery) {
            const query = state.searchQuery.toLowerCase();
            filtered = filtered.filter(t => 
              t.employeeName.toLowerCase().includes(query) ||
              t.employeeId.toLowerCase().includes(query) ||
              t.department.toLowerCase().includes(query)
            );
          }
          
          // Apply filters
          if (state.filters.department !== 'all') {
            filtered = filtered.filter(t => t.department === state.filters.department);
          }
          if (state.filters.status !== 'all') {
            filtered = filtered.filter(t => t.status === state.filters.status);
          }
          if (state.filters.manager !== 'all') {
            filtered = filtered.filter(t => t.manager === state.filters.manager);
          }
          
          return filtered;
        },
        
        get metrics() {
          const state = get();
          const trainees = state.trainees;
          
          if (trainees.length === 0) {
            return {
              total: 0,
              active: 0,
              completed: 0,
              onHold: 0,
              notStarted: 0,
              terminated: 0,
              avgProgress: 0,
              reviewsDue: 0,
              certificationsExpiring: 0,
              budgetUtilization: 0
            };
          }
          
          const metrics: TraineeMetrics = {
            total: trainees.length,
            active: trainees.filter(t => t.status === 'in_progress').length,
            completed: trainees.filter(t => t.status === 'completed').length,
            onHold: trainees.filter(t => t.status === 'on_hold').length,
            notStarted: trainees.filter(t => t.status === 'not_started').length,
            terminated: trainees.filter(t => t.status === 'terminated').length,
            avgProgress: Math.round(
              trainees.reduce((sum, t) => sum + t.overallProgress, 0) / trainees.length
            ),
            reviewsDue: trainees.filter(t => {
              // Check if any review is due
              return t.quarterlyReviews?.some(r => !r.completedDate) || false;
            }).length,
            certificationsExpiring: trainees.reduce((count, t) => {
              // Count expiring certifications (within 30 days)
              const expiringCount = t.certifications?.filter(c => {
                if (!c.expiryDate) return false;
                const expiry = new Date(c.expiryDate);
                const thirtyDaysFromNow = new Date();
                thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
                return expiry <= thirtyDaysFromNow && c.status === 'completed';
              }).length || 0;
              return count + expiringCount;
            }, 0),
            budgetUtilization: trainees.length > 0 ? 
              Math.round(
                (trainees.reduce((sum, t) => sum + t.budgetSpent, 0) /
                trainees.reduce((sum, t) => sum + t.budgetAllocated, 0)) * 100
              ) : 0
          };
          
          return metrics;
        },
        
        // Actions - Data
        fetchTrainees: async () => {
          set(state => {
            state.ui.isLoading = true;
            state.ui.error = null;
          });
          
          try {
            let data: GraduateTraineeProgram[];
            
            if (USE_MOCK_DATA) {
              // Use mock data in development
              await new Promise(resolve => setTimeout(resolve, 500)); // Simulate network delay
              data = getMockTrainees();
            } else {
              // Use real API
              data = await graduateTraineeApi.getAllPrograms();
            }
            
            set(state => {
              state.trainees = data;
              state.ui.isLoading = false;
              state.cache.lastSync = Date.now();
            });
          } catch (error) {
            set(state => {
              state.ui.error = error instanceof Error ? error.message : 'Failed to fetch trainees';
              state.ui.isLoading = false;
            });
          }
        },
        
        addTrainee: async (traineeData) => {
          set(state => { state.ui.isLoading = true; });
          
          try {
            let newTrainee: GraduateTraineeProgram;
            
            if (USE_MOCK_DATA) {
              // Mock implementation
              newTrainee = {
                ...traineeData,
                id: Math.max(...get().trainees.map(t => t.id || 0)) + 1,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
              } as GraduateTraineeProgram;
            } else {
              newTrainee = await graduateTraineeApi.createProgram(traineeData as any);
            }
            
            set(state => {
              state.trainees.push(newTrainee);
              state.ui.isLoading = false;
              state.dialog = initialDialog;
            });
          } catch (error) {
            set(state => {
              state.ui.error = error instanceof Error ? error.message : 'Failed to add trainee';
              state.ui.isLoading = false;
            });
          }
        },
        
        updateTrainee: async (id, data) => {
          set(state => { state.ui.isLoading = true; });
          
          try {
            let updatedTrainee: GraduateTraineeProgram;
            
            if (USE_MOCK_DATA) {
              const index = get().trainees.findIndex(t => t.id === id);
              if (index === -1) throw new Error('Trainee not found');
              updatedTrainee = { ...get().trainees[index], ...data };
            } else {
              updatedTrainee = await graduateTraineeApi.updateProgram({ id, ...data } as any) as GraduateTraineeProgram;
            }
            
            set(state => {
              const index = state.trainees.findIndex(t => t.id === id);
              if (index !== -1) {
                state.trainees[index] = updatedTrainee;
              }
              state.ui.isLoading = false;
              state.dialog = initialDialog;
            });
          } catch (error) {
            set(state => {
              state.ui.error = error instanceof Error ? error.message : 'Failed to update trainee';
              state.ui.isLoading = false;
            });
          }
        },
        
        deleteTrainee: async (id) => {
          set(state => { state.ui.isLoading = true; });
          
          try {
            if (!USE_MOCK_DATA) {
              await graduateTraineeApi.deleteProgram(id);
            }
            
            set(state => {
              state.trainees = state.trainees.filter(t => t.id !== id);
              state.selectedTrainee = null;
              state.ui.isLoading = false;
              state.dialog = initialDialog;
            });
          } catch (error) {
            set(state => {
              state.ui.error = error instanceof Error ? error.message : 'Failed to delete trainee';
              state.ui.isLoading = false;
            });
          }
        },
        
        selectTrainee: (trainee) => set(state => { 
          state.selectedTrainee = trainee; 
        }),
        
        // Actions - UI
        setActiveTab: (tab) => set(state => { 
          state.ui.activeTab = tab; 
        }),
        
        setViewMode: (mode) => set(state => { 
          state.ui.viewMode = mode; 
        }),
        
        setLoading: (loading) => set(state => { 
          state.ui.isLoading = loading; 
        }),
        
        setError: (error) => set(state => { 
          state.ui.error = error; 
        }),
        
        // Actions - Filters
        setSearchQuery: (query) => set(state => { 
          state.searchQuery = query; 
        }),
        
        setFilter: (key, value) => set(state => { 
          state.filters[key] = value; 
        }),
        
        setFilters: (filters) => set(state => { 
          state.filters = { ...state.filters, ...filters }; 
        }),
        
        resetFilters: () => set(state => { 
          state.filters = initialFilters;
          state.searchQuery = '';
        }),
        
        // Actions - Dialog
        openDialog: (type, data) => set(state => {
          state.dialog = {
            isOpen: true,
            type,
            data
          };
        }),
        
        closeDialog: () => set(state => { 
          state.dialog = initialDialog;
        }),
        
        // Actions - Cache
        invalidateCache: () => set(state => {
          state.cache = initialCache;
        }),
        
        // Reset store
        reset: () => set(state => {
          state.trainees = [];
          state.selectedTrainee = null;
          state.ui = initialUI;
          state.filters = initialFilters;
          state.searchQuery = '';
          state.dialog = initialDialog;
          state.cache = initialCache;
        })
      })),
      {
        name: 'trainee-store',
        partialize: (state) => ({
          filters: state.filters,
          ui: {
            activeTab: state.ui.activeTab,
            viewMode: state.ui.viewMode
          }
        })
      }
    ),
    {
      name: 'TraineeStore'
    }
  )
);

// Selector hooks for better performance
export const useFilteredTrainees = () => useTraineeStore((state) => state.filteredTrainees);
export const useTraineeMetrics = () => useTraineeStore((state) => state.metrics);
export const useSelectedTrainee = () => useTraineeStore((state) => state.selectedTrainee);
export const useTraineeDialog = () => useTraineeStore((state) => state.dialog);
export const useTraineeUI = () => useTraineeStore((state) => state.ui);