import React, { useMemo } from 'react';
import {
  FileText,
  Download,
  Filter,
  Calendar,
  TrendingUp,
  Users,
  DollarSign,
  Clock,
  <PERSON><PERSON>hart,
  BarChart3,
  <PERSON><PERSON>hart,
  FileSpreadsheet
} from 'lucide-react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import Container from '../components/Container';
import type { GraduateTraineeProgram } from '@/lib/api/graduateTrainee';

interface ReportsTabProps {
  trainees: GraduateTraineeProgram[];
}

// Report Summary Card
const ReportSummaryCard: React.FC<{
  title: string;
  value: string | number;
  change?: number;
  icon: React.ReactNode;
  trend?: 'up' | 'down' | 'neutral';
}> = ({ title, value, change, icon, trend }) => {
  const trendColor = trend === 'up' ? 'text-green-500' : 
                     trend === 'down' ? 'text-red-500' : 'text-gray-500';
  
  return (
    <Card className="p-4">
      <div className="flex items-start justify-between">
        <div className="space-y-1">
          <p className="text-sm text-muted-foreground">{title}</p>
          <p className="text-2xl font-bold">{value}</p>
          {change !== undefined && (
            <p className={`text-xs ${trendColor} font-medium`}>
              {trend === 'up' && '+'}
              {change}% from last month
            </p>
          )}
        </div>
        <div className="text-muted-foreground">{icon}</div>
      </div>
    </Card>
  );
};

// Budget Report
const BudgetReport: React.FC<{ trainees: GraduateTraineeProgram[] }> = ({ trainees }) => {
  const budgetStats = useMemo(() => {
    const totalAllocated = trainees.reduce((sum, t) => sum + t.budgetAllocated, 0);
    const totalSpent = trainees.reduce((sum, t) => sum + t.budgetSpent, 0);
    const utilization = (totalSpent / totalAllocated) * 100;
    const avgPerTrainee = totalSpent / trainees.length;
    
    const departmentBudgets = trainees.reduce((acc, t) => {
      if (!acc[t.department]) {
        acc[t.department] = { allocated: 0, spent: 0 };
      }
      acc[t.department].allocated += t.budgetAllocated;
      acc[t.department].spent += t.budgetSpent;
      return acc;
    }, {} as Record<string, { allocated: number; spent: number }>);

    return { totalAllocated, totalSpent, utilization, avgPerTrainee, departmentBudgets };
  }, [trainees]);

  return (
    <Card className="p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold">Budget Analysis</h3>
        <DollarSign className="h-5 w-5 text-muted-foreground" />
      </div>

      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <div>
          <p className="text-sm text-muted-foreground">Total Allocated</p>
          <p className="text-lg font-bold">${budgetStats.totalAllocated.toLocaleString()}</p>
        </div>
        <div>
          <p className="text-sm text-muted-foreground">Total Spent</p>
          <p className="text-lg font-bold">${budgetStats.totalSpent.toLocaleString()}</p>
        </div>
        <div>
          <p className="text-sm text-muted-foreground">Utilization</p>
          <p className="text-lg font-bold">{budgetStats.utilization.toFixed(1)}%</p>
        </div>
        <div>
          <p className="text-sm text-muted-foreground">Avg/Trainee</p>
          <p className="text-lg font-bold">${budgetStats.avgPerTrainee.toLocaleString()}</p>
        </div>
      </div>

      <div className="space-y-3">
        <h4 className="text-sm font-medium">Department Breakdown</h4>
        {Object.entries(budgetStats.departmentBudgets).map(([dept, budget]) => {
          const utilization = (budget.spent / budget.allocated) * 100;
          return (
            <div key={dept} className="space-y-1">
              <div className="flex justify-between text-sm">
                <span>{dept}</span>
                <span className="text-muted-foreground">
                  ${budget.spent.toLocaleString()} / ${budget.allocated.toLocaleString()}
                </span>
              </div>
              <div className="h-2 bg-muted rounded-full overflow-hidden">
                <div
                  className="h-full bg-primary transition-all"
                  style={{ width: `${Math.min(100, utilization)}%` }}
                />
              </div>
            </div>
          );
        })}
      </div>
    </Card>
  );
};

// Performance Report
const PerformanceReport: React.FC<{ trainees: GraduateTraineeProgram[] }> = ({ trainees }) => {
  const performanceStats = useMemo(() => {
    const byStatus = trainees.reduce((acc, t) => {
      acc[t.status] = (acc[t.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const completionRate = (byStatus.completed || 0) / trainees.length * 100;
    const dropoutRate = (byStatus.terminated || 0) / trainees.length * 100;
    
    const avgTrainingHours = trainees.reduce((sum, t) => 
      sum + (t.trainingHoursCompleted / t.trainingHoursRequired * 100), 0
    ) / trainees.length;

    return { byStatus, completionRate, dropoutRate, avgTrainingHours };
  }, [trainees]);

  return (
    <Card className="p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold">Performance Metrics</h3>
        <BarChart3 className="h-5 w-5 text-muted-foreground" />
      </div>

      <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-6">
        <div>
          <p className="text-sm text-muted-foreground">Completion Rate</p>
          <p className="text-lg font-bold text-green-600">
            {performanceStats.completionRate.toFixed(1)}%
          </p>
        </div>
        <div>
          <p className="text-sm text-muted-foreground">Dropout Rate</p>
          <p className="text-lg font-bold text-red-600">
            {performanceStats.dropoutRate.toFixed(1)}%
          </p>
        </div>
        <div>
          <p className="text-sm text-muted-foreground">Avg Training Progress</p>
          <p className="text-lg font-bold text-blue-600">
            {performanceStats.avgTrainingHours.toFixed(1)}%
          </p>
        </div>
      </div>

      <div className="space-y-3">
        <h4 className="text-sm font-medium">Status Distribution</h4>
        <div className="grid grid-cols-2 gap-2">
          {Object.entries(performanceStats.byStatus).map(([status, count]) => (
            <div key={status} className="flex justify-between p-2 bg-muted/30 rounded">
              <span className="text-sm capitalize">{status.replace('_', ' ')}</span>
              <span className="text-sm font-medium">{count}</span>
            </div>
          ))}
        </div>
      </div>
    </Card>
  );
};

// Quick Actions
const QuickActions: React.FC = () => {
  const handleExport = (format: string) => {
    // Implement export logic
    console.log(`Exporting as ${format}`);
  };

  return (
    <Card className="p-4">
      <h3 className="text-sm font-semibold mb-3">Quick Actions</h3>
      <div className="space-y-2">
        <Button
          variant="outline"
          size="sm"
          className="w-full justify-start"
          onClick={() => handleExport('pdf')}
        >
          <FileText className="h-4 w-4 mr-2" />
          Export as PDF
        </Button>
        <Button
          variant="outline"
          size="sm"
          className="w-full justify-start"
          onClick={() => handleExport('excel')}
        >
          <FileSpreadsheet className="h-4 w-4 mr-2" />
          Export as Excel
        </Button>
        <Button
          variant="outline"
          size="sm"
          className="w-full justify-start"
          onClick={() => handleExport('csv')}
        >
          <Download className="h-4 w-4 mr-2" />
          Export as CSV
        </Button>
      </div>
    </Card>
  );
};

export const ReportsTab: React.FC<ReportsTabProps> = ({ trainees = [] }) => {
  const [timeRange, setTimeRange] = React.useState('month');
  const [department, setDepartment] = React.useState('all');

  const departments = useMemo(() => {
    if (!trainees || trainees.length === 0) return ['all'];
    return ['all', ...new Set(trainees.map(t => t.department))];
  }, [trainees]);

  const filteredTrainees = useMemo(() => {
    if (!trainees) return [];
    if (department === 'all') return trainees;
    return trainees.filter(t => t.department === department);
  }, [trainees, department]);

  // Calculate summary stats
  const summaryStats = useMemo(() => {
    const active = filteredTrainees.filter(t => t.status === 'in_progress').length;
    const completed = filteredTrainees.filter(t => t.status === 'completed').length;
    const totalBudget = filteredTrainees.reduce((sum, t) => sum + t.budgetAllocated, 0);
    const totalHours = filteredTrainees.reduce((sum, t) => sum + t.trainingHoursRequired, 0);

    return { active, completed, totalBudget, totalHours };
  }, [filteredTrainees]);

  return (
    <Container padding="lg" className="space-y-6 overflow-y-auto custom-scrollbar">
      {/* Filters */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="week">This Week</SelectItem>
              <SelectItem value="month">This Month</SelectItem>
              <SelectItem value="quarter">This Quarter</SelectItem>
              <SelectItem value="year">This Year</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={department} onValueChange={setDepartment}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {departments.map(dept => (
                <SelectItem key={dept} value={dept}>
                  {dept === 'all' ? 'All Departments' : dept}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <Button size="sm" variant="outline">
          <Calendar className="h-4 w-4 mr-2" />
          Custom Range
        </Button>
      </div>

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <ReportSummaryCard
          title="Active Trainees"
          value={summaryStats.active}
          change={12}
          trend="up"
          icon={<Users className="h-5 w-5" />}
        />
        <ReportSummaryCard
          title="Completed Programs"
          value={summaryStats.completed}
          change={8}
          trend="up"
          icon={<TrendingUp className="h-5 w-5" />}
        />
        <ReportSummaryCard
          title="Total Budget"
          value={`$${summaryStats.totalBudget.toLocaleString()}`}
          change={-5}
          trend="down"
          icon={<DollarSign className="h-5 w-5" />}
        />
        <ReportSummaryCard
          title="Training Hours"
          value={summaryStats.totalHours.toLocaleString()}
          change={15}
          trend="up"
          icon={<Clock className="h-5 w-5" />}
        />
      </div>

      {/* Detailed Reports */}
      <Tabs defaultValue="budget" className="w-full">
        <TabsList>
          <TabsTrigger value="budget">Budget</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="trends">Trends</TabsTrigger>
        </TabsList>

        <TabsContent value="budget" className="mt-4">
          <div className="grid gap-4 lg:grid-cols-3">
            <div className="lg:col-span-2">
              <BudgetReport trainees={filteredTrainees} />
            </div>
            <div>
              <QuickActions />
            </div>
          </div>
        </TabsContent>

        <TabsContent value="performance" className="mt-4">
          <PerformanceReport trainees={filteredTrainees} />
        </TabsContent>

        <TabsContent value="trends" className="mt-4">
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Trend Analysis</h3>
            <div className="flex items-center justify-center h-64 text-muted-foreground">
              <LineChart className="h-12 w-12" />
              <p className="ml-4">Interactive charts will be displayed here</p>
            </div>
          </Card>
        </TabsContent>
      </Tabs>
    </Container>
  );
};

export default ReportsTab;