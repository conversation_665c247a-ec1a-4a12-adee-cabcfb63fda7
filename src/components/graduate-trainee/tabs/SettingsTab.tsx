import React, { useState } from 'react';
import {
  Settings,
  Bell,
  Shield,
  Database,
  Users,
  Calendar,
  Mail,
  Globe,
  Save,
  AlertCircle,
  Check,
  ChevronRight,
  ToggleLeft,
  ToggleRight,
  Key,
  FileText,
  Palette,
  Zap
} from 'lucide-react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import Container from '../components/Container';
import { useToast } from '@/components/ui/use-toast';

interface SettingsTabProps {
  // Add any props needed
}

// Settings Section Component
const SettingsSection: React.FC<{
  title: string;
  description?: string;
  icon: React.ReactNode;
  children: React.ReactNode;
}> = ({ title, description, icon, children }) => {
  return (
    <Card className="p-6">
      <div className="flex items-start gap-4">
        <div className="text-muted-foreground">{icon}</div>
        <div className="flex-1 space-y-4">
          <div>
            <h3 className="text-lg font-semibold">{title}</h3>
            {description && (
              <p className="text-sm text-muted-foreground mt-1">{description}</p>
            )}
          </div>
          {children}
        </div>
      </div>
    </Card>
  );
};

// General Settings
const GeneralSettings: React.FC = () => {
  const [programDuration, setProgramDuration] = useState('18');
  const [reviewFrequency, setReviewFrequency] = useState('quarterly');
  const [autoArchive, setAutoArchive] = useState(true);
  const { toast } = useToast();

  const handleSave = () => {
    toast({
      title: "Settings saved",
      description: "General settings have been updated successfully.",
    });
  };

  return (
    <div className="space-y-4">
      <SettingsSection
        title="Program Defaults"
        description="Default settings for new graduate trainee programs"
        icon={<Calendar className="h-5 w-5" />}
      >
        <div className="space-y-4">
          <div className="grid gap-2">
            <Label htmlFor="duration">Default Program Duration (months)</Label>
            <Input
              id="duration"
              type="number"
              value={programDuration}
              onChange={(e) => setProgramDuration(e.target.value)}
              min="6"
              max="36"
            />
          </div>
          
          <div className="grid gap-2">
            <Label htmlFor="review">Review Frequency</Label>
            <Select value={reviewFrequency} onValueChange={setReviewFrequency}>
              <SelectTrigger id="review">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="monthly">Monthly</SelectItem>
                <SelectItem value="quarterly">Quarterly</SelectItem>
                <SelectItem value="biannual">Bi-annual</SelectItem>
                <SelectItem value="annual">Annual</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="archive">Auto-archive completed programs</Label>
              <p className="text-xs text-muted-foreground">
                Automatically archive programs 30 days after completion
              </p>
            </div>
            <Switch
              id="archive"
              checked={autoArchive}
              onCheckedChange={setAutoArchive}
            />
          </div>
        </div>
      </SettingsSection>

      <SettingsSection
        title="Data Management"
        description="Configure how trainee data is stored and managed"
        icon={<Database className="h-5 w-5" />}
      >
        <div className="space-y-4">
          <div className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
            <div>
              <p className="text-sm font-medium">Export Data</p>
              <p className="text-xs text-muted-foreground">Download all trainee data</p>
            </div>
            <Button size="sm" variant="outline">
              Export
            </Button>
          </div>
          
          <div className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
            <div>
              <p className="text-sm font-medium">Import Data</p>
              <p className="text-xs text-muted-foreground">Bulk import trainee records</p>
            </div>
            <Button size="sm" variant="outline">
              Import
            </Button>
          </div>

          <div className="flex items-center justify-between p-3 bg-orange-500/10 rounded-lg border border-orange-500/20">
            <div>
              <p className="text-sm font-medium">Clear All Data</p>
              <p className="text-xs text-muted-foreground">This action cannot be undone</p>
            </div>
            <Button size="sm" variant="destructive">
              Clear Data
            </Button>
          </div>
        </div>
      </SettingsSection>

      <div className="flex justify-end">
        <Button onClick={handleSave}>
          <Save className="h-4 w-4 mr-2" />
          Save Changes
        </Button>
      </div>
    </div>
  );
};

// Notification Settings
const NotificationSettings: React.FC = () => {
  const [emailNotifications, setEmailNotifications] = useState(true);
  const [reviewReminders, setReviewReminders] = useState(true);
  const [completionAlerts, setCompletionAlerts] = useState(true);
  const [budgetAlerts, setBudgetAlerts] = useState(true);
  const { toast } = useToast();

  const handleSave = () => {
    toast({
      title: "Settings saved",
      description: "Notification preferences have been updated.",
    });
  };

  return (
    <div className="space-y-4">
      <SettingsSection
        title="Email Notifications"
        description="Configure which events trigger email notifications"
        icon={<Mail className="h-5 w-5" />}
      >
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Enable email notifications</Label>
              <p className="text-xs text-muted-foreground">
                Receive email updates for important events
              </p>
            </div>
            <Switch
              checked={emailNotifications}
              onCheckedChange={setEmailNotifications}
            />
          </div>

          <Separator />

          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label className="text-sm">Quarterly review reminders</Label>
              <Switch
                checked={reviewReminders}
                onCheckedChange={setReviewReminders}
                disabled={!emailNotifications}
              />
            </div>

            <div className="flex items-center justify-between">
              <Label className="text-sm">Program completion alerts</Label>
              <Switch
                checked={completionAlerts}
                onCheckedChange={setCompletionAlerts}
                disabled={!emailNotifications}
              />
            </div>

            <div className="flex items-center justify-between">
              <Label className="text-sm">Budget threshold alerts</Label>
              <Switch
                checked={budgetAlerts}
                onCheckedChange={setBudgetAlerts}
                disabled={!emailNotifications}
              />
            </div>
          </div>
        </div>
      </SettingsSection>

      <SettingsSection
        title="Notification Timing"
        description="Set when to receive notifications"
        icon={<Bell className="h-5 w-5" />}
      >
        <div className="space-y-4">
          <div className="grid gap-2">
            <Label>Review reminder lead time</Label>
            <Select defaultValue="7">
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="3">3 days before</SelectItem>
                <SelectItem value="7">1 week before</SelectItem>
                <SelectItem value="14">2 weeks before</SelectItem>
                <SelectItem value="30">1 month before</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="grid gap-2">
            <Label>Budget alert threshold</Label>
            <Select defaultValue="80">
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="70">70% utilized</SelectItem>
                <SelectItem value="80">80% utilized</SelectItem>
                <SelectItem value="90">90% utilized</SelectItem>
                <SelectItem value="95">95% utilized</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </SettingsSection>

      <div className="flex justify-end">
        <Button onClick={handleSave}>
          <Save className="h-4 w-4 mr-2" />
          Save Changes
        </Button>
      </div>
    </div>
  );
};

// Access Control Settings
const AccessSettings: React.FC = () => {
  const { toast } = useToast();

  const handleSave = () => {
    toast({
      title: "Settings saved",
      description: "Access control settings have been updated.",
    });
  };

  return (
    <div className="space-y-4">
      <SettingsSection
        title="User Roles"
        description="Manage user roles and permissions"
        icon={<Shield className="h-5 w-5" />}
      >
        <div className="space-y-4">
          <div className="space-y-2">
            <div className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
              <div>
                <p className="text-sm font-medium">Admin</p>
                <p className="text-xs text-muted-foreground">Full access to all features</p>
              </div>
              <Badge className="bg-blue-500/10 text-blue-500">3 users</Badge>
            </div>
            
            <div className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
              <div>
                <p className="text-sm font-medium">Manager</p>
                <p className="text-xs text-muted-foreground">View and edit assigned trainees</p>
              </div>
              <Badge className="bg-green-500/10 text-green-500">12 users</Badge>
            </div>
            
            <div className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
              <div>
                <p className="text-sm font-medium">Viewer</p>
                <p className="text-xs text-muted-foreground">Read-only access</p>
              </div>
              <Badge className="bg-gray-500/10 text-gray-500">25 users</Badge>
            </div>
          </div>

          <Button variant="outline" className="w-full">
            <Users className="h-4 w-4 mr-2" />
            Manage Users
          </Button>
        </div>
      </SettingsSection>

      <SettingsSection
        title="API Access"
        description="Manage API keys and integrations"
        icon={<Key className="h-5 w-5" />}
      >
        <div className="space-y-4">
          <div className="p-3 bg-muted/30 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <p className="text-sm font-medium">API Key</p>
              <Button size="sm" variant="ghost">
                Regenerate
              </Button>
            </div>
            <code className="text-xs bg-background px-2 py-1 rounded">
              sk_live_************************
            </code>
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Enable API access</Label>
              <p className="text-xs text-muted-foreground">
                Allow external applications to access data
              </p>
            </div>
            <Switch defaultChecked />
          </div>
        </div>
      </SettingsSection>

      <div className="flex justify-end">
        <Button onClick={handleSave}>
          <Save className="h-4 w-4 mr-2" />
          Save Changes
        </Button>
      </div>
    </div>
  );
};

// Badge component (simple implementation)
const Badge: React.FC<{ children: React.ReactNode; className?: string }> = ({ 
  children, 
  className = '' 
}) => {
  return (
    <span className={`px-2 py-1 text-xs font-medium rounded-full ${className}`}>
      {children}
    </span>
  );
};

export const SettingsTab: React.FC<SettingsTabProps> = () => {
  return (
    <Container padding="lg" className="overflow-y-auto custom-scrollbar">
      <Tabs defaultValue="general" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
          <TabsTrigger value="access">Access & Security</TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="mt-6">
          <GeneralSettings />
        </TabsContent>

        <TabsContent value="notifications" className="mt-6">
          <NotificationSettings />
        </TabsContent>

        <TabsContent value="access" className="mt-6">
          <AccessSettings />
        </TabsContent>
      </Tabs>
    </Container>
  );
};

export default SettingsTab;