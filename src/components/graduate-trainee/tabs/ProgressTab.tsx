import React, { useMemo } from 'react';
import { 
  TrendingUp, 
  Target, 
  Award, 
  Clock, 
  Calendar,
  CheckCircle,
  AlertCircle,
  BarChart3,
  Users
} from 'lucide-react';
import { Card } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import Container from '../components/Container';
import StatusBadge from '../components/StatusBadge';
import type { GraduateTraineeProgram } from '@/lib/api/graduateTrainee';

interface ProgressTabProps {
  trainees: GraduateTraineeProgram[];
}

// Progress Overview Card
const ProgressOverviewCard: React.FC<{ trainees: GraduateTraineeProgram[] }> = ({ trainees }) => {
  const stats = useMemo(() => {
    const total = trainees.length;
    const avgProgress = trainees.reduce((acc, t) => acc + t.overallProgress, 0) / total || 0;
    const onTrack = trainees.filter(t => t.overallProgress >= 60).length;
    const atRisk = trainees.filter(t => t.overallProgress < 40 && t.status === 'in_progress').length;
    const completed = trainees.filter(t => t.status === 'completed').length;
    
    return { total, avgProgress, onTrack, atRisk, completed };
  }, [trainees]);

  return (
    <Card className="p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold">Progress Overview</h3>
        <TrendingUp className="h-5 w-5 text-muted-foreground" />
      </div>
      
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div>
          <p className="text-sm text-muted-foreground">Average Progress</p>
          <p className="text-2xl font-bold">{Math.round(stats.avgProgress)}%</p>
        </div>
        <div>
          <p className="text-sm text-muted-foreground">On Track</p>
          <p className="text-2xl font-bold text-green-600">{stats.onTrack}</p>
        </div>
        <div>
          <p className="text-sm text-muted-foreground">At Risk</p>
          <p className="text-2xl font-bold text-orange-600">{stats.atRisk}</p>
        </div>
        <div>
          <p className="text-sm text-muted-foreground">Completed</p>
          <p className="text-2xl font-bold text-blue-600">{stats.completed}</p>
        </div>
      </div>
    </Card>
  );
};

// Individual Progress Card
const TraineeProgressCard: React.FC<{ trainee: GraduateTraineeProgram }> = ({ trainee }) => {
  const isOnTrack = trainee.overallProgress >= 60;
  const progressColor = isOnTrack ? 'bg-green-500' : 'bg-orange-500';
  
  // Calculate time progress
  const startDate = new Date(trainee.startDate);
  const endDate = new Date(trainee.expectedEndDate);
  const now = new Date();
  const totalDuration = endDate.getTime() - startDate.getTime();
  const elapsed = now.getTime() - startDate.getTime();
  const timeProgress = Math.min(100, Math.max(0, (elapsed / totalDuration) * 100));

  return (
    <Card className="p-4">
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="font-medium">{trainee.employeeName}</h4>
            <p className="text-sm text-muted-foreground">{trainee.department}</p>
          </div>
          <StatusBadge status={trainee.status} size="sm" />
        </div>

        <div className="space-y-3">
          <div>
            <div className="flex justify-between text-sm mb-1">
              <span>Overall Progress</span>
              <span className="font-medium">{trainee.overallProgress}%</span>
            </div>
            <Progress value={trainee.overallProgress} className="h-2" />
          </div>

          <div>
            <div className="flex justify-between text-sm mb-1">
              <span>Time Elapsed</span>
              <span className="font-medium">{Math.round(timeProgress)}%</span>
            </div>
            <Progress value={timeProgress} className="h-2" />
          </div>

          <div className="grid grid-cols-2 gap-2 text-sm">
            <div>
              <p className="text-muted-foreground">Training Hours</p>
              <p className="font-medium">
                {trainee.trainingHoursCompleted}/{trainee.trainingHoursRequired}
              </p>
            </div>
            <div>
              <p className="text-muted-foreground">Budget Used</p>
              <p className="font-medium">
                ${trainee.budgetSpent.toLocaleString()}/${trainee.budgetAllocated.toLocaleString()}
              </p>
            </div>
          </div>
        </div>

        <div className="flex items-center justify-between pt-2 border-t">
          <div className="flex items-center gap-2 text-sm">
            <Calendar className="h-3 w-3" />
            <span>{trainee.programDurationMonths} months</span>
          </div>
          <div className="flex items-center gap-1">
            {isOnTrack ? (
              <CheckCircle className="h-4 w-4 text-green-500" />
            ) : (
              <AlertCircle className="h-4 w-4 text-orange-500" />
            )}
            <span className="text-sm font-medium">
              {isOnTrack ? 'On Track' : 'Needs Attention'}
            </span>
          </div>
        </div>
      </div>
    </Card>
  );
};

// Milestone Tracker
const MilestoneTracker: React.FC<{ trainees: GraduateTraineeProgram[] }> = ({ trainees }) => {
  const upcomingMilestones = useMemo(() => {
    const milestones: any[] = [];
    
    trainees.forEach(trainee => {
      // Check for quarterly reviews
      trainee.quarterlyReviews?.forEach(review => {
        if (!review.completed) {
          milestones.push({
            type: 'review',
            trainee: trainee.employeeName,
            date: review.scheduledDate,
            quarter: review.quarter
          });
        }
      });

      // Check for certifications
      trainee.certifications?.forEach(cert => {
        if (cert.status === 'in_progress') {
          milestones.push({
            type: 'certification',
            trainee: trainee.employeeName,
            date: cert.targetDate,
            name: cert.name
          });
        }
      });
    });

    return milestones.sort((a, b) => 
      new Date(a.date).getTime() - new Date(b.date).getTime()
    ).slice(0, 5);
  }, [trainees]);

  return (
    <Card className="p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold">Upcoming Milestones</h3>
        <Target className="h-5 w-5 text-muted-foreground" />
      </div>

      <div className="space-y-3">
        {upcomingMilestones.length > 0 ? (
          upcomingMilestones.map((milestone, index) => (
            <div key={index} className="flex items-start gap-3 p-3 rounded-lg bg-muted/30">
              <div className="mt-1">
                {milestone.type === 'review' ? (
                  <BarChart3 className="h-4 w-4 text-blue-500" />
                ) : (
                  <Award className="h-4 w-4 text-purple-500" />
                )}
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium">{milestone.trainee}</p>
                <p className="text-sm text-muted-foreground">
                  {milestone.type === 'review' 
                    ? `Q${milestone.quarter} Review`
                    : milestone.name}
                </p>
              </div>
              <p className="text-sm text-muted-foreground">
                {new Date(milestone.date).toLocaleDateString()}
              </p>
            </div>
          ))
        ) : (
          <p className="text-sm text-muted-foreground text-center py-4">
            No upcoming milestones
          </p>
        )}
      </div>
    </Card>
  );
};

export const ProgressTab: React.FC<ProgressTabProps> = ({ trainees = [] }) => {
  const activeTrainees = trainees.filter(t => t.status === 'in_progress');
  
  // Show empty state if no trainees
  if (!trainees || trainees.length === 0) {
    return (
      <Container padding="lg" className="space-y-6 overflow-y-auto custom-scrollbar">
        <Card className="p-8 text-center">
          <Users className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">No Trainees Yet</h3>
          <p className="text-sm text-muted-foreground">
            Add trainees to start tracking their progress
          </p>
        </Card>
      </Container>
    );
  }
  
  return (
    <Container padding="lg" className="space-y-6 overflow-y-auto custom-scrollbar">
      <ProgressOverviewCard trainees={trainees} />

      <Tabs defaultValue="individual" className="w-full">
        <TabsList>
          <TabsTrigger value="individual">Individual Progress</TabsTrigger>
          <TabsTrigger value="milestones">Milestones</TabsTrigger>
          <TabsTrigger value="comparisons">Comparisons</TabsTrigger>
        </TabsList>

        <TabsContent value="individual" className="mt-4">
          {activeTrainees.length > 0 ? (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {activeTrainees.map(trainee => (
                <TraineeProgressCard key={trainee.id} trainee={trainee} />
              ))}
            </div>
          ) : (
            <Card className="p-6 text-center">
              <p className="text-sm text-muted-foreground">
                No trainees currently in progress
              </p>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="milestones" className="mt-4">
          <MilestoneTracker trainees={trainees} />
        </TabsContent>

        <TabsContent value="comparisons" className="mt-4">
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Department Comparison</h3>
            <div className="space-y-4">
              {Object.entries(
                trainees.reduce((acc, t) => {
                  if (!acc[t.department]) {
                    acc[t.department] = { total: 0, progress: 0 };
                  }
                  acc[t.department].total++;
                  acc[t.department].progress += t.overallProgress;
                  return acc;
                }, {} as Record<string, { total: number; progress: number }>)
              ).map(([dept, data]) => (
                <div key={dept}>
                  <div className="flex justify-between text-sm mb-1">
                    <span>{dept}</span>
                    <span className="text-muted-foreground">
                      {Math.round(data.progress / data.total)}% avg
                    </span>
                  </div>
                  <Progress value={data.progress / data.total} className="h-2" />
                </div>
              ))}
            </div>
          </Card>
        </TabsContent>
      </Tabs>
    </Container>
  );
};

export default ProgressTab;