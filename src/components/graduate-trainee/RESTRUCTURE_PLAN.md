# Graduate Trainee Module Restructuring Plan

## Current Issues
1. **Overlapping responsibilities**: `tabs/`, `sections/`, and `pages/` folders serve similar purposes
2. **Duplicate components**: Multiple TraineeCard components in different locations
3. **Mixed concerns**: Components folder contains both UI and business logic
4. **Inconsistent organization**: Some components have subfolders, others don't
5. **Unclear separation**: Dialogs separated from components but they're also components

## New Structure Benefits
1. **Feature-based organization**: Each major feature has its own folder
2. **Clear separation of concerns**: Shared vs feature-specific components
3. **Consistent patterns**: All features follow same structure
4. **Better scalability**: Easy to add new features
5. **Improved maintainability**: Related code stays together

## Migration Steps

### Step 1: Create New Folder Structure
```bash
# Create feature folders
mkdir -p features/dashboard/components
mkdir -p features/trainees/components  
mkdir -p features/progress/components
mkdir -p features/reports/components
mkdir -p features/settings/components

# Create organized component folders
mkdir -p components/common
mkdir -p components/dialogs
mkdir -p components/forms/fields

# Create other folders
mkdir -p store
mkdir -p services
mkdir -p data
```

### Step 2: File Movement Mapping

#### Features
- `tabs/ProgressTab.tsx` → `features/progress/ProgressView.tsx`
- `tabs/ReportsTab.tsx` → `features/reports/ReportsView.tsx`
- `tabs/SettingsTab.tsx` → `features/settings/SettingsView.tsx`
- `pages/TraineeDashboard.tsx` → `features/dashboard/DashboardView.tsx`
- `pages/TraineeProgressDashboard.tsx` → `features/progress/components/ProgressDashboard.tsx`

#### Common Components
- `components/Container.tsx` → `components/common/Container.tsx`
- `components/PageHeader.tsx` → `components/common/PageHeader.tsx`
- `components/FilterBar.tsx` → `components/common/FilterBar.tsx`
- `components/LoadingState.tsx` → `components/common/LoadingState.tsx`
- `components/ErrorState.tsx` → `components/common/ErrorState.tsx`
- `components/StatusBadge.tsx` → `components/common/StatusBadge.tsx`
- `components/NotificationSystem.tsx` → `components/common/NotificationSystem.tsx`

#### Feature-Specific Components
- `components/MetricsGrid.tsx` → `features/dashboard/components/MetricsGrid.tsx`
- `components/QuickActions.tsx` → `features/dashboard/components/QuickActions.tsx`
- `components/TraineeCard.tsx` → `features/trainees/components/TraineeCard.tsx`
- `components/TraineeGrid.tsx` → `features/trainees/components/TraineeGrid.tsx`
- `components/TraineeCardVariants.tsx` → `features/trainees/components/TraineeCardVariants.tsx`
- `components/charts/*` → `features/progress/components/`
- `components/timeline/*` → `features/progress/components/`

#### Dialogs (keep together as they're cross-feature)
- `dialogs/*` → `components/dialogs/*`

#### Forms
- `forms/TraineeForm.tsx` → `components/forms/TraineeForm.tsx`

#### State & Services
- `state/traineeStore.ts` → `store/traineeStore.ts`
- `services/TraineeIntegrationService.ts` → `services/integration.ts`

#### Utils & Data
- `utils/sampleData.ts` → `data/sampleData.ts`
- `utils/mwsMockData.ts` → `data/mwsMockData.ts`
- `utils/export.ts` → `utils/export.ts`
- `constants/traineeConstants.tsx` → `utils/constants.ts`

### Step 3: Update Imports
All import paths need to be updated to reflect new structure.

### Step 4: Create Barrel Exports
Each feature folder should have an index.ts that exports its public API.

### Step 5: Clean Up
Remove empty folders and unused files.

## Implementation Priority
1. Create new folder structure
2. Move common components first
3. Move feature-specific components
4. Update state and services
5. Fix all imports
6. Test functionality
7. Remove old folders