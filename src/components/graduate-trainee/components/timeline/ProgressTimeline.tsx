import React from 'react';
import { <PERSON>, <PERSON>Header, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Calendar, 
  Clock,
  User,
  Building
} from 'lucide-react';
import { GraduateTraineeProgram } from '@/lib/api/graduateTrainee';
import { getStatusIcon, getStatusColor } from '../../constants/traineeConstants';

interface ProgressTimelineProps {
  trainee: GraduateTraineeProgram;
}

export const ProgressTimeline: React.FC<ProgressTimelineProps> = ({ trainee }) => {
  // Generate timeline events from trainee data
  const timelineEvents = [
    {
      id: 'program-start',
      title: 'Program Started',
      date: trainee.startDate,
      description: 'Graduate trainee program initiation',
      type: 'milestone',
      status: 'completed'
    },
    ...(trainee.quarterlyReviews?.map(review => ({
      id: `review-${review.reviewNumber}`,
      title: `Quarter ${review.reviewNumber} Review`,
      date: review.dueDate,
      description: review.feedback || 'Quarterly performance review',
      type: 'review',
      status: review.completedDate ? 'completed' : 'upcoming'
    })) || []),
    {
      id: 'program-end',
      title: 'Program End',
      date: trainee.expectedEndDate,
      description: 'Expected program completion date',
      type: 'milestone',
      status: trainee.actualEndDate ? 'completed' : 
              new Date() > new Date(trainee.expectedEndDate) ? 'overdue' : 'upcoming'
    }
  ].sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

  // Add actual end date if program is completed
  if (trainee.actualEndDate) {
    timelineEvents.push({
      id: 'actual-end',
      title: 'Program Completed',
      date: trainee.actualEndDate,
      description: 'Actual program completion date',
      type: 'milestone',
      status: 'completed'
    });
  }

  const getTimelineStatusColor = (status: string) => {
    const color = getStatusColor(status);
    switch (color) {
      case 'green': return 'border-green-200 bg-green-50';
      case 'red': return 'border-red-200 bg-red-50';
      case 'yellow': return 'border-yellow-200 bg-yellow-50';
      case 'blue': return 'border-blue-200 bg-blue-50';
      default: return 'border-gray-200 bg-gray-50';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'completed': return 'success';
      case 'overdue': return 'destructive';
      case 'upcoming': return 'secondary';
      default: return 'outline';
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Program Timeline</h3>
          <Badge variant="secondary" className="flex items-center gap-1">
            <Calendar className="h-4 w-4" />
            {trainee.programDurationMonths} months
          </Badge>
        </div>
        <p className="text-sm text-gray-600">
          Tracking progress from {formatDate(trainee.startDate)} to {formatDate(trainee.expectedEndDate)}
        </p>
      </CardHeader>
      <CardContent>
        <div className="relative">
          {/* Timeline line */}
          <div className="absolute left-4 top-0 h-full w-0.5 bg-gray-200"></div>
          
          {/* Timeline events */}
          <div className="space-y-6">
            {timelineEvents.map((event, index) => (
              <div key={event.id} className="relative flex items-start">
                {/* Event icon */}
                <div className={`relative z-10 flex h-8 w-8 items-center justify-center rounded-full ${getTimelineStatusColor(event.status)}`}>
                  {getStatusIcon(event.status)}
                </div>
                
                {/* Event content */}
                <div className="ml-4 flex-1">
                  <div className={`rounded-lg border p-4 ${getTimelineStatusColor(event.status)}`}>
                    <div className="flex items-center justify-between">
                      <h4 className="font-semibold">{event.title}</h4>
                      <Badge variant={getStatusBadgeVariant(event.status) as any}>
                        {event.status.charAt(0).toUpperCase() + event.status.slice(1)}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">{event.description}</p>
                    <div className="flex items-center gap-2 text-xs text-gray-500 mt-2">
                      <Calendar className="h-3 w-3" />
                      <span>{formatDate(event.date)}</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
        
        {/* Program summary */}
        <div className="mt-8 pt-6 border-t">
          <h4 className="font-medium mb-3">Program Summary</h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-3 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{trainee.trainingHoursCompleted || 0}</div>
              <div className="text-sm text-gray-600">Hours Completed</div>
              <div className="text-xs text-gray-500">of {trainee.trainingHoursRequired}</div>
            </div>
            <div className="text-center p-3 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {Math.round((trainee.budgetSpent / trainee.budgetAllocated) * 100) || 0}%
              </div>
              <div className="text-sm text-gray-600">Budget Used</div>
              <div className="text-xs text-gray-500">${trainee.budgetSpent || 0} / ${trainee.budgetAllocated}</div>
            </div>
            <div className="text-center p-3 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">
                {trainee.skillsDevelopment?.length || 0}
              </div>
              <div className="text-sm text-gray-600">Skills Tracked</div>
            </div>
            <div className="text-center p-3 bg-orange-50 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">
                {trainee.certifications?.filter(c => c.status === 'completed').length || 0}
              </div>
              <div className="text-sm text-gray-600">Certifications</div>
              <div className="text-xs text-gray-500">Completed</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};