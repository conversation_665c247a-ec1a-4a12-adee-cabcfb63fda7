import React from 'react';
import { 
  Grid, 
  List, 
  LayoutGrid,
  Search,
  Filter,
  ChevronDown,
  Columns
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel,
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';
import type { GraduateTraineeProgram } from '@/lib/api/graduateTrainee';

export type ViewMode = 'grid' | 'list' | 'kanban' | 'compact';

interface TraineeGridProps {
  trainees: GraduateTraineeProgram[];
  viewMode: ViewMode;
  onViewModeChange: (mode: ViewMode) => void;
  renderCard: (trainee: GraduateTraineeProgram) => React.ReactNode;
  loading?: boolean;
}

export const TraineeGrid: React.FC<TraineeGridProps> = ({
  trainees,
  viewMode,
  onViewModeChange,
  renderCard,
  loading = false
}) => {
  const viewModes: Array<{ mode: ViewMode; icon: React.ElementType; label: string }> = [
    { mode: 'grid', icon: Grid, label: 'Grid View' },
    { mode: 'list', icon: List, label: 'List View' },
    { mode: 'kanban', icon: Columns, label: 'Kanban Board' },
    { mode: 'compact', icon: LayoutGrid, label: 'Compact View' },
  ];

  const getGridClassName = () => {
    switch (viewMode) {
      case 'grid':
        return 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4';
      case 'list':
        return 'space-y-3';
      case 'kanban':
        return 'flex gap-4 overflow-x-auto pb-4';
      case 'compact':
        return 'space-y-2';
      default:
        return 'space-y-3';
    }
  };

  const renderKanbanBoard = () => {
    const columns = {
      'not_started': { title: 'Not Started', trainees: [] as GraduateTraineeProgram[] },
      'in_progress': { title: 'In Progress', trainees: [] as GraduateTraineeProgram[] },
      'on_hold': { title: 'On Hold', trainees: [] as GraduateTraineeProgram[] },
      'completed': { title: 'Completed', trainees: [] as GraduateTraineeProgram[] },
      'terminated': { title: 'Terminated', trainees: [] as GraduateTraineeProgram[] },
    };

    trainees.forEach(trainee => {
      if (columns[trainee.status]) {
        columns[trainee.status].trainees.push(trainee);
      }
    });

    return (
      <div className="flex gap-4 overflow-x-auto pb-4">
        {Object.entries(columns).map(([status, column]) => (
          <div key={status} className="flex-shrink-0 w-80">
            <div className="glass-panel rounded-lg p-3 h-full">
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-sm font-medium">{column.title}</h3>
                <span className="text-xs px-2 py-0.5 rounded-full bg-muted">
                  {column.trainees.length}
                </span>
              </div>
              <div className="space-y-2 max-h-[calc(100vh-300px)] overflow-y-auto custom-scrollbar">
                {column.trainees.map(trainee => (
                  <div key={trainee.id} className="transform transition-all hover:scale-[1.02]">
                    {renderCard(trainee)}
                  </div>
                ))}
                {column.trainees.length === 0 && (
                  <div className="text-center py-8 text-muted-foreground/50 text-xs">
                    No trainees
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* View Mode Selector */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <span className="text-sm text-muted-foreground">
            {trainees.length} {trainees.length === 1 ? 'trainee' : 'trainees'}
          </span>
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" className="h-8 gap-2">
              {viewModes.find(v => v.mode === viewMode)?.icon && 
                React.createElement(viewModes.find(v => v.mode === viewMode)!.icon, { 
                  className: "h-3 w-3" 
                })
              }
              {viewModes.find(v => v.mode === viewMode)?.label}
              <ChevronDown className="h-3 w-3" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            <DropdownMenuLabel className="text-xs">View Mode</DropdownMenuLabel>
            <DropdownMenuSeparator />
            {viewModes.map(({ mode, icon: Icon, label }) => (
              <DropdownMenuItem
                key={mode}
                onClick={() => onViewModeChange(mode)}
                className={cn(
                  "gap-2",
                  viewMode === mode && "bg-accent"
                )}
              >
                <Icon className="h-3 w-3" />
                {label}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Trainee Grid/List/Kanban */}
      {viewMode === 'kanban' ? (
        renderKanbanBoard()
      ) : (
        <div className={getGridClassName()}>
          {trainees.length === 0 ? (
            <div className="col-span-full text-center py-12">
              <Search className="w-12 h-12 text-muted-foreground/30 mx-auto mb-3" />
              <p className="text-sm text-muted-foreground">No trainees found</p>
              <p className="text-xs text-muted-foreground/70 mt-1">
                Try adjusting your filters or add a new trainee
              </p>
            </div>
          ) : (
            trainees.map(trainee => (
              <div 
                key={trainee.id}
                className={cn(
                  "transform transition-all hover:scale-[1.02]",
                  viewMode === 'compact' && "hover:bg-accent/5 rounded-lg"
                )}
              >
                {renderCard(trainee)}
              </div>
            ))
          )}
        </div>
      )}
    </div>
  );
};