import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { cn } from '@/lib/utils';
import {
  Users,
  BarChart3,
  Target,
  FileText,
  Settings,
  Calendar,
  Award,
  BookOpen,
  TrendingUp
} from 'lucide-react';
import { Button } from '@/components/ui/button';

interface NavigationItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  path: string;
  badge?: string | number;
}

interface TraineeNavigationProps {
  className?: string;
  orientation?: 'horizontal' | 'vertical';
  showLabels?: boolean;
}

export const TraineeNavigation: React.FC<TraineeNavigationProps> = ({
  className,
  orientation = 'horizontal',
  showLabels = true
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  
  const navigationItems: NavigationItem[] = [
    {
      id: 'overview',
      label: 'Overview',
      icon: <Users className="h-5 w-5" />,
      path: '/graduate-trainee'
    },
    {
      id: 'tracker',
      label: 'Tracker',
      icon: <Target className="h-5 w-5" />,
      path: '/graduate-trainee/tracker'
    },
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: <BarChart3 className="h-5 w-5" />,
      path: '/graduate-trainee/dashboard'
    },
    {
      id: 'progress',
      label: 'Progress',
      icon: <TrendingUp className="h-5 w-5" />,
      path: '/graduate-trainee/progress'
    },
    {
      id: 'reviews',
      label: 'Reviews',
      icon: <Calendar className="h-5 w-5" />,
      path: '/graduate-trainee/reviews',
      badge: 3
    },
    {
      id: 'skills',
      label: 'Skills',
      icon: <BookOpen className="h-5 w-5" />,
      path: '/graduate-trainee/skills'
    },
    {
      id: 'certifications',
      label: 'Certifications',
      icon: <Award className="h-5 w-5" />,
      path: '/graduate-trainee/certifications'
    },
    {
      id: 'reports',
      label: 'Reports',
      icon: <FileText className="h-5 w-5" />,
      path: '/graduate-trainee/reports'
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: <Settings className="h-5 w-5" />,
      path: '/graduate-trainee/settings'
    }
  ];

  const isActive = (path: string) => {
    return location.pathname === path || location.pathname.startsWith(path + '/');
  };

  const containerClass = cn(
    'flex gap-2',
    {
      'flex-row flex-wrap': orientation === 'horizontal',
      'flex-col': orientation === 'vertical'
    },
    className
  );

  const buttonClass = (path: string) => cn(
    'relative transition-all duration-200',
    {
      'justify-start': orientation === 'vertical',
      'justify-center': orientation === 'horizontal' && !showLabels
    }
  );

  return (
    <nav className={containerClass}>
      {navigationItems.map((item) => (
        <Button
          key={item.id}
          variant={isActive(item.path) ? 'default' : 'ghost'}
          size={orientation === 'horizontal' && !showLabels ? 'icon' : 'default'}
          className={buttonClass(item.path)}
          onClick={() => navigate(item.path)}
        >
          {item.icon}
          {showLabels && (
            <span className={cn(
              'ml-2',
              { 'sr-only': orientation === 'horizontal' && !showLabels }
            )}>
              {item.label}
            </span>
          )}
          {item.badge && (
            <span className="absolute -top-1 -right-1 h-5 w-5 rounded-full bg-red-500 text-white text-xs flex items-center justify-center">
              {item.badge}
            </span>
          )}
        </Button>
      ))}
    </nav>
  );
};

export const TraineeNavigationSidebar: React.FC = () => {
  return (
    <div className="w-64 bg-background border-r h-full">
      <div className="p-4">
        <h2 className="text-lg font-semibold mb-4">Graduate Trainee</h2>
        <TraineeNavigation orientation="vertical" showLabels={true} />
      </div>
    </div>
  );
};

export const TraineeNavigationHeader: React.FC = () => {
  return (
    <div className="border-b bg-background">
      <div className="container mx-auto px-4 py-2">
        <TraineeNavigation orientation="horizontal" showLabels={true} />
      </div>
    </div>
  );
};