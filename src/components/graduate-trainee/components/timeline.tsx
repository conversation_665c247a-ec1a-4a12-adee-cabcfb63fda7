import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { 
  CheckCircle, 
  Circle, 
  Clock,
  Calendar,
  Award,
  BookOpen,
  Target,
  AlertCircle
} from 'lucide-react';
import type { GraduateTraineeProgram } from '@/lib/api/graduateTrainee';
import { cn } from '@/lib/utils';

interface ProgressTimelineProps {
  trainee: GraduateTraineeProgram;
}

interface TimelineEvent {
  date: string;
  title: string;
  description: string;
  type: 'milestone' | 'review' | 'certification' | 'training';
  status: 'completed' | 'in_progress' | 'upcoming';
  icon?: React.ReactNode;
}

export const ProgressTimeline: React.FC<ProgressTimelineProps> = ({ trainee }) => {
  // Generate timeline events from trainee data
  const events: TimelineEvent[] = [];

  // Add start event
  events.push({
    date: trainee.startDate,
    title: 'Program Started',
    description: `Joined as Graduate Trainee in ${trainee.department}`,
    type: 'milestone',
    status: 'completed',
    icon: <Calendar className="h-4 w-4" />
  });

  // Add quarterly reviews
  trainee.quarterlyReviews?.forEach(review => {
    events.push({
      date: review.completedDate || review.scheduledDate,
      title: `Q${review.quarter} Review`,
      description: review.feedback || 'Quarterly performance review',
      type: 'review',
      status: review.completed ? 'completed' : 'upcoming',
      icon: <Target className="h-4 w-4" />
    });
  });

  // Add certifications
  trainee.certifications?.forEach(cert => {
    events.push({
      date: cert.targetDate || cert.completionDate || trainee.expectedEndDate,
      title: cert.name,
      description: `Certification from ${cert.provider}`,
      type: 'certification',
      status: cert.status === 'completed' ? 'completed' : 
              cert.status === 'in_progress' ? 'in_progress' : 'upcoming',
      icon: <Award className="h-4 w-4" />
    });
  });

  // Add expected end
  events.push({
    date: trainee.expectedEndDate,
    title: 'Program Completion',
    description: 'Expected graduation from trainee program',
    type: 'milestone',
    status: trainee.status === 'completed' ? 'completed' : 'upcoming',
    icon: <CheckCircle className="h-4 w-4" />
  });

  // Sort events by date
  events.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'in_progress':
        return <Clock className="h-5 w-5 text-blue-500" />;
      default:
        return <Circle className="h-5 w-5 text-gray-400" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'milestone':
        return 'bg-purple-100 text-purple-700';
      case 'review':
        return 'bg-blue-100 text-blue-700';
      case 'certification':
        return 'bg-green-100 text-green-700';
      case 'training':
        return 'bg-orange-100 text-orange-700';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  };

  if (events.length === 0) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <AlertCircle className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <p className="text-muted-foreground">No timeline events available</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent className="p-6">
        <div className="space-y-6">
          {events.map((event, index) => (
            <div key={index} className="flex gap-4">
              <div className="flex flex-col items-center">
                {getStatusIcon(event.status)}
                {index < events.length - 1 && (
                  <div className={cn(
                    "w-0.5 h-16 mt-2",
                    event.status === 'completed' ? 'bg-green-200' : 'bg-gray-200'
                  )} />
                )}
              </div>
              
              <div className="flex-1 pb-4">
                <div className="flex items-start justify-between">
                  <div>
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-medium text-sm">{event.title}</h4>
                      <span className={cn(
                        "px-2 py-0.5 text-xs rounded-full font-medium",
                        getTypeColor(event.type)
                      )}>
                        {event.type}
                      </span>
                    </div>
                    <p className="text-sm text-muted-foreground">{event.description}</p>
                  </div>
                  <span className="text-xs text-muted-foreground">
                    {new Date(event.date).toLocaleDateString()}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default ProgressTimeline;