import React from 'react';
import { LucideIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface PageHeaderProps {
  icon?: LucideIcon;
  title: string;
  subtitle?: string;
  action?: {
    label: string;
    onClick: () => void;
    icon?: LucideIcon;
    variant?: 'default' | 'outline' | 'ghost' | 'secondary';
  };
  className?: string;
}

export const PageHeader: React.FC<PageHeaderProps> = ({
  icon: Icon,
  title,
  subtitle,
  action,
  className
}) => {
  return (
    <div className={cn('glass-panel header-minimal flex items-center justify-between', className)}>
      <div className="flex items-center gap-3">
        {Icon && <Icon className="icon-small text-primary" />}
        <div>
          <h1 className="header-title">{title}</h1>
          {subtitle && <p className="header-subtitle">{subtitle}</p>}
        </div>
      </div>
      {action && (
        <Button
          onClick={action.onClick}
          size="sm"
          variant={action.variant || 'default'}
          className="h-8 px-3 text-mini gap-1.5 transition-smooth"
        >
          {action.icon && <action.icon className="icon-micro" />}
          {action.label}
        </Button>
      )}
    </div>
  );
};

export default PageHeader;