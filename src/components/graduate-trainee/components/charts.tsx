import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { 
  Target,
  TrendingUp,
  Award,
  BookOpen,
  Star,
  AlertCircle,
  CheckCircle,
  Circle
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface SkillsDevelopmentChartProps {
  skills: Array<{
    skillName: string;
    targetLevel: number;
    currentLevel: number;
    assessmentDate: string;
  }>;
  traineeName: string;
}

export const SkillsDevelopmentChart: React.FC<SkillsDevelopmentChartProps> = ({ 
  skills, 
  traineeName 
}) => {
  if (!skills || skills.length === 0) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <BookOpen className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <p className="text-muted-foreground">No skills data available</p>
        </CardContent>
      </Card>
    );
  }

  const getLevelColor = (current: number, target: number) => {
    const percentage = (current / target) * 100;
    if (percentage >= 100) return 'text-green-600 bg-green-100';
    if (percentage >= 75) return 'text-blue-600 bg-blue-100';
    if (percentage >= 50) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  const getProgressColor = (current: number, target: number) => {
    const percentage = (current / target) * 100;
    if (percentage >= 100) return 'bg-green-500';
    if (percentage >= 75) return 'bg-blue-500';
    if (percentage >= 50) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const renderStars = (level: number, max: number = 5) => {
    return Array.from({ length: max }, (_, i) => (
      <Star
        key={i}
        className={cn(
          "h-4 w-4",
          i < level ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
        )}
      />
    ));
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Skills Development Progress</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {skills.map((skill, index) => {
              const progress = (skill.currentLevel / skill.targetLevel) * 100;
              const isComplete = skill.currentLevel >= skill.targetLevel;

              return (
                <div key={index} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <h4 className="font-medium text-sm">{skill.skillName}</h4>
                      {isComplete && (
                        <Award className="h-4 w-4 text-green-500" />
                      )}
                    </div>
                    <span className={cn(
                      "px-2 py-1 text-xs rounded-full font-medium",
                      getLevelColor(skill.currentLevel, skill.targetLevel)
                    )}>
                      Level {skill.currentLevel}/{skill.targetLevel}
                    </span>
                  </div>

                  <div className="flex items-center gap-2">
                    <div className="flex gap-0.5">
                      {renderStars(skill.currentLevel)}
                    </div>
                    <span className="text-xs text-muted-foreground">
                      Current Level
                    </span>
                  </div>

                  <div className="space-y-1">
                    <div className="flex justify-between text-xs">
                      <span className="text-muted-foreground">Progress</span>
                      <span className="font-medium">{Math.round(progress)}%</span>
                    </div>
                    <Progress 
                      value={progress} 
                      className="h-2"
                    />
                  </div>

                  <p className="text-xs text-muted-foreground">
                    Last assessed: {new Date(skill.assessmentDate).toLocaleDateString()}
                  </p>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Skills Summary Card */}
      <Card>
        <CardContent className="p-4">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <p className="text-muted-foreground">Total Skills</p>
              <p className="text-xl font-bold">{skills.length}</p>
            </div>
            <div>
              <p className="text-muted-foreground">Skills Mastered</p>
              <p className="text-xl font-bold text-green-600">
                {skills.filter(s => s.currentLevel >= s.targetLevel).length}
              </p>
            </div>
            <div>
              <p className="text-muted-foreground">Average Progress</p>
              <p className="text-xl font-bold">
                {Math.round(
                  skills.reduce((sum, s) => sum + (s.currentLevel / s.targetLevel) * 100, 0) / skills.length
                )}%
              </p>
            </div>
            <div>
              <p className="text-muted-foreground">Skills In Progress</p>
              <p className="text-xl font-bold text-blue-600">
                {skills.filter(s => s.currentLevel < s.targetLevel).length}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// Budget Breakdown Chart Component
export const BudgetBreakdownChart: React.FC<{
  budgetAllocated: number;
  budgetSpent: number;
  categories?: Array<{ name: string; amount: number; }>;
}> = ({ budgetAllocated, budgetSpent, categories }) => {
  const utilizationPercentage = (budgetSpent / budgetAllocated) * 100;
  const remaining = budgetAllocated - budgetSpent;

  const getUtilizationColor = () => {
    if (utilizationPercentage > 90) return 'text-red-600';
    if (utilizationPercentage > 75) return 'text-orange-600';
    if (utilizationPercentage > 50) return 'text-yellow-600';
    return 'text-green-600';
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Budget Overview</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Main Budget Progress */}
          <div>
            <div className="flex justify-between text-sm mb-2">
              <span>Budget Utilization</span>
              <span className={cn("font-medium", getUtilizationColor())}>
                ${budgetSpent.toLocaleString()} / ${budgetAllocated.toLocaleString()}
              </span>
            </div>
            <Progress value={utilizationPercentage} className="h-3" />
            <p className="text-xs text-muted-foreground mt-1">
              ${remaining.toLocaleString()} remaining ({(100 - utilizationPercentage).toFixed(1)}%)
            </p>
          </div>

          {/* Category Breakdown if available */}
          {categories && categories.length > 0 && (
            <div className="space-y-2 pt-4 border-t">
              <h4 className="text-sm font-medium mb-2">Spending by Category</h4>
              {categories.map((category, index) => (
                <div key={index} className="flex justify-between text-sm">
                  <span className="text-muted-foreground">{category.name}</span>
                  <span className="font-medium">${category.amount.toLocaleString()}</span>
                </div>
              ))}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

// Training Progress Chart
export const TrainingProgressChart: React.FC<{
  hoursCompleted: number;
  hoursRequired: number;
  modules?: Array<{ name: string; completed: boolean; hours: number; }>;
}> = ({ hoursCompleted, hoursRequired, modules }) => {
  const progressPercentage = (hoursCompleted / hoursRequired) * 100;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Training Progress</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Hours Progress */}
          <div>
            <div className="flex justify-between text-sm mb-2">
              <span>Training Hours</span>
              <span className="font-medium">
                {hoursCompleted} / {hoursRequired} hours
              </span>
            </div>
            <Progress value={progressPercentage} className="h-3" />
            <p className="text-xs text-muted-foreground mt-1">
              {(hoursRequired - hoursCompleted)} hours remaining
            </p>
          </div>

          {/* Modules if available */}
          {modules && modules.length > 0 && (
            <div className="space-y-2 pt-4 border-t">
              <h4 className="text-sm font-medium mb-2">Training Modules</h4>
              {modules.map((module, index) => (
                <div key={index} className="flex items-center justify-between text-sm">
                  <div className="flex items-center gap-2">
                    {module.completed ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : (
                      <Circle className="h-4 w-4 text-gray-400" />
                    )}
                    <span className={module.completed ? 'line-through text-muted-foreground' : ''}>
                      {module.name}
                    </span>
                  </div>
                  <span className="text-xs text-muted-foreground">{module.hours}h</span>
                </div>
              ))}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default SkillsDevelopmentChart;