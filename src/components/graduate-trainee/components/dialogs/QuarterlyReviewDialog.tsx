import React, { useState } from 'react';
import { Calendar, Star, Save, Plus, X } from 'lucide-react';
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import type { GraduateTraineeProgram, QuarterlyReview } from '@/lib/api/graduateTrainee';

interface QuarterlyReviewDialogProps {
  isOpen: boolean;
  onClose: () => void;
  trainee: GraduateTraineeProgram;
  onSubmit: (review: QuarterlyReview) => Promise<void>;
}

export const QuarterlyReviewDialog: React.FC<QuarterlyReviewDialogProps> = ({
  isOpen,
  onClose,
  trainee,
  onSubmit
}) => {
  const [formData, setFormData] = useState<Partial<QuarterlyReview>>({
    reviewNumber: (trainee.quarterlyReviews?.length || 0) + 1,
    dueDate: new Date().toISOString().split('T')[0],
    reviewer: trainee.manager,
    overallRating: 3,
    feedback: '',
    goalsAchieved: [],
    goalsPending: [],
    skillsImproved: [],
    areasForImprovement: [],
    nextQuarterGoals: [],
    supportNeeded: [],
    isSatisfactory: true,
    continuationRecommendation: true
  });

  const [newGoalAchieved, setNewGoalAchieved] = useState('');
  const [newGoalPending, setNewGoalPending] = useState('');
  const [newSkillImproved, setNewSkillImproved] = useState('');
  const [newAreaImprovement, setNewAreaImprovement] = useState('');
  const [newNextGoal, setNewNextGoal] = useState('');
  const [newSupport, setNewSupport] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    try {
      await onSubmit({
        ...formData,
        completedDate: new Date().toISOString().split('T')[0]
      } as QuarterlyReview);
      onClose();
    } catch (error) {
      console.error('Failed to submit review:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const addToList = (
    listKey: keyof QuarterlyReview,
    value: string,
    setValue: (value: string) => void
  ) => {
    if (value.trim()) {
      const currentList = (formData[listKey] as string[]) || [];
      setFormData(prev => ({
        ...prev,
        [listKey]: [...currentList, value.trim()]
      }));
      setValue('');
    }
  };

  const removeFromList = (listKey: keyof QuarterlyReview, index: number) => {
    const currentList = (formData[listKey] as string[]) || [];
    setFormData(prev => ({
      ...prev,
      [listKey]: currentList.filter((_, i) => i !== index)
    }));
  };

  const renderStarRating = () => {
    return (
      <div className="flex items-center gap-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            type="button"
            onClick={() => setFormData(prev => ({ ...prev, overallRating: star }))}
            className={`p-1 rounded ${
              star <= (formData.overallRating || 0)
                ? 'text-yellow-500'
                : 'text-gray-300 hover:text-yellow-400'
            }`}
          >
            <Star className="h-5 w-5 fill-current" />
          </button>
        ))}
        <span className="ml-2 text-sm text-muted-foreground">
          {formData.overallRating}/5
        </span>
      </div>
    );
  };

  const renderListSection = (
    title: string,
    listKey: keyof QuarterlyReview,
    newValue: string,
    setNewValue: (value: string) => void,
    placeholder: string
  ) => {
    const list = (formData[listKey] as string[]) || [];
    
    return (
      <div className="space-y-2">
        <Label className="text-sm font-medium">{title}</Label>
        <div className="flex gap-2">
          <Input
            value={newValue}
            onChange={(e) => setNewValue(e.target.value)}
            placeholder={placeholder}
            onKeyPress={(e) => {
              if (e.key === 'Enter') {
                e.preventDefault();
                addToList(listKey, newValue, setNewValue);
              }
            }}
          />
          <Button
            type="button"
            size="sm"
            onClick={() => addToList(listKey, newValue, setNewValue)}
            disabled={!newValue.trim()}
          >
            <Plus className="h-4 w-4" />
          </Button>
        </div>
        {list.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {list.map((item, index) => (
              <Badge key={index} variant="secondary" className="text-xs">
                {item}
                <button
                  type="button"
                  onClick={() => removeFromList(listKey, index)}
                  className="ml-1 hover:text-red-500"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            ))}
          </div>
        )}
      </div>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Quarterly Review - {trainee.employeeName}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Review Information</CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="reviewNumber">Review Number</Label>
                <Input
                  id="reviewNumber"
                  type="number"
                  value={formData.reviewNumber}
                  onChange={(e) => setFormData(prev => ({ 
                    ...prev, 
                    reviewNumber: parseInt(e.target.value) 
                  }))}
                  min="1"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="dueDate">Due Date</Label>
                <Input
                  id="dueDate"
                  type="date"
                  value={formData.dueDate}
                  onChange={(e) => setFormData(prev => ({ 
                    ...prev, 
                    dueDate: e.target.value 
                  }))}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="reviewer">Reviewer</Label>
                <Input
                  id="reviewer"
                  value={formData.reviewer}
                  onChange={(e) => setFormData(prev => ({ 
                    ...prev, 
                    reviewer: e.target.value 
                  }))}
                  placeholder="Enter reviewer name"
                />
              </div>

              <div className="space-y-2">
                <Label>Overall Rating</Label>
                {renderStarRating()}
              </div>
            </CardContent>
          </Card>

          {/* Feedback */}
          <Card>
            <CardHeader>
              <CardTitle>Feedback</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Label htmlFor="feedback">Overall Feedback</Label>
                <Textarea
                  id="feedback"
                  value={formData.feedback}
                  onChange={(e) => setFormData(prev => ({ 
                    ...prev, 
                    feedback: e.target.value 
                  }))}
                  placeholder="Provide detailed feedback on the trainee's performance..."
                  rows={4}
                />
              </div>
            </CardContent>
          </Card>

          {/* Goals and Performance */}
          <Card>
            <CardHeader>
              <CardTitle>Goals and Performance</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {renderListSection(
                'Goals Achieved',
                'goalsAchieved',
                newGoalAchieved,
                setNewGoalAchieved,
                'Enter achieved goal...'
              )}

              {renderListSection(
                'Pending Goals',
                'goalsPending',
                newGoalPending,
                setNewGoalPending,
                'Enter pending goal...'
              )}

              {renderListSection(
                'Skills Improved',
                'skillsImproved',
                newSkillImproved,
                setNewSkillImproved,
                'Enter improved skill...'
              )}

              {renderListSection(
                'Areas for Improvement',
                'areasForImprovement',
                newAreaImprovement,
                setNewAreaImprovement,
                'Enter area for improvement...'
              )}
            </CardContent>
          </Card>

          {/* Future Planning */}
          <Card>
            <CardHeader>
              <CardTitle>Future Planning</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {renderListSection(
                'Next Quarter Goals',
                'nextQuarterGoals',
                newNextGoal,
                setNewNextGoal,
                'Enter next quarter goal...'
              )}

              {renderListSection(
                'Support Needed',
                'supportNeeded',
                newSupport,
                setNewSupport,
                'Enter support requirement...'
              )}
            </CardContent>
          </Card>

          {/* Recommendations */}
          <Card>
            <CardHeader>
              <CardTitle>Recommendations</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Performance is Satisfactory</Label>
                  <p className="text-sm text-muted-foreground">
                    Overall performance meets expectations
                  </p>
                </div>
                <Switch
                  checked={formData.isSatisfactory}
                  onCheckedChange={(checked) => setFormData(prev => ({ 
                    ...prev, 
                    isSatisfactory: checked 
                  }))}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Recommend Continuation</Label>
                  <p className="text-sm text-muted-foreground">
                    Recommend trainee continues in the program
                  </p>
                </div>
                <Switch
                  checked={formData.continuationRecommendation}
                  onCheckedChange={(checked) => setFormData(prev => ({ 
                    ...prev, 
                    continuationRecommendation: checked 
                  }))}
                />
              </div>
            </CardContent>
          </Card>

          <DialogFooter className="gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="min-w-[120px]"
            >
              {isSubmitting ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                  Saving...
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <Save className="h-4 w-4" />
                  Save Review
                </div>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};