import React, { useState } from 'react';
import { Award, Save } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import type { GraduateTraineeProgram, Certification, CertificationStatus } from '@/lib/api/graduateTrainee';

interface CertificationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  trainee: GraduateTraineeProgram;
  onSubmit: (certification: Certification) => Promise<void>;
}

const certificationStatuses: CertificationStatus[] = [
  'not_started',
  'in_progress', 
  'completed',
  'expired',
  'waived'
];

const statusLabels = {
  not_started: 'Not Started',
  in_progress: 'In Progress',
  completed: 'Completed',
  expired: 'Expired',
  waived: 'Waived'
};

const commonCertifications = [
  'AWS Certified Solutions Architect',
  'Microsoft Azure Fundamentals',
  'Google Cloud Professional',
  'Certified Scrum Master',
  'Project Management Professional (PMP)',
  'Certified Information Systems Security Professional (CISSP)',
  'CompTIA Security+',
  'Certified Ethical Hacker (CEH)',
  'Salesforce Administrator',
  'HubSpot Content Marketing',
  'Google Analytics Certified',
  'Facebook Blueprint Certification',
  'Certified Public Accountant (CPA)',
  'Financial Risk Manager (FRM)',
  'Chartered Financial Analyst (CFA)',
  'Professional in Human Resources (PHR)',
  'Certified Compensation Professional (CCP)',
  'Six Sigma Green Belt',
  'Lean Six Sigma Black Belt',
  'ITIL Foundation'
];

const issuingBodies = [
  'Amazon Web Services (AWS)',
  'Microsoft',
  'Google',
  'Scrum Alliance',
  'Project Management Institute (PMI)',
  '(ISC)² - International Information System Security Certification Consortium',
  'CompTIA',
  'EC-Council',
  'Salesforce',
  'HubSpot',
  'Google Analytics',
  'Facebook',
  'American Institute of CPAs (AICPA)',
  'Global Association of Risk Professionals (GARP)',
  'CFA Institute',
  'HR Certification Institute (HRCI)',
  'WorldatWork',
  'American Society for Quality (ASQ)',
  'AXELOS'
];

export const CertificationDialog: React.FC<CertificationDialogProps> = ({
  isOpen,
  onClose,
  trainee,
  onSubmit
}) => {
  const [formData, setFormData] = useState<Partial<Certification>>({
    name: '',
    issuingBody: '',
    dateAchieved: '',
    expiryDate: '',
    isMandatory: false,
    status: 'not_started'
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name?.trim()) {
      newErrors.name = 'Certification name is required';
    }
    if (!formData.issuingBody?.trim()) {
      newErrors.issuingBody = 'Issuing body is required';
    }
    if (!formData.status) {
      newErrors.status = 'Status is required';
    }

    // Date validations
    if (formData.dateAchieved && formData.expiryDate) {
      const achieved = new Date(formData.dateAchieved);
      const expiry = new Date(formData.expiryDate);
      if (expiry <= achieved) {
        newErrors.expiryDate = 'Expiry date must be after achievement date';
      }
    }

    if (formData.status === 'completed' && !formData.dateAchieved) {
      newErrors.dateAchieved = 'Achievement date is required for completed certifications';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    
    try {
      await onSubmit(formData as Certification);
      onClose();
    } catch (error) {
      console.error('Failed to submit certification:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: keyof Certification, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleStatusChange = (status: CertificationStatus) => {
    setFormData(prev => {
      const updated = { ...prev, status };
      
      // Clear dates if status is not completed
      if (status !== 'completed') {
        updated.dateAchieved = '';
      }
      
      // Clear expiry date if status is waived or not started
      if (status === 'waived' || status === 'not_started') {
        updated.expiryDate = '';
      }
      
      return updated;
    });
  };

  const suggestIssuingBody = (certName: string) => {
    const cert = certName.toLowerCase();
    if (cert.includes('aws')) return 'Amazon Web Services (AWS)';
    if (cert.includes('azure') || cert.includes('microsoft')) return 'Microsoft';
    if (cert.includes('google')) return 'Google';
    if (cert.includes('scrum')) return 'Scrum Alliance';
    if (cert.includes('pmp') || cert.includes('project management')) return 'Project Management Institute (PMI)';
    if (cert.includes('cissp')) return '(ISC)² - International Information System Security Certification Consortium';
    if (cert.includes('comptia')) return 'CompTIA';
    if (cert.includes('ceh') || cert.includes('ethical hacker')) return 'EC-Council';
    if (cert.includes('salesforce')) return 'Salesforce';
    if (cert.includes('hubspot')) return 'HubSpot';
    if (cert.includes('analytics')) return 'Google Analytics';
    if (cert.includes('facebook')) return 'Facebook';
    if (cert.includes('cpa')) return 'American Institute of CPAs (AICPA)';
    if (cert.includes('frm')) return 'Global Association of Risk Professionals (GARP)';
    if (cert.includes('cfa')) return 'CFA Institute';
    if (cert.includes('phr') || cert.includes('human resources')) return 'HR Certification Institute (HRCI)';
    if (cert.includes('six sigma')) return 'American Society for Quality (ASQ)';
    if (cert.includes('itil')) return 'AXELOS';
    return '';
  };

  const handleCertificationNameChange = (name: string) => {
    handleInputChange('name', name);
    
    // Auto-suggest issuing body
    const suggestedBody = suggestIssuingBody(name);
    if (suggestedBody && !formData.issuingBody) {
      handleInputChange('issuingBody', suggestedBody);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Award className="h-5 w-5" />
            Add Certification - {trainee.employeeName}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Certification Information */}
          <Card>
            <CardHeader>
              <CardTitle>Certification Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">Certification Name *</Label>
                <div className="space-y-2">
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleCertificationNameChange(e.target.value)}
                    placeholder="Enter certification name"
                    className={errors.name ? 'border-red-500' : ''}
                  />
                  {errors.name && (
                    <p className="text-sm text-red-500">{errors.name}</p>
                  )}
                  
                  {/* Suggestions */}
                  <div className="space-y-2">
                    <span className="text-xs text-muted-foreground">Popular certifications:</span>
                    <div className="flex flex-wrap gap-1 max-h-20 overflow-y-auto">
                      {commonCertifications.map(cert => (
                        <Button
                          key={cert}
                          type="button"
                          variant="outline"
                          size="sm"
                          className="h-6 text-xs"
                          onClick={() => handleCertificationNameChange(cert)}
                        >
                          {cert}
                        </Button>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="issuingBody">Issuing Body *</Label>
                <div className="space-y-2">
                  <Input
                    id="issuingBody"
                    value={formData.issuingBody}
                    onChange={(e) => handleInputChange('issuingBody', e.target.value)}
                    placeholder="Enter issuing organization"
                    className={errors.issuingBody ? 'border-red-500' : ''}
                  />
                  {errors.issuingBody && (
                    <p className="text-sm text-red-500">{errors.issuingBody}</p>
                  )}
                  
                  {/* Issuing Body Suggestions */}
                  <div className="space-y-2">
                    <span className="text-xs text-muted-foreground">Common issuing bodies:</span>
                    <div className="flex flex-wrap gap-1 max-h-16 overflow-y-auto">
                      {issuingBodies.slice(0, 8).map(body => (
                        <Button
                          key={body}
                          type="button"
                          variant="outline"
                          size="sm"
                          className="h-6 text-xs"
                          onClick={() => handleInputChange('issuingBody', body)}
                        >
                          {body.length > 20 ? body.substring(0, 20) + '...' : body}
                        </Button>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="status">Status *</Label>
                <Select
                  value={formData.status}
                  onValueChange={(value) => handleStatusChange(value as CertificationStatus)}
                >
                  <SelectTrigger className={errors.status ? 'border-red-500' : ''}>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    {certificationStatuses.map(status => (
                      <SelectItem key={status} value={status}>
                        {statusLabels[status]}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.status && (
                  <p className="text-sm text-red-500">{errors.status}</p>
                )}
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Mandatory Certification</Label>
                  <p className="text-sm text-muted-foreground">
                    Required for program completion
                  </p>
                </div>
                <Switch
                  checked={formData.isMandatory}
                  onCheckedChange={(checked) => handleInputChange('isMandatory', checked)}
                />
              </div>
            </CardContent>
          </Card>

          {/* Dates */}
          <Card>
            <CardHeader>
              <CardTitle>Important Dates</CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="dateAchieved">
                  Date Achieved {formData.status === 'completed' && '*'}
                </Label>
                <Input
                  id="dateAchieved"
                  type="date"
                  value={formData.dateAchieved}
                  onChange={(e) => handleInputChange('dateAchieved', e.target.value)}
                  disabled={formData.status !== 'completed'}
                  className={errors.dateAchieved ? 'border-red-500' : ''}
                />
                {errors.dateAchieved && (
                  <p className="text-sm text-red-500">{errors.dateAchieved}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="expiryDate">Expiry Date (Optional)</Label>
                <Input
                  id="expiryDate"
                  type="date"
                  value={formData.expiryDate}
                  onChange={(e) => handleInputChange('expiryDate', e.target.value)}
                  disabled={formData.status === 'waived' || formData.status === 'not_started'}
                  className={errors.expiryDate ? 'border-red-500' : ''}
                />
                {errors.expiryDate && (
                  <p className="text-sm text-red-500">{errors.expiryDate}</p>
                )}
                {formData.status !== 'waived' && formData.status !== 'not_started' && (
                  <p className="text-xs text-muted-foreground">
                    Leave empty if certification doesn't expire
                  </p>
                )}
              </div>
            </CardContent>
          </Card>

          <DialogFooter className="gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="min-w-[120px]"
            >
              {isSubmitting ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                  Saving...
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <Save className="h-4 w-4" />
                  Add Certification
                </div>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};