import React, { useState } from 'react';
import { TrendingUp, Save, Plus } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import type { GraduateTraineeProgram, SkillDevelopment } from '@/lib/api/graduateTrainee';

interface SkillsDevelopmentDialogProps {
  isOpen: boolean;
  onClose: () => void;
  trainee: GraduateTraineeProgram;
  onSubmit: (skill: SkillDevelopment) => Promise<void>;
}

const skillCategories = [
  'Technical Skills',
  'Soft Skills',
  'Leadership',
  'Communication',
  'Problem Solving',
  'Project Management',
  'Industry Knowledge',
  'Tools & Technologies',
  'Analytical Skills',
  'Creative Skills'
];

const commonSkills = {
  'Technical Skills': [
    'Programming Languages',
    'Database Management',
    'System Architecture',
    'Testing & QA',
    'DevOps',
    'Security'
  ],
  'Soft Skills': [
    'Time Management',
    'Teamwork',
    'Adaptability',
    'Critical Thinking',
    'Emotional Intelligence',
    'Conflict Resolution'
  ],
  'Leadership': [
    'Team Leadership',
    'Decision Making',
    'Strategic Thinking',
    'Mentoring',
    'Change Management',
    'Vision Setting'
  ],
  'Communication': [
    'Presentation Skills',
    'Written Communication',
    'Active Listening',
    'Public Speaking',
    'Cross-cultural Communication',
    'Negotiation'
  ]
};

export const SkillsDevelopmentDialog: React.FC<SkillsDevelopmentDialogProps> = ({
  isOpen,
  onClose,
  trainee,
  onSubmit
}) => {
  const [formData, setFormData] = useState<Partial<SkillDevelopment>>({
    skillName: '',
    category: '',
    baselineLevel: 1,
    targetLevel: 4,
    currentLevel: 1,
    progressPercentage: 0,
    trainingCompleted: [],
    nextSteps: ''
  });

  const [newTraining, setNewTraining] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.skillName || !formData.category) {
      return;
    }

    setIsSubmitting(true);
    
    try {
      // Calculate progress percentage based on current level vs target level
      const progress = ((formData.currentLevel! - formData.baselineLevel!) / 
                       (formData.targetLevel! - formData.baselineLevel!)) * 100;
      
      await onSubmit({
        ...formData,
        progressPercentage: Math.min(100, Math.max(0, progress))
      } as SkillDevelopment);
      onClose();
    } catch (error) {
      console.error('Failed to submit skill:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const addTraining = () => {
    if (newTraining.trim()) {
      setFormData(prev => ({
        ...prev,
        trainingCompleted: [...(prev.trainingCompleted || []), newTraining.trim()]
      }));
      setNewTraining('');
    }
  };

  const removeTraining = (index: number) => {
    setFormData(prev => ({
      ...prev,
      trainingCompleted: prev.trainingCompleted?.filter((_, i) => i !== index) || []
    }));
  };

  const handleLevelChange = (field: 'baselineLevel' | 'currentLevel' | 'targetLevel', value: number) => {
    setFormData(prev => {
      const updated = { ...prev, [field]: value };
      
      // Recalculate progress when levels change
      if (updated.baselineLevel && updated.currentLevel && updated.targetLevel) {
        const progress = ((updated.currentLevel - updated.baselineLevel) / 
                         (updated.targetLevel - updated.baselineLevel)) * 100;
        updated.progressPercentage = Math.min(100, Math.max(0, progress));
      }
      
      return updated;
    });
  };

  const getSuggestedSkills = () => {
    if (!formData.category) return [];
    return commonSkills[formData.category as keyof typeof commonSkills] || [];
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Skills Development - {trainee.employeeName}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Skill Information */}
          <Card>
            <CardHeader>
              <CardTitle>Skill Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="category">Category *</Label>
                  <Select
                    value={formData.category}
                    onValueChange={(value) => setFormData(prev => ({ 
                      ...prev, 
                      category: value,
                      skillName: '' // Reset skill name when category changes
                    }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {skillCategories.map(category => (
                        <SelectItem key={category} value={category}>
                          {category}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="skillName">Skill Name *</Label>
                  <div className="space-y-2">
                    <Input
                      id="skillName"
                      value={formData.skillName}
                      onChange={(e) => setFormData(prev => ({ 
                        ...prev, 
                        skillName: e.target.value 
                      }))}
                      placeholder="Enter skill name"
                    />
                    {getSuggestedSkills().length > 0 && (
                      <div className="flex flex-wrap gap-1">
                        <span className="text-xs text-muted-foreground">Suggestions:</span>
                        {getSuggestedSkills().map(skill => (
                          <Button
                            key={skill}
                            type="button"
                            variant="outline"
                            size="sm"
                            className="h-6 text-xs"
                            onClick={() => setFormData(prev => ({ 
                              ...prev, 
                              skillName: skill 
                            }))}
                          >
                            {skill}
                          </Button>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Skill Levels */}
          <Card>
            <CardHeader>
              <CardTitle>Skill Levels</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="baselineLevel">Baseline Level</Label>
                  <Select
                    value={formData.baselineLevel?.toString()}
                    onValueChange={(value) => handleLevelChange('baselineLevel', parseInt(value))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {[1, 2, 3, 4, 5].map(level => (
                        <SelectItem key={level} value={level.toString()}>
                          Level {level}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="currentLevel">Current Level</Label>
                  <Select
                    value={formData.currentLevel?.toString()}
                    onValueChange={(value) => handleLevelChange('currentLevel', parseInt(value))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {[1, 2, 3, 4, 5].map(level => (
                        <SelectItem key={level} value={level.toString()}>
                          Level {level}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="targetLevel">Target Level</Label>
                  <Select
                    value={formData.targetLevel?.toString()}
                    onValueChange={(value) => handleLevelChange('targetLevel', parseInt(value))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {[1, 2, 3, 4, 5].map(level => (
                        <SelectItem key={level} value={level.toString()}>
                          Level {level}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Progress Visualization */}
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Progress</span>
                  <span>{Math.round(formData.progressPercentage || 0)}%</span>
                </div>
                <Progress value={formData.progressPercentage || 0} className="h-2" />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>Level {formData.baselineLevel}</span>
                  <span>Level {formData.currentLevel}</span>
                  <span>Level {formData.targetLevel}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Training Completed */}
          <Card>
            <CardHeader>
              <CardTitle>Training Completed</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Input
                  value={newTraining}
                  onChange={(e) => setNewTraining(e.target.value)}
                  placeholder="Enter training program or course name"
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      addTraining();
                    }
                  }}
                />
                <Button
                  type="button"
                  size="sm"
                  onClick={addTraining}
                  disabled={!newTraining.trim()}
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>

              {formData.trainingCompleted && formData.trainingCompleted.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {formData.trainingCompleted.map((training, index) => (
                    <Badge key={index} variant="secondary" className="text-sm">
                      {training}
                      <button
                        type="button"
                        onClick={() => removeTraining(index)}
                        className="ml-2 hover:text-red-500"
                      >
                        ×
                      </button>
                    </Badge>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Next Steps */}
          <Card>
            <CardHeader>
              <CardTitle>Next Steps</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Label htmlFor="nextSteps">Development Plan</Label>
                <Textarea
                  id="nextSteps"
                  value={formData.nextSteps}
                  onChange={(e) => setFormData(prev => ({ 
                    ...prev, 
                    nextSteps: e.target.value 
                  }))}
                  placeholder="Describe the next steps for developing this skill..."
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>

          <DialogFooter className="gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting || !formData.skillName || !formData.category}
              className="min-w-[120px]"
            >
              {isSubmitting ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                  Saving...
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <Save className="h-4 w-4" />
                  Save Skill
                </div>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};