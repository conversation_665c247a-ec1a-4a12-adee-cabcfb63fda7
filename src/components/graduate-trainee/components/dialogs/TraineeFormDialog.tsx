import React, { useState, useEffect } from 'react';
import { X, Save, User, Building, Calendar, DollarSign } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import type { GraduateTraineeProgram } from '@/lib/api/graduateTrainee';

interface TraineeFormDialogProps {
  isOpen: boolean;
  onClose: () => void;
  trainee?: GraduateTraineeProgram | null;
  onSubmit: (data: Partial<GraduateTraineeProgram>) => Promise<void>;
}

interface FormData {
  employeeId: string;
  employeeName: string;
  department: string;
  position: string;
  manager: string;
  mentor?: string;
  startDate: string;
  expectedEndDate: string;
  programDurationMonths: number;
  trainingHoursRequired: number;
  notes?: string;
}

const departments = [
  'Engineering',
  'Marketing', 
  'Sales',
  'HR',
  'Finance',
  'Operations',
  'Product',
  'Design',
  'Legal',
  'Customer Success'
];

const positions = [
  'Software Engineer',
  'Marketing Specialist',
  'Sales Associate',
  'HR Coordinator',
  'Financial Analyst',
  'Operations Manager',
  'Product Manager',
  'UX Designer',
  'Legal Counsel',
  'Customer Success Manager'
];

const managers = [
  'John Smith',
  'Sarah Johnson',
  'Michael Brown',
  'Emily Davis',
  'David Wilson',
  'Lisa Anderson',
  'Robert Taylor',
  'Jennifer Martinez',
  'Christopher Lee',
  'Amanda White'
];

export const TraineeFormDialog: React.FC<TraineeFormDialogProps> = ({
  isOpen,
  onClose,
  trainee,
  onSubmit
}) => {
  const [formData, setFormData] = useState<FormData>({
    employeeId: '',
    employeeName: '',
    department: '',
    position: '',
    manager: '',
    mentor: '',
    startDate: '',
    expectedEndDate: '',
    programDurationMonths: 18,
    trainingHoursRequired: 160,
    notes: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const isEditing = !!trainee;

  useEffect(() => {
    if (trainee) {
      setFormData({
        employeeId: trainee.employeeId,
        employeeName: trainee.employeeName,
        department: trainee.department,
        position: trainee.position,
        manager: trainee.manager,
        mentor: trainee.mentor || '',
        startDate: trainee.startDate,
        expectedEndDate: trainee.expectedEndDate,
        programDurationMonths: trainee.programDurationMonths,
        trainingHoursRequired: trainee.trainingHoursRequired,
        notes: trainee.notes || ''
      });
    } else {
      // Reset form for new trainee
      const today = new Date().toISOString().split('T')[0];
      const endDate = new Date();
      endDate.setMonth(endDate.getMonth() + 18);
      
      setFormData({
        employeeId: '',
        employeeName: '',
        department: '',
        position: '',
        manager: '',
        mentor: '',
        startDate: today,
        expectedEndDate: endDate.toISOString().split('T')[0],
        programDurationMonths: 18,
        trainingHoursRequired: 160,
            notes: ''
      });
    }
    setErrors({});
  }, [trainee, isOpen]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.employeeId.trim()) {
      newErrors.employeeId = 'Employee ID is required';
    }
    if (!formData.employeeName.trim()) {
      newErrors.employeeName = 'Employee name is required';
    }
    if (!formData.department) {
      newErrors.department = 'Department is required';
    }
    if (!formData.position) {
      newErrors.position = 'Position is required';
    }
    if (!formData.manager) {
      newErrors.manager = 'Manager is required';
    }
    if (!formData.startDate) {
      newErrors.startDate = 'Start date is required';
    }
    if (!formData.expectedEndDate) {
      newErrors.expectedEndDate = 'Expected end date is required';
    }
    if (formData.programDurationMonths < 6 || formData.programDurationMonths > 36) {
      newErrors.programDurationMonths = 'Duration must be between 6 and 36 months';
    }
    if (formData.trainingHoursRequired < 40 || formData.trainingHoursRequired > 500) {
      newErrors.trainingHoursRequired = 'Training hours must be between 40 and 500';
    }

    // Date validation
    if (formData.startDate && formData.expectedEndDate) {
      const start = new Date(formData.startDate);
      const end = new Date(formData.expectedEndDate);
      if (end <= start) {
        newErrors.expectedEndDate = 'End date must be after start date';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      await onSubmit(formData);
      onClose();
    } catch (error) {
      console.error('Failed to submit form:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: keyof FormData, value: string | number) => {
    // Convert "none" value back to empty string for mentor field
    const actualValue = field === 'mentor' && value === 'none' ? '' : value;
    setFormData(prev => ({ ...prev, [field]: actualValue }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleDurationChange = (months: number) => {
    setFormData(prev => ({ ...prev, programDurationMonths: months }));
    
    // Auto-calculate end date based on duration
    if (formData.startDate) {
      const start = new Date(formData.startDate);
      const end = new Date(start);
      end.setMonth(end.getMonth() + months);
      setFormData(prev => ({ 
        ...prev, 
        programDurationMonths: months,
        expectedEndDate: end.toISOString().split('T')[0]
      }));
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            {isEditing ? 'Edit Trainee Program' : 'Create New Trainee Program'}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <User className="h-4 w-4" />
                Basic Information
              </CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="employeeId">Employee ID *</Label>
                <Input
                  id="employeeId"
                  value={formData.employeeId}
                  onChange={(e) => handleInputChange('employeeId', e.target.value)}
                  placeholder="EMP0001"
                  className={errors.employeeId ? 'border-red-500' : ''}
                />
                {errors.employeeId && (
                  <p className="text-sm text-red-500">{errors.employeeId}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="employeeName">Full Name *</Label>
                <Input
                  id="employeeName"
                  value={formData.employeeName}
                  onChange={(e) => handleInputChange('employeeName', e.target.value)}
                  placeholder="John Doe"
                  className={errors.employeeName ? 'border-red-500' : ''}
                />
                {errors.employeeName && (
                  <p className="text-sm text-red-500">{errors.employeeName}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="department">Department *</Label>
                <Select
                  value={formData.department}
                  onValueChange={(value) => handleInputChange('department', value)}
                >
                  <SelectTrigger className={errors.department ? 'border-red-500' : ''}>
                    <SelectValue placeholder="Select department" />
                  </SelectTrigger>
                  <SelectContent>
                    {departments.map(dept => (
                      <SelectItem key={dept} value={dept}>{dept}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.department && (
                  <p className="text-sm text-red-500">{errors.department}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="position">Position *</Label>
                <Select
                  value={formData.position}
                  onValueChange={(value) => handleInputChange('position', value)}
                >
                  <SelectTrigger className={errors.position ? 'border-red-500' : ''}>
                    <SelectValue placeholder="Select position" />
                  </SelectTrigger>
                  <SelectContent>
                    {positions.map(pos => (
                      <SelectItem key={pos} value={pos}>{pos}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.position && (
                  <p className="text-sm text-red-500">{errors.position}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="manager">Manager *</Label>
                <Select
                  value={formData.manager}
                  onValueChange={(value) => handleInputChange('manager', value)}
                >
                  <SelectTrigger className={errors.manager ? 'border-red-500' : ''}>
                    <SelectValue placeholder="Select manager" />
                  </SelectTrigger>
                  <SelectContent>
                    {managers.map(mgr => (
                      <SelectItem key={mgr} value={mgr}>{mgr}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.manager && (
                  <p className="text-sm text-red-500">{errors.manager}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="mentor">Mentor (Optional)</Label>
                <Select
                  value={formData.mentor || 'none'}
                  onValueChange={(value) => handleInputChange('mentor', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select mentor" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">No mentor assigned</SelectItem>
                    {managers.map(mgr => (
                      <SelectItem key={mgr} value={mgr}>{mgr}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Program Details */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Calendar className="h-4 w-4" />
                Program Details
              </CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="startDate">Start Date *</Label>
                <Input
                  id="startDate"
                  type="date"
                  value={formData.startDate}
                  onChange={(e) => handleInputChange('startDate', e.target.value)}
                  className={errors.startDate ? 'border-red-500' : ''}
                />
                {errors.startDate && (
                  <p className="text-sm text-red-500">{errors.startDate}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="expectedEndDate">Expected End Date *</Label>
                <Input
                  id="expectedEndDate"
                  type="date"
                  value={formData.expectedEndDate}
                  onChange={(e) => handleInputChange('expectedEndDate', e.target.value)}
                  className={errors.expectedEndDate ? 'border-red-500' : ''}
                />
                {errors.expectedEndDate && (
                  <p className="text-sm text-red-500">{errors.expectedEndDate}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="duration">Program Duration (Months) *</Label>
                <Select
                  value={formData.programDurationMonths.toString()}
                  onValueChange={(value) => handleDurationChange(parseInt(value))}
                >
                  <SelectTrigger className={errors.programDurationMonths ? 'border-red-500' : ''}>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {[6, 9, 12, 15, 18, 24, 30, 36].map(months => (
                      <SelectItem key={months} value={months.toString()}>
                        {months} months
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.programDurationMonths && (
                  <p className="text-sm text-red-500">{errors.programDurationMonths}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="trainingHours">Required Training Hours *</Label>
                <Input
                  id="trainingHours"
                  type="number"
                  min="40"
                  max="500"
                  value={formData.trainingHoursRequired}
                  onChange={(e) => handleInputChange('trainingHoursRequired', parseInt(e.target.value) || 0)}
                  className={errors.trainingHoursRequired ? 'border-red-500' : ''}
                />
                {errors.trainingHoursRequired && (
                  <p className="text-sm text-red-500">{errors.trainingHoursRequired}</p>
                )}
              </div>
            </CardContent>
          </Card>


          {/* Additional Notes */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Additional Notes</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Label htmlFor="notes">Notes (Optional)</Label>
                <Textarea
                  id="notes"
                  value={formData.notes}
                  onChange={(e) => handleInputChange('notes', e.target.value)}
                  placeholder="Any additional information about this trainee program..."
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>

          <DialogFooter className="gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="min-w-[100px]"
            >
              {isSubmitting ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                  Saving...
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <Save className="h-4 w-4" />
                  {isEditing ? 'Update' : 'Create'}
                </div>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};