// Individual Dialog Components (used by UnifiedDialogManager)
export { CertificationDialog } from './CertificationDialog';
export { DeleteConfirmationDialog } from './DeleteConfirmationDialog';
export { FilterDialog } from './FilterDialog';
export { QuarterlyReviewDialog } from './QuarterlyReviewDialog';
export { SkillsDevelopmentDialog } from './SkillsDevelopmentDialog';
export { TraineeDetailDialog } from './TraineeDetailDialog';
export { TraineeFormDialog } from './TraineeFormDialog';

// Main Dialog Manager - Use this for all dialog operations
export { UnifiedDialogManager } from './UnifiedDialogManager';
export type { DialogType } from './UnifiedDialogManager';