import React from 'react';
import {
  User,
  Building,
  Calendar,
  Clock,
  DollarSign,
  Award,
  TrendingUp,
  Edit2,
  Trash2,
  X,
  Mail,
  Phone,
  MapPin
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import StatusBadge from '../common/StatusBadge';
import type { GraduateTraineeProgram } from '@/lib/api/graduateTrainee';

interface TraineeDetailDialogProps {
  isOpen: boolean;
  onClose: () => void;
  trainee: GraduateTraineeProgram;
  onEdit: () => void;
  onDelete: () => void;
}

export const TraineeDetailDialog: React.FC<TraineeDetailDialogProps> = ({
  isOpen,
  onClose,
  trainee,
  onEdit,
  onDelete
}) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const calculateTimeProgress = () => {
    const start = new Date(trainee.startDate);
    const end = new Date(trainee.expectedEndDate);
    const now = new Date();
    
    const totalDuration = end.getTime() - start.getTime();
    const elapsed = now.getTime() - start.getTime();
    
    return Math.min(100, Math.max(0, (elapsed / totalDuration) * 100));
  };

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  const timeProgress = calculateTimeProgress();

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <DialogTitle className="flex items-center gap-3">
            <Avatar className="h-12 w-12">
              <AvatarFallback className="text-lg font-semibold bg-primary/10">
                {getInitials(trainee.employeeName)}
              </AvatarFallback>
            </Avatar>
            <div>
              <h2 className="text-xl font-semibold">{trainee.employeeName}</h2>
              <p className="text-sm text-muted-foreground">{trainee.employeeId}</p>
            </div>
          </DialogTitle>
          <div className="flex items-center gap-2">
            <StatusBadge status={trainee.status} size="md" />
            <Button variant="outline" size="sm" onClick={onEdit}>
              <Edit2 className="h-4 w-4 mr-2" />
              Edit
            </Button>
            <Button variant="outline" size="sm" onClick={onDelete} className="text-red-600 hover:text-red-700">
              <Trash2 className="h-4 w-4 mr-2" />
              Delete
            </Button>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {/* Overview Section */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Overview</h3>
            <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Basic Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="h-4 w-4" />
                    Basic Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Building className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">
                      <strong>Department:</strong> {trainee.department}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">
                      <strong>Position:</strong> {trainee.position}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">
                      <strong>Manager:</strong> {trainee.manager}
                    </span>
                  </div>
                  {trainee.mentor && (
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">
                        <strong>Mentor:</strong> {trainee.mentor}
                      </span>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Program Timeline */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    Program Timeline
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">
                      <strong>Start Date:</strong> {formatDate(trainee.startDate)}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">
                      <strong>Expected End:</strong> {formatDate(trainee.expectedEndDate)}
                    </span>
                  </div>
                  {trainee.actualEndDate && (
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">
                        <strong>Actual End:</strong> {formatDate(trainee.actualEndDate)}
                      </span>
                    </div>
                  )}
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">
                      <strong>Duration:</strong> {trainee.programDurationMonths} months
                    </span>
                  </div>
                </CardContent>
              </Card>


              {/* Training Hours */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    Training Hours
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">Required</span>
                    <span className="font-medium">{trainee.trainingHoursRequired}h</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">Completed</span>
                    <span className="font-medium">{trainee.trainingHoursCompleted}h</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">Remaining</span>
                    <span className="font-medium text-orange-600">
                      {trainee.trainingHoursRequired - trainee.trainingHoursCompleted}h
                    </span>
                  </div>
                  <div className="space-y-1">
                    <div className="flex justify-between text-sm">
                      <span>Progress</span>
                      <span>{Math.round((trainee.trainingHoursCompleted / trainee.trainingHoursRequired) * 100)}%</span>
                    </div>
                    <Progress 
                      value={(trainee.trainingHoursCompleted / trainee.trainingHoursRequired) * 100} 
                      className="h-2" 
                    />
                  </div>
                </CardContent>
              </Card>
            </div>

            {trainee.notes && (
              <Card>
                <CardHeader>
                  <CardTitle>Notes</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">{trainee.notes}</p>
                </CardContent>
              </Card>
            )}
            </div>
          </div>

          <Separator />

          {/* Progress Section */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Progress</h3>
            <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="h-4 w-4" />
                    Overall Progress
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Program Progress</span>
                      <span className="font-medium">{trainee.overallProgress}%</span>
                    </div>
                    <Progress value={trainee.overallProgress} className="h-3" />
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Time Elapsed</span>
                      <span className="font-medium">{Math.round(timeProgress)}%</span>
                    </div>
                    <Progress value={timeProgress} className="h-3" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Skills Development</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {trainee.skillsDevelopment?.map((skill, index) => (
                    <div key={index} className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium">{skill.skillName}</span>
                        <Badge variant="outline" className="text-xs">
                          {skill.category}
                        </Badge>
                      </div>
                      <div className="space-y-1">
                        <div className="flex justify-between text-xs text-muted-foreground">
                          <span>Level {skill.currentLevel} of {skill.targetLevel}</span>
                          <span>{skill.progressPercentage}%</span>
                        </div>
                        <Progress value={skill.progressPercentage} className="h-2" />
                      </div>
                      {skill.nextSteps && (
                        <p className="text-xs text-muted-foreground">
                          Next: {skill.nextSteps}
                        </p>
                      )}
                    </div>
                  ))}
                </CardContent>
              </Card>
            </div>
            </div>
          </div>

          <Separator />

          {/* Reviews Section */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Reviews</h3>
            <div className="space-y-4">
            {trainee.quarterlyReviews?.length > 0 ? (
              trainee.quarterlyReviews.map((review, index) => (
                <Card key={index}>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <span>Quarter {review.reviewNumber} Review</span>
                      <Badge variant={review.completedDate ? "default" : "secondary"}>
                        {review.completedDate ? "Completed" : "Pending"}
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">Due Date:</span>
                        <p>{formatDate(review.dueDate)}</p>
                      </div>
                      {review.completedDate && (
                        <div>
                          <span className="text-muted-foreground">Completed:</span>
                          <p>{formatDate(review.completedDate)}</p>
                        </div>
                      )}
                      <div>
                        <span className="text-muted-foreground">Reviewer:</span>
                        <p>{review.reviewer}</p>
                      </div>
                      {review.overallRating && (
                        <div>
                          <span className="text-muted-foreground">Rating:</span>
                          <p>{review.overallRating}/5</p>
                        </div>
                      )}
                    </div>
                    
                    {review.feedback && (
                      <div>
                        <span className="text-sm font-medium">Feedback:</span>
                        <p className="text-sm text-muted-foreground mt-1">{review.feedback}</p>
                      </div>
                    )}

                    {review.goalsAchieved?.length > 0 && (
                      <div>
                        <span className="text-sm font-medium">Goals Achieved:</span>
                        <ul className="text-sm text-muted-foreground mt-1 list-disc list-inside">
                          {review.goalsAchieved.map((goal, i) => (
                            <li key={i}>{goal}</li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {review.areasForImprovement?.length > 0 && (
                      <div>
                        <span className="text-sm font-medium">Areas for Improvement:</span>
                        <ul className="text-sm text-muted-foreground mt-1 list-disc list-inside">
                          {review.areasForImprovement.map((area, i) => (
                            <li key={i}>{area}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))
            ) : (
              <Card>
                <CardContent className="text-center py-8">
                  <p className="text-muted-foreground">No reviews available yet</p>
                </CardContent>
              </Card>
            )}
            </div>
          </div>

          <Separator />

          {/* Certifications Section */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Certifications</h3>
            <div className="space-y-4">
            {trainee.certifications?.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {trainee.certifications.map((cert, index) => (
                  <Card key={index}>
                    <CardHeader>
                      <CardTitle className="flex items-center justify-between">
                        <span className="flex items-center gap-2">
                          <Award className="h-4 w-4" />
                          {cert.name}
                        </span>
                        <Badge 
                          variant={cert.status === 'completed' ? 'default' : 
                                 cert.status === 'in_progress' ? 'secondary' : 'outline'}
                        >
                          {cert.status.replace('_', ' ')}
                        </Badge>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      <div className="text-sm">
                        <span className="text-muted-foreground">Issuing Body:</span>
                        <p>{cert.issuingBody}</p>
                      </div>
                      {cert.dateAchieved && (
                        <div className="text-sm">
                          <span className="text-muted-foreground">Date Achieved:</span>
                          <p>{formatDate(cert.dateAchieved)}</p>
                        </div>
                      )}
                      {cert.expiryDate && (
                        <div className="text-sm">
                          <span className="text-muted-foreground">Expires:</span>
                          <p>{formatDate(cert.expiryDate)}</p>
                        </div>
                      )}
                      <div className="flex items-center gap-2">
                        <Badge variant={cert.isMandatory ? "destructive" : "outline"} className="text-xs">
                          {cert.isMandatory ? "Mandatory" : "Optional"}
                        </Badge>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <Card>
                <CardContent className="text-center py-8">
                  <p className="text-muted-foreground">No certifications assigned yet</p>
                </CardContent>
              </Card>
            )}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};