import React, { useState } from 'react';
import { Filter, X, RotateCcw } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import type { FilterState } from '../types/trainee.types';

interface FilterDialogProps {
  isOpen: boolean;
  onClose: () => void;
  filters: FilterState;
  onApplyFilters: (filters: FilterState) => void;
  onClearFilters: () => void;
}

const departments = [
  'Engineering',
  'Marketing',
  'Sales', 
  'HR',
  'Finance',
  'Operations',
  'Product',
  'Design',
  'Legal',
  'Customer Success'
];

const statuses = [
  { value: 'not_started', label: 'Not Started' },
  { value: 'in_progress', label: 'In Progress' },
  { value: 'on_hold', label: 'On Hold' },
  { value: 'completed', label: 'Completed' },
  { value: 'terminated', label: 'Terminated' }
];

const managers = [
  'John Smith',
  'Sarah Johnson',
  'Michael Brown',
  '<PERSON>',
  'David Wilson',
  'Lisa Anderson',
  'Robert Taylor',
  'Jennifer Martinez',
  'Christopher Lee',
  'Amanda White'
];

export const FilterDialog: React.FC<FilterDialogProps> = ({
  isOpen,
  onClose,
  filters,
  onApplyFilters,
  onClearFilters
}) => {
  const [tempFilters, setTempFilters] = useState<FilterState>(filters);

  React.useEffect(() => {
    if (isOpen) {
      setTempFilters(filters);
    }
  }, [isOpen, filters]);

  const handleApply = () => {
    onApplyFilters(tempFilters);
    onClose();
  };

  const handleClear = () => {
    const clearedFilters: FilterState = {
      department: 'all',
      status: 'all',
      manager: 'all'
    };
    setTempFilters(clearedFilters);
    onClearFilters();
    onClose();
  };

  const updateFilter = (key: keyof FilterState, value: string) => {
    setTempFilters(prev => ({ ...prev, [key]: value }));
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (tempFilters.department !== 'all') count++;
    if (tempFilters.status !== 'all') count++;
    if (tempFilters.manager !== 'all') count++;
    return count;
  };

  const getActiveFiltersList = () => {
    const activeFilters: Array<{ key: string; value: string; label: string }> = [];
    
    if (tempFilters.department !== 'all') {
      activeFilters.push({
        key: 'department',
        value: tempFilters.department,
        label: `Department: ${tempFilters.department}`
      });
    }
    
    if (tempFilters.status !== 'all') {
      const statusLabel = statuses.find(s => s.value === tempFilters.status)?.label || tempFilters.status;
      activeFilters.push({
        key: 'status',
        value: tempFilters.status,
        label: `Status: ${statusLabel}`
      });
    }
    
    if (tempFilters.manager !== 'all') {
      activeFilters.push({
        key: 'manager',
        value: tempFilters.manager,
        label: `Manager: ${tempFilters.manager}`
      });
    }
    
    return activeFilters;
  };

  const removeFilter = (key: keyof FilterState) => {
    updateFilter(key, 'all');
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Advanced Filters
            {getActiveFiltersCount() > 0 && (
              <Badge variant="secondary" className="ml-2">
                {getActiveFiltersCount()} active
              </Badge>
            )}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Active Filters Summary */}
          {getActiveFiltersList().length > 0 && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Active Filters</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="flex flex-wrap gap-2">
                  {getActiveFiltersList().map((filter) => (
                    <Badge
                      key={filter.key}
                      variant="secondary"
                      className="text-xs flex items-center gap-1"
                    >
                      {filter.label}
                      <button
                        type="button"
                        onClick={() => removeFilter(filter.key as keyof FilterState)}
                        className="ml-1 hover:text-red-500"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Filter Controls */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="department">Department</Label>
              <Select
                value={tempFilters.department}
                onValueChange={(value) => updateFilter('department', value)}
              >
                <SelectTrigger id="department">
                  <SelectValue placeholder="Select department" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Departments</SelectItem>
                  <Separator />
                  {departments.map(dept => (
                    <SelectItem key={dept} value={dept}>
                      {dept}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select
                value={tempFilters.status}
                onValueChange={(value) => updateFilter('status', value)}
              >
                <SelectTrigger id="status">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <Separator />
                  {statuses.map(status => (
                    <SelectItem key={status.value} value={status.value}>
                      {status.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="manager">Manager</Label>
              <Select
                value={tempFilters.manager}
                onValueChange={(value) => updateFilter('manager', value)}
              >
                <SelectTrigger id="manager">
                  <SelectValue placeholder="Select manager" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Managers</SelectItem>
                  <Separator />
                  {managers.map(manager => (
                    <SelectItem key={manager} value={manager}>
                      {manager}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Quick Filter Presets */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Quick Filters</CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="grid grid-cols-2 gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setTempFilters(prev => ({ ...prev, status: 'in_progress' }));
                  }}
                  className="text-xs"
                >
                  Active Programs
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setTempFilters(prev => ({ ...prev, status: 'completed' }));
                  }}
                  className="text-xs"
                >
                  Completed
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setTempFilters(prev => ({ ...prev, status: 'on_hold' }));
                  }}
                  className="text-xs"
                >
                  On Hold
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setTempFilters(prev => ({ ...prev, status: 'not_started' }));
                  }}
                  className="text-xs"
                >
                  Not Started
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        <DialogFooter className="gap-2">
          <Button
            variant="outline"
            onClick={handleClear}
            className="flex items-center gap-2"
          >
            <RotateCcw className="h-4 w-4" />
            Clear All
          </Button>
          <Button
            variant="outline"
            onClick={onClose}
          >
            Cancel
          </Button>
          <Button
            onClick={handleApply}
            className="flex items-center gap-2"
          >
            <Filter className="h-4 w-4" />
            Apply Filters
            {getActiveFiltersCount() > 0 && (
              <Badge variant="secondary" className="ml-1 bg-white text-primary">
                {getActiveFiltersCount()}
              </Badge>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};