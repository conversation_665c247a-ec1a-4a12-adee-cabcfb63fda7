import React from 'react';
import {
  Users,
  TrendingUp,
  Calendar,
  AlertCircle,
  CheckCircle,
  Clock,
  PauseCircle,
  XCircle,
  DollarSign,
  Award,
  Target,
  Activity
} from 'lucide-react';
import { Card } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { cn } from '@/lib/utils';
import type { TraineeMetrics } from '../types/trainee.types';

interface MetricsGridProps {
  metrics: TraineeMetrics;
  className?: string;
}

interface MetricCardProps {
  title: string;
  value: number | string;
  subtitle?: string;
  icon: React.ElementType;
  trend?: 'up' | 'down' | 'neutral';
  trendValue?: string;
  color?: string;
  progress?: number;
}

const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  subtitle,
  icon: Icon,
  trend,
  trendValue,
  color = 'text-primary',
  progress
}) => {
  return (
    <Card className="glass-card p-4 hover:shadow-md transition-all">
      <div className="flex items-start justify-between mb-3">
        <div className={cn("p-2 rounded-lg bg-primary/5", color.replace('text-', 'bg-').replace('500', '50'))}>
          <Icon className={cn("h-4 w-4", color)} />
        </div>
        {trend && (
          <div className={cn(
            "flex items-center gap-1 text-xs px-2 py-1 rounded-full",
            trend === 'up' && "bg-green-50 text-green-600",
            trend === 'down' && "bg-red-50 text-red-600",
            trend === 'neutral' && "bg-gray-50 text-gray-600"
          )}>
            {trend === 'up' && <TrendingUp className="h-3 w-3" />}
            {trend === 'down' && <TrendingUp className="h-3 w-3 rotate-180" />}
            {trend === 'neutral' && <Activity className="h-3 w-3" />}
            {trendValue && <span>{trendValue}</span>}
          </div>
        )}
      </div>
      
      <div className="space-y-1">
        <p className="text-xs text-muted-foreground">{title}</p>
        <p className="text-2xl font-semibold">{value}</p>
        {subtitle && (
          <p className="text-xs text-muted-foreground/70">{subtitle}</p>
        )}
        {progress !== undefined && (
          <Progress value={progress} className="h-1 mt-2" />
        )}
      </div>
    </Card>
  );
};

export const MetricsGrid: React.FC<MetricsGridProps> = ({ metrics, className }) => {
  const statusMetrics = [
    {
      title: 'Active Programs',
      value: metrics.active,
      icon: CheckCircle,
      color: 'text-green-500',
      subtitle: `${Math.round((metrics.active / metrics.total) * 100)}% of total`,
      progress: (metrics.active / metrics.total) * 100
    },
    {
      title: 'On Hold',
      value: metrics.onHold,
      icon: PauseCircle,
      color: 'text-yellow-500',
      subtitle: 'Paused programs'
    },
    {
      title: 'Not Started',
      value: metrics.notStarted,
      icon: Clock,
      color: 'text-gray-500',
      subtitle: 'Pending start'
    },
    {
      title: 'Completed',
      value: metrics.completed,
      icon: Award,
      color: 'text-blue-500',
      subtitle: 'Successfully finished'
    }
  ];

  const performanceMetrics = [
    {
      title: 'Average Progress',
      value: `${metrics.avgProgress}%`,
      icon: Target,
      color: 'text-primary',
      progress: metrics.avgProgress,
      trend: metrics.avgProgress > 50 ? 'up' : 'down',
      trendValue: '+5%'
    },
    {
      title: 'Reviews Due',
      value: metrics.reviewsDue,
      icon: Calendar,
      color: 'text-orange-500',
      subtitle: 'Upcoming reviews',
      trend: metrics.reviewsDue > 5 ? 'down' : 'neutral'
    },
    {
      title: 'Expiring Certs',
      value: metrics.certificationsExpiring,
      icon: AlertCircle,
      color: 'text-red-500',
      subtitle: 'Within 30 days'
    },
    {
      title: 'Budget Used',
      value: `${metrics.budgetUtilization}%`,
      icon: DollarSign,
      color: 'text-green-500',
      progress: metrics.budgetUtilization,
      trend: 'neutral'
    }
  ];

  return (
    <div className={cn("space-y-4", className)}>
      {/* Main Overview */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3">
        <Card className="glass-card p-4 col-span-1 sm:col-span-2 lg:col-span-1">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-xs text-muted-foreground mb-1">Total Trainees</p>
              <p className="text-3xl font-bold">{metrics.total}</p>
              <p className="text-xs text-muted-foreground mt-1">
                All programs
              </p>
            </div>
            <div className="p-3 rounded-full bg-primary/10">
              <Users className="h-6 w-6 text-primary" />
            </div>
          </div>
        </Card>
        
        {statusMetrics.slice(0, 3).map((metric, idx) => (
          <MetricCard key={idx} {...metric} />
        ))}
      </div>

      {/* Performance Metrics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3">
        {performanceMetrics.map((metric, idx) => (
          <MetricCard key={idx} {...metric} />
        ))}
      </div>
    </div>
  );
};