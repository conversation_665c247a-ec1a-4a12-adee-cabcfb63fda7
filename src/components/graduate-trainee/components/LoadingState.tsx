import React from 'react';
import { cn } from '@/lib/utils';

interface LoadingStateProps {
  message?: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const spinnerSizes = {
  sm: 'w-6 h-6',
  md: 'w-8 h-8',
  lg: 'w-10 h-10'
};

const textSizes = {
  sm: 'text-micro',
  md: 'text-mini',
  lg: 'text-sm'
};

export const LoadingState: React.FC<LoadingStateProps> = ({
  message = 'Loading...',
  size = 'md',
  className
}) => {
  return (
    <div className={cn('flex items-center justify-center h-full', className)}>
      <div className="text-center space-y-3">
        <div className={cn(
          'border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto',
          spinnerSizes[size]
        )} />
        <p className={cn('text-muted-foreground', textSizes[size])}>
          {message}
        </p>
      </div>
    </div>
  );
};

export default LoadingState;