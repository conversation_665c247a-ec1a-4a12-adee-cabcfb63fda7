// Export all reusable components from a single entry point

export { default as Container } from './Container';
export { default as PageHeader } from './PageHeader';
export { default as FilterBar } from './FilterBar';
export type { FilterOption, FilterConfig } from './FilterBar';
export { default as StatusBadge, defaultStatusConfigs } from './StatusBadge';
export type { StatusConfig } from './StatusBadge';
export { default as LoadingState } from './LoadingState';
export { default as ErrorState } from './ErrorState';
export { default as TraineeCard } from './TraineeCard';

// Re-export existing components
export { TraineeGrid } from './TraineeGrid';
export type { ViewMode } from './TraineeGrid';
export { TraineeCardVariant } from './TraineeCardVariants';
export { MetricsGrid } from './MetricsGrid';
export { StatCard } from './StatCard';
export { RecentActivity } from './RecentActivity';
export { TraineeTable } from './TraineeTable';