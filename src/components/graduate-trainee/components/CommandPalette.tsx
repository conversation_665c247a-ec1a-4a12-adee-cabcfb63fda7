/**
 * CommandPalette Component - Modern Command Center
 * Quick actions and search with keyboard navigation
 */

import React, { useState, useEffect, useRef, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Search,
  X,
  ChevronRight,
  Users,
  FileText,
  Settings,
  Plus,
  LayoutGrid,
  GraduationCap,
  Calendar,
  TrendingUp,
  Award,
  Filter,
  Download,
  Upload,
  RefreshCw,
  Eye,
  Edit,
  Trash2,
  Command
} from 'lucide-react';
import { GlassInput } from '../core/design-system/components/GlassInput';
import { cn } from '@/lib/utils';
import type { GraduateTraineeProgramExtended } from '../types/adapter';

interface CommandAction {
  id: string;
  label: string;
  icon: React.ReactNode;
  shortcut?: string;
  category?: string;
  action: () => void;
  keywords?: string[];
}

interface CommandPaletteProps {
  isOpen: boolean;
  onClose: () => void;
  actions: CommandAction[];
  trainees?: GraduateTraineeProgramExtended[];
}

export const CommandPalette: React.FC<CommandPaletteProps> = ({
  isOpen,
  onClose,
  actions: externalActions,
  trainees = []
}) => {
  const [search, setSearch] = useState('');
  const [selectedIndex, setSelectedIndex] = useState(0);
  const inputRef = useRef<HTMLInputElement>(null);
  const listRef = useRef<HTMLDivElement>(null);

  // Combine external actions with dynamic trainee actions
  const allActions = useMemo(() => {
    const baseActions: CommandAction[] = [
      {
        id: 'new-trainee',
        label: 'Add New Trainee',
        icon: <Plus className="w-4 h-4" />,
        shortcut: '⌘N',
        category: 'Quick Actions',
        keywords: ['create', 'add', 'new'],
        action: () => {
          // Trigger new trainee dialog
          onClose();
        }
      },
      {
        id: 'export-data',
        label: 'Export Data',
        icon: <Download className="w-4 h-4" />,
        shortcut: '⌘E',
        category: 'Quick Actions',
        keywords: ['download', 'save'],
        action: () => {
          // Trigger export
          onClose();
        }
      },
      {
        id: 'import-data',
        label: 'Import Data',
        icon: <Upload className="w-4 h-4" />,
        shortcut: '⌘I',
        category: 'Quick Actions',
        keywords: ['upload', 'load'],
        action: () => {
          // Trigger import
          onClose();
        }
      },
      {
        id: 'refresh',
        label: 'Refresh Data',
        icon: <RefreshCw className="w-4 h-4" />,
        shortcut: '⌘R',
        category: 'Quick Actions',
        keywords: ['reload', 'sync'],
        action: () => {
          // Trigger refresh
          onClose();
        }
      },
      {
        id: 'filter',
        label: 'Filter Trainees',
        icon: <Filter className="w-4 h-4" />,
        shortcut: '⌘F',
        category: 'View',
        keywords: ['search', 'find'],
        action: () => {
          // Trigger filter
          onClose();
        }
      },
      {
        id: 'calendar',
        label: 'View Calendar',
        icon: <Calendar className="w-4 h-4" />,
        category: 'View',
        keywords: ['schedule', 'dates'],
        action: () => {
          // Open calendar view
          onClose();
        }
      },
      {
        id: 'trends',
        label: 'View Trends',
        icon: <TrendingUp className="w-4 h-4" />,
        category: 'Analytics',
        keywords: ['analytics', 'statistics'],
        action: () => {
          // Open trends
          onClose();
        }
      },
      {
        id: 'achievements',
        label: 'View Achievements',
        icon: <Award className="w-4 h-4" />,
        category: 'Analytics',
        keywords: ['awards', 'certifications'],
        action: () => {
          // Open achievements
          onClose();
        }
      }
    ];

    // Add trainee-specific actions
    const traineeActions: CommandAction[] = trainees.slice(0, 10).map((trainee, index) => ({
      id: `trainee-${trainee.id || trainee.traineeId || index}`,
      label: trainee.employeeName,
      icon: <GraduationCap className="w-4 h-4" />,
      category: 'Trainees',
      keywords: [trainee.department, trainee.position, trainee.status],
      action: () => {
        // Open trainee detail
        onClose();
      }
    }));

    return [...externalActions, ...baseActions, ...traineeActions];
  }, [externalActions, trainees, onClose]);

  // Filter actions based on search
  const filteredActions = useMemo(() => {
    if (!search) return allActions;

    const searchLower = search.toLowerCase();
    return allActions.filter(action => {
      const labelMatch = action.label.toLowerCase().includes(searchLower);
      const keywordMatch = action.keywords?.some(k => k.toLowerCase().includes(searchLower));
      const categoryMatch = action.category?.toLowerCase().includes(searchLower);
      return labelMatch || keywordMatch || categoryMatch;
    });
  }, [search, allActions]);

  // Group actions by category
  const groupedActions = useMemo(() => {
    const groups: Record<string, CommandAction[]> = {};
    filteredActions.forEach(action => {
      const category = action.category || 'General';
      if (!groups[category]) groups[category] = [];
      groups[category].push(action);
    });
    return groups;
  }, [filteredActions]);

  // Keyboard navigation
  useEffect(() => {
    if (!isOpen) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setSelectedIndex(prev => 
            prev < filteredActions.length - 1 ? prev + 1 : 0
          );
          break;
        case 'ArrowUp':
          e.preventDefault();
          setSelectedIndex(prev => 
            prev > 0 ? prev - 1 : filteredActions.length - 1
          );
          break;
        case 'Enter':
          e.preventDefault();
          if (filteredActions[selectedIndex]) {
            filteredActions[selectedIndex].action();
          }
          break;
        case 'Escape':
          e.preventDefault();
          onClose();
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, selectedIndex, filteredActions, onClose]);

  // Focus input when opened
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
      setSearch('');
      setSelectedIndex(0);
    }
  }, [isOpen]);

  // Scroll selected item into view
  useEffect(() => {
    if (listRef.current && selectedIndex >= 0) {
      const items = listRef.current.querySelectorAll('[data-command-item]');
      items[selectedIndex]?.scrollIntoView({ block: 'nearest' });
    }
  }, [selectedIndex]);

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
          />

          {/* Command Palette */}
          <motion.div
            className="fixed inset-x-0 top-[20%] mx-auto max-w-2xl z-50 px-4"
            initial={{ opacity: 0, scale: 0.95, y: -20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: -20 }}
            transition={{ duration: 0.2 }}
          >
            <div className="bg-gray-900/95 backdrop-blur-xl rounded-2xl border border-white/10 shadow-2xl overflow-hidden">
              {/* Search Header */}
              <div className="p-4 border-b border-white/10">
                <div className="flex items-center gap-3">
                  <Search className="w-5 h-5 text-white/40" />
                  <input
                    ref={inputRef}
                    type="text"
                    value={search}
                    onChange={(e) => setSearch(e.target.value)}
                    placeholder="Type a command or search..."
                    className="flex-1 bg-transparent text-white placeholder-white/40 focus:outline-none"
                  />
                  <button
                    onClick={onClose}
                    className="p-1 rounded-lg hover:bg-white/10 transition-colors"
                  >
                    <X className="w-4 h-4 text-white/60" />
                  </button>
                </div>
              </div>

              {/* Actions List */}
              <div
                ref={listRef}
                className="max-h-[400px] overflow-y-auto custom-scrollbar p-2"
              >
                {Object.keys(groupedActions).length === 0 ? (
                  <div className="text-center py-8 text-white/40">
                    No commands found
                  </div>
                ) : (
                  <div className="space-y-4">
                    {Object.entries(groupedActions).map(([category, categoryActions]) => (
                      <div key={category}>
                        <div className="px-2 py-1 text-xs font-semibold text-white/40 uppercase tracking-wider">
                          {category}
                        </div>
                        <div className="mt-1 space-y-1">
                          {categoryActions.map((action, index) => {
                            const globalIndex = filteredActions.indexOf(action);
                            const isSelected = globalIndex === selectedIndex;

                            return (
                              <motion.button
                                key={action.id}
                                data-command-item
                                className={cn(
                                  'w-full px-3 py-2 rounded-lg flex items-center gap-3',
                                  'text-left transition-all duration-150',
                                  isSelected
                                    ? 'bg-primary-500/20 text-white border border-primary-500/30'
                                    : 'text-white/70 hover:bg-white/5 hover:text-white'
                                )}
                                onClick={action.action}
                                onMouseEnter={() => setSelectedIndex(globalIndex)}
                                whileHover={{ x: 4 }}
                              >
                                <span className={cn(
                                  'flex-shrink-0',
                                  isSelected ? 'text-primary-400' : 'text-white/50'
                                )}>
                                  {action.icon}
                                </span>
                                <span className="flex-1">{action.label}</span>
                                {action.shortcut && (
                                  <kbd className="px-2 py-0.5 text-xs bg-white/10 rounded">
                                    {action.shortcut}
                                  </kbd>
                                )}
                                {isSelected && (
                                  <ChevronRight className="w-4 h-4 text-primary-400" />
                                )}
                              </motion.button>
                            );
                          })}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Footer */}
              <div className="p-3 border-t border-white/10 flex items-center justify-between text-xs text-white/40">
                <div className="flex items-center gap-4">
                  <span className="flex items-center gap-1">
                    <kbd className="px-1.5 py-0.5 bg-white/10 rounded">↑↓</kbd>
                    Navigate
                  </span>
                  <span className="flex items-center gap-1">
                    <kbd className="px-1.5 py-0.5 bg-white/10 rounded">Enter</kbd>
                    Select
                  </span>
                  <span className="flex items-center gap-1">
                    <kbd className="px-1.5 py-0.5 bg-white/10 rounded">Esc</kbd>
                    Close
                  </span>
                </div>
                <span className="flex items-center gap-1">
                  <Command className="w-3 h-3" />
                  Command Palette
                </span>
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};