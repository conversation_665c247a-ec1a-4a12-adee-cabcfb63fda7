import React from 'react';
import { Card, CardHeader, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { TrendingUp, Target } from 'lucide-react';
import { SkillDevelopment } from '@/lib/api/graduateTrainee';

interface SkillsDevelopmentChartProps {
  skills: SkillDevelopment[];
  traineeName: string;
}

export const SkillsDevelopmentChart: React.FC<SkillsDevelopmentChartProps> = ({
  skills,
  traineeName
}) => {
  if (!skills || skills.length === 0) {
    return (
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Skills Development</h3>
        </CardHeader>
        <CardContent>
          <p className="text-gray-500 text-center py-8">No skills development data available.</p>
        </CardContent>
      </Card>
    );
  }

  // Group skills by category for better organization
  const skillsByCategory: Record<string, SkillDevelopment[]> = {};
  skills.forEach(skill => {
    if (!skillsByCategory[skill.category]) {
      skillsByCategory[skill.category] = [];
    }
    skillsByCategory[skill.category].push(skill);
  });

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Skills Development</h3>
          <Badge variant="secondary" className="flex items-center gap-1">
            <TrendingUp className="h-4 w-4" />
            {skills.length} Skills
          </Badge>
        </div>
        <p className="text-sm text-gray-600">Tracking progress for {traineeName}</p>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {Object.entries(skillsByCategory).map(([category, categorySkills]) => (
            <div key={category}>
              <h4 className="font-medium text-gray-700 mb-3 flex items-center gap-2">
                <Target className="h-4 w-4" />
                {category}
              </h4>
              <div className="space-y-4">
                {categorySkills.map((skill, index) => (
                  <div key={index} className="border-l-4 border-blue-200 pl-4 py-2">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h5 className="font-medium">{skill.skillName}</h5>
                        <p className="text-sm text-gray-600">
                          Level: {skill.currentLevel} → {skill.targetLevel}
                        </p>
                      </div>
                      <Badge variant="secondary">{skill.progressPercentage}%</Badge>
                    </div>
                    <div className="mt-2">
                      <div className="flex justify-between text-xs mb-1">
                        <span>Progress</span>
                        <span>{skill.progressPercentage}%</span>
                      </div>
                      <Progress value={skill.progressPercentage} className="h-2" />
                    </div>
                    {skill.nextSteps && (
                      <p className="text-sm text-gray-600 mt-2">
                        <strong>Next steps:</strong> {skill.nextSteps}
                      </p>
                    )}
                    {skill.trainingCompleted && skill.trainingCompleted.length > 0 && (
                      <div className="mt-2">
                        <p className="text-xs text-gray-500">Completed training:</p>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {skill.trainingCompleted.map((training, idx) => (
                            <Badge key={idx} variant="outline" className="text-xs">
                              {training}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};