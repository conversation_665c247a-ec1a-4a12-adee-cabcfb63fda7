import React from 'react';
import {
  MoreVertical,
  Eye,
  Edit2,
  Trash2,
  Calendar,
  TrendingUp,
  Award,
  FileText,
  Download,
  UserPlus
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { useTraineeStore } from '../../store/traineeStore';
import type { GraduateTraineeProgram } from '@/lib/api/graduateTrainee';

interface ActionMenuProps {
  trainee?: GraduateTraineeProgram;
  variant?: 'card' | 'table' | 'header';
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
}

export const ActionMenu: React.FC<ActionMenuProps> = ({
  trainee,
  variant = 'card',
  size = 'md',
  showLabel = false
}) => {
  const { selectTrainee, openDialog } = useTraineeStore();

  const handleAction = (action: string, e?: React.MouseEvent) => {
    e?.stopPropagation();
    
    if (trainee) {
      selectTrainee(trainee);
    }

    switch (action) {
      case 'view':
        openDialog('view');
        break;
      case 'edit':
        openDialog('edit');
        break;
      case 'delete':
        openDialog('delete');
        break;
      case 'review':
        openDialog('quarterlyReview');
        break;
      case 'skills':
        openDialog('skillsDevelopment');
        break;
      case 'certification':
        openDialog('certification');
        break;
      case 'create':
        selectTrainee(null);
        openDialog('create');
        break;
      case 'export':
        // Handle export logic
        console.log('Export action');
        break;
      case 'reports':
        // Handle reports logic
        console.log('Reports action');
        break;
    }
  };

  const buttonSize = size === 'sm' ? 'sm' : size === 'lg' ? 'lg' : 'sm';
  const iconSize = size === 'sm' ? 'h-3 w-3' : size === 'lg' ? 'h-5 w-5' : 'h-4 w-4';

  // Header actions (when no specific trainee is selected)
  if (variant === 'header') {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size={buttonSize} className="gap-2">
            <MoreVertical className={iconSize} />
            {showLabel && 'Actions'}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48">
          <DropdownMenuLabel>Quick Actions</DropdownMenuLabel>
          <DropdownMenuSeparator />
          
          <DropdownMenuItem onClick={(e) => handleAction('create', e)}>
            <UserPlus className="h-4 w-4 mr-2" />
            Add New Trainee
          </DropdownMenuItem>
          
          <DropdownMenuItem onClick={(e) => handleAction('export', e)}>
            <Download className="h-4 w-4 mr-2" />
            Export Data
          </DropdownMenuItem>
          
          <DropdownMenuItem onClick={(e) => handleAction('reports', e)}>
            <FileText className="h-4 w-4 mr-2" />
            Generate Report
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  // Trainee-specific actions
  if (!trainee) return null;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="ghost" 
          size={buttonSize} 
          className={`${variant === 'card' ? 'opacity-0 group-hover:opacity-100' : ''} transition-opacity`}
        >
          <MoreVertical className={iconSize} />
          {showLabel && <span className="sr-only">Actions</span>}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuLabel>{trainee.employeeName}</DropdownMenuLabel>
        <DropdownMenuSeparator />
        
        <DropdownMenuItem onClick={(e) => handleAction('view', e)}>
          <Eye className="h-4 w-4 mr-2" />
          View Details
        </DropdownMenuItem>
        
        <DropdownMenuItem onClick={(e) => handleAction('edit', e)}>
          <Edit2 className="h-4 w-4 mr-2" />
          Edit Program
        </DropdownMenuItem>
        
        <DropdownMenuSeparator />
        
        <DropdownMenuItem onClick={(e) => handleAction('review', e)}>
          <Calendar className="h-4 w-4 mr-2" />
          Schedule Review
        </DropdownMenuItem>
        
        <DropdownMenuItem onClick={(e) => handleAction('skills', e)}>
          <TrendingUp className="h-4 w-4 mr-2" />
          Update Skills
        </DropdownMenuItem>
        
        <DropdownMenuItem onClick={(e) => handleAction('certification', e)}>
          <Award className="h-4 w-4 mr-2" />
          Add Certification
        </DropdownMenuItem>
        
        <DropdownMenuSeparator />
        
        <DropdownMenuItem 
          onClick={(e) => handleAction('delete', e)}
          className="text-red-600 focus:text-red-600"
        >
          <Trash2 className="h-4 w-4 mr-2" />
          Delete Program
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default ActionMenu;