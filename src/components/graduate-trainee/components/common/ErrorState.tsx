import React from 'react';
import { AlertCircle, RefreshCw, Home } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface ErrorStateProps {
  title?: string;
  message?: string;
  onRetry?: () => void;
  onGoHome?: () => void;
  showIcon?: boolean;
  className?: string;
}

export const ErrorState: React.FC<ErrorStateProps> = ({
  title = 'Something went wrong',
  message = 'An error occurred while loading the data',
  onRetry,
  onGoHome,
  showIcon = true,
  className
}) => {
  return (
    <div className={cn('flex items-center justify-center h-full', className)}>
      <div className="text-center space-y-3 max-w-md">
        {showIcon && (
          <AlertCircle className="w-10 h-10 text-red-500 mx-auto" />
        )}
        <h3 className="text-sm font-medium">{title}</h3>
        <p className="text-mini text-muted-foreground">{message}</p>
        <div className="flex items-center justify-center gap-2">
          {onRetry && (
            <Button
              onClick={onRetry}
              size="sm"
              variant="outline"
              className="text-mini"
            >
              <RefreshCw className="icon-micro mr-1" />
              Retry
            </Button>
          )}
          {onGoHome && (
            <Button
              onClick={onGoHome}
              size="sm"
              variant="outline"
              className="text-mini"
            >
              <Home className="icon-micro mr-1" />
              Go Home
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default ErrorState;