import React from 'react';
import { <PERSON>, <PERSON><PERSON>eader, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  User, 
  Building, 
  Clock, 
  Target, 
  CheckCircle,
  AlertCircle,
  XCircle
} from 'lucide-react';
import { GraduateTraineeProgram } from '@/lib/api/graduateTrainee';

interface TraineeCardProps {
  trainee: GraduateTraineeProgram;
  onView: (trainee: GraduateTraineeProgram) => void;
  onEdit: (trainee: GraduateTraineeProgram) => void;
}

export const TraineeCard: React.FC<TraineeCardProps> = ({
  trainee,
  onView,
  onEdit
}) => {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'terminated': return <XCircle className="h-4 w-4 text-red-500" />;
      case 'on_hold': return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      default: return <Clock className="h-4 w-4 text-blue-500" />;
    }
  };

  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'completed': return 'success';
      case 'terminated': return 'destructive';
      case 'on_hold': return 'warning';
      default: return 'secondary';
    }
  };

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-2">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
              <User className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <h3 className="font-semibold text-sm">{trainee.employeeName}</h3>
              <p className="text-xs text-gray-500">{trainee.employeeId}</p>
            </div>
          </div>
          <Badge variant={getStatusVariant(trainee.status) as any} className="flex items-center gap-1 text-xs">
            {getStatusIcon(trainee.status)}
            {trainee.status.replace('_', ' ')}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div className="flex items-center gap-2 text-sm">
            <Building className="h-4 w-4 text-gray-500" />
            <span className="truncate">{trainee.department}</span>
          </div>
          
          <div className="flex items-center gap-2 text-sm">
            <User className="h-4 w-4 text-gray-500" />
            <span className="truncate">Manager: {trainee.manager}</span>
          </div>
          
          <div className="flex items-center gap-2 text-sm">
            <Clock className="h-4 w-4 text-gray-500" />
            <span>{trainee.programDurationMonths} months</span>
          </div>
          
          <div className="pt-2">
            <div className="flex justify-between text-xs mb-1">
              <span>Progress</span>
              <span>{Math.round((trainee.overallProgress || 0) * 100)}%</span>
            </div>
            <Progress value={(trainee.overallProgress || 0) * 100} className="h-2" />
          </div>
          
          <div className="flex gap-2 pt-2">
            <Button 
              variant="outline" 
              size="sm" 
              className="flex-1 text-xs"
              onClick={() => onView(trainee)}
            >
              View
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              className="flex-1 text-xs"
              onClick={() => onEdit(trainee)}
            >
              Edit
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};