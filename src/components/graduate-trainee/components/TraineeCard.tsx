import React from 'react';
import { MoreVertical, Eye, Edit2, Building, User, Calendar } from 'lucide-react';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import { StatusBadge, defaultStatusConfigs } from './StatusBadge';
import { cn } from '@/lib/utils';
import type { GraduateTraineeProgram } from '@/lib/api/graduateTrainee';

interface TraineeCardProps {
  trainee: GraduateTraineeProgram;
  onView: () => void;
  onEdit: () => void;
  onDelete: () => void;
  variant?: 'default' | 'compact' | 'detailed';
  className?: string;
}

export const TraineeCard: React.FC<TraineeCardProps> = React.memo(({
  trainee,
  onView,
  onEdit,
  onDelete,
  variant = 'default',
  className
}) => {
  const progressColor = trainee.overallProgress >= 80 ? 'bg-green-500' :
                        trainee.overallProgress >= 60 ? 'bg-blue-500' :
                        trainee.overallProgress >= 40 ? 'bg-yellow-500' :
                        trainee.overallProgress >= 20 ? 'bg-orange-500' : 'bg-red-500';

  const initials = trainee.employeeName
    .split(' ')
    .map(n => n[0])
    .join('')
    .toUpperCase();

  if (variant === 'compact') {
    return (
      <div className={cn('glass-card p-3 glass-card-interactive group', className)}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Avatar className="h-8 w-8">
              <AvatarFallback className="text-micro bg-primary/10">
                {initials}
              </AvatarFallback>
            </Avatar>
            <div>
              <p className="text-sm font-medium">{trainee.employeeName}</p>
              <p className="text-micro text-muted-foreground">{trainee.employeeId}</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <StatusBadge status={trainee.status} size="xs" />
            <Button
              variant="ghost"
              size="sm"
              onClick={onView}
              className="h-7 w-7 p-0"
            >
              <Eye className="h-3 w-3" />
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn('glass-card trainee-card-minimal glass-card-interactive group', className)}>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Avatar className="trainee-avatar-small">
            <AvatarFallback className="text-micro bg-primary/10">
              {initials}
            </AvatarFallback>
          </Avatar>
          <div className="space-y-0.5">
            <div className="flex items-center gap-2">
              <h3 className="trainee-name">{trainee.employeeName}</h3>
              <span className="trainee-id">{trainee.employeeId}</span>
            </div>
            <div className="flex items-center gap-3 text-micro text-muted-foreground">
              <span className="flex items-center gap-1">
                <Building className="icon-micro" />
                {trainee.department}
              </span>
              <span className="flex items-center gap-1">
                <User className="icon-micro" />
                {trainee.manager}
              </span>
              <span className="flex items-center gap-1">
                <Calendar className="icon-micro" />
                {trainee.programDurationMonths} months
              </span>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-4">
          <div className="text-right">
            <div className="flex items-center gap-2 mb-1">
              <StatusBadge status={trainee.status} />
            </div>
            <div className="flex items-center gap-2">
              <span className="text-micro text-muted-foreground">Progress</span>
              <div className="w-20">
                <div className="progress-micro">
                  <div 
                    className={cn('progress-micro-fill', progressColor)}
                    style={{ width: `${trainee.overallProgress}%` }}
                  />
                </div>
              </div>
              <span className="text-micro font-medium">{trainee.overallProgress}%</span>
            </div>
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button 
                variant="ghost" 
                size="sm" 
                className="h-7 w-7 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
              >
                <MoreVertical className="icon-mini" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-32">
              <DropdownMenuItem onClick={onView} className="text-mini gap-2">
                <Eye className="icon-micro" />
                View Details
              </DropdownMenuItem>
              <DropdownMenuItem onClick={onEdit} className="text-mini gap-2">
                <Edit2 className="icon-micro" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={onDelete} className="text-mini gap-2 text-red-500">
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </div>
  );
});

TraineeCard.displayName = 'TraineeCard';

export default TraineeCard;