import React from 'react';
import {
  PlayCircle,
  CheckCircle,
  PauseCircle,
  Clock,
  XCircle,
  AlertCircle,
  LucideIcon
} from 'lucide-react';
import { cn } from '@/lib/utils';

export interface StatusConfig {
  icon: LucideIcon;
  color: string;
  bg: string;
  dot: string;
  label: string;
}

export const defaultStatusConfigs: Record<string, StatusConfig> = {
  in_progress: {
    icon: PlayCircle,
    color: 'text-blue-400',
    bg: 'bg-blue-400/10',
    dot: 'bg-blue-400',
    label: 'In Progress'
  },
  completed: {
    icon: CheckCircle,
    color: 'text-green-400',
    bg: 'bg-green-400/10',
    dot: 'bg-green-400',
    label: 'Completed'
  },
  on_hold: {
    icon: PauseCircle,
    color: 'text-yellow-400',
    bg: 'bg-yellow-400/10',
    dot: 'bg-yellow-400',
    label: 'On Hold'
  },
  not_started: {
    icon: Clock,
    color: 'text-gray-400',
    bg: 'bg-gray-400/10',
    dot: 'bg-gray-400',
    label: 'Not Started'
  },
  terminated: {
    icon: XCircle,
    color: 'text-red-400',
    bg: 'bg-red-400/10',
    dot: 'bg-red-400',
    label: 'Terminated'
  },
  pending: {
    icon: AlertCircle,
    color: 'text-orange-400',
    bg: 'bg-orange-400/10',
    dot: 'bg-orange-400',
    label: 'Pending'
  }
};

interface StatusBadgeProps {
  status: string;
  statusConfig?: StatusConfig;
  showIcon?: boolean;
  showDot?: boolean;
  size?: 'xs' | 'sm' | 'md';
  className?: string;
}

const sizeClasses = {
  xs: 'text-micro px-1.5 py-0.5',
  sm: 'text-mini px-2 py-0.5',
  md: 'text-xs px-2.5 py-1'
};

export const StatusBadge: React.FC<StatusBadgeProps> = ({
  status,
  statusConfig,
  showIcon = false,
  showDot = true,
  size = 'sm',
  className
}) => {
  const config = statusConfig || defaultStatusConfigs[status] || defaultStatusConfigs.not_started;
  const Icon = config.icon;

  return (
    <span className={cn(
      'inline-flex items-center gap-1.5 rounded-full font-medium',
      config.bg,
      config.color,
      sizeClasses[size],
      className
    )}>
      {showDot && <span className={cn('w-1.5 h-1.5 rounded-full', config.dot)} />}
      {showIcon && <Icon className="w-3 h-3" />}
      {config.label}
    </span>
  );
};

export default StatusBadge;