/**
 * Graduate Trainee Module - Type Definitions
 * Central type definitions for the entire module
 */

import type { 
  GraduateTraineeProgram,
  TraineeStatus,
  QuarterlyReview,
  SkillDevelopment,
  Certification,
  CertificationStatus
} from '@/lib/api/graduateTrainee';

// Re-export API types
export type {
  GraduateTraineeProgram,
  TraineeStatus,
  QuarterlyReview,
  SkillDevelopment,
  Certification,
  CertificationStatus
};

// UI State Types
export interface UIState {
  activeTab: TabType;
  viewMode: ViewMode;
  isLoading: boolean;
  error: string | null;
}

export type TabType = 'dashboard' | 'trainees' | 'progress' | 'reports' | 'settings';
export type ViewMode = 'grid' | 'list' | 'kanban' | 'compact';

// Filter Types
export interface FilterState {
  department: string;
  status: string;
  manager: string;
  dateRange?: DateRange;
  searchQuery?: string;
}

export interface DateRange {
  start: Date;
  end: Date;
}

// Dialog Types
export type DialogType = 
  | 'create'
  | 'edit'
  | 'view'
  | 'delete'
  | 'quarterlyReview'
  | 'certification'
  | 'skillsDevelopment'
  | 'filter'
  | 'export';

export interface DialogState {
  isOpen: boolean;
  type: DialogType | null;
  data?: any;
}

// Metrics Types
export interface TraineeMetrics {
  total: number;
  active: number;
  completed: number;
  onHold: number;
  notStarted: number;
  terminated: number;
  avgProgress: number;
  reviewsDue: number;
  certificationsExpiring: number;
}

// Chart Data Types
export interface ChartDataPoint {
  label: string;
  value: number;
  color?: string;
}

export interface ProgressChartData {
  traineeId: number;
  traineeName: string;
  progress: number;
  trend: 'up' | 'down' | 'stable';
}

// Action Types for State Management
export type TraineeAction =
  | { type: 'SET_TRAINEES'; payload: GraduateTraineeProgram[] }
  | { type: 'ADD_TRAINEE'; payload: GraduateTraineeProgram }
  | { type: 'UPDATE_TRAINEE'; payload: { id: number; data: Partial<GraduateTraineeProgram> } }
  | { type: 'DELETE_TRAINEE'; payload: number }
  | { type: 'SET_FILTER'; payload: Partial<FilterState> }
  | { type: 'RESET_FILTERS' }
  | { type: 'SET_UI_STATE'; payload: Partial<UIState> }
  | { type: 'SET_DIALOG'; payload: DialogState }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_LOADING'; payload: boolean };

// Form Types
export interface TraineeFormData {
  employeeId: string;
  employeeName: string;
  department: string;
  position: string;
  manager: string;
  mentor?: string;
  startDate: string;
  expectedEndDate: string;
  programDurationMonths: number;
  trainingHoursRequired: number;
}

export interface ReviewFormData {
  traineeId: number;
  reviewNumber: number;
  dueDate: string;
  reviewer: string;
  overallRating: number;
  feedback: string;
  goalsAchieved: string[];
  areasForImprovement: string[];
  continuationRecommendation: boolean;
}

// Validation Types
export interface ValidationError {
  field: string;
  message: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

// Export Types
export type ExportFormat = 'csv' | 'excel' | 'pdf' | 'json';

export interface ExportOptions {
  format: ExportFormat;
  includeArchived: boolean;
  dateRange?: DateRange;
  fields?: string[];
}

// Notification Types
export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number;
}

// Permission Types
export interface UserPermissions {
  canCreate: boolean;
  canEdit: boolean;
  canDelete: boolean;
  canExport: boolean;
  canViewReports: boolean;
  canManageSettings: boolean;
}

// Cache Types
export interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

export interface CacheState {
  trainees: CacheEntry<GraduateTraineeProgram[]> | null;
  metrics: CacheEntry<TraineeMetrics> | null;
  lastSync: number;
}