import { GraduateTraineeProgram, TraineeStatus } from '@/lib/api/graduateTrainee';

// Re-export types from trainee.types.ts
export * from './trainee.types';

export interface TraineeFilterState {
  searchTerm: string;
  department: string;
  status: TraineeStatus | 'all';
  manager: string;
  startDateFrom?: Date;
  startDateTo?: Date;
}

export interface TraineeSortConfig {
  key: keyof GraduateTraineeProgram | 'none';
  direction: 'asc' | 'desc';
}

export interface TraineeTableColumn {
  key: string;
  header: string;
  sortable?: boolean;
  width?: string;
}

export interface TraineeAction {
  type: 'view' | 'edit' | 'delete' | 'export' | 'review';
  trainee: GraduateTraineeProgram;
}

// TraineeFormData is defined in trainee.types.ts
// Import from './trainee.types' when needed

export interface TraineeValidationError {
  field: string;
  message: string;
}

export interface TraineeSearchResult {
  trainee: GraduateTraineeProgram;
  score: number;
  matches: Array<{
    field: string;
    value: string;
  }>;
}

export interface TraineeDashboardConfig {
  showMetrics: boolean;
  showCharts: boolean;
  showRecentActivity: boolean;
  defaultView: 'grid' | 'table';
  itemsPerPage: number;
}

export interface TraineeNotification {
  id: string;
  type: 'info' | 'warning' | 'error' | 'success';
  title: string;
  message: string;
  traineeId?: string;
  createdAt: Date;
  read: boolean;
}

export interface TraineeChartData {
  labels: string[];
  datasets: Array<{
    label: string;
    data: number[];
    backgroundColor?: string;
    borderColor?: string;
    fill?: boolean;
  }>;
}

export interface TraineeExportOptions {
  format: 'csv' | 'json' | 'pdf';
  includeSkills: boolean;
  includeReviews: boolean;
  includeCertifications: boolean;
  dateRange?: {
    from: Date;
    to: Date;
  };
}