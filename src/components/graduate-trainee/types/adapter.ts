/**
 * Type Adapter for Graduate Trainee
 * Handles field name differences between API and mock data
 */

import type { GraduateTraineeProgram } from '@/lib/api/graduateTrainee';

// Extended type that includes both API fields and mock data fields
export interface GraduateTraineeProgramExtended extends GraduateTraineeProgram {
  // Mock data compatibility fields
  traineeId?: number;
  currentProgress?: number;
}

// Helper to normalize trainee data
export function normalizeTrainee(trainee: any): GraduateTraineeProgramExtended {
  return {
    ...trainee,
    // Map traineeId to id if needed
    id: trainee.id || trainee.traineeId,
    traineeId: trainee.traineeId || trainee.id,
    // Map currentProgress to overallProgress if needed
    overallProgress: trainee.overallProgress ?? trainee.currentProgress ?? 0,
    currentProgress: trainee.currentProgress ?? trainee.overallProgress ?? 0,
    // Ensure required fields have defaults
    quarterlyReviews: trainee.quarterlyReviews || [],
    skillsDevelopment: trainee.skillsDevelopment || [],
    certifications: trainee.certifications || [],
  };
}

// Helper to normalize array of trainees
export function normalizeTrainees(trainees: any[]): GraduateTraineeProgramExtended[] {
  return trainees.map(normalizeTrainee);
}