import { GraduateTraineeProgram, QuarterlyReview, SkillDevelopment, Certification, graduateTraineeApi } from '@/lib/api/graduateTrainee';

export class TraineeIntegrationService {
  private static instance: TraineeIntegrationService;
  private subscribers: Map<string, Set<Function>> = new Map();
  private cache: Map<string, any> = new Map();

  private constructor() {}

  static getInstance(): TraineeIntegrationService {
    if (!TraineeIntegrationService.instance) {
      TraineeIntegrationService.instance = new TraineeIntegrationService();
    }
    return TraineeIntegrationService.instance;
  }

  // Event Subscription
  subscribe(event: string, callback: Function) {
    if (!this.subscribers.has(event)) {
      this.subscribers.set(event, new Set());
    }
    this.subscribers.get(event)!.add(callback);
    
    return () => {
      this.subscribers.get(event)?.delete(callback);
    };
  }

  emit(event: string, data?: any) {
    const callbacks = this.subscribers.get(event);
    if (callbacks) {
      callbacks.forEach(callback => callback(data));
    }
  }

  // Cache Management
  setCache(key: string, value: any) {
    this.cache.set(key, value);
    this.emit('cache:updated', { key, value });
  }

  getCache(key: string) {
    return this.cache.get(key);
  }

  clearCache(key?: string) {
    if (key) {
      this.cache.delete(key);
    } else {
      this.cache.clear();
    }
    this.emit('cache:cleared', key);
  }

  // Data Synchronization
  async syncTraineeData(traineeId: string) {
    try {
      const program = await graduateTraineeApi.getProgram(parseInt(traineeId));
      this.setCache(`trainee:${traineeId}`, program);
      this.emit('trainee:updated', program);
      return program;
    } catch (error) {
      this.emit('error', { type: 'sync', error });
      throw error;
    }
  }

  async syncAllTrainees() {
    try {
      const programs = await graduateTraineeApi.getAllPrograms();
      this.setCache('trainees:all', programs);
      this.emit('trainees:synced', programs);
      return programs;
    } catch (error) {
      this.emit('error', { type: 'sync:all', error });
      throw error;
    }
  }

  // Review Management
  async addQuarterlyReview(traineeId: string, review: QuarterlyReview) {
    try {
      const program = await this.getTraineeProgram(traineeId);
      if (!program) throw new Error('Trainee not found');
      
      const updatedProgram = await graduateTraineeApi.updateProgram({
        id: parseInt(traineeId),
        quarterlyReviews: [...program.quarterlyReviews, review]
      });
      
      this.setCache(`trainee:${traineeId}`, updatedProgram);
      this.emit('review:added', { traineeId, review });
      return updatedProgram;
    } catch (error) {
      this.emit('error', { type: 'review:add', error });
      throw error;
    }
  }

  async updateQuarterlyReview(traineeId: string, reviewNumber: number, review: Partial<QuarterlyReview>) {
    try {
      const program = await this.getTraineeProgram(traineeId);
      if (!program) throw new Error('Trainee not found');
      
      const updatedReviews = program.quarterlyReviews.map(r => 
        r.reviewNumber === reviewNumber ? { ...r, ...review } : r
      );
      
      const updatedProgram = await graduateTraineeApi.updateProgram({
        id: parseInt(traineeId),
        quarterlyReviews: updatedReviews
      });
      
      this.setCache(`trainee:${traineeId}`, updatedProgram);
      this.emit('review:updated', { traineeId, reviewNumber, review });
      return updatedProgram;
    } catch (error) {
      this.emit('error', { type: 'review:update', error });
      throw error;
    }
  }

  // Skills Management
  async updateSkillDevelopment(traineeId: string, skill: SkillDevelopment) {
    try {
      const program = await this.getTraineeProgram(traineeId);
      if (!program) throw new Error('Trainee not found');
      
      const existingIndex = program.skillsDevelopment.findIndex(s => s.skillName === skill.skillName);
      const updatedSkills = [...program.skillsDevelopment];
      
      if (existingIndex >= 0) {
        updatedSkills[existingIndex] = skill;
      } else {
        updatedSkills.push(skill);
      }
      
      const updatedProgram = await graduateTraineeApi.updateProgram({
        id: parseInt(traineeId),
        skillsDevelopment: updatedSkills
      });
      
      this.setCache(`trainee:${traineeId}`, updatedProgram);
      this.emit('skill:updated', { traineeId, skill });
      return updatedProgram;
    } catch (error) {
      this.emit('error', { type: 'skill:update', error });
      throw error;
    }
  }

  // Certification Management
  async updateCertification(traineeId: string, certification: Certification) {
    try {
      const program = await this.getTraineeProgram(traineeId);
      if (!program) throw new Error('Trainee not found');
      
      const existingIndex = program.certifications.findIndex(c => c.name === certification.name);
      const updatedCertifications = [...program.certifications];
      
      if (existingIndex >= 0) {
        updatedCertifications[existingIndex] = certification;
      } else {
        updatedCertifications.push(certification);
      }
      
      const updatedProgram = await graduateTraineeApi.updateProgram({
        id: parseInt(traineeId),
        certifications: updatedCertifications
      });
      
      this.setCache(`trainee:${traineeId}`, updatedProgram);
      this.emit('certification:updated', { traineeId, certification });
      return updatedProgram;
    } catch (error) {
      this.emit('error', { type: 'certification:update', error });
      throw error;
    }
  }

  // Progress Tracking
  async updateProgress(traineeId: string, progress: number) {
    try {
      const updatedProgram = await graduateTraineeApi.updateProgram({
        id: parseInt(traineeId),
        overallProgress: progress
      });
      
      this.setCache(`trainee:${traineeId}`, updatedProgram);
      this.emit('progress:updated', { traineeId, progress });
      return updatedProgram;
    } catch (error) {
      this.emit('error', { type: 'progress:update', error });
      throw error;
    }
  }

  // Helper Methods
  async getTraineeProgram(traineeId: string): Promise<GraduateTraineeProgram | null> {
    const cached = this.getCache(`trainee:${traineeId}`);
    if (cached) return cached;
    
    try {
      const program = await graduateTraineeApi.getProgram(parseInt(traineeId));
      this.setCache(`trainee:${traineeId}`, program);
      return program;
    } catch (error) {
      return null;
    }
  }

  async getAllPrograms(): Promise<GraduateTraineeProgram[]> {
    const cached = this.getCache('trainees:all');
    if (cached) return cached;
    
    return this.syncAllTrainees();
  }

  // Analytics and Reporting
  async getMetrics() {
    const programs = await this.getAllPrograms();
    
    const metrics = {
      totalTrainees: programs.length,
      activePrograms: programs.filter(p => p.status === 'in_progress').length,
      completedPrograms: programs.filter(p => p.status === 'completed').length,
      avgProgress: programs.reduce((sum, p) => sum + p.overallProgress, 0) / programs.length || 0,
      reviewsDue: programs.reduce((count, p) => {
        return count + p.quarterlyReviews.filter(r => !r.completedDate && new Date(r.dueDate) <= new Date()).length;
      }, 0),
      upcomingReviews: programs.reduce((count, p) => {
        const nextWeek = new Date();
        nextWeek.setDate(nextWeek.getDate() + 7);
        return count + p.quarterlyReviews.filter(r => 
          !r.completedDate && 
          new Date(r.dueDate) > new Date() && 
          new Date(r.dueDate) <= nextWeek
        ).length;
      }, 0),
      certificationsExpiringSoon: programs.reduce((count, p) => {
        const nextMonth = new Date();
        nextMonth.setMonth(nextMonth.getMonth() + 1);
        return count + p.certifications.filter(c => 
          c.expiryDate && 
          new Date(c.expiryDate) <= nextMonth
        ).length;
      }, 0)
    };
    
    this.setCache('metrics', metrics);
    this.emit('metrics:updated', metrics);
    return metrics;
  }

  // Bulk Operations
  async bulkUpdateStatus(traineeIds: string[], status: string) {
    const results = await Promise.allSettled(
      traineeIds.map(id => 
        graduateTraineeApi.updateProgram({ id: parseInt(id), status: status as any })
      )
    );
    
    const successful = results.filter(r => r.status === 'fulfilled').length;
    const failed = results.filter(r => r.status === 'rejected').length;
    
    this.emit('bulk:update:completed', { successful, failed });
    await this.syncAllTrainees(); // Refresh cache
    
    return { successful, failed };
  }

  // Export Functionality
  generateExportData(programs: GraduateTraineeProgram[], format: 'csv' | 'json' = 'csv') {
    if (format === 'json') {
      return JSON.stringify(programs, null, 2);
    }
    
    // CSV format
    const headers = [
      'Employee ID', 'Name', 'Department', 'Position', 'Manager', 'Mentor',
      'Status', 'Progress', 'Start Date', 'End Date'
    ];
    
    const rows = programs.map(p => [
      p.employeeId,
      p.employeeName,
      p.department,
      p.position,
      p.manager,
      p.mentor || '',
      p.status,
      p.overallProgress + '%',
      p.startDate,
      p.expectedEndDate
    ]);
    
    return [headers, ...rows].map(row => row.join(',')).join('\n');
  }
}

export const traineeIntegrationService = TraineeIntegrationService.getInstance();