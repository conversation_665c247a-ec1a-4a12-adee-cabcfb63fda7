/**
 * Graduate Trainee Hub - Modern Integration
 * Combines modern design with existing tab structure
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Users,
  Plus,
  GraduationCap,
  LayoutGrid,
  FileText,
  Settings,
  Command,
  Search,
  X
} from 'lucide-react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

// Modern Design System
import { ThemeProvider } from './core/providers/ThemeProvider';
import { ModernNavigation } from './core/design-system/components/ModernNavigation';
import { GlassCard } from './core/design-system/components/GlassCard';
import { GlassButton } from './core/design-system/components/GlassButton';
import { GlassInput } from './core/design-system/components/GlassInput';

// Command Palette
import { CommandPalette } from './components/CommandPalette';

// Hooks
import { useUnifiedTraineeState } from './hooks/useUnifiedTraineeState';
import { useTraineeStore } from './store/traineeStore';

// Common Components
import Container from './components/common/Container';
import LoadingState from './components/common/LoadingState';
import ErrorState from './components/common/ErrorState';
import NotificationSystem from './components/common/NotificationSystem';

// Feature Views - Modern Components
import { ModernDashboard } from './features/dashboard';
import { TraineesView } from './features/trainees';  // This now imports ModernTraineesView
import { ReportsView } from './features/reports';  // This now imports ModernReportsView
import SettingsView from './features/settings/SettingsView';

// Dialogs
import { UnifiedDialogManager } from './components/dialogs';

// Data
import { generateMWSTrainees, generateMWSMetrics } from './data/mwsMockData';

// Styles
import './styles/graduateTrainee.css';

// Main Hub Component
export const GraduateTraineeHub: React.FC = () => {
  const [commandPaletteOpen, setCommandPaletteOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('dashboard');
  
  // Use Zustand store for shared state
  const {
    trainees,
    selectTrainee,
    openDialog,
    closeDialog,
    setSearchQuery,
    setFilter,
    setViewMode,
    ui,
    filters,
    searchQuery,
    metrics,
    fetchTrainees
  } = useTraineeStore();

  // Initialize data
  useEffect(() => {
    fetchTrainees();
  }, [fetchTrainees]);

  // Keyboard shortcut for command palette
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault();
        setCommandPaletteOpen((prev) => !prev);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  // Command palette actions - memoized for performance
  const commandActions = useMemo(() => [
    {
      id: 'add-trainee',
      label: 'Add New Trainee',
      icon: <Plus className="w-4 h-4" />,
      shortcut: '⌘N',
      action: () => {
        openDialog('create', null);
        setCommandPaletteOpen(false);
      }
    },
    {
      id: 'view-dashboard',
      label: 'View Dashboard',
      icon: <LayoutGrid className="w-4 h-4" />,
      shortcut: '⌘1',
      action: () => {
        setActiveTab('dashboard');
        setCommandPaletteOpen(false);
      }
    },
    {
      id: 'view-trainees',
      label: 'View Trainees',
      icon: <Users className="w-4 h-4" />,
      shortcut: '⌘2',
      action: () => {
        setActiveTab('trainees');
        setCommandPaletteOpen(false);
      }
    },
    {
      id: 'view-reports',
      label: 'View Reports',
      icon: <FileText className="w-4 h-4" />,
      shortcut: '⌘3',
      action: () => {
        setActiveTab('reports');
        setCommandPaletteOpen(false);
      }
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: <Settings className="w-4 h-4" />,
      shortcut: '⌘,',
      action: () => {
        setActiveTab('settings');
        setCommandPaletteOpen(false);
      }
    },
    ...trainees.slice(0, 5).map((trainee: any, index: number) => ({
      id: `trainee-${trainee.id || trainee.traineeId || index}`,
      label: `View ${trainee.employeeName}`,
      icon: <GraduationCap className="w-4 h-4" />,
      category: 'Trainees',
      action: () => {
        selectTrainee(trainee);
        openDialog('view', trainee);
        setCommandPaletteOpen(false);
      }
    }))
  ], [trainees, openDialog, selectTrainee]);

  // Handle loading and error states
  if (ui.isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 flex items-center justify-center">
        <LoadingState message="Loading graduate trainee data..." />
      </div>
    );
  }

  if (ui.error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 flex items-center justify-center">
        <ErrorState message={ui.error} onRetry={() => fetchTrainees()} />
      </div>
    );
  }

  return (
    <ThemeProvider defaultMode="dark">
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
        {/* Static background gradients - removed animations for performance */}
        <div className="fixed inset-0 overflow-hidden pointer-events-none">
          <div className="absolute -top-48 -left-48 w-96 h-96 bg-primary-500/10 rounded-full blur-2xl" />
          <div className="absolute -bottom-48 -right-48 w-96 h-96 bg-accent-500/10 rounded-full blur-2xl" />
        </div>

        <div className="relative z-10 flex flex-col h-screen">
          {/* Header with Command Palette Button */}
          <div className="bg-gradient-to-r from-gray-900/95 via-gray-800/95 to-gray-900/95 backdrop-blur-xl border-b border-white/10">
            <div className="container mx-auto px-6 py-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="relative">
                    <div className="absolute inset-0 bg-gradient-primary rounded-xl blur-lg opacity-50" />
                    <div className="relative bg-gradient-primary rounded-xl p-2">
                      <GraduationCap className="w-8 h-8 text-white" />
                    </div>
                  </div>
                  <div>
                    <h1 className="text-xl font-bold text-white">Graduate Trainee Hub</h1>
                    <p className="text-xs text-white/60">Talent Development Platform</p>
                  </div>
                </div>
                
                {/* Command Palette Trigger */}
                <GlassButton
                  variant="secondary"
                  size="md"
                  icon={<Command className="w-4 h-4" />}
                  onClick={() => setCommandPaletteOpen(true)}
                  className="gap-3"
                >
                  <span className="text-white/60">Command Palette</span>
                  <kbd className="px-2 py-0.5 text-xs bg-white/10 rounded">⌘K</kbd>
                </GlassButton>
              </div>
            </div>
          </div>

          {/* Modern Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
            <div className="bg-gray-900/50 backdrop-blur-md border-b border-white/10">
              <div className="container mx-auto px-6">
                <TabsList className="bg-transparent border-0 h-auto p-0 gap-1">
                  <TabsTrigger
                    value="dashboard"
                    className="data-[state=active]:bg-white/10 data-[state=active]:text-white text-white/60 border-0 rounded-lg px-4 py-2.5 transition-all duration-200 hover:bg-white/5"
                  >
                    <LayoutGrid className="w-4 h-4 mr-2" />
                    Dashboard
                  </TabsTrigger>
                  <TabsTrigger
                    value="trainees"
                    className="data-[state=active]:bg-white/10 data-[state=active]:text-white text-white/60 border-0 rounded-lg px-4 py-2.5 transition-all duration-200 hover:bg-white/5"
                  >
                    <Users className="w-4 h-4 mr-2" />
                    Trainees
                  </TabsTrigger>
                  <TabsTrigger
                    value="reports"
                    className="data-[state=active]:bg-white/10 data-[state=active]:text-white text-white/60 border-0 rounded-lg px-4 py-2.5 transition-all duration-200 hover:bg-white/5"
                  >
                    <FileText className="w-4 h-4 mr-2" />
                    Reports
                  </TabsTrigger>
                  <TabsTrigger
                    value="settings"
                    className="data-[state=active]:bg-white/10 data-[state=active]:text-white text-white/60 border-0 rounded-lg px-4 py-2.5 transition-all duration-200 hover:bg-white/5"
                  >
                    <Settings className="w-4 h-4 mr-2" />
                    Settings
                  </TabsTrigger>
                </TabsList>
              </div>
            </div>

            {/* Tab Content */}
            <div className="flex-1 overflow-hidden">
              <div className="container mx-auto px-6 py-6 h-full">
                <AnimatePresence mode="wait">
                  <motion.div
                    key={activeTab}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.3 }}
                    className="h-full"
                  >
                    <GlassCard
                      variant="elevated"
                      blur="lg"
                      intensity="subtle"
                      className="h-full p-6 overflow-hidden"
                    >
                      <TabsContent value="dashboard" className="h-full m-0 border-0 p-0">
                        <ModernDashboard />
                      </TabsContent>
                      
                      <TabsContent value="trainees" className="h-full m-0 border-0 p-0">
                        <TraineesView />
                      </TabsContent>
                      
                      <TabsContent value="reports" className="h-full m-0 border-0 p-0">
                        <ReportsView />
                      </TabsContent>
                      
                      <TabsContent value="settings" className="h-full m-0 border-0 p-0">
                        <SettingsView />
                      </TabsContent>
                    </GlassCard>
                  </motion.div>
                </AnimatePresence>
              </div>
            </div>
          </Tabs>
        </div>

        {/* Command Palette */}
        <CommandPalette
          isOpen={commandPaletteOpen}
          onClose={() => setCommandPaletteOpen(false)}
          actions={commandActions}
          trainees={trainees}
        />

        {/* Dialog Manager */}
        <UnifiedDialogManager />

        {/* Notification System */}
        <NotificationSystem />
      </div>
    </ThemeProvider>
  );
};

export default GraduateTraineeHub;