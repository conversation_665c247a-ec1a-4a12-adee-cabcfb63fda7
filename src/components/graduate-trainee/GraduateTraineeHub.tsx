import React, { useMemo } from 'react';
import {
  Users,
  TrendingUp,
  Plus,
  GraduationCap,
  LayoutGrid,
  FileText,
  Settings
} from 'lucide-react';
import { useUnifiedTraineeState } from './hooks/useUnifiedTraineeState';
import { useTraineeStore } from './state/traineeStore';
import { UnifiedDialogManager } from './dialogs/UnifiedDialogManager';
import { TraineeGrid, type ViewMode } from './components/TraineeGrid';
import { TraineeCardVariant } from './components/TraineeCardVariants';
import { MetricsGrid } from './components/MetricsGrid';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import './styles/graduateTrainee.css';

// Import reusable components
import Container from './components/Container';
import PageHeader from './components/PageHeader';
import FilterBar, { FilterConfig } from './components/FilterBar';
import LoadingState from './components/LoadingState';
import ErrorState from './components/ErrorState';
import { defaultStatusConfigs } from './components/StatusBadge';

// Import tab components
import ProgressTab from './tabs/ProgressTab';
import ReportsTab from './tabs/ReportsTab';
import SettingsTab from './tabs/SettingsTab';

// Import sample data generator
import { generateSampleTrainees } from './utils/sampleData';

// Dashboard Tab Component
const DashboardTab: React.FC<{ metrics: any; trainees: any[] }> = ({ metrics, trainees }) => {
  const recentTrainees = trainees.filter(t => {
    const startDate = new Date(t.startDate);
    const thisMonth = new Date();
    return startDate.getMonth() === thisMonth.getMonth() && 
           startDate.getFullYear() === thisMonth.getFullYear();
  }).length;

  const completingSoon = trainees.filter(t => {
    const endDate = new Date(t.expectedEndDate);
    const thirtyDaysFromNow = new Date();
    thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
    return endDate <= thirtyDaysFromNow && t.status === 'in_progress';
  }).length;

  return (
    <Container padding="lg" className="space-y-4 overflow-y-auto custom-scrollbar">
      <MetricsGrid metrics={metrics} />
      
      <div className="glass-panel p-4 rounded-lg">
        <h3 className="text-sm font-medium mb-3">Recent Activity</h3>
        <div className="space-y-2">
          <div className="flex items-center justify-between text-xs">
            <span className="text-muted-foreground">New trainees this month</span>
            <span className="font-medium">{recentTrainees}</span>
          </div>
          <div className="flex items-center justify-between text-xs">
            <span className="text-muted-foreground">Programs completing soon</span>
            <span className="font-medium">{completingSoon}</span>
          </div>
          <div className="flex items-center justify-between text-xs">
            <span className="text-muted-foreground">Pending reviews</span>
            <span className="font-medium">{metrics.reviewsDue}</span>
          </div>
        </div>
      </div>
    </Container>
  );
};

// Trainees Tab Component
const TraineesTab: React.FC<{
  trainees: any[];
  searchQuery: string;
  onSearchChange: (query: string) => void;
  filters: any;
  onFilterChange: (key: string, value: string) => void;
  onClearFilters: () => void;
  viewMode: ViewMode;
  onViewModeChange: (mode: ViewMode) => void;
  onSelectTrainee: (trainee: any) => void;
  onOpenDialog: (type: string) => void;
  isLoading: boolean;
}> = ({
  trainees,
  searchQuery,
  onSearchChange,
  filters,
  onFilterChange,
  onClearFilters,
  viewMode,
  onViewModeChange,
  onSelectTrainee,
  onOpenDialog,
  isLoading
}) => {
  // Get unique departments
  const departments = useMemo(() => {
    if (!trainees || trainees.length === 0) return ['all'];
    const depts = [...new Set(trainees.map(t => t.department))];
    return ['all', ...depts];
  }, [trainees]);

  // Filter configurations
  const filterConfigs: FilterConfig[] = [
    {
      key: 'department',
      placeholder: 'Department',
      value: filters.department,
      onChange: (val) => onFilterChange('department', val),
      options: departments.map(dept => ({
        value: dept,
        label: dept === 'all' ? 'All Departments' : dept
      }))
    },
    {
      key: 'status',
      placeholder: 'Status',
      value: filters.status,
      onChange: (val) => onFilterChange('status', val),
      options: [
        { value: 'all', label: 'All Status' },
        ...Object.entries(defaultStatusConfigs).map(([key, config]) => ({
          value: key,
          label: config.label
        }))
      ]
    }
  ];

  return (
    <Container padding="lg" className="space-y-4 overflow-y-auto custom-scrollbar">
      <FilterBar
        searchQuery={searchQuery}
        onSearchChange={onSearchChange}
        searchPlaceholder="Search trainees..."
        filters={filterConfigs}
        onClearAll={onClearFilters}
      />

      <TraineeGrid
        trainees={trainees}
        viewMode={viewMode}
        onViewModeChange={onViewModeChange}
        loading={isLoading}
        renderCard={(trainee) => (
          <TraineeCardVariant
            key={trainee.id}
            trainee={trainee}
            variant={viewMode}
            onView={() => {
              onSelectTrainee(trainee);
              onOpenDialog('view');
            }}
            onEdit={() => {
              onSelectTrainee(trainee);
              onOpenDialog('edit');
            }}
            onDelete={() => {
              onSelectTrainee(trainee);
              onOpenDialog('delete');
            }}
          />
        )}
      />
    </Container>
  );
};

// Main Hub Component
export const GraduateTraineeHub: React.FC = () => {
  // Use Zustand store for shared state
  const {
    trainees,
    selectTrainee,
    openDialog,
    closeDialog,
    setActiveTab,
    setSearchQuery,
    setFilter,
    setViewMode,
    ui,
    filters,
    searchQuery,
    metrics,
    fetchTrainees
  } = useTraineeStore();
  
  // Use local hook for data loading and operations
  const {
    loading,
    error,
    deleteTrainee,
    handleDialogAction
  } = useUnifiedTraineeState();
  
  const activeTab = ui.activeTab;
  const viewMode = ui.viewMode as ViewMode;
  
  // Fetch trainees on component mount - use sample data if none exist
  React.useEffect(() => {
    fetchTrainees();
    // If no trainees, use sample data for demonstration
    if (trainees.length === 0) {
      const sampleData = generateSampleTrainees();
      // You can set this to your store if needed
      // For now, we'll just use it for display
    }
  }, [fetchTrainees, trainees.length]);

  // Use sample data if no real data exists
  const displayTrainees = useMemo(() => {
    return trainees.length > 0 ? trainees : generateSampleTrainees();
  }, [trainees]);

  // Memoized filtered trainees
  const filteredTrainees = useMemo(() => {
    return displayTrainees.filter(trainee => {
      const matchesSearch = searchQuery ? 
        trainee.employeeName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        trainee.employeeId.toLowerCase().includes(searchQuery.toLowerCase()) ||
        trainee.department.toLowerCase().includes(searchQuery.toLowerCase()) : true;
      
      const matchesDept = filters.department === 'all' || trainee.department === filters.department;
      const matchesStatus = filters.status === 'all' || trainee.status === filters.status;
      
      return matchesSearch && matchesDept && matchesStatus;
    });
  }, [displayTrainees, searchQuery, filters.department, filters.status]);

  // Clear all filters
  const handleClearFilters = () => {
    setSearchQuery('');
    setFilter('department', 'all');
    setFilter('status', 'all');
    setFilter('manager', 'all');
  };

  // Loading state
  if (loading) {
    return (
      <Container size="full" className="h-full">
        <LoadingState message="Loading trainee data..." />
      </Container>
    );
  }

  // Error state
  if (error) {
    return (
      <Container size="full" className="h-full">
        <ErrorState
          title="Failed to load trainee data"
          message={error}
          onRetry={() => window.location.reload()}
        />
      </Container>
    );
  }

  return (
    <Container size="full" padding="none" className="h-full flex flex-col bg-background">
      {/* Page Header */}
      <PageHeader
        icon={GraduationCap}
        title="Graduate Trainee Hub"
        subtitle="Manage and track trainee programs"
        action={{
          label: 'Add Trainee',
          icon: Plus,
          onClick: () => {
            selectTrainee(null);
            openDialog('create');
          }
        }}
      />

      {/* Tab Navigation */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
        <Container>
          <div className="border-b border-border/30">
            <TabsList className="h-9 p-0 bg-transparent gap-4">
              <TabsTrigger value="dashboard" className="tab-minimal">
                <LayoutGrid className="h-3 w-3 mr-1.5" />
                Dashboard
              </TabsTrigger>
              <TabsTrigger value="trainees" className="tab-minimal">
                <Users className="h-3 w-3 mr-1.5" />
                Trainees
              </TabsTrigger>
              <TabsTrigger value="progress" className="tab-minimal">
                <TrendingUp className="h-3 w-3 mr-1.5" />
                Progress
              </TabsTrigger>
              <TabsTrigger value="reports" className="tab-minimal">
                <FileText className="h-3 w-3 mr-1.5" />
                Reports
              </TabsTrigger>
              <TabsTrigger value="settings" className="tab-minimal">
                <Settings className="h-3 w-3 mr-1.5" />
                Settings
              </TabsTrigger>
            </TabsList>
          </div>
        </Container>

        <TabsContent value="dashboard" className="flex-1">
          <DashboardTab metrics={metrics} trainees={displayTrainees} />
        </TabsContent>

        <TabsContent value="trainees" className="flex-1">
          <TraineesTab
            trainees={filteredTrainees}
            searchQuery={searchQuery}
            onSearchChange={setSearchQuery}
            filters={filters}
            onFilterChange={setFilter}
            onClearFilters={handleClearFilters}
            viewMode={viewMode}
            onViewModeChange={setViewMode}
            onSelectTrainee={selectTrainee}
            onOpenDialog={openDialog}
            isLoading={ui.isLoading}
          />
        </TabsContent>

        <TabsContent value="progress" className="flex-1">
          <ProgressTab trainees={displayTrainees} />
        </TabsContent>

        <TabsContent value="reports" className="flex-1">
          <ReportsTab trainees={displayTrainees} />
        </TabsContent>

        <TabsContent value="settings" className="flex-1">
          <SettingsTab />
        </TabsContent>
      </Tabs>

      {/* Dialog Manager */}
      <UnifiedDialogManager />
    </Container>
  );
};

export default GraduateTraineeHub;