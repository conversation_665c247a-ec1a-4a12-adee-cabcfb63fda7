import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Plus, X, CheckCircle, AlertCircle } from 'lucide-react';
import type { QuarterlyReview } from '@/lib/api/graduateTrainee';

interface QuarterlyReviewDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  review?: Partial<QuarterlyReview>;
  onSave: (review: QuarterlyReview) => void;
  traineeId?: number;
  reviewNumber: number;
}

export const QuarterlyReviewDialog: React.FC<QuarterlyReviewDialogProps> = ({
  open,
  onOpenChange,
  review,
  onSave,
  traineeId,
  reviewNumber,
}) => {
  const [formData, setFormData] = useState<Partial<QuarterlyReview>>({
    reviewNumber: reviewNumber,
    dueDate: review?.dueDate || new Date().toISOString().split('T')[0],
    completedDate: new Date().toISOString().split('T')[0],
    reviewer: review?.reviewer || '',
    overallRating: review?.overallRating || 3,
    feedback: review?.feedback || '',
    goalsAchieved: review?.goalsAchieved || [],
    goalsPending: review?.goalsPending || [],
    skillsImproved: review?.skillsImproved || [],
    areasForImprovement: review?.areasForImprovement || [],
    nextQuarterGoals: review?.nextQuarterGoals || [],
    supportNeeded: review?.supportNeeded || [],
    isSatisfactory: review?.isSatisfactory ?? true,
    continuationRecommendation: review?.continuationRecommendation ?? true,
  });

  const [newGoalAchieved, setNewGoalAchieved] = useState('');
  const [newGoalPending, setNewGoalPending] = useState('');
  const [newSkillImproved, setNewSkillImproved] = useState('');
  const [newAreaForImprovement, setNewAreaForImprovement] = useState('');
  const [newNextQuarterGoal, setNewNextQuarterGoal] = useState('');
  const [newSupportNeeded, setNewSupportNeeded] = useState('');

  const handleAddItem = (field: keyof QuarterlyReview, value: string, setter: (v: string) => void) => {
    if (value.trim()) {
      setFormData(prev => ({
        ...prev,
        [field]: [...(prev[field] as string[] || []), value.trim()]
      }));
      setter('');
    }
  };

  const handleRemoveItem = (field: keyof QuarterlyReview, index: number) => {
    setFormData(prev => ({
      ...prev,
      [field]: (prev[field] as string[] || []).filter((_, i) => i !== index)
    }));
  };

  const handleSubmit = () => {
    if (!formData.reviewer || !formData.feedback) {
      alert('Please fill in reviewer and feedback fields');
      return;
    }

    onSave(formData as QuarterlyReview);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Quarter {reviewNumber} Review</DialogTitle>
          <DialogDescription>
            Complete the quarterly review for the graduate trainee
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-6 py-4">
          {/* Basic Information */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="reviewer">Reviewer *</Label>
              <Input
                id="reviewer"
                value={formData.reviewer}
                onChange={(e) => setFormData({ ...formData, reviewer: e.target.value })}
                placeholder="Enter reviewer name"
              />
            </div>
            <div>
              <Label htmlFor="completedDate">Completion Date</Label>
              <Input
                id="completedDate"
                type="date"
                value={formData.completedDate}
                onChange={(e) => setFormData({ ...formData, completedDate: e.target.value })}
              />
            </div>
          </div>

          {/* Overall Rating */}
          <div>
            <Label>Overall Rating: {formData.overallRating}/5</Label>
            <Slider
              value={[formData.overallRating || 3]}
              onValueChange={(value) => setFormData({ ...formData, overallRating: value[0] })}
              min={1}
              max={5}
              step={0.1}
              className="mt-2"
            />
          </div>

          {/* Feedback */}
          <div>
            <Label htmlFor="feedback">Feedback *</Label>
            <Textarea
              id="feedback"
              value={formData.feedback}
              onChange={(e) => setFormData({ ...formData, feedback: e.target.value })}
              placeholder="Provide detailed feedback on the trainee's performance"
              rows={4}
            />
          </div>

          {/* Goals Achieved */}
          <div>
            <Label>Goals Achieved</Label>
            <div className="flex gap-2 mt-2">
              <Input
                value={newGoalAchieved}
                onChange={(e) => setNewGoalAchieved(e.target.value)}
                placeholder="Enter achieved goal"
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    handleAddItem('goalsAchieved', newGoalAchieved, setNewGoalAchieved);
                  }
                }}
              />
              <Button
                type="button"
                size="sm"
                onClick={() => handleAddItem('goalsAchieved', newGoalAchieved, setNewGoalAchieved)}
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            <div className="flex flex-wrap gap-2 mt-2">
              {formData.goalsAchieved?.map((goal, index) => (
                <Badge key={index} variant="secondary" className="flex items-center gap-1">
                  {goal}
                  <X
                    className="h-3 w-3 cursor-pointer"
                    onClick={() => handleRemoveItem('goalsAchieved', index)}
                  />
                </Badge>
              ))}
            </div>
          </div>

          {/* Goals Pending */}
          <div>
            <Label>Goals Pending</Label>
            <div className="flex gap-2 mt-2">
              <Input
                value={newGoalPending}
                onChange={(e) => setNewGoalPending(e.target.value)}
                placeholder="Enter pending goal"
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    handleAddItem('goalsPending', newGoalPending, setNewGoalPending);
                  }
                }}
              />
              <Button
                type="button"
                size="sm"
                onClick={() => handleAddItem('goalsPending', newGoalPending, setNewGoalPending)}
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            <div className="flex flex-wrap gap-2 mt-2">
              {formData.goalsPending?.map((goal, index) => (
                <Badge key={index} variant="outline" className="flex items-center gap-1">
                  {goal}
                  <X
                    className="h-3 w-3 cursor-pointer"
                    onClick={() => handleRemoveItem('goalsPending', index)}
                  />
                </Badge>
              ))}
            </div>
          </div>

          {/* Skills Improved */}
          <div>
            <Label>Skills Improved</Label>
            <div className="flex gap-2 mt-2">
              <Input
                value={newSkillImproved}
                onChange={(e) => setNewSkillImproved(e.target.value)}
                placeholder="Enter improved skill"
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    handleAddItem('skillsImproved', newSkillImproved, setNewSkillImproved);
                  }
                }}
              />
              <Button
                type="button"
                size="sm"
                onClick={() => handleAddItem('skillsImproved', newSkillImproved, setNewSkillImproved)}
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            <div className="flex flex-wrap gap-2 mt-2">
              {formData.skillsImproved?.map((skill, index) => (
                <Badge key={index} variant="secondary" className="flex items-center gap-1">
                  {skill}
                  <X
                    className="h-3 w-3 cursor-pointer"
                    onClick={() => handleRemoveItem('skillsImproved', index)}
                  />
                </Badge>
              ))}
            </div>
          </div>

          {/* Areas for Improvement */}
          <div>
            <Label>Areas for Improvement</Label>
            <div className="flex gap-2 mt-2">
              <Input
                value={newAreaForImprovement}
                onChange={(e) => setNewAreaForImprovement(e.target.value)}
                placeholder="Enter area for improvement"
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    handleAddItem('areasForImprovement', newAreaForImprovement, setNewAreaForImprovement);
                  }
                }}
              />
              <Button
                type="button"
                size="sm"
                onClick={() => handleAddItem('areasForImprovement', newAreaForImprovement, setNewAreaForImprovement)}
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            <div className="flex flex-wrap gap-2 mt-2">
              {formData.areasForImprovement?.map((area, index) => (
                <Badge key={index} variant="destructive" className="flex items-center gap-1">
                  {area}
                  <X
                    className="h-3 w-3 cursor-pointer"
                    onClick={() => handleRemoveItem('areasForImprovement', index)}
                  />
                </Badge>
              ))}
            </div>
          </div>

          {/* Next Quarter Goals */}
          <div>
            <Label>Next Quarter Goals</Label>
            <div className="flex gap-2 mt-2">
              <Input
                value={newNextQuarterGoal}
                onChange={(e) => setNewNextQuarterGoal(e.target.value)}
                placeholder="Enter next quarter goal"
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    handleAddItem('nextQuarterGoals', newNextQuarterGoal, setNewNextQuarterGoal);
                  }
                }}
              />
              <Button
                type="button"
                size="sm"
                onClick={() => handleAddItem('nextQuarterGoals', newNextQuarterGoal, setNewNextQuarterGoal)}
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            <div className="flex flex-wrap gap-2 mt-2">
              {formData.nextQuarterGoals?.map((goal, index) => (
                <Badge key={index} variant="default" className="flex items-center gap-1">
                  {goal}
                  <X
                    className="h-3 w-3 cursor-pointer"
                    onClick={() => handleRemoveItem('nextQuarterGoals', index)}
                  />
                </Badge>
              ))}
            </div>
          </div>

          {/* Support Needed */}
          <div>
            <Label>Support Needed</Label>
            <div className="flex gap-2 mt-2">
              <Input
                value={newSupportNeeded}
                onChange={(e) => setNewSupportNeeded(e.target.value)}
                placeholder="Enter support needed"
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    handleAddItem('supportNeeded', newSupportNeeded, setNewSupportNeeded);
                  }
                }}
              />
              <Button
                type="button"
                size="sm"
                onClick={() => handleAddItem('supportNeeded', newSupportNeeded, setNewSupportNeeded)}
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            <div className="flex flex-wrap gap-2 mt-2">
              {formData.supportNeeded?.map((support, index) => (
                <Badge key={index} variant="outline" className="flex items-center gap-1">
                  {support}
                  <X
                    className="h-3 w-3 cursor-pointer"
                    onClick={() => handleRemoveItem('supportNeeded', index)}
                  />
                </Badge>
              ))}
            </div>
          </div>

          {/* Recommendation */}
          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="isSatisfactory"
                checked={formData.isSatisfactory}
                onChange={(e) => setFormData({ ...formData, isSatisfactory: e.target.checked })}
              />
              <Label htmlFor="isSatisfactory" className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                Performance is Satisfactory
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="continuationRecommendation"
                checked={formData.continuationRecommendation}
                onChange={(e) => setFormData({ ...formData, continuationRecommendation: e.target.checked })}
              />
              <Label htmlFor="continuationRecommendation" className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                Recommend Continuation
              </Label>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSubmit}>
            Save Review
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};