import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Plus, X } from 'lucide-react';
import type { SkillDevelopment } from '@/lib/api/graduateTrainee';

interface SkillsDevelopmentDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  skill?: Partial<SkillDevelopment>;
  onSave: (skill: SkillDevelopment) => void;
}

export const SkillsDevelopmentDialog: React.FC<SkillsDevelopmentDialogProps> = ({
  open,
  onOpenChange,
  skill,
  onSave,
}) => {
  const [formData, setFormData] = useState<Partial<SkillDevelopment>>({
    skillName: skill?.skillName || '',
    category: skill?.category || 'Technical',
    baselineLevel: skill?.baselineLevel || 1,
    targetLevel: skill?.targetLevel || 8,
    currentLevel: skill?.currentLevel || 1,
    progressPercentage: skill?.progressPercentage || 0,
    trainingCompleted: skill?.trainingCompleted || [],
    nextSteps: skill?.nextSteps || '',
  });

  const [newTraining, setNewTraining] = useState('');

  const categories = [
    'Technical',
    'Soft Skills',
    'Leadership',
    'Management',
    'Safety',
    'Quality',
    'Process',
    'Communication',
  ];

  const calculateProgress = (current: number, baseline: number, target: number) => {
    if (target <= baseline) return 0;
    return Math.round(((current - baseline) / (target - baseline)) * 100);
  };

  const handleLevelChange = (field: 'baselineLevel' | 'currentLevel' | 'targetLevel', value: number) => {
    const newData = { ...formData, [field]: value };
    
    // Recalculate progress
    const progress = calculateProgress(
      field === 'currentLevel' ? value : formData.currentLevel || 1,
      field === 'baselineLevel' ? value : formData.baselineLevel || 1,
      field === 'targetLevel' ? value : formData.targetLevel || 8
    );
    
    newData.progressPercentage = progress;
    setFormData(newData);
  };

  const handleAddTraining = () => {
    if (newTraining.trim()) {
      setFormData(prev => ({
        ...prev,
        trainingCompleted: [...(prev.trainingCompleted || []), newTraining.trim()]
      }));
      setNewTraining('');
    }
  };

  const handleRemoveTraining = (index: number) => {
    setFormData(prev => ({
      ...prev,
      trainingCompleted: (prev.trainingCompleted || []).filter((_, i) => i !== index)
    }));
  };

  const handleSubmit = () => {
    if (!formData.skillName || !formData.category) {
      alert('Please fill in skill name and category');
      return;
    }

    onSave(formData as SkillDevelopment);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>{skill ? 'Edit' : 'Add'} Skill Development</DialogTitle>
          <DialogDescription>
            Track skill development progress for the graduate trainee
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-6 py-4">
          {/* Skill Name and Category */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="skillName">Skill Name *</Label>
              <Input
                id="skillName"
                value={formData.skillName}
                onChange={(e) => setFormData({ ...formData, skillName: e.target.value })}
                placeholder="e.g., Python Programming"
              />
            </div>
            <div>
              <Label htmlFor="category">Category *</Label>
              <Select
                value={formData.category}
                onValueChange={(value) => setFormData({ ...formData, category: value })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {categories.map(cat => (
                    <SelectItem key={cat} value={cat}>{cat}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Skill Levels */}
          <div className="space-y-4">
            <div>
              <Label>Baseline Level: {formData.baselineLevel}/10</Label>
              <Slider
                value={[formData.baselineLevel || 1]}
                onValueChange={(value) => handleLevelChange('baselineLevel', value[0])}
                min={1}
                max={10}
                step={1}
                className="mt-2"
              />
            </div>

            <div>
              <Label>Current Level: {formData.currentLevel}/10</Label>
              <Slider
                value={[formData.currentLevel || 1]}
                onValueChange={(value) => handleLevelChange('currentLevel', value[0])}
                min={1}
                max={10}
                step={1}
                className="mt-2"
              />
            </div>

            <div>
              <Label>Target Level: {formData.targetLevel}/10</Label>
              <Slider
                value={[formData.targetLevel || 8]}
                onValueChange={(value) => handleLevelChange('targetLevel', value[0])}
                min={1}
                max={10}
                step={1}
                className="mt-2"
              />
            </div>

            <div>
              <Label>Progress: {formData.progressPercentage}%</Label>
              <div className="h-3 bg-gray-200 rounded-full mt-2">
                <div
                  className="h-full bg-blue-500 rounded-full transition-all"
                  style={{ width: `${formData.progressPercentage}%` }}
                />
              </div>
            </div>
          </div>

          {/* Training Completed */}
          <div>
            <Label>Training Completed</Label>
            <div className="flex gap-2 mt-2">
              <Input
                value={newTraining}
                onChange={(e) => setNewTraining(e.target.value)}
                placeholder="Enter completed training"
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    handleAddTraining();
                  }
                }}
              />
              <Button
                type="button"
                size="sm"
                onClick={handleAddTraining}
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            <div className="flex flex-wrap gap-2 mt-2">
              {formData.trainingCompleted?.map((training, index) => (
                <Badge key={index} variant="secondary" className="flex items-center gap-1">
                  {training}
                  <X
                    className="h-3 w-3 cursor-pointer"
                    onClick={() => handleRemoveTraining(index)}
                  />
                </Badge>
              ))}
            </div>
          </div>

          {/* Next Steps */}
          <div>
            <Label htmlFor="nextSteps">Next Steps</Label>
            <Textarea
              id="nextSteps"
              value={formData.nextSteps}
              onChange={(e) => setFormData({ ...formData, nextSteps: e.target.value })}
              placeholder="Describe the next steps for skill development"
              rows={3}
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSubmit}>
            Save Skill
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};