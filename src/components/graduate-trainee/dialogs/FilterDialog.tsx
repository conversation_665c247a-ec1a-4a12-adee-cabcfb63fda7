import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>Title,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DEPARTMENTS, TRAINEE_STATUSES } from '../constants/traineeConstants';
import { Filter, X } from 'lucide-react';

interface FilterDialogProps {
  isOpen: boolean;
  onClose: () => void;
  filters?: {
    status: string;
    department: string;
    manager: string;
    searchQuery: string;
  };
  onApplyFilters?: (filters: any) => void;
  onClearFilters?: () => void;
}

const defaultFilters = { status: '', department: '', manager: '', searchQuery: '' };

export const FilterDialog: React.FC<FilterDialogProps> = ({
  isO<PERSON>,
  onClose,
  filters = defaultFilters,
  onApplyFilters,
  onClearFilters,
}) => {
  const [localFilters, setLocalFilters] = React.useState(() => filters);

  // Only update local filters when dialog opens, not on every filter change
  React.useEffect(() => {
    if (isOpen) {
      setLocalFilters(filters);
    }
  }, [isOpen]); // Removed filters from dependencies to prevent loops

  const handleApply = () => {
    if (onApplyFilters) {
      onApplyFilters(localFilters);
    }
    onClose();
  };

  const handleClear = () => {
    const clearedFilters = { status: '', department: '', manager: '', searchQuery: '' };
    setLocalFilters(clearedFilters);
    if (onClearFilters) {
      onClearFilters();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filter Trainees
          </DialogTitle>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="search">Search</Label>
            <Input
              id="search"
              placeholder="Search by name, ID..."
              value={localFilters.searchQuery}
              onChange={(e) => setLocalFilters({ ...localFilters, searchQuery: e.target.value })}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="department">Department</Label>
            <Select
              value={localFilters.department}
              onValueChange={(value) => setLocalFilters({ ...localFilters, department: value === '_all' ? '' : value })}
            >
              <SelectTrigger id="department">
                <SelectValue placeholder="All Departments" />
              </SelectTrigger>
              <SelectContent>
                {DEPARTMENTS.map(dept => (
                  <SelectItem key={dept.value} value={dept.value || '_all'}>
                    {dept.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="status">Status</Label>
            <Select
              value={localFilters.status}
              onValueChange={(value) => setLocalFilters({ ...localFilters, status: value === '_all' ? '' : value })}
            >
              <SelectTrigger id="status">
                <SelectValue placeholder="All Statuses" />
              </SelectTrigger>
              <SelectContent>
                {TRAINEE_STATUSES.map(status => (
                  <SelectItem key={status.value} value={status.value || '_all'}>
                    {status.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="manager">Manager</Label>
            <Input
              id="manager"
              placeholder="Filter by manager..."
              value={localFilters.manager}
              onChange={(e) => setLocalFilters({ ...localFilters, manager: e.target.value })}
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClear}>
            <X className="h-4 w-4 mr-2" />
            Clear All
          </Button>
          <Button onClick={handleApply}>Apply Filters</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};