import React from 'react';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { QuarterlyReviewDialog } from './QuarterlyReviewDialog';
import { SkillsDevelopmentDialog } from './SkillsDevelopmentDialog';
import { CertificationDialog } from './CertificationDialog';
import { TraineeDetailDialog } from './TraineeDetailDialog';
import { TraineeFormDialog } from './TraineeFormDialog';
import { DeleteConfirmationDialog } from './DeleteConfirmationDialog';
import { FilterDialog } from './FilterDialog';
import { useUnifiedTraineeState } from '../hooks/useUnifiedTraineeState';
import { useTraineeStore } from '../state/traineeStore';
import '../styles/graduateTrainee.css';

export type DialogType = 
  | 'traineeForm'
  | 'traineeDetail'
  | 'quarterlyReview'
  | 'skillsDevelopment'
  | 'certification'
  | 'deleteConfirm'
  | 'filter';

interface DialogConfig {
  type: DialogType;
  props?: any;
}

export const UnifiedDialogManager: React.FC = () => {
  // Use Zustand store for shared state
  const {
    selectedTrainee,
    dialog,
    openDialog,
    closeDialog,
    filters,
    setFilter,
    resetFilters
  } = useTraineeStore();
  
  // Use local hook for operations
  const {
    createTrainee,
    updateTrainee,
    deleteTrainee,
    scheduleReview,
    updateSkill,
    addCertification,
  } = useUnifiedTraineeState();

  const handleTraineeSubmit = async (data: any) => {
    if (dialog.type === 'edit' && selectedTrainee) {
      await updateTrainee(selectedTrainee.id, data);
    } else {
      await createTrainee(data);
    }
    closeDialog();
  };

  const handleReviewSubmit = async (review: any) => {
    if (selectedTrainee) {
      await scheduleReview(selectedTrainee.id, review);
    }
    closeDialog();
  };

  const handleSkillSubmit = async (skill: any) => {
    if (selectedTrainee) {
      await updateSkill(selectedTrainee.id, skill);
    }
    closeDialog();
  };

  const handleCertificationSubmit = async (certification: any) => {
    if (selectedTrainee) {
      await addCertification(selectedTrainee.id, certification);
    }
    closeDialog();
  };

  const handleDelete = async () => {
    if (selectedTrainee) {
      await deleteTrainee(selectedTrainee.id);
    }
    closeDialog();
  };

  return (
    <>
      {/* Trainee Form Dialog - for both create and edit */}
      <TraineeFormDialog
        isOpen={dialog.isOpen && (dialog.type === 'create' || dialog.type === 'edit')}
        onClose={() => closeDialog()}
        trainee={dialog.type === 'edit' ? selectedTrainee : null}
        onSubmit={handleTraineeSubmit}
      />

      {/* Trainee Detail Dialog */}
      {selectedTrainee && (
        <TraineeDetailDialog
          isOpen={dialog.isOpen && dialog.type === 'view'}
          onClose={() => closeDialog()}
          trainee={selectedTrainee}
          onEdit={() => {
            closeDialog();
            setTimeout(() => openDialog('edit', selectedTrainee), 100);
          }}
          onDelete={() => {
            closeDialog();
            setTimeout(() => openDialog('delete', selectedTrainee), 100);
          }}
        />
      )}

      {/* Quarterly Review Dialog */}
      {selectedTrainee && (
        <QuarterlyReviewDialog
          isOpen={dialog.isOpen && dialog.type === 'quarterlyReview'}
          onClose={() => closeDialog()}
          trainee={selectedTrainee}
          onSubmit={handleReviewSubmit}
        />
      )}

      {/* Skills Development Dialog */}
      {selectedTrainee && (
        <SkillsDevelopmentDialog
          isOpen={dialog.isOpen && dialog.type === 'skillsDevelopment'}
          onClose={() => closeDialog()}
          trainee={selectedTrainee}
          onSubmit={handleSkillSubmit}
        />
      )}

      {/* Certification Dialog */}
      {selectedTrainee && (
        <CertificationDialog
          isOpen={dialog.isOpen && dialog.type === 'certification'}
          onClose={() => closeDialog()}
          trainee={selectedTrainee}
          onSubmit={handleCertificationSubmit}
        />
      )}

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmationDialog
        isOpen={dialog.isOpen && dialog.type === 'delete'}
        onClose={() => closeDialog()}
        onConfirm={handleDelete}
        itemName={selectedTrainee?.employeeName}
      />

      {/* Filter Dialog */}
      <FilterDialog
        isOpen={dialog.isOpen && dialog.type === 'filter'}
        onClose={() => closeDialog()}
        filters={filters}
        onApplyFilters={(newFilters) => {
          Object.keys(newFilters).forEach(key => {
            setFilter(key, newFilters[key]);
          });
          closeDialog();
        }}
        onClearFilters={resetFilters}
      />
    </>
  );
};

export default UnifiedDialogManager;