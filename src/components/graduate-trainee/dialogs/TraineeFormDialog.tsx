import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { TraineeForm } from '../forms/TraineeForm';
import { GraduateTraineeProgram } from '@/lib/api/graduateTrainee';

interface TraineeFormDialogProps {
  isOpen: boolean;
  onClose: () => void;
  trainee?: GraduateTraineeProgram | null;
  onSubmit: (data: Partial<GraduateTraineeProgram>) => Promise<void>;
}

export const TraineeFormDialog: React.FC<TraineeFormDialogProps> = ({
  isOpen,
  onClose,
  trainee,
  onSubmit,
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (data: Partial<GraduateTraineeProgram>) => {
    setIsSubmitting(true);
    try {
      await onSubmit(data);
      onClose();
    } catch (error) {
      console.error('Error submitting form:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[800px] max-h-[85vh] overflow-hidden flex flex-col p-0">
        <DialogHeader className="px-6 pt-6 pb-4">
          <DialogTitle className="text-xl font-semibold">
            {trainee ? 'Edit Trainee Program' : 'Create New Trainee Program'}
          </DialogTitle>
          <DialogDescription>
            {trainee 
              ? 'Update the details of the existing trainee program.'
              : 'Fill in the information below to create a new graduate trainee program.'}
          </DialogDescription>
        </DialogHeader>
        <div className="flex-1 overflow-y-auto px-6 custom-scrollbar">
          <TraineeForm
            trainee={trainee}
            onSubmit={handleSubmit}
            onCancel={onClose}
            isSubmitting={isSubmitting}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};