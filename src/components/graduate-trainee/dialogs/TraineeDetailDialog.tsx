import React from 'react';
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { GraduateTraineeProgram } from '@/lib/api/graduateTrainee';
import { ProgressTimeline } from '../components/timeline';
import { SkillsDevelopmentChart } from '../components/charts';
import {
  User,
  Building,
  Calendar,
  Target,
  BookOpen,
  Award,
  Edit,
  Trash2,
  Clock,
  CheckCircle,
} from 'lucide-react';

interface TraineeDetailDialogProps {
  isOpen: boolean;
  onClose: () => void;
  trainee: GraduateTraineeProgram;
  onEdit: () => void;
  onDelete: () => void;
}

export const TraineeDetailDialog: React.FC<TraineeDetailDialogProps> = ({
  isOpen,
  onClose,
  trainee,
  onEdit,
  onDelete,
}) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-100';
      case 'in_progress': return 'text-blue-600 bg-blue-100';
      case 'on_hold': return 'text-yellow-600 bg-yellow-100';
      case 'terminated': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="text-2xl">Trainee Details</DialogTitle>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={onEdit}>
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Button>
              <Button variant="outline" size="sm" onClick={onDelete} className="text-red-600">
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </Button>
            </div>
          </div>
        </DialogHeader>

        <div className="mt-6 space-y-6">
          {/* Header Information */}
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-4">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                <User className="h-8 w-8 text-blue-600" />
              </div>
              <div>
                <h3 className="text-xl font-semibold">{trainee.employeeName}</h3>
                <p className="text-sm text-muted-foreground">{trainee.employeeId}</p>
                <div className="flex items-center gap-2 mt-1">
                  <Building className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">{trainee.department}</span>
                </div>
              </div>
            </div>
            <Badge className={getStatusColor(trainee.status)}>
              {trainee.status.replace('_', ' ')}
            </Badge>
          </div>

          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-xs text-muted-foreground">Progress</p>
                    <p className="text-xl font-bold">
                      {Math.round((trainee.overallProgress || 0) * 100)}%
                    </p>
                  </div>
                  <Target className="h-6 w-6 text-muted-foreground" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-xs text-muted-foreground">Duration</p>
                    <p className="text-xl font-bold">{trainee.programDurationMonths}m</p>
                  </div>
                  <Clock className="h-6 w-6 text-muted-foreground" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-xs text-muted-foreground">Skills</p>
                    <p className="text-xl font-bold">{trainee.skillsDevelopment?.length || 0}</p>
                  </div>
                  <BookOpen className="h-6 w-6 text-muted-foreground" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-xs text-muted-foreground">Certifications</p>
                    <p className="text-xl font-bold">
                      {trainee.certifications?.filter(c => c.status === 'completed').length || 0}
                    </p>
                  </div>
                  <Award className="h-6 w-6 text-muted-foreground" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Detailed Information Tabs */}
          <Tabs defaultValue="timeline" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="timeline">Timeline</TabsTrigger>
              <TabsTrigger value="skills">Skills</TabsTrigger>
              <TabsTrigger value="details">Details</TabsTrigger>
            </TabsList>

            <TabsContent value="timeline">
              <ProgressTimeline trainee={trainee} />
            </TabsContent>

            <TabsContent value="skills">
              {trainee.skillsDevelopment && trainee.skillsDevelopment.length > 0 ? (
                <SkillsDevelopmentChart 
                  skills={trainee.skillsDevelopment} 
                  traineeName={trainee.employeeName}
                />
              ) : (
                <Card>
                  <CardContent className="p-8 text-center text-muted-foreground">
                    No skills development data available
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="details">
              <Card>
                <CardHeader>
                  <CardTitle>Program Details</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-muted-foreground">Manager</p>
                      <p className="font-medium">{trainee.manager}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Start Date</p>
                      <p className="font-medium">
                        {new Date(trainee.startDate).toLocaleDateString()}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Expected End Date</p>
                      <p className="font-medium">
                        {new Date(trainee.expectedEndDate).toLocaleDateString()}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Budget</p>
                      <p className="font-medium">
                        ${trainee.budgetSpent || 0} / ${trainee.budgetAllocated}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Training Hours</p>
                      <p className="font-medium">
                        {trainee.trainingHoursCompleted || 0} / {trainee.trainingHoursRequired}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Reviews Completed</p>
                      <p className="font-medium">
                        {trainee.quarterlyReviews?.filter(r => r.completedDate).length || 0} / {trainee.quarterlyReviews?.length || 0}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </DialogContent>
    </Dialog>
  );
};