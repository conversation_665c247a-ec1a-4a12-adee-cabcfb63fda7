import React from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Calendar,
  User,
  Building,
  Award,
  Target,
  Clock,
  DollarSign,
  FileText,
  Star,
  CheckCircle,
  AlertCircle,
  X
} from 'lucide-react';
import { cn } from '@/lib/utils';
import type { DialogType, GraduateTraineeProgram } from '../types/trainee.types';
import '../styles/graduateTrainee.css';

interface TraineeDialogProps {
  isOpen: boolean;
  type: DialogType | null;
  trainee?: GraduateTraineeProgram | null;
  onClose: () => void;
  onConfirm: (data?: any) => void;
}

export const TraineeDialog: React.FC<TraineeDialogProps> = ({
  isOpen,
  type,
  trainee,
  onClose,
  onConfirm
}) => {
  // Dialog configurations
  const getDialogConfig = () => {
    switch (type) {
      case 'view':
        return {
          title: 'Trainee Details',
          description: 'View comprehensive trainee information',
          showFooter: false,
          size: 'lg'
        };
      case 'create':
        return {
          title: 'Add New Trainee',
          description: 'Create a new graduate trainee program',
          showFooter: true,
          size: 'md'
        };
      case 'edit':
        return {
          title: 'Edit Trainee',
          description: 'Update trainee information',
          showFooter: true,
          size: 'md'
        };
      case 'delete':
        return {
          title: 'Delete Trainee',
          description: 'This action cannot be undone',
          showFooter: true,
          size: 'sm'
        };
      case 'quarterlyReview':
        return {
          title: 'Quarterly Review',
          description: 'Record quarterly performance review',
          showFooter: true,
          size: 'md'
        };
      case 'certification':
        return {
          title: 'Manage Certifications',
          description: 'Add or update certification records',
          showFooter: true,
          size: 'md'
        };
      case 'skillsDevelopment':
        return {
          title: 'Skills Development',
          description: 'Track skill progression',
          showFooter: true,
          size: 'md'
        };
      default:
        return {
          title: '',
          description: '',
          showFooter: false,
          size: 'md'
        };
    }
  };

  const config = getDialogConfig();

  // Render content based on dialog type
  const renderContent = () => {
    switch (type) {
      case 'view':
        return <ViewTraineeContent trainee={trainee} />;
      case 'create':
      case 'edit':
        return <TraineeFormContent trainee={trainee} isEdit={type === 'edit'} />;
      case 'delete':
        return <DeleteConfirmContent trainee={trainee} />;
      case 'quarterlyReview':
        return <QuarterlyReviewContent trainee={trainee} />;
      case 'certification':
        return <CertificationContent trainee={trainee} />;
      case 'skillsDevelopment':
        return <SkillsContent trainee={trainee} />;
      default:
        return null;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className={cn('glass-panel', {
        'max-w-2xl': config.size === 'lg',
        'max-w-lg': config.size === 'md',
        'max-w-sm': config.size === 'sm'
      })}>
        <DialogHeader>
          <DialogTitle className="text-base">{config.title}</DialogTitle>
          {config.description && (
            <DialogDescription className="text-mini">
              {config.description}
            </DialogDescription>
          )}
        </DialogHeader>
        
        <div className="py-4">
          {renderContent()}
        </div>

        {config.showFooter && (
          <DialogFooter>
            <Button variant="outline" size="sm" onClick={onClose} className="text-mini">
              Cancel
            </Button>
            <Button size="sm" onClick={() => onConfirm()} className="text-mini">
              {type === 'delete' ? 'Delete' : 'Save'}
            </Button>
          </DialogFooter>
        )}
      </DialogContent>
    </Dialog>
  );
};

// View Trainee Content Component
const ViewTraineeContent: React.FC<{ trainee?: GraduateTraineeProgram | null }> = ({ trainee }) => {
  if (!trainee) return null;

  return (
    <Tabs defaultValue="overview" className="w-full">
      <TabsList className="grid w-full grid-cols-4 h-8">
        <TabsTrigger value="overview" className="text-mini">Overview</TabsTrigger>
        <TabsTrigger value="progress" className="text-mini">Progress</TabsTrigger>
        <TabsTrigger value="reviews" className="text-mini">Reviews</TabsTrigger>
        <TabsTrigger value="skills" className="text-mini">Skills</TabsTrigger>
      </TabsList>

      <TabsContent value="overview" className="space-y-4 mt-4">
        <div className="flex items-start gap-4">
          <Avatar className="h-12 w-12">
            <AvatarFallback>
              {trainee.employeeName.split(' ').map(n => n[0]).join('')}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1">
            <h3 className="font-semibold">{trainee.employeeName}</h3>
            <p className="text-mini text-muted-foreground">{trainee.employeeId}</p>
            <div className="flex gap-2 mt-2">
              <Badge variant="outline" className="text-micro">
                {trainee.department}
              </Badge>
              <Badge variant="outline" className="text-micro">
                {trainee.position}
              </Badge>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-3">
          <div className="space-y-1">
            <p className="text-mini text-muted-foreground flex items-center gap-1">
              <User className="icon-micro" /> Manager
            </p>
            <p className="text-sm font-medium">{trainee.manager}</p>
          </div>
          <div className="space-y-1">
            <p className="text-mini text-muted-foreground flex items-center gap-1">
              <User className="icon-micro" /> Mentor
            </p>
            <p className="text-sm font-medium">{trainee.mentor || 'Not assigned'}</p>
          </div>
          <div className="space-y-1">
            <p className="text-mini text-muted-foreground flex items-center gap-1">
              <Calendar className="icon-micro" /> Start Date
            </p>
            <p className="text-sm font-medium">{trainee.startDate}</p>
          </div>
          <div className="space-y-1">
            <p className="text-mini text-muted-foreground flex items-center gap-1">
              <Calendar className="icon-micro" /> End Date
            </p>
            <p className="text-sm font-medium">{trainee.expectedEndDate}</p>
          </div>
        </div>
      </TabsContent>

      <TabsContent value="progress" className="space-y-4 mt-4">
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Overall Progress</span>
            <span className="font-medium">{trainee.overallProgress}%</span>
          </div>
          <Progress value={trainee.overallProgress} className="h-2" />
        </div>
        <div className="grid grid-cols-2 gap-3">
          <div className="glass-card p-3 rounded-md">
            <p className="text-mini text-muted-foreground">Training Hours</p>
            <p className="text-lg font-semibold">
              {trainee.trainingHoursCompleted} / {trainee.trainingHoursRequired}
            </p>
          </div>
          <div className="glass-card p-3 rounded-md">
            <p className="text-mini text-muted-foreground">Budget Used</p>
            <p className="text-lg font-semibold">
              ${trainee.budgetSpent} / ${trainee.budgetAllocated}
            </p>
          </div>
        </div>
      </TabsContent>

      <TabsContent value="reviews" className="mt-4">
        <ScrollArea className="h-[200px]">
          {trainee.quarterlyReviews?.length > 0 ? (
            <div className="space-y-3">
              {trainee.quarterlyReviews.map((review, index) => (
                <div key={index} className="glass-card p-3 rounded-md">
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <p className="text-sm font-medium">Q{review.reviewNumber} Review</p>
                      <p className="text-mini text-muted-foreground">{review.completedDate || review.dueDate}</p>
                    </div>
                    <div className="flex items-center gap-1">
                      <Star className="icon-micro fill-yellow-400 text-yellow-400" />
                      <span className="text-sm font-medium">{review.overallRating}/5</span>
                    </div>
                  </div>
                  <p className="text-mini text-muted-foreground">{review.feedback}</p>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-sm text-muted-foreground text-center py-4">No reviews yet</p>
          )}
        </ScrollArea>
      </TabsContent>

      <TabsContent value="skills" className="mt-4">
        <ScrollArea className="h-[200px]">
          {trainee.skillsDevelopment?.length > 0 ? (
            <div className="space-y-3">
              {trainee.skillsDevelopment.map((skill, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>{skill.skillName}</span>
                    <span className="text-mini text-muted-foreground">{skill.currentLevel}/{skill.targetLevel}</span>
                  </div>
                  <Progress value={(skill.currentLevel / skill.targetLevel) * 100} className="h-1.5" />
                </div>
              ))}
            </div>
          ) : (
            <p className="text-sm text-muted-foreground text-center py-4">No skills tracked</p>
          )}
        </ScrollArea>
      </TabsContent>
    </Tabs>
  );
};

// Form Content Component
const TraineeFormContent: React.FC<{ trainee?: GraduateTraineeProgram | null; isEdit: boolean }> = ({ trainee, isEdit }) => {
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-3">
        <div className="space-y-2">
          <Label htmlFor="employeeId" className="text-mini">Employee ID</Label>
          <Input id="employeeId" defaultValue={trainee?.employeeId} className="h-8 text-sm" />
        </div>
        <div className="space-y-2">
          <Label htmlFor="employeeName" className="text-mini">Employee Name</Label>
          <Input id="employeeName" defaultValue={trainee?.employeeName} className="h-8 text-sm" />
        </div>
      </div>
      <div className="grid grid-cols-2 gap-3">
        <div className="space-y-2">
          <Label htmlFor="department" className="text-mini">Department</Label>
          <Select defaultValue={trainee?.department}>
            <SelectTrigger className="h-8 text-sm">
              <SelectValue placeholder="Select department" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Technology">Technology</SelectItem>
              <SelectItem value="Finance">Finance</SelectItem>
              <SelectItem value="Marketing">Marketing</SelectItem>
              <SelectItem value="Operations">Operations</SelectItem>
              <SelectItem value="Human Resources">Human Resources</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="space-y-2">
          <Label htmlFor="position" className="text-mini">Position</Label>
          <Input id="position" defaultValue={trainee?.position} className="h-8 text-sm" />
        </div>
      </div>
      <div className="grid grid-cols-2 gap-3">
        <div className="space-y-2">
          <Label htmlFor="manager" className="text-mini">Manager</Label>
          <Input id="manager" defaultValue={trainee?.manager} className="h-8 text-sm" />
        </div>
        <div className="space-y-2">
          <Label htmlFor="mentor" className="text-mini">Mentor (Optional)</Label>
          <Input id="mentor" defaultValue={trainee?.mentor} className="h-8 text-sm" />
        </div>
      </div>
    </div>
  );
};

// Delete Confirmation Content
const DeleteConfirmContent: React.FC<{ trainee?: GraduateTraineeProgram | null }> = ({ trainee }) => {
  if (!trainee) return null;
  
  return (
    <div className="space-y-4">
      <div className="flex items-center gap-3 p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
        <AlertCircle className="w-5 h-5 text-red-500" />
        <div className="flex-1">
          <p className="text-sm font-medium">Are you sure you want to delete this trainee?</p>
          <p className="text-mini text-muted-foreground mt-1">
            This will permanently delete {trainee.employeeName} ({trainee.employeeId}) and all associated data.
          </p>
        </div>
      </div>
    </div>
  );
};

// Quarterly Review Content
const QuarterlyReviewContent: React.FC<{ trainee?: GraduateTraineeProgram | null }> = ({ trainee }) => {
  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="rating" className="text-mini">Overall Rating</Label>
        <Select defaultValue="4">
          <SelectTrigger className="h-8 text-sm">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="1">1 - Needs Improvement</SelectItem>
            <SelectItem value="2">2 - Below Expectations</SelectItem>
            <SelectItem value="3">3 - Meets Expectations</SelectItem>
            <SelectItem value="4">4 - Exceeds Expectations</SelectItem>
            <SelectItem value="5">5 - Outstanding</SelectItem>
          </SelectContent>
        </Select>
      </div>
      <div className="space-y-2">
        <Label htmlFor="feedback" className="text-mini">Feedback</Label>
        <Textarea id="feedback" className="min-h-[100px] text-sm" placeholder="Enter review feedback..." />
      </div>
    </div>
  );
};

// Certification Content
const CertificationContent: React.FC<{ trainee?: GraduateTraineeProgram | null }> = ({ trainee }) => {
  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="certName" className="text-mini">Certification Name</Label>
        <Input id="certName" className="h-8 text-sm" placeholder="e.g., AWS Cloud Practitioner" />
      </div>
      <div className="space-y-2">
        <Label htmlFor="issuer" className="text-mini">Issuing Body</Label>
        <Input id="issuer" className="h-8 text-sm" placeholder="e.g., Amazon" />
      </div>
      <div className="grid grid-cols-2 gap-3">
        <div className="space-y-2">
          <Label htmlFor="achieved" className="text-mini">Date Achieved</Label>
          <Input id="achieved" type="date" className="h-8 text-sm" />
        </div>
        <div className="space-y-2">
          <Label htmlFor="expiry" className="text-mini">Expiry Date</Label>
          <Input id="expiry" type="date" className="h-8 text-sm" />
        </div>
      </div>
    </div>
  );
};

// Skills Development Content
const SkillsContent: React.FC<{ trainee?: GraduateTraineeProgram | null }> = ({ trainee }) => {
  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="skillName" className="text-mini">Skill Name</Label>
        <Input id="skillName" className="h-8 text-sm" placeholder="e.g., React Development" />
      </div>
      <div className="grid grid-cols-3 gap-3">
        <div className="space-y-2">
          <Label htmlFor="baseline" className="text-mini">Baseline Level</Label>
          <Input id="baseline" type="number" min="1" max="10" className="h-8 text-sm" />
        </div>
        <div className="space-y-2">
          <Label htmlFor="current" className="text-mini">Current Level</Label>
          <Input id="current" type="number" min="1" max="10" className="h-8 text-sm" />
        </div>
        <div className="space-y-2">
          <Label htmlFor="target" className="text-mini">Target Level</Label>
          <Input id="target" type="number" min="1" max="10" className="h-8 text-sm" />
        </div>
      </div>
    </div>
  );
};

export default TraineeDialog;