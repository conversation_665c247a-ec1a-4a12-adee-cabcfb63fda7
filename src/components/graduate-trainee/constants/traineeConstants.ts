// Consolidated constants for Graduate Trainee module
// Single source of truth for all shared values

export const DEPARTMENTS = [
  { value: '', label: 'All Departments' },
  { value: 'Engineering', label: 'Engineering' },
  { value: 'Marketing', label: 'Marketing' },
  { value: 'Finance', label: 'Finance' },
  { value: 'Operations', label: 'Operations' },
  { value: 'HR', label: 'Human Resources' },
  { value: 'Sales', label: 'Sales' },
  { value: 'IT', label: 'Information Technology' },
  { value: 'Product', label: 'Product Management' },
  { value: 'Design', label: 'Design' },
] as const;

export const TRAINEE_STATUSES = [
  { value: '', label: 'All Statuses' },
  { value: 'not_started', label: 'Not Started', color: 'gray', icon: 'Clock' },
  { value: 'in_progress', label: 'In Progress', color: 'blue', icon: 'Loader' },
  { value: 'on_hold', label: 'On Hold', color: 'yellow', icon: 'AlertCircle' },
  { value: 'completed', label: 'Completed', color: 'green', icon: 'CheckCircle' },
  { value: 'terminated', label: 'Terminated', color: 'red', icon: 'XCircle' },
] as const;

export const SKILL_LEVELS = [
  { value: 'beginner', label: 'Beginner', level: 1 },
  { value: 'intermediate', label: 'Intermediate', level: 2 },
  { value: 'advanced', label: 'Advanced', level: 3 },
  { value: 'expert', label: 'Expert', level: 4 },
] as const;

export const CERTIFICATION_STATUSES = [
  { value: 'planned', label: 'Planned', color: 'gray' },
  { value: 'in_progress', label: 'In Progress', color: 'blue' },
  { value: 'completed', label: 'Completed', color: 'green' },
  { value: 'expired', label: 'Expired', color: 'red' },
] as const;

export const REVIEW_QUARTERS = [
  { value: 1, label: 'Q1 Review' },
  { value: 2, label: 'Q2 Review' },
  { value: 3, label: 'Q3 Review' },
  { value: 4, label: 'Q4 Review' },
] as const;

export const DEFAULT_FILTERS = {
  status: '',
  department: '',
  manager: '',
  searchQuery: '',
  dateRange: null,
} as const;

export const PROGRAM_DURATIONS = [
  { value: 6, label: '6 months' },
  { value: 12, label: '12 months' },
  { value: 18, label: '18 months' },
  { value: 24, label: '24 months' },
] as const;

export const REPORT_TYPES = [
  { value: 'summary', label: 'Summary Report', icon: 'FileText' },
  { value: 'detailed', label: 'Detailed Report', icon: 'FileText' },
  { value: 'progress', label: 'Progress Report', icon: 'TrendingUp' },
  { value: 'skills', label: 'Skills Report', icon: 'BookOpen' },
  { value: 'certifications', label: 'Certifications Report', icon: 'Award' },
  { value: 'reviews', label: 'Reviews Report', icon: 'Calendar' },
] as const;

export const VIEW_MODES = {
  GRID: 'grid',
  LIST: 'list',
  KANBAN: 'kanban',
} as const;

export const TAB_ITEMS = [
  { value: 'dashboard', label: 'Dashboard', icon: 'BarChart3' },
  { value: 'progress', label: 'Progress', icon: 'Target' },
  { value: 'reports', label: 'Reports', icon: 'FileText' },
  { value: 'settings', label: 'Settings', icon: 'Settings' },
] as const;

// Validation schemas
export const VALIDATION = {
  NAME_MIN_LENGTH: 2,
  NAME_MAX_LENGTH: 100,
  EMAIL_PATTERN: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
  EMPLOYEE_ID_PATTERN: /^[A-Z0-9-]+$/i,
  MIN_PROGRESS: 0,
  MAX_PROGRESS: 100,
} as const;

// API endpoints (if needed)
export const ENDPOINTS = {
  BASE: '/api/graduate-trainee',
  PROGRAMS: '/api/graduate-trainee/programs',
  REVIEWS: '/api/graduate-trainee/reviews',
  SKILLS: '/api/graduate-trainee/skills',
  CERTIFICATIONS: '/api/graduate-trainee/certifications',
  REPORTS: '/api/graduate-trainee/reports',
} as const;

// Chart colors for consistency
export const CHART_COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

// Helper functions for status
import React from 'react';
import { CheckCircle, XCircle, AlertCircle, Clock, Loader } from 'lucide-react';

export const getStatusIcon = (status: string) => {
  switch (status) {
    case 'completed': 
      return React.createElement(CheckCircle, { className: "h-4 w-4 text-green-500" });
    case 'terminated': 
      return React.createElement(XCircle, { className: "h-4 w-4 text-red-500" });
    case 'on_hold': 
      return React.createElement(AlertCircle, { className: "h-4 w-4 text-yellow-500" });
    case 'in_progress':
      return React.createElement(Loader, { className: "h-4 w-4 text-blue-500" });
    default: 
      return React.createElement(Clock, { className: "h-4 w-4 text-gray-500" });
  }
};

export const getStatusColor = (status: string): string => {
  switch (status) {
    case 'completed': return 'green';
    case 'terminated': return 'red';
    case 'on_hold': return 'yellow';
    case 'in_progress': return 'blue';
    default: return 'gray';
  }
};