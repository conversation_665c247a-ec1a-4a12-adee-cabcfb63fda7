import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import GraduateTraineeHub from './GraduateTraineeHub';
import GraduateTraineeTracker from './pages/GraduateTraineeTracker';
import { TraineeDashboard } from './pages/TraineeDashboard';
import { TraineeProgressDashboard } from './pages/TraineeProgressDashboard';
import { TraineeDetailPanel } from './modals/TraineeDetailPanel';
import { TraineeProvider } from './context/TraineeContext';
import type { GraduateTraineeProgram } from '@/lib/api/graduateTrainee';

// Wrapper components with proper props
const TraineeDashboardWrapper: React.FC = () => {
  const mockFilters = {
    status: '',
    department: '',
    manager: '',
    searchQuery: ''
  };
  
  return (
    <TraineeDashboard 
      trainees={[]}
      onViewTrainee={() => {}}
      onEditTrainee={() => {}}
      filters={mockFilters}
      onFilterChange={() => {}}
      onClearFilters={() => {}}
    />
  );
};

const TraineeProgressDashboardWrapper: React.FC = () => {
  return <TraineeProgressDashboard programs={[]} />;
};

export const GraduateTraineeRoutes: React.FC = () => {
  return (
    <Routes>
      <Route path="/" element={<GraduateTraineeHub />} />
      <Route path="/tracker" element={<GraduateTraineeTracker />} />
      <Route path="/dashboard" element={<TraineeDashboardWrapper />} />
      <Route path="/progress" element={<TraineeProgressDashboardWrapper />} />
      <Route path="/trainee/:id" element={<TraineeDetailView />} />
      <Route path="*" element={<Navigate to="/" replace />} />
    </Routes>
  );
};

const TraineeDetailView: React.FC = () => {
  // Create a mock trainee for the detail view
  const mockTrainee: GraduateTraineeProgram = {
    id: 1,
    employeeId: 'EMP001',
    employeeName: 'Loading...',
    department: 'Loading...',
    position: 'Graduate Trainee',
    manager: 'Loading...',
    mentor: '',
    startDate: new Date().toISOString().split('T')[0],
    expectedEndDate: new Date().toISOString().split('T')[0],
    programDurationMonths: 18,
    status: 'not_started',
    overallProgress: 0,
    trainingHoursRequired: 0,
    trainingHoursCompleted: 0,
    budgetAllocated: 0,
    budgetSpent: 0,
    quarterlyReviews: [],
    skillsDevelopment: [],
    certifications: [],
    notes: '',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
  
  return (
    <div className="container mx-auto p-6">
      <TraineeDetailPanel 
        trainee={mockTrainee} 
        onClose={() => window.history.back()} 
        onEdit={() => {}}
        onUpdateTrainee={() => {}}
      />
    </div>
  );
};

export const graduateTraineeRouteConfig = {
  path: '/graduate-trainee',
  element: <GraduateTraineeRoutes />,
  children: [
    {
      index: true,
      element: <GraduateTraineeHub />
    },
    {
      path: 'tracker',
      element: <GraduateTraineeTracker />
    },
    {
      path: 'dashboard', 
      element: <TraineeDashboardWrapper />
    },
    {
      path: 'progress',
      element: <TraineeProgressDashboardWrapper />
    },
    {
      path: 'trainee/:id',
      element: <TraineeDetailView />
    }
  ]
};