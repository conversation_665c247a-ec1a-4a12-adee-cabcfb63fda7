/* Graduate Trainee Module - Professional Minimalist Styles */

/* Glassmorphism Base */
.glass-panel {
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.08);
  transition: all 0.2s ease;
}

.glass-panel-hover:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.12);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.glass-card {
  background: rgba(255, 255, 255, 0.02);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.06);
  transition: all 0.2s ease;
}

.glass-card-interactive:hover {
  background: rgba(255, 255, 255, 0.04);
  border-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

/* Typography - Smaller, Professional */
.text-micro {
  font-size: 0.625rem; /* 10px */
  line-height: 1rem;
}

.text-nano {
  font-size: 0.6875rem; /* 11px */
  line-height: 1.125rem;
}

.text-mini {
  font-size: 0.75rem; /* 12px */
  line-height: 1.25rem;
}

/* Compact Metrics Cards */
.metric-card-compact {
  padding: 0.75rem;
  height: 3.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.metric-value-compact {
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1;
}

.metric-label-compact {
  font-size: 0.6875rem;
  color: rgba(255, 255, 255, 0.6);
  margin-top: 0.125rem;
}

/* Minimal Status Badge */
.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  display: inline-block;
  margin-right: 0.375rem;
}

.status-badge-minimal {
  display: inline-flex;
  align-items: center;
  padding: 0.125rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.6875rem;
  font-weight: 500;
  transition: all 0.15s ease;
}

/* Trainee Card Redesign */
.trainee-card-minimal {
  padding: 0.875rem;
  border-radius: 0.5rem;
  transition: all 0.2s ease;
}

.trainee-card-minimal:hover {
  background: rgba(255, 255, 255, 0.03);
}

.trainee-avatar-small {
  width: 32px;
  height: 32px;
  font-size: 0.75rem;
}

.trainee-name {
  font-size: 0.875rem;
  font-weight: 600;
  line-height: 1.2;
}

.trainee-id {
  font-size: 0.625rem;
  color: rgba(255, 255, 255, 0.5);
}

/* Micro Progress Bar */
.progress-micro {
  height: 3px;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 1.5px;
  overflow: hidden;
  transition: opacity 0.2s ease;
}

.progress-micro-fill {
  height: 100%;
  transition: width 0.3s ease;
  border-radius: 1.5px;
}

/* Subtle Buttons */
.btn-ghost-minimal {
  padding: 0.25rem 0.625rem;
  font-size: 0.6875rem;
  border: 1px solid transparent;
  background: transparent;
  color: rgba(255, 255, 255, 0.7);
  border-radius: 0.25rem;
  transition: all 0.15s ease;
}

.btn-ghost-minimal:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
}

/* Quick Actions Grid - Compact */
.quick-action-minimal {
  padding: 0.625rem;
  border-radius: 0.375rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  transition: all 0.15s ease;
  border: 1px solid transparent;
}

.quick-action-minimal:hover {
  background: rgba(255, 255, 255, 0.04);
  border-color: rgba(255, 255, 255, 0.08);
  transform: translateX(2px);
}

/* Filter Bar - Streamlined */
.filter-input-minimal {
  height: 32px;
  padding: 0 0.75rem;
  font-size: 0.75rem;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  transition: all 0.15s ease;
}

.filter-input-minimal:focus {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.15);
  outline: none;
}

/* Tab Navigation - Refined */
.tab-minimal {
  padding: 0.375rem 0.875rem;
  font-size: 0.75rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.6);
  border-bottom: 2px solid transparent;
  transition: all 0.15s ease;
}

.tab-minimal:hover {
  color: rgba(255, 255, 255, 0.8);
  background: rgba(255, 255, 255, 0.02);
}

.tab-minimal.active {
  color: rgba(255, 255, 255, 0.95);
  border-bottom-color: currentColor;
}

/* Header Section - Compact */
.header-minimal {
  padding: 1rem 1.25rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
}

.header-title {
  font-size: 1.125rem;
  font-weight: 600;
  letter-spacing: -0.025em;
}

.header-subtitle {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.6);
  margin-top: 0.125rem;
}

/* Data Grid - Professional */
.data-row-minimal {
  padding: 0.625rem 0.875rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.04);
  transition: background 0.15s ease;
}

.data-row-minimal:hover {
  background: rgba(255, 255, 255, 0.02);
}

/* Icon Sizing */
.icon-micro {
  width: 12px;
  height: 12px;
}

.icon-mini {
  width: 14px;
  height: 14px;
}

.icon-small {
  width: 16px;
  height: 16px;
}

/* Smooth Transitions */
.transition-smooth {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-fast {
  transition: all 0.15s ease;
}

/* Scrollbar Styling */
.custom-scrollbar::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.02);
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.15);
}

/* Subtle Shadows */
.shadow-minimal {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.shadow-subtle {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

/* Focus States */
.focus-ring-minimal:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1);
}

/* Loading State */
.skeleton-minimal {
  background: linear-gradient(90deg, 
    rgba(255, 255, 255, 0.03) 25%, 
    rgba(255, 255, 255, 0.06) 50%, 
    rgba(255, 255, 255, 0.03) 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s ease-in-out infinite;
}

@keyframes skeleton-loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}