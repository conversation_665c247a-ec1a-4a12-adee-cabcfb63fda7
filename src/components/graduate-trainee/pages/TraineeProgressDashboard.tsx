import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import {
  <PERSON><PERSON>hart,
  Bar,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
} from 'recharts';
import {
  TrendingUp,
  Users,
  Award,
  BookOpen,
  Calendar,
  DollarSign,
  Clock,
  Target,
} from 'lucide-react';
import type { GraduateTraineeProgram } from '@/lib/api/graduateTrainee';

interface TraineeProgressDashboardProps {
  programs: GraduateTraineeProgram[];
}

export const TraineeProgressDashboard: React.FC<TraineeProgressDashboardProps> = ({ programs }) => {
  // Calculate metrics
  const totalTrainees = programs.length;
  const activeTrainees = programs.filter(p => p.status === 'in_progress').length;
  const completedTrainees = programs.filter(p => p.status === 'completed').length;
  const avgProgress = programs.length > 0
    ? programs.reduce((sum, p) => sum + p.overallProgress, 0) / programs.length
    : 0;

  // Department distribution
  const departmentData = programs.reduce((acc, program) => {
    const dept = acc.find(d => d.name === program.department);
    if (dept) {
      dept.value++;
    } else {
      acc.push({ name: program.department, value: 1 });
    }
    return acc;
  }, [] as { name: string; value: number }[]);

  // Status distribution
  const statusData = [
    { name: 'Not Started', value: programs.filter(p => p.status === 'not_started').length, color: '#6b7280' },
    { name: 'In Progress', value: activeTrainees, color: '#3b82f6' },
    { name: 'On Hold', value: programs.filter(p => p.status === 'on_hold').length, color: '#f59e0b' },
    { name: 'Completed', value: completedTrainees, color: '#10b981' },
    { name: 'Terminated', value: programs.filter(p => p.status === 'terminated').length, color: '#ef4444' },
  ].filter(s => s.value > 0);

  // Progress by department
  const progressByDept = departmentData.map(dept => {
    const deptPrograms = programs.filter(p => p.department === dept.name);
    const avgDeptProgress = deptPrograms.reduce((sum, p) => sum + p.overallProgress, 0) / deptPrograms.length;
    return {
      department: dept.name,
      progress: Math.round(avgDeptProgress),
      count: deptPrograms.length,
    };
  });

  // Skills development overview
  const skillsData = programs.flatMap(p => p.skillsDevelopment || []);
  const skillCategories = skillsData.reduce((acc, skill) => {
    const cat = acc.find(c => c.category === skill.category);
    if (cat) {
      cat.avgProgress = (cat.avgProgress * cat.count + skill.progressPercentage) / (cat.count + 1);
      cat.count++;
    } else {
      acc.push({
        category: skill.category,
        avgProgress: skill.progressPercentage,
        count: 1,
      });
    }
    return acc;
  }, [] as { category: string; avgProgress: number; count: number }[]);

  // Training hours analysis
  const trainingData = programs.map(p => ({
    name: p.employeeName.split(' ')[0],
    completed: p.trainingHoursCompleted,
    required: p.trainingHoursRequired,
    percentage: Math.round((p.trainingHoursCompleted / p.trainingHoursRequired) * 100),
  })).slice(0, 10); // Top 10 for readability

  // Budget utilization
  const budgetData = programs.map(p => ({
    name: p.employeeName.split(' ')[0],
    allocated: p.budgetAllocated,
    spent: p.budgetSpent,
    utilization: Math.round((p.budgetSpent / p.budgetAllocated) * 100),
  })).slice(0, 10);

  // Certification status
  const certificationData = programs.flatMap(p => p.certifications || []);
  const certStatus = {
    completed: certificationData.filter(c => c.status === 'completed').length,
    inProgress: certificationData.filter(c => c.status === 'in_progress').length,
    notStarted: certificationData.filter(c => c.status === 'not_started').length,
    total: certificationData.length,
  };

  // Quarterly review completion
  const reviewData = programs.flatMap(p => p.quarterlyReviews || []);
  const reviewCompletion = {
    completed: reviewData.filter(r => r.completedDate).length,
    pending: reviewData.filter(r => !r.completedDate).length,
    avgRating: reviewData.filter(r => r.completedDate && r.overallRating > 0)
      .reduce((sum, r) => sum + r.overallRating, 0) / 
      reviewData.filter(r => r.completedDate && r.overallRating > 0).length || 0,
  };

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Trainees</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalTrainees}</div>
            <p className="text-xs text-muted-foreground">
              {activeTrainees} active, {completedTrainees} completed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Progress</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{Math.round(avgProgress)}%</div>
            <Progress value={avgProgress} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Certifications</CardTitle>
            <Award className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{certStatus.completed}/{certStatus.total}</div>
            <p className="text-xs text-muted-foreground">
              {certStatus.inProgress} in progress
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Review Rating</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{reviewCompletion.avgRating.toFixed(1)}/5</div>
            <p className="text-xs text-muted-foreground">
              {reviewCompletion.completed} reviews completed
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts Row 1 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Department Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Trainees by Department</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={departmentData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, value }) => `${name}: ${value}`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {departmentData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={['#0088FE', '#00C49F', '#FFBB28', '#FF8042'][index % 4]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Status Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Program Status Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={statusData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="value" fill="#8884d8">
                  {statusData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Charts Row 2 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Progress by Department */}
        <Card>
          <CardHeader>
            <CardTitle>Average Progress by Department</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={progressByDept}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="department" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="progress" fill="#3b82f6" name="Progress %" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Skills Development Radar */}
        <Card>
          <CardHeader>
            <CardTitle>Skills Development by Category</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <RadarChart data={skillCategories}>
                <PolarGrid />
                <PolarAngleAxis dataKey="category" />
                <PolarRadiusAxis angle={90} domain={[0, 100]} />
                <Radar
                  name="Average Progress"
                  dataKey="avgProgress"
                  stroke="#8884d8"
                  fill="#8884d8"
                  fillOpacity={0.6}
                />
                <Tooltip />
              </RadarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Training Hours Chart */}
      <Card>
        <CardHeader>
          <CardTitle>Training Hours Progress (Top 10)</CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={trainingData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Bar dataKey="completed" stackId="a" fill="#10b981" name="Completed Hours" />
              <Bar dataKey="required" stackId="b" fill="#e5e7eb" name="Remaining Hours" />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Budget Utilization */}
      <Card>
        <CardHeader>
          <CardTitle>Budget Utilization (Top 10)</CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={budgetData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Line type="monotone" dataKey="allocated" stroke="#8884d8" name="Allocated" />
              <Line type="monotone" dataKey="spent" stroke="#82ca9d" name="Spent" />
            </LineChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </div>
  );
};