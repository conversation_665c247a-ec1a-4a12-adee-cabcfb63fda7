import React, { useState, useMemo } from 'react';
import { Card, CardHeader, CardContent, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { But<PERSON> } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Plus, Search, Filter, Download, RefreshCw, 
  Grid, List, Users, AlertCircle 
} from 'lucide-react';
import { TraineeCard } from '../components/cards';
import { GraduateTraineeProgram } from '@/lib/api/graduateTrainee';
import { DEPARTMENTS, TRAINEE_STATUSES, VIEW_MODES } from '../constants/traineeConstants';

interface TraineeDashboardProps {
  trainees: GraduateTraineeProgram[];
  onViewTrainee: (trainee: GraduateTraineeProgram) => void;
  onEditTrainee: (trainee: GraduateTraineeProgram) => void;
  filters: {
    status: string;
    department: string;
    manager: string;
    searchQuery: string;
  };
  onFilterChange: (key: string, value: string) => void;
  onClearFilters: () => void;
  viewMode?: string;
  onViewModeChange?: (mode: string) => void;
}

export const TraineeDashboard: React.FC<TraineeDashboardProps> = ({
  trainees,
  onViewTrainee,
  onEditTrainee,
  filters,
  onFilterChange,
  onClearFilters,
  viewMode: propViewMode,
  onViewModeChange,
}) => {
  const [localViewMode, setLocalViewMode] = useState<string>(VIEW_MODES.GRID);
  const viewMode = propViewMode || localViewMode;
  const setViewMode = onViewModeChange || setLocalViewMode;

  // Statistics
  const stats = useMemo(() => {
    const total = trainees.length;
    const active = trainees.filter(t => t.status === 'in_progress').length;
    const completed = trainees.filter(t => t.status === 'completed').length;
    const onHold = trainees.filter(t => t.status === 'on_hold').length;
    
    return { total, active, completed, onHold };
  }, [trainees]);

  // Group by department
  const traineesByDepartment = useMemo(() => {
    const grouped: Record<string, GraduateTraineeProgram[]> = {};
    trainees.forEach(trainee => {
      if (!grouped[trainee.department]) {
        grouped[trainee.department] = [];
      }
      grouped[trainee.department].push(trainee);
    });
    return grouped;
  }, [trainees]);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    onFilterChange('searchQuery', e.target.value);
  };

  const handleDepartmentFilter = (value: string) => {
    onFilterChange('department', value);
  };

  const handleStatusFilter = (value: string) => {
    onFilterChange('status', value);
  };

  const activeFiltersCount = Object.values(filters).filter(v => v !== '').length;

  return (
    <div className="container mx-auto p-6">
      {/* Header Section */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-2xl font-bold">Trainee Dashboard</h2>
            <p className="text-sm text-muted-foreground">
              Manage and track all graduate trainee programs
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="icon"
              onClick={() => setViewMode(viewMode === VIEW_MODES.GRID ? VIEW_MODES.LIST : VIEW_MODES.GRID)}
            >
              {viewMode === VIEW_MODES.GRID ? <List className="h-4 w-4" /> : <Grid className="h-4 w-4" />}
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Total Trainees</p>
                  <p className="text-2xl font-bold">{stats.total}</p>
                </div>
                <Users className="h-8 w-8 text-muted-foreground" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Active</p>
                  <p className="text-2xl font-bold text-blue-600">{stats.active}</p>
                </div>
                <Badge variant="secondary" className="bg-blue-100">In Progress</Badge>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Completed</p>
                  <p className="text-2xl font-bold text-green-600">{stats.completed}</p>
                </div>
                <Badge variant="secondary" className="bg-green-100">Completed</Badge>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">On Hold</p>
                  <p className="text-2xl font-bold text-yellow-600">{stats.onHold}</p>
                </div>
                <Badge variant="secondary" className="bg-yellow-100">On Hold</Badge>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters Section */}
        <Card className="mb-6">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-base">Filters</CardTitle>
              {activeFiltersCount > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onClearFilters}
                  className="text-xs"
                >
                  Clear All ({activeFiltersCount})
                </Button>
              )}
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search by name, ID, or department..."
                    value={filters.searchQuery}
                    onChange={handleSearch}
                    className="pl-9"
                  />
                </div>
              </div>
              <Select value={filters.department} onValueChange={handleDepartmentFilter}>
                <SelectTrigger className="w-[200px]">
                  <SelectValue placeholder="All Departments" />
                </SelectTrigger>
                <SelectContent>
                  {DEPARTMENTS.map(dept => (
                    <SelectItem key={dept.value} value={dept.value || '_all'}>
                      {dept.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select value={filters.status} onValueChange={handleStatusFilter}>
                <SelectTrigger className="w-[200px]">
                  <SelectValue placeholder="All Statuses" />
                </SelectTrigger>
                <SelectContent>
                  {TRAINEE_STATUSES.map(status => (
                    <SelectItem key={status.value} value={status.value || '_all'}>
                      {status.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Trainees Display */}
      {trainees.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <AlertCircle className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Trainees Found</h3>
            <p className="text-sm text-muted-foreground text-center mb-4">
              {activeFiltersCount > 0 
                ? 'No trainees match your current filters. Try adjusting your search criteria.'
                : 'Get started by adding your first graduate trainee program.'}
            </p>
            {activeFiltersCount > 0 && (
              <Button variant="outline" onClick={onClearFilters}>
                Clear Filters
              </Button>
            )}
          </CardContent>
        </Card>
      ) : viewMode === VIEW_MODES.GRID ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {trainees.map(trainee => (
            <TraineeCard
              key={trainee.id}
              trainee={trainee}
              onView={onViewTrainee}
              onEdit={onEditTrainee}
            />
          ))}
        </div>
      ) : (
        <Card>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-4">Name</th>
                    <th className="text-left p-4">Department</th>
                    <th className="text-left p-4">Manager</th>
                    <th className="text-left p-4">Progress</th>
                    <th className="text-left p-4">Status</th>
                    <th className="text-left p-4">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {trainees.map(trainee => (
                    <tr key={trainee.id} className="border-b hover:bg-muted/50">
                      <td className="p-4">
                        <div>
                          <p className="font-medium">{trainee.employeeName}</p>
                          <p className="text-xs text-muted-foreground">{trainee.employeeId}</p>
                        </div>
                      </td>
                      <td className="p-4">{trainee.department}</td>
                      <td className="p-4">{trainee.manager}</td>
                      <td className="p-4">
                        <div className="flex items-center gap-2">
                          <div className="w-20 bg-gray-200 rounded-full h-2">
                            <div 
                              className="bg-blue-600 h-2 rounded-full"
                              style={{ width: `${(trainee.overallProgress || 0) * 100}%` }}
                            />
                          </div>
                          <span className="text-sm">
                            {Math.round((trainee.overallProgress || 0) * 100)}%
                          </span>
                        </div>
                      </td>
                      <td className="p-4">
                        <Badge variant="secondary">
                          {trainee.status.replace('_', ' ')}
                        </Badge>
                      </td>
                      <td className="p-4">
                        <div className="flex gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onViewTrainee(trainee)}
                          >
                            View
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onEditTrainee(trainee)}
                          >
                            Edit
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};