import React from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { formatCurrency } from '../../budget/utils/formatters';
import { formatPercentage } from '../../financial-dashboard/utils/formatters';

interface UnifiedMetricCardProps {
  title: string;
  value: number | string;
  change?: number;
  variant?: 'default' | 'gain' | 'loss' | 'neutral';
  className?: string;
  loading?: boolean;
  icon?: React.ReactNode;
  subtitle?: string;
  format?: 'currency' | 'percentage' | 'number' | 'text';
  size?: 'sm' | 'md' | 'lg';
}

export const UnifiedMetricCard: React.FC<UnifiedMetricCardProps> = ({
  title,
  value,
  change,
  variant = 'default',
  className = '',
  loading = false,
  icon,
  subtitle,
  format = 'currency',
  size = 'md'
}) => {
  const getChangeColor = (change: number) => {
    if (change > 0) return 'text-dashboard-success border-dashboard-success';
    if (change < 0) return 'text-dashboard-accent border-dashboard-accent';
    return 'text-gray-400 border-gray-400';
  };

  const getVariantStyles = () => {
    switch (variant) {
      case 'gain':
        return 'border-dashboard-success/20 bg-dashboard-success/5';
      case 'loss':
        return 'border-dashboard-accent/20 bg-dashboard-accent/5';
      case 'neutral':
        return 'border-dashboard-border bg-dashboard-card/50';
      default:
        return 'border-dashboard-border bg-dashboard-card';
    }
  };

  const getSizeStyles = () => {
    switch (size) {
      case 'sm':
        return 'p-3';
      case 'lg':
        return 'p-6';
      default:
        return 'p-4';
    }
  };

  const formatValue = (val: number | string) => {
    if (typeof val === 'string') return val;
    
    switch (format) {
      case 'currency':
        return formatCurrency(val);
      case 'percentage':
        return `${val}%`;
      case 'number':
        return val.toLocaleString();
      default:
        return val.toString();
    }
  };

  const getTextSize = () => {
    switch (size) {
      case 'sm':
        return 'text-lg';
      case 'lg':
        return 'text-3xl';
      default:
        return 'text-2xl';
    }
  };

  if (loading) {
    return (
      <Card className={`${getSizeStyles()} ${getVariantStyles()} ${className}`}>
        <div className="space-y-2">
          <Skeleton className="h-4 w-20" />
          <Skeleton className="h-6 w-24" />
          {change !== undefined && <Skeleton className="h-4 w-12" />}
        </div>
      </Card>
    );
  }

  return (
    <Card className={`${getSizeStyles()} ${getVariantStyles()} ${className}`}>
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {icon && <span className="text-muted-foreground">{icon}</span>}
            <p className="text-sm text-muted-foreground">{title}</p>
          </div>
          {change !== undefined && (
            <Badge 
              variant="outline" 
              className={`text-xs ${getChangeColor(change)}`}
            >
              {formatPercentage(change)}
            </Badge>
          )}
        </div>
        
        <div className={`${getTextSize()} font-bold text-foreground`}>
          {formatValue(value)}
        </div>
        
        {subtitle && (
          <p className="text-xs text-muted-foreground">{subtitle}</p>
        )}
      </div>
    </Card>
  );
};