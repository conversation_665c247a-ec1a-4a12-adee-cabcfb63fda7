import React from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Wallet, TrendingUp, BarChart3, Settings } from 'lucide-react';

export type NavigationView = 'budget' | 'financial' | 'analytics' | 'settings';

interface NavigationItem {
  id: NavigationView;
  label: string;
  icon: React.ReactNode;
  description: string;
  badge?: string | number;
}

interface IntegratedNavigationProps {
  activeView: NavigationView;
  onViewChange: (view: NavigationView) => void;
  className?: string;
  variant?: 'tabs' | 'sidebar' | 'pills';
}

const navigationItems: NavigationItem[] = [
  {
    id: 'budget',
    label: 'Budget',
    icon: <Wallet className="w-4 h-4" />,
    description: 'Training budget management'
  },
  {
    id: 'financial',
    label: 'Portfolio',
    icon: <TrendingUp className="w-4 h-4" />,
    description: 'Financial dashboard'
  },
  {
    id: 'analytics',
    label: 'Analytics',
    icon: <BarChart3 className="w-4 h-4" />,
    description: 'Performance analytics'
  },
  {
    id: 'settings',
    label: 'Settings',
    icon: <Settings className="w-4 h-4" />,
    description: 'Configuration'
  }
];

export const IntegratedNavigation: React.FC<IntegratedNavigationProps> = ({
  activeView,
  onViewChange,
  className = '',
  variant = 'tabs'
}) => {
  const renderTabs = () => (
    <div className={`flex space-x-1 bg-muted p-1 rounded-lg ${className}`}>
      {navigationItems.map((item) => (
        <Button
          key={item.id}
          variant={activeView === item.id ? "default" : "ghost"}
          size="sm"
          onClick={() => onViewChange(item.id)}
          className="flex items-center gap-2 flex-1"
        >
          {item.icon}
          <span>{item.label}</span>
          {item.badge && (
            <Badge variant="secondary" className="ml-1 text-xs">
              {item.badge}
            </Badge>
          )}
        </Button>
      ))}
    </div>
  );

  const renderSidebar = () => (
    <Card className={`p-4 space-y-2 ${className}`}>
      {navigationItems.map((item) => (
        <Button
          key={item.id}
          variant={activeView === item.id ? "default" : "ghost"}
          size="sm"
          onClick={() => onViewChange(item.id)}
          className="w-full justify-start gap-3"
        >
          {item.icon}
          <div className="flex-1 text-left">
            <div className="flex items-center justify-between">
              <span className="font-medium">{item.label}</span>
              {item.badge && (
                <Badge variant="secondary" className="text-xs">
                  {item.badge}
                </Badge>
              )}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              {item.description}
            </p>
          </div>
        </Button>
      ))}
    </Card>
  );

  const renderPills = () => (
    <div className={`flex flex-wrap gap-2 ${className}`}>
      {navigationItems.map((item) => (
        <Button
          key={item.id}
          variant={activeView === item.id ? "default" : "outline"}
          size="sm"
          onClick={() => onViewChange(item.id)}
          className="flex items-center gap-2"
        >
          {item.icon}
          <span>{item.label}</span>
          {item.badge && (
            <Badge variant="secondary" className="ml-1 text-xs">
              {item.badge}
            </Badge>
          )}
        </Button>
      ))}
    </div>
  );

  switch (variant) {
    case 'sidebar':
      return renderSidebar();
    case 'pills':
      return renderPills();
    default:
      return renderTabs();
  }
};