import React, { ReactNode, Suspense } from 'react';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface TabConfig {
  value: string;
  label: string;
  component: React.ComponentType<any>;
  props?: Record<string, any>;
  hidden?: boolean;
  icon?: React.ReactNode;
  badge?: string | number;
}

interface IntegratedTabManagerProps {
  tabs: TabConfig[];
  activeTab: string;
  onTabChange: (value: string) => void;
  className?: string;
  showTabList?: boolean;
  variant?: 'default' | 'pills' | 'underline';
}

const TabLoadingSkeleton: React.FC = () => (
  <div className="space-y-4">
    <Skeleton className="h-32 w-full" />
    <Skeleton className="h-48 w-full" />
    <Skeleton className="h-24 w-full" />
  </div>
);

export const IntegratedTabManager: React.FC<IntegratedTabManagerProps> = ({
  tabs,
  activeTab,
  onTabChange,
  className = '',
  showTabList = true,
  variant = 'default'
}) => {
  const visibleTabs = tabs.filter(tab => !tab.hidden);
  
  const getTabListClassName = () => {
    switch (variant) {
      case 'pills':
        return 'bg-muted p-1 rounded-lg';
      case 'underline':
        return 'bg-transparent border-b';
      default:
        return 'bg-muted';
    }
  };

  return (
    <Tabs 
      value={activeTab} 
      onValueChange={onTabChange} 
      className={`w-full ${className}`}
    >
      {showTabList && (
        <TabsList className={`w-full ${getTabListClassName()}`}>
          {visibleTabs.map(tab => (
            <TabsTrigger 
              key={tab.value} 
              value={tab.value}
              className="flex items-center gap-2 flex-1"
            >
              {tab.icon && <span className="w-4 h-4">{tab.icon}</span>}
              <span>{tab.label}</span>
              {tab.badge && (
                <Badge variant="secondary" className="ml-1 text-xs">
                  {tab.badge}
                </Badge>
              )}
            </TabsTrigger>
          ))}
        </TabsList>
      )}
      
      {tabs.map(tab => {
        const Component = tab.component;
        return (
          <TabsContent key={tab.value} value={tab.value} className="space-y-4 mt-6">
            <Suspense fallback={<TabLoadingSkeleton />}>
              <Component {...(tab.props || {})} />
            </Suspense>
          </TabsContent>
        );
      })}
    </Tabs>
  );
};

export default IntegratedTabManager;