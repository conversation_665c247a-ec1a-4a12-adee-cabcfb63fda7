import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { 
  Search, 
  FileText, 
  Settings, 
  BarChart3, 
  Network, 
  Bot, 
  Wallet, 
  Target, 
  Users, 
  Database, 
  BookOpen, 
  Lightbulb,
  Brain,
  X,
  Command,
  GitBranch,
  Clock,
  ChevronRight,
  LayoutGrid,
  List
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface CommandItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  shortcut?: string;
  action: () => void;
  category: string;
  description?: string;
  keywords?: string[];
  color?: string;
}

interface CommandPaletteProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onClaudeClick: () => void;
  onSettingsClick: () => void;
  onUsageClick: () => void;
  onMCPClick: () => void;
  onAgentsClick?: () => void;
  onAnalyticsClick?: () => void;
  onWorkflowClick?: () => void;
  onTrainingBudgetClick: () => void;
  onTrainingDashboardClick: () => void;
  onGraduateTraineeTrackerClick: () => void;
  onDataCenterClick: () => void;
  onContentCreatorsClick: () => void;
  onLighthouseLMClick: () => void;
  onLightbulbClick: () => void;
  onLDProfessionalTNAClick: () => void;
}

const CATEGORY_ORDER = ['Recent', 'Tools', 'Analytics', 'Training', 'Content', 'Configuration'];
const RECENT_COMMANDS_KEY = 'sanity-recent-commands';
const MAX_RECENT_COMMANDS = 6;
const VIEW_MODE_KEY = 'sanity-command-palette-view';

type ViewMode = 'grid' | 'list';

export const CommandPalette: React.FC<CommandPaletteProps> = ({
  open,
  onOpenChange,
  onClaudeClick,
  onSettingsClick,
  onUsageClick,
  onMCPClick,
  onAgentsClick,
  onAnalyticsClick,
  onWorkflowClick,
  onTrainingBudgetClick,
  onTrainingDashboardClick,
  onGraduateTraineeTrackerClick,
  onDataCenterClick,
  onContentCreatorsClick,
  onLighthouseLMClick,
  onLightbulbClick,
  onLDProfessionalTNAClick,
}) => {
  const [search, setSearch] = useState('');
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [recentCommands, setRecentCommands] = useState<string[]>([]);
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const inputRef = useRef<HTMLInputElement>(null);
  const listRef = useRef<HTMLDivElement>(null);
  const itemRefs = useRef<(HTMLButtonElement | null)[]>([]);

  // Load preferences from localStorage
  useEffect(() => {
    try {
      const storedRecent = localStorage.getItem(RECENT_COMMANDS_KEY);
      if (storedRecent) {
        setRecentCommands(JSON.parse(storedRecent));
      }
      const storedView = localStorage.getItem(VIEW_MODE_KEY);
      if (storedView === 'list' || storedView === 'grid') {
        setViewMode(storedView);
      }
    } catch (error) {
      console.error('Failed to load preferences:', error);
    }
  }, []);

  const toggleViewMode = () => {
    const newMode = viewMode === 'grid' ? 'list' : 'grid';
    setViewMode(newMode);
    localStorage.setItem(VIEW_MODE_KEY, newMode);
  };

  const recordCommand = useCallback((commandId: string) => {
    const updated = [commandId, ...recentCommands.filter(id => id !== commandId)].slice(0, MAX_RECENT_COMMANDS);
    setRecentCommands(updated);
    try {
      localStorage.setItem(RECENT_COMMANDS_KEY, JSON.stringify(updated));
    } catch (error) {
      console.error('Failed to save recent commands:', error);
    }
  }, [recentCommands]);

  const commands: CommandItem[] = useMemo(() => {
    const items: CommandItem[] = [
      {
        id: 'lightbulb',
        label: 'Lightbulb',
        icon: <Lightbulb className="h-6 w-6" />,
        description: 'Intelligent assistant',
        color: 'from-yellow-500/20 to-orange-500/20',
        action: () => {
          recordCommand('lightbulb');
          onLightbulbClick();
          onOpenChange(false);
        },
        category: 'Tools',
        keywords: ['idea', 'assistant', 'help']
      },
      {
        id: 'lighthouse',
        label: 'LighthouseLM',
        icon: <Brain className="h-6 w-6" />,
        description: 'AI learning',
        color: 'from-purple-500/20 to-pink-500/20',
        action: () => {
          recordCommand('lighthouse');
          onLighthouseLMClick();
          onOpenChange(false);
        },
        category: 'Tools',
        keywords: ['ai', 'learning', 'lm']
      },
      {
        id: 'usage',
        label: 'Usage',
        icon: <BarChart3 className="h-6 w-6" />,
        shortcut: '⌘U',
        description: 'Monitor usage',
        color: 'from-blue-500/20 to-cyan-500/20',
        action: () => {
          recordCommand('usage');
          onUsageClick();
          onOpenChange(false);
        },
        category: 'Analytics',
        keywords: ['stats', 'metrics', 'consumption']
      },
      {
        id: 'analytics',
        label: 'Analytics',
        icon: <BarChart3 className="h-6 w-6" />,
        shortcut: '⌘⇧A',
        description: 'Agent metrics',
        color: 'from-green-500/20 to-emerald-500/20',
        action: () => {
          recordCommand('analytics');
          onAnalyticsClick?.();
          onOpenChange(false);
        },
        category: 'Analytics',
        keywords: ['performance', 'agents', 'metrics']
      },
      {
        id: 'workflow',
        label: 'Workflow',
        icon: <GitBranch className="h-6 w-6" />,
        shortcut: '⌘W',
        description: 'Design flows',
        color: 'from-indigo-500/20 to-blue-500/20',
        action: () => {
          recordCommand('workflow');
          onWorkflowClick?.();
          onOpenChange(false);
        },
        category: 'Tools',
        keywords: ['automation', 'flow', 'process']
      },
      {
        id: 'training-budget',
        label: 'Budget',
        icon: <Wallet className="h-6 w-6" />,
        description: 'Training budget',
        color: 'from-emerald-500/20 to-green-500/20',
        action: () => {
          recordCommand('training-budget');
          onTrainingBudgetClick();
          onOpenChange(false);
        },
        category: 'Training',
        keywords: ['budget', 'finance', 'allocation']
      },
      {
        id: 'training-dashboard',
        label: 'Training',
        icon: <Target className="h-6 w-6" />,
        description: 'Track progress',
        color: 'from-red-500/20 to-rose-500/20',
        action: () => {
          recordCommand('training-dashboard');
          onTrainingDashboardClick();
          onOpenChange(false);
        },
        category: 'Training',
        keywords: ['progress', 'tracking', 'learning']
      },
      {
        id: 'graduate-trainees',
        label: 'Graduates',
        icon: <Users className="h-6 w-6" />,
        shortcut: '⌘G',
        description: 'Trainee hub',
        color: 'from-violet-500/20 to-purple-500/20',
        action: () => {
          recordCommand('graduate-trainees');
          onGraduateTraineeTrackerClick();
          onOpenChange(false);
        },
        category: 'Training',
        keywords: ['graduates', 'trainees', 'hub']
      },
      {
        id: 'ld-tna',
        label: 'LD TNA',
        icon: <Brain className="h-6 w-6" />,
        description: 'Needs assessment',
        color: 'from-pink-500/20 to-rose-500/20',
        action: () => {
          recordCommand('ld-tna');
          onLDProfessionalTNAClick();
          onOpenChange(false);
        },
        category: 'Training',
        keywords: ['assessment', 'needs', 'professional']
      },
      {
        id: 'data-center',
        label: 'Data Center',
        icon: <Database className="h-6 w-6" />,
        description: 'Data management',
        color: 'from-slate-500/20 to-gray-500/20',
        action: () => {
          recordCommand('data-center');
          onDataCenterClick();
          onOpenChange(false);
        },
        category: 'Tools',
        keywords: ['database', 'storage', 'management']
      },
      {
        id: 'content-creators',
        label: 'Content',
        icon: <BookOpen className="h-6 w-6" />,
        description: 'Creator hub',
        color: 'from-amber-500/20 to-yellow-500/20',
        action: () => {
          recordCommand('content-creators');
          onContentCreatorsClick();
          onOpenChange(false);
        },
        category: 'Content',
        keywords: ['create', 'publish', 'media']
      },
      {
        id: 'claude-md',
        label: 'CLAUDE.md',
        icon: <FileText className="h-6 w-6" />,
        shortcut: '⌘D',
        description: 'Documentation',
        color: 'from-teal-500/20 to-cyan-500/20',
        action: () => {
          recordCommand('claude-md');
          onClaudeClick();
          onOpenChange(false);
        },
        category: 'Configuration',
        keywords: ['docs', 'documentation', 'readme']
      },
      {
        id: 'mcp',
        label: 'MCP',
        icon: <Network className="h-6 w-6" />,
        shortcut: '⌘M',
        description: 'Servers',
        color: 'from-blue-500/20 to-indigo-500/20',
        action: () => {
          recordCommand('mcp');
          onMCPClick();
          onOpenChange(false);
        },
        category: 'Configuration',
        keywords: ['servers', 'connections', 'network']
      },
      {
        id: 'settings',
        label: 'Settings',
        icon: <Settings className="h-6 w-6" />,
        shortcut: '⌘,',
        description: 'Preferences',
        color: 'from-gray-500/20 to-slate-500/20',
        action: () => {
          recordCommand('settings');
          onSettingsClick();
          onOpenChange(false);
        },
        category: 'Configuration',
        keywords: ['preferences', 'config', 'options']
      }
    ];

    if (onAgentsClick) {
      items.splice(2, 0, {
        id: 'agents',
        label: 'Agents',
        icon: <Bot className="h-6 w-6" />,
        shortcut: '⌘⇧G',
        description: 'AI agents',
        color: 'from-orange-500/20 to-red-500/20',
        action: () => {
          recordCommand('agents');
          onAgentsClick();
          onOpenChange(false);
        },
        category: 'Tools',
        keywords: ['ai', 'bots', 'automation']
      });
    }

    return items;
  }, [
    onClaudeClick,
    onSettingsClick,
    onUsageClick,
    onMCPClick,
    onAgentsClick,
    onAnalyticsClick,
    onWorkflowClick,
    onTrainingBudgetClick,
    onTrainingDashboardClick,
    onGraduateTraineeTrackerClick,
    onDataCenterClick,
    onContentCreatorsClick,
    onLighthouseLMClick,
    onLightbulbClick,
    onLDProfessionalTNAClick,
    onOpenChange,
    recordCommand
  ]);

  const filteredCommands = useMemo(() => {
    if (!search) {
      // Show recent commands when no search
      const recentItems = recentCommands
        .map(id => commands.find(cmd => cmd.id === id))
        .filter(Boolean) as CommandItem[];
      
      if (recentItems.length > 0) {
        const recentWithCategory = recentItems.map(cmd => ({ ...cmd, category: 'Recent' }));
        return [...recentWithCategory, ...commands];
      }
      return commands;
    }
    
    const searchLower = search.toLowerCase();
    const filtered = commands.filter(cmd => {
      const matchLabel = cmd.label.toLowerCase().includes(searchLower);
      const matchCategory = cmd.category.toLowerCase().includes(searchLower);
      const matchDescription = cmd.description?.toLowerCase().includes(searchLower);
      const matchKeywords = cmd.keywords?.some(keyword => 
        keyword.toLowerCase().includes(searchLower)
      );
      
      return matchLabel || matchCategory || matchDescription || matchKeywords;
    });
    
    // Sort by relevance
    return filtered.sort((a, b) => {
      const aLabelMatch = a.label.toLowerCase().startsWith(searchLower);
      const bLabelMatch = b.label.toLowerCase().startsWith(searchLower);
      
      if (aLabelMatch && !bLabelMatch) return -1;
      if (!aLabelMatch && bLabelMatch) return 1;
      
      // Sort by category order
      const aIndex = CATEGORY_ORDER.indexOf(a.category);
      const bIndex = CATEGORY_ORDER.indexOf(b.category);
      return aIndex - bIndex;
    });
  }, [commands, search, recentCommands]);

  const groupedCommands = useMemo(() => {
    const groups: Record<string, CommandItem[]> = {};
    const seen = new Set<string>();
    
    filteredCommands.forEach(cmd => {
      // Skip duplicates in different categories
      if (cmd.category === 'Recent' || !seen.has(cmd.id)) {
        if (!groups[cmd.category]) {
          groups[cmd.category] = [];
        }
        groups[cmd.category].push(cmd);
        seen.add(cmd.id);
      }
    });
    
    // Sort groups by category order
    const sortedGroups: Record<string, CommandItem[]> = {};
    CATEGORY_ORDER.forEach(category => {
      if (groups[category]) {
        sortedGroups[category] = groups[category];
      }
    });
    
    return sortedGroups;
  }, [filteredCommands]);

  // Calculate grid columns based on filtered commands
  const getGridCols = () => {
    if (viewMode === 'list') return 1;
    const count = filteredCommands.length;
    if (count <= 4) return 2;
    if (count <= 9) return 3;
    return 4;
  };

  // Reset state when opening
  useEffect(() => {
    if (open) {
      setSearch('');
      setSelectedIndex(0);
      setTimeout(() => inputRef.current?.focus(), 100);
    }
  }, [open]);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!open) return;

      const cols = getGridCols();
      const total = filteredCommands.length;

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          if (viewMode === 'list') {
            setSelectedIndex((prev) => (prev + 1) % total);
          } else {
            setSelectedIndex((prev) => {
              const nextRow = prev + cols;
              return nextRow < total ? nextRow : prev % cols;
            });
          }
          break;
        case 'ArrowUp':
          e.preventDefault();
          if (viewMode === 'list') {
            setSelectedIndex((prev) => (prev - 1 + total) % total);
          } else {
            setSelectedIndex((prev) => {
              const prevRow = prev - cols;
              return prevRow >= 0 ? prevRow : (Math.floor((total - 1) / cols) * cols) + (prev % cols);
            });
          }
          break;
        case 'ArrowRight':
          e.preventDefault();
          if (viewMode === 'grid') {
            setSelectedIndex((prev) => {
              const nextIndex = prev + 1;
              if (nextIndex < total && Math.floor(nextIndex / cols) === Math.floor(prev / cols)) {
                return nextIndex;
              }
              return Math.floor(prev / cols) * cols;
            });
          }
          break;
        case 'ArrowLeft':
          e.preventDefault();
          if (viewMode === 'grid') {
            setSelectedIndex((prev) => {
              const prevIndex = prev - 1;
              if (prevIndex >= 0 && Math.floor(prevIndex / cols) === Math.floor(prev / cols)) {
                return prevIndex;
              }
              return Math.min(Math.floor(prev / cols) * cols + cols - 1, total - 1);
            });
          }
          break;
        case 'Enter':
          e.preventDefault();
          if (filteredCommands[selectedIndex]) {
            filteredCommands[selectedIndex].action();
          }
          break;
        case 'Escape':
          e.preventDefault();
          onOpenChange(false);
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [open, selectedIndex, filteredCommands, onOpenChange, viewMode]);

  // Reset selected index on search change
  useEffect(() => {
    setSelectedIndex(0);
  }, [search]);

  // Scroll selected item into view
  useEffect(() => {
    if (itemRefs.current[selectedIndex]) {
      itemRefs.current[selectedIndex]?.scrollIntoView({ 
        block: 'nearest',
        behavior: 'smooth'
      });
    }
  }, [selectedIndex]);

  if (!open) return null;

  let commandIndex = -1;

  const renderGridView = () => (
    <div className="p-6">
      {Object.entries(groupedCommands).map(([category, items]) => (
        <div key={category} className="mb-6">
          {/* Category Header */}
          <div className="flex items-center gap-2 mb-3">
            {category === 'Recent' && <Clock className="h-3.5 w-3.5 text-muted-foreground" />}
            <span className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">
              {category}
            </span>
          </div>
          
          {/* Commands Grid */}
          <div className={cn(
            "grid gap-3",
            viewMode === 'grid' 
              ? `grid-cols-${Math.min(items.length, 4)}`
              : "grid-cols-1",
            items.length === 1 && "grid-cols-1",
            items.length === 2 && "grid-cols-2",
            items.length === 3 && "grid-cols-3",
            items.length >= 4 && "grid-cols-4"
          )}>
            {items.map((cmd) => {
              commandIndex++;
              const isSelected = commandIndex === selectedIndex;
              const index = commandIndex;
              
              return (
                <button
                  key={`${category}-${cmd.id}`}
                  ref={el => itemRefs.current[index] = el}
                  id={`command-${index}`}
                  role="option"
                  aria-selected={isSelected}
                  onClick={() => cmd.action()}
                  onMouseEnter={() => setSelectedIndex(index)}
                  className={cn(
                    "relative group flex flex-col items-center justify-center p-4 rounded-xl transition-all duration-200",
                    "border-2",
                    isSelected
                      ? "border-primary bg-accent shadow-lg scale-105"
                      : "border-transparent bg-card hover:bg-accent hover:border-border hover:shadow-md"
                  )}
                >
                  {/* Background Gradient */}
                  {cmd.color && (
                    <div className={cn(
                      "absolute inset-0 rounded-xl bg-gradient-to-br opacity-50",
                      cmd.color
                    )} />
                  )}
                  
                  {/* Content */}
                  <div className="relative z-10 flex flex-col items-center gap-2">
                    {/* Icon Container */}
                    <div className={cn(
                      "flex items-center justify-center w-12 h-12 rounded-lg transition-colors",
                      isSelected 
                        ? "bg-primary/20 text-primary"
                        : "bg-background/80 text-muted-foreground group-hover:text-foreground"
                    )}>
                      {cmd.icon}
                    </div>
                    
                    {/* Label */}
                    <span className={cn(
                      "text-sm font-medium",
                      isSelected
                        ? "text-foreground"
                        : "text-foreground/80"
                    )}>
                      {cmd.label}
                    </span>
                    
                    {/* Description */}
                    {cmd.description && (
                      <span className="text-xs text-muted-foreground text-center">
                        {cmd.description}
                      </span>
                    )}
                    
                    {/* Shortcut Badge */}
                    {cmd.shortcut && (
                      <kbd className={cn(
                        "absolute -top-1 -right-1 px-1.5 py-0.5 rounded text-xs font-medium",
                        "bg-background border border-border",
                        isSelected && "bg-primary/10 border-primary/20 text-primary"
                      )}>
                        {cmd.shortcut}
                      </kbd>
                    )}
                  </div>
                </button>
              );
            })}
          </div>
        </div>
      ))}
    </div>
  );

  const renderListView = () => (
    <div className="py-2">
      {Object.entries(groupedCommands).map(([category, items]) => (
        <div key={category}>
          {/* Category Header */}
          <div className="flex items-center gap-2 px-4 py-2">
            {category === 'Recent' && <Clock className="h-3 w-3 text-muted-foreground" />}
            <span className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">
              {category}
            </span>
          </div>
          
          {/* Commands in Category */}
          {items.map((cmd) => {
            commandIndex++;
            const isSelected = commandIndex === selectedIndex;
            const index = commandIndex;
            
            return (
              <button
                key={`${category}-${cmd.id}`}
                ref={el => itemRefs.current[index] = el}
                id={`command-${index}`}
                role="option"
                aria-selected={isSelected}
                onClick={() => cmd.action()}
                onMouseEnter={() => setSelectedIndex(index)}
                className={cn(
                  "w-full px-4 py-3 flex items-center gap-3 transition-all duration-150",
                  "group relative",
                  isSelected
                    ? "bg-accent"
                    : "hover:bg-accent/50"
                )}
              >
                {/* Selection Indicator */}
                {isSelected && (
                  <div className="absolute left-0 top-1/2 -translate-y-1/2 w-1 h-8 bg-primary rounded-r-full animate-in fade-in-0 slide-in-from-left-1 duration-200" />
                )}
                
                {/* Icon */}
                <div className={cn(
                  "flex items-center justify-center w-10 h-10 rounded-lg transition-colors",
                  isSelected 
                    ? "bg-primary/10 text-primary"
                    : "bg-muted text-muted-foreground group-hover:bg-accent"
                )}>
                  {React.cloneElement(cmd.icon as React.ReactElement, { className: "h-5 w-5" })}
                </div>
                
                {/* Label and Description */}
                <div className="flex-1 text-left">
                  <div className="flex items-center gap-2">
                    <span className={cn(
                      "font-medium",
                      isSelected
                        ? "text-foreground"
                        : "text-foreground/80"
                    )}>
                      {cmd.label}
                    </span>
                  </div>
                  {cmd.description && (
                    <p className="text-xs text-muted-foreground mt-0.5">
                      {cmd.description}
                    </p>
                  )}
                </div>
                
                {/* Shortcut or Arrow */}
                {cmd.shortcut ? (
                  <kbd className={cn(
                    "hidden sm:flex items-center gap-1 px-2 py-1 rounded border text-xs font-medium transition-colors",
                    isSelected
                      ? "bg-primary/10 border-primary/20 text-primary"
                      : "bg-muted border-border text-muted-foreground"
                  )}>
                    {cmd.shortcut}
                  </kbd>
                ) : (
                  <ChevronRight className={cn(
                    "h-4 w-4 transition-all",
                    isSelected 
                      ? "text-primary translate-x-0.5"
                      : "text-muted-foreground"
                  )} />
                )}
              </button>
            );
          })}
        </div>
      ))}
    </div>
  );

  return (
    <div 
      className="fixed inset-0 z-50"
      role="dialog"
      aria-modal="true"
      aria-label="Command Palette"
    >
      {/* Backdrop with blur */}
      <div 
        className="absolute inset-0 bg-black/50 backdrop-blur-sm animate-in fade-in-0 duration-200"
        onClick={() => onOpenChange(false)}
        aria-hidden="true"
      />
      
      {/* Modal */}
      <div className="relative flex items-start justify-center pt-[8vh] px-4">
        <div className={cn(
          "w-full animate-in fade-in-0 zoom-in-95 duration-200",
          viewMode === 'grid' ? "max-w-4xl" : "max-w-2xl"
        )}>
          <div className="relative bg-background rounded-xl shadow-2xl overflow-hidden border border-border">
            {/* Search Header */}
            <div className="flex items-center px-4 border-b border-border">
              <Search className="h-5 w-5 text-muted-foreground" />
              <input
                ref={inputRef}
                type="text"
                placeholder="Search commands..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="flex-1 px-3 py-4 bg-transparent outline-none text-foreground placeholder:text-muted-foreground"
                aria-label="Search commands"
                aria-expanded="true"
                aria-controls="command-list"
                aria-activedescendant={`command-${selectedIndex}`}
              />
              <div className="flex items-center gap-2">
                <button
                  onClick={toggleViewMode}
                  className="p-2 hover:bg-accent rounded-lg transition-colors"
                  aria-label="Toggle view mode"
                  title={`Switch to ${viewMode === 'grid' ? 'list' : 'grid'} view`}
                >
                  {viewMode === 'grid' ? 
                    <List className="h-4 w-4 text-muted-foreground" /> : 
                    <LayoutGrid className="h-4 w-4 text-muted-foreground" />
                  }
                </button>
                <button
                  onClick={() => onOpenChange(false)}
                  className="p-2 hover:bg-accent rounded-lg transition-colors"
                  aria-label="Close command palette"
                >
                  <X className="h-4 w-4 text-muted-foreground" />
                </button>
              </div>
            </div>
            
            {/* Command List/Grid */}
            <div 
              ref={listRef}
              id="command-list"
              role="listbox"
              className="max-h-[70vh] overflow-y-auto overscroll-contain"
            >
              {filteredCommands.length === 0 ? (
                <div className="px-4 py-12 text-center">
                  <p className="text-sm text-muted-foreground">
                    No results found for "{search}"
                  </p>
                  <p className="text-xs text-muted-foreground/60 mt-2">
                    Try different keywords or browse categories
                  </p>
                </div>
              ) : viewMode === 'grid' ? (
                renderGridView()
              ) : (
                renderListView()
              )}
            </div>
            
            {/* Footer */}
            <div className="px-4 py-3 border-t border-border bg-muted/30">
              <div className="flex items-center justify-between text-xs">
                <div className="flex items-center gap-4 text-muted-foreground">
                  <span className="flex items-center gap-1.5">
                    <kbd className="px-1.5 py-0.5 bg-background rounded border border-border font-medium">
                      {viewMode === 'grid' ? '↑↓←→' : '↑↓'}
                    </kbd>
                    Navigate
                  </span>
                  <span className="flex items-center gap-1.5">
                    <kbd className="px-1.5 py-0.5 bg-background rounded border border-border font-medium">↵</kbd>
                    Select
                  </span>
                  <span className="flex items-center gap-1.5">
                    <kbd className="px-1.5 py-0.5 bg-background rounded border border-border font-medium">esc</kbd>
                    Close
                  </span>
                </div>
                <span className="flex items-center gap-1.5 text-muted-foreground">
                  <Command className="h-3 w-3" />
                  <kbd className="font-medium">K</kbd>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};