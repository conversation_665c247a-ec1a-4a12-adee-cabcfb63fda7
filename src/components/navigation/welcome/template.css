/* Standard Page Template Styles */

.page-template {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', sans-serif;
}

.page-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding: 1rem 2rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.page-header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
}

.page-subtitle {
  font-size: 0.875rem;
  color: #718096;
  margin: 0;
  margin-top: 0.25rem;
}

.page-header-actions {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.page-main {
  flex: 1;
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

.page-content {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-height: 400px;
}

.page-footer {
  background: rgba(255, 255, 255, 0.95);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  padding: 1rem 2rem;
  text-align: center;
  color: #718096;
  font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .page-header {
    padding: 1rem;
  }
  
  .page-header-content {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
  
  .page-main {
    padding: 1rem;
  }
  
  .page-content {
    padding: 1.5rem;
  }
  
  .page-title {
    font-size: 1.25rem;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .page-template {
    background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
  }
  
  .page-header {
    background: rgba(26, 32, 44, 0.95);
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }
  
  .page-title {
    color: #f7fafc;
  }
  
  .page-subtitle {
    color: #a0aec0;
  }
  
  .page-content {
    background: rgba(26, 32, 44, 0.9);
    border-color: rgba(255, 255, 255, 0.1);
    color: #f7fafc;
  }
  
  .page-footer {
    background: rgba(26, 32, 44, 0.95);
    border-top-color: rgba(255, 255, 255, 0.1);
    color: #a0aec0;
  }
}

/* Animation utilities */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(-20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Button styles for consistency */
.template-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.template-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.template-button:active {
  transform: translateY(0);
}

.template-button-secondary {
  background: rgba(255, 255, 255, 0.9);
  color: #4a5568;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.template-button-secondary:hover {
  background: rgba(255, 255, 255, 1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}