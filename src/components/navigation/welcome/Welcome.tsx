import React, { useState, useCallback, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Plus, 
  Settings, 
  Grid, 
  Lightbulb, 
  Package, 
  Bot, 
  BarChart3, 
  Folder, 
  Zap,
  CheckCircle,
  ArrowRight,
  Play,
  BookOpen,
  Activity,
  Users,
  Sparkles
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { PageTemplate } from './PageTemplate';
import { useSystemHealth } from '@/hooks/useWelcomeData';

export type WelcomeTabVariant = 'default' | 'minimal' | 'professional' | 'creative' | 'dark';

interface WelcomeProps {
  onNewSession: () => void;
  variant?: WelcomeTabVariant;
  className?: string;
  showOnboarding?: boolean;
  userProgress?: {
    completedTutorials: string[];
    totalProgress: number;
  };
}

interface QuickAction {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  action: () => void;
  variant?: 'primary' | 'secondary';
  badge?: string;
}

interface SystemStatus {
  status: 'operational' | 'degraded' | 'offline';
  message: string;
  details?: string;
  lastUpdated: Date;
}

export const Welcome: React.FC<WelcomeProps> = ({ 
  onNewSession, 
  variant = 'default',
  className,
  showOnboarding = false,
  userProgress = { completedTutorials: [], totalProgress: 0 }
}) => {
  const [activeSection, setActiveSection] = useState<string>('overview');
  const [systemStatus, setSystemStatus] = useState<SystemStatus>({
    status: 'operational',
    message: 'All systems operational',
    lastUpdated: new Date()
  });
  const [isLoading, setIsLoading] = useState(false);
  const fetchSystemHealth = useSystemHealth();

  const handleSectionChange = useCallback((section: string) => {
    setActiveSection(section);
  }, []);

  // Load system health on mount
  useEffect(() => {
    const loadSystemHealth = async () => {
      setIsLoading(true);
      try {
        const health = await fetchSystemHealth();
        setSystemStatus({
          status: health.healthScore > 80 ? 'operational' : health.healthScore > 50 ? 'degraded' : 'offline',
          message: health.healthScore > 80 ? 'All systems operational' : 'Some services may be slow',
          details: `Health Score: ${health.healthScore}% | Response Time: ${health.responseTime}ms`,
          lastUpdated: new Date()
        });
      } catch (error) {
        setSystemStatus({
          status: 'offline',
          message: 'Unable to check system status',
          lastUpdated: new Date()
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadSystemHealth();
  }, [fetchSystemHealth]);

  const quickActions: QuickAction[] = [
    {
      id: 'new-session',
      title: 'Start New Session',
      description: 'Begin a new AI-powered development session',
      icon: <Plus className="w-5 h-5" />,
      action: onNewSession,
      variant: 'primary'
    },
    {
      id: 'browse-projects',
      title: 'Browse Projects',
      description: 'View and manage your existing projects',
      icon: <Folder className="w-5 h-5" />,
      action: () => handleSectionChange('projects'),
      variant: 'secondary'
    },
    {
      id: 'ai-agents',
      title: 'AI Agents',
      description: 'Explore available AI development agents',
      icon: <Bot className="w-5 h-5" />,
      action: () => handleSectionChange('agents'),
      variant: 'secondary',
      badge: 'New'
    },
    {
      id: 'analytics',
      title: 'Analytics',
      description: 'View your development insights and metrics',
      icon: <BarChart3 className="w-5 h-5" />,
      action: () => handleSectionChange('analytics'),
      variant: 'secondary'
    }
  ];

  const tutorials = [
    {
      id: 'getting-started',
      title: 'Getting Started Guide',
      description: 'Learn the basics of Sanity development environment',
      duration: '5 min',
      completed: userProgress.completedTutorials.includes('getting-started')
    },
    {
      id: 'ai-features',
      title: 'AI-Powered Features',
      description: 'Discover how to leverage AI in your workflow',
      duration: '8 min',
      completed: userProgress.completedTutorials.includes('ai-features')
    },
    {
      id: 'advanced-tips',
      title: 'Advanced Tips & Tricks',
      description: 'Master advanced development techniques',
      duration: '12 min',
      completed: userProgress.completedTutorials.includes('advanced-tips')
    }
  ];

  const headerActions = (
    <div className="flex items-center gap-3">
      <Button 
        onClick={onNewSession}
        size="default"
        className="shadow-sm"
        aria-label="Start a new development session"
      >
        <Plus className="w-4 h-4 mr-2" />
        New Session
      </Button>
      <Button 
        variant="outline"
        size="default"
        onClick={() => handleSectionChange('settings')}
        aria-label="Open settings"
      >
        <Settings className="w-4 h-4 mr-2" />
        Settings
      </Button>
    </div>
  );

  const renderOverview = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="space-y-8"
    >
      {/* Welcome Hero Section */}
      <div className="text-center py-8">
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ delay: 0.2 }}
          className="mx-auto w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mb-6 shadow-lg"
        >
          <Sparkles className="w-10 h-10 text-white" />
        </motion.div>
        <h2 className="text-3xl font-bold text-foreground mb-3">
          Welcome to Sanity
        </h2>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          Your AI-powered development environment is ready. Start building amazing projects with intelligent assistance.
        </p>
      </div>

      {/* Progress Section */}
      {userProgress.totalProgress > 0 && (
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card className="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-950/20 dark:to-blue-950/20 border-green-200 dark:border-green-800">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-green-800 dark:text-green-200 flex items-center gap-2">
                  <Activity className="w-5 h-5" />
                  Your Progress
                </CardTitle>
                <Badge variant="secondary" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                  {userProgress.totalProgress}% Complete
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="w-full bg-green-200 dark:bg-green-800 rounded-full h-3 mb-2">
                <motion.div 
                  className="bg-gradient-to-r from-green-500 to-blue-500 h-3 rounded-full"
                  initial={{ width: 0 }}
                  animate={{ width: `${userProgress.totalProgress}%` }}
                  transition={{ duration: 1, delay: 0.5 }}
                />
              </div>
              <p className="text-sm text-green-700 dark:text-green-300">
                Keep going! You're making great progress on your development journey.
              </p>
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* Quick Actions Grid */}
      <div>
        <h3 className="text-xl font-semibold mb-6 flex items-center gap-2">
          <Grid className="w-5 h-5 text-primary" />
          Quick Actions
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {quickActions.map((action, index) => (
            <motion.div
              key={action.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 * index }}
            >
              <Card className="group hover:shadow-lg transition-all duration-300 cursor-pointer border-2 hover:border-primary/20"
                    onClick={action.action}
                    role="button"
                    tabIndex={0}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        action.action();
                      }
                    }}
                    aria-label={action.description}
              >
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-3">
                      <div className={`p-3 rounded-lg ${
                        action.variant === 'primary' 
                          ? 'bg-primary/10 text-primary' 
                          : 'bg-muted text-muted-foreground'
                      } group-hover:scale-110 transition-transform`}>
                        {action.icon}
                      </div>
                      <div>
                        <CardTitle className="text-base group-hover:text-primary transition-colors">
                          {action.title}
                        </CardTitle>
                        <CardDescription className="mt-1">
                          {action.description}
                        </CardDescription>
                      </div>
                    </div>
                    {action.badge && (
                      <Badge variant="secondary" className="text-xs">
                        {action.badge}
                      </Badge>
                    )}
                  </div>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="flex items-center text-sm text-muted-foreground group-hover:text-primary transition-colors">
                    <span>Click to continue</span>
                    <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Learning Resources */}
      <div>
        <h3 className="text-xl font-semibold mb-6 flex items-center gap-2">
          <BookOpen className="w-5 h-5 text-primary" />
          Learning Resources
        </h3>
        <div className="space-y-3">
          {tutorials.map((tutorial, index) => (
            <motion.div
              key={tutorial.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.1 * index }}
            >
              <Card className="hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                        {tutorial.completed ? (
                          <CheckCircle className="w-5 h-5 text-green-600" />
                        ) : (
                          <Play className="w-5 h-5 text-blue-600" />
                        )}
                      </div>
                      <div>
                        <h4 className="font-medium text-foreground">
                          {tutorial.title}
                        </h4>
                        <p className="text-sm text-muted-foreground">
                          {tutorial.description}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <span className="text-xs text-muted-foreground">
                        {tutorial.duration}
                      </span>
                      {tutorial.completed ? (
                        <Badge variant="secondary" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                          Completed
                        </Badge>
                      ) : (
                        <Button variant="outline" size="sm">
                          Start
                        </Button>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>

      {/* System Status */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
      >
        <Card className={`${
          systemStatus.status === 'operational' 
            ? 'bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20 border-green-200 dark:border-green-800'
            : systemStatus.status === 'degraded'
            ? 'bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-950/20 dark:to-orange-950/20 border-yellow-200 dark:border-yellow-800'
            : 'bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-950/20 dark:to-pink-950/20 border-red-200 dark:border-red-800'
        }`}>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className={`w-3 h-3 rounded-full ${
                  systemStatus.status === 'operational' 
                    ? 'bg-green-500 animate-pulse' 
                    : systemStatus.status === 'degraded'
                    ? 'bg-yellow-500'
                    : 'bg-red-500'
                }`} />
                <div>
                  <h3 className="font-semibold text-foreground flex items-center gap-2">
                    <Activity className="w-4 h-4" />
                    System Status
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    {systemStatus.message}
                  </p>
                  {systemStatus.details && (
                    <p className="text-xs text-muted-foreground mt-1">
                      {systemStatus.details}
                    </p>
                  )}
                </div>
              </div>
              <div className="text-right">
                <p className="text-sm font-medium text-foreground">
                  {systemStatus.status === 'operational' ? 'Ready to code' : 'Limited functionality'}
                </p>
                <p className="text-xs text-muted-foreground">
                  Updated {systemStatus.lastUpdated.toLocaleTimeString()}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </motion.div>
  );

  const renderSettings = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="space-y-6"
    >
      <Card>
        <CardHeader>
          <CardTitle>Preferences</CardTitle>
          <CardDescription>
            Customize your development environment settings
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <label htmlFor="theme-select" className="block text-sm font-medium text-foreground mb-2">
              Theme
            </label>
            <select 
              id="theme-select"
              className="w-full p-3 border border-input rounded-md bg-background text-foreground focus:ring-2 focus:ring-ring focus:border-transparent"
              defaultValue="default"
              aria-describedby="theme-help"
            >
              <option value="default">Default</option>
              <option value="dark">Dark</option>
              <option value="minimal">Minimal</option>
              <option value="professional">Professional</option>
            </select>
            <p id="theme-help" className="text-xs text-muted-foreground mt-1">
              Choose your preferred visual theme
            </p>
          </div>
          
          <div className="flex items-center space-x-3">
            <input 
              type="checkbox" 
              id="auto-save"
              className="w-4 h-4 text-primary border-border rounded focus:ring-ring"
              defaultChecked 
            />
            <label htmlFor="auto-save" className="text-sm font-medium text-foreground">
              Enable automatic saving
            </label>
          </div>

          <div className="flex items-center space-x-3">
            <input 
              type="checkbox" 
              id="ai-suggestions"
              className="w-4 h-4 text-primary border-border rounded focus:ring-ring"
              defaultChecked 
            />
            <label htmlFor="ai-suggestions" className="text-sm font-medium text-foreground">
              Enable AI code suggestions
            </label>
          </div>
        </CardContent>
      </Card>
      
      <div className="flex justify-start">
        <Button 
          onClick={() => handleSectionChange('overview')}
          variant="outline"
        >
          Back to Overview
        </Button>
      </div>
    </motion.div>
  );

  const renderComingSoon = () => (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      className="text-center py-16"
    >
      <div className="mx-auto w-16 h-16 bg-muted rounded-full flex items-center justify-center mb-6">
        <Zap className="w-8 h-8 text-muted-foreground" />
      </div>
      <h3 className="text-2xl font-semibold text-foreground mb-3">Coming Soon</h3>
      <p className="text-muted-foreground mb-6 max-w-md mx-auto">
        This section is under development. We're working hard to bring you new features.
      </p>
      <Button 
        onClick={() => handleSectionChange('overview')}
        variant="outline"
      >
        Back to Overview
      </Button>
    </motion.div>
  );

  const renderContent = () => {
    switch (activeSection) {
      case 'overview':
        return renderOverview();
      case 'settings':
        return renderSettings();
      default:
        return renderComingSoon();
    }
  };

  return (
    <div className={className}>
      <PageTemplate
        title="Welcome to Sanity"
        subtitle="Your AI-powered development environment"
        headerActions={headerActions}
      >
        <AnimatePresence mode="wait">
          {renderContent()}
        </AnimatePresence>
      </PageTemplate>
    </div>
  );
};

export default Welcome;