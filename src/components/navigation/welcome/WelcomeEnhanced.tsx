import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Welcome } from './Welcome';
import { OnboardingWelcome } from '../../data-center/onboarding/OnboardingWelcome';
import { TutorialTooltip } from '../../tutorial/TutorialTooltip';
import { StatusIndicator } from '../../feedback/StatusIndicator';
import { LoadingSpinner } from '../../feedback/LoadingSpinner';
import { ProgressRing } from '../../progress/ProgressRing';
import { SearchInput } from '../../search/SearchInput';
import { Breadcrumb } from '../Breadcrumb';

interface WelcomeEnhancedProps {
  onNewSession: () => void;
  variant?: 'default' | 'minimal' | 'professional' | 'creative' | 'dark';
}

export const WelcomeEnhanced: React.FC<WelcomeEnhancedProps> = ({
  onNewSession,
  variant = 'default'
}) => {
  const [showOnboarding, setShowOnboarding] = useState(false);
  const [showTutorial, setShowTutorial] = useState(false);
  const [currentTutorialStep, setCurrentTutorialStep] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  
  const [userProgress, setUserProgress] = useState({
    completedTutorials: ['getting-started'],
    totalProgress: 65
  });

  // Simulate loading
  useEffect(() => {
    const timer = setTimeout(() => setIsLoading(false), 2000);
    return () => clearTimeout(timer);
  }, []);

  const tutorialSteps = [
    {
      id: 'welcome-header',
      title: 'Welcome Header',
      content: 'This is your main navigation area where you can access key actions.',
      target: '[data-tutorial="header"]',
      position: 'bottom' as const
    },
    {
      id: 'quick-actions',
      title: 'Quick Actions',
      content: 'These cards provide quick access to common tasks and features.',
      target: '[data-tutorial="quick-actions"]',
      position: 'top' as const
    },
    {
      id: 'progress-section',
      title: 'Your Progress',
      content: 'Track your learning journey and completed tutorials here.',
      target: '[data-tutorial="progress"]',
      position: 'left' as const
    }
  ];

  const breadcrumbItems = [
    { label: 'Dashboard', onClick: () => console.log('Navigate to dashboard') },
    { label: 'Welcome', isActive: true }
  ];

  const handleStartTutorial = () => {
    setShowOnboarding(false);
    setShowTutorial(true);
    setCurrentTutorialStep(0);
  };

  const handleTutorialNext = () => {
    if (currentTutorialStep < tutorialSteps.length - 1) {
      setCurrentTutorialStep(prev => prev + 1);
    }
  };

  const handleTutorialPrevious = () => {
    if (currentTutorialStep > 0) {
      setCurrentTutorialStep(prev => prev - 1);
    }
  };

  const handleTutorialComplete = () => {
    setShowTutorial(false);
    setUserProgress(prev => ({
      ...prev,
      completedTutorials: [...prev.completedTutorials, 'welcome-tour'],
      totalProgress: Math.min(100, prev.totalProgress + 15)
    }));
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <LoadingSpinner size="lg" variant="spinner" />
          <p className="text-muted-foreground">Loading your workspace...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Enhanced Header with Breadcrumb and Search */}
      <div className="border-b bg-background/95 backdrop-blur" data-tutorial="header">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between mb-4">
            <Breadcrumb items={breadcrumbItems} />
            <div className="flex items-center gap-4">
              <StatusIndicator
                status="success"
                label="System Online"
                variant="badge"
              />
              <ProgressRing
                progress={userProgress.totalProgress}
                size={40}
                strokeWidth={4}
                showText={false}
              />
            </div>
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold">Welcome to Sanity</h1>
              <p className="text-muted-foreground">Your AI-powered development environment</p>
            </div>
            
            <div className="flex items-center gap-3">
              <SearchInput
                placeholder="Search features..."
                value={searchQuery}
                onChange={setSearchQuery}
                size="sm"
                className="w-64"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Main Welcome Component with Tutorial Markers */}
      <div className="relative">
        <Welcome
          onNewSession={onNewSession}
          variant={variant}
          userProgress={userProgress}
          className="tutorial-enhanced"
        />
        
        {/* Tutorial Tooltip */}
        <TutorialTooltip
          steps={tutorialSteps}
          currentStep={currentTutorialStep}
          isVisible={showTutorial}
          onNext={handleTutorialNext}
          onPrevious={handleTutorialPrevious}
          onClose={() => setShowTutorial(false)}
          onComplete={handleTutorialComplete}
        />
      </div>

      {/* Enhanced Onboarding */}
      <OnboardingWelcome
        isOpen={showOnboarding}
        onStartTutorial={handleStartTutorial}
        onSkip={() => setShowOnboarding(false)}
        onClose={() => setShowOnboarding(false)}
        progress={userProgress}
        variant="modal"
      />

      {/* Floating Action Button for Tutorial */}
      {!showTutorial && !showOnboarding && (
        <motion.button
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          onClick={() => setShowTutorial(true)}
          className="fixed bottom-6 right-6 w-14 h-14 bg-primary text-primary-foreground rounded-full shadow-lg hover:shadow-xl transition-shadow z-30 flex items-center justify-center"
          aria-label="Start tutorial"
        >
          <span className="text-xl">?</span>
        </motion.button>
      )}
    </div>
  );
};