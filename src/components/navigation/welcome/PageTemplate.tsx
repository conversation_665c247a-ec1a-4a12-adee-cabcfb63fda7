import React from 'react';
import './template.css';

interface PageTemplateProps {
  title: string;
  subtitle?: string;
  children: React.ReactNode;
  headerActions?: React.ReactNode;
  className?: string;
  contentClassName?: string;
}

export const PageTemplate: React.FC<PageTemplateProps> = ({
  title,
  subtitle,
  children,
  headerActions,
  className = '',
  contentClassName = ''
}) => {
  return (
    <div className={`page-template ${className}`}>
      {/* Header */}
      <header className="page-header">
        <div className="page-header-content">
          <div>
            <h1 className="page-title">{title}</h1>
            {subtitle && <p className="page-subtitle">{subtitle}</p>}
          </div>
          {headerActions && (
            <div className="page-header-actions">
              {headerActions}
            </div>
          )}
        </div>
      </header>

      {/* Main Content */}
      <main className="page-main">
        <div className={`page-content fade-in ${contentClassName}`}>
          {children}
        </div>
      </main>

      {/* Footer */}
      <footer className="page-footer">
        <p>© 2024 Sanity - AI-Powered Development Environment</p>
      </footer>
    </div>
  );
};

export default PageTemplate;