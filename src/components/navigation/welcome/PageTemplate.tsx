import React from 'react';

interface PageTemplateProps {
  title: string;
  subtitle?: string;
  children: React.ReactNode;
  headerActions?: React.ReactNode;
  className?: string;
  contentClassName?: string;
}

export const PageTemplate: React.FC<PageTemplateProps> = ({
  title,
  subtitle,
  children,
  headerActions,
  className = '',
  contentClassName = ''
}) => {
  return (
    <div className={`min-h-screen bg-background text-foreground ${className}`}>
      {/* Header */}
      <header className="sticky top-0 z-40 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex h-16 items-center justify-between">
            <div className="flex-1 min-w-0">
              <h1 className="text-xl sm:text-2xl font-bold text-foreground truncate">
                {title}
              </h1>
              {subtitle && (
                <p className="text-sm text-muted-foreground mt-1 truncate">
                  {subtitle}
                </p>
              )}
            </div>
            {headerActions && (
              <div className="flex items-center gap-2 ml-4">
                {headerActions}
              </div>
            )}
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main 
        className="flex-1 container mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8"
        role="main"
        aria-label="Main content"
      >
        <div className={`${contentClassName}`}>
          {children}
        </div>
      </main>

      {/* Footer */}
      <footer className="border-t bg-muted/30" role="contentinfo">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <p className="text-center text-sm text-muted-foreground">
            © 2024 Sanity - AI-Powered Development Environment
          </p>
        </div>
      </footer>
    </div>
  );
};

export default PageTemplate;