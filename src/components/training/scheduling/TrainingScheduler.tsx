import React, { useState, useEffect } from 'react';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Calendar } from '@/components/ui/calendar';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  Calendar as CalendarIcon,
  Clock,
  MapPin,
  Users,
  Video,
  Plus,
  Edit,
  Trash2,
  Send,
  ChevronLeft,
  ChevronRight,
  AlertCircle,
  CheckCircle2,
  User
} from 'lucide-react';
import { format, addDays, startOfWeek, startOfMonth, endOfMonth, eachDayOfInterval, isSameDay, isSameMonth } from 'date-fns';
import { TrainingSession, TrainingCalendar } from '../types';
import { useTraining } from '@/contexts/TrainingContext';

export const TrainingScheduler: React.FC = () => {
  const { state, dispatch } = useTraining();
  const [sessions, setSessions] = useState<TrainingSession[]>([]);
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [viewMode, setViewMode] = useState<'month' | 'week' | 'day' | 'list'>('month');
  const [showCreateSession, setShowCreateSession] = useState(false);
  const [selectedSession, setSelectedSession] = useState<TrainingSession | null>(null);
  const [newSession, setNewSession] = useState<Partial<TrainingSession>>({
    mode: 'in_person',
    status: 'scheduled'
  });

  // Mock data for demonstration
  useEffect(() => {
    const mockSessions: TrainingSession[] = [
      {
        id: '1',
        programId: 'prog-1',
        title: 'Safety Procedures Training',
        instructor: 'John Smith',
        startDate: new Date('2024-01-25T09:00:00'),
        endDate: new Date('2024-01-25T12:00:00'),
        location: 'Training Room A',
        maxParticipants: 20,
        enrolledParticipants: ['emp1', 'emp2', 'emp3', 'emp4', 'emp5'],
        waitlist: [],
        status: 'scheduled',
        mode: 'in_person',
        resources: ['safety-manual.pdf', 'equipment-guide.pdf']
      },
      {
        id: '2',
        programId: 'prog-2',
        title: 'Leadership Workshop',
        instructor: 'Sarah Johnson',
        startDate: new Date('2024-01-26T14:00:00'),
        endDate: new Date('2024-01-26T17:00:00'),
        location: 'Virtual',
        maxParticipants: 30,
        enrolledParticipants: ['emp6', 'emp7', 'emp8'],
        waitlist: ['emp9'],
        status: 'scheduled',
        mode: 'virtual',
        meetingUrl: 'https://zoom.us/j/123456789'
      },
      {
        id: '3',
        programId: 'prog-3',
        title: 'Technical Skills Development',
        instructor: 'Mike Wilson',
        startDate: new Date('2024-01-28T10:00:00'),
        endDate: new Date('2024-01-28T16:00:00'),
        location: 'Lab 2',
        maxParticipants: 15,
        enrolledParticipants: Array(15).fill(null).map((_, i) => `emp${i + 10}`),
        waitlist: ['emp26', 'emp27'],
        status: 'scheduled',
        mode: 'in_person'
      },
      {
        id: '4',
        programId: 'prog-4',
        title: 'Compliance Training',
        instructor: 'Lisa Brown',
        startDate: addDays(new Date(), 2),
        endDate: addDays(new Date(), 2),
        location: 'Conference Room B',
        maxParticipants: 25,
        enrolledParticipants: ['emp30', 'emp31', 'emp32'],
        waitlist: [],
        status: 'scheduled',
        mode: 'hybrid',
        meetingUrl: 'https://teams.microsoft.com/meet/123'
      }
    ];

    setSessions(mockSessions);
  }, []);

  const handleCreateSession = () => {
    const session: TrainingSession = {
      id: Date.now().toString(),
      programId: newSession.programId || '',
      title: newSession.title || '',
      instructor: newSession.instructor || '',
      startDate: newSession.startDate || new Date(),
      endDate: newSession.endDate || new Date(),
      location: newSession.location || '',
      maxParticipants: newSession.maxParticipants || 20,
      enrolledParticipants: [],
      waitlist: [],
      status: 'scheduled',
      mode: newSession.mode as any || 'in_person',
      meetingUrl: newSession.meetingUrl,
      resources: newSession.resources || []
    };

    setSessions([...sessions, session]);
    setShowCreateSession(false);
    setNewSession({ mode: 'in_person', status: 'scheduled' });
  };

  const handleDeleteSession = (sessionId: string) => {
    setSessions(sessions.filter(s => s.id !== sessionId));
  };

  const handleCancelSession = (sessionId: string) => {
    setSessions(sessions.map(s => 
      s.id === sessionId ? { ...s, status: 'cancelled' } : s
    ));
  };

  const getSessionsForDate = (date: Date) => {
    return sessions.filter(session => 
      isSameDay(session.startDate, date)
    );
  };

  const renderCalendarView = () => {
    const monthStart = startOfMonth(selectedDate);
    const monthEnd = endOfMonth(selectedDate);
    const days = eachDayOfInterval({ start: monthStart, end: monthEnd });

    return (
      <div className="grid grid-cols-7 gap-1">
        {/* Day headers */}
        {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
          <div key={day} className="text-center font-semibold p-2 text-sm">
            {day}
          </div>
        ))}
        
        {/* Calendar days */}
        {days.map(day => {
          const daySessions = getSessionsForDate(day);
          const isToday = isSameDay(day, new Date());
          const isSelected = isSameDay(day, selectedDate);
          
          return (
            <div
              key={day.toISOString()}
              onClick={() => setSelectedDate(day)}
              className={`
                min-h-[100px] p-2 border rounded-lg cursor-pointer transition-colors
                ${isToday ? 'bg-primary/5 border-primary' : 'border-border'}
                ${isSelected ? 'ring-2 ring-primary' : ''}
                ${!isSameMonth(day, selectedDate) ? 'opacity-50' : ''}
                hover:bg-muted/50
              `}
            >
              <div className="font-medium text-sm mb-1">
                {format(day, 'd')}
              </div>
              <div className="space-y-1">
                {daySessions.slice(0, 2).map((session, idx) => (
                  <div
                    key={session.id}
                    className="text-xs p-1 rounded bg-primary/10 truncate"
                    title={session.title}
                  >
                    {format(session.startDate, 'HH:mm')} - {session.title}
                  </div>
                ))}
                {daySessions.length > 2 && (
                  <div className="text-xs text-muted-foreground">
                    +{daySessions.length - 2} more
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  const renderListView = () => {
    const upcomingSessions = sessions
      .filter(s => s.startDate >= new Date())
      .sort((a, b) => a.startDate.getTime() - b.startDate.getTime());

    return (
      <div className="space-y-4">
        {upcomingSessions.map(session => (
          <Card key={session.id}>
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-lg">{session.title}</CardTitle>
                  <CardDescription>
                    {format(session.startDate, 'PPP')} • {format(session.startDate, 'p')} - {format(session.endDate, 'p')}
                  </CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant={
                    session.status === 'scheduled' ? 'default' :
                    session.status === 'in_progress' ? 'secondary' :
                    session.status === 'completed' ? 'success' : 'destructive'
                  }>
                    {session.status}
                  </Badge>
                  <Badge variant="outline">
                    {session.mode === 'virtual' && <Video className="h-3 w-3 mr-1" />}
                    {session.mode === 'in_person' && <MapPin className="h-3 w-3 mr-1" />}
                    {session.mode}
                  </Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="text-muted-foreground flex items-center gap-1">
                    <User className="h-3 w-3" />
                    Instructor
                  </span>
                  <p className="font-medium">{session.instructor}</p>
                </div>
                <div>
                  <span className="text-muted-foreground flex items-center gap-1">
                    <MapPin className="h-3 w-3" />
                    Location
                  </span>
                  <p className="font-medium">{session.location}</p>
                </div>
                <div>
                  <span className="text-muted-foreground flex items-center gap-1">
                    <Users className="h-3 w-3" />
                    Participants
                  </span>
                  <p className="font-medium">
                    {session.enrolledParticipants.length}/{session.maxParticipants}
                  </p>
                </div>
                <div>
                  <span className="text-muted-foreground flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    Duration
                  </span>
                  <p className="font-medium">
                    {Math.round((session.endDate.getTime() - session.startDate.getTime()) / (1000 * 60))} min
                  </p>
                </div>
              </div>

              {session.enrolledParticipants.length >= session.maxParticipants && (
                <div className="flex items-center gap-2 text-sm text-amber-600">
                  <AlertCircle className="h-4 w-4" />
                  Session full • {session.waitlist.length} on waitlist
                </div>
              )}

              <div className="flex gap-2">
                <Button size="sm" variant="outline">
                  <Edit className="h-3 w-3 mr-1" />
                  Edit
                </Button>
                <Button size="sm" variant="outline">
                  <Users className="h-3 w-3 mr-1" />
                  Manage Participants
                </Button>
                {session.mode !== 'in_person' && session.meetingUrl && (
                  <Button size="sm" variant="outline">
                    <Video className="h-3 w-3 mr-1" />
                    Join Meeting
                  </Button>
                )}
                <Button 
                  size="sm" 
                  variant="ghost"
                  onClick={() => handleCancelSession(session.id)}
                >
                  Cancel Session
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Training Schedule</h2>
          <p className="text-muted-foreground">
            Manage training sessions and calendars
          </p>
        </div>
        <Dialog open={showCreateSession} onOpenChange={setShowCreateSession}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Schedule Session
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Schedule New Training Session</DialogTitle>
              <DialogDescription>
                Create a new training session and invite participants
              </DialogDescription>
            </DialogHeader>
            
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Session Title</Label>
                  <Input 
                    placeholder="Enter session title"
                    value={newSession.title || ''}
                    onChange={e => setNewSession({...newSession, title: e.target.value})}
                  />
                </div>
                <div className="space-y-2">
                  <Label>Instructor</Label>
                  <Input 
                    placeholder="Instructor name"
                    value={newSession.instructor || ''}
                    onChange={e => setNewSession({...newSession, instructor: e.target.value})}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Start Date & Time</Label>
                  <Input 
                    type="datetime-local"
                    onChange={e => setNewSession({...newSession, startDate: new Date(e.target.value)})}
                  />
                </div>
                <div className="space-y-2">
                  <Label>End Date & Time</Label>
                  <Input 
                    type="datetime-local"
                    onChange={e => setNewSession({...newSession, endDate: new Date(e.target.value)})}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Delivery Mode</Label>
                  <Select 
                    value={newSession.mode}
                    onValueChange={value => setNewSession({...newSession, mode: value as any})}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="in_person">In Person</SelectItem>
                      <SelectItem value="virtual">Virtual</SelectItem>
                      <SelectItem value="hybrid">Hybrid</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label>Max Participants</Label>
                  <Input 
                    type="number"
                    placeholder="20"
                    value={newSession.maxParticipants || ''}
                    onChange={e => setNewSession({...newSession, maxParticipants: parseInt(e.target.value)})}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label>Location</Label>
                <Input 
                  placeholder="Training room, virtual link, or address"
                  value={newSession.location || ''}
                  onChange={e => setNewSession({...newSession, location: e.target.value})}
                />
              </div>

              {(newSession.mode === 'virtual' || newSession.mode === 'hybrid') && (
                <div className="space-y-2">
                  <Label>Meeting URL</Label>
                  <Input 
                    placeholder="https://zoom.us/j/..."
                    value={newSession.meetingUrl || ''}
                    onChange={e => setNewSession({...newSession, meetingUrl: e.target.value})}
                  />
                </div>
              )}

              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setShowCreateSession(false)}>
                  Cancel
                </Button>
                <Button onClick={handleCreateSession}>
                  <Send className="h-4 w-4 mr-2" />
                  Schedule Session
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="icon"
            onClick={() => setSelectedDate(addDays(selectedDate, -1))}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <h3 className="text-lg font-medium">
            {format(selectedDate, 'MMMM yyyy')}
          </h3>
          <Button
            variant="outline"
            size="icon"
            onClick={() => setSelectedDate(addDays(selectedDate, 1))}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setSelectedDate(new Date())}
          >
            Today
          </Button>
        </div>

        <Tabs value={viewMode} onValueChange={(v: any) => setViewMode(v)}>
          <TabsList>
            <TabsTrigger value="month">Month</TabsTrigger>
            <TabsTrigger value="week">Week</TabsTrigger>
            <TabsTrigger value="day">Day</TabsTrigger>
            <TabsTrigger value="list">List</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      <Card>
        <CardContent className="p-6">
          {viewMode === 'month' && renderCalendarView()}
          {viewMode === 'list' && renderListView()}
          {viewMode === 'week' && (
            <div className="text-center text-muted-foreground py-8">
              Week view coming soon...
            </div>
          )}
          {viewMode === 'day' && (
            <div className="text-center text-muted-foreground py-8">
              Day view coming soon...
            </div>
          )}
        </CardContent>
      </Card>

      {/* Session Statistics */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Total Sessions</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{sessions.length}</div>
            <p className="text-xs text-muted-foreground">This month</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Upcoming</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {sessions.filter(s => s.startDate > new Date() && s.status === 'scheduled').length}
            </div>
            <p className="text-xs text-muted-foreground">Next 30 days</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Total Enrolled</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {sessions.reduce((acc, s) => acc + s.enrolledParticipants.length, 0)}
            </div>
            <p className="text-xs text-muted-foreground">Active participants</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Capacity</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Math.round(
                (sessions.reduce((acc, s) => acc + s.enrolledParticipants.length, 0) /
                sessions.reduce((acc, s) => acc + s.maxParticipants, 1)) * 100
              )}%
            </div>
            <p className="text-xs text-muted-foreground">Average utilization</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};