import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  BookOpen,
  Plus,
  Edit,
  Trash2,
  Save,
  Eye,
  Upload,
  Download,
  FileText,
  Image,
  Video,
  Users,
  Target,
  Clock,
  Hash,
  Search,
  Filter,
  Calendar,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { motion } from 'framer-motion';
import { useTraining } from '@/contexts/TrainingContext';
import type { TrainingModule, MediaAsset, LearningObjective, AssessmentQuestion } from './types';

export const TrainingContentStudio: React.FC = () => {
  const { state, dispatch } = useTraining();
  const [activeTab, setActiveTab] = useState('modules');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedModule, setSelectedModule] = useState<TrainingModule | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [modules, setModules] = useState<TrainingModule[]>([]);

  // Initialize with mock data or from context
  useEffect(() => {
    // In a real implementation, this would fetch from the backend
    const mockModules: TrainingModule[] = [
      {
        id: '1',
        title: 'Introduction to React Hooks',
        description: 'Learn the fundamentals of React Hooks and how to use them effectively',
        type: 'SMP',
        duration: 120,
        objectives: [
          { id: 'obj1', text: 'Understand useState and useEffect hooks', priority: 'high' },
          { id: 'obj2', text: 'Create custom hooks', priority: 'medium' }
        ],
        content: '# React Hooks Introduction\n\nReact Hooks are functions that let you "hook into" React state and lifecycle features from function components.',
        assessments: [
          {
            id: 'q1',
            question: 'What is the purpose of useState hook?',
            type: 'multiple_choice',
            options: ['State management', 'Side effects', 'DOM manipulation', 'Routing'],
            correctAnswer: 'State management'
          }
        ],
        media: [
          { id: 'media1', name: 'react-hooks-diagram.png', type: 'image', url: '/placeholder.jpg', size: '2.4MB' }
        ],
        complianceTags: ['React', 'Frontend'],
        targetRoles: ['Developer', 'Frontend Engineer'],
        status: 'published'
      }
    ];
    setModules(mockModules);
  }, []);

  const handleCreateModule = () => {
    const newModule: TrainingModule = {
      id: Date.now().toString(),
      title: 'New Training Module',
      description: '',
      type: 'SMP',
      duration: 60,
      objectives: [],
      content: '',
      assessments: [],
      media: [],
      complianceTags: [],
      targetRoles: [],
      status: 'draft'
    };
    setSelectedModule(newModule);
    setIsEditing(true);
  };

  const handleSaveModule = () => {
    if (selectedModule) {
      if (isEditing) {
        setModules(prev => 
          prev.some(m => m.id === selectedModule.id) 
            ? prev.map(m => m.id === selectedModule.id ? selectedModule : m)
            : [...prev, selectedModule]
        );
      }
      setIsEditing(false);
      setSelectedModule(null);
    }
  };

  const handleDeleteModule = (id: string) => {
    setModules(prev => prev.filter(m => m.id !== id));
    if (selectedModule?.id === id) {
      setSelectedModule(null);
    }
  };

  const filteredModules = modules.filter(module => 
    module.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    module.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const renderModuleList = () => (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search modules..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Button onClick={handleCreateModule}>
          <Plus className="h-4 w-4 mr-2" />
          New Module
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredModules.map(module => (
          <Card key={module.id} className="p-4 hover:shadow-md transition-shadow">
            <div className="flex items-start justify-between mb-3">
              <div className="flex-1">
                <h3 className="font-semibold text-sm mb-1">{module.title}</h3>
                <p className="text-xs text-muted-foreground mb-2 line-clamp-2">
                  {module.description}
                </p>
              </div>
              <Badge variant="outline" className="text-xs ml-2">
                {module.type}
              </Badge>
            </div>

            <div className="flex items-center gap-2 mb-3">
              <div className="flex items-center gap-1 text-xs text-muted-foreground">
                <Clock className="h-3 w-3" />
                <span>{module.duration}m</span>
              </div>
              <Badge 
                variant={module.status === 'published' ? 'default' : 'secondary'}
                className="text-xs"
              >
                {module.status}
              </Badge>
            </div>

            <div className="flex items-center gap-2">
              <Button 
                size="sm" 
                variant="outline" 
                className="flex-1 text-xs"
                onClick={() => {
                  setSelectedModule(module);
                  setIsEditing(false);
                }}
              >
                <Eye className="h-3 w-3 mr-1" />
                View
              </Button>
              <Button 
                size="sm" 
                variant="outline" 
                className="flex-1 text-xs"
                onClick={() => {
                  setSelectedModule(module);
                  setIsEditing(true);
                }}
              >
                <Edit className="h-3 w-3 mr-1" />
                Edit
              </Button>
              <Button 
                size="sm" 
                variant="outline" 
                className="flex-1 text-xs"
                onClick={() => handleDeleteModule(module.id)}
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );

  const renderModuleEditor = () => {
    if (!selectedModule) return null;

    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold">
            {isEditing ? 'Edit Module' : 'View Module'}
          </h2>
          <div className="flex items-center gap-2">
            <Button 
              variant="outline" 
              onClick={() => {
                setSelectedModule(null);
                setIsEditing(false);
              }}
            >
              Back
            </Button>
            {isEditing && (
              <Button onClick={handleSaveModule}>
                <Save className="h-4 w-4 mr-2" />
                Save Module
              </Button>
            )}
          </div>
        </div>

        <Tabs defaultValue="content">
          <TabsList>
            <TabsTrigger value="content">Content</TabsTrigger>
            <TabsTrigger value="objectives">Objectives</TabsTrigger>
            <TabsTrigger value="assessments">Assessments</TabsTrigger>
            <TabsTrigger value="media">Media</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="content" className="space-y-4">
            <Card className="p-4">
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium">Title</label>
                  <Input
                    value={selectedModule.title}
                    onChange={(e) => setSelectedModule({...selectedModule, title: e.target.value})}
                    disabled={!isEditing}
                  />
                </div>
                
                <div>
                  <label className="text-sm font-medium">Description</label>
                  <Textarea
                    value={selectedModule.description}
                    onChange={(e) => setSelectedModule({...selectedModule, description: e.target.value})}
                    rows={3}
                    disabled={!isEditing}
                  />
                </div>

                <div>
                  <label className="text-sm font-medium">Content</label>
                  <Textarea
                    value={selectedModule.content}
                    onChange={(e) => setSelectedModule({...selectedModule, content: e.target.value})}
                    rows={10}
                    disabled={!isEditing}
                  />
                </div>
              </div>
            </Card>
          </TabsContent>

          <TabsContent value="objectives" className="space-y-4">
            <Card className="p-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-medium">Learning Objectives</h3>
                {isEditing && (
                  <Button size="sm">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Objective
                  </Button>
                )}
              </div>
              
              <div className="space-y-3">
                {selectedModule.objectives.map((objective, index) => (
                  <div key={objective.id} className="flex items-center gap-2">
                    <div className="flex-1">
                      <Input
                        value={objective.text}
                        onChange={(e) => {
                          const newObjectives = [...selectedModule.objectives];
                          newObjectives[index] = {...objective, text: e.target.value};
                          setSelectedModule({...selectedModule, objectives: newObjectives});
                        }}
                        disabled={!isEditing}
                      />
                    </div>
                    <Select 
                      value={objective.priority}
                      onValueChange={(value) => {
                        const newObjectives = [...selectedModule.objectives];
                        newObjectives[index] = {...objective, priority: value as any};
                        setSelectedModule({...selectedModule, objectives: newObjectives});
                      }}
                      disabled={!isEditing}
                    >
                      <SelectTrigger className="w-24">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="high">High</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="low">Low</SelectItem>
                      </SelectContent>
                    </Select>
                    {isEditing && (
                      <Button size="sm" variant="outline">
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    )}
                  </div>
                ))}
              </div>
            </Card>
          </TabsContent>

          <TabsContent value="assessments" className="space-y-4">
            <Card className="p-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-medium">Assessments</h3>
                {isEditing && (
                  <Button size="sm">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Question
                  </Button>
                )}
              </div>
              
              <div className="space-y-4">
                {selectedModule.assessments.map((question, index) => (
                  <Card key={question.id} className="p-4">
                    <div className="space-y-3">
                      <div>
                        <label className="text-sm font-medium">Question</label>
                        <Input
                          value={question.question}
                          onChange={(e) => {
                            const newAssessments = [...selectedModule.assessments];
                            newAssessments[index] = {...question, question: e.target.value};
                            setSelectedModule({...selectedModule, assessments: newAssessments});
                          }}
                          disabled={!isEditing}
                        />
                      </div>
                      
                      <div>
                        <label className="text-sm font-medium">Question Type</label>
                        <Select 
                          value={question.type}
                          onValueChange={(value) => {
                            const newAssessments = [...selectedModule.assessments];
                            newAssessments[index] = {...question, type: value as any};
                            setSelectedModule({...selectedModule, assessments: newAssessments});
                          }}
                          disabled={!isEditing}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="multiple_choice">Multiple Choice</SelectItem>
                            <SelectItem value="true_false">True/False</SelectItem>
                            <SelectItem value="short_answer">Short Answer</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      {question.type === 'multiple_choice' && (
                        <div>
                          <label className="text-sm font-medium">Options</label>
                          <div className="space-y-2 mt-1">
                            {question.options?.map((option, optIndex) => (
                              <Input
                                key={optIndex}
                                value={option}
                                onChange={(e) => {
                                  const newOptions = [...(question.options || [])];
                                  newOptions[optIndex] = e.target.value;
                                  const newAssessments = [...selectedModule.assessments];
                                  newAssessments[index] = {...question, options: newOptions};
                                  setSelectedModule({...selectedModule, assessments: newAssessments});
                                }}
                                disabled={!isEditing}
                              />
                            ))}
                          </div>
                        </div>
                      )}

                      <div>
                        <label className="text-sm font-medium">Correct Answer</label>
                        <Input
                          value={question.correctAnswer}
                          onChange={(e) => {
                            const newAssessments = [...selectedModule.assessments];
                            newAssessments[index] = {...question, correctAnswer: e.target.value};
                            setSelectedModule({...selectedModule, assessments: newAssessments});
                          }}
                          disabled={!isEditing}
                        />
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </Card>
          </TabsContent>

          <TabsContent value="media" className="space-y-4">
            <Card className="p-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-medium">Media Assets</h3>
                {isEditing && (
                  <Button size="sm">
                    <Upload className="h-4 w-4 mr-2" />
                    Upload Media
                  </Button>
                )}
              </div>
              
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                {selectedModule.media.map(media => (
                  <div key={media.id} className="border rounded-lg p-3">
                    <div className="flex items-center gap-2 mb-2">
                      {media.type === 'image' && <Image className="h-4 w-4" />}
                      {media.type === 'video' && <Video className="h-4 w-4" />}
                      <span className="text-xs font-medium">{media.name}</span>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {media.size}
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          </TabsContent>

          <TabsContent value="settings" className="space-y-4">
            <Card className="p-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">Module Type</label>
                  <Select 
                    value={selectedModule.type}
                    onValueChange={(value) => setSelectedModule({...selectedModule, type: value as any})}
                    disabled={!isEditing}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="SMP">SMP</SelectItem>
                      <SelectItem value="SOP">SOP</SelectItem>
                      <SelectItem value="RiskAssessment">Risk Assessment</SelectItem>
                      <SelectItem value="ElectricalBasic">Electrical (Basic)</SelectItem>
                      <SelectItem value="ElectricalAdvanced">Electrical (Advanced)</SelectItem>
                      <SelectItem value="ElectricalExpert">Electrical (Expert)</SelectItem>
                      <SelectItem value="VFD750">VFD 750</SelectItem>
                      <SelectItem value="VFD755">VFD 755</SelectItem>
                      <SelectItem value="PLCBasic">PLC (Basic)</SelectItem>
                      <SelectItem value="PLCAdvanced">PLC (Advanced)</SelectItem>
                      <SelectItem value="PLCExpert">PLC (Expert)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium">Duration (minutes)</label>
                  <Input
                    type="number"
                    value={selectedModule.duration}
                    onChange={(e) => setSelectedModule({...selectedModule, duration: parseInt(e.target.value) || 0})}
                    disabled={!isEditing}
                  />
                </div>

                <div>
                  <label className="text-sm font-medium">Status</label>
                  <Select 
                    value={selectedModule.status}
                    onValueChange={(value) => setSelectedModule({...selectedModule, status: value as any})}
                    disabled={!isEditing}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="draft">Draft</SelectItem>
                      <SelectItem value="review">Review</SelectItem>
                      <SelectItem value="published">Published</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium">Target Roles</label>
                  <Input
                    value={selectedModule.targetRoles.join(', ')}
                    onChange={(e) => setSelectedModule({
                      ...selectedModule, 
                      targetRoles: e.target.value.split(',').map(role => role.trim())
                    })}
                    disabled={!isEditing}
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="text-sm font-medium">Compliance Tags</label>
                  <Input
                    value={selectedModule.complianceTags.join(', ')}
                    onChange={(e) => setSelectedModule({
                      ...selectedModule, 
                      complianceTags: e.target.value.split(',').map(tag => tag.trim())
                    })}
                    disabled={!isEditing}
                  />
                </div>
              </div>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    );
  };

  return (
    <div className="h-full overflow-y-auto">
      <div className="container mx-auto p-6">
        <motion.div 
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight flex items-center gap-3">
                <BookOpen className="h-8 w-8" />
                Training Content Studio
              </h1>
              <p className="mt-1 text-sm text-muted-foreground">
                Create and manage eLearning content modules
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm">
                <Upload className="h-4 w-4 mr-2" />
                Import
              </Button>
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </div>
          </div>
        </motion.div>

        {selectedModule ? renderModuleEditor() : renderModuleList()}
      </div>
    </div>
  );
};

export default TrainingContentStudio;