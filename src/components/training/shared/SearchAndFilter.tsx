import React, { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Separator } from '@/components/ui/separator';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Calendar } from '@/components/ui/calendar';
import { 
  Search, 
  Filter, 
  X, 
  Calendar as CalendarIcon,
  SlidersHorizontal,
  RotateCcw
} from 'lucide-react';
import { format } from 'date-fns';

export interface FilterOption {
  id: string;
  label: string;
  type: 'select' | 'multiselect' | 'date' | 'daterange' | 'checkbox';
  options?: { value: string; label: string }[];
  value?: any;
}

export interface SearchAndFilterProps {
  searchValue?: string;
  onSearchChange?: (value: string) => void;
  searchPlaceholder?: string;
  filters?: FilterOption[];
  onFiltersChange?: (filters: FilterOption[]) => void;
  onClearAll?: () => void;
  showFilterCount?: boolean;
  className?: string;
}

export const SearchAndFilter: React.FC<SearchAndFilterProps> = ({
  searchValue = '',
  onSearchChange,
  searchPlaceholder = 'Search...',
  filters = [],
  onFiltersChange,
  onClearAll,
  showFilterCount = true,
  className = ''
}) => {
  const [localSearchValue, setLocalSearchValue] = useState(searchValue);
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [localFilters, setLocalFilters] = useState<FilterOption[]>(filters);

  // Debounce search input
  useEffect(() => {
    const timer = setTimeout(() => {
      onSearchChange?.(localSearchValue);
    }, 300);

    return () => clearTimeout(timer);
  }, [localSearchValue, onSearchChange]);

  // Update local filters when props change
  useEffect(() => {
    setLocalFilters(filters);
  }, [filters]);

  const handleSearchChange = (value: string) => {
    setLocalSearchValue(value);
  };

  const handleFilterChange = (filterId: string, value: any) => {
    const updatedFilters = localFilters.map(filter =>
      filter.id === filterId ? { ...filter, value } : filter
    );
    setLocalFilters(updatedFilters);
    onFiltersChange?.(updatedFilters);
  };

  const handleClearFilter = (filterId: string) => {
    const updatedFilters = localFilters.map(filter =>
      filter.id === filterId ? { ...filter, value: undefined } : filter
    );
    setLocalFilters(updatedFilters);
    onFiltersChange?.(updatedFilters);
  };

  const handleClearAll = () => {
    setLocalSearchValue('');
    const clearedFilters = localFilters.map(filter => ({ ...filter, value: undefined }));
    setLocalFilters(clearedFilters);
    onClearAll?.();
  };

  const getActiveFilterCount = () => {
    let count = 0;
    if (localSearchValue.trim()) count++;
    count += localFilters.filter(filter => {
      if (Array.isArray(filter.value)) return filter.value.length > 0;
      return filter.value !== undefined && filter.value !== '';
    }).length;
    return count;
  };

  const getActiveFilters = () => {
    const active = [];
    if (localSearchValue.trim()) {
      active.push({
        id: 'search',
        label: `Search: "${localSearchValue}"`,
        onRemove: () => setLocalSearchValue('')
      });
    }
    
    localFilters.forEach(filter => {
      if (filter.value !== undefined && filter.value !== '') {
        if (Array.isArray(filter.value) && filter.value.length > 0) {
          active.push({
            id: filter.id,
            label: `${filter.label}: ${filter.value.length} selected`,
            onRemove: () => handleClearFilter(filter.id)
          });
        } else if (!Array.isArray(filter.value)) {
          let displayValue = filter.value;
          if (filter.type === 'date' && filter.value) {
            displayValue = format(new Date(filter.value), 'MMM dd, yyyy');
          } else if (filter.type === 'select' && filter.options) {
            const option = filter.options.find(opt => opt.value === filter.value);
            displayValue = option?.label || filter.value;
          }
          active.push({
            id: filter.id,
            label: `${filter.label}: ${displayValue}`,
            onRemove: () => handleClearFilter(filter.id)
          });
        }
      }
    });
    
    return active;
  };

  const renderFilterControl = (filter: FilterOption) => {
    switch (filter.type) {
      case 'select':
        return (
          <Select value={filter.value || ''} onValueChange={(value) => handleFilterChange(filter.id, value)}>
            <SelectTrigger>
              <SelectValue placeholder={`Select ${filter.label.toLowerCase()}`} />
            </SelectTrigger>
            <SelectContent>
              {filter.options?.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );

      case 'multiselect':
        const selectedValues = Array.isArray(filter.value) ? filter.value : [];
        return (
          <div className="space-y-2">
            <div className="max-h-32 overflow-y-auto space-y-2">
              {filter.options?.map((option) => (
                <div key={option.value} className="flex items-center space-x-2">
                  <Checkbox
                    id={`${filter.id}-${option.value}`}
                    checked={selectedValues.includes(option.value)}
                    onCheckedChange={(checked) => {
                      const newValues = checked
                        ? [...selectedValues, option.value]
                        : selectedValues.filter(v => v !== option.value);
                      handleFilterChange(filter.id, newValues);
                    }}
                  />
                  <Label htmlFor={`${filter.id}-${option.value}`} className="text-sm">
                    {option.label}
                  </Label>
                </div>
              ))}
            </div>
          </div>
        );

      case 'date':
        return (
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="w-full justify-start text-left font-normal"
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {filter.value ? format(new Date(filter.value), 'PPP') : 'Pick a date'}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={filter.value ? new Date(filter.value) : undefined}
                onSelect={(date) => handleFilterChange(filter.id, date?.toISOString())}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        );

      case 'checkbox':
        return (
          <div className="flex items-center space-x-2">
            <Checkbox
              id={filter.id}
              checked={filter.value || false}
              onCheckedChange={(checked) => handleFilterChange(filter.id, checked)}
            />
            <Label htmlFor={filter.id} className="text-sm">
              {filter.label}
            </Label>
          </div>
        );

      default:
        return null;
    }
  };

  const activeFilterCount = getActiveFilterCount();
  const activeFilters = getActiveFilters();

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Search and Filter Controls */}
      <div className="flex gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            value={localSearchValue}
            onChange={(e) => handleSearchChange(e.target.value)}
            placeholder={searchPlaceholder}
            className="pl-10"
          />
          {localSearchValue && (
            <Button
              variant="ghost"
              size="sm"
              className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
              onClick={() => setLocalSearchValue('')}
            >
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>
        
        {filters.length > 0 && (
          <Popover open={isFilterOpen} onOpenChange={setIsFilterOpen}>
            <PopoverTrigger asChild>
              <Button variant="outline" className="relative">
                <SlidersHorizontal className="h-4 w-4 mr-2" />
                Filters
                {showFilterCount && activeFilterCount > 0 && (
                  <Badge variant="secondary" className="ml-2 h-5 w-5 rounded-full p-0 text-xs flex items-center justify-center">
                    {activeFilterCount}
                  </Badge>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80 p-0" align="end">
              <div className="p-4 space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">Filters</h4>
                  {activeFilterCount > 0 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleClearAll}
                      className="text-xs"
                    >
                      <RotateCcw className="h-3 w-3 mr-1" />
                      Clear all
                    </Button>
                  )}
                </div>
                <Separator />
                <div className="space-y-4">
                  {localFilters.map((filter) => (
                    <div key={filter.id} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label className="text-sm font-medium">{filter.label}</Label>
                        {filter.value !== undefined && filter.value !== '' && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleClearFilter(filter.id)}
                            className="h-6 w-6 p-0"
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        )}
                      </div>
                      {renderFilterControl(filter)}
                    </div>
                  ))}
                </div>
              </div>
            </PopoverContent>
          </Popover>
        )}
      </div>

      {/* Active Filters */}
      {activeFilters.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {activeFilters.map((filter) => (
            <Badge key={filter.id} variant="secondary" className="text-xs">
              {filter.label}
              <Button
                variant="ghost"
                size="sm"
                className="h-auto p-0 ml-1 hover:bg-transparent"
                onClick={filter.onRemove}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          ))}
        </div>
      )}
    </div>
  );
};