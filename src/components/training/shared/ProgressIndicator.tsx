import React from 'react';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';

interface ProgressIndicatorProps {
  current: number;
  total: number;
  label?: string;
  showPercentage?: boolean;
  showFraction?: boolean;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'success' | 'warning' | 'danger';
  className?: string;
}

export const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({
  current,
  total,
  label,
  showPercentage = true,
  showFraction = false,
  size = 'md',
  variant = 'default',
  className = ''
}) => {
  const percentage = total > 0 ? (current / total) * 100 : 0;
  
  const getVariantColor = () => {
    switch (variant) {
      case 'success': return 'text-green-600';
      case 'warning': return 'text-yellow-600';
      case 'danger': return 'text-red-600';
      default: return 'text-primary';
    }
  };

  const getSizeClass = () => {
    switch (size) {
      case 'sm': return 'h-1';
      case 'lg': return 'h-3';
      default: return 'h-2';
    }
  };

  return (
    <div className={`space-y-2 ${className}`}>
      {label && (
        <div className="flex items-center justify-between text-sm">
          <span className="font-medium">{label}</span>
          <div className="flex items-center gap-2">
            {showFraction && (
              <span className="text-muted-foreground">
                {current}/{total}
              </span>
            )}
            {showPercentage && (
              <span className={`font-medium ${getVariantColor()}`}>
                {percentage.toFixed(0)}%
              </span>
            )}
          </div>
        </div>
      )}
      <Progress 
        value={percentage} 
        className={getSizeClass()}
      />
    </div>
  );
};