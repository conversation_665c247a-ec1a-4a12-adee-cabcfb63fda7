import React from 'react';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, Clock, AlertTriangle, XCircle, Pause } from 'lucide-react';

type StatusType = 
  | 'active' | 'completed' | 'in_progress' | 'pending' | 'expired' 
  | 'cancelled' | 'scheduled' | 'not_started' | 'dropped' | 'approved'
  | 'rejected' | 'on_hold' | 'expiring_soon' | 'pending_renewal';

interface StatusBadgeProps {
  status: StatusType;
  showIcon?: boolean;
  className?: string;
}

const statusConfig = {
  active: { variant: 'success' as const, icon: CheckCircle, color: 'text-green-500' },
  completed: { variant: 'success' as const, icon: CheckCircle, color: 'text-green-500' },
  in_progress: { variant: 'default' as const, icon: Clock, color: 'text-blue-500' },
  pending: { variant: 'secondary' as const, icon: Clock, color: 'text-gray-500' },
  expired: { variant: 'destructive' as const, icon: XCircle, color: 'text-red-500' },
  cancelled: { variant: 'destructive' as const, icon: XCircle, color: 'text-red-500' },
  scheduled: { variant: 'default' as const, icon: Clock, color: 'text-blue-500' },
  not_started: { variant: 'secondary' as const, icon: Pause, color: 'text-gray-500' },
  dropped: { variant: 'destructive' as const, icon: XCircle, color: 'text-red-500' },
  approved: { variant: 'success' as const, icon: CheckCircle, color: 'text-green-500' },
  rejected: { variant: 'destructive' as const, icon: XCircle, color: 'text-red-500' },
  on_hold: { variant: 'secondary' as const, icon: Pause, color: 'text-gray-500' },
  expiring_soon: { variant: 'warning' as const, icon: AlertTriangle, color: 'text-yellow-500' },
  pending_renewal: { variant: 'warning' as const, icon: AlertTriangle, color: 'text-yellow-500' },
};

export const StatusBadge: React.FC<StatusBadgeProps> = ({
  status,
  showIcon = false,
  className = ''
}) => {
  const config = statusConfig[status] || statusConfig.pending;
  const Icon = config.icon;

  return (
    <Badge variant={config.variant} className={className}>
      {showIcon && <Icon className="h-3 w-3 mr-1" />}
      {status.replace('_', ' ')}
    </Badge>
  );
};