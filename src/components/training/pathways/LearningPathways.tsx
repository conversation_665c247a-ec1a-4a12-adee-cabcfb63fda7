import React, { useState, useEffect } from 'react';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Map as Route,
  Target,
  CheckCircle,
  Circle,
  Lock,
  Unlock,
  Star,
  Clock,
  TrendingUp,
  Users,
  BookOpen,
  Award,
  ChevronRight,
  Play,
  Pause,
  RotateCcw,
  Map,
  Compass,
  Flag,
  Zap
} from 'lucide-react';
import { format, addMonths } from 'date-fns';
import { LearningPathway, PathwayStage, EmployeePathway } from '../types';
import { useTraining } from '@/contexts/TrainingContext';

export const LearningPathways: React.FC = () => {
  const { state, dispatch } = useTraining();
  const [pathways, setPathways] = useState<LearningPathway[]>([]);
  const [employeePathways, setEmployeePathways] = useState<EmployeePathway[]>([]);
  const [selectedPathway, setSelectedPathway] = useState<string>('');
  const [selectedEmployee, setSelectedEmployee] = useState<string>('emp-001');

  // Mock data
  useEffect(() => {
    const mockPathways: LearningPathway[] = [
      {
        id: 'path-1',
        name: 'Junior to Senior Engineer',
        description: 'Complete technical progression from junior to senior engineering role',
        targetRole: 'Senior Software Engineer',
        level: 'advanced',
        estimatedDuration: 480, // hours
        stages: [
          {
            id: 'stage-1',
            name: 'Foundation Skills',
            order: 1,
            courses: ['prog-1', 'prog-2', 'prog-3'],
            milestones: ['Complete core programming', 'Pass technical assessment'],
            requiredScore: 80,
            optional: false,
            estimatedDuration: 120
          },
          {
            id: 'stage-2',
            name: 'Advanced Development',
            order: 2,
            courses: ['prog-4', 'prog-5'],
            milestones: ['Build production app', 'Code review certification'],
            requiredScore: 85,
            optional: false,
            estimatedDuration: 160
          },
          {
            id: 'stage-3',
            name: 'Architecture & Design',
            order: 3,
            courses: ['prog-6', 'prog-7'],
            milestones: ['Design system architecture', 'Lead technical project'],
            requiredScore: 90,
            optional: false,
            estimatedDuration: 120
          },
          {
            id: 'stage-4',
            name: 'Leadership & Mentoring',
            order: 4,
            courses: ['prog-8'],
            milestones: ['Mentor junior developers', 'Lead team meetings'],
            requiredScore: 85,
            optional: true,
            estimatedDuration: 80
          }
        ],
        prerequisites: ['2 years experience', 'Bachelor in CS or equivalent'],
        outcomes: [
          'Advanced programming skills',
          'System design capabilities',
          'Team leadership experience',
          'Production deployment expertise'
        ],
        certification: 'Senior Engineer Certification'
      },
      {
        id: 'path-2',
        name: 'Team Lead Development',
        description: 'Develop leadership skills for team management roles',
        targetRole: 'Team Lead',
        level: 'intermediate',
        estimatedDuration: 240,
        stages: [
          {
            id: 'stage-5',
            name: 'Leadership Fundamentals',
            order: 1,
            courses: ['lead-1', 'lead-2'],
            milestones: ['Complete leadership assessment'],
            requiredScore: 75,
            optional: false,
            estimatedDuration: 80
          },
          {
            id: 'stage-6',
            name: 'Team Management',
            order: 2,
            courses: ['lead-3', 'lead-4'],
            milestones: ['Manage team project', 'Conduct performance reviews'],
            requiredScore: 80,
            optional: false,
            estimatedDuration: 100
          },
          {
            id: 'stage-7',
            name: 'Strategic Planning',
            order: 3,
            courses: ['lead-5'],
            milestones: ['Create team roadmap'],
            requiredScore: 85,
            optional: true,
            estimatedDuration: 60
          }
        ],
        prerequisites: ['3 years experience', 'Current senior role'],
        outcomes: [
          'Team management skills',
          'Strategic thinking',
          'Conflict resolution',
          'Performance management'
        ],
        certification: 'Certified Team Leader'
      },
      {
        id: 'path-3',
        name: 'Safety Specialist',
        description: 'Comprehensive safety certification pathway',
        targetRole: 'Safety Specialist',
        level: 'beginner',
        estimatedDuration: 160,
        stages: [
          {
            id: 'stage-8',
            name: 'Safety Basics',
            order: 1,
            courses: ['safety-1', 'safety-2'],
            milestones: ['Pass safety exam'],
            requiredScore: 90,
            optional: false,
            estimatedDuration: 60
          },
          {
            id: 'stage-9',
            name: 'Risk Assessment',
            order: 2,
            courses: ['safety-3'],
            milestones: ['Conduct risk assessment'],
            requiredScore: 85,
            optional: false,
            estimatedDuration: 50
          },
          {
            id: 'stage-10',
            name: 'Emergency Response',
            order: 3,
            courses: ['safety-4'],
            milestones: ['Emergency drill leadership'],
            requiredScore: 88,
            optional: false,
            estimatedDuration: 50
          }
        ],
        prerequisites: ['Safety orientation completed'],
        outcomes: [
          'Safety compliance expertise',
          'Risk assessment skills',
          'Emergency response leadership'
        ],
        certification: 'Certified Safety Specialist'
      }
    ];

    const mockEmployeePathways: EmployeePathway[] = [
      {
        employeeId: 'emp-001',
        pathwayId: 'path-1',
        startDate: new Date('2024-01-01'),
        currentStage: 2,
        progress: 45,
        completedStages: ['stage-1'],
        estimatedCompletion: addMonths(new Date(), 4),
        status: 'active'
      },
      {
        employeeId: 'emp-002',
        pathwayId: 'path-2',
        startDate: new Date('2023-11-01'),
        currentStage: 3,
        progress: 75,
        completedStages: ['stage-5', 'stage-6'],
        estimatedCompletion: addMonths(new Date(), 2),
        status: 'active'
      },
      {
        employeeId: 'emp-003',
        pathwayId: 'path-3',
        startDate: new Date('2024-01-15'),
        currentStage: 1,
        progress: 30,
        completedStages: [],
        estimatedCompletion: addMonths(new Date(), 3),
        status: 'paused'
      }
    ];

    setPathways(mockPathways);
    setEmployeePathways(mockEmployeePathways);
    setSelectedPathway(mockPathways[0].id);
  }, []);

  const getEmployeeName = (employeeId: string) => {
    const names: { [key: string]: string } = {
      'emp-001': 'John Smith',
      'emp-002': 'Sarah Johnson',
      'emp-003': 'Mike Wilson'
    };
    return names[employeeId] || employeeId;
  };

  const selectedPathwayData = pathways.find(p => p.id === selectedPathway);
  const currentEmployeePathway = employeePathways.find(
    ep => ep.employeeId === selectedEmployee && ep.pathwayId === selectedPathway
  );

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'beginner': return 'success';
      case 'intermediate': return 'warning';
      case 'advanced': return 'destructive';
      case 'expert': return 'purple';
      default: return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <Play className="h-4 w-4 text-green-500" />;
      case 'paused': return <Pause className="h-4 w-4 text-amber-500" />;
      case 'completed': return <CheckCircle className="h-4 w-4 text-blue-500" />;
      case 'abandoned': return <RotateCcw className="h-4 w-4 text-red-500" />;
      default: return <Circle className="h-4 w-4" />;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Learning Pathways</h2>
          <p className="text-muted-foreground">
            Structured career development and skill progression paths
          </p>
        </div>
        <Button>
          <Map className="h-4 w-4 mr-2" />
          Create Pathway
        </Button>
      </div>

      {/* Pathway Overview Cards */}
      <div className="grid gap-4 md:grid-cols-3">
        {pathways.map(pathway => (
          <Card 
            key={pathway.id}
            className={selectedPathway === pathway.id ? 'ring-2 ring-primary' : ''}
            onClick={() => setSelectedPathway(pathway.id)}
          >
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-lg">{pathway.name}</CardTitle>
                  <CardDescription className="mt-1">
                    {pathway.targetRole}
                  </CardDescription>
                </div>
                <Badge variant={getLevelColor(pathway.level) as any}>
                  {pathway.level}
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="space-y-3">
              <p className="text-sm text-muted-foreground line-clamp-2">
                {pathway.description}
              </p>
              
              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center gap-1">
                  <Route className="h-3 w-3" />
                  <span>{pathway.stages.length} stages</span>
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  <span>{pathway.estimatedDuration}h</span>
                </div>
              </div>

              <div className="flex items-center gap-2">
                {pathway.certification && (
                  <Badge variant="outline" className="text-xs">
                    <Award className="h-3 w-3 mr-1" />
                    Certification
                  </Badge>
                )}
                <Badge variant="secondary" className="text-xs">
                  {employeePathways.filter(ep => ep.pathwayId === pathway.id).length} enrolled
                </Badge>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {selectedPathwayData && (
        <Tabs defaultValue="overview">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="stages">Stages & Progress</TabsTrigger>
            <TabsTrigger value="enrolled">Enrolled Learners</TabsTrigger>
            <TabsTrigger value="requirements">Requirements</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>{selectedPathwayData.name}</CardTitle>
                <CardDescription>{selectedPathwayData.description}</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <h4 className="font-medium">Target Role</h4>
                    <p className="text-sm text-muted-foreground">{selectedPathwayData.targetRole}</p>
                  </div>
                  <div className="space-y-2">
                    <h4 className="font-medium">Duration</h4>
                    <p className="text-sm text-muted-foreground">
                      {selectedPathwayData.estimatedDuration} hours ({Math.ceil(selectedPathwayData.estimatedDuration / 40)} weeks)
                    </p>
                  </div>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium">Learning Outcomes</h4>
                  <ul className="list-disc list-inside text-sm text-muted-foreground space-y-1">
                    {selectedPathwayData.outcomes.map((outcome, idx) => (
                      <li key={idx}>{outcome}</li>
                    ))}
                  </ul>
                </div>

                {selectedPathwayData.certification && (
                  <Alert>
                    <Award className="h-4 w-4" />
                    <AlertDescription>
                      Successful completion awards: <strong>{selectedPathwayData.certification}</strong>
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="stages" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Pathway Stages</CardTitle>
                <CardDescription>
                  Progressive learning stages with milestones and requirements
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="relative">
                  {/* Progress Line */}
                  <div className="absolute left-6 top-8 bottom-8 w-0.5 bg-muted"></div>
                  
                  {selectedPathwayData.stages
                    .sort((a, b) => a.order - b.order)
                    .map((stage, idx) => {
                      const isCompleted = currentEmployeePathway?.completedStages.includes(stage.id);
                      const isCurrent = currentEmployeePathway?.currentStage === stage.order;
                      const isLocked = currentEmployeePathway ? 
                        stage.order > currentEmployeePathway.currentStage : 
                        stage.order > 1;
                      
                      return (
                        <div key={stage.id} className="relative flex gap-4 pb-8 last:pb-0">
                          {/* Stage Indicator */}
                          <div className={`
                            relative z-10 flex h-12 w-12 items-center justify-center rounded-full border-2 bg-background
                            ${isCompleted ? 'border-green-500 bg-green-50' : 
                              isCurrent ? 'border-primary bg-primary/10' : 
                              'border-muted bg-muted/50'}
                          `}>
                            {isCompleted ? (
                              <CheckCircle className="h-6 w-6 text-green-500" />
                            ) : isLocked ? (
                              <Lock className="h-5 w-5 text-muted-foreground" />
                            ) : (
                              <span className="font-bold">{stage.order}</span>
                            )}
                          </div>

                          {/* Stage Content */}
                          <div className="flex-1 space-y-2">
                            <div className="flex items-center justify-between">
                              <div>
                                <h4 className="font-medium">{stage.name}</h4>
                                <p className="text-sm text-muted-foreground">
                                  {stage.courses.length} courses • {stage.estimatedDuration}h
                                </p>
                              </div>
                              <div className="flex items-center gap-2">
                                {stage.optional && (
                                  <Badge variant="outline" className="text-xs">Optional</Badge>
                                )}
                                {stage.requiredScore && (
                                  <Badge variant="secondary" className="text-xs">
                                    Min {stage.requiredScore}%
                                  </Badge>
                                )}
                              </div>
                            </div>

                            <div className="space-y-1">
                              <p className="text-sm font-medium">Milestones:</p>
                              <ul className="text-sm text-muted-foreground space-y-0.5">
                                {stage.milestones.map((milestone, midx) => (
                                  <li key={midx} className="flex items-center gap-2">
                                    <Target className="h-3 w-3" />
                                    {milestone}
                                  </li>
                                ))}
                              </ul>
                            </div>

                            {!isLocked && (
                              <Button size="sm" variant={isCompleted ? 'outline' : 'default'}>
                                {isCompleted ? 'Review' : isCurrent ? 'Continue' : 'Start'}
                                <ChevronRight className="h-3 w-3 ml-1" />
                              </Button>
                            )}
                          </div>
                        </div>
                      );
                    })}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="enrolled" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Enrolled Learners</CardTitle>
                <CardDescription>
                  Employees currently on this learning pathway
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                {employeePathways
                  .filter(ep => ep.pathwayId === selectedPathway)
                  .map(empPath => (
                    <div key={empPath.employeeId} className="flex items-center justify-between p-3 rounded-lg bg-muted/50">
                      <div className="flex items-center gap-3">
                        <Avatar>
                          <AvatarFallback>
                            {getEmployeeName(empPath.employeeId).split(' ').map(n => n[0]).join('')}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="font-medium">{getEmployeeName(empPath.employeeId)}</p>
                          <p className="text-sm text-muted-foreground">
                            Started {format(empPath.startDate, 'PP')}
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-4">
                        <div className="text-right">
                          <p className="text-sm font-medium">{empPath.progress}% complete</p>
                          <p className="text-xs text-muted-foreground">
                            Stage {empPath.currentStage} of {selectedPathwayData.stages.length}
                          </p>
                        </div>
                        <Progress value={empPath.progress} className="w-20" />
                        {getStatusIcon(empPath.status)}
                      </div>
                    </div>
                  ))}

                {employeePathways.filter(ep => ep.pathwayId === selectedPathway).length === 0 && (
                  <p className="text-center text-muted-foreground py-8">
                    No employees enrolled in this pathway yet
                  </p>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="requirements" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Prerequisites</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="list-disc list-inside space-y-1">
                  {selectedPathwayData.prerequisites?.map((prereq, idx) => (
                    <li key={idx} className="text-sm">{prereq}</li>
                  )) || <li className="text-sm text-muted-foreground">No prerequisites required</li>}
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Time Commitment</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="grid gap-4 md:grid-cols-3">
                  <div>
                    <p className="text-sm text-muted-foreground">Total Duration</p>
                    <p className="text-xl font-bold">{selectedPathwayData.estimatedDuration}h</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Weekly Commitment</p>
                    <p className="text-xl font-bold">10-15h</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Estimated Completion</p>
                    <p className="text-xl font-bold">
                      {Math.ceil(selectedPathwayData.estimatedDuration / 40)} weeks
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
};