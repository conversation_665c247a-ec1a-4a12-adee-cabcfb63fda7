import React, { useState } from 'react';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { 
  FileText,
  Download,
  Filter,
  TrendingUp,
  Users,
  BookOpen,
  DollarSign,
  Target,
  BarChart3,
  PieChart,
  Activity,
  Calendar,
  AlertCircle
} from 'lucide-react';
import { format } from 'date-fns';
import { TrainingReport, DashboardMetric, ReportParameters } from '../types';
import { useTraining } from '@/contexts/TrainingContext';
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON>, 
  <PERSON><PERSON><PERSON>, 
  Bar, 
  PieChart as RePie<PERSON>hart, 
  Pie, 
  Cell,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  Legend,
  RadialBarChart,
  RadialBar
} from 'recharts';

export const ReportingDashboard: React.FC = () => {
  const { state, dispatch } = useTraining();
  const [dateRange, setDateRange] = useState<{ from: Date; to: Date }>({
    from: new Date(2024, 0, 1),
    to: new Date()
  });
  const [selectedDepartment, setSelectedDepartment] = useState<string>('all');
  const [reportType, setReportType] = useState<string>('completion');

  // Dashboard metrics
  const metrics: DashboardMetric[] = [
    {
      id: 'metric-1',
      name: 'Training Completion Rate',
      value: '78%',
      change: 12,
      changeType: 'increase',
      trend: [65, 68, 72, 75, 78],
      target: 85,
      unit: '%'
    },
    {
      id: 'metric-2',
      name: 'Average Time to Completion',
      value: '14.5',
      change: -2.5,
      changeType: 'decrease',
      trend: [17, 16, 15, 14.5],
      target: 12,
      unit: 'days'
    },
    {
      id: 'metric-3',
      name: 'Training ROI',
      value: '245%',
      change: 18,
      changeType: 'increase',
      trend: [210, 225, 235, 245],
      target: 250,
      unit: '%'
    },
    {
      id: 'metric-4',
      name: 'Skills Gap Reduction',
      value: '62%',
      change: 8,
      changeType: 'increase',
      trend: [45, 52, 58, 62],
      target: 70,
      unit: '%'
    }
  ];

  // Chart data
  const completionTrend = [
    { month: 'Jan', completed: 45, enrolled: 60 },
    { month: 'Feb', completed: 52, enrolled: 65 },
    { month: 'Mar', completed: 58, enrolled: 70 },
    { month: 'Apr', completed: 65, enrolled: 75 },
    { month: 'May', completed: 72, enrolled: 80 },
    { month: 'Jun', completed: 78, enrolled: 85 }
  ];

  const departmentPerformance = [
    { department: 'Engineering', completion: 85, satisfaction: 4.5, compliance: 92 },
    { department: 'Sales', completion: 72, satisfaction: 4.2, compliance: 88 },
    { department: 'HR', completion: 90, satisfaction: 4.7, compliance: 95 },
    { department: 'Operations', completion: 68, satisfaction: 4.0, compliance: 82 },
    { department: 'Finance', completion: 78, satisfaction: 4.3, compliance: 90 }
  ];

  const trainingCostBreakdown = [
    { category: 'External Training', value: 45000, percentage: 35 },
    { category: 'Online Courses', value: 32000, percentage: 25 },
    { category: 'Internal Programs', value: 28000, percentage: 22 },
    { category: 'Certifications', value: 23000, percentage: 18 }
  ];

  const skillsImprovement = [
    { skill: 'Technical', before: 3.2, after: 4.5 },
    { skill: 'Leadership', before: 3.5, after: 4.3 },
    { skill: 'Communication', before: 3.8, after: 4.6 },
    { skill: 'Safety', before: 3.0, after: 4.8 },
    { skill: 'Compliance', before: 3.3, after: 4.7 }
  ];

  const COLORS = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'];

  const handleExportReport = () => {
    // Export logic here
    console.log('Exporting report...');
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Training Reports & Analytics</h2>
          <p className="text-muted-foreground">
            Comprehensive insights and performance metrics
          </p>
        </div>
        <Button onClick={handleExportReport}>
          <Download className="h-4 w-4 mr-2" />
          Export Report
        </Button>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-4">
        {metrics.map((metric, idx) => (
          <Card key={metric.id}>
            <CardHeader className="pb-2">
              <CardDescription>{metric.name}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-baseline justify-between">
                <div className="text-2xl font-bold">
                  {metric.value}{metric.unit === '%' ? '%' : ` ${metric.unit}`}
                </div>
                <Badge 
                  variant={metric.changeType === 'increase' ? 'success' : 'destructive'}
                  className="text-xs"
                >
                  {metric.changeType === 'increase' ? '↑' : '↓'} {Math.abs(metric.change!)}%
                </Badge>
              </div>
              <ResponsiveContainer width="100%" height={40}>
                <LineChart data={metric.trend?.map((v, i) => ({ value: v }))}>
                  <Line 
                    type="monotone" 
                    dataKey="value" 
                    stroke={COLORS[idx % COLORS.length]}
                    strokeWidth={2}
                    dot={false}
                  />
                </LineChart>
              </ResponsiveContainer>
              {metric.target && (
                <div className="mt-2 text-xs text-muted-foreground">
                  Target: {metric.target}{metric.unit}
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Report Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-4">
            <div className="space-y-2">
              <Label>Date Range</Label>
              <div className="flex gap-2">
                <Input 
                  type="date" 
                  value={format(dateRange.from, 'yyyy-MM-dd')}
                  onChange={e => setDateRange({...dateRange, from: new Date(e.target.value)})}
                />
                <Input 
                  type="date" 
                  value={format(dateRange.to, 'yyyy-MM-dd')}
                  onChange={e => setDateRange({...dateRange, to: new Date(e.target.value)})}
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label>Department</Label>
              <Select value={selectedDepartment} onValueChange={setSelectedDepartment}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Departments</SelectItem>
                  <SelectItem value="engineering">Engineering</SelectItem>
                  <SelectItem value="sales">Sales</SelectItem>
                  <SelectItem value="hr">HR</SelectItem>
                  <SelectItem value="operations">Operations</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Report Type</Label>
              <Select value={reportType} onValueChange={setReportType}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="completion">Completion Report</SelectItem>
                  <SelectItem value="progress">Progress Report</SelectItem>
                  <SelectItem value="roi">ROI Analysis</SelectItem>
                  <SelectItem value="compliance">Compliance Report</SelectItem>
                  <SelectItem value="skills_gap">Skills Gap Report</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>&nbsp;</Label>
              <Button className="w-full">
                <Filter className="h-4 w-4 mr-2" />
                Apply Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="overview">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="completion">Completion Analysis</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="financial">Financial</TabsTrigger>
          <TabsTrigger value="skills">Skills Impact</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Training Completion Trend</CardTitle>
                <CardDescription>Monthly completion vs enrollment</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={250}>
                  <LineChart data={completionTrend}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line 
                      type="monotone" 
                      dataKey="completed" 
                      stroke="#10b981" 
                      name="Completed"
                      strokeWidth={2}
                    />
                    <Line 
                      type="monotone" 
                      dataKey="enrolled" 
                      stroke="#3b82f6" 
                      name="Enrolled"
                      strokeWidth={2}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Department Performance</CardTitle>
                <CardDescription>Completion rates by department</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={250}>
                  <BarChart data={departmentPerformance}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="department" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="completion" fill="#3b82f6" name="Completion %" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Executive Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="grid gap-4 md:grid-cols-3">
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">Total Trained</p>
                  <p className="text-2xl font-bold">1,247</p>
                  <p className="text-xs text-green-600">+15% from last quarter</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">Training Hours</p>
                  <p className="text-2xl font-bold">18,450</p>
                  <p className="text-xs text-green-600">+22% from last quarter</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">Compliance Rate</p>
                  <p className="text-2xl font-bold">94.2%</p>
                  <p className="text-xs text-green-600">Above target</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="completion" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Program Completion Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {['Safety Certification', 'Leadership Excellence', 'Technical Skills', 'Compliance Training'].map((program, idx) => {
                  const completion = [92, 85, 78, 95][idx];
                  const enrolled = [150, 120, 180, 200][idx];
                  const avgTime = [12, 21, 18, 8][idx];
                  
                  return (
                    <div key={program} className="space-y-2">
                      <div className="flex justify-between items-center">
                        <div>
                          <p className="font-medium">{program}</p>
                          <p className="text-sm text-muted-foreground">
                            {enrolled} enrolled • {avgTime} days avg completion
                          </p>
                        </div>
                        <Badge variant={completion >= 90 ? 'success' : completion >= 80 ? 'warning' : 'destructive'}>
                          {completion}%
                        </Badge>
                      </div>
                      <Progress value={completion} />
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Assessment Scores Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={250}>
                  <RePieChart>
                    <Pie
                      data={[
                        { name: '90-100%', value: 35 },
                        { name: '80-89%', value: 40 },
                        { name: '70-79%', value: 20 },
                        { name: 'Below 70%', value: 5 }
                      ]}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={entry => `${entry.name}: ${entry.value}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {[0, 1, 2, 3].map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </RePieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Learning Velocity</CardTitle>
                <CardDescription>Average time to complete modules</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Fast Learners (&lt; 7 days)</span>
                    <div className="flex items-center gap-2">
                      <Progress value={25} className="w-20" />
                      <span className="text-sm font-medium">25%</span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Average (7-14 days)</span>
                    <div className="flex items-center gap-2">
                      <Progress value={55} className="w-20" />
                      <span className="text-sm font-medium">55%</span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Slow (&gt; 14 days)</span>
                    <div className="flex items-center gap-2">
                      <Progress value={20} className="w-20" />
                      <span className="text-sm font-medium">20%</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="financial" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Training Cost Breakdown</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={250}>
                  <RePieChart>
                    <Pie
                      data={trainingCostBreakdown}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={entry => `${entry.category}: $${(entry.value/1000).toFixed(0)}k`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {trainingCostBreakdown.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => `$${Number(value).toLocaleString()}`} />
                  </RePieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>ROI Analysis</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm">Total Investment</span>
                    <span className="font-medium">$128,000</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Productivity Gains</span>
                    <span className="font-medium text-green-600">+$245,000</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Error Reduction Savings</span>
                    <span className="font-medium text-green-600">+$68,000</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Compliance Penalty Avoidance</span>
                    <span className="font-medium text-green-600">+$45,000</span>
                  </div>
                  <div className="pt-2 border-t">
                    <div className="flex justify-between">
                      <span className="font-medium">Net ROI</span>
                      <span className="font-bold text-green-600">245%</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="skills" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Skills Improvement Analysis</CardTitle>
              <CardDescription>Average skill levels before and after training</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={skillsImprovement}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="skill" />
                  <YAxis domain={[0, 5]} />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="before" fill="#ef4444" name="Before Training" />
                  <Bar dataKey="after" fill="#10b981" name="After Training" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Skills Gap Closure</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {['Technical', 'Leadership', 'Communication', 'Safety'].map((skill, idx) => {
                  const closure = [75, 82, 88, 95][idx];
                  return (
                    <div key={skill} className="space-y-1">
                      <div className="flex justify-between text-sm">
                        <span>{skill}</span>
                        <span className="font-medium">{closure}%</span>
                      </div>
                      <Progress value={closure} />
                    </div>
                  );
                })}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Critical Skills Status</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex items-center justify-between p-2 rounded bg-green-50">
                  <span className="text-sm">Safety Compliance</span>
                  <Badge variant="success">On Track</Badge>
                </div>
                <div className="flex items-center justify-between p-2 rounded bg-amber-50">
                  <span className="text-sm">Technical Expertise</span>
                  <Badge variant="warning">Needs Attention</Badge>
                </div>
                <div className="flex items-center justify-between p-2 rounded bg-green-50">
                  <span className="text-sm">Leadership Skills</span>
                  <Badge variant="success">On Track</Badge>
                </div>
                <div className="flex items-center justify-between p-2 rounded bg-red-50">
                  <span className="text-sm">Digital Skills</span>
                  <Badge variant="destructive">Critical Gap</Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};