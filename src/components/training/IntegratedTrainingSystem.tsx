import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  BookOpen,
  Users,
  Calendar,
  DollarSign,
  Award,
  Activity,
  BarChart3,
  Settings,
  Grid,
  Target,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertTriangle
} from 'lucide-react';
import { motion } from 'framer-motion';

// Import existing components
import { TrainingProvider, useTraining } from '@/contexts/TrainingContext';
import { TrainingContentStudio } from './TrainingContentStudio';
import { SkillsGapAnalysis } from './analysis/SkillsGapAnalysis';
import { TrainingBudgetManager } from './budget/TrainingBudgetManager';
import { CertificationTracker } from './certification/CertificationTracker';
import { ProgressTracker } from './progress/ProgressTracker';
import { TrainingScheduler } from './scheduling/TrainingScheduler';

// Import shared components
import { MetricCard, StatusBadge, ProgressIndicator, ChartContainer } from './shared';

// Import chart components
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  Area,
  AreaChart
} from 'recharts';

const IntegratedTrainingSystemContent: React.FC = () => {
  const { state } = useTraining();
  const [activeTab, setActiveTab] = useState('overview');

  // Mock data for overview metrics
  const overviewMetrics = {
    totalEmployees: 245,
    activeTrainees: 89,
    completedThisMonth: 34,
    upcomingCertifications: 12,
    budgetUtilized: 65,
    skillGapsClosed: 28,
    averageProgress: 73,
    complianceRate: 92
  };

  // Mock data for charts
  const trainingTrendsData = [
    { month: 'Jan', completed: 25, enrolled: 45, budget: 15000 },
    { month: 'Feb', completed: 32, enrolled: 52, budget: 18000 },
    { month: 'Mar', completed: 28, enrolled: 48, budget: 16500 },
    { month: 'Apr', completed: 41, enrolled: 58, budget: 22000 },
    { month: 'May', completed: 38, enrolled: 55, budget: 19500 },
    { month: 'Jun', completed: 45, enrolled: 62, budget: 25000 }
  ];

  const skillDistributionData = [
    { name: 'Technical', value: 35, color: '#3b82f6' },
    { name: 'Leadership', value: 25, color: '#10b981' },
    { name: 'Compliance', value: 20, color: '#f59e0b' },
    { name: 'Soft Skills', value: 20, color: '#8b5cf6' }
  ];

  const departmentProgressData = [
    { department: 'Engineering', progress: 85, target: 90 },
    { department: 'Sales', progress: 78, target: 85 },
    { department: 'HR', progress: 92, target: 95 },
    { department: 'Operations', progress: 71, target: 80 },
    { department: 'Marketing', progress: 88, target: 90 }
  ];

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-4">
        <MetricCard
          title="Active Trainees"
          value={overviewMetrics.activeTrainees}
          description="Currently in training"
          icon={Users}
          trend={{ value: 12, isPositive: true }}
        />
        <MetricCard
          title="Completed This Month"
          value={overviewMetrics.completedThisMonth}
          description="Successfully certified"
          icon={Award}
          trend={{ value: 8, isPositive: true }}
        />
        <MetricCard
          title="Budget Utilized"
          value={`${overviewMetrics.budgetUtilized}%`}
          description="Of allocated budget"
          icon={DollarSign}
          progress={overviewMetrics.budgetUtilized}
        />
        <MetricCard
          title="Compliance Rate"
          value={`${overviewMetrics.complianceRate}%`}
          description="Organization-wide"
          icon={CheckCircle}
          badge={{ text: 'Excellent', variant: 'success' }}
        />
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>Common training management tasks</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-4">
            <Button 
              variant="outline" 
              className="justify-start h-auto p-4"
              onClick={() => setActiveTab('content')}
            >
              <BookOpen className="h-5 w-5 mr-3" />
              <div className="text-left">
                <div className="font-medium">Create Content</div>
                <div className="text-xs text-muted-foreground">New training module</div>
              </div>
            </Button>
            <Button 
              variant="outline" 
              className="justify-start h-auto p-4"
              onClick={() => setActiveTab('schedule')}
            >
              <Calendar className="h-5 w-5 mr-3" />
              <div className="text-left">
                <div className="font-medium">Schedule Session</div>
                <div className="text-xs text-muted-foreground">Book training room</div>
              </div>
            </Button>
            <Button 
              variant="outline" 
              className="justify-start h-auto p-4"
              onClick={() => setActiveTab('skills')}
            >
              <Target className="h-5 w-5 mr-3" />
              <div className="text-left">
                <div className="font-medium">Analyze Skills</div>
                <div className="text-xs text-muted-foreground">Gap assessment</div>
              </div>
            </Button>
            <Button 
              variant="outline" 
              className="justify-start h-auto p-4"
              onClick={() => setActiveTab('budget')}
            >
              <DollarSign className="h-5 w-5 mr-3" />
              <div className="text-left">
                <div className="font-medium">Manage Budget</div>
                <div className="text-xs text-muted-foreground">Track expenses</div>
              </div>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Charts and Analytics */}
      <div className="grid gap-6 lg:grid-cols-2">
        <ChartContainer
          title="Training Trends"
          description="Monthly completion and enrollment trends"
          height={300}
        >
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart data={trainingTrendsData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Area 
                type="monotone" 
                dataKey="completed" 
                stackId="1"
                stroke="#10b981" 
                fill="#10b981" 
                name="Completed"
                fillOpacity={0.6}
              />
              <Area 
                type="monotone" 
                dataKey="enrolled" 
                stackId="2"
                stroke="#3b82f6" 
                fill="#3b82f6"
                name="Enrolled" 
                fillOpacity={0.4}
              />
            </AreaChart>
          </ResponsiveContainer>
        </ChartContainer>

        <ChartContainer
          title="Skills Distribution"
          description="Training focus areas"
          height={300}
        >
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={skillDistributionData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {skillDistributionData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>

      {/* Department Progress */}
      <Card>
        <CardHeader>
          <CardTitle>Department Progress</CardTitle>
          <CardDescription>Training completion by department</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {departmentProgressData.map((dept) => (
            <ProgressIndicator
              key={dept.department}
              current={dept.progress}
              total={dept.target}
              label={dept.department}
              showPercentage={true}
              variant={dept.progress >= dept.target ? 'success' : 
                      dept.progress >= dept.target * 0.8 ? 'warning' : 'danger'}
            />
          ))}
        </CardContent>
      </Card>

      {/* Recent Activities */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Activities</CardTitle>
          <CardDescription>Latest training system updates</CardDescription>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex items-center gap-3">
            <div className="h-2 w-2 bg-green-500 rounded-full"></div>
            <div className="flex-1">
              <p className="text-sm font-medium">Safety Training completed by John Smith</p>
              <p className="text-xs text-muted-foreground">2 hours ago</p>
            </div>
            <StatusBadge status="completed" />
          </div>
          <div className="flex items-center gap-3">
            <div className="h-2 w-2 bg-blue-500 rounded-full"></div>
            <div className="flex-1">
              <p className="text-sm font-medium">New Leadership Workshop scheduled</p>
              <p className="text-xs text-muted-foreground">4 hours ago</p>
            </div>
            <StatusBadge status="scheduled" />
          </div>
          <div className="flex items-center gap-3">
            <div className="h-2 w-2 bg-yellow-500 rounded-full"></div>
            <div className="flex-1">
              <p className="text-sm font-medium">Budget approval required for AWS Certification</p>
              <p className="text-xs text-muted-foreground">6 hours ago</p>
            </div>
            <StatusBadge status="pending" />
          </div>
          <div className="flex items-center gap-3">
            <div className="h-2 w-2 bg-red-500 rounded-full"></div>
            <div className="flex-1">
              <p className="text-sm font-medium">ISO 9001 certification expiring soon</p>
              <p className="text-xs text-muted-foreground">1 day ago</p>
            </div>
            <StatusBadge status="expiring_soon" />
          </div>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto p-6 space-y-6">
        {/* Header */}
        <motion.div 
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex justify-between items-center"
        >
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Training Management System</h1>
            <p className="text-muted-foreground">
              Comprehensive training, certification, and development platform
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline">
              <BarChart3 className="h-4 w-4 mr-2" />
              Reports
            </Button>
            <Button variant="outline">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
          </div>
        </motion.div>

        {/* Navigation Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-7 gap-2 h-auto p-1">
            <TabsTrigger value="overview" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">
              <Grid className="h-4 w-4 mr-2" />
              Overview
            </TabsTrigger>
            <TabsTrigger value="content">
              <BookOpen className="h-4 w-4 mr-2" />
              Content
            </TabsTrigger>
            <TabsTrigger value="schedule">
              <Calendar className="h-4 w-4 mr-2" />
              Schedule
            </TabsTrigger>
            <TabsTrigger value="progress">
              <Activity className="h-4 w-4 mr-2" />
              Progress
            </TabsTrigger>
            <TabsTrigger value="skills">
              <Target className="h-4 w-4 mr-2" />
              Skills
            </TabsTrigger>
            <TabsTrigger value="certifications">
              <Award className="h-4 w-4 mr-2" />
              Certifications
            </TabsTrigger>
            <TabsTrigger value="budget">
              <DollarSign className="h-4 w-4 mr-2" />
              Budget
            </TabsTrigger>
          </TabsList>

          <motion.div
            key={activeTab}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.2 }}
          >
            <TabsContent value="overview" className="mt-6">
              {renderOverview()}
            </TabsContent>

            <TabsContent value="content" className="mt-6">
              <TrainingContentStudio />
            </TabsContent>

            <TabsContent value="schedule" className="mt-6">
              <TrainingScheduler />
            </TabsContent>

            <TabsContent value="progress" className="mt-6">
              <ProgressTracker />
            </TabsContent>

            <TabsContent value="skills" className="mt-6">
              <SkillsGapAnalysis />
            </TabsContent>

            <TabsContent value="certifications" className="mt-6">
              <CertificationTracker />
            </TabsContent>

            <TabsContent value="budget" className="mt-6">
              <TrainingBudgetManager />
            </TabsContent>
          </motion.div>
        </Tabs>
      </div>
    </div>
  );
};

// Wrap with provider
export const IntegratedTrainingSystem: React.FC = () => {
  return (
    <TrainingProvider>
      <IntegratedTrainingSystemContent />
    </TrainingProvider>
  );
};

export default IntegratedTrainingSystem;