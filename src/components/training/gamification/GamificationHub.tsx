import React, { useState, useEffect } from 'react';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Trophy,
  Medal,
  Target,
  Zap,
  Flame,
  Star,
  Award,
  TrendingUp,
  Users,
  Crown,
  Gift,
  Lock,
  Unlock,
  Calendar,
  Activity,
  ChevronUp,
  ChevronDown,
  Sparkles,
  Heart
} from 'lucide-react';
import { format, differenceInDays } from 'date-fns';
import { GamificationProfile, Badge as BadgeType, Achievement, LearningStreak } from '../types';
import { useTraining } from '@/contexts/TrainingContext';
import { 
  RadialBar<PERSON>hart,
  RadialBar,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  AreaChart,
  Area
} from 'recharts';

export const GamificationHub: React.FC = () => {
  const { state, dispatch } = useTraining();
  const [profiles, setProfiles] = useState<GamificationProfile[]>([]);
  const [selectedEmployee, setSelectedEmployee] = useState<string>('emp-001');
  const [showAllBadges, setShowAllBadges] = useState(false);
  const [leaderboardTimeframe, setLeaderboardTimeframe] = useState<'week' | 'month' | 'all'>('month');

  // Mock data
  useEffect(() => {
    const mockProfiles: GamificationProfile[] = [
      {
        employeeId: 'emp-001',
        points: 2450,
        level: 12,
        rank: 2,
        badges: [
          {
            id: 'badge-1',
            name: 'Fast Learner',
            description: 'Complete 5 modules in one week',
            icon: '⚡',
            category: 'speed',
            earnedDate: new Date('2024-01-15'),
            rarity: 'rare'
          },
          {
            id: 'badge-2',
            name: 'Perfect Score',
            description: 'Achieve 100% on any assessment',
            icon: '🎯',
            category: 'performance',
            earnedDate: new Date('2024-01-10'),
            rarity: 'epic'
          },
          {
            id: 'badge-3',
            name: 'Team Player',
            description: 'Help 3 colleagues with their training',
            icon: '🤝',
            category: 'collaboration',
            earnedDate: new Date('2024-01-08'),
            rarity: 'common'
          },
          {
            id: 'badge-4',
            name: 'Early Bird',
            description: 'Start training before 7 AM',
            icon: '🌅',
            category: 'dedication',
            earnedDate: new Date('2024-01-05'),
            rarity: 'common'
          }
        ],
        achievements: [
          {
            id: 'ach-1',
            title: 'Safety Champion',
            description: 'Complete all safety certifications',
            points: 500,
            unlockedDate: new Date('2024-01-20'),
            progress: 100,
            requirement: 'Complete 5/5 safety modules'
          },
          {
            id: 'ach-2',
            title: 'Knowledge Seeker',
            description: 'Complete 20 training modules',
            points: 300,
            progress: 85,
            requirement: 'Complete 17/20 modules'
          },
          {
            id: 'ach-3',
            title: 'Consistency King',
            description: 'Maintain a 30-day learning streak',
            points: 400,
            progress: 60,
            requirement: '18/30 days'
          }
        ],
        streaks: [
          {
            type: 'daily',
            currentStreak: 18,
            bestStreak: 24,
            lastActivity: new Date()
          },
          {
            type: 'weekly',
            currentStreak: 7,
            bestStreak: 12,
            lastActivity: new Date()
          }
        ],
        leaderboardPosition: 2
      },
      {
        employeeId: 'emp-002',
        points: 2850,
        level: 14,
        rank: 1,
        badges: [
          {
            id: 'badge-5',
            name: 'Leadership Master',
            description: 'Complete all leadership courses',
            icon: '👑',
            category: 'leadership',
            earnedDate: new Date('2024-01-18'),
            rarity: 'legendary'
          },
          {
            id: 'badge-6',
            name: 'Mentor',
            description: 'Guide 5 new employees',
            icon: '🎓',
            category: 'mentorship',
            earnedDate: new Date('2024-01-12'),
            rarity: 'epic'
          }
        ],
        achievements: [],
        streaks: [
          {
            type: 'daily',
            currentStreak: 32,
            bestStreak: 32,
            lastActivity: new Date()
          }
        ],
        leaderboardPosition: 1
      },
      {
        employeeId: 'emp-003',
        points: 1950,
        level: 10,
        rank: 3,
        badges: [],
        achievements: [],
        streaks: [
          {
            type: 'daily',
            currentStreak: 5,
            bestStreak: 15,
            lastActivity: new Date()
          }
        ],
        leaderboardPosition: 3
      }
    ];

    setProfiles(mockProfiles);
  }, []);

  const getEmployeeName = (employeeId: string) => {
    const names: { [key: string]: string } = {
      'emp-001': 'John Smith',
      'emp-002': 'Sarah Johnson',
      'emp-003': 'Mike Wilson'
    };
    return names[employeeId] || employeeId;
  };

  const currentProfile = profiles.find(p => p.employeeId === selectedEmployee) || profiles[0];

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common': return 'bg-gray-500';
      case 'rare': return 'bg-blue-500';
      case 'epic': return 'bg-purple-500';
      case 'legendary': return 'bg-yellow-500';
      default: return 'bg-gray-400';
    }
  };

  const getLevelProgress = (points: number, level: number) => {
    const pointsForCurrentLevel = level * 200;
    const pointsForNextLevel = (level + 1) * 200;
    const progress = ((points - pointsForCurrentLevel) / (pointsForNextLevel - pointsForCurrentLevel)) * 100;
    return Math.min(Math.max(progress, 0), 100);
  };

  const allBadges = [
    { id: 'all-1', name: 'Night Owl', icon: '🦉', locked: true, rarity: 'rare' },
    { id: 'all-2', name: 'Speed Demon', icon: '🏃', locked: true, rarity: 'epic' },
    { id: 'all-3', name: 'Perfectionist', icon: '💎', locked: true, rarity: 'legendary' },
    { id: 'all-4', name: 'Explorer', icon: '🧭', locked: true, rarity: 'common' },
    { id: 'all-5', name: 'Innovator', icon: '💡', locked: false, rarity: 'rare' },
  ];

  const progressData = [
    { day: 'Mon', points: 120 },
    { day: 'Tue', points: 180 },
    { day: 'Wed', points: 150 },
    { day: 'Thu', points: 220 },
    { day: 'Fri', points: 280 },
    { day: 'Sat', points: 100 },
    { day: 'Sun', points: 150 },
  ];

  const leaderboard = profiles.sort((a, b) => b.points - a.points);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Training Gamification</h2>
          <p className="text-muted-foreground">
            Earn points, unlock badges, and compete with colleagues
          </p>
        </div>
        <Button>
          <Gift className="h-4 w-4 mr-2" />
          Redeem Rewards
        </Button>
      </div>

      {/* User Profile Overview */}
      {currentProfile && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Avatar className="h-16 w-16">
                  <AvatarFallback className="text-lg">
                    {getEmployeeName(currentProfile.employeeId).split(' ').map(n => n[0]).join('')}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <CardTitle className="text-2xl">
                    {getEmployeeName(currentProfile.employeeId)}
                  </CardTitle>
                  <CardDescription className="flex items-center gap-2">
                    <Trophy className="h-4 w-4 text-yellow-500" />
                    Rank #{currentProfile.rank} • Level {currentProfile.level}
                  </CardDescription>
                </div>
              </div>
              <div className="text-right">
                <div className="text-3xl font-bold">{currentProfile.points.toLocaleString()}</div>
                <p className="text-sm text-muted-foreground">Total Points</p>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <div className="flex justify-between text-sm mb-2">
                <span>Level {currentProfile.level}</span>
                <span>Level {currentProfile.level + 1}</span>
              </div>
              <Progress value={getLevelProgress(currentProfile.points, currentProfile.level)} />
              <p className="text-xs text-muted-foreground mt-1">
                {200 - (currentProfile.points % 200)} points to next level
              </p>
            </div>

            <div className="grid grid-cols-3 gap-4">
              <Card>
                <CardContent className="pt-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-2xl font-bold flex items-center gap-1">
                        <Flame className="h-5 w-5 text-orange-500" />
                        {currentProfile.streaks?.[0]?.currentStreak || 0}
                      </p>
                      <p className="text-xs text-muted-foreground">Day Streak</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="pt-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-2xl font-bold flex items-center gap-1">
                        <Medal className="h-5 w-5 text-purple-500" />
                        {currentProfile.badges.length}
                      </p>
                      <p className="text-xs text-muted-foreground">Badges Earned</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="pt-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-2xl font-bold flex items-center gap-1">
                        <Star className="h-5 w-5 text-yellow-500" />
                        {currentProfile.achievements.filter(a => a.unlockedDate).length}
                      </p>
                      <p className="text-xs text-muted-foreground">Achievements</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </CardContent>
        </Card>
      )}

      <Tabs defaultValue="badges">
        <TabsList>
          <TabsTrigger value="badges">Badges</TabsTrigger>
          <TabsTrigger value="achievements">Achievements</TabsTrigger>
          <TabsTrigger value="leaderboard">Leaderboard</TabsTrigger>
          <TabsTrigger value="progress">Progress</TabsTrigger>
        </TabsList>

        <TabsContent value="badges" className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold">Your Badge Collection</h3>
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => setShowAllBadges(!showAllBadges)}
            >
              {showAllBadges ? 'Show Earned' : 'Show All'}
            </Button>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {(showAllBadges ? allBadges : currentProfile?.badges || []).map(badge => {
              const isLocked = 'locked' in badge && badge.locked;
              return (
                <Card key={badge.id} className={isLocked ? 'opacity-50' : ''}>
                  <CardContent className="pt-6 text-center">
                    <div className="text-4xl mb-2">
                      {isLocked ? (
                        <Lock className="h-12 w-12 mx-auto text-muted-foreground" />
                      ) : (
                        <span>{('icon' in badge && badge.icon) || '🏆'}</span>
                      )}
                    </div>
                    <h4 className="font-semibold text-sm">{badge.name}</h4>
                    {'description' in badge && (
                      <p className="text-xs text-muted-foreground mt-1">
                        {badge.description}
                      </p>
                    )}
                    <div className="mt-2">
                      <Badge className={getRarityColor(badge.rarity)} variant="secondary">
                        {badge.rarity}
                      </Badge>
                    </div>
                    {'earnedDate' in badge && (
                      <p className="text-xs text-muted-foreground mt-2">
                        Earned {format(badge.earnedDate, 'PP')}
                      </p>
                    )}
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>

        <TabsContent value="achievements" className="space-y-4">
          <div className="space-y-4">
            {currentProfile?.achievements.map(achievement => (
              <Card key={achievement.id}>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div className="flex items-center gap-3">
                      {achievement.unlockedDate ? (
                        <Unlock className="h-8 w-8 text-green-500" />
                      ) : (
                        <Target className="h-8 w-8 text-muted-foreground" />
                      )}
                      <div>
                        <CardTitle className="text-lg">{achievement.title}</CardTitle>
                        <CardDescription>{achievement.description}</CardDescription>
                      </div>
                    </div>
                    <Badge variant="outline">
                      <Sparkles className="h-3 w-3 mr-1" />
                      {achievement.points} pts
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">{achievement.requirement}</span>
                      <span className="font-medium">{achievement.progress}%</span>
                    </div>
                    <Progress value={achievement.progress} />
                  </div>
                  {achievement.unlockedDate && (
                    <p className="text-sm text-green-600">
                      ✓ Completed on {format(achievement.unlockedDate, 'PP')}
                    </p>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Upcoming Achievements */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Next Achievements</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Activity className="h-4 w-4 text-blue-500" />
                  <span className="text-sm">Complete 5 more modules</span>
                </div>
                <Badge variant="outline">+200 pts</Badge>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-purple-500" />
                  <span className="text-sm">Maintain streak for 7 more days</span>
                </div>
                <Badge variant="outline">+150 pts</Badge>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Heart className="h-4 w-4 text-red-500" />
                  <span className="text-sm">Give feedback on 3 courses</span>
                </div>
                <Badge variant="outline">+100 pts</Badge>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="leaderboard" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Top Learners</CardTitle>
                <div className="flex gap-2">
                  <Button 
                    variant={leaderboardTimeframe === 'week' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setLeaderboardTimeframe('week')}
                  >
                    Week
                  </Button>
                  <Button 
                    variant={leaderboardTimeframe === 'month' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setLeaderboardTimeframe('month')}
                  >
                    Month
                  </Button>
                  <Button 
                    variant={leaderboardTimeframe === 'all' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setLeaderboardTimeframe('all')}
                  >
                    All Time
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {leaderboard.map((profile, index) => {
                  const isCurrentUser = profile.employeeId === selectedEmployee;
                  return (
                    <div 
                      key={profile.employeeId}
                      className={`flex items-center justify-between p-3 rounded-lg ${
                        isCurrentUser ? 'bg-primary/10 border border-primary' : 'bg-muted/50'
                      }`}
                    >
                      <div className="flex items-center gap-3">
                        <div className="flex items-center justify-center w-8 h-8 rounded-full bg-background font-bold">
                          {index === 0 && <Crown className="h-5 w-5 text-yellow-500" />}
                          {index === 1 && <Medal className="h-5 w-5 text-gray-400" />}
                          {index === 2 && <Medal className="h-5 w-5 text-amber-600" />}
                          {index > 2 && <span className="text-sm">{index + 1}</span>}
                        </div>
                        <Avatar>
                          <AvatarFallback>
                            {getEmployeeName(profile.employeeId).split(' ').map(n => n[0]).join('')}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="font-medium">{getEmployeeName(profile.employeeId)}</p>
                          <p className="text-sm text-muted-foreground">Level {profile.level}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-4">
                        {profile.streaks?.[0]?.currentStreak > 0 && (
                          <div className="flex items-center gap-1">
                            <Flame className="h-4 w-4 text-orange-500" />
                            <span className="text-sm font-medium">
                              {profile.streaks[0].currentStreak}
                            </span>
                          </div>
                        )}
                        <div className="text-right">
                          <p className="font-bold">{profile.points.toLocaleString()}</p>
                          <p className="text-xs text-muted-foreground">points</p>
                        </div>
                        {index < 3 && (
                          <ChevronUp className="h-4 w-4 text-green-500" />
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="progress" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Weekly Progress</CardTitle>
              <CardDescription>Points earned over the last 7 days</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={200}>
                <AreaChart data={progressData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="day" />
                  <YAxis />
                  <Tooltip />
                  <Area 
                    type="monotone" 
                    dataKey="points" 
                    stroke="#3b82f6" 
                    fill="#3b82f6" 
                    fillOpacity={0.3}
                  />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Activity Stats</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span>Total Time Spent</span>
                  <span className="font-medium">42h 15m</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Modules Completed</span>
                  <span className="font-medium">17</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Average Score</span>
                  <span className="font-medium">88%</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Certificates Earned</span>
                  <span className="font-medium">3</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Streak History</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Current Daily Streak</span>
                  <div className="flex items-center gap-2">
                    <Flame className="h-4 w-4 text-orange-500" />
                    <span className="font-bold">{currentProfile?.streaks?.[0]?.currentStreak || 0} days</span>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Best Daily Streak</span>
                  <div className="flex items-center gap-2">
                    <Trophy className="h-4 w-4 text-yellow-500" />
                    <span className="font-bold">{currentProfile?.streaks?.[0]?.bestStreak || 0} days</span>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Weekly Streak</span>
                  <div className="flex items-center gap-2">
                    <Zap className="h-4 w-4 text-blue-500" />
                    <span className="font-bold">{currentProfile?.streaks?.[1]?.currentStreak || 0} weeks</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};