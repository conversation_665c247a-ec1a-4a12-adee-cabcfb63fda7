import React, { useState, useEffect } from 'react';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Activity,
  Clock,
  Target,
  TrendingUp,
  Award,
  BookOpen,
  CheckCircle,
  XCircle,
  PlayCircle,
  PauseCircle,
  BarChart3,
  Calendar,
  User,
  Users,
  Zap
} from 'lucide-react';
import { format, differenceInDays } from 'date-fns';
import { LearningProgress, ModuleProgress } from '../types';
import { useTraining } from '@/contexts/TrainingContext';
import { 
  LineChart, 
  Line, 
  AreaChart,
  Area,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar
} from 'recharts';

export const ProgressTracker: React.FC = () => {
  const { state, dispatch } = useTraining();
  const [learningProgress, setLearningProgress] = useState<LearningProgress[]>([]);
  const [selectedEmployee, setSelectedEmployee] = useState<string>('all');
  const [selectedProgram, setSelectedProgram] = useState<string>('all');
  const [timeRange, setTimeRange] = useState<'week' | 'month' | 'quarter' | 'year'>('month');

  // Mock data
  useEffect(() => {
    const mockProgress: LearningProgress[] = [
      {
        id: 'prog-1',
        employeeId: 'emp-001',
        programId: 'training-1',
        enrollmentDate: new Date('2024-01-01'),
        startDate: new Date('2024-01-05'),
        progress: 75,
        status: 'in_progress',
        modules: [
          {
            moduleId: 'mod-1',
            moduleName: 'Introduction to Safety',
            completed: true,
            completionDate: new Date('2024-01-06'),
            score: 92,
            attempts: 1,
            timeSpent: 45
          },
          {
            moduleId: 'mod-2',
            moduleName: 'Risk Assessment',
            completed: true,
            completionDate: new Date('2024-01-08'),
            score: 88,
            attempts: 2,
            timeSpent: 60
          },
          {
            moduleId: 'mod-3',
            moduleName: 'Emergency Procedures',
            completed: false,
            attempts: 0,
            timeSpent: 30
          },
          {
            moduleId: 'mod-4',
            moduleName: 'Final Assessment',
            completed: false,
            attempts: 0,
            timeSpent: 0
          }
        ],
        assessmentScores: [92, 88],
        timeSpent: 135
      },
      {
        id: 'prog-2',
        employeeId: 'emp-002',
        programId: 'training-2',
        enrollmentDate: new Date('2024-01-10'),
        startDate: new Date('2024-01-12'),
        completionDate: new Date('2024-01-20'),
        progress: 100,
        status: 'completed',
        modules: [
          {
            moduleId: 'mod-5',
            moduleName: 'Leadership Fundamentals',
            completed: true,
            completionDate: new Date('2024-01-13'),
            score: 95,
            attempts: 1,
            timeSpent: 90
          },
          {
            moduleId: 'mod-6',
            moduleName: 'Team Management',
            completed: true,
            completionDate: new Date('2024-01-15'),
            score: 91,
            attempts: 1,
            timeSpent: 120
          },
          {
            moduleId: 'mod-7',
            moduleName: 'Communication Skills',
            completed: true,
            completionDate: new Date('2024-01-18'),
            score: 89,
            attempts: 1,
            timeSpent: 75
          }
        ],
        assessmentScores: [95, 91, 89],
        timeSpent: 285
      },
      {
        id: 'prog-3',
        employeeId: 'emp-003',
        programId: 'training-1',
        enrollmentDate: new Date('2024-01-15'),
        progress: 0,
        status: 'not_started',
        modules: [
          {
            moduleId: 'mod-1',
            moduleName: 'Introduction to Safety',
            completed: false,
            attempts: 0,
            timeSpent: 0
          }
        ],
        assessmentScores: [],
        timeSpent: 0
      },
      {
        id: 'prog-4',
        employeeId: 'emp-001',
        programId: 'training-3',
        enrollmentDate: new Date('2023-12-01'),
        startDate: new Date('2023-12-05'),
        progress: 40,
        status: 'in_progress',
        modules: [
          {
            moduleId: 'mod-8',
            moduleName: 'Technical Basics',
            completed: true,
            completionDate: new Date('2023-12-10'),
            score: 85,
            attempts: 2,
            timeSpent: 180
          },
          {
            moduleId: 'mod-9',
            moduleName: 'Advanced Concepts',
            completed: false,
            attempts: 1,
            timeSpent: 60
          }
        ],
        assessmentScores: [85],
        timeSpent: 240
      }
    ];

    setLearningProgress(mockProgress);
  }, []);

  const getEmployeeName = (employeeId: string) => {
    const names: { [key: string]: string } = {
      'emp-001': 'John Smith',
      'emp-002': 'Sarah Johnson',
      'emp-003': 'Mike Wilson'
    };
    return names[employeeId] || employeeId;
  };

  const getProgramName = (programId: string) => {
    const programs: { [key: string]: string } = {
      'training-1': 'Safety Certification',
      'training-2': 'Leadership Excellence',
      'training-3': 'Technical Skills Advanced'
    };
    return programs[programId] || programId;
  };

  const filteredProgress = learningProgress.filter(prog => {
    if (selectedEmployee !== 'all' && prog.employeeId !== selectedEmployee) return false;
    if (selectedProgram !== 'all' && prog.programId !== selectedProgram) return false;
    return true;
  });

  // Calculate metrics
  const totalEnrolled = learningProgress.length;
  const completed = learningProgress.filter(p => p.status === 'completed').length;
  const inProgress = learningProgress.filter(p => p.status === 'in_progress').length;
  const notStarted = learningProgress.filter(p => p.status === 'not_started').length;
  const avgProgress = learningProgress.reduce((acc, p) => acc + p.progress, 0) / totalEnrolled || 0;
  const avgScore = learningProgress
    .flatMap(p => p.assessmentScores)
    .reduce((acc, score, _, arr) => acc + score / arr.length, 0) || 0;

  // Progress over time data
  const progressData = [
    { month: 'Jan', completed: 12, inProgress: 8, notStarted: 5 },
    { month: 'Feb', completed: 18, inProgress: 10, notStarted: 3 },
    { month: 'Mar', completed: 25, inProgress: 12, notStarted: 2 },
    { month: 'Apr', completed: 32, inProgress: 15, notStarted: 4 },
  ];

  // Skills radar data
  const skillsData = [
    { skill: 'Technical', current: 75, target: 90 },
    { skill: 'Leadership', current: 82, target: 85 },
    { skill: 'Communication', current: 88, target: 90 },
    { skill: 'Safety', current: 92, target: 95 },
    { skill: 'Compliance', current: 95, target: 100 },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'success';
      case 'in_progress': return 'default';
      case 'not_started': return 'secondary';
      case 'dropped': return 'destructive';
      default: return 'outline';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Learning Progress Tracker</h2>
          <p className="text-muted-foreground">
            Monitor employee training progress and performance
          </p>
        </div>
        <Button>
          <BarChart3 className="h-4 w-4 mr-2" />
          Export Report
        </Button>
      </div>

      {/* Metrics Cards */}
      <div className="grid gap-4 md:grid-cols-5">
        <Card>
          <CardHeader className="pb-2">
            <CardDescription className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Total Enrolled
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalEnrolled}</div>
            <p className="text-xs text-muted-foreground">Active learners</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardDescription className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              Completed
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{completed}</div>
            <p className="text-xs text-muted-foreground">
              {((completed / totalEnrolled) * 100).toFixed(0)}% completion rate
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardDescription className="flex items-center gap-2">
              <PlayCircle className="h-4 w-4 text-blue-500" />
              In Progress
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{inProgress}</div>
            <p className="text-xs text-muted-foreground">Currently learning</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardDescription className="flex items-center gap-2">
              <Target className="h-4 w-4" />
              Avg Progress
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{avgProgress.toFixed(0)}%</div>
            <Progress value={avgProgress} className="mt-2 h-1" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardDescription className="flex items-center gap-2">
              <Award className="h-4 w-4 text-yellow-500" />
              Avg Score
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{avgScore.toFixed(0)}%</div>
            <p className="text-xs text-muted-foreground">Assessment average</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex gap-4">
        <Select value={selectedEmployee} onValueChange={setSelectedEmployee}>
          <SelectTrigger className="w-[200px]">
            <SelectValue placeholder="All Employees" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Employees</SelectItem>
            <SelectItem value="emp-001">John Smith</SelectItem>
            <SelectItem value="emp-002">Sarah Johnson</SelectItem>
            <SelectItem value="emp-003">Mike Wilson</SelectItem>
          </SelectContent>
        </Select>

        <Select value={selectedProgram} onValueChange={setSelectedProgram}>
          <SelectTrigger className="w-[200px]">
            <SelectValue placeholder="All Programs" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Programs</SelectItem>
            <SelectItem value="training-1">Safety Certification</SelectItem>
            <SelectItem value="training-2">Leadership Excellence</SelectItem>
            <SelectItem value="training-3">Technical Skills Advanced</SelectItem>
          </SelectContent>
        </Select>

        <Select value={timeRange} onValueChange={(v: any) => setTimeRange(v)}>
          <SelectTrigger className="w-[150px]">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="week">This Week</SelectItem>
            <SelectItem value="month">This Month</SelectItem>
            <SelectItem value="quarter">This Quarter</SelectItem>
            <SelectItem value="year">This Year</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <Tabs defaultValue="individual">
        <TabsList>
          <TabsTrigger value="individual">Individual Progress</TabsTrigger>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="individual" className="space-y-4">
          {filteredProgress.map(progress => {
            const completedModules = progress.modules.filter(m => m.completed).length;
            const totalModules = progress.modules.length;
            const daysSinceEnrollment = differenceInDays(new Date(), progress.enrollmentDate);
            
            return (
              <Card key={progress.id}>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div className="flex items-center gap-3">
                      <Avatar>
                        <AvatarFallback>
                          {getEmployeeName(progress.employeeId).split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <CardTitle className="text-lg">
                          {getEmployeeName(progress.employeeId)}
                        </CardTitle>
                        <CardDescription>
                          {getProgramName(progress.programId)}
                        </CardDescription>
                      </div>
                    </div>
                    <Badge variant={getStatusColor(progress.status) as any}>
                      {progress.status.replace('_', ' ')}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Overall Progress</span>
                      <span className="font-medium">{progress.progress}%</span>
                    </div>
                    <Progress value={progress.progress} />
                  </div>

                  <div className="grid grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground flex items-center gap-1">
                        <BookOpen className="h-3 w-3" />
                        Modules
                      </span>
                      <p className="font-medium">{completedModules}/{totalModules}</p>
                    </div>
                    <div>
                      <span className="text-muted-foreground flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        Time Spent
                      </span>
                      <p className="font-medium">{Math.round(progress.timeSpent / 60)}h {progress.timeSpent % 60}m</p>
                    </div>
                    <div>
                      <span className="text-muted-foreground flex items-center gap-1">
                        <Target className="h-3 w-3" />
                        Avg Score
                      </span>
                      <p className="font-medium">
                        {progress.assessmentScores.length > 0 
                          ? Math.round(progress.assessmentScores.reduce((a, b) => a + b, 0) / progress.assessmentScores.length)
                          : 'N/A'}%
                      </p>
                    </div>
                    <div>
                      <span className="text-muted-foreground flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        Days Active
                      </span>
                      <p className="font-medium">{daysSinceEnrollment} days</p>
                    </div>
                  </div>

                  {/* Module Details */}
                  <div className="space-y-2">
                    <p className="text-sm font-medium">Module Progress</p>
                    {progress.modules.map(module => (
                      <div key={module.moduleId} className="flex items-center justify-between text-sm">
                        <div className="flex items-center gap-2">
                          {module.completed ? (
                            <CheckCircle className="h-4 w-4 text-green-500" />
                          ) : (
                            <XCircle className="h-4 w-4 text-muted-foreground" />
                          )}
                          <span className={module.completed ? '' : 'text-muted-foreground'}>
                            {module.moduleName}
                          </span>
                        </div>
                        <div className="flex items-center gap-4 text-muted-foreground">
                          {module.score && (
                            <span className="font-medium">{module.score}%</span>
                          )}
                          <span>{module.timeSpent}m</span>
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="flex gap-2 pt-2">
                    <Button size="sm" variant="outline">
                      View Details
                    </Button>
                    {progress.status === 'in_progress' && (
                      <Button size="sm" variant="outline">
                        Send Reminder
                      </Button>
                    )}
                    {progress.status === 'not_started' && (
                      <Button size="sm">
                        <Zap className="h-3 w-3 mr-1" />
                        Nudge to Start
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </TabsContent>

        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Progress Trends</CardTitle>
              <CardDescription>Training completion over time</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <AreaChart data={progressData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip />
                  <Area 
                    type="monotone" 
                    dataKey="completed" 
                    stackId="1"
                    stroke="#10b981" 
                    fill="#10b981" 
                    name="Completed"
                  />
                  <Area 
                    type="monotone" 
                    dataKey="inProgress" 
                    stackId="1"
                    stroke="#3b82f6" 
                    fill="#3b82f6"
                    name="In Progress" 
                  />
                  <Area 
                    type="monotone" 
                    dataKey="notStarted" 
                    stackId="1"
                    stroke="#94a3b8" 
                    fill="#94a3b8"
                    name="Not Started" 
                  />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Skills Development</CardTitle>
              <CardDescription>Current vs target skill levels</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <RadarChart data={skillsData}>
                  <PolarGrid />
                  <PolarAngleAxis dataKey="skill" />
                  <PolarRadiusAxis angle={90} domain={[0, 100]} />
                  <Radar 
                    name="Current" 
                    dataKey="current" 
                    stroke="#3b82f6" 
                    fill="#3b82f6" 
                    fillOpacity={0.6} 
                  />
                  <Radar 
                    name="Target" 
                    dataKey="target" 
                    stroke="#10b981" 
                    fill="#10b981" 
                    fillOpacity={0.3} 
                  />
                  <Tooltip />
                </RadarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Completion Rate by Program</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {['Safety Certification', 'Leadership Excellence', 'Technical Skills'].map((program, idx) => {
                  const rate = [85, 92, 78][idx];
                  return (
                    <div key={program} className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>{program}</span>
                        <span className="font-medium">{rate}%</span>
                      </div>
                      <Progress value={rate} />
                    </div>
                  );
                })}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Time to Completion</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Average</span>
                    <span className="font-medium">14 days</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Fastest</span>
                    <span className="font-medium text-green-600">7 days</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Slowest</span>
                    <span className="font-medium text-amber-600">32 days</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Engagement Metrics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span>Daily Active Learners</span>
                  <span className="font-medium">42</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Weekly Active Learners</span>
                  <span className="font-medium">128</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Avg Session Duration</span>
                  <span className="font-medium">45 min</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Assessment Performance</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span>First Attempt Pass Rate</span>
                  <span className="font-medium">78%</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Average Score</span>
                  <span className="font-medium">86%</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Improvement Rate</span>
                  <span className="font-medium text-green-600">+12%</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};