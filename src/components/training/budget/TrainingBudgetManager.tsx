import React, { useState, useEffect } from 'react';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  DollarSign,
  TrendingUp,
  TrendingDown,
  PieChart,
  AlertCircle,
  CheckCircle,
  Clock,
  FileText,
  Plus,
  Calculator,
  Wallet,
  CreditCard,
  ArrowUpRight,
  ArrowDownRight
} from 'lucide-react';
import { format } from 'date-fns';
import { TrainingBudget, BudgetRequest, BudgetCategory } from '../types';
import { useTraining } from '@/contexts/TrainingContext';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart as RePieChart,
  Pie,
  Cell,
  Legend
} from 'recharts';

export const TrainingBudgetManager: React.FC = () => {
  const { state, dispatch } = useTraining();
  const [budgets, setBudgets] = useState<TrainingBudget[]>([]);
  const [requests, setRequests] = useState<BudgetRequest[]>([]);
  const [selectedYear, setSelectedYear] = useState<number>(new Date().getFullYear());
  const [selectedDepartment, setSelectedDepartment] = useState<string>('all');

  // Mock data
  useEffect(() => {
    const mockBudgets: TrainingBudget[] = [
      {
        id: 'budget-1',
        year: 2024,
        department: 'Engineering',
        totalAllocated: 150000,
        spent: 65000,
        committed: 25000,
        available: 60000,
        categories: [
          { name: 'Technical Training', allocated: 60000, spent: 30000, percentage: 40 },
          { name: 'Leadership Development', allocated: 40000, spent: 15000, percentage: 27 },
          { name: 'Compliance', allocated: 30000, spent: 12000, percentage: 20 },
          { name: 'Soft Skills', allocated: 20000, spent: 8000, percentage: 13 }
        ],
        approvalThreshold: 5000
      },
      {
        id: 'budget-2',
        year: 2024,
        department: 'Sales',
        totalAllocated: 100000,
        spent: 45000,
        committed: 15000,
        available: 40000,
        categories: [
          { name: 'Sales Training', allocated: 50000, spent: 25000, percentage: 50 },
          { name: 'Product Knowledge', allocated: 30000, spent: 12000, percentage: 30 },
          { name: 'Customer Service', allocated: 20000, spent: 8000, percentage: 20 }
        ],
        approvalThreshold: 3000
      },
      {
        id: 'budget-3',
        year: 2024,
        department: 'HR',
        totalAllocated: 80000,
        spent: 35000,
        committed: 10000,
        available: 35000,
        categories: [
          { name: 'Compliance', allocated: 40000, spent: 20000, percentage: 50 },
          { name: 'Leadership', allocated: 25000, spent: 10000, percentage: 31 },
          { name: 'Systems Training', allocated: 15000, spent: 5000, percentage: 19 }
        ],
        approvalThreshold: 2500
      }
    ];

    const mockRequests: BudgetRequest[] = [
      {
        id: 'req-1',
        requestorId: 'emp-001',
        trainingProgramId: 'prog-1',
        amount: 3500,
        justification: 'AWS certification required for cloud migration project',
        status: 'pending',
        comments: 'Critical for Q2 objectives'
      },
      {
        id: 'req-2',
        requestorId: 'emp-002',
        trainingProgramId: 'prog-2',
        amount: 12000,
        justification: 'Leadership training for newly promoted managers',
        status: 'approved',
        approvedBy: 'manager-001',
        approvalDate: new Date('2024-01-15'),
        comments: 'Approved for Q1 cohort'
      },
      {
        id: 'req-3',
        requestorId: 'emp-003',
        trainingProgramId: 'prog-3',
        amount: 8500,
        justification: 'Compliance training for new regulations',
        status: 'pending',
        comments: 'Mandatory by Q2'
      }
    ];

    setBudgets(mockBudgets);
    setRequests(mockRequests);
  }, []);

  const getTotalBudget = () => {
    const filtered = selectedDepartment === 'all' 
      ? budgets 
      : budgets.filter(b => b.department === selectedDepartment);
    
    return filtered.reduce((acc, budget) => ({
      allocated: acc.allocated + budget.totalAllocated,
      spent: acc.spent + budget.spent,
      committed: acc.committed + budget.committed,
      available: acc.available + budget.available
    }), { allocated: 0, spent: 0, committed: 0, available: 0 });
  };

  const totals = getTotalBudget();
  const utilizationRate = totals.allocated > 0 
    ? ((totals.spent + totals.committed) / totals.allocated * 100).toFixed(1)
    : '0';

  const categoryData = budgets
    .filter(b => selectedDepartment === 'all' || b.department === selectedDepartment)
    .flatMap(b => b.categories)
    .reduce((acc, cat) => {
      const existing = acc.find(c => c.name === cat.name);
      if (existing) {
        existing.allocated += cat.allocated;
        existing.spent += cat.spent;
      } else {
        acc.push({ ...cat });
      }
      return acc;
    }, [] as BudgetCategory[]);

  const COLORS = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'];

  const handleApproveRequest = (requestId: string) => {
    setRequests(requests.map(req => 
      req.id === requestId 
        ? { ...req, status: 'approved' as const, approvalDate: new Date(), approvedBy: 'current-user' }
        : req
    ));
  };

  const handleRejectRequest = (requestId: string) => {
    setRequests(requests.map(req => 
      req.id === requestId 
        ? { ...req, status: 'rejected' as const }
        : req
    ));
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Training Budget Management</h2>
          <p className="text-muted-foreground">
            Monitor and manage training budget allocation and spending
          </p>
        </div>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          New Budget Request
        </Button>
      </div>

      {/* Budget Overview Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardDescription className="flex items-center gap-2">
              <Wallet className="h-4 w-4" />
              Total Allocated
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${totals.allocated.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">Annual budget</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardDescription className="flex items-center gap-2">
              <CreditCard className="h-4 w-4" />
              Spent
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${totals.spent.toLocaleString()}</div>
            <div className="flex items-center text-xs text-muted-foreground">
              <ArrowUpRight className="h-3 w-3 text-red-500" />
              {((totals.spent / totals.allocated) * 100).toFixed(0)}% of budget
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardDescription className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Committed
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${totals.committed.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">Pending expenses</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardDescription className="flex items-center gap-2">
              <DollarSign className="h-4 w-4" />
              Available
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              ${totals.available.toLocaleString()}
            </div>
            <div className="flex items-center text-xs text-muted-foreground">
              <ArrowDownRight className="h-3 w-3 text-green-500" />
              {((totals.available / totals.allocated) * 100).toFixed(0)}% remaining
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Budget Utilization Alert */}
      {parseFloat(utilizationRate) > 80 && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Budget utilization is at {utilizationRate}%. Consider reviewing upcoming training requests carefully.
          </AlertDescription>
        </Alert>
      )}

      <div className="flex gap-4 mb-4">
        <Select value={selectedDepartment} onValueChange={setSelectedDepartment}>
          <SelectTrigger className="w-[200px]">
            <SelectValue placeholder="All Departments" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Departments</SelectItem>
            <SelectItem value="Engineering">Engineering</SelectItem>
            <SelectItem value="Sales">Sales</SelectItem>
            <SelectItem value="HR">HR</SelectItem>
            <SelectItem value="Operations">Operations</SelectItem>
          </SelectContent>
        </Select>

        <Select value={selectedYear.toString()} onValueChange={v => setSelectedYear(parseInt(v))}>
          <SelectTrigger className="w-[150px]">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="2024">2024</SelectItem>
            <SelectItem value="2023">2023</SelectItem>
            <SelectItem value="2022">2022</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <Tabs defaultValue="overview">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="departments">By Department</TabsTrigger>
          <TabsTrigger value="categories">By Category</TabsTrigger>
          <TabsTrigger value="requests">Approval Requests</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Budget Distribution</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={categoryData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip formatter={(value) => `$${Number(value).toLocaleString()}`} />
                  <Bar dataKey="allocated" fill="#3b82f6" name="Allocated" />
                  <Bar dataKey="spent" fill="#10b981" name="Spent" />
                  <Legend />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Category Breakdown</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <RePieChart>
                  <Pie
                    data={categoryData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={entry => entry.name}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="allocated"
                  >
                    {categoryData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => `$${Number(value).toLocaleString()}`} />
                </RePieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="departments" className="space-y-4">
          {budgets.map(budget => (
            <Card key={budget.id}>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle>{budget.department}</CardTitle>
                    <CardDescription>
                      FY {budget.year} • Approval threshold: ${budget.approvalThreshold.toLocaleString()}
                    </CardDescription>
                  </div>
                  <Badge variant="outline">
                    {((budget.spent / budget.totalAllocated) * 100).toFixed(0)}% utilized
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="text-muted-foreground">Allocated</span>
                    <p className="font-medium">${budget.totalAllocated.toLocaleString()}</p>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Spent</span>
                    <p className="font-medium">${budget.spent.toLocaleString()}</p>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Committed</span>
                    <p className="font-medium">${budget.committed.toLocaleString()}</p>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Available</span>
                    <p className="font-medium text-green-600">
                      ${budget.available.toLocaleString()}
                    </p>
                  </div>
                </div>

                <Progress 
                  value={(budget.spent + budget.committed) / budget.totalAllocated * 100} 
                />

                <div className="space-y-2">
                  {budget.categories.map(cat => (
                    <div key={cat.name} className="flex justify-between items-center text-sm">
                      <span>{cat.name}</span>
                      <div className="flex items-center gap-4">
                        <span className="text-muted-foreground">
                          ${cat.spent.toLocaleString()} / ${cat.allocated.toLocaleString()}
                        </span>
                        <Progress 
                          value={cat.spent / cat.allocated * 100} 
                          className="w-20"
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="categories" className="space-y-4">
          {categoryData.map((category, idx) => (
            <Card key={category.name}>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <CardTitle className="text-lg">{category.name}</CardTitle>
                  <Badge style={{ backgroundColor: COLORS[idx % COLORS.length] }}>
                    {((category.spent / category.allocated) * 100).toFixed(0)}% spent
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Budget Allocated</span>
                    <span className="font-medium">${category.allocated.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Amount Spent</span>
                    <span className="font-medium">${category.spent.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Remaining</span>
                    <span className="font-medium text-green-600">
                      ${(category.allocated - category.spent).toLocaleString()}
                    </span>
                  </div>
                  <Progress value={category.spent / category.allocated * 100} />
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="requests" className="space-y-4">
          {requests.map(request => (
            <Card key={request.id}>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-lg">
                      Training Program #{request.trainingProgramId}
                    </CardTitle>
                    <CardDescription>
                      Requested by {request.requestorId} • ${request.amount.toLocaleString()}
                    </CardDescription>
                  </div>
                  <Badge variant={
                    request.status === 'approved' ? 'success' :
                    request.status === 'rejected' ? 'destructive' :
                    request.status === 'on_hold' ? 'secondary' : 'default'
                  }>
                    {request.status}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <p className="text-sm font-medium mb-1">Justification</p>
                  <p className="text-sm text-muted-foreground">{request.justification}</p>
                </div>

                {request.comments && (
                  <div>
                    <p className="text-sm font-medium mb-1">Comments</p>
                    <p className="text-sm text-muted-foreground">{request.comments}</p>
                  </div>
                )}

                {request.approvalDate && (
                  <div className="text-sm text-muted-foreground">
                    Approved by {request.approvedBy} on {format(request.approvalDate, 'PP')}
                  </div>
                )}

                {request.status === 'pending' && (
                  <div className="flex gap-2 pt-2">
                    <Button 
                      size="sm" 
                      onClick={() => handleApproveRequest(request.id)}
                    >
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Approve
                    </Button>
                    <Button 
                      size="sm" 
                      variant="destructive"
                      onClick={() => handleRejectRequest(request.id)}
                    >
                      Reject
                    </Button>
                    <Button size="sm" variant="outline">
                      Request Info
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </TabsContent>
      </Tabs>
    </div>
  );
};