import React, { useState, useEffect } from 'react';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Award,
  Clock,
  AlertTriangle,
  CheckCircle,
  Calendar,
  DollarSign,
  FileText,
  Download,
  Upload,
  RefreshCw,
  Bell,
  TrendingUp,
  Users,
  Shield,
  AlertCircle,
  Plus
} from 'lucide-react';
import { format, differenceInDays, addMonths } from 'date-fns';
import { Certification, EmployeeCertification, ComplianceStatus } from '../types';
import { useTraining } from '@/contexts/TrainingContext';

export const CertificationTracker: React.FC = () => {
  const { state, dispatch } = useTraining();
  const [certifications, setCertifications] = useState<Certification[]>([]);
  const [employeeCerts, setEmployeeCerts] = useState<EmployeeCertification[]>([]);
  const [selectedEmployee, setSelectedEmployee] = useState<string>('all');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [showAddCert, setShowAddCert] = useState(false);

  // Mock data for demonstration
  useEffect(() => {
    const mockCertifications: Certification[] = [
      {
        id: 'cert-1',
        name: 'ISO 9001 Lead Auditor',
        issuingOrganization: 'International Organization for Standardization',
        category: 'compliance',
        validityPeriod: 36,
        renewalRequired: true,
        cost: 2500,
        requirements: ['Complete training course', 'Pass examination', '3 years experience']
      },
      {
        id: 'cert-2',
        name: 'Certified Safety Professional (CSP)',
        issuingOrganization: 'Board of Certified Safety Professionals',
        category: 'safety',
        validityPeriod: 60,
        renewalRequired: true,
        cost: 1800,
        requirements: ['Bachelor degree', '4 years safety experience', 'Pass CSP exam']
      },
      {
        id: 'cert-3',
        name: 'Project Management Professional (PMP)',
        issuingOrganization: 'Project Management Institute',
        category: 'professional',
        validityPeriod: 36,
        renewalRequired: true,
        cost: 555,
        requirements: ['35 hours training', '3 years project management experience', 'Pass PMP exam']
      },
      {
        id: 'cert-4',
        name: 'AWS Solutions Architect',
        issuingOrganization: 'Amazon Web Services',
        category: 'technical',
        validityPeriod: 36,
        renewalRequired: true,
        cost: 300,
        requirements: ['Cloud computing knowledge', 'AWS experience', 'Pass certification exam']
      },
      {
        id: 'cert-5',
        name: 'First Aid & CPR',
        issuingOrganization: 'Red Cross',
        category: 'safety',
        validityPeriod: 24,
        renewalRequired: true,
        cost: 150,
        requirements: ['Complete training course', 'Practical demonstration']
      }
    ];

    const mockEmployeeCerts: EmployeeCertification[] = [
      {
        id: 'emp-cert-1',
        employeeId: 'emp-001',
        certificationId: 'cert-1',
        issueDate: new Date('2023-01-15'),
        expiryDate: new Date('2026-01-15'),
        status: 'active',
        certificateUrl: '/certs/iso9001-john.pdf',
        renewalReminders: [new Date('2025-10-15'), new Date('2025-12-15')]
      },
      {
        id: 'emp-cert-2',
        employeeId: 'emp-002',
        certificationId: 'cert-2',
        issueDate: new Date('2022-06-01'),
        expiryDate: new Date('2024-06-01'),
        status: 'expiring_soon',
        certificateUrl: '/certs/csp-sarah.pdf',
        renewalReminders: [new Date('2024-03-01'), new Date('2024-05-01')]
      },
      {
        id: 'emp-cert-3',
        employeeId: 'emp-003',
        certificationId: 'cert-3',
        issueDate: new Date('2021-09-15'),
        expiryDate: new Date('2024-09-15'),
        status: 'active',
        renewalReminders: [new Date('2024-06-15'), new Date('2024-08-15')]
      },
      {
        id: 'emp-cert-4',
        employeeId: 'emp-001',
        certificationId: 'cert-4',
        issueDate: new Date('2020-11-01'),
        expiryDate: new Date('2023-11-01'),
        status: 'expired',
        renewalReminders: []
      },
      {
        id: 'emp-cert-5',
        employeeId: 'emp-004',
        certificationId: 'cert-5',
        issueDate: new Date('2023-03-01'),
        expiryDate: new Date('2025-03-01'),
        status: 'active',
        certificateUrl: '/certs/firstaid-mike.pdf',
        renewalReminders: [new Date('2025-01-01')]
      },
      {
        id: 'emp-cert-6',
        employeeId: 'emp-005',
        certificationId: 'cert-1',
        issueDate: new Date('2023-07-01'),
        expiryDate: new Date('2026-07-01'),
        status: 'active',
        renewalReminders: [new Date('2026-04-01')]
      }
    ];

    setCertifications(mockCertifications);
    setEmployeeCerts(mockEmployeeCerts);
  }, []);

  const getEmployeeName = (employeeId: string) => {
    const names: { [key: string]: string } = {
      'emp-001': 'John Smith',
      'emp-002': 'Sarah Johnson',
      'emp-003': 'Mike Wilson',
      'emp-004': 'Lisa Brown',
      'emp-005': 'David Lee'
    };
    return names[employeeId] || employeeId;
  };

  const getCertificationName = (certId: string) => {
    const cert = certifications.find(c => c.id === certId);
    return cert?.name || certId;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success';
      case 'expiring_soon': return 'warning';
      case 'expired': return 'destructive';
      case 'pending_renewal': return 'secondary';
      default: return 'default';
    }
  };

  const getDaysUntilExpiry = (expiryDate: Date) => {
    return differenceInDays(expiryDate, new Date());
  };

  const filteredCerts = employeeCerts.filter(cert => {
    if (selectedEmployee !== 'all' && cert.employeeId !== selectedEmployee) return false;
    if (filterStatus !== 'all' && cert.status !== filterStatus) return false;
    return true;
  });

  const upcomingRenewals = employeeCerts.filter(cert => {
    const daysUntil = getDaysUntilExpiry(cert.expiryDate);
    return daysUntil > 0 && daysUntil <= 90;
  });

  const complianceRate = (
    (employeeCerts.filter(c => c.status === 'active').length / employeeCerts.length) * 100
  ).toFixed(1);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Certification Tracking</h2>
          <p className="text-muted-foreground">
            Monitor employee certifications and compliance requirements
          </p>
        </div>
        <Button onClick={() => setShowAddCert(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Add Certification
        </Button>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardDescription className="flex items-center gap-2">
              <Award className="h-4 w-4" />
              Total Certifications
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{employeeCerts.length}</div>
            <p className="text-xs text-muted-foreground">
              Across {certifications.length} types
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardDescription className="flex items-center gap-2">
              <Shield className="h-4 w-4" />
              Compliance Rate
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{complianceRate}%</div>
            <Progress value={parseFloat(complianceRate)} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardDescription className="flex items-center gap-2">
              <AlertTriangle className="h-4 w-4 text-amber-500" />
              Expiring Soon
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{upcomingRenewals.length}</div>
            <p className="text-xs text-muted-foreground">Next 90 days</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardDescription className="flex items-center gap-2">
              <AlertCircle className="h-4 w-4 text-red-500" />
              Expired
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {employeeCerts.filter(c => c.status === 'expired').length}
            </div>
            <p className="text-xs text-muted-foreground">Needs renewal</p>
          </CardContent>
        </Card>
      </div>

      {/* Alerts */}
      {upcomingRenewals.length > 0 && (
        <Alert>
          <Bell className="h-4 w-4" />
          <AlertDescription>
            <strong>{upcomingRenewals.length} certifications</strong> are expiring in the next 90 days. 
            Review and schedule renewals to maintain compliance.
          </AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="overview">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="employees">By Employee</TabsTrigger>
          <TabsTrigger value="certifications">By Certification</TabsTrigger>
          <TabsTrigger value="compliance">Compliance</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="flex gap-4 mb-4">
            <Select value={selectedEmployee} onValueChange={setSelectedEmployee}>
              <SelectTrigger className="w-[200px]">
                <SelectValue placeholder="All Employees" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Employees</SelectItem>
                <SelectItem value="emp-001">John Smith</SelectItem>
                <SelectItem value="emp-002">Sarah Johnson</SelectItem>
                <SelectItem value="emp-003">Mike Wilson</SelectItem>
                <SelectItem value="emp-004">Lisa Brown</SelectItem>
                <SelectItem value="emp-005">David Lee</SelectItem>
              </SelectContent>
            </Select>

            <Select value={filterStatus} onValueChange={setFilterStatus}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="All Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="expiring_soon">Expiring Soon</SelectItem>
                <SelectItem value="expired">Expired</SelectItem>
                <SelectItem value="pending_renewal">Pending Renewal</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="grid gap-4">
            {filteredCerts.map(cert => {
              const certification = certifications.find(c => c.id === cert.certificationId);
              const daysUntil = getDaysUntilExpiry(cert.expiryDate);
              
              return (
                <Card key={cert.id}>
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="text-lg">
                          {getCertificationName(cert.certificationId)}
                        </CardTitle>
                        <CardDescription>
                          {getEmployeeName(cert.employeeId)} • {certification?.issuingOrganization}
                        </CardDescription>
                      </div>
                      <Badge variant={getStatusColor(cert.status) as any}>
                        {cert.status.replace('_', ' ')}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">Issue Date</span>
                        <p className="font-medium">{format(cert.issueDate, 'PP')}</p>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Expiry Date</span>
                        <p className="font-medium">{format(cert.expiryDate, 'PP')}</p>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Time Remaining</span>
                        <p className="font-medium">
                          {daysUntil > 0 ? `${daysUntil} days` : 'Expired'}
                        </p>
                      </div>
                    </div>

                    {daysUntil > 0 && daysUntil <= 90 && (
                      <Alert>
                        <AlertTriangle className="h-4 w-4" />
                        <AlertDescription>
                          This certification expires in {daysUntil} days. Schedule renewal training.
                        </AlertDescription>
                      </Alert>
                    )}

                    <div className="flex gap-2">
                      {cert.certificateUrl && (
                        <Button size="sm" variant="outline">
                          <Download className="h-3 w-3 mr-1" />
                          Download Certificate
                        </Button>
                      )}
                      <Button size="sm" variant="outline">
                        <RefreshCw className="h-3 w-3 mr-1" />
                        Schedule Renewal
                      </Button>
                      <Button size="sm" variant="outline">
                        <Bell className="h-3 w-3 mr-1" />
                        Set Reminder
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>

        <TabsContent value="employees" className="space-y-4">
          <div className="grid gap-4">
            {['emp-001', 'emp-002', 'emp-003', 'emp-004', 'emp-005'].map(empId => {
              const empCerts = employeeCerts.filter(c => c.employeeId === empId);
              const activeCerts = empCerts.filter(c => c.status === 'active').length;
              const expiredCerts = empCerts.filter(c => c.status === 'expired').length;
              
              return (
                <Card key={empId}>
                  <CardHeader>
                    <div className="flex justify-between items-center">
                      <div>
                        <CardTitle>{getEmployeeName(empId)}</CardTitle>
                        <CardDescription>
                          {empCerts.length} certifications
                        </CardDescription>
                      </div>
                      <div className="flex gap-2">
                        <Badge variant="success">{activeCerts} Active</Badge>
                        {expiredCerts > 0 && (
                          <Badge variant="destructive">{expiredCerts} Expired</Badge>
                        )}
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {empCerts.map(cert => (
                        <div key={cert.id} className="flex justify-between items-center text-sm">
                          <span>{getCertificationName(cert.certificationId)}</span>
                          <Badge variant={getStatusColor(cert.status) as any} className="text-xs">
                            Expires {format(cert.expiryDate, 'PP')}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>

        <TabsContent value="certifications" className="space-y-4">
          <div className="grid gap-4">
            {certifications.map(cert => {
              const holders = employeeCerts.filter(ec => ec.certificationId === cert.id);
              const activeHolders = holders.filter(h => h.status === 'active').length;
              
              return (
                <Card key={cert.id}>
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle>{cert.name}</CardTitle>
                        <CardDescription>
                          {cert.issuingOrganization}
                        </CardDescription>
                      </div>
                      <div className="flex flex-col items-end gap-2">
                        <Badge variant="outline">
                          {cert.category}
                        </Badge>
                        <span className="text-sm text-muted-foreground">
                          ${cert.cost}/person
                        </span>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">Validity Period</span>
                        <p className="font-medium">{cert.validityPeriod} months</p>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Certificate Holders</span>
                        <p className="font-medium">{activeHolders} active / {holders.length} total</p>
                      </div>
                    </div>

                    <div>
                      <span className="text-sm text-muted-foreground">Requirements</span>
                      <ul className="mt-1 text-sm list-disc list-inside">
                        {cert.requirements.map((req, idx) => (
                          <li key={idx}>{req}</li>
                        ))}
                      </ul>
                    </div>

                    <div className="pt-2">
                      <Progress value={(activeHolders / (holders.length || 1)) * 100} />
                      <p className="text-xs text-muted-foreground mt-1">
                        {Math.round((activeHolders / (holders.length || 1)) * 100)}% compliance rate
                      </p>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>

        <TabsContent value="compliance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Compliance Dashboard</CardTitle>
              <CardDescription>
                Organization-wide certification compliance status
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {certifications.filter(c => c.category === 'compliance' || c.category === 'safety').map(cert => {
                const holders = employeeCerts.filter(ec => ec.certificationId === cert.id);
                const activeCount = holders.filter(h => h.status === 'active').length;
                const totalRequired = 10; // This would come from actual requirements
                const compliancePercent = (activeCount / totalRequired) * 100;
                
                return (
                  <div key={cert.id} className="space-y-2">
                    <div className="flex justify-between items-center">
                      <div>
                        <p className="font-medium">{cert.name}</p>
                        <p className="text-sm text-muted-foreground">
                          {activeCount} of {totalRequired} required employees certified
                        </p>
                      </div>
                      <Badge 
                        variant={compliancePercent >= 100 ? 'success' : 
                                compliancePercent >= 80 ? 'warning' : 'destructive'}
                      >
                        {compliancePercent.toFixed(0)}%
                      </Badge>
                    </div>
                    <Progress value={compliancePercent} />
                  </div>
                );
              })}

              <div className="pt-4 border-t">
                <h4 className="font-medium mb-2">Compliance Actions Required</h4>
                <div className="space-y-2">
                  {employeeCerts
                    .filter(c => c.status === 'expired' || c.status === 'expiring_soon')
                    .slice(0, 5)
                    .map(cert => (
                      <div key={cert.id} className="flex justify-between items-center text-sm">
                        <span>
                          {getEmployeeName(cert.employeeId)} - {getCertificationName(cert.certificationId)}
                        </span>
                        <Button size="sm" variant="outline">
                          Take Action
                        </Button>
                      </div>
                    ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};