import React, { useState, useEffect } from 'react';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import { Progress } from '@/components/ui/progress';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  MessageSquare,
  Star,
  ThumbsUp,
  ThumbsDown,
  Send,
  FileText,
  BarChart,
  TrendingUp,
  Users,
  Clock,
  CheckCircle,
  AlertCircle,
  Plus,
  Edit,
  Eye,
  Trash2,
  Copy
} from 'lucide-react';
import { format } from 'date-fns';
import { TrainingFeedback, FeedbackSurvey, SurveyQuestion } from '../types';
import { useTraining } from '@/contexts/TrainingContext';
import { 
  BarChart as ReBarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  RadialBarChart,
  RadialBar,
  Legend,
  PieChart,
  Pie,
  Cell
} from 'recharts';

export const FeedbackCollector: React.FC = () => {
  const { state, dispatch } = useTraining();
  const [feedbacks, setFeedbacks] = useState<TrainingFeedback[]>([]);
  const [surveys, setSurveys] = useState<FeedbackSurvey[]>([]);
  const [selectedProgram, setSelectedProgram] = useState<string>('all');
  const [showCreateSurvey, setShowCreateSurvey] = useState(false);
  const [showFeedbackForm, setShowFeedbackForm] = useState(false);
  const [newSurvey, setNewSurvey] = useState<Partial<FeedbackSurvey>>({
    questions: []
  });

  // Mock data
  useEffect(() => {
    const mockFeedbacks: TrainingFeedback[] = [
      {
        id: 'fb-1',
        programId: 'prog-1',
        sessionId: 'session-1',
        employeeId: 'emp-001',
        submittedAt: new Date('2024-01-20'),
        overallRating: 4.5,
        contentQuality: 5,
        instructorRating: 4,
        relevance: 5,
        wouldRecommend: true,
        improvements: 'More hands-on exercises would be beneficial',
        highlights: 'Excellent real-world examples and case studies',
        anonymous: false
      },
      {
        id: 'fb-2',
        programId: 'prog-2',
        employeeId: 'emp-002',
        submittedAt: new Date('2024-01-18'),
        overallRating: 5,
        contentQuality: 5,
        instructorRating: 5,
        relevance: 5,
        wouldRecommend: true,
        highlights: 'Outstanding leadership framework presentation',
        anonymous: false
      },
      {
        id: 'fb-3',
        programId: 'prog-1',
        sessionId: 'session-1',
        employeeId: 'emp-003',
        submittedAt: new Date('2024-01-19'),
        overallRating: 3.5,
        contentQuality: 3,
        instructorRating: 4,
        relevance: 3,
        wouldRecommend: false,
        improvements: 'Content was too basic for experienced professionals',
        highlights: 'Good pace and clear explanations',
        anonymous: true
      },
      {
        id: 'fb-4',
        programId: 'prog-3',
        employeeId: 'emp-004',
        submittedAt: new Date('2024-01-17'),
        overallRating: 4,
        contentQuality: 4,
        relevance: 5,
        wouldRecommend: true,
        improvements: 'Could benefit from more visual aids',
        highlights: 'Practical skills that can be immediately applied',
        anonymous: false
      }
    ];

    const mockSurveys: FeedbackSurvey[] = [
      {
        id: 'survey-1',
        title: 'Post-Training Evaluation',
        questions: [
          {
            id: 'q1',
            text: 'How would you rate the overall training experience?',
            type: 'rating',
            required: true
          },
          {
            id: 'q2',
            text: 'Was the content relevant to your role?',
            type: 'yes_no',
            required: true
          },
          {
            id: 'q3',
            text: 'What did you like most about the training?',
            type: 'text',
            required: false
          },
          {
            id: 'q4',
            text: 'Which areas need improvement?',
            type: 'multiple_choice',
            options: ['Content', 'Delivery', 'Duration', 'Materials', 'Venue'],
            required: true
          }
        ],
        targetAudience: ['all'],
        deadline: new Date('2024-02-01'),
        responseRate: 75
      },
      {
        id: 'survey-2',
        title: 'Learning Impact Assessment',
        questions: [
          {
            id: 'q5',
            text: 'Have you applied the learned skills in your work?',
            type: 'yes_no',
            required: true
          },
          {
            id: 'q6',
            text: 'Rate the impact on your productivity',
            type: 'rating',
            required: true
          }
        ],
        targetAudience: ['completed_training'],
        deadline: new Date('2024-03-01'),
        responseRate: 62
      }
    ];

    setFeedbacks(mockFeedbacks);
    setSurveys(mockSurveys);
  }, []);

  const getEmployeeName = (employeeId: string) => {
    const names: { [key: string]: string } = {
      'emp-001': 'John Smith',
      'emp-002': 'Sarah Johnson',
      'emp-003': 'Anonymous',
      'emp-004': 'Lisa Brown'
    };
    return names[employeeId] || employeeId;
  };

  const getProgramName = (programId: string) => {
    const programs: { [key: string]: string } = {
      'prog-1': 'Safety Certification',
      'prog-2': 'Leadership Excellence',
      'prog-3': 'Technical Skills'
    };
    return programs[programId] || programId;
  };

  // Calculate metrics
  const avgRating = feedbacks.reduce((acc, f) => acc + f.overallRating, 0) / feedbacks.length || 0;
  const recommendationRate = (feedbacks.filter(f => f.wouldRecommend).length / feedbacks.length) * 100 || 0;
  const responseCount = feedbacks.length;

  // Rating distribution
  const ratingDistribution = [
    { rating: '5 Stars', count: feedbacks.filter(f => f.overallRating >= 4.5).length },
    { rating: '4 Stars', count: feedbacks.filter(f => f.overallRating >= 3.5 && f.overallRating < 4.5).length },
    { rating: '3 Stars', count: feedbacks.filter(f => f.overallRating >= 2.5 && f.overallRating < 3.5).length },
    { rating: '2 Stars', count: feedbacks.filter(f => f.overallRating >= 1.5 && f.overallRating < 2.5).length },
    { rating: '1 Star', count: feedbacks.filter(f => f.overallRating < 1.5).length },
  ];

  // Category scores
  const categoryScores = [
    { category: 'Content Quality', score: feedbacks.reduce((acc, f) => acc + f.contentQuality, 0) / feedbacks.filter(f => f.contentQuality).length || 0 },
    { category: 'Instructor Rating', score: feedbacks.reduce((acc, f) => acc + (f.instructorRating || 0), 0) / feedbacks.filter(f => f.instructorRating).length || 0 },
    { category: 'Relevance', score: feedbacks.reduce((acc, f) => acc + f.relevance, 0) / feedbacks.filter(f => f.relevance).length || 0 },
  ];

  const COLORS = ['#10b981', '#3b82f6', '#f59e0b', '#ef4444', '#8b5cf6'];

  const handleAddQuestion = () => {
    const newQuestion: SurveyQuestion = {
      id: Date.now().toString(),
      text: '',
      type: 'rating',
      required: true
    };
    setNewSurvey({
      ...newSurvey,
      questions: [...(newSurvey.questions || []), newQuestion]
    });
  };

  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center gap-1">
        {[1, 2, 3, 4, 5].map(star => (
          <Star 
            key={star} 
            className={`h-4 w-4 ${star <= rating ? 'fill-yellow-400 text-yellow-400' : 'text-muted-foreground'}`}
          />
        ))}
        <span className="ml-2 text-sm font-medium">{rating.toFixed(1)}</span>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Training Feedback</h2>
          <p className="text-muted-foreground">
            Collect and analyze training feedback and evaluations
          </p>
        </div>
        <div className="flex gap-2">
          <Dialog open={showCreateSurvey} onOpenChange={setShowCreateSurvey}>
            <DialogTrigger asChild>
              <Button variant="outline">
                <FileText className="h-4 w-4 mr-2" />
                Create Survey
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Create Feedback Survey</DialogTitle>
                <DialogDescription>
                  Design a custom feedback survey for training programs
                </DialogDescription>
              </DialogHeader>
              
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label>Survey Title</Label>
                  <Input 
                    placeholder="Enter survey title"
                    value={newSurvey.title || ''}
                    onChange={e => setNewSurvey({...newSurvey, title: e.target.value})}
                  />
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <Label>Questions</Label>
                    <Button size="sm" onClick={handleAddQuestion}>
                      <Plus className="h-3 w-3 mr-1" />
                      Add Question
                    </Button>
                  </div>
                  
                  {newSurvey.questions?.map((question, idx) => (
                    <Card key={question.id}>
                      <CardContent className="pt-4 space-y-3">
                        <Input 
                          placeholder="Enter question text"
                          value={question.text}
                          onChange={e => {
                            const updatedQuestions = [...(newSurvey.questions || [])];
                            updatedQuestions[idx] = {...question, text: e.target.value};
                            setNewSurvey({...newSurvey, questions: updatedQuestions});
                          }}
                        />
                        <div className="flex gap-2">
                          <Select 
                            value={question.type}
                            onValueChange={value => {
                              const updatedQuestions = [...(newSurvey.questions || [])];
                              updatedQuestions[idx] = {...question, type: value as any};
                              setNewSurvey({...newSurvey, questions: updatedQuestions});
                            }}
                          >
                            <SelectTrigger className="w-[150px]">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="rating">Rating</SelectItem>
                              <SelectItem value="text">Text</SelectItem>
                              <SelectItem value="multiple_choice">Multiple Choice</SelectItem>
                              <SelectItem value="yes_no">Yes/No</SelectItem>
                            </SelectContent>
                          </Select>
                          <div className="flex items-center gap-2">
                            <Checkbox 
                              checked={question.required}
                              onCheckedChange={checked => {
                                const updatedQuestions = [...(newSurvey.questions || [])];
                                updatedQuestions[idx] = {...question, required: checked as boolean};
                                setNewSurvey({...newSurvey, questions: updatedQuestions});
                              }}
                            />
                            <Label className="text-sm">Required</Label>
                          </div>
                          <Button 
                            size="sm" 
                            variant="ghost"
                            onClick={() => {
                              const updatedQuestions = newSurvey.questions?.filter((_, i) => i !== idx);
                              setNewSurvey({...newSurvey, questions: updatedQuestions});
                            }}
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>

                <div className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => setShowCreateSurvey(false)}>
                    Cancel
                  </Button>
                  <Button onClick={() => {
                    // Save survey logic here
                    setShowCreateSurvey(false);
                  }}>
                    Create Survey
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>

          <Button onClick={() => setShowFeedbackForm(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Collect Feedback
          </Button>
        </div>
      </div>

      {/* Metrics Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardDescription className="flex items-center gap-2">
              <Star className="h-4 w-4" />
              Average Rating
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{avgRating.toFixed(1)}/5.0</div>
            {renderStars(avgRating)}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardDescription className="flex items-center gap-2">
              <ThumbsUp className="h-4 w-4" />
              Recommendation Rate
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{recommendationRate.toFixed(0)}%</div>
            <p className="text-xs text-muted-foreground">Would recommend</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardDescription className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              Total Responses
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{responseCount}</div>
            <p className="text-xs text-muted-foreground">This month</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardDescription className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              Response Rate
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">68%</div>
            <Progress value={68} className="mt-2 h-1" />
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="feedback">
        <TabsList>
          <TabsTrigger value="feedback">Recent Feedback</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="surveys">Active Surveys</TabsTrigger>
          <TabsTrigger value="insights">Insights</TabsTrigger>
        </TabsList>

        <TabsContent value="feedback" className="space-y-4">
          <div className="flex gap-4 mb-4">
            <Select value={selectedProgram} onValueChange={setSelectedProgram}>
              <SelectTrigger className="w-[200px]">
                <SelectValue placeholder="All Programs" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Programs</SelectItem>
                <SelectItem value="prog-1">Safety Certification</SelectItem>
                <SelectItem value="prog-2">Leadership Excellence</SelectItem>
                <SelectItem value="prog-3">Technical Skills</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {feedbacks
            .filter(f => selectedProgram === 'all' || f.programId === selectedProgram)
            .map(feedback => (
              <Card key={feedback.id}>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-lg">
                        {getProgramName(feedback.programId)}
                      </CardTitle>
                      <CardDescription>
                        {feedback.anonymous ? 'Anonymous' : getEmployeeName(feedback.employeeId)} • {format(feedback.submittedAt, 'PP')}
                      </CardDescription>
                    </div>
                    <div className="flex items-center gap-2">
                      {renderStars(feedback.overallRating)}
                      {feedback.wouldRecommend && (
                        <Badge variant="success">
                          <ThumbsUp className="h-3 w-3 mr-1" />
                          Recommended
                        </Badge>
                      )}
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">Content Quality</span>
                      <div className="font-medium">{renderStars(feedback.contentQuality)}</div>
                    </div>
                    {feedback.instructorRating && (
                      <div>
                        <span className="text-muted-foreground">Instructor</span>
                        <div className="font-medium">{renderStars(feedback.instructorRating)}</div>
                      </div>
                    )}
                    <div>
                      <span className="text-muted-foreground">Relevance</span>
                      <div className="font-medium">{renderStars(feedback.relevance)}</div>
                    </div>
                  </div>

                  {feedback.highlights && (
                    <div>
                      <p className="text-sm font-medium text-green-600 mb-1">Highlights</p>
                      <p className="text-sm text-muted-foreground">{feedback.highlights}</p>
                    </div>
                  )}

                  {feedback.improvements && (
                    <div>
                      <p className="text-sm font-medium text-amber-600 mb-1">Areas for Improvement</p>
                      <p className="text-sm text-muted-foreground">{feedback.improvements}</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Rating Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={200}>
                  <ReBarChart data={ratingDistribution}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="rating" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="count" fill="#3b82f6" />
                  </ReBarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Category Scores</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={200}>
                  <RadialBarChart cx="50%" cy="50%" innerRadius="10%" outerRadius="90%" data={categoryScores}>
                    <RadialBar minAngle={15} dataKey="score" fill="#10b981" />
                    <Tooltip />
                  </RadialBarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Feedback Trends</CardTitle>
              <CardDescription>Average rating over time</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={200}>
                <BarChart data={[
                  { month: 'Oct', rating: 4.2 },
                  { month: 'Nov', rating: 4.3 },
                  { month: 'Dec', rating: 4.5 },
                  { month: 'Jan', rating: 4.4 },
                ]}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis domain={[0, 5]} />
                  <Tooltip />
                  <Bar dataKey="rating" fill="#3b82f6" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="surveys" className="space-y-4">
          {surveys.map(survey => (
            <Card key={survey.id}>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle>{survey.title}</CardTitle>
                    <CardDescription>
                      {survey.questions.length} questions • Deadline: {survey.deadline && format(survey.deadline, 'PP')}
                    </CardDescription>
                  </div>
                  <Badge variant="outline">
                    {survey.responseRate}% response rate
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-3">
                <Progress value={survey.responseRate || 0} />
                
                <div className="space-y-2">
                  {survey.questions.map(question => (
                    <div key={question.id} className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">{question.text}</span>
                      <div className="flex items-center gap-2">
                        <Badge variant="secondary" className="text-xs">
                          {question.type}
                        </Badge>
                        {question.required && (
                          <Badge variant="outline" className="text-xs">Required</Badge>
                        )}
                      </div>
                    </div>
                  ))}
                </div>

                <div className="flex gap-2 pt-2">
                  <Button size="sm" variant="outline">
                    <Eye className="h-3 w-3 mr-1" />
                    View Responses
                  </Button>
                  <Button size="sm" variant="outline">
                    <Copy className="h-3 w-3 mr-1" />
                    Duplicate
                  </Button>
                  <Button size="sm" variant="outline">
                    <Send className="h-3 w-3 mr-1" />
                    Send Reminder
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="insights" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-green-500" />
                  Top Strengths
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="text-sm">
                  <p className="font-medium">• Excellent real-world examples</p>
                  <p className="text-muted-foreground">Mentioned in 85% of feedback</p>
                </div>
                <div className="text-sm">
                  <p className="font-medium">• Clear and engaging instructors</p>
                  <p className="text-muted-foreground">Mentioned in 78% of feedback</p>
                </div>
                <div className="text-sm">
                  <p className="font-medium">• Practical, applicable content</p>
                  <p className="text-muted-foreground">Mentioned in 72% of feedback</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertCircle className="h-5 w-5 text-amber-500" />
                  Areas for Improvement
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="text-sm">
                  <p className="font-medium">• More hands-on exercises needed</p>
                  <p className="text-muted-foreground">Mentioned in 45% of feedback</p>
                </div>
                <div className="text-sm">
                  <p className="font-medium">• Session duration too long</p>
                  <p className="text-muted-foreground">Mentioned in 32% of feedback</p>
                </div>
                <div className="text-sm">
                  <p className="font-medium">• Need more visual aids</p>
                  <p className="text-muted-foreground">Mentioned in 28% of feedback</p>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Key Recommendations</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Alert>
                <TrendingUp className="h-4 w-4" />
                <AlertDescription>
                  <strong>High Priority:</strong> Incorporate more interactive elements and hands-on exercises based on consistent feedback across multiple programs.
                </AlertDescription>
              </Alert>
              <Alert>
                <Clock className="h-4 w-4" />
                <AlertDescription>
                  <strong>Consider:</strong> Breaking longer sessions into multiple shorter modules to improve engagement and retention.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};