import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  BarChart3,
  Users,
  Calendar,
  DollarSign,
  Award,
  BookOpen,
  Activity,
  MessageSquare,
  Trophy,
  Map,
  TrendingUp,
  Settings,
  Grid,
  AlertCircle
} from 'lucide-react';

// Import API hooks
import { useDashboardMetrics } from '@/hooks/useTrainingApi';

// Import all components
import { LMSIntegration } from './integrations/LMSIntegration';
import { TrainingScheduler } from './scheduling/TrainingScheduler';
import { CertificationTracker } from './certification/CertificationTracker';
import { TrainingBudgetManager } from './budget/TrainingBudgetManager';
import { ProgressTracker } from './progress/ProgressTracker';
import { FeedbackCollector } from './feedback/FeedbackCollector';
import { GamificationHub } from './gamification/GamificationHub';
import { ReportingDashboard } from './reporting/ReportingDashboard';
import { LearningPathways } from './pathways/LearningPathways';
import { SkillsGapAnalysis } from './analysis/SkillsGapAnalysis';

/**
 * Enhanced Training Dashboard with API Integration
 * This version uses the MWS mock API for data fetching
 */
export const TrainingDashboardWithAPI: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');
  
  // Fetch dashboard metrics using API hook
  const { data: metrics, loading, error } = useDashboardMetrics();

  const renderOverview = () => {
    if (loading) {
      return (
        <div className="space-y-6">
          <div className="grid gap-4 md:grid-cols-4">
            {[...Array(4)].map((_, i) => (
              <Card key={i}>
                <CardHeader className="pb-2">
                  <Skeleton className="h-4 w-24" />
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-8 w-16" />
                  <Skeleton className="h-3 w-32 mt-2" />
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      );
    }

    if (error) {
      return (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Failed to load dashboard metrics. Please try again later.
          </AlertDescription>
        </Alert>
      );
    }

    const overview = metrics?.overview || {
      activePrograms: 0,
      activeTrainees: 0,
      completedThisMonth: 0,
      upcomingCertifications: 0,
      totalBudget: 0,
      budgetUtilized: 0,
      averageSatisfaction: 0,
      completionRate: 0
    };

    return (
      <div className="space-y-6">
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="pb-2">
              <CardDescription className="flex items-center gap-2">
                <BookOpen className="h-4 w-4" />
                Active Programs
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{overview.activePrograms}</div>
              <p className="text-xs text-muted-foreground">Across all departments</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardDescription className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                Active Trainees
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{overview.activeTrainees}</div>
              <p className="text-xs text-muted-foreground">Currently in training</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardDescription className="flex items-center gap-2">
                <Award className="h-4 w-4" />
                Completed This Month
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{overview.completedThisMonth}</div>
              <p className="text-xs text-muted-foreground">Successfully certified</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardDescription className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Upcoming Certifications
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{overview.upcomingCertifications}</div>
              <p className="text-xs text-muted-foreground">Next 30 days</p>
            </CardContent>
          </Card>
        </div>

        <div className="grid gap-4 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Budget Utilization</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Total Budget</span>
                  <span className="font-semibold">${overview.totalBudget.toLocaleString()}</span>
                </div>
                <div className="w-full bg-secondary rounded-full h-2">
                  <div 
                    className="bg-primary rounded-full h-2 transition-all"
                    style={{ width: `${overview.budgetUtilized * 100}%` }}
                  />
                </div>
                <p className="text-xs text-muted-foreground">
                  {(overview.budgetUtilized * 100).toFixed(0)}% utilized
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Performance Metrics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Average Satisfaction</span>
                  <div className="flex items-center gap-1">
                    <span className="font-semibold">{overview.averageSatisfaction.toFixed(1)}</span>
                    <span className="text-xs text-muted-foreground">/ 5.0</span>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">Completion Rate</span>
                  <span className="font-semibold">{(overview.completionRate * 100).toFixed(0)}%</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent className="grid gap-2 md:grid-cols-2">
            <Button 
              variant="outline" 
              className="justify-start"
              onClick={() => setActiveTab('schedule')}
            >
              <Calendar className="h-4 w-4 mr-2" />
              Schedule Training Session
            </Button>
            <Button 
              variant="outline" 
              className="justify-start"
              onClick={() => setActiveTab('certifications')}
            >
              <Award className="h-4 w-4 mr-2" />
              Track Certifications
            </Button>
            <Button 
              variant="outline" 
              className="justify-start"
              onClick={() => setActiveTab('progress')}
            >
              <Activity className="h-4 w-4 mr-2" />
              View Progress Reports
            </Button>
            <Button 
              variant="outline" 
              className="justify-start"
              onClick={() => setActiveTab('pathways')}
            >
              <Map className="h-4 w-4 mr-2" />
              Manage Learning Paths
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Training Management System</h1>
          <p className="text-muted-foreground">
            Comprehensive training, certification, and development platform
          </p>
        </div>
        <Button>
          <Settings className="h-4 w-4 mr-2" />
          Settings
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-6 lg:grid-cols-11 gap-2 h-auto">
          <TabsTrigger value="overview" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">
            <Grid className="h-4 w-4 mr-2" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="lms">
            <BookOpen className="h-4 w-4 mr-2" />
            LMS
          </TabsTrigger>
          <TabsTrigger value="schedule">
            <Calendar className="h-4 w-4 mr-2" />
            Schedule
          </TabsTrigger>
          <TabsTrigger value="certifications">
            <Award className="h-4 w-4 mr-2" />
            Certifications
          </TabsTrigger>
          <TabsTrigger value="budget">
            <DollarSign className="h-4 w-4 mr-2" />
            Budget
          </TabsTrigger>
          <TabsTrigger value="progress">
            <Activity className="h-4 w-4 mr-2" />
            Progress
          </TabsTrigger>
          <TabsTrigger value="feedback">
            <MessageSquare className="h-4 w-4 mr-2" />
            Feedback
          </TabsTrigger>
          <TabsTrigger value="gamification">
            <Trophy className="h-4 w-4 mr-2" />
            Gamification
          </TabsTrigger>
          <TabsTrigger value="reports">
            <BarChart3 className="h-4 w-4 mr-2" />
            Reports
          </TabsTrigger>
          <TabsTrigger value="pathways">
            <Map className="h-4 w-4 mr-2" />
            Pathways
          </TabsTrigger>
          <TabsTrigger value="skills">
            <TrendingUp className="h-4 w-4 mr-2" />
            Skills Gap
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="mt-6">
          {renderOverview()}
        </TabsContent>

        <TabsContent value="lms" className="mt-6">
          <LMSIntegration />
        </TabsContent>

        <TabsContent value="schedule" className="mt-6">
          <TrainingScheduler />
        </TabsContent>

        <TabsContent value="certifications" className="mt-6">
          <CertificationTracker />
        </TabsContent>

        <TabsContent value="budget" className="mt-6">
          <TrainingBudgetManager />
        </TabsContent>

        <TabsContent value="progress" className="mt-6">
          <ProgressTracker />
        </TabsContent>

        <TabsContent value="feedback" className="mt-6">
          <FeedbackCollector />
        </TabsContent>

        <TabsContent value="gamification" className="mt-6">
          <GamificationHub />
        </TabsContent>

        <TabsContent value="reports" className="mt-6">
          <ReportingDashboard />
        </TabsContent>

        <TabsContent value="pathways" className="mt-6">
          <LearningPathways />
        </TabsContent>

        <TabsContent value="skills" className="mt-6">
          <SkillsGapAnalysis />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default TrainingDashboardWithAPI;