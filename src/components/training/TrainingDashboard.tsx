import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { TrainingProvider, useTraining } from '@/contexts/TrainingContext';
import { 
  BarChart3,
  Users,
  Calendar,
  DollarSign,
  Award,
  BookOpen,
  Activity,
  MessageSquare,
  Trophy,
  Map,
  TrendingUp,
  Settings,
  Grid
} from 'lucide-react';

// Import all new components
import { LMSIntegration } from './integrations/LMSIntegration';
import { TrainingScheduler } from './scheduling/TrainingScheduler';
import { CertificationTracker } from './certification/CertificationTracker';
import { TrainingBudgetManager } from './budget/TrainingBudgetManager';
import { ProgressTracker } from './progress/ProgressTracker';
import { FeedbackCollector } from './feedback/FeedbackCollector';
import { GamificationHub } from './gamification/GamificationHub';
import { ReportingDashboard } from './reporting/ReportingDashboard';
import { LearningPathways } from './pathways/LearningPathways';

// Import existing components
import { SkillsGapAnalysis } from './analysis/SkillsGapAnalysis';
import { TrainingNeedsCSVImport } from './data/TrainingNeedsCSVImport';

const EnhancedTrainingDashboardContent: React.FC = () => {
  const { state } = useTraining();
  const [activeTab, setActiveTab] = useState('overview');

  // Calculate key metrics from state
  const totalTrainingPrograms = state.trainingPrograms.length;
  const activeTrainees = state.trainingNeeds.filter(n => n.status === 'in_progress').length;
  const completedThisMonth = state.trainingNeeds.filter(n => n.status === 'completed').length;
  const upcomingCertifications = 5; // This would come from certification data

  const renderOverview = () => (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardDescription className="flex items-center gap-2">
              <BookOpen className="h-4 w-4" />
              Active Programs
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalTrainingPrograms}</div>
            <p className="text-xs text-muted-foreground">Across all departments</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardDescription className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Active Trainees
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activeTrainees}</div>
            <p className="text-xs text-muted-foreground">Currently in training</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardDescription className="flex items-center gap-2">
              <Award className="h-4 w-4" />
              Completed This Month
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{completedThisMonth}</div>
            <p className="text-xs text-muted-foreground">Successfully certified</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardDescription className="flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              Upcoming Certifications
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{upcomingCertifications}</div>
            <p className="text-xs text-muted-foreground">Next 30 days</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent className="grid gap-2">
            <Button 
              variant="outline" 
              className="justify-start"
              onClick={() => setActiveTab('schedule')}
            >
              <Calendar className="h-4 w-4 mr-2" />
              Schedule Training Session
            </Button>
            <Button 
              variant="outline" 
              className="justify-start"
              onClick={() => setActiveTab('certifications')}
            >
              <Award className="h-4 w-4 mr-2" />
              Track Certifications
            </Button>
            <Button 
              variant="outline" 
              className="justify-start"
              onClick={() => setActiveTab('progress')}
            >
              <Activity className="h-4 w-4 mr-2" />
              View Progress Reports
            </Button>
            <Button 
              variant="outline" 
              className="justify-start"
              onClick={() => setActiveTab('pathways')}
            >
              <Map className="h-4 w-4 mr-2" />
              Manage Learning Paths
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Recent Activities</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="text-sm">
              <p className="font-medium">New certification achieved</p>
              <p className="text-xs text-muted-foreground">John Smith - Safety Specialist</p>
            </div>
            <div className="text-sm">
              <p className="font-medium">Training session scheduled</p>
              <p className="text-xs text-muted-foreground">Leadership Workshop - Jan 28</p>
            </div>
            <div className="text-sm">
              <p className="font-medium">Budget approval required</p>
              <p className="text-xs text-muted-foreground">AWS Certification - $3,500</p>
            </div>
            <div className="text-sm">
              <p className="font-medium">Feedback survey completed</p>
              <p className="text-xs text-muted-foreground">Safety Training - 4.5/5 rating</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Training Management System</h1>
          <p className="text-muted-foreground">
            Comprehensive training, certification, and development platform
          </p>
        </div>
        <Button>
          <Settings className="h-4 w-4 mr-2" />
          Settings
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-6 lg:grid-cols-11 gap-2 h-auto">
          <TabsTrigger value="overview" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">
            <Grid className="h-4 w-4 mr-2" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="lms">
            <BookOpen className="h-4 w-4 mr-2" />
            LMS
          </TabsTrigger>
          <TabsTrigger value="schedule">
            <Calendar className="h-4 w-4 mr-2" />
            Schedule
          </TabsTrigger>
          <TabsTrigger value="certifications">
            <Award className="h-4 w-4 mr-2" />
            Certifications
          </TabsTrigger>
          <TabsTrigger value="budget">
            <DollarSign className="h-4 w-4 mr-2" />
            Budget
          </TabsTrigger>
          <TabsTrigger value="progress">
            <Activity className="h-4 w-4 mr-2" />
            Progress
          </TabsTrigger>
          <TabsTrigger value="feedback">
            <MessageSquare className="h-4 w-4 mr-2" />
            Feedback
          </TabsTrigger>
          <TabsTrigger value="gamification">
            <Trophy className="h-4 w-4 mr-2" />
            Gamification
          </TabsTrigger>
          <TabsTrigger value="reports">
            <BarChart3 className="h-4 w-4 mr-2" />
            Reports
          </TabsTrigger>
          <TabsTrigger value="pathways">
            <Map className="h-4 w-4 mr-2" />
            Pathways
          </TabsTrigger>
          <TabsTrigger value="skills">
            <TrendingUp className="h-4 w-4 mr-2" />
            Skills Gap
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="mt-6">
          {renderOverview()}
        </TabsContent>

        <TabsContent value="lms" className="mt-6">
          <LMSIntegration />
        </TabsContent>

        <TabsContent value="schedule" className="mt-6">
          <TrainingScheduler />
        </TabsContent>

        <TabsContent value="certifications" className="mt-6">
          <CertificationTracker />
        </TabsContent>

        <TabsContent value="budget" className="mt-6">
          <TrainingBudgetManager />
        </TabsContent>

        <TabsContent value="progress" className="mt-6">
          <ProgressTracker />
        </TabsContent>

        <TabsContent value="feedback" className="mt-6">
          <FeedbackCollector />
        </TabsContent>

        <TabsContent value="gamification" className="mt-6">
          <GamificationHub />
        </TabsContent>

        <TabsContent value="reports" className="mt-6">
          <ReportingDashboard />
        </TabsContent>

        <TabsContent value="pathways" className="mt-6">
          <LearningPathways />
        </TabsContent>

        <TabsContent value="skills" className="mt-6">
          <SkillsGapAnalysis />
        </TabsContent>
      </Tabs>
    </div>
  );
};

// Wrap with provider
export const EnhancedTrainingDashboard: React.FC = () => {
  return (
    <TrainingProvider>
      <EnhancedTrainingDashboardContent />
    </TrainingProvider>
  );
};

export default EnhancedTrainingDashboard;