import { describe, it, expect } from 'vitest';
import * as TrainingExports from '../index';

describe('Training Module Integration', () => {
  describe('Component Exports', () => {
    it('should export all core components', () => {
      // Dashboard components
      expect(TrainingExports.TrainingDashboard).toBeDefined();
      expect(TrainingExports.TrainingDashboardDefault).toBeDefined();
      expect(TrainingExports.EnhancedTrainingDashboard).toBeDefined();
      
      // Content components
      expect(TrainingExports.ContentEditor).toBeDefined();
      expect(TrainingExports.MediaLibrary).toBeDefined();
      expect(TrainingExports.TemplateManager).toBeDefined();
      expect(TrainingExports.TrainingContentStudio).toBeDefined();
    });

    it('should export all NEW integrated components', () => {
      // LMS Integration
      expect(TrainingExports.LMSIntegration).toBeDefined();
      
      // Scheduling
      expect(TrainingExports.TrainingScheduler).toBeDefined();
      
      // Certification
      expect(TrainingExports.CertificationTracker).toBeDefined();
      
      // Budget Management
      expect(TrainingExports.TrainingBudgetManager).toBeDefined();
      
      // Progress Tracking
      expect(TrainingExports.ProgressTracker).toBeDefined();
      
      // Feedback
      expect(TrainingExports.FeedbackCollector).toBeDefined();
      
      // Gamification
      expect(TrainingExports.GamificationHub).toBeDefined();
      
      // Reporting
      expect(TrainingExports.ReportingDashboard).toBeDefined();
      
      // Learning Pathways
      expect(TrainingExports.LearningPathways).toBeDefined();
    });

    it('should export analysis components', () => {
      expect(TrainingExports.SkillsGapAnalysis).toBeDefined();
      expect(TrainingExports.LDProfessionalTNA).toBeDefined();
    });

    it('should export data components', () => {
      expect(TrainingExports.TrainingNeedsCSVImport).toBeDefined();
    });
  });

  describe('Type Definitions', () => {
    it('should have all required type exports', () => {
      // Check for type existence by attempting to use them
      // Note: These are compile-time checks, runtime will show as undefined
      const typeChecks = [
        'TrainingNeed',
        'TrainingProgram',
        'SkillAssessment',
        'TrainingMetrics',
        'LMSProvider',
        'LMSCourse',
        'TrainingSession',
        'TrainingCalendar',
        'Certification',
        'EmployeeCertification',
        'TrainingBudget',
        'BudgetRequest',
        'LearningProgress',
        'ModuleProgress',
        'TrainingFeedback',
        'FeedbackSurvey',
        'GamificationProfile',
        'Badge',
        'Achievement',
        'LearningStreak',
        'LearningPathway',
        'PathwayStage',
        'EmployeePathway',
        'TrainingReport',
        'DashboardMetric',
        'ComplianceTraining',
        'ComplianceStatus'
      ];
      
      // Types are compile-time only, so we just verify the module structure
      expect(TrainingExports).toHaveProperty('SkillsGapAnalysis');
    });
  });

  describe('Module Structure', () => {
    it('should have proper folder structure', () => {
      // Verify the module has all expected subfolders
      const expectedStructure = [
        'analysis',
        'budget',
        'certification',
        'data',
        'feedback',
        'gamification',
        'integrations',
        'pathways',
        'progress',
        'reporting',
        'scheduling',
        'types',
        'hooks',
        'utils',
        'visualizations'
      ];
      
      // Check that components are from correct paths
      expect(TrainingExports.LMSIntegration).toBeDefined();
      expect(TrainingExports.TrainingScheduler).toBeDefined();
      expect(TrainingExports.CertificationTracker).toBeDefined();
      expect(TrainingExports.TrainingBudgetManager).toBeDefined();
      expect(TrainingExports.ProgressTracker).toBeDefined();
      expect(TrainingExports.FeedbackCollector).toBeDefined();
      expect(TrainingExports.GamificationHub).toBeDefined();
      expect(TrainingExports.ReportingDashboard).toBeDefined();
      expect(TrainingExports.LearningPathways).toBeDefined();
    });
  });

  describe('Component Integration', () => {
    it('EnhancedTrainingDashboard should integrate all components', () => {
      // The EnhancedTrainingDashboard should be a valid React component
      expect(typeof TrainingExports.EnhancedTrainingDashboard).toBe('function');
    });

    it('all components should be properly typed', () => {
      // Type checking happens at compile time
      // Runtime check to ensure components are functions (React components)
      const components = [
        TrainingExports.LMSIntegration,
        TrainingExports.TrainingScheduler,
        TrainingExports.CertificationTracker,
        TrainingExports.TrainingBudgetManager,
        TrainingExports.ProgressTracker,
        TrainingExports.FeedbackCollector,
        TrainingExports.GamificationHub,
        TrainingExports.ReportingDashboard,
        TrainingExports.LearningPathways
      ];

      components.forEach(component => {
        expect(typeof component).toBe('function');
      });
    });
  });
});