import React, { useState, useEffect } from 'react';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { 
  RefreshCw, 
  Plus, 
  Settings, 
  CheckCircle, 
  AlertCircle,
  Cloud,
  Link,
  BookOpen,
  Users,
  Calendar,
  Activity
} from 'lucide-react';
import { LMSProvider, LMSCourse } from '../types';
import { useTraining } from '@/contexts/TrainingContext';

export const LMSIntegration: React.FC = () => {
  const { state, dispatch } = useTraining();
  const [providers, setProviders] = useState<LMSProvider[]>([]);
  const [courses, setCourses] = useState<LMSCourse[]>([]);
  const [selectedProvider, setSelectedProvider] = useState<string>('');
  const [syncProgress, setSyncProgress] = useState<number>(0);
  const [isSyncing, setIsSyncing] = useState(false);
  const [showAddProvider, setShowAddProvider] = useState(false);

  // Mock data for demonstration
  useEffect(() => {
    const mockProviders: LMSProvider[] = [
      {
        id: '1',
        name: 'Moodle LMS',
        type: 'moodle',
        apiUrl: 'https://lms.company.com/api',
        isActive: true,
        features: ['SCORM', 'xAPI', 'Gradebook', 'Certificates'],
        lastSync: new Date('2024-01-20T10:00:00')
      },
      {
        id: '2',
        name: 'Cornerstone OnDemand',
        type: 'cornerstone',
        apiUrl: 'https://api.cornerstoneondemand.com',
        isActive: true,
        features: ['Skills Management', 'Compliance', 'Analytics'],
        lastSync: new Date('2024-01-21T14:30:00')
      },
      {
        id: '3',
        name: 'Canvas LMS',
        type: 'canvas',
        apiUrl: 'https://canvas.instructure.com/api',
        isActive: false,
        features: ['Mobile Learning', 'Video Conferencing', 'Rubrics'],
        lastSync: new Date('2024-01-15T08:00:00')
      }
    ];

    const mockCourses: LMSCourse[] = [
      {
        id: '1',
        externalId: 'MOD-101',
        providerId: '1',
        title: 'Safety Fundamentals',
        description: 'Basic safety procedures and protocols',
        duration: 120,
        enrolledCount: 45,
        completedCount: 38,
        rating: 4.5,
        syncStatus: 'synced'
      },
      {
        id: '2',
        externalId: 'COR-202',
        providerId: '2',
        title: 'Leadership Excellence',
        description: 'Advanced leadership skills for managers',
        duration: 480,
        enrolledCount: 28,
        completedCount: 15,
        rating: 4.8,
        syncStatus: 'synced'
      },
      {
        id: '3',
        externalId: 'MOD-103',
        providerId: '1',
        title: 'Technical Skills Workshop',
        description: 'Hands-on technical training',
        duration: 360,
        enrolledCount: 62,
        completedCount: 45,
        rating: 4.2,
        syncStatus: 'pending'
      }
    ];

    setProviders(mockProviders);
    setCourses(mockCourses);
  }, []);

  const handleSync = async (providerId: string) => {
    setIsSyncing(true);
    setSyncProgress(0);

    // Simulate sync progress
    const interval = setInterval(() => {
      setSyncProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setIsSyncing(false);
          
          // Update last sync time
          setProviders(prev => prev.map(p => 
            p.id === providerId 
              ? { ...p, lastSync: new Date() }
              : p
          ));
          
          return 100;
        }
        return prev + 10;
      });
    }, 300);
  };

  const handleAddProvider = (providerData: Partial<LMSProvider>) => {
    const newProvider: LMSProvider = {
      id: Date.now().toString(),
      name: providerData.name || '',
      type: providerData.type || 'custom',
      apiUrl: providerData.apiUrl || '',
      apiKey: providerData.apiKey,
      isActive: false,
      features: [],
      lastSync: undefined
    };

    setProviders([...providers, newProvider]);
    setShowAddProvider(false);
  };

  const toggleProviderStatus = (providerId: string) => {
    setProviders(prev => prev.map(p => 
      p.id === providerId 
        ? { ...p, isActive: !p.isActive }
        : p
    ));
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">LMS Integration</h2>
          <p className="text-muted-foreground">
            Connect and sync with external learning management systems
          </p>
        </div>
        <Button onClick={() => setShowAddProvider(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Add Provider
        </Button>
      </div>

      {isSyncing && (
        <Alert>
          <Activity className="h-4 w-4" />
          <AlertDescription className="space-y-2">
            <div>Synchronizing data with LMS provider...</div>
            <Progress value={syncProgress} className="w-full" />
          </AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="providers">
        <TabsList>
          <TabsTrigger value="providers">LMS Providers</TabsTrigger>
          <TabsTrigger value="courses">Synced Courses</TabsTrigger>
          <TabsTrigger value="settings">Integration Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="providers" className="space-y-4">
          {providers.map(provider => (
            <Card key={provider.id}>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div className="space-y-1">
                    <CardTitle className="flex items-center gap-2">
                      <Cloud className="h-5 w-5" />
                      {provider.name}
                    </CardTitle>
                    <CardDescription>
                      {provider.apiUrl}
                    </CardDescription>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant={provider.isActive ? 'default' : 'secondary'}>
                      {provider.isActive ? 'Active' : 'Inactive'}
                    </Badge>
                    <Button 
                      variant="ghost" 
                      size="sm"
                      onClick={() => toggleProviderStatus(provider.id)}
                    >
                      {provider.isActive ? 'Disable' : 'Enable'}
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex flex-wrap gap-2">
                  {provider.features.map(feature => (
                    <Badge key={feature} variant="outline">
                      {feature}
                    </Badge>
                  ))}
                </div>

                <div className="flex justify-between items-center">
                  <div className="text-sm text-muted-foreground">
                    {provider.lastSync ? (
                      <span>
                        Last synced: {provider.lastSync.toLocaleString()}
                      </span>
                    ) : (
                      <span>Never synced</span>
                    )}
                  </div>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => handleSync(provider.id)}
                    disabled={!provider.isActive || isSyncing}
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Sync Now
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="courses" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {courses.map(course => (
              <Card key={course.id}>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <BookOpen className="h-4 w-4" />
                    {course.title}
                  </CardTitle>
                  <CardDescription>
                    {course.description}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">Duration:</span>
                      <p className="font-medium">{course.duration} minutes</p>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Rating:</span>
                      <p className="font-medium">
                        {course.rating ? `${course.rating}/5.0` : 'N/A'}
                      </p>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Enrolled:</span>
                      <p className="font-medium flex items-center gap-1">
                        <Users className="h-3 w-3" />
                        {course.enrolledCount}
                      </p>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Completed:</span>
                      <p className="font-medium">
                        {course.completedCount} ({Math.round((course.completedCount / course.enrolledCount) * 100)}%)
                      </p>
                    </div>
                  </div>

                  <div className="flex justify-between items-center pt-2">
                    <Badge 
                      variant={course.syncStatus === 'synced' ? 'default' : 'secondary'}
                    >
                      {course.syncStatus === 'synced' ? (
                        <>
                          <CheckCircle className="h-3 w-3 mr-1" />
                          Synced
                        </>
                      ) : (
                        <>
                          <AlertCircle className="h-3 w-3 mr-1" />
                          Pending
                        </>
                      )}
                    </Badge>
                    <Button variant="ghost" size="sm">
                      <Link className="h-4 w-4 mr-2" />
                      View in LMS
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Integration Settings</CardTitle>
              <CardDescription>
                Configure global settings for LMS integrations
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>Sync Frequency</Label>
                <Select defaultValue="daily">
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="hourly">Every Hour</SelectItem>
                    <SelectItem value="daily">Daily</SelectItem>
                    <SelectItem value="weekly">Weekly</SelectItem>
                    <SelectItem value="manual">Manual Only</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Data Retention Period</Label>
                <Select defaultValue="90">
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="30">30 Days</SelectItem>
                    <SelectItem value="60">60 Days</SelectItem>
                    <SelectItem value="90">90 Days</SelectItem>
                    <SelectItem value="180">180 Days</SelectItem>
                    <SelectItem value="365">1 Year</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Sync Scope</Label>
                <Select defaultValue="all">
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Data</SelectItem>
                    <SelectItem value="courses">Courses Only</SelectItem>
                    <SelectItem value="enrollments">Enrollments Only</SelectItem>
                    <SelectItem value="progress">Progress Data Only</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Button className="w-full">
                <Settings className="h-4 w-4 mr-2" />
                Save Settings
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Add Provider Dialog - Simplified for now */}
      {showAddProvider && (
        <Card className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
          <div className="bg-background p-6 rounded-lg w-full max-w-md space-y-4">
            <h3 className="text-lg font-semibold">Add LMS Provider</h3>
            
            <div className="space-y-2">
              <Label>Provider Name</Label>
              <Input placeholder="Enter provider name" />
            </div>

            <div className="space-y-2">
              <Label>Provider Type</Label>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="moodle">Moodle</SelectItem>
                  <SelectItem value="canvas">Canvas</SelectItem>
                  <SelectItem value="blackboard">Blackboard</SelectItem>
                  <SelectItem value="cornerstone">Cornerstone</SelectItem>
                  <SelectItem value="custom">Custom</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>API URL</Label>
              <Input placeholder="https://api.example.com" />
            </div>

            <div className="space-y-2">
              <Label>API Key (Optional)</Label>
              <Input type="password" placeholder="Enter API key" />
            </div>

            <div className="flex gap-2">
              <Button 
                variant="outline" 
                onClick={() => setShowAddProvider(false)}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button 
                onClick={() => handleAddProvider({})}
                className="flex-1"
              >
                Add Provider
              </Button>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};