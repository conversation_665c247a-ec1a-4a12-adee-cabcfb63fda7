import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  BarChart3,
  Users,
  Calendar,
  DollarSign,
  Award,
  BookOpen,
  Activity,
  MessageSquare,
  Trophy,
  Map,
  TrendingUp,
  Settings,
  Grid,
  Plus,
  Download,
  Upload,
  RefreshCw,
  Bell,
  Search,
  Filter,
  MoreHorizontal,
  Eye,
  Edit,
  Trash2
} from 'lucide-react';

// Import shared components
import { 
  MetricCard, 
  StatusBadge, 
  NotificationCenter, 
  FormField, 
  SearchAndFilter, 
  PageHeader 
} from './shared';

// Mock data for demonstration
const mockTrainingData = {
  totalPrograms: 24,
  activeTrainees: 156,
  completedThisMonth: 89,
  upcomingCertifications: 12,
  budgetUtilization: 68,
  averageRating: 4.6,
  skillsGapCritical: 8,
  onTrackProgress: 92
};

const mockRecentActivities = [
  {
    id: '1',
    type: 'certification',
    title: 'AWS Solutions Architect Certification Achieved',
    description: '<PERSON> successfully completed AWS certification',
    timestamp: '2 hours ago',
    status: 'completed'
  },
  {
    id: '2',
    type: 'training',
    title: 'Leadership Workshop Scheduled',
    description: 'New session scheduled for January 28, 2024',
    timestamp: '4 hours ago',
    status: 'scheduled'
  },
  {
    id: '3',
    type: 'budget',
    title: 'Training Budget Approval Required',
    description: 'Data Science bootcamp requires $15,000 approval',
    timestamp: '6 hours ago',
    status: 'pending'
  },
  {
    id: '4',
    type: 'feedback',
    title: 'Training Feedback Received',
    description: 'Safety training received 4.8/5 average rating',
    timestamp: '8 hours ago',
    status: 'completed'
  }
];

const mockUpcomingTrainings = [
  {
    id: '1',
    title: 'Advanced React Development',
    instructor: 'Sarah Johnson',
    date: '2024-01-28',
    time: '09:00 AM',
    participants: 15,
    maxParticipants: 20,
    status: 'scheduled'
  },
  {
    id: '2',
    title: 'Project Management Fundamentals',
    instructor: 'Mike Chen',
    date: '2024-01-30',
    time: '02:00 PM',
    participants: 12,
    maxParticipants: 15,
    status: 'scheduled'
  },
  {
    id: '3',
    title: 'Cybersecurity Awareness',
    instructor: 'Lisa Rodriguez',
    date: '2024-02-02',
    time: '10:00 AM',
    participants: 25,
    maxParticipants: 30,
    status: 'scheduled'
  }
];

const EnhancedDashboardContent: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [searchValue, setSearchValue] = useState('');
  const [filters, setFilters] = useState([
    {
      id: 'status',
      label: 'Status',
      type: 'select' as const,
      options: [
        { value: 'active', label: 'Active' },
        { value: 'completed', label: 'Completed' },
        { value: 'pending', label: 'Pending' },
        { value: 'cancelled', label: 'Cancelled' }
      ]
    },
    {
      id: 'department',
      label: 'Department',
      type: 'multiselect' as const,
      options: [
        { value: 'engineering', label: 'Engineering' },
        { value: 'marketing', label: 'Marketing' },
        { value: 'sales', label: 'Sales' },
        { value: 'hr', label: 'Human Resources' },
        { value: 'finance', label: 'Finance' }
      ]
    },
    {
      id: 'dateRange',
      label: 'Date Range',
      type: 'date' as const
    }
  ]);

  const breadcrumbs = [
    { label: 'Training', href: '/training' },
    { label: 'Dashboard', icon: Grid }
  ];

  const headerActions = [
    {
      label: 'Import Data',
      onClick: () => console.log('Import data'),
      variant: 'outline' as const,
      icon: Upload
    },
    {
      label: 'Export Report',
      onClick: () => console.log('Export report'),
      variant: 'outline' as const,
      icon: Download
    },
    {
      label: 'New Training',
      onClick: () => console.log('New training'),
      icon: Plus
    }
  ];

  const renderOverviewTab = () => (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <MetricCard
          title="Active Programs"
          value={mockTrainingData.totalPrograms}
          description="Across all departments"
          icon={BookOpen}
          trend={{ value: 12, isPositive: true }}
          badge={{ text: 'Growing', variant: 'success' }}
        />
        <MetricCard
          title="Active Trainees"
          value={mockTrainingData.activeTrainees}
          description="Currently enrolled"
          icon={Users}
          progress={75}
        />
        <MetricCard
          title="Completed This Month"
          value={mockTrainingData.completedThisMonth}
          description="Successfully certified"
          icon={Award}
          trend={{ value: 8, isPositive: true }}
        />
        <MetricCard
          title="Budget Utilization"
          value={`${mockTrainingData.budgetUtilization}%`}
          description="Of allocated budget"
          icon={DollarSign}
          progress={mockTrainingData.budgetUtilization}
          badge={{ text: 'On Track', variant: 'success' }}
        />
      </div>

      {/* Secondary Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <MetricCard
          title="Average Rating"
          value={mockTrainingData.averageRating}
          description="Training satisfaction"
          icon={MessageSquare}
          badge={{ text: 'Excellent', variant: 'success' }}
        />
        <MetricCard
          title="Skills Gap (Critical)"
          value={mockTrainingData.skillsGapCritical}
          description="Require immediate attention"
          icon={TrendingUp}
          badge={{ text: 'Action Needed', variant: 'warning' }}
        />
        <MetricCard
          title="On Track Progress"
          value={`${mockTrainingData.onTrackProgress}%`}
          description="Meeting milestones"
          icon={Activity}
          progress={mockTrainingData.onTrackProgress}
        />
        <MetricCard
          title="Upcoming Certifications"
          value={mockTrainingData.upcomingCertifications}
          description="Next 30 days"
          icon={Calendar}
        />
      </div>

      {/* Recent Activities and Upcoming Trainings */}
      <div className="grid gap-6 lg:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Recent Activities
            </CardTitle>
            <CardDescription>Latest updates and achievements</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {mockRecentActivities.map((activity) => (
              <div key={activity.id} className="flex items-start gap-3 p-3 rounded-lg border bg-card/50">
                <div className="mt-1">
                  {activity.type === 'certification' && <Award className="h-4 w-4 text-green-500" />}
                  {activity.type === 'training' && <BookOpen className="h-4 w-4 text-blue-500" />}
                  {activity.type === 'budget' && <DollarSign className="h-4 w-4 text-yellow-500" />}
                  {activity.type === 'feedback' && <MessageSquare className="h-4 w-4 text-purple-500" />}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium">{activity.title}</p>
                  <p className="text-xs text-muted-foreground mt-1">{activity.description}</p>
                  <div className="flex items-center gap-2 mt-2">
                    <StatusBadge status={activity.status as any} />
                    <span className="text-xs text-muted-foreground">{activity.timestamp}</span>
                  </div>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Upcoming Trainings
            </CardTitle>
            <CardDescription>Scheduled sessions this week</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {mockUpcomingTrainings.map((training) => (
              <div key={training.id} className="flex items-start gap-3 p-3 rounded-lg border bg-card/50">
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium">{training.title}</p>
                  <p className="text-xs text-muted-foreground mt-1">
                    Instructor: {training.instructor}
                  </p>
                  <div className="flex items-center gap-4 mt-2">
                    <div className="flex items-center gap-1 text-xs text-muted-foreground">
                      <Calendar className="h-3 w-3" />
                      {training.date} at {training.time}
                    </div>
                    <div className="flex items-center gap-1 text-xs text-muted-foreground">
                      <Users className="h-3 w-3" />
                      {training.participants}/{training.maxParticipants}
                    </div>
                  </div>
                  <Progress 
                    value={(training.participants / training.maxParticipants) * 100} 
                    className="mt-2 h-1"
                  />
                </div>
                <div className="flex items-center gap-1">
                  <Button variant="ghost" size="sm">
                    <Eye className="h-3 w-3" />
                  </Button>
                  <Button variant="ghost" size="sm">
                    <Edit className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>Common tasks and shortcuts</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-4">
            <Button variant="outline" className="justify-start h-auto p-4" onClick={() => setActiveTab('schedule')}>
              <div className="flex flex-col items-start gap-2">
                <Calendar className="h-5 w-5 text-blue-500" />
                <div>
                  <p className="font-medium">Schedule Training</p>
                  <p className="text-xs text-muted-foreground">Create new session</p>
                </div>
              </div>
            </Button>
            <Button variant="outline" className="justify-start h-auto p-4" onClick={() => setActiveTab('certifications')}>
              <div className="flex flex-col items-start gap-2">
                <Award className="h-5 w-5 text-green-500" />
                <div>
                  <p className="font-medium">Track Certifications</p>
                  <p className="text-xs text-muted-foreground">Monitor progress</p>
                </div>
              </div>
            </Button>
            <Button variant="outline" className="justify-start h-auto p-4" onClick={() => setActiveTab('budget')}>
              <div className="flex flex-col items-start gap-2">
                <DollarSign className="h-5 w-5 text-yellow-500" />
                <div>
                  <p className="font-medium">Manage Budget</p>
                  <p className="text-xs text-muted-foreground">Allocate resources</p>
                </div>
              </div>
            </Button>
            <Button variant="outline" className="justify-start h-auto p-4" onClick={() => setActiveTab('reports')}>
              <div className="flex flex-col items-start gap-2">
                <BarChart3 className="h-5 w-5 text-purple-500" />
                <div>
                  <p className="font-medium">View Reports</p>
                  <p className="text-xs text-muted-foreground">Analytics & insights</p>
                </div>
              </div>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderPlaceholderTab = (tabName: string) => (
    <Card>
      <CardHeader>
        <CardTitle className="capitalize">{tabName} Management</CardTitle>
        <CardDescription>
          {tabName} management features will be implemented here
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col items-center justify-center py-12 text-center">
          <div className="rounded-full bg-muted p-6 mb-4">
            <Settings className="h-12 w-12 text-muted-foreground" />
          </div>
          <h3 className="text-lg font-semibold mb-2">Coming Soon</h3>
          <p className="text-muted-foreground max-w-md">
            The {tabName} management interface is currently under development. 
            Check back soon for comprehensive {tabName} tools and features.
          </p>
          <Button className="mt-4" variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="min-h-screen bg-background">
      <div className="container-responsive py-6 space-y-6">
        {/* Page Header */}
        <PageHeader
          title="Training Management System"
          description="Comprehensive training, certification, and development platform"
          breadcrumbs={breadcrumbs}
          actions={headerActions}
          badge={{ text: 'Enhanced', variant: 'success' }}
          icon={BookOpen}
        >
          <div className="flex items-center justify-between">
            <SearchAndFilter
              searchValue={searchValue}
              onSearchChange={setSearchValue}
              searchPlaceholder="Search trainings, trainees, or programs..."
              filters={filters}
              onFiltersChange={setFilters}
              className="flex-1 max-w-2xl"
            />
            <NotificationCenter className="ml-4" />
          </div>
        </PageHeader>

        {/* Main Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid grid-cols-6 lg:grid-cols-11 gap-1 h-auto p-1">
            <TabsTrigger value="overview" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">
              <Grid className="h-4 w-4 mr-2" />
              Overview
            </TabsTrigger>
            <TabsTrigger value="lms">
              <BookOpen className="h-4 w-4 mr-2" />
              LMS
            </TabsTrigger>
            <TabsTrigger value="schedule">
              <Calendar className="h-4 w-4 mr-2" />
              Schedule
            </TabsTrigger>
            <TabsTrigger value="certifications">
              <Award className="h-4 w-4 mr-2" />
              Certifications
            </TabsTrigger>
            <TabsTrigger value="budget">
              <DollarSign className="h-4 w-4 mr-2" />
              Budget
            </TabsTrigger>
            <TabsTrigger value="progress">
              <Activity className="h-4 w-4 mr-2" />
              Progress
            </TabsTrigger>
            <TabsTrigger value="feedback">
              <MessageSquare className="h-4 w-4 mr-2" />
              Feedback
            </TabsTrigger>
            <TabsTrigger value="gamification">
              <Trophy className="h-4 w-4 mr-2" />
              Gamification
            </TabsTrigger>
            <TabsTrigger value="reports">
              <BarChart3 className="h-4 w-4 mr-2" />
              Reports
            </TabsTrigger>
            <TabsTrigger value="pathways">
              <Map className="h-4 w-4 mr-2" />
              Pathways
            </TabsTrigger>
            <TabsTrigger value="skills">
              <TrendingUp className="h-4 w-4 mr-2" />
              Skills Gap
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            {renderOverviewTab()}
          </TabsContent>

          <TabsContent value="lms">
            {renderPlaceholderTab('LMS')}
          </TabsContent>

          <TabsContent value="schedule">
            {renderPlaceholderTab('Schedule')}
          </TabsContent>

          <TabsContent value="certifications">
            {renderPlaceholderTab('Certifications')}
          </TabsContent>

          <TabsContent value="budget">
            {renderPlaceholderTab('Budget')}
          </TabsContent>

          <TabsContent value="progress">
            {renderPlaceholderTab('Progress')}
          </TabsContent>

          <TabsContent value="feedback">
            {renderPlaceholderTab('Feedback')}
          </TabsContent>

          <TabsContent value="gamification">
            {renderPlaceholderTab('Gamification')}
          </TabsContent>

          <TabsContent value="reports">
            {renderPlaceholderTab('Reports')}
          </TabsContent>

          <TabsContent value="pathways">
            {renderPlaceholderTab('Pathways')}
          </TabsContent>

          <TabsContent value="skills">
            {renderPlaceholderTab('Skills Gap')}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export const EnhancedDashboard: React.FC = () => {
  return <EnhancedDashboardContent />;
};