/**
 * Agent Analytics Dashboard
 * Comprehensive analytics visualization for agent performance and insights
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  PieChart,
  Pie,
  RadarChart,
  Radar,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Cell,
  Area,
  AreaChart,
  Treemap
} from 'recharts';
import {
  Activity,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Clock,
  AlertTriangle,
  CheckCircle,
  XCircle,
  RefreshCw,
  Download,
  Filter,
  Calendar,
  BarChart3,
  Pie<PERSON>hart as PieChartIcon,
  Line<PERSON>hart as LineChartIcon,
  Brain,
  Zap,
  Target,
  Users,
  Settings,
  ChevronRight,
  ChevronDown,
  Info,
  AlertCircle,
  Bot
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import {
  Tooltip as UITooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { agentAnalytics } from '@/services/agentAnalyticsService';
import {
  AgentPerformanceMetrics,
  SessionAnalytics,
  WorkflowAnalytics,
  MLInsight,
  CostAnalytics,
  PerformanceTrend,
  RealTimeMetrics,
  UsagePattern,
  Anomaly,
  AnalyticsDashboardState
} from '@/types/agent-analytics';
import { cn } from '@/lib/utils';

interface AgentAnalyticsDashboardProps {
  sessionId?: string;
  agentId?: string;
  className?: string;
  onClose?: () => void;
}

export const AgentAnalyticsDashboard: React.FC<AgentAnalyticsDashboardProps> = ({
  sessionId,
  agentId,
  className,
  onClose
}) => {
  // State
  const [dashboardState, setDashboardState] = useState<AnalyticsDashboardState>({
    timeRange: {
      start: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
      end: new Date().toISOString(),
      preset: 'last_24h'
    },
    selectedMetrics: ['execution_time', 'success_rate', 'token_usage', 'cost'],
    selectedAgents: agentId ? [agentId] : [],
    selectedSessions: sessionId ? [sessionId] : [],
    groupBy: 'agent',
    aggregation: 'average',
    refreshInterval: 30000,
    autoRefresh: true
  });

  const [activeTab, setActiveTab] = useState<'overview' | 'performance' | 'costs' | 'insights' | 'patterns'>('overview');
  const [performanceMetrics, setPerformanceMetrics] = useState<AgentPerformanceMetrics[]>([]);
  const [sessionAnalytics, setSessionAnalytics] = useState<SessionAnalytics | null>(null);
  const [workflowAnalytics, setWorkflowAnalytics] = useState<WorkflowAnalytics[]>([]);
  const [mlInsights, setMlInsights] = useState<MLInsight[]>([]);
  const [costAnalytics, setCostAnalytics] = useState<CostAnalytics | null>(null);
  const [performanceTrends, setPerformanceTrends] = useState<PerformanceTrend[]>([]);
  const [realTimeMetrics, setRealTimeMetrics] = useState<RealTimeMetrics | null>(null);
  const [usagePatterns, setUsagePatterns] = useState<UsagePattern[]>([]);
  const [anomalies, setAnomalies] = useState<Anomaly[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [expandedInsights, setExpandedInsights] = useState<Set<string>>(new Set());

  // Load data
  useEffect(() => {
    loadAnalytics();
    
    if (dashboardState.autoRefresh) {
      const interval = setInterval(loadAnalytics, dashboardState.refreshInterval);
      return () => clearInterval(interval);
    }
  }, [dashboardState, sessionId, agentId]);

  // Listen for real-time updates
  useEffect(() => {
    const handleMetricsUpdate = (event: CustomEvent) => {
      setRealTimeMetrics(event.detail);
    };

    const handleAlert = (event: CustomEvent) => {
      handleAnalyticsAlert(event.detail);
    };

    window.addEventListener('analytics:metrics-update', handleMetricsUpdate as EventListener);
    window.addEventListener('analytics:alert', handleAlert as EventListener);

    return () => {
      window.removeEventListener('analytics:metrics-update', handleMetricsUpdate as EventListener);
      window.removeEventListener('analytics:alert', handleAlert as EventListener);
    };
  }, []);

  const loadAnalytics = async () => {
    setIsLoading(true);
    try {
      // Load all analytics data in parallel
      const promises = [];
      
      // Agent performance metrics
      if (dashboardState.selectedAgents.length > 0) {
        promises.push(
          Promise.all(
            dashboardState.selectedAgents.map(id =>
              agentAnalytics.getAgentPerformanceMetrics(id, dashboardState.timeRange)
            )
          ).then(setPerformanceMetrics)
        );
      }
      
      // Session analytics
      if (sessionId) {
        promises.push(
          agentAnalytics.getSessionAnalytics(sessionId).then(setSessionAnalytics)
        );
      }
      
      // ML insights
      promises.push(
        agentAnalytics.generateMLInsights({
          sessionId,
          agentId,
          timeRange: dashboardState.timeRange
        }).then(setMlInsights)
      );
      
      // Cost analytics
      promises.push(
        agentAnalytics.getCostAnalytics(
          'day',
          dashboardState.timeRange.start,
          dashboardState.timeRange.end
        ).then(setCostAnalytics)
      );
      
      // Performance trends
      promises.push(
        Promise.all(
          dashboardState.selectedMetrics.map(metric =>
            agentAnalytics.getPerformanceTrends(metric, 'hour', agentId)
          )
        ).then(setPerformanceTrends)
      );
      
      // Usage patterns
      if (dashboardState.selectedSessions.length > 0) {
        promises.push(
          agentAnalytics.detectUsagePatterns(dashboardState.selectedSessions)
            .then(setUsagePatterns)
        );
      }
      
      await Promise.all(promises);
    } catch (error) {
    } finally {
      setIsLoading(false);
    }
  };

  const handleAnalyticsAlert = (alert: any) => {
    // Handle analytics alerts
  };

  const handleExport = async () => {
    try {
      const exportData = await agentAnalytics.exportAnalytics('csv', {
        dateRange: dashboardState.timeRange,
        includedMetrics: dashboardState.selectedMetrics,
        includedAgents: dashboardState.selectedAgents,
        aggregationLevel: 'hourly'
      });
      
      // Download the exported file
      window.open(exportData.downloadUrl, '_blank');
    } catch (error) {
    }
  };

  const handleTimeRangeChange = (preset: string) => {
    const now = new Date();
    let start: Date;
    
    switch (preset) {
      case 'last_hour':
        start = new Date(now.getTime() - 60 * 60 * 1000);
        break;
      case 'last_24h':
        start = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case 'last_7d':
        start = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'last_30d':
        start = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      default:
        start = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    }
    
    setDashboardState(prev => ({
      ...prev,
      timeRange: {
        start: start.toISOString(),
        end: now.toISOString(),
        preset: preset as any
      }
    }));
  };

  // Calculate summary metrics
  const summaryMetrics = useMemo(() => {
    const totalExecutions = performanceMetrics.reduce((sum, m) => sum + m.totalExecutions, 0);
    const avgSuccessRate = performanceMetrics.length > 0
      ? performanceMetrics.reduce((sum, m) => sum + m.successRate, 0) / performanceMetrics.length
      : 0;
    const totalCost = costAnalytics?.totalCost || 0;
    const totalTokens = performanceMetrics.reduce((sum, m) => sum + m.totalTokensUsed, 0);
    
    return {
      totalExecutions,
      avgSuccessRate,
      totalCost,
      totalTokens,
      activeAgents: realTimeMetrics?.activeAgents || 0,
      activeSessions: realTimeMetrics?.activeSessions || 0,
      avgResponseTime: realTimeMetrics?.averageLatency || 0,
      errorRate: performanceMetrics.length > 0
        ? performanceMetrics.reduce((sum, m) => sum + m.errorRate, 0) / performanceMetrics.length
        : 0
    };
  }, [performanceMetrics, costAnalytics, realTimeMetrics]);

  // Render metric card
  const renderMetricCard = (
    title: string,
    value: string | number,
    change?: number,
    icon: React.ReactNode,
    trend?: 'up' | 'down' | 'stable'
  ) => (
    <Card className="relative overflow-hidden">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        {icon}
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        {change !== undefined && (
          <div className="flex items-center mt-2">
            {trend === 'up' ? (
              <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
            ) : trend === 'down' ? (
              <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
            ) : (
              <Activity className="h-4 w-4 text-gray-500 mr-1" />
            )}
            <span className={cn(
              "text-xs",
              trend === 'up' ? "text-green-500" : 
              trend === 'down' ? "text-red-500" : 
              "text-gray-500"
            )}>
              {change > 0 ? '+' : ''}{change.toFixed(1)}%
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  );

  // Render insights
  const renderInsights = () => (
    <div className="space-y-4">
      {mlInsights.length === 0 && (
        <Alert>
          <Info className="h-4 w-4" />
          <AlertTitle>No insights available</AlertTitle>
          <AlertDescription>
            ML insights will appear here as more data is collected.
          </AlertDescription>
        </Alert>
      )}
      
      {mlInsights.map(insight => (
        <Card key={insight.insightId} className="overflow-hidden">
          <CardHeader 
            className="cursor-pointer"
            onClick={() => {
              const newExpanded = new Set(expandedInsights);
              if (newExpanded.has(insight.insightId)) {
                newExpanded.delete(insight.insightId);
              } else {
                newExpanded.add(insight.insightId);
              }
              setExpandedInsights(newExpanded);
            }}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {insight.severity === 'critical' ? (
                  <AlertCircle className="h-5 w-5 text-red-500" />
                ) : insight.severity === 'warning' ? (
                  <AlertTriangle className="h-5 w-5 text-yellow-500" />
                ) : (
                  <Info className="h-5 w-5 text-blue-500" />
                )}
                <CardTitle className="text-base">{insight.title}</CardTitle>
                <Badge variant={
                  insight.type === 'optimization' ? 'default' :
                  insight.type === 'anomaly' ? 'destructive' :
                  insight.type === 'prediction' ? 'secondary' :
                  'outline'
                }>
                  {insight.type}
                </Badge>
                <Badge variant="outline">
                  {Math.round(insight.confidence * 100)}% confidence
                </Badge>
              </div>
              {expandedInsights.has(insight.insightId) ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </div>
          </CardHeader>
          
          <AnimatePresence>
            {expandedInsights.has(insight.insightId) && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: 'auto', opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                transition={{ duration: 0.2 }}
              >
                <CardContent className="pt-0">
                  <p className="text-sm text-muted-foreground mb-4">
                    {insight.description}
                  </p>
                  
                  {insight.evidence.length > 0 && (
                    <div className="mb-4">
                      <h4 className="text-sm font-semibold mb-2">Evidence</h4>
                      <div className="space-y-2">
                        {insight.evidence.map((e, i) => (
                          <div key={i} className="flex items-center justify-between text-sm">
                            <span>{e.metric}</span>
                            <div className="flex items-center gap-2">
                              <span className="font-mono">{e.value.toFixed(2)}</span>
                              <span className="text-muted-foreground">
                                (threshold: {e.threshold.toFixed(2)})
                              </span>
                              <Badge variant="outline" className="text-xs">
                                {e.trend}
                              </Badge>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  {insight.recommendations.length > 0 && (
                    <div>
                      <h4 className="text-sm font-semibold mb-2">Recommendations</h4>
                      <div className="space-y-2">
                        {insight.recommendations.map((rec, i) => (
                          <div key={i} className="flex items-start gap-2">
                            <Zap className="h-4 w-4 text-primary mt-0.5" />
                            <div className="flex-1">
                              <p className="text-sm">{rec.action}</p>
                              <div className="flex gap-2 mt-1">
                                <Badge variant="outline" className="text-xs">
                                  Impact: {rec.impact}
                                </Badge>
                                <Badge variant="outline" className="text-xs">
                                  Effort: {rec.effort}
                                </Badge>
                                <Badge variant="outline" className="text-xs">
                                  +{rec.estimatedImprovement}%
                                </Badge>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </motion.div>
            )}
          </AnimatePresence>
        </Card>
      ))}
    </div>
  );

  // Render performance chart
  const renderPerformanceChart = () => {
    const data = performanceTrends.find(t => t.metric === 'execution_time')?.dataPoints || [];
    
    return (
      <Card>
        <CardHeader>
          <CardTitle>Performance Trends</CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart data={data}>
              <defs>
                <linearGradient id="colorPerf" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#8884d8" stopOpacity={0.8}/>
                  <stop offset="95%" stopColor="#8884d8" stopOpacity={0}/>
                </linearGradient>
              </defs>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="timestamp" />
              <YAxis />
              <Tooltip />
              <Area 
                type="monotone" 
                dataKey="value" 
                stroke="#8884d8" 
                fillOpacity={1} 
                fill="url(#colorPerf)" 
              />
            </AreaChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    );
  };

  // Render cost breakdown
  const renderCostBreakdown = () => {
    if (!costAnalytics) return null;
    
    const data = Object.entries(costAnalytics.breakdown.byAgent).map(([agent, cost]) => ({
      name: agent,
      value: cost
    }));
    
    const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];
    
    return (
      <Card>
        <CardHeader>
          <CardTitle>Cost Breakdown by Agent</CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={data}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    );
  };

  // Render agent comparison
  const renderAgentComparison = () => {
    const data = performanceMetrics.map(m => ({
      agent: m.agentName,
      successRate: m.successRate * 100,
      avgTime: m.averageExecutionTime,
      cost: m.averageCost,
      tokens: m.averageTokensUsed
    }));
    
    return (
      <Card>
        <CardHeader>
          <CardTitle>Agent Comparison</CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <RadarChart data={data}>
              <PolarGrid />
              <PolarAngleAxis dataKey="agent" />
              <PolarRadiusAxis />
              <Radar 
                name="Success Rate" 
                dataKey="successRate" 
                stroke="#8884d8" 
                fill="#8884d8" 
                fillOpacity={0.6} 
              />
              <Tooltip />
            </RadarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className={cn("flex flex-col h-full bg-background", className)}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-3">
          <BarChart3 className="h-5 w-5 text-primary" />
          <h2 className="text-lg font-semibold">Agent Analytics</h2>
        </div>
        <div className="flex items-center gap-2">
          {/* Time Range Selector */}
          <Select 
            value={dashboardState.timeRange.preset} 
            onValueChange={handleTimeRangeChange}
          >
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="last_hour">Last Hour</SelectItem>
              <SelectItem value="last_24h">Last 24 Hours</SelectItem>
              <SelectItem value="last_7d">Last 7 Days</SelectItem>
              <SelectItem value="last_30d">Last 30 Days</SelectItem>
            </SelectContent>
          </Select>
          
          <Button 
            variant="outline" 
            size="icon"
            onClick={loadAnalytics}
            disabled={isLoading}
          >
            <RefreshCw className={cn("h-4 w-4", isLoading && "animate-spin")} />
          </Button>
          
          <Button 
            variant="outline" 
            size="icon"
            onClick={handleExport}
          >
            <Download className="h-4 w-4" />
          </Button>
          
          {onClose && (
            <Button 
              variant="ghost" 
              size="icon"
              onClick={onClose}
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      {/* Content */}
      <ScrollArea className="flex-1">
        <div className="p-4 space-y-4">
          {/* Summary Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {renderMetricCard(
              'Total Executions',
              summaryMetrics.totalExecutions.toLocaleString(),
              undefined,
              <Activity className="h-4 w-4 text-muted-foreground" />,
              'stable'
            )}
            {renderMetricCard(
              'Success Rate',
              `${(summaryMetrics.avgSuccessRate * 100).toFixed(1)}%`,
              summaryMetrics.avgSuccessRate > 0.95 ? 5 : -5,
              <CheckCircle className="h-4 w-4 text-muted-foreground" />,
              summaryMetrics.avgSuccessRate > 0.95 ? 'up' : 'down'
            )}
            {renderMetricCard(
              'Total Cost',
              `$${summaryMetrics.totalCost.toFixed(2)}`,
              costAnalytics?.costTrend === 'increasing' ? 10 : -10,
              <DollarSign className="h-4 w-4 text-muted-foreground" />,
              costAnalytics?.costTrend === 'increasing' ? 'up' : 'down'
            )}
            {renderMetricCard(
              'Avg Response Time',
              `${summaryMetrics.avgResponseTime.toFixed(0)}ms`,
              undefined,
              <Clock className="h-4 w-4 text-muted-foreground" />,
              'stable'
            )}
          </div>

          {/* Tabs */}
          <Tabs value={activeTab} onValueChange={(v) => setActiveTab(v as any)}>
            <TabsList>
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="performance">Performance</TabsTrigger>
              <TabsTrigger value="costs">Costs</TabsTrigger>
              <TabsTrigger value="insights">Insights</TabsTrigger>
              <TabsTrigger value="patterns">Patterns</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                {renderPerformanceChart()}
                {renderCostBreakdown()}
              </div>
              {renderAgentComparison()}
            </TabsContent>

            <TabsContent value="performance" className="space-y-4">
              {renderPerformanceChart()}
              {renderAgentComparison()}
            </TabsContent>

            <TabsContent value="costs" className="space-y-4">
              {renderCostBreakdown()}
              {costAnalytics?.savingsOpportunities && (
                <Card>
                  <CardHeader>
                    <CardTitle>Cost Savings Opportunities</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {costAnalytics.savingsOpportunities.map((opp, i) => (
                        <Alert key={i}>
                          <DollarSign className="h-4 w-4" />
                          <AlertTitle>{opp.opportunity}</AlertTitle>
                          <AlertDescription>
                            Potential savings: ${opp.potentialSavings.toFixed(2)}
                            <br />
                            {opp.implementation}
                          </AlertDescription>
                        </Alert>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="insights" className="space-y-4">
              {renderInsights()}
            </TabsContent>

            <TabsContent value="patterns" className="space-y-4">
              {usagePatterns.length === 0 ? (
                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertTitle>No patterns detected</AlertTitle>
                  <AlertDescription>
                    Usage patterns will appear here as more data is collected.
                  </AlertDescription>
                </Alert>
              ) : (
                <div className="space-y-4">
                  {usagePatterns.map(pattern => (
                    <Card key={pattern.patternId}>
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-base">{pattern.description}</CardTitle>
                          <div className="flex gap-2">
                            <Badge>{pattern.patternType}</Badge>
                            <Badge variant="outline">
                              {Math.round(pattern.confidence * 100)}% confidence
                            </Badge>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            <span className="text-sm text-muted-foreground">Frequency:</span>
                            <span className="text-sm font-medium">{pattern.frequency} times</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <span className="text-sm text-muted-foreground">Agents:</span>
                            <div className="flex gap-1">
                              {pattern.agents.map(a => (
                                <Badge key={a} variant="secondary" className="text-xs">
                                  {a}
                                </Badge>
                              ))}
                            </div>
                          </div>
                          {pattern.recommendations.length > 0 && (
                            <div>
                              <span className="text-sm text-muted-foreground">Recommendations:</span>
                              <ul className="mt-1 space-y-1">
                                {pattern.recommendations.map((rec, i) => (
                                  <li key={i} className="text-sm flex items-start gap-2">
                                    <ChevronRight className="h-3 w-3 mt-0.5" />
                                    {rec}
                                  </li>
                                ))}
                              </ul>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </TabsContent>
          </Tabs>
        </div>
      </ScrollArea>
    </div>
  );
};