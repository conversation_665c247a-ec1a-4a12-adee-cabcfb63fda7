/**
 * ML Insight Panel
 * Real-time ML-powered insights and recommendations for agent optimization
 */

import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Brain,
  Sparkles,
  TrendingUp,
  AlertTriangle,
  Lightbulb,
  Target,
  Zap,
  ChevronRight,
  RefreshCw,
  Settings,
  Info,
  CheckCircle,
  XCircle,
  Clock,
  DollarSign,
  BarChart3,
  Activity,
  Shield,
  Cpu,
  Database,
  GitBranch,
  Layers
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { agentAnalytics } from '@/services/agentAnalyticsService';
import {
  MLInsight,
  AgentRecommendation,
  Anomaly,
  BenchmarkComparison,
  UsagePattern
} from '@/types/agent-analytics';
import { cn } from '@/lib/utils';

interface MLInsightPanelProps {
  sessionId?: string;
  agentId?: string;
  context?: any;
  className?: string;
  onApplyInsight?: (insight: MLInsight) => void;
  onDismissInsight?: (insightId: string) => void;
}

export const MLInsightPanel: React.FC<MLInsightPanelProps> = ({
  sessionId,
  agentId,
  context,
  className,
  onApplyInsight,
  onDismissInsight
}) => {
  const [insights, setInsights] = useState<MLInsight[]>([]);
  const [recommendations, setRecommendations] = useState<AgentRecommendation[]>([]);
  const [anomalies, setAnomalies] = useState<Anomaly[]>([]);
  const [benchmarks, setBenchmarks] = useState<BenchmarkComparison[]>([]);
  const [patterns, setPatterns] = useState<UsagePattern[]>([]);
  const [activeTab, setActiveTab] = useState<'insights' | 'recommendations' | 'anomalies' | 'benchmarks'>('insights');
  const [isLoading, setIsLoading] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [selectedInsight, setSelectedInsight] = useState<MLInsight | null>(null);
  const [appliedInsights, setAppliedInsights] = useState<Set<string>>(new Set());
  const [dismissedInsights, setDismissedInsights] = useState<Set<string>>(new Set());

  // Load ML insights
  useEffect(() => {
    loadInsights();
    
    if (autoRefresh) {
      const interval = setInterval(loadInsights, 60000); // Refresh every minute
      return () => clearInterval(interval);
    }
  }, [sessionId, agentId, context, autoRefresh]);

  const loadInsights = async () => {
    setIsLoading(true);
    try {
      // Load all ML-powered data in parallel
      const [
        insightsData,
        recommendationsData,
        anomaliesData,
        benchmarksData
      ] = await Promise.all([
        agentAnalytics.generateMLInsights({
          sessionId,
          agentId,
          ...context
        }),
        context?.task ? agentAnalytics.getAgentRecommendations({
          task: context.task,
          sessionHistory: context.sessionHistory,
          projectContext: context.projectContext
        }) : Promise.resolve([]),
        agentAnalytics.detectAnomalies({ sessionId, agentId }),
        agentId ? agentAnalytics.getBenchmarkComparison(agentId, [
          'execution_time',
          'success_rate',
          'token_usage',
          'cost'
        ]) : Promise.resolve([])
      ]);

      // Filter out dismissed insights
      const filteredInsights = insightsData.filter(
        i => !dismissedInsights.has(i.insightId)
      );

      setInsights(filteredInsights);
      setRecommendations(recommendationsData);
      setAnomalies(anomaliesData as any);
      setBenchmarks(benchmarksData);
    } catch (error) {
    } finally {
      setIsLoading(false);
    }
  };

  const handleApplyInsight = useCallback(async (insight: MLInsight) => {
    try {
      // Mark as applied
      setAppliedInsights(prev => new Set(prev).add(insight.insightId));
      
      // Call parent handler if provided
      if (onApplyInsight) {
        await onApplyInsight(insight);
      }
      
      // Show success feedback
    } catch (error) {
      // Remove from applied if failed
      setAppliedInsights(prev => {
        const newSet = new Set(prev);
        newSet.delete(insight.insightId);
        return newSet;
      });
    }
  }, [onApplyInsight]);

  const handleDismissInsight = useCallback((insightId: string) => {
    setDismissedInsights(prev => new Set(prev).add(insightId));
    setInsights(prev => prev.filter(i => i.insightId !== insightId));
    
    if (onDismissInsight) {
      onDismissInsight(insightId);
    }
  }, [onDismissInsight]);

  // Get insight icon based on type
  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'optimization':
        return <Zap className="h-4 w-4" />;
      case 'anomaly':
        return <AlertTriangle className="h-4 w-4" />;
      case 'prediction':
        return <TrendingUp className="h-4 w-4" />;
      case 'recommendation':
        return <Lightbulb className="h-4 w-4" />;
      default:
        return <Brain className="h-4 w-4" />;
    }
  };

  // Get severity color
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'text-red-500';
      case 'warning':
        return 'text-yellow-500';
      case 'info':
        return 'text-blue-500';
      default:
        return 'text-gray-500';
    }
  };

  // Render insight card
  const renderInsightCard = (insight: MLInsight) => {
    const isApplied = appliedInsights.has(insight.insightId);
    const isSelected = selectedInsight?.insightId === insight.insightId;

    return (
      <Card 
        key={insight.insightId}
        className={cn(
          "cursor-pointer transition-all",
          isSelected && "ring-2 ring-primary",
          isApplied && "opacity-60"
        )}
        onClick={() => setSelectedInsight(isSelected ? null : insight)}
      >
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-2">
              <div className={getSeverityColor(insight.severity)}>
                {getInsightIcon(insight.type)}
              </div>
              <CardTitle className="text-sm font-medium">
                {insight.title}
              </CardTitle>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="text-xs">
                {Math.round(insight.confidence * 100)}%
              </Badge>
              {isApplied && (
                <Badge variant="secondary" className="text-xs">
                  Applied
                </Badge>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          <p className="text-xs text-muted-foreground mb-3">
            {insight.description}
          </p>
          
          {/* Quick metrics */}
          <div className="flex items-center gap-4 mb-3">
            {insight.evidence.slice(0, 2).map((e, i) => (
              <div key={i} className="flex items-center gap-1">
                <Activity className="h-3 w-3 text-muted-foreground" />
                <span className="text-xs">
                  {e.metric}: {e.value.toFixed(1)}
                </span>
              </div>
            ))}
          </div>
          
          {/* Action buttons */}
          <div className="flex gap-2">
            {!isApplied && insight.recommendations.length > 0 && (
              <Button
                size="sm"
                variant="default"
                onClick={(e) => {
                  e.stopPropagation();
                  handleApplyInsight(insight);
                }}
              >
                <CheckCircle className="h-3 w-3 mr-1" />
                Apply
              </Button>
            )}
            <Button
              size="sm"
              variant="outline"
              onClick={(e) => {
                e.stopPropagation();
                handleDismissInsight(insight.insightId);
              }}
            >
              <XCircle className="h-3 w-3 mr-1" />
              Dismiss
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  };

  // Render recommendation card
  const renderRecommendationCard = (rec: AgentRecommendation) => (
    <Card key={rec.context}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <CardTitle className="text-sm font-medium">
            Recommended: {rec.recommendedAgent}
          </CardTitle>
          <Badge variant="outline" className="text-xs">
            {Math.round(rec.confidence * 100)}% match
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <p className="text-xs text-muted-foreground mb-3">
          {rec.reasoning}
        </p>
        
        {/* Metrics */}
        <div className="grid grid-cols-3 gap-2 mb-3">
          <div className="text-center">
            <Clock className="h-3 w-3 mx-auto mb-1 text-muted-foreground" />
            <div className="text-xs font-medium">
              {(rec.estimatedDuration / 1000).toFixed(1)}s
            </div>
            <div className="text-xs text-muted-foreground">Duration</div>
          </div>
          <div className="text-center">
            <DollarSign className="h-3 w-3 mx-auto mb-1 text-muted-foreground" />
            <div className="text-xs font-medium">
              ${rec.estimatedCost.toFixed(3)}
            </div>
            <div className="text-xs text-muted-foreground">Cost</div>
          </div>
          <div className="text-center">
            <BarChart3 className="h-3 w-3 mx-auto mb-1 text-muted-foreground" />
            <div className="text-xs font-medium">
              {Math.round(rec.historicalSuccess * 100)}%
            </div>
            <div className="text-xs text-muted-foreground">Success</div>
          </div>
        </div>
        
        {/* Alternative agents */}
        {rec.alternativeAgents.length > 0 && (
          <div>
            <div className="text-xs text-muted-foreground mb-1">Alternatives:</div>
            <div className="flex gap-1 flex-wrap">
              {rec.alternativeAgents.map(alt => (
                <Badge key={alt.agentId} variant="secondary" className="text-xs">
                  {alt.agentId} ({Math.round(alt.confidence * 100)}%)
                </Badge>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );

  // Render anomaly card
  const renderAnomalyCard = (anomaly: Anomaly) => (
    <Card key={anomaly.anomalyId}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-2">
            <AlertTriangle className={cn(
              "h-4 w-4",
              getSeverityColor(anomaly.severity)
            )} />
            <CardTitle className="text-sm font-medium">
              {anomaly.type.replace('_', ' ')} anomaly
            </CardTitle>
          </div>
          <Badge 
            variant={anomaly.severity === 'critical' ? 'destructive' : 'outline'}
            className="text-xs"
          >
            {anomaly.severity}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-2">
          <div className="flex items-center justify-between text-xs">
            <span className="text-muted-foreground">Metric:</span>
            <span className="font-medium">{anomaly.metric}</span>
          </div>
          <div className="flex items-center justify-between text-xs">
            <span className="text-muted-foreground">Expected:</span>
            <span className="font-medium">{anomaly.expectedValue.toFixed(2)}</span>
          </div>
          <div className="flex items-center justify-between text-xs">
            <span className="text-muted-foreground">Actual:</span>
            <span className="font-medium text-red-500">
              {anomaly.actualValue.toFixed(2)}
            </span>
          </div>
          <div className="flex items-center justify-between text-xs">
            <span className="text-muted-foreground">Deviation:</span>
            <span className="font-medium">
              {anomaly.standardDeviations.toFixed(1)}σ
            </span>
          </div>
        </div>
        
        {anomaly.possibleCauses.length > 0 && (
          <div className="mt-3">
            <div className="text-xs text-muted-foreground mb-1">Possible causes:</div>
            <ul className="space-y-1">
              {anomaly.possibleCauses.map((cause, i) => (
                <li key={i} className="text-xs flex items-start gap-1">
                  <ChevronRight className="h-3 w-3 mt-0.5" />
                  {cause}
                </li>
              ))}
            </ul>
          </div>
        )}
      </CardContent>
    </Card>
  );

  // Render benchmark comparison
  const renderBenchmarkCard = (benchmark: BenchmarkComparison) => (
    <Card key={`${benchmark.agentId}_${benchmark.metric}`}>
      <CardHeader className="pb-3">
        <CardTitle className="text-sm font-medium">
          {benchmark.metric.replace('_', ' ')}
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-3">
          {/* Performance bar */}
          <div>
            <div className="flex justify-between text-xs mb-1">
              <span>Your Performance</span>
              <span className="font-medium">
                {benchmark.yourValue.toFixed(2)}
              </span>
            </div>
            <div className="relative">
              <Progress 
                value={benchmark.percentile} 
                className="h-2"
              />
              <div 
                className="absolute top-0 h-2 w-0.5 bg-primary"
                style={{ left: `${benchmark.percentile}%` }}
              />
            </div>
            <div className="flex justify-between text-xs mt-1 text-muted-foreground">
              <span>0th</span>
              <span>50th</span>
              <span>100th percentile</span>
            </div>
          </div>
          
          {/* Comparison */}
          <div className="flex items-center justify-between text-xs">
            <span className="text-muted-foreground">Benchmark:</span>
            <span className="font-medium">{benchmark.benchmarkValue.toFixed(2)}</span>
          </div>
          
          {/* Performance indicator */}
          <div className="flex items-center justify-between">
            <Badge 
              variant={
                benchmark.performance === 'above' ? 'default' :
                benchmark.performance === 'average' ? 'secondary' :
                'destructive'
              }
              className="text-xs"
            >
              {benchmark.performance} average
            </Badge>
            {benchmark.improvementPotential > 0 && (
              <span className="text-xs text-muted-foreground">
                {benchmark.improvementPotential}% improvement possible
              </span>
            )}
          </div>
          
          {/* Best practices */}
          {benchmark.bestPractices.length > 0 && (
            <div>
              <div className="text-xs text-muted-foreground mb-1">Best practices:</div>
              <ul className="space-y-1">
                {benchmark.bestPractices.slice(0, 2).map((practice, i) => (
                  <li key={i} className="text-xs flex items-start gap-1">
                    <CheckCircle className="h-3 w-3 mt-0.5 text-green-500" />
                    {practice}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className={cn("flex flex-col h-full bg-background", className)}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-2">
          <Brain className="h-5 w-5 text-primary" />
          <h3 className="text-lg font-semibold">ML Insights</h3>
          {insights.length > 0 && (
            <Badge variant="secondary">
              {insights.length} new
            </Badge>
          )}
        </div>
        <div className="flex items-center gap-2">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setAutoRefresh(!autoRefresh)}
                >
                  <RefreshCw className={cn(
                    "h-4 w-4",
                    autoRefresh && "text-primary"
                  )} />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Auto-refresh: {autoRefresh ? 'On' : 'Off'}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          
          <Button
            variant="outline"
            size="icon"
            onClick={loadInsights}
            disabled={isLoading}
          >
            <RefreshCw className={cn(
              "h-4 w-4",
              isLoading && "animate-spin"
            )} />
          </Button>
        </div>
      </div>

      {/* Content */}
      <Tabs value={activeTab} onValueChange={(v) => setActiveTab(v as any)} className="flex-1">
        <TabsList className="px-4">
          <TabsTrigger value="insights" className="gap-2">
            <Sparkles className="h-3 w-3" />
            Insights
            {insights.length > 0 && (
              <Badge variant="secondary" className="ml-1 h-4 px-1 text-xs">
                {insights.length}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="recommendations" className="gap-2">
            <Target className="h-3 w-3" />
            Recommendations
          </TabsTrigger>
          <TabsTrigger value="anomalies" className="gap-2">
            <AlertTriangle className="h-3 w-3" />
            Anomalies
          </TabsTrigger>
          <TabsTrigger value="benchmarks" className="gap-2">
            <BarChart3 className="h-3 w-3" />
            Benchmarks
          </TabsTrigger>
        </TabsList>

        <TabsContent value="insights" className="flex-1 p-4">
          <ScrollArea className="h-full">
            {insights.length === 0 ? (
              <Alert>
                <Info className="h-4 w-4" />
                <AlertTitle>No insights available</AlertTitle>
                <AlertDescription>
                  ML insights will appear here as patterns are detected in your agent usage.
                </AlertDescription>
              </Alert>
            ) : (
              <div className="space-y-3">
                {insights.map(renderInsightCard)}
              </div>
            )}
          </ScrollArea>
        </TabsContent>

        <TabsContent value="recommendations" className="flex-1 p-4">
          <ScrollArea className="h-full">
            {recommendations.length === 0 ? (
              <Alert>
                <Info className="h-4 w-4" />
                <AlertTitle>No recommendations available</AlertTitle>
                <AlertDescription>
                  Agent recommendations will appear here based on your current context.
                </AlertDescription>
              </Alert>
            ) : (
              <div className="space-y-3">
                {recommendations.map(renderRecommendationCard)}
              </div>
            )}
          </ScrollArea>
        </TabsContent>

        <TabsContent value="anomalies" className="flex-1 p-4">
          <ScrollArea className="h-full">
            {anomalies.length === 0 ? (
              <Alert>
                <Info className="h-4 w-4" />
                <AlertTitle>No anomalies detected</AlertTitle>
                <AlertDescription>
                  The system is operating within normal parameters.
                </AlertDescription>
              </Alert>
            ) : (
              <div className="space-y-3">
                {anomalies.map(renderAnomalyCard)}
              </div>
            )}
          </ScrollArea>
        </TabsContent>

        <TabsContent value="benchmarks" className="flex-1 p-4">
          <ScrollArea className="h-full">
            {benchmarks.length === 0 ? (
              <Alert>
                <Info className="h-4 w-4" />
                <AlertTitle>No benchmarks available</AlertTitle>
                <AlertDescription>
                  Benchmark comparisons will appear here once enough data is collected.
                </AlertDescription>
              </Alert>
            ) : (
              <div className="space-y-3">
                {benchmarks.map(renderBenchmarkCard)}
              </div>
            )}
          </ScrollArea>
        </TabsContent>
      </Tabs>

      {/* Selected insight details */}
      <AnimatePresence>
        {selectedInsight && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="border-t"
          >
            <div className="p-4 space-y-3">
              <h4 className="text-sm font-semibold">Insight Details</h4>
              
              {/* Evidence */}
              <div>
                <div className="text-xs text-muted-foreground mb-2">Evidence</div>
                <div className="space-y-2">
                  {selectedInsight.evidence.map((e, i) => (
                    <div key={i} className="flex items-center justify-between p-2 bg-muted/50 rounded">
                      <span className="text-xs font-medium">{e.metric}</span>
                      <div className="flex items-center gap-3">
                        <span className="text-xs">Value: {e.value.toFixed(2)}</span>
                        <span className="text-xs text-muted-foreground">
                          Threshold: {e.threshold.toFixed(2)}
                        </span>
                        <Badge variant="outline" className="text-xs">
                          {e.trend}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              
              {/* Recommendations */}
              <div>
                <div className="text-xs text-muted-foreground mb-2">Recommended Actions</div>
                <div className="space-y-2">
                  {selectedInsight.recommendations.map((rec, i) => (
                    <div key={i} className="p-2 bg-muted/50 rounded">
                      <div className="text-xs font-medium mb-1">{rec.action}</div>
                      <div className="flex gap-2">
                        <Badge variant="outline" className="text-xs">
                          Impact: {rec.impact}
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          Effort: {rec.effort}
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          +{rec.estimatedImprovement}%
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};