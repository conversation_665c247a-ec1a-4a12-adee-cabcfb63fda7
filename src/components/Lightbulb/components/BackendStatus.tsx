import React, { useState, useEffect } from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { iconMapping } from '@/lib/icon-mapping';
import { invokeTauriCommand } from '../utils/tauriCommands';

interface BackendStatusProps {
  className?: string;
}

export const BackendStatus: React.FC<BackendStatusProps> = ({ className }) => {
  const [isConnected, setIsConnected] = useState<boolean | null>(null);
  const [lastError, setLastError] = useState<string | null>(null);
  const [itemCount, setItemCount] = useState<number>(0);

  useEffect(() => {
    checkBackendConnection();
    
    // Listen for command errors
    const handleError = (event: CustomEvent) => {
      setLastError(`${event.detail.command}: ${event.detail.error}`);
      setTimeout(() => setLastError(null), 5000); // Clear after 5 seconds
    };
    
    window.addEventListener('tauri-command-error' as any, handleError);
    return () => {
      window.removeEventListener('tauri-command-error' as any, handleError);
    };
  }, []);

  const checkBackendConnection = async () => {
    try {
      // Try to get user count as a connection test
      const response = await invokeTauriCommand('get_user_count');
      setIsConnected(true);
      setItemCount(response?.count || 0);
    } catch (error) {
      setIsConnected(false);
    }
  };

  if (isConnected === null) {
    return (
      <div className={className}>
        <Badge variant="outline" className="animate-pulse">
          <iconMapping.Sync className="h-3 w-3 mr-1 animate-spin" />
          Connecting...
        </Badge>
      </div>
    );
  }

  return (
    <div className={className}>
      {/* Connection Status */}
      <div className="flex items-center gap-2">
        <Badge variant={isConnected ? "default" : "destructive"}>
          {isConnected ? (
            <>
              <iconMapping.CheckCircle className="h-3 w-3 mr-1" />
              Backend Connected
            </>
          ) : (
            <>
              <iconMapping.Error className="h-3 w-3 mr-1" />
              Backend Offline
            </>
          )}
        </Badge>
        
        {isConnected && itemCount > 0 && (
          <Badge variant="secondary">
            {itemCount} items
          </Badge>
        )}
      </div>

      {/* Error Alert */}
      {lastError && (
        <Alert variant="destructive" className="mt-2">
          <iconMapping.Error className="h-4 w-4" />
          <AlertDescription>{lastError}</AlertDescription>
        </Alert>
      )}

      {/* Offline Mode Warning */}
      {!isConnected && (
        <Alert variant="warning" className="mt-2">
          <iconMapping.Info className="h-4 w-4" />
          <AlertDescription>
            Working in offline mode. Data will not be saved.
            <button 
              onClick={checkBackendConnection}
              className="ml-2 underline hover:no-underline"
            >
              Retry connection
            </button>
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
};

export default BackendStatus;