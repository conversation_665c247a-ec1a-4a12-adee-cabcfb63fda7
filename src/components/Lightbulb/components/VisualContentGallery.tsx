import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, Tabs<PERSON>ontent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Image,
  Video,
  Search,
  Eye,
  Download,
  Trash2,
  RefreshCw,
  FileImage,
  Film,
  Type,
  Box,
  Loader2
} from 'lucide-react';
import { invoke } from '@tauri-apps/api/core';
import { toast } from 'sonner';

interface VisualContent {
  id: string;
  source: string;
  content_type: string;
  dimensions?: [number, number];
  format?: string;
  color_mode?: string;
  extracted_text?: string;
  detected_objects?: DetectedObject[];
  thumbnail_base64?: string;
  processed_at: string;
}

interface VideoContent {
  id: string;
  source: string;
  duration_seconds: number;
  fps: number;
  resolution: [number, number];
  codec: string;
  transcript?: string;
  processed_at: string;
}

interface DetectedObject {
  label: string;
  confidence: number;
  bounding_box: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
}

const VisualContentGallery: React.FC = () => {
  const [visualContent, setVisualContent] = useState<VisualContent[]>([]);
  const [videoContent, setVideoContent] = useState<VideoContent[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedContent, setSelectedContent] = useState<VisualContent | VideoContent | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('images');

  useEffect(() => {
    loadVisualContent();
  }, []);

  const loadVisualContent = async () => {
    setIsLoading(true);
    try {
      const content = await invoke<VisualContent[]>('get_visual_content', { limit: 100 });
      setVisualContent(content.filter(c => c.content_type === 'Image'));
      setVideoContent(content.filter(c => c.content_type === 'Video') as VideoContent[]);
    } catch (error) {
      console.error('Failed to load visual content:', error);
      toast.error('Failed to load visual content');
    } finally {
      setIsLoading(false);
    }
  };

  const searchContent = async () => {
    if (!searchQuery.trim()) {
      loadVisualContent();
      return;
    }

    setIsLoading(true);
    try {
      const results = await invoke<VisualContent[]>('search_visual_content', { query: searchQuery });
      setVisualContent(results.filter(c => c.content_type === 'Image'));
      setVideoContent(results.filter(c => c.content_type === 'Video') as VideoContent[]);
      toast.success(`Found ${results.length} results`);
    } catch (error) {
      console.error('Search failed:', error);
      toast.error('Search failed');
    } finally {
      setIsLoading(false);
    }
  };

  const renderImageCard = (content: VisualContent) => (
    <Card 
      key={content.id} 
      className="cursor-pointer hover:shadow-lg transition-shadow"
      onClick={() => setSelectedContent(content)}
    >
      <CardContent className="p-4">
        {content.thumbnail_base64 ? (
          <img 
            src={`data:image/png;base64,${content.thumbnail_base64}`}
            alt={content.source}
            className="w-full h-48 object-cover rounded mb-2"
          />
        ) : (
          <div className="w-full h-48 bg-gray-200 rounded mb-2 flex items-center justify-center">
            <FileImage className="w-12 h-12 text-gray-400" />
          </div>
        )}
        <div className="space-y-1">
          <p className="text-sm font-medium truncate">{content.source.split('/').pop()}</p>
          <div className="flex items-center gap-2">
            <Badge variant="secondary" className="text-xs">
              {content.format}
            </Badge>
            {content.dimensions && (
              <span className="text-xs text-muted-foreground">
                {content.dimensions[0]}x{content.dimensions[1]}
              </span>
            )}
          </div>
          {content.extracted_text && (
            <div className="flex items-center gap-1">
              <Type className="w-3 h-3" />
              <span className="text-xs text-muted-foreground">Has text</span>
            </div>
          )}
          {content.detected_objects && content.detected_objects.length > 0 && (
            <div className="flex items-center gap-1">
              <Box className="w-3 h-3" />
              <span className="text-xs text-muted-foreground">
                {content.detected_objects.length} objects
              </span>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );

  const renderVideoCard = (content: VideoContent) => (
    <Card 
      key={content.id}
      className="cursor-pointer hover:shadow-lg transition-shadow"
      onClick={() => setSelectedContent(content)}
    >
      <CardContent className="p-4">
        <div className="w-full h-48 bg-gray-900 rounded mb-2 flex items-center justify-center">
          <Film className="w-12 h-12 text-gray-400" />
        </div>
        <div className="space-y-1">
          <p className="text-sm font-medium truncate">{content.source.split('/').pop()}</p>
          <div className="flex items-center gap-2">
            <Badge variant="secondary" className="text-xs">
              {content.codec}
            </Badge>
            <span className="text-xs text-muted-foreground">
              {content.resolution[0]}x{content.resolution[1]}
            </span>
          </div>
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <span>{Math.floor(content.duration_seconds / 60)}:{String(Math.floor(content.duration_seconds % 60)).padStart(2, '0')}</span>
            <span>•</span>
            <span>{content.fps} fps</span>
          </div>
          {content.transcript && (
            <div className="flex items-center gap-1">
              <Type className="w-3 h-3" />
              <span className="text-xs text-muted-foreground">Has transcript</span>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Visual Content Gallery</h2>
          <p className="text-muted-foreground">
            Browse and search processed images and videos
          </p>
        </div>
        <Button onClick={loadVisualContent} variant="outline">
          <RefreshCw className="w-4 h-4 mr-2" />
          Refresh
        </Button>
      </div>

      <div className="flex gap-2">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
          <Input
            placeholder="Search by text, objects, or filename..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && searchContent()}
            className="pl-10"
          />
        </div>
        <Button onClick={searchContent} disabled={isLoading}>
          {isLoading ? (
            <Loader2 className="w-4 h-4 animate-spin" />
          ) : (
            'Search'
          )}
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="images" className="flex items-center gap-2">
            <Image className="w-4 h-4" />
            Images ({visualContent.length})
          </TabsTrigger>
          <TabsTrigger value="videos" className="flex items-center gap-2">
            <Video className="w-4 h-4" />
            Videos ({videoContent.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="images">
          <ScrollArea className="h-[600px]">
            {isLoading ? (
              <div className="flex items-center justify-center h-40">
                <Loader2 className="w-8 h-8 animate-spin" />
              </div>
            ) : visualContent.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                {visualContent.map(renderImageCard)}
              </div>
            ) : (
              <div className="text-center py-12 text-muted-foreground">
                No images found. Process some images to see them here.
              </div>
            )}
          </ScrollArea>
        </TabsContent>

        <TabsContent value="videos">
          <ScrollArea className="h-[600px]">
            {isLoading ? (
              <div className="flex items-center justify-center h-40">
                <Loader2 className="w-8 h-8 animate-spin" />
              </div>
            ) : videoContent.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {videoContent.map(renderVideoCard)}
              </div>
            ) : (
              <div className="text-center py-12 text-muted-foreground">
                No videos found. Process some videos to see them here.
              </div>
            )}
          </ScrollArea>
        </TabsContent>
      </Tabs>

      {/* Detail Dialog */}
      <Dialog open={!!selectedContent} onOpenChange={() => setSelectedContent(null)}>
        <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {selectedContent?.source.split('/').pop()}
            </DialogTitle>
            <DialogDescription>
              Processed on {new Date(selectedContent?.processed_at || '').toLocaleString()}
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            {selectedContent && 'thumbnail_base64' in selectedContent && selectedContent.thumbnail_base64 && (
              <img 
                src={`data:image/png;base64,${selectedContent.thumbnail_base64}`}
                alt="Preview"
                className="w-full rounded"
              />
            )}
            
            {selectedContent && (
              <div className="space-y-2">
                <div>
                  <h4 className="font-semibold mb-1">Details</h4>
                  <div className="text-sm space-y-1">
                    {'dimensions' in selectedContent && selectedContent.dimensions && (
                      <p>Dimensions: {selectedContent.dimensions[0]}x{selectedContent.dimensions[1]}</p>
                    )}
                    {'format' in selectedContent && (
                      <p>Format: {selectedContent.format}</p>
                    )}
                    {'color_mode' in selectedContent && (
                      <p>Color Mode: {selectedContent.color_mode}</p>
                    )}
                    {'duration_seconds' in selectedContent && (
                      <p>Duration: {selectedContent.duration_seconds} seconds</p>
                    )}
                    {'fps' in selectedContent && (
                      <p>FPS: {selectedContent.fps}</p>
                    )}
                    {'codec' in selectedContent && (
                      <p>Codec: {selectedContent.codec}</p>
                    )}
                  </div>
                </div>
                
                {'extracted_text' in selectedContent && selectedContent.extracted_text && (
                  <div>
                    <h4 className="font-semibold mb-1">Extracted Text</h4>
                    <p className="text-sm bg-muted p-3 rounded">
                      {selectedContent.extracted_text}
                    </p>
                  </div>
                )}
                
                {'transcript' in selectedContent && selectedContent.transcript && (
                  <div>
                    <h4 className="font-semibold mb-1">Transcript</h4>
                    <p className="text-sm bg-muted p-3 rounded">
                      {selectedContent.transcript}
                    </p>
                  </div>
                )}
                
                {'detected_objects' in selectedContent && selectedContent.detected_objects && selectedContent.detected_objects.length > 0 && (
                  <div>
                    <h4 className="font-semibold mb-1">Detected Objects</h4>
                    <div className="flex gap-2 flex-wrap">
                      {selectedContent.detected_objects.map((obj, idx) => (
                        <Badge key={idx} variant="outline">
                          {obj.label} ({Math.round(obj.confidence * 100)}%)
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default VisualContentGallery;