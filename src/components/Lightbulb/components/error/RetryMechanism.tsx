import React, { useState, useCallback, useEffect } from 'react';
import { RefreshCw, AlertCircle, Wifi, WifiOff } from 'lucide-react';

interface RetryConfig {
  maxAttempts?: number;
  baseDelay?: number;
  maxDelay?: number;
  backoffFactor?: number;
  onRetry?: (attempt: number) => void;
  onMaxAttemptsReached?: () => void;
}

interface RetryMechanismProps {
  onRetry: () => Promise<void> | void;
  config?: RetryConfig;
  error?: Error | string;
  isLoading?: boolean;
  showRetryButton?: boolean;
  className?: string;
  children?: React.ReactNode;
}

const defaultConfig: Required<RetryConfig> = {
  maxAttempts: 3,
  baseDelay: 1000,
  maxDelay: 10000,
  backoffFactor: 2,
  onRetry: () => {},
  onMaxAttemptsReached: () => {}
};

export const RetryMechanism: React.FC<RetryMechanismProps> = ({
  onRetry,
  config = {},
  error,
  isLoading = false,
  showRetryButton = true,
  className = '',
  children
}) => {
  const [attemptCount, setAttemptCount] = useState(0);
  const [isRetrying, setIsRetrying] = useState(false);
  const [nextRetryIn, setNextRetryIn] = useState(0);
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  const finalConfig = { ...defaultConfig, ...config };

  // Monitor online status
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Countdown timer for automatic retry
  useEffect(() => {
    let intervalId: NodeJS.Timeout;

    if (nextRetryIn > 0) {
      intervalId = setInterval(() => {
        setNextRetryIn(prev => {
          if (prev <= 1) {
            handleRetry();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [nextRetryIn]);

  const calculateDelay = useCallback((attempt: number): number => {
    const delay = finalConfig.baseDelay * Math.pow(finalConfig.backoffFactor, attempt - 1);
    return Math.min(delay, finalConfig.maxDelay);
  }, [finalConfig]);

  const handleRetry = useCallback(async () => {
    if (isRetrying || attemptCount >= finalConfig.maxAttempts) {
      return;
    }

    const newAttemptCount = attemptCount + 1;
    setAttemptCount(newAttemptCount);
    setIsRetrying(true);
    setNextRetryIn(0);

    try {
      finalConfig.onRetry(newAttemptCount);
      await onRetry();
      // Reset on success
      setAttemptCount(0);
    } catch (retryError) {
      console.error(`Retry attempt ${newAttemptCount} failed:`, retryError);
      
      if (newAttemptCount >= finalConfig.maxAttempts) {
        finalConfig.onMaxAttemptsReached();
      } else {
        // Schedule next retry
        const delay = calculateDelay(newAttemptCount);
        setNextRetryIn(Math.ceil(delay / 1000));
      }
    } finally {
      setIsRetrying(false);
    }
  }, [attemptCount, finalConfig, onRetry, calculateDelay, isRetrying]);

  const handleManualRetry = useCallback(() => {
    setAttemptCount(0);
    setNextRetryIn(0);
    handleRetry();
  }, [handleRetry]);

  const canRetry = attemptCount < finalConfig.maxAttempts && !isRetrying && !isLoading;
  const hasReachedMaxAttempts = attemptCount >= finalConfig.maxAttempts;

  if (!error && !isLoading) {
    return <>{children}</>;
  }

  return (
    <div className={`text-center p-6 ${className}`}>
      {/* Error state */}
      {error && (
        <div className="mb-4">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
            {!isOnline ? (
              <WifiOff className="h-6 w-6 text-red-600" />
            ) : (
              <AlertCircle className="h-6 w-6 text-red-600" />
            )}
          </div>
          
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {!isOnline ? 'No Internet Connection' : 'Something went wrong'}
          </h3>
          
          <p className="text-sm text-gray-600 mb-4">
            {!isOnline 
              ? 'Please check your internet connection and try again.'
              : typeof error === 'string' 
                ? error 
                : error.message || 'An unexpected error occurred.'
            }
          </p>

          {attemptCount > 0 && (
            <p className="text-xs text-gray-500 mb-4">
              Attempt {attemptCount} of {finalConfig.maxAttempts}
            </p>
          )}
        </div>
      )}

      {/* Loading state */}
      {(isLoading || isRetrying) && (
        <div className="mb-4">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 mb-4">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          </div>
          <p className="text-sm text-gray-600">
            {isRetrying ? `Retrying... (Attempt ${attemptCount})` : 'Loading...'}
          </p>
        </div>
      )}

      {/* Retry controls */}
      {error && !isLoading && showRetryButton && (
        <div className="space-y-3">
          {/* Manual retry button */}
          {canRetry && (
            <button
              onClick={handleManualRetry}
              disabled={!isOnline}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </button>
          )}

          {/* Automatic retry countdown */}
          {nextRetryIn > 0 && canRetry && (
            <div className="text-sm text-gray-600">
              <p>Retrying automatically in {nextRetryIn} second{nextRetryIn !== 1 ? 's' : ''}...</p>
              <button
                onClick={handleManualRetry}
                className="text-blue-600 hover:text-blue-700 underline mt-1"
              >
                Retry now
              </button>
            </div>
          )}

          {/* Max attempts reached */}
          {hasReachedMaxAttempts && (
            <div className="text-sm text-gray-600">
              <p className="mb-2">Maximum retry attempts reached.</p>
              <button
                onClick={() => {
                  setAttemptCount(0);
                  setNextRetryIn(0);
                }}
                className="text-blue-600 hover:text-blue-700 underline"
              >
                Reset and try again
              </button>
            </div>
          )}

          {/* Offline indicator */}
          {!isOnline && (
            <div className="flex items-center justify-center text-sm text-gray-600 mt-4">
              <WifiOff className="h-4 w-4 mr-2" />
              <span>You're currently offline</span>
            </div>
          )}
        </div>
      )}

      {/* Success state - render children */}
      {!error && !isLoading && children}
    </div>
  );
};

// Hook for using retry mechanism
export const useRetryMechanism = (config?: RetryConfig) => {
  const [error, setError] = useState<Error | string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [attemptCount, setAttemptCount] = useState(0);

  const finalConfig = { ...defaultConfig, ...config };

  const executeWithRetry = useCallback(async (
    operation: () => Promise<any>,
    onSuccess?: (result: any) => void,
    onError?: (error: Error) => void
  ): Promise<any> => {
    setIsLoading(true);
    setError(null);

    for (let attempt = 1; attempt <= finalConfig.maxAttempts; attempt++) {
      try {
        setAttemptCount(attempt);
        finalConfig.onRetry(attempt);
        
        const result = await operation();
        
        // Success
        setError(null);
        setAttemptCount(0);
        onSuccess?.(result);
        return result;
      } catch (err) {
        const error = err instanceof Error ? err : new Error(String(err));
        
        if (attempt === finalConfig.maxAttempts) {
          // Final attempt failed
          setError(error);
          finalConfig.onMaxAttemptsReached();
          onError?.(error);
          return null;
        } else {
          // Wait before next attempt
          const delay = finalConfig.baseDelay * Math.pow(finalConfig.backoffFactor, attempt - 1);
          await new Promise(resolve => setTimeout(resolve, Math.min(delay, finalConfig.maxDelay)));
        }
      }
    }

    return null;
  }, [finalConfig]);

  const reset = useCallback(() => {
    setError(null);
    setIsLoading(false);
    setAttemptCount(0);
  }, []);

  useEffect(() => {
    setIsLoading(false);
  }, []);

  return {
    executeWithRetry,
    error,
    isLoading: isLoading,
    attemptCount,
    reset,
    canRetry: attemptCount < finalConfig.maxAttempts
  };
};

export default RetryMechanism;