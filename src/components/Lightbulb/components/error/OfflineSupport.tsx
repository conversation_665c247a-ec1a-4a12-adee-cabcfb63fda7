import React, { useState, useEffect, useCallback } from 'react';
import { WifiOff, Wifi, RefreshCw, AlertCircle } from 'lucide-react';

interface OfflineSupportProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  showBanner?: boolean;
  enableQueueing?: boolean;
  onOnline?: () => void;
  onOffline?: () => void;
}

interface QueuedAction {
  id: string;
  action: () => Promise<void>;
  description: string;
  timestamp: number;
}

// Hook for online/offline status
export const useOnlineStatus = () => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [wasOffline, setWasOffline] = useState(false);

  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      if (wasOffline) {
        setWasOffline(false);
      }
    };

    const handleOffline = () => {
      setIsOnline(false);
      setWasOffline(true);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [wasOffline]);

  return { isOnline, wasOffline };
};

// Hook for action queuing when offline
export const useOfflineQueue = () => {
  const [queue, setQueue] = useState<QueuedAction[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const { isOnline } = useOnlineStatus();

  const addToQueue = useCallback((action: () => Promise<void>, description: string) => {
    const queuedAction: QueuedAction = {
      id: Date.now().toString(),
      action,
      description,
      timestamp: Date.now()
    };
    
    setQueue(prev => [...prev, queuedAction]);
    return queuedAction.id;
  }, []);

  const removeFromQueue = useCallback((id: string) => {
    setQueue(prev => prev.filter(item => item.id !== id));
  }, []);

  const processQueue = useCallback(async () => {
    if (!isOnline || isProcessing || queue.length === 0) {
      return;
    }

    setIsProcessing(true);
    const currentQueue = [...queue];
    
    for (const item of currentQueue) {
      try {
        await item.action();
        removeFromQueue(item.id);
      } catch (error) {
        console.error(`Failed to process queued action: ${item.description}`, error);
        // Keep failed items in queue for retry
      }
    }
    
    setIsProcessing(false);
  }, [isOnline, isProcessing, queue, removeFromQueue]);

  const clearQueue = useCallback(() => {
    setQueue([]);
  }, []);

  // Auto-process queue when coming back online
  useEffect(() => {
    if (isOnline && queue.length > 0) {
      processQueue();
    }
  }, [isOnline, queue.length, processQueue]);

  return {
    queue,
    addToQueue,
    removeFromQueue,
    processQueue,
    clearQueue,
    isProcessing,
    queueSize: queue.length
  };
};

// Offline banner component
const OfflineBanner: React.FC<{
  isOnline: boolean;
  wasOffline: boolean;
  queueSize?: number;
  onRetry?: () => void;
}> = ({ isOnline, wasOffline, queueSize = 0, onRetry }) => {
  const [showReconnected, setShowReconnected] = useState(false);

  useEffect(() => {
    if (isOnline && wasOffline) {
      setShowReconnected(true);
      const timer = setTimeout(() => setShowReconnected(false), 3000);
      return () => clearTimeout(timer);
    }
  }, [isOnline, wasOffline]);

  if (isOnline && !showReconnected) {
    return null;
  }

  return (
    <div className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
      isOnline 
        ? 'bg-green-600 text-white' 
        : 'bg-red-600 text-white'
    }`}>
      <div className="max-w-7xl mx-auto px-4 py-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {isOnline ? (
              <Wifi className="h-4 w-4" />
            ) : (
              <WifiOff className="h-4 w-4" />
            )}
            <span className="text-sm font-medium">
              {isOnline 
                ? 'Connection restored!' 
                : 'You\'re currently offline'
              }
            </span>
            {!isOnline && queueSize > 0 && (
              <span className="text-xs bg-white bg-opacity-20 px-2 py-1 rounded">
                {queueSize} action{queueSize !== 1 ? 's' : ''} queued
              </span>
            )}
          </div>
          
          {!isOnline && onRetry && (
            <button
              onClick={onRetry}
              className="flex items-center space-x-1 text-sm hover:bg-white hover:bg-opacity-10 px-2 py-1 rounded transition-colors"
            >
              <RefreshCw className="h-3 w-3" />
              <span>Retry</span>
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

// Offline fallback component
const OfflineFallback: React.FC<{
  queueSize: number;
  onClearQueue: () => void;
  onRetry: () => void;
}> = ({ queueSize, onClearQueue, onRetry }) => (
  <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
    <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6 text-center">
      <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
        <WifiOff className="h-6 w-6 text-red-600" />
      </div>
      
      <h1 className="text-xl font-semibold text-gray-900 mb-2">
        No Internet Connection
      </h1>
      
      <p className="text-gray-600 mb-6">
        You're currently offline. Some features may not be available until your connection is restored.
      </p>

      {queueSize > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <div className="flex items-center space-x-2 mb-2">
            <AlertCircle className="h-4 w-4 text-blue-600" />
            <span className="text-sm font-medium text-blue-800">
              Queued Actions
            </span>
          </div>
          <p className="text-sm text-blue-700">
            {queueSize} action{queueSize !== 1 ? 's' : ''} will be processed when you're back online.
          </p>
          <button
            onClick={onClearQueue}
            className="text-xs text-blue-600 hover:text-blue-700 underline mt-2"
          >
            Clear queue
          </button>
        </div>
      )}

      <div className="space-y-3">
        <button
          onClick={onRetry}
          className="w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          Check Connection
        </button>
        
        <p className="text-xs text-gray-500">
          Make sure you're connected to the internet and try again.
        </p>
      </div>
    </div>
  </div>
);

// Main offline support component
export const OfflineSupport: React.FC<OfflineSupportProps> = ({
  children,
  fallback,
  showBanner = true,
  enableQueueing = false,
  onOnline,
  onOffline
}) => {
  const { isOnline, wasOffline } = useOnlineStatus();
  const { queue, clearQueue, queueSize } = useOfflineQueue();

  useEffect(() => {
    if (isOnline) {
      onOnline?.();
    } else {
      onOffline?.();
    }
  }, [isOnline, onOnline, onOffline]);

  const handleRetry = useCallback(() => {
    // Force a network check by trying to fetch a small resource
    fetch('/favicon.ico', { method: 'HEAD', cache: 'no-cache' })
      .then(() => {
        // Connection is working
        if (!navigator.onLine) {
          // Manually trigger online event if navigator.onLine is stale
          window.dispatchEvent(new Event('online'));
        }
      })
      .catch(() => {
        // Still offline
        console.log('Still offline');
      });
  }, []);

  // Show offline fallback if completely offline and no custom fallback provided
  if (!isOnline && !fallback) {
    return (
      <OfflineFallback 
        queueSize={queueSize}
        onClearQueue={clearQueue}
        onRetry={handleRetry}
      />
    );
  }

  // Show custom fallback if offline
  if (!isOnline && fallback) {
    return (
      <>
        {showBanner && (
          <OfflineBanner 
            isOnline={isOnline} 
            wasOffline={wasOffline}
            queueSize={queueSize}
            onRetry={handleRetry}
          />
        )}
        {fallback}
      </>
    );
  }

  // Normal online state
  return (
    <>
      {showBanner && (
        <OfflineBanner 
          isOnline={isOnline} 
          wasOffline={wasOffline}
          queueSize={queueSize}
          onRetry={handleRetry}
        />
      )}
      <div className={showBanner && !isOnline ? 'pt-12' : ''}>
        {children}
      </div>
    </>
  );
};

export default OfflineSupport;