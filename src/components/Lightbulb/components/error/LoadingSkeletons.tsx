import React from 'react';

interface SkeletonLoaderProps {
  width?: string | number;
  height?: string | number;
  className?: string;
  rounded?: boolean;
}

// Base skeleton loader component
export const SkeletonLoader: React.FC<SkeletonLoaderProps> = ({
  width = '100%',
  height = '1rem',
  className = '',
  rounded = false
}) => {
  return (
    <div
      className={`animate-pulse bg-gray-200 ${rounded ? 'rounded-full' : 'rounded'} ${className}`}
      style={{ width, height }}
    />
  );
};

// Content item skeleton
export const ContentItemSkeleton: React.FC = () => (
  <div className="p-4 border border-gray-200 rounded-lg space-y-3">
    <div className="flex items-center space-x-3">
      <SkeletonLoader width={40} height={40} rounded />
      <div className="flex-1 space-y-2">
        <SkeletonLoader width="60%" height={16} />
        <SkeletonLoader width="40%" height={14} />
      </div>
    </div>
    <SkeletonLoader width="100%" height={12} />
    <SkeletonLoader width="80%" height={12} />
    <div className="flex space-x-2">
      <SkeletonLoader width={60} height={24} rounded />
      <SkeletonLoader width={80} height={24} rounded />
    </div>
  </div>
);

// Chat message skeleton
export const ChatMessageSkeleton: React.FC<{ isUser?: boolean }> = ({ isUser = false }) => (
  <div className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-4`}>
    <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg space-y-2 ${
      isUser ? 'bg-blue-50' : 'bg-gray-50'
    }`}>
      <SkeletonLoader width="100%" height={14} />
      <SkeletonLoader width="80%" height={14} />
      <SkeletonLoader width="60%" height={14} />
    </div>
  </div>
);

// Search result skeleton
export const SearchResultSkeleton: React.FC = () => (
  <div className="p-4 border-b border-gray-200 space-y-3">
    <div className="flex items-start space-x-3">
      <SkeletonLoader width={24} height={24} />
      <div className="flex-1 space-y-2">
        <SkeletonLoader width="70%" height={18} />
        <SkeletonLoader width="100%" height={14} />
        <SkeletonLoader width="90%" height={14} />
        <div className="flex items-center space-x-4 mt-2">
          <SkeletonLoader width={80} height={12} />
          <SkeletonLoader width={60} height={12} />
          <SkeletonLoader width={100} height={12} />
        </div>
      </div>
    </div>
  </div>
);

// Settings panel skeleton
export const SettingsPanelSkeleton: React.FC = () => (
  <div className="space-y-6">
    {/* Header */}
    <div className="space-y-2">
      <SkeletonLoader width="30%" height={24} />
      <SkeletonLoader width="60%" height={16} />
    </div>
    
    {/* Tabs */}
    <div className="flex space-x-4 border-b border-gray-200">
      {[1, 2, 3].map(i => (
        <SkeletonLoader key={i} width={80} height={32} />
      ))}
    </div>
    
    {/* Settings sections */}
    {[1, 2, 3].map(section => (
      <div key={section} className="space-y-4">
        <SkeletonLoader width="25%" height={20} />
        {[1, 2, 3].map(item => (
          <div key={item} className="flex items-center justify-between py-3">
            <div className="space-y-1">
              <SkeletonLoader width={200} height={16} />
              <SkeletonLoader width={300} height={14} />
            </div>
            <SkeletonLoader width={60} height={32} rounded />
          </div>
        ))}
      </div>
    ))}
  </div>
);

// Table skeleton
export const TableSkeleton: React.FC<{ rows?: number; columns?: number }> = ({ 
  rows = 5, 
  columns = 4 
}) => (
  <div className="space-y-3">
    {/* Header */}
    <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
      {Array.from({ length: columns }).map((_, i) => (
        <SkeletonLoader key={i} width="80%" height={16} />
      ))}
    </div>
    
    {/* Rows */}
    {Array.from({ length: rows }).map((_, rowIndex) => (
      <div key={rowIndex} className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
        {Array.from({ length: columns }).map((_, colIndex) => (
          <SkeletonLoader key={colIndex} width="90%" height={14} />
        ))}
      </div>
    ))}
  </div>
);

// Card skeleton
export const CardSkeleton: React.FC = () => (
  <div className="bg-white rounded-lg shadow p-6 space-y-4">
    <div className="flex items-center space-x-3">
      <SkeletonLoader width={48} height={48} rounded />
      <div className="flex-1 space-y-2">
        <SkeletonLoader width="60%" height={18} />
        <SkeletonLoader width="40%" height={14} />
      </div>
    </div>
    <SkeletonLoader width="100%" height={12} />
    <SkeletonLoader width="80%" height={12} />
    <SkeletonLoader width="90%" height={12} />
    <div className="flex justify-between items-center pt-4">
      <SkeletonLoader width={80} height={32} rounded />
      <SkeletonLoader width={100} height={32} rounded />
    </div>
  </div>
);

// List skeleton
export const ListSkeleton: React.FC<{ items?: number }> = ({ items = 5 }) => (
  <div className="space-y-3">
    {Array.from({ length: items }).map((_, i) => (
      <div key={i} className="flex items-center space-x-3 p-3 border border-gray-200 rounded">
        <SkeletonLoader width={32} height={32} rounded />
        <div className="flex-1 space-y-2">
          <SkeletonLoader width="70%" height={16} />
          <SkeletonLoader width="50%" height={14} />
        </div>
        <SkeletonLoader width={24} height={24} />
      </div>
    ))}
  </div>
);

// Full page skeleton
export const FullPageSkeleton: React.FC = () => (
  <div className="min-h-screen bg-gray-50 p-4">
    {/* Header */}
    <div className="bg-white rounded-lg shadow p-6 mb-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <SkeletonLoader width={40} height={40} rounded />
          <div className="space-y-2">
            <SkeletonLoader width={200} height={20} />
            <SkeletonLoader width={150} height={16} />
          </div>
        </div>
        <div className="flex space-x-2">
          <SkeletonLoader width={80} height={36} rounded />
          <SkeletonLoader width={100} height={36} rounded />
        </div>
      </div>
    </div>
    
    {/* Main content */}
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Sidebar */}
      <div className="lg:col-span-1">
        <div className="bg-white rounded-lg shadow p-6 space-y-4">
          <SkeletonLoader width="60%" height={20} />
          <ListSkeleton items={6} />
        </div>
      </div>
      
      {/* Content area */}
      <div className="lg:col-span-2 space-y-6">
        {[1, 2, 3].map(i => (
          <CardSkeleton key={i} />
        ))}
      </div>
    </div>
  </div>
);

export type { SkeletonLoaderProps };