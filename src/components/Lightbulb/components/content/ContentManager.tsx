import React, { useState, useEffect, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { iconMapping } from '@/lib/icon-mapping';
import { Upload } from 'lucide-react';
import { invokeTauriCommand } from '../../utils/tauriCommands';
import { autoTaggingService } from '../../services/autoTaggingService';
import { 
  KnowledgeItem, 
  DocumentSource, 
  ContentManagerState,
  ContentManagerProps 
} from '../../types';

const ContentManager: React.FC<ContentManagerProps> = ({
  contentItems,
  tags,
  sources,
  categories,
  searchQuery,
  onContentUpdate,
  onTagsUpdate,
  onSourcesUpdate,
  onCategoriesUpdate,
  onSearchQueryChange
}) => {
  const [localState, setLocalState] = useState<ContentManagerState>({
    inputValue: '',
    selectedTags: [],
    selectedCategory: '',
    isProcessing: false,
    processingProgress: 0,
    lastProcessedContent: null
  });

  const [filteredContent, setFilteredContent] = useState<KnowledgeItem[]>(contentItems);

  // Filter content based on search query and selected filters
  useEffect(() => {
    let filtered = contentItems;

    if (searchQuery) {
      filtered = filtered.filter(item => 
        item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    if (localState.selectedTags.length > 0) {
      filtered = filtered.filter(item => 
        localState.selectedTags.some(tag => item.tags.includes(tag))
      );
    }

    if (localState.selectedCategory) {
      filtered = filtered.filter(item => item.category === localState.selectedCategory);
    }

    setFilteredContent(filtered);
  }, [contentItems, searchQuery, localState.selectedTags, localState.selectedCategory]);

  const handleSaveContent = useCallback(async () => {
    if (!localState.inputValue.trim()) return;

    setLocalState(prev => ({ ...prev, isProcessing: true, processingProgress: 0 }));

    try {
      // Auto-generate tags
      const tagSuggestions = await autoTaggingService.analyzeContent(localState.inputValue);
      const suggestedTags = tagSuggestions.suggestedTags.map((s: any) => s.tag);

      // Create new content item
      const newItem: KnowledgeItem = {
        id: Date.now().toString(),
        title: localState.inputValue.substring(0, 50) + (localState.inputValue.length > 50 ? '...' : ''),
        content: localState.inputValue,
        tags: [...localState.selectedTags, ...suggestedTags],
        category: localState.selectedCategory || (tagSuggestions.categories.length > 0 ? tagSuggestions.categories[0] : 'general'),
        timestamp: new Date().toISOString(),
        created_at: Date.now(),
        updated_at: Date.now(),
        metadata: {
          wordCount: localState.inputValue.split(' ').length,
          readingTime: Math.ceil(localState.inputValue.split(' ').length / 200),
          sentiment: tagSuggestions.sentiment
        }
      };

      setLocalState(prev => ({ ...prev, processingProgress: 50 }));

      // Save to backend
      await invokeTauriCommand('save_content', { content: newItem });
      
      setLocalState(prev => ({ ...prev, processingProgress: 100 }));

      // Update parent state
      onContentUpdate([...contentItems, newItem]);
      
      // Update tags if new ones were generated
      const newTags = suggestedTags.filter(tag => !tags.includes(tag));
      if (newTags.length > 0) {
        onTagsUpdate([...tags, ...newTags]);
      }

      // Update categories if new ones were detected
      const newCategories = tagSuggestions.categories.filter((cat: string) => !categories.includes(cat));
      if (newCategories.length > 0) {
        onCategoriesUpdate([...categories, ...newCategories]);
      }

      // Reset form
      setLocalState(prev => ({
        ...prev,
        inputValue: '',
        selectedTags: [],
        selectedCategory: '',
        isProcessing: false,
        processingProgress: 0,
        lastProcessedContent: newItem
      }));

    } catch (error) {
      console.error('Error saving content:', error);
      setLocalState(prev => ({ ...prev, isProcessing: false, processingProgress: 0 }));
    }
  }, [localState.inputValue, localState.selectedTags, localState.selectedCategory, contentItems, tags, categories, onContentUpdate, onTagsUpdate, onCategoriesUpdate]);

  const handleDeleteContent = useCallback(async (itemId: string) => {
    try {
      await invokeTauriCommand('delete_content', { id: itemId });
      const updatedContent = contentItems.filter(item => item.id !== itemId);
      onContentUpdate(updatedContent);
    } catch (error) {
      console.error('Error deleting content:', error);
    }
  }, [contentItems, onContentUpdate]);

  const handleTagToggle = useCallback((tag: string) => {
    setLocalState(prev => ({
      ...prev,
      selectedTags: prev.selectedTags.includes(tag)
        ? prev.selectedTags.filter(t => t !== tag)
        : [...prev.selectedTags, tag]
    }));
  }, []);

  const renderContentForm = () => (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <iconMapping.Add className="h-5 w-5" />
          Add New Content
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <Textarea
          placeholder="Enter your thoughts, ideas, or content here..."
          value={localState.inputValue}
          onChange={(e) => setLocalState(prev => ({ ...prev, inputValue: e.target.value }))}
          className="min-h-[120px] resize-none"
          disabled={localState.isProcessing}
        />
        
        {/* Tag Selection */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Tags</label>
          <div className="flex flex-wrap gap-2">
            {tags.map(tag => (
              <Badge
                key={tag}
                variant={localState.selectedTags.includes(tag) ? "default" : "outline"}
                className="cursor-pointer"
                onClick={() => handleTagToggle(tag)}
              >
                {tag}
              </Badge>
            ))}
          </div>
        </div>

        {/* Category Selection */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Category</label>
          <select
            value={localState.selectedCategory}
            onChange={(e) => setLocalState(prev => ({ ...prev, selectedCategory: e.target.value }))}
            className="w-full p-2 border border-border rounded-md bg-background"
            disabled={localState.isProcessing}
          >
            <option value="">Auto-detect category</option>
            {categories.map(category => (
              <option key={category} value={category}>{category}</option>
            ))}
          </select>
        </div>

        {/* Processing Progress */}
        {localState.isProcessing && (
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Upload className="h-4 w-4 animate-pulse" />
              Processing content...
            </div>
            <div className="w-full bg-muted rounded-full h-2">
              <div 
                className="bg-primary h-2 rounded-full transition-all duration-300"
                style={{ width: `${localState.processingProgress}%` }}
              />
            </div>
          </div>
        )}

        <div className="flex gap-2">
          <Button 
            onClick={handleSaveContent}
            disabled={!localState.inputValue.trim() || localState.isProcessing}
            className="flex-1"
          >
            {localState.isProcessing ? (
              <>
                <Upload className="h-4 w-4 mr-2 animate-spin" />
                Processing...
              </>
            ) : (
              <>
                <Upload className="h-4 w-4 mr-2" />
                Save Content
              </>
            )}
          </Button>
          <Button 
            variant="outline"
            onClick={() => setLocalState(prev => ({ 
              ...prev, 
              inputValue: '', 
              selectedTags: [], 
              selectedCategory: '' 
            }))}
            disabled={localState.isProcessing}
          >
            Clear
          </Button>
        </div>
      </CardContent>
    </Card>
  );

  const renderContentList = () => (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Your Content ({filteredContent.length})</h3>
        <div className="flex gap-2">
          <Input
            placeholder="Search content..."
            value={searchQuery}
            onChange={(e) => onSearchQueryChange(e.target.value)}
            className="w-64"
          />
          <Button variant="outline" size="sm">
            <iconMapping.FilterList className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {filteredContent.length === 0 ? (
        <Card>
          <CardContent className="text-center py-8">
            <iconMapping.Memory className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">
              {contentItems.length === 0 
                ? "No content yet. Start by adding your first thought above!"
                : "No content matches your search criteria."
              }
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4">
          {filteredContent.map(item => (
            <Card key={item.id} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-2">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-base">{item.title}</CardTitle>
                    <div className="flex items-center gap-2 text-xs text-muted-foreground mt-1">
                      <span>{new Date(item.timestamp).toLocaleDateString()}</span>
                      <span>•</span>
                      <span>{item.metadata?.wordCount || 0} words</span>
                      <span>•</span>
                      <span>{item.metadata?.readingTime || 1} min read</span>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDeleteContent(item.id)}
                    className="text-destructive hover:text-destructive"
                  >
                    <iconMapping.Delete className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-3 line-clamp-3">
                  {item.content}
                </p>
                <div className="flex flex-wrap gap-1">
                  {item.tags.map(tag => (
                    <Badge key={tag} variant="secondary" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                  {item.category && (
                    <Badge variant="outline" className="text-xs">
                      {item.category}
                    </Badge>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );

  return (
    <div className="space-y-6">
      {renderContentForm()}
      {renderContentList()}
    </div>
  );
};

export default ContentManager;