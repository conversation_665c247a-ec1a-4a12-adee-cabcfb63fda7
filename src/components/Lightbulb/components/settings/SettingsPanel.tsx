import React from 'react';
import { iconMapping } from '../../../../lib/icon-mapping';
import { Upload } from 'lucide-react';
import { SettingsPanelProps, SettingsPanelState } from '../../types';

const SettingsPanel: React.FC<SettingsPanelProps> = ({
  isOpen,
  onClose,
  settings,
  onSettingsChange,
  onExportData,
  onImportData,
  onClearData,
  onResetSettings
}) => {
  const [localState, setLocalState] = React.useState<SettingsPanelState>({
    activeTab: 'general',
    isExporting: false,
    isImporting: false,
    isClearing: false,
    showConfirmDialog: false,
    confirmAction: null
  });

  const handleSettingChange = (key: string, value: any) => {
    onSettingsChange({
      ...settings,
      [key]: value
    });
  };

  const handleConfirmAction = async () => {
    if (!localState.confirmAction) return;

    try {
      switch (localState.confirmAction) {
        case 'export':
          setLocalState((prev: SettingsPanelState) => ({ ...prev, isExporting: true }));
          await onExportData();
          break;
        case 'import':
          setLocalState((prev: SettingsPanelState) => ({ ...prev, isImporting: true }));
          await onImportData();
          break;
        case 'clear':
          setLocalState((prev: SettingsPanelState) => ({ ...prev, isClearing: true }));
          await onClearData();
          break;
        case 'reset':
          await onResetSettings();
          break;
      }
    } catch (error) {
    } finally {
      setLocalState((prev: SettingsPanelState) => ({
        ...prev,
        isExporting: false,
        isImporting: false,
        isClearing: false,
        showConfirmDialog: false,
        confirmAction: null
      }));
    }
  };

  const showConfirmDialog = (action: 'export' | 'import' | 'clear' | 'reset') => {
    setLocalState((prev: SettingsPanelState) => ({
      ...prev,
      showConfirmDialog: true,
      confirmAction: action
    }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-background border rounded-lg shadow-lg w-full max-w-2xl max-h-[80vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <h2 className="text-lg font-semibold">Settings</h2>
          <button
            onClick={onClose}
            className="p-1 hover:bg-muted rounded"
          >
            <iconMapping.Close className="h-5 w-5" />
          </button>
        </div>

        {/* Tabs */}
        <div className="flex border-b">
          {[
            { id: 'general', label: 'General', icon: iconMapping.Settings },
            { id: 'ai', label: 'AI Settings', icon: iconMapping.SmartToy },
            { id: 'data', label: 'Data Management', icon: iconMapping.Storage },
            { id: 'appearance', label: 'Appearance', icon: iconMapping.PaletteIcon }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setLocalState((prev: SettingsPanelState) => ({ ...prev, activeTab: tab.id as any }))}
              className={`flex items-center gap-2 px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                localState.activeTab === tab.id
                  ? 'border-primary text-primary'
                  : 'border-transparent text-muted-foreground hover:text-foreground'
              }`}
            >
              <tab.icon className="h-4 w-4" />
              {tab.label}
            </button>
          ))}
        </div>

        {/* Content */}
        <div className="p-4 overflow-y-auto max-h-[60vh]">
          {localState.activeTab === 'general' && (
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium mb-2">Auto-save interval (seconds)</label>
                <input
                  type="number"
                  value={settings.autoSaveInterval}
                  onChange={(e) => handleSettingChange('autoSaveInterval', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border rounded-md"
                  min="10"
                  max="300"
                />
              </div>

              <div>
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={settings.enableNotifications}
                    onChange={(e) => handleSettingChange('enableNotifications', e.target.checked)}
                    className="rounded"
                  />
                  <span className="text-sm font-medium">Enable notifications</span>
                </label>
              </div>

              <div>
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={settings.enableAutoTagging}
                    onChange={(e) => handleSettingChange('enableAutoTagging', e.target.checked)}
                    className="rounded"
                  />
                  <span className="text-sm font-medium">Enable auto-tagging</span>
                </label>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Default view mode</label>
                <select
                  value={settings.defaultViewMode}
                  onChange={(e) => handleSettingChange('defaultViewMode', e.target.value)}
                  className="w-full px-3 py-2 border rounded-md"
                >
                  <option value="grid">Grid</option>
                  <option value="list">List</option>
                  <option value="timeline">Timeline</option>
                </select>
              </div>
            </div>
          )}

          {localState.activeTab === 'ai' && (
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium mb-2">AI Model</label>
                <select
                  value={settings.aiModel}
                  onChange={(e) => handleSettingChange('aiModel', e.target.value)}
                  className="w-full px-3 py-2 border rounded-md"
                >
                  <option value="gpt-4">GPT-4</option>
                  <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                  <option value="claude-3">Claude 3</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Temperature ({settings.temperature})</label>
                <input
                  type="range"
                  min="0"
                  max="2"
                  step="0.1"
                  value={settings.temperature}
                  onChange={(e) => handleSettingChange('temperature', parseFloat(e.target.value))}
                  className="w-full"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Max tokens</label>
                <input
                  type="number"
                  value={settings.maxTokens}
                  onChange={(e) => handleSettingChange('maxTokens', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border rounded-md"
                  min="100"
                  max="4000"
                />
              </div>

              <div>
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={settings.enableContextMemory}
                    onChange={(e) => handleSettingChange('enableContextMemory', e.target.checked)}
                    className="rounded"
                  />
                  <span className="text-sm font-medium">Enable context memory</span>
                </label>
              </div>
            </div>
          )}

          {localState.activeTab === 'data' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <button
                  onClick={() => showConfirmDialog('export')}
                  disabled={localState.isExporting}
                  className="flex items-center gap-2 p-4 border rounded-lg hover:bg-muted transition-colors disabled:opacity-50"
                >
                  <iconMapping.Download className="h-5 w-5" />
                  <div className="text-left">
                    <div className="font-medium">Export Data</div>
                    <div className="text-sm text-muted-foreground">Download all your data</div>
                  </div>
                </button>

                <button
                  onClick={() => showConfirmDialog('import')}
                  disabled={localState.isImporting}
                  className="flex items-center gap-2 p-4 border rounded-lg hover:bg-muted transition-colors disabled:opacity-50"
                >
                  <Upload className="h-5 w-5" />
                  <div className="text-left">
                    <div className="font-medium">Import Data</div>
                    <div className="text-sm text-muted-foreground">Upload data from file</div>
                  </div>
                </button>

                <button
                  onClick={() => showConfirmDialog('clear')}
                  disabled={localState.isClearing}
                  className="flex items-center gap-2 p-4 border border-destructive/20 rounded-lg hover:bg-destructive/5 transition-colors disabled:opacity-50"
                >
                  <iconMapping.Delete className="h-5 w-5 text-destructive" />
                  <div className="text-left">
                    <div className="font-medium text-destructive">Clear All Data</div>
                    <div className="text-sm text-muted-foreground">Remove all stored data</div>
                  </div>
                </button>

                <button
                  onClick={() => showConfirmDialog('reset')}
                  className="flex items-center gap-2 p-4 border border-destructive/20 rounded-lg hover:bg-destructive/5 transition-colors"
                >
                  <iconMapping.Refresh className="h-5 w-5 text-destructive" />
                  <div className="text-left">
                    <div className="font-medium text-destructive">Reset Settings</div>
                    <div className="text-sm text-muted-foreground">Restore default settings</div>
                  </div>
                </button>
              </div>
            </div>
          )}

          {localState.activeTab === 'appearance' && (
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium mb-2">Theme</label>
                <select
                  value={settings.theme}
                  onChange={(e) => handleSettingChange('theme', e.target.value)}
                  className="w-full px-3 py-2 border rounded-md"
                >
                  <option value="light">Light</option>
                  <option value="dark">Dark</option>
                  <option value="system">System</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Font size</label>
                <select
                  value={settings.fontSize}
                  onChange={(e) => handleSettingChange('fontSize', e.target.value)}
                  className="w-full px-3 py-2 border rounded-md"
                >
                  <option value="small">Small</option>
                  <option value="medium">Medium</option>
                  <option value="large">Large</option>
                </select>
              </div>

              <div>
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={settings.compactMode}
                    onChange={(e) => handleSettingChange('compactMode', e.target.checked)}
                    className="rounded"
                  />
                  <span className="text-sm font-medium">Compact mode</span>
                </label>
              </div>

              <div>
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={settings.showAnimations}
                    onChange={(e) => handleSettingChange('showAnimations', e.target.checked)}
                    className="rounded"
                  />
                  <span className="text-sm font-medium">Show animations</span>
                </label>
              </div>
            </div>
          )}
        </div>

        {/* Confirmation Dialog */}
        {localState.showConfirmDialog && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-background border rounded-lg shadow-lg p-6 max-w-md">
              <h3 className="text-lg font-semibold mb-4">
                Confirm {localState.confirmAction}
              </h3>
              <p className="text-muted-foreground mb-6">
                Are you sure you want to {localState.confirmAction} your data? This action cannot be undone.
              </p>
              <div className="flex gap-3 justify-end">
                <button
                  onClick={() => setLocalState((prev: SettingsPanelState) => ({ ...prev, showConfirmDialog: false, confirmAction: null }))}
                  className="px-4 py-2 border rounded-md hover:bg-muted transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handleConfirmAction}
                  className="px-4 py-2 bg-destructive text-destructive-foreground rounded-md hover:bg-destructive/90 transition-colors"
                >
                  Confirm
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SettingsPanel;