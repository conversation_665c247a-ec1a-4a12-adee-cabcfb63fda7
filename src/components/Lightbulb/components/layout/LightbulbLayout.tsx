import React from 'react';
import { cn } from '@/lib/utils';
import { iconMapping } from '@/lib/icon-mapping';
import { Menu } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useIsMobile } from '@/hooks/useResponsiveDesign';
import { 
  ActiveSection, 
  SidebarItem, 
  HelpItem, 
  UIState 
} from '../../types';

interface LightbulbLayoutProps {
  children: React.ReactNode;
  uiState: UIState;
  onSectionChange: (section: ActiveSection) => void;
  onQuickCaptureOpen: () => void;
  onAdvancedSearchOpen: () => void;
  onMobileSidebarToggle: () => void;
  userCount: number;
  sidebarItems: SidebarItem[];
  helpItems: HelpItem[];
}

const LightbulbLayout: React.FC<LightbulbLayoutProps> = ({
  children,
  uiState,
  onSectionChange,
  onQuickCaptureOpen,
  onAdvancedSearchOpen,
  onMobileSidebarToggle,
  userCount,
  sidebarItems,
  helpItems
}) => {
  const isMobile = useIsMobile();
  const { activeSection, showMobileSidebar } = uiState;

  const renderDesktopSidebar = () => (
    <aside className="w-64 bg-card border-r border-border p-4 overflow-y-auto">
      <div className="space-y-6">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-2xl font-bold text-foreground mb-2">💡 Lightbulb</h1>
          <p className="text-sm text-muted-foreground">
            Your AI-powered second brain
          </p>
          <div className="mt-2 text-xs text-muted-foreground">
            {userCount} users connected
          </div>
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">Quick Actions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <Button 
              variant="outline" 
              size="sm" 
              className="w-full justify-start"
              onClick={onQuickCaptureOpen}
            >
              <iconMapping.Add className="h-4 w-4 mr-2" />
              Quick Capture
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              className="w-full justify-start"
              onClick={() => onSectionChange('pages')}
            >
              <iconMapping.FileCopy className="h-4 w-4 mr-2" />
              Create Page
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              className="w-full justify-start"
              onClick={() => onSectionChange('projects')}
            >
              <iconMapping.FolderIcon className="h-4 w-4 mr-2" />
              New Project
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              className="w-full justify-start"
              onClick={onAdvancedSearchOpen}
            >
              <iconMapping.Search className="h-4 w-4 mr-2" />
              Advanced Search
            </Button>
          </CardContent>
        </Card>

        {/* Navigation */}
        <nav className="space-y-1">
          {sidebarItems.map((item) => {
            const Icon = item.icon;
            return (
              <button
                key={item.id}
                onClick={() => onSectionChange(item.id as ActiveSection)}
                className={cn(
                  "w-full flex items-center px-3 py-2 text-sm rounded-md transition-colors",
                  "hover:bg-accent hover:text-accent-foreground",
                  activeSection === item.id
                    ? "bg-primary text-primary-foreground"
                    : "text-muted-foreground"
                )}
              >
                <Icon className="h-4 w-4 mr-3" />
                <span className="flex-1 text-left">{item.label}</span>
                {item.count && (
                  <span className="ml-2 px-2 py-0.5 text-xs bg-muted rounded-full">
                    {item.count}
                  </span>
                )}
              </button>
            );
          })}
        </nav>

        {/* Help Section */}
        <div className="border-t border-border pt-4">
          <div className="space-y-1">
            {helpItems.map((item) => {
              const Icon = item.icon;
              return (
                <button
                  key={item.id}
                  onClick={item.action}
                  className={cn(
                    "w-full flex items-center px-3 py-2 text-sm rounded-md transition-colors",
                    "hover:bg-accent hover:text-accent-foreground text-muted-foreground"
                  )}
                >
                  <Icon className="h-4 w-4 mr-3" />
                  <span className="flex-1 text-left">{item.label}</span>
                </button>
              );
            })}
          </div>
        </div>
      </div>
    </aside>
  );

  const renderMobileBottomNav = () => (
    <nav className="mobile-nav safe-area-bottom">
      <div className="flex">
        {[
          { id: 'thoughts', icon: iconMapping.Memory, label: 'Thoughts' },
          { id: 'chat', icon: iconMapping.Comment, label: 'Chat' },
          { id: 'visual', icon: iconMapping.Dashboard, label: 'Visual' },
          { id: 'search', icon: iconMapping.Search, label: 'Search' },
          { id: 'more', icon: iconMapping.MoreHoriz, label: 'More' }
        ].map((item) => (
          <button
            key={item.id}
            onClick={() => {
              if (item.id === 'more') {
                onMobileSidebarToggle();
              } else {
                onSectionChange(item.id as ActiveSection);
              }
            }}
            className={cn(
              "mobile-nav-item",
              activeSection === item.id && "active"
            )}
          >
            <item.icon className="h-5 w-5" />
            <span className="text-xs mt-1">{item.label}</span>
          </button>
        ))}
      </div>
    </nav>
  );

  const renderMobileSidebar = () => (
    <div className={cn(
      "fixed inset-0 z-50 bg-background/80 backdrop-blur-sm transition-opacity",
      showMobileSidebar ? "opacity-100" : "opacity-0 pointer-events-none"
    )}>
      <div className={cn(
        "fixed inset-y-0 left-0 w-64 bg-card border-r border-border transform transition-transform",
        showMobileSidebar ? "translate-x-0" : "-translate-x-full"
      )}>
        <div className="p-4">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-lg font-semibold">Menu</h2>
            <Button
              variant="ghost"
              size="sm"
              onClick={onMobileSidebarToggle}
            >
              <iconMapping.Close className="h-4 w-4" />
            </Button>
          </div>
          
          <nav className="space-y-1">
            {sidebarItems.map((item) => {
              const Icon = item.icon;
              return (
                <button
                  key={item.id}
                  onClick={() => {
                    onSectionChange(item.id as ActiveSection);
                    onMobileSidebarToggle();
                  }}
                  className={cn(
                    "w-full flex items-center px-3 py-2 text-sm rounded-md transition-colors",
                    "hover:bg-accent hover:text-accent-foreground",
                    activeSection === item.id
                      ? "bg-primary text-primary-foreground"
                      : "text-muted-foreground"
                  )}
                >
                  <Icon className="h-4 w-4 mr-3" />
                  <span className="flex-1 text-left">{item.label}</span>
                  {item.count && (
                    <span className="ml-2 px-2 py-0.5 text-xs bg-muted rounded-full">
                      {item.count}
                    </span>
                  )}
                </button>
              );
            })}
          </nav>
        </div>
      </div>
    </div>
  );

  return (
    <div className="flex h-screen bg-background">
      {/* Desktop Sidebar */}
      {!isMobile && renderDesktopSidebar()}
      
      {/* Mobile Sidebar Overlay */}
      {isMobile && renderMobileSidebar()}
      
      {/* Main Content */}
      <main className="flex-1 overflow-hidden">
        <div className="h-full flex flex-col">
          {/* Mobile Header */}
          {isMobile && (
            <header className="bg-card border-b border-border p-4 safe-area-top">
              <div className="flex items-center justify-between">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onMobileSidebarToggle}
                >
                  <Menu className="h-5 w-5" />
                </Button>
                <h1 className="text-lg font-semibold">💡 Lightbulb</h1>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onQuickCaptureOpen}
                >
                  <iconMapping.Add className="h-5 w-5" />
                </Button>
              </div>
            </header>
          )}
          
          {/* Content Area */}
          <div className="flex-1 overflow-auto p-4">
            {children}
          </div>
        </div>
      </main>
      
      {/* Mobile Bottom Navigation */}
      {isMobile && renderMobileBottomNav()}
    </div>
  );
};

export default LightbulbLayout;