import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { iconMapping } from '@/lib/icon-mapping';
import { Upload } from 'lucide-react';
import { invokeTauriCommand } from '../../utils/tauriCommands';
import { 
  ChatMessage, 
  DocumentSource, 
  ChatInterfaceProps,
  ChatInterfaceState 
} from '../../types';

const ChatInterface: React.FC<ChatInterfaceProps> = ({
  messages,
  sources,
  onMessagesUpdate,
  onSourcesUpdate,
  className
}) => {
  const [localState, setLocalState] = useState<ChatInterfaceState>({
    inputMessage: '',
    isLoading: false,
    selectedSources: [],
    chatMode: 'general',
    showSources: true,
    autoScroll: true
  });

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (localState.autoScroll && messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages, localState.autoScroll]);

  // Focus input on mount
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, []);

  const handleSendMessage = useCallback(async () => {
    if (!localState.inputMessage.trim() || localState.isLoading) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      content: localState.inputMessage,
      role: 'user',
      timestamp: Date.now(),
      sources: localState.selectedSources
    };

    // Add user message immediately
    const updatedMessages = [...messages, userMessage];
    onMessagesUpdate(updatedMessages);

    // Clear input and set loading
    setLocalState((prev: ChatInterfaceState) => ({
      ...prev,
      inputMessage: '',
      isLoading: true
    }));

    try {
      // Send to AI backend
      const response = await invokeTauriCommand('chat_with_ai', {
        message: localState.inputMessage,
        context: messages.slice(-10), // Last 10 messages for context
        sources: localState.selectedSources,
        mode: localState.chatMode
      });

      const aiMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        content: response.message || 'Sorry, I encountered an error processing your request.',
        role: 'assistant',
        timestamp: Date.now() + 1,
        sources: response.sources || [],
        metadata: {
          model: response.model,
          tokens: response.tokens,
          processingTime: response.processingTime
        }
      };

      // Add AI response
      onMessagesUpdate([...updatedMessages, aiMessage]);

      // Update sources if new ones were referenced
      if (response.sources && response.sources.length > 0) {
        const newSources = response.sources.filter((source: DocumentSource) => 
          !sources.some((existing: DocumentSource) => existing.id === source.id)
        );
        if (newSources.length > 0) {
          onSourcesUpdate([...sources, ...newSources]);
        }
      }

    } catch (error) {
      
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        content: 'Sorry, I encountered an error. Please try again.',
        role: 'assistant',
        timestamp: Date.now() + 1,
        isError: true
      };

      onMessagesUpdate([...updatedMessages, errorMessage]);
    } finally {
      setLocalState((prev: ChatInterfaceState) => ({ ...prev, isLoading: false }));
    }
  }, [localState.inputMessage, localState.selectedSources, localState.chatMode, messages, sources, onMessagesUpdate, onSourcesUpdate, localState.isLoading]);

  const handleKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  }, [handleSendMessage]);

  const handleSourceToggle = useCallback((sourceId: string) => {
    setLocalState((prev: ChatInterfaceState) => ({
      ...prev,
      selectedSources: prev.selectedSources.includes(sourceId)
        ? prev.selectedSources.filter((id: string) => id !== sourceId)
        : [...prev.selectedSources, sourceId]
    }));
  }, []);

  const handleClearChat = useCallback(() => {
    onMessagesUpdate([]);
    setLocalState((prev: ChatInterfaceState) => ({ ...prev, selectedSources: [] }));
  }, [onMessagesUpdate]);

  const renderMessage = (message: ChatMessage) => {
    const isUser = message.role === 'user';
    const isError = message.isError;

    return (
      <div
        key={message.id}
        className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-4`}
      >
        <div className={`max-w-[80%] ${isUser ? 'order-2' : 'order-1'}`}>
          <div
            className={`rounded-lg px-4 py-2 ${
              isUser
                ? 'bg-primary text-primary-foreground'
                : isError
                ? 'bg-destructive text-destructive-foreground'
                : 'bg-muted text-muted-foreground'
            }`}
          >
            <p className="whitespace-pre-wrap">{message.content}</p>
            
            {/* Message metadata */}
            <div className="flex items-center justify-between mt-2 text-xs opacity-70">
              <span>{new Date(message.timestamp).toLocaleTimeString()}</span>
              {message.metadata && (
                <div className="flex items-center gap-2">
                  {message.metadata.model && (
                    <span>{message.metadata.model}</span>
                  )}
                  {(message.metadata.tokens || message.metadata.tokens_used) && (
                    <span>{message.metadata.tokens || message.metadata.tokens_used} tokens</span>
                  )}
                </div>
              )}
            </div>
          </div>
          
          {/* Sources */}
          {message.sources && message.sources.length > 0 && (
            <div className="mt-2 flex flex-wrap gap-1">
              {message.sources.map((sourceId: string) => {
                const source = sources.find((s: DocumentSource) => s.id === sourceId);
                return source ? (
                  <Badge key={sourceId} variant="outline" className="text-xs">
                    <iconMapping.FileCopy className="h-3 w-3 mr-1" />
                    {source.title}
                  </Badge>
                ) : null;
              })}
            </div>
          )}
        </div>
        
        {/* Avatar */}
        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
          isUser ? 'order-1 mr-2 bg-primary' : 'order-2 ml-2 bg-muted'
        }`}>
          {isUser ? (
            <iconMapping.Person className="h-4 w-4 text-primary-foreground" />
          ) : (
            <iconMapping.SmartToy className="h-4 w-4 text-muted-foreground" />
          )}
        </div>
      </div>
    );
  };

  const renderSourcesPanel = () => {
    if (!localState.showSources || sources.length === 0) return null;

    return (
      <Card className="mb-4">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm flex items-center justify-between">
            <span>Available Sources ({sources.length})</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setLocalState((prev: ChatInterfaceState) => ({ ...prev, showSources: false }))}
            >
              <iconMapping.Close className="h-4 w-4" />
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
            {sources.map(source => (
              <div
                key={source.id}
                className={`p-2 border rounded cursor-pointer transition-colors ${
                  localState.selectedSources.includes(source.id)
                    ? 'border-primary bg-primary/10'
                    : 'border-border hover:bg-muted'
                }`}
                onClick={() => handleSourceToggle(source.id)}
              >
                <div className="flex items-center gap-2">
                  <iconMapping.FileCopy className="h-4 w-4 text-muted-foreground" />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate">{source.title}</p>
                    <p className="text-xs text-muted-foreground truncate">
                      {source.type} • {source.size ? `${Math.round(source.size / 1024)}KB` : 'Unknown size'}
                    </p>
                  </div>
                  {localState.selectedSources.includes(source.id) && (
                    <iconMapping.CheckCircle className="h-4 w-4 text-primary" />
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className={`flex flex-col h-full ${className || ''}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        <div className="flex items-center gap-2">
          <iconMapping.Comment className="h-5 w-5 text-primary" />
          <h2 className="text-lg font-semibold">AI Chat</h2>
          <Badge variant="outline">{messages.length} messages</Badge>
        </div>
        
        <div className="flex items-center gap-2">
          {/* Chat Mode Selector */}
          <select
            value={localState.chatMode}
            onChange={(e) => setLocalState((prev: ChatInterfaceState) => ({ ...prev, chatMode: e.target.value as ChatInterfaceState['chatMode'] }))}
            className="text-sm border border-border rounded px-2 py-1 bg-background"
          >
            <option value="general">General</option>
            <option value="research">Research</option>
            <option value="creative">Creative</option>
            <option value="technical">Technical</option>
          </select>
          
          {/* Sources Toggle */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => setLocalState((prev: ChatInterfaceState) => ({ ...prev, showSources: !prev.showSources }))}
          >
            <iconMapping.FileCopy className="h-4 w-4 mr-1" />
            Sources
          </Button>
          
          {/* Clear Chat */}
          <Button
            variant="outline"
            size="sm"
            onClick={handleClearChat}
            disabled={messages.length === 0}
          >
            <iconMapping.Clear className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Sources Panel */}
      {renderSourcesPanel()}

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.length === 0 ? (
          <div className="text-center py-12">
            <iconMapping.Comment className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">Start a conversation</h3>
            <p className="text-muted-foreground mb-4">
              Ask questions about your content, get insights, or brainstorm ideas.
            </p>
            <div className="flex flex-wrap justify-center gap-2">
              {[
                "What are the main themes in my content?",
                "Summarize my recent notes",
                "Help me brainstorm ideas",
                "Find connections between my thoughts"
              ].map((suggestion, index) => (
                <Button
                  key={index}
                  variant="outline"
                  size="sm"
                  onClick={() => setLocalState((prev: ChatInterfaceState) => ({ ...prev, inputMessage: suggestion }))}
                >
                  {suggestion}
                </Button>
              ))}
            </div>
          </div>
        ) : (
          <>
            {messages.map(renderMessage)}
            <div ref={messagesEndRef} />
          </>
        )}
      </div>

      {/* Input */}
      <div className="p-4 border-t border-border">
        <div className="flex gap-2">
          <Input
            ref={inputRef}
            placeholder="Type your message..."
            value={localState.inputMessage}
            onChange={(e) => setLocalState((prev: ChatInterfaceState) => ({ ...prev, inputMessage: e.target.value }))}
            onKeyPress={handleKeyPress}
            disabled={localState.isLoading}
            className="flex-1"
          />
          <Button
            onClick={handleSendMessage}
            disabled={!localState.inputMessage.trim() || localState.isLoading}
          >
            {localState.isLoading ? (
              <Upload className="h-4 w-4 animate-spin" />
            ) : (
              <iconMapping.Send className="h-4 w-4" />
            )}
          </Button>
        </div>
        
        {/* Selected Sources Indicator */}
        {localState.selectedSources.length > 0 && (
          <div className="mt-2 flex items-center gap-2 text-sm text-muted-foreground">
            <iconMapping.FileCopy className="h-4 w-4" />
            <span>{localState.selectedSources.length} source(s) selected</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setLocalState((prev: ChatInterfaceState) => ({ ...prev, selectedSources: [] }))}
            >
              Clear
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default ChatInterface;