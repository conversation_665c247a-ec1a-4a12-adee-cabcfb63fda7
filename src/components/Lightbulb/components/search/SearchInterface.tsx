import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { iconMapping } from '@/lib/icon-mapping';
import { Upload } from 'lucide-react';
import { invokeTauriCommand } from '../../utils/tauriCommands';
import { 
  KnowledgeItem, 
  DocumentSource, 
  SearchInterfaceProps,
  SearchInterfaceState,
  SearchResult 
} from '../../types';

const SearchInterface: React.FC<SearchInterfaceProps> = ({
  knowledgeItems,
  sources,
  onItemSelect,
  onSourceSelect,
  className
}) => {
  const [localState, setLocalState] = useState<SearchInterfaceState>({
    query: '',
    results: [],
    isLoading: false,
    searchType: 'all',
    filters: {
      tags: [],
      dateRange: 'all',
      contentType: 'all',
      minRelevance: 0.5
    },
    sortBy: 'relevance',
    sortOrder: 'desc',
    showFilters: false,
    selectedResults: [],
    searchHistory: []
  });

  // Debounced search effect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (localState.query.trim()) {
        handleSearch();
      } else {
        setLocalState((prev: SearchInterfaceState) => ({ ...prev, results: [] }));
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [localState.query, localState.filters, localState.searchType]);

  // Available tags from knowledge items
  const availableTags = useMemo(() => {
    const tagSet = new Set<string>();
    knowledgeItems.forEach((item: KnowledgeItem) => {
      item.tags?.forEach((tag: string) => tagSet.add(tag));
    });
    return Array.from(tagSet).sort();
  }, [knowledgeItems]);

  const handleSearch = useCallback(async () => {
    if (!localState.query.trim()) return;

    setLocalState((prev: SearchInterfaceState) => ({ ...prev, isLoading: true }));

    try {
      // Perform semantic search via Tauri
      const searchResponse = await invokeTauriCommand('semantic_search', {
        query: localState.query,
        search_type: localState.searchType,
        filters: {
          tags: localState.filters.tags,
          date_range: localState.filters.dateRange,
          content_type: localState.filters.contentType,
          min_relevance: localState.filters.minRelevance
        },
        limit: 50
      });

      const results: SearchResult[] = searchResponse.results || [];

      // Sort results
      const sortedResults = [...results].sort((a, b) => {
        const multiplier = localState.sortOrder === 'desc' ? -1 : 1;
        
        switch (localState.sortBy) {
          case 'relevance':
            return (a.relevance - b.relevance) * multiplier;
          case 'date':
            return (new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()) * multiplier;
          case 'title':
            return a.title.localeCompare(b.title) * multiplier;
          default:
            return 0;
        }
      });

      setLocalState((prev: SearchInterfaceState) => ({
        ...prev,
        results: sortedResults,
        isLoading: false,
        searchHistory: prev.searchHistory.includes(localState.query)
          ? prev.searchHistory
          : [localState.query, ...prev.searchHistory.slice(0, 9)] // Keep last 10 searches
      }));

    } catch (error) {
      console.error('Search error:', error);
      setLocalState((prev: SearchInterfaceState) => ({
        ...prev,
        results: [],
        isLoading: false
      }));
    }
  }, [localState.query, localState.searchType, localState.filters, localState.sortBy, localState.sortOrder]);

  const handleResultSelect = useCallback((result: SearchResult) => {
    if (result.type === 'knowledge_item') {
      const item = knowledgeItems.find((item: KnowledgeItem) => item.id === result.id);
      if (item && onItemSelect) {
        onItemSelect(item);
      }
    } else if (result.type === 'source') {
      const source = sources.find((source: DocumentSource) => source.id === result.id);
      if (source && onSourceSelect) {
        onSourceSelect(source);
      }
    }
  }, [knowledgeItems, sources, onItemSelect, onSourceSelect]);

  const handleTagFilter = useCallback((tag: string) => {
    setLocalState((prev: SearchInterfaceState) => ({
      ...prev,
      filters: {
        ...prev.filters,
        tags: prev.filters.tags.includes(tag)
          ? prev.filters.tags.filter((t: string) => t !== tag)
          : [...prev.filters.tags, tag]
      }
    }));
  }, []);

  const handleClearFilters = useCallback(() => {
    setLocalState((prev: SearchInterfaceState) => ({
      ...prev,
      filters: {
        tags: [],
        dateRange: 'all',
        contentType: 'all',
        minRelevance: 0.5
      }
    }));
  }, []);

  const handleHistorySelect = useCallback((query: string) => {
    setLocalState((prev: SearchInterfaceState) => ({ ...prev, query }));
  }, []);

  const renderSearchResult = (result: SearchResult) => {
    const isSelected = localState.selectedResults.includes(result.id);
    
    return (
      <Card 
        key={result.id} 
        className={`cursor-pointer transition-all hover:shadow-md ${
          isSelected ? 'ring-2 ring-primary' : ''
        }`}
        onClick={() => handleResultSelect(result)}
      >
        <CardContent className="p-4">
          <div className="flex items-start justify-between mb-2">
            <div className="flex items-center gap-2">
              {result.type === 'knowledge_item' ? (
                <iconMapping.FileCopy className="h-4 w-4 text-primary" />
              ) : (
                <iconMapping.FileCopy className="h-4 w-4 text-muted-foreground" />
              )}
              <h3 className="font-medium text-sm">{result.title}</h3>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="text-xs">
                {Math.round(result.relevance * 100)}%
              </Badge>
              <Badge variant="secondary" className="text-xs">
                {result.type}
              </Badge>
            </div>
          </div>
          
          {result.snippet && (
            <p className="text-sm text-muted-foreground mb-2 line-clamp-2">
              {result.snippet}
            </p>
          )}
          
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <span>{new Date(result.timestamp).toLocaleDateString()}</span>
            
            {result.tags && result.tags.length > 0 && (
              <div className="flex gap-1">
                {result.tags.slice(0, 3).map((tag: string) => (
                  <Badge key={tag} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ))}
                {result.tags.length > 3 && (
                  <span className="text-xs">+{result.tags.length - 3}</span>
                )}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    );
  };

  const renderFilters = () => {
    if (!localState.showFilters) return null;

    return (
      <Card className="mb-4">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm flex items-center justify-between">
            <span>Search Filters</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClearFilters}
            >
              Clear All
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Tags Filter */}
          <div>
            <label className="text-sm font-medium mb-2 block">Tags</label>
            <div className="flex flex-wrap gap-2">
              {availableTags.map(tag => (
                <Badge
                  key={tag}
                  variant={localState.filters.tags.includes(tag) ? 'default' : 'outline'}
                  className="cursor-pointer text-xs"
                  onClick={() => handleTagFilter(tag)}
                >
                  {tag}
                </Badge>
              ))}
            </div>
          </div>

          {/* Date Range Filter */}
          <div>
            <label className="text-sm font-medium mb-2 block">Date Range</label>
            <select
              value={localState.filters.dateRange}
              onChange={(e) => setLocalState((prev: SearchInterfaceState) => ({
                ...prev,
                filters: { ...prev.filters, dateRange: e.target.value as any }
              }))}
              className="w-full text-sm border border-border rounded px-2 py-1 bg-background"
            >
              <option value="all">All Time</option>
              <option value="today">Today</option>
              <option value="week">This Week</option>
              <option value="month">This Month</option>
              <option value="year">This Year</option>
            </select>
          </div>

          {/* Content Type Filter */}
          <div>
            <label className="text-sm font-medium mb-2 block">Content Type</label>
            <select
              value={localState.filters.contentType}
              onChange={(e) => setLocalState((prev: SearchInterfaceState) => ({
                ...prev,
                filters: { ...prev.filters, contentType: e.target.value as any }
              }))}
              className="w-full text-sm border border-border rounded px-2 py-1 bg-background"
            >
              <option value="all">All Types</option>
              <option value="document">Documents</option>
              <option value="note">Notes</option>
              <option value="link">Links</option>
              <option value="image">Images</option>
            </select>
          </div>

          {/* Relevance Threshold */}
          <div>
            <label className="text-sm font-medium mb-2 block">
              Min Relevance: {Math.round(localState.filters.minRelevance * 100)}%
            </label>
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              value={localState.filters.minRelevance}
              onChange={(e) => setLocalState((prev: SearchInterfaceState) => ({
                ...prev,
                filters: { ...prev.filters, minRelevance: parseFloat(e.target.value) }
              }))}
              className="w-full"
            />
          </div>
        </CardContent>
      </Card>
    );
  };

  const renderSearchHistory = () => {
    if (localState.searchHistory.length === 0) return null;

    return (
      <div className="mb-4">
        <h3 className="text-sm font-medium mb-2">Recent Searches</h3>
        <div className="flex flex-wrap gap-2">
          {localState.searchHistory.slice(0, 5).map((query: string, index: number) => (
            <Button
              key={index}
              variant="outline"
              size="sm"
              onClick={() => handleHistorySelect(query)}
              className="text-xs"
            >
              {query}
            </Button>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className={`flex flex-col h-full ${className || ''}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        <div className="flex items-center gap-2">
          <iconMapping.Search className="h-5 w-5 text-primary" />
          <h2 className="text-lg font-semibold">Search</h2>
          {localState.results.length > 0 && (
            <Badge variant="outline">{localState.results.length} results</Badge>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          {/* Search Type Selector */}
          <select
            value={localState.searchType}
            onChange={(e) => setLocalState((prev: SearchInterfaceState) => ({ 
              ...prev, 
              searchType: e.target.value as SearchInterfaceState['searchType'] 
            }))}
            className="text-sm border border-border rounded px-2 py-1 bg-background"
          >
            <option value="all">All</option>
            <option value="semantic">Semantic</option>
            <option value="keyword">Keyword</option>
            <option value="fuzzy">Fuzzy</option>
          </select>
          
          {/* Sort Options */}
          <select
            value={`${localState.sortBy}-${localState.sortOrder}`}
            onChange={(e) => {
              const [sortBy, sortOrder] = e.target.value.split('-');
              setLocalState((prev: SearchInterfaceState) => ({ 
                ...prev, 
                sortBy: sortBy as SearchInterfaceState['sortBy'],
                sortOrder: sortOrder as SearchInterfaceState['sortOrder']
              }));
            }}
            className="text-sm border border-border rounded px-2 py-1 bg-background"
          >
            <option value="relevance-desc">Relevance ↓</option>
            <option value="relevance-asc">Relevance ↑</option>
            <option value="date-desc">Newest First</option>
            <option value="date-asc">Oldest First</option>
            <option value="title-asc">Title A-Z</option>
            <option value="title-desc">Title Z-A</option>
          </select>
          
          {/* Filters Toggle */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => setLocalState((prev: SearchInterfaceState) => ({ 
              ...prev, 
              showFilters: !prev.showFilters 
            }))}
          >
            <iconMapping.FilterList className="h-4 w-4 mr-1" />
            Filters
          </Button>
        </div>
      </div>

      {/* Search Input */}
      <div className="p-4 border-b border-border">
        <div className="relative">
          <iconMapping.Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search your knowledge base..."
            value={localState.query}
            onChange={(e) => setLocalState((prev: SearchInterfaceState) => ({ 
              ...prev, 
              query: e.target.value 
            }))}
            className="pl-10 pr-4"
          />
          {localState.isLoading && (
            <Upload className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 animate-spin text-muted-foreground" />
          )}
        </div>
        
        {renderSearchHistory()}
      </div>

      {/* Filters */}
      {renderFilters()}

      {/* Results */}
      <div className="flex-1 overflow-y-auto p-4">
        {localState.query && localState.results.length === 0 && !localState.isLoading && (
          <div className="text-center py-12">
            <iconMapping.Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">No results found</h3>
            <p className="text-muted-foreground mb-4">
              Try adjusting your search terms or filters.
            </p>
            <Button
              variant="outline"
              onClick={handleClearFilters}
            >
              Clear Filters
            </Button>
          </div>
        )}
        
        {!localState.query && (
          <div className="text-center py-12">
            <iconMapping.Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">Search your knowledge</h3>
            <p className="text-muted-foreground mb-4">
              Find documents, notes, and insights across your entire knowledge base.
            </p>
            <div className="flex flex-wrap justify-center gap-2">
              {[
                "recent notes",
                "important documents",
                "project ideas",
                "meeting notes"
              ].map((suggestion, index) => (
                <Button
                  key={index}
                  variant="outline"
                  size="sm"
                  onClick={() => setLocalState((prev: SearchInterfaceState) => ({ 
                    ...prev, 
                    query: suggestion 
                  }))}
                >
                  {suggestion}
                </Button>
              ))}
            </div>
          </div>
        )}
        
        <div className="space-y-3">
          {localState.results.map(renderSearchResult)}
        </div>
      </div>
    </div>
  );
};

export default SearchInterface;