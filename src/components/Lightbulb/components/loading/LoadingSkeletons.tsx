import React from 'react';

// Base skeleton component
const Skeleton: React.FC<{
  className?: string;
  animate?: boolean;
}> = ({ className = '', animate = true }) => (
  <div 
    className={`bg-gray-200 rounded ${animate ? 'animate-pulse' : ''} ${className}`}
  />
);

// Content item skeleton
export const ContentItemSkeleton: React.FC = () => (
  <div className="p-4 border border-gray-200 rounded-lg space-y-3">
    <div className="flex items-start space-x-3">
      <Skeleton className="h-10 w-10 rounded-full" />
      <div className="flex-1 space-y-2">
        <Skeleton className="h-4 w-3/4" />
        <Skeleton className="h-3 w-1/2" />
      </div>
    </div>
    <Skeleton className="h-3 w-full" />
    <Skeleton className="h-3 w-5/6" />
    <div className="flex space-x-2">
      <Skeleton className="h-6 w-16 rounded-full" />
      <Skeleton className="h-6 w-20 rounded-full" />
      <Skeleton className="h-6 w-14 rounded-full" />
    </div>
  </div>
);

// Chat message skeleton
export const ChatMessageSkeleton: React.FC<{ isUser?: boolean }> = ({ isUser = false }) => (
  <div className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-4`}>
    <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg space-y-2 ${
      isUser ? 'bg-blue-50' : 'bg-gray-50'
    }`}>
      <Skeleton className="h-3 w-full" />
      <Skeleton className="h-3 w-4/5" />
      <Skeleton className="h-3 w-3/5" />
    </div>
  </div>
);

// Search result skeleton
export const SearchResultSkeleton: React.FC = () => (
  <div className="p-4 border-b border-gray-100 space-y-3">
    <div className="flex items-start space-x-3">
      <Skeleton className="h-8 w-8 rounded" />
      <div className="flex-1 space-y-2">
        <Skeleton className="h-5 w-3/4" />
        <Skeleton className="h-3 w-full" />
        <Skeleton className="h-3 w-2/3" />
      </div>
      <Skeleton className="h-4 w-16" />
    </div>
    <div className="flex items-center space-x-4 text-xs">
      <Skeleton className="h-3 w-20" />
      <Skeleton className="h-3 w-16" />
      <Skeleton className="h-3 w-12" />
    </div>
  </div>
);

// Settings panel skeleton
export const SettingsPanelSkeleton: React.FC = () => (
  <div className="space-y-6">
    <div className="space-y-4">
      <Skeleton className="h-6 w-48" />
      {[...Array(4)].map((_, i) => (
        <div key={i} className="flex items-center justify-between py-3">
          <div className="space-y-1">
            <Skeleton className="h-4 w-32" />
            <Skeleton className="h-3 w-48" />
          </div>
          <Skeleton className="h-6 w-12 rounded-full" />
        </div>
      ))}
    </div>
  </div>
);

// Table skeleton
export const TableSkeleton: React.FC<{
  rows?: number;
  columns?: number;
}> = ({ rows = 5, columns = 4 }) => (
  <div className="space-y-3">
    {/* Header */}
    <div className="flex space-x-4 pb-2 border-b border-gray-200">
      {[...Array(columns)].map((_, i) => (
        <Skeleton key={i} className="h-4 flex-1" />
      ))}
    </div>
    {/* Rows */}
    {[...Array(rows)].map((_, rowIndex) => (
      <div key={rowIndex} className="flex space-x-4 py-2">
        {[...Array(columns)].map((_, colIndex) => (
          <Skeleton key={colIndex} className="h-4 flex-1" />
        ))}
      </div>
    ))}
  </div>
);

// Card skeleton
export const CardSkeleton: React.FC<{
  hasImage?: boolean;
  hasActions?: boolean;
}> = ({ hasImage = false, hasActions = false }) => (
  <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
    {hasImage && <Skeleton className="h-48 w-full" />}
    <div className="p-4 space-y-3">
      <Skeleton className="h-6 w-3/4" />
      <Skeleton className="h-4 w-full" />
      <Skeleton className="h-4 w-5/6" />
      {hasActions && (
        <div className="flex space-x-2 pt-2">
          <Skeleton className="h-8 w-20" />
          <Skeleton className="h-8 w-16" />
        </div>
      )}
    </div>
  </div>
);

// List skeleton
export const ListSkeleton: React.FC<{
  items?: number;
  showAvatar?: boolean;
}> = ({ items = 5, showAvatar = true }) => (
  <div className="space-y-3">
    {[...Array(items)].map((_, i) => (
      <div key={i} className="flex items-center space-x-3 p-3">
        {showAvatar && <Skeleton className="h-10 w-10 rounded-full" />}
        <div className="flex-1 space-y-2">
          <Skeleton className="h-4 w-3/4" />
          <Skeleton className="h-3 w-1/2" />
        </div>
        <Skeleton className="h-4 w-16" />
      </div>
    ))}
  </div>
);

// Page skeleton (full page loading)
export const PageSkeleton: React.FC = () => (
  <div className="min-h-screen bg-gray-50 p-4">
    <div className="max-w-7xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <Skeleton className="h-8 w-64 mb-4" />
        <Skeleton className="h-4 w-96" />
      </div>
      
      {/* Navigation */}
      <div className="flex space-x-6 mb-8">
        {[...Array(4)].map((_, i) => (
          <Skeleton key={i} className="h-10 w-24" />
        ))}
      </div>
      
      {/* Content grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(6)].map((_, i) => (
          <CardSkeleton key={i} hasImage hasActions />
        ))}
      </div>
    </div>
  </div>
);

// Composite loading states for specific components
export const ContentManagerSkeleton: React.FC = () => (
  <div className="space-y-6">
    <div className="flex items-center justify-between">
      <Skeleton className="h-8 w-48" />
      <div className="flex space-x-2">
        <Skeleton className="h-10 w-24" />
        <Skeleton className="h-10 w-20" />
      </div>
    </div>
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {[...Array(6)].map((_, i) => (
        <ContentItemSkeleton key={i} />
      ))}
    </div>
  </div>
);

export const ChatInterfaceSkeleton: React.FC = () => (
  <div className="flex flex-col h-full">
    <div className="flex-1 p-4 space-y-4 overflow-y-auto">
      <ChatMessageSkeleton />
      <ChatMessageSkeleton isUser />
      <ChatMessageSkeleton />
      <ChatMessageSkeleton isUser />
      <ChatMessageSkeleton />
    </div>
    <div className="p-4 border-t border-gray-200">
      <div className="flex space-x-2">
        <Skeleton className="flex-1 h-10" />
        <Skeleton className="h-10 w-20" />
      </div>
    </div>
  </div>
);

export const SearchInterfaceSkeleton: React.FC = () => (
  <div className="space-y-4">
    <div className="flex space-x-4">
      <Skeleton className="flex-1 h-10" />
      <Skeleton className="h-10 w-24" />
    </div>
    <div className="space-y-2">
      {[...Array(5)].map((_, i) => (
        <SearchResultSkeleton key={i} />
      ))}
    </div>
  </div>
);

export default Skeleton;