// Optimized components with performance enhancements
// These components use React.memo(), useMemo(), useCallback, and virtualization
// to prevent unnecessary re-renders and improve performance

export { default as OptimizedContentManager } from './OptimizedContentManager';
export { default as OptimizedChatInterface } from './OptimizedChatInterface';
export { default as OptimizedSearchInterface } from './OptimizedSearchInterface';
export { default as OptimizedSettingsPanel } from './OptimizedSettingsPanel';

// Re-export types for convenience
export type {
  ContentManagerProps,
  ChatInterfaceProps,
  SearchInterfaceProps,
  SettingsPanelProps
} from '../../types';