import React, { memo, useMemo, useCallback, useState, useRef, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  Search, 
  Filter, 
  Calendar, 
  Tag, 
  Clock, 
  FileText, 
  File,
  Image,
  Video,
  Music,
  Archive,
  X,
  ChevronDown,
  ChevronUp,
  Loader2,
  Star,
  Eye
} from 'lucide-react';
import { SearchResult, SearchInterfaceProps, SearchInterfaceState } from '../../types';
import { useLightbulbActions } from '../../hooks/useLightbulbActions';

// Virtual list configuration
const RESULT_HEIGHT = 120;
const VISIBLE_RESULTS = 8;

// Debounced search hook
const useDebounce = <T,>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

// Memoized search result item
const SearchResultItem = memo<{
  result: SearchResult;
  onSelect?: (result: SearchResult) => void;
  onToggleFavorite?: (id: string) => void;
  searchQuery?: string;
}>(({ result, onSelect, onToggleFavorite, searchQuery }) => {
  const handleSelect = useCallback(() => {
    onSelect?.(result);
  }, [result, onSelect]);

  const handleToggleFavorite = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    onToggleFavorite?.(result.id);
  }, [result.id, onToggleFavorite]);

  const highlightedTitle = useMemo(() => {
    if (!searchQuery) return result.title;
    
    const regex = new RegExp(`(${searchQuery})`, 'gi');
    return result.title.replace(regex, '<mark>$1</mark>');
  }, [result.title, searchQuery]);

  const highlightedContent = useMemo(() => {
    if (!searchQuery) return result.content;
    
    const regex = new RegExp(`(${searchQuery})`, 'gi');
    return result.content.replace(regex, '<mark>$1</mark>');
  }, [result.content, searchQuery]);

  const getTypeIcon = useCallback((type: string) => {
    switch (type) {
      case 'document':
        return <FileText className="h-4 w-4" />;
      case 'knowledge_item':
        return <File className="h-4 w-4" />;
      case 'image':
        return <Image className="h-4 w-4" />;
      case 'video':
        return <Video className="h-4 w-4" />;
      case 'audio':
        return <Music className="h-4 w-4" />;
      case 'archive':
        return <Archive className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  }, []);

  const formattedDate = useMemo(() => {
    return new Date(result.lastModified).toLocaleDateString([], {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }, [result.lastModified]);

  return (
    <div 
      className="p-4 border-b hover:bg-accent/50 cursor-pointer transition-colors"
      onClick={handleSelect}
    >
      <div className="flex items-start justify-between gap-3">
        <div className="flex items-start gap-3 flex-1 min-w-0">
          <div className="flex-shrink-0 mt-1">
            {getTypeIcon(result.type)}
          </div>
          
          <div className="flex-1 min-w-0 space-y-2">
            <div className="flex items-start justify-between gap-2">
              <h3 
                className="font-medium text-sm line-clamp-1"
                dangerouslySetInnerHTML={{ __html: highlightedTitle }}
              />
              <div className="flex items-center gap-1 flex-shrink-0">
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0"
                  onClick={handleToggleFavorite}
                >
                  <Star 
                    className={`h-3 w-3 ${
                      result.isFavorite ? 'fill-yellow-400 text-yellow-400' : 'text-muted-foreground'
                    }`} 
                  />
                </Button>
                <Badge variant="outline" className="text-xs">
                  {result.relevanceScore}%
                </Badge>
              </div>
            </div>
            
            <p 
              className="text-sm text-muted-foreground line-clamp-2"
              dangerouslySetInnerHTML={{ __html: highlightedContent }}
            />
            
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                <Calendar className="h-3 w-3" />
                <span>{formattedDate}</span>
                {result.viewCount && (
                  <>
                    <Separator orientation="vertical" className="h-3" />
                    <Eye className="h-3 w-3" />
                    <span>{result.viewCount} views</span>
                  </>
                )}
              </div>
              
              {result.tags && result.tags.length > 0 && (
                <div className="flex items-center gap-1">
                  {result.tags.slice(0, 2).map((tag, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                  {result.tags.length > 2 && (
                    <Badge variant="secondary" className="text-xs">
                      +{result.tags.length - 2}
                    </Badge>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
});

SearchResultItem.displayName = 'SearchResultItem';

// Virtualized results list
const VirtualizedResultsList = memo<{
  results: SearchResult[];
  onSelect?: (result: SearchResult) => void;
  onToggleFavorite?: (id: string) => void;
  searchQuery?: string;
}>(({ results, onSelect, onToggleFavorite, searchQuery }) => {
  const [scrollTop, setScrollTop] = useState(0);
  const [containerHeight, setContainerHeight] = useState(600);
  const containerRef = useRef<HTMLDivElement>(null);

  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop);
  }, []);

  useEffect(() => {
    const updateHeight = () => {
      if (containerRef.current) {
        setContainerHeight(containerRef.current.clientHeight);
      }
    };
    
    updateHeight();
    window.addEventListener('resize', updateHeight);
    return () => window.removeEventListener('resize', updateHeight);
  }, []);

  const { visibleResults, totalHeight, offsetY } = useMemo(() => {
    const startIndex = Math.floor(scrollTop / RESULT_HEIGHT);
    const endIndex = Math.min(
      startIndex + Math.ceil(containerHeight / RESULT_HEIGHT) + 1,
      results.length
    );
    
    return {
      visibleResults: results.slice(startIndex, endIndex),
      totalHeight: results.length * RESULT_HEIGHT,
      offsetY: startIndex * RESULT_HEIGHT
    };
  }, [results, scrollTop, containerHeight]);

  if (results.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 text-muted-foreground">
        <div className="text-center space-y-2">
          <Search className="h-12 w-12 mx-auto opacity-50" />
          <p>No results found</p>
          <p className="text-sm">Try adjusting your search terms or filters</p>
        </div>
      </div>
    );
  }

  return (
    <div 
      ref={containerRef}
      className="h-full overflow-auto"
      onScroll={handleScroll}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        <div style={{ transform: `translateY(${offsetY}px)` }}>
          {visibleResults.map((result) => (
            <div key={result.id} style={{ height: RESULT_HEIGHT }}>
              <SearchResultItem
                result={result}
                onSelect={onSelect}
                onToggleFavorite={onToggleFavorite}
                searchQuery={searchQuery}
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
});

VirtualizedResultsList.displayName = 'VirtualizedResultsList';

// Search filters component
const SearchFilters = memo<{
  availableTags: string[];
  selectedTags: string[];
  onTagsChange: (tags: string[]) => void;
  contentTypes: string[];
  selectedContentTypes: string[];
  onContentTypesChange: (types: string[]) => void;
  dateRange: { start?: string; end?: string };
  onDateRangeChange: (range: { start?: string; end?: string }) => void;
  sortBy: 'relevance' | 'date' | 'title' | 'views';
  onSortChange: (sort: 'relevance' | 'date' | 'title' | 'views') => void;
  isExpanded: boolean;
  onToggleExpanded: () => void;
}>(({ 
  availableTags, 
  selectedTags, 
  onTagsChange, 
  contentTypes, 
  selectedContentTypes, 
  onContentTypesChange,
  dateRange,
  onDateRangeChange,
  sortBy,
  onSortChange,
  isExpanded,
  onToggleExpanded
}) => {
  const handleTagToggle = useCallback((tag: string) => {
    const newTags = selectedTags.includes(tag)
      ? selectedTags.filter(t => t !== tag)
      : [...selectedTags, tag];
    onTagsChange(newTags);
  }, [selectedTags, onTagsChange]);

  const handleContentTypeToggle = useCallback((type: string) => {
    const newTypes = selectedContentTypes.includes(type)
      ? selectedContentTypes.filter(t => t !== type)
      : [...selectedContentTypes, type];
    onContentTypesChange(newTypes);
  }, [selectedContentTypes, onContentTypesChange]);

  const handleDateStartChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    onDateRangeChange({ ...dateRange, start: e.target.value });
  }, [dateRange, onDateRangeChange]);

  const handleDateEndChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    onDateRangeChange({ ...dateRange, end: e.target.value });
  }, [dateRange, onDateRangeChange]);

  const handleSortChange = useCallback((value: string) => {
    onSortChange(value as 'relevance' | 'date' | 'title' | 'views');
  }, [onSortChange]);

  const clearAllFilters = useCallback(() => {
    onTagsChange([]);
    onContentTypesChange([]);
    onDateRangeChange({});
    onSortChange('relevance');
  }, [onTagsChange, onContentTypesChange, onDateRangeChange, onSortChange]);

  const activeFiltersCount = useMemo(() => {
    return selectedTags.length + selectedContentTypes.length + 
           (dateRange.start ? 1 : 0) + (dateRange.end ? 1 : 0) +
           (sortBy !== 'relevance' ? 1 : 0);
  }, [selectedTags.length, selectedContentTypes.length, dateRange, sortBy]);

  return (
    <div className="border-b">
      <div className="p-4">
        <div className="flex items-center justify-between">
          <Button
            variant="ghost"
            onClick={onToggleExpanded}
            className="flex items-center gap-2 p-0 h-auto"
          >
            <Filter className="h-4 w-4" />
            <span>Filters</span>
            {activeFiltersCount > 0 && (
              <Badge variant="secondary" className="text-xs">
                {activeFiltersCount}
              </Badge>
            )}
            {isExpanded ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
          
          <div className="flex items-center gap-2">
            <Select value={sortBy} onValueChange={handleSortChange}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="relevance">Relevance</SelectItem>
                <SelectItem value="date">Date</SelectItem>
                <SelectItem value="title">Title</SelectItem>
                <SelectItem value="views">Views</SelectItem>
              </SelectContent>
            </Select>
            
            {activeFiltersCount > 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={clearAllFilters}
                className="text-xs"
              >
                Clear All
              </Button>
            )}
          </div>
        </div>
        
        {isExpanded && (
          <div className="mt-4 space-y-4">
            {/* Content Types */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Content Types</label>
              <div className="flex flex-wrap gap-2">
                {contentTypes.map((type) => (
                  <div key={type} className="flex items-center space-x-2">
                    <Checkbox
                      id={`type-${type}`}
                      checked={selectedContentTypes.includes(type)}
                      onCheckedChange={() => handleContentTypeToggle(type)}
                    />
                    <label htmlFor={`type-${type}`} className="text-sm capitalize cursor-pointer">
                      {type}
                    </label>
                  </div>
                ))}
              </div>
            </div>
            
            {/* Tags */}
            {availableTags.length > 0 && (
              <div className="space-y-2">
                <label className="text-sm font-medium">Tags</label>
                <div className="max-h-32 overflow-y-auto">
                  <div className="flex flex-wrap gap-1">
                    {availableTags.map((tag) => (
                      <Badge
                        key={tag}
                        variant={selectedTags.includes(tag) ? "default" : "outline"}
                        className="cursor-pointer text-xs"
                        onClick={() => handleTagToggle(tag)}
                      >
                        <Tag className="h-3 w-3 mr-1" />
                        {tag}
                        {selectedTags.includes(tag) && (
                          <X className="h-3 w-3 ml-1" />
                        )}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            )}
            
            {/* Date Range */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Date Range</label>
              <div className="flex items-center gap-2">
                <Input
                  type="date"
                  value={dateRange.start || ''}
                  onChange={handleDateStartChange}
                  className="w-auto"
                  placeholder="Start date"
                />
                <span className="text-sm text-muted-foreground">to</span>
                <Input
                  type="date"
                  value={dateRange.end || ''}
                  onChange={handleDateEndChange}
                  className="w-auto"
                  placeholder="End date"
                />
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
});

SearchFilters.displayName = 'SearchFilters';

// Main optimized search interface
const OptimizedSearchInterface = memo<SearchInterfaceProps>(({ 
  onResultSelect, 
  className 
}) => {
  const { loading } = useLightbulbActions();
  
  const [localState, setLocalState] = useState<SearchInterfaceState>({
    query: '',
    results: [],
    isLoading: false,
    searchType: 'all',
    selectedTags: [],
    selectedContentTypes: [],
    dateRange: {},
    filters: {
      tags: [],
      dateRange: 'all',
      contentType: 'all',
      minRelevance: 0
    },
    sortBy: 'relevance',
    sortOrder: 'desc',
    showFilters: false,
    selectedResults: [],
    searchHistory: []
  });

  // Debounced search query
  const debouncedQuery = useDebounce(localState.query, 300);

  // Mock data for demonstration
  const mockResults: SearchResult[] = useMemo(() => [
    {
      id: '1',
      title: 'React Performance Optimization Guide',
      content: 'Learn how to optimize React applications using memo, useMemo, and useCallback hooks for better performance.',
      type: 'document',
      relevance: 95,
      relevanceScore: 95,
      timestamp: new Date('2024-01-15').getTime(),
      lastModified: '2024-01-15',
      tags: ['react', 'performance', 'optimization'],
      viewCount: 142,
      isFavorite: true
    },
    {
      id: '2',
      title: 'TypeScript Best Practices',
      content: 'A comprehensive guide to writing better TypeScript code with proper typing and error handling.',
      type: 'knowledge_item',
      relevance: 88,
      relevanceScore: 88,
      timestamp: new Date('2024-01-10').getTime(),
      lastModified: '2024-01-10',
      tags: ['typescript', 'best-practices', 'coding'],
      viewCount: 89,
      isFavorite: false
    }
  ], []);

  const availableTags = useMemo(() => {
    const tags = new Set<string>();
    mockResults.forEach(result => {
      result.tags?.forEach(tag => tags.add(tag));
    });
    return Array.from(tags);
  }, [mockResults]);

  const contentTypes = useMemo(() => {
    const types = new Set<string>();
    mockResults.forEach(result => types.add(result.type));
    return Array.from(types);
  }, [mockResults]);

  // Filtered and sorted results
  const filteredResults = useMemo(() => {
    let filtered = mockResults;

    // Filter by query
    if (debouncedQuery) {
      filtered = filtered.filter(result => 
        result.title.toLowerCase().includes(debouncedQuery.toLowerCase()) ||
        result.content.toLowerCase().includes(debouncedQuery.toLowerCase()) ||
        result.tags?.some(tag => tag.toLowerCase().includes(debouncedQuery.toLowerCase()))
      );
    }

    // Filter by content types
    if (localState.selectedContentTypes.length > 0) {
      filtered = filtered.filter(result => 
        localState.selectedContentTypes.includes(result.type)
      );
    }

    // Filter by tags
    if (localState.selectedTags.length > 0) {
      filtered = filtered.filter(result => 
        result.tags?.some(tag => localState.selectedTags.includes(tag))
      );
    }

    // Filter by date range
    if (localState.dateRange.start || localState.dateRange.end) {
      filtered = filtered.filter(result => {
        const resultDate = new Date(result.lastModified);
        const startDate = localState.dateRange.start ? new Date(localState.dateRange.start) : null;
        const endDate = localState.dateRange.end ? new Date(localState.dateRange.end) : null;
        
        if (startDate && resultDate < startDate) return false;
        if (endDate && resultDate > endDate) return false;
        return true;
      });
    }

    // Sort results
    switch (localState.sortBy) {
      case 'date':
        return filtered.sort((a, b) => new Date(b.lastModified).getTime() - new Date(a.lastModified).getTime());
      case 'title':
        return filtered.sort((a, b) => a.title.localeCompare(b.title));
      case 'views':
        return filtered.sort((a, b) => (b.viewCount || 0) - (a.viewCount || 0));
      case 'relevance':
      default:
        return filtered.sort((a, b) => b.relevanceScore - a.relevanceScore);
    }
  }, [mockResults, debouncedQuery, localState.selectedContentTypes, localState.selectedTags, localState.dateRange, localState.sortBy]);

  // Callbacks
  const handleQueryChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setLocalState(prev => ({ ...prev, query: e.target.value }));
  }, []);

  const handleSearch = useCallback(async () => {
    if (!debouncedQuery.trim()) return;

    setLocalState(prev => ({ 
      ...prev, 
      isLoading: true,
      searchHistory: [debouncedQuery, ...prev.searchHistory.filter(h => h !== debouncedQuery)].slice(0, 10)
    }));

    try {
      // Simulate search API call
      await new Promise(resolve => setTimeout(resolve, 500));
      setLocalState(prev => ({ ...prev, results: filteredResults, isLoading: false }));
    } catch (error) {
      setLocalState(prev => ({ ...prev, isLoading: false }));
    }
  }, [debouncedQuery, filteredResults]);

  const handleResultSelect = useCallback((result: SearchResult) => {
    onResultSelect?.(result);
    // Update view count
  }, [onResultSelect]);

  const handleToggleFavorite = useCallback((id: string) => {
    // Implement favorite toggle logic
  }, []);

  const handleTagsChange = useCallback((tags: string[]) => {
    setLocalState(prev => ({ ...prev, selectedTags: tags }));
  }, []);

  const handleContentTypesChange = useCallback((types: string[]) => {
    setLocalState(prev => ({ ...prev, selectedContentTypes: types }));
  }, []);

  const handleDateRangeChange = useCallback((range: { start?: string; end?: string }) => {
    setLocalState(prev => ({ ...prev, dateRange: range }));
  }, []);

  const handleSortChange = useCallback((sort: 'relevance' | 'date' | 'title' | 'views') => {
    setLocalState(prev => ({ ...prev, sortBy: sort }));
  }, []);

  const handleToggleFilters = useCallback(() => {
    setLocalState(prev => ({ ...prev, showFilters: !prev.showFilters }));
  }, []);

  // Auto-search when debounced query changes
  useEffect(() => {
    if (debouncedQuery) {
      handleSearch();
    } else {
      setLocalState(prev => ({ ...prev, results: [] }));
    }
  }, [debouncedQuery, handleSearch]);

  return (
    <Card className={className}>
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center gap-2">
          <Search className="h-5 w-5" />
          Search ({filteredResults.length} results)
        </CardTitle>
      </CardHeader>
      
      <CardContent className="p-0">
        <div className="p-4 border-b">
          <div className="flex gap-2">
            <Input
              placeholder="Search content, tags, or keywords..."
              value={localState.query}
              onChange={handleQueryChange}
              className="flex-1"
            />
            <Button
              onClick={handleSearch}
              disabled={localState.isLoading}
              size="icon"
            >
              {localState.isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Search className="h-4 w-4" />
              )}
            </Button>
          </div>
          
          {localState.searchHistory.length > 0 && (
            <div className="mt-2">
              <div className="flex items-center gap-2 text-xs text-muted-foreground mb-1">
                <Clock className="h-3 w-3" />
                <span>Recent searches:</span>
              </div>
              <div className="flex flex-wrap gap-1">
                {localState.searchHistory.slice(0, 5).map((query, index) => (
                  <Badge
                    key={index}
                    variant="outline"
                    className="cursor-pointer text-xs"
                    onClick={() => setLocalState(prev => ({ ...prev, query }))}
                  >
                    {query}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </div>
        
        <SearchFilters
          availableTags={availableTags}
          selectedTags={localState.selectedTags}
          onTagsChange={handleTagsChange}
          contentTypes={contentTypes}
          selectedContentTypes={localState.selectedContentTypes}
          onContentTypesChange={handleContentTypesChange}
          dateRange={localState.dateRange}
          onDateRangeChange={handleDateRangeChange}
          sortBy={localState.sortBy}
          onSortChange={handleSortChange}
          isExpanded={localState.showFilters}
          onToggleExpanded={handleToggleFilters}
        />
        
        <div className="h-[600px]">
          <VirtualizedResultsList
            results={filteredResults}
            onSelect={handleResultSelect}
            onToggleFavorite={handleToggleFavorite}
            searchQuery={debouncedQuery}
          />
        </div>
      </CardContent>
    </Card>
  );
});

OptimizedSearchInterface.displayName = 'OptimizedSearchInterface';

export default OptimizedSearchInterface;