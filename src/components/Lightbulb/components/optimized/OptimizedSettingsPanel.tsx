import React, { useState, useCallback, useMemo } from 'react';
import { 
  Setting<PERSON>, 
  User, 
  Brain, 
  Database, 
  Palette, 
  Download, 
  Upload, 
  Trash2, 
  RotateCcw,
  Save,
  X,
  Check,
  AlertTriangle
} from 'lucide-react';
import { SettingsPanelProps, SettingsPanelState } from '../../types';
import { useLightbulbActions } from '../../hooks/useLightbulbActions';

// Memoized tab component
const SettingsTab = React.memo<{
  id: string;
  label: string;
  icon: React.ReactNode;
  isActive: boolean;
  onClick: (id: string) => void;
}>(({ id, label, icon, isActive, onClick }) => {
  const handleClick = useCallback(() => {
    onClick(id);
  }, [id, onClick]);

  return (
    <button
      onClick={handleClick}
      className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
        isActive 
          ? 'bg-blue-100 text-blue-700 border border-blue-200' 
          : 'text-gray-600 hover:bg-gray-100'
      }`}
    >
      {icon}
      <span className="font-medium">{label}</span>
    </button>
  );
});

// Memoized setting item component
const SettingItem = React.memo<{
  label: string;
  description?: string;
  children: React.ReactNode;
}>(({ label, description, children }) => (
  <div className="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0">
    <div className="flex-1">
      <label className="text-sm font-medium text-gray-700">{label}</label>
      {description && (
        <p className="text-xs text-gray-500 mt-1">{description}</p>
      )}
    </div>
    <div className="ml-4">
      {children}
    </div>
  </div>
));

// Memoized general settings section
const GeneralSettings = React.memo<{
  settings: any;
  onSettingChange: (key: string, value: any) => void;
}>(({ settings, onSettingChange }) => {
  const handleToggle = useCallback((key: string) => {
    onSettingChange(key, !settings[key]);
  }, [settings, onSettingChange]);

  const handleSelectChange = useCallback((key: string, value: string) => {
    onSettingChange(key, value);
  }, [onSettingChange]);

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-800 mb-4">General Settings</h3>
      
      <SettingItem 
        label="Dark Mode" 
        description="Switch between light and dark themes"
      >
        <button
          onClick={() => handleToggle('darkMode')}
          className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
            settings.darkMode ? 'bg-blue-600' : 'bg-gray-200'
          }`}
        >
          <span
            className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
              settings.darkMode ? 'translate-x-6' : 'translate-x-1'
            }`}
          />
        </button>
      </SettingItem>

      <SettingItem 
        label="Language" 
        description="Choose your preferred language"
      >
        <select
          value={settings.language || 'en'}
          onChange={(e) => handleSelectChange('language', e.target.value)}
          className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="en">English</option>
          <option value="es">Español</option>
          <option value="fr">Français</option>
          <option value="de">Deutsch</option>
        </select>
      </SettingItem>

      <SettingItem 
        label="Auto-save" 
        description="Automatically save changes"
      >
        <button
          onClick={() => handleToggle('autoSave')}
          className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
            settings.autoSave ? 'bg-blue-600' : 'bg-gray-200'
          }`}
        >
          <span
            className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
              settings.autoSave ? 'translate-x-6' : 'translate-x-1'
            }`}
          />
        </button>
      </SettingItem>
    </div>
  );
});

// Memoized AI settings section
const AISettings = React.memo<{
  settings: any;
  onSettingChange: (key: string, value: any) => void;
}>(({ settings, onSettingChange }) => {
  const handleSliderChange = useCallback((key: string, value: number) => {
    onSettingChange(key, value);
  }, [onSettingChange]);

  const handleSelectChange = useCallback((key: string, value: string) => {
    onSettingChange(key, value);
  }, [onSettingChange]);

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-800 mb-4">AI Settings</h3>
      
      <SettingItem 
        label="AI Model" 
        description="Choose the AI model for processing"
      >
        <select
          value={settings.aiModel || 'gpt-4'}
          onChange={(e) => handleSelectChange('aiModel', e.target.value)}
          className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="gpt-4">GPT-4</option>
          <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
          <option value="claude-3">Claude 3</option>
        </select>
      </SettingItem>

      <SettingItem 
        label="Response Length" 
        description="Control the length of AI responses"
      >
        <div className="flex items-center space-x-2">
          <input
            type="range"
            min="1"
            max="10"
            value={settings.responseLength || 5}
            onChange={(e) => handleSliderChange('responseLength', parseInt(e.target.value))}
            className="w-20"
          />
          <span className="text-sm text-gray-600">{settings.responseLength || 5}</span>
        </div>
      </SettingItem>

      <SettingItem 
        label="Auto-tagging" 
        description="Automatically tag content using AI"
      >
        <button
          onClick={() => onSettingChange('autoTagging', !settings.autoTagging)}
          className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
            settings.autoTagging ? 'bg-blue-600' : 'bg-gray-200'
          }`}
        >
          <span
            className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
              settings.autoTagging ? 'translate-x-6' : 'translate-x-1'
            }`}
          />
        </button>
      </SettingItem>
    </div>
  );
});

// Memoized data management section
const DataManagement = React.memo<{
  onExport: () => void;
  onImport: () => void;
  onClear: () => void;
  onReset: () => void;
}>(({ onExport, onImport, onClear, onReset }) => (
  <div className="space-y-4">
    <h3 className="text-lg font-semibold text-gray-800 mb-4">Data Management</h3>
    
    <div className="grid grid-cols-2 gap-4">
      <button
        onClick={onExport}
        className="flex items-center justify-center space-x-2 px-4 py-3 bg-green-50 text-green-700 border border-green-200 rounded-lg hover:bg-green-100 transition-colors"
      >
        <Download className="h-4 w-4" />
        <span>Export Data</span>
      </button>
      
      <button
        onClick={onImport}
        className="flex items-center justify-center space-x-2 px-4 py-3 bg-blue-50 text-blue-700 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors"
      >
        <Upload className="h-4 w-4" />
        <span>Import Data</span>
      </button>
      
      <button
        onClick={onClear}
        className="flex items-center justify-center space-x-2 px-4 py-3 bg-orange-50 text-orange-700 border border-orange-200 rounded-lg hover:bg-orange-100 transition-colors"
      >
        <Trash2 className="h-4 w-4" />
        <span>Clear Data</span>
      </button>
      
      <button
        onClick={onReset}
        className="flex items-center justify-center space-x-2 px-4 py-3 bg-red-50 text-red-700 border border-red-200 rounded-lg hover:bg-red-100 transition-colors"
      >
        <RotateCcw className="h-4 w-4" />
        <span>Reset Settings</span>
      </button>
    </div>
    
    <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
      <div className="flex items-start space-x-2">
        <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
        <div>
          <h4 className="text-sm font-medium text-yellow-800">Data Management Warning</h4>
          <p className="text-sm text-yellow-700 mt-1">
            Clearing data or resetting settings cannot be undone. Please export your data before performing these actions.
          </p>
        </div>
      </div>
    </div>
  </div>
));

// Main optimized settings panel component
const OptimizedSettingsPanel: React.FC<SettingsPanelProps> = ({
  isOpen,
  onClose,
  settings,
  onSettingsChange,
  onExportData,
  onImportData,
  onClearData,
  onResetSettings
}) => {
  const [activeTab, setActiveTab] = useState('general');
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const { loading } = useLightbulbActions();

  // Memoized tabs configuration
  const tabs = useMemo(() => [
    { id: 'general', label: 'General', icon: <User className="h-4 w-4" /> },
    { id: 'ai', label: 'AI', icon: <Brain className="h-4 w-4" /> },
    { id: 'data', label: 'Data', icon: <Database className="h-4 w-4" /> },
    { id: 'appearance', label: 'Appearance', icon: <Palette className="h-4 w-4" /> }
  ], []);

  // Memoized setting change handler
  const handleSettingChange = useCallback((key: string, value: any) => {
    onSettingsChange?.({ [key]: value });
    setHasUnsavedChanges(true);
  }, [onSettingsChange]);

  // Memoized save handler
  const handleSave = useCallback(() => {
    setHasUnsavedChanges(false);
    // Additional save logic here
  }, []);

  // Memoized data management handlers
  const handleExport = useCallback(() => {
    onExportData?.();
  }, [onExportData]);

  const handleImport = useCallback(() => {
    onImportData?.();
  }, [onImportData]);

  const handleClear = useCallback(() => {
    if (window.confirm('Are you sure you want to clear all data? This action cannot be undone.')) {
      onClearData?.();
    }
  }, [onClearData]);

  const handleReset = useCallback(() => {
    if (window.confirm('Are you sure you want to reset all settings? This action cannot be undone.')) {
      onResetSettings?.();
      setHasUnsavedChanges(false);
    }
  }, [onResetSettings]);

  // Memoized tab change handler
  const handleTabChange = useCallback((tabId: string) => {
    setActiveTab(tabId);
  }, []);

  // Memoized close handler
  const handleClose = useCallback(() => {
    if (hasUnsavedChanges) {
      if (window.confirm('You have unsaved changes. Are you sure you want to close?')) {
        onClose();
        setHasUnsavedChanges(false);
      }
    } else {
      onClose();
    }
  }, [hasUnsavedChanges, onClose]);

  // Memoized content renderer
  const renderContent = useMemo(() => {
    switch (activeTab) {
      case 'general':
        return (
          <GeneralSettings 
            settings={settings} 
            onSettingChange={handleSettingChange} 
          />
        );
      case 'ai':
        return (
          <AISettings 
            settings={settings} 
            onSettingChange={handleSettingChange} 
          />
        );
      case 'data':
        return (
          <DataManagement 
            onExport={handleExport}
            onImport={handleImport}
            onClear={handleClear}
            onReset={handleReset}
          />
        );
      case 'appearance':
        return (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">Appearance Settings</h3>
            <p className="text-gray-600">Appearance settings coming soon...</p>
          </div>
        );
      default:
        return null;
    }
  }, [activeTab, settings, handleSettingChange, handleExport, handleImport, handleClear, handleReset]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <Settings className="h-5 w-5 text-gray-600" />
            <h2 className="text-xl font-semibold text-gray-800">Settings</h2>
            {hasUnsavedChanges && (
              <span className="px-2 py-1 bg-orange-100 text-orange-700 text-xs rounded-full">
                Unsaved changes
              </span>
            )}
          </div>
          <div className="flex items-center space-x-2">
            {hasUnsavedChanges && (
              <button
                onClick={handleSave}
                disabled={loading}
                className="flex items-center space-x-1 px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 text-sm"
              >
                <Save className="h-3 w-3" />
                <span>Save</span>
              </button>
            )}
            <button
              onClick={handleClose}
              className="p-2 text-gray-400 hover:text-gray-600 rounded-md hover:bg-gray-100"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
        </div>

        <div className="flex h-[calc(90vh-80px)]">
          {/* Sidebar */}
          <div className="w-64 bg-gray-50 border-r border-gray-200 p-4">
            <nav className="space-y-2">
              {tabs.map((tab) => (
                <SettingsTab
                  key={tab.id}
                  id={tab.id}
                  label={tab.label}
                  icon={tab.icon}
                  isActive={activeTab === tab.id}
                  onClick={handleTabChange}
                />
              ))}
            </nav>
          </div>

          {/* Content */}
          <div className="flex-1 p-6 overflow-y-auto">
            {loading ? (
              <div className="flex items-center justify-center h-64">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                  <p className="text-gray-600">Loading settings...</p>
                </div>
              </div>
            ) : (
              renderContent
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default OptimizedSettingsPanel;