import React, { memo, useMemo, useCallback, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2, Save, Tag, Search, Filter, Plus, X } from 'lucide-react';
import { KnowledgeItem, DocumentSource, ContentManagerProps, ContentManagerState } from '../../types';
import { useLightbulbActions } from '../../hooks/useLightbulbActions';

// Memoized individual content item component
const ContentItem = memo<{
  item: KnowledgeItem;
  onSelect?: (item: KnowledgeItem) => void;
}>(({ item, onSelect }) => {
  const handleClick = useCallback(() => {
    onSelect?.(item);
  }, [item, onSelect]);

  const formattedDate = useMemo(() => {
    return new Date(item.timestamp).toLocaleDateString();
  }, [item.timestamp]);

  const truncatedContent = useMemo(() => {
    return item.content.length > 150 ? item.content.substring(0, 150) + '...' : item.content;
  }, [item.content]);

  return (
    <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={handleClick}>
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium">{item.title}</CardTitle>
        <div className="text-xs text-muted-foreground">{formattedDate}</div>
      </CardHeader>
      <CardContent className="pt-0">
        <p className="text-sm text-muted-foreground mb-2">{truncatedContent}</p>
        {item.tags && item.tags.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {item.tags.slice(0, 3).map((tag) => (
              <Badge key={tag} variant="secondary" className="text-xs">
                {tag}
              </Badge>
            ))}
            {item.tags.length > 3 && (
              <Badge variant="outline" className="text-xs">
                +{item.tags.length - 3}
              </Badge>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
});

ContentItem.displayName = 'ContentItem';

// Memoized tag selector component
const TagSelector = memo<{
  availableTags: string[];
  selectedTags: string[];
  onTagsChange: (tags: string[]) => void;
}>(({ availableTags, selectedTags, onTagsChange }) => {
  const handleTagToggle = useCallback((tag: string) => {
    const newTags = selectedTags.includes(tag)
      ? selectedTags.filter(t => t !== tag)
      : [...selectedTags, tag];
    onTagsChange(newTags);
  }, [selectedTags, onTagsChange]);

  const sortedTags = useMemo(() => {
    return [...availableTags].sort();
  }, [availableTags]);

  return (
    <div className="space-y-2">
      <div className="text-sm font-medium">Tags</div>
      <div className="flex flex-wrap gap-1 max-h-32 overflow-y-auto">
        {sortedTags.map((tag) => (
          <Badge
            key={tag}
            variant={selectedTags.includes(tag) ? "default" : "outline"}
            className="cursor-pointer text-xs"
            onClick={() => handleTagToggle(tag)}
          >
            {tag}
          </Badge>
        ))}
      </div>
    </div>
  );
});

TagSelector.displayName = 'TagSelector';

// Memoized search and filter component
const SearchAndFilter = memo<{
  searchQuery: string;
  onSearchChange: (query: string) => void;
  selectedCategory: string;
  onCategoryChange: (category: string) => void;
  categories: string[];
  showFilters: boolean;
  onToggleFilters: () => void;
}>(({ searchQuery, onSearchChange, selectedCategory, onCategoryChange, categories, showFilters, onToggleFilters }) => {
  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    onSearchChange(e.target.value);
  }, [onSearchChange]);

  const handleCategoryChange = useCallback((value: string) => {
    onCategoryChange(value);
  }, [onCategoryChange]);

  return (
    <div className="space-y-4">
      <div className="flex gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search content..."
            value={searchQuery}
            onChange={handleSearchChange}
            className="pl-10"
          />
        </div>
        <Button
          variant="outline"
          size="icon"
          onClick={onToggleFilters}
          className={showFilters ? "bg-accent" : ""}
        >
          <Filter className="h-4 w-4" />
        </Button>
      </div>
      
      {showFilters && (
        <div className="space-y-3 p-3 border rounded-lg bg-muted/50">
          <div>
            <label className="text-sm font-medium mb-2 block">Category</label>
            <Select value={selectedCategory} onValueChange={handleCategoryChange}>
              <SelectTrigger>
                <SelectValue placeholder="All categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All categories</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category} value={category}>
                    {category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      )}
    </div>
  );
});

SearchAndFilter.displayName = 'SearchAndFilter';

// Main optimized ContentManager component
const OptimizedContentManager = memo<ContentManagerProps>(({ 
  contentItems, 
  tags, 
  sources, 
  categories, 
  searchQuery, 
  onContentUpdate, 
  onTagsUpdate, 
  onSourcesUpdate, 
  onCategoriesUpdate, 
  onSearchQueryChange 
}) => {
  const {
    inputValue,
    loading,
    setInputValue,
    saveContent,
    addTags
  } = useLightbulbActions();

  const [localState, setLocalState] = useState<ContentManagerState>({
    inputValue: '',
    selectedTags: [],
    selectedCategory: 'all',
    isProcessing: false,
    processingProgress: 0,
    lastProcessedContent: null
  });

  const [showFilters, setShowFilters] = useState(false);

  // Memoized filtered content
  const filteredContent = useMemo(() => {
    let filtered = [...contentItems];

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(item => 
        item.title.toLowerCase().includes(query) ||
        item.content.toLowerCase().includes(query) ||
        item.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }

    // Apply category filter
    if (localState.selectedCategory !== 'all') {
      filtered = filtered.filter(item => item.category === localState.selectedCategory);
    }

    return filtered;
  }, [contentItems, searchQuery, localState.selectedCategory]);

  // Memoized category list
  const categoryList = useMemo(() => {
    return Array.from(new Set(categories));
  }, [categories]);

  // Callbacks
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setInputValue(value);
    setLocalState(prev => ({ ...prev, inputValue: value }));
  }, [setInputValue]);

  const handleSaveContent = useCallback(async () => {
    if (inputValue.trim()) {
      setLocalState(prev => ({ ...prev, isProcessing: true, processingProgress: 0 }));
      try {
        await saveContent();
        setLocalState(prev => ({ 
          ...prev, 
          inputValue: '', 
          isProcessing: false, 
          processingProgress: 100 
        }));
      } catch (error) {
        console.error('Error saving content:', error);
        setLocalState(prev => ({ ...prev, isProcessing: false }));
      }
    }
  }, [inputValue, saveContent]);

  const handleTagsChange = useCallback((newTags: string[]) => {
    setLocalState(prev => ({ ...prev, selectedTags: newTags }));
  }, []);

  const handleCategoryChange = useCallback((category: string) => {
    setLocalState(prev => ({ ...prev, selectedCategory: category }));
  }, []);

  const handleToggleFilters = useCallback(() => {
    setShowFilters(prev => !prev);
  }, []);

  const handleItemSelect = useCallback((item: KnowledgeItem) => {
    // Handle item selection logic
    console.log('Selected item:', item);
  }, []);

  return (
    <div className="space-y-6">
      {/* Content Input Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Plus className="h-5 w-5" />
            Add New Content
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Textarea
            placeholder="Enter your thoughts, notes, or ideas..."
            value={inputValue}
            onChange={handleInputChange}
            className="min-h-[120px] resize-none"
            disabled={localState.isProcessing}
          />
          
          {localState.selectedTags.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {localState.selectedTags.map((tag) => (
                <Badge key={tag} variant="secondary" className="text-xs">
                  {tag}
                  <X 
                    className="h-3 w-3 ml-1 cursor-pointer" 
                    onClick={() => handleTagsChange(localState.selectedTags.filter(t => t !== tag))}
                  />
                </Badge>
              ))}
            </div>
          )}
          
          <div className="flex justify-between items-center">
            <div className="text-sm text-muted-foreground">
              {inputValue.length} characters
            </div>
            <Button 
              onClick={handleSaveContent} 
              disabled={!inputValue.trim() || loading || localState.isProcessing}
              className="flex items-center gap-2"
            >
              {(loading || localState.isProcessing) ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Save className="h-4 w-4" />
              )}
              {localState.isProcessing ? 'Processing...' : 'Save Content'}
            </Button>
          </div>
        </CardContent>
      </Card>

      <Separator />

      {/* Search and Filter Section */}
      <SearchAndFilter
        searchQuery={searchQuery}
        onSearchChange={onSearchQueryChange}
        selectedCategory={localState.selectedCategory}
        onCategoryChange={handleCategoryChange}
        categories={categoryList}
        showFilters={showFilters}
        onToggleFilters={handleToggleFilters}
      />

      {/* Tag Selector */}
      {showFilters && (
        <TagSelector
          availableTags={tags}
          selectedTags={localState.selectedTags}
          onTagsChange={handleTagsChange}
        />
      )}

      {/* Content List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Content ({filteredContent.length})</span>
            <Badge variant="outline">{contentItems.length} total</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-[600px]">
            <div className="space-y-3">
              {filteredContent.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  {searchQuery ? 'No content matches your search.' : 'No content available. Add some content to get started!'}
                </div>
              ) : (
                filteredContent.map((item) => (
                  <ContentItem
                    key={item.id}
                    item={item}
                    onSelect={handleItemSelect}
                  />
                ))
              )}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>
    </div>
  );
});

OptimizedContentManager.displayName = 'OptimizedContentManager';

export default OptimizedContentManager;