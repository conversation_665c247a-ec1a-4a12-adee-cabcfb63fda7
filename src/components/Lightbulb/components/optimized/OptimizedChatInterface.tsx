import React, { memo, useMemo, useCallback, useState, useRef, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Send, 
  Bot, 
  User, 
  Clock, 
  AlertCircle, 
  FileText, 
  Settings,
  Loader2,
  Copy,
  ThumbsUp,
  ThumbsDown
} from 'lucide-react';
import { ChatMessage, DocumentSource, ChatInterfaceProps, ChatInterfaceState } from '../../types';
import { useLightbulbActions } from '../../hooks/useLightbulbActions';

// Virtual list item height
const MESSAGE_HEIGHT = 120;
const VISIBLE_MESSAGES = 10;

// Memoized message component
const MessageItem = memo<{
  message: ChatMessage;
  onCopy?: (content: string) => void;
  onFeedback?: (messageId: string, type: 'positive' | 'negative') => void;
}>(({ message, onCopy, onFeedback }) => {
  const handleCopy = useCallback(() => {
    onCopy?.(message.content);
  }, [message.content, onCopy]);

  const handlePositiveFeedback = useCallback(() => {
    onFeedback?.(message.id, 'positive');
  }, [message.id, onFeedback]);

  const handleNegativeFeedback = useCallback(() => {
    onFeedback?.(message.id, 'negative');
  }, [message.id, onFeedback]);

  const formattedTime = useMemo(() => {
    return new Date(message.timestamp).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  }, [message.timestamp]);

  const isUser = message.role === 'user';
  const isError = message.isError;

  return (
    <div className={`flex gap-3 p-4 ${isUser ? 'flex-row-reverse' : 'flex-row'}`}>
      <Avatar className="h-8 w-8 flex-shrink-0">
        <AvatarFallback className={isUser ? 'bg-primary text-primary-foreground' : 'bg-secondary'}>
          {isUser ? <User className="h-4 w-4" /> : <Bot className="h-4 w-4" />}
        </AvatarFallback>
      </Avatar>
      
      <div className={`flex-1 space-y-2 ${isUser ? 'text-right' : 'text-left'}`}>
        <div className={`inline-block max-w-[80%] p-3 rounded-lg ${
          isUser 
            ? 'bg-primary text-primary-foreground ml-auto' 
            : isError 
              ? 'bg-destructive/10 border border-destructive/20 text-destructive'
              : 'bg-muted'
        }`}>
          {isError && (
            <div className="flex items-center gap-2 mb-2 text-destructive">
              <AlertCircle className="h-4 w-4" />
              <span className="text-sm font-medium">Error</span>
            </div>
          )}
          
          <div className="text-sm whitespace-pre-wrap">{message.content}</div>
          
          {message.sources && message.sources.length > 0 && (
            <div className="mt-2 pt-2 border-t border-border/50">
              <div className="text-xs opacity-70 mb-1">Sources:</div>
              <div className="flex flex-wrap gap-1">
                {message.sources.slice(0, 3).map((source, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    <FileText className="h-3 w-3 mr-1" />
                    {source}
                  </Badge>
                ))}
                {message.sources.length > 3 && (
                  <Badge variant="outline" className="text-xs">
                    +{message.sources.length - 3} more
                  </Badge>
                )}
              </div>
            </div>
          )}
          
          {message.metadata?.tokens && (
            <div className="mt-2 pt-2 border-t border-border/50 text-xs opacity-70">
              Tokens: {message.metadata.tokens}
              {message.metadata.processing_time && (
                <span className="ml-2">• {message.metadata.processing_time}ms</span>
              )}
            </div>
          )}
        </div>
        
        <div className={`flex items-center gap-2 text-xs text-muted-foreground ${
          isUser ? 'justify-end' : 'justify-start'
        }`}>
          <Clock className="h-3 w-3" />
          <span>{formattedTime}</span>
          
          {!isUser && !isError && (
            <div className="flex items-center gap-1 ml-2">
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
                onClick={handleCopy}
              >
                <Copy className="h-3 w-3" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
                onClick={handlePositiveFeedback}
              >
                <ThumbsUp className="h-3 w-3" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
                onClick={handleNegativeFeedback}
              >
                <ThumbsDown className="h-3 w-3" />
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
});

MessageItem.displayName = 'MessageItem';

// Virtualized message list component
const VirtualizedMessageList = memo<{
  messages: ChatMessage[];
  onCopy?: (content: string) => void;
  onFeedback?: (messageId: string, type: 'positive' | 'negative') => void;
}>(({ messages, onCopy, onFeedback }) => {
  const [scrollTop, setScrollTop] = useState(0);
  const [containerHeight, setContainerHeight] = useState(600);
  const containerRef = useRef<HTMLDivElement>(null);

  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop);
  }, []);

  useEffect(() => {
    const updateHeight = () => {
      if (containerRef.current) {
        setContainerHeight(containerRef.current.clientHeight);
      }
    };
    
    updateHeight();
    window.addEventListener('resize', updateHeight);
    return () => window.removeEventListener('resize', updateHeight);
  }, []);

  const { visibleMessages, totalHeight, offsetY } = useMemo(() => {
    const startIndex = Math.floor(scrollTop / MESSAGE_HEIGHT);
    const endIndex = Math.min(
      startIndex + Math.ceil(containerHeight / MESSAGE_HEIGHT) + 1,
      messages.length
    );
    
    return {
      visibleMessages: messages.slice(startIndex, endIndex),
      totalHeight: messages.length * MESSAGE_HEIGHT,
      offsetY: startIndex * MESSAGE_HEIGHT
    };
  }, [messages, scrollTop, containerHeight]);

  return (
    <div 
      ref={containerRef}
      className="h-full overflow-auto"
      onScroll={handleScroll}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        <div style={{ transform: `translateY(${offsetY}px)` }}>
          {visibleMessages.map((message) => (
            <div key={message.id} style={{ height: MESSAGE_HEIGHT }}>
              <MessageItem
                message={message}
                onCopy={onCopy}
                onFeedback={onFeedback}
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
});

VirtualizedMessageList.displayName = 'VirtualizedMessageList';

// Chat settings component
const ChatSettings = memo<{
  chatMode: 'general' | 'research' | 'creative' | 'technical';
  onModeChange: (mode: 'general' | 'research' | 'creative' | 'technical') => void;
  selectedSources: string[];
  availableSources: DocumentSource[];
  onSourcesChange: (sources: string[]) => void;
  showSources: boolean;
  onToggleShowSources: () => void;
}>(({ 
  chatMode, 
  onModeChange, 
  selectedSources, 
  availableSources, 
  onSourcesChange, 
  showSources, 
  onToggleShowSources 
}) => {
  const handleModeChange = useCallback((value: string) => {
    onModeChange(value as 'general' | 'research' | 'creative' | 'technical');
  }, [onModeChange]);

  const handleSourceToggle = useCallback((sourceId: string) => {
    const newSources = selectedSources.includes(sourceId)
      ? selectedSources.filter(id => id !== sourceId)
      : [...selectedSources, sourceId];
    onSourcesChange(newSources);
  }, [selectedSources, onSourcesChange]);

  return (
    <div className="space-y-4 p-4 border-b">
      <div className="flex items-center justify-between">
        <div className="space-y-2">
          <label className="text-sm font-medium">Chat Mode</label>
          <Select value={chatMode} onValueChange={handleModeChange}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="general">General</SelectItem>
              <SelectItem value="research">Research</SelectItem>
              <SelectItem value="creative">Creative</SelectItem>
              <SelectItem value="technical">Technical</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <Button
          variant="outline"
          size="sm"
          onClick={onToggleShowSources}
          className="flex items-center gap-2"
        >
          <FileText className="h-4 w-4" />
          Sources ({selectedSources.length})
        </Button>
      </div>
      
      {showSources && (
        <div className="space-y-2">
          <label className="text-sm font-medium">Available Sources</label>
          <div className="max-h-32 overflow-y-auto space-y-1">
            {availableSources.map((source) => (
              <div key={source.id} className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id={source.id}
                  checked={selectedSources.includes(source.id)}
                  onChange={() => handleSourceToggle(source.id)}
                  className="rounded"
                />
                <label htmlFor={source.id} className="text-sm cursor-pointer flex-1">
                  {source.name}
                </label>
                <Badge variant="outline" className="text-xs">
                  {source.type}
                </Badge>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
});

ChatSettings.displayName = 'ChatSettings';

// Main optimized chat interface
const OptimizedChatInterface = memo<ChatInterfaceProps>(({ 
  messages, 
  sources, 
  onMessagesUpdate, 
  onSourcesUpdate, 
  className 
}) => {
  const { addMessage, loading } = useLightbulbActions();
  
  const [localState, setLocalState] = useState<ChatInterfaceState>({
    inputMessage: '',
    isLoading: false,
    selectedSources: [],
    chatMode: 'general',
    showSources: false,
    autoScroll: true
  });

  const [showSettings, setShowSettings] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (localState.autoScroll && messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages.length, localState.autoScroll]);

  // Memoized sorted messages
  const sortedMessages = useMemo(() => {
    return [...messages].sort((a, b) => a.timestamp - b.timestamp);
  }, [messages]);

  // Callbacks
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setLocalState(prev => ({ ...prev, inputMessage: e.target.value }));
  }, []);

  const handleSendMessage = useCallback(async () => {
    if (!localState.inputMessage.trim() || localState.isLoading) return;

    const userMessage: ChatMessage = {
      id: `msg_${Date.now()}`,
      content: localState.inputMessage,
      role: 'user',
      timestamp: Date.now(),
      context_ids: localState.selectedSources
    };

    setLocalState(prev => ({ 
      ...prev, 
      inputMessage: '', 
      isLoading: true 
    }));

    try {
      addMessage(userMessage);
      
      // Simulate AI response (replace with actual API call)
      setTimeout(() => {
        const aiMessage: ChatMessage = {
          id: `msg_${Date.now() + 1}`,
          content: `This is a simulated AI response to: "${userMessage.content}"`,
          role: 'assistant',
          timestamp: Date.now(),
          sources: localState.selectedSources.slice(0, 2),
          metadata: {
            tokens: 150,
            processing_time: 1200,
            model: 'gpt-4'
          }
        };
        
        addMessage(aiMessage);
        setLocalState(prev => ({ ...prev, isLoading: false }));
      }, 2000);
      
    } catch (error) {
      setLocalState(prev => ({ ...prev, isLoading: false }));
    }
  }, [localState.inputMessage, localState.isLoading, localState.selectedSources, addMessage]);

  const handleKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  }, [handleSendMessage]);

  const handleCopyMessage = useCallback((content: string) => {
    navigator.clipboard.writeText(content);
    // Could add toast notification here
  }, []);

  const handleMessageFeedback = useCallback((messageId: string, type: 'positive' | 'negative') => {
    // Implement feedback logic
  }, []);

  const handleModeChange = useCallback((mode: 'general' | 'research' | 'creative' | 'technical') => {
    setLocalState(prev => ({ ...prev, chatMode: mode }));
  }, []);

  const handleSourcesChange = useCallback((newSources: string[]) => {
    setLocalState(prev => ({ ...prev, selectedSources: newSources }));
  }, []);

  const handleToggleShowSources = useCallback(() => {
    setLocalState(prev => ({ ...prev, showSources: !prev.showSources }));
  }, []);

  const handleToggleSettings = useCallback(() => {
    setShowSettings(prev => !prev);
  }, []);

  return (
    <Card className={className}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Bot className="h-5 w-5" />
            AI Chat ({sortedMessages.length} messages)
          </CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={handleToggleSettings}
            className={showSettings ? "bg-accent" : ""}
          >
            <Settings className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      
      {showSettings && (
        <ChatSettings
          chatMode={localState.chatMode}
          onModeChange={handleModeChange}
          selectedSources={localState.selectedSources}
          availableSources={sources}
          onSourcesChange={handleSourcesChange}
          showSources={localState.showSources}
          onToggleShowSources={handleToggleShowSources}
        />
      )}
      
      <CardContent className="p-0">
        <div className="h-[600px] flex flex-col">
          {/* Messages Area */}
          <div className="flex-1 overflow-hidden">
            {sortedMessages.length === 0 ? (
              <div className="flex items-center justify-center h-full text-muted-foreground">
                <div className="text-center space-y-2">
                  <Bot className="h-12 w-12 mx-auto opacity-50" />
                  <p>Start a conversation with AI</p>
                  <p className="text-sm">Ask questions, get insights, or brainstorm ideas</p>
                </div>
              </div>
            ) : (
              <VirtualizedMessageList
                messages={sortedMessages}
                onCopy={handleCopyMessage}
                onFeedback={handleMessageFeedback}
              />
            )}
            <div ref={messagesEndRef} />
          </div>
          
          <Separator />
          
          {/* Input Area */}
          <div className="p-4">
            <div className="flex gap-2">
              <Input
                placeholder={`Type your message... (${localState.chatMode} mode)`}
                value={localState.inputMessage}
                onChange={handleInputChange}
                onKeyPress={handleKeyPress}
                disabled={localState.isLoading}
                className="flex-1"
              />
              <Button
                onClick={handleSendMessage}
                disabled={!localState.inputMessage.trim() || localState.isLoading}
                size="icon"
              >
                {localState.isLoading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Send className="h-4 w-4" />
                )}
              </Button>
            </div>
            
            {localState.selectedSources.length > 0 && (
              <div className="mt-2 flex flex-wrap gap-1">
                <span className="text-xs text-muted-foreground">Using sources:</span>
                {localState.selectedSources.slice(0, 3).map((sourceId) => {
                  const source = sources.find(s => s.id === sourceId);
                  return source ? (
                    <Badge key={sourceId} variant="outline" className="text-xs">
                      {source.name}
                    </Badge>
                  ) : null;
                })}
                {localState.selectedSources.length > 3 && (
                  <Badge variant="outline" className="text-xs">
                    +{localState.selectedSources.length - 3} more
                  </Badge>
                )}
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
});

OptimizedChatInterface.displayName = 'OptimizedChatInterface';

export default OptimizedChatInterface;