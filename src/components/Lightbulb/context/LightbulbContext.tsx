import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { KnowledgeItem, DocumentSource, ChatMessage, Category } from '../types';
import { invokeTauriCommand } from '../utils/tauriCommands';
import { autoTaggingService, TagSuggestion } from '../services/autoTaggingService';
import { categorizationService } from '../services/categorizationService';
import { pagesService } from '../services/pagesService';
import { projectService } from '../services/projectService';
import { onboardingService } from '@/services/onboardingService';
import { feedbackService } from '@/services/feedbackService';
import { performanceService } from '@/services/performanceService';

// State interface
export interface LightbulbState {
  // Core data
  userCount: number;
  contentItems: KnowledgeItem[];
  savedItems: KnowledgeItem[];
  sources: DocumentSource[];
  messages: ChatMessage[];
  tags: string[];
  categories: Category[];
  pages: any[];
  projects: any[];
  searchResults: any[];
  
  // UI state
  activeSection: string;
  searchQuery: string;
  inputValue: string;
  loading: boolean;
  
  // Modal/Dialog state
  showQuickCapture: boolean;
  showAdvancedSearch: boolean;
  showMobileSidebar: boolean;
  showMobileCapture: boolean;
  showOnboarding: boolean;
  showTutorial: boolean;
  showHelp: boolean;
  showFeedback: boolean;
  
  // Mobile state
  mobileKeyboardOpen: boolean;
  
  // Tutorial state
  tutorialType: 'basic' | 'content-processing' | 'visual-workspace' | 'ai-features';
  helpQuery: string;
}

// Action types
export type LightbulbAction =
  | { type: 'SET_USER_COUNT'; payload: number }
  | { type: 'SET_CONTENT_ITEMS'; payload: KnowledgeItem[] }
  | { type: 'SET_SAVED_ITEMS'; payload: KnowledgeItem[] }
  | { type: 'ADD_SAVED_ITEM'; payload: KnowledgeItem }
  | { type: 'SET_SOURCES'; payload: DocumentSource[] }
  | { type: 'SET_MESSAGES'; payload: ChatMessage[] }
  | { type: 'ADD_MESSAGE'; payload: ChatMessage }
  | { type: 'SET_TAGS'; payload: string[] }
  | { type: 'ADD_TAGS'; payload: string[] }
  | { type: 'SET_CATEGORIES'; payload: Category[] }
  | { type: 'SET_PAGES'; payload: any[] }
  | { type: 'SET_PROJECTS'; payload: any[] }
  | { type: 'SET_SEARCH_RESULTS'; payload: any[] }
  | { type: 'SET_ACTIVE_SECTION'; payload: string }
  | { type: 'SET_SEARCH_QUERY'; payload: string }
  | { type: 'SET_INPUT_VALUE'; payload: string }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'TOGGLE_QUICK_CAPTURE' }
  | { type: 'TOGGLE_ADVANCED_SEARCH' }
  | { type: 'TOGGLE_MOBILE_SIDEBAR' }
  | { type: 'TOGGLE_MOBILE_CAPTURE' }
  | { type: 'SET_SHOW_ONBOARDING'; payload: boolean }
  | { type: 'SET_SHOW_TUTORIAL'; payload: boolean }
  | { type: 'SET_SHOW_HELP'; payload: boolean }
  | { type: 'SET_SHOW_FEEDBACK'; payload: boolean }
  | { type: 'SET_MOBILE_KEYBOARD_OPEN'; payload: boolean }
  | { type: 'SET_TUTORIAL_TYPE'; payload: 'basic' | 'content-processing' | 'visual-workspace' | 'ai-features' }
  | { type: 'SET_HELP_QUERY'; payload: string }
  | { type: 'RESET_STATE' };

// Initial state
const initialState: LightbulbState = {
  userCount: 0,
  contentItems: [],
  savedItems: [],
  sources: [],
  messages: [],
  tags: [],
  categories: [],
  pages: [],
  projects: [],
  searchResults: [],
  activeSection: 'thoughts',
  searchQuery: '',
  inputValue: '',
  loading: false,
  showQuickCapture: false,
  showAdvancedSearch: false,
  showMobileSidebar: false,
  showMobileCapture: false,
  showOnboarding: false,
  showTutorial: false,
  showHelp: false,
  showFeedback: false,
  mobileKeyboardOpen: false,
  tutorialType: 'basic',
  helpQuery: ''
};

// Reducer function
function lightbulbReducer(state: LightbulbState, action: LightbulbAction): LightbulbState {
  switch (action.type) {
    case 'SET_USER_COUNT':
      return { ...state, userCount: action.payload };
    case 'SET_CONTENT_ITEMS':
      return { ...state, contentItems: action.payload };
    case 'SET_SAVED_ITEMS':
      return { ...state, savedItems: action.payload };
    case 'ADD_SAVED_ITEM':
      return {
        ...state,
        savedItems: [action.payload, ...state.savedItems],
        contentItems: [action.payload, ...state.contentItems]
      };
    case 'SET_SOURCES':
      return { ...state, sources: action.payload };
    case 'SET_MESSAGES':
      return { ...state, messages: action.payload };
    case 'ADD_MESSAGE':
      return { ...state, messages: [...state.messages, action.payload] };
    case 'SET_TAGS':
      return { ...state, tags: action.payload };
    case 'ADD_TAGS':
      const newTags = new Set([...state.tags, ...action.payload]);
      return { ...state, tags: Array.from(newTags) };
    case 'SET_CATEGORIES':
      return { ...state, categories: action.payload };
    case 'SET_PAGES':
      return { ...state, pages: action.payload };
    case 'SET_PROJECTS':
      return { ...state, projects: action.payload };
    case 'SET_SEARCH_RESULTS':
      return { ...state, searchResults: action.payload };
    case 'SET_ACTIVE_SECTION':
      return { ...state, activeSection: action.payload };
    case 'SET_SEARCH_QUERY':
      return { ...state, searchQuery: action.payload };
    case 'SET_INPUT_VALUE':
      return { ...state, inputValue: action.payload };
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    case 'TOGGLE_QUICK_CAPTURE':
      return { ...state, showQuickCapture: !state.showQuickCapture };
    case 'TOGGLE_ADVANCED_SEARCH':
      return { ...state, showAdvancedSearch: !state.showAdvancedSearch };
    case 'TOGGLE_MOBILE_SIDEBAR':
      return { ...state, showMobileSidebar: !state.showMobileSidebar };
    case 'TOGGLE_MOBILE_CAPTURE':
      return { ...state, showMobileCapture: !state.showMobileCapture };
    case 'SET_SHOW_ONBOARDING':
      return { ...state, showOnboarding: action.payload };
    case 'SET_SHOW_TUTORIAL':
      return { ...state, showTutorial: action.payload };
    case 'SET_SHOW_HELP':
      return { ...state, showHelp: action.payload };
    case 'SET_SHOW_FEEDBACK':
      return { ...state, showFeedback: action.payload };
    case 'SET_MOBILE_KEYBOARD_OPEN':
      return { ...state, mobileKeyboardOpen: action.payload };
    case 'SET_TUTORIAL_TYPE':
      return { ...state, tutorialType: action.payload };
    case 'SET_HELP_QUERY':
      return { ...state, helpQuery: action.payload };
    case 'RESET_STATE':
      return initialState;
    default:
      return state;
  }
}

// Context interface
interface LightbulbContextType {
  state: LightbulbState;
  dispatch: React.Dispatch<LightbulbAction>;
  actions: {
    // Data fetching actions
    fetchUserCount: () => Promise<void>;
    fetchSavedContent: () => Promise<void>;
    fetchSources: () => Promise<void>;
    loadCategories: () => Promise<void>;
    loadPages: () => Promise<void>;
    loadProjects: () => Promise<void>;
    
    // Content actions
    saveContent: () => Promise<void>;
    
    // Integration actions
    initializeIntegrationServices: () => void;
    handleOnboardingComplete: () => void;
    handleTutorialComplete: () => void;
    handleStartTutorial: (type: 'basic' | 'content-processing' | 'visual-workspace' | 'ai-features') => void;
    handleShowHelp: (query?: string) => void;
    handleShowFeedback: () => void;
    
    // Computed values
    getFilteredContent: () => KnowledgeItem[];
  };
}

// Create context
const LightbulbContext = createContext<LightbulbContextType | undefined>(undefined);

// Provider component
export const LightbulbProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(lightbulbReducer, initialState);

  // Data fetching functions
  const fetchUserCount = async () => {
    try {
      const response = await invokeTauriCommand('get_user_count');
      dispatch({ type: 'SET_USER_COUNT', payload: response.count || 0 });
    } catch (error) {
    }
  };

  const fetchSavedContent = async () => {
    try {
      const content: KnowledgeItem[] = await invokeTauriCommand('get_all_content');
      dispatch({ type: 'SET_SAVED_ITEMS', payload: content });
      dispatch({ type: 'SET_CONTENT_ITEMS', payload: content });
      
      // Extract unique tags
      const allTags = new Set<string>();
      content.forEach(item => {
        item.tags?.forEach(tag => allTags.add(tag));
      });
      dispatch({ type: 'SET_TAGS', payload: Array.from(allTags) });
    } catch (error) {
    }
  };

  const fetchSources = async () => {
    try {
      const sourcesData: DocumentSource[] = await invokeTauriCommand('get_all_sources');
      dispatch({ type: 'SET_SOURCES', payload: sourcesData });
    } catch (error) {
    }
  };

  const loadCategories = async () => {
    try {
      await categorizationService.loadUserPatterns();
      const allCategories = categorizationService.getCategories();
      // Map to match the expected Category interface
      const mappedCategories = allCategories.map(cat => ({
        ...cat,
        created_at: Date.now() / 1000
      }));
      dispatch({ type: 'SET_CATEGORIES', payload: mappedCategories });
    } catch (error) {
    }
  };

  const loadPages = async () => {
    try {
      const pagesData = await pagesService.getAllPages();
      dispatch({ type: 'SET_PAGES', payload: pagesData });
    } catch (error) {
    }
  };

  const loadProjects = async () => {
    try {
      const projectsData = await projectService.getAllProjects();
      dispatch({ type: 'SET_PROJECTS', payload: projectsData });
    } catch (error) {
    }
  };

  const saveContent = async () => {
    if (state.inputValue.trim()) {
      dispatch({ type: 'SET_LOADING', payload: true });
      try {
        // Generate auto-tags
        const tagResult = await autoTaggingService.analyzeContent(state.inputValue);
        const autoTags = tagResult.suggestedTags.map((suggestion: TagSuggestion) => suggestion.tag);
        
        const newItem: KnowledgeItem = {
          id: `item_${Date.now()}`,
          title: state.inputValue.substring(0, 50) + (state.inputValue.length > 50 ? '...' : ''),
          content: state.inputValue,
          tags: autoTags,
          timestamp: new Date().toISOString(),
          created_at: Date.now() / 1000,
          updated_at: Date.now() / 1000,
          source_type: 'manual'
        };
        
        const savedItem = await invokeTauriCommand('save_content', { 
          request: {
            title: newItem.title,
            content: state.inputValue,
            tags: autoTags
          }
        });
        
        dispatch({ type: 'ADD_SAVED_ITEM', payload: savedItem });
        dispatch({ type: 'SET_INPUT_VALUE', payload: '' });
        dispatch({ type: 'ADD_TAGS', payload: autoTags });
        
        await fetchUserCount();
      } catch (error) {
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    }
  };

  // Integration functions
  const initializeIntegrationServices = () => {
    performanceService.initialize();
    
    if (onboardingService.shouldShowOnboarding()) {
      dispatch({ type: 'SET_SHOW_ONBOARDING', payload: true });
    }
    
    feedbackService.trackPageView('lightbulb_main');
    feedbackService.trackFeatureUsage('lightbulb', 'component_loaded');
  };

  const handleOnboardingComplete = () => {
    onboardingService.completeOnboarding();
    dispatch({ type: 'SET_SHOW_ONBOARDING', payload: false });
    feedbackService.trackEvent('onboarding', 'completed', 'main_flow');
  };

  const handleTutorialComplete = () => {
    onboardingService.completeTutorial(state.tutorialType);
    dispatch({ type: 'SET_SHOW_TUTORIAL', payload: false });
    feedbackService.trackEvent('tutorial', 'completed', state.tutorialType);
  };

  const handleStartTutorial = (type: 'basic' | 'content-processing' | 'visual-workspace' | 'ai-features') => {
    dispatch({ type: 'SET_TUTORIAL_TYPE', payload: type });
    dispatch({ type: 'SET_SHOW_TUTORIAL', payload: true });
    onboardingService.startTutorial(type, 5);
    feedbackService.trackEvent('tutorial', 'started', type);
  };

  const handleShowHelp = (query = '') => {
    dispatch({ type: 'SET_HELP_QUERY', payload: query });
    dispatch({ type: 'SET_SHOW_HELP', payload: true });
  };

  const handleShowFeedback = () => {
    dispatch({ type: 'SET_SHOW_FEEDBACK', payload: true });
  };

  // Computed values
  const getFilteredContent = (): KnowledgeItem[] => {
    if (!state.searchQuery) return state.contentItems;
    const query = state.searchQuery.toLowerCase();
    return state.contentItems.filter(item => 
      item.title.toLowerCase().includes(query) || 
      item.content.toLowerCase().includes(query) ||
      item.tags.some(tag => tag.toLowerCase().includes(query))
    );
  };

  // Initialize data on mount
  useEffect(() => {
    fetchUserCount();
    fetchSavedContent();
    fetchSources();
    loadCategories();
    loadPages();
    loadProjects();
    initializeIntegrationServices();
  }, []);

  const actions = {
    fetchUserCount,
    fetchSavedContent,
    fetchSources,
    loadCategories,
    loadPages,
    loadProjects,
    saveContent,
    initializeIntegrationServices,
    handleOnboardingComplete,
    handleTutorialComplete,
    handleStartTutorial,
    handleShowHelp,
    handleShowFeedback,
    getFilteredContent
  };

  return (
    <LightbulbContext.Provider value={{ state, dispatch, actions }}>
      {children}
    </LightbulbContext.Provider>
  );
};

// Custom hook to use the context
export const useLightbulb = () => {
  const context = useContext(LightbulbContext);
  if (context === undefined) {
    throw new Error('useLightbulb must be used within a LightbulbProvider');
  }
  return context;
};

export default LightbulbContext;