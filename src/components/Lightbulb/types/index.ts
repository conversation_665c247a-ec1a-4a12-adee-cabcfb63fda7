// Shared TypeScript definitions for Lightbulb component
// Extracted from LightbulbComponent.tsx for better type safety and reusability

// ============================================================================
// Core Data Models
// ============================================================================

export interface KnowledgeItem {
  id: string;
  title: string;
  content: string;
  tags: string[];
  created_at: number;
  updated_at: number;
  timestamp: string;
  source_type?: 'manual' | 'document' | 'url' | 'note';
  source_url?: string;
  file_type?: string;
  category?: string;
  priority?: 'low' | 'medium' | 'high';
  metadata?: Record<string, any>;
}

export interface DocumentSource {
  id: string;
  name: string;
  type: 'pdf' | 'txt' | 'docx' | 'url' | 'note';
  content: string;
  url?: string;
  uploaded_at: number;
  size?: number;
  metadata?: Record<string, any>;
}

export interface ChatMessage {
  id: string;
  content: string;
  role: 'user' | 'assistant' | 'system';
  timestamp: number;
  context_ids?: string[];
  sources?: string[];
  isError?: boolean;
  metadata?: {
    tokens_used?: number;
    tokens?: number;
    model?: string;
    processing_time?: number;
    processingTime?: number;
  };
}

export interface Project {
  id: string;
  name: string;
  description?: string;
  created_at: number;
  updated_at: number;
  status: 'active' | 'completed' | 'archived';
  items: string[]; // KnowledgeItem IDs
  tags: string[];
  metadata?: Record<string, any>;
}

export interface Page {
  id: string;
  title: string;
  content: string;
  created_at: number;
  updated_at: number;
  parent_id?: string;
  children?: string[];
  tags: string[];
  metadata?: Record<string, any>;
}

// ============================================================================
// Tauri Command Types
// ============================================================================

export interface SaveContentRequest {
  title: string;
  content: string;
  tags?: string[];
  source_type?: KnowledgeItem['source_type'];
  source_url?: string;
  metadata?: Record<string, any>;
}

export interface ChatRequest {
  message: string;
  context_ids?: string[];
  model?: string;
  temperature?: number;
  max_tokens?: number;
}

export interface UserCountResponse {
  count: number;
}

export interface SearchRequest {
  query: string;
  filters?: {
    tags?: string[];
    source_type?: KnowledgeItem['source_type'][];
    date_range?: {
      start: number;
      end: number;
    };
    content_type?: string[];
  };
  sort?: {
    field: 'created_at' | 'updated_at' | 'title' | 'relevance';
    direction: 'asc' | 'desc';
  };
  limit?: number;
  offset?: number;
}

export interface SearchResponse {
  items: KnowledgeItem[];
  total: number;
  has_more: boolean;
}

// ============================================================================
// UI State Types
// ============================================================================

export type ActiveSection = 
  | 'chat'
  | 'processor'
  | 'visual'
  | 'thoughts'
  | 'pages'
  | 'projects'
  | 'humanizer'
  | 'search'
  | 'tags'
  | 'extension'
  | 'history'
  | 'test'
  | 'mobile-test';

export interface UIState {
  activeSection: ActiveSection;
  showQuickCapture: boolean;
  showAdvancedSearch: boolean;
  showMobileSidebar: boolean;
  showMobileCapture: boolean;
  mobileKeyboardOpen: boolean;
  loading: boolean;
}

export interface ModalState {
  showOnboarding: boolean;
  showTutorial: boolean;
  showHelp: boolean;
  showFeedback: boolean;
  tutorialType: 'basic' | 'content-processing' | 'visual-workspace' | 'ai-features';
}

// ============================================================================
// Service Types
// ============================================================================

export interface TagSuggestion {
  tag: string;
  confidence: number;
  category: 'topic' | 'type' | 'priority' | 'context';
}

export interface AutoTaggingResult {
  suggestedTags: TagSuggestion[];
  categories: string[];
  keywords: string[];
  sentiment?: 'positive' | 'negative' | 'neutral';
}

export interface Category {
  id: string;
  name: string;
  description?: string;
  color?: string;
  parent_id?: string;
  created_at: number;
}

export interface CategorySuggestion {
  category: Category;
  confidence: number;
  reasoning?: string;
}

// ============================================================================
// Component Props Types
// ============================================================================

export interface LightbulbComponentProps {
  className?: string;
  onSectionChange?: (section: ActiveSection) => void;
}

export interface ContentManagerProps {
  contentItems: KnowledgeItem[];
  tags: string[];
  sources: DocumentSource[];
  categories: string[];
  searchQuery: string;
  onContentUpdate: (items: KnowledgeItem[]) => void;
  onTagsUpdate: (tags: string[]) => void;
  onSourcesUpdate: (sources: DocumentSource[]) => void;
  onCategoriesUpdate: (categories: string[]) => void;
  onSearchQueryChange: (query: string) => void;
}

export interface ContentManagerState {
  inputValue: string;
  selectedTags: string[];
  selectedCategory: string;
  isProcessing: boolean;
  processingProgress: number;
  lastProcessedContent: KnowledgeItem | null;
}

export interface ChatInterfaceProps {
  messages: ChatMessage[];
  sources: DocumentSource[];
  onMessagesUpdate: (messages: ChatMessage[]) => void;
  onSourcesUpdate: (sources: DocumentSource[]) => void;
  className?: string;
}

export interface ChatInterfaceState {
  inputMessage: string;
  isLoading: boolean;
  selectedSources: string[];
  chatMode: 'general' | 'research' | 'creative' | 'technical';
  showSources: boolean;
  autoScroll: boolean;
}

export interface SearchResult {
  id: string;
  title: string;
  content: string;
  snippet?: string;
  relevance: number;
  relevanceScore: number;
  timestamp: number;
  lastModified: string;
  type: 'knowledge_item' | 'source' | 'document' | 'image' | 'video' | 'audio' | 'archive';
  tags?: string[];
  viewCount?: number;
  isFavorite?: boolean;
}

export interface SearchInterfaceProps {
  knowledgeItems?: KnowledgeItem[];
  sources?: DocumentSource[];
  onItemSelect?: (item: KnowledgeItem) => void;
  onSourceSelect?: (source: DocumentSource) => void;
  onResultSelect?: (result: SearchResult) => void;
  className?: string;
}

export interface SearchInterfaceState {
  query: string;
  results: SearchResult[];
  isLoading: boolean;
  searchType: 'all' | 'semantic' | 'keyword' | 'fuzzy';
  selectedTags: string[];
  selectedContentTypes: string[];
  dateRange: { start?: string; end?: string };
  filters: {
    tags: string[];
    dateRange: 'all' | 'today' | 'week' | 'month' | 'year';
    contentType: 'all' | 'document' | 'note' | 'link' | 'image';
    minRelevance: number;
  };
  sortBy: 'relevance' | 'date' | 'title' | 'views';
  sortOrder: 'asc' | 'desc';
  showFilters: boolean;
  selectedResults: string[];
  searchHistory: string[];
}

export interface SettingsPanelProps {
  isOpen: boolean;
  onClose: () => void;
  settings: {
    autoSaveInterval: number;
    enableNotifications: boolean;
    enableAutoTagging: boolean;
    defaultViewMode: string;
    aiModel: string;
    temperature: number;
    maxTokens: number;
    enableContextMemory: boolean;
    theme: string;
    fontSize: string;
    compactMode: boolean;
    showAnimations: boolean;
  };
  onSettingsChange: (settings: any) => void;
  onExportData: () => Promise<void>;
  onImportData: () => Promise<void>;
  onClearData: () => Promise<void>;
  onResetSettings: () => Promise<void>;
}

export interface SettingsPanelState {
  activeTab: 'general' | 'ai' | 'data' | 'appearance';
  isExporting: boolean;
  isImporting: boolean;
  isClearing: boolean;
  showConfirmDialog: boolean;
  confirmAction: 'export' | 'import' | 'clear' | 'reset' | null;
}

export interface SidebarItem {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  count?: number;
  badge?: string;
}

export interface HelpItem {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  action: () => void;
}

export interface QuickCaptureProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (content: string) => Promise<void>;
}

export interface MobileCaptureProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (content: string, media?: any) => Promise<void>;
}

export interface AdvancedSearchProps {
  isOpen: boolean;
  onClose: () => void;
  items: KnowledgeItem[];
  pages: Page[];
  onResultsChange: (results: any[]) => void;
}

// ============================================================================
// Hook Types
// ============================================================================

export interface ResponsiveState {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  screenWidth: number;
  screenHeight: number;
}

export interface TouchGestureState {
  isSwipeEnabled: boolean;
  swipeDirection: 'left' | 'right' | 'up' | 'down' | null;
  touchStartX: number;
  touchStartY: number;
}

// ============================================================================
// Error Types
// ============================================================================

export interface LightbulbError {
  code: string;
  message: string;
  details?: any;
  timestamp: number;
  context?: {
    component?: string;
    action?: string;
    userId?: string;
  };
}

export type ErrorSeverity = 'low' | 'medium' | 'high' | 'critical';

export interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: React.ErrorInfo;
  errorId?: string;
}

// ============================================================================
// Performance Types
// ============================================================================

export interface PerformanceMetrics {
  componentRenderTime: number;
  dataFetchTime: number;
  searchTime: number;
  memoryUsage?: number;
  timestamp: number;
}

export interface LoadingState {
  isLoading: boolean;
  loadingText?: string;
  progress?: number;
  error?: LightbulbError;
}

// ============================================================================
// Generic Utility Types
// ============================================================================

export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

// ============================================================================
// Event Types
// ============================================================================

export interface LightbulbEvent<T = any> {
  type: string;
  payload: T;
  timestamp: number;
  source: string;
}

export type EventHandler<T = any> = (event: LightbulbEvent<T>) => void;

// ============================================================================
// Configuration Types
// ============================================================================

export interface LightbulbConfig {
  theme: 'light' | 'dark' | 'auto';
  language: string;
  autoSave: boolean;
  autoSaveInterval: number;
  maxItems: number;
  enableAnalytics: boolean;
  aiProvider: 'openai' | 'anthropic' | 'local';
  features: {
    quickCapture: boolean;
    advancedSearch: boolean;
    autoTagging: boolean;
    visualWorkspace: boolean;
    mobileCapture: boolean;
  };
}

// ============================================================================
// Export all types for easy importing
// ============================================================================

// Note: Additional type exports can be added here as needed