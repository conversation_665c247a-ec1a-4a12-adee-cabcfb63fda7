import React from 'react';
import { render, screen, act, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { LightbulbProvider, useLightbulbContext } from '../context/LightbulbContext';
import { LightbulbState, LightbulbAction } from '../types';

// Test component to access context
const TestComponent: React.FC = () => {
  const { state, dispatch } = useLightbulbContext();
  
  return (
    <div>
      <div data-testid="active-tab">{state.ui.activeTab}</div>
      <div data-testid="is-loading">{state.ui.isLoading.toString()}</div>
      <div data-testid="knowledge-items-count">{state.content.knowledgeItems.length}</div>
      <div data-testid="categories-count">{state.content.categories.length}</div>
      <button 
        data-testid="set-tab-button" 
        onClick={() => dispatch({ type: 'SET_ACTIVE_TAB', payload: 'chat' })}
      >
        Set Chat Tab
      </button>
      <button 
        data-testid="toggle-loading" 
        onClick={() => dispatch({ type: 'SET_LOADING', payload: !state.ui.isLoading })}
      >
        Toggle Loading
      </button>
      <button 
        data-testid="add-knowledge-item" 
        onClick={() => dispatch({ 
          type: 'ADD_KNOWLEDGE_ITEM', 
          payload: {
            id: 'test-item',
            title: 'Test Item',
            content: 'Test content',
            type: 'note',
            tags: ['test'],
            timestamp: new Date().toISOString(),
            source: 'manual'
          }
        })}
      >
        Add Knowledge Item
      </button>
    </div>
  );
};

const renderWithProvider = (component: React.ReactElement) => {
  return render(
    <LightbulbProvider>
      {component}
    </LightbulbProvider>
  );
};

describe('LightbulbContext', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Provider', () => {
    it('should provide initial state', () => {
      renderWithProvider(<TestComponent />);
      
      expect(screen.getByTestId('active-tab')).toHaveTextContent('content');
      expect(screen.getByTestId('is-loading')).toHaveTextContent('false');
      expect(screen.getByTestId('knowledge-items-count')).toHaveTextContent('0');
      expect(screen.getByTestId('categories-count')).toHaveTextContent('0');
    });

    it('should throw error when used outside provider', () => {
      // Suppress console.error for this test
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      
      expect(() => {
        render(<TestComponent />);
      }).toThrow('useLightbulbContext must be used within a LightbulbProvider');
      
      consoleSpy.mockRestore();
    });
  });

  describe('State Management', () => {
    it('should handle SET_ACTIVE_TAB action', async () => {
      const user = userEvent.setup();
      renderWithProvider(<TestComponent />);
      
      expect(screen.getByTestId('active-tab')).toHaveTextContent('content');
      
      await user.click(screen.getByTestId('set-tab-button'));
      
      expect(screen.getByTestId('active-tab')).toHaveTextContent('chat');
    });

    it('should handle SET_LOADING action', async () => {
      const user = userEvent.setup();
      renderWithProvider(<TestComponent />);
      
      expect(screen.getByTestId('is-loading')).toHaveTextContent('false');
      
      await user.click(screen.getByTestId('toggle-loading'));
      
      expect(screen.getByTestId('is-loading')).toHaveTextContent('true');
    });

    it('should handle ADD_KNOWLEDGE_ITEM action', async () => {
      const user = userEvent.setup();
      renderWithProvider(<TestComponent />);
      
      expect(screen.getByTestId('knowledge-items-count')).toHaveTextContent('0');
      
      await user.click(screen.getByTestId('add-knowledge-item'));
      
      expect(screen.getByTestId('knowledge-items-count')).toHaveTextContent('1');
    });
  });

  describe('Reducer Logic', () => {
    const TestReducerComponent: React.FC = () => {
      const { state, dispatch } = useLightbulbContext();
      
      const testComplexActions = () => {
        // Test multiple actions in sequence
        dispatch({ type: 'SET_LOADING', payload: true });
        dispatch({ type: 'SET_ACTIVE_TAB', payload: 'search' });
        dispatch({ 
          type: 'ADD_CATEGORY', 
          payload: {
            id: 'test-category',
            name: 'Test Category',
            color: '#ff0000',
            created_at: Date.now()
          }
        });
        dispatch({ type: 'SET_LOADING', payload: false });
      };
      
      return (
        <div>
          <div data-testid="state-snapshot">
            {JSON.stringify({
              activeTab: state.ui.activeTab,
              isLoading: state.ui.isLoading,
              categoriesCount: state.content.categories.length
            })}
          </div>
          <button data-testid="test-complex-actions" onClick={testComplexActions}>
            Test Complex Actions
          </button>
        </div>
      );
    };

    it('should handle complex action sequences', async () => {
      const user = userEvent.setup();
      renderWithProvider(<TestReducerComponent />);
      
      const initialState = JSON.parse(screen.getByTestId('state-snapshot').textContent || '{}');
      expect(initialState).toEqual({
        activeTab: 'content',
        isLoading: false,
        categoriesCount: 0
      });
      
      await user.click(screen.getByTestId('test-complex-actions'));
      
      const finalState = JSON.parse(screen.getByTestId('state-snapshot').textContent || '{}');
      expect(finalState).toEqual({
        activeTab: 'search',
        isLoading: false,
        categoriesCount: 1
      });
    });
  });

  describe('Error Handling', () => {
    const ErrorTestComponent: React.FC = () => {
      const { dispatch } = useLightbulbContext();
      
      const triggerInvalidAction = () => {
        // @ts-ignore - Testing invalid action
        dispatch({ type: 'INVALID_ACTION', payload: 'test' });
      };
      
      return (
        <button data-testid="invalid-action" onClick={triggerInvalidAction}>
          Trigger Invalid Action
        </button>
      );
    };

    it('should handle invalid actions gracefully', async () => {
      const user = userEvent.setup();
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      
      renderWithProvider(<ErrorTestComponent />);
      
      await user.click(screen.getByTestId('invalid-action'));
      
      // Should not crash the application
      expect(screen.getByTestId('invalid-action')).toBeInTheDocument();
      
      consoleSpy.mockRestore();
    });
  });

  describe('Performance', () => {
    it('should not cause unnecessary re-renders', () => {
      let renderCount = 0;
      
      const PerformanceTestComponent: React.FC = () => {
        renderCount++;
        const { state } = useLightbulbContext();
        
        return (
          <div data-testid="render-count">{renderCount}</div>
        );
      };
      
      const { rerender } = renderWithProvider(<PerformanceTestComponent />);
      
      expect(screen.getByTestId('render-count')).toHaveTextContent('1');
      
      // Re-render with same props should not increase render count
      rerender(
        <LightbulbProvider>
          <PerformanceTestComponent />
        </LightbulbProvider>
      );
      
      expect(screen.getByTestId('render-count')).toHaveTextContent('2');
    });
  });
});