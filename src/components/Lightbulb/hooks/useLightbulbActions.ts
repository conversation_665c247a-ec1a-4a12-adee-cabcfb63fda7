import { useLightbulb } from '../context/LightbulbContext';
import { KnowledgeItem, ChatMessage, DocumentSource } from '../types';

/**
 * Custom hook that provides convenient action methods for the Lightbulb context
 * This hook abstracts common patterns and provides a cleaner API for components
 */
export const useLightbulbActions = () => {
  const { state, dispatch, actions } = useLightbulb();

  return {
    // State access
    ...state,
    
    // Core actions
    ...actions,
    
    // UI state actions
    setActiveSection: (section: string) => {
      dispatch({ type: 'SET_ACTIVE_SECTION', payload: section });
    },
    
    setSearchQuery: (query: string) => {
      dispatch({ type: 'SET_SEARCH_QUERY', payload: query });
    },
    
    setInputValue: (value: string) => {
      dispatch({ type: 'SET_INPUT_VALUE', payload: value });
    },
    
    setLoading: (loading: boolean) => {
      dispatch({ type: 'SET_LOADING', payload: loading });
    },
    
    // Modal toggles
    toggleQuickCapture: () => {
      dispatch({ type: 'TOGGLE_QUICK_CAPTURE' });
    },
    
    toggleAdvancedSearch: () => {
      dispatch({ type: 'TOGGLE_ADVANCED_SEARCH' });
    },
    
    toggleMobileSidebar: () => {
      dispatch({ type: 'TOGGLE_MOBILE_SIDEBAR' });
    },
    
    toggleMobileCapture: () => {
      dispatch({ type: 'TOGGLE_MOBILE_CAPTURE' });
    },
    
    // Content management
    addMessage: (message: ChatMessage) => {
      dispatch({ type: 'ADD_MESSAGE', payload: message });
    },
    
    addSavedItem: (item: KnowledgeItem) => {
      dispatch({ type: 'ADD_SAVED_ITEM', payload: item });
    },
    
    updateSources: (sources: DocumentSource[]) => {
      dispatch({ type: 'SET_SOURCES', payload: sources });
    },
    
    addTags: (tags: string[]) => {
      dispatch({ type: 'ADD_TAGS', payload: tags });
    },
    
    // Integration state
    setShowOnboarding: (show: boolean) => {
      dispatch({ type: 'SET_SHOW_ONBOARDING', payload: show });
    },
    
    setShowTutorial: (show: boolean) => {
      dispatch({ type: 'SET_SHOW_TUTORIAL', payload: show });
    },
    
    setShowHelp: (show: boolean) => {
      dispatch({ type: 'SET_SHOW_HELP', payload: show });
    },
    
    setShowFeedback: (show: boolean) => {
      dispatch({ type: 'SET_SHOW_FEEDBACK', payload: show });
    },
    
    setTutorialType: (type: 'basic' | 'content-processing' | 'visual-workspace' | 'ai-features') => {
      dispatch({ type: 'SET_TUTORIAL_TYPE', payload: type });
    },
    
    setHelpQuery: (query: string) => {
      dispatch({ type: 'SET_HELP_QUERY', payload: query });
    },
    
    setMobileKeyboardOpen: (open: boolean) => {
      dispatch({ type: 'SET_MOBILE_KEYBOARD_OPEN', payload: open });
    },
    
    // Utility actions
    resetState: () => {
      dispatch({ type: 'RESET_STATE' });
    },
    
    // Computed getters
    get filteredContent() {
      return actions.getFilteredContent();
    },
    
    get hasContent() {
      return state.contentItems.length > 0;
    },
    
    get hasMessages() {
      return state.messages.length > 0;
    },
    
    get isSearching() {
      return state.searchQuery.length > 0;
    },
    
    get availableTags() {
      return state.tags;
    },
    
    get totalItems() {
      return state.contentItems.length;
    }
  };
};

export default useLightbulbActions;