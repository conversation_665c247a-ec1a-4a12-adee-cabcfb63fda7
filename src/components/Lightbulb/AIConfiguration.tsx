import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { useToast } from '@/components/ui/use-toast';
import { iconMapping } from '@/lib/icon-mapping';
import { invokeTauriCommand } from './utils/tauriCommands';

interface AIConfig {
  provider: 'openai' | 'claude' | 'local' | 'mock';
  apiKey: string;
  model: string;
  maxTokens?: number;
  temperature: number;
  apiBaseUrl?: string;
}

interface AIConfigurationProps {
  onConfigUpdate?: (config: AIConfig) => void;
}

const AIConfiguration: React.FC<AIConfigurationProps> = ({ onConfigUpdate }) => {
  const [config, setConfig] = useState<AIConfig | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [testResult, setTestResult] = useState<string | null>(null);
  const [showApiKey, setShowApiKey] = useState(false);
  const { toast } = useToast();

  const Settings = iconMapping.Settings || 'div';
  const Key = iconMapping.Key || 'div';
  const Bot = iconMapping.Bot || 'div';
  const CheckCircle = iconMapping.CheckCircle || 'div';
  const AlertCircle = iconMapping.AlertCircle || 'div';
  const Eye = iconMapping.Eye || 'div';
  const EyeOff = iconMapping.EyeOff || 'div';
  const Zap = iconMapping.Zap || 'div';
  const Brain = iconMapping.Brain || 'div';

  // Model options for different providers
  const modelOptions = {
    openai: [
      { value: 'gpt-4-turbo-preview', label: 'GPT-4 Turbo' },
      { value: 'gpt-4', label: 'GPT-4' },
      { value: 'gpt-3.5-turbo', label: 'GPT-3.5 Turbo' },
      { value: 'gpt-3.5-turbo-16k', label: 'GPT-3.5 Turbo 16K' },
    ],
    claude: [
      { value: 'claude-3-opus-20240229', label: 'Claude 3 Opus' },
      { value: 'claude-3-sonnet-20240229', label: 'Claude 3 Sonnet' },
      { value: 'claude-3-haiku-20240307', label: 'Claude 3 Haiku' },
      { value: 'claude-2.1', label: 'Claude 2.1' },
    ],
    local: [
      { value: 'llama-2-7b', label: 'Llama 2 7B' },
      { value: 'llama-2-13b', label: 'Llama 2 13B' },
      { value: 'mistral-7b', label: 'Mistral 7B' },
      { value: 'custom', label: 'Custom Model' },
    ],
    mock: [
      { value: 'mock-model', label: 'Mock Model (Testing)' },
    ],
  };

  useEffect(() => {
    loadConfig();
  }, []);

  const loadConfig = async () => {
    try {
      setIsLoading(true);
      const existingConfig = await invokeTauriCommand('get_ai_config');
      if (existingConfig) {
        setConfig(existingConfig);
      } else {
        // Set default config
        setConfig({
          provider: 'mock',
          apiKey: '',
          model: 'mock-model',
          temperature: 0.7,
          maxTokens: 1000,
        });
      }
    } catch (error) {
      console.error('Failed to load AI config:', error);
      toast({
        title: 'Error',
        description: 'Failed to load AI configuration',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const saveConfig = async () => {
    if (!config) return;

    try {
      setIsLoading(true);
      await invokeTauriCommand('configure_ai_service', { config });
      
      toast({
        title: 'Success',
        description: 'AI configuration saved successfully',
      });
      
      if (onConfigUpdate) {
        onConfigUpdate(config);
      }
    } catch (error) {
      console.error('Failed to save config:', error);
      toast({
        title: 'Error',
        description: 'Failed to save AI configuration',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const testConnection = async () => {
    try {
      setIsTesting(true);
      setTestResult(null);
      
      const response = await invokeTauriCommand('test_ai_connection');
      setTestResult(response.content);
      
      toast({
        title: 'Success',
        description: 'AI connection test successful',
      });
    } catch (error) {
      console.error('Connection test failed:', error);
      setTestResult(`Connection failed: ${error}`);
      toast({
        title: 'Error',
        description: 'AI connection test failed',
        variant: 'destructive',
      });
    } finally {
      setIsTesting(false);
    }
  };

  const updateConfig = (updates: Partial<AIConfig>) => {
    if (config) {
      setConfig({ ...config, ...updates });
    }
  };

  if (isLoading && !config) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!config) return null;

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            <CardTitle>AI Service Configuration</CardTitle>
          </div>
          <Badge variant={config.provider === 'mock' ? 'secondary' : 'default'}>
            {config.provider.toUpperCase()}
          </Badge>
        </div>
        <CardDescription>
          Configure your AI provider for intelligent content processing
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <Tabs value={config.provider} onValueChange={(value) => updateConfig({ provider: value as AIConfig['provider'] })}>
          <TabsList className="grid grid-cols-4 w-full">
            <TabsTrigger value="openai">OpenAI</TabsTrigger>
            <TabsTrigger value="claude">Claude</TabsTrigger>
            <TabsTrigger value="local">Local</TabsTrigger>
            <TabsTrigger value="mock">Mock</TabsTrigger>
          </TabsList>

          <TabsContent value="openai" className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="openai-key">API Key</Label>
              <div className="flex gap-2">
                <Input
                  id="openai-key"
                  type={showApiKey ? 'text' : 'password'}
                  value={config.apiKey}
                  onChange={(e) => updateConfig({ apiKey: e.target.value })}
                  placeholder="sk-..."
                />
                <Button
                  type="button"
                  variant="outline"
                  size="icon"
                  onClick={() => setShowApiKey(!showApiKey)}
                >
                  {showApiKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="openai-model">Model</Label>
              <Select
                value={config.model}
                onValueChange={(value) => updateConfig({ model: value })}
              >
                <SelectTrigger id="openai-model">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {modelOptions.openai.map((model) => (
                    <SelectItem key={model.value} value={model.value}>
                      {model.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="openai-base">API Base URL (Optional)</Label>
              <Input
                id="openai-base"
                value={config.apiBaseUrl || ''}
                onChange={(e) => updateConfig({ apiBaseUrl: e.target.value })}
                placeholder="https://api.openai.com/v1"
              />
            </div>
          </TabsContent>

          <TabsContent value="claude" className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="claude-key">API Key</Label>
              <div className="flex gap-2">
                <Input
                  id="claude-key"
                  type={showApiKey ? 'text' : 'password'}
                  value={config.apiKey}
                  onChange={(e) => updateConfig({ apiKey: e.target.value })}
                  placeholder="sk-ant-..."
                />
                <Button
                  type="button"
                  variant="outline"
                  size="icon"
                  onClick={() => setShowApiKey(!showApiKey)}
                >
                  {showApiKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="claude-model">Model</Label>
              <Select
                value={config.model}
                onValueChange={(value) => updateConfig({ model: value })}
              >
                <SelectTrigger id="claude-model">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {modelOptions.claude.map((model) => (
                    <SelectItem key={model.value} value={model.value}>
                      {model.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="claude-base">API Base URL (Optional)</Label>
              <Input
                id="claude-base"
                value={config.apiBaseUrl || ''}
                onChange={(e) => updateConfig({ apiBaseUrl: e.target.value })}
                placeholder="https://api.anthropic.com/v1"
              />
            </div>
          </TabsContent>

          <TabsContent value="local" className="space-y-4">
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Local models require additional setup. Please ensure you have a compatible model server running.
              </AlertDescription>
            </Alert>

            <div className="space-y-2">
              <Label htmlFor="local-model">Model</Label>
              <Select
                value={config.model}
                onValueChange={(value) => updateConfig({ model: value })}
              >
                <SelectTrigger id="local-model">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {modelOptions.local.map((model) => (
                    <SelectItem key={model.value} value={model.value}>
                      {model.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="local-base">Server URL</Label>
              <Input
                id="local-base"
                value={config.apiBaseUrl || ''}
                onChange={(e) => updateConfig({ apiBaseUrl: e.target.value })}
                placeholder="http://localhost:8080"
              />
            </div>
          </TabsContent>

          <TabsContent value="mock" className="space-y-4">
            <Alert>
              <Zap className="h-4 w-4" />
              <AlertDescription>
                Mock mode is for testing and development. AI responses will be simulated.
              </AlertDescription>
            </Alert>
          </TabsContent>
        </Tabs>

        {/* Advanced Settings */}
        <div className="space-y-4 pt-4 border-t">
          <h3 className="text-sm font-medium">Advanced Settings</h3>
          
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="temperature">Temperature: {config.temperature.toFixed(2)}</Label>
              <span className="text-xs text-muted-foreground">
                {config.temperature < 0.3 ? 'Focused' : config.temperature < 0.7 ? 'Balanced' : 'Creative'}
              </span>
            </div>
            <Slider
              id="temperature"
              value={[config.temperature]}
              onValueChange={(value) => updateConfig({ temperature: value[0] })}
              min={0}
              max={1}
              step={0.1}
              className="w-full"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="max-tokens">Max Tokens</Label>
            <Input
              id="max-tokens"
              type="number"
              value={config.maxTokens || ''}
              onChange={(e) => updateConfig({ maxTokens: parseInt(e.target.value) || undefined })}
              placeholder="1000"
            />
          </div>
        </div>

        {/* Test Connection */}
        {testResult && (
          <Alert variant={testResult.includes('failed') ? 'destructive' : 'default'}>
            <AlertDescription>{testResult}</AlertDescription>
          </Alert>
        )}

        {/* Action Buttons */}
        <div className="flex gap-2 pt-4">
          <Button
            onClick={saveConfig}
            disabled={isLoading || !config.apiKey || config.provider === 'local'}
            className="flex-1"
          >
            <Settings className="h-4 w-4 mr-2" />
            Save Configuration
          </Button>
          <Button
            onClick={testConnection}
            variant="outline"
            disabled={isTesting || !config.apiKey || config.provider === 'local'}
          >
            {isTesting ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-2" />
            ) : (
              <CheckCircle className="h-4 w-4 mr-2" />
            )}
            Test Connection
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default AIConfiguration;