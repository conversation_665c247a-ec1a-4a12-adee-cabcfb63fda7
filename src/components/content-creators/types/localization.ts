// Localization and Translation Types

export interface LocalizationData {
  contentId: string;
  sourceLanguage: string;
  targetLanguages: string[];
  translations: Record<string, TranslationEntry[]>;
  progress: Record<string, number>; // language -> completion percentage
  lastUpdated: Date;
}

export interface TranslationEntry {
  id: string;
  key: string;
  sourceText: string;
  translatedText: string;
  context?: string;
  notes?: string;
  status: TranslationStatus;
  translatedBy?: string;
  reviewedBy?: string;
  translatedAt?: Date;
  reviewedAt?: Date;
}

export enum TranslationStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  TRANSLATED = 'translated',
  REVIEWED = 'reviewed',
  APPROVED = 'approved',
  REJECTED = 'rejected'
}

export interface LanguageConfig {
  code: string; // ISO 639-1 code (e.g., 'en', 'es', 'fr')
  name: string; // Display name (e.g., 'English', 'Español', 'Français')
  nativeName: string; // Native name (e.g., 'English', 'Español', 'Français')
  direction: 'ltr' | 'rtl';
  region?: string; // ISO 3166-1 alpha-2 code (e.g., 'US', 'ES', 'FR')
  isEnabled: boolean;
  isDefault: boolean;
}

export interface TranslationProject {
  id: string;
  name: string;
  description: string;
  sourceLanguage: string;
  targetLanguages: string[];
  contentIds: string[];
  translators: ProjectTranslator[];
  reviewers: ProjectReviewer[];
  deadline?: Date;
  status: ProjectStatus;
  progress: ProjectProgress;
  createdAt: Date;
  updatedAt: Date;
}

export interface ProjectTranslator {
  userId: string;
  name: string;
  email: string;
  languages: string[];
  specializations: string[];
  assignedEntries: string[];
}

export interface ProjectReviewer {
  userId: string;
  name: string;
  email: string;
  languages: string[];
  assignedEntries: string[];
}

export enum ProjectStatus {
  DRAFT = 'draft',
  ACTIVE = 'active',
  IN_REVIEW = 'in_review',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

export interface ProjectProgress {
  totalEntries: number;
  translatedEntries: number;
  reviewedEntries: number;
  approvedEntries: number;
  overallProgress: number; // percentage
  languageProgress: Record<string, LanguageProgress>;
}

export interface LanguageProgress {
  totalEntries: number;
  translatedEntries: number;
  reviewedEntries: number;
  approvedEntries: number;
  progress: number; // percentage
}

// Translation Memory and Glossary
export interface TranslationMemory {
  id: string;
  sourceText: string;
  targetText: string;
  sourceLanguage: string;
  targetLanguage: string;
  context?: string;
  domain?: string;
  quality: number; // 0-1 confidence score
  usageCount: number;
  createdAt: Date;
  lastUsed: Date;
}

export interface GlossaryTerm {
  id: string;
  term: string;
  definition: string;
  translations: Record<string, string>; // language -> translation
  context?: string;
  domain?: string;
  notes?: string;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

// Localization API Types
export interface CreateTranslationProjectRequest {
  name: string;
  description: string;
  sourceLanguage: string;
  targetLanguages: string[];
  contentIds: string[];
  deadline?: Date;
}

export interface AssignTranslatorRequest {
  projectId: string;
  userId: string;
  languages: string[];
  entryIds?: string[];
}

export interface SubmitTranslationRequest {
  entryId: string;
  translatedText: string;
  notes?: string;
}

export interface ReviewTranslationRequest {
  entryId: string;
  status: TranslationStatus;
  feedback?: string;
  suggestedChanges?: string;
}

export interface ExportTranslationsRequest {
  projectId: string;
  languages: string[];
  format: 'json' | 'csv' | 'xliff' | 'po';
  includeMetadata: boolean;
}

export interface ImportTranslationsRequest {
  projectId: string;
  file: File;
  format: 'json' | 'csv' | 'xliff' | 'po';
  overwriteExisting: boolean;
}