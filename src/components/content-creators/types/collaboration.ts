// Collaboration and Review Types

import { ReviewStatus } from '../shared/constants';
import { ContentAuthor } from './common';

export interface CollaborationSession {
  id: string;
  contentId: string;
  participants: Participant[];
  startedAt: Date;
  endedAt?: Date;
  isActive: boolean;
  changes: ContentChange[];
  chatMessages: ChatMessage[];
}

export interface Participant {
  userId: string;
  name: string;
  avatar?: string;
  role: 'owner' | 'editor' | 'reviewer' | 'viewer';
  joinedAt: Date;
  lastSeen: Date;
  isOnline: boolean;
  cursor?: CursorPosition;
  permissions: ParticipantPermissions;
}

export interface ParticipantPermissions {
  canEdit: boolean;
  canComment: boolean;
  canReview: boolean;
  canInvite: boolean;
  canManagePermissions: boolean;
}

export interface CursorPosition {
  x: number;
  y: number;
  elementId?: string;
  selection?: {
    start: number;
    end: number;
  };
}

export interface ContentChange {
  id: string;
  type: 'create' | 'update' | 'delete' | 'move';
  elementId: string;
  elementType: string;
  oldValue?: any;
  newValue?: any;
  userId: string;
  timestamp: Date;
  description: string;
}

export interface ChatMessage {
  id: string;
  sessionId: string;
  userId: string;
  userName: string;
  message: string;
  timestamp: Date;
  type: 'text' | 'system' | 'file' | 'mention';
  metadata?: {
    mentionedUsers?: string[];
    attachments?: string[];
    elementReference?: string;
  };
}

// Review and Feedback Types
export interface ContentReview {
  id: string;
  contentId: string;
  reviewerId: string;
  reviewer: ContentAuthor;
  status: ReviewStatus;
  overallRating?: number;
  comments: ReviewComment[];
  suggestions: string[];
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
  deadline?: Date;
}

export interface ReviewComment {
  id: string;
  reviewId: string;
  elementId?: string; // specific element being commented on
  elementType?: string;
  position?: { x: number; y: number };
  content: string;
  type: 'general' | 'suggestion' | 'issue' | 'praise';
  severity?: 'low' | 'medium' | 'high' | 'critical';
  status: 'open' | 'resolved' | 'dismissed';
  createdAt: Date;
  resolvedAt?: Date;
  resolvedBy?: string;
  replies: CommentReply[];
}

export interface CommentReply {
  id: string;
  commentId: string;
  userId: string;
  userName: string;
  content: string;
  createdAt: Date;
}

// AI Content Suggestions
export interface AIContentSuggestion {
  id: string;
  contentId: string;
  type: 'improvement' | 'accessibility' | 'engagement' | 'structure';
  title: string;
  description: string;
  suggestedChanges: any;
  confidence: number; // 0-1
  priority: 'low' | 'medium' | 'high';
  category: string;
  createdAt: Date;
  appliedAt?: Date;
  dismissedAt?: Date;
}

// Collaboration API Types
export interface CreateCollaborationRequest {
  contentId: string;
  inviteEmails: string[];
  message?: string;
  permissions: ParticipantPermissions;
}

export interface InviteCollaboratorRequest {
  sessionId: string;
  email: string;
  role: 'editor' | 'reviewer' | 'viewer';
  message?: string;
}

export interface UpdatePermissionsRequest {
  sessionId: string;
  userId: string;
  permissions: ParticipantPermissions;
}

export interface CreateReviewRequest {
  contentId: string;
  reviewerId: string;
  deadline?: Date;
  instructions?: string;
}

export interface SubmitReviewRequest {
  reviewId: string;
  status: ReviewStatus;
  overallRating?: number;
  comments: Omit<ReviewComment, 'id' | 'reviewId' | 'createdAt' | 'replies'>[];
  suggestions: string[];
}