// Analytics and Progress Types

import { AssessmentScore } from './assessment';

export interface ContentAnalytics {
  contentId: string;
  views: number;
  completions: number;
  averageRating: number;
  totalRatings: number;
  engagementMetrics: EngagementMetrics;
  feedbackSummary: FeedbackSummary;
  ratingSummary: RatingSummary;
  period: AnalyticsPeriod;
}

export interface EngagementMetrics {
  totalTimeSpent: number; // in seconds
  averageTimeSpent: number;
  dropOffPoints: DropOffPoint[];
  interactionCounts: Record<string, number>;
  completionRate: number;
  retentionRate: number;
  bounceRate: number;
}

export interface DropOffPoint {
  contentId: string;
  position: number; // percentage through content
  count: number;
  timestamp?: number;
}

export interface FeedbackSummary {
  totalFeedback: number;
  averageRating: number;
  sentimentScore: number;
  commonThemes: string[];
  improvementSuggestions: string[];
}

export interface RatingSummary {
  fiveStars: number;
  fourStars: number;
  threeStars: number;
  twoStars: number;
  oneStar: number;
  averageRating: number;
  totalRatings: number;
}

export interface AnalyticsPeriod {
  start: Date;
  end: Date;
  granularity: 'hour' | 'day' | 'week' | 'month';
}

// Learning Progress Types
export interface LearningProgress {
  userId: string;
  courseId: string;
  overallProgress: number; // percentage
  moduleProgress: ModuleProgress[];
  assessmentScores: AssessmentScore[];
  timeSpent: number; // in minutes
  lastAccessed: Date;
  completedAt?: Date;
  certificateEarned?: boolean;
  badges: Badge[];
}

export interface ModuleProgress {
  moduleId: string;
  progress: number; // percentage
  lessonsCompleted: string[];
  assessmentsCompleted: string[];
  timeSpent: number;
  startedAt: Date;
  completedAt?: Date;
}



// Gamification Types
export interface Badge {
  id: string;
  name: string;
  description: string;
  icon: string;
  criteria: BadgeCriteria;
  earnedAt?: Date;
  rarity: 'common' | 'uncommon' | 'rare' | 'epic' | 'legendary';
}

export interface BadgeCriteria {
  type: 'completion' | 'score' | 'time' | 'streak' | 'participation';
  threshold: number;
  conditions?: Record<string, any>;
}

export interface Certificate {
  id: string;
  courseId: string;
  userId: string;
  templateId: string;
  issuedAt: Date;
  expiresAt?: Date;
  verificationCode: string;
  metadata: CertificateMetadata;
}

export interface CertificateMetadata {
  recipientName: string;
  courseName: string;
  completionDate: Date;
  finalScore?: number;
  instructorName: string;
  organizationName: string;
  credentialUrl?: string;
}

// Analytics Dashboard Types
export interface DashboardMetrics {
  totalCourses: number;
  totalLearners: number;
  totalCompletions: number;
  averageRating: number;
  engagementTrends: TrendData[];
  popularContent: PopularContent[];
  recentActivity: ActivityItem[];
}

export interface TrendData {
  date: Date;
  value: number;
  metric: string;
}

export interface PopularContent {
  contentId: string;
  title: string;
  views: number;
  rating: number;
  completions: number;
}

export interface ActivityItem {
  id: string;
  type: 'completion' | 'enrollment' | 'rating' | 'comment';
  userId: string;
  userName: string;
  contentId: string;
  contentTitle: string;
  timestamp: Date;
  details?: Record<string, any>;
}