// Assessment and Question Types

import { AssessmentType, QuestionType } from '../shared/constants';
import { MediaAsset, InteractiveElement } from './common';

export interface Assessment {
  id: string;
  title: string;
  description: string;
  type: AssessmentType;
  questions: Question[];
  timeLimit?: number; // in minutes
  passingScore: number; // percentage
  maxAttempts?: number;
  randomizeQuestions: boolean;
  showResults: boolean;
  allowReview: boolean;
  instructions?: string;
  tags: string[];
  weight: number; // for grading
}

export interface Question {
  id: string;
  type: QuestionType;
  title: string;
  content: string;
  options?: QuestionOption[];
  correctAnswers: string[];
  explanation?: string;
  points: number;
  timeLimit?: number;
  media?: MediaAsset[];
  interactions?: InteractiveElement[];
  hints?: string[];
  tags: string[];
  difficulty: 'easy' | 'medium' | 'hard';
  metadata?: QuestionMetadata;
}

export interface QuestionOption {
  id: string;
  text: string;
  isCorrect: boolean;
  feedback?: string;
  media?: MediaAsset;
}

export interface QuestionMetadata {
  bloomsLevel?: 'remember' | 'understand' | 'apply' | 'analyze' | 'evaluate' | 'create';
  learningObjective?: string;
  keywords?: string[];
  estimatedTime?: number;
}

export interface AssessmentScore {
  assessmentId: string;
  userId: string;
  score: number;
  maxScore: number;
  percentage: number;
  passed: boolean;
  timeSpent: number;
  answers: AnswerRecord[];
  completedAt: Date;
  attempt: number;
}

export interface AnswerRecord {
  questionId: string;
  selectedAnswers: string[];
  isCorrect: boolean;
  points: number;
  timeSpent: number;
  feedback?: string;
}

// Assessment Builder Types
export interface AssessmentTemplate {
  id: string;
  name: string;
  description: string;
  type: AssessmentType;
  questionTypes: QuestionType[];
  defaultSettings: AssessmentSettings;
  sampleQuestions?: Question[];
}

export interface AssessmentSettings {
  timeLimit?: number;
  passingScore: number;
  maxAttempts?: number;
  randomizeQuestions: boolean;
  showResults: boolean;
  allowReview: boolean;
  showCorrectAnswers: boolean;
  showExplanations: boolean;
}

export interface QuestionBank {
  id: string;
  name: string;
  description: string;
  questions: Question[];
  tags: string[];
  category: string;
  isPublic: boolean;
  createdBy: string;
  lastModified: Date;
}