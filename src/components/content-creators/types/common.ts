// Common types shared across features

import { ResourceType, MediaType, InteractionType, UserRole } from '../shared/constants';

export interface ContentAuthor {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role: UserRole;
  department?: string;
  expertise?: string[];
}

export interface Resource {
  id: string;
  title: string;
  description?: string;
  type: ResourceType;
  url: string;
  size?: number;
  mimeType?: string;
  thumbnail?: string;
  downloadable: boolean;
  tags: string[];
}

export interface MediaAsset {
  id: string;
  title: string;
  type: MediaType;
  url: string;
  thumbnail?: string;
  duration?: number; // for video/audio
  dimensions?: { width: number; height: number };
  size: number;
  mimeType: string;
  alt?: string;
  caption?: string;
  credits?: string;
  uploadedAt: Date;
  uploadedBy: string;
  cdnUrl?: string;
  processingStatus?: 'pending' | 'processing' | 'completed' | 'failed';
}

export interface InteractiveElement {
  id: string;
  type: InteractionType;
  title: string;
  description?: string;
  config: any;
  position?: { x: number; y: number };
  size?: { width: number; height: number };
  trigger?: 'click' | 'hover' | 'scroll' | 'time';
  data?: any;
}

export interface Transcript {
  id: string;
  language: string;
  content: string;
  timestamps?: TranscriptTimestamp[];
  format: 'vtt' | 'srt' | 'txt';
}

export interface TranscriptTimestamp {
  start: number;
  end: number;
  text: string;
}

export interface ContentTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  thumbnail?: string;
  structure: any;
  styles?: any;
  defaultContent?: any;
  tags: string[];
  isPublic: boolean;
  createdBy: string;
  usageCount: number;
}

export interface ContentVersion {
  id: string;
  contentId: string;
  version: string;
  changes: string[];
  changedBy: string;
  changedAt: Date;
  snapshot: any;
  isPublished: boolean;
}