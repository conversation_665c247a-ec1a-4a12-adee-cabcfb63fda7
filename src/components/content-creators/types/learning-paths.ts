// Learning Paths and Milestones Types

import { DifficultyLevel } from '../shared/constants';
import { ContentAuthor, ContentTemplate } from './common';
import { Course } from './course';
import { Badge, Certificate } from './analytics';

export interface LearningPath {
  id: string;
  title: string;
  description: string;
  thumbnail?: string;
  difficulty: DifficultyLevel;
  estimatedDuration: number; // in hours
  milestones: Milestone[];
  prerequisites: string[]; // course IDs or skill requirements
  outcomes: string[];
  tags: string[];
  category: string;
  isPublic: boolean;
  enrollmentCount: number;
  completionRate: number;
  averageRating: number;
  author: ContentAuthor;
  collaborators: ContentAuthor[];
  createdAt: Date;
  updatedAt: Date;
  publishedAt?: Date;
  status: 'draft' | 'published' | 'archived';
}

export interface Milestone {
  id: string;
  pathId: string;
  title: string;
  description: string;
  order: number;
  type: MilestoneType;
  requirements: MilestoneRequirement[];
  rewards: MilestoneReward[];
  isOptional: boolean;
  estimatedDuration: number; // in hours
  resources: MilestoneResource[];
  assessments: string[]; // assessment IDs
  completionCriteria: CompletionCriteria;
}

export enum MilestoneType {
  COURSE_COMPLETION = 'course_completion',
  SKILL_ASSESSMENT = 'skill_assessment',
  PROJECT_SUBMISSION = 'project_submission',
  PEER_REVIEW = 'peer_review',
  CERTIFICATION = 'certification',
  PRACTICAL_EXERCISE = 'practical_exercise'
}

export interface MilestoneRequirement {
  id: string;
  type: 'course' | 'assessment' | 'skill' | 'prerequisite';
  targetId: string; // course ID, assessment ID, etc.
  minScore?: number; // for assessments
  completionRequired: boolean;
  description: string;
}

export interface MilestoneReward {
  id: string;
  type: 'badge' | 'certificate' | 'points' | 'unlock';
  value: any; // badge ID, certificate template, points amount, etc.
  description: string;
}

export interface MilestoneResource {
  id: string;
  title: string;
  type: 'course' | 'document' | 'video' | 'link' | 'template';
  url?: string;
  description?: string;
  isRequired: boolean;
}

export interface CompletionCriteria {
  type: 'all' | 'any' | 'percentage' | 'score';
  threshold?: number; // for percentage or score types
  requirements: string[]; // requirement IDs
}

// Learning Path Progress
export interface LearningPathProgress {
  userId: string;
  pathId: string;
  currentMilestone: number;
  completedMilestones: string[];
  overallProgress: number; // percentage
  milestoneProgress: Record<string, MilestoneProgress>;
  totalTimeSpent: number; // in minutes
  estimatedTimeRemaining: number; // in minutes
  startedAt: Date;
  lastAccessedAt: Date;
  completedAt?: Date;
  earnedBadges: Badge[];
  earnedCertificates: Certificate[];
}

export interface MilestoneProgress {
  milestoneId: string;
  status: 'not_started' | 'in_progress' | 'completed' | 'skipped';
  progress: number; // percentage
  completedRequirements: string[];
  timeSpent: number; // in minutes
  startedAt?: Date;
  completedAt?: Date;
  score?: number;
  feedback?: string;
}

// Learning Path Templates
export interface LearningPathTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  difficulty: DifficultyLevel;
  structure: PathStructure;
  milestoneTemplates: MilestoneTemplate[];
  defaultSettings: PathSettings;
  tags: string[];
  isPublic: boolean;
  usageCount: number;
  createdBy: string;
  createdAt: Date;
}

export interface PathStructure {
  type: 'linear' | 'branching' | 'adaptive' | 'flexible';
  allowSkipping: boolean;
  requireSequential: boolean;
  adaptiveCriteria?: AdaptiveCriteria;
}

export interface AdaptiveCriteria {
  assessmentBased: boolean;
  skillBased: boolean;
  performanceBased: boolean;
  timeBased: boolean;
  rules: AdaptiveRule[];
}

export interface AdaptiveRule {
  id: string;
  condition: string; // expression to evaluate
  action: 'skip' | 'recommend' | 'require' | 'unlock';
  targetMilestone: string;
  description: string;
}

export interface MilestoneTemplate {
  id: string;
  name: string;
  description: string;
  type: MilestoneType;
  defaultRequirements: Omit<MilestoneRequirement, 'id' | 'targetId'>[];
  defaultRewards: Omit<MilestoneReward, 'id' | 'value'>[];
  estimatedDuration: number;
  isOptional: boolean;
}

export interface PathSettings {
  allowSelfEnrollment: boolean;
  requireApproval: boolean;
  maxEnrollments?: number;
  enrollmentDeadline?: Date;
  completionDeadline?: Date;
  certificateTemplate?: string;
  passingScore: number;
  retakePolicy: RetakePolicy;
}

export interface RetakePolicy {
  allowed: boolean;
  maxAttempts?: number;
  cooldownPeriod?: number; // in days
  resetProgress: boolean;
}

// Learning Path API Types
export interface CreateLearningPathRequest {
  title: string;
  description: string;
  difficulty: DifficultyLevel;
  category: string;
  milestones: Omit<Milestone, 'id' | 'pathId'>[];
  settings: PathSettings;
}

export interface UpdateLearningPathRequest {
  pathId: string;
  updates: Partial<LearningPath>;
}

export interface EnrollInPathRequest {
  pathId: string;
  userId: string;
  startDate?: Date;
}

export interface UpdateProgressRequest {
  pathId: string;
  userId: string;
  milestoneId: string;
  progress: Partial<MilestoneProgress>;
}

export interface GeneratePathRequest {
  templateId: string;
  customization: {
    title: string;
    description: string;
    courseIds: string[];
    settings: Partial<PathSettings>;
  };
}