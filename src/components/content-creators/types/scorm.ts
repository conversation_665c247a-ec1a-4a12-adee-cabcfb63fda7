// SCORM Package and Export Types

export interface SCORMPackage {
  id: string;
  courseId: string;
  version: '1.2' | '2004';
  title: string;
  identifier: string;
  metadata: SCORMMetadata;
  manifest: string; // XML content
  resources: SCORMResource[];
  packageUrl: string;
  size: number;
  createdAt: Date;
  expiresAt?: Date;
}

export interface SCORMMetadata {
  title: string;
  description: string;
  keywords: string[];
  language: string;
  version: string;
  status: 'draft' | 'final' | 'revised' | 'unavailable';
  contribute: SCORMContribute[];
  format: string;
  type: string;
  interactivityType: 'active' | 'expositive' | 'mixed';
  learningResourceType: string[];
  interactivityLevel: 'very low' | 'low' | 'medium' | 'high' | 'very high';
  semanticDensity: 'very low' | 'low' | 'medium' | 'high' | 'very high';
  intendedEndUserRole: string[];
  context: string[];
  typicalAgeRange: string;
  difficulty: 'very easy' | 'easy' | 'medium' | 'difficult' | 'very difficult';
  typicalLearningTime: string; // ISO 8601 duration
  rights: SCORMRights;
}

export interface SCORMContribute {
  role: 'author' | 'publisher' | 'unknown' | 'initiator' | 'terminator' | 'validator' | 'editor' | 'graphical designer' | 'technical implementer' | 'content provider' | 'technical validator' | 'educational validator' | 'script writer' | 'instructional designer' | 'subject matter expert';
  entity: string;
  date: Date;
}

export interface SCORMRights {
  cost: 'yes' | 'no';
  copyrightAndOtherRestrictions: 'yes' | 'no';
  description: string;
}

export interface SCORMResource {
  identifier: string;
  type: 'webcontent' | 'sco' | 'asset';
  href?: string;
  metadata?: any;
  files: SCORMFile[];
  dependencies?: string[];
}

export interface SCORMFile {
  href: string;
  metadata?: any;
}

export interface SCORMOrganization {
  identifier: string;
  title: string;
  structure?: string;
  items: SCORMItem[];
  metadata?: any;
  objectives?: SCORMObjective[];
}

export interface SCORMItem {
  identifier: string;
  title: string;
  identifierref?: string;
  isvisible: boolean;
  parameters?: string;
  timelimitaction?: 'exit,message' | 'exit,no message' | 'continue,message' | 'continue,no message';
  datafromlms?: string;
  masteryscore?: number;
  maxtimeallowed?: string;
  prerequisites?: string;
  items?: SCORMItem[];
  metadata?: any;
  objectives?: SCORMObjective[];
}

export interface SCORMObjective {
  identifier: string;
  satisfiedByMeasure: boolean;
  minNormalizedMeasure?: number;
  primaryObjective?: boolean;
  mapInfo?: SCORMMapInfo[];
}

export interface SCORMMapInfo {
  targetObjectiveID: string;
  readSatisfiedStatus: boolean;
  readNormalizedMeasure: boolean;
  writeSatisfiedStatus: boolean;
  writeNormalizedMeasure: boolean;
}

// SCORM Generation Configuration
export interface SCORMGenerationConfig {
  version: '1.2' | '2004';
  includeSequencing: boolean;
  includeNavigation: boolean;
  completionThreshold: number; // percentage
  masteryScore?: number;
  timeLimit?: number; // minutes
  maxAttempts?: number;
  preventReview: boolean;
  preventForward: boolean;
  customCSS?: string;
  customJS?: string;
  branding?: SCORMBranding;
}

export interface SCORMBranding {
  logo?: string;
  colors?: {
    primary: string;
    secondary: string;
    background: string;
    text: string;
  };
  fonts?: {
    primary: string;
    secondary: string;
  };
}

// SCORM Export Options
export interface SCORMExportOptions {
  includeVideos: boolean;
  videoQuality: 'low' | 'medium' | 'high';
  includeInteractions: boolean;
  includeAssessments: boolean;
  includeResources: boolean;
  optimizeForMobile: boolean;
  offlineCapable: boolean;
  compressionLevel: 'none' | 'low' | 'medium' | 'high';
}

// SCORM API Types
export interface GenerateSCORMRequest {
  courseId: string;
  config: SCORMGenerationConfig;
  options: SCORMExportOptions;
}

export interface SCORMGenerationStatus {
  id: string;
  courseId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number; // percentage
  currentStep: string;
  estimatedTimeRemaining?: number; // seconds
  error?: string;
  packageUrl?: string;
  createdAt: Date;
  completedAt?: Date;
}

export interface SCORMValidationResult {
  isValid: boolean;
  errors: SCORMValidationError[];
  warnings: SCORMValidationWarning[];
  metadata: SCORMMetadata;
}

export interface SCORMValidationError {
  code: string;
  message: string;
  severity: 'error' | 'warning';
  location?: string;
  suggestion?: string;
}

export interface SCORMValidationWarning {
  code: string;
  message: string;
  location?: string;
  suggestion?: string;
}