// Course and Module Types

import { ContentStatus, ContentType, DifficultyLevel } from '../shared/constants';
import { ContentAuthor, MediaAsset, Resource, InteractiveElement, Transcript } from './common';
import { Assessment } from './assessment';

export interface Course {
  id: string;
  title: string;
  description: string;
  thumbnail?: string;
  duration: number; // in minutes
  modules: CourseModule[];
  metadata: CourseMetadata;
  status: ContentStatus;
  version: string;
  createdAt: Date;
  updatedAt: Date;
  publishedAt?: Date;
  author: ContentAuthor;
  collaborators: ContentAuthor[];
  tags: string[];
  category: string;
  difficulty: DifficultyLevel;
  language: string;
  scormPackageUrl?: string;
}

export interface CourseModule {
  id: string;
  courseId: string;
  title: string;
  description: string;
  order: number;
  lessons: Lesson[];
  assessments: Assessment[];
  duration: number;
  isRequired: boolean;
  prerequisites?: string[];
}

export interface Lesson {
  id: string;
  moduleId: string;
  title: string;
  description: string;
  order: number;
  contentType: ContentType;
  content: LessonContent;
  duration: number;
  resources: Resource[];
  interactions: InteractiveElement[];
  transcripts?: Transcript[];
}

export interface LessonContent {
  type: ContentType;
  data: any;
  url?: string;
  embedCode?: string;
  markdown?: string;
  slides?: Slide[];
}

export interface Slide {
  id: string;
  title: string;
  content: string;
  media?: MediaAsset[];
  notes?: string;
  transitions?: string;
  animations?: Animation[];
}

export interface Animation {
  id: string;
  type: 'fade' | 'slide' | 'zoom' | 'rotate';
  trigger: 'onEnter' | 'onClick' | 'onScroll';
  duration: number;
  delay: number;
  properties: Record<string, any>;
}

export interface CourseMetadata {
  objectives: string[];
  prerequisites: string[];
  targetAudience: string[];
  outcomes: string[];
  keywords: string[];
  estimatedHours: number;
  certificateAvailable: boolean;
  price?: number;
  currency?: string;
}

// Course API Request/Response Types
export interface CreateCourseRequest {
  title: string;
  description: string;
  category: string;
  difficulty: string;
  language: string;
  metadata: CourseMetadata;
}

export interface UpdateCourseRequest {
  id: string;
  updates: Partial<Course>;
}

export interface PublishCourseRequest {
  courseId: string;
  publishOptions: {
    sendNotifications: boolean;
    scheduleDate?: Date;
    targetAudience?: string[];
  };
}

export interface ContentSearchRequest {
  query?: string;
  filters: {
    status?: ContentStatus[];
    type?: ContentType[];
    author?: string[];
    category?: string[];
    tags?: string[];
    dateRange?: {
      start: Date;
      end: Date;
    };
  };
  sort?: {
    field: string;
    order: 'asc' | 'desc';
  };
  pagination: {
    page: number;
    limit: number;
  };
}

export interface ContentSearchResponse {
  results: Course[];
  total: number;
  page: number;
  pages: number;
}