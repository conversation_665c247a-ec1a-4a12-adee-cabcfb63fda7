import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  GitBranch, Plus, ArrowRight, Award, Lock, CheckCircle, 
  Edit, Trash2, Copy, Move, Play, Pause, RotateCcw,
  Target, Clock, Users, BookOpen, Trophy, Star,
  ChevronUp, ChevronDown, Zap, Shield, Gem, Flag
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue 
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from '@/components/ui/dialog';
import {
  DndContext,
  DragEndEvent,
  useSensor,
  useSensors,
  PointerSensor,
  closestCenter
} from '@dnd-kit/core';
import {
  SortableContext,
  verticalListSortingStrategy,
  useSortable
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { useContentCreator } from '../../hooks/useContentCreator';
import { useToast } from '@/components/ui/use-toast';
import * as types from '../../types';

interface PathNode {
  id: string;
  courseId: string;
  title: string;
  description: string;
  order: number;
  duration: number;
  prerequisites: string[];
  status: 'locked' | 'available' | 'in-progress' | 'completed';
  progress: number;
  type: 'course' | 'module' | 'assessment' | 'milestone';
  optional: boolean;
  children?: PathNode[];
}

interface LearningPathData extends types.LearningPath {
  nodes: PathNode[];
  rules: PathRule[];
  rewards: PathReward[];
  analytics: PathAnalytics;
}

interface PathRule {
  id: string;
  type: 'prerequisite' | 'time-lock' | 'score-requirement' | 'completion';
  sourceNodeId: string;
  targetNodeId: string;
  condition: any;
}

interface PathReward {
  id: string;
  type: 'badge' | 'certificate' | 'points' | 'unlock';
  nodeId: string;
  name: string;
  description: string;
  icon: string;
  value?: number;
}

interface PathAnalytics {
  totalLearners: number;
  avgCompletion: number;
  avgTimeToComplete: number;
  dropoffPoints: { nodeId: string; rate: number }[];
  popularPaths: string[][];
}

// Sortable Path Node Component
const SortablePathNode: React.FC<{
  node: PathNode;
  onEdit: (node: PathNode) => void;
  onDelete: (id: string) => void;
}> = ({ node, onEdit, onDelete }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging
  } = useSortable({ id: node.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  const getNodeIcon = () => {
    switch (node.type) {
      case 'course': return <BookOpen className="h-4 w-4" />;
      case 'module': return <GitBranch className="h-4 w-4" />;
      case 'assessment': return <Award className="h-4 w-4" />;
      case 'milestone': return <Flag className="h-4 w-4" />;
      default: return null;
    }
  };

  const getStatusIcon = () => {
    switch (node.status) {
      case 'completed': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'in-progress': return <div className="h-4 w-4 rounded-full border-2 border-blue-500 animate-pulse" />;
      case 'available': return <Play className="h-4 w-4 text-blue-500" />;
      case 'locked': return <Lock className="h-4 w-4 text-gray-400" />;
      default: return null;
    }
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className="border rounded-lg p-4 bg-background hover:shadow-md transition-shadow"
    >
      <div className="flex items-start justify-between">
        <div className="flex items-start gap-3 flex-1">
          <div
            {...attributes}
            {...listeners}
            className="mt-1 cursor-move"
          >
            <Move className="h-4 w-4 text-muted-foreground" />
          </div>
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-1">
              {getNodeIcon()}
              <h4 className="font-semibold">{node.title}</h4>
              {getStatusIcon()}
              {node.optional && (
                <Badge variant="outline" className="text-xs">Optional</Badge>
              )}
            </div>
            <p className="text-sm text-muted-foreground mb-2">{node.description}</p>
            <div className="flex items-center gap-4 text-xs text-muted-foreground">
              <span className="flex items-center gap-1">
                <Clock className="h-3 w-3" />
                {node.duration} hours
              </span>
              {node.progress > 0 && (
                <span className="flex items-center gap-1">
                  <Target className="h-3 w-3" />
                  {node.progress}% complete
                </span>
              )}
            </div>
            {node.progress > 0 && (
              <Progress value={node.progress} className="h-1 mt-2" />
            )}
          </div>
        </div>
        <div className="flex gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onEdit(node)}
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onDelete(node.id)}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export const LearningPathDesigner: React.FC = () => {
  const { 
    getLearningPaths,
    createLearningPath,
    getCourses,
    loading,
    error 
  } = useContentCreator();
  const { toast } = useToast();

  // State
  const [paths, setPaths] = useState<LearningPathData[]>([]);
  const [selectedPath, setSelectedPath] = useState<LearningPathData | null>(null);
  const [nodes, setNodes] = useState<PathNode[]>([]);
  const [showPathDialog, setShowPathDialog] = useState(false);
  const [showNodeDialog, setShowNodeDialog] = useState(false);
  const [currentNode, setCurrentNode] = useState<PathNode | null>(null);
  const [currentPath, setCurrentPath] = useState<types.LearningPath | null>(null);
  const [availableCourses, setAvailableCourses] = useState<types.Course[]>([]);
  const [rules, setRules] = useState<PathRule[]>([]);
  const [rewards, setRewards] = useState<PathReward[]>([]);
  const [activeTab, setActiveTab] = useState('structure');
  const [showRuleDialog, setShowRuleDialog] = useState(false);
  const [currentRule, setCurrentRule] = useState<PathRule | null>(null);
  const [showRewardDialog, setShowRewardDialog] = useState(false);
  const [currentReward, setCurrentReward] = useState<PathReward | null>(null);
  const [pathAnalytics, setPathAnalytics] = useState<PathAnalytics | null>(null);

  // DnD sensors
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  // Load learning paths on mount
  useEffect(() => {
    loadLearningPaths();
    loadAvailableCourses();
  }, []);

  const loadLearningPaths = async () => {
    try {
      const pathsData = await getLearningPaths();
      // Transform to our extended format
      const transformedPaths = pathsData.map(path => ({
        ...path,
        nodes: [],
        rules: [],
        rewards: [],
        analytics: {
          totalLearners: Math.floor(Math.random() * 1000),
          avgCompletion: Math.floor(Math.random() * 100),
          avgTimeToComplete: Math.floor(Math.random() * 100),
          dropoffPoints: [],
          popularPaths: []
        }
      }));
      setPaths(transformedPaths);
    } catch (err) {
    }
  };

  const loadAvailableCourses = async () => {
    try {
      const response = await getCourses();
      setAvailableCourses(response.results);
    } catch (err) {
    }
  };

  // Handle drag end for reordering nodes
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      setNodes((items) => {
        const oldIndex = items.findIndex(item => item.id === active.id);
        const newIndex = items.findIndex(item => item.id === over.id);
        
        const newItems = [...items];
        const [movedItem] = newItems.splice(oldIndex, 1);
        newItems.splice(newIndex, 0, movedItem);
        
        // Update order property
        return newItems.map((item, index) => ({ ...item, order: index + 1 }));
      });
    }
  };

  // Create new learning path
  const handleCreatePath = () => {
    setCurrentPath({
      id: '',
      title: '',
      description: '',
      courses: [],
      milestones: [],
      duration: 0,
      difficulty: 'Beginner',
    });
    setShowPathDialog(true);
  };

  // Save learning path
  const handleSavePath = async () => {
    if (!currentPath) return;

    try {
      const newPath = await createLearningPath(currentPath);
      
      // Add to local state with extended properties
      const extendedPath: LearningPathData = {
        ...newPath,
        nodes: nodes,
        rules: rules,
        rewards: rewards,
        analytics: {
          totalLearners: 0,
          avgCompletion: 0,
          avgTimeToComplete: 0,
          dropoffPoints: [],
          popularPaths: []
        }
      };
      
      setPaths(prev => [...prev, extendedPath]);
      setSelectedPath(extendedPath);
      
      toast({
        title: "Path Created",
        description: "Learning path has been created successfully",
      });
      
      setShowPathDialog(false);
      setCurrentPath(null);
    } catch (err) {
      toast({
        title: "Creation Failed",
        description: "Could not create learning path",
        variant: "destructive"
      });
    }
  };

  // Add node to path
  const handleAddNode = () => {
    setCurrentNode({
      id: '',
      courseId: '',
      title: '',
      description: '',
      order: nodes.length + 1,
      duration: 0,
      prerequisites: [],
      status: 'locked',
      progress: 0,
      type: 'course',
      optional: false
    });
    setShowNodeDialog(true);
  };

  // Save node
  const handleSaveNode = () => {
    if (!currentNode) return;

    if (currentNode.id) {
      // Update existing
      setNodes(prev => prev.map(node => 
        node.id === currentNode.id ? currentNode : node
      ));
    } else {
      // Add new
      const newNode = { 
        ...currentNode, 
        id: `node-${Date.now()}`,
        order: nodes.length + 1
      };
      setNodes(prev => [...prev, newNode]);
    }

    toast({
      title: "Node Saved",
      description: "Path node has been saved successfully",
    });

    setShowNodeDialog(false);
    setCurrentNode(null);
  };

  // Delete node
  const handleDeleteNode = (nodeId: string) => {
    setNodes(prev => prev.filter(node => node.id !== nodeId));
    // Also remove related rules and rewards
    setRules(prev => prev.filter(rule => 
      rule.sourceNodeId !== nodeId && rule.targetNodeId !== nodeId
    ));
    setRewards(prev => prev.filter(reward => reward.nodeId !== nodeId));
    
    toast({
      title: "Node Deleted",
      description: "Path node has been removed",
    });
  };

  // Add rule
  const handleAddRule = () => {
    if (nodes.length < 2) {
      toast({
        title: "Not Enough Nodes",
        description: "You need at least 2 nodes to create rules",
        variant: "destructive"
      });
      return;
    }

    setCurrentRule({
      id: '',
      type: 'prerequisite',
      sourceNodeId: nodes[0].id,
      targetNodeId: nodes[1].id,
      condition: {}
    });
    setShowRuleDialog(true);
  };

  // Save rule
  const handleSaveRule = () => {
    if (!currentRule) return;

    if (currentRule.id) {
      // Update existing
      setRules(prev => prev.map(rule => 
        rule.id === currentRule.id ? currentRule : rule
      ));
    } else {
      // Add new
      const newRule = { 
        ...currentRule, 
        id: `rule-${Date.now()}`
      };
      setRules(prev => [...prev, newRule]);
    }

    toast({
      title: "Rule Saved",
      description: "Path rule has been saved successfully",
    });

    setShowRuleDialog(false);
    setCurrentRule(null);
  };

  // Add reward
  const handleAddReward = () => {
    if (nodes.length === 0) {
      toast({
        title: "No Nodes Available",
        description: "You need at least one node to add rewards",
        variant: "destructive"
      });
      return;
    }

    setCurrentReward({
      id: '',
      type: 'badge',
      nodeId: nodes[0].id,
      name: '',
      description: '',
      icon: 'star',
      value: 0
    });
    setShowRewardDialog(true);
  };

  // Save reward
  const handleSaveReward = () => {
    if (!currentReward) return;

    if (currentReward.id) {
      // Update existing
      setRewards(prev => prev.map(reward => 
        reward.id === currentReward.id ? currentReward : reward
      ));
    } else {
      // Add new
      const newReward = { 
        ...currentReward, 
        id: `reward-${Date.now()}`
      };
      setRewards(prev => [...prev, newReward]);
    }

    toast({
      title: "Reward Saved",
      description: "Path reward has been saved successfully",
    });

    setShowRewardDialog(false);
    setCurrentReward(null);
  };

  // Generate analytics (mock)
  const generateAnalytics = () => {
    setPathAnalytics({
      totalLearners: Math.floor(Math.random() * 5000) + 500,
      avgCompletion: Math.floor(Math.random() * 40) + 60,
      avgTimeToComplete: Math.floor(Math.random() * 50) + 20,
      dropoffPoints: nodes.slice(0, 3).map(node => ({
        nodeId: node.id,
        rate: Math.floor(Math.random() * 30) + 10
      })),
      popularPaths: [
        nodes.slice(0, Math.ceil(nodes.length / 2)).map(n => n.id),
        nodes.filter((_, i) => i % 2 === 0).map(n => n.id)
      ]
    });

    toast({
      title: "Analytics Generated",
      description: "Path analytics have been calculated",
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Learning Path Designer</h2>
          <p className="text-muted-foreground mt-1">
            Create and manage structured learning journeys
          </p>
        </div>
        <Button onClick={handleCreatePath}>
          <Plus className="h-4 w-4 mr-2" />
          Create New Path
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Path List */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Learning Paths</CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <ScrollArea className="h-[600px]">
                <div className="p-4 space-y-2">
                  {paths.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground text-sm">
                      No paths created yet
                    </div>
                  ) : (
                    paths.map((path) => (
                      <Button
                        key={path.id}
                        variant={selectedPath?.id === path.id ? "default" : "outline"}
                        className="w-full justify-start"
                        onClick={() => {
                          setSelectedPath(path);
                          setNodes(path.nodes || []);
                          setRules(path.rules || []);
                          setRewards(path.rewards || []);
                        }}
                      >
                        <GitBranch className="h-4 w-4 mr-2" />
                        <span className="truncate">{path.title}</span>
                      </Button>
                    ))
                  )}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </div>

        {/* Path Designer */}
        <div className="lg:col-span-3">
          {selectedPath ? (
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle>{selectedPath.title}</CardTitle>
                    <CardDescription>{selectedPath.description}</CardDescription>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={generateAnalytics}
                    >
                      <Zap className="h-4 w-4 mr-2" />
                      Analytics
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                    >
                      <Trophy className="h-4 w-4 mr-2" />
                      Preview
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <Tabs value={activeTab} onValueChange={setActiveTab}>
                  <TabsList className="grid w-full grid-cols-4">
                    <TabsTrigger value="structure">Structure</TabsTrigger>
                    <TabsTrigger value="rules">Rules</TabsTrigger>
                    <TabsTrigger value="rewards">Rewards</TabsTrigger>
                    <TabsTrigger value="analytics">Analytics</TabsTrigger>
                  </TabsList>

                  {/* Structure Tab */}
                  <TabsContent value="structure" className="space-y-4">
                    <div className="flex justify-between items-center">
                      <h3 className="font-semibold">Path Nodes</h3>
                      <Button size="sm" onClick={handleAddNode}>
                        <Plus className="h-4 w-4 mr-2" />
                        Add Node
                      </Button>
                    </div>

                    {nodes.length === 0 ? (
                      <div className="text-center py-12 border-2 border-dashed rounded-lg">
                        <GitBranch className="h-12 w-12 mx-auto text-muted-foreground mb-3" />
                        <p className="text-muted-foreground">
                          No nodes in this path yet
                        </p>
                        <Button 
                          variant="outline" 
                          size="sm" 
                          className="mt-3"
                          onClick={handleAddNode}
                        >
                          Add First Node
                        </Button>
                      </div>
                    ) : (
                      <DndContext
                        sensors={sensors}
                        collisionDetection={closestCenter}
                        onDragEnd={handleDragEnd}
                      >
                        <SortableContext
                          items={nodes.map(n => n.id)}
                          strategy={verticalListSortingStrategy}
                        >
                          <div className="space-y-3">
                            {nodes.map((node) => (
                              <SortablePathNode
                                key={node.id}
                                node={node}
                                onEdit={(node) => {
                                  setCurrentNode(node);
                                  setShowNodeDialog(true);
                                }}
                                onDelete={handleDeleteNode}
                              />
                            ))}
                          </div>
                        </SortableContext>
                      </DndContext>
                    )}
                  </TabsContent>

                  {/* Rules Tab */}
                  <TabsContent value="rules" className="space-y-4">
                    <div className="flex justify-between items-center">
                      <h3 className="font-semibold">Path Rules</h3>
                      <Button size="sm" onClick={handleAddRule}>
                        <Plus className="h-4 w-4 mr-2" />
                        Add Rule
                      </Button>
                    </div>

                    {rules.length === 0 ? (
                      <div className="text-center py-8 text-muted-foreground">
                        No rules defined yet
                      </div>
                    ) : (
                      <div className="space-y-3">
                        {rules.map((rule) => {
                          const sourceNode = nodes.find(n => n.id === rule.sourceNodeId);
                          const targetNode = nodes.find(n => n.id === rule.targetNodeId);
                          
                          return (
                            <div key={rule.id} className="border rounded-lg p-4">
                              <div className="flex justify-between items-start">
                                <div>
                                  <Badge variant="outline">{rule.type}</Badge>
                                  <p className="text-sm mt-2">
                                    <strong>{sourceNode?.title}</strong>
                                    {' → '}
                                    <strong>{targetNode?.title}</strong>
                                  </p>
                                </div>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => {
                                    setCurrentRule(rule);
                                    setShowRuleDialog(true);
                                  }}
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    )}
                  </TabsContent>

                  {/* Rewards Tab */}
                  <TabsContent value="rewards" className="space-y-4">
                    <div className="flex justify-between items-center">
                      <h3 className="font-semibold">Achievements & Rewards</h3>
                      <Button size="sm" onClick={handleAddReward}>
                        <Plus className="h-4 w-4 mr-2" />
                        Add Reward
                      </Button>
                    </div>

                    {rewards.length === 0 ? (
                      <div className="text-center py-8 text-muted-foreground">
                        No rewards configured yet
                      </div>
                    ) : (
                      <div className="grid grid-cols-2 gap-4">
                        {rewards.map((reward) => {
                          const node = nodes.find(n => n.id === reward.nodeId);
                          const RewardIcon = reward.type === 'badge' ? Shield :
                                            reward.type === 'certificate' ? Award :
                                            reward.type === 'points' ? Star : Gem;
                          
                          return (
                            <Card key={reward.id}>
                              <CardContent className="pt-4">
                                <div className="flex items-start justify-between">
                                  <div className="flex items-start gap-3">
                                    <RewardIcon className="h-8 w-8 text-yellow-500" />
                                    <div>
                                      <h4 className="font-semibold">{reward.name}</h4>
                                      <p className="text-sm text-muted-foreground">
                                        {reward.description}
                                      </p>
                                      <p className="text-xs text-muted-foreground mt-1">
                                        Unlocked at: {node?.title}
                                      </p>
                                      {reward.value && (
                                        <Badge className="mt-2">{reward.value} points</Badge>
                                      )}
                                    </div>
                                  </div>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => {
                                      setCurrentReward(reward);
                                      setShowRewardDialog(true);
                                    }}
                                  >
                                    <Edit className="h-4 w-4" />
                                  </Button>
                                </div>
                              </CardContent>
                            </Card>
                          );
                        })}
                      </div>
                    )}
                  </TabsContent>

                  {/* Analytics Tab */}
                  <TabsContent value="analytics" className="space-y-4">
                    {!pathAnalytics ? (
                      <div className="text-center py-12">
                        <Zap className="h-12 w-12 mx-auto text-muted-foreground mb-3" />
                        <p className="text-muted-foreground mb-3">
                          No analytics data available
                        </p>
                        <Button onClick={generateAnalytics}>
                          Generate Analytics
                        </Button>
                      </div>
                    ) : (
                      <div className="space-y-6">
                        <div className="grid grid-cols-4 gap-4">
                          <Card>
                            <CardContent className="pt-6">
                              <div className="text-2xl font-bold">
                                {pathAnalytics.totalLearners.toLocaleString()}
                              </div>
                              <p className="text-sm text-muted-foreground">
                                Total Learners
                              </p>
                            </CardContent>
                          </Card>
                          <Card>
                            <CardContent className="pt-6">
                              <div className="text-2xl font-bold">
                                {pathAnalytics.avgCompletion}%
                              </div>
                              <p className="text-sm text-muted-foreground">
                                Avg Completion
                              </p>
                            </CardContent>
                          </Card>
                          <Card>
                            <CardContent className="pt-6">
                              <div className="text-2xl font-bold">
                                {pathAnalytics.avgTimeToComplete}h
                              </div>
                              <p className="text-sm text-muted-foreground">
                                Avg Time
                              </p>
                            </CardContent>
                          </Card>
                          <Card>
                            <CardContent className="pt-6">
                              <div className="text-2xl font-bold">
                                {nodes.length}
                              </div>
                              <p className="text-sm text-muted-foreground">
                                Total Nodes
                              </p>
                            </CardContent>
                          </Card>
                        </div>

                        {pathAnalytics.dropoffPoints.length > 0 && (
                          <div>
                            <h4 className="font-semibold mb-3">Drop-off Points</h4>
                            <div className="space-y-2">
                              {pathAnalytics.dropoffPoints.map((point) => {
                                const node = nodes.find(n => n.id === point.nodeId);
                                return (
                                  <div key={point.nodeId} className="flex items-center justify-between p-3 border rounded-lg">
                                    <span className="text-sm">{node?.title}</span>
                                    <Badge variant="destructive">{point.rate}% drop-off</Badge>
                                  </div>
                                );
                              })}
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="py-12">
                <div className="text-center">
                  <GitBranch className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <h3 className="font-semibold mb-2">No Path Selected</h3>
                  <p className="text-muted-foreground mb-4">
                    Select a path from the list or create a new one
                  </p>
                  <Button onClick={handleCreatePath}>
                    <Plus className="h-4 w-4 mr-2" />
                    Create New Path
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Path Dialog */}
      <Dialog open={showPathDialog} onOpenChange={setShowPathDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create Learning Path</DialogTitle>
            <DialogDescription>
              Define the basic information for your learning path
            </DialogDescription>
          </DialogHeader>
          {currentPath && (
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">Path Title</label>
                <Input
                  value={currentPath.title}
                  onChange={(e) => setCurrentPath({...currentPath, title: e.target.value})}
                  placeholder="Enter path title"
                />
              </div>
              <div>
                <label className="text-sm font-medium">Description</label>
                <Textarea
                  value={currentPath.description}
                  onChange={(e) => setCurrentPath({...currentPath, description: e.target.value})}
                  placeholder="Describe the learning path"
                  rows={3}
                />
              </div>
              <div>
                <label className="text-sm font-medium">Difficulty Level</label>
                <Select 
                  value={currentPath.difficulty}
                  onValueChange={(value) => setCurrentPath({...currentPath, difficulty: value})}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="beginner">Beginner</SelectItem>
                    <SelectItem value="intermediate">Intermediate</SelectItem>
                    <SelectItem value="advanced">Advanced</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowPathDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleSavePath}>
              Create Path
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Node Dialog */}
      <Dialog open={showNodeDialog} onOpenChange={setShowNodeDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {currentNode?.id ? 'Edit' : 'Add'} Path Node
            </DialogTitle>
            <DialogDescription>
              Configure the node for your learning path
            </DialogDescription>
          </DialogHeader>
          {currentNode && (
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">Node Type</label>
                <Select 
                  value={currentNode.type}
                  onValueChange={(value: any) => setCurrentNode({...currentNode, type: value})}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="course">Course</SelectItem>
                    <SelectItem value="module">Module</SelectItem>
                    <SelectItem value="assessment">Assessment</SelectItem>
                    <SelectItem value="milestone">Milestone</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {currentNode.type === 'course' && availableCourses.length > 0 && (
                <div>
                  <label className="text-sm font-medium">Select Course</label>
                  <Select 
                    value={currentNode.courseId}
                    onValueChange={(value) => {
                      const course = availableCourses.find(c => c.id === value);
                      if (course) {
                        setCurrentNode({
                          ...currentNode,
                          courseId: value,
                          title: course.title,
                          description: course.description,
                          duration: course.duration
                        });
                      }
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Choose a course" />
                    </SelectTrigger>
                    <SelectContent>
                      {availableCourses.map((course) => (
                        <SelectItem key={course.id} value={course.id}>
                          {course.title}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

              <div>
                <label className="text-sm font-medium">Title</label>
                <Input
                  value={currentNode.title}
                  onChange={(e) => setCurrentNode({...currentNode, title: e.target.value})}
                  placeholder="Enter node title"
                />
              </div>

              <div>
                <label className="text-sm font-medium">Description</label>
                <Textarea
                  value={currentNode.description}
                  onChange={(e) => setCurrentNode({...currentNode, description: e.target.value})}
                  placeholder="Describe this node"
                  rows={2}
                />
              </div>

              <div>
                <label className="text-sm font-medium">Duration (hours)</label>
                <Input
                  type="number"
                  value={currentNode.duration}
                  onChange={(e) => setCurrentNode({...currentNode, duration: parseInt(e.target.value) || 0})}
                />
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={currentNode.optional}
                  onChange={(e) => setCurrentNode({...currentNode, optional: e.target.checked})}
                />
                <label className="text-sm">This node is optional</label>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowNodeDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleSaveNode}>
              Save Node
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Rule Dialog */}
      <Dialog open={showRuleDialog} onOpenChange={setShowRuleDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Configure Path Rule</DialogTitle>
            <DialogDescription>
              Define rules and prerequisites for path progression
            </DialogDescription>
          </DialogHeader>
          {currentRule && (
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">Rule Type</label>
                <Select 
                  value={currentRule.type}
                  onValueChange={(value: any) => setCurrentRule({...currentRule, type: value})}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="prerequisite">Prerequisite</SelectItem>
                    <SelectItem value="time-lock">Time Lock</SelectItem>
                    <SelectItem value="score-requirement">Score Requirement</SelectItem>
                    <SelectItem value="completion">Completion</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium">From Node</label>
                <Select 
                  value={currentRule.sourceNodeId}
                  onValueChange={(value) => setCurrentRule({...currentRule, sourceNodeId: value})}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {nodes.map((node) => (
                      <SelectItem key={node.id} value={node.id}>
                        {node.title}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium">To Node</label>
                <Select 
                  value={currentRule.targetNodeId}
                  onValueChange={(value) => setCurrentRule({...currentRule, targetNodeId: value})}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {nodes.map((node) => (
                      <SelectItem key={node.id} value={node.id}>
                        {node.title}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowRuleDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleSaveRule}>
              Save Rule
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Reward Dialog */}
      <Dialog open={showRewardDialog} onOpenChange={setShowRewardDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Configure Reward</DialogTitle>
            <DialogDescription>
              Set up achievements and rewards for learners
            </DialogDescription>
          </DialogHeader>
          {currentReward && (
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">Reward Type</label>
                <Select 
                  value={currentReward.type}
                  onValueChange={(value: any) => setCurrentReward({...currentReward, type: value})}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="badge">Badge</SelectItem>
                    <SelectItem value="certificate">Certificate</SelectItem>
                    <SelectItem value="points">Points</SelectItem>
                    <SelectItem value="unlock">Unlock Content</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium">Unlock at Node</label>
                <Select 
                  value={currentReward.nodeId}
                  onValueChange={(value) => setCurrentReward({...currentReward, nodeId: value})}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {nodes.map((node) => (
                      <SelectItem key={node.id} value={node.id}>
                        {node.title}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium">Reward Name</label>
                <Input
                  value={currentReward.name}
                  onChange={(e) => setCurrentReward({...currentReward, name: e.target.value})}
                  placeholder="Enter reward name"
                />
              </div>

              <div>
                <label className="text-sm font-medium">Description</label>
                <Textarea
                  value={currentReward.description}
                  onChange={(e) => setCurrentReward({...currentReward, description: e.target.value})}
                  placeholder="Describe the reward"
                  rows={2}
                />
              </div>

              {currentReward.type === 'points' && (
                <div>
                  <label className="text-sm font-medium">Point Value</label>
                  <Input
                    type="number"
                    value={currentReward.value}
                    onChange={(e) => setCurrentReward({...currentReward, value: parseInt(e.target.value) || 0})}
                  />
                </div>
              )}
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowRewardDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleSaveReward}>
              Save Reward
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};