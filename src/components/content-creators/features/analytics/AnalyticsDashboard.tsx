import React, { useState, useEffect, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  BarChart3, TrendingUp, Users, Clock, Award, Eye, 
  Download, RefreshCw, Calendar, Filter, TrendingDown,
  BookOpen, Star, MessageSquare, Share2, AlertCircle 
} from 'lucide-react';
import { useContentCreator } from '../../hooks/useContentCreator';
import { ContentAnalytics, Course, AnalyticsPeriod } from '../../types';
import {
  BarChart, Bar, LineChart, Line, PieChart, Pie, Cell,
  XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer,
  Area, AreaChart, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar
} from 'recharts';

interface MetricCardProps {
  label: string;
  value: string | number;
  change: number;
  icon: React.ElementType;
  trend: 'up' | 'down' | 'neutral';
  loading?: boolean;
}

const MetricCard: React.FC<MetricCardProps> = ({ label, value, change, icon: Icon, trend, loading }) => {
  if (loading) {
    return (
      <Card>
        <CardHeader className="pb-2">
          <Skeleton className="h-4 w-24" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-8 w-32 mb-2" />
          <Skeleton className="h-3 w-20" />
        </CardContent>
      </Card>
    );
  }

  const TrendIcon = trend === 'up' ? TrendingUp : trend === 'down' ? TrendingDown : TrendingUp;
  const trendColor = trend === 'up' ? 'text-green-600' : trend === 'down' ? 'text-red-600' : 'text-gray-600';

  return (
    <Card className="hover:shadow-lg transition-shadow">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardDescription>{label}</CardDescription>
          <Icon className="h-4 w-4 text-muted-foreground" />
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        <p className={`text-xs flex items-center gap-1 mt-1 ${trendColor}`}>
          <TrendIcon className="h-3 w-3" />
          {change > 0 ? '+' : ''}{change}% from last period
        </p>
      </CardContent>
    </Card>
  );
};

export const AnalyticsDashboard: React.FC = () => {
  const { getContentAnalytics, getCourses, loading, error } = useContentCreator();
  const [selectedCourse, setSelectedCourse] = useState<string>('all');
  const [selectedPeriod, setSelectedPeriod] = useState<'day' | 'week' | 'month'>('week');
  const [analytics, setAnalytics] = useState<ContentAnalytics | null>(null);
  const [courses, setCourses] = useState<Course[]>([]);
  const [refreshing, setRefreshing] = useState(false);

  // Fetch courses on mount
  useEffect(() => {
    const fetchCourses = async () => {
      try {
        const response = await getCourses({
          filters: { status: ['Published'] },
          pagination: { page: 1, limit: 100 }
        });
        setCourses(response.results);
        if (response.results.length > 0) {
          setSelectedCourse(response.results[0].id);
        }
      } catch (err) {
      }
    };
    fetchCourses();
  }, [getCourses]);

  // Fetch analytics when course or period changes
  useEffect(() => {
    if (selectedCourse && selectedCourse !== 'all') {
      fetchAnalytics();
    }
  }, [selectedCourse, selectedPeriod]);

  const fetchAnalytics = async () => {
    if (!selectedCourse || selectedCourse === 'all') return;

    setRefreshing(true);
    try {
      const endDate = new Date();
      const startDate = new Date();
      
      switch (selectedPeriod) {
        case 'day':
          startDate.setDate(startDate.getDate() - 7);
          break;
        case 'week':
          startDate.setDate(startDate.getDate() - 30);
          break;
        case 'month':
          startDate.setMonth(startDate.getMonth() - 3);
          break;
      }

      const period: AnalyticsPeriod = {
        start: startDate,
        end: endDate,
        granularity: selectedPeriod
      };

      const data = await getContentAnalytics(selectedCourse, period);
      setAnalytics(data);
    } catch (err) {
    } finally {
      setRefreshing(false);
    }
  };

  // Calculate metrics
  const metrics = useMemo(() => {
    if (!analytics) {
      return [
        { label: 'Total Views', value: '0', change: 0, icon: Eye, trend: 'neutral' as const },
        { label: 'Completion Rate', value: '0%', change: 0, icon: Award, trend: 'neutral' as const },
        { label: 'Active Learners', value: '0', change: 0, icon: Users, trend: 'neutral' as const },
        { label: 'Avg. Time', value: '0 min', change: 0, icon: Clock, trend: 'neutral' as const },
      ];
    }

    const previousPeriodChange = 12; // Mock previous period comparison

    return [
      { 
        label: 'Total Views', 
        value: analytics.views.toLocaleString(), 
        change: previousPeriodChange,
        icon: Eye,
        trend: previousPeriodChange > 0 ? 'up' : 'down' as const
      },
      { 
        label: 'Completion Rate', 
        value: `${Math.round(analytics.completionRate)}%`, 
        change: 5,
        icon: Award,
        trend: 'up' as const
      },
      { 
        label: 'Active Learners', 
        value: analytics.uniqueViewers.toLocaleString(), 
        change: 18,
        icon: Users,
        trend: 'up' as const
      },
      { 
        label: 'Avg. Time', 
        value: `${Math.round(analytics.averageTimeSpent)} min`, 
        change: -3,
        icon: Clock,
        trend: 'down' as const
      },
    ];
  }, [analytics]);

  // Generate chart data
  const engagementData = useMemo(() => {
    if (!analytics) return [];

    // Generate mock time series data based on period
    const dataPoints = selectedPeriod === 'day' ? 7 : selectedPeriod === 'week' ? 4 : 12;
    const data = [];
    
    for (let i = 0; i < dataPoints; i++) {
      const date = new Date();
      if (selectedPeriod === 'day') {
        date.setDate(date.getDate() - (dataPoints - i - 1));
      } else if (selectedPeriod === 'week') {
        date.setDate(date.getDate() - (dataPoints - i - 1) * 7);
      } else {
        date.setMonth(date.getMonth() - (dataPoints - i - 1));
      }

      data.push({
        date: date.toLocaleDateString('en-US', { 
          month: 'short', 
          day: selectedPeriod === 'day' ? 'numeric' : undefined,
          year: selectedPeriod === 'month' ? '2-digit' : undefined
        }),
        views: Math.floor(analytics.views / dataPoints * (0.8 + Math.random() * 0.4)),
        completions: Math.floor(analytics.uniqueViewers * analytics.completionRate / 100 / dataPoints * (0.8 + Math.random() * 0.4)),
        interactions: Math.floor(analytics.engagement.interactions / dataPoints * (0.8 + Math.random() * 0.4))
      });
    }
    
    return data;
  }, [analytics, selectedPeriod]);

  const ratingDistribution = useMemo(() => {
    if (!analytics) return [];
    
    return Object.entries(analytics.engagement.ratings.distribution || {
      5: 45, 4: 30, 3: 15, 2: 7, 1: 3
    }).map(([rating, count]) => ({
      rating: `${rating} Stars`,
      count,
      percentage: Math.round(count / analytics.engagement.ratings.count * 100)
    })).reverse();
  }, [analytics]);

  const engagementBreakdown = useMemo(() => {
    if (!analytics) return [];
    
    return [
      { name: 'Interactions', value: analytics.engagement.interactions, color: '#8884d8' },
      { name: 'Downloads', value: analytics.engagement.downloads, color: '#82ca9d' },
      { name: 'Shares', value: analytics.engagement.shares, color: '#ffc658' },
      { name: 'Comments', value: analytics.engagement.comments, color: '#ff7c7c' },
      { name: 'Bookmarks', value: analytics.engagement.bookmarks, color: '#8dd1e1' }
    ];
  }, [analytics]);

  const feedbackData = useMemo(() => {
    if (!analytics) return [];
    
    const total = analytics.feedback.positive + analytics.feedback.neutral + analytics.feedback.negative;
    return [
      { name: 'Positive', value: analytics.feedback.positive, percentage: Math.round(analytics.feedback.positive / total * 100) },
      { name: 'Neutral', value: analytics.feedback.neutral, percentage: Math.round(analytics.feedback.neutral / total * 100) },
      { name: 'Negative', value: analytics.feedback.negative, percentage: Math.round(analytics.feedback.negative / total * 100) }
    ];
  }, [analytics]);

  const handleExport = () => {
    // Export analytics data as CSV
    if (!analytics) return;

    const csvContent = [
      ['Metric', 'Value'],
      ['Total Views', analytics.views],
      ['Unique Viewers', analytics.uniqueViewers],
      ['Completion Rate', analytics.completionRate],
      ['Average Time Spent', analytics.averageTimeSpent],
      ['Average Score', analytics.averageScore],
      ['Total Interactions', analytics.engagement.interactions],
      ['Downloads', analytics.engagement.downloads],
      ['Shares', analytics.engagement.shares],
      ['Comments', analytics.engagement.comments],
      ['Average Rating', analytics.engagement.ratings.average]
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `analytics-${selectedCourse}-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
  };

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Failed to load analytics data. Please try again later.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold">Content Analytics</h2>
          <p className="text-muted-foreground mt-1">
            Track performance and engagement metrics for your content
          </p>
        </div>
        <div className="flex gap-2">
          <Select value={selectedCourse} onValueChange={setSelectedCourse}>
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="Select course" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Courses</SelectItem>
              {courses.map(course => (
                <SelectItem key={course.id} value={course.id}>
                  {course.title}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-[120px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="day">Daily</SelectItem>
              <SelectItem value="week">Weekly</SelectItem>
              <SelectItem value="month">Monthly</SelectItem>
            </SelectContent>
          </Select>
          <Button 
            variant="outline" 
            size="icon"
            onClick={fetchAnalytics}
            disabled={refreshing || !selectedCourse || selectedCourse === 'all'}
          >
            <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
          </Button>
          <Button 
            variant="outline" 
            size="icon"
            onClick={handleExport}
            disabled={!analytics}
          >
            <Download className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {metrics.map((metric, index) => (
          <MetricCard 
            key={index}
            {...metric}
            loading={loading || refreshing}
          />
        ))}
      </div>

      {/* Charts */}
      <Tabs defaultValue="engagement" className="space-y-4">
        <TabsList>
          <TabsTrigger value="engagement">Engagement</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="feedback">Feedback</TabsTrigger>
          <TabsTrigger value="demographics">Demographics</TabsTrigger>
        </TabsList>

        <TabsContent value="engagement" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Engagement Overview
              </CardTitle>
              <CardDescription>Content performance over time</CardDescription>
            </CardHeader>
            <CardContent>
              {loading || refreshing ? (
                <Skeleton className="h-64 w-full" />
              ) : analytics ? (
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={engagementData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Area type="monotone" dataKey="views" stackId="1" stroke="#8884d8" fill="#8884d8" />
                    <Area type="monotone" dataKey="completions" stackId="1" stroke="#82ca9d" fill="#82ca9d" />
                    <Area type="monotone" dataKey="interactions" stackId="1" stroke="#ffc658" fill="#ffc658" />
                  </AreaChart>
                </ResponsiveContainer>
              ) : (
                <div className="h-64 flex items-center justify-center text-muted-foreground">
                  Select a course to view analytics
                </div>
              )}
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Engagement Breakdown</CardTitle>
                <CardDescription>User interaction types</CardDescription>
              </CardHeader>
              <CardContent>
                {analytics ? (
                  <ResponsiveContainer width="100%" height={250}>
                    <PieChart>
                      <Pie
                        data={engagementBreakdown}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percentage }) => `${name}: ${percentage}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {engagementBreakdown.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                ) : (
                  <Skeleton className="h-[250px] w-full" />
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Rating Distribution</CardTitle>
                <CardDescription>User ratings breakdown</CardDescription>
              </CardHeader>
              <CardContent>
                {analytics ? (
                  <div className="space-y-3">
                    {ratingDistribution.map(rating => (
                      <div key={rating.rating} className="flex items-center gap-3">
                        <span className="text-sm w-20">{rating.rating}</span>
                        <Progress value={rating.percentage} className="flex-1" />
                        <span className="text-sm font-medium w-12 text-right">
                          {rating.percentage}%
                        </span>
                      </div>
                    ))}
                    <div className="flex items-center justify-between pt-3 border-t">
                      <div className="flex items-center gap-2">
                        <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                        <span className="font-medium">
                          {analytics.engagement.ratings.average.toFixed(1)}
                        </span>
                      </div>
                      <span className="text-sm text-muted-foreground">
                        {analytics.engagement.ratings.count} total ratings
                      </span>
                    </div>
                  </div>
                ) : (
                  <Skeleton className="h-[250px] w-full" />
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Completion Funnel</CardTitle>
              <CardDescription>User progress through content</CardDescription>
            </CardHeader>
            <CardContent>
              {analytics ? (
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={[
                    { stage: 'Started', users: analytics.uniqueViewers },
                    { stage: '25% Complete', users: Math.floor(analytics.uniqueViewers * 0.75) },
                    { stage: '50% Complete', users: Math.floor(analytics.uniqueViewers * 0.6) },
                    { stage: '75% Complete', users: Math.floor(analytics.uniqueViewers * 0.45) },
                    { stage: 'Completed', users: Math.floor(analytics.uniqueViewers * analytics.completionRate / 100) },
                  ]}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="stage" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="users" fill="#8884d8" />
                  </BarChart>
                </ResponsiveContainer>
              ) : (
                <Skeleton className="h-[300px] w-full" />
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="feedback" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Feedback Summary</CardTitle>
              <CardDescription>User sentiment analysis</CardDescription>
            </CardHeader>
            <CardContent>
              {analytics ? (
                <div className="space-y-4">
                  <ResponsiveContainer width="100%" height={200}>
                    <BarChart data={feedbackData} layout="horizontal">
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis type="number" />
                      <YAxis dataKey="name" type="category" />
                      <Tooltip />
                      <Bar dataKey="value" fill="#8884d8" />
                    </BarChart>
                  </ResponsiveContainer>
                  
                  <div className="space-y-2">
                    <h4 className="font-medium">Common Suggestions</h4>
                    <ul className="text-sm text-muted-foreground space-y-1">
                      {analytics.feedback.suggestions.slice(0, 3).map((suggestion, i) => (
                        <li key={i}>• {suggestion}</li>
                      ))}
                    </ul>
                  </div>

                  {analytics.feedback.commonIssues.length > 0 && (
                    <Alert>
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>
                        <strong>Common Issues:</strong>
                        <ul className="mt-2 space-y-1">
                          {analytics.feedback.commonIssues.slice(0, 3).map((issue, i) => (
                            <li key={i}>• {issue}</li>
                          ))}
                        </ul>
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
              ) : (
                <Skeleton className="h-[300px] w-full" />
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="demographics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Learner Demographics</CardTitle>
              <CardDescription>User distribution and characteristics</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-64 flex items-center justify-center text-muted-foreground">
                Demographics data visualization would go here
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};