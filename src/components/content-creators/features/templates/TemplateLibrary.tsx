import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  FileText, Layout, Video, Image, Code, Package,
  Plus, Search, Filter, Download, Upload, Copy,
  Star, Heart, Eye, Edit, Trash2, Share2,
  Clock, TrendingUp, Award, Zap, BookOpen, Globe
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';
import { useContentCreator } from '../../hooks/useContentCreator';
import { useToast } from '@/components/ui/use-toast';
import * as types from '../../types';

// Helper function to generate unique IDs
const generate_id = () => `template-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

interface TemplateLibraryProps {
  mini?: boolean;
}

interface ExtendedTemplate extends types.ContentTemplate {
  author?: string;
  rating: number;
  downloads: number;
  lastUpdated: Date;
  preview?: string;
  features?: string[];
  compatibility?: string[];
  isFavorite?: boolean;
}

interface TemplateCategory {
  id: string;
  name: string;
  icon: React.ElementType;
  count: number;
  description: string;
}

export const TemplateLibrary: React.FC<TemplateLibraryProps> = ({ mini = false }) => {
  const { 
    getContentTemplates,
    createContentTemplate,
    createCourse,
    loading,
    error 
  } = useContentCreator();
  const { toast } = useToast();

  // State
  const [templates, setTemplates] = useState<ExtendedTemplate[]>([]);
  const [filteredTemplates, setFilteredTemplates] = useState<ExtendedTemplate[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<ExtendedTemplate | null>(null);
  const [activeCategory, setActiveCategory] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'popular' | 'recent' | 'rating'>('popular');
  const [showTemplateDialog, setShowTemplateDialog] = useState(false);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showPreviewDialog, setShowPreviewDialog] = useState(false);
  const [newTemplate, setNewTemplate] = useState<Partial<ExtendedTemplate>>({
    name: '',
    description: '',
    category: '',
    tags: [],
    isPublic: true
  });
  const [favorites, setFavorites] = useState<string[]>([]);

  // Categories
  const categories: TemplateCategory[] = [
    { id: 'all', name: 'All Templates', icon: Package, count: 0, description: 'Browse all templates' },
    { id: 'media', name: 'Media', icon: Video, count: 0, description: 'Video and audio templates' },
    { id: 'assessment', name: 'Assessment', icon: FileText, count: 0, description: 'Quiz and test templates' },
    { id: 'interactive', name: 'Interactive', icon: Layout, count: 0, description: 'Interactive content' },
    { id: 'presentation', name: 'Presentation', icon: Image, count: 0, description: 'Slides and presentations' },
    { id: 'technical', name: 'Technical', icon: Code, count: 0, description: 'Code and technical content' },
    { id: 'complete', name: 'Complete Courses', icon: BookOpen, count: 0, description: 'Full course templates' }
  ];

  // Load templates on mount
  useEffect(() => {
    loadTemplates();
  }, []);

  // Filter templates
  useEffect(() => {
    let filtered = [...templates];

    // Apply category filter
    if (activeCategory !== 'all') {
      filtered = filtered.filter(t => t.category === activeCategory);
    }

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(t => 
        t.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        t.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        t.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    // Apply sorting
    switch (sortBy) {
      case 'popular':
        filtered.sort((a, b) => b.downloads - a.downloads);
        break;
      case 'recent':
        filtered.sort((a, b) => new Date(b.lastUpdated).getTime() - new Date(a.lastUpdated).getTime());
        break;
      case 'rating':
        filtered.sort((a, b) => b.rating - a.rating);
        break;
    }

    setFilteredTemplates(filtered);
  }, [templates, activeCategory, searchQuery, sortBy]);

  // Update category counts
  useEffect(() => {
    categories.forEach(cat => {
      if (cat.id === 'all') {
        cat.count = templates.length;
      } else {
        cat.count = templates.filter(t => t.category === cat.id).length;
      }
    });
  }, [templates]);

  const loadTemplates = async () => {
    try {
      // Fetch real templates from backend
      const fetchedTemplates = await getContentTemplates(activeCategory === 'all' ? undefined : activeCategory);
      
      let templatesToUse: ExtendedTemplate[] = [];
      
      if (fetchedTemplates.length === 0) {
        // Create default templates if none exist
        const defaultTemplates = [
          {
            name: 'Interactive Video Course',
            description: 'Complete video course template with quizzes and assignments',
            category: 'media',
            thumbnail: undefined,
            structure: {},
            tags: ['video', 'interactive', 'quiz'],
            isPublic: true,
            createdBy: 'system',
            usageCount: 0
          },
          {
            name: 'Assessment Builder',
            description: 'Comprehensive assessment template with various question types',
            category: 'assessment',
            thumbnail: undefined,
            structure: {},
            tags: ['quiz', 'exam', 'assessment'],
            isPublic: true,
            createdBy: 'system',
            usageCount: 0
          },
          {
            name: 'Interactive Slides',
            description: 'Presentation template with interactive elements',
            category: 'presentation',
            thumbnail: undefined,
            structure: {},
            tags: ['slides', 'presentation', 'interactive'],
            isPublic: true,
            createdBy: 'system',
            usageCount: 0
          },
          {
            name: 'Code Lab Environment',
            description: 'Interactive coding environment for technical training',
            category: 'technical',
            thumbnail: undefined,
            structure: {},
            tags: ['code', 'programming', 'lab'],
            isPublic: true,
            createdBy: 'system',
            usageCount: 0
          },
          {
            name: 'Complete Course Bundle',
            description: 'Full course structure with all content types',
            category: 'complete',
            thumbnail: undefined,
            structure: {},
            tags: ['course', 'complete', 'bundle'],
            isPublic: true,
            createdBy: 'system',
            usageCount: 0
          }
        ];

        // Create default templates in backend
        for (const template of defaultTemplates) {
          try {
            const created = await createContentTemplate({
              id: '',
              ...template
            });
            
            templatesToUse.push({
              ...created,
              author: created.createdBy || 'System',
              rating: 4.5 + Math.random() * 0.5,
              downloads: created.usageCount || 0,
              lastUpdated: new Date(),
              features: getDefaultFeatures(template.category),
              compatibility: getDefaultCompatibility(template.category)
            });
          } catch (err) {
          }
        }
      } else {
        // Use fetched templates and extend with UI properties
        templatesToUse = fetchedTemplates.map(template => ({
          ...template,
          author: template.createdBy || 'Unknown',
          rating: 4.0 + Math.random() * 1.0,
          downloads: template.usageCount || 0,
          lastUpdated: new Date(),
          features: getDefaultFeatures(template.category),
          compatibility: getDefaultCompatibility(template.category)
        }));
      }

      setTemplates(templatesToUse);
    } catch (err) {
      toast({
        title: "Failed to Load Templates",
        description: "Could not load template library",
        variant: "destructive"
      });
    }
  };
  
  const getDefaultFeatures = (category: string): string[] => {
    switch (category) {
      case 'media':
        return ['Video player', 'Interactive transcripts', 'Quiz integration', 'Progress tracking'];
      case 'assessment':
        return ['Multiple question types', 'Auto-grading', 'Feedback system', 'Analytics'];
      case 'presentation':
        return ['Drag and drop', 'Animations', 'Embedded media', 'Branching scenarios'];
      case 'technical':
        return ['Live code editor', 'Multiple languages', 'Auto-testing', 'Hints system'];
      case 'complete':
        return ['Complete curriculum', 'All content types', 'Assessment suite', 'Certification'];
      default:
        return ['Customizable', 'Reusable', 'Standards compliant'];
    }
  };

  const getDefaultCompatibility = (category: string): string[] => {
    switch (category) {
      case 'media':
      case 'assessment':
        return ['SCORM 1.2', 'SCORM 2004', 'xAPI'];
      case 'presentation':
        return ['HTML5', 'SCORM 2004'];
      case 'technical':
        return ['Web-based', 'LTI 1.3'];
      case 'complete':
        return ['All LMS platforms'];
      default:
        return ['SCORM 1.2', 'xAPI'];
    }
  };

  const handleUseTemplate = async (template: ExtendedTemplate) => {
    try {
      // Create course from template
      const courseData: types.CreateCourseRequest = {
        title: `New Course from ${template.name}`,
        description: template.description,
        category: template.category,
        difficulty: 'Beginner',
        language: 'en',
        metadata: {
          objectives: [],
          prerequisites: [],
          targetAudience: [],
          outcomes: [],
          keywords: template.tags,
          estimatedHours: 10,
          certificateAvailable: false
        }
      };

      const course = await createCourse(courseData);

      // Update template usage count
      setTemplates(prev => prev.map(t => 
        t.id === template.id 
          ? { ...t, usageCount: t.usageCount + 1, downloads: t.downloads + 1 }
          : t
      ));

      toast({
        title: "Template Applied",
        description: `Created new course from ${template.name}`,
      });

      setShowTemplateDialog(false);
    } catch (err) {
      toast({
        title: "Template Application Failed",
        description: "Could not create course from template",
        variant: "destructive"
      });
    }
  };

  const handleCreateTemplate = async () => {
    if (!newTemplate.name || !newTemplate.description || !newTemplate.category) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields",
        variant: "destructive"
      });
      return;
    }

    try {
      const templateData: types.ContentTemplate = {
        id: '',
        name: newTemplate.name,
        description: newTemplate.description,
        category: newTemplate.category,
        thumbnail: newTemplate.thumbnail,
        structure: {},
        tags: newTemplate.tags || [],
        isPublic: newTemplate.isPublic || false,
        createdBy: 'current-user',
        usageCount: 0
      };

      const created = await createContentTemplate(templateData);

      // Add to local state
      const extendedTemplate: ExtendedTemplate = {
        ...created,
        author: 'You',
        rating: 0,
        downloads: 0,
        lastUpdated: new Date(),
        features: [],
        compatibility: []
      };

      setTemplates(prev => [extendedTemplate, ...prev]);

      toast({
        title: "Template Created",
        description: "Your template has been added to the library",
      });

      setShowCreateDialog(false);
      setNewTemplate({
        name: '',
        description: '',
        category: '',
        tags: [],
        isPublic: true
      });
    } catch (err) {
      toast({
        title: "Creation Failed",
        description: "Could not create template",
        variant: "destructive"
      });
    }
  };

  const toggleFavorite = (templateId: string) => {
    if (favorites.includes(templateId)) {
      setFavorites(prev => prev.filter(id => id !== templateId));
    } else {
      setFavorites(prev => [...prev, templateId]);
    }

    // Update template
    setTemplates(prev => prev.map(t => 
      t.id === templateId ? { ...t, isFavorite: !t.isFavorite } : t
    ));
  };

  const handleDuplicateTemplate = (template: ExtendedTemplate) => {
    const duplicated: ExtendedTemplate = {
      ...template,
      id: `template-${Date.now()}`,
      name: `${template.name} (Copy)`,
      author: 'You',
      downloads: 0,
      usageCount: 0,
      lastUpdated: new Date()
    };

    setTemplates(prev => [duplicated, ...prev]);

    toast({
      title: "Template Duplicated",
      description: `Created a copy of ${template.name}`,
    });
  };

  if (mini) {
    return (
      <div className="space-y-2">
        {templates.slice(0, 3).map((template) => {
          const categoryIcon = categories.find(c => c.id === template.category)?.icon || Package;
          const Icon = categoryIcon;
          return (
            <div 
              key={template.id} 
              className="flex items-center justify-between p-2 rounded hover:bg-muted cursor-pointer"
              onClick={() => {
                setSelectedTemplate(template);
                setShowTemplateDialog(true);
              }}
            >
              <div className="flex items-center gap-2">
                <Icon className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">{template.name}</span>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="secondary" className="text-xs">
                  {template.downloads} uses
                </Badge>
              </div>
            </div>
          );
        })}
        <Button variant="ghost" size="sm" className="w-full text-xs">
          View All Templates ({templates.length})
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Template Library</h2>
          <p className="text-muted-foreground mt-1">
            Start with pre-built templates to accelerate content creation
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => setShowCreateDialog(true)}>
            <Upload className="h-4 w-4 mr-2" />
            Upload Template
          </Button>
          <Button onClick={() => setShowCreateDialog(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Create Template
          </Button>
        </div>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search templates..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="popular">
                  <span className="flex items-center gap-2">
                    <TrendingUp className="h-4 w-4" />
                    Most Popular
                  </span>
                </SelectItem>
                <SelectItem value="recent">
                  <span className="flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    Recently Updated
                  </span>
                </SelectItem>
                <SelectItem value="rating">
                  <span className="flex items-center gap-2">
                    <Star className="h-4 w-4" />
                    Highest Rated
                  </span>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Categories Sidebar */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Categories</CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <div className="space-y-1 p-4">
                {categories.map((category) => {
                  const Icon = category.icon;
                  return (
                    <Button
                      key={category.id}
                      variant={activeCategory === category.id ? "secondary" : "ghost"}
                      className="w-full justify-start"
                      onClick={() => setActiveCategory(category.id)}
                    >
                      <Icon className="h-4 w-4 mr-2" />
                      <span className="flex-1 text-left">{category.name}</span>
                      <Badge variant="outline" className="ml-auto">
                        {category.count}
                      </Badge>
                    </Button>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Templates Grid */}
        <div className="lg:col-span-3">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {filteredTemplates.length === 0 ? (
              <div className="col-span-2 text-center py-12">
                <Package className="h-12 w-12 mx-auto text-muted-foreground mb-3" />
                <p className="text-muted-foreground">No templates found</p>
              </div>
            ) : (
              filteredTemplates.map((template) => {
                const categoryIcon = categories.find(c => c.id === template.category)?.icon || Package;
                const Icon = categoryIcon;
                
                return (
                  <Card 
                    key={template.id} 
                    className="hover:shadow-lg transition-shadow cursor-pointer"
                    onClick={() => {
                      setSelectedTemplate(template);
                      setShowTemplateDialog(true);
                    }}
                  >
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div className="flex items-start gap-3">
                          <div className="p-2 bg-muted rounded-lg">
                            <Icon className="h-6 w-6" />
                          </div>
                          <div className="flex-1">
                            <CardTitle className="text-base">{template.name}</CardTitle>
                            <CardDescription className="mt-1">
                              {template.description}
                            </CardDescription>
                          </div>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            toggleFavorite(template.id);
                          }}
                        >
                          <Heart 
                            className={`h-4 w-4 ${
                              favorites.includes(template.id) 
                                ? 'fill-red-500 text-red-500' 
                                : 'text-muted-foreground'
                            }`} 
                          />
                        </Button>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="flex flex-wrap gap-2">
                          {template.tags.map((tag) => (
                            <Badge key={tag} variant="secondary" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                        
                        <div className="flex items-center justify-between text-sm text-muted-foreground">
                          <div className="flex items-center gap-3">
                            <span className="flex items-center gap-1">
                              <Star className="h-3 w-3 fill-yellow-500 text-yellow-500" />
                              {template.rating}
                            </span>
                            <span className="flex items-center gap-1">
                              <Download className="h-3 w-3" />
                              {template.downloads}
                            </span>
                          </div>
                          <span className="text-xs">
                            by {template.author}
                          </span>
                        </div>

                        <div className="flex gap-2">
                          <Button 
                            variant="outline" 
                            size="sm" 
                            className="flex-1"
                            onClick={(e) => {
                              e.stopPropagation();
                              setSelectedTemplate(template);
                              setShowPreviewDialog(true);
                            }}
                          >
                            <Eye className="h-4 w-4 mr-2" />
                            Preview
                          </Button>
                          <Button 
                            size="sm" 
                            className="flex-1"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleUseTemplate(template);
                            }}
                          >
                            <Zap className="h-4 w-4 mr-2" />
                            Use Template
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })
            )}
          </div>
        </div>
      </div>

      {/* Template Details Dialog */}
      <Dialog open={showTemplateDialog} onOpenChange={setShowTemplateDialog}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Template Details</DialogTitle>
            <DialogDescription>
              Review template features and start using it
            </DialogDescription>
          </DialogHeader>
          {selectedTemplate && (
            <div className="space-y-4">
              <div className="flex items-start gap-4">
                <div className="p-3 bg-muted rounded-lg">
                  {(() => {
                    const Icon = categories.find(c => c.id === selectedTemplate.category)?.icon || Package;
                    return <Icon className="h-8 w-8" />;
                  })()}
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-lg">{selectedTemplate.name}</h3>
                  <p className="text-muted-foreground mt-1">{selectedTemplate.description}</p>
                  <div className="flex items-center gap-4 mt-3 text-sm">
                    <span className="flex items-center gap-1">
                      <Star className="h-4 w-4 fill-yellow-500 text-yellow-500" />
                      {selectedTemplate.rating} rating
                    </span>
                    <span className="flex items-center gap-1">
                      <Download className="h-4 w-4" />
                      {selectedTemplate.downloads} downloads
                    </span>
                    <span className="flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      Updated {new Date(selectedTemplate.lastUpdated).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              </div>

              <Separator />

              {selectedTemplate.features && selectedTemplate.features.length > 0 && (
                <div>
                  <h4 className="font-semibold mb-3">Features</h4>
                  <div className="grid grid-cols-2 gap-2">
                    {selectedTemplate.features.map((feature, idx) => (
                      <div key={idx} className="flex items-center gap-2 text-sm">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span>{feature}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {selectedTemplate.compatibility && selectedTemplate.compatibility.length > 0 && (
                <div>
                  <h4 className="font-semibold mb-3">Compatibility</h4>
                  <div className="flex flex-wrap gap-2">
                    {selectedTemplate.compatibility.map((compat, idx) => (
                      <Badge key={idx} variant="outline">
                        <Globe className="h-3 w-3 mr-1" />
                        {compat}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              <div className="flex flex-wrap gap-2">
                {selectedTemplate.tags.map((tag) => (
                  <Badge key={tag} variant="secondary">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}
          <DialogFooter>
            <Button 
              variant="outline"
              onClick={() => selectedTemplate && handleDuplicateTemplate(selectedTemplate)}
            >
              <Copy className="h-4 w-4 mr-2" />
              Duplicate
            </Button>
            <Button 
              variant="outline"
              onClick={() => {
                // Share functionality
                toast({
                  title: "Share Link Copied",
                  description: "Template link has been copied to clipboard",
                });
              }}
            >
              <Share2 className="h-4 w-4 mr-2" />
              Share
            </Button>
            <Button onClick={() => selectedTemplate && handleUseTemplate(selectedTemplate)}>
              <Zap className="h-4 w-4 mr-2" />
              Use This Template
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Create Template Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New Template</DialogTitle>
            <DialogDescription>
              Create a reusable template for your content
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">Template Name</label>
              <Input
                value={newTemplate.name}
                onChange={(e) => setNewTemplate({...newTemplate, name: e.target.value})}
                placeholder="Enter template name"
              />
            </div>
            <div>
              <label className="text-sm font-medium">Description</label>
              <Textarea
                value={newTemplate.description}
                onChange={(e) => setNewTemplate({...newTemplate, description: e.target.value})}
                placeholder="Describe your template"
                rows={3}
              />
            </div>
            <div>
              <label className="text-sm font-medium">Category</label>
              <Select 
                value={newTemplate.category}
                onValueChange={(value) => setNewTemplate({...newTemplate, category: value})}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  {categories.filter(c => c.id !== 'all').map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium">Tags (comma-separated)</label>
              <Input
                value={newTemplate.tags?.join(', ')}
                onChange={(e) => setNewTemplate({
                  ...newTemplate, 
                  tags: e.target.value.split(',').map(t => t.trim()).filter(t => t)
                })}
                placeholder="tag1, tag2, tag3"
              />
            </div>
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={newTemplate.isPublic}
                onChange={(e) => setNewTemplate({...newTemplate, isPublic: e.target.checked})}
              />
              <label className="text-sm">Make this template public</label>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateTemplate}>
              Create Template
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Preview Dialog */}
      <Dialog open={showPreviewDialog} onOpenChange={setShowPreviewDialog}>
        <DialogContent className="max-w-4xl max-h-[80vh]">
          <DialogHeader>
            <DialogTitle>Template Preview</DialogTitle>
            <DialogDescription>
              Preview how this template will look
            </DialogDescription>
          </DialogHeader>
          {selectedTemplate && (
            <ScrollArea className="h-[500px]">
              <div className="p-6 border rounded-lg">
                <div className="text-center py-12">
                  <Package className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
                  <h3 className="text-xl font-semibold mb-2">{selectedTemplate.name}</h3>
                  <p className="text-muted-foreground mb-6">{selectedTemplate.description}</p>
                  <div className="text-sm text-muted-foreground">
                    Template preview would be rendered here
                  </div>
                </div>
              </div>
            </ScrollArea>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowPreviewDialog(false)}>
              Close
            </Button>
            <Button onClick={() => {
              setShowPreviewDialog(false);
              selectedTemplate && handleUseTemplate(selectedTemplate);
            }}>
              Use Template
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};