import React, { useState, useRef, useEffect } from 'react';
import {
  <PERSON>,
  Mic,
  MicOff,
  Camera,
  CameraOff,
  Monitor,
  Square,
  Circle,
  Pause,
  Play,
  Download,
  Upload,
  Settings,
  Scissors,
  Volume2,
  VolumeX,
  Maximize,
  Minimize,
  RefreshCw
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/components/ui/use-toast';

interface RecordingState {
  isRecording: boolean;
  isPaused: boolean;
  duration: number;
  startTime: number | null;
}

interface MediaDevices {
  cameras: MediaDeviceInfo[];
  microphones: MediaDeviceInfo[];
  speakers: MediaDeviceInfo[];
}

export const ContentStudio: React.FC = () => {
  const { toast } = useToast();
  const [recordingState, setRecordingState] = useState<RecordingState>({
    isRecording: false,
    isPaused: false,
    duration: 0,
    startTime: null
  });
  
  const [mediaDevices, setMediaDevices] = useState<MediaDevices>({
    cameras: [],
    microphones: [],
    speakers: []
  });
  
  const [selectedDevices, setSelectedDevices] = useState({
    camera: 'no-camera',
    microphone: 'no-microphone',
    speaker: 'no-speaker'
  });
  
  const [recordingSettings, setRecordingSettings] = useState({
    video: true,
    audio: true,
    screenShare: false,
    resolution: '1920x1080',
    frameRate: 30,
    bitrate: 2500,
    format: 'mp4'
  });
  
  const [audioLevels, setAudioLevels] = useState({
    input: 0,
    output: 50
  });
  
  const videoPreviewRef = useRef<HTMLVideoElement>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const recordedChunksRef = useRef<Blob[]>([]);
  const streamRef = useRef<MediaStream | null>(null);
  const durationIntervalRef = useRef<NodeJS.Timeout | null>(null);
  
  useEffect(() => {
    // Get available media devices
    loadMediaDevices();
    
    return () => {
      // Cleanup
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
      }
      if (durationIntervalRef.current) {
        clearInterval(durationIntervalRef.current);
      }
    };
  }, []);
  
  const loadMediaDevices = async () => {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices();
      
      const cameras = devices.filter(device => device.kind === 'videoinput');
      const microphones = devices.filter(device => device.kind === 'audioinput');
      const speakers = devices.filter(device => device.kind === 'audiooutput');
      
      setMediaDevices({ cameras, microphones, speakers });
      
      // Set default devices
      if (cameras.length > 0) {
        setSelectedDevices(prev => ({ ...prev, camera: cameras[0].deviceId }));
      }
      if (microphones.length > 0) {
        setSelectedDevices(prev => ({ ...prev, microphone: microphones[0].deviceId }));
      }
      if (speakers.length > 0) {
        setSelectedDevices(prev => ({ ...prev, speaker: speakers[0].deviceId }));
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to load media devices',
        variant: 'destructive'
      });
    }
  };
  
  const startRecording = async () => {
    try {
      const constraints: MediaStreamConstraints = {
        video: recordingSettings.video ? {
          deviceId: selectedDevices.camera,
          width: { ideal: parseInt(recordingSettings.resolution.split('x')[0]) },
          height: { ideal: parseInt(recordingSettings.resolution.split('x')[1]) },
          frameRate: { ideal: recordingSettings.frameRate }
        } : false,
        audio: recordingSettings.audio ? {
          deviceId: selectedDevices.microphone,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        } : false
      };
      
      let stream: MediaStream;
      
      if (recordingSettings.screenShare) {
        // Get screen share stream
        const displayStream = await navigator.mediaDevices.getDisplayMedia({
          video: true,
          audio: false
        });
        
        // Get audio stream if needed
        if (recordingSettings.audio) {
          const audioStream = await navigator.mediaDevices.getUserMedia({ audio: constraints.audio });
          stream = new MediaStream([
            ...displayStream.getVideoTracks(),
            ...audioStream.getAudioTracks()
          ]);
        } else {
          stream = displayStream;
        }
      } else {
        stream = await navigator.mediaDevices.getUserMedia(constraints);
      }
      
      streamRef.current = stream;
      
      // Display preview
      if (videoPreviewRef.current) {
        videoPreviewRef.current.srcObject = stream;
      }
      
      // Setup media recorder
      const mimeType = `video/${recordingSettings.format === 'mp4' ? 'mp4' : 'webm'}`;
      const options: MediaRecorderOptions = {
        mimeType: MediaRecorder.isTypeSupported(mimeType) ? mimeType : 'video/webm',
        videoBitsPerSecond: recordingSettings.bitrate * 1000
      };
      
      const mediaRecorder = new MediaRecorder(stream, options);
      mediaRecorderRef.current = mediaRecorder;
      
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          recordedChunksRef.current.push(event.data);
        }
      };
      
      mediaRecorder.onstop = () => {
        const blob = new Blob(recordedChunksRef.current, { type: mimeType });
        const url = URL.createObjectURL(blob);
        
        // Download the recording
        const a = document.createElement('a');
        a.href = url;
        a.download = `recording-${Date.now()}.${recordingSettings.format}`;
        a.click();
        
        // Reset
        recordedChunksRef.current = [];
      };
      
      mediaRecorder.start(1000); // Collect data every second
      
      // Update state
      setRecordingState({
        isRecording: true,
        isPaused: false,
        duration: 0,
        startTime: Date.now()
      });
      
      // Start duration timer
      durationIntervalRef.current = setInterval(() => {
        setRecordingState(prev => ({
          ...prev,
          duration: Date.now() - (prev.startTime || Date.now())
        }));
      }, 100);
      
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to start recording',
        variant: 'destructive'
      });
    }
  };
  
  const stopRecording = () => {
    if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {
      mediaRecorderRef.current.stop();
    }
    
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }
    
    if (videoPreviewRef.current) {
      videoPreviewRef.current.srcObject = null;
    }
    
    if (durationIntervalRef.current) {
      clearInterval(durationIntervalRef.current);
      durationIntervalRef.current = null;
    }
    
    setRecordingState({
      isRecording: false,
      isPaused: false,
      duration: 0,
      startTime: null
    });
  };
  
  const pauseResumeRecording = () => {
    if (!mediaRecorderRef.current) return;
    
    if (recordingState.isPaused) {
      mediaRecorderRef.current.resume();
      setRecordingState(prev => ({ ...prev, isPaused: false }));
    } else {
      mediaRecorderRef.current.pause();
      setRecordingState(prev => ({ ...prev, isPaused: true }));
    }
  };
  
  const formatDuration = (ms: number) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    return `${hours.toString().padStart(2, '0')}:${(minutes % 60).toString().padStart(2, '0')}:${(seconds % 60).toString().padStart(2, '0')}`;
  };
  
  return (
    <div className="grid gap-6">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Recording Area */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Recording Studio</CardTitle>
              <CardDescription>
                Record high-quality video and audio content for your courses
              </CardDescription>
            </CardHeader>
            <CardContent>
              {/* Video Preview */}
              <div className="aspect-video bg-black rounded-lg overflow-hidden mb-4 relative">
                <video
                  ref={videoPreviewRef}
                  autoPlay
                  muted
                  playsInline
                  className="w-full h-full object-contain"
                />
                
                {recordingState.isRecording && (
                  <div className="absolute top-4 left-4 flex items-center gap-2">
                    <div className="flex items-center gap-2 bg-red-600 text-white px-3 py-1 rounded-full">
                      <Circle className="h-3 w-3 fill-current animate-pulse" />
                      <span className="text-sm font-medium">
                        {recordingState.isPaused ? 'PAUSED' : 'RECORDING'}
                      </span>
                      <span className="text-sm">
                        {formatDuration(recordingState.duration)}
                      </span>
                    </div>
                  </div>
                )}
              </div>
              
              {/* Recording Controls */}
              <div className="flex items-center justify-center gap-4">
                {!recordingState.isRecording ? (
                  <Button
                    size="lg"
                    onClick={startRecording}
                    className="gap-2"
                  >
                    <Circle className="h-5 w-5" />
                    Start Recording
                  </Button>
                ) : (
                  <>
                    <Button
                      size="lg"
                      variant="outline"
                      onClick={pauseResumeRecording}
                      className="gap-2"
                    >
                      {recordingState.isPaused ? (
                        <>
                          <Play className="h-5 w-5" />
                          Resume
                        </>
                      ) : (
                        <>
                          <Pause className="h-5 w-5" />
                          Pause
                        </>
                      )}
                    </Button>
                    <Button
                      size="lg"
                      variant="destructive"
                      onClick={stopRecording}
                      className="gap-2"
                    >
                      <Square className="h-5 w-5" />
                      Stop Recording
                    </Button>
                  </>
                )}
              </div>
              
              {/* Quick Settings */}
              <div className="grid grid-cols-3 gap-4 mt-6">
                <div className="flex items-center justify-between">
                  <Label htmlFor="video-toggle">Video</Label>
                  <Switch
                    id="video-toggle"
                    checked={recordingSettings.video}
                    onCheckedChange={(checked) => 
                      setRecordingSettings(prev => ({ ...prev, video: checked }))
                    }
                    disabled={recordingState.isRecording}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <Label htmlFor="audio-toggle">Audio</Label>
                  <Switch
                    id="audio-toggle"
                    checked={recordingSettings.audio}
                    onCheckedChange={(checked) => 
                      setRecordingSettings(prev => ({ ...prev, audio: checked }))
                    }
                    disabled={recordingState.isRecording}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <Label htmlFor="screen-toggle">Screen Share</Label>
                  <Switch
                    id="screen-toggle"
                    checked={recordingSettings.screenShare}
                    onCheckedChange={(checked) => 
                      setRecordingSettings(prev => ({ ...prev, screenShare: checked }))
                    }
                    disabled={recordingState.isRecording}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
        
        {/* Settings Panel */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle>Recording Settings</CardTitle>
            </CardHeader>
            <CardContent>
              <Tabs value="devices" className="w-full">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="devices">Devices</TabsTrigger>
                  <TabsTrigger value="quality">Quality</TabsTrigger>
                </TabsList>
                
                <TabsContent value="devices" className="space-y-4">
                  <div>
                    <Label htmlFor="camera-select">Camera</Label>
                    <Select
                      value={selectedDevices.camera}
                      onValueChange={(value) => 
                        setSelectedDevices(prev => ({ ...prev, camera: value }))
                      }
                      disabled={recordingState.isRecording}
                    >
                      <SelectTrigger id="camera-select">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {mediaDevices.cameras.length > 0 ? (
                          mediaDevices.cameras.map(device => (
                            <SelectItem 
                              key={device.deviceId || 'default-camera'} 
                              value={device.deviceId || 'default-camera'}
                            >
                              {device.label || `Camera ${device.deviceId.substring(0, 8)}`}
                            </SelectItem>
                          ))
                        ) : (
                          <SelectItem value="no-camera">No cameras available</SelectItem>
                        )}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <Label htmlFor="microphone-select">Microphone</Label>
                    <Select
                      value={selectedDevices.microphone}
                      onValueChange={(value) => 
                        setSelectedDevices(prev => ({ ...prev, microphone: value }))
                      }
                      disabled={recordingState.isRecording}
                    >
                      <SelectTrigger id="microphone-select">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {mediaDevices.microphones.length > 0 ? (
                          mediaDevices.microphones.map(device => (
                            <SelectItem 
                              key={device.deviceId || 'default-mic'} 
                              value={device.deviceId || 'default-mic'}
                            >
                              {device.label || `Microphone ${device.deviceId.substring(0, 8)}`}
                            </SelectItem>
                          ))
                        ) : (
                          <SelectItem value="no-microphone">No microphones available</SelectItem>
                        )}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <Label htmlFor="input-volume">Input Volume</Label>
                    <div className="flex items-center gap-2">
                      <Volume2 className="h-4 w-4" />
                      <Slider
                        id="input-volume"
                        value={[audioLevels.input]}
                        onValueChange={([value]) => 
                          setAudioLevels(prev => ({ ...prev, input: value }))
                        }
                        max={100}
                        step={1}
                        className="flex-1"
                      />
                      <span className="text-sm w-12 text-right">
                        {audioLevels.input}%
                      </span>
                    </div>
                  </div>
                </TabsContent>
                
                <TabsContent value="quality" className="space-y-4">
                  <div>
                    <Label htmlFor="resolution-select">Resolution</Label>
                    <Select
                      value={recordingSettings.resolution}
                      onValueChange={(value) => 
                        setRecordingSettings(prev => ({ ...prev, resolution: value }))
                      }
                      disabled={recordingState.isRecording}
                    >
                      <SelectTrigger id="resolution-select">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="1920x1080">1080p (1920x1080)</SelectItem>
                        <SelectItem value="1280x720">720p (1280x720)</SelectItem>
                        <SelectItem value="854x480">480p (854x480)</SelectItem>
                        <SelectItem value="640x360">360p (640x360)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <Label htmlFor="framerate-select">Frame Rate</Label>
                    <Select
                      value={recordingSettings.frameRate.toString()}
                      onValueChange={(value) => 
                        setRecordingSettings(prev => ({ ...prev, frameRate: parseInt(value) }))
                      }
                      disabled={recordingState.isRecording}
                    >
                      <SelectTrigger id="framerate-select">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="60">60 FPS</SelectItem>
                        <SelectItem value="30">30 FPS</SelectItem>
                        <SelectItem value="24">24 FPS</SelectItem>
                        <SelectItem value="15">15 FPS</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <Label htmlFor="bitrate-slider">Bitrate (kbps)</Label>
                    <div className="flex items-center gap-2">
                      <Slider
                        id="bitrate-slider"
                        value={[recordingSettings.bitrate]}
                        onValueChange={([value]) => 
                          setRecordingSettings(prev => ({ ...prev, bitrate: value }))
                        }
                        min={500}
                        max={10000}
                        step={500}
                        className="flex-1"
                        disabled={recordingState.isRecording}
                      />
                      <span className="text-sm w-16 text-right">
                        {recordingSettings.bitrate}
                      </span>
                    </div>
                  </div>
                  
                  <div>
                    <Label htmlFor="format-select">Format</Label>
                    <Select
                      value={recordingSettings.format}
                      onValueChange={(value) => 
                        setRecordingSettings(prev => ({ ...prev, format: value }))
                      }
                      disabled={recordingState.isRecording}
                    >
                      <SelectTrigger id="format-select">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="mp4">MP4</SelectItem>
                        <SelectItem value="webm">WebM</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>
      </div>
      
      {/* Additional Tools */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Video Editor</CardTitle>
          </CardHeader>
          <CardContent>
            <Button variant="outline" className="w-full gap-2">
              <Scissors className="h-4 w-4" />
              Edit Recording
            </Button>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Audio Enhancement</CardTitle>
          </CardHeader>
          <CardContent>
            <Button variant="outline" className="w-full gap-2">
              <Volume2 className="h-4 w-4" />
              Enhance Audio
            </Button>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Upload Media</CardTitle>
          </CardHeader>
          <CardContent>
            <Button variant="outline" className="w-full gap-2">
              <Upload className="h-4 w-4" />
              Upload Files
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};