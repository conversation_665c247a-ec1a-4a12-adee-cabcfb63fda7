import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Globe, Languages, Upload, Download, CheckCircle, Clock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';

export const LocalizationManager: React.FC = () => {
  const languages = [
    { code: 'en', name: 'English', progress: 100, status: 'completed' },
    { code: 'es', name: 'Spanish', progress: 85, status: 'in-progress' },
    { code: 'fr', name: 'French', progress: 60, status: 'in-progress' },
    { code: 'de', name: 'German', progress: 30, status: 'in-progress' },
    { code: 'zh', name: 'Chinese', progress: 0, status: 'pending' },
    { code: 'ja', name: 'Japanese', progress: 0, status: 'pending' },
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'in-progress': return <Clock className="h-4 w-4 text-blue-500" />;
      case 'pending': return <Clock className="h-4 w-4 text-gray-400" />;
      default: return null;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed': return <Badge className="bg-green-100 text-green-800">Complete</Badge>;
      case 'in-progress': return <Badge className="bg-blue-100 text-blue-800">In Progress</Badge>;
      case 'pending': return <Badge variant="outline">Pending</Badge>;
      default: return null;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Localization Manager</h2>
          <p className="text-muted-foreground mt-1">
            Translate and adapt your content for global audiences
          </p>
        </div>
        <Button>
          <Languages className="h-4 w-4 mr-2" />
          Add Language
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-5 w-5" />
                Language Progress
              </CardTitle>
              <CardDescription>Translation status for all languages</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {languages.map((lang) => (
                <div key={lang.code} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {getStatusIcon(lang.status)}
                      <span className="font-medium">{lang.name}</span>
                      <span className="text-sm text-muted-foreground">({lang.code})</span>
                    </div>
                    {getStatusBadge(lang.status)}
                  </div>
                  <Progress value={lang.progress} className="h-2" />
                  <div className="flex items-center justify-between text-sm text-muted-foreground">
                    <span>{lang.progress}% translated</span>
                    <div className="flex gap-2">
                      <Button variant="ghost" size="sm">Edit</Button>
                      <Button variant="ghost" size="sm">Export</Button>
                    </div>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>

        <div className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button variant="outline" className="w-full justify-start">
                <Upload className="h-4 w-4 mr-2" />
                Import Translations
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Download className="h-4 w-4 mr-2" />
                Export All Languages
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Globe className="h-4 w-4 mr-2" />
                Auto-Translate
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Statistics</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Total Languages</span>
                <span className="font-medium">6</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Completed</span>
                <span className="font-medium">1</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">In Progress</span>
                <span className="font-medium">3</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Total Words</span>
                <span className="font-medium">12,543</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};