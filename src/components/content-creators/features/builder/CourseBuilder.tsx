import React, { useState, useCallback, useRef } from 'react';
// Note: @dnd-kit is not installed - drag and drop functionality disabled
// import { DndContext, DragEndEvent, DragOverlay, closestCenter } from '@dnd-kit/core';
// import { SortableContext, verticalListSortingStrategy, arrayMove } from '@dnd-kit/sortable';
// import { useSortable } from '@dnd-kit/sortable';
// import { CSS } from '@dnd-kit/utilities';

// Simple arrayMove replacement
const arrayMove = <T,>(array: T[], from: number, to: number): T[] => {
  const newArray = [...array];
  const [movedItem] = newArray.splice(from, 1);
  newArray.splice(to, 0, movedItem);
  return newArray;
};
import {
  Plus,
  GripVertical,
  Edit,
  Trash2,
  Copy,
  Eye,
  Save,
  // Publish, // Not available in lucide-react
  ChevronRight,
  ChevronDown,
  Video,
  FileText,
  Headphones,
  Image,
  Package,
  Clock,
  Users,
  Award,
  Settings,
  Upload,
  Link,
  Code,
  BookOpen
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/components/ui/use-toast';

import * as types from '../../types';
import {
  Course,
  CourseModule,
  Lesson,
  ContentType,
  ContentStatus,
  CourseMetadata,
  InteractiveElement,
  Resource
} from '../../types';
import { useContentCreator } from '../../hooks/useContentCreator';

interface SortableModuleProps {
  module: CourseModule;
  onEdit: () => void;
  onDelete: () => void;
  onDuplicate: () => void;
  isExpanded: boolean;
  onToggleExpand: () => void;
  children?: React.ReactNode;
}

const SortableModule: React.FC<SortableModuleProps> = ({
  module,
  onEdit,
  onDelete,
  onDuplicate,
  isExpanded,
  onToggleExpand,
  children
}) => {
  // Drag and drop disabled - @dnd-kit not installed
  const isDragging = false;
  const attributes = {}; // Placeholder for drag attributes
  const listeners = {}; // Placeholder for drag listeners

  return (
    <div>
      <Card className={`mb-4 ${isDragging ? 'shadow-lg' : ''}`}>
        <CardHeader>
          <div className="flex items-center gap-2">
            <button
              className="cursor-grab active:cursor-grabbing p-1 hover:bg-muted rounded"
              {...attributes}
              {...listeners}
            >
              <GripVertical className="h-4 w-4 text-muted-foreground" />
            </button>
            
            <button
              onClick={onToggleExpand}
              className="p-1 hover:bg-muted rounded"
            >
              {isExpanded ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
            </button>
            
            <div className="flex-1">
              <CardTitle className="text-base">{module.title}</CardTitle>
              <CardDescription className="text-sm">{module.description}</CardDescription>
            </div>
            
            <div className="flex items-center gap-2">
              <Badge variant="outline">
                <Clock className="h-3 w-3 mr-1" />
                {module.duration} min
              </Badge>
              <Badge variant="outline">
                {module.lessons.length} lessons
              </Badge>
              {module.isRequired && (
                <Badge variant="default">Required</Badge>
              )}
            </div>
            
            <div className="flex items-center gap-1">
              <Button variant="ghost" size="icon" onClick={onEdit}>
                <Edit className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="icon" onClick={onDuplicate}>
                <Copy className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="icon" onClick={onDelete}>
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        
        {isExpanded && (
          <CardContent>
            <Separator className="mb-4" />
            {children}
          </CardContent>
        )}
      </Card>
    </div>
  );
};

interface SortableLessonProps {
  lesson: Lesson;
  onEdit: () => void;
  onDelete: () => void;
  onDuplicate: () => void;
}

const SortableLesson: React.FC<SortableLessonProps> = ({
  lesson,
  onEdit,
  onDelete,
  onDuplicate
}) => {
  // Drag and drop disabled - @dnd-kit not installed
  const isDragging = false;
  const attributes = {}; // Placeholder for drag attributes
  const listeners = {}; // Placeholder for drag listeners
  const setNodeRef = React.useRef(null); // Placeholder for drag ref

  const style = {
    opacity: isDragging ? 0.5 : 1,
  };

  const getContentIcon = () => {
    switch (lesson.contentType) {
      case ContentType.VIDEO:
        return <Video className="h-4 w-4" />;
      case ContentType.AUDIO:
        return <Headphones className="h-4 w-4" />;
      case ContentType.TEXT:
        return <FileText className="h-4 w-4" />;
      case ContentType.INTERACTIVE:
        return <Code className="h-4 w-4" />;
      default:
        return <BookOpen className="h-4 w-4" />;
    }
  };

  return (
    <div ref={setNodeRef} style={style}>
      <div className={`flex items-center gap-2 p-3 bg-muted/50 rounded-lg mb-2 ${isDragging ? 'shadow-md' : ''}`}>
        <button
          className="cursor-grab active:cursor-grabbing p-1 hover:bg-background rounded"
          {...attributes}
          {...listeners}
        >
          <GripVertical className="h-4 w-4 text-muted-foreground" />
        </button>
        
        {getContentIcon()}
        
        <div className="flex-1">
          <div className="font-medium text-sm">{lesson.title}</div>
          <div className="text-xs text-muted-foreground">{lesson.description}</div>
        </div>
        
        <Badge variant="outline" className="text-xs">
          {lesson.duration} min
        </Badge>
        
        <div className="flex items-center gap-1">
          <Button variant="ghost" size="icon" className="h-8 w-8" onClick={onEdit}>
            <Edit className="h-3 w-3" />
          </Button>
          <Button variant="ghost" size="icon" className="h-8 w-8" onClick={onDuplicate}>
            <Copy className="h-3 w-3" />
          </Button>
          <Button variant="ghost" size="icon" className="h-8 w-8" onClick={onDelete}>
            <Trash2 className="h-3 w-3" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export const CourseBuilder: React.FC = () => {
  const { toast } = useToast();
  const { 
    createCourse, 
    updateCourse, 
    createModule, 
    updateModule, 
    createLesson, 
    updateLesson, 
    publishCourse 
  } = useContentCreator();
  
  const [course, setCourse] = useState<Partial<Course>>({
    title: '',
    description: '',
    modules: [],
    metadata: {
      objectives: [],
      prerequisites: [],
      targetAudience: [],
      outcomes: [],
      keywords: [],
      estimatedHours: 0,
      certificateAvailable: false
    },
    status: ContentStatus.DRAFT,
    difficulty: 'Beginner',
    language: 'en',
    tags: [],
    category: ''
  });

  const [expandedModules, setExpandedModules] = useState<Set<string>>(new Set());
  const [activeModuleId, setActiveModuleId] = useState<string | null>(null);
  const [activeLessonId, setActiveLessonId] = useState<string | null>(null);
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Drag and drop disabled - @dnd-kit not installed
  const handleDragEnd = (event: any) => {
    const { active, over } = event;
    
    if (!over || active.id === over.id) return;
    
    setCourse((prev) => {
      if (!prev.modules) return prev;
      
      const oldIndex = prev.modules.findIndex((m) => m.id === active.id);
      const newIndex = prev.modules.findIndex((m) => m.id === over.id);
      
      const newModules = arrayMove(prev.modules || [], oldIndex, newIndex);
      
      // Update order property
      return {
        ...prev,
        modules: newModules.map((m, index) => ({ ...m, order: index }))
      };
    });
  };

  const handleLessonDragEnd = (moduleId: string) => (event: any) => {
    const { active, over } = event;
    
    if (!over || active.id === over.id) return;
    
    setCourse((prev) => {
      if (!prev.modules) return prev;
      
      return {
        ...prev,
        modules: prev.modules.map((module) => {
          if (module.id !== moduleId) return module;
          
          const oldIndex = module.lessons.findIndex((l) => l.id === active.id);
          const newIndex = module.lessons.findIndex((l) => l.id === over.id);
          
          const newLessons = arrayMove(module.lessons, oldIndex, newIndex);
          
          return {
            ...module,
            lessons: newLessons.map((l, index) => ({ ...l, order: index }))
          };
        })
      };
    });
  };

  const addModule = () => {
    const newModule: CourseModule = {
      id: `module-${Date.now()}`,
      courseId: course.id || '',
      title: 'New Module',
      description: '',
      order: course.modules?.length || 0,
      lessons: [],
      assessments: [],
      duration: 0,
      isRequired: true
    };
    
    setCourse((prev) => ({
      ...prev,
      modules: [...(prev.modules || []), newModule]
    }));
    
    setExpandedModules((prev) => new Set(prev).add(newModule.id));
  };

  const addLesson = (moduleId: string) => {
    const newLesson: Lesson = {
      id: `lesson-${Date.now()}`,
      moduleId,
      title: 'New Lesson',
      description: '',
      order: 0,
      contentType: ContentType.VIDEO,
      content: {
        type: ContentType.VIDEO,
        data: null
      },
      duration: 0,
      resources: [],
      interactions: []
    };
    
    setCourse((prev) => ({
      ...prev,
      modules: prev.modules?.map((module) => {
        if (module.id !== moduleId) return module;
        return {
          ...module,
          lessons: [...module.lessons, newLesson]
        };
      })
    }));
  };

  const duplicateModule = (moduleId: string) => {
    setCourse((prev) => {
      const moduleToDuplicate = prev.modules?.find((m) => m.id === moduleId);
      if (!moduleToDuplicate) return prev;
      
      const duplicatedModule: CourseModule = {
        ...moduleToDuplicate,
        id: `module-${Date.now()}`,
        title: `${moduleToDuplicate.title} (Copy)`,
        order: prev.modules?.length || 0,
        lessons: moduleToDuplicate.lessons.map((lesson) => ({
          ...lesson,
          id: `lesson-${Date.now()}-${Math.random()}`,
          moduleId: `module-${Date.now()}`
        }))
      };
      
      return {
        ...prev,
        modules: [...(prev.modules || []), duplicatedModule]
      };
    });
  };

  const deleteModule = (moduleId: string) => {
    setCourse((prev) => ({
      ...prev,
      modules: prev.modules?.filter((m) => m.id !== moduleId)
    }));
  };

  const duplicateLesson = (moduleId: string, lessonId: string) => {
    setCourse((prev) => ({
      ...prev,
      modules: prev.modules?.map((module) => {
        if (module.id !== moduleId) return module;
        
        const lessonToDuplicate = module.lessons.find((l) => l.id === lessonId);
        if (!lessonToDuplicate) return module;
        
        const duplicatedLesson: Lesson = {
          ...lessonToDuplicate,
          id: `lesson-${Date.now()}`,
          title: `${lessonToDuplicate.title} (Copy)`,
          order: module.lessons.length
        };
        
        return {
          ...module,
          lessons: [...module.lessons, duplicatedLesson]
        };
      })
    }));
  };

  const deleteLesson = (moduleId: string, lessonId: string) => {
    setCourse((prev) => ({
      ...prev,
      modules: prev.modules?.map((module) => {
        if (module.id !== moduleId) return module;
        return {
          ...module,
          lessons: module.lessons.filter((l) => l.id !== lessonId)
        };
      })
    }));
  };

  const toggleModuleExpand = (moduleId: string) => {
    setExpandedModules((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(moduleId)) {
        newSet.delete(moduleId);
      } else {
        newSet.add(moduleId);
      }
      return newSet;
    });
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      if (course.id) {
        await updateCourse({
          id: course.id,
          updates: course
        });
      } else {
        const result = await createCourse(course as types.CreateCourseRequest);
        setCourse(result);
      }
      toast({
        title: 'Course saved',
        description: 'Your course has been saved successfully.'
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to save course. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handlePublish = async () => {
    if (!course.id) {
      toast({
        title: 'Error',
        description: 'Please save the course before publishing.',
        variant: 'destructive'
      });
      return;
    }
    
    try {
      await publishCourse({
        courseId: course.id,
        publishOptions: {
          sendNotifications: true
        }
      });
      
      setCourse((prev) => ({
        ...prev,
        status: ContentStatus.PUBLISHED
      }));
      
      toast({
        title: 'Course published',
        description: 'Your course is now live and available to learners.'
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to publish course. Please try again.',
        variant: 'destructive'
      });
    }
  };

  return (
    <div className="flex h-full">
      {/* Main Content Area */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <div className="border-b p-4 bg-background">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Input
                placeholder="Course Title"
                value={course.title}
                onChange={(e) => setCourse((prev) => ({ ...prev, title: e.target.value }))}
                className="text-xl font-semibold w-96"
              />
              <Badge variant={course.status === ContentStatus.PUBLISHED ? 'default' : 'outline'}>
                {course.status}
              </Badge>
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                onClick={() => setIsPreviewMode(!isPreviewMode)}
              >
                <Eye className="h-4 w-4 mr-2" />
                Preview
              </Button>
              <Button
                variant="outline"
                onClick={handleSave}
                disabled={isSaving}
              >
                <Save className="h-4 w-4 mr-2" />
                Save
              </Button>
              <Button
                onClick={handlePublish}
                disabled={course.status === ContentStatus.PUBLISHED}
              >
                <Upload className="h-4 w-4 mr-2" />
                Publish
              </Button>
            </div>
          </div>
        </div>

        {/* Course Builder Content */}
        <div className="flex-1 overflow-auto">
          <div className="p-6">
            {/* Course Details */}
            <Card className="mb-6">
              <CardHeader>
                <CardTitle>Course Details</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4">
                  <div>
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      placeholder="Enter course description..."
                      value={course.description}
                      onChange={(e) => setCourse((prev) => ({ ...prev, description: e.target.value }))}
                      className="min-h-[100px]"
                    />
                  </div>
                  
                  <div className="grid grid-cols-3 gap-4">
                    <div>
                      <Label htmlFor="category">Category</Label>
                      <Input
                        id="category"
                        placeholder="e.g., Technology, Business"
                        value={course.category}
                        onChange={(e) => setCourse((prev) => ({ ...prev, category: e.target.value }))}
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="difficulty">Difficulty</Label>
                      <Select
                        value={course.difficulty}
                        onValueChange={(value: any) => setCourse((prev) => ({ ...prev, difficulty: value }))}
                      >
                        <SelectTrigger id="difficulty">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="beginner">Beginner</SelectItem>
                          <SelectItem value="intermediate">Intermediate</SelectItem>
                          <SelectItem value="advanced">Advanced</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div>
                      <Label htmlFor="language">Language</Label>
                      <Select
                        value={course.language}
                        onValueChange={(value) => setCourse((prev) => ({ ...prev, language: value }))}
                      >
                        <SelectTrigger id="language">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="en">English</SelectItem>
                          <SelectItem value="es">Spanish</SelectItem>
                          <SelectItem value="fr">French</SelectItem>
                          <SelectItem value="de">German</SelectItem>
                          <SelectItem value="zh">Chinese</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Modules Section */}
            <div className="mb-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold">Course Modules</h2>
                <Button onClick={addModule}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Module
                </Button>
              </div>
              
              {course.modules && course.modules.length > 0 ? (
                <div>
                  {/* Drag and drop disabled - @dnd-kit not installed */}
                  <div>
                    {course.modules.map((module) => (
                      <SortableModule
                        key={module.id}
                        module={module}
                        onEdit={() => setActiveModuleId(module.id)}
                        onDelete={() => deleteModule(module.id)}
                        onDuplicate={() => duplicateModule(module.id)}
                        isExpanded={expandedModules.has(module.id)}
                        onToggleExpand={() => toggleModuleExpand(module.id)}
                      >
                        {/* Lessons within Module */}
                        <div>
                          <div className="flex items-center justify-between mb-3">
                            <h3 className="text-sm font-medium">Lessons</h3>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => addLesson(module.id)}
                            >
                              <Plus className="h-3 w-3 mr-1" />
                              Add Lesson
                            </Button>
                          </div>
                          
                          {module.lessons.length > 0 ? (
                            <div>
                              {/* Drag and drop disabled for lessons - @dnd-kit not installed */}
                              <div>
                                {module.lessons.map((lesson) => (
                                  <SortableLesson
                                    key={lesson.id}
                                    lesson={lesson}
                                    onEdit={() => setActiveLessonId(lesson.id)}
                                    onDelete={() => deleteLesson(module.id, lesson.id)}
                                    onDuplicate={() => duplicateLesson(module.id, lesson.id)}
                                  />
                                ))}
                              </div>
                            </div>
                          ) : (
                            <div className="text-center py-8 text-muted-foreground bg-muted/30 rounded-lg">
                              No lessons yet. Add your first lesson to get started.
                            </div>
                          )}
                        </div>
                      </SortableModule>
                    ))}
                  </div>
                </div>
              ) : (
                <Card className="border-dashed">
                  <CardContent className="flex flex-col items-center justify-center py-12">
                    <Package className="h-12 w-12 text-muted-foreground mb-4" />
                    <p className="text-muted-foreground mb-4">No modules yet</p>
                    <Button onClick={addModule}>
                      <Plus className="h-4 w-4 mr-2" />
                      Add Your First Module
                    </Button>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Right Sidebar - Properties Panel */}
      <div className="w-80 border-l bg-muted/10">
        <ScrollArea className="h-full">
          <div className="p-4">
            <h3 className="font-semibold mb-4">Course Properties</h3>
            
            <Tabs value="metadata" className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="metadata">Metadata</TabsTrigger>
                <TabsTrigger value="settings">Settings</TabsTrigger>
              </TabsList>
              
              <TabsContent value="metadata" className="space-y-4">
                <div>
                  <Label>Learning Objectives</Label>
                  <Textarea
                    placeholder="Enter learning objectives (one per line)"
                    value={course.metadata?.objectives.join('\n')}
                    onChange={(e) => setCourse((prev) => ({
                      ...prev,
                      metadata: {
                        ...prev.metadata!,
                        objectives: e.target.value.split('\n').filter(Boolean)
                      }
                    }))}
                    className="min-h-[100px]"
                  />
                </div>
                
                <div>
                  <Label>Prerequisites</Label>
                  <Textarea
                    placeholder="Enter prerequisites (one per line)"
                    value={course.metadata?.prerequisites.join('\n')}
                    onChange={(e) => setCourse((prev) => ({
                      ...prev,
                      metadata: {
                        ...prev.metadata!,
                        prerequisites: e.target.value.split('\n').filter(Boolean)
                      }
                    }))}
                    className="min-h-[80px]"
                  />
                </div>
                
                <div>
                  <Label>Target Audience</Label>
                  <Textarea
                    placeholder="Describe your target audience"
                    value={course.metadata?.targetAudience.join('\n')}
                    onChange={(e) => setCourse((prev) => ({
                      ...prev,
                      metadata: {
                        ...prev.metadata!,
                        targetAudience: e.target.value.split('\n').filter(Boolean)
                      }
                    }))}
                    className="min-h-[80px]"
                  />
                </div>
                
                <div>
                  <Label>Keywords/Tags</Label>
                  <Input
                    placeholder="Enter tags separated by commas"
                    value={course.tags?.join(', ')}
                    onChange={(e) => setCourse((prev) => ({
                      ...prev,
                      tags: e.target.value.split(',').map(t => t.trim()).filter(Boolean)
                    }))}
                  />
                </div>
              </TabsContent>
              
              <TabsContent value="settings" className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="certificate">Certificate Available</Label>
                  <Switch
                    id="certificate"
                    checked={course.metadata?.certificateAvailable}
                    onCheckedChange={(checked) => setCourse((prev) => ({
                      ...prev,
                      metadata: {
                        ...prev.metadata!,
                        certificateAvailable: checked
                      }
                    }))}
                  />
                </div>
                
                <div>
                  <Label>Estimated Hours</Label>
                  <Input
                    type="number"
                    placeholder="0"
                    value={course.metadata?.estimatedHours}
                    onChange={(e) => setCourse((prev) => ({
                      ...prev,
                      metadata: {
                        ...prev.metadata!,
                        estimatedHours: parseInt(e.target.value) || 0
                      }
                    }))}
                  />
                </div>
                
                <Separator />
                
                <div>
                  <h4 className="text-sm font-medium mb-2">Course Statistics</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Total Modules</span>
                      <span>{course.modules?.length || 0}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Total Lessons</span>
                      <span>
                        {course.modules?.reduce((acc, m) => acc + m.lessons.length, 0) || 0}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Total Duration</span>
                      <span>
                        {course.modules?.reduce((acc, m) => acc + m.duration, 0) || 0} min
                      </span>
                    </div>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </ScrollArea>
      </div>
    </div>
  );
};