import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Layout, Target, BookOpen, Users, FileText, Award, 
  Plus, Save, Download, Upload, Lightbulb, Brain,
  ChevronRight, Edit, Trash2, Copy, Move, CheckCircle,
  AlertCircle, Info, Settings, Layers, GitBranch, Clock
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue 
} from '@/components/ui/select';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from '@/components/ui/dialog';
import { <PERSON><PERSON><PERSON><PERSON> } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Progress } from '@/components/ui/progress';
import { useContentCreator } from '../../hooks/useContentCreator';
import { useToast } from '@/components/ui/use-toast';
import * as types from '../../types';

interface LearningObjective {
  id: string;
  level: 'remember' | 'understand' | 'apply' | 'analyze' | 'evaluate' | 'create';
  verb: string;
  description: string;
  measurable: boolean;
  linkedContent: string[];
}

interface CourseBlueprint {
  id: string;
  title: string;
  description: string;
  objectives: LearningObjective[];
  modules: BlueprintModule[];
  assessmentStrategy: AssessmentStrategy;
  deliveryMethod: 'self-paced' | 'instructor-led' | 'blended';
  estimatedDuration: number;
  targetAudience: TargetAudience;
}

interface BlueprintModule {
  id: string;
  title: string;
  objectives: string[];
  topics: string[];
  activities: LearningActivity[];
  duration: number;
  order: number;
}

interface LearningActivity {
  id: string;
  type: 'video' | 'reading' | 'quiz' | 'discussion' | 'project' | 'simulation';
  title: string;
  description: string;
  duration: number;
  resources: string[];
}

interface AssessmentStrategy {
  formative: string[];
  summative: string[];
  frequency: 'per-module' | 'per-topic' | 'end-only';
  passingScore: number;
}

interface TargetAudience {
  level: 'beginner' | 'intermediate' | 'advanced';
  prerequisites: string[];
  roles: string[];
  skills: string[];
  learningStyles: string[];
}

interface Storyboard {
  id: string;
  courseId: string;
  scenes: StoryboardScene[];
  transitions: string[];
  audioNarration: boolean;
  visualStyle: 'minimal' | 'illustrated' | 'realistic' | 'animated';
}

interface StoryboardScene {
  id: string;
  order: number;
  title: string;
  description: string;
  visualDescription: string;
  narrationText: string;
  interactions: string[];
  duration: number;
  assets: string[];
}

export const InstructionalDesignWorkspace: React.FC = () => {
  const { 
    createCourse,
    updateCourse,
    getCourse,
    generateAISuggestions,
    loading,
    error 
  } = useContentCreator();
  const { toast } = useToast();

  // State
  const [activeTab, setActiveTab] = useState('objectives');
  const [courseBlueprint, setCourseBlueprint] = useState<CourseBlueprint | null>(null);
  const [objectives, setObjectives] = useState<LearningObjective[]>([]);
  const [currentObjective, setCurrentObjective] = useState<LearningObjective | null>(null);
  const [showObjectiveDialog, setShowObjectiveDialog] = useState(false);
  const [modules, setModules] = useState<BlueprintModule[]>([]);
  const [currentModule, setCurrentModule] = useState<BlueprintModule | null>(null);
  const [showModuleDialog, setShowModuleDialog] = useState(false);
  const [storyboard, setStoryboard] = useState<Storyboard | null>(null);
  const [currentScene, setCurrentScene] = useState<StoryboardScene | null>(null);
  const [showSceneDialog, setShowSceneDialog] = useState(false);
  const [targetAudience, setTargetAudience] = useState<TargetAudience>({
    level: 'beginner',
    prerequisites: [],
    roles: [],
    skills: [],
    learningStyles: []
  });
  const [aiSuggestions, setAiSuggestions] = useState<string[]>([]);
  const [blueprintProgress, setBlueprintProgress] = useState(0);

  // Bloom's Taxonomy verbs
  const bloomsVerbs = {
    remember: ['identify', 'list', 'name', 'recognize', 'recall', 'describe'],
    understand: ['explain', 'summarize', 'interpret', 'classify', 'compare'],
    apply: ['use', 'implement', 'solve', 'demonstrate', 'operate'],
    analyze: ['analyze', 'differentiate', 'organize', 'attribute', 'deconstruct'],
    evaluate: ['evaluate', 'critique', 'judge', 'justify', 'assess'],
    create: ['create', 'design', 'construct', 'develop', 'formulate']
  };

  // Calculate blueprint progress
  useEffect(() => {
    if (!courseBlueprint) {
      setBlueprintProgress(0);
      return;
    }

    let completed = 0;
    const total = 6;

    if (courseBlueprint.title) completed++;
    if (courseBlueprint.objectives.length > 0) completed++;
    if (courseBlueprint.modules.length > 0) completed++;
    if (courseBlueprint.targetAudience.roles.length > 0) completed++;
    if (courseBlueprint.assessmentStrategy.formative.length > 0) completed++;
    if (courseBlueprint.deliveryMethod) completed++;

    setBlueprintProgress((completed / total) * 100);
  }, [courseBlueprint]);

  // Create or update learning objective
  const handleSaveObjective = () => {
    if (!currentObjective) return;

    if (currentObjective.id) {
      // Update existing
      setObjectives(prev => prev.map(obj => 
        obj.id === currentObjective.id ? currentObjective : obj
      ));
    } else {
      // Add new
      const newObjective = { ...currentObjective, id: `obj-${Date.now()}` };
      setObjectives(prev => [...prev, newObjective]);
    }

    toast({
      title: "Objective Saved",
      description: "Learning objective has been saved successfully",
    });

    setShowObjectiveDialog(false);
    setCurrentObjective(null);
  };

  // Delete learning objective
  const handleDeleteObjective = (id: string) => {
    setObjectives(prev => prev.filter(obj => obj.id !== id));
    toast({
      title: "Objective Deleted",
      description: "Learning objective has been removed",
    });
  };

  // Create or update module
  const handleSaveModule = () => {
    if (!currentModule) return;

    if (currentModule.id) {
      // Update existing
      setModules(prev => prev.map(mod => 
        mod.id === currentModule.id ? currentModule : mod
      ));
    } else {
      // Add new
      const newModule = { 
        ...currentModule, 
        id: `mod-${Date.now()}`,
        order: modules.length + 1
      };
      setModules(prev => [...prev, newModule]);
    }

    toast({
      title: "Module Saved",
      description: "Course module has been saved successfully",
    });

    setShowModuleDialog(false);
    setCurrentModule(null);
  };

  // Create or update storyboard scene
  const handleSaveScene = () => {
    if (!currentScene) return;

    if (!storyboard) {
      // Create new storyboard
      const newStoryboard: Storyboard = {
        id: `story-${Date.now()}`,
        courseId: courseBlueprint?.id || '',
        scenes: [{ ...currentScene, id: `scene-${Date.now()}`, order: 1 }],
        transitions: [],
        audioNarration: true,
        visualStyle: 'illustrated'
      };
      setStoryboard(newStoryboard);
    } else {
      // Update existing storyboard
      if (currentScene.id) {
        // Update existing scene
        setStoryboard({
          ...storyboard,
          scenes: storyboard.scenes.map(scene => 
            scene.id === currentScene.id ? currentScene : scene
          )
        });
      } else {
        // Add new scene
        const newScene = { 
          ...currentScene, 
          id: `scene-${Date.now()}`,
          order: storyboard.scenes.length + 1
        };
        setStoryboard({
          ...storyboard,
          scenes: [...storyboard.scenes, newScene]
        });
      }
    }

    toast({
      title: "Scene Saved",
      description: "Storyboard scene has been saved successfully",
    });

    setShowSceneDialog(false);
    setCurrentScene(null);
  };

  // Generate AI suggestions
  const handleGenerateAISuggestions = async () => {
    if (!courseBlueprint?.id) return;

    try {
      const suggestions = await generateAISuggestions(courseBlueprint.id);
      setAiSuggestions(suggestions.map(s => s.suggestion));
      toast({
        title: "AI Suggestions Generated",
        description: "AI has provided suggestions for your course design",
      });
    } catch (err) {
      toast({
        title: "Generation Failed",
        description: "Could not generate AI suggestions",
        variant: "destructive"
      });
    }
  };

  // Save blueprint to backend
  const handleSaveBlueprint = async () => {
    if (!courseBlueprint) return;

    try {
      const courseData: types.CreateCourseRequest = {
        title: courseBlueprint.title,
        description: courseBlueprint.description,
        category: 'instructional-design',
        difficulty: targetAudience.level === 'beginner' ? 'Beginner' : 
                   targetAudience.level === 'intermediate' ? 'Intermediate' : 'Advanced',
        language: 'en',
        metadata: {
          objectives: objectives.map(o => o.description),
          prerequisites: targetAudience.prerequisites,
          targetAudience: targetAudience.roles,
          outcomes: objectives.filter(o => o.measurable).map(o => o.description),
          keywords: [],
          estimatedHours: courseBlueprint.estimatedDuration / 60,
          certificateAvailable: true,
        }
      };

      const course = await createCourse(courseData);
      
      toast({
        title: "Blueprint Saved",
        description: "Your course blueprint has been saved successfully",
      });

      // Update blueprint with course ID
      setCourseBlueprint({ ...courseBlueprint, id: course.id });
    } catch (err) {
      toast({
        title: "Save Failed",
        description: "Could not save course blueprint",
        variant: "destructive"
      });
    }
  };

  // Export blueprint
  const handleExportBlueprint = () => {
    if (!courseBlueprint) return;

    const exportData = {
      blueprint: courseBlueprint,
      objectives,
      modules,
      storyboard,
      targetAudience,
      exportDate: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `blueprint-${courseBlueprint.title.replace(/\s+/g, '-')}.json`;
    a.click();
    URL.revokeObjectURL(url);

    toast({
      title: "Blueprint Exported",
      description: "Your course blueprint has been exported",
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Instructional Design Workspace</h2>
          <p className="text-muted-foreground mt-1">
            Design effective learning experiences using proven methodologies
          </p>
        </div>
        <div className="flex gap-2">
          <Button 
            variant="outline"
            onClick={handleGenerateAISuggestions}
            disabled={!courseBlueprint}
          >
            <Lightbulb className="h-4 w-4 mr-2" />
            AI Suggestions
          </Button>
          <Button 
            variant="outline"
            onClick={handleExportBlueprint}
            disabled={!courseBlueprint}
          >
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button 
            onClick={handleSaveBlueprint}
            disabled={!courseBlueprint}
          >
            <Save className="h-4 w-4 mr-2" />
            Save Blueprint
          </Button>
        </div>
      </div>

      {/* Progress Bar */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex justify-between items-center">
            <CardTitle className="text-sm">Blueprint Completion</CardTitle>
            <span className="text-sm text-muted-foreground">{Math.round(blueprintProgress)}%</span>
          </div>
        </CardHeader>
        <CardContent>
          <Progress value={blueprintProgress} className="h-2" />
        </CardContent>
      </Card>

      {/* Main Workspace */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="objectives">
            <Target className="h-4 w-4 mr-2" />
            Objectives
          </TabsTrigger>
          <TabsTrigger value="structure">
            <Layout className="h-4 w-4 mr-2" />
            Structure
          </TabsTrigger>
          <TabsTrigger value="audience">
            <Users className="h-4 w-4 mr-2" />
            Audience
          </TabsTrigger>
          <TabsTrigger value="storyboard">
            <FileText className="h-4 w-4 mr-2" />
            Storyboard
          </TabsTrigger>
          <TabsTrigger value="assessment">
            <Award className="h-4 w-4 mr-2" />
            Assessment
          </TabsTrigger>
          <TabsTrigger value="mapping">
            <BookOpen className="h-4 w-4 mr-2" />
            Mapping
          </TabsTrigger>
        </TabsList>

        {/* Learning Objectives Tab */}
        <TabsContent value="objectives" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Learning Objectives</CardTitle>
                  <CardDescription>
                    Define clear, measurable learning outcomes using Bloom's Taxonomy
                  </CardDescription>
                </div>
                <Button onClick={() => {
                  setCurrentObjective({
                    id: '',
                    level: 'remember',
                    verb: '',
                    description: '',
                    measurable: true,
                    linkedContent: []
                  });
                  setShowObjectiveDialog(true);
                }}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Objective
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {objectives.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    No objectives defined yet. Start by adding your first learning objective.
                  </div>
                ) : (
                  objectives.map((objective) => (
                    <div key={objective.id} className="border rounded-lg p-4 space-y-2">
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <Badge variant={objective.measurable ? "default" : "secondary"}>
                              {objective.level}
                            </Badge>
                            <Badge variant="outline">{objective.verb}</Badge>
                            {objective.measurable && (
                              <CheckCircle className="h-4 w-4 text-green-500" />
                            )}
                          </div>
                          <p className="text-sm">{objective.description}</p>
                        </div>
                        <div className="flex gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setCurrentObjective(objective);
                              setShowObjectiveDialog(true);
                            }}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteObjective(objective.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Course Structure Tab */}
        <TabsContent value="structure" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Course Structure</CardTitle>
                  <CardDescription>
                    Organize your content into logical modules and lessons
                  </CardDescription>
                </div>
                <Button onClick={() => {
                  setCurrentModule({
                    id: '',
                    title: '',
                    objectives: [],
                    topics: [],
                    activities: [],
                    duration: 60,
                    order: modules.length + 1
                  });
                  setShowModuleDialog(true);
                }}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Module
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {modules.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    No modules defined yet. Start structuring your course.
                  </div>
                ) : (
                  modules.sort((a, b) => a.order - b.order).map((module) => (
                    <div key={module.id} className="border rounded-lg p-4">
                      <div className="flex justify-between items-start mb-3">
                        <div>
                          <h4 className="font-semibold flex items-center gap-2">
                            <Badge variant="outline">Module {module.order}</Badge>
                            {module.title}
                          </h4>
                          <p className="text-sm text-muted-foreground mt-1">
                            {module.duration} minutes • {module.activities.length} activities
                          </p>
                        </div>
                        <div className="flex gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setCurrentModule(module);
                              setShowModuleDialog(true);
                            }}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <div className="text-sm">
                          <strong>Topics:</strong> {module.topics.join(', ') || 'None defined'}
                        </div>
                        <div className="flex flex-wrap gap-2 mt-2">
                          {module.activities.map((activity) => (
                            <Badge key={activity.id} variant="secondary">
                              {activity.type}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Target Audience Tab */}
        <TabsContent value="audience" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Target Audience Analysis</CardTitle>
              <CardDescription>
                Define and understand your learners
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium">Experience Level</label>
                <Select 
                  value={targetAudience.level} 
                  onValueChange={(value: any) => setTargetAudience({...targetAudience, level: value})}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="beginner">Beginner</SelectItem>
                    <SelectItem value="intermediate">Intermediate</SelectItem>
                    <SelectItem value="advanced">Advanced</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium">Prerequisites</label>
                <Textarea
                  placeholder="List prerequisites, one per line"
                  value={targetAudience.prerequisites.join('\n')}
                  onChange={(e) => setTargetAudience({
                    ...targetAudience,
                    prerequisites: e.target.value.split('\n').filter(p => p.trim())
                  })}
                  rows={3}
                />
              </div>

              <div>
                <label className="text-sm font-medium">Target Roles</label>
                <Textarea
                  placeholder="List target roles, one per line"
                  value={targetAudience.roles.join('\n')}
                  onChange={(e) => setTargetAudience({
                    ...targetAudience,
                    roles: e.target.value.split('\n').filter(r => r.trim())
                  })}
                  rows={3}
                />
              </div>

              <div>
                <label className="text-sm font-medium">Required Skills</label>
                <Textarea
                  placeholder="List required skills, one per line"
                  value={targetAudience.skills.join('\n')}
                  onChange={(e) => setTargetAudience({
                    ...targetAudience,
                    skills: e.target.value.split('\n').filter(s => s.trim())
                  })}
                  rows={3}
                />
              </div>

              <div>
                <label className="text-sm font-medium">Learning Styles to Address</label>
                <div className="grid grid-cols-2 gap-2 mt-2">
                  {['Visual', 'Auditory', 'Reading/Writing', 'Kinesthetic'].map((style) => (
                    <label key={style} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={targetAudience.learningStyles.includes(style)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setTargetAudience({
                              ...targetAudience,
                              learningStyles: [...targetAudience.learningStyles, style]
                            });
                          } else {
                            setTargetAudience({
                              ...targetAudience,
                              learningStyles: targetAudience.learningStyles.filter(s => s !== style)
                            });
                          }
                        }}
                      />
                      <span className="text-sm">{style}</span>
                    </label>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Storyboard Tab */}
        <TabsContent value="storyboard" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Course Storyboard</CardTitle>
                  <CardDescription>
                    Create visual narratives for your learning content
                  </CardDescription>
                </div>
                <Button onClick={() => {
                  setCurrentScene({
                    id: '',
                    order: storyboard?.scenes.length ? storyboard.scenes.length + 1 : 1,
                    title: '',
                    description: '',
                    visualDescription: '',
                    narrationText: '',
                    interactions: [],
                    duration: 60,
                    assets: []
                  });
                  setShowSceneDialog(true);
                }}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Scene
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {!storyboard || storyboard.scenes.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  No storyboard scenes created yet. Start visualizing your course.
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="flex gap-4 mb-4">
                    <div>
                      <label className="text-sm font-medium">Visual Style</label>
                      <Select 
                        value={storyboard.visualStyle}
                        onValueChange={(value: any) => setStoryboard({...storyboard, visualStyle: value})}
                      >
                        <SelectTrigger className="w-[150px]">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="minimal">Minimal</SelectItem>
                          <SelectItem value="illustrated">Illustrated</SelectItem>
                          <SelectItem value="realistic">Realistic</SelectItem>
                          <SelectItem value="animated">Animated</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={storyboard.audioNarration}
                        onChange={(e) => setStoryboard({...storyboard, audioNarration: e.target.checked})}
                      />
                      <label className="text-sm">Include Audio Narration</label>
                    </div>
                  </div>

                  <ScrollArea className="h-[400px]">
                    <div className="space-y-3">
                      {storyboard.scenes.sort((a, b) => a.order - b.order).map((scene) => (
                        <div key={scene.id} className="border rounded-lg p-4">
                          <div className="flex justify-between items-start mb-2">
                            <div className="flex items-center gap-2">
                              <Badge>Scene {scene.order}</Badge>
                              <h4 className="font-semibold">{scene.title}</h4>
                            </div>
                            <div className="flex gap-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  setCurrentScene(scene);
                                  setShowSceneDialog(true);
                                }}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                          <p className="text-sm text-muted-foreground mb-2">{scene.description}</p>
                          <div className="text-sm space-y-1">
                            <div><strong>Visual:</strong> {scene.visualDescription}</div>
                            <div><strong>Duration:</strong> {scene.duration} seconds</div>
                            {scene.interactions.length > 0 && (
                              <div><strong>Interactions:</strong> {scene.interactions.join(', ')}</div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Assessment Strategy Tab */}
        <TabsContent value="assessment" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Assessment Strategy</CardTitle>
              <CardDescription>
                Design formative and summative assessments
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-semibold mb-2">Formative Assessments</h4>
                    <div className="space-y-2">
                      {['Knowledge Checks', 'Practice Exercises', 'Discussions', 'Peer Reviews'].map((type) => (
                        <label key={type} className="flex items-center space-x-2">
                          <input type="checkbox" />
                          <span className="text-sm">{type}</span>
                        </label>
                      ))}
                    </div>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">Summative Assessments</h4>
                    <div className="space-y-2">
                      {['Final Exam', 'Project', 'Portfolio', 'Presentation'].map((type) => (
                        <label key={type} className="flex items-center space-x-2">
                          <input type="checkbox" />
                          <span className="text-sm">{type}</span>
                        </label>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Content Mapping Tab */}
        <TabsContent value="mapping" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Content Mapping</CardTitle>
              <CardDescription>
                Map content to learning objectives and outcomes
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                Content mapping visualization will be displayed here
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Learning Objective Dialog */}
      <Dialog open={showObjectiveDialog} onOpenChange={setShowObjectiveDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              {currentObjective?.id ? 'Edit' : 'Create'} Learning Objective
            </DialogTitle>
            <DialogDescription>
              Define a clear, measurable learning objective using Bloom's Taxonomy
            </DialogDescription>
          </DialogHeader>
          {currentObjective && (
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">Cognitive Level</label>
                <Select 
                  value={currentObjective.level}
                  onValueChange={(value: any) => setCurrentObjective({...currentObjective, level: value})}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.keys(bloomsVerbs).map((level) => (
                      <SelectItem key={level} value={level}>
                        {level.charAt(0).toUpperCase() + level.slice(1)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium">Action Verb</label>
                <Select 
                  value={currentObjective.verb}
                  onValueChange={(value) => setCurrentObjective({...currentObjective, verb: value})}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a verb" />
                  </SelectTrigger>
                  <SelectContent>
                    {bloomsVerbs[currentObjective.level].map((verb) => (
                      <SelectItem key={verb} value={verb}>
                        {verb.charAt(0).toUpperCase() + verb.slice(1)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium">Objective Description</label>
                <Textarea
                  placeholder="Complete the objective statement..."
                  value={currentObjective.description}
                  onChange={(e) => setCurrentObjective({...currentObjective, description: e.target.value})}
                  rows={3}
                />
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={currentObjective.measurable}
                  onChange={(e) => setCurrentObjective({...currentObjective, measurable: e.target.checked})}
                />
                <label className="text-sm">This objective is measurable</label>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowObjectiveDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleSaveObjective}>
              Save Objective
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Module Dialog */}
      <Dialog open={showModuleDialog} onOpenChange={setShowModuleDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              {currentModule?.id ? 'Edit' : 'Create'} Module
            </DialogTitle>
            <DialogDescription>
              Define module structure and content
            </DialogDescription>
          </DialogHeader>
          {currentModule && (
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">Module Title</label>
                <Input
                  value={currentModule.title}
                  onChange={(e) => setCurrentModule({...currentModule, title: e.target.value})}
                  placeholder="Enter module title"
                />
              </div>

              <div>
                <label className="text-sm font-medium">Topics (comma-separated)</label>
                <Input
                  value={currentModule.topics.join(', ')}
                  onChange={(e) => setCurrentModule({
                    ...currentModule,
                    topics: e.target.value.split(',').map(t => t.trim()).filter(t => t)
                  })}
                  placeholder="Topic 1, Topic 2, Topic 3"
                />
              </div>

              <div>
                <label className="text-sm font-medium">Duration (minutes)</label>
                <Input
                  type="number"
                  value={currentModule.duration}
                  onChange={(e) => setCurrentModule({...currentModule, duration: parseInt(e.target.value) || 0})}
                />
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowModuleDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleSaveModule}>
              Save Module
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Scene Dialog */}
      <Dialog open={showSceneDialog} onOpenChange={setShowSceneDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              {currentScene?.id ? 'Edit' : 'Create'} Storyboard Scene
            </DialogTitle>
            <DialogDescription>
              Design a scene for your course storyboard
            </DialogDescription>
          </DialogHeader>
          {currentScene && (
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">Scene Title</label>
                <Input
                  value={currentScene.title}
                  onChange={(e) => setCurrentScene({...currentScene, title: e.target.value})}
                  placeholder="Enter scene title"
                />
              </div>

              <div>
                <label className="text-sm font-medium">Description</label>
                <Textarea
                  value={currentScene.description}
                  onChange={(e) => setCurrentScene({...currentScene, description: e.target.value})}
                  placeholder="Describe what happens in this scene"
                  rows={2}
                />
              </div>

              <div>
                <label className="text-sm font-medium">Visual Description</label>
                <Textarea
                  value={currentScene.visualDescription}
                  onChange={(e) => setCurrentScene({...currentScene, visualDescription: e.target.value})}
                  placeholder="Describe the visual elements"
                  rows={2}
                />
              </div>

              <div>
                <label className="text-sm font-medium">Narration Text</label>
                <Textarea
                  value={currentScene.narrationText}
                  onChange={(e) => setCurrentScene({...currentScene, narrationText: e.target.value})}
                  placeholder="Enter narration or voiceover text"
                  rows={3}
                />
              </div>

              <div>
                <label className="text-sm font-medium">Duration (seconds)</label>
                <Input
                  type="number"
                  value={currentScene.duration}
                  onChange={(e) => setCurrentScene({...currentScene, duration: parseInt(e.target.value) || 0})}
                />
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowSceneDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleSaveScene}>
              Save Scene
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};