import React, { useState, useEffect, useRef, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  FolderOpen, Upload, Image, Video, FileText, Music, Download, Search,
  Trash2, Edit2, Eye, Copy, Share2, Link, Filter, Grid, List,
  Folder, FileVideo, FileImage, FileAudio, File, MoreVertical,
  X, Check, AlertCircle, Cloud, HardDrive, Clock, Tag, Info,
  Play, Pause, ZoomIn, ZoomOut, RotateCw, ChevronLeft, ChevronRight
} from 'lucide-react';
import { useContentCreator } from '../../hooks/useContentCreator';
import { MediaAsset, MediaType } from '../../types';

interface UploadProgress {
  file: File;
  progress: number;
  status: 'pending' | 'uploading' | 'processing' | 'completed' | 'failed';
  error?: string;
}

interface AssetFilters {
  type: MediaType | 'all';
  search: string;
  tags: string[];
  dateRange: { start: Date | null; end: Date | null };
  sizeRange: { min: number; max: number };
}

const AssetPreview: React.FC<{
  asset: MediaAsset;
  onClose: () => void;
}> = ({ asset, onClose }) => {
  const [imageZoom, setImageZoom] = useState(100);
  const [videoPlaying, setVideoPlaying] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const audioRef = useRef<HTMLAudioElement>(null);

  const handleZoomIn = () => setImageZoom(prev => Math.min(prev + 25, 200));
  const handleZoomOut = () => setImageZoom(prev => Math.max(prev - 25, 50));

  const toggleVideoPlay = () => {
    if (videoRef.current) {
      if (videoPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setVideoPlaying(!videoPlaying);
    }
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh]">
        <DialogHeader>
          <DialogTitle>{asset.title}</DialogTitle>
          <DialogDescription>
            {asset.type} • {formatFileSize(asset.size)} • Uploaded {new Date(asset.uploadedAt).toLocaleDateString()}
          </DialogDescription>
        </DialogHeader>
        
        <div className="relative bg-muted rounded-lg overflow-hidden" style={{ height: '500px' }}>
          {asset.type === MediaType.IMAGE && (
            <>
              <div className="absolute top-2 right-2 z-10 flex gap-2">
                <Button size="sm" variant="secondary" onClick={handleZoomOut}>
                  <ZoomOut className="h-4 w-4" />
                </Button>
                <Button size="sm" variant="secondary" onClick={handleZoomIn}>
                  <ZoomIn className="h-4 w-4" />
                </Button>
              </div>
              <div className="w-full h-full flex items-center justify-center overflow-auto">
                <img 
                  src={asset.url} 
                  alt={asset.alt || asset.title}
                  style={{ 
                    maxWidth: `${imageZoom}%`,
                    maxHeight: `${imageZoom}%`,
                    objectFit: 'contain'
                  }}
                />
              </div>
            </>
          )}

          {asset.type === MediaType.VIDEO && (
            <>
              <video 
                ref={videoRef}
                src={asset.url}
                className="w-full h-full object-contain"
                controls
              />
              <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
                <Button onClick={toggleVideoPlay} variant="secondary">
                  {videoPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                </Button>
              </div>
            </>
          )}

          {asset.type === MediaType.AUDIO && (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <Music className="h-24 w-24 text-muted-foreground mb-4 mx-auto" />
                <audio ref={audioRef} src={asset.url} controls className="w-64" />
              </div>
            </div>
          )}

          {asset.type === MediaType.DOCUMENT && (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <FileText className="h-24 w-24 text-muted-foreground mb-4 mx-auto" />
                <p className="text-muted-foreground mb-4">Document preview not available</p>
                <Button onClick={() => window.open(asset.url, '_blank')}>
                  <Download className="h-4 w-4 mr-2" />
                  Download Document
                </Button>
              </div>
            </div>
          )}
        </div>

        <div className="space-y-4">
          {asset.caption && (
            <div>
              <Label>Caption</Label>
              <p className="text-sm text-muted-foreground">{asset.caption}</p>
            </div>
          )}

          {asset.credits && (
            <div>
              <Label>Credits</Label>
              <p className="text-sm text-muted-foreground">{asset.credits}</p>
            </div>
          )}

          {asset.dimensions && (
            <div>
              <Label>Dimensions</Label>
              <p className="text-sm text-muted-foreground">
                {asset.dimensions.width} × {asset.dimensions.height} pixels
              </p>
            </div>
          )}

          <div className="flex gap-2">
            <Button variant="outline" className="flex-1" onClick={() => window.open(asset.url, '_blank')}>
              <Download className="h-4 w-4 mr-2" />
              Download
            </Button>
            <Button variant="outline" className="flex-1" onClick={() => navigator.clipboard.writeText(asset.url)}>
              <Link className="h-4 w-4 mr-2" />
              Copy URL
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return Math.round(bytes / Math.pow(k, i) * 100) / 100 + ' ' + sizes[i];
};

const getMediaTypeIcon = (type: MediaType) => {
  switch (type) {
    case MediaType.IMAGE: return FileImage;
    case MediaType.VIDEO: return FileVideo;
    case MediaType.AUDIO: return FileAudio;
    case MediaType.DOCUMENT: return FileText;
    default: return File;
  }
};

export const AssetManager: React.FC = () => {
  const { uploadMedia, getMediaLibrary, loading, error } = useContentCreator();
  const [assets, setAssets] = useState<MediaAsset[]>([]);
  const [selectedAssets, setSelectedAssets] = useState<Set<string>>(new Set());
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [previewAsset, setPreviewAsset] = useState<MediaAsset | null>(null);
  const [uploadQueue, setUploadQueue] = useState<UploadProgress[]>([]);
  const [showUploadDialog, setShowUploadDialog] = useState(false);
  const [filters, setFilters] = useState<AssetFilters>({
    type: 'all',
    search: '',
    tags: [],
    dateRange: { start: null, end: null },
    sizeRange: { min: 0, max: Infinity }
  });

  const fileInputRef = useRef<HTMLInputElement>(null);
  const dragCounter = useRef(0);
  const [isDragging, setIsDragging] = useState(false);

  // Load assets on mount
  useEffect(() => {
    loadAssets();
  }, []);

  const loadAssets = async () => {
    try {
      const mediaAssets = await getMediaLibrary();
      setAssets(mediaAssets);
    } catch (err) {
    }
  };

  // Filter assets based on current filters
  const filteredAssets = useMemo(() => {
    return assets.filter(asset => {
      if (filters.type !== 'all' && asset.type !== filters.type) return false;
      if (filters.search && !asset.title.toLowerCase().includes(filters.search.toLowerCase())) return false;
      if (filters.dateRange.start && new Date(asset.uploadedAt) < filters.dateRange.start) return false;
      if (filters.dateRange.end && new Date(asset.uploadedAt) > filters.dateRange.end) return false;
      if (asset.size < filters.sizeRange.min || asset.size > filters.sizeRange.max) return false;
      return true;
    });
  }, [assets, filters]);

  // Group assets by type for statistics
  const assetStats = useMemo(() => {
    const stats = {
      total: assets.length,
      totalSize: assets.reduce((sum, a) => sum + a.size, 0),
      byType: {
        [MediaType.IMAGE]: 0,
        [MediaType.VIDEO]: 0,
        [MediaType.AUDIO]: 0,
        [MediaType.DOCUMENT]: 0,
        [MediaType.ANIMATION]: 0,
        [MediaType.INTERACTIVE]: 0
      }
    };

    assets.forEach(asset => {
      if (asset.type in stats.byType) {
        stats.byType[asset.type]++;
      }
    });

    return stats;
  }, [assets]);

  const handleFileSelect = (files: FileList | null) => {
    if (!files) return;

    const newUploads: UploadProgress[] = Array.from(files).map(file => ({
      file,
      progress: 0,
      status: 'pending' as const
    }));

    setUploadQueue(prev => [...prev, ...newUploads]);
    setShowUploadDialog(true);
    uploadFiles(newUploads);
  };

  const uploadFiles = async (uploads: UploadProgress[]) => {
    for (const upload of uploads) {
      try {
        setUploadQueue(prev => 
          prev.map(u => u.file === upload.file ? { ...u, status: 'uploading', progress: 10 } : u)
        );

        // Simulate progress updates
        const progressInterval = setInterval(() => {
          setUploadQueue(prev => 
            prev.map(u => {
              if (u.file === upload.file && u.status === 'uploading') {
                const newProgress = Math.min(u.progress + 10, 90);
                return { ...u, progress: newProgress };
              }
              return u;
            })
          );
        }, 200);

        const asset = await uploadMedia(upload.file);
        
        clearInterval(progressInterval);

        setUploadQueue(prev => 
          prev.map(u => u.file === upload.file ? { ...u, status: 'completed', progress: 100 } : u)
        );

        setAssets(prev => [...prev, asset]);
      } catch (err) {
        setUploadQueue(prev => 
          prev.map(u => u.file === upload.file ? { 
            ...u, 
            status: 'failed', 
            error: err instanceof Error ? err.message : 'Upload failed' 
          } : u)
        );
      }
    }
  };

  // Drag and drop handlers
  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    dragCounter.current++;
    if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {
      setIsDragging(true);
    }
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    dragCounter.current--;
    if (dragCounter.current === 0) {
      setIsDragging(false);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
    dragCounter.current = 0;

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      handleFileSelect(e.dataTransfer.files);
    }
  };

  const toggleAssetSelection = (assetId: string) => {
    setSelectedAssets(prev => {
      const newSet = new Set(prev);
      if (newSet.has(assetId)) {
        newSet.delete(assetId);
      } else {
        newSet.add(assetId);
      }
      return newSet;
    });
  };

  const selectAllAssets = () => {
    if (selectedAssets.size === filteredAssets.length) {
      setSelectedAssets(new Set());
    } else {
      setSelectedAssets(new Set(filteredAssets.map(a => a.id)));
    }
  };

  const deleteSelectedAssets = () => {
    // In a real implementation, this would call a delete API
    setAssets(prev => prev.filter(a => !selectedAssets.has(a.id)));
    setSelectedAssets(new Set());
  };

  const downloadSelectedAssets = () => {
    selectedAssets.forEach(assetId => {
      const asset = assets.find(a => a.id === assetId);
      if (asset) {
        window.open(asset.url, '_blank');
      }
    });
  };

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Failed to load media assets. Please try again later.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div 
      className="space-y-6"
      onDragEnter={handleDragEnter}
      onDragLeave={handleDragLeave}
      onDragOver={handleDragOver}
      onDrop={handleDrop}
    >
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Asset Manager</h2>
          <p className="text-muted-foreground mt-1">
            Manage and organize your media files and resources
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="icon"
            onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
          >
            {viewMode === 'grid' ? <List className="h-4 w-4" /> : <Grid className="h-4 w-4" />}
          </Button>
          <Button onClick={() => fileInputRef.current?.click()}>
            <Upload className="h-4 w-4 mr-2" />
            Upload Files
          </Button>
          <input
            ref={fileInputRef}
            type="file"
            multiple
            onChange={(e) => handleFileSelect(e.target.files)}
            className="hidden"
          />
        </div>
      </div>

      {/* Statistics Bar */}
      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">{assetStats.total}</div>
            <p className="text-xs text-muted-foreground">Total Assets</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">{formatFileSize(assetStats.totalSize)}</div>
            <p className="text-xs text-muted-foreground">Total Size</p>
          </CardContent>
        </Card>
        {Object.entries(assetStats.byType).map(([type, count]) => (
          <Card key={type}>
            <CardContent className="p-4">
              <div className="text-2xl font-bold">{count}</div>
              <p className="text-xs text-muted-foreground capitalize">{type}s</p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Filters */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-base">Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-4">
            <div className="relative flex-1 min-w-[200px]">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input 
                placeholder="Search assets..." 
                className="pl-8"
                value={filters.search}
                onChange={(e) => setFilters({ ...filters, search: e.target.value })}
              />
            </div>
            <Select 
              value={filters.type}
              onValueChange={(value: MediaType | 'all') => setFilters({ ...filters, type: value })}
            >
              <SelectTrigger className="w-[150px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value={MediaType.IMAGE}>Images</SelectItem>
                <SelectItem value={MediaType.VIDEO}>Videos</SelectItem>
                <SelectItem value={MediaType.AUDIO}>Audio</SelectItem>
                <SelectItem value={MediaType.DOCUMENT}>Documents</SelectItem>
              </SelectContent>
            </Select>
            {selectedAssets.size > 0 && (
              <div className="flex items-center gap-2">
                <Badge variant="secondary">
                  {selectedAssets.size} selected
                </Badge>
                <Button variant="outline" size="sm" onClick={downloadSelectedAssets}>
                  <Download className="h-4 w-4 mr-1" />
                  Download
                </Button>
                <Button variant="outline" size="sm" onClick={deleteSelectedAssets}>
                  <Trash2 className="h-4 w-4 mr-1" />
                  Delete
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Assets Grid/List */}
      {loading ? (
        <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4' : 'space-y-2'}>
          {[...Array(8)].map((_, i) => (
            <Skeleton key={i} className="h-48" />
          ))}
        </div>
      ) : filteredAssets.length === 0 ? (
        <Card>
          <CardContent className="py-12">
            <div className="text-center text-muted-foreground">
              <Upload className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No assets found. Upload files to get started.</p>
            </div>
          </CardContent>
        </Card>
      ) : viewMode === 'grid' ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {filteredAssets.map((asset) => {
            const Icon = getMediaTypeIcon(asset.type);
            const isSelected = selectedAssets.has(asset.id);
            
            return (
              <Card 
                key={asset.id} 
                className={`hover:shadow-lg transition-shadow cursor-pointer ${isSelected ? 'ring-2 ring-primary' : ''}`}
              >
                <div 
                  className="relative aspect-video bg-muted flex items-center justify-center overflow-hidden"
                  onClick={() => setPreviewAsset(asset)}
                >
                  {asset.type === MediaType.IMAGE && asset.thumbnail ? (
                    <img src={asset.thumbnail} alt={asset.title} className="object-cover w-full h-full" />
                  ) : (
                    <Icon className="h-12 w-12 text-muted-foreground" />
                  )}
                  <div className="absolute top-2 left-2">
                    <Checkbox 
                      checked={isSelected}
                      onCheckedChange={() => toggleAssetSelection(asset.id)}
                      onClick={(e) => e.stopPropagation()}
                    />
                  </div>
                  {asset.processingStatus === 'processing' && (
                    <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
                      <div className="text-white text-sm">Processing...</div>
                    </div>
                  )}
                </div>
                <CardHeader className="p-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-sm truncate">{asset.title}</CardTitle>
                    <Badge variant="secondary" className="text-xs">
                      {asset.type}
                    </Badge>
                  </div>
                  <CardDescription className="text-xs">
                    {formatFileSize(asset.size)} • {new Date(asset.uploadedAt).toLocaleDateString()}
                  </CardDescription>
                </CardHeader>
              </Card>
            );
          })}
        </div>
      ) : (
        <div className="space-y-2">
          <div className="flex items-center gap-2 p-2 border-b">
            <Checkbox 
              checked={selectedAssets.size === filteredAssets.length && filteredAssets.length > 0}
              onCheckedChange={selectAllAssets}
            />
            <span className="text-sm font-medium">Select All</span>
          </div>
          {filteredAssets.map((asset) => {
            const Icon = getMediaTypeIcon(asset.type);
            const isSelected = selectedAssets.has(asset.id);
            
            return (
              <Card key={asset.id} className={isSelected ? 'ring-2 ring-primary' : ''}>
                <CardContent className="p-3">
                  <div className="flex items-center gap-3">
                    <Checkbox 
                      checked={isSelected}
                      onCheckedChange={() => toggleAssetSelection(asset.id)}
                    />
                    <Icon className="h-8 w-8 text-muted-foreground flex-shrink-0" />
                    <div className="flex-1 min-w-0">
                      <p className="font-medium truncate">{asset.title}</p>
                      <p className="text-sm text-muted-foreground">
                        {asset.type} • {formatFileSize(asset.size)} • {new Date(asset.uploadedAt).toLocaleDateString()}
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button variant="ghost" size="sm" onClick={() => setPreviewAsset(asset)}>
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm" onClick={() => window.open(asset.url, '_blank')}>
                        <Download className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}

      {/* Upload Dialog */}
      <Dialog open={showUploadDialog} onOpenChange={setShowUploadDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Uploading Files</DialogTitle>
            <DialogDescription>
              {uploadQueue.filter(u => u.status === 'completed').length} of {uploadQueue.length} files uploaded
            </DialogDescription>
          </DialogHeader>
          <ScrollArea className="max-h-[400px]">
            <div className="space-y-3">
              {uploadQueue.map((upload, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium truncate">{upload.file.name}</span>
                    <Badge variant={
                      upload.status === 'completed' ? 'default' :
                      upload.status === 'failed' ? 'destructive' :
                      'secondary'
                    }>
                      {upload.status}
                    </Badge>
                  </div>
                  {upload.status === 'uploading' && (
                    <Progress value={upload.progress} />
                  )}
                  {upload.error && (
                    <p className="text-xs text-destructive">{upload.error}</p>
                  )}
                </div>
              ))}
            </div>
          </ScrollArea>
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => {
                setShowUploadDialog(false);
                setUploadQueue([]);
              }}
            >
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Preview Dialog */}
      {previewAsset && (
        <AssetPreview 
          asset={previewAsset} 
          onClose={() => setPreviewAsset(null)}
        />
      )}

      {/* Drag Overlay */}
      {isDragging && (
        <div className="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm">
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <Upload className="h-16 w-16 mx-auto mb-4 text-primary" />
              <p className="text-xl font-semibold">Drop files here to upload</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};