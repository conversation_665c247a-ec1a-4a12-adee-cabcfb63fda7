import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  CheckCircle, Clock, AlertCircle, MessageSquare, Send,
  User, Calendar, FileText, ChevronRight, Eye, Edit,
  CheckSquare, XCircle, RotateCcw, Filter, Search,
  Users, GitBranch, History, Bell, Archive, Flag
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Progress } from '@/components/ui/progress';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';
import { useContentCreator } from '../../hooks/useContentCreator';
import { useToast } from '@/components/ui/use-toast';
import * as types from '../../types';

interface ReviewWorkflowProps {
  mini?: boolean;
}

interface ExtendedReview extends types.ContentReview {
  contentTitle: string;
  submittedBy: string;
  submittedAt: Date;
  deadline?: Date;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  version: number;
  changesSummary?: string;
  attachments?: string[];
}

interface ReviewComment extends types.ReviewComment {
  authorName: string;
  authorAvatar?: string;
  edited?: boolean;
  editedAt?: Date;
}

interface ReviewStats {
  total: number;
  pending: number;
  approved: number;
  rejected: number;
  needsChanges: number;
  avgReviewTime: number;
  overdue: number;
}

export const ReviewWorkflow: React.FC<ReviewWorkflowProps> = ({ mini = false }) => {
  const { 
    getContentReviews,
    submitContentReview,
    getCourse,
    getCourses,
    loading,
    error 
  } = useContentCreator();
  const { toast } = useToast();

  // State
  const [reviews, setReviews] = useState<ExtendedReview[]>([]);
  const [filteredReviews, setFilteredReviews] = useState<ExtendedReview[]>([]);
  const [selectedReview, setSelectedReview] = useState<ExtendedReview | null>(null);
  const [activeTab, setActiveTab] = useState('pending');
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState<types.ReviewStatus | 'all'>('all');
  const [filterPriority, setFilterPriority] = useState<string>('all');
  const [showReviewDialog, setShowReviewDialog] = useState(false);
  const [showCommentDialog, setShowCommentDialog] = useState(false);
  const [commentText, setCommentText] = useState('');
  const [reviewDecision, setReviewDecision] = useState<types.ReviewStatus>(types.ReviewStatus.PENDING);
  const [requestedChanges, setRequestedChanges] = useState<string[]>([]);
  const [currentChangeRequest, setCurrentChangeRequest] = useState('');
  const [stats, setStats] = useState<ReviewStats>({
    total: 0,
    pending: 0,
    approved: 0,
    rejected: 0,
    needsChanges: 0,
    avgReviewTime: 0,
    overdue: 0
  });

  // Load reviews on mount
  useEffect(() => {
    loadReviews();
  }, []);

  // Filter reviews based on search and filters
  useEffect(() => {
    let filtered = [...reviews];

    // Apply search
    if (searchQuery) {
      filtered = filtered.filter(review => 
        review.contentTitle.toLowerCase().includes(searchQuery.toLowerCase()) ||
        review.submittedBy.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply status filter
    if (filterStatus !== 'all') {
      filtered = filtered.filter(review => review.status === filterStatus);
    }

    // Apply priority filter
    if (filterPriority !== 'all') {
      filtered = filtered.filter(review => review.priority === filterPriority);
    }

    // Apply tab filter
    switch (activeTab) {
      case 'pending':
        filtered = filtered.filter(r => r.status === types.ReviewStatus.PENDING);
        break;
      case 'in-progress':
        filtered = filtered.filter(r => r.status === types.ReviewStatus.IN_PROGRESS);
        break;
      case 'completed':
        filtered = filtered.filter(r => 
          r.status === types.ReviewStatus.APPROVED || 
          r.status === types.ReviewStatus.REJECTED
        );
        break;
      case 'needs-changes':
        filtered = filtered.filter(r => r.status === types.ReviewStatus.NEEDS_CHANGES);
        break;
    }

    setFilteredReviews(filtered);
  }, [reviews, searchQuery, filterStatus, filterPriority, activeTab]);

  // Calculate stats
  useEffect(() => {
    const newStats: ReviewStats = {
      total: reviews.length,
      pending: reviews.filter(r => r.status === types.ReviewStatus.PENDING).length,
      approved: reviews.filter(r => r.status === types.ReviewStatus.APPROVED).length,
      rejected: reviews.filter(r => r.status === types.ReviewStatus.REJECTED).length,
      needsChanges: reviews.filter(r => r.status === types.ReviewStatus.NEEDS_CHANGES).length,
      avgReviewTime: calculateAvgReviewTime(reviews),
      overdue: reviews.filter(r => r.deadline && new Date(r.deadline) < new Date()).length
    };
    setStats(newStats);
  }, [reviews]);

  const calculateAvgReviewTime = (reviews: ExtendedReview[]): number => {
    const completedReviews = reviews.filter(r => r.approvedAt || r.rejectedAt);
    if (completedReviews.length === 0) return 0;

    const totalTime = completedReviews.reduce((sum, review) => {
      const endDate = review.approvedAt || review.rejectedAt;
      if (!endDate) return sum;
      const startDate = review.submittedAt;
      return sum + (new Date(endDate).getTime() - new Date(startDate).getTime());
    }, 0);

    return Math.round(totalTime / completedReviews.length / (1000 * 60 * 60)); // Convert to hours
  };

  const loadReviews = async () => {
    try {
      // Try to fetch reviews from all available content
      const allReviews: ExtendedReview[] = [];
      
      // First, get all courses to fetch their reviews
      try {
        const coursesResponse = await getCourses();
        
        if (coursesResponse && coursesResponse.items) {
          for (const course of coursesResponse.items) {
            try {
              const reviews = await getContentReviews(course.id);
              
              // Extend reviews with UI properties
              const extendedReviews = reviews.map(review => ({
                ...review,
                contentTitle: course.title,
                submittedBy: review.reviewerId || 'Unknown Reviewer',
                submittedAt: new Date(),
                priority: getPriorityFromStatus(review.status),
                version: 1,
                changesSummary: `Review for ${course.title}`
              } as ExtendedReview));
              
              allReviews.push(...extendedReviews);
            } catch (err) {
            }
          }
        }
      } catch (err) {
      }
      
      // Don't create default reviews - they would fail due to foreign key constraints
      // Reviews should only be created for existing content

      setReviews(allReviews);
    } catch (err) {
      toast({
        title: "Failed to Load Reviews",
        description: "Could not load review items",
        variant: "destructive"
      });
    }
  };

  const getPriorityFromStatus = (status: types.ReviewStatus): 'low' | 'medium' | 'high' | 'urgent' => {
    switch (status) {
      case types.ReviewStatus.NEEDS_CHANGES:
        return 'urgent';
      case types.ReviewStatus.PENDING:
        return 'high';
      case types.ReviewStatus.IN_PROGRESS:
        return 'medium';
      default:
        return 'low';
    }
  };

  const handleSubmitReview = async () => {
    if (!selectedReview) return;

    try {
      const reviewData: types.ContentReview = {
        ...selectedReview,
        status: reviewDecision,
        requestedChanges: reviewDecision === types.ReviewStatus.NEEDS_CHANGES ? requestedChanges : undefined,
        approvedAt: reviewDecision === types.ReviewStatus.APPROVED ? new Date() : undefined,
        rejectedAt: reviewDecision === types.ReviewStatus.REJECTED ? new Date() : undefined,
      };

      await submitContentReview(reviewData);

      // Update local state
      setReviews(prev => prev.map(r => 
        r.id === selectedReview.id ? { ...r, ...reviewData } : r
      ));

      toast({
        title: "Review Submitted",
        description: `Content has been ${reviewDecision.toLowerCase()}`,
      });

      setShowReviewDialog(false);
      setSelectedReview(null);
      setReviewDecision(types.ReviewStatus.PENDING);
      setRequestedChanges([]);
    } catch (err) {
      toast({
        title: "Review Failed",
        description: "Could not submit review decision",
        variant: "destructive"
      });
    }
  };

  const handleAddComment = () => {
    if (!selectedReview || !commentText.trim()) return;

    const newComment: ReviewComment = {
      id: `comment-${Date.now()}`,
      reviewId: selectedReview.id,
      authorId: 'current-user',
      authorName: 'Current Reviewer',
      text: commentText,
      timestamp: new Date(),
      resolved: false
    };

    setReviews(prev => prev.map(r => 
      r.id === selectedReview.id 
        ? { ...r, comments: [...r.comments, newComment] }
        : r
    ));

    setCommentText('');
    setShowCommentDialog(false);

    toast({
      title: "Comment Added",
      description: "Your comment has been added to the review",
    });
  };

  const getStatusIcon = (status: types.ReviewStatus) => {
    switch (status) {
      case types.ReviewStatus.APPROVED:
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case types.ReviewStatus.PENDING:
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case types.ReviewStatus.IN_PROGRESS:
        return <RotateCcw className="h-4 w-4 text-blue-500 animate-spin" />;
      case types.ReviewStatus.NEEDS_CHANGES:
        return <AlertCircle className="h-4 w-4 text-orange-500" />;
      case types.ReviewStatus.REJECTED:
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return null;
    }
  };

  const getStatusBadge = (status: types.ReviewStatus) => {
    const variants: Record<types.ReviewStatus, string> = {
      [types.ReviewStatus.APPROVED]: 'bg-green-100 text-green-800',
      [types.ReviewStatus.PENDING]: 'bg-yellow-100 text-yellow-800',
      [types.ReviewStatus.IN_PROGRESS]: 'bg-blue-100 text-blue-800',
      [types.ReviewStatus.NEEDS_CHANGES]: 'bg-orange-100 text-orange-800',
      [types.ReviewStatus.REJECTED]: 'bg-red-100 text-red-800'
    };

    return (
      <Badge className={variants[status]}>
        {status.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}
      </Badge>
    );
  };

  const getPriorityBadge = (priority: string) => {
    const colors: Record<string, string> = {
      low: 'bg-gray-100 text-gray-800',
      medium: 'bg-blue-100 text-blue-800',
      high: 'bg-orange-100 text-orange-800',
      urgent: 'bg-red-100 text-red-800'
    };

    return (
      <Badge className={colors[priority] || 'bg-gray-100 text-gray-800'}>
        {priority.charAt(0).toUpperCase() + priority.slice(1)}
      </Badge>
    );
  };

  if (mini) {
    return (
      <div className="space-y-2">
        {reviews.slice(0, 3).map((review) => (
          <div 
            key={review.id} 
            className="flex items-center justify-between p-2 rounded hover:bg-muted cursor-pointer"
            onClick={() => {
              setSelectedReview(review);
              setShowReviewDialog(true);
            }}
          >
            <div className="flex items-center gap-2">
              {getStatusIcon(review.status)}
              <div>
                <p className="text-sm font-medium">{review.contentTitle}</p>
                <p className="text-xs text-muted-foreground">{review.submittedBy}</p>
              </div>
            </div>
            {review.priority === 'urgent' && (
              <Flag className="h-3 w-3 text-red-500" />
            )}
          </div>
        ))}
        <Button variant="ghost" size="sm" className="w-full text-xs">
          View All Reviews ({reviews.length})
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Review Workflow</h2>
          <p className="text-muted-foreground mt-1">
            Manage content review and approval processes
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Archive className="h-4 w-4 mr-2" />
            Archive
          </Button>
          <Button variant="outline">
            <Bell className="h-4 w-4 mr-2" />
            Notifications
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Reviews</p>
                <p className="text-2xl font-bold">{stats.total}</p>
              </div>
              <FileText className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Pending</p>
                <p className="text-2xl font-bold">{stats.pending}</p>
              </div>
              <Clock className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Approved</p>
                <p className="text-2xl font-bold">{stats.approved}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Overdue</p>
                <p className="text-2xl font-bold text-red-500">{stats.overdue}</p>
              </div>
              <AlertCircle className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search reviews..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={filterStatus} onValueChange={(value: any) => setFilterStatus(value)}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value={types.ReviewStatus.PENDING}>Pending</SelectItem>
                <SelectItem value={types.ReviewStatus.IN_PROGRESS}>In Progress</SelectItem>
                <SelectItem value={types.ReviewStatus.APPROVED}>Approved</SelectItem>
                <SelectItem value={types.ReviewStatus.NEEDS_CHANGES}>Needs Changes</SelectItem>
                <SelectItem value={types.ReviewStatus.REJECTED}>Rejected</SelectItem>
              </SelectContent>
            </Select>
            <Select value={filterPriority} onValueChange={setFilterPriority}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Priority" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Priority</SelectItem>
                <SelectItem value="low">Low</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="high">High</SelectItem>
                <SelectItem value="urgent">Urgent</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Reviews List */}
      <Card>
        <CardContent className="p-0">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <div className="border-b px-6 pt-6">
              <TabsList className="grid w-full grid-cols-5">
                <TabsTrigger value="all">All</TabsTrigger>
                <TabsTrigger value="pending">Pending</TabsTrigger>
                <TabsTrigger value="in-progress">In Progress</TabsTrigger>
                <TabsTrigger value="needs-changes">Changes</TabsTrigger>
                <TabsTrigger value="completed">Completed</TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value={activeTab} className="p-6">
              <ScrollArea className="h-[400px]">
                {filteredReviews.length === 0 ? (
                  <div className="text-center py-12 text-muted-foreground">
                    No reviews found
                  </div>
                ) : (
                  <div className="space-y-4">
                    {filteredReviews.map((review) => (
                      <div
                        key={review.id}
                        className="border rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
                        onClick={() => {
                          setSelectedReview(review);
                          setShowReviewDialog(true);
                        }}
                      >
                        <div className="flex justify-between items-start mb-3">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <h3 className="font-semibold">{review.contentTitle}</h3>
                              {getStatusBadge(review.status)}
                              {getPriorityBadge(review.priority)}
                              {review.deadline && new Date(review.deadline) < new Date() && (
                                <Badge variant="destructive">Overdue</Badge>
                              )}
                            </div>
                            <p className="text-sm text-muted-foreground mb-2">
                              {review.changesSummary || 'No description provided'}
                            </p>
                            <div className="flex items-center gap-4 text-xs text-muted-foreground">
                              <span className="flex items-center gap-1">
                                <User className="h-3 w-3" />
                                {review.submittedBy}
                              </span>
                              <span className="flex items-center gap-1">
                                <Calendar className="h-3 w-3" />
                                {new Date(review.submittedAt).toLocaleDateString()}
                              </span>
                              <span className="flex items-center gap-1">
                                <GitBranch className="h-3 w-3" />
                                Version {review.version}
                              </span>
                              {review.comments.length > 0 && (
                                <span className="flex items-center gap-1">
                                  <MessageSquare className="h-3 w-3" />
                                  {review.comments.length} comments
                                </span>
                              )}
                            </div>
                          </div>
                          <Button variant="ghost" size="sm">
                            <ChevronRight className="h-4 w-4" />
                          </Button>
                        </div>
                        {review.requestedChanges && review.requestedChanges.length > 0 && (
                          <div className="mt-3 pt-3 border-t">
                            <p className="text-xs font-medium mb-1">Requested Changes:</p>
                            <ul className="text-xs text-muted-foreground space-y-1">
                              {review.requestedChanges.map((change, idx) => (
                                <li key={idx} className="flex items-start gap-1">
                                  <span className="mt-0.5">•</span>
                                  <span>{change}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </ScrollArea>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Review Dialog */}
      <Dialog open={showReviewDialog} onOpenChange={setShowReviewDialog}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Review Content</DialogTitle>
            <DialogDescription>
              Review and approve content submissions
            </DialogDescription>
          </DialogHeader>
          {selectedReview && (
            <div className="space-y-4">
              <div>
                <h3 className="font-semibold mb-2">{selectedReview.contentTitle}</h3>
                <div className="flex gap-2 mb-3">
                  {getStatusBadge(selectedReview.status)}
                  {getPriorityBadge(selectedReview.priority)}
                  <Badge variant="outline">Version {selectedReview.version}</Badge>
                </div>
                <p className="text-sm text-muted-foreground">
                  {selectedReview.changesSummary}
                </p>
              </div>

              <Separator />

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-muted-foreground">Submitted by</p>
                  <p className="font-medium">{selectedReview.submittedBy}</p>
                </div>
                <div>
                  <p className="text-muted-foreground">Submitted on</p>
                  <p className="font-medium">
                    {new Date(selectedReview.submittedAt).toLocaleString()}
                  </p>
                </div>
                {selectedReview.deadline && (
                  <div>
                    <p className="text-muted-foreground">Deadline</p>
                    <p className="font-medium">
                      {new Date(selectedReview.deadline).toLocaleDateString()}
                    </p>
                  </div>
                )}
                <div>
                  <p className="text-muted-foreground">Content Type</p>
                  <p className="font-medium capitalize">{selectedReview.contentType}</p>
                </div>
              </div>

              <Separator />

              <div>
                <div className="flex justify-between items-center mb-3">
                  <h4 className="font-semibold">Review Decision</h4>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowCommentDialog(true)}
                  >
                    <MessageSquare className="h-4 w-4 mr-2" />
                    Add Comment
                  </Button>
                </div>
                <div className="space-y-3">
                  <Select 
                    value={reviewDecision}
                    onValueChange={(value: any) => setReviewDecision(value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={types.ReviewStatus.APPROVED}>
                        <span className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-green-500" />
                          Approve
                        </span>
                      </SelectItem>
                      <SelectItem value={types.ReviewStatus.NEEDS_CHANGES}>
                        <span className="flex items-center gap-2">
                          <AlertCircle className="h-4 w-4 text-orange-500" />
                          Request Changes
                        </span>
                      </SelectItem>
                      <SelectItem value={types.ReviewStatus.REJECTED}>
                        <span className="flex items-center gap-2">
                          <XCircle className="h-4 w-4 text-red-500" />
                          Reject
                        </span>
                      </SelectItem>
                    </SelectContent>
                  </Select>

                  {reviewDecision === types.ReviewStatus.NEEDS_CHANGES && (
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Requested Changes</label>
                      <div className="space-y-2">
                        {requestedChanges.map((change, idx) => (
                          <div key={idx} className="flex items-center gap-2">
                            <Input value={change} readOnly />
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => setRequestedChanges(prev => 
                                prev.filter((_, i) => i !== idx)
                              )}
                            >
                              <XCircle className="h-4 w-4" />
                            </Button>
                          </div>
                        ))}
                        <div className="flex gap-2">
                          <Input
                            placeholder="Add a change request..."
                            value={currentChangeRequest}
                            onChange={(e) => setCurrentChangeRequest(e.target.value)}
                            onKeyPress={(e) => {
                              if (e.key === 'Enter' && currentChangeRequest.trim()) {
                                setRequestedChanges(prev => [...prev, currentChangeRequest]);
                                setCurrentChangeRequest('');
                              }
                            }}
                          />
                          <Button
                            onClick={() => {
                              if (currentChangeRequest.trim()) {
                                setRequestedChanges(prev => [...prev, currentChangeRequest]);
                                setCurrentChangeRequest('');
                              }
                            }}
                          >
                            Add
                          </Button>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Comments Section */}
              {selectedReview.comments.length > 0 && (
                <div>
                  <h4 className="font-semibold mb-3">Comments</h4>
                  <ScrollArea className="h-[200px]">
                    <div className="space-y-3">
                      {selectedReview.comments.map((comment) => (
                        <div key={comment.id} className="flex gap-3">
                          <Avatar>
                            <AvatarFallback>
                              {(comment as ReviewComment).authorName?.split(' ').map(n => n[0]).join('')}
                            </AvatarFallback>
                          </Avatar>
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <p className="text-sm font-medium">
                                {(comment as ReviewComment).authorName}
                              </p>
                              <p className="text-xs text-muted-foreground">
                                {new Date(comment.timestamp).toLocaleString()}
                              </p>
                            </div>
                            <p className="text-sm">{comment.text}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </div>
              )}
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowReviewDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleSubmitReview}>
              Submit Review
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Comment Dialog */}
      <Dialog open={showCommentDialog} onOpenChange={setShowCommentDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Comment</DialogTitle>
            <DialogDescription>
              Add a comment to this review
            </DialogDescription>
          </DialogHeader>
          <div>
            <Textarea
              placeholder="Enter your comment..."
              value={commentText}
              onChange={(e) => setCommentText(e.target.value)}
              rows={4}
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowCommentDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleAddComment}>
              Add Comment
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};