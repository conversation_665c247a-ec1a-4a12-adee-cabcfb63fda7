import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  FileText, Plus, CheckSquare, Circle, MessageSquare, Image, Code,
  Trash2, Edit, Eye, Copy, Settings, Save, AlertCircle, ChevronUp,
  ChevronDown, GripVertical, Hash, Type, ListOrdered, Move, CheckCircle,
  X, Clock, Trophy, Users, BarChart2, Shuffle
} from 'lucide-react';
import { useContentCreator } from '../../hooks/useContentCreator';
import { Assessment, Question, QuestionOption, AssessmentType, QuestionType } from '../../types';

interface QuestionFormData {
  type: QuestionType;
  text: string;
  points: number;
  options: QuestionOption[];
  correctAnswer: string;
  explanation: string;
  hints: string[];
  difficulty: 1 | 2 | 3 | 4 | 5;
  tags: string[];
}

const QuestionEditor: React.FC<{
  question: Partial<Question>;
  onSave: (question: Partial<Question>) => void;
  onCancel: () => void;
}> = ({ question, onSave, onCancel }) => {
  const [formData, setFormData] = useState<Partial<Question>>({
    type: QuestionType.MULTIPLE_CHOICE,
    text: '',
    points: 10,
    options: [],
    correctAnswer: '',
    explanation: '',
    hints: [],
    difficulty: 3,
    tags: [],
    ...question
  });

  const [newOption, setNewOption] = useState('');
  const [newHint, setNewHint] = useState('');
  const [newTag, setNewTag] = useState('');

  const addOption = () => {
    if (newOption.trim()) {
      const option: QuestionOption = {
        id: Date.now().toString(),
        text: newOption,
        isCorrect: false
      };
      setFormData({
        ...formData,
        options: [...(formData.options || []), option]
      });
      setNewOption('');
    }
  };

  const removeOption = (id: string) => {
    setFormData({
      ...formData,
      options: formData.options?.filter(opt => opt.id !== id)
    });
  };

  const toggleCorrectOption = (id: string) => {
    setFormData({
      ...formData,
      options: formData.options?.map(opt => ({
        ...opt,
        isCorrect: opt.id === id
      }))
    });
  };

  const addHint = () => {
    if (newHint.trim()) {
      setFormData({
        ...formData,
        hints: [...(formData.hints || []), newHint]
      });
      setNewHint('');
    }
  };

  const addTag = () => {
    if (newTag.trim() && !formData.tags?.includes(newTag)) {
      setFormData({
        ...formData,
        tags: [...(formData.tags || []), newTag]
      });
      setNewTag('');
    }
  };

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label>Question Type</Label>
          <Select 
            value={formData.type} 
            onValueChange={(value: QuestionType) => setFormData({ ...formData, type: value })}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value={QuestionType.MULTIPLE_CHOICE}>Multiple Choice</SelectItem>
              <SelectItem value={QuestionType.TRUE_FALSE}>True/False</SelectItem>
              <SelectItem value={QuestionType.SHORT_ANSWER}>Short Answer</SelectItem>
              <SelectItem value={QuestionType.ESSAY}>Essay</SelectItem>
              <SelectItem value={QuestionType.MATCHING}>Matching</SelectItem>
              <SelectItem value={QuestionType.DRAG_DROP}>Drag & Drop</SelectItem>
              <SelectItem value={QuestionType.FILL_BLANK}>Fill in the Blank</SelectItem>
              <SelectItem value={QuestionType.HOTSPOT}>Hotspot</SelectItem>
              <SelectItem value={QuestionType.SEQUENCE}>Sequence</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label>Points</Label>
          <Input 
            type="number" 
            value={formData.points} 
            onChange={(e) => setFormData({ ...formData, points: parseInt(e.target.value) })}
          />
        </div>
      </div>

      <div>
        <Label>Question Text</Label>
        <Textarea 
          value={formData.text}
          onChange={(e) => setFormData({ ...formData, text: e.target.value })}
          placeholder="Enter your question here..."
          rows={3}
        />
      </div>

      {(formData.type === QuestionType.MULTIPLE_CHOICE || formData.type === QuestionType.TRUE_FALSE) && (
        <div>
          <Label>Answer Options</Label>
          <div className="space-y-2 mt-2">
            {formData.options?.map((option) => (
              <div key={option.id} className="flex items-center gap-2 p-2 border rounded">
                <Button
                  variant={option.isCorrect ? "default" : "outline"}
                  size="sm"
                  onClick={() => toggleCorrectOption(option.id)}
                >
                  <CheckCircle className="h-4 w-4" />
                </Button>
                <span className="flex-1">{option.text}</span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeOption(option.id)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ))}
            <div className="flex gap-2">
              <Input 
                value={newOption}
                onChange={(e) => setNewOption(e.target.value)}
                placeholder="Add an option..."
                onKeyPress={(e) => e.key === 'Enter' && addOption()}
              />
              <Button onClick={addOption}>Add</Button>
            </div>
          </div>
        </div>
      )}

      <div>
        <Label>Explanation (shown after answer)</Label>
        <Textarea 
          value={formData.explanation}
          onChange={(e) => setFormData({ ...formData, explanation: e.target.value })}
          placeholder="Explain the correct answer..."
          rows={2}
        />
      </div>

      <div>
        <Label>Hints</Label>
        <div className="space-y-2 mt-2">
          {formData.hints?.map((hint, index) => (
            <div key={index} className="flex items-center gap-2">
              <span className="text-sm flex-1">{hint}</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setFormData({
                  ...formData,
                  hints: formData.hints?.filter((_, i) => i !== index)
                })}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          ))}
          <div className="flex gap-2">
            <Input 
              value={newHint}
              onChange={(e) => setNewHint(e.target.value)}
              placeholder="Add a hint..."
              onKeyPress={(e) => e.key === 'Enter' && addHint()}
            />
            <Button onClick={addHint} size="sm">Add Hint</Button>
          </div>
        </div>
      </div>

      <div>
        <Label>Difficulty</Label>
        <Select 
          value={formData.difficulty?.toString()} 
          onValueChange={(value) => setFormData({ ...formData, difficulty: parseInt(value) as 1 | 2 | 3 | 4 | 5 })}
        >
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="1">Very Easy</SelectItem>
            <SelectItem value="2">Easy</SelectItem>
            <SelectItem value="3">Medium</SelectItem>
            <SelectItem value="4">Hard</SelectItem>
            <SelectItem value="5">Very Hard</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div>
        <Label>Tags</Label>
        <div className="flex flex-wrap gap-2 mt-2">
          {formData.tags?.map((tag, index) => (
            <Badge key={index} variant="secondary">
              {tag}
              <Button
                variant="ghost"
                size="sm"
                className="ml-1 h-4 w-4 p-0"
                onClick={() => setFormData({
                  ...formData,
                  tags: formData.tags?.filter((_, i) => i !== index)
                })}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          ))}
          <div className="flex gap-2">
            <Input 
              value={newTag}
              onChange={(e) => setNewTag(e.target.value)}
              placeholder="Add tag..."
              className="w-32"
              onKeyPress={(e) => e.key === 'Enter' && addTag()}
            />
          </div>
        </div>
      </div>

      <div className="flex justify-end gap-2">
        <Button variant="outline" onClick={onCancel}>Cancel</Button>
        <Button onClick={() => onSave(formData)}>Save Question</Button>
      </div>
    </div>
  );
};

export const AssessmentBuilder: React.FC = () => {
  const { createAssessment, updateAssessment, getCourses, loading, error } = useContentCreator();
  const [assessments, setAssessments] = useState<Assessment[]>([]);
  const [selectedAssessment, setSelectedAssessment] = useState<Assessment | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const [editingQuestion, setEditingQuestion] = useState<Partial<Question> | null>(null);
  const [showQuestionDialog, setShowQuestionDialog] = useState(false);

  const [newAssessment, setNewAssessment] = useState<Partial<Assessment>>({
    title: '',
    description: '',
    type: AssessmentType.QUIZ,
    questions: [],
    passingScore: 70,
    timeLimit: undefined,
    attempts: 3,
    randomizeQuestions: false,
    showFeedback: true
  });

  const questionTypeIcons = {
    [QuestionType.MULTIPLE_CHOICE]: Circle,
    [QuestionType.TRUE_FALSE]: CheckSquare,
    [QuestionType.SHORT_ANSWER]: MessageSquare,
    [QuestionType.ESSAY]: Type,
    [QuestionType.MATCHING]: Move,
    [QuestionType.DRAG_DROP]: GripVertical,
    [QuestionType.FILL_BLANK]: Hash,
    [QuestionType.HOTSPOT]: Image,
    [QuestionType.SEQUENCE]: ListOrdered
  };

  const assessmentTypeColors = {
    [AssessmentType.QUIZ]: 'default',
    [AssessmentType.EXAM]: 'destructive',
    [AssessmentType.SURVEY]: 'secondary',
    [AssessmentType.ASSIGNMENT]: 'outline',
    [AssessmentType.PROJECT]: 'default',
    [AssessmentType.PEER_REVIEW]: 'secondary'
  };

  // Load sample assessments
  useEffect(() => {
    const sampleAssessments: Assessment[] = [
      {
        id: '1',
        title: 'Module 1 Quiz',
        description: 'Basic concepts and fundamentals',
        type: AssessmentType.QUIZ,
        questions: [],
        passingScore: 70,
        timeLimit: 15,
        attempts: 3,
        randomizeQuestions: false,
        showFeedback: true
      },
      {
        id: '2',
        title: 'Midterm Exam',
        description: 'Comprehensive assessment of first half',
        type: AssessmentType.EXAM,
        questions: [],
        passingScore: 75,
        timeLimit: 45,
        attempts: 1,
        randomizeQuestions: true,
        showFeedback: false
      }
    ];
    setAssessments(sampleAssessments);
  }, []);

  const handleCreateAssessment = async () => {
    if (!newAssessment.title) return;

    try {
      const assessment = await createAssessment({
        ...newAssessment,
        id: Date.now().toString(),
        questions: []
      } as Assessment);
      
      setAssessments([...assessments, assessment]);
      setSelectedAssessment(assessment);
      setIsCreating(false);
      setNewAssessment({
        title: '',
        description: '',
        type: AssessmentType.QUIZ,
        questions: [],
        passingScore: 70,
        timeLimit: undefined,
        attempts: 3,
        randomizeQuestions: false,
        showFeedback: true
      });
    } catch (err) {
    }
  };

  const handleUpdateAssessment = async (assessment: Assessment) => {
    try {
      const updated = await updateAssessment(assessment);
      setAssessments(assessments.map(a => a.id === updated.id ? updated : a));
      setSelectedAssessment(updated);
    } catch (err) {
    }
  };

  const handleAddQuestion = (question: Partial<Question>) => {
    if (!selectedAssessment) return;

    const newQuestion: Question = {
      id: Date.now().toString(),
      assessmentId: selectedAssessment.id,
      ...question
    } as Question;

    const updatedAssessment = {
      ...selectedAssessment,
      questions: [...selectedAssessment.questions, newQuestion]
    };

    handleUpdateAssessment(updatedAssessment);
    setShowQuestionDialog(false);
    setEditingQuestion(null);
  };

  const handleUpdateQuestion = (question: Partial<Question>) => {
    if (!selectedAssessment || !editingQuestion) return;

    const updatedAssessment = {
      ...selectedAssessment,
      questions: selectedAssessment.questions.map(q =>
        q.id === editingQuestion.id ? { ...q, ...question } : q
      )
    };

    handleUpdateAssessment(updatedAssessment);
    setShowQuestionDialog(false);
    setEditingQuestion(null);
  };

  const handleDeleteQuestion = (questionId: string) => {
    if (!selectedAssessment) return;

    const updatedAssessment = {
      ...selectedAssessment,
      questions: selectedAssessment.questions.filter(q => q.id !== questionId)
    };

    handleUpdateAssessment(updatedAssessment);
  };

  const handleDuplicateQuestion = (question: Question) => {
    if (!selectedAssessment) return;

    const duplicated: Question = {
      ...question,
      id: Date.now().toString()
    };

    const updatedAssessment = {
      ...selectedAssessment,
      questions: [...selectedAssessment.questions, duplicated]
    };

    handleUpdateAssessment(updatedAssessment);
  };

  const moveQuestion = (index: number, direction: 'up' | 'down') => {
    if (!selectedAssessment) return;

    const questions = [...selectedAssessment.questions];
    const newIndex = direction === 'up' ? index - 1 : index + 1;

    if (newIndex < 0 || newIndex >= questions.length) return;

    [questions[index], questions[newIndex]] = [questions[newIndex], questions[index]];

    const updatedAssessment = {
      ...selectedAssessment,
      questions
    };

    handleUpdateAssessment(updatedAssessment);
  };

  const getTotalPoints = () => {
    if (!selectedAssessment) return 0;
    return selectedAssessment.questions.reduce((sum, q) => sum + q.points, 0);
  };

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Failed to load assessment builder. Please try again later.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Assessment Builder</h2>
          <p className="text-muted-foreground mt-1">
            Create quizzes, tests, and assessments for your courses
          </p>
        </div>
        <Dialog open={isCreating} onOpenChange={setIsCreating}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              New Assessment
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New Assessment</DialogTitle>
              <DialogDescription>
                Set up the basic details for your assessment
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label>Title</Label>
                <Input 
                  value={newAssessment.title}
                  onChange={(e) => setNewAssessment({ ...newAssessment, title: e.target.value })}
                  placeholder="Assessment title..."
                />
              </div>
              <div>
                <Label>Description</Label>
                <Textarea 
                  value={newAssessment.description}
                  onChange={(e) => setNewAssessment({ ...newAssessment, description: e.target.value })}
                  placeholder="Brief description..."
                  rows={2}
                />
              </div>
              <div>
                <Label>Type</Label>
                <Select 
                  value={newAssessment.type}
                  onValueChange={(value: AssessmentType) => setNewAssessment({ ...newAssessment, type: value })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={AssessmentType.QUIZ}>Quiz</SelectItem>
                    <SelectItem value={AssessmentType.EXAM}>Exam</SelectItem>
                    <SelectItem value={AssessmentType.SURVEY}>Survey</SelectItem>
                    <SelectItem value={AssessmentType.ASSIGNMENT}>Assignment</SelectItem>
                    <SelectItem value={AssessmentType.PROJECT}>Project</SelectItem>
                    <SelectItem value={AssessmentType.PEER_REVIEW}>Peer Review</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Passing Score (%)</Label>
                  <Input 
                    type="number"
                    value={newAssessment.passingScore}
                    onChange={(e) => setNewAssessment({ ...newAssessment, passingScore: parseInt(e.target.value) })}
                  />
                </div>
                <div>
                  <Label>Time Limit (minutes)</Label>
                  <Input 
                    type="number"
                    value={newAssessment.timeLimit || ''}
                    onChange={(e) => setNewAssessment({ ...newAssessment, timeLimit: e.target.value ? parseInt(e.target.value) : undefined })}
                    placeholder="No limit"
                  />
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsCreating(false)}>Cancel</Button>
              <Button onClick={handleCreateAssessment} disabled={!newAssessment.title}>Create</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-4">
          {selectedAssessment ? (
            <>
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle>{selectedAssessment.title}</CardTitle>
                      <CardDescription>{selectedAssessment.description}</CardDescription>
                    </div>
                    <Badge variant={assessmentTypeColors[selectedAssessment.type] as any}>
                      {selectedAssessment.type}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <FileText className="h-4 w-4" />
                      {selectedAssessment.questions.length} questions
                    </div>
                    <div className="flex items-center gap-1">
                      <Trophy className="h-4 w-4" />
                      {getTotalPoints()} points
                    </div>
                    {selectedAssessment.timeLimit && (
                      <div className="flex items-center gap-1">
                        <Clock className="h-4 w-4" />
                        {selectedAssessment.timeLimit} minutes
                      </div>
                    )}
                    <div className="flex items-center gap-1">
                      <Users className="h-4 w-4" />
                      {selectedAssessment.attempts} attempts
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle>Questions</CardTitle>
                    <Dialog open={showQuestionDialog} onOpenChange={setShowQuestionDialog}>
                      <DialogTrigger asChild>
                        <Button size="sm">
                          <Plus className="h-4 w-4 mr-2" />
                          Add Question
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
                        <DialogHeader>
                          <DialogTitle>
                            {editingQuestion ? 'Edit Question' : 'Add New Question'}
                          </DialogTitle>
                        </DialogHeader>
                        <QuestionEditor 
                          question={editingQuestion || {}}
                          onSave={editingQuestion ? handleUpdateQuestion : handleAddQuestion}
                          onCancel={() => {
                            setShowQuestionDialog(false);
                            setEditingQuestion(null);
                          }}
                        />
                      </DialogContent>
                    </Dialog>
                  </div>
                </CardHeader>
                <CardContent>
                  {selectedAssessment.questions.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      No questions added yet. Click "Add Question" to get started.
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {selectedAssessment.questions.map((question, index) => {
                        const Icon = questionTypeIcons[question.type];
                        return (
                          <Card key={question.id}>
                            <CardHeader className="pb-3">
                              <div className="flex items-start justify-between">
                                <div className="flex items-start gap-3 flex-1">
                                  <div className="flex flex-col items-center gap-1">
                                    <span className="text-sm font-medium">#{index + 1}</span>
                                    <Icon className="h-4 w-4 text-muted-foreground" />
                                  </div>
                                  <div className="flex-1">
                                    <p className="font-medium">{question.text}</p>
                                    <div className="flex items-center gap-2 mt-2">
                                      <Badge variant="secondary" className="text-xs">
                                        {question.points} pts
                                      </Badge>
                                      <Badge variant="outline" className="text-xs">
                                        Difficulty: {question.difficulty}/5
                                      </Badge>
                                      {question.tags.map(tag => (
                                        <Badge key={tag} variant="outline" className="text-xs">
                                          {tag}
                                        </Badge>
                                      ))}
                                    </div>
                                  </div>
                                </div>
                                <div className="flex items-center gap-1">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => moveQuestion(index, 'up')}
                                    disabled={index === 0}
                                  >
                                    <ChevronUp className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => moveQuestion(index, 'down')}
                                    disabled={index === selectedAssessment.questions.length - 1}
                                  >
                                    <ChevronDown className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => {
                                      setEditingQuestion(question);
                                      setShowQuestionDialog(true);
                                    }}
                                  >
                                    <Edit className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleDuplicateQuestion(question)}
                                  >
                                    <Copy className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleDeleteQuestion(question.id)}
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </div>
                              </div>
                            </CardHeader>
                            {question.options && question.options.length > 0 && (
                              <CardContent className="pt-0">
                                <div className="space-y-1">
                                  {question.options.map((option, i) => (
                                    <div key={option.id} className="flex items-center gap-2 text-sm">
                                      <span className={`w-6 h-6 rounded-full border flex items-center justify-center text-xs ${
                                        option.isCorrect ? 'bg-green-100 border-green-500 text-green-700' : ''
                                      }`}>
                                        {String.fromCharCode(65 + i)}
                                      </span>
                                      <span className={option.isCorrect ? 'font-medium' : ''}>
                                        {option.text}
                                      </span>
                                      {option.isCorrect && (
                                        <CheckCircle className="h-3 w-3 text-green-500" />
                                      )}
                                    </div>
                                  ))}
                                </div>
                              </CardContent>
                            )}
                          </Card>
                        );
                      })}
                    </div>
                  )}
                </CardContent>
              </Card>
            </>
          ) : (
            <Card>
              <CardContent className="py-12">
                <div className="text-center text-muted-foreground">
                  <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Select an assessment from the list or create a new one to get started</p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        <div className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Your Assessments</CardTitle>
              <CardDescription>Select an assessment to edit</CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="space-y-2">
                  <Skeleton className="h-16 w-full" />
                  <Skeleton className="h-16 w-full" />
                  <Skeleton className="h-16 w-full" />
                </div>
              ) : (
                <div className="space-y-2">
                  {assessments.map((assessment) => (
                    <Button
                      key={assessment.id}
                      variant={selectedAssessment?.id === assessment.id ? "default" : "outline"}
                      className="w-full justify-start"
                      onClick={() => setSelectedAssessment(assessment)}
                    >
                      <div className="flex items-center justify-between w-full">
                        <div className="flex items-center gap-2">
                          <FileText className="h-4 w-4" />
                          <span className="text-left">{assessment.title}</span>
                        </div>
                        <Badge variant="secondary" className="text-xs">
                          {assessment.questions.length}
                        </Badge>
                      </div>
                    </Button>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {selectedAssessment && (
            <Card>
              <CardHeader>
                <CardTitle>Assessment Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label>Passing Score (%)</Label>
                  <Input 
                    type="number"
                    value={selectedAssessment.passingScore}
                    onChange={(e) => handleUpdateAssessment({
                      ...selectedAssessment,
                      passingScore: parseInt(e.target.value)
                    })}
                  />
                </div>

                <div>
                  <Label>Time Limit (minutes)</Label>
                  <Input 
                    type="number"
                    value={selectedAssessment.timeLimit || ''}
                    onChange={(e) => handleUpdateAssessment({
                      ...selectedAssessment,
                      timeLimit: e.target.value ? parseInt(e.target.value) : undefined
                    })}
                    placeholder="No limit"
                  />
                </div>

                <div>
                  <Label>Attempts Allowed</Label>
                  <Select 
                    value={selectedAssessment.attempts.toString()}
                    onValueChange={(value) => handleUpdateAssessment({
                      ...selectedAssessment,
                      attempts: parseInt(value)
                    })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">1 attempt</SelectItem>
                      <SelectItem value="2">2 attempts</SelectItem>
                      <SelectItem value="3">3 attempts</SelectItem>
                      <SelectItem value="5">5 attempts</SelectItem>
                      <SelectItem value="999">Unlimited</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center justify-between">
                  <Label>Randomize Questions</Label>
                  <Switch 
                    checked={selectedAssessment.randomizeQuestions}
                    onCheckedChange={(checked) => handleUpdateAssessment({
                      ...selectedAssessment,
                      randomizeQuestions: checked
                    })}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label>Show Feedback</Label>
                  <Switch 
                    checked={selectedAssessment.showFeedback}
                    onCheckedChange={(checked) => handleUpdateAssessment({
                      ...selectedAssessment,
                      showFeedback: checked
                    })}
                  />
                </div>

                <Button className="w-full">
                  <Save className="h-4 w-4 mr-2" />
                  Save Settings
                </Button>
              </CardContent>
            </Card>
          )}

          <Card>
            <CardHeader>
              <CardTitle>Quick Stats</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Total Assessments</span>
                  <span className="font-medium">{assessments.length}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Total Questions</span>
                  <span className="font-medium">
                    {assessments.reduce((sum, a) => sum + a.questions.length, 0)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Avg. Questions</span>
                  <span className="font-medium">
                    {assessments.length > 0 
                      ? Math.round(assessments.reduce((sum, a) => sum + a.questions.length, 0) / assessments.length)
                      : 0}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};