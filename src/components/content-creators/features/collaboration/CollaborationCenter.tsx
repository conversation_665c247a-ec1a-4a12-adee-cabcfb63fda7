import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Users, MessageSquare, Share2, Calendar, Bell, UserPlus, 
  Circle, Video, Mic, MicOff, VideoOff, Send, Clock,
  CheckCircle, XCircle, AlertCircle, FileText, Edit3,
  Activity, Globe, Lock, Unlock, MoreVertical, Hash, RotateCcw
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle,
  DialogFooter 
} from '@/components/ui/dialog';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue 
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Skeleton } from '@/components/ui/skeleton';
import { useContentCreator } from '../../hooks/useContentCreator';
import { useToast } from '@/components/ui/use-toast';
import * as types from '../../types';

interface CollaborationMessage {
  id: string;
  userId: string;
  userName: string;
  text: string;
  timestamp: Date;
  type: 'message' | 'system' | 'edit';
  elementId?: string;
}

interface ActiveSession {
  id: string;
  contentId: string;
  contentTitle: string;
  participants: types.Participant[];
  startedAt: Date;
  lastActivity: Date;
  status: 'active' | 'idle' | 'ended';
}

export const CollaborationCenter: React.FC = () => {
  const { 
    getCollaborationWsUrl, 
    inviteCollaborator,
    getCourse,
    getCourses,
    createCourse,
    loading,
    error 
  } = useContentCreator();
  const { toast } = useToast();

  // State
  const [activeSession, setActiveSession] = useState<ActiveSession | null>(null);
  const [participants, setParticipants] = useState<types.Participant[]>([]);
  const [messages, setMessages] = useState<CollaborationMessage[]>([]);
  const [messageInput, setMessageInput] = useState('');
  const [isConnected, setIsConnected] = useState(false);
  const [showInviteDialog, setShowInviteDialog] = useState(false);
  const [inviteEmail, setInviteEmail] = useState('');
  const [inviteRole, setInviteRole] = useState<'editor' | 'reviewer' | 'viewer'>('viewer');
  const [inviteMessage, setInviteMessage] = useState('');
  const [recentActivities, setRecentActivities] = useState<types.ContentChange[]>([]);
  const [selectedContentId, setSelectedContentId] = useState<string>('');
  const [videoEnabled, setVideoEnabled] = useState(false);
  const [audioEnabled, setAudioEnabled] = useState(true);
  const [screenSharing, setScreenSharing] = useState(false);
  
  // WebSocket and WebRTC refs
  const wsRef = useRef<WebSocket | null>(null);
  const peerConnectionRef = useRef<RTCPeerConnection | null>(null);
  const localStreamRef = useRef<MediaStream | null>(null);
  const remoteStreamRef = useRef<MediaStream | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // State for courses
  const [courses, setCourses] = useState<Array<{ id: string; title: string; status: string }>>([]);
  const [coursesLoading, setCoursesLoading] = useState(false);

  // Connect to collaboration session
  const connectToSession = useCallback(async (contentId: string) => {
    try {
      const wsUrl = await getCollaborationWsUrl(contentId);
      
      // Close existing connection
      if (wsRef.current) {
        wsRef.current.close();
      }

      // Create new WebSocket connection
      const ws = new WebSocket(wsUrl);
      
      ws.onopen = () => {
        setIsConnected(true);
        toast({
          title: "Connected",
          description: "Successfully connected to collaboration session",
        });
        
        // Send join message
        ws.send(JSON.stringify({
          type: 'join',
          userId: 'current-user',
          userName: 'Current User',
          contentId
        }));
      };

      ws.onmessage = (event) => {
        const data = JSON.parse(event.data);
        handleWebSocketMessage(data);
      };

      ws.onerror = (error) => {
        toast({
          title: "Connection Error",
          description: "Failed to connect to collaboration session",
          variant: "destructive"
        });
      };

      ws.onclose = () => {
        setIsConnected(false);
        toast({
          title: "Disconnected",
          description: "Collaboration session ended",
        });
      };

      wsRef.current = ws;
      setSelectedContentId(contentId);

      // Create session
      setActiveSession({
        id: `session-${Date.now()}`,
        contentId,
        contentTitle: courses.find(c => c.id === contentId)?.title || 'Unknown',
        participants: [],
        startedAt: new Date(),
        lastActivity: new Date(),
        status: 'active'
      });

    } catch (err) {
      toast({
        title: "Connection Failed",
        description: "Could not establish collaboration session",
        variant: "destructive"
      });
    }
  }, [getCollaborationWsUrl, toast]);

  // Handle incoming WebSocket messages
  const handleWebSocketMessage = (data: any) => {
    switch (data.type) {
      case 'participant_joined':
        setParticipants(prev => [...prev, data.participant]);
        setMessages(prev => [...prev, {
          id: `msg-${Date.now()}`,
          userId: 'system',
          userName: 'System',
          text: `${data.participant.name} joined the session`,
          timestamp: new Date(),
          type: 'system'
        }]);
        break;
        
      case 'participant_left':
        setParticipants(prev => prev.filter(p => p.userId !== data.userId));
        break;
        
      case 'message':
        setMessages(prev => [...prev, {
          ...data.message,
          timestamp: new Date(data.message.timestamp)
        }]);
        break;
        
      case 'content_change':
        setRecentActivities(prev => [data.change, ...prev].slice(0, 10));
        break;
        
      case 'cursor_update':
        // Update participant cursor position
        setParticipants(prev => prev.map(p => 
          p.userId === data.userId 
            ? { ...p, cursor: data.cursor }
            : p
        ));
        break;
    }
  };

  // Send chat message
  const sendMessage = () => {
    if (!messageInput.trim() || !wsRef.current || !isConnected) return;

    const message: CollaborationMessage = {
      id: `msg-${Date.now()}`,
      userId: 'current-user',
      userName: 'Current User',
      text: messageInput,
      timestamp: new Date(),
      type: 'message'
    };

    wsRef.current.send(JSON.stringify({
      type: 'message',
      message
    }));

    setMessages(prev => [...prev, message]);
    setMessageInput('');
  };

  // Invite collaborator
  const handleInviteCollaborator = async () => {
    if (!inviteEmail || !selectedContentId) return;

    try {
      await inviteCollaborator({
        contentId: selectedContentId,
        inviteeEmail: inviteEmail,
        role: inviteRole,
        message: inviteMessage
      });

      toast({
        title: "Invitation Sent",
        description: `Invitation sent to ${inviteEmail}`,
      });

      setShowInviteDialog(false);
      setInviteEmail('');
      setInviteMessage('');
    } catch (err) {
      toast({
        title: "Invitation Failed",
        description: "Could not send invitation",
        variant: "destructive"
      });
    }
  };

  // Toggle video/audio
  const toggleVideo = async () => {
    if (!localStreamRef.current) {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ 
          video: true, 
          audio: audioEnabled 
        });
        localStreamRef.current = stream;
        setVideoEnabled(true);
      } catch (err) {
        toast({
          title: "Camera Access Failed",
          description: "Could not access your camera",
          variant: "destructive"
        });
      }
    } else {
      localStreamRef.current.getVideoTracks().forEach(track => {
        track.enabled = !track.enabled;
      });
      setVideoEnabled(!videoEnabled);
    }
  };

  const toggleAudio = () => {
    if (localStreamRef.current) {
      localStreamRef.current.getAudioTracks().forEach(track => {
        track.enabled = !track.enabled;
      });
    }
    setAudioEnabled(!audioEnabled);
  };

  // Load courses on mount
  useEffect(() => {
    loadCourses();
  }, []);

  const loadCourses = async () => {
    try {
      setCoursesLoading(true);
      const response = await getCourses();
      
      if (response && response.items && response.items.length > 0) {
        setCourses(response.items.map(course => ({
          id: course.id,
          title: course.title,
          status: course.status || 'draft'
        })));
      } else {
        // Don't create default courses - requires valid authentication
        // Courses should be created through the UI with proper authentication
        setCourses([]);
      }
    } catch (err) {
      toast({
        title: "Failed to Load Courses",
        description: "Could not load course list",
        variant: "destructive"
      });
    } finally {
      setCoursesLoading(false);
    }
  };

  // Auto-scroll messages
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
      if (localStreamRef.current) {
        localStreamRef.current.getTracks().forEach(track => track.stop());
      }
    };
  }, []);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Collaboration Center</h2>
          <p className="text-muted-foreground mt-1">
            Real-time collaboration with your team
          </p>
        </div>
        <div className="flex items-center gap-2">
          {isConnected && (
            <Badge variant="outline" className="gap-1">
              <Circle className="h-2 w-2 fill-green-500 text-green-500" />
              Connected
            </Badge>
          )}
          <Button 
            onClick={() => setShowInviteDialog(true)}
            disabled={!selectedContentId}
          >
            <UserPlus className="h-4 w-4 mr-2" />
            Invite Team
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Main Collaboration Area */}
        <div className="lg:col-span-3 space-y-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Active Session</CardTitle>
                  <CardDescription>
                    {activeSession ? activeSession.contentTitle : 'Select a course to start collaborating'}
                  </CardDescription>
                </div>
                {activeSession && (
                  <div className="flex gap-2">
                    <Button
                      variant={videoEnabled ? "default" : "outline"}
                      size="sm"
                      onClick={toggleVideo}
                    >
                      {videoEnabled ? <Video className="h-4 w-4" /> : <VideoOff className="h-4 w-4" />}
                    </Button>
                    <Button
                      variant={audioEnabled ? "default" : "outline"}
                      size="sm"
                      onClick={toggleAudio}
                    >
                      {audioEnabled ? <Mic className="h-4 w-4" /> : <MicOff className="h-4 w-4" />}
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setScreenSharing(!screenSharing)}
                    >
                      <Globe className="h-4 w-4" />
                    </Button>
                  </div>
                )}
              </div>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="chat" className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="chat">
                    <MessageSquare className="h-4 w-4 mr-2" />
                    Chat
                  </TabsTrigger>
                  <TabsTrigger value="activity">
                    <Activity className="h-4 w-4 mr-2" />
                    Activity
                  </TabsTrigger>
                  <TabsTrigger value="participants">
                    <Users className="h-4 w-4 mr-2" />
                    Participants ({participants.length})
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="chat" className="space-y-4">
                  <ScrollArea className="h-[400px] w-full rounded-md border p-4">
                    {messages.length === 0 ? (
                      <div className="text-center text-muted-foreground py-8">
                        No messages yet. Start the conversation!
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {messages.map((msg) => (
                          <div 
                            key={msg.id}
                            className={`flex ${msg.userId === 'current-user' ? 'justify-end' : 'justify-start'}`}
                          >
                            <div className={`max-w-[70%] ${msg.type === 'system' ? 'w-full' : ''}`}>
                              {msg.type === 'system' ? (
                                <div className="text-center text-sm text-muted-foreground py-2">
                                  {msg.text}
                                </div>
                              ) : (
                                <div className={`rounded-lg p-3 ${
                                  msg.userId === 'current-user' 
                                    ? 'bg-primary text-primary-foreground' 
                                    : 'bg-muted'
                                }`}>
                                  <div className="font-semibold text-sm mb-1">
                                    {msg.userName}
                                  </div>
                                  <div>{msg.text}</div>
                                  <div className="text-xs opacity-70 mt-1">
                                    {new Date(msg.timestamp).toLocaleTimeString()}
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>
                        ))}
                        <div ref={messagesEndRef} />
                      </div>
                    )}
                  </ScrollArea>
                  
                  {isConnected && (
                    <div className="flex gap-2">
                      <Input
                        placeholder="Type a message..."
                        value={messageInput}
                        onChange={(e) => setMessageInput(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
                      />
                      <Button onClick={sendMessage}>
                        <Send className="h-4 w-4" />
                      </Button>
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="activity" className="space-y-4">
                  <ScrollArea className="h-[400px] w-full">
                    {recentActivities.length === 0 ? (
                      <div className="text-center text-muted-foreground py-8">
                        No recent activity
                      </div>
                    ) : (
                      <div className="space-y-2">
                        {recentActivities.map((activity) => (
                          <div key={activity.id} className="flex items-start gap-3 p-3 rounded-lg hover:bg-muted">
                            <div className="mt-1">
                              {activity.type === 'add' && <CheckCircle className="h-4 w-4 text-green-500" />}
                              {activity.type === 'edit' && <Edit3 className="h-4 w-4 text-blue-500" />}
                              {activity.type === 'delete' && <XCircle className="h-4 w-4 text-red-500" />}
                            </div>
                            <div className="flex-1">
                              <div className="font-medium text-sm">
                                {activity.type === 'add' && 'Added'}
                                {activity.type === 'edit' && 'Modified'}
                                {activity.type === 'delete' && 'Removed'}
                                {' '}{activity.target}
                              </div>
                              <div className="text-xs text-muted-foreground">
                                by {activity.userId} • {new Date(activity.timestamp).toLocaleTimeString()}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </ScrollArea>
                </TabsContent>

                <TabsContent value="participants" className="space-y-4">
                  <div className="grid gap-3">
                    {participants.map((participant) => (
                      <div key={participant.userId} className="flex items-center justify-between p-3 rounded-lg border">
                        <div className="flex items-center gap-3">
                          <Avatar>
                            <AvatarFallback>
                              {participant.name.split(' ').map(n => n[0]).join('')}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="font-medium">{participant.name}</div>
                            <div className="text-sm text-muted-foreground">{participant.role}</div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {participant.isActive && (
                            <Badge variant="outline" className="gap-1">
                              <Circle className="h-2 w-2 fill-green-500 text-green-500" />
                              Active
                            </Badge>
                          )}
                          <Button variant="ghost" size="sm">
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-4">
          {/* Course Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Select Course</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              {coursesLoading ? (
                <div className="text-center py-4">
                  <RotateCcw className="h-6 w-6 animate-spin mx-auto text-muted-foreground" />
                  <p className="text-sm text-muted-foreground mt-2">Loading courses...</p>
                </div>
              ) : courses.length === 0 ? (
                <div className="text-center py-4 text-sm text-muted-foreground">
                  No courses available
                </div>
              ) : (
                courses.map((course) => (
                  <Button
                    key={course.id}
                    variant={selectedContentId === course.id ? "default" : "outline"}
                    className="w-full justify-start"
                    onClick={() => connectToSession(course.id)}
                  >
                    <FileText className="h-4 w-4 mr-2" />
                    <span className="truncate">{course.title}</span>
                  </Button>
                ))
              )}
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button 
                variant="outline" 
                className="w-full justify-start"
                onClick={() => setShowInviteDialog(true)}
                disabled={!selectedContentId}
              >
                <UserPlus className="h-4 w-4 mr-2" />
                Invite Collaborator
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Share2 className="h-4 w-4 mr-2" />
                Share Project
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Calendar className="h-4 w-4 mr-2" />
                Schedule Meeting
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Bell className="h-4 w-4 mr-2" />
                Notifications
              </Button>
            </CardContent>
          </Card>

          {/* Session Info */}
          {activeSession && (
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Session Info</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center gap-2 text-sm">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span>Started {new Date(activeSession.startedAt).toLocaleTimeString()}</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <Users className="h-4 w-4 text-muted-foreground" />
                  <span>{participants.length} participants</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <Hash className="h-4 w-4 text-muted-foreground" />
                  <span className="font-mono text-xs">{activeSession.id}</span>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Invite Dialog */}
      <Dialog open={showInviteDialog} onOpenChange={setShowInviteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Invite Collaborator</DialogTitle>
            <DialogDescription>
              Send an invitation to collaborate on this content
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">Email Address</label>
              <Input
                type="email"
                placeholder="<EMAIL>"
                value={inviteEmail}
                onChange={(e) => setInviteEmail(e.target.value)}
              />
            </div>
            <div>
              <label className="text-sm font-medium">Role</label>
              <Select value={inviteRole} onValueChange={(value: any) => setInviteRole(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="viewer">Viewer</SelectItem>
                  <SelectItem value="reviewer">Reviewer</SelectItem>
                  <SelectItem value="editor">Editor</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium">Message (Optional)</label>
              <Textarea
                placeholder="Add a personal message..."
                value={inviteMessage}
                onChange={(e) => setInviteMessage(e.target.value)}
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowInviteDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleInviteCollaborator} disabled={!inviteEmail}>
              Send Invitation
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};