// Content Creator API Service Layer

import {
  Course,
  CreateCourseRequest,
  UpdateCourseRequest,
  PublishCourseRequest,
  GenerateSCORMRequest,
  ContentSearchRequest,
  ContentSearchResponse,
  CollaborationInvite,
  ContentExportOptions,
  ContentImportOptions
} from '../../types';
import { camelToSnakeCase, snakeToCamelCase } from '../utils';

// Mock invoke function for now - replace with actual implementation
const invoke = async (command: string, args?: any): Promise<any> => {
  console.log(`Mock API call: ${command}`, args);
  throw new Error('API not implemented yet');
};

/**
 * Service class for handling content-related API calls
 */
export class ContentService {
  /**
   * Creates a new course
   */
  static async createCourse(request: CreateCourseRequest): Promise<Course> {
    try {
      const snakeCaseRequest = {
        ...request,
        metadata: (request as any).metadata ? camelToSnakeCase((request as any).metadata) : undefined
      };
      
      const response = await invoke('create_course', { request: snakeCaseRequest });
      return response as Course;
    } catch (error) {
      console.error('Failed to create course:', error);
      throw new Error(`Failed to create course: ${error}`);
    }
  }

  /**
   * Retrieves a course by ID
   */
  static async getCourse(courseId: string): Promise<Course> {
    try {
      const response = await invoke('get_course', { courseId });
      const course = response as Course;
      
      // Convert metadata from snake_case to camelCase
      if (course.metadata) {
        course.metadata = snakeToCamelCase(course.metadata as any) as any;
      }
      
      return course;
    } catch (error) {
      console.error('Failed to get course:', error);
      throw new Error(`Failed to get course: ${error}`);
    }
  }

  /**
   * Updates an existing course
   */
  static async updateCourse(courseId: string, request: UpdateCourseRequest): Promise<Course> {
    try {
      const snakeCaseRequest = {
        ...request,
        metadata: (request as any).metadata ? camelToSnakeCase((request as any).metadata) : undefined
      };
      
      const response = await invoke('update_course', { courseId, request: snakeCaseRequest });
      return response as Course;
    } catch (error) {
      console.error('Failed to update course:', error);
      throw new Error(`Failed to update course: ${error}`);
    }
  }

  /**
   * Deletes a course
   */
  static async deleteCourse(courseId: string): Promise<void> {
    try {
      await invoke('delete_course', { courseId });
    } catch (error) {
      console.error('Failed to delete course:', error);
      throw new Error(`Failed to delete course: ${error}`);
    }
  }

  /**
   * Publishes a course
   */
  static async publishCourse(courseId: string, request: PublishCourseRequest): Promise<Course> {
    try {
      const response = await invoke('publish_course', { courseId, request });
      return response as Course;
    } catch (error) {
      console.error('Failed to publish course:', error);
      throw new Error(`Failed to publish course: ${error}`);
    }
  }

  /**
   * Searches for courses
   */
  static async searchCourses(request?: ContentSearchRequest): Promise<ContentSearchResponse> {
    try {
      const response = await invoke('search_courses', { request });
      return response as ContentSearchResponse;
    } catch (error) {
      console.error('Failed to search courses:', error);
      throw new Error(`Failed to search courses: ${error}`);
    }
  }

  /**
   * Generates SCORM package
   */
  static async generateSCORM(courseId: string, request: GenerateSCORMRequest): Promise<string> {
    try {
      const response = await invoke('generate_scorm', { courseId, request });
      return response as string;
    } catch (error) {
      console.error('Failed to generate SCORM:', error);
      throw new Error(`Failed to generate SCORM: ${error}`);
    }
  }

  /**
   * Exports course content
   */
  static async exportContent(courseId: string, options: ContentExportOptions): Promise<string> {
    try {
      const response = await invoke('export_content', { courseId, options });
      return response as string;
    } catch (error) {
      console.error('Failed to export content:', error);
      throw new Error(`Failed to export content: ${error}`);
    }
  }

  /**
   * Imports course content
   */
  static async importContent(filePath: string, options: ContentImportOptions): Promise<Course> {
    try {
      const response = await invoke('import_content', { filePath, options });
      return response as Course;
    } catch (error) {
      console.error('Failed to import content:', error);
      throw new Error(`Failed to import content: ${error}`);
    }
  }

  /**
   * Sends collaboration invite
   */
  static async sendCollaborationInvite(courseId: string, invite: CollaborationInvite): Promise<void> {
    try {
      await invoke('send_collaboration_invite', { courseId, invite });
    } catch (error) {
      console.error('Failed to send collaboration invite:', error);
      throw new Error(`Failed to send collaboration invite: ${error}`);
    }
  }

  /**
   * Gets course analytics
   */
  static async getCourseAnalytics(courseId: string, period?: string): Promise<any> {
    try {
      const response = await invoke('get_course_analytics', { courseId, period });
      return response;
    } catch (error) {
      console.error('Failed to get course analytics:', error);
      throw new Error(`Failed to get course analytics: ${error}`);
    }
  }

  /**
   * Auto-saves course content
   */
  static async autoSaveCourse(courseId: string, content: Partial<Course>): Promise<void> {
    try {
      await invoke('auto_save_course', { courseId, content });
    } catch (error) {
      console.error('Failed to auto-save course:', error);
      // Don't throw error for auto-save failures to avoid disrupting user experience
    }
  }
}