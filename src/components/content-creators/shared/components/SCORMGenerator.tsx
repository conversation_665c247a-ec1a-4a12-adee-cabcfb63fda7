import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Package, Download, Settings, CheckCircle, FileArchive } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

interface SCORMGeneratorProps {
  mini?: boolean;
}

export const SCORMGenerator: React.FC<SCORMGeneratorProps> = ({ mini = false }) => {
  const packages = [
    { id: 1, name: 'Basic Training v1.2', version: 'SCORM 1.2', size: '15 MB' },
    { id: 2, name: 'Advanced Course', version: 'SCORM 2004', size: '28 MB' },
    { id: 3, name: 'Quick Tutorial', version: 'xAPI', size: '8 MB' },
  ];

  if (mini) {
    return (
      <div className="space-y-2">
        <Button variant="outline" className="w-full justify-start" size="sm">
          <Package className="h-4 w-4 mr-2" />
          Generate Package
        </Button>
        <Button variant="outline" className="w-full justify-start" size="sm">
          <Settings className="h-4 w-4 mr-2" />
          SCORM Settings
        </Button>
        <div className="text-xs text-muted-foreground p-2">
          3 packages ready for export
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold">SCORM Generator</h2>
        <p className="text-muted-foreground mt-1">
          Export your courses as SCORM-compliant packages
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Package Settings</CardTitle>
          <CardDescription>Configure SCORM export options</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-sm">SCORM Version</span>
            <select className="px-3 py-1 border rounded">
              <option>SCORM 1.2</option>
              <option>SCORM 2004 3rd Edition</option>
              <option>SCORM 2004 4th Edition</option>
              <option>xAPI (Tin Can)</option>
            </select>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm">Completion Tracking</span>
            <Badge variant="outline">
              <CheckCircle className="h-3 w-3 mr-1" />
              Enabled
            </Badge>
          </div>
          <Button className="w-full">
            <Package className="h-4 w-4 mr-2" />
            Generate SCORM Package
          </Button>
        </CardContent>
      </Card>

      <div className="space-y-4">
        <h3 className="font-semibold">Recent Packages</h3>
        {packages.map((pkg) => (
          <Card key={pkg.id}>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-base flex items-center gap-2">
                    <FileArchive className="h-4 w-4" />
                    {pkg.name}
                  </CardTitle>
                  <CardDescription>{pkg.version} • {pkg.size}</CardDescription>
                </div>
                <Button variant="outline" size="sm">
                  <Download className="h-3 w-3 mr-1" />
                  Download
                </Button>
              </div>
            </CardHeader>
          </Card>
        ))}
      </div>
    </div>
  );
};