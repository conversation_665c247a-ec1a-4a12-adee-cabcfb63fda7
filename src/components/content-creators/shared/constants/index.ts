// Content Creator Constants and Enums

export enum ContentStatus {
  DRAFT = 'Draft',
  IN_REVIEW = 'InReview',
  APPROVED = 'Approved',
  PUBLISHED = 'Published',
  ARCHIVED = 'Archived'
}

export enum ContentType {
  VIDEO = 'video',
  AUDIO = 'audio',
  TEXT = 'text',
  SLIDE = 'slide',
  INTERACTIVE = 'interactive',
  DOCUMENT = 'document',
  QUIZ = 'quiz',
  SIMULATION = 'simulation',
  SCORM = 'scorm'
}

export enum AssessmentType {
  QUIZ = 'quiz',
  EXAM = 'exam',
  SURVEY = 'survey',
  ASSIGNMENT = 'assignment',
  PROJECT = 'project',
  PEER_REVIEW = 'peer_review'
}

export enum QuestionType {
  MULTIPLE_CHOICE = 'multiple_choice',
  TRUE_FALSE = 'true_false',
  SHORT_ANSWER = 'short_answer',
  ESSAY = 'essay',
  MATCHING = 'matching',
  DRAG_DROP = 'drag_drop',
  FILL_BLANK = 'fill_blank',
  HOTSPOT = 'hotspot',
  SEQUENCE = 'sequence'
}

export enum InteractionType {
  BUTTON = 'button',
  HOTSPOT = 'hotspot',
  DRAG_DROP = 'drag_drop',
  ACCORDION = 'accordion',
  TABS = 'tabs',
  TIMELINE = 'timeline',
  CAROUSEL = 'carousel',
  FLIP_CARD = 'flip_card',
  BRANCHING = 'branching',
  SIMULATION = 'simulation'
}

export enum ResourceType {
  PDF = 'pdf',
  DOC = 'doc',
  SPREADSHEET = 'spreadsheet',
  PRESENTATION = 'presentation',
  VIDEO = 'video',
  AUDIO = 'audio',
  IMAGE = 'image',
  LINK = 'link',
  ZIP = 'zip'
}

export enum MediaType {
  IMAGE = 'image',
  VIDEO = 'video',
  AUDIO = 'audio',
  DOCUMENT = 'document',
  ANIMATION = 'animation',
  INTERACTIVE = 'interactive'
}

export enum ReviewStatus {
  PENDING = 'Pending',
  IN_PROGRESS = 'InProgress',
  APPROVED = 'Approved',
  REJECTED = 'Rejected',
  NEEDS_CHANGES = 'NeedsChanges'
}

export enum UserRole {
  ADMIN = 'admin',
  INSTRUCTOR = 'instructor',
  CONTENT_CREATOR = 'content_creator',
  REVIEWER = 'reviewer',
  LEARNER = 'learner'
}

export enum DifficultyLevel {
  BEGINNER = 'beginner',
  INTERMEDIATE = 'intermediate',
  ADVANCED = 'advanced',
  EXPERT = 'expert'
}

// Configuration Constants
export const DEFAULT_AUTO_SAVE_INTERVAL = 30000; // 30 seconds
export const DEFAULT_PAGINATION_LIMIT = 20;
export const MAX_FILE_SIZE = 100 * 1024 * 1024; // 100MB
export const SUPPORTED_VIDEO_FORMATS = ['mp4', 'webm', 'avi', 'mov'];
export const SUPPORTED_AUDIO_FORMATS = ['mp3', 'wav', 'ogg', 'm4a'];
export const SUPPORTED_IMAGE_FORMATS = ['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp'];
export const SUPPORTED_DOCUMENT_FORMATS = ['pdf', 'doc', 'docx', 'txt', 'rtf'];

// SCORM Configuration
export const SCORM_VERSIONS = ['1.2', '2004'] as const;
export type SCORMVersion = typeof SCORM_VERSIONS[number];

// Legacy constants for backward compatibility
export const DIFFICULTY_LEVELS = ['Beginner', 'Intermediate', 'Advanced'] as const;
export const USER_ROLES = ['owner', 'editor', 'reviewer', 'viewer'] as const;

// Analytics Periods
export const ANALYTICS_GRANULARITY = ['hour', 'day', 'week', 'month'] as const;
export type AnalyticsGranularity = typeof ANALYTICS_GRANULARITY[number];