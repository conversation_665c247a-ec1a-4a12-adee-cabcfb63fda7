// Shared utility functions for Content Creators module

import { ContentStatus, ContentType, DifficultyLevel } from '../constants';

/**
 * Converts camelCase object keys to snake_case for API requests
 */
export function camelToSnakeCase(obj: Record<string, any>): Record<string, any> {
  const result: Record<string, any> = {};
  
  for (const [key, value] of Object.entries(obj)) {
    const snakeKey = key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
    result[snakeKey] = value;
  }
  
  return result;
}

/**
 * Converts snake_case object keys to camelCase for frontend use
 */
export function snakeToCamelCase(obj: Record<string, any>): Record<string, any> {
  const result: Record<string, any> = {};
  
  for (const [key, value] of Object.entries(obj)) {
    const camelKey = key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
    result[camelKey] = value;
  }
  
  return result;
}

/**
 * Formats file size in bytes to human readable format
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Formats duration in seconds to MM:SS or HH:MM:SS format
 */
export function formatDuration(seconds: number): string {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  if (hours > 0) {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }
  
  return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
}

/**
 * Generates a unique ID for content items
 */
export function generateId(): string {
  return Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
}

/**
 * Validates file type against allowed formats
 */
export function validateFileType(file: File, allowedTypes: string[]): boolean {
  const fileExtension = file.name.split('.').pop()?.toLowerCase();
  return fileExtension ? allowedTypes.includes(fileExtension) : false;
}

/**
 * Calculates completion percentage
 */
export function calculateCompletionPercentage(completed: number, total: number): number {
  if (total === 0) return 0;
  return Math.round((completed / total) * 100);
}

/**
 * Debounce function for search and auto-save
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * Validates content status transitions
 */
export function canTransitionStatus(from: ContentStatus, to: ContentStatus): boolean {
  const validTransitions: Record<ContentStatus, ContentStatus[]> = {
    [ContentStatus.DRAFT]: [ContentStatus.IN_REVIEW, ContentStatus.ARCHIVED],
    [ContentStatus.IN_REVIEW]: [ContentStatus.DRAFT, ContentStatus.APPROVED, ContentStatus.ARCHIVED],
    [ContentStatus.APPROVED]: [ContentStatus.PUBLISHED, ContentStatus.DRAFT, ContentStatus.ARCHIVED],
    [ContentStatus.PUBLISHED]: [ContentStatus.ARCHIVED, ContentStatus.DRAFT],
    [ContentStatus.ARCHIVED]: [ContentStatus.DRAFT]
  };
  
  return validTransitions[from]?.includes(to) ?? false;
}

/**
 * Gets content type icon name
 */
export function getContentTypeIcon(type: ContentType): string {
  const iconMap: Record<ContentType, string> = {
    [ContentType.VIDEO]: 'video',
    [ContentType.AUDIO]: 'audio',
    [ContentType.TEXT]: 'file-text',
    [ContentType.SLIDE]: 'presentation',
    [ContentType.INTERACTIVE]: 'mouse-pointer',
    [ContentType.DOCUMENT]: 'file',
    [ContentType.QUIZ]: 'help-circle',
    [ContentType.SIMULATION]: 'play-circle',
    [ContentType.SCORM]: 'package'
  };
  
  return iconMap[type] || 'file';
}

/**
 * Gets difficulty level color
 */
export function getDifficultyColor(level: DifficultyLevel): string {
  const colorMap: Record<DifficultyLevel, string> = {
    'Beginner': 'green',
    'Intermediate': 'yellow',
    'Advanced': 'red'
  };
  
  return colorMap[level] || 'gray';
}

/**
 * Sanitizes filename for safe storage
 */
export function sanitizeFilename(filename: string): string {
  return filename
    .replace(/[^a-zA-Z0-9.-]/g, '_')
    .replace(/_{2,}/g, '_')
    .replace(/^_|_$/g, '');
}