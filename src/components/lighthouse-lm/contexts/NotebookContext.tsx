import * as React from 'react';
import { Citation } from '../types/message';
import { useToast } from '../hooks/useToast';
import { lighthouseService, Source, ChatMessage, StudioDocument } from '../../../services/lighthouse/lighthouseService';

interface SelectedSource extends Source {
  selected?: boolean;
}

interface ActiveDocument extends StudioDocument {
  lastModified?: Date;
}

interface AIContext {
  assistantId: string;
  capabilities: string[];
  active: boolean;
  lastSuggestion?: string;
}

interface NotebookContextType {
  // Source Management
  selectedSources: SelectedSource[];
  activeSource: SelectedSource | null;
  setActiveSource: (source: SelectedSource | null) => void;
  addSourceToContext: (source: SelectedSource) => void;
  removeSourceFromContext: (sourceId: string) => void;
  clearSourceContext: () => void;
  
  // Studio Management
  activeDocument: ActiveDocument | null;
  setActiveDocument: (doc: ActiveDocument | null) => void;
  studioContent: string;
  setStudioContent: (content: string) => void;
  studioMode: 'edit' | 'preview' | 'split';
  setStudioMode: (mode: 'edit' | 'preview' | 'split') => void;
  
  // Chat Integration
  chatContext: string;
  setChatContext: (context: string) => void;
  insertToChat: (content: string) => void;
  appendToChat: (content: string) => void;
  selectedCitation: Citation | null;
  setSelectedCitation: (citation: Citation | null) => void;
  
  // AI Integration
  aiContext: AIContext | null;
  setAIContext: (context: AIContext | null) => void;
  aiSuggestions: string[];
  addAISuggestion: (suggestion: string) => void;
  clearAISuggestions: () => void;
  
  // Cross-Component Communication
  sendToStudio: (content: string, type: 'Note' | 'Diagram' | 'Slide' | 'Code' | 'Markdown' | 'RichText') => Promise<void>;
  sendToChat: (content: string, withContext?: boolean) => Promise<void>;
  requestSourceAnalysis: (sourceId: string) => Promise<any>;
  requestAIAssistance: (prompt: string, context?: any) => Promise<string>;
  
  // Synchronization
  syncEnabled: boolean;
  setSyncEnabled: (enabled: boolean) => void;
  lastSyncTime: Date | null;
  syncStatus: 'idle' | 'syncing' | 'error';
  
  // Workspace State
  focusMode: 'chat' | 'sources' | 'studio' | 'split';
  setFocusMode: (mode: 'chat' | 'sources' | 'studio' | 'split') => void;
  isFullscreen: boolean;
  setIsFullscreen: (fullscreen: boolean) => void;
  
  // Backend Integration
  sources: Source[];
  messages: ChatMessage[];
  isLoadingSources: boolean;
  isLoadingMessages: boolean;
  sendMessage: (content: string, options?: any) => Promise<ChatMessage | null>;
  addSource: (sourceData: any) => Promise<void>;
  deleteSource: (sourceId: string) => Promise<void>;
  
  // Events
  onSourceSelect: (handler: (source: SelectedSource) => void) => () => void;
  onStudioUpdate: (handler: (content: string) => void) => () => void;
  onChatMessage: (handler: (message: string) => void) => () => void;
  onAISuggestion: (handler: (suggestion: string) => void) => () => void;
}

const NotebookContext = React.createContext<NotebookContextType | undefined>(undefined);

export const useNotebookContext = () => {
  const context = React.useContext(NotebookContext);
  if (!context) {
    throw new Error('useNotebookContext must be used within NotebookProvider');
  }
  return context;
};

interface NotebookProviderProps {
  children: React.ReactNode;
  notebookId?: string;
}

export const NotebookProvider: React.FC<NotebookProviderProps> = ({ children, notebookId }) => {
  const { toast } = useToast();
  
  // Backend state
  const [sources, setSources] = React.useState<Source[]>([]);
  const [messages, setMessages] = React.useState<ChatMessage[]>([]);
  const [isLoadingSources, setIsLoadingSources] = React.useState(false);
  const [isLoadingMessages, setIsLoadingMessages] = React.useState(false);
  
  // Source State
  const [selectedSources, setSelectedSources] = React.useState<SelectedSource[]>([]);
  const [activeSource, setActiveSource] = React.useState<SelectedSource | null>(null);
  
  // Studio State
  const [activeDocument, setActiveDocument] = React.useState<ActiveDocument | null>(null);
  const [studioContent, setStudioContent] = React.useState('');
  const [studioMode, setStudioMode] = React.useState<'edit' | 'preview' | 'split'>('edit');
  
  // Chat State
  const [chatContext, setChatContext] = React.useState('');
  const [selectedCitation, setSelectedCitation] = React.useState<Citation | null>(null);
  const chatInsertRef = React.useRef<(content: string) => void>();
  
  // AI State
  const [aiContext, setAIContext] = React.useState<AIContext | null>(null);
  const [aiSuggestions, setAISuggestions] = React.useState<string[]>([]);
  
  // Sync State
  const [syncEnabled, setSyncEnabled] = React.useState(true);
  const [lastSyncTime, setLastSyncTime] = React.useState<Date | null>(null);
  const [syncStatus, setSyncStatus] = React.useState<'idle' | 'syncing' | 'error'>('idle');
  
  // Workspace State
  const [focusMode, setFocusMode] = React.useState<'chat' | 'sources' | 'studio' | 'split'>('split');
  const [isFullscreen, setIsFullscreen] = React.useState(false);
  
  // Event Handlers Storage
  const eventHandlers = React.useRef<{
    sourceSelect: Set<(source: SelectedSource) => void>;
    studioUpdate: Set<(content: string) => void>;
    chatMessage: Set<(message: string) => void>;
    aiSuggestion: Set<(suggestion: string) => void>;
  }>({
    sourceSelect: new Set(),
    studioUpdate: new Set(),
    chatMessage: new Set(),
    aiSuggestion: new Set(),
  });
  
  // Load sources and messages when notebookId changes
  React.useEffect(() => {
    if (!notebookId) return;
    
    const loadData = async () => {
      try {
        setIsLoadingSources(true);
        setIsLoadingMessages(true);
        
        // Load sources
        const sourcesData = await lighthouseService.getSources(notebookId);
        setSources(sourcesData);
        
        // Load messages
        const messagesData = await lighthouseService.getMessages(notebookId);
        setMessages(messagesData);
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to load notebook data",
          variant: "destructive",
        });
      } finally {
        setIsLoadingSources(false);
        setIsLoadingMessages(false);
      }
    };
    
    loadData();
  }, [notebookId, toast]);
  
  // Set up real-time subscriptions
  React.useEffect(() => {
    if (!notebookId) return;
    
    let unsubscribe: (() => void) | undefined;
    
    const setupSubscription = async () => {
      unsubscribe = await lighthouseService.subscribeToNotebook(notebookId, (event: any) => {
        setLastSyncTime(new Date());
        
        // Handle different event types
        if (event.type === 'source_added' || event.type === 'source_updated' || event.type === 'source_deleted') {
          // Reload sources
          lighthouseService.getSources(notebookId).then(setSources).catch(() => {});
        } else if (event.type === 'message_added' || event.type === 'message_updated' || event.type === 'message_deleted') {
          // Reload messages
          lighthouseService.getMessages(notebookId).then(setMessages).catch(() => {});
        }
      });
    };
    
    setupSubscription();
    
    return () => {
      unsubscribe?.();
    };
  }, [notebookId]);
  
  // Sync AI context with backend
  React.useEffect(() => {
    if (!notebookId || !syncEnabled) return;
    
    const syncContext = async () => {
      setSyncStatus('syncing');
      try {
        const context = await lighthouseService.syncContext(
          notebookId,
          selectedSources.map(s => s.id),
          activeDocument?.id,
          chatContext
        );
        
        setAIContext({
          assistantId: context.id,
          capabilities: ['chat', 'analyze', 'generate'],
          active: true,
        });
        
        setSyncStatus('idle');
        setLastSyncTime(new Date());
      } catch (error) {
        setSyncStatus('error');
      }
    };
    
    const debounceTimer = setTimeout(syncContext, 1000);
    return () => clearTimeout(debounceTimer);
  }, [notebookId, selectedSources, activeDocument, chatContext, syncEnabled]);
  
  // Source Management Functions
  const addSourceToContext = React.useCallback((source: SelectedSource) => {
    setSelectedSources(prev => {
      if (prev.find(s => s.id === source.id)) return prev;
      const updated = [...prev, source];
      
      // Notify listeners
      eventHandlers.current.sourceSelect.forEach(handler => handler(source));
      
      toast({
        title: "Source added",
        description: `"${source.title}" added to context`,
      });
      
      return updated;
    });
  }, [toast]);
  
  const removeSourceFromContext = React.useCallback((sourceId: string) => {
    setSelectedSources(prev => prev.filter(s => s.id !== sourceId));
  }, []);
  
  const clearSourceContext = React.useCallback(() => {
    setSelectedSources([]);
    setActiveSource(null);
  }, []);
  
  // Chat Functions
  const insertToChat = React.useCallback((content: string) => {
    chatInsertRef.current?.(content);
    setChatContext(prev => prev + '\n' + content);
  }, []);
  
  const appendToChat = React.useCallback((content: string) => {
    setChatContext(prev => prev + content);
  }, []);
  
  // AI Functions
  const addAISuggestion = React.useCallback((suggestion: string) => {
    setAISuggestions(prev => [...prev, suggestion]);
    eventHandlers.current.aiSuggestion.forEach(handler => handler(suggestion));
  }, []);
  
  const clearAISuggestions = React.useCallback(() => {
    setAISuggestions([]);
  }, []);
  
  // Backend Integration Functions
  const sendMessage = React.useCallback(async (
    content: string,
    options?: any
  ) => {
    if (!notebookId) return null;
    
    try {
      const message = await lighthouseService.sendMessage(notebookId, content, options);
      // Update local messages state
      setMessages(prev => [...prev, message]);
      return message;
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to send message",
        variant: "destructive",
      });
      return null;
    }
  }, [notebookId, toast]);
  
  const addSource = React.useCallback(async (sourceData: any) => {
    if (!notebookId) return;
    
    try {
      let newSource: Source;
      
      if (sourceData.file) {
        newSource = await lighthouseService.uploadFile(notebookId, sourceData.file);
      } else {
        newSource = await lighthouseService.addSource(
          notebookId,
          sourceData.title,
          sourceData.content || '',
          sourceData.type
        );
      }
      
      // Update local sources state
      setSources(prev => [...prev, newSource]);
      
      toast({
        title: "Source added",
        description: `"${newSource.title}" has been added to the notebook`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add source",
        variant: "destructive",
      });
    }
  }, [notebookId, toast]);
  
  const deleteSource = React.useCallback(async (sourceId: string) => {
    try {
      await lighthouseService.deleteSource(sourceId);
      // Update local sources state
      setSources(prev => prev.filter(s => s.id !== sourceId));
      
      toast({
        title: "Source deleted",
        description: "The source has been removed from the notebook",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete source",
        variant: "destructive",
      });
    }
  }, [toast]);
  
  // Cross-Component Communication
  const sendToStudio = React.useCallback(async (content: string, type: 'Note' | 'Diagram' | 'Slide' | 'Code' | 'Markdown' | 'RichText') => {
    if (!notebookId) return;
    
    try {
      const doc = await lighthouseService.createDocument(
        notebookId,
        `New ${type}`,
        content,
        type
      );
      
      setActiveDocument({
        ...doc,
        lastModified: new Date(doc.updated_at),
      });
      setStudioContent(content);
      setFocusMode('studio');
      
      eventHandlers.current.studioUpdate.forEach(handler => handler(content));
      
      toast({
        title: "Content sent to Studio",
        description: `Created new ${type.toLowerCase()} document`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create studio document",
        variant: "destructive",
      });
    }
  }, [notebookId, toast]);
  
  const sendToChat = React.useCallback(async (content: string, withContext = false) => {
    if (!notebookId) return;
    
    const messageContent = withContext 
      ? `${content}\n\nContext:\n${selectedSources.map(s => s.title).join(', ')}`
      : content;
    
    await sendMessage(messageContent, {
      sources: withContext ? selectedSources.map(s => s.id) : undefined,
    });
    
    eventHandlers.current.chatMessage.forEach(handler => handler(content));
    setFocusMode('chat');
  }, [notebookId, selectedSources, sendMessage]);
  
  const requestSourceAnalysis = React.useCallback(async (sourceId: string) => {
    if (!notebookId) return null;
    
    const source = sources.find(s => s.id === sourceId);
    if (!source) return null;
    
    try {
      const analysis = await sendMessage(
        `Please analyze this source: ${source.title}`,
        { sources: [sourceId] }
      );
      
      return analysis;
    } catch (error) {
      return null;
    }
  }, [notebookId, sources, sendMessage]);
  
  const requestAIAssistance = React.useCallback(async (prompt: string, context?: any) => {
    if (!notebookId) return '';
    
    try {
      setSyncStatus('syncing');
      
      const suggestions = await lighthouseService.generateSuggestions(notebookId, prompt);
      if (suggestions.length > 0) {
        addAISuggestion(suggestions[0]);
        setSyncStatus('idle');
        setLastSyncTime(new Date());
        return suggestions[0];
      }
      
      setSyncStatus('idle');
      return '';
    } catch (error) {
      setSyncStatus('error');
      return '';
    }
  }, [notebookId, addAISuggestion]);
  
  // Event Subscription Functions
  const onSourceSelect = React.useCallback((handler: (source: SelectedSource) => void) => {
    eventHandlers.current.sourceSelect.add(handler);
    return () => {
      eventHandlers.current.sourceSelect.delete(handler);
    };
  }, []);
  
  const onStudioUpdate = React.useCallback((handler: (content: string) => void) => {
    eventHandlers.current.studioUpdate.add(handler);
    return () => {
      eventHandlers.current.studioUpdate.delete(handler);
    };
  }, []);
  
  const onChatMessage = React.useCallback((handler: (message: string) => void) => {
    eventHandlers.current.chatMessage.add(handler);
    return () => {
      eventHandlers.current.chatMessage.delete(handler);
    };
  }, []);
  
  const onAISuggestion = React.useCallback((handler: (suggestion: string) => void) => {
    eventHandlers.current.aiSuggestion.add(handler);
    return () => {
      eventHandlers.current.aiSuggestion.delete(handler);
    };
  }, []);
  
  const value: NotebookContextType = {
    // Source Management
    selectedSources,
    activeSource,
    setActiveSource,
    addSourceToContext,
    removeSourceFromContext,
    clearSourceContext,
    
    // Studio Management
    activeDocument,
    setActiveDocument,
    studioContent,
    setStudioContent,
    studioMode,
    setStudioMode,
    
    // Chat Integration
    chatContext,
    setChatContext,
    insertToChat,
    appendToChat,
    selectedCitation,
    setSelectedCitation,
    
    // AI Integration
    aiContext,
    setAIContext,
    aiSuggestions,
    addAISuggestion,
    clearAISuggestions,
    
    // Cross-Component Communication
    sendToStudio,
    sendToChat,
    requestSourceAnalysis,
    requestAIAssistance,
    
    // Synchronization
    syncEnabled,
    setSyncEnabled,
    lastSyncTime,
    syncStatus,
    
    // Workspace State
    focusMode,
    setFocusMode,
    isFullscreen,
    setIsFullscreen,
    
    // Backend Integration
    sources,
    messages,
    isLoadingSources,
    isLoadingMessages,
    sendMessage,
    addSource,
    deleteSource,
    
    // Events
    onSourceSelect,
    onStudioUpdate,
    onChatMessage,
    onAISuggestion,
  };
  
  return (
    <NotebookContext.Provider value={value}>
      {children}
    </NotebookContext.Provider>
  );
};

export default NotebookContext;