import React, { createContext, useContext, useState, useCallback, useEffect, useRef } from 'react';
import { Citation } from '../types/message';
import { useToast } from '../hooks/useToast';

interface SelectedSource {
  id: string;
  title: string;
  content: string;
  type: string;
  metadata?: any;
}

interface ActiveDocument {
  id: string;
  title: string;
  content: string;
  type: 'note' | 'diagram' | 'slide' | 'code';
  lastModified: Date;
}

interface AIContext {
  assistantId: string;
  capabilities: string[];
  active: boolean;
  lastSuggestion?: string;
}

interface NotebookContextType {
  // Source Management
  selectedSources: SelectedSource[];
  activeSource: SelectedSource | null;
  setActiveSource: (source: SelectedSource | null) => void;
  addSourceToContext: (source: SelectedSource) => void;
  removeSourceFromContext: (sourceId: string) => void;
  clearSourceContext: () => void;
  
  // Studio Management
  activeDocument: ActiveDocument | null;
  setActiveDocument: (doc: ActiveDocument | null) => void;
  studioContent: string;
  setStudioContent: (content: string) => void;
  studioMode: 'edit' | 'preview' | 'split';
  setStudioMode: (mode: 'edit' | 'preview' | 'split') => void;
  
  // Chat Integration
  chatContext: string;
  setChatContext: (context: string) => void;
  insertToChat: (content: string) => void;
  appendToChat: (content: string) => void;
  selectedCitation: Citation | null;
  setSelectedCitation: (citation: Citation | null) => void;
  
  // AI Integration
  aiContext: AIContext | null;
  setAIContext: (context: AIContext | null) => void;
  aiSuggestions: string[];
  addAISuggestion: (suggestion: string) => void;
  clearAISuggestions: () => void;
  
  // Cross-Component Communication
  sendToStudio: (content: string, type: 'note' | 'diagram' | 'slide' | 'code') => void;
  sendToChat: (content: string, withContext?: boolean) => void;
  requestSourceAnalysis: (sourceId: string) => Promise<any>;
  requestAIAssistance: (prompt: string, context?: any) => Promise<string>;
  
  // Synchronization
  syncEnabled: boolean;
  setSyncEnabled: (enabled: boolean) => void;
  lastSyncTime: Date | null;
  syncStatus: 'idle' | 'syncing' | 'error';
  
  // Workspace State
  focusMode: 'chat' | 'sources' | 'studio' | 'split';
  setFocusMode: (mode: 'chat' | 'sources' | 'studio' | 'split') => void;
  isFullscreen: boolean;
  setIsFullscreen: (fullscreen: boolean) => void;
  
  // Events
  onSourceSelect: (handler: (source: SelectedSource) => void) => () => void;
  onStudioUpdate: (handler: (content: string) => void) => () => void;
  onChatMessage: (handler: (message: string) => void) => () => void;
  onAISuggestion: (handler: (suggestion: string) => void) => () => void;
}

const NotebookContext = createContext<NotebookContextType | undefined>(undefined);

export const useNotebookContext = () => {
  const context = useContext(NotebookContext);
  if (!context) {
    throw new Error('useNotebookContext must be used within NotebookProvider');
  }
  return context;
};

interface NotebookProviderProps {
  children: React.ReactNode;
  notebookId?: string;
}

export const NotebookProvider: React.FC<NotebookProviderProps> = ({ children, notebookId }) => {
  const { toast } = useToast();
  
  // Source State
  const [selectedSources, setSelectedSources] = useState<SelectedSource[]>([]);
  const [activeSource, setActiveSource] = useState<SelectedSource | null>(null);
  
  // Studio State
  const [activeDocument, setActiveDocument] = useState<ActiveDocument | null>(null);
  const [studioContent, setStudioContent] = useState('');
  const [studioMode, setStudioMode] = useState<'edit' | 'preview' | 'split'>('edit');
  
  // Chat State
  const [chatContext, setChatContext] = useState('');
  const [selectedCitation, setSelectedCitation] = useState<Citation | null>(null);
  const chatInsertRef = useRef<(content: string) => void>();
  
  // AI State
  const [aiContext, setAIContext] = useState<AIContext | null>(null);
  const [aiSuggestions, setAISuggestions] = useState<string[]>([]);
  
  // Sync State
  const [syncEnabled, setSyncEnabled] = useState(true);
  const [lastSyncTime, setLastSyncTime] = useState<Date | null>(null);
  const [syncStatus, setSyncStatus] = useState<'idle' | 'syncing' | 'error'>('idle');
  
  // Workspace State
  const [focusMode, setFocusMode] = useState<'chat' | 'sources' | 'studio' | 'split'>('split');
  const [isFullscreen, setIsFullscreen] = useState(false);
  
  // Event Handlers Storage
  const eventHandlers = useRef<{
    sourceSelect: Set<(source: SelectedSource) => void>;
    studioUpdate: Set<(content: string) => void>;
    chatMessage: Set<(message: string) => void>;
    aiSuggestion: Set<(suggestion: string) => void>;
  }>({
    sourceSelect: new Set(),
    studioUpdate: new Set(),
    chatMessage: new Set(),
    aiSuggestion: new Set(),
  });
  
  // Source Management Functions
  const addSourceToContext = useCallback((source: SelectedSource) => {
    setSelectedSources(prev => {
      if (prev.find(s => s.id === source.id)) return prev;
      const updated = [...prev, source];
      
      // Notify listeners
      eventHandlers.current.sourceSelect.forEach(handler => handler(source));
      
      toast({
        title: 'Source added to context',
        description: `${source.title} is now available in chat and studio`,
      });
      
      return updated;
    });
  }, [toast]);
  
  const removeSourceFromContext = useCallback((sourceId: string) => {
    setSelectedSources(prev => prev.filter(s => s.id !== sourceId));
  }, []);
  
  const clearSourceContext = useCallback(() => {
    setSelectedSources([]);
    setActiveSource(null);
  }, []);
  
  // Chat Integration Functions
  const insertToChat = useCallback((content: string) => {
    setChatContext(content);
    chatInsertRef.current?.(content);
    
    // Notify listeners
    eventHandlers.current.chatMessage.forEach(handler => handler(content));
  }, []);
  
  const appendToChat = useCallback((content: string) => {
    setChatContext(prev => prev + '\n' + content);
    chatInsertRef.current?.(content);
  }, []);
  
  // Cross-Component Communication
  const sendToStudio = useCallback((content: string, type: 'note' | 'diagram' | 'slide' | 'code') => {
    setActiveDocument({
      id: Date.now().toString(),
      title: 'Untitled',
      content,
      type,
      lastModified: new Date(),
    });
    setStudioContent(content);
    
    // Notify listeners
    eventHandlers.current.studioUpdate.forEach(handler => handler(content));
    
    toast({
      title: 'Sent to Studio',
      description: `Content added to studio as ${type}`,
    });
  }, [toast]);
  
  const sendToChat = useCallback((content: string, withContext: boolean = false) => {
    const finalContent = withContext && selectedSources.length > 0
      ? `Context from sources:\n${selectedSources.map(s => s.title).join(', ')}\n\n${content}`
      : content;
    
    insertToChat(finalContent);
  }, [selectedSources, insertToChat]);
  
  const requestSourceAnalysis = useCallback(async (sourceId: string): Promise<any> => {
    // Simulate API call for source analysis
    const source = selectedSources.find(s => s.id === sourceId);
    if (!source) throw new Error('Source not found');
    
    return {
      summary: `Analysis of ${source.title}`,
      keyPoints: ['Point 1', 'Point 2', 'Point 3'],
      sentiment: 'positive',
      readability: 8.5,
    };
  }, [selectedSources]);
  
  const requestAIAssistance = useCallback(async (prompt: string, context?: any): Promise<string> => {
    // Simulate AI assistance
    setSyncStatus('syncing');
    
    try {
      // Include selected sources in AI context
      const contextData = {
        sources: selectedSources.map(s => ({ id: s.id, title: s.title })),
        studio: activeDocument?.title,
        ...context,
      };
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const suggestion = `AI suggestion for: ${prompt}`;
      addAISuggestion(suggestion);
      
      setSyncStatus('idle');
      setLastSyncTime(new Date());
      
      return suggestion;
    } catch (error) {
      setSyncStatus('error');
      throw error;
    }
  }, [selectedSources, activeDocument]);
  
  const addAISuggestion = useCallback((suggestion: string) => {
    setAISuggestions(prev => [...prev, suggestion]);
    
    // Notify listeners
    eventHandlers.current.aiSuggestion.forEach(handler => handler(suggestion));
  }, []);
  
  const clearAISuggestions = useCallback(() => {
    setAISuggestions([]);
  }, []);
  
  // Event Subscription Functions
  const onSourceSelect = useCallback((handler: (source: SelectedSource) => void) => {
    eventHandlers.current.sourceSelect.add(handler);
    return () => eventHandlers.current.sourceSelect.delete(handler);
  }, []);
  
  const onStudioUpdate = useCallback((handler: (content: string) => void) => {
    eventHandlers.current.studioUpdate.add(handler);
    return () => eventHandlers.current.studioUpdate.delete(handler);
  }, []);
  
  const onChatMessage = useCallback((handler: (message: string) => void) => {
    eventHandlers.current.chatMessage.add(handler);
    return () => eventHandlers.current.chatMessage.delete(handler);
  }, []);
  
  const onAISuggestion = useCallback((handler: (suggestion: string) => void) => {
    eventHandlers.current.aiSuggestion.add(handler);
    return () => eventHandlers.current.aiSuggestion.delete(handler);
  }, []);
  
  // Auto-sync when sources change
  useEffect(() => {
    if (!syncEnabled || selectedSources.length === 0) return;
    
    const syncData = async () => {
      setSyncStatus('syncing');
      try {
        // Sync selected sources to chat context
        const sourceContext = selectedSources
          .map(s => `[${s.title}]: ${s.content.substring(0, 200)}...`)
          .join('\n');
        
        setChatContext(prev => {
          if (prev.includes('[Sources]')) {
            return prev.replace(/\[Sources\][\s\S]*?\[\/Sources\]/, `[Sources]\n${sourceContext}\n[/Sources]`);
          }
          return `${prev}\n\n[Sources]\n${sourceContext}\n[/Sources]`;
        });
        
        setSyncStatus('idle');
        setLastSyncTime(new Date());
      } catch (error) {
        setSyncStatus('error');
        console.error('Sync failed:', error);
      }
    };
    
    const timer = setTimeout(syncData, 1000);
    return () => clearTimeout(timer);
  }, [selectedSources, syncEnabled]);
  
  // Register chat insert function
  const registerChatInsert = useCallback((insertFn: (content: string) => void) => {
    chatInsertRef.current = insertFn;
  }, []);
  
  const value: NotebookContextType = {
    // Source Management
    selectedSources,
    activeSource,
    setActiveSource,
    addSourceToContext,
    removeSourceFromContext,
    clearSourceContext,
    
    // Studio Management
    activeDocument,
    setActiveDocument,
    studioContent,
    setStudioContent,
    studioMode,
    setStudioMode,
    
    // Chat Integration
    chatContext,
    setChatContext,
    insertToChat,
    appendToChat,
    selectedCitation,
    setSelectedCitation,
    
    // AI Integration
    aiContext,
    setAIContext,
    aiSuggestions,
    addAISuggestion,
    clearAISuggestions,
    
    // Cross-Component Communication
    sendToStudio,
    sendToChat,
    requestSourceAnalysis,
    requestAIAssistance,
    
    // Synchronization
    syncEnabled,
    setSyncEnabled,
    lastSyncTime,
    syncStatus,
    
    // Workspace State
    focusMode,
    setFocusMode,
    isFullscreen,
    setIsFullscreen,
    
    // Events
    onSourceSelect,
    onStudioUpdate,
    onChatMessage,
    onAISuggestion,
  };
  
  return (
    <NotebookContext.Provider value={value}>
      {children}
    </NotebookContext.Provider>
  );
};

export default NotebookContext;