
import { useToast } from '../../hooks/useToast';

export const useLogout = () => {
  const { toast } = useToast();

  const logout = async () => {
    // Since there's no actual authentication, just show a message
    toast({
      title: "Session ended",
      description: "Your session has ended.",
      variant: "default"
    });
    
    // Could redirect to a login page if needed
    // For now, just log
  };

  return { logout };
};
