import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { lighthouseService, Notebook } from '../../../services/lighthouse/lighthouseService';
import { useToast } from './useToast';

export const useNotebooksWithBackend = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  // Fetch notebooks
  const {
    data: notebooks = [],
    isLoading,
    error,
    isError,
  } = useQuery({
    queryKey: ['notebooks'],
    queryFn: async () => {
      try {
        const notebooksData = await lighthouseService.listNotebooks();
        return notebooksData || [];
      } catch (err) {
        console.error('Error fetching notebooks:', err);
        throw err;
      }
    },
    retry: (failureCount, error) => {
      // Don't retry on auth errors
      if (error?.message?.includes('JWT') || error?.message?.includes('auth')) {
        return false;
      }
      return failureCount < 3;
    },
  });

  // Create notebook mutation
  const createNotebookMutation = useMutation({
    mutationFn: async (notebookData: { title: string; description?: string }) => {
      return await lighthouseService.createNotebook(notebookData.title, notebookData.description);
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['notebooks'] });
      toast({
        title: "Notebook created",
        description: `"${data.title}" has been created successfully.`,
      });
    },
    onError: (error) => {
      console.error('Error creating notebook:', error);
      toast({
        title: "Error",
        description: "Failed to create notebook. Please try again.",
        variant: "destructive",
      });
    },
  });

  // Update notebook mutation
  const updateNotebookMutation = useMutation({
    mutationFn: async ({ id, updates }: { id: string; updates: Partial<Notebook> }) => {
      return await lighthouseService.updateNotebook(id, updates);
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['notebooks'] });
      queryClient.invalidateQueries({ queryKey: ['notebook', data.id] });
      toast({
        title: "Notebook updated",
        description: "Changes have been saved successfully.",
      });
    },
    onError: (error) => {
      console.error('Error updating notebook:', error);
      toast({
        title: "Error",
        description: "Failed to update notebook. Please try again.",
        variant: "destructive",
      });
    },
  });

  // Delete notebook mutation
  const deleteNotebookMutation = useMutation({
    mutationFn: async (id: string) => {
      await lighthouseService.deleteNotebook(id);
      return id;
    },
    onSuccess: (id) => {
      queryClient.invalidateQueries({ queryKey: ['notebooks'] });
      queryClient.removeQueries({ queryKey: ['notebook', id] });
      queryClient.removeQueries({ queryKey: ['sources', id] });
      queryClient.removeQueries({ queryKey: ['messages', id] });
      toast({
        title: "Notebook deleted",
        description: "The notebook has been permanently deleted.",
      });
    },
    onError: (error) => {
      console.error('Error deleting notebook:', error);
      toast({
        title: "Error",
        description: "Failed to delete notebook. Please try again.",
        variant: "destructive",
      });
    },
  });

  // Share notebook mutation
  const shareNotebookMutation = useMutation({
    mutationFn: async ({
      notebookId,
      email,
      permission,
    }: {
      notebookId: string;
      email: string;
      permission: 'read' | 'write';
    }) => {
      await lighthouseService.shareNotebook(notebookId, email, permission);
    },
    onSuccess: () => {
      toast({
        title: "Notebook shared",
        description: "The notebook has been shared successfully.",
      });
    },
    onError: (error) => {
      console.error('Error sharing notebook:', error);
      toast({
        title: "Error",
        description: "Failed to share notebook. Please try again.",
        variant: "destructive",
      });
    },
  });

  // Get single notebook
  const getNotebook = async (id: string) => {
    return await queryClient.fetchQuery({
      queryKey: ['notebook', id],
      queryFn: () => lighthouseService.getNotebook(id),
    });
  };

  // Get collaborators
  const getCollaborators = async (notebookId: string) => {
    return await lighthouseService.getCollaborators(notebookId);
  };

  // Get analytics
  const getAnalytics = async (notebookId: string) => {
    return await lighthouseService.getAnalytics(notebookId);
  };

  return {
    notebooks,
    isLoading,
    error: error?.message || null,
    isError,
    createNotebook: createNotebookMutation.mutate,
    isCreating: createNotebookMutation.isPending,
    updateNotebook: updateNotebookMutation.mutate,
    isUpdating: updateNotebookMutation.isPending,
    deleteNotebook: deleteNotebookMutation.mutate,
    isDeleting: deleteNotebookMutation.isPending,
    shareNotebook: shareNotebookMutation.mutate,
    isSharing: shareNotebookMutation.isPending,
    getNotebook,
    getCollaborators,
    getAnalytics,
  };
};