import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { lighthouseService, StudioDocument, DocumentType, ExportOptions } from '../../../services/lighthouse/lighthouseService';
import { useToast } from './useToast';

export const useStudioDocuments = (notebookId?: string) => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  // Fetch documents
  const {
    data: documents = [],
    isLoading,
    error,
  } = useQuery({
    queryKey: ['studio-documents', notebookId],
    queryFn: async () => {
      if (!notebookId) return [];
      
      // Since lighthouseService doesn't have a direct getDocuments method,
      // we'll need to fetch from the backend
      try {
        const { invoke } = await import('@tauri-apps/api/core');
        const docs = await invoke<StudioDocument[]>('get_studio_documents', {
          notebook_id: notebookId,
        });
        return docs || [];
      } catch (err) {
        throw err;
      }
    },
    enabled: !!notebookId,
    refetchInterval: false,
  });

  // Create document mutation
  const createDocumentMutation = useMutation({
    mutationFn: async ({
      title,
      content,
      documentType,
    }: {
      title: string;
      content: string;
      documentType: DocumentType;
    }) => {
      if (!notebookId) throw new Error('Notebook ID required');
      return await lighthouseService.createDocument(notebookId, title, content, documentType);
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['studio-documents', notebookId] });
      toast({
        title: "Document created",
        description: `"${data.title}" has been created successfully.`,
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to create document. Please try again.",
        variant: "destructive",
      });
    },
  });

  // Update document mutation
  const updateDocumentMutation = useMutation({
    mutationFn: async ({
      id,
      updates,
    }: {
      id: string;
      updates: Partial<StudioDocument>;
    }) => {
      return await lighthouseService.updateDocument(id, updates);
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['studio-documents', notebookId] });
      toast({
        title: "Document updated",
        description: "Changes have been saved successfully.",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to update document. Please try again.",
        variant: "destructive",
      });
    },
  });

  // Delete document mutation
  const deleteDocumentMutation = useMutation({
    mutationFn: async (documentId: string) => {
      const { invoke } = await import('@tauri-apps/api/core');
      await invoke('delete_studio_document', { id: documentId });
      return documentId;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['studio-documents', notebookId] });
      toast({
        title: "Document deleted",
        description: "The document has been permanently deleted.",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to delete document. Please try again.",
        variant: "destructive",
      });
    },
  });

  // Export document mutation
  const exportDocumentMutation = useMutation({
    mutationFn: async ({
      documentId,
      options,
    }: {
      documentId: string;
      options: ExportOptions;
    }) => {
      return await lighthouseService.exportDocument(documentId, options);
    },
    onSuccess: (blob, variables) => {
      // Create download link
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `document.${variables.options.format}`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      toast({
        title: "Document exported",
        description: `Document exported as ${variables.options.format.toUpperCase()} successfully.`,
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to export document. Please try again.",
        variant: "destructive",
      });
    },
  });

  // Publish document mutation
  const publishDocumentMutation = useMutation({
    mutationFn: async ({
      documentId,
      platform,
    }: {
      documentId: string;
      platform: string;
    }) => {
      return await lighthouseService.publishDocument(documentId, platform);
    },
    onSuccess: (url) => {
      toast({
        title: "Document published",
        description: "Your document has been published successfully.",
        action: {
          label: "View",
          onClick: () => window.open(url, '_blank'),
        },
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to publish document. Please try again.",
        variant: "destructive",
      });
    },
  });

  // Duplicate document
  const duplicateDocument = async (documentId: string) => {
    const document = documents.find(d => d.id === documentId);
    if (!document || !notebookId) return;
    
    await createDocumentMutation.mutateAsync({
      title: `${document.title} (Copy)`,
      content: document.content,
      documentType: document.document_type,
    });
  };

  // Get document by ID
  const getDocument = (documentId: string) => {
    return documents.find(d => d.id === documentId);
  };

  // Search documents
  const searchDocuments = (query: string) => {
    const lowercaseQuery = query.toLowerCase();
    return documents.filter(doc => 
      doc.title.toLowerCase().includes(lowercaseQuery) ||
      doc.content.toLowerCase().includes(lowercaseQuery)
    );
  };

  // Get documents by type
  const getDocumentsByType = (type: DocumentType) => {
    return documents.filter(doc => doc.document_type === type);
  };

  // Get recent documents
  const getRecentDocuments = (limit = 5) => {
    return [...documents]
      .sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime())
      .slice(0, limit);
  };

  // Real-time subscription
  const subscribeToUpdates = async (onUpdate: (event: any) => void) => {
    if (!notebookId) return () => {};
    
    return await lighthouseService.subscribeToNotebook(notebookId, (event) => {
      onUpdate(event);
      
      // Invalidate queries based on event type
      if (event.type === 'document_created' || event.type === 'document_updated' || event.type === 'document_deleted') {
        queryClient.invalidateQueries({ queryKey: ['studio-documents', notebookId] });
      }
    });
  };

  return {
    documents,
    isLoading,
    error: error?.message || null,
    createDocument: createDocumentMutation.mutate,
    isCreating: createDocumentMutation.isPending,
    updateDocument: updateDocumentMutation.mutate,
    isUpdating: updateDocumentMutation.isPending,
    deleteDocument: deleteDocumentMutation.mutate,
    isDeleting: deleteDocumentMutation.isPending,
    exportDocument: exportDocumentMutation.mutate,
    isExporting: exportDocumentMutation.isPending,
    publishDocument: publishDocumentMutation.mutate,
    isPublishing: publishDocumentMutation.isPending,
    duplicateDocument,
    getDocument,
    searchDocuments,
    getDocumentsByType,
    getRecentDocuments,
    subscribeToUpdates,
  };
};