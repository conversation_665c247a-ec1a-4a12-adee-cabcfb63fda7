import { useMutation, useQueryClient, UseMutationOptions, MutationFunction } from '@tanstack/react-query';
import { useToast } from './useToast';

export interface MutationConfig<TData = unknown, TVariables = unknown, TError = Error> {
  mutationFn: MutationFunction<TData, TVariables>;
  invalidateQueries?: string[][];
  successMessage?: string | ((data: TData) => string);
  errorMessage?: string | ((error: TError) => string);
  onSuccess?: (data: TData, variables: TVariables) => void;
  onError?: (error: TError, variables: TVariables) => void;
  options?: Omit<UseMutationOptions<TData, TError, TVariables>, 'mutationFn' | 'onSuccess' | 'onError'>;
}

/**
 * Generic hook for mutations with toast notifications
 * Eliminates duplication across update/delete/create hooks
 */
export function useMutationWithToast<TData = unknown, TVariables = unknown, TError = Error>(
  config: MutationConfig<TData, TVariables, TError>
) {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const mutation = useMutation<TData, TError, TVariables>({
    mutationFn: config.mutationFn,
    onSuccess: (data, variables) => {
      // Invalidate queries
      if (config.invalidateQueries) {
        config.invalidateQueries.forEach(queryKey => {
          queryClient.invalidateQueries({ queryKey });
        });
      }

      // Show success toast
      if (config.successMessage) {
        const message = typeof config.successMessage === 'function' 
          ? config.successMessage(data) 
          : config.successMessage;
        
        toast({
          title: "Success",
          description: message,
        });
      }

      // Call custom success handler
      config.onSuccess?.(data, variables);
    },
    onError: (error, variables) => {
      // Show error toast
      if (config.errorMessage) {
        const message = typeof config.errorMessage === 'function'
          ? config.errorMessage(error)
          : config.errorMessage;
        
        toast({
          title: "Error",
          description: message,
          variant: "destructive",
        });
      } else {
        // Default error message
        toast({
          title: "Error",
          description: error instanceof Error ? error.message : "An error occurred",
          variant: "destructive",
        });
      }

      // Call custom error handler
      config.onError?.(error, variables);
    },
    ...config.options,
  });

  return {
    mutate: mutation.mutate,
    mutateAsync: mutation.mutateAsync,
    isPending: mutation.isPending,
    isSuccess: mutation.isSuccess,
    isError: mutation.isError,
    data: mutation.data,
    error: mutation.error,
    reset: mutation.reset,
  };
}

/**
 * Preset configurations for common operations
 */
export const mutationPresets = {
  create: (entityName: string) => ({
    successMessage: `${entityName} created successfully`,
    errorMessage: `Failed to create ${entityName}`,
  }),
  
  update: (entityName: string) => ({
    successMessage: `${entityName} updated successfully`,
    errorMessage: `Failed to update ${entityName}`,
  }),
  
  delete: (entityName: string) => ({
    successMessage: `${entityName} deleted successfully`,
    errorMessage: `Failed to delete ${entityName}`,
  }),
  
  upload: (entityName: string) => ({
    successMessage: `${entityName} uploaded successfully`,
    errorMessage: `Failed to upload ${entityName}`,
  }),
};