import { useState, useEffect, useRef, useCallback } from 'react';
import { useToast } from './useToast';
import { initializeMermaid, renderMermaidDiagram } from '../utils/mermaidConfig';
import { generateSampleDiagram } from '../utils/mermaidTypes';
import { EnhancedExportService, ExportOptions } from '../../../services/enhancedExportService';
import { SourceDiagram } from '../../../services/sourceDiagramService';
import { useNotificationService } from '../services/notification';

// Initialize mermaid on module load
initializeMermaid();

/**
 * Unified Mermaid types
 */
export interface MermaidDiagram {
  id: string;
  type: string;
  code: string;
  title: string;
  description?: string;
  createdAt: Date;
  updatedAt: Date;
  sourceMessages?: string[];
}

export interface ChatMessage {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: Date;
}

export interface UnifiedMermaidOptions {
  // Single diagram options
  initialDiagram?: string;
  onDiagramChange?: (diagram: string) => void;
  autoRender?: boolean;
  sourceMap?: Record<string, string[]>;
  onNodeClick?: (nodeId: string, sourceIds: string[]) => void;
  highlightedNodes?: string[];
  sourceDiagram?: SourceDiagram;
  enableEnhancedExport?: boolean;
  
  // Multiple diagrams options
  chatMessages?: ChatMessage[];
  autoGenerate?: boolean;
  maxMessages?: number;
  onDiagramGenerated?: (diagram: MermaidDiagram) => void;
}

/**
 * Add source interactivity to rendered Mermaid diagram
 */
const addSourceInteractivity = (
  container: HTMLElement,
  sourceMap: Record<string, string[]>,
  onNodeClick?: (nodeId: string, sourceIds: string[]) => void,
  highlightedNodes: string[] = []
) => {
  const clickableElements = container.querySelectorAll('g.node, g.edgeLabel, rect, circle, ellipse, polygon');
  
  clickableElements.forEach((element) => {
    const nodeElement = element as HTMLElement;
    
    let nodeId = nodeElement.id || 
                 nodeElement.getAttribute('data-id') ||
                 nodeElement.getAttribute('class')?.match(/node-(\w+)/)?.[1] ||
                 nodeElement.textContent?.trim();
    
    if (!nodeId) return;
    
    nodeId = nodeId.replace(/^(flowchart-|node-|edge-)/i, '').replace(/[^a-zA-Z0-9_-]/g, '');
    
    const sourceIds = sourceMap[nodeId] || [];
    const isHighlighted = highlightedNodes.includes(nodeId);
    const hasSourceAttribution = sourceIds.length > 0;
    
    if (hasSourceAttribution) {
      nodeElement.style.cursor = 'pointer';
      nodeElement.style.transition = 'all 0.2s ease';
      
      const rect = nodeElement.querySelector('rect, circle, ellipse, polygon');
      if (rect) {
        rect.setAttribute('stroke-width', '2');
        rect.setAttribute('stroke-dasharray', '3,3');
        rect.setAttribute('stroke', '#3b82f6');
        rect.setAttribute('stroke-opacity', '0.6');
      }
    }
    
    if (isHighlighted) {
      nodeElement.style.filter = 'drop-shadow(0 0 8px #fbbf24)';
      const rect = nodeElement.querySelector('rect, circle, ellipse, polygon');
      if (rect) {
        rect.setAttribute('stroke', '#f59e0b');
        rect.setAttribute('stroke-width', '3');
        rect.setAttribute('stroke-opacity', '1');
      }
    }
    
    const addHoverEffect = () => {
      if (hasSourceAttribution) {
        nodeElement.style.transform = 'scale(1.05)';
        nodeElement.style.filter = (nodeElement.style.filter || '') + ' brightness(1.1)';
      }
    };
    
    const removeHoverEffect = () => {
      if (hasSourceAttribution) {
        nodeElement.style.transform = 'scale(1)';
        nodeElement.style.filter = nodeElement.style.filter?.replace(' brightness(1.1)', '') || '';
      }
    };
    
    if (hasSourceAttribution) {
      nodeElement.addEventListener('mouseenter', addHoverEffect);
      nodeElement.addEventListener('mouseleave', removeHoverEffect);
      
      if (onNodeClick) {
        nodeElement.addEventListener('click', (event) => {
          event.preventDefault();
          event.stopPropagation();
          onNodeClick(nodeId!, sourceIds);
        });
      }
      
      nodeElement.title = `Click to view ${sourceIds.length} source${sourceIds.length > 1 ? 's' : ''}`;
    }
  });
};

/**
 * Unified Mermaid hook that combines single and multiple diagram management
 */
export const useUnifiedMermaid = (options: UnifiedMermaidOptions = {}) => {
  const {
    initialDiagram = '',
    onDiagramChange,
    autoRender = true,
    sourceMap = {},
    onNodeClick,
    highlightedNodes = [],
    sourceDiagram,
    enableEnhancedExport = false,
    chatMessages = [],
    autoGenerate = false,
    maxMessages = 10,
    onDiagramGenerated,
  } = options;

  // State for single diagram management
  const containerRef = useRef<HTMLDivElement>(null);
  const [zoom, setZoom] = useState(100);
  const [error, setError] = useState<string | null>(null);
  const [localDiagram, setLocalDiagram] = useState(initialDiagram);
  const [isEditing, setIsEditing] = useState(false);
  
  // State for multiple diagrams management
  const [diagrams, setDiagrams] = useState<MermaidDiagram[]>([]);
  const [selectedDiagram, setSelectedDiagram] = useState<MermaidDiagram | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [diagramType, setDiagramType] = useState('flowchart');
  const [autoGenerateEnabled, setAutoGenerateEnabled] = useState(autoGenerate);
  const [maxMessagesLimit, setMaxMessagesLimit] = useState(maxMessages);
  const lastMessageCountRef = useRef(0);
  
  const { toast } = useToast();
  const { copyToClipboard, exportToFile } = useNotificationService();

  // Update local diagram when initial diagram changes
  useEffect(() => {
    setLocalDiagram(initialDiagram);
  }, [initialDiagram]);

  // Render single diagram
  useEffect(() => {
    if (!autoRender || !containerRef.current || !localDiagram) return;

    const renderDiagram = async () => {
      try {
        containerRef.current!.innerHTML = '';
        setError(null);
        
        const id = `mermaid-${Date.now()}`;
        const mermaidDiv = document.createElement('div');
        mermaidDiv.id = id;
        mermaidDiv.style.transform = `scale(${zoom / 100})`;
        mermaidDiv.style.transformOrigin = 'top left';
        
        containerRef.current!.appendChild(mermaidDiv);
        
        const { svg, error: renderError } = await renderMermaidDiagram(id, localDiagram);
        
        if (renderError) {
          throw new Error(renderError);
        }
        
        mermaidDiv.innerHTML = svg;
        
        const svgElement = mermaidDiv.querySelector('svg');
        if (svgElement) {
          svgElement.style.maxWidth = '100%';
          svgElement.style.height = 'auto';
        }

        addSourceInteractivity(mermaidDiv, sourceMap, onNodeClick, highlightedNodes);
        
      } catch (error: any) {
        console.error('Failed to render mermaid diagram:', error);
        setError(error.message || 'Failed to render diagram');
        if (containerRef.current) {
          containerRef.current.innerHTML = `
            <div class="flex flex-col items-center justify-center h-full text-muted-foreground">
              <div class="mb-4">⚠️</div>
              <div class="text-center">
                <p class="font-medium mb-2">Failed to render diagram</p>
                <p class="text-sm">${error.message || 'Invalid Mermaid syntax'}</p>
              </div>
            </div>
          `;
        }
      }
    };

    renderDiagram();
  }, [localDiagram, zoom, autoRender, sourceMap, highlightedNodes, onNodeClick]);

  // Auto-generate diagrams when new messages arrive
  useEffect(() => {
    if (autoGenerateEnabled && chatMessages.length > lastMessageCountRef.current && chatMessages.length >= 3) {
      const newMessageCount = chatMessages.length - lastMessageCountRef.current;
      if (newMessageCount >= 2) {
        generateMermaidDiagram('flowchart');
      }
    }
    lastMessageCountRef.current = chatMessages.length;
  }, [chatMessages, autoGenerateEnabled]);

  // Diagram generation
  const generateMermaidDiagram = useCallback(async (
    type: string = 'flowchart',
    customPrompt?: string
  ) => {
    if (!chatMessages || chatMessages.length === 0) return;
    
    setIsGenerating(true);
    try {
      const recentMessages = chatMessages.slice(-maxMessagesLimit);
      const conversationContext = recentMessages
        .map(msg => `${msg.role}: ${msg.content}`)
        .join('\n\n');
      
      const basePrompt = customPrompt || 
        `Based on the following conversation, create a ${type} diagram that visualizes the key concepts, relationships, or processes discussed:\n\n${conversationContext}`;
      
      console.log('Generating diagram with prompt:', basePrompt);
      
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const { code: sampleCode, title } = generateSampleDiagram(type);
      
      const newDiagram: MermaidDiagram = {
        id: `diagram-${Date.now()}`,
        type,
        code: sampleCode,
        title: `${title} - ${new Date().toLocaleTimeString()}`,
        description: `Generated from ${recentMessages.length} messages`,
        createdAt: new Date(),
        updatedAt: new Date(),
        sourceMessages: recentMessages.map(msg => msg.id)
      };
      
      setDiagrams(prev => [newDiagram, ...prev]);
      setSelectedDiagram(newDiagram);
      
      onDiagramGenerated?.(newDiagram);
      
    } catch (error) {
      console.error('Failed to generate diagram:', error);
      toast({
        title: 'Generation Failed',
        description: 'Failed to generate diagram',
        variant: 'destructive',
      });
    } finally {
      setIsGenerating(false);
    }
  }, [chatMessages, maxMessagesLimit, onDiagramGenerated, toast]);

  // Copy operations
  const handleCopy = useCallback(async () => {
    await copyToClipboard(localDiagram);
  }, [localDiagram, copyToClipboard]);

  const handleCopyDiagram = useCallback((diagram: MermaidDiagram) => {
    navigator.clipboard.writeText(diagram.code);
  }, []);

  // Export operations
  const handleExportCode = useCallback((title?: string, exportOptions?: ExportOptions) => {
    if (enableEnhancedExport && sourceDiagram) {
      try {
        EnhancedExportService.downloadDiagramWithAttribution(
          sourceDiagram,
          'mmd',
          exportOptions
        );
        return;
      } catch (error) {
        console.error('Enhanced code export failed:', error);
      }
    }

    exportToFile(
      localDiagram,
      `${title || 'mermaid-diagram'}.mmd`,
      'text/plain'
    );
  }, [enableEnhancedExport, sourceDiagram, localDiagram, exportToFile]);

  const handleExportSVG = useCallback(async (title?: string, exportOptions?: ExportOptions) => {
    if (enableEnhancedExport && sourceDiagram) {
      try {
        await EnhancedExportService.downloadDiagramWithAttribution(
          sourceDiagram,
          'svg',
          exportOptions
        );
        return;
      } catch (error) {
        console.error('Enhanced SVG export failed:', error);
      }
    }

    const svgElement = containerRef.current?.querySelector('svg');
    if (!svgElement) {
      toast({
        title: 'Export Failed',
        description: 'No diagram to export',
        variant: 'destructive',
      });
      return;
    }

    const svgString = new XMLSerializer().serializeToString(svgElement);
    exportToFile(
      svgString,
      `${title || 'mermaid-diagram'}.svg`,
      'image/svg+xml'
    );
  }, [enableEnhancedExport, sourceDiagram, exportToFile, toast]);

  const handleExportPNG = useCallback(async (title?: string, exportOptions?: ExportOptions) => {
    if (enableEnhancedExport && sourceDiagram) {
      try {
        await EnhancedExportService.downloadDiagramWithAttribution(
          sourceDiagram,
          'png',
          exportOptions
        );
        return;
      } catch (error) {
        console.error('Enhanced PNG export failed:', error);
      }
    }

    const svgElement = containerRef.current?.querySelector('svg');
    if (!svgElement) {
      toast({
        title: 'Export Failed',
        description: 'No diagram to export',
        variant: 'destructive',
      });
      return;
    }

    try {
      const svgString = new XMLSerializer().serializeToString(svgElement);
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();
      
      const svgBlob = new Blob([svgString], { type: 'image/svg+xml;charset=utf-8' });
      const url = URL.createObjectURL(svgBlob);
      
      img.onload = () => {
        canvas.width = img.width;
        canvas.height = img.height;
        ctx?.drawImage(img, 0, 0);
        
        canvas.toBlob((blob) => {
          if (blob) {
            const pngUrl = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = pngUrl;
            a.download = `${title || 'mermaid-diagram'}.png`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(pngUrl);
          }
          URL.revokeObjectURL(url);
        }, 'image/png');
      };
      
      img.src = url;
    } catch (error) {
      console.error('Failed to export PNG:', error);
      toast({
        title: 'Export Failed',
        description: 'Failed to export diagram as PNG',
        variant: 'destructive',
      });
    }
  }, [enableEnhancedExport, sourceDiagram, toast]);

  const handleExportJSON = useCallback(async (exportOptions?: ExportOptions) => {
    if (!sourceDiagram) {
      toast({
        title: 'Export Failed',
        description: 'JSON export requires source diagram metadata',
        variant: 'destructive',
      });
      return;
    }

    try {
      await EnhancedExportService.downloadDiagramWithAttribution(
        sourceDiagram,
        'json',
        exportOptions
      );
    } catch (error) {
      console.error('Failed to export JSON:', error);
      toast({
        title: 'Export Failed',
        description: 'Failed to export diagram as JSON',
        variant: 'destructive',
      });
    }
  }, [sourceDiagram, toast]);

  const handleCreateShareableLink = useCallback(async (options?: {
    expiresIn?: number;
    includeSourceAccess?: boolean;
    publicAccess?: boolean;
  }) => {
    if (!sourceDiagram) {
      toast({
        title: 'Share Failed',
        description: 'Sharing requires source diagram metadata',
        variant: 'destructive',
      });
      return null;
    }

    try {
      const shareableLink = await EnhancedExportService.createShareableLink(
        sourceDiagram,
        options
      );
      
      await copyToClipboard(shareableLink.url);
      
      return shareableLink;
    } catch (error) {
      console.error('Failed to create shareable link:', error);
      toast({
        title: 'Share Failed',
        description: 'Failed to create shareable link',
        variant: 'destructive',
      });
      return null;
    }
  }, [sourceDiagram, copyToClipboard, toast]);

  // Diagram management
  const handleDownloadDiagram = useCallback((diagram: MermaidDiagram) => {
    const blob = new Blob([diagram.code], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${diagram.title.replace(/\s+/g, '-').toLowerCase()}.mmd`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, []);

  const handleRegenerateDiagram = useCallback((diagram: MermaidDiagram) => {
    generateMermaidDiagram(diagram.type);
  }, [generateMermaidDiagram]);

  const deleteDiagram = useCallback((diagramId: string) => {
    setDiagrams(prev => prev.filter(d => d.id !== diagramId));
    if (selectedDiagram?.id === diagramId) {
      setSelectedDiagram(null);
    }
  }, [selectedDiagram]);

  const updateDiagram = useCallback((diagramId: string, updates: Partial<MermaidDiagram>) => {
    setDiagrams(prev => prev.map(d => 
      d.id === diagramId 
        ? { ...d, ...updates, updatedAt: new Date() }
        : d
    ));
    
    if (selectedDiagram?.id === diagramId) {
      setSelectedDiagram(prev => prev ? { ...prev, ...updates, updatedAt: new Date() } : null);
    }
  }, [selectedDiagram]);

  // Zoom operations
  const handleZoomIn = useCallback(() => setZoom(Math.min(zoom + 10, 200)), [zoom]);
  const handleZoomOut = useCallback(() => setZoom(Math.max(zoom - 10, 50)), [zoom]);
  const handleResetZoom = useCallback(() => setZoom(100), []);

  // Edit operations
  const handleEdit = useCallback(() => setIsEditing(true), []);
  const handleSaveEdit = useCallback(() => {
    setIsEditing(false);
    onDiagramChange?.(localDiagram);
  }, [localDiagram, onDiagramChange]);
  const handleCancelEdit = useCallback(() => {
    setIsEditing(false);
    setLocalDiagram(initialDiagram);
  }, [initialDiagram]);

  const updateLocalDiagram = useCallback((newDiagram: string) => {
    setLocalDiagram(newDiagram);
  }, []);

  const getExportPreview = useCallback(async (format: 'svg' | 'png' | 'json' | 'mmd') => {
    if (!sourceDiagram) {
      return null;
    }

    try {
      return await EnhancedExportService.getExportPreview(sourceDiagram, format);
    } catch (error) {
      console.error('Failed to get export preview:', error);
      return null;
    }
  }, [sourceDiagram]);

  return {
    // Single diagram management
    containerRef,
    zoom,
    error,
    localDiagram,
    isEditing,
    handleCopy,
    handleExportCode,
    handleExportSVG,
    handleExportPNG,
    handleExportJSON,
    handleCreateShareableLink,
    getExportPreview,
    handleZoomIn,
    handleZoomOut,
    handleResetZoom,
    handleEdit,
    handleSaveEdit,
    handleCancelEdit,
    updateDiagram: updateLocalDiagram,
    setZoom,
    setError,
    setIsEditing,
    hasEnhancedExport: enableEnhancedExport && !!sourceDiagram,
    
    // Multiple diagrams management
    diagrams,
    selectedDiagram,
    isGenerating,
    diagramType,
    autoGenerate: autoGenerateEnabled,
    maxMessages: maxMessagesLimit,
    setSelectedDiagram,
    setDiagramType,
    setAutoGenerate: setAutoGenerateEnabled,
    setMaxMessages: setMaxMessagesLimit,
    generateDiagram: generateMermaidDiagram,
    copyDiagram: handleCopyDiagram,
    downloadDiagram: handleDownloadDiagram,
    regenerateDiagram: handleRegenerateDiagram,
    deleteDiagram,
    updateDiagramInList: updateDiagram,
  };
};

// Export backward-compatible hooks
export const useMermaidDiagram = (initialDiagram: string, options: Omit<UnifiedMermaidOptions, 'initialDiagram'> = {}) => {
  const result = useUnifiedMermaid({ ...options, initialDiagram });
  
  // Return only single diagram related properties for backward compatibility
  return {
    containerRef: result.containerRef,
    zoom: result.zoom,
    error: result.error,
    localDiagram: result.localDiagram,
    isEditing: result.isEditing,
    handleCopy: result.handleCopy,
    handleExportCode: result.handleExportCode,
    handleExportSVG: result.handleExportSVG,
    handleExportPNG: result.handleExportPNG,
    handleExportJSON: result.handleExportJSON,
    handleCreateShareableLink: result.handleCreateShareableLink,
    getExportPreview: result.getExportPreview,
    handleZoomIn: result.handleZoomIn,
    handleZoomOut: result.handleZoomOut,
    handleResetZoom: result.handleResetZoom,
    handleEdit: result.handleEdit,
    handleSaveEdit: result.handleSaveEdit,
    handleCancelEdit: result.handleCancelEdit,
    updateDiagram: result.updateDiagram,
    setZoom: result.setZoom,
    setError: result.setError,
    setIsEditing: result.setIsEditing,
    hasEnhancedExport: result.hasEnhancedExport,
  };
};

export const useMermaidDiagrams = (chatMessages: ChatMessage[] = [], options: Omit<UnifiedMermaidOptions, 'chatMessages'> = {}) => {
  const result = useUnifiedMermaid({ ...options, chatMessages });
  
  // Return only multiple diagrams related properties for backward compatibility
  return {
    diagrams: result.diagrams,
    selectedDiagram: result.selectedDiagram,
    isGenerating: result.isGenerating,
    diagramType: result.diagramType,
    autoGenerate: result.autoGenerate,
    maxMessages: result.maxMessages,
    setSelectedDiagram: result.setSelectedDiagram,
    setDiagramType: result.setDiagramType,
    setAutoGenerate: result.setAutoGenerate,
    setMaxMessages: result.setMaxMessages,
    generateDiagram: result.generateDiagram,
    copyDiagram: result.copyDiagram,
    downloadDiagram: result.downloadDiagram,
    regenerateDiagram: result.regenerateDiagram,
    deleteDiagram: result.deleteDiagram,
    updateDiagram: result.updateDiagramInList,
  };
};