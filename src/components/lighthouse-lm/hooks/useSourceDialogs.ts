import { useState, useEffect } from 'react';
import { Source } from '@/hooks/useSources';

export interface SourceDialogState {
  showAddSourcesDialog: boolean;
  showDeleteDialog: boolean;
  showRenameDialog: boolean;
  showSourceManager: boolean;
  showDiagramGenerator: boolean;
  sourceToDelete: Source | null;
  sourceToRename: Source | null;
  selectedSources: Source[];
}

export const useSourceDialogs = () => {
  const [showAddSourcesDialog, setShowAddSourcesDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showRenameDialog, setShowRenameDialog] = useState(false);
  const [showSourceManager, setShowSourceManager] = useState(false);
  const [showDiagramGenerator, setShowDiagramGenerator] = useState(false);
  const [sourceToDelete, setSourceToDelete] = useState<Source | null>(null);
  const [sourceToRename, setSourceToRename] = useState<Source | null>(null);
  const [selectedSources, setSelectedSources] = useState<Source[]>([]);

  // Clean up rename dialog state
  useEffect(() => {
    if (!showRenameDialog) {
      setSourceToRename(null);
    }
  }, [showRenameDialog]);

  // Clean up delete dialog state
  useEffect(() => {
    if (!showDeleteDialog) {
      setSourceToDelete(null);
    }
  }, [showDeleteDialog]);

  const openAddSourceDialog = () => setShowAddSourcesDialog(true);
  const closeAddSourceDialog = () => setShowAddSourcesDialog(false);

  const openDeleteDialog = (source: Source) => {
    setSourceToDelete(source);
    setShowDeleteDialog(true);
  };
  const closeDeleteDialog = () => {
    setShowDeleteDialog(false);
    setSourceToDelete(null);
  };

  const openRenameDialog = (source: Source) => {
    setSourceToRename(source);
    setShowRenameDialog(true);
  };
  const closeRenameDialog = () => {
    setShowRenameDialog(false);
    setSourceToRename(null);
  };

  const openSourceManager = () => setShowSourceManager(true);
  const closeSourceManager = () => setShowSourceManager(false);

  const openDiagramGenerator = (sources: Source[]) => {
    setSelectedSources(sources);
    setShowDiagramGenerator(true);
  };
  const closeDiagramGenerator = () => setShowDiagramGenerator(false);

  return {
    // State
    showAddSourcesDialog,
    showDeleteDialog,
    showRenameDialog,
    showSourceManager,
    showDiagramGenerator,
    sourceToDelete,
    sourceToRename,
    selectedSources,
    
    // Setters
    setShowAddSourcesDialog,
    setShowDeleteDialog,
    setShowRenameDialog,
    setShowSourceManager,
    setShowDiagramGenerator,
    setSelectedSources,
    
    // Helper functions
    openAddSourceDialog,
    closeAddSourceDialog,
    openDeleteDialog,
    closeDeleteDialog,
    openRenameDialog,
    closeRenameDialog,
    openSourceManager,
    closeSourceManager,
    openDiagramGenerator,
    closeDiagramGenerator,
  };
};