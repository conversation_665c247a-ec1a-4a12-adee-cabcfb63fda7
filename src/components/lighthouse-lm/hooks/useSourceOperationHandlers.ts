import { useRef } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { Source } from '@/hooks/useSources';
import { Citation } from '@/components/lighthouse-lm/types/message';
import { useSourceDelete } from '@/components/lighthouse-lm/hooks/useSourceDelete';
import { useSourceOperations } from '@/services/sourceOperationsService';
import { DiagramType } from '@/services/sourceDiagramService';

interface UseSourceOperationHandlersProps {
  notebookId?: string;
  sources?: Source[] | null;
  setSelectedCitation?: (citation: Citation | null) => void;
  openDeleteDialog: (source: Source) => void;
  openRenameDialog: (source: Source) => void;
  openDiagramGenerator: (sources: Source[]) => void;
}

export const useSourceOperationHandlers = ({
  notebookId,
  sources,
  setSelectedCitation,
  openDeleteDialog,
  openRenameDialog,
  openDiagramGenerator,
}: UseSourceOperationHandlersProps) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const queryClient = useQueryClient();
  const { deleteSource, isDeleting } = useSourceDelete();
  
  const sourceOperations = useSourceOperations(
    async (id: string) => {
      await deleteSource(id);
    },
    notebookId
  );

  const handleAddSource = () => {
    // This will be handled by dialog state
  };

  const handleFileUpload = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      // Handle file upload logic here
    }
  };

  const handleSourceClick = (source: Source) => {
    if (setSelectedCitation) {
      const citation: Citation = {
        citation_id: parseInt(source.id) || 0,
        source_id: source.id,
        source_title: source.title || 'Untitled',
        source_type: source.source_type || 'document',
        chunk_lines_from: 0,
        chunk_lines_to: 0,
        chunk_index: 0,
        excerpt: source.content || ''
      };
      setSelectedCitation(citation);
    }
  };

  const handleRemoveSource = (source: Source) => {
    openDeleteDialog(source);
  };

  const handleRenameSource = (source: Source) => {
    openRenameDialog(source);
  };

  const confirmDelete = async (sourceToDelete: Source | null) => {
    if (sourceToDelete) {
      try {
        await deleteSource(sourceToDelete.id);
        queryClient.invalidateQueries({ queryKey: ['sources', notebookId] });
        return true;
      } catch (error) {
        return false;
      }
    }
    return false;
  };

  const handleBulkDelete = async (sourceIds: string[]) => {
    const result = await sourceOperations.bulkDelete(sourceIds);
    return result;
  };

  const handleBulkExport = async (sourceIds: string[]) => {
    const sourcesToExport = sources?.filter(s => sourceIds.includes(s.id)) || [];
    const result = sourceOperations.bulkExport(sourcesToExport, sourceIds);
    return result;
  };

  const handleBulkArchive = async (sourceIds: string[]) => {
    const result = await sourceOperations.bulkArchive(sourceIds);
    return result;
  };

  const handleGenerateDiagram = () => {
    if (sources && sources.length > 0) {
      openDiagramGenerator(sources);
    }
  };

  const handleQuickDiagramGenerate = (sourcesToUse: Source[], diagramType: DiagramType) => {
    openDiagramGenerator(sourcesToUse);
  };

  const handleDiagramGenerated = (diagram: any) => {
    // Could add a toast notification here
  };

  const handleDiagramError = (error: string) => {
    // Could add a toast notification here
  };

  const handleDiagramView = (diagram: any) => {
    // Could open diagram in a modal or navigate to diagram view
  };

  return {
    fileInputRef,
    isDeleting,
    handleAddSource,
    handleFileUpload,
    handleFileChange,
    handleSourceClick,
    handleRemoveSource,
    handleRenameSource,
    confirmDelete,
    handleBulkDelete,
    handleBulkExport,
    handleBulkArchive,
    handleGenerateDiagram,
    handleQuickDiagramGenerate,
    handleDiagramGenerated,
    handleDiagramError,
    handleDiagramView,
  };
};