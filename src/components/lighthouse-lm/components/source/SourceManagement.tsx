import React, { useState, useCallback, useEffect, useMemo, useRef } from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '../../../ui/dialog';
import { <PERSON><PERSON> } from '../../../ui/button';
import { Input } from '../../../ui/input';
import { Label } from '../../../ui/label';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '../../../ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../../ui/card';
import { Badge } from '../../../ui/badge';
import { ScrollArea } from '../../../ui/scroll-area';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../../ui/select';
import { Checkbox } from '../../../ui/checkbox';
import { Progress } from '../../../ui/progress';
import { Separator } from '../../../ui/separator';
import { Switch } from '../../../ui/switch';
import { Slider } from '../../../ui/slider';
import { RadioGroup, RadioGroupItem } from '../../../ui/radio-group';
import { Popover, PopoverContent, PopoverTrigger } from '../../../ui/popover';
import { Calendar } from '../../../ui/calendar';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '../../../ui/command';
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle } from '../../../ui/sheet';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FileText,
  Upload,
  Download,
  Search,
  Filter,
  Grid,
  List,
  Brain,
  TrendingUp,
  Calendar as CalendarIcon,
  Tag,
  Archive,
  Trash2,
  Share2,
  Copy,
  ExternalLink,
  RefreshCw,
  Settings,
  ChevronRight,
  ChevronLeft,
  Eye,
  EyeOff,
  Lock,
  Unlock,
  GitBranch,
  History,
  Users,
  MessageSquare,
  Zap,
  Database,
  Cloud,
  HardDrive,
  Wifi,
  WifiOff,
  Plus,
  Minus,
  X,
  Check,
  AlertCircle,
  Info,
  HelpCircle,
  FileUp,
  FolderOpen,
  Link,
  Unlink,
  Sparkles,
  Lightbulb,
  BookOpen,
  Code,
  Image,
  Video,
  Music,
  FileSpreadsheet,
  Presentation,
  Map,
  Compass,
  Target,
  BarChart3,
  PieChart,
  LineChart,
  Activity,
  Layers,
  Layout,
  Maximize2,
  Minimize2,
  Move,
  MoreVertical,
  ChevronDown,
  ChevronUp,
  ChevronsUpDown,
  Globe,
  Mail,
  Phone,
  MapPin,
  Clock,
  Calendar as CalendarIcon2,
  User,
  UserPlus,
  UserMinus,
  UserCheck,
  Shield,
  ShieldCheck,
  ShieldAlert,
  Bell,
  BellOff,
  Volume2,
  VolumeX,
  Mic,
  MicOff,
  Camera,
  CameraOff,
  Paperclip,
  Send,
  Save,
  Edit,
  Edit2,
  Edit3,
  Palette,
  Brush,
  Wand2,
  Stars,
  Hash,
  AtSign,
  DollarSign,
  Percent,
  Binary,
  Code2,
  Terminal,
  FileCode,
  GitCommit,
  GitMerge,
  GitPullRequest,
  Github,
  Gitlab,
  Package,
  Box,
  Briefcase,
  Award,
  Trophy,
  Medal,
  Flag,
  Bookmark,
  Heart,
  Star,
  ThumbsUp,
  ThumbsDown,
  MessageCircle,
  MessagesSquare,
  Quote,
  FileCheck,
  FileX,
  FilePlus,
  FileMinus,
  FileSearch,
  FileArchive,
  Folder,
  FolderPlus,
  FolderMinus,
  FolderSearch,
  FolderArchive,
  FolderCheck,
  FolderX,
  Server,
  Cpu,
  HardDrive as HardDriveIcon,
  Usb,
  Bluetooth,
  Cast,
  Airplay,
  Smartphone,
  Tablet,
  Laptop,
  Monitor,
  Watch,
  Headphones,
  Speaker,
  Printer,
  Scanner,
  Keyboard,
  Mouse,
} from 'lucide-react';
import { useToast } from '../../hooks/useToast';
import { useSources } from '../../hooks/useSources';
import { useFileUpload } from '../../hooks/useFileUpload';
import { useDropzone } from 'react-dropzone';
import { format, formatDistanceToNow } from 'date-fns';
import { cn } from '../../lib/utils';

interface SourceManagementDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  notebookId?: string;
  onSourceSelect?: (source: any) => void;
  onBulkOperation?: (operation: string, sourceIds: string[]) => void;
}

interface SourceTemplate {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  fields: Array<{
    name: string;
    type: string;
    required: boolean;
    placeholder?: string;
  }>;
  category: string;
}

interface SourceRelation {
  id: string;
  sourceId: string;
  targetId: string;
  type: 'references' | 'contradicts' | 'supports' | 'extends' | 'replaces';
  strength: number;
  notes?: string;
}

interface SourceVersion {
  id: string;
  sourceId: string;
  version: number;
  timestamp: Date;
  changes: string;
  author: string;
  size: number;
}

interface ExportFormat {
  id: string;
  name: string;
  extension: string;
  icon: React.ReactNode;
  description: string;
  available: boolean;
}

interface IntegrationService {
  id: string;
  name: string;
  icon: React.ReactNode;
  connected: boolean;
  description: string;
  features: string[];
}

const SourceManagementDialog: React.FC<SourceManagementDialogProps> = ({
  open,
  onOpenChange,
  notebookId,
  onSourceSelect,
  onBulkOperation,
}) => {
  const { sources, isLoading, error, refetch, addSource, deleteSource, updateSource } = useSources(notebookId);
  const { toast } = useToast();
  const { uploadFile, extractText, isUploading, uploadProgress, validateFile } = useFileUpload(notebookId);
  
  const [activeTab, setActiveTab] = useState('overview');
  const [viewMode, setViewMode] = useState<'grid' | 'list' | 'kanban' | 'timeline' | 'graph'>('grid');
  const [selectedSources, setSelectedSources] = useState<Set<string>>(new Set());
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [sortBy, setSortBy] = useState('date');
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [dateRange, setDateRange] = useState<{ from: Date | undefined; to: Date | undefined }>({
    from: undefined,
    to: undefined,
  });
  const [tags, setTags] = useState<string[]>([]);
  const [showVersionHistory, setShowVersionHistory] = useState(false);
  const [selectedSourceForHistory, setSelectedSourceForHistory] = useState<any>(null);
  const [showRelationshipMapper, setShowRelationshipMapper] = useState(false);
  const [showTemplateSelector, setShowTemplateSelector] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<SourceTemplate | null>(null);
  const [showIntegrations, setShowIntegrations] = useState(false);
  const [showComparison, setShowComparison] = useState(false);
  const [comparisonSources, setComparisonSources] = useState<any[]>([]);
  const [showBatchProcessor, setShowBatchProcessor] = useState(false);
  const [batchOperations, setBatchOperations] = useState<any[]>([]);
  const [showCollaboration, setShowCollaboration] = useState(false);
  const [collaborators, setCollaborators] = useState<any[]>([]);
  const [showAnalytics, setShowAnalytics] = useState(false);
  const [analyticsTimeframe, setAnalyticsTimeframe] = useState('week');
  const [showAIAssistant, setShowAIAssistant] = useState(false);
  const [aiSuggestions, setAiSuggestions] = useState<any[]>([]);

  // Helper function to determine file type from file extension
  const getFileType = useCallback((file: File): string => {
    const extension = file.name.split('.').pop()?.toLowerCase() || '';
    
    const typeMap: Record<string, string> = {
      'pdf': 'pdf',
      'txt': 'text',
      'md': 'text',
      'csv': 'text',
      'json': 'text',
      'html': 'text',
      'htm': 'text',
      'doc': 'text',
      'docx': 'text',
      'xls': 'text',
      'xlsx': 'text',
      'mp3': 'audio',
      'wav': 'audio',
      'ogg': 'audio',
      'mp4': 'video',
      'mov': 'video',
      'avi': 'video',
      'jpg': 'image',
      'jpeg': 'image',
      'png': 'image',
      'gif': 'image',
      'svg': 'image',
    };
    
    return typeMap[extension] || 'text';
  }, []);

  const sourceTemplates: SourceTemplate[] = [
    {
      id: 'research-paper',
      name: 'Research Paper',
      description: 'Academic papers and scientific publications',
      icon: <FileText className="w-4 h-4" />,
      category: 'Academic',
      fields: [
        { name: 'title', type: 'text', required: true },
        { name: 'authors', type: 'text', required: true },
        { name: 'journal', type: 'text', required: false },
        { name: 'year', type: 'number', required: true },
        { name: 'doi', type: 'text', required: false },
        { name: 'abstract', type: 'textarea', required: false },
      ],
    },
    {
      id: 'web-article',
      name: 'Web Article',
      description: 'Online articles and blog posts',
      icon: <Globe className="w-4 h-4" />,
      category: 'Web',
      fields: [
        { name: 'url', type: 'url', required: true },
        { name: 'title', type: 'text', required: true },
        { name: 'author', type: 'text', required: false },
        { name: 'publishDate', type: 'date', required: false },
        { name: 'tags', type: 'tags', required: false },
      ],
    },
    {
      id: 'book',
      name: 'Book',
      description: 'Books and e-books',
      icon: <BookOpen className="w-4 h-4" />,
      category: 'Literature',
      fields: [
        { name: 'title', type: 'text', required: true },
        { name: 'author', type: 'text', required: true },
        { name: 'isbn', type: 'text', required: false },
        { name: 'publisher', type: 'text', required: false },
        { name: 'year', type: 'number', required: false },
        { name: 'chapters', type: 'array', required: false },
      ],
    },
    {
      id: 'code-repository',
      name: 'Code Repository',
      description: 'GitHub, GitLab, or other code repositories',
      icon: <Github className="w-4 h-4" />,
      category: 'Technical',
      fields: [
        { name: 'repoUrl', type: 'url', required: true },
        { name: 'branch', type: 'text', required: false, placeholder: 'main' },
        { name: 'language', type: 'select', required: false },
        { name: 'description', type: 'textarea', required: false },
      ],
    },
    {
      id: 'media',
      name: 'Media File',
      description: 'Images, videos, audio files',
      icon: <Image className="w-4 h-4" />,
      category: 'Media',
      fields: [
        { name: 'title', type: 'text', required: true },
        { name: 'description', type: 'textarea', required: false },
        { name: 'duration', type: 'text', required: false },
        { name: 'resolution', type: 'text', required: false },
        { name: 'tags', type: 'tags', required: false },
      ],
    },
    {
      id: 'dataset',
      name: 'Dataset',
      description: 'Structured data and spreadsheets',
      icon: <Database className="w-4 h-4" />,
      category: 'Data',
      fields: [
        { name: 'name', type: 'text', required: true },
        { name: 'format', type: 'select', required: true },
        { name: 'rows', type: 'number', required: false },
        { name: 'columns', type: 'number', required: false },
        { name: 'schema', type: 'json', required: false },
      ],
    },
  ];

  const exportFormats: ExportFormat[] = [
    { id: 'pdf', name: 'PDF Document', extension: '.pdf', icon: <FileText />, description: 'Export as PDF with formatting', available: true },
    { id: 'docx', name: 'Word Document', extension: '.docx', icon: <FileText />, description: 'Microsoft Word format', available: true },
    { id: 'markdown', name: 'Markdown', extension: '.md', icon: <Code />, description: 'Plain text with formatting', available: true },
    { id: 'json', name: 'JSON', extension: '.json', icon: <Code2 />, description: 'Structured data format', available: true },
    { id: 'csv', name: 'CSV', extension: '.csv', icon: <FileSpreadsheet />, description: 'Spreadsheet format', available: true },
    { id: 'html', name: 'HTML', extension: '.html', icon: <Globe />, description: 'Web page format', available: true },
    { id: 'bibtex', name: 'BibTeX', extension: '.bib', icon: <BookOpen />, description: 'Bibliography format', available: true },
    { id: 'notion', name: 'Notion', extension: '.notion', icon: <Package />, description: 'Import to Notion', available: false },
    { id: 'obsidian', name: 'Obsidian', extension: '.md', icon: <Brain />, description: 'Obsidian vault format', available: true },
  ];

  const integrationServices: IntegrationService[] = [
    {
      id: 'google-drive',
      name: 'Google Drive',
      icon: <Cloud className="w-5 h-5" />,
      connected: false,
      description: 'Sync with Google Drive',
      features: ['Auto-sync', 'Backup', 'Collaboration'],
    },
    {
      id: 'dropbox',
      name: 'Dropbox',
      icon: <Box className="w-5 h-5" />,
      connected: false,
      description: 'Connect to Dropbox',
      features: ['File sync', 'Version history', 'Sharing'],
    },
    {
      id: 'notion',
      name: 'Notion',
      icon: <Package className="w-5 h-5" />,
      connected: true,
      description: 'Import from Notion',
      features: ['Database sync', 'Page import', 'Export'],
    },
    {
      id: 'obsidian',
      name: 'Obsidian',
      icon: <Brain className="w-5 h-5" />,
      connected: false,
      description: 'Sync with Obsidian vault',
      features: ['Markdown sync', 'Graph view', 'Plugins'],
    },
    {
      id: 'github',
      name: 'GitHub',
      icon: <Github className="w-5 h-5" />,
      connected: true,
      description: 'Connect repositories',
      features: ['Code import', 'Issue tracking', 'Wiki sync'],
    },
    {
      id: 'zotero',
      name: 'Zotero',
      icon: <BookOpen className="w-5 h-5" />,
      connected: false,
      description: 'Import citations',
      features: ['Bibliography', 'PDF sync', 'Annotations'],
    },
  ];

  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (!notebookId) {
      toast({
        title: 'Notebook required',
        description: 'Please select a notebook first',
        variant: 'destructive',
      });
      return;
    }

    acceptedFiles.forEach(async (file) => {
      try {
        setIsUploading(true);
        setUploadProgress(0);
        
        // Validate file first
        if (!validateFile(file)) {
          return;
        }

        // Upload file and get result
        const uploadResult = await uploadFile(file);
        setUploadProgress(50); // Processing phase

        // Determine file type for content extraction
        const fileType = getFileType(file);

        // Extract text from the uploaded file
        let content = '';
        if (uploadResult.filePath) {
          content = await extractText(uploadResult.filePath, fileType) || '';
        }

        // Create source in the backend
        const sourceData = {
          notebookId: notebookId,
          title: file.name,
          type: fileType as 'pdf' | 'text' | 'website' | 'youtube' | 'audio',
          content: content,
          file_path: uploadResult.filePath,
          file_size: file.size,
        };

        // Add source to backend
        await addSource(sourceData);
        
        setUploadProgress(100);
        
        toast({
          title: 'Upload complete',
          description: `${file.name} uploaded and processed successfully`,
        });
      } catch (error) {
        toast({
          title: 'Upload failed',
          description: error instanceof Error ? error.message : 'Failed to upload file',
          variant: 'destructive',
        });
      } finally {
        setIsUploading(false);
        setUploadProgress(0);
      }
    });
  }, [notebookId, toast, uploadFile, extractText, addSource, validateFile]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'text/plain': ['.txt', '.md'],
      'application/msword': ['.doc', '.docx'],
      'application/vnd.ms-excel': ['.xls', '.xlsx'],
      'text/csv': ['.csv'],
      'application/json': ['.json'],
      'text/html': ['.html', '.htm'],
      'image/*': ['.png', '.jpg', '.jpeg', '.gif', '.svg'],
      'video/*': ['.mp4', '.avi', '.mov'],
      'audio/*': ['.mp3', '.wav', '.ogg'],
    },
  });

  const handleSelectAll = () => {
    if (selectedSources.size === sources?.length) {
      setSelectedSources(new Set());
    } else {
      setSelectedSources(new Set(sources?.map(s => s.id) || []));
    }
  };

  const handleSourceSelect = (sourceId: string) => {
    const newSelected = new Set(selectedSources);
    if (newSelected.has(sourceId)) {
      newSelected.delete(sourceId);
    } else {
      newSelected.add(sourceId);
    }
    setSelectedSources(newSelected);
  };

  const handleBulkOperation = async (operation: string) => {
    if (selectedSources.size === 0) {
      toast({
        title: 'No sources selected',
        description: 'Please select at least one source',
        variant: 'destructive',
      });
      return;
    }
    
    try {
      if (operation === 'delete') {
        // Delete selected sources
        for (const sourceId of Array.from(selectedSources)) {
          await deleteSource(sourceId);
        }
        
        toast({
          title: 'Sources deleted',
          description: `${selectedSources.size} source(s) deleted successfully`,
        });
        
        setSelectedSources(new Set());
      } else {
        onBulkOperation?.(operation, Array.from(selectedSources));
        setSelectedSources(new Set());
      }
    } catch (error) {
      toast({
        title: 'Operation failed',
        description: 'Failed to complete the operation',
        variant: 'destructive',
      });
    }
  };

  const handleExport = (format: ExportFormat) => {
    if (!format.available) {
      toast({
        title: 'Format not available',
        description: `${format.name} export is coming soon`,
      });
      return;
    }
    
    toast({
      title: 'Exporting sources',
      description: `Exporting ${selectedSources.size || sources?.length} sources as ${format.name}`,
    });
  };

  const handleIntegrationConnect = (service: IntegrationService) => {
    toast({
      title: service.connected ? 'Disconnecting' : 'Connecting',
      description: `${service.connected ? 'Disconnecting from' : 'Connecting to'} ${service.name}...`,
    });
  };

  const handleTemplateSelect = (template: SourceTemplate) => {
    setSelectedTemplate(template);
    setShowTemplateSelector(false);
    toast({
      title: 'Template selected',
      description: `Using ${template.name} template`,
    });
  };

  const filteredSources = useMemo(() => {
    let filtered = sources || [];
    
    if (searchQuery) {
      filtered = filtered.filter(source =>
        source.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        source.content?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }
    
    if (filterType !== 'all') {
      filtered = filtered.filter(source => source.type === filterType);
    }
    
    if (tags.length > 0) {
      filtered = filtered.filter(source =>
        tags.some(tag => source.tags?.includes(tag))
      );
    }
    
    if (dateRange.from && dateRange.to) {
      filtered = filtered.filter(source => {
        const sourceDate = new Date(source.created_at);
        return sourceDate >= dateRange.from! && sourceDate <= dateRange.to!;
      });
    }
    
    // Sort
    filtered = [...filtered].sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.title.localeCompare(b.title);
        case 'date':
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        case 'size':
          return (b.size || 0) - (a.size || 0);
        case 'type':
          return a.type.localeCompare(b.type);
        default:
          return 0;
      }
    });
    
    return filtered;
  }, [sources, searchQuery, filterType, tags, dateRange, sortBy]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-[95vw] w-full h-[90vh] p-0">
        <div className="flex h-full">
          {/* Sidebar Navigation */}
          <div className="w-64 bg-muted/30 border-r p-4 overflow-y-auto">
            <DialogHeader className="mb-6">
              <DialogTitle className="flex items-center gap-2">
                <Database className="w-5 h-5" />
                Source Management
              </DialogTitle>
              <DialogDescription>
                Organize and manage your knowledge sources
              </DialogDescription>
            </DialogHeader>
            
            <nav className="space-y-1">
              <Button
                variant={activeTab === 'overview' ? 'secondary' : 'ghost'}
                className="w-full justify-start"
                onClick={() => setActiveTab('overview')}
              >
                <BarChart3 className="w-4 h-4 mr-2" />
                Overview
              </Button>
              <Button
                variant={activeTab === 'sources' ? 'secondary' : 'ghost'}
                className="w-full justify-start"
                onClick={() => setActiveTab('sources')}
              >
                <FileText className="w-4 h-4 mr-2" />
                All Sources
              </Button>
              <Button
                variant={activeTab === 'upload' ? 'secondary' : 'ghost'}
                className="w-full justify-start"
                onClick={() => setActiveTab('upload')}
              >
                <Upload className="w-4 h-4 mr-2" />
                Upload & Import
              </Button>
              <Button
                variant={activeTab === 'templates' ? 'secondary' : 'ghost'}
                className="w-full justify-start"
                onClick={() => setActiveTab('templates')}
              >
                <Layout className="w-4 h-4 mr-2" />
                Templates
              </Button>
              <Button
                variant={activeTab === 'relationships' ? 'secondary' : 'ghost'}
                className="w-full justify-start"
                onClick={() => setActiveTab('relationships')}
              >
                <GitBranch className="w-4 h-4 mr-2" />
                Relationships
              </Button>
              <Button
                variant={activeTab === 'comparison' ? 'secondary' : 'ghost'}
                className="w-full justify-start"
                onClick={() => setActiveTab('comparison')}
              >
                <Layers className="w-4 h-4 mr-2" />
                Compare
              </Button>
              <Button
                variant={activeTab === 'analytics' ? 'secondary' : 'ghost'}
                className="w-full justify-start"
                onClick={() => setActiveTab('analytics')}
              >
                <TrendingUp className="w-4 h-4 mr-2" />
                Analytics
              </Button>
              <Button
                variant={activeTab === 'ai' ? 'secondary' : 'ghost'}
                className="w-full justify-start"
                onClick={() => setActiveTab('ai')}
              >
                <Brain className="w-4 h-4 mr-2" />
                AI Assistant
              </Button>
              <Button
                variant={activeTab === 'export' ? 'secondary' : 'ghost'}
                className="w-full justify-start"
                onClick={() => setActiveTab('export')}
              >
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
              <Button
                variant={activeTab === 'integrations' ? 'secondary' : 'ghost'}
                className="w-full justify-start"
                onClick={() => setActiveTab('integrations')}
              >
                <Zap className="w-4 h-4 mr-2" />
                Integrations
              </Button>
              <Button
                variant={activeTab === 'collaboration' ? 'secondary' : 'ghost'}
                className="w-full justify-start"
                onClick={() => setActiveTab('collaboration')}
              >
                <Users className="w-4 h-4 mr-2" />
                Collaboration
              </Button>
              <Button
                variant={activeTab === 'settings' ? 'secondary' : 'ghost'}
                className="w-full justify-start"
                onClick={() => setActiveTab('settings')}
              >
                <Settings className="w-4 h-4 mr-2" />
                Settings
              </Button>
            </nav>
            
            {/* Quick Stats */}
            <div className="mt-8 p-4 bg-background rounded-lg space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Total Sources</span>
                <span className="font-semibold">{sources?.length || 0}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Selected</span>
                <span className="font-semibold">{selectedSources.size}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Storage Used</span>
                <span className="font-semibold">2.4 GB</span>
              </div>
              <Progress value={65} className="h-2" />
            </div>
          </div>
          
          {/* Main Content Area */}
          <div className="flex-1 overflow-y-auto">
            <Tabs value={activeTab} className="h-full">
              {/* Overview Tab */}
              <TabsContent value="overview" className="h-full p-6">
                <div className="space-y-6">
                  <div className="grid grid-cols-4 gap-4">
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium">Total Sources</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">{sources?.length || 0}</div>
                        <p className="text-xs text-muted-foreground">+12% from last week</p>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium">Recent Uploads</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">24</div>
                        <p className="text-xs text-muted-foreground">Last 7 days</p>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium">Storage Used</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">2.4 GB</div>
                        <p className="text-xs text-muted-foreground">65% of 4 GB</p>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium">Collaborators</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">5</div>
                        <p className="text-xs text-muted-foreground">2 active now</p>
                      </CardContent>
                    </Card>
                  </div>
                  
                  {/* Recent Activity */}
                  <Card>
                    <CardHeader>
                      <CardTitle>Recent Activity</CardTitle>
                      <CardDescription>Your source management activity</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {[1, 2, 3, 4, 5].map(i => (
                          <div key={i} className="flex items-center gap-4">
                            <div className="w-2 h-2 bg-primary rounded-full" />
                            <div className="flex-1">
                              <p className="text-sm">Added new research paper: "AI in Healthcare"</p>
                              <p className="text-xs text-muted-foreground">2 hours ago</p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
              
              {/* Sources Tab */}
              <TabsContent value="sources" className="h-full">
                <div className="p-6 space-y-4">
                  {/* Toolbar */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Input
                        placeholder="Search sources..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="w-64"
                      />
                      <Select value={filterType} onValueChange={setFilterType}>
                        <SelectTrigger className="w-32">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Types</SelectItem>
                          <SelectItem value="pdf">PDF</SelectItem>
                          <SelectItem value="web">Web</SelectItem>
                          <SelectItem value="doc">Document</SelectItem>
                          <SelectItem value="code">Code</SelectItem>
                          <SelectItem value="media">Media</SelectItem>
                        </SelectContent>
                      </Select>
                      <Select value={sortBy} onValueChange={setSortBy}>
                        <SelectTrigger className="w-32">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="date">Date</SelectItem>
                          <SelectItem value="name">Name</SelectItem>
                          <SelectItem value="size">Size</SelectItem>
                          <SelectItem value="type">Type</SelectItem>
                        </SelectContent>
                      </Select>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
                      >
                        <Filter className="w-4 h-4 mr-2" />
                        Filters
                      </Button>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleSelectAll}
                      >
                        {selectedSources.size === sources?.length ? 'Deselect All' : 'Select All'}
                      </Button>
                      {selectedSources.size > 0 && (
                        <>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleBulkOperation('archive')}
                          >
                            <Archive className="w-4 h-4 mr-2" />
                            Archive
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleBulkOperation('tag')}
                          >
                            <Tag className="w-4 h-4 mr-2" />
                            Tag
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleBulkOperation('delete')}
                            className="text-destructive"
                          >
                            <Trash2 className="w-4 h-4 mr-2" />
                            Delete
                          </Button>
                        </>
                      )}
                      <div className="flex items-center border rounded-md">
                        <Button
                          variant={viewMode === 'grid' ? 'secondary' : 'ghost'}
                          size="sm"
                          className="rounded-none rounded-l-md"
                          onClick={() => setViewMode('grid')}
                        >
                          <Grid className="w-4 h-4" />
                        </Button>
                        <Button
                          variant={viewMode === 'list' ? 'secondary' : 'ghost'}
                          size="sm"
                          className="rounded-none"
                          onClick={() => setViewMode('list')}
                        >
                          <List className="w-4 h-4" />
                        </Button>
                        <Button
                          variant={viewMode === 'kanban' ? 'secondary' : 'ghost'}
                          size="sm"
                          className="rounded-none"
                          onClick={() => setViewMode('kanban')}
                        >
                          <Layout className="w-4 h-4" />
                        </Button>
                        <Button
                          variant={viewMode === 'timeline' ? 'secondary' : 'ghost'}
                          size="sm"
                          className="rounded-none"
                          onClick={() => setViewMode('timeline')}
                        >
                          <Activity className="w-4 h-4" />
                        </Button>
                        <Button
                          variant={viewMode === 'graph' ? 'secondary' : 'ghost'}
                          size="sm"
                          className="rounded-none rounded-r-md"
                          onClick={() => setViewMode('graph')}
                        >
                          <GitBranch className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                  
                  {/* Advanced Filters */}
                  {showAdvancedFilters && (
                    <Card>
                      <CardContent className="p-4">
                        <div className="grid grid-cols-3 gap-4">
                          <div>
                            <Label>Date Range</Label>
                            <Popover>
                              <PopoverTrigger asChild>
                                <Button variant="outline" className="w-full justify-start">
                                  <CalendarIcon className="w-4 h-4 mr-2" />
                                  {dateRange.from ? (
                                    dateRange.to ? (
                                      <>
                                        {format(dateRange.from, 'LLL dd, y')} -{' '}
                                        {format(dateRange.to, 'LLL dd, y')}
                                      </>
                                    ) : (
                                      format(dateRange.from, 'LLL dd, y')
                                    )
                                  ) : (
                                    'Pick a date range'
                                  )}
                                </Button>
                              </PopoverTrigger>
                              <PopoverContent className="w-auto p-0" align="start">
                                <Calendar
                                  mode="range"
                                  selected={dateRange}
                                  onSelect={setDateRange}
                                />
                              </PopoverContent>
                            </Popover>
                          </div>
                          <div>
                            <Label>Tags</Label>
                            <Input
                              placeholder="Enter tags..."
                              value={tags.join(', ')}
                              onChange={(e) => setTags(e.target.value.split(',').map(t => t.trim()))}
                            />
                          </div>
                          <div>
                            <Label>File Size</Label>
                            <Select>
                              <SelectTrigger>
                                <SelectValue placeholder="Any size" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="any">Any size</SelectItem>
                                <SelectItem value="small">&lt; 1 MB</SelectItem>
                                <SelectItem value="medium">1-10 MB</SelectItem>
                                <SelectItem value="large">&gt; 10 MB</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                  
                  {/* Sources Display */}
                  <ScrollArea className="h-[calc(100vh-300px)]">
                    {viewMode === 'grid' && (
                      <div className="grid grid-cols-3 gap-4">
                        {filteredSources.map((source) => (
                          <Card
                            key={source.id}
                            className={cn(
                              'cursor-pointer transition-all hover:shadow-lg',
                              selectedSources.has(source.id) && 'ring-2 ring-primary'
                            )}
                          >
                            <CardHeader className="pb-3">
                              <div className="flex items-start justify-between">
                                <Checkbox
                                  checked={selectedSources.has(source.id)}
                                  onCheckedChange={() => handleSourceSelect(source.id)}
                                />
                                <Badge variant="secondary">{source.type}</Badge>
                              </div>
                              <CardTitle className="text-sm mt-2">{source.title}</CardTitle>
                              <CardDescription className="text-xs line-clamp-2">
                                {source.content}
                              </CardDescription>
                            </CardHeader>
                            <CardContent className="pt-0">
                              <div className="flex items-center justify-between text-xs text-muted-foreground">
                                <span>{formatDistanceToNow(new Date(source.created_at))} ago</span>
                                <div className="flex items-center gap-2">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-6 w-6 p-0"
                                    onClick={() => {
                                      setSelectedSourceForHistory(source);
                                      setShowVersionHistory(true);
                                    }}
                                  >
                                    <History className="w-3 h-3" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-6 w-6 p-0"
                                  >
                                    <MoreVertical className="w-3 h-3" />
                                  </Button>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    )}
                    
                    {viewMode === 'list' && (
                      <div className="space-y-2">
                        {filteredSources.map((source) => (
                          <div
                            key={source.id}
                            className={cn(
                              'flex items-center gap-4 p-4 rounded-lg border hover:bg-muted/50 transition-colors',
                              selectedSources.has(source.id) && 'bg-muted'
                            )}
                          >
                            <Checkbox
                              checked={selectedSources.has(source.id)}
                              onCheckedChange={() => handleSourceSelect(source.id)}
                            />
                            <FileText className="w-5 h-5 text-muted-foreground" />
                            <div className="flex-1">
                              <h4 className="font-medium text-sm">{source.title}</h4>
                              <p className="text-xs text-muted-foreground">
                                {source.type} • {formatDistanceToNow(new Date(source.created_at))} ago
                              </p>
                            </div>
                            <Badge variant="secondary">{source.status || 'Active'}</Badge>
                            <Button variant="ghost" size="sm">
                              <MoreVertical className="w-4 h-4" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    )}
                  </ScrollArea>
                </div>
              </TabsContent>
              
              {/* Upload Tab */}
              <TabsContent value="upload" className="h-full p-6">
                <div className="space-y-6">
                  <div
                    {...getRootProps()}
                    className={cn(
                      'border-2 border-dashed rounded-lg p-12 text-center cursor-pointer transition-colors',
                      isDragActive ? 'border-primary bg-primary/10' : 'border-muted-foreground/25'
                    )}
                  >
                    <input {...getInputProps()} />
                    <Upload className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
                    <h3 className="text-lg font-semibold mb-2">
                      {isDragActive ? 'Drop files here' : 'Drag & Drop files here'}
                    </h3>
                    <p className="text-sm text-muted-foreground mb-4">
                      or click to browse files from your computer
                    </p>
                    <Button variant="secondary">
                      <FolderOpen className="w-4 h-4 mr-2" />
                      Browse Files
                    </Button>
                  </div>
                  
                  {isUploading && (
                    <Card>
                      <CardHeader>
                        <CardTitle>Uploading...</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <Progress value={uploadProgress} className="mb-2" />
                        <p className="text-sm text-muted-foreground">{uploadProgress}% complete</p>
                      </CardContent>
                    </Card>
                  )}
                  
                  {/* Import Options */}
                  <div className="grid grid-cols-3 gap-4">
                    <Card className="cursor-pointer hover:shadow-lg transition-shadow">
                      <CardHeader>
                        <Globe className="w-8 h-8 mb-2 text-primary" />
                        <CardTitle className="text-base">Import from URL</CardTitle>
                        <CardDescription className="text-xs">
                          Import web pages and online content
                        </CardDescription>
                      </CardHeader>
                    </Card>
                    <Card className="cursor-pointer hover:shadow-lg transition-shadow">
                      <CardHeader>
                        <Cloud className="w-8 h-8 mb-2 text-primary" />
                        <CardTitle className="text-base">Cloud Storage</CardTitle>
                        <CardDescription className="text-xs">
                          Import from Google Drive, Dropbox, etc.
                        </CardDescription>
                      </CardHeader>
                    </Card>
                    <Card className="cursor-pointer hover:shadow-lg transition-shadow">
                      <CardHeader>
                        <Github className="w-8 h-8 mb-2 text-primary" />
                        <CardTitle className="text-base">GitHub</CardTitle>
                        <CardDescription className="text-xs">
                          Import code repositories and documentation
                        </CardDescription>
                      </CardHeader>
                    </Card>
                  </div>
                </div>
              </TabsContent>
              
              {/* Templates Tab */}
              <TabsContent value="templates" className="h-full p-6">
                <div className="space-y-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-lg font-semibold">Source Templates</h3>
                      <p className="text-sm text-muted-foreground">
                        Use templates to quickly add structured sources
                      </p>
                    </div>
                    <Button>
                      <Plus className="w-4 h-4 mr-2" />
                      Create Template
                    </Button>
                  </div>
                  
                  <div className="grid grid-cols-3 gap-4">
                    {sourceTemplates.map((template) => (
                      <Card
                        key={template.id}
                        className="cursor-pointer hover:shadow-lg transition-shadow"
                        onClick={() => handleTemplateSelect(template)}
                      >
                        <CardHeader>
                          <div className="flex items-center justify-between mb-2">
                            {template.icon}
                            <Badge variant="outline">{template.category}</Badge>
                          </div>
                          <CardTitle className="text-base">{template.name}</CardTitle>
                          <CardDescription className="text-xs">
                            {template.description}
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="text-xs text-muted-foreground">
                            {template.fields.length} fields
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              </TabsContent>
              
              {/* Other tabs content would go here... */}
              
            </Tabs>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default SourceManagementDialog;