import React, { useEffect, useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '../../../ui/dialog';
import { Badge } from '../../../ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '../../../ui/tabs';
import { ScrollArea } from '../../../ui/scroll-area';
import { Separator } from '../../../ui/separator';
import {
  Command,
  Search,
  FileText,
  MessageSquare,
  Workflow,
  Save,
  Copy,
  Undo,
  Redo,
  Plus,
  Trash2,
  Download,
  Upload,
  Settings,
  HelpCircle,
  ChevronUp,
  ChevronDown,
  ArrowLeft,
  ArrowRight
} from 'lucide-react';

interface ShortcutGroup {
  title: string;
  shortcuts: Shortcut[];
}

interface Shortcut {
  keys: string[];
  description: string;
  icon?: React.ReactNode;
  category?: string;
}

interface KeyboardShortcutsProps {
  isOpen: boolean;
  onClose: () => void;
}

const KeyboardShortcuts: React.FC<KeyboardShortcutsProps> = ({ isOpen, onClose }) => {
  const [activeCategory, setActiveCategory] = useState('general');

  const shortcutGroups: Record<string, ShortcutGroup[]> = {
    general: [
      {
        title: 'Navigation',
        shortcuts: [
          { keys: ['⌘', 'K'], description: 'Open search', icon: <Search className="h-3 w-3" /> },
          { keys: ['⌘', 'B'], description: 'Toggle sidebar', icon: <ArrowLeft className="h-3 w-3" /> },
          { keys: ['⌘', ','], description: 'Open preferences', icon: <Settings className="h-3 w-3" /> },
          { keys: ['⌘', '/'], description: 'Show keyboard shortcuts', icon: <HelpCircle className="h-3 w-3" /> },
          { keys: ['Esc'], description: 'Close dialog/modal' },
        ]
      },
      {
        title: 'Notebooks',
        shortcuts: [
          { keys: ['⌘', 'N'], description: 'New notebook', icon: <Plus className="h-3 w-3" /> },
          { keys: ['⌘', 'O'], description: 'Open notebook', icon: <FileText className="h-3 w-3" /> },
          { keys: ['⌘', 'S'], description: 'Save current work', icon: <Save className="h-3 w-3" /> },
          { keys: ['⌘', 'D'], description: 'Delete selected', icon: <Trash2 className="h-3 w-3" /> },
        ]
      }
    ],
    editor: [
      {
        title: 'Text Editing',
        shortcuts: [
          { keys: ['⌘', 'Z'], description: 'Undo', icon: <Undo className="h-3 w-3" /> },
          { keys: ['⌘', 'Shift', 'Z'], description: 'Redo', icon: <Redo className="h-3 w-3" /> },
          { keys: ['⌘', 'C'], description: 'Copy', icon: <Copy className="h-3 w-3" /> },
          { keys: ['⌘', 'V'], description: 'Paste' },
          { keys: ['⌘', 'X'], description: 'Cut' },
          { keys: ['⌘', 'A'], description: 'Select all' },
        ]
      },
      {
        title: 'Formatting',
        shortcuts: [
          { keys: ['⌘', 'B'], description: 'Bold text' },
          { keys: ['⌘', 'I'], description: 'Italic text' },
          { keys: ['⌘', 'U'], description: 'Underline text' },
          { keys: ['⌘', 'Shift', 'S'], description: 'Strikethrough' },
          { keys: ['⌘', 'E'], description: 'Code block' },
        ]
      }
    ],
    chat: [
      {
        title: 'Chat Actions',
        shortcuts: [
          { keys: ['Enter'], description: 'Send message', icon: <MessageSquare className="h-3 w-3" /> },
          { keys: ['Shift', 'Enter'], description: 'New line in message' },
          { keys: ['↑'], description: 'Edit last message', icon: <ChevronUp className="h-3 w-3" /> },
          { keys: ['⌘', 'Enter'], description: 'Regenerate response' },
          { keys: ['⌘', 'L'], description: 'Clear chat' },
        ]
      },
      {
        title: 'Citations',
        shortcuts: [
          { keys: ['⌘', 'Click'], description: 'Open citation in new tab' },
          { keys: ['Space'], description: 'Preview citation' },
          { keys: ['⌘', 'Shift', 'C'], description: 'Copy citation' },
        ]
      }
    ],
    diagrams: [
      {
        title: 'Diagram Controls',
        shortcuts: [
          { keys: ['⌘', 'G'], description: 'Generate diagram', icon: <Workflow className="h-3 w-3" /> },
          { keys: ['⌘', 'E'], description: 'Export diagram', icon: <Download className="h-3 w-3" /> },
          { keys: ['⌘', 'R'], description: 'Refresh diagram' },
          { keys: ['⌘', '+'], description: 'Zoom in' },
          { keys: ['⌘', '-'], description: 'Zoom out' },
          { keys: ['⌘', '0'], description: 'Reset zoom' },
        ]
      },
      {
        title: 'Navigation',
        shortcuts: [
          { keys: ['Space', 'Drag'], description: 'Pan diagram' },
          { keys: ['Scroll'], description: 'Zoom in/out' },
          { keys: ['←', '→', '↑', '↓'], description: 'Move selection' },
        ]
      }
    ],
    sources: [
      {
        title: 'Source Management',
        shortcuts: [
          { keys: ['⌘', 'U'], description: 'Upload source', icon: <Upload className="h-3 w-3" /> },
          { keys: ['⌘', 'Shift', 'D'], description: 'Download source', icon: <Download className="h-3 w-3" /> },
          { keys: ['⌘', 'R'], description: 'Rename source' },
          { keys: ['Delete'], description: 'Delete source', icon: <Trash2 className="h-3 w-3" /> },
        ]
      },
      {
        title: 'Navigation',
        shortcuts: [
          { keys: ['Tab'], description: 'Next source' },
          { keys: ['Shift', 'Tab'], description: 'Previous source' },
          { keys: ['Enter'], description: 'Open source' },
          { keys: ['Space'], description: 'Quick preview' },
        ]
      }
    ]
  };

  // Global keyboard shortcut handler
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Show shortcuts modal with Cmd+/
      if ((e.metaKey || e.ctrlKey) && e.key === '/') {
        e.preventDefault();
        onClose(); // Toggle if already open
      }
    };

    if (!isOpen) {
      window.addEventListener('keydown', handleKeyDown);
      return () => window.removeEventListener('keydown', handleKeyDown);
    }
  }, [isOpen, onClose]);

  const renderKey = (key: string) => {
    const specialKeys: Record<string, React.ReactNode> = {
      '⌘': <Command className="h-3 w-3" />,
      '⌥': <span className="text-xs">⌥</span>,
      '⇧': <span className="text-xs">⇧</span>,
      'Shift': <span className="text-xs">⇧</span>,
      'Enter': <span className="text-xs">↵</span>,
      'Tab': <span className="text-xs">⇥</span>,
      'Delete': <span className="text-xs">⌫</span>,
      'Space': <span className="text-xs">␣</span>,
      '↑': <ChevronUp className="h-3 w-3" />,
      '↓': <ChevronDown className="h-3 w-3" />,
      '←': <ArrowLeft className="h-3 w-3" />,
      '→': <ArrowRight className="h-3 w-3" />,
    };

    return specialKeys[key] || <span className="text-xs">{key}</span>;
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle>Keyboard Shortcuts</DialogTitle>
          <DialogDescription>
            Quick keyboard shortcuts to boost your productivity
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeCategory} onValueChange={setActiveCategory} className="w-full">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="editor">Editor</TabsTrigger>
            <TabsTrigger value="chat">Chat</TabsTrigger>
            <TabsTrigger value="diagrams">Diagrams</TabsTrigger>
            <TabsTrigger value="sources">Sources</TabsTrigger>
          </TabsList>

          {Object.entries(shortcutGroups).map(([category, groups]) => (
            <TabsContent key={category} value={category} className="mt-4">
              <ScrollArea className="h-[400px] pr-4">
                <div className="space-y-6">
                  {groups.map((group, groupIndex) => (
                    <div key={groupIndex}>
                      <h3 className="text-sm font-semibold mb-3">{group.title}</h3>
                      <div className="space-y-2">
                        {group.shortcuts.map((shortcut, index) => (
                          <div
                            key={index}
                            className="flex items-center justify-between py-1.5 px-2 rounded hover:bg-muted/50"
                          >
                            <div className="flex items-center gap-2">
                              {shortcut.icon && (
                                <div className="text-muted-foreground">
                                  {shortcut.icon}
                                </div>
                              )}
                              <span className="text-sm">{shortcut.description}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              {shortcut.keys.map((key, keyIndex) => (
                                <React.Fragment key={keyIndex}>
                                  <Badge
                                    variant="outline"
                                    className="px-2 py-0.5 min-w-[28px] flex items-center justify-center"
                                  >
                                    {renderKey(key)}
                                  </Badge>
                                  {keyIndex < shortcut.keys.length - 1 && (
                                    <span className="text-xs text-muted-foreground mx-0.5">+</span>
                                  )}
                                </React.Fragment>
                              ))}
                            </div>
                          </div>
                        ))}
                      </div>
                      {groupIndex < groups.length - 1 && <Separator className="mt-4" />}
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </TabsContent>
          ))}
        </Tabs>

        <div className="flex items-center justify-between pt-4 border-t">
          <p className="text-xs text-muted-foreground">
            Press <Badge variant="outline" className="mx-1 px-1.5 py-0">⌘ /</Badge> to toggle this dialog
          </p>
          <p className="text-xs text-muted-foreground">
            Customize shortcuts in <Badge variant="outline" className="mx-1 px-1.5 py-0">Settings</Badge>
          </p>
        </div>
      </DialogContent>
    </Dialog>
  );
};

// Hook to use keyboard shortcuts
export const useKeyboardShortcut = (
  keys: string[],
  callback: () => void,
  enabled = true
) => {
  useEffect(() => {
    if (!enabled) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      const isCmd = e.metaKey || e.ctrlKey;
      const isShift = e.shiftKey;
      const isAlt = e.altKey;
      
      // Check if the key combination matches
      const matches = keys.every(key => {
        switch (key.toLowerCase()) {
          case 'cmd':
          case '⌘':
            return isCmd;
          case 'shift':
          case '⇧':
            return isShift;
          case 'alt':
          case '⌥':
            return isAlt;
          default:
            return e.key.toLowerCase() === key.toLowerCase();
        }
      });

      if (matches) {
        e.preventDefault();
        callback();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [keys, callback, enabled]);
};

export default KeyboardShortcuts;