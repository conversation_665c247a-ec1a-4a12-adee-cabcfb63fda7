import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Plus, Search, LogOut } from 'lucide-react';
import { DASHBOARD_CONSTANTS, DASHBOARD_CLASSES } from './constants';

const DashboardHeader = () => {
  const navigate = useNavigate();
  const [isSearchFocused, setIsSearchFocused] = useState(false);

  return (
    <header className="bg-background border-b border-border px-6 py-4">
      <div className="w-full">
        <div className="flex items-center justify-between">
          {/* Logo and Brand */}
          <div className="flex items-center space-x-4">
            <button 
              onClick={() => navigate('/lighthouse-lm')}
              className="flex items-center space-x-2 group transition-all duration-300 hover:scale-105 rounded-lg p-1"
              aria-label="Go to dashboard home"
            >
              <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center transition-all duration-300 group-hover:shadow-lg group-active:scale-95">
                <span className="text-primary-foreground font-bold text-sm transition-transform duration-300 group-hover:scale-110">{DASHBOARD_CONSTANTS.BRAND_INITIALS}</span>
              </div>
              <h1 className="text-xl font-semibold text-foreground transition-colors duration-300 group-hover:text-primary">{DASHBOARD_CONSTANTS.BRAND_NAME}</h1>
            </button>
          </div>

          {/* Search Bar */}
          <div className="flex-1 max-w-xl mx-8">
            <div className="relative group">
              <Search className={DASHBOARD_CLASSES.searchIcon(isSearchFocused)} aria-hidden="true" />
              <input
                type="text"
                placeholder="Search notebooks..."
                className={`w-full pl-10 pr-4 py-2 border rounded-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent hover:border-border/80 hover:shadow-sm bg-background text-foreground ${
                  isSearchFocused ? 'border-primary/50 shadow-md' : 'border-border'
                }`}
                onFocus={() => setIsSearchFocused(true)}
                onBlur={() => setIsSearchFocused(false)}
                aria-label="Search notebooks"
              />
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center space-x-4">
            <Button
              onClick={() => navigate('/lighthouse-lm/new')}
              className="flex items-center space-x-2 bg-primary hover:bg-primary/90 text-primary-foreground transition-all duration-300 hover:scale-105 hover:shadow-lg active:scale-95 group relative overflow-hidden"
              aria-label="Create a new notebook"
            >
              <Plus className="w-4 h-4 transition-all duration-300 group-hover:rotate-90 group-hover:scale-110 relative z-10" aria-hidden="true" />
              <span className="transition-all duration-300 group-hover:tracking-wide relative z-10">New Notebook</span>
            </Button>


          </div>
        </div>
      </div>
    </header>
  );
};

export default DashboardHeader;