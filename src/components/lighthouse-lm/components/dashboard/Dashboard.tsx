import React from 'react';
import DashboardHeader from './DashboardHeader';
import NotebookGrid from '../layout/NotebookGrid';
import EmptyDashboard from './EmptyDashboard';
import { useNotebooks } from '../../hooks/useNotebooks';
import { GridSkeleton } from '../shared/LoadingSkeleton';
import { DASHBOARD_CONSTANTS } from './constants';

interface DashboardProps {
  onNotebookSelect?: (notebookId: string) => void;
}

const Dashboard: React.FC<DashboardProps> = ({ onNotebookSelect }) => {
  const { notebooks, isLoading, error, isError } = useNotebooks();
  const hasNotebooks = notebooks && notebooks.length > 0;

  // Show notebooks loading state with skeleton
  if (isLoading) {
    return (
      <div className="min-h-screen bg-background">
        <DashboardHeader />
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <GridSkeleton count={6} />
        </main>
      </div>
    );
  }

  // Show notebooks error if present
  if (isError && error) {
    return (
      <div className="min-h-screen bg-muted/30">
        <DashboardHeader />
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="mb-8">
            <h1 className="text-4xl font-medium text-foreground mb-2">{DASHBOARD_CONSTANTS.WELCOME_TEXT}</h1>
          </div>
          <div className="text-center py-16">
            <p className="text-destructive">Error loading notebooks: {error}</p>
            <button
              onClick={() => window.location.reload()}
              className="mt-4 px-4 py-2 bg-primary text-primary-foreground rounded hover:bg-primary/90"
            >
              Retry
            </button>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <DashboardHeader />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="font-medium text-foreground mb-2 text-5xl">{DASHBOARD_CONSTANTS.WELCOME_TEXT}</h1>
        </div>

        {hasNotebooks ? <NotebookGrid onNotebookSelect={onNotebookSelect} /> : <EmptyDashboard />}
      </main>
    </div>
  );
};

export default Dashboard;