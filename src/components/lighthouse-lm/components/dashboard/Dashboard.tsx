import React, { useState, useEffect } from 'react';
import DashboardHeader from './DashboardHeader';
import NotebookGrid from '../notebook/NotebookGrid';
import EmptyDashboard from './EmptyDashboard';
import { useNotebooks } from '../../hooks/useNotebooks';
import { useNotebookGeneration } from '../../hooks/useNotebookGeneration';
import { useSources } from '../../hooks/useSources';
import { GridSkeleton } from '../shared/LoadingSkeleton';
import { DASHBOARD_CONSTANTS } from './constants';
import OnboardingFlow from '../onboarding/OnboardingFlow';
import SearchBar from '../search/SearchBar';
import NotificationCenter, { Notification } from '../notifications/NotificationCenter';
import KeyboardShortcuts from '../shortcuts/KeyboardShortcuts';
import QuickActions from '../quickactions/QuickActions';

interface DashboardProps {
  onNotebookSelect?: (notebookId: string) => void;
}

const Dashboard: React.FC<DashboardProps> = ({ onNotebookSelect }) => {
  const { notebooks, isLoading, error, isError, createNotebook } = useNotebooks();
  const { generateNotebookContent } = useNotebookGeneration();
  const hasNotebooks = notebooks && notebooks.length > 0;
  
  // UI State
  const [showOnboarding, setShowOnboarding] = useState(false);
  const [showKeyboardShortcuts, setShowKeyboardShortcuts] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [notifications, setNotifications] = useState<Notification[]>([]);
  
  // Check if user needs onboarding
  useEffect(() => {
    const hasSeenOnboarding = localStorage.getItem('lighthouse-onboarding-complete');
    if (!hasSeenOnboarding && !hasNotebooks && !isLoading) {
      setShowOnboarding(true);
    }
  }, [hasNotebooks, isLoading]);

  const handleOnboardingComplete = () => {
    localStorage.setItem('lighthouse-onboarding-complete', 'true');
    setShowOnboarding(false);
  };

  const handleSearch = (query: string, filters?: any) => {
    setSearchQuery(query);
    // Search is handled by filtering the notebooks array in the render method
  };

  const handleMarkNotificationRead = (id: string) => {
    setNotifications(prev => 
      prev.map(n => n.id === id ? { ...n, read: true } : n)
    );
  };

  const handleMarkAllNotificationsRead = () => {
    setNotifications(prev => prev.map(n => ({ ...n, read: true })));
  };

  const handleClearNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  const handleClearAllNotifications = () => {
    setNotifications([]);
  };

  // Quick action handlers
  const handleCreateNotebook = async () => {
    try {
      const newNotebook = await createNotebook({
        title: "New Notebook",
        description: "Created from dashboard"
      });
      if (onNotebookSelect && newNotebook) {
        onNotebookSelect(newNotebook.id);
      }
    } catch (error) {
    }
  };

  const handleUploadSource = () => {
    // This would typically open a file dialog or source management modal
    const event = new CustomEvent('open-source-management');
    window.dispatchEvent(event);
  };

  const handleStartChat = () => {
    // This would typically navigate to or open a chat interface
  };

  const handleGenerateDiagram = () => {
    // This would typically open diagram generation interface
  };

  const handleOpenSearch = () => {
    document.getElementById('lighthouse-search')?.focus();
  };

  const handleImportFromWeb = () => {
    // This would typically open a web import dialog
  };

  // Show notebooks loading state with skeleton
  if (isLoading) {
    return (
      <div className="min-h-screen bg-background">
        <DashboardHeader />
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <GridSkeleton count={6} />
        </main>
      </div>
    );
  }

  // Show notebooks error if present
  if (isError && error) {
    return (
      <div className="min-h-screen bg-muted/30">
        <DashboardHeader />
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="mb-8">
            <h1 className="text-4xl font-medium text-foreground mb-2">{DASHBOARD_CONSTANTS.WELCOME_TEXT}</h1>
          </div>
          <div className="text-center py-16">
            <p className="text-destructive">Error loading notebooks: {error}</p>
            <button
              onClick={() => window.location.reload()}
              className="mt-4 px-4 py-2 bg-primary text-primary-foreground rounded hover:bg-primary/90"
            >
              Retry
            </button>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <DashboardHeader>
        {/* Add Search Bar and Notification Center to header */}
        <div className="flex items-center gap-4">
          <SearchBar
            onSearch={handleSearch}
            className="w-96"
            suggestions={[]}
            recentSearches={[]}
          />
          <NotificationCenter
            notifications={notifications}
            onMarkAsRead={handleMarkNotificationRead}
            onMarkAllAsRead={handleMarkAllNotificationsRead}
            onClear={handleClearNotification}
            onClearAll={handleClearAllNotifications}
          />
        </div>
      </DashboardHeader>
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="font-medium text-foreground mb-2 text-5xl">{DASHBOARD_CONSTANTS.WELCOME_TEXT}</h1>
        </div>

        {hasNotebooks ? (
          <>
            {/* Search and Filters */}
            <div className="mb-8">
              <SearchBar 
                onSearch={handleSearch}
                placeholder="Search notebooks..."
                className="max-w-md"
              />
            </div>
            
            {/* Notebook Grid with Search Filtering */}
            <NotebookGrid 
              notebooks={notebooks.filter(notebook => 
                notebook.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                (notebook.description && notebook.description.toLowerCase().includes(searchQuery.toLowerCase()))
              )}
              onNotebookSelect={onNotebookSelect}
            />
          </>
        ) : (
          <EmptyDashboard onCreateNotebook={handleCreateNotebook} />
        )}
      </main>

      {/* Onboarding Flow */}
      {showOnboarding && (
        <OnboardingFlow
          onComplete={handleOnboardingComplete}
          onSkip={handleOnboardingComplete}
        />
      )}

      {/* Keyboard Shortcuts Modal */}
      <KeyboardShortcuts
        isOpen={showKeyboardShortcuts}
        onClose={() => setShowKeyboardShortcuts(false)}
      />

      {/* Quick Actions Floating Menu */}
      <QuickActions
        onCreateNotebook={handleCreateNotebook}
        onUploadSource={handleUploadSource}
        onStartChat={handleStartChat}
        onGenerateDiagram={handleGenerateDiagram}
        onOpenSearch={handleOpenSearch}
        onImportFromWeb={handleImportFromWeb}
      />
    </div>
  );
};

export default Dashboard;