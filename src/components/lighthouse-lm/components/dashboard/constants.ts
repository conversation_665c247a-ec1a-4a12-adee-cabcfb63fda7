// Shared constants for dashboard components
export const DASHBOARD_CONSTANTS = {
  WELCOME_TEXT: 'Welcome to LighthouseLM',
  BRAND_NAME: 'LighthouseLM',
  BRAND_INITIALS: 'IL',
} as const;

export const DASHBOARD_STYLES = {
  CONTAINER: 'max-w-7xl mx-auto',
  MAIN_PADDING: 'px-6 py-[60px]',
  HEADER_PADDING: 'px-6 py-4',
  TRANSITION: 'transition-all duration-300',
  BUTTON_BASE: 'bg-primary hover:bg-primary/90 text-primary-foreground transition-all duration-300 hover:scale-105 active:scale-95 group',
  BUTTON_HOVER: 'hover:shadow-lg hover:scale-105 active:scale-95',
  SCALE_HOVER: 'hover:scale-105',
  SCALE_ACTIVE: 'active:scale-95',
  FOCUS_RING: 'focus:ring-2 focus:ring-primary focus:ring-offset-2',
} as const;

export const DASHBOARD_CLASSES = {
  searchIcon: (isFocused: boolean) =>
    `absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 ${DASHBOARD_STYLES.TRANSITION} ${
      isFocused ? 'text-primary scale-110' : 'text-muted-foreground group-hover:text-foreground'
    }`,
  primaryButton: `${DASHBOARD_STYLES.BUTTON_BASE} ${DASHBOARD_STYLES.BUTTON_HOVER}`,
  disabledButton: 'disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100',
} as const;