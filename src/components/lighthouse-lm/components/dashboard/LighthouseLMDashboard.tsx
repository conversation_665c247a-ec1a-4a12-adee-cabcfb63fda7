import React, { useState } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import Dashboard from './Dashboard';
import NotebookPage from '../notebook/NotebookPage';

// Create a separate query client for LighthouseLM to avoid conflicts
const lighthouseLmQueryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
});

const LighthouseLMDashboard = () => {
  const [selectedNotebookId, setSelectedNotebookId] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'dashboard' | 'notebook'>('dashboard');

  const handleNotebookSelect = (notebookId: string) => {
    // console.log('Selecting notebook:', notebookId);
    setSelectedNotebookId(notebookId);
    setViewMode('notebook');
  };

  const handleBackToDashboard = () => {
    // console.log('Going back to dashboard');
    setViewMode('dashboard');
    setSelectedNotebookId(null);
  };

  return (
    <div className="h-full w-full overflow-hidden bg-background">
      <QueryClientProvider client={lighthouseLmQueryClient}>
        {viewMode === 'dashboard' ? (
          <Dashboard onNotebookSelect={handleNotebookSelect} />
        ) : selectedNotebookId ? (
          <NotebookPage 
            notebookId={selectedNotebookId} 
            onBack={handleBackToDashboard}
          />
        ) : (
          <Dashboard onNotebookSelect={handleNotebookSelect} />
        )}
      </QueryClientProvider>
    </div>
  );
};

export default LighthouseLMDashboard;