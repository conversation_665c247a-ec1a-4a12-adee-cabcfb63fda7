import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Plus, Save, Bot, Sparkles, Presentation, Grid, ChevronLeft, ChevronRight, Play, Edit, Trash2, Download, Upload, RefreshCw, Copy, Palette, Layout, Type, Image, Maximize, Eye, EyeOff, Settings, ArrowUp, ArrowDown, SkipForward, Pause, MessageCircle } from 'lucide-react';
import { Card } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { invoke } from '@tauri-apps/api/core';
import { EmptyState } from '@/components/shared/sidebar';
import { useToast } from '../../hooks/useToast';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Input } from '@/components/ui/input';
import { Slider } from '@/components/ui/slider';
import { useChatMessages } from '@/components/lighthouse-lm/hooks/useChatMessages';

interface Slide {
  id: string;
  title: string;
  content: string;
  notes?: string;
  layout?: string;
  transition?: string;
  background?: string;
  class?: string;
  order: number;
  created_at: string;
  updated_at: string;
}

interface SlideTransition {
  name: string;
  label: string;
  description: string;
}

interface SlideLayout {
  name: string;
  label: string;
  description: string;
  icon?: React.ReactNode;
}

interface Presentation {
  id: string;
  title: string;
  description?: string;
  author?: string;
  theme: string;
  slides: Slide[];
  frontmatter: SlidevFrontmatter;
  created_at: string;
  updated_at: string;
}

interface SlidevFrontmatter {
  theme: string;
  background?: string;
  class?: string;
  highlighter?: string;
  lineNumbers?: boolean;
  info?: string;
  persist?: boolean;
  exportFilename?: string;
  title?: string;
}

interface GenerateSlidesRequest {
  prompt: string;
  theme?: string;
  slide_count?: number;
  audience?: string;
  duration?: number;
  context?: string;
}

interface GenerateSlidesResponse {
  presentation: Presentation;
  metadata: GenerationMetadata;
}

interface GenerationMetadata {
  model: string;
  generation_time: number;
  token_usage: {
    input_tokens: number;
    output_tokens: number;
    total_tokens: number;
  };
  generated_at: string;
}

interface PresentationSummary {
  id: string;
  title: string;
  description?: string;
  author?: string;
  slide_count: number;
  theme: string;
  created_at: string;
  updated_at: string;
}

interface SlidevTabProps {
  notebookId?: string;
}

const SlidevTab: React.FC<SlidevTabProps> = ({ notebookId }) => {
  const { toast } = useToast();
  const { messages: chatMessages, isLoading: chatLoading } = useChatMessages(notebookId);
  const [presentations, setPresentations] = useState<PresentationSummary[]>([]);
  const [selectedPresentation, setSelectedPresentation] = useState<Presentation | null>(null);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [isEditingSlide, setIsEditingSlide] = useState(false);
  const [editingSlideIndex, setEditingSlideIndex] = useState<number | null>(null);
  const [slideContent, setSlideContent] = useState('');
  const [slideTitle, setSlideTitle] = useState('');
  const [slideNotes, setSlideNotes] = useState('');
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [presentationToDelete, setPresentationToDelete] = useState<string | null>(null);
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [presentationTitle, setPresentationTitle] = useState('');
  const [isPlaybackMode, setIsPlaybackMode] = useState(false);
  const [playbackSlide, setPlaybackSlide] = useState(0);
  const [showPresenterNotes, setShowPresenterNotes] = useState(false);
  const [slideTransition, setSlideTransition] = useState('slide-left');
  const [slideLayout, setSlideLayout] = useState('default');
  const [slideBackground, setSlideBackground] = useState('');
  const [slideClass, setSlideClass] = useState('');
  const [isDragging, setIsDragging] = useState(false);
  const [draggedSlide, setDraggedSlide] = useState<number | null>(null);
  const [dragOverSlide, setDragOverSlide] = useState<number | null>(null);
  const [useChatContext, setUseChatContext] = useState(false);
  const [chatMessageCount, setChatMessageCount] = useState(10);
  
  // AI Generation state
  const [aiPrompt, setAiPrompt] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [showAiGenerator, setShowAiGenerator] = useState(false);
  const [aiTheme, setAiTheme] = useState('default');
  const [aiSlideCount, setAiSlideCount] = useState(10);
  const [aiAudience, setAiAudience] = useState('general');
  const [aiDuration, setAiDuration] = useState(15);
  
  // View mode state
  const [viewMode, setViewMode] = useState<'list' | 'grid' | 'compact'>('grid');
  const [gridSize, setGridSize] = useState<'small' | 'medium' | 'large'>('medium');
  
  // Available transitions and layouts
  const slideTransitions: SlideTransition[] = [
    { name: 'slide-left', label: 'Slide Left', description: 'Slide from right to left' },
    { name: 'slide-right', label: 'Slide Right', description: 'Slide from left to right' },
    { name: 'slide-up', label: 'Slide Up', description: 'Slide from bottom to top' },
    { name: 'slide-down', label: 'Slide Down', description: 'Slide from top to bottom' },
    { name: 'fade', label: 'Fade', description: 'Fade in/out' },
    { name: 'fade-out', label: 'Fade Out', description: 'Fade out then in' },
    { name: 'none', label: 'None', description: 'No transition' },
  ];
  
  const slideLayouts: SlideLayout[] = [
    { name: 'default', label: 'Default', description: 'Standard layout', icon: <Layout className="h-4 w-4" /> },
    { name: 'center', label: 'Center', description: 'Centered content', icon: <Grid className="h-4 w-4" /> },
    { name: 'cover', label: 'Cover', description: 'Full cover slide', icon: <Maximize className="h-4 w-4" /> },
    { name: 'intro', label: 'Intro', description: 'Introduction layout', icon: <Type className="h-4 w-4" /> },
    { name: 'two-cols', label: 'Two Columns', description: 'Two column layout', icon: <Layout className="h-4 w-4" /> },
    { name: 'image-right', label: 'Image Right', description: 'Image on the right', icon: <Image className="h-4 w-4" /> },
    { name: 'image-left', label: 'Image Left', description: 'Image on the left', icon: <Image className="h-4 w-4" /> },
  ];
  
  // Refs
  const slideContainerRef = useRef<HTMLDivElement>(null);
  const overviewGridRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Save and Load handlers
  const handleSavePresentation = async (presentation: Presentation) => {
    try {
      setIsLoading(true);
      const savedPath = await invoke<string>('save_presentation', {
        presentation
      });
      
      toast({
        title: 'Presentation Saved',
        description: `Successfully saved to ${savedPath}`,
      });
      
      // Refresh the presentations list
      await handleLoadPresentations();
    } catch (error) {
      console.error('Failed to save presentation:', error);
      toast({
        title: 'Save Failed',
        description: error instanceof Error ? error.message : 'Failed to save presentation',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleLoadPresentations = async () => {
    try {
      setIsLoading(true);
      const savedPresentations = await invoke<PresentationSummary[]>('list_presentations');
      setPresentations(savedPresentations);
      
      if (savedPresentations.length > 0) {
        toast({
          title: 'Presentations Loaded',
          description: `Found ${savedPresentations.length} presentation${savedPresentations.length > 1 ? 's' : ''}`,
        });
      }
    } catch (error) {
      console.error('Failed to load presentations:', error);
      toast({
        title: 'Load Failed',
        description: error instanceof Error ? error.message : 'Failed to load presentations',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleLoadPresentation = async (presentationId: string) => {
    try {
      setIsLoading(true);
      const presentation = await invoke<Presentation>('load_presentation', {
        presentationId
      });
      setSelectedPresentation(presentation);
      setCurrentSlide(0);
    } catch (error) {
      console.error('Failed to load presentation:', error);
      toast({
        title: 'Load Failed',
        description: error instanceof Error ? error.message : 'Failed to load presentation',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeletePresentation = async (presentationId: string) => {
    try {
      setIsLoading(true);
      await invoke('delete_presentation', {
        presentationId
      });
      
      toast({
        title: 'Presentation Deleted',
        description: 'Successfully removed the presentation',
      });
      
      // Clear selection if deleted presentation was selected
      if (selectedPresentation?.id === presentationId) {
        setSelectedPresentation(null);
      }
      
      // Refresh the list
      await handleLoadPresentations();
    } catch (error) {
      console.error('Failed to delete presentation:', error);
      toast({
        title: 'Delete Failed',
        description: error instanceof Error ? error.message : 'Failed to delete presentation',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
      setDeleteDialogOpen(false);
      setPresentationToDelete(null);
    }
  };

  const handleExportPresentation = async (presentation: Presentation) => {
    try {
      setIsLoading(true);
      const markdown = await invoke<string>('export_presentation_markdown', {
        presentation
      });
      
      // Create a download link
      const blob = new Blob([markdown], { type: 'text/markdown' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${presentation.title.replace(/\s+/g, '-').toLowerCase()}.md`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      toast({
        title: 'Export Successful',
        description: 'Presentation exported as Slidev markdown',
      });
    } catch (error) {
      console.error('Failed to export presentation:', error);
      toast({
        title: 'Export Failed',
        description: error instanceof Error ? error.message : 'Failed to export presentation',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreatePresentation = () => {
    const now = new Date().toISOString();
    const newPresentation: Presentation = {
      id: `pres-${Date.now()}`,
      title: 'New Presentation',
      description: 'A new Slidev presentation',
      author: 'Lighthouse LM User',
      theme: 'default',
      slides: [
        {
          id: 'slide-1',
          title: 'Welcome',
          content: '## Welcome to Your Presentation\n\nStart creating amazing slides!',
          notes: 'Speaker notes go here',
          layout: 'cover',
          order: 0,
          created_at: now,
          updated_at: now
        }
      ],
      frontmatter: {
        theme: 'default',
        highlighter: 'shiki',
        lineNumbers: false,
        title: 'New Presentation'
      },
      created_at: now,
      updated_at: now
    };
    setSelectedPresentation(newPresentation);
    setCurrentSlide(0);
    
    toast({
      title: 'New Presentation Created',
      description: 'You can now start adding slides',
    });
  };

  const generateSlidesFromAI = async (prompt: string) => {
    if (!prompt.trim()) {
      toast({
        title: 'Invalid Prompt',
        description: 'Please enter a description for your presentation',
        variant: 'destructive',
      });
      return;
    }

    setIsGenerating(true);
    try {
      // Build context from chat messages if enabled
      let context = notebookId ? `Generated for notebook: ${notebookId}` : undefined;
      
      if (useChatContext && notebookId && chatMessages.length > 0) {
        // Get the most recent messages
        const recentMessages = chatMessages.slice(-chatMessageCount);
        
        // Format chat context
        const chatContext = recentMessages.map(msg => 
          `[${msg.role.toUpperCase()}]: ${msg.content}`
        ).join('\n\n');
        
        context = `Chat Context:\n${chatContext}\n\n${context || ''}`;
      }

      const request: GenerateSlidesRequest = {
        prompt,
        theme: aiTheme,
        slide_count: aiSlideCount,
        audience: aiAudience,
        duration: aiDuration,
        context
      };

      const response = await invoke<GenerateSlidesResponse>('generate_slides_from_prompt', {
        request
      });

      setSelectedPresentation(response.presentation);
      setCurrentSlide(0);
      setAiPrompt('');
      setShowAiGenerator(false);
      
      toast({
        title: 'Slides Generated!',
        description: `Created ${response.presentation.slides.length} slides using ${response.metadata.model}`,
      });
    } catch (error) {
      console.error('Failed to generate slides:', error);
      toast({
        title: 'Generation Failed',
        description: error instanceof Error ? error.message : 'Failed to generate slides from AI',
        variant: 'destructive',
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleSlideClick = (slideIndex: number) => {
    setCurrentSlide(slideIndex);
    if (selectedPresentation) {
      const slide = selectedPresentation.slides[slideIndex];
      if (slide) {
        setSlideContent(slide.content);
        setSlideTitle(slide.title);
        setSlideNotes(slide.notes || '');
      }
    }
  };

  const handleEditSlide = (slideIndex: number) => {
    if (!selectedPresentation) return;
    const slide = selectedPresentation.slides[slideIndex];
    if (slide) {
      setEditingSlideIndex(slideIndex);
      setSlideContent(slide.content);
      setSlideTitle(slide.title);
      setSlideNotes(slide.notes || '');
      setSlideLayout(slide.layout || 'default');
      setSlideTransition(slide.transition || 'slide-left');
      setSlideBackground(slide.background || '');
      setSlideClass(slide.class || '');
      setIsEditingSlide(true);
    }
  };

  const handleSaveSlideEdit = () => {
    if (!selectedPresentation || editingSlideIndex === null) return;
    
    const updatedSlides = [...selectedPresentation.slides];
    updatedSlides[editingSlideIndex] = {
      ...updatedSlides[editingSlideIndex],
      title: slideTitle,
      content: slideContent,
      notes: slideNotes,
      layout: slideLayout,
      transition: slideTransition,
      background: slideBackground,
      class: slideClass,
      updated_at: new Date().toISOString()
    };
    
    const updatedPresentation = {
      ...selectedPresentation,
      slides: updatedSlides,
      updated_at: new Date().toISOString()
    };
    
    setSelectedPresentation(updatedPresentation);
    setIsEditingSlide(false);
    setEditingSlideIndex(null);
    
    toast({
      title: 'Slide Updated',
      description: 'Your changes have been saved',
    });
  };

  const handleCancelSlideEdit = () => {
    setIsEditingSlide(false);
    setEditingSlideIndex(null);
    setSlideContent('');
    setSlideTitle('');
    setSlideNotes('');
    setSlideLayout('default');
    setSlideTransition('slide-left');
    setSlideBackground('');
    setSlideClass('');
  };

  const handleDuplicateSlide = (slideIndex: number) => {
    if (!selectedPresentation) return;
    
    const slideToDuplicate = selectedPresentation.slides[slideIndex];
    if (!slideToDuplicate) return;
    
    const now = new Date().toISOString();
    const newSlide: Slide = {
      ...slideToDuplicate,
      id: `slide-${Date.now()}`,
      title: `${slideToDuplicate.title} (Copy)`,
      order: slideIndex + 1,
      created_at: now,
      updated_at: now
    };
    
    const updatedSlides = [
      ...selectedPresentation.slides.slice(0, slideIndex + 1),
      newSlide,
      ...selectedPresentation.slides.slice(slideIndex + 1).map(s => ({
        ...s,
        order: s.order + 1
      }))
    ];
    
    const updatedPresentation = {
      ...selectedPresentation,
      slides: updatedSlides,
      updated_at: now
    };
    
    setSelectedPresentation(updatedPresentation);
    setCurrentSlide(slideIndex + 1);
    
    toast({
      title: 'Slide Duplicated',
      description: 'Slide has been duplicated successfully',
    });
  };

  const handleMoveSlide = (fromIndex: number, toIndex: number) => {
    if (!selectedPresentation || fromIndex === toIndex) return;
    
    const slides = [...selectedPresentation.slides];
    const [movedSlide] = slides.splice(fromIndex, 1);
    slides.splice(toIndex, 0, movedSlide);
    
    // Update order values
    const updatedSlides = slides.map((slide, index) => ({
      ...slide,
      order: index
    }));
    
    const updatedPresentation = {
      ...selectedPresentation,
      slides: updatedSlides,
      updated_at: new Date().toISOString()
    };
    
    setSelectedPresentation(updatedPresentation);
    setCurrentSlide(toIndex);
  };

  // Drag and drop handlers
  const handleDragStart = (e: React.DragEvent, slideIndex: number) => {
    setIsDragging(true);
    setDraggedSlide(slideIndex);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e: React.DragEvent, slideIndex: number) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    setDragOverSlide(slideIndex);
  };

  const handleDragEnd = () => {
    setIsDragging(false);
    setDraggedSlide(null);
    setDragOverSlide(null);
  };

  const handleDrop = (e: React.DragEvent, toIndex: number) => {
    e.preventDefault();
    if (draggedSlide !== null && draggedSlide !== toIndex) {
      handleMoveSlide(draggedSlide, toIndex);
    }
    handleDragEnd();
  };

  // Playback mode functions
  const enterPlaybackMode = () => {
    setIsPlaybackMode(true);
    setPlaybackSlide(currentSlide);
    setShowPresenterNotes(false);
  };

  const exitPlaybackMode = () => {
    setIsPlaybackMode(false);
    setCurrentSlide(playbackSlide);
  };

  const nextSlide = () => {
    if (!selectedPresentation) return;
    if (playbackSlide < selectedPresentation.slides.length - 1) {
      setPlaybackSlide(playbackSlide + 1);
    }
  };

  const previousSlide = () => {
    if (playbackSlide > 0) {
      setPlaybackSlide(playbackSlide - 1);
    }
  };

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (isPlaybackMode) {
        switch (e.key) {
          case 'ArrowRight':
          case ' ':
            nextSlide();
            break;
          case 'ArrowLeft':
            previousSlide();
            break;
          case 'Escape':
            exitPlaybackMode();
            break;
          case 'n':
            setShowPresenterNotes(!showPresenterNotes);
            break;
        }
      } else if (selectedPresentation && !isEditingSlide && !isEditingTitle) {
        switch (e.key) {
          case 'ArrowDown':
            if (currentSlide < selectedPresentation.slides.length - 1) {
              setCurrentSlide(currentSlide + 1);
            }
            break;
          case 'ArrowUp':
            if (currentSlide > 0) {
              setCurrentSlide(currentSlide - 1);
            }
            break;
          case 'Enter':
            if (e.ctrlKey || e.metaKey) {
              enterPlaybackMode();
            }
            break;
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isPlaybackMode, playbackSlide, showPresenterNotes, selectedPresentation, currentSlide, isEditingSlide, isEditingTitle]);

  const handleUpdatePresentationTitle = () => {
    if (!selectedPresentation) return;
    
    const updatedPresentation = {
      ...selectedPresentation,
      title: presentationTitle,
      updated_at: new Date().toISOString()
    };
    
    setSelectedPresentation(updatedPresentation);
    setIsEditingTitle(false);
    
    toast({
      title: 'Title Updated',
      description: 'Presentation title has been updated',
    });
  };

  const handlePresentationClick = async (presentationSummary: PresentationSummary) => {
    await handleLoadPresentation(presentationSummary.id);
  };

  const handleAddSlide = () => {
    if (!selectedPresentation) return;
    
    const now = new Date().toISOString();
    const newSlide: Slide = {
      id: `slide-${Date.now()}`,
      title: `Slide ${selectedPresentation.slides.length + 1}`,
      content: '## New Slide\n\nAdd your content here',
      layout: 'default',
      order: selectedPresentation.slides.length,
      created_at: now,
      updated_at: now
    };
    
    const updatedPresentation = {
      ...selectedPresentation,
      slides: [...selectedPresentation.slides, newSlide],
      updated_at: now
    };
    
    setSelectedPresentation(updatedPresentation);
    setCurrentSlide(selectedPresentation.slides.length);
    
    toast({
      title: 'Slide Added',
      description: 'New slide added to presentation',
    });
  };

  const handleRemoveSlide = (slideIndex: number) => {
    if (!selectedPresentation || selectedPresentation.slides.length <= 1) return;
    
    const updatedSlides = selectedPresentation.slides.filter((_, index) => index !== slideIndex);
    const updatedPresentation = {
      ...selectedPresentation,
      slides: updatedSlides,
      updated_at: new Date().toISOString()
    };
    
    setSelectedPresentation(updatedPresentation);
    if (currentSlide >= updatedSlides.length) {
      setCurrentSlide(updatedSlides.length - 1);
    }
    
    toast({
      title: 'Slide Removed',
      description: 'Slide has been removed from presentation',
    });
  };

  // Initialize by loading saved presentations
  useEffect(() => {
    handleLoadPresentations();
  }, []);

  // Import/Export functions
  const handleImportPresentation = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      const text = await file.text();
      let presentation: Presentation;
      
      if (file.name.endsWith('.json')) {
        presentation = JSON.parse(text);
      } else if (file.name.endsWith('.md')) {
        // Parse markdown to presentation
        const slides = text.split('---\n').filter(s => s.trim());
        const now = new Date().toISOString();
        
        presentation = {
          id: `imported-${Date.now()}`,
          title: file.name.replace(/\.(md|json)$/, ''),
          theme: 'default',
          slides: slides.map((slideText, index) => ({
            id: `slide-${Date.now()}-${index}`,
            title: `Slide ${index + 1}`,
            content: slideText.trim(),
            layout: 'default',
            order: index,
            created_at: now,
            updated_at: now
          })),
          frontmatter: {
            theme: 'default',
            highlighter: 'shiki',
            lineNumbers: false
          },
          created_at: now,
          updated_at: now
        };
      } else {
        throw new Error('Unsupported file format');
      }
      
      setSelectedPresentation(presentation);
      toast({
        title: 'Presentation Imported',
        description: `Successfully imported ${presentation.slides.length} slides`,
      });
    } catch (error) {
      console.error('Import failed:', error);
      toast({
        title: 'Import Failed',
        description: error instanceof Error ? error.message : 'Failed to import presentation',
        variant: 'destructive',
      });
    }
    
    // Reset input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const generateSlidesFromChat = async () => {
    if (!notebookId || chatMessages.length === 0) {
      toast({
        title: 'No Chat Context',
        description: 'No chat messages available to generate slides from',
        variant: 'destructive',
      });
      return;
    }

    setIsGenerating(true);
    try {
      // Analyze chat conversation to create intelligent prompt
      const recentMessages = chatMessages.slice(-10); // Last 10 messages
      
      // Extract key topics and themes from conversation
      const chatContent = recentMessages.map(msg => 
        `[${msg.role.toUpperCase()}]: ${msg.content}`
      ).join('\n\n');
      
      // Create intelligent prompt based on conversation flow
      const intelligentPrompt = `Based on the following conversation, create a comprehensive presentation that captures the key discussion points, decisions, and insights. Organize the content logically and provide clear, engaging slides.

Conversation:
${chatContent}

Please structure this as a professional presentation with:
1. A clear title slide summarizing the main topic
2. An agenda slide outlining key discussion points
3. Content slides covering each major topic with supporting details
4. A conclusion slide summarizing key takeaways
5. A Q&A slide for further discussion`;

      const request: GenerateSlidesRequest = {
        prompt: intelligentPrompt,
        theme: aiTheme,
        slide_count: Math.min(15, Math.max(5, recentMessages.length)), // Adaptive slide count
        audience: aiAudience,
        duration: aiDuration,
        context: `Generated from chat conversation in notebook: ${notebookId}\nTotal messages analyzed: ${recentMessages.length}`
      };

      const response = await invoke<GenerateSlidesResponse>('generate_slides_from_prompt', {
        request
      });

      setSelectedPresentation(response.presentation);
      setCurrentSlide(0);
      setShowAiGenerator(false);
      
      toast({
        title: 'Slides Generated from Chat!',
        description: `Created ${response.presentation.slides.length} slides from ${recentMessages.length} chat messages using ${response.metadata.model}`,
      });
    } catch (error) {
      console.error('Failed to generate slides from chat:', error);
      toast({
        title: 'Generation Failed',
        description: error instanceof Error ? error.message : 'Failed to generate slides from chat conversation',
        variant: 'destructive',
      });
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="flex-1 flex flex-col overflow-hidden">
      <div className="p-4 flex-shrink-0 space-y-2">
        <div className="flex flex-wrap gap-2">
          <Button
            variant="outline"
            size="sm"
            className="flex-1 min-w-[100px]"
            onClick={handleCreatePresentation}
          >
            <Plus className="h-3 w-3 mr-1" />
            <span className="text-xs">New</span>
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="flex-1 min-w-[60px]"
            onClick={() => setShowAiGenerator(true)}
          >
            <Bot className="h-3 w-3 mr-1" />
            <span className="text-xs">AI</span>
          </Button>
          {notebookId && chatMessages.length > 0 && (
            <Button
              variant="outline"
              size="sm"
              className="flex-1 min-w-[100px]"
              onClick={generateSlidesFromChat}
              disabled={isGenerating || isLoading}
            >
              <MessageCircle className="h-3 w-3 mr-1" />
              <span className="text-xs">From Chat</span>
            </Button>
          )}
          <Button
            variant="outline"
            size="sm"
            className="flex-1 min-w-[100px]"
            onClick={handleLoadPresentations}
          >
            <RefreshCw className="h-3 w-3 mr-1" />
            <span className="text-xs">Refresh</span>
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="flex-1 min-w-[100px]"
            onClick={handleImportClick}
          >
            <Upload className="h-3 w-3 mr-1" />
            <span className="text-xs">Import</span>
          </Button>
        </div>
        
        {showAiGenerator && (
          <div className="space-y-3 p-3 bg-muted rounded-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Sparkles className="h-4 w-4 text-primary" />
                <span className="text-sm font-medium">AI Slide Generator</span>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowAiGenerator(false)}
                className="h-6 w-6 p-0"
              >
                ×
              </Button>
            </div>
            
            <Textarea
              placeholder="Describe your presentation topic, key points, and target audience..."
              value={aiPrompt}
              onChange={(e) => setAiPrompt(e.target.value)}
              className="min-h-[100px]"
            />
            
            {notebookId && chatMessages.length > 0 && (
              <div className="flex items-center gap-2 p-2 bg-background rounded border">
                <MessageCircle className="h-4 w-4 text-muted-foreground" />
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <Switch
                      id="use-chat-context"
                      checked={useChatContext}
                      onCheckedChange={setUseChatContext}
                    />
                    <Label htmlFor="use-chat-context" className="text-xs">
                      Use chat context ({chatMessages.length} messages)
                    </Label>
                  </div>
                  {useChatContext && (
                    <div className="mt-2">
                      <Label className="text-xs text-muted-foreground mb-1 block">Messages to include</Label>
                      <Select 
                        value={chatMessageCount.toString()} 
                        onValueChange={(v) => setChatMessageCount(parseInt(v))}
                      >
                        <SelectTrigger className="h-7 text-xs">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="5">Last 5 messages</SelectItem>
                          <SelectItem value="10">Last 10 messages</SelectItem>
                          <SelectItem value="15">Last 15 messages</SelectItem>
                          <SelectItem value="20">Last 20 messages</SelectItem>
                          <SelectItem value={chatMessages.length.toString()}>All messages</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  )}
                </div>
              </div>
            )}
            
            <div className="grid grid-cols-2 gap-2">
              <div>
                <label className="text-xs text-muted-foreground mb-1 block">Theme</label>
                <Select value={aiTheme} onValueChange={setAiTheme}>
                  <SelectTrigger className="h-8 text-xs">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="default">Default</SelectItem>
                    <SelectItem value="seriph">Seriph</SelectItem>
                    <SelectItem value="bricks">Bricks</SelectItem>
                    <SelectItem value="apple-basic">Apple Basic</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <label className="text-xs text-muted-foreground mb-1 block">Slides</label>
                <Select value={aiSlideCount.toString()} onValueChange={(v) => setAiSlideCount(parseInt(v))}>
                  <SelectTrigger className="h-8 text-xs">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="5">5 slides</SelectItem>
                    <SelectItem value="10">10 slides</SelectItem>
                    <SelectItem value="15">15 slides</SelectItem>
                    <SelectItem value="20">20 slides</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <label className="text-xs text-muted-foreground mb-1 block">Audience</label>
                <Select value={aiAudience} onValueChange={setAiAudience}>
                  <SelectTrigger className="h-8 text-xs">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="general">General</SelectItem>
                    <SelectItem value="technical">Technical</SelectItem>
                    <SelectItem value="business">Business</SelectItem>
                    <SelectItem value="academic">Academic</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <label className="text-xs text-muted-foreground mb-1 block">Duration</label>
                <Select value={aiDuration.toString()} onValueChange={(v) => setAiDuration(parseInt(v))}>
                  <SelectTrigger className="h-8 text-xs">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="5">5 min</SelectItem>
                    <SelectItem value="10">10 min</SelectItem>
                    <SelectItem value="15">15 min</SelectItem>
                    <SelectItem value="30">30 min</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div className="flex gap-2">
              <Button
                size="sm"
                onClick={() => generateSlidesFromAI(aiPrompt)}
                disabled={!aiPrompt.trim() || isGenerating || isLoading}
                className="flex-1"
              >
                {isGenerating ? (
                  <>
                    <Sparkles className="h-3 w-3 mr-1 animate-pulse" />
                    <span className="text-xs">Generating...</span>
                  </>
                ) : (
                  <>
                    <Sparkles className="h-3 w-3 mr-1" />
                    <span className="text-xs">Generate Slides</span>
                  </>
                )}
              </Button>
            </div>
          </div>
        )}
      </div>

      <div className="flex-1 overflow-hidden">
        {presentations.length > 0 && !selectedPresentation && (
          <div className="p-4">
            <h3 className="text-sm font-medium mb-3">Saved Presentations</h3>
            <ScrollArea className="h-[calc(100vh-200px)]">
              <div className="space-y-2">
                {presentations.map((presentation) => (
                  <Card
                    key={presentation.id}
                    className="p-3 cursor-pointer hover:border-primary/50 transition-all"
                    onClick={() => handlePresentationClick(presentation)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        <h4 className="text-sm font-medium truncate">{presentation.title}</h4>
                        {presentation.description && (
                          <p className="text-xs text-muted-foreground truncate">{presentation.description}</p>
                        )}
                        <div className="flex items-center gap-2 mt-1">
                          <Badge variant="secondary" className="text-xs">
                            {presentation.slide_count} slides
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            {presentation.theme}
                          </Badge>
                        </div>
                      </div>
                      <div className="flex items-center gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            setPresentationToDelete(presentation.id);
                            setDeleteDialogOpen(true);
                          }}
                          className="h-7 w-7 p-0"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </ScrollArea>
          </div>
        )}
        
        {selectedPresentation ? (
          <>
            <div className="px-4 pb-2 flex-shrink-0">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2 min-w-0">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSelectedPresentation(null)}
                    className="h-7 w-7 p-0 flex-shrink-0"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  {isEditingTitle ? (
                    <div className="flex items-center gap-1 flex-1">
                      <input
                        type="text"
                        value={presentationTitle}
                        onChange={(e) => setPresentationTitle(e.target.value)}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') handleUpdatePresentationTitle();
                          if (e.key === 'Escape') setIsEditingTitle(false);
                        }}
                        className="text-sm font-medium bg-transparent border-b border-primary focus:outline-none flex-1"
                        autoFocus
                      />
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleUpdatePresentationTitle}
                        className="h-6 w-6 p-0"
                      >
                        ✓
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setIsEditingTitle(false)}
                        className="h-6 w-6 p-0"
                      >
                        ×
                      </Button>
                    </div>
                  ) : (
                    <h3
                      className="text-sm font-medium truncate cursor-pointer hover:text-primary"
                      onClick={() => {
                        setPresentationTitle(selectedPresentation.title);
                        setIsEditingTitle(true);
                      }}
                    >
                      {selectedPresentation.title}
                    </h3>
                  )}
                </div>
                <div className="flex items-center gap-1 flex-shrink-0">
                  <Badge variant="secondary" className="text-xs">
                    {selectedPresentation.slides.length} slides
                  </Badge>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={enterPlaybackMode}
                    className="h-7 px-2 text-xs"
                    disabled={isLoading || !selectedPresentation?.slides?.length}
                    title="Present (Ctrl/Cmd + Enter)"
                  >
                    <Play className="h-3 w-3 mr-1" />
                    Present
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleAddSlide}
                    className="h-7 px-2 text-xs"
                    disabled={isLoading}
                  >
                    <Plus className="h-3 w-3 mr-1" />
                    Add
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleSavePresentation(selectedPresentation)}
                    className="h-7 px-2 text-xs"
                    disabled={isLoading}
                  >
                    <Save className="h-3 w-3 mr-1" />
                    Save
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleExportPresentation(selectedPresentation)}
                    className="h-7 px-2 text-xs"
                    disabled={isLoading}
                  >
                    <Download className="h-3 w-3 mr-1" />
                    Export
                  </Button>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <Select value={viewMode} onValueChange={(value: any) => setViewMode(value)}>
                  <SelectTrigger className="w-20 h-7 text-xs">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="list">List</SelectItem>
                    <SelectItem value="grid">Grid</SelectItem>
                    <SelectItem value="compact">Compact</SelectItem>
                  </SelectContent>
                </Select>
                
                {viewMode === 'grid' && (
                  <Select value={gridSize} onValueChange={(value: any) => setGridSize(value)}>
                    <SelectTrigger className="w-20 h-7 text-xs">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="small">S</SelectItem>
                      <SelectItem value="medium">M</SelectItem>
                      <SelectItem value="large">L</SelectItem>
                    </SelectContent>
                  </Select>
                )}
              </div>
            </div>
            
            <div className="flex-1 overflow-hidden">
              <ScrollArea className="h-full">
                <div className="p-4">
                  <div className={`grid gap-3 ${
                    viewMode === 'grid' ? (
                      gridSize === 'small' ? 'grid-cols-4' :
                      gridSize === 'medium' ? 'grid-cols-3' :
                      'grid-cols-2'
                    ) : 'grid-cols-1'
                  }`}>
                    {selectedPresentation.slides.map((slide, index) => (
                      <div 
                        key={slide.id} 
                        className={`group relative ${
                          isDragging && draggedSlide === index ? 'opacity-50' : ''
                        } ${
                          isDragging && dragOverSlide === index ? 'ring-2 ring-primary ring-offset-2' : ''
                        }`}
                        draggable
                        onDragStart={(e) => handleDragStart(e, index)}
                        onDragOver={(e) => handleDragOver(e, index)}
                        onDrop={(e) => handleDrop(e, index)}
                        onDragEnd={handleDragEnd}
                      >
                        <Card 
                          className={`cursor-pointer transition-all duration-200 hover:border-primary/50 ${
                            currentSlide === index ? 'border-primary bg-primary/5' : 'hover:shadow-md'
                          } ${
                            viewMode === 'grid' ? (
                              gridSize === 'small' ? 'aspect-[4/3] p-2' :
                              gridSize === 'medium' ? 'aspect-[4/3] p-3' :
                              'aspect-[4/3] p-4'
                            ) : 'p-3'
                          }`}
                          onClick={() => handleSlideClick(index)}
                        >
                          {viewMode === 'list' ? (
                            <div className="flex items-center gap-3">
                              <div className="flex-shrink-0">
                                <div className="w-8 h-8 rounded bg-primary/10 flex items-center justify-center">
                                  <span className="text-xs font-medium text-primary">{index + 1}</span>
                                </div>
                              </div>
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center gap-2 mb-1">
                                  <Presentation className={`flex-shrink-0 ${
                                    gridSize === 'small' ? 'h-3 w-3' : 'h-4 w-4'
                                  }`} />
                                  <span className={`font-medium text-foreground ${
                                    gridSize === 'small' ? 'text-xs' : 'text-sm'
                                  }`}>
                                    {slide.title}
                                  </span>
                                </div>
                                <Badge variant="secondary" className={`${
                                  gridSize === 'small' ? 'text-[10px] px-1' : 'text-xs'
                                }`}>
                                  {index + 1}
                                </Badge>
                              </div>
                              <div className="flex-1 bg-gradient-to-br from-muted/20 to-muted/40 rounded-md border-2 border-dashed border-muted-foreground/20 flex items-center justify-center overflow-hidden">
                                <div className="text-center p-2">
                                  <p className={`text-muted-foreground line-clamp-3 ${
                                    gridSize === 'small' ? 'text-[10px]' :
                                    gridSize === 'medium' ? 'text-xs' :
                                    'text-sm'
                                  }`}>
                                    {slide.content.split('\n').find(line => line.startsWith('# '))?.replace('# ', '') || 'Slide content'}
                                  </p>
                                </div>
                              </div>
                            </div>
                          ) : (
                            <div className="h-full flex flex-col items-center justify-center">
                              <div className={`rounded-full bg-primary/10 flex items-center justify-center mb-1 ${
                                gridSize === 'small' ? 'w-6 h-6' :
                                gridSize === 'medium' ? 'w-8 h-8' :
                                'w-10 h-10'
                              }`}>
                                <span className={`font-bold text-primary ${
                                  gridSize === 'small' ? 'text-xs' :
                                  gridSize === 'medium' ? 'text-sm' :
                                  'text-base'
                                }`}>
                                  {index + 1}
                                </span>
                              </div>
                              {gridSize !== 'small' && (
                                <p className={`text-center text-muted-foreground line-clamp-2 ${
                                  gridSize === 'medium' ? 'text-[10px]' : 'text-xs'
                                }`}>
                                  {slide.title}
                                </p>
                              )}
                              <div className="absolute top-1 right-1 flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleEditSlide(index);
                                  }}
                                  className="h-6 w-6 p-0"
                                  title="Edit slide"
                                >
                                  <Edit className="h-3 w-3" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleDuplicateSlide(index);
                                  }}
                                  className="h-6 w-6 p-0"
                                  title="Duplicate slide"
                                >
                                  <Copy className="h-3 w-3" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleRemoveSlide(index);
                                  }}
                                  className="h-6 w-6 p-0"
                                  disabled={selectedPresentation.slides.length <= 1}
                                  title="Delete slide"
                                >
                                  <Trash2 className="h-3 w-3" />
                                </Button>
                              </div>
                              {viewMode === 'list' && (
                                <div className="absolute left-1 top-1/2 -translate-y-1/2 flex flex-col gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      if (index > 0) handleMoveSlide(index, index - 1);
                                    }}
                                    className="h-6 w-6 p-0"
                                    disabled={index === 0}
                                    title="Move up"
                                  >
                                    <ArrowUp className="h-3 w-3" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      if (index < selectedPresentation.slides.length - 1) {
                                        handleMoveSlide(index, index + 1);
                                      }
                                    }}
                                    className="h-6 w-6 p-0"
                                    disabled={index === selectedPresentation.slides.length - 1}
                                    title="Move down"
                                  >
                                    <ArrowDown className="h-3 w-3" />
                                  </Button>
                                </div>
                              )}
                            </div>
                          )}
                        </Card>
                      </div>
                    ))}
                  </div>
                </div>
              </ScrollArea>
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center p-8">
            <EmptyState
              icon={<Presentation />}
              title="No presentations yet"
              description="Create your first presentation to get started"
              actionLabel="Create Presentation"
              onAction={handleCreatePresentation}
            />
          </div>
        )}
      </div>
      
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Presentation</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this presentation? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => presentationToDelete && handleDeletePresentation(presentationToDelete)}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      
      {/* Playback Mode */}
      {isPlaybackMode && selectedPresentation && (
        <div className="fixed inset-0 bg-black z-50 flex flex-col">
          {/* Slide Content */}
          <div className="flex-1 flex items-center justify-center p-8 relative overflow-hidden">
            <div 
              className={`w-full h-full flex ${(
                selectedPresentation.slides[playbackSlide]?.layout === 'center' ||
                selectedPresentation.slides[playbackSlide]?.layout === 'cover'
              ) ? 'items-center justify-center text-center' : 'items-start'}`}
              style={{
                backgroundImage: selectedPresentation.slides[playbackSlide]?.background?.startsWith('http') 
                  ? `url(${selectedPresentation.slides[playbackSlide].background})` 
                  : undefined,
                backgroundColor: selectedPresentation.slides[playbackSlide]?.background?.startsWith('#') 
                  ? selectedPresentation.slides[playbackSlide].background 
                  : undefined,
                background: selectedPresentation.slides[playbackSlide]?.background?.includes('gradient') 
                  ? selectedPresentation.slides[playbackSlide].background 
                  : undefined,
                backgroundSize: 'cover',
                backgroundPosition: 'center'
              }}
            >
              <div className={`text-white max-w-6xl mx-auto ${selectedPresentation.slides[playbackSlide]?.class || ''}`}>
                <h1 className="text-6xl font-bold mb-8">
                  {selectedPresentation.slides[playbackSlide]?.title}
                </h1>
                <div className="text-2xl leading-relaxed space-y-4">
                  {selectedPresentation.slides[playbackSlide]?.content.split('\n').map((line, i) => {
                    if (line.startsWith('# ')) {
                      return <h1 key={i} className="text-5xl font-bold my-4">{line.substring(2)}</h1>;
                    } else if (line.startsWith('## ')) {
                      return <h2 key={i} className="text-4xl font-semibold my-3">{line.substring(3)}</h2>;
                    } else if (line.startsWith('### ')) {
                      return <h3 key={i} className="text-3xl font-medium my-2">{line.substring(4)}</h3>;
                    } else if (line.startsWith('- ')) {
                      return <li key={i} className="ml-8 my-2">{line.substring(2)}</li>;
                    } else if (line.startsWith('```')) {
                      return (
                        <pre key={i} className="bg-black/50 p-4 rounded-lg my-4">
                          <code className="text-green-400 font-mono">{line.substring(3)}</code>
                        </pre>
                      );
                    } else if (line.startsWith('> ')) {
                      return (
                        <blockquote key={i} className="border-l-4 border-white/50 pl-4 my-3 italic">
                          {line.substring(2)}
                        </blockquote>
                      );
                    } else if (line) {
                      return <p key={i} className="my-3">{line}</p>;
                    }
                    return null;
                  })}
                </div>
              </div>
            </div>
            
            {/* Navigation Hints */}
            <div className="absolute bottom-8 left-1/2 -translate-x-1/2 flex items-center gap-4 text-white/50">
              <span className="text-sm">←→ Navigate</span>
              <span className="text-sm">ESC Exit</span>
              <span className="text-sm">N Notes</span>
            </div>
          </div>
          
          {/* Controls Bar */}
          <div className="bg-black/90 border-t border-white/10 p-4">
            <div className="max-w-6xl mx-auto flex items-center justify-between">
              {/* Left: Slide Info */}
              <div className="flex items-center gap-4">
                <Badge variant="outline" className="bg-white/10 text-white border-white/20">
                  {playbackSlide + 1} / {selectedPresentation.slides.length}
                </Badge>
                <span className="text-white/70 text-sm">
                  {selectedPresentation.slides[playbackSlide]?.title}
                </span>
              </div>
              
              {/* Center: Navigation */}
              <div className="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={previousSlide}
                  disabled={playbackSlide === 0}
                  className="text-white hover:bg-white/10"
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                
                {/* Slide Dots */}
                <div className="flex items-center gap-1">
                  {selectedPresentation.slides.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setPlaybackSlide(index)}
                      className={`w-2 h-2 rounded-full transition-all ${
                        index === playbackSlide 
                          ? 'bg-white w-8' 
                          : 'bg-white/30 hover:bg-white/50'
                      }`}
                    />
                  ))}
                </div>
                
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={nextSlide}
                  disabled={playbackSlide === selectedPresentation.slides.length - 1}
                  className="text-white hover:bg-white/10"
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
              
              {/* Right: Actions */}
              <div className="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowPresenterNotes(!showPresenterNotes)}
                  className={`text-white hover:bg-white/10 ${
                    showPresenterNotes ? 'bg-white/10' : ''
                  }`}
                >
                  {showPresenterNotes ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  <span className="ml-1 text-xs">Notes</span>
                </Button>
                
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={exitPlaybackMode}
                  className="text-white hover:bg-white/10"
                >
                  <span className="text-xs">Exit</span>
                </Button>
              </div>
            </div>
            
            {/* Presenter Notes */}
            {showPresenterNotes && selectedPresentation.slides[playbackSlide]?.notes && (
              <div className="max-w-6xl mx-auto mt-4 p-3 bg-white/5 rounded-lg">
                <h5 className="text-xs font-medium text-white/50 mb-1">Speaker Notes</h5>
                <p className="text-sm text-white/80">
                  {selectedPresentation.slides[playbackSlide].notes}
                </p>
              </div>
            )}
          </div>
        </div>
      )}
      
      {/* Slide Edit Dialog */}
      {isEditingSlide && (
        <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <Card className="w-full max-w-3xl max-h-[90vh] overflow-hidden flex flex-col">
            <div className="p-4 border-b flex items-center justify-between">
              <h3 className="text-lg font-semibold">Edit Slide</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCancelSlideEdit}
                className="h-8 w-8 p-0"
              >
                ×
              </Button>
            </div>
            
            <div className="flex-1 overflow-hidden">
              <Tabs defaultValue="content" className="h-full flex flex-col">
                <TabsList className="mx-4 mt-4">
                  <TabsTrigger value="content">Content</TabsTrigger>
                  <TabsTrigger value="properties">Properties</TabsTrigger>
                  <TabsTrigger value="preview">Preview</TabsTrigger>
                </TabsList>
                
                <TabsContent value="content" className="flex-1 overflow-y-auto p-4 space-y-4">
                  <div>
                    <Label htmlFor="slide-title">Slide Title</Label>
                    <Input
                      id="slide-title"
                      value={slideTitle}
                      onChange={(e) => setSlideTitle(e.target.value)}
                      placeholder="Enter slide title"
                      className="mt-1"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="slide-content">Content (Markdown)</Label>
                    <Textarea
                      id="slide-content"
                      value={slideContent}
                      onChange={(e) => setSlideContent(e.target.value)}
                      className="min-h-[250px] font-mono text-sm mt-1"
                      placeholder="# Heading\n\n- Bullet point\n- Another point\n\n```code\nExample code\n```"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="slide-notes">Speaker Notes</Label>
                    <Textarea
                      id="slide-notes"
                      value={slideNotes}
                      onChange={(e) => setSlideNotes(e.target.value)}
                      className="min-h-[100px] mt-1"
                      placeholder="Add speaker notes (optional)..."
                    />
                  </div>
                </TabsContent>
                
                <TabsContent value="properties" className="flex-1 overflow-y-auto p-4 space-y-4">
                  <div>
                    <Label>Layout</Label>
                    <div className="grid grid-cols-2 gap-2 mt-2">
                      {slideLayouts.map(layout => (
                        <Button
                          key={layout.name}
                          variant={slideLayout === layout.name ? 'default' : 'outline'}
                          size="sm"
                          onClick={() => setSlideLayout(layout.name)}
                          className="justify-start"
                        >
                          {layout.icon}
                          <span className="ml-2 text-xs">{layout.label}</span>
                        </Button>
                      ))}
                    </div>
                  </div>
                  
                  <div>
                    <Label>Transition</Label>
                    <Select value={slideTransition} onValueChange={setSlideTransition}>
                      <SelectTrigger className="mt-2">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {slideTransitions.map(transition => (
                          <SelectItem key={transition.name} value={transition.name}>
                            <div>
                              <div className="font-medium">{transition.label}</div>
                              <div className="text-xs text-muted-foreground">{transition.description}</div>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <Label htmlFor="slide-background">Background (URL or Color)</Label>
                    <Input
                      id="slide-background"
                      value={slideBackground}
                      onChange={(e) => setSlideBackground(e.target.value)}
                      placeholder="https://... or #hex or gradient"
                      className="mt-1"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="slide-class">CSS Classes</Label>
                    <Input
                      id="slide-class"
                      value={slideClass}
                      onChange={(e) => setSlideClass(e.target.value)}
                      placeholder="text-center, text-left, etc."
                      className="mt-1"
                    />
                  </div>
                </TabsContent>
                
                <TabsContent value="preview" className="flex-1 overflow-y-auto p-4">
                  <div className="h-full flex flex-col">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="text-sm font-medium">Live Preview</h4>
                      <div className="flex items-center gap-2">
                        <Switch
                          id="show-notes"
                          checked={showPresenterNotes}
                          onCheckedChange={setShowPresenterNotes}
                        />
                        <Label htmlFor="show-notes" className="text-xs">Show Notes</Label>
                      </div>
                    </div>
                    
                    <div 
                      className={`flex-1 bg-gradient-to-br from-background to-muted rounded-lg border-2 border-dashed p-8 ${
                        slideLayout === 'center' ? 'flex items-center justify-center text-center' :
                        slideLayout === 'cover' ? 'flex flex-col items-center justify-center text-center' :
                        ''
                      }`}
                      style={{
                        backgroundImage: slideBackground?.startsWith('http') ? `url(${slideBackground})` : undefined,
                        backgroundColor: slideBackground?.startsWith('#') ? slideBackground : undefined,
                        background: slideBackground?.includes('gradient') ? slideBackground : undefined,
                        backgroundSize: 'cover',
                        backgroundPosition: 'center'
                      }}
                    >
                      <div className={`${slideClass} max-w-full`}>
                        <h1 className="text-3xl font-bold mb-4">{slideTitle || 'Untitled Slide'}</h1>
                        <div className="prose prose-lg dark:prose-invert max-w-none">
                          {slideContent.split('\n').map((line, i) => {
                            if (line.startsWith('# ')) {
                              return <h1 key={i} className="text-2xl font-bold my-2">{line.substring(2)}</h1>;
                            } else if (line.startsWith('## ')) {
                              return <h2 key={i} className="text-xl font-semibold my-2">{line.substring(3)}</h2>;
                            } else if (line.startsWith('- ')) {
                              return <li key={i} className="ml-4 my-1">{line.substring(2)}</li>;
                            } else if (line.startsWith('```')) {
                              return <pre key={i} className="bg-muted p-2 rounded my-2"><code>{line.substring(3)}</code></pre>;
                            } else if (line) {
                              return <p key={i} className="my-2">{line}</p>;
                            }
                            return null;
                          })}
                        </div>
                      </div>
                    </div>
                    
                    {showPresenterNotes && slideNotes && (
                      <div className="mt-4 p-3 bg-muted rounded-md">
                        <h5 className="text-xs font-medium mb-1">Speaker Notes</h5>
                        <p className="text-xs text-muted-foreground">{slideNotes}</p>
                      </div>
                    )}
                  </div>
                </TabsContent>
              </Tabs>
            </div>
            
            <div className="p-4 border-t flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={handleCancelSlideEdit}
              >
                Cancel
              </Button>
              <Button
                onClick={handleSaveSlideEdit}
              >
                Save Changes
              </Button>
            </div>
          </Card>
        </div>
      )}
      
      {/* Import Dialog */}
      <input
        type="file"
        ref={fileInputRef}
        accept=".md,.json"
        onChange={handleImportPresentation}
        className="hidden"
      />
    </div>
  );
};

export default SlidevTab;