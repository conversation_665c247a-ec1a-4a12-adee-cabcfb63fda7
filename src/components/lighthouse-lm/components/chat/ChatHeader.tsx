import React from 'react';
import { Button } from '@/components/ui/button';
import { RefreshCw } from 'lucide-react';

interface ChatHeaderProps {
  shouldShowRefreshButton: boolean;
  isDeletingChatHistory: boolean;
  isChatDisabled: boolean;
  onRefreshChat: () => void;
}

const ChatHeader: React.FC<ChatHeaderProps> = React.memo(({
  shouldShowRefreshButton,
  isDeletingChatHistory,
  isChatDisabled,
  onRefreshChat
}) => {
  return (
    <header className="p-4 border-b border-border flex-shrink-0">
      <div className="max-w-4xl mx-auto flex items-center justify-end">
        {shouldShowRefreshButton && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onRefreshChat}
            disabled={isDeletingChatHistory || isChatDisabled}
            className="flex items-center space-x-2"
            aria-label="Clear chat conversation"
          >
            <RefreshCw className={`h-4 w-4 ${isDeletingChatHistory ? 'animate-spin' : ''}`} aria-hidden="true" />
            <span>{isDeletingChatHistory ? 'Clearing...' : 'Clear Chat'}</span>
          </Button>
        )}
      </div>
    </header>
  );
});

ChatHeader.displayName = 'ChatHeader';

export default ChatHeader;