import { DiagramType } from '../../../../services/mermaidService';
import { Source } from '../../../../hooks/useSources';

/**
 * Shared types for chat components
 */

export interface DiagramSuggestion {
  type: DiagramType;
  title: string;
  description: string;
  confidence: number;
  relevantSources: Source[];
  suggestedReason: string;
}

export interface RefinementCommand {
  id: string;
  label: string;
  description: string;
  icon: React.ReactNode;
  action: string;
  parameters?: Record<string, any>;
}