/**
 * Shared utility functions for chat components
 */

/**
 * Get confidence color classes based on confidence value using theme variables
 */
export const getConfidenceColor = (confidence: number): string => {
  if (confidence >= 0.8) return 'bg-emerald-100 text-emerald-800 border-emerald-200 dark:bg-emerald-900/30 dark:text-emerald-200 dark:border-emerald-800/50';
  if (confidence >= 0.6) return 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/30 dark:text-blue-200 dark:border-blue-800/50';
  if (confidence >= 0.4) return 'bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/30 dark:text-yellow-200 dark:border-yellow-800/50';
  return 'bg-muted text-muted-foreground border-border';
};

/**
 * Get source count display text with proper pluralization
 */
export const getSourceCountText = (count: number): string => {
  return `Using ${count} source${count > 1 ? 's' : ''}`;
};

/**
 * Format confidence percentage
 */
export const formatConfidence = (confidence: number): string => {
  return `${Math.round(confidence * 100)}%`;
};

/**
 * Get loading spinner classes with optional size
 */
export const getLoadingSpinnerClasses = (size: 'sm' | 'md' | 'lg' = 'md'): string => {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8'
  };
  return `animate-spin ${sizeClasses[size]} border-2 border-primary border-t-transparent rounded-full`;
};