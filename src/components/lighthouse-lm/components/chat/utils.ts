/**
 * Shared utility functions for chat components
 */

/**
 * Get confidence color classes based on confidence value
 */
export const getConfidenceColor = (confidence: number): string => {
  if (confidence >= 0.8) return 'bg-green-100 text-green-800 border-green-200';
  if (confidence >= 0.6) return 'bg-blue-100 text-blue-800 border-blue-200';
  if (confidence >= 0.4) return 'bg-yellow-100 text-yellow-800 border-yellow-200';
  return 'bg-gray-100 text-gray-800 border-gray-200';
};

/**
 * Get source count display text with proper pluralization
 */
export const getSourceCountText = (count: number): string => {
  return `Using ${count} source${count > 1 ? 's' : ''}`;
};

/**
 * Format confidence percentage
 */
export const formatConfidence = (confidence: number): string => {
  return `${Math.round(confidence * 100)}%`;
};

/**
 * Get loading spinner classes with optional size
 */
export const getLoadingSpinnerClasses = (size: 'sm' | 'md' | 'lg' = 'md'): string => {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8'
  };
  return `animate-spin ${sizeClasses[size]} border-2 border-primary border-t-transparent rounded-full`;
};