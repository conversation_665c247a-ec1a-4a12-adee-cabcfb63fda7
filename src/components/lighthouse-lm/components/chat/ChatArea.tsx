import React, { useState, useEffect, useCallback, useRef } from 'react';
import { But<PERSON> } from '../../../ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../../ui/card';
import { Badge } from '../../../ui/badge';
import { ScrollArea } from '../../../ui/scroll-area';
import { Textarea } from '../../../ui/textarea';
import { Separator } from '../../../ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../../ui/tabs';
import { Avatar, AvatarFallback, AvatarImage } from '../../../ui/avatar';
import { Popover, PopoverContent, PopoverTrigger } from '../../../ui/popover';
import { Input } from '../../../ui/input';
import { Label } from '../../../ui/label';
import { RadioGroup, RadioGroupItem } from '../../../ui/radio-group';
import { Switch } from '../../../ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../../ui/select';
import { Slider } from '../../../ui/slider';
import { 
  Send,
  Paperclip,
  Mic,
  Image,
  FileText,
  Code,
  Link,
  Hash,
  AtSign,
  Sparkles,
  Brain,
  BookOpen,
  MessageSquare,
  Plus,
  X,
  ChevronDown,
  ChevronUp,
  RefreshCw,
  Copy,
  Share2,
  Download,
  Bookmark,
  MoreVertical,
  Search,
  Filter,
  Settings,
  Info,
  AlertCircle,
  CheckCircle,
  Clock,
  Zap,
  Database,
  GitBranch,
  Layers,
  PenTool,
  Wand2,
  Bot,
  User,
  Quote,
  Loader2,
} from 'lucide-react';
import { useNotebookContext } from '../../contexts/NotebookContext';
import { useChatMessages } from '../../hooks/useChatMessages';
import { useSources } from '../../hooks/useSources';
import { useToast } from '../../hooks/useToast';
import { Citation } from '../../types/message';
import { cn } from '../../lib/utils';

interface ChatAreaProps {
  notebookId?: string;
  notebook?: any;
  onCitationClick?: (citation: Citation) => void;
}

interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  sources?: string[];
  citations?: Citation[];
  metadata?: {
    model?: string;
    tokens?: number;
    processingTime?: number;
    confidence?: number;
  };
  status?: 'sending' | 'sent' | 'failed';
  edited?: boolean;
}

interface QuickAction {
  id: string;
  label: string;
  icon: React.ReactNode;
  action: () => void;
  description?: string;
}

const ChatArea: React.FC<ChatAreaProps> = ({
  notebookId,
  notebook,
  onCitationClick,
}) => {
  const { toast } = useToast();
  const {
    selectedSources,
    activeSource,
    chatContext,
    setChatContext,
    insertToChat,
    sendToStudio,
    requestAIAssistance,
    aiSuggestions,
    syncStatus,
    focusMode,
  } = useNotebookContext();
  
  const { messages, sendMessage, isSending } = useChatMessages(notebookId);
  const { sources } = useSources(notebookId);
  
  const [inputMessage, setInputMessage] = useState('');
  const [showSourceContext, setShowSourceContext] = useState(true);
  const [showAISuggestions, setShowAISuggestions] = useState(true);
  const [isRecording, setIsRecording] = useState(false);
  const [attachments, setAttachments] = useState<any[]>([]);
  const [selectedModel, setSelectedModel] = useState('gpt-4');
  const [temperature, setTemperature] = useState(0.7);
  const [maxTokens, setMaxTokens] = useState(2000);
  const [showAdvancedSettings, setShowAdvancedSettings] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterRole, setFilterRole] = useState<'all' | 'user' | 'assistant'>('all');
  const [showCitations, setShowCitations] = useState(true);
  const [autoScroll, setAutoScroll] = useState(true);
  
  const chatEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);
  
  // Quick actions for enhanced chat functionality
  const quickActions: QuickAction[] = [
    {
      id: 'summarize',
      label: 'Summarize',
      icon: <Sparkles className="w-4 h-4" />,
      action: () => handleQuickAction('summarize'),
      description: 'Summarize the conversation',
    },
    {
      id: 'explain',
      label: 'Explain',
      icon: <Info className="w-4 h-4" />,
      action: () => handleQuickAction('explain'),
      description: 'Explain in simpler terms',
    },
    {
      id: 'expand',
      label: 'Expand',
      icon: <ChevronDown className="w-4 h-4" />,
      action: () => handleQuickAction('expand'),
      description: 'Provide more details',
    },
    {
      id: 'critique',
      label: 'Critique',
      icon: <AlertCircle className="w-4 h-4" />,
      action: () => handleQuickAction('critique'),
      description: 'Provide critical analysis',
    },
    {
      id: 'brainstorm',
      label: 'Brainstorm',
      icon: <Brain className="w-4 h-4" />,
      action: () => handleQuickAction('brainstorm'),
      description: 'Generate related ideas',
    },
    {
      id: 'code',
      label: 'Code',
      icon: <Code className="w-4 h-4" />,
      action: () => handleQuickAction('code'),
      description: 'Generate code example',
    },
  ];
  
  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (autoScroll && chatEndRef.current) {
      chatEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages, autoScroll]);
  
  // Register insert function with context
  useEffect(() => {
    const insertFn = (content: string) => {
      setInputMessage(prev => prev + content);
      inputRef.current?.focus();
    };
    
    // This would need to be added to the context
    // registerChatInsert(insertFn);
  }, []);
  
  // Handle message sending with context
  const handleSendMessage = useCallback(async () => {
    if (!inputMessage.trim() && attachments.length === 0) return;
    
    let finalMessage = inputMessage;
    
    // Add source context if enabled
    if (showSourceContext && selectedSources.length > 0) {
      const sourceContext = selectedSources
        .map(s => `[Source: ${s.title}]`)
        .join(' ');
      finalMessage = `${finalMessage}\n\nContext: ${sourceContext}`;
    }
    
    // Add attachments
    if (attachments.length > 0) {
      const attachmentInfo = attachments
        .map(a => `[Attachment: ${a.name}]`)
        .join(' ');
      finalMessage = `${finalMessage}\n\n${attachmentInfo}`;
    }
    
    try {
      await sendMessage(finalMessage, {
        model: selectedModel,
        temperature,
        maxTokens,
        sources: selectedSources.map(s => s.id),
      });
      
      setInputMessage('');
      setAttachments([]);
      
      // Request AI follow-up suggestions
      if (showAISuggestions) {
        requestAIAssistance('suggest follow-up questions', { 
          lastMessage: finalMessage 
        });
      }
    } catch (error) {
      toast({
        title: 'Failed to send message',
        description: 'Please try again',
        variant: 'destructive',
      });
    }
  }, [inputMessage, attachments, selectedSources, showSourceContext, showAISuggestions, 
      selectedModel, temperature, maxTokens, sendMessage, requestAIAssistance, toast]);
  
  // Handle quick actions
  const handleQuickAction = useCallback((action: string) => {
    const lastMessage = messages[messages.length - 1]?.content || '';
    const prompt = `${action}: ${lastMessage}`;
    setInputMessage(prompt);
    handleSendMessage();
  }, [messages, handleSendMessage]);
  
  // Handle citation click with context
  const handleCitationClick = useCallback((citation: Citation) => {
    onCitationClick?.(citation);
    
    // Also send to studio if connected
    if (citation.excerpt) {
      sendToStudio(citation.excerpt, 'note');
    }
  }, [onCitationClick, sendToStudio]);
  
  // Handle attachment
  const handleAttachment = useCallback((file: File) => {
    setAttachments(prev => [...prev, {
      id: Date.now().toString(),
      name: file.name,
      type: file.type,
      size: file.size,
      file,
    }]);
    
    toast({
      title: 'File attached',
      description: file.name,
    });
  }, [toast]);
  
  // Handle voice input
  const handleVoiceInput = useCallback(() => {
    setIsRecording(!isRecording);
    
    if (!isRecording) {
      // Start recording
      toast({
        title: 'Recording started',
        description: 'Speak your message',
      });
    } else {
      // Stop recording
      toast({
        title: 'Recording stopped',
        description: 'Processing audio...',
      });
    }
  }, [isRecording, toast]);
  
  // Handle message actions
  const handleCopyMessage = useCallback((content: string) => {
    navigator.clipboard.writeText(content);
    toast({
      title: 'Copied to clipboard',
    });
  }, [toast]);
  
  const handleShareMessage = useCallback((message: ChatMessage) => {
    // Implement share functionality
    toast({
      title: 'Share',
      description: 'Sharing functionality coming soon',
    });
  }, [toast]);
  
  const handleRegenerateMessage = useCallback((messageId: string) => {
    // Implement regenerate functionality
    toast({
      title: 'Regenerating response',
      description: 'Please wait...',
    });
  }, [toast]);
  
  // Filter messages based on search and role
  const filteredMessages = messages?.filter(msg => {
    const matchesSearch = !searchQuery || 
      msg.content.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesRole = filterRole === 'all' || msg.role === filterRole;
    return matchesSearch && matchesRole;
  }) || [];
  
  return (
    <div className="flex flex-col h-full bg-background">
      {/* Header Bar */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-3">
          <MessageSquare className="w-5 h-5 text-primary" />
          <div>
            <h3 className="font-semibold">AI Chat</h3>
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <Badge variant="outline" className="text-xs">
                {selectedModel}
              </Badge>
              {syncStatus === 'syncing' && (
                <Badge variant="secondary" className="text-xs">
                  <RefreshCw className="w-3 h-3 mr-1 animate-spin" />
                  Syncing
                </Badge>
              )}
              {selectedSources.length > 0 && (
                <Badge variant="secondary" className="text-xs">
                  <Database className="w-3 h-3 mr-1" />
                  {selectedSources.length} sources
                </Badge>
              )}
            </div>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowSourceContext(!showSourceContext)}
            className={cn(showSourceContext && "bg-primary/10")}
          >
            <BookOpen className="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowAISuggestions(!showAISuggestions)}
            className={cn(showAISuggestions && "bg-primary/10")}
          >
            <Sparkles className="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowCitations(!showCitations)}
            className={cn(showCitations && "bg-primary/10")}
          >
            <Quote className="w-4 h-4" />
          </Button>
          <Separator orientation="vertical" className="h-6" />
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="ghost" size="sm">
                <Search className="w-4 h-4" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80">
              <div className="space-y-3">
                <Input
                  placeholder="Search messages..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full"
                />
                <div className="flex items-center gap-2">
                  <Label className="text-xs">Filter:</Label>
                  <RadioGroup
                    value={filterRole}
                    onValueChange={(value) => setFilterRole(value as any)}
                    className="flex items-center gap-2"
                  >
                    <div className="flex items-center gap-1">
                      <RadioGroupItem value="all" id="all" />
                      <Label htmlFor="all" className="text-xs">All</Label>
                    </div>
                    <div className="flex items-center gap-1">
                      <RadioGroupItem value="user" id="user" />
                      <Label htmlFor="user" className="text-xs">User</Label>
                    </div>
                    <div className="flex items-center gap-1">
                      <RadioGroupItem value="assistant" id="assistant" />
                      <Label htmlFor="assistant" className="text-xs">AI</Label>
                    </div>
                  </RadioGroup>
                </div>
              </div>
            </PopoverContent>
          </Popover>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowAdvancedSettings(!showAdvancedSettings)}
          >
            <Settings className="w-4 h-4" />
          </Button>
        </div>
      </div>
      
      {/* Source Context Bar */}
      {showSourceContext && selectedSources.length > 0 && (
        <div className="p-3 bg-muted/30 border-b">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Database className="w-4 h-4 text-muted-foreground" />
              <span className="text-sm font-medium">Active Sources:</span>
              <div className="flex items-center gap-1 flex-wrap">
                {selectedSources.map(source => (
                  <Badge key={source.id} variant="secondary" className="text-xs">
                    {source.title}
                    <X 
                      className="w-3 h-3 ml-1 cursor-pointer" 
                      onClick={() => {
                        // Remove source from context
                      }}
                    />
                  </Badge>
                ))}
              </div>
            </div>
            <Button variant="ghost" size="sm">
              <Plus className="w-3 h-3 mr-1" />
              Add Source
            </Button>
          </div>
        </div>
      )}
      
      {/* AI Suggestions Bar */}
      {showAISuggestions && aiSuggestions.length > 0 && (
        <div className="p-3 bg-primary/5 border-b">
          <div className="flex items-start gap-2">
            <Sparkles className="w-4 h-4 text-primary mt-0.5" />
            <div className="flex-1">
              <p className="text-sm font-medium mb-2">Suggested follow-ups:</p>
              <div className="flex flex-wrap gap-2">
                {aiSuggestions.slice(0, 3).map((suggestion, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    size="sm"
                    className="text-xs"
                    onClick={() => setInputMessage(suggestion)}
                  >
                    {suggestion}
                  </Button>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
      
      {/* Advanced Settings Panel */}
      {showAdvancedSettings && (
        <div className="p-4 border-b bg-muted/20">
          <div className="grid grid-cols-3 gap-4">
            <div>
              <Label className="text-xs">Model</Label>
              <Select value={selectedModel} onValueChange={setSelectedModel}>
                <SelectTrigger className="h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="gpt-4">GPT-4</SelectItem>
                  <SelectItem value="gpt-3.5-turbo">GPT-3.5 Turbo</SelectItem>
                  <SelectItem value="claude-3">Claude 3</SelectItem>
                  <SelectItem value="gemini-pro">Gemini Pro</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label className="text-xs">Temperature: {temperature}</Label>
              <Slider
                value={[temperature]}
                onValueChange={([value]) => setTemperature(value)}
                min={0}
                max={2}
                step={0.1}
                className="mt-1"
              />
            </div>
            <div>
              <Label className="text-xs">Max Tokens: {maxTokens}</Label>
              <Slider
                value={[maxTokens]}
                onValueChange={([value]) => setMaxTokens(value)}
                min={100}
                max={4000}
                step={100}
                className="mt-1"
              />
            </div>
          </div>
        </div>
      )}
      
      {/* Messages Area */}
      <ScrollArea className="flex-1 p-4">
        <div className="space-y-4">
          {filteredMessages.map((message) => (
            <div
              key={message.id}
              className={cn(
                "flex gap-3",
                message.role === 'user' ? "justify-end" : "justify-start"
              )}
            >
              {message.role === 'assistant' && (
                <Avatar className="w-8 h-8">
                  <AvatarFallback>
                    <Bot className="w-4 h-4" />
                  </AvatarFallback>
                </Avatar>
              )}
              
              <div className={cn(
                "max-w-[70%] space-y-2",
                message.role === 'user' ? "items-end" : "items-start"
              )}>
                <Card className={cn(
                  message.role === 'user' ? "bg-primary text-primary-foreground" : ""
                )}>
                  <CardContent className="p-3">
                    <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                    
                    {/* Citations */}
                    {showCitations && message.citations && message.citations.length > 0 && (
                      <div className="mt-3 pt-3 border-t space-y-1">
                        {message.citations.map((citation, index) => (
                          <button
                            key={index}
                            onClick={() => handleCitationClick(citation)}
                            className="flex items-center gap-2 text-xs hover:underline"
                          >
                            <FileText className="w-3 h-3" />
                            {citation.source_title}
                            {citation.page_number && ` (p. ${citation.page_number})`}
                          </button>
                        ))}
                      </div>
                    )}
                    
                    {/* Metadata */}
                    {message.metadata && (
                      <div className="mt-2 pt-2 border-t flex items-center gap-3 text-xs opacity-70">
                        {message.metadata.model && (
                          <span>{message.metadata.model}</span>
                        )}
                        {message.metadata.tokens && (
                          <span>{message.metadata.tokens} tokens</span>
                        )}
                        {message.metadata.processingTime && (
                          <span>{message.metadata.processingTime}ms</span>
                        )}
                      </div>
                    )}
                  </CardContent>
                </Card>
                
                {/* Message Actions */}
                <div className="flex items-center gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0"
                    onClick={() => handleCopyMessage(message.content)}
                  >
                    <Copy className="w-3 h-3" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0"
                    onClick={() => handleShareMessage(message)}
                  >
                    <Share2 className="w-3 h-3" />
                  </Button>
                  {message.role === 'assistant' && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0"
                      onClick={() => handleRegenerateMessage(message.id)}
                    >
                      <RefreshCw className="w-3 h-3" />
                    </Button>
                  )}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0"
                    onClick={() => sendToStudio(message.content, 'note')}
                  >
                    <PenTool className="w-3 h-3" />
                  </Button>
                </div>
              </div>
              
              {message.role === 'user' && (
                <Avatar className="w-8 h-8">
                  <AvatarFallback>
                    <User className="w-4 h-4" />
                  </AvatarFallback>
                </Avatar>
              )}
            </div>
          ))}
          
          {isSending && (
            <div className="flex gap-3">
              <Avatar className="w-8 h-8">
                <AvatarFallback>
                  <Bot className="w-4 h-4" />
                </AvatarFallback>
              </Avatar>
              <Card>
                <CardContent className="p-3">
                  <div className="flex items-center gap-2">
                    <Loader2 className="w-4 h-4 animate-spin" />
                    <span className="text-sm">Thinking...</span>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
          
          <div ref={chatEndRef} />
        </div>
      </ScrollArea>
      
      {/* Quick Actions Bar */}
      <div className="p-3 border-t bg-muted/20">
        <div className="flex items-center gap-2">
          <span className="text-xs text-muted-foreground mr-2">Quick:</span>
          {quickActions.map(action => (
            <Button
              key={action.id}
              variant="outline"
              size="sm"
              className="h-7 text-xs"
              onClick={action.action}
              title={action.description}
            >
              {action.icon}
              <span className="ml-1">{action.label}</span>
            </Button>
          ))}
        </div>
      </div>
      
      {/* Attachments Bar */}
      {attachments.length > 0 && (
        <div className="p-3 border-t bg-muted/10">
          <div className="flex items-center gap-2 flex-wrap">
            {attachments.map(attachment => (
              <Badge key={attachment.id} variant="secondary">
                <Paperclip className="w-3 h-3 mr-1" />
                {attachment.name}
                <X
                  className="w-3 h-3 ml-1 cursor-pointer"
                  onClick={() => setAttachments(prev => 
                    prev.filter(a => a.id !== attachment.id)
                  )}
                />
              </Badge>
            ))}
          </div>
        </div>
      )}
      
      {/* Input Area */}
      <div className="p-4 border-t">
        <div className="flex items-end gap-2">
          <div className="flex-1">
            <Textarea
              ref={inputRef}
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  handleSendMessage();
                }
              }}
              placeholder={
                selectedSources.length > 0
                  ? `Ask about ${selectedSources[0].title}...`
                  : "Type your message..."
              }
              className="min-h-[80px] resize-none"
              disabled={isSending}
            />
          </div>
          
          <div className="flex flex-col gap-2">
            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0"
                onClick={() => document.getElementById('file-upload')?.click()}
              >
                <Paperclip className="w-4 h-4" />
              </Button>
              <input
                id="file-upload"
                type="file"
                className="hidden"
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  if (file) handleAttachment(file);
                }}
              />
              <Button
                variant="ghost"
                size="sm"
                className={cn("h-8 w-8 p-0", isRecording && "text-red-500")}
                onClick={handleVoiceInput}
              >
                <Mic className="w-4 h-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0"
              >
                <Image className="w-4 h-4" />
              </Button>
            </div>
            
            <Button
              onClick={handleSendMessage}
              disabled={isSending || (!inputMessage.trim() && attachments.length === 0)}
              className="h-8"
            >
              {isSending ? (
                <RefreshCw className="w-4 h-4 animate-spin" />
              ) : (
                <Send className="w-4 h-4" />
              )}
            </Button>
          </div>
        </div>
        
        {/* Character count */}
        <div className="flex items-center justify-between mt-2">
          <div className="text-xs text-muted-foreground">
            {inputMessage.length} characters
          </div>
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <Switch
              checked={autoScroll}
              onCheckedChange={setAutoScroll}
              className="scale-75"
            />
            <span>Auto-scroll</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatArea;