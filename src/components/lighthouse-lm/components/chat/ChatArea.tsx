import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Send, Upload, FileText, Loader2, RefreshCw, Bot, User, BookOpen, Lightbulb, Workflow } from 'lucide-react';
import { Card } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
// Removed carousel import - using simple flex layout instead
import { useChatMessages } from '../../hooks/useChatMessages';
import { useSources } from '../../hooks/useSources';
import { useChatDiagramIntegration } from '../../hooks/useChatDiagramIntegration';
import MarkdownRenderer from '../chat/MarkdownRenderer';
import SaveToNoteButton from '../note/SaveToNoteButton';
import AddSourcesDialog from '../sourcemanager/AddSourcesDialog';
import DiagramSuggestionsPanel from '../chat/DiagramSuggestionsPanel';
import ChatDiagramInsertion from '../chat/ChatDiagramInsertion';
import DiagramRefinementCommands from '../chat/DiagramRefinementCommands';
import { Citation } from '../../types/message';
import { DiagramSuggestion } from '../../../../services/chatDiagramService';
import { SourceDiagram } from '../../../../services/sourceDiagramService';

interface ChatAreaProps {
  hasSource: boolean;
  notebookId?: string;
  notebook?: {
    id: string;
    title: string;
    description?: string;
    generation_status?: string;
    icon?: string;
    example_questions?: string[];
  } | null;
  onCitationClick?: (citation: Citation) => void;
}

const ChatArea = ({
  hasSource,
  notebookId,
  notebook,
  onCitationClick
}: ChatAreaProps) => {
  const [message, setMessage] = useState('');
  const [pendingUserMessage, setPendingUserMessage] = useState<string | null>(null);
  const [showAiLoading, setShowAiLoading] = useState(false);
  const [clickedQuestions, setClickedQuestions] = useState<Set<string>>(new Set());
  const [showAddSourcesDialog, setShowAddSourcesDialog] = useState(false);
  const [isInputFocused, setIsInputFocused] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const [messageAnimations, setMessageAnimations] = useState<Set<string>>(new Set());
  
  const isGenerating = notebook?.generation_status === 'generating';
  
  const {
    messages,
    sendMessage,
    isSending,
    deleteChatHistory,
    isDeletingChatHistory
  } = useChatMessages(notebookId);
  
  const {
    sources
  } = useSources(notebookId);
  
  const sourceCount = sources?.length || 0;

  // Helper to get message content
  const getMessageContent = (msg: any) => {
    return msg.message?.content || msg.content || '';
  };

  // Chat diagram integration
  const recentMessages = messages.slice(-5).map(msg => getMessageContent(msg));
  const chatDiagram = useChatDiagramIntegration({
    sources: sources || [],
    recentMessages,
    notebookId,
    autoGenerateSuggestions: true,
    maxSuggestions: 3
  });

  const [showDiagramRefinement, setShowDiagramRefinement] = useState<SourceDiagram | null>(null);

  // Check if at least one source has been successfully processed
  // Also check for sources with content (which should be considered complete)
  const hasProcessedSource = sources?.some(source => 
    source.processing_status === 'completed' || 
    source.content !== null && source.content !== undefined && source.content !== ''
  ) || false;

  // Chat should be disabled if there are no processed sources
  const isChatDisabled = !hasProcessedSource;

  // Track when we send a message to show loading state
  const [lastMessageCount, setLastMessageCount] = useState(0);

  // Ref for auto-scrolling to the most recent message
  const latestMessageRef = useRef<HTMLDivElement>(null);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  useEffect(() => {
    // If we have new messages and we have a pending message, clear it
    if (messages.length > lastMessageCount && pendingUserMessage) {
      setPendingUserMessage(null);
      setShowAiLoading(false);
    }
    setLastMessageCount(messages.length);
  }, [messages.length, lastMessageCount, pendingUserMessage]);

  // Auto-scroll when pending message is set, when messages update, or when AI loading appears
  useEffect(() => {
    if (latestMessageRef.current && scrollAreaRef.current) {
      // Find the viewport within the ScrollArea
      const viewport = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]');
      if (viewport) {
        // Use a small delay to ensure the DOM has updated
        setTimeout(() => {
          latestMessageRef.current?.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }, 50);
      }
    }
  }, [pendingUserMessage, messages.length, showAiLoading]);
  const handleSendMessage = async (messageText?: string) => {
    const textToSend = messageText || message.trim();
    if (textToSend && notebookId) {
      try {
        // Add send animation
        setIsTyping(false);
        
        // Store the pending message to display immediately
        setPendingUserMessage(textToSend);
        await sendMessage(textToSend);
        setMessage('');

        // Show AI loading after user message is sent with delay for smooth transition
        setTimeout(() => {
          setShowAiLoading(true);
        }, 300);
      } catch (error) {
        console.error('Failed to send message:', error);
        // Clear pending message on error
        setPendingUserMessage(null);
        setShowAiLoading(false);
      }
    }
  };
  const handleRefreshChat = () => {
    if (notebookId) {
      console.log('Refresh button clicked for notebook:', notebookId);
      deleteChatHistory(notebookId);
      // Reset clicked questions when chat is refreshed
      setClickedQuestions(new Set());
    }
  };
  const handleCitationClick = (citation: Citation) => {
    onCitationClick?.(citation);
  };
  const handleExampleQuestionClick = (question: string) => {
    // Add question to clicked set to remove it from display
    setClickedQuestions(prev => new Set(prev).add(question));
    setMessage(question);
    
    // Add a small delay for visual feedback
    setTimeout(() => {
      handleSendMessage(question);
    }, 150);
  };

  // Diagram integration handlers
  const handleDiagramSuggestionGenerate = async (suggestion: DiagramSuggestion) => {
    const diagram = await chatDiagram.generateDiagramFromSuggestion(suggestion);
    if (diagram) {
      // Optionally insert into chat or show in a modal
      console.log('Generated diagram:', diagram);
    }
  };

  const handleDiagramRefinement = async (command: string, parameters?: Record<string, any>) => {
    if (showDiagramRefinement) {
      const refinedDiagram = await chatDiagram.processRefinementCommand(
        showDiagramRefinement,
        command,
        parameters
      );
      if (refinedDiagram) {
        setShowDiagramRefinement(refinedDiagram);
      }
    }
  };

  const handleCustomRefinementCommand = async (command: string) => {
    if (showDiagramRefinement) {
      await handleDiagramRefinement(command);
    }
  };

  const handleDiagramViewFullscreen = (diagram: SourceDiagram) => {
    // Could open in a modal or navigate to a full diagram view
    console.log('View diagram fullscreen:', diagram);
  };

  const handleDiagramCopyCode = (code: string) => {
    console.log('Diagram code copied:', code);
  };

  const handleDiagramExport = (diagram: SourceDiagram, format: 'svg' | 'png') => {
    console.log('Export diagram:', diagram.id, format);
  };

  const toggleDiagramSuggestions = () => {
    chatDiagram.toggleSuggestions();
  };

  // Handle typing indicator
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsTyping(message.length > 0);
    }, 300);
    
    return () => clearTimeout(timer);
  }, [message]);

  // Add message animation when new messages arrive
  useEffect(() => {
    if (messages.length > 0) {
      const latestMessage = messages[messages.length - 1];
      if (latestMessage?.id && !messageAnimations.has(latestMessage.id)) {
        setMessageAnimations(prev => new Set(prev).add(latestMessage.id));
        
        // Remove animation class after animation completes
        setTimeout(() => {
          setMessageAnimations(prev => {
            const newSet = new Set(prev);
            newSet.delete(latestMessage.id);
            return newSet;
          });
        }, 500);
      }
    }
  }, [messages, messageAnimations]);

  // Helper function to determine if message is from user
  const isUserMessage = (msg: any) => {
    // Check both possible message structures
    const messageType = msg.message?.type || msg.message?.role || msg.role;
    return messageType === 'human' || messageType === 'user';
  };

  // Helper function to determine if message is from AI
  const isAiMessage = (msg: any) => {
    // Check both possible message structures
    const messageType = msg.message?.type || msg.message?.role || msg.role;
    return messageType === 'ai' || messageType === 'assistant';
  };

  // Get the index of the last message for auto-scrolling
  const shouldShowScrollTarget = () => {
    return messages.length > 0 || pendingUserMessage || showAiLoading;
  };

  // Show refresh button if there are any messages (including system messages)
  const shouldShowRefreshButton = messages.length > 0;

  // Get example questions from the notebook, filtering out clicked ones
  const exampleQuestions = notebook?.example_questions?.filter(q => !clickedQuestions.has(q)) || [];

  // Update placeholder text based on processing status
  const getPlaceholderText = () => {
    if (isChatDisabled) {
      if (sourceCount === 0) {
        return "Upload a source to get started...";
      } else {
        // Check if sources exist but none have content
        const sourcesWithoutContent = sources?.every(source => 
          !source.content || source.content === ''
        );
        if (sourcesWithoutContent) {
          return "Sources need content. Please re-add sources with text content...";
        }
        return "Please wait while your sources are being processed...";
      }
    }
    return "Start typing...";
  };
  return <div className="flex-1 flex flex-col h-full overflow-hidden" role="main" aria-label="Chat interface">
      {hasSource ? <div className="flex-1 flex flex-col h-full overflow-hidden">
          {/* Chat Header */}
          <header className="p-4 border-b border-border flex-shrink-0">
            <div className="max-w-4xl mx-auto flex items-center justify-end">
              {shouldShowRefreshButton && <Button variant="ghost" size="sm" onClick={handleRefreshChat} disabled={isDeletingChatHistory || isChatDisabled} className="flex items-center space-x-2" aria-label="Clear chat conversation">
                  <RefreshCw className={`h-4 w-4 ${isDeletingChatHistory ? 'animate-spin' : ''}`} aria-hidden="true" />
                  <span>{isDeletingChatHistory ? 'Clearing...' : 'Clear Chat'}</span>
                </Button>}
            </div>
          </header>

          <ScrollArea className="flex-1 h-full" ref={scrollAreaRef} role="log" aria-label="Chat messages" aria-live="polite">
            {/* Document Summary section removed */}
            <section className="p-8" aria-labelledby="notebook-title">
              <div className="max-w-4xl mx-auto">

                {/* Diagram Suggestions Panel */}
                {chatDiagram.showSuggestions && (
                  <div className="mb-6">
                    <DiagramSuggestionsPanel
                      sources={sources || []}
                      recentMessages={recentMessages}
                      onSuggestionGenerate={handleDiagramSuggestionGenerate}
                      onClose={chatDiagram.hideSuggestions}
                      isVisible={chatDiagram.showSuggestions}
                      isGenerating={chatDiagram.isGeneratingDiagram}
                    />
                  </div>
                )}

                {/* Generated Diagrams */}
                {chatDiagram.hasGeneratedDiagrams && (
                  <div className="mb-6 space-y-4">
                    {chatDiagram.generatedDiagrams.map((diagram) => (
                      <ChatDiagramInsertion
                        key={diagram.id}
                        diagram={diagram}
                        onViewFullscreen={handleDiagramViewFullscreen}
                        onCopyCode={handleDiagramCopyCode}
                        onExport={handleDiagramExport}
                        compact={false}
                      />
                    ))}
                  </div>
                )}

                {/* Chat Messages */}
                {(messages.length > 0 || pendingUserMessage || showAiLoading) && <div className="mb-6 space-y-6">
                    {messages.map((msg, index) => {
                  const isAnimating = messageAnimations.has(msg.id);
                  return (
                      <div 
                        key={msg.id} 
                        className={`flex items-start space-x-3 transition-all duration-500 ${
                          isUserMessage(msg) ? 'flex-row-reverse space-x-reverse' : ''
                        } ${
                          isAnimating ? 'animate-in slide-in-from-bottom-4 fade-in-0' : ''
                        }`}
                        role="article"
                        aria-label={`Message from ${isUserMessage(msg) ? 'you' : 'AI assistant'}`}
                      >
                        {/* Avatar */}
                        <Avatar className="w-8 h-8 flex-shrink-0" aria-hidden="true">
                          <AvatarFallback className={`${isUserMessage(msg) ? 'bg-primary/10 text-primary' : 'bg-muted text-muted-foreground'}`}>
                            {isUserMessage(msg) ? <User className="w-4 h-4" aria-hidden="true" /> : <Bot className="w-4 h-4" aria-hidden="true" />}
                          </AvatarFallback>
                        </Avatar>
                        
                        {/* Message content */}
                        <div className={`flex-1 ${isUserMessage(msg) ? 'max-w-2xl' : 'w-full'}`}>
                          <div className={`transform transition-all duration-300 hover:scale-[1.02] ${
                            isUserMessage(msg) 
                              ? 'bg-primary text-primary-foreground rounded-2xl rounded-tr-md px-4 py-3 shadow-sm' 
                              : 'bg-card border border-border rounded-2xl rounded-tl-md px-4 py-3 shadow-sm'
                          }`}>
                            <div className={isUserMessage(msg) ? 'text-primary-foreground' : 'prose max-w-none text-card-foreground'}>
                              <MarkdownRenderer 
                                content={getMessageContent(msg)} 
                                className={isUserMessage(msg) ? 'text-white' : ''} 
                                onCitationClick={handleCitationClick} 
                                isUserMessage={isUserMessage(msg)} 
                              />
                            </div>
                          </div>
                          {isAiMessage(msg) && (
                            <div className="mt-3 flex justify-start gap-2">
                              <SaveToNoteButton content={getMessageContent(msg)} notebookId={notebookId} />
                              
                              {/* Diagram Refinement Button for AI messages */}
                              {chatDiagram.hasGeneratedDiagrams && (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => setShowDiagramRefinement(chatDiagram.generatedDiagrams[0])}
                                  className="text-xs"
                                >
                                  <Workflow className="h-3 w-3 mr-1" />
                                  Refine Diagram
                                </Button>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    );
                  })}
                    
                    {/* Pending user message */}
                    {pendingUserMessage && (
                      <div className="flex items-start space-x-3 flex-row-reverse space-x-reverse animate-in slide-in-from-bottom-4 fade-in-0 duration-300" role="article" aria-label="Sending message">
                        <Avatar className="w-8 h-8 flex-shrink-0" aria-hidden="true">
                          <AvatarFallback className="bg-primary/10 text-primary">
                            <User className="w-4 w-4" aria-hidden="true" />
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1 max-w-2xl">
                          <div className="bg-primary text-primary-foreground rounded-2xl rounded-tr-md px-4 py-3 shadow-sm opacity-70 animate-pulse">
                            <MarkdownRenderer content={pendingUserMessage} className="text-primary-foreground" isUserMessage={true} />
                          </div>
                        </div>
                      </div>
                    )}
                    
                    {/* Enhanced AI Loading Indicator */}
                    {showAiLoading && (
                      <div className="flex items-start space-x-3 animate-in slide-in-from-bottom-4 fade-in-0 duration-500" ref={latestMessageRef} role="status" aria-label="AI is processing your message">
                        <Avatar className="w-8 h-8 flex-shrink-0" aria-hidden="true">
                          <AvatarFallback className="bg-gradient-to-br from-primary/10 to-primary/20 text-primary animate-pulse">
                            <Bot className="w-4 h-4 animate-pulse" aria-hidden="true" />
                          </AvatarFallback>
                        </Avatar>
                        <div className="bg-card border border-border rounded-2xl rounded-tl-md px-4 py-3 shadow-sm relative overflow-hidden">
                          {/* Shimmer effect */}
                          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-primary/5 to-transparent animate-pulse"></div>
                          
                          {/* Message skeleton */}
                          <div className="space-y-2 relative z-10">
                            <div className="flex items-center space-x-2">
                              <div className="flex space-x-1" aria-hidden="true">
                                <div className="w-2 h-2 bg-primary rounded-full animate-bounce"></div>
                                <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{
                                  animationDelay: '0.1s'
                                }}></div>
                                <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{
                                  animationDelay: '0.2s'
                                }}></div>
                              </div>
                              <span className="text-xs text-primary font-medium animate-pulse">Generating response...</span>
                            </div>
                            
                            {/* Skeleton lines */}
                            <div className="space-y-1.5 mt-3">
                              <div className="h-3 bg-gradient-to-r from-muted via-muted/50 to-muted rounded animate-pulse w-full"></div>
                              <div className="h-3 bg-gradient-to-r from-muted via-muted/50 to-muted rounded animate-pulse w-3/4" style={{ animationDelay: '0.1s' }}></div>
                              <div className="h-3 bg-gradient-to-r from-muted via-muted/50 to-muted rounded animate-pulse w-1/2" style={{ animationDelay: '0.2s' }}></div>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                    
                    {/* Scroll target for when no AI loading is shown */}
                    {!showAiLoading && shouldShowScrollTarget() && <div ref={latestMessageRef} />}
                  </div>}
              </div>
            </section>
          </ScrollArea>

          {/* Chat Input - Fixed at bottom */}
          <div className="pt-4 px-4 pb-8 border-t border-border flex-shrink-0">
            <div className="max-w-4xl mx-auto">
              {/* Example Questions */}
              {!isChatDisabled && !pendingUserMessage && !showAiLoading && exampleQuestions.length > 0 && (
                <div className="mb-4">
                  <p className="text-sm text-muted-foreground mb-3 font-medium">Suggested questions:</p>
                  <div className="flex gap-2 overflow-x-auto pb-2 scrollbar-thin" role="list" aria-label="Suggested questions">
                    {exampleQuestions.map((question, index) => (
                      <Button 
                        key={index}
                        variant="outline" 
                        size="sm" 
                        className="text-left whitespace-nowrap h-auto py-2 px-4 text-sm flex-shrink-0 bg-background hover:bg-accent hover:border-border hover:text-primary transition-all duration-200 rounded-full transform hover:scale-105 hover:shadow-md active:scale-95 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2" 
                        onClick={() => handleExampleQuestionClick(question)}
                        role="listitem"
                        aria-label={`Ask: ${question}`}
                      >
                        {question}
                      </Button>
                    ))}
                  </div>
                </div>
              )}
              
              {/* Enhanced Input Area */}
              <div className={`bg-background rounded-2xl border transition-all duration-300 p-2 ${
                isInputFocused 
                  ? 'border-primary/50 shadow-lg shadow-primary/10 ring-2 ring-primary/20' 
                  : 'border-border shadow-sm hover:shadow-md hover:border-border/80'
              }`}>
                <div className="flex items-end space-x-3">
                  <div className="flex-1 relative">
                    <Input 
                      placeholder={getPlaceholderText()} 
                      value={message} 
                      onChange={e => setMessage(e.target.value)} 
                      onFocus={() => setIsInputFocused(true)}
                      onBlur={() => setIsInputFocused(false)}
                      onKeyDown={e => e.key === 'Enter' && !isChatDisabled && !isSending && !pendingUserMessage && handleSendMessage()} 
                      className="border-0 bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 text-base py-3 px-4 resize-none transition-all duration-200" 
                      disabled={isChatDisabled || isSending || !!pendingUserMessage}
                      aria-label="Type your message"
                      aria-describedby={isChatDisabled ? "chat-status" : undefined}
                    />
                    {sourceCount > 0 && (
                      <div className="absolute right-16 top-1/2 transform -translate-y-1/2 flex items-center gap-2">
                        {/* Diagram Suggestions Toggle */}
                        {chatDiagram.shouldShowSuggestions && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={toggleDiagramSuggestions}
                            className="h-7 w-7 p-0 text-primary hover:bg-primary/10"
                            title="Show diagram suggestions"
                          >
                            <Lightbulb className="h-3 w-3" />
                          </Button>
                        )}
                        
                        <div className="flex items-center space-x-1 text-xs text-muted-foreground bg-muted px-2 py-1 rounded-full transition-all duration-300 hover:bg-accent hover:text-foreground hover:scale-105 group cursor-default">
                          <FileText className="h-3 w-3 transition-transform duration-300 group-hover:scale-110" />
                          <span className="transition-all duration-300 group-hover:font-medium">{sourceCount}</span>
                        </div>
                      </div>
                    )}
                  </div>
                  
                  {/* Diagram Quick Action */}
                  {!isChatDisabled && sourceCount > 0 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={toggleDiagramSuggestions}
                      disabled={chatDiagram.isLoading}
                      className="h-10 px-3 text-primary hover:bg-primary/10"
                      title="Generate diagram"
                    >
                      <Workflow className="h-4 w-4" />
                    </Button>
                  )}
                  
                  <Button 
                    onClick={() => handleSendMessage()} 
                    disabled={!message.trim() || isChatDisabled || isSending || !!pendingUserMessage}
                    className={`rounded-xl h-10 w-10 p-0 transition-all duration-300 transform focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 group ${
                      !message.trim() || isChatDisabled || isSending || !!pendingUserMessage
                        ? 'bg-muted cursor-not-allowed opacity-60'
                        : 'bg-primary hover:bg-primary/90 hover:scale-110 active:scale-95 shadow-md hover:shadow-xl hover:shadow-primary/25 hover:rotate-3 active:rotate-0'
                    }`}
                    size="sm"
                    aria-label="Send message"
                  >
                    {isSending || pendingUserMessage ? (
                      <Loader2 className="h-4 w-4 animate-spin transition-transform duration-300" aria-hidden="true" />
                    ) : (
                      <Send className={`h-4 w-4 transition-all duration-300 group-hover:scale-110 ${
                        message.trim() ? 'translate-x-0 rotate-0' : 'translate-x-1 -rotate-12'
                      }`} aria-hidden="true" />
                    )}
                  </Button>
                </div>
                
                {/* Status indicator */}
                {isChatDisabled && (
                  <div className="px-4 pb-2 animate-in fade-in-0 duration-300">
                    <div id="chat-status" className="text-xs text-amber-600 flex items-center space-x-1" role="status" aria-live="polite">
                      <Loader2 className="h-3 w-3 animate-spin" aria-hidden="true" />
                      <span>
                        {sourceCount === 0 
                          ? "Add sources to start chatting" 
                          : "Processing sources..."
                        }
                      </span>
                    </div>
                  </div>
                )}
                
                {/* Enhanced Typing indicator */}
                {isTyping && !isChatDisabled && (
                  <div className="px-4 pb-2 animate-in fade-in-0 duration-200">
                    <div className="bg-primary/5 rounded-lg px-3 py-2 border border-primary/20">
                      <div className="text-xs text-primary flex items-center space-x-2" role="status" aria-live="polite">
                        <div className="flex space-x-1" aria-hidden="true">
                          <div className="w-1.5 h-1.5 bg-primary rounded-full animate-bounce"></div>
                          <div className="w-1.5 h-1.5 bg-primary rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                          <div className="w-1.5 h-1.5 bg-primary rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                        </div>
                        <span className="font-medium">AI is thinking...</span>
                        {/* Pulse effect */}
                        <div className="w-2 h-2 bg-primary/60 rounded-full animate-ping opacity-75"></div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div> :
    // Empty State
    <div className="flex-1 flex flex-col items-center justify-center p-8 overflow-hidden" role="main" aria-label="Empty chat state">
          <div className="text-center mb-8">
            <div className="w-16 h-16 rounded-full mx-auto mb-4 flex items-center justify-center bg-muted" aria-hidden="true">
              <Upload className="h-8 w-8 text-slate-600" aria-hidden="true" />
            </div>
            <h2 className="text-xl font-medium text-foreground mb-4">Add a source to get started</h2>
            <Button 
              onClick={() => setShowAddSourcesDialog(true)} 
              className="transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-blue-500/25 active:scale-95 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 group"
              aria-label="Upload a source to start chatting"
            >
              <Upload className="h-4 w-4 mr-2 transition-transform duration-300 group-hover:scale-110 group-hover:-rotate-12" aria-hidden="true" />
              <span className="transition-all duration-300 group-hover:tracking-wide">Upload a source</span>
            </Button>
          </div>

          {/* Bottom Input */}
          <div className="w-full max-w-2xl">
            <div className="flex space-x-4">
              <Input placeholder="Upload a source to get started" disabled className="flex-1" aria-label="Message input (disabled)" />
              <div className="flex items-center text-sm text-muted-foreground transition-colors duration-300 hover:text-foreground" aria-label="Source count">
                <FileText className="h-4 w-4 mr-1 opacity-60" aria-hidden="true" />
                <span>0 sources</span>
              </div>
              <Button 
                disabled 
                className="opacity-50 cursor-not-allowed transition-opacity duration-300"
                aria-label="Send message (disabled)"
              >
                <Send className="h-4 w-4 opacity-60" aria-hidden="true" />
              </Button>
            </div>
          </div>
        </div>}
      
      {/* Footer */}
      <footer className="p-4 border-t border-border flex-shrink-0">
        <p className="text-center text-sm text-muted-foreground" role="note">LighthouseLM can be inaccurate; please double-check its responses.</p>
      </footer>
      
      {/* Add Sources Dialog */}
      <AddSourcesDialog open={showAddSourcesDialog} onOpenChange={setShowAddSourcesDialog} notebookId={notebookId} />
      
      {/* Diagram Refinement Panel */}
      {showDiagramRefinement && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-background rounded-lg shadow-xl max-w-md w-full max-h-[80vh] overflow-hidden">
            <div className="flex items-center justify-between p-4 border-b">
              <h3 className="font-semibold">Refine Diagram</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowDiagramRefinement(null)}
                className="h-8 w-8 p-0"
              >
                ×
              </Button>
            </div>
            <div className="p-4 overflow-y-auto">
              <DiagramRefinementCommands
                diagram={showDiagramRefinement}
                onRefinementCommand={handleDiagramRefinement}
                onCustomCommand={handleCustomRefinementCommand}
                isProcessing={chatDiagram.isProcessingRefinement}
              />
            </div>
          </div>
        </div>
      )}
    </div>;
};

export default ChatArea;
