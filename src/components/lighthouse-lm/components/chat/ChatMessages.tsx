import React, { useRef, useEffect } from 'react';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Button } from '@/components/ui/button';
import { Bot, User, Workflow } from 'lucide-react';
import MarkdownRenderer from './MarkdownRenderer';
import SaveToNoteButton from '../note/SaveToNoteButton';
import { Citation } from '../../types/message';
import { ChatMessagesProps } from '../../types/chat';

const ChatMessages: React.FC<ChatMessagesProps> = React.memo(({
  messages,
  pendingUserMessage,
  showAiLoading,
  messageAnimations,
  onCitationClick,
  onSaveToNote,
  notebookId,
  hasGeneratedDiagrams = false,
  onRefineDiagram
}) => {
  const latestMessageRef = useRef<HTMLDivElement>(null);
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to latest message
  useEffect(() => {
    if (latestMessageRef.current && scrollAreaRef.current) {
      const viewport = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]');
      if (viewport) {
        setTimeout(() => {
          latestMessageRef.current?.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }, 50);
      }
    }
  }, [pendingUserMessage, messages.length, showAiLoading]);

  const getMessageContent = (msg: any): string => {
    if (typeof msg.message?.content === 'string') {
      return msg.message.content;
    }
    if (typeof msg.content === 'string') {
      return msg.content;
    }
    if (msg.message?.content && typeof msg.message.content === 'object' && 'segments' in msg.message.content) {
      return msg.message.content.segments?.map((segment: any) => segment.text).join('') || '';
    }
    return '';
  };

  const isUserMessage = (msg: any): boolean => {
    const messageType = msg.message?.type || msg.message?.role || msg.role;
    return messageType === 'human' || messageType === 'user';
  };

  const isAiMessage = (msg: any): boolean => {
    const messageType = msg.message?.type || msg.message?.role || msg.role;
    return messageType === 'ai' || messageType === 'assistant';
  };

  const shouldShowScrollTarget = (): boolean => {
    return messages.length > 0 || !!pendingUserMessage || showAiLoading;
  };

  return (
    <ScrollArea
      className="flex-1 h-full"
      ref={scrollAreaRef}
      role="log"
      aria-label="Chat messages"
      aria-live="polite"
    >
      <section className="p-8" aria-labelledby="notebook-title">
        <div className="max-w-4xl mx-auto">
          {(messages.length > 0 || pendingUserMessage || showAiLoading) && (
            <div className="mb-6 space-y-6">
              {messages.map((msg, index) => {
                const isAnimating = messageAnimations.has(msg.id?.toString() || '');
                return (
                  <div
                    key={msg.id}
                    className={`flex items-start space-x-3 transition-all duration-500 ${
                      isUserMessage(msg) ? 'flex-row-reverse space-x-reverse' : ''
                    } ${
                      isAnimating ? 'animate-in slide-in-from-bottom-4 fade-in-0' : ''
                    }`}
                    role="article"
                    aria-label={`Message from ${isUserMessage(msg) ? 'you' : 'AI assistant'}`}
                  >
                    {/* Avatar */}
                    <Avatar className="w-8 h-8 flex-shrink-0" aria-hidden="true">
                      <AvatarFallback className={`${isUserMessage(msg) ? 'bg-primary/10 text-primary' : 'bg-muted text-muted-foreground'}`}>
                        {isUserMessage(msg) ? <User className="w-4 h-4" aria-hidden="true" /> : <Bot className="w-4 h-4" aria-hidden="true" />}
                      </AvatarFallback>
                    </Avatar>

                    {/* Message content */}
                    <div className={`flex-1 ${isUserMessage(msg) ? 'max-w-2xl' : 'w-full'}`}>
                      <div className={`transform transition-all duration-300 hover:scale-[1.02] ${
                        isUserMessage(msg)
                          ? 'bg-primary text-primary-foreground rounded-2xl rounded-tr-md px-4 py-3 shadow-sm'
                          : 'bg-card border border-border rounded-2xl rounded-tl-md px-4 py-3 shadow-sm'
                      }`}>
                        <div className={isUserMessage(msg) ? 'text-primary-foreground' : 'prose max-w-none text-card-foreground'}>
                          <MarkdownRenderer
                            content={getMessageContent(msg)}
                            className={isUserMessage(msg) ? 'text-white' : ''}
                            onCitationClick={onCitationClick}
                            isUserMessage={isUserMessage(msg)}
                          />
                        </div>
                      </div>
                      {isAiMessage(msg) && (
                        <div className="mt-3 flex justify-start gap-2">
                          <SaveToNoteButton content={getMessageContent(msg)} notebookId={notebookId} />

                          {/* Diagram Refinement Button for AI messages */}
                          {hasGeneratedDiagrams && onRefineDiagram && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={onRefineDiagram}
                              className="text-xs"
                            >
                              <Workflow className="h-3 w-3 mr-1" />
                              Refine Diagram
                            </Button>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}

              {/* Pending user message */}
              {pendingUserMessage && (
                <div className="flex items-start space-x-3 flex-row-reverse space-x-reverse animate-in slide-in-from-bottom-4 fade-in-0 duration-300" role="article" aria-label="Sending message">
                  <Avatar className="w-8 h-8 flex-shrink-0" aria-hidden="true">
                    <AvatarFallback className="bg-primary/10 text-primary">
                      <User className="w-4 w-4" aria-hidden="true" />
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 max-w-2xl">
                    <div className="bg-primary text-primary-foreground rounded-2xl rounded-tr-md px-4 py-3 shadow-sm opacity-70 animate-pulse">
                      <MarkdownRenderer content={pendingUserMessage} className="text-primary-foreground" isUserMessage={true} />
                    </div>
                  </div>
                </div>
              )}

              {/* Enhanced AI Loading Indicator */}
              {showAiLoading && (
                <div className="flex items-start space-x-3 animate-in slide-in-from-bottom-4 fade-in-0 duration-500" ref={latestMessageRef} role="status" aria-label="AI is processing your message">
                  <Avatar className="w-8 h-8 flex-shrink-0" aria-hidden="true">
                    <AvatarFallback className="bg-gradient-to-br from-primary/10 to-primary/20 text-primary animate-pulse">
                      <Bot className="w-4 h-4 animate-pulse" aria-hidden="true" />
                    </AvatarFallback>
                  </Avatar>
                  <div className="bg-card border border-border rounded-2xl rounded-tl-md px-4 py-3 shadow-sm relative overflow-hidden">
                    {/* Shimmer effect */}
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-primary/5 to-transparent animate-pulse"></div>

                    {/* Message skeleton */}
                    <div className="space-y-2 relative z-10">
                      <div className="flex items-center space-x-2">
                        <div className="flex space-x-1" aria-hidden="true">
                          <div className="w-2 h-2 bg-primary rounded-full animate-bounce"></div>
                          <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{
                            animationDelay: '0.1s'
                          }}></div>
                          <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{
                            animationDelay: '0.2s'
                          }}></div>
                        </div>
                        <span className="text-xs text-primary font-medium animate-pulse">Generating response...</span>
                      </div>

                      {/* Skeleton lines */}
                      <div className="space-y-1.5 mt-3">
                        <div className="h-3 bg-gradient-to-r from-muted via-muted/50 to-muted rounded animate-pulse w-full"></div>
                        <div className="h-3 bg-gradient-to-r from-muted via-muted/50 to-muted rounded animate-pulse w-3/4" style={{ animationDelay: '0.1s' }}></div>
                        <div className="h-3 bg-gradient-to-r from-muted via-muted/50 to-muted rounded animate-pulse w-1/2" style={{ animationDelay: '0.2s' }}></div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Scroll target for when no AI loading is shown */}
              {!showAiLoading && shouldShowScrollTarget() && <div ref={latestMessageRef} />}
            </div>
          )}
        </div>
      </section>
    </ScrollArea>
  );
});

ChatMessages.displayName = 'ChatMessages';

export default ChatMessages;