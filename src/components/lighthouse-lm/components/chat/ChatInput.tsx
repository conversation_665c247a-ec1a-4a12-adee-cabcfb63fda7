import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Send, Upload, FileText, Loader2, RefreshCw, Lightbulb, Workflow } from 'lucide-react';

interface ExampleQuestion {
  question: string;
}

interface ChatInputProps {
  message: string;
  onMessageChange: (message: string) => void;
  onSendMessage: (messageText?: string) => void;
  onRefreshChat?: () => void;
  onToggleDiagramSuggestions?: () => void;
  isChatDisabled: boolean;
  isSending: boolean;
  pendingUserMessage: string | null;
  showAiLoading: boolean;
  sourceCount: number;
  shouldShowSuggestions: boolean;
  isGenerating?: boolean;
  exampleQuestions: ExampleQuestion[];
  onExampleQuestionClick: (question: string) => void;
  getPlaceholderText: () => string;
}

const ChatInput: React.FC<ChatInputProps> = React.memo(({
  message,
  onMessageChange,
  onSendMessage,
  onRefreshChat,
  onToggleDiagramSuggestions,
  isChatDisabled,
  isSending,
  pendingUserMessage,
  showAiLoading,
  sourceCount,
  shouldShowSuggestions,
  isGenerating = false,
  exampleQuestions,
  onExampleQuestionClick,
  getPlaceholderText
}) => {
  const [isInputFocused, setIsInputFocused] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  // Handle typing indicator
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsTyping(message.length > 0);
    }, 300);

    return () => clearTimeout(timer);
  }, [message]);

  // Focus management
  const handleFocus = useCallback(() => {
    setIsInputFocused(true);
  }, []);

  const handleBlur = useCallback(() => {
    setIsInputFocused(false);
  }, []);

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !isChatDisabled && !isSending && !pendingUserMessage) {
      onSendMessage();
    }
  }, [isChatDisabled, isSending, pendingUserMessage, onSendMessage]);

  const shouldShowRefreshButton = exampleQuestions.length > 0 || message.length > 0;

  return (
    <div className="pt-4 px-4 pb-8 border-t border-border flex-shrink-0">
      <div className="max-w-4xl mx-auto">
        {/* Example Questions */}
        {!isChatDisabled && !pendingUserMessage && !showAiLoading && exampleQuestions.length > 0 && (
          <div className="mb-4">
            <p className="text-sm text-muted-foreground mb-3 font-medium">Suggested questions:</p>
            <div className="flex gap-2 overflow-x-auto pb-2 scrollbar-thin" role="list" aria-label="Suggested questions">
              {exampleQuestions.map((example, index) => (
                <Button
                  key={index}
                  variant="outline"
                  size="sm"
                  className="text-left whitespace-nowrap h-auto py-2 px-4 text-sm flex-shrink-0 bg-background hover:bg-accent hover:border-border hover:text-primary transition-all duration-200 rounded-full transform hover:scale-105 hover:shadow-md active:scale-95 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
                  onClick={() => onExampleQuestionClick(example.question)}
                  role="listitem"
                  aria-label={`Ask: ${example.question}`}
                >
                  {example.question}
                </Button>
              ))}
            </div>
          </div>
        )}

        {/* Enhanced Input Area */}
        <div className={`bg-background rounded-2xl border transition-all duration-300 p-2 ${
          isInputFocused
            ? 'border-primary/50 shadow-lg shadow-primary/10 ring-2 ring-primary/20'
            : 'border-border shadow-sm hover:shadow-md hover:border-border/80'
        }`}>
          <div className="flex items-end space-x-3">
            <div className="flex-1 relative">
              <Input
                ref={inputRef}
                placeholder={getPlaceholderText()}
                value={message}
                onChange={e => onMessageChange(e.target.value)}
                onFocus={handleFocus}
                onBlur={handleBlur}
                onKeyDown={handleKeyDown}
                className="border-0 bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 text-base py-3 px-4 resize-none transition-all duration-200"
                disabled={isChatDisabled || isSending || !!pendingUserMessage}
                aria-label="Type your message"
                aria-describedby={isChatDisabled ? "chat-status" : undefined}
              />
              {sourceCount > 0 && (
                <div className="absolute right-16 top-1/2 transform -translate-y-1/2 flex items-center gap-2">
                  {/* Diagram Suggestions Toggle */}
                  {shouldShowSuggestions && onToggleDiagramSuggestions && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={onToggleDiagramSuggestions}
                      className="h-7 w-7 p-0 text-primary hover:bg-primary/10"
                      title="Show diagram suggestions"
                      aria-label="Toggle diagram suggestions"
                    >
                      <Lightbulb className="h-3 w-3" />
                    </Button>
                  )}

                  <div className="flex items-center space-x-1 text-xs text-muted-foreground bg-muted px-2 py-1 rounded-full transition-all duration-300 hover:bg-accent hover:text-foreground hover:scale-105 group cursor-default">
                    <FileText className="h-3 w-3 transition-transform duration-300 group-hover:scale-110" />
                    <span className="transition-all duration-300 group-hover:font-medium">{sourceCount}</span>
                  </div>
                </div>
              )}
            </div>

            {/* Diagram Quick Action */}
            {!isChatDisabled && sourceCount > 0 && onToggleDiagramSuggestions && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onToggleDiagramSuggestions}
                disabled={isGenerating}
                className="h-10 px-3 text-primary hover:bg-primary/10"
                title="Generate diagram"
                aria-label="Generate diagram"
              >
                <Workflow className="h-4 w-4" />
              </Button>
            )}

            <Button
              onClick={() => onSendMessage()}
              disabled={!message.trim() || isChatDisabled || isSending || !!pendingUserMessage}
              className={`rounded-xl h-10 w-10 p-0 transition-all duration-300 transform focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 group ${
                !message.trim() || isChatDisabled || isSending || !!pendingUserMessage
                  ? 'bg-muted cursor-not-allowed opacity-60'
                  : 'bg-primary hover:bg-primary/90 hover:scale-110 active:scale-95 shadow-md hover:shadow-xl hover:shadow-primary/25 hover:rotate-3 active:rotate-0'
              }`}
              size="sm"
              aria-label="Send message"
            >
              {isSending || pendingUserMessage ? (
                <Loader2 className="h-4 w-4 animate-spin transition-transform duration-300" aria-hidden="true" />
              ) : (
                <Send className={`h-4 w-4 transition-all duration-300 group-hover:scale-110 ${
                  message.trim() ? 'translate-x-0 rotate-0' : 'translate-x-1 -rotate-12'
                }`} aria-hidden="true" />
              )}
            </Button>
          </div>

          {/* Status indicator */}
          {isChatDisabled && (
            <div className="px-4 pb-2 animate-in fade-in-0 duration-300">
              <div id="chat-status" className="text-xs text-amber-600 flex items-center space-x-1" role="status" aria-live="polite">
                <Loader2 className="h-3 w-3 animate-spin" aria-hidden="true" />
                <span>
                  {sourceCount === 0
                    ? "Add sources to start chatting"
                    : "Processing sources..."
                  }
                </span>
              </div>
            </div>
          )}

          {/* Enhanced Typing indicator */}
          {isTyping && !isChatDisabled && (
            <div className="px-4 pb-2 animate-in fade-in-0 duration-200">
              <div className="bg-primary/5 rounded-lg px-3 py-2 border border-primary/20">
                <div className="text-xs text-primary flex items-center space-x-2" role="status" aria-live="polite">
                  <div className="flex space-x-1" aria-hidden="true">
                    <div className="w-1.5 h-1.5 bg-primary rounded-full animate-bounce"></div>
                    <div className="w-1.5 h-1.5 bg-primary rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                    <div className="w-1.5 h-1.5 bg-primary rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                  </div>
                  <span className="font-medium">AI is thinking...</span>
                  {/* Pulse effect */}
                  <div className="w-2 h-2 bg-primary/60 rounded-full animate-ping opacity-75"></div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
});

ChatInput.displayName = 'ChatInput';

export default ChatInput;