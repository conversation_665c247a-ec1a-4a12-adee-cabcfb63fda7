import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { LucideIcon } from 'lucide-react';
import { getLoadingSpinnerClasses } from './utils';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  className = ''
}) => {
  return <div className={`${getLoadingSpinnerClasses(size)} ${className}`} />;
};

interface CardHeaderWithIconProps {
  icon: LucideIcon;
  title: string;
  description?: string;
  actionButtons?: React.ReactNode;
  className?: string;
}

export const CardHeaderWithIcon: React.FC<CardHeaderWithIconProps> = ({
  icon: Icon,
  title,
  description,
  actionButtons,
  className = ''
}) => {
  return (
    <CardHeader className={`pb-3 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div className="p-1.5 rounded-md bg-primary/10">
            <Icon className="h-4 w-4 text-primary" />
          </div>
          <div>
            <CardTitle className="text-sm font-medium">{title}</CardTitle>
            {description && (
              <p className="text-xs text-muted-foreground mt-0.5">
                {description}
              </p>
            )}
          </div>
        </div>
        {actionButtons && (
          <div className="flex items-center gap-1">
            {actionButtons}
          </div>
        )}
      </div>
    </CardHeader>
  );
};

interface ConfidenceBadgeProps {
  confidence: number;
  className?: string;
}

export const ConfidenceBadge: React.FC<ConfidenceBadgeProps> = ({
  confidence,
  className = ''
}) => {
  const getConfidenceColor = (confidence: number): string => {
    if (confidence >= 0.8) return 'bg-green-100 text-green-800 border-green-200';
    if (confidence >= 0.6) return 'bg-blue-100 text-blue-800 border-blue-200';
    if (confidence >= 0.4) return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    return 'bg-gray-100 text-gray-800 border-gray-200';
  };

  return (
    <Badge
      variant="outline"
      className={`text-xs px-2 py-0.5 ${getConfidenceColor(confidence)} ${className}`}
    >
      {Math.round(confidence * 100)}% match
    </Badge>
  );
};

interface GhostButtonProps {
  icon: LucideIcon;
  title: string;
  onClick?: () => void;
  disabled?: boolean;
  isLoading?: boolean;
  className?: string;
}

export const GhostButton: React.FC<GhostButtonProps> = ({
  icon: Icon,
  title,
  onClick,
  disabled = false,
  isLoading = false,
  className = ''
}) => {
  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={onClick}
      disabled={disabled}
      className={`h-7 w-7 p-0 ${className}`}
      title={title}
    >
      <Icon className={`h-3 w-3 ${isLoading ? 'animate-spin' : ''}`} />
    </Button>
  );
};