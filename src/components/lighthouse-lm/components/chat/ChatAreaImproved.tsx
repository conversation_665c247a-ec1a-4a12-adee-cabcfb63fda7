import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useChatMessages } from '../../hooks/useChatMessages';
import { useSources } from '../../hooks/useSources';
import { useChatDiagramIntegration } from '../../hooks/useChatDiagramIntegration';
import { useErrorHandler } from '../../utils/errorHandlingUtils';
import { Citation } from '../../types/message';
import { ChatAreaProps } from '../../types/chat';
import ErrorBoundary from '../shared/ErrorBoundary';
import ChatHeader from './ChatHeader';
import ChatMessages from './ChatMessages';
import ChatInput from './ChatInput';
import EmptyChatState from './EmptyChatState';
import DiagramRefinementCommands from './DiagramRefinementCommands';

// Lazy load heavy components for better performance
const DiagramSuggestionsPanel = React.lazy(() => import('./DiagramSuggestionsPanel'));
const ChatDiagramInsertion = React.lazy(() => import('./ChatDiagramInsertion'));

const ChatAreaImproved: React.FC<ChatAreaProps> = React.memo(({
  hasSource,
  notebookId,
  notebook,
  onCitationClick
}) => {
  // State management with better organization
  const [message, setMessage] = useState('');
  const [pendingUserMessage, setPendingUserMessage] = useState<string | null>(null);
  const [showAiLoading, setShowAiLoading] = useState(false);
  const [clickedQuestions, setClickedQuestions] = useState<Set<string>>(new Set());
  const [showDiagramRefinement, setShowDiagramRefinement] = useState<any>(null);

  // Error handling
  const { handleError } = useErrorHandler();

  // Data fetching with error handling
  const {
    messages,
    sendMessage,
    isSending,
    deleteChatHistory,
    isDeletingChatHistory
  } = useChatMessages(notebookId);

  const { sources } = useSources(notebookId);

  // Memoized calculations for performance
  const sourceCount = useMemo(() => sources?.length || 0, [sources]);

  const hasProcessedSource = useMemo(() =>
    sources?.some(source =>
      source.processing_status === 'completed' ||
      source.content !== null && source.content !== undefined && source.content !== ''
    ) || false,
    [sources]
  );

  const isChatDisabled = !hasProcessedSource;
  const shouldShowRefreshButton = messages.length > 0;

  // Chat diagram integration with error handling
  const recentMessages = useMemo(() =>
    messages.slice(-5).map(msg => {
      if (typeof msg.message?.content === 'string') {
        return msg.message.content;
      }
      if (typeof msg.content === 'string') {
        return msg.content;
      }
      return '';
    }),
    [messages]
  );

  const chatDiagram = useChatDiagramIntegration({
    sources: sources || [],
    recentMessages,
    notebookId,
    autoGenerateSuggestions: true,
    maxSuggestions: 3
  });

  // Auto-scroll and loading state management
  useEffect(() => {
    if (messages.length > 0 && pendingUserMessage) {
      setPendingUserMessage(null);
      setShowAiLoading(false);
    }
  }, [messages.length, pendingUserMessage]);

  // Event handlers with useCallback for performance
  const handleSendMessage = useCallback(async (messageText?: string) => {
    const textToSend = messageText || message.trim();
    if (textToSend && notebookId && !isChatDisabled) {
      try {
        setPendingUserMessage(textToSend);
        await sendMessage(textToSend);
        setMessage('');

        // Show AI loading with delay for smooth transition
        setTimeout(() => {
          setShowAiLoading(true);
        }, 300);
      } catch (error) {
        handleError(error as Error, {
          showNotification: true,
          fallbackMessage: 'Failed to send message'
        });
        setPendingUserMessage(null);
        setShowAiLoading(false);
      }
    }
  }, [message, notebookId, isChatDisabled, sendMessage, handleError]);

  const handleRefreshChat = useCallback(() => {
    if (notebookId) {
      deleteChatHistory(notebookId);
      setClickedQuestions(new Set());
    }
  }, [notebookId, deleteChatHistory]);

  const handleCitationClick = useCallback((citation: Citation) => {
    onCitationClick?.(citation);
  }, [onCitationClick]);

  const handleExampleQuestionClick = useCallback((question: string) => {
    setClickedQuestions(prev => new Set(prev).add(question));
    setMessage(question);
    setTimeout(() => handleSendMessage(question), 150);
  }, [handleSendMessage]);

  const handleDiagramSuggestionGenerate = useCallback(async (suggestion: any) => {
    try {
      const diagram = await chatDiagram.generateDiagramFromSuggestion(suggestion);
      if (diagram) {
        console.log('Generated diagram:', diagram);
      }
    } catch (error) {
      handleError(error as Error, {
        showNotification: true,
        fallbackMessage: 'Failed to generate diagram'
      });
    }
  }, [chatDiagram, handleError]);

  const handleDiagramRefinement = useCallback(async (command: string, parameters?: Record<string, any>) => {
    if (showDiagramRefinement) {
      try {
        const refinedDiagram = await chatDiagram.processRefinementCommand(
          showDiagramRefinement,
          command,
          parameters
        );
        if (refinedDiagram) {
          setShowDiagramRefinement(refinedDiagram);
        }
      } catch (error) {
        handleError(error as Error, {
          showNotification: true,
          fallbackMessage: 'Failed to refine diagram'
        });
      }
    }
  }, [showDiagramRefinement, chatDiagram, handleError]);

  const toggleDiagramSuggestions = useCallback(() => {
    chatDiagram.toggleSuggestions();
  }, [chatDiagram]);

  // Memoized values
  const exampleQuestions = useMemo(() =>
    notebook?.example_questions?.filter(q => !clickedQuestions.has(q)) || [],
    [notebook?.example_questions, clickedQuestions]
  );

  const getPlaceholderText = useCallback(() => {
    if (isChatDisabled) {
      if (sourceCount === 0) {
        return "Upload a source to get started...";
      } else {
        const sourcesWithoutContent = sources?.every(source =>
          !source.content || source.content === ''
        );
        if (sourcesWithoutContent) {
          return "Sources need content. Please re-add sources with text content...";
        }
        return "Please wait while your sources are being processed...";
      }
    }
    return "Start typing...";
  }, [isChatDisabled, sourceCount, sources]);

  const handleSaveToNote = useCallback((content: string, notebookId?: string) => {
    console.log('Saving to note:', content, notebookId);
  }, []);

  if (!hasSource) {
    return <EmptyChatState onAddSources={() => console.log('Add sources')} />;
  }

  return (
    <ErrorBoundary
      onError={(error, errorInfo) => {
        console.error('ChatArea error:', error, errorInfo);
      }}
    >
      <div className="flex-1 flex flex-col h-full overflow-hidden" role="main" aria-label="Chat interface">
        <ChatHeader
          shouldShowRefreshButton={shouldShowRefreshButton}
          isDeletingChatHistory={isDeletingChatHistory}
          isChatDisabled={isChatDisabled}
          onRefreshChat={handleRefreshChat}
        />

        <ChatMessages
          messages={messages}
          pendingUserMessage={pendingUserMessage}
          showAiLoading={showAiLoading}
          messageAnimations={new Set()}
          onCitationClick={handleCitationClick}
          onSaveToNote={handleSaveToNote}
          notebookId={notebookId}
          hasGeneratedDiagrams={chatDiagram.hasGeneratedDiagrams}
          onRefineDiagram={() => setShowDiagramRefinement(chatDiagram.generatedDiagrams[0])}
        />

        {/* Diagram Suggestions Panel */}
        {chatDiagram.showSuggestions && (
          <React.Suspense fallback={<div className="p-4">Loading diagram suggestions...</div>}>
            <div className="mb-6">
              <DiagramSuggestionsPanel
                sources={sources || []}
                recentMessages={recentMessages}
                onSuggestionGenerate={handleDiagramSuggestionGenerate}
                onClose={chatDiagram.hideSuggestions}
                isVisible={chatDiagram.showSuggestions}
                isGenerating={chatDiagram.isGeneratingDiagram}
              />
            </div>
          </React.Suspense>
        )}

        {/* Generated Diagrams */}
        {chatDiagram.hasGeneratedDiagrams && (
          <React.Suspense fallback={<div className="p-4">Loading diagrams...</div>}>
            <div className="mb-6 space-y-4">
              {chatDiagram.generatedDiagrams.map((diagram) => (
                <ChatDiagramInsertion
                  key={diagram.id}
                  diagram={diagram}
                  onViewFullscreen={(diagram) => console.log('View diagram fullscreen:', diagram)}
                  onCopyCode={(code) => console.log('Diagram code copied:', code)}
                  onExport={(diagram, format) => console.log('Export diagram:', diagram.id, format)}
                  compact={false}
                />
              ))}
            </div>
          </React.Suspense>
        )}

        <ChatInput
          message={message}
          onMessageChange={setMessage}
          onSendMessage={handleSendMessage}
          onRefreshChat={handleRefreshChat}
          onToggleDiagramSuggestions={toggleDiagramSuggestions}
          isChatDisabled={isChatDisabled}
          isSending={isSending}
          pendingUserMessage={pendingUserMessage}
          showAiLoading={showAiLoading}
          sourceCount={sourceCount}
          shouldShowSuggestions={chatDiagram.shouldShowSuggestions}
          isGenerating={chatDiagram.isGeneratingDiagram}
          exampleQuestions={exampleQuestions}
          onExampleQuestionClick={handleExampleQuestionClick}
          getPlaceholderText={getPlaceholderText}
        />

        {/* Footer */}
        <footer className="p-4 border-t border-border flex-shrink-0">
          <p className="text-center text-sm text-muted-foreground" role="note">
            LighthouseLM can be inaccurate; please double-check its responses.
          </p>
        </footer>

        {/* Diagram Refinement Panel */}
        {showDiagramRefinement && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
            <div className="bg-background rounded-lg shadow-xl max-w-md w-full max-h-[80vh] overflow-hidden">
              <div className="flex items-center justify-between p-4 border-b">
                <h3 className="font-semibold">Refine Diagram</h3>
                <button
                  onClick={() => setShowDiagramRefinement(null)}
                  className="h-8 w-8 p-0 hover:bg-accent rounded"
                  aria-label="Close diagram refinement"
                >
                  ×
                </button>
              </div>
              <div className="p-4 overflow-y-auto">
                <DiagramRefinementCommands
                  diagram={showDiagramRefinement}
                  onRefinementCommand={handleDiagramRefinement}
                  onCustomCommand={(command) => handleDiagramRefinement(command)}
                  isProcessing={chatDiagram.isProcessingRefinement}
                />
              </div>
            </div>
          </div>
        )}
      </div>
    </ErrorBoundary>
  );
});

ChatAreaImproved.displayName = 'ChatAreaImproved';

export default ChatAreaImproved;