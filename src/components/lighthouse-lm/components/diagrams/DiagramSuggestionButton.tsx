import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Workflow, Sparkles, ChevronRight } from 'lucide-react';
import { DiagramType } from '../../../../services/mermaidService';
import { LoadingSpinner, ConfidenceBadge } from './components';
import { DiagramSuggestion } from './types';
import { getSourceCountText } from './utils';

interface DiagramSuggestionButtonProps {
  suggestion: DiagramSuggestion;
  onGenerate: (suggestion: DiagramSuggestion) => void;
  isGenerating?: boolean;
  className?: string;
}

/**
 * Button component for displaying and triggering diagram suggestions in chat
 */
const DiagramSuggestionButton: React.FC<DiagramSuggestionButtonProps> = ({
  suggestion,
  onGenerate,
  isGenerating = false,
  className = ''
}) => {
  const [isHovered, setIsHovered] = useState(false);

  const handleClick = () => {
    if (!isGenerating) {
      onGenerate(suggestion);
    }
  };

  const getTypeIcon = (type: DiagramType) => {
    switch (type) {
      case 'flowchart':
        return <Workflow className="h-4 w-4" />;
      case 'mindmap':
        return <Sparkles className="h-4 w-4" />;
      default:
        return <Workflow className="h-4 w-4" />;
    }
  };


  return (
    <Button
      variant="outline"
      className={`
        relative p-4 h-auto flex flex-col items-start gap-3 
        border-2 border-dashed border-primary/20 
        hover:border-primary/40 hover:bg-primary/5
        transition-all duration-200 group
        ${isGenerating ? 'opacity-60 cursor-not-allowed' : 'cursor-pointer'}
        ${className}
      `}
      onClick={handleClick}
      disabled={isGenerating}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Header */}
      <div className="flex items-center justify-between w-full">
        <div className="flex items-center gap-2">
          <div className="p-1.5 rounded-md bg-primary/10 text-primary">
            {getTypeIcon(suggestion.type)}
          </div>
          <div className="flex flex-col items-start">
            <span className="font-medium text-sm text-foreground">
              {suggestion.title}
            </span>
            <span className="text-xs text-muted-foreground capitalize">
              {suggestion.type} diagram
            </span>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <ConfidenceBadge confidence={suggestion.confidence} />
          <ChevronRight
            className={`h-4 w-4 text-muted-foreground transition-transform duration-200 ${
              isHovered ? 'translate-x-1' : ''
            }`}
          />
        </div>
      </div>

      {/* Description */}
      <p className="text-xs text-muted-foreground text-left leading-relaxed">
        {suggestion.description}
      </p>

      {/* Suggested Reason */}
      <div className="flex items-start gap-2 w-full">
        <Sparkles className="h-3 w-3 text-primary mt-0.5 flex-shrink-0" />
        <span className="text-xs text-primary font-medium">
          {suggestion.suggestedReason}
        </span>
      </div>

      {/* Source Count */}
      {suggestion.relevantSources.length > 0 && (
        <div className="flex items-center gap-1 text-xs text-muted-foreground">
          <span>{getSourceCountText(suggestion.relevantSources.length)}</span>
        </div>
      )}

      {/* Loading overlay */}
      {isGenerating && (
        <div className="absolute inset-0 bg-background/80 rounded-md flex items-center justify-center">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <LoadingSpinner size="sm" />
            Generating...
          </div>
        </div>
      )}
    </Button>
  );
};

export default DiagramSuggestionButton;