import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  GitCompare, 
  Plus, 
  Minus, 
  Edit, 
  Eye, 
  RefreshCw,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import { SourceDiagramMonitor, DiagramDiff } from '../../../../services/sourceDiagramMonitor';
import { SourceDiagramService } from '../../../../services/sourceDiagramService';
import Mermaid from '../mermaid/Mermaid';

interface DiagramDiffViewerProps {
  diagramId: number;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onRegenerate?: (diagramId: number) => void;
}

interface DiffSummary {
  totalChanges: number;
  addedNodes: number;
  removedNodes: number;
  modifiedNodes: number;
  addedEdges: number;
  removedEdges: number;
  modifiedEdges: number;
  similarityScore: number;
}

export default function DiagramDiffViewer({
  diagramId,
  isOpen,
  onOpenChange,
  onRegenerate
}: DiagramDiffViewerProps) {
  const [diff, setDiff] = useState<DiagramDiff | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentContent, setCurrentContent] = useState<string>('');
  const [newContent, setNewContent] = useState<string>('');
  const [diffSummary, setDiffSummary] = useState<DiffSummary | null>(null);
  const [selectedTab, setSelectedTab] = useState('visual');

  // Load diagram diff when dialog opens
  useEffect(() => {
    if (isOpen && diagramId) {
      loadDiagramDiff();
    }
  }, [isOpen, diagramId]);

  const loadDiagramDiff = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Get current diagram
      const diagram = await SourceDiagramService.getSourceDiagram(diagramId);
      setCurrentContent(diagram.diagram_content);
      
      // Generate new content (this would typically be done by re-analyzing sources)
      // For now, we'll simulate this by getting a regenerated version
      const regenerationResult = await SourceDiagramMonitor.regenerateDiagram(diagramId, {
        force: false,
        preserve_layout: true
      });
      
      if (regenerationResult.success && regenerationResult.new_content) {
        setNewContent(regenerationResult.new_content);
        
        // Generate diff
        const diffResult = await SourceDiagramMonitor.generateDiagramDiff(
          diagramId,
          regenerationResult.new_content
        );
        
        setDiff(diffResult);
        setDiffSummary(calculateDiffSummary(diffResult));
      } else {
        setError('Failed to generate new diagram content for comparison');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load diagram diff');
    } finally {
      setIsLoading(false);
    }
  };

  const calculateDiffSummary = (diff: DiagramDiff): DiffSummary => {
    return {
      totalChanges: diff.changes.added_nodes.length + 
                   diff.changes.removed_nodes.length + 
                   diff.changes.modified_nodes.length +
                   diff.changes.added_edges.length + 
                   diff.changes.removed_edges.length + 
                   diff.changes.modified_edges.length,
      addedNodes: diff.changes.added_nodes.length,
      removedNodes: diff.changes.removed_nodes.length,
      modifiedNodes: diff.changes.modified_nodes.length,
      addedEdges: diff.changes.added_edges.length,
      removedEdges: diff.changes.removed_edges.length,
      modifiedEdges: diff.changes.modified_edges.length,
      similarityScore: diff.similarity_score
    };
  };

  const handleRegenerate = () => {
    if (onRegenerate) {
      onRegenerate(diagramId);
      onOpenChange(false);
    }
  };

  const renderChangesList = () => {
    if (!diff) return null;

    return (
      <div className="space-y-4">
        {/* Added Nodes */}
        {diff.changes.added_nodes.length > 0 && (
          <div>
            <h4 className="font-medium text-green-700 dark:text-green-400 flex items-center gap-2 mb-2">
              <Plus className="h-4 w-4" />
              Added Nodes ({diff.changes.added_nodes.length})
            </h4>
            <div className="space-y-1">
              {diff.changes.added_nodes.map((node, index) => (
                <Badge key={index} variant="outline" className="bg-green-50 border-green-200 text-green-800">
                  {node}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* Removed Nodes */}
        {diff.changes.removed_nodes.length > 0 && (
          <div>
            <h4 className="font-medium text-red-700 dark:text-red-400 flex items-center gap-2 mb-2">
              <Minus className="h-4 w-4" />
              Removed Nodes ({diff.changes.removed_nodes.length})
            </h4>
            <div className="space-y-1">
              {diff.changes.removed_nodes.map((node, index) => (
                <Badge key={index} variant="outline" className="bg-red-50 border-red-200 text-red-800">
                  {node}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* Modified Nodes */}
        {diff.changes.modified_nodes.length > 0 && (
          <div>
            <h4 className="font-medium text-blue-700 dark:text-blue-400 flex items-center gap-2 mb-2">
              <Edit className="h-4 w-4" />
              Modified Nodes ({diff.changes.modified_nodes.length})
            </h4>
            <div className="space-y-1">
              {diff.changes.modified_nodes.map((node, index) => (
                <Badge key={index} variant="outline" className="bg-blue-50 border-blue-200 text-blue-800">
                  {node}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* Edge Changes */}
        {(diff.changes.added_edges.length > 0 || diff.changes.removed_edges.length > 0 || diff.changes.modified_edges.length > 0) && (
          <div>
            <h4 className="font-medium text-gray-700 dark:text-gray-300 mb-2">
              Connection Changes
            </h4>
            <div className="grid grid-cols-3 gap-2 text-sm">
              {diff.changes.added_edges.length > 0 && (
                <div className="text-green-600">
                  +{diff.changes.added_edges.length} connections
                </div>
              )}
              {diff.changes.removed_edges.length > 0 && (
                <div className="text-red-600">
                  -{diff.changes.removed_edges.length} connections
                </div>
              )}
              {diff.changes.modified_edges.length > 0 && (
                <div className="text-blue-600">
                  ~{diff.changes.modified_edges.length} connections
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    );
  };

  const renderSimilarityScore = () => {
    if (!diffSummary) return null;

    const score = Math.round(diffSummary.similarityScore * 100);
    const isHighSimilarity = score >= 80;
    const isMediumSimilarity = score >= 50;

    return (
      <div className="flex items-center gap-2">
        {isHighSimilarity ? (
          <CheckCircle className="h-4 w-4 text-green-500" />
        ) : (
          <AlertCircle className="h-4 w-4 text-yellow-500" />
        )}
        <span className="text-sm">
          Similarity: {score}%
        </span>
        <Badge 
          variant={isHighSimilarity ? "default" : isMediumSimilarity ? "secondary" : "destructive"}
          className="text-xs"
        >
          {isHighSimilarity ? "Minor Changes" : isMediumSimilarity ? "Moderate Changes" : "Major Changes"}
        </Badge>
      </div>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <GitCompare className="h-5 w-5" />
            Diagram Changes Preview
          </DialogTitle>
          {diffSummary && (
            <div className="flex items-center justify-between">
              {renderSimilarityScore()}
              <div className="text-sm text-muted-foreground">
                {diffSummary.totalChanges} total changes detected
              </div>
            </div>
          )}
        </DialogHeader>

        {isLoading && (
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-6 w-6 animate-spin mr-2" />
            Analyzing changes...
          </div>
        )}

        {error && (
          <div className="flex items-center gap-2 p-4 bg-red-50 border border-red-200 rounded-lg">
            <AlertCircle className="h-5 w-5 text-red-500" />
            <span className="text-red-700">{error}</span>
          </div>
        )}

        {diff && !isLoading && (
          <Tabs value={selectedTab} onValueChange={setSelectedTab} className="flex-1">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="visual">Visual Comparison</TabsTrigger>
              <TabsTrigger value="changes">Changes List</TabsTrigger>
              <TabsTrigger value="code">Code Diff</TabsTrigger>
            </TabsList>

            <TabsContent value="visual" className="space-y-4">
              <div className="grid grid-cols-2 gap-4 h-96">
                <div>
                  <h4 className="font-medium mb-2 flex items-center gap-2">
                    <Eye className="h-4 w-4" />
                    Current Version
                  </h4>
                  <div className="border rounded-lg h-full overflow-hidden">
                    <ScrollArea className="h-full">
                      <Mermaid 
                        diagram={currentContent}
                        className="p-4"
                      />
                    </ScrollArea>
                  </div>
                </div>
                <div>
                  <h4 className="font-medium mb-2 flex items-center gap-2">
                    <RefreshCw className="h-4 w-4" />
                    Updated Version
                  </h4>
                  <div className="border rounded-lg h-full overflow-hidden">
                    <ScrollArea className="h-full">
                      <Mermaid 
                        diagram={newContent}
                        className="p-4"
                      />
                    </ScrollArea>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="changes">
              <ScrollArea className="h-96">
                <div className="p-4">
                  {diffSummary?.totalChanges === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      <CheckCircle className="h-8 w-8 mx-auto mb-2 text-green-500" />
                      No changes detected
                    </div>
                  ) : (
                    renderChangesList()
                  )}
                </div>
              </ScrollArea>
            </TabsContent>

            <TabsContent value="code">
              <div className="grid grid-cols-2 gap-4 h-96">
                <div>
                  <h4 className="font-medium mb-2">Current Code</h4>
                  <ScrollArea className="h-full border rounded-lg">
                    <pre className="p-4 text-sm font-mono">
                      {currentContent}
                    </pre>
                  </ScrollArea>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Updated Code</h4>
                  <ScrollArea className="h-full border rounded-lg">
                    <pre className="p-4 text-sm font-mono">
                      {newContent}
                    </pre>
                  </ScrollArea>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        )}

        <div className="flex justify-end gap-2 pt-4 border-t">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          {diff && diffSummary && diffSummary.totalChanges > 0 && (
            <Button onClick={handleRegenerate} className="flex items-center gap-2">
              <RefreshCw className="h-4 w-4" />
              Apply Changes
            </Button>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}