import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Wand2,
  Send,
  RefreshCw,
  Zap,
  Settings,
  Plus,
  Minus,
  Eye,
  EyeOff
} from 'lucide-react';
import { SourceDiagram } from '../../../../services/sourceDiagramService';
import { LoadingSpinner, CardHeaderWithIcon } from './components';
import { RefinementCommand } from './types';

interface DiagramRefinementCommandsProps {
  diagram: SourceDiagram;
  onRefinementCommand: (command: string, parameters?: Record<string, any>) => void;
  onCustomCommand: (command: string) => void;
  isProcessing?: boolean;
  className?: string;
}

/**
 * Component for chat-based diagram refinement commands
 */
const DiagramRefinementCommands: React.FC<DiagramRefinementCommandsProps> = ({
  diagram,
  onRefinementCommand,
  onCustomCommand,
  isProcessing = false,
  className = ''
}) => {
  const [customCommand, setCustomCommand] = useState('');
  const [showAdvanced, setShowAdvanced] = useState(false);

  // Predefined refinement commands
  const quickCommands: RefinementCommand[] = [
    {
      id: 'simplify',
      label: 'Simplify',
      description: 'Reduce complexity and show only main concepts',
      icon: <Minus className="h-3 w-3" />,
      action: 'simplify_diagram',
      parameters: { max_concepts: 8, minimum_confidence: 0.7 }
    },
    {
      id: 'expand',
      label: 'Add Detail',
      description: 'Include more concepts and relationships',
      icon: <Plus className="h-3 w-3" />,
      action: 'expand_diagram',
      parameters: { max_concepts: 25, minimum_confidence: 0.3 }
    },
    {
      id: 'focus_workflow',
      label: 'Focus on Process',
      description: 'Emphasize workflow and sequential steps',
      icon: <RefreshCw className="h-3 w-3" />,
      action: 'focus_workflow',
      parameters: { diagram_type: 'flowchart', show_decision_outcomes: true }
    },
    {
      id: 'show_relationships',
      label: 'Show Connections',
      description: 'Highlight relationships between concepts',
      icon: <Eye className="h-3 w-3" />,
      action: 'emphasize_relationships',
      parameters: { relationship_depth: 3, include_metadata: true }
    },
    {
      id: 'hide_metadata',
      label: 'Clean View',
      description: 'Hide technical details and metadata',
      icon: <EyeOff className="h-3 w-3" />,
      action: 'clean_diagram',
      parameters: { include_metadata: false, show_confidence: false }
    },
    {
      id: 'regenerate',
      label: 'Regenerate',
      description: 'Create a fresh version with current settings',
      icon: <RefreshCw className="h-3 w-3" />,
      action: 'regenerate_diagram',
      parameters: {}
    }
  ];

  const advancedCommands: RefinementCommand[] = [
    {
      id: 'change_type_mindmap',
      label: 'Convert to Mindmap',
      description: 'Change diagram type to mindmap',
      icon: <Zap className="h-3 w-3" />,
      action: 'change_diagram_type',
      parameters: { diagram_type: 'mindmap' }
    },
    {
      id: 'change_type_flowchart',
      label: 'Convert to Flowchart',
      description: 'Change diagram type to flowchart',
      icon: <Zap className="h-3 w-3" />,
      action: 'change_diagram_type',
      parameters: { diagram_type: 'flowchart' }
    },
    {
      id: 'adjust_confidence',
      label: 'Higher Confidence',
      description: 'Show only high-confidence concepts',
      icon: <Settings className="h-3 w-3" />,
      action: 'adjust_confidence',
      parameters: { minimum_confidence: 0.8 }
    },
    {
      id: 'include_all',
      label: 'Include All',
      description: 'Show all extracted concepts',
      icon: <Settings className="h-3 w-3" />,
      action: 'include_all_concepts',
      parameters: { minimum_confidence: 0.1, filter_by_relevance: false }
    }
  ];

  const handleQuickCommand = (command: RefinementCommand) => {
    if (!isProcessing) {
      onRefinementCommand(command.action, command.parameters);
    }
  };

  const handleCustomCommandSubmit = () => {
    if (customCommand.trim() && !isProcessing) {
      onCustomCommand(customCommand.trim());
      setCustomCommand('');
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleCustomCommandSubmit();
    }
  };

  const getCurrentSettings = () => {
    const options = diagram.analysis_metadata?.generation_parameters;
    if (!options) return null;

    return [
      { label: 'Type', value: diagram.diagram_type },
      { label: 'Max Concepts', value: options.max_concepts },
      { label: 'Min Confidence', value: `${Math.round(options.minimum_confidence * 100)}%` },
      { label: 'Metadata', value: options.include_metadata ? 'Shown' : 'Hidden' }
    ];
  };

  return (
    <Card className={`diagram-refinement-commands ${className}`}>
      <CardHeaderWithIcon
        icon={Wand2}
        title="Refine Diagram"
        description="Use commands to modify the diagram"
        actionButtons={
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="text-xs"
          >
            <Settings className="h-3 w-3 mr-1" />
            {showAdvanced ? 'Basic' : 'Advanced'}
          </Button>
        }
      />

      <CardContent className="pt-0 space-y-4">
        {/* Quick Commands */}
        <div>
          <h4 className="text-xs font-medium text-muted-foreground mb-2">Quick Actions</h4>
          <div className="grid grid-cols-2 gap-2">
            {quickCommands.map((command) => (
              <Button
                key={command.id}
                variant="outline"
                size="sm"
                onClick={() => handleQuickCommand(command)}
                disabled={isProcessing}
                className="h-auto p-2 flex flex-col items-start gap-1 text-left"
                title={command.description}
              >
                <div className="flex items-center gap-1.5 w-full">
                  {command.icon}
                  <span className="text-xs font-medium">{command.label}</span>
                </div>
                <span className="text-xs text-muted-foreground leading-tight">
                  {command.description}
                </span>
              </Button>
            ))}
          </div>
        </div>

        {/* Advanced Commands */}
        {showAdvanced && (
          <>
            <Separator />
            <div>
              <h4 className="text-xs font-medium text-muted-foreground mb-2">Advanced Options</h4>
              <div className="grid grid-cols-1 gap-2">
                {advancedCommands.map((command) => (
                  <Button
                    key={command.id}
                    variant="outline"
                    size="sm"
                    onClick={() => handleQuickCommand(command)}
                    disabled={isProcessing}
                    className="h-auto p-2 flex items-center justify-start gap-2 text-left"
                    title={command.description}
                  >
                    {command.icon}
                    <div className="flex flex-col items-start">
                      <span className="text-xs font-medium">{command.label}</span>
                      <span className="text-xs text-muted-foreground">
                        {command.description}
                      </span>
                    </div>
                  </Button>
                ))}
              </div>
            </div>
          </>
        )}

        {/* Custom Command Input */}
        <Separator />
        <div>
          <h4 className="text-xs font-medium text-muted-foreground mb-2">Custom Command</h4>
          <div className="flex gap-2">
            <Input
              placeholder="e.g., 'make it simpler' or 'add more detail'"
              value={customCommand}
              onChange={(e) => setCustomCommand(e.target.value)}
              onKeyPress={handleKeyPress}
              disabled={isProcessing}
              className="text-sm"
            />
            <Button
              size="sm"
              onClick={handleCustomCommandSubmit}
              disabled={!customCommand.trim() || isProcessing}
              className="px-3"
            >
              <Send className="h-3 w-3" />
            </Button>
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            Describe how you'd like to modify the diagram
          </p>
        </div>

        {/* Current Settings */}
        {getCurrentSettings() && (
          <>
            <Separator />
            <div>
              <h4 className="text-xs font-medium text-muted-foreground mb-2">Current Settings</h4>
              <div className="flex flex-wrap gap-1">
                {getCurrentSettings()!.map((setting, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {setting.label}: {setting.value}
                  </Badge>
                ))}
              </div>
            </div>
          </>
        )}

        {/* Processing Indicator */}
        {isProcessing && (
          <div className="flex items-center justify-center py-2">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <LoadingSpinner size="sm" />
              Processing refinement...
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default DiagramRefinementCommands;