import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { AlertTriangle, RefreshCw } from 'lucide-react';
import { useDiagramUpdates } from '../../hooks/useDiagramUpdates';

interface DiagramUpdateBadgeProps {
  notebookId: string;
  onUpdateClick?: () => void;
  className?: string;
}

export default function DiagramUpdateBadge({
  notebookId,
  onUpdateClick,
  className
}: DiagramUpdateBadgeProps) {
  const { 
    getOutdatedCount, 
    batchRegenerate, 
    outdatedDiagrams,
    isRegenerating 
  } = useDiagramUpdates({ 
    notebookId, 
    autoMonitoring: true 
  });

  const outdatedCount = getOutdatedCount();

  const handleUpdateAll = async () => {
    if (outdatedDiagrams.length === 0) return;
    
    await batchRegenerate(outdatedDiagrams, {
      preserve_layout: true,
      update_metadata: true,
      max_concurrent: 3
    });
    
    if (onUpdateClick) {
      onUpdateClick();
    }
  };

  if (outdatedCount === 0) {
    return null;
  }

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <Badge 
        variant="destructive" 
        className="flex items-center gap-1 text-xs"
      >
        <AlertTriangle className="h-3 w-3" />
        {outdatedCount} outdated diagram{outdatedCount > 1 ? 's' : ''}
      </Badge>
      <Button
        size="sm"
        variant="outline"
        onClick={handleUpdateAll}
        disabled={isRegenerating}
        className="text-xs h-6"
      >
        <RefreshCw className={`h-3 w-3 mr-1 ${isRegenerating ? 'animate-spin' : ''}`} />
        Update All
      </Button>
    </div>
  );
}