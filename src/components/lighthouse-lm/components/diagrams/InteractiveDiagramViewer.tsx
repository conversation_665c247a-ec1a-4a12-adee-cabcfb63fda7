import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { 
  ChevronLeft, 
  ChevronRight, 
  Home, 
  ExternalLink, 
  Eye, 
  EyeOff,
  Info,
  MapPin
} from 'lucide-react';
import Mermaid from '../mermaid/Mermaid';
import { SourceAttribution } from '../../types/sourceAnalysis';

interface InteractiveDiagramViewerProps {
  diagram: string;
  title?: string;
  sourceMap: Record<string, string[]>;
  sourceAttribution: SourceAttribution[];
  onSourceView: (sourceId: string) => void;
  onNodeClick?: (nodeId: string, sourceIds: string[]) => void;
  className?: string;
  height?: string;
}

interface NavigationState {
  currentNode: string | null;
  history: string[];
  historyIndex: number;
}

/**
 * Interactive diagram viewer with source navigation and exploration features
 * 
 * Features:
 * - Clickable diagram nodes with source attribution
 * - Navigation history with breadcrumbs
 * - Collapsible source information panel
 * - Back/forward navigation controls
 * - Source content preview and quick actions
 */
const InteractiveDiagramViewer: React.FC<InteractiveDiagramViewerProps> = ({
  diagram,
  title,
  sourceMap,
  sourceAttribution,
  onSourceView,
  onNodeClick,
  className = '',
  height = '600px'
}) => {
  const [navigation, setNavigation] = useState<NavigationState>({
    currentNode: null,
    history: [],
    historyIndex: -1
  });
  const [selectedNode, setSelectedNode] = useState<string | null>(null);
  const [showSourcePanel, setShowSourcePanel] = useState(true);
  const [highlightedNodes, setHighlightedNodes] = useState<string[]>([]);

  // Keyboard shortcuts for navigation
  React.useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.altKey) {
        switch (event.key) {
          case 'ArrowLeft':
            event.preventDefault();
            if (canGoBack) goBack();
            break;
          case 'ArrowRight':
            event.preventDefault();
            if (canGoForward) goForward();
            break;
          case 'Home':
            event.preventDefault();
            goHome();
            break;
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [canGoBack, canGoForward]);

  // Handle node selection and navigation
  const handleNodeClick = (nodeId: string, sourceIds: string[]) => {
    setSelectedNode(nodeId);
    
    // Update navigation history
    const newHistory = navigation.history.slice(0, navigation.historyIndex + 1);
    newHistory.push(nodeId);
    
    setNavigation({
      currentNode: nodeId,
      history: newHistory,
      historyIndex: newHistory.length - 1
    });

    // Highlight related nodes
    setHighlightedNodes([nodeId]);

    // Call parent handler
    if (onNodeClick) {
      onNodeClick(nodeId, sourceIds);
    }
  };

  // Navigation controls
  const canGoBack = navigation.historyIndex > 0;
  const canGoForward = navigation.historyIndex < navigation.history.length - 1;

  const goBack = () => {
    if (canGoBack) {
      const newIndex = navigation.historyIndex - 1;
      const nodeId = navigation.history[newIndex];
      setNavigation(prev => ({
        ...prev,
        currentNode: nodeId,
        historyIndex: newIndex
      }));
      setSelectedNode(nodeId);
      setHighlightedNodes([nodeId]);
    }
  };

  const goForward = () => {
    if (canGoForward) {
      const newIndex = navigation.historyIndex + 1;
      const nodeId = navigation.history[newIndex];
      setNavigation(prev => ({
        ...prev,
        currentNode: nodeId,
        historyIndex: newIndex
      }));
      setSelectedNode(nodeId);
      setHighlightedNodes([nodeId]);
    }
  };

  const goHome = () => {
    setNavigation({
      currentNode: null,
      history: [],
      historyIndex: -1
    });
    setSelectedNode(null);
    setHighlightedNodes([]);
  };

  // Get attribution for selected node
  const selectedNodeAttribution = selectedNode 
    ? sourceAttribution.filter(attr => attr.node_id === selectedNode)
    : [];

  const selectedNodeSources = selectedNode ? sourceMap[selectedNode] || [] : [];

  return (
    <div className={`interactive-diagram-viewer ${className}`}>
      <Card className="h-full flex flex-col">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <CardTitle className="text-lg">
                {title || 'Interactive Diagram'}
              </CardTitle>
              {selectedNode && (
                <Badge variant="secondary" className="text-xs">
                  Exploring: {selectedNode}
                </Badge>
              )}
            </div>
            
            {/* Navigation Controls */}
            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={goBack}
                disabled={!canGoBack}
                title="Go Back (Alt + ←)"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={goHome}
                disabled={!selectedNode}
                title="Reset View (Alt + Home)"
              >
                <Home className="h-4 w-4" />
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={goForward}
                disabled={!canGoForward}
                title="Go Forward (Alt + →)"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
              
              <Separator orientation="vertical" className="h-6 mx-1" />
              
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowSourcePanel(!showSourcePanel)}
                title={showSourcePanel ? 'Hide Source Panel' : 'Show Source Panel'}
              >
                {showSourcePanel ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </Button>
            </div>
          </div>
          
          {/* Breadcrumb Navigation */}
          {navigation.history.length > 0 ? (
            <div className="flex items-center gap-1 text-sm text-muted-foreground">
              <MapPin className="h-3 w-3" />
              <span>Path:</span>
              {navigation.history.map((nodeId, index) => (
                <React.Fragment key={nodeId}>
                  {index > 0 && <span className="mx-1">→</span>}
                  <button
                    className={`hover:text-foreground transition-colors ${
                      index === navigation.historyIndex ? 'text-foreground font-medium' : ''
                    }`}
                    onClick={() => {
                      setNavigation(prev => ({
                        ...prev,
                        currentNode: nodeId,
                        historyIndex: index
                      }));
                      setSelectedNode(nodeId);
                      setHighlightedNodes([nodeId]);
                    }}
                  >
                    {nodeId}
                  </button>
                </React.Fragment>
              ))}
            </div>
          ) : (
            <div className="flex items-center gap-1 text-sm text-muted-foreground">
              <MapPin className="h-3 w-3" />
              <span>
                {Object.keys(sourceMap).length} interactive nodes available
              </span>
            </div>
          )}
        </CardHeader>
        
        <CardContent className="flex-1 p-0 flex">
          {/* Main Diagram Area */}
          <div className={`flex-1 ${showSourcePanel ? 'border-r' : ''}`}>
            <Mermaid
              diagram={diagram}
              title=""
              mode="view"
              height={height}
              showControls={true}
              sourceMap={sourceMap}
              sourceAttribution={sourceAttribution}
              onNodeClick={handleNodeClick}
              onSourceView={onSourceView}
              highlightedNodes={highlightedNodes}
              showSourceAttribution={false} // We handle this in the side panel
            />
          </div>
          
          {/* Source Content Panel */}
          {showSourcePanel && (
            <div className="w-80 flex flex-col">
              <div className="p-4 border-b">
                <div className="flex items-center gap-2 mb-2">
                  <Info className="h-4 w-4 text-blue-500" />
                  <span className="font-medium">Source Information</span>
                </div>
                
                {selectedNode ? (
                  <div className="space-y-3">
                    <div>
                      <div className="text-sm font-medium text-gray-700 mb-1">
                        Selected Node: {selectedNode}
                      </div>
                      <div className="text-xs text-gray-500">
                        {selectedNodeSources.length} source{selectedNodeSources.length !== 1 ? 's' : ''} linked
                      </div>
                    </div>
                    
                    {selectedNodeAttribution.length > 0 && (
                      <div>
                        <div className="text-sm font-medium text-gray-700 mb-2">Attribution</div>
                        <div className="space-y-2">
                          {selectedNodeAttribution.map((attr, index) => (
                            <div key={index} className="p-2 bg-gray-50 rounded text-xs">
                              <div className="flex items-center justify-between mb-1">
                                <Badge variant="outline" className="text-[10px] px-1">
                                  {Math.round(attr.confidence * 100)}% confidence
                                </Badge>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-5 w-5 p-0"
                                  onClick={() => onSourceView(attr.source_id)}
                                  title="View source"
                                >
                                  <ExternalLink className="h-3 w-3" />
                                </Button>
                              </div>
                              <div className="text-gray-600 line-clamp-3">
                                {attr.content_excerpt}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-sm text-gray-500">
                    Click on a diagram node to explore its source content
                  </div>
                )}
              </div>
              
              {/* Source Actions */}
              {selectedNodeSources.length > 0 && (
                <div className="p-4">
                  <div className="text-sm font-medium text-gray-700 mb-2">Quick Actions</div>
                  <div className="space-y-2">
                    {selectedNodeSources.slice(0, 3).map((sourceId) => (
                      <Button
                        key={sourceId}
                        variant="outline"
                        size="sm"
                        className="w-full justify-start text-xs"
                        onClick={() => onSourceView(sourceId)}
                      >
                        <ExternalLink className="h-3 w-3 mr-2" />
                        View {sourceId.slice(0, 12)}...
                      </Button>
                    ))}
                    {selectedNodeSources.length > 3 && (
                      <div className="text-xs text-gray-500 text-center">
                        +{selectedNodeSources.length - 3} more sources
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default InteractiveDiagramViewer;