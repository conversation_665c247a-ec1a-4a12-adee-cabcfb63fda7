import React from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { 
  Workflow, 
  Brain, 
  GitBranch, 
  Network,
  Loader2
} from 'lucide-react';
import { Source } from '@/hooks/useSources';
import { DiagramType } from '../../../../services/sourceDiagramService';

interface QuickDiagramActionsProps {
  sources: Source[];
  onGenerateDiagram: (sources: Source[], diagramType: DiagramType) => void;
  isGenerating?: boolean;
  disabled?: boolean;
}

const QuickDiagramActions: React.FC<QuickDiagramActionsProps> = ({
  sources,
  onGenerateDiagram,
  isGenerating = false,
  disabled = false
}) => {
  const quickActions = [
    {
      type: 'flowchart' as DiagramType,
      label: 'Flowchart',
      icon: Workflow,
      description: 'Process flow from sources',
      color: 'text-blue-600 hover:text-blue-700'
    },
    {
      type: 'mindmap' as DiagramType,
      label: 'Mind Map',
      icon: Brain,
      description: 'Hierarchical concepts',
      color: 'text-green-600 hover:text-green-700'
    },
    {
      type: 'sequence' as DiagramType,
      label: 'Sequence',
      icon: GitBranch,
      description: 'Interaction flow',
      color: 'text-purple-600 hover:text-purple-700'
    }
  ];

  const handleQuickGenerate = (diagramType: DiagramType) => {
    if (sources.length > 0 && !isGenerating && !disabled) {
      onGenerateDiagram(sources, diagramType);
    }
  };

  const isDisabled = disabled || isGenerating || sources.length === 0;

  return (
    <div className="space-y-2">
      <div className="text-xs font-medium text-muted-foreground mb-2">
        Quick Generate
      </div>
      
      <div className="grid grid-cols-1 gap-1">
        {quickActions.map((action) => {
          const Icon = action.icon;
          return (
            <Button
              key={action.type}
              variant="ghost"
              size="sm"
              className={`justify-start h-auto p-2 ${action.color}`}
              onClick={() => handleQuickGenerate(action.type)}
              disabled={isDisabled}
            >
              <div className="flex items-center w-full">
                {isGenerating ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Icon className="w-4 h-4 mr-2" />
                )}
                <div className="flex-1 text-left">
                  <div className="text-xs font-medium">{action.label}</div>
                  <div className="text-xs text-muted-foreground">
                    {action.description}
                  </div>
                </div>
              </div>
            </Button>
          );
        })}
      </div>

      {sources.length === 0 && (
        <div className="text-xs text-muted-foreground text-center py-2">
          Select sources to generate diagrams
        </div>
      )}
    </div>
  );
};

export default QuickDiagramActions;