import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Download, 
  Share2, 
  FileImage, 
  FileText, 
  FileCode, 
  Database,
  Eye,
  Copy,
  ExternalLink,
  Clock,
  Users,
  Lock,
  Unlock
} from 'lucide-react';
import { toast } from 'sonner';
import { SourceDiagram } from '../../../../services/sourceDiagramService';
import { EnhancedExportService, ExportOptions, ShareableLink } from '../../../../services/enhancedExportService';
import { formatFileSize } from '../../utils/common';

interface EnhancedExportDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  diagram: SourceDiagram;
  onExportComplete?: (format: string) => void;
}

const formatOptions = [
  {
    id: 'svg',
    label: 'SVG',
    description: 'Vector graphics with embedded source metadata',
    icon: FileImage,
    features: ['Scalable', 'Source Attribution', 'Metadata Embedded']
  },
  {
    id: 'png',
    label: 'PNG',
    description: 'High-quality raster image with attribution watermark',
    icon: FileImage,
    features: ['High Resolution', 'Attribution Watermark', 'Configurable Quality']
  },
  {
    id: 'json',
    label: 'JSON',
    description: 'Complete diagram data with full source metadata',
    icon: Database,
    features: ['Full Metadata', 'Source References', 'Analysis Data']
  },
  {
    id: 'mmd',
    label: 'Mermaid Code',
    description: 'Diagram code with source comments and attribution',
    icon: FileCode,
    features: ['Source Comments', 'Node Mapping', 'Attribution Details']
  }
] as const;

type ExportFormat = typeof formatOptions[number]['id'];

const EnhancedExportDialog: React.FC<EnhancedExportDialogProps> = ({
  isOpen,
  onOpenChange,
  diagram,
  onExportComplete
}) => {
  const [selectedFormat, setSelectedFormat] = useState<ExportFormat>('svg');
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    includeSourceMetadata: true,
    includeAttribution: true,
    includeComments: true,
    quality: 1.0,
    backgroundColor: 'white',
    embedSourceLinks: true
  });
  const [isExporting, setIsExporting] = useState(false);
  const [exportPreview, setExportPreview] = useState<{
    preview: string;
    size: number;
  } | null>(null);
  const [shareableLink, setShareableLink] = useState<ShareableLink | null>(null);
  const [shareOptions, setShareOptions] = useState({
    expiresIn: 24,
    includeSourceAccess: false,
    publicAccess: false
  });
  const [isCreatingLink, setIsCreatingLink] = useState(false);

  // Load export preview when format or options change
  useEffect(() => {
    if (isOpen) {
      loadExportPreview();
    }
  }, [selectedFormat, exportOptions, isOpen]);

  const loadExportPreview = async () => {
    try {
      const preview = await EnhancedExportService.getExportPreview(diagram, selectedFormat);
      setExportPreview(preview);
    } catch (error) {
      console.error('Failed to load export preview:', error);
      setExportPreview(null);
    }
  };

  const handleExport = async () => {
    setIsExporting(true);
    try {
      await EnhancedExportService.downloadDiagramWithAttribution(
        diagram,
        selectedFormat,
        exportOptions
      );
      
      onExportComplete?.(selectedFormat);
      onOpenChange(false);
      
      toast.success(`Diagram exported as ${selectedFormat.toUpperCase()}`);
    } catch (error) {
      console.error('Export failed:', error);
      toast.error('Failed to export diagram');
    } finally {
      setIsExporting(false);
    }
  };

  const handleCreateShareableLink = async () => {
    setIsCreatingLink(true);
    try {
      const link = await EnhancedExportService.createShareableLink(diagram, shareOptions);
      setShareableLink(link);
      
      // Copy to clipboard
      await navigator.clipboard.writeText(link.url);
      toast.success('Shareable link created and copied to clipboard');
    } catch (error) {
      console.error('Failed to create shareable link:', error);
      toast.error('Failed to create shareable link');
    } finally {
      setIsCreatingLink(false);
    }
  };

  const copyShareableLink = async () => {
    if (shareableLink) {
      try {
        await navigator.clipboard.writeText(shareableLink.url);
        toast.success('Link copied to clipboard');
      } catch (error) {
        toast.error('Failed to copy link');
      }
    }
  };

  const selectedFormatInfo = formatOptions.find(f => f.id === selectedFormat)!;

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Enhanced Export Options
          </DialogTitle>
          <DialogDescription>
            Export your diagram with source attribution and metadata. Choose from multiple formats
            with advanced options for preserving source references.
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="export" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="export">Export Diagram</TabsTrigger>
            <TabsTrigger value="share">Share Link</TabsTrigger>
          </TabsList>

          <TabsContent value="export" className="space-y-6">
            {/* Format Selection */}
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-medium mb-3">Export Format</h3>
                <div className="grid grid-cols-2 gap-3">
                  {formatOptions.map((format) => {
                    const Icon = format.icon;
                    return (
                      <Card
                        key={format.id}
                        className={`cursor-pointer transition-all ${
                          selectedFormat === format.id
                            ? 'ring-2 ring-blue-500 bg-blue-50'
                            : 'hover:bg-gray-50'
                        }`}
                        onClick={() => setSelectedFormat(format.id)}
                      >
                        <CardHeader className="pb-2">
                          <div className="flex items-center gap-2">
                            <Icon className="h-5 w-5" />
                            <CardTitle className="text-base">{format.label}</CardTitle>
                          </div>
                          <CardDescription className="text-sm">
                            {format.description}
                          </CardDescription>
                        </CardHeader>
                        <CardContent className="pt-0">
                          <div className="flex flex-wrap gap-1">
                            {format.features.map((feature) => (
                              <Badge key={feature} variant="secondary" className="text-xs">
                                {feature}
                              </Badge>
                            ))}
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              </div>

              {/* Export Options */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Export Options</h3>
                
                <div className="grid grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="includeSourceMetadata"
                        checked={exportOptions.includeSourceMetadata}
                        onCheckedChange={(checked) =>
                          setExportOptions(prev => ({
                            ...prev,
                            includeSourceMetadata: checked as boolean
                          }))
                        }
                      />
                      <Label htmlFor="includeSourceMetadata">Include Source Metadata</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="includeAttribution"
                        checked={exportOptions.includeAttribution}
                        onCheckedChange={(checked) =>
                          setExportOptions(prev => ({
                            ...prev,
                            includeAttribution: checked as boolean
                          }))
                        }
                      />
                      <Label htmlFor="includeAttribution">Include Source Attribution</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="includeComments"
                        checked={exportOptions.includeComments}
                        onCheckedChange={(checked) =>
                          setExportOptions(prev => ({
                            ...prev,
                            includeComments: checked as boolean
                          }))
                        }
                      />
                      <Label htmlFor="includeComments">Include Comments (Code Export)</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="embedSourceLinks"
                        checked={exportOptions.embedSourceLinks}
                        onCheckedChange={(checked) =>
                          setExportOptions(prev => ({
                            ...prev,
                            embedSourceLinks: checked as boolean
                          }))
                        }
                      />
                      <Label htmlFor="embedSourceLinks">Embed Source Links</Label>
                    </div>
                  </div>

                  <div className="space-y-4">
                    {selectedFormat === 'png' && (
                      <div className="space-y-2">
                        <Label>Image Quality: {Math.round(exportOptions.quality! * 100)}%</Label>
                        <Slider
                          value={[exportOptions.quality! * 100]}
                          onValueChange={([value]) =>
                            setExportOptions(prev => ({
                              ...prev,
                              quality: value / 100
                            }))
                          }
                          max={100}
                          min={10}
                          step={10}
                          className="w-full"
                        />
                      </div>
                    )}

                    <div className="space-y-2">
                      <Label htmlFor="backgroundColor">Background Color</Label>
                      <select
                        id="backgroundColor"
                        value={exportOptions.backgroundColor}
                        onChange={(e) =>
                          setExportOptions(prev => ({
                            ...prev,
                            backgroundColor: e.target.value
                          }))
                        }
                        className="w-full p-2 border rounded-md"
                      >
                        <option value="white">White</option>
                        <option value="transparent">Transparent</option>
                        <option value="#f8f9fa">Light Gray</option>
                        <option value="#1f2937">Dark Gray</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>

              {/* Preview */}
              {exportPreview && (
                <div className="space-y-2">
                  <h3 className="text-lg font-medium">Export Preview</h3>
                  <Card>
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-base">
                          {selectedFormatInfo.label} Export
                        </CardTitle>
                        <Badge variant="outline">
                          {formatFileSize(exportPreview.size)}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <Textarea
                        value={exportPreview.preview}
                        readOnly
                        className="font-mono text-xs h-32 resize-none"
                      />
                    </CardContent>
                  </Card>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="share" className="space-y-6">
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-medium mb-3">Create Shareable Link</h3>
                <p className="text-sm text-gray-600 mb-4">
                  Generate a secure link to share your diagram with source references.
                </p>
              </div>

              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>Link Expires In (hours)</Label>
                    <select
                      value={shareOptions.expiresIn}
                      onChange={(e) =>
                        setShareOptions(prev => ({
                          ...prev,
                          expiresIn: parseInt(e.target.value)
                        }))
                      }
                      className="w-full p-2 border rounded-md"
                    >
                      <option value={1}>1 hour</option>
                      <option value={6}>6 hours</option>
                      <option value={24}>24 hours</option>
                      <option value={168}>1 week</option>
                      <option value={720}>1 month</option>
                    </select>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="includeSourceAccess"
                      checked={shareOptions.includeSourceAccess}
                      onCheckedChange={(checked) =>
                        setShareOptions(prev => ({
                          ...prev,
                          includeSourceAccess: checked as boolean
                        }))
                      }
                    />
                    <Label htmlFor="includeSourceAccess">Include Source Access</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="publicAccess"
                      checked={shareOptions.publicAccess}
                      onCheckedChange={(checked) =>
                        setShareOptions(prev => ({
                          ...prev,
                          publicAccess: checked as boolean
                        }))
                      }
                    />
                    <Label htmlFor="publicAccess">Public Access (No Authentication)</Label>
                  </div>
                </div>

                <div className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base flex items-center gap-2">
                        <Eye className="h-4 w-4" />
                        Share Preview
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      <div className="flex items-center gap-2 text-sm">
                        <Clock className="h-4 w-4" />
                        Expires in {shareOptions.expiresIn} hours
                      </div>
                      <div className="flex items-center gap-2 text-sm">
                        {shareOptions.publicAccess ? (
                          <>
                            <Unlock className="h-4 w-4" />
                            Public access
                          </>
                        ) : (
                          <>
                            <Lock className="h-4 w-4" />
                            Authentication required
                          </>
                        )}
                      </div>
                      <div className="flex items-center gap-2 text-sm">
                        <Users className="h-4 w-4" />
                        {shareOptions.includeSourceAccess ? 'With' : 'Without'} source access
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>

              {shareableLink && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base flex items-center gap-2">
                      <Share2 className="h-4 w-4" />
                      Shareable Link Created
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex items-center gap-2">
                      <input
                        type="text"
                        value={shareableLink.url}
                        readOnly
                        className="flex-1 p-2 border rounded-md font-mono text-sm"
                      />
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={copyShareableLink}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.open(shareableLink.url, '_blank')}
                      >
                        <ExternalLink className="h-4 w-4" />
                      </Button>
                    </div>
                    <div className="text-xs text-gray-500">
                      Link ID: {shareableLink.id}
                      {shareableLink.expires_at && (
                        <> • Expires: {new Date(shareableLink.expires_at).toLocaleString()}</>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}

              <Button
                onClick={handleCreateShareableLink}
                disabled={isCreatingLink}
                className="w-full"
              >
                {isCreatingLink ? 'Creating Link...' : 'Create Shareable Link'}
              </Button>
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleExport}
            disabled={isExporting}
            className="min-w-[120px]"
          >
            {isExporting ? 'Exporting...' : `Export ${selectedFormatInfo.label}`}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default EnhancedExportDialog;