import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { 
  Eye, 
  MoreVertical, 
  Trash2, 
  Download, 
  RefreshCw,
  Clock,
  FileText,
  Loader2
} from 'lucide-react';
import { SourceDiagram } from '../../../../services/sourceDiagramService';
import { useSourceDiagrams } from '../../hooks/useSourceDiagrams';
import { formatDistanceToNow } from 'date-fns';

interface DiagramHistoryProps {
  notebookId?: string;
  onDiagramSelect?: (diagram: SourceDiagram) => void;
  onDiagramView?: (diagram: SourceDiagram) => void;
  maxHeight?: number;
}

const DiagramHistory: React.FC<DiagramHistoryProps> = ({
  notebookId,
  onDiagramSelect,
  onDiagramView,
  maxHeight = 300
}) => {
  const [diagramToDelete, setDiagramToDelete] = useState<SourceDiagram | null>(null);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  const { 
    diagrams, 
    isLoading, 
    error, 
    deleteDiagram, 
    isDeleting,
    refetch 
  } = useSourceDiagrams({ notebookId });

  const handleDeleteClick = (diagram: SourceDiagram) => {
    setDiagramToDelete(diagram);
    setShowDeleteDialog(true);
  };

  const confirmDelete = async () => {
    if (diagramToDelete) {
      const result = await deleteDiagram(diagramToDelete.id);
      if (result.success) {
        setShowDeleteDialog(false);
        setDiagramToDelete(null);
      }
    }
  };

  const handleViewDiagram = (diagram: SourceDiagram) => {
    if (onDiagramView) {
      onDiagramView(diagram);
    } else if (onDiagramSelect) {
      onDiagramSelect(diagram);
    }
  };

  const getDiagramTypeColor = (type: string) => {
    switch (type) {
      case 'flowchart':
        return 'bg-blue-100 text-blue-800';
      case 'mindmap':
        return 'bg-green-100 text-green-800';
      case 'sequence':
        return 'bg-purple-100 text-purple-800';
      case 'relationship':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDiagramTitle = (diagram: SourceDiagram) => {
    const sourceCount = diagram.source_ids?.length || 0;
    const type = diagram.diagram_type.charAt(0).toUpperCase() + diagram.diagram_type.slice(1);
    return `${type} (${sourceCount} source${sourceCount !== 1 ? 's' : ''})`;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="w-6 h-6 animate-spin" />
        <span className="ml-2 text-sm text-muted-foreground">Loading diagrams...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-4">
        <p className="text-sm text-destructive mb-2">Failed to load diagrams</p>
        <Button variant="outline" size="sm" onClick={() => refetch()}>
          <RefreshCw className="w-4 h-4 mr-2" />
          Retry
        </Button>
      </div>
    );
  }

  if (!diagrams || diagrams.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        <FileText className="w-8 h-8 mx-auto mb-2 opacity-50" />
        <p className="text-sm">No diagrams yet</p>
        <p className="text-xs">Generate your first diagram from sources</p>
      </div>
    );
  }

  return (
    <>
      <ScrollArea style={{ maxHeight }} className="pr-2">
        <div className="space-y-2">
          {diagrams.map((diagram) => (
            <div
              key={diagram.id}
              className="border rounded-lg p-3 hover:bg-accent/50 transition-colors"
            >
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <Badge 
                      variant="secondary" 
                      className={`text-xs ${getDiagramTypeColor(diagram.diagram_type)}`}
                    >
                      {diagram.diagram_type}
                    </Badge>
                    <span className="text-xs text-muted-foreground flex items-center">
                      <Clock className="w-3 h-3 mr-1" />
                      {formatDistanceToNow(new Date(diagram.created_at), { addSuffix: true })}
                    </span>
                  </div>
                  
                  <h4 className="text-sm font-medium truncate mb-1">
                    {formatDiagramTitle(diagram)}
                  </h4>
                  
                  {diagram.analysis_metadata && (
                    <div className="text-xs text-muted-foreground">
                      {diagram.analysis_metadata.concepts_extracted} concepts, {' '}
                      {diagram.analysis_metadata.relationships_found} relationships
                    </div>
                  )}
                </div>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                      <MoreVertical className="w-4 h-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => handleViewDiagram(diagram)}>
                      <Eye className="w-4 h-4 mr-2" />
                      View
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleDeleteClick(diagram)}>
                      <Trash2 className="w-4 h-4 mr-2" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>

              <Button
                variant="ghost"
                size="sm"
                className="w-full mt-2 h-8 text-xs"
                onClick={() => handleViewDiagram(diagram)}
              >
                <Eye className="w-3 h-3 mr-1" />
                View Diagram
              </Button>
            </div>
          ))}
        </div>
      </ScrollArea>

      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Diagram</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this diagram? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => {
              setShowDeleteDialog(false);
              setDiagramToDelete(null);
            }}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Deleting...
                </>
              ) : (
                'Delete'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default DiagramHistory;