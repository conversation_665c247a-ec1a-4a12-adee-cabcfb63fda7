import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Bell, 
  RefreshCw, 
  X, 
  AlertTriangle, 
  CheckCircle, 
  Clock,
  GitCompare,
  Eye,
  Trash2
} from 'lucide-react';
import { useDiagramUpdates } from '../../hooks/useDiagramUpdates';
import { DiagramNotification } from '../../../../services/diagramNotificationService';
import { formatDistanceToNow } from 'date-fns';
import DiagramDiffViewer from './DiagramDiffViewer';

interface DiagramUpdateNotificationsProps {
  notebookId: string;
  className?: string;
}

export default function DiagramUpdateNotifications({
  notebookId,
  className
}: DiagramUpdateNotificationsProps) {
  const {
    notifications,
    outdatedDiagrams,
    isRegenerating,
    regenerateDiagram,
    batchRegenerate,
    dismissNotification,
    clearAllNotifications,
    getOutdatedCount
  } = useDiagramUpdates({ notebookId, enableNotifications: true });

  const [selectedDiffDiagram, setSelectedDiffDiagram] = useState<number | null>(null);
  const [isDiffViewerOpen, setIsDiffViewerOpen] = useState(false);

  const handleRegenerateClick = async (diagramId: number) => {
    await regenerateDiagram(diagramId, {
      preserve_layout: true,
      update_metadata: true
    });
  };

  const handleBatchRegenerate = async () => {
    if (outdatedDiagrams.length === 0) return;
    
    await batchRegenerate(outdatedDiagrams, {
      preserve_layout: true,
      update_metadata: true,
      max_concurrent: 3
    });
  };

  const handleViewDiff = (diagramId: number) => {
    setSelectedDiffDiagram(diagramId);
    setIsDiffViewerOpen(true);
  };

  const getNotificationIcon = (type: DiagramNotification['type']) => {
    switch (type) {
      case 'outdated':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'regenerated':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <X className="h-4 w-4 text-red-500" />;
      case 'batch_complete':
        return <RefreshCw className="h-4 w-4 text-blue-500" />;
      default:
        return <Bell className="h-4 w-4" />;
    }
  };

  const getNotificationVariant = (type: DiagramNotification['type']) => {
    switch (type) {
      case 'outdated':
        return 'secondary';
      case 'regenerated':
        return 'default';
      case 'error':
        return 'destructive';
      case 'batch_complete':
        return 'default';
      default:
        return 'secondary';
    }
  };

  const renderNotificationActions = (notification: DiagramNotification) => {
    return (
      <div className="flex items-center gap-2 mt-2">
        {notification.actions?.map((action, index) => (
          <Button
            key={index}
            size="sm"
            variant="outline"
            onClick={() => handleNotificationAction(notification, action)}
            disabled={isRegenerating}
            className="text-xs"
          >
            {action.action === 'regenerate' && <RefreshCw className="h-3 w-3 mr-1" />}
            {action.action === 'view_diff' && <GitCompare className="h-3 w-3 mr-1" />}
            {action.action === 'view' && <Eye className="h-3 w-3 mr-1" />}
            {action.label}
          </Button>
        ))}
        <Button
          size="sm"
          variant="ghost"
          onClick={() => dismissNotification(notification.id)}
          className="text-xs p-1"
        >
          <X className="h-3 w-3" />
        </Button>
      </div>
    );
  };

  const handleNotificationAction = async (
    notification: DiagramNotification,
    action: any
  ) => {
    switch (action.action) {
      case 'regenerate':
        await handleRegenerateClick(notification.diagram_id);
        dismissNotification(notification.id);
        break;
      case 'view_diff':
        handleViewDiff(notification.diagram_id);
        break;
      case 'view':
        // This would typically navigate to the diagram
        console.log('View diagram:', notification.diagram_id);
        break;
      case 'dismiss':
        dismissNotification(notification.id);
        break;
    }
  };

  const outdatedCount = getOutdatedCount();

  if (notifications.length === 0 && outdatedCount === 0) {
    return null;
  }

  return (
    <>
      <Card className={className}>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center justify-between text-sm">
            <div className="flex items-center gap-2">
              <Bell className="h-4 w-4" />
              Diagram Updates
              {(notifications.length > 0 || outdatedCount > 0) && (
                <Badge variant="secondary" className="text-xs">
                  {notifications.length + outdatedCount}
                </Badge>
              )}
            </div>
            <div className="flex items-center gap-1">
              {outdatedCount > 0 && (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleBatchRegenerate}
                  disabled={isRegenerating}
                  className="text-xs"
                >
                  <RefreshCw className="h-3 w-3 mr-1" />
                  Update All ({outdatedCount})
                </Button>
              )}
              {notifications.length > 0 && (
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={clearAllNotifications}
                  className="text-xs p-1"
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              )}
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <ScrollArea className="max-h-64">
            <div className="space-y-3">
              {/* Show outdated diagrams that don't have notifications yet */}
              {outdatedDiagrams
                .filter(diagramId => !notifications.some(n => n.diagram_id === diagramId))
                .map(diagramId => (
                  <div key={`outdated-${diagramId}`} className="p-3 border rounded-lg bg-yellow-50 dark:bg-yellow-900/20">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-2">
                        <AlertTriangle className="h-4 w-4 text-yellow-500 mt-0.5" />
                        <div>
                          <p className="text-sm font-medium">
                            Diagram #{diagramId} is outdated
                          </p>
                          <p className="text-xs text-muted-foreground">
                            Source content has been updated
                          </p>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2 mt-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleRegenerateClick(diagramId)}
                        disabled={isRegenerating}
                        className="text-xs"
                      >
                        <RefreshCw className="h-3 w-3 mr-1" />
                        Update
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleViewDiff(diagramId)}
                        className="text-xs"
                      >
                        <GitCompare className="h-3 w-3 mr-1" />
                        View Changes
                      </Button>
                    </div>
                  </div>
                ))}

              {/* Show notifications */}
              {notifications.map(notification => (
                <div 
                  key={notification.id}
                  className={`p-3 border rounded-lg ${
                    notification.type === 'error' 
                      ? 'bg-red-50 dark:bg-red-900/20 border-red-200' 
                      : notification.type === 'outdated'
                      ? 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200'
                      : 'bg-green-50 dark:bg-green-900/20 border-green-200'
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-2">
                      {getNotificationIcon(notification.type)}
                      <div className="flex-1">
                        <p className="text-sm font-medium">
                          {notification.diagram_title}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {notification.message}
                        </p>
                        <div className="flex items-center gap-2 mt-1">
                          <Clock className="h-3 w-3 text-muted-foreground" />
                          <span className="text-xs text-muted-foreground">
                            {formatDistanceToNow(new Date(notification.timestamp), { addSuffix: true })}
                          </span>
                          <Badge 
                            variant={getNotificationVariant(notification.type)}
                            className="text-xs"
                          >
                            {notification.priority}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </div>
                  {renderNotificationActions(notification)}
                </div>
              ))}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>

      {/* Diagram Diff Viewer */}
      {selectedDiffDiagram && (
        <DiagramDiffViewer
          diagramId={selectedDiffDiagram}
          isOpen={isDiffViewerOpen}
          onOpenChange={setIsDiffViewerOpen}
          onRegenerate={handleRegenerateClick}
        />
      )}
    </>
  );
}