import React, { useState, useEffect } from 'react';
import { Source } from '../../../../hooks/useSources';
import { SourceDiagramService, WorkflowDiagramOptions } from '../../../../services/sourceDiagramService';
import { WorkflowAnalysisResult } from '../../../../services/workflowSourceAnalyzer';
import { DiagramType } from '../../../../services/mermaidService';

interface WorkflowDiagramGeneratorProps {
  source: Source;
  notebookId?: string;
  onDiagramGenerated?: (diagramId: number, diagram: string) => void;
  onError?: (error: string) => void;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}

interface ProceduralAnalysis {
  is_procedural: boolean;
  confidence: number;
  step_count: number;
  decision_count: number;
  complexity_score: number;
}

export const WorkflowDiagramGenerator: React.FC<WorkflowDiagramGeneratorProps> = ({
  source,
  notebookId,
  onDiagramGenerated,
  onError,
  isOpen,
  onOpenChange
}) => {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [proceduralAnalysis, setProceduralAnalysis] = useState<ProceduralAnalysis | null>(null);
  const [workflowAnalysis, setWorkflowAnalysis] = useState<WorkflowAnalysisResult | null>(null);
  const [selectedTemplate, setSelectedTemplate] = useState<string>('standard');
  const [customOptions, setCustomOptions] = useState<Partial<WorkflowDiagramOptions>>({});
  const [previewDiagram, setPreviewDiagram] = useState<string>('');

  const templates = SourceDiagramService.getWorkflowDiagramTemplates();

  useEffect(() => {
    if (isOpen && source && !proceduralAnalysis) {
      analyzeSource();
    }
  }, [isOpen, source]);

  const analyzeSource = async () => {
    if (!source) return;

    setIsAnalyzing(true);
    try {
      const analysis = await SourceDiagramService.analyzeProceduralContent(source);
      setProceduralAnalysis(analysis);

      if (analysis.is_procedural) {
        const workflowAnalysis = await SourceDiagramService.getWorkflowAnalysis(source);
        setWorkflowAnalysis(workflowAnalysis);
      }
    } catch (error) {
      console.error('Failed to analyze source:', error);
      onError?.(`Failed to analyze source: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const generatePreview = async () => {
    if (!workflowAnalysis) return;

    const template = templates.find(t => t.name.toLowerCase().includes(selectedTemplate));
    if (!template) return;

    const options: WorkflowDiagramOptions = {
      ...template.options,
      ...customOptions
    };

    try {
      const { diagram } = await import('../../../../components/lighthouse-lm/utils/mermaidGenerator').then(
        module => module.MermaidGenerator.generateWorkflowDiagram(workflowAnalysis, options)
      );
      setPreviewDiagram(diagram);
    } catch (error) {
      console.error('Failed to generate preview:', error);
    }
  };

  const generateDiagram = async () => {
    if (!source || !workflowAnalysis) return;

    setIsGenerating(true);
    try {
      const template = templates.find(t => t.name.toLowerCase().includes(selectedTemplate));
      if (!template) throw new Error('Invalid template selected');

      const options: WorkflowDiagramOptions = {
        ...template.options,
        ...customOptions
      };

      const { diagramId, diagram } = await SourceDiagramService.generateWorkflowDiagram(
        source,
        notebookId,
        options
      );

      onDiagramGenerated?.(diagramId, diagram);
      onOpenChange(false);
    } catch (error) {
      console.error('Failed to generate workflow diagram:', error);
      onError?.(`Failed to generate diagram: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleTemplateChange = (templateName: string) => {
    setSelectedTemplate(templateName);
    setPreviewDiagram(''); // Clear preview when template changes
  };

  const handleOptionChange = (key: keyof WorkflowDiagramOptions, value: any) => {
    setCustomOptions(prev => ({
      ...prev,
      [key]: value
    }));
    setPreviewDiagram(''); // Clear preview when options change
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold text-gray-900">
                Generate Workflow Diagram
              </h2>
              <button
                onClick={() => onOpenChange(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto">
            {isAnalyzing ? (
              <div className="flex items-center justify-center h-64">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                  <p className="text-gray-600">Analyzing source content for workflow elements...</p>
                </div>
              </div>
            ) : proceduralAnalysis ? (
              <div className="p-6 space-y-6">
                {/* Analysis Results */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="text-lg font-medium text-gray-900 mb-3">Source Analysis</h3>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center">
                      <div className={`text-2xl font-bold ${proceduralAnalysis.is_procedural ? 'text-green-600' : 'text-red-600'}`}>
                        {proceduralAnalysis.is_procedural ? 'Yes' : 'No'}
                      </div>
                      <div className="text-sm text-gray-600">Procedural Content</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">
                        {(proceduralAnalysis.confidence * 100).toFixed(0)}%
                      </div>
                      <div className="text-sm text-gray-600">Confidence</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-purple-600">
                        {proceduralAnalysis.step_count}
                      </div>
                      <div className="text-sm text-gray-600">Steps Found</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-orange-600">
                        {proceduralAnalysis.decision_count}
                      </div>
                      <div className="text-sm text-gray-600">Decision Points</div>
                    </div>
                  </div>
                </div>

                {proceduralAnalysis.is_procedural && workflowAnalysis ? (
                  <>
                    {/* Template Selection */}
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 mb-3">Diagram Template</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {templates.map((template) => (
                          <div
                            key={template.name}
                            className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                              selectedTemplate === template.name.toLowerCase().replace(/\s+/g, '')
                                ? 'border-blue-500 bg-blue-50'
                                : 'border-gray-200 hover:border-gray-300'
                            }`}
                            onClick={() => handleTemplateChange(template.name.toLowerCase().replace(/\s+/g, ''))}
                          >
                            <h4 className="font-medium text-gray-900">{template.name}</h4>
                            <p className="text-sm text-gray-600 mt-1">{template.description}</p>
                            <div className="mt-2">
                              <div className="text-xs text-gray-500">Use cases:</div>
                              <div className="text-xs text-gray-600">
                                {template.use_cases.join(', ')}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Custom Options */}
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 mb-3">Customization Options</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Diagram Type
                          </label>
                          <select
                            value={customOptions.diagram_type || 'flowchart'}
                            onChange={(e) => handleOptionChange('diagram_type', e.target.value as DiagramType)}
                            className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                          >
                            <option value="flowchart">Flowchart</option>
                            <option value="timeline">Timeline</option>
                          </select>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Max Steps ({customOptions.max_concepts || 20})
                          </label>
                          <input
                            type="range"
                            min="5"
                            max="50"
                            value={customOptions.max_concepts || 20}
                            onChange={(e) => handleOptionChange('max_concepts', parseInt(e.target.value))}
                            className="w-full"
                          />
                        </div>
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id="include_metadata"
                            checked={customOptions.include_metadata ?? false}
                            onChange={(e) => handleOptionChange('include_metadata', e.target.checked)}
                            className="mr-2"
                          />
                          <label htmlFor="include_metadata" className="text-sm text-gray-700">
                            Include confidence indicators
                          </label>
                        </div>
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id="show_decision_outcomes"
                            checked={customOptions.show_decision_outcomes ?? true}
                            onChange={(e) => handleOptionChange('show_decision_outcomes', e.target.checked)}
                            className="mr-2"
                          />
                          <label htmlFor="show_decision_outcomes" className="text-sm text-gray-700">
                            Show decision outcomes
                          </label>
                        </div>
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id="include_loop_indicators"
                            checked={customOptions.include_loop_indicators ?? true}
                            onChange={(e) => handleOptionChange('include_loop_indicators', e.target.checked)}
                            className="mr-2"
                          />
                          <label htmlFor="include_loop_indicators" className="text-sm text-gray-700">
                            Include loop indicators
                          </label>
                        </div>
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id="show_step_numbers"
                            checked={customOptions.show_step_numbers ?? true}
                            onChange={(e) => handleOptionChange('show_step_numbers', e.target.checked)}
                            className="mr-2"
                          />
                          <label htmlFor="show_step_numbers" className="text-sm text-gray-700">
                            Show step numbers
                          </label>
                        </div>
                      </div>
                    </div>

                    {/* Preview */}
                    {previewDiagram && (
                      <div>
                        <h3 className="text-lg font-medium text-gray-900 mb-3">Preview</h3>
                        <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                          <pre className="text-xs text-gray-700 whitespace-pre-wrap font-mono">
                            {previewDiagram}
                          </pre>
                        </div>
                      </div>
                    )}
                  </>
                ) : (
                  <div className="text-center py-8">
                    <div className="text-gray-500 mb-4">
                      <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      Source doesn't contain procedural content
                    </h3>
                    <p className="text-gray-600 mb-4">
                      This source appears to be informational rather than procedural. 
                      Workflow diagrams work best with step-by-step instructions, processes, or procedures.
                    </p>
                    <p className="text-sm text-gray-500">
                      Confidence: {(proceduralAnalysis.confidence * 100).toFixed(0)}% 
                      (minimum 50% required for workflow diagrams)
                    </p>
                  </div>
                )}
              </div>
            ) : (
              <div className="flex items-center justify-center h-64">
                <div className="text-center">
                  <p className="text-gray-600">Failed to analyze source content</p>
                  <button
                    onClick={analyzeSource}
                    className="mt-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                  >
                    Retry Analysis
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* Footer */}
          {proceduralAnalysis?.is_procedural && workflowAnalysis && (
            <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
              <div className="flex items-center justify-between">
                <div className="flex space-x-3">
                  <button
                    onClick={generatePreview}
                    disabled={isGenerating}
                    className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50"
                  >
                    Generate Preview
                  </button>
                </div>
                <div className="flex space-x-3">
                  <button
                    onClick={() => onOpenChange(false)}
                    className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={generateDiagram}
                    disabled={isGenerating}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center"
                  >
                    {isGenerating && (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    )}
                    Generate Diagram
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};