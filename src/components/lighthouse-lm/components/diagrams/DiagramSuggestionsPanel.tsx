import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import {
  X,
  RefreshCw,
  Workflow,
  TrendingUp,
  Clock,
  Lightbulb
} from 'lucide-react';
import DiagramSuggestionButton from './DiagramSuggestionButton';
import { Loading<PERSON>pinner, CardHeaderWithIcon, GhostButton } from './components';
import { DiagramSuggestion } from './types';
import { Source } from '../../../../hooks/useSources';
import { ChatDiagramService } from '../../../../services/chatDiagramService';

interface DiagramSuggestionsPanelProps {
  sources: Source[];
  recentMessages: string[];
  onSuggestionGenerate: (suggestion: DiagramSuggestion) => void;
  onClose: () => void;
  isVisible: boolean;
  isGenerating?: boolean;
  className?: string;
}

/**
 * Panel component that displays context-aware diagram suggestions in chat
 */
const DiagramSuggestionsPanel: React.FC<DiagramSuggestionsPanelProps> = ({
  sources,
  recentMessages,
  onSuggestionGenerate,
  onClose,
  isVisible,
  isGenerating = false,
  className = ''
}) => {
  const [suggestions, setSuggestions] = useState<DiagramSuggestion[]>([]);
  const [isLoadingSuggestions, setIsLoadingSuggestions] = useState(false);
  const [lastAnalysisTime, setLastAnalysisTime] = useState<Date | null>(null);

  // Generate suggestions when sources or messages change
  useEffect(() => {
    if (isVisible && sources.length > 0) {
      generateSuggestions();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isVisible, sources.length, recentMessages?.length]);

  const generateSuggestions = async () => {
    if (sources.length === 0) return;

    setIsLoadingSuggestions(true);
    try {
      const contextualSuggestions = await ChatDiagramService.generateContextualSuggestions(
        sources,
        recentMessages
      );
      setSuggestions(contextualSuggestions);
      setLastAnalysisTime(new Date());
    } catch (error) {
      console.error('Failed to generate diagram suggestions:', error);
      setSuggestions([]);
    } finally {
      setIsLoadingSuggestions(false);
    }
  };

  const handleRefreshSuggestions = () => {
    generateSuggestions();
  };

  const handleSuggestionClick = (suggestion: DiagramSuggestion) => {
    onSuggestionGenerate(suggestion);
  };

  if (!isVisible) return null;

  return (
    <Card className={`diagram-suggestions-panel ${className}`}>
      <CardHeaderWithIcon
        icon={Lightbulb}
        title="Diagram Suggestions"
        description="Based on your sources and conversation"
        actionButtons={
          <>
            <GhostButton
              icon={RefreshCw}
              title="Refresh suggestions"
              onClick={handleRefreshSuggestions}
              isLoading={isLoadingSuggestions}
            />
            <GhostButton
              icon={X}
              title="Close suggestions"
              onClick={onClose}
            />
          </>
        }
      />

      <CardContent className="pt-0">
        {isLoadingSuggestions ? (
          <div className="flex items-center justify-center py-8">
            <div className="flex flex-col items-center gap-2">
              <LoadingSpinner size="md" />
              <span className="text-sm text-muted-foreground">
                Analyzing your content...
              </span>
            </div>
          </div>
        ) : suggestions.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <div className="p-3 rounded-full bg-muted/50 mb-3">
              <Workflow className="h-6 w-6 text-muted-foreground" />
            </div>
            <p className="text-sm font-medium text-muted-foreground mb-1">
              No suggestions available
            </p>
            <p className="text-xs text-muted-foreground">
              Add more sources or continue the conversation to get diagram suggestions
            </p>
          </div>
        ) : (
          <div className="space-y-3">
            {/* Suggestions List */}
            <div className="space-y-2">
              {suggestions.map((suggestion, index) => (
                <DiagramSuggestionButton
                  key={index}
                  suggestion={suggestion}
                  onGenerate={handleSuggestionClick}
                  isGenerating={isGenerating}
                />
              ))}
            </div>

            {/* Footer Info */}
            {lastAnalysisTime && (
              <>
                <Separator className="my-3" />
                <div className="flex items-center justify-between text-xs text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    <span>
                      Updated {lastAnalysisTime.toLocaleTimeString()}
                    </span>
                  </div>
                  <div className="flex items-center gap-1">
                    <TrendingUp className="h-3 w-3" />
                    <span>
                      {suggestions.length} suggestion{suggestions.length !== 1 ? 's' : ''}
                    </span>
                  </div>
                </div>
              </>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default DiagramSuggestionsPanel;