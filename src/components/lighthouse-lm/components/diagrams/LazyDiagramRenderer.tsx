/**
 * Lazy loading diagram renderer for performance optimization with large diagrams
 */

import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  ChevronDown, 
  ChevronUp, 
  Loader2, 
  Eye, 
  EyeOff,
  MoreHorizontal,
  Zap
} from 'lucide-react';
import Mermaid from '../mermaid/Mermaid';
import { LazyLoader, LazyLoadState, PERFORMANCE_CONFIG } from '../../../../services/performanceOptimizations';
import { SourceAttribution } from '../../types/sourceAnalysis';

interface DiagramElement {
  id: string;
  type: 'node' | 'edge' | 'cluster';
  content: string;
  sourceIds: string[];
  priority: number;
  size: number;
}

interface LazyDiagramRendererProps {
  diagram: string;
  title?: string;
  sourceMap: Record<string, string[]>;
  sourceAttribution: SourceAttribution[];
  onSourceView: (sourceId: string) => void;
  onNodeClick?: (nodeId: string, sourceIds: string[]) => void;
  className?: string;
  height?: string;
  enableLazyLoading?: boolean;
  initialRenderCount?: number;
  batchSize?: number;
  onPerformanceMetrics?: (metrics: PerformanceMetrics) => void;
}

interface PerformanceMetrics {
  renderTime: number;
  elementsRendered: number;
  totalElements: number;
  memoryUsage?: number;
  cacheHits: number;
  cacheMisses: number;
}

interface ViewportState {
  isVisible: boolean;
  intersectionRatio: number;
}

/**
 * Lazy loading diagram renderer that progressively loads diagram elements
 * for better performance with large diagrams
 */
const LazyDiagramRenderer: React.FC<LazyDiagramRendererProps> = ({
  diagram,
  title,
  sourceMap,
  sourceAttribution,
  onSourceView,
  onNodeClick,
  className = '',
  height = '600px',
  enableLazyLoading = true,
  initialRenderCount = PERFORMANCE_CONFIG.INITIAL_RENDER_COUNT,
  batchSize = PERFORMANCE_CONFIG.LAZY_LOAD_BATCH_SIZE,
  onPerformanceMetrics
}) => {
  const [lazyState, setLazyState] = useState<LazyLoadState<DiagramElement>>({
    items: [],
    loadedCount: 0,
    isLoading: false,
    hasMore: false
  });
  const [isExpanded, setIsExpanded] = useState(false);
  const [viewportState, setViewportState] = useState<ViewportState>({
    isVisible: false,
    intersectionRatio: 0
  });
  const [performanceMetrics, setPerformanceMetrics] = useState<PerformanceMetrics>({
    renderTime: 0,
    elementsRendered: 0,
    totalElements: 0,
    cacheHits: 0,
    cacheMisses: 0
  });

  const containerRef = useRef<HTMLDivElement>(null);
  const lazyLoaderRef = useRef<LazyLoader<DiagramElement> | null>(null);
  const intersectionObserverRef = useRef<IntersectionObserver | null>(null);
  const renderStartTimeRef = useRef<number>(0);

  // Parse diagram into elements for lazy loading
  const diagramElements = useMemo(() => {
    const startTime = performance.now();
    const elements = parseDiagramIntoElements(diagram, sourceMap, sourceAttribution);
    const parseTime = performance.now() - startTime;
    
    console.log(`Parsed ${elements.length} diagram elements in ${parseTime.toFixed(2)}ms`);
    return elements;
  }, [diagram, sourceMap, sourceAttribution]);

  // Initialize lazy loader
  useEffect(() => {
    if (!enableLazyLoading) {
      setLazyState({
        items: diagramElements,
        loadedCount: diagramElements.length,
        isLoading: false,
        hasMore: false
      });
      return;
    }

    lazyLoaderRef.current = new LazyLoader(diagramElements, batchSize);
    const initialState = lazyLoaderRef.current.getInitialBatch();
    setLazyState(initialState);

    // Update performance metrics
    setPerformanceMetrics(prev => ({
      ...prev,
      totalElements: diagramElements.length,
      elementsRendered: initialState.loadedCount
    }));
  }, [diagramElements, enableLazyLoading, batchSize]);

  // Set up intersection observer for viewport-based loading
  useEffect(() => {
    if (!enableLazyLoading || !containerRef.current) return;

    intersectionObserverRef.current = new IntersectionObserver(
      (entries) => {
        const entry = entries[0];
        setViewportState({
          isVisible: entry.isIntersecting,
          intersectionRatio: entry.intersectionRatio
        });

        // Auto-load more content when scrolling near the bottom
        if (entry.isIntersecting && entry.intersectionRatio > 0.8 && lazyState.hasMore) {
          loadMoreElements();
        }
      },
      {
        threshold: [0, 0.25, 0.5, 0.75, 1.0],
        rootMargin: '50px'
      }
    );

    intersectionObserverRef.current.observe(containerRef.current);

    return () => {
      if (intersectionObserverRef.current) {
        intersectionObserverRef.current.disconnect();
      }
    };
  }, [enableLazyLoading, lazyState.hasMore]);

  // Load more elements
  const loadMoreElements = useCallback(async () => {
    if (!lazyLoaderRef.current || lazyState.isLoading || !lazyState.hasMore) {
      return;
    }

    setLazyState(prev => ({ ...prev, isLoading: true }));
    renderStartTimeRef.current = performance.now();

    // Simulate async loading with a small delay for better UX
    await new Promise(resolve => setTimeout(resolve, 50));

    const newState = lazyLoaderRef.current.loadNextBatch();
    const renderTime = performance.now() - renderStartTimeRef.current;

    setLazyState({ ...newState, isLoading: false });

    // Update performance metrics
    setPerformanceMetrics(prev => {
      const newMetrics = {
        ...prev,
        renderTime,
        elementsRendered: newState.loadedCount,
        cacheHits: prev.cacheHits + (newState.loadedCount - prev.elementsRendered)
      };
      
      onPerformanceMetrics?.(newMetrics);
      return newMetrics;
    });
  }, [lazyState.isLoading, lazyState.hasMore, onPerformanceMetrics]);

  // Load all remaining elements
  const loadAllElements = useCallback(async () => {
    if (!lazyLoaderRef.current) return;

    setLazyState(prev => ({ ...prev, isLoading: true }));
    renderStartTimeRef.current = performance.now();

    // Load all elements at once
    const allElements = diagramElements;
    const renderTime = performance.now() - renderStartTimeRef.current;

    setLazyState({
      items: allElements,
      loadedCount: allElements.length,
      isLoading: false,
      hasMore: false
    });

    // Update performance metrics
    setPerformanceMetrics(prev => {
      const newMetrics = {
        ...prev,
        renderTime,
        elementsRendered: allElements.length,
        cacheMisses: prev.cacheMisses + (allElements.length - prev.elementsRendered)
      };
      
      onPerformanceMetrics?.(newMetrics);
      return newMetrics;
    });
  }, [diagramElements, onPerformanceMetrics]);

  // Generate diagram content from loaded elements
  const renderedDiagram = useMemo(() => {
    if (!enableLazyLoading) {
      return diagram;
    }

    return generateDiagramFromElements(lazyState.items, diagram);
  }, [lazyState.items, diagram, enableLazyLoading]);

  // Performance indicator component
  const PerformanceIndicator = () => (
    <div className="flex items-center gap-2 text-xs text-muted-foreground">
      <Zap className="h-3 w-3" />
      <span>
        {performanceMetrics.elementsRendered}/{performanceMetrics.totalElements} elements
      </span>
      {performanceMetrics.renderTime > 0 && (
        <span>• {performanceMetrics.renderTime.toFixed(1)}ms</span>
      )}
      {viewportState.isVisible && (
        <Badge variant="outline" className="text-[10px] px-1">
          Visible
        </Badge>
      )}
    </div>
  );

  return (
    <div ref={containerRef} className={`lazy-diagram-renderer ${className}`}>
      <Card className="h-full flex flex-col">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <CardTitle className="text-lg">
                {title || 'Diagram'}
              </CardTitle>
              {enableLazyLoading && (
                <Badge variant="secondary" className="text-xs">
                  Lazy Loading
                </Badge>
              )}
            </div>
            
            <div className="flex items-center gap-2">
              {enableLazyLoading && lazyState.hasMore && (
                <>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={loadMoreElements}
                    disabled={lazyState.isLoading}
                    title="Load more elements"
                  >
                    {lazyState.isLoading ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <ChevronDown className="h-4 w-4" />
                    )}
                  </Button>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={loadAllElements}
                    disabled={lazyState.isLoading}
                    title="Load all elements"
                  >
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </>
              )}
              
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsExpanded(!isExpanded)}
                title={isExpanded ? 'Collapse details' : 'Expand details'}
              >
                {isExpanded ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </Button>
            </div>
          </div>
          
          <div className="flex items-center justify-between">
            <PerformanceIndicator />
            
            {enableLazyLoading && lazyState.hasMore && (
              <div className="text-xs text-muted-foreground">
                {lazyState.loadedCount} of {performanceMetrics.totalElements} loaded
              </div>
            )}
          </div>
        </CardHeader>
        
        <CardContent className="flex-1 p-0">
          {/* Main Diagram Area */}
          <div className="relative h-full">
            {lazyState.isLoading && lazyState.loadedCount === 0 ? (
              <div className="flex items-center justify-center h-full">
                <div className="space-y-4 w-full max-w-md">
                  <Skeleton className="h-8 w-3/4" />
                  <Skeleton className="h-32 w-full" />
                  <Skeleton className="h-8 w-1/2" />
                </div>
              </div>
            ) : (
              <Mermaid
                diagram={renderedDiagram}
                title=""
                mode="view"
                height={height}
                showControls={true}
                sourceMap={sourceMap}
                sourceAttribution={sourceAttribution}
                onNodeClick={onNodeClick}
                onSourceView={onSourceView}
                showSourceAttribution={false}
              />
            )}
            
            {/* Loading overlay for incremental loading */}
            {lazyState.isLoading && lazyState.loadedCount > 0 && (
              <div className="absolute bottom-4 right-4 bg-background/80 backdrop-blur-sm rounded-lg p-2 border">
                <div className="flex items-center gap-2 text-sm">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span>Loading more elements...</span>
                </div>
              </div>
            )}
          </div>
          
          {/* Expanded Details Panel */}
          {isExpanded && (
            <div className="border-t p-4 bg-muted/30">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <div className="font-medium text-muted-foreground">Elements</div>
                  <div>{performanceMetrics.elementsRendered}/{performanceMetrics.totalElements}</div>
                </div>
                <div>
                  <div className="font-medium text-muted-foreground">Render Time</div>
                  <div>{performanceMetrics.renderTime.toFixed(1)}ms</div>
                </div>
                <div>
                  <div className="font-medium text-muted-foreground">Cache Hits</div>
                  <div>{performanceMetrics.cacheHits}</div>
                </div>
                <div>
                  <div className="font-medium text-muted-foreground">Viewport</div>
                  <div>{(viewportState.intersectionRatio * 100).toFixed(0)}%</div>
                </div>
              </div>
              
              {enableLazyLoading && lazyState.hasMore && (
                <div className="mt-4 pt-4 border-t">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={loadAllElements}
                    disabled={lazyState.isLoading}
                    className="w-full"
                  >
                    {lazyState.isLoading ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Loading all elements...
                      </>
                    ) : (
                      <>
                        Load All Remaining ({performanceMetrics.totalElements - lazyState.loadedCount})
                      </>
                    )}
                  </Button>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

// Helper functions

/**
 * Parse diagram content into elements for lazy loading
 */
function parseDiagramIntoElements(
  diagram: string,
  sourceMap: Record<string, string[]>,
  sourceAttribution: SourceAttribution[]
): DiagramElement[] {
  const elements: DiagramElement[] = [];
  const lines = diagram.split('\n');
  
  let elementId = 0;
  
  for (const line of lines) {
    const trimmedLine = line.trim();
    if (!trimmedLine || trimmedLine.startsWith('%%')) continue;
    
    // Parse different types of diagram elements
    if (trimmedLine.includes('[') || trimmedLine.includes('(') || trimmedLine.includes('{')) {
      // Node definition
      const nodeMatch = trimmedLine.match(/(\w+)[\[\(]([^\]\)]+)[\]\)]/);
      if (nodeMatch) {
        const [, nodeId, nodeLabel] = nodeMatch;
        const sourceIds = sourceMap[nodeId] || [];
        
        elements.push({
          id: `element_${elementId++}`,
          type: 'node',
          content: trimmedLine,
          sourceIds,
          priority: sourceIds.length > 0 ? 2 : 1,
          size: trimmedLine.length
        });
      }
    } else if (trimmedLine.includes('-->') || trimmedLine.includes('---')) {
      // Edge definition
      elements.push({
        id: `element_${elementId++}`,
        type: 'edge',
        content: trimmedLine,
        sourceIds: [],
        priority: 1,
        size: trimmedLine.length
      });
    } else if (trimmedLine.includes('subgraph') || trimmedLine.includes('end')) {
      // Cluster definition
      elements.push({
        id: `element_${elementId++}`,
        type: 'cluster',
        content: trimmedLine,
        sourceIds: [],
        priority: 3,
        size: trimmedLine.length
      });
    } else {
      // Other diagram elements
      elements.push({
        id: `element_${elementId++}`,
        type: 'node',
        content: trimmedLine,
        sourceIds: [],
        priority: 1,
        size: trimmedLine.length
      });
    }
  }
  
  // Sort by priority (higher priority elements first)
  return elements.sort((a, b) => b.priority - a.priority);
}

/**
 * Generate diagram content from loaded elements
 */
function generateDiagramFromElements(elements: DiagramElement[], originalDiagram: string): string {
  if (elements.length === 0) {
    return originalDiagram.split('\n')[0] || 'graph TD'; // Return just the diagram type
  }
  
  // Extract diagram type from original
  const lines = originalDiagram.split('\n');
  const diagramType = lines[0] || 'graph TD';
  
  // Combine loaded elements
  const elementContent = elements.map(el => el.content).join('\n    ');
  
  return `${diagramType}\n    ${elementContent}`;
}

export default LazyDiagramRenderer;