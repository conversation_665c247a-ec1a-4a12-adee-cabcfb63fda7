import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useNotebooks } from '../../hooks/useNotebooks';
import { useNotebookUpdate } from '../../hooks/useNotebookUpdate';
import { useSources } from '../../hooks/useSources';
import { useIsDesktop } from '../../hooks/useIsDesktop';
import InsightsAPI from '../../../../services/insights-api';
import '../../styles/resizable-panels.css';

// Import layout components directly to avoid barrel export issues
import LoadingSkeleton from '../layout/LoadingSkeleton';
import DesktopLayout from '../layout/DesktopLayout';
import MobileLayout from '../layout/MobileLayout';
import NotFound from '../layout/NotFound';

interface NotebookPageProps {
  notebookId?: string;
  onBack?: () => void;
}

const NotebookPage: React.FC<NotebookPageProps> = ({ notebookId: propId, onBack: propOnBack }) => {
  const params = useParams<{ id: string }>();
  const id = propId || params?.id;
  const navigate = useNavigate();
  const isDesktop = useIsDesktop();
  
  const [notebook, setNotebook] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  
  const { notebooks } = useNotebooks();
  const { updateNotebook, isUpdating } = useNotebookUpdate();
  const { sources } = useSources(id);
  
  // Load notebook data
  useEffect(() => {
    const loadNotebook = async () => {
      if (!id) return;
      
      try {
        setLoading(true);
        const notebookData = await InsightsAPI.getNotebook(id);
        setNotebook(notebookData);
      } catch (error) {
        console.error('Failed to load notebook:', error);
      } finally {
        setLoading(false);
      }
    };
    
    loadNotebook();
  }, [id]);
  
  // Check if notebook has sources
  const hasSource = sources && sources.length > 0;

  const handleBackClick = () => {
    if (propOnBack) {
      propOnBack();
    } else {
      navigate('/lighthouse-lm');
    }
  };

  // Show loading skeleton
  if (loading) {
    return <LoadingSkeleton isDesktop={isDesktop} />;
  }

  // Show not found if notebook doesn't exist
  if (!notebook) {
    return <NotFound onBack={handleBackClick} />;
  }

  // Mobile layout
  if (!isDesktop) {
    return (
      <MobileLayout
        notebookId={id}
        notebook={notebook}
        hasSource={hasSource}
        onBack={handleBackClick}
      />
    );
  }

  // Desktop layout with resizable panels
  return (
    <DesktopLayout
      notebookId={id}
      notebook={notebook}
      hasSource={hasSource}
      onBack={handleBackClick}
    />
  );
};

export default NotebookPage;