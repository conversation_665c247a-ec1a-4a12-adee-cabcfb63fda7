import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useNotebooks } from '../../hooks/useNotebooks';
import { useNotebookUpdate } from '../../hooks/useNotebookUpdate';
import { useSources } from '../../hooks/useSources';
import { useIsDesktop } from '../../hooks/useIsDesktop';
import { Citation } from '../../types/message';
import InsightsAPI from '../../../../services/insights-api';
import '../../styles/resizable-panels.css';

// Import layout components
import { 
  NotebookLoadingSkeleton, 
  NotebookDesktopLayout, 
  NotebookMobileLayout, 
  NotebookNotFound 
} from '../layout';

interface NotebookPageProps {
  notebookId?: string;
  onBack?: () => void;
}

const NotebookPage: React.FC<NotebookPageProps> = ({ notebookId: propId, onBack: propOnBack }) => {
  const params = useParams<{ id: string }>();
  const id = propId || params?.id;
  const navigate = useNavigate();
  const isDesktop = useIsDesktop();
  
  const [selectedCitation, setSelectedCitation] = useState<Citation | null>(null);
  const [activeTab, setActiveTab] = useState('chat');
  const [notebook, setNotebook] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  
  const { notebooks } = useNotebooks();
  const { updateNotebook, isUpdating } = useNotebookUpdate();
  const { sources } = useSources(id);
  
  // Load notebook data
  useEffect(() => {
    const loadNotebook = async () => {
      if (!id) return;
      
      try {
        setLoading(true);
        const notebookData = await InsightsAPI.getNotebook(id);
        setNotebook(notebookData);
      } catch (error) {
        console.error('Failed to load notebook:', error);
      } finally {
        setLoading(false);
      }
    };
    
    loadNotebook();
  }, [id]);
  
  // Check if notebook has sources
  const hasSource = sources && sources.length > 0;

  const handleCitationClick = (citation: Citation) => {
    console.log('NotebookPage: Citation clicked from chat', citation);
    setSelectedCitation(citation);
    // Switch to sources tab to show the citation
    if (!isDesktop) {
      setActiveTab('sources');
    }
  };

  const handleCitationClose = () => {
    console.log('NotebookPage: Citation closed');
    setSelectedCitation(null);
  };

  const handleBackClick = () => {
    if (propOnBack) {
      propOnBack();
    } else {
      navigate('/lighthouse-lm');
    }
  };

  // Show loading skeleton
  if (loading) {
    return <NotebookLoadingSkeleton isDesktop={isDesktop} />;
  }

  // Show not found if notebook doesn't exist
  if (!notebook) {
    return <NotebookNotFound onBack={handleBackClick} />;
  }

  // Mobile layout
  if (!isDesktop) {
    return (
      <NotebookMobileLayout
        notebookId={id}
        notebook={notebook}
        hasSource={hasSource}
        selectedCitation={selectedCitation}
        activeTab={activeTab}
        onBack={handleBackClick}
        onCitationClick={handleCitationClick}
        onCitationClose={handleCitationClose}
        onTabChange={setActiveTab}
      />
    );
  }

  // Desktop layout with resizable panels
  return (
    <NotebookDesktopLayout
      notebookId={id}
      notebook={notebook}
      hasSource={hasSource}
      selectedCitation={selectedCitation}
      activeTab={activeTab}
      setActiveTab={setActiveTab}
      onCitationClick={handleCitationClick}
      onCitationClose={handleCitationClose}
      setSelectedCitation={setSelectedCitation}
    />
  );
};

export default NotebookPage;