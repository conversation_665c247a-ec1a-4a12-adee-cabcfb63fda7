import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { XCircle } from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { useQueryClient } from '@tanstack/react-query';
import { Source } from '@/hooks/useSources';
import { SourceList } from '@/components/lighthouse-lm/components/sourcemanager/SourceList';
// import QuickDiagramActions from '../diagrams/QuickDiagramActions'; // Removed - diagrams folder deleted
import { DiagramType } from '@/services/sourceDiagramService';

interface SourcesTabContentProps {
  sources: Source[] | null;
  isLoading: boolean;
  error: string | null;
  notebookId?: string;
  onSourceClick: (source: Source) => void;
  onSourceRename: (source: Source) => void;
  onSourceDelete: (source: Source) => void;
  onQuickDiagramGenerate: (sources: Source[], diagramType: DiagramType) => void;
}

const SourcesTabContent: React.FC<SourcesTabContentProps> = ({
  sources,
  isLoading,
  error,
  notebookId,
  onSourceClick,
  onSourceRename,
  onSourceDelete,
  onQuickDiagramGenerate,
}) => {
  const queryClient = useQueryClient();

  if (error) {
    return (
      <ScrollArea className="flex-1">
        <div className="p-4">
          <div className="text-center py-8 text-destructive">
            <XCircle className="w-12 h-12 mx-auto mb-4" />
            <p className="text-lg font-medium mb-2">Error loading sources</p>
            <p className="text-sm mb-4">{error}</p>
            <Button 
              onClick={() => queryClient.invalidateQueries({ queryKey: ['sources', notebookId] })} 
              variant="outline"
            >
              Try Again
            </Button>
          </div>
        </div>
      </ScrollArea>
    );
  }

  return (
    <ScrollArea className="flex-1">
      <div className="p-4">
        <SourceList
          sources={sources || []}
          isLoading={isLoading}
          viewMode="compact"
          showCheckbox={false}
          showActions={true}
          emptyMessage="No sources yet"
          emptyDescription="Add your first source to get started."
          height={400}
          onSourceClick={onSourceClick}
          onSourceRename={onSourceRename}
          onSourceDelete={(sourceId) => {
            const source = sources?.find(s => s.id === sourceId);
            if (source) onSourceDelete(source);
          }}
        />
        
        {/* QuickDiagramActions removed - diagrams folder deleted */}
      </div>
    </ScrollArea>
  );
};

export default SourcesTabContent;