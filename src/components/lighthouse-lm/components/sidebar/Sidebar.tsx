import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Card } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  FileText, 
  GitBranch, 
  Presentation, 
  Plus, 
  Settings,
  FolderOpen,
  Clock,
  Star
} from 'lucide-react';
import NotesTab from '../note/NotesTab';
import SlidevTab from '../slidev/SlidevTab';
import { Citation } from '../../types/message';

interface SidebarProps {
  notebookId?: string;
  selectedCitation?: Citation | null;
  onExport?: (format: string, content: any) => void;
  onPublish?: (platform: string, content: any) => void;
  onCollaborate?: (action: string, data: any) => void;
}

const Sidebar: React.FC<SidebarProps> = ({
  notebookId,
  selectedCitation,
  onExport,
  onPublish,
  onCollaborate,
}) => {
  const [activeTab, setActiveTab] = useState('notes');

  return (
    <div className="h-full flex flex-col bg-background">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
        <div className="px-4 pt-4 pb-2">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="notes" className="text-xs sm:text-sm">
              <FileText className="h-4 w-4 mr-2" />
              Notes
            </TabsTrigger>
            <TabsTrigger value="slidev" className="text-xs sm:text-sm">
              <Presentation className="h-4 w-4 mr-2" />
              Slides
            </TabsTrigger>
          </TabsList>
        </div>

        <ScrollArea className="flex-1">
          <TabsContent value="notes" className="px-4 pb-4 mt-0">
            <NotesTab notebookId={notebookId} />
          </TabsContent>

          <TabsContent value="slidev" className="px-4 pb-4 mt-0">
            <SlidevTab />
          </TabsContent>
        </ScrollArea>

        {/* Quick Actions Bar */}
        <div className="border-t p-3 space-y-2">
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              size="sm" 
              className="flex-1"
              onClick={() => onExport?.('markdown', {})}
            >
              <FileText className="h-4 w-4 mr-1" />
              Export
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              className="flex-1"
              onClick={() => onPublish?.('web', {})}
            >
              <GitBranch className="h-4 w-4 mr-1" />
              Publish
            </Button>
          </div>
          
          {/* Recent Items */}
          <div className="space-y-1">
            <div className="text-xs text-muted-foreground px-1">Recent</div>
            <div className="space-y-1">
              <button className="w-full text-left px-2 py-1 text-sm hover:bg-accent rounded-sm flex items-center gap-2">
                <Clock className="h-3 w-3 text-muted-foreground" />
                <span className="truncate">Last edited note</span>
              </button>
            </div>
          </div>
        </div>
      </Tabs>
    </div>
  );
};

export default Sidebar;