
import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Plus, Loader2, XCircle, Settings, FileText, Workflow, History } from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import AddSourcesDialog from '../sourcemanager/AddSourcesDialog';
import RenameSourceDialog from '../sourcemanager/RenameSourceDialog';
import SourceContentViewer from '../chat/SourceContentViewer';
import SourceManagerDialog from '../sourcemanager/SourceManagerDialog';
import SourceDiagramGenerator from '../mermaid/SourceDiagramGenerator';
import DiagramHistory from '../diagrams/DiagramHistory';
import QuickDiagramActions from '../diagrams/QuickDiagramActions';
import { useSources } from '@/hooks/useSources';
import { useSourceDelete } from '@/components/lighthouse-lm/hooks/useSourceDelete';
import { Citation } from '@/components/lighthouse-lm/types/message';
import { useQueryClient } from '@tanstack/react-query';
import { Source } from '@/hooks/useSources';
import { LoadingState, EmptyState } from '@/components/shared/sidebar';
import { SourceList } from '@/components/lighthouse-lm/components/sourcemanager/SourceList';
import { useSourceManager } from '@/components/lighthouse-lm/hooks/useSourceManager';
import { useSourceOperations } from '@/services/sourceOperationsService';
import { DiagramType } from '../../../../services/sourceDiagramService';

interface SourcesSidebarProps {
  hasSource: boolean;
  notebookId?: string;
  selectedCitation?: Citation | null;
  onCitationClose?: () => void;
  setSelectedCitation?: (citation: Citation | null) => void;
}

const SourcesSidebar = ({
  hasSource,
  notebookId,
  selectedCitation,
  onCitationClose,
  setSelectedCitation
}: SourcesSidebarProps) => {
  const [showAddSourcesDialog, setShowAddSourcesDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showRenameDialog, setShowRenameDialog] = useState(false);
  const [showSourceManager, setShowSourceManager] = useState(false);
  const [showDiagramGenerator, setShowDiagramGenerator] = useState(false);
  const [sourceToDelete, setSourceToDelete] = useState<Source | null>(null);
  const [sourceToRename, setSourceToRename] = useState<Source | null>(null);
  const [selectedSources, setSelectedSources] = useState<Source[]>([]);
  const [activeTab, setActiveTab] = useState('sources');
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Debug logging for notebookId
  // console.log('[SourcesSidebar] Rendering with notebookId:', notebookId);
  // console.log('[SourcesSidebar] notebookId type:', typeof notebookId);
  // console.log('[SourcesSidebar] notebookId is undefined?', notebookId === undefined);
  // console.log('[SourcesSidebar] notebookId is empty string?', notebookId === '');
  
  const { sources, isLoading, error } = useSources(notebookId);
  const { deleteSource, isDeleting } = useSourceDelete();
  const queryClient = useQueryClient();
  const sourceOperations = useSourceOperations(
    async (id: string) => {
      await deleteSource(id);
    },
    notebookId
  );

  // Handle dialog state changes to refetch sources
  useEffect(() => {
    if (!showAddSourcesDialog && !showRenameDialog) {
      queryClient.invalidateQueries({ queryKey: ['sources', notebookId] });
    }
  }, [showAddSourcesDialog, showRenameDialog, queryClient, notebookId]);

  // Clean up rename dialog state
  useEffect(() => {
    if (!showRenameDialog) {
      setSourceToRename(null);
    }
  }, [showRenameDialog]);

  const handleAddSource = () => {
    setShowAddSourcesDialog(true);
  };

  const handleFileUpload = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      // Handle file upload logic here
      console.log('Files selected:', files);
    }
  };

  const handleSourceClick = (source: Source) => {
    if (setSelectedCitation) {
      const citation: Citation = {
          citation_id: parseInt(source.id) || 0,
          source_id: source.id,
          source_title: source.title || 'Untitled',
          source_type: source.source_type || 'document',
          chunk_lines_from: 0,
          chunk_lines_to: 0,
          chunk_index: 0,
          excerpt: source.content || ''
        };
      setSelectedCitation(citation);
    }
  };

  const handleRemoveSource = (source: Source) => {
    setSourceToDelete(source);
    setShowDeleteDialog(true);
  };

  const handleRenameSource = (source: Source) => {
    setSourceToRename(source);
    setShowRenameDialog(true);
  };

  const confirmDelete = async () => {
    if (sourceToDelete) {
      try {
        await deleteSource(sourceToDelete.id);
        queryClient.invalidateQueries({ queryKey: ['sources', notebookId] });
        setShowDeleteDialog(false);
        setSourceToDelete(null);
      } catch (error) {
        console.error('Failed to delete source:', error);
      }
    }
  };

  const handleBulkDelete = async (sourceIds: string[]) => {
    const result = await sourceOperations.bulkDelete(sourceIds);
    console.log('Bulk delete result:', result);
  };

  const handleBulkExport = async (sourceIds: string[]) => {
    const sourcesToExport = sources?.filter(s => sourceIds.includes(s.id)) || [];
    const result = sourceOperations.bulkExport(sourcesToExport, sourceIds);
    console.log('Bulk export result:', result);
  };

  const handleBulkArchive = async (sourceIds: string[]) => {
    const result = await sourceOperations.bulkArchive(sourceIds);
    console.log('Bulk archive result:', result);
  };

  const handleGenerateDiagram = () => {
    if (sources && sources.length > 0) {
      setSelectedSources(sources);
      setShowDiagramGenerator(true);
    }
  };

  const handleQuickDiagramGenerate = (sourcesToUse: Source[], diagramType: DiagramType) => {
    setSelectedSources(sourcesToUse);
    setShowDiagramGenerator(true);
  };

  const handleDiagramGenerated = (diagram: any) => {
    console.log('Diagram generated:', diagram);
    // Could add a toast notification here
  };

  const handleDiagramError = (error: string) => {
    console.error('Diagram generation error:', error);
    // Could add a toast notification here
  };

  const handleDiagramView = (diagram: any) => {
    console.log('View diagram:', diagram);
    // Could open diagram in a modal or navigate to diagram view
  };



  if (selectedCitation) {
    return (
      <aside className="w-full h-full bg-background border-l border-border flex flex-col">
        <div className="flex items-center justify-between p-4 border-b border-border">
          <h2 className="text-lg font-semibold">Source Content</h2>
          <Button
            variant="ghost"
            size="sm"
            onClick={onCitationClose}
            className="h-8 w-8 p-0"
          >
            <XCircle className="w-4 h-4" />
          </Button>
        </div>
        <div className="flex-1 overflow-hidden">
          <SourceContentViewer 
            citation={selectedCitation}
            sourceContent={selectedCitation.excerpt || ''}
          />
        </div>
      </aside>
    );
  }

  return (
    <aside className="w-full h-full bg-background border-l border-border flex flex-col">
      <div className="flex items-center justify-between p-4 border-b border-border">
        <h2 className="text-lg font-semibold">Sources & Diagrams</h2>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleAddSource}
            className="flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            Add
          </Button>
          {sources && sources.length > 0 && (
            <>
              <Button
                variant="outline"
                size="sm"
                onClick={handleGenerateDiagram}
                className="flex items-center gap-2"
              >
                <Workflow className="w-4 h-4" />
                Diagram
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowSourceManager(true)}
                className="flex items-center gap-2"
              >
                <Settings className="w-4 h-4" />
                Manager
              </Button>
            </>
          )}
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
        <div className="px-4 pt-2">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="sources" className="text-xs">
              <FileText className="w-3 h-3 mr-1" />
              Sources
            </TabsTrigger>
            <TabsTrigger value="diagrams" className="text-xs">
              <History className="w-3 h-3 mr-1" />
              Diagrams
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="sources" className="flex-1 mt-0">
          <ScrollArea className="flex-1">
            <div className="p-4">
              {error ? (
                <div className="text-center py-8 text-destructive">
                  <XCircle className="w-12 h-12 mx-auto mb-4" />
                  <p className="text-lg font-medium mb-2">Error loading sources</p>
                   <p className="text-sm mb-4">{error}</p>
                  <Button onClick={() => queryClient.invalidateQueries({ queryKey: ['sources', notebookId] })} variant="outline">
                    Try Again
                  </Button>
                </div>
              ) : (
                <>
                  <SourceList
                    sources={sources || []}
                    isLoading={isLoading}
                    viewMode="compact"
                    showCheckbox={false}
                    showActions={true}
                    emptyMessage="No sources yet"
                    emptyDescription="Add your first source to get started with your AI visual board."
                    height={400}
                    onSourceClick={handleSourceClick}
                    onSourceRename={handleRenameSource}
                    onSourceDelete={(sourceId) => {
                      const source = sources?.find(s => s.id === sourceId);
                      if (source) handleRemoveSource(source);
                    }}
                  />
                  
                  {sources && sources.length > 0 && (
                    <>
                      <Separator className="my-4" />
                      <QuickDiagramActions
                        sources={sources}
                        onGenerateDiagram={handleQuickDiagramGenerate}
                      />
                    </>
                  )}
                </>
              )}
            </div>
          </ScrollArea>
        </TabsContent>

        <TabsContent value="diagrams" className="flex-1 mt-0">
          <div className="p-4">
            <DiagramHistory
              notebookId={notebookId}
              onDiagramView={handleDiagramView}
              maxHeight={500}
            />
          </div>
        </TabsContent>
      </Tabs>

      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept=".pdf,.doc,.docx,.txt,.md"
        onChange={handleFileChange}
        className="hidden"
      />

      <AddSourcesDialog
        open={showAddSourcesDialog}
        onOpenChange={setShowAddSourcesDialog}
        notebookId={notebookId}
      />

      <RenameSourceDialog
        open={showRenameDialog}
        onOpenChange={setShowRenameDialog}
        source={sourceToRename}
        notebookId={notebookId}
      />

      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Source</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{sourceToDelete?.title || 'this source'}"? 
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => {
              setShowDeleteDialog(false);
              setSourceToDelete(null);
            }}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Deleting...
                </>
              ) : (
                'Delete'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <SourceManagerDialog
        open={showSourceManager}
        onOpenChange={setShowSourceManager}
        sources={sources || []}
        onSourceSelect={(source) => handleSourceClick(source as Source)}
        onSourceDelete={async (sourceId) => {
          const source = sources?.find(s => s.id === sourceId);
          if (source) {
            setSourceToDelete(source);
            setShowDeleteDialog(true);
          }
        }}
        onSourceRename={async (source) => {
          setSourceToRename(source as Source);
          setShowRenameDialog(true);
        }}
        onBulkDelete={handleBulkDelete}
        onBulkExport={handleBulkExport}
        onBulkArchive={handleBulkArchive}
      />

      <SourceDiagramGenerator
        sources={selectedSources}
        onDiagramGenerated={handleDiagramGenerated}
        onError={handleDiagramError}
        isOpen={showDiagramGenerator}
        onOpenChange={setShowDiagramGenerator}
        notebookId={notebookId}
      />
    </aside>
  );
};

export default SourcesSidebar;
