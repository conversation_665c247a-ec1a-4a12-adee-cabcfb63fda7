import React from 'react';
import { Button } from '@/components/ui/button';
import { XCircle } from 'lucide-react';
import SourceContentViewer from '../sourcemanager/SourceContentViewer';
import { Citation } from '@/components/lighthouse-lm/types/message';

interface CitationViewProps {
  citation: Citation;
  onClose?: () => void;
}

const CitationView: React.FC<CitationViewProps> = ({ citation, onClose }) => {
  return (
    <aside className="w-full h-full bg-background border-l border-border flex flex-col">
      <div className="flex items-center justify-between p-4 border-b border-border">
        <h2 className="text-lg font-semibold">Source Content</h2>
        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
          className="h-8 w-8 p-0"
        >
          <XCircle className="w-4 h-4" />
        </Button>
      </div>
      <div className="flex-1 overflow-hidden">
        <SourceContentViewer 
          citation={citation}
          sourceContent={citation.excerpt || ''}
        />
      </div>
    </aside>
  );
};

export default CitationView;