import React, { useState, useEffect, useRef, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Plus, Edit, Trash2, FileText, Save, Play, Grid3X3, Presentation, ChevronLeft, ChevronRight, RotateCcw, Maximize2, Eye, Moon, Sun, Wand2, Sparkles, Bot, GitBranch, Network, RefreshCw } from 'lucide-react';
import { Card } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Citation } from '../../types/message';
import NotesTab from '../note/NotesTab';
import SlidevTab from '../slidev/SlidevTab';
import MermaidTab from '../mermaid/MermaidTab';

interface Slide {
  id: string;
  title: string;
  content: string;
  notes?: string;
  clicks?: number;
  transition?: string;
  background?: string;
  layout?: string;
  createdAt: Date;
  updatedAt: Date;
}

interface Presentation {
  id: string;
  title: string;
  theme: string;
  slides: Slide[];
  currentSlide: number;
  totalClicks: number;
  createdAt: Date;
  updatedAt: Date;
}

interface StudioSidebarProps {
  notebookId?: string;
  selectedCitation?: Citation | null;
}

const StudioSidebar: React.FC<StudioSidebarProps> = ({
  notebookId,
  selectedCitation,
}) => {
  const [activeTab, setActiveTab] = useState('notes');
  
  return (
    <div className="h-full flex flex-col bg-background overflow-hidden">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col overflow-hidden">
        <div className="px-4 pt-4 pb-2 flex-shrink-0">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="notes" className="text-xs sm:text-sm">Notes</TabsTrigger>
            <TabsTrigger value="slidev" className="text-xs sm:text-sm">Slidev</TabsTrigger>
            <TabsTrigger value="mermaid" className="text-xs sm:text-sm">Mermaid</TabsTrigger>
          </TabsList>
        </div>
        
        <TabsContent value="notes" className="flex-1 flex flex-col mt-0 overflow-hidden">
          <NotesTab 
             notebookId={notebookId}
           />
        </TabsContent>
        
        <TabsContent value="slidev" className="flex-1 flex flex-col mt-0 overflow-hidden">
          <SlidevTab />
        </TabsContent>
        
        <TabsContent value="mermaid" className="flex-1 flex flex-col mt-0 overflow-hidden">
          <MermaidTab />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default StudioSidebar;