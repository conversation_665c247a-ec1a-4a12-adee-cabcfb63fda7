import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent } from '../../../ui/card';
import { Progress } from '../../../ui/progress';
import { Button } from '../../../ui/button';
import { Badge } from '../../../ui/badge';
import {
  Loader2,
  CheckCircle,
  AlertCircle,
  X,
  Minimize2,
  Maximize2,
  Pause,
  Play,
  RotateCcw,
  Info,
  Clock,
  Activity
} from 'lucide-react';

export interface ProgressTask {
  id: string;
  title: string;
  description?: string;
  progress: number;
  status: 'pending' | 'running' | 'paused' | 'completed' | 'failed';
  startTime: Date;
  endTime?: Date;
  estimatedTime?: number; // in seconds
  subtasks?: ProgressSubtask[];
  error?: string;
  canCancel?: boolean;
  canPause?: boolean;
  canRetry?: boolean;
}

interface ProgressSubtask {
  id: string;
  title: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress?: number;
}

interface ProgressTrackerProps {
  tasks: ProgressTask[];
  onCancel?: (taskId: string) => void;
  onPause?: (taskId: string) => void;
  onResume?: (taskId: string) => void;
  onRetry?: (taskId: string) => void;
  onClose?: (taskId: string) => void;
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  className?: string;
}

const ProgressTracker: React.FC<ProgressTrackerProps> = ({
  tasks,
  onCancel,
  onPause,
  onResume,
  onRetry,
  onClose,
  position = 'bottom-right',
  className = ''
}) => {
  const [minimized, setMinimized] = useState(false);
  const [expandedTasks, setExpandedTasks] = useState<Set<string>>(new Set());

  const activeTasks = tasks.filter(t => 
    t.status === 'running' || t.status === 'paused' || t.status === 'pending'
  );
  const completedTasks = tasks.filter(t => t.status === 'completed');
  const failedTasks = tasks.filter(t => t.status === 'failed');

  const toggleTaskExpansion = (taskId: string) => {
    setExpandedTasks(prev => {
      const newSet = new Set(prev);
      if (newSet.has(taskId)) {
        newSet.delete(taskId);
      } else {
        newSet.add(taskId);
      }
      return newSet;
    });
  };

  const formatTime = (seconds: number): string => {
    if (seconds < 60) return `${seconds}s`;
    if (seconds < 3600) return `${Math.floor(seconds / 60)}m ${seconds % 60}s`;
    return `${Math.floor(seconds / 3600)}h ${Math.floor((seconds % 3600) / 60)}m`;
  };

  const calculateElapsedTime = (task: ProgressTask): string => {
    const now = task.endTime || new Date();
    const elapsed = Math.floor((now.getTime() - task.startTime.getTime()) / 1000);
    return formatTime(elapsed);
  };

  const calculateRemainingTime = (task: ProgressTask): string | null => {
    if (!task.estimatedTime || task.progress === 0) return null;
    const elapsed = (new Date().getTime() - task.startTime.getTime()) / 1000;
    const rate = task.progress / elapsed;
    const remaining = (100 - task.progress) / rate;
    return formatTime(Math.floor(remaining));
  };

  const getStatusIcon = (status: ProgressTask['status']) => {
    switch (status) {
      case 'running':
        return <Loader2 className="h-4 w-4 animate-spin" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'paused':
        return <Pause className="h-4 w-4 text-yellow-500" />;
      default:
        return <Clock className="h-4 w-4 text-muted-foreground" />;
    }
  };

  const getPositionClasses = () => {
    switch (position) {
      case 'bottom-left':
        return 'bottom-4 left-4';
      case 'top-right':
        return 'top-4 right-4';
      case 'top-left':
        return 'top-4 left-4';
      default:
        return 'bottom-4 right-4';
    }
  };

  if (tasks.length === 0) return null;

  return (
    <div className={`fixed ${getPositionClasses()} z-40 ${className}`}>
      <AnimatePresence>
        {minimized ? (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
          >
            <Button
              onClick={() => setMinimized(false)}
              className="shadow-lg"
              size="lg"
            >
              <Activity className="h-4 w-4 mr-2" />
              {activeTasks.length} task{activeTasks.length !== 1 ? 's' : ''}
              {failedTasks.length > 0 && (
                <Badge variant="destructive" className="ml-2">
                  {failedTasks.length}
                </Badge>
              )}
            </Button>
          </motion.div>
        ) : (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            className="w-96"
          >
            <Card className="shadow-lg">
              <div className="flex items-center justify-between p-3 border-b">
                <div className="flex items-center gap-2">
                  <Activity className="h-4 w-4" />
                  <span className="font-semibold text-sm">Tasks</span>
                  <div className="flex items-center gap-1">
                    {activeTasks.length > 0 && (
                      <Badge variant="outline" className="text-xs">
                        {activeTasks.length} active
                      </Badge>
                    )}
                    {completedTasks.length > 0 && (
                      <Badge variant="outline" className="text-xs text-green-600">
                        {completedTasks.length} done
                      </Badge>
                    )}
                    {failedTasks.length > 0 && (
                      <Badge variant="destructive" className="text-xs">
                        {failedTasks.length} failed
                      </Badge>
                    )}
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setMinimized(true)}
                  className="h-7 w-7 p-0"
                >
                  <Minimize2 className="h-3 w-3" />
                </Button>
              </div>

              <CardContent className="p-0 max-h-96 overflow-y-auto">
                {tasks.map((task) => (
                  <div
                    key={task.id}
                    className="border-b last:border-b-0 p-3"
                  >
                    <div className="space-y-2">
                      <div className="flex items-start justify-between">
                        <div className="flex items-start gap-2 flex-1">
                          {getStatusIcon(task.status)}
                          <div className="flex-1">
                            <div className="flex items-center gap-2">
                              <span className="text-sm font-medium">
                                {task.title}
                              </span>
                              {task.subtasks && task.subtasks.length > 0 && (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => toggleTaskExpansion(task.id)}
                                  className="h-5 w-5 p-0"
                                >
                                  {expandedTasks.has(task.id) ? (
                                    <Minimize2 className="h-3 w-3" />
                                  ) : (
                                    <Maximize2 className="h-3 w-3" />
                                  )}
                                </Button>
                              )}
                            </div>
                            {task.description && (
                              <p className="text-xs text-muted-foreground mt-0.5">
                                {task.description}
                              </p>
                            )}
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-1">
                          {task.status === 'running' && task.canPause && onPause && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => onPause(task.id)}
                              className="h-6 w-6 p-0"
                            >
                              <Pause className="h-3 w-3" />
                            </Button>
                          )}
                          {task.status === 'paused' && onResume && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => onResume(task.id)}
                              className="h-6 w-6 p-0"
                            >
                              <Play className="h-3 w-3" />
                            </Button>
                          )}
                          {task.status === 'failed' && task.canRetry && onRetry && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => onRetry(task.id)}
                              className="h-6 w-6 p-0"
                            >
                              <RotateCcw className="h-3 w-3" />
                            </Button>
                          )}
                          {(task.status === 'running' || task.status === 'paused') && 
                           task.canCancel && onCancel && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => onCancel(task.id)}
                              className="h-6 w-6 p-0"
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          )}
                          {(task.status === 'completed' || task.status === 'failed') && 
                           onClose && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => onClose(task.id)}
                              className="h-6 w-6 p-0"
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          )}
                        </div>
                      </div>

                      {task.status === 'running' && (
                        <div className="space-y-1">
                          <Progress value={task.progress} className="h-1" />
                          <div className="flex items-center justify-between text-xs text-muted-foreground">
                            <span>{task.progress}%</span>
                            <div className="flex items-center gap-2">
                              <span className="flex items-center gap-1">
                                <Clock className="h-3 w-3" />
                                {calculateElapsedTime(task)}
                              </span>
                              {calculateRemainingTime(task) && (
                                <span>~{calculateRemainingTime(task)} left</span>
                              )}
                            </div>
                          </div>
                        </div>
                      )}

                      {task.error && (
                        <div className="flex items-start gap-1 p-2 bg-destructive/10 rounded text-xs">
                          <Info className="h-3 w-3 text-destructive mt-0.5" />
                          <span className="text-destructive">{task.error}</span>
                        </div>
                      )}

                      {expandedTasks.has(task.id) && task.subtasks && (
                        <div className="ml-6 space-y-1 pt-1">
                          {task.subtasks.map((subtask) => (
                            <div
                              key={subtask.id}
                              className="flex items-center gap-2 text-xs"
                            >
                              {subtask.status === 'completed' ? (
                                <CheckCircle className="h-3 w-3 text-green-500" />
                              ) : subtask.status === 'running' ? (
                                <Loader2 className="h-3 w-3 animate-spin" />
                              ) : subtask.status === 'failed' ? (
                                <AlertCircle className="h-3 w-3 text-red-500" />
                              ) : (
                                <div className="h-3 w-3 rounded-full border border-muted-foreground" />
                              )}
                              <span className={
                                subtask.status === 'completed' ? 'line-through text-muted-foreground' : ''
                              }>
                                {subtask.title}
                              </span>
                              {subtask.progress !== undefined && subtask.status === 'running' && (
                                <span className="text-muted-foreground ml-auto">
                                  {subtask.progress}%
                                </span>
                              )}
                            </div>
                          ))}
                        </div>
                      )}

                      {task.status === 'completed' && (
                        <div className="text-xs text-muted-foreground">
                          Completed in {calculateElapsedTime(task)}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default ProgressTracker;