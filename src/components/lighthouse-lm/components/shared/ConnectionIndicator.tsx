import React from 'react';
import { Badge } from '../../../ui/badge';
import { Wifi, WifiOff, RefreshCw, CheckCircle, AlertCircle } from 'lucide-react';
import { useNotebookContext } from '../../contexts/NotebookContext';
import { cn } from '../../lib/utils';

const ConnectionIndicator: React.FC = () => {
  const { syncStatus, syncEnabled, selectedSources, activeDocument } = useNotebookContext();
  
  const getStatusIcon = () => {
    if (!syncEnabled) return <WifiOff className="w-3 h-3" />;
    switch (syncStatus) {
      case 'syncing':
        return <RefreshCw className="w-3 h-3 animate-spin" />;
      case 'error':
        return <AlertCircle className="w-3 h-3" />;
      default:
        return <CheckCircle className="w-3 h-3" />;
    }
  };
  
  const getStatusColor = () => {
    if (!syncEnabled) return 'secondary';
    switch (syncStatus) {
      case 'syncing':
        return 'default';
      case 'error':
        return 'destructive';
      default:
        return 'outline';
    }
  };
  
  return (
    <div className="flex items-center gap-2">
      <Badge variant={getStatusColor()} className="text-xs">
        {getStatusIcon()}
        <span className="ml-1">
          {!syncEnabled ? 'Offline' : syncStatus === 'syncing' ? 'Syncing' : 'Connected'}
        </span>
      </Badge>
      {selectedSources.length > 0 && (
        <Badge variant="secondary" className="text-xs">
          {selectedSources.length} sources
        </Badge>
      )}
      {activeDocument && (
        <Badge variant="secondary" className="text-xs">
          Studio active
        </Badge>
      )}
    </div>
  );
};

export default ConnectionIndicator;