import React from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';

interface ConfirmationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  description: string;
  confirmText?: string;
  cancelText?: string;
  onConfirm: () => void | Promise<void>;
  onCancel?: () => void;
  variant?: 'default' | 'destructive';
  loading?: boolean;
  loadingText?: string;
  children?: React.ReactNode;
}

/**
 * Reusable confirmation dialog component
 */
export const ConfirmationDialog: React.FC<ConfirmationDialogProps> = ({
  open,
  onOpenChange,
  title,
  description,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  onConfirm,
  onCancel,
  variant = 'default',
  loading = false,
  loadingText,
  children,
}) => {
  const handleConfirm = async () => {
    try {
      await onConfirm();
    } catch (error) {
      console.error('Confirmation action failed:', error);
    }
  };

  const handleCancel = () => {
    onCancel?.();
    onOpenChange(false);
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{title}</AlertDialogTitle>
          <AlertDialogDescription>{description}</AlertDialogDescription>
        </AlertDialogHeader>

        {children && (
          <div className="py-4">
            {children}
          </div>
        )}

        <AlertDialogFooter>
          <AlertDialogCancel onClick={handleCancel} disabled={loading}>
            {cancelText}
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            disabled={loading}
            className={variant === 'destructive' ? 'bg-destructive text-destructive-foreground hover:bg-destructive/90' : ''}
          >
            {loading ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                {loadingText || 'Processing...'}
              </>
            ) : (
              confirmText
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

/**
 * Specialized delete confirmation dialog
 */
export const DeleteConfirmationDialog: React.FC<
  Omit<ConfirmationDialogProps, 'title' | 'description' | 'confirmText' | 'variant'> & {
    itemName?: string;
    itemType?: string;
  }
> = ({ itemName, itemType = 'item', ...props }) => {
  const title = `Delete ${itemType}`;
  const description = `Are you sure you want to delete "${itemName || 'this ' + itemType}"? This action cannot be undone.`;

  return (
    <ConfirmationDialog
      {...props}
      title={title}
      description={description}
      confirmText="Delete"
      variant="destructive"
    />
  );
};

/**
 * Specialized bulk operation confirmation dialog
 */
export const BulkOperationDialog: React.FC<
  Omit<ConfirmationDialogProps, 'title' | 'confirmText'> & {
    operation: string;
    count: number;
    itemType: string;
  }
> = ({ operation, count, itemType, description, ...props }) => {
  const title = `${operation} ${count} ${itemType}${count > 1 ? 's' : ''}`;
  const finalDescription = description || `Are you sure you want to ${operation.toLowerCase()} ${count} ${itemType}${count > 1 ? 's' : ''}?`;

  return (
    <ConfirmationDialog
      {...props}
      title={title}
      description={finalDescription}
      confirmText={operation}
    />
  );
};