import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertTriangle, RefreshCw, FileX, Bug } from 'lucide-react';

export type ErrorContext = 'generation' | 'analysis' | 'rendering' | 'export' | 'general';

export interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  context?: ErrorContext;
  maxRetries?: number;
  showTechnicalDetails?: boolean;
  showDebugInfo?: boolean;
  variant?: 'simple' | 'detailed';
  enableAnalytics?: boolean;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  retryCount: number;
}

interface ErrorDetails {
  title: string;
  description: string;
  suggestions?: string[];
}

/**
 * Unified error boundary component that handles both simple and complex error scenarios
 * Supports context-aware error messages for diagram operations and general errors
 */
export class UnifiedErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  public static defaultProps = {
    variant: 'simple',
    maxRetries: 3,
    showTechnicalDetails: true,
    showDebugInfo: process.env.NODE_ENV === 'development',
    enableAnalytics: true,
    context: 'general' as ErrorContext,
  };

  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      error,
      errorInfo,
    });

    // Log error
    console.error('ErrorBoundary caught an error:', error, errorInfo);

    // Call custom error handler
    this.props.onError?.(error, errorInfo);

    // Report to analytics if enabled
    if (this.props.enableAnalytics && typeof window !== 'undefined' && (window as any).analytics) {
      (window as any).analytics.track('Error Boundary Triggered', {
        error: error.message,
        context: this.props.context,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
      });
    }
  }

  private handleRetry = () => {
    const maxRetries = this.props.maxRetries || 3;
    if (this.state.retryCount < maxRetries) {
      this.setState(prevState => ({
        hasError: false,
        error: null,
        errorInfo: null,
        retryCount: prevState.retryCount + 1,
      }));
    }
  };

  private handleReset = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0,
    });
  };

  private handleRefresh = () => {
    window.location.reload();
  };

  private getErrorDetails(): ErrorDetails {
    const { error } = this.state;
    const { context } = this.props;

    if (!error) {
      return {
        title: 'Something went wrong',
        description: 'An unexpected error occurred. Please try again.',
        suggestions: ['Try refreshing the page', 'Contact support if the problem persists'],
      };
    }

    // Context-specific error messages for diagram operations
    if (context && context !== 'general') {
      return this.getDiagramErrorDetails(error, context);
    }

    // General error messages
    return {
      title: 'Something went wrong',
      description: error.message || 'We encountered an unexpected error.',
      suggestions: [
        'Try refreshing the page',
        'Check your internet connection',
        'Contact support if the problem persists',
      ],
    };
  }

  private getDiagramErrorDetails(error: Error, context: ErrorContext): ErrorDetails {
    switch (context) {
      case 'generation':
        if (error.message.includes('timeout')) {
          return {
            title: 'Diagram Generation Timeout',
            description: 'The diagram generation took too long to complete.',
            suggestions: [
              'Try reducing the number of sources',
              'Simplify the content complexity',
              'Check your internet connection',
            ],
          };
        }
        if (error.message.includes('memory')) {
          return {
            title: 'Memory Limit Exceeded',
            description: 'The source content is too large to process.',
            suggestions: [
              'Split large sources into smaller parts',
              'Reduce the maximum concepts setting',
              'Try processing fewer sources at once',
            ],
          };
        }
        return {
          title: 'Diagram Generation Failed',
          description: 'Unable to generate diagram from the selected sources.',
          suggestions: [
            'Check that sources contain valid content',
            'Try with different diagram settings',
            'Verify source accessibility',
          ],
        };

      case 'analysis':
        if (error.message.includes('parse')) {
          return {
            title: 'Content Analysis Failed',
            description: 'Unable to analyze the source content structure.',
            suggestions: [
              'Check source content format',
              'Ensure sources are not corrupted',
              'Try with plain text sources first',
            ],
          };
        }
        return {
          title: 'Source Analysis Error',
          description: 'Failed to extract concepts and relationships from sources.',
          suggestions: [
            'Verify source content is readable',
            'Try with simpler content first',
            'Check source file integrity',
          ],
        };

      case 'rendering':
        if (error.message.includes('mermaid')) {
          return {
            title: 'Diagram Rendering Failed',
            description: 'The generated diagram syntax is invalid.',
            suggestions: [
              'Try regenerating with simpler options',
              'Check diagram complexity settings',
              'Report this as a bug if it persists',
            ],
          };
        }
        return {
          title: 'Rendering Error',
          description: 'Unable to display the generated diagram.',
          suggestions: [
            'Try refreshing the diagram',
            'Check browser compatibility',
            'Clear browser cache',
          ],
        };

      case 'export':
        return {
          title: 'Export Failed',
          description: 'Unable to export the diagram in the requested format.',
          suggestions: [
            'Try a different export format',
            'Check available disk space',
            'Verify export permissions',
          ],
        };

      default:
        return {
          title: 'Operation Failed',
          description: error.message || 'An unexpected error occurred.',
          suggestions: ['Try the operation again', 'Contact support if the issue persists'],
        };
    }
  }

  private renderSimpleError() {
    const { error } = this.state;
    const { showTechnicalDetails } = this.props;

    return (
      <div
        className="flex flex-col items-center justify-center p-8 text-center space-y-4"
        role="alert"
        aria-live="assertive"
      >
        <div className="w-16 h-16 rounded-full bg-destructive/10 flex items-center justify-center">
          <AlertTriangle className="w-8 h-8 text-destructive" aria-hidden="true" />
        </div>

        <div className="space-y-2">
          <h3 className="text-lg font-semibold text-foreground">Something went wrong</h3>
          <p className="text-sm text-muted-foreground max-w-md">
            We encountered an unexpected error. Please try refreshing the page or contact support if
            the problem persists.
          </p>
        </div>

        {showTechnicalDetails && error && (
          <details className="text-left w-full max-w-md">
            <summary className="cursor-pointer text-sm text-muted-foreground hover:text-foreground">
              Technical Details
            </summary>
            <pre className="mt-2 p-2 bg-muted rounded text-xs overflow-auto">{error.message}</pre>
          </details>
        )}

        <div className="flex gap-2">
          <Button onClick={this.handleRetry} variant="default" className="flex items-center gap-2">
            <RefreshCw className="w-4 h-4" aria-hidden="true" />
            Try Again
          </Button>
          <Button onClick={this.handleRefresh} variant="outline">
            Refresh Page
          </Button>
        </div>
      </div>
    );
  }

  private renderDetailedError() {
    const { error, errorInfo, retryCount } = this.state;
    const { showTechnicalDetails, showDebugInfo, maxRetries = 3 } = this.props;
    const { title, description, suggestions = [] } = this.getErrorDetails();
    const canRetry = retryCount < maxRetries;

    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardHeader>
          <div className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-destructive" />
            <CardTitle className="text-destructive">{title}</CardTitle>
          </div>
          <CardDescription>{description}</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Error Details */}
          {showTechnicalDetails && error && (
            <Alert>
              <Bug className="h-4 w-4" />
              <AlertTitle>Technical Details</AlertTitle>
              <AlertDescription className="font-mono text-sm">{error.message}</AlertDescription>
            </Alert>
          )}

          {/* Suggestions */}
          {suggestions.length > 0 && (
            <div className="space-y-2">
              <h4 className="font-medium">Suggested Solutions:</h4>
              <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                {suggestions.map((suggestion, index) => (
                  <li key={index}>{suggestion}</li>
                ))}
              </ul>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-2 pt-4">
            {canRetry && (
              <Button onClick={this.handleRetry} variant="default" size="sm">
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry ({maxRetries - retryCount} left)
              </Button>
            )}
            <Button onClick={this.handleReset} variant="outline" size="sm">
              <FileX className="h-4 w-4 mr-2" />
              Reset
            </Button>
            <Button onClick={this.handleRefresh} variant="ghost" size="sm">
              Refresh Page
            </Button>
          </div>

          {/* Debug Information */}
          {showDebugInfo && errorInfo && (
            <details className="mt-4">
              <summary className="cursor-pointer text-sm font-medium">Debug Information</summary>
              <pre className="mt-2 text-xs bg-muted p-2 rounded overflow-auto max-h-40">
                {error?.stack}
                {'\n\nComponent Stack:'}
                {errorInfo.componentStack}
              </pre>
            </details>
          )}
        </CardContent>
      </Card>
    );
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return this.props.variant === 'detailed'
        ? this.renderDetailedError()
        : this.renderSimpleError();
    }

    return this.props.children;
  }
}

// Export default for backward compatibility
export default UnifiedErrorBoundary;