import React from 'react';
import { Panel, PanelGroup, PanelResizeHandle } from 'react-resizable-panels';
import { MessageSquare, BookOpen, Brain } from 'lucide-react';
import Chat<PERSON>rea from '../chat/ChatArea';
import SourcesSidebar from '../sidebar/SourcesSidebar';
import StudioSidebar from '../sidebar/StudioSidebar';
import { Citation } from '../../types/message';

interface NotebookDesktopLayoutProps {
  notebookId?: string;
  notebook: any;
  hasSource: boolean;
  selectedCitation: Citation | null;
  activeTab: string;
  setActiveTab: (tab: string) => void;
  onCitationClick: (citation: Citation) => void;
  onCitationClose: () => void;
  setSelectedCitation: (citation: Citation | null) => void;
}

export const NotebookDesktopLayout: React.FC<NotebookDesktopLayoutProps> = ({
  notebookId,
  notebook,
  hasSource,
  selectedCitation,
  activeTab,
  setActiveTab,
  onCitationClick,
  onCitationClose,
  setSelectedCitation,
}) => {
  return (
    <div className="h-screen bg-background text-foreground p-4 overflow-hidden">
      <PanelGroup direction="horizontal" className="h-full rounded-lg">
        {/* Left Sidebar */}
        <Panel defaultSize={20} minSize={15} maxSize={30} className="bg-card border border-border rounded-l-lg">
          <div className="flex flex-col h-full overflow-hidden">
            {/* Logo Section */}
            <div className="p-4 border-b border-border flex-shrink-0">
              <div className="flex items-center gap-2">
                <Brain className="h-5 w-5 text-primary flex-shrink-0" />
                <span className="font-semibold truncate">{notebook?.title || 'Notebook'}</span>
              </div>
            </div>

            {/* Navigation Items */}
            <div className="flex-1 min-h-0 overflow-y-auto">
              <nav className="p-2">
                <button
                  onClick={() => setActiveTab('chat')}
                  className={`w-full flex items-center gap-3 px-3 py-2 rounded-lg transition-colors mb-1 ${
                    activeTab === 'chat'
                      ? 'bg-accent text-accent-foreground'
                      : 'text-foreground hover:bg-accent hover:text-accent-foreground'
                  }`}
                >
                  <MessageSquare className="h-4 w-4 flex-shrink-0" />
                  <span className="text-sm truncate">Chat With Anything</span>
                </button>

                <button
                  onClick={() => setActiveTab('sources')}
                  className={`w-full flex items-center gap-3 px-3 py-2 rounded-lg transition-colors mb-1 ${
                    activeTab === 'sources'
                      ? 'bg-accent text-accent-foreground'
                      : 'text-foreground hover:bg-accent hover:text-accent-foreground'
                  }`}
                >
                  <BookOpen className="h-4 w-4 flex-shrink-0" />
                  <span className="text-sm truncate">AI Visual Boards</span>
                </button>
              </nav>
            </div>
          </div>
        </Panel>

        <PanelResizeHandle className="resize-handle-horizontal" />

        {/* Main Content Area */}
        <Panel defaultSize={50} minSize={30} className="bg-card border-y border-border">
          <div className="flex flex-col h-full">
            <div className="flex-1 overflow-hidden">
              {activeTab === 'chat' ? (
                <ChatArea
                  hasSource={hasSource}
                  notebookId={notebookId}
                  notebook={notebook}
                  onCitationClick={onCitationClick}
                />
              ) : activeTab === 'sources' ? (
                <div className="p-8">
                  <SourcesSidebar 
                    hasSource={hasSource}
                    notebookId={notebookId}
                    selectedCitation={selectedCitation}
                    onCitationClose={onCitationClose}
                    setSelectedCitation={setSelectedCitation}
                  />
                </div>
              ) : (
                <div className="p-8 text-center text-muted-foreground">
                  <p>Select a tab to view content</p>
                </div>
              )}
            </div>
          </div>
        </Panel>

        <PanelResizeHandle className="resize-handle-horizontal" />

        {/* Right Panel - Studio Sidebar */}
        <Panel defaultSize={30} minSize={20} maxSize={40} className="bg-card border border-border rounded-r-lg">
          <div className="flex flex-col h-full overflow-hidden">
            <div className="flex-1 overflow-y-auto">
              {selectedCitation && (
                <div className="p-4 border-b border-border">
                  <h3 className="text-sm font-medium mb-3">Citation Details</h3>
                  <div className="space-y-2 text-sm text-muted-foreground">
                    <p>{selectedCitation.excerpt || selectedCitation.source_title}</p>
                  </div>
                </div>
              )}

              <div className="flex-1">
                <StudioSidebar
                  notebookId={notebookId}
                  selectedCitation={selectedCitation}
                />
              </div>
            </div>
          </div>
        </Panel>
      </PanelGroup>
    </div>
  );
};

export default NotebookDesktopLayout;