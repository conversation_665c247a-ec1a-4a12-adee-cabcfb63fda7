import React from 'react';
import Header from './Header';
import MobileNotebookTabs from '../notebook/MobileNotebookTabs';
import { useNotebookLayout } from '../../hooks/useNotebookLayout';

interface MobileLayoutProps {
  notebookId?: string;
  notebook: any;
  hasSource: boolean;
  onBack: () => void;
}

export const MobileLayout: React.FC<MobileLayoutProps> = ({
  notebookId,
  notebook,
  hasSource,
  onBack,
}) => {
  const {
    activeTab,
    selectedCitation,
    handleCitationClick,
    handleCitationClose,
    handleTabChange,
  } = useNotebookLayout('chat');

  return (
    <div className="min-h-screen bg-background flex flex-col pb-4">
      <NotebookHeader 
        notebookId={notebookId}
        notebook={notebook}
        onBack={onBack}
      />
      
      <div className="flex-1 overflow-hidden">
        <MobileNotebookTabs
          notebookId={notebookId}
          notebook={notebook}
          hasSource={hasSource}
          selectedCitation={selectedCitation}
          onCitationClick={handleCitationClick}
          onCitationClose={handleCitationClose}
          activeTab={activeTab}
          onTabChange={handleTabChange}
        />
      </div>
    </div>
  );
};

export default MobileLayout;