import React, { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Trash2, Users, Calendar, FileText, Loader2 } from 'lucide-react';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { useNotebookDelete } from '../../hooks/useNotebookDelete';

interface NotebookCardProps {
  notebook: {
    id: string;
    title: string;
    date: string;
    sources: number;
    icon: string;
    color: string;
    hasCollaborators?: boolean;
  };
}

const NotebookCard = ({
  notebook
}: NotebookCardProps) => {
  const navigate = useNavigate();
  const [isHovered, setIsHovered] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);
  const { deleteNotebook, isDeleting } = useNotebookDelete();

  // Animation on mount
  useEffect(() => {
    if (cardRef.current) {
      cardRef.current.style.opacity = '0';
      cardRef.current.style.transform = 'translateY(20px) scale(0.95)';
      
      const timer = setTimeout(() => {
        if (cardRef.current) {
          cardRef.current.style.transition = 'all 0.6s cubic-bezier(0.16, 1, 0.3, 1)';
          cardRef.current.style.opacity = '1';
          cardRef.current.style.transform = 'translateY(0) scale(1)';
        }
      }, Math.random() * 200); // Staggered animation
      
      return () => clearTimeout(timer);
    }
  }, []);

  const handleDeleteClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    console.log('Delete button clicked for notebook:', notebook.id);
    setShowDeleteDialog(true);
  };

  const handleCardClick = () => {
    setIsLoading(true);
    // Add a brief delay for smooth transition effect
    setTimeout(() => {
      navigate(`/notebook/${notebook.id}`);
    }, 200);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      handleCardClick();
    }
    if (e.key === 'Delete' && (e.metaKey || e.ctrlKey)) {
      e.preventDefault();
      setShowDeleteDialog(true);
    }
  };

  const handleConfirmDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    console.log('Confirming delete for notebook:', notebook.id);
    deleteNotebook(notebook.id);
    setShowDeleteDialog(false);
  };

  // Generate CSS classes from color name
  const colorName = notebook.color || 'gray';
  const backgroundClass = `bg-${colorName}-100`;
  const borderClass = `border-${colorName}-200`;

  return (
    <div 
      ref={cardRef}
      className={`group relative bg-card rounded-xl border transition-all duration-300 cursor-pointer overflow-hidden h-64 transform-gpu ${
        isHovered || isFocused 
          ? 'border-primary/30 shadow-xl scale-[1.02] -translate-y-1' 
          : 'border-border hover:border-border/60 shadow-sm'
      } ${
        isLoading ? 'pointer-events-none' : ''
      }`}
      onClick={handleCardClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onFocus={() => setIsFocused(true)}
      onBlur={() => setIsFocused(false)}
      onKeyDown={handleKeyDown}
      tabIndex={0}
      role="button"
      aria-label={`Open notebook: ${notebook.title}. Created on ${notebook.date}. Contains ${notebook.sources} source${notebook.sources !== 1 ? 's' : ''}${notebook.hasCollaborators ? '. Shared with collaborators' : ''}`}
      aria-describedby={`notebook-${notebook.id}-details`}
      aria-pressed={false}
    >
      {/* Enhanced gradient overlay with animation */}
      <div className={`absolute inset-0 bg-gradient-to-br from-accent/30 via-accent/20 to-accent/30 transition-all duration-500 ${
        isHovered || isFocused ? 'opacity-100 scale-105' : 'opacity-0 scale-100'
      }`} />
      
      {/* Loading overlay */}
      {isLoading && (
        <div className="absolute inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-20 animate-in fade-in duration-200">
          <div className="flex items-center space-x-2 text-primary">
            <Loader2 className="h-5 w-5 animate-spin" />
            <span className="text-sm font-medium">Opening...</span>
          </div>
        </div>
      )}
      
      {/* Focus ring */}
      <div className={`absolute inset-0 rounded-xl ring-2 ring-primary ring-offset-2 transition-opacity duration-200 ${
        isFocused ? 'opacity-100' : 'opacity-0'
      }`} />
      
      {/* Enhanced delete button with micro-interactions */}
      <div className={`absolute top-4 right-4 z-10 transition-all duration-300 transform ${
        isHovered || isFocused 
          ? 'opacity-100 scale-100 translate-x-0' 
          : 'opacity-0 scale-90 translate-x-2'
      }`} data-delete-action="true">
        <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
          <AlertDialogTrigger asChild>
            <button 
              onClick={handleDeleteClick} 
              className="p-2 bg-card/90 backdrop-blur-sm hover:bg-destructive/10 rounded-lg text-muted-foreground hover:text-destructive transition-all duration-300 shadow-sm hover:shadow-lg transform hover:scale-110 focus:outline-none focus:ring-2 focus:ring-destructive focus:ring-offset-2" 
              disabled={isDeleting} 
              data-delete-action="true"
              aria-label={`Delete notebook: ${notebook.title}`}
              tabIndex={-1}
            >
              {isDeleting ? (
                <Loader2 className="h-4 w-4 animate-spin" aria-hidden="true" />
              ) : (
                <Trash2 className="h-4 w-4" aria-hidden="true" />
              )}
              <span className="sr-only">Delete notebook</span>
            </button>
          </AlertDialogTrigger>
          <AlertDialogContent role="alertdialog" aria-labelledby="delete-dialog-title" aria-describedby="delete-dialog-description">
            <AlertDialogHeader>
              <AlertDialogTitle id="delete-dialog-title">Delete this notebook?</AlertDialogTitle>
              <AlertDialogDescription id="delete-dialog-description">
                You're about to delete this notebook and all of its content. This cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel aria-label="Cancel deletion">Cancel</AlertDialogCancel>
              <AlertDialogAction 
                onClick={handleConfirmDelete} 
                className="bg-destructive hover:bg-destructive/90" 
                disabled={isDeleting}
                aria-label={`Confirm deletion of ${notebook.title}`}
              >
                {isDeleting ? 'Deleting...' : 'Delete'}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
      
      {/* Card content */}
      <div className="p-6 h-full flex flex-col">
        {/* Icon with advanced animations */}
        <div 
          className={`w-14 h-14 rounded-xl bg-gradient-to-br from-accent to-accent/80 flex items-center justify-center mb-4 transition-all duration-500 transform-gpu ${
            isHovered || isFocused 
              ? 'scale-110 rotate-3 shadow-lg bg-gradient-to-br from-accent/80 to-accent' 
              : 'scale-100 rotate-0 shadow-sm'
          }`}
          aria-hidden="true"
        >
          <span 
            className={`text-3xl transition-all duration-300 ${
              isHovered || isFocused ? 'scale-110' : 'scale-100'
            }`}
            aria-hidden="true"
          >
            {notebook.icon}
          </span>
        </div>
        
        {/* Title with enhanced typography and animations */}
        <h3 
          className={`mb-3 pr-8 line-clamp-2 text-xl font-semibold leading-tight flex-grow transition-all duration-300 ${
            isHovered || isFocused 
              ? 'text-foreground/80 transform translate-x-1' 
              : 'text-foreground'
          }`}
          id={`notebook-${notebook.id}-title`}
        >
          {notebook.title}
        </h3>
        
        {/* Enhanced collaborators badge */}
        {notebook.hasCollaborators && (
          <div className="mb-3">
            <Badge 
              variant="secondary" 
              className={`text-xs transition-all duration-300 transform ${
                isHovered || isFocused 
                  ? 'bg-primary/20 text-primary border-primary/30 scale-105' 
                  : 'bg-primary/10 text-primary border-primary/20'
              }`}
              aria-label="Shared notebook"
            >
              <Users 
                className={`h-3 w-3 mr-1 transition-transform duration-300 ${
                  isHovered || isFocused ? 'scale-110' : 'scale-100'
                }`} 
                aria-hidden="true"
              />
              Shared
            </Badge>
          </div>
        )}
        
        {/* Enhanced metadata section with staggered animations */}
        <div className="mt-auto space-y-2" id={`notebook-${notebook.id}-details`}>
          <div className={`flex items-center text-sm transition-all duration-300 delay-75 ${
            isHovered || isFocused 
              ? 'text-muted-foreground/80 transform translate-x-1' 
              : 'text-muted-foreground'
          }`}>
            <Calendar 
              className={`h-4 w-4 mr-2 transition-all duration-300 ${
                isHovered || isFocused ? 'text-primary scale-110' : 'scale-100'
              }`} 
              aria-hidden="true"
            />
            <span>{notebook.date}</span>
          </div>
          <div className={`flex items-center text-sm transition-all duration-300 delay-100 ${
            isHovered || isFocused 
              ? 'text-muted-foreground transform translate-x-1'
                : 'text-muted-foreground'
          }`}>
            <FileText 
              className={`h-4 w-4 mr-2 transition-all duration-300 ${
                isHovered || isFocused ? 'text-primary scale-110' : 'scale-100'
              }`} 
              aria-hidden="true"
            />
            <span>{notebook.sources} source{notebook.sources !== 1 ? 's' : ''}</span>
          </div>
        </div>
      </div>
      
      {/* Enhanced bottom accent line with animation */}
      <div className={`absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-primary via-primary/80 to-primary transition-all duration-500 transform origin-left ${
        isHovered || isFocused 
          ? 'opacity-100 scale-x-100' 
          : 'opacity-0 scale-x-0'
      }`} />
      
      {/* Subtle glow effect */}
      <div className={`absolute inset-0 rounded-xl bg-gradient-to-br from-primary/5 to-primary/5 transition-opacity duration-500 ${
        isHovered || isFocused ? 'opacity-100' : 'opacity-0'
      }`} />
    </div>
  );
};

export default NotebookCard;
