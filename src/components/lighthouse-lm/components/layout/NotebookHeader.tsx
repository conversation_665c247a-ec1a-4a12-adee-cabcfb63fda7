import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowLef<PERSON>, Settings, Share2, Download, Trash2, BookOpen } from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { useNotebookDelete } from '../../hooks/useNotebookDelete';
import InsightsAPI from '../../../../services/insights-api';

interface NotebookHeaderProps {
  notebookId?: string;
  notebook?: any;
  onBack?: () => void;
}

const NotebookHeader: React.FC<NotebookHeaderProps> = ({
  notebookId,
  notebook,
  onBack,
}) => {
  const { deleteNotebook } = useNotebookDelete();

  const handleExport = async () => {
    if (!notebookId) return;
    
    try {
      const exportData = await InsightsAPI.exportNotebook(notebookId);
      
      const dataStr = JSON.stringify(exportData, null, 2);
      const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
      const exportFileDefaultName = `${notebook?.title.replace(/[^a-z0-9]/gi, '_').toLowerCase() || 'notebook'}_export.json`;
      
      const linkElement = document.createElement('a');
      linkElement.setAttribute('href', dataUri);
      linkElement.setAttribute('download', exportFileDefaultName);
      linkElement.click();
    } catch (error) {
      console.error('Failed to export notebook:', error);
    }
  };

  const handleDelete = () => {
    if (!notebookId) return;
    
    if (confirm('Are you sure you want to delete this notebook? This action cannot be undone.')) {
      deleteNotebook(notebookId);
    }
  };

  return (
    <header className="bg-background/95 backdrop-blur-sm border-b border-border/50 px-6 py-4 transition-all duration-300 hover:shadow-md">
      <div className="flex items-center justify-between max-w-7xl mx-auto">
        <div className="flex items-center space-x-6">
          <Button
            variant="ghost"
            size="sm"
            onClick={onBack}
            className="flex items-center space-x-3 text-muted-foreground hover:text-foreground transition-all duration-300 hover:scale-105 hover:bg-accent/80 active:scale-95 focus:ring-2 focus:ring-ring focus:ring-offset-2 group px-3 py-2 rounded-lg"
          >
            <ArrowLeft className="w-4 h-4 transition-all duration-300 group-hover:-translate-x-1 group-hover:scale-110" />
            <span className="font-medium transition-all duration-300 group-hover:tracking-wide">Back</span>
          </Button>
          
          <div className="flex items-center space-x-4 group cursor-pointer transition-all duration-300 hover:scale-105 px-3 py-2 rounded-xl hover:bg-accent/50">
            <div className="p-2 bg-primary/10 rounded-lg transition-all duration-300 group-hover:bg-primary/20 group-hover:scale-110">
              {notebook?.icon ? (
                <span className="text-xl transition-transform duration-300 group-hover:scale-110 group-hover:rotate-12">{notebook.icon}</span>
              ) : (
                <BookOpen className="w-5 h-5 text-primary transition-transform duration-300 group-hover:scale-110 group-hover:rotate-12" />
              )}
            </div>
            <div>
              <h1 className="text-xl font-semibold text-foreground transition-colors duration-300 group-hover:text-primary tracking-tight">
                {notebook?.title || 'Untitled Notebook'}
              </h1>
              <p className="text-sm text-muted-foreground transition-colors duration-300 group-hover:text-muted-foreground/80">
                Research & Writing Workspace
              </p>
            </div>
          </div>
        </div>
        
        <div className="flex items-center space-x-3">
          <Button 
            variant="ghost" 
            size="sm"
            className="transition-all duration-300 hover:scale-110 hover:bg-emerald-50 hover:text-emerald-600 focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 active:scale-95 group p-2.5 rounded-lg"
            aria-label="Share notebook"
          >
            <Share2 className="h-4 w-4 transition-transform duration-300 group-hover:scale-110 group-hover:rotate-12" />
          </Button>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button 
                variant="ghost" 
                size="sm"
                className="transition-all duration-300 hover:scale-110 hover:bg-violet-50 hover:text-violet-600 focus:ring-2 focus:ring-violet-500 focus:ring-offset-2 active:scale-95 group p-2.5 rounded-lg"
                aria-label="Notebook settings"
              >
                <Settings className="h-4 w-4 transition-transform duration-300 group-hover:rotate-90 group-hover:scale-110" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="transition-all duration-200 animate-in slide-in-from-top-2 shadow-lg border-border/50">
              <DropdownMenuItem 
                onClick={handleExport}
                className="transition-all duration-200 hover:bg-accent hover:text-primary focus:bg-accent focus:text-primary group px-3 py-2.5"
              >
                <Download className="h-4 w-4 mr-3 transition-transform duration-200 group-hover:scale-110 group-hover:-translate-y-0.5" />
                <span className="font-medium">Export Notebook</span>
              </DropdownMenuItem>
              <DropdownMenuSeparator className="bg-border/50" />
              <DropdownMenuItem 
                onClick={handleDelete}
                className="text-destructive focus:text-destructive transition-all duration-200 hover:bg-destructive/10 focus:bg-destructive/10 group px-3 py-2.5"
              >
                <Trash2 className="h-4 w-4 mr-3 transition-transform duration-200 group-hover:scale-110" />
                <span className="font-medium">Delete Notebook</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
};

export default NotebookHeader;