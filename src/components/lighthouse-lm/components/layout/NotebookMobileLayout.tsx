import React from 'react';
import NotebookHeader from './NotebookHeader';
import MobileNotebookTabs from '../notebook/MobileNotebookTabs';
import { Citation } from '../../types/message';

interface NotebookMobileLayoutProps {
  notebookId?: string;
  notebook: any;
  hasSource: boolean;
  selectedCitation: Citation | null;
  activeTab: string;
  onBack: () => void;
  onCitationClick: (citation: Citation) => void;
  onCitationClose: () => void;
  onTabChange: (tab: string) => void;
}

export const NotebookMobileLayout: React.FC<NotebookMobileLayoutProps> = ({
  notebookId,
  notebook,
  hasSource,
  selectedCitation,
  activeTab,
  onBack,
  onCitationClick,
  onCitationClose,
  onTabChange,
}) => {
  return (
    <div className="min-h-screen bg-background flex flex-col pb-4">
      <NotebookHeader 
        notebookId={notebookId}
        notebook={notebook}
        onBack={onBack}
      />
      
      <div className="flex-1 overflow-hidden">
        <MobileNotebookTabs
          notebookId={notebookId}
          notebook={notebook}
          hasSource={hasSource}
          selectedCitation={selectedCitation}
          onCitationClick={onCitationClick}
          onCitationClose={onCitationClose}
          activeTab={activeTab}
          onTabChange={onTabChange}
        />
      </div>
    </div>
  );
};

export default NotebookMobileLayout;