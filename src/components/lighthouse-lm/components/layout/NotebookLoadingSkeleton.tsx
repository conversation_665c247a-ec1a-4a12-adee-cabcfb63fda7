import React from 'react';

interface NotebookLoadingSkeletonProps {
  isDesktop: boolean;
}

export const NotebookLoadingSkeleton: React.FC<NotebookLoadingSkeletonProps> = ({ isDesktop }) => {
  // Mobile skeleton with enhanced visual hierarchy
  if (!isDesktop) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/10 flex flex-col animate-in fade-in-0 duration-700">
        {/* Enhanced Header skeleton */}
        <div className="bg-background/95 backdrop-blur-sm border-b border-border/50 px-4 py-4 shadow-sm">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-8 h-8 bg-gradient-to-br from-muted to-muted/60 rounded-lg animate-pulse shadow-sm"></div>
              <div className="space-y-2">
                <div className="h-6 bg-gradient-to-r from-muted to-muted/60 rounded-md w-40 animate-pulse"></div>
                <div className="h-3 bg-gradient-to-r from-muted/60 to-muted/40 rounded w-24 animate-pulse"></div>
              </div>
            </div>
            <div className="flex space-x-3">
              <div className="w-8 h-8 bg-gradient-to-br from-muted to-muted/60 rounded-lg animate-pulse"></div>
              <div className="w-8 h-8 bg-gradient-to-br from-muted to-muted/60 rounded-lg animate-pulse"></div>
            </div>
          </div>
        </div>
        
        {/* Enhanced Tabs skeleton */}
        <div className="bg-background/80 border-b border-border/30 px-4 shadow-sm">
          <div className="flex space-x-8 py-4">
            {['Sources', 'Chat', 'Studio'].map((tab, index) => (
              <div key={tab} className="flex items-center space-x-3" style={{ animationDelay: `${index * 100}ms` }}>
                <div className="w-5 h-5 bg-gradient-to-br from-primary/20 to-primary/10 rounded animate-pulse"></div>
                <div className="h-4 bg-gradient-to-r from-muted to-muted/60 rounded-md w-16 animate-pulse"></div>
              </div>
            ))}
          </div>
        </div>
        
        {/* Enhanced Content skeleton */}
        <div className="flex-1 p-6 space-y-6">
          {[...Array(4)].map((_, index) => (
            <div key={index} className="bg-card/80 backdrop-blur-sm border border-border/50 rounded-xl p-6 animate-pulse shadow-sm" style={{ animationDelay: `${index * 150}ms` }}>
              <div className="flex items-start space-x-4">
                <div className="w-10 h-10 bg-gradient-to-br from-primary/20 to-primary/10 rounded-full animate-pulse"></div>
                <div className="flex-1 space-y-3">
                  <div className="h-5 bg-gradient-to-r from-muted to-muted/60 rounded-md w-3/4 animate-pulse"></div>
                  <div className="h-4 bg-gradient-to-r from-muted/60 to-muted/40 rounded w-1/2 animate-pulse"></div>
                  <div className="h-3 bg-gradient-to-r from-muted/40 to-muted/20 rounded w-2/3 animate-pulse"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        {/* Enhanced Loading indicator */}
        <div className="text-center py-6 animate-in fade-in-0 duration-500" style={{ animationDelay: '800ms' }}>
          <div className="inline-flex items-center justify-center space-x-3 bg-card/90 backdrop-blur-sm rounded-full px-6 py-3 shadow-lg border border-border/50">
            <div className="flex space-x-1">
              <div className="w-2 h-2 bg-primary/80 rounded-full animate-bounce"></div>
              <div className="w-2 h-2 bg-primary/60 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
              <div className="w-2 h-2 bg-primary/40 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
            </div>
            <span className="text-sm font-medium text-foreground">Loading notebook...</span>
          </div>
        </div>
      </div>
    );
  }
  
  // Enhanced Desktop skeleton with improved visual hierarchy
  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/5 flex flex-col h-screen animate-in fade-in-0 duration-700">
      {/* Enhanced Header skeleton */}
      <div className="bg-background/95 backdrop-blur-sm border-b border-border/50 px-8 py-5 shadow-sm">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-5">
            <div className="w-9 h-9 bg-gradient-to-br from-primary/20 to-primary/10 rounded-xl animate-pulse shadow-sm"></div>
            <div className="space-y-2">
              <div className="h-7 bg-gradient-to-r from-muted to-muted/60 rounded-lg w-48 animate-pulse"></div>
              <div className="h-4 bg-gradient-to-r from-muted/60 to-muted/40 rounded-md w-32 animate-pulse"></div>
            </div>
          </div>
          <div className="flex space-x-4">
            <div className="w-24 h-9 bg-gradient-to-r from-muted to-muted/60 rounded-lg animate-pulse"></div>
            <div className="w-9 h-9 bg-gradient-to-br from-muted to-muted/60 rounded-xl animate-pulse shadow-sm"></div>
          </div>
        </div>
      </div>
      
      <div className="flex-1 overflow-hidden flex">
        {/* Enhanced Left Panel Skeleton - Sources */}
        <div className="w-1/5 min-w-[200px] max-w-[400px] border-r border-border/50 bg-card/30 backdrop-blur-sm p-6">
          <div className="space-y-6">
            <div className="h-7 bg-gradient-to-r from-muted to-muted/60 rounded-lg w-24 animate-pulse"></div>
            {[...Array(3)].map((_, index) => (
              <div key={index} className="bg-card/80 backdrop-blur-sm border border-border/50 rounded-xl p-4 animate-pulse shadow-sm" style={{ animationDelay: `${index * 100}ms` }}>
                <div className="flex items-center space-x-3 mb-3">
                  <div className="w-5 h-5 bg-gradient-to-br from-primary/20 to-primary/10 rounded animate-pulse"></div>
                  <div className="h-5 bg-gradient-to-r from-muted to-muted/60 rounded-md w-20 animate-pulse"></div>
                </div>
                <div className="space-y-3">
                  <div className="h-4 bg-gradient-to-r from-muted/60 to-muted/40 rounded w-full animate-pulse"></div>
                  <div className="h-3 bg-gradient-to-r from-muted/40 to-muted/20 rounded w-3/4 animate-pulse"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
        
        {/* Enhanced Middle Panel Skeleton - Chat */}
        <div className="flex-1 flex flex-col bg-background/20">
          <div className="flex-1 p-8 space-y-6">
            {[...Array(3)].map((_, index) => (
              <div key={index} className="flex items-start space-x-4 animate-pulse" style={{ animationDelay: `${index * 200}ms` }}>
                <div className="w-10 h-10 bg-gradient-to-br from-primary/20 to-primary/10 rounded-full animate-pulse shadow-sm"></div>
                <div className="flex-1 space-y-3">
                  <div className="h-5 bg-gradient-to-r from-muted to-muted/60 rounded-md w-3/4 animate-pulse"></div>
                  <div className="h-4 bg-gradient-to-r from-muted/60 to-muted/40 rounded w-1/2 animate-pulse"></div>
                </div>
              </div>
            ))}
          </div>
          
          {/* Enhanced Chat input skeleton */}
          <div className="p-8 border-t border-border/50 bg-card/30 backdrop-blur-sm">
            <div className="h-14 bg-gradient-to-r from-muted/60 to-muted/40 rounded-xl animate-pulse shadow-sm"></div>
          </div>
        </div>
        
        {/* Enhanced Right Panel Skeleton - Studio */}
        <div className="w-[30%] min-w-[250px] max-w-[500px] border-l border-border/50 bg-card/30 backdrop-blur-sm p-6">
          <div className="space-y-6">
            <div className="h-7 bg-gradient-to-r from-muted to-muted/60 rounded-lg w-20 animate-pulse"></div>
            {[...Array(2)].map((_, index) => (
              <div key={index} className="bg-card/80 backdrop-blur-sm border border-border/50 rounded-xl p-5 animate-pulse shadow-sm" style={{ animationDelay: `${index * 150}ms` }}>
                <div className="space-y-4">
                  <div className="h-5 bg-gradient-to-r from-muted to-muted/60 rounded-md w-full animate-pulse"></div>
                  <div className="h-4 bg-gradient-to-r from-muted/60 to-muted/40 rounded w-2/3 animate-pulse"></div>
                  <div className="h-24 bg-gradient-to-br from-muted/40 to-muted/20 rounded-lg animate-pulse"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
      
      {/* Enhanced Loading indicator */}
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 animate-in fade-in-0 duration-500" style={{ animationDelay: '1000ms' }}>
        <div className="bg-card/95 backdrop-blur-sm rounded-xl p-6 shadow-xl border border-border/50">
          <div className="flex items-center space-x-4 text-primary">
            <div className="flex space-x-1">
              <div className="w-2 h-2 bg-primary/80 rounded-full animate-bounce"></div>
              <div className="w-2 h-2 bg-primary/60 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
              <div className="w-2 h-2 bg-primary/40 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
            </div>
            <span className="text-sm font-medium text-foreground">Loading notebook...</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotebookLoadingSkeleton;