import React from 'react';
import { But<PERSON> } from '@/components/ui/button';

interface NotebookNotFoundProps {
  onBack: () => void;
}

export const NotebookNotFound: React.FC<NotebookNotFoundProps> = ({ onBack }) => {
  return (
    <div className="min-h-screen bg-background flex items-center justify-center transition-all duration-500">
      <div className="text-center group">
        <p className="text-muted-foreground mb-4 transition-all duration-200 group-hover:text-foreground cursor-default">
          Notebook not found
        </p>
        <Button 
          onClick={onBack}
          className="transition-all duration-200 hover:scale-105 hover:shadow-lg focus:ring-2 active:scale-95 group"
        >
          <span className="transition-all duration-200 group-hover:tracking-wide">Back to Dashboard</span>
        </Button>
      </div>
    </div>
  );
};

export default NotebookNotFound;