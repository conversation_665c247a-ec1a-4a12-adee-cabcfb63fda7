import React from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Copy, 
  Download, 
  ZoomIn, 
  ZoomOut, 
  Maximize2, 
  Minimize2, 
  Edit3, 
  Save, 
  X,
  FileImage,
  FileText,
  RefreshCw,
  ExternalLink,
  Info,
  Eye,
  Share2,
  MoreHorizontal
} from 'lucide-react';
import { toast } from 'sonner';
import { useMermaidDiagram } from '../../hooks/useMermaidDiagram';
import { getDiagramTypeLabel } from '../../utils/mermaidTypes';
import { SourceAttribution } from '../../types/sourceAnalysis';
import { SourceDiagram } from '../../../../services/sourceDiagramService';
import EnhancedExportDialog from '../diagrams/EnhancedExportDialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface MermaidProps {
  diagram: string;
  title?: string;
  mode?: 'view' | 'edit' | 'compact';
  height?: string;
  className?: string;
  showControls?: boolean;
  onRegenerate?: () => void;
  isGenerating?: boolean;
  onDiagramChange?: (diagram: string) => void;
  editable?: boolean;
  // Source interactivity props
  sourceMap?: Record<string, string[]>;
  sourceAttribution?: SourceAttribution[];
  onNodeClick?: (nodeId: string, sourceIds: string[]) => void;
  onSourceView?: (sourceId: string) => void;
  highlightedNodes?: string[];
  showSourceAttribution?: boolean;
  // Enhanced export props
  sourceDiagram?: SourceDiagram;
  enableEnhancedExport?: boolean;
}

/**
 * Main Mermaid component that handles all diagram rendering and interactions
 * Enhanced with source interactivity features
 */
const Mermaid: React.FC<MermaidProps> = ({
  diagram,
  title,
  mode = 'view',
  height = '500px',
  className = '',
  showControls = true,
  onRegenerate,
  isGenerating = false,
  onDiagramChange,
  editable = false,
  // Source interactivity props
  sourceMap = {},
  sourceAttribution = [],
  onNodeClick,
  onSourceView,
  highlightedNodes = [],
  showSourceAttribution = false,
  // Enhanced export props
  sourceDiagram,
  enableEnhancedExport = false,
}) => {
  const {
    containerRef,
    zoom,
    isEditing,
    error,
    localDiagram,
    handleCopy,
    handleExportCode,
    handleExportSVG,
    handleExportPNG,
    handleZoomIn,
    handleZoomOut,
    handleResetZoom,
    handleEdit,
    handleSaveEdit,
    handleCancelEdit,
    updateDiagram,
    setZoom,
    handleExportJSON,
    handleCreateShareableLink,
    hasEnhancedExport
  } = useMermaidDiagram(diagram, {
    onDiagramChange,
    autoRender: true,
    sourceMap,
    onNodeClick,
    highlightedNodes,
    sourceDiagram,
    enableEnhancedExport
  });

  const [isFullscreen, setIsFullscreen] = React.useState(false);
  const [contextMenu, setContextMenu] = React.useState<{
    x: number;
    y: number;
    nodeId: string;
    sourceIds: string[];
  } | null>(null);
  const [hoveredNode, setHoveredNode] = React.useState<string | null>(null);
  const [showEnhancedExportDialog, setShowEnhancedExportDialog] = React.useState(false);

  const toggleFullscreen = () => setIsFullscreen(!isFullscreen);
  const toggleEdit = () => handleEdit();
  const handleSave = () => handleSaveEdit();
  const handleCancel = () => handleCancelEdit();
  const handleCodeChange = (code: string) => updateDiagram(code);
  
  // Wrapper functions for export handlers
  const exportCode = () => handleExportCode(title);
  const exportSVG = () => handleExportSVG(title);
  const exportPNG = () => handleExportPNG(title);

  // Source interactivity handlers
  const handleNodeContextMenu = (event: React.MouseEvent, nodeId: string) => {
    event.preventDefault();
    const sourceIds = sourceMap[nodeId] || [];
    if (sourceIds.length > 0) {
      setContextMenu({
        x: event.clientX,
        y: event.clientY,
        nodeId,
        sourceIds
      });
    }
  };

  const handleContextMenuAction = (action: 'view' | 'highlight', sourceId?: string) => {
    if (!contextMenu) return;
    
    if (action === 'view' && sourceId && onSourceView) {
      onSourceView(sourceId);
    } else if (action === 'highlight' && onNodeClick) {
      onNodeClick(contextMenu.nodeId, contextMenu.sourceIds);
    }
    
    setContextMenu(null);
  };

  const closeContextMenu = () => setContextMenu(null);

  // Close context menu on click outside
  React.useEffect(() => {
    const handleClickOutside = () => setContextMenu(null);
    if (contextMenu) {
      document.addEventListener('click', handleClickOutside);
      return () => document.removeEventListener('click', handleClickOutside);
    }
  }, [contextMenu]);

  // Get source attribution for a node
  const getNodeAttribution = (nodeId: string): SourceAttribution[] => {
    return sourceAttribution.filter(attr => attr.node_id === nodeId);
  };

  if (mode === 'compact') {
    return (
      <div className={`mermaid-compact ${className}`}>
        <div 
          ref={containerRef}
          className="mermaid-container"
          style={{ 
            height: height,
            overflow: 'auto',
            border: '1px solid #e2e8f0',
            borderRadius: '6px',
            padding: '8px'
          }}
        />
        {error && (
          <div className="text-red-500 text-sm mt-2 p-2 bg-red-50 rounded">
            {error}
          </div>
        )}
      </div>
    );
  }

  return (
    <Card className={`mermaid-card ${className} ${isFullscreen ? 'fixed inset-0 z-50' : ''}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <CardTitle className="text-lg">
              {title || 'Mermaid Diagram'}
            </CardTitle>
            <Badge variant="secondary" className="text-xs">
              {getDiagramTypeLabel('flowchart')}
            </Badge>
          </div>
          
          {showControls && (
            <div className="flex items-center gap-1">
              {/* Zoom Controls */}
              <Button
                variant="ghost"
                size="sm"
                onClick={handleZoomOut}
                disabled={zoom <= 25}
                title="Zoom Out"
              >
                <ZoomOut className="h-4 w-4" />
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={handleResetZoom}
                title={`Reset Zoom (${zoom}%)`}
                className="min-w-[60px] text-xs"
              >
                {zoom}%
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={handleZoomIn}
                disabled={zoom >= 200}
                title="Zoom In"
              >
                <ZoomIn className="h-4 w-4" />
              </Button>
              
              <Separator orientation="vertical" className="h-6 mx-1" />
              
              {/* Action Controls */}
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCopy}
                title="Copy Code"
              >
                <Copy className="h-4 w-4" />
              </Button>
              
              {hasEnhancedExport ? (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      title="Export Options"
                    >
                      <Download className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>Quick Export</DropdownMenuLabel>
                    <DropdownMenuItem onClick={exportCode}>
                      <FileText className="h-4 w-4 mr-2" />
                      Mermaid Code
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={exportSVG}>
                      <Download className="h-4 w-4 mr-2" />
                      SVG (Basic)
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={exportPNG}>
                      <FileImage className="h-4 w-4 mr-2" />
                      PNG (Basic)
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuLabel>Enhanced Export</DropdownMenuLabel>
                    <DropdownMenuItem onClick={() => setShowEnhancedExportDialog(true)}>
                      <MoreHorizontal className="h-4 w-4 mr-2" />
                      Advanced Options...
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleCreateShareableLink()}>
                      <Share2 className="h-4 w-4 mr-2" />
                      Create Share Link
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              ) : (
                <>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={exportCode}
                    title="Download as .mmd"
                  >
                    <FileText className="h-4 w-4" />
                  </Button>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={exportSVG}
                    title="Export as SVG"
                  >
                    <Download className="h-4 w-4" />
                  </Button>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={exportPNG}
                    title="Export as PNG"
                  >
                    <FileImage className="h-4 w-4" />
                  </Button>
                </>
              )}
              
              {onRegenerate && (
                <>
                  <Separator orientation="vertical" className="h-6 mx-1" />
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={onRegenerate}
                    disabled={isGenerating}
                    title="Regenerate Diagram"
                  >
                    <RefreshCw className={`h-4 w-4 ${isGenerating ? 'animate-spin' : ''}`} />
                  </Button>
                </>
              )}
              
              {editable && (
                <>
                  <Separator orientation="vertical" className="h-6 mx-1" />
                  {isEditing ? (
                    <>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleSave}
                        title="Save Changes"
                      >
                        <Save className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleCancel}
                        title="Cancel Edit"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </>
                  ) : (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={toggleEdit}
                      title="Edit Diagram"
                    >
                      <Edit3 className="h-4 w-4" />
                    </Button>
                  )}
                </>
              )}
              
              <Separator orientation="vertical" className="h-6 mx-1" />
              
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleFullscreen}
                title={isFullscreen ? 'Exit Fullscreen' : 'Fullscreen'}
              >
                {isFullscreen ? (
                  <Minimize2 className="h-4 w-4" />
                ) : (
                  <Maximize2 className="h-4 w-4" />
                )}
              </Button>
            </div>
          )}
        </div>
      </CardHeader>
      
      <CardContent className="p-0">
        {isEditing ? (
          <div className="p-4">
            <Textarea
              value={localDiagram}
              onChange={(e) => handleCodeChange(e.target.value)}
              className="font-mono text-sm min-h-[300px] resize-none"
              placeholder="Enter Mermaid diagram code..."
            />
          </div>
        ) : (
          <div className="relative">
            <div 
              ref={containerRef}
              className="mermaid-container p-4 overflow-auto"
              style={{ 
                height: isFullscreen ? 'calc(100vh - 120px)' : height,
                minHeight: '200px'
              }}
            />
            
            {/* Source Attribution Panel */}
            {showSourceAttribution && sourceAttribution.length > 0 && (
              <div className="absolute top-4 right-4 bg-white/95 backdrop-blur-sm border rounded-lg p-3 max-w-xs shadow-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Info className="h-4 w-4 text-blue-500" />
                  <span className="text-sm font-medium">Source Attribution</span>
                </div>
                <div className="space-y-2 max-h-32 overflow-y-auto">
                  {sourceAttribution.slice(0, 5).map((attr, index) => (
                    <div key={index} className="text-xs">
                      <div className="font-medium text-gray-700">Node: {attr.node_id}</div>
                      <div className="text-gray-500 truncate">{attr.content_excerpt}</div>
                      <div className="flex items-center gap-1 mt-1">
                        <Badge variant="outline" className="text-[10px] px-1">
                          {Math.round(attr.confidence * 100)}%
                        </Badge>
                        {onSourceView && (
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-5 w-5 p-0"
                            onClick={() => onSourceView(attr.source_id)}
                            title="View source"
                          >
                            <Eye className="h-3 w-3" />
                          </Button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Context Menu */}
            {contextMenu && (
              <div
                className="fixed bg-white border rounded-lg shadow-lg py-1 z-50"
                style={{
                  left: contextMenu.x,
                  top: contextMenu.y,
                }}
                onClick={(e) => e.stopPropagation()}
              >
                <div className="px-3 py-1 text-xs font-medium text-gray-500 border-b">
                  Node: {contextMenu.nodeId}
                </div>
                {contextMenu.sourceIds.map((sourceId) => (
                  <button
                    key={sourceId}
                    className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2"
                    onClick={() => handleContextMenuAction('view', sourceId)}
                  >
                    <ExternalLink className="h-3 w-3" />
                    View Source {sourceId.slice(0, 8)}...
                  </button>
                ))}
                <button
                  className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2 border-t"
                  onClick={() => handleContextMenuAction('highlight')}
                >
                  <Eye className="h-3 w-3" />
                  Highlight Related
                </button>
              </div>
            )}
            
            {error && (
              <div className="absolute inset-0 flex items-center justify-center bg-red-50 m-4 rounded">
                <div className="text-center text-red-600">
                  <div className="text-lg font-medium mb-2">Diagram Error</div>
                  <div className="text-sm">{error}</div>
                </div>
              </div>
            )}
            
            {isGenerating && (
              <div className="absolute inset-0 flex items-center justify-center bg-white/80 m-4 rounded">
                <div className="text-center">
                  <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2 text-blue-500" />
                  <div className="text-sm text-gray-600">Generating diagram...</div>
                </div>
              </div>
            )}
          </div>
        )}
      </CardContent>

      {/* Enhanced Export Dialog */}
      {hasEnhancedExport && sourceDiagram && (
        <EnhancedExportDialog
          isOpen={showEnhancedExportDialog}
          onOpenChange={setShowEnhancedExportDialog}
          diagram={sourceDiagram}
          onExportComplete={(format) => {
            toast(`Diagram exported as ${format.toUpperCase()}`);
          }}
        />
      )}
    </Card>
  );
};

export default Mermaid;