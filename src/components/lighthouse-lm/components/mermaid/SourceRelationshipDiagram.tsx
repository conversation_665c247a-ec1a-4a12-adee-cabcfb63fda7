import React, { useState, useEffect, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Separator } from '@/components/ui/separator';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { 
  Network, 
  Filter, 
  RefreshCw, 
  Eye, 
  EyeOff, 
  Info,
  Download,
  Settings,
  Maximize2,
  Minimize2
} from 'lucide-react';
import { toast } from 'sonner';
import { Source } from '../../../../hooks/useSources';
import { MermaidSourceAnalyzer } from '../../../../services/mermaidSourceAnalyzer';
import { 
  MultiSourceAnalysisResult, 
  CrossSourceRelationship, 
  RelationshipType 
} from '../../types/sourceAnalysis';
import Mermaid from './Mermaid';

export interface SourceRelationshipDiagramProps {
  sources: Source[];
  relationshipType: 'similarity' | 'references' | 'topics' | 'all';
  onSourceSelect: (source: Source) => void;
  showMetrics?: boolean;
  className?: string;
  height?: string;
}

interface RelationshipMetrics {
  totalRelationships: number;
  averageStrength: number;
  strongestRelationship: CrossSourceRelationship | null;
  weakestRelationship: CrossSourceRelationship | null;
  relationshipTypes: Record<RelationshipType, number>;
}

interface FilterOptions {
  minStrength: number;
  maxSources: number;
  relationshipTypes: RelationshipType[];
  showIsolatedSources: boolean;
}

/**
 * SourceRelationshipDiagram - Visualizes relationships between sources
 * Implements similarity analysis, interactive filtering, and metrics display
 */
const SourceRelationshipDiagram: React.FC<SourceRelationshipDiagramProps> = ({
  sources,
  relationshipType,
  onSourceSelect,
  showMetrics = true,
  className = '',
  height = '600px'
}) => {
  const [analysisResult, setAnalysisResult] = useState<MultiSourceAnalysisResult | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  
  // Filter state
  const [filters, setFilters] = useState<FilterOptions>({
    minStrength: 0.1,
    maxSources: 20,
    relationshipTypes: ['similar_to', 'references', 'contains', 'depends_on'],
    showIsolatedSources: true
  });

  // Analyze sources when they change
  useEffect(() => {
    if (sources.length > 1) {
      analyzeSources();
    } else {
      setAnalysisResult(null);
      setError(null);
    }
  }, [sources]);

  const analyzeSources = async () => {
    if (sources.length < 2) {
      setError('At least 2 sources are required for relationship analysis');
      return;
    }

    setIsAnalyzing(true);
    setError(null);

    try {
      const result = await MermaidSourceAnalyzer.analyzeMultipleSources(sources);
      setAnalysisResult(result);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to analyze sources';
      setError(errorMessage);
      toast.error('Analysis failed', {
        description: errorMessage
      });
    } finally {
      setIsAnalyzing(false);
    }
  };

  // Calculate metrics from analysis result
  const metrics = useMemo((): RelationshipMetrics | null => {
    if (!analysisResult) return null;

    const relationships = analysisResult.cross_source_relationships;
    
    if (relationships.length === 0) {
      return {
        totalRelationships: 0,
        averageStrength: 0,
        strongestRelationship: null,
        weakestRelationship: null,
        relationshipTypes: {} as Record<RelationshipType, number>
      };
    }

    const averageStrength = relationships.reduce((sum, rel) => sum + rel.strength, 0) / relationships.length;
    const strongestRelationship = relationships.reduce((strongest, current) => 
      current.strength > strongest.strength ? current : strongest
    );
    const weakestRelationship = relationships.reduce((weakest, current) => 
      current.strength < weakest.strength ? current : weakest
    );

    const relationshipTypes = relationships.reduce((types, rel) => {
      types[rel.relationship_type] = (types[rel.relationship_type] || 0) + 1;
      return types;
    }, {} as Record<RelationshipType, number>);

    return {
      totalRelationships: relationships.length,
      averageStrength,
      strongestRelationship,
      weakestRelationship,
      relationshipTypes
    };
  }, [analysisResult]);

  // Filter relationships based on current filters and relationship type
  const filteredRelationships = useMemo(() => {
    if (!analysisResult) return [];

    let relationships = analysisResult.cross_source_relationships;

    // Filter by relationship type
    if (relationshipType !== 'all') {
      relationships = relationships.filter(rel => {
        switch (relationshipType) {
          case 'similarity':
            return rel.relationship_type === 'similar_to';
          case 'references':
            return rel.relationship_type === 'references';
          case 'topics':
            return rel.relationship_type === 'contains' || rel.relationship_type === 'part_of';
          default:
            return true;
        }
      });
    }

    // Apply filters
    relationships = relationships.filter(rel => {
      return rel.strength >= filters.minStrength &&
             filters.relationshipTypes.includes(rel.relationship_type);
    });

    return relationships;
  }, [analysisResult, relationshipType, filters]);

  // Helper functions for diagram generation
  const generateEmptyDiagram = (): string => {
    if (sources.length === 0) {
      return `graph TD
        A[No sources selected]
        style A fill:#f9f9f9,stroke:#ddd`;
    }

    if (sources.length === 1) {
      const source = sources[0];
      return `graph TD
        A["${source.title || source.display_name || 'Source'}"]
        style A fill:#e3f2fd,stroke:#1976d2`;
    }

    return `graph TD
      A[No relationships found]
      B[Try adjusting filters]
      A --> B
      style A fill:#fff3e0,stroke:#f57c00
      style B fill:#f3e5f5,stroke:#7b1fa2`;
  };

  const generateRelationshipDiagram = (
    relationships: CrossSourceRelationship[], 
    sources: Source[], 
    filters: FilterOptions
  ): string => {
    const sourceMap = new Map(sources.map(s => [s.id, s]));
    const nodes = new Set<string>();
    const edges: string[] = [];
    const nodeStyles: string[] = [];

    // Add nodes and edges from relationships
    relationships.forEach(rel => {
      const sourceA = sourceMap.get(rel.source_a);
      const sourceB = sourceMap.get(rel.source_b);
      
      if (sourceA && sourceB) {
        const nodeA = `source_${rel.source_a.slice(0, 8)}`;
        const nodeB = `source_${rel.source_b.slice(0, 8)}`;
        
        nodes.add(nodeA);
        nodes.add(nodeB);

        // Create edge with relationship info
        const strength = Math.round(rel.strength * 100);
        const edgeLabel = `${rel.relationship_type}|${strength}%`;
        const edgeStyle = getEdgeStyle(rel.relationship_type, rel.strength);
        
        edges.push(`${nodeA} ${edgeStyle} ${nodeB}`);
        
        // Add node labels
        if (!nodeStyles.find(style => style.includes(nodeA))) {
          nodeStyles.push(`${nodeA}["${sourceA.title || sourceA.display_name || 'Source'}"]`);
        }
        if (!nodeStyles.find(style => style.includes(nodeB))) {
          nodeStyles.push(`${nodeB}["${sourceB.title || sourceB.display_name || 'Source'}"]`);
        }
      }
    });

    // Add isolated sources if enabled
    if (filters.showIsolatedSources) {
      sources.forEach(source => {
        const nodeId = `source_${source.id.slice(0, 8)}`;
        if (!nodes.has(nodeId)) {
          nodes.add(nodeId);
          nodeStyles.push(`${nodeId}["${source.title || source.display_name || 'Source'}"]`);
        }
      });
    }

    // Generate diagram
    const diagramLines = [
      'graph TD',
      ...nodeStyles,
      ...edges,
      ...generateNodeStyles(Array.from(nodes), relationships)
    ];

    return diagramLines.join('\n    ');
  };

  const getEdgeStyle = (relationshipType: RelationshipType, strength: number): string => {
    const thickness = strength > 0.7 ? '===' : strength > 0.4 ? '--' : '-.';
    
    switch (relationshipType) {
      case 'similar_to':
        return `${thickness}>`;
      case 'references':
        return `${thickness}->`;
      case 'contains':
        return `${thickness}o`;
      case 'depends_on':
        return `${thickness}x`;
      default:
        return `${thickness}>`;
    }
  };

  const generateNodeStyles = (nodes: string[], relationships: CrossSourceRelationship[]): string[] => {
    const styles: string[] = [];
    
    nodes.forEach(nodeId => {
      const sourceId = nodeId.replace('source_', '');
      const relCount = relationships.filter(rel => 
        rel.source_a.startsWith(sourceId) || rel.source_b.startsWith(sourceId)
      ).length;
      
      let color = '#e3f2fd'; // Default blue
      if (relCount === 0) {
        color = '#f5f5f5'; // Gray for isolated
      } else if (relCount > 3) {
        color = '#e8f5e8'; // Green for highly connected
      } else if (relCount > 1) {
        color = '#fff3e0'; // Orange for moderately connected
      }
      
      styles.push(`style ${nodeId} fill:${color},stroke:#666`);
    });

    return styles;
  };

  // Generate Mermaid diagram from filtered relationships
  const diagramCode = useMemo(() => {
    if (!analysisResult || filteredRelationships.length === 0) {
      return generateEmptyDiagram();
    }

    return generateRelationshipDiagram(filteredRelationships, sources, filters);
  }, [filteredRelationships, sources, filters]);

  // Generate source map for interactivity
  const sourceMap = useMemo(() => {
    const map: Record<string, string[]> = {};
    
    sources.forEach(source => {
      const nodeId = `source_${source.id.slice(0, 8)}`;
      map[nodeId] = [source.id];
    });

    return map;
  }, [sources]);

  const handleNodeClick = (nodeId: string, sourceIds: string[]) => {
    if (sourceIds.length > 0) {
      const source = sources.find(s => s.id === sourceIds[0]);
      if (source) {
        onSourceSelect(source);
      }
    }
  };

  const handleExportDiagram = () => {
    const blob = new Blob([diagramCode], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'source-relationships.mmd';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    toast.success('Diagram exported successfully');
  };

  const updateFilter = <K extends keyof FilterOptions>(key: K, value: FilterOptions[K]) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  if (sources.length === 0) {
    return (
      <Card className={`${className}`}>
        <CardContent className="flex items-center justify-center h-64">
          <div className="text-center text-gray-500">
            <Network className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>Select sources to visualize relationships</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (sources.length === 1) {
    return (
      <Card className={`${className}`}>
        <CardContent className="flex items-center justify-center h-64">
          <div className="text-center text-gray-500">
            <Network className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>At least 2 sources are required for relationship analysis</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`${className} ${isFullscreen ? 'fixed inset-0 z-50 bg-white' : ''}`}>
      <Card className="h-full">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <CardTitle className="text-lg flex items-center gap-2">
                <Network className="h-5 w-5" />
                Source Relationships
              </CardTitle>
              <Badge variant="secondary" className="text-xs">
                {sources.length} sources
              </Badge>
              {metrics && (
                <Badge variant="outline" className="text-xs">
                  {metrics.totalRelationships} relationships
                </Badge>
              )}
            </div>
            
            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowSettings(!showSettings)}
                title="Filter Settings"
              >
                <Settings className="h-4 w-4" />
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={analyzeSources}
                disabled={isAnalyzing}
                title="Refresh Analysis"
              >
                <RefreshCw className={`h-4 w-4 ${isAnalyzing ? 'animate-spin' : ''}`} />
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={handleExportDiagram}
                title="Export Diagram"
              >
                <Download className="h-4 w-4" />
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsFullscreen(!isFullscreen)}
                title={isFullscreen ? 'Exit Fullscreen' : 'Fullscreen'}
              >
                {isFullscreen ? (
                  <Minimize2 className="h-4 w-4" />
                ) : (
                  <Maximize2 className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>

          {/* Filter Settings Panel */}
          {showSettings && (
            <div className="mt-4 p-4 bg-gray-50 rounded-lg space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">
                    Minimum Relationship Strength: {Math.round(filters.minStrength * 100)}%
                  </label>
                  <Slider
                    value={[filters.minStrength]}
                    onValueChange={([value]) => updateFilter('minStrength', value)}
                    min={0}
                    max={1}
                    step={0.1}
                    className="w-full"
                  />
                </div>
                
                <div>
                  <label className="text-sm font-medium mb-2 block">
                    Max Sources: {filters.maxSources}
                  </label>
                  <Slider
                    value={[filters.maxSources]}
                    onValueChange={([value]) => updateFilter('maxSources', value)}
                    min={5}
                    max={50}
                    step={5}
                    className="w-full"
                  />
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <Button
                  variant={filters.showIsolatedSources ? "default" : "outline"}
                  size="sm"
                  onClick={() => updateFilter('showIsolatedSources', !filters.showIsolatedSources)}
                >
                  {filters.showIsolatedSources ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
                  Show Isolated Sources
                </Button>
              </div>
            </div>
          )}

          {/* Metrics Panel */}
          {showMetrics && metrics && (
            <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="text-center p-2 bg-blue-50 rounded">
                      <div className="text-2xl font-bold text-blue-600">
                        {metrics.totalRelationships}
                      </div>
                      <div className="text-xs text-gray-600">Relationships</div>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Total number of relationships found between sources</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="text-center p-2 bg-green-50 rounded">
                      <div className="text-2xl font-bold text-green-600">
                        {Math.round(metrics.averageStrength * 100)}%
                      </div>
                      <div className="text-xs text-gray-600">Avg Strength</div>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Average relationship strength across all connections</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="text-center p-2 bg-orange-50 rounded">
                      <div className="text-2xl font-bold text-orange-600">
                        {metrics.strongestRelationship ? 
                          Math.round(metrics.strongestRelationship.strength * 100) : 0}%
                      </div>
                      <div className="text-xs text-gray-600">Strongest</div>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Strength of the strongest relationship found</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="text-center p-2 bg-purple-50 rounded">
                      <div className="text-2xl font-bold text-purple-600">
                        {Object.keys(metrics.relationshipTypes).length}
                      </div>
                      <div className="text-xs text-gray-600">Types</div>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Number of different relationship types found</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          )}
        </CardHeader>

        <CardContent className="p-0">
          {error ? (
            <div className="flex items-center justify-center h-64 text-red-500">
              <div className="text-center">
                <Info className="h-8 w-8 mx-auto mb-2" />
                <p>{error}</p>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={analyzeSources}
                  className="mt-2"
                >
                  Retry Analysis
                </Button>
              </div>
            </div>
          ) : (
            <Mermaid
              diagram={diagramCode}
              title="Source Relationships"
              mode="view"
              height={isFullscreen ? 'calc(100vh - 200px)' : height}
              showControls={true}
              isGenerating={isAnalyzing}
              sourceMap={sourceMap}
              onNodeClick={handleNodeClick}
              onRegenerate={analyzeSources}
            />
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default SourceRelationshipDiagram;