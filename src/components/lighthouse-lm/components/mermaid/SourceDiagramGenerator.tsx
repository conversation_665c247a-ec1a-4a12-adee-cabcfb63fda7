import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Loader2, Eye, Download, Save } from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Source } from '@/hooks/useSources';
import { DiagramType, DiagramGenerationOptions } from '../../../../services/sourceDiagramService';
import { MermaidSourceAnalyzer } from '../../../../services/mermaidSourceAnalyzer';
import { OptimizedMermaidSourceAnalyzer, OptimizedAnalysisOptions } from '../../../../services/optimizedMermaidSourceAnalyzer';
import { SourceDiagramService } from '../../../../services/sourceDiagramService';

interface SourceDiagramGeneratorProps {
  sources: Source[];
  onDiagramGenerated: (diagram: any) => void;
  onError: (error: string) => void;
  defaultDiagramType?: DiagramType;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  notebookId?: string;
}

const SourceDiagramGenerator: React.FC<SourceDiagramGeneratorProps> = ({
  sources,
  onDiagramGenerated,
  onError,
  defaultDiagramType = 'flowchart',
  isOpen,
  onOpenChange,
  notebookId
}) => {
  const [diagramType, setDiagramType] = useState<DiagramType>(defaultDiagramType);
  const [maxConcepts, setMaxConcepts] = useState([20]);
  const [relationshipDepth, setRelationshipDepth] = useState([2]);
  const [includeMetadata, setIncludeMetadata] = useState(true);
  const [filterByRelevance, setFilterByRelevance] = useState(true);
  const [minimumConfidence, setMinimumConfidence] = useState([0.5]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [generatedDiagram, setGeneratedDiagram] = useState<string | null>(null);
  const [analysisResult, setAnalysisResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [useOptimizedAnalysis, setUseOptimizedAnalysis] = useState(true);
  const [analysisProgress, setAnalysisProgress] = useState({ progress: 0, stage: '' });
  const [abortController, setAbortController] = useState<AbortController | null>(null);

  // Reset state when dialog opens/closes
  useEffect(() => {
    if (!isOpen) {
      setGeneratedDiagram(null);
      setAnalysisResult(null);
      setError(null);
      setIsGenerating(false);
      setIsSaving(false);
    }
  }, [isOpen]);

  const generateDiagram = async () => {
    if (sources.length === 0) {
      setError('No sources selected for diagram generation');
      return;
    }

    setIsGenerating(true);
    setError(null);
    setAnalysisProgress({ progress: 0, stage: 'Starting analysis...' });

    // Create abort controller for cancellation
    const controller = new AbortController();
    setAbortController(controller);

    try {
      let analysis;
      
      if (useOptimizedAnalysis && sources.length > 5) {
        // Use optimized analyzer for large source sets
        const options: OptimizedAnalysisOptions = {
          enableCaching: true,
          enableChunking: sources.length > 10,
          maxChunkSize: Math.min(10, Math.ceil(sources.length / 3)),
          enableMemoryOptimization: true,
          onProgress: (progress, stage) => {
            setAnalysisProgress({ progress, stage });
          },
          onChunkComplete: (chunkIndex, totalChunks) => {
            console.log(`Completed chunk ${chunkIndex + 1}/${totalChunks}`);
          },
          abortSignal: controller.signal
        };
        
        analysis = await OptimizedMermaidSourceAnalyzer.analyzeMultipleSourcesOptimized(sources, options);
        
        // Log performance metrics
        console.log('Analysis performance:', analysis.performance_metadata);
      } else {
        // Use standard analyzer for smaller sets
        setAnalysisProgress({ progress: 0.5, stage: 'Analyzing sources...' });
        analysis = await MermaidSourceAnalyzer.analyzeMultipleSources(sources);
      }
      
      setAnalysisResult(analysis);
      setAnalysisProgress({ progress: 0.9, stage: 'Generating diagram...' });

      // Generate diagram based on analysis
      const diagramContent = generateMermaidFromAnalysis(analysis);
      setGeneratedDiagram(diagramContent);
      
      setAnalysisProgress({ progress: 1, stage: 'Complete' });

    } catch (err) {
      if (err instanceof Error && err.message === 'Analysis aborted') {
        setError('Analysis was cancelled');
      } else {
        const errorMessage = err instanceof Error ? err.message : 'Failed to generate diagram';
        setError(errorMessage);
        onError(errorMessage);
      }
    } finally {
      setIsGenerating(false);
      setAbortController(null);
      setAnalysisProgress({ progress: 0, stage: '' });
    }
  };

  const cancelGeneration = () => {
    if (abortController) {
      abortController.abort();
      setAbortController(null);
    }
  };

  const generateMermaidFromAnalysis = (analysis: any): string => {
    const { sources: sourceAnalyses, concept_clusters, cross_source_relationships } = analysis;
    
    switch (diagramType) {
      case 'flowchart':
        return generateFlowchart(sourceAnalyses, concept_clusters);
      case 'mindmap':
        return generateMindmap(sourceAnalyses, concept_clusters);
      case 'sequence':
        return generateSequenceDiagram(sourceAnalyses);
      default:
        return generateFlowchart(sourceAnalyses, concept_clusters);
    }
  };

  const generateFlowchart = (sourceAnalyses: any[], conceptClusters: any[]): string => {
    let mermaid = 'flowchart TD\n';
    const nodeMap = new Map<string, string>();
    let nodeCounter = 0;

    // Add concept nodes
    sourceAnalyses.forEach(analysis => {
      analysis.concepts
        .filter((concept: any) => concept.confidence >= minimumConfidence[0])
        .slice(0, Math.floor(maxConcepts[0] / sourceAnalyses.length))
        .forEach((concept: any) => {
          const nodeId = `N${nodeCounter++}`;
          nodeMap.set(concept.id, nodeId);
          const cleanText = concept.text.replace(/[^\w\s]/g, '').trim();
          mermaid += `    ${nodeId}["${cleanText}"]\n`;
        });
    });

    // Add relationships
    sourceAnalyses.forEach(analysis => {
      analysis.relationships
        .filter((rel: any) => 
          nodeMap.has(rel.from_concept) && 
          nodeMap.has(rel.to_concept) &&
          rel.confidence >= minimumConfidence[0]
        )
        .forEach((rel: any) => {
          const fromNode = nodeMap.get(rel.from_concept);
          const toNode = nodeMap.get(rel.to_concept);
          mermaid += `    ${fromNode} --> ${toNode}\n`;
        });
    });

    return mermaid;
  };

  const generateMindmap = (sourceAnalyses: any[], conceptClusters: any[]): string => {
    let mermaid = 'mindmap\n  root((Sources))\n';
    
    sourceAnalyses.forEach((analysis, index) => {
      const sourceTitle = sources[index]?.title || `Source ${index + 1}`;
      mermaid += `    ${sourceTitle}\n`;
      
      analysis.concepts
        .filter((concept: any) => concept.confidence >= minimumConfidence[0])
        .slice(0, 5)
        .forEach((concept: any) => {
          const cleanText = concept.text.replace(/[^\w\s]/g, '').trim();
          mermaid += `      ${cleanText}\n`;
        });
    });

    return mermaid;
  };

  const generateSequenceDiagram = (sourceAnalyses: any[]): string => {
    let mermaid = 'sequenceDiagram\n';
    const participants = new Set<string>();
    
    // Extract participants from concepts
    sourceAnalyses.forEach(analysis => {
      analysis.concepts
        .filter((concept: any) => concept.type === 'entity' && concept.confidence >= minimumConfidence[0])
        .slice(0, 5)
        .forEach((concept: any) => {
          const cleanText = concept.text.replace(/[^\w\s]/g, '').replace(/\s+/g, '');
          participants.add(cleanText);
        });
    });

    // Add participants
    Array.from(participants).forEach(participant => {
      mermaid += `    participant ${participant}\n`;
    });

    // Add interactions based on relationships
    sourceAnalyses.forEach(analysis => {
      analysis.relationships
        .filter((rel: any) => rel.confidence >= minimumConfidence[0])
        .slice(0, 10)
        .forEach((rel: any) => {
          const fromConcept = analysis.concepts.find((c: any) => c.id === rel.from_concept);
          const toConcept = analysis.concepts.find((c: any) => c.id === rel.to_concept);
          
          if (fromConcept && toConcept) {
            const from = fromConcept.text.replace(/[^\w\s]/g, '').replace(/\s+/g, '');
            const to = toConcept.text.replace(/[^\w\s]/g, '').replace(/\s+/g, '');
            
            if (participants.has(from) && participants.has(to)) {
              mermaid += `    ${from}->>+${to}: ${rel.relationship_type}\n`;
            }
          }
        });
    });

    return mermaid;
  };

  const saveDiagram = async () => {
    if (!generatedDiagram || !analysisResult) {
      setError('No diagram to save');
      return;
    }

    setIsSaving(true);
    setError(null);

    try {
      const options: DiagramGenerationOptions = {
        diagram_type: diagramType,
        max_concepts: maxConcepts[0],
        relationship_depth: relationshipDepth[0],
        include_metadata: includeMetadata,
        filter_by_relevance: filterByRelevance,
        minimum_confidence: minimumConfidence[0]
      };

      const metadata = {
        source_ids: sources.map(s => s.id),
        generation_options: options,
        analysis_metadata: {
          concepts_extracted: analysisResult.global_metadata.total_concepts,
          relationships_found: analysisResult.global_metadata.total_relationships,
          analysis_confidence: analysisResult.global_metadata.average_confidence
        },
        interactive_metadata: {
          node_source_map: {},
          clickable_nodes: [],
          source_attribution: []
        }
      };

      const diagramId = await SourceDiagramService.saveSourceDiagram(
        notebookId,
        diagramType,
        generatedDiagram,
        sources.map(s => s.id),
        metadata
      );

      onDiagramGenerated({ id: diagramId, content: generatedDiagram, metadata });
      onOpenChange(false);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to save diagram';
      setError(errorMessage);
      onError(errorMessage);
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>Generate Diagram from Sources</DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-hidden">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 h-full">
            {/* Configuration Panel */}
            <div className="space-y-6">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="diagram-type">Diagram Type</Label>
                  <Select value={diagramType} onValueChange={(value) => setDiagramType(value as DiagramType)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select diagram type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="flowchart">Flowchart</SelectItem>
                      <SelectItem value="mindmap">Mind Map</SelectItem>
                      <SelectItem value="sequence">Sequence Diagram</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Max Concepts: {maxConcepts[0]}</Label>
                  <Slider
                    value={maxConcepts}
                    onValueChange={setMaxConcepts}
                    max={50}
                    min={5}
                    step={5}
                    className="mt-2"
                  />
                </div>

                <div>
                  <Label>Relationship Depth: {relationshipDepth[0]}</Label>
                  <Slider
                    value={relationshipDepth}
                    onValueChange={setRelationshipDepth}
                    max={5}
                    min={1}
                    step={1}
                    className="mt-2"
                  />
                </div>

                <div>
                  <Label>Minimum Confidence: {minimumConfidence[0].toFixed(1)}</Label>
                  <Slider
                    value={minimumConfidence}
                    onValueChange={setMinimumConfidence}
                    max={1}
                    min={0.1}
                    step={0.1}
                    className="mt-2"
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="include-metadata"
                    checked={includeMetadata}
                    onCheckedChange={setIncludeMetadata}
                  />
                  <Label htmlFor="include-metadata">Include Metadata</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="filter-relevance"
                    checked={filterByRelevance}
                    onCheckedChange={setFilterByRelevance}
                  />
                  <Label htmlFor="filter-relevance">Filter by Relevance</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="use-optimized"
                    checked={useOptimizedAnalysis}
                    onCheckedChange={setUseOptimizedAnalysis}
                  />
                  <Label htmlFor="use-optimized">Use Optimized Analysis</Label>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Selected Sources ({sources.length})</Label>
                <ScrollArea className="h-32 border rounded p-2">
                  {sources.map(source => (
                    <div key={source.id} className="text-sm py-1">
                      {source.title || 'Untitled Source'}
                    </div>
                  ))}
                </ScrollArea>
              </div>

              <div className="space-y-2">
                <Button 
                  onClick={generateDiagram} 
                  disabled={isGenerating || sources.length === 0}
                  className="w-full"
                >
                  {isGenerating ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Eye className="w-4 h-4 mr-2" />
                      Generate Preview
                    </>
                  )}
                </Button>
                
                {isGenerating && (
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">{analysisProgress.stage}</span>
                      <span className="text-muted-foreground">
                        {Math.round(analysisProgress.progress * 100)}%
                      </span>
                    </div>
                    <div className="w-full bg-secondary rounded-full h-2">
                      <div 
                        className="bg-primary h-2 rounded-full transition-all duration-300"
                        style={{ width: `${analysisProgress.progress * 100}%` }}
                      />
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={cancelGeneration}
                      className="w-full"
                    >
                      Cancel Analysis
                    </Button>
                  </div>
                )}
              </div>
            </div>

            {/* Preview Panel */}
            <div className="space-y-4">
              <Label>Diagram Preview</Label>
              <div className="border rounded-lg h-96 overflow-hidden">
                {generatedDiagram ? (
                  <ScrollArea className="h-full p-4">
                    <pre className="text-xs font-mono whitespace-pre-wrap">
                      {generatedDiagram}
                    </pre>
                  </ScrollArea>
                ) : (
                  <div className="flex items-center justify-center h-full text-muted-foreground">
                    Click "Generate Preview" to see the diagram
                  </div>
                )}
              </div>

              {analysisResult && (
                <div className="text-sm space-y-1">
                  <div>Concepts found: {analysisResult.global_metadata.total_concepts}</div>
                  <div>Relationships found: {analysisResult.global_metadata.total_relationships}</div>
                  <div>Average confidence: {(analysisResult.global_metadata.average_confidence * 100).toFixed(1)}%</div>
                  {analysisResult.performance_metadata && (
                    <>
                      <div>Processing time: {analysisResult.performance_metadata.total_processing_time_ms.toFixed(0)}ms</div>
                      {analysisResult.performance_metadata.chunks_processed > 1 && (
                        <div>Chunks processed: {analysisResult.performance_metadata.chunks_processed}</div>
                      )}
                      <div>Cache hits: {analysisResult.performance_metadata.cache_hits}</div>
                    </>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>

        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button 
            onClick={saveDiagram} 
            disabled={!generatedDiagram || isSaving}
          >
            {isSaving ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="w-4 h-4 mr-2" />
                Save Diagram
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default SourceDiagramGenerator;