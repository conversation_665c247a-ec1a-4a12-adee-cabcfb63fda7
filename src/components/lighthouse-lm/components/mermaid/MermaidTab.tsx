import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { 
  Copy, 
  Download, 
  RefreshCw,
  Clock,
  MessageSquare,
  Bot,
  Network,
  Zap
} from 'lucide-react';
import { EmptyState } from '@/components/shared/sidebar';
import { useMermaidDiagrams } from '../../hooks/useMermaidDiagrams';
import { diagramTypes } from '../../utils/mermaidTypes';

interface ChatMessage {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: Date;
}

interface MermaidTabProps {
  chatMessages?: ChatMessage[];
  notebookId?: string;
}

const MermaidTab: React.FC<MermaidTabProps> = ({ chatMessages = [], notebookId }) => {
  const [showTimestamps, setShowTimestamps] = useState(true);
  const [customPrompt, setCustomPrompt] = useState('');
  const [showCustomPrompt, setShowCustomPrompt] = useState(false);
  
  const {
    diagrams,
    selectedDiagram,
    diagramType,
    autoGenerate,
    maxMessages,
    isGenerating,
    generateDiagram,
    copyDiagram,
    downloadDiagram,
    regenerateDiagram,
    deleteDiagram,
    updateDiagram,
    setSelectedDiagram,
    setDiagramType,
    setAutoGenerate,
    setMaxMessages
  } = useMermaidDiagrams(chatMessages, { maxMessages: 10 });

  const handleGenerateDiagram = () => {
    generateDiagram(diagramType, customPrompt || undefined);
    setCustomPrompt('');
    setShowCustomPrompt(false);
  };

  return (
    <div className="flex-1 flex flex-col overflow-hidden">
      <div className="p-4 flex-shrink-0 space-y-3">
        <div className="flex flex-wrap gap-2">
          <Button
            variant="outline"
            size="sm"
            className="flex-1 min-w-[120px]"
            onClick={handleGenerateDiagram}
            disabled={isGenerating || chatMessages.length === 0}
          >
            {isGenerating ? (
              <>
                <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
                <span className="text-xs">Generating...</span>
              </>
            ) : (
              <>
                <Zap className="h-3 w-3 mr-1" />
                <span className="text-xs">Generate</span>
              </>
            )}
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="flex-1 min-w-[100px]"
            onClick={() => setShowCustomPrompt(!showCustomPrompt)}
          >
            <Bot className="h-3 w-3 mr-1" />
            <span className="text-xs">Custom</span>
          </Button>
        </div>
        
        {showCustomPrompt && (
          <div className="space-y-3 p-3 bg-muted rounded-lg">
            <div className="flex items-center gap-2">
              <Bot className="h-4 w-4 text-primary" />
              <span className="text-sm font-medium">Custom Diagram Prompt</span>
            </div>
            <Textarea
              placeholder="Describe what you want to visualize..."
              value={customPrompt}
              onChange={(e) => setCustomPrompt(e.target.value)}
              className="min-h-[80px]"
            />
            <div className="flex gap-2">
              <Button
                size="sm"
                onClick={handleGenerateDiagram}
                disabled={!customPrompt.trim() || isGenerating}
                className="flex-1"
              >
                {isGenerating ? (
                  <>
                    <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
                    <span className="text-xs">Generating...</span>
                  </>
                ) : (
                  <>
                    <Zap className="h-3 w-3 mr-1" />
                    <span className="text-xs">Generate</span>
                  </>
                )}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowCustomPrompt(false)}
                className="text-xs px-3"
              >
                Cancel
              </Button>
            </div>
          </div>
        )}
        
        <div className="grid grid-cols-2 gap-3">
          <div className="space-y-2">
            <Label className="text-xs font-medium">Diagram Type</Label>
            <Select value={diagramType} onValueChange={setDiagramType}>
              <SelectTrigger className="h-8 text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {diagramTypes.map(type => {
                  const IconComponent = type.icon;
                  return (
                    <SelectItem key={type.value} value={type.value}>
                      <div className="flex items-center gap-2">
                        <IconComponent className="h-3 w-3" />
                        <span>{type.label}</span>
                      </div>
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <Label className="text-xs font-medium">Max Messages</Label>
            <Select value={maxMessages.toString()} onValueChange={(value) => setMaxMessages(parseInt(value))}>
              <SelectTrigger className="h-8 text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="5">5 messages</SelectItem>
                <SelectItem value="10">10 messages</SelectItem>
                <SelectItem value="20">20 messages</SelectItem>
                <SelectItem value="50">50 messages</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Switch
              id="auto-generate"
              checked={autoGenerate}
              onCheckedChange={setAutoGenerate}
            />
            <Label htmlFor="auto-generate" className="text-xs">Auto-generate</Label>
          </div>
          
          <div className="flex items-center space-x-2">
            <Switch
              id="show-timestamps"
              checked={showTimestamps}
              onCheckedChange={setShowTimestamps}
            />
            <Label htmlFor="show-timestamps" className="text-xs">Timestamps</Label>
          </div>
        </div>
        
        {chatMessages.length > 0 && (
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <MessageSquare className="h-3 w-3" />
            <span>{chatMessages.length} messages available</span>
            {autoGenerate && (
              <Badge variant="secondary" className="text-[10px] px-1">
                Auto-gen ON
              </Badge>
            )}
          </div>
        )}
      </div>

      <div className="flex-1 overflow-hidden">
        {diagrams.length > 0 ? (
          <ScrollArea className="h-full">
            <div className="p-4 space-y-4">
              {diagrams.map((diagram) => (
                <Card key={diagram.id} className="p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        {(() => {
                          const diagramType = diagramTypes.find(t => t.value === diagram.type);
                          const IconComponent = diagramType?.icon || Network;
                          return <IconComponent className="h-4 w-4 text-primary" />;
                        })()}
                        <h3 className="text-sm font-medium truncate">{diagram.title}</h3>
                        <Badge variant="outline" className="text-xs">
                          {diagram.type}
                        </Badge>
                      </div>
                      {diagram.description && (
                        <p className="text-xs text-muted-foreground mb-2">{diagram.description}</p>
                      )}
                      {showTimestamps && (
                        <div className="flex items-center gap-1 text-xs text-muted-foreground">
                          <Clock className="h-3 w-3" />
                          <span>{diagram.createdAt.toLocaleString()}</span>
                        </div>
                      )}
                    </div>
                    
                    <div className="flex items-center gap-1 flex-shrink-0 ml-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyDiagram(diagram)}
                        className="h-7 w-7 p-0"
                        title="Copy code"
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => downloadDiagram(diagram)}
                        className="h-7 w-7 p-0"
                        title="Download"
                      >
                        <Download className="h-3 w-3" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => regenerateDiagram(diagram)}
                        className="h-7 w-7 p-0"
                        title="Regenerate"
                        disabled={isGenerating}
                      >
                        <RefreshCw className={`h-3 w-3 ${isGenerating ? 'animate-spin' : ''}`} />
                      </Button>
                    </div>
                  </div>
                  
                  <div className="bg-muted/30 rounded-lg p-3 border">
                    <pre className="text-xs font-mono text-foreground whitespace-pre-wrap overflow-x-auto">
                      {diagram.code}
                    </pre>
                  </div>
                  
                  {diagram.sourceMessages && diagram.sourceMessages.length > 0 && (
                    <div className="mt-3 pt-3 border-t">
                      <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <MessageSquare className="h-3 w-3" />
                        <span>Generated from {diagram.sourceMessages.length} messages</span>
                      </div>
                    </div>
                  )}
                </Card>
              ))}
            </div>
          </ScrollArea>
        ) : (
          <div className="flex-1 flex items-center justify-center p-8">
            <EmptyState
              icon={<Network className="h-8 w-8" />}
              title="No diagrams yet"
              description={chatMessages.length === 0 
                ? "Start a conversation to generate diagrams"
                : "Generate your first diagram from the conversation"
              }
              action={chatMessages.length > 0 ? {
                label: "Generate Diagram",
                onClick: handleGenerateDiagram
              } : undefined}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default MermaidTab;