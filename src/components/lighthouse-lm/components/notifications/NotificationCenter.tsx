import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '../../../ui/button';
import { Badge } from '../../../ui/badge';
import { ScrollArea } from '../../../ui/scroll-area';
import { Separator } from '../../../ui/separator';
import {
  Bell,
  Check,
  CheckCheck,
  X,
  Info,
  AlertCircle,
  CheckCircle,
  AlertTriangle,
  FileText,
  Download,
  Upload,
  Workflow,
  MessageSquare,
  Settings,
  Trash2
} from 'lucide-react';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '../../../ui/popover';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../../ui/tabs';

export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  category: 'system' | 'upload' | 'diagram' | 'chat' | 'source';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  action?: {
    label: string;
    onClick: () => void;
  };
  metadata?: Record<string, any>;
}

interface NotificationCenterProps {
  notifications: Notification[];
  onMarkAsRead: (id: string) => void;
  onMarkAllAsRead: () => void;
  onClear: (id: string) => void;
  onClearAll: () => void;
  className?: string;
}

const NotificationCenter: React.FC<NotificationCenterProps> = ({
  notifications,
  onMarkAsRead,
  onMarkAllAsRead,
  onClear,
  onClearAll,
  className = ''
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [filter, setFilter] = useState<'all' | 'unread'>('all');
  
  const unreadCount = notifications.filter(n => !n.read).length;
  const filteredNotifications = filter === 'unread' 
    ? notifications.filter(n => !n.read)
    : notifications;

  const getIcon = (type: Notification['type'], category: Notification['category']) => {
    if (category === 'upload') return <Upload className="h-4 w-4" />;
    if (category === 'diagram') return <Workflow className="h-4 w-4" />;
    if (category === 'chat') return <MessageSquare className="h-4 w-4" />;
    if (category === 'source') return <FileText className="h-4 w-4" />;
    
    switch (type) {
      case 'success':
        return <CheckCircle className="h-4 w-4" />;
      case 'error':
        return <AlertCircle className="h-4 w-4" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4" />;
      case 'info':
      default:
        return <Info className="h-4 w-4" />;
    }
  };

  const getIconColor = (type: Notification['type']) => {
    switch (type) {
      case 'success':
        return 'text-emerald-500 dark:text-emerald-400';
      case 'error':
        return 'text-destructive dark:text-red-400';
      case 'warning':
        return 'text-amber-500 dark:text-amber-400';
      case 'info':
      default:
        return 'text-primary dark:text-blue-400';
    }
  };

  const formatTime = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    if (days < 7) return `${days}d ago`;
    return date.toLocaleDateString();
  };

  const groupNotificationsByDate = (notifs: Notification[]) => {
    const grouped: Record<string, Notification[]> = {};
    const today = new Date().toDateString();
    const yesterday = new Date(Date.now() - 86400000).toDateString();

    notifs.forEach(notif => {
      const dateStr = notif.timestamp.toDateString();
      let groupKey = dateStr;
      
      if (dateStr === today) groupKey = 'Today';
      else if (dateStr === yesterday) groupKey = 'Yesterday';
      
      if (!grouped[groupKey]) grouped[groupKey] = [];
      grouped[groupKey].push(notif);
    });

    return grouped;
  };

  const groupedNotifications = groupNotificationsByDate(filteredNotifications);

  // Auto-mark as read when viewing
  useEffect(() => {
    if (isOpen) {
      const unreadIds = filteredNotifications
        .filter(n => !n.read)
        .map(n => n.id);
      
      const timer = setTimeout(() => {
        unreadIds.forEach(id => onMarkAsRead(id));
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [isOpen, filteredNotifications, onMarkAsRead]);

  return (
    <div className={className}>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className="relative"
          >
            <Bell className="h-4 w-4" />
            {unreadCount > 0 && (
              <Badge
                variant="destructive"
                className="absolute -top-1 -right-1 h-5 w-5 p-0 flex items-center justify-center"
              >
                {unreadCount > 9 ? '9+' : unreadCount}
              </Badge>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-96 p-0" align="end">
          <div className="flex items-center justify-between p-4 border-b">
            <h3 className="font-semibold">Notifications</h3>
            <div className="flex items-center gap-2">
              {unreadCount > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onMarkAllAsRead}
                  className="h-7 text-xs"
                >
                  <CheckCheck className="h-3 w-3 mr-1" />
                  Mark all read
                </Button>
              )}
              {notifications.length > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onClearAll}
                  className="h-7 text-xs"
                >
                  <Trash2 className="h-3 w-3 mr-1" />
                  Clear all
                </Button>
              )}
            </div>
          </div>

          <Tabs value={filter} onValueChange={(v) => setFilter(v as any)} className="w-full">
            <TabsList className="grid w-full grid-cols-2 rounded-none border-b">
              <TabsTrigger value="all" className="rounded-none">
                All ({notifications.length})
              </TabsTrigger>
              <TabsTrigger value="unread" className="rounded-none">
                Unread ({unreadCount})
              </TabsTrigger>
            </TabsList>

            <TabsContent value={filter} className="mt-0">
              <ScrollArea className="h-[400px]">
                {filteredNotifications.length === 0 ? (
                  <div className="flex flex-col items-center justify-center py-12">
                    <Bell className="h-12 w-12 text-muted-foreground/20 mb-3" />
                    <p className="text-sm text-muted-foreground">
                      {filter === 'unread' ? 'No unread notifications' : 'No notifications'}
                    </p>
                  </div>
                ) : (
                  <div className="p-2">
                    {Object.entries(groupedNotifications).map(([date, notifs]) => (
                      <div key={date}>
                        <div className="px-2 py-1.5">
                          <span className="text-xs font-medium text-muted-foreground">
                            {date}
                          </span>
                        </div>
                        {notifs.map((notification) => (
                          <motion.div
                            key={notification.id}
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            exit={{ opacity: 0, x: 20 }}
                            className={`
                              relative p-3 rounded-lg mb-2 transition-colors
                              ${notification.read ? 'bg-muted/30' : 'bg-muted/60'}
                              hover:bg-muted/80
                            `}
                          >
                            <button
                              onClick={() => onClear(notification.id)}
                              className="absolute top-2 right-2 p-1 hover:bg-background/50 rounded"
                            >
                              <X className="h-3 w-3 text-muted-foreground" />
                            </button>

                            <div className="flex gap-3 pr-6">
                              <div className={`mt-0.5 ${getIconColor(notification.type)}`}>
                                {getIcon(notification.type, notification.category)}
                              </div>
                              <div className="flex-1 space-y-1">
                                <div className="flex items-start justify-between">
                                  <p className="text-sm font-medium">
                                    {notification.title}
                                  </p>
                                </div>
                                <p className="text-xs text-muted-foreground">
                                  {notification.message}
                                </p>
                                <div className="flex items-center justify-between pt-1">
                                  <span className="text-xs text-muted-foreground">
                                    {formatTime(notification.timestamp)}
                                  </span>
                                  {notification.action && (
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={notification.action.onClick}
                                      className="h-6 text-xs px-2"
                                    >
                                      {notification.action.label}
                                    </Button>
                                  )}
                                </div>
                              </div>
                            </div>

                            {!notification.read && (
                              <div className="absolute top-3 left-1 h-2 w-2 rounded-full bg-primary" />
                            )}
                          </motion.div>
                        ))}
                      </div>
                    ))}
                  </div>
                )}
              </ScrollArea>
            </TabsContent>
          </Tabs>

          {notifications.length > 5 && (
            <div className="p-2 border-t">
              <Button
                variant="ghost"
                size="sm"
                className="w-full text-xs"
                onClick={() => {
                  setIsOpen(false);
                  // Navigate to full notifications page
                }}
              >
                View all notifications
                <Settings className="h-3 w-3 ml-1" />
              </Button>
            </div>
          )}
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default NotificationCenter;