import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Save, Check } from 'lucide-react';
import { useNotes } from '../../hooks/useNotes';
import { useToast } from '../../hooks/use-toast';

interface SaveToNoteButtonProps {
  content: string;
  notebookId?: string;
}

const SaveToNoteButton: React.FC<SaveToNoteButtonProps> = ({
  content,
  notebookId,
}) => {
  const [isSaved, setIsSaved] = useState(false);
  const { createNote, isCreating } = useNotes(notebookId);
  const { toast } = useToast();

  const handleSaveToNote = async () => {
    if (!notebookId || !content) return;
    
    try {
      // Extract first line as title or use default
      const lines = content.split('\n');
      const title = lines[0]?.slice(0, 100) || 'AI Response';
      
      await createNote({
        title,
        content,
        source_type: 'ai_response',
      });
      
      setIsSaved(true);
      
      toast({
        title: "Saved to Notes",
        description: "The AI response has been saved to your notes.",
      });
      
      // Reset saved state after 3 seconds
      setTimeout(() => {
        setIsSaved(false);
      }, 3000);
    } catch (error) {
      console.error('Failed to save to note:', error);
      toast({
        title: "Save Failed",
        description: "Failed to save the response. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={handleSaveToNote}
      disabled={isCreating || isSaved}
      className="text-xs"
    >
      {isSaved ? (
        <>
          <Check className="h-3 w-3 mr-1" />
          Saved
        </>
      ) : isCreating ? (
        <>
          <Save className="h-3 w-3 mr-1" />
          Saving...
        </>
      ) : (
        <>
          <Save className="h-3 w-3 mr-1" />
          Save to Note
        </>
      )}
    </Button>
  );
};

export default SaveToNoteButton;