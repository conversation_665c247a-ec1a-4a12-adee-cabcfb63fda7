import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Plus, FileText } from 'lucide-react';
import { Card } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import NoteEditor from './NoteEditor';
import { useNotes } from '../../hooks/useNotes';
import { LoadingState, EmptyState } from '@/components/shared/sidebar';

interface NotesTabProps {
  notebookId?: string;
}

const NotesTab: React.FC<NotesTabProps> = ({ notebookId }) => {
  const [selectedNote, setSelectedNote] = useState<any>(null);
  const [isCreatingNote, setIsCreatingNote] = useState(false);

  const {
    notes,
    isLoading,
    createNote,
    updateNote,
    deleteNote,
    isCreating,
    isUpdating,
    isDeleting,
  } = useNotes(notebookId);

  const handleCreateNote = () => {
    setSelectedNote(null);
    setIsCreatingNote(true);
  };

  const handleSaveNote = async (noteData: any) => {
    try {
      if (selectedNote) {
        await updateNote(selectedNote.id, noteData);
      } else {
        await createNote(noteData);
      }
      setSelectedNote(null);
      setIsCreatingNote(false);
    } catch (error) {
      console.error('Failed to save note:', error);
    }
  };

  const handleDeleteNote = async () => {
    if (selectedNote) {
      deleteNote(selectedNote.id);
      setSelectedNote(null);
    }
  };

  const handleCancelEdit = () => {
    setSelectedNote(null);
    setIsCreatingNote(false);
  };

  const handleNoteClick = (note: any) => {
    setSelectedNote(note);
    setIsCreatingNote(false);
  };

  if (isLoading) {
    return <LoadingState message="Loading notes..." />;
  }

  return (
    <div className="flex-1 flex flex-col overflow-hidden">
      <div className="p-4 flex-shrink-0">
        <Button
          variant="outline"
          size="sm"
          className="w-full"
          onClick={handleCreateNote}
        >
          <Plus className="h-4 w-4 mr-2" />
          New Note
        </Button>
      </div>
      
      <div className="flex-1 overflow-hidden">
        <ScrollArea className="h-full">
          <div className="p-4">
            {notes && notes.length > 0 ? (
              <div className="space-y-3">
                {notes.map((note) => (
                  <Card
                    key={note.id}
                    className="p-3 cursor-pointer hover:bg-accent transition group"
                    onClick={() => handleNoteClick(note)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                          <FileText className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                          <h3 className="text-sm font-medium text-foreground truncate">
                            {note.title}
                          </h3>
                        </div>
                        <p className="text-xs text-muted-foreground line-clamp-2">
                          {note.content}
                        </p>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            ) : (
              <EmptyState
                icon={FileText}
                title="No notes yet"
                description="Create your first note to get started"
                actionLabel="Create Note"
                onAction={handleCreateNote}
              />
            )}
          </div>
        </ScrollArea>
      </div>
      
      {(selectedNote || isCreatingNote) && (
        <div className="flex-shrink-0">
          <NoteEditor
            note={selectedNote}
            onSave={handleSaveNote}
            onDelete={selectedNote ? handleDeleteNote : undefined}
            onCancel={handleCancelEdit}
            isLoading={isCreating || isUpdating || isDeleting}
          />
        </div>
      )}
    </div>
  );
};

export default NotesTab;