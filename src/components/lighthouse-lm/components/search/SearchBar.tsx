import React, { useState, useCallback, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Input } from '../../../ui/input';
import { Button } from '../../../ui/button';
import { Badge } from '../../../ui/badge';
import {
  Search,
  X,
  Filter,
  Calendar,
  Tag,
  FileText,
  Clock,
  TrendingUp,
  Command
} from 'lucide-react';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '../../../ui/popover';
import { Checkbox } from '../../../ui/checkbox';
import { Label } from '../../../ui/label';
import { Separator } from '../../../ui/separator';
import { debounce } from 'lodash';

interface SearchFilter {
  dateRange?: 'today' | 'week' | 'month' | 'all';
  tags?: string[];
  sourceTypes?: string[];
  hasNotes?: boolean;
  hasDiagrams?: boolean;
}

interface SearchBarProps {
  onSearch: (query: string, filters?: SearchFilter) => void;
  placeholder?: string;
  suggestions?: string[];
  recentSearches?: string[];
  onClearRecent?: () => void;
  className?: string;
}

const SearchBar: React.FC<SearchBarProps> = ({
  onSearch,
  placeholder = 'Search notebooks, sources, or content...',
  suggestions = [],
  recentSearches = [],
  onClearRecent,
  className = ''
}) => {
  const [query, setQuery] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<SearchFilter>({
    dateRange: 'all',
    tags: [],
    sourceTypes: [],
    hasNotes: false,
    hasDiagrams: false
  });
  const [showSuggestions, setShowSuggestions] = useState(false);

  // Debounced search
  const debouncedSearch = useCallback(
    debounce((searchQuery: string, searchFilters: SearchFilter) => {
      onSearch(searchQuery, searchFilters);
    }, 300),
    [onSearch]
  );

  useEffect(() => {
    if (query.length > 0) {
      debouncedSearch(query, filters);
    }
  }, [query, filters, debouncedSearch]);

  const handleQueryChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newQuery = e.target.value;
    setQuery(newQuery);
    setShowSuggestions(newQuery.length > 0);
  };

  const handleClear = () => {
    setQuery('');
    setShowSuggestions(false);
    onSearch('', filters);
  };

  const handleSuggestionClick = (suggestion: string) => {
    setQuery(suggestion);
    setShowSuggestions(false);
    onSearch(suggestion, filters);
  };

  const handleFilterChange = (newFilters: Partial<SearchFilter>) => {
    const updatedFilters = { ...filters, ...newFilters };
    setFilters(updatedFilters);
    if (query) {
      onSearch(query, updatedFilters);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      setShowSuggestions(false);
      if (query) {
        handleClear();
      }
    } else if (e.key === 'Enter') {
      setShowSuggestions(false);
      onSearch(query, filters);
    }
  };

  // Keyboard shortcut for search focus
  useEffect(() => {
    const handleGlobalKeyDown = (e: KeyboardEvent) => {
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault();
        document.getElementById('lighthouse-search')?.focus();
      }
    };

    window.addEventListener('keydown', handleGlobalKeyDown);
    return () => window.removeEventListener('keydown', handleGlobalKeyDown);
  }, []);

  const activeFilterCount = [
    filters.dateRange !== 'all',
    (filters.tags?.length || 0) > 0,
    (filters.sourceTypes?.length || 0) > 0,
    filters.hasNotes,
    filters.hasDiagrams
  ].filter(Boolean).length;

  return (
    <div className={`relative ${className}`}>
      <div className="flex items-center gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            id="lighthouse-search"
            type="text"
            value={query}
            onChange={handleQueryChange}
            onKeyDown={handleKeyDown}
            onFocus={() => {
              setIsFocused(true);
              setShowSuggestions(query.length > 0 || recentSearches.length > 0);
            }}
            onBlur={() => {
              setIsFocused(false);
              setTimeout(() => setShowSuggestions(false), 200);
            }}
            placeholder={placeholder}
            className="pl-9 pr-20"
          />
          <div className="absolute right-2 top-1/2 -translate-y-1/2 flex items-center gap-1">
            {query && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClear}
                className="h-6 w-6 p-0"
              >
                <X className="h-3 w-3" />
              </Button>
            )}
            <div className="flex items-center gap-1 text-xs text-muted-foreground">
              <Command className="h-3 w-3" />
              <span>K</span>
            </div>
          </div>
        </div>

        <Popover open={showFilters} onOpenChange={setShowFilters}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              className="relative"
            >
              <Filter className="h-4 w-4 mr-2" />
              Filters
              {activeFilterCount > 0 && (
                <Badge
                  variant="secondary"
                  className="absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center"
                >
                  {activeFilterCount}
                </Badge>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-80" align="end">
            <div className="space-y-4">
              <div>
                <h4 className="font-medium text-sm mb-2">Date Range</h4>
                <div className="grid grid-cols-2 gap-2">
                  {['today', 'week', 'month', 'all'].map((range) => (
                    <Button
                      key={range}
                      variant={filters.dateRange === range ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => handleFilterChange({ dateRange: range as any })}
                      className="capitalize"
                    >
                      {range === 'all' ? 'All Time' : range}
                    </Button>
                  ))}
                </div>
              </div>

              <Separator />

              <div>
                <h4 className="font-medium text-sm mb-2">Content Type</h4>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="has-notes"
                      checked={filters.hasNotes}
                      onCheckedChange={(checked) =>
                        handleFilterChange({ hasNotes: checked as boolean })
                      }
                    />
                    <Label htmlFor="has-notes" className="text-sm">
                      Has Notes
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="has-diagrams"
                      checked={filters.hasDiagrams}
                      onCheckedChange={(checked) =>
                        handleFilterChange({ hasDiagrams: checked as boolean })
                      }
                    />
                    <Label htmlFor="has-diagrams" className="text-sm">
                      Has Diagrams
                    </Label>
                  </div>
                </div>
              </div>

              <Separator />

              <div>
                <h4 className="font-medium text-sm mb-2">Source Types</h4>
                <div className="flex flex-wrap gap-2">
                  {['PDF', 'Web', 'Audio', 'Text'].map((type) => (
                    <Badge
                      key={type}
                      variant={filters.sourceTypes?.includes(type) ? 'default' : 'outline'}
                      className="cursor-pointer"
                      onClick={() => {
                        const current = filters.sourceTypes || [];
                        const updated = current.includes(type)
                          ? current.filter((t) => t !== type)
                          : [...current, type];
                        handleFilterChange({ sourceTypes: updated });
                      }}
                    >
                      {type}
                    </Badge>
                  ))}
                </div>
              </div>

              <Separator />

              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  setFilters({
                    dateRange: 'all',
                    tags: [],
                    sourceTypes: [],
                    hasNotes: false,
                    hasDiagrams: false
                  });
                  if (query) {
                    onSearch(query, {});
                  }
                }}
                className="w-full"
              >
                Clear All Filters
              </Button>
            </div>
          </PopoverContent>
        </Popover>
      </div>

      {/* Suggestions Dropdown */}
      <AnimatePresence>
        {showSuggestions && (suggestions.length > 0 || recentSearches.length > 0) && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="absolute top-full left-0 right-0 mt-2 bg-popover border rounded-lg shadow-lg z-50 overflow-hidden"
          >
            {recentSearches.length > 0 && !query && (
              <div className="p-2">
                <div className="flex items-center justify-between px-2 py-1">
                  <span className="text-xs font-medium text-muted-foreground flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    Recent
                  </span>
                  {onClearRecent && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={onClearRecent}
                      className="h-5 text-xs px-2"
                    >
                      Clear
                    </Button>
                  )}
                </div>
                {recentSearches.slice(0, 5).map((search, index) => (
                  <button
                    key={index}
                    onClick={() => handleSuggestionClick(search)}
                    className="w-full text-left px-2 py-1.5 hover:bg-accent rounded text-sm"
                  >
                    {search}
                  </button>
                ))}
              </div>
            )}

            {suggestions.length > 0 && query && (
              <div className="p-2">
                <div className="px-2 py-1">
                  <span className="text-xs font-medium text-muted-foreground flex items-center gap-1">
                    <TrendingUp className="h-3 w-3" />
                    Suggestions
                  </span>
                </div>
                {suggestions.slice(0, 5).map((suggestion, index) => (
                  <button
                    key={index}
                    onClick={() => handleSuggestionClick(suggestion)}
                    className="w-full text-left px-2 py-1.5 hover:bg-accent rounded text-sm"
                  >
                    {suggestion}
                  </button>
                ))}
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default SearchBar;