import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useSourceUpdate } from '../../hooks/useSourceUpdate';

interface RenameSourceDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  source: any;
  notebookId?: string;
}

const RenameSourceDialog: React.FC<RenameSourceDialogProps> = ({
  open,
  onOpenChange,
  source,
  notebookId,
}) => {
  const [newTitle, setNewTitle] = useState('');
  const { renameSource, isUpdating } = useSourceUpdate();

  useEffect(() => {
    if (source) {
      setNewTitle(source.title || '');
    }
  }, [source]);

  const handleRename = async () => {
    if (!source || !newTitle.trim()) return;
    
    try {
      await renameSource(source.id, newTitle.trim());
      onOpenChange(false);
      setNewTitle('');
    } catch (error) {
      console.error('Failed to rename source:', error);
    }
  };

  const handleCancel = () => {
    onOpenChange(false);
    setNewTitle(source?.title || '');
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Rename Source</DialogTitle>
        </DialogHeader>
        
        <div className="py-4">
          <label className="text-sm font-medium text-foreground mb-1 block">
            Source Name
          </label>
          <Input
            value={newTitle}
            onChange={(e) => setNewTitle(e.target.value)}
            placeholder="Enter new name"
            autoFocus
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !isUpdating) {
                handleRename();
              }
            }}
          />
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={handleCancel}>
            Cancel
          </Button>
          <Button 
            onClick={handleRename} 
            disabled={!newTitle.trim() || isUpdating}
          >
            {isUpdating ? 'Renaming...' : 'Rename'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default RenameSourceDialog;