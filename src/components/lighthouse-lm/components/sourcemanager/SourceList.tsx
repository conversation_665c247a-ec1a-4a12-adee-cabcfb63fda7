import React from 'react';
import { Source } from '@/services/insights-api';
import { SourceCard } from './SourceCard';
import { EmptyState } from '@/components/Lightbulb/components/ui/EmptyState';
import { FileText } from 'lucide-react';

export interface SourceListProps {
  sources: Source[];
  selectedSources?: Set<string>;
  viewMode?: 'list' | 'grid' | 'compact';
  showCheckbox?: boolean;
  showActions?: boolean;
  isLoading?: boolean;
  emptyMessage?: string;
  emptyDescription?: string;
  height?: number;
  onSourceSelect?: (sourceId: string, event?: React.MouseEvent) => void;
  onSourceClick?: (source: Source) => void;
  onSourceRename?: (source: Source) => void;
  onSourceDelete?: (sourceId: string) => void;
  onSourceToggleStar?: (sourceId: string) => void;
  onSourceDownload?: (sourceId: string) => void;
  onSourceView?: (source: Source) => void;
}

const ITEM_HEIGHTS = {
  list: 120,
  grid: 200,
  compact: 60,
};

const GRID_COLUMNS = {
  sm: 1,
  md: 2,
  lg: 3,
  xl: 4,
};

export const SourceList: React.FC<SourceListProps> = ({
  sources,
  selectedSources = new Set(),
  viewMode = 'list',
  showCheckbox = false,
  showActions = true,
  isLoading = false,
  emptyMessage = 'No sources found',
  emptyDescription = 'Add some sources to get started',
  height = 400,
  onSourceSelect,
  onSourceClick,
  onSourceRename,
  onSourceDelete,
  onSourceToggleStar,
  onSourceDownload,
  onSourceView,
}) => {
  if (isLoading) {
    return (
      <div className="space-y-4">
        {Array.from({ length: 3 }).map((_, index) => (
          <div key={index} className="animate-pulse">
            <div className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
              <div className="w-8 h-8 bg-gray-200 rounded"></div>
              <div className="flex-1">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  // console.log('[SourceList] Sources received:', sources);
  // console.log('[SourceList] Sources type:', typeof sources, Array.isArray(sources));
  
  if (!sources || sources.length === 0) {
    // console.log('[SourceList] No sources to display, showing empty state');
    return (
      <EmptyState
        icon={<FileText />}
        title={emptyMessage || "No sources added yet"}
        description={emptyDescription || "Click 'Add' to upload documents, PDFs, or add web content to your notebook."}
      />
    );
  }

  const renderGridView = () => {
    const columns = GRID_COLUMNS.lg; // Default to lg for now
    const rows = Math.ceil(sources.length / columns);
    
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {sources.map((source) => (
          <div key={source.id} className="relative">
            <SourceCard
              source={source}
              isSelected={selectedSources.has(source.id)}
              viewMode={viewMode}
              showCheckbox={showCheckbox}
              showActions={showActions}
              onSelect={onSourceSelect}
              onClick={onSourceClick}
              onRename={onSourceRename}
              onDelete={onSourceDelete}
              onToggleStar={onSourceToggleStar}
              onDownload={onSourceDownload}
              onView={onSourceView}
            />
          </div>
        ))}
      </div>
    );
  };

  const renderListView = () => {
    return (
      <div className="space-y-2" style={{ maxHeight: height, overflowY: 'auto' }}>
        {sources.map((source) => (
          <SourceCard
            key={source.id}
            source={source}
            isSelected={selectedSources.has(source.id)}
            viewMode={viewMode}
            showCheckbox={showCheckbox}
            showActions={showActions}
            onSelect={onSourceSelect}
            onClick={onSourceClick}
            onRename={onSourceRename}
            onDelete={onSourceDelete}
            onToggleStar={onSourceToggleStar}
            onDownload={onSourceDownload}
            onView={onSourceView}
          />
        ))}
      </div>
    );
  };

  const renderCompactView = () => {
    return (
      <div className="space-y-1">
        {sources.map((source) => (
          <SourceCard
            key={source.id}
            source={source}
            isSelected={selectedSources.has(source.id)}
            viewMode={viewMode}
            showCheckbox={showCheckbox}
            showActions={showActions}
            onSelect={onSourceSelect}
            onClick={onSourceClick}
            onRename={onSourceRename}
            onDelete={onSourceDelete}
            onToggleStar={onSourceToggleStar}
            onDownload={onSourceDownload}
            onView={onSourceView}
          />
        ))}
      </div>
    );
  };

  switch (viewMode) {
    case 'grid':
      return renderGridView();
    case 'compact':
      return renderCompactView();
    default:
      return renderListView();
  }
};

export default SourceList;