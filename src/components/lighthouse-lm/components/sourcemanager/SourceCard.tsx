import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { 
  FileText, 
  Globe, 
  Image, 
  Video, 
  Music, 
  Archive,
  MoreVertical,
  Star,
  StarOff,
  Edit2,
  Trash2,
  Download,
  Eye
} from 'lucide-react';
import { Source } from '@/services/insights-api';
import { formatDistanceToNow } from 'date-fns';
import { formatBytes } from '@/lib/utils';

export interface SourceCardProps {
  source: Source;
  isSelected?: boolean;
  isProcessing?: boolean;
  viewMode?: 'list' | 'grid' | 'compact';
  showCheckbox?: boolean;
  showActions?: boolean;
  onSelect?: (sourceId: string, event?: React.MouseEvent) => void;
  onClick?: (source: Source) => void;
  onRename?: (source: Source) => void;
  onDelete?: (sourceId: string) => void;
  onToggleStar?: (sourceId: string) => void;
  onDownload?: (sourceId: string) => void;
  onView?: (source: Source) => void;
}

const getSourceIcon = (sourceType: string) => {
  switch (sourceType.toLowerCase()) {
    case 'url':
    case 'web':
      return Globe;
    case 'image':
    case 'png':
    case 'jpg':
    case 'jpeg':
    case 'gif':
      return Image;
    case 'video':
    case 'mp4':
    case 'avi':
    case 'mov':
      return Video;
    case 'audio':
    case 'mp3':
    case 'wav':
      return Music;
    case 'archive':
    case 'zip':
    case 'rar':
      return Archive;
    default:
      return FileText;
  }
};

const getSourceTypeColor = (sourceType: string) => {
  switch (sourceType.toLowerCase()) {
    case 'url':
    case 'web':
      return 'bg-blue-100 text-blue-800';
    case 'pdf':
      return 'bg-red-100 text-red-800';
    case 'image':
    case 'png':
    case 'jpg':
    case 'jpeg':
    case 'gif':
      return 'bg-green-100 text-green-800';
    case 'video':
    case 'mp4':
    case 'avi':
    case 'mov':
      return 'bg-purple-100 text-purple-800';
    case 'audio':
    case 'mp3':
    case 'wav':
      return 'bg-yellow-100 text-yellow-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

export const SourceCard: React.FC<SourceCardProps> = ({
  source,
  isSelected = false,
  isProcessing = false,
  viewMode = 'list',
  showCheckbox = false,
  showActions = true,
  onSelect,
  onClick,
  onRename,
  onDelete,
  onToggleStar,
  onDownload,
  onView,
}) => {
  const IconComponent = getSourceIcon(source.source_type);
  const typeColorClass = getSourceTypeColor(source.source_type);
  
  const handleCardClick = (e: React.MouseEvent) => {
    if (e.target instanceof HTMLElement && 
        (e.target.closest('button') || e.target.closest('[role="checkbox"]'))) {
      return;
    }
    onClick?.(source);
  };

  const handleCheckboxChange = (e: React.MouseEvent) => {
    e.stopPropagation();
    onSelect?.(source.id, e);
  };

  const renderListView = () => (
    <Card 
      className={`cursor-pointer transition-all hover:shadow-md ${
        isSelected ? 'ring-2 ring-blue-500' : ''
      } ${isProcessing ? 'opacity-50' : ''}`}
      onClick={handleCardClick}
    >
      <CardContent className="p-4">
        <div className="flex items-center space-x-4">
          {showCheckbox && (
            <Checkbox
              checked={isSelected}
              onCheckedChange={() => onSelect?.(source.id)}
              onClick={handleCheckboxChange}
            />
          )}
          
          <div className="flex-shrink-0">
            <IconComponent className="h-8 w-8 text-gray-500" />
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2 mb-1">
              <h3 className="text-sm font-medium text-gray-900 truncate">
                {source.title || source.display_name || 'Untitled'}
              </h3>
              <Badge className={`text-xs ${typeColorClass}`}>
                {source.source_type}
              </Badge>
            </div>
            
            {source.summary && (
              <p className="text-sm text-gray-500 truncate mb-1">
                {source.summary}
              </p>
            )}
            
            <div className="flex items-center space-x-4 text-xs text-gray-400">
              {source.file_size && (
                <span>{formatBytes(source.file_size)}</span>
              )}
              <span>
                {formatDistanceToNow(new Date(source.created_at), { addSuffix: true })}
              </span>
              <span className={`px-2 py-1 rounded-full text-xs ${
                source.processing_status === 'completed' ? 'bg-green-100 text-green-800' :
                source.processing_status === 'processing' ? 'bg-yellow-100 text-yellow-800' :
                'bg-gray-100 text-gray-800'
              }`}>
                {source.processing_status}
              </span>
            </div>
          </div>
          
          {showActions && (
            <div className="flex items-center space-x-1">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        onView?.(source);
                      }}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>View</TooltipContent>
                </Tooltip>
              </TooltipProvider>
              
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        onRename?.(source);
                      }}
                    >
                      <Edit2 className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Rename</TooltipContent>
                </Tooltip>
              </TooltipProvider>
              
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        onDelete?.(source.id);
                      }}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Delete</TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );

  const renderGridView = () => (
    <Card 
      className={`cursor-pointer transition-all hover:shadow-md ${
        isSelected ? 'ring-2 ring-blue-500' : ''
      } ${isProcessing ? 'opacity-50' : ''}`}
      onClick={handleCardClick}
    >
      <CardContent className="p-4">
        <div className="text-center">
          {showCheckbox && (
            <div className="absolute top-2 left-2">
              <Checkbox
                checked={isSelected}
                onCheckedChange={() => onSelect?.(source.id)}
                onClick={handleCheckboxChange}
              />
            </div>
          )}
          
          <div className="mb-3">
            <IconComponent className="h-12 w-12 text-gray-500 mx-auto" />
          </div>
          
          <h3 className="text-sm font-medium text-gray-900 mb-2 truncate">
            {source.title || source.display_name || 'Untitled'}
          </h3>
          
          <Badge className={`text-xs mb-2 ${typeColorClass}`}>
            {source.source_type}
          </Badge>
          
          {source.summary && (
            <p className="text-xs text-gray-500 mb-2 line-clamp-2">
              {source.summary}
            </p>
          )}
          
          <div className="text-xs text-gray-400 space-y-1">
            {source.file_size && (
              <div>{formatBytes(source.file_size)}</div>
            )}
            <div>
              {formatDistanceToNow(new Date(source.created_at), { addSuffix: true })}
            </div>
          </div>
          
          {showActions && (
            <div className="flex justify-center space-x-1 mt-3">
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  onView?.(source);
                }}
              >
                <Eye className="h-4 w-4" />
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  onRename?.(source);
                }}
              >
                <Edit2 className="h-4 w-4" />
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  onDelete?.(source.id);
                }}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );

  const renderCompactView = () => (
    <div 
      className={`flex items-center space-x-3 p-2 rounded-lg cursor-pointer transition-all hover:bg-gray-50 ${
        isSelected ? 'bg-blue-50 ring-1 ring-blue-200' : ''
      } ${isProcessing ? 'opacity-50' : ''}`}
      onClick={handleCardClick}
    >
      {showCheckbox && (
        <Checkbox
          checked={isSelected}
          onCheckedChange={() => onSelect?.(source.id)}
          onClick={handleCheckboxChange}
        />
      )}
      
      <IconComponent className="h-5 w-5 text-gray-500 flex-shrink-0" />
      
      <div className="flex-1 min-w-0">
        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium text-gray-900 truncate">
            {source.title || source.display_name || 'Untitled'}
          </span>
          <Badge className={`text-xs ${typeColorClass}`}>
            {source.source_type}
          </Badge>
        </div>
      </div>
      
      {showActions && (
        <div className="flex items-center space-x-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              onView?.(source);
            }}
          >
            <Eye className="h-3 w-3" />
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              onDelete?.(source.id);
            }}
          >
            <Trash2 className="h-3 w-3" />
          </Button>
        </div>
      )}
    </div>
  );

  switch (viewMode) {
    case 'grid':
      return renderGridView();
    case 'compact':
      return renderCompactView();
    default:
      return renderListView();
  }
};

export default SourceCard;