import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Slider } from '@/components/ui/slider';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Play, Pause, RotateCcw, Volume2, Download, MoreVertical, Trash2, Loader2, RefreshCw, AlertTriangle } from 'lucide-react';
import { useToast } from '../../hooks/use-toast';

interface AudioPlayerProps {
  audioUrl: string;
  title?: string;
  notebookId?: string;
  expiresAt?: string | null;
  onError?: () => void;
  onDeleted?: () => void;
  onRetry?: () => void;
  onUrlRefresh?: (notebookId: string) => void;
}

const AudioPlayer = ({ 
  audioUrl, 
  title = "Deep Dive Conversation", 
  notebookId,
  expiresAt,
  onError,
  onDeleted,
  onRetry,
  onUrlRefresh
}: AudioPlayerProps) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [loading, setLoading] = useState(true);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const [audioError, setAudioError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [autoRetryInProgress, setAutoRetryInProgress] = useState(false);
  const audioRef = useRef<HTMLAudioElement>(null);
  const { toast } = useToast();

  // Check if audio is expired
  const isExpired = expiresAt ? new Date(expiresAt) <= new Date() : false;

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const updateTime = () => setCurrentTime(audio.currentTime);
    const updateDuration = () => {
      setDuration(audio.duration);
      setLoading(false);
      setAudioError(null);
      setRetryCount(0);
    };
    const handleEnded = () => setIsPlaying(false);
    const handleError = async (e: Event) => {
      console.error('Audio error:', e);
      setLoading(false);
      setIsPlaying(false);
      
      // If the URL has expired and we have a notebookId, try to refresh it automatically
      if ((isExpired || audioError?.includes('403') || audioError?.includes('expired')) && 
          notebookId && onUrlRefresh && retryCount < 2 && !autoRetryInProgress) {
        console.log('Audio URL expired or access denied, attempting automatic refresh...');
        setAutoRetryInProgress(true);
        setRetryCount(prev => prev + 1);
        onUrlRefresh(notebookId);
        return;
      }

      if (retryCount < 2 && !autoRetryInProgress) {
        // Auto-retry up to 2 times for transient errors
        setTimeout(() => {
          setRetryCount(prev => prev + 1);
          audio.load();
        }, 1000 * (retryCount + 1)); // Exponential backoff
      } else {
        setAudioError('Failed to load audio');
        setAutoRetryInProgress(false);
        onError?.();
      }
    };

    const handleCanPlay = () => {
      setLoading(false);
      setAudioError(null);
      setRetryCount(0);
      setAutoRetryInProgress(false);
    };

    const handleLoadStart = () => {
      if (autoRetryInProgress) {
        setLoading(true);
      }
    };

    audio.addEventListener('timeupdate', updateTime);
    audio.addEventListener('loadedmetadata', updateDuration);
    audio.addEventListener('canplay', handleCanPlay);
    audio.addEventListener('loadstart', handleLoadStart);
    audio.addEventListener('ended', handleEnded);
    audio.addEventListener('error', handleError);

    return () => {
      audio.removeEventListener('timeupdate', updateTime);
      audio.removeEventListener('loadedmetadata', updateDuration);
      audio.removeEventListener('canplay', handleCanPlay);
      audio.removeEventListener('loadstart', handleLoadStart);
      audio.removeEventListener('ended', handleEnded);
      audio.removeEventListener('error', handleError);
    };
  }, [onError, isExpired, retryCount, notebookId, onUrlRefresh, audioError, autoRetryInProgress]);

  // Reload audio when URL changes (for automatic refresh)
  useEffect(() => {
    const audio = audioRef.current;
    if (audio && autoRetryInProgress) {
      console.log('Reloading audio with new URL...');
      audio.load();
    }
  }, [audioUrl, autoRetryInProgress]);

  const togglePlayPause = () => {
    const audio = audioRef.current;
    if (!audio || audioError) return;

    if (isPlaying) {
      audio.pause();
    } else {
      const playPromise = audio.play();
      if (playPromise !== undefined) {
        playPromise.catch(error => {
          console.error('Play failed:', error);
          setAudioError('Playback failed');
        });
      }
    }
    setIsPlaying(!isPlaying);
  };

  const handleSeek = (value: number[]) => {
    const audio = audioRef.current;
    if (!audio || audioError) return;

    const time = value[0];
    audio.currentTime = time;
    setCurrentTime(time);
  };

  const handleVolumeChange = (value: number[]) => {
    const audio = audioRef.current;
    if (!audio) return;

    const vol = value[0];
    audio.volume = vol;
    setVolume(vol);
  };

  const restart = () => {
    const audio = audioRef.current;
    if (!audio || audioError) return;

    audio.currentTime = 0;
    setCurrentTime(0);
  };

  const retryLoad = () => {
    const audio = audioRef.current;
    if (!audio) return;

    setLoading(true);
    setAudioError(null);
    setRetryCount(0);
    setAutoRetryInProgress(false);
    audio.load();
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const downloadAudio = async () => {
    setIsDownloading(true);
    
    try {
      // Fetch the audio file
      const response = await fetch(audioUrl);
      if (!response.ok) {
        throw new Error('Failed to fetch audio file');
      }
      
      // Create a blob from the response
      const blob = await response.blob();
      
      // Create a temporary URL for the blob
      const blobUrl = URL.createObjectURL(blob);
      
      // Create a temporary anchor element and trigger download
      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = `${title}.mp3`;
      document.body.appendChild(link);
      link.click();
      
      // Clean up
      document.body.removeChild(link);
      URL.revokeObjectURL(blobUrl);
      
      toast({
        title: "Download Started",
        description: "Your audio file is being downloaded.",
      });
    } catch (error) {
      console.error('Download failed:', error);
      toast({
        title: "Download Failed",
        description: "Failed to download the audio file. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsDownloading(false);
    }
  };

  const deleteAudio = async () => {
    if (!notebookId) {
      toast({
        title: "Error",
        description: "Cannot delete audio - notebook ID not found",
        variant: "destructive",
      });
      return;
    }

    setIsDeleting(true);
    
    try {
      // Handle audio deletion locally
      // TODO: Implement local audio file deletion if needed
      console.log('Audio deletion requested for notebook:', notebookId);
      
      // Update the notebook through the backend API
      const InsightsAPI = (await import('../../../../services/insights-api')).default;
      await InsightsAPI.updateNotebook(notebookId, {
        audio_overview_url: undefined,
        audio_overview_generation_status: undefined
      });
      
      console.log('Successfully cleared audio fields from notebook');

      toast({
        title: "Audio Deleted",
        description: "The audio overview and associated files have been successfully deleted.",
      });

      // Call the onDeleted callback to update parent component
      onDeleted?.();

    } catch (error) {
      console.error('Failed to delete audio:', error);
      toast({
        title: "Delete Failed",
        description: "Failed to delete the audio overview. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <Card className="p-4 space-y-4 transition-all duration-300 hover:shadow-lg hover:shadow-primary/10 hover:border-primary/20 hover:bg-gradient-to-br hover:from-card hover:to-accent/30">
      <audio ref={audioRef} src={audioUrl} preload="metadata" />
      
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <h4 className="font-medium text-foreground transition-all duration-200 hover:text-primary hover:scale-[1.02] cursor-default">{title}</h4>
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button 
              variant="ghost" 
              size="sm" 
              disabled={isDeleting}
              className="transition-all duration-200 hover:scale-110 hover:rotate-90 hover:bg-accent hover:text-primary hover:shadow-md focus:ring-2 focus:ring-primary/20 focus:bg-accent active:scale-95 disabled:hover:scale-100 disabled:hover:rotate-0"
            >
              {isDeleting ? (
                <Loader2 className="h-4 w-4 animate-spin transition-all duration-200" />
              ) : (
                <MoreVertical className="h-4 w-4 transition-all duration-200" />
              )}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="animate-in slide-in-from-top-2 duration-200">
            <DropdownMenuItem 
              onClick={downloadAudio} 
              disabled={isDownloading}
              className="transition-all duration-200 hover:bg-accent hover:text-primary focus:bg-accent focus:text-primary cursor-pointer disabled:cursor-not-allowed"
            >
              {isDownloading ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin transition-all duration-200" />
              ) : (
                <Download className="h-4 w-4 mr-2 transition-all duration-200 group-hover:scale-110 group-hover:rotate-12" />
              )}
              <span className="transition-all duration-200">{isDownloading ? 'Downloading...' : 'Download'}</span>
            </DropdownMenuItem>
            <DropdownMenuItem 
              onClick={deleteAudio}
              className="text-destructive focus:text-destructive transition-all duration-200 hover:bg-destructive/10 hover:text-destructive focus:bg-destructive/10 focus:text-destructive cursor-pointer disabled:cursor-not-allowed"
              disabled={isDeleting}
            >
              <Trash2 className="h-4 w-4 mr-2 transition-all duration-200 group-hover:scale-110 group-hover:rotate-12" />
              <span className="transition-all duration-200">Delete</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Auto-refresh indicator */}
      {autoRetryInProgress && (
        <div className="flex items-center justify-between p-3 bg-accent rounded-md border border-border transition-all duration-300 animate-in slide-in-from-top-2 hover:bg-accent/80 hover:border-primary/30">
          <div className="flex items-center space-x-2">
            <Loader2 className="h-4 w-4 text-primary animate-spin transition-all duration-200" />
            <span className="text-sm text-primary transition-all duration-200">Refreshing audio access...</span>
          </div>
        </div>
      )}

      {/* Error State */}
      {audioError && !autoRetryInProgress && (
        <div className="flex items-center justify-between p-3 bg-destructive/10 rounded-md border border-destructive/20 transition-all duration-300 animate-in slide-in-from-top-2 hover:bg-destructive/20 hover:border-destructive/30">
          <div className="flex items-center space-x-2">
            <AlertTriangle className="h-4 w-4 text-destructive transition-all duration-200 hover:scale-110 hover:rotate-12" />
            <span className="text-sm text-destructive transition-all duration-200">{audioError}</span>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={onRetry || retryLoad}
            className="text-destructive border-destructive/30 hover:bg-destructive/10 transition-all duration-200 hover:scale-105 hover:shadow-md hover:border-destructive/40 focus:ring-2 focus:ring-destructive/20 active:scale-95"
          >
            <RefreshCw className="h-4 w-4 mr-1 transition-all duration-200 hover:rotate-180" />
            <span className="transition-all duration-200">Retry</span>
          </Button>
        </div>
      )}

      {/* Enhanced Progress Bar with Loading State */}
      {loading ? (
        <div className="space-y-2">
          {/* Progress bar skeleton */}
          <div className="w-full h-2 bg-gradient-to-r from-muted via-muted/50 to-muted rounded-full animate-pulse relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-primary/20 to-transparent animate-pulse" style={{ animationDelay: '0.5s' }}></div>
          </div>
          <div className="flex justify-between">
            <div className="h-3 w-8 bg-gradient-to-r from-muted via-muted/50 to-muted rounded animate-pulse"></div>
            <div className="h-3 w-8 bg-gradient-to-r from-muted via-muted/50 to-muted rounded animate-pulse" style={{ animationDelay: '0.1s' }}></div>
          </div>
        </div>
      ) : (
        <div className="space-y-2">
          <Slider
            value={[currentTime]}
            max={duration || 100}
            step={1}
            onValueChange={handleSeek}
            className="w-full transition-all duration-200 hover:scale-[1.02] focus-within:scale-[1.02]"
            disabled={loading || !!audioError}
          />
          <div className="flex justify-between text-xs text-muted-foreground">
            <span className="transition-all duration-200 hover:text-primary hover:scale-105 cursor-default">{formatTime(currentTime)}</span>
            <span className="transition-all duration-200 hover:text-primary hover:scale-105 cursor-default">{formatTime(duration)}</span>
          </div>
        </div>
      )}

      {/* Enhanced Controls with Loading State */}
      {loading ? (
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {/* Restart button skeleton */}
            <div className="w-8 h-8 bg-gradient-to-br from-muted to-muted/80 rounded animate-pulse"></div>
            {/* Play button skeleton with pulsing effect */}
            <div className="w-12 h-8 bg-gradient-to-br from-primary/20 to-primary/30 rounded animate-pulse relative overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-primary/10 to-transparent animate-pulse" style={{ animationDelay: '0.3s' }}></div>
            </div>
          </div>
          
          {/* Volume control skeleton */}
          <div className="flex items-center space-x-2 w-24">
            <div className="w-4 h-4 bg-gradient-to-br from-muted to-muted/80 rounded animate-pulse"></div>
            <div className="flex-1 h-2 bg-gradient-to-r from-muted via-muted/50 to-muted rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
          </div>
        </div>
      ) : (
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={restart}
              disabled={loading || !!audioError}
              className="transition-all duration-200 hover:scale-110 hover:bg-accent hover:text-primary hover:shadow-md focus:ring-2 focus:ring-primary/20 focus:bg-accent active:scale-95 disabled:hover:scale-100"
            >
              <RotateCcw className="h-4 w-4 transition-all duration-200 hover:rotate-180" />
            </Button>
            
            <Button
              variant="default"
              size="sm"
              onClick={togglePlayPause}
              disabled={loading || !!audioError}
              className="w-12 transition-all duration-200 hover:scale-110 hover:shadow-lg hover:shadow-primary/25 focus:ring-2 focus:ring-primary/20 active:scale-95 disabled:hover:scale-100"
            >
              {loading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-foreground transition-all duration-200" />
              ) : isPlaying ? (
                <Pause className="h-4 w-4 transition-all duration-200" />
              ) : (
                <Play className="h-4 w-4 transition-all duration-200" />
              )}
            </Button>
          </div>

          {/* Volume Control */}
          <div className="flex items-center space-x-2 w-24">
            <Volume2 className="h-4 w-4 text-muted-foreground transition-all duration-200 hover:text-primary hover:scale-110 cursor-default" />
            <Slider
              value={[volume]}
              max={1}
              step={0.1}
              onValueChange={handleVolumeChange}
              className="flex-1 transition-all duration-200 hover:scale-105 focus-within:scale-105"
            />
          </div>
        </div>
      )}
    </Card>
  );
};

export default AudioPlayer;
