import React, { useEffect, useRef, useState } from 'react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Card } from '@/components/ui/card';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Citation } from '../../types/message';
import { ChevronDown, ChevronUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { getSourceIcon } from '../../utils/common';

interface SourceContentViewerProps {
  citation: Citation | null;
  sourceContent?: string;
  sourceSummary?: string;
  sourceUrl?: string;
  className?: string;
  isOpenedFromSourceList?: boolean;
  // Dialog props for modal usage
  isOpen?: boolean;
  onClose?: () => void;
  asDialog?: boolean;
}

const SourceContentViewer: React.FC<SourceContentViewerProps> = ({
  citation,
  sourceContent = '',
  sourceSummary,
  sourceUrl,
  className = '',
  isOpenedFromSourceList = false,
  isOpen = false,
  onClose,
  asDialog = false,
}) => {
  const contentRef = useRef<HTMLDivElement>(null);
  const [showSummary, setShowSummary] = useState(true);
  const [highlightedLines, setHighlightedLines] = useState<Set<number>>(new Set());

  useEffect(() => {
    // Only auto-scroll if we have line data and not opened from source list
    if (!isOpenedFromSourceList && citation?.chunk_lines_from && citation?.chunk_lines_to && contentRef.current) {
      const lines = new Set<number>();
      for (let i = citation.chunk_lines_from; i <= citation.chunk_lines_to; i++) {
        lines.add(i);
      }
      setHighlightedLines(lines);

      // Scroll to the first highlighted line
      setTimeout(() => {
        const firstHighlightedElement = contentRef.current?.querySelector(`[data-line="${citation.chunk_lines_from}"]`);
        if (firstHighlightedElement) {
          firstHighlightedElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }, 100);
    } else {
      setHighlightedLines(new Set());
    }
  }, [citation, isOpenedFromSourceList]);


  const renderContent = () => {
    if (!sourceContent) {
      return <p className="text-muted-foreground">No content available for this source.</p>;
    }

    const lines = sourceContent.split('\n');
    
    return (
      <div ref={contentRef} className="font-mono text-sm">
        {lines.map((line, index) => {
          const lineNumber = index + 1;
          const isHighlighted = highlightedLines.has(lineNumber);
          
          return (
            <div
              key={lineNumber}
              data-line={lineNumber}
              className={`flex transition-all duration-200 hover:bg-muted/50 group ${isHighlighted ? 'bg-accent hover:bg-accent/80' : ''}`}
            >
              <span className="select-none text-muted-foreground pr-4 w-12 text-right flex-shrink-0 transition-all duration-200 group-hover:text-foreground">
                {lineNumber}
              </span>
              <pre className="flex-1 whitespace-pre-wrap break-words transition-all duration-200 group-hover:text-foreground">
                {line || ' '}
              </pre>
            </div>
          );
        })}
      </div>
    );
  };

  if (!citation) {
    return null;
  }

  const content = (
    <div className={`flex flex-col h-full ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-border bg-card transition-all duration-300 hover:bg-muted/50 hover:border-border/80">
        <div className="flex items-start space-x-3 group">
          <div className="flex-shrink-0 mt-1">
            {getSourceIcon(citation.source_type, "w-5 h-5 transition-all duration-200 group-hover:scale-110 group-hover:text-primary")}
          </div>
          <div className="flex-1 min-w-0">
            <h3 className="font-medium text-card-foreground truncate transition-all duration-200 group-hover:text-primary cursor-default">
              {citation.source_title}
            </h3>
            {citation.chunk_lines_from && citation.chunk_lines_to && !isOpenedFromSourceList && (
              <p className="text-sm text-muted-foreground mt-1 transition-all duration-200 group-hover:text-foreground cursor-default">
                Lines {citation.chunk_lines_from} - {citation.chunk_lines_to}
              </p>
            )}
            {sourceUrl && (
              <a
                href={sourceUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="text-sm text-primary hover:text-primary/80 hover:underline mt-1 inline-block transition-all duration-200 hover:scale-105 focus:ring-2 focus:ring-primary/20 rounded"
              >
                View original source
              </a>
            )}
          </div>
        </div>
      </div>

      {/* Summary Section */}
      {sourceSummary && (
        <div className="border-b border-border bg-muted transition-all duration-300 hover:bg-muted/80">
          <button
            onClick={() => setShowSummary(!showSummary)}
            className="w-full px-4 py-3 flex items-center justify-between hover:bg-muted/80 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary/20 group active:scale-[0.99]"
          >
            <span className="font-medium text-sm text-muted-foreground transition-all duration-200 group-hover:text-foreground">Summary</span>
            {showSummary ? (
              <ChevronUp className="w-4 h-4 text-muted-foreground transition-all duration-200 group-hover:text-foreground group-hover:scale-110" />
            ) : (
              <ChevronDown className="w-4 h-4 text-muted-foreground transition-all duration-200 group-hover:text-foreground group-hover:scale-110" />
            )}
          </button>
          {showSummary && (
            <div className="px-4 pb-3 transition-all duration-300">
              <p className="text-sm text-muted-foreground transition-all duration-200 hover:text-foreground cursor-default">{sourceSummary}</p>
            </div>
          )}
        </div>
      )}

      {/* Excerpt if available */}
      {citation.excerpt && !isOpenedFromSourceList && (
        <Card className="m-4 p-3 bg-accent border-border transition-all duration-300 hover:bg-accent/80 hover:border-border/80 hover:shadow-md group cursor-default">
          <p className="text-sm font-medium text-accent-foreground mb-1 transition-all duration-200 group-hover:text-foreground">Relevant Excerpt:</p>
          <p className="text-sm text-accent-foreground transition-all duration-200 group-hover:text-foreground">{citation.excerpt}</p>
        </Card>
      )}

      {/* Content */}
      <ScrollArea className="flex-1">
        <div className="p-4">
          {renderContent()}
        </div>
      </ScrollArea>
    </div>
  );

  // Return as dialog if asDialog prop is true
  if (asDialog) {
    return (
      <Dialog open={isOpen} onOpenChange={(open) => !open && onClose?.()}>
        <DialogContent className="max-w-4xl h-[80vh] p-0 overflow-hidden">
          {content}
        </DialogContent>
      </Dialog>
    );
  }

  // Return inline content
  return content;
};

export default SourceContentViewer;