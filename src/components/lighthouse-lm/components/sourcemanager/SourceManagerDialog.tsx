import React, { use<PERSON>allback, useEffect, useRef, useState } from 'react';
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuLabel, DropdownMenuSeparator } from '@/components/ui/dropdown-menu';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { TooltipProvider } from '@/components/ui/tooltip';
import { useToast } from '@/components/ui/use-toast';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  X, Search, Filter, CheckSquare,
  Upload, FolderOpen, AlertCircle, Grid, List,
  Plus, GitBranch, Download, Eye, Trash2,
  BarChart3, Network, FileText
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useSourceManager } from '../../hooks/useSourceManager';
import { SourceList } from './SourceList';
import { Source } from '../../../../services/insights-api';
import SourceDiagramGenerator from '../mermaid/SourceDiagramGenerator';
import { SourceDiagramService, SourceDiagram, DiagramType } from '../../../../services/sourceDiagramService';
import DiagramUpdateNotifications from '../diagrams/DiagramUpdateNotifications';
import { useDiagramUpdates } from '../../hooks/useDiagramUpdates';
import {
  useNotification,
  withNotification,
  copyToClipboard,
  NOTIFICATION_MESSAGES
} from '../../utils/notificationUtils';
import {
  useErrorHandler,
  useAsyncOperation,
  ErrorClassifier,
  ErrorMessages,
  FileErrorHandler,
  ApiErrorHandler
} from '../../utils/errorHandlingUtils';
import {
  useExport
} from '../../utils/exportUtils';
import {
  useBulkOperations
} from '../../utils/bulkOperationsUtils';

interface SourceManagerDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  sources: Source[];
  onSourceSelect: (source: Source) => void;
  onSourceDelete: (sourceId: string) => Promise<void>;
  onSourceRename: (source: Source) => Promise<void>;
  onBulkDelete: (sourceIds: string[]) => Promise<void>;
  onBulkExport: (sourceIds: string[]) => Promise<void>;
  onBulkArchive: (sourceIds: string[]) => Promise<void>;
  onSourceUpload?: (files: FileList) => Promise<void>;
  onSourceImportUrl?: (url: string) => Promise<void>;
  onBulkDiagramGenerate?: (sourceIds: string[], diagramType: DiagramType) => Promise<void>;
  isLoading?: boolean;
  error?: string | null;
  notebookId?: string;
}

interface SourceManagerProps extends Omit<SourceManagerDialogProps, 'open' | 'onOpenChange'> {
  className?: string;
}

interface DiagramMappingVisualizationProps {
  sources: Source[];
  diagrams: SourceDiagram[];
  onDiagramSelect: (diagram: SourceDiagram) => void;
  onDiagramDelete: (diagramId: number) => Promise<void>;
  onDiagramExport: (diagram: SourceDiagram, format: 'svg' | 'png' | 'json') => Promise<void>;
  onDiagramRegenerate: (diagram: SourceDiagram) => Promise<void>;
  onBulkDiagramDelete: (diagramIds: number[]) => Promise<void>;
  onBulkDiagramExport: (diagrams: SourceDiagram[], format: 'svg' | 'png' | 'json') => Promise<void>;
}

const DiagramMappingVisualization: React.FC<DiagramMappingVisualizationProps> = ({
  sources,
  diagrams,
  onDiagramSelect,
  onDiagramDelete,
  onDiagramExport,
  onDiagramRegenerate,
  onBulkDiagramDelete,
  onBulkDiagramExport
}) => {
  const { showSuccess, showError } = useNotification();
  const [selectedDiagram, setSelectedDiagram] = useState<SourceDiagram | null>(null);
  const [selectedDiagrams, setSelectedDiagrams] = useState<Set<number>>(new Set());
  const [showBulkActions, setShowBulkActions] = useState(false);
  const [diagramFreshness, setDiagramFreshness] = useState<Map<number, boolean>>(new Map());
  const [diagramSearchTerm, setDiagramSearchTerm] = useState('');
  const [diagramTypeFilter, setDiagramTypeFilter] = useState<DiagramType | 'all'>('all');
  const [showOutdatedOnly, setShowOutdatedOnly] = useState(false);

  const getSourcesForDiagram = (diagram: SourceDiagram) => {
    return sources.filter(source => diagram.source_ids.includes(source.id));
  };

  const getDiagramsForSource = (sourceId: string) => {
    return diagrams.filter(diagram => diagram.source_ids.includes(sourceId));
  };

  // Check diagram freshness on mount
  useEffect(() => {
    const checkFreshness = async () => {
      const freshnessMap = new Map<number, boolean>();
      for (const diagram of diagrams) {
        try {
          const freshness = await SourceDiagramService.checkDiagramFreshness(diagram.id);
          freshnessMap.set(diagram.id, freshness.is_fresh);
        } catch (error) {
          // If we can't check freshness, assume it's fresh
          freshnessMap.set(diagram.id, true);
        }
      }
      setDiagramFreshness(freshnessMap);
    };

    if (diagrams.length > 0) {
      checkFreshness();
    }
  }, [diagrams]);

  const toggleDiagramSelection = (diagramId: number) => {
    const newSelection = new Set(selectedDiagrams);
    if (newSelection.has(diagramId)) {
      newSelection.delete(diagramId);
    } else {
      newSelection.add(diagramId);
    }
    setSelectedDiagrams(newSelection);
  };

  const selectAllDiagrams = () => {
    setSelectedDiagrams(new Set(diagrams.map(d => d.id)));
  };

  const deselectAllDiagrams = () => {
    setSelectedDiagrams(new Set());
  };

  // Filter diagrams based on search and filters
  const filteredDiagrams = diagrams.filter(diagram => {
    // Search filter
    if (diagramSearchTerm) {
      const searchLower = diagramSearchTerm.toLowerCase();
      const matchesSearch =
        diagram.diagram_type.toLowerCase().includes(searchLower) ||
        diagram.id.toString().includes(searchLower) ||
        getSourcesForDiagram(diagram).some(source =>
          source.title?.toLowerCase().includes(searchLower)
        );
      if (!matchesSearch) return false;
    }

    // Type filter
    if (diagramTypeFilter !== 'all' && diagram.diagram_type !== diagramTypeFilter) {
      return false;
    }

    // Freshness filter
    if (showOutdatedOnly) {
      const isFresh = diagramFreshness.get(diagram.id) ?? true;
      if (isFresh) return false;
    }

    return true;
  });

  const handleExport = async (diagram: SourceDiagram, format: 'svg' | 'png' | 'json') => {
    try {
      await onDiagramExport(diagram, format);
      showSuccess({ title: `Diagram exported as ${format.toUpperCase()}` });
    } catch (error) {
      toast({
        title: 'Export failed',
        description: `Failed to export diagram as ${format}`,
        variant: 'destructive'
      });
    }
  };

  const handleDelete = async (diagramId: number) => {
    try {
      await onDiagramDelete(diagramId);
      showSuccess({ title: 'Diagram deleted successfully' });
    } catch (error) {
      toast({
        title: 'Delete failed',
        description: 'Failed to delete diagram',
        variant: 'destructive'
      });
    }
  };

  const handleRegenerate = async (diagram: SourceDiagram) => {
    try {
      await onDiagramRegenerate(diagram);
      toast({ title: 'Diagram regenerated successfully' });
    } catch (error) {
      toast({
        title: 'Regeneration failed',
        description: 'Failed to regenerate diagram',
        variant: 'destructive'
      });
    }
  };

  const handleBulkDelete = async () => {
    if (selectedDiagrams.size === 0) return;

    try {
      await onBulkDiagramDelete(Array.from(selectedDiagrams));
      setSelectedDiagrams(new Set());
      toast({ title: `Deleted ${selectedDiagrams.size} diagrams successfully` });
    } catch (error) {
      toast({
        title: 'Bulk delete failed',
        description: 'Failed to delete selected diagrams',
        variant: 'destructive'
      });
    }
  };

  const handleBulkExport = async (format: 'svg' | 'png' | 'json') => {
    if (selectedDiagrams.size === 0) return;

    try {
      const selectedDiagramObjects = diagrams.filter(d => selectedDiagrams.has(d.id));
      await onBulkDiagramExport(selectedDiagramObjects, format);
      toast({ title: `Exported ${selectedDiagrams.size} diagrams as ${format.toUpperCase()}` });
    } catch (error) {
      toast({
        title: 'Bulk export failed',
        description: `Failed to export diagrams as ${format}`,
        variant: 'destructive'
      });
    }
  };

  return (
    <div className="space-y-6">
      {/* Diagram Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-muted/50 rounded-lg p-4">
          <div className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4 text-primary" />
            <span className="text-sm font-medium">Total Diagrams</span>
          </div>
          <div className="text-2xl font-bold mt-1">{diagrams.length}</div>
          <div className="text-xs text-muted-foreground mt-1">
            {filteredDiagrams.length !== diagrams.length && `${filteredDiagrams.length} filtered`}
          </div>
        </div>
        <div className="bg-muted/50 rounded-lg p-4">
          <div className="flex items-center gap-2">
            <Network className="h-4 w-4 text-primary" />
            <span className="text-sm font-medium">Connected Sources</span>
          </div>
          <div className="text-2xl font-bold mt-1">
            {new Set(diagrams.flatMap(d => d.source_ids)).size}
          </div>
        </div>
        <div className="bg-muted/50 rounded-lg p-4">
          <div className="flex items-center gap-2">
            <FileText className="h-4 w-4 text-primary" />
            <span className="text-sm font-medium">Avg Confidence</span>
          </div>
          <div className="text-2xl font-bold mt-1">
            {diagrams.length > 0
              ? Math.round(diagrams.reduce((acc, d) => acc + d.analysis_metadata.analysis_confidence, 0) / diagrams.length * 100)
              : 0}%
          </div>
        </div>
        <div className="bg-muted/50 rounded-lg p-4">
          <div className="flex items-center gap-2">
            <AlertCircle className="h-4 w-4 text-orange-500" />
            <span className="text-sm font-medium">Outdated</span>
          </div>
          <div className="text-2xl font-bold mt-1">
            {Array.from(diagramFreshness.values()).filter(fresh => !fresh).length}
          </div>
          <div className="text-xs text-muted-foreground mt-1">
            Need regeneration
          </div>
        </div>
      </div>

      {/* Diagram Management Header */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Source-Diagram Mappings</h3>
        </div>

        {/* Diagram Search and Filters */}
        <div className="flex items-center gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search diagrams..."
              value={diagramSearchTerm}
              onChange={(e) => setDiagramSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <Select value={diagramTypeFilter} onValueChange={(value) => setDiagramTypeFilter(value as DiagramType | 'all')}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="All types" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="flowchart">Flowchart</SelectItem>
              <SelectItem value="mindmap">Mind Map</SelectItem>
              <SelectItem value="sequence">Sequence</SelectItem>
            </SelectContent>
          </Select>
          <Button
            variant={showOutdatedOnly ? "default" : "outline"}
            size="sm"
            onClick={() => setShowOutdatedOnly(!showOutdatedOnly)}
          >
            <AlertCircle className="h-4 w-4 mr-2" />
            Outdated Only
          </Button>
        </div>

        {/* Bulk Actions Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowBulkActions(!showBulkActions)}
              className={cn(showBulkActions && "bg-accent")}
            >
              <CheckSquare className="h-4 w-4 mr-2" />
              Select
            </Button>
            {showBulkActions && selectedDiagrams.size > 0 && (
              <>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="sm">
                      <Download className="h-4 w-4 mr-2" />
                      Export ({selectedDiagrams.size})
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => handleBulkExport('svg')}>
                      Export as SVG
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleBulkExport('png')}>
                      Export as PNG
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleBulkExport('json')}>
                      Export as JSON
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleBulkDelete}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete ({selectedDiagrams.size})
                </Button>
              </>
            )}
            {showBulkActions && (
              <div className="flex items-center gap-1">
                <Button variant="ghost" size="sm" onClick={selectAllDiagrams}>
                  Select All
                </Button>
                <Button variant="ghost" size="sm" onClick={deselectAllDiagrams}>
                  Clear
                </Button>
              </div>
            )}
          </div>
        </div>
        <ScrollArea className="h-96 border rounded-lg">
          <div className="p-4 space-y-4">
            {filteredDiagrams.length === 0 ? (
              diagrams.length === 0 ? (
                <div className="text-center text-muted-foreground py-8">
                  <Network className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>No diagrams generated yet</p>
                  <p className="text-sm">Generate diagrams from your sources to see mappings here</p>
                </div>
              ) : (
                <div className="text-center text-muted-foreground py-8">
                  <Network className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>No diagrams match your filters</p>
                  <p className="text-sm">Try adjusting your search or filter criteria</p>
                </div>
              )
            ) : (
              filteredDiagrams.map(diagram => {
                const diagramSources = getSourcesForDiagram(diagram);
                const isSelected = selectedDiagrams.has(diagram.id);
                const isFresh = diagramFreshness.get(diagram.id) ?? true;

                return (
                  <div key={diagram.id} className={cn(
                    "border rounded-lg p-4 space-y-3 transition-colors",
                    isSelected && "bg-accent/50 border-primary",
                    !isFresh && "border-orange-200 bg-orange-50/50"
                  )}>
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-3">
                        {showBulkActions && (
                          <input
                            type="checkbox"
                            checked={isSelected}
                            onChange={() => toggleDiagramSelection(diagram.id)}
                            className="mt-1"
                          />
                        )}
                        <div className="space-y-1">
                          <div className="flex items-center gap-2">
                            <Badge variant="outline">{diagram.diagram_type}</Badge>
                            <span className="text-sm font-medium">
                              Diagram #{diagram.id}
                            </span>
                            {!isFresh && (
                              <Badge variant="secondary" className="text-xs bg-orange-100 text-orange-800">
                                Outdated
                              </Badge>
                            )}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            Created: {new Date(diagram.created_at).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onDiagramSelect(diagram)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        {!isFresh && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleRegenerate(diagram)}
                            title="Regenerate outdated diagram"
                          >
                            <GitBranch className="h-4 w-4" />
                          </Button>
                        )}
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <Download className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Export with Attribution</DropdownMenuLabel>
                            <DropdownMenuItem onClick={() => handleExport(diagram, 'svg')}>
                              Export as SVG
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleExport(diagram, 'png')}>
                              Export as PNG
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleExport(diagram, 'json')}>
                              Export as JSON
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDelete(diagram.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    {/* Source connections */}
                    <div className="space-y-2">
                      <div className="text-sm font-medium">Connected Sources ({diagramSources.length})</div>
                      <div className="flex flex-wrap gap-2">
                        {diagramSources.map(source => (
                          <Badge key={source.id} variant="secondary" className="text-xs">
                            {source.title || 'Untitled'}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    {/* Analysis metadata */}
                    <div className="grid grid-cols-3 gap-4 text-xs text-muted-foreground">
                      <div>
                        <span className="font-medium">Concepts:</span> {diagram.analysis_metadata.concepts_extracted}
                      </div>
                      <div>
                        <span className="font-medium">Relations:</span> {diagram.analysis_metadata.relationships_found}
                      </div>
                      <div>
                        <span className="font-medium">Confidence:</span> {Math.round(diagram.analysis_metadata.analysis_confidence * 100)}%
                      </div>
                    </div>

                    {/* Generation parameters */}
                    <div className="text-xs text-muted-foreground border-t pt-2">
                      <div className="flex items-center justify-between">
                        <span>Generation Settings:</span>
                        <span>
                          Max: {diagram.analysis_metadata.generation_parameters.max_concepts} concepts,
                          Depth: {diagram.analysis_metadata.generation_parameters.relationship_depth},
                          Min Confidence: {Math.round(diagram.analysis_metadata.generation_parameters.minimum_confidence * 100)}%
                        </span>
                      </div>
                    </div>
                  </div>
                );
              })
            )}
          </div>
        </ScrollArea>
      </div>
    </div>
  );
};

const SourceManager: React.FC<SourceManagerProps> = ({
  sources,
  onSourceSelect,
  onSourceDelete,
  onSourceRename,
  onBulkDelete,
  onBulkExport,
  onBulkArchive,
  onBulkDiagramGenerate,
  onSourceUpload,
  onSourceImportUrl,
  isLoading = false,
  error = null,
  notebookId,
  className
}) => {
  const { showSuccess, showError } = useNotification();
  const [isDragging, setIsDragging] = useState(false);
  const [importUrl, setImportUrl] = useState('');
  const [showImportDialog, setShowImportDialog] = useState(false);
  const [showDiagramGenerator, setShowDiagramGenerator] = useState(false);
  const [sourceDiagrams, setSourceDiagrams] = useState<SourceDiagram[]>([]);
  const [isLoadingDiagrams, setIsLoadingDiagrams] = useState(false);
  const [activeTab, setActiveTab] = useState('sources');

  const fileInputRef = useRef<HTMLInputElement>(null);
  const dropZoneRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  const {
    state,
    actions,
    filteredAndSortedSources
  } = useSourceManager(sources);

  // Load diagrams when component mounts or notebook changes
  useEffect(() => {
    if (notebookId) {
      loadSourceDiagrams();
    }
  }, [notebookId]);

  const loadSourceDiagrams = async () => {
    if (!notebookId) return;

    setIsLoadingDiagrams(true);
    try {
      const diagrams = await SourceDiagramService.getNotebookSourceDiagrams(notebookId);
      setSourceDiagrams(diagrams);
    } catch (error) {
      toast({
        title: 'Error loading diagrams',
        description: 'Failed to load source diagrams',
        variant: 'destructive'
      });
    } finally {
      setIsLoadingDiagrams(false);
    }
  };

  const handleBulkDiagramGenerate = async (diagramType: DiagramType) => {
    const selectedSourceIds = Array.from(state.selectedSources);
    if (selectedSourceIds.length === 0) {
      toast({
        title: 'No sources selected',
        description: 'Please select sources to generate diagrams from',
        variant: 'destructive'
      });
      return;
    }

    try {
      if (onBulkDiagramGenerate) {
        await onBulkDiagramGenerate(selectedSourceIds, diagramType);
        await loadSourceDiagrams(); // Refresh diagrams
        toast({ title: `Generated ${diagramType} diagram from ${selectedSourceIds.length} sources` });
      }
    } catch (error) {
      toast({
        title: 'Generation failed',
        description: 'Failed to generate diagram from selected sources',
        variant: 'destructive'
      });
    }
  };

  const handleDiagramSelect = (diagram: SourceDiagram) => {
    // This could open a diagram viewer or navigate to diagram details
    toast({ title: `Selected diagram #${diagram.id}` });
  };

  const handleDiagramDelete = async (diagramId: number) => {
    try {
      await SourceDiagramService.deleteSourceDiagram(diagramId);
      await loadSourceDiagrams(); // Refresh diagrams
    } catch (error) {
      throw error; // Re-throw to be handled by the component
    }
  };

  const handleDiagramExport = async (diagram: SourceDiagram, format: 'svg' | 'png' | 'json') => {
    try {
      // Use enhanced export service for better attribution
      const { EnhancedExportService } = await import('../../../../services/enhancedExportService');
      await EnhancedExportService.downloadDiagramWithAttribution(diagram, format as any, {
        includeSourceMetadata: true,
        includeAttribution: true,
        includeComments: format === 'json'
      });
    } catch (error) {
      // Fallback to basic export
      try {
        await SourceDiagramService.exportSourceDiagramWithAttribution(diagram.id, format);
      } catch (fallbackError) {
        throw fallbackError; // Re-throw to be handled by the component
      }
    }
  };

  const handleDiagramRegenerate = async (diagram: SourceDiagram) => {
    try {
      // Get the sources used in the original diagram
      const diagramSources = sources.filter(source => diagram.source_ids.includes(source.id));

      if (diagramSources.length === 0) {
        toast({
          title: 'Cannot regenerate',
          description: 'Original sources are no longer available',
          variant: 'destructive'
        });
        return;
      }

      // Use the original generation parameters to regenerate
      const options = diagram.analysis_metadata.generation_parameters;

      if (onBulkDiagramGenerate) {
        await onBulkDiagramGenerate(diagram.source_ids, options.diagram_type);
        await loadSourceDiagrams(); // Refresh diagrams
        toast({ title: 'Diagram regenerated successfully' });
      }
    } catch (error) {
      throw error; // Re-throw to be handled by the component
    }
  };

  const handleBulkDiagramDelete = async (diagramIds: number[]) => {
    try {
      await Promise.all(diagramIds.map(id => SourceDiagramService.deleteSourceDiagram(id)));
      await loadSourceDiagrams(); // Refresh diagrams
    } catch (error) {
      throw error; // Re-throw to be handled by the component
    }
  };

  const handleBulkDiagramExport = async (diagrams: SourceDiagram[], format: 'svg' | 'png' | 'json') => {
    try {
      // Use enhanced bulk export service
      const { EnhancedExportService } = await import('../../../../services/enhancedExportService');
      await EnhancedExportService.bulkExportDiagrams(diagrams, format as any, {
        includeSourceMetadata: true,
        includeAttribution: true,
        includeComments: format === 'json'
      });
    } catch (error) {
      // Fallback to basic bulk export
      try {
        await Promise.all(diagrams.map(diagram =>
          SourceDiagramService.exportSourceDiagramWithAttribution(diagram.id, format)
        ));
      } catch (fallbackError) {
        throw fallbackError; // Re-throw to be handled by the component
      }
    }
  };

  const handleDiagramGenerated = (diagram: any) => {
    loadSourceDiagrams(); // Refresh diagrams list
    setShowDiagramGenerator(false);
    toast({ title: 'Diagram generated and saved successfully' });
  };

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.currentTarget === e.target) {
      setIsDragging(false);
    }
  }, []);

  const handleDrop = useCallback(async (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    const files = e.dataTransfer.files;
    if (files.length > 0 && onSourceUpload) {
      try {
        await onSourceUpload(files);
        toast({ title: `Uploaded ${files.length} file(s) successfully` });
      } catch (error) {
        toast({
          title: 'Error',
          description: 'Failed to upload files',
          variant: 'destructive'
        });
      }
    }
  }, [onSourceUpload, toast]);

  const handleFileSelect = useCallback(async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0 && onSourceUpload) {
      try {
        await onSourceUpload(files);
        toast({ title: `Uploaded ${files.length} file(s) successfully` });
      } catch (error) {
        toast({
          title: 'Error',
          description: 'Failed to upload files',
          variant: 'destructive'
        });
      }
    }
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, [onSourceUpload, toast]);

  const handleImportUrl = useCallback(async () => {
    if (!importUrl.trim() || !onSourceImportUrl) return;

    try {
      await onSourceImportUrl(importUrl);
      toast({ title: 'URL imported successfully' });
      setImportUrl('');
      setShowImportDialog(false);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to import URL',
        variant: 'destructive'
      });
    }
  }, [importUrl, onSourceImportUrl, toast]);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.metaKey || e.ctrlKey) {
        switch (e.key) {
          case 'a':
            e.preventDefault();
            actions.selectAllSources();
            break;
          case 'f':
            e.preventDefault();
            searchInputRef.current?.focus();
            break;
          case 'd':
            if (state.selectedSources.size > 0) {
              e.preventDefault();
              actions.handleBulkDelete(Array.from(state.selectedSources), onBulkDelete);
            }
            break;
          case 'e':
            if (state.selectedSources.size > 0) {
              e.preventDefault();
              actions.handleBulkExport(Array.from(state.selectedSources), onBulkExport);
            }
            break;
        }
      } else if (e.key === 'Escape') {
        actions.deselectAllSources();
        actions.toggleBulkActions();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [actions, state.selectedSources.size]);

  return (
    <TooltipProvider>
      <div
        className={cn(
          "flex flex-col h-full",
          className
        )}
        ref={dropZoneRef}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        {/* Header with search and controls */}
        <div className="flex-shrink-0 space-y-4 p-4 border-b">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  ref={searchInputRef}
                  placeholder="Search sources..."
                  value={state.filters.searchTerm}
                  onChange={(e) => actions.updateFilters({ searchTerm: e.target.value })}
                  className="w-64 pl-10"
                />
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={actions.toggleBulkActions}
                className={cn(
                  state.showBulkActions && "bg-accent"
                )}
              >
                <CheckSquare className="h-4 w-4 mr-2" />
                Select
              </Button>
            </div>

            <div className="flex items-center gap-2">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    <Filter className="h-4 w-4 mr-2" />
                    Filter
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>View Mode</DropdownMenuLabel>
                  <DropdownMenuItem onClick={() => actions.setViewMode('list')}>
                    <List className="h-4 w-4 mr-2" />
                    List
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => actions.setViewMode('grid')}>
                    <Grid className="h-4 w-4 mr-2" />
                    Grid
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    <Plus className="h-4 w-4 mr-2" />
                    Add
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => fileInputRef.current?.click()}>
                    <Upload className="h-4 w-4 mr-2" />
                    Upload Files
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setShowImportDialog(true)}>
                    <FolderOpen className="h-4 w-4 mr-2" />
                    Import URL
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          {/* Bulk actions bar */}
          {state.showBulkActions && state.selectedSources.size > 0 && (
            <div className="flex items-center justify-between p-3 bg-accent rounded-lg">
              <span className="text-sm font-medium">
                {state.selectedSources.size} selected
              </span>
              <div className="flex items-center gap-2">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="sm">
                      <GitBranch className="h-4 w-4 mr-2" />
                      Generate Diagram
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>Diagram Type</DropdownMenuLabel>
                    <DropdownMenuItem onClick={() => handleBulkDiagramGenerate('flowchart')}>
                      <Network className="h-4 w-4 mr-2" />
                      Flowchart
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleBulkDiagramGenerate('mindmap')}>
                      <BarChart3 className="h-4 w-4 mr-2" />
                      Mind Map
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleBulkDiagramGenerate('sequence')}>
                      <FileText className="h-4 w-4 mr-2" />
                      Sequence Diagram
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => setShowDiagramGenerator(true)}>
                      <Eye className="h-4 w-4 mr-2" />
                      Advanced Options
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => actions.handleBulkDelete(Array.from(state.selectedSources), onBulkDelete)}
                >
                  Delete
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => actions.handleBulkExport(Array.from(state.selectedSources), onBulkExport)}
                >
                  Export
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => actions.handleBulkArchive(Array.from(state.selectedSources), onBulkArchive)}
                >
                  Archive
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* Error display */}
        {error && (
          <Alert variant="destructive" className="mx-4 mt-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Drag overlay */}
        {isDragging && (
          <div className="absolute inset-0 bg-primary/10 border-2 border-dashed border-primary rounded-lg flex items-center justify-center z-10">
            <div className="text-center">
              <Upload className="h-8 w-8 mx-auto mb-2 text-primary" />
              <p className="text-sm font-medium text-primary">Drop files to upload</p>
            </div>
          </div>
        )}

        {/* Main content with tabs */}
        <div className="flex-1 min-h-0">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="sources" className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Sources ({sources.length})
              </TabsTrigger>
              <TabsTrigger value="diagrams" className="flex items-center gap-2">
                <Network className="h-4 w-4" />
                Diagrams ({sourceDiagrams.length})
              </TabsTrigger>
              <TabsTrigger value="updates" className="flex items-center gap-2">
                <AlertCircle className="h-4 w-4" />
                Updates
                {getOutdatedCount() > 0 && (
                  <Badge variant="destructive" className="text-xs ml-1">
                    {getOutdatedCount()}
                  </Badge>
                )}
              </TabsTrigger>
            </TabsList>

            <TabsContent value="sources" className="flex-1 min-h-0 mt-4">
              <SourceList
                sources={filteredAndSortedSources}
                selectedSources={state.selectedSources}
                viewMode={state.viewMode}
                showActions={state.showBulkActions}
                onSourceSelect={actions.toggleSourceSelection}
                onSourceClick={onSourceSelect}
                onSourceDelete={(sourceId) => actions.handleSourceDelete(sourceId, onSourceDelete)}
                onSourceRename={(source) => actions.handleSourceRename(source, onSourceRename)}
                isLoading={isLoading}
              />
            </TabsContent>

            <TabsContent value="diagrams" className="flex-1 min-h-0 mt-4">
              <DiagramMappingVisualization
                sources={sources}
                diagrams={sourceDiagrams}
                onDiagramSelect={handleDiagramSelect}
                onDiagramDelete={handleDiagramDelete}
                onDiagramExport={handleDiagramExport}
                onDiagramRegenerate={handleDiagramRegenerate}
                onBulkDiagramDelete={handleBulkDiagramDelete}
                onBulkDiagramExport={handleBulkDiagramExport}
              />
            </TabsContent>

            <TabsContent value="updates" className="flex-1 min-h-0 mt-4">
              {props.notebookId && (
                <DiagramUpdateNotifications 
                  notebookId={props.notebookId}
                  className="h-full"
                />
              )}
            </TabsContent>
          </Tabs>
        </div>

        {/* Hidden file input */}
        <input
          ref={fileInputRef}
          type="file"
          multiple
          className="hidden"
          onChange={handleFileSelect}
          accept=".pdf,.doc,.docx,.txt,.md,.csv,.json"
        />

        {/* Import URL Dialog */}
        {showImportDialog && (
          <Dialog open={showImportDialog} onOpenChange={setShowImportDialog}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Import from URL</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <Input
                  placeholder="Enter URL..."
                  value={importUrl}
                  onChange={(e) => setImportUrl(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      handleImportUrl();
                    }
                  }}
                />
                <div className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => setShowImportDialog(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleImportUrl} disabled={!importUrl.trim()}>
                    Import
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        )}

        {/* Diagram Generator Dialog */}
        <SourceDiagramGenerator
          sources={sources.filter(source => state.selectedSources.has(source.id))}
          onDiagramGenerated={handleDiagramGenerated}
          onError={(error) => toast({ title: 'Error', description: error, variant: 'destructive' })}
          isOpen={showDiagramGenerator}
          onOpenChange={setShowDiagramGenerator}
          notebookId={notebookId}
        />
      </div>
    </TooltipProvider>
  );
};

const SourceManagerDialog: React.FC<SourceManagerDialogProps> = ({
  open,
  onOpenChange,
  ...props
}) => {
  const [sourceDiagrams, setSourceDiagrams] = useState<SourceDiagram[]>([]);
  
  // Diagram updates hook
  const { getOutdatedCount } = useDiagramUpdates({ 
    notebookId: props.notebookId,
    autoMonitoring: open 
  });

  // Load diagrams when dialog opens
  useEffect(() => {
    if (open && props.notebookId) {
      loadDiagrams();
    }
  }, [open, props.notebookId]);

  const loadDiagrams = async () => {
    if (!props.notebookId) return;

    try {
      const diagrams = await SourceDiagramService.getNotebookSourceDiagrams(props.notebookId);
      setSourceDiagrams(diagrams);
    } catch (error) {
      console.error('Failed to load diagrams:', error);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl max-h-[90vh] flex flex-col p-0">
        <DialogHeader className="flex-shrink-0 px-6 pt-6 pb-4 border-b">
          <DialogTitle className="flex items-center justify-between">
            <span className="text-xl font-semibold">Source Manager</span>
            <div className="flex items-center gap-2">
              <Badge variant="secondary">{props.sources.length} sources</Badge>
              <Badge variant="outline">{sourceDiagrams.length} diagrams</Badge>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onOpenChange(false)}
                className="h-8 w-8 p-0"
                aria-label="Close dialog"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </DialogTitle>
        </DialogHeader>
        <div className="flex-1 min-h-0 px-6 pb-6">
          <SourceManager {...props} className="h-full" />
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default SourceManagerDialog;
export { SourceManager, type Source, type SourceManagerProps };