import React, { useState, useRef } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Upload, Globe, Youtube, FileText, Mic, Link, Loader2, X } from 'lucide-react';
import { useFileUpload } from '../../hooks/useFileUpload';
import { useDocumentProcessing } from '../../hooks/useDocumentProcessing';
import { useSources } from '../../hooks/useSources';
import { useToast } from '../../hooks/use-toast';

interface AddSourcesDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  notebookId?: string;
}

const AddSourcesDialog: React.FC<AddSourcesDialogProps> = React.memo(({
  open,
  onOpenChange,
  notebookId,
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();
  
  // Debug logging for notebookId
  // console.log('[AddSourcesDialog] Rendering with notebookId:', notebookId);
  // console.log('[AddSourcesDialog] notebookId type:', typeof notebookId);
  // console.log('[AddSourcesDialog] Dialog open state:', open);
  
  // State for different input types
  const [selectedFiles, setSelectedFiles] = useState<FileList | null>(null);
  const [websiteUrl, setWebsiteUrl] = useState('');
  const [youtubeUrl, setYoutubeUrl] = useState('');
  const [pastedText, setPastedText] = useState('');
  const [textTitle, setTextTitle] = useState('');
  
  // Hooks
  const { uploadFile, validateFile, isUploading } = useFileUpload(notebookId);
  const { processDocument, processWebContent, processYouTubeVideo, isUploading: isProcessing } = useDocumentProcessing();
  const { addSource } = useSources(notebookId);
  
  const [activeTab, setActiveTab] = useState('upload');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      // Validate each file
      const validFiles = Array.from(files).filter(file => validateFile(file));
      if (validFiles.length > 0) {
        setSelectedFiles(files);
      }
    }
  };

  const handleUploadFiles = async () => {
    if (!selectedFiles) {
      toast({
        title: "No Files Selected",
        description: "Please select files to upload.",
        variant: "destructive",
      });
      return;
    }
    
    if (!notebookId) {
      console.error('Notebook ID is undefined when trying to upload files');
      toast({
        title: "Error",
        description: "Unable to upload files. Notebook ID is missing.",
        variant: "destructive",
      });
      return;
    }
    
    setIsSubmitting(true);
    try {
      for (let i = 0; i < selectedFiles.length; i++) {
        const file = selectedFiles[i];
        console.log(`Processing file ${i + 1}/${selectedFiles.length}: ${file.name}`);
        
        // Upload file
        console.log('Starting file upload...');
        const uploadResult = await uploadFile(file);
        console.log('File uploaded, result:', uploadResult);
        
        // Determine file type
        let fileType = 'text';
        if (file.type === 'application/pdf') {
          fileType = 'pdf';
        } else if (file.type.startsWith('audio/')) {
          fileType = 'audio';
        }
        console.log('File type determined:', fileType);
        
        // Process the document
        console.log('Starting document processing...');
        const processingResult = await processDocument(uploadResult.filePath, fileType);
        console.log('Document processed, result:', processingResult);
        
        // Add source to notebook
        console.log('Adding source to notebook...');
        await addSource({
          notebookId,
          title: file.name,
          type: fileType as any,
          content: processingResult.content,
          file_path: uploadResult.filePath,
          file_size: file.size,
          processing_status: 'completed',
          metadata: processingResult.metadata,
        });
        console.log('Source added successfully');
      }
      
      toast({
        title: "Sources Added",
        description: `Successfully added ${selectedFiles.length} source(s) to your notebook.`,
      });
      
      // Reset and close
      setSelectedFiles(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
      onOpenChange(false);
    } catch (error) {
      console.error('Failed to upload files:', error);
      toast({
        title: "Upload Failed",
        description: "Failed to upload one or more files. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleAddWebsite = async () => {
    if (!websiteUrl) {
      toast({
        title: "No URL Provided",
        description: "Please enter a website URL.",
        variant: "destructive",
      });
      return;
    }
    
    if (!notebookId) {
      console.error('Notebook ID is undefined when trying to add website');
      toast({
        title: "Error",
        description: "Unable to add website. Notebook ID is missing.",
        variant: "destructive",
      });
      return;
    }
    
    setIsSubmitting(true);
    try {
      // Process web content
      const result = await processWebContent(websiteUrl);
      
      // Add source to notebook
      await addSource({
        notebookId,
        title: websiteUrl,
        type: 'website',
        content: result.content,
        url: websiteUrl,
        processing_status: 'completed',
        metadata: result.metadata,
      });
      
      toast({
        title: "Website Added",
        description: "The website has been added to your notebook.",
      });
      
      // Reset and close
      setWebsiteUrl('');
      onOpenChange(false);
    } catch (error) {
      console.error('Failed to add website:', error);
      toast({
        title: "Failed to Add Website",
        description: "Could not process the website. Please check the URL and try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleAddYouTube = async () => {
    if (!youtubeUrl) {
      toast({
        title: "No URL Provided",
        description: "Please enter a YouTube URL.",
        variant: "destructive",
      });
      return;
    }
    
    if (!notebookId) {
      console.error('Notebook ID is undefined when trying to add YouTube video');
      toast({
        title: "Error",
        description: "Unable to add YouTube video. Notebook ID is missing.",
        variant: "destructive",
      });
      return;
    }
    
    setIsSubmitting(true);
    try {
      // Process YouTube video
      const result = await processYouTubeVideo(youtubeUrl);
      
      // Add source to notebook
      await addSource({
        notebookId,
        title: `YouTube: ${youtubeUrl}`,
        type: 'youtube',
        content: result.content,
        url: youtubeUrl,
        processing_status: 'completed',
        metadata: result.metadata,
      });
      
      toast({
        title: "YouTube Video Added",
        description: "The video transcript has been added to your notebook.",
      });
      
      // Reset and close
      setYoutubeUrl('');
      onOpenChange(false);
    } catch (error) {
      console.error('Failed to add YouTube video:', error);
      toast({
        title: "Failed to Add Video",
        description: "Could not process the YouTube video. Please check the URL and try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleAddText = async () => {
    if (!pastedText || !textTitle) {
      toast({
        title: "Missing Information",
        description: "Please provide both a title and text content.",
        variant: "destructive",
      });
      return;
    }
    
    if (!notebookId) {
      console.error('Notebook ID is undefined when trying to add text');
      toast({
        title: "Error",
        description: "Unable to add text. Notebook ID is missing.",
        variant: "destructive",
      });
      return;
    }
    
    setIsSubmitting(true);
    try {
      // Add text source directly
      await addSource({
        notebookId,
        title: textTitle,
        type: 'text',
        content: pastedText,
        processing_status: 'completed',
      });
      
      toast({
        title: "Text Added",
        description: "The text has been added to your notebook.",
      });
      
      // Reset and close
      setPastedText('');
      setTextTitle('');
      onOpenChange(false);
    } catch (error) {
      console.error('Failed to add text:', error);
      toast({
        title: "Failed to Add Text",
        description: "Could not add the text. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const removeSelectedFile = (index: number) => {
    if (!selectedFiles) return;
    const dt = new DataTransfer();
    Array.from(selectedFiles).forEach((file, i) => {
      if (i !== index) dt.items.add(file);
    });
    setSelectedFiles(dt.files);
    if (fileInputRef.current) {
      fileInputRef.current.files = dt.files;
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Add Sources to Notebook</DialogTitle>
        </DialogHeader>
        
        <Tabs value={activeTab} onValueChange={setActiveTab} className="mt-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="upload">
              <Upload className="w-4 h-4 mr-2" />
              Upload
            </TabsTrigger>
            <TabsTrigger value="website">
              <Globe className="w-4 h-4 mr-2" />
              Website
            </TabsTrigger>
            <TabsTrigger value="youtube">
              <Youtube className="w-4 h-4 mr-2" />
              YouTube
            </TabsTrigger>
            <TabsTrigger value="text">
              <FileText className="w-4 h-4 mr-2" />
              Text
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="upload" className="mt-4">
            <div className="space-y-4">
              <div>
                <input
                  ref={fileInputRef}
                  type="file"
                  multiple
                  accept=".pdf,.txt,.md,.csv,.json,.mp3,.wav,.ogg,.doc,.docx"
                  onChange={handleFileSelect}
                  className="hidden"
                />
                <div
                  onClick={() => fileInputRef.current?.click()}
                  className="border-2 border-dashed border-border rounded-lg p-8 text-center cursor-pointer hover:border-border/80 transition"
                >
                  <Upload className="w-12 h-12 text-muted-foreground mx-auto mb-3" />
                  <p className="text-sm text-muted-foreground">
                    Click to upload or drag and drop
                  </p>
                  <p className="text-xs text-muted-foreground mt-1">
                    PDF, TXT, Audio files (max 50MB)
                  </p>
                </div>
              </div>
              
              {selectedFiles && selectedFiles.length > 0 && (
                <div className="space-y-2">
                  <p className="text-sm font-medium text-foreground">
                    Selected files:
                  </p>
                  {Array.from(selectedFiles).map((file, index) => (
                    <div key={index} className="flex items-center justify-between p-2 bg-muted/50 rounded">
                      <span className="text-sm text-muted-foreground">{file.name}</span>
                      <button
                        onClick={() => removeSelectedFile(index)}
                        className="text-muted-foreground hover:text-foreground"
                      >
                        <X className="w-4 h-4" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
              
              <Button
                onClick={handleUploadFiles}
                disabled={!selectedFiles || selectedFiles.length === 0 || isSubmitting}
                className="w-full"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Uploading...
                  </>
                ) : (
                  <>
                    <Upload className="w-4 h-4 mr-2" />
                    Upload Files
                  </>
                )}
              </Button>
            </div>
          </TabsContent>
          
          <TabsContent value="website" className="mt-4">
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium text-foreground mb-1 block">
                  Website URL
                </label>
                <Input
                  type="url"
                  placeholder="https://example.com"
                  value={websiteUrl}
                  onChange={(e) => setWebsiteUrl(e.target.value)}
                />
              </div>
              
              <Button
                onClick={handleAddWebsite}
                disabled={!websiteUrl || isSubmitting}
                className="w-full"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>
                    <Globe className="w-4 h-4 mr-2" />
                    Add Website
                  </>
                )}
              </Button>
            </div>
          </TabsContent>
          
          <TabsContent value="youtube" className="mt-4">
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium text-foreground mb-1 block">
                  YouTube URL
                </label>
                <Input
                  type="url"
                  placeholder="https://youtube.com/watch?v=..."
                  value={youtubeUrl}
                  onChange={(e) => setYoutubeUrl(e.target.value)}
                />
              </div>
              
              <Button
                onClick={handleAddYouTube}
                disabled={!youtubeUrl || isSubmitting}
                className="w-full"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>
                    <Youtube className="w-4 h-4 mr-2" />
                    Add YouTube Video
                  </>
                )}
              </Button>
            </div>
          </TabsContent>
          
          <TabsContent value="text" className="mt-4">
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium text-foreground mb-1 block">
                  Title
                </label>
                <Input
                  placeholder="Enter a title for this text"
                  value={textTitle}
                  onChange={(e) => setTextTitle(e.target.value)}
                />
              </div>
              
              <div>
                <label className="text-sm font-medium text-foreground mb-1 block">
                  Text Content
                </label>
                <Textarea
                  placeholder="Paste or type your text here..."
                  value={pastedText}
                  onChange={(e) => setPastedText(e.target.value)}
                  rows={8}
                />
              </div>
              
              <Button
                onClick={handleAddText}
                disabled={!pastedText || !textTitle || isSubmitting}
                className="w-full"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Adding...
                  </>
                ) : (
                  <>
                    <FileText className="w-4 h-4 mr-2" />
                    Add Text
                  </>
                )}
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
});

AddSourcesDialog.displayName = 'AddSourcesDialog';

export default AddSourcesDialog;