import { Citation, MessageSegment } from './message';

export interface MessageContent {
  segments?: MessageSegment[];
  citations?: Citation[];
}

export interface ChatMessage {
  id: string | number;
  message?: {
    type?: 'human' | 'ai' | 'assistant' | 'user';
    content?: string | MessageContent;
    role?: 'human' | 'ai' | 'assistant' | 'user';
  };
  content?: string;
  role?: 'human' | 'ai' | 'assistant' | 'user';
  timestamp?: string;
}

export interface ExampleQuestion {
  question: string;
}

export interface Source {
  id: string;
  title: string;
  content?: string;
  processing_status?: 'pending' | 'processing' | 'completed' | 'error';
  file_type?: string;
  file_size?: number;
}

export interface ChatAreaProps {
  hasSource: boolean;
  notebookId?: string;
  notebook?: {
    id: string;
    title: string;
    description?: string;
    generation_status?: string;
    icon?: string;
    example_questions?: string[];
  } | null;
  onCitationClick?: (citation: Citation) => void;
}

export interface ChatMessagesProps {
  messages: ChatMessage[];
  pendingUserMessage: string | null;
  showAiLoading: boolean;
  messageAnimations: Set<string>;
  onCitationClick: (citation: Citation) => void;
  onSaveToNote: (content: string, notebookId?: string) => void;
  notebookId?: string;
  hasGeneratedDiagrams?: boolean;
  onRefineDiagram?: () => void;
}

export interface ChatInputProps {
  message: string;
  onMessageChange: (message: string) => void;
  onSendMessage: (messageText?: string) => void;
  onRefreshChat?: () => void;
  onToggleDiagramSuggestions?: () => void;
  isChatDisabled: boolean;
  isSending: boolean;
  pendingUserMessage: string | null;
  showAiLoading: boolean;
  sourceCount: number;
  shouldShowSuggestions: boolean;
  isGenerating?: boolean;
  exampleQuestions: string[];
  onExampleQuestionClick: (question: string) => void;
  getPlaceholderText: () => string;
}

export interface ChatHeaderProps {
  shouldShowRefreshButton: boolean;
  isDeletingChatHistory: boolean;
  isChatDisabled: boolean;
  onRefreshChat: () => void;
}

export interface EmptyChatStateProps {
  onAddSources: () => void;
}

export interface SourceDiagram {
  id: string;
  code: string;
  title: string;
  type: string;
  description?: string;
  source_id?: string;
  created_at?: string;
  updated_at?: string;
}

export interface ChatDiagramIntegration {
  showSuggestions: boolean;
  isGeneratingDiagram: boolean;
  isProcessingRefinement: boolean;
  generatedDiagrams: SourceDiagram[];
  hasGeneratedDiagrams: boolean;
  shouldShowSuggestions: boolean;
  hideSuggestions: () => void;
  toggleSuggestions: () => void;
  generateDiagramFromSuggestion: (suggestion: any) => Promise<SourceDiagram | null>;
  processRefinementCommand: (diagram: SourceDiagram, command: string, parameters?: Record<string, any>) => Promise<SourceDiagram | null>;
}

export type MessageType = 'human' | 'ai' | 'assistant' | 'user';
export type ProcessingStatus = 'pending' | 'processing' | 'completed' | 'error';
export type DiagramType = 'flowchart' | 'sequence' | 'mindmap' | 'timeline' | 'gantt';