import { useNotificationService as useNotification } from '../services/notification';
import { useErrorHandler } from './error';
import { exportToFile as commonExportToFile } from './common';

/**
 * Consolidated export functionality utilities
 */

export type ExportFormat = 'svg' | 'png' | 'json' | 'pdf' | 'mermaid' | 'markdown';

export interface ExportOptions {
  includeMetadata?: boolean;
  includeAttribution?: boolean;
  includeComments?: boolean;
  format?: ExportFormat;
  filename?: string;
  quality?: number; // For image exports (0-1)
  scale?: number; // For image scaling
}

export interface ExportResult {
  success: boolean;
  url?: string;
  blob?: Blob;
  error?: string;
}

/**
 * Hook for consolidated export operations
 */
export const useExport = () => {
  const { notifications } = useNotification();
  const { handleError } = useErrorHandler();

  // Use the common exportToFile function
  const exportToFile = commonExportToFile;

  const copyToClipboard = async (content: string, type: string = 'text'): Promise<boolean> => {
    try {
      await navigator.clipboard.writeText(content);
      notifications.success.copiedToClipboard();
      return true;
    } catch (err) {
      handleError(err as Error, {
        fallbackMessage: `Failed to copy ${type} to clipboard`,
      });
      return false;
    }
  };

  const exportDiagram = async (
    diagramId: string | number,
    format: ExportFormat,
    options: ExportOptions = {}
  ): Promise<ExportResult> => {
    try {
      // Placeholder implementation - would integrate with actual export service
      console.log(`Exporting diagram ${diagramId} as ${format}`, options);

      // Simulate successful export
      notifications.success.diagramExported(format.toUpperCase());

      return {
        success: true,
        url: `blob:exported_${diagramId}.${format}`,
      };
    } catch (err) {
      const errorResult = {
        success: false,
        error: (err as Error).message,
      };

      handleError(err as Error, {
        fallbackMessage: `Failed to export diagram as ${format}`,
      });

      return errorResult;
    }
  };

  const exportMultipleDiagrams = async (
    diagramIds: (string | number)[],
    format: ExportFormat,
    options: ExportOptions = {}
  ): Promise<ExportResult> => {
    try {
      // Placeholder implementation - would integrate with actual export service
      console.log(`Exporting ${diagramIds.length} diagrams as ${format}`, options);

      notifications.success.bulkOperation(diagramIds.length, 'diagram', 'Exported');

      return {
        success: true,
        url: `blob:exported_${diagramIds.length}_diagrams.${format}`,
      };
    } catch (err) {
      const errorResult = {
        success: false,
        error: (err as Error).message,
      };

      handleError(err as Error, {
        fallbackMessage: `Failed to export ${diagramIds.length} diagrams`,
      });

      return errorResult;
    }
  };

  return {
    exportToFile,
    copyToClipboard,
    exportDiagram,
    exportMultipleDiagrams,
  };
};

/**
 * Standard export configurations
 */
export const EXPORT_CONFIGS = {
  DIAGRAM: {
    SVG: { format: 'svg' as ExportFormat, includeMetadata: true, includeAttribution: true },
    PNG: { format: 'png' as ExportFormat, quality: 0.9, scale: 2, includeMetadata: true },
    JSON: { format: 'json' as ExportFormat, includeMetadata: true, includeComments: true },
    PDF: { format: 'pdf' as ExportFormat, includeMetadata: true, includeAttribution: true },
    MERMAID: { format: 'mermaid' as ExportFormat },
  },
};

/**
 * File naming utilities
 */
export const FileNaming = {
  generateFilename: (
    baseName: string,
    type: string,
    format: ExportFormat,
    timestamp = true
  ): string => {
    const timestampStr = timestamp ? `_${new Date().toISOString().split('T')[0]}` : '';
    const extension = getFileExtension(format);
    return `${baseName}_${type}${timestampStr}.${extension}`;
  },

  generateDiagramFilename: (
    diagramId: string | number,
    format: ExportFormat,
    title?: string
  ): string => {
    const baseName = title ? sanitizeFilename(title) : `diagram_${diagramId}`;
    return FileNaming.generateFilename(baseName, 'diagram', format);
  },
};

/**
 * Utility functions
 */
const getFileExtension = (format: ExportFormat): string => {
  switch (format) {
    case 'svg': return 'svg';
    case 'png': return 'png';
    case 'json': return 'json';
    case 'pdf': return 'pdf';
    case 'mermaid': return 'mmd';
    case 'markdown': return 'md';
    default: return 'txt';
  }
};

const sanitizeFilename = (filename: string): string => {
  return filename
    .replace(/[^a-z0-9\s-]/gi, '') // Remove special characters
    .replace(/\s+/g, '_') // Replace spaces with underscores
    .toLowerCase()
    .substring(0, 50); // Limit length
};

/**
 * MIME type utilities
 */
export const MIME_TYPES = {
  'svg': 'image/svg+xml',
  'png': 'image/png',
  'json': 'application/json',
  'pdf': 'application/pdf',
  'mermaid': 'text/plain',
  'markdown': 'text/markdown',
  'txt': 'text/plain',
} as const;