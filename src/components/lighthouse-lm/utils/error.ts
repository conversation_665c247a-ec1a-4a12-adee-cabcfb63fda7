import { useCallback } from 'react';
import { useNotificationService as useNotification } from '../services/notification';

/**
 * Consolidated error handling utilities
 */

export interface ErrorHandlerOptions {
  showNotification?: boolean;
  logError?: boolean;
  fallbackMessage?: string;
  onError?: (error: Error) => void;
}

export interface AsyncOperationOptions extends ErrorHandlerOptions {
  successMessage?: string;
  loadingMessage?: string;
  onSuccess?: (result: any) => void;
}

/**
 * Error classification utilities
 */
export const ErrorClassifier = {
  isNetworkError: (error: Error): boolean => {
    return error.message.includes('fetch') ||
           error.message.includes('network') ||
           error.message.includes('connection');
  },

  isPermissionError: (error: Error): boolean => {
    return error.message.includes('permission') ||
           error.message.includes('unauthorized') ||
           error.message.includes('forbidden');
  },

  isValidationError: (error: Error): boolean => {
    return error.message.includes('validation') ||
           error.message.includes('invalid');
  },

  isFileError: (error: Error): boolean => {
    return error.message.includes('file') ||
           error.message.includes('upload');
  },

  isDiagramError: (error: Error): boolean => {
    return error.message.includes('diagram') ||
           error.message.includes('mermaid');
  },

  classifyError: (error: Error): string => {
    if (ErrorClassifier.isNetworkError(error)) return 'network';
    if (ErrorClassifier.isPermissionError(error)) return 'permission';
    if (ErrorClassifier.isValidationError(error)) return 'validation';
    if (ErrorClassifier.isFileError(error)) return 'file';
    if (ErrorClassifier.isDiagramError(error)) return 'diagram';
    return 'unknown';
  },
};

/**
 * Error message utilities
 */
export const ErrorMessages = {
  getErrorMessage: (error: Error, fallback = 'An unexpected error occurred'): string => {
    // If it's a known error with a message, use it
    if (error.message) {
      return error.message;
    }

    // Try to extract meaningful error from different error types
    if (error instanceof Error) {
      return error.message || fallback;
    }

    // Handle string errors
    if (typeof error === 'string') {
      return error;
    }

    // Handle object errors (like API responses)
    if (typeof error === 'object' && error !== null) {
      const errorObj = error as any;
      return errorObj.message || errorObj.error || errorObj.detail || fallback;
    }

    return fallback;
  },

  getUserFriendlyMessage: (error: Error): string => {
    const errorType = ErrorClassifier.classifyError(error);

    switch (errorType) {
      case 'network':
        return 'Unable to connect to the server. Please check your internet connection and try again.';
      case 'permission':
        return 'You don\'t have permission to perform this action. Please contact your administrator.';
      case 'validation':
        return 'The provided information is invalid. Please check your input and try again.';
      case 'file':
        return 'There was an issue with the file. Please check the file format and size.';
      case 'diagram':
        return 'Unable to generate or process the diagram. Please try again.';
      default:
        return ErrorMessages.getErrorMessage(error, 'Something went wrong. Please try again.');
    }
  },
};

/**
 * Hook for consolidated error handling
 */
export const useErrorHandler = () => {
  const { showError, showWarning } = useNotification();

  const handleError = useCallback((
    error: Error | string,
    options: ErrorHandlerOptions = {}
  ) => {
    const {
      showNotification = true,
      logError = true,
      fallbackMessage = 'An error occurred',
      onError,
    } = options;

    // Normalize error to Error object
    const errorObj = typeof error === 'string' ? new Error(error) : error;

    // Log error if requested
    if (logError) {
      console.error('Error handled:', errorObj);
    }

    // Call custom error handler if provided
    onError?.(errorObj);

    // Show notification if requested
    if (showNotification) {
      const userFriendlyMessage = ErrorMessages.getUserFriendlyMessage(errorObj);
      const shouldUseWarning = ErrorClassifier.classifyError(errorObj) === 'validation';

      if (shouldUseWarning) {
        showWarning({
          title: 'Warning',
          description: userFriendlyMessage,
        });
      } else {
        showError({
          title: 'Error',
          description: userFriendlyMessage,
        });
      }
    }

    return errorObj;
  }, [showError, showWarning]);

  const handleAsyncError = useCallback(async (
    asyncFn: () => Promise<any>,
    options: AsyncOperationOptions = {}
  ) => {
    try {
      const result = await asyncFn();
      if (options.onSuccess) {
        options.onSuccess(result);
      }
      return result;
    } catch (error) {
      handleError(error as Error, options);
      return null;
    }
  }, [handleError]);

  return {
    handleError,
    handleAsyncError,
    ErrorMessages,
    ErrorClassifier,
  };
};

/**
 * Async operation wrapper with loading states
 */
export const useAsyncOperation = () => {
  const { handleAsyncError } = useErrorHandler();

  const executeAsync = useCallback(async (
    operation: () => Promise<any>,
    options: AsyncOperationOptions & {
      setLoading?: (loading: boolean) => void;
      loadingState?: boolean;
    } = {}
  ) => {
    const { setLoading, loadingState, ...errorOptions } = options;

    if (setLoading) setLoading(true);

    try {
      const result = await operation();
      if (errorOptions.onSuccess) {
        errorOptions.onSuccess(result);
      }
      return result;
    } catch (error) {
      handleAsyncError(() => Promise.reject(error), errorOptions);
      return null;
    } finally {
      if (setLoading) setLoading(false);
    }
  }, [handleAsyncError]);

  return { executeAsync };
};

/**
 * File operation error handling
 */
export const FileErrorHandler = {
  handleFileUploadError: (error: Error): string => {
    if (error.message.includes('size')) {
      return 'File is too large. Please choose a smaller file.';
    }
    if (error.message.includes('type') || error.message.includes('format')) {
      return 'File format not supported. Please choose a different file.';
    }
    if (error.message.includes('permission')) {
      return 'Permission denied. Please check file permissions.';
    }
    return 'Failed to upload file. Please try again.';
  },

  handleFileReadError: (error: Error): string => {
    if (error.message.includes('encoding')) {
      return 'File encoding not supported. Please save the file with UTF-8 encoding.';
    }
    if (error.message.includes('corrupt')) {
      return 'File appears to be corrupted. Please choose a different file.';
    }
    return 'Unable to read file. Please check the file and try again.';
  },
};

/**
 * API error handling
 */
export const ApiErrorHandler = {
  handleApiError: (error: any): string => {
    if (error?.status) {
      switch (error.status) {
        case 400:
          return 'Invalid request. Please check your input.';
        case 401:
          return 'Authentication required. Please log in again.';
        case 403:
          return 'Access denied. You don\'t have permission for this action.';
        case 404:
          return 'Resource not found. It may have been deleted.';
        case 429:
          return 'Too many requests. Please wait a moment and try again.';
        case 500:
          return 'Server error. Please try again later.';
        case 503:
          return 'Service temporarily unavailable. Please try again later.';
        default:
          return `Server error (${error.status}). Please try again.`;
      }
    }

    if (error?.code) {
      switch (error.code) {
        case 'NETWORK_ERROR':
          return 'Network connection error. Please check your internet connection.';
        case 'TIMEOUT':
          return 'Request timed out. Please try again.';
        case 'CANCELLED':
          return 'Request was cancelled.';
        default:
          return `Error (${error.code}). Please try again.`;
      }
    }

    return ErrorMessages.getUserFriendlyMessage(error);
  },
};