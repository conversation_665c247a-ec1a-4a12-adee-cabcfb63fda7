import { useToast } from '@/components/ui/use-toast';

/**
 * Consolidated notification utilities to eliminate toast duplication
 */

export interface NotificationOptions {
  title: string;
  description?: string;
  variant?: 'default' | 'destructive' | null | undefined;
}

export interface SuccessNotification extends Omit<NotificationOptions, 'variant'> {
  variant?: 'default';
}

export interface ErrorNotification extends Omit<NotificationOptions, 'variant'> {
  variant?: 'destructive';
}

/**
 * Standard notification messages for common operations
 */
export const NOTIFICATION_MESSAGES = {
  // Success messages
  SUCCESS: {
    SOURCE_UPLOADED: (count: number) => `Uploaded ${count} file(s) successfully`,
    SOURCE_DELETED: 'Source deleted successfully',
    SOURCE_RENAMED: 'Source renamed successfully',
    SOURCE_UPDATED: 'Source updated successfully',
    URL_IMPORTED: 'URL imported successfully',
    DIAGRAM_GENERATED: 'Diagram generated and saved successfully',
    DIAGRAM_DELETED: 'Diagram deleted successfully',
    DIAGRAM_REGENERATED: 'Diagram regenerated successfully',
    DIAGRAM_EXPORTED: (format: string) => `Diagram exported as ${format.toUpperCase()}`,
    BULK_DELETED: (count: number, type: string) => `Deleted ${count} ${type}(s) successfully`,
    BULK_EXPORTED: (count: number, type: string, format?: string) =>
      `Exported ${count} ${type}(s)${format ? ` as ${format.toUpperCase()}` : ''}`,
    BULK_ARCHIVED: (count: number, type: string) => `Archived ${count} ${type}(s) successfully`,
    CHAT_CLEARED: 'Chat history cleared',
    COPIED_TO_CLIPBOARD: 'Copied to clipboard',
    SETTINGS_SAVED: 'Settings saved successfully',
    PRESENTATION_SAVED: 'Presentation saved successfully',
    SLIDES_GENERATED: 'Slides generated successfully',
    AUDIO_GENERATED: 'Audio overview generated successfully',
  },

  // Error messages
  ERROR: {
    SOURCE_UPLOAD_FAILED: 'Failed to upload files',
    SOURCE_DELETE_FAILED: 'Failed to delete source',
    SOURCE_RENAME_FAILED: 'Failed to rename source',
    SOURCE_UPDATE_FAILED: 'Failed to update source',
    URL_IMPORT_FAILED: 'Failed to import URL',
    DIAGRAM_GENERATION_FAILED: 'Failed to generate diagram',
    DIAGRAM_DELETE_FAILED: 'Failed to delete diagram',
    DIAGRAM_REGENERATION_FAILED: 'Failed to regenerate diagram',
    DIAGRAM_EXPORT_FAILED: (format?: string) => `Failed to export diagram${format ? ` as ${format}` : ''}`,
    BULK_OPERATION_FAILED: (operation: string) => `Failed to ${operation}`,
    CHAT_LOAD_FAILED: 'Failed to load chat messages',
    CHAT_DELETE_FAILED: 'Failed to delete chat history',
    SETTINGS_SAVE_FAILED: 'Failed to save settings',
    PRESENTATION_LOAD_FAILED: 'Failed to load presentations',
    PRESENTATION_SAVE_FAILED: 'Failed to save presentation',
    EXPORT_FAILED: 'Export failed',
    GENERATION_FAILED: 'Generation failed',
    LOAD_FAILED: 'Failed to load data',
    SAVE_FAILED: 'Save failed',
    DELETE_FAILED: 'Delete failed',
    NETWORK_ERROR: 'Network error occurred',
    PERMISSION_DENIED: 'Permission denied',
  },

  // Warning messages
  WARNING: {
    NO_SOURCES_SELECTED: 'Please select sources to continue',
    NO_SOURCES_AVAILABLE: 'No sources available for this operation',
    INVALID_FILE_TYPE: 'Invalid file type selected',
    FILE_TOO_LARGE: 'File is too large',
    MISSING_REQUIRED: 'Required information is missing',
    OPERATION_IN_PROGRESS: 'Operation already in progress',
  },
} as const;

/**
 * Hook for consolidated notification management
 */
export const useNotification = () => {
  const { toast } = useToast();

  const showSuccess = (options: SuccessNotification) => {
    toast({
      ...options,
      variant: 'default',
    });
  };

  const showError = (options: ErrorNotification) => {
    toast({
      ...options,
      variant: 'destructive',
    });
  };

  const showWarning = (options: NotificationOptions) => {
    toast({
      ...options,
      variant: 'default',
    });
  };

  // Pre-configured notification helpers
  const success = {
    sourceUploaded: (count: number) =>
      showSuccess({ title: NOTIFICATION_MESSAGES.SUCCESS.SOURCE_UPLOADED(count) }),
    sourceDeleted: () =>
      showSuccess({ title: NOTIFICATION_MESSAGES.SUCCESS.SOURCE_DELETED }),
    sourceRenamed: () =>
      showSuccess({ title: NOTIFICATION_MESSAGES.SUCCESS.SOURCE_RENAMED }),
    diagramGenerated: () =>
      showSuccess({ title: NOTIFICATION_MESSAGES.SUCCESS.DIAGRAM_GENERATED }),
    diagramExported: (format: string) =>
      showSuccess({ title: NOTIFICATION_MESSAGES.SUCCESS.DIAGRAM_EXPORTED(format) }),
    bulkOperation: (count: number, type: string, operation: string) =>
      showSuccess({
        title: NOTIFICATION_MESSAGES.SUCCESS.BULK_DELETED(count, type).replace('Deleted', operation)
      }),
    copiedToClipboard: () =>
      showSuccess({ title: NOTIFICATION_MESSAGES.SUCCESS.COPIED_TO_CLIPBOARD }),
  };

  const error = {
    sourceUploadFailed: (description?: string) =>
      showError({
        title: NOTIFICATION_MESSAGES.ERROR.SOURCE_UPLOAD_FAILED,
        description
      }),
    sourceDeleteFailed: (description?: string) =>
      showError({
        title: NOTIFICATION_MESSAGES.ERROR.SOURCE_DELETE_FAILED,
        description
      }),
    diagramGenerationFailed: (description?: string) =>
      showError({
        title: NOTIFICATION_MESSAGES.ERROR.DIAGRAM_GENERATION_FAILED,
        description
      }),
    bulkOperationFailed: (operation: string, description?: string) =>
      showError({
        title: NOTIFICATION_MESSAGES.ERROR.BULK_OPERATION_FAILED(operation),
        description
      }),
    networkError: () =>
      showError({ title: NOTIFICATION_MESSAGES.ERROR.NETWORK_ERROR }),
  };

  const warning = {
    noSourcesSelected: () =>
      showWarning({ title: NOTIFICATION_MESSAGES.WARNING.NO_SOURCES_SELECTED }),
    invalidFileType: () =>
      showWarning({ title: NOTIFICATION_MESSAGES.WARNING.INVALID_FILE_TYPE }),
  };

  return {
    showSuccess,
    showError,
    showWarning,
    success,
    error,
    warning,
  };
};

/**
 * Async operation wrapper with automatic error handling and notifications
 */
export const withNotification = async <T>(
  operation: () => Promise<T>,
  {
    onSuccess,
    onError,
    successMessage,
    errorMessage,
    showNotification = true,
  }: {
    onSuccess?: (result: T) => void;
    onError?: (error: Error) => void;
    successMessage?: string;
    errorMessage?: string;
    showNotification?: boolean;
  } = {}
): Promise<T | null> => {
  try {
    const result = await operation();
    if (showNotification && successMessage) {
      const { showSuccess } = useNotification();
      showSuccess({ title: successMessage });
    }
    onSuccess?.(result);
    return result;
  } catch (error) {
    console.error('Operation failed:', error);
    if (showNotification) {
      const { showError } = useNotification();
      showError({
        title: errorMessage || NOTIFICATION_MESSAGES.ERROR.NETWORK_ERROR,
        description: error instanceof Error ? error.message : undefined,
      });
    }
    onError?.(error as Error);
    return null;
  }
};

/**
 * Copy to clipboard with notification
 */
export const copyToClipboard = async (
  text: string,
  successMessage = NOTIFICATION_MESSAGES.SUCCESS.COPIED_TO_CLIPBOARD,
  errorMessage = 'Failed to copy to clipboard'
): Promise<boolean> => {
  return (await withNotification(
    () => navigator.clipboard.writeText(text),
    {
      successMessage,
      errorMessage,
    }
  )) !== null;
};