import { useState, useCallback } from 'react';

/**
 * Consolidated loading state management utilities
 */

export interface LoadingState {
  isLoading: boolean;
  message?: string;
  progress?: number;
}

export interface LoadingOptions {
  initialMessage?: string;
  showProgress?: boolean;
  onStart?: () => void;
  onComplete?: (result?: any) => void;
  onError?: (error: Error) => void;
}

/**
 * Hook for managing loading states
 */
export const useLoadingState = (options: LoadingOptions = {}) => {
  const [loadingState, setLoadingState] = useState<LoadingState>({
    isLoading: false,
  });

  const startLoading = useCallback((message?: string) => {
    setLoadingState({
      isLoading: true,
      message: message || options.initialMessage || 'Loading...',
      progress: options.showProgress ? 0 : undefined,
    });
    options.onStart?.();
  }, [options]);

  const stopLoading = useCallback(() => {
    setLoadingState({ isLoading: false });
  }, []);

  const updateProgress = useCallback((progress: number, message?: string) => {
    setLoadingState(prev => ({
      ...prev,
      progress,
      message: message || prev.message,
    }));
  }, []);

  const updateMessage = useCallback((message: string) => {
    setLoadingState(prev => ({
      ...prev,
      message,
    }));
  }, []);

  const withLoading = useCallback(async <T>(
    asyncFn: () => Promise<T>,
    loadingMessage?: string
  ): Promise<T | null> => {
    startLoading(loadingMessage);

    try {
      const result = await asyncFn();
      options.onComplete?.(result);
      return result;
    } catch (error) {
      options.onError?.(error as Error);
      return null;
    } finally {
      stopLoading();
    }
  }, [startLoading, stopLoading, options]);

  return {
    loadingState,
    startLoading,
    stopLoading,
    updateProgress,
    updateMessage,
    withLoading,
    isLoading: loadingState.isLoading,
  };
};

/**
 * Hook for managing multiple concurrent operations
 */
export const useConcurrentOperations = () => {
  const [operations, setOperations] = useState<Map<string, LoadingState>>(new Map());

  const startOperation = useCallback((id: string, message?: string) => {
    setOperations(prev => {
      const newMap = new Map(prev);
      newMap.set(id, {
        isLoading: true,
        message: message || `Operation ${id} in progress...`,
      });
      return newMap;
    });
  }, []);

  const stopOperation = useCallback((id: string) => {
    setOperations(prev => {
      const newMap = new Map(prev);
      newMap.delete(id);
      return newMap;
    });
  }, []);

  const updateOperation = useCallback((id: string, updates: Partial<LoadingState>) => {
    setOperations(prev => {
      const newMap = new Map(prev);
      const existing = newMap.get(id);
      if (existing) {
        newMap.set(id, { ...existing, ...updates });
      }
      return newMap;
    });
  }, []);

  const getOperationState = useCallback((id: string) => {
    return operations.get(id);
  }, [operations]);

  const isAnyOperationLoading = Array.from(operations.values()).some(op => op.isLoading);
  const activeOperationsCount = Array.from(operations.values()).filter(op => op.isLoading).length;

  return {
    operations,
    startOperation,
    stopOperation,
    updateOperation,
    getOperationState,
    isAnyOperationLoading,
    activeOperationsCount,
  };
};

/**
 * Loading overlay component props
 */
export interface LoadingOverlayProps {
  isVisible: boolean;
  message?: string;
  progress?: number;
  showProgress?: boolean;
  children?: React.ReactNode;
  className?: string;
}

/**
 * Loading spinner component props
 */
export interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

/**
 * Common loading messages
 */
export const LOADING_MESSAGES = {
  SAVING: 'Saving...',
  LOADING: 'Loading...',
  UPLOADING: 'Uploading...',
  PROCESSING: 'Processing...',
  GENERATING: 'Generating...',
  EXPORTING: 'Exporting...',
  DELETING: 'Deleting...',
  UPDATING: 'Updating...',
  SYNCING: 'Syncing...',
  FETCHING: 'Fetching data...',
} as const;

/**
 * File upload progress utilities
 */
export const FileUploadProgress = {
  createProgressHandler: (onProgress: (progress: number) => void) => {
    return (loaded: number, total: number) => {
      const progress = Math.round((loaded / total) * 100);
      onProgress(progress);
    };
  },

  formatFileSize: (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },

  formatTimeRemaining: (bytesRemaining: number, bytesPerSecond: number): string => {
    if (bytesPerSecond === 0) return 'Unknown';
    const seconds = bytesRemaining / bytesPerSecond;
    if (seconds < 60) return `${Math.round(seconds)}s`;
    const minutes = seconds / 60;
    if (minutes < 60) return `${Math.round(minutes)}m`;
    const hours = minutes / 60;
    return `${Math.round(hours)}h`;
  },
};

/**
 * Debounced loading state
 */
export const useDebouncedLoading = (delay = 300) => {
  const [isLoading, setIsLoading] = useState(false);
  const [loadingTimeout, setLoadingTimeout] = useState<NodeJS.Timeout | null>(null);

  const startLoading = useCallback(() => {
    if (loadingTimeout) {
      clearTimeout(loadingTimeout);
    }
    setLoadingTimeout(setTimeout(() => setIsLoading(true), delay));
  }, [delay, loadingTimeout]);

  const stopLoading = useCallback(() => {
    if (loadingTimeout) {
      clearTimeout(loadingTimeout);
      setLoadingTimeout(null);
    }
    setIsLoading(false);
  }, [loadingTimeout]);

  return { isLoading, startLoading, stopLoading };
};