import { useState, useCallback } from 'react';
import { Source } from '../../../services/insights-api';
import { useNotificationService as useNotification } from '../services/notification';
import { useError<PERSON>and<PERSON> } from './error';
import { useBulkOperations } from './bulkOps';

/**
 * Consolidated source management utilities
 */

export interface SourceFilters {
  searchTerm: string;
  type: string;
  tags: string[];
  dateRange?: {
    from: Date;
    to: Date;
  };
  processingStatus?: string;
}

export interface SourceSortOptions {
  field: 'name' | 'type' | 'date' | 'size';
  direction: 'asc' | 'desc';
}

export interface SourceManagementState {
  selectedSources: Set<string>;
  filters: SourceFilters;
  sortOptions: SourceSortOptions;
  viewMode: 'list' | 'grid' | 'compact';
  isLoading: boolean;
  processingIds: Set<string>;
}

/**
 * Hook for consolidated source management
 */
export const useSourceManagement = (
  sources: Source[],
  notebookId?: string
) => {
  const { success, error } = useNotification();
  const { handleError } = useErrorHandler();
  const { bulkDelete, bulkExport, bulkArchive } = useBulkOperations();

  const [state, setState] = useState<SourceManagementState>({
    selectedSources: new Set(),
    filters: {
      searchTerm: '',
      type: 'all',
      tags: [],
    },
    sortOptions: {
      field: 'date',
      direction: 'desc',
    },
    viewMode: 'list',
    isLoading: false,
    processingIds: new Set(),
  });

  // Filtering logic
  const filteredSources = useCallback(() => {
    return sources.filter(source => {
      // Search filter
      if (state.filters.searchTerm) {
        const searchTerm = state.filters.searchTerm.toLowerCase();
        const matchesSearch =
          source.title?.toLowerCase().includes(searchTerm) ||
          source.display_name?.toLowerCase().includes(searchTerm) ||
          source.content?.toLowerCase().includes(searchTerm);

        if (!matchesSearch) return false;
      }

      // Type filter
      if (state.filters.type !== 'all' && source.source_type !== state.filters.type) {
        return false;
      }

      // Processing status filter
      if (state.filters.processingStatus && source.processing_status !== state.filters.processingStatus) {
        return false;
      }

      // Date range filter
      if (state.filters.dateRange) {
        const sourceDate = new Date(source.updated_at || source.created_at || 0);
        if (sourceDate < state.filters.dateRange.from || sourceDate > state.filters.dateRange.to) {
          return false;
        }
      }

      return true;
    });
  }, [sources, state.filters]);

  // Sorting logic
  const sortedSources = useCallback(() => {
    const filtered = filteredSources();

    return [...filtered].sort((a, b) => {
      let comparison = 0;

      switch (state.sortOptions.field) {
        case 'name':
          comparison = (a.title || a.display_name || '').localeCompare(b.title || b.display_name || '');
          break;
        case 'type':
          comparison = (a.source_type || '').localeCompare(b.source_type || '');
          break;
        case 'date':
          const dateA = new Date(a.updated_at || a.created_at || 0).getTime();
          const dateB = new Date(b.updated_at || b.created_at || 0).getTime();
          comparison = dateB - dateA;
          break;
        case 'size':
          comparison = (b.file_size || b.content?.length || 0) - (a.file_size || a.content?.length || 0);
          break;
        default:
          comparison = 0;
      }

      return state.sortOptions.direction === 'asc' ? comparison : -comparison;
    });
  }, [filteredSources, state.sortOptions]);

  // Selection management
  const toggleSourceSelection = useCallback((sourceId: string) => {
    setState(prev => {
      const newSelected = new Set(prev.selectedSources);
      if (newSelected.has(sourceId)) {
        newSelected.delete(sourceId);
      } else {
        newSelected.add(sourceId);
      }
      return { ...prev, selectedSources: newSelected };
    });
  }, []);

  const selectAllSources = useCallback(() => {
    const allIds = sortedSources().map(s => s.id);
    setState(prev => ({
      ...prev,
      selectedSources: new Set(allIds)
    }));
  }, [sortedSources]);

  const deselectAllSources = useCallback(() => {
    setState(prev => ({
      ...prev,
      selectedSources: new Set()
    }));
  }, []);

  const selectSourcesByType = useCallback((sourceType: string) => {
    const typeSources = sortedSources().filter(s => s.source_type === sourceType);
    const typeIds = typeSources.map(s => s.id);
    setState(prev => ({
      ...prev,
      selectedSources: new Set(typeIds)
    }));
  }, [sortedSources]);

  // Filter and sort actions
  const updateFilters = useCallback((updates: Partial<SourceFilters>) => {
    setState(prev => ({
      ...prev,
      filters: { ...prev.filters, ...updates }
    }));
  }, []);

  const updateSortOptions = useCallback((updates: Partial<SourceSortOptions>) => {
    setState(prev => ({
      ...prev,
      sortOptions: { ...prev.sortOptions, ...updates }
    }));
  }, []);

  const clearFilters = useCallback(() => {
    setState(prev => ({
      ...prev,
      filters: {
        searchTerm: '',
        type: 'all',
        tags: [],
      }
    }));
  }, []);

  const setViewMode = useCallback((viewMode: 'list' | 'grid' | 'compact') => {
    setState(prev => ({ ...prev, viewMode }));
  }, []);

  // Bulk operations
  const handleBulkDelete = useCallback(async (
    onDelete: (sourceIds: string[]) => Promise<void>
  ) => {
    const sourceIds = Array.from(state.selectedSources);
    if (sourceIds.length === 0) return;

    setState(prev => ({
      ...prev,
      processingIds: new Set([...prev.processingIds, ...sourceIds])
    }));

    try {
      await onDelete(sourceIds);
      success.bulkOperation(sourceIds.length, 'source', 'Deleted');
      deselectAllSources();
    } catch (err) {
      handleError(err as Error, {
        fallbackMessage: 'Failed to delete sources'
      });
    } finally {
      setState(prev => {
        const newProcessing = new Set(prev.processingIds);
        sourceIds.forEach(id => newProcessing.delete(id));
        return { ...prev, processingIds: newProcessing };
      });
    }
  }, [state.selectedSources, success, error, deselectAllSources, handleError]);

  const handleBulkExport = useCallback(async (
    onExport: (sourceIds: string[]) => Promise<void>
  ) => {
    const sourceIds = Array.from(state.selectedSources);
    if (sourceIds.length === 0) return;

    try {
      await onExport(sourceIds);
      success.bulkOperation(sourceIds.length, 'source', 'Exported');
    } catch (err) {
      handleError(err as Error, {
        fallbackMessage: 'Failed to export sources'
      });
    }
  }, [state.selectedSources, success, handleError]);

  const handleBulkArchive = useCallback(async (
    onArchive: (sourceIds: string[]) => Promise<void>
  ) => {
    const sourceIds = Array.from(state.selectedSources);
    if (sourceIds.length === 0) return;

    try {
      await onArchive(sourceIds);
      success.bulkOperation(sourceIds.length, 'source', 'Archived');
    } catch (err) {
      handleError(err as Error, {
        fallbackMessage: 'Failed to archive sources'
      });
    }
  }, [state.selectedSources, success, handleError]);

  // Source operations
  const handleSourceDelete = useCallback(async (
    sourceId: string,
    onDelete: (id: string) => Promise<void>
  ) => {
    setState(prev => ({
      ...prev,
      processingIds: new Set([...prev.processingIds, sourceId])
    }));

    try {
      await onDelete(sourceId);
      success.sourceDeleted();
      setState(prev => {
        const newSelected = new Set(prev.selectedSources);
        newSelected.delete(sourceId);
        return { ...prev, selectedSources: newSelected };
      });
    } catch (err) {
      handleError(err as Error, {
        fallbackMessage: 'Failed to delete source'
      });
    } finally {
      setState(prev => {
        const newProcessing = new Set(prev.processingIds);
        newProcessing.delete(sourceId);
        return { ...prev, processingIds: newProcessing };
      });
    }
  }, [success, handleError]);

  const handleSourceRename = useCallback(async (
    source: Source,
    onRename: (source: Source) => Promise<void>
  ) => {
    try {
      await onRename(source);
      success.sourceRenamed();
    } catch (err) {
      handleError(err as Error, {
        fallbackMessage: 'Failed to rename source'
      });
    }
  }, [success, handleError]);

  // Computed values
  const processedSources = sortedSources();
  const selectedSourcesCount = state.selectedSources.size;
  const hasSelectedSources = selectedSourcesCount > 0;
  const isAllSelected = selectedSourcesCount === processedSources.length;
  const sourceTypes = Array.from(new Set(sources.map(s => s.source_type).filter(Boolean)));

  return {
    // State
    state,

    // Computed values
    processedSources,
    selectedSourcesCount,
    hasSelectedSources,
    isAllSelected,
    sourceTypes,

    // Actions
    toggleSourceSelection,
    selectAllSources,
    deselectAllSources,
    selectSourcesByType,
    updateFilters,
    updateSortOptions,
    clearFilters,
    setViewMode,

    // Bulk operations
    handleBulkDelete,
    handleBulkExport,
    handleBulkArchive,

    // Individual operations
    handleSourceDelete,
    handleSourceRename,
  };
};

/**
 * Source validation utilities
 */
export const SourceValidation = {
  isValidSource: (source: Source): boolean => {
    return !!(
      source.id &&
      (source.title || source.display_name) &&
      source.source_type
    );
  },

  validateSourceForProcessing: (source: Source): { valid: boolean; reason?: string } => {
    if (!source.content || source.content.trim() === '') {
      return { valid: false, reason: 'Source has no content' };
    }

    if (source.processing_status === 'failed') {
      return { valid: false, reason: 'Source processing failed' };
    }

    return { valid: true };
  },

  getSourceFileSize: (source: Source): string => {
    const size = source.file_size || source.content?.length || 0;

    if (size === 0) return 'Unknown';

    const units = ['B', 'KB', 'MB', 'GB'];
    let unitIndex = 0;
    let displaySize = size;

    while (displaySize >= 1024 && unitIndex < units.length - 1) {
      displaySize /= 1024;
      unitIndex++;
    }

    return `${displaySize.toFixed(1)} ${units[unitIndex]}`;
  },
};

/**
 * Source statistics utilities
 */
export const SourceStatistics = {
  getSourceStats: (sources: Source[]) => {
    const total = sources.length;
    const byType = sources.reduce((acc, source) => {
      acc[source.source_type || 'unknown'] = (acc[source.source_type || 'unknown'] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const processed = sources.filter(s => s.processing_status === 'completed').length;
    const processing = sources.filter(s => s.processing_status === 'processing').length;
    const failed = sources.filter(s => s.processing_status === 'failed').length;

    const totalSize = sources.reduce((sum, source) => {
      return sum + (source.file_size || source.content?.length || 0);
    }, 0);

    return {
      total,
      byType,
      processed,
      processing,
      failed,
      pending: total - processed - processing - failed,
      totalSize,
      averageSize: total > 0 ? totalSize / total : 0,
    };
  },

  formatSourceStats: (stats: any) => {
    return {
      total: `${stats.total} sources`,
      processed: `${stats.processed} processed (${((stats.processed / stats.total) * 100).toFixed(1)}%)`,
      processing: `${stats.processing} processing`,
      failed: `${stats.failed} failed`,
      totalSize: SourceValidation.getSourceFileSize({ file_size: stats.totalSize } as Source),
    };
  },
};
