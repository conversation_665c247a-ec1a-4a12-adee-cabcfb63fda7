import { useState, useCallback } from 'react';
import { SourceDiagram } from '../../../services/sourceDiagramService';
import { Source } from '../../../services/insights-api';
import { useNotification } from './notificationUtils';
import { useErrorHandler } from './errorHandlingUtils';
import { useBulkOperations } from './bulkOperationsUtils';
import { DiagramType } from '../../../services/sourceDiagramService';

type DiagramType = 'flowchart' | 'mindmap' | 'sequence' | 'timeline' | 'gantt' | string;

/**
 * Consolidated diagram management utilities
 */

export interface DiagramFilters {
  searchTerm: string;
  type: DiagramType | 'all';
  freshness: 'all' | 'fresh' | 'outdated';
  sourceCount?: number;
}

export interface DiagramSortOptions {
  field: 'name' | 'type' | 'date' | 'confidence' | 'sources';
  direction: 'asc' | 'desc';
}

export interface DiagramManagementState {
  selectedDiagrams: Set<number>;
  filters: DiagramFilters;
  sortOptions: DiagramSortOptions;
  viewMode: 'list' | 'grid' | 'compact';
  isLoading: boolean;
  processingIds: Set<number>;
}

/**
 * Hook for consolidated diagram management
 */
export const useDiagramManagement = (
  diagrams: SourceDiagram[],
  sources: Source[],
  notebookId?: string
) => {
  const { success, error } = useNotification();
  const { handleError } = useErrorHandler();
  const { bulkDelete, bulkExport } = useBulkOperations();

  const [state, setState] = useState<DiagramManagementState>({
    selectedDiagrams: new Set(),
    filters: {
      searchTerm: '',
      type: 'all',
      freshness: 'all',
    },
    sortOptions: {
      field: 'date',
      direction: 'desc',
    },
    viewMode: 'list',
    isLoading: false,
    processingIds: new Set(),
  });

  // Helper functions
  const getSourcesForDiagram = useCallback((diagram: SourceDiagram) => {
    return sources.filter(source => diagram.source_ids.includes(source.id));
  }, [sources]);

  const getDiagramsForSource = useCallback((sourceId: string) => {
    return diagrams.filter(diagram => diagram.source_ids.includes(sourceId));
  }, [diagrams]);

  const checkDiagramFreshness = useCallback((diagram: SourceDiagram): boolean => {
    // Placeholder - would integrate with actual freshness checking logic
    const diagramAge = Date.now() - new Date(diagram.created_at).getTime();
    const maxAge = 7 * 24 * 60 * 60 * 1000; // 7 days
    return diagramAge < maxAge;
  }, []);

  // Filtering logic
  const filteredDiagrams = useCallback(() => {
    return diagrams.filter(diagram => {
      // Search filter
      if (state.filters.searchTerm) {
        const searchTerm = state.filters.searchTerm.toLowerCase();
        const matchesSearch =
          diagram.diagram_type.toLowerCase().includes(searchTerm) ||
          diagram.id.toString().includes(searchTerm) ||
          getSourcesForDiagram(diagram).some(source =>
            source.title?.toLowerCase().includes(searchTerm)
          );

        if (!matchesSearch) return false;
      }

      // Type filter
      if (state.filters.type !== 'all' && diagram.diagram_type !== state.filters.type) {
        return false;
      }

      // Freshness filter
      if (state.filters.freshness !== 'all') {
        const isFresh = checkDiagramFreshness(diagram);
        if (state.filters.freshness === 'fresh' && !isFresh) return false;
        if (state.filters.freshness === 'outdated' && isFresh) return false;
      }

      // Source count filter
      if (state.filters.sourceCount !== undefined) {
        if (diagram.source_ids.length < state.filters.sourceCount) return false;
      }

      return true;
    });
  }, [diagrams, state.filters, getSourcesForDiagram, checkDiagramFreshness]);

  // Sorting logic
  const sortedDiagrams = useCallback(() => {
    const filtered = filteredDiagrams();

    return [...filtered].sort((a, b) => {
      let comparison = 0;

      switch (state.sortOptions.field) {
        case 'name':
          comparison = a.diagram_type.localeCompare(b.diagram_type);
          break;
        case 'type':
          comparison = a.diagram_type.localeCompare(b.diagram_type);
          break;
        case 'date':
          comparison = new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
          break;
        case 'confidence':
          comparison = (b.analysis_metadata?.analysis_confidence || 0) - (a.analysis_metadata?.analysis_confidence || 0);
          break;
        case 'sources':
          comparison = b.source_ids.length - a.source_ids.length;
          break;
        default:
          comparison = 0;
      }

      return state.sortOptions.direction === 'asc' ? comparison : -comparison;
    });
  }, [filteredDiagrams, state.sortOptions]);

  // Selection management
  const toggleDiagramSelection = useCallback((diagramId: number) => {
    setState(prev => {
      const newSelected = new Set(prev.selectedDiagrams);
      if (newSelected.has(diagramId)) {
        newSelected.delete(diagramId);
      } else {
        newSelected.add(diagramId);
      }
      return { ...prev, selectedDiagrams: newSelected };
    });
  }, []);

  const selectAllDiagrams = useCallback(() => {
    const allIds = sortedDiagrams().map(d => d.id);
    setState(prev => ({
      ...prev,
      selectedDiagrams: new Set(allIds)
    }));
  }, [sortedDiagrams]);

  const deselectAllDiagrams = useCallback(() => {
    setState(prev => ({
      ...prev,
      selectedDiagrams: new Set()
    }));
  }, []);

  // Filter and sort actions
  const updateFilters = useCallback((updates: Partial<DiagramFilters>) => {
    setState(prev => ({
      ...prev,
      filters: { ...prev.filters, ...updates }
    }));
  }, []);

  const updateSortOptions = useCallback((updates: Partial<DiagramSortOptions>) => {
    setState(prev => ({
      ...prev,
      sortOptions: { ...prev.sortOptions, ...updates }
    }));
  }, []);

  const clearFilters = useCallback(() => {
    setState(prev => ({
      ...prev,
      filters: {
        searchTerm: '',
        type: 'all',
        freshness: 'all',
      }
    }));
  }, []);

  const setViewMode = useCallback((viewMode: 'list' | 'grid' | 'compact') => {
    setState(prev => ({ ...prev, viewMode }));
  }, []);

  // Bulk operations
  const handleBulkDelete = useCallback(async (
    onDelete: (diagramIds: number[]) => Promise<void>
  ) => {
    const diagramIds = Array.from(state.selectedDiagrams);
    if (diagramIds.length === 0) return;

    setState(prev => ({
      ...prev,
      processingIds: new Set([...prev.processingIds, ...diagramIds])
    }));

    try {
      await onDelete(diagramIds);
      success.bulkOperation(diagramIds.length, 'diagram', 'Deleted');
      deselectAllDiagrams();
    } catch (err) {
      handleError(err as Error, {
        fallbackMessage: 'Failed to delete diagrams'
      });
    } finally {
      setState(prev => {
        const newProcessing = new Set(prev.processingIds);
        diagramIds.forEach(id => newProcessing.delete(id));
        return { ...prev, processingIds: newProcessing };
      });
    }
  }, [state.selectedDiagrams, success, error, deselectAllDiagrams, handleError]);

  const handleBulkExport = useCallback(async (
    onExport: (diagrams: SourceDiagram[], format: string) => Promise<void>,
    format: string = 'svg'
  ) => {
    const diagramIds = Array.from(state.selectedDiagrams);
    if (diagramIds.length === 0) return;

    const selectedDiagrams = diagrams.filter(d => diagramIds.includes(d.id));

    try {
      await onExport(selectedDiagrams, format);
      success.bulkOperation(diagramIds.length, 'diagram', 'Exported');
    } catch (err) {
      handleError(err as Error, {
        fallbackMessage: `Failed to export diagrams as ${format}`
      });
    }
  }, [state.selectedDiagrams, diagrams, success, handleError]);

  // Individual diagram operations
  const handleDiagramDelete = useCallback(async (
    diagramId: number,
    onDelete: (id: number) => Promise<void>
  ) => {
    setState(prev => ({
      ...prev,
      processingIds: new Set([...prev.processingIds, diagramId])
    }));

    try {
      await onDelete(diagramId);
      success.copiedToClipboard(); // Using available success method
      setState(prev => {
        const newSelected = new Set(prev.selectedDiagrams);
        newSelected.delete(diagramId);
        return { ...prev, selectedDiagrams: newSelected };
      });
    } catch (err) {
      handleError(err as Error, {
        fallbackMessage: 'Failed to delete diagram'
      });
    } finally {
      setState(prev => {
        const newProcessing = new Set(prev.processingIds);
        newProcessing.delete(diagramId);
        return { ...prev, processingIds: newProcessing };
      });
    }
  }, [success, handleError]);

  const handleDiagramRegenerate = useCallback(async (
    diagram: SourceDiagram,
    onRegenerate: (diagram: SourceDiagram) => Promise<void>
  ) => {
    try {
      await onRegenerate(diagram);
      success.copiedToClipboard(); // Using available success method
    } catch (err) {
      handleError(err as Error, {
        fallbackMessage: 'Failed to regenerate diagram'
      });
    }
  }, [success, handleError]);

  // Computed values
  const processedDiagrams = sortedDiagrams();
  const selectedDiagramsCount = state.selectedDiagrams.size;
  const hasSelectedDiagrams = selectedDiagramsCount > 0;
  const isAllSelected = selectedDiagramsCount === processedDiagrams.length;

  const diagramTypes = Array.from(new Set(diagrams.map(d => d.diagram_type)));
  const freshDiagrams = diagrams.filter(d => checkDiagramFreshness(d)).length;
  const outdatedDiagrams = diagrams.length - freshDiagrams;

  return {
    // State
    state,

    // Computed values
    processedDiagrams,
    selectedDiagramsCount,
    hasSelectedDiagrams,
    isAllSelected,
    diagramTypes,
    freshDiagrams,
    outdatedDiagrams,

    // Helper functions
    getSourcesForDiagram,
    getDiagramsForSource,
    checkDiagramFreshness,

    // Actions
    toggleDiagramSelection,
    selectAllDiagrams,
    deselectAllDiagrams,
    updateFilters,
    updateSortOptions,
    clearFilters,
    setViewMode,

    // Bulk operations
    handleBulkDelete,
    handleBulkExport,

    // Individual operations
    handleDiagramDelete,
    handleDiagramRegenerate,
  };
};

/**
 * Diagram validation utilities
 */
export const DiagramValidation = {
  isValidDiagram: (diagram: SourceDiagram): boolean => {
    return !!(
      diagram.id &&
      diagram.diagram_type &&
      diagram.diagram_content &&
      diagram.source_ids.length > 0
    );
  },

  validateDiagramForExport: (diagram: SourceDiagram): { valid: boolean; reason?: string } => {
    if (!diagram.diagram_content || diagram.diagram_content.trim() === '') {
      return { valid: false, reason: 'Diagram has no content' };
    }

    if (diagram.source_ids.length === 0) {
      return { valid: false, reason: 'Diagram has no associated sources' };
    }

    return { valid: true };
  },

  getDiagramConfidence: (diagram: SourceDiagram): number => {
    return diagram.analysis_metadata?.analysis_confidence || 0;
  },

  getDiagramSize: (diagram: SourceDiagram): string => {
    const size = diagram.diagram_content.length;

    if (size < 1024) return `${size} B`;
    if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`;
    return `${(size / (1024 * 1024)).toFixed(1)} MB`;
  },
};

/**
 * Diagram statistics utilities
 */
export const DiagramStatistics = {
  getDiagramStats: (diagrams: SourceDiagram[]) => {
    const total = diagrams.length;
    const byType = diagrams.reduce((acc, diagram) => {
      acc[diagram.diagram_type] = (acc[diagram.diagram_type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const averageConfidence = diagrams.length > 0
      ? diagrams.reduce((sum, d) => sum + (d.analysis_metadata?.analysis_confidence || 0), 0) / diagrams.length
      : 0;

    const connectedSources = new Set(diagrams.flatMap(d => d.source_ids)).size;

    const conceptsExtracted = diagrams.reduce((sum, d) => sum + (d.analysis_metadata?.concepts_extracted || 0), 0);
    const relationshipsFound = diagrams.reduce((sum, d) => sum + (d.analysis_metadata?.relationships_found || 0), 0);

    return {
      total,
      byType,
      averageConfidence,
      connectedSources,
      conceptsExtracted,
      relationshipsFound,
    };
  },

  formatDiagramStats: (stats: any) => {
    return {
      total: `${stats.total} diagrams`,
      averageConfidence: `${(stats.averageConfidence * 100).toFixed(1)}%`,
      connectedSources: `${stats.connectedSources} sources`,
      conceptsExtracted: `${stats.conceptsExtracted} concepts`,
      relationshipsFound: `${stats.relationshipsFound} relationships`,
    };
  },
};