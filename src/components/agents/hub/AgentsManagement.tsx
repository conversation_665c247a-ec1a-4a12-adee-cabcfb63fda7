import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Bot, 
  Search, 
  Grid, 
  List, 
  Play, 
  Trash2, 
  Download,
  MoreVertical,
  Filter,
  RefreshCw,
  Calendar,
  Clock,
  ArrowUpDown,
  FolderOpen,
  Archive,
  Sparkles,
  Zap,
  Award,
  TrendingUp,
  Activity,
  Shield,
  Cpu,
  Brain
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useAgentsHub } from './AgentsHubContext';
import { type Agent } from '@/lib/api';

export const AgentsManagement: React.FC = () => {
  const {
    agents,
    searchQuery,
    setSearchQuery,
    viewMode,
    setViewMode,
    selectedAgents,
    toggleAgentSelection,
    clearSelection,
    runAgent,
    deleteAgent,
    exportAgent,
    loadAgents,
    loadingAgents,
  } = useAgentsHub();

  const [agentToDelete, setAgentToDelete] = useState<Agent | null>(null);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [filterCategory, setFilterCategory] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'name' | 'date' | 'runs'>('name');

  // Filter agents based on search query and category
  const filteredAgents = useMemo(() => {
    let filtered = agents;
    
    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(agent =>
        agent.name.toLowerCase().includes(query) ||
        agent.default_task?.toLowerCase().includes(query)
      );
    }
    
    // Apply category filter
    if (filterCategory !== 'all') {
      // Add category filtering logic here
      // For now, just return filtered
    }
    
    // Apply sorting
    filtered = [...filtered].sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'date':
          return new Date(b.created_at || 0).getTime() - new Date(a.created_at || 0).getTime();
        case 'runs':
          // Sort by run count (would need to add this data)
          return 0;
        default:
          return 0;
      }
    });
    
    return filtered;
  }, [agents, searchQuery, filterCategory, sortBy]);

  const handleRunAgent = async (agent: Agent) => {
    try {
      await runAgent(agent);
    } catch (error) {
    }
  };

  const handleDeleteAgent = (agent: Agent) => {
    setAgentToDelete(agent);
    setShowDeleteDialog(true);
  };

  const handleExportAgent = async (agent: Agent) => {
    try {
      await exportAgent(agent);
    } catch (error) {
    }
  };

  const confirmDelete = async () => {
    if (!agentToDelete?.id) return;
    
    try {
      await deleteAgent(agentToDelete.id);
      setShowDeleteDialog(false);
      setAgentToDelete(null);
    } catch (error) {
    }
  };

  const handleRefresh = () => {
    loadAgents();
  };

  // Enhanced Agent Card with Modern Design
  const AgentCard = React.memo<{ agent: Agent; index: number }>(({ agent, index }) => {
    const [isHovered, setIsHovered] = useState(false);
    const isRunning = Math.random() > 0.8; // Demo: simulate running status
    const successRate = Math.floor(Math.random() * 30) + 70; // Demo: 70-100%
    
    return (
      <motion.div
        initial={{ opacity: 0, y: 20, scale: 0.95 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        whileHover={{ y: -4, scale: 1.02 }}
        transition={{ 
          duration: 0.3, 
          delay: Math.min(index * 0.05, 0.5),
          type: "spring",
          stiffness: 300
        }}
        onHoverStart={() => setIsHovered(true)}
        onHoverEnd={() => setIsHovered(false)}
      >
        <Card className="relative h-full overflow-hidden border-border/50 bg-gradient-to-br from-background via-background/95 to-muted/30 group hover:shadow-xl transition-all duration-300">
          {/* Animated Background Gradient */}
          <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-primary/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
          
          {/* Status Indicator */}
          {isRunning && (
            <div className="absolute top-3 right-3">
              <div className="relative">
                <div className="absolute inset-0 bg-green-500 rounded-full blur-md animate-pulse" />
                <div className="relative w-2 h-2 bg-green-500 rounded-full" />
              </div>
            </div>
          )}
          
          <CardHeader className="p-4 pb-3 relative">
            <div className="flex items-start justify-between gap-2">
              <div className="flex items-start gap-3 flex-1">
                <motion.div 
                  className="relative"
                  animate={isHovered ? { rotate: [0, -10, 10, 0] } : {}}
                  transition={{ duration: 0.5 }}
                >
                  <div className="p-2 bg-gradient-to-br from-primary/20 to-primary/10 rounded-lg backdrop-blur-sm">
                    <Brain className="h-4 w-4 text-primary" />
                  </div>
                  {isRunning && (
                    <motion.div
                      className="absolute -bottom-1 -right-1"
                      animate={{ scale: [1, 1.2, 1] }}
                      transition={{ duration: 2, repeat: Infinity }}
                    >
                      <Activity className="h-3 w-3 text-green-500" />
                    </motion.div>
                  )}
                </motion.div>
                
                <div className="flex-1 min-w-0">
                  <CardTitle className="text-sm font-semibold bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
                    {agent.name}
                  </CardTitle>
                  {agent.default_task && (
                    <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                      {agent.default_task}
                    </p>
                  )}
                  
                  {/* Performance Badge */}
                  <div className="flex items-center gap-2 mt-2">
                    <Badge variant="outline" className="h-5 px-1.5 text-[10px] border-primary/30 bg-primary/5">
                      <TrendingUp className="h-2.5 w-2.5 mr-1" />
                      {successRate}%
                    </Badge>
                    {isRunning && (
                      <Badge variant="default" className="h-5 px-1.5 text-[10px] bg-green-500/20 text-green-600 border-green-500/30">
                        <Zap className="h-2.5 w-2.5 mr-1" />
                        Running
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
              
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-7 w-7 p-0 opacity-0 group-hover:opacity-100 transition-all duration-200"
                  >
                    <MoreVertical className="h-3.5 w-3.5" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="text-xs">
                  <DropdownMenuItem onClick={() => handleRunAgent(agent)}>
                    <Play className="mr-2 h-3 w-3" />
                    Run Agent
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleExportAgent(agent)}>
                    <Download className="mr-2 h-3 w-3" />
                    Export
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Shield className="mr-2 h-3 w-3" />
                    Configure
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem 
                    onClick={() => handleDeleteAgent(agent)}
                    className="text-destructive focus:text-destructive"
                  >
                    <Trash2 className="mr-2 h-3 w-3" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </CardHeader>
          
          <CardContent className="p-4 pt-0 space-y-3">
            {/* Stats Grid */}
            <div className="grid grid-cols-2 gap-2">
              <div className="flex items-center gap-1.5 text-xs">
                <Activity className="h-3 w-3 text-muted-foreground" />
                <span className="text-muted-foreground">Runs:</span>
                <span className="font-medium">128</span>
              </div>
              <div className="flex items-center gap-1.5 text-xs">
                <Clock className="h-3 w-3 text-muted-foreground" />
                <span className="text-muted-foreground">Avg:</span>
                <span className="font-medium">2.3s</span>
              </div>
            </div>
            
            {/* Action Buttons */}
            <div className="flex gap-2">
              <Button
                onClick={() => handleRunAgent(agent)}
                size="sm"
                className="flex-1 h-8 text-xs bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 transition-all duration-200"
                disabled={isRunning}
              >
                {isRunning ? (
                  <>
                    <Activity className="mr-1.5 h-3 w-3 animate-pulse" />
                    Running
                  </>
                ) : (
                  <>
                    <Play className="mr-1.5 h-3 w-3" />
                    Execute
                  </>
                )}
              </Button>
              
              <AnimatePresence>
                {isHovered && (
                  <motion.div
                    initial={{ width: 0, opacity: 0 }}
                    animate={{ width: "auto", opacity: 1 }}
                    exit={{ width: 0, opacity: 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Button
                      variant="outline"
                      size="sm"
                      className="h-8 px-2"
                      onClick={() => {}}
                    >
                      <Cpu className="h-3 w-3" />
                    </Button>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </CardContent>
          
          {/* Decorative Elements */}
          <motion.div
            className="absolute -bottom-1 -right-1 w-16 h-16 bg-primary/5 rounded-full blur-2xl"
            animate={isHovered ? { scale: [1, 1.5, 1] } : {}}
            transition={{ duration: 2 }}
          />
        </Card>
      </motion.div>
    );
  });

  // Enhanced List Item with Modern Design
  const AgentListItem = React.memo<{ agent: Agent; index: number }>(({ agent, index }) => {
    const isRunning = Math.random() > 0.85;
    const successRate = Math.floor(Math.random() * 30) + 70;
    
    return (
      <motion.div
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ 
          duration: 0.3, 
          delay: Math.min(index * 0.03, 0.3),
          type: "spring",
          stiffness: 300
        }}
        className="group relative overflow-hidden"
      >
        <div className="relative flex items-center justify-between p-3 px-4 border border-border/50 rounded-xl bg-gradient-to-r from-background via-background/98 to-muted/20 hover:from-muted/10 hover:to-muted/30 transition-all duration-300 group-hover:shadow-md">
          {/* Animated background accent */}
          <motion.div 
            className="absolute inset-0 bg-gradient-to-r from-primary/5 via-transparent to-primary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
            initial={{ x: "-100%" }}
            whileHover={{ x: "100%" }}
            transition={{ duration: 1.5 }}
          />
          
          <div className="relative flex items-center gap-4 flex-1 min-w-0">
            {/* Enhanced Icon with Status */}
            <div className="relative">
              <motion.div
                className="p-2 bg-gradient-to-br from-primary/20 to-primary/10 rounded-lg backdrop-blur-sm"
                whileHover={{ rotate: 360 }}
                transition={{ duration: 0.5 }}
              >
                <Brain className="h-4 w-4 text-primary" />
              </motion.div>
              {isRunning && (
                <div className="absolute -bottom-1 -right-1">
                  <div className="relative">
                    <div className="absolute inset-0 bg-green-500 rounded-full blur animate-pulse" />
                    <div className="w-2 h-2 bg-green-500 rounded-full" />
                  </div>
                </div>
              )}
            </div>
            
            {/* Agent Info */}
            <div className="flex-1 min-w-0">
              <h3 className="text-sm font-semibold text-foreground truncate flex items-center gap-2">
                {agent.name}
                {isRunning && (
                  <Badge variant="default" className="h-4 px-1 text-[10px] bg-green-500/20 text-green-600 border-green-500/30">
                    <Activity className="h-2.5 w-2.5 mr-0.5 animate-pulse" />
                    Live
                  </Badge>
                )}
              </h3>
              {agent.default_task && (
                <p className="text-xs text-muted-foreground truncate mt-0.5">
                  {agent.default_task}
                </p>
              )}
            </div>
            
            {/* Stats */}
            <div className="hidden md:flex items-center gap-4">
              <div className="flex items-center gap-1.5">
                <TrendingUp className="h-3 w-3 text-primary" />
                <span className="text-xs font-medium">{successRate}%</span>
              </div>
              <div className="flex items-center gap-1.5">
                <Activity className="h-3 w-3 text-muted-foreground" />
                <span className="text-xs text-muted-foreground">128 runs</span>
              </div>
              <div className="flex items-center gap-1.5">
                <Clock className="h-3 w-3 text-muted-foreground" />
                <span className="text-xs text-muted-foreground">2h ago</span>
              </div>
            </div>
          </div>
          
          {/* Actions */}
          <div className="relative flex items-center gap-2 ml-4">
            <div className="flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
              <Button
                onClick={() => handleExportAgent(agent)}
                size="sm"
                variant="ghost"
                className="h-7 w-7 p-0"
              >
                <Download className="h-3.5 w-3.5" />
              </Button>
              <Button
                onClick={() => {}}
                size="sm"
                variant="ghost"
                className="h-7 w-7 p-0"
              >
                <Shield className="h-3.5 w-3.5" />
              </Button>
              <Button
                onClick={() => handleDeleteAgent(agent)}
                size="sm"
                variant="ghost"
                className="h-7 w-7 p-0 text-destructive hover:text-destructive"
              >
                <Trash2 className="h-3.5 w-3.5" />
              </Button>
            </div>
            
            <Button
              onClick={() => handleRunAgent(agent)}
              size="sm"
              className="h-8 px-3 text-xs bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 transition-all duration-200"
              disabled={isRunning}
            >
              {isRunning ? (
                <>
                  <Activity className="mr-1.5 h-3 w-3 animate-pulse" />
                  Running
                </>
              ) : (
                <>
                  <Play className="mr-1.5 h-3 w-3" />
                  Execute
                </>
              )}
            </Button>
          </div>
        </div>
      </motion.div>
    );
  });

  return (
    <div className="flex flex-col gap-3 h-full">
      {/* Compact Controls Bar - 8px grid */}
      <div className="flex items-center justify-between gap-2">
        {/* Search */}
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2.5 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-muted-foreground" />
          <Input
            placeholder="Search agents..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="h-8 pl-8 text-sm"
          />
        </div>
        
        {/* Filters & Actions */}
        <div className="flex items-center gap-2">
          {/* Sort Dropdown */}
          <Select value={sortBy} onValueChange={(v) => setSortBy(v as any)}>
            <SelectTrigger className="h-8 w-32 text-xs">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="name">Name</SelectItem>
              <SelectItem value="date">Date Created</SelectItem>
              <SelectItem value="runs">Run Count</SelectItem>
            </SelectContent>
          </Select>
          
          {/* Category Filter */}
          <div className="flex items-center gap-1 p-0.5 bg-muted/50 rounded">
            <Button
              variant={filterCategory === 'all' ? 'secondary' : 'ghost'}
              size="sm"
              onClick={() => setFilterCategory('all')}
              className="h-7 px-2 text-xs"
            >
              All
            </Button>
            <Button
              variant={filterCategory === 'active' ? 'secondary' : 'ghost'}
              size="sm"
              onClick={() => setFilterCategory('active')}
              className="h-7 px-2 text-xs"
            >
              Active
            </Button>
            <Button
              variant={filterCategory === 'archived' ? 'secondary' : 'ghost'}
              size="sm"
              onClick={() => setFilterCategory('archived')}
              className="h-7 px-2 text-xs"
            >
              Archived
            </Button>
          </div>
          
          {/* View Mode */}
          <div className="flex items-center gap-1 p-0.5 bg-muted/50 rounded">
            <Button
              onClick={() => setViewMode('grid')}
              variant={viewMode === 'grid' ? 'secondary' : 'ghost'}
              size="sm"
              className="h-7 w-7 p-0"
            >
              <Grid className="h-3.5 w-3.5" />
            </Button>
            <Button
              onClick={() => setViewMode('list')}
              variant={viewMode === 'list' ? 'secondary' : 'ghost'}
              size="sm"
              className="h-7 w-7 p-0"
            >
              <List className="h-3.5 w-3.5" />
            </Button>
          </div>
          
          <Button
            onClick={handleRefresh}
            variant="outline"
            size="sm"
            disabled={loadingAgents}
            className="h-7 w-7 p-0"
          >
            <RefreshCw className={`h-3.5 w-3.5 ${loadingAgents ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </div>

      {/* Results Summary Bar */}
      <div className="flex items-center justify-between text-xs text-muted-foreground px-1">
        <span>
          {filteredAgents.length} agents
          {searchQuery && ` matching "${searchQuery}"`}
        </span>
        
        {selectedAgents.length > 0 && (
          <div className="flex items-center gap-2">
            <span>{selectedAgents.length} selected</span>
            <Button
              onClick={clearSelection}
              variant="ghost"
              size="sm"
              className="h-5 px-2 text-xs"
            >
              Clear
            </Button>
          </div>
        )}
      </div>

      {/* Agents Grid/List with Proper Spacing */}
      <div className="flex-1 overflow-auto">
        {loadingAgents ? (
          <div className="flex items-center justify-center py-16">
            <RefreshCw className="h-6 w-6 animate-spin text-muted-foreground" />
          </div>
        ) : filteredAgents.length === 0 ? (
          <motion.div 
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="flex flex-col items-center justify-center py-16 text-center"
          >
            <motion.div
              animate={{ y: [0, -8, 0] }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              <Bot className="h-12 w-12 text-muted-foreground mb-3" />
            </motion.div>
            <h3 className="text-base font-medium mb-1">No agents found</h3>
            <p className="text-sm text-muted-foreground mb-3">
              {searchQuery 
                ? `No agents match "${searchQuery}"`
                : 'Create your first agent to get started'
              }
            </p>
            {!searchQuery && (
              <Button 
                onClick={() => setSearchQuery('')}
                size="sm"
                className="h-8"
              >
                Create Agent
              </Button>
            )}
          </motion.div>
        ) : (
          <AnimatePresence mode="wait">
            {viewMode === 'grid' ? (
              <motion.div
                key="grid"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.2 }}
                className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-3"
              >
                {filteredAgents.map((agent, index) => (
                  <AgentCard key={agent.id} agent={agent} index={index} />
                ))}
              </motion.div>
            ) : (
              <motion.div
                key="list"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.2 }}
                className="space-y-2"
              >
                {filteredAgents.map((agent, index) => (
                  <AgentListItem key={agent.id} agent={agent} index={index} />
                ))}
              </motion.div>
            )}
          </AnimatePresence>
        )}
      </div>

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Delete Agent</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete "{agentToDelete?.name}"? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setShowDeleteDialog(false);
                setAgentToDelete(null);
              }}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              size="sm"
              onClick={confirmDelete}
            >
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};