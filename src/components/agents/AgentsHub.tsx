import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  <PERSON><PERSON>, 
  <PERSON>rkles, 
  Command, 
  Zap,
  ArrowLeft,
  Home,
  Plus,
  Upload,
  Network,
  Activity,
  CheckCircle,
  TrendingUp,
  Brain,
  Shield,
  Globe,
  Layers,
  Settings,
  MoreVertical,
  ChevronRight,
  Users,
  Clock,
  BarChart3
} from 'lucide-react';
import { Ta<PERSON>, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { AgentsHubProvider, useAgentsHub } from './hub/AgentsHubContext';
import { OverviewDashboard } from './hub/OverviewDashboard';
import { AgentsManagement } from './hub/AgentsManagement';
import { RunningAgentsView } from './hub/RunningAgentsView';
import { CreateAgentView } from './hub/CreateAgentView';
import { ImportExportView } from './hub/ImportExportView';
import { AgentWorkflowView } from './hub/AgentWorkflowView';
import { cn } from '@/lib/utils';

interface AgentsHubProps {
  onBack?: () => void;
}

const AgentsHubContent: React.FC<AgentsHubProps> = ({ onBack }) => {
  const { activeView, setActiveView, runningAgents, metrics } = useAgentsHub();
  const [isHeaderCompact, setIsHeaderCompact] = useState(false);
  const [showQuickStats, setShowQuickStats] = useState(true);

  useEffect(() => {
    const handleScroll = (e: Event) => {
      const target = e.target as HTMLElement;
      setIsHeaderCompact(target.scrollTop > 50);
    };
    return () => {};
  }, []);

  // Quick Stats Component
  const QuickStats = () => (
    <motion.div
      initial={{ opacity: 0, height: 0 }}
      animate={{ opacity: 1, height: 'auto' }}
      exit={{ opacity: 0, height: 0 }}
      className="grid grid-cols-4 gap-3 mt-4"
    >
      <motion.div 
        whileHover={{ scale: 1.02 }}
        className="px-3 py-2 bg-gradient-to-r from-green-500/10 to-green-500/5 rounded-lg border border-green-500/20"
      >
        <div className="flex items-center justify-between">
          <div>
            <p className="text-xs text-muted-foreground">Active</p>
            <p className="text-lg font-semibold text-green-600">{runningAgents.length}</p>
          </div>
          <Activity className="h-4 w-4 text-green-500" />
        </div>
      </motion.div>

      <motion.div 
        whileHover={{ scale: 1.02 }}
        className="px-3 py-2 bg-gradient-to-r from-blue-500/10 to-blue-500/5 rounded-lg border border-blue-500/20"
      >
        <div className="flex items-center justify-between">
          <div>
            <p className="text-xs text-muted-foreground">Total</p>
            <p className="text-lg font-semibold text-blue-600">{metrics?.totalAgents || 0}</p>
          </div>
          <Bot className="h-4 w-4 text-blue-500" />
        </div>
      </motion.div>

      <motion.div 
        whileHover={{ scale: 1.02 }}
        className="px-3 py-2 bg-gradient-to-r from-purple-500/10 to-purple-500/5 rounded-lg border border-purple-500/20"
      >
        <div className="flex items-center justify-between">
          <div>
            <p className="text-xs text-muted-foreground">Success</p>
            <p className="text-lg font-semibold text-purple-600">98%</p>
          </div>
          <TrendingUp className="h-4 w-4 text-purple-500" />
        </div>
      </motion.div>

      <motion.div 
        whileHover={{ scale: 1.02 }}
        className="px-3 py-2 bg-gradient-to-r from-amber-500/10 to-amber-500/5 rounded-lg border border-amber-500/20"
      >
        <div className="flex items-center justify-between">
          <div>
            <p className="text-xs text-muted-foreground">Workflows</p>
            <p className="text-lg font-semibold text-amber-600">12</p>
          </div>
          <Network className="h-4 w-4 text-amber-500" />
        </div>
      </motion.div>
    </motion.div>
  );

  return (
    <div className="h-full flex flex-col bg-gradient-to-br from-background via-background/98 to-muted/10">
      {/* Enhanced Header with Modern Design */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ 
          opacity: 1, 
          y: 0,
          height: isHeaderCompact ? 65 : 'auto'
        }}
        transition={{ duration: 0.3, type: "spring", stiffness: 300 }}
        className="flex-shrink-0 backdrop-blur-xl bg-background/80 border-b border-border/50 relative overflow-hidden"
      >
        {/* Animated Background Gradient - Subtle */}
        <div className="absolute inset-0 opacity-[0.03]">
          <div className="absolute inset-0 bg-gradient-to-r from-primary via-purple-500 to-blue-500 animate-gradient-x" />
        </div>

        {/* Floating Orbs Background Effect */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <motion.div
            animate={{ 
              x: [0, 100, 0],
              y: [0, -50, 0]
            }}
            transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
            className="absolute -top-20 -left-20 w-40 h-40 bg-primary/10 rounded-full blur-3xl"
          />
          <motion.div
            animate={{ 
              x: [0, -100, 0],
              y: [0, 50, 0]
            }}
            transition={{ duration: 25, repeat: Infinity, ease: "linear" }}
            className="absolute -bottom-20 -right-20 w-40 h-40 bg-purple-500/10 rounded-full blur-3xl"
          />
        </div>

        <div className="relative z-10 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              {onBack && (
                <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                  <Button
                    onClick={onBack}
                    variant="ghost"
                    size="sm"
                    className="hover:bg-primary/10 transition-all duration-200"
                  >
                    <ArrowLeft className="w-4 h-4" />
                  </Button>
                </motion.div>
              )}
              
              {/* Modern Logo with Pulse Effect */}
              <motion.div
                className="relative"
                whileHover={{ scale: 1.1 }}
                transition={{ type: "spring", stiffness: 400 }}
              >
                <div className="absolute inset-0 bg-gradient-to-r from-primary to-purple-600 rounded-xl blur-xl opacity-50 animate-pulse" />
                <div className="relative p-2.5 bg-gradient-to-br from-primary/90 to-purple-600/90 rounded-xl shadow-lg">
                  <Bot className="w-6 h-6 text-white" />
                </div>
                {/* Status Indicator */}
                <motion.div
                  animate={{ scale: [1, 1.2, 1] }}
                  transition={{ duration: 2, repeat: Infinity }}
                  className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-background"
                />
              </motion.div>
              
              <div className="flex flex-col">
                <motion.h1 
                  animate={{ fontSize: isHeaderCompact ? '1.125rem' : '1.5rem' }}
                  className="font-bold tracking-tight flex items-center gap-2"
                >
                  <span className="bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
                    Agents Hub
                  </span>
                  <Badge variant="outline" className="text-xs px-1.5 py-0.5 border-primary/30 text-primary">
                    BETA
                  </Badge>
                </motion.h1>
                <AnimatePresence>
                  {!isHeaderCompact && (
                    <motion.p
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      className="text-sm text-muted-foreground"
                    >
                      Orchestrate, Monitor & Scale Your AI Agents
                    </motion.p>
                  )}
                </AnimatePresence>
              </div>

              {/* Live Status Indicators */}
              <div className="flex items-center gap-2 ml-6">
                <motion.div
                  animate={{ opacity: [0.5, 1, 0.5] }}
                  transition={{ duration: 2, repeat: Infinity }}
                  className="flex items-center gap-1.5 px-2 py-1 bg-green-500/10 rounded-full"
                >
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                  <span className="text-xs text-green-600 font-medium">System Online</span>
                </motion.div>
                
                {runningAgents.length > 0 && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0 }}
                    animate={{ opacity: 1, scale: 1 }}
                    className="flex items-center gap-1.5 px-2 py-1 bg-blue-500/10 rounded-full"
                  >
                    <Activity className="w-3 h-3 text-blue-500 animate-pulse" />
                    <span className="text-xs text-blue-600 font-medium">
                      {runningAgents.length} Active
                    </span>
                  </motion.div>
                )}
              </div>
            </div>

            {/* Enhanced Quick Actions */}
            <div className="flex items-center gap-3">
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.2 }}
              >
                <Button
                  onClick={() => setActiveView('create')}
                  variant="default"
                  size="sm"
                  className="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 transition-all shadow-lg shadow-primary/20 group"
                >
                  <Plus className="w-4 h-4 mr-2 group-hover:rotate-90 transition-transform duration-300" />
                  New Agent
                </Button>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.3 }}
                className="flex items-center gap-2"
              >
                <Button
                  variant="ghost"
                  size="icon"
                  className="hover:bg-primary/10"
                  onClick={() => setShowQuickStats(!showQuickStats)}
                >
                  <BarChart3 className="w-4 h-4" />
                </Button>
                
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="hover:bg-primary/10"
                    >
                      <MoreVertical className="w-4 h-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-48">
                    <DropdownMenuItem>
                      <Settings className="w-4 h-4 mr-2" />
                      Settings
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Shield className="w-4 h-4 mr-2" />
                      Security
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Globe className="w-4 h-4 mr-2" />
                      API Keys
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem>
                      <Users className="w-4 h-4 mr-2" />
                      Collaboration
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </motion.div>
            </div>
          </div>

          {/* Quick Stats Row */}
          <AnimatePresence>
            {showQuickStats && !isHeaderCompact && <QuickStats />}
          </AnimatePresence>
        </div>
      </motion.div>

      {/* Enhanced Overview Dashboard */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.1 }}
        className="flex-shrink-0"
      >
        <OverviewDashboard />
      </motion.div>

      {/* Modern Tabbed Interface with Glass Effect */}
      <div className="flex-1 overflow-hidden">
        <Tabs 
          value={activeView === 'overview' ? 'available' : activeView} 
          onValueChange={(value) => setActiveView(value as any)}
          className="h-full flex flex-col"
        >
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="mx-6 mt-4 p-1.5 bg-gradient-to-r from-muted/20 via-muted/30 to-muted/20 rounded-xl backdrop-blur-sm"
          >
            <TabsList className="w-full grid grid-cols-5 bg-background/50 backdrop-blur-sm border border-border/30 rounded-lg p-1">
              <TabsTrigger 
                value="available" 
                className="relative data-[state=active]:bg-gradient-to-r data-[state=active]:from-primary/20 data-[state=active]:to-primary/10 data-[state=active]:shadow-sm transition-all duration-300"
              >
                <div className="flex items-center gap-2">
                  <Layers className="w-4 h-4" />
                  <span className="hidden sm:inline">Available</span>
                </div>
              </TabsTrigger>
              
              <TabsTrigger 
                value="running" 
                className="relative data-[state=active]:bg-gradient-to-r data-[state=active]:from-green-500/20 data-[state=active]:to-green-500/10 data-[state=active]:shadow-sm transition-all duration-300"
              >
                <div className="flex items-center gap-2">
                  <Zap className="w-4 h-4" />
                  <span className="hidden sm:inline">Running</span>
                  {runningAgents.length > 0 && (
                    <Badge variant="secondary" className="h-5 px-1.5 bg-green-500/20 text-green-700">
                      {runningAgents.length}
                    </Badge>
                  )}
                </div>
              </TabsTrigger>
              
              <TabsTrigger 
                value="create"
                className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500/20 data-[state=active]:to-blue-500/10 data-[state=active]:shadow-sm transition-all duration-300"
              >
                <div className="flex items-center gap-2">
                  <Plus className="w-4 h-4" />
                  <span className="hidden sm:inline">Create</span>
                </div>
              </TabsTrigger>
              
              <TabsTrigger 
                value="import-export"
                className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-500/20 data-[state=active]:to-purple-500/10 data-[state=active]:shadow-sm transition-all duration-300"
              >
                <div className="flex items-center gap-2">
                  <Upload className="w-4 h-4" />
                  <span className="hidden sm:inline">Import</span>
                </div>
              </TabsTrigger>
              
              <TabsTrigger 
                value="workflows"
                className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-orange-500/20 data-[state=active]:to-orange-500/10 data-[state=active]:shadow-sm transition-all duration-300"
              >
                <div className="flex items-center gap-2">
                  <Network className="w-4 h-4" />
                  <span className="hidden sm:inline">Workflows</span>
                </div>
              </TabsTrigger>
            </TabsList>
          </motion.div>

          <div className="flex-1 mt-4 overflow-hidden px-6">
            <AnimatePresence mode="wait">
              <TabsContent value="available" className="h-full m-0">
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  transition={{ duration: 0.3 }}
                  className="h-full"
                >
                  <AgentsManagement />
                </motion.div>
              </TabsContent>

              <TabsContent value="running" className="h-full m-0">
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  transition={{ duration: 0.3 }}
                  className="h-full"
                >
                  <RunningAgentsView />
                </motion.div>
              </TabsContent>

              <TabsContent value="create" className="h-full m-0">
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  transition={{ duration: 0.3 }}
                  className="h-full"
                >
                  <CreateAgentView />
                </motion.div>
              </TabsContent>

              <TabsContent value="import-export" className="h-full m-0">
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  transition={{ duration: 0.3 }}
                  className="h-full"
                >
                  <ImportExportView />
                </motion.div>
              </TabsContent>

              <TabsContent value="workflows" className="h-full m-0">
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  transition={{ duration: 0.3 }}
                  className="h-full"
                >
                  <AgentWorkflowView />
                </motion.div>
              </TabsContent>
            </AnimatePresence>
          </div>
        </Tabs>
      </div>
    </div>
  );
};

/**
 * Unified Agents Hub component with modern UI/UX design
 * 
 * Design Principles Applied:
 * - Monitor Dashboard Pattern: Real-time status indicators and metrics
 * - Card-based Layout: One card, one topic principle
 * - Visual Hierarchy: Important data in prominent positions
 * - Smooth Animations: Framer Motion for delightful interactions
 * - Glass Morphism: Modern translucent effects
 * - Color Psychology: Green for active, blue for info, amber for warnings
 * - Responsive Design: Adapts to different screen sizes
 * - Accessibility: Proper contrast ratios and focus states
 * 
 * @param onBack - Optional callback for navigation
 */
export const AgentsHub: React.FC<AgentsHubProps> = ({ onBack }) => {
  return (
    <AgentsHubProvider>
      <AgentsHubContent onBack={onBack} />
    </AgentsHubProvider>
  );
};

export default AgentsHub;