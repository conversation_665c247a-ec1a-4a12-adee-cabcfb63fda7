import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Eye, Edit, TrendingUp, TrendingDown } from 'lucide-react';
import { SkillMatrix } from '../common/types';

interface SkillsMatrixTableProps {
  skillMatrices: SkillMatrix[];
  onViewDetails?: (matrix: SkillMatrix) => void;
  onEdit?: (matrix: SkillMatrix) => void;
  className?: string;
}

export const SkillsMatrixTable: React.FC<SkillsMatrixTableProps> = ({
  skillMatrices,
  onViewDetails,
  onEdit,
  className = ''
}) => {
  const getPriorityBadge = (priority: string) => {
    const priorityConfig = {
      critical: { variant: 'destructive' as const, text: 'Critical', className: '' },
      high: { variant: 'default' as const, text: 'High', className: 'bg-orange-100 text-orange-800' },
      medium: { variant: 'default' as const, text: 'Medium', className: 'bg-yellow-100 text-yellow-800' },
      low: { variant: 'outline' as const, text: 'Low', className: '' }
    };
    
    const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig.low;
    return (
      <Badge variant={config.variant} className={config.className}>
        {config.text}
      </Badge>
    );
  };

  const getReadinessColor = (readiness: number) => {
    if (readiness >= 80) return 'text-green-600';
    if (readiness >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const calculateAverageGap = (matrix: SkillMatrix) => {
    const totalGap = matrix.competencies.reduce((sum, comp) => sum + comp.gap, 0);
    return matrix.competencies.length > 0 ? totalGap / matrix.competencies.length : 0;
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Skills Matrix Overview</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {skillMatrices.map((matrix) => {
            const avgGap = calculateAverageGap(matrix);
            const criticalGaps = matrix.competencies.filter(c => c.priority === 'critical').length;
            
            return (
              <div key={matrix.employeeId} className="border rounded-lg p-4 space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-semibold">{matrix.employeeName}</h3>
                    <p className="text-sm text-muted-foreground">
                      {matrix.role} • {matrix.department}
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    {onViewDetails && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onViewDetails(matrix)}
                      >
                        <Eye className="h-3 w-3 mr-1" />
                        View
                      </Button>
                    )}
                    {onEdit && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onEdit(matrix)}
                      >
                        <Edit className="h-3 w-3 mr-1" />
                        Edit
                      </Button>
                    )}
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="space-y-1">
                    <p className="text-xs text-muted-foreground">Overall Readiness</p>
                    <div className="flex items-center gap-2">
                      <Progress value={matrix.overallReadiness} className="flex-1 h-2" />
                      <span className={`text-sm font-medium ${getReadinessColor(matrix.overallReadiness)}`}>
                        {matrix.overallReadiness}%
                      </span>
                    </div>
                  </div>
                  
                  <div className="space-y-1">
                    <p className="text-xs text-muted-foreground">Average Gap</p>
                    <div className="flex items-center gap-1">
                      {avgGap > 0 ? (
                        <TrendingDown className="h-3 w-3 text-red-500" />
                      ) : (
                        <TrendingUp className="h-3 w-3 text-green-500" />
                      )}
                      <span className="text-sm font-medium">
                        {avgGap.toFixed(1)} levels
                      </span>
                    </div>
                  </div>
                  
                  <div className="space-y-1">
                    <p className="text-xs text-muted-foreground">Critical Gaps</p>
                    <div className="flex items-center gap-1">
                      <span className="text-sm font-medium">{criticalGaps}</span>
                      {criticalGaps > 0 && getPriorityBadge('critical')}
                    </div>
                  </div>
                  
                  <div className="space-y-1">
                    <p className="text-xs text-muted-foreground">Total Competencies</p>
                    <span className="text-sm font-medium">
                      {matrix.competencies.length}
                    </span>
                  </div>
                </div>
                
                {matrix.competencies.length > 0 && (
                  <div className="space-y-2">
                    <p className="text-xs text-muted-foreground">Top Priority Gaps:</p>
                    <div className="flex flex-wrap gap-1">
                      {matrix.competencies
                        .filter(c => c.priority === 'critical' || c.priority === 'high')
                        .slice(0, 3)
                        .map((comp, index) => (
                          <div key={index} className="flex items-center gap-1">
                            {getPriorityBadge(comp.priority)}
                            <span className="text-xs">Gap: {comp.gap}</span>
                          </div>
                        ))}
                    </div>
                  </div>
                )}
              </div>
            );
          })}
          
          {skillMatrices.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <p>No skills matrices available</p>
              <p className="text-sm">Add employees to start tracking competencies</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default SkillsMatrixTable;