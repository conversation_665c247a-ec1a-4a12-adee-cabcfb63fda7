import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/components/ui/use-toast';
// LearningPath type import removed - type no longer exists
import { 
  BookOpen,
  Brain,
  BarChart3,
  Users,
  Target,
  TrendingUp,
  FileText,
  MessageSquare,
  Calendar,
  Download,
  Upload,
  Plus,
  GitBranch,
  Clock,
  DollarSign,
  Percent,
  Star,
  GraduationCap,
  Search
} from 'lucide-react';

// Import extracted components and types
import { MetricCard, QuickActionCard } from './cards';
import { ViewNavigation, ADDIEPhaseNavigation } from './navigation';
import { ADDIETabs, CompetencyTabs, AnalyticsTabs, LibraryTabs, CollaborationTabs } from './tabs';
import { ADDIEProject, SkillMatrix, CompetencyModel } from './common';
import { useCompetencyModels, useSkillMatrices, useTemplates, useAnalytics } from '@/hooks/useApi';
import { TrainingDatabaseService } from '@/services/trainingDatabaseService';
import { fileUploadService } from '@/lib/services/fileUploadService';

// Interfaces are now imported from ./types.ts

export const LDProfessionalTNA: React.FC = () => {
  const [activeView, setActiveView] = useState<'dashboard' | 'addie' | 'competency' | 'analytics' | 'library' | 'collaboration'>('dashboard');
  const { toast } = useToast();

  // Use API hooks for data management
  const competencyModelsApi = useCompetencyModels();
  const skillMatricesApi = useSkillMatrices();
  const templatesApi = useTemplates();
  const analyticsApi = useAnalytics();

  // Get data from hooks
  const competencyModels = competencyModelsApi.data || [];
  const skillMatrices = skillMatricesApi.data || [];

  // Data initialization - fetch data using API hooks
   useEffect(() => {
     const initializeData = async () => {
       try {
         // Initialize sample data if database is empty
         await TrainingDatabaseService.initializeSampleData();
         
         // Fetch competency models and skill matrices on component mount
         await competencyModelsApi.refetch();
         await skillMatricesApi.refetch();
         await templatesApi.refetch();
         await analyticsApi.refetch();
         
         // Initialize default models if none exist
         if (competencyModels.length === 0) {
           await initializeDefaultModels();
         }
       } catch (error) {
         console.error('Failed to initialize data:', error);
         toast({
           title: "Initialization Error",
           description: "Failed to initialize training data. Some features may not work properly.",
           variant: "destructive"
         });
       }
     };
     
     initializeData();
   }, []);

  const initializeDefaultModels = async () => {
    try {
      // Check if models already exist
      if (competencyModels.length > 0) {
        return;
      }

      const defaultModels = [
        {
          name: 'Leadership Excellence',
          description: 'Core leadership competencies for managers and senior staff',
          category: 'leadership',
          levels: [
            {
              level: 1,
              name: 'Emerging Leader',
              description: 'Developing foundational leadership skills',
              behaviors: ['Shows initiative', 'Communicates clearly', 'Takes responsibility'],
              assessmentCriteria: ['Demonstrates basic leadership behaviors', 'Receives positive feedback from peers'],
              developmentActivities: ['Leadership fundamentals course', 'Mentoring program', 'Team project leadership']
            },
            {
              level: 2,
              name: 'Competent Leader',
              description: 'Consistently demonstrates effective leadership',
              behaviors: ['Motivates team members', 'Makes informed decisions', 'Manages conflicts effectively'],
              assessmentCriteria: ['Successfully leads team projects', 'Achieves team performance goals'],
              developmentActivities: ['Advanced leadership program', 'Cross-functional assignments', '360-degree feedback']
            },
            {
              level: 3,
              name: 'Expert Leader',
              description: 'Exceptional leadership with strategic impact',
              behaviors: ['Inspires organizational change', 'Develops other leaders', 'Drives innovation'],
              assessmentCriteria: ['Leads major organizational initiatives', 'Mentors other leaders successfully'],
              developmentActivities: ['Executive coaching', 'Strategic leadership program', 'Board presentations']
            }
          ],
          businessCriticality: 'critical',
          industryStandard: 'Leadership Excellence Framework'
        },
        {
          name: 'Technical Proficiency',
          description: 'Core technical skills for software development and IT operations',
          category: 'technical',
          levels: [
            {
              level: 1,
              name: 'Junior Developer',
              description: 'Basic technical skills and programming knowledge',
              behaviors: ['Writes clean code', 'Follows coding standards', 'Learns new technologies'],
              assessmentCriteria: ['Completes assigned tasks', 'Code passes review process'],
              developmentActivities: ['Technical training courses', 'Code review participation', 'Pair programming']
            },
            {
              level: 2,
              name: 'Mid-Level Developer',
              description: 'Solid technical foundation with independent work capability',
              behaviors: ['Designs system components', 'Troubleshoots complex issues', 'Mentors junior developers'],
              assessmentCriteria: ['Delivers features independently', 'Contributes to architecture decisions'],
              developmentActivities: ['Advanced technical certifications', 'Conference attendance', 'Open source contributions']
            },
            {
              level: 3,
              name: 'Senior Developer',
              description: 'Expert-level technical skills with architectural influence',
              behaviors: ['Leads technical decisions', 'Optimizes system performance', 'Drives technical innovation'],
              assessmentCriteria: ['Designs scalable solutions', 'Leads technical teams effectively'],
              developmentActivities: ['Technical leadership roles', 'Industry speaking engagements', 'Research projects']
            }
          ],
          businessCriticality: 'important',
          industryStandard: 'IEEE Software Engineering Standards'
        }
      ];

      // Create models using the API
      for (const model of defaultModels) {
        await competencyModelsApi.create(model);
      }

      toast({
        title: "Default Models Initialized",
        description: "Default competency models have been created successfully.",
      });
    } catch (error) {
      toast({
        title: "Initialization Failed",
        description: "Failed to initialize default competency models.",
        variant: "destructive"
      });
    }
  };

  // Button handlers
  const handleNewADDIEProject = () => {
    setActiveView('addie');
    toast({
      title: "New ADDIE Project",
      description: "Navigate to ADDIE Framework to create a new project.",
    });
  };

  const handleExportMatrix = async (matrixId: string) => {
     try {
       await competencyModelsApi.export(matrixId, 'excel');
       toast({
         title: "Matrix Exported",
         description: "Competency matrix has been successfully exported.",
       });
     } catch (error) {
       toast({
         title: "Export Failed",
         description: "Failed to export competency matrix. Please try again.",
         variant: "destructive"
       });
     }
   };

  const handleNewCompetencyModel = async () => {
    try {
      const newModel = {
          name: `New Competency Model ${competencyModels.length + 1}`,
          description: 'A new competency model for organizational development',
          version: '1.0',
          competencies: [],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          createdBy: 'current-user'
       };

      await competencyModelsApi.create(newModel);
      
      toast({
        title: "Competency Model Created",
        description: `New competency model "${newModel.name}" has been created successfully.`,
      });
    } catch (error) {
      toast({
        title: "Creation Failed",
        description: "Failed to create competency model. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleExportReport = async () => {
     try {
       // Mock export functionality since analytics API doesn't have exportReport method
       console.log('Exporting analytics report...');
       toast({
         title: "Report Exported",
         description: "Analytics report has been successfully exported.",
       });
     } catch (error) {
       toast({
         title: "Export Failed",
         description: "Failed to export analytics report. Please try again.",
         variant: "destructive"
       });
     }
   };

  const handleScheduleReport = async () => {
    try {
      const scheduleConfig = {
        reportType: 'analytics',
        frequency: 'monthly',
        recipients: ['<EMAIL>'],
        format: 'JSON'
      };

      // Mock schedule functionality since analytics API doesn't have scheduleReport method
       console.log('Scheduling report with config:', scheduleConfig);
      
      toast({
        title: "Report Scheduled",
        description: "Analytics report has been scheduled successfully.",
      });
    } catch (error) {
      toast({
        title: "Scheduling Failed",
        description: "Failed to schedule report. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleSearchTemplates = async () => {
    try {
      await templatesApi.search({ query: '', category: '', tags: [] });
       const searchResults = templatesApi.data;
      
      // Navigate to library view to show search results
      setActiveView('library');
      
      toast({
        title: "Template Search",
        description: `Found ${searchResults?.length || 0} templates. Navigating to Library to view results.`,
      });
    } catch (error) {
      toast({
        title: "Search Failed",
        description: "Failed to search templates. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleUploadTemplate = () => {
    // Create a file input element
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.accept = '.json,.csv,.xlsx,.pdf';
    fileInput.style.display = 'none';
    
    fileInput.onchange = async (event: any) => {
      const file = event.target.files[0];
      if (!file) return;
      
      // Use client-side validation from fileUploadService
      const validationResult = fileUploadService.validateTrainingTemplateFile(file);
      if (!validationResult.valid) {
        toast({
          title: "Upload Failed",
          description: validationResult.error || "Invalid file format or size.",
          variant: "destructive"
        });
        return;
      }
      
      try {
        const response = await fileUploadService.uploadTrainingTemplate(file);
        
        // Refresh templates list
        await templatesApi.refetch();
        
        toast({
          title: "Template Uploaded",
          description: `"${file.name}" has been successfully processed. ${response.message}`,
        });
      } catch (error) {
        console.error('Template upload error:', error);
        toast({
          title: "Upload Failed",
          description: error instanceof Error ? error.message : "Failed to upload template. Please try again.",
          variant: "destructive"
        });
      }
    };
    
    // Trigger file selection
    document.body.appendChild(fileInput);
    fileInput.click();
    document.body.removeChild(fileInput);
  };

  const handleInviteTeamMembers = () => {
    // Mock team member invitation data
    const invitationData = {
      id: `invite-${Date.now()}`,
      invitedBy: '<EMAIL>',
      invitedAt: new Date().toISOString(),
      members: [
        {
          email: '<EMAIL>',
          role: 'Instructional Designer',
          permissions: ['read', 'write', 'comment'],
          status: 'pending'
        },
        {
          email: '<EMAIL>',
          role: 'Subject Matter Expert',
          permissions: ['read', 'comment'],
          status: 'pending'
        },
        {
          email: '<EMAIL>',
          role: 'Project Manager',
          permissions: ['read', 'write', 'admin'],
          status: 'pending'
        }
      ],
      projectId: 'current-project',
      message: 'You have been invited to collaborate on the LD Professional TNA project.'
    };

    // Save invitation to localStorage
    const existingInvitations = JSON.parse(localStorage.getItem('teamInvitations') || '[]');
    const updatedInvitations = [...existingInvitations, invitationData];
    localStorage.setItem('teamInvitations', JSON.stringify(updatedInvitations));

    // Update team members list
    const existingTeamMembers = JSON.parse(localStorage.getItem('teamMembers') || '[]');
    const newTeamMembers = [...existingTeamMembers, ...invitationData.members];
    localStorage.setItem('teamMembers', JSON.stringify(newTeamMembers));

    toast({
      title: "Team Members Invited",
      description: `Invitations sent to ${invitationData.members.length} team members. They will receive email notifications.`,
    });
  };

  const handleNewCollaborationSpace = () => {
    // Create a new collaboration space
    const newCollaborationSpace = {
      id: `collab-${Date.now()}`,
      name: `Collaboration Space ${Date.now().toString().slice(-4)}`,
      description: 'A dedicated space for team collaboration on learning and development projects',
      type: 'project-workspace',
      privacy: 'team-only',
      createdBy: '<EMAIL>',
      createdAt: new Date().toISOString(),
      members: [
        {
          email: '<EMAIL>',
          role: 'owner',
          joinedAt: new Date().toISOString()
        }
      ],
      channels: [
        {
          id: 'general',
          name: 'General Discussion',
          description: 'General project discussions and updates',
          type: 'text'
        },
        {
          id: 'resources',
          name: 'Resources & Files',
          description: 'Share documents, templates, and learning materials',
          type: 'file-sharing'
        },
        {
          id: 'feedback',
          name: 'Feedback & Reviews',
          description: 'Provide feedback on learning content and assessments',
          type: 'discussion'
        }
      ],
      settings: {
        allowFileSharing: true,
        allowExternalGuests: false,
        notificationsEnabled: true,
        autoArchiveInactive: false
      },
      status: 'active'
    };

    // Save to localStorage
    const existingSpaces = JSON.parse(localStorage.getItem('collaborationSpaces') || '[]');
    const updatedSpaces = [...existingSpaces, newCollaborationSpace];
    localStorage.setItem('collaborationSpaces', JSON.stringify(updatedSpaces));

    // Navigate to collaboration view
    setActiveView('collaboration');

    toast({
      title: "Collaboration Space Created",
      description: `"${newCollaborationSpace.name}" has been created with ${newCollaborationSpace.channels.length} default channels.`,
    });
  };

  // Professional Dashboard View
  const renderDashboard = () => (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <MetricCard
          title="Active Projects"
          value="0"
          icon={BookOpen}
          iconColor="text-primary"
          badge={{ text: "0 in Analysis", variant: "secondary" }}
        />
        <MetricCard
          title="Skill Gaps Identified"
          value="0"
          icon={Target}
          iconColor="text-orange-500"
          badge={{ text: "18 Critical", variant: "destructive" }}
        />
        <MetricCard
          title="Learning Paths"
          value="0"
          icon={GitBranch}
          iconColor="text-blue-500"
          badge={{ text: "0 New", variant: "default" }}
        />
        <MetricCard
          title="Avg. Training ROI"
          value="0%"
          icon={TrendingUp}
          iconColor="text-green-500"
          badge={{ text: "0% vs Q3", variant: "outline" }}
        />
      </div>

      {/* Quick Actions */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">Quick Actions</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <QuickActionCard
            title="New ADDIE Project"
            icon={BookOpen}
            onClick={() => setActiveView('addie')}
          />
          <QuickActionCard
            title="Skills Assessment"
            icon={Brain}
            onClick={() => setActiveView('competency')}
          />
          <QuickActionCard
            title="Training Analytics"
            icon={BarChart3}
            onClick={() => setActiveView('analytics')}
          />
          <QuickActionCard
            title="Template Library"
            icon={FileText}
            onClick={() => setActiveView('library')}
          />
        </div>
      </Card>

      {/* Recent Projects */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">Recent ADDIE Projects</h3>
          <Button size="sm" onClick={handleNewADDIEProject}>
            <Plus className="h-4 w-4 mr-2" />
            New Project
          </Button>
        </div>
        <div className="space-y-3">
          {[
            // Empty array - projects should be loaded from actual source
          ].map((project: any, index) => (
            <div key={index} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
              <div className="flex-1">
                <h4 className="font-medium">{project.name}</h4>
                <div className="flex items-center gap-2 mt-1">
                  <Badge variant="outline" className="text-xs">{project.phase}</Badge>
                  <Badge variant={project.risk === 'low' ? 'secondary' : project.risk === 'medium' ? 'default' : 'destructive'} className="text-xs">
                    {project.risk} risk
                  </Badge>
                </div>
              </div>
              <div className="text-right">
                <div className="text-sm font-medium">{project.progress}%</div>
                <Progress value={project.progress} className="w-20 h-2 mt-1" />
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* Skills Gap Heatmap */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">Skills Gap Heatmap by Department</h3>
        <div className="grid grid-cols-5 gap-2">
          {/* Heatmap will be populated with real data */}
          {Array.from({ length: 0 }, (_, i) => {
            const intensity = 0;
            return (
              <div
                key={i}
                className={`aspect-square rounded flex items-center justify-center text-xs font-medium text-white bg-gray-200`}
              >
                {intensity}
              </div>
            );
          })}
          {/* Show placeholder when no data */}
          <div className="col-span-5 text-center py-8 text-muted-foreground">
            No skills gap data available
          </div>
        </div>
        <div className="flex items-center justify-between mt-4 text-sm text-muted-foreground">
          <span>Low Gap</span>
          <span>High Gap</span>
        </div>
      </Card>
    </div>
  );

  // ADDIE Framework View
  const renderADDIEView = () => (
    <div className="space-y-6">
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold">ADDIE Framework</h2>
            <p className="text-muted-foreground">Analysis, Design, Development, Implementation, Evaluation</p>
          </div>
          <Button onClick={handleNewADDIEProject}>
            <Plus className="h-4 w-4 mr-2" />
            New ADDIE Project
          </Button>
        </div>

        {/* ADDIE Phases Navigation */}
        <ADDIEPhaseNavigation 
          phases={[
          { id: 'analysis', name: 'Analysis', status: 'completed' },
          { id: 'design', name: 'Design', status: 'in_progress' },
          { id: 'development', name: 'Development', status: 'pending' },
          { id: 'implementation', name: 'Implementation', status: 'pending' },
          { id: 'evaluation', name: 'Evaluation', status: 'pending' }
        ]}
          currentPhase="analysis"
          onPhaseChange={() => {}}
        />

        <ADDIETabs className="mt-6" />
      </Card>
    </div>
  );

  // Competency Mapping View
  const renderCompetencyView = () => (
    <div className="space-y-6">
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold">Competency Mapping & Skills Matrix</h2>
            <p className="text-muted-foreground">Define competencies and assess skill levels across your organization</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={handleExportMatrix}>
              <Download className="h-4 w-4 mr-2" />
              Export Matrix
            </Button>
            <Button onClick={handleNewCompetencyModel}>
              <Plus className="h-4 w-4 mr-2" />
              New Competency Model
            </Button>
          </div>
        </div>

        <CompetencyTabs 
              competencyModels={competencyModels} 
              skillMatrices={skillMatrices} 
              onUpdateCompetencyModels={(models) => console.log('Update models:', models)}
              className="mt-6" 
            />
      </Card>
    </div>
  );

  // Advanced Analytics View
  const renderAnalyticsView = () => (
    <div className="space-y-6">
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold">Training Analytics & ROI Dashboard</h2>
            <p className="text-muted-foreground">Advanced insights and performance metrics for your training programs</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={handleExportReport}>
              <Download className="h-4 w-4 mr-2" />
              Export Report
            </Button>
            <Button variant="outline" onClick={handleScheduleReport}>
              <Calendar className="h-4 w-4 mr-2" />
              Schedule Report
            </Button>
          </div>
        </div>

        {/* ROI Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <MetricCard
            title="Total ROI"
            value="234%"
            icon={Percent}
            iconColor="text-green-600"
            valueColor="text-green-600"
            subtitle="+18% from last quarter"
          />
          <MetricCard
            title="Training Investment"
            value="$487K"
            icon={DollarSign}
            iconColor="text-blue-600"
            subtitle="73% of allocated budget"
          />
          <MetricCard
            title="Effectiveness Score"
            value="0"
            icon={Star}
            iconColor="text-yellow-500"
            subtitle="Out of 10 (Kirkpatrick L1-L2)"
          />
          <MetricCard
            title="Time to Proficiency"
            value="0%"
            icon={Clock}
            iconColor="text-purple-600"
            subtitle="Reduction from baseline"
          />
        </div>

        <AnalyticsTabs />
      </Card>
    </div>
  );

  // Template Library View
  const renderLibraryView = () => (
    <div className="space-y-6">
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold">Template Library & Best Practices</h2>
            <p className="text-muted-foreground">Industry-standard templates, methodologies, and best practices for L&D professionals</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={handleSearchTemplates}>
              <Search className="h-4 w-4 mr-2" />
              Search Templates
            </Button>
            <Button onClick={handleUploadTemplate}>
              <Upload className="h-4 w-4 mr-2" />
              Upload Template
            </Button>
          </div>
        </div>

        <LibraryTabs />
      </Card>
    </div>
  );

  // Collaboration & Team Management View
  const renderCollaborationView = () => (
    <div className="space-y-6">
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold">Team Collaboration & Knowledge Sharing</h2>
            <p className="text-muted-foreground">Collaborate with stakeholders, share insights, and manage team projects</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={handleInviteTeamMembers}>
              <Users className="h-4 w-4 mr-2" />
              Invite Team Members
            </Button>
            <Button onClick={handleNewCollaborationSpace}>
              <Plus className="h-4 w-4 mr-2" />
              New Collaboration Space
            </Button>
          </div>
        </div>

        <CollaborationTabs />
      </Card>
    </div>
  );

  const renderCurrentView = () => {
    switch (activeView) {
      case 'dashboard':
        return renderDashboard();
      case 'addie':
        return renderADDIEView();
      case 'competency':
        return renderCompetencyView();
      case 'analytics':
        return renderAnalyticsView();
      case 'library':
        return renderLibraryView();
      case 'collaboration':
        return renderCollaborationView();
      default:
        return renderDashboard();
    }
  };

  return (
    <div className="h-full overflow-y-auto">
      <div className="container mx-auto p-6 max-w-7xl">
        {/* Header */}
        <motion.div 
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-6"
        >
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-3xl font-bold tracking-tight flex items-center gap-3">
                <GraduationCap className="h-8 w-8" />
                L&D Professional Training Needs Analysis
              </h1>
              <p className="mt-1 text-sm text-muted-foreground">
                Advanced tools and frameworks for learning and development professionals
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline">ADDIE Framework</Badge>
              <Badge variant="outline">Kirkpatrick Model</Badge>
              <Badge variant="secondary">Professional</Badge>
            </div>
          </div>

          {/* Navigation */}
          <ViewNavigation 
            navigationItems={[
              { id: 'dashboard', name: 'Dashboard', icon: BarChart3 },
              { id: 'addie', name: 'ADDIE Framework', icon: BookOpen },
              { id: 'competency', name: 'Competency Mapping', icon: Brain },
              { id: 'analytics', name: 'Analytics & ROI', icon: TrendingUp },
              { id: 'library', name: 'Template Library', icon: FileText },
              { id: 'collaboration', name: 'Collaboration', icon: MessageSquare }
            ]}
            activeView={activeView}
            onViewChange={setActiveView}
          />
        </motion.div>

        {/* Main Content */}
        <AnimatePresence mode="wait">
          <motion.div
            key={activeView}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
          >
            {renderCurrentView()}
          </motion.div>
        </AnimatePresence>
      </div>
    </div>
  );
};

export default LDProfessionalTNA;