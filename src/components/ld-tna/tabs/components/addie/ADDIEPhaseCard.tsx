import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';

interface ADDIEPhaseCardProps {
  phase: 'analysis' | 'design' | 'development' | 'implementation' | 'evaluation';
  title: string;
  description?: string;
  children?: React.ReactNode;
  className?: string;
  onSave?: () => void;
  onCancel?: () => void;
  showActions?: boolean;
}

export const ADDIEPhaseCard: React.FC<ADDIEPhaseCardProps> = ({
  phase,
  title,
  description,
  children,
  className,
  onSave,
  onCancel,
  showActions = false
}) => {
  const getPhaseColor = (phase: string) => {
    switch (phase) {
      case 'analysis': return 'bg-blue-100 text-blue-800';
      case 'design': return 'bg-green-100 text-green-800';
      case 'development': return 'bg-yellow-100 text-yellow-800';
      case 'implementation': return 'bg-purple-100 text-purple-800';
      case 'evaluation': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card className={`p-4 ${className || ''}`}>
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <Badge className={getPhaseColor(phase)}>
            {phase.charAt(0).toUpperCase() + phase.slice(1)}
          </Badge>
          <h3 className="font-semibold">{title}</h3>
        </div>
        {showActions && (
          <div className="flex gap-2">
            {onCancel && (
              <Button variant="outline" size="sm" onClick={onCancel}>
                Cancel
              </Button>
            )}
            {onSave && (
              <Button size="sm" onClick={onSave}>
                Save
              </Button>
            )}
          </div>
        )}
      </div>
      
      {description && (
        <p className="text-sm text-muted-foreground mb-4">{description}</p>
      )}
      
      <div className="space-y-4">
        {children}
      </div>
    </Card>
  );
};

// Specific ADDIE phase form components
export const AnalysisPhaseCard: React.FC<{ className?: string }> = ({ className }) => {
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    performanceGap: '',
    audienceSize: '',
    skillLevel: ''
  });

  // Load saved data from localStorage
  useEffect(() => {
    const savedData = localStorage.getItem('addie-analysis-data');
    if (savedData) {
      setFormData(JSON.parse(savedData));
    }
  }, []);

  const handleSave = () => {
    if (!formData.performanceGap.trim()) {
      toast({
        title: "Validation Error",
        description: "Please describe the performance gap.",
        variant: "destructive"
      });
      return;
    }

    localStorage.setItem('addie-analysis-data', JSON.stringify(formData));
    toast({
      title: "Analysis Saved",
      description: "Your analysis phase data has been saved successfully."
    });
  };

  const handleCancel = () => {
    const savedData = localStorage.getItem('addie-analysis-data');
    if (savedData) {
      setFormData(JSON.parse(savedData));
    } else {
      setFormData({ performanceGap: '', audienceSize: '', skillLevel: '' });
    }
  };

  return (
    <ADDIEPhaseCard
      phase="analysis"
      title="Business Need Analysis"
      description="Identify performance gaps and define learning requirements"
      className={className}
      showActions
      onSave={handleSave}
      onCancel={handleCancel}
    >
      <div>
        <Label>Performance Gap Description</Label>
        <Textarea 
          placeholder="Describe the current performance gap and business impact..." 
          rows={3}
          value={formData.performanceGap}
          onChange={(e) => setFormData(prev => ({ ...prev, performanceGap: e.target.value }))}
        />
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label>Target Audience Size</Label>
          <Input 
            type="number" 
            placeholder="Number of learners"
            value={formData.audienceSize}
            onChange={(e) => setFormData(prev => ({ ...prev, audienceSize: e.target.value }))}
          />
        </div>
        <div>
          <Label>Current Skill Level</Label>
          <Select value={formData.skillLevel} onValueChange={(value) => setFormData(prev => ({ ...prev, skillLevel: value }))}>
            <SelectTrigger>
              <SelectValue placeholder="Select level" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="beginner">Beginner</SelectItem>
              <SelectItem value="intermediate">Intermediate</SelectItem>
              <SelectItem value="advanced">Advanced</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </ADDIEPhaseCard>
  );
};

export const DesignPhaseCard: React.FC<{ className?: string }> = ({ className }) => {
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    learningObjectives: '',
    deliveryMethod: '',
    duration: ''
  });

  useEffect(() => {
    const savedData = localStorage.getItem('addie-design-data');
    if (savedData) {
      setFormData(JSON.parse(savedData));
    }
  }, []);

  const handleSave = () => {
    if (!formData.learningObjectives.trim()) {
      toast({
        title: "Validation Error",
        description: "Please define learning objectives.",
        variant: "destructive"
      });
      return;
    }

    localStorage.setItem('addie-design-data', JSON.stringify(formData));
    toast({
      title: "Design Saved",
      description: "Your design phase data has been saved successfully."
    });
  };

  const handleCancel = () => {
    const savedData = localStorage.getItem('addie-design-data');
    if (savedData) {
      setFormData(JSON.parse(savedData));
    } else {
      setFormData({ learningObjectives: '', deliveryMethod: '', duration: '' });
    }
  };

  return (
    <ADDIEPhaseCard
      phase="design"
      title="Instructional Design Strategy"
      description="Define learning objectives and delivery methods"
      className={className}
      showActions
      onSave={handleSave}
      onCancel={handleCancel}
    >
      <div>
        <Label>Learning Objectives (SMART)</Label>
        <Textarea 
          placeholder="Define specific, measurable, achievable, relevant, time-bound objectives..." 
          rows={4}
          value={formData.learningObjectives}
          onChange={(e) => setFormData(prev => ({ ...prev, learningObjectives: e.target.value }))}
        />
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label>Delivery Method</Label>
          <Select value={formData.deliveryMethod} onValueChange={(value) => setFormData(prev => ({ ...prev, deliveryMethod: value }))}>
            <SelectTrigger>
              <SelectValue placeholder="Select delivery method" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="elearning">E-Learning</SelectItem>
              <SelectItem value="classroom">Classroom</SelectItem>
              <SelectItem value="blended">Blended</SelectItem>
              <SelectItem value="microlearning">Microlearning</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label>Duration (Hours)</Label>
          <Input 
            type="number" 
            placeholder="Total training hours"
            value={formData.duration}
            onChange={(e) => setFormData(prev => ({ ...prev, duration: e.target.value }))}
          />
        </div>
      </div>
    </ADDIEPhaseCard>
  );
};

export const DevelopmentPhaseCard: React.FC<{ className?: string }> = ({ className }) => {
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    contentType: '',
    developmentTimeline: '',
    resourceRequirements: ''
  });

  useEffect(() => {
    const savedData = localStorage.getItem('addie-development-data');
    if (savedData) {
      setFormData(JSON.parse(savedData));
    }
  }, []);

  const handleSave = () => {
    if (!formData.resourceRequirements.trim()) {
      toast({
        title: "Validation Error",
        description: "Please provide resource requirements.",
        variant: "destructive"
      });
      return;
    }

    localStorage.setItem('addie-development-data', JSON.stringify(formData));
    toast({
      title: "Development Saved",
      description: "Your development phase data has been saved successfully."
    });
  };

  const handleCancel = () => {
    const savedData = localStorage.getItem('addie-development-data');
    if (savedData) {
      setFormData(JSON.parse(savedData));
    } else {
      setFormData({ contentType: '', developmentTimeline: '', resourceRequirements: '' });
    }
  };

  return (
    <ADDIEPhaseCard
      phase="development"
      title="Content Development"
      description="Create and develop learning materials and resources"
      className={className}
      showActions
      onSave={handleSave}
      onCancel={handleCancel}
    >
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label>Content Type</Label>
          <Select value={formData.contentType} onValueChange={(value) => setFormData(prev => ({ ...prev, contentType: value }))}>
            <SelectTrigger>
              <SelectValue placeholder="Select content type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="video">Video Content</SelectItem>
              <SelectItem value="interactive">Interactive Modules</SelectItem>
              <SelectItem value="documents">Documents & PDFs</SelectItem>
              <SelectItem value="simulations">Simulations</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label>Development Timeline</Label>
          <Input 
            type="date"
            value={formData.developmentTimeline}
            onChange={(e) => setFormData(prev => ({ ...prev, developmentTimeline: e.target.value }))}
          />
        </div>
      </div>
      <div>
        <Label>Resource Requirements</Label>
        <Textarea 
          placeholder="List required resources, tools, and team members..." 
          rows={3}
          value={formData.resourceRequirements}
          onChange={(e) => setFormData(prev => ({ ...prev, resourceRequirements: e.target.value }))}
        />
      </div>
    </ADDIEPhaseCard>
  );
};

export const ImplementationPhaseCard: React.FC<{ className?: string }> = ({ className }) => {
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    launchDate: '',
    pilotGroupSize: '',
    implementationStrategy: ''
  });

  useEffect(() => {
    const savedData = localStorage.getItem('addie-implementation-data');
    if (savedData) {
      setFormData(JSON.parse(savedData));
    }
  }, []);

  const handleSave = () => {
    if (!formData.implementationStrategy.trim()) {
      toast({
        title: "Validation Error",
        description: "Please describe the implementation strategy.",
        variant: "destructive"
      });
      return;
    }

    localStorage.setItem('addie-implementation-data', JSON.stringify(formData));
    toast({
      title: "Implementation Saved",
      description: "Your implementation phase data has been saved successfully."
    });
  };

  const handleCancel = () => {
    const savedData = localStorage.getItem('addie-implementation-data');
    if (savedData) {
      setFormData(JSON.parse(savedData));
    } else {
      setFormData({ launchDate: '', pilotGroupSize: '', implementationStrategy: '' });
    }
  };

  return (
    <ADDIEPhaseCard
      phase="implementation"
      title="Implementation Plan"
      description="Deploy and deliver the training program"
      className={className}
      showActions
      onSave={handleSave}
      onCancel={handleCancel}
    >
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label>Launch Date</Label>
          <Input 
            type="date"
            value={formData.launchDate}
            onChange={(e) => setFormData(prev => ({ ...prev, launchDate: e.target.value }))}
          />
        </div>
        <div>
          <Label>Pilot Group Size</Label>
          <Input 
            type="number" 
            placeholder="Number of pilot participants"
            value={formData.pilotGroupSize}
            onChange={(e) => setFormData(prev => ({ ...prev, pilotGroupSize: e.target.value }))}
          />
        </div>
      </div>
      <div>
        <Label>Implementation Strategy</Label>
        <Textarea 
          placeholder="Describe rollout plan, communication strategy, and support mechanisms..." 
          rows={3}
          value={formData.implementationStrategy}
          onChange={(e) => setFormData(prev => ({ ...prev, implementationStrategy: e.target.value }))}
        />
      </div>
    </ADDIEPhaseCard>
  );
};

export const EvaluationPhaseCard: React.FC<{ className?: string }> = ({ className }) => {
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    evaluationMethod: '',
    evaluationTimeline: '',
    successMetrics: ''
  });

  useEffect(() => {
    const savedData = localStorage.getItem('addie-evaluation-data');
    if (savedData) {
      setFormData(JSON.parse(savedData));
    }
  }, []);

  const handleSave = () => {
    if (!formData.successMetrics.trim()) {
      toast({
        title: "Validation Error",
        description: "Please define success metrics.",
        variant: "destructive"
      });
      return;
    }

    localStorage.setItem('addie-evaluation-data', JSON.stringify(formData));
    toast({
      title: "Evaluation Saved",
      description: "Your evaluation phase data has been saved successfully."
    });
  };

  const handleCancel = () => {
    const savedData = localStorage.getItem('addie-evaluation-data');
    if (savedData) {
      setFormData(JSON.parse(savedData));
    } else {
      setFormData({ evaluationMethod: '', evaluationTimeline: '', successMetrics: '' });
    }
  };

  return (
    <ADDIEPhaseCard
      phase="evaluation"
      title="Training Evaluation"
      description="Assess training effectiveness and measure outcomes"
      className={className}
      showActions
      onSave={handleSave}
      onCancel={handleCancel}
    >
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label>Evaluation Method</Label>
          <Select value={formData.evaluationMethod} onValueChange={(value) => setFormData(prev => ({ ...prev, evaluationMethod: value }))}>
            <SelectTrigger>
              <SelectValue placeholder="Select evaluation method" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="kirkpatrick">Kirkpatrick Model</SelectItem>
              <SelectItem value="phillips">Phillips ROI Model</SelectItem>
              <SelectItem value="cipp">CIPP Model</SelectItem>
              <SelectItem value="custom">Custom Framework</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label>Evaluation Timeline</Label>
          <Input 
            type="date"
            value={formData.evaluationTimeline}
            onChange={(e) => setFormData(prev => ({ ...prev, evaluationTimeline: e.target.value }))}
          />
        </div>
      </div>
      <div>
        <Label>Success Metrics</Label>
        <Textarea 
          placeholder="Define key performance indicators and success criteria..." 
          rows={3}
          value={formData.successMetrics}
          onChange={(e) => setFormData(prev => ({ ...prev, successMetrics: e.target.value }))}
        />
      </div>
    </ADDIEPhaseCard>
  );
};

export default ADDIEPhaseCard;