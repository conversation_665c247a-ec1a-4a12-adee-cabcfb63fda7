import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { CompetencyModel, SkillMatrix } from '../../../common/types';

// Extended interface for the card component
interface CompetencyModelWithDetails extends CompetencyModel {
  status: 'active' | 'draft' | 'archived';
  department: string;
  role: string;
  competencies: {
    name: string;
    description: string;
    skills: {
      name: string;
      currentLevel: number;
      requiredLevel: number;
    }[];
  }[];
  createdAt: string;
  updatedAt: string;
}

interface CompetencyModelCardProps {
  model: CompetencyModelWithDetails;
  onEdit?: (model: CompetencyModelWithDetails) => void;
  onDelete?: (modelId: string) => void;
  onViewDetails?: (model: CompetencyModelWithDetails) => void;
  className?: string;
  showActions?: boolean;
}

export const CompetencyModelCard: React.FC<CompetencyModelCardProps> = ({
  model,
  onEdit,
  onDelete,
  onViewDetails,
  className,
  showActions = true
}) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'draft': return 'bg-yellow-100 text-yellow-800';
      case 'archived': return 'bg-gray-100 text-gray-800';
      default: return 'bg-blue-100 text-blue-800';
    }
  };

  const calculateCompletionRate = () => {
    if (!model.competencies || model.competencies.length === 0) return 0;
    const totalSkills = model.competencies.reduce((acc: number, comp: any) => acc + comp.skills.length, 0);
    const completedSkills = model.competencies.reduce(
      (acc: number, comp: any) => acc + comp.skills.filter((skill: any) => skill.currentLevel >= skill.requiredLevel).length,
      0
    );
    return totalSkills > 0 ? Math.round((completedSkills / totalSkills) * 100) : 0;
  };

  const completionRate = calculateCompletionRate();

  return (
    <Card className={`p-4 hover:shadow-md transition-shadow ${className || ''}`}>
      <div className="flex items-start justify-between mb-3">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-2">
            <h3 className="font-semibold text-lg">{model.name}</h3>
            <Badge className={getStatusColor(model.status)}>
              {model.status.charAt(0).toUpperCase() + model.status.slice(1)}
            </Badge>
          </div>
          <p className="text-sm text-muted-foreground mb-3">{model.description}</p>
        </div>
        {showActions && (
          <div className="flex gap-2 ml-4">
            {onViewDetails && (
              <Button variant="outline" size="sm" onClick={() => onViewDetails(model)}>
                View
              </Button>
            )}
            {onEdit && (
              <Button variant="outline" size="sm" onClick={() => onEdit(model)}>
                Edit
              </Button>
            )}
            {onDelete && (
              <Button variant="destructive" size="sm" onClick={() => onDelete(model.id)}>
                Delete
              </Button>
            )}
          </div>
        )}
      </div>

      <div className="space-y-3">
        <div className="flex items-center justify-between text-sm">
          <span className="text-muted-foreground">Department:</span>
          <Badge variant="secondary">{model.department}</Badge>
        </div>

        <div className="flex items-center justify-between text-sm">
          <span className="text-muted-foreground">Role:</span>
          <span className="font-medium">{model.role}</span>
        </div>

        <div className="flex items-center justify-between text-sm">
          <span className="text-muted-foreground">Competencies:</span>
          <span className="font-medium">{model.competencies?.length || 0}</span>
        </div>

        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Completion Rate:</span>
            <span className="font-medium">{completionRate}%</span>
          </div>
          <Progress value={completionRate} className="h-2" />
        </div>

        {model.competencies && model.competencies.length > 0 && (
          <div className="pt-2 border-t">
            <p className="text-xs text-muted-foreground mb-2">Top Competencies:</p>
            <div className="flex flex-wrap gap-1">
              {model.competencies.slice(0, 3).map((competency: any, index: number) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {competency.name}
                </Badge>
              ))}
              {model.competencies.length > 3 && (
                <Badge variant="outline" className="text-xs">
                  +{model.competencies.length - 3} more
                </Badge>
              )}
            </div>
          </div>
        )}

        <div className="flex items-center justify-between text-xs text-muted-foreground pt-2 border-t">
          <span>Created: {new Date(model.createdAt).toLocaleDateString()}</span>
          <span>Updated: {new Date(model.updatedAt).toLocaleDateString()}</span>
        </div>
      </div>
    </Card>
  );
};

// Competency detail card for expanded view
export const CompetencyDetailCard: React.FC<{
  competency: CompetencyModelWithDetails['competencies'][0];
  className?: string;
}> = ({ competency, className }) => {
  const averageLevel = competency.skills.reduce((acc: number, skill: any) => acc + skill.currentLevel, 0) / competency.skills.length;
  const requiredLevel = competency.skills.reduce((acc: number, skill: any) => acc + skill.requiredLevel, 0) / competency.skills.length;
  const gapPercentage = Math.max(0, ((requiredLevel - averageLevel) / requiredLevel) * 100);

  return (
    <Card className={`p-4 ${className || ''}`}>
      <div className="flex items-center justify-between mb-3">
        <h4 className="font-semibold">{competency.name}</h4>
        <Badge variant={gapPercentage > 20 ? 'destructive' : gapPercentage > 10 ? 'secondary' : 'default'}>
          {gapPercentage.toFixed(0)}% Gap
        </Badge>
      </div>
      
      <p className="text-sm text-muted-foreground mb-4">{competency.description}</p>
      
      <div className="space-y-3">
        <div className="flex items-center justify-between text-sm">
          <span>Skills Count:</span>
          <span className="font-medium">{competency.skills.length}</span>
        </div>
        
        <div className="flex items-center justify-between text-sm">
          <span>Average Current Level:</span>
          <span className="font-medium">{averageLevel.toFixed(1)}/5</span>
        </div>
        
        <div className="flex items-center justify-between text-sm">
          <span>Required Level:</span>
          <span className="font-medium">{requiredLevel.toFixed(1)}/5</span>
        </div>
        
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span>Progress:</span>
            <span className="font-medium">{Math.max(0, 100 - gapPercentage).toFixed(0)}%</span>
          </div>
          <Progress value={Math.max(0, 100 - gapPercentage)} className="h-2" />
        </div>
      </div>
    </Card>
  );
};

export default CompetencyModelCard;