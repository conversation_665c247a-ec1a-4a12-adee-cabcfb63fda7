import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Play,
  Download,
  Star,
  Clock,
  Users,
  BarChart3,
  FileText,
  CheckCircle,
  AlertCircle,
  Calendar,
  Target,
  TrendingUp
} from 'lucide-react';

export interface AssessmentTool {
  id: string;
  title: string;
  description: string;
  type: 'quiz' | 'survey' | 'practical' | 'simulation' | 'portfolio' | 'peer-review';
  category: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  duration: number; // in minutes
  questions: number;
  passingScore: number;
  maxAttempts: number;
  tags: string[];
  author: {
    name: string;
    avatar?: string;
  };
  createdAt: string;
  updatedAt: string;
  rating: number;
  totalRatings: number;
  completions: number;
  averageScore: number;
  isActive: boolean;
  competencies: string[];
  prerequisites?: string[];
  certification?: {
    name: string;
    validityPeriod: number; // in months
  };
}

export interface AssessmentToolCardProps {
  tool: AssessmentTool;
  variant?: 'default' | 'compact' | 'detailed';
  onStart?: (toolId: string) => void;
  onDownload?: (toolId: string) => void;
  onRate?: (toolId: string, rating: number) => void;
  onViewResults?: (toolId: string) => void;
}

const getTypeColor = (type: AssessmentTool['type']) => {
  const colors = {
    quiz: 'bg-blue-100 text-blue-800',
    survey: 'bg-green-100 text-green-800',
    practical: 'bg-purple-100 text-purple-800',
    simulation: 'bg-orange-100 text-orange-800',
    portfolio: 'bg-pink-100 text-pink-800',
    'peer-review': 'bg-indigo-100 text-indigo-800'
  };
  return colors[type] || 'bg-gray-100 text-gray-800';
};

const getDifficultyColor = (difficulty: AssessmentTool['difficulty']) => {
  const colors = {
    beginner: 'bg-green-100 text-green-800',
    intermediate: 'bg-yellow-100 text-yellow-800',
    advanced: 'bg-red-100 text-red-800'
  };
  return colors[difficulty];
};

const formatDuration = (minutes: number) => {
  if (minutes < 60) return `${minutes}m`;
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

export const AssessmentToolCard: React.FC<AssessmentToolCardProps> = ({
  tool,
  variant = 'default',
  onStart,
  onDownload,
  onRate,
  onViewResults
}) => {
  if (variant === 'compact') {
    return (
      <Card className="hover:shadow-md transition-shadow">
        <CardContent className="p-4">
          <div className="flex items-start justify-between mb-2">
            <div className="flex-1">
              <h3 className="font-semibold text-sm mb-1 line-clamp-1">{tool.title}</h3>
              <p className="text-xs text-muted-foreground line-clamp-2 mb-2">
                {tool.description}
              </p>
            </div>
            <Badge className={getTypeColor(tool.type)}>
              {tool.type}
            </Badge>
          </div>
          
          <div className="flex items-center justify-between text-xs text-muted-foreground mb-3">
            <div className="flex items-center gap-3">
              <span className="flex items-center gap-1">
                <Clock className="w-3 h-3" />
                {formatDuration(tool.duration)}
              </span>
              <span className="flex items-center gap-1">
                <FileText className="w-3 h-3" />
                {tool.questions}Q
              </span>
              <span className="flex items-center gap-1">
                <Star className="w-3 h-3 fill-current" />
                {tool.rating.toFixed(1)}
              </span>
            </div>
          </div>
          
          <div className="flex gap-2">
            <Button size="sm" onClick={() => onStart?.(tool.id)} className="flex-1">
              <Play className="w-3 h-3 mr-1" />
              Start
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => onDownload?.(tool.id)}
            >
              <Download className="w-3 h-3" />
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (variant === 'detailed') {
    return (
      <Card className="hover:shadow-lg transition-shadow">
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <CardTitle className="text-xl mb-2">{tool.title}</CardTitle>
              <p className="text-muted-foreground mb-4">{tool.description}</p>
              
              <div className="flex flex-wrap gap-2 mb-4">
                <Badge className={getTypeColor(tool.type)}>
                  {tool.type}
                </Badge>
                <Badge className={getDifficultyColor(tool.difficulty)}>
                  {tool.difficulty}
                </Badge>
                <Badge variant="outline">{tool.category}</Badge>
                {!tool.isActive && (
                  <Badge variant="secondary">
                    <AlertCircle className="w-3 h-3 mr-1" />
                    Inactive
                  </Badge>
                )}
              </div>
            </div>
            
            <div className="text-right">
              <div className="flex items-center gap-1 mb-1">
                <Star className="w-4 h-4 fill-current text-yellow-400" />
                <span className="font-semibold">{tool.rating.toFixed(1)}</span>
                <span className="text-sm text-muted-foreground">({tool.totalRatings})</span>
              </div>
              <div className="text-sm text-muted-foreground">
                {tool.completions} completions
              </div>
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2 flex items-center gap-2">
                  <BarChart3 className="w-4 h-4" />
                  Assessment Details
                </h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Duration:</span>
                    <span>{formatDuration(tool.duration)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Questions:</span>
                    <span>{tool.questions}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Passing Score:</span>
                    <span>{tool.passingScore}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Max Attempts:</span>
                    <span>{tool.maxAttempts}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Average Score:</span>
                    <span className="flex items-center gap-1">
                      {tool.averageScore}%
                      <TrendingUp className="w-3 h-3 text-green-500" />
                    </span>
                  </div>
                </div>
              </div>
              
              {tool.certification && (
                <div>
                  <h4 className="font-semibold mb-2 flex items-center gap-2">
                    <CheckCircle className="w-4 h-4" />
                    Certification
                  </h4>
                  <div className="text-sm">
                    <div className="flex justify-between mb-1">
                      <span className="text-muted-foreground">Certificate:</span>
                      <span>{tool.certification.name}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Valid for:</span>
                      <span>{tool.certification.validityPeriod} months</span>
                    </div>
                  </div>
                </div>
              )}
            </div>
            
            <div className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2 flex items-center gap-2">
                  <Target className="w-4 h-4" />
                  Competencies
                </h4>
                <div className="flex flex-wrap gap-1">
                  {tool.competencies.map((competency, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {competency}
                    </Badge>
                  ))}
                </div>
              </div>
              
              {tool.prerequisites && tool.prerequisites.length > 0 && (
                <div>
                  <h4 className="font-semibold mb-2">Prerequisites</h4>
                  <div className="flex flex-wrap gap-1">
                    {tool.prerequisites.map((prereq, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {prereq}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
              
              <div>
                <h4 className="font-semibold mb-2">Tags</h4>
                <div className="flex flex-wrap gap-1">
                  {tool.tags.map((tag, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      #{tag}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          </div>
          
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <Avatar className="w-8 h-8">
                <AvatarImage src={tool.author.avatar} />
                <AvatarFallback>
                  {tool.author.name.split(' ').map((n: string) => n[0]).join('')}
                </AvatarFallback>
              </Avatar>
              <div>
                <div className="text-sm font-medium">{tool.author.name}</div>
                <div className="text-xs text-muted-foreground">
                  Created {formatDate(tool.createdAt)}
                </div>
              </div>
            </div>
            
            <div className="text-right text-xs text-muted-foreground">
              <div>Updated {formatDate(tool.updatedAt)}</div>
            </div>
          </div>
          
          <div className="flex gap-3">
            <Button onClick={() => onStart?.(tool.id)} className="flex-1">
              <Play className="w-4 h-4 mr-2" />
              Start Assessment
            </Button>
            <Button variant="outline" onClick={() => onDownload?.(tool.id)}>
              <Download className="w-4 h-4 mr-2" />
              Download
            </Button>
            <Button variant="outline" onClick={() => onViewResults?.(tool.id)}>
              <BarChart3 className="w-4 h-4 mr-2" />
              Results
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Default variant
  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg mb-2">{tool.title}</CardTitle>
            <p className="text-muted-foreground text-sm mb-3 line-clamp-2">
              {tool.description}
            </p>
            
            <div className="flex flex-wrap gap-2 mb-3">
              <Badge className={getTypeColor(tool.type)}>
                {tool.type}
              </Badge>
              <Badge className={getDifficultyColor(tool.difficulty)}>
                {tool.difficulty}
              </Badge>
              <Badge variant="outline">{tool.category}</Badge>
            </div>
          </div>
          
          <div className="text-right">
            <div className="flex items-center gap-1 mb-1">
              <Star className="w-4 h-4 fill-current text-yellow-400" />
              <span className="font-semibold">{tool.rating.toFixed(1)}</span>
            </div>
            <div className="text-xs text-muted-foreground">
              {tool.totalRatings} ratings
            </div>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="flex items-center justify-between text-sm text-muted-foreground mb-4">
          <div className="flex items-center gap-4">
            <span className="flex items-center gap-1">
              <Clock className="w-4 h-4" />
              {formatDuration(tool.duration)}
            </span>
            <span className="flex items-center gap-1">
              <FileText className="w-4 h-4" />
              {tool.questions} questions
            </span>
            <span className="flex items-center gap-1">
              <Users className="w-4 h-4" />
              {tool.completions} completed
            </span>
          </div>
        </div>
        
        <div className="mb-4">
          <div className="flex justify-between text-sm mb-1">
            <span>Average Score</span>
            <span>{tool.averageScore}%</span>
          </div>
          <Progress value={tool.averageScore} className="h-2" />
        </div>
        
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <Avatar className="w-6 h-6">
              <AvatarImage src={tool.author.avatar} />
              <AvatarFallback className="text-xs">
                {tool.author.name.split(' ').map((n: string) => n[0]).join('')}
              </AvatarFallback>
            </Avatar>
            <span className="text-sm text-muted-foreground">{tool.author.name}</span>
          </div>
          
          <div className="text-xs text-muted-foreground">
            {formatDate(tool.createdAt)}
          </div>
        </div>
        
        <div className="flex gap-2">
          <Button onClick={() => onStart?.(tool.id)} className="flex-1">
            <Play className="w-4 h-4 mr-2" />
            Start
          </Button>
          <Button variant="outline" onClick={() => onDownload?.(tool.id)}>
            <Download className="w-4 h-4" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default AssessmentToolCard;