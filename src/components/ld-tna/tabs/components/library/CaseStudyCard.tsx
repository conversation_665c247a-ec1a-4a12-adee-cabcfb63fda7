import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  BookOpen,
  Download,
  Star,
  ThumbsUp,
  Share2,
  Bookmark,
  Eye,
  Clock,
  Users,
  Award,
  TrendingUp,
  CheckCircle,
  Building,
  Target,
  BarChart3,
  DollarSign,
  Calendar,
  MapPin,
  Lightbulb,
  FileText
} from 'lucide-react';

export interface CaseStudy {
  id: string;
  title: string;
  subtitle?: string;
  description: string;
  summary: string;
  content: string;
  category: string;
  industry: string;
  organizationType: 'startup' | 'sme' | 'enterprise' | 'nonprofit' | 'government' | 'education';
  organizationSize: string;
  location: string;
  tags: string[];
  author: {
    name: string;
    title?: string;
    avatar?: string;
    organization?: string;
  };
  organization: {
    name: string;
    logo?: string;
    website?: string;
  };
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  rating: number;
  totalRatings: number;
  likes: number;
  views: number;
  downloads: number;
  bookmarks: number;
  shares: number;
  readingTime: number; // in minutes
  isVerified: boolean;
  isFeatured: boolean;
  challenge: {
    description: string;
    painPoints: string[];
    businessImpact: string;
  };
  solution: {
    approach: string;
    methodology: string[];
    tools: string[];
    timeline: string;
    budget?: string;
  };
  results: {
    outcomes: string[];
    metrics: {
      name: string;
      before: string | number;
      after: string | number;
      improvement: string;
      unit?: string;
    }[];
    roi?: number;
    testimonials?: {
      quote: string;
      author: string;
      title: string;
    }[];
  };
  lessonsLearned: string[];
  recommendations: string[];
  competencies: string[];
  relatedCaseStudies?: string[];
  attachments?: {
    name: string;
    type: string;
    size: number;
    url: string;
  }[];
}

export interface CaseStudyCardProps {
  caseStudy: CaseStudy;
  variant?: 'default' | 'compact' | 'detailed' | 'featured';
  onRead?: (caseStudyId: string) => void;
  onDownload?: (caseStudyId: string) => void;
  onLike?: (caseStudyId: string) => void;
  onBookmark?: (caseStudyId: string) => void;
  onShare?: (caseStudyId: string) => void;
  onRate?: (caseStudyId: string, rating: number) => void;
}

const getOrganizationTypeColor = (type: CaseStudy['organizationType']) => {
  const colors = {
    startup: 'bg-purple-100 text-purple-800',
    sme: 'bg-blue-100 text-blue-800',
    enterprise: 'bg-indigo-100 text-indigo-800',
    nonprofit: 'bg-green-100 text-green-800',
    government: 'bg-gray-100 text-gray-800',
    education: 'bg-orange-100 text-orange-800'
  };
  return colors[type] || 'bg-gray-100 text-gray-800';
};

const formatNumber = (num: number) => {
  if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
  if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
  return num.toString();
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

const formatReadingTime = (minutes: number) => {
  if (minutes < 60) return `${minutes} min read`;
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m read` : `${hours}h read`;
};

const calculateImprovement = (before: string | number, after: string | number) => {
  const beforeNum = typeof before === 'string' ? parseFloat(before.replace(/[^0-9.-]/g, '')) : before;
  const afterNum = typeof after === 'string' ? parseFloat(after.replace(/[^0-9.-]/g, '')) : after;
  
  if (isNaN(beforeNum) || isNaN(afterNum) || beforeNum === 0) return 0;
  
  return ((afterNum - beforeNum) / beforeNum) * 100;
};

export const CaseStudyCard: React.FC<CaseStudyCardProps> = ({
  caseStudy,
  variant = 'default',
  onRead,
  onDownload,
  onLike,
  onBookmark,
  onShare,
  onRate
}) => {
  if (variant === 'compact') {
    return (
      <Card className="hover:shadow-md transition-shadow">
        <CardContent className="p-4">
          <div className="flex items-start justify-between mb-2">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-1">
                <h3 className="font-semibold text-sm line-clamp-1">{caseStudy.title}</h3>
                {caseStudy.isVerified && (
                  <CheckCircle className="w-3 h-3 text-green-500 flex-shrink-0" />
                )}
                {caseStudy.isFeatured && (
                  <Award className="w-3 h-3 text-yellow-500 flex-shrink-0" />
                )}
              </div>
              <p className="text-xs text-muted-foreground line-clamp-2 mb-2">
                {caseStudy.description}
              </p>
            </div>
            <Badge className={getOrganizationTypeColor(caseStudy.organizationType)}>
              {caseStudy.organizationType}
            </Badge>
          </div>
          
          <div className="flex items-center gap-2 mb-2">
            <Building className="w-3 h-3 text-muted-foreground" />
            <span className="text-xs text-muted-foreground">{caseStudy.organization.name}</span>
            <span className="text-xs text-muted-foreground">•</span>
            <span className="text-xs text-muted-foreground">{caseStudy.industry}</span>
          </div>
          
          <div className="flex items-center justify-between text-xs text-muted-foreground mb-3">
            <div className="flex items-center gap-3">
              <span className="flex items-center gap-1">
                <Clock className="w-3 h-3" />
                {formatReadingTime(caseStudy.readingTime)}
              </span>
              <span className="flex items-center gap-1">
                <Eye className="w-3 h-3" />
                {formatNumber(caseStudy.views)}
              </span>
              <span className="flex items-center gap-1">
                <Star className="w-3 h-3 fill-current" />
                {caseStudy.rating.toFixed(1)}
              </span>
            </div>
          </div>
          
          <div className="flex gap-2">
            <Button size="sm" onClick={() => onRead?.(caseStudy.id)} className="flex-1">
              <BookOpen className="w-3 h-3 mr-1" />
              Read
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => onBookmark?.(caseStudy.id)}
            >
              <Bookmark className="w-3 h-3" />
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (variant === 'featured') {
    return (
      <Card className="hover:shadow-lg transition-shadow border-2 border-blue-200 bg-gradient-to-br from-blue-50 to-indigo-50">
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <Award className="w-5 h-5 text-yellow-500" />
                <Badge className="bg-blue-100 text-blue-800">Featured Case Study</Badge>
                {caseStudy.isVerified && (
                  <Badge className="bg-green-100 text-green-800">
                    <CheckCircle className="w-3 h-3 mr-1" />
                    Verified
                  </Badge>
                )}
              </div>
              <CardTitle className="text-xl mb-1">{caseStudy.title}</CardTitle>
              {caseStudy.subtitle && (
                <p className="text-lg text-muted-foreground mb-2">{caseStudy.subtitle}</p>
              )}
              <p className="text-muted-foreground mb-4">{caseStudy.description}</p>
              
              <div className="flex items-center gap-4 mb-4">
                <div className="flex items-center gap-2">
                  <Building className="w-4 h-4 text-muted-foreground" />
                  <span className="font-medium">{caseStudy.organization.name}</span>
                </div>
                <Badge className={getOrganizationTypeColor(caseStudy.organizationType)}>
                  {caseStudy.organizationType}
                </Badge>
                <Badge variant="outline">{caseStudy.industry}</Badge>
              </div>
            </div>
            
            <div className="text-right">
              <div className="flex items-center gap-1 mb-1">
                <Star className="w-4 h-4 fill-current text-yellow-400" />
                <span className="font-semibold">{caseStudy.rating.toFixed(1)}</span>
                <span className="text-sm text-muted-foreground">({caseStudy.totalRatings})</span>
              </div>
              <div className="text-sm text-muted-foreground">
                {formatNumber(caseStudy.views)} views
              </div>
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="bg-white/50 p-3 rounded-lg">
              <h4 className="font-semibold text-sm mb-2 text-red-700">Challenge</h4>
              <p className="text-xs text-muted-foreground line-clamp-3">
                {caseStudy.challenge.description}
              </p>
            </div>
            <div className="bg-white/50 p-3 rounded-lg">
              <h4 className="font-semibold text-sm mb-2 text-blue-700">Solution</h4>
              <p className="text-xs text-muted-foreground line-clamp-3">
                {caseStudy.solution.approach}
              </p>
            </div>
            <div className="bg-white/50 p-3 rounded-lg">
              <h4 className="font-semibold text-sm mb-2 text-green-700">Results</h4>
              <div className="space-y-1">
                {caseStudy.results.metrics.slice(0, 2).map((metric, index) => (
                  <div key={index} className="text-xs">
                    <span className="font-medium">{metric.improvement}</span>
                    <span className="text-muted-foreground"> {metric.name}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
          
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <Avatar className="w-8 h-8">
                <AvatarImage src={caseStudy.author.avatar} />
                <AvatarFallback>
                  {caseStudy.author.name.split(' ').map((n: string) => n[0]).join('')}
                </AvatarFallback>
              </Avatar>
              <div>
                <div className="text-sm font-medium">{caseStudy.author.name}</div>
                <div className="text-xs text-muted-foreground">
                  {caseStudy.author.title} • {caseStudy.author.organization}
                </div>
              </div>
            </div>
            
            <div className="text-right text-xs text-muted-foreground">
              <div>Published {formatDate(caseStudy.publishedAt)}</div>
            </div>
          </div>
          
          <div className="flex gap-3">
            <Button onClick={() => onRead?.(caseStudy.id)} className="flex-1">
              <BookOpen className="w-4 h-4 mr-2" />
              Read Case Study
            </Button>
            <Button variant="outline" onClick={() => onDownload?.(caseStudy.id)}>
              <Download className="w-4 h-4" />
            </Button>
            <Button variant="outline" onClick={() => onBookmark?.(caseStudy.id)}>
              <Bookmark className="w-4 h-4" />
            </Button>
            <Button variant="outline" onClick={() => onShare?.(caseStudy.id)}>
              <Share2 className="w-4 h-4" />
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (variant === 'detailed') {
    return (
      <Card className="hover:shadow-lg transition-shadow">
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                {caseStudy.isFeatured && (
                  <Badge className="bg-yellow-100 text-yellow-800">
                    <Award className="w-3 h-3 mr-1" />
                    Featured
                  </Badge>
                )}
                {caseStudy.isVerified && (
                  <Badge className="bg-green-100 text-green-800">
                    <CheckCircle className="w-3 h-3 mr-1" />
                    Verified
                  </Badge>
                )}
              </div>
              <CardTitle className="text-xl mb-1">{caseStudy.title}</CardTitle>
              {caseStudy.subtitle && (
                <p className="text-lg text-muted-foreground mb-2">{caseStudy.subtitle}</p>
              )}
              <p className="text-muted-foreground mb-4">{caseStudy.description}</p>
              
              <div className="flex items-center gap-4 mb-4">
                <div className="flex items-center gap-2">
                  <Building className="w-4 h-4 text-muted-foreground" />
                  <span className="font-medium">{caseStudy.organization.name}</span>
                </div>
                <Badge className={getOrganizationTypeColor(caseStudy.organizationType)}>
                  {caseStudy.organizationType}
                </Badge>
                <Badge variant="outline">{caseStudy.industry}</Badge>
                <div className="flex items-center gap-1 text-sm text-muted-foreground">
                  <MapPin className="w-3 h-3" />
                  <span>{caseStudy.location}</span>
                </div>
              </div>
            </div>
            
            <div className="text-right">
              <div className="flex items-center gap-1 mb-1">
                <Star className="w-4 h-4 fill-current text-yellow-400" />
                <span className="font-semibold">{caseStudy.rating.toFixed(1)}</span>
                <span className="text-sm text-muted-foreground">({caseStudy.totalRatings})</span>
              </div>
              <div className="text-sm text-muted-foreground">
                {formatNumber(caseStudy.views)} views
              </div>
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div className="space-y-4">
              <div className="border-l-4 border-red-500 pl-4">
                <h4 className="font-semibold mb-2 flex items-center gap-2 text-red-700">
                  <Target className="w-4 h-4" />
                  Challenge
                </h4>
                <p className="text-sm text-muted-foreground mb-3">
                  {caseStudy.challenge.description}
                </p>
                <div className="space-y-1">
                  <h5 className="text-xs font-medium">Pain Points:</h5>
                  <ul className="text-xs space-y-1">
                    {caseStudy.challenge.painPoints.map((point, index) => (
                      <li key={index} className="flex items-start gap-1">
                        <span className="text-red-500 mt-1">•</span>
                        <span>{point}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
            
            <div className="space-y-4">
              <div className="border-l-4 border-blue-500 pl-4">
                <h4 className="font-semibold mb-2 flex items-center gap-2 text-blue-700">
                  <Lightbulb className="w-4 h-4" />
                  Solution
                </h4>
                <p className="text-sm text-muted-foreground mb-3">
                  {caseStudy.solution.approach}
                </p>
                <div className="space-y-2">
                  <div>
                    <h5 className="text-xs font-medium mb-1">Methodology:</h5>
                    <div className="flex flex-wrap gap-1">
                      {caseStudy.solution.methodology.map((method, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {method}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  <div>
                    <h5 className="text-xs font-medium mb-1">Tools:</h5>
                    <div className="flex flex-wrap gap-1">
                      {caseStudy.solution.tools.map((tool, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {tool}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  <div className="text-xs">
                    <span className="font-medium">Timeline:</span> {caseStudy.solution.timeline}
                  </div>
                </div>
              </div>
            </div>
            
            <div className="space-y-4">
              <div className="border-l-4 border-green-500 pl-4">
                <h4 className="font-semibold mb-2 flex items-center gap-2 text-green-700">
                  <TrendingUp className="w-4 h-4" />
                  Results
                </h4>
                <div className="space-y-3">
                  {caseStudy.results.metrics.map((metric, index) => (
                    <div key={index} className="bg-green-50 p-2 rounded">
                      <div className="flex justify-between items-center mb-1">
                        <span className="text-xs font-medium">{metric.name}</span>
                        <span className="text-xs font-bold text-green-600">
                          {metric.improvement}
                        </span>
                      </div>
                      <div className="flex justify-between text-xs text-muted-foreground">
                        <span>Before: {metric.before}{metric.unit}</span>
                        <span>After: {metric.after}{metric.unit}</span>
                      </div>
                      <Progress 
                        value={Math.abs(calculateImprovement(metric.before, metric.after))} 
                        className="h-1 mt-1" 
                      />
                    </div>
                  ))}
                  {caseStudy.results.roi && (
                    <div className="bg-green-100 p-2 rounded">
                      <div className="flex items-center gap-1">
                        <DollarSign className="w-3 h-3 text-green-600" />
                        <span className="text-xs font-medium">ROI: {caseStudy.results.roi}%</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
          
          {caseStudy.results.testimonials && caseStudy.results.testimonials.length > 0 && (
            <div className="mb-6">
              <h4 className="font-semibold mb-3">Testimonials</h4>
              <div className="space-y-3">
                {caseStudy.results.testimonials.map((testimonial, index) => (
                  <div key={index} className="bg-muted p-3 rounded-lg">
                    <p className="text-sm italic mb-2">"{testimonial.quote}"</p>
                    <div className="text-xs text-muted-foreground">
                      — {testimonial.author}, {testimonial.title}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <h4 className="font-semibold mb-2 flex items-center gap-2">
                <Lightbulb className="w-4 h-4" />
                Lessons Learned
              </h4>
              <ul className="text-sm space-y-1">
                {caseStudy.lessonsLearned.map((lesson, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <CheckCircle className="w-3 h-3 text-green-500 mt-0.5 flex-shrink-0" />
                    <span>{lesson}</span>
                  </li>
                ))}
              </ul>
            </div>
            
            <div>
              <h4 className="font-semibold mb-2 flex items-center gap-2">
                <Target className="w-4 h-4" />
                Recommendations
              </h4>
              <ul className="text-sm space-y-1">
                {caseStudy.recommendations.map((recommendation, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <TrendingUp className="w-3 h-3 text-blue-500 mt-0.5 flex-shrink-0" />
                    <span>{recommendation}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
          
          {caseStudy.competencies.length > 0 && (
            <div className="mb-6">
              <h4 className="font-semibold mb-2">Related Competencies</h4>
              <div className="flex flex-wrap gap-1">
                {caseStudy.competencies.map((competency, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {competency}
                  </Badge>
                ))}
              </div>
            </div>
          )}
          
          <div className="mb-6">
            <h4 className="font-semibold mb-2">Tags</h4>
            <div className="flex flex-wrap gap-1">
              {caseStudy.tags.map((tag, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  #{tag}
                </Badge>
              ))}
            </div>
          </div>
          
          {caseStudy.attachments && caseStudy.attachments.length > 0 && (
            <div className="mb-6">
              <h4 className="font-semibold mb-2 flex items-center gap-2">
                <FileText className="w-4 h-4" />
                Attachments
              </h4>
              <div className="space-y-2">
                {caseStudy.attachments.map((attachment, index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-muted rounded">
                    <div className="flex items-center gap-2">
                      <FileText className="w-4 h-4" />
                      <span className="text-sm">{attachment.name}</span>
                      <Badge variant="outline" className="text-xs">
                        {attachment.type}
                      </Badge>
                    </div>
                    <Button size="sm" variant="ghost">
                      <Download className="w-3 h-3" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}
          
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <Avatar className="w-8 h-8">
                <AvatarImage src={caseStudy.author.avatar} />
                <AvatarFallback>
                  {caseStudy.author.name.split(' ').map((n: string) => n[0]).join('')}
                </AvatarFallback>
              </Avatar>
              <div>
                <div className="text-sm font-medium">{caseStudy.author.name}</div>
                <div className="text-xs text-muted-foreground">
                  {caseStudy.author.title}
                  {caseStudy.author.organization && ` • ${caseStudy.author.organization}`}
                </div>
              </div>
            </div>
            
            <div className="text-right text-xs text-muted-foreground">
              <div>Published {formatDate(caseStudy.publishedAt)}</div>
              <div>Updated {formatDate(caseStudy.updatedAt)}</div>
            </div>
          </div>
          
          <div className="flex gap-3">
            <Button onClick={() => onRead?.(caseStudy.id)} className="flex-1">
              <BookOpen className="w-4 h-4 mr-2" />
              Read Full Case Study
            </Button>
            <Button variant="outline" onClick={() => onDownload?.(caseStudy.id)}>
              <Download className="w-4 h-4 mr-2" />
              Download
            </Button>
            <Button variant="outline" onClick={() => onLike?.(caseStudy.id)}>
              <ThumbsUp className="w-4 h-4 mr-2" />
              Like
            </Button>
            <Button variant="outline" onClick={() => onBookmark?.(caseStudy.id)}>
              <Bookmark className="w-4 h-4 mr-2" />
              Save
            </Button>
            <Button variant="outline" onClick={() => onShare?.(caseStudy.id)}>
              <Share2 className="w-4 h-4" />
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Default variant
  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              {caseStudy.isFeatured && (
                <Award className="w-4 h-4 text-yellow-500" />
              )}
              {caseStudy.isVerified && (
                <CheckCircle className="w-4 h-4 text-green-500" />
              )}
            </div>
            <CardTitle className="text-lg mb-1">{caseStudy.title}</CardTitle>
            {caseStudy.subtitle && (
              <p className="text-sm text-muted-foreground mb-2">{caseStudy.subtitle}</p>
            )}
            <p className="text-muted-foreground text-sm mb-3 line-clamp-2">
              {caseStudy.description}
            </p>
            
            <div className="flex items-center gap-2 mb-3">
              <Building className="w-3 h-3 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">{caseStudy.organization.name}</span>
              <Badge className={getOrganizationTypeColor(caseStudy.organizationType)}>
                {caseStudy.organizationType}
              </Badge>
              <Badge variant="outline">{caseStudy.industry}</Badge>
            </div>
          </div>
          
          <div className="text-right">
            <div className="flex items-center gap-1 mb-1">
              <Star className="w-4 h-4 fill-current text-yellow-400" />
              <span className="font-semibold">{caseStudy.rating.toFixed(1)}</span>
            </div>
            <div className="text-xs text-muted-foreground">
              {caseStudy.totalRatings} ratings
            </div>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="flex items-center justify-between text-sm text-muted-foreground mb-4">
          <div className="flex items-center gap-4">
            <span className="flex items-center gap-1">
              <Clock className="w-4 h-4" />
              {formatReadingTime(caseStudy.readingTime)}
            </span>
            <span className="flex items-center gap-1">
              <Eye className="w-4 h-4" />
              {formatNumber(caseStudy.views)}
            </span>
            <span className="flex items-center gap-1">
              <ThumbsUp className="w-4 h-4" />
              {formatNumber(caseStudy.likes)}
            </span>
          </div>
        </div>
        
        <div className="grid grid-cols-3 gap-2 mb-4">
          <div className="text-center p-2 bg-red-50 rounded">
            <div className="text-xs font-medium text-red-700">Challenge</div>
            <div className="text-xs text-muted-foreground line-clamp-2">
              {caseStudy.challenge.description}
            </div>
          </div>
          <div className="text-center p-2 bg-blue-50 rounded">
            <div className="text-xs font-medium text-blue-700">Solution</div>
            <div className="text-xs text-muted-foreground line-clamp-2">
              {caseStudy.solution.approach}
            </div>
          </div>
          <div className="text-center p-2 bg-green-50 rounded">
            <div className="text-xs font-medium text-green-700">Results</div>
            <div className="text-xs text-muted-foreground">
              {caseStudy.results.metrics.length} metrics
              {caseStudy.results.roi && (
                <div className="font-medium text-green-600">
                  {caseStudy.results.roi}% ROI
                </div>
              )}
            </div>
          </div>
        </div>
        
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <Avatar className="w-6 h-6">
              <AvatarImage src={caseStudy.author.avatar} />
              <AvatarFallback className="text-xs">
                {caseStudy.author.name.split(' ').map((n: string) => n[0]).join('')}
              </AvatarFallback>
            </Avatar>
            <span className="text-sm text-muted-foreground">{caseStudy.author.name}</span>
          </div>
          
          <div className="text-xs text-muted-foreground">
            {formatDate(caseStudy.publishedAt)}
          </div>
        </div>
        
        <div className="flex gap-2">
          <Button onClick={() => onRead?.(caseStudy.id)} className="flex-1">
            <BookOpen className="w-4 h-4 mr-2" />
            Read
          </Button>
          <Button variant="outline" onClick={() => onBookmark?.(caseStudy.id)}>
            <Bookmark className="w-4 h-4" />
          </Button>
          <Button variant="outline" onClick={() => onShare?.(caseStudy.id)}>
            <Share2 className="w-4 h-4" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default CaseStudyCard;