import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  BookOpen,
  Download,
  Star,
  ThumbsUp,
  Share2,
  Bookmark,
  Eye,
  Clock,
  Users,
  Award,
  TrendingUp,
  CheckCircle,
  Lightbulb,
  Target,
  FileText
} from 'lucide-react';

export interface BestPractice {
  id: string;
  title: string;
  description: string;
  content: string;
  category: string;
  subcategory?: string;
  type: 'guideline' | 'framework' | 'methodology' | 'template' | 'checklist' | 'case-study';
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  tags: string[];
  author: {
    name: string;
    title?: string;
    avatar?: string;
    organization?: string;
  };
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  rating: number;
  totalRatings: number;
  likes: number;
  views: number;
  downloads: number;
  bookmarks: number;
  shares: number;
  readingTime: number; // in minutes
  isVerified: boolean;
  isFeatured: boolean;
  competencies: string[];
  learningObjectives: string[];
  targetAudience: string[];
  prerequisites?: string[];
  relatedPractices?: string[];
  attachments?: {
    name: string;
    type: string;
    size: number;
    url: string;
  }[];
  metrics?: {
    implementationRate: number;
    successRate: number;
    roi?: number;
  };
}

export interface BestPracticeCardProps {
  practice: BestPractice;
  variant?: 'default' | 'compact' | 'detailed' | 'featured';
  onRead?: (practiceId: string) => void;
  onDownload?: (practiceId: string) => void;
  onLike?: (practiceId: string) => void;
  onBookmark?: (practiceId: string) => void;
  onShare?: (practiceId: string) => void;
  onRate?: (practiceId: string, rating: number) => void;
}

const getTypeColor = (type: BestPractice['type']) => {
  const colors = {
    guideline: 'bg-blue-100 text-blue-800',
    framework: 'bg-purple-100 text-purple-800',
    methodology: 'bg-green-100 text-green-800',
    template: 'bg-orange-100 text-orange-800',
    checklist: 'bg-pink-100 text-pink-800',
    'case-study': 'bg-indigo-100 text-indigo-800'
  };
  return colors[type] || 'bg-gray-100 text-gray-800';
};

const getDifficultyColor = (difficulty: BestPractice['difficulty']) => {
  const colors = {
    beginner: 'bg-green-100 text-green-800',
    intermediate: 'bg-yellow-100 text-yellow-800',
    advanced: 'bg-red-100 text-red-800'
  };
  return colors[difficulty];
};

const formatNumber = (num: number) => {
  if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
  if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
  return num.toString();
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

const formatReadingTime = (minutes: number) => {
  if (minutes < 60) return `${minutes} min read`;
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m read` : `${hours}h read`;
};

export const BestPracticeCard: React.FC<BestPracticeCardProps> = ({
  practice,
  variant = 'default',
  onRead,
  onDownload,
  onLike,
  onBookmark,
  onShare,
  onRate
}) => {
  if (variant === 'compact') {
    return (
      <Card className="hover:shadow-md transition-shadow">
        <CardContent className="p-4">
          <div className="flex items-start justify-between mb-2">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-1">
                <h3 className="font-semibold text-sm line-clamp-1">{practice.title}</h3>
                {practice.isVerified && (
                  <CheckCircle className="w-3 h-3 text-green-500 flex-shrink-0" />
                )}
                {practice.isFeatured && (
                  <Award className="w-3 h-3 text-yellow-500 flex-shrink-0" />
                )}
              </div>
              <p className="text-xs text-muted-foreground line-clamp-2 mb-2">
                {practice.description}
              </p>
            </div>
            <Badge className={getTypeColor(practice.type)}>
              {practice.type}
            </Badge>
          </div>
          
          <div className="flex items-center justify-between text-xs text-muted-foreground mb-3">
            <div className="flex items-center gap-3">
              <span className="flex items-center gap-1">
                <Clock className="w-3 h-3" />
                {formatReadingTime(practice.readingTime)}
              </span>
              <span className="flex items-center gap-1">
                <Eye className="w-3 h-3" />
                {formatNumber(practice.views)}
              </span>
              <span className="flex items-center gap-1">
                <Star className="w-3 h-3 fill-current" />
                {practice.rating.toFixed(1)}
              </span>
            </div>
          </div>
          
          <div className="flex gap-2">
            <Button size="sm" onClick={() => onRead?.(practice.id)} className="flex-1">
              <BookOpen className="w-3 h-3 mr-1" />
              Read
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => onBookmark?.(practice.id)}
            >
              <Bookmark className="w-3 h-3" />
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (variant === 'featured') {
    return (
      <Card className="hover:shadow-lg transition-shadow border-2 border-yellow-200 bg-gradient-to-br from-yellow-50 to-orange-50">
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <Award className="w-5 h-5 text-yellow-500" />
                <Badge className="bg-yellow-100 text-yellow-800">Featured</Badge>
                {practice.isVerified && (
                  <Badge className="bg-green-100 text-green-800">
                    <CheckCircle className="w-3 h-3 mr-1" />
                    Verified
                  </Badge>
                )}
              </div>
              <CardTitle className="text-xl mb-2">{practice.title}</CardTitle>
              <p className="text-muted-foreground mb-4">{practice.description}</p>
              
              <div className="flex flex-wrap gap-2 mb-4">
                <Badge className={getTypeColor(practice.type)}>
                  {practice.type}
                </Badge>
                <Badge className={getDifficultyColor(practice.difficulty)}>
                  {practice.difficulty}
                </Badge>
                <Badge variant="outline">{practice.category}</Badge>
              </div>
            </div>
            
            <div className="text-right">
              <div className="flex items-center gap-1 mb-1">
                <Star className="w-4 h-4 fill-current text-yellow-400" />
                <span className="font-semibold">{practice.rating.toFixed(1)}</span>
                <span className="text-sm text-muted-foreground">({practice.totalRatings})</span>
              </div>
              <div className="text-sm text-muted-foreground">
                {formatNumber(practice.views)} views
              </div>
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          <div className="flex items-center justify-between text-sm text-muted-foreground mb-4">
            <div className="flex items-center gap-4">
              <span className="flex items-center gap-1">
                <Clock className="w-4 h-4" />
                {formatReadingTime(practice.readingTime)}
              </span>
              <span className="flex items-center gap-1">
                <ThumbsUp className="w-4 h-4" />
                {formatNumber(practice.likes)}
              </span>
              <span className="flex items-center gap-1">
                <Download className="w-4 h-4" />
                {formatNumber(practice.downloads)}
              </span>
            </div>
          </div>
          
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <Avatar className="w-8 h-8">
                <AvatarImage src={practice.author.avatar} />
                <AvatarFallback>
                  {practice.author.name.split(' ').map((n: string) => n[0]).join('')}
                </AvatarFallback>
              </Avatar>
              <div>
                <div className="text-sm font-medium">{practice.author.name}</div>
                <div className="text-xs text-muted-foreground">
                  {practice.author.title} • {practice.author.organization}
                </div>
              </div>
            </div>
            
            <div className="text-right text-xs text-muted-foreground">
              <div>Published {formatDate(practice.publishedAt)}</div>
            </div>
          </div>
          
          <div className="flex gap-3">
            <Button onClick={() => onRead?.(practice.id)} className="flex-1">
              <BookOpen className="w-4 h-4 mr-2" />
              Read Now
            </Button>
            <Button variant="outline" onClick={() => onDownload?.(practice.id)}>
              <Download className="w-4 h-4" />
            </Button>
            <Button variant="outline" onClick={() => onBookmark?.(practice.id)}>
              <Bookmark className="w-4 h-4" />
            </Button>
            <Button variant="outline" onClick={() => onShare?.(practice.id)}>
              <Share2 className="w-4 h-4" />
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (variant === 'detailed') {
    return (
      <Card className="hover:shadow-lg transition-shadow">
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                {practice.isFeatured && (
                  <Badge className="bg-yellow-100 text-yellow-800">
                    <Award className="w-3 h-3 mr-1" />
                    Featured
                  </Badge>
                )}
                {practice.isVerified && (
                  <Badge className="bg-green-100 text-green-800">
                    <CheckCircle className="w-3 h-3 mr-1" />
                    Verified
                  </Badge>
                )}
              </div>
              <CardTitle className="text-xl mb-2">{practice.title}</CardTitle>
              <p className="text-muted-foreground mb-4">{practice.description}</p>
              
              <div className="flex flex-wrap gap-2 mb-4">
                <Badge className={getTypeColor(practice.type)}>
                  {practice.type}
                </Badge>
                <Badge className={getDifficultyColor(practice.difficulty)}>
                  {practice.difficulty}
                </Badge>
                <Badge variant="outline">{practice.category}</Badge>
                {practice.subcategory && (
                  <Badge variant="secondary">{practice.subcategory}</Badge>
                )}
              </div>
            </div>
            
            <div className="text-right">
              <div className="flex items-center gap-1 mb-1">
                <Star className="w-4 h-4 fill-current text-yellow-400" />
                <span className="font-semibold">{practice.rating.toFixed(1)}</span>
                <span className="text-sm text-muted-foreground">({practice.totalRatings})</span>
              </div>
              <div className="text-sm text-muted-foreground">
                {formatNumber(practice.views)} views
              </div>
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2 flex items-center gap-2">
                  <Target className="w-4 h-4" />
                  Learning Objectives
                </h4>
                <ul className="text-sm space-y-1">
                  {practice.learningObjectives.map((objective, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <CheckCircle className="w-3 h-3 text-green-500 mt-0.5 flex-shrink-0" />
                      <span>{objective}</span>
                    </li>
                  ))}
                </ul>
              </div>
              
              <div>
                <h4 className="font-semibold mb-2 flex items-center gap-2">
                  <Users className="w-4 h-4" />
                  Target Audience
                </h4>
                <div className="flex flex-wrap gap-1">
                  {practice.targetAudience.map((audience, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {audience}
                    </Badge>
                  ))}
                </div>
              </div>
              
              {practice.competencies.length > 0 && (
                <div>
                  <h4 className="font-semibold mb-2 flex items-center gap-2">
                    <Lightbulb className="w-4 h-4" />
                    Competencies
                  </h4>
                  <div className="flex flex-wrap gap-1">
                    {practice.competencies.map((competency, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {competency}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
            
            <div className="space-y-4">
              <div>
                <h4 className="font-semibold mb-2 flex items-center gap-2">
                  <TrendingUp className="w-4 h-4" />
                  Engagement Metrics
                </h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Views:</span>
                    <span>{formatNumber(practice.views)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Likes:</span>
                    <span>{formatNumber(practice.likes)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Downloads:</span>
                    <span>{formatNumber(practice.downloads)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Bookmarks:</span>
                    <span>{formatNumber(practice.bookmarks)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Reading Time:</span>
                    <span>{formatReadingTime(practice.readingTime)}</span>
                  </div>
                </div>
              </div>
              
              {practice.metrics && (
                <div>
                  <h4 className="font-semibold mb-2 flex items-center gap-2">
                    <Award className="w-4 h-4" />
                    Success Metrics
                  </h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Implementation Rate:</span>
                      <span>{practice.metrics.implementationRate}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Success Rate:</span>
                      <span>{practice.metrics.successRate}%</span>
                    </div>
                    {practice.metrics.roi && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">ROI:</span>
                        <span>{practice.metrics.roi}%</span>
                      </div>
                    )}
                  </div>
                </div>
              )}
              
              <div>
                <h4 className="font-semibold mb-2">Tags</h4>
                <div className="flex flex-wrap gap-1">
                  {practice.tags.map((tag, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      #{tag}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          </div>
          
          {practice.attachments && practice.attachments.length > 0 && (
            <div className="mb-6">
              <h4 className="font-semibold mb-2 flex items-center gap-2">
                <FileText className="w-4 h-4" />
                Attachments
              </h4>
              <div className="space-y-2">
                {practice.attachments.map((attachment, index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-muted rounded">
                    <div className="flex items-center gap-2">
                      <FileText className="w-4 h-4" />
                      <span className="text-sm">{attachment.name}</span>
                      <Badge variant="outline" className="text-xs">
                        {attachment.type}
                      </Badge>
                    </div>
                    <Button size="sm" variant="ghost">
                      <Download className="w-3 h-3" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}
          
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <Avatar className="w-8 h-8">
                <AvatarImage src={practice.author.avatar} />
                <AvatarFallback>
                  {practice.author.name.split(' ').map((n: string) => n[0]).join('')}
                </AvatarFallback>
              </Avatar>
              <div>
                <div className="text-sm font-medium">{practice.author.name}</div>
                <div className="text-xs text-muted-foreground">
                  {practice.author.title}
                  {practice.author.organization && ` • ${practice.author.organization}`}
                </div>
              </div>
            </div>
            
            <div className="text-right text-xs text-muted-foreground">
              <div>Published {formatDate(practice.publishedAt)}</div>
              <div>Updated {formatDate(practice.updatedAt)}</div>
            </div>
          </div>
          
          <div className="flex gap-3">
            <Button onClick={() => onRead?.(practice.id)} className="flex-1">
              <BookOpen className="w-4 h-4 mr-2" />
              Read Practice
            </Button>
            <Button variant="outline" onClick={() => onDownload?.(practice.id)}>
              <Download className="w-4 h-4 mr-2" />
              Download
            </Button>
            <Button variant="outline" onClick={() => onLike?.(practice.id)}>
              <ThumbsUp className="w-4 h-4 mr-2" />
              Like
            </Button>
            <Button variant="outline" onClick={() => onBookmark?.(practice.id)}>
              <Bookmark className="w-4 h-4 mr-2" />
              Save
            </Button>
            <Button variant="outline" onClick={() => onShare?.(practice.id)}>
              <Share2 className="w-4 h-4" />
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Default variant
  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              {practice.isFeatured && (
                <Award className="w-4 h-4 text-yellow-500" />
              )}
              {practice.isVerified && (
                <CheckCircle className="w-4 h-4 text-green-500" />
              )}
            </div>
            <CardTitle className="text-lg mb-2">{practice.title}</CardTitle>
            <p className="text-muted-foreground text-sm mb-3 line-clamp-2">
              {practice.description}
            </p>
            
            <div className="flex flex-wrap gap-2 mb-3">
              <Badge className={getTypeColor(practice.type)}>
                {practice.type}
              </Badge>
              <Badge className={getDifficultyColor(practice.difficulty)}>
                {practice.difficulty}
              </Badge>
              <Badge variant="outline">{practice.category}</Badge>
            </div>
          </div>
          
          <div className="text-right">
            <div className="flex items-center gap-1 mb-1">
              <Star className="w-4 h-4 fill-current text-yellow-400" />
              <span className="font-semibold">{practice.rating.toFixed(1)}</span>
            </div>
            <div className="text-xs text-muted-foreground">
              {practice.totalRatings} ratings
            </div>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="flex items-center justify-between text-sm text-muted-foreground mb-4">
          <div className="flex items-center gap-4">
            <span className="flex items-center gap-1">
              <Clock className="w-4 h-4" />
              {formatReadingTime(practice.readingTime)}
            </span>
            <span className="flex items-center gap-1">
              <Eye className="w-4 h-4" />
              {formatNumber(practice.views)}
            </span>
            <span className="flex items-center gap-1">
              <ThumbsUp className="w-4 h-4" />
              {formatNumber(practice.likes)}
            </span>
          </div>
        </div>
        
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <Avatar className="w-6 h-6">
              <AvatarImage src={practice.author.avatar} />
              <AvatarFallback className="text-xs">
                {practice.author.name.split(' ').map((n: string) => n[0]).join('')}
              </AvatarFallback>
            </Avatar>
            <span className="text-sm text-muted-foreground">{practice.author.name}</span>
          </div>
          
          <div className="text-xs text-muted-foreground">
            {formatDate(practice.publishedAt)}
          </div>
        </div>
        
        <div className="flex gap-2">
          <Button onClick={() => onRead?.(practice.id)} className="flex-1">
            <BookOpen className="w-4 h-4 mr-2" />
            Read
          </Button>
          <Button variant="outline" onClick={() => onBookmark?.(practice.id)}>
            <Bookmark className="w-4 h-4" />
          </Button>
          <Button variant="outline" onClick={() => onShare?.(practice.id)}>
            <Share2 className="w-4 h-4" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default BestPracticeCard;