import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Progress } from '@/components/ui/progress';
import { 
  Download, 
  Eye, 
  Heart, 
  Share2, 
  FileText, 
  Video, 
  Image, 
  File, 
  Link, 
  Star,
  Clock,
  User,
  Calendar,
  MoreHorizontal,
  Bookmark,
  Flag
} from 'lucide-react';

interface ResourceAuthor {
  id: string;
  name: string;
  avatar?: string;
  role?: string;
}

interface ResourceRating {
  average: number;
  count: number;
  userRating?: number;
}

interface Resource {
  id: string;
  title: string;
  description: string;
  type: 'document' | 'video' | 'image' | 'link' | 'presentation' | 'spreadsheet' | 'other';
  category: string;
  tags: string[];
  author: ResourceAuthor;
  uploadDate: string;
  lastModified?: string;
  fileSize?: string;
  downloadCount: number;
  viewCount: number;
  likeCount: number;
  rating?: ResourceRating;
  url?: string;
  thumbnailUrl?: string;
  isBookmarked?: boolean;
  isLiked?: boolean;
  accessLevel: 'public' | 'restricted' | 'private';
  department?: string;
  version?: string;
}

interface ResourceCardProps {
  resource: Resource;
  onDownload?: (resourceId: string) => void;
  onView?: (resource: Resource) => void;
  onLike?: (resourceId: string) => void;
  onBookmark?: (resourceId: string) => void;
  onShare?: (resource: Resource) => void;
  onRate?: (resourceId: string, rating: number) => void;
  onFlag?: (resourceId: string) => void;
  className?: string;
  showActions?: boolean;
  compact?: boolean;
}

export const ResourceCard: React.FC<ResourceCardProps> = ({
  resource,
  onDownload,
  onView,
  onLike,
  onBookmark,
  onShare,
  onRate,
  onFlag,
  className,
  showActions = true,
  compact = false
}) => {
  const [showRating, setShowRating] = useState(false);
  const [hoveredRating, setHoveredRating] = useState(0);

  const getTypeIcon = (type: string) => {
    const iconProps = { className: "h-5 w-5" };
    switch (type) {
      case 'document':
      case 'presentation':
      case 'spreadsheet':
        return <FileText {...iconProps} />;
      case 'video':
        return <Video {...iconProps} />;
      case 'image':
        return <Image {...iconProps} />;
      case 'link':
        return <Link {...iconProps} />;
      default:
        return <File {...iconProps} />;
    }
  };

  const getTypeColor = (type: string) => {
    const colors: Record<string, string> = {
      'document': 'bg-blue-100 text-blue-800',
      'video': 'bg-red-100 text-red-800',
      'image': 'bg-green-100 text-green-800',
      'link': 'bg-purple-100 text-purple-800',
      'presentation': 'bg-orange-100 text-orange-800',
      'spreadsheet': 'bg-teal-100 text-teal-800'
    };
    return colors[type] || 'bg-gray-100 text-gray-800';
  };

  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      'training': 'bg-blue-100 text-blue-800',
      'template': 'bg-green-100 text-green-800',
      'guide': 'bg-yellow-100 text-yellow-800',
      'policy': 'bg-red-100 text-red-800',
      'reference': 'bg-purple-100 text-purple-800',
      'tool': 'bg-indigo-100 text-indigo-800'
    };
    return colors[category.toLowerCase()] || 'bg-gray-100 text-gray-800';
  };

  const formatFileSize = (size: string | undefined) => {
    if (!size) return '';
    return size;
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatCount = (count: number) => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    }
    if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };

  const handleRating = (rating: number) => {
    if (onRate) {
      onRate(resource.id, rating);
      setShowRating(false);
    }
  };

  if (compact) {
    return (
      <Card className={`p-3 hover:shadow-sm transition-shadow ${className || ''}`}>
        <div className="flex items-center gap-3">
          <div className="flex-shrink-0">
            {resource.thumbnailUrl ? (
              <img 
                src={resource.thumbnailUrl} 
                alt={resource.title}
                className="h-12 w-12 rounded object-cover"
              />
            ) : (
              <div className="h-12 w-12 rounded bg-gray-100 flex items-center justify-center">
                {getTypeIcon(resource.type)}
              </div>
            )}
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <h4 className="font-medium text-sm truncate">{resource.title}</h4>
              <Badge className={getTypeColor(resource.type)}>
                {resource.type}
              </Badge>
            </div>
            
            <div className="flex items-center gap-4 text-xs text-muted-foreground">
              <span>{resource.author.name}</span>
              <span>{formatCount(resource.downloadCount)} downloads</span>
              <span>{formatDate(resource.uploadDate)}</span>
              {resource.fileSize && <span>{formatFileSize(resource.fileSize)}</span>}
            </div>
          </div>
          
          {showActions && (
            <div className="flex gap-1">
              <Button variant="ghost" size="sm" onClick={() => onView?.(resource)}>
                <Eye className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm" onClick={() => onDownload?.(resource.id)}>
                <Download className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>
      </Card>
    );
  }

  return (
    <Card className={`p-4 hover:shadow-md transition-shadow ${className || ''}`}>
      {/* Header */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-start gap-3 flex-1">
          <div className="flex-shrink-0">
            {resource.thumbnailUrl ? (
              <img 
                src={resource.thumbnailUrl} 
                alt={resource.title}
                className="h-16 w-16 rounded-lg object-cover"
              />
            ) : (
              <div className="h-16 w-16 rounded-lg bg-gray-100 flex items-center justify-center">
                {getTypeIcon(resource.type)}
              </div>
            )}
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-2">
              <h3 className="font-semibold text-lg truncate">{resource.title}</h3>
              <Badge className={getTypeColor(resource.type)}>
                {resource.type}
              </Badge>
              <Badge className={getCategoryColor(resource.category)}>
                {resource.category}
              </Badge>
              {resource.accessLevel !== 'public' && (
                <Badge variant="outline">
                  {resource.accessLevel}
                </Badge>
              )}
            </div>
            
            <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
              <Avatar className="h-6 w-6">
                <AvatarImage src={resource.author.avatar} alt={resource.author.name} />
                <AvatarFallback className="text-xs">
                  {resource.author.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <span className="font-medium">{resource.author.name}</span>
              {resource.author.role && (
                <Badge variant="secondary" className="text-xs">
                  {resource.author.role}
                </Badge>
              )}
              {resource.department && (
                <span>• {resource.department}</span>
              )}
            </div>
            
            {resource.rating && (
              <div className="flex items-center gap-2 mb-2">
                <div className="flex items-center">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <Star
                      key={star}
                      className={`h-4 w-4 ${
                        star <= resource.rating!.average
                          ? 'text-yellow-400 fill-current'
                          : 'text-gray-300'
                      }`}
                    />
                  ))}
                </div>
                <span className="text-sm text-muted-foreground">
                  {resource.rating.average.toFixed(1)} ({resource.rating.count} reviews)
                </span>
              </div>
            )}
          </div>
        </div>
        
        {showActions && (
          <div className="flex gap-1">
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => onBookmark?.(resource.id)}
              className={resource.isBookmarked ? 'text-blue-600' : ''}
            >
              <Bookmark className={`h-4 w-4 ${resource.isBookmarked ? 'fill-current' : ''}`} />
            </Button>
            {onFlag && (
              <Button variant="ghost" size="sm" onClick={() => onFlag(resource.id)}>
                <Flag className="h-4 w-4" />
              </Button>
            )}
            <Button variant="ghost" size="sm">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </div>
        )}
      </div>

      {/* Description */}
      <div className="mb-4">
        <p className="text-sm text-muted-foreground line-clamp-2">
          {resource.description}
        </p>
      </div>

      {/* Tags */}
      {resource.tags.length > 0 && (
        <div className="flex flex-wrap gap-1 mb-4">
          {resource.tags.slice(0, 5).map((tag, index) => (
            <Badge key={index} variant="outline" className="text-xs">
              #{tag}
            </Badge>
          ))}
          {resource.tags.length > 5 && (
            <Badge variant="outline" className="text-xs">
              +{resource.tags.length - 5} more
            </Badge>
          )}
        </div>
      )}

      {/* Metadata */}
      <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
        <div className="flex items-center gap-2 text-muted-foreground">
          <Calendar className="h-4 w-4" />
          <span>Uploaded: {formatDate(resource.uploadDate)}</span>
        </div>
        {resource.lastModified && (
          <div className="flex items-center gap-2 text-muted-foreground">
            <Clock className="h-4 w-4" />
            <span>Modified: {formatDate(resource.lastModified)}</span>
          </div>
        )}
        {resource.fileSize && (
          <div className="flex items-center gap-2 text-muted-foreground">
            <File className="h-4 w-4" />
            <span>Size: {formatFileSize(resource.fileSize)}</span>
          </div>
        )}
        {resource.version && (
          <div className="flex items-center gap-2 text-muted-foreground">
            <span>Version: {resource.version}</span>
          </div>
        )}
      </div>

      {/* Stats */}
      <div className="flex items-center justify-between py-3 border-t border-b">
        <div className="flex items-center gap-6">
          <div className="flex items-center gap-2">
            <Download className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm font-medium">{formatCount(resource.downloadCount)}</span>
            <span className="text-sm text-muted-foreground">downloads</span>
          </div>
          
          <div className="flex items-center gap-2">
            <Eye className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm font-medium">{formatCount(resource.viewCount)}</span>
            <span className="text-sm text-muted-foreground">views</span>
          </div>
          
          <div className="flex items-center gap-2">
            <Heart className={`h-4 w-4 ${resource.isLiked ? 'text-red-500 fill-current' : 'text-muted-foreground'}`} />
            <span className="text-sm font-medium">{formatCount(resource.likeCount)}</span>
            <span className="text-sm text-muted-foreground">likes</span>
          </div>
        </div>
      </div>

      {/* Actions */}
      {showActions && (
        <div className="flex items-center justify-between pt-3">
          <div className="flex gap-2">
            <Button onClick={() => onView?.(resource)}>
              <Eye className="h-4 w-4 mr-2" />
              View
            </Button>
            <Button variant="outline" onClick={() => onDownload?.(resource.id)}>
              <Download className="h-4 w-4 mr-2" />
              Download
            </Button>
            <Button 
              variant="outline" 
              onClick={() => onLike?.(resource.id)}
              className={resource.isLiked ? 'text-red-600 border-red-200' : ''}
            >
              <Heart className={`h-4 w-4 mr-2 ${resource.isLiked ? 'fill-current' : ''}`} />
              {resource.isLiked ? 'Liked' : 'Like'}
            </Button>
            <Button variant="outline" onClick={() => onShare?.(resource)}>
              <Share2 className="h-4 w-4 mr-2" />
              Share
            </Button>
          </div>
          
          {resource.rating && (
            <div className="relative">
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => setShowRating(!showRating)}
              >
                <Star className="h-4 w-4 mr-1" />
                Rate
              </Button>
              
              {showRating && (
                <div className="absolute right-0 top-full mt-2 p-3 bg-white border rounded-lg shadow-lg z-10">
                  <div className="flex items-center gap-1 mb-2">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <button
                        key={star}
                        onClick={() => handleRating(star)}
                        onMouseEnter={() => setHoveredRating(star)}
                        onMouseLeave={() => setHoveredRating(0)}
                        className="p-1 hover:bg-gray-100 rounded"
                      >
                        <Star
                          className={`h-5 w-5 ${
                            star <= (hoveredRating || resource.rating?.userRating || 0)
                              ? 'text-yellow-400 fill-current'
                              : 'text-gray-300'
                          }`}
                        />
                      </button>
                    ))}
                  </div>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => setShowRating(false)}
                    className="w-full"
                  >
                    Cancel
                  </Button>
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </Card>
  );
};

// Grid version for resource galleries
export const ResourceCardGrid: React.FC<{
  resource: Resource;
  onSelect?: (resource: Resource) => void;
  className?: string;
}> = ({ resource, onSelect, className }) => {
  const getTypeIcon = (type: string) => {
    const iconProps = { className: "h-6 w-6" };
    switch (type) {
      case 'document':
      case 'presentation':
      case 'spreadsheet':
        return <FileText {...iconProps} />;
      case 'video':
        return <Video {...iconProps} />;
      case 'image':
        return <Image {...iconProps} />;
      case 'link':
        return <Link {...iconProps} />;
      default:
        return <File {...iconProps} />;
    }
  };

  const formatCount = (count: number) => {
    if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };

  return (
    <Card 
      className={`p-4 hover:shadow-md transition-shadow cursor-pointer ${className || ''}`}
      onClick={() => onSelect?.(resource)}
    >
      <div className="aspect-video mb-3 rounded-lg overflow-hidden bg-gray-100 flex items-center justify-center">
        {resource.thumbnailUrl ? (
          <img 
            src={resource.thumbnailUrl} 
            alt={resource.title}
            className="w-full h-full object-cover"
          />
        ) : (
          getTypeIcon(resource.type)
        )}
      </div>
      
      <div className="space-y-2">
        <h4 className="font-medium text-sm line-clamp-2">{resource.title}</h4>
        
        <div className="flex items-center gap-2">
          <Avatar className="h-5 w-5">
            <AvatarImage src={resource.author.avatar} alt={resource.author.name} />
            <AvatarFallback className="text-xs">
              {resource.author.name.split(' ').map(n => n[0]).join('').toUpperCase()}
            </AvatarFallback>
          </Avatar>
          <span className="text-xs text-muted-foreground truncate">
            {resource.author.name}
          </span>
        </div>
        
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <span>{formatCount(resource.downloadCount)} downloads</span>
          {resource.rating && (
            <div className="flex items-center gap-1">
              <Star className="h-3 w-3 text-yellow-400 fill-current" />
              <span>{resource.rating.average.toFixed(1)}</span>
            </div>
          )}
        </div>
      </div>
    </Card>
  );
};

export default ResourceCard;