import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Progress } from '@/components/ui/progress';
import { 
  Calendar, 
  Clock, 
  MapPin, 
  Users, 
  Video, 
  Phone,
  Mail,
  ExternalLink,
  Bell,
  BellOff,
  UserPlus,
  UserMinus,
  Share2,
  MoreHorizontal,
  CheckCircle,
  XCircle,
  AlertCircle,
  Star,
  MessageCircle
} from 'lucide-react';

interface EventAttendee {
  id: string;
  name: string;
  avatar?: string;
  role?: string;
  status: 'attending' | 'maybe' | 'not_attending' | 'pending';
  joinedAt?: string;
}

interface EventOrganizer {
  id: string;
  name: string;
  avatar?: string;
  role?: string;
  email?: string;
  phone?: string;
}

interface EventLocation {
  type: 'physical' | 'virtual' | 'hybrid';
  address?: string;
  room?: string;
  meetingLink?: string;
  dialIn?: string;
  instructions?: string;
}

interface Event {
  id: string;
  title: string;
  description: string;
  type: 'meeting' | 'training' | 'workshop' | 'webinar' | 'conference' | 'social' | 'other';
  category: string;
  startDate: string;
  endDate: string;
  duration: number; // in minutes
  location: EventLocation;
  organizer: EventOrganizer;
  attendees: EventAttendee[];
  maxAttendees?: number;
  tags: string[];
  status: 'scheduled' | 'ongoing' | 'completed' | 'cancelled' | 'postponed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  isRecurring?: boolean;
  recurringPattern?: string;
  reminderSet?: boolean;
  isPublic: boolean;
  requiresApproval?: boolean;
  materials?: string[];
  agenda?: string;
  notes?: string;
  rating?: {
    average: number;
    count: number;
  };
  userStatus?: 'attending' | 'maybe' | 'not_attending' | 'pending';
}

interface EventCardProps {
  event: Event;
  onJoin?: (eventId: string) => void;
  onLeave?: (eventId: string) => void;
  onUpdateStatus?: (eventId: string, status: 'attending' | 'maybe' | 'not_attending') => void;
  onSetReminder?: (eventId: string, enabled: boolean) => void;
  onShare?: (event: Event) => void;
  onViewDetails?: (event: Event) => void;
  onEdit?: (event: Event) => void;
  onCancel?: (eventId: string) => void;
  className?: string;
  showActions?: boolean;
  compact?: boolean;
  currentUserId?: string;
}

export const EventCard: React.FC<EventCardProps> = ({
  event,
  onJoin,
  onLeave,
  onUpdateStatus,
  onSetReminder,
  onShare,
  onViewDetails,
  onEdit,
  onCancel,
  className,
  showActions = true,
  compact = false,
  currentUserId
}) => {
  const [showAttendees, setShowAttendees] = useState(false);
  const [showStatusMenu, setShowStatusMenu] = useState(false);

  const getTypeIcon = (type: string) => {
    const iconProps = { className: "h-5 w-5" };
    switch (type) {
      case 'meeting':
        return <Users {...iconProps} />;
      case 'training':
      case 'workshop':
        return <Star {...iconProps} />;
      case 'webinar':
        return <Video {...iconProps} />;
      case 'conference':
        return <MessageCircle {...iconProps} />;
      default:
        return <Calendar {...iconProps} />;
    }
  };

  const getTypeColor = (type: string) => {
    const colors: Record<string, string> = {
      'meeting': 'bg-blue-100 text-blue-800',
      'training': 'bg-green-100 text-green-800',
      'workshop': 'bg-purple-100 text-purple-800',
      'webinar': 'bg-red-100 text-red-800',
      'conference': 'bg-indigo-100 text-indigo-800',
      'social': 'bg-pink-100 text-pink-800'
    };
    return colors[type] || 'bg-gray-100 text-gray-800';
  };

  const getStatusColor = (status: string) => {
    const colors: Record<string, string> = {
      'scheduled': 'bg-blue-100 text-blue-800',
      'ongoing': 'bg-green-100 text-green-800',
      'completed': 'bg-gray-100 text-gray-800',
      'cancelled': 'bg-red-100 text-red-800',
      'postponed': 'bg-yellow-100 text-yellow-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  const getPriorityColor = (priority: string) => {
    const colors: Record<string, string> = {
      'low': 'bg-gray-100 text-gray-800',
      'medium': 'bg-yellow-100 text-yellow-800',
      'high': 'bg-orange-100 text-orange-800',
      'urgent': 'bg-red-100 text-red-800'
    };
    return colors[priority] || 'bg-gray-100 text-gray-800';
  };

  const getAttendeeStatusIcon = (status: string) => {
    const iconProps = { className: "h-4 w-4" };
    switch (status) {
      case 'attending':
        return <CheckCircle {...iconProps} className="text-green-500" />;
      case 'maybe':
        return <AlertCircle {...iconProps} className="text-yellow-500" />;
      case 'not_attending':
        return <XCircle {...iconProps} className="text-red-500" />;
      default:
        return <Clock {...iconProps} className="text-gray-400" />;
    }
  };

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return {
      date: date.toLocaleDateString('en-US', {
        weekday: 'short',
        month: 'short',
        day: 'numeric'
      }),
      time: date.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      })
    };
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return mins > 0 ? `${hours}h ${mins}m` : `${hours}h`;
    }
    return `${mins}m`;
  };

  const getAttendeeStats = () => {
    const stats = event.attendees.reduce(
      (acc, attendee) => {
        acc[attendee.status] = (acc[attendee.status] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>
    );
    return stats;
  };

  const isUserAttending = () => {
    if (!currentUserId) return false;
    return event.attendees.some(
      attendee => attendee.id === currentUserId && attendee.status === 'attending'
    );
  };

  const canUserJoin = () => {
    if (!event.maxAttendees) return true;
    const attendingCount = event.attendees.filter(a => a.status === 'attending').length;
    return attendingCount < event.maxAttendees;
  };

  const startDateTime = formatDateTime(event.startDate);
  const endDateTime = formatDateTime(event.endDate);
  const attendeeStats = getAttendeeStats();

  if (compact) {
    return (
      <Card className={`p-3 hover:shadow-sm transition-shadow ${className || ''}`}>
        <div className="flex items-center gap-3">
          <div className="flex-shrink-0">
            <div className="h-12 w-12 rounded-lg bg-gray-100 flex items-center justify-center">
              {getTypeIcon(event.type)}
            </div>
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <h4 className="font-medium text-sm truncate">{event.title}</h4>
              <Badge className={getTypeColor(event.type)}>
                {event.type}
              </Badge>
              <Badge className={getStatusColor(event.status)}>
                {event.status}
              </Badge>
            </div>
            
            <div className="flex items-center gap-4 text-xs text-muted-foreground">
              <span>{startDateTime.date} at {startDateTime.time}</span>
              <span>{formatDuration(event.duration)}</span>
              <span>{event.attendees.length} attendees</span>
            </div>
          </div>
          
          {showActions && (
            <div className="flex gap-1">
              <Button variant="ghost" size="sm" onClick={() => onViewDetails?.(event)}>
                <ExternalLink className="h-4 w-4" />
              </Button>
              {!isUserAttending() && canUserJoin() && (
                <Button variant="ghost" size="sm" onClick={() => onJoin?.(event.id)}>
                  <UserPlus className="h-4 w-4" />
                </Button>
              )}
            </div>
          )}
        </div>
      </Card>
    );
  }

  return (
    <Card className={`p-4 hover:shadow-md transition-shadow ${className || ''}`}>
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-start gap-3 flex-1">
          <div className="flex-shrink-0">
            <div className="h-16 w-16 rounded-lg bg-gray-100 flex items-center justify-center">
              {getTypeIcon(event.type)}
            </div>
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-2">
              <h3 className="font-semibold text-lg truncate">{event.title}</h3>
              <Badge className={getTypeColor(event.type)}>
                {event.type}
              </Badge>
              <Badge className={getStatusColor(event.status)}>
                {event.status}
              </Badge>
              <Badge className={getPriorityColor(event.priority)}>
                {event.priority}
              </Badge>
            </div>
            
            <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
              <Avatar className="h-6 w-6">
                <AvatarImage src={event.organizer.avatar} alt={event.organizer.name} />
                <AvatarFallback className="text-xs">
                  {event.organizer.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <span className="font-medium">{event.organizer.name}</span>
              {event.organizer.role && (
                <Badge variant="secondary" className="text-xs">
                  {event.organizer.role}
                </Badge>
              )}
            </div>
            
            {event.isRecurring && (
              <div className="flex items-center gap-1 text-xs text-muted-foreground">
                <Calendar className="h-3 w-3" />
                <span>Recurring: {event.recurringPattern}</span>
              </div>
            )}
          </div>
        </div>
        
        {showActions && (
          <div className="flex gap-1">
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => onSetReminder?.(event.id, !event.reminderSet)}
              className={event.reminderSet ? 'text-blue-600' : ''}
            >
              {event.reminderSet ? <Bell className="h-4 w-4" /> : <BellOff className="h-4 w-4" />}
            </Button>
            <Button variant="ghost" size="sm" onClick={() => onShare?.(event)}>
              <Share2 className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </div>
        )}
      </div>

      {/* Description */}
      <div className="mb-4">
        <p className="text-sm text-muted-foreground line-clamp-2">
          {event.description}
        </p>
      </div>

      {/* Date & Time */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
          <Calendar className="h-5 w-5 text-blue-600" />
          <div>
            <div className="font-medium text-sm">{startDateTime.date}</div>
            <div className="text-xs text-muted-foreground">
              {startDateTime.time} - {endDateTime.time}
            </div>
          </div>
        </div>
        
        <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
          <Clock className="h-5 w-5 text-green-600" />
          <div>
            <div className="font-medium text-sm">{formatDuration(event.duration)}</div>
            <div className="text-xs text-muted-foreground">Duration</div>
          </div>
        </div>
      </div>

      {/* Location */}
      <div className="mb-4">
        <div className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
          {event.location.type === 'virtual' ? (
            <Video className="h-5 w-5 text-purple-600 mt-0.5" />
          ) : event.location.type === 'physical' ? (
            <MapPin className="h-5 w-5 text-red-600 mt-0.5" />
          ) : (
            <MapPin className="h-5 w-5 text-orange-600 mt-0.5" />
          )}
          
          <div className="flex-1">
            <div className="font-medium text-sm capitalize">{event.location.type} Event</div>
            {event.location.address && (
              <div className="text-xs text-muted-foreground">{event.location.address}</div>
            )}
            {event.location.room && (
              <div className="text-xs text-muted-foreground">Room: {event.location.room}</div>
            )}
            {event.location.meetingLink && (
              <div className="flex items-center gap-1 mt-1">
                <Button variant="link" size="sm" className="p-0 h-auto text-xs">
                  <ExternalLink className="h-3 w-3 mr-1" />
                  Join Meeting
                </Button>
              </div>
            )}
            {event.location.dialIn && (
              <div className="flex items-center gap-1 text-xs text-muted-foreground mt-1">
                <Phone className="h-3 w-3" />
                <span>{event.location.dialIn}</span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Attendees */}
      <div className="mb-4">
        <div className="flex items-center justify-between mb-2">
          <h4 className="font-medium text-sm">Attendees ({event.attendees.length}{event.maxAttendees ? `/${event.maxAttendees}` : ''})</h4>
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={() => setShowAttendees(!showAttendees)}
          >
            {showAttendees ? 'Hide' : 'Show'}
          </Button>
        </div>
        
        <div className="flex items-center gap-4 mb-2">
          <div className="flex items-center gap-1 text-xs">
            <CheckCircle className="h-3 w-3 text-green-500" />
            <span>{attendeeStats.attending || 0} attending</span>
          </div>
          <div className="flex items-center gap-1 text-xs">
            <AlertCircle className="h-3 w-3 text-yellow-500" />
            <span>{attendeeStats.maybe || 0} maybe</span>
          </div>
          <div className="flex items-center gap-1 text-xs">
            <XCircle className="h-3 w-3 text-red-500" />
            <span>{attendeeStats.not_attending || 0} not attending</span>
          </div>
        </div>
        
        {event.maxAttendees && (
          <div className="mb-2">
            <Progress 
              value={(attendeeStats.attending || 0) / event.maxAttendees * 100} 
              className="h-2"
            />
          </div>
        )}
        
        <div className="flex -space-x-2">
          {event.attendees.slice(0, 8).map((attendee) => (
            <div key={attendee.id} className="relative">
              <Avatar className="h-8 w-8 border-2 border-white">
                <AvatarImage src={attendee.avatar} alt={attendee.name} />
                <AvatarFallback className="text-xs">
                  {attendee.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <div className="absolute -bottom-1 -right-1">
                {getAttendeeStatusIcon(attendee.status)}
              </div>
            </div>
          ))}
          {event.attendees.length > 8 && (
            <div className="h-8 w-8 rounded-full bg-gray-200 border-2 border-white flex items-center justify-center">
              <span className="text-xs font-medium">+{event.attendees.length - 8}</span>
            </div>
          )}
        </div>
        
        {showAttendees && (
          <div className="mt-3 space-y-2 max-h-40 overflow-y-auto">
            {event.attendees.map((attendee) => (
              <div key={attendee.id} className="flex items-center gap-3 p-2 bg-gray-50 rounded">
                <Avatar className="h-6 w-6">
                  <AvatarImage src={attendee.avatar} alt={attendee.name} />
                  <AvatarFallback className="text-xs">
                    {attendee.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <div className="font-medium text-sm">{attendee.name}</div>
                  {attendee.role && (
                    <div className="text-xs text-muted-foreground">{attendee.role}</div>
                  )}
                </div>
                <div className="flex items-center gap-1">
                  {getAttendeeStatusIcon(attendee.status)}
                  <span className="text-xs capitalize">{attendee.status.replace('_', ' ')}</span>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Tags */}
      {event.tags.length > 0 && (
        <div className="flex flex-wrap gap-1 mb-4">
          {event.tags.map((tag, index) => (
            <Badge key={index} variant="outline" className="text-xs">
              #{tag}
            </Badge>
          ))}
        </div>
      )}

      {/* Actions */}
      {showActions && (
        <div className="flex items-center justify-between pt-3 border-t">
          <div className="flex gap-2">
            {!isUserAttending() && canUserJoin() && event.status === 'scheduled' && (
              <Button onClick={() => onJoin?.(event.id)}>
                <UserPlus className="h-4 w-4 mr-2" />
                Join Event
              </Button>
            )}
            
            {isUserAttending() && (
              <div className="relative">
                <Button 
                  variant="outline" 
                  onClick={() => setShowStatusMenu(!showStatusMenu)}
                >
                  <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                  Attending
                </Button>
                
                {showStatusMenu && (
                  <div className="absolute left-0 top-full mt-2 p-2 bg-white border rounded-lg shadow-lg z-10 min-w-40">
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="w-full justify-start"
                      onClick={() => {
                        onUpdateStatus?.(event.id, 'attending');
                        setShowStatusMenu(false);
                      }}
                    >
                      <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                      Attending
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="w-full justify-start"
                      onClick={() => {
                        onUpdateStatus?.(event.id, 'maybe');
                        setShowStatusMenu(false);
                      }}
                    >
                      <AlertCircle className="h-4 w-4 mr-2 text-yellow-500" />
                      Maybe
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="w-full justify-start"
                      onClick={() => {
                        onUpdateStatus?.(event.id, 'not_attending');
                        setShowStatusMenu(false);
                      }}
                    >
                      <XCircle className="h-4 w-4 mr-2 text-red-500" />
                      Not Attending
                    </Button>
                    <hr className="my-1" />
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="w-full justify-start text-red-600"
                      onClick={() => {
                        onLeave?.(event.id);
                        setShowStatusMenu(false);
                      }}
                    >
                      <UserMinus className="h-4 w-4 mr-2" />
                      Leave Event
                    </Button>
                  </div>
                )}
              </div>
            )}
            
            <Button variant="outline" onClick={() => onViewDetails?.(event)}>
              <ExternalLink className="h-4 w-4 mr-2" />
              View Details
            </Button>
          </div>
          
          {event.rating && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Star className="h-4 w-4 text-yellow-400 fill-current" />
              <span>{event.rating.average.toFixed(1)} ({event.rating.count} reviews)</span>
            </div>
          )}
        </div>
      )}
    </Card>
  );
};

// Calendar view version
export const EventCardCalendar: React.FC<{
  event: Event;
  onSelect?: (event: Event) => void;
  className?: string;
}> = ({ event, onSelect, className }) => {
  const startTime = new Date(event.startDate).toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  });

  const getStatusColor = (status: string) => {
    const colors: Record<string, string> = {
      'scheduled': 'border-l-blue-500',
      'ongoing': 'border-l-green-500',
      'completed': 'border-l-gray-500',
      'cancelled': 'border-l-red-500',
      'postponed': 'border-l-yellow-500'
    };
    return colors[status] || 'border-l-gray-500';
  };

  return (
    <Card 
      className={`p-2 hover:shadow-sm transition-shadow cursor-pointer border-l-4 ${getStatusColor(event.status)} ${className || ''}`}
      onClick={() => onSelect?.(event)}
    >
      <div className="space-y-1">
        <div className="flex items-center gap-2">
          <span className="text-xs font-medium text-muted-foreground">{startTime}</span>
          <Badge className={`${getTypeColor(event.type)} text-xs`}>
            {event.type}
          </Badge>
        </div>
        
        <h4 className="font-medium text-sm line-clamp-1">{event.title}</h4>
        
        <div className="flex items-center gap-2 text-xs text-muted-foreground">
          {event.location.type === 'virtual' ? (
            <Video className="h-3 w-3" />
          ) : (
            <MapPin className="h-3 w-3" />
          )}
          <span className="truncate">
            {event.location.type === 'virtual' ? 'Virtual' : event.location.address || 'Physical'}
          </span>
        </div>
        
        <div className="flex items-center justify-between">
          <div className="flex -space-x-1">
            {event.attendees.slice(0, 3).map((attendee) => (
              <Avatar key={attendee.id} className="h-4 w-4 border border-white">
                <AvatarImage src={attendee.avatar} alt={attendee.name} />
                <AvatarFallback className="text-xs">
                  {attendee.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                </AvatarFallback>
              </Avatar>
            ))}
            {event.attendees.length > 3 && (
              <div className="h-4 w-4 rounded-full bg-gray-200 border border-white flex items-center justify-center">
                <span className="text-xs font-medium">+{event.attendees.length - 3}</span>
              </div>
            )}
          </div>
          
          <span className="text-xs text-muted-foreground">
            {formatDuration(event.duration)}
          </span>
        </div>
      </div>
    </Card>
  );
};

function getTypeColor(type: string): string {
  const colors: Record<string, string> = {
    'meeting': 'bg-blue-100 text-blue-800',
    'training': 'bg-green-100 text-green-800',
    'workshop': 'bg-purple-100 text-purple-800',
    'webinar': 'bg-red-100 text-red-800',
    'conference': 'bg-indigo-100 text-indigo-800',
    'social': 'bg-pink-100 text-pink-800'
  };
  return colors[type] || 'bg-gray-100 text-gray-800';
}

function formatDuration(minutes: number): string {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  if (hours > 0) {
    return mins > 0 ? `${hours}h ${mins}m` : `${hours}h`;
  }
  return `${mins}m`;
}

export default EventCard;