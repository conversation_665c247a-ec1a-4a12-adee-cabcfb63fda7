import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Textarea } from '@/components/ui/textarea';
import { MessageCircle, ThumbsUp, ThumbsDown, Reply, MoreHorizontal, Pin, Flag } from 'lucide-react';

interface DiscussionReply {
  id: string;
  content: string;
  author: {
    id: string;
    name: string;
    avatar?: string;
    role?: string;
  };
  timestamp: string;
  likes: number;
  isLiked?: boolean;
}

interface Discussion {
  id: string;
  title: string;
  content: string;
  author: {
    id: string;
    name: string;
    avatar?: string;
    role?: string;
  };
  timestamp: string;
  category: string;
  tags: string[];
  likes: number;
  dislikes: number;
  replies: DiscussionReply[];
  isPinned?: boolean;
  isResolved?: boolean;
  isLiked?: boolean;
  isDisliked?: boolean;
}

interface DiscussionCardProps {
  discussion: Discussion;
  onLike?: (discussionId: string) => void;
  onDislike?: (discussionId: string) => void;
  onReply?: (discussionId: string, content: string) => void;
  onReplyLike?: (discussionId: string, replyId: string) => void;
  onPin?: (discussionId: string) => void;
  onFlag?: (discussionId: string) => void;
  onViewDetails?: (discussion: Discussion) => void;
  className?: string;
  showActions?: boolean;
  expanded?: boolean;
}

export const DiscussionCard: React.FC<DiscussionCardProps> = ({
  discussion,
  onLike,
  onDislike,
  onReply,
  onReplyLike,
  onPin,
  onFlag,
  onViewDetails,
  className,
  showActions = true,
  expanded = false
}) => {
  const [showReplyForm, setShowReplyForm] = useState(false);
  const [replyContent, setReplyContent] = useState('');
  const [showAllReplies, setShowAllReplies] = useState(expanded);

  const handleReplySubmit = () => {
    if (replyContent.trim() && onReply) {
      onReply(discussion.id, replyContent);
      setReplyContent('');
      setShowReplyForm(false);
    }
  };

  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      'general': 'bg-blue-100 text-blue-800',
      'technical': 'bg-green-100 text-green-800',
      'feedback': 'bg-yellow-100 text-yellow-800',
      'announcement': 'bg-purple-100 text-purple-800',
      'question': 'bg-orange-100 text-orange-800',
      'idea': 'bg-pink-100 text-pink-800'
    };
    return colors[category.toLowerCase()] || 'bg-gray-100 text-gray-800';
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    return date.toLocaleDateString();
  };

  const visibleReplies = showAllReplies ? discussion.replies : discussion.replies.slice(0, 2);

  return (
    <Card className={`p-4 hover:shadow-md transition-shadow ${className || ''}`}>
      {/* Header */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-start gap-3 flex-1">
          <Avatar className="h-10 w-10">
            <AvatarImage src={discussion.author.avatar} alt={discussion.author.name} />
            <AvatarFallback>
              {discussion.author.name.split(' ').map(n => n[0]).join('').toUpperCase()}
            </AvatarFallback>
          </Avatar>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              {discussion.isPinned && <Pin className="h-4 w-4 text-yellow-500" />}
              <h3 className="font-semibold text-lg truncate">{discussion.title}</h3>
              <Badge className={getCategoryColor(discussion.category)}>
                {discussion.category}
              </Badge>
              {discussion.isResolved && (
                <Badge variant="outline" className="bg-green-50 text-green-700">
                  Resolved
                </Badge>
              )}
            </div>
            
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <span className="font-medium">{discussion.author.name}</span>
              {discussion.author.role && (
                <Badge variant="secondary" className="text-xs">
                  {discussion.author.role}
                </Badge>
              )}
              <span>•</span>
              <span>{formatTimestamp(discussion.timestamp)}</span>
            </div>
          </div>
        </div>
        
        {showActions && (
          <div className="flex gap-1">
            {onPin && (
              <Button variant="ghost" size="sm" onClick={() => onPin(discussion.id)}>
                <Pin className={`h-4 w-4 ${discussion.isPinned ? 'text-yellow-500' : ''}`} />
              </Button>
            )}
            {onFlag && (
              <Button variant="ghost" size="sm" onClick={() => onFlag(discussion.id)}>
                <Flag className="h-4 w-4" />
              </Button>
            )}
            <Button variant="ghost" size="sm">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </div>
        )}
      </div>

      {/* Content */}
      <div className="mb-4">
        <p className="text-sm text-muted-foreground whitespace-pre-wrap">
          {expanded ? discussion.content : 
           discussion.content.length > 200 ? 
           `${discussion.content.substring(0, 200)}...` : 
           discussion.content
          }
        </p>
        {!expanded && discussion.content.length > 200 && onViewDetails && (
          <Button 
            variant="link" 
            size="sm" 
            className="p-0 h-auto text-xs"
            onClick={() => onViewDetails(discussion)}
          >
            Read more
          </Button>
        )}
      </div>

      {/* Tags */}
      {discussion.tags.length > 0 && (
        <div className="flex flex-wrap gap-1 mb-4">
          {discussion.tags.map((tag, index) => (
            <Badge key={index} variant="outline" className="text-xs">
              #{tag}
            </Badge>
          ))}
        </div>
      )}

      {/* Actions Bar */}
      <div className="flex items-center justify-between py-2 border-t border-b">
        <div className="flex items-center gap-4">
          <Button 
            variant="ghost" 
            size="sm" 
            className={`gap-2 ${discussion.isLiked ? 'text-green-600' : ''}`}
            onClick={() => onLike?.(discussion.id)}
          >
            <ThumbsUp className="h-4 w-4" />
            {discussion.likes}
          </Button>
          
          <Button 
            variant="ghost" 
            size="sm" 
            className={`gap-2 ${discussion.isDisliked ? 'text-red-600' : ''}`}
            onClick={() => onDislike?.(discussion.id)}
          >
            <ThumbsDown className="h-4 w-4" />
            {discussion.dislikes}
          </Button>
          
          <Button 
            variant="ghost" 
            size="sm" 
            className="gap-2"
            onClick={() => setShowReplyForm(!showReplyForm)}
          >
            <Reply className="h-4 w-4" />
            Reply
          </Button>
        </div>
        
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <MessageCircle className="h-4 w-4" />
          <span>{discussion.replies.length} replies</span>
        </div>
      </div>

      {/* Reply Form */}
      {showReplyForm && (
        <div className="mt-4 p-3 bg-gray-50 rounded-lg">
          <Textarea
            placeholder="Write your reply..."
            value={replyContent}
            onChange={(e) => setReplyContent(e.target.value)}
            rows={3}
            className="mb-3"
          />
          <div className="flex gap-2 justify-end">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => {
                setShowReplyForm(false);
                setReplyContent('');
              }}
            >
              Cancel
            </Button>
            <Button 
              size="sm" 
              onClick={handleReplySubmit}
              disabled={!replyContent.trim()}
            >
              Reply
            </Button>
          </div>
        </div>
      )}

      {/* Replies */}
      {discussion.replies.length > 0 && (
        <div className="mt-4 space-y-3">
          {visibleReplies.map((reply) => (
            <ReplyCard 
              key={reply.id} 
              reply={reply} 
              onLike={() => onReplyLike?.(discussion.id, reply.id)}
            />
          ))}
          
          {discussion.replies.length > 2 && !showAllReplies && (
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => setShowAllReplies(true)}
              className="text-xs"
            >
              Show {discussion.replies.length - 2} more replies
            </Button>
          )}
          
          {showAllReplies && discussion.replies.length > 2 && (
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => setShowAllReplies(false)}
              className="text-xs"
            >
              Show fewer replies
            </Button>
          )}
        </div>
      )}
    </Card>
  );
};

// Reply Card Component
const ReplyCard: React.FC<{
  reply: DiscussionReply;
  onLike?: () => void;
}> = ({ reply, onLike }) => {
  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    return date.toLocaleDateString();
  };

  return (
    <div className="flex gap-3 p-3 bg-gray-50 rounded-lg">
      <Avatar className="h-8 w-8">
        <AvatarImage src={reply.author.avatar} alt={reply.author.name} />
        <AvatarFallback className="text-xs">
          {reply.author.name.split(' ').map(n => n[0]).join('').toUpperCase()}
        </AvatarFallback>
      </Avatar>
      
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2 mb-1">
          <span className="font-medium text-sm">{reply.author.name}</span>
          {reply.author.role && (
            <Badge variant="secondary" className="text-xs">
              {reply.author.role}
            </Badge>
          )}
          <span className="text-xs text-muted-foreground">
            {formatTimestamp(reply.timestamp)}
          </span>
        </div>
        
        <p className="text-sm text-muted-foreground mb-2 whitespace-pre-wrap">
          {reply.content}
        </p>
        
        <Button 
          variant="ghost" 
          size="sm" 
          className={`gap-1 text-xs h-6 ${reply.isLiked ? 'text-green-600' : ''}`}
          onClick={onLike}
        >
          <ThumbsUp className="h-3 w-3" />
          {reply.likes}
        </Button>
      </div>
    </div>
  );
};

// Compact version for lists
export const DiscussionCardCompact: React.FC<{
  discussion: Discussion;
  onSelect?: (discussion: Discussion) => void;
  className?: string;
}> = ({ discussion, onSelect, className }) => {
  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      'general': 'bg-blue-100 text-blue-800',
      'technical': 'bg-green-100 text-green-800',
      'feedback': 'bg-yellow-100 text-yellow-800',
      'announcement': 'bg-purple-100 text-purple-800',
      'question': 'bg-orange-100 text-orange-800',
      'idea': 'bg-pink-100 text-pink-800'
    };
    return colors[category.toLowerCase()] || 'bg-gray-100 text-gray-800';
  };

  return (
    <Card 
      className={`p-3 hover:shadow-sm transition-shadow cursor-pointer ${className || ''}`}
      onClick={() => onSelect?.(discussion)}
    >
      <div className="flex items-start gap-3">
        <Avatar className="h-8 w-8">
          <AvatarImage src={discussion.author.avatar} alt={discussion.author.name} />
          <AvatarFallback className="text-xs">
            {discussion.author.name.split(' ').map(n => n[0]).join('').toUpperCase()}
          </AvatarFallback>
        </Avatar>
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            {discussion.isPinned && <Pin className="h-3 w-3 text-yellow-500" />}
            <h4 className="font-medium text-sm truncate">{discussion.title}</h4>
            <Badge className={getCategoryColor(discussion.category)}>
              {discussion.category}
            </Badge>
          </div>
          
          <div className="flex items-center gap-4 text-xs text-muted-foreground">
            <span>{discussion.author.name}</span>
            <span>{discussion.replies.length} replies</span>
            <span>{discussion.likes} likes</span>
            <span>{new Date(discussion.timestamp).toLocaleDateString()}</span>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default DiscussionCard;