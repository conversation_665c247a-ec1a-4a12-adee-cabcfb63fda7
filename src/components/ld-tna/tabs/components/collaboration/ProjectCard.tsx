import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Calendar, Users, Clock, Target, MoreHorizontal } from 'lucide-react';

interface ProjectMember {
  id: string;
  name: string;
  role: string;
  avatar?: string;
  status: 'active' | 'inactive';
}

interface Project {
  id: string;
  name: string;
  description: string;
  status: 'planning' | 'in-progress' | 'review' | 'completed' | 'on-hold';
  priority: 'low' | 'medium' | 'high' | 'critical';
  progress: number;
  startDate: string;
  endDate: string;
  members: ProjectMember[];
  tags: string[];
  budget?: number;
  spent?: number;
  deliverables: number;
  completedDeliverables: number;
}

interface ProjectCardProps {
  project: Project;
  onJoin?: (projectId: string) => void;
  onLeave?: (projectId: string) => void;
  onViewDetails?: (project: Project) => void;
  onEdit?: (project: Project) => void;
  onDelete?: (projectId: string) => void;
  className?: string;
  showActions?: boolean;
  isUserMember?: boolean;
}

export const ProjectCard: React.FC<ProjectCardProps> = ({
  project,
  onJoin,
  onLeave,
  onViewDetails,
  onEdit,
  onDelete,
  className,
  showActions = true,
  isUserMember = false
}) => {
  const getStatusColor = (status: Project['status']) => {
    switch (status) {
      case 'planning': return 'bg-blue-100 text-blue-800';
      case 'in-progress': return 'bg-green-100 text-green-800';
      case 'review': return 'bg-yellow-100 text-yellow-800';
      case 'completed': return 'bg-purple-100 text-purple-800';
      case 'on-hold': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: Project['priority']) => {
    switch (priority) {
      case 'critical': return 'bg-red-500';
      case 'high': return 'bg-orange-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  const getDaysRemaining = () => {
    const endDate = new Date(project.endDate);
    const today = new Date();
    const diffTime = endDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const daysRemaining = getDaysRemaining();
  const budgetUsed = project.budget && project.spent ? (project.spent / project.budget) * 100 : 0;

  return (
    <Card className={`p-4 hover:shadow-md transition-shadow ${className || ''}`}>
      <div className="flex items-start justify-between mb-3">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-2">
            <div className={`w-3 h-3 rounded-full ${getPriorityColor(project.priority)}`} />
            <h3 className="font-semibold text-lg">{project.name}</h3>
            <Badge className={getStatusColor(project.status)}>
              {project.status.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
            </Badge>
          </div>
          <p className="text-sm text-muted-foreground mb-3 line-clamp-2">{project.description}</p>
        </div>
        
        {showActions && (
          <div className="flex gap-2 ml-4">
            {!isUserMember && onJoin && (
              <Button size="sm" onClick={() => onJoin(project.id)}>
                Join
              </Button>
            )}
            {isUserMember && onLeave && (
              <Button variant="outline" size="sm" onClick={() => onLeave(project.id)}>
                Leave
              </Button>
            )}
            {onViewDetails && (
              <Button variant="outline" size="sm" onClick={() => onViewDetails(project)}>
                View
              </Button>
            )}
            <Button variant="ghost" size="sm">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </div>
        )}
      </div>

      <div className="space-y-4">
        {/* Progress Section */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Progress</span>
            <span className="font-medium">{project.progress}%</span>
          </div>
          <Progress value={project.progress} className="h-2" />
        </div>

        {/* Deliverables */}
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center gap-2">
            <Target className="h-4 w-4 text-muted-foreground" />
            <span className="text-muted-foreground">Deliverables</span>
          </div>
          <span className="font-medium">
            {project.completedDeliverables}/{project.deliverables}
          </span>
        </div>

        {/* Timeline */}
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-muted-foreground" />
            <span className="text-muted-foreground">Timeline</span>
          </div>
          <div className="text-right">
            <div className="font-medium">
              {new Date(project.startDate).toLocaleDateString()} - {new Date(project.endDate).toLocaleDateString()}
            </div>
            <div className={`text-xs ${
              daysRemaining < 0 ? 'text-red-500' : 
              daysRemaining < 7 ? 'text-yellow-500' : 'text-muted-foreground'
            }`}>
              {daysRemaining < 0 ? `${Math.abs(daysRemaining)} days overdue` :
               daysRemaining === 0 ? 'Due today' :
               `${daysRemaining} days remaining`}
            </div>
          </div>
        </div>

        {/* Budget (if available) */}
        {project.budget && (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Budget Usage</span>
              <span className="font-medium">
                ${project.spent?.toLocaleString()} / ${project.budget.toLocaleString()}
              </span>
            </div>
            <Progress value={budgetUsed} className="h-2" />
          </div>
        )}

        {/* Team Members */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Users className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm text-muted-foreground">Team</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="flex -space-x-2">
              {project.members.slice(0, 4).map((member) => (
                <Avatar key={member.id} className="h-6 w-6 border-2 border-white">
                  <AvatarImage src={member.avatar} alt={member.name} />
                  <AvatarFallback className="text-xs">
                    {member.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                  </AvatarFallback>
                </Avatar>
              ))}
              {project.members.length > 4 && (
                <div className="h-6 w-6 rounded-full bg-gray-100 border-2 border-white flex items-center justify-center">
                  <span className="text-xs text-muted-foreground">+{project.members.length - 4}</span>
                </div>
              )}
            </div>
            <span className="text-sm text-muted-foreground ml-2">
              {project.members.length} member{project.members.length !== 1 ? 's' : ''}
            </span>
          </div>
        </div>

        {/* Tags */}
        {project.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 pt-2 border-t">
            {project.tags.slice(0, 3).map((tag, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
            {project.tags.length > 3 && (
              <Badge variant="outline" className="text-xs">
                +{project.tags.length - 3} more
              </Badge>
            )}
          </div>
        )}
      </div>
    </Card>
  );
};

// Compact version for lists
export const ProjectCardCompact: React.FC<{
  project: Project;
  onSelect?: (project: Project) => void;
  className?: string;
}> = ({ project, onSelect, className }) => {
  const getStatusColor = (status: Project['status']) => {
    switch (status) {
      case 'planning': return 'bg-blue-100 text-blue-800';
      case 'in-progress': return 'bg-green-100 text-green-800';
      case 'review': return 'bg-yellow-100 text-yellow-800';
      case 'completed': return 'bg-purple-100 text-purple-800';
      case 'on-hold': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card 
      className={`p-3 hover:shadow-sm transition-shadow cursor-pointer ${className || ''}`}
      onClick={() => onSelect?.(project)}
    >
      <div className="flex items-center justify-between">
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <h4 className="font-medium truncate">{project.name}</h4>
            <Badge className={getStatusColor(project.status)}>
              {project.status}
            </Badge>
          </div>
          <div className="flex items-center gap-4 text-sm text-muted-foreground">
            <span>{project.progress}% complete</span>
            <span>{project.members.length} members</span>
            <span>{new Date(project.endDate).toLocaleDateString()}</span>
          </div>
        </div>
        <Progress value={project.progress} className="w-16 h-2" />
      </div>
    </Card>
  );
};

export default ProjectCard;