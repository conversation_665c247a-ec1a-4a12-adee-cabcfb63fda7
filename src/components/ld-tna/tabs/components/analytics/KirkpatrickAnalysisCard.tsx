import React from 'react';
import { Card } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  KirkpatrickReaction, 
  KirkpatrickLearning, 
  KirkpatrickBehavior, 
  KirkpatrickResults 
} from '../../../common/types';
import { TrendingUp, TrendingDown, Minus, Target, Users, Award, DollarSign } from 'lucide-react';

type KirkpatrickLevel = 1 | 2 | 3 | 4;

interface KirkpatrickAnalysisCardProps {
  level: KirkpatrickLevel;
  data: KirkpatrickReaction | KirkpatrickLearning | KirkpatrickBehavior | KirkpatrickResults;
  title: string;
  description: string;
  onViewDetails?: () => void;
  onExport?: () => void;
  className?: string;
  showActions?: boolean;
}

export const KirkpatrickAnalysisCard: React.FC<KirkpatrickAnalysisCardProps> = ({
  level,
  data,
  title,
  description,
  onViewDetails,
  onExport,
  className,
  showActions = true
}) => {
  const getLevelColor = (level: KirkpatrickLevel) => {
    switch (level) {
      case 1: return 'bg-blue-100 text-blue-800';
      case 2: return 'bg-green-100 text-green-800';
      case 3: return 'bg-yellow-100 text-yellow-800';
      case 4: return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getLevelIcon = (level: KirkpatrickLevel) => {
    switch (level) {
      case 1: return Users;
      case 2: return Award;
      case 3: return Target;
      case 4: return DollarSign;
      default: return Users;
    }
  };

  const LevelIcon = getLevelIcon(level);

  const renderMetrics = () => {
    switch (level) {
      case 1:
        const reactionData = data as KirkpatrickReaction;
        return (
          <div className="grid grid-cols-2 gap-4">
            <MetricItem 
              label="Satisfaction" 
              value={`${reactionData.satisfactionScore}%`} 
              progress={reactionData.satisfactionScore}
            />
            <MetricItem 
              label="Engagement" 
              value={`${reactionData.engagementLevel}%`} 
              progress={reactionData.engagementLevel}
            />
            <MetricItem 
              label="Relevance" 
              value={`${reactionData.relevanceRating}%`} 
              progress={reactionData.relevanceRating}
            />
            <MetricItem 
              label="Completion" 
              value={`${reactionData.completionRate}%`} 
              progress={reactionData.completionRate}
            />
          </div>
        );
      
      case 2:
        const learningData = data as KirkpatrickLearning;
        return (
          <div className="grid grid-cols-2 gap-4">
            <MetricItem 
              label="Knowledge" 
              value={`${learningData.knowledgeAcquisition}%`} 
              progress={learningData.knowledgeAcquisition}
            />
            <MetricItem 
              label="Skills" 
              value={`${learningData.skillDemonstration}%`} 
              progress={learningData.skillDemonstration}
            />
            <MetricItem 
              label="Attitude" 
              value={`${learningData.attitudeChange}%`} 
              progress={learningData.attitudeChange}
            />
            <MetricItem 
              label="Confidence" 
              value={`${learningData.confidenceLevel}%`} 
              progress={learningData.confidenceLevel}
            />
          </div>
        );
      
      case 3:
        const behaviorData = data as KirkpatrickBehavior;
        return (
          <div className="grid grid-cols-2 gap-4">
            <MetricItem 
              label="Behavior Change" 
              value={`${behaviorData.behaviorChange}%`} 
              progress={behaviorData.behaviorChange}
            />
            <MetricItem 
              label="Application Rate" 
              value={`${behaviorData.applicationRate}%`} 
              progress={behaviorData.applicationRate}
            />
            <MetricItem 
              label="Performance" 
              value={`${behaviorData.performanceImprovement}%`} 
              progress={behaviorData.performanceImprovement}
            />
            <MetricItem 
              label="Time to Apply" 
              value={`${behaviorData.timeToApplication} days`} 
              progress={Math.max(0, 100 - (behaviorData.timeToApplication / 30) * 100)}
            />
          </div>
        );
      
      case 4:
        const resultsData = data as KirkpatrickResults;
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <MetricItem 
                label="Business Impact" 
                value={`${resultsData.businessImpact}%`} 
                progress={resultsData.businessImpact}
              />
              <MetricItem 
                label="Quality Improvement" 
                value={`${resultsData.qualityImprovement}%`} 
                progress={resultsData.qualityImprovement}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-3 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">
                  ${resultsData.revenueIncrease.toLocaleString()}
                </div>
                <div className="text-sm text-muted-foreground">Revenue Increase</div>
              </div>
              <div className="text-center p-3 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">
                  ${resultsData.costReduction.toLocaleString()}
                </div>
                <div className="text-sm text-muted-foreground">Cost Reduction</div>
              </div>
            </div>
          </div>
        );
      
      default:
        return <div>No data available</div>;
    }
  };

  return (
    <Card className={`p-6 hover:shadow-md transition-shadow ${className || ''}`}>
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center gap-3">
          <div className={`p-2 rounded-lg ${getLevelColor(level)}`}>
            <LevelIcon className="h-5 w-5" />
          </div>
          <div>
            <div className="flex items-center gap-2 mb-1">
              <h3 className="font-semibold text-lg">{title}</h3>
              <Badge className={getLevelColor(level)}>
                Level {level}
              </Badge>
            </div>
            <p className="text-sm text-muted-foreground">{description}</p>
          </div>
        </div>
        
        {showActions && (
          <div className="flex gap-2">
            {onViewDetails && (
              <Button variant="outline" size="sm" onClick={onViewDetails}>
                Details
              </Button>
            )}
            {onExport && (
              <Button variant="outline" size="sm" onClick={onExport}>
                Export
              </Button>
            )}
          </div>
        )}
      </div>

      <div className="space-y-4">
        {renderMetrics()}
      </div>
    </Card>
  );
};

// Helper component for individual metrics
const MetricItem: React.FC<{
  label: string;
  value: string;
  progress: number;
  trend?: 'up' | 'down' | 'neutral';
}> = ({ label, value, progress, trend }) => {
  const getTrendIcon = () => {
    switch (trend) {
      case 'up': return <TrendingUp className="h-4 w-4 text-green-500" />;
      case 'down': return <TrendingDown className="h-4 w-4 text-red-500" />;
      case 'neutral': return <Minus className="h-4 w-4 text-gray-500" />;
      default: return null;
    }
  };

  const getProgressColor = (progress: number) => {
    if (progress >= 80) return 'bg-green-500';
    if (progress >= 60) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <span className="text-sm text-muted-foreground">{label}</span>
        <div className="flex items-center gap-1">
          <span className="font-semibold">{value}</span>
          {getTrendIcon()}
        </div>
      </div>
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div 
          className={`h-2 rounded-full transition-all duration-300 ${getProgressColor(progress)}`}
          style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
        />
      </div>
    </div>
  );
};

// Specific Kirkpatrick level cards
export const ReactionAnalysisCard: React.FC<{
  data: KirkpatrickReaction;
  className?: string;
}> = ({ data, className }) => {
  return (
    <KirkpatrickAnalysisCard
      level={1}
      data={data}
      title="Reaction Analysis"
      description="Learner satisfaction and engagement metrics"
      className={className}
    />
  );
};

export const LearningAnalysisCard: React.FC<{
  data: KirkpatrickLearning;
  className?: string;
}> = ({ data, className }) => {
  return (
    <KirkpatrickAnalysisCard
      level={2}
      data={data}
      title="Learning Analysis"
      description="Knowledge acquisition and skill development"
      className={className}
    />
  );
};

export const BehaviorAnalysisCard: React.FC<{
  data: KirkpatrickBehavior;
  className?: string;
}> = ({ data, className }) => {
  return (
    <KirkpatrickAnalysisCard
      level={3}
      data={data}
      title="Behavior Analysis"
      description="On-the-job application and behavior change"
      className={className}
    />
  );
};

export const ResultsAnalysisCard: React.FC<{
  data: KirkpatrickResults;
  className?: string;
}> = ({ data, className }) => {
  return (
    <KirkpatrickAnalysisCard
      level={4}
      data={data}
      title="Results Analysis"
      description="Business impact and ROI measurement"
      className={className}
    />
  );
};

export default KirkpatrickAnalysisCard;