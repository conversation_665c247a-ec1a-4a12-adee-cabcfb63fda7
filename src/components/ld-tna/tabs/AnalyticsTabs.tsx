import React from 'react';
import { Card } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { KirkpatrickLevelCard } from '../cards';
import { 
  TrendingUp, 
  TrendingDown, 
  Users, 
  Target, 
  DollarSign, 
  Clock, 
  Award, 
  BarChart3,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  Download,
  Calendar,
  Heart,
  Brain,
  Activity,
  BarChart
} from 'lucide-react';
import { useAnalyticsCalculations } from '@/hooks/useAnalyticsCalculations';

interface AnalyticsTabsProps {
  className?: string;
}

export const AnalyticsTabs: React.FC<AnalyticsTabsProps> = ({ className }) => {
  const { kirkpatrick, roi, effectiveness, trends, loading, error, recalculate } = useAnalyticsCalculations();

  if (loading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="flex items-center justify-center p-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Calculating analytics...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`space-y-6 ${className}`}>
        <Card className="p-6">
          <div className="text-center">
            <p className="text-destructive mb-4">Error loading analytics: {error}</p>
            <Button onClick={recalculate} variant="outline">
              Retry
            </Button>
          </div>
        </Card>
      </div>
    );
  }
  return (
    <Tabs value="kirkpatrick" onValueChange={() => {}} className={`w-full ${className || ''}`}>
      <TabsList>
        <TabsTrigger value="kirkpatrick">Kirkpatrick Analysis</TabsTrigger>
        <TabsTrigger value="roi">ROI Deep Dive</TabsTrigger>
        <TabsTrigger value="effectiveness">Program Effectiveness</TabsTrigger>
        <TabsTrigger value="trends">Trends & Insights</TabsTrigger>
      </TabsList>

      <TabsContent value="kirkpatrick" className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <KirkpatrickLevelCard
            level={1}
            title="Reaction"
            description="Learner satisfaction and engagement"
            score={kirkpatrick?.reaction.satisfactionScore ? Math.round((kirkpatrick.reaction.satisfactionScore / 5) * 100) : 0}
            icon={Heart}
            iconColor="text-pink-500"
            status={kirkpatrick?.reaction.satisfactionScore >= 4.5 ? "excellent" : kirkpatrick?.reaction.satisfactionScore >= 3.5 ? "good" : "needs_improvement"}
          />
          <KirkpatrickLevelCard
            level={2}
            title="Learning"
            description="Knowledge acquisition and skill development"
            score={kirkpatrick?.learning.knowledgeAcquisition ? Math.round(kirkpatrick.learning.knowledgeAcquisition) : 0}
            icon={Brain}
            iconColor="text-blue-500"
            status={kirkpatrick?.learning.knowledgeAcquisition >= 85 ? "excellent" : kirkpatrick?.learning.knowledgeAcquisition >= 70 ? "good" : "needs_improvement"}
          />
          <KirkpatrickLevelCard
            level={3}
            title="Behavior"
            description="On-the-job application of skills"
            score={kirkpatrick?.behavior.behaviorChange ? Math.round(kirkpatrick.behavior.behaviorChange) : 0}
            icon={Activity}
            iconColor="text-green-500"
            status={kirkpatrick?.behavior.behaviorChange >= 80 ? "excellent" : kirkpatrick?.behavior.behaviorChange >= 65 ? "good" : "needs_improvement"}
          />
          <KirkpatrickLevelCard
            level={4}
            title="Results"
            description="Business impact and ROI measurement"
            score={roi?.roiPercentage ? Math.round(roi.roiPercentage) : 0}
            icon={TrendingUp}
            iconColor="text-purple-500"
            status={roi?.roiPercentage >= 200 ? "excellent" : roi?.roiPercentage >= 100 ? "good" : "needs_improvement"}
          />
        </div>

        {/* Detailed Kirkpatrick Analysis */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="p-4">
            <h3 className="font-semibold mb-3">Level 1: Reaction Analysis</h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm">Overall Satisfaction</span>
                <span className="font-medium">4.6/5.0</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Content Relevance</span>
                <span className="font-medium">4.4/5.0</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Instructor Effectiveness</span>
                <span className="font-medium">4.7/5.0</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Engagement Level</span>
                <span className="font-medium">4.3/5.0</span>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <h3 className="font-semibold mb-3">Level 2: Learning Outcomes</h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm">Knowledge Retention</span>
                <span className="font-medium">84%</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Skill Demonstration</span>
                <span className="font-medium">78%</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Confidence Increase</span>
                <span className="font-medium">+32%</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Assessment Pass Rate</span>
                <span className="font-medium">91%</span>
              </div>
            </div>
          </Card>
        </div>
      </TabsContent>

      <TabsContent value="roi" className="space-y-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="p-4">
            <h3 className="font-semibold mb-3">ROI Calculation</h3>
            <div className="space-y-4">
              <div className="flex justify-between items-center p-3 bg-green-50 rounded">
                <span className="text-sm font-medium">Total Benefits</span>
                <span className="font-bold text-green-600">${(roi?.totalBenefits || 0).toLocaleString()}</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-red-50 rounded">
                <span className="text-sm font-medium">Total Costs</span>
                <span className="font-bold text-red-600">${(roi?.totalInvestment || 0).toLocaleString()}</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-blue-50 rounded">
                <span className="text-sm font-medium">Net Benefits</span>
                <span className="font-bold text-blue-600">${((roi?.totalBenefits || 0) - (roi?.totalInvestment || 0)).toLocaleString()}</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-purple-50 rounded">
                <span className="text-sm font-medium">ROI Percentage</span>
                <span className="font-bold text-purple-600">{Math.round(roi?.roiPercentage || 0)}%</span>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <h3 className="font-semibold mb-3">Cost Breakdown</h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm">Development Costs</span>
                <span className="font-medium">${Math.round((roi?.totalInvestment || 0) * 0.49).toLocaleString()}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Delivery Costs</span>
                <span className="font-medium">${Math.round((roi?.totalInvestment || 0) * 0.35).toLocaleString()}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Technology Platform</span>
                <span className="font-medium">${Math.round((roi?.totalInvestment || 0) * 0.10).toLocaleString()}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Evaluation & Assessment</span>
                <span className="font-medium">${Math.round((roi?.totalInvestment || 0) * 0.06).toLocaleString()}</span>
              </div>
            </div>
          </Card>
        </div>
      </TabsContent>

      <TabsContent value="effectiveness" className="space-y-4">
        <Card className="p-4">
          <h3 className="font-semibold mb-3">Program Effectiveness Metrics</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-muted/50 rounded">
              <BarChart className="h-8 w-8 mx-auto mb-2 text-blue-500" />
              <div className="text-2xl font-bold">{Math.round(effectiveness?.completionRate || 0)}%</div>
              <div className="text-sm text-muted-foreground">Completion Rate</div>
            </div>
            <div className="text-center p-4 bg-muted/50 rounded">
              <PieChart className="h-8 w-8 mx-auto mb-2 text-green-500" />
              <div className="text-2xl font-bold">{(effectiveness?.averageRating || 0).toFixed(1)}/5</div>
              <div className="text-sm text-muted-foreground">Avg. Rating</div>
            </div>
            <div className="text-center p-4 bg-muted/50 rounded">
              <LineChart className="h-8 w-8 mx-auto mb-2 text-purple-500" />
              <div className="text-2xl font-bold">+{Math.round(effectiveness?.performanceGain || 0)}%</div>
              <div className="text-sm text-muted-foreground">Performance Gain</div>
            </div>
          </div>
        </Card>
      </TabsContent>

      <TabsContent value="trends" className="space-y-4">
        <Card className="p-4">
          <h3 className="font-semibold mb-3">Trends & Insights</h3>
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-4">
              <div className="p-4 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">Competency Development Trend</h4>
                <div className="flex items-center justify-between">
                  <span className="text-2xl font-bold text-blue-700">{trends?.competencyDevelopmentTrend.change > 0 ? '+' : ''}{Math.round(trends?.competencyDevelopmentTrend.change || 0)}%</span>
                  <span className="text-sm text-blue-600">vs last quarter</span>
                </div>
                <p className="text-sm text-blue-600 mt-1">Average skill level improvement across all competencies</p>
              </div>
              
              <div className="p-4 bg-green-50 rounded-lg">
                <h4 className="font-medium text-green-900 mb-2">Training Completion Rate</h4>
                <div className="flex items-center justify-between">
                  <span className="text-2xl font-bold text-green-700">{Math.round(trends?.completionRateTrend.currentValue || 0)}%</span>
                  <span className="text-sm text-green-600">{trends?.completionRateTrend.change > 0 ? '+' : ''}{Math.round(trends?.completionRateTrend.change || 0)}% change</span>
                </div>
                <p className="text-sm text-green-600 mt-1">Employees completing assigned development activities</p>
              </div>
            </div>
            
            <div className="space-y-4">
              <div className="p-4 bg-orange-50 rounded-lg">
                <h4 className="font-medium text-orange-900 mb-2">Critical Skills Gap</h4>
                <div className="flex items-center justify-between">
                  <span className="text-2xl font-bold text-orange-700">{trends?.criticalSkillGaps.length || 0}</span>
                  <span className="text-sm text-orange-600">high priority</span>
                </div>
                <p className="text-sm text-orange-600 mt-1">Competencies requiring immediate attention</p>
              </div>
              
              <div className="p-4 bg-purple-50 rounded-lg">
                <h4 className="font-medium text-purple-900 mb-2">ROI on L&D Investment</h4>
                <div className="flex items-center justify-between">
                  <span className="text-2xl font-bold text-purple-700">{((roi?.roiPercentage || 0) / 100).toFixed(1)}x</span>
                  <span className="text-sm text-purple-600">return ratio</span>
                </div>
                <p className="text-sm text-purple-600 mt-1">Value generated per dollar invested in training</p>
              </div>
            </div>
          </div>
          
          <div className="mt-6 pt-4 border-t">
            <h4 className="font-medium mb-3">Key Insights</h4>
            <ul className="space-y-2 text-sm text-muted-foreground">
              <li>• {trends?.topPerformingCompetency || 'Leadership'} competencies show the highest engagement rates ({Math.round(effectiveness?.completionRate || 0)}%)</li>
              <li>• {trends?.fastestGrowingSkill || 'Technical skills'} development has accelerated by {Math.round(trends?.competencyDevelopmentTrend.change || 0)}% this quarter</li>
              <li>• {trends?.criticalSkillGaps[0] || 'Cross-functional collaboration'} skills need focused attention</li>
              <li>• Mobile learning adoption increased by {Math.round(trends?.mobileAdoptionIncrease || 45)}% among remote workers</li>
            </ul>
          </div>
        </Card>
      </TabsContent>
    </Tabs>
  );
};

export default AnalyticsTabs;