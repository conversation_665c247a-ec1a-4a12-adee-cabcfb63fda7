import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import { Search, Filter, Download, Star, Clock, Users, X } from 'lucide-react';
import { TemplateCard } from '../cards';
import { Template } from '../common/types';
import { AssessmentToolCard, BestPracticeCard, CaseStudyCard } from './components/library';
import { advancedSearchService, SearchQuery, SearchResult } from '@/lib/services/advancedSearchService';

interface TemplateWithId extends Template {
  id: string;
}

interface LibraryTabsProps {
  className?: string;
}

export const LibraryTabs: React.FC<LibraryTabsProps> = ({ className }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedFormat, setSelectedFormat] = useState('all');
  const [filteredTemplates, setFilteredTemplates] = useState<TemplateWithId[]>([]);
  const [filteredResources, setFilteredResources] = useState<any[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const { toast } = useToast();

  const templates: TemplateWithId[] = [
    {
      id: '1',
      name: 'Leadership Development Program',
      description: 'Comprehensive 6-month leadership development curriculum',
      category: 'Leadership',
      format: 'PDF',
      rating: 4.8,
      downloads: 1250
    },
    {
      id: '2',
      name: 'Technical Skills Assessment',
      description: 'Multi-level technical competency evaluation framework',
      category: 'Technical',
      format: 'Excel',
      rating: 4.6,
      downloads: 890
    },
    {
      id: '3',
      name: 'Customer Service Excellence',
      description: 'Customer-centric service delivery training program',
      category: 'Customer Service',
      format: 'PDF',
      rating: 4.7,
      downloads: 650
    }
  ];

  const resources = [
    {
      id: '1',
      title: 'ADDIE Model Implementation Guide',
      type: 'Guide',
      format: 'PDF',
      size: '2.4 MB',
      downloads: 2100,
      rating: 4.9
    },
    {
      id: '2',
      title: 'Kirkpatrick Evaluation Templates',
      type: 'Template',
      format: 'Excel',
      size: '1.8 MB',
      downloads: 1850,
      rating: 4.7
    },
    {
      id: '3',
      title: 'Learning Objectives Taxonomy',
      type: 'Reference',
      format: 'PDF',
      size: '950 KB',
      downloads: 1200,
      rating: 4.5
    }
  ];

  // Initialize search index and filtered data
  useEffect(() => {
    const initializeSearch = async () => {
      // Index templates
      for (const template of templates) {
        await advancedSearchService.indexItem({
          id: template.id,
          title: template.name,
          content: template.description,
          type: 'template',
          tags: [template.category, template.format],
          mentions: [],
          createdAt: new Date(),
          updatedAt: new Date(),
          metadata: {
            category: template.category,
            format: template.format,
            rating: template.rating,
            downloads: template.downloads
          }
        });
      }

      // Index resources
      for (const resource of resources) {
        await advancedSearchService.indexItem({
          id: resource.id,
          title: resource.title,
          content: `${resource.type} resource in ${resource.format} format`,
          type: 'resource',
          tags: [resource.type, resource.format],
          mentions: [],
          createdAt: new Date(),
          updatedAt: new Date(),
          metadata: {
            type: resource.type,
            format: resource.format,
            size: resource.size,
            rating: resource.rating,
            downloads: resource.downloads
          }
        });
      }

      // Initialize filtered data
      setFilteredTemplates(templates);
      setFilteredResources(resources);
    };

    initializeSearch();
  }, []);

  // Search function
  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      setFilteredTemplates(templates);
      setFilteredResources(resources);
      return;
    }

    setIsSearching(true);
    try {
      const query: SearchQuery = {
        text: searchQuery,
        filters: {
          category: selectedCategory !== 'all' ? selectedCategory : undefined,
          format: selectedFormat !== 'all' ? selectedFormat : undefined
        },
        sortBy: 'relevance',
        sortOrder: 'desc',
        page: 1,
        limit: 50
      };

      const searchResponse = await advancedSearchService.search(query);
      
      // Filter results by type
      const templateResults = searchResponse.results.filter(r => r.type === 'template');
      const resourceResults = searchResponse.results.filter(r => r.type === 'resource');

      // Map search results back to original format
      const matchedTemplates = templates.filter(t => 
        templateResults.some(r => r.id === t.id)
      );
      const matchedResources = resources.filter(r => 
        resourceResults.some(sr => sr.id === r.id)
      );

      setFilteredTemplates(matchedTemplates);
      setFilteredResources(matchedResources);

      toast({
        title: "Search Complete",
        description: `Found ${matchedTemplates.length} templates and ${matchedResources.length} resources`,
      });
    } catch (error) {
      toast({
        title: "Search Failed",
        description: "Failed to search content. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSearching(false);
    }
  };

  // Filter function
  const handleFilter = () => {
    let filtered = templates;

    if (selectedCategory !== 'all') {
      filtered = filtered.filter(t => t.category.toLowerCase().includes(selectedCategory.toLowerCase()));
    }

    if (selectedFormat !== 'all') {
      filtered = filtered.filter(t => t.format.toLowerCase() === selectedFormat.toLowerCase());
    }

    setFilteredTemplates(filtered);

    // Apply same filters to resources
    let filteredRes = resources;
    if (selectedFormat !== 'all') {
      filteredRes = filteredRes.filter(r => r.format.toLowerCase() === selectedFormat.toLowerCase());
    }
    setFilteredResources(filteredRes);

    toast({
      title: "Filters Applied",
      description: `Showing ${filtered.length} templates and ${filteredRes.length} resources`,
    });
  };

  // Clear filters
  const clearFilters = () => {
    setSearchQuery('');
    setSelectedCategory('all');
    setSelectedFormat('all');
    setFilteredTemplates(templates);
    setFilteredResources(resources);
  };

  // Trigger search on query change
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      handleSearch();
    }, 300);
    return () => clearTimeout(timeoutId);
  }, [searchQuery]);

  // Trigger filter on filter change
  useEffect(() => {
    if (selectedCategory !== 'all' || selectedFormat !== 'all') {
      handleFilter();
    }
  }, [selectedCategory, selectedFormat]);

  return (
    <Tabs value="templates" onValueChange={() => {}} className={`w-full ${className || ''}`}>
      <TabsList>
        <TabsTrigger value="templates">Program Templates</TabsTrigger>
        <TabsTrigger value="resources">Resources</TabsTrigger>
        <TabsTrigger value="best-practices">Best Practices</TabsTrigger>
        <TabsTrigger value="case-studies">Case Studies</TabsTrigger>
      </TabsList>

      <TabsContent value="templates" className="space-y-4">
        {/* Search and Filter Bar */}
        <Card className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search templates..."
                className="pl-10"
              />
            </div>
            <Button variant="outline">
              <Filter className="h-4 w-4 mr-2" />
              Filter
            </Button>
          </div>
        </Card>

        {/* Templates Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {templates.map((template) => (
            <TemplateCard
              key={template.id}
              template={template}
            />
          ))}
        </div>
      </TabsContent>

      <TabsContent value="resources" className="space-y-4">
        {/* Search Bar */}
        <Card className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search resources..."
                className="pl-10"
              />
            </div>
            <Button variant="outline">
              <Filter className="h-4 w-4 mr-2" />
              Filter by Type
            </Button>
          </div>
        </Card>

        {/* Resources List */}
        <div className="space-y-3">
          {resources.map((resource) => (
            <Card key={resource.id} className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <h3 className="font-semibold">{resource.title}</h3>
                    <Badge variant="outline">{resource.type}</Badge>
                    <Badge variant="secondary">{resource.format}</Badge>
                  </div>
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <span>{resource.size}</span>
                    <div className="flex items-center gap-1">
                      <Download className="h-3 w-3" />
                      <span>{resource.downloads.toLocaleString()}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                      <span>{resource.rating}</span>
                    </div>
                  </div>
                </div>
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-2" />
                  Download
                </Button>
              </div>
            </Card>
          ))}
        </div>
      </TabsContent>

      <TabsContent value="best-practices" className="space-y-4">
        <Card className="p-4">
          <h3 className="font-semibold mb-3">Learning & Development Best Practices</h3>
          <div className="space-y-4">
            <div className="border-l-4 border-blue-500 pl-4">
              <h4 className="font-medium mb-1">Needs Analysis</h4>
              <p className="text-sm text-muted-foreground">
                Conduct thorough organizational and individual needs assessments before designing any learning intervention.
              </p>
            </div>
            <div className="border-l-4 border-green-500 pl-4">
              <h4 className="font-medium mb-1">Adult Learning Principles</h4>
              <p className="text-sm text-muted-foreground">
                Apply andragogy principles: relevance, experience-based learning, problem-solving orientation, and immediate application.
              </p>
            </div>
            <div className="border-l-4 border-purple-500 pl-4">
              <h4 className="font-medium mb-1">Blended Learning Approach</h4>
              <p className="text-sm text-muted-foreground">
                Combine multiple delivery methods (online, face-to-face, experiential) for maximum effectiveness.
              </p>
            </div>
            <div className="border-l-4 border-orange-500 pl-4">
              <h4 className="font-medium mb-1">Continuous Evaluation</h4>
              <p className="text-sm text-muted-foreground">
                Implement ongoing assessment and feedback mechanisms throughout the learning journey.
              </p>
            </div>
          </div>
        </Card>
      </TabsContent>

      <TabsContent value="case-studies" className="space-y-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          <Card className="p-4">
            <div className="flex items-start gap-3 mb-3">
              <div className="p-2 bg-blue-100 rounded">
                <Users className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <h3 className="font-semibold">Global Tech Company</h3>
                <p className="text-sm text-muted-foreground">Leadership Development Initiative</p>
              </div>
            </div>
            <p className="text-sm mb-3">
              Implemented a comprehensive leadership development program across 15 countries, 
              resulting in 40% improvement in leadership effectiveness scores.
            </p>
            <div className="flex items-center gap-4 text-xs text-muted-foreground">
              <div className="flex items-center gap-1">
                <Clock className="h-3 w-3" />
                <span>12 months</span>
              </div>
              <div className="flex items-center gap-1">
                <Users className="h-3 w-3" />
                <span>500+ participants</span>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-start gap-3 mb-3">
              <div className="p-2 bg-green-100 rounded">
                <Star className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <h3 className="font-semibold">Healthcare Organization</h3>
                <p className="text-sm text-muted-foreground">Clinical Skills Enhancement</p>
              </div>
            </div>
            <p className="text-sm mb-3">
              Developed a simulation-based training program that reduced medical errors by 35% 
              and improved patient satisfaction scores.
            </p>
            <div className="flex items-center gap-4 text-xs text-muted-foreground">
              <div className="flex items-center gap-1">
                <Clock className="h-3 w-3" />
                <span>6 months</span>
              </div>
              <div className="flex items-center gap-1">
                <Users className="h-3 w-3" />
                <span>200+ participants</span>
              </div>
            </div>
          </Card>
        </div>
      </TabsContent>
    </Tabs>
  );
};

export default LibraryTabs;