import React, { useState } from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  AnalysisPhaseCard,
  DesignPhaseCard,
  DevelopmentPhaseCard,
  ImplementationPhaseCard,
  EvaluationPhaseCard
} from './components/addie';

interface ADDIETabsProps {
  className?: string;
  defaultTab?: string;
  onTabChange?: (tab: string) => void;
}

export const ADDIETabs: React.FC<ADDIETabsProps> = ({ 
  className, 
  defaultTab = "analysis",
  onTabChange 
}) => {
  const [activeTab, setActiveTab] = useState(defaultTab);

  const handleTabChange = (value: string) => {
    setActiveTab(value);
    onTabChange?.(value);
  };

  return (
    <Tabs value={activeTab} onValueChange={handleTabChange} className={`w-full ${className || ''}`}>
      <TabsList className="grid w-full grid-cols-5">
        <TabsTrigger value="analysis">Analysis</TabsTrigger>
        <TabsTrigger value="design">Design</TabsTrigger>
        <TabsTrigger value="development">Development</TabsTrigger>
        <TabsTrigger value="implementation">Implementation</TabsTrigger>
        <TabsTrigger value="evaluation">Evaluation</TabsTrigger>
      </TabsList>

      <TabsContent value="analysis" className="space-y-4">
        <AnalysisPhaseCard />
      </TabsContent>

      <TabsContent value="design" className="space-y-4">
        <DesignPhaseCard />
      </TabsContent>

      <TabsContent value="development" className="space-y-4">
        <DevelopmentPhaseCard />
      </TabsContent>

      <TabsContent value="implementation" className="space-y-4">
        <ImplementationPhaseCard />
      </TabsContent>

      <TabsContent value="evaluation" className="space-y-4">
        <EvaluationPhaseCard />
      </TabsContent>
    </Tabs>
  );
};

export default ADDIETabs;