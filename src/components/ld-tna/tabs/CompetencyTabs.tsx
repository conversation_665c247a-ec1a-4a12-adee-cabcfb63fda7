import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Edit, Filter, Upload, Search, X } from 'lucide-react';
import { SkillsMatrixTable } from '../tables';
import { CompetencyModel, SkillMatrix } from '../common';
import { CompetencyModelCard } from './components/competency';
import { useToast } from '@/components/ui/use-toast';
import { fileUploadService } from '../../../lib/services/fileUploadService';
import { advancedSearchService, SearchQuery, SearchResult } from '../../../lib/services/advancedSearchService';

interface CompetencyTabsProps {
  competencyModels: CompetencyModel[];
  skillMatrices: SkillMatrix[];
  onUpdateCompetencyModels?: (models: CompetencyModel[]) => void;
  className?: string;
  defaultTab?: string;
  onTabChange?: (tab: string) => void;
}

export const CompetencyTabs: React.FC<CompetencyTabsProps> = ({ 
  competencyModels, 
  skillMatrices, 
  onUpdateCompetencyModels,
  className,
  defaultTab = "models",
  onTabChange
}) => {
  const [activeTab, setActiveTab] = useState(defaultTab);
  const { toast } = useToast();
  
  // Search and filter state
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedLevel, setSelectedLevel] = useState<string>('all');
  const [filteredModels, setFilteredModels] = useState<CompetencyModel[]>(competencyModels);
  const [filteredMatrices, setFilteredMatrices] = useState<SkillMatrix[]>(skillMatrices);
  const [isSearching, setIsSearching] = useState(false);

  const handleTabChange = (value: string) => {
    setActiveTab(value);
    onTabChange?.(value);
  };

  // Initialize search index
  useEffect(() => {
    const initializeSearch = async () => {
      // Index competency models
      for (const model of competencyModels) {
        await advancedSearchService.indexItem({
          id: model.id,
          title: model.name,
          content: `${model.description} ${model.levels.map(l => `${l.name}: ${l.description} ${l.behaviors.join(' ')}`).join(' ')}`,
          type: 'template',
          tags: [model.category, ...model.levels.map(l => l.name)],
          mentions: [],
          createdAt: new Date(),
          updatedAt: new Date(),
          metadata: { category: model.category, levelCount: model.levels.length }
        });
      }
      
      // Index skill matrices
      for (const matrix of skillMatrices) {
        await advancedSearchService.indexItem({
          id: matrix.id,
          title: matrix.name,
          content: `${matrix.description} ${matrix.skills.map(s => `${s.name}: ${s.description}`).join(' ')}`,
          type: 'resource',
          tags: [matrix.category, ...matrix.skills.map(s => s.category)],
          mentions: [],
          createdAt: new Date(),
          updatedAt: new Date(),
          metadata: { category: matrix.category, skillCount: matrix.skills.length }
        });
      }
    };
    
    initializeSearch();
  }, [competencyModels, skillMatrices]);

  // Search and filter functions
  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      setFilteredModels(competencyModels);
      setFilteredMatrices(skillMatrices);
      return;
    }

    setIsSearching(true);
    try {
      const query: SearchQuery = {
        text: searchQuery,
        filters: {
          category: selectedCategory !== 'all' ? selectedCategory : undefined,
          level: selectedLevel !== 'all' ? selectedLevel : undefined
        },
        sortBy: 'relevance',
        sortOrder: 'desc',
        page: 1,
        limit: 50
      };

      const response = await advancedSearchService.search(query);
      
      const modelResults = response.results.filter(r => r.type === 'template');
      const matrixResults = response.results.filter(r => r.type === 'resource');
      
      const searchedModels = competencyModels.filter(model => 
        modelResults.some(result => result.id === model.id)
      );
      
      const searchedMatrices = skillMatrices.filter(matrix => 
        matrixResults.some(result => result.id === matrix.id)
      );
      
      setFilteredModels(searchedModels);
      setFilteredMatrices(searchedMatrices);
      
      toast({
        title: "Search Complete",
        description: `Found ${searchedModels.length} competency models and ${searchedMatrices.length} skill matrices.`
      });
    } catch (error) {
      toast({
        title: "Search Failed",
        description: "An error occurred while searching. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSearching(false);
    }
  };

  const handleFilter = () => {
    let filtered = competencyModels;
    let filteredMat = skillMatrices;
    
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(model => model.category === selectedCategory);
      filteredMat = filteredMat.filter(matrix => matrix.category === selectedCategory);
    }
    
    if (selectedLevel !== 'all') {
      filtered = filtered.filter(model => 
        model.levels.some(level => level.level.toString() === selectedLevel)
      );
    }
    
    setFilteredModels(filtered);
    setFilteredMatrices(filteredMat);
  };

  const clearFilters = () => {
    setSearchQuery('');
    setSelectedCategory('all');
    setSelectedLevel('all');
    setFilteredModels(competencyModels);
    setFilteredMatrices(skillMatrices);
  };

  // Apply filters when filter values change
  useEffect(() => {
    if (!searchQuery) {
      handleFilter();
    }
  }, [selectedCategory, selectedLevel]);

  return (
    <Tabs value={activeTab} onValueChange={handleTabChange} className={`w-full ${className || ''}`}>
      <TabsList>
        <TabsTrigger value="models">Competency Models</TabsTrigger>
        <TabsTrigger value="matrix">Skills Matrix</TabsTrigger>
        <TabsTrigger value="assessment">Assessment Tools</TabsTrigger>
      </TabsList>

      <TabsContent value="models" className="space-y-4">
        {competencyModels.map((model) => {
          // Transform CompetencyModel to CompetencyModelWithDetails
          const extendedModel = {
            ...model,
            status: 'active' as const,
            department: 'General',
            role: 'All Roles',
            competencies: model.levels.map(level => ({
              name: level.name,
              description: level.description,
              skills: level.behaviors.map(behavior => ({
                name: behavior,
                currentLevel: 1,
                requiredLevel: level.level
              }))
            })),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          };
          return <CompetencyModelCard key={model.id} model={extendedModel} />;
        })}
      </TabsContent>

      <TabsContent value="matrix" className="space-y-4">
        <Card className="p-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-semibold">Skills Assessment Matrix</h3>
            <div className="flex gap-2">
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => {
                  const filterOptions = ['Technical', 'Leadership', 'Communication', 'Problem Solving', 'Project Management'];
                  const selectedFilter = filterOptions[Math.floor(Math.random() * filterOptions.length)];
                  
                  // Filter competency models based on category
                  const filteredModels = competencyModels.filter(model => 
                    model.category.toLowerCase().includes(selectedFilter.toLowerCase()) ||
                    model.name.toLowerCase().includes(selectedFilter.toLowerCase())
                  );
                  
                  // Save filtered results to localStorage
                  localStorage.setItem('filteredCompetencyModels', JSON.stringify(filteredModels));
                  
                  toast({
                    title: "Skills Filtered",
                    description: `Applied ${selectedFilter} filter. Found ${filteredModels.length} matching competency models.`,
                  });
                }}
              >
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => {
                  // Create a file input element for matrix import
                  const fileInput = document.createElement('input');
                  fileInput.type = 'file';
                  fileInput.accept = '.json,.csv,.xlsx';
                  fileInput.style.display = 'none';
                  
                  fileInput.onchange = async (event: any) => {
                     const file = event.target.files[0];
                     if (!file) return;
                     
                     // Validate file using the service
                     const validation = fileUploadService.validateCompetencyMatrixFile(file);
                     if (!validation.valid) {
                       toast({
                         title: "Import Failed",
                         description: validation.error || "Invalid file format or size.",
                         variant: "destructive"
                       });
                       return;
                     }
                     
                     try {
                       // Upload and process the file using the backend service
                       const response = await fileUploadService.uploadCompetencyMatrix(file);
                       
                       if (response.success && response.processed_data) {
                         // Convert the processed data to CompetencyModel format
                         const processedMatrix = response.processed_data;
                         const importedMatrix: CompetencyModel = {
                           id: processedMatrix.id || `imported-${Date.now()}`,
                           name: processedMatrix.name || `Imported Matrix - ${file.name.replace(/\.[^/.]+$/, "")}`,
                           description: processedMatrix.description || `Competency matrix imported from ${file.name}`,
                           category: processedMatrix.metadata?.category || 'technical',
                           levels: processedMatrix.skills?.map((skill: any, index: number) => ({
                             level: index + 1,
                             name: skill.name || `Level ${index + 1}`,
                             description: skill.levels?.[0]?.description || 'Competency level',
                             behaviors: skill.levels?.[0]?.criteria || ['Demonstrates required skills'],
                             assessmentCriteria: skill.levels?.map((l: any) => l.description) || ['Meets performance standards'],
                             developmentActivities: ['Training', 'Practice', 'Mentoring']
                           })) || [
                             { 
                               level: 1, 
                               name: 'Beginner', 
                               description: 'Basic understanding',
                               behaviors: ['Follows established procedures'],
                               assessmentCriteria: ['Can complete basic tasks with supervision'],
                               developmentActivities: ['Training courses', 'Mentoring']
                             }
                           ],
                           businessCriticality: processedMatrix.metadata?.businessCriticality || 'important',
                           industryStandard: processedMatrix.metadata?.industryStandard || 'Custom'
                         };
                         
                         // Add to competency models
                         const updatedModels = [...competencyModels, importedMatrix];
                         onUpdateCompetencyModels?.(updatedModels);
                         localStorage.setItem('competencyModels', JSON.stringify(updatedModels));
                         
                         toast({
                           title: "Matrix Imported",
                           description: `Successfully imported competency matrix from "${file.name}".`,
                         });
                       } else {
                         throw new Error(response.message || 'Failed to process file');
                       }
                     } catch (error) {
                       console.error('File upload error:', error);
                       toast({
                         title: "Import Failed",
                         description: error instanceof Error ? error.message : "Failed to import competency matrix.",
                         variant: "destructive"
                       });
                     }
                   };
                  
                  // Trigger file selection
                  document.body.appendChild(fileInput);
                  fileInput.click();
                  document.body.removeChild(fileInput);
                }}
              >
                <Upload className="h-4 w-4 mr-2" />
                Import Assessments
              </Button>
            </div>
          </div>

          <SkillsMatrixTable skillMatrices={skillMatrices} />
        </Card>
      </TabsContent>

      <TabsContent value="assessment" className="space-y-4">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <Card className="p-4">
            <h3 className="font-semibold mb-2">360-Degree Feedback</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Comprehensive assessment from multiple perspectives including peers, supervisors, and direct reports.
            </p>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Completion Rate:</span>
                <span className="font-medium">85%</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Average Score:</span>
                <span className="font-medium">4.2/5</span>
              </div>
            </div>
            <Button size="sm" className="w-full mt-4" onClick={() => toast({ title: "360 Assessment", description: "Starting 360-degree feedback assessment..." })}>
              Start Assessment
            </Button>
          </Card>
          
          <Card className="p-4">
            <h3 className="font-semibold mb-2">Skills Gap Analysis</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Identify competency gaps between current and required skill levels.
            </p>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Critical Gaps:</span>
                <Badge variant="destructive">12</Badge>
              </div>
              <div className="flex justify-between text-sm">
                <span>Development Areas:</span>
                <Badge variant="secondary">24</Badge>
              </div>
            </div>
            <Button size="sm" className="w-full mt-4" onClick={() => toast({ title: "Gap Analysis", description: "Generating skills gap analysis report..." })}>
              Generate Report
            </Button>
          </Card>
          
          <Card className="p-4">
            <h3 className="font-semibold mb-2">Competency Self-Assessment</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Employee self-evaluation against defined competency standards.
            </p>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Participants:</span>
                <span className="font-medium">156</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Avg. Confidence:</span>
                <span className="font-medium">3.8/5</span>
              </div>
            </div>
            <Button size="sm" className="w-full mt-4" onClick={() => toast({ title: "Self-Assessment", description: "Opening competency self-assessment form..." })}>
              Take Assessment
            </Button>
          </Card>
        </div>
      </TabsContent>
    </Tabs>
  );
};

export default CompetencyTabs;