import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/components/ui/use-toast';
import { 
  Users, 
  MessageSquare, 
  FileText, 
  Calendar,
  Plus,
  Send,
  Reply,
  Share,
  Clock,
  MapPin,
  Video
} from 'lucide-react';
import { ProjectCard, DiscussionCard, ResourceCard, EventCard } from './components/collaboration';

interface CollaborationTabsProps {
  className?: string;
}

export const CollaborationTabs: React.FC<CollaborationTabsProps> = ({ className }) => {
  const [collaborationTab, setCollaborationTab] = useState('projects');
  const [newDiscussion, setNewDiscussion] = useState('');
  const [newResource, setNewResource] = useState({ title: '', description: '', url: '' });
  const [showResourceForm, setShowResourceForm] = useState(false);
  const [errors, setErrors] = useState({ discussion: '', resource: { title: '', url: '' } });
  const [discussionsList, setDiscussionsList] = useState<any[]>([]);
  const [resourcesList, setResourcesList] = useState<any[]>([]);
  const [projectsList, setProjectsList] = useState<any[]>([]);
  const { toast } = useToast();

  // Load data from localStorage on component mount
  useEffect(() => {
    const savedDiscussions = localStorage.getItem('collaboration-discussions');
    const savedResources = localStorage.getItem('collaboration-resources');
    const savedProjects = localStorage.getItem('collaboration-projects');
    
    if (savedDiscussions) {
      setDiscussionsList(JSON.parse(savedDiscussions));
    } else {
      // Set default discussions if none saved
      const defaultDiscussions = [
        {
          id: '1',
          title: 'Best Practices for Remote Learning',
          author: 'Emily Davis',
          avatar: '/avatars/emily.jpg',
          timestamp: '2 hours ago',
          replies: 8,
          content: 'What are your experiences with remote learning effectiveness?'
        },
        {
          id: '2',
          title: 'Microlearning Implementation Strategies',
          author: 'David Wilson',
          avatar: '/avatars/david.jpg',
          timestamp: '5 hours ago',
          replies: 12,
          content: 'Looking for insights on breaking down complex topics into micro-modules.'
        }
      ];
      setDiscussionsList(defaultDiscussions);
    }
    
    if (savedResources) {
      setResourcesList(JSON.parse(savedResources));
    } else {
      // Set default resources if none saved
      const defaultResources = [
        {
          id: '1',
          title: 'Learning Analytics Dashboard Template',
          sharedBy: 'Alex Thompson',
          type: 'Template',
          downloads: 45,
          timestamp: '1 day ago'
        },
        {
          id: '2',
          title: 'Instructional Design Checklist',
          sharedBy: 'Maria Garcia',
          type: 'Checklist',
          downloads: 67,
          timestamp: '3 days ago'
        }
      ];
      setResourcesList(defaultResources);
    }
    
    if (savedProjects) {
      setProjectsList(JSON.parse(savedProjects));
    } else {
      // Set default projects if none saved
      const defaultProjects = [
        {
          id: '1',
          title: 'Digital Transformation Training',
          description: 'Comprehensive program for digital skills development',
          status: 'active',
          members: 12,
          progress: 65,
          deadline: '2024-03-15',
          lead: 'Sarah Johnson'
        },
        {
          id: '2',
          title: 'Leadership Competency Framework',
          description: 'Developing new leadership assessment criteria',
          status: 'planning',
          members: 8,
          progress: 25,
          deadline: '2024-04-01',
          lead: 'Michael Chen'
        }
      ];
      setProjectsList(defaultProjects);
    }
  }, []);

  const validateDiscussion = (text: string) => {
    if (!text.trim()) {
      return 'Discussion content is required';
    }
    if (text.trim().length < 10) {
      return 'Discussion must be at least 10 characters long';
    }
    if (text.trim().length > 500) {
      return 'Discussion must be less than 500 characters';
    }
    return '';
  };

  const validateUrl = (url: string) => {
    if (!url.trim()) {
      return 'URL is required';
    }
    const urlPattern = /^(https?:\/\/)?(www\.)?[a-zA-Z0-9-]+(\.[a-zA-Z]{2,})+(\/[^\s]*)?$/;
    if (!urlPattern.test(url.trim())) {
      return 'Please enter a valid URL';
    }
    return '';
  };

  const handleAddDiscussion = () => {
    const discussionError = validateDiscussion(newDiscussion);
    
    if (discussionError) {
      setErrors(prev => ({ ...prev, discussion: discussionError }));
      toast({
        title: "Validation Error",
        description: discussionError,
        variant: "destructive"
      });
      return;
    }

    const newDiscussionItem = {
      id: Date.now().toString(),
      title: newDiscussion.trim(),
      author: 'Current User',
      avatar: '/avatars/default.jpg',
      timestamp: 'Just now',
      replies: 0,
      content: newDiscussion.trim()
    };
    const updatedDiscussions = [newDiscussionItem, ...discussionsList];
    setDiscussionsList(updatedDiscussions);
    localStorage.setItem('collaboration-discussions', JSON.stringify(updatedDiscussions));
    setNewDiscussion('');
    setErrors(prev => ({ ...prev, discussion: '' }));
    toast({
      title: "Discussion Posted",
      description: "Your discussion has been added successfully.",
    });
  };

  const handleAddResource = () => {
    const titleError = !newResource.title.trim() ? 'Title is required' : 
                      newResource.title.trim().length < 3 ? 'Title must be at least 3 characters' :
                      newResource.title.trim().length > 100 ? 'Title must be less than 100 characters' : '';
    
    const urlError = validateUrl(newResource.url);
    
    const newErrors = { title: titleError, url: urlError };
    setErrors(prev => ({ ...prev, resource: newErrors }));
    
    if (titleError || urlError) {
      toast({
        title: "Validation Error",
        description: titleError || urlError,
        variant: "destructive"
      });
      return;
    }

    const newResourceItem = {
      id: Date.now().toString(),
      title: newResource.title.trim(),
      sharedBy: 'Current User',
      type: 'Resource',
      downloads: 0,
      timestamp: 'Just now',
      description: newResource.description.trim(),
      url: newResource.url.trim()
    };
    const updatedResources = [newResourceItem, ...resourcesList];
    setResourcesList(updatedResources);
    localStorage.setItem('collaboration-resources', JSON.stringify(updatedResources));
    setNewResource({ title: '', description: '', url: '' });
    setErrors(prev => ({ ...prev, resource: { title: '', url: '' } }));
    setShowResourceForm(false);
    toast({
      title: "Resource Shared",
      description: "Your resource has been shared successfully.",
    });
  };

  const handleJoinProject = (projectId: string) => {
    const updatedProjects = projectsList.map(project => 
      project.id === projectId 
        ? { ...project, members: project.members + 1 }
        : project
    );
    setProjectsList(updatedProjects);
    localStorage.setItem('collaboration-projects', JSON.stringify(updatedProjects));
    toast({
      title: "Project Joined",
      description: "You have successfully joined the project.",
    });
  };

  const handleReplyToDiscussion = (discussionId: string) => {
    const updatedDiscussions = discussionsList.map(discussion => 
      discussion.id === discussionId 
        ? { ...discussion, replies: discussion.replies + 1 }
        : discussion
    );
    setDiscussionsList(updatedDiscussions);
    localStorage.setItem('collaboration-discussions', JSON.stringify(updatedDiscussions));
    toast({
      title: "Reply Added",
      description: "Your reply has been posted to the discussion.",
    });
  };

  const projects = [
    {
      id: '1',
      title: 'Digital Transformation Training',
      description: 'Comprehensive program for digital skills development',
      status: 'active',
      members: 12,
      progress: 65,
      deadline: '2024-03-15',
      lead: 'Sarah Johnson'
    },
    {
      id: '2',
      title: 'Leadership Competency Framework',
      description: 'Developing new leadership assessment criteria',
      status: 'planning',
      members: 8,
      progress: 25,
      deadline: '2024-04-01',
      lead: 'Michael Chen'
    }
  ];

  const discussions = [
    {
      id: '1',
      title: 'Best Practices for Remote Learning',
      author: 'Emily Davis',
      avatar: '/avatars/emily.jpg',
      timestamp: '2 hours ago',
      replies: 8,
      content: 'What are your experiences with remote learning effectiveness?'
    },
    {
      id: '2',
      title: 'Microlearning Implementation Strategies',
      author: 'David Wilson',
      avatar: '/avatars/david.jpg',
      timestamp: '5 hours ago',
      replies: 12,
      content: 'Looking for insights on breaking down complex topics into micro-modules.'
    }
  ];

  const resources = [
    {
      id: '1',
      title: 'Learning Analytics Dashboard Template',
      sharedBy: 'Alex Thompson',
      type: 'Template',
      downloads: 45,
      timestamp: '1 day ago'
    },
    {
      id: '2',
      title: 'Instructional Design Checklist',
      sharedBy: 'Maria Garcia',
      type: 'Checklist',
      downloads: 67,
      timestamp: '3 days ago'
    }
  ];

  const events = [
    {
      id: '1',
      title: 'L&D Strategy Review',
      date: '2024-02-15',
      time: '10:00 AM',
      duration: '2 hours',
      attendees: 8,
      type: 'meeting',
      status: 'confirmed',
      location: 'Conference Room A'
    },
    {
      id: '2',
      title: 'Quarterly Training Planning',
      date: '2024-02-20',
      time: '2:00 PM',
      duration: '3 hours',
      attendees: 15,
      type: 'workshop',
      status: 'pending',
      location: 'Virtual'
    }
  ];

  return (
    <Tabs 
      value={collaborationTab} 
      onValueChange={setCollaborationTab} 
      className={`w-full ${className || ''}`}
    >
      <TabsList>
        <TabsTrigger value="projects">Active Projects</TabsTrigger>
        <TabsTrigger value="discussions">Discussions</TabsTrigger>
        <TabsTrigger value="resources">Shared Resources</TabsTrigger>
        <TabsTrigger value="calendar">Team Calendar</TabsTrigger>
      </TabsList>

      <TabsContent value="projects" className="space-y-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          {projectsList.map((project) => (
            <Card key={project.id} className="p-4">
              <div className="flex items-start justify-between mb-3">
                <div>
                  <h3 className="font-semibold">{project.title}</h3>
                  <p className="text-sm text-muted-foreground mt-1">{project.description}</p>
                </div>
                <Badge variant={project.status === 'active' ? 'default' : 'secondary'}>
                  {project.status}
                </Badge>
              </div>
              
              <div className="space-y-3">
                <div className="flex items-center justify-between text-sm">
                  <span>Progress</span>
                  <span>{project.progress}%</span>
                </div>
                <div className="w-full bg-muted rounded-full h-2">
                  <div 
                    className="bg-primary h-2 rounded-full transition-all" 
                    style={{ width: `${project.progress}%` }}
                  />
                </div>
                
                <div className="flex items-center justify-between text-sm text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <Users className="h-3 w-3" />
                    <span>{project.members} members</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    <span>{project.deadline}</span>
                  </div>
                </div>
                
                <div className="flex items-center justify-between pt-2">
                  <span className="text-sm">Lead: {project.lead}</span>
                  <Button 
                    size="sm" 
                    onClick={() => handleJoinProject(project.id)}
                  >
                    Join Project
                  </Button>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </TabsContent>

      <TabsContent value="discussions" className="space-y-4">
        {/* New Discussion Input */}
        <Card className="p-4">
          <div className="space-y-3">
            <h3 className="font-semibold">Start a Discussion</h3>
            <div className="space-y-2">
              <Input
                placeholder="What's on your mind?"
                value={newDiscussion}
                onChange={(e) => {
                  setNewDiscussion(e.target.value);
                  if (errors.discussion) {
                    setErrors(prev => ({ ...prev, discussion: '' }));
                  }
                }}
                onKeyPress={(e) => e.key === 'Enter' && handleAddDiscussion()}
                className={errors.discussion ? 'border-red-500' : ''}
              />
              {errors.discussion && (
                <p className="text-sm text-red-500">{errors.discussion}</p>
              )}
              <div className="text-xs text-muted-foreground text-right">
                {newDiscussion.length}/500 characters
              </div>
            </div>
            <div className="flex justify-end">
              <Button onClick={handleAddDiscussion} disabled={!newDiscussion.trim()}>
                <Send className="h-4 w-4 mr-2" />
                Post
              </Button>
            </div>
          </div>
        </Card>

        {/* Discussion Threads */}
        <div className="space-y-4">
          {discussionsList.map((discussion) => (
            <Card key={discussion.id} className="p-4">
              <div className="flex items-start gap-3">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={discussion.avatar} />
                  <AvatarFallback>{discussion.author.split(' ').map((n: string) => n[0]).join('')}</AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="font-medium">{discussion.author}</span>
                    <span className="text-sm text-muted-foreground">{discussion.timestamp}</span>
                  </div>
                  <h4 className="font-semibold mb-2">{discussion.title}</h4>
                  <p className="text-sm text-muted-foreground mb-3">{discussion.content}</p>
                  <div className="flex items-center gap-4">
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      onClick={() => handleReplyToDiscussion(discussion.id)}
                    >
                      <Reply className="h-4 w-4 mr-1" />
                      Reply
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Share className="h-4 w-4 mr-1" />
                      Share
                    </Button>
                    <span className="text-sm text-muted-foreground">
                      {discussion.replies} replies
                    </span>
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </TabsContent>

      <TabsContent value="resources" className="space-y-4">
        {/* Add Resource Button */}
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <h3 className="font-semibold">Shared Resources</h3>
            <Button onClick={() => setShowResourceForm(!showResourceForm)}>
              <Plus className="h-4 w-4 mr-2" />
              Share Resource
            </Button>
          </div>
          
          {showResourceForm && (
            <div className="mt-4 space-y-3">
              <div className="space-y-2">
                <Input
                  placeholder="Resource title"
                  value={newResource.title}
                  onChange={(e) => {
                    setNewResource({...newResource, title: e.target.value});
                    if (errors.resource.title) {
                      setErrors(prev => ({ ...prev, resource: { ...prev.resource, title: '' } }));
                    }
                  }}
                  className={errors.resource.title ? 'border-red-500' : ''}
                />
                {errors.resource.title && (
                  <p className="text-sm text-red-500">{errors.resource.title}</p>
                )}
                <div className="text-xs text-muted-foreground text-right">
                  {newResource.title.length}/100 characters
                </div>
              </div>
              <Textarea
                placeholder="Description (optional)"
                value={newResource.description}
                onChange={(e) => setNewResource({...newResource, description: e.target.value})}
                maxLength={300}
              />
              <div className="space-y-2">
                <Input
                  placeholder="URL or file path (e.g., https://example.com/resource)"
                  value={newResource.url}
                  onChange={(e) => {
                    setNewResource({...newResource, url: e.target.value});
                    if (errors.resource.url) {
                      setErrors(prev => ({ ...prev, resource: { ...prev.resource, url: '' } }));
                    }
                  }}
                  className={errors.resource.url ? 'border-red-500' : ''}
                />
                {errors.resource.url && (
                  <p className="text-sm text-red-500">{errors.resource.url}</p>
                )}
              </div>
              <div className="flex gap-2">
                <Button 
                  onClick={handleAddResource}
                  disabled={!newResource.title.trim() || !newResource.url.trim()}
                >
                  Share
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => {
                    setShowResourceForm(false);
                    setNewResource({ title: '', description: '', url: '' });
                    setErrors(prev => ({ ...prev, resource: { title: '', url: '' } }));
                  }}
                >
                  Cancel
                </Button>
              </div>
            </div>
          )}
        </Card>

        {/* Resources List */}
        <div className="space-y-3">
          {resourcesList.map((resource) => (
            <Card key={resource.id} className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <FileText className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <h4 className="font-medium">{resource.title}</h4>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <span>Shared by {resource.sharedBy}</span>
                      <span>•</span>
                      <span>{resource.timestamp}</span>
                      <span>•</span>
                      <span>{resource.downloads} downloads</span>
                    </div>
                  </div>
                </div>
                <Badge variant="outline">{resource.type}</Badge>
              </div>
            </Card>
          ))}
        </div>
      </TabsContent>

      <TabsContent value="calendar" className="space-y-4">
        {/* Schedule Event Button */}
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <h3 className="font-semibold">Upcoming Events</h3>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Schedule Event
            </Button>
          </div>
        </Card>

        {/* Events List */}
        <div className="space-y-3">
          {events.map((event) => (
            <Card key={event.id} className="p-4">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <h4 className="font-semibold">{event.title}</h4>
                    <Badge variant={event.status === 'confirmed' ? 'default' : 'secondary'}>
                      {event.status}
                    </Badge>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 text-sm text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      <span>{event.date}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      <span>{event.time} ({event.duration})</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Users className="h-3 w-3" />
                      <span>{event.attendees} attendees</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <MapPin className="h-3 w-3" />
                      <span>{event.location}</span>
                    </div>
                  </div>
                </div>
                
                <div className="flex gap-2">
                  <Button variant="outline" size="sm">
                    <Video className="h-4 w-4 mr-1" />
                    Join
                  </Button>
                  <Button variant="ghost" size="sm">
                    Edit
                  </Button>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </TabsContent>
    </Tabs>
  );
};

export default CollaborationTabs;