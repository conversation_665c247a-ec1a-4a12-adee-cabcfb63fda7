import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, Circle, Clock } from 'lucide-react';

type ADDIEPhase = 'analysis' | 'design' | 'development' | 'implementation' | 'evaluation';

interface PhaseItem {
  id: ADDIEPhase;
  name: string;
  status: 'completed' | 'in_progress' | 'pending';
}

interface ADDIEPhaseNavigationProps {
  currentPhase: ADDIEPhase;
  onPhaseChange: (phase: ADDIEPhase) => void;
  phases: PhaseItem[];
  className?: string;
}

export const ADDIEPhaseNavigation: React.FC<ADDIEPhaseNavigationProps> = ({
  currentPhase,
  onPhaseChange,
  phases,
  className = ''
}) => {
  const getPhaseIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'in_progress':
        return <Clock className="h-4 w-4 text-blue-600" />;
      default:
        return <Circle className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge variant="default" className="bg-green-100 text-green-800">Complete</Badge>;
      case 'in_progress':
        return <Badge variant="default" className="bg-blue-100 text-blue-800">In Progress</Badge>;
      default:
        return <Badge variant="outline">Pending</Badge>;
    }
  };

  return (
    <div className={`space-y-2 ${className}`}>
      {phases.map((phase, index) => (
        <div key={phase.id} className="flex items-center space-x-2">
          <Button
            variant={currentPhase === phase.id ? 'default' : 'ghost'}
            className="flex items-center justify-start gap-3 w-full"
            onClick={() => onPhaseChange(phase.id)}
          >
            {getPhaseIcon(phase.status)}
            <span className="flex-1 text-left">{phase.name}</span>
            {getStatusBadge(phase.status)}
          </Button>
          {index < phases.length - 1 && (
            <div className="h-8 w-px bg-gray-200 ml-6" />
          )}
        </div>
      ))}
    </div>
  );
};

export default ADDIEPhaseNavigation;