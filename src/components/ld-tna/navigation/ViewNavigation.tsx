import React from 'react';
import { Button } from '@/components/ui/button';
import { LucideIcon } from 'lucide-react';
import { ViewType } from '../common/types';

interface NavigationItem {
  id: ViewType;
  name: string;
  icon: LucideIcon;
}

interface ViewNavigationProps {
  activeView: ViewType;
  onViewChange: (view: ViewType) => void;
  navigationItems: NavigationItem[];
  className?: string;
}

export const ViewNavigation: React.FC<ViewNavigationProps> = ({
  activeView,
  onViewChange,
  navigationItems,
  className = ''
}) => {
  return (
    <nav className={`flex space-x-1 ${className}`}>
      {navigationItems.map((item) => {
        const Icon = item.icon;
        return (
          <Button
            key={item.id}
            variant={activeView === item.id ? 'default' : 'ghost'}
            className="flex items-center gap-2"
            onClick={() => onViewChange(item.id)}
          >
            <Icon className="h-4 w-4" />
            {item.name}
          </Button>
        );
      })}
    </nav>
  );
};

export default ViewNavigation;