import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Download, Star, FileText } from 'lucide-react';
import { Template } from '../common/types';

interface TemplateCardProps {
  template: Template;
  onDownload?: (template: Template) => void;
  onPreview?: (template: Template) => void;
  className?: string;
}

export const TemplateCard: React.FC<TemplateCardProps> = ({
  template,
  onDownload,
  onPreview,
  className = ''
}) => {
  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-3 w-3 ${
          i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ));
  };

  return (
    <Card className={`hover:shadow-md transition-shadow ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-2">
            <FileText className="h-5 w-5 text-blue-600" />
            <CardTitle className="text-base font-semibold">
              {template.name}
            </CardTitle>
          </div>
          <Badge variant="outline" className="text-xs">
            {template.format}
          </Badge>
        </div>
        <Badge variant="secondary" className="w-fit text-xs">
          {template.category}
        </Badge>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <p className="text-sm text-muted-foreground line-clamp-3">
          {template.description}
        </p>
        
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center gap-1">
            {renderStars(template.rating)}
            <span className="text-muted-foreground ml-1">({template.rating})</span>
          </div>
          <div className="flex items-center gap-1 text-muted-foreground">
            <Download className="h-3 w-3" />
            <span>{template.downloads.toLocaleString()}</span>
          </div>
        </div>
        
        <div className="flex gap-2">
          {onPreview && (
            <Button
              variant="outline"
              size="sm"
              className="flex-1"
              onClick={() => onPreview(template)}
            >
              Preview
            </Button>
          )}
          {onDownload && (
            <Button
              size="sm"
              className="flex-1"
              onClick={() => onDownload(template)}
            >
              <Download className="h-3 w-3 mr-1" />
              Download
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default TemplateCard;