import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { LucideIcon } from 'lucide-react';

interface QuickActionCardProps {
  title: string;
  icon: LucideIcon;
  onClick: () => void;
  className?: string;
  variant?: 'default' | 'outline' | 'secondary' | 'ghost';
}

export const QuickActionCard: React.FC<QuickActionCardProps> = ({
  title,
  icon: Icon,
  onClick,
  className = '',
  variant = 'outline'
}) => {
  return (
    <Card className={`hover:shadow-md transition-shadow cursor-pointer ${className}`}>
      <CardContent className="p-4">
        <Button
          variant={variant}
          className="w-full h-auto flex flex-col items-center gap-2 p-4"
          onClick={onClick}
        >
          <Icon className="h-6 w-6" />
          <span className="text-sm font-medium text-center">{title}</span>
        </Button>
      </CardContent>
    </Card>
  );
};

export default QuickActionCard;