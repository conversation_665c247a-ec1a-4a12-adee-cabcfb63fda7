import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { LucideIcon } from 'lucide-react';

interface KirkpatrickLevelCardProps {
  level: number;
  title: string;
  description: string;
  score: number;
  maxScore?: number;
  icon: LucideIcon;
  iconColor?: string;
  details?: string[];
  status?: 'excellent' | 'good' | 'needs_improvement' | 'poor';
  className?: string;
}

export const KirkpatrickLevelCard: React.FC<KirkpatrickLevelCardProps> = ({
  level,
  title,
  description,
  score,
  maxScore = 100,
  icon: Icon,
  iconColor = 'text-blue-600',
  details = [],
  status,
  className = ''
}) => {
  const percentage = (score / maxScore) * 100;
  
  const getStatusBadge = () => {
    if (!status) return null;
    
    const statusConfig = {
      excellent: { text: 'Excellent', variant: 'default' as const, className: 'bg-green-100 text-green-800' },
      good: { text: 'Good', variant: 'default' as const, className: 'bg-blue-100 text-blue-800' },
      needs_improvement: { text: 'Needs Improvement', variant: 'default' as const, className: 'bg-yellow-100 text-yellow-800' },
      poor: { text: 'Poor', variant: 'destructive' as const, className: '' }
    };
    
    const config = statusConfig[status];
    return (
      <Badge variant={config.variant} className={config.className}>
        {config.text}
      </Badge>
    );
  };

  return (
    <Card className={`hover:shadow-md transition-shadow ${className}`}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div className="flex items-center gap-2">
          <Icon className={`h-5 w-5 ${iconColor}`} />
          <div>
            <CardTitle className="text-lg font-semibold">
              Level {level}: {title}
            </CardTitle>
            <p className="text-sm text-muted-foreground">{description}</p>
          </div>
        </div>
        {getStatusBadge()}
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">Score</span>
            <span className="text-sm font-bold">{score}/{maxScore}</span>
          </div>
          <Progress value={percentage} className="h-2" />
          <div className="text-xs text-muted-foreground text-right">
            {percentage.toFixed(1)}%
          </div>
        </div>
        
        {details.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium">Key Metrics:</h4>
            <ul className="text-sm text-muted-foreground space-y-1">
              {details.map((detail, index) => (
                <li key={index} className="flex items-start gap-2">
                  <span className="w-1 h-1 bg-current rounded-full mt-2 flex-shrink-0" />
                  {detail}
                </li>
              ))}
            </ul>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default KirkpatrickLevelCard;