// Professional L&D Interfaces
export interface CompetencyModel {
  id: string;
  name: string;
  description: string;
  category: 'technical' | 'behavioral' | 'leadership' | 'compliance';
  levels: CompetencyLevel[];
  businessCriticality: 'critical' | 'important' | 'beneficial';
  industryStandard?: string;
}

export interface CompetencyLevel {
  level: number;
  name: string;
  description: string;
  behaviors: string[];
  assessmentCriteria: string[];
  developmentActivities: string[];
}

export interface SkillMatrix {
  employeeId: string;
  employeeName: string;
  role: string;
  department: string;
  competencies: {
    competencyId: string;
    currentLevel: number;
    targetLevel: number;
    gap: number;
    priority: 'critical' | 'high' | 'medium' | 'low';
    assessmentMethod: 'self' | 'manager' | '360' | 'observation' | 'test';
    lastAssessed: Date;
    assessorName: string;
  }[];
  overallReadiness: number;
  developmentPlan?: any; // Consider creating local type
}

export interface LearningModule {
  id: string;
  name: string;
  type: 'course' | 'workshop' | 'mentoring' | 'project' | 'assessment';
  format: 'online' | 'classroom' | 'blended' | 'experiential';
  duration: number;
  provider?: string;
  cost: number;
  competenciesAddressed: string[];
  prerequisites: string[];
  learningObjectives: string[];
  assessmentMethod: string;
  effectivenessRating: number;
}

export interface ADDIEProject {
  id: string;
  name: string;
  description: string;
  currentPhase: 'analysis' | 'design' | 'development' | 'implementation' | 'evaluation';
  phases: {
    analysis: ADDIEAnalysisData;
    design: ADDIEDesignData;
    development: ADDIEDevelopmentData;
    implementation: ADDIEImplementationData;
    evaluation: ADDIEEvaluationData;
  };
  timeline: {
    startDate: Date;
    endDate: Date;
    milestones: { phase: string; date: Date; status: 'completed' | 'in_progress' | 'planned' }[];
  };
  stakeholders: {
    projectManager: string;
    instructionalDesigner: string;
    subjectMatterExperts: string[];
    businessSponsor: string;
    targetAudience: string;
  };
  budget: {
    total: number;
    allocated: number;
    spent: number;
  };
  riskAssessment: {
    level: 'low' | 'medium' | 'high';
    factors: string[];
    mitigation: string[];
  };
}

export interface ADDIEAnalysisData {
  businessNeed: string;
  performanceGap: string;
  targetAudience: {
    size: number;
    demographics: string;
    currentSkillLevel: string;
    learningPreferences: string;
    constraints: string[];
  };
  goalAnalysis: {
    businessGoals: string[];
    learningGoals: string[];
    performanceGoals: string[];
  };
  contextualFactors: {
    organizational: string[];
    environmental: string[];
    technological: string[];
  };
  successCriteria: string[];
}

export interface ADDIEDesignData {
  learningObjectives: string[];
  assessmentStrategy: string;
  contentStructure: string[];
  deliveryMethod: string;
  mediaSelection: string[];
  interactionDesign: string;
  accessibility: string[];
  timeline: string;
}

export interface ADDIEDevelopmentData {
  contentCreation: {
    modules: string[];
    assessments: string[];
    resources: string[];
  };
  qualityAssurance: {
    reviews: string[];
    testing: string[];
    revisions: string[];
  };
  pilotTesting: {
    participants: number;
    feedback: string[];
    improvements: string[];
  };
}

export interface ADDIEImplementationData {
  rolloutPlan: string;
  communicationStrategy: string;
  supportResources: string[];
  facilitatorTraining: string;
  technologySetup: string;
  changeManagement: string[];
}

export interface ADDIEEvaluationData {
  kirkpatrickLevels: {
    reaction: KirkpatrickReaction;
    learning: KirkpatrickLearning;
    behavior: KirkpatrickBehavior;
    results: KirkpatrickResults;
  };
  roi: {
    investment: number;
    benefits: number;
    roi: number;
    paybackPeriod: number;
  };
}

export interface KirkpatrickReaction {
  satisfactionScore: number;
  engagementLevel: number;
  relevanceRating: number;
  completionRate: number;
  feedback: string[];
}

export interface KirkpatrickLearning {
  knowledgeAcquisition: number;
  skillDemonstration: number;
  attitudeChange: number;
  confidenceLevel: number;
  assessmentScores: number[];
}

export interface KirkpatrickBehavior {
  behaviorChange: number;
  applicationRate: number;
  performanceImprovement: number;
  timeToApplication: number;
  barriers: string[];
  enablers: string[];
}

export interface KirkpatrickResults {
  businessImpact: number;
  kpiImprovement: { metric: string; before: number; after: number; improvement: number }[];
  costReduction: number;
  revenueIncrease: number;
  qualityImprovement: number;
}

// View types
export type ViewType = 'dashboard' | 'addie' | 'competency' | 'analytics' | 'library' | 'collaboration';

// Template types
export interface Template {
  name: string;
  category: string;
  downloads: number;
  rating: number;
  format: string;
  description: string;
}

export interface Methodology {
  name: string;
  description: string;
  phases: string[];
  bestFor: string;
  timeframe: string;
}

// Metric types
export interface MetricData {
  label: string;
  value: string | number;
  icon: any; // LucideIcon type
  color?: string;
  badge?: {
    text: string;
    variant: 'default' | 'secondary' | 'destructive' | 'outline';
  };
}

export interface QuickAction {
  title: string;
  icon: any; // LucideIcon type
  onClick: () => void;
}

export interface NavigationItem {
  id: ViewType;
  name: string;
  icon: any; // LucideIcon type
}