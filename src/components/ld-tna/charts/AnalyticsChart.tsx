import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { TrendingUp, TrendingDown, BarChart3 } from 'lucide-react';

interface ChartDataPoint {
  label: string;
  value: number;
  change?: number;
}

interface AnalyticsChartProps {
  title: string;
  data: ChartDataPoint[];
  type?: 'bar' | 'line' | 'pie';
  showTrend?: boolean;
  className?: string;
}

export const AnalyticsChart: React.FC<AnalyticsChartProps> = ({
  title,
  data,
  type = 'bar',
  showTrend = true,
  className = ''
}) => {
  const getTrendIcon = (change: number) => {
    if (change > 0) {
      return <TrendingUp className="h-3 w-3 text-green-600" />;
    } else if (change < 0) {
      return <TrendingDown className="h-3 w-3 text-red-600" />;
    }
    return null;
  };

  const getTrendBadge = (change: number) => {
    if (change === 0) return null;
    
    const isPositive = change > 0;
    return (
      <Badge 
        variant="outline" 
        className={`text-xs ${
          isPositive ? 'text-green-700 border-green-200' : 'text-red-700 border-red-200'
        }`}
      >
        {isPositive ? '+' : ''}{change.toFixed(1)}%
      </Badge>
    );
  };

  return (
    <Card className={`hover:shadow-md transition-shadow ${className}`}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-base font-semibold flex items-center gap-2">
          <BarChart3 className="h-4 w-4" />
          {title}
        </CardTitle>
        <Badge variant="outline" className="text-xs capitalize">
          {type} Chart
        </Badge>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Actual chart implementation */}
        <div className="h-48 bg-white rounded-lg border p-4">
          {type === 'bar' && (
            <div className="h-full flex items-end justify-between gap-2">
              {data.map((point, index) => {
                const maxValue = Math.max(...data.map(d => d.value));
                const height = (point.value / maxValue) * 100;
                return (
                  <div key={index} className="flex-1 flex flex-col items-center">
                    <div className="text-xs mb-1 font-medium">{point.value}</div>
                    <div 
                      className="w-full bg-blue-500 rounded-t transition-all duration-300 hover:bg-blue-600"
                      style={{ height: `${height}%`, minHeight: '4px' }}
                      title={`${point.label}: ${point.value}`}
                    />
                    <div className="text-xs mt-2 text-center truncate w-full">{point.label}</div>
                  </div>
                );
              })}
            </div>
          )}
          
          {type === 'line' && (
            <div className="h-full relative">
              <svg className="w-full h-full" viewBox="0 0 400 200">
                {data.map((point, index) => {
                  const maxValue = Math.max(...data.map(d => d.value));
                  const x = (index / (data.length - 1)) * 380 + 10;
                  const y = 190 - (point.value / maxValue) * 170;
                  const nextPoint = data[index + 1];
                  
                  return (
                    <g key={index}>
                      <circle cx={x} cy={y} r="4" fill="#3b82f6" />
                      {nextPoint && (
                        <line 
                          x1={x} y1={y} 
                          x2={(index + 1) / (data.length - 1) * 380 + 10} 
                          y2={190 - (nextPoint.value / maxValue) * 170}
                          stroke="#3b82f6" strokeWidth="2"
                        />
                      )}
                      <text x={x} y="195" textAnchor="middle" fontSize="10" fill="#666">
                        {point.label.slice(0, 8)}
                      </text>
                    </g>
                  );
                })}
              </svg>
            </div>
          )}
          
          {type === 'pie' && (
            <div className="h-full flex items-center justify-center">
              <div className="relative w-32 h-32">
                <svg className="w-full h-full transform -rotate-90" viewBox="0 0 100 100">
                  {(() => {
                    const total = data.reduce((sum, point) => sum + point.value, 0);
                    let currentAngle = 0;
                    const colors = ['#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6'];
                    
                    return data.map((point, index) => {
                      const percentage = (point.value / total) * 100;
                      const angle = (percentage / 100) * 360;
                      const startAngle = currentAngle;
                      const endAngle = currentAngle + angle;
                      currentAngle += angle;
                      
                      const startX = 50 + 40 * Math.cos((startAngle * Math.PI) / 180);
                      const startY = 50 + 40 * Math.sin((startAngle * Math.PI) / 180);
                      const endX = 50 + 40 * Math.cos((endAngle * Math.PI) / 180);
                      const endY = 50 + 40 * Math.sin((endAngle * Math.PI) / 180);
                      
                      const largeArcFlag = angle > 180 ? 1 : 0;
                      
                      return (
                         <path
                           key={index}
                           d={`M 50 50 L ${startX} ${startY} A 40 40 0 ${largeArcFlag} 1 ${endX} ${endY} Z`}
                           fill={colors[index % colors.length]}
                         />
                       );
                    });
                  })()
                  }
                </svg>
              </div>
            </div>
          )}
        </div>
        
        {/* Data summary */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Data Summary:</h4>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
            {data.slice(0, 4).map((point, index) => (
              <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                <span className="text-sm">{point.label}</span>
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">{point.value}</span>
                  {showTrend && point.change !== undefined && (
                    <div className="flex items-center gap-1">
                      {getTrendIcon(point.change)}
                      {getTrendBadge(point.change)}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
          
          {data.length > 4 && (
            <p className="text-xs text-muted-foreground text-center">
              +{data.length - 4} more data points
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default AnalyticsChart;