import React, { useState, useEffect, useRef } from 'react';
import { X, ArrowRight, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

interface TutorialStep {
  id: string;
  title: string;
  content: string;
  target: string; // CSS selector
  position?: 'top' | 'bottom' | 'left' | 'right';
  action?: {
    label: string;
    onClick: () => void;
  };
}

interface TutorialTooltipProps {
  steps: TutorialStep[];
  currentStep: number;
  isVisible: boolean;
  onNext: () => void;
  onPrevious: () => void;
  onClose: () => void;
  onComplete: () => void;
}

export const TutorialTooltip: React.FC<TutorialTooltipProps> = ({
  steps,
  currentStep,
  isVisible,
  onNext,
  onPrevious,
  onClose,
  onComplete
}) => {
  const [position, setPosition] = useState({ top: 0, left: 0 });
  const tooltipRef = useRef<HTMLDivElement>(null);

  const currentStepData = steps[currentStep];
  const isFirstStep = currentStep === 0;
  const isLastStep = currentStep === steps.length - 1;

  useEffect(() => {
    if (!isVisible || !currentStepData) return;

    const targetElement = document.querySelector(currentStepData.target);
    if (!targetElement || !tooltipRef.current) return;

    const targetRect = targetElement.getBoundingClientRect();
    const tooltipRect = tooltipRef.current.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    let top = 0;
    let left = 0;

    switch (currentStepData.position || 'bottom') {
      case 'top':
        top = targetRect.top - tooltipRect.height - 10;
        left = targetRect.left + (targetRect.width - tooltipRect.width) / 2;
        break;
      case 'bottom':
        top = targetRect.bottom + 10;
        left = targetRect.left + (targetRect.width - tooltipRect.width) / 2;
        break;
      case 'left':
        top = targetRect.top + (targetRect.height - tooltipRect.height) / 2;
        left = targetRect.left - tooltipRect.width - 10;
        break;
      case 'right':
        top = targetRect.top + (targetRect.height - tooltipRect.height) / 2;
        left = targetRect.right + 10;
        break;
    }

    // Ensure tooltip stays within viewport
    left = Math.max(10, Math.min(left, viewportWidth - tooltipRect.width - 10));
    top = Math.max(10, Math.min(top, viewportHeight - tooltipRect.height - 10));

    setPosition({ top, left });

    // Highlight target element
    targetElement.classList.add('tutorial-highlight');
    
    return () => {
      targetElement.classList.remove('tutorial-highlight');
    };
  }, [currentStep, isVisible, currentStepData]);

  if (!isVisible || !currentStepData) return null;

  return (
    <>
      {/* Overlay */}
      <div className="fixed inset-0 bg-black/50 z-40" onClick={onClose} />
      
      {/* Tooltip */}
      <Card
        ref={tooltipRef}
        className="fixed z-50 w-80 shadow-2xl border-2"
        style={{
          top: `${position.top}px`,
          left: `${position.left}px`
        }}
      >
        <CardContent className="p-4">
          <div className="flex items-start justify-between mb-3">
            <div className="flex-1">
              <h3 className="font-semibold text-base mb-1">
                {currentStepData.title}
              </h3>
              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                <span>Step {currentStep + 1} of {steps.length}</span>
                <div className="flex-1 bg-muted rounded-full h-1">
                  <div 
                    className="bg-primary h-1 rounded-full transition-all duration-300"
                    style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
                  />
                </div>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0"
              onClick={onClose}
            >
              <X className="w-4 h-4" />
            </Button>
          </div>

          <p className="text-sm text-muted-foreground mb-4">
            {currentStepData.content}
          </p>

          {currentStepData.action && (
            <Button
              variant="outline"
              size="sm"
              className="w-full mb-3"
              onClick={currentStepData.action.onClick}
            >
              {currentStepData.action.label}
            </Button>
          )}

          <div className="flex items-center justify-between">
            <Button
              variant="ghost"
              size="sm"
              onClick={onPrevious}
              disabled={isFirstStep}
              className="flex items-center gap-1"
            >
              <ArrowLeft className="w-3 h-3" />
              Previous
            </Button>

            {isLastStep ? (
              <Button
                size="sm"
                onClick={onComplete}
                className="flex items-center gap-1"
              >
                Complete
              </Button>
            ) : (
              <Button
                size="sm"
                onClick={onNext}
                className="flex items-center gap-1"
              >
                Next
                <ArrowRight className="w-3 h-3" />
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      <style jsx global>{`
        .tutorial-highlight {
          position: relative;
          z-index: 45;
          box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.5);
          border-radius: 4px;
        }
      `}</style>
    </>
  );
};