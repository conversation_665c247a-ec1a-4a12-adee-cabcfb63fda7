// Enums for the financial dashboard
export enum TimeFrame {
  ONE_DAY = '1D',
  ONE_WEEK = '1W',
  ONE_MONTH = '1M',
  ONE_YEAR = '1Y',
  MAX = 'MAX'
}

export enum TransactionType {
  BUY = 'buy',
  SELL = 'sell',
  DEPOSIT = 'deposit',
  WITHDRAW = 'withdraw'
}

export enum MetricType {
  INVESTMENT = 'investment',
  TOTAL_GAIN = 'total_gain',
  TOTAL_LOSS = 'total_loss'
}

export enum ChartType {
  LINE = 'line',
  AREA = 'area'
}

// Props types (data passed to components)
export interface CompanyData {
  name: string;
  symbol: string;
  logoUrl: string;
  websiteUrl: string;
}

export interface PortfolioData {
  totalValue: number;
  investment: number;
  totalGain: number;
  totalLoss: number;
  investmentChange: number;
  gainChange: number;
  lossChange: number;
}

export interface ShareHolderData {
  promoterPercentage: number;
  promoterChange: number;
}

export interface StockData {
  currentPrice: number;
  previousClose: number;
  percentChange: number;
  marketCap: string;
  peRatio: number;
  lastUpdated: Date;
}

export interface ChartDataPoint {
  date: string;
  value: number;
  time: string;
}

export interface DateRange {
  start: Date;
  end: Date;
}

export interface DashboardProps {
  company: CompanyData;
  portfolio: PortfolioData;
  shareHolders: ShareHolderData;
  stockData: StockData;
  chartData: ChartDataPoint[];
  dateRange: DateRange;
  selectedTimeFrame: TimeFrame;
  availableTimeFrames: readonly TimeFrame[];
}

// Root component props interface
export interface FinancialDashboardProps {
  company: CompanyData;
  portfolio: PortfolioData;
  shareHolders: ShareHolderData;
  stockData: StockData;
  chartData: ChartDataPoint[];
  dateRange: DateRange;
  selectedTimeFrame: TimeFrame;
  availableTimeFrames: readonly TimeFrame[];
  onTimeFrameChange?: (timeFrame: TimeFrame) => void;
  onBuyStock?: () => void;
  onSellStock?: () => void;
  onDeposit?: () => void;
  onWithdraw?: () => void;
  onVisitWebsite?: (url: string) => void;
}