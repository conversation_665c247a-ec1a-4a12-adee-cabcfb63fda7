import React from 'react';
import { Button } from '@/components/ui/button';
import { TimeFrame } from '../types';

interface TimeFrameSelectorProps {
  selectedTimeFrame: TimeFrame;
  availableTimeFrames: readonly TimeFrame[];
  onTimeFrameChange: (timeFrame: TimeFrame) => void;
}

export const TimeFrameSelector: React.FC<TimeFrameSelectorProps> = ({
  selectedTimeFrame,
  availableTimeFrames,
  onTimeFrameChange
}) => {
  return (
    <div className="flex items-center gap-2">
      {availableTimeFrames.map((timeFrame) => (
        <Button
          key={timeFrame}
          variant={selectedTimeFrame === timeFrame ? "default" : "outline"}
          size="sm"
          onClick={() => onTimeFrameChange(timeFrame)}
          className={
            selectedTimeFrame === timeFrame
              ? "bg-dashboard-accent text-white border-dashboard-accent hover:bg-dashboard-accent/90"
              : "bg-transparent text-gray-400 border-dashboard-border hover:text-white hover:border-gray-300"
          }
        >
          {timeFrame}
        </Button>
      ))}
    </div>
  );
};