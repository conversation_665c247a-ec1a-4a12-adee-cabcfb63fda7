import React from 'react';
import { Card } from '@/components/ui/card';
import { ExternalLink, MoreVertical } from 'lucide-react';
import { CompanyData } from '../types';

interface CompanyHeaderProps {
  company: CompanyData;
  onVisitWebsite?: (url: string) => void;
}

export const CompanyHeader: React.FC<CompanyHeaderProps> = ({
  company,
  onVisitWebsite
}) => {
  const handleWebsiteClick = () => {
    if (onVisitWebsite) {
      onVisitWebsite(company.websiteUrl);
    } else {
      window.open(company.websiteUrl, '_blank');
    }
  };

  return (
    <Card className="p-6 bg-gradient-to-br from-dashboard-card to-dashboard-card/80 border-dashboard-border">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="w-12 h-12 rounded-full overflow-hidden bg-dashboard-gradient-start/20 flex items-center justify-center">
            <img 
              src={company.logoUrl}
              alt={`${company.name} logo - Woliul <PERSON> on Unsplash`}
              className="w-8 h-8 rounded-full object-cover"
              style={{ width: '32px', height: '32px' }}
            />
          </div>
          <div>
            <h1 className="text-xl font-bold text-white">{company.name}</h1>
            <p className="text-sm text-gray-400">({company.symbol})</p>
          </div>
        </div>
        
        <div className="flex items-center gap-3">
          <button
            onClick={handleWebsiteClick}
            className="flex items-center gap-2 px-3 py-2 text-sm text-gray-300 hover:text-white transition-colors"
          >
            <span>Official Website</span>
            <ExternalLink size={16} />
          </button>
          
          <button className="p-2 text-gray-400 hover:text-white transition-colors">
            <MoreVertical size={20} />
          </button>
        </div>
      </div>
    </Card>
  );
};