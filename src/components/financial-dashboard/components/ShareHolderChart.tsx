import React from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { ShareHolderData } from '../types';
import { formatPercentage } from '../utils/formatters';

interface ShareHolderChartProps {
  shareHolders: ShareHolderData;
}

export const ShareHolderChart: React.FC<ShareHolderChartProps> = ({ shareHolders }) => {
  return (
    <Card className="p-6 bg-dashboard-card border-dashboard-border">
      <div className="space-y-6">
        <div className="text-center">
          <h3 className="text-lg font-semibold text-white mb-2">Share Holder</h3>
          <div className="relative w-32 h-32 mx-auto">
            {/* Custom circular progress */}
            <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 120 120">
              {/* Background circle */}
              <circle
                cx="60"
                cy="60"
                r="50"
                stroke="rgba(255, 255, 255, 0.1)"
                strokeWidth="8"
                fill="none"
              />
              
              {/* Progress circle with gradient */}
              <circle
                cx="60"
                cy="60"
                r="50"
                stroke="url(#gradient)"
                strokeWidth="8"
                fill="none"
                strokeDasharray={`${(shareHolders.promoterPercentage / 100) * 314} 314`}
                strokeLinecap="round"
                className="transition-all duration-1000 ease-out"
              />
              
              {/* Gradient definition */}
              <defs>
                <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" stopColor="#ff6b6b" />
                  <stop offset="50%" stopColor="#4ecdc4" />
                  <stop offset="100%" stopColor="#45b7d1" />
                </linearGradient>
              </defs>
            </svg>
            
            {/* Center text */}
            <div className="absolute inset-0 flex flex-col items-center justify-center">
              <span className="text-3xl font-bold text-white">
                {shareHolders.promoterPercentage}%
              </span>
            </div>
          </div>
          
          <div className="flex items-center justify-center gap-2 mt-4">
            <div className="w-3 h-3 rounded-full bg-dashboard-chart-secondary"></div>
            <span className="text-sm text-gray-400">Promoter</span>
          </div>
        </div>
        
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-400">Performance</span>
          <Badge 
            variant="outline" 
            className="text-dashboard-success border-dashboard-success"
          >
            {formatPercentage(shareHolders.promoterChange)}
          </Badge>
        </div>
      </div>
    </Card>
  );
};