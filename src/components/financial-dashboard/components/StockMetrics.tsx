import React from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { StockData } from '../types';
import { formatCompactCurrency, formatDateTime, formatPercentage } from '../utils/formatters';

interface StockMetricsProps {
  stockData: StockData;
}

export const StockMetrics: React.FC<StockMetricsProps> = ({ stockData }) => {
  return (
    <Card className="p-6 bg-dashboard-card border-dashboard-border">
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-400">. (OREA)</span>
          <Badge 
            variant="outline" 
            className="text-dashboard-success border-dashboard-success"
          >
            {formatPercentage(stockData.percentChange)}
          </Badge>
        </div>
        
        <div className="text-3xl font-bold text-white">
          {formatCompactCurrency(stockData.currentPrice)}
        </div>
        
        <div className="text-sm text-gray-400">
          {formatDateTime(stockData.lastUpdated)}
        </div>
        
        <div className="space-y-3 pt-4 border-t border-dashboard-border">
          <div className="flex justify-between">
            <span className="text-sm text-gray-400">Prev Close</span>
            <span className="text-sm text-white">{formatCompactCurrency(stockData.previousClose)}</span>
          </div>
          
          <div className="flex justify-between">
            <span className="text-sm text-gray-400">% Change</span>
            <span className="text-sm text-dashboard-success">{formatPercentage(stockData.percentChange)}</span>
          </div>
          
          <div className="flex justify-between">
            <span className="text-sm text-gray-400">Market Cap</span>
            <span className="text-sm text-white">{stockData.marketCap}</span>
          </div>
          
          <div className="flex justify-between">
            <span className="text-sm text-gray-400">PE Ratio</span>
            <span className="text-sm text-white">{stockData.peRatio.toFixed(2)}%</span>
          </div>
        </div>
      </div>
    </Card>
  );
};