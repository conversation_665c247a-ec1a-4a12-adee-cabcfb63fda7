import React from 'react';
import { Button } from '@/components/ui/button';

interface ActionButtonsProps {
  onBuyStock?: () => void;
  onSellStock?: () => void;
  onDeposit?: () => void;
  onWithdraw?: () => void;
}

export const ActionButtons: React.FC<ActionButtonsProps> = ({
  onBuyStock,
  onSellStock,
  onDeposit,
  onWithdraw
}) => {
  return (
    <div className="flex flex-col sm:flex-row gap-4">
      <div className="flex gap-3">
        <Button
          onClick={onSellStock}
          variant="outline"
          className="flex-1 bg-transparent text-gray-300 border-dashboard-border hover:text-white hover:border-gray-300"
        >
          Sell Stock
        </Button>
        
        <Button
          onClick={onBuyStock}
          className="flex-1 bg-dashboard-accent text-white hover:bg-dashboard-accent/90"
        >
          Buy Stock
        </Button>
      </div>
      
      <div className="flex gap-3">
        <Button
          onClick={onWithdraw}
          variant="outline"
          className="flex-1 bg-transparent text-gray-300 border-dashboard-border hover:text-white hover:border-gray-300"
        >
          Withdraw
        </Button>
        
        <Button
          onClick={onDeposit}
          className="flex-1 bg-dashboard-accent text-white hover:bg-dashboard-accent/90"
        >
          Deposit +
        </Button>
      </div>
    </div>
  );
};