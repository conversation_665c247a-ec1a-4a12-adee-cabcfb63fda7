import React from 'react';
import { MetricCard } from './MetricCard';
import { PortfolioData } from '../types';

interface PortfolioStatsProps {
  portfolio: PortfolioData;
}

export const PortfolioStats: React.FC<PortfolioStatsProps> = ({ portfolio }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
      <MetricCard
        title="My Portfolio"
        value={portfolio.totalValue}
        className="md:col-span-1"
      />
      
      <MetricCard
        title="Investment"
        value={portfolio.investment}
        change={portfolio.investmentChange}
        className="md:col-span-1"
      />
      
      <MetricCard
        title="Total Gain"
        value={portfolio.totalGain}
        change={portfolio.gainChange}
        variant="gain"
        className="md:col-span-1"
      />
      
      <MetricCard
        title="Total Loss"
        value={portfolio.totalLoss}
        change={portfolio.lossChange}
        variant="loss"
        className="md:col-span-1"
      />
    </div>
  );
};