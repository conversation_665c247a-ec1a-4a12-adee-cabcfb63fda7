import React from 'react';
import { Card } from '@/components/ui/card';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import { LineChart, Line, XAxis, YAxis, ResponsiveContainer } from 'recharts';
import { ChartDataPoint, DateRange } from '../types';
import { formatDate, formatCompactCurrency } from '../utils/formatters';

interface InteractiveChartProps {
  chartData: ChartDataPoint[];
  dateRange: DateRange;
}

const chartConfig = {
  value: {
    label: "Stock Price",
    color: "hsl(var(--dashboard-chart-primary))",
  },
};

export const InteractiveChart: React.FC<InteractiveChartProps> = ({
  chartData,
  dateRange
}) => {
  return (
    <Card className="p-6 bg-dashboard-card border-dashboard-border">
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-400">
            {formatDate(dateRange.start)} - {formatDate(dateRange.end)}
          </div>
        </div>
        
        <div className="h-64 w-full">
          <ChartContainer config={chartConfig} className="w-full h-full">
            <LineChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
              <XAxis 
                dataKey="time" 
                axisLine={false}
                tickLine={false}
                tick={{ fill: '#9ca3af', fontSize: 12 }}
              />
              <YAxis 
                hide 
              />
              <ChartTooltip 
                content={<ChartTooltipContent 
                  formatter={(value) => [formatCompactCurrency(value as number), 'Stock Price']}
                  labelFormatter={(label) => `Time: ${label}`}
                />} 
              />
              <Line
                type="monotone"
                dataKey="value"
                stroke="url(#chartGradient)"
                strokeWidth={3}
                dot={false}
                activeDot={{ 
                  r: 6, 
                  fill: '#ff6b6b',
                  stroke: '#ffffff',
                  strokeWidth: 2
                }}
              />
              <defs>
                <linearGradient id="chartGradient" x1="0" y1="0" x2="1" y2="0">
                  <stop offset="0%" stopColor="#ff6b6b" />
                  <stop offset="50%" stopColor="#4ecdc4" />
                  <stop offset="100%" stopColor="#45b7d1" />
                </linearGradient>
              </defs>
            </LineChart>
          </ChartContainer>
        </div>
      </div>
    </Card>
  );
};