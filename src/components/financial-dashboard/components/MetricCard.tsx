import React from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { formatCurrency, formatPercentage } from '../utils/formatters';

interface MetricCardProps {
  title: string;
  value: number;
  change?: number;
  variant?: 'default' | 'gain' | 'loss';
  className?: string;
}

export const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  change,
  variant = 'default',
  className = ''
}) => {
  const getChangeColor = (change: number) => {
    if (change > 0) return 'text-dashboard-success';
    if (change < 0) return 'text-dashboard-accent';
    return 'text-gray-400';
  };

  const getVariantStyles = () => {
    switch (variant) {
      case 'gain':
        return 'border-dashboard-success/20 bg-dashboard-success/5';
      case 'loss':
        return 'border-dashboard-accent/20 bg-dashboard-accent/5';
      default:
        return 'border-dashboard-border bg-dashboard-card';
    }
  };

  return (
    <Card className={`p-4 ${getVariantStyles()} ${className}`}>
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <p className="text-sm text-gray-400">{title}</p>
          {change !== undefined && (
            <Badge 
              variant="outline" 
              className={`text-xs ${getChangeColor(change)} border-current`}
            >
              {formatPercentage(change)}
            </Badge>
          )}
        </div>
        <div className="text-2xl font-bold text-white">
          {formatCurrency(value)}
        </div>
      </div>
    </Card>
  );
};