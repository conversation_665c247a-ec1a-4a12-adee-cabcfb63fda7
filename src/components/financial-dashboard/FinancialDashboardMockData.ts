import { TimeFrame, TransactionType, MetricType } from './types';

// Data passed as props to the root component
export const mockRootProps = {
  company: {
    name: "Origin Game Inc.",
    symbol: "OREA",
    logoUrl: "https://images.unsplash.com/photo-1668903678362-0fbcb9108d53?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NTAwNDR8MHwxfHNlYXJjaHwxfHxsb2dvJTIwZ2FtaW5nJTIwdGVjaG5vbG9neSUyMGNpcmN1bGFyfGVufDB8Mnx8fDE3NTYzMTYyNzJ8MA&ixlib=rb-4.1.0&q=85",
    websiteUrl: "https://origingame.com"
  },
  portfolio: {
    totalValue: 8089.00,
    investment: 20619.00,
    totalGain: 8664.00,
    totalLoss: 1212.00,
    investmentChange: 28,
    gainChange: 22,
    lossChange: -20
  },
  shareHolders: {
    promoterPercentage: 50,
    promoterChange: 26
  },
  stockData: {
    currentPrice: 30089.00,
    previousClose: 17112.00,
    percentChange: 26,
    marketCap: "28 M USD",
    peRatio: 14.28,
    lastUpdated: new Date("2024-11-13T20:20:40")
  },
  chartData: [
    { date: "2024-11-11", value: 25000, time: "1" },
    { date: "2024-11-12", value: 22000, time: "11/11" },
    { date: "2024-11-13", value: 28000, time: "12/11" },
    { date: "2024-11-14", value: 32000, time: "13/11" },
    { date: "2024-11-15", value: 29000, time: "14/11" },
    { date: "2024-11-16", value: 31000, time: "15/11" },
    { date: "2024-11-17", value: 30089, time: "16/11" }
  ],
  dateRange: {
    start: new Date("2024-11-08"),
    end: new Date("2024-11-17")
  },
  selectedTimeFrame: TimeFrame.ONE_MONTH as const,
  availableTimeFrames: [
    TimeFrame.ONE_DAY,
    TimeFrame.ONE_WEEK, 
    TimeFrame.ONE_MONTH,
    TimeFrame.ONE_YEAR,
    TimeFrame.MAX
  ] as const
};