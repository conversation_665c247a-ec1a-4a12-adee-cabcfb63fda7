import React, { useState } from 'react';
import { CompanyHeader } from './components/CompanyHeader';
import { PortfolioStats } from './components/PortfolioStats';
import { ShareHolderChart } from './components/ShareHolderChart';
import { StockMetrics } from './components/StockMetrics';
import { TimeFrameSelector } from './components/TimeFrameSelector';
import { InteractiveChart } from './components/InteractiveChart';
import { ActionButtons } from './components/ActionButtons';
import { FinancialDashboardProps, TimeFrame } from './types';

export const FinancialDashboard: React.FC<FinancialDashboardProps> = ({
  company,
  portfolio,
  shareHolders,
  stockData,
  chartData,
  dateRange,
  selectedTimeFrame,
  availableTimeFrames,
  onTimeFrameChange,
  onBuyStock,
  onSellStock,
  onDeposit,
  onWithdraw,
  onVisitWebsite
}) => {
  const [currentTimeFrame, setCurrentTimeFrame] = useState<TimeFrame>(selectedTimeFrame);

  const handleTimeFrameChange = (timeFrame: TimeFrame) => {
    setCurrentTimeFrame(timeFrame);
    if (onTimeFrameChange) {
      onTimeFrameChange(timeFrame);
    }
  };

  const handleBuyStock = () => {
    if (onBuyStock) {
      onBuyStock();
    } else {
      console.log('Buy stock clicked');
    }
  };

  const handleSellStock = () => {
    if (onSellStock) {
      onSellStock();
    } else {
      console.log('Sell stock clicked');
    }
  };

  const handleDeposit = () => {
    if (onDeposit) {
      onDeposit();
    } else {
      console.log('Deposit clicked');
    }
  };

  const handleWithdraw = () => {
    if (onWithdraw) {
      onWithdraw();
    } else {
      console.log('Withdraw clicked');
    }
  };

  return (
    <div className="min-h-screen bg-dashboard-bg p-4 space-y-6">
      {/* Company Header */}
      <CompanyHeader 
        company={company} 
        onVisitWebsite={onVisitWebsite}
      />

      {/* Portfolio Statistics */}
      <PortfolioStats portfolio={portfolio} />

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Share Holder Chart */}
        <div className="lg:col-span-1">
          <ShareHolderChart shareHolders={shareHolders} />
        </div>

        {/* Middle Column - Stock Metrics */}
        <div className="lg:col-span-1">
          <StockMetrics stockData={stockData} />
        </div>

        {/* Right Column - Time Frame Selector */}
        <div className="lg:col-span-1 flex items-start justify-end">
          <TimeFrameSelector
            selectedTimeFrame={currentTimeFrame}
            availableTimeFrames={availableTimeFrames}
            onTimeFrameChange={handleTimeFrameChange}
          />
        </div>
      </div>

      {/* Interactive Chart */}
      <InteractiveChart 
        chartData={chartData}
        dateRange={dateRange}
      />

      {/* Action Buttons */}
      <ActionButtons
        onBuyStock={handleBuyStock}
        onSellStock={handleSellStock}
        onDeposit={handleDeposit}
        onWithdraw={handleWithdraw}
      />
    </div>
  );
};