/**
 * Session utility functions
 * Extracted from ClaudeCodeSession.tsx to promote reusability
 */

/**
 * Detects if a text contains a question pattern
 * This helps identify when <PERSON> is asking for clarification or confirmation
 * 
 * @param text - The text to analyze
 * @returns true if the text appears to be a question
 */
export function detectQuestion(text: string): boolean {
  // Common question patterns
  const questionPatterns = [
    /\?$/m,  // Ends with question mark
    /\?\s*$/m,  // Ends with question mark and whitespace
    /^(Would you|Do you|Can you|Should I|May I|Could you|Will you|Is it|Are you|Have you|What|When|Where|How|Why|Which)/mi,
    /(would you like|do you want|should I|can I|may I|shall I|need me to|want me to)/i,
    /(please (confirm|specify|provide|clarify|choose|select|let me know|tell me))/i,
    /(yes or no|y\/n|\(y\/n\))/i,
    /(proceed\?|continue\?|go ahead\?)/i
  ];
  
  return questionPatterns.some(pattern => pattern.test(text));
}

/**
 * Extracts text content from a <PERSON> message
 * 
 * @param message - The message object to extract text from
 * @returns The extracted text content or empty string
 */
export function extractMessageText(message: any): string {
  if (!message?.message?.content) return '';
  
  const textContent = message.message.content
    .filter((item: any) => item.type === 'text')
    .map((item: any) => item.text || '')
    .join(' ')
    .trim();
    
  return textContent;
}

/**
 * Calculates token usage from messages
 * 
 * @param messages - Array of Claude stream messages
 * @returns Total token count
 */
export function calculateTokenUsage(messages: any[]): number {
  return messages.reduce((total, msg) => {
    if (msg.usage) {
      return total + (msg.usage.input_tokens || 0) + (msg.usage.output_tokens || 0);
    }
    if (msg.message?.usage) {
      return total + (msg.message.usage.input_tokens || 0) + (msg.message.usage.output_tokens || 0);
    }
    return total;
  }, 0);
}

/**
 * Extracts session ID from a Claude init message
 * 
 * @param message - The message to extract session ID from
 * @returns The session ID if found, null otherwise
 */
export function extractSessionId(message: any): string | null {
  if (message?.type === 'system' && message?.subtype === 'init' && message?.session_id) {
    return message.session_id;
  }
  
  // Try to extract from message content
  if (message?.message?.content) {
    for (const item of message.message.content) {
      if (item.type === 'text' && item.text?.includes('Session ID:')) {
        const match = item.text.match(/Session ID:\s*([a-zA-Z0-9-]+)/);
        if (match) return match[1];
      }
    }
  }
  
  return null;
}

/**
 * Formats a timestamp for display
 * 
 * @param timestamp - Unix timestamp or ISO string
 * @returns Formatted time string
 */
export function formatSessionTime(timestamp: number | string): string {
  const date = typeof timestamp === 'string' ? new Date(timestamp) : new Date(timestamp);
  return date.toLocaleTimeString('en-US', { 
    hour: '2-digit', 
    minute: '2-digit', 
    second: '2-digit' 
  });
}

/**
 * Checks if a message indicates an error state
 * 
 * @param message - The message to check
 * @returns true if the message indicates an error
 */
export function isErrorMessage(message: any): boolean {
  if (message?.type === 'result' && message?.result?.exit_code !== 0) {
    return true;
  }
  
  if (message?.subtype === 'error') {
    return true;
  }
  
  const textContent = extractMessageText(message).toLowerCase();
  return textContent.includes('error') || textContent.includes('failed') || textContent.includes('exception');
}

/**
 * Groups messages by checkpoint for timeline view
 * 
 * @param messages - Array of messages to group
 * @returns Grouped messages by checkpoint ID
 */
export function groupMessagesByCheckpoint(messages: any[]): Map<string, any[]> {
  const groups = new Map<string, any[]>();
  let currentCheckpoint = 'initial';
  
  for (const message of messages) {
    if (message.checkpoint_id) {
      currentCheckpoint = message.checkpoint_id;
    }
    
    if (!groups.has(currentCheckpoint)) {
      groups.set(currentCheckpoint, []);
    }
    
    groups.get(currentCheckpoint)!.push(message);
  }
  
  return groups;
}

/**
 * Sanitizes user input to prevent injection attacks
 * 
 * @param input - The user input to sanitize
 * @returns Sanitized input string
 */
export function sanitizePrompt(input: string): string {
  // Remove potential command injection patterns
  return input
    .replace(/[;&|`$]/g, '') // Remove shell metacharacters
    .replace(/\.\.\//g, '') // Remove directory traversal
    .trim();
}

/**
 * Determines if a session can be resumed
 * 
 * @param session - The session metadata
 * @returns true if the session can be resumed
 */
export function canResumeSession(session: any): boolean {
  if (!session?.id || !session?.project_path) return false;
  
  // Check if session is not too old (24 hours)
  const sessionAge = Date.now() - (session.created_at || 0);
  const maxAge = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
  
  return sessionAge < maxAge;
}

/**
 * Generates a session display name
 * 
 * @param session - The session metadata
 * @returns Display name for the session
 */
export function getSessionDisplayName(session: any): string {
  if (session?.first_message) {
    // Truncate to first 50 characters
    const truncated = session.first_message.substring(0, 50);
    return truncated + (session.first_message.length > 50 ? '...' : '');
  }
  
  if (session?.id) {
    return `Session ${session.id.substring(0, 8)}`;
  }
  
  return 'Unnamed Session';
}

/**
 * Checks if a message should trigger an auto-checkpoint
 * 
 * @param message - The message to check
 * @param messageCount - Current number of messages in session
 * @returns true if an auto-checkpoint should be created
 */
export function shouldAutoCheckpoint(message: any, messageCount: number): boolean {
  // Checkpoint every 10 messages
  if (messageCount % 10 === 0) return true;
  
  // Checkpoint on significant events
  if (message?.type === 'result') return true;
  if (message?.subtype === 'error') return true;
  
  // Checkpoint when files are modified
  const textContent = extractMessageText(message).toLowerCase();
  if (textContent.includes('created') || textContent.includes('modified') || textContent.includes('deleted')) {
    return true;
  }
  
  return false;
}