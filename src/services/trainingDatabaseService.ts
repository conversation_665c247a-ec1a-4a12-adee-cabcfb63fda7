/**
 * Training Database Service
 * Provides TypeScript interface for training database operations
 */

import { invoke } from '@tauri-apps/api/core';

// Types matching the Rust structs
export interface TrainingNeed {
  id?: number;
  employee_id: string;
  employee_name: string;
  department: string;
  role: string;
  skill_category: string;
  skill_name: string;
  current_level: number;
  required_level: number;
  priority: 'Low' | 'Medium' | 'High' | 'Critical';
  business_impact: string;
  deadline?: string;
  status: 'Identified' | 'Approved' | 'InProgress' | 'Completed' | 'Cancelled';
  justification: string;
  estimated_cost?: number;
  estimated_duration_hours?: number;
  manager_id?: string;
  created_by: string;
  notes?: string;
}

export interface TrainingProgram {
  id?: number;
  name: string;
  description: string;
  category: 'Technical' | 'Leadership' | 'Compliance' | 'SoftSkills' | 'Safety' | 'Other';
  provider: string;
  delivery_method: 'InPerson' | 'Online' | 'Hybrid' | 'SelfPaced';
  duration_hours: number;
  cost_per_participant: number;
  max_participants?: number;
  prerequisites?: string;
  learning_objectives: string[];
  target_audience: string;
  difficulty_level: 'Beginner' | 'Intermediate' | 'Advanced';
  certification_provided: boolean;
  is_active: boolean;
  created_by: string;
}

export interface GraduateTraineeProgram {
  id?: number;
  employee_id: string;
  employee_name: string;
  program_name: string;
  start_date: string;
  expected_end_date: string;
  current_rotation?: string;
  rotation_history: string[];
  mentor_id?: string;
  status: 'Active' | 'Completed' | 'OnHold' | 'Terminated';
  overall_progress: number;
  quarterly_reviews: QuarterlyReview[];
  skill_developments: SkillDevelopment[];
  certifications: Certification[];
  notes?: string;
}

export interface QuarterlyReview {
  quarter: string;
  year: number;
  performance_rating: number;
  feedback: string;
  goals_achieved: string[];
  areas_for_improvement: string[];
  next_quarter_goals: string[];
  reviewer_id: string;
  review_date: string;
}

export interface SkillDevelopment {
  skill_name: string;
  category: string;
  initial_level: number;
  current_level: number;
  target_level: number;
  development_activities: string[];
  assessment_date: string;
  assessor_id: string;
}

export interface Certification {
  name: string;
  provider: string;
  date_obtained: string;
  expiry_date?: string;
  certificate_url?: string;
  verification_code?: string;
}

export interface TrainingMetrics {
  total_training_needs: number;
  completed_training_needs: number;
  in_progress_training_needs: number;
  total_training_programs: number;
  active_training_programs: number;
  total_graduate_trainees: number;
  active_graduate_trainees: number;
  average_completion_rate: number;
  total_training_cost: number;
  average_training_duration: number;
}

export interface CompetencyModel {
  id?: number;
  name: string;
  description?: string;
  category: string;
  department?: string;
  role_level?: string;
  competencies: Competency[];
  business_criticality?: string;
  industry_standard?: string;
  version: string;
  is_active: boolean;
  created_by?: string;
  approved_by?: string;
  approval_date?: string;
}

export interface Competency {
  name: string;
  description: string;
  category: string;
  required_level: number;
  current_level?: number;
  importance: string;
  behavioral_indicators: string[];
}

export interface SkillMatrix {
  id?: number;
  name: string;
  description?: string;
  department?: string;
  role?: string;
  skills: Skill[];
  proficiency_levels: ProficiencyLevel[];
  assessment_criteria?: AssessmentCriteria;
  created_by?: string;
  is_template: boolean;
}

export interface Skill {
  name: string;
  category: string;
  description: string;
  required_level: number;
  current_level?: number;
  importance: string;
  learning_resources: string[];
}

export interface ProficiencyLevel {
  level: number;
  name: string;
  description: string;
  criteria: string[];
}

export interface AssessmentCriteria {
  method: string;
  frequency: string;
  assessors: string[];
  scoring_rubric: Record<string, string>;
}

export interface Assessment {
  id?: number;
  employee_id: string;
  competency_id?: number;
  skill_id: string;
  score: number;
  max_score: number;
  assessment_date: string;
  assessor_id: string;
  assessment_type: string;
  notes?: string;
}

/**
 * Training Database Service Class
 */
export class TrainingDatabaseService {
  // Training Needs Operations
  static async createTrainingNeed(trainingNeed: TrainingNeed): Promise<number> {
    return await invoke('create_training_need', { trainingNeed });
  }

  static async getTrainingNeed(id: number): Promise<TrainingNeed | null> {
    return await invoke('get_training_need', { id });
  }

  static async getTrainingNeedsByEmployee(employeeId: string): Promise<TrainingNeed[]> {
    return await invoke('get_training_needs_by_employee', { employeeId });
  }

  static async getTrainingNeedsByDepartment(department: string): Promise<TrainingNeed[]> {
    return await invoke('get_training_needs_by_department', { department });
  }

  static async updateTrainingNeed(id: number, trainingNeed: TrainingNeed): Promise<void> {
    return await invoke('update_training_need', { id, trainingNeed });
  }

  static async deleteTrainingNeed(id: number): Promise<void> {
    return await invoke('delete_training_need', { id });
  }

  // Training Programs Operations
  static async createTrainingProgram(program: TrainingProgram): Promise<number> {
    return await invoke('create_training_program', { program });
  }

  static async getTrainingPrograms(): Promise<TrainingProgram[]> {
    return await invoke('get_training_programs');
  }

  static async getTrainingProgramsByCategory(category: string): Promise<TrainingProgram[]> {
    return await invoke('get_training_programs_by_category', { category });
  }

  // Graduate Trainee Programs Operations
  static async createGraduateTraineeProgram(program: GraduateTraineeProgram): Promise<number> {
    return await invoke('create_graduate_trainee_program', { program });
  }

  static async getGraduateTraineePrograms(): Promise<GraduateTraineeProgram[]> {
    return await invoke('get_graduate_trainee_programs');
  }

  static async getGraduateTraineeProgramByEmployee(employeeId: string): Promise<GraduateTraineeProgram | null> {
    return await invoke('get_graduate_trainee_program_by_employee', { employeeId });
  }

  // Analytics Operations
  static async getTrainingMetrics(): Promise<TrainingMetrics> {
    return await invoke('get_training_metrics');
  }

  // Competency Models Operations
  static async createCompetencyModel(model: CompetencyModel): Promise<number> {
    return await invoke('create_competency_model', { model });
  }

  static async getCompetencyModels(): Promise<CompetencyModel[]> {
    return await invoke('get_competency_models');
  }

  // Skill Matrix Operations
  static async createSkillMatrix(matrix: SkillMatrix): Promise<number> {
    return await invoke('create_skill_matrix', { matrix });
  }

  static async getSkillMatrices(): Promise<SkillMatrix[]> {
    return await invoke('get_skill_matrices');
  }

  static async getSkillMatrixTemplates(): Promise<SkillMatrix[]> {
    return await invoke('get_skill_matrix_templates');
  }

  // Assessment Operations
  static async createAssessment(assessment: Assessment): Promise<number> {
    return await invoke('create_assessment', { assessment });
  }

  static async getAssessmentsByEmployee(employeeId: string): Promise<Assessment[]> {
    return await invoke('get_assessments_by_employee', { employeeId });
  }

  // Utility Methods
  static async initializeSampleData(): Promise<void> {
    try {
      // Create sample competency models
      const sampleCompetencyModel: CompetencyModel = {
        name: "Software Engineering Competencies",
        description: "Core competencies for software engineers",
        category: "Technical",
        department: "Engineering",
        role_level: "Mid-Level",
        competencies: [
          {
            name: "Programming Languages",
            description: "Proficiency in multiple programming languages",
            category: "Technical",
            required_level: 4,
            current_level: 3,
            importance: "High",
            behavioral_indicators: [
              "Writes clean, maintainable code",
              "Follows coding standards and best practices",
              "Can debug complex issues efficiently"
            ]
          },
          {
            name: "System Design",
            description: "Ability to design scalable systems",
            category: "Technical",
            required_level: 3,
            current_level: 2,
            importance: "High",
            behavioral_indicators: [
              "Designs systems with scalability in mind",
              "Considers performance implications",
              "Documents architectural decisions"
            ]
          }
        ],
        business_criticality: "High",
        industry_standard: "IEEE Software Engineering Standards",
        version: "1.0",
        is_active: true,
        created_by: "system"
      };

      await this.createCompetencyModel(sampleCompetencyModel);

      // Create sample skill matrix
      const sampleSkillMatrix: SkillMatrix = {
        name: "Frontend Developer Skills",
        description: "Skills matrix for frontend developers",
        department: "Engineering",
        role: "Frontend Developer",
        skills: [
          {
            name: "React",
            category: "Frontend Framework",
            description: "React.js library for building user interfaces",
            required_level: 4,
            current_level: 3,
            importance: "High",
            learning_resources: [
              "React Official Documentation",
              "React Hooks Tutorial",
              "Advanced React Patterns Course"
            ]
          },
          {
            name: "TypeScript",
            category: "Programming Language",
            description: "Typed superset of JavaScript",
            required_level: 3,
            current_level: 2,
            importance: "Medium",
            learning_resources: [
              "TypeScript Handbook",
              "TypeScript Deep Dive",
              "Advanced TypeScript Course"
            ]
          }
        ],
        proficiency_levels: [
          {
            level: 1,
            name: "Beginner",
            description: "Basic understanding and can perform simple tasks",
            criteria: [
              "Can follow tutorials",
              "Understands basic concepts",
              "Requires significant guidance"
            ]
          },
          {
            level: 2,
            name: "Intermediate",
            description: "Can work independently on standard tasks",
            criteria: [
              "Can solve common problems",
              "Understands best practices",
              "Requires minimal guidance"
            ]
          },
          {
            level: 3,
            name: "Advanced",
            description: "Can handle complex tasks and mentor others",
            criteria: [
              "Can solve complex problems",
              "Can mentor junior developers",
              "Contributes to architectural decisions"
            ]
          },
          {
            level: 4,
            name: "Expert",
            description: "Subject matter expert who can lead and innovate",
            criteria: [
              "Recognized as subject matter expert",
              "Can lead technical initiatives",
              "Drives innovation and best practices"
            ]
          }
        ],
        assessment_criteria: {
          method: "Practical Assessment + Peer Review",
          frequency: "Quarterly",
          assessors: ["Technical Lead", "Senior Developer", "Peer"],
          scoring_rubric: {
            "Code Quality": "40%",
            "Problem Solving": "30%",
            "Knowledge Application": "20%",
            "Collaboration": "10%"
          }
        },
        created_by: "system",
        is_template: true
      };

      await this.createSkillMatrix(sampleSkillMatrix);

      console.log('Sample training data initialized successfully');
    } catch (error) {
      console.error('Failed to initialize sample training data:', error);
      throw error;
    }
  }
}

export default TrainingDatabaseService;