/**
 * Session Service Layer
 * Centralizes all session-related API calls and business logic
 */

import { invoke } from '@tauri-apps/api/core';
import { 
  SessionMetadata,
  ExecuteClaudeCodeParams,
  ContinueClaudeCodeParams,
  ResumeClaudeCodeParams,
  CancelClaudeExecutionParams,
  SessionCheckpoint,
  ClaudeStreamMessage,
  ClaudeCodeSession
} from '@/types/session';

export type { ClaudeCodeSession };

/**
 * Service class for managing Claude sessions
 */
export class SessionService {
  private static instance: SessionService;
  
  private constructor() {}
  
  /**
   * Get singleton instance
   */
  public static getInstance(): SessionService {
    if (!SessionService.instance) {
      SessionService.instance = new SessionService();
    }
    return SessionService.instance;
  }
  
  /**
   * Execute a new Claude Code session
   */
  async executeClaudeCode(params: ExecuteClaudeCodeParams): Promise<string> {
    try {
      const result = await invoke<string>('execute_claude_code', params as any);
      return result;
    } catch (error) {
      throw this.handleError(error);
    }
  }
  
  /**
   * Continue an existing Claude Code session
   */
  async continueClaudeCode(params: ContinueClaudeCodeParams): Promise<void> {
    try {
      await invoke('continue_claude_code', params as any);
    } catch (error) {
      throw this.handleError(error);
    }
  }
  
  /**
   * Resume a Claude Code session from checkpoint
   */
  async resumeClaudeCode(params: ResumeClaudeCodeParams): Promise<void> {
    try {
      await invoke('resume_claude_code', params as any);
    } catch (error) {
      throw this.handleError(error);
    }
  }
  
  /**
   * Cancel a running Claude execution
   */
  async cancelClaudeExecution(params: CancelClaudeExecutionParams): Promise<void> {
    try {
      await invoke('cancel_claude_execution', params as any);
    } catch (error) {
      throw this.handleError(error);
    }
  }
  
  /**
   * Get project sessions
   */
  async getProjectSessions(projectId: string): Promise<SessionMetadata[]> {
    try {
      const sessions = await invoke<SessionMetadata[]>('get_project_sessions', { projectId });
      return sessions;
    } catch (error) {
      throw this.handleError(error);
    }
  }
  
  /**
   * Load session history
   */
  async loadSessionHistory(sessionId: string, projectPath: string): Promise<ClaudeStreamMessage[]> {
    try {
      const history = await invoke<ClaudeStreamMessage[]>('load_session_history', {
        sessionId,
        projectPath
      });
      return history;
    } catch (error) {
      throw this.handleError(error);
    }
  }
  
  /**
   * Workflow Integration Methods
   */
  
  /**
   * Create a session for workflow execution
   */
  async createWorkflowSession(params: {
    workflowId: string;
    executionId: string;
    nodeId: string;
    context?: Record<string, any>;
  }): Promise<ClaudeCodeSession> {
    const session: ClaudeCodeSession = {
      id: `workflow-${params.executionId}-${params.nodeId}`,
      name: `Workflow Node: ${params.nodeId}`,
      status: 'idle',
      startTime: new Date().toISOString(),
      contextWindow: 200000,
      messages: [],
      metadata: {
        workflowId: params.workflowId,
        nodeId: params.nodeId,
        executionId: params.executionId,
        ...params.context,
      },
    };
    
    // Store session in backend
    await invoke('store_workflow_session', { session });
    
    return session;
  }
  
  /**
   * Execute Claude Code within a workflow context
   */
  async executeWorkflowClaudeCode(params: {
    sessionId: string;
    prompt: string;
    context: Record<string, any>;
    workflowId: string;
    nodeId: string;
    executionId: string;
  }): Promise<any> {
    try {
      // Enrich the prompt with workflow context
      const enrichedPrompt = this.enrichPromptWithWorkflowContext(params.prompt, params.context);
      
      // Execute Claude Code with workflow metadata
      const result = await this.executeClaudeCode({
        projectPath: params.context.projectPath || '.',
        prompt: enrichedPrompt,
        model: params.context.model || 'claude-3-sonnet-20240229',
        sessionId: params.sessionId,
        metadata: {
          workflowId: params.workflowId,
          nodeId: params.nodeId,
          executionId: params.executionId,
        },
      } as ExecuteClaudeCodeParams);
      
      // Track execution in workflow analytics
      await invoke('track_workflow_claude_execution', {
        workflowId: params.workflowId,
        executionId: params.executionId,
        nodeId: params.nodeId,
        sessionId: params.sessionId,
        result,
      });
      
      return result;
    } catch (error) {
      throw this.handleError(error);
    }
  }
  
  /**
   * Get all sessions associated with a workflow execution
   */
  async getWorkflowSessions(workflowId: string, executionId?: string): Promise<ClaudeCodeSession[]> {
    try {
      const sessions = await invoke<ClaudeCodeSession[]>('get_workflow_sessions', {
        workflowId,
        executionId,
      });
      return sessions;
    } catch (error) {
      throw this.handleError(error);
    }
  }
  
  /**
   * Get aggregated metrics for all sessions in a workflow
   */
  async getWorkflowSessionMetrics(workflowId: string): Promise<{
    totalSessions: number;
    totalTokens: number;
    totalCost: number;
    avgDuration: number;
    successRate: number;
  }> {
    try {
      const metrics = await invoke<any>('get_workflow_session_metrics', { workflowId });
      return metrics;
    } catch (error) {
      throw this.handleError(error);
    }
  }
  
  /**
   * Enrich prompt with workflow context
   */
  private enrichPromptWithWorkflowContext(prompt: string, context: Record<string, any>): string {
    let enrichedPrompt = prompt;
    
    // Add workflow context if available
    if (context.previousNodeOutputs) {
      enrichedPrompt = `Previous step outputs:\n${JSON.stringify(context.previousNodeOutputs, null, 2)}\n\n${prompt}`;
    }
    
    if (context.workflowVariables) {
      enrichedPrompt = `Workflow variables:\n${JSON.stringify(context.workflowVariables, null, 2)}\n\n${enrichedPrompt}`;
    }
    
    return enrichedPrompt;
  }
  
  /**
   * Create a checkpoint
   */
  async createCheckpoint(
    sessionId: string,
    projectPath: string,
    description?: string,
    significance?: 'minor' | 'major' | 'critical'
  ): Promise<SessionCheckpoint> {
    try {
      const checkpoint = await invoke<SessionCheckpoint>('create_checkpoint', {
        sessionId,
        projectPath,
        description,
        significance: significance || 'minor'
      });
      return checkpoint;
    } catch (error) {
      throw this.handleError(error);
    }
  }
  
  /**
   * Restore from checkpoint
   */
  async restoreCheckpoint(
    checkpointId: string,
    projectPath: string
  ): Promise<void> {
    try {
      await invoke('restore_checkpoint', {
        checkpointId,
        projectPath
      });
    } catch (error) {
      throw this.handleError(error);
    }
  }
  
  /**
   * List checkpoints for a session
   */
  async listCheckpoints(sessionId: string, projectPath: string): Promise<SessionCheckpoint[]> {
    try {
      const checkpoints = await invoke<SessionCheckpoint[]>('list_checkpoints', {
        sessionId,
        projectPath
      });
      return checkpoints;
    } catch (error) {
      throw this.handleError(error);
    }
  }
  
  /**
   * Fork from checkpoint
   */
  async forkFromCheckpoint(
    checkpointId: string,
    newSessionName: string,
    projectPath: string
  ): Promise<string> {
    try {
      const newSessionId = await invoke<string>('fork_from_checkpoint', {
        checkpointId,
        newSessionName,
        projectPath
      });
      return newSessionId;
    } catch (error) {
      throw this.handleError(error);
    }
  }
  
  /**
   * Get session timeline
   */
  async getSessionTimeline(sessionId: string, projectPath: string): Promise<any> {
    try {
      const timeline = await invoke('get_session_timeline', {
        sessionId,
        projectPath
      });
      return timeline;
    } catch (error) {
      throw this.handleError(error);
    }
  }
  
  /**
   * Send session to background
   */
  async sendToBackground(sessionId: string): Promise<void> {
    try {
      await invoke('send_to_background', { sessionId });
    } catch (error) {
      throw this.handleError(error);
    }
  }
  
  /**
   * Bring session to foreground
   */
  async bringToForeground(sessionId: string): Promise<void> {
    try {
      await invoke('bring_to_foreground', { sessionId });
    } catch (error) {
      throw this.handleError(error);
    }
  }
  
  /**
   * Approve plan execution
   */
  async approvePlanExecution(sessionId: string): Promise<void> {
    try {
      await invoke('approve_plan_execution', { sessionId });
    } catch (error) {
      throw this.handleError(error);
    }
  }
  
  /**
   * Reject plan execution
   */
  async rejectPlanExecution(sessionId: string): Promise<void> {
    try {
      await invoke('reject_plan_execution', { sessionId });
    } catch (error) {
      throw this.handleError(error);
    }
  }
  
  /**
   * Get running Claude sessions
   */
  async listRunningSessions(): Promise<any[]> {
    try {
      const sessions = await invoke<any[]>('list_running_claude_sessions');
      return sessions;
    } catch (error) {
      throw this.handleError(error);
    }
  }
  
  /**
   * Get session output
   */
  async getSessionOutput(runId: number): Promise<string> {
    try {
      const output = await invoke<string>('get_claude_session_output', { runId });
      return output;
    } catch (error) {
      throw this.handleError(error);
    }
  }
  
  /**
   * Track session messages for metrics
   */
  async trackSessionMessages(
    sessionId: string,
    projectPath: string,
    messageCount: number,
    significance?: 'minor' | 'major' | 'critical'
  ): Promise<boolean> {
    try {
      const shouldCheckpoint = await invoke<boolean>('track_session_messages', {
        sessionId,
        projectPath,
        messageCount,
        significance
      });
      return shouldCheckpoint;
    } catch (error) {
      return false;
    }
  }
  
  /**
   * Handle and transform errors
   */
  private handleError(error: any): Error {
    if (typeof error === 'string') {
      return new Error(error);
    }
    
    if (error instanceof Error) {
      return error;
    }
    
    return new Error('An unknown error occurred');
  }
}

// Export singleton instance
export const sessionService = SessionService.getInstance();

// Export convenience functions for direct use
export const {
  executeClaudeCode,
  continueClaudeCode,
  resumeClaudeCode,
  cancelClaudeExecution,
  getProjectSessions,
  loadSessionHistory,
  createCheckpoint,
  restoreCheckpoint,
  listCheckpoints,
  forkFromCheckpoint,
  getSessionTimeline,
  sendToBackground,
  bringToForeground,
  approvePlanExecution,
  rejectPlanExecution,
  listRunningSessions,
  getSessionOutput,
  trackSessionMessages
} = sessionService;