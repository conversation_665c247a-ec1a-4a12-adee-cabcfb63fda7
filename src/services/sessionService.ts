/**
 * Session Service Layer
 * Centralizes all session-related API calls and business logic
 */

import { invoke } from '@tauri-apps/api/tauri';
import { 
  SessionMetadata,
  ExecuteClaudeCodeParams,
  ContinueClaudeCodeParams,
  ResumeClaudeCodeParams,
  CancelClaudeExecutionParams,
  SessionCheckpoint,
  ClaudeStreamMessage
} from '@/types/session';

/**
 * Service class for managing Claude sessions
 */
export class SessionService {
  private static instance: SessionService;
  
  private constructor() {}
  
  /**
   * Get singleton instance
   */
  public static getInstance(): SessionService {
    if (!SessionService.instance) {
      SessionService.instance = new SessionService();
    }
    return SessionService.instance;
  }
  
  /**
   * Execute a new Claude Code session
   */
  async executeClaudeCode(params: ExecuteClaudeCodeParams): Promise<string> {
    try {
      console.log('[SessionService] Executing Claude Code:', params);
      const result = await invoke<string>('execute_claude_code', params);
      return result;
    } catch (error) {
      console.error('[SessionService] Error executing Claude Code:', error);
      throw this.handleError(error);
    }
  }
  
  /**
   * Continue an existing Claude Code session
   */
  async continueClaudeCode(params: ContinueClaudeCodeParams): Promise<void> {
    try {
      console.log('[SessionService] Continuing Claude Code:', params);
      await invoke('continue_claude_code', params);
    } catch (error) {
      console.error('[SessionService] Error continuing Claude Code:', error);
      throw this.handleError(error);
    }
  }
  
  /**
   * Resume a Claude Code session from checkpoint
   */
  async resumeClaudeCode(params: ResumeClaudeCodeParams): Promise<void> {
    try {
      console.log('[SessionService] Resuming Claude Code:', params);
      await invoke('resume_claude_code', params);
    } catch (error) {
      console.error('[SessionService] Error resuming Claude Code:', error);
      throw this.handleError(error);
    }
  }
  
  /**
   * Cancel a running Claude execution
   */
  async cancelClaudeExecution(params: CancelClaudeExecutionParams): Promise<void> {
    try {
      console.log('[SessionService] Canceling Claude execution:', params);
      await invoke('cancel_claude_execution', params);
    } catch (error) {
      console.error('[SessionService] Error canceling execution:', error);
      throw this.handleError(error);
    }
  }
  
  /**
   * Get project sessions
   */
  async getProjectSessions(projectId: string): Promise<SessionMetadata[]> {
    try {
      const sessions = await invoke<SessionMetadata[]>('get_project_sessions', { projectId });
      return sessions;
    } catch (error) {
      console.error('[SessionService] Error getting project sessions:', error);
      throw this.handleError(error);
    }
  }
  
  /**
   * Load session history
   */
  async loadSessionHistory(sessionId: string, projectPath: string): Promise<ClaudeStreamMessage[]> {
    try {
      const history = await invoke<ClaudeStreamMessage[]>('load_session_history', {
        sessionId,
        projectPath
      });
      return history;
    } catch (error) {
      console.error('[SessionService] Error loading session history:', error);
      throw this.handleError(error);
    }
  }
  
  /**
   * Create a checkpoint
   */
  async createCheckpoint(
    sessionId: string,
    projectPath: string,
    description?: string,
    significance?: 'minor' | 'major' | 'critical'
  ): Promise<SessionCheckpoint> {
    try {
      const checkpoint = await invoke<SessionCheckpoint>('create_checkpoint', {
        sessionId,
        projectPath,
        description,
        significance: significance || 'minor'
      });
      return checkpoint;
    } catch (error) {
      console.error('[SessionService] Error creating checkpoint:', error);
      throw this.handleError(error);
    }
  }
  
  /**
   * Restore from checkpoint
   */
  async restoreCheckpoint(
    checkpointId: string,
    projectPath: string
  ): Promise<void> {
    try {
      await invoke('restore_checkpoint', {
        checkpointId,
        projectPath
      });
    } catch (error) {
      console.error('[SessionService] Error restoring checkpoint:', error);
      throw this.handleError(error);
    }
  }
  
  /**
   * List checkpoints for a session
   */
  async listCheckpoints(sessionId: string, projectPath: string): Promise<SessionCheckpoint[]> {
    try {
      const checkpoints = await invoke<SessionCheckpoint[]>('list_checkpoints', {
        sessionId,
        projectPath
      });
      return checkpoints;
    } catch (error) {
      console.error('[SessionService] Error listing checkpoints:', error);
      throw this.handleError(error);
    }
  }
  
  /**
   * Fork from checkpoint
   */
  async forkFromCheckpoint(
    checkpointId: string,
    newSessionName: string,
    projectPath: string
  ): Promise<string> {
    try {
      const newSessionId = await invoke<string>('fork_from_checkpoint', {
        checkpointId,
        newSessionName,
        projectPath
      });
      return newSessionId;
    } catch (error) {
      console.error('[SessionService] Error forking from checkpoint:', error);
      throw this.handleError(error);
    }
  }
  
  /**
   * Get session timeline
   */
  async getSessionTimeline(sessionId: string, projectPath: string): Promise<any> {
    try {
      const timeline = await invoke('get_session_timeline', {
        sessionId,
        projectPath
      });
      return timeline;
    } catch (error) {
      console.error('[SessionService] Error getting session timeline:', error);
      throw this.handleError(error);
    }
  }
  
  /**
   * Send session to background
   */
  async sendToBackground(sessionId: string): Promise<void> {
    try {
      await invoke('send_to_background', { sessionId });
    } catch (error) {
      console.error('[SessionService] Error sending to background:', error);
      throw this.handleError(error);
    }
  }
  
  /**
   * Bring session to foreground
   */
  async bringToForeground(sessionId: string): Promise<void> {
    try {
      await invoke('bring_to_foreground', { sessionId });
    } catch (error) {
      console.error('[SessionService] Error bringing to foreground:', error);
      throw this.handleError(error);
    }
  }
  
  /**
   * Approve plan execution
   */
  async approvePlanExecution(sessionId: string): Promise<void> {
    try {
      await invoke('approve_plan_execution', { sessionId });
    } catch (error) {
      console.error('[SessionService] Error approving plan:', error);
      throw this.handleError(error);
    }
  }
  
  /**
   * Reject plan execution
   */
  async rejectPlanExecution(sessionId: string): Promise<void> {
    try {
      await invoke('reject_plan_execution', { sessionId });
    } catch (error) {
      console.error('[SessionService] Error rejecting plan:', error);
      throw this.handleError(error);
    }
  }
  
  /**
   * Get running Claude sessions
   */
  async listRunningSessions(): Promise<any[]> {
    try {
      const sessions = await invoke<any[]>('list_running_claude_sessions');
      return sessions;
    } catch (error) {
      console.error('[SessionService] Error listing running sessions:', error);
      throw this.handleError(error);
    }
  }
  
  /**
   * Get session output
   */
  async getSessionOutput(runId: number): Promise<string> {
    try {
      const output = await invoke<string>('get_claude_session_output', { runId });
      return output;
    } catch (error) {
      console.error('[SessionService] Error getting session output:', error);
      throw this.handleError(error);
    }
  }
  
  /**
   * Track session messages for metrics
   */
  async trackSessionMessages(
    sessionId: string,
    projectPath: string,
    messageCount: number,
    significance?: 'minor' | 'major' | 'critical'
  ): Promise<boolean> {
    try {
      const shouldCheckpoint = await invoke<boolean>('track_session_messages', {
        sessionId,
        projectPath,
        messageCount,
        significance
      });
      return shouldCheckpoint;
    } catch (error) {
      console.error('[SessionService] Error tracking messages:', error);
      return false;
    }
  }
  
  /**
   * Handle and transform errors
   */
  private handleError(error: any): Error {
    if (typeof error === 'string') {
      return new Error(error);
    }
    
    if (error instanceof Error) {
      return error;
    }
    
    return new Error('An unknown error occurred');
  }
}

// Export singleton instance
export const sessionService = SessionService.getInstance();

// Export convenience functions for direct use
export const {
  executeClaudeCode,
  continueClaudeCode,
  resumeClaudeCode,
  cancelClaudeExecution,
  getProjectSessions,
  loadSessionHistory,
  createCheckpoint,
  restoreCheckpoint,
  listCheckpoints,
  forkFromCheckpoint,
  getSessionTimeline,
  sendToBackground,
  bringToForeground,
  approvePlanExecution,
  rejectPlanExecution,
  listRunningSessions,
  getSessionOutput,
  trackSessionMessages
} = sessionService;