// Lighthouse LM Service Layer - TypeScript/Tauri Bridge

import { invoke } from '@tauri-apps/api/core';
import { listen, emit, UnlistenFn } from '@tauri-apps/api/event';
import { Body, fetch } from '@tauri-apps/plugin-http';

// ============= Types =============

export interface Notebook {
  id: string;
  title: string;
  description?: string;
  created_at: string;
  updated_at: string;
  owner_id: string;
  is_public: boolean;
  tags: string[];
  metadata: Record<string, any>;
}

export interface Source {
  id: string;
  notebook_id: string;
  title: string;
  content: string;
  source_type: SourceType;
  url?: string;
  file_path?: string;
  metadata: SourceMetadata;
  created_at: string;
  updated_at: string;
  processed: boolean;
  embeddings?: number[];
  tags: string[];
}

export type SourceType = 'PDF' | 'Web' | 'Document' | 'Code' | 'Media' | 'Note' | 'External';

export interface SourceMetadata {
  size?: number;
  page_count?: number;
  word_count?: number;
  language?: string;
  author?: string;
  published_date?: string;
  last_accessed?: string;
  extraction_method?: string;
  confidence_score?: number;
}

export interface ChatMessage {
  id: string;
  notebook_id: string;
  role: MessageRole;
  content: string;
  model?: string;
  temperature?: number;
  max_tokens?: number;
  sources: string[];
  citations: Citation[];
  metadata: MessageMetadata;
  parent_id?: string;
  created_at: string;
  edited_at?: string;
  reactions: Reaction[];
}

export type MessageRole = 'user' | 'assistant' | 'system';

export interface MessageMetadata {
  tokens_used?: number;
  processing_time_ms?: number;
  confidence?: number;
  model_version?: string;
  attachments: Attachment[];
}

export interface Citation {
  source_id: string;
  source_title: string;
  excerpt: string;
  page_number?: number;
  relevance_score: number;
}

export interface Attachment {
  id: string;
  name: string;
  mime_type: string;
  size: number;
  url: string;
}

export interface Reaction {
  user_id: string;
  emoji: string;
  created_at: string;
}

export interface StudioDocument {
  id: string;
  notebook_id: string;
  title: string;
  content: string;
  document_type: DocumentType;
  version: number;
  created_at: string;
  updated_at: string;
  collaborators: string[];
  is_published: boolean;
  export_formats: string[];
}

export type DocumentType = 'Note' | 'Diagram' | 'Slide' | 'Code' | 'Markdown' | 'RichText';

export interface AIContext {
  id: string;
  notebook_id: string;
  selected_sources: string[];
  active_document?: string;
  chat_context: string;
  ai_model: string;
  temperature: number;
  max_tokens: number;
  system_prompt?: string;
  created_at: string;
  updated_at: string;
}

export interface SearchResult {
  sources: Source[];
  total: number;
  query: string;
  processing_time_ms: number;
}

export interface ExportOptions {
  format: 'pdf' | 'docx' | 'markdown' | 'html' | 'json';
  include_citations: boolean;
  include_metadata: boolean;
  template?: string;
}

export interface SyncStatus {
  is_syncing: boolean;
  last_sync?: string;
  errors?: string[];
  progress?: number;
}

// ============= Service Class =============

class LighthouseService {
  private static instance: LighthouseService;
  private listeners: Map<string, UnlistenFn> = new Map();
  private cache: Map<string, { data: any; expires: number }> = new Map();
  private pendingRequests: Map<string, Promise<any>> = new Map();
  
  private constructor() {
    this.setupEventListeners();
    this.startCacheCleanup();
  }
  
  static getInstance(): LighthouseService {
    if (!this.instance) {
      this.instance = new LighthouseService();
    }
    return this.instance;
  }
  
  // ============= Notebook Operations =============
  
  async createNotebook(title: string, description?: string): Promise<Notebook> {
    return this.invokeWithCache('create_notebook', { title, description });
  }
  
  async getNotebook(id: string): Promise<Notebook> {
    return this.invokeWithCache(`notebook_${id}`, 'get_notebook', { id }, 60000);
  }
  
  async listNotebooks(): Promise<Notebook[]> {
    return this.invokeWithCache('notebooks', 'list_notebooks', {}, 30000);
  }
  
  async updateNotebook(id: string, updates: Partial<Notebook>): Promise<Notebook> {
    this.invalidateCache(`notebook_${id}`);
    this.invalidateCache('notebooks');
    return invoke('update_notebook', { id, updates });
  }
  
  async deleteNotebook(id: string): Promise<void> {
    this.invalidateCache(`notebook_${id}`);
    this.invalidateCache('notebooks');
    await invoke('delete_notebook', { id });
  }
  
  // ============= Source Operations =============
  
  async addSource(
    notebookId: string,
    title: string,
    content: string,
    sourceType: SourceType
  ): Promise<Source> {
    this.invalidateCache(`sources_${notebookId}`);
    return invoke('add_source', {
      notebook_id: notebookId,
      title,
      content,
      source_type: sourceType,
    });
  }
  
  async uploadFile(notebookId: string, file: File): Promise<Source> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('notebook_id', notebookId);
    
    // Use Tauri's file system API to handle file upload
    const content = await this.readFileContent(file);
    
    return this.addSource(
      notebookId,
      file.name,
      content,
      this.getSourceTypeFromFile(file)
    );
  }
  
  async getSources(notebookId: string): Promise<Source[]> {
    return this.invokeWithCache(
      `sources_${notebookId}`,
      'get_sources',
      { notebook_id: notebookId },
      30000
    );
  }
  
  async searchSources(
    notebookId: string,
    query: string,
    limit?: number
  ): Promise<SearchResult> {
    const start = Date.now();
    const sources = await invoke<Source[]>('search_sources', {
      notebook_id: notebookId,
      query,
      limit,
    });
    
    return {
      sources,
      total: sources.length,
      query,
      processing_time_ms: Date.now() - start,
    };
  }
  
  async deleteSource(id: string): Promise<void> {
    await invoke('delete_source', { id });
    // Invalidate related caches
    this.cache.forEach((_, key) => {
      if (key.startsWith('sources_')) {
        this.invalidateCache(key);
      }
    });
  }
  
  // ============= Chat Operations =============
  
  async sendMessage(
    notebookId: string,
    content: string,
    options?: {
      sources?: string[];
      model?: string;
      temperature?: number;
      maxTokens?: number;
      attachments?: Attachment[];
    }
  ): Promise<ChatMessage> {
    this.invalidateCache(`messages_${notebookId}`);
    
    // Handle attachments if present
    if (options?.attachments) {
      // Process attachments...
    }
    
    return invoke('send_chat_message', {
      notebook_id: notebookId,
      content,
      sources: options?.sources || [],
      model: options?.model,
      temperature: options?.temperature,
      max_tokens: options?.maxTokens,
    });
  }
  
  async getMessages(notebookId: string, limit?: number): Promise<ChatMessage[]> {
    return this.invokeWithCache(
      `messages_${notebookId}`,
      'get_messages',
      { notebook_id: notebookId, limit },
      10000
    );
  }
  
  async editMessage(id: string, content: string): Promise<ChatMessage> {
    return invoke('edit_message', { id, content });
  }
  
  async deleteMessage(id: string): Promise<void> {
    await invoke('delete_message', { id });
  }
  
  async regenerateMessage(id: string): Promise<ChatMessage> {
    return invoke('regenerate_message', { id });
  }
  
  async addReaction(messageId: string, emoji: string): Promise<void> {
    await invoke('add_reaction', { message_id: messageId, emoji });
  }
  
  // ============= Studio Operations =============
  
  async createDocument(
    notebookId: string,
    title: string,
    content: string,
    documentType: DocumentType
  ): Promise<StudioDocument> {
    return invoke('create_studio_document', {
      notebook_id: notebookId,
      title,
      content,
      document_type: documentType,
    });
  }
  
  async updateDocument(
    id: string,
    updates: Partial<StudioDocument>
  ): Promise<StudioDocument> {
    return invoke('update_document', { id, updates });
  }
  
  async exportDocument(
    id: string,
    options: ExportOptions
  ): Promise<Blob> {
    const data = await invoke<string>('export_document', { id, options });
    return new Blob([data], { type: this.getMimeType(options.format) });
  }
  
  async publishDocument(id: string, platform: string): Promise<string> {
    return invoke('publish_document', { id, platform });
  }
  
  // ============= AI & Context Operations =============
  
  async syncContext(
    notebookId: string,
    selectedSources: string[],
    activeDocument?: string,
    chatContext?: string
  ): Promise<AIContext> {
    return invoke('sync_context', {
      notebook_id: notebookId,
      selected_sources: selectedSources,
      active_document: activeDocument,
      chat_context: chatContext || '',
    });
  }
  
  async generateSuggestions(
    notebookId: string,
    context: string
  ): Promise<string[]> {
    return invoke('generate_suggestions', {
      notebook_id: notebookId,
      context,
    });
  }
  
  async generateEmbeddings(text: string): Promise<number[]> {
    return invoke('generate_embeddings', { text });
  }
  
  // ============= Real-time Sync =============
  
  async subscribeToNotebook(
    notebookId: string,
    onUpdate: (event: any) => void
  ): Promise<() => void> {
    // Subscribe to backend updates
    await invoke('subscribe_to_updates', { notebook_id: notebookId });
    
    // Listen for events
    const unlisten = await listen(`notebook-update-${notebookId}`, (event) => {
      onUpdate(event.payload);
      
      // Invalidate relevant caches
      if (event.payload.type === 'source_added') {
        this.invalidateCache(`sources_${notebookId}`);
      } else if (event.payload.type === 'message_added') {
        this.invalidateCache(`messages_${notebookId}`);
      }
    });
    
    this.listeners.set(`notebook_${notebookId}`, unlisten);
    
    return () => {
      unlisten();
      this.listeners.delete(`notebook_${notebookId}`);
    };
  }
  
  async getSyncStatus(): Promise<SyncStatus> {
    return invoke('get_sync_status');
  }
  
  // ============= Collaboration =============
  
  async shareNotebook(
    notebookId: string,
    email: string,
    permission: 'read' | 'write'
  ): Promise<void> {
    await invoke('share_notebook', {
      notebook_id: notebookId,
      email,
      permission,
    });
  }
  
  async getCollaborators(notebookId: string): Promise<any[]> {
    return invoke('get_collaborators', { notebook_id: notebookId });
  }
  
  // ============= Voice & Media =============
  
  async transcribeAudio(audioBlob: Blob): Promise<string> {
    const base64 = await this.blobToBase64(audioBlob);
    return invoke('transcribe_audio', { audio: base64 });
  }
  
  async generateSpeech(text: string, voice?: string): Promise<Blob> {
    const audio = await invoke<string>('generate_speech', { text, voice });
    return this.base64ToBlob(audio, 'audio/mp3');
  }
  
  async processImage(imageBlob: Blob): Promise<{
    text: string;
    objects: any[];
  }> {
    const base64 = await this.blobToBase64(imageBlob);
    return invoke('process_image', { image: base64 });
  }
  
  // ============= Analytics =============
  
  async getAnalytics(notebookId: string): Promise<any> {
    return invoke('get_analytics', { notebook_id: notebookId });
  }
  
  async trackEvent(event: string, properties?: Record<string, any>): Promise<void> {
    await invoke('track_event', { event, properties });
  }
  
  // ============= Helper Methods =============
  
  private async invokeWithCache<T>(
    cacheKey: string,
    command: string,
    args: any,
    ttl: number = 5000
  ): Promise<T> {
    // Check cache
    const cached = this.cache.get(cacheKey);
    if (cached && cached.expires > Date.now()) {
      return cached.data;
    }
    
    // Check for pending request
    const pending = this.pendingRequests.get(cacheKey);
    if (pending) {
      return pending;
    }
    
    // Make request
    const promise = invoke<T>(command, args);
    this.pendingRequests.set(cacheKey, promise);
    
    try {
      const data = await promise;
      this.cache.set(cacheKey, {
        data,
        expires: Date.now() + ttl,
      });
      return data;
    } finally {
      this.pendingRequests.delete(cacheKey);
    }
  }
  
  private invalidateCache(pattern?: string): void {
    if (!pattern) {
      this.cache.clear();
      return;
    }
    
    this.cache.forEach((_, key) => {
      if (key.includes(pattern)) {
        this.cache.delete(key);
      }
    });
  }
  
  private async readFileContent(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsText(file);
    });
  }
  
  private getSourceTypeFromFile(file: File): SourceType {
    const ext = file.name.split('.').pop()?.toLowerCase();
    switch (ext) {
      case 'pdf':
        return 'PDF';
      case 'html':
      case 'htm':
        return 'Web';
      case 'doc':
      case 'docx':
      case 'txt':
      case 'md':
        return 'Document';
      case 'js':
      case 'ts':
      case 'py':
      case 'rs':
        return 'Code';
      case 'jpg':
      case 'png':
      case 'mp4':
      case 'mp3':
        return 'Media';
      default:
        return 'External';
    }
  }
  
  private getMimeType(format: string): string {
    const mimeTypes: Record<string, string> = {
      pdf: 'application/pdf',
      docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      markdown: 'text/markdown',
      html: 'text/html',
      json: 'application/json',
    };
    return mimeTypes[format] || 'application/octet-stream';
  }
  
  private async blobToBase64(blob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        resolve(result.split(',')[1]);
      };
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  }
  
  private base64ToBlob(base64: string, mimeType: string): Blob {
    const byteCharacters = atob(base64);
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    return new Blob([byteArray], { type: mimeType });
  }
  
  private setupEventListeners(): void {
    // Listen for global events
    listen('sync-status-changed', (event) => {
      console.log('Sync status changed:', event.payload);
    });
    
    listen('error', (event) => {
      console.error('Backend error:', event.payload);
    });
  }
  
  private startCacheCleanup(): void {
    setInterval(() => {
      const now = Date.now();
      this.cache.forEach((value, key) => {
        if (value.expires < now) {
          this.cache.delete(key);
        }
      });
    }, 60000); // Clean up every minute
  }
  
  // Cleanup on destroy
  destroy(): void {
    this.listeners.forEach(unlisten => unlisten());
    this.listeners.clear();
    this.cache.clear();
    this.pendingRequests.clear();
  }
}

// ============= Export Singleton =============

export const lighthouseService = LighthouseService.getInstance();

// ============= React Hooks =============

export function useLighthouseService() {
  return lighthouseService;
}