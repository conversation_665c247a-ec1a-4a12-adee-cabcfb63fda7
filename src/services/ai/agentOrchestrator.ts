/**
 * Agent Orchestrator Service
 * Manages agent execution within workflows
 */

import { invoke } from '@tauri-apps/api/core';
import { aiService } from './aiService';

export interface Agent {
  id: string;
  name: string;
  type: 'assistant' | 'researcher' | 'coder' | 'analyst' | 'custom';
  model?: string;
  capabilities: string[];
  config: Record<string, any>;
}

export interface AgentExecutionParams {
  agentId: string;
  task: string;
  context: Record<string, any>;
  options?: {
    temperature?: number;
    maxTokens?: number;
    timeout?: number;
    retryOnFailure?: boolean;
  };
}

export interface AgentExecutionResult {
  agentId: string;
  output: any;
  duration: number;
  tokenUsage?: number;
  cost?: number;
  metrics?: Record<string, any>;
  error?: string;
}

class AgentOrchestrator {
  private static instance: AgentOrchestrator;
  private agents: Map<string, Agent> = new Map();
  private executionHistory: Map<string, AgentExecutionResult[]> = new Map();

  private constructor() {
    this.initializeDefaultAgents();
  }

  static getInstance(): AgentOrchestrator {
    if (!AgentOrchestrator.instance) {
      AgentOrchestrator.instance = new AgentOrchestrator();
    }
    return AgentOrchestrator.instance;
  }

  private initializeDefaultAgents() {
    // Default assistant agent
    this.registerAgent({
      id: 'assistant',
      name: 'General Assistant',
      type: 'assistant',
      capabilities: ['general', 'conversation', 'reasoning'],
      config: {
        model: 'claude-3-sonnet',
        temperature: 0.7,
      },
    });

    // Research agent
    this.registerAgent({
      id: 'researcher',
      name: 'Research Agent',
      type: 'researcher',
      capabilities: ['research', 'analysis', 'documentation'],
      config: {
        model: 'claude-3-opus',
        temperature: 0.3,
      },
    });

    // Coding agent
    this.registerAgent({
      id: 'coder',
      name: 'Coding Agent',
      type: 'coder',
      capabilities: ['coding', 'debugging', 'refactoring'],
      config: {
        model: 'claude-3-sonnet',
        temperature: 0.1,
      },
    });
  }

  registerAgent(agent: Agent): void {
    this.agents.set(agent.id, agent);
  }

  async getAgent(agentId: string): Promise<Agent | undefined> {
    return this.agents.get(agentId);
  }

  async listAgents(): Promise<Agent[]> {
    return Array.from(this.agents.values());
  }

  async executeAgent(params: AgentExecutionParams): Promise<AgentExecutionResult> {
    const startTime = Date.now();
    const agent = this.agents.get(params.agentId);

    if (!agent) {
      throw new Error(`Agent not found: ${params.agentId}`);
    }

    try {
      // Build prompt based on agent type
      const prompt = this.buildAgentPrompt(agent, params.task, params.context);

      // Execute via AI service - using invoke directly for now
      const result = await invoke('process_ai_message', {
        message: prompt,
        model: agent.model || 'claude-3-sonnet',
        temperature: params.options?.temperature ?? agent.config.temperature,
        maxTokens: params.options?.maxTokens,
      }) as any;

      const executionResult: AgentExecutionResult = {
        agentId: params.agentId,
        output: result.response,
        duration: Date.now() - startTime,
        tokenUsage: result.usage?.totalTokens,
        cost: result.usage?.totalCost,
        metrics: {
          promptTokens: result.usage?.promptTokens,
          completionTokens: result.usage?.completionTokens,
        },
      };

      // Store in history
      this.addToHistory(params.agentId, executionResult);

      return executionResult;
    } catch (error) {
      const executionResult: AgentExecutionResult = {
        agentId: params.agentId,
        output: null,
        duration: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      };

      this.addToHistory(params.agentId, executionResult);

      if (params.options?.retryOnFailure) {
        // Retry once
        return this.executeAgent({ ...params, options: { ...params.options, retryOnFailure: false } });
      }

      throw error;
    }
  }

  private buildAgentPrompt(agent: Agent, task: string, context: Record<string, any>): string {
    let prompt = '';

    switch (agent.type) {
      case 'assistant':
        prompt = `You are a helpful assistant. ${task}`;
        break;
      case 'researcher':
        prompt = `You are a research specialist. Analyze and provide detailed information about: ${task}`;
        break;
      case 'coder':
        prompt = `You are a coding expert. ${task}`;
        break;
      case 'analyst':
        prompt = `You are a data analyst. ${task}`;
        break;
      default:
        prompt = task;
    }

    // Add context if provided
    if (Object.keys(context).length > 0) {
      prompt += `\n\nContext:\n${JSON.stringify(context, null, 2)}`;
    }

    return prompt;
  }

  private addToHistory(agentId: string, result: AgentExecutionResult): void {
    if (!this.executionHistory.has(agentId)) {
      this.executionHistory.set(agentId, []);
    }
    const history = this.executionHistory.get(agentId)!;
    history.push(result);

    // Keep only last 100 executions per agent
    if (history.length > 100) {
      history.shift();
    }
  }

  async getExecutionHistory(agentId: string): Promise<AgentExecutionResult[]> {
    return this.executionHistory.get(agentId) || [];
  }

  async getAgentMetrics(agentId: string): Promise<{
    totalExecutions: number;
    successRate: number;
    avgDuration: number;
    totalTokenUsage: number;
    totalCost: number;
  }> {
    const history = this.executionHistory.get(agentId) || [];
    const successful = history.filter(h => !h.error);

    return {
      totalExecutions: history.length,
      successRate: history.length > 0 ? successful.length / history.length : 0,
      avgDuration: history.reduce((sum, h) => sum + h.duration, 0) / (history.length || 1),
      totalTokenUsage: history.reduce((sum, h) => sum + (h.tokenUsage || 0), 0),
      totalCost: history.reduce((sum, h) => sum + (h.cost || 0), 0),
    };
  }

  // Workflow-specific methods
  async createWorkflowAgent(workflowId: string, nodeId: string, config: any): Promise<Agent> {
    const agent: Agent = {
      id: `workflow-${workflowId}-${nodeId}`,
      name: `Workflow Agent ${nodeId}`,
      type: 'custom',
      capabilities: config.capabilities || [],
      config: config,
    };

    this.registerAgent(agent);
    return agent;
  }

  async executeWorkflowAgent(
    workflowId: string,
    nodeId: string,
    task: string,
    context: Record<string, any>
  ): Promise<AgentExecutionResult> {
    const agentId = `workflow-${workflowId}-${nodeId}`;
    let agent = await this.getAgent(agentId);

    if (!agent) {
      // Create temporary agent for workflow
      agent = await this.createWorkflowAgent(workflowId, nodeId, context.agentConfig || {});
    }

    return this.executeAgent({
      agentId: agent.id,
      task,
      context,
      options: context.options,
    });
  }
}

export const agentOrchestrator = AgentOrchestrator.getInstance();