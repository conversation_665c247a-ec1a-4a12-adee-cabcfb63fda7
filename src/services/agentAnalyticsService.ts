/**
 * Agent Analytics Service
 * Comprehensive analytics collection, processing, and ML insights generation
 */

import { api } from '@/lib/api';
import {
  AgentPerformanceMetrics,
  SessionAnalytics,
  AgentExecutionAnalytics,
  WorkflowAnalytics,
  UsagePattern,
  MLInsight,
  CostAnalytics,
  PerformanceTrend,
  AgentRecommendation,
  RealTimeMetrics,
  Anomaly,
  BenchmarkComparison,
  AnalyticsConfig,
  AnalyticsExport
} from '@/types/agent-analytics';

class AgentAnalyticsService {
  private metricsCache: Map<string, any> = new Map();
  private realTimeMetrics: RealTimeMetrics | null = null;
  private updateInterval: NodeJS.Timeout | null = null;
  private config: AnalyticsConfig = {
    enabled: true,
    collectDetailedMetrics: true,
    retentionDays: 30,
    aggregationIntervals: ['5m', '1h', '1d'],
    alertThresholds: {
      errorRate: 0.1,
      costPerHour: 10,
      responseTime: 5000,
      tokenUsage: 100000
    },
    mlInsights: {
      enabled: true,
      modelUpdateFrequency: 'daily',
      minConfidenceThreshold: 0.7
    }
  };

  // Initialize analytics
  async initialize(): Promise<void> {
    try {
      // Load configuration
      try {
        const savedConfig = await api.getAnalyticsConfig();
        if (savedConfig) {
          this.config = { ...this.config, ...savedConfig };
        }
      } catch (configError) {
        // Silently continue with default config if backend command fails
      }

      // Start real-time metrics collection
      if (this.config.enabled) {
        this.startRealTimeMetrics();
      }

      // Initialize ML models
      if (this.config.mlInsights.enabled) {
        await this.initializeMLModels();
      }
    } catch (error) {
    }
  }

  // Performance Metrics
  async getAgentPerformanceMetrics(
    agentId: string,
    timeRange?: { start: string; end: string }
  ): Promise<AgentPerformanceMetrics> {
    const cacheKey = `perf_${agentId}_${timeRange?.start}_${timeRange?.end}`;
    
    if (this.metricsCache.has(cacheKey)) {
      return this.metricsCache.get(cacheKey);
    }

    try {
      const metrics = await api.getAgentPerformanceMetrics(agentId, timeRange);
      
      // Calculate derived metrics
      const enrichedMetrics: AgentPerformanceMetrics = {
        ...metrics,
        successRate: metrics.totalExecutions > 0 
          ? metrics.successfulExecutions / metrics.totalExecutions 
          : 0,
        errorRate: metrics.totalExecutions > 0
          ? metrics.failedExecutions / metrics.totalExecutions
          : 0,
        timeoutRate: metrics.totalExecutions > 0
          ? (metrics.timeoutCount || 0) / metrics.totalExecutions
          : 0,
        averageCost: metrics.totalExecutions > 0
          ? metrics.totalCost / metrics.totalExecutions
          : 0
      };

      this.metricsCache.set(cacheKey, enrichedMetrics);
      return enrichedMetrics;
    } catch (error) {
      throw error;
    }
  }

  // Session Analytics
  async getSessionAnalytics(sessionId: string): Promise<SessionAnalytics> {
    try {
      const analytics = await api.getSessionAnalytics(sessionId);
      
      // Calculate derived metrics
      return {
        ...analytics,
        completionRate: this.calculateCompletionRate(analytics),
        agentUtilizationRate: analytics.totalMessages > 0
          ? analytics.agentExecutions / analytics.totalMessages
          : 0
      };
    } catch (error) {
      throw error;
    }
  }

  // Workflow Analytics
  async getWorkflowAnalytics(workflowId: string): Promise<WorkflowAnalytics> {
    try {
      const analytics = await api.getWorkflowAnalytics(workflowId);
      
      // Identify bottlenecks and critical path
      const bottlenecks = this.identifyBottlenecks(analytics);
      const criticalPath = this.calculateCriticalPath(analytics);
      
      return {
        ...analytics,
        bottlenecks,
        criticalPath
      };
    } catch (error) {
      throw error;
    }
  }

  // Pattern Recognition
  async detectUsagePatterns(
    sessionIds: string[],
    minConfidence = 0.7
  ): Promise<UsagePattern[]> {
    try {
      const patterns = await api.detectUsagePatterns(sessionIds);
      
      // Filter by confidence threshold
      return patterns.filter(p => p.confidence >= minConfidence);
    } catch (error) {
      return [];
    }
  }

  // ML Insights Generation
  async generateMLInsights(
    context: {
      sessionId?: string;
      agentId?: string;
      timeRange?: { start: string; end: string };
    }
  ): Promise<MLInsight[]> {
    try {
      const insights: MLInsight[] = [];

      // Performance optimization insights
      const perfInsights = await this.generatePerformanceInsights(context);
      insights.push(...perfInsights);

      // Cost optimization insights
      const costInsights = await this.generateCostInsights(context);
      insights.push(...costInsights);

      // Anomaly detection insights
      const anomalyInsights = await this.detectAnomalies(context);
      insights.push(...anomalyInsights);

      // Predictive insights
      const predictiveInsights = await this.generatePredictiveInsights(context);
      insights.push(...predictiveInsights);

      // Filter by confidence threshold
      return insights.filter(i => i.confidence >= this.config.mlInsights.minConfidenceThreshold);
    } catch (error) {
      return [];
    }
  }

  // Agent Recommendations
  async getAgentRecommendations(
    context: {
      task: string;
      sessionHistory?: any[];
      projectContext?: string;
    }
  ): Promise<AgentRecommendation[]> {
    try {
      // Use ML model to recommend agents
      const recommendations = await api.getAgentRecommendations(context);
      
      // Sort by confidence
      return recommendations.sort((a, b) => b.confidence - a.confidence);
    } catch (error) {
      return [];
    }
  }

  // Cost Analytics
  async getCostAnalytics(
    period: 'hour' | 'day' | 'week' | 'month',
    startDate: string,
    endDate: string
  ): Promise<CostAnalytics> {
    try {
      const analytics = await api.getCostAnalytics(period, startDate, endDate);
      
      // Calculate savings opportunities
      const savingsOpportunities = this.identifySavingsOpportunities(analytics);
      
      return {
        ...analytics,
        savingsOpportunities,
        costTrend: this.calculateCostTrend(analytics)
      };
    } catch (error) {
      throw error;
    }
  }

  // Performance Trends
  async getPerformanceTrends(
    metric: string,
    period: 'hour' | 'day' | 'week' | 'month',
    agentId?: string
  ): Promise<PerformanceTrend> {
    try {
      const trend = await api.getPerformanceTrend(metric, period, agentId);
      
      // Calculate statistical metrics
      const stats = this.calculateStatistics(trend.dataPoints);
      
      // Generate forecast
      const forecast = await this.forecastNextPeriod(trend);
      
      return {
        ...trend,
        ...stats,
        forecast,
        trend: this.determineTrend(trend.dataPoints),
        changePercent: this.calculateChangePercent(trend.dataPoints)
      };
    } catch (error) {
      throw error;
    }
  }

  // Real-time Metrics
  private startRealTimeMetrics(): void {
    this.updateInterval = setInterval(async () => {
      try {
        this.realTimeMetrics = await api.getRealTimeMetrics();
        
        // Check for alerts
        this.checkAlertThresholds(this.realTimeMetrics);
        
        // Emit metrics update event
        this.emitMetricsUpdate(this.realTimeMetrics);
      } catch (error) {
        // Silently continue if backend command fails
        if (!error?.toString().includes('state not managed')) {
        }
        // Use mock data for development
        this.realTimeMetrics = {
          activeAgents: 0,
          totalSessions: 0,
          avgResponseTime: 0,
          errorRate: 0,
          tokenUsageRate: 0,
          costPerHour: 0,
          timestamp: new Date().toISOString()
        } as RealTimeMetrics;
      }
    }, 5000); // Update every 5 seconds
  }

  getRealTimeMetrics(): RealTimeMetrics | null {
    return this.realTimeMetrics;
  }

  // Anomaly Detection
  async detectAnomalies(context: any): Promise<MLInsight[]> {
    try {
      const anomalies = await api.detectAnomalies(context);
      
      // Convert anomalies to insights
      return anomalies.map(anomaly => this.anomalyToInsight(anomaly));
    } catch (error) {
      return [];
    }
  }

  // Benchmark Comparison
  async getBenchmarkComparison(
    agentId: string,
    metrics: string[]
  ): Promise<BenchmarkComparison[]> {
    try {
      return await api.getBenchmarkComparison(agentId, metrics);
    } catch (error) {
      return [];
    }
  }

  // Export Analytics
  async exportAnalytics(
    format: 'json' | 'csv' | 'excel' | 'pdf',
    options: {
      dateRange: { start: string; end: string };
      includedMetrics: string[];
      includedAgents: string[];
      aggregationLevel: 'raw' | 'hourly' | 'daily' | 'weekly' | 'monthly';
    }
  ): Promise<AnalyticsExport> {
    try {
      return await api.exportAnalytics(format, options);
    } catch (error) {
      throw error;
    }
  }

  // Helper Methods
  private calculateCompletionRate(analytics: any): number {
    // Calculate based on successful task completions vs total tasks
    if (!analytics.totalTasks) return 0;
    return analytics.completedTasks / analytics.totalTasks;
  }

  private identifyBottlenecks(analytics: any): string[] {
    // Identify tasks that take significantly longer than average
    const bottlenecks: string[] = [];
    const avgDuration = analytics.averageTaskDuration;
    
    for (const task of analytics.tasks || []) {
      if (task.duration > avgDuration * 1.5) {
        bottlenecks.push(task.id);
      }
    }
    
    return bottlenecks;
  }

  private calculateCriticalPath(analytics: any): string[] {
    // Calculate the longest path through the workflow
    // This is a simplified version - real implementation would use graph algorithms
    return analytics.tasks
      ?.filter((t: any) => t.isCritical)
      ?.map((t: any) => t.id) || [];
  }

  private async generatePerformanceInsights(context: any): Promise<MLInsight[]> {
    const insights: MLInsight[] = [];
    
    // Check for slow agents
    const slowAgents = await this.identifySlowAgents(context);
    for (const agent of slowAgents) {
      insights.push({
        insightId: `perf_${agent.id}_${Date.now()}`,
        type: 'optimization',
        severity: 'warning',
        confidence: 0.85,
        title: `Agent "${agent.name}" is performing slower than usual`,
        description: `Execution time has increased by ${agent.degradation}% over the last 24 hours`,
        evidence: [{
          metric: 'execution_time',
          value: agent.currentTime,
          threshold: agent.baselineTime,
          trend: 'increasing'
        }],
        recommendations: [{
          action: 'Review agent configuration and optimize prompts',
          impact: 'high',
          effort: 'medium',
          estimatedImprovement: 30
        }],
        relatedAgents: [agent.id],
        relatedSessions: [],
        generatedAt: new Date().toISOString(),
        validUntil: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
      });
    }
    
    return insights;
  }

  private async generateCostInsights(context: any): Promise<MLInsight[]> {
    const insights: MLInsight[] = [];
    
    // Check for cost optimization opportunities
    const costData = await api.getCostOptimizationOpportunities(context);
    
    for (const opportunity of costData) {
      insights.push({
        insightId: `cost_${opportunity.id}_${Date.now()}`,
        type: 'optimization',
        severity: opportunity.potentialSavings > 100 ? 'critical' : 'warning',
        confidence: opportunity.confidence,
        title: opportunity.title,
        description: opportunity.description,
        evidence: opportunity.evidence,
        recommendations: opportunity.recommendations,
        relatedAgents: opportunity.relatedAgents,
        relatedSessions: [],
        generatedAt: new Date().toISOString(),
        validUntil: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
      });
    }
    
    return insights;
  }

  private async generatePredictiveInsights(context: any): Promise<MLInsight[]> {
    const insights: MLInsight[] = [];
    
    // Predict future issues based on trends
    const predictions = await api.getPredictiveAnalytics(context);
    
    for (const prediction of predictions) {
      if (prediction.probability > 0.7) {
        insights.push({
          insightId: `pred_${prediction.id}_${Date.now()}`,
          type: 'prediction',
          severity: prediction.impact === 'high' ? 'critical' : 'warning',
          confidence: prediction.probability,
          title: prediction.title,
          description: prediction.description,
          evidence: prediction.evidence,
          recommendations: prediction.preventiveActions,
          relatedAgents: prediction.affectedAgents,
          relatedSessions: [],
          generatedAt: new Date().toISOString(),
          validUntil: prediction.expectedTime
        });
      }
    }
    
    return insights;
  }

  private identifySavingsOpportunities(analytics: any): any[] {
    const opportunities = [];
    
    // Check for underutilized agents
    if (analytics.agentUtilization < 0.3) {
      opportunities.push({
        opportunity: 'Reduce agent pool size',
        potentialSavings: analytics.totalCost * 0.2,
        implementation: 'Consolidate similar agents and remove duplicates'
      });
    }
    
    // Check for inefficient workflows
    if (analytics.averageRetries > 2) {
      opportunities.push({
        opportunity: 'Optimize retry logic',
        potentialSavings: analytics.retryCost,
        implementation: 'Implement exponential backoff and better error handling'
      });
    }
    
    return opportunities;
  }

  private calculateStatistics(dataPoints: any[]): any {
    const values = dataPoints.map(d => d.value);
    values.sort((a, b) => a - b);
    
    return {
      average: values.reduce((a, b) => a + b, 0) / values.length,
      median: values[Math.floor(values.length / 2)],
      min: values[0],
      max: values[values.length - 1]
    };
  }

  private async forecastNextPeriod(trend: any): Promise<any> {
    // Simple linear regression for forecasting
    // In production, use more sophisticated ML models
    const n = trend.dataPoints.length;
    const sumX = trend.dataPoints.reduce((sum, _, i) => sum + i, 0);
    const sumY = trend.dataPoints.reduce((sum, p) => sum + p.value, 0);
    const sumXY = trend.dataPoints.reduce((sum, p, i) => sum + i * p.value, 0);
    const sumX2 = trend.dataPoints.reduce((sum, _, i) => sum + i * i, 0);
    
    const slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
    const intercept = (sumY - slope * sumX) / n;
    
    const nextValue = slope * n + intercept;
    
    return {
      nextPeriod: Math.max(0, nextValue),
      confidence: 0.75 // Simplified confidence calculation
    };
  }

  private determineTrend(dataPoints: any[]): 'improving' | 'degrading' | 'stable' {
    if (dataPoints.length < 2) return 'stable';
    
    const recentAvg = dataPoints.slice(-3).reduce((sum, p) => sum + p.value, 0) / 3;
    const olderAvg = dataPoints.slice(0, 3).reduce((sum, p) => sum + p.value, 0) / 3;
    
    const change = (recentAvg - olderAvg) / olderAvg;
    
    if (Math.abs(change) < 0.05) return 'stable';
    return change > 0 ? 'degrading' : 'improving';
  }

  private calculateChangePercent(dataPoints: any[]): number {
    if (dataPoints.length < 2) return 0;
    
    const first = dataPoints[0].value;
    const last = dataPoints[dataPoints.length - 1].value;
    
    return ((last - first) / first) * 100;
  }

  private calculateCostTrend(analytics: any): 'increasing' | 'decreasing' | 'stable' {
    const trend = analytics.costHistory?.trend;
    if (!trend) return 'stable';
    
    if (Math.abs(trend) < 0.05) return 'stable';
    return trend > 0 ? 'increasing' : 'decreasing';
  }

  private async identifySlowAgents(context: any): Promise<any[]> {
    // Identify agents with degraded performance
    const agents = await api.getAgentPerformanceHistory(context);
    const slowAgents = [];
    
    for (const agent of agents) {
      const recentPerf = agent.recentPerformance;
      const baseline = agent.baseline;
      
      if (recentPerf > baseline * 1.2) {
        slowAgents.push({
          id: agent.id,
          name: agent.name,
          currentTime: recentPerf,
          baselineTime: baseline,
          degradation: ((recentPerf - baseline) / baseline) * 100
        });
      }
    }
    
    return slowAgents;
  }

  private anomalyToInsight(anomaly: Anomaly): MLInsight {
    return {
      insightId: `anomaly_${anomaly.anomalyId}`,
      type: 'anomaly',
      severity: anomaly.severity === 'critical' ? 'critical' : 'warning',
      confidence: 1 - (1 / anomaly.standardDeviations),
      title: `Anomaly detected in ${anomaly.metric}`,
      description: `${anomaly.metric} is ${anomaly.deviation}% ${anomaly.actualValue > anomaly.expectedValue ? 'above' : 'below'} expected value`,
      evidence: [{
        metric: anomaly.metric,
        value: anomaly.actualValue,
        threshold: anomaly.expectedValue,
        trend: 'stable'
      }],
      recommendations: anomaly.suggestedActions.map(action => ({
        action,
        impact: 'high' as const,
        effort: 'low' as const,
        estimatedImprovement: 20
      })),
      relatedAgents: anomaly.affectedAgents,
      relatedSessions: anomaly.affectedSessions,
      generatedAt: anomaly.detectedAt,
      validUntil: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
    };
  }

  private checkAlertThresholds(metrics: RealTimeMetrics): void {
    // Check error rate
    if (metrics.errorsPerMinute > this.config.alertThresholds.errorRate * 60) {
      this.emitAlert('error_rate', metrics.errorsPerMinute);
    }
    
    // Check response time
    if (metrics.averageLatency > this.config.alertThresholds.responseTime) {
      this.emitAlert('response_time', metrics.averageLatency);
    }
    
    // Check token usage
    if (metrics.tokensPerSecond * 3600 > this.config.alertThresholds.tokenUsage) {
      this.emitAlert('token_usage', metrics.tokensPerSecond * 3600);
    }
  }

  private emitMetricsUpdate(metrics: RealTimeMetrics): void {
    // Emit custom event for UI updates
    window.dispatchEvent(new CustomEvent('analytics:metrics-update', { detail: metrics }));
  }

  private emitAlert(type: string, value: number): void {
    // Emit custom event for alerts
    window.dispatchEvent(new CustomEvent('analytics:alert', { 
      detail: { type, value, threshold: this.config.alertThresholds[type] }
    }));
  }

  private async initializeMLModels(): Promise<void> {
    // Initialize ML models for predictions and recommendations
    try {
      await api.initializeMLModels();
    } catch (error) {
      // Silently continue if backend command fails
      if (!error?.toString().includes('state not managed')) {
      }
    }
  }

  // Configuration
  async updateConfig(config: Partial<AnalyticsConfig>): Promise<void> {
    this.config = { ...this.config, ...config };
    await api.updateAnalyticsConfig(this.config);
    
    // Restart services if needed
    if (config.enabled !== undefined) {
      if (config.enabled && !this.updateInterval) {
        this.startRealTimeMetrics();
      } else if (!config.enabled && this.updateInterval) {
        clearInterval(this.updateInterval);
        this.updateInterval = null;
      }
    }
  }

  getConfig(): AnalyticsConfig {
    return this.config;
  }

  // Cleanup
  destroy(): void {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
    this.metricsCache.clear();
  }
}

// Export singleton instance
export const agentAnalytics = new AgentAnalyticsService();