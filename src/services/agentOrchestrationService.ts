/**
 * Agent Orchestration Service
 * Manages multi-agent workflow execution within Claude sessions
 */

import { api } from '@/lib/api';
import type {
  AgentWorkflow,
  AgentTask,
  AgentOrchestrationResult,
  WorkflowContext,
  AgentChain,
  IAgentOrchestrator,
  OrchestrationMetrics,
  ErrorStrategy
} from '@/types/agent-orchestration';

export class AgentOrchestrationService implements IAgentOrchestrator {
  private runningWorkflows: Map<string, AgentWorkflow> = new Map();
  private taskResults: Map<string, any> = new Map();
  private cancellationTokens: Map<string, AbortController> = new Map();

  /**
   * Execute a complete workflow with multiple agents
   */
  async executeWorkflow(workflow: AgentWorkflow): Promise<AgentOrchestrationResult> {
    const startTime = Date.now();
    const workflowId = workflow.id;
    
    // Store workflow in running state
    workflow.status = 'running';
    workflow.startedAt = new Date();
    this.runningWorkflows.set(workflowId, workflow);
    
    // Create cancellation token
    const abortController = new AbortController();
    this.cancellationTokens.set(workflowId, abortController);
    
    const errors: Array<{ taskId: string; error: string }> = [];
    const metrics: OrchestrationMetrics = {
      totalTasks: workflow.tasks.length,
      completedTasks: 0,
      failedTasks: 0,
      averageTaskTime: 0,
      totalTokensUsed: 0,
      parallelExecutions: 0
    };
    
    try {
      // Execute based on mode
      if (workflow.executionMode === 'sequential') {
        await this.executeSequential(workflow, abortController.signal);
      } else if (workflow.executionMode === 'parallel') {
        await this.executeParallel(workflow, abortController.signal);
        metrics.parallelExecutions = workflow.tasks.length;
      } else {
        await this.executeMixed(workflow, abortController.signal);
      }
      
      // Calculate metrics
      workflow.tasks.forEach(task => {
        if (task.status === 'completed') {
          metrics.completedTasks++;
          if (task.startTime && task.endTime) {
            const taskTime = task.endTime.getTime() - task.startTime.getTime();
            metrics.averageTaskTime += taskTime;
          }
        } else if (task.status === 'failed') {
          metrics.failedTasks++;
          errors.push({
            taskId: task.id,
            error: task.error || 'Unknown error'
          });
        }
      });
      
      if (metrics.completedTasks > 0) {
        metrics.averageTaskTime /= metrics.completedTasks;
      }
      
      // Mark workflow as completed
      workflow.status = errors.length === 0 ? 'completed' : 'failed';
      workflow.completedAt = new Date();
      
    } catch (error) {
      workflow.status = 'failed';
      workflow.completedAt = new Date();
      errors.push({
        taskId: 'workflow',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      // Cleanup
      this.runningWorkflows.delete(workflowId);
      this.cancellationTokens.delete(workflowId);
    }
    
    // Aggregate results
    const aggregatedResults = this.aggregateResults(workflow);
    
    return {
      workflowId,
      success: workflow.status === 'completed',
      tasks: workflow.tasks,
      aggregatedResults,
      errors,
      executionTime: Date.now() - startTime,
      metrics
    };
  }

  /**
   * Execute tasks sequentially
   */
  private async executeSequential(
    workflow: AgentWorkflow,
    signal: AbortSignal
  ): Promise<void> {
    for (const task of workflow.tasks) {
      if (signal.aborted) {
        task.status = 'failed';
        task.error = 'Workflow cancelled';
        break;
      }
      
      // Check dependencies
      if (!this.areDependenciesMet(task, workflow)) {
        task.status = 'failed';
        task.error = 'Dependencies not met';
        continue;
      }
      
      await this.executeTask(task, workflow);
    }
  }

  /**
   * Execute all tasks in parallel
   */
  private async executeParallel(
    workflow: AgentWorkflow,
    signal: AbortSignal
  ): Promise<void> {
    const promises = workflow.tasks.map(task => {
      if (signal.aborted) {
        task.status = 'failed';
        task.error = 'Workflow cancelled';
        return Promise.resolve();
      }
      
      return this.executeTask(task, workflow);
    });
    
    await Promise.allSettled(promises);
  }

  /**
   * Execute with mixed strategy (respecting dependencies)
   */
  private async executeMixed(
    workflow: AgentWorkflow,
    signal: AbortSignal
  ): Promise<void> {
    const executed = new Set<string>();
    const executing = new Map<string, Promise<void>>();
    
    while (executed.size < workflow.tasks.length) {
      if (signal.aborted) break;
      
      // Find tasks ready to execute
      const readyTasks = workflow.tasks.filter(task => 
        !executed.has(task.id) &&
        !executing.has(task.id) &&
        this.areDependenciesMet(task, workflow)
      );
      
      if (readyTasks.length === 0) {
        // Wait for some tasks to complete
        if (executing.size > 0) {
          await Promise.race(Array.from(executing.values()));
        } else {
          // No tasks can be executed - deadlock or all done
          break;
        }
        continue;
      }
      
      // Execute ready tasks in parallel
      for (const task of readyTasks) {
        const promise = this.executeTask(task, workflow).then(() => {
          executed.add(task.id);
          executing.delete(task.id);
        });
        executing.set(task.id, promise);
      }
    }
    
    // Wait for all remaining tasks
    if (executing.size > 0) {
      await Promise.allSettled(Array.from(executing.values()));
    }
  }

  /**
   * Execute a single task
   */
  private async executeTask(
    task: AgentTask,
    workflow: AgentWorkflow
  ): Promise<void> {
    task.status = 'running';
    task.startTime = new Date();
    
    try {
      // Prepare context with previous results
      const context = this.prepareTaskContext(task, workflow);
      
      // Execute agent
      const result = await api.executeAgentInSession(
        workflow.sessionId || '',
        task.agentId,
        task.task,
        {
          session_id: workflow.sessionId || '',
          project_path: workflow.context?.projectPath || '',
          recent_messages: workflow.context?.messages || [],
          checkpoints: [],
          metrics: {
            workflowId: workflow.id,
            taskId: task.id,
            dependencies: task.dependencies,
            sharedMemory: workflow.context?.sharedMemory || {}
          }
        }
      );
      
      task.status = result.success ? 'completed' : 'failed';
      task.result = result.output;
      task.error = result.error;
      
      // Store result for dependent tasks
      this.taskResults.set(task.id, result.output);
      
      // Update shared memory
      if (workflow.context?.sharedMemory && result.output) {
        workflow.context.sharedMemory[task.id] = result.output;
      }
      
    } catch (error) {
      task.status = 'failed';
      task.error = error instanceof Error ? error.message : 'Unknown error';
      
      // Retry logic
      if (task.retryCount < task.maxRetries) {
        task.retryCount++;
        task.status = 'pending';
        await new Promise(resolve => setTimeout(resolve, 1000 * task.retryCount));
        return this.executeTask(task, workflow);
      }
    } finally {
      task.endTime = new Date();
    }
  }

  /**
   * Check if task dependencies are met
   */
  private areDependenciesMet(task: AgentTask, workflow: AgentWorkflow): boolean {
    if (task.dependencies.length === 0) return true;
    
    return task.dependencies.every(depId => {
      const depTask = workflow.tasks.find(t => t.id === depId);
      return depTask && depTask.status === 'completed';
    });
  }

  /**
   * Prepare context for task execution
   */
  private prepareTaskContext(task: AgentTask, workflow: AgentWorkflow): any {
    const context: any = {
      ...workflow.context,
      taskId: task.id,
      previousResults: {}
    };
    
    // Include results from dependencies
    task.dependencies.forEach(depId => {
      if (this.taskResults.has(depId)) {
        context.previousResults[depId] = this.taskResults.get(depId);
      }
    });
    
    return context;
  }

  /**
   * Aggregate results from all tasks
   */
  private aggregateResults(workflow: AgentWorkflow): Record<string, any> {
    const results: Record<string, any> = {};
    
    workflow.tasks.forEach(task => {
      if (task.result) {
        results[task.id] = {
          agentName: task.agentName,
          status: task.status,
          result: task.result,
          executionTime: task.endTime && task.startTime
            ? task.endTime.getTime() - task.startTime.getTime()
            : 0
        };
      }
    });
    
    return results;
  }

  /**
   * Execute an agent chain with data flow
   */
  async executeChain(chain: AgentChain, input: any): Promise<any> {
    const results = new Map<string, any>();
    results.set('input', input);
    
    for (const agent of chain.agents) {
      // Check execution condition
      if (!this.shouldExecuteAgent(agent, chain.errorHandling, results)) {
        continue;
      }
      
      // Prepare input for agent
      const agentInput = this.mapInputForAgent(agent, results);
      
      try {
        // Execute agent
        const result = await api.executeAgent(
          agent.agentId,
          '', // project path from context
          JSON.stringify(agentInput)
        );
        
        // Store results
        results.set(agent.agentName, result);
        
        // Apply data flow rules
        this.applyDataFlowRules(chain.dataFlow, agent.agentName, result, results);
        
      } catch (error) {
        // Handle error based on strategy
        const handled = await this.handleChainError(
          error,
          agent,
          chain.errorHandling,
          results
        );
        
        if (!handled) {
          throw error;
        }
      }
    }
    
    // Return final output
    return results.get('output') || results;
  }

  /**
   * Cancel a running workflow
   */
  async cancelWorkflow(workflowId: string): Promise<void> {
    const controller = this.cancellationTokens.get(workflowId);
    if (controller) {
      controller.abort();
    }
    
    const workflow = this.runningWorkflows.get(workflowId);
    if (workflow) {
      workflow.status = 'cancelled';
      workflow.completedAt = new Date();
      
      // Mark all pending tasks as cancelled
      workflow.tasks.forEach(task => {
        if (task.status === 'pending' || task.status === 'running') {
          task.status = 'failed';
          task.error = 'Workflow cancelled';
        }
      });
    }
  }

  /**
   * Get workflow status
   */
  async getWorkflowStatus(workflowId: string): Promise<AgentWorkflow> {
    const workflow = this.runningWorkflows.get(workflowId);
    if (!workflow) {
      throw new Error(`Workflow ${workflowId} not found`);
    }
    return workflow;
  }

  /**
   * Retry a failed task
   */
  async retryTask(workflowId: string, taskId: string): Promise<void> {
    const workflow = this.runningWorkflows.get(workflowId);
    if (!workflow) {
      throw new Error(`Workflow ${workflowId} not found`);
    }
    
    const task = workflow.tasks.find(t => t.id === taskId);
    if (!task) {
      throw new Error(`Task ${taskId} not found`);
    }
    
    if (task.status !== 'failed') {
      throw new Error(`Task ${taskId} is not in failed state`);
    }
    
    // Reset task for retry
    task.status = 'pending';
    task.error = undefined;
    task.retryCount = 0;
    
    // Re-execute task
    await this.executeTask(task, workflow);
  }

  /**
   * Helper: Check if agent should execute
   */
  private shouldExecuteAgent(
    agent: any,
    errorStrategy: ErrorStrategy,
    results: Map<string, any>
  ): boolean {
    if (!agent.condition) return true;
    
    switch (agent.condition.type) {
      case 'always':
        return true;
      case 'if_success':
        return !Array.from(results.values()).some(r => r.error);
      case 'if_failure':
        return Array.from(results.values()).some(r => r.error);
      case 'custom':
        return agent.condition.customCondition?.(null as any, Array.from(results.values())) || false;
      default:
        return true;
    }
  }

  /**
   * Helper: Map input for agent based on chain configuration
   */
  private mapInputForAgent(agent: any, results: Map<string, any>): any {
    const input: any = {};
    
    Object.entries(agent.inputMapping).forEach(([key, source]) => {
      const [sourceName, sourceKey] = (source as string).split('.');
      const sourceData = results.get(sourceName);
      
      if (sourceData && sourceKey) {
        input[key] = sourceData[sourceKey];
      } else if (sourceData) {
        input[key] = sourceData;
      }
    });
    
    return input;
  }

  /**
   * Helper: Apply data flow rules
   */
  private applyDataFlowRules(
    rules: any[],
    agentName: string,
    result: any,
    results: Map<string, any>
  ): void {
    rules
      .filter(rule => rule.from === agentName)
      .forEach(rule => {
        let data = result;
        
        // Apply transformation if defined
        if (rule.transform) {
          data = rule.transform(data);
        }
        
        // Apply mapping
        if (rule.mapping) {
          const mapped: any = {};
          Object.entries(rule.mapping).forEach(([key, source]) => {
            mapped[key] = data[source as string];
          });
          data = mapped;
        }
        
        // Store in results
        if (rule.to === 'output') {
          results.set('output', data);
        } else {
          const existing = results.get(rule.to) || {};
          results.set(rule.to, { ...existing, ...data });
        }
      });
  }

  /**
   * Helper: Handle chain execution errors
   */
  private async handleChainError(
    error: any,
    agent: any,
    strategy: ErrorStrategy,
    results: Map<string, any>
  ): Promise<boolean> {
    switch (strategy.type) {
      case 'fail_fast':
        return false;
        
      case 'continue':
        results.set(agent.agentName, { error: error.message });
        return true;
        
      case 'retry':
        if (agent.retryCount < (strategy.maxRetries || 3)) {
          agent.retryCount = (agent.retryCount || 0) + 1;
          await new Promise(resolve => 
            setTimeout(resolve, strategy.retryDelay || 1000)
          );
          // Re-execute agent
          return true;
        }
        return false;
        
      case 'fallback':
        if (strategy.fallbackAgentId) {
          // Execute fallback agent
          const fallbackResult = await api.executeAgent(
            strategy.fallbackAgentId,
            '',
            agent.task
          );
          results.set(agent.agentName, fallbackResult);
          return true;
        }
        return false;
        
      default:
        return false;
    }
  }
}

// Export singleton instance
export const agentOrchestrator = new AgentOrchestrationService();