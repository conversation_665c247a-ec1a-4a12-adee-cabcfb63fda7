import { useState, useCallback, useRef, useEffect } from 'react';

export function useExtendedThinking() {
  const [isExtendedThinking, setIsExtendedThinking] = useState(false);
  const [thinkingDuration, setThinkingDuration] = useState(0);
  const thinkingStartTime = useRef<number | null>(null);
  const thinkingInterval = useRef<NodeJS.Timeout | null>(null);

  const enableExtendedThinking = useCallback(() => {
    setIsExtendedThinking(true);
    thinkingStartTime.current = Date.now();
    setThinkingDuration(0);
    
    // Start duration counter
    thinkingInterval.current = setInterval(() => {
      if (thinkingStartTime.current) {
        setThinkingDuration(Date.now() - thinkingStartTime.current);
      }
    }, 100); // Update every 100ms for smooth display
  }, []);

  const disableExtendedThinking = useCallback(() => {
    setIsExtendedThinking(false);
    
    if (thinkingInterval.current) {
      clearInterval(thinkingInterval.current);
      thinkingInterval.current = null;
    }
    
    // Keep the final duration for a moment before resetting
    setTimeout(() => {
      setThinkingDuration(0);
      thinkingStartTime.current = null;
    }, 2000);
  }, []);

  const updateThinkingDuration = useCallback((duration: number) => {
    setThinkingDuration(duration);
  }, []);

  const getFormattedDuration = useCallback(() => {
    const seconds = Math.floor(thinkingDuration / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    
    if (minutes > 0) {
      return `${minutes}m ${remainingSeconds}s`;
    }
    return `${seconds}s`;
  }, [thinkingDuration]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (thinkingInterval.current) {
        clearInterval(thinkingInterval.current);
      }
    };
  }, []);

  return {
    isExtendedThinking,
    thinkingDuration,
    formattedDuration: getFormattedDuration(),
    enableExtendedThinking,
    disableExtendedThinking,
    updateThinkingDuration,
  };
}