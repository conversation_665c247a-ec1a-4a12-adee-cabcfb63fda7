import { useState, useCallback, useRef } from 'react';

export interface PlanStep {
  id: string;
  description: string;
  status: 'pending' | 'executing' | 'completed' | 'failed';
  timestamp: number;
}

export function usePlanMode() {
  const [isPlanMode, setIsPlanMode] = useState(false);
  const [planBuffer, setPlanBuffer] = useState<PlanStep[]>([]);
  const [isExecutingPlan, setIsExecutingPlan] = useState(false);
  const planIdCounter = useRef(0);

  const enablePlanMode = useCallback(() => {
    setIsPlanMode(true);
    setPlanBuffer([]);
  }, []);

  const disablePlanMode = useCallback(() => {
    setIsPlanMode(false);
    setPlanBuffer([]);
    setIsExecutingPlan(false);
  }, []);

  const addToPlanBuffer = useCallback((description: string) => {
    const step: PlanStep = {
      id: `plan-step-${++planIdCounter.current}`,
      description,
      status: 'pending',
      timestamp: Date.now(),
    };
    
    setPlanBuffer(prev => [...prev, step]);
    return step;
  }, []);

  const updatePlanStep = useCallback((stepId: string, status: PlanStep['status']) => {
    setPlanBuffer(prev => 
      prev.map(step => 
        step.id === stepId ? { ...step, status } : step
      )
    );
  }, []);

  const clearPlanBuffer = useCallback(() => {
    setPlanBuffer([]);
    planIdCounter.current = 0;
  }, []);

  const approvePlan = useCallback(async () => {
    if (planBuffer.length === 0) return false;
    
    setIsExecutingPlan(true);
    
    try {
      // Execute plan steps sequentially
      for (const step of planBuffer) {
        updatePlanStep(step.id, 'executing');
        
        // This would call the actual execution logic
        // await api.executePlanStep(step);
        
        // Simulate execution
        await new Promise(resolve => setTimeout(resolve, 500));
        
        updatePlanStep(step.id, 'completed');
      }
      
      // Clear after successful execution
      setTimeout(() => {
        clearPlanBuffer();
        disablePlanMode();
      }, 1000);
      
      return true;
    } catch (error) {
      
      // Mark remaining steps as failed
      planBuffer.forEach(step => {
        if (step.status === 'pending' || step.status === 'executing') {
          updatePlanStep(step.id, 'failed');
        }
      });
      
      return false;
    } finally {
      setIsExecutingPlan(false);
    }
  }, [planBuffer, updatePlanStep, clearPlanBuffer, disablePlanMode]);

  const rejectPlan = useCallback(() => {
    clearPlanBuffer();
    disablePlanMode();
  }, [clearPlanBuffer, disablePlanMode]);

  return {
    isPlanMode,
    planBuffer,
    isExecutingPlan,
    enablePlanMode,
    disablePlanMode,
    addToPlanBuffer,
    updatePlanStep,
    clearPlanBuffer,
    approvePlan,
    rejectPlan,
  };
}