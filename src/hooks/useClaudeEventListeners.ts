/**
 * Custom hook for managing Claude event listeners
 * Reduces boilerplate and ensures proper cleanup
 */

import { useEffect, useRef, useCallback } from 'react';
import { listen, UnlistenFn } from '@tauri-apps/api/event';
import { 
  SessionEventType, 
  ClaudeOutputEvent, 
  ClaudeErrorE<PERSON>, 
  ClaudeCompleteEvent,
  getSessionEventChannel 
} from '@/types/session';

export interface ClaudeEventHandlers {
  onOutput?: (payload: string) => void;
  onError?: (payload: string) => void;
  onComplete?: (payload: boolean) => void;
  onCheckpoint?: (payload: any) => void;
  onMetrics?: (payload: any) => void;
}

export interface UseClaudeEventListenersOptions {
  sessionId?: string;
  useGenericFallback?: boolean;
  enabled?: boolean;
}

/**
 * Hook for managing Claude event listeners with automatic cleanup
 * 
 * @param handlers - Event handler functions
 * @param options - Configuration options
 * @returns Object with methods to control listeners
 */
export function useClaudeEventListeners(
  handlers: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  options: UseClaudeEventListenersOptions = {}
) {
  const { 
    sessionId, 
    useGenericFallback = true, 
    enabled = true 
  } = options;
  
  const unlistenRefs = useRef<UnlistenFn[]>([]);
  const isListeningRef = useRef(false);

  /**
   * Sets up event listeners
   */
  const setupListeners = useCallback(async () => {
    if (!enabled || isListeningRef.current) return;
    
    console.log('[useClaudeEventListeners] Setting up listeners', { 
      sessionId, 
      useGenericFallback 
    });
    
    const newUnlisteners: UnlistenFn[] = [];
    
    try {
      // Session-specific listeners
      if (sessionId) {
        if (handlers.onOutput) {
          const channel = getSessionEventChannel(SessionEventType.OUTPUT, sessionId);
          const unlisten = await listen<string>(channel, (event) => {
            handlers.onOutput!(event.payload);
          });
          newUnlisteners.push(unlisten);
        }
        
        if (handlers.onError) {
          const channel = getSessionEventChannel(SessionEventType.ERROR, sessionId);
          const unlisten = await listen<string>(channel, (event) => {
            handlers.onError!(event.payload);
          });
          newUnlisteners.push(unlisten);
        }
        
        if (handlers.onComplete) {
          const channel = getSessionEventChannel(SessionEventType.COMPLETE, sessionId);
          const unlisten = await listen<boolean>(channel, (event) => {
            handlers.onComplete!(event.payload);
          });
          newUnlisteners.push(unlisten);
        }
        
        if (handlers.onCheckpoint) {
          const channel = getSessionEventChannel(SessionEventType.CHECKPOINT, sessionId);
          const unlisten = await listen(channel, (event) => {
            handlers.onCheckpoint!(event.payload);
          });
          newUnlisteners.push(unlisten);
        }
        
        if (handlers.onMetrics) {
          const channel = getSessionEventChannel(SessionEventType.METRICS, sessionId);
          const unlisten = await listen(channel, (event) => {
            handlers.onMetrics!(event.payload);
          });
          newUnlisteners.push(unlisten);
        }
      }
      
      // Generic fallback listeners
      if (useGenericFallback || !sessionId) {
        if (handlers.onOutput) {
          const unlisten = await listen<string>(SessionEventType.OUTPUT, (event) => {
            handlers.onOutput!(event.payload);
          });
          newUnlisteners.push(unlisten);
        }
        
        if (handlers.onError) {
          const unlisten = await listen<string>(SessionEventType.ERROR, (event) => {
            handlers.onError!(event.payload);
          });
          newUnlisteners.push(unlisten);
        }
        
        if (handlers.onComplete) {
          const unlisten = await listen<boolean>(SessionEventType.COMPLETE, (event) => {
            handlers.onComplete!(event.payload);
          });
          newUnlisteners.push(unlisten);
        }
      }
      
      unlistenRefs.current = newUnlisteners;
      isListeningRef.current = true;
      
      console.log('[useClaudeEventListeners] Listeners setup complete', {
        count: newUnlisteners.length
      });
      
    } catch (error) {
      console.error('[useClaudeEventListeners] Failed to setup listeners:', error);
      // Clean up any partial setup
      newUnlisteners.forEach(unlisten => unlisten());
    }
  }, [sessionId, useGenericFallback, enabled, handlers]);

  /**
   * Cleans up all event listeners
   */
  const cleanup = useCallback(() => {
    if (!isListeningRef.current) return;
    
    console.log('[useClaudeEventListeners] Cleaning up listeners', {
      count: unlistenRefs.current.length
    });
    
    unlistenRefs.current.forEach(unlisten => {
      try {
        unlisten();
      } catch (error) {
        console.error('[useClaudeEventListeners] Error during cleanup:', error);
      }
    });
    
    unlistenRefs.current = [];
    isListeningRef.current = false;
  }, []);

  /**
   * Restarts listeners (cleanup + setup)
   */
  const restart = useCallback(async () => {
    cleanup();
    await setupListeners();
  }, [cleanup, setupListeners]);

  /**
   * Updates the session ID and restarts listeners
   */
  const updateSessionId = useCallback(async (newSessionId: string) => {
    console.log('[useClaudeEventListeners] Updating session ID:', newSessionId);
    cleanup();
    // The effect will handle re-setup with new sessionId
  }, [cleanup]);

  // Setup and cleanup on mount/unmount and when dependencies change
  useEffect(() => {
    if (enabled) {
      setupListeners();
    }
    
    return () => {
      cleanup();
    };
  }, [sessionId, enabled]); // Don't include handlers to avoid re-setup on every render

  return {
    isListening: isListeningRef.current,
    cleanup,
    restart,
    updateSessionId,
    listenersCount: unlistenRefs.current.length
  };
}

/**
 * Simplified hook for basic session event handling
 */
export function useSessionEvents(
  sessionId: string | undefined,
  onOutput: (payload: string) => void,
  onError: (payload: string) => void,
  onComplete: (payload: boolean) => void
) {
  return useClaudeEventListeners(
    {
      onOutput,
      onError,
      onComplete
    },
    {
      sessionId,
      useGenericFallback: true,
      enabled: true
    }
  );
}