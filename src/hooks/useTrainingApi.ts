/**
 * React hooks for Training API integration
 * Provides easy-to-use hooks for all training API operations
 */

import { useState, useEffect, useCallback } from 'react';
import { mwsTrainingApi } from '@/lib/api/training/mwsTrainingApi';
import type {
  TrainingProgram,
  TrainingSchedule,
  Certification,
  BudgetAllocation,
  ProgressData,
  FeedbackResponse,
  Achievement,
  LearningPathway,
  SkillGap,
  TrainingNeed,
  DashboardMetrics,
  LMSCourse,
  LeaderboardEntry,
  ReportTemplate,
  ApiResponse
} from '@/types/training';

// Generic hook for API calls with loading and error states
function useApiCall<T>(
  apiCall: () => Promise<ApiResponse<T> | T>,
  dependencies: any[] = []
) {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiCall();
      
      // Handle both ApiResponse and direct data returns
      if (response && typeof response === 'object' && 'data' in response) {
        setData((response as ApiResponse<T>).data);
      } else {
        setData(response as T);
      }
    } catch (err) {
      setError(err instanceof Error ? err : new Error('An error occurred'));
    } finally {
      setLoading(false);
    }
  }, dependencies);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return { data, loading, error, refetch: fetchData };
}

// Dashboard Metrics Hook
export function useDashboardMetrics() {
  return useApiCall<DashboardMetrics>(
    () => mwsTrainingApi.overview.getMetrics()
  );
}

// Training Programs Hook
export function useTrainingPrograms() {
  const [programs, setPrograms] = useState<TrainingProgram[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    // For now, using mock data from context
    // Will be replaced with actual API call
    setLoading(false);
  }, []);

  return { programs, loading, error };
}

// Training Schedules Hook
export function useTrainingSchedules(filters?: any) {
  return useApiCall<TrainingSchedule[]>(
    () => mwsTrainingApi.schedule.getSchedules(filters),
    [filters]
  );
}

// Certifications Hook
export function useCertifications(employeeId?: string) {
  return useApiCall<Certification[]>(
    () => mwsTrainingApi.certification.getCertifications(employeeId),
    [employeeId]
  );
}

// Budget Allocations Hook
export function useBudgetAllocations(department?: string) {
  return useApiCall<BudgetAllocation[]>(
    () => mwsTrainingApi.budget.getBudgetAllocations(department),
    [department]
  );
}

// Progress Data Hook
export function useProgressData(employeeId?: string) {
  return useApiCall<ProgressData[]>(
    () => mwsTrainingApi.progress.getProgressData(employeeId),
    [employeeId]
  );
}

// Feedback Hook
export function useFeedback(filters?: any) {
  return useApiCall<FeedbackResponse[]>(
    () => mwsTrainingApi.feedback.getFeedback(filters),
    [filters]
  );
}

// Achievements Hook
export function useAchievements(employeeId?: string) {
  return useApiCall<Achievement[]>(
    () => mwsTrainingApi.gamification.getAchievements(employeeId),
    [employeeId]
  );
}

// Leaderboard Hook
export function useLeaderboard() {
  return useApiCall<LeaderboardEntry[]>(
    () => mwsTrainingApi.gamification.getLeaderboard()
  );
}

// Learning Pathways Hook
export function useLearningPathways() {
  return useApiCall<LearningPathway[]>(
    () => mwsTrainingApi.pathways.getPathways()
  );
}

// Skills Gap Hook
export function useSkillGaps(department?: string) {
  return useApiCall<SkillGap[]>(
    () => mwsTrainingApi.skillsGap.getSkillGaps(department),
    [department]
  );
}

// Training Needs Hook
export function useTrainingNeeds(filters?: any) {
  return useApiCall<TrainingNeed[]>(
    () => mwsTrainingApi.trainingNeeds.getTrainingNeeds(filters),
    [filters]
  );
}

// LMS Courses Hook
export function useLMSCourses() {
  return useApiCall<LMSCourse[]>(
    () => mwsTrainingApi.lms.getCourses()
  );
}

// Report Templates Hook
export function useReportTemplates() {
  return useApiCall<ReportTemplate[]>(
    () => mwsTrainingApi.reporting.getReportTemplates()
  );
}

// Mutation Hooks (for create, update, delete operations)

// Schedule Management Hook
export function useScheduleManagement() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const createSchedule = useCallback(async (schedule: Partial<TrainingSchedule>) => {
    try {
      setLoading(true);
      setError(null);
      const result = await mwsTrainingApi.schedule.createSchedule(schedule);
      return result;
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to create schedule'));
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const updateSchedule = useCallback(async (id: string, updates: Partial<TrainingSchedule>) => {
    try {
      setLoading(true);
      setError(null);
      const result = await mwsTrainingApi.schedule.updateSchedule(id, updates);
      return result;
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to update schedule'));
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const deleteSchedule = useCallback(async (id: string) => {
    try {
      setLoading(true);
      setError(null);
      const result = await mwsTrainingApi.schedule.deleteSchedule(id);
      return result;
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to delete schedule'));
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    createSchedule,
    updateSchedule,
    deleteSchedule,
    loading,
    error
  };
}

// Certification Management Hook
export function useCertificationManagement() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const createCertification = useCallback(async (cert: Partial<Certification>) => {
    try {
      setLoading(true);
      setError(null);
      const result = await mwsTrainingApi.certification.createCertification(cert);
      return result;
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to create certification'));
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const updateCertification = useCallback(async (id: string, updates: Partial<Certification>) => {
    try {
      setLoading(true);
      setError(null);
      const result = await mwsTrainingApi.certification.updateCertification(id, updates);
      return result;
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to update certification'));
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const uploadCertificate = useCallback(async (id: string, file: File) => {
    try {
      setLoading(true);
      setError(null);
      const result = await mwsTrainingApi.certification.uploadCertificate(id, file);
      return result;
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to upload certificate'));
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    createCertification,
    updateCertification,
    uploadCertificate,
    loading,
    error
  };
}

// Feedback Management Hook
export function useFeedbackManagement() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const submitFeedback = useCallback(async (feedback: Partial<FeedbackResponse>) => {
    try {
      setLoading(true);
      setError(null);
      const result = await mwsTrainingApi.feedback.submitFeedback(feedback);
      return result;
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to submit feedback'));
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    submitFeedback,
    loading,
    error
  };
}

// Budget Management Hook
export function useBudgetManagement() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const createAllocation = useCallback(async (allocation: Partial<BudgetAllocation>) => {
    try {
      setLoading(true);
      setError(null);
      const result = await mwsTrainingApi.budget.createAllocation(allocation);
      return result;
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to create allocation'));
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const updateAllocation = useCallback(async (id: string, updates: Partial<BudgetAllocation>) => {
    try {
      setLoading(true);
      setError(null);
      const result = await mwsTrainingApi.budget.updateAllocation(id, updates);
      return result;
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to update allocation'));
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    createAllocation,
    updateAllocation,
    loading,
    error
  };
}

// Training Needs Management Hook
export function useTrainingNeedsManagement() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const createTrainingNeed = useCallback(async (need: Partial<TrainingNeed>) => {
    try {
      setLoading(true);
      setError(null);
      const result = await mwsTrainingApi.trainingNeeds.createTrainingNeed(need);
      return result;
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to create training need'));
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const updateTrainingNeed = useCallback(async (id: string, updates: Partial<TrainingNeed>) => {
    try {
      setLoading(true);
      setError(null);
      const result = await mwsTrainingApi.trainingNeeds.updateTrainingNeed(id, updates);
      return result;
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to update training need'));
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const approveNeed = useCallback(async (id: string, approverType: 'manager' | 'hr') => {
    try {
      setLoading(true);
      setError(null);
      const result = await mwsTrainingApi.trainingNeeds.approveNeed(id, approverType);
      return result;
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to approve training need'));
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    createTrainingNeed,
    updateTrainingNeed,
    approveNeed,
    loading,
    error
  };
}

// Batch Operations Hook
export function useBatchOperations() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const importTrainingData = useCallback(async (file: File, type: string) => {
    try {
      setLoading(true);
      setError(null);
      const result = await mwsTrainingApi.batch.importTrainingData(file, type);
      return result;
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to import data'));
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const bulkEnroll = useCallback(async (employeeIds: string[], programId: string) => {
    try {
      setLoading(true);
      setError(null);
      const result = await mwsTrainingApi.batch.bulkEnroll(employeeIds, programId);
      return result;
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to bulk enroll'));
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const bulkAssignCertifications = useCallback(async (employeeIds: string[], certificationIds: string[]) => {
    try {
      setLoading(true);
      setError(null);
      const result = await mwsTrainingApi.batch.bulkAssignCertifications(employeeIds, certificationIds);
      return result;
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to assign certifications'));
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    importTrainingData,
    bulkEnroll,
    bulkAssignCertifications,
    loading,
    error
  };
}

// Report Generation Hook
export function useReportGeneration() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const generateReport = useCallback(async (templateId: string, params?: any) => {
    try {
      setLoading(true);
      setError(null);
      const result = await mwsTrainingApi.reporting.generateReport(templateId, params);
      return result;
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to generate report'));
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const exportData = useCallback(async (format: 'csv' | 'excel' | 'pdf', data: any) => {
    try {
      setLoading(true);
      setError(null);
      const result = await mwsTrainingApi.reporting.exportData(format, data);
      return result;
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to export data'));
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    generateReport,
    exportData,
    loading,
    error
  };
}