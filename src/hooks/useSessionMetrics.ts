import { useState, useCallback, useRef, useEffect } from 'react';

export interface SessionMetrics {
  promptCount: number;
  tokenCount: number;
  toolExecutions: number;
  fileOperations: number;
  errors: number;
  checkpoints: number;
  startTime: number;
  lastActivityTime: number;
}

export interface MetricsSummary {
  totalPrompts: number;
  totalTokens: number;
  averageTokensPerPrompt: number;
  totalToolExecutions: number;
  totalFileOperations: number;
  errorRate: number;
  sessionDuration: number;
  checkpointsCreated: number;
}

export function useSessionMetrics(isActive: boolean) {
  const [metrics, setMetrics] = useState<SessionMetrics>({
    promptCount: 0,
    tokenCount: 0,
    toolExecutions: 0,
    fileOperations: 0,
    errors: 0,
    checkpoints: 0,
    startTime: Date.now(),
    lastActivityTime: Date.now(),
  });

  const metricsRef = useRef(metrics);
  metricsRef.current = metrics;

  const trackPrompt = useCallback((tokens: number) => {
    setMetrics(prev => ({
      ...prev,
      promptCount: prev.promptCount + 1,
      tokenCount: prev.tokenCount + tokens,
      lastActivityTime: Date.now(),
    }));
  }, []);

  const trackToolExecution = useCallback((toolName: string) => {
    setMetrics(prev => ({
      ...prev,
      toolExecutions: prev.toolExecutions + 1,
      lastActivityTime: Date.now(),
    }));
  }, []);

  const trackFileOperation = useCallback((operation: string) => {
    setMetrics(prev => ({
      ...prev,
      fileOperations: prev.fileOperations + 1,
      lastActivityTime: Date.now(),
    }));
  }, []);

  const trackError = useCallback((error: Error) => {
    setMetrics(prev => ({
      ...prev,
      errors: prev.errors + 1,
      lastActivityTime: Date.now(),
    }));
  }, []);

  const trackCheckpoint = useCallback(() => {
    setMetrics(prev => ({
      ...prev,
      checkpoints: prev.checkpoints + 1,
      lastActivityTime: Date.now(),
    }));
  }, []);

  const getMetricsSummary = useCallback((): MetricsSummary => {
    const current = metricsRef.current;
    const sessionDuration = Date.now() - current.startTime;
    
    return {
      totalPrompts: current.promptCount,
      totalTokens: current.tokenCount,
      averageTokensPerPrompt: current.promptCount > 0 
        ? Math.round(current.tokenCount / current.promptCount) 
        : 0,
      totalToolExecutions: current.toolExecutions,
      totalFileOperations: current.fileOperations,
      errorRate: current.promptCount > 0 
        ? (current.errors / current.promptCount) * 100 
        : 0,
      sessionDuration,
      checkpointsCreated: current.checkpoints,
    };
  }, []);

  // Reset metrics when session becomes inactive
  useEffect(() => {
    if (!isActive) {
      setMetrics({
        promptCount: 0,
        tokenCount: 0,
        toolExecutions: 0,
        fileOperations: 0,
        errors: 0,
        checkpoints: 0,
        startTime: Date.now(),
        lastActivityTime: Date.now(),
      });
    }
  }, [isActive]);

  return {
    metrics,
    trackPrompt,
    trackToolExecution,
    trackFileOperation,
    trackError,
    trackCheckpoint,
    getMetricsSummary,
  };
}