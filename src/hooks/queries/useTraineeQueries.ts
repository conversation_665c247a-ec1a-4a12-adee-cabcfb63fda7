import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { graduateTraineeApi, type GraduateTraineeProgram, type CreateGraduateTraineeProgramRequest, type UpdateGraduateTraineeProgramRequest } from '@/lib/api/graduateTrainee';
import { useToast } from '@/components/ui/use-toast';

// Query Keys
export const traineeKeys = {
  all: ['trainees'] as const,
  lists: () => [...traineeKeys.all, 'list'] as const,
  list: (filters?: any) => [...traineeKeys.lists(), filters] as const,
  details: () => [...traineeKeys.all, 'detail'] as const,
  detail: (id: number) => [...traineeKeys.details(), id] as const,
};

// ============= Trainee Queries =============

/**
 * Hook to fetch all trainee programs
 */
export function useTraineePrograms() {
  return useQuery({
    queryKey: traineeKeys.lists(),
    queryFn: () => graduateTraineeApi.getAllPrograms(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to fetch a specific trainee program by ID
 */
export function useTraineeProgram(id: number) {
  return useQuery({
    queryKey: traineeKeys.detail(id),
    queryFn: () => graduateTraineeApi.getProgram(id),
    enabled: !!id,
  });
}

/**
 * Hook to filter trainee programs
 */
export function useFilteredTraineePrograms(filter?: any) {
  return useQuery({
    queryKey: traineeKeys.list(filter),
    queryFn: () => {
      if (!filter) {
        return graduateTraineeApi.getAllPrograms();
      }
      return graduateTraineeApi.filterPrograms(filter);
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

// ============= Trainee Mutations =============

/**
 * Mutation to create a new trainee program
 */
export function useCreateTraineeProgram() {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  
  return useMutation({
    mutationFn: (request: CreateGraduateTraineeProgramRequest) => 
      graduateTraineeApi.createProgram(request),
    onSuccess: (data) => {
      // Invalidate and refetch trainee lists
      queryClient.invalidateQueries({ queryKey: traineeKeys.lists() });
      toast({
        title: "Success",
        description: "Trainee program created successfully",
      });
      return data;
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to create trainee program",
        variant: "destructive",
      });
    },
  });
}

/**
 * Mutation to update an existing trainee program
 */
export function useUpdateTraineeProgram() {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  
  return useMutation({
    mutationFn: (request: UpdateGraduateTraineeProgramRequest) => 
      graduateTraineeApi.updateProgram(request),
    onSuccess: (data) => {
      // Invalidate and refetch trainee lists and details
      queryClient.invalidateQueries({ queryKey: traineeKeys.lists() });
      if (data?.id) {
        queryClient.invalidateQueries({ queryKey: traineeKeys.detail(data.id) });
      }
      toast({
        title: "Success",
        description: "Trainee program updated successfully",
      });
      return data;
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to update trainee program",
        variant: "destructive",
      });
    },
  });
}

/**
 * Mutation to delete a trainee program
 */
export function useDeleteTraineeProgram() {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  
  return useMutation({
    mutationFn: (id: number) => graduateTraineeApi.deleteProgram(id),
    onSuccess: (_, variables) => {
      // Invalidate and refetch trainee lists
      queryClient.invalidateQueries({ queryKey: traineeKeys.lists() });
      queryClient.invalidateQueries({ queryKey: traineeKeys.detail(variables) });
      toast({
        title: "Success",
        description: "Trainee program deleted successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to delete trainee program",
        variant: "destructive",
      });
    },
  });
}

/**
 * Hook to invalidate all trainee-related queries
 */
export function useInvalidateTraineeQueries() {
  const queryClient = useQueryClient();
  
  return () => {
    queryClient.invalidateQueries({ queryKey: traineeKeys.all });
  };
}