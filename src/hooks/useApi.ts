import { useState, useEffect, useCallback } from 'react';
import { TrainingDatabaseService } from '@/services/trainingDatabaseService';
import { CompetencyModel, SkillMatrix } from '@/backend/types/competency';
import { Template, TemplateSearchFilters } from '@/backend/types/template';
import { QueryParams } from '@/backend/types/api';
import { useToast } from '@/components/ui/use-toast';

interface UseApiState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
}

interface UseApiActions<T> {
  refetch: () => Promise<void>;
  create: (item: Omit<T, 'id'>) => Promise<T | null>;
  update: (id: string, item: Partial<T>) => Promise<T | null>;
  remove: (id: string) => Promise<boolean>;
}

// Hook for managing competency models
export const useCompetencyModels = () => {
  const [state, setState] = useState<UseApiState<CompetencyModel[]>>({
    data: null,
    loading: true,
    error: null,
  });
  const { toast } = useToast();

  const fetchModels = useCallback(async (params?: QueryParams) => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      const models = await TrainingDatabaseService.getAllCompetencyModels();
      setState({ data: models, loading: false, error: null });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch competency models';
      setState({ data: null, loading: false, error: errorMessage });
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
    }
  }, [toast]);

  const createModel = useCallback(async (model: Omit<CompetencyModel, 'id'>): Promise<CompetencyModel | null> => {
    try {
      const createdModel = await TrainingDatabaseService.createCompetencyModel(model);
      if (createdModel) {
        setState((prev: UseApiState<CompetencyModel[]>) => ({
        ...prev,
        data: prev.data ? [...prev.data, createdModel] : [createdModel]
      }));
        toast({
          title: "Success",
          description: "Competency model created successfully"
        });
        return createdModel;
      }
      return null;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create competency model';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
      return null;
    }
  }, [toast]);

  const updateModel = useCallback(async (id: string, model: Partial<CompetencyModel>): Promise<CompetencyModel | null> => {
    try {
      const updatedModel = await TrainingDatabaseService.updateCompetencyModel(id, model);
      if (updatedModel) {
        setState((prev: UseApiState<CompetencyModel[]>) => ({
           ...prev,
           data: prev.data ? prev.data.map((m: CompetencyModel) => m.id === id ? updatedModel : m) : null
         }));
        toast({
          title: "Success",
          description: "Competency model updated successfully"
        });
        return updatedModel;
      }
      return null;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update competency model';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
      return null;
    }
  }, [toast]);

  const deleteModel = useCallback(async (id: string): Promise<boolean> => {
    try {
      await TrainingDatabaseService.deleteCompetencyModel(id);
      setState((prev: UseApiState<CompetencyModel[]>) => ({
         ...prev,
         data: prev.data ? prev.data.filter((m: CompetencyModel) => m.id !== id) : null
       }));
      toast({
        title: "Success",
        description: "Competency model deleted successfully"
      });
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete competency model';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
      return false;
    }
  }, [toast]);

  const exportModel = useCallback(async (id: string, format: 'json' | 'excel' = 'json') => {
    try {
      const blob = await TrainingDatabaseService.exportCompetencyModel(id, format);
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `competency-model-${id}.${format === 'json' ? 'json' : 'xlsx'}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
      toast({
        title: "Success",
        description: "Competency model exported successfully"
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to export competency model';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
    }
  }, [toast]);

  const importModel = useCallback(async (file: File): Promise<CompetencyModel | null> => {
    try {
      const importedModel = await TrainingDatabaseService.importCompetencyModel(file);
      if (importedModel) {
        setState((prev: UseApiState<CompetencyModel[]>) => ({
           ...prev,
           data: prev.data ? [...prev.data, importedModel] : [importedModel]
         }));
        toast({
          title: "Success",
          description: "Competency model imported successfully"
        });
        return importedModel;
      }
      return null;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to import competency model';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
      return null;
    }
  }, [toast]);

  useEffect(() => {
    fetchModels();
  }, [fetchModels]);

  return {
    ...state,
    refetch: fetchModels,
    create: createModel,
    update: updateModel,
    remove: deleteModel,
    export: exportModel,
    import: importModel,
  };
};

// Hook for managing skill matrices
export const useSkillMatrices = () => {
  const [state, setState] = useState<UseApiState<SkillMatrix[]>>({
    data: null,
    loading: true,
    error: null,
  });
  const { toast } = useToast();

  const fetchMatrices = useCallback(async (params?: QueryParams) => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      const matrices = await TrainingDatabaseService.getAllSkillMatrices();
      setState({ data: matrices, loading: false, error: null });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch skill matrices';
      setState({ data: null, loading: false, error: errorMessage });
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
    }
  }, [toast]);

  const createMatrix = useCallback(async (matrix: Omit<SkillMatrix, 'id'>): Promise<SkillMatrix | null> => {
    try {
      const createdMatrix = await TrainingDatabaseService.createSkillMatrix(matrix);
      if (createdMatrix) {
        setState((prev: UseApiState<SkillMatrix[]>) => ({
        ...prev,
        data: prev.data ? [...prev.data, createdMatrix] : [createdMatrix]
      }));
        toast({
          title: "Success",
          description: "Skill matrix created successfully"
        });
        return createdMatrix;
      }
      return null;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create skill matrix';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
      return null;
    }
  }, [toast]);

  const updateMatrix = useCallback(async (id: string, matrix: Partial<SkillMatrix>): Promise<SkillMatrix | null> => {
    try {
      const updatedMatrix = await TrainingDatabaseService.updateSkillMatrix(id, matrix);
      if (updatedMatrix) {
        setState(prev => ({
          ...prev,
          data: prev.data ? prev.data.map(m => m.id === id ? updatedMatrix : m) : null
        }));
        toast({
          title: "Success",
          description: "Skill matrix updated successfully"
        });
        return updatedMatrix;
      }
      return null;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update skill matrix';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
      return null;
    }
  }, [toast]);

  const deleteMatrix = useCallback(async (id: string): Promise<boolean> => {
    try {
      await TrainingDatabaseService.deleteSkillMatrix(id);
      setState(prev => ({
        ...prev,
        data: prev.data ? prev.data.filter(m => m.id !== id) : null
      }));
      toast({
        title: "Success",
        description: "Skill matrix deleted successfully"
      });
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete skill matrix';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
      return false;
    }
  }, [toast]);

  useEffect(() => {
    fetchMatrices();
  }, [fetchMatrices]);

  return {
    ...state,
    refetch: fetchMatrices,
    create: createMatrix,
    update: updateMatrix,
    remove: deleteMatrix,
  };
};

// Hook for managing templates
export const useTemplates = () => {
  const [state, setState] = useState<UseApiState<Template[]>>({
    data: null,
    loading: true,
    error: null,
  });
  const { toast } = useToast();

  const fetchTemplates = useCallback(async (params?: QueryParams) => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      const response = await apiService.getTemplates(params);
      setState({ data: response.data, loading: false, error: null });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch templates';
      setState({ data: null, loading: false, error: errorMessage });
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
    }
  }, [toast]);

  const searchTemplates = useCallback(async (filters: TemplateSearchFilters) => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      const response = await apiService.searchTemplates(filters);
      setState({ data: response.data, loading: false, error: null });
      toast({
        title: "Search Complete",
        description: `Found ${response.data.length} templates`
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to search templates';
      setState({ data: null, loading: false, error: errorMessage });
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
    }
  }, [toast]);

  const uploadTemplate = useCallback(async (file: File, metadata?: any): Promise<Template | null> => {
    try {
      const response = await apiService.uploadTemplate(file, metadata);
      if (response.data) {
        setState((prev: UseApiState<Template[]>) => ({
          ...prev,
          data: prev.data ? [...prev.data, response.data!] : [response.data!]
        }));
        toast({
          title: "Success",
          description: "Template uploaded successfully"
        });
        return response.data;
      }
      return null;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to upload template';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
      return null;
    }
  }, [toast]);

  const downloadTemplate = useCallback(async (id: string, filename?: string) => {
    try {
      const blob = await apiService.downloadTemplate(id);
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename || `template-${id}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
      toast({
        title: "Success",
        description: "Template downloaded successfully"
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to download template';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
    }
  }, [toast]);

  useEffect(() => {
    fetchTemplates();
  }, [fetchTemplates]);

  return {
    ...state,
    refetch: fetchTemplates,
    search: searchTemplates,
    upload: uploadTemplate,
    download: downloadTemplate,
  };
};

// Hook for analytics
export const useAnalytics = () => {
  const [state, setState] = useState<UseApiState<any>>({
    data: null,
    loading: true,
    error: null,
  });
  const { toast } = useToast();

  const fetchAnalytics = useCallback(async (params?: QueryParams) => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      const response = await apiService.getCompetencyAnalytics(params);
      setState({ data: response.data, loading: false, error: null });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch analytics';
      setState({ data: null, loading: false, error: errorMessage });
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
    }
  }, [toast]);

  useEffect(() => {
    fetchAnalytics();
  }, [fetchAnalytics]);

  return {
    ...state,
    refetch: fetchAnalytics,
  };
};