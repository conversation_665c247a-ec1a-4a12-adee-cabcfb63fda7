import { useCallback, useState, useEffect } from 'react';
import { api, type Session } from '@/lib/api';

interface SystemHealth {
  claudeCodeStatus: 'connected' | 'disconnected';
  claudeVersion: string;
  apiConnection: 'online' | 'slow' | 'offline';
  responseTime: number;
  diskSpace: {
    used: number;
    total: number;
    percentage: number;
  };
  activeProcesses: number;
  healthScore: number;
}

export const useSystemHealth = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchSystemHealth = useCallback(async (): Promise<SystemHealth> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const claudeStatus = await api.checkClaudeVersion();
      const startTime = Date.now();
      const processes = await api.listProcesses();
      const responseTime = Date.now() - startTime;
      
      const diskSpace = {
        used: 45.2,
        total: 100,
        percentage: 45.2
      };
      
      const healthScore = claudeStatus.is_installed ? 85 : 50;
      
      return {
        claudeCodeStatus: claudeStatus.is_installed ? 'connected' : 'disconnected',
        claudeVersion: claudeStatus.version,
        apiConnection: responseTime < 100 ? 'online' : responseTime < 500 ? 'slow' : 'offline',
        responseTime,
        diskSpace,
        activeProcesses: processes.length,
        healthScore
      };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch system health';
      setError(errorMessage);
      
      // Return fallback data
      return {
        claudeCodeStatus: 'disconnected',
        claudeVersion: 'Unknown',
        apiConnection: 'offline',
        responseTime: 0,
        diskSpace: { used: 0, total: 100, percentage: 0 },
        activeProcesses: 0,
        healthScore: 0
      };
    } finally {
      setIsLoading(false);
    }
  }, []);

  return { fetchSystemHealth, isLoading, error };
};

export const useWelcomeData = () => {
  const fetchRecentSessions = useCallback(async () => {
    const projects = await api.listProjects();
    const allSessions: Session[] = [];
    
    for (const project of projects.slice(0, 5)) {
      const sessions = await api.getProjectSessions(project.id);
      allSessions.push(...sessions);
    }
    
    return allSessions
      .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
      .slice(0, 5);
  }, []);

  return { fetchRecentSessions };
};