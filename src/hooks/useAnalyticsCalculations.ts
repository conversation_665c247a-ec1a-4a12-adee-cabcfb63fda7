/**
 * Analytics Calculations Hook
 * Provides real-time analytics calculations for L&D components
 */

import { useState, useEffect, useMemo } from 'react';
import { analyticsEngine, type KirkpatrickMetrics, type ROIAnalysis, type EffectivenessMetrics, type TrendsInsights, type Assessment, type TrainingProgram, type HistoricalData } from '@/services/analyticsCalculationEngine';
import { useCompetencyModels, useSkillMatrices, useTemplates, useAnalytics } from './useApi';
import { CompetencyModel, SkillMatrix } from '@/components/ld-tna/common/types';

export interface AnalyticsCalculationsState {
  kirkpatrick: KirkpatrickMetrics | null;
  roi: ROIAnalysis | null;
  effectiveness: EffectivenessMetrics | null;
  trends: TrendsInsights | null;
  loading: boolean;
  error: string | null;
}

export interface UseAnalyticsCalculationsReturn extends AnalyticsCalculationsState {
  recalculate: () => void;
  updateData: (data: {
    competencyModels?: CompetencyModel[];
    skillMatrices?: SkillMatrix[];
    assessments?: Assessment[];
    trainingPrograms?: TrainingProgram[];
    historicalData?: any[];
  }) => void;
}

export const useAnalyticsCalculations = (): UseAnalyticsCalculationsReturn => {
  const [state, setState] = useState<AnalyticsCalculationsState>({
    kirkpatrick: null,
    roi: null,
    effectiveness: null,
    trends: null,
    loading: true,
    error: null
  });

  // Fetch data from API hooks
  const competencyModelsApi = useCompetencyModels();
  const skillMatricesApi = useSkillMatrices();
  const templatesApi = useTemplates();
  const analyticsApi = useAnalytics();

  // Mock assessments and training programs data
  const mockAssessments: Assessment[] = useMemo(() => [
    {
      id: '1',
      employeeId: 'user1',
      competencyId: 'comp1',
      skillId: 'skill1',
      score: 85,
      maxScore: 100,
      assessmentDate: new Date().toISOString(),
      assessmentType: 'feedback'
    },
    {
      id: '2',
      employeeId: 'user2',
      competencyId: 'comp2',
      skillId: 'skill2',
      score: 84,
      maxScore: 100,
      assessmentDate: new Date().toISOString(),
      assessmentType: 'post-training'
    },
    {
      id: '3',
      employeeId: 'user3',
      competencyId: 'comp3',
      skillId: 'skill3',
      score: 78,
      maxScore: 100,
      assessmentDate: new Date().toISOString(),
      assessmentType: 'practical'
    },
    {
      id: '4',
      employeeId: 'user1',
      competencyId: 'comp4',
      skillId: 'skill4',
      score: 76,
      maxScore: 100,
      assessmentDate: new Date().toISOString(),
      assessmentType: 'follow-up'
    },
    {
      id: '5',
      employeeId: 'user2',
      competencyId: 'comp5',
      skillId: 'skill5',
      score: 68,
      maxScore: 100,
      assessmentDate: new Date().toISOString(),
      assessmentType: '360-feedback'
    }
  ], []);

  const mockTrainingPrograms: TrainingProgram[] = useMemo(() => [
    {
      id: '1',
      name: 'Leadership Development Program',
      type: 'leadership',
      duration: 40,
      cost: 254200,
      participants: 150,
      completionRate: 86.7,
      satisfactionScore: 4.6,
      effectivenessScore: 88,
      roiPercentage: 245,
      createdAt: new Date().toISOString()
    },
    {
      id: '2',
      name: 'Technical Skills Enhancement',
      type: 'technical',
      duration: 60,
      cost: 143000,
      participants: 200,
      completionRate: 87.5,
      satisfactionScore: 4.5,
      effectivenessScore: 91,
      roiPercentage: 312,
      createdAt: new Date().toISOString()
    }
  ], []);

  const mockHistoricalData: HistoricalData[] = useMemo(() => [
    {
      period: '2024-Q1',
      metrics: {
        performanceImprovement: 82,
        productivityGain: 28,
        costSavings: 15,
        qualityScore: 4.3,
        turnoverReduction: 25,
        engagementIncrease: 12
      }
    },
    {
      period: '2024-Q2',
      metrics: {
        performanceImprovement: 85,
        productivityGain: 32,
        costSavings: 18,
        qualityScore: 4.5,
        turnoverReduction: 28,
        engagementIncrease: 15
      }
    }
  ], []);

  // Calculate analytics when data changes
  const calculateAnalytics = useMemo(() => {
    return () => {
      try {
        setState(prev => ({ ...prev, loading: true, error: null }));

        // Initialize analytics engine with current data
        analyticsEngine.initialize({
          competencyModels: (competencyModelsApi.data || []).map(cm => ({
            ...cm,
            levels: cm.levels || ['Beginner', 'Intermediate', 'Advanced', 'Expert'],
            businessCriticality: cm.businessCriticality || 'medium'
          })),
          skillMatrices: (skillMatricesApi.data || []).map(sm => ({
            ...sm,
            employeeId: sm.employeeId || 'unknown',
            employeeName: sm.employeeName || 'Unknown Employee',
            role: sm.role || 'Unknown Role',
            department: sm.department || 'Unknown Department',
            assessmentDate: sm.assessmentDate || new Date().toISOString(),
            overallScore: sm.overallScore || 0
          })),
          assessments: mockAssessments,
          trainingPrograms: mockTrainingPrograms,
          historicalData: mockHistoricalData
        });

        // Calculate all metrics
        const kirkpatrick = analyticsEngine.calculateKirkpatrickMetrics();
        const roi = analyticsEngine.calculateROIAnalysis();
        const effectiveness = analyticsEngine.calculateEffectivenessMetrics();
        const trends = analyticsEngine.calculateTrendsInsights();

        setState({
          kirkpatrick,
          roi,
          effectiveness,
          trends,
          loading: false,
          error: null
        });
      } catch (error) {
        setState(prev => ({
          ...prev,
          loading: false,
          error: error instanceof Error ? error.message : 'Failed to calculate analytics'
        }));
      }
    };
  }, [
    competencyModelsApi.data,
    skillMatricesApi.data,
    mockAssessments,
    mockTrainingPrograms,
    mockHistoricalData
  ]);

  // Recalculate when dependencies change
  useEffect(() => {
    if (!competencyModelsApi.loading && !skillMatricesApi.loading) {
      calculateAnalytics();
    }
  }, [
    competencyModelsApi.loading,
    skillMatricesApi.loading,
    calculateAnalytics
  ]);

  // Manual recalculation function
  const recalculate = () => {
    calculateAnalytics();
  };

  // Update data function for external data injection
  const updateData = (data: {
    competencyModels?: CompetencyModel[];
    skillMatrices?: SkillMatrix[];
    assessments?: Assessment[];
    trainingPrograms?: TrainingProgram[];
    historicalData?: any[];
  }) => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));

      // Update the existing analytics engine data instead of reinitializing
      const updatedData = {
        competencyModels: (data.competencyModels || competencyModelsApi.data || []).map(cm => ({
          ...cm,
          levels: cm.levels || ['Beginner', 'Intermediate', 'Advanced', 'Expert'],
          businessCriticality: cm.businessCriticality || 'medium'
        })),
        skillMatrices: (data.skillMatrices || skillMatricesApi.data || []).map(sm => ({
          ...sm,
          employeeId: sm.employeeId || 'unknown',
          employeeName: sm.employeeName || 'Unknown Employee',
          role: sm.role || 'Unknown Role',
          department: sm.department || 'Unknown Department',
          assessmentDate: sm.assessmentDate || new Date().toISOString(),
          overallScore: sm.overallScore || 0
        })),
        assessments: data.assessments || mockAssessments,
        trainingPrograms: data.trainingPrograms || mockTrainingPrograms,
        historicalData: data.historicalData || mockHistoricalData
      };

      analyticsEngine.initialize(updatedData);

      // Calculate all metrics
      const kirkpatrick = analyticsEngine.calculateKirkpatrickMetrics();
      const roi = analyticsEngine.calculateROIAnalysis();
      const effectiveness = analyticsEngine.calculateEffectivenessMetrics();
      const trends = analyticsEngine.calculateTrendsInsights();

      setState({
        kirkpatrick,
        roi,
        effectiveness,
        trends,
        loading: false,
        error: null
      });
    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Failed to update analytics'
      }));
    }
  };

  return {
    ...state,
    recalculate,
    updateData
  };
};

export default useAnalyticsCalculations;