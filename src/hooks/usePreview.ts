import { useState, useCallback } from 'react';

export function usePreview() {
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [previewError, setPreviewError] = useState<string | null>(null);

  const openPreview = useCallback((url: string) => {
    setPreviewUrl(url);
    setIsPreviewOpen(true);
    setPreviewError(null);
  }, []);

  const closePreview = useCallback(() => {
    setIsPreviewOpen(false);
    // Keep URL for potential reopening
  }, []);

  const updatePreviewUrl = useCallback((url: string) => {
    setPreviewUrl(url);
    setPreviewError(null);
  }, []);

  const setError = useCallback((error: string) => {
    setPreviewError(error);
  }, []);

  const clearPreview = useCallback(() => {
    setPreviewUrl(null);
    setIsPreviewOpen(false);
    setPreviewError(null);
  }, []);

  return {
    previewUrl,
    isPreviewOpen,
    previewError,
    openPreview,
    closePreview,
    updatePreviewUrl,
    setError,
    clearPreview,
  };
}