import type { GraduateTraineeProgram } from './graduateTrainee';

// Mock data for development
export const getMockTrainees = (): GraduateTraineeProgram[] => {
  return [
    {
      id: 1,
      employeeId: 'EMP0001',
      employeeName: '<PERSON>',
      department: 'Engineering',
      position: 'Software Engineer',
      manager: '<PERSON>',
      mentor: '<PERSON>',
      startDate: '2024-01-15',
      expectedEndDate: '2025-07-15',
      programDurationMonths: 18,
      status: 'in_progress',
      overallProgress: 65,
      quarterlyReviews: [
        {
          reviewNumber: 1,
          dueDate: '2024-04-15',
          completedDate: '2024-04-10',
          reviewer: '<PERSON>',
          overallRating: 4,
          feedback: 'Excellent progress in the first quarter. Shows strong technical skills.',
          goalsAchieved: ['Complete onboarding', 'Shadow senior team members'],
          goalsPending: [],
          skillsImproved: ['Technical skills', 'Communication'],
          areasForImprovement: ['Time management'],
          nextQuarterGoals: ['Lead a small project'],
          supportNeeded: ['Project management training'],
          isSatisfactory: true,
          continuationRecommendation: true
        }
      ],
      skillsDevelopment: [
        {
          skillName: 'React Development',
          category: 'Technical Skills',
          baselineLevel: 2,
          targetLevel: 4,
          currentLevel: 3,
          progressPercentage: 75,
          trainingCompleted: ['React Fundamentals', 'Advanced React Patterns'],
          nextSteps: 'Complete React Testing workshop'
        }
      ],
      certifications: [
        {
          name: 'AWS Certified Developer',
          issuingBody: 'Amazon Web Services',
          dateAchieved: '2024-03-15',
          expiryDate: '2027-03-15',
          isMandatory: true,
          status: 'completed'
        }
      ],
      trainingHoursCompleted: 104,
      trainingHoursRequired: 160,
      budgetAllocated: 55000,
      budgetSpent: 35750,
      notes: 'High potential candidate, consider for leadership track',
      createdAt: '2024-01-15T00:00:00Z',
      updatedAt: '2024-03-20T00:00:00Z'
    },
    {
      id: 2,
      employeeId: 'EMP0002',
      employeeName: 'Jordan Smith',
      department: 'Marketing',
      position: 'Marketing Specialist',
      manager: 'Emily Davis',
      startDate: '2024-02-01',
      expectedEndDate: '2025-08-01',
      programDurationMonths: 18,
      status: 'in_progress',
      overallProgress: 45,
      quarterlyReviews: [],
      skillsDevelopment: [
        {
          skillName: 'Digital Marketing',
          category: 'Marketing',
          baselineLevel: 1,
          targetLevel: 4,
          currentLevel: 2,
          progressPercentage: 50,
          trainingCompleted: ['Marketing Fundamentals'],
          nextSteps: 'Complete Google Analytics certification'
        }
      ],
      certifications: [
        {
          name: 'Google Analytics Certified',
          issuingBody: 'Google',
          isMandatory: false,
          status: 'in_progress'
        }
      ],
      trainingHoursCompleted: 72,
      trainingHoursRequired: 160,
      budgetAllocated: 45000,
      budgetSpent: 20250,
      createdAt: '2024-02-01T00:00:00Z',
      updatedAt: '2024-03-15T00:00:00Z'
    }
  ];
};