import { GraduateTraineeProgram, TraineeStatus } from './graduateTrainee';

const mockTrainees: GraduateTraineeProgram[] = [
  {
    id: 1,
    employeeId: 'EMP001',
    employeeName: '<PERSON>',
    department: 'Technology',
    position: 'Junior Developer',
    manager: '<PERSON>',
    mentor: '<PERSON>',
    startDate: '2024-01-15',
    expectedEndDate: '2025-01-15',
    programDurationMonths: 12,
    status: 'in_progress' as TraineeStatus,
    overallProgress: 65,
    quarterlyReviews: [
      {
        reviewNumber: 1,
        dueDate: '2024-04-15',
        completedDate: '2024-04-10',
        reviewer: '<PERSON>',
        overallRating: 4,
        feedback: 'Excellent progress in technical skills',
        goalsAchieved: ['Completed React training', 'Built first project'],
        goalsPending: ['AWS certification'],
        skillsImproved: ['React', 'TypeScript'],
        areasForImprovement: ['System design'],
        nextQuarterGoals: ['Complete AWS certification', 'Lead a small project'],
        supportNeeded: ['Mentorship on architecture'],
        isSatisfactory: true,
        continuationRecommendation: true
      }
    ],
    skillsDevelopment: [
      {
        skillName: 'React',
        category: 'Frontend',
        baselineLevel: 2,
        targetLevel: 8,
        currentLevel: 6,
        progressPercentage: 75,
        trainingCompleted: ['React Fundamentals', 'Advanced Patterns'],
        nextSteps: 'Performance optimization'
      },
      {
        skillName: 'TypeScript',
        category: 'Programming',
        baselineLevel: 1,
        targetLevel: 7,
        currentLevel: 5,
        progressPercentage: 71,
        trainingCompleted: ['TypeScript Basics'],
        nextSteps: 'Advanced types'
      }
    ],
    certifications: [
      {
        name: 'AWS Cloud Practitioner',
        issuingBody: 'Amazon',
        isMandatory: true,
        status: 'in_progress'
      }
    ],
    trainingHoursCompleted: 120,
    trainingHoursRequired: 200,
    budgetAllocated: 5000,
    budgetSpent: 3000,
    notes: 'Strong performer, showing great potential',
    createdAt: '2024-01-15T00:00:00Z',
    updatedAt: '2024-10-01T00:00:00Z'
  },
  {
    id: 2,
    employeeId: 'EMP002',
    employeeName: 'Emma Davis',
    department: 'Finance',
    position: 'Junior Analyst',
    manager: 'Robert Chen',
    mentor: 'Lisa Brown',
    startDate: '2024-02-01',
    expectedEndDate: '2025-02-01',
    programDurationMonths: 12,
    status: 'in_progress' as TraineeStatus,
    overallProgress: 45,
    quarterlyReviews: [],
    skillsDevelopment: [
      {
        skillName: 'Financial Modeling',
        category: 'Finance',
        baselineLevel: 3,
        targetLevel: 8,
        currentLevel: 5,
        progressPercentage: 40,
        trainingCompleted: ['Excel Advanced'],
        nextSteps: 'DCF modeling'
      }
    ],
    certifications: [
      {
        name: 'CPA',
        issuingBody: 'AICPA',
        isMandatory: false,
        status: 'not_started'
      }
    ],
    trainingHoursCompleted: 80,
    trainingHoursRequired: 180,
    budgetAllocated: 4000,
    budgetSpent: 1800,
    createdAt: '2024-02-01T00:00:00Z',
    updatedAt: '2024-10-01T00:00:00Z'
  },
  {
    id: 3,
    employeeId: 'EMP003',
    employeeName: 'Michael Zhang',
    department: 'Marketing',
    position: 'Junior Marketing Specialist',
    manager: 'Jennifer Lee',
    mentor: 'David Kim',
    startDate: '2023-09-01',
    expectedEndDate: '2024-09-01',
    actualEndDate: '2024-09-01',
    programDurationMonths: 12,
    status: 'completed' as TraineeStatus,
    overallProgress: 100,
    quarterlyReviews: [
      {
        reviewNumber: 4,
        dueDate: '2024-09-01',
        completedDate: '2024-08-28',
        reviewer: 'Jennifer Lee',
        overallRating: 5,
        feedback: 'Outstanding completion of program',
        goalsAchieved: ['All goals achieved'],
        goalsPending: [],
        skillsImproved: ['Digital Marketing', 'Analytics', 'Content Strategy'],
        areasForImprovement: [],
        nextQuarterGoals: ['Transition to full role'],
        supportNeeded: [],
        isSatisfactory: true,
        continuationRecommendation: true
      }
    ],
    skillsDevelopment: [
      {
        skillName: 'Digital Marketing',
        category: 'Marketing',
        baselineLevel: 2,
        targetLevel: 8,
        currentLevel: 8,
        progressPercentage: 100,
        trainingCompleted: ['Google Ads', 'Facebook Marketing', 'SEO'],
        nextSteps: 'Advanced strategies'
      }
    ],
    certifications: [
      {
        name: 'Google Analytics',
        issuingBody: 'Google',
        dateAchieved: '2024-06-15',
        isMandatory: true,
        status: 'completed'
      }
    ],
    trainingHoursCompleted: 200,
    trainingHoursRequired: 200,
    budgetAllocated: 4500,
    budgetSpent: 4200,
    notes: 'Successfully completed program, promoted to Marketing Specialist',
    createdAt: '2023-09-01T00:00:00Z',
    updatedAt: '2024-09-01T00:00:00Z'
  },
  {
    id: 4,
    employeeId: 'EMP004',
    employeeName: 'Sophia Martinez',
    department: 'Human Resources',
    position: 'Junior HR Coordinator',
    manager: 'Patricia Wilson',
    startDate: '2024-03-01',
    expectedEndDate: '2025-03-01',
    programDurationMonths: 12,
    status: 'on_hold' as TraineeStatus,
    overallProgress: 30,
    quarterlyReviews: [],
    skillsDevelopment: [
      {
        skillName: 'Recruitment',
        category: 'HR',
        baselineLevel: 1,
        targetLevel: 7,
        currentLevel: 3,
        progressPercentage: 33,
        trainingCompleted: ['Recruitment Basics'],
        nextSteps: 'Advanced sourcing'
      }
    ],
    certifications: [],
    trainingHoursCompleted: 60,
    trainingHoursRequired: 160,
    budgetAllocated: 3500,
    budgetSpent: 1000,
    notes: 'Program on hold due to medical leave',
    createdAt: '2024-03-01T00:00:00Z',
    updatedAt: '2024-10-01T00:00:00Z'
  },
  {
    id: 5,
    employeeId: 'EMP005',
    employeeName: 'James Thompson',
    department: 'Operations',
    position: 'Junior Operations Analyst',
    manager: 'Kevin Brown',
    mentor: 'Rachel Green',
    startDate: '2024-04-15',
    expectedEndDate: '2025-04-15',
    programDurationMonths: 12,
    status: 'in_progress' as TraineeStatus,
    overallProgress: 25,
    quarterlyReviews: [],
    skillsDevelopment: [
      {
        skillName: 'Process Optimization',
        category: 'Operations',
        baselineLevel: 2,
        targetLevel: 8,
        currentLevel: 4,
        progressPercentage: 33,
        trainingCompleted: ['Lean Six Sigma Yellow Belt'],
        nextSteps: 'Green Belt certification'
      }
    ],
    certifications: [
      {
        name: 'Lean Six Sigma Green Belt',
        issuingBody: 'ASQ',
        isMandatory: true,
        status: 'in_progress'
      }
    ],
    trainingHoursCompleted: 40,
    trainingHoursRequired: 180,
    budgetAllocated: 5500,
    budgetSpent: 1200,
    createdAt: '2024-04-15T00:00:00Z',
    updatedAt: '2024-10-01T00:00:00Z'
  },
  {
    id: 6,
    employeeId: 'EMP006',
    employeeName: 'Olivia Anderson',
    department: 'Technology',
    position: 'Junior Data Analyst',
    manager: 'Daniel Moore',
    mentor: 'Chris Taylor',
    startDate: '2024-05-01',
    expectedEndDate: '2025-05-01',
    programDurationMonths: 12,
    status: 'not_started' as TraineeStatus,
    overallProgress: 0,
    quarterlyReviews: [],
    skillsDevelopment: [
      {
        skillName: 'Python',
        category: 'Programming',
        baselineLevel: 3,
        targetLevel: 9,
        currentLevel: 3,
        progressPercentage: 0,
        trainingCompleted: [],
        nextSteps: 'Start Python fundamentals course'
      },
      {
        skillName: 'SQL',
        category: 'Database',
        baselineLevel: 2,
        targetLevel: 8,
        currentLevel: 2,
        progressPercentage: 0,
        trainingCompleted: [],
        nextSteps: 'SQL basics training'
      }
    ],
    certifications: [
      {
        name: 'Microsoft Data Analyst',
        issuingBody: 'Microsoft',
        isMandatory: false,
        status: 'not_started'
      }
    ],
    trainingHoursCompleted: 0,
    trainingHoursRequired: 200,
    budgetAllocated: 6000,
    budgetSpent: 0,
    notes: 'Scheduled to start next month',
    createdAt: '2024-05-01T00:00:00Z',
    updatedAt: '2024-10-01T00:00:00Z'
  },
  {
    id: 7,
    employeeId: 'EMP007',
    employeeName: 'William Garcia',
    department: 'Sales',
    position: 'Junior Sales Representative',
    manager: 'Jennifer White',
    startDate: '2023-06-01',
    expectedEndDate: '2024-06-01',
    programDurationMonths: 12,
    status: 'terminated' as TraineeStatus,
    overallProgress: 40,
    quarterlyReviews: [],
    skillsDevelopment: [],
    certifications: [],
    trainingHoursCompleted: 70,
    trainingHoursRequired: 160,
    budgetAllocated: 4000,
    budgetSpent: 1500,
    notes: 'Program terminated - employee resigned',
    createdAt: '2023-06-01T00:00:00Z',
    updatedAt: '2024-02-01T00:00:00Z'
  },
  {
    id: 8,
    employeeId: 'EMP008',
    employeeName: 'Isabella Rodriguez',
    department: 'Customer Success',
    position: 'Junior Customer Success Manager',
    manager: 'Thomas Harris',
    mentor: 'Nancy Clark',
    startDate: '2024-01-01',
    expectedEndDate: '2025-01-01',
    programDurationMonths: 12,
    status: 'in_progress' as TraineeStatus,
    overallProgress: 70,
    quarterlyReviews: [
      {
        reviewNumber: 2,
        dueDate: '2024-07-01',
        completedDate: '2024-06-28',
        reviewer: 'Thomas Harris',
        overallRating: 4.5,
        feedback: 'Excellent customer interaction skills',
        goalsAchieved: ['Onboarded 10 clients', 'Achieved 95% CSAT'],
        goalsPending: ['Upselling training'],
        skillsImproved: ['Communication', 'Product Knowledge'],
        areasForImprovement: ['Technical troubleshooting'],
        nextQuarterGoals: ['Complete technical certification'],
        supportNeeded: ['Technical mentorship'],
        isSatisfactory: true,
        continuationRecommendation: true
      }
    ],
    skillsDevelopment: [
      {
        skillName: 'Customer Relationship Management',
        category: 'Customer Success',
        baselineLevel: 3,
        targetLevel: 9,
        currentLevel: 7,
        progressPercentage: 67,
        trainingCompleted: ['CRM Fundamentals', 'Advanced Customer Engagement'],
        nextSteps: 'Strategic account management'
      }
    ],
    certifications: [
      {
        name: 'Customer Success Professional',
        issuingBody: 'SuccessHacker',
        isMandatory: false,
        status: 'in_progress'
      }
    ],
    trainingHoursCompleted: 140,
    trainingHoursRequired: 180,
    budgetAllocated: 4500,
    budgetSpent: 3200,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-10-01T00:00:00Z'
  }
];

export const getMockTrainees = (): GraduateTraineeProgram[] => {
  return mockTrainees;
};

export const getMockTraineeById = (id: number): GraduateTraineeProgram | undefined => {
  return mockTrainees.find(t => t.id === id);
};

let nextId = 9;

export const addMockTrainee = (trainee: Omit<GraduateTraineeProgram, 'id'>): GraduateTraineeProgram => {
  const newTrainee = {
    ...trainee,
    id: nextId++
  };
  mockTrainees.push(newTrainee);
  return newTrainee;
};

export const updateMockTrainee = (id: number, updates: Partial<GraduateTraineeProgram>): GraduateTraineeProgram | undefined => {
  const index = mockTrainees.findIndex(t => t.id === id);
  if (index !== -1) {
    mockTrainees[index] = { ...mockTrainees[index], ...updates };
    return mockTrainees[index];
  }
  return undefined;
};

export const deleteMockTrainee = (id: number): boolean => {
  const index = mockTrainees.findIndex(t => t.id === id);
  if (index !== -1) {
    mockTrainees.splice(index, 1);
    return true;
  }
  return false;
};