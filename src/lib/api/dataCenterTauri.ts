// Data Center API - Tauri IPC Version (No HTTP)
import { invoke } from '@tauri-apps/api/core';
import { listen } from '@tauri-apps/api/event';

export interface DataSource {
  source_id: string;
  source_name: string;
  source_type: 'csv' | 'excel' | 'json' | 'sql' | 'api' | 'xml' | 'parquet' | 'avro';
  connection_status: 'connected' | 'disconnected' | 'error' | 'syncing';
  last_sync: string;
  record_count: number;
  data_size_mb: number;
  created_at: string;
  updated_at: string;
}

export interface ProcessingJob {
  job_id: string;
  job_name: string;
  source_id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  records_processed: number;
  total_records: number;
  started_at: string;
  completed_at?: string;
  error_message?: string;
}

export interface SystemMetrics {
  cpu_usage: number;
  memory_usage: number;
  disk_usage: number;
  network_in_mbps: number;
  network_out_mbps: number;
  active_connections: number;
  queue_size: number;
  processing_rate: number;
}

export interface DataQualityMetrics {
  completeness: number;
  accuracy: number;
  consistency: number;
  validity: number;
  uniqueness: number;
  timeliness: number;
  overall_score: number;
}

export interface DataFlow {
  nodes: Array<{
    id: string;
    label: string;
    type: 'source' | 'transform' | 'destination' | 'process';
    status: 'active' | 'inactive' | 'error';
    metrics?: {
      throughput: number;
      latency: number;
      error_rate: number;
    };
  }>;
  edges: Array<{
    source: string;
    target: string;
    animated: boolean;
    label?: string;
  }>;
}

export interface ActivityMetrics {
  hour: number;
  day: number;
  value: number;
  type: 'upload' | 'process' | 'query' | 'export';
}

export interface LogEntry {
  id: string;
  timestamp: string;
  level: 'info' | 'warning' | 'error' | 'debug';
  message: string;
  source: string;
  details?: any;
}

export interface SqlQueryResult {
  columns: Array<{
    name: string;
    type: string;
  }>;
  rows: any[];
  rowCount: number;
  executionTime: number;
  error?: string;
}

export interface GridData {
  columns: Array<{
    field: string;
    headerName: string;
    type: 'string' | 'number' | 'date' | 'boolean';
    sortable: boolean;
    filterable: boolean;
  }>;
  rows: any[];
  totalRows: number;
  pageSize: number;
  currentPage: number;
}

class DataCenterTauriAPI {
  private logListeners: Map<string, any> = new Map();

  // Data Sources
  async getDataSources(): Promise<DataSource[]> {
    return await invoke('get_data_sources');
  }

  async createDataSource(source: Partial<DataSource>): Promise<DataSource> {
    return await invoke('create_data_source', { source });
  }

  async deleteDataSource(sourceId: string): Promise<void> {
    await invoke('delete_data_source', { sourceId });
  }

  async testDataSourceConnection(sourceId: string): Promise<boolean> {
    return await invoke('test_data_source_connection', { sourceId });
  }

  async syncDataSource(sourceId: string): Promise<ProcessingJob> {
    return await invoke('sync_data_source', { sourceId });
  }

  // Processing Jobs
  async getProcessingJobs(): Promise<ProcessingJob[]> {
    return await invoke('get_processing_jobs');
  }

  async createProcessingJob(job: Partial<ProcessingJob>): Promise<ProcessingJob> {
    return await invoke('create_processing_job', { job });
  }

  async cancelProcessingJob(jobId: string): Promise<void> {
    await invoke('cancel_processing_job', { jobId });
  }

  async getJobLogs(jobId: string): Promise<LogEntry[]> {
    return await invoke('get_job_logs', { jobId });
  }

  // System Metrics
  async getSystemMetrics(): Promise<SystemMetrics> {
    return await invoke('get_system_metrics');
  }

  // Data Quality
  async getDataQualityMetrics(sourceId?: string): Promise<DataQualityMetrics> {
    return await invoke('get_data_quality_metrics', { sourceId });
  }

  async runQualityChecks(sourceId: string, rules?: any[]): Promise<DataQualityMetrics> {
    return await invoke('run_quality_checks', { sourceId, rules });
  }

  // Data Flow
  async getDataFlow(): Promise<DataFlow> {
    return await invoke('get_data_flow');
  }

  async updateDataFlow(flow: DataFlow): Promise<DataFlow> {
    return await invoke('update_data_flow', { flow });
  }

  async executeDataFlow(flowId: string): Promise<ProcessingJob> {
    return await invoke('execute_data_flow', { flowId });
  }

  // Activity Metrics
  async getActivityMetrics(days: number = 7): Promise<ActivityMetrics[]> {
    return await invoke('get_activity_metrics', { days });
  }

  // Logs
  async getLogs(limit: number = 100, level?: string): Promise<LogEntry[]> {
    return await invoke('get_logs', { limit, level });
  }

  // Real-time log streaming using Tauri events
  subscribeToLogs(onMessage: (log: LogEntry) => void): () => void {
    const listenerId = `log_listener_${Date.now()}`;
    
    const setupListener = async () => {
      const unlisten = await listen<LogEntry>('data_center_log', (event) => {
        onMessage(event.payload);
      });
      this.logListeners.set(listenerId, unlisten);
    };
    
    setupListener();
    
    // Return unsubscribe function
    return () => {
      const unlisten = this.logListeners.get(listenerId);
      if (unlisten) {
        unlisten();
        this.logListeners.delete(listenerId);
      }
    };
  }

  // SQL Query
  async executeQuery(query: string, dataSourceId?: string): Promise<SqlQueryResult> {
    return await invoke('execute_sql_query', { query, dataSourceId });
  }

  async getSavedQueries(): Promise<Array<{ id: string; name: string; query: string; created_at: string }>> {
    return await invoke('get_saved_queries');
  }

  async saveQuery(name: string, query: string): Promise<void> {
    await invoke('save_query', { name, query });
  }

  // Grid Data
  async getGridData(
    sourceId: string, 
    page: number = 1, 
    pageSize: number = 50,
    sortBy?: string,
    filterBy?: Record<string, any>
  ): Promise<GridData> {
    return await invoke('get_grid_data', {
      sourceId,
      page,
      pageSize,
      sortBy,
      filterBy
    });
  }

  // File Processing (using Rust for high performance)
  async uploadFile(file: File, processType: string): Promise<{ jobId: string; message: string }> {
    // Convert File to base64 or use Tauri's file dialog
    const arrayBuffer = await file.arrayBuffer();
    const uint8Array = new Uint8Array(arrayBuffer);
    
    return await invoke('process_file_upload', {
      fileName: file.name,
      fileData: Array.from(uint8Array),
      fileType: file.type,
      processType
    });
  }

  // Python Processing
  async executePythonCode(code: string, inputs?: any): Promise<{ output: any; error?: string; execution_time: number }> {
    return await invoke('execute_python', {
      code,
      context: inputs || {}
    });
  }

  async trainModel(dataset: string, modelType: string, parameters?: any): Promise<any> {
    return await invoke('train_model', {
      dataset,
      modelType,
      parameters: parameters || {}
    });
  }

  async runInference(modelId: string, inputData: any): Promise<any> {
    return await invoke('run_inference', {
      modelId,
      inputData
    });
  }

  async getPythonTemplates(): Promise<Array<{ id: string; name: string; description: string; code: string }>> {
    return await invoke('get_python_templates');
  }

  // Data Processing Operations
  async processData(data: any, operation: string, options?: any): Promise<any> {
    return await invoke('process_data', {
      data,
      operation,
      options: options || {}
    });
  }

  async analyzeData(data: any, analysisType: 'statistical' | 'quality' | 'profiling'): Promise<any> {
    return await invoke('analyze_data', {
      data,
      analysisType
    });
  }

  // High-Performance Operations (Rust)
  async processCSV(filePath: string, options?: any): Promise<any> {
    return await invoke('process_csv_file', {
      path: filePath,
      options: options || {}
    });
  }

  async aggregateData(data: any, groupBy: string[], aggregations: any[]): Promise<any> {
    return await invoke('aggregate_data', {
      data,
      groupBy,
      aggregations
    });
  }

  async joinDatasets(left: any, right: any, joinType: string, on: string[]): Promise<any> {
    return await invoke('join_datasets', {
      left,
      right,
      joinType,
      on
    });
  }

  // Machine Learning Operations (Python)
  async detectAnomalies(data: any, method: string = 'isolation_forest'): Promise<any> {
    return await invoke('detect_anomalies', {
      data,
      method
    });
  }

  async forecastTimeSeries(data: any, dateCol: string, valueCol: string, periods: number): Promise<any> {
    return await invoke('forecast_timeseries', {
      data,
      dateCol,
      valueCol,
      periods
    });
  }

  async engineerFeatures(data: any, featureConfigs: any[]): Promise<any> {
    return await invoke('engineer_features', {
      data,
      featureConfigs
    });
  }

  async profileData(data: any): Promise<any> {
    return await invoke('profile_data', { data });
  }

  // Export Operations
  async exportData(sourceId: string, format: 'csv' | 'excel' | 'json' | 'parquet', options?: any): Promise<Uint8Array> {
    const result = await invoke<number[]>('export_data', {
      sourceId,
      format,
      options: options || {}
    });
    return new Uint8Array(result);
  }

  // Health & Status
  async getDataCenterHealth(): Promise<{
    rust_engine: 'healthy' | 'degraded' | 'error';
    python_engine: 'healthy' | 'degraded' | 'error';
    database: 'healthy' | 'degraded' | 'error';
    cache: 'healthy' | 'degraded' | 'error';
  }> {
    return await invoke('get_data_center_health');
  }
}

export const dataCenterAPI = new DataCenterTauriAPI();