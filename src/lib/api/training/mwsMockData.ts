/**
 * Mock data for MWS Training API
 * This file contains all mock data structures used by the training module
 */

import { 
  TrainingProgram,
  TrainingSchedule,
  Certification,
  BudgetAllocation,
  ProgressData,
  FeedbackResponse,
  Achievement,
  LearningPathway,
  SkillGap,
  TrainingNeed
} from '@/types/training';

// Training Programs Mock Data
export const mockTrainingPrograms: TrainingProgram[] = [
  {
    id: 'prog-001',
    name: 'Safety Fundamentals',
    description: 'Essential safety training for all employees',
    category: 'safety',
    duration: 8,
    format: 'online',
    level: 'beginner',
    objectives: ['Understand safety protocols', 'Identify workplace hazards', 'Emergency procedures'],
    prerequisites: [],
    targetAudience: 'All employees',
    createdBy: '<EMAIL>',
    createdAt: '2025-01-15T09:00:00Z',
    updatedAt: '2025-01-20T10:30:00Z',
    status: 'active',
    enrollmentCount: 245,
    completionRate: 0.78,
    rating: 4.5
  },
  {
    id: 'prog-002',
    name: 'Leadership Excellence',
    description: 'Advanced leadership development program',
    category: 'leadership',
    duration: 24,
    format: 'blended',
    level: 'advanced',
    objectives: ['Strategic thinking', 'Team management', 'Decision making'],
    prerequisites: ['prog-003'],
    targetAudience: 'Senior managers',
    createdBy: '<EMAIL>',
    createdAt: '2025-01-10T09:00:00Z',
    updatedAt: '2025-01-22T14:00:00Z',
    status: 'active',
    enrollmentCount: 48,
    completionRate: 0.65,
    rating: 4.8
  },
  {
    id: 'prog-003',
    name: 'Project Management Essentials',
    description: 'Comprehensive project management training',
    category: 'technical',
    duration: 16,
    format: 'online',
    level: 'intermediate',
    objectives: ['Project planning', 'Risk management', 'Stakeholder communication'],
    prerequisites: [],
    targetAudience: 'Project coordinators',
    createdBy: '<EMAIL>',
    createdAt: '2024-12-01T09:00:00Z',
    updatedAt: '2025-01-18T11:00:00Z',
    status: 'active',
    enrollmentCount: 156,
    completionRate: 0.72,
    rating: 4.6
  }
];

// Training Schedules Mock Data
export const mockSchedules: TrainingSchedule[] = [
  {
    id: 'sched-001',
    programId: 'prog-001',
    title: 'Safety Fundamentals - Q1 Session',
    startDate: '2025-02-01T09:00:00Z',
    endDate: '2025-02-01T17:00:00Z',
    location: 'Online',
    instructor: 'John Smith',
    maxParticipants: 30,
    enrolledParticipants: 25,
    status: 'scheduled',
    resources: ['Safety Manual', 'PPE Guidelines'],
    notifications: ['1 week before', '1 day before']
  },
  {
    id: 'sched-002',
    programId: 'prog-002',
    title: 'Leadership Workshop - In Person',
    startDate: '2025-02-15T09:00:00Z',
    endDate: '2025-02-16T17:00:00Z',
    location: 'Conference Room A',
    instructor: 'Dr. Sarah Johnson',
    maxParticipants: 20,
    enrolledParticipants: 18,
    status: 'scheduled',
    resources: ['Leadership Workbook', 'Case Studies'],
    notifications: ['2 weeks before', '3 days before']
  }
];

// Certifications Mock Data
export const mockCertifications: Certification[] = [
  {
    id: 'cert-001',
    name: 'Safety Specialist',
    issuingOrganization: 'National Safety Council',
    employeeId: 'emp-001',
    employeeName: 'John Doe',
    issueDate: '2024-06-15T00:00:00Z',
    expiryDate: '2026-06-15T00:00:00Z',
    status: 'active',
    credentialId: 'NSC-2024-12345',
    verificationUrl: 'https://verify.nsc.org/12345'
  },
  {
    id: 'cert-002',
    name: 'PMP Certification',
    issuingOrganization: 'Project Management Institute',
    employeeId: 'emp-002',
    employeeName: 'Jane Smith',
    issueDate: '2024-08-20T00:00:00Z',
    expiryDate: '2027-08-20T00:00:00Z',
    status: 'active',
    credentialId: 'PMP-2024-67890',
    verificationUrl: 'https://verify.pmi.org/67890'
  },
  {
    id: 'cert-003',
    name: 'AWS Solutions Architect',
    issuingOrganization: 'Amazon Web Services',
    employeeId: 'emp-003',
    employeeName: 'Mike Johnson',
    issueDate: '2024-03-10T00:00:00Z',
    expiryDate: '2025-03-10T00:00:00Z',
    status: 'expiring_soon',
    credentialId: 'AWS-SA-2024-11111',
    verificationUrl: 'https://verify.aws.com/11111'
  }
];

// Budget Allocations Mock Data
export const mockBudgetAllocations: BudgetAllocation[] = [
  {
    id: 'budget-001',
    department: 'IT',
    category: 'technical',
    allocatedAmount: 50000,
    spentAmount: 32000,
    remainingAmount: 18000,
    fiscalYear: 2025,
    quarter: 'Q1',
    approvedBy: 'CFO',
    approvalDate: '2024-12-15T00:00:00Z',
    status: 'active'
  },
  {
    id: 'budget-002',
    department: 'Operations',
    category: 'safety',
    allocatedAmount: 30000,
    spentAmount: 12000,
    remainingAmount: 18000,
    fiscalYear: 2025,
    quarter: 'Q1',
    approvedBy: 'CFO',
    approvalDate: '2024-12-15T00:00:00Z',
    status: 'active'
  },
  {
    id: 'budget-003',
    department: 'Management',
    category: 'leadership',
    allocatedAmount: 75000,
    spentAmount: 45000,
    remainingAmount: 30000,
    fiscalYear: 2025,
    quarter: 'Q1',
    approvedBy: 'CEO',
    approvalDate: '2024-12-10T00:00:00Z',
    status: 'active'
  }
];

// Progress Data Mock
export const mockProgressData: ProgressData[] = [
  {
    id: 'prog-data-001',
    employeeId: 'emp-001',
    employeeName: 'John Doe',
    programId: 'prog-001',
    programName: 'Safety Fundamentals',
    enrollmentDate: '2025-01-15T00:00:00Z',
    progress: 85,
    completedModules: 7,
    totalModules: 8,
    lastAccessDate: '2025-01-26T14:30:00Z',
    estimatedCompletion: '2025-02-01T00:00:00Z',
    status: 'in_progress',
    quizScores: [92, 88, 95, 90],
    timeSpent: 420 // minutes
  },
  {
    id: 'prog-data-002',
    employeeId: 'emp-002',
    employeeName: 'Jane Smith',
    programId: 'prog-002',
    programName: 'Leadership Excellence',
    enrollmentDate: '2025-01-10T00:00:00Z',
    progress: 100,
    completedModules: 12,
    totalModules: 12,
    lastAccessDate: '2025-01-25T16:00:00Z',
    completionDate: '2025-01-25T16:00:00Z',
    status: 'completed',
    quizScores: [96, 94, 98, 95, 97],
    timeSpent: 1440,
    certificateIssued: true
  }
];

// Feedback Responses Mock Data
export const mockFeedbackResponses: FeedbackResponse[] = [
  {
    id: 'feedback-001',
    programId: 'prog-001',
    employeeId: 'emp-001',
    employeeName: 'John Doe',
    rating: 5,
    comments: 'Excellent safety training program. Very comprehensive and practical.',
    suggestions: 'Could include more real-world scenarios',
    submittedDate: '2025-01-25T00:00:00Z',
    categories: ['content_quality', 'relevance'],
    wouldRecommend: true
  },
  {
    id: 'feedback-002',
    programId: 'prog-002',
    employeeId: 'emp-002',
    employeeName: 'Jane Smith',
    rating: 4,
    comments: 'Great leadership insights and practical tools.',
    suggestions: 'More group activities would be beneficial',
    submittedDate: '2025-01-24T00:00:00Z',
    categories: ['instructor_quality', 'materials'],
    wouldRecommend: true
  }
];

// Achievements Mock Data
export const mockAchievements: Achievement[] = [
  {
    id: 'achieve-001',
    name: 'Safety Champion',
    description: 'Completed all safety training modules',
    icon: '🏆',
    points: 100,
    category: 'safety',
    criteria: 'Complete 5 safety courses',
    unlockedBy: ['emp-001', 'emp-003'],
    dateCreated: '2024-01-01T00:00:00Z'
  },
  {
    id: 'achieve-002',
    name: 'Quick Learner',
    description: 'Complete a course in record time',
    icon: '⚡',
    points: 50,
    category: 'speed',
    criteria: 'Complete any course 25% faster than average',
    unlockedBy: ['emp-002'],
    dateCreated: '2024-01-01T00:00:00Z'
  },
  {
    id: 'achieve-003',
    name: 'Perfect Score',
    description: 'Score 100% on all assessments in a course',
    icon: '💯',
    points: 75,
    category: 'performance',
    criteria: 'Score 100% on all quizzes in any course',
    unlockedBy: ['emp-002', 'emp-004'],
    dateCreated: '2024-01-01T00:00:00Z'
  }
];

// Learning Pathways Mock Data
export const mockLearningPathways: LearningPathway[] = [
  {
    id: 'path-001',
    name: 'Project Manager Career Path',
    description: 'Complete pathway to become a certified project manager',
    targetRole: 'Senior Project Manager',
    estimatedDuration: '6 months',
    programs: ['prog-003', 'prog-004', 'prog-005'],
    milestones: [
      { name: 'PM Fundamentals', requiredPrograms: ['prog-003'], completed: true },
      { name: 'Advanced PM', requiredPrograms: ['prog-004'], completed: false },
      { name: 'PMP Certification', requiredPrograms: ['prog-005'], completed: false }
    ],
    enrolledEmployees: 45,
    completionRate: 0.38,
    averageRating: 4.6
  },
  {
    id: 'path-002',
    name: 'Leadership Development',
    description: 'Progressive leadership training from team lead to executive',
    targetRole: 'Executive Leadership',
    estimatedDuration: '12 months',
    programs: ['prog-002', 'prog-006', 'prog-007', 'prog-008'],
    milestones: [
      { name: 'Team Leadership', requiredPrograms: ['prog-006'], completed: true },
      { name: 'Department Head', requiredPrograms: ['prog-002', 'prog-007'], completed: false },
      { name: 'Executive Ready', requiredPrograms: ['prog-008'], completed: false }
    ],
    enrolledEmployees: 28,
    completionRate: 0.25,
    averageRating: 4.8
  }
];

// Skills Gap Analysis Mock Data
export const mockSkillGaps: SkillGap[] = [
  {
    id: 'gap-001',
    department: 'IT',
    skill: 'Cloud Architecture',
    currentLevel: 2.5,
    requiredLevel: 4.0,
    gap: 1.5,
    priority: 'high',
    affectedEmployees: 15,
    recommendedPrograms: ['prog-009', 'prog-010'],
    estimatedTimeToClose: '3 months',
    businessImpact: 'Critical for digital transformation initiatives'
  },
  {
    id: 'gap-002',
    department: 'Operations',
    skill: 'Lean Six Sigma',
    currentLevel: 1.8,
    requiredLevel: 3.5,
    gap: 1.7,
    priority: 'medium',
    affectedEmployees: 25,
    recommendedPrograms: ['prog-011'],
    estimatedTimeToClose: '4 months',
    businessImpact: 'Required for process optimization goals'
  },
  {
    id: 'gap-003',
    department: 'Sales',
    skill: 'Digital Marketing',
    currentLevel: 2.0,
    requiredLevel: 3.8,
    gap: 1.8,
    priority: 'high',
    affectedEmployees: 20,
    recommendedPrograms: ['prog-012', 'prog-013'],
    estimatedTimeToClose: '2 months',
    businessImpact: 'Essential for online sales targets'
  }
];

// Training Needs Mock Data
export const mockTrainingNeeds: TrainingNeed[] = [
  {
    id: 'need-001',
    employeeId: 'emp-001',
    employeeName: 'John Doe',
    department: 'Operations',
    role: 'Operations Manager',
    identifiedSkills: ['Safety Management', 'Risk Assessment'],
    requiredPrograms: ['prog-001'],
    priority: 'high',
    deadline: '2025-03-01T00:00:00Z',
    status: 'in_progress',
    managerApproval: true,
    hrApproval: true
  },
  {
    id: 'need-002',
    employeeId: 'emp-002',
    employeeName: 'Jane Smith',
    department: 'IT',
    role: 'Senior Developer',
    identifiedSkills: ['Cloud Architecture', 'DevOps'],
    requiredPrograms: ['prog-009', 'prog-010'],
    priority: 'medium',
    deadline: '2025-04-01T00:00:00Z',
    status: 'pending',
    managerApproval: true,
    hrApproval: false
  }
];

// LMS Course Catalog Mock Data
export const mockLMSCourses = [
  {
    id: 'lms-001',
    title: 'Introduction to Machine Learning',
    provider: 'Coursera',
    duration: '6 weeks',
    format: 'online',
    price: 49.99,
    rating: 4.7,
    enrollments: 1250,
    difficulty: 'intermediate',
    topics: ['Python', 'TensorFlow', 'Neural Networks'],
    certificate: true
  },
  {
    id: 'lms-002',
    title: 'Advanced Excel for Business',
    provider: 'LinkedIn Learning',
    duration: '4 hours',
    format: 'online',
    price: 29.99,
    rating: 4.5,
    enrollments: 850,
    difficulty: 'beginner',
    topics: ['Pivot Tables', 'Macros', 'Data Analysis'],
    certificate: true
  }
];

// Gamification Leaderboard Mock Data
export const mockLeaderboard = [
  {
    rank: 1,
    employeeId: 'emp-002',
    employeeName: 'Jane Smith',
    department: 'IT',
    points: 1250,
    badges: 12,
    coursesCompleted: 15,
    streak: 45,
    level: 'Expert'
  },
  {
    rank: 2,
    employeeId: 'emp-004',
    employeeName: 'Alice Johnson',
    department: 'HR',
    points: 1180,
    badges: 10,
    coursesCompleted: 13,
    streak: 30,
    level: 'Advanced'
  },
  {
    rank: 3,
    employeeId: 'emp-001',
    employeeName: 'John Doe',
    department: 'Operations',
    points: 950,
    badges: 8,
    coursesCompleted: 10,
    streak: 15,
    level: 'Intermediate'
  }
];

// Report Templates Mock Data
export const mockReportTemplates = [
  {
    id: 'template-001',
    name: 'Monthly Training Summary',
    description: 'Overview of training activities for the month',
    metrics: ['enrollments', 'completions', 'avgRating', 'budgetSpent'],
    schedule: 'monthly',
    recipients: ['<EMAIL>', '<EMAIL>']
  },
  {
    id: 'template-002',
    name: 'Certification Status Report',
    description: 'Track certification renewals and expirations',
    metrics: ['activeСerts', 'expiringCerts', 'renewalCosts'],
    schedule: 'quarterly',
    recipients: ['<EMAIL>']
  }
];

// Dashboard Metrics Mock Data
export const mockDashboardMetrics = {
  overview: {
    activePrograms: 24,
    activeTrainees: 156,
    completedThisMonth: 45,
    upcomingCertifications: 8,
    totalBudget: 250000,
    budgetUtilized: 0.64,
    averageSatisfaction: 4.6,
    completionRate: 0.78
  },
  trends: {
    enrollments: [
      { month: 'Jan', value: 120 },
      { month: 'Feb', value: 145 },
      { month: 'Mar', value: 138 },
      { month: 'Apr', value: 165 },
      { month: 'May', value: 180 },
      { month: 'Jun', value: 195 }
    ],
    completions: [
      { month: 'Jan', value: 85 },
      { month: 'Feb', value: 92 },
      { month: 'Mar', value: 88 },
      { month: 'Apr', value: 110 },
      { month: 'May', value: 125 },
      { month: 'Jun', value: 140 }
    ]
  },
  departmentStats: [
    { department: 'IT', enrolled: 45, completed: 38, avgScore: 88 },
    { department: 'Operations', enrolled: 62, completed: 48, avgScore: 85 },
    { department: 'Sales', enrolled: 38, completed: 30, avgScore: 82 },
    { department: 'HR', enrolled: 28, completed: 25, avgScore: 90 }
  ]
};

// Export all mock data collections
export const mwsMockData = {
  trainingPrograms: mockTrainingPrograms,
  schedules: mockSchedules,
  certifications: mockCertifications,
  budgetAllocations: mockBudgetAllocations,
  progressData: mockProgressData,
  feedbackResponses: mockFeedbackResponses,
  achievements: mockAchievements,
  learningPathways: mockLearningPathways,
  skillGaps: mockSkillGaps,
  trainingNeeds: mockTrainingNeeds,
  lmsCourses: mockLMSCourses,
  leaderboard: mockLeaderboard,
  reportTemplates: mockReportTemplates,
  dashboardMetrics: mockDashboardMetrics
};