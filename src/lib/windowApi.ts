import * as budgetApi from '@/lib/services/budgetApi';

// Initialize window.budget API
if (typeof window !== 'undefined') {
  window.budget = {
    // Analytics
    getAnalytics: budgetApi.getBudgetAnalytics,
    getForecast: budgetApi.getForecast,
    
    // Bulk operations
    bulkImportExpenses: budgetApi.bulkImportExpenses,
    
    // Audit
    getAuditTrail: budgetApi.getAuditTrail,
    
    // Existing methods
    getQuarterlyAllocations: budgetApi.getQuarterlyAllocations,
    getDepartmentAllocations: budgetApi.getDepartmentAllocations,
    getCategoryLimits: budgetApi.getCategoryLimits,
    getAllocationRules: budgetApi.getAllocationRules,
  };
}

export {};