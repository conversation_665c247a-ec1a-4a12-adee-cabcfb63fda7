// Theme initializer script to prevent FOUC (Flash of Unstyled Content)
// This runs before React to ensure proper theme is applied immediately

(function() {
  const THEME_STORAGE_KEY = 'theme_preference';
  const CUSTOM_COLORS_STORAGE_KEY = 'theme_custom_colors';
  
  try {
    // Get saved theme from localStorage
    const savedTheme = localStorage.getItem(THEME_STORAGE_KEY);
    const root = document.documentElement;
    
    // Determine which theme to apply
    let themeToApply = 'dark'; // default theme
    
    if (savedTheme && ['dark', 'gray', 'light', 'custom'].includes(savedTheme)) {
      themeToApply = savedTheme;
    }
    
    // Remove any existing theme classes
    root.classList.remove('dark', 'light', 'gray', 'custom');
    root.classList.remove('theme-dark', 'theme-gray', 'theme-light', 'theme-custom');
    
    // Apply the theme
    root.classList.add(themeToApply);
    root.classList.add(`theme-${themeToApply}`);
    root.setAttribute('data-theme', themeToApply);
    
    // Set color-scheme for system UI
    root.style.colorScheme = themeToApply === 'light' ? 'light' : 'dark';
    
    // If custom theme, load and apply custom colors
    if (themeToApply === 'custom') {
      const savedColors = localStorage.getItem(CUSTOM_COLORS_STORAGE_KEY);
      if (savedColors) {
        try {
          const colors = JSON.parse(savedColors);
          Object.entries(colors).forEach(([key, value]) => {
            const cssVarName = `--color-${key.replace(/([A-Z])/g, '-$1').toLowerCase()}`;
            root.style.setProperty(cssVarName, value);
          });
        } catch (e) {
        }
      }
    }
  } catch (error) {
    // Fallback to default dark theme
    document.documentElement.classList.add('dark', 'theme-dark');
    document.documentElement.setAttribute('data-theme', 'dark');
  }
})();