// Advanced Search Service with faceted filtering, date ranges, and AI-powered semantic search

export interface SearchFilter {
  id: string;
  name: string;
  type: 'select' | 'multiselect' | 'daterange' | 'slider' | 'toggle';
  options?: SearchFilterOption[];
  value?: any;
  min?: number;
  max?: number;
}

export interface SearchFilterOption {
  value: string;
  label: string;
  count?: number;
  color?: string;
}

export interface DateRange {
  start: Date | null;
  end: Date | null;
}

export interface SearchQuery {
  text: string;
  filters: Record<string, any>;
  sortBy: 'relevance' | 'date' | 'title' | 'type' | 'tags';
  sortOrder: 'asc' | 'desc';
  page: number;
  limit: number;
}

export interface SearchResult {
  id: string;
  title: string;
  content: string;
  type: 'document' | 'note' | 'chat' | 'capture' | 'memory' | 'link' | 'file' | 'template' | 'resource';
  tags: string[];
  mentions: string[];
  createdAt: Date;
  updatedAt: Date;
  relevanceScore: number;
  highlights: string[];
  metadata: Record<string, any>;
}

export interface SearchResponse {
  results: SearchResult[];
  total: number;
  page: number;
  totalPages: number;
  facets: Record<string, SearchFilterOption[]>;
  suggestions: string[];
  searchTime: number;
}

export interface SearchAnalytics {
  totalSearches: number;
  popularQueries: Array<{ query: string; count: number; lastUsed: Date }>;
  searchPatterns: Array<{ pattern: string; frequency: number }>;
  averageSearchTime: number;
  mostAccessedContent: Array<{ id: string; title: string; accessCount: number }>;
  searchTrends: Array<{ date: string; searchCount: number }>;
  userBehavior: {
    averageResultsClicked: number;
    commonFilters: Record<string, number>;
    searchDepth: number;
  };
}

export interface ExportOptions {
  format: 'json' | 'csv' | 'pdf' | 'markdown' | 'html';
  includeMetadata: boolean;
  includeHighlights: boolean;
  maxResults?: number;
  fields?: string[];
}

export interface ExportResult {
  data: string | Blob;
  filename: string;
  mimeType: string;
}

export interface SemanticSearchOptions {
  useEmbeddings: boolean;
  similarityThreshold: number;
  maxResults: number;
  includeContext: boolean;
}

export interface SearchIndex {
  id: string;
  content: string;
  title: string;
  type: string;
  tags: string[];
  mentions: string[];
  createdAt: Date;
  updatedAt: Date;
  metadata: Record<string, any>;
  embedding?: number[];
}

export class AdvancedSearchService {
  private searchIndex: Map<string, SearchIndex> = new Map();
  private invertedIndex: Map<string, Set<string>> = new Map();
  private tagIndex: Map<string, Set<string>> = new Map();
  private typeIndex: Map<string, Set<string>> = new Map();
  private dateIndex: Map<string, Set<string>> = new Map();
  private searchAnalytics!: SearchAnalytics;
  private searchHistory: Array<{ query: string; timestamp: Date; resultCount: number; searchTime: number }> = [];
  private contentAccessCounts: Map<string, number> = new Map();
  private stopWords = new Set([
    'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those'
  ]);

  constructor() {
    this.initializeSearchIndex();
    this.initializeAnalytics();
  }

  // Core search functionality
  async search(query: SearchQuery): Promise<SearchResponse> {
    const startTime = Date.now();
    
    let results: SearchResult[] = [];
    
    if (query.text.trim()) {
      // Text-based search
      results = await this.performTextSearch(query.text, query.filters);
    } else {
      // Filter-only search
      results = await this.performFilterSearch(query.filters);
    }
    
    // Apply additional filters
    results = this.applyFilters(results, query.filters);
    
    // Sort results
    results = this.sortResults(results, query.sortBy, query.sortOrder);
    
    // Calculate pagination
    const total = results.length;
    const totalPages = Math.ceil(total / query.limit);
    const startIndex = (query.page - 1) * query.limit;
    const paginatedResults = results.slice(startIndex, startIndex + query.limit);
    
    // Generate facets
    const facets = this.generateFacets(results, query.filters);
    
    // Generate suggestions
    const suggestions = await this.generateSuggestions(query.text);
    
    const searchTime = Date.now() - startTime;
    
    // Track search analytics
    this.trackSearch(query.text, total, searchTime);
    
    return {
      results: paginatedResults,
      total,
      page: query.page,
      totalPages,
      facets,
      suggestions,
      searchTime
    };
  }

  // Semantic search using embeddings
  async semanticSearch(query: string, options: SemanticSearchOptions): Promise<SearchResult[]> {
    if (!options.useEmbeddings) {
      return [];
    }
    
    // In a real implementation, this would use actual embeddings
    // For now, we'll simulate semantic search with enhanced text matching
    const queryTerms = this.tokenize(query.toLowerCase());
    const results: Array<{ result: SearchResult; score: number }> = [];
    
    for (const [id, indexItem] of this.searchIndex) {
      const contentTerms = this.tokenize(indexItem.content.toLowerCase());
      const titleTerms = this.tokenize(indexItem.title.toLowerCase());
      
      // Calculate semantic similarity (simplified)
      let score = 0;
      
      // Exact phrase matching
      if (indexItem.content.toLowerCase().includes(query.toLowerCase())) {
        score += 10;
      }
      
      // Term frequency scoring
      queryTerms.forEach(term => {
        const contentFreq = contentTerms.filter(t => t === term).length;
        const titleFreq = titleTerms.filter(t => t === term).length;
        score += contentFreq * 1 + titleFreq * 3;
        
        // Fuzzy matching for similar terms
        contentTerms.forEach(contentTerm => {
          if (this.calculateSimilarity(term, contentTerm) > 0.8) {
            score += 0.5;
          }
        });
      });
      
      // Tag and mention matching
      indexItem.tags.forEach(tag => {
        if (queryTerms.some(term => tag.toLowerCase().includes(term))) {
          score += 2;
        }
      });
      
      indexItem.mentions.forEach(mention => {
        if (queryTerms.some(term => mention.toLowerCase().includes(term))) {
          score += 2;
        }
      });
      
      if (score >= options.similarityThreshold) {
        const result: SearchResult = {
          id: indexItem.id,
          title: indexItem.title,
          content: indexItem.content,
          type: indexItem.type as any,
          tags: indexItem.tags,
          mentions: indexItem.mentions,
          createdAt: indexItem.createdAt,
          updatedAt: indexItem.updatedAt,
          relevanceScore: score,
          highlights: this.generateHighlights(indexItem.content, queryTerms),
          metadata: indexItem.metadata
        };
        
        results.push({ result, score });
      }
    }
    
    return results
      .sort((a, b) => b.score - a.score)
      .slice(0, options.maxResults)
      .map(item => item.result);
  }

  // Add or update item in search index
  async indexItem(item: SearchIndex): Promise<void> {
    this.searchIndex.set(item.id, item);
    
    // Update inverted index
    const terms = this.tokenize(item.content.toLowerCase() + ' ' + item.title.toLowerCase());
    terms.forEach(term => {
      if (!this.stopWords.has(term)) {
        if (!this.invertedIndex.has(term)) {
          this.invertedIndex.set(term, new Set());
        }
        this.invertedIndex.get(term)!.add(item.id);
      }
    });
    
    // Update tag index
    item.tags.forEach(tag => {
      if (!this.tagIndex.has(tag)) {
        this.tagIndex.set(tag, new Set());
      }
      this.tagIndex.get(tag)!.add(item.id);
    });
    
    // Update type index
    if (!this.typeIndex.has(item.type)) {
      this.typeIndex.set(item.type, new Set());
    }
    this.typeIndex.get(item.type)!.add(item.id);
    
    // Update date index
    const dateKey = this.getDateKey(item.createdAt);
    if (!this.dateIndex.has(dateKey)) {
      this.dateIndex.set(dateKey, new Set());
    }
    this.dateIndex.get(dateKey)!.add(item.id);
  }

  // Remove item from search index
  async removeItem(id: string): Promise<void> {
    const item = this.searchIndex.get(id);
    if (!item) return;
    
    this.searchIndex.delete(id);
    
    // Clean up inverted index
    for (const [term, ids] of this.invertedIndex) {
      ids.delete(id);
      if (ids.size === 0) {
        this.invertedIndex.delete(term);
      }
    }
    
    // Clean up tag index
    for (const [tag, ids] of this.tagIndex) {
      ids.delete(id);
      if (ids.size === 0) {
        this.tagIndex.delete(tag);
      }
    }
    
    // Clean up type index
    for (const [type, ids] of this.typeIndex) {
      ids.delete(id);
      if (ids.size === 0) {
        this.typeIndex.delete(type);
      }
    }
    
    // Clean up date index
    for (const [dateKey, ids] of this.dateIndex) {
      ids.delete(id);
      if (ids.size === 0) {
        this.dateIndex.delete(dateKey);
      }
    }
  }

  // Get available search filters
  getAvailableFilters(): SearchFilter[] {
    const filters: SearchFilter[] = [
      {
        id: 'type',
        name: 'Content Type',
        type: 'multiselect',
        options: Array.from(this.typeIndex.keys()).map(type => ({
          value: type,
          label: this.capitalizeFirst(type),
          count: this.typeIndex.get(type)?.size || 0
        }))
      },
      {
        id: 'tags',
        name: 'Tags',
        type: 'multiselect',
        options: Array.from(this.tagIndex.keys())
          .sort((a, b) => (this.tagIndex.get(b)?.size || 0) - (this.tagIndex.get(a)?.size || 0))
          .slice(0, 20)
          .map(tag => ({
            value: tag,
            label: tag,
            count: this.tagIndex.get(tag)?.size || 0
          }))
      },
      {
        id: 'dateRange',
        name: 'Date Range',
        type: 'daterange'
      },
      {
        id: 'relevanceScore',
        name: 'Relevance Score',
        type: 'slider',
        min: 0,
        max: 100
      },
      {
        id: 'hasAttachments',
        name: 'Has Attachments',
        type: 'toggle'
      },
      {
        id: 'hasMentions',
        name: 'Has Mentions',
        type: 'toggle'
      }
    ];
    
    return filters;
  }

  // Get search suggestions
  async getSuggestions(query: string, limit = 5): Promise<string[]> {
    const suggestions: string[] = [];
    const queryLower = query.toLowerCase();
    
    // Term-based suggestions
    for (const term of this.invertedIndex.keys()) {
      if (term.startsWith(queryLower) && term !== queryLower) {
        suggestions.push(term);
      }
    }
    
    // Tag-based suggestions
    for (const tag of this.tagIndex.keys()) {
      if (tag.toLowerCase().includes(queryLower)) {
        suggestions.push(`#${tag}`);
      }
    }
    
    return suggestions
      .sort((a, b) => a.length - b.length)
      .slice(0, limit);
  }

  // Get search analytics
  getSearchAnalytics(): SearchAnalytics {
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    
    // Filter recent searches
    const recentSearches = this.searchHistory.filter(s => s.timestamp >= thirtyDaysAgo);
    
    // Calculate popular queries
    const queryCount = new Map<string, { count: number; lastUsed: Date }>();
    recentSearches.forEach(search => {
      const existing = queryCount.get(search.query) || { count: 0, lastUsed: search.timestamp };
      queryCount.set(search.query, {
        count: existing.count + 1,
        lastUsed: search.timestamp > existing.lastUsed ? search.timestamp : existing.lastUsed
      });
    });
    
    const popularQueries = Array.from(queryCount.entries())
      .map(([query, data]) => ({ query, count: data.count, lastUsed: data.lastUsed }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);
    
    // Calculate search patterns
    const patterns = new Map<string, number>();
    recentSearches.forEach(search => {
      const words = search.query.toLowerCase().split(/\s+/);
      words.forEach(word => {
        if (word.length > 2) {
          patterns.set(word, (patterns.get(word) || 0) + 1);
        }
      });
    });
    
    const searchPatterns = Array.from(patterns.entries())
      .map(([pattern, frequency]) => ({ pattern, frequency }))
      .sort((a, b) => b.frequency - a.frequency)
      .slice(0, 20);
    
    // Calculate average search time
    const averageSearchTime = recentSearches.length > 0
      ? recentSearches.reduce((sum, s) => sum + s.searchTime, 0) / recentSearches.length
      : 0;
    
    // Get most accessed content
    const mostAccessedContent = Array.from(this.contentAccessCounts.entries())
      .map(([id, count]) => {
        const item = this.searchIndex.get(id);
        return {
          id,
          title: item?.title || 'Unknown',
          accessCount: count
        };
      })
      .sort((a, b) => b.accessCount - a.accessCount)
      .slice(0, 10);
    
    // Calculate search trends (daily counts for last 30 days)
    const searchTrends: Array<{ date: string; searchCount: number }> = [];
    for (let i = 29; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
      const dateStr = date.toISOString().split('T')[0];
      const dayStart = new Date(date.getFullYear(), date.getMonth(), date.getDate());
      const dayEnd = new Date(dayStart.getTime() + 24 * 60 * 60 * 1000);
      
      const daySearches = this.searchHistory.filter(s => 
        s.timestamp >= dayStart && s.timestamp < dayEnd
      );
      
      searchTrends.push({
        date: dateStr,
        searchCount: daySearches.length
      });
    }
    
    return {
      totalSearches: this.searchHistory.length,
      popularQueries,
      searchPatterns,
      averageSearchTime,
      mostAccessedContent,
      searchTrends,
      userBehavior: {
        averageResultsClicked: 2.5, // Mock data - would track actual clicks
        commonFilters: {
          type: recentSearches.filter(s => s.query.includes('type:')).length,
          tag: recentSearches.filter(s => s.query.includes('#')).length,
          date: recentSearches.filter(s => s.query.includes('date:')).length
        },
        searchDepth: 1.8 // Mock data - would track pagination usage
      }
    };
  }

  // Enhanced analytics with detailed insights
  getDetailedAnalytics(): {
    totalItems: number;
    itemsByType: Record<string, number>;
    topTags: Array<{ tag: string; count: number }>;
    indexSize: number;
    searchAnalytics: SearchAnalytics;
  } {
    const itemsByType: Record<string, number> = {};
    for (const [type, ids] of this.typeIndex) {
      itemsByType[type] = ids.size;
    }
    
    const topTags = Array.from(this.tagIndex.entries())
      .map(([tag, ids]) => ({ tag, count: ids.size }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);
    
    return {
      totalItems: this.searchIndex.size,
      itemsByType,
      topTags,
      indexSize: this.invertedIndex.size,
      searchAnalytics: this.getSearchAnalytics()
    };
  }

  // Track content access for analytics
  trackContentAccess(contentId: string): void {
    const currentCount = this.contentAccessCounts.get(contentId) || 0;
    this.contentAccessCounts.set(contentId, currentCount + 1);
  }

  // Export search results in various formats
  async exportResults(results: SearchResult[], options: ExportOptions): Promise<ExportResult> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    
    switch (options.format) {
      case 'json':
        return this.exportAsJSON(results, options, timestamp);
      case 'csv':
        return this.exportAsCSV(results, options, timestamp);
      case 'markdown':
        return this.exportAsMarkdown(results, options, timestamp);
      case 'html':
        return this.exportAsHTML(results, options, timestamp);
      case 'pdf':
        return this.exportAsPDF(results, options, timestamp);
      default:
        throw new Error(`Unsupported export format: ${options.format}`);
    }
  }

  // Bulk export functionality
  async bulkExport(queries: string[], options: ExportOptions): Promise<ExportResult[]> {
    const results: ExportResult[] = [];
    
    for (const query of queries) {
      const searchQuery: SearchQuery = {
        text: query,
        filters: {},
        sortBy: 'relevance',
        sortOrder: 'desc',
        page: 1,
        limit: options.maxResults || 100
      };
      
      const searchResponse = await this.search(searchQuery);
      const exportResult = await this.exportResults(searchResponse.results, {
        ...options,
        format: options.format
      });
      
      results.push(exportResult);
    }
    
    return results;
  }

  // Advanced vector similarity search with real embeddings support
  async vectorSimilaritySearch(
    queryEmbedding: number[], 
    options: SemanticSearchOptions & { 
      weights?: { title: number; content: number; tags: number };
      boostRecent?: boolean;
    }
  ): Promise<SearchResult[]> {
    const results: Array<{ result: SearchResult; score: number }> = [];
    const weights = options.weights || { title: 0.3, content: 0.6, tags: 0.1 };
    
    for (const [id, indexItem] of this.searchIndex) {
      if (!indexItem.embedding) continue;
      
      // Calculate cosine similarity
      const similarity = this.cosineSimilarity(queryEmbedding, indexItem.embedding);
      
      if (similarity >= options.similarityThreshold) {
        let score = similarity;
        
        // Apply weights and boosts
        if (options.boostRecent) {
          const daysSinceCreated = (Date.now() - indexItem.createdAt.getTime()) / (1000 * 60 * 60 * 24);
          if (daysSinceCreated < 7) {
            score *= 1.2;
          } else if (daysSinceCreated < 30) {
            score *= 1.1;
          }
        }
        
        const result: SearchResult = {
          id: indexItem.id,
          title: indexItem.title,
          content: indexItem.content,
          type: indexItem.type as any,
          tags: indexItem.tags,
          mentions: indexItem.mentions,
          createdAt: indexItem.createdAt,
          updatedAt: indexItem.updatedAt,
          relevanceScore: score,
          highlights: [],
          metadata: indexItem.metadata
        };
        
        results.push({ result, score });
      }
    }
    
    return results
      .sort((a, b) => b.score - a.score)
      .slice(0, options.maxResults)
      .map(item => item.result);
  }

  // Generate embeddings for content (placeholder for real implementation)
  async generateEmbedding(text: string): Promise<number[]> {
    // In a real implementation, this would call an embedding API like OpenAI's text-embedding-ada-002
    // For now, we'll create a simple hash-based embedding
    const words = this.tokenize(text);
    const embedding = new Array(384).fill(0); // Common embedding dimension
    
    words.forEach((word, index) => {
      const hash = this.simpleHash(word);
      const position = Math.abs(hash) % embedding.length;
      embedding[position] += 1 / (index + 1); // Position-weighted
    });
    
    // Normalize the embedding
    const magnitude = Math.sqrt(embedding.reduce((sum, val) => sum + val * val, 0));
    return embedding.map(val => magnitude > 0 ? val / magnitude : 0);
  }

  // Update item with embedding
  async indexItemWithEmbedding(item: SearchIndex, embedding?: number[]): Promise<void> {
    if (!embedding) {
      embedding = await this.generateEmbedding(item.title + ' ' + item.content);
    }
    
    const itemWithEmbedding = { ...item, embedding };
    await this.indexItem(itemWithEmbedding);
  }

  // Private helper methods
  private async performTextSearch(query: string, filters: Record<string, any>): Promise<SearchResult[]> {
    const terms = this.tokenize(query.toLowerCase());
    const candidateIds = new Set<string>();
    
    // Find candidate documents using inverted index
    terms.forEach(term => {
      const ids = this.invertedIndex.get(term);
      if (ids) {
        ids.forEach(id => candidateIds.add(id));
      }
    });
    
    // Score and rank candidates
    const results: Array<{ result: SearchResult; score: number }> = [];
    
    for (const id of candidateIds) {
      const item = this.searchIndex.get(id);
      if (!item) continue;
      
      const score = this.calculateRelevanceScore(item, terms, query);
      
      const result: SearchResult = {
        id: item.id,
        title: item.title,
        content: item.content,
        type: item.type as any,
        tags: item.tags,
        mentions: item.mentions,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt,
        relevanceScore: score,
        highlights: this.generateHighlights(item.content, terms),
        metadata: item.metadata
      };
      
      results.push({ result, score });
    }
    
    return results
      .sort((a, b) => b.score - a.score)
      .map(item => item.result);
  }

  private async performFilterSearch(filters: Record<string, any>): Promise<SearchResult[]> {
    let candidateIds = new Set(this.searchIndex.keys());
    
    // Apply type filter
    if (filters.type && filters.type.length > 0) {
      const typeIds = new Set<string>();
      filters.type.forEach((type: string) => {
        const ids = this.typeIndex.get(type);
        if (ids) {
          ids.forEach(id => typeIds.add(id));
        }
      });
      candidateIds = new Set([...candidateIds].filter(id => typeIds.has(id)));
    }
    
    // Apply tag filter
    if (filters.tags && filters.tags.length > 0) {
      const tagIds = new Set<string>();
      filters.tags.forEach((tag: string) => {
        const ids = this.tagIndex.get(tag);
        if (ids) {
          ids.forEach(id => tagIds.add(id));
        }
      });
      candidateIds = new Set([...candidateIds].filter(id => tagIds.has(id)));
    }
    
    // Convert to results
    const results: SearchResult[] = [];
    for (const id of candidateIds) {
      const item = this.searchIndex.get(id);
      if (!item) continue;
      
      results.push({
        id: item.id,
        title: item.title,
        content: item.content,
        type: item.type as any,
        tags: item.tags,
        mentions: item.mentions,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt,
        relevanceScore: 1,
        highlights: [],
        metadata: item.metadata
      });
    }
    
    return results;
  }

  private applyFilters(results: SearchResult[], filters: Record<string, any>): SearchResult[] {
    let filtered = results;
    
    // Date range filter
    if (filters.dateRange && (filters.dateRange.start || filters.dateRange.end)) {
      filtered = filtered.filter(result => {
        const date = result.createdAt;
        if (filters.dateRange.start && date < filters.dateRange.start) return false;
        if (filters.dateRange.end && date > filters.dateRange.end) return false;
        return true;
      });
    }
    
    // Relevance score filter
    if (filters.relevanceScore && filters.relevanceScore > 0) {
      filtered = filtered.filter(result => result.relevanceScore >= filters.relevanceScore);
    }
    
    // Has attachments filter
    if (filters.hasAttachments !== undefined) {
      filtered = filtered.filter(result => {
        const hasAttachments = result.metadata.attachments && result.metadata.attachments.length > 0;
        return filters.hasAttachments ? hasAttachments : !hasAttachments;
      });
    }
    
    // Has mentions filter
    if (filters.hasMentions !== undefined) {
      filtered = filtered.filter(result => {
        const hasMentions = result.mentions.length > 0;
        return filters.hasMentions ? hasMentions : !hasMentions;
      });
    }
    
    return filtered;
  }

  private sortResults(results: SearchResult[], sortBy: string, sortOrder: string): SearchResult[] {
    return results.sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case 'relevance':
          comparison = b.relevanceScore - a.relevanceScore; // Higher score first (desc by default)
          break;
        case 'date':
          comparison = b.createdAt.getTime() - a.createdAt.getTime(); // Newer first (desc by default)
          break;
        case 'title':
          comparison = a.title.localeCompare(b.title); // A-Z (asc by default)
          break;
        case 'type':
          comparison = a.type.localeCompare(b.type); // A-Z (asc by default)
          break;
        case 'tags':
          comparison = b.tags.length - a.tags.length; // More tags first (desc by default)
          break;
        default:
          comparison = b.relevanceScore - a.relevanceScore;
      }
      
      // For relevance, date, and tags: desc is natural order, asc reverses it
      // For title and type: asc is natural order, desc reverses it
      if (sortBy === 'relevance' || sortBy === 'date' || sortBy === 'tags') {
        return sortOrder === 'asc' ? -comparison : comparison;
      } else {
        return sortOrder === 'desc' ? -comparison : comparison;
      }
    });
  }

  private generateFacets(results: SearchResult[], currentFilters: Record<string, any>): Record<string, SearchFilterOption[]> {
    const facets: Record<string, SearchFilterOption[]> = {};
    
    // Type facets
    const typeCounts = new Map<string, number>();
    results.forEach(result => {
      typeCounts.set(result.type, (typeCounts.get(result.type) || 0) + 1);
    });
    facets.type = Array.from(typeCounts.entries()).map(([type, count]) => ({
      value: type,
      label: this.capitalizeFirst(type),
      count
    }));
    
    // Tag facets
    const tagCounts = new Map<string, number>();
    results.forEach(result => {
      result.tags.forEach(tag => {
        tagCounts.set(tag, (tagCounts.get(tag) || 0) + 1);
      });
    });
    facets.tags = Array.from(tagCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([tag, count]) => ({
        value: tag,
        label: tag,
        count
      }));
    
    return facets;
  }

  private async generateSuggestions(query: string): Promise<string[]> {
    if (!query.trim()) return [];
    return this.getSuggestions(query, 5);
  }

  private calculateRelevanceScore(item: SearchIndex, terms: string[], originalQuery: string): number {
    let score = 0;
    const content = item.content.toLowerCase();
    const title = item.title.toLowerCase();
    
    // Exact phrase matching
    if (content.includes(originalQuery.toLowerCase())) {
      score += 20;
    }
    if (title.includes(originalQuery.toLowerCase())) {
      score += 30;
    }
    
    // Term frequency scoring
    terms.forEach(term => {
      const contentMatches = (content.match(new RegExp(term, 'g')) || []).length;
      const titleMatches = (title.match(new RegExp(term, 'g')) || []).length;
      score += contentMatches * 2 + titleMatches * 5;
    });
    
    // Tag matching
    item.tags.forEach(tag => {
      if (terms.some(term => tag.toLowerCase().includes(term))) {
        score += 10;
      }
    });
    
    // Mention matching
    item.mentions.forEach(mention => {
      if (terms.some(term => mention.toLowerCase().includes(term))) {
        score += 8;
      }
    });
    
    // Recency boost
    const daysSinceCreated = (Date.now() - item.createdAt.getTime()) / (1000 * 60 * 60 * 24);
    if (daysSinceCreated < 7) {
      score += 5;
    } else if (daysSinceCreated < 30) {
      score += 2;
    }
    
    return Math.round(score);
  }

  private generateHighlights(content: string, terms: string[]): string[] {
    const highlights: string[] = [];
    const sentences = content.split(/[.!?]+/);
    
    sentences.forEach(sentence => {
      const lowerSentence = sentence.toLowerCase();
      if (terms.some(term => lowerSentence.includes(term))) {
        let highlighted = sentence;
        terms.forEach(term => {
          const regex = new RegExp(`(${term})`, 'gi');
          highlighted = highlighted.replace(regex, '<mark>$1</mark>');
        });
        highlights.push(highlighted.trim());
      }
    });
    
    return highlights.slice(0, 3);
  }

  private tokenize(text: string): string[] {
    return text
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(token => token.length > 2 && !this.stopWords.has(token));
  }

  private calculateSimilarity(str1: string, str2: string): number {
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;
    
    if (longer.length === 0) return 1.0;
    
    const editDistance = this.levenshteinDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
  }

  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = [];
    
    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }
    
    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }
    
    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }
    
    return matrix[str2.length][str1.length];
  }

  private getDateKey(date: Date): string {
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
  }

  private capitalizeFirst(str: string): string {
    return str.charAt(0).toUpperCase() + str.slice(1);
  }

  private initializeSearchIndex(): void {
    // Initialize with some sample data
    const sampleItems: SearchIndex[] = [
      {
        id: '1',
        title: 'Getting Started with React',
        content: 'React is a JavaScript library for building user interfaces. It allows you to create reusable UI components.',
        type: 'document',
        tags: ['react', 'javascript', 'frontend'],
        mentions: ['@developer', '@team'],
        createdAt: new Date('2024-01-15'),
        updatedAt: new Date('2024-01-15'),
        metadata: { attachments: [] }
      },
      {
        id: '2',
        title: 'Meeting Notes - Project Planning',
        content: 'Discussed the upcoming project timeline and resource allocation. Need to finalize the technical architecture.',
        type: 'note',
        tags: ['meeting', 'planning', 'project'],
        mentions: ['@manager', '@architect'],
        createdAt: new Date('2024-01-10'),
        updatedAt: new Date('2024-01-10'),
        metadata: { attachments: ['agenda.pdf'] }
      },
      {
        id: '3',
        title: 'Chat with AI Assistant',
        content: 'Had a productive conversation about implementing search functionality with advanced filtering capabilities.',
        type: 'chat',
        tags: ['ai', 'search', 'development'],
        mentions: ['@assistant'],
        createdAt: new Date('2024-01-20'),
        updatedAt: new Date('2024-01-20'),
        metadata: { attachments: [] }
      }
    ];
    
    sampleItems.forEach(item => {
      this.indexItem(item);
    });
  }

  private initializeAnalytics(): void {
    this.searchAnalytics = {
      totalSearches: 0,
      popularQueries: [],
      searchPatterns: [],
      averageSearchTime: 0,
      mostAccessedContent: [],
      searchTrends: [],
      userBehavior: {
        averageResultsClicked: 0,
        commonFilters: {},
        searchDepth: 0
      }
    };
  }

  private trackSearch(query: string, resultCount: number, searchTime: number): void {
    this.searchHistory.push({
      query: query.trim(),
      timestamp: new Date(),
      resultCount,
      searchTime
    });
    
    // Keep only last 1000 searches to prevent memory issues
    if (this.searchHistory.length > 1000) {
      this.searchHistory = this.searchHistory.slice(-1000);
    }
  }

  private async exportAsJSON(results: SearchResult[], options: ExportOptions, timestamp: string): Promise<ExportResult> {
    const exportData = {
      exportedAt: new Date().toISOString(),
      totalResults: results.length,
      options,
      results: results.map(result => this.filterResultFields(result, options.fields))
    };
    
    return {
      data: JSON.stringify(exportData, null, 2),
      filename: `search-results-${timestamp}.json`,
      mimeType: 'application/json'
    };
  }

  private async exportAsCSV(results: SearchResult[], options: ExportOptions, timestamp: string): Promise<ExportResult> {
    const fields = options.fields || ['id', 'title', 'type', 'tags', 'createdAt', 'relevanceScore'];
    const headers = fields.join(',');
    
    const rows = results.map(result => {
      return fields.map(field => {
        let value = (result as any)[field];
        if (Array.isArray(value)) {
          value = value.join(';');
        } else if (value instanceof Date) {
          value = value.toISOString();
        } else if (typeof value === 'object') {
          value = JSON.stringify(value);
        }
        // Escape CSV values
        return `"${String(value || '').replace(/"/g, '""')}"`;
      }).join(',');
    });
    
    const csvContent = [headers, ...rows].join('\n');
    
    return {
      data: csvContent,
      filename: `search-results-${timestamp}.csv`,
      mimeType: 'text/csv'
    };
  }

  private async exportAsMarkdown(results: SearchResult[], options: ExportOptions, timestamp: string): Promise<ExportResult> {
    let markdown = `# Search Results Export\n\n`;
    markdown += `**Exported:** ${new Date().toLocaleString()}\n`;
    markdown += `**Total Results:** ${results.length}\n\n`;
    
    results.forEach((result, index) => {
      markdown += `## ${index + 1}. ${result.title}\n\n`;
      markdown += `**Type:** ${result.type}\n`;
      markdown += `**Created:** ${result.createdAt.toLocaleDateString()}\n`;
      markdown += `**Relevance Score:** ${result.relevanceScore}\n`;
      
      if (result.tags.length > 0) {
        markdown += `**Tags:** ${result.tags.map(tag => `\`${tag}\``).join(', ')}\n`;
      }
      
      if (options.includeHighlights && result.highlights.length > 0) {
        markdown += `**Highlights:**\n`;
        result.highlights.forEach(highlight => {
          markdown += `- ${highlight}\n`;
        });
      }
      
      markdown += `\n**Content:**\n${result.content.substring(0, 500)}${result.content.length > 500 ? '...' : ''}\n\n`;
      markdown += `---\n\n`;
    });
    
    return {
      data: markdown,
      filename: `search-results-${timestamp}.md`,
      mimeType: 'text/markdown'
    };
  }

  private async exportAsHTML(results: SearchResult[], options: ExportOptions, timestamp: string): Promise<ExportResult> {
    let html = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Search Results Export</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .result { border: 1px solid #ddd; margin: 20px 0; padding: 15px; border-radius: 5px; }
        .title { font-size: 1.2em; font-weight: bold; color: #333; }
        .meta { color: #666; font-size: 0.9em; margin: 5px 0; }
        .tags { margin: 10px 0; }
        .tag { background: #e1f5fe; padding: 2px 6px; border-radius: 3px; margin-right: 5px; font-size: 0.8em; }
        .content { margin-top: 10px; line-height: 1.5; }
        .highlights { background: #fff3e0; padding: 10px; margin: 10px 0; border-left: 3px solid #ff9800; }
    </style>
</head>
<body>
    <h1>Search Results Export</h1>
    <p><strong>Exported:</strong> ${new Date().toLocaleString()}</p>
    <p><strong>Total Results:</strong> ${results.length}</p>
`;
    
    results.forEach((result, index) => {
      html += `
    <div class="result">
        <div class="title">${this.escapeHtml(result.title)}</div>
        <div class="meta">
            Type: ${result.type} | 
            Created: ${result.createdAt.toLocaleDateString()} | 
            Relevance: ${result.relevanceScore}
        </div>
        <div class="tags">
            ${result.tags.map(tag => `<span class="tag">${this.escapeHtml(tag)}</span>`).join('')}
        </div>
        ${options.includeHighlights && result.highlights.length > 0 ? `
        <div class="highlights">
            <strong>Highlights:</strong><br>
            ${result.highlights.map(h => `• ${h}`).join('<br>')}
        </div>
        ` : ''}
        <div class="content">
            ${this.escapeHtml(result.content.substring(0, 500))}${result.content.length > 500 ? '...' : ''}
        </div>
    </div>`;
    });
    
    html += `
</body>
</html>`;
    
    return {
      data: html,
      filename: `search-results-${timestamp}.html`,
      mimeType: 'text/html'
    };
  }

  private async exportAsPDF(results: SearchResult[], options: ExportOptions, timestamp: string): Promise<ExportResult> {
    // For PDF export, we'll return HTML that can be converted to PDF by the client
    // In a real implementation, you might use a library like jsPDF or Puppeteer
    const htmlResult = await this.exportAsHTML(results, options, timestamp);
    
    return {
      data: htmlResult.data,
      filename: `search-results-${timestamp}.pdf`,
      mimeType: 'application/pdf'
    };
  }

  private filterResultFields(result: SearchResult, fields?: string[]): Partial<SearchResult> {
    if (!fields) return result;
    
    const filtered: any = {};
    fields.forEach(field => {
      if (field in result) {
        filtered[field] = (result as any)[field];
      }
    });
    
    return filtered;
  }

  private escapeHtml(text: string): string {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  private cosineSimilarity(a: number[], b: number[]): number {
    if (a.length !== b.length) return 0;
    
    let dotProduct = 0;
    let normA = 0;
    let normB = 0;
    
    for (let i = 0; i < a.length; i++) {
      dotProduct += a[i] * b[i];
      normA += a[i] * a[i];
      normB += b[i] * b[i];
    }
    
    const magnitude = Math.sqrt(normA) * Math.sqrt(normB);
    return magnitude > 0 ? dotProduct / magnitude : 0;
  }

  private simpleHash(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash;
  }
}

// Export singleton instance
export const advancedSearchService = new AdvancedSearchService();