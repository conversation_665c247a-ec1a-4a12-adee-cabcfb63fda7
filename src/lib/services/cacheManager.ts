interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

interface HealthMetrics {
  score: number;
  status: string;
  indicators: Record<string, any>;
}

interface ForecastData {
  month: string;
  predicted: number;
  confidence: number;
}

class BudgetCacheManager {
  private cache: Map<string, CacheEntry<any>> = new Map();
  private computedCache: Map<string, CacheEntry<any>> = new Map();

  // Set cache entry with TTL
  set<T>(key: string, data: T, ttl: number = 300000): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }

  // Get cache entry if not expired
  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }

    const now = Date.now();
    if (now - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }

    return entry.data as T;
  }

  // Invalidate cache entries matching pattern
  invalidate(pattern: string): void {
    const regex = new RegExp(pattern);
    const keysToDelete: string[] = [];

    this.cache.forEach((_, key) => {
      if (regex.test(key)) {
        keysToDelete.push(key);
      }
    });

    keysToDelete.forEach(key => this.cache.delete(key));
    
    // Also invalidate computed cache
    this.computedCache.forEach((_, key) => {
      if (regex.test(key)) {
        this.computedCache.delete(key);
      }
    });
  }

  // Clear all cache
  clear(): void {
    this.cache.clear();
    this.computedCache.clear();
  }

  // Get cache statistics
  getStats(): {
    size: number;
    computedSize: number;
    totalSize: number;
    keys: string[];
  } {
    return {
      size: this.cache.size,
      computedSize: this.computedCache.size,
      totalSize: this.cache.size + this.computedCache.size,
      keys: Array.from(this.cache.keys())
    };
  }

  // Computed values caching with memoization
  computed = {
    budgetHealth: async (year: number): Promise<HealthMetrics> => {
      const key = `budget_health_${year}`;
      const cached = this.getComputed<HealthMetrics>(key);
      
      if (cached) return cached;

      // Simulate computation (in real app, this would call the API)
      const metrics = await this.computeBudgetHealth(year);
      this.setComputed(key, metrics, 600000); // 10 minute TTL
      
      return metrics;
    },

    forecastData: async (year: number): Promise<ForecastData[]> => {
      const key = `forecast_${year}`;
      const cached = this.getComputed<ForecastData[]>(key);
      
      if (cached) return cached;

      const forecast = await this.computeForecast(year);
      this.setComputed(key, forecast, 900000); // 15 minute TTL
      
      return forecast;
    },

    complianceScore: async (year: number): Promise<number> => {
      const key = `compliance_${year}`;
      const cached = this.getComputed<number>(key);
      
      if (cached !== null) return cached;

      const score = await this.computeComplianceScore(year);
      this.setComputed(key, score, 1800000); // 30 minute TTL
      
      return score;
    }
  };

  // Helper methods for computed cache
  private setComputed<T>(key: string, data: T, ttl: number): void {
    this.computedCache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }

  private getComputed<T>(key: string): T | null {
    const entry = this.computedCache.get(key);
    
    if (!entry) {
      return null;
    }

    const now = Date.now();
    if (now - entry.timestamp > entry.ttl) {
      this.computedCache.delete(key);
      return null;
    }

    return entry.data as T;
  }

  // Simulation methods (replace with actual API calls)
  private async computeBudgetHealth(year: number): Promise<HealthMetrics> {
    // In real implementation, this would call the backend
    return {
      score: 85,
      status: 'good',
      indicators: {
        spending: { value: 75, status: 'healthy', trend: 'stable' },
        efficiency: { value: 88, status: 'healthy', trend: 'increasing' },
        risk: { value: 22, status: 'healthy', trend: 'decreasing' },
        compliance: { value: 95, status: 'healthy', trend: 'stable' }
      }
    };
  }

  private async computeForecast(year: number): Promise<ForecastData[]> {
    // In real implementation, this would call the backend
    return Array.from({ length: 12 }, (_, i) => ({
      month: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'][i],
      predicted: 50000 + (i * 2000) + Math.random() * 5000,
      confidence: 0.85 + Math.random() * 0.15
    }));
  }

  private async computeComplianceScore(year: number): Promise<number> {
    // In real implementation, this would call the backend
    return 92.5;
  }
}

// Create singleton instance
export const budgetCache = new BudgetCacheManager();

// Cache key generators for consistency
export const cacheKeys = {
  budget: (year: number) => `budget_${year}`,
  stats: (year: number) => `stats_${year}`,
  expenses: (year: number, month?: number) => 
    month ? `expenses_${year}_${month}` : `expenses_${year}`,
  departments: (year: number) => `departments_${year}`,
  categories: (year: number) => `categories_${year}`,
  analytics: (year: number) => `analytics_${year}`,
  forecast: (year: number) => `forecast_${year}`,
  compliance: (year: number) => `compliance_${year}`,
  roi: (year: number) => `roi_${year}`
};

// Cache invalidation patterns
export const invalidationPatterns = {
  all: '.*',
  year: (year: number) => `.*_${year}.*`,
  expenses: 'expenses_.*',
  analytics: '(analytics|forecast|compliance|roi)_.*',
  computed: '(budget_health|forecast|compliance)_.*'
};

// Export types for use in other modules
export type { CacheEntry, HealthMetrics, ForecastData };