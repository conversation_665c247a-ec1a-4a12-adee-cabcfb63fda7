import { invoke } from '@tauri-apps/api/core';

export interface TrainingFileUploadRequest {
  filename: string;
  content: string; // base64 encoded file content
  file_type: string;
  metadata?: Record<string, any>;
}

export interface TrainingFileUploadResponse {
  success: boolean;
  message: string;
  file_id?: string;
  processed_data?: any;
}

export interface ProcessedCompetencyMatrix {
  id: string;
  name: string;
  description: string;
  skills: Array<{
    name: string;
    levels: CompetencyLevel[];
  }>;
  metadata: Record<string, any>;
}

export interface CompetencyLevel {
  level: number;
  name: string;
  description: string;
  criteria: string[];
}

class FileUploadService {
  /**
   * Upload a training template file
   */
  async uploadTrainingTemplate(file: File): Promise<TrainingFileUploadResponse> {
    try {
      // Convert file to base64
      const content = await this.fileToBase64(file);
      
      const request: TrainingFileUploadRequest = {
        filename: file.name,
        content,
        file_type: file.type,
        metadata: {
          size: file.size,
          lastModified: file.lastModified
        }
      };

      const response = await invoke<TrainingFileUploadResponse>(
        'upload_training_template',
        { request }
      );

      return response;
    } catch (error) {
      console.error('Error uploading training template:', error);
      throw new Error(`Failed to upload training template: ${error}`);
    }
  }

  /**
   * Upload a competency matrix file
   */
  async uploadCompetencyMatrix(file: File): Promise<TrainingFileUploadResponse> {
    try {
      // Convert file to base64
      const content = await this.fileToBase64(file);
      
      const request: TrainingFileUploadRequest = {
        filename: file.name,
        content,
        file_type: file.type,
        metadata: {
          size: file.size,
          lastModified: file.lastModified
        }
      };

      const response = await invoke<TrainingFileUploadResponse>(
        'upload_competency_matrix',
        { request }
      );

      return response;
    } catch (error) {
      console.error('Error uploading competency matrix:', error);
      throw new Error(`Failed to upload competency matrix: ${error}`);
    }
  }

  /**
   * Convert file to base64 string
   */
  private fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        const result = reader.result as string;
        // Remove the data URL prefix (e.g., "data:application/json;base64,")
        const base64 = result.split(',')[1];
        resolve(base64);
      };
      reader.onerror = (error) => reject(error);
    });
  }

  /**
   * Validate file type and size for training templates
   */
  validateTrainingTemplateFile(file: File): { valid: boolean; error?: string } {
    const allowedTypes = [
      'application/json',
      'text/csv',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/pdf'
    ];
    
    const maxSize = 10 * 1024 * 1024; // 10MB

    if (!allowedTypes.includes(file.type)) {
      return {
        valid: false,
        error: 'Invalid file type. Please upload JSON, CSV, Excel, or PDF files only.'
      };
    }

    if (file.size > maxSize) {
      return {
        valid: false,
        error: 'File size must be less than 10MB.'
      };
    }

    return { valid: true };
  }

  /**
   * Validate file type and size for competency matrices
   */
  validateCompetencyMatrixFile(file: File): { valid: boolean; error?: string } {
    const allowedTypes = [
      'application/json',
      'text/csv',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ];
    
    const maxSize = 5 * 1024 * 1024; // 5MB

    if (!allowedTypes.includes(file.type)) {
      return {
        valid: false,
        error: 'Invalid file type. Please upload JSON, CSV, or Excel files only.'
      };
    }

    if (file.size > maxSize) {
      return {
        valid: false,
        error: 'File size must be less than 5MB.'
      };
    }

    return { valid: true };
  }
}

export const fileUploadService = new FileUploadService();
export default fileUploadService;