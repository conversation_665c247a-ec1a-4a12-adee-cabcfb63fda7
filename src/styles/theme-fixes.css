/* Theme Transition Fixes */
/* Prevents layout issues during theme changes */

/* Ensure smooth transitions for theme changes */
* {
  transition: background-color 0.2s ease, color 0.2s ease, border-color 0.2s ease;
}

/* Prevent card compression during theme transitions */
[role="alert"],
.alert,
.card,
[class*="card"] {
  min-height: auto !important;
  contain: layout style;
  will-change: background-color, color;
}

/* Ensure cards maintain their dimensions */
.relative.w-full.rounded-lg.border.p-4 {
  min-height: fit-content;
  overflow: visible;
}

/* Fix for warning and error cards */
.border-destructive\/50,
.text-destructive,
[class*="warning"],
[class*="error"] {
  min-height: auto !important;
  padding: 1rem !important;
  overflow: visible !important;
  display: block !important;
}

/* Prevent layout shift on theme change */
html {
  overflow-y: scroll;
}

body {
  overflow-x: hidden;
}

/* Ensure theme variables are available in all states */
html:not([data-theme]) {
  /* Fallback to dark theme if data-theme is missing */
  --color-background: oklch(0.12 0.01 240);
  --color-foreground: oklch(0.98 0.01 240);
  --color-card: oklch(0.14 0.01 240);
  --color-card-foreground: oklch(0.98 0.01 240);
  --color-border: oklch(0.16 0.01 240);
  --color-primary: oklch(0.98 0.01 240);
  --color-destructive: oklch(0.6 0.2 25);
}

/* Ensure proper theme application order */
html.theme-dark,
html[data-theme="dark"] {
  color-scheme: dark;
}

html.theme-light,
html[data-theme="light"] {
  color-scheme: light;
}

html.theme-gray,
html[data-theme="gray"] {
  color-scheme: dark;
}

/* Prevent FOUC (Flash of Unstyled Content) */
html:not(.theme-initialized) {
  visibility: hidden;
  opacity: 0;
}

html.theme-initialized {
  visibility: visible;
  opacity: 1;
  transition: opacity 0.3s ease;
}