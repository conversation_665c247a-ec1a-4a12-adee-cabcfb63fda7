import React, { useEffect } from 'react';
import { FinancialDashboard } from './components/financial-dashboard';
import { mockRootProps } from './components/financial-dashboard/FinancialDashboardMockData';

function App() {
  // Set dark theme on mount
  useEffect(() => {
    document.documentElement.classList.add('dark');
    return () => {
      document.documentElement.classList.remove('dark');
    };
  }, []);

  const handleTimeFrameChange = (timeFrame: any) => {
    console.log('Time frame changed to:', timeFrame);
  };

  const handleBuyStock = () => {
    console.log('Buy stock action triggered');
  };

  const handleSellStock = () => {
    console.log('Sell stock action triggered');
  };

  const handleDeposit = () => {
    console.log('Deposit action triggered');
  };

  const handleWithdraw = () => {
    console.log('Withdraw action triggered');
  };

  const handleVisitWebsite = (url: string) => {
    window.open(url, '_blank');
  };

  return (
    <div className="min-h-screen bg-dashboard-bg">
      <FinancialDashboard
        {...mockRootProps}
        onTimeFrameChange={handleTimeFrameChange}
        onBuyStock={handleBuyStock}
        onSellStock={handleSellStock}
        onDeposit={handleDeposit}
        onWithdraw={handleWithdraw}
        onVisitWebsite={handleVisitWebsite}
      />
    </div>
  );
}

export default App;