# Developer Experience Subagents

Developer Experience subagents are your productivity multipliers, focusing on making development faster, easier, and more enjoyable. These specialists handle everything from code refactoring to documentation, from build optimization to Git workflows. They remove friction from the development process, automate repetitive tasks, and help teams work more efficiently with better tools and practices.

## <� When to Use Developer Experience Subagents

Use these subagents when you need to:
- **Refactor legacy code** for better maintainability
- **Optimize build systems** for faster development
- **Create developer tools** and CLI applications
- **Write technical documentation** that developers love
- **Manage dependencies** and package updates
- **Streamline Git workflows** and branching strategies
- **Modernize codebases** with latest practices
- **Improve developer productivity** across teams

## =� Available Subagents

### [**build-engineer**](build-engineer.md) - Build system specialist
Build optimization expert making compilation and bundling lightning fast. Masters various build tools, optimization techniques, and caching strategies. Reduces build times from minutes to seconds.

**Use when:** Optimizing build times, configuring build tools, implementing build caching, setting up monorepo builds, or troubleshooting build issues.

### [**cli-developer**](cli-developer.md) - Command-line tool creator
<PERSON><PERSON><PERSON> specialist building intuitive command-line interfaces. Expert in argument parsing, interactive prompts, and cross-platform compatibility. Creates tools developers love to use.

**Use when:** Building CLI tools, designing command interfaces, implementing interactive CLIs, creating developer utilities, or improving existing CLI applications.

### [**dependency-manager**](dependency-manager.md) - Package and dependency specialist
Dependency expert managing complex package ecosystems. Masters version resolution, security updates, and dependency optimization. Keeps dependencies secure and up-to-date without breaking things.

**Use when:** Managing dependencies, resolving version conflicts, implementing security updates, optimizing package sizes, or setting up dependency automation.

### [**documentation-engineer**](documentation-engineer.md) - Technical documentation expert
Documentation specialist creating clear, comprehensive technical docs. Masters API documentation, tutorials, and developer guides. Makes complex systems understandable through great documentation.

**Use when:** Writing API documentation, creating developer guides, building documentation sites, improving existing docs, or setting up documentation workflows.

### [**dx-optimizer**](dx-optimizer.md) - Developer experience optimization specialist
DX expert identifying and eliminating developer friction. Analyzes workflows, tools, and processes to improve productivity. Makes development feel effortless and enjoyable.

**Use when:** Improving developer workflows, analyzing productivity bottlenecks, selecting developer tools, optimizing development environments, or measuring developer experience.

### [**git-workflow-manager**](git-workflow-manager.md) - Git workflow and branching expert
Git specialist designing efficient version control workflows. Masters branching strategies, merge conflict resolution, and Git automation. Ensures smooth collaboration through Git best practices.

**Use when:** Designing Git workflows, implementing branching strategies, resolving complex merges, automating Git processes, or training teams on Git.

### [**legacy-modernizer**](legacy-modernizer.md) - Legacy code modernization specialist
Modernization expert breathing new life into old codebases. Masters incremental refactoring, dependency updates, and architecture improvements. Transforms legacy code without breaking functionality.

**Use when:** Modernizing legacy applications, planning refactoring strategies, updating old frameworks, migrating to new technologies, or improving code maintainability.

### [**mcp-developer**](mcp-developer.md) - Model Context Protocol specialist
MCP expert building servers and clients that connect AI systems with external tools and data sources. Masters protocol specification, SDK implementation, and production-ready integrations. Creates seamless bridges between AI and external services.

**Use when:** Building MCP servers, creating AI tool integrations, implementing Model Context Protocol clients, connecting AI systems to external APIs, or developing AI-powered applications with external data sources.

### [**refactoring-specialist**](refactoring-specialist.md) - Code refactoring expert
Refactoring master improving code structure without changing behavior. Expert in design patterns, code smells, and safe refactoring techniques. Makes code cleaner and more maintainable.

**Use when:** Refactoring complex code, eliminating code smells, implementing design patterns, improving code structure, or preparing code for new features.

### [**tooling-engineer**](tooling-engineer.md) - Developer tooling specialist
Tooling expert building and integrating developer tools. Masters IDE configurations, linters, formatters, and custom tooling. Creates development environments that boost productivity.

**Use when:** Setting up development tools, creating custom tooling, configuring IDEs, implementing code quality tools, or building developer platforms.

## =� Quick Selection Guide

| If you need to... | Use this subagent |
|-------------------|-------------------|
| Speed up builds | **build-engineer** |
| Create CLI tools | **cli-developer** |
| Manage packages | **dependency-manager** |
| Write documentation | **documentation-engineer** |
| Improve workflows | **dx-optimizer** |
| Design Git strategies | **git-workflow-manager** |
| Modernize legacy code | **legacy-modernizer** |
| Build MCP integrations | **mcp-developer** |
| Refactor code | **refactoring-specialist** |
| Build dev tools | **tooling-engineer** |

## =� Common DX Patterns

**Legacy Modernization:**
- **legacy-modernizer** for strategy
- **refactoring-specialist** for code improvement
- **dependency-manager** for package updates
- **documentation-engineer** for new docs

**Developer Productivity:**
- **dx-optimizer** for workflow analysis
- **tooling-engineer** for tool setup
- **build-engineer** for build optimization
- **git-workflow-manager** for version control

**Tool Development:**
- **cli-developer** for command-line tools
- **tooling-engineer** for IDE integration
- **documentation-engineer** for tool docs
- **build-engineer** for tool packaging

**Code Quality:**
- **refactoring-specialist** for code structure
- **dependency-manager** for package health
- **git-workflow-manager** for code review
- **documentation-engineer** for standards

## <� Getting Started

1. **Identify pain points** in your development process
2. **Choose relevant specialists** for improvement
3. **Analyze current state** of tools and workflows
4. **Implement improvements** incrementally
5. **Measure impact** on developer productivity

## =� Best Practices

- **Automate repetitive tasks:** Time saved compounds
- **Document everything:** Future developers will thank you
- **Incremental improvements:** Small changes add up
- **Measure impact:** Track productivity gains
- **Tool standardization:** Consistency reduces friction
- **Developer feedback:** Listen to your users
- **Continuous improvement:** DX is never "done"
- **Share knowledge:** Spread best practices

Choose your developer experience specialist and make development a joy!