# Language Specialists Subagents

Language Specialists are your expert guides for specific programming languages and their ecosystems. These subagents bring deep knowledge of language idioms, best practices, performance optimization techniques, and framework expertise. Whether you're working with modern web frameworks, system programming languages, or enterprise platforms, these specialists ensure you're writing idiomatic, efficient, and maintainable code.

## When to Use Language Specialists

Use these subagents when you need to:
- **Master language-specific features** and advanced patterns
- **Optimize performance** using language-specific techniques
- **Implement framework best practices** for production applications
- **Migrate or modernize** existing codebases
- **Solve language-specific challenges** with expert guidance
- **Learn advanced patterns** and idioms of a language
- **Build framework-specific applications** with confidence

## Available Subagents

### [**angular-architect**](angular-architect.md) - Angular 15+ enterprise patterns expert
Master of Angular ecosystem specializing in enterprise-scale applications. Expert in RxJS, NgRx state management, and micro-frontend architectures. Builds performant, maintainable Angular applications with advanced patterns.

**Use when:** Building enterprise Angular apps, implementing complex state management, optimizing Angular performance, or migrating to latest Angular versions.

### [**cpp-pro**](cpp-pro.md) - C++ performance expert
Systems programming specialist with deep knowledge of modern C++ standards, memory management, and performance optimization. Masters template metaprogramming, RAII patterns, and low-level optimizations.

**Use when:** Writing high-performance C++ code, implementing system-level software, optimizing memory usage, or working with embedded systems.

### [**csharp-developer**](csharp-developer.md) - .NET ecosystem specialist
Expert in C# language features and the entire .NET ecosystem. Proficient in ASP.NET Core, Entity Framework, and cross-platform development. Builds enterprise applications with clean architecture.

**Use when:** Developing .NET applications, building ASP.NET Core APIs, implementing Windows applications, or working with Azure services.

### [**django-developer**](django-developer.md) - Django 4+ web development expert
Python web framework specialist focusing on Django's batteries-included philosophy. Masters ORM optimization, async views, and Django's security features. Builds scalable web applications rapidly.

**Use when:** Creating Django web applications, building REST APIs with DRF, implementing complex database operations, or developing data-driven applications.

### [**dotnet-core-expert**](dotnet-core-expert.md) - .NET 8 cross-platform specialist
Modern .NET expert specializing in cross-platform development, minimal APIs, and cloud-native applications. Masters performance optimization with native AOT compilation and microservices patterns.

**Use when:** Building cross-platform .NET apps, creating minimal APIs, implementing microservices, or optimizing .NET performance.

### [**flutter-expert**](flutter-expert.md) - Flutter 3+ cross-platform mobile expert
Mobile development specialist creating beautiful, natively compiled applications from a single codebase. Expert in widget composition, state management, and platform-specific implementations.

**Use when:** Building cross-platform mobile apps, creating custom Flutter widgets, implementing complex animations, or optimizing Flutter performance.

### [**golang-pro**](golang-pro.md) - Go concurrency specialist
Go language expert focusing on concurrent programming, channels, and goroutines. Masters building efficient, scalable backend services and CLI tools with Go's simplicity and performance.

**Use when:** Building concurrent systems, creating microservices in Go, developing CLI tools, or implementing high-performance network services.

### [**java-architect**](java-architect.md) - Enterprise Java expert
Java ecosystem master with expertise in Spring, Jakarta EE, and enterprise patterns. Specializes in building robust, scalable applications with modern Java features and frameworks.

**Use when:** Developing enterprise Java applications, implementing Spring Boot services, designing Java architectures, or modernizing legacy Java code.

### [**javascript-pro**](javascript-pro.md) - JavaScript development expert
Modern JavaScript specialist mastering ES6+, async patterns, and the npm ecosystem. Expert in both browser and Node.js environments, building everything from scripts to full applications.

**Use when:** Writing modern JavaScript, working with Node.js, implementing async patterns, or optimizing JavaScript performance.

### [**kotlin-specialist**](kotlin-specialist.md) - Modern JVM language expert
Kotlin language expert for Android development and JVM applications. Masters coroutines, DSL creation, and Kotlin's expressive features. Builds safe, concise applications.

**Use when:** Developing Android apps with Kotlin, building Kotlin backend services, migrating from Java to Kotlin, or creating Kotlin DSLs.

### [**laravel-specialist**](laravel-specialist.md) - Laravel 10+ PHP framework expert
PHP framework specialist focusing on Laravel's elegant syntax and powerful features. Masters Eloquent ORM, queue systems, and Laravel's extensive ecosystem.

**Use when:** Building Laravel applications, implementing complex queue jobs, creating Laravel packages, or optimizing Eloquent queries.

### [**nextjs-developer**](nextjs-developer.md) - Next.js 14+ full-stack specialist
React framework expert specializing in Next.js App Router, server components, and full-stack features. Builds blazing-fast, SEO-friendly web applications.

**Use when:** Creating Next.js applications, implementing server-side rendering, building full-stack React apps, or optimizing for Core Web Vitals.

### [**php-pro**](php-pro.md) - PHP web development expert
Modern PHP specialist with expertise in PHP 8+ features, Composer ecosystem, and framework-agnostic development. Builds secure, performant PHP applications.

**Use when:** Developing PHP applications, modernizing legacy PHP code, implementing PHP APIs, or working with PHP frameworks.

### [**python-pro**](python-pro.md) - Python ecosystem master
Python language expert covering web development, data science, automation, and system scripting. Masters Pythonic code patterns and the vast Python ecosystem.

**Use when:** Writing Python applications, building data pipelines, creating automation scripts, or developing Python packages.

### [**rails-expert**](rails-expert.md) - Rails 7+ rapid development expert
Ruby on Rails specialist focusing on convention over configuration and rapid development. Masters Active Record, Hotwire, and Rails' comprehensive feature set.

**Use when:** Building Rails applications, implementing real-time features with Hotwire, optimizing Active Record queries, or upgrading Rails versions.

### [**react-specialist**](react-specialist.md) - React 18+ modern patterns expert
React ecosystem expert mastering hooks, concurrent features, and modern patterns. Builds performant, maintainable React applications with best practices.

**Use when:** Developing React applications, implementing complex state management, optimizing React performance, or migrating to modern React patterns.

### [**rust-engineer**](rust-engineer.md) - Systems programming expert
Rust language specialist focusing on memory safety, ownership patterns, and zero-cost abstractions. Builds reliable, efficient systems software.

**Use when:** Writing systems software in Rust, building performance-critical applications, implementing safe concurrent code, or developing WebAssembly modules.

### [**spring-boot-engineer**](spring-boot-engineer.md) - Spring Boot 3+ microservices expert
Spring ecosystem specialist building cloud-native Java applications. Masters reactive programming, Spring Cloud, and microservices patterns.

**Use when:** Creating Spring Boot microservices, implementing reactive applications, building cloud-native Java apps, or working with Spring Cloud.

### [**sql-pro**](sql-pro.md) - Database query expert
SQL language master optimizing complex queries across different database systems. Expert in query optimization, indexing strategies, and advanced SQL features.

**Use when:** Writing complex SQL queries, optimizing database performance, designing database schemas, or troubleshooting query performance.

### [**swift-expert**](swift-expert.md) - iOS and macOS specialist
Swift language expert for Apple platform development. Masters SwiftUI, UIKit, and Apple's frameworks. Builds native iOS, macOS, and cross-platform Apple applications.

**Use when:** Developing iOS/macOS applications, implementing SwiftUI interfaces, working with Apple frameworks, or optimizing Swift performance.

### [**typescript-pro**](typescript-pro.md) - TypeScript specialist
TypeScript expert ensuring type safety in JavaScript applications. Masters advanced type system features, generics, and TypeScript configuration for large-scale applications.

**Use when:** Adding TypeScript to projects, implementing complex type definitions, migrating JavaScript to TypeScript, or building type-safe applications.

### [**vue-expert**](vue-expert.md) - Vue 3 Composition API expert
Vue.js framework specialist mastering the Composition API, reactivity system, and Vue ecosystem. Builds elegant, reactive web applications with Vue's progressive framework.

**Use when:** Creating Vue applications, implementing Composition API patterns, working with Nuxt.js, or optimizing Vue performance.

##   Quick Selection Guide

| Language/Framework | Subagent | Best For |
|-------------------|----------|----------|
| Angular | **angular-architect** | Enterprise web apps, complex SPAs |
| C++ | **cpp-pro** | Systems programming, performance-critical code |
| C#/.NET | **csharp-developer** | Windows apps, enterprise software |
| Django | **django-developer** | Python web apps, REST APIs |
| .NET Core | **dotnet-core-expert** | Cross-platform .NET, microservices |
| Flutter | **flutter-expert** | Cross-platform mobile apps |
| Go | **golang-pro** | Concurrent systems, microservices |
| Java | **java-architect** | Enterprise applications |
| JavaScript | **javascript-pro** | Web development, Node.js |
| Kotlin | **kotlin-specialist** | Android apps, modern JVM |
| Laravel | **laravel-specialist** | PHP web applications |
| Next.js | **nextjs-developer** | Full-stack React apps |
| PHP | **php-pro** | Web development, APIs |
| Python | **python-pro** | General purpose, data science |
| Rails | **rails-expert** | Rapid web development |
| React | **react-specialist** | Modern web UIs |
| Rust | **rust-engineer** | Systems software, WebAssembly |
| Spring Boot | **spring-boot-engineer** | Java microservices |
| SQL | **sql-pro** | Database queries, optimization |
| Swift | **swift-expert** | iOS/macOS development |
| TypeScript | **typescript-pro** | Type-safe JavaScript |
| Vue | **vue-expert** | Progressive web apps |

##   Common Technology Stacks

**Modern Web Application:**
- **react-specialist** + **typescript-pro** + **nextjs-developer**
- **vue-expert** + **typescript-pro** + **laravel-specialist**
- **angular-architect** + **spring-boot-engineer**

**Mobile Development:**
- **flutter-expert** for cross-platform
- **swift-expert** for iOS native
- **kotlin-specialist** for Android native

**Enterprise Backend:**
- **java-architect** + **spring-boot-engineer**
- **csharp-developer** + **dotnet-core-expert**
- **python-pro** + **django-developer**

**Systems Programming:**
- **rust-engineer** for safety-critical systems
- **cpp-pro** for performance-critical applications
- **golang-pro** for concurrent systems

##    Getting Started

1. **Identify your technology stack** and choose the appropriate specialist
2. **Describe your project context** including existing code and constraints
3. **Specify your goals** (learning, optimization, implementation)
4. **Share relevant code** for context-aware assistance
5. **Follow the specialist's guidance** for best practices

##   Best Practices

- **Use language idioms:** Each specialist knows the idiomatic way to write code
- **Leverage ecosystem tools:** Specialists understand the full ecosystem
- **Follow framework conventions:** Each framework has its own best practices
- **Consider performance early:** Language-specific optimizations matter
- **Think about maintenance:** Write code that future developers will understand

Choose your language specialist and write better code today!