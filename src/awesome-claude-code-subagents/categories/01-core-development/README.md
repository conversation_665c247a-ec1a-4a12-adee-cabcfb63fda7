# Core Development Subagents

Core Development subagents are your essential toolkit for building modern applications from the ground up. These specialized agents cover the entire development spectrum - from backend services to frontend interfaces, from mobile apps to desktop applications, and from simple APIs to complex distributed systems.

## 🎯 When to Use Core Development Subagents

Use these subagents when you need to:
- **Build new applications** from scratch with proper architecture
- **Implement complex features** that require deep technical expertise  
- **Design scalable systems** that can grow with your needs
- **Create beautiful UIs** that provide exceptional user experiences
- **Develop real-time features** for interactive applications
- **Modernize legacy systems** with current best practices
- **Optimize performance** across the entire stack

## 📋 Available Subagents

### [**backend-developer**](backend-developer.md) - Server-side expert for scalable APIs
Your go-to specialist for building robust server applications, RESTful APIs, and microservices. Excels at database design, authentication systems, and performance optimization. Perfect for creating the backbone of your application with Node.js, Python, Java, or other backend technologies.

**Use when:** Building APIs, designing databases, implementing authentication, handling business logic, or optimizing server performance.

### [**frontend-developer**](frontend-developer.md) - UI/UX specialist for React, Vue, and Angular  
Master of modern web interfaces who creates responsive, accessible, and performant user experiences. Expert in component architecture, state management, and modern CSS. Transforms designs into pixel-perfect, interactive applications.

**Use when:** Creating web interfaces, implementing complex UI components, optimizing frontend performance, or ensuring accessibility compliance.

### [**fullstack-developer**](fullstack-developer.md) - End-to-end feature development
The versatile expert who seamlessly works across the entire stack. Builds complete features from database to UI, ensuring smooth integration between frontend and backend. Ideal for rapid prototyping and full feature implementation.

**Use when:** Building complete features, prototyping applications, working on small to medium projects, or when you need unified development across the stack.

### [**mobile-developer**](mobile-developer.md) - Cross-platform mobile specialist
Expert in creating native and cross-platform mobile applications for iOS and Android. Proficient in React Native, Flutter, and native development. Focuses on mobile-specific challenges like offline functionality, push notifications, and app store optimization.

**Use when:** Building mobile apps, implementing mobile-specific features, optimizing for mobile performance, or preparing for app store deployment.

### [**electron-pro**](electron-pro.md) - Desktop application expert
Specialist in building cross-platform desktop applications using web technologies. Masters Electron framework for creating installable desktop apps with native capabilities. Handles auto-updates, system integration, and desktop-specific features.

**Use when:** Creating desktop applications, porting web apps to desktop, implementing system tray features, or building offline-capable desktop tools.

### [**api-designer**](api-designer.md) - REST and GraphQL API architect
The architect who designs beautiful, intuitive, and scalable APIs. Expert in RESTful principles, GraphQL schemas, API versioning, and documentation. Ensures your APIs are developer-friendly and future-proof.

**Use when:** Designing new APIs, refactoring existing endpoints, implementing API standards, or creating comprehensive API documentation.

### [**graphql-architect**](graphql-architect.md) - GraphQL schema and federation expert
Specialized in GraphQL ecosystem, from schema design to federation strategies. Masters resolver optimization, subscription patterns, and GraphQL best practices. Perfect for building flexible, efficient data layers.

**Use when:** Implementing GraphQL APIs, designing schemas, optimizing resolvers, setting up federation, or migrating from REST to GraphQL.

### [**microservices-architect**](microservices-architect.md) - Distributed systems designer
Expert in designing and implementing microservices architectures. Handles service decomposition, inter-service communication, distributed transactions, and orchestration. Ensures your system scales horizontally with resilience.

**Use when:** Breaking monoliths into microservices, designing distributed systems, implementing service mesh, or solving distributed system challenges.

### [**websocket-engineer**](websocket-engineer.md) - Real-time communication specialist
Master of real-time, bidirectional communication. Implements WebSocket servers, manages connections at scale, and handles real-time features like chat, notifications, and live updates. Expert in Socket.io and native WebSocket implementations.

**Use when:** Building chat applications, implementing real-time notifications, creating collaborative features, or developing live-updating dashboards.

## 🚀 Quick Selection Guide

| If you need to... | Use this subagent |
|-------------------|-------------------|
| Build a REST API with database | **backend-developer** |
| Create a responsive web UI | **frontend-developer** |
| Develop a complete web application | **fullstack-developer** |
| Build a mobile app | **mobile-developer** |
| Create a desktop application | **electron-pro** |
| Design a new API structure | **api-designer** |
| Implement GraphQL | **graphql-architect** |
| Build a distributed system | **microservices-architect** |
| Add real-time features | **websocket-engineer** |

## 💡 Common Combinations

**Full-Stack Web Application:**
- Start with **api-designer** for API structure
- Use **backend-developer** for server implementation  
- Employ **frontend-developer** for UI development

**Enterprise System:**
- Begin with **microservices-architect** for system design
- Use **graphql-architect** for data layer
- Add **backend-developer** for service implementation

**Real-time Application:**
- Start with **websocket-engineer** for real-time infrastructure
- Add **backend-developer** for business logic
- Use **frontend-developer** for interactive UI

## 🎬 Getting Started

1. **Choose the right subagent** based on your specific needs
2. **Provide clear context** about your project requirements
3. **Specify your tech stack** preferences if any
4. **Describe your constraints** (performance, scalability, timeline)
5. **Let the subagent guide you** through best practices and implementation

Each subagent comes with:
- Deep expertise in their domain
- Knowledge of current best practices
- Ability to work with your existing codebase
- Focus on clean, maintainable code
- Understanding of production requirements

## 📚 Best Practices

- **Start with architecture:** Use architects (API, GraphQL, Microservices) before implementation
- **Iterate frequently:** Work with subagents in short cycles for better results
- **Combine expertise:** Use multiple subagents for complex projects
- **Follow conventions:** Each subagent knows the best practices for their domain
- **Think production-ready:** All subagents consider scalability, security, and maintenance

Choose your subagent and start building amazing applications today!