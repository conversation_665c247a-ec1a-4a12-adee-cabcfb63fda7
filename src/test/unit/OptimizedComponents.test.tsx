import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import '@testing-library/jest-dom';
import {
  OptimizedContentManager,
  OptimizedChatInterface,
  OptimizedSearchInterface,
  OptimizedSettingsPanel
} from '../../components/Lightbulb/components/optimized';
import { KnowledgeItem, ChatMessage, DocumentSource } from '../../components/Lightbulb/types';

// Mock data
const mockKnowledgeItems: KnowledgeItem[] = [
  {
    id: '1',
    title: 'Test Item 1',
    content: 'Test content 1',
    tags: ['test', 'mock'],
    created_at: Date.now(),
    updated_at: Date.now(),
    timestamp: new Date().toISOString(),
    category: 'general'
  },
  {
    id: '2',
    title: 'Test Item 2',
    content: 'Test content 2',
    tags: ['test'],
    created_at: Date.now(),
    updated_at: Date.now(),
    timestamp: new Date().toISOString(),
    category: 'work'
  }
];

const mockChatMessages: ChatMessage[] = [
  {
    id: '1',
    content: 'Hello, how can I help?',
    role: 'assistant',
    timestamp: Date.now()
  },
  {
    id: '2',
    content: 'I need help with testing',
    role: 'user',
    timestamp: Date.now()
  }
];

const mockDocumentSources: DocumentSource[] = [
  {
    id: '1',
    name: 'Test Document',
    type: 'pdf',
    content: 'Test document content',
    uploaded_at: Date.now()
  }
];

const mockSettings = {
  autoSaveInterval: 30000,
  enableNotifications: true,
  enableAutoTagging: true,
  defaultViewMode: 'grid',
  aiModel: 'gpt-4',
  temperature: 0.7,
  maxTokens: 2000,
  enableContextMemory: true,
  theme: 'light',
  fontSize: 'medium',
  compactMode: false,
  showAnimations: true
};

describe('OptimizedContentManager', () => {
  const defaultProps = {
    contentItems: mockKnowledgeItems,
    tags: ['test', 'mock', 'work'],
    sources: mockDocumentSources,
    categories: ['general', 'work'],
    searchQuery: '',
    onContentUpdate: vi.fn(),
    onTagsUpdate: vi.fn(),
    onSourcesUpdate: vi.fn(),
    onCategoriesUpdate: vi.fn(),
    onSearchQueryChange: vi.fn()
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render content items', () => {
    render(<OptimizedContentManager {...defaultProps} />);
    
    expect(screen.getByText('Test Item 1')).toBeDefined();
    expect(screen.getByText('Test Item 2')).toBeDefined();
  });

  it('should handle search query changes', async () => {
    const user = userEvent.setup();
    render(<OptimizedContentManager {...defaultProps} />);
    
    const searchInput = screen.getByPlaceholderText(/search/i);
    await user.type(searchInput, 'test query');
    
    expect(defaultProps.onSearchQueryChange).toHaveBeenCalledWith('test query');
  });

  it('should filter content based on search query', () => {
    const propsWithSearch = {
      ...defaultProps,
      searchQuery: 'Item 1'
    };
    
    render(<OptimizedContentManager {...propsWithSearch} />);
    
    expect(screen.getByText('Test Item 1')).toBeDefined();
    expect(screen.queryByText('Test Item 2')).toBeNull();
  });

  it('should not re-render when props are the same', () => {
    const renderSpy = vi.fn();
    
    const SpyComponent = React.memo(() => {
      renderSpy();
      return <OptimizedContentManager {...defaultProps} />;
    });
    
    const { rerender } = render(<SpyComponent />);
    expect(renderSpy).toHaveBeenCalledTimes(1);
    
    // Re-render with same props
    rerender(<SpyComponent />);
    expect(renderSpy).toHaveBeenCalledTimes(1); // Should not re-render
  });
});

describe('OptimizedChatInterface', () => {
  const defaultProps = {
    messages: mockChatMessages,
    sources: mockDocumentSources,
    onMessagesUpdate: vi.fn(),
    onSourcesUpdate: vi.fn()
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render chat messages', () => {
    render(<OptimizedChatInterface {...defaultProps} />);
    
    expect(screen.getByText('Hello, how can I help?')).toBeDefined();
    expect(screen.getByText('I need help with testing')).toBeDefined();
  });

  it('should handle new message input', async () => {
    const user = userEvent.setup();
    render(<OptimizedChatInterface {...defaultProps} />);
    
    const messageInput = screen.getByPlaceholderText(/type your message/i);
    const sendButton = screen.getByRole('button', { name: /send/i });
    
    await user.type(messageInput, 'New test message');
    await user.click(sendButton);
    
    expect(defaultProps.onMessagesUpdate).toHaveBeenCalled();
  });

  it('should display loading state', () => {
    const { rerender } = render(<OptimizedChatInterface {...defaultProps} />);
    
    // Simulate loading state by checking if loading indicator appears during message sending
    const messageInput = screen.getByPlaceholderText(/type your message/i);
    fireEvent.change(messageInput, { target: { value: 'test' } });
    
    expect((messageInput as HTMLInputElement).value).toBe('test');
  });

  it('should optimize rendering with memoization', () => {
    const renderSpy = vi.fn();
    
    const SpyComponent = React.memo(() => {
      renderSpy();
      return <OptimizedChatInterface {...defaultProps} />;
    });
    
    const { rerender } = render(<SpyComponent />);
    expect(renderSpy).toHaveBeenCalledTimes(1);
    
    // Re-render with same props
    rerender(<SpyComponent />);
    expect(renderSpy).toHaveBeenCalledTimes(1);
  });
});

describe('OptimizedSearchInterface', () => {
  const defaultProps = {
    knowledgeItems: mockKnowledgeItems,
    sources: mockDocumentSources,
    onItemSelect: vi.fn(),
    onSourceSelect: vi.fn(),
    onResultSelect: vi.fn()
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render search interface', () => {
    render(<OptimizedSearchInterface {...defaultProps} />);
    
    expect(screen.getByPlaceholderText(/search/i)).toBeDefined();
  });

  it('should handle search input', async () => {
    const user = userEvent.setup();
    render(<OptimizedSearchInterface {...defaultProps} />);
    
    const searchInput = screen.getByPlaceholderText(/search/i);
    await user.type(searchInput, 'test search');
    
    expect((searchInput as HTMLInputElement).value).toBe('test search');
  });

  it('should filter results based on search query', async () => {
    const user = userEvent.setup();
    render(<OptimizedSearchInterface {...defaultProps} />);
    
    const searchInput = screen.getByPlaceholderText(/search/i);
    await user.type(searchInput, 'Item 1');
    
    await waitFor(() => {
      expect(screen.getByText('Test Item 1')).toBeDefined();
    });
  });

  it('should handle item selection', async () => {
    const user = userEvent.setup();
    render(<OptimizedSearchInterface {...defaultProps} />);
    
    const firstItem = screen.getByText('Test Item 1');
    await user.click(firstItem);
    
    expect(defaultProps.onItemSelect).toHaveBeenCalledWith(mockKnowledgeItems[0]);
  });
});

describe('OptimizedSettingsPanel', () => {
  const defaultProps = {
    isOpen: true,
    onClose: vi.fn(),
    settings: mockSettings,
    onSettingsChange: vi.fn(),
    onExportData: vi.fn(),
    onImportData: vi.fn(),
    onClearData: vi.fn(),
    onResetSettings: vi.fn()
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render when open', () => {
    render(<OptimizedSettingsPanel {...defaultProps} />);
    
    expect(screen.getByText(/settings/i)).toBeDefined();
  });

  it('should not render when closed', () => {
    render(<OptimizedSettingsPanel {...defaultProps} isOpen={false} />);
    
    expect(screen.queryByText(/settings/i)).toBeNull();
  });

  it('should handle settings changes', async () => {
    const user = userEvent.setup();
    render(<OptimizedSettingsPanel {...defaultProps} />);
    
    // Find a toggle or input to change
    const notificationToggle = screen.getByLabelText(/notifications/i);
    await user.click(notificationToggle);
    
    expect(defaultProps.onSettingsChange).toHaveBeenCalled();
  });

  it('should handle close action', async () => {
    const user = userEvent.setup();
    render(<OptimizedSettingsPanel {...defaultProps} />);
    
    const closeButton = screen.getByRole('button', { name: /close/i });
    await user.click(closeButton);
    
    expect(defaultProps.onClose).toHaveBeenCalled();
  });

  it('should handle export data', async () => {
    const user = userEvent.setup();
    render(<OptimizedSettingsPanel {...defaultProps} />);
    
    const exportButton = screen.getByRole('button', { name: /export/i });
    await user.click(exportButton);
    
    expect(defaultProps.onExportData).toHaveBeenCalled();
  });

  it('should optimize rendering with React.memo', () => {
    const renderSpy = vi.fn();
    
    const SpyComponent = React.memo(() => {
      renderSpy();
      return <OptimizedSettingsPanel {...defaultProps} />;
    });
    
    const { rerender } = render(<SpyComponent />);
    expect(renderSpy).toHaveBeenCalledTimes(1);
    
    // Re-render with same props
    rerender(<SpyComponent />);
    expect(renderSpy).toHaveBeenCalledTimes(1);
  });
});

describe('Performance Tests', () => {
  it('should handle large datasets efficiently', () => {
    const largeDataset = Array.from({ length: 1000 }, (_, i) => ({
      id: `item-${i}`,
      title: `Item ${i}`,
      content: `Content for item ${i}`,
      tags: [`tag-${i % 10}`],
      created_at: Date.now(),
      updated_at: Date.now(),
      timestamp: new Date().toISOString(),
      category: `category-${i % 5}`
    }));

    const props = {
      contentItems: largeDataset,
      tags: ['tag-0', 'tag-1', 'tag-2'],
      sources: mockDocumentSources,
      categories: ['category-0', 'category-1'],
      searchQuery: '',
      onContentUpdate: vi.fn(),
      onTagsUpdate: vi.fn(),
      onSourcesUpdate: vi.fn(),
      onCategoriesUpdate: vi.fn(),
      onSearchQueryChange: vi.fn()
    };

    const startTime = performance.now();
    render(<OptimizedContentManager {...props} />);
    const endTime = performance.now();

    // Should render within reasonable time (less than 100ms)
    expect(endTime - startTime).toBeLessThan(100);
  });

  it('should handle rapid state changes without performance issues', async () => {
    const user = userEvent.setup();
    const onSearchChange = vi.fn();
    
    const props = {
      contentItems: mockKnowledgeItems,
      tags: ['test'],
      sources: mockDocumentSources,
      categories: ['general'],
      searchQuery: '',
      onContentUpdate: vi.fn(),
      onTagsUpdate: vi.fn(),
      onSourcesUpdate: vi.fn(),
      onCategoriesUpdate: vi.fn(),
      onSearchQueryChange: onSearchChange
    };

    render(<OptimizedContentManager {...props} />);
    
    const searchInput = screen.getByPlaceholderText(/search/i);
    
    // Simulate rapid typing
    const startTime = performance.now();
    for (let i = 0; i < 10; i++) {
      await user.type(searchInput, 'a');
    }
    const endTime = performance.now();

    // Should handle rapid changes efficiently
    expect(endTime - startTime).toBeLessThan(1000);
    expect(onSearchChange).toHaveBeenCalled();
  });
});