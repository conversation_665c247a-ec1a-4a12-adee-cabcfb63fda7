import React from 'react';
import { render, screen, act, waitFor } from '@testing-library/react';
import { renderHook } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { LightbulbProvider, useLightbulb, LightbulbState, LightbulbAction } from '../../components/Lightbulb/context/LightbulbContext';
import { vi, describe, it, expect, beforeEach } from 'vitest';

// <PERSON>ck Tauri commands
vi.mock('../../components/Lightbulb/utils/tauriCommands', () => ({
  invokeTauriCommand: vi.fn().mockResolvedValue({}),
}));

// Mock services
vi.mock('../../components/Lightbulb/services/autoTaggingService', () => ({
  autoTaggingService: {
    suggestTags: vi.fn().mockResolvedValue([]),
  },
}));

vi.mock('../../components/Lightbulb/services/categorizationService', () => ({
  categorizationService: {
    categorizeContent: vi.fn().mockResolvedValue('general'),
  },
}));

vi.mock('../../components/Lightbulb/services/pagesService', () => ({
  pagesService: {
    getPages: vi.fn().mockResolvedValue([]),
  },
}));

vi.mock('../../components/Lightbulb/services/projectService', () => ({
  projectService: {
    getProjects: vi.fn().mockResolvedValue([]),
  },
}));

vi.mock('@/services/onboardingService', () => ({
  onboardingService: {
    completeOnboarding: vi.fn(),
  },
}));

vi.mock('@/services/feedbackService', () => ({
  feedbackService: {
    submitFeedback: vi.fn(),
  },
}));

vi.mock('@/services/performanceService', () => ({
  performanceService: {
    trackPerformance: vi.fn(),
  },
}));

// Test component to access context
const TestComponent: React.FC = () => {
  const { state, dispatch } = useLightbulb();
  return (
    <div>
      <div data-testid="active-section">{state.activeSection}</div>
      <div data-testid="loading">{state.loading.toString()}</div>
      <div data-testid="content-count">{state.contentItems.length}</div>
      <button 
        data-testid="set-loading" 
        onClick={() => dispatch({ type: 'SET_LOADING', payload: true })}
      >
        Set Loading
      </button>
    </div>
  );
};

describe('LightbulbContext', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Initial State', () => {
    it('should provide initial state values', () => {
      const { result } = renderHook(() => useLightbulb(), {
        wrapper: LightbulbProvider,
      });

      expect(result.current.state.activeSection).toBe('thoughts');
      expect(result.current.state.loading).toBe(false);
      expect(result.current.state.contentItems).toEqual([]);
      expect(result.current.state.userCount).toBe(0);
    });

    it('should throw error when used outside provider', () => {
      expect(() => {
        renderHook(() => useLightbulb());
      }).toThrow('useLightbulb must be used within a LightbulbProvider');
    });
  });

  describe('State Management', () => {
    it('should update active section', async () => {
      const { result } = renderHook(() => useLightbulb(), {
        wrapper: LightbulbProvider,
      });

      act(() => {
        result.current.dispatch({ type: 'SET_ACTIVE_SECTION', payload: 'chat' });
      });

      expect(result.current.state.activeSection).toBe('chat');
    });

    it('should update loading state', async () => {
      const { result } = renderHook(() => useLightbulb(), {
        wrapper: LightbulbProvider,
      });

      act(() => {
        result.current.dispatch({ type: 'SET_LOADING', payload: true });
      });

      expect(result.current.state.loading).toBe(true);
    });

    it('should add knowledge items', async () => {
      const { result } = renderHook(() => useLightbulb(), {
        wrapper: LightbulbProvider,
      });

      const mockItems: any[] = [
        { 
          id: '1', 
          title: 'Test Item', 
          content: 'Test content', 
          tags: [], 
          category: 'general',
          created_at: Date.now(),
          updated_at: Date.now(),
          timestamp: new Date().toISOString()
        },
      ];

      act(() => {
        result.current.dispatch({ type: 'SET_CONTENT_ITEMS', payload: mockItems });
      });

      expect(result.current.state.contentItems).toEqual(mockItems);
    });
  });

  describe('Reducer Logic', () => {
    it('should handle complex action sequences', async () => {
      const { result } = renderHook(() => useLightbulb(), {
        wrapper: LightbulbProvider,
      });

      // Sequence of actions
      act(() => {
        result.current.dispatch({ type: 'SET_LOADING', payload: true });
        result.current.dispatch({ type: 'SET_ACTIVE_SECTION', payload: 'search' });
        result.current.dispatch({ type: 'SET_SEARCH_QUERY', payload: 'test query' });
      });

      expect(result.current.state.loading).toBe(true);
      expect(result.current.state.activeSection).toBe('search');
      expect(result.current.state.searchQuery).toBe('test query');
    });

    it('should handle toggle actions', async () => {
      const { result } = renderHook(() => useLightbulb(), {
        wrapper: LightbulbProvider,
      });

      // Initial state
      expect(result.current.state.showQuickCapture).toBe(false);

      // Toggle on
      act(() => {
        result.current.dispatch({ type: 'TOGGLE_QUICK_CAPTURE' });
      });
      expect(result.current.state.showQuickCapture).toBe(true);

      // Toggle off
      act(() => {
        result.current.dispatch({ type: 'TOGGLE_QUICK_CAPTURE' });
      });
      expect(result.current.state.showQuickCapture).toBe(false);
    });

    it('should reset state', async () => {
      const { result } = renderHook(() => useLightbulb(), {
        wrapper: LightbulbProvider,
      });

      // Modify state
      act(() => {
        result.current.dispatch({ type: 'SET_LOADING', payload: true });
        result.current.dispatch({ type: 'SET_ACTIVE_SECTION', payload: 'chat' });
        result.current.dispatch({ type: 'SET_SEARCH_QUERY', payload: 'test' });
      });

      // Reset
      act(() => {
        result.current.dispatch({ type: 'RESET_STATE' });
      });

      expect(result.current.state.loading).toBe(false);
      expect(result.current.state.activeSection).toBe('thoughts');
      expect(result.current.state.searchQuery).toBe('');
    });
  });

  describe('Component Integration', () => {
    it('should work with React components', async () => {
      render(
        <LightbulbProvider>
          <TestComponent />
        </LightbulbProvider>
      );

      expect(screen.getByTestId('active-section').textContent).toBe('thoughts');
      expect(screen.getByTestId('loading').textContent).toBe('false');
      expect(screen.getByTestId('content-count').textContent).toBe('0');
    });

    it('should handle user interactions', async () => {
      const user = userEvent.setup();
      
      render(
        <LightbulbProvider>
          <TestComponent />
        </LightbulbProvider>
      );

      const loadingButton = screen.getByTestId('set-loading');
      await user.click(loadingButton);

      expect(screen.getByTestId('loading').textContent).toBe('true');
    });
  });

  describe('Performance', () => {
    it('should not cause unnecessary re-renders', async () => {
      const renderSpy = vi.fn();
      
      const SpyComponent: React.FC = () => {
        renderSpy();
        const { state } = useLightbulb();
        return <div>{state.activeSection}</div>;
      };

      const { rerender } = render(
        <LightbulbProvider>
          <SpyComponent />
        </LightbulbProvider>
      );

      expect(renderSpy).toHaveBeenCalledTimes(1);

      // Re-render with same props should not cause additional renders
      rerender(
        <LightbulbProvider>
          <SpyComponent />
        </LightbulbProvider>
      );

      expect(renderSpy).toHaveBeenCalledTimes(2); // Initial + rerender
    });
  });
});