import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import '@testing-library/jest-dom';
import { LightbulbProvider } from '../../components/Lightbulb/context/LightbulbContext';
import LightbulbComponent from '../../components/Lightbulb/LightbulbComponent';

// Mock Tauri commands
vi.mock('../../components/Lightbulb/utils/tauriCommands', () => ({
  invokeTauriCommand: vi.fn().mockImplementation((command, args) => {
    switch (command) {
      case 'get_user_count':
        return Promise.resolve({ count: 42 });
      case 'get_saved_content':
        return Promise.resolve([
          {
            id: '1',
            title: 'Test Content',
            content: 'This is test content',
            tags: ['test'],
            created_at: Date.now(),
            updated_at: Date.now(),
            timestamp: new Date().toISOString()
          }
        ]);
      case 'save_content':
        return Promise.resolve({ success: true });
      case 'chat_with_ai':
        return Promise.resolve({
          response: 'This is a test AI response',
          sources: []
        });
      case 'search_content':
        return Promise.resolve({
          results: [
            {
              id: '1',
              title: 'Search Result',
              content: 'Search result content',
              relevance: 0.9
            }
          ]
        });
      default:
        return Promise.resolve({});
    }
  }),
}));

// Mock services
vi.mock('../../components/Lightbulb/services/autoTaggingService', () => ({
  autoTaggingService: {
    suggestTags: vi.fn().mockResolvedValue([
      { tag: 'auto-tag', confidence: 0.8, category: 'topic' }
    ]),
  },
}));

vi.mock('../../components/Lightbulb/services/categorizationService', () => ({
  categorizationService: {
    categorizeContent: vi.fn().mockResolvedValue('general'),
  },
}));

vi.mock('../../components/Lightbulb/services/pagesService', () => ({
  pagesService: {
    getPages: vi.fn().mockResolvedValue([]),
  },
}));

vi.mock('../../components/Lightbulb/services/projectService', () => ({
  projectService: {
    getProjects: vi.fn().mockResolvedValue([]),
  },
}));

vi.mock('@/services/onboardingService', () => ({
  onboardingService: {
    completeOnboarding: vi.fn(),
  },
}));

vi.mock('@/services/feedbackService', () => ({
  feedbackService: {
    submitFeedback: vi.fn(),
  },
}));

vi.mock('@/services/performanceService', () => ({
  performanceService: {
    trackPerformance: vi.fn(),
  },
}));

// Mock framer-motion for testing
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    button: ({ children, ...props }: any) => <button {...props}>{children}</button>,
    span: ({ children, ...props }: any) => <span {...props}>{children}</span>,
  },
  AnimatePresence: ({ children }: any) => <>{children}</>,
}));

describe('Lightbulb Integration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Complete User Workflows', () => {
    it('should handle content creation workflow', async () => {
      const user = userEvent.setup();
      
      render(
        <LightbulbProvider>
          <LightbulbComponent />
        </LightbulbProvider>
      );

      // Wait for initial load
      await waitFor(() => {
        expect(screen.getByText(/lightbulb/i)).toBeDefined();
      });

      // Navigate to content section
      const thoughtsTab = screen.getByText(/thoughts/i);
      await user.click(thoughtsTab);

      // Add new content
      const contentInput = screen.getByPlaceholderText(/enter your thoughts/i);
      await user.type(contentInput, 'This is a new thought for testing');

      // Save content
      const saveButton = screen.getByRole('button', { name: /save/i });
      await user.click(saveButton);

      // Verify content was saved
      await waitFor(() => {
        expect(screen.getByText('This is a new thought for testing')).toBeDefined();
      });
    });

    it('should handle chat workflow', async () => {
      const user = userEvent.setup();
      
      render(
        <LightbulbProvider>
          <LightbulbComponent />
        </LightbulbProvider>
      );

      // Navigate to chat section
      const chatTab = screen.getByText(/chat/i);
      await user.click(chatTab);

      // Send a message
      const messageInput = screen.getByPlaceholderText(/type your message/i);
      await user.type(messageInput, 'Hello, can you help me?');

      const sendButton = screen.getByRole('button', { name: /send/i });
      await user.click(sendButton);

      // Verify message appears
      await waitFor(() => {
        expect(screen.getByText('Hello, can you help me?')).toBeDefined();
      });

      // Verify AI response appears
      await waitFor(() => {
        expect(screen.getByText('This is a test AI response')).toBeDefined();
      });
    });

    it('should handle search workflow', async () => {
      const user = userEvent.setup();
      
      render(
        <LightbulbProvider>
          <LightbulbComponent />
        </LightbulbProvider>
      );

      // Navigate to search section
      const searchTab = screen.getByText(/search/i);
      await user.click(searchTab);

      // Perform search
      const searchInput = screen.getByPlaceholderText(/search/i);
      await user.type(searchInput, 'test query');

      // Verify search results appear
      await waitFor(() => {
        expect(screen.getByText('Search Result')).toBeDefined();
      });
    });

    it('should handle settings workflow', async () => {
      const user = userEvent.setup();
      
      render(
        <LightbulbProvider>
          <LightbulbComponent />
        </LightbulbProvider>
      );

      // Open settings
      const settingsButton = screen.getByRole('button', { name: /settings/i });
      await user.click(settingsButton);

      // Verify settings panel opens
      await waitFor(() => {
        expect(screen.getByText(/preferences/i)).toBeDefined();
      });

      // Change a setting
      const notificationToggle = screen.getByLabelText(/notifications/i);
      await user.click(notificationToggle);

      // Close settings
      const closeButton = screen.getByRole('button', { name: /close/i });
      await user.click(closeButton);
    });
  });

  describe('Cross-Component Integration', () => {
    it('should share state between components', async () => {
      const user = userEvent.setup();
      
      render(
        <LightbulbProvider>
          <LightbulbComponent />
        </LightbulbProvider>
      );

      // Add content in thoughts section
      const thoughtsTab = screen.getByText(/thoughts/i);
      await user.click(thoughtsTab);

      const contentInput = screen.getByPlaceholderText(/enter your thoughts/i);
      await user.type(contentInput, 'Shared content test');

      const saveButton = screen.getByRole('button', { name: /save/i });
      await user.click(saveButton);

      // Switch to search and verify content is searchable
      const searchTab = screen.getByText(/search/i);
      await user.click(searchTab);

      const searchInput = screen.getByPlaceholderText(/search/i);
      await user.type(searchInput, 'Shared content');

      // Content should be found in search
      await waitFor(() => {
        expect(screen.getByText(/shared content/i)).toBeDefined();
      });
    });

    it('should handle loading states across components', async () => {
      const user = userEvent.setup();
      
      render(
        <LightbulbProvider>
          <LightbulbComponent />
        </LightbulbProvider>
      );

      // Trigger an action that causes loading
      const chatTab = screen.getByText(/chat/i);
      await user.click(chatTab);

      const messageInput = screen.getByPlaceholderText(/type your message/i);
      await user.type(messageInput, 'Test message');

      const sendButton = screen.getByRole('button', { name: /send/i });
      await user.click(sendButton);

      // Should show loading state briefly
      // Note: This might be too fast to catch in tests, but the structure should support it
      expect(sendButton).toBeDefined();
    });
  });

  describe('Error Handling Integration', () => {
    it('should handle API errors gracefully', async () => {
      // Mock a failing API call
      const mockInvoke = vi.fn().mockRejectedValue(new Error('API Error'));
      vi.mocked(require('../../components/Lightbulb/utils/tauriCommands').invokeTauriCommand)
        .mockImplementation(mockInvoke);

      const user = userEvent.setup();
      
      render(
        <LightbulbProvider>
          <LightbulbComponent />
        </LightbulbProvider>
      );

      // Try to perform an action that would fail
      const thoughtsTab = screen.getByText(/thoughts/i);
      await user.click(thoughtsTab);

      const contentInput = screen.getByPlaceholderText(/enter your thoughts/i);
      await user.type(contentInput, 'This will fail to save');

      const saveButton = screen.getByRole('button', { name: /save/i });
      await user.click(saveButton);

      // Should handle error gracefully (not crash)
      await waitFor(() => {
        expect(screen.getByText(/thoughts/i)).toBeDefined();
      });
    });

    it('should recover from errors', async () => {
      const user = userEvent.setup();
      
      render(
        <LightbulbProvider>
          <LightbulbComponent />
        </LightbulbProvider>
      );

      // Component should still be functional after error
      const searchTab = screen.getByText(/search/i);
      await user.click(searchTab);

      expect(screen.getByPlaceholderText(/search/i)).toBeDefined();
    });
  });

  describe('Performance Integration', () => {
    it('should handle rapid user interactions', async () => {
      const user = userEvent.setup();
      
      render(
        <LightbulbProvider>
          <LightbulbComponent />
        </LightbulbProvider>
      );

      // Rapidly switch between tabs
      const thoughtsTab = screen.getByText(/thoughts/i);
      const chatTab = screen.getByText(/chat/i);
      const searchTab = screen.getByText(/search/i);

      for (let i = 0; i < 5; i++) {
        await user.click(thoughtsTab);
        await user.click(chatTab);
        await user.click(searchTab);
      }

      // Should still be responsive
      expect(screen.getByText(/search/i)).toBeDefined();
    });

    it('should handle large amounts of data', async () => {
      // Mock large dataset
      const largeDataset = Array.from({ length: 100 }, (_, i) => ({
        id: `item-${i}`,
        title: `Item ${i}`,
        content: `Content ${i}`,
        tags: [`tag-${i}`],
        created_at: Date.now(),
        updated_at: Date.now(),
        timestamp: new Date().toISOString()
      }));

      vi.mocked(require('../../components/Lightbulb/utils/tauriCommands').invokeTauriCommand)
        .mockImplementation((command) => {
          if (command === 'get_saved_content') {
            return Promise.resolve(largeDataset);
          }
          return Promise.resolve({});
        });

      const startTime = performance.now();
      
      render(
        <LightbulbProvider>
          <LightbulbComponent />
        </LightbulbProvider>
      );

      await waitFor(() => {
        expect(screen.getByText(/lightbulb/i)).toBeDefined();
      });

      const endTime = performance.now();
      
      // Should render within reasonable time
      expect(endTime - startTime).toBeLessThan(2000);
    });
  });
});