/**
 * Agent Analytics Types
 * Comprehensive type definitions for agent performance tracking and analytics
 */

// Performance Metrics
export interface AgentPerformanceMetrics {
  agentId: string;
  agentName: string;
  agentType: string;
  totalExecutions: number;
  successfulExecutions: number;
  failedExecutions: number;
  averageExecutionTime: number;
  medianExecutionTime: number;
  p95ExecutionTime: number;
  p99ExecutionTime: number;
  successRate: number;
  errorRate: number;
  timeoutRate: number;
  averageTokensUsed: number;
  totalTokensUsed: number;
  averageCost: number;
  totalCost: number;
  lastExecutionTime: string;
  peakUsageHour: number;
  peakUsageDay: string;
}

// Session Analytics
export interface SessionAnalytics {
  sessionId: string;
  projectPath: string;
  startTime: string;
  endTime?: string;
  duration: number;
  totalMessages: number;
  userMessages: number;
  assistantMessages: number;
  agentExecutions: number;
  uniqueAgentsUsed: number;
  totalTokensUsed: number;
  inputTokens: number;
  outputTokens: number;
  totalCost: number;
  checkpointsCreated: number;
  errorsEncountered: number;
  averageResponseTime: number;
  userSatisfactionScore?: number;
  completionRate: number;
  agentUtilizationRate: number;
}

// Agent Execution Analytics
export interface AgentExecutionAnalytics {
  executionId: string;
  agentId: string;
  sessionId: string;
  startTime: string;
  endTime: string;
  duration: number;
  status: 'success' | 'failure' | 'timeout' | 'cancelled';
  tokensUsed: number;
  cost: number;
  errorMessage?: string;
  retryCount: number;
  contextSize: number;
  outputSize: number;
  memoryUsage: number;
  cpuUsage: number;
  dependencies: string[];
  triggeredBy: 'user' | 'system' | 'workflow' | 'auto-detect';
}

// Workflow Analytics
export interface WorkflowAnalytics {
  workflowId: string;
  workflowName: string;
  sessionId: string;
  startTime: string;
  endTime?: string;
  duration: number;
  status: 'running' | 'completed' | 'failed' | 'cancelled';
  totalTasks: number;
  completedTasks: number;
  failedTasks: number;
  parallelExecutions: number;
  sequentialExecutions: number;
  totalTokensUsed: number;
  totalCost: number;
  criticalPath: string[];
  bottlenecks: string[];
  averageTaskDuration: number;
  longestTask: {
    taskId: string;
    duration: number;
  };
}

// Pattern Recognition
export interface UsagePattern {
  patternId: string;
  patternType: 'agent_sequence' | 'time_based' | 'error_recovery' | 'workflow_preference';
  frequency: number;
  confidence: number;
  description: string;
  agents: string[];
  timeRange?: {
    start: string;
    end: string;
  };
  contexts: string[];
  recommendations: string[];
}

// ML Insights
export interface MLInsight {
  insightId: string;
  type: 'optimization' | 'anomaly' | 'prediction' | 'recommendation';
  severity: 'info' | 'warning' | 'critical';
  confidence: number;
  title: string;
  description: string;
  evidence: {
    metric: string;
    value: number;
    threshold: number;
    trend: 'increasing' | 'decreasing' | 'stable';
  }[];
  recommendations: {
    action: string;
    impact: 'high' | 'medium' | 'low';
    effort: 'high' | 'medium' | 'low';
    estimatedImprovement: number;
  }[];
  relatedAgents: string[];
  relatedSessions: string[];
  generatedAt: string;
  validUntil: string;
}

// Cost Analytics
export interface CostAnalytics {
  period: 'hour' | 'day' | 'week' | 'month';
  startDate: string;
  endDate: string;
  totalCost: number;
  breakdown: {
    byAgent: Record<string, number>;
    bySession: Record<string, number>;
    byProject: Record<string, number>;
    byModel: Record<string, number>;
  };
  projectedCost: number;
  costTrend: 'increasing' | 'decreasing' | 'stable';
  savingsOpportunities: {
    opportunity: string;
    potentialSavings: number;
    implementation: string;
  }[];
  budgetUtilization: number;
  budgetRemaining: number;
}

// Performance Trends
export interface PerformanceTrend {
  metric: string;
  period: 'hour' | 'day' | 'week' | 'month';
  dataPoints: {
    timestamp: string;
    value: number;
  }[];
  average: number;
  median: number;
  min: number;
  max: number;
  trend: 'improving' | 'degrading' | 'stable';
  changePercent: number;
  forecast: {
    nextPeriod: number;
    confidence: number;
  };
}

// Agent Recommendations
export interface AgentRecommendation {
  context: string;
  recommendedAgent: string;
  confidence: number;
  reasoning: string;
  alternativeAgents: {
    agentId: string;
    confidence: number;
  }[];
  historicalSuccess: number;
  estimatedDuration: number;
  estimatedCost: number;
}

// Analytics Dashboard State
export interface AnalyticsDashboardState {
  timeRange: {
    start: string;
    end: string;
    preset?: 'last_hour' | 'last_24h' | 'last_7d' | 'last_30d' | 'custom';
  };
  selectedMetrics: string[];
  selectedAgents: string[];
  selectedSessions: string[];
  groupBy: 'agent' | 'session' | 'project' | 'time';
  aggregation: 'sum' | 'average' | 'median' | 'max' | 'min';
  refreshInterval: number;
  autoRefresh: boolean;
}

// Real-time Metrics
export interface RealTimeMetrics {
  timestamp: string;
  activeAgents: number;
  activeSessions: number;
  queuedTasks: number;
  executingTasks: number;
  tokensPerSecond: number;
  errorsPerMinute: number;
  averageLatency: number;
  systemLoad: number;
  memoryUsage: number;
  cacheHitRate: number;
}

// Analytics Export Format
export interface AnalyticsExport {
  exportId: string;
  format: 'json' | 'csv' | 'excel' | 'pdf';
  dateRange: {
    start: string;
    end: string;
  };
  includedMetrics: string[];
  includedAgents: string[];
  aggregationLevel: 'raw' | 'hourly' | 'daily' | 'weekly' | 'monthly';
  generatedAt: string;
  fileSize: number;
  downloadUrl: string;
}

// Anomaly Detection
export interface Anomaly {
  anomalyId: string;
  detectedAt: string;
  type: 'performance' | 'cost' | 'error_rate' | 'usage_pattern';
  severity: 'low' | 'medium' | 'high' | 'critical';
  metric: string;
  expectedValue: number;
  actualValue: number;
  deviation: number;
  standardDeviations: number;
  affectedAgents: string[];
  affectedSessions: string[];
  possibleCauses: string[];
  suggestedActions: string[];
  autoResolved: boolean;
  resolvedAt?: string;
}

// Benchmark Comparison
export interface BenchmarkComparison {
  agentId: string;
  metric: string;
  yourValue: number;
  benchmarkValue: number;
  percentile: number;
  performance: 'below' | 'average' | 'above';
  improvementPotential: number;
  bestPractices: string[];
}

// Analytics Configuration
export interface AnalyticsConfig {
  enabled: boolean;
  collectDetailedMetrics: boolean;
  retentionDays: number;
  aggregationIntervals: string[];
  alertThresholds: {
    errorRate: number;
    costPerHour: number;
    responseTime: number;
    tokenUsage: number;
  };
  exportSchedule?: {
    enabled: boolean;
    frequency: 'daily' | 'weekly' | 'monthly';
    recipients: string[];
    format: 'json' | 'csv' | 'pdf';
  };
  mlInsights: {
    enabled: boolean;
    modelUpdateFrequency: 'hourly' | 'daily' | 'weekly';
    minConfidenceThreshold: number;
  };
}