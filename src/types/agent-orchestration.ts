/**
 * Agent Orchestration Types
 * Defines structures for multi-agent collaboration within Claude sessions
 */

export interface AgentTask {
  id: string;
  agentId: number;
  agentName: string;
  task: string;
  dependencies: string[]; // Task IDs this depends on
  status: 'pending' | 'running' | 'completed' | 'failed';
  result?: any;
  error?: string;
  startTime?: Date;
  endTime?: Date;
  retryCount: number;
  maxRetries: number;
}

export interface AgentWorkflow {
  id: string;
  name: string;
  description: string;
  tasks: AgentTask[];
  executionMode: 'sequential' | 'parallel' | 'mixed';
  status: 'idle' | 'running' | 'completed' | 'failed' | 'cancelled';
  createdAt: Date;
  startedAt?: Date;
  completedAt?: Date;
  sessionId?: string; // Link to Claude session
  context?: WorkflowContext;
}

export interface WorkflowContext {
  sessionId: string;
  projectPath: string;
  messages: any[];
  variables: Record<string, any>;
  sharedMemory: Record<string, any>; // Shared between agents
}

export interface AgentChain {
  id: string;
  agents: ChainedAgent[];
  dataFlow: DataFlowRule[];
  errorHandling: ErrorStrategy;
}

export interface ChainedAgent {
  agentId: number;
  agentName: string;
  inputMapping: Record<string, string>; // Maps previous outputs to inputs
  outputKeys: string[]; // Keys this agent will produce
  condition?: ExecutionCondition; // Optional condition to run
}

export interface DataFlowRule {
  from: string; // Agent ID or 'input'
  to: string; // Agent ID or 'output'
  mapping: Record<string, string>;
  transform?: (data: any) => any;
}

export interface ExecutionCondition {
  type: 'always' | 'if_success' | 'if_failure' | 'custom';
  customCondition?: (context: WorkflowContext, previousResults: any[]) => boolean;
}

export interface ErrorStrategy {
  type: 'fail_fast' | 'continue' | 'retry' | 'fallback';
  maxRetries?: number;
  fallbackAgentId?: number;
  retryDelay?: number;
}

export interface AgentOrchestrationResult {
  workflowId: string;
  success: boolean;
  tasks: AgentTask[];
  aggregatedResults: Record<string, any>;
  errors: Array<{ taskId: string; error: string }>;
  executionTime: number;
  metrics: OrchestrationMetrics;
}

export interface OrchestrationMetrics {
  totalTasks: number;
  completedTasks: number;
  failedTasks: number;
  averageTaskTime: number;
  totalTokensUsed: number;
  parallelExecutions: number;
}

// Workflow Templates
export interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  category: 'development' | 'testing' | 'documentation' | 'analysis' | 'custom';
  agents: Array<{
    role: string;
    suggestedAgent?: string;
    required: boolean;
  }>;
  executionPattern: string; // e.g., "analyze -> test -> document"
  defaultContext?: Partial<WorkflowContext>;
  tags: string[];
}

export const BUILT_IN_WORKFLOWS: WorkflowTemplate[] = [
  {
    id: 'test-and-fix',
    name: 'Test and Fix',
    description: 'Run tests, analyze failures, and fix issues',
    category: 'testing',
    agents: [
      { role: 'tester', suggestedAgent: 'test-runner', required: true },
      { role: 'analyzer', suggestedAgent: 'error-analyzer', required: true },
      { role: 'fixer', suggestedAgent: 'code-fixer', required: true }
    ],
    executionPattern: 'tester -> analyzer -> fixer',
    tags: ['testing', 'automation', 'quality']
  },
  {
    id: 'code-review-refactor',
    name: 'Review and Refactor',
    description: 'Review code, suggest improvements, and refactor',
    category: 'development',
    agents: [
      { role: 'reviewer', suggestedAgent: 'code-reviewer', required: true },
      { role: 'architect', suggestedAgent: 'architecture-analyzer', required: false },
      { role: 'refactorer', suggestedAgent: 'code-refactorer', required: true }
    ],
    executionPattern: 'reviewer + architect -> refactorer',
    tags: ['code-quality', 'refactoring', 'review']
  },
  {
    id: 'document-and-test',
    name: 'Document and Test',
    description: 'Generate documentation and create tests',
    category: 'documentation',
    agents: [
      { role: 'documenter', suggestedAgent: 'doc-generator', required: true },
      { role: 'test-writer', suggestedAgent: 'test-generator', required: true },
      { role: 'validator', suggestedAgent: 'doc-validator', required: false }
    ],
    executionPattern: 'documenter || test-writer -> validator',
    tags: ['documentation', 'testing', 'quality']
  },
  {
    id: 'security-audit',
    name: 'Security Audit',
    description: 'Scan for vulnerabilities and suggest fixes',
    category: 'analysis',
    agents: [
      { role: 'scanner', suggestedAgent: 'security-scanner', required: true },
      { role: 'analyzer', suggestedAgent: 'vulnerability-analyzer', required: true },
      { role: 'reporter', suggestedAgent: 'report-generator', required: true }
    ],
    executionPattern: 'scanner -> analyzer -> reporter',
    tags: ['security', 'audit', 'compliance']
  }
];

// Helper functions for workflow management
export class WorkflowBuilder {
  private workflow: Partial<AgentWorkflow> = {
    tasks: [],
    executionMode: 'sequential',
    status: 'idle'
  };

  setName(name: string): this {
    this.workflow.name = name;
    return this;
  }

  setDescription(description: string): this {
    this.workflow.description = description;
    return this;
  }

  addTask(task: Partial<AgentTask>): this {
    const fullTask: AgentTask = {
      id: `task-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      dependencies: [],
      status: 'pending',
      retryCount: 0,
      maxRetries: 3,
      ...task
    } as AgentTask;
    
    this.workflow.tasks!.push(fullTask);
    return this;
  }

  addParallelTasks(tasks: Partial<AgentTask>[]): this {
    tasks.forEach(task => this.addTask(task));
    this.workflow.executionMode = 'mixed';
    return this;
  }

  setExecutionMode(mode: 'sequential' | 'parallel' | 'mixed'): this {
    this.workflow.executionMode = mode;
    return this;
  }

  setContext(context: WorkflowContext): this {
    this.workflow.context = context;
    return this;
  }

  build(): AgentWorkflow {
    if (!this.workflow.name) {
      throw new Error('Workflow name is required');
    }
    
    return {
      id: `workflow-${Date.now()}`,
      name: this.workflow.name,
      description: this.workflow.description || '',
      tasks: this.workflow.tasks || [],
      executionMode: this.workflow.executionMode!,
      status: 'idle',
      createdAt: new Date(),
      context: this.workflow.context
    };
  }
}

// Execution engine interface
export interface IAgentOrchestrator {
  executeWorkflow(workflow: AgentWorkflow): Promise<AgentOrchestrationResult>;
  executeChain(chain: AgentChain, input: any): Promise<any>;
  cancelWorkflow(workflowId: string): Promise<void>;
  getWorkflowStatus(workflowId: string): Promise<AgentWorkflow>;
  retryTask(workflowId: string, taskId: string): Promise<void>;
}