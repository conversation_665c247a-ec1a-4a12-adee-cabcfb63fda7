/**
 * Centralized session types used across the application
 * This file consolidates previously duplicated interfaces
 */

/**
 * Represents a message in the Claude streaming output
 * Previously duplicated in outputCache.tsx, AgentExecution.tsx, and SessionOutputViewer.tsx
 */
export interface ClaudeStreamMessage {
  type: "system" | "assistant" | "user" | "result";
  subtype?: string;
  message?: {
    content?: any[];
    usage?: {
      input_tokens: number;
      output_tokens: number;
    };
  };
  usage?: {
    input_tokens: number;
    output_tokens: number;
  };
  result?: {
    exit_code: number;
    message: string;
  } | string; // Can be either an object or a string
  timestamp?: string;
  session_id?: string;
  checkpoint_id?: string;
  
  // Agent-specific fields
  isMeta?: boolean;
  leafUuid?: string;
  summary?: string;
  model?: string;
  is_error?: boolean;
  duration_ms?: number;
  
  // Allow additional properties for flexibility
  [key: string]: any;
}

/**
 * Session metadata and state
 */
export interface SessionMetadata {
  id: string;
  project_id: string;
  project_path: string;
  created_at: number;
  first_message?: string;
  message_timestamp?: string;
  todo_data?: any;
}

/**
 * Session metrics for tracking performance and usage
 */
export interface SessionMetrics {
  firstMessageTime: number | null;
  promptsSent: number;
  toolsExecuted: number;
  toolsFailed: number;
  filesCreated: number;
  filesModified: number;
  filesDeleted: number;
  codeBlocksGenerated: number;
  errorsEncountered: number;
  lastActivityTime: number;
  toolExecutionTimes: number[];
  checkpointCount: number;
  wasResumed: boolean;
  modelChanges: Array<{ from: string; to: string; timestamp: number }>;
}

/**
 * Error details for session error handling
 */
export interface SessionErrorDetails {
  code: string;
  message: string;
  timestamp: number;
  recoverable: boolean;
  context?: any;
}

/**
 * Session state for UI components
 */
export interface SessionUIState {
  isLoading: boolean;
  error: string | null;
  showTimeline: boolean;
  showSettings: boolean;
  showForkDialog: boolean;
  showSlashCommandsSettings: boolean;
  showSuperClaude: boolean;
  isInBackground: boolean;
  copyPopoverOpen: boolean;
}

/**
 * Checkpoint information
 */
export interface SessionCheckpoint {
  id: string;
  session_id: string;
  created_at: number;
  message_count: number;
  description?: string;
  significance: 'minor' | 'major' | 'critical';
}

/**
 * Event payload types for Tauri events
 */
export interface ClaudeOutputEvent {
  payload: string;
}

export interface ClaudeErrorEvent {
  payload: string;
}

export interface ClaudeCompleteEvent {
  payload: boolean;
}

/**
 * Session command parameters
 */
export interface ExecuteClaudeCodeParams {
  projectPath: string;
  prompt: string;
  model: string;
}

export interface ContinueClaudeCodeParams {
  projectPath: string;
  prompt: string;
  model: string;
}

export interface ResumeClaudeCodeParams {
  projectPath: string;
  sessionId: string;
  prompt: string;
  model: string;
}

export interface CancelClaudeExecutionParams {
  sessionId?: string;
}

/**
 * Session event types for type-safe event handling
 */
export enum SessionEventType {
  OUTPUT = 'claude-output',
  ERROR = 'claude-error',
  COMPLETE = 'claude-complete',
  CHECKPOINT = 'claude-checkpoint',
  METRICS = 'claude-metrics'
}

/**
 * Session event channel builder
 */
export function getSessionEventChannel(eventType: SessionEventType, sessionId?: string): string {
  return sessionId ? `${eventType}:${sessionId}` : eventType;
}