/**
 * Advanced Workflow System Type Definitions
 * Provides comprehensive types for workflow creation, execution, and optimization
 */

export type AdvancedNodeType = 
  | 'start'
  | 'end'
  | 'agent'
  | 'condition'
  | 'loop'
  | 'parallel'
  | 'sequential'
  | 'transform'
  | 'validation'
  | 'integration'
  | 'human-review'
  | 'cache'
  | 'error-handler'
  | 'ai-decision'
  | 'data-aggregator'
  | 'scheduler';

export type WorkflowStatus = 
  | 'draft'
  | 'active'
  | 'running'
  | 'paused'
  | 'completed'
  | 'failed'
  | 'archived'
  | 'optimizing';

export interface AdvancedWorkflowNode {
  id: string;
  type: AdvancedNodeType;
  name: string;
  description?: string;
  position: { x: number; y: number };
  config: NodeConfiguration;
  inputs: WorkflowPort[];
  outputs: WorkflowPort[];
  metadata: NodeMetadata;
  performance?: NodePerformance;
}

export interface NodeConfiguration {
  agentId?: string;
  condition?: ConditionExpression;
  loopConfig?: LoopConfiguration;
  parallelConfig?: ParallelConfiguration;
  transformConfig?: TransformConfiguration;
  validationRules?: ValidationRule[];
  integrationConfig?: IntegrationConfig;
  aiConfig?: AIConfiguration;
  cacheConfig?: CacheConfiguration;
  errorHandling?: ErrorHandlingConfig;
  [key: string]: any;
}

export interface WorkflowPort {
  id: string;
  name: string;
  type: 'input' | 'output';
  dataType: DataType;
  required: boolean;
  multiple: boolean;
  validation?: ValidationRule[];
  transform?: TransformExpression;
}

export interface AdvancedWorkflowEdge {
  id: string;
  source: string;
  sourcePort: string;
  target: string;
  targetPort: string;
  condition?: ConditionExpression;
  transform?: TransformExpression;
  priority?: number;
  metadata?: {
    label?: string;
    color?: string;
    animated?: boolean;
  };
}

export interface AdvancedWorkflow {
  id: string;
  name: string;
  description: string;
  version: string;
  status: WorkflowStatus;
  nodes: AdvancedWorkflowNode[];
  edges: AdvancedWorkflowEdge[];
  variables: WorkflowVariable[];
  triggers: WorkflowTrigger[];
  settings: WorkflowSettings;
  optimization?: WorkflowOptimization;
  analytics?: WorkflowAnalytics;
  metadata: WorkflowMetadata;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  tags: string[];
}

export interface WorkflowVariable {
  id: string;
  name: string;
  type: DataType;
  defaultValue?: any;
  scope: 'global' | 'local' | 'session';
  encrypted?: boolean;
  source?: 'user' | 'system' | 'environment' | 'computed';
}

export interface WorkflowTrigger {
  id: string;
  type: TriggerType;
  enabled: boolean;
  config: TriggerConfiguration;
  conditions?: ConditionExpression[];
}

export type TriggerType = 
  | 'manual'
  | 'schedule'
  | 'webhook'
  | 'event'
  | 'file'
  | 'email'
  | 'api'
  | 'database'
  | 'message-queue';

export interface TriggerConfiguration {
  schedule?: CronExpression;
  webhookUrl?: string;
  eventPattern?: string;
  filePath?: string;
  emailPattern?: string;
  apiEndpoint?: string;
  databaseQuery?: string;
  queueName?: string;
  [key: string]: any;
}

export interface WorkflowSettings {
  timeout?: number;
  maxRetries?: number;
  parallelism?: number;
  errorHandling?: ErrorHandlingStrategy;
  notifications?: NotificationConfig[];
  resourceLimits?: ResourceLimits;
  optimization?: OptimizationSettings;
  monitoring?: MonitoringSettings;
  security?: SecuritySettings;
}

export interface WorkflowMetadata {
  category?: string;
  department?: string;
  owner?: string;
  sla?: number;
  cost?: CostEstimate;
  performance?: PerformanceMetrics;
  dependencies?: string[];
  compliance?: ComplianceInfo[];
  documentation?: string;
}

export interface NodeMetadata {
  icon?: string;
  color?: string;
  estimatedDuration?: number;
  cost?: number;
  retryPolicy?: RetryPolicy;
  monitoring?: boolean;
  cacheable?: boolean;
}

export interface NodePerformance {
  avgDuration: number;
  p95Duration: number;
  successRate: number;
  errorRate: number;
  lastExecution?: string;
}

export interface LoopConfiguration {
  type: 'for' | 'while' | 'foreach';
  condition?: ConditionExpression;
  maxIterations?: number;
  collection?: string;
  breakCondition?: ConditionExpression;
}

export interface ParallelConfiguration {
  maxConcurrency?: number;
  waitForAll?: boolean;
  failFast?: boolean;
  aggregation?: 'merge' | 'array' | 'custom';
}

export interface TransformConfiguration {
  type: 'map' | 'filter' | 'reduce' | 'custom';
  expression: string;
  language?: 'javascript' | 'python' | 'jq';
}

export interface IntegrationConfig {
  service: string;
  method: string;
  authentication?: AuthenticationConfig;
  headers?: Record<string, string>;
  timeout?: number;
  retryPolicy?: RetryPolicy;
}

export interface AIConfiguration {
  model: string;
  prompt?: string;
  temperature?: number;
  maxTokens?: number;
  systemPrompt?: string;
  responseFormat?: 'text' | 'json' | 'structured';
}

export interface CacheConfiguration {
  ttl?: number;
  key?: string;
  invalidation?: CacheInvalidation[];
}

export interface ErrorHandlingConfig {
  strategy: ErrorHandlingStrategy;
  fallbackNode?: string;
  retryPolicy?: RetryPolicy;
  alerting?: AlertingConfig;
}

export interface WorkflowOptimization {
  id: string;
  status: 'analyzing' | 'ready' | 'applied' | 'failed';
  suggestions: OptimizationSuggestion[];
  estimatedImprovement: {
    performance?: number;
    cost?: number;
    reliability?: number;
  };
  appliedAt?: string;
  results?: OptimizationResults;
}

export interface OptimizationSuggestion {
  id: string;
  type: OptimizationType;
  description: string;
  impact: 'high' | 'medium' | 'low';
  effort: 'high' | 'medium' | 'low';
  changes: WorkflowChange[];
  estimatedBenefit: Record<string, number>;
  risks?: string[];
}

export type OptimizationType = 
  | 'parallelization'
  | 'caching'
  | 'pruning'
  | 'consolidation'
  | 'reordering'
  | 'batching'
  | 'circuit-breaking';

export interface WorkflowChange {
  type: 'add' | 'remove' | 'modify' | 'reorder';
  target: 'node' | 'edge' | 'variable' | 'setting';
  targetId?: string;
  before?: any;
  after?: any;
  reason?: string;
}

export interface WorkflowAnalytics {
  workflowId: string;
  period: DateRange;
  executions: ExecutionStats;
  performance: PerformanceStats;
  cost: CostStats;
  bottlenecks: Bottleneck[];
  trends: TrendAnalysis;
  predictions: Prediction[];
}

export interface ExecutionStats {
  total: number;
  successful: number;
  failed: number;
  cancelled: number;
  avgDuration: number;
  p50Duration: number;
  p95Duration: number;
  p99Duration: number;
}

export interface PerformanceStats {
  throughput: number;
  errorRate: number;
  availability: number;
  mttr: number;
  mtbf: number;
}

export interface CostStats {
  total: number;
  perExecution: number;
  byNode: Record<string, number>;
  byResource: Record<string, number>;
  trend: TrendDirection;
}

export interface Bottleneck {
  nodeId: string;
  nodeName: string;
  avgDuration: number;
  frequency: number;
  impact: number;
  suggestions: string[];
}

export interface TrendAnalysis {
  executions: TrendData;
  performance: TrendData;
  cost: TrendData;
  errors: TrendData;
}

export interface TrendData {
  direction: TrendDirection;
  change: number;
  forecast?: number[];
}

export type TrendDirection = 'up' | 'down' | 'stable';

export interface Prediction {
  type: 'failure' | 'bottleneck' | 'cost-spike' | 'sla-breach';
  probability: number;
  timeframe: string;
  factors: string[];
  mitigation?: string;
}

// Supporting Types

export type DataType = 
  | 'string'
  | 'number'
  | 'boolean'
  | 'object'
  | 'array'
  | 'file'
  | 'date'
  | 'binary'
  | 'stream'
  | 'any';

export interface ValidationRule {
  type: 'required' | 'pattern' | 'range' | 'length' | 'type' | 'custom';
  value?: any;
  message?: string;
  severity?: 'error' | 'warning';
}

export interface ConditionExpression {
  type: 'simple' | 'complex' | 'script' | 'ai';
  operator?: ComparisonOperator;
  left?: string;
  right?: any;
  expression?: string;
  script?: string;
}

export type ComparisonOperator = 
  | 'equals'
  | 'not_equals'
  | 'greater'
  | 'less'
  | 'greater_or_equal'
  | 'less_or_equal'
  | 'contains'
  | 'not_contains'
  | 'matches'
  | 'in'
  | 'not_in';

export interface TransformExpression {
  type: 'map' | 'filter' | 'reduce' | 'aggregate' | 'custom';
  expression: string;
  language?: 'javascript' | 'python' | 'jq';
}

export interface RetryPolicy {
  maxAttempts: number;
  backoff: BackoffStrategy;
  delay: number;
  maxDelay?: number;
  retryableErrors?: string[];
}

export type BackoffStrategy = 'fixed' | 'exponential' | 'linear' | 'fibonacci';

export type ErrorHandlingStrategy = 
  | 'fail-fast'
  | 'continue'
  | 'retry'
  | 'fallback'
  | 'compensate'
  | 'circuit-break';

export interface NotificationConfig {
  channel: NotificationChannel;
  events: NotificationEvent[];
  recipients: string[];
  template?: string;
  throttle?: number;
}

export type NotificationChannel = 
  | 'email'
  | 'slack'
  | 'teams'
  | 'webhook'
  | 'sms'
  | 'in-app';

export type NotificationEvent = 
  | 'start'
  | 'success'
  | 'failure'
  | 'warning'
  | 'sla-breach'
  | 'cost-alert';

export interface ResourceLimits {
  cpu?: number;
  memory?: number;
  timeout?: number;
  concurrency?: number;
  rate?: RateLimit;
}

export interface RateLimit {
  requests: number;
  window: number;
  strategy: 'sliding' | 'fixed';
}

export interface OptimizationSettings {
  enableCaching?: boolean;
  enableParallelization?: boolean;
  enablePruning?: boolean;
  enableBatching?: boolean;
  costThreshold?: number;
  performanceTarget?: number;
  autoOptimize?: boolean;
}

export interface MonitoringSettings {
  enabled: boolean;
  metrics?: string[];
  logging?: LoggingConfig;
  tracing?: TracingConfig;
  profiling?: boolean;
}

export interface SecuritySettings {
  encryption?: EncryptionConfig;
  authentication?: AuthenticationConfig;
  authorization?: AuthorizationConfig;
  audit?: AuditConfig;
}

export interface ComplianceInfo {
  standard: string;
  requirements: string[];
  validated: boolean;
  validatedAt?: string;
}

export interface CacheInvalidation {
  trigger: 'time' | 'event' | 'dependency';
  value?: any;
}

export interface AlertingConfig {
  enabled: boolean;
  channels: NotificationChannel[];
  severity: 'critical' | 'high' | 'medium' | 'low';
}

export interface AuthenticationConfig {
  type: 'basic' | 'bearer' | 'oauth2' | 'api-key' | 'certificate';
  credentials?: Record<string, string>;
}

export interface DateRange {
  start: string;
  end: string;
}

export interface CronExpression {
  expression: string;
  timezone?: string;
}

export interface LoggingConfig {
  level: 'debug' | 'info' | 'warning' | 'error';
  destination: string;
  format: 'json' | 'text';
}

export interface TracingConfig {
  enabled: boolean;
  samplingRate: number;
  propagation: string[];
}

export interface EncryptionConfig {
  algorithm: string;
  keyId: string;
}

export interface AuthorizationConfig {
  roles?: string[];
  permissions?: string[];
}

export interface AuditConfig {
  enabled: boolean;
  events: string[];
  retention: number;
}

export interface CostEstimate {
  fixed: number;
  variable: number;
  currency: string;
}

export interface PerformanceMetrics {
  avgDuration: number;
  p95Duration: number;
  successRate: number;
  throughput: number;
}

export interface OptimizationResults {
  before: ExecutionMetrics;
  after: ExecutionMetrics;
  improvement: Record<string, number>;
}

export interface ExecutionMetrics {
  duration: number;
  cost: number;
  tokenUsage?: number;
  resourceUsage?: Record<string, number>;
}