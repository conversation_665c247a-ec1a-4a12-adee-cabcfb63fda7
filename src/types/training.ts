/**
 * TypeScript type definitions for the Training module
 */

// Base types
export type TrainingStatus = 'pending' | 'in_progress' | 'completed' | 'cancelled';
export type Priority = 'low' | 'medium' | 'high' | 'critical';
export type TrainingFormat = 'online' | 'in_person' | 'blended' | 'self_paced';
export type TrainingLevel = 'beginner' | 'intermediate' | 'advanced' | 'expert';
export type CertificationStatus = 'active' | 'expired' | 'expiring_soon' | 'pending';

// Training Program
export interface TrainingProgram {
  id: string;
  name: string;
  description: string;
  category: string;
  duration: number; // in hours
  format: TrainingFormat;
  level: TrainingLevel;
  objectives: string[];
  prerequisites: string[];
  targetAudience: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  status: 'active' | 'draft' | 'archived';
  enrollmentCount?: number;
  completionRate?: number;
  rating?: number;
}

// Training Schedule
export interface TrainingSchedule {
  id: string;
  programId: string;
  title: string;
  startDate: string;
  endDate: string;
  location: string;
  instructor: string;
  maxParticipants: number;
  enrolledParticipants: number;
  status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled';
  resources: string[];
  notifications: string[];
  meetingUrl?: string;
  materials?: string[];
}

// Certification
export interface Certification {
  id: string;
  name: string;
  issuingOrganization: string;
  employeeId: string;
  employeeName: string;
  issueDate: string;
  expiryDate: string;
  status: CertificationStatus;
  credentialId: string;
  verificationUrl?: string;
  renewalCost?: number;
  renewalRequirements?: string[];
}

// Budget Allocation
export interface BudgetAllocation {
  id: string;
  department: string;
  category: string;
  allocatedAmount: number;
  spentAmount: number;
  remainingAmount: number;
  fiscalYear: number;
  quarter: string;
  approvedBy: string;
  approvalDate: string;
  status: 'active' | 'frozen' | 'exhausted';
  expenses?: BudgetExpense[];
}

export interface BudgetExpense {
  id: string;
  description: string;
  amount: number;
  date: string;
  category: string;
  approvedBy: string;
  receiptUrl?: string;
}

// Progress Tracking
export interface ProgressData {
  id: string;
  employeeId: string;
  employeeName: string;
  programId: string;
  programName: string;
  enrollmentDate: string;
  progress: number; // percentage
  completedModules: number;
  totalModules: number;
  lastAccessDate: string;
  estimatedCompletion?: string;
  completionDate?: string;
  status: TrainingStatus;
  quizScores: number[];
  timeSpent: number; // in minutes
  certificateIssued?: boolean;
}

// Feedback
export interface FeedbackResponse {
  id: string;
  programId: string;
  employeeId: string;
  employeeName: string;
  rating: number;
  comments: string;
  suggestions?: string;
  submittedDate: string;
  categories: string[];
  wouldRecommend: boolean;
  instructorRating?: number;
  contentRating?: number;
  platformRating?: number;
}

export interface FeedbackSurvey {
  id: string;
  title: string;
  questions: FeedbackQuestion[];
  targetProgram?: string;
  targetAudience: string[];
  deadline: string;
  responses: number;
  averageRating?: number;
}

export interface FeedbackQuestion {
  id: string;
  question: string;
  type: 'rating' | 'text' | 'multiple_choice' | 'yes_no';
  required: boolean;
  options?: string[];
}

// Gamification
export interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  points: number;
  category: string;
  criteria: string;
  unlockedBy: string[];
  dateCreated: string;
  rarity?: 'common' | 'rare' | 'epic' | 'legendary';
}

export interface LeaderboardEntry {
  rank: number;
  employeeId: string;
  employeeName: string;
  department: string;
  points: number;
  badges: number;
  coursesCompleted: number;
  streak: number;
  level: string;
  achievements?: string[];
}

export interface GamificationProfile {
  employeeId: string;
  totalPoints: number;
  level: string;
  nextLevelPoints: number;
  achievements: Achievement[];
  badges: Badge[];
  currentStreak: number;
  longestStreak: number;
  rank: number;
  percentile: number;
}

export interface Badge {
  id: string;
  name: string;
  description: string;
  icon: string;
  earnedDate: string;
  category: string;
}

// Learning Pathways
export interface LearningPathway {
  id: string;
  name: string;
  description: string;
  targetRole: string;
  estimatedDuration: string;
  programs: string[];
  milestones: PathwayMilestone[];
  enrolledEmployees: number;
  completionRate: number;
  averageRating: number;
  skills?: string[];
  certifications?: string[];
}

export interface PathwayMilestone {
  name: string;
  description?: string;
  requiredPrograms: string[];
  optionalPrograms?: string[];
  completed: boolean;
  completionDate?: string;
  badge?: string;
}

// Skills Gap Analysis
export interface SkillGap {
  id: string;
  department: string;
  skill: string;
  currentLevel: number;
  requiredLevel: number;
  gap: number;
  priority: Priority;
  affectedEmployees: number;
  recommendedPrograms: string[];
  estimatedTimeToClose: string;
  businessImpact: string;
  assessmentDate?: string;
}

export interface SkillAssessment {
  id: string;
  employeeId: string;
  skills: SkillRating[];
  assessmentDate: string;
  assessedBy: string;
  nextAssessment: string;
  overallLevel: number;
  strengths: string[];
  improvements: string[];
}

export interface SkillRating {
  skill: string;
  currentLevel: number;
  targetLevel: number;
  lastUpdated: string;
  validatedBy?: string;
}

// Training Needs
export interface TrainingNeed {
  id: string;
  employeeId: string;
  employeeName: string;
  department: string;
  role: string;
  identifiedSkills: string[];
  requiredPrograms: string[];
  priority: Priority;
  deadline: string;
  status: TrainingStatus;
  managerApproval: boolean;
  hrApproval: boolean;
  notes?: string;
  createdDate?: string;
  lastUpdated?: string;
}

// LMS Integration
export interface LMSCourse {
  id: string;
  title: string;
  provider: string;
  duration: string;
  format: TrainingFormat;
  price: number;
  rating: number;
  enrollments: number;
  difficulty: TrainingLevel;
  topics: string[];
  certificate: boolean;
  externalId?: string;
  syncStatus?: 'synced' | 'pending' | 'failed';
  lastSynced?: string;
}

export interface LMSIntegration {
  id: string;
  provider: string;
  apiEndpoint: string;
  apiKey: string;
  isActive: boolean;
  lastSync: string;
  syncFrequency: string;
  coursesImported: number;
  usersLinked: number;
  features: string[];
}

// Reports
export interface TrainingReport {
  id: string;
  name: string;
  type: 'summary' | 'detailed' | 'compliance' | 'budget' | 'performance';
  generatedDate: string;
  generatedBy: string;
  period: string;
  metrics: ReportMetric[];
  charts?: ChartData[];
  insights: string[];
  recommendations: string[];
}

export interface ReportMetric {
  name: string;
  value: number | string;
  change?: number;
  trend?: 'up' | 'down' | 'stable';
  target?: number | string;
}

export interface ChartData {
  type: 'bar' | 'line' | 'pie' | 'area';
  title: string;
  data: any[];
  xAxis?: string;
  yAxis?: string;
}

export interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  metrics: string[];
  schedule: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
  recipients: string[];
  format?: 'pdf' | 'excel' | 'html';
  lastGenerated?: string;
}

// Dashboard Types
export interface DashboardMetrics {
  overview: {
    activePrograms: number;
    activeTrainees: number;
    completedThisMonth: number;
    upcomingCertifications: number;
    totalBudget: number;
    budgetUtilized: number;
    averageSatisfaction: number;
    completionRate: number;
  };
  trends: {
    enrollments: TrendData[];
    completions: TrendData[];
  };
  departmentStats: DepartmentStat[];
}

export interface TrendData {
  month: string;
  value: number;
}

export interface DepartmentStat {
  department: string;
  enrolled: number;
  completed: number;
  avgScore: number;
}

// API Response Types
export interface ApiResponse<T> {
  data: T;
  status: 'success' | 'error';
  message?: string;
  timestamp: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
  hasMore: boolean;
}

export interface BatchOperationResult {
  successful: number;
  failed: number;
  errors: string[];
  results: any[];
}

// Notification Types
export interface TrainingNotification {
  id: string;
  type: 'reminder' | 'deadline' | 'achievement' | 'announcement' | 'approval';
  title: string;
  message: string;
  recipientId: string;
  createdDate: string;
  readDate?: string;
  priority: Priority;
  actionUrl?: string;
  metadata?: any;
}