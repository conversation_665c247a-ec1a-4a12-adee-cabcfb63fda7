import type { 
  BudgetAnalytics, 
  ForecastData, 
  AuditLog,
  ExpenseRow,
  NewExpense 
} from '@/lib/services/budgetApi';

declare global {
  interface Window {
    budget: {
      // Analytics
      getAnalytics: (year: number) => Promise<BudgetAnalytics>;
      getForecast: (year: number) => Promise<ForecastData[]>;
      
      // Bulk operations
      bulkImportExpenses: (expenses: NewExpense[]) => Promise<ExpenseRow[]>;
      
      // Audit
      getAuditTrail: (entityType: string, entityId?: string) => Promise<AuditLog[]>;
      
      // Existing methods
      getQuarterlyAllocations: (year: number) => Promise<any>;
      getDepartmentAllocations: (year: number) => Promise<any>;
      getCategoryLimits: (year: number) => Promise<any>;
      getAllocationRules: () => Promise<any>;
    };
  }
}

export {};