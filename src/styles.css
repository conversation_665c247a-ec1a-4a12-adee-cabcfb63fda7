@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap');
@import "tailwindcss";
@import "./components/sessions/SessionsTheme.css";

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-success: var(--success);
  --color-success-foreground: var(--success-foreground);
  --color-warning: var(--warning);
  --color-warning-foreground: var(--warning-foreground);
  --color-info: var(--info);
  --color-info-foreground: var(--info-foreground);
  --color-dashboard-bg: var(--dashboard-bg);
  --color-dashboard-card: var(--dashboard-card);
  --color-dashboard-border: var(--dashboard-border);
  --color-dashboard-accent: var(--dashboard-accent);
  --color-dashboard-success: var(--dashboard-success);
  --color-dashboard-warning: var(--dashboard-warning);
  --color-dashboard-gradient-start: var(--dashboard-gradient-start);
  --color-dashboard-gradient-end: var(--dashboard-gradient-end);
  --color-dashboard-chart-primary: var(--dashboard-chart-primary);
  --color-dashboard-chart-secondary: var(--dashboard-chart-secondary);
  --color-dashboard-chart-tertiary: var(--dashboard-chart-tertiary);
  
  /* Training-specific colors */
  --color-training-in-progress: var(--training-in-progress);
  --color-training-completed: var(--training-completed);
  --color-training-overdue: var(--training-overdue);
  --color-skill-gap-critical: var(--skill-gap-critical);
  --color-skill-gap-moderate: var(--skill-gap-moderate);
  --color-skill-gap-minor: var(--skill-gap-minor);
  --color-certification-active: var(--certification-active);
  --color-certification-expiring: var(--certification-expiring);
  --color-budget-allocated: var(--budget-allocated);
  --color-budget-spent: var(--budget-spent);
}

:root {
  --radius: 0.5rem;
  --background: #ffffff;
  --foreground: #0f172a;
  --card: #ffffff;
  --card-foreground: #0f172a;
  --popover: #ffffff;
  --popover-foreground: #0f172a;
  --primary: #3b82f6;
  --primary-foreground: #ffffff;
  --secondary: #f1f5f9;
  --secondary-foreground: #0f172a;
  --muted: #f8fafc;
  --muted-foreground: #64748b;
  --accent: #f1f5f9;
  --accent-foreground: #0f172a;
  --destructive: #ef4444;
  --border: #e2e8f0;
  --input: #e2e8f0;
  --ring: #3b82f6;
  --chart-1: #3b82f6;
  --chart-2: #10b981;
  --chart-3: #f59e0b;
  --chart-4: #ef4444;
  --chart-5: #8b5cf6;
  --sidebar: #ffffff;
  --sidebar-foreground: #0f172a;
  --sidebar-primary: #3b82f6;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #f1f5f9;
  --sidebar-accent-foreground: #0f172a;
  --sidebar-border: #e2e8f0;
  --sidebar-ring: #3b82f6;
  --success: #10b981;
  --success-foreground: #ffffff;
  --warning: #f59e0b;
  --warning-foreground: #ffffff;
  --info: #3b82f6;
  --info-foreground: #ffffff;
  
  /* Agent & AI specific colors */
  --agent-primary: #8b5cf6;
  --agent-secondary: #a78bfa;
  --mcp-primary: #6366f1;
  --mcp-secondary: #818cf8;
  
  /* Status colors for different states */
  --status-thinking: #f59e0b;
  --status-processing: #3b82f6;
  --status-waiting: #6b7280;
  --status-agent-active: #8b5cf6;
  --status-mcp-connected: #6366f1;
  
  /* Financial dashboard specific colors */
  --dashboard-bg: #0a0a0a;
  --dashboard-card: #1a1a1a;
  --dashboard-border: #333333;
  --dashboard-accent: #ff4d4d;
  --dashboard-success: #22c55e;
  --dashboard-warning: #f59e0b;
  --dashboard-gradient-start: #ff6b6b;
  --dashboard-gradient-end: #4ecdc4;
  --dashboard-chart-primary: #ff6b6b;
  --dashboard-chart-secondary: #4ecdc4;
  --dashboard-chart-tertiary: #45b7d1;
  
  /* Training system colors */
  --training-in-progress: #f59e0b;
  --training-completed: #10b981;
  --training-overdue: #ef4444;
  --skill-gap-critical: #dc2626;
  --skill-gap-moderate: #f59e0b;
  --skill-gap-minor: #10b981;
  --certification-active: #10b981;
  --certification-expiring: #f59e0b;
  --budget-allocated: #3b82f6;
  --budget-spent: #8b5cf6;
}

.dark {
  --background: #0a0a0a;
  --foreground: #ffffff;
  --card: #1a1a1a;
  --card-foreground: #ffffff;
  --popover: #1a1a1a;
  --popover-foreground: #ffffff;
  --primary: #ff4d4d;
  --primary-foreground: #ffffff;
  --secondary: #2a2a2a;
  --secondary-foreground: #ffffff;
  --muted: #1a1a1a;
  --muted-foreground: #a1a1aa;
  --accent: #2a2a2a;
  --accent-foreground: #ffffff;
  --destructive: #ef4444;
  --border: #333333;
  --input: #333333;
  --ring: #ff4d4d;
  --chart-1: #ff6b6b;
  --chart-2: #4ecdc4;
  --chart-3: #45b7d1;
  --chart-4: #f59e0b;
  --chart-5: #8b5cf6;
  --sidebar: #1a1a1a;
  --sidebar-foreground: #ffffff;
  --sidebar-primary: #ff4d4d;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #2a2a2a;
  --sidebar-accent-foreground: #ffffff;
  --sidebar-border: #333333;
  --sidebar-ring: #ff4d4d;
  --success: #22c55e;
  --success-foreground: #ffffff;
  --warning: #f59e0b;
  --warning-foreground: #ffffff;
  --info: #45b7d1;
  --info-foreground: #ffffff;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
    font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }
  
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Playfair Display', Georgia, serif;
  }
}

@layer utilities {
  /* Typography utilities */
  .text-heading-xl {
    @apply text-4xl font-bold tracking-tight;
    font-family: 'Playfair Display', Georgia, serif;
  }
  
  .text-heading-lg {
    @apply text-3xl font-semibold tracking-tight;
    font-family: 'Playfair Display', Georgia, serif;
  }
  
  .text-heading-md {
    @apply text-2xl font-semibold tracking-tight;
    font-family: 'Playfair Display', Georgia, serif;
  }
  
  .text-heading-sm {
    @apply text-xl font-semibold tracking-tight;
    font-family: 'Playfair Display', Georgia, serif;
  }
  
  .text-body-lg {
    @apply text-lg leading-relaxed;
  }
  
  .text-body {
    @apply text-base leading-relaxed;
  }
  
  .text-body-sm {
    @apply text-sm leading-relaxed;
  }
  
  .text-caption {
    @apply text-xs text-muted-foreground;
  }
  
  /* Glass morphism effects */
  /* .glass-effect class removed - use explicit CSS properties instead */
  /* .glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  } */
  
  .glass-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }
  
  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }
  
  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }
  
  /* Custom scrollbar */
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: hsl(var(--muted-foreground) / 0.3);
    border-radius: 3px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--muted-foreground) / 0.5);
  }
  
  /* Status colors */
  .status-success {
    @apply bg-success/10 text-success border-success/20;
  }
  
  .status-warning {
    @apply bg-warning/10 text-warning border-warning/20;
  }
  
  .status-error {
    @apply bg-destructive/10 text-destructive border-destructive/20;
  }
  
  .status-info {
    @apply bg-info/10 text-info border-info/20;
  }
  
  /* Interactive states */
  .interactive {
    @apply transition-all duration-200 ease-in-out;
  }

  .interactive:hover {
    @apply scale-[1.02] shadow-md;
  }

  .interactive:active {
    @apply scale-[0.98];
  }
  
  /* Training-specific utilities */
  .training-metric-large { 
    @apply text-3xl font-bold tracking-tight; 
  }
  
  .training-metric-medium { 
    @apply text-xl font-semibold; 
  }
  
  .training-label { 
    @apply text-sm font-medium text-muted-foreground; 
  }
  
  .training-description { 
    @apply text-sm leading-relaxed; 
  }
  
  .training-section-gap { 
    @apply space-y-8; 
  }
  
  .training-card-gap { 
    @apply gap-6; 
  }
  
  .training-form-gap { 
    @apply space-y-4; 
  }
  
  /* Status-specific colors */
  .status-training-active {
    @apply bg-training-completed/10 text-training-completed border-training-completed/20;
  }
  
  .status-training-progress {
    @apply bg-training-in-progress/10 text-training-in-progress border-training-in-progress/20;
  }
  
  .status-training-overdue {
    @apply bg-training-overdue/10 text-training-overdue border-training-overdue/20;
  }
  
  .status-skill-critical {
    @apply bg-skill-gap-critical/10 text-skill-gap-critical border-skill-gap-critical/20;
  }
  
  .status-skill-moderate {
    @apply bg-skill-gap-moderate/10 text-skill-gap-moderate border-skill-gap-moderate/20;
  }
  
  .status-skill-minor {
    @apply bg-skill-gap-minor/10 text-skill-gap-minor border-skill-gap-minor/20;
  }
  
  /* Focus states */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
  }
  
  /* Layout utilities */
  .container-responsive {
    @apply w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  .grid-responsive {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4;
  }
  
  /* Card variants */
  .card-elevated {
    @apply bg-card border border-border shadow-lg rounded-lg;
  }
  
  .card-flat {
    @apply bg-card border border-border rounded-lg;
  }
  
  .card-ghost {
    @apply bg-transparent border-0 rounded-lg;
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(20px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from { 
    opacity: 0;
    transform: scale(0.95);
  }
  to { 
    opacity: 1;
    transform: scale(1);
  }
}

/* Component-specific styles */
.trainee-card {
  @apply bg-card border border-border shadow-lg rounded-lg p-4 hover:shadow-xl transition-all duration-300;
}

.trainee-card:hover {
  @apply transform -translate-y-1;
}

.metric-card {
  @apply bg-card border border-border shadow-lg rounded-lg p-6 text-center;
}

.progress-bar {
  @apply w-full bg-muted rounded-full overflow-hidden;
}

.progress-fill {
  @apply h-full bg-primary transition-all duration-500 ease-out;
}

/* Tab styles */
.tab-content {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Dialog styles */
.dialog-overlay {
  @apply fixed inset-0 bg-black/50 backdrop-blur-sm;
}

.dialog-content {
  @apply bg-card border border-border shadow-2xl rounded-lg;
}

/* Toast styles */
.toast {
  @apply bg-card border border-border shadow-lg rounded-lg p-4;
}

.toast-success {
  @apply border-success/20 bg-success/5;
}

.toast-error {
  @apply border-destructive/20 bg-destructive/5;
}

.toast-warning {
  @apply border-warning/20 bg-warning/5;
}

.toast-info {
  @apply border-info/20 bg-info/5;
}

/* Loading states */
.skeleton {
  @apply animate-pulse bg-muted rounded;
}

.spinner {
  @apply animate-spin rounded-full border-2 border-muted border-t-primary;
}

/* Responsive utilities */
@media (max-width: 640px) {
  .mobile-stack {
    @apply flex-col space-y-2 space-x-0;
  }
}

@media (min-width: 1024px) {
  .desktop-grid {
    @apply grid-cols-4;
  }
}