[build]
publish = "docs/.vitepress/dist"
command = "pnpm run docs:build"

[build.environment]
NODE_VERSION = "22"
NODE_OPTIONS = "--max-old-space-size=8192"
PLAYWRIGHT_BROWSERS_PATH = "0"

[[redirects]]
from = "/new"
to = "https://stackblitz.com/github/slidevjs/new?file=slides.md"
status = 302
force = true

[[redirects]]
from = "/demo/composable-vue/*"
to = "/demo/composable-vue/index.html"
status = 200

[[redirects]]
from = "/demo/starter/*"
to = "/demo/starter/index.html"
status = 200

[[redirects]]
from = "/demo/vue-runner/*"
to = "/demo/vue-runner/index.html"
status = 200

[[redirects]]
from = "https://slidev.antfu.me/*"
to = "https://sli.dev/:splat"
status = 301
force = true

[[redirects]]
from = "/*"
to = "/index.html"
status = 200
