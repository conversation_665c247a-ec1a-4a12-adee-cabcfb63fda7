html.dark:root {
  color-scheme: dark;
}

/* Shiki */
html.dark .shiki {
  color: var(--shiki-dark, inherit);
  --twoslash-popup-bg: var(--shiki-dark-bg, inherit);
}

html.dark .shiki span {
  color: var(--shiki-dark);
}

html:not(.dark) .shiki {
  color: var(--shiki-light, inherit);
  --twoslash-popup-bg: var(--shiki-light-bg, inherit);
}

html:not(.dark) .shiki span {
  color: var(--shiki-light);
}

.twoslash-meta-line.twoslash-popover-line {
  margin-top: -10px;
}

/* Slidev */
.slidev-code-wrapper {
  margin: var(--slidev-code-margin) !important;
  scroll-padding: var(--slidev-code-padding);
  &:-webkit-scrollbar {
    width: 0px;
  }
}

.slidev-code {
  font-family: var(--slidev-code-font-family) !important;
  padding: var(--slidev-code-padding) !important;
  font-size: var(--slidev-code-font-size) !important;
  line-height: var(--slidev-code-line-height) !important;
  border-radius: var(--slidev-code-radius) !important;
  background: var(--slidev-code-background);
  overflow: auto;
}

.slidev-code-block-title,
.slidev-code-group-tabs {
  background: var(--slidev-code-background);
  color: var(--slidev-code-tab-text-color);
  padding-left: var(--slidev-code-padding);
  padding-right: var(--slidev-code-padding);
  font-size: var(--slidev-code-tab-font-size);
  border-radius: var(--slidev-code-radius) var(--slidev-code-radius) 0 0;
  box-shadow: inset 0 -1px var(--slidev-code-tab-divider);
  display: flex;
  gap: 8px;
  align-items: center;
}

.slidev-code-block-title {
  padding: var(--slidev-code-padding);
}

.slidev-code-tab {
  font-size: var(--slidev-code-tab-font-size);
  white-space: nowrap;
  cursor: pointer;
  transition: color 0.25s;
  padding: var(--slidev-code-padding);
  border-bottom: 2px solid transparent;
  color: var(--slidev-code-tab-text-color);
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;
}

.slidev-code-tab:hover {
  color: var(--slidev-code-tab-active-text-color) !important;
}

.slidev-code-group-blocks .slidev-code-wrapper {
  margin: 0 !important;
}

.slidev-code-group-blocks .slidev-code {
  border-radius: 0 0 var(--slidev-code-radius) var(--slidev-code-radius) !important;
}

.slidev-code-group-blocks .slidev-code-wrapper.active {
  display: block;
}

.slidev-code-group-blocks .slidev-code-wrapper {
  display: none;
}

.slidev-code-block-title + .slidev-code,
.slidev-code-group-tabs + .slidev-code {
  border-top-left-radius: 0 !important;
  border-top-right-radius: 0 !important;
}

.slidev-code .slidev-code-highlighted {
}
.slidev-code .slidev-code-dishonored {
  opacity: 0.3;
  pointer-events: none;
}

.slidev-code-line-numbers .slidev-code code {
  counter-reset: step;
  counter-increment: step calc(var(--start, 1) - 1);
}

.slidev-code-line-numbers .slidev-code code .line::before {
  content: counter(step);
  counter-increment: step;
  display: inline-block;
  text-align: right;
  --uno: w-4 mr-6 text-gray-400 dark-text-gray-600;
}

/* Inline Code */
.slidev-note :not(pre) > code,
.slidev-layout :not(pre) > code {
  font-size: 0.9em;
  background: var(--slidev-code-background);
  border-radius: var(--slidev-code-radius);
  --uno: font-light py-0.5 px-1.5;
}

.slidev-note :not(pre) > code:after,
.slidev-note :not(pre) > code:before {
  content: '';
}

.slidev-layout :not(pre) > code:before {
  margin-right: -0.08em;
}

/* Revert CSS reset for KaTex */
.katex,
.katex :after,
.katex :before {
  border-color: currentColor;
}
