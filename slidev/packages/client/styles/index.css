html,
body,
#app,
#page-root {
  padding: 0;
  margin: 0;
  width: 100%;
  height: 100vh;
  height: calc(var(--vh, 1vh) * 100);
  overflow: hidden;
  print-color-adjust: exact;
  -webkit-print-color-adjust: exact;
  --uno: font-sans bg-main;
}

html {
  background: transparent;
}

.slidev-icon-btn {
  aspect-ratio: 1;
  user-select: none;
  outline: none;
  cursor: pointer;
  @apply inline-flex items-center justify-center opacity-75 transition duration-200 ease-in-out align-middle rounded p-1 relative;
  @apply hover:(opacity-100 bg-gray-400 bg-opacity-10);
  @apply focus-visible:(opacity-100 outline outline-2 outline-offset-2 outline-black dark:outline-white);
  @apply md:p-2;
}

.slidev-icon-btn.shallow {
  opacity: 0.3;
}

.slidev-icon-btn.active {
  opacity: 1;
}

.slidev-icon-btn.disabled {
  opacity: 0.25;
  pointer-events: none;
}

.slidev-layout a.slidev-icon-btn {
  @apply border-none hover:border-none hover:text-white;
}

.slidev-vclick-target {
  @apply transition-opacity duration-100;
}

.slidev-vclick-hidden {
  opacity: 0 !important;
  pointer-events: none !important;
  user-select: none !important;
}

.slidev-vclick-display-none {
  display: none !important;
}

.slidev-vclick-fade {
  opacity: 0.5;
}

.slidev-icon {
  display: inline-block;
  vertical-align: sub;
  line-height: 1em;
}

.slidev-page {
  position: relative;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
}

/* Note Clicks */

.slidev-note-with-clicks .slidev-note-fade {
  color: #888888ab;
}

.slidev-note-click-mark {
  user-select: none;
  font-size: 0.7em;
  display: inline-flex;
  --uno: text-violet bg-violet/10 px1 font-mono rounded items-center border border-transparent;
}
.slidev-note-click-mark.slidev-note-click-mark-active {
  --uno: border border-violet;
}
.slidev-note-click-mark.slidev-note-click-mark-past {
  filter: saturate(0);
  opacity: 0.5;
}
.slidev-note-click-mark.slidev-note-click-mark-future {
  opacity: 0.5;
}

.slidev-note-click-mark::before {
  content: '';
  display: inline-block;
  --un-icon: url("data:image/svg+xml;utf8,%3Csvg viewBox='0 0 32 32' width='1.2em' height='1.2em' xmlns='http://www.w3.org/2000/svg' %3E%3Cpath fill='currentColor' d='M23 28a1 1 0 0 1-.71-.29l-6.13-6.14l-3.33 5a1 1 0 0 1-1 .44a1 1 0 0 1-.81-.7l-6-20A1 1 0 0 1 6.29 5l20 6a1 1 0 0 1 .7.81a1 1 0 0 1-.44 1l-5 3.33l6.14 6.13a1 1 0 0 1 0 1.42l-4 4A1 1 0 0 1 23 28m0-2.41L25.59 23l-7.16-7.15l5.25-3.5L7.49 7.49l4.86 16.19l3.5-5.25Z'/%3E%3C/svg%3E");
  -webkit-mask: var(--un-icon) no-repeat;
  mask: var(--un-icon) no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
  background-color: currentColor;
  color: inherit;
  width: 1.2em;
  height: 1.2em;
  opacity: 0.8;
}

.slidev-note-click-mark::after {
  content: attr(data-clicks);
  display: inline-block;
  transform: translateY(0.1em);
}

.slidev-form-button {
  --uno: text-white px-4 py-1 rounded border-b-2;
  --uno: 'bg-gray-500 border-gray-700';
  --uno: 'hover:(bg-gray-400 border-gray6)';
}
.slidev-form-button.primary {
  --uno: bg-teal-600 border-teal-800;
  --uno: 'hover:(bg-teal-500 border-teal-700)';
}

/* Transform the position back for Rough Notation (v-mark) */
.rough-annotation {
  transform: scale(calc(1 / var(--slidev-slide-scale)));
}

#twoslash-container {
  position: fixed;
}

#twoslash-container .v-popper__wrapper:not(.no-slide-scale > *) {
  transform: scale(calc(1 * var(--slidev-slide-scale)));
  transform-origin: 30px top;
}

.slidev-note ul {
  margin: 0;
}
