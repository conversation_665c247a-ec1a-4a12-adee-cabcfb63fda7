/* Sliding */
.slide-left-enter-active,
.slide-left-leave-active,
.slide-right-enter-active,
.slide-right-leave-active,
.slide-up-enter-active,
.slide-up-leave-active,
.slide-down-enter-active,
.slide-down-leave-active {
  transition: all var(--slidev-transition-duration) ease;
}

.slide-left-enter-from,
.slide-right-leave-to {
  transform: translateX(100%);
}

.slide-left-leave-to,
.slide-right-enter-from {
  transform: translateX(-100%);
}

.slide-up-enter-from,
.slide-down-leave-to {
  transform: translateY(100%);
}

.slide-up-leave-to,
.slide-down-enter-from {
  transform: translateY(-100%);
}

/* Fading */
.fade-enter-active,
.fade-leave-active {
  transition: opacity var(--slidev-transition-duration) ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.fade-out-leave-active {
  transition: opacity calc(var(--slidev-transition-duration) * 0.6) ease-out;
}

.fade-out-enter-active {
  transition: opacity calc(var(--slidev-transition-duration) * 0.8) ease-in;
  transition-delay: calc(var(--slidev-transition-duration) * 0.6);
}

.fade-out-enter-from,
.fade-out-leave-to {
  opacity: 0;
}
