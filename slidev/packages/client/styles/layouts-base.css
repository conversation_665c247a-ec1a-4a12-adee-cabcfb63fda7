.slidev-layout {
  @apply px-14 py-10 text-[1.1rem] h-full;

  pre,
  code {
    @apply select-text;
  }

  code {
    @apply font-mono;
  }

  h1 {
    @apply text-4xl mb-4;
  }

  h2 {
    @apply text-3xl;
  }

  h3 {
    @apply text-2xl;
  }

  h4 {
    @apply text-xl;
  }

  h5 {
    @apply text-base;
  }

  h6 {
    @apply text-sm pt-1 uppercase tracking-widest font-500;
  }

  h6:not(.opacity-100) {
    @apply opacity-40;
  }

  p {
    @apply my-4 leading-6;
  }

  ul {
    list-style: square;
  }

  ol {
    list-style: decimal;
  }

  li {
    @apply leading-1.8em;
  }

  blockquote {
    background: var(--slidev-code-background);
    color: var(--slidev-code-foreground);
    @apply text-sm px-2 py-1 border-primary border-l rounded;
  }

  blockquote > * {
    @apply my-0;
  }

  table {
    @apply w-full;
  }

  tr {
    @apply border-b border-main;
  }

  th {
    @apply text-left font-400;
  }

  a {
    @apply border-current border-b border-dashed hover:text-primary hover:border-solid;
  }

  td,
  th {
    @apply p-2 py-3;
  }

  b,
  strong {
    @apply font-600;
  }

  kbd {
    @apply border border-main border-b-2 rounded;
    @apply bg-gray-400 bg-opacity-5 py-0.5 px-1 text-xs font-mono;
  }
}

.slidev-layout,
[dir='ltr'],
.slidev-layout [dir='ltr'] {
  h1 {
    @apply -ml-[0.05em] mr-0;
  }

  h6 {
    @apply -ml-[0.05em] mr-0;
  }

  li {
    @apply ml-1.1em pl-0.2em mr-0 pr-0;
  }
}

[dir='rtl'],
.slidev-layout [dir='rtl'] {
  h1 {
    @apply -mr-[0.05em] ml-0;
  }

  h6 {
    @apply -mr-[0.05em] ml-0;
  }

  li {
    @apply mr-1.1em pr-0.2em ml-0 pl-0;
  }
}
