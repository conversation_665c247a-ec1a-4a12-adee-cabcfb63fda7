{"name": "create-slidev", "type": "module", "version": "52.1.0", "description": "Create starter template for <PERSON>lide<PERSON>", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://sli.dev", "repository": {"type": "git", "url": "https://github.com/slidevjs/slidev"}, "bugs": "https://github.com/slidevjs/slidev/issues", "main": "index.mjs", "bin": {"create-slidev": "index.mjs"}, "files": ["index.mjs", "template"], "engines": {"node": ">=18.0.0"}, "scripts": {"build": "node build.mjs", "prepublishOnly": "npm run build"}, "dependencies": {"ansis": "catalog:prod", "minimist": "catalog:prod", "prompts": "catalog:prod", "tinyexec": "catalog:prod"}}