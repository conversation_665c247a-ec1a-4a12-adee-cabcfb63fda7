{"name": "@slidev/types", "version": "52.1.0", "description": "Shared types declarations for Slidev", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://sli.dev", "repository": {"type": "git", "url": "https://github.com/slidevjs/slidev"}, "bugs": "https://github.com/slidevjs/slidev/issues", "main": "dist/index.mjs", "module": "dist/index.mjs", "types": "index.d.ts", "files": ["client.d.ts", "dist", "index.d.ts"], "engines": {"node": ">=18.0.0"}, "scripts": {"build": "tsdown src/index.ts", "dev": "nr build --watch", "prepublishOnly": "npm run build"}, "dependencies": {"@antfu/utils": "catalog:frontend", "@shikijs/markdown-it": "catalog:frontend", "@vitejs/plugin-vue": "catalog:prod", "@vitejs/plugin-vue-jsx": "catalog:prod", "katex": "catalog:frontend", "mermaid": "catalog:frontend", "monaco-editor": "catalog:monaco", "shiki": "catalog:frontend", "unocss": "catalog:prod", "unplugin-icons": "catalog:prod", "unplugin-vue-markdown": "catalog:prod", "vite-plugin-inspect": "catalog:prod", "vite-plugin-remote-assets": "catalog:prod", "vite-plugin-static-copy": "catalog:prod", "vite-plugin-vue-server-ref": "catalog:prod", "vue": "catalog:frontend", "vue-router": "catalog:frontend"}}