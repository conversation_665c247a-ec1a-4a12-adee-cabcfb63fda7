{"name": "@slidev/parser", "version": "52.1.0", "description": "Markdown parser for <PERSON>lide<PERSON>", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://sli.dev", "repository": {"type": "git", "url": "https://github.com/slidevjs/slidev"}, "bugs": "https://github.com/slidevjs/slidev/issues", "exports": {".": {"types": "./dist/index.d.mts", "import": "./dist/index.mjs"}, "./core": {"types": "./dist/core.d.mts", "import": "./dist/core.mjs"}, "./fs": {"types": "./dist/fs.d.mts", "import": "./dist/fs.mjs"}, "./utils": {"types": "./dist/utils.d.mts", "import": "./dist/utils.mjs"}}, "main": "dist/index.mjs", "module": "dist/index.mjs", "types": "dist/index.d.mts", "files": ["*.d.ts", "dist"], "engines": {"node": ">=18.0.0"}, "scripts": {"build": "tsdown src/index.ts src/core.ts src/fs.ts src/utils.ts", "dev": "nr build --watch", "prepublishOnly": "npm run build"}, "dependencies": {"@antfu/utils": "catalog:frontend", "@slidev/types": "workspace:*", "yaml": "catalog:prod"}}