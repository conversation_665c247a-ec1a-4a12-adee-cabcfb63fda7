{"name": "@slidev/cli", "type": "module", "version": "52.1.0", "description": "Presentation slides for developers", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://sli.dev", "repository": {"type": "git", "url": "https://github.com/slidevjs/slidev"}, "bugs": "https://github.com/slidevjs/slidev/issues", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./*": "./*"}, "main": "dist/index.js", "module": "dist/index.js", "types": "dist/index.d.ts", "bin": {"slidev": "./bin/slidev.mjs"}, "files": ["bin", "dist", "template.md"], "engines": {"node": ">=18.0.0"}, "scripts": {"build": "tsdown node/index.ts node/cli.ts", "dev": "nr build --watch", "prepublishOnly": "npm run build", "start": "tsx node/cli.ts"}, "peerDependencies": {"playwright-chromium": "^1.10.0"}, "peerDependenciesMeta": {"playwright-chromium": {"optional": true}}, "dependencies": {"@antfu/ni": "catalog:prod", "@antfu/utils": "catalog:frontend", "@iconify-json/carbon": "catalog:icons", "@iconify-json/ph": "catalog:icons", "@iconify-json/svg-spinners": "catalog:icons", "@lillallol/outline-pdf": "catalog:prod", "@shikijs/markdown-it": "catalog:frontend", "@shikijs/twoslash": "catalog:prod", "@shikijs/vitepress-twoslash": "catalog:prod", "@slidev/client": "workspace:*", "@slidev/parser": "workspace:*", "@slidev/types": "workspace:*", "@unocss/extractor-mdc": "catalog:prod", "@unocss/reset": "catalog:frontend", "@vitejs/plugin-vue": "catalog:prod", "@vitejs/plugin-vue-jsx": "catalog:prod", "ansis": "catalog:prod", "chokidar": "catalog:prod", "cli-progress": "catalog:prod", "connect": "catalog:prod", "debug": "catalog:prod", "fast-deep-equal": "catalog:prod", "fast-glob": "catalog:prod", "get-port-please": "catalog:prod", "global-directory": "catalog:prod", "htmlparser2": "catalog:prod", "is-installed-globally": "catalog:prod", "jiti": "catalog:prod", "katex": "catalog:frontend", "local-pkg": "catalog:prod", "lz-string": "catalog:frontend", "magic-string": "catalog:prod", "magic-string-stack": "catalog:prod", "markdown-it": "catalog:prod", "markdown-it-footnote": "catalog:prod", "markdown-it-mdc": "catalog:prod", "mlly": "catalog:prod", "monaco-editor": "catalog:monaco", "open": "catalog:prod", "pdf-lib": "catalog:prod", "picomatch": "catalog:prod", "plantuml-encoder": "catalog:frontend", "postcss-nested": "catalog:dev", "pptxgenjs": "catalog:prod", "prompts": "catalog:prod", "public-ip": "catalog:prod", "resolve-from": "catalog:prod", "resolve-global": "catalog:prod", "semver": "catalog:prod", "shiki": "catalog:frontend", "shiki-magic-move": "catalog:frontend", "sirv": "catalog:prod", "source-map-js": "catalog:prod", "typescript": "catalog:dev", "unhead": "catalog:frontend", "unocss": "catalog:prod", "unplugin-icons": "catalog:prod", "unplugin-vue-components": "catalog:prod", "unplugin-vue-markdown": "catalog:prod", "untun": "catalog:prod", "uqr": "catalog:prod", "vite": "catalog:prod", "vite-plugin-inspect": "catalog:prod", "vite-plugin-remote-assets": "catalog:prod", "vite-plugin-static-copy": "catalog:prod", "vite-plugin-vue-server-ref": "catalog:prod", "vitefu": "catalog:dev", "vue": "catalog:frontend", "yaml": "catalog:prod", "yargs": "catalog:prod"}, "devDependencies": {"@hedgedoc/markdown-it-plugins": "catalog:prod", "@types/picomatch": "catalog:types", "@types/plantuml-encoder": "catalog:types"}}