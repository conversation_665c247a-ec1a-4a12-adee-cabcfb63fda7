// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`md parser > frontmatter.md > config 1`] = `
{
  "drawings": {
    "enabled": true,
    "persist": false,
    "presenterOnly": false,
    "syncAll": true,
  },
  "fonts": {
    "italic": false,
    "local": [],
    "mono": [
      ""Fira Code"",
      "ui-monospace",
      "SFMono-Regular",
      "Menlo",
      "Monaco",
      "Consolas",
      ""Liberation Mono"",
      ""Courier New"",
      "monospace",
    ],
    "provider": "google",
    "sans": [
      ""Roboto"",
      ""Lato"",
      "ui-sans-serif",
      "system-ui",
      "-apple-system",
      "BlinkMacSystemFont",
      ""Segoe UI"",
      "Roboto",
      ""Helvetica Neue"",
      "Arial",
      ""Noto Sans"",
      "sans-serif",
      ""Apple Color Emoji"",
      ""Segoe UI Emoji"",
      ""Segoe UI Symbol"",
      ""Noto Color Emoji"",
    ],
    "serif": [
      ""Mate SC"",
      "ui-serif",
      "Georgia",
      "Cambria",
      ""Times New Roman"",
      "Times",
      "serif",
    ],
    "webfonts": [
      "Roboto",
      "Lato",
      "Mate SC",
      "Fira Code",
    ],
    "weights": [
      "200",
      "400",
      "600",
    ],
  },
  "layout": "cover",
  "title": "Hi",
}
`;

exports[`md parser > frontmatter.md > features 1`] = `
{
  "katex": false,
  "mermaid": false,
  "monaco": false,
  "tweet": false,
}
`;

exports[`md parser > frontmatter.md > slides 1`] = `
[
  {
    "content": "# Hi",
    "frontmatter": {
      "fonts": {
        "mono": "Fira Code",
        "sans": "Roboto, Lato",
        "serif": "Mate SC",
      },
      "layout": "cover",
    },
    "frontmatterRaw": "layout: cover
fonts:
  sans: Roboto, Lato
  serif: Mate SC
  mono: Fira Code
",
    "importChain": undefined,
    "index": 0,
    "level": 1,
    "note": undefined,
    "revision": "243b9h",
    "source": {
      "content": "
# Hi
",
      "contentRaw": "# Hi",
      "contentStart": 7,
      "end": 10,
      "filepath": "frontmatter.md",
      "frontmatter": {
        "fonts": {
          "mono": "Fira Code",
          "sans": "Roboto, Lato",
          "serif": "Mate SC",
        },
        "layout": "cover",
      },
      "frontmatterDoc": {
        "fonts": {
          "mono": "Fira Code",
          "sans": "Roboto, Lato",
          "serif": "Mate SC",
        },
        "layout": "cover",
      },
      "frontmatterRaw": "layout: cover
fonts:
  sans: Roboto, Lato
  serif: Mate SC
  mono: Fira Code
",
      "frontmatterStyle": "frontmatter",
      "index": 0,
      "level": 1,
      "note": undefined,
      "raw": "---
layout: cover
fonts:
  sans: Roboto, Lato
  serif: Mate SC
  mono: Fira Code
---

# Hi
",
      "revision": "243b9h",
      "start": 0,
      "title": "Hi",
    },
    "title": "Hi",
  },
  {
    "content": "# Hello",
    "frontmatter": {
      "layout": "center",
      "meta": {
        "duration": 12,
        "title": "FooBar",
      },
    },
    "frontmatterRaw": "meta:
  title: FooBar
  duration: 12
layout: center
",
    "importChain": undefined,
    "index": 1,
    "level": 1,
    "note": "This is note",
    "revision": "bi4rhs",
    "source": {
      "content": "
# Hello
",
      "contentRaw": "# Hello",
      "contentStart": 16,
      "end": 23,
      "filepath": "frontmatter.md",
      "frontmatter": {
        "layout": "center",
        "meta": {
          "duration": 12,
          "title": "FooBar",
        },
      },
      "frontmatterDoc": {
        "layout": "center",
        "meta": {
          "duration": 12,
          "title": "FooBar",
        },
      },
      "frontmatterRaw": "meta:
  title: FooBar
  duration: 12
layout: center
",
      "frontmatterStyle": "frontmatter",
      "index": 1,
      "level": 1,
      "note": "This is note",
      "raw": "---
meta:
  title: FooBar
  duration: 12
layout: center
---

# Hello

<!--
This is note
-->
",
      "revision": "bi4rhs",
      "start": 10,
      "title": "Hello",
    },
    "title": "Hello",
  },
  {
    "content": "# Morning",
    "frontmatter": {},
    "frontmatterRaw": undefined,
    "importChain": undefined,
    "index": 2,
    "level": 1,
    "note": undefined,
    "revision": "-4tdhll",
    "source": {
      "content": "
# Morning
",
      "contentRaw": "# Morning",
      "contentStart": 24,
      "end": 27,
      "filepath": "frontmatter.md",
      "frontmatter": {},
      "frontmatterDoc": undefined,
      "frontmatterRaw": undefined,
      "frontmatterStyle": undefined,
      "index": 2,
      "level": 1,
      "note": undefined,
      "raw": "
# Morning
",
      "revision": "-4tdhll",
      "start": 24,
      "title": "Morning",
    },
    "title": "Morning",
  },
  {
    "content": "<!-- This is not note -->
Hey",
    "frontmatter": {
      "layout": "text",
    },
    "frontmatterRaw": "layout: text
",
    "importChain": undefined,
    "index": 3,
    "level": undefined,
    "note": "This is note",
    "revision": "w0llj2",
    "source": {
      "content": "
<!-- This is not note -->
Hey
",
      "contentRaw": "<!-- This is not note -->
Hey",
      "contentStart": 30,
      "end": 38,
      "filepath": "frontmatter.md",
      "frontmatter": {
        "layout": "text",
      },
      "frontmatterDoc": {
        "layout": "text",
      },
      "frontmatterRaw": "layout: text
",
      "frontmatterStyle": "frontmatter",
      "index": 3,
      "level": undefined,
      "note": "This is note",
      "raw": "---
layout: text
---

<!-- This is not note -->
Hey

<!--
This is note
-->
",
      "revision": "w0llj2",
      "start": 27,
      "title": undefined,
    },
    "title": undefined,
  },
  {
    "content": "\`\`\`md
---
this should be treated as code block
---

---

Also part of the code block
\`\`\`",
    "frontmatter": {},
    "frontmatterRaw": undefined,
    "importChain": undefined,
    "index": 4,
    "level": undefined,
    "note": undefined,
    "revision": "-1rfsmv",
    "source": {
      "content": "
\`\`\`md
---
this should be treated as code block
---

---

Also part of the code block
\`\`\`
",
      "contentRaw": "\`\`\`md
---
this should be treated as code block
---

---

Also part of the code block
\`\`\`",
      "contentStart": 39,
      "end": 50,
      "filepath": "frontmatter.md",
      "frontmatter": {},
      "frontmatterDoc": undefined,
      "frontmatterRaw": undefined,
      "frontmatterStyle": undefined,
      "index": 4,
      "level": undefined,
      "note": undefined,
      "raw": "
\`\`\`md
---
this should be treated as code block
---

---

Also part of the code block
\`\`\`
",
      "revision": "-1rfsmv",
      "start": 39,
      "title": undefined,
    },
    "title": undefined,
  },
  {
    "content": "Content 1",
    "frontmatter": {
      "layout": "from yaml",
    },
    "frontmatterRaw": "
# The first yaml block should be treated as frontmatter
layout: from yaml
",
    "importChain": undefined,
    "index": 5,
    "level": undefined,
    "note": undefined,
    "revision": "rp0t01",
    "source": {
      "content": "
Content 1
",
      "contentRaw": "Content 1",
      "contentStart": 51,
      "end": 59,
      "filepath": "frontmatter.md",
      "frontmatter": {
        "layout": "from yaml",
      },
      "frontmatterDoc": {
        "layout": "from yaml",
      },
      "frontmatterRaw": "
# The first yaml block should be treated as frontmatter
layout: from yaml
",
      "frontmatterStyle": "yaml",
      "index": 5,
      "level": undefined,
      "note": undefined,
      "raw": "\`\`\`yaml
# The first yaml block should be treated as frontmatter
layout: from yaml
\`\`\`

Content 1
",
      "revision": "rp0t01",
      "start": 51,
      "title": undefined,
    },
    "title": undefined,
  },
  {
    "content": "\`\`\`yaml
# When there is already a frontmatter, the first yaml block should be treated as content
layout: should not from yaml 1
\`\`\`

Content 2",
    "frontmatter": {
      "layout": "cover",
    },
    "frontmatterRaw": "layout: cover
",
    "importChain": undefined,
    "index": 6,
    "level": 1,
    "note": undefined,
    "revision": "-vfy6yo",
    "source": {
      "content": "
\`\`\`yaml
# When there is already a frontmatter, the first yaml block should be treated as content
layout: should not from yaml 1
\`\`\`

Content 2
",
      "contentRaw": "\`\`\`yaml
# When there is already a frontmatter, the first yaml block should be treated as content
layout: should not from yaml 1
\`\`\`

Content 2",
      "contentStart": 62,
      "end": 70,
      "filepath": "frontmatter.md",
      "frontmatter": {
        "layout": "cover",
      },
      "frontmatterDoc": {
        "layout": "cover",
      },
      "frontmatterRaw": "layout: cover
",
      "frontmatterStyle": "frontmatter",
      "index": 6,
      "level": 1,
      "note": undefined,
      "raw": "---
layout: cover
---

\`\`\`yaml
# When there is already a frontmatter, the first yaml block should be treated as content
layout: should not from yaml 1
\`\`\`

Content 2
",
      "revision": "-vfy6yo",
      "start": 59,
      "title": "When there is already a frontmatter, the first yaml block should be treated as content",
    },
    "title": "When there is already a frontmatter, the first yaml block should be treated as content",
  },
  {
    "content": "# Title

\`\`\`yaml
# When there is already a frontmatter, the first yaml block should be treated as content
layout: should not from yaml 2
\`\`\`

Content 3",
    "frontmatter": {},
    "frontmatterRaw": undefined,
    "importChain": undefined,
    "index": 7,
    "level": 1,
    "note": undefined,
    "revision": "-hu94b6",
    "source": {
      "content": "
# Title

\`\`\`yaml
# When there is already a frontmatter, the first yaml block should be treated as content
layout: should not from yaml 2
\`\`\`

Content 3
",
      "contentRaw": "# Title

\`\`\`yaml
# When there is already a frontmatter, the first yaml block should be treated as content
layout: should not from yaml 2
\`\`\`

Content 3",
      "contentStart": 71,
      "end": 81,
      "filepath": "frontmatter.md",
      "frontmatter": {},
      "frontmatterDoc": undefined,
      "frontmatterRaw": undefined,
      "frontmatterStyle": undefined,
      "index": 7,
      "level": 1,
      "note": undefined,
      "raw": "
# Title

\`\`\`yaml
# When there is already a frontmatter, the first yaml block should be treated as content
layout: should not from yaml 2
\`\`\`

Content 3
",
      "revision": "-hu94b6",
      "start": 71,
      "title": "Title",
    },
    "title": "Title",
  },
]
`;

exports[`md parser > mdc.md > config 1`] = `
{
  "drawings": {
    "enabled": true,
    "persist": false,
    "presenterOnly": false,
    "syncAll": true,
  },
  "fonts": {
    "italic": false,
    "local": [],
    "mono": [
      "ui-monospace",
      "SFMono-Regular",
      "Menlo",
      "Monaco",
      "Consolas",
      ""Liberation Mono"",
      ""Courier New"",
      "monospace",
    ],
    "provider": "google",
    "sans": [
      "ui-sans-serif",
      "system-ui",
      "-apple-system",
      "BlinkMacSystemFont",
      ""Segoe UI"",
      "Roboto",
      ""Helvetica Neue"",
      "Arial",
      ""Noto Sans"",
      "sans-serif",
      ""Apple Color Emoji"",
      ""Segoe UI Emoji"",
      ""Segoe UI Symbol"",
      ""Noto Color Emoji"",
    ],
    "serif": [
      "ui-serif",
      "Georgia",
      "Cambria",
      ""Times New Roman"",
      "Times",
      "serif",
    ],
    "webfonts": [],
    "weights": [
      "200",
      "400",
      "600",
    ],
  },
  "mdc": true,
  "title": "MDC{style="color:red"}",
}
`;

exports[`md parser > mdc.md > features 1`] = `
{
  "katex": false,
  "mermaid": false,
  "monaco": false,
  "tweet": false,
}
`;

exports[`md parser > mdc.md > slides 1`] = `
[
  {
    "content": "# MDC{style="color:red"}

:arrow{x1=1 y1=1 x2=2 y2=2}",
    "frontmatter": {
      "mdc": true,
    },
    "frontmatterRaw": "mdc: true
",
    "importChain": undefined,
    "index": 0,
    "level": 1,
    "note": undefined,
    "revision": "kb2y4a",
    "source": {
      "content": "
# MDC{style="color:red"}

:arrow{x1=1 y1=1 x2=2 y2=2}
",
      "contentRaw": "# MDC{style="color:red"}

:arrow{x1=1 y1=1 x2=2 y2=2}",
      "contentStart": 3,
      "end": 8,
      "filepath": "mdc.md",
      "frontmatter": {
        "mdc": true,
      },
      "frontmatterDoc": {
        "mdc": true,
      },
      "frontmatterRaw": "mdc: true
",
      "frontmatterStyle": "frontmatter",
      "index": 0,
      "level": 1,
      "note": undefined,
      "raw": "---
mdc: true
---

# MDC{style="color:red"}

:arrow{x1=1 y1=1 x2=2 y2=2}
",
      "revision": "kb2y4a",
      "start": 0,
      "title": "MDC{style="color:red"}",
    },
    "title": "MDC{style="color:red"}",
  },
]
`;

exports[`md parser > minimal.md > config 1`] = `
{
  "drawings": {
    "enabled": true,
    "persist": false,
    "presenterOnly": false,
    "syncAll": true,
  },
  "fonts": {
    "italic": false,
    "local": [],
    "mono": [
      "ui-monospace",
      "SFMono-Regular",
      "Menlo",
      "Monaco",
      "Consolas",
      ""Liberation Mono"",
      ""Courier New"",
      "monospace",
    ],
    "provider": "google",
    "sans": [
      "ui-sans-serif",
      "system-ui",
      "-apple-system",
      "BlinkMacSystemFont",
      ""Segoe UI"",
      "Roboto",
      ""Helvetica Neue"",
      "Arial",
      ""Noto Sans"",
      "sans-serif",
      ""Apple Color Emoji"",
      ""Segoe UI Emoji"",
      ""Segoe UI Symbol"",
      ""Noto Color Emoji"",
    ],
    "serif": [
      "ui-serif",
      "Georgia",
      "Cambria",
      ""Times New Roman"",
      "Times",
      "serif",
    ],
    "webfonts": [],
    "weights": [
      "200",
      "400",
      "600",
    ],
  },
  "title": "H1",
}
`;

exports[`md parser > minimal.md > features 1`] = `
{
  "katex": false,
  "mermaid": false,
  "monaco": false,
  "tweet": false,
}
`;

exports[`md parser > minimal.md > slides 1`] = `
[
  {
    "content": "# H1
## H2
### H3

Sample Text

\`\`\`ts
console.log('Hello World')
\`\`\`",
    "frontmatter": {},
    "frontmatterRaw": undefined,
    "importChain": undefined,
    "index": 0,
    "level": 1,
    "note": undefined,
    "revision": "-l1yoe",
    "source": {
      "content": "
# H1
## H2
### H3

Sample Text

\`\`\`ts
console.log('Hello World')
\`\`\`
",
      "contentRaw": "# H1
## H2
### H3

Sample Text

\`\`\`ts
console.log('Hello World')
\`\`\`",
      "contentStart": 0,
      "end": 10,
      "filepath": "minimal.md",
      "frontmatter": {},
      "frontmatterDoc": undefined,
      "frontmatterRaw": undefined,
      "frontmatterStyle": undefined,
      "index": 0,
      "level": 1,
      "note": undefined,
      "raw": "
# H1
## H2
### H3

Sample Text

\`\`\`ts
console.log('Hello World')
\`\`\`
",
      "revision": "-l1yoe",
      "start": 0,
      "title": "H1",
    },
    "title": "H1",
  },
  {
    "content": "# Hello

- Hello
- Hi
- Hey
- Yo",
    "frontmatter": {},
    "frontmatterRaw": undefined,
    "importChain": undefined,
    "index": 1,
    "level": 1,
    "note": undefined,
    "revision": "1owo9s",
    "source": {
      "content": "
# Hello

- Hello
- Hi
- Hey
- Yo
",
      "contentRaw": "# Hello

- Hello
- Hi
- Hey
- Yo",
      "contentStart": 11,
      "end": 19,
      "filepath": "minimal.md",
      "frontmatter": {},
      "frontmatterDoc": undefined,
      "frontmatterRaw": undefined,
      "frontmatterStyle": undefined,
      "index": 1,
      "level": 1,
      "note": undefined,
      "raw": "
# Hello

- Hello
- Hi
- Hey
- Yo
",
      "revision": "1owo9s",
      "start": 11,
      "title": "Hello",
    },
    "title": "Hello",
  },
  {
    "content": "Nice to meet you",
    "frontmatter": {},
    "frontmatterRaw": undefined,
    "importChain": undefined,
    "index": 2,
    "level": undefined,
    "note": undefined,
    "revision": "-kcr6u0",
    "source": {
      "content": "
Nice to meet you
",
      "contentRaw": "Nice to meet you",
      "contentStart": 20,
      "end": 23,
      "filepath": "minimal.md",
      "frontmatter": {},
      "frontmatterDoc": undefined,
      "frontmatterRaw": undefined,
      "frontmatterStyle": undefined,
      "index": 2,
      "level": undefined,
      "note": undefined,
      "raw": "
Nice to meet you
",
      "revision": "-kcr6u0",
      "start": 20,
      "title": undefined,
    },
    "title": undefined,
  },
]
`;

exports[`md parser > multi-entries.md > config 1`] = `
{
  "drawings": {
    "enabled": true,
    "persist": false,
    "presenterOnly": false,
    "syncAll": true,
  },
  "fonts": {
    "italic": false,
    "local": [],
    "mono": [
      "ui-monospace",
      "SFMono-Regular",
      "Menlo",
      "Monaco",
      "Consolas",
      ""Liberation Mono"",
      ""Courier New"",
      "monospace",
    ],
    "provider": "google",
    "sans": [
      "ui-sans-serif",
      "system-ui",
      "-apple-system",
      "BlinkMacSystemFont",
      ""Segoe UI"",
      "Roboto",
      ""Helvetica Neue"",
      "Arial",
      ""Noto Sans"",
      "sans-serif",
      ""Apple Color Emoji"",
      ""Segoe UI Emoji"",
      ""Segoe UI Symbol"",
      ""Noto Color Emoji"",
    ],
    "serif": [
      "ui-serif",
      "Georgia",
      "Cambria",
      ""Times New Roman"",
      "Times",
      "serif",
    ],
    "webfonts": [],
    "weights": [
      "200",
      "400",
      "600",
    ],
  },
  "src": "sub/page1.md",
  "title": "Page 1",
}
`;

exports[`md parser > multi-entries.md > features 1`] = `
{
  "katex": true,
  "mermaid": false,
  "monaco": false,
  "tweet": true,
}
`;

exports[`md parser > multi-entries.md > slides 1`] = `
[
  {
    "content": "# Page 1",
    "frontmatter": {},
    "frontmatterRaw": undefined,
    "importChain": [
      {
        "content": "",
        "contentRaw": "",
        "contentStart": 3,
        "end": 4,
        "filepath": "multi-entries.md",
        "frontmatter": {
          "src": "sub/page1.md",
        },
        "frontmatterDoc": {
          "src": "sub/page1.md",
        },
        "frontmatterRaw": "src: sub/page1.md
",
        "frontmatterStyle": "frontmatter",
        "imports": [
          {
            "content": "# Page 1",
            "contentRaw": "# Page 1",
            "contentStart": 0,
            "end": 2,
            "filepath": "sub/page1.md",
            "frontmatter": {},
            "frontmatterDoc": undefined,
            "frontmatterRaw": undefined,
            "frontmatterStyle": undefined,
            "index": 0,
            "level": 1,
            "note": undefined,
            "raw": "# Page 1
",
            "revision": "q9078d",
            "start": 0,
            "title": "Page 1",
          },
        ],
        "index": 0,
        "level": undefined,
        "note": undefined,
        "raw": "---
src: sub/page1.md
---
",
        "revision": "ad0zia",
        "start": 0,
        "title": undefined,
      },
    ],
    "index": 0,
    "level": 1,
    "note": undefined,
    "revision": "q9078d",
    "source": {
      "content": "# Page 1",
      "contentRaw": "# Page 1",
      "contentStart": 0,
      "end": 2,
      "filepath": "sub/page1.md",
      "frontmatter": {},
      "frontmatterDoc": undefined,
      "frontmatterRaw": undefined,
      "frontmatterStyle": undefined,
      "index": 0,
      "level": 1,
      "note": undefined,
      "raw": "# Page 1
",
      "revision": "q9078d",
      "start": 0,
      "title": "Page 1",
    },
    "title": "Page 1",
  },
  {
    "content": "# Page 2

<Tweet />",
    "frontmatter": {
      "background": "https://sli.dev/demo-cover.png#2",
      "layout": "cover",
    },
    "frontmatterRaw": "layout: cover
",
    "importChain": [
      {
        "content": "",
        "contentRaw": "",
        "contentStart": 8,
        "end": 9,
        "filepath": "multi-entries.md",
        "frontmatter": {
          "background": "https://sli.dev/demo-cover.png#2",
          "src": "/sub/page2.md",
        },
        "frontmatterDoc": {
          "background": "https://sli.dev/demo-cover.png#2",
          "src": "/sub/page2.md",
        },
        "frontmatterRaw": "src: /sub/page2.md
background: https://sli.dev/demo-cover.png#2
",
        "frontmatterStyle": "frontmatter",
        "imports": [
          {
            "content": "# Page 2

<Tweet />",
            "contentRaw": "# Page 2

<Tweet />",
            "contentStart": 3,
            "end": 8,
            "filepath": "sub/page2.md",
            "frontmatter": {
              "layout": "cover",
            },
            "frontmatterDoc": {
              "layout": "cover",
            },
            "frontmatterRaw": "layout: cover
",
            "frontmatterStyle": "frontmatter",
            "index": 0,
            "level": 1,
            "note": undefined,
            "raw": "---
layout: cover
---

# Page 2

<Tweet />
",
            "revision": "rdjwxb",
            "start": 0,
            "title": "Page 2",
          },
        ],
        "index": 1,
        "level": undefined,
        "note": undefined,
        "raw": "---
src: /sub/page2.md
background: https://sli.dev/demo-cover.png#2
---
",
        "revision": "-fzej2s",
        "start": 4,
        "title": undefined,
      },
    ],
    "index": 1,
    "level": 1,
    "note": undefined,
    "revision": "rdjwxb",
    "source": {
      "content": "# Page 2

<Tweet />",
      "contentRaw": "# Page 2

<Tweet />",
      "contentStart": 3,
      "end": 8,
      "filepath": "sub/page2.md",
      "frontmatter": {
        "layout": "cover",
      },
      "frontmatterDoc": {
        "layout": "cover",
      },
      "frontmatterRaw": "layout: cover
",
      "frontmatterStyle": "frontmatter",
      "index": 0,
      "level": 1,
      "note": undefined,
      "raw": "---
layout: cover
---

# Page 2

<Tweet />
",
      "revision": "rdjwxb",
      "start": 0,
      "title": "Page 2",
    },
    "title": "Page 2",
  },
  {
    "content": "# Page 3",
    "frontmatter": {
      "background": "https://sli.dev/demo-cover.png#34",
    },
    "frontmatterRaw": undefined,
    "importChain": [
      {
        "content": "",
        "contentRaw": "",
        "contentStart": 13,
        "end": 14,
        "filepath": "multi-entries.md",
        "frontmatter": {
          "background": "https://sli.dev/demo-cover.png#34",
          "src": "./sub/pages3-4.md",
        },
        "frontmatterDoc": {
          "background": "https://sli.dev/demo-cover.png#34",
          "src": "./sub/pages3-4.md",
        },
        "frontmatterRaw": "src: ./sub/pages3-4.md
background: https://sli.dev/demo-cover.png#34
",
        "frontmatterStyle": "frontmatter",
        "imports": [
          {
            "content": "# Page 3",
            "contentRaw": "# Page 3",
            "contentStart": 0,
            "end": 2,
            "filepath": "sub/pages3-4.md",
            "frontmatter": {},
            "frontmatterDoc": undefined,
            "frontmatterRaw": undefined,
            "frontmatterStyle": undefined,
            "index": 0,
            "level": 1,
            "note": undefined,
            "raw": "# Page 3
",
            "revision": "q9078f",
            "start": 0,
            "title": "Page 3",
          },
          {
            "content": "# Page 4

<Tweet />",
            "contentRaw": "# Page 4

<Tweet />",
            "contentStart": 5,
            "end": 10,
            "filepath": "sub/pages3-4.md",
            "frontmatter": {
              "layout": "cover",
            },
            "frontmatterDoc": {
              "layout": "cover",
            },
            "frontmatterRaw": "layout: cover
",
            "frontmatterStyle": "frontmatter",
            "index": 1,
            "level": 1,
            "note": undefined,
            "raw": "---
layout: cover
---

# Page 4

<Tweet />
",
            "revision": "vn9anh",
            "start": 2,
            "title": "Page 4",
          },
        ],
        "index": 2,
        "level": undefined,
        "note": undefined,
        "raw": "---
src: ./sub/pages3-4.md
background: https://sli.dev/demo-cover.png#34
---
",
        "revision": "-y8rid0",
        "start": 9,
        "title": undefined,
      },
    ],
    "index": 2,
    "level": 1,
    "note": undefined,
    "revision": "q9078f",
    "source": {
      "content": "# Page 3",
      "contentRaw": "# Page 3",
      "contentStart": 0,
      "end": 2,
      "filepath": "sub/pages3-4.md",
      "frontmatter": {},
      "frontmatterDoc": undefined,
      "frontmatterRaw": undefined,
      "frontmatterStyle": undefined,
      "index": 0,
      "level": 1,
      "note": undefined,
      "raw": "# Page 3
",
      "revision": "q9078f",
      "start": 0,
      "title": "Page 3",
    },
    "title": "Page 3",
  },
  {
    "content": "# Page 4

<Tweet />",
    "frontmatter": {
      "background": "https://sli.dev/demo-cover.png#34",
      "layout": "cover",
    },
    "frontmatterRaw": "layout: cover
",
    "importChain": [
      {
        "content": "",
        "contentRaw": "",
        "contentStart": 13,
        "end": 14,
        "filepath": "multi-entries.md",
        "frontmatter": {
          "background": "https://sli.dev/demo-cover.png#34",
          "src": "./sub/pages3-4.md",
        },
        "frontmatterDoc": {
          "background": "https://sli.dev/demo-cover.png#34",
          "src": "./sub/pages3-4.md",
        },
        "frontmatterRaw": "src: ./sub/pages3-4.md
background: https://sli.dev/demo-cover.png#34
",
        "frontmatterStyle": "frontmatter",
        "imports": [
          {
            "content": "# Page 3",
            "contentRaw": "# Page 3",
            "contentStart": 0,
            "end": 2,
            "filepath": "sub/pages3-4.md",
            "frontmatter": {},
            "frontmatterDoc": undefined,
            "frontmatterRaw": undefined,
            "frontmatterStyle": undefined,
            "index": 0,
            "level": 1,
            "note": undefined,
            "raw": "# Page 3
",
            "revision": "q9078f",
            "start": 0,
            "title": "Page 3",
          },
          {
            "content": "# Page 4

<Tweet />",
            "contentRaw": "# Page 4

<Tweet />",
            "contentStart": 5,
            "end": 10,
            "filepath": "sub/pages3-4.md",
            "frontmatter": {
              "layout": "cover",
            },
            "frontmatterDoc": {
              "layout": "cover",
            },
            "frontmatterRaw": "layout: cover
",
            "frontmatterStyle": "frontmatter",
            "index": 1,
            "level": 1,
            "note": undefined,
            "raw": "---
layout: cover
---

# Page 4

<Tweet />
",
            "revision": "vn9anh",
            "start": 2,
            "title": "Page 4",
          },
        ],
        "index": 2,
        "level": undefined,
        "note": undefined,
        "raw": "---
src: ./sub/pages3-4.md
background: https://sli.dev/demo-cover.png#34
---
",
        "revision": "-y8rid0",
        "start": 9,
        "title": undefined,
      },
    ],
    "index": 3,
    "level": 1,
    "note": undefined,
    "revision": "vn9anh",
    "source": {
      "content": "# Page 4

<Tweet />",
      "contentRaw": "# Page 4

<Tweet />",
      "contentStart": 5,
      "end": 10,
      "filepath": "sub/pages3-4.md",
      "frontmatter": {
        "layout": "cover",
      },
      "frontmatterDoc": {
        "layout": "cover",
      },
      "frontmatterRaw": "layout: cover
",
      "frontmatterStyle": "frontmatter",
      "index": 1,
      "level": 1,
      "note": undefined,
      "raw": "---
layout: cover
---

# Page 4

<Tweet />
",
      "revision": "vn9anh",
      "start": 2,
      "title": "Page 4",
    },
    "title": "Page 4",
  },
  {
    "content": "# Page 1",
    "frontmatter": {
      "background": "https://sli.dev/demo-cover.png#14",
    },
    "frontmatterRaw": undefined,
    "importChain": [
      {
        "content": "",
        "contentRaw": "",
        "contentStart": 18,
        "end": 19,
        "filepath": "multi-entries.md",
        "frontmatter": {
          "background": "https://sli.dev/demo-cover.png#14",
          "src": "sub/nested1-4.md",
        },
        "frontmatterDoc": {
          "background": "https://sli.dev/demo-cover.png#14",
          "src": "sub/nested1-4.md",
        },
        "frontmatterRaw": "src: sub/nested1-4.md
background: https://sli.dev/demo-cover.png#14
",
        "frontmatterStyle": "frontmatter",
        "imports": [
          {
            "content": "",
            "contentRaw": "",
            "contentStart": 3,
            "end": 4,
            "filepath": "sub/nested1-4.md",
            "frontmatter": {
              "src": "/sub/page1.md",
            },
            "frontmatterDoc": {
              "src": "/sub/page1.md",
            },
            "frontmatterRaw": "src: /sub/page1.md
",
            "frontmatterStyle": "frontmatter",
            "imports": [
              {
                "content": "# Page 1",
                "contentRaw": "# Page 1",
                "contentStart": 0,
                "end": 2,
                "filepath": "sub/page1.md",
                "frontmatter": {},
                "frontmatterDoc": undefined,
                "frontmatterRaw": undefined,
                "frontmatterStyle": undefined,
                "index": 0,
                "level": 1,
                "note": undefined,
                "raw": "# Page 1
",
                "revision": "q9078d",
                "start": 0,
                "title": "Page 1",
              },
            ],
            "index": 0,
            "level": undefined,
            "note": undefined,
            "raw": "---
src: /sub/page1.md
---
",
            "revision": "-kzazk9",
            "start": 0,
            "title": undefined,
          },
          {
            "content": "",
            "contentRaw": "",
            "contentStart": 7,
            "end": 8,
            "filepath": "sub/nested1-4.md",
            "frontmatter": {
              "src": "page2.md",
            },
            "frontmatterDoc": {
              "src": "page2.md",
            },
            "frontmatterRaw": "src: page2.md
",
            "frontmatterStyle": "frontmatter",
            "imports": [
              {
                "content": "# Page 2

<Tweet />",
                "contentRaw": "# Page 2

<Tweet />",
                "contentStart": 3,
                "end": 8,
                "filepath": "sub/page2.md",
                "frontmatter": {
                  "layout": "cover",
                },
                "frontmatterDoc": {
                  "layout": "cover",
                },
                "frontmatterRaw": "layout: cover
",
                "frontmatterStyle": "frontmatter",
                "index": 0,
                "level": 1,
                "note": undefined,
                "raw": "---
layout: cover
---

# Page 2

<Tweet />
",
                "revision": "rdjwxb",
                "start": 0,
                "title": "Page 2",
              },
            ],
            "index": 1,
            "level": undefined,
            "note": undefined,
            "raw": "---
src: page2.md
---
",
            "revision": "koer3m",
            "start": 4,
            "title": undefined,
          },
          {
            "content": "",
            "contentRaw": "",
            "contentStart": 11,
            "end": 12,
            "filepath": "sub/nested1-4.md",
            "frontmatter": {
              "src": "../sub/pages3-4.md",
            },
            "frontmatterDoc": {
              "src": "../sub/pages3-4.md",
            },
            "frontmatterRaw": "src: ../sub/pages3-4.md
",
            "frontmatterStyle": "frontmatter",
            "imports": [
              {
                "content": "# Page 3",
                "contentRaw": "# Page 3",
                "contentStart": 0,
                "end": 2,
                "filepath": "sub/pages3-4.md",
                "frontmatter": {},
                "frontmatterDoc": undefined,
                "frontmatterRaw": undefined,
                "frontmatterStyle": undefined,
                "index": 0,
                "level": 1,
                "note": undefined,
                "raw": "# Page 3
",
                "revision": "q9078f",
                "start": 0,
                "title": "Page 3",
              },
              {
                "content": "# Page 4

<Tweet />",
                "contentRaw": "# Page 4

<Tweet />",
                "contentStart": 5,
                "end": 10,
                "filepath": "sub/pages3-4.md",
                "frontmatter": {
                  "layout": "cover",
                },
                "frontmatterDoc": {
                  "layout": "cover",
                },
                "frontmatterRaw": "layout: cover
",
                "frontmatterStyle": "frontmatter",
                "index": 1,
                "level": 1,
                "note": undefined,
                "raw": "---
layout: cover
---

# Page 4

<Tweet />
",
                "revision": "vn9anh",
                "start": 2,
                "title": "Page 4",
              },
            ],
            "index": 2,
            "level": undefined,
            "note": undefined,
            "raw": "---
src: ../sub/pages3-4.md
---
",
            "revision": "-9fgden",
            "start": 8,
            "title": undefined,
          },
        ],
        "index": 3,
        "level": undefined,
        "note": undefined,
        "raw": "---
src: sub/nested1-4.md
background: https://sli.dev/demo-cover.png#14
---
",
        "revision": "-bcli90",
        "start": 14,
        "title": undefined,
      },
      {
        "content": "",
        "contentRaw": "",
        "contentStart": 3,
        "end": 4,
        "filepath": "sub/nested1-4.md",
        "frontmatter": {
          "src": "/sub/page1.md",
        },
        "frontmatterDoc": {
          "src": "/sub/page1.md",
        },
        "frontmatterRaw": "src: /sub/page1.md
",
        "frontmatterStyle": "frontmatter",
        "imports": [
          {
            "content": "# Page 1",
            "contentRaw": "# Page 1",
            "contentStart": 0,
            "end": 2,
            "filepath": "sub/page1.md",
            "frontmatter": {},
            "frontmatterDoc": undefined,
            "frontmatterRaw": undefined,
            "frontmatterStyle": undefined,
            "index": 0,
            "level": 1,
            "note": undefined,
            "raw": "# Page 1
",
            "revision": "q9078d",
            "start": 0,
            "title": "Page 1",
          },
        ],
        "index": 0,
        "level": undefined,
        "note": undefined,
        "raw": "---
src: /sub/page1.md
---
",
        "revision": "-kzazk9",
        "start": 0,
        "title": undefined,
      },
    ],
    "index": 4,
    "level": 1,
    "note": undefined,
    "revision": "q9078d",
    "source": {
      "content": "# Page 1",
      "contentRaw": "# Page 1",
      "contentStart": 0,
      "end": 2,
      "filepath": "sub/page1.md",
      "frontmatter": {},
      "frontmatterDoc": undefined,
      "frontmatterRaw": undefined,
      "frontmatterStyle": undefined,
      "index": 0,
      "level": 1,
      "note": undefined,
      "raw": "# Page 1
",
      "revision": "q9078d",
      "start": 0,
      "title": "Page 1",
    },
    "title": "Page 1",
  },
  {
    "content": "# Page 2

<Tweet />",
    "frontmatter": {
      "background": "https://sli.dev/demo-cover.png#14",
      "layout": "cover",
    },
    "frontmatterRaw": "layout: cover
",
    "importChain": [
      {
        "content": "",
        "contentRaw": "",
        "contentStart": 18,
        "end": 19,
        "filepath": "multi-entries.md",
        "frontmatter": {
          "background": "https://sli.dev/demo-cover.png#14",
          "src": "sub/nested1-4.md",
        },
        "frontmatterDoc": {
          "background": "https://sli.dev/demo-cover.png#14",
          "src": "sub/nested1-4.md",
        },
        "frontmatterRaw": "src: sub/nested1-4.md
background: https://sli.dev/demo-cover.png#14
",
        "frontmatterStyle": "frontmatter",
        "imports": [
          {
            "content": "",
            "contentRaw": "",
            "contentStart": 3,
            "end": 4,
            "filepath": "sub/nested1-4.md",
            "frontmatter": {
              "src": "/sub/page1.md",
            },
            "frontmatterDoc": {
              "src": "/sub/page1.md",
            },
            "frontmatterRaw": "src: /sub/page1.md
",
            "frontmatterStyle": "frontmatter",
            "imports": [
              {
                "content": "# Page 1",
                "contentRaw": "# Page 1",
                "contentStart": 0,
                "end": 2,
                "filepath": "sub/page1.md",
                "frontmatter": {},
                "frontmatterDoc": undefined,
                "frontmatterRaw": undefined,
                "frontmatterStyle": undefined,
                "index": 0,
                "level": 1,
                "note": undefined,
                "raw": "# Page 1
",
                "revision": "q9078d",
                "start": 0,
                "title": "Page 1",
              },
            ],
            "index": 0,
            "level": undefined,
            "note": undefined,
            "raw": "---
src: /sub/page1.md
---
",
            "revision": "-kzazk9",
            "start": 0,
            "title": undefined,
          },
          {
            "content": "",
            "contentRaw": "",
            "contentStart": 7,
            "end": 8,
            "filepath": "sub/nested1-4.md",
            "frontmatter": {
              "src": "page2.md",
            },
            "frontmatterDoc": {
              "src": "page2.md",
            },
            "frontmatterRaw": "src: page2.md
",
            "frontmatterStyle": "frontmatter",
            "imports": [
              {
                "content": "# Page 2

<Tweet />",
                "contentRaw": "# Page 2

<Tweet />",
                "contentStart": 3,
                "end": 8,
                "filepath": "sub/page2.md",
                "frontmatter": {
                  "layout": "cover",
                },
                "frontmatterDoc": {
                  "layout": "cover",
                },
                "frontmatterRaw": "layout: cover
",
                "frontmatterStyle": "frontmatter",
                "index": 0,
                "level": 1,
                "note": undefined,
                "raw": "---
layout: cover
---

# Page 2

<Tweet />
",
                "revision": "rdjwxb",
                "start": 0,
                "title": "Page 2",
              },
            ],
            "index": 1,
            "level": undefined,
            "note": undefined,
            "raw": "---
src: page2.md
---
",
            "revision": "koer3m",
            "start": 4,
            "title": undefined,
          },
          {
            "content": "",
            "contentRaw": "",
            "contentStart": 11,
            "end": 12,
            "filepath": "sub/nested1-4.md",
            "frontmatter": {
              "src": "../sub/pages3-4.md",
            },
            "frontmatterDoc": {
              "src": "../sub/pages3-4.md",
            },
            "frontmatterRaw": "src: ../sub/pages3-4.md
",
            "frontmatterStyle": "frontmatter",
            "imports": [
              {
                "content": "# Page 3",
                "contentRaw": "# Page 3",
                "contentStart": 0,
                "end": 2,
                "filepath": "sub/pages3-4.md",
                "frontmatter": {},
                "frontmatterDoc": undefined,
                "frontmatterRaw": undefined,
                "frontmatterStyle": undefined,
                "index": 0,
                "level": 1,
                "note": undefined,
                "raw": "# Page 3
",
                "revision": "q9078f",
                "start": 0,
                "title": "Page 3",
              },
              {
                "content": "# Page 4

<Tweet />",
                "contentRaw": "# Page 4

<Tweet />",
                "contentStart": 5,
                "end": 10,
                "filepath": "sub/pages3-4.md",
                "frontmatter": {
                  "layout": "cover",
                },
                "frontmatterDoc": {
                  "layout": "cover",
                },
                "frontmatterRaw": "layout: cover
",
                "frontmatterStyle": "frontmatter",
                "index": 1,
                "level": 1,
                "note": undefined,
                "raw": "---
layout: cover
---

# Page 4

<Tweet />
",
                "revision": "vn9anh",
                "start": 2,
                "title": "Page 4",
              },
            ],
            "index": 2,
            "level": undefined,
            "note": undefined,
            "raw": "---
src: ../sub/pages3-4.md
---
",
            "revision": "-9fgden",
            "start": 8,
            "title": undefined,
          },
        ],
        "index": 3,
        "level": undefined,
        "note": undefined,
        "raw": "---
src: sub/nested1-4.md
background: https://sli.dev/demo-cover.png#14
---
",
        "revision": "-bcli90",
        "start": 14,
        "title": undefined,
      },
      {
        "content": "",
        "contentRaw": "",
        "contentStart": 7,
        "end": 8,
        "filepath": "sub/nested1-4.md",
        "frontmatter": {
          "src": "page2.md",
        },
        "frontmatterDoc": {
          "src": "page2.md",
        },
        "frontmatterRaw": "src: page2.md
",
        "frontmatterStyle": "frontmatter",
        "imports": [
          {
            "content": "# Page 2

<Tweet />",
            "contentRaw": "# Page 2

<Tweet />",
            "contentStart": 3,
            "end": 8,
            "filepath": "sub/page2.md",
            "frontmatter": {
              "layout": "cover",
            },
            "frontmatterDoc": {
              "layout": "cover",
            },
            "frontmatterRaw": "layout: cover
",
            "frontmatterStyle": "frontmatter",
            "index": 0,
            "level": 1,
            "note": undefined,
            "raw": "---
layout: cover
---

# Page 2

<Tweet />
",
            "revision": "rdjwxb",
            "start": 0,
            "title": "Page 2",
          },
        ],
        "index": 1,
        "level": undefined,
        "note": undefined,
        "raw": "---
src: page2.md
---
",
        "revision": "koer3m",
        "start": 4,
        "title": undefined,
      },
    ],
    "index": 5,
    "level": 1,
    "note": undefined,
    "revision": "rdjwxb",
    "source": {
      "content": "# Page 2

<Tweet />",
      "contentRaw": "# Page 2

<Tweet />",
      "contentStart": 3,
      "end": 8,
      "filepath": "sub/page2.md",
      "frontmatter": {
        "layout": "cover",
      },
      "frontmatterDoc": {
        "layout": "cover",
      },
      "frontmatterRaw": "layout: cover
",
      "frontmatterStyle": "frontmatter",
      "index": 0,
      "level": 1,
      "note": undefined,
      "raw": "---
layout: cover
---

# Page 2

<Tweet />
",
      "revision": "rdjwxb",
      "start": 0,
      "title": "Page 2",
    },
    "title": "Page 2",
  },
  {
    "content": "# Page 3",
    "frontmatter": {
      "background": "https://sli.dev/demo-cover.png#14",
    },
    "frontmatterRaw": undefined,
    "importChain": [
      {
        "content": "",
        "contentRaw": "",
        "contentStart": 18,
        "end": 19,
        "filepath": "multi-entries.md",
        "frontmatter": {
          "background": "https://sli.dev/demo-cover.png#14",
          "src": "sub/nested1-4.md",
        },
        "frontmatterDoc": {
          "background": "https://sli.dev/demo-cover.png#14",
          "src": "sub/nested1-4.md",
        },
        "frontmatterRaw": "src: sub/nested1-4.md
background: https://sli.dev/demo-cover.png#14
",
        "frontmatterStyle": "frontmatter",
        "imports": [
          {
            "content": "",
            "contentRaw": "",
            "contentStart": 3,
            "end": 4,
            "filepath": "sub/nested1-4.md",
            "frontmatter": {
              "src": "/sub/page1.md",
            },
            "frontmatterDoc": {
              "src": "/sub/page1.md",
            },
            "frontmatterRaw": "src: /sub/page1.md
",
            "frontmatterStyle": "frontmatter",
            "imports": [
              {
                "content": "# Page 1",
                "contentRaw": "# Page 1",
                "contentStart": 0,
                "end": 2,
                "filepath": "sub/page1.md",
                "frontmatter": {},
                "frontmatterDoc": undefined,
                "frontmatterRaw": undefined,
                "frontmatterStyle": undefined,
                "index": 0,
                "level": 1,
                "note": undefined,
                "raw": "# Page 1
",
                "revision": "q9078d",
                "start": 0,
                "title": "Page 1",
              },
            ],
            "index": 0,
            "level": undefined,
            "note": undefined,
            "raw": "---
src: /sub/page1.md
---
",
            "revision": "-kzazk9",
            "start": 0,
            "title": undefined,
          },
          {
            "content": "",
            "contentRaw": "",
            "contentStart": 7,
            "end": 8,
            "filepath": "sub/nested1-4.md",
            "frontmatter": {
              "src": "page2.md",
            },
            "frontmatterDoc": {
              "src": "page2.md",
            },
            "frontmatterRaw": "src: page2.md
",
            "frontmatterStyle": "frontmatter",
            "imports": [
              {
                "content": "# Page 2

<Tweet />",
                "contentRaw": "# Page 2

<Tweet />",
                "contentStart": 3,
                "end": 8,
                "filepath": "sub/page2.md",
                "frontmatter": {
                  "layout": "cover",
                },
                "frontmatterDoc": {
                  "layout": "cover",
                },
                "frontmatterRaw": "layout: cover
",
                "frontmatterStyle": "frontmatter",
                "index": 0,
                "level": 1,
                "note": undefined,
                "raw": "---
layout: cover
---

# Page 2

<Tweet />
",
                "revision": "rdjwxb",
                "start": 0,
                "title": "Page 2",
              },
            ],
            "index": 1,
            "level": undefined,
            "note": undefined,
            "raw": "---
src: page2.md
---
",
            "revision": "koer3m",
            "start": 4,
            "title": undefined,
          },
          {
            "content": "",
            "contentRaw": "",
            "contentStart": 11,
            "end": 12,
            "filepath": "sub/nested1-4.md",
            "frontmatter": {
              "src": "../sub/pages3-4.md",
            },
            "frontmatterDoc": {
              "src": "../sub/pages3-4.md",
            },
            "frontmatterRaw": "src: ../sub/pages3-4.md
",
            "frontmatterStyle": "frontmatter",
            "imports": [
              {
                "content": "# Page 3",
                "contentRaw": "# Page 3",
                "contentStart": 0,
                "end": 2,
                "filepath": "sub/pages3-4.md",
                "frontmatter": {},
                "frontmatterDoc": undefined,
                "frontmatterRaw": undefined,
                "frontmatterStyle": undefined,
                "index": 0,
                "level": 1,
                "note": undefined,
                "raw": "# Page 3
",
                "revision": "q9078f",
                "start": 0,
                "title": "Page 3",
              },
              {
                "content": "# Page 4

<Tweet />",
                "contentRaw": "# Page 4

<Tweet />",
                "contentStart": 5,
                "end": 10,
                "filepath": "sub/pages3-4.md",
                "frontmatter": {
                  "layout": "cover",
                },
                "frontmatterDoc": {
                  "layout": "cover",
                },
                "frontmatterRaw": "layout: cover
",
                "frontmatterStyle": "frontmatter",
                "index": 1,
                "level": 1,
                "note": undefined,
                "raw": "---
layout: cover
---

# Page 4

<Tweet />
",
                "revision": "vn9anh",
                "start": 2,
                "title": "Page 4",
              },
            ],
            "index": 2,
            "level": undefined,
            "note": undefined,
            "raw": "---
src: ../sub/pages3-4.md
---
",
            "revision": "-9fgden",
            "start": 8,
            "title": undefined,
          },
        ],
        "index": 3,
        "level": undefined,
        "note": undefined,
        "raw": "---
src: sub/nested1-4.md
background: https://sli.dev/demo-cover.png#14
---
",
        "revision": "-bcli90",
        "start": 14,
        "title": undefined,
      },
      {
        "content": "",
        "contentRaw": "",
        "contentStart": 11,
        "end": 12,
        "filepath": "sub/nested1-4.md",
        "frontmatter": {
          "src": "../sub/pages3-4.md",
        },
        "frontmatterDoc": {
          "src": "../sub/pages3-4.md",
        },
        "frontmatterRaw": "src: ../sub/pages3-4.md
",
        "frontmatterStyle": "frontmatter",
        "imports": [
          {
            "content": "# Page 3",
            "contentRaw": "# Page 3",
            "contentStart": 0,
            "end": 2,
            "filepath": "sub/pages3-4.md",
            "frontmatter": {},
            "frontmatterDoc": undefined,
            "frontmatterRaw": undefined,
            "frontmatterStyle": undefined,
            "index": 0,
            "level": 1,
            "note": undefined,
            "raw": "# Page 3
",
            "revision": "q9078f",
            "start": 0,
            "title": "Page 3",
          },
          {
            "content": "# Page 4

<Tweet />",
            "contentRaw": "# Page 4

<Tweet />",
            "contentStart": 5,
            "end": 10,
            "filepath": "sub/pages3-4.md",
            "frontmatter": {
              "layout": "cover",
            },
            "frontmatterDoc": {
              "layout": "cover",
            },
            "frontmatterRaw": "layout: cover
",
            "frontmatterStyle": "frontmatter",
            "index": 1,
            "level": 1,
            "note": undefined,
            "raw": "---
layout: cover
---

# Page 4

<Tweet />
",
            "revision": "vn9anh",
            "start": 2,
            "title": "Page 4",
          },
        ],
        "index": 2,
        "level": undefined,
        "note": undefined,
        "raw": "---
src: ../sub/pages3-4.md
---
",
        "revision": "-9fgden",
        "start": 8,
        "title": undefined,
      },
    ],
    "index": 6,
    "level": 1,
    "note": undefined,
    "revision": "q9078f",
    "source": {
      "content": "# Page 3",
      "contentRaw": "# Page 3",
      "contentStart": 0,
      "end": 2,
      "filepath": "sub/pages3-4.md",
      "frontmatter": {},
      "frontmatterDoc": undefined,
      "frontmatterRaw": undefined,
      "frontmatterStyle": undefined,
      "index": 0,
      "level": 1,
      "note": undefined,
      "raw": "# Page 3
",
      "revision": "q9078f",
      "start": 0,
      "title": "Page 3",
    },
    "title": "Page 3",
  },
  {
    "content": "# Page 4

<Tweet />",
    "frontmatter": {
      "background": "https://sli.dev/demo-cover.png#14",
      "layout": "cover",
    },
    "frontmatterRaw": "layout: cover
",
    "importChain": [
      {
        "content": "",
        "contentRaw": "",
        "contentStart": 18,
        "end": 19,
        "filepath": "multi-entries.md",
        "frontmatter": {
          "background": "https://sli.dev/demo-cover.png#14",
          "src": "sub/nested1-4.md",
        },
        "frontmatterDoc": {
          "background": "https://sli.dev/demo-cover.png#14",
          "src": "sub/nested1-4.md",
        },
        "frontmatterRaw": "src: sub/nested1-4.md
background: https://sli.dev/demo-cover.png#14
",
        "frontmatterStyle": "frontmatter",
        "imports": [
          {
            "content": "",
            "contentRaw": "",
            "contentStart": 3,
            "end": 4,
            "filepath": "sub/nested1-4.md",
            "frontmatter": {
              "src": "/sub/page1.md",
            },
            "frontmatterDoc": {
              "src": "/sub/page1.md",
            },
            "frontmatterRaw": "src: /sub/page1.md
",
            "frontmatterStyle": "frontmatter",
            "imports": [
              {
                "content": "# Page 1",
                "contentRaw": "# Page 1",
                "contentStart": 0,
                "end": 2,
                "filepath": "sub/page1.md",
                "frontmatter": {},
                "frontmatterDoc": undefined,
                "frontmatterRaw": undefined,
                "frontmatterStyle": undefined,
                "index": 0,
                "level": 1,
                "note": undefined,
                "raw": "# Page 1
",
                "revision": "q9078d",
                "start": 0,
                "title": "Page 1",
              },
            ],
            "index": 0,
            "level": undefined,
            "note": undefined,
            "raw": "---
src: /sub/page1.md
---
",
            "revision": "-kzazk9",
            "start": 0,
            "title": undefined,
          },
          {
            "content": "",
            "contentRaw": "",
            "contentStart": 7,
            "end": 8,
            "filepath": "sub/nested1-4.md",
            "frontmatter": {
              "src": "page2.md",
            },
            "frontmatterDoc": {
              "src": "page2.md",
            },
            "frontmatterRaw": "src: page2.md
",
            "frontmatterStyle": "frontmatter",
            "imports": [
              {
                "content": "# Page 2

<Tweet />",
                "contentRaw": "# Page 2

<Tweet />",
                "contentStart": 3,
                "end": 8,
                "filepath": "sub/page2.md",
                "frontmatter": {
                  "layout": "cover",
                },
                "frontmatterDoc": {
                  "layout": "cover",
                },
                "frontmatterRaw": "layout: cover
",
                "frontmatterStyle": "frontmatter",
                "index": 0,
                "level": 1,
                "note": undefined,
                "raw": "---
layout: cover
---

# Page 2

<Tweet />
",
                "revision": "rdjwxb",
                "start": 0,
                "title": "Page 2",
              },
            ],
            "index": 1,
            "level": undefined,
            "note": undefined,
            "raw": "---
src: page2.md
---
",
            "revision": "koer3m",
            "start": 4,
            "title": undefined,
          },
          {
            "content": "",
            "contentRaw": "",
            "contentStart": 11,
            "end": 12,
            "filepath": "sub/nested1-4.md",
            "frontmatter": {
              "src": "../sub/pages3-4.md",
            },
            "frontmatterDoc": {
              "src": "../sub/pages3-4.md",
            },
            "frontmatterRaw": "src: ../sub/pages3-4.md
",
            "frontmatterStyle": "frontmatter",
            "imports": [
              {
                "content": "# Page 3",
                "contentRaw": "# Page 3",
                "contentStart": 0,
                "end": 2,
                "filepath": "sub/pages3-4.md",
                "frontmatter": {},
                "frontmatterDoc": undefined,
                "frontmatterRaw": undefined,
                "frontmatterStyle": undefined,
                "index": 0,
                "level": 1,
                "note": undefined,
                "raw": "# Page 3
",
                "revision": "q9078f",
                "start": 0,
                "title": "Page 3",
              },
              {
                "content": "# Page 4

<Tweet />",
                "contentRaw": "# Page 4

<Tweet />",
                "contentStart": 5,
                "end": 10,
                "filepath": "sub/pages3-4.md",
                "frontmatter": {
                  "layout": "cover",
                },
                "frontmatterDoc": {
                  "layout": "cover",
                },
                "frontmatterRaw": "layout: cover
",
                "frontmatterStyle": "frontmatter",
                "index": 1,
                "level": 1,
                "note": undefined,
                "raw": "---
layout: cover
---

# Page 4

<Tweet />
",
                "revision": "vn9anh",
                "start": 2,
                "title": "Page 4",
              },
            ],
            "index": 2,
            "level": undefined,
            "note": undefined,
            "raw": "---
src: ../sub/pages3-4.md
---
",
            "revision": "-9fgden",
            "start": 8,
            "title": undefined,
          },
        ],
        "index": 3,
        "level": undefined,
        "note": undefined,
        "raw": "---
src: sub/nested1-4.md
background: https://sli.dev/demo-cover.png#14
---
",
        "revision": "-bcli90",
        "start": 14,
        "title": undefined,
      },
      {
        "content": "",
        "contentRaw": "",
        "contentStart": 11,
        "end": 12,
        "filepath": "sub/nested1-4.md",
        "frontmatter": {
          "src": "../sub/pages3-4.md",
        },
        "frontmatterDoc": {
          "src": "../sub/pages3-4.md",
        },
        "frontmatterRaw": "src: ../sub/pages3-4.md
",
        "frontmatterStyle": "frontmatter",
        "imports": [
          {
            "content": "# Page 3",
            "contentRaw": "# Page 3",
            "contentStart": 0,
            "end": 2,
            "filepath": "sub/pages3-4.md",
            "frontmatter": {},
            "frontmatterDoc": undefined,
            "frontmatterRaw": undefined,
            "frontmatterStyle": undefined,
            "index": 0,
            "level": 1,
            "note": undefined,
            "raw": "# Page 3
",
            "revision": "q9078f",
            "start": 0,
            "title": "Page 3",
          },
          {
            "content": "# Page 4

<Tweet />",
            "contentRaw": "# Page 4

<Tweet />",
            "contentStart": 5,
            "end": 10,
            "filepath": "sub/pages3-4.md",
            "frontmatter": {
              "layout": "cover",
            },
            "frontmatterDoc": {
              "layout": "cover",
            },
            "frontmatterRaw": "layout: cover
",
            "frontmatterStyle": "frontmatter",
            "index": 1,
            "level": 1,
            "note": undefined,
            "raw": "---
layout: cover
---

# Page 4

<Tweet />
",
            "revision": "vn9anh",
            "start": 2,
            "title": "Page 4",
          },
        ],
        "index": 2,
        "level": undefined,
        "note": undefined,
        "raw": "---
src: ../sub/pages3-4.md
---
",
        "revision": "-9fgden",
        "start": 8,
        "title": undefined,
      },
    ],
    "index": 7,
    "level": 1,
    "note": undefined,
    "revision": "vn9anh",
    "source": {
      "content": "# Page 4

<Tweet />",
      "contentRaw": "# Page 4

<Tweet />",
      "contentStart": 5,
      "end": 10,
      "filepath": "sub/pages3-4.md",
      "frontmatter": {
        "layout": "cover",
      },
      "frontmatterDoc": {
        "layout": "cover",
      },
      "frontmatterRaw": "layout: cover
",
      "frontmatterStyle": "frontmatter",
      "index": 1,
      "level": 1,
      "note": undefined,
      "raw": "---
layout: cover
---

# Page 4

<Tweet />
",
      "revision": "vn9anh",
      "start": 2,
      "title": "Page 4",
    },
    "title": "Page 4",
  },
  {
    "content": "# Inline Page

$x+2$",
    "frontmatter": {},
    "frontmatterRaw": undefined,
    "importChain": undefined,
    "index": 8,
    "level": 1,
    "note": undefined,
    "revision": "6kem5s",
    "source": {
      "content": "
# Inline Page

$x+2$
",
      "contentRaw": "# Inline Page

$x+2$",
      "contentStart": 20,
      "end": 25,
      "filepath": "multi-entries.md",
      "frontmatter": {},
      "frontmatterDoc": undefined,
      "frontmatterRaw": undefined,
      "frontmatterStyle": undefined,
      "index": 4,
      "level": 1,
      "note": undefined,
      "raw": "
# Inline Page

$x+2$
",
      "revision": "6kem5s",
      "start": 20,
      "title": "Inline Page",
    },
    "title": "Inline Page",
  },
]
`;
