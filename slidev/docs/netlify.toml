[build]
publish = ".vitepress/dist"
command = "pnpm run build"

[build.environment]
NODE_VERSION = "20"
PLAYWRIGHT_BROWSERS_PATH = "0"

[[redirects]]
from = "/new"
to = "https://stackblitz.com/github/slidevjs/new?file=slides.md"
status = 302
force = true

[[redirects]]
from = "https://slidev.antfu.me/*"
to = "https://sli.dev/:splat"
status = 301
force = true

[[redirects]]
from = "/demo/composable-vue/*"
to = "https://demo.sli.dev/composable-vue"
status = 301
force = true

[[redirects]]
from = "/demo/starter/*"
to = "https://demo.sli.dev/starter"
status = 301
force = true

[[redirects]]
from = "/*"
to = "/index.html"
status = 200
