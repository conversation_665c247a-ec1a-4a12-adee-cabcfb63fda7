{"compilerOptions": {"target": "ESNext", "jsx": "preserve", "lib": ["DOM", "ESNext"], "baseUrl": ".", "module": "ESNext", "moduleResolution": "bundler", "resolveJsonModule": true, "types": ["vite/client", "node"], "strict": true, "strictNullChecks": true, "noUnusedLocals": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true}, "include": ["./*.ts", "./.vitepress/**/*.ts", "./.vitepress/**/*.vue"], "exclude": ["**/dist/**", "node_modules"]}