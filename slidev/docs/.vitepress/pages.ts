export const Guides = [
  {
    text: 'Why Slidev',
    link: '/guide/why',
  },
  {
    text: 'Getting Started',
    link: '/guide/',
  },
  {
    text: 'Syntax Guide',
    link: '/guide/syntax',
  },
  {
    text: 'User Interface',
    link: '/guide/ui',
  },
  {
    text: 'Animations',
    link: '/guide/animations',
  },
  {
    text: 'Theme & Addons',
    link: '/guide/theme-addon',
  },
  {
    text: 'Components',
    link: '/guide/component',
  },
  {
    text: 'Layouts',
    link: '/guide/layout',
  },
  {
    text: 'Exporting',
    link: '/guide/exporting',
  },
  {
    text: 'Hosting',
    link: '/guide/hosting',
  },
  {
    text: 'FAQ',
    link: '/guide/faq',
  },
]

export const BuiltIn = [
  {
    text: 'CLI',
    link: '/builtin/cli',
  },
  {
    text: 'Components',
    link: '/builtin/components',
  },
  {
    text: 'Layouts',
    link: '/builtin/layouts',
  },
]

export const Advanced = [
  {
    text: 'Global Context',
    link: '/guide/global-context',
  },
  {
    text: 'Writing Layouts',
    link: '/guide/write-layout',
  },
  {
    text: 'Writing Themes',
    link: '/guide/write-theme',
  },
  {
    text: 'Writing Addons',
    link: '/guide/write-addon',
  },
]

export const Resources = [
  {
    text: 'Showcases',
    link: '/resources/showcases',
  },
  {
    text: 'Theme Gallery',
    link: '/resources/theme-gallery',
  },
  {
    text: 'Addon Gallery',
    link: '/resources/addon-gallery',
  },
  {
    text: 'Learning Resources',
    link: '/resources/learning',
  },
  {
    text: 'Curated Covers',
    link: '/resources/covers',
  },
  {
    text: 'Release Notes',
    link: 'https://github.com/slidevjs/slidev/releases',
  },
]
