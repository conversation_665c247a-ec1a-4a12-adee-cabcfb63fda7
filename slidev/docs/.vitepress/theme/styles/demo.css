html:not(.dark) {
  --prism-foreground: #393a34;
  --prism-background: #fafafa;
  --prism-inline-background: #f5f5f5;
  --prism-comment: #a0ada0;
  --prism-string: #b56959;
  --prism-literal: #2f8a89;
  --prism-number: #296aa3;
  --prism-keyword: #1c6b48;
  --prism-function: #6c7834;
  --prism-boolean: #296aa3;
  --prism-constant: #a65e2b;
  --prism-deleted: #a14f55;
  --prism-class: #2993a3;
  --prism-builtin: #ab5959;
  --prism-property: #b58451;
  --prism-namespace: #b05a78;
  --prism-punctuation: #8e8f8b;
  --prism-decorator: #bd8f8f;
  --prism-regex: #ab5e3f;
  --prism-json-property: #698c96;
}

html.dark {
  --prism-scheme: dark;
  --prism-foreground: #d4cfbf;
  --prism-background: #181818;
  --prism-comment: #758575;
  --prism-string: #d48372;
  --prism-literal: #429988;
  --prism-keyword: #4d9375;
  --prism-boolean: #6394bf;
  --prism-number: #6394bf;
  --prism-variable: #c2b36e;
  --prism-function: #a1b567;
  --prism-deleted: #bc6066;
  --prism-class: #54b1bf;
  --prism-builtin: #e0a569;
  --prism-property: #dd8e6e;
  --prism-namespace: #db889a;
  --prism-punctuation: #858585;
  --prism-decorator: #bd8f8f;
  --prism-regex: #ab5e3f;
  --prism-json-property: #6b8b9e;
  --prism-line-number: #888888;
  --prism-line-number-gutter: #eeeeee;
  --prism-line-highlight-background: #444444;
  --prism-selection-background: #444444;
  --prism-inline-background: theme('colors.dark.300');
}

.token.title {
  color: var(--prism-keyword);
}

.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
  color: var(--prism-comment);
  font-style: var(--prism-comment-style);
}

.token.namespace {
  color: var(--prism-namespace);
}

.token.interpolation {
  color: var(--prism-interpolation);
}

.token.string {
  color: var(--prism-string);
}

.token.punctuation {
  color: var(--prism-punctuation);
}

.token.operator {
  color: var(--prism-operator);
}

.token.keyword.module,
.token.keyword.control-flow {
  color: var(--prism-keyword-control);
}

.token.url,
.token.symbol,
.token.inserted {
  color: var(--prism-symbol);
}

.token.constant {
  color: var(--prism-constant);
}

.token.string.url {
  text-decoration: var(--prism-url-decoration);
}

.token.boolean,
.language-json .token.boolean {
  color: var(--prism-boolean);
}

.token.number,
.language-json .token.number {
  color: var(--prism-number);
}

.token.variable {
  color: var(--prism-variable);
}

.token.keyword {
  color: var(--prism-keyword);
}

.token.atrule,
.token.attr-value,
.token.selector {
  color: var(--prism-selector);
}

.token.function {
  color: var(--prism-function);
}

.token.deleted {
  color: var(--prism-deleted);
}

.token.important,
.token.bold {
  font-weight: bold;
}

.token.italic {
  font-style: italic;
}

.token.class-name {
  color: var(--prism-class);
}

.token.tag,
.token.builtin {
  color: var(--prism-builtin);
}

.token.attr-name,
.token.property,
.token.entity {
  color: var(--prism-property);
}

.language-json .token.property {
  color: var(--prism-json-property);
}

.token.regex {
  color: var(--prism-regex);
}

.token.decorator,
.token.annotation {
  color: var(--prism-decorator);
}
