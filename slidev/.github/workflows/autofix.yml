name: autofix.ci

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main
permissions:
  contents: read

jobs:
  autofix:
    runs-on: ubuntu-latest
    timeout-minutes: 10

    steps:
      - uses: actions/checkout@v4

      - name: Use Node.js lts/*
        uses: actions/setup-node@v4
        with:
          node-version: lts/*

      - name: Setup
        run: npm i -g @antfu/ni

      - name: Install
        run: nci
        env:
          CYPRESS_INSTALL_BINARY: 0

      - name: Lint
        run: nr lint --fix

      - uses: autofix-ci/action@dd55f44df8f7cdb7a6bf74c78677eb8acd40cd0a
