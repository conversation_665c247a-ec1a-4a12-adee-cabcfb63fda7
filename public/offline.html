<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - Lightbulb</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            color: #f8fafc;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
        }

        .container {
            text-align: center;
            max-width: 400px;
            width: 100%;
        }

        .icon {
            font-size: 4rem;
            margin-bottom: 1.5rem;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        h1 {
            color: #3b82f6;
            font-size: 2rem;
            margin-bottom: 1rem;
            font-weight: 600;
        }

        p {
            color: #94a3b8;
            line-height: 1.6;
            margin-bottom: 2rem;
            font-size: 1.1rem;
        }

        .actions {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.2s;
            min-height: 44px;
        }

        button:hover {
            background: #2563eb;
            transform: translateY(-1px);
        }

        button:active {
            transform: translateY(0);
        }

        .secondary {
            background: transparent;
            border: 1px solid #475569;
            color: #94a3b8;
        }

        .secondary:hover {
            background: #1e293b;
            border-color: #64748b;
            color: #f1f5f9;
        }

        .status {
            margin-top: 2rem;
            padding: 1rem;
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 0.5rem;
            font-size: 0.9rem;
        }

        .status.online {
            background: rgba(34, 197, 94, 0.1);
            border-color: rgba(34, 197, 94, 0.2);
            color: #22c55e;
        }

        .status.offline {
            background: rgba(239, 68, 68, 0.1);
            border-color: rgba(239, 68, 68, 0.2);
            color: #ef4444;
        }

        .features {
            margin-top: 2rem;
            text-align: left;
        }

        .features h3 {
            color: #e2e8f0;
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
        }

        .features ul {
            list-style: none;
            color: #94a3b8;
        }

        .features li {
            padding: 0.25rem 0;
            position: relative;
            padding-left: 1.5rem;
        }

        .features li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #22c55e;
            font-weight: bold;
        }

        @media (max-width: 480px) {
            .container {
                padding: 1rem;
            }
            
            h1 {
                font-size: 1.5rem;
            }
            
            .icon {
                font-size: 3rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">💡</div>
        <h1>You're Offline</h1>
        <p>Lightbulb is currently offline, but don't worry! Your data is safely stored locally and will sync automatically when you're back online.</p>
        
        <div class="actions">
            <button onclick="window.location.reload()">Try Again</button>
            <button class="secondary" onclick="goToApp()">Continue Offline</button>
        </div>
        
        <div class="status" id="status">
            <span id="status-text">Checking connection...</span>
        </div>
        
        <div class="features">
            <h3>Available Offline:</h3>
            <ul>
                <li>View saved thoughts and notes</li>
                <li>Create new content</li>
                <li>Browse your knowledge base</li>
                <li>Use visual workspace</li>
                <li>Access recent activity</li>
            </ul>
        </div>
    </div>

    <script>
        // Check online status
        function updateStatus() {
            const status = document.getElementById('status');
            const statusText = document.getElementById('status-text');
            
            if (navigator.onLine) {
                status.className = 'status online';
                statusText.textContent = 'Connection restored! You can now sync your data.';
            } else {
                status.className = 'status offline';
                statusText.textContent = 'No internet connection. Working in offline mode.';
            }
        }

        // Go to main app
        function goToApp() {
            window.location.href = '/';
        }

        // Listen for online/offline events
        window.addEventListener('online', updateStatus);
        window.addEventListener('offline', updateStatus);

        // Initial status check
        updateStatus();

        // Auto-redirect when online
        window.addEventListener('online', () => {
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        });

        // Register service worker if not already registered
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('/sw.js')
                .then(registration => {
                    console.log('SW registered from offline page:', registration);
                })
                .catch(error => {
                    console.log('SW registration failed from offline page:', error);
                });
        }
    </script>
</body>
</html>