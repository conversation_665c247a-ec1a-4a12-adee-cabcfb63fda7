import type { Config } from "tailwindcss";

// all in fixtures is set to tailwind v3 as interims solutions

const config: Config = {
    darkMode: ["class"],
    content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
    "*.{js,ts,jsx,tsx,mdx}",
    "../src/**/*.{js,ts,jsx,tsx,mdx}" // Include src directory
  ],
  theme: {
  	extend: {
  		colors: {
  			background: 'hsl(var(--background))',
  			foreground: 'hsl(var(--foreground))',
  			card: {
  				DEFAULT: 'hsl(var(--card))',
  				foreground: 'hsl(var(--card-foreground))'
  			},
  			popover: {
  				DEFAULT: 'hsl(var(--popover))',
  				foreground: 'hsl(var(--popover-foreground))'
  			},
  			primary: {
  				DEFAULT: 'hsl(var(--primary))',
  				foreground: 'hsl(var(--primary-foreground))',
  				50: '#f0f4ff',
  				100: '#e0e9ff',
  				200: '#c2d3ff',
  				300: '#9bb4ff',
  				400: '#6e8cff',
  				500: '#4d6bff',
  				600: '#3b4eff'
  			},
  			secondary: {
  				DEFAULT: 'hsl(var(--secondary))',
  				foreground: 'hsl(var(--secondary-foreground))'
  			},
  			muted: {
  				DEFAULT: 'hsl(var(--muted))',
  				foreground: 'hsl(var(--muted-foreground))'
  			},
  			accent: {
  				DEFAULT: 'hsl(var(--accent))',
  				foreground: 'hsl(var(--accent-foreground))'
  			},
  			destructive: {
  				DEFAULT: 'hsl(var(--destructive))',
  				foreground: 'hsl(var(--destructive-foreground))'
  			},
  			border: 'hsl(var(--border))',
  			input: 'hsl(var(--input))',
  			ring: 'hsl(var(--ring))',
  			chart: {
  				'1': 'hsl(var(--chart-1))',
  				'2': 'hsl(var(--chart-2))',
  				'3': 'hsl(var(--chart-3))',
  				'4': 'hsl(var(--chart-4))',
  				'5': 'hsl(var(--chart-5))'
  			},
  			sidebar: {
  				DEFAULT: 'hsl(var(--sidebar-background))',
  				foreground: 'hsl(var(--sidebar-foreground))',
  				primary: 'hsl(var(--sidebar-primary))',
  				'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
  				accent: 'hsl(var(--sidebar-accent))',
  				'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
  				border: 'hsl(var(--sidebar-border))',
  				ring: 'hsl(var(--sidebar-ring))'
  			},
  			// Soft UI colors
  			soft: {
  				50: '#f8fafc',
  				100: '#f1f5f9',
  				200: '#e2e8f0',
  				300: '#cbd5e1',
  				400: '#94a3b8',
  				500: '#64748b',
  				600: '#475569',
  				700: '#334155',
  				800: '#1e293b',
  				900: '#0f172a',
  			},
  			glass: {
  				white: 'rgba(255, 255, 255, 0.1)',
  				'white-50': 'rgba(255, 255, 255, 0.05)',
  				'white-100': 'rgba(255, 255, 255, 0.1)',
  				'white-200': 'rgba(255, 255, 255, 0.2)',
  				'white-300': 'rgba(255, 255, 255, 0.3)',
  				'white-400': 'rgba(255, 255, 255, 0.4)',
  				'white-500': 'rgba(255, 255, 255, 0.5)',
  				'white-600': 'rgba(255, 255, 255, 0.6)',
  				'white-700': 'rgba(255, 255, 255, 0.7)',
  				'white-800': 'rgba(255, 255, 255, 0.8)',
  				'white-900': 'rgba(255, 255, 255, 0.9)',
  			}
  		},
  		// Soft UI shadows
  		boxShadow: {
  			'soft': '0 8px 32px 0 rgba(31, 38, 135, 0.15)',
  			'soft-hover': '0 8px 40px 0 rgba(31, 38, 135, 0.25)',
  			'soft-active': '0 4px 20px 0 rgba(31, 38, 135, 0.2)',
  			'soft-primary': '0 8px 25px -5px rgba(102, 126, 234, 0.4)',
  			'soft-success': '0 8px 25px -5px rgba(102, 217, 160, 0.4)',
  			'soft-warning': '0 8px 25px -5px rgba(255, 181, 71, 0.4)',
  			'soft-danger': '0 8px 25px -5px rgba(255, 107, 107, 0.4)',
  			'soft-gradient': '0 8px 25px -5px rgba(236, 72, 153, 0.4)',
  			'soft-inset': 'inset 0 2px 4px 0 rgba(31, 38, 135, 0.06)',
  			'soft-xl': '0 20px 50px -12px rgba(31, 38, 135, 0.25)',
  			'soft-2xl': '0 25px 60px -15px rgba(31, 38, 135, 0.3)',
  			'neumorphic-up': '20px 20px 60px #d1d1d1, -20px -20px 60px #ffffff',
  			'neumorphic-down': 'inset 20px 20px 60px #d1d1d1, inset -20px -20px 60px #ffffff',
  			'neumorphic-flat': '5px 5px 10px #d1d1d1, -5px -5px 10px #ffffff',
  			'glass': '0 8px 32px 0 rgba(255, 255, 255, 0.1)',
  		},
  		// Gradient backgrounds
  		backgroundImage: {
  			'gradient-soft': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  			'gradient-peach': 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
  			'gradient-lavender': 'linear-gradient(135deg, #e9d5ff 0%, #c084fc 100%)',
  			'gradient-mint': 'linear-gradient(135deg, #b2f5ea 0%, #5eead4 100%)',
  			'gradient-sky': 'linear-gradient(135deg, #bfdbfe 0%, #60a5fa 100%)',
  			'gradient-glass': 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)',
  			'gradient-frosted': 'linear-gradient(135deg, rgba(255,255,255,0.25) 0%, rgba(255,255,255,0.1) 100%)',
  		},
  		backdropBlur: {
  			xs: '2px',
  			'3xl': '64px',
  		},
  		borderRadius: {
  			lg: 'var(--radius)',
  			md: 'calc(var(--radius) - 2px)',
  			sm: 'calc(var(--radius) - 4px)',
  			'soft': '1rem',
  			'soft-lg': '1.5rem',
  			'soft-xl': '2rem',
  		},
  		keyframes: {
  			'accordion-down': {
  				from: {
  					height: '0'
  				},
  				to: {
  					height: 'var(--radix-accordion-content-height)'
  				}
  			},
  			'accordion-up': {
  				from: {
  					height: 'var(--radix-accordion-content-height)'
  				},
  				to: {
  					height: '0'
  				}
  			},
  			// Soft UI animations
  			float: {
  				'0%, 100%': { transform: 'translateY(0) translateZ(0)' },
  				'50%': { transform: 'translateY(-10px) translateZ(0)' },
  			},
  			glow: {
  				'0%, 100%': { boxShadow: '0 8px 32px 0 rgba(31, 38, 135, 0.15)' },
  				'50%': { boxShadow: '0 8px 40px 0 rgba(31, 38, 135, 0.35)' },
  			},
  			'gradient-shift': {
  				'0%, 100%': { backgroundPosition: '0% 50%' },
  				'50%': { backgroundPosition: '100% 50%' },
  			},
  			shimmer: {
  				'0%': { backgroundPosition: '-200% 0' },
  				'100%': { backgroundPosition: '200% 0' },
  			},
  			'pulse-soft': {
  				'0%, 100%': { opacity: '1' },
  				'50%': { opacity: '0.8' },
  			},
  		},
  		animation: {
  			'accordion-down': 'accordion-down 0.2s ease-out',
  			'accordion-up': 'accordion-up 0.2s ease-out',
  			// Soft UI animations
  			'float': 'float 3s ease-in-out infinite',
  			'glow': 'glow 2s ease-in-out infinite',
  			'gradient-shift': 'gradient-shift 5s ease infinite',
  			'shimmer': 'shimmer 2s linear infinite',
  			'pulse-soft': 'pulse-soft 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
  		},
  		transitionTimingFunction: {
  			'bounce-in': 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
  			'smooth': 'cubic-bezier(0.23, 1, 0.32, 1)',
  			'smooth-out': 'cubic-bezier(0, 0, 0.2, 1)',
  		},
  	}
  },
  plugins: [
    require("tailwindcss-animate"),
    // Custom Soft UI plugin
    function({ addUtilities }: any) {
      const newUtilities = {
        '.glass': {
          background: 'rgba(255, 255, 255, 0.25)',
          backdropFilter: 'blur(20px) saturate(200%)',
          WebkitBackdropFilter: 'blur(20px) saturate(200%)',
          border: '1px solid rgba(255, 255, 255, 0.18)',
        },
        '.glass-dark': {
          background: 'rgba(0, 0, 0, 0.25)',
          backdropFilter: 'blur(20px) saturate(200%)',
          WebkitBackdropFilter: 'blur(20px) saturate(200%)',
          border: '1px solid rgba(255, 255, 255, 0.08)',
        },
        '.neumorphic': {
          background: '#f0f0f0',
          boxShadow: '20px 20px 60px #d1d1d1, -20px -20px 60px #ffffff',
        },
        '.neumorphic-pressed': {
          boxShadow: 'inset 20px 20px 60px #d1d1d1, inset -20px -20px 60px #ffffff',
        },
        '.soft-glow': {
          boxShadow: '0 0 20px rgba(102, 126, 234, 0.3)',
        },
        '.transition-soft': {
          transition: 'all 0.3s cubic-bezier(0.23, 1, 0.32, 1)',
        },
        '.frosted': {
          background: 'rgba(255, 255, 255, 0.9)',
          backdropFilter: 'blur(10px)',
          WebkitBackdropFilter: 'blur(10px)',
        },
        '.border-soft': {
          border: '1px solid rgba(255, 255, 255, 0.2)',
        },
        '.gpu-accelerated': {
          transform: 'translateZ(0)',
          willChange: 'transform',
        },
        '.backface-hidden': {
          backfaceVisibility: 'hidden',
        },
        '.shadow-soft': {
          boxShadow: '0 8px 32px 0 rgba(31, 38, 135, 0.15)',
        },
        '.shadow-neumorphic-up': {
          boxShadow: '20px 20px 60px #d1d1d1, -20px -20px 60px #ffffff',
        },
        '.shadow-neumorphic-down': {
          boxShadow: 'inset 20px 20px 60px #d1d1d1, inset -20px -20px 60px #ffffff',
        }
      }
      addUtilities(newUtilities)
    }
  ],
};
export default config;
