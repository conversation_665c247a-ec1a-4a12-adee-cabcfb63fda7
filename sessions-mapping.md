# Frontend Sessions to Rust Backend Mapping

## Overview
This document provides a comprehensive mapping of the frontend session components (`src/components/sessions/`) to their corresponding Rust backend handlers in `src-tauri/`.

## Architecture Overview

```
Frontend (TypeScript/React)
    ↓
API Layer (src/lib/api.ts)
    ↓
Tauri IPC Bridge (@tauri-apps/api)
    ↓
Rust Backend Commands (src-tauri/src/commands/)
    ↓
Core Services & Database
```

## Main Command Handlers

### 1. Claude Session Management
**Location:** `src-tauri/src/commands/claude.rs`

| Frontend API Call | Rust Command | Description |
|-------------------|--------------|-------------|
| `executeClaudeCode()` | `execute_claude_code` | Starts new Claude Code sessions |
| `resumeClaudeCode()` | `resume_claude_code` | Resumes existing Claude sessions |
| `loadSessionHistory()` | `load_session_history` | Loads JSONL session history |
| `getClaudeSessionOutput()` | `get_claude_session_output` | Gets session output (live or stored) |
| `listRunningClaudeSessions()` | `list_running_claude_sessions` | Lists all active Claude sessions |
| `cancelClaudeExecution()` | `cancel_claude_execution` | Cancels running Claude processes |
| `bringToForeground()` | `bring_to_foreground` | Brings session to foreground |
| `sendToBackground()` | `send_to_background` | Sends session to background |
| `approvePlanExecution()` | `approve_plan_execution` | Approves plan execution |
| `rejectPlanExecution()` | `reject_plan_execution` | Rejects plan execution |
| `getSessionOutput()` | `get_session_output` | Gets output from process registry |
| `streamSessionOutput()` | `stream_session_output` | Streams session output in real-time |

### 2. Checkpoint System
**Location:** `src-tauri/src/commands/claude.rs` & `src-tauri/src/commands/checkpoint_commands.rs`

| Frontend API Call | Rust Command | Description |
|-------------------|--------------|-------------|
| `createCheckpoint()` | `create_checkpoint` | Creates session checkpoints |
| `getCheckpointSettings()` | `get_checkpoint_settings` | Gets checkpoint configurations |
| `listCheckpoints()` | `list_checkpoints` | Lists all checkpoints for a session |
| `restoreCheckpoint()` | `restore_checkpoint` | Restores session to checkpoint state |
| `deleteCheckpoint()` | `delete_checkpoint` | Deletes a checkpoint |
| `forkFromCheckpoint()` | `fork_from_checkpoint` | Creates new session from checkpoint |
| `checkAutoCheckpoint()` | `check_auto_checkpoint` | Checks automatic checkpoint conditions |
| `clearCheckpointManager()` | `clear_checkpoint_manager` | Clears checkpoint manager state |

### 3. SuperClaude System
**Location:** `src-tauri/src/commands/superclaude.rs`

| Frontend API Call | Rust Command | Description |
|-------------------|--------------|-------------|
| `executeSuperClaudeCommand()` | `execute_superclaude_command` | Executes SuperClaude commands |
| `loadSuperClaudeContext()` | `load_superclaude_context` | Loads SuperClaude session context |
| `getSuperClaudeSuggestions()` | `get_superclaude_suggestions` | Gets command suggestions |
| `getSessionPersonas()` | `get_session_personas` | Gets active personas for sessions |
| `detectPersonasFromText()` | `detect_personas_from_text` | Detects personas in text |
| `activatePersonas()` | `activate_personas` | Activates personas for session |
| `saveCommandHistory()` | `save_command_history` | Saves command execution history |
| `getCommandHistory()` | `get_command_history` | Retrieves command history |
| `getProjectCommandHistory()` | `get_project_command_history` | Gets project-level command history |
| `getSuperClaudeStats()` | `get_superclaude_stats` | Gets SuperClaude usage statistics |

### 4. Agent System
**Location:** `src-tauri/src/commands/agents.rs`

| Frontend API Call | Rust Command | Description |
|-------------------|--------------|-------------|
| `loadAgentSessionHistory()` | `load_agent_session_history` | Loads agent session history across projects |

## Event System

### Frontend Event Listeners

| Event Name | Source Component | Description |
|------------|------------------|-------------|
| `claude-stream` | `useClaudeMessages.ts` | Receives streaming Claude messages |
| `claude-output:<session_id>` | `ClaudeCodeSession.tsx` | Session-specific output stream |
| `claude-error:<session_id>` | `ClaudeCodeSession.tsx` | Session-specific error events |
| `claude-complete:<session_id>` | `ClaudeCodeSession.tsx` | Session completion notifications |
| `agent-output:<session_id>` | `SessionOutputViewer.tsx` | Agent output streaming |
| `agent-error:<session_id>` | `SessionOutputViewer.tsx` | Agent error notifications |

### Backend Event Emitters

| Event Name | Emitting Module | Trigger |
|------------|-----------------|---------|
| `claude-output` | `claude.rs` | Generic output for backward compatibility |
| `claude-output:<session_id>` | `claude.rs` | Session-specific output during execution |
| `claude-error:<session_id>` | `claude.rs` | Error notifications |
| `claude-complete:<session_id>` | `claude.rs` | Session completion signals |
| `claude-cancelled:<session_id>` | `claude.rs` | Cancellation events |
| `session-background-changed` | `claude.rs` | Session focus state changes |
| `plan-approval:<session_id>` | `claude.rs` | Plan approval request events |

## Supporting Infrastructure

### Core Backend Modules

#### **SuperClaude Core** (`src-tauri/src/superclaude/`)
- `db.rs` - Database operations for SuperClaude
- `parser.rs` - Command parsing logic
- `personas.rs` - Persona management
- `types.rs` - Type definitions
- `commands.rs` - Command definitions

#### **Checkpoint System** (`src-tauri/src/checkpoint/`)
- `manager.rs` - Checkpoint management logic
- `state.rs` - Checkpoint state handling
- `storage.rs` - Checkpoint persistence
- `significance.rs` - Checkpoint significance levels

#### **Process Management** (`src-tauri/src/process/`)
- `registry.rs` - Process registry for tracking sessions
- Maps session IDs to process PIDs
- Tracks both Claude and Agent processes

#### **Database Layer** (`src-tauri/src/db.rs`)
- SQLite backend for persistence
- Stores session history, checkpoints, SuperClaude data
- Manages user preferences and settings

## Frontend Components Structure

### Main Session Components
```
src/components/sessions/
├── ClaudeCodeSession.tsx           # Main Claude session component
├── SessionList.tsx                 # Session listing component
├── SessionOutputViewer.tsx         # Output display component
├── RunningClaudeSessions.tsx       # Active sessions management
└── superclaude/                    # SuperClaude subsystem
    ├── hooks/
    │   └── useSuperClaude.ts      # Main SuperClaude hook
    ├── core/
    │   ├── commands.ts             # Command definitions
    │   ├── parser.ts               # Command parsing
    │   ├── personas.ts             # Persona logic
    │   └── types.ts                # TypeScript types
    └── ui/
        ├── CommandSuggestions.tsx  # Command suggestion UI
        ├── PersonaBadge.tsx        # Persona display
        ├── SuperClaudeHistory.tsx  # History display
        └── SuperClaudePanel.tsx    # Main panel UI
```

### Claude Session Components
```
src/components/sessions/claude-code-session/
├── useClaudeMessages.ts            # Message handling hook
├── useCheckpoints.ts               # Checkpoint management hook
├── MessageList.tsx                 # Message display
├── SessionHeader.tsx               # Session header UI
├── PlanModeManager.tsx             # Plan mode handling
├── ExtendedThinkingManager.tsx    # Extended thinking UI
├── ErrorRecoverySystem.tsx        # Error recovery
├── SessionMetricsTracker.tsx      # Metrics tracking
└── SessionMetricsVisualizer.tsx   # Metrics visualization
```

## Data Flow

### Command Execution Flow
1. **User Action** → React Component
2. **API Call** → `api.ts` function
3. **Tauri Invoke** → IPC bridge
4. **Rust Handler** → Command function
5. **Business Logic** → Core services
6. **Database** → SQLite operations
7. **Response** → Back through chain

### Event Streaming Flow
1. **Backend Process** → Generates output
2. **Event Emission** → `app.emit()` in Rust
3. **IPC Transport** → Tauri event system
4. **Frontend Listener** → `listen()` handler
5. **State Update** → React component state
6. **UI Update** → Re-render with new data

## Key Integration Points

### Session Lifecycle
1. **Creation**: `executeClaudeCode` → New process spawn
2. **Streaming**: Event listeners for real-time output
3. **Interaction**: Commands sent via IPC
4. **Checkpointing**: Automatic/manual state saves
5. **Background/Foreground**: Focus management
6. **Completion**: Cleanup and history save

### SuperClaude Integration
1. **Command Detection**: Frontend parser identifies commands
2. **Backend Validation**: Rust validates and executes
3. **Persona Activation**: Dynamic persona management
4. **History Tracking**: All commands saved to database
5. **Statistics**: Usage metrics aggregation

## Database Schema (Relevant Tables)

### Session Storage
- Session history (JSONL files)
- Checkpoint data
- Session metadata

### SuperClaude Tables
- Command history
- Active personas
- Session contexts
- Usage statistics

### Process Registry
- Running processes
- Session-to-PID mappings
- Background/foreground states

## Performance Considerations

### Optimizations
- Streaming output for real-time feedback
- Event debouncing for high-frequency updates
- Lazy loading of session history
- Checkpoint compression

### Scalability
- Process registry handles multiple concurrent sessions
- Database indexes on frequently queried fields
- Event system supports selective listening

## Security Considerations

### IPC Security
- Commands validated before execution
- Input sanitization in Rust handlers
- Process isolation between sessions

### Data Protection
- Session data stored locally
- Checkpoints encrypted if sensitive
- No external data transmission without consent

## Future Enhancements

### Planned Features
- Enhanced checkpoint diffing
- Multi-session synchronization
- Advanced persona learning
- Session replay capabilities

### Technical Debt
- Consolidate duplicate event handlers
- Optimize database queries
- Improve error recovery mechanisms
- Add comprehensive logging

## Summary

The session components have extensive integration with the Rust backend through:
- **40+ command handlers** across multiple modules
- **Bidirectional event system** for real-time communication
- **Comprehensive state management** via checkpoints
- **Advanced features** like SuperClaude and personas
- **Robust process management** for session lifecycle

This architecture provides a solid foundation for the Claude Code IDE's session management capabilities while maintaining separation of concerns and enabling future extensibility.