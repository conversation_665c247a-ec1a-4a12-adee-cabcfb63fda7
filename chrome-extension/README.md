# Lightbulb Second Brain Chrome Extension

A Chrome extension that allows you to capture and save any web content to your Lightbulb Second Brain application. Chat with any content type including web pages, text selections, screenshots, and ChatGPT conversations.

## Features

### 🚀 Quick Capture
- **Save Web Pages**: Capture entire web pages with one click
- **Text Selection**: Save selected text with context
- **Screenshots**: Capture and OCR process screenshots
- **ChatGPT Conversations**: Extract and save ChatGPT conversations automatically

### 🤖 AI Integration
- Chat with captured content using multiple AI models
- Automatic content processing and chunking
- Smart tagging and organization
- Semantic search across all saved content

### 📱 Cross-Platform Sync
- Offline queue for when the app is not running
- Real-time sync with your Second Brain application
- Works across all your devices

### ⌨️ Keyboard Shortcuts
- `Ctrl+Shift+S` - Save selected text
- `Ctrl+Shift+P` - Save current page
- `Ctrl+Shift+C` - Capture screenshot

## Installation

### Development Installation

1. **Clone or download** this extension folder
2. **Generate icon files** (see `icons/README.md` for instructions)
3. **Open Chrome** and navigate to `chrome://extensions/`
4. **Enable Developer mode** (toggle in top right)
5. **Click "Load unpacked"** and select the `chrome-extension` folder
6. **Pin the extension** to your toolbar for easy access

### Production Installation

1. Download from the Chrome Web Store (when published)
2. Click "Add to Chrome"
3. Pin to toolbar and configure settings

## Setup

### 1. Start Your Second Brain App
Make sure your Lightbulb Second Brain application is running:
```bash
# Default URL: http://localhost:1420
npm run dev
# or
cargo run
```

### 2. Configure Extension Settings
1. Click the extension icon in your toolbar
2. Click the "Settings" button
3. Configure your API URL (default: `http://localhost:1420`)
4. Add API key if required
5. Enable desired features

### 3. Test the Extension
1. Open `test-extension.html` in Chrome
2. Try the different capture features
3. Verify content appears in your Second Brain app

## Usage

### Capturing Web Pages
1. Navigate to any web page
2. Click the extension icon
3. Click "Save Page" to capture the entire page
4. Or use `Ctrl+Shift+P` keyboard shortcut

### Saving Text Selections
1. Select any text on a web page
2. A popup will appear with save options
3. Click "💡 Save" or use `Ctrl+Shift+S`
4. Text will be saved with context

### Taking Screenshots
1. Click the extension icon
2. Click "Screenshot" button
3. Or use `Ctrl+Shift+C` keyboard shortcut
4. Screenshot will be processed with OCR

### ChatGPT Integration
1. Navigate to `chat.openai.com` or `chatgpt.com`
2. Start a conversation
3. Look for the floating "💡 Save to Second Brain" button
4. Click to save the entire conversation
5. Messages are extracted and organized automatically

### Context Menu Options
Right-click on any page to access:
- Save selection to Second Brain
- Save page to Second Brain
- Capture screenshot

## File Structure

```
chrome-extension/
├── manifest.json          # Extension configuration
├── background.js          # Service worker for background tasks
├── content.js            # Content script for page interaction
├── content.css           # Styles for content script elements
├── chatgpt-extractor.js  # ChatGPT conversation extraction
├── popup.html            # Extension popup interface
├── popup.css             # Popup styles
├── popup.js              # Popup functionality
├── welcome.html          # Welcome page for new users
├── test-extension.html   # Test page for development
├── icons/                # Extension icons
│   ├── icon.svg         # Source SVG icon
│   └── README.md        # Icon generation instructions
└── README.md            # This file
```

## API Integration

The extension communicates with your Second Brain application via REST API:

### Endpoints Used
- `GET /api/health` - Check connection status
- `POST /api/content/capture` - Save captured content
- `GET /api/captures/recent` - Fetch recent captures

### Content Format
```javascript
{
  type: 'webpage' | 'text-selection' | 'screenshot' | 'chatgpt-conversation',
  content: 'extracted content',
  url: 'source URL',
  title: 'page title',
  timestamp: **********,
  metadata: {
    // Additional context and information
  }
}
```

## Offline Support

The extension includes robust offline support:

1. **Offline Queue**: Content is queued when the app is unavailable
2. **Auto Sync**: Automatically syncs when connection is restored
3. **Status Indicators**: Shows connection status in popup
4. **Manual Sync**: Force sync offline items via popup

## Privacy & Security

- **Local Processing**: All content processing happens locally
- **No External Servers**: Data only goes to your Second Brain app
- **Secure Storage**: Settings stored in Chrome's secure storage
- **Permission Minimal**: Only requests necessary permissions

## Troubleshooting

### Extension Not Working
1. Check that Developer mode is enabled
2. Verify the extension is loaded and enabled
3. Check for errors in Chrome DevTools console
4. Reload the extension if needed

### Connection Issues
1. Ensure Second Brain app is running on correct port
2. Check API URL in extension settings
3. Verify firewall/network settings
4. Look for CORS issues in browser console

### ChatGPT Extraction Not Working
1. Make sure you're on `chat.openai.com` or `chatgpt.com`
2. Wait for the page to fully load
3. Check if the save button appears after starting a conversation
4. Try refreshing the page

### Content Not Saving
1. Check connection status in popup
2. Verify API endpoint is accessible
3. Look for error messages in notifications
4. Check if content is in offline queue

## Development

### Building for Production
1. Generate all required icon files
2. Update version in `manifest.json`
3. Test thoroughly across different websites
4. Package as ZIP for Chrome Web Store

### Testing
1. Use `test-extension.html` for basic functionality
2. Test on various websites (news, blogs, documentation)
3. Test ChatGPT integration on OpenAI's site
4. Verify offline/online transitions
5. Test keyboard shortcuts

### Contributing
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This extension is part of the Lightbulb Second Brain project. See the main project license for details.

## Support

For issues and support:
1. Check this README and troubleshooting section
2. Look for similar issues in the project repository
3. Create a new issue with detailed information
4. Include browser version, extension version, and error messages