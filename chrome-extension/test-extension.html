<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Test Page for Lightbulb Extension</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 40px 20px;
      line-height: 1.6;
      color: #333;
    }
    
    h1, h2 {
      color: #2c3e50;
    }
    
    .test-section {
      background: #f8f9fa;
      padding: 20px;
      margin: 20px 0;
      border-radius: 8px;
      border-left: 4px solid #007cba;
    }
    
    .highlight {
      background: #fff3cd;
      padding: 2px 4px;
      border-radius: 3px;
    }
    
    code {
      background: #e9ecef;
      padding: 2px 6px;
      border-radius: 3px;
      font-family: 'Monaco', 'Consolas', monospace;
    }
    
    .instructions {
      background: #d4edda;
      border: 1px solid #c3e6cb;
      padding: 15px;
      border-radius: 6px;
      margin: 20px 0;
    }
    
    .warning {
      background: #fff3cd;
      border: 1px solid #ffeaa7;
      padding: 15px;
      border-radius: 6px;
      margin: 20px 0;
    }
  </style>
</head>
<body>
  <h1>Lightbulb Second Brain Extension Test Page</h1>
  
  <div class="instructions">
    <h3>🧪 Testing Instructions</h3>
    <p>Use this page to test the Lightbulb Second Brain Chrome extension functionality:</p>
    <ol>
      <li>Load the extension in Chrome (Developer mode)</li>
      <li>Click the extension icon in the toolbar</li>
      <li>Try the different capture features listed below</li>
    </ol>
  </div>

  <div class="test-section">
    <h2>📄 Page Capture Test</h2>
    <p>This entire page should be capturable using the <strong>"Save Page"</strong> button in the extension popup.</p>
    <p>The extension should extract the main content, including this text, headings, and structured information.</p>
  </div>

  <div class="test-section">
    <h2>✂️ Text Selection Test</h2>
    <p>Try selecting this text: <span class="highlight">This is a test selection for the Lightbulb Second Brain extension. Select this text and use Ctrl+Shift+S or the extension popup to save it.</span></p>
    <p>When you select text, a small popup should appear with options to save or chat with the selection.</p>
  </div>

  <div class="test-section">
    <h2>📸 Screenshot Test</h2>
    <p>Use the <strong>"Screenshot"</strong> button in the extension popup or press <code>Ctrl+Shift+C</code> to capture a screenshot of this page.</p>
    <p>The screenshot should be processed and saved to your Second Brain with OCR text extraction.</p>
  </div>

  <div class="test-section">
    <h2>⌨️ Keyboard Shortcuts</h2>
    <ul>
      <li><code>Ctrl+Shift+S</code> - Save selected text</li>
      <li><code>Ctrl+Shift+P</code> - Save current page</li>
      <li><code>Ctrl+Shift+C</code> - Capture screenshot</li>
    </ul>
  </div>

  <div class="test-section">
    <h2>🔗 Links and Images Test</h2>
    <p>This section contains various elements that should be captured:</p>
    <ul>
      <li><a href="https://example.com">External link</a></li>
      <li><a href="#test">Internal link</a></li>
      <li>Code snippet: <code>console.log('Hello, Second Brain!');</code></li>
      <li>Formatted text: <strong>bold</strong>, <em>italic</em>, <u>underlined</u></li>
    </ul>
  </div>

  <div class="warning">
    <h3>⚠️ ChatGPT Testing</h3>
    <p>To test ChatGPT conversation extraction:</p>
    <ol>
      <li>Navigate to <a href="https://chat.openai.com" target="_blank">chat.openai.com</a></li>
      <li>Start a conversation</li>
      <li>Look for the floating "💡 Save to Second Brain" button</li>
      <li>Click it to save the conversation</li>
    </ol>
  </div>

  <div class="test-section">
    <h2>📊 Expected Results</h2>
    <p>After testing, you should see:</p>
    <ul>
      <li>Captured content appearing in the "Recent Captures" section of the popup</li>
      <li>Success notifications when content is saved</li>
      <li>Offline queue if the Second Brain app is not running</li>
      <li>Content synchronized to your Second Brain application</li>
    </ul>
  </div>

  <div class="test-section">
    <h2>🛠️ Troubleshooting</h2>
    <p>If something doesn't work:</p>
    <ul>
      <li>Check that the Second Brain app is running on <code>http://localhost:1420</code></li>
      <li>Verify extension permissions in Chrome settings</li>
      <li>Check the browser console for error messages</li>
      <li>Ensure the extension is enabled and up to date</li>
    </ul>
  </div>

  <script>
    // Add some dynamic content for testing
    document.addEventListener('DOMContentLoaded', function() {
      console.log('Test page loaded - ready for extension testing');
      
      // Add timestamp
      const timestamp = new Date().toLocaleString();
      const timestampEl = document.createElement('p');
      timestampEl.innerHTML = `<small>Page loaded at: ${timestamp}</small>`;
      document.body.appendChild(timestampEl);
    });

    // Test selection events
    document.addEventListener('mouseup', function() {
      const selection = window.getSelection();
      if (selection.toString().trim().length > 0) {
        console.log('Text selected:', selection.toString().trim());
      }
    });
  </script>
</body>
</html>