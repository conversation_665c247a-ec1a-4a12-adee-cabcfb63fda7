/* Popup styles for Lightbulb Second Brain extension */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  font-size: 14px;
  line-height: 1.4;
  color: #333;
  background: #f8f9fa;
}

.popup-container {
  width: 380px;
  max-height: 600px;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

/* Header */
.popup-header {
  background: linear-gradient(135deg, #007cba 0%, #005a87 100%);
  color: white;
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo-icon {
  font-size: 20px;
}

.logo h1 {
  font-size: 18px;
  font-weight: 600;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #ffc107;
  animation: pulse 2s infinite;
}

.status-dot.online {
  background: #28a745;
  animation: none;
}

.status-dot.offline {
  background: #dc3545;
  animation: none;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Main Content */
.popup-main {
  padding: 20px;
  max-height: 480px;
  overflow-y: auto;
}

.popup-main::-webkit-scrollbar {
  width: 6px;
}

.popup-main::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.popup-main::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.popup-main::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Sections */
section {
  margin-bottom: 24px;
}

section:last-child {
  margin-bottom: 0;
}

section h2 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
  color: #2c3e50;
}

/* Action Buttons */
.action-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  text-align: left;
  background: #f8f9fa;
  color: #495057;
  border: 1px solid #e9ecef;
}

.action-btn:hover {
  background: #e9ecef;
  transform: translateY(-1px);
}

.action-btn.primary {
  background: #007cba;
  color: white;
  border-color: #007cba;
  grid-column: 1 / -1;
}

.action-btn.primary:hover {
  background: #005a87;
}

.action-btn.secondary {
  background: #6c757d;
  color: white;
  border-color: #6c757d;
}

.action-btn.secondary:hover {
  background: #545b62;
}

.action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn-icon {
  font-size: 16px;
  flex-shrink: 0;
}

.btn-text {
  flex: 1;
}

/* ChatGPT Section */
.chatgpt-info {
  background: #e8f4fd;
  border: 1px solid #b8daff;
  border-radius: 6px;
  padding: 12px;
}

.chatgpt-info p {
  margin-bottom: 8px;
  color: #004085;
  font-size: 13px;
}

/* Recent Captures */
.recent-list {
  max-height: 120px;
  overflow-y: auto;
}

.recent-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  margin-bottom: 6px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.recent-item:hover {
  background: #e9ecef;
}

.recent-item:last-child {
  margin-bottom: 0;
}

.recent-icon {
  font-size: 16px;
  flex-shrink: 0;
}

.recent-content {
  flex: 1;
  min-width: 0;
}

.recent-title {
  font-weight: 500;
  font-size: 13px;
  color: #2c3e50;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.recent-meta {
  font-size: 11px;
  color: #6c757d;
  margin-top: 2px;
}

.empty-state {
  text-align: center;
  padding: 20px;
  color: #6c757d;
}

.empty-state p {
  font-weight: 500;
  margin-bottom: 4px;
}

.empty-state small {
  font-size: 12px;
}

/* Offline Section */
.offline-info {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 6px;
  padding: 12px;
}

.offline-info p {
  margin-bottom: 8px;
  color: #856404;
  font-size: 13px;
}

/* Footer */
.popup-footer {
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  padding: 12px 20px;
}

.footer-actions {
  display: flex;
  gap: 8px;
}

.footer-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 8px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  background: #e9ecef;
  color: #495057;
  transition: background-color 0.2s ease;
}

.footer-btn:hover {
  background: #dee2e6;
}

/* Modal */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 400px;
  max-height: 80%;
  overflow: hidden;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e9ecef;
  background: #f8f9fa;
}

.modal-header h2 {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.close-btn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: #6c757d;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.close-btn:hover {
  background: #e9ecef;
}

.modal-body {
  padding: 20px;
  max-height: 300px;
  overflow-y: auto;
}

.modal-footer {
  padding: 16px 20px;
  border-top: 1px solid #e9ecef;
  background: #f8f9fa;
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

/* Form Styles */
.form-group {
  margin-bottom: 16px;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-group label {
  display: block;
  margin-bottom: 4px;
  font-weight: 500;
  color: #2c3e50;
  font-size: 13px;
}

.form-group input[type="text"],
.form-group input[type="url"],
.form-group input[type="password"] {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.form-group input:focus {
  outline: none;
  border-color: #007cba;
  box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.2);
}

.form-group small {
  display: block;
  margin-top: 4px;
  font-size: 11px;
  color: #6c757d;
}

/* Checkbox Styles */
.checkbox-label {
  display: flex !important;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-weight: normal !important;
  margin-bottom: 0 !important;
}

.checkbox-label input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.checkmark {
  font-size: 13px;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.popup-container {
  animation: fadeIn 0.2s ease-out;
}

/* Responsive */
@media (max-width: 400px) {
  .popup-container {
    width: 100vw;
    height: 100vh;
    border-radius: 0;
  }
  
  .action-buttons {
    grid-template-columns: 1fr;
  }
}