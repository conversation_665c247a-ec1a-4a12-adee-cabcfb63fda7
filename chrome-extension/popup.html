<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Lightbulb Second Brain</title>
  <link rel="stylesheet" href="popup.css">
</head>
<body>
  <div class="popup-container">
    <header class="popup-header">
      <div class="logo">
        <span class="logo-icon">💡</span>
        <h1>Second Brain</h1>
      </div>
      <div class="status-indicator" id="status-indicator">
        <span class="status-dot"></span>
        <span class="status-text">Connecting...</span>
      </div>
    </header>

    <main class="popup-main">
      <!-- Quick Actions -->
      <section class="quick-actions">
        <h2>Quick Capture</h2>
        <div class="action-buttons">
          <button id="capture-page" class="action-btn primary">
            <span class="btn-icon">📄</span>
            <span class="btn-text">Save Page</span>
          </button>
          <button id="capture-selection" class="action-btn">
            <span class="btn-icon">✂️</span>
            <span class="btn-text">Save Selection</span>
          </button>
          <button id="capture-screenshot" class="action-btn">
            <span class="btn-icon">📸</span>
            <span class="btn-text">Screenshot</span>
          </button>
        </div>
      </section>

      <!-- ChatGPT Integration -->
      <section class="chatgpt-section" id="chatgpt-section" style="display: none;">
        <h2>ChatGPT Integration</h2>
        <div class="chatgpt-info">
          <p id="chatgpt-status">ChatGPT conversation detected</p>
          <button id="save-conversation" class="action-btn primary">
            <span class="btn-icon">💬</span>
            <span class="btn-text">Save Conversation</span>
          </button>
        </div>
      </section>

      <!-- Recent Captures -->
      <section class="recent-captures">
        <h2>Recent Captures</h2>
        <div id="recent-list" class="recent-list">
          <div class="empty-state">
            <p>No recent captures</p>
            <small>Start capturing content to see it here</small>
          </div>
        </div>
      </section>

      <!-- Offline Queue -->
      <section class="offline-section" id="offline-section" style="display: none;">
        <h2>Offline Queue</h2>
        <div class="offline-info">
          <p id="offline-count">0 items waiting to sync</p>
          <button id="sync-offline" class="action-btn secondary">
            <span class="btn-icon">🔄</span>
            <span class="btn-text">Sync Now</span>
          </button>
        </div>
      </section>
    </main>

    <footer class="popup-footer">
      <div class="footer-actions">
        <button id="open-app" class="footer-btn">
          <span class="btn-icon">🚀</span>
          <span class="btn-text">Open App</span>
        </button>
        <button id="settings" class="footer-btn">
          <span class="btn-icon">⚙️</span>
          <span class="btn-text">Settings</span>
        </button>
      </div>
    </footer>
  </div>

  <!-- Settings Modal -->
  <div id="settings-modal" class="modal" style="display: none;">
    <div class="modal-content">
      <div class="modal-header">
        <h2>Settings</h2>
        <button id="close-settings" class="close-btn">✕</button>
      </div>
      <div class="modal-body">
        <form id="settings-form">
          <div class="form-group">
            <label for="api-url">API URL</label>
            <input type="url" id="api-url" placeholder="http://localhost:1420" />
            <small>URL of your Second Brain application</small>
          </div>
          
          <div class="form-group">
            <label for="api-key">API Key (Optional)</label>
            <input type="password" id="api-key" placeholder="Enter API key if required" />
            <small>Authentication key for your Second Brain API</small>
          </div>

          <div class="form-group">
            <label class="checkbox-label">
              <input type="checkbox" id="auto-capture" />
              <span class="checkmark"></span>
              Auto-capture on supported sites
            </label>
          </div>

          <div class="form-group">
            <label class="checkbox-label">
              <input type="checkbox" id="capture-screenshots" />
              <span class="checkmark"></span>
              Enable screenshot capture
            </label>
          </div>

          <div class="form-group">
            <label class="checkbox-label">
              <input type="checkbox" id="capture-full-page" />
              <span class="checkmark"></span>
              Capture full page content
            </label>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button id="save-settings" class="action-btn primary">Save Settings</button>
        <button id="cancel-settings" class="action-btn secondary">Cancel</button>
      </div>
    </div>
  </div>

  <script src="popup.js"></script>
</body>
</html>