// Popup script for Lightbulb Second Brain extension

class PopupController {
  constructor() {
    this.currentTab = null;
    this.settings = {};
    this.recentCaptures = [];
    this.offlineQueue = [];
    
    this.init();
  }

  async init() {
    await this.loadSettings();
    await this.loadRecentCaptures();
    await this.loadOfflineQueue();
    await this.getCurrentTab();
    
    this.setupEventListeners();
    this.updateUI();
    this.checkConnectionStatus();
  }

  setupEventListeners() {
    // Quick action buttons
    document.getElementById('capture-page').addEventListener('click', () => {
      this.capturePage();
    });

    document.getElementById('capture-selection').addEventListener('click', () => {
      this.captureSelection();
    });

    document.getElementById('capture-screenshot').addEventListener('click', () => {
      this.captureScreenshot();
    });

    // ChatGPT conversation
    const saveConversationBtn = document.getElementById('save-conversation');
    if (saveConversationBtn) {
      saveConversationBtn.addEventListener('click', () => {
        this.saveChatGPTConversation();
      });
    }

    // Offline sync
    const syncOfflineBtn = document.getElementById('sync-offline');
    if (syncOfflineBtn) {
      syncOfflineBtn.addEventListener('click', () => {
        this.syncOfflineQueue();
      });
    }

    // Footer actions
    document.getElementById('open-app').addEventListener('click', () => {
      this.openApp();
    });

    document.getElementById('settings').addEventListener('click', () => {
      this.showSettings();
    });

    // Settings modal
    document.getElementById('close-settings').addEventListener('click', () => {
      this.hideSettings();
    });

    document.getElementById('save-settings').addEventListener('click', () => {
      this.saveSettings();
    });

    document.getElementById('cancel-settings').addEventListener('click', () => {
      this.hideSettings();
    });

    // Close modal on backdrop click
    document.getElementById('settings-modal').addEventListener('click', (e) => {
      if (e.target.id === 'settings-modal') {
        this.hideSettings();
      }
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        this.hideSettings();
      }
    });
  }

  async getCurrentTab() {
    const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
    this.currentTab = tabs[0];
  }

  async loadSettings() {
    const result = await chrome.storage.sync.get([
      'apiUrl',
      'apiKey',
      'autoCapture',
      'captureScreenshots',
      'captureFullPage'
    ]);
    
    this.settings = {
      apiUrl: result.apiUrl || 'http://localhost:1420',
      apiKey: result.apiKey || '',
      autoCapture: result.autoCapture !== false,
      captureScreenshots: result.captureScreenshots !== false,
      captureFullPage: result.captureFullPage !== false
    };
  }

  async loadRecentCaptures() {
    const result = await chrome.storage.local.get(['recentCaptures']);
    this.recentCaptures = result.recentCaptures || [];
  }

  async loadOfflineQueue() {
    const result = await chrome.storage.local.get(['offlineQueue']);
    this.offlineQueue = result.offlineQueue || [];
  }

  updateUI() {
    this.updateConnectionStatus();
    this.updateChatGPTSection();
    this.updateRecentCaptures();
    this.updateOfflineSection();
    this.updateSettingsForm();
  }

  updateConnectionStatus() {
    const statusIndicator = document.getElementById('status-indicator');
    const statusDot = statusIndicator.querySelector('.status-dot');
    const statusText = statusIndicator.querySelector('.status-text');

    // Check connection status
    this.checkConnection().then(isOnline => {
      if (isOnline) {
        statusDot.className = 'status-dot online';
        statusText.textContent = 'Connected';
      } else {
        statusDot.className = 'status-dot offline';
        statusText.textContent = 'Offline';
      }
    });
  }

  updateChatGPTSection() {
    const chatgptSection = document.getElementById('chatgpt-section');
    
    if (this.currentTab && this.isChatGPTUrl(this.currentTab.url)) {
      chatgptSection.style.display = 'block';
      
      // Update status text
      const statusText = document.getElementById('chatgpt-status');
      statusText.textContent = 'ChatGPT conversation detected on this page';
    } else {
      chatgptSection.style.display = 'none';
    }
  }

  updateRecentCaptures() {
    const recentList = document.getElementById('recent-list');
    
    if (this.recentCaptures.length === 0) {
      recentList.innerHTML = `
        <div class="empty-state">
          <p>No recent captures</p>
          <small>Start capturing content to see it here</small>
        </div>
      `;
      return;
    }

    const recentItems = this.recentCaptures.slice(0, 5).map(capture => {
      const icon = this.getTypeIcon(capture.type);
      const timeAgo = this.getTimeAgo(capture.timestamp);
      
      return `
        <div class="recent-item" data-id="${capture.id}">
          <span class="recent-icon">${icon}</span>
          <div class="recent-content">
            <div class="recent-title">${this.truncateText(capture.title || capture.url || 'Untitled', 40)}</div>
            <div class="recent-meta">${timeAgo} • ${capture.type}</div>
          </div>
        </div>
      `;
    }).join('');

    recentList.innerHTML = recentItems;

    // Add click handlers
    recentList.querySelectorAll('.recent-item').forEach(item => {
      item.addEventListener('click', () => {
        const captureId = item.dataset.id;
        this.openCapture(captureId);
      });
    });
  }

  updateOfflineSection() {
    const offlineSection = document.getElementById('offline-section');
    
    if (this.offlineQueue.length === 0) {
      offlineSection.style.display = 'none';
      return;
    }

    offlineSection.style.display = 'block';
    
    const offlineCount = document.getElementById('offline-count');
    const count = this.offlineQueue.length;
    offlineCount.textContent = `${count} item${count !== 1 ? 's' : ''} waiting to sync`;
  }

  updateSettingsForm() {
    document.getElementById('api-url').value = this.settings.apiUrl;
    document.getElementById('api-key').value = this.settings.apiKey;
    document.getElementById('auto-capture').checked = this.settings.autoCapture;
    document.getElementById('capture-screenshots').checked = this.settings.captureScreenshots;
    document.getElementById('capture-full-page').checked = this.settings.captureFullPage;
  }

  async checkConnection() {
    try {
      const response = await fetch(`${this.settings.apiUrl}/api/health`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.settings.apiKey}`
        }
      });
      return response.ok;
    } catch (error) {
      return false;
    }
  }

  async checkConnectionStatus() {
    const isOnline = await this.checkConnection();
    this.updateConnectionStatus();
    
    // Check again in 30 seconds
    setTimeout(() => {
      this.checkConnectionStatus();
    }, 30000);
  }

  async capturePage() {
    this.showLoading('capture-page');
    
    try {
      const response = await chrome.tabs.sendMessage(this.currentTab.id, {
        type: 'capture-page'
      });
      
      if (response && response.success) {
        this.showSuccess('Page captured successfully!');
        await this.refreshRecentCaptures();
      } else {
        throw new Error('Failed to capture page');
      }
    } catch (error) {
      console.error('Capture page error:', error);
      this.showError('Failed to capture page');
    } finally {
      this.hideLoading('capture-page');
    }
  }

  async captureSelection() {
    this.showLoading('capture-selection');
    
    try {
      const response = await chrome.tabs.sendMessage(this.currentTab.id, {
        type: 'capture-selection'
      });
      
      if (response && response.success) {
        this.showSuccess('Selection captured successfully!');
        await this.refreshRecentCaptures();
      } else {
        throw new Error('Failed to capture selection');
      }
    } catch (error) {
      console.error('Capture selection error:', error);
      this.showError('Failed to capture selection. Please select some text first.');
    } finally {
      this.hideLoading('capture-selection');
    }
  }

  async captureScreenshot() {
    this.showLoading('capture-screenshot');
    
    try {
      const response = await chrome.tabs.sendMessage(this.currentTab.id, {
        type: 'capture-screenshot'
      });
      
      if (response && response.success) {
        this.showSuccess('Screenshot captured successfully!');
        await this.refreshRecentCaptures();
      } else {
        throw new Error('Failed to capture screenshot');
      }
    } catch (error) {
      console.error('Capture screenshot error:', error);
      this.showError('Failed to capture screenshot');
    } finally {
      this.hideLoading('capture-screenshot');
    }
  }

  async saveChatGPTConversation() {
    this.showLoading('save-conversation');
    
    try {
      // Send message to ChatGPT extractor
      const response = await chrome.tabs.sendMessage(this.currentTab.id, {
        type: 'extract-conversation'
      });
      
      if (response && response.success) {
        this.showSuccess('Conversation saved successfully!');
        await this.refreshRecentCaptures();
      } else {
        throw new Error('Failed to save conversation');
      }
    } catch (error) {
      console.error('Save conversation error:', error);
      this.showError('Failed to save conversation');
    } finally {
      this.hideLoading('save-conversation');
    }
  }

  async syncOfflineQueue() {
    this.showLoading('sync-offline');
    
    try {
      const response = await chrome.runtime.sendMessage({
        type: 'sync-offline-queue'
      });
      
      if (response && response.success) {
        this.showSuccess('Offline items synced successfully!');
        await this.loadOfflineQueue();
        this.updateOfflineSection();
      } else {
        throw new Error('Failed to sync offline items');
      }
    } catch (error) {
      console.error('Sync offline error:', error);
      this.showError('Failed to sync offline items');
    } finally {
      this.hideLoading('sync-offline');
    }
  }

  openApp() {
    chrome.tabs.create({
      url: this.settings.apiUrl
    });
    window.close();
  }

  showSettings() {
    document.getElementById('settings-modal').style.display = 'flex';
  }

  hideSettings() {
    document.getElementById('settings-modal').style.display = 'none';
  }

  async saveSettings() {
    const newSettings = {
      apiUrl: document.getElementById('api-url').value.trim(),
      apiKey: document.getElementById('api-key').value.trim(),
      autoCapture: document.getElementById('auto-capture').checked,
      captureScreenshots: document.getElementById('capture-screenshots').checked,
      captureFullPage: document.getElementById('capture-full-page').checked
    };

    // Validate URL
    if (newSettings.apiUrl && !this.isValidUrl(newSettings.apiUrl)) {
      this.showError('Please enter a valid URL');
      return;
    }

    try {
      await chrome.storage.sync.set(newSettings);
      this.settings = newSettings;
      this.showSuccess('Settings saved successfully!');
      this.hideSettings();
      this.updateConnectionStatus();
    } catch (error) {
      console.error('Save settings error:', error);
      this.showError('Failed to save settings');
    }
  }

  async refreshRecentCaptures() {
    // Fetch recent captures from API or storage
    try {
      const response = await fetch(`${this.settings.apiUrl}/api/captures/recent`, {
        headers: {
          'Authorization': `Bearer ${this.settings.apiKey}`
        }
      });
      
      if (response.ok) {
        const captures = await response.json();
        this.recentCaptures = captures;
        await chrome.storage.local.set({ recentCaptures: captures });
        this.updateRecentCaptures();
      }
    } catch (error) {
      console.error('Failed to refresh recent captures:', error);
    }
  }

  openCapture(captureId) {
    const url = `${this.settings.apiUrl}/capture/${captureId}`;
    chrome.tabs.create({ url });
    window.close();
  }

  showLoading(buttonId) {
    const button = document.getElementById(buttonId);
    if (button) {
      button.disabled = true;
      const originalText = button.querySelector('.btn-text').textContent;
      button.querySelector('.btn-text').textContent = 'Loading...';
      button.dataset.originalText = originalText;
    }
  }

  hideLoading(buttonId) {
    const button = document.getElementById(buttonId);
    if (button) {
      button.disabled = false;
      const originalText = button.dataset.originalText;
      if (originalText) {
        button.querySelector('.btn-text').textContent = originalText;
      }
    }
  }

  showSuccess(message) {
    this.showNotification(message, 'success');
  }

  showError(message) {
    this.showNotification(message, 'error');
  }

  showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    notification.style.cssText = `
      position: fixed;
      top: 10px;
      right: 10px;
      padding: 12px 16px;
      border-radius: 6px;
      color: white;
      font-size: 13px;
      font-weight: 500;
      z-index: 1001;
      max-width: 300px;
      word-wrap: break-word;
      animation: slideIn 0.3s ease-out;
      background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#007cba'};
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
      notification.remove();
    }, 3000);
  }

  isChatGPTUrl(url) {
    return url && (
      url.includes('chat.openai.com') || 
      url.includes('chatgpt.com')
    );
  }

  isValidUrl(string) {
    try {
      new URL(string);
      return true;
    } catch (_) {
      return false;
    }
  }

  getTypeIcon(type) {
    const icons = {
      'webpage': '📄',
      'text-selection': '✂️',
      'screenshot': '📸',
      'chatgpt-conversation': '💬',
      'pdf': '📋',
      'image': '🖼️',
      'video': '🎥',
      'audio': '🎵'
    };
    return icons[type] || '📄';
  }

  getTimeAgo(timestamp) {
    const now = Date.now();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  }

  truncateText(text, maxLength) {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength - 3) + '...';
  }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new PopupController();
});

// Add CSS for notifications
const style = document.createElement('style');
style.textContent = `
  @keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
  }
`;
document.head.appendChild(style);