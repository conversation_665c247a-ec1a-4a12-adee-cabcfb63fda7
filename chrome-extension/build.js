#!/usr/bin/env node

/**
 * Build script for Lightbulb Second Brain Chrome Extension
 * Helps with development tasks like icon generation and packaging
 */

const fs = require('fs');
const path = require('path');

class ExtensionBuilder {
  constructor() {
    this.extensionDir = __dirname;
    this.manifestPath = path.join(this.extensionDir, 'manifest.json');
  }

  async build() {
    console.log('🔨 Building Lightbulb Second Brain Chrome Extension...\n');

    try {
      await this.validateManifest();
      await this.checkRequiredFiles();
      await this.generateIconPlaceholders();
      await this.validatePermissions();
      
      console.log('✅ Build completed successfully!');
      console.log('\n📦 Extension is ready for loading in Chrome Developer mode');
      console.log('📍 Load unpacked extension from:', this.extensionDir);
      
    } catch (error) {
      console.error('❌ Build failed:', error.message);
      process.exit(1);
    }
  }

  async validateManifest() {
    console.log('📋 Validating manifest.json...');
    
    if (!fs.existsSync(this.manifestPath)) {
      throw new Error('manifest.json not found');
    }

    const manifest = JSON.parse(fs.readFileSync(this.manifestPath, 'utf8'));
    
    // Check required fields
    const requiredFields = ['name', 'version', 'manifest_version', 'permissions'];
    for (const field of requiredFields) {
      if (!manifest[field]) {
        throw new Error(`Missing required field in manifest: ${field}`);
      }
    }

    // Validate manifest version
    if (manifest.manifest_version !== 3) {
      console.warn('⚠️  Warning: Using Manifest V2. Consider upgrading to V3');
    }

    console.log(`   ✓ Extension: ${manifest.name} v${manifest.version}`);
  }

  async checkRequiredFiles() {
    console.log('📁 Checking required files...');
    
    const requiredFiles = [
      'background.js',
      'content.js',
      'content.css',
      'chatgpt-extractor.js',
      'popup.html',
      'popup.css',
      'popup.js',
      'welcome.html'
    ];

    for (const file of requiredFiles) {
      const filePath = path.join(this.extensionDir, file);
      if (!fs.existsSync(filePath)) {
        throw new Error(`Required file missing: ${file}`);
      }
      console.log(`   ✓ ${file}`);
    }
  }

  async generateIconPlaceholders() {
    console.log('🎨 Checking icons...');
    
    const iconDir = path.join(this.extensionDir, 'icons');
    const requiredIcons = ['icon16.png', 'icon32.png', 'icon48.png', 'icon128.png'];
    
    if (!fs.existsSync(iconDir)) {
      fs.mkdirSync(iconDir, { recursive: true });
    }

    let missingIcons = [];
    for (const icon of requiredIcons) {
      const iconPath = path.join(iconDir, icon);
      if (!fs.existsSync(iconPath)) {
        missingIcons.push(icon);
      } else {
        console.log(`   ✓ ${icon}`);
      }
    }

    if (missingIcons.length > 0) {
      console.log('   ⚠️  Missing icon files:', missingIcons.join(', '));
      console.log('   📖 See icons/README.md for generation instructions');
      
      // Create placeholder text files
      for (const icon of missingIcons) {
        const placeholderPath = path.join(iconDir, icon + '.placeholder');
        fs.writeFileSync(placeholderPath, 
          `This is a placeholder for ${icon}\n` +
          `Generate the actual PNG file using the SVG icon.\n` +
          `See icons/README.md for instructions.`
        );
      }
    }
  }

  async validatePermissions() {
    console.log('🔒 Validating permissions...');
    
    const manifest = JSON.parse(fs.readFileSync(this.manifestPath, 'utf8'));
    const permissions = manifest.permissions || [];
    const hostPermissions = manifest.host_permissions || [];

    console.log('   Permissions:', permissions.join(', '));
    console.log('   Host permissions:', hostPermissions.join(', '));

    // Check for potentially sensitive permissions
    const sensitivePermissions = ['tabs', 'activeTab', 'storage'];
    const usedSensitive = permissions.filter(p => sensitivePermissions.includes(p));
    
    if (usedSensitive.length > 0) {
      console.log('   ⚠️  Using sensitive permissions:', usedSensitive.join(', '));
      console.log('   📝 Ensure these are necessary for functionality');
    }
  }

  async package() {
    console.log('📦 Packaging extension...');
    
    // This would create a ZIP file for Chrome Web Store
    // For now, just provide instructions
    console.log('   📋 To package for Chrome Web Store:');
    console.log('   1. Ensure all icon files are generated');
    console.log('   2. Test thoroughly in development mode');
    console.log('   3. Create ZIP of entire extension directory');
    console.log('   4. Upload to Chrome Web Store Developer Dashboard');
  }

  async clean() {
    console.log('🧹 Cleaning build artifacts...');
    
    const cleanPatterns = [
      '*.placeholder',
      '*.log',
      '.DS_Store',
      'Thumbs.db'
    ];

    // Clean files matching patterns
    const cleanFile = (dir, patterns) => {
      const files = fs.readdirSync(dir);
      files.forEach(file => {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        
        if (stat.isDirectory()) {
          cleanFile(filePath, patterns);
        } else {
          for (const pattern of patterns) {
            if (file.endsWith(pattern.replace('*', ''))) {
              fs.unlinkSync(filePath);
              console.log(`   🗑️  Removed: ${file}`);
            }
          }
        }
      });
    };

    cleanFile(this.extensionDir, cleanPatterns);
  }
}

// CLI interface
const command = process.argv[2] || 'build';
const builder = new ExtensionBuilder();

switch (command) {
  case 'build':
    builder.build();
    break;
  case 'package':
    builder.package();
    break;
  case 'clean':
    builder.clean();
    break;
  case 'help':
    console.log('Lightbulb Second Brain Extension Builder\n');
    console.log('Commands:');
    console.log('  build   - Build and validate extension (default)');
    console.log('  package - Package for Chrome Web Store');
    console.log('  clean   - Clean build artifacts');
    console.log('  help    - Show this help');
    break;
  default:
    console.error('Unknown command:', command);
    console.log('Use "node build.js help" for available commands');
    process.exit(1);
}