// ChatGPT conversation extractor for supported sites

class ChatGPTExtractor {
  constructor() {
    this.conversations = new Map();
    this.isExtracting = false;
    this.setupExtraction();
    this.setupUI();
  }

  setupExtraction() {
    // Monitor for new messages
    this.observeConversations();
    
    // Extract existing conversation on load
    setTimeout(() => {
      this.extractCurrentConversation();
    }, 2000);
  }

  observeConversations() {
    // Create observer for ChatGPT message container
    const observer = new MutationObserver((mutations) => {
      let hasNewMessages = false;
      
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          // Check if new message elements were added
          const hasMessageNodes = Array.from(mutation.addedNodes).some(node => 
            node.nodeType === Node.ELEMENT_NODE && 
            (node.matches?.('[data-message-author-role]') || 
             node.querySelector?.('[data-message-author-role]'))
          );
          
          if (hasMessageNodes) {
            hasNewMessages = true;
          }
        }
      });

      if (hasNewMessages) {
        // Debounce extraction to avoid excessive calls
        clearTimeout(this.extractionTimeout);
        this.extractionTimeout = setTimeout(() => {
          this.extractCurrentConversation();
        }, 1000);
      }
    });

    // Start observing
    const targetNode = document.body;
    observer.observe(targetNode, {
      childList: true,
      subtree: true
    });

    this.conversationObserver = observer;
  }

  extractCurrentConversation() {
    if (this.isExtracting) return;
    this.isExtracting = true;

    try {
      const conversation = this.parseConversation();
      if (conversation && conversation.messages.length > 0) {
        this.conversations.set(conversation.id, conversation);
        this.updateSaveButton(conversation);
      }
    } catch (error) {
      console.error('Failed to extract conversation:', error);
    } finally {
      this.isExtracting = false;
    }
  }

  parseConversation() {
    // Try different selectors for different ChatGPT versions
    const messageSelectors = [
      '[data-message-author-role]',
      '.message',
      '[class*="message"]',
      '[class*="conversation-turn"]'
    ];

    let messageElements = [];
    for (const selector of messageSelectors) {
      messageElements = document.querySelectorAll(selector);
      if (messageElements.length > 0) break;
    }

    if (messageElements.length === 0) {
      // Fallback: try to find messages by content structure
      messageElements = this.findMessagesByStructure();
    }

    const messages = [];
    const conversationId = this.getConversationId();

    messageElements.forEach((element, index) => {
      const message = this.parseMessage(element, index);
      if (message) {
        messages.push(message);
      }
    });

    return {
      id: conversationId,
      title: this.getConversationTitle(),
      messages: messages,
      model: this.detectModel(),
      url: window.location.href,
      timestamp: Date.now()
    };
  }

  parseMessage(element, index) {
    try {
      // Determine role (user or assistant)
      const role = this.determineMessageRole(element);
      
      // Extract message content
      const content = this.extractMessageContent(element);
      
      if (!content || content.trim().length === 0) {
        return null;
      }

      return {
        id: `msg_${index}`,
        role: role,
        content: content.trim(),
        timestamp: Date.now(),
        element: element.outerHTML // Store for reference
      };
    } catch (error) {
      console.error('Failed to parse message:', error);
      return null;
    }
  }

  determineMessageRole(element) {
    // Check data attributes
    const authorRole = element.getAttribute('data-message-author-role');
    if (authorRole) {
      return authorRole === 'user' ? 'user' : 'assistant';
    }

    // Check for class names or other indicators
    const elementText = element.textContent || '';
    const elementHTML = element.innerHTML || '';
    
    // Look for user indicators
    if (element.querySelector('[class*="user"]') || 
        elementHTML.includes('user-message') ||
        element.closest('[class*="user"]')) {
      return 'user';
    }

    // Look for assistant indicators
    if (element.querySelector('[class*="assistant"]') || 
        element.querySelector('[class*="gpt"]') ||
        elementHTML.includes('assistant-message') ||
        element.closest('[class*="assistant"]')) {
      return 'assistant';
    }

    // Fallback: alternate between user and assistant
    const allMessages = document.querySelectorAll('[data-message-author-role], .message, [class*="message"]');
    const messageIndex = Array.from(allMessages).indexOf(element);
    return messageIndex % 2 === 0 ? 'user' : 'assistant';
  }

  extractMessageContent(element) {
    // Try to find the main content area
    const contentSelectors = [
      '[class*="content"]',
      '[class*="text"]',
      '[class*="message-content"]',
      'p',
      'div'
    ];

    let contentElement = null;
    for (const selector of contentSelectors) {
      contentElement = element.querySelector(selector);
      if (contentElement && contentElement.textContent?.trim()) {
        break;
      }
    }

    // Fallback to element itself
    if (!contentElement) {
      contentElement = element;
    }

    // Extract text content, preserving some formatting
    let content = '';
    
    // Handle code blocks specially
    const codeBlocks = contentElement.querySelectorAll('pre, code');
    codeBlocks.forEach((block, index) => {
      const placeholder = `__CODE_BLOCK_${index}__`;
      block.setAttribute('data-placeholder', placeholder);
    });

    // Get text content
    content = contentElement.textContent || '';

    // Restore code blocks with formatting
    codeBlocks.forEach((block, index) => {
      const placeholder = `__CODE_BLOCK_${index}__`;
      const codeContent = block.textContent || '';
      content = content.replace(placeholder, `\n\`\`\`\n${codeContent}\n\`\`\`\n`);
    });

    return content;
  }

  findMessagesByStructure() {
    // Fallback method to find messages by DOM structure
    const possibleContainers = document.querySelectorAll('div');
    const messageElements = [];

    possibleContainers.forEach(container => {
      const text = container.textContent?.trim();
      if (text && text.length > 20 && text.length < 10000) {
        // Check if it looks like a message
        const hasUserIndicators = /^(you|user|me):/i.test(text);
        const hasAssistantIndicators = /^(chatgpt|assistant|ai):/i.test(text);
        const hasConversationalContent = /\?|\.|!/.test(text);

        if (hasUserIndicators || hasAssistantIndicators || hasConversationalContent) {
          messageElements.push(container);
        }
      }
    });

    return messageElements;
  }

  getConversationId() {
    // Try to extract conversation ID from URL
    const urlMatch = window.location.pathname.match(/\/c\/([a-f0-9-]+)/);
    if (urlMatch) {
      return urlMatch[1];
    }

    // Fallback to page-based ID
    return `conv_${window.location.pathname.replace(/[^a-zA-Z0-9]/g, '_')}_${Date.now()}`;
  }

  getConversationTitle() {
    // Try to find conversation title
    const titleSelectors = [
      'h1',
      '[class*="title"]',
      '[class*="conversation-title"]',
      'title'
    ];

    for (const selector of titleSelectors) {
      const titleElement = document.querySelector(selector);
      if (titleElement && titleElement.textContent?.trim()) {
        const title = titleElement.textContent.trim();
        if (title !== 'ChatGPT' && title.length > 0) {
          return title;
        }
      }
    }

    return `ChatGPT Conversation - ${new Date().toLocaleDateString()}`;
  }

  detectModel() {
    // Try to detect which model is being used
    const pageText = document.body.textContent || '';
    
    if (pageText.includes('GPT-4') || pageText.includes('gpt-4')) {
      return 'GPT-4';
    } else if (pageText.includes('GPT-3.5') || pageText.includes('gpt-3.5')) {
      return 'GPT-3.5';
    }

    return 'ChatGPT';
  }

  setupUI() {
    this.createSaveButton();
  }

  createSaveButton() {
    // Remove existing button
    const existingButton = document.getElementById('lightbulb-save-conversation');
    if (existingButton) {
      existingButton.remove();
    }

    // Create save button
    const saveButton = document.createElement('button');
    saveButton.id = 'lightbulb-save-conversation';
    saveButton.innerHTML = '💡 Save to Second Brain';
    saveButton.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 10000;
      background: #007cba;
      color: white;
      border: none;
      padding: 10px 16px;
      border-radius: 6px;
      cursor: pointer;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      font-weight: 500;
      box-shadow: 0 2px 8px rgba(0,0,0,0.15);
      transition: all 0.2s ease;
      opacity: 0.8;
    `;

    saveButton.addEventListener('click', () => {
      this.saveCurrentConversation();
    });

    saveButton.addEventListener('mouseenter', () => {
      saveButton.style.opacity = '1';
      saveButton.style.transform = 'translateY(-1px)';
    });

    saveButton.addEventListener('mouseleave', () => {
      saveButton.style.opacity = '0.8';
      saveButton.style.transform = 'translateY(0)';
    });

    document.body.appendChild(saveButton);
    this.saveButton = saveButton;
  }

  updateSaveButton(conversation) {
    if (this.saveButton && conversation) {
      const messageCount = conversation.messages.length;
      this.saveButton.innerHTML = `💡 Save Conversation (${messageCount} messages)`;
      
      // Highlight button when new messages are detected
      this.saveButton.style.background = '#28a745';
      setTimeout(() => {
        this.saveButton.style.background = '#007cba';
      }, 1000);
    }
  }

  saveCurrentConversation() {
    const conversationId = this.getConversationId();
    const conversation = this.conversations.get(conversationId);

    if (!conversation || conversation.messages.length === 0) {
      this.showNotification('No conversation found to save');
      return;
    }

    // Send to background script
    chrome.runtime.sendMessage({
      type: 'save-chatgpt-conversation',
      data: conversation
    }, (response) => {
      if (response && response.success) {
        this.showNotification(`Saved ${conversation.messages.length} messages to Second Brain!`);
        this.saveButton.innerHTML = '✅ Saved!';
        setTimeout(() => {
          this.saveButton.innerHTML = '💡 Save to Second Brain';
        }, 2000);
      } else {
        this.showNotification('Failed to save conversation');
      }
    });
  }

  showNotification(message) {
    const notification = document.createElement('div');
    notification.textContent = message;
    notification.style.cssText = `
      position: fixed;
      top: 80px;
      right: 20px;
      background: #4CAF50;
      color: white;
      padding: 12px 20px;
      border-radius: 6px;
      z-index: 10001;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.2);
      animation: slideIn 0.3s ease-out;
    `;

    // Add animation
    const style = document.createElement('style');
    style.textContent = `
      @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
      }
    `;
    document.head.appendChild(style);

    document.body.appendChild(notification);

    setTimeout(() => {
      notification.remove();
      style.remove();
    }, 3000);
  }

  destroy() {
    if (this.conversationObserver) {
      this.conversationObserver.disconnect();
    }
    
    if (this.saveButton) {
      this.saveButton.remove();
    }

    clearTimeout(this.extractionTimeout);
  }
}

// Initialize ChatGPT extractor
let chatgptExtractor = null;

// Check if we're on a ChatGPT page
if (window.location.hostname.includes('openai.com') || 
    window.location.hostname.includes('chatgpt.com')) {
  
  // Wait for page to load
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      setTimeout(() => {
        chatgptExtractor = new ChatGPTExtractor();
      }, 1000);
    });
  } else {
    setTimeout(() => {
      chatgptExtractor = new ChatGPTExtractor();
    }, 1000);
  }
}

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
  if (chatgptExtractor) {
    chatgptExtractor.destroy();
  }
});