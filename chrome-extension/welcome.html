<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Welcome to Lightbulb Second Brain</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
      line-height: 1.6;
      color: #333;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .welcome-container {
      background: white;
      border-radius: 16px;
      padding: 40px;
      max-width: 600px;
      width: 90%;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
      text-align: center;
    }

    .logo {
      font-size: 64px;
      margin-bottom: 20px;
    }

    h1 {
      font-size: 32px;
      font-weight: 700;
      margin-bottom: 16px;
      color: #2c3e50;
    }

    .subtitle {
      font-size: 18px;
      color: #7f8c8d;
      margin-bottom: 32px;
    }

    .features {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 24px;
      margin-bottom: 32px;
    }

    .feature {
      padding: 20px;
      background: #f8f9fa;
      border-radius: 12px;
      text-align: left;
    }

    .feature-icon {
      font-size: 32px;
      margin-bottom: 12px;
      display: block;
    }

    .feature h3 {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 8px;
      color: #2c3e50;
    }

    .feature p {
      font-size: 14px;
      color: #6c757d;
    }

    .setup-section {
      background: #e8f4fd;
      border: 1px solid #b8daff;
      border-radius: 12px;
      padding: 24px;
      margin-bottom: 32px;
      text-align: left;
    }

    .setup-section h3 {
      font-size: 20px;
      font-weight: 600;
      margin-bottom: 16px;
      color: #004085;
    }

    .setup-steps {
      list-style: none;
      counter-reset: step-counter;
    }

    .setup-steps li {
      counter-increment: step-counter;
      margin-bottom: 12px;
      padding-left: 32px;
      position: relative;
      font-size: 14px;
      color: #495057;
    }

    .setup-steps li::before {
      content: counter(step-counter);
      position: absolute;
      left: 0;
      top: 0;
      background: #007cba;
      color: white;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      font-weight: 600;
    }

    .actions {
      display: flex;
      gap: 16px;
      justify-content: center;
      flex-wrap: wrap;
    }

    .btn {
      padding: 12px 24px;
      border: none;
      border-radius: 8px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: 8px;
      transition: all 0.2s ease;
    }

    .btn-primary {
      background: #007cba;
      color: white;
    }

    .btn-primary:hover {
      background: #005a87;
      transform: translateY(-1px);
    }

    .btn-secondary {
      background: #6c757d;
      color: white;
    }

    .btn-secondary:hover {
      background: #545b62;
      transform: translateY(-1px);
    }

    .keyboard-shortcuts {
      margin-top: 24px;
      padding: 16px;
      background: #f8f9fa;
      border-radius: 8px;
      text-align: left;
    }

    .keyboard-shortcuts h4 {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 12px;
      color: #2c3e50;
    }

    .shortcut {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      font-size: 13px;
    }

    .shortcut-key {
      background: #e9ecef;
      padding: 4px 8px;
      border-radius: 4px;
      font-family: monospace;
      font-size: 12px;
    }

    @media (max-width: 768px) {
      .welcome-container {
        padding: 24px;
        margin: 20px;
      }

      h1 {
        font-size: 24px;
      }

      .subtitle {
        font-size: 16px;
      }

      .features {
        grid-template-columns: 1fr;
      }

      .actions {
        flex-direction: column;
      }
    }
  </style>
</head>
<body>
  <div class="welcome-container">
    <div class="logo">💡</div>
    <h1>Welcome to Lightbulb Second Brain</h1>
    <p class="subtitle">Your AI-powered knowledge management companion</p>

    <div class="features">
      <div class="feature">
        <span class="feature-icon">📄</span>
        <h3>Capture Anything</h3>
        <p>Save web pages, text selections, screenshots, and ChatGPT conversations with one click</p>
      </div>
      <div class="feature">
        <span class="feature-icon">🤖</span>
        <h3>AI-Powered Chat</h3>
        <p>Chat with any content you've saved using advanced AI models</p>
      </div>
      <div class="feature">
        <span class="feature-icon">🔍</span>
        <h3>Smart Search</h3>
        <p>Find anything in your knowledge base with semantic search</p>
      </div>
      <div class="feature">
        <span class="feature-icon">📱</span>
        <h3>Works Everywhere</h3>
        <p>Capture content from any website and sync across all your devices</p>
      </div>
    </div>

    <div class="setup-section">
      <h3>Quick Setup</h3>
      <ol class="setup-steps">
        <li>Make sure your Lightbulb Second Brain application is running (default: http://localhost:1420)</li>
        <li>Click the extension icon in your browser toolbar to open the popup</li>
        <li>Go to Settings and configure your API URL if different from default</li>
        <li>Start capturing content by clicking "Save Page" or selecting text and using the popup</li>
        <li>Visit ChatGPT and save conversations with the floating save button</li>
      </ol>
    </div>

    <div class="keyboard-shortcuts">
      <h4>Keyboard Shortcuts</h4>
      <div class="shortcut">
        <span>Save selected text</span>
        <span class="shortcut-key">Ctrl+Shift+S</span>
      </div>
      <div class="shortcut">
        <span>Save current page</span>
        <span class="shortcut-key">Ctrl+Shift+P</span>
      </div>
      <div class="shortcut">
        <span>Capture screenshot</span>
        <span class="shortcut-key">Ctrl+Shift+C</span>
      </div>
    </div>

    <div class="actions">
      <button class="btn btn-primary" onclick="openApp()">
        <span>🚀</span>
        Open Second Brain App
      </button>
      <button class="btn btn-secondary" onclick="openSettings()">
        <span>⚙️</span>
        Extension Settings
      </button>
    </div>
  </div>

  <script>
    function openApp() {
      chrome.storage.sync.get(['apiUrl'], (result) => {
        const apiUrl = result.apiUrl || 'http://localhost:1420';
        chrome.tabs.create({ url: apiUrl });
      });
    }

    function openSettings() {
      chrome.runtime.openOptionsPage();
    }

    // Auto-close after 30 seconds if user doesn't interact
    let autoCloseTimer = setTimeout(() => {
      window.close();
    }, 30000);

    // Cancel auto-close on user interaction
    document.addEventListener('click', () => {
      clearTimeout(autoCloseTimer);
    });

    document.addEventListener('keydown', () => {
      clearTimeout(autoCloseTimer);
    });
  </script>
</body>
</html>