// Background service worker for Lightbulb Second Brain extension

class BackgroundService {
  constructor() {
    this.setupEventListeners();
    this.setupContextMenus();
    this.offlineQueue = [];
    this.isOnline = true;
  }

  setupEventListeners() {
    // Extension installation
    chrome.runtime.onInstalled.addListener((details) => {
      if (details.reason === 'install') {
        this.handleFirstInstall();
      }
    });

    // Message handling from content scripts and popup
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true; // Keep message channel open for async responses
    });

    // Tab updates for ChatGPT detection
    chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
      if (changeInfo.status === 'complete' && this.isChatGPTUrl(tab.url)) {
        this.injectChatGPTExtractor(tabId);
      }
    });

    // Network status monitoring
    chrome.runtime.onConnect.addListener((port) => {
      if (port.name === 'network-status') {
        port.onMessage.addListener((msg) => {
          if (msg.type === 'network-status') {
            this.isOnline = msg.online;
            if (this.isOnline) {
              this.processOfflineQueue();
            }
          }
        });
      }
    });
  }

  setupContextMenus() {
    chrome.contextMenus.create({
      id: 'save-selection',
      title: 'Save to Second Brain',
      contexts: ['selection']
    });

    chrome.contextMenus.create({
      id: 'save-page',
      title: 'Save Page to Second Brain',
      contexts: ['page']
    });

    chrome.contextMenus.create({
      id: 'capture-screenshot',
      title: 'Capture Screenshot',
      contexts: ['page']
    });

    chrome.contextMenus.onClicked.addListener((info, tab) => {
      this.handleContextMenuClick(info, tab);
    });
  }

  async handleMessage(message, sender, sendResponse) {
    try {
      switch (message.type) {
        case 'capture-selection':
          await this.captureSelection(message.data, sender.tab);
          sendResponse({ success: true });
          break;

        case 'capture-page':
          await this.capturePage(message.data, sender.tab);
          sendResponse({ success: true });
          break;

        case 'capture-screenshot':
          const screenshot = await this.captureScreenshot(sender.tab.id);
          sendResponse({ success: true, screenshot });
          break;

        case 'save-chatgpt-conversation':
          await this.saveChatGPTConversation(message.data, sender.tab);
          sendResponse({ success: true });
          break;

        case 'get-settings':
          const settings = await this.getSettings();
          sendResponse({ settings });
          break;

        case 'save-settings':
          await this.saveSettings(message.settings);
          sendResponse({ success: true });
          break;

        case 'sync-offline-queue':
          await this.processOfflineQueue();
          sendResponse({ success: true });
          break;

        default:
          sendResponse({ error: 'Unknown message type' });
      }
    } catch (error) {
      console.error('Background script error:', error);
      sendResponse({ error: error.message });
    }
  }

  async handleContextMenuClick(info, tab) {
    switch (info.menuItemId) {
      case 'save-selection':
        await this.captureSelection({ text: info.selectionText }, tab);
        break;
      case 'save-page':
        await this.capturePage({}, tab);
        break;
      case 'capture-screenshot':
        await this.captureScreenshot(tab.id);
        break;
    }
  }

  async captureSelection(data, tab) {
    const captureData = {
      type: 'text-selection',
      content: data.text,
      url: tab.url,
      title: tab.title,
      timestamp: Date.now(),
      metadata: {
        selectedText: data.text,
        context: data.context || '',
        pageTitle: tab.title,
        pageUrl: tab.url
      }
    };

    await this.saveToSecondBrain(captureData);
  }

  async capturePage(data, tab) {
    // Inject content script to extract page content
    const results = await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      function: this.extractPageContent
    });

    const pageContent = results[0].result;
    
    const captureData = {
      type: 'webpage',
      content: pageContent.text,
      url: tab.url,
      title: tab.title,
      timestamp: Date.now(),
      metadata: {
        fullContent: pageContent.html,
        extractedText: pageContent.text,
        images: pageContent.images,
        links: pageContent.links,
        pageTitle: tab.title,
        pageUrl: tab.url
      }
    };

    await this.saveToSecondBrain(captureData);
  }

  async captureScreenshot(tabId) {
    try {
      const screenshot = await chrome.tabs.captureVisibleTab(null, {
        format: 'png',
        quality: 90
      });

      const captureData = {
        type: 'screenshot',
        content: screenshot,
        timestamp: Date.now(),
        metadata: {
          format: 'png',
          captureMethod: 'chrome-api'
        }
      };

      await this.saveToSecondBrain(captureData);
      return screenshot;
    } catch (error) {
      console.error('Screenshot capture failed:', error);
      throw error;
    }
  }

  async saveChatGPTConversation(conversationData, tab) {
    const captureData = {
      type: 'chatgpt-conversation',
      content: conversationData.messages,
      url: tab.url,
      title: conversationData.title || tab.title,
      timestamp: Date.now(),
      metadata: {
        conversationId: conversationData.conversationId,
        messages: conversationData.messages,
        model: conversationData.model,
        pageUrl: tab.url
      }
    };

    await this.saveToSecondBrain(captureData);
  }

  async saveToSecondBrain(captureData) {
    if (!this.isOnline) {
      // Add to offline queue
      this.offlineQueue.push(captureData);
      await this.saveOfflineQueue();
      this.showNotification('Content saved offline. Will sync when online.');
      return;
    }

    try {
      const settings = await this.getSettings();
      const apiUrl = settings.apiUrl || 'http://localhost:1420';
      
      const response = await fetch(`${apiUrl}/api/content/capture`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${settings.apiKey || ''}`
        },
        body: JSON.stringify(captureData)
      });

      if (response.ok) {
        this.showNotification('Content saved to Second Brain!');
      } else {
        throw new Error(`API error: ${response.status}`);
      }
    } catch (error) {
      console.error('Failed to save to Second Brain:', error);
      // Add to offline queue as fallback
      this.offlineQueue.push(captureData);
      await this.saveOfflineQueue();
      this.showNotification('Content saved offline. Will sync when connection is restored.');
    }
  }

  async processOfflineQueue() {
    if (this.offlineQueue.length === 0) return;

    const settings = await this.getSettings();
    const apiUrl = settings.apiUrl || 'http://localhost:1420';
    
    const processedItems = [];
    
    for (const item of this.offlineQueue) {
      try {
        const response = await fetch(`${apiUrl}/api/content/capture`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${settings.apiKey || ''}`
          },
          body: JSON.stringify(item)
        });

        if (response.ok) {
          processedItems.push(item);
        }
      } catch (error) {
        console.error('Failed to sync offline item:', error);
        break; // Stop processing if we hit an error
      }
    }

    // Remove successfully processed items
    this.offlineQueue = this.offlineQueue.filter(item => !processedItems.includes(item));
    await this.saveOfflineQueue();

    if (processedItems.length > 0) {
      this.showNotification(`Synced ${processedItems.length} offline items to Second Brain!`);
    }
  }

  async saveOfflineQueue() {
    await chrome.storage.local.set({ offlineQueue: this.offlineQueue });
  }

  async loadOfflineQueue() {
    const result = await chrome.storage.local.get(['offlineQueue']);
    this.offlineQueue = result.offlineQueue || [];
  }

  async getSettings() {
    const result = await chrome.storage.sync.get([
      'apiUrl',
      'apiKey',
      'autoCapture',
      'captureScreenshots',
      'captureFullPage'
    ]);
    
    return {
      apiUrl: result.apiUrl || 'http://localhost:1420',
      apiKey: result.apiKey || '',
      autoCapture: result.autoCapture !== false,
      captureScreenshots: result.captureScreenshots !== false,
      captureFullPage: result.captureFullPage !== false
    };
  }

  async saveSettings(settings) {
    await chrome.storage.sync.set(settings);
  }

  showNotification(message) {
    chrome.notifications.create({
      type: 'basic',
      iconUrl: 'icons/icon48.png',
      title: 'Lightbulb Second Brain',
      message: message
    });
  }

  isChatGPTUrl(url) {
    return url && (
      url.includes('chat.openai.com') || 
      url.includes('chatgpt.com')
    );
  }

  async injectChatGPTExtractor(tabId) {
    try {
      await chrome.scripting.executeScript({
        target: { tabId },
        files: ['chatgpt-extractor.js']
      });
    } catch (error) {
      console.error('Failed to inject ChatGPT extractor:', error);
    }
  }

  handleFirstInstall() {
    chrome.tabs.create({
      url: chrome.runtime.getURL('welcome.html')
    });
  }

  // Function to be injected for page content extraction
  extractPageContent() {
    const content = {
      text: '',
      html: '',
      images: [],
      links: []
    };

    // Extract main text content
    const textElements = document.querySelectorAll('p, h1, h2, h3, h4, h5, h6, li, td, th, div');
    const textContent = Array.from(textElements)
      .map(el => el.textContent?.trim())
      .filter(text => text && text.length > 10)
      .join('\n');

    content.text = textContent;
    content.html = document.documentElement.outerHTML;

    // Extract images
    const images = document.querySelectorAll('img');
    content.images = Array.from(images).map(img => ({
      src: img.src,
      alt: img.alt,
      title: img.title
    }));

    // Extract links
    const links = document.querySelectorAll('a[href]');
    content.links = Array.from(links).map(link => ({
      href: link.href,
      text: link.textContent?.trim(),
      title: link.title
    }));

    return content;
  }
}

// Initialize the background service
const backgroundService = new BackgroundService();
backgroundService.loadOfflineQueue();