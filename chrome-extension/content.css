/* Styles for Lightbulb Second Brain content script elements */

#lightbulb-selection-popup {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, Oxygen, Ubuntu, Cantarell, sans-serif !important;
  font-size: 14px !important;
  line-height: 1.4 !important;
  color: #333 !important;
}

.lightbulb-popup-content {
  display: flex !important;
  gap: 8px !important;
  align-items: center !important;
}

.lightbulb-popup-content button {
  background: #007cba !important;
  color: white !important;
  border: none !important;
  padding: 6px 12px !important;
  border-radius: 4px !important;
  cursor: pointer !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  transition: background-color 0.2s ease !important;
  white-space: nowrap !important;
}

.lightbulb-popup-content button:hover {
  background: #005a87 !important;
}

.lightbulb-popup-content button:active {
  transform: translateY(1px) !important;
}

#lightbulb-capture-overlay {
  font-family: -apple-system, BlinkMacSystemFont, 'Sego<PERSON> UI', <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ell, sans-serif !important;
}

.lightbulb-overlay-content {
  background: white !important;
  border-radius: 12px !important;
  padding: 24px !important;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2) !important;
  position: relative !important;
  max-width: 400px !important;
  width: 90% !important;
}

.lightbulb-overlay-content h3 {
  margin: 0 0 20px 0 !important;
  font-size: 20px !important;
  font-weight: 600 !important;
  color: #333 !important;
  text-align: center !important;
}

.lightbulb-capture-options {
  display: flex !important;
  flex-direction: column !important;
  gap: 12px !important;
}

.lightbulb-capture-options button {
  background: #007cba !important;
  color: white !important;
  border: none !important;
  padding: 12px 20px !important;
  border-radius: 8px !important;
  cursor: pointer !important;
  font-size: 16px !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
  text-align: left !important;
}

.lightbulb-capture-options button:hover {
  background: #005a87 !important;
  transform: translateY(-1px) !important;
}

.lightbulb-capture-options button:active {
  transform: translateY(0) !important;
}

#lightbulb-close-overlay {
  position: absolute !important;
  top: 12px !important;
  right: 12px !important;
  background: #f0f0f0 !important;
  color: #666 !important;
  border: none !important;
  width: 32px !important;
  height: 32px !important;
  border-radius: 50% !important;
  cursor: pointer !important;
  font-size: 16px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: background-color 0.2s ease !important;
}

#lightbulb-close-overlay:hover {
  background: #e0e0e0 !important;
}

/* Notification styles */
.lightbulb-notification {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  animation: lightbulb-slide-in 0.3s ease-out !important;
}

@keyframes lightbulb-slide-in {
  from {
    transform: translateX(100%) !important;
    opacity: 0 !important;
  }
  to {
    transform: translateX(0) !important;
    opacity: 1 !important;
  }
}

/* Selection highlight enhancement */
::selection {
  background-color: rgba(0, 124, 186, 0.2) !important;
}

::-moz-selection {
  background-color: rgba(0, 124, 186, 0.2) !important;
}

/* Ensure our elements don't inherit page styles */
#lightbulb-selection-popup *,
#lightbulb-capture-overlay *,
.lightbulb-notification * {
  box-sizing: border-box !important;
  margin: 0 !important;
  padding: 0 !important;
  border: 0 !important;
  font: inherit !important;
  vertical-align: baseline !important;
}

/* Reset specific properties that might be overridden */
#lightbulb-selection-popup,
#lightbulb-capture-overlay,
.lightbulb-notification {
  all: initial !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif !important;
}