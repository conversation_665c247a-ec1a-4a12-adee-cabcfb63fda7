# Extension Icons

This directory should contain the following icon files for the Chrome extension:

## Required Icon Files

- `icon16.png` - 16x16 pixels (toolbar icon)
- `icon32.png` - 32x32 pixels (extension management page)
- `icon48.png` - 48x48 pixels (extension management page)
- `icon128.png` - 128x128 pixels (Chrome Web Store)

## Creating Icons from SVG

You can use the provided `icon.svg` file to generate the required PNG files:

### Using Online Tools
1. Open `icon.svg` in any SVG editor or online converter
2. Export/convert to PNG at the required sizes
3. Save as the appropriate filenames

### Using Command Line (if you have ImageMagick installed)
```bash
# Convert SVG to different PNG sizes
convert icon.svg -resize 16x16 icon16.png
convert icon.svg -resize 32x32 icon32.png
convert icon.svg -resize 48x48 icon48.png
convert icon.svg -resize 128x128 icon128.png
```

### Using Node.js (if you have sharp installed)
```javascript
const sharp = require('sharp');
const fs = require('fs');

const sizes = [16, 32, 48, 128];
const svgBuffer = fs.readFileSync('icon.svg');

sizes.forEach(size => {
  sharp(svgBuffer)
    .resize(size, size)
    .png()
    .toFile(`icon${size}.png`)
    .then(() => console.log(`Generated icon${size}.png`))
    .catch(err => console.error(err));
});
```

## Icon Design

The icon features:
- A lightbulb representing knowledge and ideas
- Blue color scheme matching the application theme (#007cba)
- Brain pattern overlay suggesting AI and intelligence
- Clean, modern design that works at all sizes

## Alternative

If you prefer, you can create your own icons or use the lightbulb emoji (💡) as a simple fallback by creating PNG files with the emoji rendered at the required sizes.