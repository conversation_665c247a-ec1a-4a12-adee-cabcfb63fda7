<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <!-- Background circle -->
  <circle cx="64" cy="64" r="60" fill="#007cba" stroke="#005a87" stroke-width="2"/>
  
  <!-- Lightbulb shape -->
  <g transform="translate(64, 64)">
    <!-- Bulb body -->
    <ellipse cx="0" cy="-8" rx="20" ry="28" fill="#fff" opacity="0.9"/>
    
    <!-- Filament -->
    <path d="M-12,-20 Q0,-30 12,-20 M-8,-15 Q0,-25 8,-15 M-4,-10 Q0,-20 4,-10" 
          stroke="#007cba" stroke-width="2" fill="none"/>
    
    <!-- Base -->
    <rect x="-16" y="12" width="32" height="8" rx="2" fill="#fff" opacity="0.8"/>
    <rect x="-14" y="20" width="28" height="6" rx="2" fill="#fff" opacity="0.7"/>
    <rect x="-12" y="26" width="24" height="4" rx="2" fill="#fff" opacity="0.6"/>
    
    <!-- Shine effect -->
    <ellipse cx="-8" cy="-15" rx="6" ry="12" fill="#fff" opacity="0.3"/>
  </g>
  
  <!-- Brain pattern overlay -->
  <g transform="translate(64, 64)" opacity="0.2">
    <path d="M-25,-10 Q-20,-25 -10,-20 Q0,-30 10,-20 Q20,-25 25,-10 Q20,5 10,0 Q0,10 -10,0 Q-20,5 -25,-10" 
          fill="none" stroke="#fff" stroke-width="1.5"/>
    <path d="M-15,-15 Q-10,-20 -5,-15 Q0,-20 5,-15 Q10,-20 15,-15" 
          fill="none" stroke="#fff" stroke-width="1"/>
  </g>
</svg>