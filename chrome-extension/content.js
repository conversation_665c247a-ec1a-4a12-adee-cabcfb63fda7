// Content script for webpage content capture and text selection

class ContentCapture {
  constructor() {
    this.isSelecting = false;
    this.selectedText = '';
    this.captureOverlay = null;
    this.setupEventListeners();
    this.setupSelectionCapture();
  }

  setupEventListeners() {
    // Listen for messages from popup and background script
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true;
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', (e) => {
      // Ctrl+Shift+S to save selection
      if (e.ctrlKey && e.shiftKey && e.key === 'S') {
        e.preventDefault();
        this.captureSelection();
      }
      
      // Ctrl+Shift+P to save page
      if (e.ctrlKey && e.shiftKey && e.key === 'P') {
        e.preventDefault();
        this.capturePage();
      }

      // Ctrl+Shift+C to capture screenshot
      if (e.ctrl<PERSON>ey && e.shiftKey && e.key === 'C') {
        e.preventDefault();
        this.captureScreenshot();
      }
    });
  }

  setupSelectionCapture() {
    let selectionTimeout;

    document.addEventListener('mouseup', () => {
      clearTimeout(selectionTimeout);
      selectionTimeout = setTimeout(() => {
        const selection = window.getSelection();
        if (selection.toString().trim().length > 0) {
          this.showSelectionPopup(selection);
        }
      }, 100);
    });

    document.addEventListener('mousedown', () => {
      this.hideSelectionPopup();
    });
  }

  async handleMessage(message, sender, sendResponse) {
    try {
      switch (message.type) {
        case 'capture-selection':
          await this.captureSelection();
          sendResponse({ success: true });
          break;

        case 'capture-page':
          await this.capturePage();
          sendResponse({ success: true });
          break;

        case 'capture-screenshot':
          await this.captureScreenshot();
          sendResponse({ success: true });
          break;

        case 'show-capture-overlay':
          this.showCaptureOverlay();
          sendResponse({ success: true });
          break;

        case 'get-page-content':
          const content = this.extractPageContent();
          sendResponse({ content });
          break;

        default:
          sendResponse({ error: 'Unknown message type' });
      }
    } catch (error) {
      console.error('Content script error:', error);
      sendResponse({ error: error.message });
    }
  }

  async captureSelection() {
    const selection = window.getSelection();
    const selectedText = selection.toString().trim();
    
    if (!selectedText) {
      this.showNotification('No text selected');
      return;
    }

    // Get context around selection
    const range = selection.getRangeAt(0);
    const context = this.getSelectionContext(range);

    const captureData = {
      text: selectedText,
      context: context,
      url: window.location.href,
      title: document.title,
      timestamp: Date.now()
    };

    // Send to background script
    chrome.runtime.sendMessage({
      type: 'capture-selection',
      data: captureData
    });

    this.showNotification('Selection saved to Second Brain!');
    this.hideSelectionPopup();
  }

  async capturePage() {
    const pageContent = this.extractPageContent();
    
    const captureData = {
      content: pageContent,
      url: window.location.href,
      title: document.title,
      timestamp: Date.now()
    };

    // Send to background script
    chrome.runtime.sendMessage({
      type: 'capture-page',
      data: captureData
    });

    this.showNotification('Page saved to Second Brain!');
  }

  async captureScreenshot() {
    // Send message to background script to capture screenshot
    chrome.runtime.sendMessage({
      type: 'capture-screenshot'
    }, (response) => {
      if (response.success) {
        this.showNotification('Screenshot saved to Second Brain!');
      } else {
        this.showNotification('Failed to capture screenshot');
      }
    });
  }

  extractPageContent() {
    const content = {
      text: '',
      html: '',
      images: [],
      links: [],
      metadata: {}
    };

    // Extract main text content, avoiding navigation and ads
    const mainContent = this.getMainContent();
    content.text = mainContent;

    // Get clean HTML
    content.html = document.documentElement.outerHTML;

    // Extract images with context
    const images = document.querySelectorAll('img');
    content.images = Array.from(images)
      .filter(img => img.src && !img.src.includes('data:image'))
      .map(img => ({
        src: img.src,
        alt: img.alt || '',
        title: img.title || '',
        width: img.naturalWidth,
        height: img.naturalHeight
      }));

    // Extract meaningful links
    const links = document.querySelectorAll('a[href]');
    content.links = Array.from(links)
      .filter(link => link.href && link.textContent?.trim())
      .map(link => ({
        href: link.href,
        text: link.textContent.trim(),
        title: link.title || ''
      }));

    // Extract metadata
    content.metadata = {
      title: document.title,
      description: this.getMetaContent('description'),
      keywords: this.getMetaContent('keywords'),
      author: this.getMetaContent('author'),
      publishedTime: this.getMetaContent('article:published_time'),
      modifiedTime: this.getMetaContent('article:modified_time'),
      siteName: this.getMetaContent('og:site_name'),
      type: this.getMetaContent('og:type')
    };

    return content;
  }

  getMainContent() {
    // Try to find main content area
    const mainSelectors = [
      'main',
      '[role="main"]',
      '.main-content',
      '.content',
      '.post-content',
      '.article-content',
      '.entry-content',
      '#content',
      '#main'
    ];

    let mainElement = null;
    for (const selector of mainSelectors) {
      mainElement = document.querySelector(selector);
      if (mainElement) break;
    }

    // Fallback to body if no main content found
    if (!mainElement) {
      mainElement = document.body;
    }

    // Extract text while avoiding navigation, ads, etc.
    const excludeSelectors = [
      'nav', 'header', 'footer', 'aside',
      '.navigation', '.nav', '.menu',
      '.advertisement', '.ads', '.ad',
      '.sidebar', '.widget',
      '.comments', '.comment-section',
      'script', 'style', 'noscript'
    ];

    const clone = mainElement.cloneNode(true);
    
    // Remove excluded elements
    excludeSelectors.forEach(selector => {
      const elements = clone.querySelectorAll(selector);
      elements.forEach(el => el.remove());
    });

    // Extract text content
    const textElements = clone.querySelectorAll('p, h1, h2, h3, h4, h5, h6, li, td, th, blockquote, pre');
    const textContent = Array.from(textElements)
      .map(el => el.textContent?.trim())
      .filter(text => text && text.length > 10)
      .join('\n\n');

    return textContent || clone.textContent?.trim() || '';
  }

  getMetaContent(name) {
    const meta = document.querySelector(`meta[name="${name}"], meta[property="${name}"]`);
    return meta ? meta.getAttribute('content') : '';
  }

  getSelectionContext(range) {
    try {
      // Get surrounding text for context
      const container = range.commonAncestorContainer;
      const containerText = container.textContent || '';
      const selectedText = range.toString();
      
      const startIndex = containerText.indexOf(selectedText);
      const contextStart = Math.max(0, startIndex - 100);
      const contextEnd = Math.min(containerText.length, startIndex + selectedText.length + 100);
      
      return containerText.substring(contextStart, contextEnd);
    } catch (error) {
      return '';
    }
  }

  showSelectionPopup(selection) {
    this.hideSelectionPopup();

    const selectedText = selection.toString().trim();
    if (selectedText.length < 3) return;

    const range = selection.getRangeAt(0);
    const rect = range.getBoundingClientRect();

    const popup = document.createElement('div');
    popup.id = 'lightbulb-selection-popup';
    popup.innerHTML = `
      <div class="lightbulb-popup-content">
        <button id="lightbulb-save-selection" title="Save to Second Brain">
          💡 Save
        </button>
        <button id="lightbulb-chat-selection" title="Chat with selection">
          💬 Chat
        </button>
      </div>
    `;

    // Position popup near selection
    popup.style.cssText = `
      position: fixed;
      top: ${rect.bottom + window.scrollY + 5}px;
      left: ${rect.left + window.scrollX}px;
      z-index: 10000;
      background: #fff;
      border: 1px solid #ddd;
      border-radius: 6px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      padding: 8px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
    `;

    document.body.appendChild(popup);

    // Add event listeners
    popup.querySelector('#lightbulb-save-selection').addEventListener('click', (e) => {
      e.preventDefault();
      this.captureSelection();
    });

    popup.querySelector('#lightbulb-chat-selection').addEventListener('click', (e) => {
      e.preventDefault();
      this.openChatWithSelection(selectedText);
    });

    this.selectionPopup = popup;
  }

  hideSelectionPopup() {
    if (this.selectionPopup) {
      this.selectionPopup.remove();
      this.selectionPopup = null;
    }
  }

  showCaptureOverlay() {
    if (this.captureOverlay) return;

    const overlay = document.createElement('div');
    overlay.id = 'lightbulb-capture-overlay';
    overlay.innerHTML = `
      <div class="lightbulb-overlay-content">
        <h3>Lightbulb Second Brain</h3>
        <div class="lightbulb-capture-options">
          <button id="lightbulb-capture-page">📄 Save Page</button>
          <button id="lightbulb-capture-screenshot">📸 Screenshot</button>
          <button id="lightbulb-capture-selection">✂️ Save Selection</button>
        </div>
        <button id="lightbulb-close-overlay">✕</button>
      </div>
    `;

    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.5);
      z-index: 10001;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;

    document.body.appendChild(overlay);

    // Add event listeners
    overlay.querySelector('#lightbulb-capture-page').addEventListener('click', () => {
      this.capturePage();
      this.hideCaptureOverlay();
    });

    overlay.querySelector('#lightbulb-capture-screenshot').addEventListener('click', () => {
      this.captureScreenshot();
      this.hideCaptureOverlay();
    });

    overlay.querySelector('#lightbulb-capture-selection').addEventListener('click', () => {
      this.captureSelection();
      this.hideCaptureOverlay();
    });

    overlay.querySelector('#lightbulb-close-overlay').addEventListener('click', () => {
      this.hideCaptureOverlay();
    });

    overlay.addEventListener('click', (e) => {
      if (e.target === overlay) {
        this.hideCaptureOverlay();
      }
    });

    this.captureOverlay = overlay;
  }

  hideCaptureOverlay() {
    if (this.captureOverlay) {
      this.captureOverlay.remove();
      this.captureOverlay = null;
    }
  }

  openChatWithSelection(selectedText) {
    // Open popup with chat interface
    chrome.runtime.sendMessage({
      type: 'open-chat',
      data: { selectedText }
    });
  }

  showNotification(message) {
    // Create temporary notification
    const notification = document.createElement('div');
    notification.textContent = message;
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #4CAF50;
      color: white;
      padding: 12px 20px;
      border-radius: 6px;
      z-index: 10002;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
      notification.remove();
    }, 3000);
  }
}

// Initialize content capture
const contentCapture = new ContentCapture();