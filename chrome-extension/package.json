{"name": "lightbulb-second-brain-extension", "version": "1.0.0", "description": "Chrome extension for Lightbulb Second Brain - capture and chat with any content", "main": "background.js", "scripts": {"build": "node build.js build", "package": "node build.js package", "clean": "node build.js clean", "dev": "node build.js build && echo 'Load unpacked extension from chrome-extension/ directory'", "test": "echo 'Open test-extension.html in Chrome to test functionality'", "icons": "echo 'See icons/README.md for icon generation instructions'"}, "keywords": ["chrome-extension", "second-brain", "ai", "knowledge-management", "content-capture", "chatgpt", "web-scraping"], "author": "Lightbulb Second Brain Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-repo/lightbulb-second-brain"}, "bugs": {"url": "https://github.com/your-repo/lightbulb-second-brain/issues"}, "homepage": "https://github.com/your-repo/lightbulb-second-brain#readme", "devDependencies": {"sharp": "^0.32.0"}, "optionalDependencies": {"imagemagick": "^0.1.3"}, "engines": {"node": ">=14.0.0"}, "chrome-extension": {"manifest_version": 3, "minimum_chrome_version": "88", "permissions": ["activeTab", "storage", "tabs", "scripting", "offscreen", "contextMenus"], "host_permissions": ["https://chat.openai.com/*", "https://chatgpt.com/*", "https://claude.ai/*", "https://bard.google.com/*", "<all_urls>"]}, "development": {"test_urls": ["https://chat.openai.com", "https://news.ycombinator.com", "https://stackoverflow.com", "https://github.com", "https://wikipedia.org"]}}