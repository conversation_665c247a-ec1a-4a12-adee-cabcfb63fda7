#!/bin/bash

echo "Installing professional UI libraries for Budget components..."

# Core UI Libraries
npm install @mui/material @emotion/react @emotion/styled @mui/icons-material @mui/x-data-grid @mui/x-date-pickers

# Data Table
npm install react-table @tanstack/react-table

# Date & Time
npm install react-flatpickr flatpickr

# Select Components
npm install react-select

# Tag Input
npm install react-tag-input-component

# Animations
npm install react-countup

# Charts
npm install react-chartjs-2 chart.js chartjs-plugin-datalabels chartjs-plugin-zoom

# Calendar
npm install @fullcalendar/react @fullcalendar/core @fullcalendar/daygrid @fullcalendar/timegrid @fullcalendar/interaction

# File Upload
npm install react-dropzone

# Kanban Board
npm install @lourenci/react-kanban

# Image Viewer
npm install react-images-viewer

# Rich Text Editor
npm install react-quill

# Alerts
npm install sweetalert2 sweetalert2-react-content

# 3D Graphics (optional - large)
# npm install three @react-three/fiber @react-three/drei

# Forms
npm install formik yup

# Visual Effects
npm install vanilla-tilt

# Maps (optional)
# npm install react-leaflet leaflet

# Circular Slider
npm install @fseehawer/react-circular-slider

# Color Manipulation
npm install chroma-js

# UUID
npm install uuid

# HTML Parser
npm install html-react-parser

# Types for TypeScript
npm install --save-dev @types/react-table @types/react-flatpickr @types/uuid @types/chroma-js

echo "Installation complete! All professional UI libraries have been installed."