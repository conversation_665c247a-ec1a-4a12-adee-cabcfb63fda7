#!/usr/bin/env node

const fs = require('fs').promises;
const path = require('path');

/**
 * Convert CC Agents from markdown to Claudia JSON format
 */
class CCAgentConverter {
  constructor() {
    this.ccAgentsPath = path.join(process.cwd(), 'cc_agents', 'agents');
    this.outputPath = path.join(process.cwd(), 'cc_agents', 'converted');
  }

  /**
   * Parse markdown agent file
   */
  parseMarkdownAgent(content) {
    const frontmatterRegex = /^---\n([\s\S]*?)\n---\n([\s\S]*)$/;
    const match = content.match(frontmatterRegex);
    
    if (!match) {
      throw new Error('Invalid markdown format');
    }
    
    const frontmatter = match[1];
    const systemPrompt = match[2].trim();
    
    // Parse frontmatter
    const metadata = {};
    const lines = frontmatter.split('\n');
    
    for (const line of lines) {
      const colonIndex = line.indexOf(':');
      if (colonIndex > -1) {
        const key = line.substring(0, colonIndex).trim();
        const value = line.substring(colonIndex + 1).trim();
        metadata[key] = value;
      }
    }
    
    return {
      name: metadata.name || '',
      description: metadata.description || '',
      model: metadata.model || 'sonnet',
      systemPrompt
    };
  }

  /**
   * Get emoji icon for agent
   */
  getEmojiForAgent(name) {
    const emojiMap = {
      'frontend-developer': '⚛️',
      'backend-architect': '🏗️',
      'ui-ux-designer': '🎨',
      'mobile-developer': '📱',
      'graphql-architect': '🔀',
      'python-pro': '🐍',
      'golang-pro': '🐹',
      'rust-pro': '🦀',
      'javascript-pro': '🟨',
      'typescript-pro': '🔷',
      'devops-troubleshooter': '🔧',
      'deployment-engineer': '🚀',
      'cloud-architect': '☁️',
      'database-optimizer': '💾',
      'security-auditor': '🔒',
      'test-automator': '✅',
      'performance-engineer': '⚡',
      'data-scientist': '📊',
      'ai-engineer': '🧠',
      'ml-engineer': '🤖',
      'business-analyst': '📈',
      'content-marketer': '📝',
      'sales-automator': '📧',
      'customer-support': '💬',
      'legal-advisor': '⚖️'
    };
    
    return emojiMap[name] || '🤖';
  }

  /**
   * Convert to Claudia JSON format
   */
  convertToClaudiaFormat(agent) {
    return {
      version: 1,
      exported_at: new Date().toISOString(),
      agent: {
        name: agent.name,
        icon: this.getEmojiForAgent(agent.name),
        system_prompt: agent.systemPrompt,
        default_task: agent.description,
        model: agent.model,
        hooks: null
      }
    };
  }

  /**
   * Convert all agents
   */
  async convertAllAgents() {
    try {
      // Create output directory
      await fs.mkdir(this.outputPath, { recursive: true });
      
      // Read all files in cc_agents directory
      const files = await fs.readdir(this.ccAgentsPath);
      
      let convertedCount = 0;
      let skippedCount = 0;
      const errors = [];
      
      for (const file of files) {
        if (file.endsWith('.md')) {
          try {
            const filePath = path.join(this.ccAgentsPath, file);
            const content = await fs.readFile(filePath, 'utf-8');
            
            // Parse markdown
            const agent = this.parseMarkdownAgent(content);
            
            // Convert to Claudia format
            const claudiaAgent = this.convertToClaudiaFormat(agent);
            
            // Write JSON file
            const outputFile = path.join(
              this.outputPath, 
              `${agent.name}.claudia.json`
            );
            await fs.writeFile(
              outputFile, 
              JSON.stringify(claudiaAgent, null, 2)
            );
            
            console.log(`✅ Converted: ${agent.name}`);
            convertedCount++;
          } catch (error) {
            errors.push({ file, error: error.message });
            console.error(`❌ Failed to convert ${file}: ${error.message}`);
          }
        } else if (file.endsWith('.claudia.json')) {
          console.log(`⏭️  Skipped: ${file} (already in Claudia format)`);
          skippedCount++;
        }
      }
      
      // Summary
      console.log('\n📊 Conversion Summary:');
      console.log(`   Converted: ${convertedCount} agents`);
      console.log(`   Skipped: ${skippedCount} agents`);
      console.log(`   Errors: ${errors.length}`);
      
      if (errors.length > 0) {
        console.log('\n❌ Errors:');
        errors.forEach(({ file, error }) => {
          console.log(`   ${file}: ${error}`);
        });
      }
      
      console.log(`\n✨ Converted agents saved to: ${this.outputPath}`);
      
    } catch (error) {
      console.error('Fatal error:', error);
      process.exit(1);
    }
  }

  /**
   * Convert a single agent
   */
  async convertSingleAgent(agentFile) {
    try {
      const filePath = path.join(this.ccAgentsPath, agentFile);
      const content = await fs.readFile(filePath, 'utf-8');
      
      // Parse markdown
      const agent = this.parseMarkdownAgent(content);
      
      // Convert to Claudia format
      const claudiaAgent = this.convertToClaudiaFormat(agent);
      
      // Create output directory
      await fs.mkdir(this.outputPath, { recursive: true });
      
      // Write JSON file
      const outputFile = path.join(
        this.outputPath, 
        `${agent.name}.claudia.json`
      );
      await fs.writeFile(
        outputFile, 
        JSON.stringify(claudiaAgent, null, 2)
      );
      
      console.log(`✅ Converted: ${agent.name}`);
      console.log(`📁 Saved to: ${outputFile}`);
      
    } catch (error) {
      console.error(`❌ Failed to convert ${agentFile}: ${error.message}`);
      process.exit(1);
    }
  }
}

// CLI interface
async function main() {
  const converter = new CCAgentConverter();
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    // Convert all agents
    console.log('🔄 Converting all CC agents to Claudia format...\n');
    await converter.convertAllAgents();
  } else if (args[0] === '--help' || args[0] === '-h') {
    console.log(`
CC Agent Converter - Convert markdown agents to Claudia JSON format

Usage:
  node convert-cc-agents.js              Convert all agents
  node convert-cc-agents.js <agent.md>   Convert specific agent
  node convert-cc-agents.js --help       Show this help

Examples:
  node convert-cc-agents.js
  node convert-cc-agents.js frontend-developer.md
  node convert-cc-agents.js backend-architect.md
    `);
  } else {
    // Convert specific agent
    console.log(`🔄 Converting ${args[0]} to Claudia format...\n`);
    await converter.convertSingleAgent(args[0]);
  }
}

// Run the converter
main().catch(error => {
  console.error('Fatal error:', error);
  process.exit(1);
});