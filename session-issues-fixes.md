# Session Component Issues & Fixes

## Critical Issues to Address

### 1. Checkpoint State Initialization
**Problem**: Checkpoint state not managed in Tauri backend
**Solution**: Initialize checkpoint state on app startup

```rust
// In src-tauri/src/main.rs
app.manage(CheckpointState::default());
```

### 2. Session ID Stability
**Problem**: Session ID changes during execution causing listener confusion
**Current Flow**:
```
1. Start with temp session → "temp-session-1756145715954"
2. Execute command → Gets real session ID "dc5eb732..."  
3. Continue command → Gets NEW session ID "bcedbd5e..."
```

**Fix**: Prevent session ID changes after initial assignment
```typescript
// In ClaudeCodeSession.tsx
const handleStreamMessage = useCallback((payload: string) => {
  const message = JSON.parse(payload);
  
  // Only extract session ID if we don't have one
  if (!claudeSessionId && message.session_id) {
    setClaudeSessionId(message.session_id);
    // Re-attach listeners with new session ID
    updateListeners(message.session_id);
  }
  
  // Process message...
}, [claudeSessionId]);
```

### 3. Optimize State Updates
**Problem**: State updates on every message causing excessive re-renders
**Solution**: Batch message updates

```typescript
// Use a reducer for message state
const messageReducer = (state, action) => {
  switch (action.type) {
    case 'ADD_MESSAGE':
      return [...state, action.payload];
    case 'ADD_MESSAGES_BATCH':
      return [...state, ...action.payload];
    case 'CLEAR':
      return [];
    default:
      return state;
  }
};

// Batch updates with debouncing
const batchedMessages = useRef([]);
const flushMessages = useMemo(
  () => debounce(() => {
    if (batchedMessages.current.length > 0) {
      dispatch({ 
        type: 'ADD_MESSAGES_BATCH', 
        payload: batchedMessages.current 
      });
      batchedMessages.current = [];
    }
  }, 100),
  []
);

const handleStreamMessage = useCallback((payload: string) => {
  const message = JSON.parse(payload);
  batchedMessages.current.push(message);
  flushMessages();
}, [flushMessages]);
```

### 4. Fix Component Stability
**Problem**: Component unmounting/remounting frequently
**Solution**: Memoize component and stabilize props

```typescript
// Wrap component with memo
export const ClaudeCodeSession = React.memo(({ 
  session, 
  initialProjectPath,
  onHideRequest 
}: Props) => {
  // Component implementation
}, (prevProps, nextProps) => {
  // Custom comparison to prevent unnecessary re-renders
  return (
    prevProps.session?.id === nextProps.session?.id &&
    prevProps.initialProjectPath === nextProps.initialProjectPath
  );
});
```

### 5. Improve Event Listener Management
**Problem**: Duplicate listeners and memory leaks
**Solution**: Use single listener pattern

```typescript
// Single listener manager
class SessionEventManager {
  private listeners = new Map<string, UnlistenFn>();
  
  async subscribe(sessionId: string, handlers: EventHandlers) {
    // Unsubscribe existing
    this.unsubscribe(sessionId);
    
    // Subscribe once
    const unlisten = await listen(`claude-output:${sessionId}`, handlers.onOutput);
    this.listeners.set(sessionId, unlisten);
  }
  
  unsubscribe(sessionId: string) {
    const unlisten = this.listeners.get(sessionId);
    if (unlisten) {
      unlisten();
      this.listeners.delete(sessionId);
    }
  }
  
  cleanup() {
    this.listeners.forEach(unlisten => unlisten());
    this.listeners.clear();
  }
}
```

## Implementation Priority

1. **High Priority** (Immediate)
   - Fix checkpoint state initialization
   - Stabilize session ID handling
   - Batch message updates

2. **Medium Priority** (This week)
   - Optimize re-renders with React.memo
   - Consolidate event listeners
   - Add error boundaries

3. **Low Priority** (Future)
   - Implement virtualized message list
   - Add performance monitoring
   - Create e2e tests

## Performance Metrics to Track

```typescript
// Add performance tracking
const metrics = {
  renderCount: 0,
  messageCount: 0,
  listenerCount: 0,
  stateUpdateCount: 0,
  
  log() {
    console.log('[Performance]', {
      renders: this.renderCount,
      messages: this.messageCount,
      listeners: this.listenerCount,
      updates: this.stateUpdateCount,
      ratio: this.stateUpdateCount / this.messageCount
    });
  }
};

// Track in component
useEffect(() => {
  metrics.renderCount++;
});
```

## Testing Checklist

- [ ] Session ID remains stable throughout session
- [ ] No checkpoint state errors in console
- [ ] State updates batched (not 1:1 with messages)
- [ ] Component doesn't unmount during normal use
- [ ] Memory usage stable over long sessions
- [ ] No duplicate event listeners
- [ ] Performance metrics show improvement

## Expected Improvements

| Metric | Current | Target | Improvement |
|--------|---------|--------|-------------|
| Re-renders per 30 messages | 30+ | 3-5 | 85% reduction |
| State updates per message | 1 | 0.1 | 90% reduction |
| Event listeners | 6+ | 3 | 50% reduction |
| Memory growth | Linear | Stable | 100% improvement |

## Code Changes Required

### Frontend (TypeScript/React)
1. `ClaudeCodeSession.tsx` - Implement batching and memoization
2. `useCheckpoints.ts` - Add error handling for state management
3. `useClaudeMessages.ts` - Optimize message processing
4. Event listener hooks - Consolidate and deduplicate

### Backend (Rust/Tauri)
1. `main.rs` - Initialize checkpoint state
2. `checkpoint/state.rs` - Add `.manage()` call
3. `commands/claude.rs` - Ensure session ID stability

## Monitoring Solution

```typescript
// Add debug mode for development
const DEBUG_SESSION = process.env.NODE_ENV === 'development';

if (DEBUG_SESSION) {
  console.group('[Session Debug]');
  console.log('Session ID:', claudeSessionId);
  console.log('Message Count:', messages.length);
  console.log('Listeners Active:', activeListeners);
  console.log('Last State Update:', Date.now());
  console.groupEnd();
}
```