# Session Architecture Analysis - Frontend & Backend

## Executive Summary
The Sanity application uses a complex session management system built on <PERSON><PERSON> (Rust backend) and React (TypeScript frontend). Sessions represent individual Claude Code interactions with persistent state, checkpointing, and real-time streaming capabilities.

## 1. Frontend Architecture

### 1.1 Component Structure
```
src/components/sessions/
├── ClaudeCodeSession.tsx          # Main session component (1000+ lines)
├── SessionList.tsx                # Session listing & management
├── SessionOutputViewer.tsx        # Output display component
├── RunningClaudeSessions.tsx     # Active sessions manager
├── claude-code-session/          # Modular session components
│   ├── PromptQueue.tsx           # Queue management for prompts
│   ├── useClaudeMessages.ts      # Message handling hook
│   ├── ErrorRecoverySystem.tsx   # Error recovery UI
│   ├── useCheckpoints.ts         # Checkpoint management hook
│   ├── SessionMetricsVisualizer.tsx # Metrics visualization
│   ├── ExtendedThinkingManager.tsx  # Extended thinking mode
│   ├── SessionHeader.tsx         # Session header UI
│   ├── PreviewManager.tsx        # Preview functionality
│   ├── PlanModeManager.tsx       # Plan mode handling
│   ├── MessageList.tsx           # Message display list
│   └── SessionMetricsTracker.tsx # Metrics tracking
└── superclaude/                  # SuperClaude AI features
    ├── ui/                       # UI components
    ├── core/                     # Core logic
    └── hooks/                    # Custom hooks
```

### 1.2 Key Frontend Components

#### ClaudeCodeSession.tsx
- **Purpose**: Main session orchestrator component
- **State Management**: 20+ useState hooks for managing:
  - Project path and session ID
  - Messages array (ClaudeStreamMessage[])
  - Loading states and errors
  - UI toggles (timeline, settings, fork dialog)
  - Queue management for prompts
  - Token tracking and metrics
- **Event Listeners**: Sets up Tauri event listeners for:
  - `claude-output:${sessionId}` - Stream output
  - `claude-error:${sessionId}` - Error messages
  - `claude-complete:${sessionId}` - Completion signals
- **Key Functions**:
  - `handleSubmit()` - Process user input
  - `handleStreamMessage()` - Process streaming responses
  - `detectQuestion()` - Identify questions in responses

#### SessionList.tsx
- Lists all available sessions
- Fetches session metadata and personas
- Provides session navigation

#### SessionOutputViewer.tsx
- Renders Claude's streaming output
- Handles markdown rendering
- Code block syntax highlighting

### 1.3 State Management

#### Local Component State
- Heavy use of useState for component-level state
- useRef for persistent references across renders
- Custom hooks for specific functionality

#### Shared State
- Zustand store (`sessionStore.ts`) for global session state
- Context providers for theme and settings

#### Event-Driven Updates
- Tauri event system for real-time updates
- Custom event emitters for component communication

## 2. Backend Architecture (Rust/Tauri)

### 2.1 Core Structures

```rust
// Session representation
pub struct Session {
    id: String,                    // UUID
    project_id: String,            // Project identifier
    project_path: String,          // File system path
    todo_data: Option<Value>,      // Associated todos
    created_at: u64,              // Unix timestamp
    first_message: Option<String>, // Initial prompt
    message_timestamp: Option<String>
}

// Process management
pub struct ClaudeProcessState {
    current_process: Arc<Mutex<Option<Child>>>
}
```

### 2.2 Key Tauri Commands

#### Session Lifecycle
- `execute_claude_code` - Start new Claude session
- `continue_claude_code` - Continue existing session
- `resume_claude_code` - Resume from checkpoint
- `cancel_claude_execution` - Stop running session

#### Session Management
- `list_projects` - Get all projects
- `get_project_sessions` - Get sessions for project
- `list_running_claude_sessions` - Active sessions
- `get_claude_session_output` - Retrieve output

#### Checkpoint System
- `create_checkpoint` - Save session state
- `restore_checkpoint` - Load previous state
- `list_checkpoints` - Available checkpoints
- `fork_from_checkpoint` - Branch from checkpoint

#### Background Processing
- `send_to_background` - Move session to background
- `bring_to_foreground` - Restore session focus

### 2.3 Data Storage

#### File System
- Sessions stored in `~/.claude/projects/`
- JSONL format for session history
- Settings in `~/.claude/settings.json`

#### SQLite Database
- Checkpoint metadata
- Session analytics
- User preferences

## 3. Frontend-Backend Communication

### 3.1 IPC Flow

```typescript
// Frontend invocation
const result = await invoke("execute_claude_code", {
    projectPath: "/path/to/project",
    prompt: "User input",
    model: "claude-3-opus"
});

// Backend processing
#[tauri::command]
pub async fn execute_claude_code(
    app: AppHandle,
    project_path: String,
    prompt: String,
    model: Option<String>
) -> Result<String, String> {
    // Process and emit events
    app.emit("claude-output:session-id", output)?;
}

// Frontend listener
await listen<string>(`claude-output:${sessionId}`, (event) => {
    handleStreamMessage(event.payload);
});
```

### 3.2 Event System
- **Emission**: Backend emits typed events
- **Subscription**: Frontend subscribes to specific channels
- **Cleanup**: Unlisten functions prevent memory leaks

### 3.3 Data Flow

1. **User Input** → Frontend component
2. **Tauri Command** → Backend processing
3. **Claude CLI** → External process spawned
4. **Stream Output** → Backend captures stdout
5. **Event Emission** → Real-time updates
6. **Frontend Update** → UI re-renders

## 4. Key Features Implementation

### 4.1 Streaming Response
- Backend spawns Claude process with stdout piped
- BufReader processes line-by-line output
- Each line emitted as separate event
- Frontend accumulates and parses JSONL

### 4.2 Checkpoint System
- Automatic checkpoints on significant events
- Manual checkpoint creation
- Fork capability for branching conversations
- Diff generation between checkpoints

### 4.3 Error Recovery
- Frontend ErrorRecoverySystem component
- Retry logic with exponential backoff
- Session state preservation on failure
- Graceful degradation

### 4.4 SuperClaude AI
- Personas for specialized behaviors
- Command history tracking
- Intelligent suggestions
- Analytics and insights

## 5. Performance Considerations

### 5.1 Frontend Optimizations
- React.memo for expensive components
- useCallback/useMemo for function/value memoization
- Virtualized lists for long message histories
- Debounced input handling

### 5.2 Backend Optimizations
- Async/await for non-blocking I/O
- Arc<Mutex> for thread-safe state sharing
- Connection pooling for database
- Efficient file streaming

## 6. Identified Issues

### 6.1 Code Duplication
- `detectQuestion()` function repeated
- Event listener setup patterns duplicated
- Similar state management across components
- API call patterns with identical error handling

### 6.2 Complexity
- ClaudeCodeSession.tsx exceeds 1000 lines
- 20+ state variables in single component
- Complex event listener management
- Tight coupling between components

### 6.3 Potential Improvements
1. **Extract Utilities**: Move detectQuestion to utils
2. **Custom Hooks**: Create useClaudeEventListeners hook
3. **State Reduction**: Use useReducer for complex state
4. **Component Split**: Break down ClaudeCodeSession
5. **Service Layer**: Abstract API calls
6. **Type Safety**: Stronger typing for events

## 7. Security Considerations

### 7.1 Frontend
- Input sanitization for prompts
- XSS prevention in markdown rendering
- Secure storage of session data

### 7.2 Backend
- Path traversal prevention
- Command injection protection
- Process isolation for Claude execution
- Capability-based permissions

## 8. Testing Strategy

### 8.1 Unit Tests
- Component testing with Vitest
- Hook testing with @testing-library/react-hooks
- Rust unit tests with cargo test

### 8.2 Integration Tests
- IPC command testing
- Event system verification
- Database operations

### 8.3 E2E Tests
- Full session lifecycle
- Error recovery scenarios
- Checkpoint/restore flows

## Conclusion

The session architecture is sophisticated but shows signs of organic growth leading to complexity and duplication. Key strengths include real-time streaming, robust checkpoint system, and comprehensive error handling. Main areas for improvement are code organization, reducing duplication, and improving maintainability through better separation of concerns.

## Recommendations

1. **Immediate**: Extract shared utilities and create custom hooks
2. **Short-term**: Refactor ClaudeCodeSession into smaller components
3. **Medium-term**: Implement proper service layer for API calls
4. **Long-term**: Consider state management library (Redux/MobX) for complex state