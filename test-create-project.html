<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Create Project From Template</title>
    <style>
        body {
            font-family: system-ui, -apple-system, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #1a1a1a;
            color: #e0e0e0;
        }
        .container {
            background: #2a2a2a;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.3);
        }
        h1 {
            color: #3b82f6;
            margin-bottom: 30px;
        }
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px 10px 10px 0;
            transition: background 0.2s;
        }
        button:hover {
            background: #2563eb;
        }
        button:disabled {
            background: #4a5568;
            cursor: not-allowed;
        }
        .output {
            background: #1a1a1a;
            border: 1px solid #3a3a3a;
            border-radius: 6px;
            padding: 20px;
            margin-top: 20px;
            white-space: pre-wrap;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            color: #10b981;
            font-weight: bold;
        }
        .error {
            color: #ef4444;
            font-weight: bold;
        }
        .info {
            color: #3b82f6;
            margin-bottom: 10px;
        }
        input, select {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            background: #1a1a1a;
            border: 1px solid #3a3a3a;
            color: #e0e0e0;
            border-radius: 4px;
            font-size: 14px;
        }
        label {
            display: block;
            margin-top: 15px;
            color: #9ca3af;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Test Create Project From Template</h1>
        
        <div>
            <label for="templateId">Template ID:</label>
            <select id="templateId">
                <option value="template_research">Research Project</option>
                <option value="template_creative">Creative Writing</option>
                <option value="template_business">Business Plan</option>
                <option value="template_personal">Personal Notes</option>
            </select>
            
            <label for="projectName">Project Name:</label>
            <input type="text" id="projectName" placeholder="My New Project" value="Test Project from Template">
            
            <label for="projectDesc">Project Description (optional):</label>
            <input type="text" id="projectDesc" placeholder="Project description..." value="Testing the create_project_from_template command">
        </div>

        <button onclick="testCreateFromTemplate()">Create Project from Template</button>
        <button onclick="listProjects()">List All Projects</button>
        <button onclick="listTemplates()">List Available Templates</button>
        <button onclick="clearOutput()">Clear Output</button>
        
        <div id="output" class="output"></div>
    </div>

    <script type="module">
        // Import Tauri API
        const { invoke } = window.__TAURI__.tauri;
        
        function log(message, type = 'info') {
            const output = document.getElementById('output');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            output.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            output.scrollTop = output.scrollHeight;
        }
        
        window.testCreateFromTemplate = async function() {
            const templateId = document.getElementById('templateId').value;
            const projectName = document.getElementById('projectName').value;
            const projectDesc = document.getElementById('projectDesc').value;
            
            if (!projectName) {
                log('Error: Project name is required', 'error');
                return;
            }
            
            log(`Creating project from template...`, 'info');
            log(`Template ID: ${templateId}`, 'info');
            log(`Project Name: ${projectName}`, 'info');
            log(`Description: ${projectDesc || '(none)'}`, 'info');
            
            try {
                const result = await invoke('create_project_from_template', {
                    template_id: templateId,
                    name: projectName,
                    description: projectDesc || null
                });
                
                log('✅ Project created successfully!', 'success');
                log(JSON.stringify(result, null, 2), 'success');
            } catch (error) {
                log(`❌ Error: ${error}`, 'error');
            }
        };
        
        window.listProjects = async function() {
            log('Fetching all projects...', 'info');
            
            try {
                const projects = await invoke('get_all_projects');
                log(`Found ${projects.length} project(s):`, 'success');
                log(JSON.stringify(projects, null, 2), 'info');
            } catch (error) {
                log(`❌ Error: ${error}`, 'error');
            }
        };
        
        window.listTemplates = async function() {
            log('Fetching available templates...', 'info');
            
            try {
                const templates = await invoke('get_project_templates');
                log(`Found ${templates.length} template(s):`, 'success');
                log(JSON.stringify(templates, null, 2), 'info');
            } catch (error) {
                log(`❌ Error: ${error}`, 'error');
            }
        };
        
        window.clearOutput = function() {
            document.getElementById('output').innerHTML = '';
        };
        
        // Auto-load templates on page load
        window.addEventListener('load', () => {
            log('Test page loaded. Ready to test create_project_from_template command.', 'info');
            listTemplates();
        });
    </script>
</body>
</html>