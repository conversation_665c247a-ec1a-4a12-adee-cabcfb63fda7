# Mermaid-Sources Integration

## Overview

The Mermaid-Sources Integration feature bridges the existing Mermaid diagram system with the sources management system in lighthouse-lm. This integration enables automatic diagram generation from source content, visual representation of source relationships, and interactive exploration of knowledge through diagrams.

## Features

- **Source-Based Diagram Generation**: Generate Mermaid diagrams directly from your source content
- **Interactive Diagrams**: Click on diagram elements to view corresponding source content
- **Source Relationship Visualization**: See how your sources relate to each other
- **Workflow Diagram Creation**: Build process flows from procedural content
- **Diagram Persistence**: Save and manage diagrams with source attribution
- **Enhanced Export**: Export diagrams with proper source references

## Quick Start

### Generating a Diagram from Sources

1. Select one or more sources in the SourcesSidebar
2. Click the "Generate Diagram" button
3. Choose your diagram type (flowchart, mindmap, or relationship)
4. Configure generation options
5. Click "Generate" to create your diagram

### Viewing Interactive Diagrams

1. Open a saved diagram or generate a new one
2. Click on any diagram node to view its source content
3. Use the source panel to navigate between related materials
4. Export the diagram with source attribution when needed

## Components

### Core Components

- **SourceDiagramGenerator**: Main dialog for generating diagrams from sources
- **MermaidSourceAnalyzer**: Analyzes source content to extract concepts and relationships
- **InteractiveDiagramViewer**: Enhanced Mermaid component with source interaction
- **SourceRelationshipDiagram**: Visualizes relationships between sources

### Enhanced Components

- **SourcesSidebar**: Now includes diagram generation capabilities
- **Mermaid Component**: Enhanced with source attribution and interactivity
- **SourceManagerDialog**: Includes bulk diagram generation options

## Documentation Structure

- [Component Documentation](./docs/components.md) - Detailed component API documentation
- [Usage Examples](./docs/examples.md) - Sample implementations and use cases
- [Troubleshooting Guide](./docs/troubleshooting.md) - Common issues and solutions
- [Developer Guide](./docs/developer-guide.md) - Extending and customizing the system
- [API Reference](./docs/api-reference.md) - Complete API documentation
- [Sample Diagrams](./docs/sample-diagrams.md) - Showcase of generated diagram types

## Requirements Coverage

This implementation addresses all requirements:

1. **Source-to-Diagram Generation** (Req 1): Generate diagrams from source content with concept extraction
2. **Source Relationship Visualization** (Req 2): Visual representation of source connections
3. **Workflow Diagram Creation** (Req 3): Process flows from procedural content
4. **Diagram Management** (Req 4): Save, manage, and regenerate diagrams
5. **Export with Attribution** (Req 5): Export diagrams with source references
6. **Interactive Exploration** (Req 6): Clickable diagrams with source navigation

## Getting Started

See the [Usage Examples](./docs/examples.md) for detailed implementation examples and the [Developer Guide](./docs/developer-guide.md) for extending the system.