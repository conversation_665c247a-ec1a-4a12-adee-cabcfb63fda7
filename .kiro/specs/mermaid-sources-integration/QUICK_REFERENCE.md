# Quick Reference

## Basic Usage

### Generate Diagram from Sources

```tsx
import { SourceDiagramGenerator } from '@/components/mermaid/SourceDiagramGenerator';

<SourceDiagramGenerator
  sources={selectedSources}
  onDiagramGenerated={(diagram) => console.log('Generated:', diagram)}
  onError={(error) => console.error('Error:', error)}
  defaultDiagramType="flowchart"
  isOpen={isOpen}
  onOpenChange={setIsOpen}
/>
```

### Interactive Diagram Viewer

```tsx
import { InteractiveDiagramViewer } from '@/components/mermaid/InteractiveDiagramViewer';

<InteractiveDiagramViewer
  code={diagram.code}
  sourceMap={diagram.interactive_metadata.node_source_map}
  onNodeClick={(nodeId, sourceIds) => showSourceContent(sourceIds)}
  showSourceAttribution={true}
/>
```

### Programmatic Analysis

```tsx
import { MermaidSourceAnalyzer } from '@/services/mermaidSourceAnalyzer';

const analyzer = new MermaidSourceAnalyzer();
const analysis = await analyzer.analyzeSources(sources);
const diagramCode = analyzer.generateDiagramCode(analysis, 'flowchart');
```

## Hooks

### Source Analysis Hook

```tsx
const { generateDiagram, isAnalyzing, error } = useMermaidSourceAnalysis();

const handleGenerate = async () => {
  const diagram = await generateDiagram(sources, 'mindmap');
};
```

### Diagram Management Hook

```tsx
const { diagrams, saveDiagram, regenerateDiagram } = useSourceDiagrams();

const handleSave = async () => {
  const saved = await saveDiagram(diagram, sources);
};
```

## Common Patterns

### Error Handling

```tsx
try {
  const diagram = await generateDiagram(sources, 'flowchart');
} catch (error) {
  if (error instanceof AnalysisError) {
    // Handle analysis-specific errors
  } else if (error instanceof DiagramGenerationError) {
    // Handle generation-specific errors
  }
}
```

### Performance Optimization

```tsx
// For large source sets
const chunkSources = (sources, chunkSize = 10) => {
  const chunks = [];
  for (let i = 0; i < sources.length; i += chunkSize) {
    chunks.push(sources.slice(i, i + chunkSize));
  }
  return chunks;
};

// Process in chunks
const chunks = chunkSources(sources);
for (const chunk of chunks) {
  await analyzer.analyzeSources(chunk);
}
```

### Custom Configuration

```tsx
const options: DiagramGenerationOptions = {
  diagram_type: 'flowchart',
  max_concepts: 30,
  relationship_depth: 2,
  minimum_confidence: 0.6,
  filter_by_relevance: true
};

const diagram = await generateDiagram(sources, 'flowchart', options);
```

## Diagram Types

| Type | Best For | Example Use Case |
|------|----------|------------------|
| `flowchart` | Processes, workflows | API request flow |
| `mindmap` | Hierarchical content | Project structure |
| `relationship` | Source connections | Document relationships |
| `workflow` | Sequential processes | Development workflow |

## Configuration Options

### Analysis Config

```typescript
{
  defaultMinConfidence: 0.5,
  maxConceptsPerSource: 50,
  enableMLAnalysis: false,
  timeout: 30000,
  enableCaching: true
}
```

### Generation Options

```typescript
{
  max_concepts: 30,
  relationship_depth: 2,
  filter_by_relevance: true,
  minimum_confidence: 0.5,
  layout_direction: 'TD',
  color_scheme: 'default'
}
```

## Troubleshooting

### Common Issues

| Issue | Solution |
|-------|----------|
| No concepts extracted | Check source content length and quality |
| Diagram too complex | Reduce `max_concepts` or increase `minimum_confidence` |
| Invalid Mermaid syntax | Enable syntax validation and correction |
| Source content not loading | Verify source IDs and permissions |
| Slow generation | Use chunked processing for large source sets |

### Debug Mode

```typescript
// Enable debug logging
localStorage.setItem('mermaid-sources-debug', 'true');

// Check diagram integrity
const validation = validateDiagramIntegrity(diagram, sources);
if (!validation.isValid) {
  console.log('Issues:', validation.issues);
}
```

## Extension Points

### Custom Diagram Generator

```typescript
class CustomGenerator extends DiagramGenerator {
  generateDiagram(analysis: SourceAnalysisResult): string {
    // Custom generation logic
    return 'graph TD\n...';
  }
}

analyzer.registerGenerator('custom', new CustomGenerator());
```

### Custom Content Extractor

```typescript
class CustomExtractor extends ContentExtractor {
  canHandle(source: Source): boolean {
    return source.type === 'custom';
  }
  
  async extractContent(source: Source): Promise<ExtractedContent> {
    // Custom extraction logic
  }
}

analyzer.registerExtractor(new CustomExtractor());
```

## Keyboard Shortcuts

| Shortcut | Action |
|----------|--------|
| `Ctrl+G` | Generate diagram from selected sources |
| `Ctrl+R` | Regenerate current diagram |
| `Ctrl+E` | Export diagram with attribution |
| `Ctrl+S` | Save diagram |
| `Escape` | Close diagram generator |

## Best Practices

1. **Source Quality**: Ensure sources have meaningful content (>100 characters)
2. **Batch Processing**: Process large source sets in chunks
3. **Error Handling**: Always handle analysis and generation errors
4. **Caching**: Enable caching for repeated operations
5. **Validation**: Validate generated diagrams before display
6. **Attribution**: Always include source attribution in exports
7. **Performance**: Monitor memory usage with large datasets
8. **Testing**: Test with various source types and sizes

## Limits

- Max sources per analysis: 100
- Max concepts per diagram: 100
- Max source content length: 1MB
- Analysis timeout: 5 minutes
- Max concurrent analyses: 5