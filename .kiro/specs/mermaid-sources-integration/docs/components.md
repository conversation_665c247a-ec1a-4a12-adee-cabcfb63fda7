# Component Documentation

## Core Components

### SourceDiagramGenerator

Main dialog component for generating diagrams from sources.

#### Props

```typescript
interface SourceDiagramGeneratorProps {
  sources: Source[];
  onDiagramGenerated: (diagram: SourceDiagram) => void;
  onError: (error: string) => void;
  defaultDiagramType?: DiagramType;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}
```

#### Usage

```tsx
import { SourceDiagramGenerator } from '@/components/mermaid/SourceDiagramGenerator';

function MyComponent() {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedSources, setSelectedSources] = useState<Source[]>([]);

  const handleDiagramGenerated = (diagram: SourceDiagram) => {
    console.log('Generated diagram:', diagram);
    // Handle the generated diagram
  };

  const handleError = (error: string) => {
    console.error('Diagram generation error:', error);
    // Handle the error
  };

  return (
    <SourceDiagramGenerator
      sources={selectedSources}
      onDiagramGenerated={handleDiagramGenerated}
      onError={handleError}
      defaultDiagramType="flowchart"
      isOpen={isOpen}
      onOpenChange={setIsOpen}
    />
  );
}
```

#### Features

- Source selection interface
- Diagram type selection (flowchart, mindmap, relationship)
- Generation options configuration
- Real-time preview
- Error handling and validation

---

### MermaidSourceAnalyzer

Service class for analyzing source content and extracting concepts and relationships.

#### Interface

```typescript
class MermaidSourceAnalyzer {
  analyzeSource(source: Source): Promise<SourceAnalysisResult>;
  analyzeSources(sources: Source[]): Promise<SourceAnalysisResult>;
  extractConcepts(content: string): Concept[];
  findRelationships(concepts: Concept[], content: string): Relationship[];
  generateDiagramCode(analysis: SourceAnalysisResult, type: DiagramType): string;
}

interface SourceAnalysisResult {
  concepts: Concept[];
  relationships: Relationship[];
  hierarchy: HierarchyNode[];
  sourceMap: Map<string, string[]>;
}
```

#### Usage

```typescript
import { MermaidSourceAnalyzer } from '@/services/mermaidSourceAnalyzer';

const analyzer = new MermaidSourceAnalyzer();

// Analyze a single source
const result = await analyzer.analyzeSource(source);

// Generate diagram code
const diagramCode = analyzer.generateDiagramCode(result, 'flowchart');
```

#### Methods

- **analyzeSource**: Analyzes a single source for concepts and relationships
- **analyzeSources**: Analyzes multiple sources and finds cross-source relationships
- **extractConcepts**: Extracts key concepts from text content
- **findRelationships**: Identifies relationships between concepts
- **generateDiagramCode**: Creates Mermaid diagram code from analysis results

---

### InteractiveDiagramViewer

Enhanced Mermaid component with source interaction capabilities.

#### Props

```typescript
interface InteractiveDiagramViewerProps extends MermaidProps {
  sourceMap: Map<string, string[]>;
  onNodeClick: (nodeId: string, sourceIds: string[]) => void;
  onSourceView: (sourceId: string) => void;
  highlightedNodes?: string[];
  showSourceAttribution?: boolean;
}
```

#### Usage

```tsx
import { InteractiveDiagramViewer } from '@/components/mermaid/InteractiveDiagramViewer';

function DiagramView({ diagram }: { diagram: SourceDiagram }) {
  const handleNodeClick = (nodeId: string, sourceIds: string[]) => {
    // Handle node click - show source content
    console.log('Node clicked:', nodeId, 'Sources:', sourceIds);
  };

  const handleSourceView = (sourceId: string) => {
    // Navigate to source
    console.log('View source:', sourceId);
  };

  return (
    <InteractiveDiagramViewer
      code={diagram.code}
      sourceMap={diagram.interactive_metadata.node_source_map}
      onNodeClick={handleNodeClick}
      onSourceView={handleSourceView}
      showSourceAttribution={true}
    />
  );
}
```

#### Features

- Clickable diagram nodes
- Source attribution display
- Node highlighting
- Context menus for diagram elements
- Source navigation integration

---

### SourceRelationshipDiagram

Specialized component for visualizing relationships between sources.

#### Props

```typescript
interface SourceRelationshipDiagramProps {
  sources: Source[];
  relationshipType: 'similarity' | 'references' | 'topics' | 'all';
  onSourceSelect: (source: Source) => void;
  showMetrics?: boolean;
  layout?: 'circular' | 'hierarchical' | 'force';
}
```

#### Usage

```tsx
import { SourceRelationshipDiagram } from '@/components/mermaid/SourceRelationshipDiagram';

function RelationshipView({ sources }: { sources: Source[] }) {
  const handleSourceSelect = (source: Source) => {
    // Handle source selection
    console.log('Selected source:', source.title);
  };

  return (
    <SourceRelationshipDiagram
      sources={sources}
      relationshipType="similarity"
      onSourceSelect={handleSourceSelect}
      showMetrics={true}
      layout="force"
    />
  );
}
```

#### Features

- Multiple relationship visualization types
- Interactive source selection
- Relationship strength metrics
- Configurable layouts
- Source filtering and search

---

## Enhanced Components

### SourcesSidebar Enhancements

The SourcesSidebar component has been enhanced with diagram generation capabilities.

#### New Features

- "Generate Diagram" button when multiple sources are selected
- Diagram history section
- Quick action buttons for common diagram types

#### Usage

```tsx
// The SourcesSidebar automatically shows diagram options when sources are selected
// No additional configuration needed - the enhancements are built-in
```

### Mermaid Component Enhancements

The existing Mermaid component has been enhanced with source integration features.

#### New Props

```typescript
interface EnhancedMermaidProps extends MermaidProps {
  sourceAttribution?: SourceAttribution[];
  onNodeClick?: (nodeId: string) => void;
  highlightNodes?: string[];
  showAttribution?: boolean;
}
```

#### Features

- Source attribution display
- Node click handlers
- Source highlighting
- Enhanced export with metadata

---

## Services

### SourceDiagramService

Service for persisting and managing source diagrams.

#### Interface

```typescript
class SourceDiagramService extends MermaidService {
  saveDiagramWithSources(diagram: MermaidDiagram, sources: Source[]): Promise<SourceDiagram>;
  getDiagramsBySources(sourceIds: string[]): Promise<SourceDiagram[]>;
  updateDiagramSources(diagramId: string, sources: Source[]): Promise<void>;
  regenerateDiagram(diagramId: string): Promise<SourceDiagram>;
}
```

#### Usage

```typescript
import { SourceDiagramService } from '@/services/sourceDiagramService';

const service = new SourceDiagramService();

// Save a diagram with source attribution
const sourceDiagram = await service.saveDiagramWithSources(diagram, sources);

// Get diagrams that use specific sources
const relatedDiagrams = await service.getDiagramsBySources(['source1', 'source2']);
```

---

## Hooks

### useMermaidSourceAnalysis

Hook for managing source analysis and diagram generation.

#### Interface

```typescript
function useMermaidSourceAnalysis() {
  return {
    analyzeSource: (source: Source) => Promise<SourceAnalysisResult>;
    generateDiagram: (sources: Source[], type: DiagramType) => Promise<SourceDiagram>;
    isAnalyzing: boolean;
    error: string | null;
  };
}
```

#### Usage

```tsx
import { useMermaidSourceAnalysis } from '@/hooks/useMermaidSourceAnalysis';

function MyComponent() {
  const { analyzeSource, generateDiagram, isAnalyzing, error } = useMermaidSourceAnalysis();

  const handleGenerate = async () => {
    try {
      const diagram = await generateDiagram(selectedSources, 'flowchart');
      // Handle generated diagram
    } catch (err) {
      console.error('Generation failed:', err);
    }
  };

  return (
    <div>
      <button onClick={handleGenerate} disabled={isAnalyzing}>
        {isAnalyzing ? 'Generating...' : 'Generate Diagram'}
      </button>
      {error && <div className="error">{error}</div>}
    </div>
  );
}
```

---

## Types and Interfaces

### Core Types

```typescript
interface SourceDiagram extends MermaidDiagram {
  source_ids: string[];
  analysis_metadata: AnalysisMetadata;
  interactive_metadata: InteractiveMetadata;
}

interface AnalysisMetadata {
  concepts_extracted: number;
  relationships_found: number;
  analysis_confidence: number;
  generation_parameters: DiagramGenerationOptions;
}

interface InteractiveMetadata {
  node_source_map: Record<string, string[]>;
  clickable_nodes: string[];
  source_attribution: SourceAttribution[];
}

interface Concept {
  id: string;
  name: string;
  type: 'entity' | 'process' | 'decision' | 'outcome';
  sourceIds: string[];
  confidence: number;
}

interface Relationship {
  from: string;
  to: string;
  type: 'depends_on' | 'leads_to' | 'contains' | 'references';
  strength: number;
  sourceIds: string[];
}
```

### Configuration Types

```typescript
interface DiagramGenerationOptions {
  diagram_type: DiagramType;
  max_concepts: number;
  relationship_depth: number;
  include_metadata: boolean;
  filter_by_relevance: boolean;
  minimum_confidence: number;
}

type DiagramType = 'flowchart' | 'mindmap' | 'relationship' | 'workflow';
```