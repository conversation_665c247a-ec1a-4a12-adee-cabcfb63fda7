# Sample Diagrams

This document showcases various types of diagrams that can be generated from different source types using the Mermaid-Sources Integration feature.

## API Documentation Diagrams

### REST API Flow

Generated from API documentation sources:

```mermaid
flowchart TD
    A[Client Request] --> B{Authentication}
    B -->|Valid <PERSON>ken| C[Rate Limiting]
    B -->|Invalid Token| D[Return 401]
    C -->|Within Limits| E[Route to Handler]
    C -->|Rate Exceeded| F[Return 429]
    E --> G{Validate Input}
    G -->|Valid| H[Process Request]
    G -->|Invalid| I[Return 400]
    H --> J[Database Query]
    J --> K[Format Response]
    K --> L[Return 200]
    
    click A "source:api-overview.md" "API Overview Documentation"
    click B "source:auth-guide.md" "Authentication Guide"
    click C "source:rate-limiting.md" "Rate Limiting Documentation"
    click E "source:routing.md" "Request Routing"
    click H "source:handlers.md" "Request Handlers"
    click J "source:database.md" "Database Integration"
    
    style A fill:#e1f5fe,stroke:#01579b
    style B fill:#fff3e0,stroke:#e65100
    style G fill:#fff3e0,stroke:#e65100
    style H fill:#e8f5e8,stroke:#1b5e20
    style L fill:#e8f5e8,stroke:#1b5e20
```

### API Endpoints Relationship

```mermaid
graph LR
    subgraph "User Management"
        A[POST /users] --> B[GET /users/:id]
        B --> C[PUT /users/:id]
        C --> D[DELETE /users/:id]
    end
    
    subgraph "Authentication"
        E[POST /auth/login] --> F[POST /auth/refresh]
        F --> G[POST /auth/logout]
    end
    
    subgraph "Data Operations"
        H[GET /data] --> I[POST /data]
        I --> J[PUT /data/:id]
        J --> K[DELETE /data/:id]
    end
    
    E --> A
    B --> H
    
    click A "source:user-endpoints.md"
    click E "source:auth-endpoints.md"
    click H "source:data-endpoints.md"
```

## Project Documentation Diagrams

### Project Structure Mindmap

Generated from project documentation and README files:

```mermaid
mindmap
  root((Project))
    Architecture
      Frontend
        React Components
          UI Components
          Business Logic
        State Management
          Redux Store
          Context API
        Routing
          React Router
          Protected Routes
      Backend
        API Layer
          REST Endpoints
          GraphQL Schema
        Business Logic
          Services
          Controllers
        Data Layer
          Database Models
          Repositories
      Infrastructure
        Deployment
          Docker Containers
          Kubernetes
        Monitoring
          Logging
          Metrics
    Documentation
      User Guide
        Getting Started
        Tutorials
        FAQ
      API Reference
        Endpoint Documentation
        Schema Definitions
      Developer Docs
        Setup Guide
        Contributing
        Architecture
    Testing
      Unit Tests
        Component Tests
        Service Tests
      Integration Tests
        API Tests
        Database Tests
      E2E Tests
        User Workflows
        Critical Paths
```

### Development Workflow

```mermaid
flowchart TD
    A[Feature Request] --> B[Create Issue]
    B --> C[Assign to Developer]
    C --> D[Create Feature Branch]
    D --> E[Implement Feature]
    E --> F[Write Tests]
    F --> G[Run Local Tests]
    G -->|Pass| H[Create Pull Request]
    G -->|Fail| E
    H --> I[Code Review]
    I -->|Approved| J[Merge to Main]
    I -->|Changes Requested| E
    J --> K[Deploy to Staging]
    K --> L[QA Testing]
    L -->|Pass| M[Deploy to Production]
    L -->|Fail| N[Create Bug Report]
    N --> B
    
    click A "source:requirements.md"
    click B "source:issue-template.md"
    click E "source:development-guide.md"
    click F "source:testing-guide.md"
    click I "source:code-review-guide.md"
    click K "source:deployment-guide.md"
```

## Code Analysis Diagrams

### Class Hierarchy

Generated from source code files:

```mermaid
classDiagram
    class BaseService {
        +logger: Logger
        +config: Config
        +initialize()
        +cleanup()
    }
    
    class UserService {
        +userRepository: UserRepository
        +createUser(userData)
        +getUserById(id)
        +updateUser(id, data)
        +deleteUser(id)
    }
    
    class AuthService {
        +tokenService: TokenService
        +login(credentials)
        +logout(token)
        +refreshToken(token)
        +validateToken(token)
    }
    
    class DataService {
        +dataRepository: DataRepository
        +processData(data)
        +validateData(data)
        +transformData(data)
    }
    
    BaseService <|-- UserService
    BaseService <|-- AuthService
    BaseService <|-- DataService
    
    click BaseService "source:base-service.ts"
    click UserService "source:user-service.ts"
    click AuthService "source:auth-service.ts"
    click DataService "source:data-service.ts"
```

### Function Call Flow

```mermaid
sequenceDiagram
    participant C as Client
    participant A as API Gateway
    participant U as User Service
    participant D as Database
    participant N as Notification Service
    
    C->>A: POST /users
    A->>U: createUser(userData)
    U->>D: INSERT user
    D-->>U: user created
    U->>N: sendWelcomeEmail(user)
    N-->>U: email queued
    U-->>A: user response
    A-->>C: 201 Created
    
    Note over C,N: User registration flow
    
    click A "source:api-gateway.ts"
    click U "source:user-service.ts"
    click N "source:notification-service.ts"
```

## Process Documentation Diagrams

### Business Process Flow

Generated from process documentation:

```mermaid
flowchart TD
    A[Customer Inquiry] --> B{Existing Customer?}
    B -->|Yes| C[Lookup Account]
    B -->|No| D[Create Account]
    C --> E[Review History]
    D --> F[Collect Information]
    E --> G[Assess Request]
    F --> G
    G --> H{Approve Request?}
    H -->|Yes| I[Process Order]
    H -->|No| J[Send Rejection]
    I --> K[Schedule Delivery]
    J --> L[Log Decision]
    K --> M[Confirm with Customer]
    L --> N[End Process]
    M --> O[Track Delivery]
    O --> P[Delivery Complete]
    P --> N
    
    click A "source:customer-service-guide.md"
    click C "source:account-management.md"
    click D "source:onboarding-process.md"
    click I "source:order-processing.md"
    click K "source:delivery-scheduling.md"
```

### Approval Workflow

```mermaid
stateDiagram-v2
    [*] --> Submitted
    Submitted --> UnderReview : assign_reviewer
    UnderReview --> Approved : approve
    UnderReview --> ChangesRequested : request_changes
    UnderReview --> Rejected : reject
    ChangesRequested --> Submitted : resubmit
    Approved --> [*]
    Rejected --> [*]
    
    note right of UnderReview
        Reviewer has 48 hours
        to make a decision
    end note
    
    note right of ChangesRequested
        Submitter has 7 days
        to make changes
    end note
```

## Knowledge Base Diagrams

### Topic Relationships

Generated from knowledge base articles:

```mermaid
graph TB
    subgraph "Getting Started"
        A[Installation Guide]
        B[Quick Start Tutorial]
        C[Basic Configuration]
    end
    
    subgraph "Advanced Topics"
        D[Custom Plugins]
        E[Performance Tuning]
        F[Security Configuration]
    end
    
    subgraph "Troubleshooting"
        G[Common Issues]
        H[Error Messages]
        I[Debug Guide]
    end
    
    subgraph "API Reference"
        J[Core API]
        K[Plugin API]
        L[Configuration API]
    end
    
    A --> B
    B --> C
    C --> D
    C --> E
    C --> F
    D --> K
    E --> L
    F --> L
    G --> H
    H --> I
    J --> K
    K --> L
    
    click A "source:installation.md"
    click B "source:quickstart.md"
    click C "source:configuration.md"
    click D "source:plugins.md"
    click E "source:performance.md"
    click F "source:security.md"
    click G "source:troubleshooting.md"
    click H "source:error-reference.md"
    click I "source:debugging.md"
    click J "source:core-api.md"
    click K "source:plugin-api.md"
    click L "source:config-api.md"
```

### Concept Map

```mermaid
mindmap
  root((Knowledge Base))
    Setup
      Installation
        System Requirements
        Download & Install
        Initial Configuration
      Configuration
        Basic Settings
        Advanced Options
        Environment Variables
    Usage
      Basic Operations
        Creating Content
        Editing Content
        Publishing Content
      Advanced Features
        Custom Workflows
        Integrations
        Automation
    Maintenance
      Updates
        Version Management
        Migration Guide
        Rollback Procedures
      Monitoring
        Performance Metrics
        Error Tracking
        Usage Analytics
    Support
      Documentation
        User Guide
        API Reference
        Tutorials
      Community
        Forums
        Discord
        GitHub Issues
```

## Multi-Source Analysis Diagrams

### Source Similarity Network

Generated from analyzing relationships between multiple sources:

```mermaid
graph TD
    A[API Documentation] -.->|references| B[User Guide]
    A -->|implements| C[Code Examples]
    B -->|explains| C
    D[Architecture Guide] -->|defines| A
    D -.->|influences| E[Design Patterns]
    E -->|used in| C
    F[Testing Guide] -->|validates| C
    F -.->|references| A
    G[Deployment Guide] -->|uses| D
    G -.->|references| F
    
    style A fill:#ff9999,stroke:#cc0000
    style B fill:#99ccff,stroke:#0066cc
    style C fill:#99ff99,stroke:#00cc00
    style D fill:#ffcc99,stroke:#cc6600
    style E fill:#cc99ff,stroke:#6600cc
    style F fill:#ffff99,stroke:#cccc00
    style G fill:#ff99cc,stroke:#cc0066
    
    click A "source:api-docs.md"
    click B "source:user-guide.md"
    click C "source:examples/"
    click D "source:architecture.md"
    click E "source:patterns.md"
    click F "source:testing.md"
    click G "source:deployment.md"
```

### Content Hierarchy

```mermaid
flowchart TD
    A[Project Root] --> B[Documentation]
    A --> C[Source Code]
    A --> D[Tests]
    A --> E[Configuration]
    
    B --> F[User Guides]
    B --> G[API Reference]
    B --> H[Tutorials]
    
    C --> I[Frontend]
    C --> J[Backend]
    C --> K[Shared]
    
    D --> L[Unit Tests]
    D --> M[Integration Tests]
    D --> N[E2E Tests]
    
    E --> O[Environment]
    E --> P[Build Scripts]
    E --> Q[Docker]
    
    F --> R[Getting Started]
    F --> S[Advanced Usage]
    G --> T[Endpoints]
    G --> U[Models]
    H --> V[Basic Tutorial]
    H --> W[Advanced Tutorial]
    
    click A "source:README.md"
    click F "source:docs/user/"
    click G "source:docs/api/"
    click H "source:docs/tutorials/"
    click I "source:src/frontend/"
    click J "source:src/backend/"
    click K "source:src/shared/"
    click L "source:tests/unit/"
    click M "source:tests/integration/"
    click N "source:tests/e2e/"
```

## Interactive Features Demo

### Clickable Process Flow

This diagram demonstrates interactive features where clicking nodes reveals source content:

```mermaid
flowchart LR
    A[📋 Requirements] --> B[🎨 Design]
    B --> C[⚡ Implementation]
    C --> D[🧪 Testing]
    D --> E[🚀 Deployment]
    E --> F[📊 Monitoring]
    
    A --> G[📝 User Stories]
    G --> H[✅ Acceptance Criteria]
    H --> D
    
    B --> I[🏗️ Architecture]
    I --> J[🔧 Technical Specs]
    J --> C
    
    C --> K[🔍 Code Review]
    K --> D
    
    D --> L[🐛 Bug Fixes]
    L --> C
    
    F --> M[📈 Performance]
    M --> N[🔧 Optimization]
    N --> C
    
    click A "source:requirements.md" "View Requirements Document"
    click B "source:design.md" "View Design Document"
    click C "source:implementation/" "View Implementation Code"
    click D "source:tests/" "View Test Suite"
    click E "source:deployment.md" "View Deployment Guide"
    click F "source:monitoring.md" "View Monitoring Setup"
    click G "source:user-stories.md" "View User Stories"
    click I "source:architecture.md" "View Architecture Guide"
    click K "source:code-review.md" "View Code Review Guidelines"
    
    style A fill:#e1f5fe,stroke:#01579b,color:#000
    style B fill:#f3e5f5,stroke:#4a148c,color:#000
    style C fill:#e8f5e8,stroke:#1b5e20,color:#000
    style D fill:#fff3e0,stroke:#e65100,color:#000
    style E fill:#fce4ec,stroke:#880e4f,color:#000
    style F fill:#f1f8e9,stroke:#33691e,color:#000
```

### Source Attribution Example

This shows how source attribution appears in generated diagrams:

```mermaid
flowchart TD
    A["🔐 User Authentication<br/><small>📄 auth-guide.md:15-30</small>"] --> B["✅ Token Validation<br/><small>📄 security.md:45-60</small>"]
    B --> C["🛡️ Permission Check<br/><small>📄 permissions.md:20-35</small>"]
    C --> D["📊 Access Granted<br/><small>📄 access-control.md:10-25</small>"]
    
    style A fill:#ffebee,stroke:#c62828
    style B fill:#e8f5e8,stroke:#2e7d32
    style C fill:#fff3e0,stroke:#f57c00
    style D fill:#e1f5fe,stroke:#1565c0
```

## Performance Optimization Examples

### Large Dataset Visualization

For large source sets, the system automatically creates simplified overview diagrams:

```mermaid
flowchart TD
    A[📚 Documentation<br/>25 sources] --> B[💻 Code<br/>150 sources]
    B --> C[🧪 Tests<br/>75 sources]
    C --> D[⚙️ Configuration<br/>20 sources]
    
    A --> E[📖 User Guides<br/>10 sources]
    A --> F[🔧 API Docs<br/>15 sources]
    
    B --> G[🎨 Frontend<br/>80 sources]
    B --> H[⚡ Backend<br/>70 sources]
    
    C --> I[🔬 Unit Tests<br/>50 sources]
    C --> J[🔗 Integration<br/>25 sources]
    
    style A fill:#e3f2fd,stroke:#1976d2
    style B fill:#f3e5f5,stroke:#7b1fa2
    style C fill:#e8f5e8,stroke:#388e3c
    style D fill:#fff3e0,stroke:#f57c00
```

### Chunked Processing Visualization

Shows how large source sets are processed in chunks:

```mermaid
gantt
    title Source Analysis Progress
    dateFormat X
    axisFormat %s
    
    section Chunk 1 (Sources 1-20)
    Analysis    :done, chunk1, 0, 5s
    
    section Chunk 2 (Sources 21-40)
    Analysis    :done, chunk2, 5s, 10s
    
    section Chunk 3 (Sources 41-60)
    Analysis    :active, chunk3, 10s, 15s
    
    section Chunk 4 (Sources 61-80)
    Analysis    :chunk4, 15s, 20s
    
    section Final Processing
    Merge Results :merge, 20s, 22s
    Generate Diagram :generate, 22s, 25s
```

These sample diagrams demonstrate the full range of visualization capabilities provided by the Mermaid-Sources Integration feature, from simple flowcharts to complex multi-source relationship networks.