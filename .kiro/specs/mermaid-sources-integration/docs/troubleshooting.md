# Troubleshooting Guide

## Common Issues and Solutions

### Diagram Generation Issues

#### Issue: "No concepts extracted from sources"

**Symptoms:**
- Diagram generation fails with empty concept list
- Error message: "Unable to extract meaningful concepts from source content"

**Causes:**
- Sources contain only metadata or very short content
- Content is heavily formatted (tables, code blocks) without descriptive text
- Sources are in unsupported formats

**Solutions:**
1. **Check source content quality:**
   ```typescript
   // Verify source has sufficient text content
   const hasContent = source.content && source.content.length > 100;
   const hasDescriptiveText = /[a-zA-Z\s]{50,}/.test(source.content);
   ```

2. **Adjust analysis parameters:**
   ```typescript
   const options: DiagramGenerationOptions = {
     minimum_confidence: 0.3, // Lower threshold
     max_concepts: 20, // Increase concept limit
     filter_by_relevance: false // Include all concepts
   };
   ```

3. **Combine with other sources:**
   - Use multiple related sources together
   - Include context-providing sources (README files, overviews)

#### Issue: "Diagram too complex to render"

**Symptoms:**
- <PERSON>rowser becomes unresponsive during rendering
- Diagram appears truncated or malformed
- Memory usage spikes

**Causes:**
- Too many concepts extracted (>100)
- Complex relationship networks
- Deeply nested hierarchies

**Solutions:**
1. **Limit diagram complexity:**
   ```typescript
   const options: DiagramGenerationOptions = {
     max_concepts: 30,
     relationship_depth: 2,
     filter_by_relevance: true,
     minimum_confidence: 0.6
   };
   ```

2. **Use chunking for large datasets:**
   ```typescript
   const chunkSources = (sources: Source[], chunkSize: number = 10) => {
     const chunks: Source[][] = [];
     for (let i = 0; i < sources.length; i += chunkSize) {
       chunks.push(sources.slice(i, i + chunkSize));
     }
     return chunks;
   };
   ```

3. **Generate multiple focused diagrams:**
   - Create separate diagrams for different topics
   - Use relationship diagrams to show high-level connections

#### Issue: "Invalid Mermaid syntax generated"

**Symptoms:**
- Diagram fails to render
- Console errors about Mermaid parsing
- Blank diagram area

**Causes:**
- Special characters in concept names
- Reserved Mermaid keywords used as node IDs
- Malformed relationship syntax

**Solutions:**
1. **Enable syntax validation:**
   ```typescript
   import { validateMermaidSyntax } from '@/lib/mermaidValidator';
   
   const generateDiagram = async (sources: Source[]) => {
     const analyzer = new MermaidSourceAnalyzer();
     const analysis = await analyzer.analyzeSources(sources);
     let diagramCode = analyzer.generateDiagramCode(analysis, 'flowchart');
     
     // Validate and fix syntax
     const validation = validateMermaidSyntax(diagramCode);
     if (!validation.isValid) {
       diagramCode = validation.correctedCode;
     }
     
     return diagramCode;
   };
   ```

2. **Sanitize concept names:**
   ```typescript
   const sanitizeNodeId = (name: string): string => {
     return name
       .replace(/[^a-zA-Z0-9_]/g, '_')
       .replace(/^(\d)/, 'n$1') // Prefix numbers
       .substring(0, 50); // Limit length
   };
   ```

### Source Integration Issues

#### Issue: "Source content not loading in interactive viewer"

**Symptoms:**
- Clicking diagram nodes shows empty content
- Source panel remains blank
- Console errors about missing source data

**Causes:**
- Source IDs don't match between diagram and source system
- Source content not properly cached
- Permission issues accessing source content

**Solutions:**
1. **Verify source ID mapping:**
   ```typescript
   const verifySourceMapping = (diagram: SourceDiagram, sources: Source[]) => {
     const diagramSourceIds = diagram.source_ids;
     const availableSourceIds = sources.map(s => s.id);
     const missingIds = diagramSourceIds.filter(id => !availableSourceIds.includes(id));
     
     if (missingIds.length > 0) {
       console.warn('Missing sources:', missingIds);
       return false;
     }
     return true;
   };
   ```

2. **Implement source caching:**
   ```typescript
   const useSourceCache = () => {
     const [cache, setCache] = useState<Map<string, Source>>(new Map());
     
     const getSource = async (sourceId: string): Promise<Source | null> => {
       if (cache.has(sourceId)) {
         return cache.get(sourceId)!;
       }
       
       try {
         const source = await fetchSource(sourceId);
         setCache(prev => new Map(prev).set(sourceId, source));
         return source;
       } catch (error) {
         console.error('Failed to fetch source:', sourceId, error);
         return null;
       }
     };
     
     return { getSource };
   };
   ```

#### Issue: "Diagram regeneration fails after source updates"

**Symptoms:**
- "Regenerate" button doesn't work
- Outdated content appears in new diagrams
- Error messages about stale data

**Causes:**
- Source change detection not working
- Cached analysis results not invalidated
- Concurrent modification conflicts

**Solutions:**
1. **Implement proper cache invalidation:**
   ```typescript
   const useSourceChangeDetection = (sources: Source[]) => {
     const [lastModified, setLastModified] = useState<Map<string, number>>(new Map());
     
     const hasSourcesChanged = useCallback(() => {
       return sources.some(source => {
         const currentModified = source.lastModified || 0;
         const cachedModified = lastModified.get(source.id) || 0;
         return currentModified > cachedModified;
       });
     }, [sources, lastModified]);
     
     const updateModificationTimes = useCallback(() => {
       const newTimes = new Map();
       sources.forEach(source => {
         newTimes.set(source.id, source.lastModified || Date.now());
       });
       setLastModified(newTimes);
     }, [sources]);
     
     return { hasSourcesChanged, updateModificationTimes };
   };
   ```

2. **Add conflict resolution:**
   ```typescript
   const regenerateDiagramSafely = async (diagramId: string) => {
     try {
       // Lock diagram for regeneration
       await lockDiagram(diagramId);
       
       // Get fresh source data
       const sources = await fetchLatestSources(diagramId);
       
       // Regenerate with fresh data
       const newDiagram = await generateDiagram(sources);
       
       // Update with conflict detection
       await updateDiagramWithConflictCheck(diagramId, newDiagram);
       
     } catch (error) {
       if (error.type === 'CONFLICT') {
         // Handle conflict - show user options
         showConflictResolutionDialog(error.details);
       } else {
         throw error;
       }
     } finally {
       await unlockDiagram(diagramId);
     }
   };
   ```

### Performance Issues

#### Issue: "Slow diagram generation with large source sets"

**Symptoms:**
- Generation takes more than 30 seconds
- Browser becomes unresponsive
- Memory usage continuously increases

**Causes:**
- Processing too many sources simultaneously
- Inefficient text analysis algorithms
- Memory leaks in analysis pipeline

**Solutions:**
1. **Implement progressive processing:**
   ```typescript
   const useProgressiveAnalysis = () => {
     const [progress, setProgress] = useState(0);
     const [isProcessing, setIsProcessing] = useState(false);
     
     const analyzeSourcesProgressively = async (sources: Source[]) => {
       setIsProcessing(true);
       setProgress(0);
       
       const results: SourceAnalysisResult[] = [];
       const batchSize = 5;
       
       for (let i = 0; i < sources.length; i += batchSize) {
         const batch = sources.slice(i, i + batchSize);
         
         // Process batch
         const batchResults = await Promise.all(
           batch.map(source => analyzer.analyzeSource(source))
         );
         
         results.push(...batchResults);
         
         // Update progress
         setProgress((i + batchSize) / sources.length);
         
         // Allow UI to update
         await new Promise(resolve => setTimeout(resolve, 10));
       }
       
       setIsProcessing(false);
       return combineAnalysisResults(results);
     };
     
     return { analyzeSourcesProgressively, progress, isProcessing };
   };
   ```

2. **Add memory management:**
   ```typescript
   const useMemoryOptimizedAnalysis = () => {
     const analyzeWithMemoryManagement = async (sources: Source[]) => {
       const analyzer = new MermaidSourceAnalyzer();
       
       // Monitor memory usage
       const initialMemory = performance.memory?.usedJSHeapSize || 0;
       
       try {
         const result = await analyzer.analyzeSources(sources);
         
         // Check memory usage
         const finalMemory = performance.memory?.usedJSHeapSize || 0;
         const memoryIncrease = finalMemory - initialMemory;
         
         if (memoryIncrease > 50 * 1024 * 1024) { // 50MB threshold
           console.warn('High memory usage detected:', memoryIncrease / 1024 / 1024, 'MB');
           
           // Trigger garbage collection if available
           if (window.gc) {
             window.gc();
           }
         }
         
         return result;
       } catch (error) {
         // Clean up on error
         analyzer.cleanup?.();
         throw error;
       }
     };
     
     return { analyzeWithMemoryManagement };
   };
   ```

### Export Issues

#### Issue: "Source attribution missing in exported diagrams"

**Symptoms:**
- Exported SVG/PNG lacks source information
- Shared diagrams don't include references
- Attribution metadata not preserved

**Causes:**
- Export process not including metadata
- Source information not properly embedded
- Format limitations

**Solutions:**
1. **Enhance export with metadata:**
   ```typescript
   const exportDiagramWithAttribution = async (diagram: SourceDiagram, format: 'svg' | 'png') => {
     const exportOptions = {
       includeSourceAttribution: true,
       embedMetadata: true,
       addWatermark: true
     };
     
     if (format === 'svg') {
       // Add source information as SVG metadata
       const svgWithMetadata = addSVGMetadata(diagram.code, {
         sources: diagram.source_ids,
         generated: new Date().toISOString(),
         attribution: diagram.interactive_metadata.source_attribution
       });
       
       return svgWithMetadata;
     } else {
       // For PNG, add attribution as overlay
       const pngWithAttribution = await addPNGAttribution(diagram.code, {
         sources: diagram.source_ids.length,
         timestamp: new Date().toLocaleDateString()
       });
       
       return pngWithAttribution;
     }
   };
   ```

2. **Create attribution templates:**
   ```typescript
   const generateAttributionText = (diagram: SourceDiagram, sources: Source[]) => {
     const sourceList = sources
       .filter(s => diagram.source_ids.includes(s.id))
       .map(s => `- ${s.title} (${s.type})`)
       .join('\n');
     
     return `
   Generated from sources:
   ${sourceList}
   
   Created: ${new Date().toLocaleDateString()}
   Concepts: ${diagram.analysis_metadata.concepts_extracted}
   Relationships: ${diagram.analysis_metadata.relationships_found}
   `;
   };
   ```

## Debugging Tools

### Analysis Debug Mode

Enable detailed logging for source analysis:

```typescript
const enableAnalysisDebug = () => {
  localStorage.setItem('mermaid-sources-debug', 'true');
};

// In MermaidSourceAnalyzer
const debugLog = (message: string, data?: any) => {
  if (localStorage.getItem('mermaid-sources-debug') === 'true') {
    console.log(`[MermaidSourceAnalyzer] ${message}`, data);
  }
};
```

### Diagram Validation Tool

```typescript
const validateDiagramIntegrity = (diagram: SourceDiagram, sources: Source[]) => {
  const issues: string[] = [];
  
  // Check source references
  const missingSourceIds = diagram.source_ids.filter(id => 
    !sources.some(s => s.id === id)
  );
  if (missingSourceIds.length > 0) {
    issues.push(`Missing sources: ${missingSourceIds.join(', ')}`);
  }
  
  // Check node-source mapping
  const nodeSourceMap = diagram.interactive_metadata.node_source_map;
  Object.entries(nodeSourceMap).forEach(([nodeId, sourceIds]) => {
    const invalidSourceIds = sourceIds.filter(id => 
      !diagram.source_ids.includes(id)
    );
    if (invalidSourceIds.length > 0) {
      issues.push(`Node ${nodeId} references invalid sources: ${invalidSourceIds.join(', ')}`);
    }
  });
  
  // Check Mermaid syntax
  try {
    // Basic syntax validation
    const lines = diagram.code.split('\n');
    const hasValidHeader = lines[0].match(/^(graph|flowchart|mindmap)/);
    if (!hasValidHeader) {
      issues.push('Invalid or missing diagram type declaration');
    }
  } catch (error) {
    issues.push(`Syntax validation error: ${error.message}`);
  }
  
  return {
    isValid: issues.length === 0,
    issues
  };
};
```

### Performance Monitoring

```typescript
const usePerformanceMonitoring = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({});
  
  const measureOperation = async <T>(
    operationName: string, 
    operation: () => Promise<T>
  ): Promise<T> => {
    const startTime = performance.now();
    const startMemory = performance.memory?.usedJSHeapSize || 0;
    
    try {
      const result = await operation();
      
      const endTime = performance.now();
      const endMemory = performance.memory?.usedJSHeapSize || 0;
      
      setMetrics(prev => ({
        ...prev,
        [operationName]: {
          duration: endTime - startTime,
          memoryDelta: endMemory - startMemory,
          timestamp: Date.now()
        }
      }));
      
      return result;
    } catch (error) {
      console.error(`Operation ${operationName} failed:`, error);
      throw error;
    }
  };
  
  return { measureOperation, metrics };
};
```

## Getting Help

### Log Collection

When reporting issues, include these logs:

```typescript
const collectDiagnosticInfo = () => {
  return {
    userAgent: navigator.userAgent,
    timestamp: new Date().toISOString(),
    memoryUsage: performance.memory,
    localStorage: {
      debugMode: localStorage.getItem('mermaid-sources-debug'),
      cacheSize: localStorage.length
    },
    sourceCount: sources.length,
    diagramCount: diagrams.length,
    lastError: getLastError()
  };
};
```

### Support Checklist

Before reporting issues:

1. ✅ Check browser console for error messages
2. ✅ Verify source content is accessible and properly formatted
3. ✅ Test with a smaller set of sources
4. ✅ Clear browser cache and localStorage
5. ✅ Try different diagram types
6. ✅ Check network connectivity for source fetching
7. ✅ Verify browser compatibility (Chrome 90+, Firefox 88+, Safari 14+)

### Common Error Codes

- **E001**: Source analysis timeout - Reduce source count or increase timeout
- **E002**: Invalid Mermaid syntax - Check for special characters in source content
- **E003**: Memory limit exceeded - Use chunked processing
- **E004**: Source not found - Verify source IDs and permissions
- **E005**: Diagram too complex - Reduce max_concepts or relationship_depth