# API Reference

## Core Classes

### MermaidSourceAnalyzer

Main service class for analyzing source content and generating diagrams.

#### Constructor

```typescript
new MermaidSourceAnalyzer(config?: AnalysisConfig)
```

#### Methods

##### analyzeSource(source: Source): Promise<SourceAnalysisResult>

Analyzes a single source for concepts and relationships.

**Parameters:**
- `source: Source` - The source to analyze

**Returns:** `Promise<SourceAnalysisResult>` - Analysis results including concepts, relationships, and hierarchy

**Example:**
```typescript
const analyzer = new MermaidSourceAnalyzer();
const result = await analyzer.analyzeSource(source);
console.log(`Found ${result.concepts.length} concepts`);
```

##### analyzeSources(sources: Source[]): Promise<SourceAnalysisResult>

Analyzes multiple sources and finds cross-source relationships.

**Parameters:**
- `sources: Source[]` - Array of sources to analyze

**Returns:** `Promise<SourceAnalysisResult>` - Combined analysis results

##### generateDiagramCode(analysis: SourceAnalysisResult, type: DiagramType, options?: DiagramGenerationOptions): string

Generates Mermaid diagram code from analysis results.

**Parameters:**
- `analysis: SourceAnalysisResult` - Analysis results
- `type: DiagramType` - Type of diagram to generate
- `options?: DiagramGenerationOptions` - Generation options

**Returns:** `string` - Mermaid diagram code

##### extractConcepts(content: string): Concept[]

Extracts concepts from text content.

**Parameters:**
- `content: string` - Text content to analyze

**Returns:** `Concept[]` - Array of extracted concepts

##### findRelationships(concepts: Concept[], content: string): Relationship[]

Finds relationships between concepts.

**Parameters:**
- `concepts: Concept[]` - Array of concepts
- `content: string` - Source content for context

**Returns:** `Relationship[]` - Array of relationships

---

### SourceDiagramService

Service for persisting and managing source diagrams.

#### Constructor

```typescript
new SourceDiagramService(dbConnection?: DatabaseConnection)
```

#### Methods

##### saveDiagramWithSources(diagram: MermaidDiagram, sources: Source[]): Promise<SourceDiagram>

Saves a diagram with source attribution.

**Parameters:**
- `diagram: MermaidDiagram` - The diagram to save
- `sources: Source[]` - Associated sources

**Returns:** `Promise<SourceDiagram>` - Saved diagram with metadata

##### getDiagramsBySources(sourceIds: string[]): Promise<SourceDiagram[]>

Retrieves diagrams that use specific sources.

**Parameters:**
- `sourceIds: string[]` - Array of source IDs

**Returns:** `Promise<SourceDiagram[]>` - Array of matching diagrams

##### updateDiagramSources(diagramId: string, sources: Source[]): Promise<void>

Updates the sources associated with a diagram.

**Parameters:**
- `diagramId: string` - ID of the diagram to update
- `sources: Source[]` - New source associations

##### regenerateDiagram(diagramId: string): Promise<SourceDiagram>

Regenerates a diagram using current source content.

**Parameters:**
- `diagramId: string` - ID of the diagram to regenerate

**Returns:** `Promise<SourceDiagram>` - Regenerated diagram

---

## React Components

### SourceDiagramGenerator

Main dialog component for generating diagrams from sources.

#### Props

```typescript
interface SourceDiagramGeneratorProps {
  sources: Source[];
  onDiagramGenerated: (diagram: SourceDiagram) => void;
  onError: (error: string) => void;
  defaultDiagramType?: DiagramType;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  className?: string;
  maxConcepts?: number;
  enablePreview?: boolean;
}
```

#### Events

- `onDiagramGenerated(diagram: SourceDiagram)` - Fired when diagram generation completes
- `onError(error: string)` - Fired when generation fails
- `onOpenChange(open: boolean)` - Fired when dialog open state changes

#### Example

```tsx
<SourceDiagramGenerator
  sources={selectedSources}
  onDiagramGenerated={handleDiagramGenerated}
  onError={handleError}
  defaultDiagramType="flowchart"
  isOpen={isGeneratorOpen}
  onOpenChange={setIsGeneratorOpen}
  maxConcepts={50}
  enablePreview={true}
/>
```

---

### InteractiveDiagramViewer

Enhanced Mermaid component with source interaction capabilities.

#### Props

```typescript
interface InteractiveDiagramViewerProps extends MermaidProps {
  sourceMap: Map<string, string[]>;
  onNodeClick: (nodeId: string, sourceIds: string[]) => void;
  onSourceView: (sourceId: string) => void;
  highlightedNodes?: string[];
  showSourceAttribution?: boolean;
  enableContextMenu?: boolean;
  className?: string;
}
```

#### Events

- `onNodeClick(nodeId: string, sourceIds: string[])` - Fired when a diagram node is clicked
- `onSourceView(sourceId: string)` - Fired when user wants to view a source

#### Example

```tsx
<InteractiveDiagramViewer
  code={diagram.code}
  sourceMap={diagram.interactive_metadata.node_source_map}
  onNodeClick={handleNodeClick}
  onSourceView={handleSourceView}
  highlightedNodes={selectedNodes}
  showSourceAttribution={true}
  enableContextMenu={true}
/>
```

---

### SourceRelationshipDiagram

Component for visualizing relationships between sources.

#### Props

```typescript
interface SourceRelationshipDiagramProps {
  sources: Source[];
  relationshipType: 'similarity' | 'references' | 'topics' | 'all';
  onSourceSelect: (source: Source) => void;
  showMetrics?: boolean;
  layout?: 'circular' | 'hierarchical' | 'force';
  minSimilarity?: number;
  maxNodes?: number;
  className?: string;
}
```

#### Events

- `onSourceSelect(source: Source)` - Fired when a source node is selected

#### Example

```tsx
<SourceRelationshipDiagram
  sources={projectSources}
  relationshipType="similarity"
  onSourceSelect={handleSourceSelect}
  showMetrics={true}
  layout="force"
  minSimilarity={0.3}
  maxNodes={50}
/>
```

---

## React Hooks

### useMermaidSourceAnalysis

Hook for managing source analysis and diagram generation.

#### Returns

```typescript
interface UseMermaidSourceAnalysisReturn {
  analyzeSource: (source: Source) => Promise<SourceAnalysisResult>;
  analyzeSources: (sources: Source[]) => Promise<SourceAnalysisResult>;
  generateDiagram: (sources: Source[], type: DiagramType, options?: DiagramGenerationOptions) => Promise<SourceDiagram>;
  isAnalyzing: boolean;
  progress: number;
  error: string | null;
  clearError: () => void;
}
```

#### Example

```tsx
const {
  generateDiagram,
  isAnalyzing,
  progress,
  error,
  clearError
} = useMermaidSourceAnalysis();

const handleGenerate = async () => {
  try {
    clearError();
    const diagram = await generateDiagram(sources, 'flowchart');
    // Handle success
  } catch (err) {
    // Error is automatically set in hook
  }
};
```

---

### useSourceDiagrams

Hook for managing source diagram persistence.

#### Returns

```typescript
interface UseSourceDiagramsReturn {
  diagrams: SourceDiagram[];
  saveDiagram: (diagram: MermaidDiagram, sources: Source[]) => Promise<SourceDiagram>;
  deleteDiagram: (diagramId: string) => Promise<void>;
  regenerateDiagram: (diagramId: string) => Promise<SourceDiagram>;
  getDiagramsBySources: (sourceIds: string[]) => SourceDiagram[];
  isLoading: boolean;
  error: string | null;
}
```

#### Example

```tsx
const {
  diagrams,
  saveDiagram,
  regenerateDiagram,
  isLoading
} = useSourceDiagrams();

const handleSave = async (diagram: MermaidDiagram, sources: Source[]) => {
  const savedDiagram = await saveDiagram(diagram, sources);
  console.log('Saved diagram:', savedDiagram.id);
};
```

---

## Type Definitions

### Core Types

#### Source

```typescript
interface Source {
  id: string;
  title: string;
  content: string;
  type: 'file' | 'url' | 'text' | 'document';
  tags?: string[];
  lastModified?: number;
  metadata?: Record<string, any>;
}
```

#### SourceDiagram

```typescript
interface SourceDiagram extends MermaidDiagram {
  source_ids: string[];
  analysis_metadata: AnalysisMetadata;
  interactive_metadata: InteractiveMetadata;
}
```

#### AnalysisMetadata

```typescript
interface AnalysisMetadata {
  concepts_extracted: number;
  relationships_found: number;
  analysis_confidence: number;
  generation_parameters: DiagramGenerationOptions;
  analysis_duration: number;
  created_at: string;
}
```

#### InteractiveMetadata

```typescript
interface InteractiveMetadata {
  node_source_map: Record<string, string[]>;
  clickable_nodes: string[];
  source_attribution: SourceAttribution[];
}
```

#### SourceAttribution

```typescript
interface SourceAttribution {
  node_id: string;
  source_id: string;
  content_excerpt: string;
  confidence: number;
  line_number?: number;
  section?: string;
}
```

### Analysis Types

#### SourceAnalysisResult

```typescript
interface SourceAnalysisResult {
  concepts: Concept[];
  relationships: Relationship[];
  hierarchy: HierarchyNode[];
  sourceMap: Map<string, string[]>;
  metadata: AnalysisResultMetadata;
}
```

#### Concept

```typescript
interface Concept {
  id: string;
  name: string;
  type: ConceptType;
  sourceIds: string[];
  confidence: number;
  context?: string;
  position?: TextPosition;
  aliases?: string[];
}
```

#### ConceptType

```typescript
type ConceptType = 
  | 'entity'
  | 'process' 
  | 'decision'
  | 'outcome'
  | 'data'
  | 'system'
  | 'actor'
  | 'event';
```

#### Relationship

```typescript
interface Relationship {
  from: string;
  to: string;
  type: RelationshipType;
  strength: number;
  sourceIds: string[];
  evidence?: string;
  bidirectional?: boolean;
}
```

#### RelationshipType

```typescript
type RelationshipType = 
  | 'depends_on'
  | 'leads_to'
  | 'contains'
  | 'references'
  | 'similar_to'
  | 'part_of'
  | 'implements'
  | 'uses';
```

#### HierarchyNode

```typescript
interface HierarchyNode {
  id: string;
  conceptId: string;
  parentId?: string;
  children: string[];
  level: number;
  weight: number;
}
```

### Configuration Types

#### DiagramGenerationOptions

```typescript
interface DiagramGenerationOptions {
  diagram_type: DiagramType;
  max_concepts: number;
  relationship_depth: number;
  include_metadata: boolean;
  filter_by_relevance: boolean;
  minimum_confidence: number;
  layout_direction?: 'TD' | 'LR' | 'BT' | 'RL';
  color_scheme?: string;
  show_labels?: boolean;
  group_by_source?: boolean;
}
```

#### DiagramType

```typescript
type DiagramType = 
  | 'flowchart'
  | 'mindmap'
  | 'relationship'
  | 'workflow'
  | 'hierarchy'
  | 'network';
```

#### AnalysisConfig

```typescript
interface AnalysisConfig {
  defaultMinConfidence: number;
  maxConceptsPerSource: number;
  enableMLAnalysis: boolean;
  customAnalyzers: string[];
  timeout: number;
  enableCaching: boolean;
  chunkSize: number;
}
```

---

## Error Types

### AnalysisError

```typescript
class AnalysisError extends Error {
  constructor(
    message: string,
    public code: string,
    public sourceId?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'AnalysisError';
  }
}
```

### DiagramGenerationError

```typescript
class DiagramGenerationError extends Error {
  constructor(
    message: string,
    public code: string,
    public diagramType?: DiagramType,
    public analysisResult?: SourceAnalysisResult
  ) {
    super(message);
    this.name = 'DiagramGenerationError';
  }
}
```

### Common Error Codes

- `ANALYSIS_TIMEOUT` - Analysis took too long
- `INSUFFICIENT_CONTENT` - Source content too short or empty
- `INVALID_SOURCE_FORMAT` - Source format not supported
- `CONCEPT_EXTRACTION_FAILED` - Failed to extract meaningful concepts
- `RELATIONSHIP_MAPPING_FAILED` - Failed to find relationships
- `DIAGRAM_GENERATION_FAILED` - Failed to generate valid Mermaid code
- `INVALID_MERMAID_SYNTAX` - Generated invalid Mermaid syntax
- `SOURCE_NOT_FOUND` - Referenced source not available
- `PERMISSION_DENIED` - Insufficient permissions to access source

---

## Utility Functions

### Text Processing

#### sanitizeNodeId(text: string): string

Sanitizes text for use as Mermaid node ID.

```typescript
const nodeId = sanitizeNodeId("User Authentication Process");
// Returns: "User_Authentication_Process"
```

#### extractTextFromHTML(html: string): string

Extracts plain text from HTML content.

#### detectLanguage(content: string): string

Detects the programming language of code content.

### Validation

#### validateMermaidSyntax(code: string): ValidationResult

Validates Mermaid diagram syntax.

```typescript
interface ValidationResult {
  isValid: boolean;
  errors: string[];
  correctedCode?: string;
}
```

#### validateSourceDiagram(diagram: SourceDiagram): ValidationResult

Validates a source diagram for integrity.

### Formatting

#### formatDiagramTitle(sources: Source[], type: DiagramType): string

Generates a descriptive title for a diagram.

#### formatSourceAttribution(sources: Source[]): string

Formats source attribution text for display.

---

## Constants

### Default Values

```typescript
export const DEFAULT_ANALYSIS_CONFIG: AnalysisConfig = {
  defaultMinConfidence: 0.5,
  maxConceptsPerSource: 50,
  enableMLAnalysis: false,
  customAnalyzers: [],
  timeout: 30000,
  enableCaching: true,
  chunkSize: 10
};

export const DEFAULT_GENERATION_OPTIONS: DiagramGenerationOptions = {
  diagram_type: 'flowchart',
  max_concepts: 30,
  relationship_depth: 2,
  include_metadata: true,
  filter_by_relevance: true,
  minimum_confidence: 0.5,
  layout_direction: 'TD',
  color_scheme: 'default',
  show_labels: true,
  group_by_source: false
};
```

### Limits

```typescript
export const LIMITS = {
  MAX_SOURCES_PER_ANALYSIS: 100,
  MAX_CONCEPTS_PER_DIAGRAM: 100,
  MAX_RELATIONSHIPS_PER_DIAGRAM: 200,
  MAX_SOURCE_CONTENT_LENGTH: 1000000, // 1MB
  MAX_ANALYSIS_TIMEOUT: 300000, // 5 minutes
  MAX_CONCURRENT_ANALYSES: 5
};
```

This API reference provides comprehensive documentation for all public interfaces, types, and utilities in the Mermaid-Sources Integration system.