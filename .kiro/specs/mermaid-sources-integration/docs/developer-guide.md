# Developer Guide

## Extending the Mermaid-Sources Integration

This guide covers how to extend and customize the Mermaid-Sources Integration system for specific use cases and new diagram types.

## Architecture Overview

The system is built with extensibility in mind, using a plugin-like architecture:

```
MermaidSourceAnalyzer (Core)
├── ContentExtractors (Pluggable)
├── ConceptAnalyzers (Pluggable)
├── RelationshipMappers (Pluggable)
└── DiagramGenerators (Pluggable)
```

## Adding New Diagram Types

### Step 1: Define the Diagram Type

```typescript
// types/diagrams.ts
export type DiagramType = 'flowchart' | 'mindmap' | 'relationship' | 'workflow' | 'custom-type';

export interface CustomDiagramOptions {
  layout: 'hierarchical' | 'circular' | 'tree';
  showMetrics: boolean;
  colorScheme: 'default' | 'categorical' | 'sequential';
  maxDepth: number;
}
```

### Step 2: Create a Diagram Generator

```typescript
// generators/CustomDiagramGenerator.ts
import { DiagramGenerator } from './base/DiagramGenerator';

export class CustomDiagramGenerator extends DiagramGenerator {
  generateDiagram(analysis: SourceAnalysisResult, options: CustomDiagramOptions): string {
    const { concepts, relationships } = analysis;
    
    // Custom diagram generation logic
    let mermaidCode = 'graph TD\n';
    
    // Add nodes with custom styling
    concepts.forEach(concept => {
      const nodeId = this.sanitizeNodeId(concept.id);
      const nodeLabel = this.formatNodeLabel(concept.name);
      const nodeStyle = this.getNodeStyle(concept.type, options.colorScheme);
      
      mermaidCode += `    ${nodeId}[${nodeLabel}]\n`;
      if (nodeStyle) {
        mermaidCode += `    style ${nodeId} ${nodeStyle}\n`;
      }
    });
    
    // Add relationships with custom formatting
    relationships.forEach(rel => {
      const fromId = this.sanitizeNodeId(rel.from);
      const toId = this.sanitizeNodeId(rel.to);
      const linkStyle = this.getLinkStyle(rel.type, rel.strength);
      
      mermaidCode += `    ${fromId} ${linkStyle} ${toId}\n`;
    });
    
    return mermaidCode;
  }
  
  private getNodeStyle(conceptType: string, colorScheme: string): string {
    const styleMap = {
      'default': {
        'entity': 'fill:#e1f5fe,stroke:#01579b',
        'process': 'fill:#f3e5f5,stroke:#4a148c',
        'decision': 'fill:#fff3e0,stroke:#e65100',
        'outcome': 'fill:#e8f5e8,stroke:#1b5e20'
      },
      'categorical': {
        'entity': 'fill:#ff9999,stroke:#cc0000',
        'process': 'fill:#99ccff,stroke:#0066cc',
        'decision': 'fill:#ffcc99,stroke:#cc6600',
        'outcome': 'fill:#99ff99,stroke:#00cc00'
      }
    };
    
    return styleMap[colorScheme]?.[conceptType] || '';
  }
  
  private getLinkStyle(relationshipType: string, strength: number): string {
    const thickness = Math.max(1, Math.floor(strength * 3));
    
    const styleMap = {
      'depends_on': `-->|depends on|`,
      'leads_to': `==>|leads to|`,
      'contains': `-.->|contains|`,
      'references': `-->|references|`
    };
    
    return styleMap[relationshipType] || '-->';
  }
}
```

### Step 3: Register the Generator

```typescript
// services/mermaidSourceAnalyzer.ts
import { CustomDiagramGenerator } from '../generators/CustomDiagramGenerator';

export class MermaidSourceAnalyzer {
  private generators: Map<DiagramType, DiagramGenerator> = new Map();
  
  constructor() {
    // Register built-in generators
    this.generators.set('flowchart', new FlowchartGenerator());
    this.generators.set('mindmap', new MindmapGenerator());
    this.generators.set('relationship', new RelationshipGenerator());
    this.generators.set('workflow', new WorkflowGenerator());
    
    // Register custom generator
    this.generators.set('custom-type', new CustomDiagramGenerator());
  }
  
  generateDiagramCode(analysis: SourceAnalysisResult, type: DiagramType, options?: any): string {
    const generator = this.generators.get(type);
    if (!generator) {
      throw new Error(`Unknown diagram type: ${type}`);
    }
    
    return generator.generateDiagram(analysis, options);
  }
  
  // Allow runtime registration of new generators
  registerGenerator(type: DiagramType, generator: DiagramGenerator): void {
    this.generators.set(type, generator);
  }
}
```

## Creating Custom Content Extractors

### Base Content Extractor

```typescript
// extractors/base/ContentExtractor.ts
export abstract class ContentExtractor {
  abstract canHandle(source: Source): boolean;
  abstract extractContent(source: Source): Promise<ExtractedContent>;
}

export interface ExtractedContent {
  text: string;
  structure: ContentStructure;
  metadata: Record<string, any>;
}

export interface ContentStructure {
  headings: Heading[];
  sections: Section[];
  lists: List[];
  codeBlocks: CodeBlock[];
}
```

### Example: Code File Extractor

```typescript
// extractors/CodeFileExtractor.ts
import { ContentExtractor } from './base/ContentExtractor';

export class CodeFileExtractor extends ContentExtractor {
  canHandle(source: Source): boolean {
    const codeExtensions = ['.js', '.ts', '.py', '.java', '.cpp', '.rs'];
    return codeExtensions.some(ext => source.title.endsWith(ext));
  }
  
  async extractContent(source: Source): Promise<ExtractedContent> {
    const content = source.content;
    
    // Extract functions, classes, and comments
    const functions = this.extractFunctions(content);
    const classes = this.extractClasses(content);
    const comments = this.extractComments(content);
    const imports = this.extractImports(content);
    
    // Create structured representation
    const structure: ContentStructure = {
      headings: this.createHeadingsFromCode(functions, classes),
      sections: this.createSectionsFromCode(functions, classes),
      lists: [],
      codeBlocks: [{
        language: this.detectLanguage(source.title),
        content: content,
        startLine: 1,
        endLine: content.split('\n').length
      }]
    };
    
    // Extract meaningful text from comments and docstrings
    const text = [
      ...comments.map(c => c.text),
      ...functions.map(f => f.docstring || f.name),
      ...classes.map(c => c.docstring || c.name)
    ].join(' ');
    
    return {
      text,
      structure,
      metadata: {
        language: this.detectLanguage(source.title),
        functions: functions.length,
        classes: classes.length,
        imports: imports.length
      }
    };
  }
  
  private extractFunctions(content: string): Function[] {
    // Language-specific function extraction logic
    const functionRegex = /(?:function|def|fn)\s+(\w+)\s*\([^)]*\)\s*(?:{|:)/g;
    const functions: Function[] = [];
    let match;
    
    while ((match = functionRegex.exec(content)) !== null) {
      functions.push({
        name: match[1],
        startLine: content.substring(0, match.index).split('\n').length,
        docstring: this.extractDocstring(content, match.index)
      });
    }
    
    return functions;
  }
  
  private extractClasses(content: string): Class[] {
    const classRegex = /(?:class|struct)\s+(\w+)/g;
    const classes: Class[] = [];
    let match;
    
    while ((match = classRegex.exec(content)) !== null) {
      classes.push({
        name: match[1],
        startLine: content.substring(0, match.index).split('\n').length,
        docstring: this.extractDocstring(content, match.index)
      });
    }
    
    return classes;
  }
  
  private extractComments(content: string): Comment[] {
    const commentRegex = /(?:\/\/|#|\/\*[\s\S]*?\*\/)\s*(.+)/g;
    const comments: Comment[] = [];
    let match;
    
    while ((match = commentRegex.exec(content)) !== null) {
      comments.push({
        text: match[1].trim(),
        line: content.substring(0, match.index).split('\n').length
      });
    }
    
    return comments;
  }
}
```

### Registering Custom Extractors

```typescript
// services/mermaidSourceAnalyzer.ts
export class MermaidSourceAnalyzer {
  private extractors: ContentExtractor[] = [];
  
  constructor() {
    // Register built-in extractors
    this.extractors.push(new MarkdownExtractor());
    this.extractors.push(new CodeFileExtractor());
    this.extractors.push(new PDFExtractor());
    this.extractors.push(new JSONExtractor());
  }
  
  registerExtractor(extractor: ContentExtractor): void {
    this.extractors.push(extractor);
  }
  
  private getExtractor(source: Source): ContentExtractor {
    const extractor = this.extractors.find(e => e.canHandle(source));
    return extractor || new DefaultExtractor();
  }
}
```

## Custom Concept Analyzers

### Base Concept Analyzer

```typescript
// analyzers/base/ConceptAnalyzer.ts
export abstract class ConceptAnalyzer {
  abstract analyzeConcepts(content: ExtractedContent): Promise<Concept[]>;
  abstract getConfidenceScore(concept: Concept, context: string): number;
}
```

### Example: Domain-Specific Analyzer

```typescript
// analyzers/APIDocumentationAnalyzer.ts
import { ConceptAnalyzer } from './base/ConceptAnalyzer';

export class APIDocumentationAnalyzer extends ConceptAnalyzer {
  private apiKeywords = ['endpoint', 'request', 'response', 'parameter', 'authentication'];
  private httpMethods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'];
  
  async analyzeConcepts(content: ExtractedContent): Promise<Concept[]> {
    const concepts: Concept[] = [];
    
    // Extract API endpoints
    const endpoints = this.extractEndpoints(content.text);
    endpoints.forEach(endpoint => {
      concepts.push({
        id: `endpoint_${endpoint.path.replace(/[^a-zA-Z0-9]/g, '_')}`,
        name: `${endpoint.method} ${endpoint.path}`,
        type: 'endpoint',
        sourceIds: [],
        confidence: 0.9
      });
    });
    
    // Extract data models
    const models = this.extractDataModels(content.structure);
    models.forEach(model => {
      concepts.push({
        id: `model_${model.name}`,
        name: model.name,
        type: 'entity',
        sourceIds: [],
        confidence: 0.8
      });
    });
    
    // Extract authentication methods
    const authMethods = this.extractAuthMethods(content.text);
    authMethods.forEach(method => {
      concepts.push({
        id: `auth_${method.type}`,
        name: `${method.type} Authentication`,
        type: 'process',
        sourceIds: [],
        confidence: 0.7
      });
    });
    
    return concepts;
  }
  
  getConfidenceScore(concept: Concept, context: string): number {
    let score = concept.confidence;
    
    // Boost score for API-specific terms
    if (this.apiKeywords.some(keyword => 
      context.toLowerCase().includes(keyword.toLowerCase())
    )) {
      score += 0.1;
    }
    
    // Boost score for HTTP methods
    if (this.httpMethods.some(method => 
      concept.name.includes(method)
    )) {
      score += 0.15;
    }
    
    return Math.min(1.0, score);
  }
  
  private extractEndpoints(text: string): APIEndpoint[] {
    const endpointRegex = /(GET|POST|PUT|DELETE|PATCH)\s+([\/\w\-\{\}]+)/g;
    const endpoints: APIEndpoint[] = [];
    let match;
    
    while ((match = endpointRegex.exec(text)) !== null) {
      endpoints.push({
        method: match[1],
        path: match[2]
      });
    }
    
    return endpoints;
  }
}
```

## Custom Relationship Mappers

### Base Relationship Mapper

```typescript
// mappers/base/RelationshipMapper.ts
export abstract class RelationshipMapper {
  abstract findRelationships(concepts: Concept[], content: ExtractedContent): Promise<Relationship[]>;
  abstract getRelationshipStrength(rel: Relationship, context: string): number;
}
```

### Example: Workflow Relationship Mapper

```typescript
// mappers/WorkflowRelationshipMapper.ts
import { RelationshipMapper } from './base/RelationshipMapper';

export class WorkflowRelationshipMapper extends RelationshipMapper {
  private sequenceWords = ['then', 'next', 'after', 'following', 'subsequently'];
  private conditionalWords = ['if', 'when', 'unless', 'provided', 'assuming'];
  
  async findRelationships(concepts: Concept[], content: ExtractedContent): Promise<Relationship[]> {
    const relationships: Relationship[] = [];
    
    // Find sequential relationships
    const sequentialRels = this.findSequentialRelationships(concepts, content.text);
    relationships.push(...sequentialRels);
    
    // Find conditional relationships
    const conditionalRels = this.findConditionalRelationships(concepts, content.text);
    relationships.push(...conditionalRels);
    
    // Find hierarchical relationships
    const hierarchicalRels = this.findHierarchicalRelationships(concepts, content.structure);
    relationships.push(...hierarchicalRels);
    
    return relationships;
  }
  
  getRelationshipStrength(rel: Relationship, context: string): number {
    let strength = 0.5; // Base strength
    
    // Increase strength for explicit sequence indicators
    if (this.sequenceWords.some(word => 
      context.toLowerCase().includes(word.toLowerCase())
    )) {
      strength += 0.3;
    }
    
    // Increase strength for conditional indicators
    if (this.conditionalWords.some(word => 
      context.toLowerCase().includes(word.toLowerCase())
    )) {
      strength += 0.2;
    }
    
    // Proximity bonus - concepts mentioned close together
    const fromIndex = context.indexOf(rel.from);
    const toIndex = context.indexOf(rel.to);
    if (fromIndex !== -1 && toIndex !== -1) {
      const distance = Math.abs(fromIndex - toIndex);
      if (distance < 100) { // Within 100 characters
        strength += 0.2;
      }
    }
    
    return Math.min(1.0, strength);
  }
  
  private findSequentialRelationships(concepts: Concept[], text: string): Relationship[] {
    const relationships: Relationship[] = [];
    
    // Look for patterns like "Step 1... then Step 2"
    const stepPattern = /step\s+(\d+)[^.]*?(?:then|next|after)\s+step\s+(\d+)/gi;
    let match;
    
    while ((match = stepPattern.exec(text)) !== null) {
      const fromStep = concepts.find(c => c.name.includes(`Step ${match[1]}`));
      const toStep = concepts.find(c => c.name.includes(`Step ${match[2]}`));
      
      if (fromStep && toStep) {
        relationships.push({
          from: fromStep.id,
          to: toStep.id,
          type: 'leads_to',
          strength: 0.8,
          sourceIds: []
        });
      }
    }
    
    return relationships;
  }
}
```

## Plugin System

### Plugin Interface

```typescript
// plugins/base/Plugin.ts
export interface MermaidSourcesPlugin {
  name: string;
  version: string;
  initialize(analyzer: MermaidSourceAnalyzer): void;
  cleanup?(): void;
}

export abstract class BasePlugin implements MermaidSourcesPlugin {
  abstract name: string;
  abstract version: string;
  
  abstract initialize(analyzer: MermaidSourceAnalyzer): void;
  
  cleanup(): void {
    // Default cleanup - override if needed
  }
}
```

### Example Plugin

```typescript
// plugins/GitIntegrationPlugin.ts
import { BasePlugin } from './base/Plugin';

export class GitIntegrationPlugin extends BasePlugin {
  name = 'git-integration';
  version = '1.0.0';
  
  initialize(analyzer: MermaidSourceAnalyzer): void {
    // Register Git-specific extractors
    analyzer.registerExtractor(new GitCommitExtractor());
    analyzer.registerExtractor(new GitBranchExtractor());
    
    // Register Git-specific analyzers
    analyzer.registerConceptAnalyzer(new GitWorkflowAnalyzer());
    
    // Register Git-specific relationship mappers
    analyzer.registerRelationshipMapper(new GitRelationshipMapper());
    
    // Register Git-specific diagram generator
    analyzer.registerGenerator('git-flow', new GitFlowDiagramGenerator());
  }
  
  cleanup(): void {
    // Clean up Git-specific resources
    console.log('Git Integration Plugin cleaned up');
  }
}
```

### Plugin Manager

```typescript
// services/PluginManager.ts
export class PluginManager {
  private plugins: Map<string, MermaidSourcesPlugin> = new Map();
  private analyzer: MermaidSourceAnalyzer;
  
  constructor(analyzer: MermaidSourceAnalyzer) {
    this.analyzer = analyzer;
  }
  
  registerPlugin(plugin: MermaidSourcesPlugin): void {
    if (this.plugins.has(plugin.name)) {
      console.warn(`Plugin ${plugin.name} is already registered`);
      return;
    }
    
    try {
      plugin.initialize(this.analyzer);
      this.plugins.set(plugin.name, plugin);
      console.log(`Plugin ${plugin.name} v${plugin.version} registered successfully`);
    } catch (error) {
      console.error(`Failed to register plugin ${plugin.name}:`, error);
    }
  }
  
  unregisterPlugin(pluginName: string): void {
    const plugin = this.plugins.get(pluginName);
    if (plugin) {
      plugin.cleanup?.();
      this.plugins.delete(pluginName);
      console.log(`Plugin ${pluginName} unregistered`);
    }
  }
  
  getPlugin(pluginName: string): MermaidSourcesPlugin | undefined {
    return this.plugins.get(pluginName);
  }
  
  listPlugins(): string[] {
    return Array.from(this.plugins.keys());
  }
}
```

## Configuration System

### Configuration Interface

```typescript
// config/MermaidSourcesConfig.ts
export interface MermaidSourcesConfig {
  analysis: AnalysisConfig;
  generation: GenerationConfig;
  performance: PerformanceConfig;
  plugins: PluginConfig[];
}

export interface AnalysisConfig {
  defaultMinConfidence: number;
  maxConceptsPerSource: number;
  enableMLAnalysis: boolean;
  customAnalyzers: string[];
}

export interface GenerationConfig {
  defaultDiagramType: DiagramType;
  maxNodesPerDiagram: number;
  enableInteractivity: boolean;
  colorSchemes: Record<string, ColorScheme>;
}

export interface PerformanceConfig {
  maxConcurrentAnalysis: number;
  analysisTimeout: number;
  enableCaching: boolean;
  cacheSize: number;
}

export interface PluginConfig {
  name: string;
  enabled: boolean;
  config: Record<string, any>;
}
```

### Configuration Manager

```typescript
// services/ConfigManager.ts
export class ConfigManager {
  private config: MermaidSourcesConfig;
  private listeners: ((config: MermaidSourcesConfig) => void)[] = [];
  
  constructor(initialConfig?: Partial<MermaidSourcesConfig>) {
    this.config = this.mergeWithDefaults(initialConfig || {});
  }
  
  getConfig(): MermaidSourcesConfig {
    return { ...this.config };
  }
  
  updateConfig(updates: Partial<MermaidSourcesConfig>): void {
    this.config = { ...this.config, ...updates };
    this.notifyListeners();
  }
  
  subscribe(listener: (config: MermaidSourcesConfig) => void): () => void {
    this.listeners.push(listener);
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }
  
  private notifyListeners(): void {
    this.listeners.forEach(listener => listener(this.config));
  }
  
  private mergeWithDefaults(config: Partial<MermaidSourcesConfig>): MermaidSourcesConfig {
    return {
      analysis: {
        defaultMinConfidence: 0.5,
        maxConceptsPerSource: 50,
        enableMLAnalysis: false,
        customAnalyzers: [],
        ...config.analysis
      },
      generation: {
        defaultDiagramType: 'flowchart',
        maxNodesPerDiagram: 100,
        enableInteractivity: true,
        colorSchemes: {
          default: { /* default colors */ },
          categorical: { /* categorical colors */ }
        },
        ...config.generation
      },
      performance: {
        maxConcurrentAnalysis: 3,
        analysisTimeout: 30000,
        enableCaching: true,
        cacheSize: 100,
        ...config.performance
      },
      plugins: config.plugins || []
    };
  }
}
```

## Testing Extensions

### Testing Custom Generators

```typescript
// tests/generators/CustomDiagramGenerator.test.ts
import { CustomDiagramGenerator } from '../../generators/CustomDiagramGenerator';
import { mockAnalysisResult } from '../mocks/analysisResults';

describe('CustomDiagramGenerator', () => {
  let generator: CustomDiagramGenerator;
  
  beforeEach(() => {
    generator = new CustomDiagramGenerator();
  });
  
  it('should generate valid Mermaid syntax', () => {
    const result = generator.generateDiagram(mockAnalysisResult, {
      layout: 'hierarchical',
      showMetrics: true,
      colorScheme: 'default',
      maxDepth: 3
    });
    
    expect(result).toMatch(/^graph TD\n/);
    expect(result).toContain('style');
  });
  
  it('should handle empty analysis results', () => {
    const emptyResult = { concepts: [], relationships: [], hierarchy: [], sourceMap: new Map() };
    const result = generator.generateDiagram(emptyResult, {});
    
    expect(result).toBe('graph TD\n');
  });
  
  it('should apply custom color schemes', () => {
    const result = generator.generateDiagram(mockAnalysisResult, {
      colorScheme: 'categorical'
    });
    
    expect(result).toContain('fill:#ff9999');
  });
});
```

### Integration Testing

```typescript
// tests/integration/PluginIntegration.test.ts
import { MermaidSourceAnalyzer } from '../../services/mermaidSourceAnalyzer';
import { PluginManager } from '../../services/PluginManager';
import { GitIntegrationPlugin } from '../../plugins/GitIntegrationPlugin';

describe('Plugin Integration', () => {
  let analyzer: MermaidSourceAnalyzer;
  let pluginManager: PluginManager;
  
  beforeEach(() => {
    analyzer = new MermaidSourceAnalyzer();
    pluginManager = new PluginManager(analyzer);
  });
  
  it('should register and initialize plugins correctly', () => {
    const plugin = new GitIntegrationPlugin();
    
    pluginManager.registerPlugin(plugin);
    
    expect(pluginManager.listPlugins()).toContain('git-integration');
    expect(analyzer.hasGenerator('git-flow')).toBe(true);
  });
  
  it('should handle plugin failures gracefully', () => {
    const faultyPlugin = {
      name: 'faulty-plugin',
      version: '1.0.0',
      initialize: () => { throw new Error('Plugin initialization failed'); }
    };
    
    expect(() => pluginManager.registerPlugin(faultyPlugin)).not.toThrow();
    expect(pluginManager.listPlugins()).not.toContain('faulty-plugin');
  });
});
```

## Best Practices

### Performance Optimization

1. **Lazy Loading**: Load analyzers and generators only when needed
2. **Caching**: Cache analysis results for unchanged sources
3. **Chunking**: Process large source sets in chunks
4. **Memory Management**: Clean up resources after analysis

### Error Handling

1. **Graceful Degradation**: Provide fallback behavior for failed analysis
2. **User Feedback**: Show meaningful error messages
3. **Logging**: Log detailed error information for debugging
4. **Recovery**: Allow users to retry failed operations

### Extensibility

1. **Plugin Architecture**: Use plugins for domain-specific functionality
2. **Configuration**: Make behavior configurable through settings
3. **Hooks**: Provide hooks for customizing behavior
4. **Documentation**: Document extension points clearly

### Testing

1. **Unit Tests**: Test individual components in isolation
2. **Integration Tests**: Test component interactions
3. **Performance Tests**: Test with large datasets
4. **User Experience Tests**: Test actual user workflows

This developer guide provides the foundation for extending the Mermaid-Sources Integration system. The modular architecture allows for easy customization while maintaining system stability and performance.