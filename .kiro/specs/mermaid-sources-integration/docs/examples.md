# Usage Examples

## Basic Diagram Generation

### Example 1: Simple Flowchart from Documentation

```tsx
import React, { useState } from 'react';
import { SourceDiagramGenerator } from '@/components/mermaid/SourceDiagramGenerator';
import { useSources } from '@/hooks/useSources';

function DocumentationFlowchart() {
  const { sources } = useSources();
  const [isGeneratorOpen, setIsGeneratorOpen] = useState(false);
  const [generatedDiagram, setGeneratedDiagram] = useState<SourceDiagram | null>(null);

  // Filter sources to documentation files
  const docSources = sources.filter(source => 
    source.type === 'file' && 
    (source.title.endsWith('.md') || source.title.includes('README'))
  );

  const handleDiagramGenerated = (diagram: SourceDiagram) => {
    setGeneratedDiagram(diagram);
    setIsGeneratorOpen(false);
    console.log('Generated flowchart with', diagram.analysis_metadata.concepts_extracted, 'concepts');
  };

  return (
    <div className="documentation-flowchart">
      <button 
        onClick={() => setIsGeneratorOpen(true)}
        disabled={docSources.length === 0}
      >
        Generate Documentation Flowchart ({docSources.length} sources)
      </button>

      <SourceDiagramGenerator
        sources={docSources}
        onDiagramGenerated={handleDiagramGenerated}
        onError={(error) => console.error('Generation failed:', error)}
        defaultDiagramType="flowchart"
        isOpen={isGeneratorOpen}
        onOpenChange={setIsGeneratorOpen}
      />

      {generatedDiagram && (
        <div className="generated-diagram">
          <h3>Generated Documentation Flow</h3>
          <InteractiveDiagramViewer
            code={generatedDiagram.code}
            sourceMap={generatedDiagram.interactive_metadata.node_source_map}
            onNodeClick={(nodeId, sourceIds) => {
              console.log('Clicked node:', nodeId, 'from sources:', sourceIds);
            }}
            showSourceAttribution={true}
          />
        </div>
      )}
    </div>
  );
}
```

### Example 2: Source Relationship Visualization

```tsx
import React from 'react';
import { SourceRelationshipDiagram } from '@/components/mermaid/SourceRelationshipDiagram';
import { useSources } from '@/hooks/useSources';

function ProjectRelationships() {
  const { sources, selectSource } = useSources();

  // Filter to project-related sources
  const projectSources = sources.filter(source => 
    source.tags?.includes('project') || 
    source.title.toLowerCase().includes('project')
  );

  const handleSourceSelect = (source: Source) => {
    selectSource(source.id);
    // Could also open source in a modal or navigate to it
  };

  return (
    <div className="project-relationships">
      <h2>Project Source Relationships</h2>
      <p>Showing relationships between {projectSources.length} project sources</p>
      
      <SourceRelationshipDiagram
        sources={projectSources}
        relationshipType="all"
        onSourceSelect={handleSourceSelect}
        showMetrics={true}
        layout="force"
      />
    </div>
  );
}
```

## Advanced Use Cases

### Example 3: Workflow Diagram from Process Documentation

```tsx
import React, { useState, useEffect } from 'react';
import { MermaidSourceAnalyzer } from '@/services/mermaidSourceAnalyzer';
import { InteractiveDiagramViewer } from '@/components/mermaid/InteractiveDiagramViewer';

function ProcessWorkflowGenerator({ processDocuments }: { processDocuments: Source[] }) {
  const [workflowDiagram, setWorkflowDiagram] = useState<string>('');
  const [sourceMap, setSourceMap] = useState<Map<string, string[]>>(new Map());
  const [isGenerating, setIsGenerating] = useState(false);

  const generateWorkflow = async () => {
    setIsGenerating(true);
    try {
      const analyzer = new MermaidSourceAnalyzer();
      
      // Analyze all process documents
      const analysis = await analyzer.analyzeSources(processDocuments);
      
      // Generate workflow-specific diagram
      const diagramCode = analyzer.generateDiagramCode(analysis, 'workflow');
      
      setWorkflowDiagram(diagramCode);
      setSourceMap(analysis.sourceMap);
    } catch (error) {
      console.error('Workflow generation failed:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  useEffect(() => {
    if (processDocuments.length > 0) {
      generateWorkflow();
    }
  }, [processDocuments]);

  const handleNodeClick = (nodeId: string, sourceIds: string[]) => {
    // Show process step details from source
    const relevantSources = processDocuments.filter(doc => 
      sourceIds.includes(doc.id)
    );
    
    console.log('Process step:', nodeId);
    console.log('Source documents:', relevantSources.map(s => s.title));
    
    // Could open a modal with step details
  };

  return (
    <div className="workflow-generator">
      <div className="workflow-header">
        <h3>Process Workflow</h3>
        <button onClick={generateWorkflow} disabled={isGenerating}>
          {isGenerating ? 'Generating...' : 'Regenerate Workflow'}
        </button>
      </div>

      {workflowDiagram && (
        <InteractiveDiagramViewer
          code={workflowDiagram}
          sourceMap={sourceMap}
          onNodeClick={handleNodeClick}
          onSourceView={(sourceId) => {
            const source = processDocuments.find(s => s.id === sourceId);
            if (source) {
              console.log('View source:', source.title);
              // Navigate to source or open in modal
            }
          }}
          showSourceAttribution={true}
        />
      )}
    </div>
  );
}
```

### Example 4: Interactive Knowledge Map

```tsx
import React, { useState } from 'react';
import { useMermaidSourceAnalysis } from '@/hooks/useMermaidSourceAnalysis';
import { SourceContentViewer } from '@/components/sources/SourceContentViewer';

function InteractiveKnowledgeMap({ knowledgeBaseSources }: { knowledgeBaseSources: Source[] }) {
  const { generateDiagram, isAnalyzing } = useMermaidSourceAnalysis();
  const [currentDiagram, setCurrentDiagram] = useState<SourceDiagram | null>(null);
  const [selectedSourceId, setSelectedSourceId] = useState<string | null>(null);
  const [selectedNode, setSelectedNode] = useState<string | null>(null);

  const generateKnowledgeMap = async () => {
    try {
      const diagram = await generateDiagram(knowledgeBaseSources, 'mindmap');
      setCurrentDiagram(diagram);
    } catch (error) {
      console.error('Knowledge map generation failed:', error);
    }
  };

  const handleNodeClick = (nodeId: string, sourceIds: string[]) => {
    setSelectedNode(nodeId);
    setSelectedSourceId(sourceIds[0]); // Show first source
  };

  const selectedSource = knowledgeBaseSources.find(s => s.id === selectedSourceId);

  return (
    <div className="knowledge-map-container">
      <div className="knowledge-map-header">
        <h2>Interactive Knowledge Map</h2>
        <button onClick={generateKnowledgeMap} disabled={isAnalyzing}>
          {isAnalyzing ? 'Generating Map...' : 'Generate Knowledge Map'}
        </button>
      </div>

      <div className="knowledge-map-content">
        <div className="diagram-panel">
          {currentDiagram && (
            <InteractiveDiagramViewer
              code={currentDiagram.code}
              sourceMap={currentDiagram.interactive_metadata.node_source_map}
              onNodeClick={handleNodeClick}
              highlightedNodes={selectedNode ? [selectedNode] : []}
              showSourceAttribution={true}
            />
          )}
        </div>

        <div className="source-panel">
          {selectedSource && (
            <SourceContentViewer
              source={selectedSource}
              highlightedConcept={selectedNode}
              onClose={() => setSelectedSourceId(null)}
            />
          )}
        </div>
      </div>
    </div>
  );
}
```

## Integration Examples

### Example 5: Chat Integration

```tsx
import React from 'react';
import { ChatArea } from '@/components/chat/ChatArea';
import { SourceDiagramGenerator } from '@/components/mermaid/SourceDiagramGenerator';

function ChatWithDiagramGeneration() {
  const [showDiagramGenerator, setShowDiagramGenerator] = useState(false);
  const [chatSources, setChatSources] = useState<Source[]>([]);

  const handleDiagramSuggestion = (sources: Source[]) => {
    setChatSources(sources);
    setShowDiagramGenerator(true);
  };

  const handleDiagramGenerated = (diagram: SourceDiagram) => {
    // Insert diagram into chat
    console.log('Inserting diagram into chat:', diagram.title);
    setShowDiagramGenerator(false);
  };

  return (
    <div className="chat-with-diagrams">
      <ChatArea
        onDiagramSuggestion={handleDiagramSuggestion}
        // Other chat props
      />

      <SourceDiagramGenerator
        sources={chatSources}
        onDiagramGenerated={handleDiagramGenerated}
        onError={(error) => console.error('Chat diagram generation failed:', error)}
        isOpen={showDiagramGenerator}
        onOpenChange={setShowDiagramGenerator}
      />
    </div>
  );
}
```

### Example 6: Bulk Diagram Management

```tsx
import React, { useState } from 'react';
import { SourceManagerDialog } from '@/components/sources/SourceManagerDialog';
import { SourceDiagramService } from '@/services/sourceDiagramService';

function BulkDiagramManager() {
  const [selectedSources, setSelectedSources] = useState<Source[]>([]);
  const [generatedDiagrams, setGeneratedDiagrams] = useState<SourceDiagram[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);

  const generateBulkDiagrams = async () => {
    setIsGenerating(true);
    const service = new SourceDiagramService();
    const diagrams: SourceDiagram[] = [];

    try {
      // Group sources by type/topic for different diagram types
      const docSources = selectedSources.filter(s => s.type === 'document');
      const codeSources = selectedSources.filter(s => s.type === 'code');
      const processSources = selectedSources.filter(s => 
        s.tags?.includes('process') || s.title.toLowerCase().includes('process')
      );

      // Generate different diagram types
      if (docSources.length > 0) {
        const analyzer = new MermaidSourceAnalyzer();
        const analysis = await analyzer.analyzeSources(docSources);
        const diagramCode = analyzer.generateDiagramCode(analysis, 'mindmap');
        
        const diagram = await service.saveDiagramWithSources({
          title: 'Documentation Overview',
          code: diagramCode,
          type: 'mindmap'
        }, docSources);
        
        diagrams.push(diagram);
      }

      if (processSources.length > 0) {
        const analyzer = new MermaidSourceAnalyzer();
        const analysis = await analyzer.analyzeSources(processSources);
        const diagramCode = analyzer.generateDiagramCode(analysis, 'workflow');
        
        const diagram = await service.saveDiagramWithSources({
          title: 'Process Workflow',
          code: diagramCode,
          type: 'flowchart'
        }, processSources);
        
        diagrams.push(diagram);
      }

      setGeneratedDiagrams(diagrams);
    } catch (error) {
      console.error('Bulk diagram generation failed:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="bulk-diagram-manager">
      <h3>Bulk Diagram Generation</h3>
      
      <div className="source-selection">
        <p>Selected Sources: {selectedSources.length}</p>
        <button onClick={generateBulkDiagrams} disabled={isGenerating || selectedSources.length === 0}>
          {isGenerating ? 'Generating Diagrams...' : 'Generate All Diagram Types'}
        </button>
      </div>

      <div className="generated-diagrams">
        {generatedDiagrams.map((diagram, index) => (
          <div key={index} className="diagram-preview">
            <h4>{diagram.title}</h4>
            <p>Sources: {diagram.source_ids.length}</p>
            <p>Concepts: {diagram.analysis_metadata.concepts_extracted}</p>
            <InteractiveDiagramViewer
              code={diagram.code}
              sourceMap={diagram.interactive_metadata.node_source_map}
              onNodeClick={(nodeId, sourceIds) => {
                console.log('Bulk diagram node clicked:', nodeId);
              }}
            />
          </div>
        ))}
      </div>
    </div>
  );
}
```

## Sample Diagram Types

### Flowchart Example

Generated from API documentation sources:

```mermaid
flowchart TD
    A[API Request] --> B{Authentication}
    B -->|Valid| C[Process Request]
    B -->|Invalid| D[Return 401]
    C --> E{Validate Input}
    E -->|Valid| F[Execute Operation]
    E -->|Invalid| G[Return 400]
    F --> H[Return Response]
    
    click A "source:api-overview.md"
    click B "source:auth-guide.md"
    click C "source:request-processing.md"
    click F "source:operations.md"
```

### Mindmap Example

Generated from project documentation:

```mermaid
mindmap
  root((Project))
    Architecture
      Frontend
        React Components
        State Management
      Backend
        API Layer
        Database
    Documentation
      User Guide
      API Reference
      Developer Docs
    Testing
      Unit Tests
      Integration Tests
      E2E Tests
```

### Relationship Diagram Example

Generated from source relationships:

```mermaid
graph LR
    A[Requirements Doc] --> B[Design Doc]
    B --> C[Implementation]
    C --> D[Tests]
    A --> E[User Stories]
    E --> F[Acceptance Criteria]
    F --> D
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
```

## Performance Examples

### Example 7: Optimized Large Dataset Processing

```tsx
import React, { useState, useCallback } from 'react';
import { useMemo } from 'react';

function OptimizedLargeDatasetDiagrams({ sources }: { sources: Source[] }) {
  const [processingChunk, setProcessingChunk] = useState(0);
  const [chunkSize] = useState(50); // Process 50 sources at a time
  
  // Memoize source chunks to avoid recalculation
  const sourceChunks = useMemo(() => {
    const chunks: Source[][] = [];
    for (let i = 0; i < sources.length; i += chunkSize) {
      chunks.push(sources.slice(i, i + chunkSize));
    }
    return chunks;
  }, [sources, chunkSize]);

  const processNextChunk = useCallback(async () => {
    if (processingChunk < sourceChunks.length) {
      const chunk = sourceChunks[processingChunk];
      
      // Process chunk with progress tracking
      console.log(`Processing chunk ${processingChunk + 1}/${sourceChunks.length}`);
      
      const analyzer = new MermaidSourceAnalyzer();
      const analysis = await analyzer.analyzeSources(chunk);
      
      // Update progress
      setProcessingChunk(prev => prev + 1);
      
      return analysis;
    }
  }, [processingChunk, sourceChunks]);

  return (
    <div className="optimized-processing">
      <h3>Large Dataset Processing</h3>
      <p>Total sources: {sources.length}</p>
      <p>Chunks: {sourceChunks.length}</p>
      <p>Progress: {processingChunk}/{sourceChunks.length}</p>
      
      <button onClick={processNextChunk} disabled={processingChunk >= sourceChunks.length}>
        Process Next Chunk
      </button>
    </div>
  );
}
```

These examples demonstrate the full range of capabilities provided by the Mermaid-Sources Integration feature, from basic diagram generation to advanced interactive knowledge exploration.