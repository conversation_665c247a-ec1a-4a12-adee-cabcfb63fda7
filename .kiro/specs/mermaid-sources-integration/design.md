# Design Document

## Overview

The Mermaid-Sources Integration feature will bridge the existing Mermaid diagram system with the sources management system in lighthouse-lm. This integration will enable automatic diagram generation from source content, visual representation of source relationships, and interactive exploration of knowledge through diagrams.

The design leverages the existing robust infrastructure including the `MermaidService`, `useMermaidDiagram` hook, and sources management system, while adding new components and utilities specifically for source-based diagram generation.

## Architecture

### Component Architecture

```mermaid
graph TD
    A[SourcesSidebar] --> B[SourceDiagramGenerator]
    B --> C[MermaidSourceAnalyzer]
    C --> D[ContentExtractor]
    C --> E[RelationshipMapper]
    B --> F[Mermaid Component]
    F --> G[InteractiveDiagramViewer]
    G --> H[SourceContentViewer]
    
    I[SourceManagerDialog] --> B
    J[ChatArea] --> K[DiagramFromSourcesButton]
    K --> B
    
    L[MermaidService] --> M[SourceDiagramService]
    M --> N[DiagramPersistence]
    M --> O[SourceMetadataTracker]
```

### Data Flow

1. **Source Selection**: User selects sources from SourcesSidebar or SourceManagerDialog
2. **Content Analysis**: MermaidSourceAnalyzer processes source content to extract concepts and relationships
3. **Diagram Generation**: Based on analysis, appropriate Mermaid diagram code is generated
4. **Rendering**: Existing Mermaid component renders the diagram with enhanced interactivity
5. **Persistence**: Diagram is saved with source metadata for future reference
6. **Interaction**: Users can click diagram elements to view corresponding source content

## Components and Interfaces

### New Components

#### 1. SourceDiagramGenerator
```typescript
interface SourceDiagramGeneratorProps {
  sources: Source[];
  onDiagramGenerated: (diagram: SourceDiagram) => void;
  onError: (error: string) => void;
  defaultDiagramType?: DiagramType;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}
```

**Purpose**: Main dialog component for generating diagrams from sources
**Features**: 
- Source selection interface
- Diagram type selection (flowchart, mindmap, relationship)
- Generation options (max concepts, relationship depth)
- Preview and refinement capabilities

#### 2. MermaidSourceAnalyzer
```typescript
interface SourceAnalysisResult {
  concepts: Concept[];
  relationships: Relationship[];
  hierarchy: HierarchyNode[];
  sourceMap: Map<string, string[]>; // concept -> source IDs
}

interface Concept {
  id: string;
  name: string;
  type: 'entity' | 'process' | 'decision' | 'outcome';
  sourceIds: string[];
  confidence: number;
}

interface Relationship {
  from: string;
  to: string;
  type: 'depends_on' | 'leads_to' | 'contains' | 'references';
  strength: number;
  sourceIds: string[];
}
```

**Purpose**: Analyzes source content to extract concepts and relationships
**Features**:
- Natural language processing for concept extraction
- Relationship detection between concepts
- Hierarchical structure identification
- Source attribution tracking

#### 3. InteractiveDiagramViewer
```typescript
interface InteractiveDiagramViewerProps extends MermaidProps {
  sourceMap: Map<string, string[]>;
  onNodeClick: (nodeId: string, sourceIds: string[]) => void;
  onSourceView: (sourceId: string) => void;
  highlightedNodes?: string[];
}
```

**Purpose**: Enhanced Mermaid component with source interaction capabilities
**Features**:
- Clickable diagram nodes
- Source highlighting
- Context menus for diagram elements
- Source attribution display

#### 4. SourceRelationshipDiagram
```typescript
interface SourceRelationshipDiagramProps {
  sources: Source[];
  relationshipType: 'similarity' | 'references' | 'topics' | 'all';
  onSourceSelect: (source: Source) => void;
  showMetrics?: boolean;
}
```

**Purpose**: Specialized component for visualizing relationships between sources
**Features**:
- Source similarity visualization
- Reference mapping
- Topic clustering
- Interactive source exploration

### Enhanced Existing Components

#### SourcesSidebar Enhancements
- Add "Generate Diagram" button when multiple sources are selected
- Add diagram history section showing previously generated diagrams
- Quick actions for common diagram types

#### Mermaid Component Enhancements
- Add source attribution display
- Implement node click handlers for source navigation
- Add source highlighting capabilities
- Enhanced export with source metadata

## Data Models

### SourceDiagram
```typescript
interface SourceDiagram extends MermaidDiagram {
  source_ids: string[];
  analysis_metadata: {
    concepts_extracted: number;
    relationships_found: number;
    analysis_confidence: number;
    generation_parameters: DiagramGenerationOptions;
  };
  interactive_metadata: {
    node_source_map: Record<string, string[]>;
    clickable_nodes: string[];
    source_attribution: SourceAttribution[];
  };
}

interface SourceAttribution {
  node_id: string;
  source_id: string;
  content_excerpt: string;
  confidence: number;
}

interface DiagramGenerationOptions {
  diagram_type: DiagramType;
  max_concepts: number;
  relationship_depth: number;
  include_metadata: boolean;
  filter_by_relevance: boolean;
  minimum_confidence: number;
}
```

### ContentAnalysis
```typescript
interface ContentAnalysis {
  source_id: string;
  concepts: ExtractedConcept[];
  relationships: ExtractedRelationship[];
  structure: ContentStructure;
  metadata: AnalysisMetadata;
}

interface ExtractedConcept {
  id: string;
  text: string;
  type: ConceptType;
  position: TextPosition;
  confidence: number;
  context: string;
}

interface ExtractedRelationship {
  from_concept: string;
  to_concept: string;
  relationship_type: RelationshipType;
  evidence: string;
  confidence: number;
}
```

## Error Handling

### Content Analysis Errors
- **Empty Sources**: Graceful handling when sources have no extractable content
- **Analysis Failures**: Fallback to simple structure-based diagrams
- **Timeout Handling**: Progress indicators and cancellation for long-running analysis

### Diagram Generation Errors
- **Invalid Mermaid Syntax**: Automatic syntax correction and validation
- **Complex Diagrams**: Automatic simplification when diagrams become too complex
- **Memory Constraints**: Chunking and pagination for large source sets

### Source Integration Errors
- **Missing Sources**: Handle cases where referenced sources are deleted
- **Permission Issues**: Graceful degradation when source access is restricted
- **Sync Issues**: Conflict resolution when sources are updated during diagram generation

## Testing Strategy

### Unit Tests
- **MermaidSourceAnalyzer**: Test concept extraction accuracy with various content types
- **ContentExtractor**: Validate text processing and structure detection
- **RelationshipMapper**: Test relationship detection algorithms
- **DiagramGenerator**: Verify Mermaid syntax generation for different scenarios

### Integration Tests
- **Source-to-Diagram Flow**: End-to-end testing of the complete generation workflow
- **Interactive Features**: Test node clicking and source navigation
- **Persistence**: Verify diagram saving and retrieval with source metadata
- **Export Functionality**: Test diagram export with source attribution

### Performance Tests
- **Large Source Sets**: Test performance with 100+ sources
- **Complex Content**: Analyze processing time for lengthy documents
- **Memory Usage**: Monitor memory consumption during analysis
- **Concurrent Operations**: Test multiple simultaneous diagram generations

### User Experience Tests
- **Diagram Clarity**: Ensure generated diagrams are readable and meaningful
- **Interaction Responsiveness**: Test click responsiveness and navigation speed
- **Error Recovery**: Validate user experience during error conditions
- **Accessibility**: Ensure diagrams and interactions are accessible

## Implementation Phases

### Phase 1: Core Analysis Engine
- Implement MermaidSourceAnalyzer
- Create ContentExtractor utilities
- Build basic concept and relationship detection
- Add unit tests for analysis components

### Phase 2: Diagram Generation
- Extend MermaidGenerator for source-based generation
- Implement SourceDiagramService
- Create diagram generation algorithms for different types
- Add persistence layer for source diagrams

### Phase 3: Interactive Components
- Build SourceDiagramGenerator dialog
- Enhance Mermaid component with interactivity
- Implement InteractiveDiagramViewer
- Add source navigation capabilities

### Phase 4: UI Integration
- Integrate with SourcesSidebar
- Add diagram management features
- Implement export enhancements
- Create source relationship visualizations

### Phase 5: Polish and Optimization
- Performance optimization for large datasets
- Enhanced error handling and user feedback
- Advanced analysis features (ML-based concept extraction)
- Comprehensive testing and bug fixes