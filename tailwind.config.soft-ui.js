/** @type {import('tailwindcss').Config} */
module.exports = {
  theme: {
    extend: {
      // Soft UI shadow presets
      boxShadow: {
        'soft': '0 8px 32px 0 rgba(31, 38, 135, 0.15)',
        'soft-hover': '0 8px 40px 0 rgba(31, 38, 135, 0.25)',
        'soft-active': '0 4px 20px 0 rgba(31, 38, 135, 0.2)',
        'soft-primary': '0 8px 25px -5px rgba(102, 126, 234, 0.4)',
        'soft-success': '0 8px 25px -5px rgba(102, 217, 160, 0.4)',
        'soft-warning': '0 8px 25px -5px rgba(255, 181, 71, 0.4)',
        'soft-danger': '0 8px 25px -5px rgba(255, 107, 107, 0.4)',
        'soft-gradient': '0 8px 25px -5px rgba(236, 72, 153, 0.4)',
        'soft-inset': 'inset 0 2px 4px 0 rgba(31, 38, 135, 0.06)',
        'soft-xl': '0 20px 50px -12px rgba(31, 38, 135, 0.25)',
        'soft-2xl': '0 25px 60px -15px rgba(31, 38, 135, 0.3)',
        // Neumorphic shadows
        'neumorphic-up': '20px 20px 60px #d1d1d1, -20px -20px 60px #ffffff',
        'neumorphic-down': 'inset 20px 20px 60px #d1d1d1, inset -20px -20px 60px #ffffff',
        'neumorphic-flat': '5px 5px 10px #d1d1d1, -5px -5px 10px #ffffff',
        'neumorphic-concave': 'inset 5px 5px 10px #d1d1d1, inset -5px -5px 10px #ffffff',
        // Glass morphism shadows
        'glass': '0 8px 32px 0 rgba(255, 255, 255, 0.1)',
        'glass-hover': '0 8px 40px 0 rgba(255, 255, 255, 0.15)',
      },
      
      // Gradient backgrounds
      backgroundImage: {
        // Soft gradients
        'gradient-soft': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        'gradient-peach': 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
        'gradient-lavender': 'linear-gradient(135deg, #e9d5ff 0%, #c084fc 100%)',
        'gradient-mint': 'linear-gradient(135deg, #b2f5ea 0%, #5eead4 100%)',
        'gradient-sky': 'linear-gradient(135deg, #bfdbfe 0%, #60a5fa 100%)',
        'gradient-sunset': 'linear-gradient(135deg, #ff6b6b 0%, #feca57 100%)',
        'gradient-ocean': 'linear-gradient(135deg, #3b82f6 0%, #06b6d4 100%)',
        'gradient-forest': 'linear-gradient(135deg, #10b981 0%, #84cc16 100%)',
        'gradient-berry': 'linear-gradient(135deg, #ec4899 0%, #8b5cf6 100%)',
        // Glass effect gradients
        'gradient-glass': 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)',
        'gradient-frosted': 'linear-gradient(135deg, rgba(255,255,255,0.25) 0%, rgba(255,255,255,0.1) 100%)',
        'gradient-crystal': 'linear-gradient(135deg, rgba(255,255,255,0.4) 0%, rgba(255,255,255,0.2) 100%)',
        // Mesh gradients
        'gradient-mesh': `
          radial-gradient(at 40% 20%, hsla(280, 100%, 74%, 0.3) 0px, transparent 50%),
          radial-gradient(at 80% 0%, hsla(189, 100%, 56%, 0.3) 0px, transparent 50%),
          radial-gradient(at 0% 50%, hsla(355, 100%, 93%, 0.3) 0px, transparent 50%),
          radial-gradient(at 80% 50%, hsla(340, 100%, 76%, 0.3) 0px, transparent 50%),
          radial-gradient(at 0% 100%, hsla(22, 100%, 77%, 0.3) 0px, transparent 50%),
          radial-gradient(at 80% 100%, hsla(242, 100%, 70%, 0.3) 0px, transparent 50%),
          radial-gradient(at 0% 0%, hsla(343, 100%, 76%, 0.3) 0px, transparent 50%)
        `,
      },
      
      // Backdrop filters
      backdropBlur: {
        xs: '2px',
        '3xl': '64px',
      },
      
      // Custom animations
      animation: {
        'float': 'float 3s ease-in-out infinite',
        'glow': 'glow 2s ease-in-out infinite',
        'gradient-shift': 'gradient-shift 5s ease infinite',
        'shimmer': 'shimmer 2s linear infinite',
        'pulse-soft': 'pulse-soft 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'bounce-soft': 'bounce-soft 1s infinite',
        'spin-slow': 'spin 3s linear infinite',
        'morph': 'morph 8s ease-in-out infinite',
        'wave': 'wave 2s linear infinite',
        'fade-in': 'fade-in 0.5s ease-out',
        'fade-out': 'fade-out 0.5s ease-out',
        'slide-up': 'slide-up 0.5s ease-out',
        'slide-down': 'slide-down 0.5s ease-out',
        'scale-in': 'scale-in 0.3s ease-out',
      },
      
      // Keyframes
      keyframes: {
        float: {
          '0%, 100%': { transform: 'translateY(0) translateZ(0)' },
          '50%': { transform: 'translateY(-10px) translateZ(0)' },
        },
        glow: {
          '0%, 100%': { boxShadow: '0 8px 32px 0 rgba(31, 38, 135, 0.15)' },
          '50%': { boxShadow: '0 8px 40px 0 rgba(31, 38, 135, 0.35)' },
        },
        'gradient-shift': {
          '0%, 100%': { backgroundPosition: '0% 50%' },
          '50%': { backgroundPosition: '100% 50%' },
        },
        shimmer: {
          '0%': { backgroundPosition: '-200% 0' },
          '100%': { backgroundPosition: '200% 0' },
        },
        'pulse-soft': {
          '0%, 100%': { opacity: 1 },
          '50%': { opacity: 0.8 },
        },
        'bounce-soft': {
          '0%, 100%': {
            transform: 'translateY(-5%)',
            animationTimingFunction: 'cubic-bezier(0.8, 0, 1, 1)',
          },
          '50%': {
            transform: 'translateY(0)',
            animationTimingFunction: 'cubic-bezier(0, 0, 0.2, 1)',
          },
        },
        morph: {
          '0%': { borderRadius: '60% 40% 30% 70% / 60% 30% 70% 40%' },
          '50%': { borderRadius: '30% 60% 70% 40% / 50% 60% 30% 60%' },
          '100%': { borderRadius: '60% 40% 30% 70% / 60% 30% 70% 40%' },
        },
        wave: {
          '0%': { transform: 'rotate(0deg)' },
          '10%': { transform: 'rotate(14deg)' },
          '20%': { transform: 'rotate(-8deg)' },
          '30%': { transform: 'rotate(14deg)' },
          '40%': { transform: 'rotate(-4deg)' },
          '50%': { transform: 'rotate(10deg)' },
          '60%': { transform: 'rotate(0deg)' },
          '100%': { transform: 'rotate(0deg)' },
        },
        'fade-in': {
          '0%': { opacity: 0 },
          '100%': { opacity: 1 },
        },
        'fade-out': {
          '0%': { opacity: 1 },
          '100%': { opacity: 0 },
        },
        'slide-up': {
          '0%': { transform: 'translateY(100%) translateZ(0)' },
          '100%': { transform: 'translateY(0) translateZ(0)' },
        },
        'slide-down': {
          '0%': { transform: 'translateY(-100%) translateZ(0)' },
          '100%': { transform: 'translateY(0) translateZ(0)' },
        },
        'scale-in': {
          '0%': { transform: 'scale(0.95) translateZ(0)' },
          '100%': { transform: 'scale(1) translateZ(0)' },
        },
      },
      
      // Custom colors for soft UI
      colors: {
        soft: {
          50: '#f8fafc',
          100: '#f1f5f9',
          200: '#e2e8f0',
          300: '#cbd5e1',
          400: '#94a3b8',
          500: '#64748b',
          600: '#475569',
          700: '#334155',
          800: '#1e293b',
          900: '#0f172a',
        },
        glass: {
          white: 'rgba(255, 255, 255, 0.1)',
          'white-50': 'rgba(255, 255, 255, 0.05)',
          'white-100': 'rgba(255, 255, 255, 0.1)',
          'white-200': 'rgba(255, 255, 255, 0.2)',
          'white-300': 'rgba(255, 255, 255, 0.3)',
          'white-400': 'rgba(255, 255, 255, 0.4)',
          'white-500': 'rgba(255, 255, 255, 0.5)',
          'white-600': 'rgba(255, 255, 255, 0.6)',
          'white-700': 'rgba(255, 255, 255, 0.7)',
          'white-800': 'rgba(255, 255, 255, 0.8)',
          'white-900': 'rgba(255, 255, 255, 0.9)',
        }
      },
      
      // Transition timing functions
      transitionTimingFunction: {
        'bounce-in': 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
        'smooth': 'cubic-bezier(0.23, 1, 0.32, 1)',
        'smooth-out': 'cubic-bezier(0, 0, 0.2, 1)',
      },
      
      // Border radius
      borderRadius: {
        'soft': '1rem',
        'soft-lg': '1.5rem',
        'soft-xl': '2rem',
      },
      
      // Spacing
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
        '120': '30rem',
      },
    },
  },
  plugins: [
    // Custom plugin for soft UI utilities
    function({ addUtilities }) {
      const newUtilities = {
        // Glass morphism utilities
        '.glass': {
          background: 'rgba(255, 255, 255, 0.25)',
          backdropFilter: 'blur(20px) saturate(200%)',
          WebkitBackdropFilter: 'blur(20px) saturate(200%)',
          border: '1px solid rgba(255, 255, 255, 0.18)',
        },
        '.glass-dark': {
          background: 'rgba(0, 0, 0, 0.25)',
          backdropFilter: 'blur(20px) saturate(200%)',
          WebkitBackdropFilter: 'blur(20px) saturate(200%)',
          border: '1px solid rgba(255, 255, 255, 0.08)',
        },
        // Soft gradient backgrounds
        '.bg-soft-gradient': {
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          backgroundSize: '200% 200%',
          animation: 'gradient-shift 5s ease infinite',
        },
        // Neumorphic effects
        '.neumorphic': {
          background: '#f0f0f0',
          boxShadow: '20px 20px 60px #d1d1d1, -20px -20px 60px #ffffff',
        },
        '.neumorphic-pressed': {
          boxShadow: 'inset 20px 20px 60px #d1d1d1, inset -20px -20px 60px #ffffff',
        },
        // Soft glow effects
        '.soft-glow': {
          boxShadow: '0 0 20px rgba(102, 126, 234, 0.3)',
        },
        // Smooth transitions
        '.transition-soft': {
          transition: 'all 0.3s cubic-bezier(0.23, 1, 0.32, 1)',
        },
        // Frosted glass effect
        '.frosted': {
          background: 'rgba(255, 255, 255, 0.9)',
          backdropFilter: 'blur(10px)',
          WebkitBackdropFilter: 'blur(10px)',
        },
        // Soft border
        '.border-soft': {
          border: '1px solid rgba(255, 255, 255, 0.2)',
        },
        // Performance optimizations
        '.gpu-accelerated': {
          transform: 'translateZ(0)',
          willChange: 'transform',
        },
        '.backface-hidden': {
          backfaceVisibility: 'hidden',
        },
      }

      addUtilities(newUtilities)
    },
  ],
};