# Python Data Center Engine Requirements
# Core data processing
pandas>=2.0.0
numpy>=1.24.0
polars>=0.20.0
pyarrow>=14.0.0
dask[complete]>=2023.5.0
modin[ray]>=0.23.0

# Machine Learning
scikit-learn>=1.3.0
lightgbm>=4.0.0
xgboost>=2.0.0
joblib>=1.3.0

# Deep Learning (optional - comment out if no GPU)
# torch>=2.0.0
# tensorflow>=2.15.0
# transformers>=4.30.0

# Time Series
prophet>=1.1.0

# Data Quality & Profiling
great-expectations>=0.17.0
pandas-profiling>=3.6.0

# API & Web
fastapi>=0.100.0
uvicorn>=0.23.0
httpx>=0.24.0
pydantic>=2.0.0

# Database connectors
sqlalchemy>=2.0.0
psycopg2-binary>=2.9.0  # PostgreSQL
pymongo>=4.4.0  # MongoDB

# Utilities
python-dotenv>=1.0.0
click>=8.1.0
tqdm>=4.65.0

# Data synthesis (optional)
# ctgan>=0.7.0  # For synthetic data generation
# imblearn>=0.10.0  # For SMOTE

# Model interpretability (optional)
# shap>=0.42.0
# lime>=0.2.0

# Optimization (optional)
# optuna>=3.2.0