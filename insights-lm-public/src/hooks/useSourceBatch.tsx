import { useState, useCallback } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { useToast } from './use-toast';

export const useSourceBatch = (sources: any[]) => {
  const [selectedSources, setSelectedSources] = useState<Set<string>>(new Set());
  const [isProcessing, setIsProcessing] = useState(false);
  const { toast } = useToast();

  const toggleSource = useCallback((sourceId: string) => {
    setSelectedSources(prev => {
      const newSet = new Set(prev);
      if (newSet.has(sourceId)) {
        newSet.delete(sourceId);
      } else {
        newSet.add(sourceId);
      }
      return newSet;
    });
  }, []);

  const selectAll = useCallback(() => {
    setSelectedSources(new Set(sources.map(s => s.id)));
  }, [sources]);

  const clearSelection = useCallback(() => {
    setSelectedSources(new Set());
  }, []);

  const batchDelete = useCallback(async () => {
    if (selectedSources.size === 0) return;
    
    setIsProcessing(true);
    try {
      const sourceIds = Array.from(selectedSources);
      await invoke('batch_delete_sources', { sourceIds });
      
      toast({
        title: 'Sources Deleted',
        description: `Successfully deleted ${sourceIds.length} sources`,
      });
      
      clearSelection();
    } catch (error) {
      console.error('Batch delete error:', error);
      toast({
        title: 'Delete Failed',
        description: 'Failed to delete sources',
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
    }
  }, [selectedSources, toast, clearSelection]);

  const batchArchive = useCallback(async () => {
    if (selectedSources.size === 0) return;
    
    setIsProcessing(true);
    try {
      const sourceIds = Array.from(selectedSources);
      await invoke('batch_archive_sources', { sourceIds });
      
      toast({
        title: 'Sources Archived',
        description: `Successfully archived ${sourceIds.length} sources`,
      });
      
      clearSelection();
    } catch (error) {
      console.error('Batch archive error:', error);
      toast({
        title: 'Archive Failed',
        description: 'Failed to archive sources',
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
    }
  }, [selectedSources, toast, clearSelection]);

  const batchUpdatePriority = useCallback(async (priority: string) => {
    if (selectedSources.size === 0) return;
    
    setIsProcessing(true);
    try {
      const sourceIds = Array.from(selectedSources);
      await invoke('batch_update_priority', { sourceIds, priority });
      
      toast({
        title: 'Priority Updated',
        description: `Updated priority for ${sourceIds.length} sources`,
      });
      
      clearSelection();
    } catch (error) {
      console.error('Batch priority update error:', error);
      toast({
        title: 'Update Failed',
        description: 'Failed to update priority',
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
    }
  }, [selectedSources, toast, clearSelection]);

  const batchAssignTags = useCallback(async (tagIds: string[]) => {
    if (selectedSources.size === 0 || tagIds.length === 0) return;
    
    setIsProcessing(true);
    try {
      const sourceIds = Array.from(selectedSources);
      
      // Assign tags to each selected source
      for (const sourceId of sourceIds) {
        for (const tagId of tagIds) {
          await invoke('assign_tag_to_source', { sourceId, tagId });
        }
      }
      
      toast({
        title: 'Tags Assigned',
        description: `Assigned ${tagIds.length} tags to ${sourceIds.length} sources`,
      });
      
      clearSelection();
    } catch (error) {
      console.error('Batch tag assignment error:', error);
      toast({
        title: 'Assignment Failed',
        description: 'Failed to assign tags',
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
    }
  }, [selectedSources, toast, clearSelection]);

  const exportSources = useCallback(async () => {
    if (selectedSources.size === 0) return;
    
    try {
      const selectedSourceData = sources.filter(s => selectedSources.has(s.id));
      const exportData = {
        exportDate: new Date().toISOString(),
        sources: selectedSourceData,
        count: selectedSourceData.length
      };
      
      // Create a blob and download
      const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `sources-export-${Date.now()}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      toast({
        title: 'Export Complete',
        description: `Exported ${selectedSourceData.length} sources`,
      });
    } catch (error) {
      console.error('Export error:', error);
      toast({
        title: 'Export Failed',
        description: 'Failed to export sources',
        variant: 'destructive',
      });
    }
  }, [selectedSources, sources, toast]);

  return {
    selectedSources,
    toggleSource,
    selectAll,
    clearSelection,
    batchDelete,
    batchArchive,
    batchUpdatePriority,
    batchAssignTags,
    exportSources,
    isProcessing
  };
};