import { useState, useEffect, useCallback } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { useToast } from './use-toast';

export interface SourceVersion {
  id: string;
  source_id: string;
  version_number: number;
  title: string;
  content: string;
  metadata: any;
  changed_by: string;
  change_summary?: string;
  created_at: string;
}

export const useSourceVersions = (sourceId?: string) => {
  const [versions, setVersions] = useState<SourceVersion[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const fetchVersions = useCallback(async () => {
    if (!sourceId) return;
    
    setIsLoading(true);
    try {
      const result = await invoke<SourceVersion[]>('get_source_versions', {
        sourceId
      });
      setVersions(result);
    } catch (error) {
      console.error('Error fetching versions:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch version history',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  }, [sourceId, toast]);

  useEffect(() => {
    fetchVersions();
  }, [fetchVersions]);

  const createVersion = useCallback(async (changeSummary?: string) => {
    if (!sourceId) return;
    
    try {
      const newVersion = await invoke<SourceVersion>('create_source_version', {
        sourceId,
        changedBy: 'current_user',
        changeSummary
      });
      
      setVersions(prev => [...prev, newVersion]);
      
      toast({
        title: 'Version Created',
        description: `Created version ${newVersion.version_number}`,
      });
      
      return newVersion;
    } catch (error) {
      console.error('Error creating version:', error);
      toast({
        title: 'Error',
        description: 'Failed to create version',
        variant: 'destructive',
      });
      throw error;
    }
  }, [sourceId, toast]);

  const restoreVersion = useCallback(async (versionId: string) => {
    if (!sourceId) return;
    
    try {
      const version = versions.find(v => v.id === versionId);
      if (!version) {
        throw new Error('Version not found');
      }
      
      await invoke('restore_source_version', {
        sourceId,
        versionId
      });
      
      toast({
        title: 'Version Restored',
        description: `Restored to version ${version.version_number}`,
      });
      
      // Refresh versions after restoration
      await fetchVersions();
    } catch (error) {
      console.error('Error restoring version:', error);
      toast({
        title: 'Error',
        description: 'Failed to restore version',
        variant: 'destructive',
      });
      throw error;
    }
  }, [sourceId, versions, toast, fetchVersions]);

  const compareVersions = useCallback((versionId1: string, versionId2: string) => {
    const v1 = versions.find(v => v.id === versionId1);
    const v2 = versions.find(v => v.id === versionId2);
    
    if (!v1 || !v2) {
      throw new Error('Version not found');
    }
    
    // Simple comparison - in a real app, you might want to use a diff library
    return {
      version1: v1,
      version2: v2,
      titleChanged: v1.title !== v2.title,
      contentChanged: v1.content !== v2.content,
      metadataChanged: JSON.stringify(v1.metadata) !== JSON.stringify(v2.metadata)
    };
  }, [versions]);

  const getLatestVersion = useCallback(() => {
    if (versions.length === 0) return null;
    return versions.reduce((latest, current) => 
      current.version_number > latest.version_number ? current : latest
    );
  }, [versions]);

  const getVersionHistory = useCallback(() => {
    return [...versions].sort((a, b) => b.version_number - a.version_number);
  }, [versions]);

  return {
    versions,
    isLoading,
    createVersion,
    restoreVersion,
    compareVersions,
    getLatestVersion,
    getVersionHistory,
    refetchVersions: fetchVersions
  };
};