import { useState, useEffect, useCallback } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { useToast } from './use-toast';

export interface SourceTag {
  id: string;
  name: string;
  color: string;
  description?: string;
  user_id: string;
  created_at: string;
}

export interface TagAssignment {
  source_id: string;
  tag_id: string;
  assigned_at: string;
}

export const useSourceTags = () => {
  const [tags, setTags] = useState<SourceTag[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const fetchTags = useCallback(async () => {
    setIsLoading(true);
    try {
      const result = await invoke<SourceTag[]>('get_source_tags');
      setTags(result);
    } catch (error) {
      console.error('Error fetching tags:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch tags',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  useEffect(() => {
    fetchTags();
  }, [fetchTags]);

  const createTag = useCallback(async (name: string, color: string, description?: string) => {
    try {
      const newTag = await invoke<SourceTag>('create_source_tag', {
        name,
        color,
        description
      });
      
      setTags(prev => [...prev, newTag]);
      
      toast({
        title: 'Tag Created',
        description: `Created tag "${name}"`,
      });
      
      return newTag;
    } catch (error) {
      console.error('Error creating tag:', error);
      toast({
        title: 'Error',
        description: 'Failed to create tag',
        variant: 'destructive',
      });
      throw error;
    }
  }, [toast]);

  const updateTag = useCallback(async (tagId: string, updates: Partial<SourceTag>) => {
    try {
      await invoke('update_source_tag', {
        tagId,
        ...updates
      });
      
      setTags(prev => prev.map(tag => 
        tag.id === tagId ? { ...tag, ...updates } : tag
      ));
      
      toast({
        title: 'Tag Updated',
        description: 'Successfully updated tag',
      });
    } catch (error) {
      console.error('Error updating tag:', error);
      toast({
        title: 'Error',
        description: 'Failed to update tag',
        variant: 'destructive',
      });
      throw error;
    }
  }, [toast]);

  const deleteTag = useCallback(async (tagId: string) => {
    try {
      await invoke('delete_source_tag', { tagId });
      
      setTags(prev => prev.filter(tag => tag.id !== tagId));
      
      toast({
        title: 'Tag Deleted',
        description: 'Successfully deleted tag',
      });
    } catch (error) {
      console.error('Error deleting tag:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete tag',
        variant: 'destructive',
      });
      throw error;
    }
  }, [toast]);

  const assignTagToSource = useCallback(async (sourceId: string, tagId: string) => {
    try {
      await invoke('assign_tag_to_source', {
        sourceId,
        tagId
      });
      
      toast({
        title: 'Tag Assigned',
        description: 'Successfully assigned tag to source',
      });
    } catch (error) {
      console.error('Error assigning tag:', error);
      toast({
        title: 'Error',
        description: 'Failed to assign tag',
        variant: 'destructive',
      });
      throw error;
    }
  }, [toast]);

  const removeTagFromSource = useCallback(async (sourceId: string, tagId: string) => {
    try {
      await invoke('remove_tag_from_source', {
        sourceId,
        tagId
      });
      
      toast({
        title: 'Tag Removed',
        description: 'Successfully removed tag from source',
      });
    } catch (error) {
      console.error('Error removing tag:', error);
      toast({
        title: 'Error',
        description: 'Failed to remove tag',
        variant: 'destructive',
      });
      throw error;
    }
  }, [toast]);

  return {
    tags,
    isLoading,
    createTag,
    updateTag,
    deleteTag,
    assignTagToSource,
    removeTagFromSource,
    refetchTags: fetchTags
  };
};

export const useSourceTagAssignments = (sourceId?: string) => {
  const [assignments, setAssignments] = useState<TagAssignment[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (!sourceId) return;

    const fetchAssignments = async () => {
      setIsLoading(true);
      try {
        const result = await invoke<TagAssignment[]>('get_source_tag_assignments', {
          sourceId
        });
        setAssignments(result);
      } catch (error) {
        console.error('Error fetching tag assignments:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchAssignments();
  }, [sourceId]);

  return {
    assignments,
    isLoading
  };
};