import { useState, useEffect, useCallback } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { useToast } from './use-toast';

export interface SourceDuplicate {
  id: string;
  source_id: string;
  duplicate_source_id: string;
  similarity_score: number;
  detection_method: string;
  is_confirmed: boolean;
  is_dismissed: boolean;
  detected_at: string;
}

export const useSourceDuplicates = (notebookId?: string) => {
  const [duplicates, setDuplicates] = useState<SourceDuplicate[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isDetecting, setIsDetecting] = useState(false);
  const { toast } = useToast();

  const fetchDuplicates = useCallback(async () => {
    if (!notebookId) return;
    
    setIsLoading(true);
    try {
      const result = await invoke<SourceDuplicate[]>('get_source_duplicates', {
        notebookId
      });
      setDuplicates(result);
    } catch (error) {
      console.error('Error fetching duplicates:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch duplicates',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  }, [notebookId, toast]);

  useEffect(() => {
    fetchDuplicates();
  }, [fetchDuplicates]);

  const checkDuplicates = useCallback(async () => {
    if (!notebookId) return;
    
    setIsDetecting(true);
    try {
      const result = await invoke<SourceDuplicate[]>('detect_duplicates', {
        notebookId
      });
      
      setDuplicates(result);
      
      if (result.length > 0) {
        toast({
          title: 'Duplicates Found',
          description: `Found ${result.length} potential duplicates`,
        });
      } else {
        toast({
          title: 'No Duplicates',
          description: 'No duplicate sources detected',
        });
      }
      
      return result;
    } catch (error) {
      console.error('Error detecting duplicates:', error);
      toast({
        title: 'Error',
        description: 'Failed to detect duplicates',
        variant: 'destructive',
      });
      throw error;
    } finally {
      setIsDetecting(false);
    }
  }, [notebookId, toast]);

  const confirmDuplicate = useCallback(async (duplicateId: string) => {
    try {
      await invoke('confirm_duplicate', {
        duplicateId
      });
      
      setDuplicates(prev => prev.map(dup => 
        dup.id === duplicateId ? { ...dup, is_confirmed: true } : dup
      ));
      
      toast({
        title: 'Duplicate Confirmed',
        description: 'Marked as confirmed duplicate',
      });
    } catch (error) {
      console.error('Error confirming duplicate:', error);
      toast({
        title: 'Error',
        description: 'Failed to confirm duplicate',
        variant: 'destructive',
      });
      throw error;
    }
  }, [toast]);

  const dismissDuplicate = useCallback(async (duplicateId: string) => {
    try {
      await invoke('dismiss_duplicate', {
        duplicateId
      });
      
      setDuplicates(prev => prev.map(dup => 
        dup.id === duplicateId ? { ...dup, is_dismissed: true } : dup
      ));
      
      toast({
        title: 'Duplicate Dismissed',
        description: 'Marked as not a duplicate',
      });
    } catch (error) {
      console.error('Error dismissing duplicate:', error);
      toast({
        title: 'Error',
        description: 'Failed to dismiss duplicate',
        variant: 'destructive',
      });
      throw error;
    }
  }, [toast]);

  const mergeDuplicates = useCallback(async (sourceId: string, duplicateId: string, keepSource: 'source' | 'duplicate') => {
    try {
      await invoke('merge_duplicates', {
        sourceId,
        duplicateId,
        keepSource
      });
      
      setDuplicates(prev => prev.filter(dup => 
        !(dup.source_id === sourceId && dup.duplicate_source_id === duplicateId)
      ));
      
      toast({
        title: 'Sources Merged',
        description: 'Successfully merged duplicate sources',
      });
    } catch (error) {
      console.error('Error merging duplicates:', error);
      toast({
        title: 'Error',
        description: 'Failed to merge duplicates',
        variant: 'destructive',
      });
      throw error;
    }
  }, [toast]);

  const getActiveDuplicates = useCallback(() => {
    return duplicates.filter(dup => !dup.is_dismissed);
  }, [duplicates]);

  const getConfirmedDuplicates = useCallback(() => {
    return duplicates.filter(dup => dup.is_confirmed && !dup.is_dismissed);
  }, [duplicates]);

  const getPendingDuplicates = useCallback(() => {
    return duplicates.filter(dup => !dup.is_confirmed && !dup.is_dismissed);
  }, [duplicates]);

  return {
    duplicates,
    activeDuplicates: getActiveDuplicates(),
    confirmedDuplicates: getConfirmedDuplicates(),
    pendingDuplicates: getPendingDuplicates(),
    isLoading,
    isDetecting,
    checkDuplicates,
    confirmDuplicate,
    dismissDuplicate,
    mergeDuplicates,
    refetchDuplicates: fetchDuplicates
  };
};