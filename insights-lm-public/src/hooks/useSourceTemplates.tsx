import { useState, useEffect, useCallback } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { useToast } from './use-toast';

export interface SourceTemplate {
  id: string;
  name: string;
  description: string;
  source_type: string;
  default_title?: string;
  default_content?: string;
  default_priority: string;
  metadata_template: any;
  default_tags: string[];
  is_system: boolean;
  created_by: string;
  created_at: string;
}

export const useSourceTemplates = () => {
  const [templates, setTemplates] = useState<SourceTemplate[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const fetchTemplates = useCallback(async () => {
    setIsLoading(true);
    try {
      const result = await invoke<SourceTemplate[]>('get_source_templates');
      setTemplates(result);
    } catch (error) {
      console.error('Error fetching templates:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch templates',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  useEffect(() => {
    fetchTemplates();
  }, [fetchTemplates]);

  const createTemplate = useCallback(async (template: Omit<SourceTemplate, 'id' | 'created_at' | 'created_by'>) => {
    try {
      const newTemplate = await invoke<SourceTemplate>('create_source_template', {
        ...template,
        createdBy: 'current_user'
      });
      
      setTemplates(prev => [...prev, newTemplate]);
      
      toast({
        title: 'Template Created',
        description: `Created template "${template.name}"`,
      });
      
      return newTemplate;
    } catch (error) {
      console.error('Error creating template:', error);
      toast({
        title: 'Error',
        description: 'Failed to create template',
        variant: 'destructive',
      });
      throw error;
    }
  }, [toast]);

  const updateTemplate = useCallback(async (templateId: string, updates: Partial<SourceTemplate>) => {
    try {
      await invoke('update_source_template', {
        templateId,
        ...updates
      });
      
      setTemplates(prev => prev.map(template => 
        template.id === templateId ? { ...template, ...updates } : template
      ));
      
      toast({
        title: 'Template Updated',
        description: 'Successfully updated template',
      });
    } catch (error) {
      console.error('Error updating template:', error);
      toast({
        title: 'Error',
        description: 'Failed to update template',
        variant: 'destructive',
      });
      throw error;
    }
  }, [toast]);

  const deleteTemplate = useCallback(async (templateId: string) => {
    try {
      const template = templates.find(t => t.id === templateId);
      
      if (template?.is_system) {
        toast({
          title: 'Cannot Delete',
          description: 'System templates cannot be deleted',
          variant: 'destructive',
        });
        return;
      }
      
      await invoke('delete_source_template', { templateId });
      
      setTemplates(prev => prev.filter(t => t.id !== templateId));
      
      toast({
        title: 'Template Deleted',
        description: 'Successfully deleted template',
      });
    } catch (error) {
      console.error('Error deleting template:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete template',
        variant: 'destructive',
      });
      throw error;
    }
  }, [templates, toast]);

  const applyTemplate = useCallback(async (templateId: string) => {
    const template = templates.find(t => t.id === templateId);
    
    if (!template) {
      throw new Error('Template not found');
    }
    
    return {
      ...template,
      // Return template data that can be used to create a new source
      apply: async (notebookId: string, customData?: any) => {
        return {
          notebook_id: notebookId,
          title: customData?.title || template.default_title || 'New Source',
          content: customData?.content || template.default_content || '',
          source_type: template.source_type,
          priority: template.default_priority,
          template_id: templateId,
          metadata: {
            ...template.metadata_template,
            ...customData?.metadata
          }
        };
      }
    };
  }, [templates]);

  const getSystemTemplates = useCallback(() => {
    return templates.filter(t => t.is_system);
  }, [templates]);

  const getUserTemplates = useCallback(() => {
    return templates.filter(t => !t.is_system);
  }, [templates]);

  return {
    templates,
    systemTemplates: getSystemTemplates(),
    userTemplates: getUserTemplates(),
    isLoading,
    createTemplate,
    updateTemplate,
    deleteTemplate,
    applyTemplate,
    refetchTemplates: fetchTemplates
  };
};