"""OpenAI-compatible Pydantic models."""

from datetime import datetime
from typing import List, Optional, Dict, Any, Union, Literal
from pydantic import BaseModel, Field, validator


class ChatMessage(BaseModel):
    """Chat message model - accepts any content format."""
    role: Literal["system", "user", "assistant"] = Field(..., description="The role of the message author")
    content: Any = Field(..., description="The content of the message")  # Accept anything
    name: Optional[str] = Field(None, description="Optional name for the message author")
    
    def get_text_content(self) -> str:
        """Extract text content from any format."""
        if isinstance(self.content, str):
            return self.content
        elif isinstance(self.content, list):
            # Extract text from content blocks
            text_parts = []
            for item in self.content:
                if isinstance(item, dict):
                    if "text" in item:
                        text_parts.append(str(item["text"]))
                    elif "content" in item:
                        text_parts.append(str(item["content"]))
                else:
                    text_parts.append(str(item))
            return "\n".join(text_parts)
        else:
            return str(self.content)


class ChatCompletionRequest(BaseModel):
    """Chat completion request model."""
    model: str = Field(..., description="ID of the model to use")
    messages: List[ChatMessage] = Field(..., description="List of messages comprising the conversation")
    temperature: Optional[float] = Field(1.0, ge=0.0, le=2.0, description="Sampling temperature")
    top_p: Optional[float] = Field(1.0, ge=0.0, le=1.0, description="Nucleus sampling parameter")
    max_tokens: Optional[int] = Field(None, ge=1, description="Maximum number of tokens to generate")
    stream: Optional[bool] = Field(False, description="Whether to stream partial message deltas")
    stop: Optional[Union[str, List[str]]] = Field(None, description="Up to 4 sequences where the API will stop generating")
    frequency_penalty: Optional[float] = Field(0.0, ge=-2.0, le=2.0, description="Frequency penalty")
    presence_penalty: Optional[float] = Field(0.0, ge=-2.0, le=2.0, description="Presence penalty")
    user: Optional[str] = Field(None, description="Unique identifier representing your end-user")
    
    # Extension fields for Claude Code
    project_id: Optional[str] = Field(None, description="Project ID for Claude Code context")
    session_id: Optional[str] = Field(None, description="Session ID to continue conversation")
    system_prompt: Optional[str] = Field(None, description="System prompt override")


class ChatCompletionChoice(BaseModel):
    """Chat completion choice model."""
    index: int = Field(..., description="The index of the choice")
    message: ChatMessage = Field(..., description="The message generated by the model")
    finish_reason: Optional[Literal["stop", "length", "content_filter", "tool_calls"]] = Field(
        None, description="The reason the model stopped generating tokens"
    )


class ChatCompletionUsage(BaseModel):
    """Token usage information."""
    prompt_tokens: int = Field(..., description="Number of tokens in the prompt")
    completion_tokens: int = Field(..., description="Number of tokens in the completion")
    total_tokens: int = Field(..., description="Total number of tokens used")


class ChatCompletionResponse(BaseModel):
    """Chat completion response model."""
    id: str = Field(..., description="Unique identifier for the chat completion")
    object: Literal["chat.completion"] = Field("chat.completion", description="Object type")
    created: int = Field(..., description="Unix timestamp of when the completion was created")
    model: str = Field(..., description="Model used for the completion")
    choices: List[ChatCompletionChoice] = Field(..., description="List of completion choices")
    usage: ChatCompletionUsage = Field(..., description="Usage statistics for the completion")
    
    # Extension fields
    session_id: Optional[str] = Field(None, description="Session ID for this completion")
    project_id: Optional[str] = Field(None, description="Project ID for this completion")


# Streaming Models
class ChatCompletionChunkDelta(BaseModel):
    """Delta object for streaming responses."""
    role: Optional[str] = Field(None, description="The role of the author of this message")
    content: Optional[str] = Field(None, description="The contents of the chunk message")


class ChatCompletionChunkChoice(BaseModel):
    """Choice object for streaming responses."""
    index: int = Field(..., description="The index of the choice")
    delta: ChatCompletionChunkDelta = Field(..., description="Delta containing message changes")
    finish_reason: Optional[Literal["stop", "length", "content_filter", "tool_calls"]] = Field(
        None, description="The reason the model stopped generating tokens"
    )


class ChatCompletionChunk(BaseModel):
    """Streaming chat completion chunk."""
    id: str = Field(..., description="Unique identifier for the chat completion")
    object: Literal["chat.completion.chunk"] = Field("chat.completion.chunk", description="Object type")
    created: int = Field(..., description="Unix timestamp of when the completion was created")
    model: str = Field(..., description="Model used for the completion")
    choices: List[ChatCompletionChunkChoice] = Field(..., description="List of completion choices")


# Models endpoint
class ModelObject(BaseModel):
    """Model object."""
    id: str = Field(..., description="Model identifier")
    object: Literal["model"] = Field("model", description="Object type")
    created: int = Field(..., description="Unix timestamp of when the model was created")
    owned_by: str = Field(..., description="Organization that owns the model")


class ModelListResponse(BaseModel):
    """Model list response."""
    object: Literal["list"] = Field("list", description="Object type")
    data: List[ModelObject] = Field(..., description="List of model objects")


# Error Models
class ErrorDetail(BaseModel):
    """Error detail object."""
    message: str = Field(..., description="Human-readable error message")
    type: str = Field(..., description="Error type")
    code: Optional[str] = Field(None, description="Error code")


class ErrorResponse(BaseModel):
    """Error response model."""
    error: ErrorDetail = Field(..., description="Error details")


# Extension Models for Claude Code specific features
class ProjectInfo(BaseModel):
    """Project information model."""
    id: str = Field(..., description="Project identifier")
    name: str = Field(..., description="Project name")
    description: Optional[str] = Field(None, description="Project description")
    path: str = Field(..., description="Project file system path")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    is_active: bool = Field(True, description="Whether the project is active")


class CreateProjectRequest(BaseModel):
    """Create project request model."""
    name: str = Field(..., description="Project name")
    description: Optional[str] = Field(None, description="Project description")
    path: Optional[str] = Field(None, description="Custom project path")


class SessionInfo(BaseModel):
    """Session information model."""
    id: str = Field(..., description="Session identifier")
    project_id: str = Field(..., description="Associated project ID")
    title: Optional[str] = Field(None, description="Session title")
    model: str = Field(..., description="Model used in this session")
    system_prompt: Optional[str] = Field(None, description="System prompt for this session")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    is_active: bool = Field(True, description="Whether the session is active")
    total_tokens: int = Field(0, description="Total tokens used in this session")
    total_cost: float = Field(0.0, description="Total cost of this session")
    message_count: int = Field(0, description="Number of messages in this session")


class CreateSessionRequest(BaseModel):
    """Create session request model."""
    project_id: str = Field(..., description="Project ID for the session")
    title: Optional[str] = Field(None, description="Session title")
    model: Optional[str] = Field(None, description="Model to use")
    system_prompt: Optional[str] = Field(None, description="System prompt")


# Tool execution models
class ToolInfo(BaseModel):
    """Tool information model."""
    name: str = Field(..., description="Tool name")
    description: str = Field(..., description="Tool description")
    parameters: Dict[str, Any] = Field(..., description="Tool parameters schema")


class ToolExecutionRequest(BaseModel):
    """Tool execution request model."""
    name: str = Field(..., description="Tool name to execute")
    parameters: Dict[str, Any] = Field(..., description="Tool parameters")
    project_id: str = Field(..., description="Project context for tool execution")


class ToolExecutionResponse(BaseModel):
    """Tool execution response model."""
    success: bool = Field(..., description="Whether the tool execution was successful")
    result: Any = Field(..., description="Tool execution result")
    error: Optional[str] = Field(None, description="Error message if execution failed")
    duration_ms: int = Field(..., description="Execution duration in milliseconds")


# Health check model
class HealthCheckResponse(BaseModel):
    """Health check response model."""
    status: Literal["healthy", "unhealthy"] = Field(..., description="Service health status")
    version: str = Field(..., description="API version")
    claude_version: Optional[str] = Field(None, description="Claude Code version")
    active_sessions: int = Field(..., description="Number of active sessions")
    uptime_seconds: Optional[int] = Field(None, description="Service uptime in seconds")


# Usage statistics models
class UsageStats(BaseModel):
    """Usage statistics model."""
    total_requests: int = Field(..., description="Total number of requests")
    total_tokens: int = Field(..., description="Total tokens processed")
    total_cost: float = Field(..., description="Total cost incurred")
    active_sessions: int = Field(..., description="Currently active sessions")
    models_used: List[str] = Field(..., description="Models that have been used")
    avg_response_time_ms: float = Field(..., description="Average response time in milliseconds")


# Webhook models (for future extension)
class WebhookEvent(BaseModel):
    """Webhook event model."""
    event_type: str = Field(..., description="Type of event")
    session_id: str = Field(..., description="Session ID")
    project_id: str = Field(..., description="Project ID")
    timestamp: datetime = Field(..., description="Event timestamp")
    data: Dict[str, Any] = Field(..., description="Event data")


# Pagination models
class PaginationInfo(BaseModel):
    """Pagination information."""
    page: int = Field(1, ge=1, description="Current page number")
    per_page: int = Field(20, ge=1, le=100, description="Items per page")
    total_items: int = Field(..., description="Total number of items")
    total_pages: int = Field(..., description="Total number of pages")
    has_next: bool = Field(..., description="Whether there are more pages")
    has_prev: bool = Field(..., description="Whether there are previous pages")


class PaginatedResponse(BaseModel):
    """Generic paginated response."""
    data: List[Any] = Field(..., description="List of items")
    pagination: PaginationInfo = Field(..., description="Pagination information")


# File upload models (for project files)
class FileUploadResponse(BaseModel):
    """File upload response model."""
    filename: str = Field(..., description="Uploaded filename")
    size: int = Field(..., description="File size in bytes")
    path: str = Field(..., description="File path in project")
    uploaded_at: datetime = Field(..., description="Upload timestamp")


# Configuration models
class APIConfiguration(BaseModel):
    """API configuration model."""
    max_concurrent_sessions: int = Field(..., description="Maximum concurrent sessions")
    session_timeout_minutes: int = Field(..., description="Session timeout in minutes")
    supported_models: List[str] = Field(..., description="List of supported models")
    features_enabled: List[str] = Field(..., description="List of enabled features")
    rate_limits: Dict[str, int] = Field(..., description="Rate limiting configuration")
