{"name": "sanity", "private": true, "version": "0.1.0", "license": "AGPL-3.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri", "check": "tsc --noEmit && cd src-tauri && cargo check", "test": "vitest", "test:unit": "vitest run src/services/__tests__/", "test:integration": "vitest run src/test/integration/", "test:e2e": "vitest run src/test/e2e/", "test:performance": "vitest run src/test/performance/", "test:accessibility": "vitest run src/test/accessibility/", "test:cross-platform": "vitest run src/test/cross-platform/", "test:ci": "vitest run src/test/ci/", "test:all": "npm run test:unit && npm run test:integration && npm run test:e2e && npm run test:performance && npm run test:accessibility && npm run test:cross-platform", "test:coverage": "vitest run --coverage", "test:watch": "vitest", "test:ui": "vitest --ui", "lint": "eslint src --ext .ts,.tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint src --ext .ts,.tsx --fix", "lint:data-center": "eslint src/components/data-center --ext .ts,.tsx", "type-check": "tsc --noEmit", "type-check:watch": "tsc --noEmit --watch", "showcase": "vite dev --open /src/components/data-center/showcase"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@fullcalendar/core": "^6.1.19", "@fullcalendar/daygrid": "^6.1.19", "@fullcalendar/interaction": "^6.1.19", "@fullcalendar/react": "^6.1.19", "@fullcalendar/timegrid": "^6.1.19", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.15", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.16", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.1.3", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.3", "@radix-ui/react-toggle": "^1.1.10", "@radix-ui/react-tooltip": "^1.1.5", "@react-spring/web": "^10.0.1", "@tailwindcss/cli": "^4.1.8", "@tailwindcss/vite": "^4.1.8", "@tanstack/react-query": "^5.85.5", "@tanstack/react-table": "^8.21.3", "@tanstack/react-virtual": "^3.13.10", "@tauri-apps/api": "^2.8.0", "@tauri-apps/plugin-dialog": "^2.3.2", "@tauri-apps/plugin-fs": "^2.4.2", "@tauri-apps/plugin-global-shortcut": "^2.3.0", "@tauri-apps/plugin-opener": "^2", "@tauri-apps/plugin-shell": "^2.3.0", "@types/canvas-confetti": "^1.9.0", "@types/diff": "^8.0.0", "@types/dompurify": "^3.0.5", "@types/html2canvas": "^0.5.35", "@types/jspdf": "^1.3.3", "@types/react-syntax-highlighter": "^15.5.13", "@types/react-window": "^1.8.8", "@types/recharts": "^1.8.29", "@types/styled-components": "^5.1.34", "@types/vanilla-tilt": "^1.4.1", "@uiw/react-md-editor": "^4.0.7", "ansi-to-html": "^0.7.2", "canvas-confetti": "^1.9.3", "chart.js": "^4.5.0", "chroma-js": "^3.1.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "diff": "^8.0.2", "dompurify": "^3.2.6", "flatpickr": "^4.6.13", "formik": "^2.4.6", "framer-motion": "^12.23.12", "html-react-parser": "^5.2.6", "html2canvas": "^1.4.1", "idb": "^8.0.0", "immer": "^10.1.1", "jspdf": "^3.0.1", "lucide-react": "^0.263.1", "mdast-util-to-string": "^4.0.0", "mermaid": "^11.10.0", "multer": "^2.0.2", "papaparse": "^5.5.3", "posthog-js": "^1.258.3", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-countup": "^6.5.3", "react-day-picker": "^9.8.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-flatpickr": "^4.0.11", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.5.2", "react-markdown": "^9.0.3", "react-quill": "^2.0.0", "react-resizable-panels": "^3.0.4", "react-router-dom": "^7.8.1", "react-select": "^5.10.2", "react-syntax-highlighter": "^15.6.1", "react-table": "^7.8.0", "react-tag-input-component": "^2.0.2", "react-window": "^1.8.11", "recharts": "^2.15.4", "remark-gfm": "^4.0.0", "remark-parse": "^11.0.0", "styled-components": "^6.1.19", "sweetalert2": "^11.22.4", "sweetalert2-react-content": "^5.1.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.8", "unified": "^11.0.5", "unist-util-visit": "^5.0.0", "uuid": "^11.1.0", "vanilla-tilt": "^1.8.1", "xlsx": "^0.18.5", "yup": "^1.7.0", "zod": "^3.24.1", "zustand": "^5.0.8"}, "devDependencies": {"@tauri-apps/cli": "^2.8.1", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/express": "^5.0.3", "@types/mdast": "^4.0.4", "@types/multer": "^2.0.0", "@types/node": "^22.17.2", "@types/papaparse": "^5.3.16", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@types/sharp": "^0.32.0", "@types/unist": "^3.0.3", "@types/xlsx": "^0.0.36", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-react": "^4.3.4", "@vitest/ui": "^3.2.4", "eslint": "^8.57.0", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "jsdom": "^26.1.0", "puppeteer": "^24.17.0", "sharp": "^0.34.2", "tw-animate-css": "^1.3.7", "typescript": "~5.9.2", "vite": "^6.0.3", "vitest": "^3.2.4"}, "trustedDependencies": ["@parcel/watcher", "@tailwindcss/oxide"]}