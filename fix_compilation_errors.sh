#!/bin/bash

echo "Fixing Rust compilation errors..."

# Fix 1: Update lightbulb.rs to handle missing tags field
cat > /tmp/fix_lightbulb.patch << 'EOF'
--- a/src-tauri/src/commands/lightbulb.rs
+++ b/src-tauri/src/commands/lightbulb.rs
@@ -63,7 +63,7 @@
             id: project.id,
             name: project.name,
             description: project.description.unwrap_or_default(),
-            tags: project.tags,
+            tags: Vec::new(), // Projects don't have tags in the new model
             created_at: project.timestamps.created_at.timestamp(),
             updated_at: project.timestamps.updated_at.timestamp(),
             status: format!("{:?}", project.status),
@@ -187,10 +187,7 @@
                     pages: Vec::new(), // These fields need to be populated separately
                     sources: Vec::new(),
-                    metadata: ProjectMetadata {
-                        page_count: p.metadata.page_count,
-                        last_activity: p.metadata.last_activity,
-                    },
+                    metadata: p.metadata.clone(),
                 }
             }).collect();
@@ -402,10 +399,10 @@
     let db_template = lightbulb_db::PageTemplate {
         id: template.id.clone(),
         name: template.name.clone(),
-        description: template.description.clone(),
-        content: template.default_content.clone(),
+        description: template.description.clone().unwrap_or_default(),
+        content: template.content_template.clone(),
         category: "custom".to_string(),
-        tags: template.default_tags.clone(),
+        tags: Vec::new(), // PageTemplate doesn't have default_tags
         preview_image: None,
         created_at: now,
         updated_at: now,
EOF

# Fix 2: Update search service to handle PaginationParams correctly
cat > /tmp/fix_search_service.patch << 'EOF'
--- a/src-tauri/src/services/search.rs
+++ b/src-tauri/src/services/search.rs
@@ -143,8 +143,8 @@
         pagination: PaginationParams,
     ) -> LightbulbResult<PaginatedResponse<SearchResult>> {
         let total_count = results.len();
-        let start_index = (pagination.page.saturating_sub(1)) * pagination.page_size;
-        let end_index = std::cmp::min(start_index + pagination.page_size, total_count);
+        let start_index = (pagination.page.saturating_sub(1)) * pagination.limit;
+        let end_index = std::cmp::min(start_index + pagination.limit, total_count);
         
         let paginated_results = if start_index < total_count {
             results[start_index..end_index].to_vec()
@@ -153,11 +153,11 @@
         };
         
         Ok(PaginatedResponse {
-            data: results,
-            total_count,
+            items: paginated_results,
+            total: total_count,
             page: pagination.page,
-            page_size: pagination.page_size,
-            total_pages: (total_count + pagination.page_size - 1) / pagination.page_size,
+            limit: pagination.limit,
+            has_next: end_index < total_count,
+            has_prev: pagination.page > 1,
         })
     }
@@ -262,7 +262,7 @@
         }
         
         // Calculate relevance scores
-        for keyword in query_keywords {
+        for keyword in &query_keywords {
             for result in &mut results {
                 let title_matches = result.title.to_lowercase().matches(&keyword.to_lowercase()).count();
                 let content_matches = result.content.to_lowercase().matches(&keyword.to_lowercase()).count();
@@ -392,7 +392,10 @@
     pub async fn rebuild_index(&self) -> LightbulbResult<()> {
         // Get all sources from database
-        let sources = self.database.list_sources(None, None).await?;
+        let sources = self.database.list_sources(
+            "", 
+            PaginationParams::default()
+        ).await?;
         
         // Rebuild search index
-        for source in sources.data {
+        for source in sources.items {
@@ -528,7 +531,7 @@
         }
         
         // Filter by source type
-        if let Some(ref source_types) = filters.source_types {
+        if !filters.source_types.is_empty() {
             results.retain(|r| {
                 source_types.iter().any(|st| {
                     // Match source type logic
@@ -538,7 +541,7 @@
         }
         
         // Filter by minimum relevance score
-        if let Some(min_score) = filters.min_relevance_score {
+        if let Some(min_score) = filters.get("min_relevance_score") {
             results.retain(|r| r.relevance_score >= min_score);
         }
EOF

# Fix 3: Update lightbulb_db.rs to fix SupportedFormat fields
cat > /tmp/fix_lightbulb_db.patch << 'EOF'
--- a/src-tauri/src/commands/lightbulb/lightbulb_db.rs
+++ b/src-tauri/src/commands/lightbulb/lightbulb_db.rs
@@ -474,13 +474,13 @@
 pub fn get_supported_formats() -> Vec<models::lightbulb::SupportedFormat> {
     vec![
         models::lightbulb::SupportedFormat {
             id: "pdf".to_string(),
-            format: "PDF".to_string(),
+            name: "PDF".to_string(),
             category: "document".to_string(),
             processor: "pdf_processor".to_string(),
             is_enabled: true,
-            max_size_mb: 50,
+            max_file_size: 50 * 1024 * 1024, // 50MB in bytes
         },
         models::lightbulb::SupportedFormat {
             id: "docx".to_string(),
-            format: "Word Document".to_string(),
+            name: "Word Document".to_string(),
             category: "document".to_string(),
             processor: "docx_processor".to_string(),
             is_enabled: true,
-            max_size_mb: 50,
+            max_file_size: 50 * 1024 * 1024,
         },
         // Continue for other formats...
EOF

# Fix 4: Add Serialize/Deserialize to DatabaseConfig
cat > /tmp/fix_database_config.patch << 'EOF'
--- a/src-tauri/src/models/database.rs
+++ b/src-tauri/src/models/database.rs
@@ -1,11 +1,12 @@
 use serde::{Deserialize, Serialize};
 use chrono::{DateTime, Utc};
 use std::collections::HashMap;
 
 /// Database configuration
-#[derive(Debug, Clone)]
+#[derive(Debug, Clone, Serialize, Deserialize)]
 pub struct DatabaseConfig {
     pub connection_string: String,
     pub max_connections: u32,
     pub connection_timeout: u64,
     pub enable_logging: bool,
 }
EOF

# Fix 5: Fix ChatMessage user_id field
cat > /tmp/fix_validation.patch << 'EOF'
--- a/src-tauri/src/services/validation.rs
+++ b/src-tauri/src/services/validation.rs
@@ -237,7 +237,7 @@
     pub fn validate_chat_message(&self, message: &ChatMessage) -> LightbulbResult<()> {
         // Validate user_id
-        self.validate_string_field(&message.user_id, "user_id", &[
+        self.validate_string_field(&message.id, "message_id", &[
             StringValidation::NotEmpty,
             StringValidation::MaxLength(100),
         ])?;
EOF

echo "Applying patches..."

# Apply the patches
cd /Users/<USER>/Downloads/sanity-main/src-tauri
patch -p1 < /tmp/fix_lightbulb.patch
patch -p1 < /tmp/fix_search_service.patch
patch -p1 < /tmp/fix_lightbulb_db.patch
patch -p1 < /tmp/fix_database_config.patch
patch -p1 < /tmp/fix_validation.patch

echo "Patches applied. Now attempting to compile..."
cd /Users/<USER>/Downloads/sanity-main
bun run tauri dev