name: Comprehensive Test Suite

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # Run tests daily at 2 AM UTC
    - cron: '0 2 * * *'

jobs:
  # Unit and Integration Tests
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [18.x, 20.x, 22.x]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run TypeScript check
      run: npx tsc --noEmit
      
    - name: Run unit tests
      run: npm run test -- --run --coverage --reporter=json --outputFile=unit-test-results.json
      
    - name: Run integration tests
      run: npm run test -- --run src/test/integration/ --reporter=json --outputFile=integration-test-results.json
      
    - name: Upload test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: test-results-node-${{ matrix.node-version }}
        path: |
          unit-test-results.json
          integration-test-results.json
          coverage/
          
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v4
      with:
        file: ./coverage/lcov.info
        flags: unittests
        name: codecov-umbrella

  # End-to-End Tests
  e2e-tests:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Build application
      run: npm run build
      
    - name: Run E2E tests
      run: npm run test -- --run src/test/e2e/ --reporter=json --outputFile=e2e-test-results.json
      
    - name: Upload E2E test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: e2e-test-results
        path: e2e-test-results.json

  # Performance Tests
  performance-tests:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run performance tests
      run: npm run test -- --run src/test/performance/ --reporter=json --outputFile=performance-test-results.json
      
    - name: Upload performance test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: performance-test-results
        path: performance-test-results.json

  # Accessibility Tests
  accessibility-tests:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run accessibility tests
      run: npm run test -- --run src/test/accessibility/ --reporter=json --outputFile=accessibility-test-results.json
      
    - name: Upload accessibility test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: accessibility-test-results
        path: accessibility-test-results.json

  # Cross-Browser Tests
  cross-browser-tests:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run cross-platform tests
      run: npm run test -- --run src/test/cross-platform/ --reporter=json --outputFile=cross-platform-test-results-${{ matrix.os }}.json
      
    - name: Upload cross-platform test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: cross-platform-test-results-${{ matrix.os }}
        path: cross-platform-test-results-${{ matrix.os }}.json

  # Security and Quality Checks
  security-quality:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run security audit
      run: npm audit --audit-level=moderate
      continue-on-error: true
      
    - name: Run dependency check
      run: npm ls --depth=0
      
    - name: Check bundle size
      run: |
        npm run build
        du -sh dist/
        
    - name: Run linting (if configured)
      run: |
        if [ -f ".eslintrc.js" ] || [ -f ".eslintrc.json" ] || [ -f "eslint.config.js" ]; then
          npx eslint src/ --ext .ts,.tsx --max-warnings 0
        else
          echo "ESLint not configured, skipping..."
        fi
      continue-on-error: true

  # Tauri Backend Tests
  tauri-tests:
    runs-on: ${{ matrix.platform }}
    strategy:
      matrix:
        platform: [ubuntu-latest, windows-latest, macos-latest]
        
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'npm'
        
    - name: Install Rust
      uses: dtolnay/rust-toolchain@stable
      
    - name: Cache Rust dependencies
      uses: actions/cache@v4
      with:
        path: |
          ~/.cargo/bin/
          ~/.cargo/registry/index/
          ~/.cargo/registry/cache/
          ~/.cargo/git/db/
          src-tauri/target/
        key: ${{ runner.os }}-cargo-${{ hashFiles('**/Cargo.lock') }}
        
    - name: Install system dependencies (Ubuntu)
      if: matrix.platform == 'ubuntu-latest'
      run: |
        sudo apt-get update
        sudo apt-get install -y libgtk-3-dev libwebkit2gtk-4.0-dev libappindicator3-dev librsvg2-dev patchelf
        
    - name: Install frontend dependencies
      run: npm ci
      
    - name: Run Rust tests
      run: cd src-tauri && cargo test
      
    - name: Build Tauri app
      run: npm run tauri build
      continue-on-error: true

  # Test Results Summary
  test-summary:
    runs-on: ubuntu-latest
    needs: [test, e2e-tests, performance-tests, accessibility-tests, cross-browser-tests, security-quality]
    if: always()
    
    steps:
    - name: Download all test results
      uses: actions/download-artifact@v4
      
    - name: Generate test summary
      run: |
        echo "# Test Results Summary" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        
        # Count test files
        UNIT_TESTS=$(find . -name "*unit-test-results.json" | wc -l)
        E2E_TESTS=$(find . -name "*e2e-test-results.json" | wc -l)
        PERF_TESTS=$(find . -name "*performance-test-results.json" | wc -l)
        A11Y_TESTS=$(find . -name "*accessibility-test-results.json" | wc -l)
        CROSS_TESTS=$(find . -name "*cross-platform-test-results*.json" | wc -l)
        
        echo "- Unit Tests: $UNIT_TESTS result files" >> $GITHUB_STEP_SUMMARY
        echo "- E2E Tests: $E2E_TESTS result files" >> $GITHUB_STEP_SUMMARY
        echo "- Performance Tests: $PERF_TESTS result files" >> $GITHUB_STEP_SUMMARY
        echo "- Accessibility Tests: $A11Y_TESTS result files" >> $GITHUB_STEP_SUMMARY
        echo "- Cross-Platform Tests: $CROSS_TESTS result files" >> $GITHUB_STEP_SUMMARY
        
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "## Coverage Information" >> $GITHUB_STEP_SUMMARY
        
        # Find and display coverage info if available
        if [ -d "coverage" ]; then
          echo "Coverage reports generated successfully" >> $GITHUB_STEP_SUMMARY
        else
          echo "No coverage reports found" >> $GITHUB_STEP_SUMMARY
        fi

  # Notification
  notify:
    runs-on: ubuntu-latest
    needs: [test-summary]
    if: always() && github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - name: Notify on success
      if: needs.test-summary.result == 'success'
      run: |
        echo "✅ All tests passed successfully!"
        
    - name: Notify on failure
      if: needs.test-summary.result == 'failure'
      run: |
        echo "❌ Some tests failed. Please check the results."
        exit 1