name: Release

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:
    inputs:
      version:
        description: 'Version to release (e.g., v1.0.0)'
        required: true
        type: string

permissions:
  contents: write

jobs:
  # Build jobs for each platform
  build-linux:
    uses: ./.github/workflows/build-linux.yml
    secrets: inherit
  
  build-macos:
    uses: ./.github/workflows/build-macos.yml
    secrets: inherit
  

  # Create release after all builds complete
  create-release:
    name: Create Release
    needs: [build-linux, build-macos]
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Determine version
        id: version
        run: |
          if [ "${{ github.event_name }}" = "push" ]; then
            VERSION="${GITHUB_REF#refs/tags/}"
          else
            VERSION="${{ inputs.version }}"
          fi
          echo "version=$VERSION" >> $GITHUB_OUTPUT
          echo "Version: $VERSION"
      
      - name: Download all artifacts
        uses: actions/download-artifact@v4
        with:
          path: artifacts
      
      - name: Prepare release assets
        run: |
          mkdir -p release-assets
          
          # Linux artifacts
          if [ -d "artifacts/linux-x86_64" ]; then
            cp artifacts/linux-x86_64/*.deb release-assets/Claudia_${{ steps.version.outputs.version }}_linux_x86_64.deb || true
            cp artifacts/linux-x86_64/*.AppImage release-assets/Claudia_${{ steps.version.outputs.version }}_linux_x86_64.AppImage || true
          fi
          
          # macOS artifacts
          if [ -d "artifacts/macos-universal" ]; then
            cp artifacts/macos-universal/Claudia.dmg release-assets/Claudia_${{ steps.version.outputs.version }}_macos_universal.dmg || true
            cp artifacts/macos-universal/Claudia.app.zip release-assets/Claudia_${{ steps.version.outputs.version }}_macos_universal.app.tar.gz || true
          fi
          
          # Create source code archives
          # Clean version without 'v' prefix for archive names
          CLEAN_VERSION="${{ steps.version.outputs.version }}"
          CLEAN_VERSION="${CLEAN_VERSION#v}"
          
          # Create source code archives (excluding .git and other unnecessary files)
          echo "Creating source code archives..."
          
          # Create a clean export of the repository
          git archive --format=tar.gz --prefix=claudia-${CLEAN_VERSION}/ -o release-assets/claudia-${CLEAN_VERSION}.tar.gz HEAD
          git archive --format=zip --prefix=claudia-${CLEAN_VERSION}/ -o release-assets/claudia-${CLEAN_VERSION}.zip HEAD

          # Generate signatures for all files
          cd release-assets
          for file in *; do
            if [ -f "$file" ]; then
              sha256sum "$file" > "$file.sha256"
            fi
          done
          cd ..
      
      - name: Create Release
        uses: softprops/action-gh-release@v1
        with:
          tag_name: ${{ steps.version.outputs.version }}
          name: Claudia ${{ steps.version.outputs.version }}
          draft: true
          prerelease: false
          generate_release_notes: true
          files: release-assets/*
          body: |
            <div align="center">
              <img src="https://raw.githubusercontent.com/${{ github.repository }}/${{ steps.version.outputs.version }}/src-tauri/icons/icon.png" alt="Claudia Logo" width="128" height="128">
              
              # Claudia ${{ steps.version.outputs.version }}
              
              🎉 **First Release!** 🎉
            
            ### 🚀 What's New in This First Release
            
            - **AI-Powered Coding**: Seamless integration with Claude for intelligent code assistance
            - **Cross-Platform Support**: Available for macOS and Linux
            - **Modern UI**: Clean, intuitive interface built with React and Tauri
            - **MCP Support**: Model Context Protocol integration for enhanced capabilities
            - **Session Management**: Save and restore your coding sessions
            - **And much more!**
            
            ### Downloads
            
            #### macOS
            - Universal binary (Intel + Apple Silicon)
            - `.dmg` - Disk image installer (recommended)
            - `.app.tar.gz` - Application bundle
            

            #### Linux
            - `.AppImage` - Universal Linux package (recommended)
            - `.deb` - Debian/Ubuntu package
            
            #### Source Code
            - `claudia-{version}.tar.gz` - Source code (tar.gz)
            - `claudia-{version}.zip` - Source code (zip)
            
            ### Installation
            
            **macOS**: Download the `.dmg` file, open it, and drag Claudia to your Applications folder.
            

            **Linux**: Download the `.AppImage` file, make it executable (`chmod +x`), and run it. For Debian/Ubuntu, use the `.deb` file.
            
            ### Verification
            
            All files include `.sha256` signature files for verification.
            
