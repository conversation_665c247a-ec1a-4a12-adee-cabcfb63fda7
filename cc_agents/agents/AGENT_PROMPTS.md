# Agent Prompt Guide

This document provides comprehensive prompts for each agent in the Claude Code subagents collection. These prompts should be used when directly invoking agents for specific tasks.

## Development & Architecture Agents

### backend-architect
```
As a backend architect, design a RESTful API for [specific functionality]. Include:
- Endpoint definitions with HTTP methods
- Request/response schemas
- Error handling patterns
- Database schema design
- Security considerations
```

### frontend-developer
```
As a frontend developer, create a React component for [specific UI element]. Include:
- Component structure with props
- State management approach
- Styling solution
- Accessibility features
- Responsive design considerations
```

### ui-ux-designer
```
As a UI/UX designer, create a design for [specific interface]. Include:
- Layout structure and information hierarchy
- Color scheme and typography
- Interaction patterns
- User flow diagrams
- Design system components
```

### mobile-developer
```
As a mobile developer, build a feature for [platform] app. Include:
- Screen layouts and navigation
- State management
- Native API integrations
- Performance optimization
- Platform-specific considerations
```

### graphql-architect
```
As a GraphQL architect, design a schema for [specific domain]. Include:
- Type definitions with relationships
- Query and mutation operations
- Resolver patterns
- Error handling
- Performance optimization
```

## Language Specialist Agents

### python-pro
```
As a Python expert, implement [specific functionality]. Follow:
- PEP 8 style guide
- Type hints for all functions
- Error handling with custom exceptions
- Memory-efficient patterns
- Performance optimization
```

### golang-pro
```
As a Go expert, write [specific functionality]. Include:
- Goroutines for concurrency
- Channels for communication
- Error handling with multiple returns
- Interface-based design
- Efficient memory usage
```

### rust-pro
```
As a Rust expert, implement [specific functionality]. Use:
- Ownership and borrowing patterns
- Lifetimes where needed
- Trait implementations
- Error handling with Result/Option
- Zero-cost abstractions
```

### javascript-pro
```
As a JavaScript expert, create [specific functionality]. Include:
- ES6+ features
- Asynchronous patterns
- Module organization
- Error handling
- Performance considerations
```

### typescript-pro
```
As a TypeScript expert, design [specific functionality]. Include:
- Advanced type definitions
- Generic constraints
- Interface and type composition
- Strict type checking
- Type inference optimization
```

### java-pro
```
As a Java expert, implement [specific functionality]. Use:
- Modern Java features (streams, lambda)
- Concurrency patterns
- Memory management
- JVM optimization
- Design patterns
```

### sql-pro
```
As an SQL expert, write queries for [specific data operations]. Include:
- Index optimization
- JOIN strategies
- Subquery alternatives
- Aggregation patterns
- Performance tuning
```

## Infrastructure & Operations Agents

### devops-troubleshooter
```
As a DevOps troubleshooter, diagnose [specific issue]. Analyze:
- Log patterns
- System metrics
- Network connectivity
- Configuration issues
- Deployment failures
```

### deployment-engineer
```
As a deployment engineer, set up [specific deployment]. Include:
- CI/CD pipeline configuration
- Containerization strategy
- Environment variables
- Rollback mechanisms
- Health checks
```

### cloud-architect
```
As a cloud architect, design [specific infrastructure]. Include:
- Service selection
- Cost optimization
- Security configuration
- Scalability patterns
- Disaster recovery
```

### database-optimizer
```
As a database optimizer, improve [specific queries/system]. Analyze:
- Execution plans
- Index effectiveness
- Query patterns
- Resource usage
- Performance bottlenecks
```

### terraform-specialist
```
As a Terraform specialist, create [specific infrastructure]. Include:
- Module structure
- State management
- Variable definitions
- Security best practices
- Resource dependencies
```

## Quality & Security Agents

### code-reviewer
```
As a code reviewer, analyze [specific code changes]. Check:
- Code quality and readability
- Security vulnerabilities
- Performance issues
- Best practices
- Test coverage
```

### security-auditor
```
As a security auditor, assess [specific system/code]. Review:
- OWASP compliance
- Input validation
- Authentication/authorization
- Data protection
- Configuration security
```

### test-automator
```
As a test automator, create tests for [specific functionality]. Include:
- Unit test structure
- Integration test scenarios
- Mock implementations
- Test data management
- Coverage requirements
```

### performance-engineer
```
As a performance engineer, optimize [specific system]. Analyze:
- Bottleneck identification
- Resource utilization
- Caching strategies
- Load testing results
- Optimization recommendations
```

## Data & AI Agents

### data-scientist
```
As a data scientist, analyze [specific dataset]. Include:
- Statistical analysis
- Visualization recommendations
- Pattern identification
- Insights summary
- Methodology explanation
```

### ai-engineer
```
As an AI engineer, build [specific AI feature]. Include:
- Model selection rationale
- Data preprocessing
- Prompt engineering
- Evaluation metrics
- Deployment considerations
```

### ml-engineer
```
As an ML engineer, implement [specific ML pipeline]. Include:
- Feature engineering
- Model training process
- Validation strategy
- Deployment approach
- Monitoring requirements
```

## Specialized Domain Agents

### api-documenter
```
As an API documenter, create documentation for [specific API]. Include:
- Endpoint specifications
- Request/response examples
- Error code definitions
- Authentication details
- Rate limiting information
```

### payment-integration
```
As a payment integration specialist, implement [specific payment provider]. Include:
- API integration patterns
- Security compliance
- Error handling
- Webhook processing
- Testing strategies
```

## Business & Marketing Agents

### business-analyst
```
As a business analyst, evaluate [specific business question]. Include:
- Data analysis approach
- Key metrics identification
- Trend analysis
- Recommendation framework
- Implementation suggestions
```

### content-marketer
```
As a content marketer, create [specific content piece]. Include:
- Target audience analysis
- SEO optimization
- Engagement strategies
- Distribution channels
- Success metrics
```

Usage Examples:
1. For code review: "Use code-reviewer to analyze these recent changes for security issues"
2. For API design: "Have backend-architect design a user authentication API"
3. For performance optimization: "Get performance-engineer to identify bottlenecks in this code"
4. For test creation: "Use test-automator to create a comprehensive test suite for this feature"
5. For documentation: "Have api-documenter create OpenAPI specs for these endpoints"