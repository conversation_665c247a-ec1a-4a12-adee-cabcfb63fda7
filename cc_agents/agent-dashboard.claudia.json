{"name": "Agent <PERSON>", "description": "Comprehensive agent management dashboard for creating, managing, and orchestrating AI agents", "version": "1.0.0", "author": "Claudia System", "category": "System", "status": "Active", "capabilities": [{"type": "agent_management", "description": "Create, edit, delete, and manage AI agents", "config": {"max_agents": 100, "supported_models": ["claude-sonnet-4-20250514", "claude-opus-4-1-20250805"], "auto_save": true}}, {"type": "agent_orchestration", "description": "Orchestrate multiple agents for complex workflows", "config": {"max_concurrent_agents": 10, "workflow_templates": ["sequential", "parallel", "conditional"], "monitoring_enabled": true}}, {"type": "marketplace_integration", "description": "Browse and install agents from the marketplace", "config": {"marketplace_url": "https://api.claudia.ai/marketplace", "auto_update": false, "verify_signatures": true}}, {"type": "mcp_server_management", "description": "Configure and manage Model Context Protocol servers", "config": {"supported_protocols": ["mcp-v1", "mcp-v2"], "max_servers": 20, "health_check_interval": 30000}}], "configuration": {"max_execution_time_ms": 0, "max_memory_mb": 2048, "max_concurrent_tasks": 50, "temperature": 0.7, "model_preferences": ["claude-sonnet-4-20250514", "claude-opus-4-1-20250805"], "environment_variables": {"AGENT_DASHBOARD_ENV": "production", "ENABLE_ANALYTICS": "true"}}, "ui_config": {"tabs": [{"id": "overview", "label": "Overview", "icon": "dashboard", "default": true}, {"id": "manager", "label": "Manager", "icon": "settings"}, {"id": "marketplace", "label": "Marketplace", "icon": "store"}, {"id": "orchestration", "label": "Orchestration", "icon": "workflow"}, {"id": "mcp-servers", "label": "MCP Servers", "icon": "server"}], "theme": {"primary_color": "#3B82F6", "secondary_color": "#10B981", "dark_mode_support": true}}, "permissions": {"required": ["agent.create", "agent.read", "agent.update", "agent.delete", "agent.execute", "marketplace.browse", "marketplace.install", "mcp.configure", "orchestration.create", "orchestration.execute"], "optional": ["analytics.view", "metrics.export", "logs.access"]}, "dependencies": {"tauri": "^2.0.0", "react": "^18.0.0", "framer-motion": "^10.0.0"}, "metadata": {"created_at": "2024-01-15T00:00:00Z", "updated_at": "2024-01-15T00:00:00Z", "tags": ["system", "dashboard", "management", "orchestration"], "documentation_url": "https://docs.claudia.ai/agent-dashboard", "support_email": "<EMAIL>"}}